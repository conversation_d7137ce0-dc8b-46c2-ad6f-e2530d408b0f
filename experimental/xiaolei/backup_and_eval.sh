#!/bin/bash

# Directory to search in
search_dir=`pwd`


# File containing processed folder names
processed_file="processed.txt"
touch $processed_file


temp_dir=$(mktemp -d)
echo Using temporary directory $temp_dir
# Infinite loop to keep the script running
while true; do
    # Find directories starting with 'checkpoint_' and loop through them
    find "$search_dir" -type d -name 'checkpoint_*' -print0 | while IFS= read -r -d '' dir; do
        # Extract the original folder name without path
        folder_name=$(basename "$dir")

        # Check if the filename is not in processed_file
        if ! grep -Fxq "$folder_name" "$processed_file"; then
        # Wait 30 second to allow checkpoints to be written out to disk
        sleep 30
            # Replace 'checkpoint_' with 'backup_'
            new_name="${folder_name/checkpoint_/backup_}"
            # Compute new directory path
            new_dir=$(dirname "$dir")/"$new_name"

        echo Creating backup ${new_dir}
        mkdir -p "${new_dir}"
        cp $search_dir/$folder_name/*.json "${new_dir}/"
        cp $search_dir/$folder_name/consolidated*.pth "${new_dir}/"
        echo Doing evaluation for directory ${new_dir}

        cd /home/<USER>/augment/research/eval
        for style in 0-shot 5-shot
        do
            eval_name="${search_dir}/${folder_name/checkpoint_/eval_}_${style}"
            input_file="${temp_dir}/${folder_name}_rendered.yml"
            cp "${search_dir}/eval_template.yml" "${input_file}"
            sed -i "s|<<PATH>>|${new_dir}|g; s|<<STYLE>>|${style}|g" ${input_file}
            output=$(python eval.py --local --v2 "${input_file}")
            result_path=$(echo "$output" | grep 'Evaluating system basic with the output folder of' | sed -e 's/.*output folder of \(.*\)\.\.\./\1/')
            # Check if the path is in output
            if [ -n "${result_path}" ]; then
                echo "Human eval output for ${new_name} ${style}: " `cat ${result_path}/*.jsonl | jq '.metrics.pass_at_k."pass@1"'`
                # Use the captured path here, to copy it to a new location
                echo "Copying result from ${result_path} to ${eval_name}"
                cp -r "${result_path}" "${eval_name}"
            else
                echo "Path not found in the command output."
            fi
        done
        cd -
        echo "$folder_name" >> "$processed_file"
    else
        # Wait for 2 minutes (120 seconds)
        echo Waiting 60 seconds...
        sleep 60
    fi
    done

done

rm -rf "$temp_dir"

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-02-15 18:51:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAnthropic Direct Client initialized\u001b[0m \u001b[36mmodel_name\u001b[0m=\u001b[35mclaude-3-5-sonnet-20241022\u001b[0m\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicDirectClient\n", "from services.integrations.github.agent_tools.config import GitHubConfig\n", "from services.integrations.github.agent_tools.github_api_tool import GitHubAPITool\n", "\n", "\n", "tool = GitHubAPITool(GitHubConfig())\n", "\n", "# anthropic api key is in the k8s secret `anthropic-api-key` in your dev deploy\n", "# kubectl get secret anthropic-api-key -n dev-xiaolei -o jsonpath='{.data.api-key\\.txt}' | base64 -d\n", "client = AnthropicDirectClient(\n", "    api_key=open(\"/home/<USER>/api-key.txt\").read().strip(),\n", "    model_name=\"claude-3-5-sonnet-20241022\",\n", "    max_output_tokens=1024,\n", "    temperature=0.0,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["response = client.generate_response_stream(\n", "    cur_message=\"what are all the repos in augment-test-org?\",\n", "    system_prompt=\"You are a helpful assistant.\",\n", "    tool_definitions=[tool.get_tool_param()],\n", "    max_output_tokens=1024,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-02-15 18:54:54\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mgenerating response for 0 messages\u001b[0m\n", "\u001b[2m2025-02-15 18:54:54\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mtool_choice_param: NOT_GIVEN  \u001b[0m\n", "\u001b[2m2025-02-15 18:54:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minput_tokens: 751, cache_read_input_tokens: 0, cache_creation_input_tokens: 0\u001b[0m\n", "TEXT:\n", "I'll help you search for repositories in the augment-test-org organization using the GitHub API.\n", "TOOL CALLS:\n", "[ToolUseResponse(tool_name='github_api', input={'path': '/orgs/augment-test-org/repos'}, tool_use_id='toolu_019FCsfRXojoCKHi1XycjM3f')]\n"]}], "source": ["text = \"\"\n", "tool_calls = []\n", "for item in response:\n", "    if item.text:\n", "        text += item.text\n", "    elif item.tool_use:\n", "        tool_calls.append(item.tool_use)\n", "\n", "print(\"TEXT:\")\n", "print(text)\n", "print(\"TOOL CALLS:\")\n", "print(tool_calls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TOOL CALL: ToolUseResponse(tool_name='github_api', input={'path': '/orgs/augment-test-org/repos'}, tool_use_id='toolu_019FCsfRXojoCKHi1XycjM3f')\n", "Found 3 items:\n", "\n", "- {'id': 873770167, 'node_id': 'R_kgDONBSstw', 'name': 'chromium', 'full_name': 'augment-test-org/chromium', 'private': False, 'owner': {'login': 'augment-test-org', 'id': 184423971, 'node_id': 'O_kgDOCv4WIw', 'avatar_url': 'https://avatars.githubusercontent.com/u/184423971?v=4', 'gravatar_id': '', 'url': 'https://api.github.com/users/augment-test-org', 'html_url': 'https://github.com/augment-test-org', 'followers_url': 'https://api.github.com/users/augment-test-org/followers', 'following_url': 'https://api.github.com/users/augment-test-org/following{/other_user}', 'gists_url': 'https://api.github.com/users/augment-test-org/gists{/gist_id}', 'starred_url': 'https://api.github.com/users/augment-test-org/starred{/owner}{/repo}', 'subscriptions_url': 'https://api.github.com/users/augment-test-org/subscriptions', 'organizations_url': 'https://api.github.com/users/augment-test-org/orgs', 'repos_url': 'https://api.github.com/users/augment-test-org/repos', 'events_url': 'https://api.github.com/users/augment-test-org/events{/privacy}', 'received_events_url': 'https://api.github.com/users/augment-test-org/received_events', 'type': 'Organization', 'user_view_type': 'public', 'site_admin': False}, 'html_url': 'https://github.com/augment-test-org/chromium', 'description': 'The official GitHub mirror of the Chromium source. Used to test handling large repos', 'fork': True, 'url': 'https://api.github.com/repos/augment-test-org/chromium', 'forks_url': 'https://api.github.com/repos/augment-test-org/chromium/forks', 'keys_url': 'https://api.github.com/repos/augment-test-org/chromium/keys{/key_id}', 'collaborators_url': 'https://api.github.com/repos/augment-test-org/chromium/collaborators{/collaborator}', 'teams_url': 'https://api.github.com/repos/augment-test-org/chromium/teams', 'hooks_url': 'https://api.github.com/repos/augment-test-org/chromium/hooks', 'issue_events_url': 'https://api.github.com/repos/augment-test-org/chromium/issues/events{/number}', 'events_url': 'https://api.github.com/repos/augment-test-org/chromium/events', 'assignees_url': 'https://api.github.com/repos/augment-test-org/chromium/assignees{/user}', 'branches_url': 'https://api.github.com/repos/augment-test-org/chromium/branches{/branch}', 'tags_url': 'https://api.github.com/repos/augment-test-org/chromium/tags', 'blobs_url': 'https://api.github.com/repos/augment-test-org/chromium/git/blobs{/sha}', 'git_tags_url': 'https://api.github.com/repos/augment-test-org/chromium/git/tags{/sha}', 'git_refs_url': 'https://api.github.com/repos/augment-test-org/chromium/git/refs{/sha}', 'trees_url': 'https://api.github.com/repos/augment-test-org/chromium/git/trees{/sha}', 'statuses_url': 'https://api.github.com/repos/augment-test-org/chromium/statuses/{sha}', 'languages_url': 'https://api.github.com/repos/augment-test-org/chromium/languages', 'stargazers_url': 'https://api.github.com/repos/augment-test-org/chromium/stargazers', 'contributors_url': 'https://api.github.com/repos/augment-test-org/chromium/contributors', 'subscribers_url': 'https://api.github.com/repos/augment-test-org/chromium/subscribers', 'subscription_url': 'https://api.github.com/repos/augment-test-org/chromium/subscription', 'commits_url': 'https://api.github.com/repos/augment-test-org/chromium/commits{/sha}', 'git_commits_url': 'https://api.github.com/repos/augment-test-org/chromium/git/commits{/sha}', 'comments_url': 'https://api.github.com/repos/augment-test-org/chromium/comments{/number}', 'issue_comment_url': 'https://api.github.com/repos/augment-test-org/chromium/issues/comments{/number}', 'contents_url': 'https://api.github.com/repos/augment-test-org/chromium/contents/{+path}', 'compare_url': 'https://api.github.com/repos/augment-test-org/chromium/compare/{base}...{head}', 'merges_url': 'https://api.github.com/repos/augment-test-org/chromium/merges', 'archive_url': 'https://api.github.com/repos/augment-test-org/chromium/{archive_format}{/ref}', 'downloads_url': 'https://api.github.com/repos/augment-test-org/chromium/downloads', 'issues_url': 'https://api.github.com/repos/augment-test-org/chromium/issues{/number}', 'pulls_url': 'https://api.github.com/repos/augment-test-org/chromium/pulls{/number}', 'milestones_url': 'https://api.github.com/repos/augment-test-org/chromium/milestones{/number}', 'notifications_url': 'https://api.github.com/repos/augment-test-org/chromium/notifications{?since,all,participating}', 'labels_url': 'https://api.github.com/repos/augment-test-org/chromium/labels{/name}', 'releases_url': 'https://api.github.com/repos/augment-test-org/chromium/releases{/id}', 'deployments_url': 'https://api.github.com/repos/augment-test-org/chromium/deployments', 'created_at': '2024-10-16T17:29:31Z', 'updated_at': '2024-10-16T17:29:31Z', 'pushed_at': '2024-10-16T17:28:21Z', 'git_url': 'git://github.com/augment-test-org/chromium.git', 'ssh_url': '**************:augment-test-org/chromium.git', 'clone_url': 'https://github.com/augment-test-org/chromium.git', 'svn_url': 'https://github.com/augment-test-org/chromium', 'homepage': 'https://chromium.googlesource.com/chromium/src/', 'size': 49342136, 'stargazers_count': 0, 'watchers_count': 0, 'language': None, 'has_issues': False, 'has_projects': True, 'has_downloads': True, 'has_wiki': False, 'has_pages': False, 'has_discussions': False, 'forks_count': 0, 'mirror_url': None, 'archived': False, 'disabled': False, 'open_issues_count': 0, 'license': {'key': 'bsd-3-clause', 'name': 'BSD 3-Clause \"New\" or \"Revised\" License', 'spdx_id': 'BSD-3-Clause', 'url': 'https://api.github.com/licenses/bsd-3-clause', 'node_id': 'MDc6TGljZW5zZTU='}, 'allow_forking': True, 'is_template': False, 'web_commit_signoff_required': False, 'topics': [], 'visibility': 'public', 'forks': 0, 'open_issues': 0, 'watchers': 0, 'default_branch': 'main', 'permissions': {'admin': True, 'maintain': True, 'push': True, 'triage': True, 'pull': True}, 'security_and_analysis': {'secret_scanning': {'status': 'disabled'}, 'secret_scanning_push_protection': {'status': 'disabled'}, 'dependabot_security_updates': {'status': 'disabled'}, 'secret_scanning_non_provider_patterns': {'status': 'disabled'}, 'secret_scanning_validity_checks': {'status': 'disabled'}}}\n", "- {'id': 875008975, 'node_id': 'R_kgDONCeTzw', 'name': 'architecture-decision-record', 'full_name': 'augment-test-org/architecture-decision-record', 'private': False, 'owner': {'login': 'augment-test-org', 'id': 184423971, 'node_id': 'O_kgDOCv4WIw', 'avatar_url': 'https://avatars.githubusercontent.com/u/184423971?v=4', 'gravatar_id': '', 'url': 'https://api.github.com/users/augment-test-org', 'html_url': 'https://github.com/augment-test-org', 'followers_url': 'https://api.github.com/users/augment-test-org/followers', 'following_url': 'https://api.github.com/users/augment-test-org/following{/other_user}', 'gists_url': 'https://api.github.com/users/augment-test-org/gists{/gist_id}', 'starred_url': 'https://api.github.com/users/augment-test-org/starred{/owner}{/repo}', 'subscriptions_url': 'https://api.github.com/users/augment-test-org/subscriptions', 'organizations_url': 'https://api.github.com/users/augment-test-org/orgs', 'repos_url': 'https://api.github.com/users/augment-test-org/repos', 'events_url': 'https://api.github.com/users/augment-test-org/events{/privacy}', 'received_events_url': 'https://api.github.com/users/augment-test-org/received_events', 'type': 'Organization', 'user_view_type': 'public', 'site_admin': False}, 'html_url': 'https://github.com/augment-test-org/architecture-decision-record', 'description': 'Architecture decision record (ADR) examples for software planning, IT leadership, and template documentation', 'fork': True, 'url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record', 'forks_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/forks', 'keys_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/keys{/key_id}', 'collaborators_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/collaborators{/collaborator}', 'teams_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/teams', 'hooks_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/hooks', 'issue_events_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/issues/events{/number}', 'events_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/events', 'assignees_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/assignees{/user}', 'branches_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/branches{/branch}', 'tags_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/tags', 'blobs_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/git/blobs{/sha}', 'git_tags_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/git/tags{/sha}', 'git_refs_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/git/refs{/sha}', 'trees_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/git/trees{/sha}', 'statuses_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/statuses/{sha}', 'languages_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/languages', 'stargazers_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/stargazers', 'contributors_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/contributors', 'subscribers_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/subscribers', 'subscription_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/subscription', 'commits_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/commits{/sha}', 'git_commits_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/git/commits{/sha}', 'comments_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/comments{/number}', 'issue_comment_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/issues/comments{/number}', 'contents_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/contents/{+path}', 'compare_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/compare/{base}...{head}', 'merges_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/merges', 'archive_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/{archive_format}{/ref}', 'downloads_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/downloads', 'issues_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/issues{/number}', 'pulls_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/pulls{/number}', 'milestones_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/milestones{/number}', 'notifications_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/notifications{?since,all,participating}', 'labels_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/labels{/name}', 'releases_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/releases{/id}', 'deployments_url': 'https://api.github.com/repos/augment-test-org/architecture-decision-record/deployments', 'created_at': '2024-10-18T22:19:08Z', 'updated_at': '2024-10-18T22:19:08Z', 'pushed_at': '2024-08-13T16:11:00Z', 'git_url': 'git://github.com/augment-test-org/architecture-decision-record.git', 'ssh_url': '**************:augment-test-org/architecture-decision-record.git', 'clone_url': 'https://github.com/augment-test-org/architecture-decision-record.git', 'svn_url': 'https://github.com/augment-test-org/architecture-decision-record', 'homepage': '', 'size': 312, 'stargazers_count': 0, 'watchers_count': 0, 'language': None, 'has_issues': False, 'has_projects': True, 'has_downloads': True, 'has_wiki': True, 'has_pages': False, 'has_discussions': False, 'forks_count': 0, 'mirror_url': None, 'archived': False, 'disabled': False, 'open_issues_count': 0, 'license': {'key': 'other', 'name': 'Other', 'spdx_id': 'NOASSERTION', 'url': None, 'node_id': 'MDc6TGljZW5zZTA='}, 'allow_forking': True, 'is_template': False, 'web_commit_signoff_required': False, 'topics': [], 'visibility': 'public', 'forks': 0, 'open_issues': 0, 'watchers': 0, 'default_branch': 'main', 'permissions': {'admin': True, 'maintain': True, 'push': True, 'triage': True, 'pull': True}, 'security_and_analysis': {'secret_scanning': {'status': 'disabled'}, 'secret_scanning_push_protection': {'status': 'disabled'}, 'dependabot_security_updates': {'status': 'disabled'}, 'secret_scanning_non_provider_patterns': {'status': 'disabled'}, 'secret_scanning_validity_checks': {'status': 'disabled'}}}\n", "- {'id': 875009100, 'node_id': 'R_kgDONCeUTA', 'name': 'Augment-FullStack-App', 'full_name': 'augment-test-org/Augment-FullStack-App', 'private': False, 'owner': {'login': 'augment-test-org', 'id': 184423971, 'node_id': 'O_kgDOCv4WIw', 'avatar_url': 'https://avatars.githubusercontent.com/u/184423971?v=4', 'gravatar_id': '', 'url': 'https://api.github.com/users/augment-test-org', 'html_url': 'https://github.com/augment-test-org', 'followers_url': 'https://api.github.com/users/augment-test-org/followers', 'following_url': 'https://api.github.com/users/augment-test-org/following{/other_user}', 'gists_url': 'https://api.github.com/users/augment-test-org/gists{/gist_id}', 'starred_url': 'https://api.github.com/users/augment-test-org/starred{/owner}{/repo}', 'subscriptions_url': 'https://api.github.com/users/augment-test-org/subscriptions', 'organizations_url': 'https://api.github.com/users/augment-test-org/orgs', 'repos_url': 'https://api.github.com/users/augment-test-org/repos', 'events_url': 'https://api.github.com/users/augment-test-org/events{/privacy}', 'received_events_url': 'https://api.github.com/users/augment-test-org/received_events', 'type': 'Organization', 'user_view_type': 'public', 'site_admin': False}, 'html_url': 'https://github.com/augment-test-org/Augment-FullStack-App', 'description': None, 'fork': True, 'url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App', 'forks_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/forks', 'keys_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/keys{/key_id}', 'collaborators_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/collaborators{/collaborator}', 'teams_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/teams', 'hooks_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/hooks', 'issue_events_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/issues/events{/number}', 'events_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/events', 'assignees_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/assignees{/user}', 'branches_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/branches{/branch}', 'tags_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/tags', 'blobs_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/git/blobs{/sha}', 'git_tags_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/git/tags{/sha}', 'git_refs_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/git/refs{/sha}', 'trees_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/git/trees{/sha}', 'statuses_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/statuses/{sha}', 'languages_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/languages', 'stargazers_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/stargazers', 'contributors_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/contributors', 'subscribers_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/subscribers', 'subscription_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/subscription', 'commits_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/commits{/sha}', 'git_commits_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/git/commits{/sha}', 'comments_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/comments{/number}', 'issue_comment_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/issues/comments{/number}', 'contents_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/contents/{+path}', 'compare_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/compare/{base}...{head}', 'merges_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/merges', 'archive_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/{archive_format}{/ref}', 'downloads_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/downloads', 'issues_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/issues{/number}', 'pulls_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/pulls{/number}', 'milestones_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/milestones{/number}', 'notifications_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/notifications{?since,all,participating}', 'labels_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/labels{/name}', 'releases_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/releases{/id}', 'deployments_url': 'https://api.github.com/repos/augment-test-org/Augment-FullStack-App/deployments', 'created_at': '2024-10-18T22:19:46Z', 'updated_at': '2024-10-18T22:19:46Z', 'pushed_at': '2024-10-08T21:08:36Z', 'git_url': 'git://github.com/augment-test-org/Augment-FullStack-App.git', 'ssh_url': '**************:augment-test-org/Augment-FullStack-App.git', 'clone_url': 'https://github.com/augment-test-org/Augment-FullStack-App.git', 'svn_url': 'https://github.com/augment-test-org/Augment-FullStack-App', 'homepage': None, 'size': 4379, 'stargazers_count': 0, 'watchers_count': 0, 'language': None, 'has_issues': False, 'has_projects': True, 'has_downloads': True, 'has_wiki': False, 'has_pages': False, 'has_discussions': False, 'forks_count': 0, 'mirror_url': None, 'archived': False, 'disabled': False, 'open_issues_count': 0, 'license': None, 'allow_forking': True, 'is_template': False, 'web_commit_signoff_required': False, 'topics': [], 'visibility': 'public', 'forks': 0, 'open_issues': 0, 'watchers': 0, 'default_branch': 'main', 'permissions': {'admin': True, 'maintain': True, 'push': True, 'triage': True, 'pull': True}, 'security_and_analysis': {'secret_scanning': {'status': 'disabled'}, 'secret_scanning_push_protection': {'status': 'disabled'}, 'dependabot_security_updates': {'status': 'disabled'}, 'secret_scanning_non_provider_patterns': {'status': 'disabled'}, 'secret_scanning_validity_checks': {'status': 'disabled'}}}\n", "\n"]}], "source": ["from services.integrations.github.agent_tools.github_api_tool import (\n", "    GitHubExtraToolInput,\n", ")\n", "from services.lib.request_context.request_context import RequestContext\n", "\n", "extra_input = GitHubExtraToolInput(\n", "    api_token=open(\"/home/<USER>/github-api-key.txt\").read().strip()\n", ")\n", "for tool_call in tool_calls:\n", "    print(\"TOOL CALL:\", tool_call)\n", "    print(tool.run(tool_call.input, extra_input, RequestContext.create()))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:research.data.spark.utils:Skipping bazel build.\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 7\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01m<PERSON>earch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m k8s_session\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# logging.basicConfig(level=logging.INFO)\u001b[39;00m\n\u001b[0;32m----> 7\u001b[0m spark \u001b[38;5;241m=\u001b[39m \u001b[43mk8s_session\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmax_workers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m50\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/augment/research/data/spark/utils.py:321\u001b[0m, in \u001b[0;36mk8s_session\u001b[0;34m(name, efs_path, image, region, gpu_type, gpu_count, s3_region, min_workers, max_workers, idle_timeout, conf, skip_bazel_build, copy_user_base)\u001b[0m\n\u001b[1;32m    319\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m efs_path:\n\u001b[1;32m    320\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m copy_user_base:\n\u001b[0;32m--> 321\u001b[0m         shared_folder \u001b[38;5;241m=\u001b[39m \u001b[43mcopy_deps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mefs_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    322\u001b[0m         k8s_config[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspark.executorEnv.PYTHONUSERBASE\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m LOCAL_DEP_BASE_PATH\n\u001b[1;32m    323\u001b[0m         k8s_config[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspark.driverEnv.PYTHONUSERBASE\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m LOCAL_DEP_BASE_PATH\n", "File \u001b[0;32m~/augment/research/data/spark/infra/copy_deps.py:87\u001b[0m, in \u001b[0;36mcopy_deps\u001b[0;34m(efs_root)\u001b[0m\n\u001b[1;32m     85\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m time_block(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCopy user_base\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger\u001b[38;5;241m.\u001b[39minfo):\n\u001b[1;32m     86\u001b[0m     user_base \u001b[38;5;241m=\u001b[39m site\u001b[38;5;241m.\u001b[39mgetuserbase()\n\u001b[0;32m---> 87\u001b[0m     \u001b[43marchive_copy\u001b[49m\u001b[43m(\u001b[49m\u001b[43muser_base\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mbase_path\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muser_base\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;66;03m# Copy packages in the augment repo\u001b[39;00m\n\u001b[1;32m     90\u001b[0m aug_path \u001b[38;5;241m=\u001b[39m base_path \u001b[38;5;241m/\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maugment\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/augment/research/data/spark/infra/copy_deps.py:44\u001b[0m, in \u001b[0;36marchive_copy\u001b[0;34m(source, destination)\u001b[0m\n\u001b[1;32m     42\u001b[0m tar_file \u001b[38;5;241m=\u001b[39m Path(tmpdir) \u001b[38;5;241m/\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marchived.tar\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     43\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m tarfile\u001b[38;5;241m.\u001b[39mopen(tar_file, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m tar:\n\u001b[0;32m---> 44\u001b[0m     \u001b[43mtar\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     45\u001b[0m \u001b[43m        \u001b[49m\u001b[43msource\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[43m        \u001b[49m\u001b[43marcname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     47\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[1;32m     49\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43many\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m                \u001b[49m\u001b[43mfnmatch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfnmatch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mPath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpattern\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mpattern\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mIGNORED_PATTERNS\u001b[49m\n\u001b[1;32m     52\u001b[0m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     53\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\n\u001b[1;32m     54\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     55\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     56\u001b[0m shutil\u001b[38;5;241m.\u001b[39mcopy(tar_file, destination)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:2186\u001b[0m, in \u001b[0;36mTarFile.add\u001b[0;34m(self, name, arcname, recursive, filter)\u001b[0m\n\u001b[1;32m   2184\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m recursive:\n\u001b[1;32m   2185\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28msorted\u001b[39m(os\u001b[38;5;241m.\u001b[39mlistdir(name)):\n\u001b[0;32m-> 2186\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43marcname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2187\u001b[0m \u001b[43m                    \u001b[49m\u001b[43mrecursive\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2189\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   2190\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maddfile(tarinfo)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:2186\u001b[0m, in \u001b[0;36mTarFile.add\u001b[0;34m(self, name, arcname, recursive, filter)\u001b[0m\n\u001b[1;32m   2184\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m recursive:\n\u001b[1;32m   2185\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28msorted\u001b[39m(os\u001b[38;5;241m.\u001b[39mlistdir(name)):\n\u001b[0;32m-> 2186\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43marcname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2187\u001b[0m \u001b[43m                    \u001b[49m\u001b[43mrecursive\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2189\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   2190\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maddfile(tarinfo)\n", "    \u001b[0;31m[... skipping similar frames: TarFile.add at line 2186 (1 times)]\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:2186\u001b[0m, in \u001b[0;36mTarFile.add\u001b[0;34m(self, name, arcname, recursive, filter)\u001b[0m\n\u001b[1;32m   2184\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m recursive:\n\u001b[1;32m   2185\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28msorted\u001b[39m(os\u001b[38;5;241m.\u001b[39mlistdir(name)):\n\u001b[0;32m-> 2186\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43marcname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2187\u001b[0m \u001b[43m                    \u001b[49m\u001b[43mrecursive\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2189\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   2190\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maddfile(tarinfo)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:2180\u001b[0m, in \u001b[0;36mTarFile.add\u001b[0;34m(self, name, arcname, recursive, filter)\u001b[0m\n\u001b[1;32m   2178\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m tarinfo\u001b[38;5;241m.\u001b[39misreg():\n\u001b[1;32m   2179\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m bltn_open(name, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrb\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m-> 2180\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43maddfile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtarinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2182\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m tarinfo\u001b[38;5;241m.\u001b[39misdir():\n\u001b[1;32m   2183\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maddfile(tarinfo)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:2208\u001b[0m, in \u001b[0;36mTarFile.addfile\u001b[0;34m(self, tarinfo, fileobj)\u001b[0m\n\u001b[1;32m   2206\u001b[0m \u001b[38;5;66;03m# If there's data to follow, append it.\u001b[39;00m\n\u001b[1;32m   2207\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m fileobj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 2208\u001b[0m     \u001b[43mcopyfileobj\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfileobj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfileobj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtarinfo\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msize\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbufsize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbufsize\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2209\u001b[0m     blocks, remainder \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdivmod\u001b[39m(tarinfo\u001b[38;5;241m.\u001b[39msize, BLOCKSIZE)\n\u001b[1;32m   2210\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m remainder \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/tarfile.py:252\u001b[0m, in \u001b[0;36mcopyfileobj\u001b[0;34m(src, dst, length, exception, bufsize)\u001b[0m\n\u001b[1;32m    250\u001b[0m blocks, remainder \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdivmod\u001b[39m(length, bufsize)\n\u001b[1;32m    251\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m b \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(blocks):\n\u001b[0;32m--> 252\u001b[0m     buf \u001b[38;5;241m=\u001b[39m src\u001b[38;5;241m.\u001b[39mread(bufsize)\n\u001b[1;32m    253\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(buf) \u001b[38;5;241m<\u001b[39m bufsize:\n\u001b[1;32m    254\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m exception(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124munexpected end of data\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import logging\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session\n", "\n", "# logging.basicConfig(level=logging.INFO)\n", "spark = k8s_session(\n", "    max_workers=50,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 14:>                                                         (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- content: string (nullable = true)\n", " |-- doc_format: string (nullable = true)\n", " |-- doc_type: string (nullable = true)\n", " |-- page_name: string (nullable = true)\n", " |-- path: string (nullable = true)\n", " |-- project: string (nullable = true)\n", " |-- version: string (nullable = true)\n", " |-- source: string (nullable = true)\n", " |-- head: string (nullable = true)\n", " |-- chunks: string (nullable = true)\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>content</th><th>doc_format</th><th>doc_type</th><th>page_name</th><th>path</th><th>project</th><th>version</th><th>source</th><th>head</th><th>chunks</th></tr>\n", "<tr><td>&lt;html class=&#x27;no-js&#x27;&gt;&lt;!-- Mirrored from docs.unity3d.com/6000.0/Documentation/ScriptReference/Huma...</td><td></td><td>Property</td><td>HumanPartDof.RightLittle</td><td>docs.unity3d.com/6000.0/Documentation/ScriptReference/HumanPartDof.RightLittle.md</td><td>Unity_3D</td><td>6000.0</td><td>zeal</td><td>&lt;html class=&#x27;no-js&#x27;&gt;&lt;!-- mirrored from docs.unity3d.com/6000.0/documentation/scriptreference/huma...</td><td>Version: **Unity 6 Preview** (6000.0)\\n\\nLanguageEnglish\\n\\n  * C#\\n\\n#  HumanPartDof.RightLittle...</td></tr>\n", "<tr><td>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.01 Transitional//EN&quot; &quot;http://www.w3.org/TR/html4/loose....</td><td>html</td><td>Interface</td><td>ReturnInst</td><td>soot/baf/ReturnInst.md</td><td>Soot</td><td>2-5-0</td><td>dash-user-upload</td><td>&lt;!doctype html public &quot;-//w3c//dtd html 4.01 transitional//en&quot; &quot;http://www.w3.org/tr/html4/loose....</td><td>JavaScript is disabled on your browser.\\n\\nSkip navigation links\\n\\n  * Summary: \\n  * Nested | \\...</td></tr>\n", "<tr><td> &lt;h1 id=&quot;tensorflow::ops::mean&quot; data-text=&quot;tensorflow::ops::Mean&quot; tabindex=&quot;0&quot;&gt;tensorflow::ops::M...</td><td></td><td></td><td>mean</td><td>class/tensorflow/ops/mean.md</td><td>tensorflow_cpp</td><td>1.15</td><td>devdocs.io</td><td>&lt;h1 id=&quot;tensorflow::ops::mean&quot; data-text=&quot;tensorflow::ops::mean&quot; tabindex=&quot;0&quot;&gt;tensorflow::ops::me...</td><td># tensorflow::ops::Mean\\n\\n`#include &lt;math_ops.h&gt;`\\n\\nComputes the mean of elements across dimens...</td></tr>\n", "<tr><td>&lt;!doctype html&gt;&lt;html xmlns=http://www.w3.org/1999/xhtml dir=ltr lang=en-us&gt;&lt;meta charset=utf-8&gt;&lt;t...</td><td>html</td><td>Method</td><td>SLevelViewport::RegisterGameViewportIfPIE</td><td>en-US/API/Editor/LevelEditor/SLevelViewport/RegisterGameViewportIfPIE/index.md</td><td>UnrealEngine4</td><td></td><td>dash-user-upload</td><td>&lt;!doctype html&gt;&lt;html xmlns=http://www.w3.org/1999/xhtml dir=ltr lang=en-us&gt;&lt;meta charset=utf-8&gt;&lt;t...</td><td># SLevelViewport::RegisterGameViewportIfPIE\\n\\n## Registers a game viewport with the Slate applic...</td></tr>\n", "<tr><td>&lt;!DOCTYPE HTML&gt;\\n&lt;html&gt;\\n&lt;head&gt;\\n\\n    &lt;title&gt;Inferno as a server.&lt;/title&gt;\\n\\n    &lt;link rel=&quot;styl...</td><td>html</td><td></td><td>msg00520</td><td>inferno/historical_documents/mailing_lists/interstice/msg00520.md</td><td>sys-doc-archive</td><td></td><td>posix-docs</td><td>&lt;!doctype html&gt;\\n&lt;html&gt;\\n&lt;head&gt;\\n\\n    &lt;title&gt;inferno as a server.&lt;/title&gt;\\n\\n    &lt;link rel=&quot;styl...</td><td># Inferno as a server.\\n\\n[Date Prev][Date Next][Thread Prev][Thread Next][Date Index][Thread Ind...</td></tr>\n", "<tr><td>&lt;h1&gt; Validator &lt;small&gt;&lt;sup&gt;&lt;span class=&quot;label label-danger&quot;&gt;deprecated&lt;/span&gt;&lt;/sup&gt;&lt;/small&gt; &lt;/h1&gt;...</td><td></td><td></td><td>validator</td><td>symfony/component/validator/validator.md</td><td>symfony</td><td>3.1</td><td>devdocs.io</td><td>&lt;h1&gt; validator &lt;small&gt;&lt;sup&gt;&lt;span class=&quot;label label-danger&quot;&gt;deprecated&lt;/span&gt;&lt;/sup&gt;&lt;/small&gt; &lt;/h1&gt;...</td><td>#  Validator\\n\\nclass **Validator** implements ValidatorInterface, MetadataFactoryInterface\\n\\nsi...</td></tr>\n", "<tr><td>    &lt;h1 class=&quot;section&quot;&gt;36.1 Timing Utilities&lt;/h1&gt; &lt;p&gt;Octave’s core set of functions for manipula...</td><td></td><td></td><td>timing-utilities</td><td>timing-utilities.md</td><td>octave</td><td>5</td><td>devdocs.io</td><td>&lt;h1 class=&quot;section&quot;&gt;36.1 timing utilities&lt;/h1&gt; &lt;p&gt;octave’s core set of functions for manipulating...</td><td># 36.1 Timing Utilities\\n\\nOctave’s core set of functions for manipulating time values are patter...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html&gt;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n...</td><td>html</td><td></td><td>how-to-create-a-simple-binding0</td><td>dotnet/desktop/wpf/data/how-to-create-a-simple-binding0.md</td><td>microsoft-learn</td><td>en</td><td>microsoft-learn</td><td>&lt;!doctype html&gt;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n...</td><td>article-header\\n\\n#### Share via\\n\\nend article-header end mobile-contents button\\n\\n\u0000# How to: C...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html&gt;&lt;html\\n\\tclass=&quot;hasSidebar hasPageActions hasBreadcrumb conceptual has-default-foc...</td><td>html</td><td></td><td>addnewimcontacttogroup0</td><td>exchange/client-developer/web-service-reference/addnewimcontacttogroup0.md</td><td>microsoft-learn</td><td>en</td><td>microsoft-learn</td><td>&lt;!doctype html&gt;&lt;html\\n\\tclass=&quot;hassidebar haspageactions hasbreadcrumb conceptual has-default-foc...</td><td>#### Share via\\n\\n# AddNewImContactToGroup\\n\\n  * Article\\n  * 03/29/2023\\n\\nThe **AddNewImContac...</td></tr>\n", "<tr><td>&lt;h1 id=&quot;pandas-series-skew&quot;&gt;pandas.Series.skew&lt;/h1&gt; &lt;dl class=&quot;method&quot;&gt; &lt;dt id=&quot;pandas.Series.ske...</td><td></td><td></td><td>pandas.series.skew</td><td>reference/api/pandas.series.skew.md</td><td>pandas</td><td>0.25</td><td>devdocs.io</td><td>&lt;h1 id=&quot;pandas-series-skew&quot;&gt;pandas.series.skew&lt;/h1&gt; &lt;dl class=&quot;method&quot;&gt; &lt;dt id=&quot;pandas.series.ske...</td><td># pandas.Series.skew\\n\\n`Series.skew(self, axis=None, skipna=None, level=None, numeric_only=None,...</td></tr>\n", "<tr><td>&lt;html class=&#x27;writer-html5&#x27;&gt;&lt;!-- Mirrored from docs.ansible.com/ansible/latest/collections/azure/a...</td><td>html</td><td>Plugin</td><td>azure.azcollection.azure_rm_aduser module</td><td>docs.ansible.com/ansible/latest/collections/azure/azcollection/azure_rm_aduser_module.md</td><td>Ansible</td><td>2-15-5</td><td>dash-user-upload</td><td>&lt;html class=&#x27;writer-html5&#x27;&gt;&lt;!-- mirrored from docs.ansible.com/ansible/latest/collections/azure/a...</td><td>  * AnsibleFest\\n  * Products\\n  * Community\\n  * Webinars &amp; Training\\n  * Blog\\n\\n</td></tr>\n", "<tr><td>&lt;!DOCTYPE html&gt;\\n&lt;html lang=&quot;en&quot;&gt;\\n\\n&lt;head&gt;\\n  &lt;meta charset=&#x27;utf-8&#x27;&gt;\\n  &lt;meta content=&#x27;IE=edge,c...</td><td>html</td><td></td><td>fr</td><td>git-clean/fr.md</td><td>git-docs</td><td></td><td>posix-docs</td><td>&lt;!doctype html&gt;\\n&lt;html lang=&quot;en&quot;&gt;\\n\\n&lt;head&gt;\\n  &lt;meta charset=&#x27;utf-8&#x27;&gt;\\n  &lt;meta content=&#x27;ie=edge,c...</td><td>.inner\\n\\n\u0000git-clean last updated in 2.45.2\\n\\n\u0000## NOM\\n\\ngit-clean - Supprime les fichiers non s...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot;&gt;\\n&lt;!-- saved from url=(0014)about:...</td><td></td><td>clconst</td><td>LCCQueryServiceFactory.APP_CONTEXT_PREFIX</td><td>com/adobe/ep/ux/content/services/search/lccontent/LCCQueryServiceFactory.md</td><td>ActionScript</td><td>3</td><td>zeal</td><td>&lt;!doctype html public &quot;-//w3c//dtd xhtml 1.0 transitional//en&quot;&gt;\\n&lt;!-- saved from url=(0014)about:...</td><td>|  |  ActionScript® 3.0 Reference for the Adobe® Flash® Platform  Home |  Show Packages and Class...</td></tr>\n", "<tr><td>&lt;html lang=&quot;en&quot;&gt;\\n  &lt;head&gt;\\n  &lt;title&gt;Editing files in another user&amp;apos;s repository - GitHub Hel...</td><td>html</td><td></td><td>editing-files-in-another-users-repository</td><td>enterprise/2.17/user/github/managing-files-in-a-repository/editing-files-in-another-users-reposit...</td><td>github-docs</td><td></td><td>official-docs</td><td>&lt;html lang=&quot;en&quot;&gt;\\n  &lt;head&gt;\\n  &lt;title&gt;editing files in another user&amp;apos;s repository - github hel...</td><td>** This version of GitHub Enterprise will be discontinued on  This version of\\nGitHub Enterprise ...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html&gt;&lt;html\\n\\tclass=&quot;hasSidebar hasPageActions hasBreadcrumb conceptual has-default-foc...</td><td>html</td><td></td><td>signalr-howto-work-with-app-gateway0</td><td>azure/azure-signalr/signalr-howto-work-with-app-gateway0.md</td><td>microsoft-learn</td><td>en</td><td>microsoft-learn</td><td>&lt;!doctype html&gt;&lt;html\\n\\tclass=&quot;hassidebar haspageactions hasbreadcrumb conceptual has-default-foc...</td><td>article-header\\n\\n#### Share via\\n\\nend article-header end mobile-contents button\\n\\n\u0000# How to us...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html&gt;\\n&lt;html lang=&quot;en&quot;&gt;&lt;!-- Online page at https://package.elm-lang.org/packages/QiTASC...</td><td>html</td><td>Type</td><td>Hatchinq.AppBar.AppBarButton</td><td>qitasc.hatchinq.Hatchinq.AppBar.md</td><td>Elm</td><td></td><td>dash-user-upload</td><td>&lt;!doctype html&gt;\\n&lt;html lang=&quot;en&quot;&gt;&lt;!-- online page at https://package.elm-lang.org/packages/qitasc...</td><td>QiTASC / hatchinq / Hatchinq.AppBar\\n\\n# Exposed\\n\\n** type alias AppBarButton** msg =\\n\\n    { i...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot; &quot;http://www.w3.org/TR/xhtml1/DTD/x...</td><td>html</td><td>Struct</td><td>FlacSubframe</td><td>api/structFlacSubframe.md</td><td>FFmpeg</td><td>4-0</td><td>dash-user-upload</td><td>&lt;!doctype html public &quot;-//w3c//dtd xhtml 1.0 transitional//en&quot; &quot;http://www.w3.org/tr/xhtml1/dtd/x...</td><td>FFmpeg 4.0  \\n---  \\n\\ntop header\\n\\n\u0000##  Data Fields  \\n\\n---  \\nint | type  \\nint | type_code  ...</td></tr>\n", "<tr><td>&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot;&gt;\\n&lt;!-- saved from url=(0014)about:...</td><td></td><td>Package</td><td>com.adobe.dct.component.datadictionary</td><td>com/adobe/dct/component/datadictionary/package-detail.md</td><td>ActionScript</td><td>3</td><td>zeal</td><td>&lt;!doctype html public &quot;-//w3c//dtd xhtml 1.0 transitional//en&quot;&gt;\\n&lt;!-- saved from url=(0014)about:...</td><td>|  |  ActionScript® 3.0 Reference for the Adobe® Flash® Platform  Home |  Show Packages and Class...</td></tr>\n", "<tr><td>&lt;!DOCTYPE HTML&gt;\\n&lt;!-- NewPage --&gt;\\n&lt;html lang=&quot;en&quot;&gt;\\n&lt;head&gt;\\n&lt;!-- Generated by javadoc --&gt;\\n&lt;titl...</td><td>html</td><td>Interface</td><td>Demangled</td><td>ghidra/app/util/demangler/Demangled.md</td><td>Ghidra</td><td></td><td>dash-user-upload</td><td>&lt;!doctype html&gt;\\n&lt;!-- newpage --&gt;\\n&lt;html lang=&quot;en&quot;&gt;\\n&lt;head&gt;\\n&lt;!-- generated by javadoc --&gt;\\n&lt;titl...</td><td>  * All Known Implementing Classes:\\n    `AbstractDemangledFunctionDefinitionDataType`, `Demangle...</td></tr>\n", "<tr><td>&lt;h1&gt;Illuminate\\Foundation\\Console&lt;/h1&gt; &lt;h2&gt;Classes&lt;/h2&gt; &lt;table class=&quot;container-fluid underlined&quot;...</td><td></td><td></td><td>console</td><td>api/5.1/illuminate/foundation/console.md</td><td>laravel</td><td>5.1</td><td>devdocs.io</td><td>&lt;h1&gt;illuminate\\foundation\\console&lt;/h1&gt; &lt;h2&gt;classes&lt;/h2&gt; &lt;table class=&quot;container-fluid underlined&quot;...</td><td># Illuminate\\Foundation\\Console\\n\\n## Classes\\n\\nAssetPublishCommand  \\n---  \\nAutoloadCommand  \\...</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+----------------------------------------------------------------------------------------------------+----------+---------+-----------------------------------------+----------------------------------------------------------------------------------------------------+---------------+-------+----------------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|                                                                                             content|doc_format| doc_type|                                page_name|                                                                                                path|        project|version|          source|                                                                                                head|                                                                                              chunks|\n", "+----------------------------------------------------------------------------------------------------+----------+---------+-----------------------------------------+----------------------------------------------------------------------------------------------------+---------------+-------+----------------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|<html class='no-js'><!-- Mirrored from docs.unity3d.com/6000.0/Documentation/ScriptReference/Huma...|          | Property|                 HumanPartDof.RightLittle|                   docs.unity3d.com/6000.0/Documentation/ScriptReference/HumanPartDof.RightLittle.md|       Unity_3D| 6000.0|            zeal|<html class='no-js'><!-- mirrored from docs.unity3d.com/6000.0/documentation/scriptreference/huma...|Version: **Unity 6 Preview** (6000.0)\\n\\nLanguageEnglish\\n\\n  * C#\\n\\n#  HumanPartDof.RightLittle...|\n", "|<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose....|      html|Interface|                               ReturnInst|                                                                              soot/baf/ReturnInst.md|           Soot|  2-5-0|dash-user-upload|<!doctype html public \"-//w3c//dtd html 4.01 transitional//en\" \"http://www.w3.org/tr/html4/loose....|JavaScript is disabled on your browser.\\n\\nSkip navigation links\\n\\n  * Summary: \\n  * Nested | \\...|\n", "| <h1 id=\"tensorflow::ops::mean\" data-text=\"tensorflow::ops::Mean\" tabindex=\"0\">tensorflow::ops::M...|          |         |                                     mean|                                                                        class/tensorflow/ops/mean.md| tensorflow_cpp|   1.15|      devdocs.io|<h1 id=\"tensorflow::ops::mean\" data-text=\"tensorflow::ops::mean\" tabindex=\"0\">tensorflow::ops::me...|# tensorflow::ops::Mean\\n\\n`#include <math_ops.h>`\\n\\nComputes the mean of elements across dimens...|\n", "|<!doctype html><html xmlns=http://www.w3.org/1999/xhtml dir=ltr lang=en-us><meta charset=utf-8><t...|      html|   Method|SLevelViewport::RegisterGameViewportIfPIE|                      en-US/API/Editor/LevelEditor/SLevelViewport/RegisterGameViewportIfPIE/index.md|  UnrealEngine4|       |dash-user-upload|<!doctype html><html xmlns=http://www.w3.org/1999/xhtml dir=ltr lang=en-us><meta charset=utf-8><t...|# SLevelViewport::RegisterGameViewportIfPIE\\n\\n## Registers a game viewport with the Slate applic...|\n", "|<!DOCTYPE HTML>\\n<html>\\n<head>\\n\\n    <title>Inferno as a server.</title>\\n\\n    <link rel=\"styl...|      html|         |                                 msg00520|                                   inferno/historical_documents/mailing_lists/interstice/msg00520.md|sys-doc-archive|       |      posix-docs|<!doctype html>\\n<html>\\n<head>\\n\\n    <title>inferno as a server.</title>\\n\\n    <link rel=\"styl...|# Inferno as a server.\\n\\n[Date Prev][Date Next][Thread Prev][Thread Next][Date Index][Thread Ind...|\n", "|<h1> Validator <small><sup><span class=\"label label-danger\">deprecated</span></sup></small> </h1>...|          |         |                                validator|                                                            symfony/component/validator/validator.md|        symfony|    3.1|      devdocs.io|<h1> validator <small><sup><span class=\"label label-danger\">deprecated</span></sup></small> </h1>...|#  Validator\\n\\nclass **Validator** implements ValidatorInterface, MetadataFactoryInterface\\n\\nsi...|\n", "|    <h1 class=\"section\">36.1 Timing Utilities</h1> <p>Octave’s core set of functions for manipula...|          |         |                         timing-utilities|                                                                                 timing-utilities.md|         octave|      5|      devdocs.io|<h1 class=\"section\">36.1 timing utilities</h1> <p>octave’s core set of functions for manipulating...|# 36.1 Timing Utilities\\n\\nOctave’s core set of functions for manipulating time values are patter...|\n", "|<!DOCTYPE html>\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n...|      html|         |          how-to-create-a-simple-binding0|                                          dotnet/desktop/wpf/data/how-to-create-a-simple-binding0.md|microsoft-learn|     en| microsoft-learn|<!doctype html>\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n...|article-header\\n\\n#### Share via\\n\\nend article-header end mobile-contents button\\n\\n\u0000# How to: C...|\n", "|<!DOCTYPE html><html\\n\\tclass=\"hasSidebar hasPageActions hasBreadcrumb conceptual has-default-foc...|      html|         |                  addnewimcontacttogroup0|                          exchange/client-developer/web-service-reference/addnewimcontacttogroup0.md|microsoft-learn|     en| microsoft-learn|<!doctype html><html\\n\\tclass=\"hassidebar haspageactions hasbreadcrumb conceptual has-default-foc...|#### Share via\\n\\n# AddNewImContactToGroup\\n\\n  * Article\\n  * 03/29/2023\\n\\nThe **AddNewImContac...|\n", "|<h1 id=\"pandas-series-skew\">pandas.Series.skew</h1> <dl class=\"method\"> <dt id=\"pandas.Series.ske...|          |         |                       pandas.series.skew|                                                                 reference/api/pandas.series.skew.md|         pandas|   0.25|      devdocs.io|<h1 id=\"pandas-series-skew\">pandas.series.skew</h1> <dl class=\"method\"> <dt id=\"pandas.series.ske...|# pandas.Series.skew\\n\\n`Series.skew(self, axis=None, skipna=None, level=None, numeric_only=None,...|\n", "|<html class='writer-html5'><!-- Mirrored from docs.ansible.com/ansible/latest/collections/azure/a...|      html|   Plugin|azure.azcollection.azure_rm_aduser module|            docs.ansible.com/ansible/latest/collections/azure/azcollection/azure_rm_aduser_module.md|        Ansible| 2-15-5|dash-user-upload|<html class='writer-html5'><!-- mirrored from docs.ansible.com/ansible/latest/collections/azure/a...|                   * AnsibleFest\\n  * Products\\n  * Community\\n  * Webinars & Training\\n  * Blog\\n\\n|\n", "|<!DOCTYPE html>\\n<html lang=\"en\">\\n\\n<head>\\n  <meta charset='utf-8'>\\n  <meta content='IE=edge,c...|      html|         |                                       fr|                                                                                     git-clean/fr.md|       git-docs|       |      posix-docs|<!doctype html>\\n<html lang=\"en\">\\n\\n<head>\\n  <meta charset='utf-8'>\\n  <meta content='ie=edge,c...|.inner\\n\\n\u0000git-clean last updated in 2.45.2\\n\\n\u0000## NOM\\n\\ngit-clean - Supprime les fichiers non s...|\n", "|<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\">\\n<!-- saved from url=(0014)about:...|          |  clconst|LCCQueryServiceFactory.APP_CONTEXT_PREFIX|                         com/adobe/ep/ux/content/services/search/lccontent/LCCQueryServiceFactory.md|   ActionScript|      3|            zeal|<!doctype html public \"-//w3c//dtd xhtml 1.0 transitional//en\">\\n<!-- saved from url=(0014)about:...||  |  ActionScript® 3.0 Reference for the Adobe® Flash® Platform  Home |  Show Packages and Class...|\n", "|<html lang=\"en\">\\n  <head>\\n  <title>Editing files in another user&apos;s repository - GitHub Hel...|      html|         |editing-files-in-another-users-repository|enterprise/2.17/user/github/managing-files-in-a-repository/editing-files-in-another-users-reposit...|    github-docs|       |   official-docs|<html lang=\"en\">\\n  <head>\\n  <title>editing files in another user&apos;s repository - github hel...|** This version of GitHub Enterprise will be discontinued on  This version of\\nGitHub Enterprise ...|\n", "|<!DOCTYPE html><html\\n\\tclass=\"hasSidebar hasPageActions hasBreadcrumb conceptual has-default-foc...|      html|         |     signalr-howto-work-with-app-gateway0|                                         azure/azure-signalr/signalr-howto-work-with-app-gateway0.md|microsoft-learn|     en| microsoft-learn|<!doctype html><html\\n\\tclass=\"hassidebar haspageactions hasbreadcrumb conceptual has-default-foc...|article-header\\n\\n#### Share via\\n\\nend article-header end mobile-contents button\\n\\n\u0000# How to us...|\n", "|<!DOCTYPE html>\\n<html lang=\"en\"><!-- Online page at https://package.elm-lang.org/packages/QiTASC...|      html|     Type|             Hatchinq.AppBar.AppBarButton|                                                                  qitasc.hatchinq.Hatchinq.AppBar.md|            Elm|       |dash-user-upload|<!doctype html>\\n<html lang=\"en\"><!-- online page at https://package.elm-lang.org/packages/qitasc...|QiTASC / hatchinq / Hatchinq.AppBar\\n\\n# Exposed\\n\\n** type alias AppBarButton** msg =\\n\\n    { i...|\n", "|<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/x...|      html|   Struct|                             FlacSubframe|                                                                           api/structFlacSubframe.md|         FFmpeg|    4-0|dash-user-upload|<!doctype html public \"-//w3c//dtd xhtml 1.0 transitional//en\" \"http://www.w3.org/tr/xhtml1/dtd/x...|FFmpeg 4.0  \\n---  \\n\\ntop header\\n\\n\u0000##  Data Fields  \\n\\n---  \\nint | type  \\nint | type_code  ...|\n", "|<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\">\\n<!-- saved from url=(0014)about:...|          |  Package|   com.adobe.dct.component.datadictionary|                                            com/adobe/dct/component/datadictionary/package-detail.md|   ActionScript|      3|            zeal|<!doctype html public \"-//w3c//dtd xhtml 1.0 transitional//en\">\\n<!-- saved from url=(0014)about:...||  |  ActionScript® 3.0 Reference for the Adobe® Flash® Platform  Home |  Show Packages and Class...|\n", "|<!DOCTYPE HTML>\\n<!-- NewPage -->\\n<html lang=\"en\">\\n<head>\\n<!-- Generated by javadoc -->\\n<titl...|      html|Interface|                                Demangled|                                                              ghidra/app/util/demangler/Demangled.md|         Ghidra|       |dash-user-upload|<!doctype html>\\n<!-- newpage -->\\n<html lang=\"en\">\\n<head>\\n<!-- generated by javadoc -->\\n<titl...|  * All Known Implementing Classes:\\n    `AbstractDemangledFunctionDefinitionDataType`, `Demangle...|\n", "|<h1>Illuminate\\Foundation\\Console</h1> <h2>Classes</h2> <table class=\"container-fluid underlined\"...|          |         |                                  console|                                                            api/5.1/illuminate/foundation/console.md|        laravel|    5.1|      devdocs.io|<h1>illuminate\\foundation\\console</h1> <h2>classes</h2> <table class=\"container-fluid underlined\"...|# Illuminate\\Foundation\\Console\\n\\n## Classes\\n\\nAssetPublishCommand  \\n---  \\nAutoloadCommand  \\...|\n", "+----------------------------------------------------------------------------------------------------+----------+---------+-----------------------------------------+----------------------------------------------------------------------------------------------------+---------------+-------+----------------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "only showing top 20 rows"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df = spark.read.parquet(\"/mnt/efs/spark-data/shared/nl-datasets/devdocs/pre_chunked/\")\n", "df.printSchema()\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["project = 'cloud.google.com'\n", "version = None\n", "if version is None:\n", "    filename = f\"/mnt/efs/spark-data/shared/nl-datasets/devdocs/samples/{project}.json\"\n", "else:\n", "    filename = f\"/mnt/efs/spark-data/shared/nl-datasets/devdocs/samples/{project}-{version}.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["condition = F.col(\"project\") == project\n", "if version is not None:\n", "    condition = condition & (<PERSON>.col(\"version\") == version)\n", "\n", "sample = df.filter(condition).to<PERSON>andas()\n", "\n", "sample.to_json(filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type mean at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type batchmean at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type typing.Literal['mean', 'batchmean'] at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "Embedding batches: 100%|██████████| 1/1 [00:00<00:00,  1.38it/s]\n"]}], "source": ["import pandas as pd\n", "from research.eval.harness.factories import create_retriever\n", "\n", "config = {\n", "    # \"scorer\": {\n", "    #     \"name\": \"starcoder_1b\",\n", "    #     # \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "    #     \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-11\",\n", "    #     \"additional_yaml_files\": [\n", "    #         canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "    #     ],\n", "    # },\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-2\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3\",\n", "        \"cache_dir\": \"/mnt/efs/spark-data/shared/nl-datasets/devdocs/scorer_cache/\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"pre_chunked\",\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "}\n", "\n", "retriever = create_retriever(config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Embedding batches: 100%|██████████| 186/186 [00:34<00:00,  5.45it/s]\n", "Embedding batches: 100%|██████████| 178/178 [00:35<00:00,  5.08it/s]\n", "Embedding batches: 100%|██████████| 167/167 [00:31<00:00,  5.23it/s]\n", "Embedding batches: 100%|██████████| 204/204 [00:36<00:00,  5.51it/s]\n", "Embedding batches: 100%|██████████| 172/172 [00:32<00:00,  5.36it/s]\n", "Embedding batches: 100%|██████████| 183/183 [00:29<00:00,  6.20it/s]\n", "Embedding batches: 100%|██████████| 259/259 [00:38<00:00,  6.71it/s]\n", "Embedding batches: 100%|██████████| 168/168 [00:28<00:00,  5.83it/s]\n", "Embedding batches: 100%|██████████| 159/159 [00:30<00:00,  5.22it/s]\n", "Embedding batches: 100%|██████████| 177/177 [00:38<00:00,  4.60it/s]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 15\u001b[0m\n\u001b[1;32m     13\u001b[0m     buffer\u001b[38;5;241m.\u001b[39mappend(docs)\n\u001b[1;32m     14\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(buffer) \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m500\u001b[39m:\n\u001b[0;32m---> 15\u001b[0m         \u001b[43mretriever\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd_docs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     16\u001b[0m         buffer \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(buffer) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m~/augment/research/retrieval/retrieval_database.py:166\u001b[0m, in \u001b[0;36mRetrievalDatabase.add_docs\u001b[0;34m(self, docs)\u001b[0m\n\u001b[1;32m    164\u001b[0m added_rows \u001b[38;5;241m=\u001b[39m {doc\u001b[38;5;241m.\u001b[39mid: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_construct_document_row(doc) \u001b[38;5;28;01mfor\u001b[39;00m doc \u001b[38;5;129;01min\u001b[39;00m to_add}\n\u001b[1;32m    165\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdocuments\u001b[38;5;241m.\u001b[39mupdate(added_rows)\n\u001b[0;32m--> 166\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscorer\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd_docs\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    167\u001b[0m \u001b[43m    \u001b[49m\u001b[43m{\u001b[49m\n\u001b[1;32m    168\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdoc_id\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mdoc_row\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchunk_blobs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    169\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdoc_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdoc_row\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43madded_rows\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    170\u001b[0m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\n\u001b[1;32m    171\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    172\u001b[0m end_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m    173\u001b[0m time_taken \u001b[38;5;241m=\u001b[39m end_time \u001b[38;5;241m-\u001b[39m start_time\n", "File \u001b[0;32m~/augment/research/retrieval/scorers/dense_scorer_v2.py:195\u001b[0m, in \u001b[0;36mDenseRetrievalScorerV2.add_docs\u001b[0;34m(self, doc_chunks)\u001b[0m\n\u001b[1;32m    181\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m doc_id, chunks \u001b[38;5;129;01min\u001b[39;00m doc_chunks\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m    182\u001b[0m         doc_chunk_tokens[doc_id] \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m    183\u001b[0m             (\n\u001b[1;32m    184\u001b[0m                 chunk,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    192\u001b[0m             \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m chunks\n\u001b[1;32m    193\u001b[0m         ]\n\u001b[0;32m--> 195\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd_doc_tokens\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdoc_chunk_tokens\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/augment/research/retrieval/scorers/dense_scorer_v2.py:229\u001b[0m, in \u001b[0;36mDenseRetrievalScorerV2.add_doc_tokens\u001b[0;34m(self, doc_chunk_tokens)\u001b[0m\n\u001b[1;32m    221\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscoring_data\u001b[38;5;241m.\u001b[39mupdate(\n\u001b[1;32m    222\u001b[0m         {\n\u001b[1;32m    223\u001b[0m             doc_id: DocDenseScoringData(chunk_ids\u001b[38;5;241m=\u001b[39m[], emb_arr\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39marray([]))\n\u001b[1;32m    224\u001b[0m             \u001b[38;5;28;01mfor\u001b[39;00m doc_id \u001b[38;5;129;01min\u001b[39;00m doc_chunk_tokens\n\u001b[1;32m    225\u001b[0m         }\n\u001b[1;32m    226\u001b[0m     )\n\u001b[1;32m    227\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[0;32m--> 229\u001b[0m emb_BD \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdoc_embedder\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membed_batch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mflat_tokens\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mfloat()\u001b[38;5;241m.\u001b[39mcpu()\u001b[38;5;241m.\u001b[39mnumpy()\n\u001b[1;32m    230\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m emb_BD\u001b[38;5;241m.\u001b[39mshape \u001b[38;5;241m==\u001b[39m (\u001b[38;5;28mlen\u001b[39m(flat_tokens), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdoc_embedder\u001b[38;5;241m.\u001b[39membedding_dim), (\n\u001b[1;32m    231\u001b[0m     emb_BD\u001b[38;5;241m.\u001b[39mshape,\n\u001b[1;32m    232\u001b[0m     (\u001b[38;5;28mlen\u001b[39m(flat_tokens), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdoc_embedder\u001b[38;5;241m.\u001b[39membedding_dim),\n\u001b[1;32m    233\u001b[0m )\n\u001b[1;32m    235\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscoring_data\u001b[38;5;241m.\u001b[39mupdate(\n\u001b[1;32m    236\u001b[0m     {\n\u001b[1;32m    237\u001b[0m         doc_id: DocDenseScoringData(\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    242\u001b[0m     }\n\u001b[1;32m    243\u001b[0m )\n", "File \u001b[0;32m~/augment/research/models/embedding_models/cached.py:98\u001b[0m, in \u001b[0;36mCachedEmbeddingModel.embed_batch\u001b[0;34m(self, tokens_batch)\u001b[0m\n\u001b[1;32m     92\u001b[0m     embeddings \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m     93\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39membedding_model\u001b[38;5;241m.\u001b[39membed_batch(to_embed)\u001b[38;5;241m.\u001b[39mfloat()\u001b[38;5;241m.\u001b[39mcpu()\u001b[38;5;241m.\u001b[39mnumpy()\n\u001b[1;32m     94\u001b[0m     )\n\u001b[1;32m     95\u001b[0m     embeddings_hashes \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     96\u001b[0m         hsh \u001b[38;5;28;01mfor\u001b[39;00m hsh, present \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(tokens_hashes, is_present) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m present\n\u001b[1;32m     97\u001b[0m     ]\n\u001b[0;32m---> 98\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cache\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mupdate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     99\u001b[0m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[43mhsh\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43memb\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mhsh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43memb\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mzip\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43membeddings_hashes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membeddings\u001b[49m\u001b[43m)\u001b[49m\u001b[43m}\u001b[49m\n\u001b[1;32m    100\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cache\u001b[38;5;241m.\u001b[39msync()\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mfrom_numpy(np\u001b[38;5;241m.\u001b[39mstack([\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cache[hsh] \u001b[38;5;28;01mfor\u001b[39;00m hsh \u001b[38;5;129;01min\u001b[39;00m tokens_hashes]))\n", "File \u001b[0;32m<frozen _collections_abc>:949\u001b[0m, in \u001b[0;36mupdate\u001b[0;34m(self, other, **kwds)\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/shelve.py:125\u001b[0m, in \u001b[0;36mShelf.__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m    123\u001b[0m p \u001b[38;5;241m=\u001b[39m Pickler(f, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_protocol)\n\u001b[1;32m    124\u001b[0m p\u001b[38;5;241m.\u001b[39mdump(value)\n\u001b[0;32m--> 125\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdict\u001b[49m\u001b[43m[\u001b[49m\u001b[43mkey\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencode\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkeyencoding\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39mgetvalue()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/dbm/dumb.py:201\u001b[0m, in \u001b[0;36m_Database.__setitem__\u001b[0;34m(self, key, val)\u001b[0m\n\u001b[1;32m    199\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modified \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m    200\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_index:\n\u001b[0;32m--> 201\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_addkey\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_addval\u001b[49m\u001b[43m(\u001b[49m\u001b[43mval\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    202\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    203\u001b[0m     \u001b[38;5;66;03m# See whether the new value is small enough to fit in the\u001b[39;00m\n\u001b[1;32m    204\u001b[0m     \u001b[38;5;66;03m# (padded) space currently occupied by the old value.\u001b[39;00m\n\u001b[1;32m    205\u001b[0m     pos, siz \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_index[key]\n", "File \u001b[0;32m/opt/conda/lib/python3.11/dbm/dumb.py:183\u001b[0m, in \u001b[0;36m_Database._addkey\u001b[0;34m(self, key, pos_and_siz_pair)\u001b[0m\n\u001b[1;32m    181\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_addkey\u001b[39m(\u001b[38;5;28mself\u001b[39m, key, pos_and_siz_pair):\n\u001b[1;32m    182\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_index[key] \u001b[38;5;241m=\u001b[39m pos_and_siz_pair\n\u001b[0;32m--> 183\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_io\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dirfile\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43ma\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mLatin-1\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mas\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    184\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_chmod\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dirfile\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    185\u001b[0m \u001b[43m        \u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;132;43;01m%r\u001b[39;49;00m\u001b[38;5;124;43m, \u001b[39;49m\u001b[38;5;132;43;01m%r\u001b[39;49;00m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m%\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mLatin-1\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpos_and_siz_pair\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["from research.retrieval.types import Document\n", "\n", "sample = pd.read_json(filename)\n", "retriever.remove_all_docs()\n", "buffer = []\n", "for _, entry in sample.iterrows():\n", "    path = entry[\"path\"]\n", "    chunks = entry[\"chunks\"]\n", "    docs = Document.new(\n", "        text=chunks,\n", "        path=f\"docs/{project}/{version or 'latest'}/{path}\",\n", "    )\n", "    buffer.append(docs)\n", "    if len(buffer) >= 500:\n", "        retriever.add_docs(buffer)\n", "        buffer = []\n", "\n", "if len(buffer) > 0:\n", "    retriever.add_docs(buffer)\n", "    buffer = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "question = \"\"\"when providing a vscode inline completion in an extension, how do i let it skip some characters after the cursor and continue inserting other text?\n", "\"\"\"\n", "model_input = ModelInput(\n", "    prefix=question,\n", "    suffix=\"\",\n", "    path=\"coding_question.md\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import llama3 instruct tokenizer\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = retriever.query(model_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here are some excepts of documentations that might help you answer questions:\n", "docs/vscode-extension-api/latest/language-extensions/embedded-languages/index.md\n", "\n", "```\n", "\n", "# Embedded Programming Languages\n", "\n", "## Request Forwarding\n", "\n", "### Request Forwarding sample\n", "\n", "By using the `middleware` option of language client, we hijack request for\n", "auto completion:\n", "\n", "    let clientOptions: LanguageClientOptions = {\n", "      documentSelector: [{ scheme: 'file', language: 'html' }],\n", "      middleware: {\n", "        provideCompletionItem: async (document, position, context, token, next) => {\n", "          // If not in `<style>`, do not perform request forwarding\n", "          if (\n", "            !isInsideStyleRegion(\n", "              htmlLanguageService,\n", "              document.getText(),\n", "              document.offsetAt(position)\n", "            )\n", "          ) {\n", "            return await next(document, position, context, token);\n", "          }\n", "\n", "          const originalUri = document.uri.toString(true);\n", "          virtualDocumentContents.set(\n", "            <PERSON><PERSON><PERSON>,\n", "            getCSSVirtualContent(htmlLanguageService, document.getText())\n", "          );\n", "\n", "          const vdocUriString = `embedded-content://css/${encodeURIComponent(originalUri)}.css`;\n", "          const vdocUri = Uri.parse(vdocUriString);\n", "          return await commands.executeCommand<CompletionList>(\n", "            'vscode.executeCompletionItemProvider',\n", "            vdocUri,\n", "            position,\n", "            context.triggerCharacter\n", "          );\n", "        }\n", "      }\n", "    };\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/references/vscode-api/index.md\n", "\n", "```\n", "\n", "# VS Code API\n", "\n", "## workspace\n", "\n", "### InlineCompletionItemProvider\n", "\n", "The inline completion item provider interface defines the contract between\n", "extensions and the inline completion feature.\n", "\n", "Providers are asked for completions either explicitly by a user gesture or\n", "implicitly when typing.\n", "\n", "#### Methods\n", "\n", "provideInlineCompletionItems(document: TextDocument, position: Position, context: InlineCompletionContext, token: CancellationToken): ProviderResult<InlineCompletionList | InlineCompletionItem[]>\n", "\n", "Provides inline completion items for the given position and document. If\n", "inline completions are enabled, this method will be called whenever the user\n", "stopped typing. It will also be called when the user explicitly triggers\n", "inline completions or explicitly asks for the next or previous inline\n", "completion. In that case, all available inline completions should be returned.\n", "`context.triggerKind` can be used to distinguish between these scenarios.\n", "\n", "Parameter| Description  \n", "---|---  \n", "document: TextDocument| The document inline completions are requested for.  \n", "position: Position| The position inline completions are requested for.  \n", "context: InlineCompletionContext| A context object with additional\n", "information.  \n", "token: CancellationToken| A cancellation token.  \n", "Returns| Description  \n", "ProviderResult<InlineCompletionList | InlineCompletionItem[]>| An array of completion items or a thenable that resolves to an array of completion items.\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/references/vscode-api/index.md\n", "\n", "```\n", "\n", "# VS Code API\n", "\n", "## workspace\n", "\n", "### InlineCompletionContext\n", "\n", "Provides information about the context in which an inline completion was\n", "requested.\n", "\n", "#### Properties\n", "\n", "selectedCompletionInfo: SelectedCompletionInfo\n", "\n", "Provides information about the currently selected item in the autocomplete\n", "widget if it is visible.\n", "\n", "If set, provided inline completions must extend the text of the selected item\n", "and use the same range, otherwise they are not shown as preview. As an\n", "example, if the document text is `console.` and the selected item is `.log`\n", "replacing the `.` in the document, the inline completion must also replace `.`\n", "and start with `.log`, for example `.log()`.\n", "\n", "Inline completion providers are requested again whenever the selected item\n", "changes.\n", "\n", "triggerKind: InlineCompletionTriggerKind\n", "\n", "Describes how the inline completion was triggered.\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/references/vscode-api/index.md\n", "\n", "```\n", "\n", "# VS Code API\n", "\n", "## workspace\n", "\n", "### InlineCompletionTriggerKind\n", "\n", "Describes how an inline completion provider was triggered.\n", "\n", "#### Enumeration Members\n", "\n", "Invoke: 0\n", "\n", "Completion was triggered explicitly by a user gesture. Return multiple\n", "completion items to enable cycling through them.\n", "\n", "Automatic: 1\n", "\n", "Completion was triggered automatically while editing. It is sufficient to\n", "return a single completion item in this case.\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/extension-guides/virtual-workspaces/index.md\n", "\n", "```\n", "\n", "# Virtual Workspaces\n", "\n", "## Language extensions and virtual workspaces\n", "\n", "### Language selectors\n", "\n", "When registering a provider for a language feature (for example, completions,\n", "hovers, Code Actions, etc.) make sure to specify the schemes the provider\n", "supports:\n", "\n", "    return vscode.languages.registerCompletionItemProvider(\n", "      { language: 'typescript', scheme: 'file' },\n", "      {\n", "        provideCompletionItems(document, position, token) {\n", "          // ...\n", "        }\n", "      }\n", "    );\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/language-extensions/embedded-languages/index.md\n", "\n", "```\n", "\n", "# Embedded Programming Languages\n", "\n", "## Language Services\n", "\n", "### Language Services sample\n", "\n", "For example, when doing an auto completion in this position:\n", "\n", "    <div></div>\n", "    <style>.foo { | }</style>\n", "\n", "The server determines that the position is inside the region and computes a\n", "virtual CSS document with the following content (█ stands for space)):\n", "\n", "    ███████████\n", "    ███████.foo { | }████████\n", "\n", "The server then uses `vscode-css-languageservice` to analyze this document and\n", "compute a list of completion items. Because the content now contains no HTML,\n", "the CSS language service can handle it without issue. By replacing all non-CSS\n", "content with whitespace, we save ourselves from having to manually offset the\n", "positions.\n", "\n", "The server code handling completion requests:\n", "\n", "    connection.onCompletion(async (textDocumentPosition, token) => {\n", "      const document = documents.get(textDocumentPosition.textDocument.uri);\n", "      if (!document) {\n", "        return null;\n", "      }\n", "\n", "      const mode = languageModes.getModeAtPosition(document, textDocumentPosition.position);\n", "      if (!mode || !mode.doComplete) {\n", "        return CompletionList.create();\n", "      }\n", "      const doComplete = mode.doComplete!;\n", "\n", "      return doComplete(document, textDocumentPosition.position);\n", "    });\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/references/vscode-api/index.md\n", "\n", "```\n", "\n", "# VS Code API\n", "\n", "## workspace\n", "\n", "### InlineCompletionItem\n", "\n", "An inline completion item represents a text snippet that is proposed inline to\n", "complete text that is being typed.\n", "\n", "_See also_ InlineCompletionItemProvider.provideInlineCompletionItems\n", "\n", "#### Constructors\n", "\n", "new InlineCompletionItem(insertText: string | SnippetString, range?: Range, command?: Command): InlineCompletionItem\n", "\n", "Creates a new inline completion item.\n", "\n", "Parameter| Description  \n", "---|---  \n", "insertText: string | SnippetString| The text to replace the range with.  \n", "range?: Range| The range to replace. If not set, the word at the requested\n", "position will be used.  \n", "command?: Command| An optional Command that is executed _after_ inserting this\n", "completion.  \n", "Returns| Description  \n", "InlineCompletionItem  \n", "\n", "#### Properties\n", "\n", "command?: Command\n", "\n", "An optional Command that is executed _after_ inserting this completion.\n", "\n", "filterText?: string\n", "\n", "A text that is used to decide if this inline completion should be shown. When\n", "`falsy` the InlineCompletionItem.insertText is used.\n", "\n", "An inline completion is shown if the text to replace is a prefix of the filter\n", "text.\n", "\n", "insertText: string | SnippetString\n", "\n", "The text to replace the range with. Must be set. Is used both for the preview\n", "and the accept operation.\n", "\n", "range?: Range\n", "\n", "The range to replace. Must begin and end on the same line.\n", "\n", "Prefer replacements over insertions to provide a better experience when the\n", "user deletes typed text.\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/references/contribution-points/index.md\n", "\n", "```\n", "\n", "# Contribution Points\n", "\n", "## contributes.snippets\n", "\n", "Contribute snippets for a specific language. The `language` attribute is the\n", "language identifier and the `path` is the relative path to the snippet file,\n", "which defines snippets in the VS Code snippet format.\n", "\n", "The example below shows adding snippets for the Go language.\n", "\n", "    {\n", "      \"contributes\": {\n", "        \"snippets\": [\n", "          {\n", "            \"language\": \"go\",\n", "            \"path\": \"./snippets/go.json\"\n", "          }\n", "        ]\n", "      }\n", "    }\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/language-extensions/programmatic-language-features/index.md\n", "\n", "```\n", "\n", "# Programmatic Language Features\n", "\n", "## Show Code Completion Proposals\n", "\n", "Code completions provide context sensitive suggestions to the user.\n", "\n", "#### Language Server Protocol\n", "\n", "In the response to the `initialize` method, your language server needs to\n", "announce that it provides completions and whether or not it supports the\n", "`completionItem\\resolve` method to provide additional information for the\n", "computed completion items.\n", "\n", "    {\n", "        ...\n", "        \"capabilities\" : {\n", "            \"completionProvider\" : {\n", "                \"resolveProvider\": \"true\",\n", "                \"triggerCharacters\": [ '.' ]\n", "            }\n", "            ...\n", "        }\n", "    }\n", "\n", "#### Direct Implementation\n", "\n", "    class GoCompletionItemProvider implements vscode.CompletionItemProvider {\n", "        public provideCompletionItems(\n", "            document: vscode.TextDocument, position: vscode.Position, token: vscode.CancellationToken):\n", "            Thenable<vscode.CompletionItem[]> {\n", "        ...\n", "        }\n", "    }\n", "\n", "    export function activate(ctx: vscode.ExtensionContext): void {\n", "        ...\n", "        ctx.subscriptions.push(getDisposable());\n", "        ctx.subscriptions.push(\n", "            vscode.languages.registerCompletionItemProvider(\n", "                GO_MODE, new GoCompletionItemProvider(), '.', '\\\"'));\n", "        ...\n", "    }\n", "\n", "> **Basic**\n", ">\n", "> You don't support resolve providers.\n", "\n", "> **Advanced**\n", ">\n", "> You support resolve providers that compute additional information for\n", "> completion proposal the user selects. This information is displayed along-\n", "> side the selected item.\n", "\n", "\n", "```\n", "\n", "\n", "docs/vscode-extension-api/latest/language-extensions/language-server-extension-guide/index.md\n", "\n", "```\n", "\n", "# Language Server Extension Guide\n", "\n", "## Implementing a Language Server\n", "\n", "### Adding additional Language Features\n", "\n", "The first interesting feature a language server usually implements is\n", "validation of documents. In that sense, even a linter counts as a language\n", "server and in VS Code linters are usually implemented as language servers (see\n", "eslint and jshint for examples). But there is more to language servers. They\n", "can provide code completion, Find All References, or Go To Definition. The\n", "example code below adds code completion to the server. It proposes the two\n", "words 'TypeScript' and 'JavaScript'.\n", "\n", "    // This handler provides the initial list of the completion items.\n", "    connection.onCompletion(\n", "      (_textDocumentPosition: TextDocumentPositionParams): CompletionItem[] => {\n", "        // The pass parameter contains the position of the text document in\n", "        // which code complete got requested. For the example we ignore this\n", "        // info and always provide the same completion items.\n", "        return [\n", "          {\n", "            label: 'TypeScript',\n", "            kind: CompletionItemKind.Text,\n", "            data: 1\n", "          },\n", "          {\n", "            label: 'JavaScript',\n", "            kind: CompletionItemKind.Text,\n", "            data: 2\n", "          }\n", "        ];\n", "      }\n", "    );\n", "\n", "    // This handler resolves additional information for the item selected in\n", "    // the completion list.\n", "    connection.onCompletionResolve(\n", "      (item: CompletionItem): CompletionItem => {\n", "        if (item.data === 1) {\n", "          item.detail = 'TypeScript details';\n", "          item.documentation = 'TypeScript documentation';\n", "        } else if (item.data === 2) {\n", "          item.detail = 'JavaScript details';\n", "          item.documentation = 'JavaScript documentation';\n", "        }\n", "        return item;\n", "      }\n", "    );\n", "\n", "\n", "```\n", "\n", "\n", "\n", "Question:  when providing a vscode inline completion in an extension, how do i let it skip some characters after the cursor and continue inserting other text?\n", "\n"]}], "source": ["print(\"Here are some excepts of documentations that might help you answer questions:\")\n", "for i, chunk in enumerate(result[0][:10]):\n", "    tokens = len(tokenizer.tokenize_safe(chunk.text))\n", "    # print(f\"======= Chunk {i} len={len(chunk.text)} lines={len(chunk.text.splitlines())} tokens={tokens} =========\")\n", "    print(chunk.path)\n", "    print(\"\\n```\\n\")\n", "    print(chunk.text)\n", "    print(\"```\\n\\n\")\n", "\n", "print(\"\\nQuestion: \", question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "0", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[23], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mretriever\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdocuments\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mchunks\n", "\u001b[0;31m<PERSON><PERSON>E<PERSON>r\u001b[0m: 0"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "tks = np.abs(ds.get(0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|endoftext|>\n"]}], "source": ["print(tokenizer.decode([0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["eos_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode([49152])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "x = torch.Tensor(tks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_eos = x == eos_id\n", "shifted = torch.roll(is_eos, 1, 0)\n", "pad_mask = is_eos & shifted\n", "pad_mask[0] = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/04/18 02:58:34 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "WARNING:root:Cleaning up shared folder /mnt/efs/augment-nvme/python_env/2024-04-18/xiaolei-dev3/fe5a3fdc-39af-4ca9-9085-dfb4f884dc97\n"]}], "source": ["from pyspark.sql import Window\n", "\n", "window = Window.partitionBy('project', 'files', 'sum_size').orderBy(F.col('version').desc())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates = hunks.select('date').dropDuplicates().collect()\n", "\n", "df = df.withColumn(\"dupe_rank\", F.row_number().over(window))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for date, in dates:\n", "    df = hunks.where(<PERSON>.col('date')=date).join()\n", "    df.write.mode('append').parquet('')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["import pyspark.sql.functions as F\n", "\n", "# assume 'df' is your PySpark dataframe and 'text_column' is the column with text data\n", "df = spark.read.parquet(\"/mnt/efs/spark-data/shared/nl-datasets/devdocs/parquet/\")\n", "\n", "word_counts = df.select(<PERSON>.explode(F.split(F.col('content'), ' ')).alias('word')) \\\n", "                 .groupby('word') \\\n", "                 .agg(F.count('word').alias('count')) \\\n", "                 .orderBy('count', ascending=False) \\\n", "                 .limit(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>word</th><th>count</th></tr>\n", "<tr><td></td><td>4151823424</td></tr>\n", "<tr><td>\\n</td><td>80222955</td></tr>\n", "<tr><td>the</td><td>79792313</td></tr>\n", "<tr><td>&lt;a</td><td>65734922</td></tr>\n", "<tr><td>class=&quot;reference</td><td>45528164</td></tr>\n", "<tr><td>0</td><td>44123745</td></tr>\n", "<tr><td>internal&quot;</td><td>43869337</td></tr>\n", "<tr><td>&lt;span</td><td>43417685</td></tr>\n", "<tr><td>to</td><td>39149065</td></tr>\n", "<tr><td>of</td><td>31453180</td></tr>\n", "<tr><td>a</td><td>28144628</td></tr>\n", "<tr><td>in</td><td>27468007</td></tr>\n", "<tr><td>and</td><td>23657885</td></tr>\n", "<tr><td>&lt;div</td><td>23159837</td></tr>\n", "<tr><td>is</td><td>20834001</td></tr>\n", "<tr><td>class=&quot;toctree-l2&quot;&gt;&lt;a</td><td>20033696</td></tr>\n", "<tr><td>for</td><td>19533138</td></tr>\n", "<tr><td>&lt;li</td><td>14874496</td></tr>\n", "<tr><td>this</td><td>13770226</td></tr>\n", "<tr><td>&lt;/li&gt;\\n</td><td>12734231</td></tr>\n", "</table>\n"], "text/plain": ["+---------------------+----------+\n", "|                 word|     count|\n", "+---------------------+----------+\n", "|                     |4151823424|\n", "|                   \\n|  80222955|\n", "|                  the|  79792313|\n", "|                   <a|  65734922|\n", "|     class=\"reference|  45528164|\n", "|                    0|  44123745|\n", "|            internal\"|  43869337|\n", "|                <span|  43417685|\n", "|                   to|  39149065|\n", "|                   of|  31453180|\n", "|                    a|  28144628|\n", "|                   in|  27468007|\n", "|                  and|  23657885|\n", "|                 <div|  23159837|\n", "|                   is|  20834001|\n", "|class=\"toctree-l2\"><a|  20033696|\n", "|                  for|  19533138|\n", "|                  <li|  14874496|\n", "|                 this|  13770226|\n", "|              </li>\\n|  12734231|\n", "+---------------------+----------+"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["word_counts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table border='1'>\n", "<tr><th>word</th></tr>\n", "<tr><td>&lt;!DOCTYPE</td></tr>\n", "<tr><td>HTML</td></tr>\n", "<tr><td>PUBLIC</td></tr>\n", "<tr><td>&quot;-//W3C//DTD</td></tr>\n", "<tr><td>HTML</td></tr>\n", "<tr><td>4.01</td></tr>\n", "<tr><td>Transitional//EN&quot;</td></tr>\n", "<tr><td>&quot;http://www.w3.org/TR/html4/loose.dtd&quot;&gt;\\n&lt;!--</td></tr>\n", "<tr><td>NewPage</td></tr>\n", "<tr><td>--&gt;\\n&lt;html</td></tr>\n", "<tr><td>lang=&quot;en&quot;&gt;\\n&lt;head&gt;\\n&lt;!--</td></tr>\n", "<tr><td>Generated</td></tr>\n", "<tr><td>by</td></tr>\n", "<tr><td>javadoc</td></tr>\n", "<tr><td>(version</td></tr>\n", "<tr><td>1.7.0_45)</td></tr>\n", "<tr><td>on</td></tr>\n", "<tr><td>Sun</td></tr>\n", "<tr><td>Sep</td></tr>\n", "<tr><td>07</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+---------------------------------------------+\n", "|                                         word|\n", "+---------------------------------------------+\n", "|                                    <!DOCTYPE|\n", "|                                         HTML|\n", "|                                       PUBLIC|\n", "|                                 \"-//W3C//DTD|\n", "|                                         HTML|\n", "|                                         4.01|\n", "|                            Transitional//EN\"|\n", "|\"http://www.w3.org/TR/html4/loose.dtd\">\\n<!--|\n", "|                                      NewPage|\n", "|                                   -->\\n<html|\n", "|                     lang=\"en\">\\n<head>\\n<!--|\n", "|                                    Generated|\n", "|                                           by|\n", "|                                      javadoc|\n", "|                                     (version|\n", "|                                    1.7.0_45)|\n", "|                                           on|\n", "|                                          Sun|\n", "|                                          Sep|\n", "|                                           07|\n", "+---------------------------------------------+\n", "only showing top 20 rows"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.select(<PERSON><PERSON>explode(<PERSON><PERSON>split(<PERSON><PERSON>col('content'), ' ')).alias('word'))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
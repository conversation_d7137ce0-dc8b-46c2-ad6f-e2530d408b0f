# %% [markdown]
# ## Group the Stack into repos
#
# In here we take the Stack dataset, do some basic filtering, then
# group all files into repos.  Repo names are lowercased in this pipeline, and an additional column is
# added to show the primary language of the repo
# %%
"""<PERSON>ript to create the grouped by repo stack dataset."""
import pyspark.sql.functions as F

from research.data.spark import k8s_session

# %%
# Basic configs
# Note that we do not filter on file level.  Rather, we filter by repo that
# have one of the following languages as the primary language.
INCLUDED_LANG = [
    "python",
    "c",
    "c++",
    "java",
    "javascript",
    "go",
    "rust",
    "dart",
    "c-sharp",
    "typescript",
    "python-traceback",
    "shell",
    "php",
    "systemverilog",
    "verilog",
    "swift",
    "objective-c",
    "ruby",
    "cuda",
    "sql",
    "perl",
    "scala",
    "lua",
    "dockerfile",
    "cython",
    "diff",
    "jsx",
    "cucumber",
    "html",
    "css",
    "graphql",
    "assembly",
]
DROP_COLS = "lang", "max_issues_repo_head_hexsha", "max_forks_repo_head_hexsha"
LOWER_COLS = "max_stars_repo_name", "max_issues_repo_name", "max_forks_repo_name"
# Maximum allowed file size and repo size.
MAX_SIZE = 5e5
MAX_TOTAL_SIZE = 1e8

# Columns that contain the repo names and language names
REPO_COLUMN = "max_stars_repo_name"
LANG_COLUMN = "langpart"

# where to store repo list
REPOS_PATH = "s3a://augment-ephemeral/the-stack-processed/by-repo-3-repos/"

# store intermediate files
TMP_PATH = "s3a://augment-ephemeral/the-stack-processed/by-repo-3-filtered/"


# %%
def get_filtered_df(
    spark,
    max_line_length=1000,
    min_alphanum_fraction=0.25,
    max_alphanum_fraction=0.9,
    max_size=MAX_SIZE,
    drop_cols=DROP_COLS,
    lower_cols=LOWER_COLS,
):
    """Get the repo and filtered somewhat.

    A strictor but simpler version of StarCoder's filtering.
    """

    filtered_df = (
        spark.read.parquet("s3a://the-stack-dedupe/").filter(
            (F.col("avg_line_length") <= max_line_length)
            & (F.col("max_line_length") <= max_line_length)
            & (F.col("alphanum_fraction") >= min_alphanum_fraction)
            & (F.col("alphanum_fraction") < max_alphanum_fraction)
            & (F.col("size") < max_size)
        )
    ).drop(*drop_cols)
    for col in lower_cols:
        filtered_df = filtered_df.withColumn(col, F.lower(F.col(col)))
    return filtered_df


# %% [markdown]
# ## Repo filtering
#
# Directly grouping the files by repo will cause a blowup, caused by the largest repos which can
# exceed a few GBs.
# We actually filter them out, but we cannot do this at the same time as when we are compiling the
# file list because it would require us to shuffle them.
#
# As a workaround, we collect the primary language and total repo size in the first pass, and store
# the result.  It is then used in the second pass to filter files before grouping.
# %%
def get_repo_list(df, repos_path=REPOS_PATH):
    """Compute the repo list that will be kept, and the total size/primary language."""
    df.groupBy(LANG_COLUMN, REPO_COLUMN).agg(F.sum("size").alias("total_size")).groupBy(
        REPO_COLUMN
    ).agg(
        F.array_max(F.collect_list(F.struct("total_size", LANG_COLUMN))).alias(
            "max_size_lang"
        ),
        F.sum("total_size").alias("total_size"),
    ).filter(
        (F.col("total_size") < MAX_TOTAL_SIZE)
        & F.col("max_size_lang.langpart").isin(INCLUDED_LANG)
    ).write.mode(
        "overwrite"
    ).parquet(
        repos_path
    )


# %%
def filter_by_lang(spark, df, output_path=TMP_PATH, repos_path=REPOS_PATH):
    repos = spark.read.parquet(repos_path)
    repo_names = repos.select(REPO_COLUMN)
    filtered_df = df.join(F.broadcast(repo_names), on=REPO_COLUMN, how="inner")
    filtered_df.coalesce(2500).write.mode("overwrite").parquet(output_path)


# %%


def group_filtered_files(
    spark, input_path=TMP_PATH, repos_path=REPOS_PATH, repo_column=REPO_COLUMN
):
    """Group filtered files by repo and combine with computed statistics."""

    repos = spark.read.parquet(repos_path)
    filtered_df = spark.read.parquet(input_path).repartition(1000, repo_column)

    file_struct = F.struct(*[filtered_df[col] for col in filtered_df.columns])
    grouped_df = (
        filtered_df.groupBy(repo_column)
        .agg(
            F.collect_list(file_struct).alias("file_list"),
        )
        .join(repos, on=repo_column, how="inner")
    )
    return grouped_df


# %%
def main():
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "120G",
            "spark.sql.shuffle.partitions": "1000",
            "spark.executor.pyspark.memory": "100G",
        },
    )

    filtered_df = get_filtered_df(spark)

    get_repo_list(filtered_df, REPOS_PATH)

    filter_by_lang(spark, filtered_df, TMP_PATH, REPOS_PATH)

    grouped_df = group_filtered_files(spark, TMP_PATH, REPOS_PATH, REPO_COLUMN)
    # Now save this
    grouped_df.write.mode("overwrite").parquet("s3a://the-stack-processed/by-repo-3/")
    spark.stop()


# %%

if __name__ == "__main__":
    main()

- **WIll thread-safe be a feature of STL in the future?** from **"cai" <<EMAIL>>** on **8 Aug 2003 18:06:54 -0400**:
    Hi, when I'm programming in mutithread environment,I have so trouble in
    synchronizing the STL container such as vector and string. Will the standard
    commitee consider to add thread safe into STL?

    {Possibly but I believe that there are thread safe implementations
    available. -mod}



          [ See http://www.gotw.ca/resources/clcm.htm for info about ]
          [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<PERSON>w <<EMAIL>>** on **8 Aug 2003 19:24:07 -0400**:
      In message <bgvh9q$t96oq$<EMAIL>>, cai
      <<EMAIL>> writes
      >Hi, when I'm programming in mutithread environment,I have so trouble in
      >synchronizing the STL container such as vector and string. Will the standard
      >commitee consider to add thread safe into STL?

      Thread safety is (I think) an implementation detail and so outside the
      remit of the Standard. I would expect people wanting thread safety would
      use thread safe implementations, or was there something else that you
      wanted?


  - **Re: WIll thread-safe be a feature of STL in the future?** from **"Sebastian Moleski" <<EMAIL>>** on **9 Aug 2003 05:26:20 -0400**:
      "cai" <<EMAIL>> wrote in message
      news:bgvh9q$t96oq$<EMAIL>...
       > Hi, when I'm programming in mutithread environment,I have so trouble in
       > synchronizing the STL container such as vector and string. Will the standard
       > commitee consider to add thread safe into STL?

      Most popular STL implementations provide both ST and MT versions. This is
      definitely true for STLport and Dinkumware's STL (the one that comes with MSVC).
      The Standard is relatively mute on the subject of multi-threading so STL
      implementations are largely free to decide about that on their own.

      sm



            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **Ulrich Eckhardt <<EMAIL>>** on **9 Aug 2003 19:24:59 -0400**:
      cai wrote:

      > Hi, when I'm programming in mutithread environment,I have so trouble in
      > synchronizing the STL container such as vector and string. Will the
      > standard commitee consider to add thread safe into STL?

      Up to now, C++ doesn't adress threads, let alone thread safety. However,
      you are not the first to notice that, and people seem to be really working
      on it (from what I gleaned by lurking in comp.programming.threads).

      Back to your problem: most stdlibraries _are_ thread-safe. However, this
      does not mean that you can randomly modify containers from different
      threads, this kind of internal synchronization has been (afaik)
      implemented in early Java, but was abandoned for practical reasons. Search
      the web and you will find exactly why and how to do it properly.

      Uli

  - **Re: WIll thread-safe be a feature of STL in the future?** from **llewelly <<EMAIL>>** on **10 Aug 2003 06:35:35 -0400**:
      Francis Glassborow <<EMAIL>> writes:

       > In message <bgvh9q$t96oq$<EMAIL>>, cai
       > <<EMAIL>> writes
       >>Hi, when I'm programming in mutithread environment,I have so trouble in
       >>synchronizing the STL container such as vector and string. Will the standard
       >>commitee consider to add thread safe into STL?
       >
       > Thread safety is (I think) an implementation detail and so outside the
       > remit of the Standard. I would expect people wanting thread safety would
       > use thread safe implementations, or was there something else that you
       > wanted?

      FWIW, all thread-safe implementations I know of require the user to
           know and do appropriate locking. (This is a good thing, but it
           surprises some, who appear to think 'threadsafe' means 'someone
           else already put locks in all the right places.)

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **"Dhruv" <<EMAIL>>** on **10 Aug 2003 06:50:05 -0400**:
      On Sat, 09 Aug 2003 05:28:57 -0400, Pete Becker wrote:

       > cai wrote:
       >>
       >> Hi, when I'm programming in mutithread environment,I have so trouble in
       >> synchronizing the STL container such as vector and string. Will the standard
       >> commitee consider to add thread safe into STL?
       >>
       >
       > Most implementations of the C++ standard library are thread-safe. That
       > doesn't mean that you don't have to synchronize access, though. Suppose
       > you run this function in multiple threads:
       >
       > void f()
       > {
       > for (int i = 0; i < 100; ++i)
       >         std::cout << "Hello, " << "world\n";
       > }
       >
       > No amount of "thread safe" code in the library will prevent splitting
       > the two insertions, so the output could very well look a bit like this:
       >
       > Hello, Hello, world
       > world

      Ok, since we are at the topic of thread safety, in context of the STL, or
      anything else, when you talk about thread safety about something, like for
      example, if you say that (function X, or container Y) is thread safe, what
      exactly do you mean by that? I currently have a choice of 2 from which I
      cannot make out. I tried searching a lot, but could not reach any
      conclusions.

      1. I can use that function freely from any new threads that I spawn, that
      may be running concurrently, so something like:

      Thread A:
      std::copy (...)

      Thread B:
      std::copy (...)


      2. I can use the function form more than 1 thread running concurrently,
      but provided that I put some sort of mutex protection around it, so it
      would mean that at a time, only 1 (instance???) of the function is
      currently running......

      Something like this:

      Thread A:
      M = create_mutex ("some string");
      std::copy (...);
      stop_mutex (A);

      Thread B:
      M = create_mutex ("some string");
      std::copy (...);
      stop_mutex (A);


      So, when you say that (function X, or container Y) is thread safe, which
      mode of usage are you exactly talking about form out of 1 and 2.


      Regards,
      -Dhruv.














            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **James Kanze <<EMAIL>>** on **10 Aug 2003 15:17:01 -0400**:
      "Dhruv" <<EMAIL>> writes:

      |>  On Sat, 09 Aug 2003 05:28:57 -0400, Pete Becker wrote:

      |>   > cai wrote:

      |>   >> Hi, when I'm programming in mutithread environment,I have so
      |>   >> trouble in synchronizing the STL container such as vector and
      |>   >> string. Will the standard commitee consider to add thread safe
      |>   >> into STL?

      |>   > Most implementations of the C++ standard library are
      |>   > thread-safe. That doesn't mean that you don't have to
      |>   > synchronize access, though. Suppose you run this function in
      |>   > multiple threads:

      |>   > void f()
      |>   > {
      |>   > for (int i = 0; i < 100; ++i)
      |>   >         std::cout << "Hello, " << "world\n";
      |>   > }

      |>   > No amount of "thread safe" code in the library will prevent
      |>   > splitting the two insertions, so the output could very well
      |>   > look a bit like this:

      |>   > Hello, Hello, world
      |>   > world

      |>  Ok, since we are at the topic of thread safety, in context of the
      |>  STL, or anything else, when you talk about thread safety about
      |>  something, like for example, if you say that (function X, or
      |>  container Y) is thread safe, what exactly do you mean by that?

      You mean that the function's contract covers the responsibilities of
      the function and of the caller with regards to behavior in a
      multi-threaded context.

      |>  I currently have a choice of 2 from which I cannot make out.

      There is only one basic concept: the function covers threading in its
      contract.  There are an infinity of different solutions which it can
      adopt, however.

      |>  I tried searching a lot, but could not reach any conclusions.

      |>  1. I can use that function freely from any new threads that I
      |>  spawn, that may be running concurrently, so something like:

      |>  Thread A:
      |>  std::copy (...)

      |>  Thread B:
      |>  std::copy (...)

      I think that thread safety is *usually* more an issue for objects.  I
      would certainly hope that I could call std::copy on different objects
      in different threads without problems.

      |>  2. I can use the function form more than 1 thread running
      |>  concurrently, but provided that I put some sort of mutex
      |>  protection around it, so it would mean that at a time, only 1
      |>  (instance???) of the function is currently running......

      |>  Something like this:

      |>  Thread A:
      |>  M = create_mutex ("some string");
      |>  std::copy (...);
      |>  stop_mutex (A);

      |>  Thread B:
      |>  M = create_mutex ("some string");
      |>  std::copy (...);
      |>  stop_mutex (A);

      Again, mutexes protect objects, not (necessarily) functions.

      |>  So, when you say that (function X, or container Y) is thread safe,
      |>  which mode of usage are you exactly talking about form out of 1
      |>  and 2.

      When you say that function X is thread safe, it normally means that
      the function itself uses no unproected static data (other than that
      whose address or reference has been furnished by the user) in
      implementation.  Typically, this is automatically the case of a well
      designed function; on the other hand, it is not the case of strtoken,
      or a certain number of Posix interfaces.

      When you say that a class Y is thread safe, it normally means that it
      has taken threads into consideration when it specifies its contract.
      Thus, for example, the STL containers at the SGI site are thread
      safe.  The contract can vary -- the SGI containers, for example, state
      that the user is responsible for locks concerning accesses to a single
      container, but that the library is responsible for locking resources
      (such as the allocator) shared between several objects.

  - **Re: WIll thread-safe be a feature of STL in the future?** from **James Kanze <<EMAIL>>** on **10 Aug 2003 15:14:51 -0400**:
      Pete Becker <<EMAIL>> writes:

      |>  cai wrote:

      |>  > Hi, when I'm programming in mutithread environment,I have so
      |>  > trouble in synchronizing the STL container such as vector and
      |>  > string. Will the standard commitee consider to add thread safe
      |>  > into STL?

      |>  Most implementations of the C++ standard library are
      |>  thread-safe. That doesn't mean that you don't have to synchronize
      |>  access, though. Suppose you run this function in multiple threads:

      |>  void f()
      |>  {
      |>  for (int i = 0; i < 100; ++i)
      |>          std::cout << "Hello, " << "world\n";
      |>  }

      |>  No amount of "thread safe" code in the library will prevent
      |>  splitting the two insertions, so the output could very well look a
      |>  bit like this:

      |>  Hello, Hello, world
      |>  world

      That's not quite true.  The first operator<< could grab a lock, and
      return a temporary which maintained the lock until the end of the full
      expression.

      Not that that buys you anything, of course (except in exceptional
      cases).  The granularity is still too low.

  - **Re: WIll thread-safe be a feature of STL in the future?** from **James Kanze <<EMAIL>>** on **10 Aug 2003 15:14:12 -0400**:
      Francis Glassborow <<EMAIL>> writes:

      |>  In message <bgvh9q$t96oq$<EMAIL>>, cai
      |>  <<EMAIL>> writes
      |>  >Hi, when I'm programming in mutithread environment,I have so
      |>  >trouble in synchronizing the STL container such as vector and
      |>  >string. Will the standard

      |>  Thread safety is (I think) an implementation detail and so outside
      |>  the remit of the Standard. I would expect people wanting thread
      |>  safety would use thread safe implementations, or was there
      |>  something else that you wanted?

      That is the current situation, but IMHO, threading is an issue the
      standard will have to address sooner or later.  At that time, of
      course, the rules of thread safety will have to be defined.  (Like
      exception safety, there is no absolute concept of thread safety.  The
      class must, however, definir the contract that it guarantees.)

  - **Re: WIll thread-safe be a feature of STL in the future?** from **Pete Becker <<EMAIL>>** on **10 Aug 2003 15:21:23 -0400**:
      Dhruv wrote:
      >
      > Ok, since we are at the topic of thread safety, in context of the STL, or
      > anything else, when you talk about thread safety about something, like for
      > example, if you say that (function X, or container Y) is thread safe, what
      > exactly do you mean by that? I currently have a choice of 2 from which I
      > cannot make out. I tried searching a lot, but could not reach any
      > conclusions.
      >
      > [common but unsatisfactory definitions of thread safety snipped]
      >
      > So, when you say that (function X, or container Y) is thread safe, which
      > mode of usage are you exactly talking about form out of 1 and 2.
      >

      Neither. That's why I gave a higher-level example. Writing correct
      multi-threaded applications requires that threading be designed into the
      application from the top down. It's not something you can patch in by
      adding thread-safe libraries, regardless of how you define thread-safe.

      --

      "To delight in war is a merit in the soldier,
      a dangerous quality in the captain, and a
      positive crime in the statesman."
      	George Santayana

      "Bring them on."
      	George W. Bush

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **James Dennett <<EMAIL>>** on **10 Aug 2003 20:08:08 -0400**:
      Dhruv wrote:
      > On Sat, 09 Aug 2003 05:28:57 -0400, Pete Becker wrote:
      >
      >  > cai wrote:
      >  >>
      >  >> Hi, when I'm programming in mutithread environment,I have so trouble in
      >  >> synchronizing the STL container such as vector and string. Will the standard
      >  >> commitee consider to add thread safe into STL?
      >  >>
      >  >
      >  > Most implementations of the C++ standard library are thread-safe. That
      >  > doesn't mean that you don't have to synchronize access, though. Suppose
      >  > you run this function in multiple threads:
      >  >
      >  > void f()
      >  > {
      >  > for (int i = 0; i < 100; ++i)
      >  >         std::cout << "Hello, " << "world\n";
      >  > }
      >  >
      >  > No amount of "thread safe" code in the library will prevent splitting
      >  > the two insertions, so the output could very well look a bit like this:
      >  >
      >  > Hello, Hello, world
      >  > world
      >
      > Ok, since we are at the topic of thread safety, in context of the STL, or
      > anything else, when you talk about thread safety about something, like for
      > example, if you say that (function X, or container Y) is thread safe, what
      > exactly do you mean by that? I currently have a choice of 2 from which I
      > cannot make out. I tried searching a lot, but could not reach any
      > conclusions.
      >
      > 1. I can use that function freely from any new threads that I spawn, that
      > may be running concurrently, so something like:
      >
      > Thread A:
      > std::copy (...)
      >
      > Thread B:
      > std::copy (...)
      >
      >
      > 2. I can use the function form more than 1 thread running concurrently,
      > but provided that I put some sort of mutex protection around it, so it
      > would mean that at a time, only 1 (instance???) of the function is
      > currently running......
      >
      > Something like this:
      >
      > Thread A:
      > M = create_mutex ("some string");
      > std::copy (...);
      > stop_mutex (A);
      >
      > Thread B:
      > M = create_mutex ("some string");
      > std::copy (...);
      > stop_mutex (A);
      >
      >
      > So, when you say that (function X, or container Y) is thread safe, which
      > mode of usage are you exactly talking about form out of 1 and 2.

      There are more forms of thread-safety than just these 2.

      SGI used to have a good webpage describing the thread-safety
      guarantees offered by their STL implementation.  Functions can
      be used freely from multiple threads, concurrently, and data
      structures such as std::list<T> can be used from multiple threads
      only when the user supplies appropriate synchronization.

      This is usually the best compromise between safety and speed.
      Even if the library ensured that containers remained valid when
      accesses simultaneously (without additional higher-level
      synchronization) from multiple threads, the code using those
      containers would usually still need to add synchronization to
      make its own operations work correctly, and it turns out that
      the containers' built-in synchronization is then pure overhead.

      About the only good argument I can think of for having the
      containers do internal synchronization is to catch bugs in
      debug builds.

      -- James.

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **Pete Becker <<EMAIL>>** on **10 Aug 2003 20:26:39 -0400**:
      James Kanze wrote:
      >
      > Pete Becker <<EMAIL>> writes:
      >
      > |>  cai wrote:
      >
      > |>  > Hi, when I'm programming in mutithread environment,I have so
      > |>  > trouble in synchronizing the STL container such as vector and
      > |>  > string. Will the standard commitee consider to add thread safe
      > |>  > into STL?
      >
      > |>  Most implementations of the C++ standard library are
      > |>  thread-safe. That doesn't mean that you don't have to synchronize
      > |>  access, though. Suppose you run this function in multiple threads:
      >
      > |>  void f()
      > |>  {
      > |>  for (int i = 0; i < 100; ++i)
      > |>          std::cout << "Hello, " << "world\n";
      > |>  }
      >
      > |>  No amount of "thread safe" code in the library will prevent
      > |>  splitting the two insertions, so the output could very well look a
      > |>  bit like this:
      >
      > |>  Hello, Hello, world
      > |>  world
      >
      > That's not quite true.  The first operator<< could grab a lock, and
      > return a temporary which maintained the lock until the end of the full
      > expression.

      Sigh.

      void f()
      {
      for (int i = 0; i < 100; ++i)
      	{
      	std::cout << "Hello, ";
      	std::cout << "workd\n";
      	}

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Shannon Barber)** on **11 Aug 2003 05:15:26 -0400**:
      "Dhruv" <<EMAIL>> wrote in message
       > Ok, since we are at the topic of thread safety, in context of the STL, or
       > anything else, when you talk about thread safety about something, like for
       > example, if you say that (function X, or container Y) is thread safe, what
       > exactly do you mean by that? I currently have a choice of 2 from which I
       > cannot make out. I tried searching a lot, but could not reach any
       > conclusions.

      When we say something is thread-safe we mean that it is possible to
      use it correctly from multiple threads concurrently.  For example,
      some classes in the MFC employ thread-local-storage in such a manor
      that they cannot be made thread-safe (you have to make a thread-local
      copy of the object to use it).

      Some early STL implementation used global variables, which does not
      necessarily preclude thread-safety, but makes them rather inefficient
      because every function call accessing that global has to be protected
      by the same mutex.  On a system that does not have recursive mutexes
      (mutices?), it is quite problematic (you'd have to fake it).

      If you're looking for good threaded code, you want more than safety,
      you want reentrancy (you want every function to be reentrant).  For
      starters, that means no global variables (sometimes the use of globals
      can be overcome by using thread-local-storage, for example errno or
      strtok), and assignments and operations must be carefully ordered.

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Randy Maddox)** on **12 Aug 2003 18:50:59 -0400**:
      Pete Becker <<EMAIL>> wrote in message news:<<EMAIL>>...
      > Dhruv wrote:
      > >
      > > Ok, since we are at the topic of thread safety, in context of the STL, or
      > > anything else, when you talk about thread safety about something, like for
      > > example, if you say that (function X, or container Y) is thread safe, what
      > > exactly do you mean by that? I currently have a choice of 2 from which I
      > > cannot make out. I tried searching a lot, but could not reach any
      > > conclusions.
      > >
      > > [common but unsatisfactory definitions of thread safety snipped]
      > >
      > > So, when you say that (function X, or container Y) is thread safe, which
      > > mode of usage are you exactly talking about form out of 1 and 2.
      > >
      >
      > Neither. That's why I gave a higher-level example. Writing correct
      > multi-threaded applications requires that threading be designed into the
      > application from the top down. It's not something you can patch in by
      > adding thread-safe libraries, regardless of how you define thread-safe.
      >
      > --
      >
      > "To delight in war is a merit in the soldier,
      > a dangerous quality in the captain, and a
      > positive crime in the statesman."
      > 	George Santayana
      >
      > "Bring them on."
      > 	George W. Bush
      >

      Pete is absolutely correct that a thread-safe application must be
      designed that way from the beginning and that, regardless of how you
      define thread-safe, there is no way that thread-safe libraries can
      accomplish this goal for you.

      I would go even further and state that thread-safe libraries are, in
      most cases, worse than useless because:

      1. The granularity of thread-safety that can be provided in a library
      is not correct for ensuring a thread-safe application.  That is, a
      thread-safe library generally means that the library itself is
      re-entrant and synchronized so as to protect its own internal data
      structures.  The former is necessary, but not sufficient, for a
      thread-safe application.

      2. In a properly designed thread-safe application it is almost always
      the case that internal library synchronization is redundant, i.e., you
      pay the cost for it but receive no benefit in return.

      The problem is that only simple data structures with limited
      interfaces can be made thread-safe at a level that is useful to the
      application.  For example, a queue, which is often used to communicate
      between a producer and consumer thread, can be made suitably
      thread-safe by merely ensuring that push() and pop() are atomic
      operations.  A producer thread can happily push() stuff onto the queue
      without worrying about the consumer thread pop()ing stuff off that
      same queue.  Everybody is happy.  :-)

      The saving grace for a queue is that there is no way to iterate over
      the contents of the queue.  As soon as you get into a list, vector,
      map, or anything else that supports iteration, you have thread
      synchronization issues that cannot be solved in the library.  For
      example, posit a "thread-safe" list.  What does this mean?  We assume
      all list functions will be re-entrant, and the list insert and remove
      functions will be atomic.  That's really all that can be provided at
      the library level.  But, while the re-entrancy is necessary,
      re-entrancy and atomicity of add/remove are not sufficient to
      guarantee correct operation when multiple threads may be iterating
      over a single non-const list since an operation by one thread may
      invalidate an iterator in use by another thread.  For example, thread
      A erases several elements in the middle of a list, while thread B
      happens to be iterating over those very elements.  Ooops!  The list
      class cannot help with this.

      For most containers, and many other types of resources, the only way
      to ensure appropriate application level thread-safety is for the
      application to synchronize access on its own.  Once the application
      does so correctly, then any additional synchronization provided within
      a library is simply redundant.

      Other than correctly designing a multi-threaded application I don't
      know of any library level solution to thread-safety.  A re-entrant
      library implementation is necessary, but not sufficient.  Redundant
      synchronization within a library is a cost for no benefit that should
      be avoided.

      Randy.

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Dhruv)** on **13 Aug 2003 12:03:21 -0400**:
      James Kanze <<EMAIL>> wrote in message
      news:<<EMAIL>>...
      > "Dhruv" <<EMAIL>> writes:
      >
      > |>  On Sat, 09 Aug 2003 05:28:57 -0400, Pete Becker wrote:
      >
      > |>   > cai wrote:
      >
      > |>   >> Hi, when I'm programming in mutithread environment,I have so
      > |>   >> trouble in synchronizing the STL container such as vector and
      > |>   >> string. Will the standard commitee consider to add thread safe
      > |>   >> into STL?
      >
      > |>   > Most implementations of the C++ standard library are
      > |>   > thread-safe. That doesn't mean that you don't have to
      > |>   > synchronize access, though. Suppose you run this function in
      > |>   > multiple threads:
      >
      > |>   > void f()
      > |>   > {
      > |>   > for (int i = 0; i < 100; ++i)
      > |>   >         std::cout << "Hello, " << "world\n";
      > |>   > }
      >
      > |>   > No amount of "thread safe" code in the library will prevent
      > |>   > splitting the two insertions, so the output could very well
      > |>   > look a bit like this:
      >
      > |>   > Hello, Hello, world
      > |>   > world
      >
      > |>  Ok, since we are at the topic of thread safety, in context of the
      > |>  STL, or anything else, when you talk about thread safety about
      > |>  something, like for example, if you say that (function X, or
      > |>  container Y) is thread safe, what exactly do you mean by that?
      >
      > You mean that the function's contract covers the responsibilities of
      > the function and of the caller with regards to behavior in a
      > multi-threaded context.
      >
      > |>  I currently have a choice of 2 from which I cannot make out.
      >
      > There is only one basic concept: the function covers threading in its
      > contract.  There are an infinity of different solutions which it can
      > adopt, however.
      >
      > |>  I tried searching a lot, but could not reach any conclusions.
      >
      > |>  1. I can use that function freely from any new threads that I
      > |>  spawn, that may be running concurrently, so something like:
      >
      > |>  Thread A:
      > |>  std::copy (...)
      >
      > |>  Thread B:
      > |>  std::copy (...)
      >
      > I think that thread safety is *usually* more an issue for objects.  I
      > would certainly hope that I could call std::copy on different objects
      > in different threads without problems.
      >
      Ok, so thread safety applies mainly to objects (that hold data), and
      not to functions. Only if the functions have a static variable that
      they modify (global or local static), then thread safety comes
      in. Otherwise, the function is usually thread safe. What about classes
      without any fields (data members). Does thread safety apply even to
      them. Assuming that the class does not contain any static data
      members, and the menber functions do not modify any static data.



      > |>  2. I can use the function form more than 1 thread running
      > |>  concurrently, but provided that I put some sort of mutex
      > |>  protection around it, so it would mean that at a time, only 1
      > |>  (instance???) of the function is currently running......
      >
      > |>  Something like this:
      >
      > |>  Thread A:
      > |>  M = create_mutex ("some string");
      > |>  std::copy (...);
      > |>  stop_mutex (A);
      >
      > |>  Thread B:
      > |>  M = create_mutex ("some string");
      > |>  std::copy (...);
      > |>  stop_mutex (A);
      >
      > Again, mutexes protect objects, not (necessarily) functions.
      >
      > |>  So, when you say that (function X, or container Y) is thread safe,
      > |>  which mode of usage are you exactly talking about form out of 1
      > |>  and 2.
      >
      > When you say that function X is thread safe, it normally means that
      > the function itself uses no unproected static data (other than that
      > whose address or reference has been furnished by the user) in
      > implementation.  Typically, this is automatically the case of a well
      > designed function; on the other hand, it is not the case of strtoken,
      > or a certain number of Posix interfaces.
      >


      Yes, so as I said above. Am I right?
      By address or reference, you mean parameters right?



      > When you say that a class Y is thread safe, it normally means that it
      > has taken threads into consideration when it specifies its contract.
      > Thus, for example, the STL containers at the SGI site are thread
      > safe.  The contract can vary -- the SGI containers, for example, state
      > that the user is responsible for locks concerning accesses to a single
      > container, but that the library is responsible for locking resources
      > (such as the allocator) shared between several objects.


      So, suppose I make a claim that container X is thread thread safe, I
      will have to specify what level of thread safety I'm talking about
      right. I was thinking that there is some sort of globally accepted
      definition of thread safety, just like there is a C++ standard, so I
      got a bit confused :-)

      Regards,
      -Dhruv.

      BTW, what is 'granurality'?

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **David Bradley <<EMAIL>>** on **18 Aug 2003 19:58:15 -0400**:
      Jerry Feldman wrote:
      > IMHO:
      > All libraries should be threadsafe by design. Unfortunately, there are
      > many C and C++ functions that are not threadsafe. Thread implementation
      > teams get around this by providing wrappers or simply duplicating the
      > functionality.

      Just a reminder, from a previous discussion here, threadsafe means
      different things to different people. 1. safely allow a single instance
      of a class to be accessed safely by multiple threads. 2. safely allow an
      instance to be accessed by a single thread. And I think there was a
      third, which I forget. From the rest of your message, it would appear
      you're looking for 2.

      David Bradley

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Randy Maddox)** on **19 Aug 2003 14:15:47 -0400**:
      Jerry Feldman <<EMAIL>> wrote in message
      news:<<EMAIL>>...
      > IMHO:
      > All libraries should be threadsafe by design. Unfortunately, there are
      > many C and C++ functions that are not threadsafe. Thread implementation
      > teams get around this by providing wrappers or simply duplicating the
      > functionality.
      >

      I agree only partly:  Some parts of some libraries should be
      threadsafe by design.  However, I also disagree partly since it is not
      possible to make many parts of a library threadsafe in a useful way.
      For example, how do you make a list threadsafe?

      After thinking about that one for a moment, please explain how a
      threadsafe list is able to ensure thread safety when one thread is
      iterating through the list, and another thread is erasing elements
      from that same list.

      After giving up on that one and realizing that, in order to use a list
      safely, the correct level of access control is at the application
      level, rather than at the list level, please explain how using a
      threadsafe list in a properly designed multi-threaded application does
      not introduce redundant locking at the list level.

      You see where I'm going with this.  If the application is correct in
      its use of the list, then any threadsafe locking going on in the list
      itself is a cost for no benefit.

      >
      > Another reason for my desire for all libraries to be threadsafe is that
      > sometimes when applications are in maintenance mode, the maintenance
      > programmer may not be as well versed in threads as we may desire,
      > causing a potential application failure of data corruption.

      And advertising the library as threadsafe helps in what way?  By
      allowing that less well versed programmer to believe they can use it
      in multiple threads without correct design at the application level?

      If you ask me, a library advertised as threadsafe plus a programmer
      not well versed in threading issues is a recipe for disaster.

      Randy.

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Randy Maddox)** on **19 Aug 2003 14:42:51 -0400**:
      Jerry Feldman <<EMAIL>> wrote in message
      news:<<EMAIL>>...
      > IMHO:
      > All libraries should be threadsafe by design. Unfortunately, there are
      > many C and C++ functions that are not threadsafe. Thread implementation
      > teams get around this by providing wrappers or simply duplicating the
      > functionality.
      >

      While I cannot agree completely with this statement, I could agree
      completely with the following:

      All libraries should be re-entrant by design so that they may be
      safely used in a properly designed multi-threaded application.
      However, except in the case of certain specific data structures, such
      as stack and queue, no internal locking should be done in the library
      itself since such locking is almost always completely redundant in a
      properly designed multi-threaded application.

      Randy.

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **Andy Sawyer <<EMAIL>>** on **20 Aug 2003 08:40:32 -0400**:
      In article <<EMAIL>>,
        on 19 Aug 2003 14:15:47 -0400,
        <EMAIL> (Randy Maddox) wrote:

       > If you ask me, a library advertised as threadsafe plus a programmer
       > not well versed in threading issues is a recipe for disaster.

      A programmer not well versed in threading issues can be a recipie for
      disaster anyway in a threaded environment. The "threadsafe" library is
      merely garnish ;)

      Regards,
        Andy S
  - **Re: WIll thread-safe be a feature of STL in the future?** from **Jerry Feldman <<EMAIL>>** on **20 Aug 2003 12:47:21 -0400**:
      On 19 Aug 2003 14:15:47 -0400
      <EMAIL> (Randy Maddox) wrote:

      > After thinking about that one for a moment, please explain how a
      > threadsafe list is able to ensure thread safety when one thread is
      > iterating through the list, and another thread is erasing elements
      > from that same list.
      Not very difficult, but in the case of some containers like lists, not
      too useful. However, I've used threadsafe queues. When tied to condition
      waits they work beautifully.

      But, in specifically answering your question, with the use of a mutex,
      and some validity flags, it's doable.

      But, you don't want to share this type of data structure between threads
      unless there is considerable coordination between the threads using that
      container.

      Also, just to add to my original opinion, threadsafeness also adds some
      overhead.
  - **Re: WIll thread-safe be a feature of STL in the future?** from **Jerry Feldman <<EMAIL>>** on **20 Aug 2003 12:49:24 -0400**:
      On 20 Aug 2003 08:40:32 -0400
      Andy Sawyer <<EMAIL>> wrote:

      > A programmer not well versed in threading issues can be a recipie for
      > disaster anyway in a threaded environment. The "threadsafe" library is
      > merely garnish ;)
      That is agreed. However, with threadsafe libraries, the programmer can
      use the standard classes and library functions. Though C++ does add some
      additional constraints in that one must consider both library functions
      and classes. And, template classes further add to this.
  - **Re: WIll thread-safe be a feature of STL in the future?** from **Jerry Feldman <<EMAIL>>** on **20 Aug 2003 12:59:44 -0400**:
      On 19 Aug 2003 14:42:51 -0400
      <EMAIL> (Randy Maddox) wrote:

      > Jerry Feldman <<EMAIL>> wrote in message
      > news:<<EMAIL>>...
      > > IMHO:
      > > All libraries should be threadsafe by design. Unfortunately, there
      > > are many C and C++ functions that are not threadsafe. Thread
      > > implementation teams get around this by providing wrappers or simply
      > > duplicating the functionality.
      > >
      >
      > While I cannot agree completely with this statement, I could agree
      > completely with the following:
      >
      > All libraries should be re-entrant by design so that they may be
      > safely used in a properly designed multi-threaded application.
      > However, except in the case of certain specific data structures, such
      > as stack and queue, no internal locking should be done in the library
      > itself since such locking is almost always completely redundant in a
      > properly designed multi-threaded application.
      I can easily live with your statement. Lists, stacks, and queues can be
      very troublesome in a multi-threaded environment.

      One of the problems with threads in general is that it is readily
      available to the application programmer who may not have any training in
      real-time and multi-tasking issues. I came into an environment as a
      consultant one time where their application was a multi-threaded web
      based application. The programmers had very little knowledge of what
      thread safety meant. Fortunately they had a competent engineering team
      at a different site working on a completely new version.

      I think that any programmer who works on threads should be served a meal
      with 3 others using the same rules as Dijkstra's Dining Philosophers.
      The following URL has a nice graphic:
      http://java.sun.com/docs/books/tutorial/essential/threads/deadlock.html



  - **Re: WIll thread-safe be a feature of STL in the future?** from **David Bradley <<EMAIL>>** on **20 Aug 2003 19:46:23 -0400**:
      Randy Maddox wrote:
       > After thinking about that one for a moment, please explain how a
       > threadsafe list is able to ensure thread safety when one thread is
       > iterating through the list, and another thread is erasing elements
       > from that same list.

      This gets back to my previous post. What you describe is not what some
      are talking about when they use the term "thread safe". What they mean
      is that any single instance can be used in the context of a single
      thread safely. That means that any shared data among instances is
      protected. It does not mean the instances can be touched by multiple
      threads.

      David Bradley

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **<EMAIL> (Le Chaud Lapin)** on **20 Aug 2003 19:59:04 -0400**:
      "cai" <<EMAIL>> wrote in message news:<bgvh9q$t96oq$<EMAIL>>...
       > Hi, when I'm programming in mutithread environment,I have so trouble in
       > synchronizing the STL container such as vector and string. Will the standard
       > commitee consider to add thread safe into STL?
       >
       > {Possibly but I believe that there are thread safe implementations
       > available. -mod}
       >

      "...set of functionally orthogonal virtuous primitives..."

      In this case, adding a new primitive to STL instead of modifying the
      existing primitives keeps your primitives functionally orthogonal.

      For my threading applications, I use a Shared<> template.  Any object
      to be shared is declared as such:

      struct Foo
      {
      } ;

      Shared <Foo> my_foo;

      my_foo.acquire();  // any other 'acquire()' will block
      // do some stuff with my_foo
      my_foo.release();

      You can also specify maximum wait throuh overloaded 'acquire()'

      my_foo.acquire (4.5); // wait maximum of 4.5 seconds.

      The best thing about this approach is that you do you have to modify
      your existing classes.

      -Chaud Lapin-

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

  - **Re: WIll thread-safe be a feature of STL in the future?** from **Ed Avis <<EMAIL>>** on **21 Aug 2003 05:43:57 -0400**:
      <EMAIL> (Randy Maddox) writes:

       >Some parts of some libraries should be threadsafe by design.
       >However, I also disagree partly since it is not possible to make many
       >parts of a library threadsafe in a useful way.  For example, how do
       >you make a list threadsafe?
       >
       >After thinking about that one for a moment, please explain how a
       >threadsafe list is able to ensure thread safety when one thread is
       >iterating through the list, and another thread is erasing elements
       >from that same list.

      Maybe some kind of multi-version concurrency control?  But I don't
      know if that is implementable without horrible slowdowns in the common
      (readers only, or single-threaded) case.

      Still there's a case for saying that the default behaviour should be
      the safest, but if the programmer explicitly chooses to turn off the
      list class's built-in locking, that's fine.  Then, as with explicitly
      choosing to catch an exception, it is clear where the responsibility
      lies.

  - **Re: WIll thread-safe be a feature of STL in the future?** from **David Bradley <<EMAIL>>** on **21 Aug 2003 06:54:34 -0400**:
      Jerry Feldman wrote:
       > That is agreed. However, with threadsafe libraries, the programmer can
       > use the standard classes and library functions. Though C++ does add some
       > additional constraints in that one must consider both library functions
       > and classes. And, template classes further add to this.

      Even being threadsafe doesn't ensure success. Deadlocks, performance are
      examples of other issues. The golden rule of writing good thread safe
      code is to expose as little code as possible to multiple threads.
      Attempting to make everything threadsafe is really a waste of effort for
      most applications.

      David Bradley

            [ See http://www.gotw.ca/resources/clcm.htm for info about ]
            [ comp.lang.c++.moderated.    First time posters: Do this! ]

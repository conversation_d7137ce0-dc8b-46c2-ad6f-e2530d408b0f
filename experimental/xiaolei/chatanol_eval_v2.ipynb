{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "config = {\n", "    # \"scorer\": {\n", "    #     \"name\": \"starcoder_1b\",\n", "    #     # \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "    #     \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-11\",\n", "    #     \"additional_yaml_files\": [\n", "    #         canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "    #     ],\n", "    # },\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-2\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "}\n", "\n", "retriever = create_retriever(config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "from research.fastbackward import distributed\n", "distributed.init_distributed_for_training(1)\n", "\n", "from augment.research.retrieval import retrieval_database\n", "\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "\n", "import research.core.prompt_formatters  # pull in registrations for prompt formatters\n", "\n", "from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastbackward_checkpoint\n", "\n", "from research.retrieval import utils as retrieval_utils\n", "from research.retrieval import chunking_functions\n", "from research.retrieval.retrieval_database import RetrievalDatabase"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from research.core.types import Document\n", "\n", "augment_repo = json.load(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_jun_08_2024.json\"\n", "    ).open()\n", ")\n", "\n", "retriever.add_docs(\n", "    Document(**doc)\n", "    for doc in augment_repo[\"docs\"]\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from research.core.model_input import ModelInput\n", "\n", "# List of (question, list of gold paths) tuples\n", "examples = [\n", "    (\n", "        \"Where in the inference host do we implement non-neural speculative decoding?\",\n", "        (\n", "            \"services/inference_host/server/continuous_batching/longest_overlap_lm.py\",\n", "            \"services/inference_host/server/continuous_batching/speculation.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"where in vscode client do we check if chat feature is enabled?\",\n", "        (\"clients/vscode/package.json\",)\n", "    ),\n", "    (\n", "        \"find me a script to change model parallel size for the fastbackward\",\n", "        (\n", "            \"research/fastbackward/scripts/resize_model_parallel_checkpoint.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Summarize all the different retrieval scorers implemented in our research code.\",\n", "        (\n", "            \"research/retrieval/scorers/dense_scorer.py\",\n", "            \"research/retrieval/scorers/good_enough_bm25_scorer.py\",\n", "            \"research/retrieval/scorers/recency_scorer.py\",\n", "            \"research/retrieval/scorers/ensembled_scorer.py\",\n", "            \"research/retrieval/scorers/dense_scorer_v2.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Do we have a method for reading a jsonl zst file?\",\n", "        (\n", "            \"research/eval/harness/utils.py\",\n", "            \"research/core/utils_for_file.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Are there evaluation methods on the AbstractSystem interface?\",\n", "        (\"research/eval/harness/systems/abs_system.py\",)\n", "    ),\n", "    (\n", "        \"How do I run perplexity distillation with Megatron/NeoX?\",\n", "        (\"research/gpt-neox/megatron/model/ppl_distill.py\",)\n", "    ),\n", "    (\n", "        \"How do I query s3 data in our research code?\",\n", "        (\"research/core/utils_for_s3.py\",)\n", "    ),\n", "    (\n", "        \"Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\",\n", "        (\"research/core/edit_prompt_input.py\",),\n", "    ),\n", "    (\n", "        \"Have we deployed the diesel retriever to production?\",\n", "        (\"services/deploy/diesel1_deploy.jsonnet\",)\n", "    ),\n", "    (\n", "        \"Is there a research interface for rerankers?\",\n", "        (\"research/retrieval/rerankers/reranker_interface.py\",)\n", "    ),\n", "    (\n", "        \"Where does filtering retrieved chunks vs prefix/suffix happen in services?\",\n", "        (\"services/completion_host/single_model_server/overlap.py\", \"services/completion_host/single_model_server/single_round_handler.py\")\n", "    ),\n", "    (\n", "        \"Where does the actual search over document embeddings happen for dense retrieval?\",\n", "        (\n", "            \"services/embeddings_search_host/cpu_server/src/index_search.rs\",\n", "            \"research/retrieval/scorers/dense_scorer.py\",\n", "            \"research/retrieval/scorers/dense_scorer_v2.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Where is the Rogue data pipeline located?\",\n", "        (\"research/data/rag/rogue.py\",)\n", "    ),\n", "    (\n", "        \"Does fastforward model in research use all available GPUs?\",\n", "        (\"research/models/fastforward_llama_models.py\",)\n", "    ),    \n", "    (\n", "        (\n", "            \"Where can I find eng.jsonnet?\",\n", "            \"Where can I find eng.jsonnet\",\n", "            \"Where can I find eng.jsonnet under augment folder?\",\n", "        ),\n", "        (\"deploy/common/eng.jsonnet\",)\n", "    ),\n", "    (\n", "        \"What does VSCode extension sends to the chat backend as context?\",\n", "        (\"clients/vscode/src/chat/chat-model.ts\",)\n", "    ),\n", "    (\n", "        (\n", "            \"Where is spark pipeline that generates roguesl fine-tuning data, look in `experimental/yury`\",\n", "            \"Where is spark pipeline that generates roguesl fine-tuning data, look in `experimental/yury`, but not in `experimental/michiel`\",\n", "        ),\n", "        (\n", "            \"experimental/yury/notebooks/rogue/samples_to_dataset_dync.ipynb\",\n", "            \"experimental/yury/notebooks/rogue/generate_samples_sl.ipynb\",\n", "        ),\n", "    )\n", "]\n", "\n", "def eval(question, gold_paths, chunk0_idx = None):\n", "    # query = question\n", "    # query = \"### Instruction:\\n\" + question\n", "    # query = \"### bla bla bla bla:\\n\" + question\n", "    # query = \"### Instruction:\\n\" + question + \"\\n### Instruction:\\nCan you elaborate?\"\n", "    query = (\n", "        \"### Instruction:\\n\" + question + \"\\n\"\n", "        + \"### Response:\\nI don't know.\\n\"\n", "        + \"### Instruction:\\nCan you elaborate?\"\n", "    )\n", "    chunks, scores = retriever.query(ModelInput(query), top_k=10)\n", "\n", "    print('\\n --------------- \\n' + query + '\\n')\n", "    print(\"Gold paths: \", gold_paths)\n", "\n", "    gold_paths_found = set()\n", "\n", "    for idx, (chunk, score) in enumerate(zip(chunks, scores)):\n", "        print(f\"({idx}) -- {chunk.path} {chunk.line_range} {score} --\")\n", "\n", "        if idx < 10 and chunk.path in gold_paths:\n", "            gold_paths_found.add(chunk.path)\n", "\n", "    perc_gold_found = float(len(gold_paths_found)) / len(gold_paths)\n", "\n", "    chunk0 = chunks[chunk0_idx] if chunk0_idx is not None else None\n", "    if chunk0:\n", "        print(\"\\nTop chunk:\\n\")\n", "        print(f\"({chunk0.path} {chunk0.line_range} --\\n\")\n", "        print(chunk0.text)\n", "\n", "    return perc_gold_found\n", "\n", "\n", "all_perc_gold_found = []\n", "\n", "for example in examples:\n", "    perc_gold_found_per_example = []\n", "    if isinstance(example[0], str):\n", "        questions = [example[0]]\n", "    else:\n", "        questions = example[0]\n", "    for question in questions:\n", "        perc_gold_found = eval(question, example[1])\n", "        perc_gold_found_per_example.append(perc_gold_found)\n", "    perc_gold_found_per_example = np.mean(perc_gold_found_per_example)\n", "    all_perc_gold_found.append(perc_gold_found_per_example)\n", "    print(f\"\\nGold found: {perc_gold_found_per_example}\")\n", "\n", "print(f\"Avg gold found: {sum(all_perc_gold_found) / len(all_perc_gold_found)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# chatanol1-11\n", "# https://gist.github.com/urikz/6ee140c6753878d156fb4cbc20aafb1f\n", "0.624074074074074\n", "\n", "# chatanol1-14\n", "# https://gist.github.com/urikz/018fdc7b441fb316f09b726442183c13\n", "0.7388888888888889\n", "\n", "# chatanol1-16-2\n", "# https://gist.github.com/urikz/08c462272d99cdb4a56c8bcdf6b3915d\n", "0.6907407407407408\n", "\n", "# chatanol1-16-2, but we prepend \"### Instruction:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.7\n", "\n", "# chatanol1-16-2, but we prepend \"### bla bla bla bla:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.8203703703703703\n", "\n", "# chatanol1-16-2, but we prepend \"### Instruction:\\n\" before every question\n", "# https://gist.github.com/urikz/5cd36c75e73fb8a44b2927f10577c483\n", "0.6888888888888889"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from research.core.model_input import ModelInput\n", "\n", "# List of (question, list of gold paths) tuples\n", "examples = [\n", "    (\n", "        \"Where in the inference host do we implement non-neural speculative decoding?\",\n", "        (\n", "            \"services/inference_host/server/continuous_batching/longest_overlap_lm.py\",\n", "            \"services/inference_host/server/continuous_batching/speculation.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"where in vscode client do we check if chat feature is enabled?\",\n", "        (\"clients/vscode/package.json\",)\n", "    ),\n", "    (\n", "        \"find me a script to change model parallel size for the fastbackward\",\n", "        (\n", "            \"research/fastbackward/scripts/resize_model_parallel_checkpoint.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Summarize all the different retrieval scorers implemented in our research code.\",\n", "        (\n", "            \"research/retrieval/scorers/dense_scorer.py\",\n", "            \"research/retrieval/scorers/good_enough_bm25_scorer.py\",\n", "            \"research/retrieval/scorers/recency_scorer.py\",\n", "            \"research/retrieval/scorers/ensembled_scorer.py\",\n", "            \"research/retrieval/scorers/dense_scorer_v2.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Do we have a method for reading a jsonl zst file?\",\n", "        (\n", "            \"research/eval/harness/utils.py\",\n", "            \"research/core/utils_for_file.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Are there evaluation methods on the AbstractSystem interface?\",\n", "        (\"research/eval/harness/systems/abs_system.py\",)\n", "    ),\n", "    (\n", "        \"How do I run perplexity distillation with Megatron/NeoX?\",\n", "        (\"research/gpt-neox/megatron/model/ppl_distill.py\",)\n", "    ),\n", "    (\n", "        \"How do I query s3 data in our research code?\",\n", "        (\"research/core/utils_for_s3.py\",)\n", "    ),\n", "    (\n", "        \"Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\",\n", "        (\"research/core/edit_prompt_input.py\",),\n", "    ),\n", "    (\n", "        \"Have we deployed the diesel retriever to production?\",\n", "        (\"services/deploy/diesel1_deploy.jsonnet\",)\n", "    ),\n", "    (\n", "        \"Is there a research interface for rerankers?\",\n", "        (\"research/retrieval/rerankers/reranker_interface.py\",)\n", "    ),\n", "    (\n", "        \"Where does filtering retrieved chunks vs prefix/suffix happen in services?\",\n", "        (\"services/completion_host/single_model_server/overlap.py\", \"services/completion_host/single_model_server/single_round_handler.py\")\n", "    ),\n", "    (\n", "        \"Where does the actual search over document embeddings happen for dense retrieval?\",\n", "        (\n", "            \"services/embeddings_search_host/cpu_server/src/index_search.rs\",\n", "            \"research/retrieval/scorers/dense_scorer.py\",\n", "            \"research/retrieval/scorers/dense_scorer_v2.py\",\n", "        )\n", "    ),\n", "    (\n", "        \"Where is the Rogue data pipeline located?\",\n", "        (\"research/data/rag/rogue.py\",)\n", "    ),\n", "    (\n", "        \"Does fastforward model in research use all available GPUs?\",\n", "        (\"research/models/fastforward_llama_models.py\",)\n", "    ),    \n", "    (\n", "        (\n", "            \"Where can I find eng.jsonnet?\",\n", "            \"Where can I find eng.jsonnet\",\n", "            \"Where can I find eng.jsonnet under augment folder?\",\n", "        ),\n", "        (\"deploy/common/eng.jsonnet\",)\n", "    ),\n", "    (\n", "        \"What does VSCode extension sends to the chat backend as context?\",\n", "        (\"clients/vscode/src/chat/chat-model.ts\",)\n", "    ),\n", "    (\n", "        (\n", "            \"Where is spark pipeline that generates roguesl fine-tuning data, look in `experimental/yury`\",\n", "            \"Where is spark pipeline that generates roguesl fine-tuning data, look in `experimental/yury`, but not in `experimental/michiel`\",\n", "        ),\n", "        (\n", "            \"experimental/yury/notebooks/rogue/samples_to_dataset_dync.ipynb\",\n", "            \"experimental/yury/notebooks/rogue/generate_samples_sl.ipynb\",\n", "        ),\n", "    )\n", "]\n", "\n", "def eval_multi_turn(question, previous_question, previous_answer, gold_paths):\n", "    if previous_question is None:\n", "        query = \"### Instruction:\\n\" + question\n", "    elif previous_answer is None:\n", "        query = \"### Instruction:\\n\" + previous_question + \"\\n### Instruction:\\n\" + question\n", "    else:\n", "        query = (\n", "            \"### Instruction:\\n\" + previous_question + \"\\n\"\n", "            + \"### Response:\\n\" + previous_answer + \"\\n\"\n", "            + \"### Instruction:\\n\" + question\n", "        )\n", "\n", "    chunks, scores = retriever.query(ModelInput(query), top_k=10)\n", "\n", "    print('\\n --------------- \\n' + query + '\\n')\n", "    print(\"Gold paths: \", gold_paths)\n", "\n", "    gold_paths_found = set()\n", "\n", "    for idx, (chunk, score) in enumerate(zip(chunks, scores)):\n", "        print(f\"({idx}) -- {chunk.path} {chunk.line_range} {score} --\")\n", "\n", "        if idx < 10 and chunk.path in gold_paths:\n", "            gold_paths_found.add(chunk.path)\n", "\n", "    perc_gold_found = float(len(gold_paths_found)) / len(gold_paths)\n", "    return perc_gold_found\n", "\n", "\n", "all_perc_gold_found = []\n", "previous_question = None\n", "previous_answer = \"I don't know.\"\n", "\n", "for example in examples:\n", "    perc_gold_found_per_example = []\n", "    if isinstance(example[0], str):\n", "        questions = [example[0]]\n", "    else:\n", "        questions = example[0]\n", "    for question in questions:\n", "        perc_gold_found = eval_multi_turn(question, previous_question, previous_answer, example[1])\n", "        perc_gold_found_per_example.append(perc_gold_found)\n", "    previous_question = question        \n", "    perc_gold_found_per_example = np.mean(perc_gold_found_per_example)\n", "    all_perc_gold_found.append(perc_gold_found_per_example)\n", "    print(f\"\\nGold found: {perc_gold_found_per_example}\")\n", "\n", "print(f\"Avg gold found: {sum(all_perc_gold_found) / len(all_perc_gold_found)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# chatanol-16 where we add previous questions to chat history\n", "# https://gist.github.com/urikz/95b50add6c7b062bee9911977f5f860a\n", "# 0.7481481481481481\n", "\n", "# chatanol-14 where we add previous questions to chat history\n", "# https://gist.github.com/urikz/f71c80aa2c4f48e5a7e499354d240a3b\n", "# 0.24444444444444446\n", "\n", "# chatanol-16 where we add previous questions to chat history with answer being \"I don't know\"\n", "# https://gist.github.com/urikz/703a5f7619e450797a8c136fb5bac22c\n", "# 0.6722222222222222\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# question = \"\"\"### Instruction:\n", "# Summarize all the different retrieval rerankers implemented in `research/retrieval/libraries/rerankers`\n", "# ### Instruction:\n", "# where do we use each of them?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Does fastforward model in research use all available GPUs?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Which prompt formatters are implement the chat in the `base/`?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Which prompt formatters are implement the chat in the `base/`?\n", "# ### Instruction:\n", "# Is there a DBRX one?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Where is the number retrieved chunks we ask the embeddings search for configured?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Where is the number retrieved chunks we ask the embeddings search for configured?\n", "# ### Response:\n", "# The number of retrieved chunks to ask the embeddings search for is configured in the __init__ method of the DenseRetriever class in services/completion_host/single_model_server/dense_retriever.py. It is passed as the num_results parameter.\n", "# ### Instruction:\n", "# Where do we configure this class\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Find the feature flag defined by <PERSON><PERSON><PERSON> about low quality filtering.\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Find the feature flag defined by <PERSON><PERSON><PERSON> about low quality filtering.\n", "# ### Response:\n", "# The feature flag defined by <PERSON><PERSON><PERSON> about low quality filtering is _COMPLETION_LOW_QUALITY_THRESHOLD. It is a StringFlag with a default value of \"1.0\".\n", "# ### Instruction:\n", "# Nice, where do we set it?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\n", "# ### Response:\n", "# Yes, there is a utility to convert a ResearchEditPromptInput to a ModelInput.\n", "# You can use the convert_research_edit_prompt_input_to_model_input function from research/core/edit_prompt_input.py to achieve this conversion.\n", "# ### Instruction:\n", "# show me any example where it is used?\"\"\"\n", "\n", "question = \"\"\"### Instruction:\n", "Which prompt formatters are implement the chat in the `base/`?\"\"\"\n", "\n", "chunks, scores = retriever.query(ModelInput(question), top_k=10)\n", "\n", "print(question + '\\n')\n", "for idx, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print(f\"({idx}) -- {chunk.path}: {score} --\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# https://gist.github.com/urikz/a66727dd1facd57ccebbc2b2fb1b22a4\n", "# https://gist.github.com/urikz/48d0beebb82201f22ddb3c09ae568020\n", "# https://gist.github.com/urikz/2d228677322c660e3ed716530f737b36"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# chatanol1-11\n", "# https://gist.github.com/urikz/6ee140c6753878d156fb4cbc20aafb1f\n", "0.624074074074074\n", "\n", "# chatanol1-14\n", "# https://gist.github.com/urikz/018fdc7b441fb316f09b726442183c13\n", "0.7388888888888889\n", "\n", "# chatanol1-16-2\n", "# https://gist.github.com/urikz/08c462272d99cdb4a56c8bcdf6b3915d\n", "0.6907407407407408\n", "\n", "# chatanol1-16-2, but we prepend \"### Instruction:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.7\n", "\n", "# chatanol1-16-2, but we prepend \"### bla bla bla bla:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.8203703703703703"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# question = \"\"\"### Instruction:\n", "# Summarize all the different retrieval rerankers implemented in `research/retrieval/libraries/rerankers`\n", "# ### Instruction:\n", "# where do we use each of them?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Does fastforward model in research use all available GPUs?\"\"\"\n", "\n", "question = \"\"\"### Instruction:\n", "Where do we implement prompt formatter for the chat in the `base/`?\"\"\"\n", "\n", "# question = \"\"\"### Instruction:\n", "# Where do we implement DeepSeek model in `research/models/` with FastForward?\n", "# ### Instruction:\n", "# what if we need FastBackward backend?\"\"\"\n", "\n", "chunks, scores = retriever.query(ModelInput(question), top_k=10)\n", "\n", "print(question + '\\n')\n", "for idx, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print(f\"({idx}) -- {chunk.path}: {score} --\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# chatanol1-11\n", "# https://gist.github.com/urikz/6ee140c6753878d156fb4cbc20aafb1f\n", "0.624074074074074\n", "\n", "# chatanol1-14\n", "# https://gist.github.com/urikz/018fdc7b441fb316f09b726442183c13\n", "0.7388888888888889\n", "\n", "# chatanol1-16-2\n", "# https://gist.github.com/urikz/08c462272d99cdb4a56c8bcdf6b3915d\n", "0.6907407407407408\n", "\n", "# chatanol1-16-2, but we prepend \"### Instruction:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.7\n", "\n", "# chatanol1-16-2, but we prepend \"### bla bla bla bla:\\n\" before every question\n", "# https://gist.github.com/urikz/3ad8881a54b730b75c71a6bcf3b30b66\n", "0.8203703703703703"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
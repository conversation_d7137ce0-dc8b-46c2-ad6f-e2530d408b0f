import json
from pathlib import Path
from research.llm_apis.chat_utils import LLaMA31VertexAIChatClient


client = LLaMA31VertexAIChatClient()


INPUT = "/mnt/efs/spark-data/user/xiaolei/raw.txt"
OUTPUT = "/mnt/efs/spark-data/user/xiaolei/deduped.json"

INPUT_PATH = Path(INPUT)
OUTPUT_PATH = Path(OUTPUT)
BATCH_SIZE = 500
MAX_OUTPUT = 4096

PROMPT = """you will be given a list of categories of coding questions that can be aided by access to specific official documentation websites.    Please remove items that are similar to earlier entries.  Please also remove questions that are too specific and are not broad categories or falls into a subset of another question.

Please output one line per item, and return no explanations, formatting or any other details.  Do not escape new lines.

input categories:
"""


def process_batch(batch: list[str]):
    prompt = PROMPT + "\n".join(batch)
    answer = client.generate(messages=[prompt], max_tokens=MAX_OUTPUT)
    doc = {
        "questions": batch,
        "deduped": answer,
    }
    with open(OUTPUT, "a+") as f:
        f.write(json.dumps(doc) + "\n")


def main():
    known = set()
    if OUTPUT_PATH.exists():
        with OUTPUT_PATH.open() as f:
            for line in f:
                for item in json.loads(line)["questions"]:
                    known.add(item)

    batch = []
    with INPUT_PATH.open() as input_f:
        for line in input_f:
            line = line.strip()
            if line in known:
                continue
            known.add(line)
            batch.append(line)
            if len(batch) >= BATCH_SIZE:
                process_batch(batch)
                batch = []

    if batch:
        process_batch(batch)


if __name__ == "__main__":
    main()

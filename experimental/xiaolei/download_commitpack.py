from pathlib import Path

from datasets import load_dataset

langs = [
    "abap",
    "actionscript",
    "ada",
    "agda",
    "antlr",
    "apacheconf",
    "api-blueprint",
    "apl",
    "applescript",
    "arc",
    "arduino",
    "asciidoc",
    "asp",
    "aspectj",
    "assembly",
    "ats",
    "augeas",
    "autohotkey",
    "awk",
    "batchfile",
    "bitbake",
    "blitzmax",
    "bluespec",
    "boo",
    "brainfuck",
    "bro",
    "c",
    "c#",
    "c++",
    "c2hs-haskell",
    "capn-proto",
    "cartocss",
    "ceylon",
    "chapel",
    "clean",
    "clojure",
    "cmake",
    "coffeescript",
    "coldfusion",
    "coldfusion-cfc",
    "common-lisp",
    "creole",
    "crystal",
    "csound",
    "css",
    "csv",
    "cucumber",
    "cuda",
    "cython",
    "dart",
    "desktop",
    "diff",
    "digital-command-language",
    "dm",
    "dns-zone",
    "dockerfile",
    "dylan",
    "eagle",
    "ecl",
    "edn",
    "eiffel",
    "elixir",
    "elm",
    "emacs-lisp",
    "emberscript",
    "erlang",
    "f#",
    "factor",
    "fancy",
    "fish",
    "flux",
    "forth",
    "fortran",
    "freemarker",
    "g-code",
    "gas",
    "gdscript",
    "genshi",
    "gentoo-ebuild",
    "gettext-catalog",
    "glsl",
    "gnuplot",
    "go",
    "graphql",
    "graphviz-dot",
    "groff",
    "groovy",
    "groovy-server-pages",
    "haml",
    "handlebars",
    "harbour",
    "haskell",
    "haxe",
    "hcl",
    "hlsl",
    "html",
    "html+django",
    "html+eex",
    "html+erb",
    "html+php",
    "http",
    "hy",
    "idris",
    "igor-pro",
    "inform-7",
    "ini",
    "inno-setup",
    "io",
    "ioke",
    "isabelle",
    "jade",
    "jasmin",
    "java",
    "java-server-pages",
    "javascript",
    "jflex",
    "json",
    "json5",
    "jsoniq",
    "jsonld",
    "jsx",
    "julia",
    "jupyter-notebook",
    "kotlin",
    "krl",
    "latte",
    "lean",
    "less",
    "lfe",
    "lilypond",
    "linker-script",
    "liquid",
    "literate-agda",
    "literate-coffeescript",
    "literate-haskell",
    "livescript",
    "llvm",
    "logos",
    "logtalk",
    "lsl",
    "lua",
    "m4",
    "makefile",
    "mako",
    "maple",
    "markdown",
    "mask",
    "mathematica",
    "mediawiki",
    "metal",
    "mirah",
    "modelica",
    "module-management-system",
    "monkey",
    "moonscript",
    "mtml",
    "mupad",
    "nesc",
    "netlinx",
    "nginx",
    "nimrod",
    "ninja",
    "nit",
    "nix",
    "nsis",
    "nu",
    "objective-c++",
    "ocaml",
    "ooc",
    "opencl",
    "openscad",
    "org",
    "oz",
    "pan",
    "parrot-assembly",
    "parrot-internal-representation",
    "pascal",
    "pawn",
    "perl",
    "perl6",
    "php",
    "piglatin",
    "pike",
    "pod",
    "pony",
    "postscript",
    "pov-ray-sdl",
    "powershell",
    "processing",
    "propeller-spin",
    "protocol-buffer",
    "pure-data",
    "purebasic",
    "purescript",
    "python",
    "qmake",
    "qml",
    "r",
    "racket",
    "ragel-in-ruby-host",
    "raml",
    "rdoc",
    "rebol",
    "red",
    "renpy",
    "restructuredtext",
    "rhtml",
    "robotframework",
    "rouge",
    "ruby",
    "rust",
    "sage",
    "saltstack",
    "sas",
    "sass",
    "scala",
    "scaml",
    "scheme",
    "scilab",
    "scss",
    "shell",
    "slash",
    "slim",
    "smalltalk",
    "smarty",
    "smt",
    "solidity",
    "sourcepawn",
    "sparql",
    "sqf",
    "sql",
    "squirrel",
    "standard-ml",
    "stata",
    "ston",
    "stylus",
    "supercollider",
    "svg",
    "swift",
    "systemverilog",
    "tcl",
    "tcsh",
    "tex",
    "text",
    "textile",
    "thrift",
    "toml",
    "turtle",
    "twig",
    "typescript",
    "unity3d-asset",
    "unknown",
    "uno",
    "unrealscript",
    "urweb",
    "vala",
    "vcl",
    "vhdl",
    "viml",
    "visual-basic",
    "volt",
    "vue",
    "webidl",
    "wisp",
    "xbase",
    "xml",
    "xpages",
    "xproc",
    "xquery",
    "xs",
    "xslt",
    "xtend",
    "yacc",
    "yaml",
    "yang",
    "zephir",
    "zig",
]


for lang in langs:
    path = Path(".") / f"lang={lang}"
    path.mkdir()
    print(f"Downloading {lang} to {path}")
    dataset = load_dataset("bigcode/commitpackft", lang)
    dataset["train"].to_parquet(str(path / "train.parquet"))

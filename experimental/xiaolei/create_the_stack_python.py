# %% [markdown]
# # Experimental Python pretraining datasets
#
# A series of Python language sets to test pretraining on.  All are grouped by repo,
# tokenized and packed into 4k token binary docs.
#
# These are the sets
# - StarCoder
# - The Stack
# - Augment GH collection
# - Augment GH collection + The stack
#
# %%
"""<PERSON><PERSON><PERSON> to create the Stack python language subset bin/idx dataset."""
import typing

import pandas as pd
import pyspark.sql.functions as F
from pyspark.sql import DataFrame, SparkSession

from base.tokenizers.tiktoken_starcoder_tokenizer import (
    StarCoderSpecialTokens,
    TiktokenStarCoderTokenizer,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session
from research.data.train.common.index_dataset_utils import generate_indexed_dataset
from research.data.train.common.pack_into_samples import pack_into_samples
from research.data.train.common.pack_utils import pack_tokens
from research.data.train.common.sort_files_within_repo import (
    FileSortingStrategy,
    sort_files_within_repo,
)
from research.fim.pretraining_fim import PreTrainingFIM

# %%
SEQ_LENGTH = 4096
ADD_ONE_TOKEN = True
FILES_COLUMN = "files"
CONTENT_FIELD = "content"
PATH_FIELD = "path"
OUTPUT_COLUMN = "packed_samples"
FIM_RATE = 0.5
FIM_SPM_RATE = 0.0


# This takes a list of repos and creates packed samples
def create_packed_docs(
    spark_session: SparkSession,
    input_path: str,
    output_path: str,
    files_column=FILES_COLUMN,
    content_field=CONTENT_FIELD,
    path_field=PATH_FIELD,
    output_column=OUTPUT_COLUMN,
    has_special_tokens=False,
) -> dict[str, typing.Any]:
    token_length = SEQ_LENGTH + (1 if ADD_ONE_TOKEN else 0)

    def tokenize_and_pack(batch: pd.DataFrame) -> pd.DataFrame:
        tokenizer = TiktokenStarCoderTokenizer()
        fim_processor = PreTrainingFIM(
            fim_rate=FIM_RATE, fim_spm_rate=FIM_SPM_RATE, tokenizer=tokenizer
        )
        special_tokens = typing.cast(StarCoderSpecialTokens, tokenizer.special_tokens)
        tokens = []
        if has_special_tokens:
            tokenize = tokenizer.tokenize_unsafe
        else:
            tokenize = tokenizer.tokenize_safe
        for files in batch[files_column]:
            # Sort repo
            sorted_files = sort_files_within_repo(
                files,
                FileSortingStrategy.MAXIMIZE_FILENAME_MATCH,
                content_field,
                path_field,
            )
            # tokenize
            for file in sorted_files:
                doc_str = f"{file[path_field]}\n{file[content_field]}"
                doc = (
                    [special_tokens.filename] + tokenize(doc_str) + [special_tokens.eos]
                )
                tokens.append(doc)
        # pack samples
        samples = pack_into_samples(
            tokens, token_length, pad_token_id=special_tokens.fim_pad
        )
        # do fim
        fim_samples = fim_processor.apply_on_samples(samples)

        # pack tokens into binary for efficient storage
        packed_samples = [pack_tokens(sample.tolist()) for sample in fim_samples]

        return pd.DataFrame({output_column: packed_samples})

    result = map_parquet.apply_pandas(
        spark_session,
        tokenize_and_pack,
        input_path,
        output_path,
        batch_size=10,
        timeout=30 * 60,
        task_info_location="s3a://augment-ephemeral/python_pipeline/",
        ignore_error=True,
    )

    print("Task stats:", result["status_count"])
    return result


# %%
def group_by_repo(
    input_df: DataFrame,
    output_path: str,
    repo_name_col: str,
    content_col: str,
    filename_col: str = "",
    files_column: str = FILES_COLUMN,
    partitions=100,
):
    """Group the input dataset by repo."""
    struct_cols = [content_col]
    if filename_col:
        struct_cols.append(filename_col)
    output_df = input_df.groupBy(repo_name_col).agg(
        F.collect_list(F.struct(struct_cols)).alias(files_column),
    )
    if partitions is not None:
        output_df = output_df.repartition(partitions)
    output_df.write.mode("overwrite").parquet(output_path)


# %%
def main():
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "120G",
            "spark.executor.pyspark.memory": "100G",
        },
    )
    THESTACK_TEMP_PATH = (
        "s3a://python-training-experiments/processing/repo_grouped/the_stack/"
    )
    THESTACK_FINAL_PATH = (
        "s3a://python-training-experiments/processing/packed/the_stack/"
    )

    df = spark.read.parquet("s3a://the-stack-dedupe/langpart=python/").filter(
        (F.col("avg_line_length") <= 200)
        & (F.col("max_line_length") <= 1000)
        & (F.col("alphanum_fraction") >= 0.25)
        & (F.col("alphanum_fraction") < 0.90)
    )
    group_by_repo(
        df,
        output_path=THESTACK_TEMP_PATH,
        repo_name_col="max_stars_repo_name",
        filename_col="max_stars_repo_path",
        content_col="content",
        partitions=5000,
    )

    create_packed_docs(
        spark,
        THESTACK_TEMP_PATH,
        THESTACK_FINAL_PATH,
        has_special_tokens=False,
        path_field="max_stars_repo_path",
    )

    VOCAB_SIZE = TiktokenStarCoderTokenizer().vocab_size
    VALIDATION_COUNT = 10000

    generate_indexed_dataset(
        spark,
        THESTACK_FINAL_PATH,
        "python-training-experiments",
        "dataset/the_stack/",
        VOCAB_SIZE,
        VALIDATION_COUNT,
    )
    spark.stop()


# %%
if __name__ == "__main__":
    main()

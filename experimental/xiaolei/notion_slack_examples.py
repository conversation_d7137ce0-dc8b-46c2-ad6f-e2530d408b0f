from research.data.external_context.search_slack import SlackSearchClient
from research.data.external_context.search_notion import NotionSearchClient
from research.data.external_context.search_linear import LinearSearchClient

# search slack
# current drawback: we are returning a message, not the whole thread
# we can change this, if we know what to do
client = SlackSearchClient()
result = client.search_messages("spark logging", count=3)

for item in result:
    print(item.text)


# search notion.  current drawback: it searches title only
# nothing we can do about this, that's the only thing notion api provides.

client = NotionSearchClient()
results = client.search_title("monitor", max_results=3)
for item in results:
    print(item.to_markdown())


# Search linear
client = LinearSearchClient()
issues = client.search_issues("bug", max_results=5, include_comments=False)
for issue in issues:
    print(f"Found issue: {issue.id} - {issue.title}")

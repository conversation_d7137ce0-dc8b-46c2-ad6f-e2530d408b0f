import json
from research.llm_apis.chat_utils import LLaMA31VertexAIChatClient


client = LLaMA31VertexAIChatClient()


INPUT = "/mnt/efs/spark-data/user/xiaolei/agg_prompts.json"
OUTPUT = "/mnt/efs/spark-data/user/xiaolei/qa.json"

known = set()
with open(OUTPUT) as f:
    for line in f:
        known.add(json.loads(line)["question"])

with open(INPUT) as input_f:
    for line in input_f:
        prompt = json.loads(line)["question"]
        if prompt in known:
            print("Skipping question")
            continue
        answer = client.generate(messages=[prompt], max_tokens=1024)
        print(f"Q: {prompt[:100]}...")
        print(f"A: {answer[:100]}...")
        with open(OUTPUT, "a+") as f:
            f.write(json.dumps({"question": prompt, "answer": answer}) + "\n")
        known.add(prompt)

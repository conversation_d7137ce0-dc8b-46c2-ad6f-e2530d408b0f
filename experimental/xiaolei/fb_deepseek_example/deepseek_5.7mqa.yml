determined:
  name: "deepseek-5.7mqa-h100-4m"
  description: "deepseek 5.7mqa h100 4m tokens/step"
  workspace: Dev
  project: xiaolei

augment:
  podspec_path: "4xH100.yaml"
  gpu_count: 32
  keep_last_n_checkpoints: 100

fastbackward_configs:
 # Zero or more config files. Path relative to fastbackward base.
 - configs/deepseek_base_5.7mqa.py

fastbackward_args:
  block_size: 16384
  n_kv_heads: 1
  learning_rate: 1e-4
  min_lr: 1.0e-10
  decay_lr: True
  max_iters: 1001
  lr_decay_iters: 1001
  eval_interval: 100
  train_data_path: /mnt/efs/spark-data/user/xiaolei/tmp/16k_test_dataset/training
  eval_data_path: /mnt/efs/spark-data/user/xiaolei/tmp/16k_test_dataset/validation
  model_vocab_size: 32256
  batch_size: 2
  gradient_accumulation_steps: 4
  use_activation_checkpointing: True
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: xiaolei-sandbox
  run_name:  deepseek_5.7mqa_h100_4m
  hf_checkpoint_dir:  /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-5.7bmqa-base/
  # use_sequence_parallel: True
  # model_parallel_size: 2

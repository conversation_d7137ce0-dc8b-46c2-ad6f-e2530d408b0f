determined:
  name: "deepseek-1b-sanity-check"
  description: "deepseek 1b sanity check"
  workspace: Dev
  project: xiaolei

augment:
  podspec_path: "4xA100.yaml"
  gpu_count: 32
  keep_last_n_checkpoints: 100

fastbackward_configs:
 # Zero or more config files. Path relative to fastbackward base.
 - configs/deepseek_coder_instruct_1.3b.py

fastbackward_args:
  block_size: 16384
  n_kv_heads: 16
  learning_rate: 1e-5
  min_lr: 1.0e-6
  decay_lr: True
  max_iters: 101
  lr_decay_iters: 101
  eval_interval: 50
  train_data_path: /mnt/efs/spark-data/user/xiaolei/tmp/16k_test_dataset/training
  eval_data_path: /mnt/efs/spark-data/user/xiaolei/tmp/16k_test_dataset/validation
  model_vocab_size: 32256
  batch_size: 1
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: xiaolei-sandbox
  run_name:  deepseektest
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-base/

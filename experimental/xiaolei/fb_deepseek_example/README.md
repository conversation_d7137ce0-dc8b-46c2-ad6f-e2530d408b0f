To submit the job in fastbackward, go to
`$AUGMENTROOT/research/fastbackward` and then run

```bash
$ python determined/launch.py test_ds_1b_base_16k.yml
```
etc.


For evals, example run:
```bash

JOB_PATH="/mnt/efs/spark-data/user/xiaolei/determined_jobs/deepseek/base_5.7mqa/"

STEP=0

CHECKPOINT="2494ace8-4fb1-4c60-9ee1-f8c3765bf140"

python /home/<USER>/augment/research/eval/eval.py --v2 --summary="${JOB_PATH}/eval_llama_iteration_${STEP}.json" --model_name=fastbackward_deepseek --job_name_suffix=deepseek7b_${STEP} --checkpoint=$CHECKPOINT /home/<USER>/augment/research/eval/configs/pretrain/*.yml --podspec 1xA100
```
Note:  `STEP` is the step count in the training; it is just for bookkeeping purpose
`JOB_<PERSON>TH` specifies the folder to store the eval summary file
`CHECKPOINT` is the checkpoint name per Determined.  This should be an UUID

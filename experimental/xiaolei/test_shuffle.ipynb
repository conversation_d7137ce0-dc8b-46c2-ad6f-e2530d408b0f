{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/08/01 21:31:39 WARN Utils: Your hostname, xiaolei-dev resolves to a loopback address: *********; using ************** instead (on interface enp2s0)\n", "23/08/01 21:31:39 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Packaging folder /tmp/tmpmo_zsynj into /tmp/spark-uploads/packages-for-workers.zip\n"]}], "source": ["from research.data.spark import k8s_session\n", "\n", "\n", "spark = k8s_session(conf={'spark.speculation': 'false', 'spark.executor.memory': '50G', 'spark.executor.pyspark.memory': '0g'})"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/08/01 21:31:59 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}], "source": ["path = 's3a://starcoder/tokenized/'\n", "df = spark.read.option('basePath', path).parquet(path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>lang</th><th>file_id</th><th>max_stars_repo_name</th><th>max_stars_repo_path</th><th>max_stars_count</th><th>chunk_pos</th><th>chunk</th><th>num_chunks</th><th>tokenized</th></tr>\n", "<tr><td>html</td><td>338052</td><td>gw0/meetup16-dl-in-python</td><td>idiot_generator/slovenian-prose/bezanec.html</td><td>0</td><td>1</td><td>  &quot;<PERSON>ko, ne!&quot; zavpije Razh&amp;uacute;dnik, ki je imel blizu ceste zidano hišo.\\n&quot;Če si ti hodil t...</td><td>2</td><td>[50286, 1, 34153, 256, 25496, 11, 497, 2474, 1976, 615, 79, 2926, 68, 38058, 71, 5, 84, 330, 1133...</td></tr>\n", "<tr><td>cpp</td><td>5632217</td><td>vishnuk007/service-fabric</td><td>src/prod/test/FabricTest/TestHealthTable.cpp</td><td>2542</td><td>2</td><td>                                TestSession::WriteWarning(TraceSource, &quot;{0}: ud {1}: baseline.Try...</td><td>3</td><td>[50257, 6208, 36044, 3712, 16594, 20361, 7, 2898, 558, 7416, 11, 45144, 15, 38362, 334, 67, 1391,...</td></tr>\n", "<tr><td>php</td><td>3184673</td><td>DaveM2011/arangodb-zep</td><td>.temp/0.9.8-433d2cd249/_data_arangodb-zep_arangodbclient_traceresponse.zep.js.php</td><td>0</td><td>0</td><td>&lt;gh_stars&gt;0\\n&lt;?php return array (\\n  0 =&gt; \\n  array (\\n    &#x27;type&#x27; =&gt; &#x27;namespace&#x27;,\\n    &#x27;name&#x27; =&gt; ...</td><td>2</td><td>[50307, 15, 198, 47934, 10121, 1441, 7177, 357, 198, 50286, 15, 5218, 220, 198, 50286, 18747, 357...</td></tr>\n", "<tr><td>java</td><td>6967283</td><td>rohaniitr/home-auto</td><td>app/build/generated/source/r/debug/com/rohansarkar/helpex/R.java</td><td>5</td><td>3</td><td>        public static final int text=0x7f0c00b5;\\n        public static final int text2=0x7f0c00b...</td><td>10</td><td>[50280, 11377, 9037, 2457, 493, 2420, 28, 15, 87, 22, 69, 15, 66, 405, 65, 20, 26, 198, 50280, 11...</td></tr>\n", "<tr><td>c</td><td>5201477</td><td>sheridan-lambert/stutools</td><td>spit/jobType.c</td><td>11</td><td>1</td><td>      if (iR) {\\n        metaData = 1;\\n        threadContext[i].flushEvery = 1;\\n        uniqueS...</td><td>2</td><td>[50282, 361, 357, 72, 49, 8, 1391, 198, 50280, 28961, 6601, 796, 352, 26, 198, 50280, 16663, 2194...</td></tr>\n", "<tr><td>java</td><td>11182403</td><td>canuckotter/manifoldcf</td><td>framework/pull-agent/src/main/java/org/apache/manifoldcf/crawler/jobs/Jobs.java</td><td>0</td><td>1</td><td>    int status = stringToStatus(statusValue);\\n    // Any active state in the lifecycle will do: ...</td><td>3</td><td>[50284, 600, 3722, 796, 4731, 2514, 19580, 7, 13376, 11395, 1776, 198, 50284, 1003, 4377, 4075, 1...</td></tr>\n", "<tr><td>c</td><td>4111960</td><td>KermitProject/ckermit</td><td>ckuus4.c</td><td>1</td><td>8</td><td>\\t\\t    sep = &quot;\\011&quot;;\\t/* Tab */\\n\\t\\t    specialchar = *sep;\\n\\t\\t    grouping = 0;\\t/* No group...</td><td>11</td><td>[50294, 50284, 325, 79, 796, 37082, 28555, 8172, 197, 15211, 16904, 9466, 198, 50294, 50284, 2088...</td></tr>\n", "<tr><td>python</td><td>2468514</td><td>PassionateAngler/CDDA-Game-Launcher</td><td>cddagl/ui/views/backups.py</td><td>4</td><td>1</td><td>that file. You might need to end it if you want to retry.&lt;/p&gt;\\n&#x27;&#x27;&#x27;).format(image_file_name=proces...</td><td>2</td><td>[5562, 2393, 13, 921, 1244, 761, 284, 886, 340, 611, 345, 765, 284, 1005, 563, 25970, 79, 29, 198...</td></tr>\n", "<tr><td>markdown</td><td>13146145</td><td>ericphanson/ConvexTests</td><td>docs/src/Hypatia.md</td><td>8</td><td>1</td><td>  CPU: Intel(R) Xeon(R) CPU E5-2673 v4 @ 2.30GHz\\n  WORD_SIZE: 64\\n  LIBM: libopenlibm\\n  LLVM: l...</td><td>2</td><td>[50286, 36037, 25, 8180, 7, 49, 8, 45266, 7, 49, 8, 9135, 412, 20, 12, 2075, 4790, 410, 19, 2488,...</td></tr>\n", "<tr><td>html</td><td>2347381</td><td>doulos-software/2nd_Temple</td><td>josephus/war-4.html</td><td>0</td><td>0</td><td>&lt;gh_stars&gt;0\\n&lt;HTML&gt;\\n\\n\\n&lt;!-- Mirrored from www.pseudepigrapha.com/josephus/war-4.html by HTTrack...</td><td>4</td><td>[50307, 15, 198, 27, 28656, 29, 628, 198, 27, 28112, 7381, 34640, 422, 7324, 13, 7752, 463, 538, ...</td></tr>\n", "<tr><td>typescript</td><td>9229507</td><td>pavelvlasov/google-api-nodejs-client</td><td>src/apis/certificatemanager/v1.ts</td><td>0</td><td>3</td><td>     */\\n    name?: string;\\n  }\\n  export interface Params$Resource$Projects$Locations$Dnsauthor...</td><td>4</td><td>[50283, 16208, 198, 50284, 3672, 27514, 4731, 26, 198, 50286, 92, 198, 50286, 39344, 7071, 2547, ...</td></tr>\n", "<tr><td>c</td><td>5551960</td><td>pgcrism/gobo</td><td>work/bootstrap/gec3.c</td><td>1</td><td>6</td><td>\\t\\t}\\n\\t}\\n}\\n\\n/* ET_C_GENERATOR.print_builtin_sized_real_to_double_call */\\nvoid T67f820(T0* C...</td><td>21</td><td>[50294, 92, 198, 197, 92, 198, 92, 198, 198, 15211, 12152, 62, 34, 62, 35353, 1137, 25633, 13, 47...</td></tr>\n", "<tr><td>java</td><td>12216583</td><td>leusonmario/2022PhDThesis</td><td>HikariCP/1bca94af9ec625f21d1b58ff10efb5be71ab87a6/randoop_1/RegressionTest25.java</td><td>0</td><td>9</td><td>        hikariConfig0.setCatalog(&quot;HikariPool-12593&quot;);\\n        hikariConfig0.setLeakDetectionThre...</td><td>18</td><td>[50280, 71, 1134, 2743, 16934, 15, 13, 2617, 49015, 7203, 39, 1134, 2743, 27201, 12, 1065, 49051,...</td></tr>\n", "<tr><td>c-sharp</td><td>10317175</td><td>merulaxr/open-brush</td><td>Assets/Plugins/CSharp/DebugDraw.cs</td><td>321</td><td>1</td><td>\\n        Gizmos.color = oldColor;\\n    }\\n\\n    /// &lt;summary&gt;\\n    /// \\t- Draws a point.\\n    /...</td><td>2</td><td>[198, 50280, 38, 528, 16785, 13, 8043, 796, 1468, 10258, 26, 198, 50284, 92, 628, 50284, 20379, 1...</td></tr>\n", "<tr><td>c-sharp</td><td>1496914</td><td>rsdn/CodeJam</td><td>CodeJam.Main/Structures/OneOf/ValueOneOf.generated.cs</td><td>252</td><td>1</td><td>\\t\\tpublic static ValueOneOf&lt;T1, T2, T3, T4, T5, T6, T7, T8&gt; Create(T4 value)\\r\\n\\t\\t{\\r\\n\\t\\t\\tC...</td><td>2</td><td>[50294, 11377, 9037, 11052, 3198, 5189, 27, 51, 16, 11, 309, 17, 11, 309, 18, 11, 309, 19, 11, 30...</td></tr>\n", "<tr><td>html</td><td>1531412</td><td>ArrButterfly/jeecg-boot-parent</td><td>logs/jeecgboot-2022-03-18.0.html</td><td>0</td><td>17</td><td>&lt;td class=&quot;Message&quot;&gt;Connection failure occurred. Restarting subscription task after 5000 ms&lt;/td&gt;\\...</td><td>20</td><td>[27, 8671, 1398, 2625, 12837, 5320, 32048, 5287, 5091, 13, 8324, 433, 278, 14569, 4876, 706, 2333...</td></tr>\n", "<tr><td>php</td><td>8230429</td><td>odouglasrocha/projeto-laravel-marcketplace</td><td>resources/views/frontend/index.blade.php</td><td>0</td><td>2</td><td>                                                &lt;/figure&gt;\\n                                      ...</td><td>3</td><td>[50257, 220, 50272, 3556, 26875, 29, 198, 50257, 220, 50272, 27, 7146, 1398, 2625, 11167, 12, 366...</td></tr>\n", "<tr><td>html</td><td>3041523</td><td>intaset/ijmem</td><td>_site/2012/006.html</td><td>0</td><td>0</td><td>﻿&lt;!DOCTYPE html&gt;\\n&lt;html lang=&quot;en&quot;&gt;\\n&lt;head&gt;\\n&lt;meta http-equiv=&quot;Content-Type&quot; content=&quot;text/html; c...</td><td>2</td><td>[171, 119, 123, 27, 0, 18227, 4177, 56, 11401, 27711, 29, 198, 27, 6494, 42392, 2625, 268, 5320, ...</td></tr>\n", "<tr><td>c</td><td>857242</td><td>kudorado/HaxePlus</td><td>extension-admob/1,6,4/project/include/AVFoundation/AVPlayer.h</td><td>416</td><td>0</td><td>&lt;reponame&gt;kudorado/HaxePlus\\n#if !__has_include(&lt;AVFCore/AVPlayer.h&gt;)\\n/*\\n    File:  AVPlayer.h\\...</td><td>2</td><td>[50309, 74, 463, 273, 4533, 14, 39, 38231, 17860, 198, 2, 361, 5145, 834, 10134, 62, 17256, 7, 27...</td></tr>\n", "<tr><td>typescript</td><td>3474544</td><td>wz2cool/ts-dynamic-query-demo</td><td>src/data/users3.ts</td><td>0</td><td>8</td><td>  },\\n  {\\n    id: 2760,\\n    avatar: &quot;https://s3.amazonaws.com/uifaces/faces/twitter/vitor376/12...</td><td>11</td><td>[50286, 5512, 198, 50286, 90, 198, 50284, 312, 25, 2681, 1899, 11, 198, 50284, 615, 9459, 25, 366...</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+----------+--------+------------------------------------------+---------------------------------------------------------------------------------+---------------+---------+----------------------------------------------------------------------------------------------------+----------+----------------------------------------------------------------------------------------------------+\n", "|      lang| file_id|                       max_stars_repo_name|                                                              max_stars_repo_path|max_stars_count|chunk_pos|                                                                                               chunk|num_chunks|                                                                                           tokenized|\n", "+----------+--------+------------------------------------------+---------------------------------------------------------------------------------+---------------+---------+----------------------------------------------------------------------------------------------------+----------+----------------------------------------------------------------------------------------------------+\n", "|      html|  338052|                 gw0/meetup16-dl-in-python|                                     idiot_generator/slovenian-prose/bezanec.html|              0|        1|  \"Ni tako, ne!\" zavpije Razh&uacute;dnik, ki je imel blizu ceste zidano hišo.\\n\"Če si ti hodil t...|         2|[50286, 1, 34153, 256, 25496, 11, 497, 2474, 1976, 615, 79, 2926, 68, 38058, 71, 5, 84, 330, 1133...|\n", "|       cpp| 5632217|                 vishnuk007/service-fabric|                                     src/prod/test/FabricTest/TestHealthTable.cpp|           2542|        2|                                TestSession::WriteWarning(TraceSource, \"{0}: ud {1}: baseline.Try...|         3|[50257, 6208, 36044, 3712, 16594, 20361, 7, 2898, 558, 7416, 11, 45144, 15, 38362, 334, 67, 1391,...|\n", "|       php| 3184673|                    DaveM2011/arangodb-zep|.temp/0.9.8-433d2cd249/_data_arangodb-zep_arangodbclient_traceresponse.zep.js.php|              0|        0|<gh_stars>0\\n<?php return array (\\n  0 => \\n  array (\\n    'type' => 'namespace',\\n    'name' => ...|         2|[50307, 15, 198, 47934, 10121, 1441, 7177, 357, 198, 50286, 15, 5218, 220, 198, 50286, 18747, 357...|\n", "|      java| 6967283|                       rohaniitr/home-auto|                 app/build/generated/source/r/debug/com/rohansarkar/helpex/R.java|              5|        3|        public static final int text=0x7f0c00b5;\\n        public static final int text2=0x7f0c00b...|        10|[50280, 11377, 9037, 2457, 493, 2420, 28, 15, 87, 22, 69, 15, 66, 405, 65, 20, 26, 198, 50280, 11...|\n", "|         c| 5201477|                 sheridan-lambert/stutools|                                                                   spit/jobType.c|             11|        1|      if (iR) {\\n        metaData = 1;\\n        threadContext[i].flushEvery = 1;\\n        uniqueS...|         2|[50282, 361, 357, 72, 49, 8, 1391, 198, 50280, 28961, 6601, 796, 352, 26, 198, 50280, 16663, 2194...|\n", "|      java|11182403|                    canuckotter/manifoldcf|  framework/pull-agent/src/main/java/org/apache/manifoldcf/crawler/jobs/Jobs.java|              0|        1|    int status = stringToStatus(statusValue);\\n    // Any active state in the lifecycle will do: ...|         3|[50284, 600, 3722, 796, 4731, 2514, 19580, 7, 13376, 11395, 1776, 198, 50284, 1003, 4377, 4075, 1...|\n", "|         c| 4111960|                     KermitProject/ckermit|                                                                         ckuus4.c|              1|        8|\\t\\t    sep = \"\\011\";\\t/* Tab */\\n\\t\\t    specialchar = *sep;\\n\\t\\t    grouping = 0;\\t/* No group...|        11|[50294, 50284, 325, 79, 796, 37082, 28555, 8172, 197, 15211, 16904, 9466, 198, 50294, 50284, 2088...|\n", "|    python| 2468514|       PassionateAngler/CDDA-Game-Launcher|                                                       cddagl/ui/views/backups.py|              4|        1|that file. You might need to end it if you want to retry.</p>\\n''').format(image_file_name=proces...|         2|[5562, 2393, 13, 921, 1244, 761, 284, 886, 340, 611, 345, 765, 284, 1005, 563, 25970, 79, 29, 198...|\n", "|  markdown|13146145|                   ericphanson/ConvexTests|                                                              docs/src/Hypatia.md|              8|        1|  CPU: Intel(R) Xeon(R) CPU E5-2673 v4 @ 2.30GHz\\n  WORD_SIZE: 64\\n  LIBM: libopenlibm\\n  LLVM: l...|         2|[50286, 36037, 25, 8180, 7, 49, 8, 45266, 7, 49, 8, 9135, 412, 20, 12, 2075, 4790, 410, 19, 2488,...|\n", "|      html| 2347381|                doulos-software/2nd_Temple|                                                              josephus/war-4.html|              0|        0|<gh_stars>0\\n<HTML>\\n\\n\\n<!-- Mirrored from www.pseudepigrapha.com/josephus/war-4.html by HTTrack...|         4|[50307, 15, 198, 27, 28656, 29, 628, 198, 27, 28112, 7381, 34640, 422, 7324, 13, 7752, 463, 538, ...|\n", "|typescript| 9229507|      pavelv<PERSON>ov/google-api-nodejs-client|                                                src/apis/certificatemanager/v1.ts|              0|        3|     */\\n    name?: string;\\n  }\\n  export interface Params$Resource$Projects$Locations$Dnsauthor...|         4|[50283, 16208, 198, 50284, 3672, 27514, 4731, 26, 198, 50286, 92, 198, 50286, 39344, 7071, 2547, ...|\n", "|         c| 5551960|                              pgcrism/gobo|                                                            work/bootstrap/gec3.c|              1|        6|\\t\\t}\\n\\t}\\n}\\n\\n/* ET_C_GENERATOR.print_builtin_sized_real_to_double_call */\\nvoid T67f820(T0* C...|        21|[50294, 92, 198, 197, 92, 198, 92, 198, 198, 15211, 12152, 62, 34, 62, 35353, 1137, 25633, 13, 47...|\n", "|      java|12216583|                 leusonmario/2022PhDThesis|HikariCP/1bca94af9ec625f21d1b58ff10efb5be71ab87a6/randoop_1/RegressionTest25.java|              0|        9|        hikariConfig0.setCatalog(\"HikariPool-12593\");\\n        hikariConfig0.setLeakDetectionThre...|        18|[50280, 71, 1134, 2743, 16934, 15, 13, 2617, 49015, 7203, 39, 1134, 2743, 27201, 12, 1065, 49051,...|\n", "|   c-sharp|10317175|                       merulaxr/open-brush|                                               Assets/Plugins/CSharp/DebugDraw.cs|            321|        1|\\n        Gizmos.color = oldColor;\\n    }\\n\\n    /// <summary>\\n    /// \\t- Draws a point.\\n    /...|         2|[198, 50280, 38, 528, 16785, 13, 8043, 796, 1468, 10258, 26, 198, 50284, 92, 628, 50284, 20379, 1...|\n", "|   c-sharp| 1496914|                              rsdn/CodeJam|                            CodeJam.Main/Structures/OneOf/ValueOneOf.generated.cs|            252|        1|\\t\\tpublic static ValueOneOf<T1, T2, T3, T4, T5, T6, T7, T8> Create(T4 value)\\r\\n\\t\\t{\\r\\n\\t\\t\\tC...|         2|[50294, 11377, 9037, 11052, 3198, 5189, 27, 51, 16, 11, 309, 17, 11, 309, 18, 11, 309, 19, 11, 30...|\n", "|      html| 1531412|            ArrButterfly/jeecg-boot-parent|                                                 logs/jeecgboot-2022-03-18.0.html|              0|       17|<td class=\"Message\">Connection failure occurred. Restarting subscription task after 5000 ms</td>\\...|        20|[27, 8671, 1398, 2625, 12837, 5320, 32048, 5287, 5091, 13, 8324, 433, 278, 14569, 4876, 706, 2333...|\n", "|       php| 8230429|odouglasrocha/projeto-laravel-marcketplace|                                         resources/views/frontend/index.blade.php|              0|        2|                                                </figure>\\n                                      ...|         3|[50257, 220, 50272, 3556, 26875, 29, 198, 50257, 220, 50272, 27, 7146, 1398, 2625, 11167, 12, 366...|\n", "|      html| 3041523|                             intaset/ijmem|                                                              _site/2012/006.html|              0|        0|﻿<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n<meta http-equiv=\"Content-Type\" content=\"text/html; c...|         2|[171, 119, 123, 27, 0, 18227, 4177, 56, 11401, 27711, 29, 198, 27, 6494, 42392, 2625, 268, 5320, ...|\n", "|         c|  857242|                         kudorado/HaxePlus|                    extension-admob/1,6,4/project/include/AVFoundation/AVPlayer.h|            416|        0|<reponame>kudorado/HaxePlus\\n#if !__has_include(<AVFCore/AVPlayer.h>)\\n/*\\n    File:  AVPlayer.h\\...|         2|[50309, 74, 463, 273, 4533, 14, 39, 38231, 17860, 198, 2, 361, 5145, 834, 10134, 62, 17256, 7, 27...|\n", "|typescript| 3474544|             wz2cool/ts-dynamic-query-demo|                                                               src/data/users3.ts|              0|        8|  },\\n  {\\n    id: 2760,\\n    avatar: \"https://s3.amazonaws.com/uifaces/faces/twitter/vitor376/12...|        11|[50286, 5512, 198, 50286, 90, 198, 50284, 312, 25, 2681, 1899, 11, 198, 50284, 615, 9459, 25, 366...|\n", "+----------+--------+------------------------------------------+---------------------------------------------------------------------------------+---------------+---------+----------------------------------------------------------------------------------------------------+----------+----------------------------------------------------------------------------------------------------+\n", "only showing top 20 rows"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["df.repartition(5000, 'file_id').write.parquet('s3a://augment-temporary/starcoder/tokenized-repo-range/')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
OllieTaboogie | 2023-03-24 23:36:16 UTC | #1

Is it possible to render non-HTML templates correctly?  So, with little modification to some original legacy code, render, from Django, an ASP file (for example)?

A little background: I'm working on a case study evaluating migrating a large, feature heavy website from a blend of PHP and ASP Classic to Python (Django in this case, obviously) and am hoping to slowly convert the legacy site in modules. I was hoping with some minimal code changes to PHP files or ASP files I could render them via Django.  It seems the way I'm going about it now, I can render a slightly modified ASP file, for example, that I have defined as a template but Django appears to treat it as HTML despite its extension.  So, the HTML portions render correctly as HTML but the ASP portions render strictly as text.

-------------------------

<PERSON><PERSON><PERSON><PERSON><PERSON> | 2022-03-02 20:29:38 UTC | #2

I guess it really depends upon the degree of integration you wish to achieve.

If the integration only exists at the "site" level, meaning that some URLs are processed and rendered in Django and some are processed and rendered by different languages, you can do that at the web server layer. Segregate your URL structure by language and dispatch the appropriate URL to the corresponding language processor.

If you're looking at integration within a page, you could have your Django view run the PHP interpreter, and render the returned HTML within your template.

There are other approaches possible, but I'm not sure how much value they would be for the given effort. (For example, you could create a template engine for PHP to more-or-less render the php directly.)

-------------------------

OllieTaboogie | 2022-03-03 12:47:38 UTC | #3

Thanks for the reply!  I was actually sold this wouldn't be possible so you are giving me a bit of hope for a Django solution again.  I'll be a little more detailed this time ...

I have a site in which the user is logged in and given access to various things.  For instance, maybe an asp page that allows the logged in individual to search a database or a PHP report page that generates a defined report and displays it.  My hope is to start with the gate to the site (login) and then convert, page by page, things over.  If this works, I know I'd have to write some minimal code in every page we want to work out the gate.  Does Django support an approach like this?

To be abundantly clear:
1) Create a new login page and permission system within Django.  The user, upon login, is served a legacy page.

2) For this example lets pick asp.  A bootstrap laden asp menu page is rendered with different selectable options based on permissions.  Eventually this page will be converted but for this phase we are rendering slightly modified legacy code:

*urls.py*

```
urlpatterns = [
    ...
    path('main_user_dashboard', views.main_user_dashboard, name="main_user_dashboard"),
]
```

*views.py*

```
def main_user_dashboard(request):
    if request.user.is_authenticated:
        return render(request, 'myTemplateFolder/myAspMenu.asp')
    else:
        ...
```

*myAspMenu.asp template*

```
<%
   Some ASP code
%>
<h2>Welcome {{ user.get_full_name }}!</h2>
<p>Some HTML</p>
<%
   Some ASP code
%>
<div class="mb-md-5 mt-md-4 pb-5">
   <form action="{% url 'perform_logout' %}" method="POST">
     {% csrf_token %}
     <button class="btn btn-outline-light btn-lg px-5" type="submit">Logout</button>
   </form>
 </div>
```

-------------------------

KenWhitesell | 2022-03-04 03:01:34 UTC | #4

Taking this approach is going to create issues that you will need to address with the passing of credentials between environments.

Addressing _that_ is going to require some detailed understanding of how authentication is handled in those applications as well as knowing how to transfer that information between those environments.

And **that** takes it out of the category of something that can realistically be addressed here in any degree of detail.

-------------------------

OllieTaboogie | 2022-03-15 12:52:50 UTC | #5

That is what I was thinking as well.  Thank you for your time Ken.

-------------------------

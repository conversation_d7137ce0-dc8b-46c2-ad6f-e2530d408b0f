import json
import time
from pathlib import Path

import requests


class WordPressScraper:
    def __init__(
        self, base_url, output_dir: Path, batch_size=100, batch_file_size=1000
    ):
        self.base_url = base_url
        self.output_dir = output_dir
        self.output_dir.mkdir(exist_ok=True)
        self.batch_size = batch_size  # Number of posts per API request
        self.batch_file_size = batch_file_size  # Number of posts per JSON file

    def fetch_posts(self):
        current_page = 1
        total_pages = None
        post_count = 0
        all_posts = []

        while True:
            response = self.get_response(current_page)
            if response is None:
                break  # Stop if there are any issues in getting a valid response

            if total_pages is None:
                total_pages = response["totalPages"]

            posts = response["data"]
            if not posts:
                break

            post_count += len(posts)
            all_posts.extend(posts)

            # Save in batches of self.batch_file_size
            if len(all_posts) >= self.batch_file_size:
                self.save_posts(all_posts, current_page)
                all_posts = []  # Reset list after saving

            print(
                f"Fetched page {current_page}/{total_pages}. Total posts fetched: {post_count}"
            )

            if current_page >= total_pages:
                break
            current_page += 1
            time.sleep(1)  # Simple rate limiting

        if all_posts:
            self.save_posts(all_posts, current_page)  # Save any remaining posts

    def get_response(self, page):
        try:
            response = requests.get(
                f"{self.base_url}/wp-json/wp/v2/posts?page={page}&per_page={self.batch_size}"
            )
            response.raise_for_status()
            return {
                "totalPages": int(response.headers["X-WP-TotalPages"]),
                "data": response.json(),
            }
        except requests.RequestException as e:
            print(f"Error fetching page {page}: {e}")
            return None

    def save_posts(self, posts, page_number):
        batch_num = (page_number - 1) // (self.batch_file_size // self.batch_size) + 1
        file_path = self.output_dir / f"posts_batch_{batch_num}.json"
        with file_path.open("w") as file:
            json.dump(posts, file, indent=4)

    def resume_from_last(self):
        # Check the last completed batch
        completed_files = sorted(self.output_dir.glob("posts_batch_*.json"))
        if completed_files:
            last_file = completed_files[-1]
            last_batch_num = int(last_file.stem.split("_")[-1])
            start_page = (last_batch_num - 1) * (
                self.batch_file_size // self.batch_size
            ) + 1
            return start_page
        return 1


if __name__ == "__main__":
    URL = "https://example.com"  # Set your WordPress site URL here
    scraper = WordPressScraper(URL, Path("wordpress_data"))
    start_page = scraper.resume_from_last()
    print(f"Resuming from page {start_page}")
    scraper.fetch_posts()

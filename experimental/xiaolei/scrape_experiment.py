"""A test run that scrapes readme files."""
import json
import traceback as tb
from pathlib import Path

from scrape_readme import ROOT, Node

root_url = f"https://github.com/{ROOT}"


def save():
    with Path("scrape_results.jsonl").open("w+") as fout:
        for doc in Node.serialize_graph():
            fout.write(json.dumps(doc) + "\n")


if __name__ == "__main__":
    try:
        with Path("scrape_results.jsonl").open() as f:
            Node.deserialize_graph([json.loads(line) for line in f])
    except Exception as e:  # pylint: disable=broad-exception-caught
        print(e)
        tb.print_exc()
        print("Failed to load graph.  Starting from scratch.")
        root = Node(root_url)
        Node.crawl_one()

    for i in range(50000):
        if Node.crawl_one() is None:
            print("We are done here")
            break
        if i % 25 == 0:
            print(
                f"Saving graph at step {i}.  Current graph size: {Node.graph_size()}.",
                flush=True,
            )
            save()

    save()
    print("Stopping...")

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "from pathlib import Path\n", "\n", "questions_dict = json.load(\n", "    open(\"/home/<USER>/augment/experimental/xiaolei/question_groups.json\")\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["DOCSETS = 20\n", "CHUNKS_PER_QUESTION = 50\n", "QUESTIONS_PER_DOCSET = 5\n", "DOCSET_ROOT = Path(\"/mnt/efs/spark-data/shared/nl-datasets/devdocs/json_docs_20240923\")\n", "DOCSET_COLLECTION = DOCSET_ROOT / \"docset_collection_index.json\"\n", "\n", "AVAILABLE_DOCSETS = set(\n", "    [\n", "        entry[\"name\"]\n", "        for entry in json.load(\n", "            open(\n", "                \"/home/<USER>/augment/services/integrations/docset/server/docsets_dump.json\"\n", "            )\n", "        )\n", "    ]\n", ")\n", "\n", "docsets = json.load((DOCSET_ROOT / \"docset_collection_index.json\").open())\n", "projects = {}\n", "# get the latest version of each project\n", "for docset in docsets:\n", "    docset_name = docset[\"docset_name\"]\n", "    if docset_name not in AVAILABLE_DOCSETS:\n", "        continue\n", "    project = docset_name.split(\"~\")[0]\n", "    if (\n", "        project in projects\n", "        and projects[project][\"docset_name\"] >= docset[\"docset_name\"]\n", "    ):\n", "        continue\n", "    projects[project] = docset\n", "\n", "# sample them\n", "docset_sample = {}\n", "while len(docset_sample) < DOCSETS:\n", "    project = random.choice(list(projects))\n", "    if project in docset_sample:\n", "        continue\n", "    docset_sample[project] = projects[project]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["PROMPT_TEMPLATE = \"\"\"You are creating a collection of learning materials to teach engineers how to use documentations to answer questions.\n", "You will be give a list of documentation snippets from {docset_name} documentation.\n", "Please write a list of {n_questions} specific and detailed question that can be answered with the information in these snippets, but is very hard to answer without them.\n", "\n", "Do not give hints or instructions about how to answer the question.   Do NOT ask more than one questions.   Do not make direct reference to the documentation itself or any specific content therein.   Only return questions that you will be unable to answer without seeing the documentation snippets, and can answer with the information inside.\n", "\n", "Here is a list of the kind of questions you might ask:\n", "\n", "{questions}\n", "\n", "Here is the list of documentation chunks from several different pages:\n", "{chunks}\n", "\n", "Your return should be a JSON list of {n_questions} objects, each containing the following fields.  All items should be concise but clear, and do not contain additional contents, explanations, or follow up questions / discussions.\n", "\n", "- docset_name: string.  The name of the docset.  It should be `{docset_name}`.\n", "- note:  any additional note if absolutely necessary.   leave as empty string if not needed.\n", "- code_snippet: string.  An optional code snippet that is the context for the question.   Empty if the question does not need code context.\n", "- question: a string containing ONE SINGLE concise but clear question.   It should not directly reference or quote the docs.\n", "- relevant_doc_chunks:  A list of strings.  verbatim quotes of documentation chunks that are required to answer this question correctly.  Please always include the ENTIRE QUOTED BLOCK and nothing else.  No filenames or descriptions.\n", "- reference_answer:  The correct answer to the question.  It must be concise and does not directly quote the snippets themselves, but it must correct utilize the information given in the documentation snippets.\n", "\n", "Make sure questions cannot be answered without seeing the documentation snippets.   Make sure some questions are difficult, some require multiple chunks to correctly answer, and some questions are regarding specific code examples or error messages.\n", "These should be general developer questions that can be helped by the documentation, not quiz about the content of the documentation itself.\n", "Note that code_snippet is part of the question, not part of the answer. Do not use verbatim content of the documentation in your question or code_snippet; instead paraphrase or create a similar example instead.\n", "You should create working code snippets that may come up in development work insteading of directly copying from the documentation.\n", "Again if all entries have empty code snippets or only used one relevant chunk, you have done something wrong!\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Working on  bun~1.1.21\n", "There are 1370 chunks in this docset.\n", "Working on  bootstrap_vue\n", "There are 316 chunks in this docset.\n", "Working on  react_native~0.61.0\n", "There are 560 chunks in this docset.\n", "Working on  mongodb~7.0.9\n", "There are 61421 chunks in this docset.\n", "Working on  bootstrap~5\n", "There are 726 chunks in this docset.\n", "Working on  actix~4.8.0\n", "There are 4336 chunks in this docset.\n", "Working on  semantic_ui~2.5.0\n", "There are 342 chunks in this docset.\n", "Working on  node~18_lts\n", "There are 2275 chunks in this docset.\n", "Working on  homebrew\n", "There are 296 chunks in this docset.\n", "Working on  django_rest_framework~3.9.1\n", "There are 362 chunks in this docset.\n", "Working on  git~2\n", "There are 3479 chunks in this docset.\n", "Working on  memcached\n", "There are 3201 chunks in this docset.\n", "Working on  terraform~1.8.3.240519\n", "There are 45501 chunks in this docset.\n", "Working on  rxswift~6.7.1\n", "There are 479 chunks in this docset.\n", "Working on  symfony~4.1\n", "There are 6091 chunks in this docset.\n", "Working on  laravel~9\n", "There are 8068 chunks in this docset.\n", "Working on  llvm~12.0.0\n", "There are 1188 chunks in this docset.\n", "Working on  mariadb\n", "There are 8843 chunks in this docset.\n", "Working on  openai_cookbook~20240909\n", "There are 2376 chunks in this docset.\n", "Working on  ruby~3.3\n", "There are 6901 chunks in this docset.\n"]}], "source": ["with open(\"/mnt/efs/augment/user/xiaolei/docset_eval_prompts.jsonl\", \"w+\") as f:\n", "    for docset in docset_sample.values():\n", "        print(\"Working on \", docset[\"docset_name\"])\n", "\n", "        # Randomly sample the docset\n", "        docset_version: str = docset[\"docset_version\"]\n", "        docset_path = DOCSET_ROOT / docset_version\n", "        index = json.load((docset_path / \"index.json\").open())\n", "        chunks = []\n", "        for blob in index[\"files\"]:\n", "            blob_path: str = blob[\"name\"]\n", "            for line in (docset_path / blob_path).open():\n", "                chunks.append(json.loads(line))\n", "        nchunks = len(chunks)\n", "        print(f\"There are {nchunks} chunks in this docset.\")\n", "\n", "        # randomly sample one question from each category\n", "        sampled_questions = [random.choice(qs) for qs in questions_dict.values()]\n", "        questions_str = \"\\n\".join(sampled_questions)\n", "\n", "        samples = random.sample(chunks, min(nchunks, CHUNKS_PER_QUESTION))\n", "        snippets = \"\"\n", "        for item in samples:\n", "            snippets += f\"here is a chunk from {item['path']}:\\n```\\n\"\n", "            for header in item[\"headers\"]:\n", "                snippets += header + \"\\n\"\n", "            snippets += item[\"text\"] + \"\\n```\\n\"\n", "\n", "        prompt = PROMPT_TEMPLATE.format(\n", "            questions=questions_str,\n", "            chunks=snippets,\n", "            docset_name=docset[\"docset_name\"],\n", "            n_questions=QUESTIONS_PER_DOCSET,\n", "        )\n", "        f.write(\n", "            json.dumps({\"docset_name\": docset[\"docset_name\"], \"question\": prompt})\n", "            + \"\\n\"\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
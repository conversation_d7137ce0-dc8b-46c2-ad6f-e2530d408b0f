{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains stages for generating retrieval-augmented dataset for fine-tuning.\"\"\"\n", "import json\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, Mapping, Sequence, Iterable\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "from research.retrieval.types import Chunk, Document\n", "from research.static_analysis.common import guess_lang_from_fp\n", "from research.fim.fim_prompt import _format_middle\n", "from research.fim.fim_sampling import CSTFimSampler, FimProblem\n", "from research.static_analysis.usage_analysis import ParsedFile"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"hexsha\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "\n", "ROWS_PER_INDEXED_DATASET = 4096\n", "\n", "\n", "# TODO(michiel) add correctness test\n", "def _file_to_samples(\n", "    file_content: str,\n", "    file_id: str,\n", "    file_path: str,\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", ") -> list[FimProblem]:\n", "    \"\"\"Convert each file into samples.\"\"\"\n", "    every_n_lines = config.every_n_lines\n", "    max_problems_per_file = config.max_problems_per_file\n", "\n", "    seed = int.from_bytes(file_id.encode(), \"little\") + config.random_seed\n", "    sampler.rng.seed(seed)\n", "\n", "    try:\n", "        lang = guess_lang_from_fp(file_path)\n", "        pfile = ParsedFile.parse(path=file_path, lang=lang, code=file_content)\n", "        samples, _ = sampler.sample_every_n_lines(\n", "            pfile,\n", "            every_n_lines=every_n_lines,\n", "            max_problems_per_file=max_problems_per_file,\n", "        )\n", "        # TOD<PERSON>(mi<PERSON><PERSON>) fix insanity.\n", "        # <PERSON><PERSON> can return empty prefix. Prompt formatters don't like this, so we filter them out for now.\n", "        samples = [sample for sample in samples if sample.prefix]\n", "        return samples\n", "    except Exception:\n", "        logging.error(f\"[{file_path}] Failed.\", exc_info=True)\n", "        return []\n", "\n", "\n", "def serialize_retrieved_chunks(retrieved_chunks: Sequence[Chunk]) -> str:\n", "    \"\"\"Convert retrieved chunks to string for use in dataframe.\"\"\"\n", "\n", "    def to_dict(chunk: Chunk) -> dict[str, Any]:\n", "        chunk_dict = {\n", "            \"id\": chunk.id,\n", "            \"text\": chunk.text,\n", "            \"parent_doc\": {\n", "                \"id\": chunk.parent_doc.id,\n", "                \"path\": chunk.parent_doc.path,\n", "                # WARNING: just storing empty string, we don't want to store  file\n", "                \"text\": \"\"\n", "                # Not supporting meta field\n", "            },\n", "            \"char_offset\": chunk.char_offset,\n", "            \"length\": chunk.length,\n", "            \"line_offset\": chunk.line_offset,\n", "            \"length_in_lines\": chunk.length_in_lines,\n", "            # Not supporting meta field\n", "        }\n", "        return chunk_dict\n", "\n", "    return json.dumps([to_dict(chunk) for chunk in retrieved_chunks])\n", "\n", "\n", "def process_repo(\n", "    files: Sequence[Mapping[str, Any]],\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", "    retrieval_database: Any,\n", "    tokenizer: StarCoderTokenizer,\n", ") -> Generator[pd.Series, None, None]:\n", "    \"\"\"Convert entire repo into retrieval-augmented FiM samples.\"\"\"\n", "    # Populate retrieval database with files\n", "    for file in files:\n", "        # Only add files of desired languages to be retrieved\n", "        if (\n", "            getattr(config, \"retrieval_languages\", None)\n", "            and file[FILE_LANG_COLUMN] not in config.retrieval_languages\n", "        ):\n", "            continue\n", "\n", "        document = Document(\n", "            id=file[ID_COLUMN], text=file[CONTENT_COLUMN], path=file[PATH_COLUMN]\n", "        )\n", "        retrieval_database.add_doc(document)\n", "\n", "    # Create samples from files and query retrieval database\n", "    for file in files:\n", "        # TODO(michiel) add option for sampling subset of rows\n", "\n", "        # Only add files of main languages to be trained on\n", "        if file[FILE_LANG_COLUMN] not in config.languages:\n", "            continue\n", "\n", "        # Construct multiple prefix, suffix, middle samples from file\n", "        base_samples = _file_to_samples(\n", "            file_content=file[CONTENT_COLUMN],\n", "            file_id=file[ID_COLUMN],\n", "            file_path=file[PATH_COLUMN],\n", "            config=config,\n", "            sampler=sampler,\n", "        )\n", "        for sample in base_samples:\n", "            sample = sample.truncated(\n", "                max_prefix_chars=config.max_prefix_chars,\n", "                max_suffix_chars=config.max_suffix_chars,\n", "            )\n", "\n", "            # For each sample, find retrieved chunks\n", "            retrieved_chunks = retrieval_database.query(\n", "                prefix=sample.prefix,\n", "                # Prompt formatter expects \"\" if no suffix but retrieval database expects None\n", "                suffix=None,\n", "                path=file[PATH_COLUMN],\n", "                top_k=config.num_retrieved_chunks,\n", "            )[0]\n", "\n", "            middle_tokens = _format_middle(\n", "                problem=sample,\n", "                tkn=tokenizer,\n", "                skip_id=tokenizer.skip_id,\n", "                pause_id=tokenizer.pause_id,\n", "                fim_stop_id=tokenizer.eod_id,\n", "            )\n", "            middle = tokenizer.detokenize(middle_tokens)\n", "\n", "            original_suffix_range = sample.original_suffix_range()\n", "            suffix_offset = len(sample.suffix) - (\n", "                original_suffix_range.stop - original_suffix_range.start\n", "            )\n", "            assert suffix_offset >= 0\n", "\n", "            middle_char_start = sample.middle_span.range.start\n", "            middle_char_end = original_suffix_range.start\n", "\n", "            # Convert sample into row\n", "            yield pd.Series(\n", "                dict(\n", "                    prefix=sample.prefix,\n", "                    middle=middle,\n", "                    suffix=sample.suffix,\n", "                    suffix_offset=suffix_offset,\n", "                    middle_char_start=middle_char_start,\n", "                    middle_char_end=middle_char_end,\n", "                    file_path=sample.file_path,\n", "                    retrieved_chunks=serialize_retrieved_chunks(retrieved_chunks),\n", "                )\n", "            )\n", "    # We keep database, but depopulate inbetween repos\n", "    retrieval_database.remove_all_docs()\n", "\n", "\n", "def filter_by_repo_size(df, min_size=None, max_size=None):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    if min_size is not None and max_size is not None:\n", "        condition = (F.col(\"total_size\") >= min_size) & (\n", "            F.col(\"total_size\") <= max_size\n", "        )\n", "    elif min_size is not None:\n", "        condition = F.col(\"total_size\") >= min_size\n", "    elif max_size is not None:\n", "        condition = F.col(\"total_size\") <= max_size\n", "    else:\n", "        condition = None\n", "\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["config = SimpleNamespace(\n", "    **{\n", "        \"input\": \"s3a://the-stack-processed/by-repo\",\n", "        # \"output\": \"s3a://michiel-dev-bucket/ragdata/gosmall/\",\n", "        \"output\": \"s3a://michiel-dev-bucket/ragdata/alphalangs_small_v2/\",\n", "        # \"output\": \"s3a://michiel-dev-bucket/spark-test/intermediate\",\n", "        \"languages\": [\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "        # \"languages\": [\"python\"],\n", "        # \"limit_repos\": 10000,\n", "        # \"limit_repos\": 400,\n", "        \"every_n_lines\": 150,\n", "        \"max_problems_per_file\": None,\n", "        \"max_prefix_chars\": 8000,\n", "        \"max_suffix_chars\": 8000,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000,\n", "        \"chunker\": \"line_level\",\n", "        # \"retriever_name\": \"bm25\",\n", "        \"retriever_name\": \"diff_boykin\",\n", "        \"max_query_lines\": 10,\n", "        \"max_chunk\": 40,\n", "        \"num_retrieved_chunks\": 40,\n", "        \"random_seed\": 74912,\n", "    }\n", ")\n", "\n", "STAGE1_URI = \"s3a://augment-temporary/test_rag/stage1/\"\n", "STAGE2_URI = \"s3a://augment-temporary/test_rag/stage2/\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["retrieval_database = create_retriever(\n", "    {\n", "        \"chunker\": config.chunker,\n", "        \"name\": config.retriever_name,\n", "        \"max_query_lines\": config.max_query_lines,\n", "        \"max_chunk\": config.max_chunk,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building CodeGenTokenizer tokenizer ...\n", " > padded vocab (size: 50328) with 872 dummy tokens (new size: 51200)\n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "> initializing torch distributed ...\n", "[2023-08-31 04:52:20,911] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-08-31 04:52:21,047] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=**************, master_port=6001\n", "[2023-08-31 04:52:21,047] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-08-31 04:52:21,051] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[xiaolei-dev3:2410273] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-08-31 04:52:21,122] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=25\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: _post_transformer_block\n", "    23: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    24: ContrastiveRetrievalHead\n", "  loss: contrastive_loss\n", "DeepSpeed is enabled.\n", "[2023-08-31 04:52:21,320] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-08-31 04:52:21,320] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-08-31 04:52:21,597] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4096, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4096\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-08-31 04:52:21,598] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 0.0003}\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   prescale_gradients ........... <PERSON>alse\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   train_batch_size ............. 12\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  12\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-08-31 04:52:21,599] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 12, \n", "    \"train_micro_batch_size_per_gpu\": 12, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 0.0003\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"initial_scale_power\": 12, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.23869752883911133 seconds\n", "[2023-08-31 04:52:22,542] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=12\n", "[2023-08-31 04:52:22,586] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=25 [0, 25) STAGE_PARAMS=304314370 (304.314M) TOTAL_PARAMS=304314370 (304.314M) UNIQUE_PARAMS=304314370 (304.314M)\n", " > number of parameters on model parallel rank 0: 304314370\n", "Warning: did not find final_linear layer, cannot calculate embedding params\n", " > total params: 304,314,370\n", " > embedding params: 0\n", "Loading: /home/<USER>/checkpoints/diff-retriever-boykin\n", "[2023-08-31 04:52:22,632] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-08-31 04:52:22,754] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_00-model_00-model_states.pt\n", "[2023-08-31 04:52:22,780] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_02-model_00-model_states.pt\n", "[2023-08-31 04:52:22,805] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_03-model_00-model_states.pt\n", "[2023-08-31 04:52:22,830] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_04-model_00-model_states.pt\n", "[2023-08-31 04:52:22,854] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_05-model_00-model_states.pt\n", "[2023-08-31 04:52:22,878] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_06-model_00-model_states.pt\n", "[2023-08-31 04:52:22,902] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_07-model_00-model_states.pt\n", "[2023-08-31 04:52:22,926] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_08-model_00-model_states.pt\n", "[2023-08-31 04:52:22,950] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_09-model_00-model_states.pt\n", "[2023-08-31 04:52:22,973] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_10-model_00-model_states.pt\n", "[2023-08-31 04:52:22,998] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_11-model_00-model_states.pt\n", "[2023-08-31 04:52:23,023] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_12-model_00-model_states.pt\n", "[2023-08-31 04:52:23,047] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_13-model_00-model_states.pt\n", "[2023-08-31 04:52:23,072] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_14-model_00-model_states.pt\n", "[2023-08-31 04:52:23,096] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_15-model_00-model_states.pt\n", "[2023-08-31 04:52:23,114] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_16-model_00-model_states.pt\n", "[2023-08-31 04:52:23,138] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_17-model_00-model_states.pt\n", "[2023-08-31 04:52:23,163] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_18-model_00-model_states.pt\n", "[2023-08-31 04:52:23,185] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_19-model_00-model_states.pt\n", "[2023-08-31 04:52:23,204] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_20-model_00-model_states.pt\n", "[2023-08-31 04:52:23,228] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_21-model_00-model_states.pt\n", "[2023-08-31 04:52:23,229] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_23-model_00-model_states.pt\n", "[2023-08-31 04:52:23,229] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/layer_24-model_00-model_states.pt\n", "checkpoint_name: /home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /home/<USER>/checkpoints/diff-retriever-boykin/global_step5000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["retrieval_database.scorer.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/08/30 20:53:11 WARN Utils: Your hostname, xiaolei-dev3 resolves to a loopback address: *********; using ************** instead (on interface enp4s0)\n", "23/08/30 20:53:11 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/08/30 20:53:44 WARN FileSystem: Cannot load filesystem: java.util.ServiceConfigurationError: org.apache.hadoop.fs.FileSystem: com.google.cloud.hadoop.fs.gcs.GoogleHadoopFileSystem Unable to get public no-arg constructor\n", "23/08/30 20:53:44 WARN FileSystem: java.lang.NoClassDefFoundError: com/google/api/client/http/HttpRequestInitializer\n", "23/08/30 20:53:44 WARN FileSystem: java.lang.ClassNotFoundException: com.google.api.client.http.HttpRequestInitializer\n", "23/08/30 20:53:45 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "[Stage 3:>                                                          (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 284413 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/08/30 21:14:25 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "# At 2000 partitions each one is between 100 to 200 repos.\n", "# Probably don't want more than 20000 partitions in anycase because we are\n", "# gonna spend most of the time initalizing retrieval databases to that limit.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "if hasattr(config, \"languages\"):\n", "    config.languages = [lang.lower() for lang in config.languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.languages))\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=getattr(config, \"repo_min_size\", None),\n", "    max_size=getattr(config, \"repo_max_size\", None),\n", ")\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "# limit_repos = config.limit_repos\n", "\n", "\n", "# df = df.limit(limit_repos)\n", "\n", "# # num_partitions = limit_repos\n", "# num_partitions = max(limit_repos // 5, 120)\n", "\n", "\n", "df = df.repartition(2000)\n", "# Perform repo-specific processing\n", "df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame,\n", "    config: SimpleNamespace,\n", ") -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "    # TODO(michiel) update for retriever query formatting options\n", "    retrieval_database = create_retriever(\n", "        {\n", "            \"chunker\": config.chunker,\n", "            \"name\": config.retriever_name,\n", "            \"max_query_lines\": config.max_query_lines,\n", "            \"max_chunk\": config.max_chunk,\n", "        }\n", "    )\n", "\n", "    if config.retriever_name != \"bm25\":\n", "        retrieval_database.scorer.load()\n", "\n", "    # Currently max middle is set in terms of characters and we just use an upper bound\n", "    # of chars to tokens to make sure our middle isn't too many tokens\n", "    sampler = CSTFimSampler()\n", "    sampler.rng.seed(config.random_seed)\n", "\n", "    tokenizer = StarCoderTokenizer()\n", "    # return map(lambda row: process_repo_fn(row), partition_iter)\n", "\n", "    for files in batch.file_list:\n", "        yield from process_repo(\n", "            files,\n", "            config=config,\n", "            sampler=sampler,\n", "            retrieval_database=retrieval_database,\n", "            tokenizer=tokenizer,\n", "        )"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Doing a timing run.  Processing one batch per file and a maximum of 5 files.\n", "23/08/30 22:44:54 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"20G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "spark = k8s_session(\n", "    max_workers=5,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    input_path=STAGE1_URI,\n", "    output_path=STAGE2_URI,\n", "    batch_size=20,  # small batch size so that we can get estimates quickly\n", "    timing_run=True,\n", "    profile=True,  # do some profiling to see where the computes are spent\n", ")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status_count': {'success': 5},\n", " 'task_info':                                           input_path  \\\n", " 0  s3a://augment-temporary/test_rag/stage1/part-0...   \n", " 1  s3a://augment-temporary/test_rag/stage1/part-0...   \n", " 2  s3a://augment-temporary/test_rag/stage1/part-0...   \n", " 3  s3a://augment-temporary/test_rag/stage1/part-0...   \n", " 4  s3a://augment-temporary/test_rag/stage1/part-0...   \n", " \n", "                                          output_path   status  timeout  \\\n", " 0  s3a://augment-temporary/test_rag/stage2/part-0...  success    False   \n", " 1  s3a://augment-temporary/test_rag/stage2/part-0...  success    False   \n", " 2  s3a://augment-temporary/test_rag/stage2/part-0...  success    False   \n", " 3  s3a://augment-temporary/test_rag/stage2/part-0...  success    False   \n", " 4  s3a://augment-temporary/test_rag/stage2/part-0...  success    False   \n", " \n", "    walltime_ms  exit_code  max_memory_gb  total_memory_gb  min_free_gb  \\\n", " 0       232439          0       3.344627             25.0    23.348335   \n", " 1       252517          0       3.335094             25.0    23.370499   \n", " 2       212487          0       3.340668             25.0    23.366108   \n", " 3       223196          0       3.339672             25.0    20.754734   \n", " 4       191623          0       3.308945             25.0    23.431679   \n", " \n", "                                               stdout stderr  \\\n", " 0  [process_file] Start processing s3a://augment-...          \n", " 1  [process_file] Start processing s3a://augment-...          \n", " 2  [process_file] Start processing s3a://augment-...          \n", " 3  [process_file] Start processing s3a://augment-...          \n", " 4  [process_file] Start processing s3a://augment-...          \n", " \n", "                                              profile  \n", " 0           221780795 function calls (221020338 p...  \n", " 1           249980228 function calls (249163500 p...  \n", " 2           230869859 function calls (230103150 p...  \n", " 3           242692085 function calls (241953240 p...  \n", " 4           192435619 function calls (191739598 p...  }"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# A rough view of the results give you the success rates, wall time and memory use etc\n", "result"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== OUTPUT ===\n", "[process_file] Start processing s3a://augment-temporary/test_rag/stage1/part-00004-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "[process_file] Processing batch 0 of 7\n", "Batch shape: (20, 5)\n", "Worker return type: <class 'pandas.core.series.Series'>\n", "Worker return size: 2512\n", "output shape: 2512 * 13\n", "[process_file] Creating new writer for augment-temporary/test_rag/stage2/part-00004-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "Est time for 7 batches: 1607.2601943016052s\n", "[process_file] Closing parquet writer\n", "[process_file] Processed s3a://augment-temporary/test_rag/stage1/part-00004-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet to augment-temporary/test_rag/stage2/part-00004-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "\n", "=== OUTPUT ===\n", "[process_file] Start processing s3a://augment-temporary/test_rag/stage1/part-00001-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "[process_file] Processing batch 0 of 7\n", "Batch shape: (20, 5)\n", "Worker return type: <class 'pandas.core.series.Series'>\n", "Worker return size: 2421\n", "output shape: 2421 * 13\n", "[process_file] Creating new writer for augment-temporary/test_rag/stage2/part-00001-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "Est time for 7 batches: 1747.7322194576263s\n", "[process_file] Closing parquet writer\n", "[process_file] Processed s3a://augment-temporary/test_rag/stage1/part-00001-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet to augment-temporary/test_rag/stage2/part-00001-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "\n", "=== OUTPUT ===\n", "[process_file] Start processing s3a://augment-temporary/test_rag/stage1/part-00002-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "[process_file] Processing batch 0 of 7\n", "Batch shape: (20, 5)\n", "Worker return type: <class 'pandas.core.series.Series'>\n", "Worker return size: 1955\n", "output shape: 1955 * 13\n", "[process_file] Creating new writer for augment-temporary/test_rag/stage2/part-00002-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "Est time for 7 batches: 1467.6020612716675s\n", "[process_file] Closing parquet writer\n", "[process_file] Processed s3a://augment-temporary/test_rag/stage1/part-00002-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet to augment-temporary/test_rag/stage2/part-00002-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "\n", "=== OUTPUT ===\n", "[process_file] Start processing s3a://augment-temporary/test_rag/stage1/part-00000-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "[process_file] Processing batch 0 of 7\n", "Batch shape: (20, 5)\n", "Worker return type: <class 'pandas.core.series.Series'>\n", "Worker return size: 2176\n", "output shape: 2176 * 13\n", "[process_file] Creating new writer for augment-temporary/test_rag/stage2/part-00000-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "Est time for 7 batches: 1542.1758441925049s\n", "[process_file] Closing parquet writer\n", "[process_file] Processed s3a://augment-temporary/test_rag/stage1/part-00000-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet to augment-temporary/test_rag/stage2/part-00000-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "\n", "=== OUTPUT ===\n", "[process_file] Start processing s3a://augment-temporary/test_rag/stage1/part-00003-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "[process_file] Processing batch 0 of 7\n", "Batch shape: (20, 5)\n", "Worker return type: <class 'pandas.core.series.Series'>\n", "Worker return size: 1926\n", "output shape: 1926 * 13\n", "[process_file] Creating new writer for augment-temporary/test_rag/stage2/part-00003-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "Est time for 7 batches: 1322.315501689911s\n", "[process_file] Closing parquet writer\n", "[process_file] Processed s3a://augment-temporary/test_rag/stage1/part-00003-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet to augment-temporary/test_rag/stage2/part-00003-643261e9-5a79-447c-9fb1-612d5a45fcb6-c000.zstd.parquet\n", "\n"]}], "source": ["# Output of the tasks contain time estimates.  Note that time estimates would get shorter\n", "# if batches are larger, so we need to account for that when setting the final timeout\n", "for output in result[\"task_info\"].stdout:\n", "    print(\"=== OUTPUT ===\")\n", "    print(output)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         ******** function calls (******** primitive calls) in 91.587 seconds\n", "\n", "   Ordered by: cumulative time\n", "   List reduced from 7947 to 50 due to restriction <50>\n", "\n", "   ncalls  tottime  percall  cumtime  percall filename:lineno(function)\n", "      564    0.003    0.000   87.722    0.156 /tmp/ipykernel_2284993/*********.py:1(process_partition_pandas)\n", "      978    0.002    0.000   61.706    0.063 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/megatron/inference/process_wrap.py:58(proxy_func)\n", "      978    0.001    0.000   61.663    0.063 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/megatron/inference/process_wrap.py:78(_receive)\n", " 8786/978   58.802    0.007   61.662    0.063 {built-in method _pickle.load}\n", "      568    0.040    0.000   60.045    0.106 /tmp/ipykernel_2284993/1178561170.py:72(process_repo)\n", "        1    0.000    0.000   27.572   27.572 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/retrieval/libraries/scorers/dense_scorer.py:58(load)\n", "      421    0.011    0.000   26.588    0.063 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/retrieval/retrieval_database.py:119(add_doc)\n", "      412    0.007    0.000   24.720    0.060 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/retrieval/libraries/scorers/dense_scorer.py:117(add_doc)\n", "      563    0.383    0.001   14.887    0.026 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/retrieval/retrieval_database.py:191(query)\n", "      412    0.004    0.000   14.836    0.036 /tmp/ipykernel_2284993/1178561170.py:16(_file_to_samples)\n", "      563    0.984    0.002   14.434    0.026 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/retrieval/libraries/scorers/dense_scorer.py:166(score)\n", "      412    0.005    0.000   12.932    0.031 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:394(sample_every_n_lines)\n", "      568    0.009    0.000   12.894    0.023 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:371(sample_fim_best_of_k)\n", "     1704    0.093    0.000   12.633    0.007 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:254(sample_fim)\n", "     1704    0.029    0.000   11.463    0.007 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:425(_pick_random_middle)\n", "     1704    2.499    0.001    5.833    0.003 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:570(_get_nodes_in_brange)\n", "  2174004    1.330    0.000    3.392    0.000 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:504(_node_nonwhitespace_chars)\n", "     1704    1.266    0.001    3.380    0.002 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:429(<listcomp>)\n", "      824    0.028    0.000    3.353    0.004 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/parsing.py:313(parse)\n", "      824    0.007    0.000    3.325    0.004 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/parsing.py:586(parse_scopes)\n", "     1236    2.332    0.002    3.048    0.002 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/common.py:112(__init__)\n", "        2    2.711    1.355    2.950    1.475 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/data/spark/pipelines/utils/map_parquet.py:75(iter_batches)\n", "      563    0.001    0.000    2.838    0.005 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/megatron/tokenizer/tokenizer.py:398(detokenize)\n", "      563    0.002    0.000    2.837    0.005 /opt/conda/lib/python3.9/site-packages/transformers/tokenization_utils_base.py:3456(decode)\n", "     1952    0.005    0.000    2.826    0.001 /opt/conda/lib/python3.9/site-packages/torch/storage.py:240(_load_from_bytes)\n", "     1952    0.021    0.000    2.821    0.001 /opt/conda/lib/python3.9/site-packages/torch/serialization.py:671(load)\n", "158386/563    0.170    0.000    2.771    0.005 /opt/conda/lib/python3.9/site-packages/transformers/utils/generic.py:182(to_py_obj)\n", "      563    0.028    0.000    2.769    0.005 /opt/conda/lib/python3.9/site-packages/transformers/utils/generic.py:189(<listcomp>)\n", "     1952    0.024    0.000    2.753    0.001 /opt/conda/lib/python3.9/site-packages/torch/serialization.py:835(_legacy_load)\n", "        1    0.000    0.000    2.461    2.461 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/megatron/inference/process_wrap.py:18(__init__)\n", "      439    0.003    0.000    2.443    0.006 {built-in method io.open}\n", "      363    0.000    0.000    2.442    0.007 /opt/conda/lib/python3.9/pathlib.py:1246(open)\n", "      363    0.000    0.000    2.439    0.007 /opt/conda/lib/python3.9/pathlib.py:1118(_opener)\n", "      363    2.438    0.007    2.439    0.007 {built-in method posix.open}\n", "     1952    2.394    0.001    2.394    0.001 {method '_set_from_file' of 'torch._C.StorageBase' objects}\n", "   157823    0.062    0.000    2.244    0.000 /opt/conda/lib/python3.9/site-packages/transformers/utils/generic.py:162(is_tf_tensor)\n", "   157823    0.050    0.000    2.169    0.000 /opt/conda/lib/python3.9/site-packages/transformers/utils/generic.py:156(_is_tensorflow)\n", "     1704    0.147    0.000    2.165    0.001 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:449(<listcomp>)\n", "   2438/3    0.009    0.000    2.114    0.705 <frozen importlib._bootstrap>:1002(_find_and_load)\n", "   2435/3    0.008    0.000    2.114    0.705 <frozen importlib._bootstrap>:967(_find_and_load_unlocked)\n", "   2388/3    0.008    0.000    2.112    0.704 <frozen importlib._bootstrap>:659(_load_unlocked)\n", "   2308/3    0.004    0.000    2.112    0.704 <frozen importlib._bootstrap_external>:844(exec_module)\n", "   4314/3    0.002    0.000    2.101    0.700 <frozen importlib._bootstrap>:220(_call_with_frames_removed)\n", "   2322/3    0.006    0.000    2.101    0.700 {built-in method builtins.exec}\n", "        1    0.000    0.000    2.099    2.099 /opt/conda/lib/python3.9/site-packages/tensorflow/__init__.py:15(<module>)\n", "  1860/52    0.005    0.000    2.095    0.040 {built-in method builtins.__import__}\n", "10722/721    0.008    0.000    2.031    0.003 <frozen importlib._bootstrap>:1033(_handle_fromlist)\n", "   992226    0.493    0.000    2.018    0.000 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/static_analysis/fim_sampling.py:440(score_node)\n", "2946/2944    0.011    0.000    1.945    0.001 /opt/conda/lib/python3.9/functools.py:973(__get__)\n", "  1243803    0.746    0.000    1.895    0.000 /mnt/efs/augment-nvme/python_env/2023-08-30/xiaolei-dev3/1a1f31eb-1996-4bcb-9659-b9677f1fc128/lib/python3.9/site-packages/augment/research/core/types.py:37(intersect)\n", "\n", "\n", "\n"]}], "source": ["# Take a quick look at the profile of the task to see where the time is spent\n", "print(result[\"task_info\"].profile[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "spark = k8s_session(\n", "    max_workers=100,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    input_path=STAGE1_URI,\n", "    output_path=STAGE2_URI,\n", "    timeout=3600,  # one hour timeout\n", "    batch_size=50,\n", ")\n", "spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
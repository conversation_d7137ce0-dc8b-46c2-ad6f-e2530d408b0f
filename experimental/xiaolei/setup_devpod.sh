#!/bin/bash

sudo mkdir /usr/local/local_user_base
sudo chown augment:augment /usr/local/local_user_base

bash /home/<USER>/augment/research/research-init.sh


# install homebrew

/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
test -d ~/.linuxbrew && eval "$(~/.linuxbrew/bin/brew shellenv)"
test -d /home/<USER>/.linuxbrew && eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"

source ~/.bashrc

# install graphite

brew install withgraphite/tap/graphite

# configure ownership

sudo chown -R $(whoami) /home/<USER>/.linuxbrew/Cellar
sudo chown -R $(whoami) /home/<USER>/.linuxbrew/Homebrew
sudo chown -R $(whoami) /home/<USER>/.linuxbrew/var/homebrew
sudo chown -R $(whoami) /home/<USER>/.linuxbrew/bin /home/<USER>/.linuxbrew/etc /home/<USER>/.linuxbrew/include /home/<USER>/.linuxbrew/lib /home/<USER>/.linuxbrew/opt /home/<USER>/.linuxbrew/sbin /home/<USER>/.linuxbrew/share

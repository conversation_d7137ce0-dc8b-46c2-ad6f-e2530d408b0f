import json

questions = []
with open("/mnt/efs/spark-data/user/xiaolei/questions.json") as f:
    for line in f:
        item = json.loads(line)
        if "message" not in item or not item["message"]:
            continue
        questions.append(item["message"])


TEMPLATE = """
Below are some questions that developers asked.  Please filter those that cannot be answered by widely known information and can be helped by reference to specific software documentations,
then categorize these qualifying questions into categories and return new categories not in the sample list you are given.

Please answer with one category per line and nothing else.

Here are some example answers.
```
- Understanding the usage of specific methods or functions in a library
- Performing tasks with particular software versions and their unique features
- Finding and utilizing hidden or lesser-known tools and features within a package
- Resolving specific error messages or exceptions based on documentation guidelines
- Migrating code between libraries or frameworks while following documented steps
- Upgrading between incompatible software versions using migration guides
- Configuring software tools for specific environments or use cases as per documentation
- Implementing features according to best practices outlined in official guidelines
- Integrating software tools with other systems using documented APIs and methods
- Optimizing performance of functions or processes as recommended in documentation
- Handling edge cases or uncommon scenarios as described in troubleshooting sections
- Identifying alternatives for deprecated methods or features through official documentation
- Customizing software behavior using configuration options detailed in the docs
- Managing dependencies and resolving version conflicts based on compatibility matrices
- Applying security recommendations and practices from the security documentation
- Effectively using APIs provided by a tool, including endpoint details and examples
- Developing or modifying plugins and extensions using the extension development guide
- Troubleshooting installation or setup issues using step-by-step documentation
- Applying advanced debugging techniques as suggested in the debugging guide
- Automating tasks with built-in scripts or tools documented in the automation guide
```

{questions}

Now, return new categories and remember to not include anything in the given list.  If there are nothing beyond these categories, return "NOTHING"

"""


def create_prompts(
    questions: list[str],
    prompt_template: str,
    question_per_prompt=100,
    max_question_len: int = 400,
):
    prompts = []
    batch = []
    for i, question in enumerate(questions):
        if len(question) > max_question_len + 1:
            question = (
                question[: max_question_len // 2]
                + "...\n"
                + question[-max_question_len // 2 :]
            )
        question = f"- Question {i + 1}:\n{question}\n\n"
        batch.append(question)
        if len(batch) == question_per_prompt:
            prompts.append(prompt_template.format(questions="\n".join(batch)))
            batch = []
    return prompts


prompts = create_prompts(questions, prompt_template=TEMPLATE)
with open("/mnt/efs/spark-data/user/xiaolei/agg_prompts.json", "w+") as f:
    for prompt in prompts:
        f.write(json.dumps({"question": prompt}) + "\n")

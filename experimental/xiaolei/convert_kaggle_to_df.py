"""Convert Kaggle raw files dump to dataframe."""

from pyspark.sql import functions as F

from research.data.spark import k8s_session

spark = k8s_session(max_workers=100)

for i in range(171, 179):
    print("Working on partition", i)
    rdd = spark.sparkContext.wholeTextFiles(
        f"/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/raw_dump/{i:04d}/*/*"
    )
    df = spark.createDataFrame(rdd, ["path", "content"]).withColumn(
        "lang", F.element_at(F.split("path", r"\."), -1)
    )
    df.repartition(20).write.partitionBy("lang").mode("overwrite").parquet(
        f"/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/partitioned/part={i:04d}"
    )

spark.stop()

from delta.tables import DeltaTable  # Importing DeltaTable
from research.data.spark import k8s_session

# Create a SparkSession
spark = k8s_session(max_workers=20)

# Assuming Delta Lake is properly set up in your environment, and delta-core is in your classpath

# Load the Delta table
deltaTable = DeltaTable.forPath(spark, "s3a://augment-github/repo/converted/")

# Vacuum the Delta table for changes older than 24 hours
# NOTE: Disabling safety check for demonstration purposes. Be cautious with this setting.
spark.conf.set("spark.databricks.delta.retentionDurationCheck.enabled", "false")
deltaTable.vacuum(12)  # Retention period in hours

spark.stop()

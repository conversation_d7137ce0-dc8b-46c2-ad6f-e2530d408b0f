- **erl; Linux; and tty reset** from **knotwell@REDACTED (knotwell@REDACTED)** on **<PERSON><PERSON>, 20 Apr 1999 16:16:51 -0700 (PDT)**:

    Hello all--

    I wrote a small program (many thanks to the author of the urlget
    module).  I run it from the command line with the following command:

    	  erl -s htmlhammer start # # URL -s erlang halt

    This seems to work okay except for one minor annoyance--every time erl
    "halts", the pseudo-terminal get "hosed up" and I need to do a reset or
    stty sane to fix it.

    It's not critical, but I was wondering if there was anyway around this
    problem.  The only other thing I've ever seen do this (_very_
    periodically) is a botched xemacs startup when using the -nw option.

    Another minor question:  is there a module performing the common (I
    presume) idiom of:

       list_to_integer(atom_to_list('numeric_atom_here'))
       %%IOW, is there an

    --Brad





  - **erl; Linux; and tty reset** from **dne@REDACTED (<PERSON>)** on **Wed, 21 Apr 1999 02:36:47 +0200**:
      knotwell@REDACTED writes:

      > This seems to work okay except for one minor annoyance--every time erl
      > "halts", the pseudo-terminal get "hosed up" and I need to do a reset or
      > stty sane to fix it.

      Yes, I'm experiencing this problem too. If you terminate the shell by
      doing "Ctrl-G q" or by executing "halt().", the tty stays "insane" and
      has to be manually restored. Quitting by "Ctrl-C a", restores the tty
      correctly.


      Regards,
      /Daniel

  - **erl; Linux; and tty reset** from **ulf.wiger@REDACTED (Ulf Wiger)** on **Wed, 21 Apr 1999 09:29:47 +0200**:
      knotwell@REDACTED wrote:
      >
      > Hello all--
      >
      > I wrote a small program (many thanks to the author of the urlget
      > module).  I run it from the command line with the following command:
      >
      >           erl -s htmlhammer start # # URL -s erlang halt
      >
      > This seems to work okay except for one minor annoyance--every time erl
      > "halts", the pseudo-terminal get "hosed up" and I need to do a reset or
      > stty sane to fix it.

      You could try init:stop() instead of erlang:halt(). It's less brutal.
      Another problem you might run into is that the halt() command comes too
      soon. Your function might be written such that it doesn't release the
      thread before it's done. Then you avoid the problem.

      One thing you could try is to write a synchronous function, one which
      doesn't return until the operation is finished. Then you pipe it into an
      erlang shell:

      $ > echo 'io:format("hello.~n",[]).' | erl -boot start_clean
      Eshell V4.7.1  (abort with ^G)
      1> hello.
      ok
      ** Terminating erlang **
      $ >

      When the shell reaches ^D, it terminates the shell automatically.


      Another option is erl_call (I don't know if it works on all platforms.)

      $ > setenv FOO `erl_call -s -name foo -a 'erlang localtime []'`
      $ > echo $FOO
      1999 4 21 9 28 11
      $ > erl_call -q -name foo


      > It's not critical, but I was wondering if there was anyway around this
      > problem.  The only other thing I've ever seen do this (_very_
      > periodically) is a botched xemacs startup when using the -nw option.
      >
      > Another minor question:  is there a module performing the common (I
      > presume) idiom of:
      >
      >    list_to_integer(atom_to_list('numeric_atom_here'))
      >    %%IOW, is there an

      No, no such module.

      /Uffe
  - **erl; Linux; and tty reset** from **seb@REDACTED (Sebastian Strollo)** on **21 Apr 1999 15:56:56 +0200**:
      knotwell@REDACTED writes:
      ...
      > This seems to work okay except for one minor annoyance--every time erl
      > "halts", the pseudo-terminal get "hosed up" and I need to do a reset or
      > stty sane to fix it.
      ...

      Yes

      This my fault and I should be shot for messing it up. In my work to
      make the system portable I cleaned up all calls to atexit() in the
      emulator (well there was only one, and it was the one which cleans up
      the terminal settings). I thought that I had replaced it with a call
      to another "atexit type" function, but it turns out that it is never
      called before exit.

      I will fix it, add it to the FAQ and it will be in the next release...

      -- Sebastian

      PS.
      If you use tcsh, which I do, you don't notice this since tcsh is
      forgiving (smart?) enough to reset your terminal to a nice state...
      (Which is my excuse for letting this whole thing slip through our
      testing.)


  - **ftp:close and garbage collection** from **knotwell@REDACTED (knotwell@REDACTED)** on **Fri, 7 May 1999 16:32:04 -0700 (PDT)**:

      Hello all--

      A question. . .I wrote a small ftphammer program.  When testing the
      program, I noticed that it ran reasonably well at first but after a
      short time the performance degraded to unacceptably.  Anyhow,
      using the process monitor, I noticed that ftp connections appeared to
      stay around after leaving the following function:

      getData(Host,InFile,OutFile) ->
          FP = element(2,ftp:open(Host)),
          ftp:user(FP,"anonymous","knotwell@REDACTED"),
          case catch ftp:recv(FP,InFile,OutFile) of
      	 ok    -> ok;
      	 Other -> error
          end.


      It turned out I needed to do the following (in order to get everything
      to cleanup okay):

      getData(Host,InFile,OutFile) ->
          FP = element(2,ftp:open(Host)),
          ftp:user(FP,"anonymous","knotwell@REDACTED"),
          case catch ftp:recv(FP,InFile,OutFile) of
              ok    ->
                      ftp:close(FP),
                      ok;
              Other -> error
          end.

      Assuming that I'm correct in thinking that ftp connections are not
      finalized automatically, is this:

      1)  related to Erlang's inability to garbage collect atoms
      2)  enforcing good practice--making the programmer reclaim allocated
          resources
      3)  a bug

      A small aside:  it isn't really clear to me why there should be
      separate open and user functions.  I suppose if your first
      username/password didn't work and you wanted to try again without
      forcing the server to spawn another ftpd.

      Thanks.

      --Brad









  - **ftp:close and garbage collection** from **cesarini@REDACTED (F. Cesarini)** on **Sun, 09 May 1999 23:50:54 +0200**:
      When you FTP a file from the shell, you have to type bye to end the
      session. The same probably applies with the module provided.

      I haven't seen the code, but a process is usually spawned for
      applications with a similar behavior. By executing MODULE:close/1, you
      usually terminate the process. MODULE:open or MODULE:start/1 spawns the
      process. It is probably more efficient to allow the user to use the same
      process to handle several requests than to spawn a process for every new
      request.
      Many users (Processes) could concurrently, if given access to the same
      FD, use the same process.

      As you are not using any atoms, that is clearly not the problem.

      Two comments on your code, if you don't mind. (I am somewhat brain
      washed from my job, and react whenever I see room for improvement..
      :)...

      {ok, FP} = ftp:open(Host)

      is much clearer, and will terminate immediately if there was an error in
      opening a connection to the host. Had {error, Reason} been returned,
      you would have opened a user session with Reason as a FP, probably
      causing a crash in the ftp module. The error would thus be harder to
      detect.

      I would call ftp:close/1 even if ftp:recv/3 fails should the process
      still be alive. If a request fails, the resources are probably still
      allocated.

      Hope this helps,
      Francesco

      > It turned out I needed to do the following (in order to get everything
      > to cleanup okay):
      >
      > getData(Host,InFile,OutFile) ->
      >     FP = element(2,ftp:open(Host)),
      >     ftp:user(FP,"anonymous","knotwell@REDACTED"),
      >     case catch ftp:recv(FP,InFile,OutFile) of
      >         ok    ->
      >                 ftp:close(FP),
      >                 ok;
      >         Other -> error
      >     end.
      >
      > Assuming that I'm correct in thinking that ftp connections are not
      > finalized automatically, is this:
      >
      > 1)  related to Erlang's inability to garbage collect atoms
      > 2)  enforcing good practice--making the programmer reclaim allocated
      >     resources
      > 3)  a bug
      >
      > A small aside:  it isn't really clear to me why there should be
      > separate open and user functions.  I suppose if your first
      > username/password didn't work and you wanted to try again without
      > forcing the server to spawn another ftpd.
      >
      > Thanks.
      >
      > --Brad

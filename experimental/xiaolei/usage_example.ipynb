{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d4875dd2-d750-40ad-9466-6ebfa985fc92", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T08:16:38.801687Z", "iopub.status.busy": "2023-07-01T08:16:38.800183Z", "iopub.status.idle": "2023-07-01T08:16:43.896456Z", "shell.execute_reply": "2023-07-01T08:16:43.894786Z", "shell.execute_reply.started": "2023-07-01T08:16:38.801648Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/07/31 17:41:15 WARN Utils: Your hostname, xiaolei-dev resolves to a loopback address: *********; using ************** instead (on interface enp2s0)\n", "23/07/31 17:41:15 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Packaging folder /tmp/tmpddfguv33 into /tmp/spark-uploads/packages-for-workers.zip\n"]}], "source": ["import pyspark.sql.functions as F\n", "from research.data.spark import k8s_session\n", "\n", "\n", "spark = k8s_session()"]}, {"cell_type": "markdown", "id": "f9665f03-6952-4e7b-8dec-ba0cbbfe0753", "metadata": {}, "source": ["### Load a small sample set\n", "Here we just read one hour of ingested data as an example.  load them, then take a look at the file content for example."]}, {"cell_type": "code", "execution_count": 2, "id": "7da6e12f-0896-44a4-942c-7221b74698bb", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T09:20:17.866715Z", "iopub.status.busy": "2023-07-01T09:20:17.865767Z", "iopub.status.idle": "2023-07-01T09:20:23.923992Z", "shell.execute_reply": "2023-07-01T09:20:23.922714Z", "shell.execute_reply.started": "2023-07-01T09:20:17.866650Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/07/31 17:41:43 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>content</th><th>is_binary</th><th>path</th><th>pct_modified</th><th>repo_id</th><th>repo_name</th><th>repo_root</th><th>sha</th><th>size</th><th>lang</th><th>is_head</th></tr>\n", "<tr><td>import fs from &#x27;fs&#x27;\\nimport path from &#x27;path&#x27;\\nimport * as core from &#x27;@actions/core&#x27;\\nimport {Cont...</td><td>false</td><td>__tests__/main.test.ts</td><td>200</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>52d2b3bcd5079457e63ae564ca6b0e98d7276d9b</td><td>13739</td><td>TypeScript</td><td>true</td></tr>\n", "<tr><td>{\\n  &quot;name&quot;: &quot;typescript-action&quot;,\\n  &quot;version&quot;: &quot;0.0.0&quot;,\\n  &quot;lockfileVersion&quot;: 2,\\n  &quot;requires&quot;: ...</td><td>false</td><td>d3d239647d3e4eb5886570adcd9258794ca40e37:package-lock.json</td><td>55</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>8b01eeec68691d970af8774581a10a906b57e74c</td><td>451514</td><td>JSON</td><td>false</td></tr>\n", "<tr><td>import * as core from &#x27;@actions/core&#x27;\\nimport Docker, {Container} from &#x27;dockerode&#x27;\\nimport {Conta...</td><td>false</td><td>src/updater-builder.ts</td><td>200</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>1d2f7eda7528f5d28e085efcf9097c194f33c1a6</td><td>2895</td><td>TypeScript</td><td>true</td></tr>\n", "<tr><td>{\\n  &quot;name&quot;: &quot;typescript-action&quot;,\\n  &quot;version&quot;: &quot;0.0.0&quot;,\\n  &quot;lockfileVersion&quot;: 2,\\n  &quot;requires&quot;: ...</td><td>false</td><td>c4de23772982d3d0cb810748c75da64429db85ad:package-lock.json</td><td>25</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>58093bac7838211fe1dba5793a5bfd203ba2e7fc</td><td>477334</td><td>JSON</td><td>false</td></tr>\n", "<tr><td>{\\n  &quot;name&quot;: &quot;typescript-action&quot;,\\n  &quot;version&quot;: &quot;0.0.0&quot;,\\n  &quot;lockfileVersion&quot;: 2,\\n  &quot;requires&quot;: ...</td><td>false</td><td>56efa1411f3301486398d8d296f4cd4fc5cdc17e:package-lock.json</td><td>55</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>581ccba882621554c63f985582b3fdfc1cae6197</td><td>451558</td><td>JSON</td><td>false</td></tr>\n", "<tr><td>{\\n  &quot;name&quot;: &quot;typescript-action&quot;,\\n  &quot;version&quot;: &quot;0.0.0&quot;,\\n  &quot;lockfileVersion&quot;: 2,\\n  &quot;requires&quot;: ...</td><td>false</td><td>e4375b7b7fb6075a2e3ffb8058c8b95da3d1f83e:package-lock.json</td><td>76</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>eba8a3ba3bd6607032bf748ee1552b56ea16f2a4</td><td>823264</td><td>JSON</td><td>false</td></tr>\n", "<tr><td>{\\n  &quot;name&quot;: &quot;typescript-action&quot;,\\n  &quot;version&quot;: &quot;0.0.0&quot;,\\n  &quot;private&quot;: true,\\n  &quot;description&quot;: &quot;T...</td><td>false</td><td>85a42505ba11446b6501d8395f4b564315a62172:package.json</td><td>34</td><td>f7ecbaac-6b99-46a8-985e-8cea499ef376</td><td>github/dependabot-action</td><td>github/413862514</td><td>049e1d814ca2b8ef1e6f0c8ac191458d92852605</td><td>1166</td><td>JSON</td><td>false</td></tr>\n", "<tr><td>## Coordinate Ascent\\n## Restart = 5\\n## MaxIteration = 25\\n## StepBase = 0.05\\n## StepScale = 2....</td><td>false</td><td>listsearch/transe_models/f5.ca</td><td>200</td><td>1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb</td><td>thunlp/EmbeddingEntityRetrieval</td><td>github/202404436</td><td>af72adb64e62e74e278275fff1a64d3832820c81</td><td>804</td><td></td><td>true</td></tr>\n", "<tr><td>0.0 qid:3 1:-7.097468 2:-37.596947 3:5.6967936 4:-37.678677 5:2.0 6:0.54719 7:-37.301388 8:6.9367...</td><td>false</td><td>listsearch/elr_svm/train4</td><td>200</td><td>1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb</td><td>thunlp/EmbeddingEntityRetrieval</td><td>github/202404436</td><td>dd7689d5a18f5d93cec89081338707bd4737b558</td><td>3265629</td><td></td><td>true</td></tr>\n", "<tr><td>SemSearch_ES-12\\tQ0\\t&lt;dbpedia:Austin%2C_Texas&gt;\\t1\\t-1.3956818743426784\\tCA\\nSemSearch_ES-12\\tQ0\\t...</td><td>false</td><td>semsearch/transe.shuffled.trec</td><td>200</td><td>1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb</td><td>thunlp/EmbeddingEntityRetrieval</td><td>github/202404436</td><td>dd758858807d14573f4d0b1c4fad7b07edcde86f</td><td>1037317</td><td></td><td>true</td></tr>\n", "<tr><td>#!/usr/bin/env bash\\nCUDA_VISIBLE_DEVICES=2 \\\\npython main.py \\\\n--data_dir ../../data-noun/ \\\\n-...</td><td>false</td><td>c991b49673ab325aeec4244aea9e22845496a0a8:SPBS-RR/src/train.sh</td><td>36</td><td>aafe538f-e6b1-48ca-b7ac-5f22c5539532</td><td>thunlp/BabelNet-Sememe-Prediction</td><td>github/221930773</td><td>21873c1685da764dccbdb2377b449b5c4cbcff8a</td><td>256</td><td>Shell</td><td>false</td></tr>\n", "<tr><td>bn:00018050n\\t0\\nbn:00046456n\\t1\\nbn:00045122n\\t2\\nbn:00004556n\\t3\\nbn:00014160n\\t4\\nbn:00025419n...</td><td>false</td><td>c991b49673ab325aeec4244aea9e22845496a0a8:data-noun/entity2id.txt</td><td>100</td><td>aafe538f-e6b1-48ca-b7ac-5f22c5539532</td><td>thunlp/BabelNet-Sememe-Prediction</td><td>github/221930773</td><td>c5cc4d804b800d1ef0652b0c697d93b272b78be2</td><td>194001</td><td>Text</td><td>false</td></tr>\n", "<tr><td><PERSON>           STAT 453: Intro to Deep Learning and Generative Models            SS ...</td><td>false</td><td>L15-autoencoder/L15_autoencoder__slides.pdf</td><td>200</td><td>5e41421c-811b-4dd9-87df-4b6722558ae8</td><td>rasbt/stat453-deep-learning-ss20</td><td>github/235217328</td><td>277c9598e083b974837396e4b26452159ad6033d</td><td>128108</td><td>Pdf</td><td>true</td></tr>\n", "<tr><td><PERSON>           STAT 453: Intro to Deep Learning             SS 2020A Brief Summary o...</td><td>false</td><td>L02-dl-history/L02_dl-history_slides.pdf</td><td>200</td><td>5e41421c-811b-4dd9-87df-4b6722558ae8</td><td>rasbt/stat453-deep-learning-ss20</td><td>github/235217328</td><td>6da5d60927fa41990bb829627c3007583fc52256</td><td>35984</td><td>Pdf</td><td>true</td></tr>\n", "<tr><td>{\\n &quot;cells&quot;: [\\n  {\\n   &quot;cell_type&quot;: &quot;code&quot;,\\n   &quot;execution_count&quot;: 1,\\n   &quot;metadata&quot;: {},\\n   &quot;o...</td><td>false</td><td>L16-gan/code/08_dcgan-celeba.ipynb</td><td>200</td><td>5e41421c-811b-4dd9-87df-4b6722558ae8</td><td>rasbt/stat453-deep-learning-ss20</td><td>github/235217328</td><td>ce6834b2fda63e8ac2b9b4f78256d00754fb35f4</td><td>3216443</td><td>Jupyter Notebook</td><td>true</td></tr>\n", "<tr><td>MAX_LEN = 300\\r\\nneg_table_size = 1000000\\r\\nNEG_SAMPLE_POWER = 0.75\\r\\nbatch_size = 64\\r\\nnum_ep...</td><td>false</td><td>code/config.py</td><td>200</td><td>4a404e2c-fe25-4e34-860f-0d362d1ce70b</td><td>thunlp/CANE</td><td>github/88707705</td><td>4dac843a04ad674989e7499abf5c4f2ad57113e4</td><td>129</td><td>Python</td><td>true</td></tr>\n", "<tr><td>﻿using System.Collections.Generic;\\nusing BenchmarkDotNet.Extensions;\\nusing System;\\nusing Bench...</td><td>false</td><td>49495fafb183d02828b7059e9aaa04963e3a1211:src/BenchmarkDotNet/Validators/ConfigValidator.cs</td><td>16</td><td>403eab83-f512-40f2-b50b-ebbeae645fe6</td><td>dotnet/BenchmarkDotNet</td><td>github/12191244</td><td>c9f7fab7ed7fe2b6f59dbaa045d9cfe80f02829f</td><td>2739</td><td>C#</td><td>false</td></tr>\n", "<tr><td>﻿using System;\\nusing System.Collections.Generic;\\nusing System.Linq;\\nusing System.Runtime.Seria...</td><td>false</td><td>49495fafb183d02828b7059e9aaa04963e3a1211:src/BenchmarkDotNet/Mathematics/Statistics.cs</td><td>14</td><td>403eab83-f512-40f2-b50b-ebbeae645fe6</td><td>dotnet/BenchmarkDotNet</td><td>github/12191244</td><td>fbf0b470cbbff0c7938682ed7758892575063acc</td><td>5603</td><td>C#</td><td>false</td></tr>\n", "<tr><td>---\\nuid: docs.console-args\\nname: Console Arguments\\n---\\n\\n# How to use console arguments\\n\\n`B...</td><td>false</td><td>docs/articles/guides/console-args.md</td><td>200</td><td>403eab83-f512-40f2-b50b-ebbeae645fe6</td><td>dotnet/BenchmarkDotNet</td><td>github/12191244</td><td>951a789fa987c808c9458fefa33b0491a80db8a0</td><td>10388</td><td>Markdown</td><td>true</td></tr>\n", "<tr><td>using System.Linq;\\nusing BenchmarkDotNet.Extensions;\\nusing BenchmarkDotNet.Reports;\\nusing Benc...</td><td>false</td><td>src/BenchmarkDotNet/Columns/CategoriesColumn.cs</td><td>200</td><td>403eab83-f512-40f2-b50b-ebbeae645fe6</td><td>dotnet/BenchmarkDotNet</td><td>github/12191244</td><td>1b302434e0622dee802e51b5c00472590e5b2ee6</td><td>1216</td><td>C#</td><td>true</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+----------------------------------------------------------------------------------------------------+---------+------------------------------------------------------------------------------------------+------------+------------------------------------+---------------------------------+----------------+----------------------------------------+-------+----------------+-------+\n", "|                                                                                             content|is_binary|                                                                                      path|pct_modified|                             repo_id|                        repo_name|       repo_root|                                     sha|   size|            lang|is_head|\n", "+----------------------------------------------------------------------------------------------------+---------+------------------------------------------------------------------------------------------+------------+------------------------------------+---------------------------------+----------------+----------------------------------------+-------+----------------+-------+\n", "|import fs from 'fs'\\nimport path from 'path'\\nimport * as core from '@actions/core'\\nimport {Cont...|    false|                                                                    __tests__/main.test.ts|         200|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|52d2b3bcd5079457e63ae564ca6b0e98d7276d9b|  13739|      TypeScript|   true|\n", "|{\\n  \"name\": \"typescript-action\",\\n  \"version\": \"0.0.0\",\\n  \"lockfileVersion\": 2,\\n  \"requires\": ...|    false|                                d3d239647d3e4eb5886570adcd9258794ca40e37:package-lock.json|          55|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|8b01eeec68691d970af8774581a10a906b57e74c| 451514|            JSON|  false|\n", "|import * as core from '@actions/core'\\nimport Docker, {Container} from 'dockerode'\\nimport {Conta...|    false|                                                                    src/updater-builder.ts|         200|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|1d2f7eda7528f5d28e085efcf9097c194f33c1a6|   2895|      TypeScript|   true|\n", "|{\\n  \"name\": \"typescript-action\",\\n  \"version\": \"0.0.0\",\\n  \"lockfileVersion\": 2,\\n  \"requires\": ...|    false|                                c4de23772982d3d0cb810748c75da64429db85ad:package-lock.json|          25|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|58093bac7838211fe1dba5793a5bfd203ba2e7fc| 477334|            JSON|  false|\n", "|{\\n  \"name\": \"typescript-action\",\\n  \"version\": \"0.0.0\",\\n  \"lockfileVersion\": 2,\\n  \"requires\": ...|    false|                                56efa1411f3301486398d8d296f4cd4fc5cdc17e:package-lock.json|          55|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|581ccba882621554c63f985582b3fdfc1cae6197| 451558|            JSON|  false|\n", "|{\\n  \"name\": \"typescript-action\",\\n  \"version\": \"0.0.0\",\\n  \"lockfileVersion\": 2,\\n  \"requires\": ...|    false|                                e4375b7b7fb6075a2e3ffb8058c8b95da3d1f83e:package-lock.json|          76|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|eba8a3ba3bd6607032bf748ee1552b56ea16f2a4| 823264|            JSON|  false|\n", "|{\\n  \"name\": \"typescript-action\",\\n  \"version\": \"0.0.0\",\\n  \"private\": true,\\n  \"description\": \"T...|    false|                                     85a42505ba11446b6501d8395f4b564315a62172:package.json|          34|f7ecbaac-6b99-46a8-985e-8cea499ef376|         github/dependabot-action|github/413862514|049e1d814ca2b8ef1e6f0c8ac191458d92852605|   1166|            JSON|  false|\n", "|## Coordinate Ascent\\n## Restart = 5\\n## MaxIteration = 25\\n## StepBase = 0.05\\n## StepScale = 2....|    false|                                                            listsearch/transe_models/f5.ca|         200|1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb|  thunlp/EmbeddingEntityRetrieval|github/202404436|af72adb64e62e74e278275fff1a64d3832820c81|    804|                |   true|\n", "|0.0 qid:3 1:-7.097468 2:-37.596947 3:5.6967936 4:-37.678677 5:2.0 6:0.54719 7:-37.301388 8:6.9367...|    false|                                                                 listsearch/elr_svm/train4|         200|1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb|  thunlp/EmbeddingEntityRetrieval|github/202404436|dd7689d5a18f5d93cec89081338707bd4737b558|3265629|                |   true|\n", "|SemSearch_ES-12\\tQ0\\t<dbpedia:Austin%2C_Texas>\\t1\\t-1.3956818743426784\\tCA\\nSemSearch_ES-12\\tQ0\\t...|    false|                                                            semsearch/transe.shuffled.trec|         200|1a2a4753-f1ff-460c-ac4c-4b7bb35a76bb|  thunlp/EmbeddingEntityRetrieval|github/202404436|dd758858807d14573f4d0b1c4fad7b07edcde86f|1037317|                |   true|\n", "|#!/usr/bin/env bash\\nCUDA_VISIBLE_DEVICES=2 \\\\npython main.py \\\\n--data_dir ../../data-noun/ \\\\n-...|    false|                             c991b49673ab325aeec4244aea9e22845496a0a8:SPBS-RR/src/train.sh|          36|aafe538f-e6b1-48ca-b7ac-5f22c5539532|thunlp/BabelNet-Sememe-Prediction|github/221930773|21873c1685da764dccbdb2377b449b5c4cbcff8a|    256|           Shell|  false|\n", "|bn:00018050n\\t0\\nbn:00046456n\\t1\\nbn:00045122n\\t2\\nbn:00004556n\\t3\\nbn:00014160n\\t4\\nbn:00025419n...|    false|                          c991b49673ab325aeec4244aea9e22845496a0a8:data-noun/entity2id.txt|         100|aafe538f-e6b1-48ca-b7ac-5f22c5539532|thunlp/BabelNet-Sememe-Prediction|github/221930773|c5cc4d804b800d1ef0652b0c697d93b272b78be2| 194001|            Text|  false|\n", "|<PERSON>           STAT 453: Intro to Deep Learning and Generative Models            SS ...|    false|                                               L15-autoencoder/L15_autoencoder__slides.pdf|         200|5e41421c-811b-4dd9-87df-4b6722558ae8| rasbt/stat453-deep-learning-ss20|github/235217328|277c9598e083b974837396e4b26452159ad6033d| 128108|             Pdf|   true|\n", "|<PERSON>           STAT 453: Intro to Deep Learning             SS 2020A Brief Summary o...|    false|                                                  L02-dl-history/L02_dl-history_slides.pdf|         200|5e41421c-811b-4dd9-87df-4b6722558ae8| rasbt/stat453-deep-learning-ss20|github/235217328|6da5d60927fa41990bb829627c3007583fc52256|  35984|             Pdf|   true|\n", "|{\\n \"cells\": [\\n  {\\n   \"cell_type\": \"code\",\\n   \"execution_count\": 1,\\n   \"metadata\": {},\\n   \"o...|    false|                                                        L16-gan/code/08_dcgan-celeba.ipynb|         200|5e41421c-811b-4dd9-87df-4b6722558ae8| rasbt/stat453-deep-learning-ss20|github/235217328|ce6834b2fda63e8ac2b9b4f78256d00754fb35f4|3216443|Jupyter Notebook|   true|\n", "|MAX_LEN = 300\\r\\nneg_table_size = 1000000\\r\\nNEG_SAMPLE_POWER = 0.75\\r\\nbatch_size = 64\\r\\nnum_ep...|    false|                                                                            code/config.py|         200|4a404e2c-fe25-4e34-860f-0d362d1ce70b|                      thunlp/CANE| github/88707705|4dac843a04ad674989e7499abf5c4f2ad57113e4|    129|          Python|   true|\n", "|﻿using System.Collections.Generic;\\nusing BenchmarkDotNet.Extensions;\\nusing System;\\nusing Bench...|    false|49495fafb183d02828b7059e9aaa04963e3a1211:src/BenchmarkDotNet/Validators/ConfigValidator.cs|          16|403eab83-f512-40f2-b50b-ebbeae645fe6|           dotnet/BenchmarkDotNet| github/12191244|c9f7fab7ed7fe2b6f59dbaa045d9cfe80f02829f|   2739|              C#|  false|\n", "|﻿using System;\\nusing System.Collections.Generic;\\nusing System.Linq;\\nusing System.Runtime.Seria...|    false|    49495fafb183d02828b7059e9aaa04963e3a1211:src/BenchmarkDotNet/Mathematics/Statistics.cs|          14|403eab83-f512-40f2-b50b-ebbeae645fe6|           dotnet/BenchmarkDotNet| github/12191244|fbf0b470cbbff0c7938682ed7758892575063acc|   5603|              C#|  false|\n", "|---\\nuid: docs.console-args\\nname: Console Arguments\\n---\\n\\n# How to use console arguments\\n\\n`B...|    false|                                                      docs/articles/guides/console-args.md|         200|403eab83-f512-40f2-b50b-ebbeae645fe6|           dotnet/BenchmarkDotNet| github/12191244|951a789fa987c808c9458fefa33b0491a80db8a0|  10388|        Markdown|   true|\n", "|using System.Linq;\\nusing BenchmarkDotNet.Extensions;\\nusing BenchmarkDotNet.Reports;\\nusing Benc...|    false|                                           src/BenchmarkDotNet/Columns/CategoriesColumn.cs|         200|403eab83-f512-40f2-b50b-ebbeae645fe6|           dotnet/BenchmarkDotNet| github/12191244|1b302434e0622dee802e51b5c00472590e5b2ee6|   1216|              C#|   true|\n", "+----------------------------------------------------------------------------------------------------+---------+------------------------------------------------------------------------------------------+------------+------------------------------------+---------------------------------+----------------+----------------------------------------+-------+----------------+-------+\n", "only showing top 20 rows"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["path = 's3a://augment-github/{}/processed/day=2023-06-24/hour=16/'\n", "files = spark.read.parquet(path.format('file_content'))\n", "commits = spark.read.parquet(path.format('git_commit')).select('sha', 'message')\n", "patch = spark.read.parquet(path.format('patch')).select(\n", "    'commit_sha', 'old_file_sha', 'diff'\n", ")\n", "hunk = spark.read.parquet(path.format('hunk'))\n", "\n", "files"]}, {"cell_type": "markdown", "id": "ea9c22d1-6741-4f18-bc05-ec22a43a578f", "metadata": {}, "source": ["### Example: Diff patches along with old files"]}, {"cell_type": "code", "execution_count": 4, "id": "4a38e73c-3c59-43d6-90d1-5fbaaaad1c9b", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T08:26:50.637545Z", "iopub.status.busy": "2023-07-01T08:26:50.635362Z", "iopub.status.idle": "2023-07-01T08:26:57.398835Z", "shell.execute_reply": "2023-07-01T08:26:57.397303Z", "shell.execute_reply.started": "2023-07-01T08:26:50.637479Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["patch_with_files = patch.join(\n", "    commits, patch.commit_sha==commits.sha\n", ").join(\n", "    files, files.sha==patch.old_file_sha\n", ").filter(\n", "    <PERSON>.col('lang')=='Python'\n", ").select(\n", "    'repo_name', 'path', 'pct_modified', F.col('content').alias('old_file'), 'diff'\n", ").<PERSON><PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 5, "id": "f86f46e2-8f47-4c9a-93f8-e6cc1e51cff3", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T08:27:01.086155Z", "iopub.status.busy": "2023-07-01T08:27:01.085259Z", "iopub.status.idle": "2023-07-01T08:27:01.111773Z", "shell.execute_reply": "2023-07-01T08:27:01.109963Z", "shell.execute_reply.started": "2023-07-01T08:27:01.086099Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>path</th>\n", "      <th>pct_modified</th>\n", "      <th>old_file</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>thunlp/OpenMatch</td>\n", "      <td>728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenM...</td>\n", "      <td>39</td>\n", "      <td>from typing import Tuple\\r\\n\\r\\nimport torch\\r...</td>\n", "      <td>index 0282a2b..b488d23 100644\\n--- a/OpenMatch...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>talkpython/getting-started-with-pytest-course</td>\n", "      <td>90e2ca5c2c8e436701d7a6892548130fddea96a4:cards...</td>\n", "      <td>25</td>\n", "      <td>\"\"\"\\nAPI for the cards project\\n\"\"\"\\nfrom data...</td>\n", "      <td>index 02ffff7..ad6b4a4 100644\\n--- a/cards_pro...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>x4nth055/product_recommendation</td>\n", "      <td>9ff2d54183e63d5655afec6e0076708e04ba1920:app.py</td>\n", "      <td>25</td>\n", "      <td>import os\\r\\nimport random\\r\\nfrom flask impor...</td>\n", "      <td>index 031b183..3dfbab8 100644\\n--- a/app.py\\n+...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>x4nth055/emotion-recognition-using-speech</td>\n", "      <td>41a672d559d4797e43993672618b8c0a5a81f8b8:emoti...</td>\n", "      <td>46</td>\n", "      <td>from data_extractor import load_data\\nfrom uti...</td>\n", "      <td>index 060fb8a..8aff4f1 100644\\n--- a/emotion_r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>wesm/vbench</td>\n", "      <td>70dfc138dcaac302c45011b725b117ba91f4e9a8:vbenc...</td>\n", "      <td>40</td>\n", "      <td>import cPickle as pickle\\nimport os\\nimport su...</td>\n", "      <td>index 06b4f18..f5e3e1c 100644\\n--- a/vbench/ru...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2875</th>\n", "      <td>rasbt/scipy2022-talk</td>\n", "      <td>fba9260ebcd45a7158b5cc0c2bb64186b4aaa3c0:src/m...</td>\n", "      <td>25</td>\n", "      <td>import argparse\\nimport os\\nimport time\\n\\nimp...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2876</th>\n", "      <td>rasbt/scipy2022-talk</td>\n", "      <td>fba9260ebcd45a7158b5cc0c2bb64186b4aaa3c0:src/m...</td>\n", "      <td>25</td>\n", "      <td>import argparse\\nimport os\\nimport time\\n\\nimp...</td>\n", "      <td>index fd7b583..bec633f 100644\\n--- a/src/main_...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2877</th>\n", "      <td>x4nth055/emotion-recognition-using-speech</td>\n", "      <td>8daa0683e143e0ca5e06fe9ff24e8e22822dc157:deep_...</td>\n", "      <td>60</td>\n", "      <td>import os\\n# to use CPU uncomment below code\\n...</td>\n", "      <td>index fdb4b12..4b1cc04 100644\\n--- a/deep_emot...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2878</th>\n", "      <td>py-why/pywhy-graphs</td>\n", "      <td>62025ad126a4c536d21b5e25d2c5263b49e72b03:pywhy...</td>\n", "      <td>92</td>\n", "      <td>from pywhy_graphs import ADMG\\n\\n\\nclass TestA...</td>\n", "      <td>index ff01273..8f3d197 100644\\n--- a/pywhy_gra...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2879</th>\n", "      <td>py-why/pywhy-graphs</td>\n", "      <td>62025ad126a4c536d21b5e25d2c5263b49e72b03:pywhy...</td>\n", "      <td>92</td>\n", "      <td>from pywhy_graphs import ADMG\\n\\n\\nclass TestA...</td>\n", "      <td>index ff01273..8f3d197 100644\\n--- a/pywhy_gra...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2880 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                          repo_name  \\\n", "0                                  thunlp/OpenMatch   \n", "1     talkpython/getting-started-with-pytest-course   \n", "2                   x4nth055/product_recommendation   \n", "3         x4nth055/emotion-recognition-using-speech   \n", "4                                       wesm/vbench   \n", "...                                             ...   \n", "2875                           rasbt/scipy2022-talk   \n", "2876                           rasbt/scipy2022-talk   \n", "2877      x4nth055/emotion-recognition-using-speech   \n", "2878                            py-why/pywhy-graphs   \n", "2879                            py-why/pywhy-graphs   \n", "\n", "                                                   path  pct_modified  \\\n", "0     728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenM...            39   \n", "1     90e2ca5c2c8e436701d7a6892548130fddea96a4:cards...            25   \n", "2       9ff2d54183e63d5655afec6e0076708e04ba1920:app.py            25   \n", "3     41a672d559d4797e43993672618b8c0a5a81f8b8:emoti...            46   \n", "4     70dfc138dcaac302c45011b725b117ba91f4e9a8:vbenc...            40   \n", "...                                                 ...           ...   \n", "2875  fba9260ebcd45a7158b5cc0c2bb64186b4aaa3c0:src/m...            25   \n", "2876  fba9260ebcd45a7158b5cc0c2bb64186b4aaa3c0:src/m...            25   \n", "2877  8daa0683e143e0ca5e06fe9ff24e8e22822dc157:deep_...            60   \n", "2878  62025ad126a4c536d21b5e25d2c5263b49e72b03:pywhy...            92   \n", "2879  62025ad126a4c536d21b5e25d2c5263b49e72b03:pywhy...            92   \n", "\n", "                                               old_file  \\\n", "0     from typing import Tuple\\r\\n\\r\\nimport torch\\r...   \n", "1     \"\"\"\\nAPI for the cards project\\n\"\"\"\\nfrom data...   \n", "2     import os\\r\\nimport random\\r\\nfrom flask impor...   \n", "3     from data_extractor import load_data\\nfrom uti...   \n", "4     import cPickle as pickle\\nimport os\\nimport su...   \n", "...                                                 ...   \n", "2875  import argparse\\nimport os\\nimport time\\n\\nimp...   \n", "2876  import argparse\\nimport os\\nimport time\\n\\nimp...   \n", "2877  import os\\n# to use CPU uncomment below code\\n...   \n", "2878  from pywhy_graphs import ADMG\\n\\n\\nclass TestA...   \n", "2879  from pywhy_graphs import ADMG\\n\\n\\nclass TestA...   \n", "\n", "                                                   diff  \n", "0     index 0282a2b..b488d23 100644\\n--- a/OpenMatch...  \n", "1     index 02ffff7..ad6b4a4 100644\\n--- a/cards_pro...  \n", "2     index 031b183..3dfbab8 100644\\n--- a/app.py\\n+...  \n", "3     index 060fb8a..8aff4f1 100644\\n--- a/emotion_r...  \n", "4     index 06b4f18..f5e3e1c 100644\\n--- a/vbench/ru...  \n", "...                                                 ...  \n", "2875                                               None  \n", "2876  index fd7b583..bec633f 100644\\n--- a/src/main_...  \n", "2877  index fdb4b12..4b1cc04 100644\\n--- a/deep_emot...  \n", "2878  index ff01273..8f3d197 100644\\n--- a/pywhy_gra...  \n", "2879  index ff01273..8f3d197 100644\\n--- a/pywhy_gra...  \n", "\n", "[2880 rows x 5 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["patch_with_files"]}, {"cell_type": "markdown", "id": "e02036d3-b784-4956-8b5a-07e322745b2d", "metadata": {}, "source": ["### Example: get each hunks with some lines of the old files associated with it"]}, {"cell_type": "code", "execution_count": 6, "id": "49c493fd-44f4-496a-b7fd-125b5187f8da", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T09:12:27.102434Z", "iopub.status.busy": "2023-07-01T09:12:27.101784Z", "iopub.status.idle": "2023-07-01T09:12:35.769613Z", "shell.execute_reply": "2023-07-01T09:12:35.768114Z", "shell.execute_reply.started": "2023-07-01T09:12:27.102384Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>repo_name</th><th>path</th><th>old_start</th><th>old_lines</th><th>new_start</th><th>new_lines</th><th>lines</th><th>old_file_seg</th></tr>\n", "<tr><td>thunlp/OpenMatch</td><td>728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenMatch/models/bert.py</td><td>26</td><td>5</td><td>28</td><td>10</td><td>[{\\r\\n,  }, {    def forward(self, input_ids: torch.Tensor, input_mask: torch.Tensor = None, segm...</td><td>        self._task = task\\r\\n\\r\\n        self._config = AutoConfig.from_pretrained(self._pretrain...</td></tr>\n", "<tr><td>thunlp/OpenMatch</td><td>728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenMatch/models/bert.py</td><td>9</td><td>10</td><td>9</td><td>12</td><td>[{    def __init__(\\r\\n,  }, {        self,\\r\\n,  }, {        pretrained: str,\\r\\n,  }, {        ...</td><td></td></tr>\n", "<tr><td>talkpython/getting-started-with-pytest-course</td><td>90e2ca5c2c8e436701d7a6892548130fddea96a4:cards_proj/src/cards/api.py</td><td>44</td><td>7</td><td>44</td><td>7</td><td>[{\\n,  }, {\\n,  }, {class CardsDB:\\n,  }, {    def __init__(self, db_path):\\n, -}, {    def __ini...</td><td>class CardsException(Exception):\\n    pass\\n\\n\\nclass MissingSummary(CardsException):\\n    pass\\n...</td></tr>\n", "<tr><td>x4nth055/product_recommendation</td><td>9ff2d54183e63d5655afec6e0076708e04ba1920:app.py</td><td>49</td><td>7</td><td>49</td><td>7</td><td>[{                            enumerate=enumerate)\\r\\n,  }, {    \\r\\n,  }, {\\r\\n,  }, {@app.route...</td><td>    sorted_products = products.copy()\\r\\n    random.shuffle(products)\\r\\n    tags = get_product_t...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>38</td><td>38</td><td>36</td><td>43</td><td>[{            emodb (bool): whether to use EMO-DB Speech dataset, default is True,\\n,  }, {      ...</td><td>    def __init__(self, model, emotions=[&quot;sad&quot;, &quot;neutral&quot;, &quot;happy&quot;], tess_ravdess=True, emodb=True...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>182</td><td>7</td><td>201</td><td>7</td><td>[{        will be ready for testing/predicting.\\n,  }, {        In case of regression, the metric...</td><td>        grid = GridSearchCV(estimator=self.model, param_grid=params, scoring=make_scorer(score),\\...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>152</td><td>10</td><td>166</td><td>15</td><td>[{                print(&quot;[+] Model trained&quot;)\\n,  }, {\\n,  }, {    def predict(self, audio_path):\\...</td><td>            self.data_loaded = True\\n\\n    def train(self, verbose=1):\\n        if not self.data_...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>333</td><td>6</td><td>360</td><td>12</td><td>[{\\n,  }, {\\n,  }, {def get_best_estimators(classification):\\n,  }, {    &quot;&quot;&quot;\\n, +}, {    Loads th...</td><td>                index = random.choice(list(range(len(self.y_train))))\\n        elif partition == ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>90</td><td>14</td><td>93</td><td>17</td><td>[{            train_desc_files.append(f&quot;train_{self.emodb_name}&quot;)\\n,  }, {            test_desc_f...</td><td>        self.data_loaded = False\\n        self.model_trained = False\\n\\n    def _set_metadata_fil...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>114</td><td>20</td><td>120</td><td>25</td><td>[{            # not safe approach\\n,  }, {            if os.path.isfile(train_csv_file) and os.pa...</td><td>    def get_best_estimators(self):\\n        &quot;&quot;&quot;Loads estimators from grid files and returns them&quot;...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>142</td><td>6</td><td>153</td><td>9</td><td>[{            self.data_loaded = True\\n,  }, {\\n,  }, {    def train(self, verbose=1):\\n,  }, {  ...</td><td>            result = load_data(self.train_desc_files, self.test_desc_files, self.audio_config, se...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>25</td><td>9</td><td>25</td><td>7</td><td>[{class EmotionRecognizer:\\n,  }, {    &quot;&quot;&quot;A class for training, testing and predicting emotions b...</td><td>import pickle\\nimport matplotlib.pyplot as pl\\nfrom time import time\\nimport numpy as np\\nimport ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>199</td><td>7</td><td>218</td><td>7</td><td>[{            if self.verbose:\\n,  }, {                estimators.set_description(f&quot;Evaluating {e...</td><td>        \\n        # loads estimators\\n        estimators = self.get_best_estimators()\\n\\n        ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>262</td><td>7</td><td>281</td><td>12</td><td>[{        return fbeta_score(self.y_test, y_pred, beta, average=&#x27;micro&#x27;)\\n,  }, {\\n,  }, {    def...</td><td>            return accuracy_score(y_true=self.y_train, y_pred=y_pred)\\n        else:\\n           ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>410</td><td>7</td><td>443</td><td>6</td><td>[{      - n_classes: number of classes\\n,  }, {    &quot;&quot;&quot;\\n,  }, {\\n,  }, {\\n, -}, {    n_estimators...</td><td>    visualize(final_result, n_classes=n_classes)\\n    \\n\\n\\ndef visualize(results, n_classes):\\n ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>341</td><td>7</td><td>374</td><td>7</td><td>[{\\n,  }, {def plot_histograms(classifiers=True, beta=0.5, n_classes=3, verbose=1):\\n,  }, {    &quot;...</td><td>        return index\\n\\n\\n\\ndef get_best_estimators(classification):\\n    if classification:\\n   ...</td></tr>\n", "<tr><td>x4nth055/emotion-recognition-using-speech</td><td>41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py</td><td>294</td><td>9</td><td>318</td><td>12</td><td>[{\\n,  }, {    def get_samples_by_class(self):\\n,  }, {        &quot;&quot;&quot;\\n,  }, {        Returns all da...</td><td>        pl.show()\\n\\n    def n_emotions(self, emotion, partition):\\n        &quot;&quot;&quot;Returns number of ...</td></tr>\n", "<tr><td>wesm/vbench</td><td>70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py</td><td>86</td><td>6</td><td>92</td><td>8</td><td>[{            return {}\\n,  }, {\\n,  }, {        print &#x27;Running %d benchmarks for revision %s&#x27; % ...</td><td>            if bm.checksum in db_checksums:\\n                continue\\n            print &#x27;Writing...</td></tr>\n", "<tr><td>wesm/vbench</td><td>70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py</td><td>21</td><td>13</td><td>21</td><td>15</td><td>[{        all: benchmark every revision\\n,  }, {        some integer N: run each N revisions\\n,  ...</td><td>    &quot;&quot;&quot;\\n\\n    Parameters\\n    ----------\\n    benchmarks : list of Benchmark objects\\n    repo_p...</td></tr>\n", "<tr><td>wesm/vbench</td><td>70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py</td><td>59</td><td>7</td><td>61</td><td>9</td><td>[{                if &#x27;traceback&#x27; in timing:\\n,  }, {                    tracebacks.append(timing[...</td><td>        self._register_benchmarks()\\n\\n    def run(self):\\n        revisions = self._get_revision...</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+---------------------------------------------+--------------------------------------------------------------------+---------+---------+---------+---------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|                                    repo_name|                                                                path|old_start|old_lines|new_start|new_lines|                                                                                               lines|                                                                                        old_file_seg|\n", "+---------------------------------------------+--------------------------------------------------------------------+---------+---------+---------+---------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|                             thunlp/OpenMatch|   728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenMatch/models/bert.py|       26|        5|       28|       10|[{\\r\\n,  }, {    def forward(self, input_ids: torch.Tensor, input_mask: torch.Tensor = None, segm...|        self._task = task\\r\\n\\r\\n        self._config = AutoConfig.from_pretrained(self._pretrain...|\n", "|                             thunlp/OpenMatch|   728ca9f51bbad7f9d088a1d040ede5b4ab1d4030:OpenMatch/models/bert.py|        9|       10|        9|       12|[{    def __init__(\\r\\n,  }, {        self,\\r\\n,  }, {        pretrained: str,\\r\\n,  }, {        ...|                                                                                                    |\n", "|talkpython/getting-started-with-pytest-course|90e2ca5c2c8e436701d7a6892548130fddea96a4:cards_proj/src/cards/api.py|       44|        7|       44|        7|[{\\n,  }, {\\n,  }, {class CardsDB:\\n,  }, {    def __init__(self, db_path):\\n, -}, {    def __ini...|class CardsException(Exception):\\n    pass\\n\\n\\nclass MissingSummary(CardsException):\\n    pass\\n...|\n", "|              x4nth055/product_recommendation|                     9ff2d54183e63d5655afec6e0076708e04ba1920:app.py|       49|        7|       49|        7|[{                            enumerate=enumerate)\\r\\n,  }, {    \\r\\n,  }, {\\r\\n,  }, {@app.route...|    sorted_products = products.copy()\\r\\n    random.shuffle(products)\\r\\n    tags = get_product_t...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      410|        7|      443|        6|[{      - n_classes: number of classes\\n,  }, {    \"\"\"\\n,  }, {\\n,  }, {\\n, -}, {    n_estimators...|    visualize(final_result, n_classes=n_classes)\\n    \\n\\n\\ndef visualize(results, n_classes):\\n ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      341|        7|      374|        7|[{\\n,  }, {def plot_histograms(classifiers=True, beta=0.5, n_classes=3, verbose=1):\\n,  }, {    \"...|        return index\\n\\n\\n\\ndef get_best_estimators(classification):\\n    if classification:\\n   ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      294|        9|      318|       12|[{\\n,  }, {    def get_samples_by_class(self):\\n,  }, {        \"\"\"\\n,  }, {        Returns all da...|        pl.show()\\n\\n    def n_emotions(self, emotion, partition):\\n        \"\"\"Returns number of ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|       38|       38|       36|       43|[{            emodb (bool): whether to use EMO-DB Speech dataset, default is True,\\n,  }, {      ...|    def __init__(self, model, emotions=[\"sad\", \"neutral\", \"happy\"], tess_ravdess=True, emodb=True...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      182|        7|      201|        7|[{        will be ready for testing/predicting.\\n,  }, {        In case of regression, the metric...|        grid = GridSearchCV(estimator=self.model, param_grid=params, scoring=make_scorer(score),\\...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      152|       10|      166|       15|[{                print(\"[+] Model trained\")\\n,  }, {\\n,  }, {    def predict(self, audio_path):\\...|            self.data_loaded = True\\n\\n    def train(self, verbose=1):\\n        if not self.data_...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      333|        6|      360|       12|[{\\n,  }, {\\n,  }, {def get_best_estimators(classification):\\n,  }, {    \"\"\"\\n, +}, {    Loads th...|                index = random.choice(list(range(len(self.y_train))))\\n        elif partition == ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      114|       20|      120|       25|[{            # not safe approach\\n,  }, {            if os.path.isfile(train_csv_file) and os.pa...|    def get_best_estimators(self):\\n        \"\"\"Loads estimators from grid files and returns them\"...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      199|        7|      218|        7|[{            if self.verbose:\\n,  }, {                estimators.set_description(f\"Evaluating {e...|        \\n        # loads estimators\\n        estimators = self.get_best_estimators()\\n\\n        ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      262|        7|      281|       12|[{        return fbeta_score(self.y_test, y_pred, beta, average='micro')\\n,  }, {\\n,  }, {    def...|            return accuracy_score(y_true=self.y_train, y_pred=y_pred)\\n        else:\\n           ...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|       90|       14|       93|       17|[{            train_desc_files.append(f\"train_{self.emodb_name}\")\\n,  }, {            test_desc_f...|        self.data_loaded = False\\n        self.model_trained = False\\n\\n    def _set_metadata_fil...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|      142|        6|      153|        9|[{            self.data_loaded = True\\n,  }, {\\n,  }, {    def train(self, verbose=1):\\n,  }, {  ...|            result = load_data(self.train_desc_files, self.test_desc_files, self.audio_config, se...|\n", "|    x4nth055/emotion-recognition-using-speech|     41a672d559d4797e43993672618b8c0a5a81f8b8:emotion_recognition.py|       25|        9|       25|        7|[{class EmotionRecognizer:\\n,  }, {    \"\"\"A class for training, testing and predicting emotions b...|import pickle\\nimport matplotlib.pyplot as pl\\nfrom time import time\\nimport numpy as np\\nimport ...|\n", "|                                  wesm/vbench|           70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py|       21|       13|       21|       15|[{        all: benchmark every revision\\n,  }, {        some integer N: run each N revisions\\n,  ...|    \"\"\"\\n\\n    Parameters\\n    ----------\\n    benchmarks : list of Benchmark objects\\n    repo_p...|\n", "|                                  wesm/vbench|           70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py|       44|        8|       46|        8|[{        # where to copy the repo\\n,  }, {        self.tmp_dir = tmp_dir\\n,  }, {        self.be...|\\n        self.start_date = start_date\\n        self.run_option = run_option\\n\\n        self.repo...|\n", "|                                  wesm/vbench|           70dfc138dcaac302c45011b725b117ba91f4e9a8:vbench/runner.py|       86|        6|       92|        8|[{            return {}\\n,  }, {\\n,  }, {        print 'Running %d benchmarks for revision %s' % ...|            if bm.checksum in db_checksums:\\n                continue\\n            print 'Writing...|\n", "+---------------------------------------------+--------------------------------------------------------------------+---------+---------+---------+---------+----------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "only showing top 20 rows"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["hunk.join(\n", "    commits, hunk.commit_sha==commits.sha\n", ").join(\n", "    files, files.sha==hunk.old_file_sha\n", ").filter(\n", "    <PERSON>.col('lang')=='Python'\n", ").select(\n", "    'repo_name', 'path',\n", "    'old_start', 'old_lines', 'new_start', 'new_lines',\n", "    'lines',\n", "    F.concat_ws('\\n', \n", "        <PERSON><PERSON>slice(\n", "            <PERSON><PERSON>split('content', '\\n'), <PERSON><PERSON>col('old_start')-10, <PERSON><PERSON>col('old_lines')+20\n", "        )\n", "    ).alias('old_file_seg'),\n", ")"]}, {"cell_type": "markdown", "id": "159b8650-eda5-4097-b8f8-83fbba0815c7", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T08:39:59.459934Z", "iopub.status.busy": "2023-07-01T08:39:59.459104Z", "iopub.status.idle": "2023-07-01T08:40:00.865105Z", "shell.execute_reply": "2023-07-01T08:40:00.863577Z", "shell.execute_reply.started": "2023-07-01T08:39:59.459881Z"}, "tags": []}, "source": ["### Example: get job stats\n", "\n", "The most basic stats can be pulled from the `batch_job` table.   The `name` indicates what job we are \n", "looking at, and the `partition_name` column indicates which specific run this row correspond to.\n", "\n", "Here show number of new repos discovered for each day's GH archive dump"]}, {"cell_type": "code", "execution_count": 8, "id": "446a76ef-e660-4ede-98c9-847a19845bd5", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T09:17:47.372231Z", "iopub.status.busy": "2023-07-01T09:17:47.371576Z", "iopub.status.idle": "2023-07-01T09:17:47.605244Z", "shell.execute_reply": "2023-07-01T09:17:47.603644Z", "shell.execute_reply.started": "2023-07-01T09:17:47.372184Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-06-28 771714\n", "2023-06-27 492976\n", "2023-06-26 424564\n", "2023-06-25 314195\n", "2023-06-24 268277\n", "2023-06-23 344963\n", "2023-06-22 338583\n", "2023-06-21 356801\n", "2023-06-29 309595\n", "2023-06-20 333905\n", "2023-06-19 301372\n", "2023-06-18 240446\n", "2023-06-17 228897\n", "2023-06-16 287689\n", "2023-06-15 301391\n", "2023-06-14 331375\n", "2023-06-13 319688\n", "2023-06-12 292205\n", "2023-06-11 217079\n", "2023-06-10 216352\n", "2023-06-09 268649\n", "2023-06-08 265344\n", "2023-06-07 271742\n", "2023-06-06 272100\n", "2023-06-05 265527\n", "2023-06-04 207252\n", "2023-06-03 194776\n", "2023-06-02 248252\n", "2023-06-01 278792\n", "2023-05-31 276255\n", "2023-05-30 265274\n", "2023-05-29 258595\n", "2023-05-28 194625\n", "2023-05-27 186711\n", "2023-05-26 248485\n", "2023-05-25 254226\n", "2023-05-24 263502\n", "2023-05-23 266243\n", "2023-05-22 279061\n", "2023-05-21 193932\n", "2023-05-20 199285\n", "2023-05-19 250404\n", "2023-05-18 241384\n", "2023-05-17 262510\n", "2023-05-16 259569\n", "2023-05-15 239596\n", "2023-05-13 179271\n", "2023-05-12 228296\n", "2023-05-11 244960\n", "2023-05-10 247378\n", "2023-05-09 259104\n", "2023-05-08 240085\n", "2023-05-07 205552\n", "2023-05-06 189139\n", "2023-05-05 231341\n", "2023-05-04 239404\n", "2023-05-03 237141\n", "2023-05-02 235433\n", "2023-06-30 230885\n", "2023-05-01 227281\n", "2023-04-30 177735\n", "2023-04-29 175929\n", "2023-04-28 219572\n", "2023-04-27 246380\n", "2023-05-14 24511\n", "2023-04-26 237991\n", "2023-04-25 252774\n", "2023-04-24 231065\n", "2023-04-23 179859\n", "2023-04-22 166045\n", "2023-04-21 232828\n", "2023-04-20 238727\n", "2023-04-19 240309\n", "2023-04-18 256742\n", "2023-04-17 237857\n", "2023-04-16 180804\n", "2023-04-15 178378\n", "2023-04-14 222710\n", "2023-04-13 240768\n", "2023-04-12 386534\n", "2023-04-11 248345\n", "2023-04-10 231724\n", "2023-04-09 174592\n", "2023-04-08 176486\n", "2023-04-07 197108\n", "2023-04-06 232874\n", "2023-04-05 234097\n", "2023-04-04 276901\n", "2023-04-03 248469\n", "2023-04-02 182189\n", "2023-04-01 188647\n", "2023-03-31 225939\n", "2023-03-30 236108\n", "2023-03-29 243964\n", "2023-03-28 235794\n", "2023-03-27 233879\n", "2023-03-26 184305\n", "2023-03-25 182888\n", "2023-03-24 225409\n", "2023-03-23 224582\n", "2023-03-22 230048\n", "2023-03-21 231349\n", "2023-03-20 234296\n", "2023-03-19 180086\n", "2023-03-18 173907\n", "2023-03-17 218766\n", "2023-03-16 271535\n", "2023-03-15 255432\n", "2023-03-14 249250\n", "2023-03-13 242319\n", "2023-03-12 185418\n", "2023-03-11 173020\n", "2023-03-10 299153\n", "2023-03-09 321415\n", "2023-03-08 478872\n", "2023-03-07 511851\n", "2023-03-06 251612\n", "2023-03-05 429172\n", "2023-03-04 448698\n", "2023-03-03 284994\n", "2023-03-02 248564\n", "2023-03-01 265481\n", "2023-02-28 252348\n", "2023-02-27 231186\n", "2023-02-26 179348\n", "2023-02-25 180878\n", "2023-02-24 216812\n", "2023-02-23 233040\n", "2023-02-22 228633\n", "2023-02-21 238370\n", "2023-02-20 217788\n", "2023-02-19 181904\n", "2023-02-18 162736\n", "2023-02-17 212739\n", "2023-02-16 229657\n", "2023-02-15 227555\n", "2023-02-14 228128\n", "2023-02-13 221830\n", "2023-02-12 171090\n", "2023-02-11 170388\n", "2023-02-10 210219\n", "2023-02-09 237176\n", "2023-02-08 226707\n", "2023-02-07 244970\n", "2023-02-06 222588\n", "2023-02-05 168788\n", "2023-02-04 180293\n", "2023-02-03 211931\n", "2023-02-02 265318\n", "2023-02-01 253807\n", "2023-01-31 226246\n", "2023-01-30 224946\n", "2023-01-29 166709\n", "2023-01-28 158557\n", "2023-01-27 201433\n", "2023-07-01 170114\n", "2023-01-26 241496\n", "2023-01-25 211659\n", "2023-01-24 267634\n", "2023-01-23 196361\n", "2023-01-22 160432\n", "2023-01-21 148177\n", "2023-01-20 189616\n", "2023-01-19 331976\n", "2023-01-18 207094\n", "2023-01-17 213311\n", "2023-01-16 195266\n", "2023-01-15 159135\n", "2023-01-14 151127\n", "2023-01-13 190285\n", "2023-01-12 215900\n", "2023-01-11 242591\n", "2023-01-10 211543\n", "2023-01-09 221833\n", "2023-01-08 157300\n", "2023-01-07 398162\n", "2023-01-06 321253\n", "2023-01-05 442996\n", "2023-01-04 390574\n", "2023-01-03 244316\n", "2023-01-02 159729\n", "2023-01-01 118579\n", "2022-12-31 125986\n", "2022-12-30 176872\n", "2022-12-29 170935\n", "2022-12-28 171369\n", "2022-12-27 168051\n", "2022-12-26 158278\n", "2022-12-25 112562\n", "2022-12-24 125580\n", "2022-12-23 165552\n", "2022-12-22 241315\n", "2022-12-21 191530\n", "2022-12-20 186416\n", "2022-12-19 191944\n", "2022-12-18 146867\n", "2022-12-17 146115\n", "2022-12-16 253760\n", "2022-12-15 220521\n", "2022-12-14 224621\n", "2022-12-13 278467\n", "2022-12-12 324068\n", "2022-12-11 322003\n", "2022-12-10 294398\n", "2022-12-09 240808\n", "2022-12-08 378143\n", "2022-12-07 268451\n", "2022-12-06 255745\n", "2022-12-05 238809\n", "2022-12-04 188513\n", "2022-12-03 167837\n", "2022-12-02 202953\n", "2022-12-01 213458\n", "2022-11-30 220694\n", "2022-11-29 210278\n", "2022-11-28 218707\n", "2022-11-27 161735\n", "2022-11-26 152276\n", "2022-11-25 190735\n", "2022-11-24 198269\n", "2022-11-23 209806\n", "2022-11-22 232708\n", "2022-11-21 221359\n", "2022-11-20 154780\n", "2022-11-19 149209\n", "2022-11-18 194190\n", "2022-11-17 212764\n", "2022-11-16 236518\n", "2022-11-15 None\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-15 None\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-15 98451\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-14 None\n", "2022-11-14 170096\n", "2022-11-13 154833\n", "2022-11-12 153369\n", "2022-11-11 198281\n", "2022-11-10 212958\n", "2022-11-09 216666\n", "2022-11-08 218856\n", "2022-11-07 218007\n", "2022-11-06 167150\n", "2022-11-05 162778\n", "2022-11-04 201647\n", "2022-11-03 202613\n", "2022-11-02 209505\n", "2022-11-01 198322\n", "2022-10-31 228775\n", "2022-10-30 177221\n", "2022-10-29 161992\n", "2022-10-28 202692\n", "2022-10-27 241255\n", "2022-10-26 283821\n", "2022-10-25 288722\n", "2022-10-24 280374\n", "2023-07-02 163312\n", "2022-10-23 260471\n", "2022-10-22 261859\n", "2022-10-21 282310\n", "2022-10-20 319956\n", "2022-10-19 339263\n", "2022-10-18 362026\n", "2022-10-17 369599\n", "2022-10-16 278423\n", "2022-10-15 266513\n", "2022-10-14 301647\n", "2022-10-13 348152\n", "2022-10-12 359061\n", "2022-10-11 360213\n", "2022-10-10 390283\n", "2022-10-09 352641\n", "2022-10-08 348861\n", "2022-10-07 389302\n", "2022-10-06 370341\n", "2022-10-05 346205\n", "2022-10-04 318523\n", "2022-10-03 316438\n", "2022-10-02 262638\n", "2022-10-01 217357\n", "2022-09-30 261338\n", "2022-09-29 275442\n", "2022-09-28 253532\n", "2022-09-27 240926\n", "2022-09-26 224570\n", "2022-09-25 182675\n", "2022-09-24 180041\n", "2022-09-23 221436\n", "2022-09-22 243651\n", "2022-09-21 224366\n", "2022-09-20 232183\n", "2022-09-19 208147\n", "2022-09-18 158576\n", "2022-09-17 146793\n", "2022-09-16 189585\n", "2022-09-15 203281\n", "2022-09-14 188551\n", "2022-09-13 213900\n", "2022-09-12 192188\n", "2022-09-11 153985\n", "2022-09-10 151810\n", "2022-09-09 None\n", "2022-09-08 None\n", "2022-09-08 None\n", "2022-09-08 None\n", "2022-09-08 None\n", "2022-09-08 None\n", "2022-09-09 None\n", "2022-09-09 None\n", "2022-09-08 None\n", "2022-09-09 None\n", "2022-09-08 None\n", "2022-09-09 None\n", "2022-09-08 None\n", "2022-09-09 None\n", "2022-09-09 None\n", "2022-09-08 105367\n", "2022-09-09 None\n", "2022-09-09 75104\n", "2022-09-07 201837\n", "2022-09-06 202646\n", "2022-09-05 200695\n", "2022-09-04 141742\n", "2022-09-03 141182\n", "2022-09-02 182077\n", "2022-09-01 205304\n", "2022-08-31 208797\n", "2022-08-30 193788\n", "2022-08-29 202456\n", "2022-08-28 143803\n", "2022-08-27 137495\n", "2022-08-26 182384\n", "2022-08-25 196692\n", "2022-08-24 206415\n", "2022-08-23 201070\n", "2022-08-22 192643\n", "2022-08-21 144533\n", "2022-08-20 137085\n", "2022-08-19 172998\n", "2022-08-18 184502\n", "2022-08-17 196011\n", "2022-08-16 190415\n", "2022-08-15 171064\n", "2022-08-14 128973\n", "2022-08-13 122631\n", "2022-08-12 159586\n", "2022-08-11 168771\n", "2022-08-10 183848\n", "2022-08-09 183099\n", "2022-08-08 182609\n", "2022-08-07 138195\n", "2022-08-06 133296\n", "2022-08-05 169644\n", "2022-08-04 170489\n", "2022-08-03 182035\n", "2022-08-02 176419\n", "2022-08-01 175259\n", "2022-07-31 132291\n", "2022-07-30 122504\n", "2022-07-29 168511\n", "2022-07-28 171625\n", "2022-07-27 193312\n", "2022-07-26 184331\n", "2022-07-25 181240\n", "2022-07-24 136862\n", "2022-07-23 128792\n", "2022-07-22 171597\n", "2022-07-21 195088\n", "2022-07-20 175837\n", "2022-07-19 186773\n", "2022-07-18 171339\n", "2023-07-03 201691\n", "2022-07-17 136652\n", "2022-07-16 131588\n", "2022-07-15 160903\n", "2022-07-14 171053\n", "2022-07-13 167172\n", "2022-07-12 167800\n", "2022-07-11 164859\n", "2022-07-10 120294\n", "2022-07-09 124543\n", "2022-07-08 154284\n", "2022-07-07 172751\n", "2022-07-06 180748\n", "2022-07-05 171152\n", "2022-07-04 174839\n", "2022-07-03 139319\n", "2022-07-02 127827\n", "2022-07-01 162798\n", "2022-06-30 173124\n", "2022-06-29 192944\n", "2022-06-28 167455\n", "2022-06-27 176734\n", "2022-06-26 150955\n", "2022-06-25 133884\n", "2022-06-24 161995\n", "2022-06-23 174749\n", "2022-06-22 201465\n", "2022-06-21 242570\n", "2022-06-20 187230\n", "2022-06-19 137449\n", "2022-06-18 130555\n", "2022-06-17 191881\n", "2022-06-16 168935\n", "2022-06-15 181074\n", "2022-06-14 176417\n", "2022-06-13 175306\n", "2022-06-12 136096\n", "2022-06-11 127768\n", "2022-06-10 166698\n", "2022-06-09 175110\n", "2022-06-08 182796\n", "2022-06-07 172145\n", "2022-06-06 185592\n", "2022-06-05 139763\n", "2022-06-04 134104\n", "2022-06-03 205406\n", "2022-06-02 180470\n", "2022-06-01 170260\n", "2022-05-31 178109\n", "2022-05-30 186690\n", "2022-05-29 142297\n", "2022-05-28 138934\n", "2022-05-27 162702\n", "2022-05-26 184096\n", "2022-05-25 182551\n", "2022-05-24 181858\n", "2022-05-23 182060\n", "2022-05-22 138302\n", "2022-05-21 133146\n", "2022-05-20 183706\n", "2022-05-19 181797\n", "2022-05-18 179185\n", "2022-05-17 183413\n", "2022-05-16 178291\n", "2022-05-15 137455\n", "2022-05-14 130399\n", "2022-05-13 159156\n", "2022-05-12 180983\n", "2022-05-11 175841\n", "2022-05-10 179962\n", "2022-05-09 170250\n", "2022-05-08 129829\n", "2022-05-07 130598\n", "2022-05-06 147996\n", "2022-05-05 174867\n", "2022-05-04 166469\n", "2022-05-03 163334\n", "2022-05-02 158591\n", "2022-05-01 123596\n", "2022-04-30 124625\n", "2022-04-29 149864\n", "2022-04-28 190245\n", "2022-04-27 177580\n", "2022-04-26 172560\n", "2022-04-25 181964\n", "2022-04-24 133170\n", "2022-04-23 125622\n", "2022-04-22 172158\n", "2022-04-21 168560\n", "2022-04-20 177745\n", "2022-04-19 173340\n", "2022-04-18 175442\n", "2022-04-17 128397\n", "2022-04-16 122052\n", "2022-04-15 144633\n", "2022-04-14 156438\n", "2022-04-13 174321\n", "2022-04-12 175252\n", "2022-04-11 184281\n", "2022-04-10 139064\n", "2022-04-09 133141\n", "2022-04-08 168480\n", "2022-04-07 172564\n", "2022-04-06 182456\n", "2022-04-05 177643\n", "2023-07-04 211657\n", "2022-04-04 173528\n", "2022-04-03 135760\n", "2022-04-02 130560\n", "2022-04-01 166747\n", "2022-03-31 171430\n", "2022-03-30 179291\n", "2022-03-29 169322\n", "2022-03-28 176614\n", "2022-03-27 135904\n", "2022-03-26 143364\n", "2022-03-25 159922\n", "2022-03-24 167943\n", "2022-03-23 175040\n", "2022-03-22 165350\n", "2022-03-21 167598\n", "2022-03-20 130659\n", "2022-03-19 124927\n", "2022-03-18 153016\n", "2022-03-17 153677\n", "2022-03-16 173727\n", "2022-03-15 166693\n", "2022-03-14 168667\n", "2022-03-13 131194\n", "2022-03-12 124434\n", "2022-03-11 161948\n", "2022-03-10 157483\n", "2022-03-09 173590\n", "2022-03-08 165876\n", "2022-03-07 160590\n", "2022-03-06 129256\n", "2022-03-05 123036\n", "2022-03-04 155522\n", "2022-03-03 161962\n", "2022-03-02 151054\n", "2022-03-01 171015\n", "2022-02-28 158610\n", "2022-02-27 124918\n", "2022-02-26 117770\n", "2022-02-25 153639\n", "2022-02-24 158766\n", "2022-02-23 174020\n", "2022-02-22 177879\n", "2022-02-21 168385\n", "2022-02-20 136709\n", "2022-02-19 122908\n", "2022-02-18 150471\n", "2022-02-17 171708\n", "2022-02-16 163813\n", "2022-02-15 174409\n", "2022-02-14 157824\n", "2022-02-13 128195\n", "2022-02-12 129437\n", "2022-02-11 154176\n", "2022-02-10 200822\n", "2022-02-09 169127\n", "2022-02-08 167888\n", "2022-02-07 164845\n", "2022-02-06 127541\n", "2022-02-05 118698\n", "2022-02-04 141087\n", "2022-02-03 164607\n", "2022-02-02 155123\n", "2022-02-01 156240\n", "2022-01-31 153421\n", "2022-01-30 127302\n", "2022-01-29 121823\n", "2022-01-28 147237\n", "2022-01-27 164625\n", "2022-01-26 151659\n", "2022-01-25 171067\n", "2022-01-24 160309\n", "2022-01-23 131947\n", "2022-01-22 125831\n", "2022-01-21 163317\n", "2022-01-20 162423\n", "2022-01-19 162450\n", "2022-01-18 162304\n", "2022-01-17 152670\n", "2022-01-16 129780\n", "2022-01-15 127743\n", "2022-01-14 142871\n", "2022-01-13 153600\n", "2022-01-12 157019\n", "2022-01-11 161619\n", "2022-01-10 149510\n", "2022-01-09 130817\n", "2022-01-08 115495\n", "2022-01-07 134900\n", "2022-01-06 153648\n", "2022-01-05 163418\n", "2022-01-04 156240\n", "2022-01-03 139026\n", "2022-01-02 103677\n", "2022-01-01 79281\n", "2023-07-05 219318\n", "2021-12-31 93639\n", "2021-12-30 119841\n", "2021-12-29 123020\n", "2021-12-28 126100\n", "2021-12-27 118939\n", "2021-12-26 94879\n", "2021-12-25 82543\n", "2021-12-24 101006\n", "2021-12-23 130331\n", "2021-12-22 129619\n", "2021-12-21 147794\n", "2021-12-20 141071\n", "2021-12-19 113561\n", "2021-12-18 104864\n", "2021-12-17 142506\n", "2021-12-16 151110\n", "2021-12-15 153496\n", "2021-12-14 164662\n", "2021-12-13 150342\n", "2021-12-12 124617\n", "2021-12-11 115300\n", "2021-12-10 143021\n", "2021-12-09 160359\n", "2021-12-08 151425\n", "2021-12-07 158514\n", "2021-12-06 152889\n", "2021-12-05 118731\n", "2021-12-04 112839\n", "2021-12-03 147496\n", "2021-12-02 161861\n", "2021-12-01 152039\n", "2021-11-30 165091\n", "2021-11-29 153791\n", "2021-11-28 119974\n", "2021-11-27 104420\n", "2021-11-26 132446\n", "2021-11-25 143484\n", "2021-11-24 147275\n", "2021-11-23 157066\n", "2021-11-22 147695\n", "2021-11-21 123731\n", "2021-11-20 111397\n", "2021-11-19 133975\n", "2021-11-18 156362\n", "2021-11-17 151480\n", "2021-11-16 157932\n", "2021-11-15 152525\n", "2021-11-14 113603\n", "2021-11-13 106421\n", "2021-11-12 136363\n", "2021-11-11 143950\n", "2021-11-10 143872\n", "2021-11-09 159393\n", "2021-11-08 153613\n", "2021-11-07 115880\n", "2021-11-06 100151\n", "2021-11-05 132173\n", "2021-11-04 129268\n", "2021-11-03 150128\n", "2021-11-02 143292\n", "2021-11-01 140196\n", "2021-10-31 116885\n", "2021-10-30 114174\n", "2023-07-06 219782\n", "2023-07-07 216364\n", "2023-07-08 199533\n", "2021-10-29 None\n", "2021-10-28 None\n", "2023-07-09 158478\n", "2023-07-10 202395\n", "2023-07-11 208080\n", "2021-10-27 None\n", "2021-10-27 None\n", "2021-10-26 None\n", "2021-10-25 None\n", "2021-10-24 None\n", "2021-10-23 None\n", "2021-10-22 None\n", "2021-10-21 105596\n", "2021-10-20 105567\n", "2021-10-19 96878\n", "2021-10-18 94462\n", "2021-10-17 110580\n", "2021-10-16 101348\n", "2023-07-12 21239\n", "2021-10-15 88622\n", "2021-10-14 95249\n", "2021-10-13 88018\n", "2021-10-12 98621\n", "2021-10-11 92559\n", "2021-10-10 102900\n", "2021-10-09 99096\n", "2021-10-08 93347\n", "2021-10-07 92906\n", "2021-10-06 159012\n", "2021-10-05 176718\n", "2021-10-04 165149\n", "2021-10-03 128029\n", "2021-10-02 130347\n", "2021-10-01 158765\n", "2021-09-30 165341\n", "2021-09-29 161599\n", "2021-09-28 145608\n", "2021-09-27 168491\n", "2021-09-26 120034\n", "2021-09-25 112071\n", "2021-09-24 133976\n", "2021-09-23 165790\n", "2021-09-22 170272\n", "2021-09-21 145717\n", "2021-09-20 157962\n", "2021-09-19 114822\n", "2021-09-18 114846\n", "2021-09-17 144865\n", "2021-09-16 157101\n", "2021-09-15 171711\n", "2021-09-14 153685\n", "2021-09-13 145546\n", "2021-09-12 115758\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-10 134429\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-09 145848\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-08 147960\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-07 144621\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-06 142431\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-05 106954\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-04 102948\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-03 131532\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-02 140980\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-01 142191\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-31 142929\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-30 140623\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-29 108143\n", "2021-09-11 None\n", "2021-08-28 103611\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-27 None\n", "2021-08-26 None\n", "2021-08-25 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-24 152324\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-23 146373\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-22 105915\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-21 105623\n", "2021-09-11 None\n", "2021-08-20 135311\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-19 143744\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-18 143788\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-17 201743\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-16 285255\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-15 239369\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-14 190156\n", "2021-09-11 None\n", "2021-08-13 185831\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-12 151255\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-11 150856\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-10 142915\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-09 183056\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-08 140128\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-07 143141\n", "2021-09-11 None\n", "2021-08-06 156218\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-05 157374\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-04 168133\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-03 165155\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-02 166191\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-08-01 146070\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-31 134567\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-30 158283\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-29 144607\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-28 192156\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-27 168983\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-26 146475\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-25 107732\n", "2021-09-11 None\n", "2021-07-24 135911\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-23 159717\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-22 169793\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-21 151412\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-20 156108\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-19 153513\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-18 101793\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-17 128350\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-16 161829\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-15 160663\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-14 152932\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-13 147451\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-12 134751\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-11 98209\n", "2021-07-10 105760\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-09 125990\n", "2021-07-08 None\n", "2021-09-11 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-09-11 None\n", "2021-07-08 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-09-11 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-07 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-08 None\n", "2021-07-07 None\n", "2021-07-07 131826\n", "2021-07-06 154174\n", "2021-07-05 137485\n", "2021-07-04 107845\n", "2021-07-03 104867\n", "2021-07-02 123531\n", "2021-07-01 145092\n", "2021-06-30 145212\n", "2021-06-29 145010\n", "2021-06-28 133106\n", "2021-06-27 112731\n", "2021-06-26 103867\n", "2021-06-25 130160\n", "2021-06-24 135525\n", "2021-06-23 131305\n", "2021-06-22 151369\n", "2021-06-21 139213\n", "2021-06-20 106053\n", "2021-06-19 97902\n", "2021-06-18 134995\n", "2021-06-17 144547\n", "2021-06-16 None\n", "2021-06-15 153501\n", "2021-06-14 None\n", "2021-06-14 None\n", "2021-06-16 None\n", "2021-06-14 17811\n", "2021-06-16 53981\n", "2021-06-13 129352\n", "2021-06-12 107497\n", "2021-06-11 139936\n", "2021-06-10 156150\n", "2021-06-09 None\n", "2021-06-08 None\n", "2021-06-09 None\n", "2021-06-09 None\n", "2021-06-09 None\n", "2021-06-09 54462\n", "2021-06-08 81748\n", "2021-06-07 151900\n", "2021-06-06 118093\n", "2021-06-05 108480\n", "2021-06-04 144564\n", "2021-06-03 142907\n", "2021-06-02 145696\n", "2021-06-01 140484\n", "2021-05-31 143819\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-29 107872\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 None\n", "2021-05-30 43735\n", "2021-05-28 137827\n", "2021-05-27 138515\n", "2021-05-26 151381\n", "2021-05-25 144068\n", "2021-05-24 141294\n", "2021-05-23 107261\n", "2021-05-22 108744\n", "2021-05-21 132648\n", "2023-07-13 202246\n", "2021-05-20 144478\n", "2021-05-19 140479\n", "2021-05-18 147178\n", "2021-05-17 133166\n", "2021-05-16 122502\n", "2021-05-15 105472\n", "2021-05-14 126777\n", "2021-05-13 131492\n", "2021-05-12 146342\n", "2021-05-11 139569\n", "2021-05-10 128448\n", "2021-05-09 120771\n", "2021-05-08 110469\n", "2021-05-07 132940\n", "2021-05-06 133536\n", "2021-05-05 143754\n", "2021-05-04 137322\n", "2021-05-03 131261\n", "2021-05-02 100491\n", "2021-05-01 97224\n", "2021-04-30 133208\n", "2021-04-29 128586\n", "2021-04-28 151331\n", "2021-04-27 145731\n", "2021-04-26 158866\n", "2021-04-25 104320\n", "2021-04-24 115130\n", "2021-04-23 131266\n", "2021-04-22 141581\n", "2021-04-21 133999\n", "2021-04-20 152739\n", "2021-04-19 142181\n", "2021-04-18 109004\n", "2021-04-17 98738\n", "2021-04-16 132058\n", "2021-04-15 138020\n", "2021-04-14 124352\n", "2021-04-13 146530\n", "2021-04-12 142307\n", "2021-04-11 112315\n", "2021-04-10 106275\n", "2021-04-09 121578\n", "2021-04-08 150354\n", "2021-04-07 143348\n", "2021-04-06 139861\n", "2021-04-05 125812\n", "2021-04-04 102213\n", "2021-04-03 98837\n", "2021-04-02 113779\n", "2021-04-01 123300\n", "2021-03-31 158863\n", "2021-03-30 139064\n", "2021-03-29 135079\n", "2021-03-28 102717\n", "2021-03-27 105251\n", "2021-03-26 131396\n", "2021-03-25 131662\n", "2021-03-24 153791\n", "2021-03-23 146010\n", "2021-03-22 167609\n", "2021-03-21 103268\n", "2021-03-20 129980\n", "2021-03-19 137699\n", "2021-03-18 128663\n", "2021-03-17 148362\n", "2021-03-16 143629\n", "2021-03-15 142507\n", "2021-03-14 104541\n", "2021-03-13 109239\n", "2021-03-12 138039\n", "2021-03-11 120769\n", "2021-03-10 136896\n", "2021-03-09 130015\n", "2021-03-08 117892\n", "2021-03-07 104856\n", "2021-03-06 96001\n", "2021-03-05 118329\n", "2021-03-04 127848\n", "2021-03-03 124909\n", "2021-03-02 137664\n", "2021-03-01 131559\n", "2021-02-28 100856\n", "2021-02-27 94077\n", "2021-02-26 120727\n", "2021-02-25 127857\n", "2021-02-24 123732\n", "2021-02-23 138238\n", "2021-02-22 129744\n", "2021-02-21 90563\n", "2021-02-20 102287\n", "2021-02-19 116419\n", "2021-02-18 121396\n", "2021-02-17 120099\n", "2021-02-16 126801\n", "2021-02-15 118407\n", "2021-02-14 90342\n", "2021-02-13 87043\n", "2021-02-12 108212\n", "2021-02-11 113777\n", "2021-02-10 115411\n", "2021-02-09 127944\n", "2021-02-08 123838\n", "2021-02-07 99374\n", "2021-02-06 92134\n", "2021-02-05 116962\n", "2021-02-04 124173\n", "2021-02-03 123896\n", "2021-02-02 136005\n", "2021-02-01 126419\n", "2021-01-31 97542\n", "2021-01-30 101135\n", "2021-01-29 118632\n", "2021-01-28 190270\n", "2021-01-27 150164\n", "2021-01-26 129960\n", "2021-01-25 127268\n", "2021-01-24 97218\n", "2021-01-23 93074\n", "2021-01-22 125481\n", "2021-01-21 134936\n", "2021-01-20 122465\n", "2021-01-19 138793\n", "2021-01-18 120455\n", "2021-01-17 95719\n", "2021-01-16 90452\n", "2021-01-15 104676\n", "2021-01-14 126576\n", "2021-01-13 122556\n", "2021-01-12 122085\n", "2021-01-11 115057\n", "2021-01-10 97171\n", "2021-01-09 88815\n", "2021-01-08 107305\n", "2021-01-07 103896\n", "2021-01-06 169010\n", "2021-01-04 118495\n", "2021-01-03 90999\n", "2021-01-02 78120\n", "2021-01-01 61792\n", "2020-12-31 78853\n", "2020-12-30 97676\n", "2020-12-29 100126\n", "2020-12-28 102864\n", "2020-12-27 83601\n", "2020-12-26 72741\n", "2020-12-25 69265\n", "2020-12-24 85743\n", "2020-12-23 99211\n", "2020-12-22 107861\n", "2020-12-21 108171\n", "2020-12-20 88794\n", "2020-12-19 87216\n", "2020-12-18 113342\n", "2020-12-17 119286\n", "2020-12-16 118892\n", "2020-12-15 121601\n", "2023-07-14 183667\n", "2020-12-14 121397\n", "2020-12-13 100758\n", "2020-12-12 110505\n", "2020-12-11 121813\n", "2020-12-10 122285\n", "2020-12-09 125885\n", "2020-12-08 121123\n", "2020-12-07 120685\n", "2020-12-06 93699\n", "2020-12-05 89056\n", "2020-12-04 113620\n", "2020-12-03 122602\n", "2020-12-02 122336\n", "2020-12-01 121861\n", "2020-11-30 120337\n", "2020-11-29 90619\n", "2020-11-28 84712\n", "2020-11-27 101996\n", "2020-11-26 109146\n", "2020-11-25 120024\n", "2020-11-24 119323\n", "2020-11-23 119123\n", "2020-11-22 91897\n", "2020-11-21 87519\n", "2020-11-20 113420\n", "2020-11-19 121835\n", "2020-11-18 121049\n", "2020-11-17 120302\n", "2020-11-16 116803\n", "2020-11-15 87217\n", "2020-11-14 78913\n", "2020-11-13 106544\n", "2020-11-12 115052\n", "2020-11-11 117431\n", "2020-11-10 120974\n", "2020-11-09 120426\n", "2020-11-08 89397\n", "2020-11-07 83767\n", "2020-11-06 108144\n", "2020-11-05 113527\n", "2020-11-04 118693\n", "2020-11-03 118317\n", "2020-11-02 113125\n", "2020-11-01 87380\n", "2020-10-31 92288\n", "2020-10-30 115735\n", "2023-07-15 144727\n", "2023-07-16 145415\n", "2023-07-17 194717\n", "2023-07-18 198298\n", "2023-07-19 191891\n", "2023-07-20 199602\n", "2023-07-21 181252\n", "2023-07-22 149255\n", "2023-07-23 None\n", "2023-07-23 None\n", "2023-07-23 None\n", "2023-07-23 None\n", "2023-07-23 147281\n", "2023-07-24 None\n", "2021-09-11 1623\n", "2021-07-08 10235\n", "2023-07-24 25356\n", "2021-01-05 None\n", "2021-01-05 None\n", "2021-01-05 33510\n", "2020-10-29 119754\n", "2020-10-28 120253\n", "2020-10-27 115336\n", "2020-10-26 124666\n", "2020-10-25 88748\n", "2020-10-24 82221\n", "2020-10-23 111979\n", "2020-10-22 113935\n", "2020-10-21 127005\n", "2020-10-20 124989\n", "2020-10-19 121641\n", "2020-10-18 96541\n", "2020-10-17 90417\n", "2020-10-16 110196\n", "2020-10-15 122745\n", "2020-10-14 120757\n", "2020-10-13 232159\n", "2020-10-12 118501\n", "2020-10-11 90242\n", "2020-10-10 86970\n", "2020-10-09 109585\n", "2020-10-08 110329\n", "2020-10-07 124285\n", "2020-10-06 123815\n", "2020-10-05 120939\n", "2020-10-04 99371\n", "2020-10-03 97454\n", "2020-10-02 130185\n", "2020-10-01 173465\n", "2020-09-30 130375\n", "2020-09-29 119164\n", "2020-09-28 113028\n", "2020-09-27 85461\n", "2020-09-26 81523\n", "2020-09-25 100304\n", "2020-09-24 116554\n", "2020-09-23 115043\n", "2020-09-22 113091\n", "2020-09-21 116060\n", "2020-09-20 84560\n", "2020-09-19 80635\n", "2020-09-18 113662\n", "2020-09-17 108257\n", "2020-09-16 113750\n", "2020-09-15 115914\n", "2020-09-14 114444\n", "2020-09-13 87547\n", "2020-09-12 91501\n", "2020-09-11 135623\n", "2020-09-10 130369\n", "2020-09-09 118462\n", "2020-09-08 113166\n", "2020-09-07 108183\n", "2020-09-06 81047\n", "2020-09-05 84969\n", "2020-09-04 114042\n", "2020-09-03 117674\n", "2020-09-02 115190\n", "2020-09-01 104887\n", "2020-08-31 104704\n", "2020-08-30 81906\n", "2020-08-29 77241\n", "2020-08-28 95865\n", "2020-08-27 110897\n", "2020-08-26 105630\n", "2020-08-25 103983\n", "2020-08-24 108109\n", "2020-08-23 None\n", "2020-08-22 None\n", "2020-08-21 None\n", "2020-08-20 119854\n", "2020-08-19 107545\n", "2020-08-18 110616\n", "2020-08-17 96907\n", "2020-08-16 87082\n", "2020-08-15 76907\n", "2020-08-14 95729\n", "2020-08-13 99771\n", "2020-08-12 105826\n", "2020-08-11 104433\n", "2020-08-10 101554\n", "2020-08-09 81329\n", "2020-08-08 78579\n", "2020-08-07 96098\n", "2020-08-06 105895\n", "2020-08-05 100597\n", "2020-08-04 99919\n", "2020-08-03 97021\n", "2020-08-02 83073\n", "2020-08-01 95005\n", "2020-07-31 91770\n", "2020-07-30 110008\n", "2020-07-29 103253\n", "2020-07-28 98500\n", "2020-07-27 104752\n", "2020-07-26 80342\n", "2020-07-25 77098\n", "2020-07-24 94436\n", "2020-07-23 106839\n", "2020-07-22 103643\n", "2020-07-21 114842\n", "2020-07-20 117781\n", "2020-07-19 90917\n", "2020-07-18 90455\n", "2020-07-17 None\n", "2020-07-16 104551\n", "2020-07-17 59352\n", "2020-07-15 102424\n", "2020-07-14 102149\n", "2020-07-13 95666\n", "2020-07-12 73601\n", "2020-07-11 77183\n", "2020-07-10 93636\n", "2020-07-09 101547\n", "2020-07-08 98450\n", "2020-07-07 99194\n", "2020-07-06 96377\n", "2020-07-05 73587\n", "2020-07-04 68710\n", "2020-07-03 87115\n", "2020-07-02 96381\n", "2020-07-01 104161\n", "2020-06-30 101177\n", "2020-06-29 95681\n", "2020-06-28 79869\n", "2020-06-27 73199\n", "2020-06-26 88735\n", "2020-06-25 95511\n", "2020-06-24 106130\n", "2020-06-23 99686\n", "2020-06-22 97818\n", "2020-06-21 77347\n", "2020-06-20 71863\n", "2020-06-19 93821\n", "2020-06-18 98623\n", "2020-06-17 103180\n", "2020-06-16 100137\n", "2020-06-15 102347\n", "2020-06-14 79547\n", "2020-06-13 74103\n", "2020-06-12 92497\n", "2020-06-11 96000\n", "2020-06-10 None\n", "2020-06-09 107025\n", "2020-06-08 217123\n", "2020-06-07 219381\n", "2020-06-06 204941\n", "2020-06-05 171741\n", "2020-06-04 100188\n", "2020-06-03 103119\n", "2020-06-02 107481\n", "2023-07-25 200802\n", "2020-06-01 104494\n", "2020-05-31 85364\n", "2020-05-30 79046\n", "2020-05-29 97694\n", "2020-05-28 105083\n", "2020-05-27 105417\n", "2020-05-26 102705\n", "2020-05-25 96880\n", "2020-05-24 82710\n", "2020-05-23 76757\n", "2020-05-22 96187\n", "2020-05-21 96327\n", "2020-05-20 144267\n", "2020-05-19 123666\n", "2020-05-18 137500\n", "2020-05-17 95082\n", "2020-05-16 85869\n", "2020-05-15 110234\n", "2020-05-14 104565\n", "2020-05-13 120495\n", "2020-05-12 112139\n", "2020-05-11 104233\n", "2020-05-10 85319\n", "2020-05-09 84168\n", "2020-05-08 100472\n", "2020-05-07 106127\n", "2020-05-06 105168\n", "2020-05-05 103146\n", "2020-05-04 100966\n", "2020-05-03 82724\n", "2020-05-02 82514\n", "2020-05-01 91328\n", "2020-04-30 113929\n", "2020-04-29 97211\n", "2020-04-28 109320\n", "2020-04-27 104529\n", "2020-04-26 83146\n", "2020-04-25 85922\n", "2020-04-24 99102\n", "2020-04-23 98315\n", "2020-04-22 108425\n", "2020-04-21 103450\n", "2020-04-20 100590\n", "2020-04-19 88529\n", "2020-04-18 82542\n", "2020-04-17 96383\n", "2020-04-16 108160\n", "2020-04-15 96443\n", "2020-04-14 105887\n", "2020-04-13 96581\n", "2020-04-12 83153\n", "2020-04-11 77101\n", "2020-04-10 93688\n", "2020-04-09 102127\n", "2020-04-08 107579\n", "2020-04-07 109109\n", "2020-04-06 105122\n", "2020-04-05 102550\n", "2020-04-04 90253\n", "2020-04-03 98640\n", "2020-04-02 102554\n", "2020-04-01 106248\n", "2020-03-31 243730\n", "2020-03-30 109895\n", "2020-03-29 88617\n", "2020-03-28 82588\n", "2020-03-27 98042\n", "2020-03-26 101039\n", "2020-03-25 116076\n", "2020-03-24 96064\n", "2020-03-23 100761\n", "2020-03-22 78823\n", "2020-03-21 75400\n", "2020-03-20 88749\n", "2020-03-19 None\n", "2020-03-18 None\n", "2020-03-17 99502\n", "2023-07-26 196364\n", "2020-03-16 100520\n", "2020-03-15 85434\n", "2020-03-14 79641\n", "2020-03-13 86641\n", "2020-03-12 95496\n", "2020-03-11 98538\n", "2020-03-10 99908\n", "2020-03-09 98588\n", "2020-03-08 70864\n", "2020-03-07 69985\n", "2020-03-06 93556\n", "2020-03-05 None\n", "2020-03-04 104289\n", "2020-03-03 100184\n", "2020-03-02 94929\n", "2020-03-01 69452\n", "2020-02-29 71857\n", "2020-02-28 89251\n", "2020-02-27 93736\n", "2020-02-26 106345\n", "2020-02-25 94266\n", "2020-02-24 93886\n", "2020-02-23 68329\n", "2020-02-22 66444\n", "2020-02-21 86380\n", "2020-02-20 95544\n", "2020-02-19 96496\n", "2020-02-18 96656\n", "2020-02-17 89048\n", "2020-02-16 67902\n", "2020-02-15 64054\n", "2020-02-14 84950\n", "2020-02-13 94659\n", "2020-02-12 100321\n", "2020-02-11 95485\n", "2020-02-10 91657\n", "2020-02-09 66033\n", "2020-02-08 66297\n", "2020-02-07 84032\n", "2020-02-06 88363\n", "2020-02-05 91845\n", "2020-02-04 89900\n", "2020-02-03 86922\n", "2020-02-02 64815\n", "2020-02-01 60692\n", "2020-01-31 79954\n", "2020-01-30 87140\n", "2020-01-29 91418\n", "2020-01-28 94341\n", "2020-01-27 84375\n", "2020-01-26 61156\n", "2020-01-25 55101\n", "2020-01-24 74700\n", "2020-01-23 86233\n", "2020-01-22 87989\n", "2020-01-21 87890\n", "2020-01-20 81879\n", "2020-01-19 63410\n", "2020-01-18 60183\n", "2020-01-17 81255\n", "2020-01-16 89721\n", "2020-01-15 86586\n", "2020-01-14 87541\n", "2020-01-13 85265\n", "2020-01-12 60279\n", "2020-01-11 57318\n", "2020-01-10 80758\n", "2020-01-09 86915\n", "2020-01-08 84042\n", "2020-01-07 83044\n", "2020-01-06 77978\n", "2020-01-05 55342\n", "2020-01-04 54759\n", "2020-01-03 68461\n", "2020-01-02 66442\n", "2020-01-01 40004\n", "2019-12-31 52036\n", "2019-12-30 68179\n", "2019-12-29 57431\n", "2019-12-28 52964\n", "2019-12-27 61912\n", "2019-12-26 62197\n", "2019-12-25 54273\n", "2019-12-24 61718\n", "2019-12-23 66713\n", "2019-12-22 52852\n", "2019-12-21 52913\n", "2019-12-20 77376\n", "2019-12-19 82218\n", "2019-12-18 85220\n", "2019-12-17 87930\n", "2019-12-16 86335\n", "2019-12-15 60862\n", "2019-12-14 57746\n", "2019-12-13 83021\n", "2019-12-12 89985\n", "2019-12-11 91311\n", "2019-12-10 90608\n", "2019-12-09 90599\n", "2019-12-08 63457\n", "2019-12-07 62056\n", "2019-12-06 85405\n", "2019-12-05 93237\n", "2019-12-04 95294\n", "2019-12-03 100953\n", "2019-12-02 91599\n", "2019-12-01 61834\n", "2019-11-30 55964\n", "2019-11-29 72392\n", "2019-11-28 79431\n", "2019-11-27 87305\n", "2019-11-26 90375\n", "2019-11-25 88041\n", "2019-11-24 61339\n", "2019-11-23 59418\n", "2019-11-22 84084\n", "2019-11-21 91503\n", "2019-11-20 93765\n", "2019-11-19 93960\n", "2019-11-18 90781\n", "2019-11-17 67249\n", "2019-11-16 59552\n", "2019-11-15 82494\n", "2019-11-14 90502\n", "2019-11-13 92360\n", "2019-11-12 92882\n", "2019-11-11 85486\n", "2019-11-10 59316\n", "2019-11-09 60374\n", "2019-11-08 83864\n", "2019-11-07 91890\n", "2019-11-06 95168\n", "2019-11-05 94674\n", "2019-11-04 90189\n", "2019-11-03 61473\n", "2019-11-02 70143\n", "2019-11-01 78810\n", "2019-10-31 98665\n", "2019-10-30 96785\n", "2019-10-29 94137\n", "2019-10-28 89235\n", "2023-07-27 192401\n", "2019-10-27 60524\n", "2019-10-26 60722\n", "2019-10-25 84130\n", "2019-10-24 91921\n", "2019-10-23 95749\n", "2019-10-22 97432\n", "2019-10-21 97500\n", "2019-10-20 66171\n", "2019-10-19 64013\n", "2019-10-18 74620\n", "2019-10-17 86711\n", "2019-10-16 95073\n", "2019-10-15 97033\n", "2019-10-14 91032\n", "2019-10-13 64870\n", "2019-10-12 66317\n", "2019-10-11 86478\n", "2019-10-10 90811\n", "2019-10-09 92811\n", "2019-10-08 92562\n", "2019-10-07 86446\n", "2019-10-06 61460\n", "2019-10-05 58611\n", "2019-10-04 82247\n", "2019-10-03 88082\n", "2019-10-02 89364\n", "2019-10-01 91104\n", "2019-09-30 85922\n", "2019-09-29 62645\n", "2019-09-28 58517\n", "2019-09-27 79514\n", "2019-09-26 None\n", "2019-09-26 None\n", "2019-09-26 38654\n", "2019-09-25 89284\n", "2019-09-24 None\n", "2019-09-24 None\n", "2019-09-23 89032\n", "2019-09-22 61328\n", "2019-09-21 58131\n", "2019-09-20 81530\n", "2019-09-19 98811\n", "2019-09-18 100007\n", "2019-09-17 101403\n", "2019-09-16 97595\n", "2019-09-15 64654\n", "2019-09-14 60015\n", "2019-09-13 None\n", "2019-09-12 None\n", "2019-09-11 104569\n", "2019-09-10 105740\n", "2019-09-09 97773\n", "2019-09-08 64726\n", "2019-09-07 62678\n", "2019-09-06 87456\n", "2019-09-05 97613\n", "2019-09-04 98214\n", "2019-09-03 95119\n", "2019-09-02 81320\n", "2019-09-01 57884\n", "2019-08-31 57074\n", "2019-08-30 82061\n", "2019-08-29 92948\n", "2019-08-28 91653\n", "2019-08-27 87793\n", "2019-08-26 79403\n", "2019-08-25 56936\n", "2019-08-24 55495\n", "2019-08-23 77744\n", "2019-08-22 84125\n", "2019-08-21 84657\n", "2019-08-20 81918\n", "2019-08-19 76819\n", "2019-08-18 51794\n", "2019-08-17 49995\n", "2019-08-16 71472\n", "2019-08-15 71381\n", "2019-08-14 77262\n", "2019-08-13 85418\n", "2019-08-12 77846\n", "2019-08-11 55204\n", "2019-08-10 61619\n", "2019-08-09 84539\n", "2019-08-08 86133\n", "2019-08-07 81492\n", "2019-08-06 72349\n", "2019-08-05 71520\n", "2019-08-04 46483\n", "2019-08-03 44647\n", "2019-08-02 66033\n", "2019-08-01 69772\n", "2019-07-31 71987\n", "2019-07-30 73905\n", "2019-07-29 71315\n", "2019-07-28 49124\n", "2019-07-27 45600\n", "2019-07-26 66953\n", "2019-07-25 73481\n", "2019-07-24 73553\n", "2019-07-23 77293\n", "2019-07-22 75896\n", "2019-07-21 47354\n", "2019-07-20 46816\n", "2019-07-19 68460\n", "2019-07-18 72905\n", "2019-07-17 75106\n", "2019-07-16 75712\n", "2019-07-15 75956\n", "2023-07-28 178992\n", "2019-07-14 48296\n", "2019-07-13 47467\n", "2019-07-12 70762\n", "2019-07-11 75268\n", "2019-07-10 74254\n", "2019-07-09 75127\n", "2019-07-08 74228\n", "2019-07-07 46613\n", "2019-07-06 44985\n", "2019-07-05 63864\n", "2019-07-04 67122\n", "2019-07-03 73512\n", "2019-07-02 73692\n", "2019-07-01 72000\n", "2019-06-30 47589\n", "2019-06-29 45642\n", "2019-06-28 67798\n", "2019-06-27 80596\n", "2019-06-26 77133\n", "2019-06-25 82983\n", "2019-06-24 77988\n", "2019-06-23 49156\n", "2019-06-22 46669\n", "2019-06-21 68194\n", "2019-06-20 73681\n", "2019-06-19 76211\n", "2019-06-18 77165\n", "2019-06-17 72949\n", "2019-06-16 47965\n", "2019-06-15 48156\n", "2019-06-14 68150\n", "2019-06-13 76310\n", "2019-06-12 78377\n", "2019-06-11 78708\n", "2019-06-10 75195\n", "2019-06-09 48138\n", "2019-06-08 44812\n", "2019-06-07 62631\n", "2019-06-06 73937\n", "2019-06-05 75518\n", "2019-06-04 77698\n", "2019-06-03 75164\n", "2019-06-02 49378\n", "2019-06-01 46400\n", "2019-05-31 73624\n", "2019-05-30 74263\n", "2019-05-29 78717\n", "2019-05-28 76813\n", "2019-05-27 70053\n", "2019-05-26 48242\n", "2019-05-25 49072\n", "2019-05-24 72312\n", "2019-05-23 76979\n", "2019-05-22 85863\n", "2019-05-21 85870\n", "2019-05-20 80904\n", "2019-05-19 56949\n", "2019-05-18 52556\n", "2019-05-17 76717\n", "2019-05-16 86141\n", "2019-05-15 85244\n", "2019-05-14 88579\n", "2019-05-13 83195\n", "2019-05-12 56689\n", "2019-05-11 53854\n", "2019-05-10 79665\n", "2019-05-09 81491\n", "2019-05-08 None\n", "2019-05-07 91224\n", "2019-05-06 83033\n", "2019-05-05 61944\n", "2019-05-04 53960\n", "2019-05-03 70983\n", "2019-05-02 76249\n", "2019-05-01 64921\n", "2019-04-30 80840\n", "2019-04-29 82212\n", "2019-04-28 59898\n", "2019-04-27 54578\n", "2019-04-26 75718\n", "2019-04-25 83891\n", "2019-04-24 85975\n", "2019-04-23 90816\n", "2019-04-22 87648\n", "2019-04-21 58695\n", "2019-04-20 51459\n", "2019-04-19 69154\n", "2019-04-18 78373\n", "2019-04-17 83544\n", "2019-04-16 89348\n", "2019-04-15 83779\n", "2019-04-14 61299\n", "2019-04-13 54563\n", "2019-04-12 81833\n", "2019-04-11 87043\n", "2019-04-10 88334\n", "2019-04-09 84081\n", "2019-04-08 86945\n", "2019-04-07 58799\n", "2019-04-06 53108\n", "2019-04-05 74021\n", "2019-04-04 86721\n", "2019-04-03 89200\n", "2019-04-02 88857\n", "2019-04-01 86245\n", "2019-03-31 58314\n", "2019-03-30 57951\n", "2019-03-29 81048\n", "2019-03-28 86001\n", "2019-03-27 86651\n", "2019-03-26 87207\n", "2019-03-25 83831\n", "2019-03-24 59080\n", "2019-03-23 55198\n", "2019-03-22 77478\n", "2019-03-21 83292\n", "2019-03-20 86363\n", "2019-03-19 87135\n", "2019-03-18 84180\n", "2019-03-17 59138\n", "2019-03-16 54635\n", "2019-03-15 78101\n", "2019-03-14 84606\n", "2019-03-13 86101\n", "2019-03-12 87886\n", "2019-03-11 85415\n", "2019-03-10 59111\n", "2019-03-09 54592\n", "2019-03-08 74485\n", "2019-03-07 81567\n", "2019-03-06 81665\n", "2019-03-05 79599\n", "2019-03-04 79871\n", "2019-03-03 58658\n", "2019-03-02 53532\n", "2019-03-01 73949\n", "2019-02-28 82991\n", "2019-02-27 82865\n", "2019-02-26 85736\n", "2019-02-25 83969\n", "2019-02-24 58684\n", "2019-02-23 56285\n", "2019-02-22 77759\n", "2019-02-21 82082\n", "2019-02-20 82849\n", "2019-02-19 80779\n", "2019-02-18 76488\n", "2019-02-17 53910\n", "2019-02-16 50635\n", "2019-02-15 70831\n", "2019-02-14 74534\n", "2019-02-13 78414\n", "2019-02-12 78224\n", "2019-02-11 74917\n", "2019-02-10 51716\n", "2019-02-09 48071\n", "2019-02-08 67270\n", "2019-02-07 69403\n", "2019-02-06 66595\n", "2019-02-05 63083\n", "2019-02-04 61184\n", "2019-02-03 45358\n", "2019-02-02 44670\n", "2019-02-01 63086\n", "2019-01-31 71588\n", "2019-01-30 72805\n", "2019-01-29 73639\n", "2019-01-28 71477\n", "2019-01-27 49418\n", "2019-01-26 45040\n", "2019-01-25 66694\n", "2019-01-24 71260\n", "2019-01-23 71802\n", "2019-01-22 72290\n", "2019-01-21 67283\n", "2019-01-20 47364\n", "2019-01-19 45941\n", "2019-01-18 71454\n", "2019-01-17 72755\n", "2019-01-16 71909\n", "2019-01-15 71289\n", "2019-01-14 71553\n", "2019-01-13 49154\n", "2019-01-12 46712\n", "2019-01-11 70503\n", "2019-01-10 73066\n", "2019-01-09 73126\n", "2019-01-08 77831\n", "2019-01-07 73125\n", "2019-01-06 50027\n", "2019-01-05 47068\n", "2019-01-04 64492\n", "2019-01-03 64895\n", "2019-01-02 59840\n", "2019-01-01 33507\n", "2018-12-31 37939\n", "2018-12-30 37565\n", "2018-12-29 42758\n", "2018-12-28 53827\n", "2018-12-27 56534\n", "2018-12-26 51182\n", "2018-12-25 41874\n", "2018-12-24 46837\n", "2018-12-23 40856\n", "2018-12-22 40466\n", "2018-12-21 63231\n", "2018-12-20 70731\n", "2018-12-19 73139\n", "2018-12-18 77079\n", "2018-12-17 75600\n", "2018-12-16 51440\n", "2018-12-15 47491\n", "2018-12-14 73508\n", "2018-12-13 80461\n", "2018-12-12 81812\n", "2018-12-11 84001\n", "2018-12-10 86842\n", "2018-12-09 60848\n", "2018-12-08 57666\n", "2018-12-07 75097\n", "2018-12-06 82693\n", "2018-12-05 83462\n", "2018-12-04 81449\n", "2018-12-03 79545\n", "2018-12-02 56460\n", "2018-12-01 52094\n", "2018-11-30 74705\n", "2018-11-29 81475\n", "2018-11-28 None\n", "2018-11-28 27634\n", "2018-11-27 82947\n", "2018-11-26 78784\n", "2018-11-25 53727\n", "2018-11-24 47600\n", "2018-11-23 62348\n", "2018-11-22 69036\n", "2018-11-21 75462\n", "2018-11-20 79179\n", "2018-11-19 80897\n", "2018-11-18 54683\n", "2018-11-17 50499\n", "2018-11-16 73024\n", "2018-11-15 78747\n", "2018-11-14 79429\n", "2018-11-13 78656\n", "2018-11-12 77299\n", "2018-11-11 52300\n", "2018-11-10 49738\n", "2018-11-09 71880\n", "2018-11-08 77074\n", "2018-11-07 76860\n", "2018-11-06 80141\n", "2018-11-05 78550\n", "2018-11-04 53867\n", "2018-11-03 49600\n", "2018-11-02 69554\n", "2018-11-01 73895\n", "2018-10-31 85486\n", "2018-10-30 88348\n", "2018-10-29 86311\n", "2018-10-28 57060\n", "2018-10-27 56028\n", "2018-10-26 77220\n", "2018-10-25 84101\n", "2018-10-24 85285\n", "2018-10-23 89078\n", "2018-10-22 None\n", "2018-10-21 None\n", "2018-10-20 54064\n", "2018-10-19 76796\n", "2018-10-18 80654\n", "2018-10-17 82100\n", "2018-10-16 83879\n", "2018-10-15 79381\n", "2018-10-14 54234\n", "2018-10-13 51594\n", "2018-10-12 72700\n", "2018-10-11 79706\n", "2018-10-10 80251\n", "2018-10-09 84767\n", "2018-10-08 80734\n", "2018-10-07 52647\n", "2018-10-06 49650\n", "2018-10-05 69813\n", "2018-10-04 74589\n", "2018-10-03 74528\n", "2018-10-02 79032\n", "2018-10-01 77056\n", "2018-09-30 52018\n", "2018-09-29 51411\n", "2018-09-28 69835\n", "2018-09-27 75694\n", "2018-09-26 77831\n", "2018-09-25 76099\n", "2018-09-24 69018\n", "2018-09-23 53579\n", "2018-09-22 51661\n", "2018-09-21 69190\n", "2018-09-20 75369\n", "2018-09-19 76186\n", "2018-09-18 78347\n", "2018-09-17 73398\n", "2018-09-16 48301\n", "2018-09-15 44872\n", "2018-09-14 67117\n", "2018-09-13 72531\n", "2018-09-12 76103\n", "2018-09-11 74831\n", "2018-09-10 74290\n", "2018-09-09 48545\n", "2018-09-08 43742\n", "2018-09-07 65160\n", "2018-09-06 71779\n", "2018-09-05 72843\n", "2018-09-04 72038\n", "2018-09-03 62661\n", "2018-09-02 43548\n", "2018-09-01 41517\n", "2018-08-31 64310\n", "2018-08-30 70158\n", "2018-08-29 71412\n", "2018-08-28 72266\n", "2018-08-27 66151\n", "2018-08-26 43398\n", "2018-08-25 42280\n", "2018-08-24 62811\n", "2018-08-23 66283\n", "2018-08-22 64875\n", "2018-08-21 65427\n", "2018-08-20 63254\n", "2018-08-19 43188\n", "2018-08-18 40952\n", "2018-08-17 60747\n", "2018-08-16 63806\n", "2018-08-15 62619\n", "2018-08-14 66579\n", "2018-08-13 65452\n", "2018-08-12 42946\n", "2018-08-11 42156\n", "2018-08-10 62553\n", "2018-08-09 66537\n", "2018-08-08 68010\n", "2018-08-07 69178\n", "2018-08-06 66618\n", "2018-08-05 42623\n", "2018-08-04 42934\n", "2018-08-03 63847\n", "2018-08-02 66697\n", "2018-08-01 67337\n", "2018-07-31 71695\n", "2018-07-30 66467\n", "2018-07-29 43956\n", "2018-07-28 41981\n", "2018-07-27 62510\n", "2018-07-26 69319\n", "2018-07-25 68612\n", "2018-07-24 69049\n", "2018-07-23 66760\n", "2018-07-22 43518\n", "2018-07-21 41309\n", "2018-07-20 61896\n", "2023-07-29 140343\n", "2018-07-19 67902\n", "2018-07-18 66272\n", "2018-07-17 69886\n", "2018-07-16 66558\n", "2018-07-15 42486\n", "2018-07-14 41526\n", "2018-07-13 62967\n", "2018-07-12 68062\n", "2018-07-11 68165\n", "2018-07-10 70153\n", "2018-07-09 68473\n", "2018-07-08 42763\n", "2018-07-07 40418\n", "2018-07-06 60695\n", "2018-07-05 66736\n", "2018-07-04 61829\n", "2018-07-03 67332\n", "2018-07-02 66720\n", "2018-07-01 43746\n", "2018-06-30 41678\n", "2018-06-29 64681\n", "2018-06-28 67369\n", "2018-06-27 69271\n", "2018-06-26 72682\n", "2018-06-25 69147\n", "2018-06-24 44558\n", "2018-06-23 42605\n", "2018-06-22 62500\n", "2018-06-21 67501\n", "2018-06-20 69466\n", "2018-06-19 69103\n", "2018-06-18 62735\n", "2018-06-17 40654\n", "2018-06-16 39640\n", "2018-06-15 61071\n", "2018-06-14 69023\n", "2018-06-13 71971\n", "2018-06-12 74720\n", "2018-06-11 71766\n", "2018-06-10 47746\n", "2018-06-09 46270\n", "2018-06-08 69175\n", "2018-06-07 74607\n", "2018-06-06 76980\n", "2018-06-05 79263\n", "2018-06-04 74753\n", "2018-06-03 49146\n", "2018-06-02 44134\n", "2018-06-01 65540\n", "2018-05-31 70720\n", "2018-05-30 70966\n", "2018-05-29 67607\n", "2018-05-28 65586\n", "2018-05-27 45301\n", "2018-05-26 43586\n", "2018-05-25 66835\n", "2018-05-24 71888\n", "2018-05-23 74600\n", "2018-05-22 73290\n", "2018-05-21 69738\n", "2018-05-20 48143\n", "2018-05-19 46221\n", "2018-05-18 68346\n", "2018-05-17 70405\n", "2018-05-16 70928\n", "2018-05-15 72136\n", "2018-05-14 72255\n", "2018-05-13 47712\n", "2018-05-12 45828\n", "2018-05-11 66380\n", "2018-05-10 69717\n", "2018-05-09 71139\n", "2018-05-08 72343\n", "2018-05-07 69650\n", "2018-05-06 46887\n", "2018-05-05 45874\n", "2018-05-04 65986\n", "2018-05-03 70711\n", "2018-05-02 68199\n", "2018-05-01 58541\n", "2018-04-30 58869\n", "2018-04-29 43353\n", "2018-04-28 47209\n", "2018-04-27 64293\n", "2018-04-26 73192\n", "2018-04-25 72676\n", "2018-04-24 75757\n", "2018-04-23 73638\n", "2018-04-22 49315\n", "2018-04-21 46540\n", "2018-04-20 67474\n", "2018-04-19 72787\n", "2018-04-18 75386\n", "2018-04-17 75433\n", "2018-04-16 73926\n", "2018-04-15 49630\n", "2018-04-14 46191\n", "2018-04-13 66406\n", "2018-04-12 74817\n", "2018-04-11 74326\n", "2018-04-10 75577\n", "2018-04-09 73643\n", "2018-04-08 53224\n", "2018-04-07 46126\n", "2018-04-06 127682\n", "2018-04-05 69348\n", "2018-04-04 73489\n", "2018-04-03 77256\n", "2018-04-02 69901\n", "2018-04-01 47650\n", "2018-03-31 47418\n", "2018-03-30 61657\n", "2018-03-29 67542\n", "2018-03-28 75538\n", "2018-03-27 76565\n", "2018-03-26 75020\n", "2018-03-25 55043\n", "2018-03-24 51366\n", "2018-03-23 70552\n", "2018-03-22 77285\n", "2018-03-21 78699\n", "2018-03-20 81314\n", "2018-03-19 76742\n", "2018-03-18 52794\n", "2018-03-17 48963\n", "2018-03-16 71400\n", "2018-03-15 77707\n", "2018-03-14 78978\n", "2018-03-13 81666\n", "2018-03-12 76507\n", "2018-03-11 53792\n", "2018-03-10 50344\n", "2018-03-09 70252\n", "2018-03-08 69503\n", "2018-03-07 76614\n", "2018-03-06 77207\n", "2018-03-05 76188\n", "2018-03-04 50646\n", "2018-03-03 49247\n", "2018-03-02 67500\n", "2018-03-01 72267\n", "2018-02-28 75170\n", "2018-02-27 75814\n", "2018-02-26 72461\n", "2018-02-25 50385\n", "2018-02-24 50894\n", "2018-02-23 66545\n", "2018-02-22 69001\n", "2018-02-21 68783\n", "2018-02-20 67303\n", "2018-02-19 62788\n", "2018-02-18 47153\n", "2018-02-17 42784\n", "2018-02-16 57601\n", "2018-02-15 61122\n", "2018-02-14 60967\n", "2018-02-13 65656\n", "2018-02-12 67561\n", "2018-02-11 49895\n", "2018-02-10 44978\n", "2018-02-09 65372\n", "2018-02-08 71011\n", "2018-02-07 73021\n", "2018-02-06 72984\n", "2018-02-05 69369\n", "2018-02-04 49544\n", "2018-02-03 47783\n", "2018-02-02 67050\n", "2018-02-01 73975\n", "2018-01-31 73536\n", "2018-01-30 74611\n", "2018-01-29 71133\n", "2018-01-28 49797\n", "2018-01-27 47074\n", "2018-01-26 65657\n", "2018-01-25 71613\n", "2018-01-24 75568\n", "2018-01-23 76660\n", "2018-01-22 69220\n", "2018-01-21 49724\n", "2018-01-20 47198\n", "2018-01-19 64089\n", "2018-01-18 70946\n", "2018-01-17 70626\n", "2018-01-16 71004\n", "2018-01-15 64672\n", "2018-01-14 46172\n", "2018-01-13 43721\n", "2018-01-12 64574\n", "2018-01-11 70861\n", "2018-01-10 69574\n", "2018-01-09 69666\n", "2018-01-08 65861\n", "2018-01-07 45212\n", "2018-01-06 42794\n", "2018-01-05 56789\n", "2018-01-04 60358\n", "2018-01-03 60734\n", "2018-01-02 53730\n", "2018-01-01 30741\n", "2017-12-31 28832\n", "2017-12-30 33810\n", "2017-12-29 47008\n", "2017-12-28 51896\n", "2017-12-27 50944\n", "2017-12-26 47416\n", "2017-12-25 38907\n", "2017-12-24 32722\n", "2017-12-23 35803\n", "2017-12-22 52723\n", "2017-12-21 59526\n", "2017-12-20 61576\n", "2017-12-19 65016\n", "2017-12-18 62785\n", "2017-12-17 44033\n", "2017-12-16 40099\n", "2017-12-15 60662\n", "2017-12-14 67472\n", "2017-12-13 68120\n", "2017-12-12 68365\n", "2017-12-11 68391\n", "2017-12-10 46524\n", "2017-12-09 43693\n", "2017-12-08 62626\n", "2017-12-07 69084\n", "2017-12-06 68795\n", "2017-12-05 71947\n", "2023-07-30 140955\n", "2017-12-04 70011\n", "2017-12-03 48924\n", "2017-12-02 45938\n", "2017-12-01 None\n", "2017-12-01 None\n"]}], "source": ["import psycopg2\n", "\n", "conn = psycopg2.connect()\n", "try:\n", "    with conn, conn.cursor() as cur:\n", "        cur.execute(\"\"\"\n", "            SELECT partition_name, (additional_info->'new_repos')::integer AS new_repos\n", "            FROM batch_job\n", "            WHERE name='gh-archive-processing'\n", "            ORDER BY created_at\n", "        \"\"\")\n", "        for date_str, new_repos in cur.fetchall():\n", "            print(date_str, new_repos)\n", "finally:\n", "    conn.close()"]}, {"cell_type": "markdown", "id": "5b04a508-bc1b-4731-8ac4-7bbd87e4ca21", "metadata": {}, "source": ["### Load a bit more data\n", "To load the full data, make sure to use basePath to ensure that hive partitioning is honored.  Note that the \n", "`day` and `hour` columns are autocreated.\n", "\n", "If you want you make joins faster you can use these partition columns by joining also on these columns;  spark however should somewhat account for that automatically due to the metadata in parquet partitions.\n", "\n", "Note that the same repo are from the same partition so if you want to group files by repo, make sure to \n", "first group by `day` and `hour` to make that clustering efficient."]}, {"cell_type": "code", "execution_count": 9, "id": "2f9bf6af-03db-4b8a-b260-3a81246c0005", "metadata": {"execution": {"iopub.execute_input": "2023-07-01T09:22:48.507570Z", "iopub.status.busy": "2023-07-01T09:22:48.506580Z", "iopub.status.idle": "2023-07-01T09:23:09.303135Z", "shell.execute_reply": "2023-07-01T09:23:09.301977Z", "shell.execute_reply.started": "2023-07-01T09:22:48.507507Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>content</th><th>is_binary</th><th>path</th><th>pct_modified</th><th>repo_id</th><th>repo_name</th><th>repo_root</th><th>sha</th><th>size</th><th>lang</th><th>is_head</th><th>day</th><th>hour</th></tr>\n", "<tr><td>const Prettier = jest != null ? require(&#x27;prettier&#x27;) : require(&#x27;prettier/standalone&#x27;) // TODO spli...</td><td>false</td><td>editor/src/components/canvas/ui-jsx-canvas.test-utils.tsx</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>6b48e1f531e0ebb3ac9c9fe75973d63a0f917664</td><td>15230</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import type {\\n  TemplatePath,\\n  id,\\n  StaticTemplatePath,\\n  Imports,\\n} from &#x27;../../core/shar...</td><td>false</td><td>4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/editor/editor-modes.ts</td><td>46</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>07d8091ffaa720a693547998006cbfeb9197c18f</td><td>3389</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as React from &#x27;react&#x27;\\nimport { filterOldPasses } from &#x27;../../core/workers/ts/ts-worker&#x27;...</td><td>false</td><td>c1c1194e9bcf7ecc27bf0cef6783f29d4e4a1b88:editor/src/components/canvas/canvas-wrapper-component.tsx</td><td>14</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>1c62d3bc480aad511550281be265e8c415a3caea</td><td>6694</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import { Interpolation, Theme } from &#x27;@emotion/react&#x27;\\nimport * as React from &#x27;react&#x27;\\nimport {\\n...</td><td>false</td><td>d71efc484ee5235b9739b3dcf19eabd2933d148c:utopia-api/src/primitives/view.tsx</td><td>50</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>7d293d330ca304e33f11c1e1c6c27cf052a9084e</td><td>749</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import {\\n  childPaths,\\n  deletePath,\\n  ensureDirectoryExists,\\n  exists,\\n  readDirectory,\\n  ...</td><td>false</td><td>utopia-vscode-common/src/mailbox.ts</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>1a7f7c519c4deb7ad70fa418c3d44af96c1c2745</td><td>8234</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>/** @jsx jsx */\\nimport { jsx } from &#x27;@emotion/react&#x27;\\nimport * as Path from &#x27;path&#x27;\\nimport * as ...</td><td>false</td><td>3110af410e7c4874a086bf2efdd20019091ed9a6:editor/src/components/filebrowser/fileitem.tsx</td><td>54</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>ab26a1aff5d05bc20a8c0bb55245d182128d3335</td><td>22428</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>/** @jsx jsx */\\nimport { jsx } from &#x27;@emotion/react&#x27;\\nimport * as React from &#x27;react&#x27;\\nimport {\\n...</td><td>false</td><td>4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/navigator/navigator-item/navigator...</td><td>36</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>735cbce240d67b22578ed43bd955398b27b8d876</td><td>13253</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>// Should allow us to guard against a type being refactored into something\\n// which Set allows b...</td><td>false</td><td>editor/src/core/shared/set-utils.ts</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>3964f8ad4375e21f093cb97ed056d79aa4c133d8</td><td>585</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import {\\n  addFileToProjectContents,\\n  projectContentFile,\\n  ProjectContentsTree,\\n  ProjectCo...</td><td>false</td><td>editor/src/sample-projects/sample-project-utils.test-utils.ts</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>c8f7adfbb7c0a5dbbe4f0e93ae65f1d9e7c1e7aa</td><td>3705</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as R from &#x27;ramda&#x27;\\nimport {\\n  SceneMetadata,\\n  StaticInstancePath,\\n  PropertyPath,\\n ...</td><td>false</td><td>87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/core/model/scene-utils.ts</td><td>25</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>8f1916e248581cd080047836d94dfc636a0ad514</td><td>11478</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>packages: .\\n\\n</td><td>false</td><td>server/cabal.project</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>b764c340a6236bfa178217064eeff4940d4c4482</td><td>13</td><td></td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>/* eslint-disable no-console */\\nrequire(&#x27;dotenv&#x27;).config({ path: &#x27;src/.env&#x27; })\\nimport * as pupp...</td><td>false</td><td>puppeteer-tests/src/performance-test.ts</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>9fb8ad1093451b0a344f92f646909add7f540777</td><td>23079</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as React from &#x27;react&#x27;\\nimport { MapLike } from &#x27;typescript&#x27;\\nimport { getUtopiaID } from...</td><td>false</td><td>87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/components/canvas/ui-jsx-canvas-renderer/ui-j...</td><td>25</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>e99be2420d3eea4df46bfe25b87456fa96c44a2a</td><td>16640</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import { render } from &#x27;@testing-library/react&#x27;\\nimport { renderHook } from &#x27;@testing-library/rea...</td><td>false</td><td>4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/inspector/common/property-path-hoo...</td><td>25</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>76d477f6e3abf430e6a017da8dbbd4ca93179479</td><td>38950</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import { act, fireEvent } from &#x27;@testing-library/react&#x27;\\nimport { jsxElement, simpleAttribute } f...</td><td>false</td><td>editor/src/components/canvas/controls/insert-mode.spec.browser2.tsx</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>3463535e0eb2993aa3e205d4a1a30b908425b1ce</td><td>7623</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as deepEqual from &#x27;fast-deep-equal&#x27;\\nimport { useContextSelector } from &#x27;use-context-sel...</td><td>false</td><td>87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/components/inspector/common/longhand-shorthan...</td><td>33</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>445c2597d6481dfe7d1b0218b3df241238198ed6</td><td>13225</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as React from &#x27;react&#x27;\\nimport { useEditorState } from &#x27;../../../components/editor/store/...</td><td>false</td><td>86903699cc92e4edb456342939e779c7cbef47a4:editor/src/components/canvas/controls/breadcrumb-trail.tsx</td><td>21</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>04d50d99f62f17ed84b49b38434e27b46958b4f6</td><td>2611</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import * as Babel from &#x27;@babel/standalone&#x27;\\nimport * as BabelTraverse from &#x27;@babel/traverse&#x27;\\nimp...</td><td>false</td><td>editor/src/core/workers/parser-printer/parser-printer-transpiling.ts</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>7e136971c4f6e181eaf3dd14b8fd103c807d0d2f</td><td>9523</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>import { defaultIfNull } from &#x27;../utils&#x27;\\nimport * as React from &#x27;react&#x27;\\nimport { NormalisedFram...</td><td>false</td><td>15e8d36fa5626d208c599919bc17c48707118609:utopia-api/src/primitives/common.tsx</td><td>43</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>423d08ad9b381f613f25683ed87987f6dda65b0a</td><td>9509</td><td>TypeScript</td><td>false</td><td>2023-07-08</td><td>5</td></tr>\n", "<tr><td>/* eslint-disable jest/expect-expect */\\nimport * as Mock<PERSON>ea<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from &#x27;@react-three/fiber...</td><td>false</td><td>editor/src/components/canvas/ui-jsx-canvas-renderer/ui-jsx-canvas-spy-wrapper-third-party.spec.br...</td><td>200</td><td>cb2982b0-fd89-4d06-a8b5-03ac0df766d7</td><td>De30/utopia</td><td>github/520937076</td><td>c450b4b1e229e1e4cf3be7c542303246e36d9aea</td><td>10550</td><td>TypeScript</td><td>true</td><td>2023-07-08</td><td>5</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+----------------------------------------------------------------------------------------------------+---------+----------------------------------------------------------------------------------------------------+------------+------------------------------------+-----------+----------------+----------------------------------------+-----+----------+-------+----------+----+\n", "|                                                                                             content|is_binary|                                                                                                path|pct_modified|                             repo_id|  repo_name|       repo_root|                                     sha| size|      lang|is_head|       day|hour|\n", "+----------------------------------------------------------------------------------------------------+---------+----------------------------------------------------------------------------------------------------+------------+------------------------------------+-----------+----------------+----------------------------------------+-----+----------+-------+----------+----+\n", "|const Prettier = jest != null ? require('prettier') : require('prettier/standalone') // TODO spli...|    false|                                           editor/src/components/canvas/ui-jsx-canvas.test-utils.tsx|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|6b48e1f531e0ebb3ac9c9fe75973d63a0f917664|15230|TypeScript|   true|2023-07-08|   5|\n", "|import type {\\n  TemplatePath,\\n  id,\\n  StaticTemplatePath,\\n  Imports,\\n} from '../../core/shar...|    false|               4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/editor/editor-modes.ts|          46|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|07d8091ffaa720a693547998006cbfeb9197c18f| 3389|TypeScript|  false|2023-07-08|   5|\n", "|import * as React from 'react'\\nimport { filterOldPasses } from '../../core/workers/ts/ts-worker'...|    false|  c1c1194e9bcf7ecc27bf0cef6783f29d4e4a1b88:editor/src/components/canvas/canvas-wrapper-component.tsx|          14|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|1c62d3bc480aad511550281be265e8c415a3caea| 6694|TypeScript|  false|2023-07-08|   5|\n", "|import { Interpolation, Theme } from '@emotion/react'\\nimport * as React from 'react'\\nimport {\\n...|    false|                         d71efc484ee5235b9739b3dcf19eabd2933d148c:utopia-api/src/primitives/view.tsx|          50|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|7d293d330ca304e33f11c1e1c6c27cf052a9084e|  749|TypeScript|  false|2023-07-08|   5|\n", "|import {\\n  childPaths,\\n  deletePath,\\n  ensureDirectoryExists,\\n  exists,\\n  readDirectory,\\n  ...|    false|                                                                 utopia-vscode-common/src/mailbox.ts|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|1a7f7c519c4deb7ad70fa418c3d44af96c1c2745| 8234|TypeScript|   true|2023-07-08|   5|\n", "|/** @jsx jsx */\\nimport { jsx } from '@emotion/react'\\nimport * as Path from 'path'\\nimport * as ...|    false|             3110af410e7c4874a086bf2efdd20019091ed9a6:editor/src/components/filebrowser/fileitem.tsx|          54|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|ab26a1aff5d05bc20a8c0bb55245d182128d3335|22428|TypeScript|  false|2023-07-08|   5|\n", "|/** @jsx jsx */\\nimport { jsx } from '@emotion/react'\\nimport * as React from 'react'\\nimport {\\n...|    false|4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/navigator/navigator-item/navigator...|          36|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|735cbce240d67b22578ed43bd955398b27b8d876|13253|TypeScript|  false|2023-07-08|   5|\n", "|// Should allow us to guard against a type being refactored into something\\n// which Set allows b...|    false|                                                                 editor/src/core/shared/set-utils.ts|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|3964f8ad4375e21f093cb97ed056d79aa4c133d8|  585|TypeScript|   true|2023-07-08|   5|\n", "|import {\\n  addFileToProjectContents,\\n  projectContentFile,\\n  ProjectContentsTree,\\n  ProjectCo...|    false|                                       editor/src/sample-projects/sample-project-utils.test-utils.ts|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|c8f7adfbb7c0a5dbbe4f0e93ae65f1d9e7c1e7aa| 3705|TypeScript|   true|2023-07-08|   5|\n", "|import * as R from 'ramda'\\nimport {\\n  SceneMetadata,\\n  StaticInstancePath,\\n  PropertyPath,\\n ...|    false|                       87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/core/model/scene-utils.ts|          25|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|8f1916e248581cd080047836d94dfc636a0ad514|11478|TypeScript|  false|2023-07-08|   5|\n", "|                                                                                     packages: .\\n\\n|    false|                                                                                server/cabal.project|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|b764c340a6236bfa178217064eeff4940d4c4482|   13|          |   true|2023-07-08|   5|\n", "|/* eslint-disable no-console */\\nrequire('dotenv').config({ path: 'src/.env' })\\nimport * as pupp...|    false|                                                             puppeteer-tests/src/performance-test.ts|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|9fb8ad1093451b0a344f92f646909add7f540777|23079|TypeScript|   true|2023-07-08|   5|\n", "|import * as React from 'react'\\nimport { MapLike } from 'typescript'\\nimport { getUtopiaID } from...|    false|87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/components/canvas/ui-jsx-canvas-renderer/ui-j...|          25|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|e99be2420d3eea4df46bfe25b87456fa96c44a2a|16640|TypeScript|  false|2023-07-08|   5|\n", "|import { render } from '@testing-library/react'\\nimport { renderHook } from '@testing-library/rea...|    false|4b96a95702f511d219f905bb9e6c9c00aeb3f22c:editor/src/components/inspector/common/property-path-hoo...|          25|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|76d477f6e3abf430e6a017da8dbbd4ca93179479|38950|TypeScript|  false|2023-07-08|   5|\n", "|import { act, fireEvent } from '@testing-library/react'\\nimport { jsxElement, simpleAttribute } f...|    false|                                 editor/src/components/canvas/controls/insert-mode.spec.browser2.tsx|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|3463535e0eb2993aa3e205d4a1a30b908425b1ce| 7623|TypeScript|   true|2023-07-08|   5|\n", "|import * as deepEqual from 'fast-deep-equal'\\nimport { useContextSelector } from 'use-context-sel...|    false|87604ef20cdef3a6d5a99a2a578840375caf55d4:editor/src/components/inspector/common/longhand-shorthan...|          33|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|445c2597d6481dfe7d1b0218b3df241238198ed6|13225|TypeScript|  false|2023-07-08|   5|\n", "|import * as React from 'react'\\nimport { useEditorState } from '../../../components/editor/store/...|    false| 86903699cc92e4edb456342939e779c7cbef47a4:editor/src/components/canvas/controls/breadcrumb-trail.tsx|          21|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|04d50d99f62f17ed84b49b38434e27b46958b4f6| 2611|TypeScript|  false|2023-07-08|   5|\n", "|import * as Babel from '@babel/standalone'\\nimport * as BabelTraverse from '@babel/traverse'\\nimp...|    false|                                editor/src/core/workers/parser-printer/parser-printer-transpiling.ts|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|7e136971c4f6e181eaf3dd14b8fd103c807d0d2f| 9523|TypeScript|   true|2023-07-08|   5|\n", "|import { defaultIfNull } from '../utils'\\nimport * as React from 'react'\\nimport { NormalisedFram...|    false|                       15e8d36fa5626d208c599919bc17c48707118609:utopia-api/src/primitives/common.tsx|          43|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|423d08ad9b381f613f25683ed87987f6dda65b0a| 9509|TypeScript|  false|2023-07-08|   5|\n", "|/* eslint-disable jest/expect-expect */\\nimport * as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@react-three/fiber...|    false|editor/src/components/canvas/ui-jsx-canvas-renderer/ui-jsx-canvas-spy-wrapper-third-party.spec.br...|         200|cb2982b0-fd89-4d06-a8b5-03ac0df766d7|De30/utopia|github/520937076|c450b4b1e229e1e4cf3be7c542303246e36d9aea|10550|TypeScript|   true|2023-07-08|   5|\n", "+----------------------------------------------------------------------------------------------------+---------+----------------------------------------------------------------------------------------------------+------------+------------------------------------+-----------+----------------+----------------------------------------+-----+----------+-------+----------+----+\n", "only showing top 20 rows"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["23/07/31 17:59:07 ERROR Utils: Uncaught exception in thread kubernetes-executor-pod-polling-sync\n", "io.fabric8.kubernetes.client.KubernetesClientException: An error has occurred.\n", "\tat io.fabric8.kubernetes.client.KubernetesClientException.launderThrowable(KubernetesClientException.java:129)\n", "\tat io.fabric8.kubernetes.client.KubernetesClientException.launderThrowable(KubernetesClientException.java:122)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.OperationSupport.waitForResult(OperationSupport.java:543)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:427)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:392)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:93)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.$anonfun$run$1(ExecutorPodsPollingSnapshotSource.scala:91)\n", "\tat org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1509)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.run(ExecutorPodsPollingSnapshotSource.scala:74)\n", "\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)\n", "\tat java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)\n", "\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "Caused by: java.util.concurrent.TimeoutException\n", "\tat java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1886)\n", "\tat java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2021)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.OperationSupport.waitForResult(OperationSupport.java:520)\n", "\t... 12 more\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49208\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596010926072\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47620\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596010926072\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49214\n", "java.lang.IllegalArgumentException: Too large frame: 1586111495861764088\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47626\n", "java.lang.IllegalArgumentException: Too large frame: 1586111495861764088\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49216\n", "java.io.IOException: Connection reset by peer\n", "\tat java.base/sun.nio.ch.FileDispatcherImpl.read0(Native Method)\n", "\tat java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)\n", "\tat java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:276)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:233)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:223)\n", "\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:356)\n", "\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)\n", "\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\n", "\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47634\n", "java.io.IOException: Connection reset by peer\n", "\tat java.base/sun.nio.ch.FileDispatcherImpl.read0(Native Method)\n", "\tat java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)\n", "\tat java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:276)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:233)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:223)\n", "\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:356)\n", "\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)\n", "\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\n", "\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49232\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596010926072\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47642\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596010926072\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49240\n", "java.lang.IllegalArgumentException: Too large frame: 1586112601866174457\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47656\n", "java.lang.IllegalArgumentException: Too large frame: 1586112601866174457\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:49250\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596967227384\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:29 WARN TransportChannelHandler: Exception in connection from /************:47664\n", "java.lang.IllegalArgumentException: Too large frame: 1586112596967227384\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:49260\n", "java.lang.IllegalArgumentException: Too large frame: 1586112604936404986\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:47680\n", "java.lang.IllegalArgumentException: Too large frame: 1586112604936404986\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:49268\n", "java.lang.IllegalArgumentException: Too large frame: 1586111504149708794\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:47686\n", "java.lang.IllegalArgumentException: Too large frame: 1586111504149708794\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:49270\n", "java.lang.IllegalArgumentException: Frame length should be positive: -9205356538800101640\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:150)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:30 WARN TransportChannelHandler: Exception in connection from /************:47702\n", "java.lang.IllegalArgumentException: Frame length should be positive: -9205356538800101640\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:150)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:36 WARN TransportChannelHandler: Exception in connection from /************:47714\n", "java.lang.IllegalArgumentException: Too large frame: 5135603447292250188\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:36 WARN TransportChannelHandler: Exception in connection from /************:49274\n", "java.lang.IllegalArgumentException: Too large frame: 5135603447292250188\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:42 WARN TransportChannelHandler: Exception in connection from /************:57278\n", "java.io.IOException: Connection reset by peer\n", "\tat java.base/sun.nio.ch.FileDispatcherImpl.read0(Native Method)\n", "\tat java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)\n", "\tat java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:276)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:233)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:223)\n", "\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:356)\n", "\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)\n", "\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\n", "\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:42 WARN TransportChannelHandler: Exception in connection from /************:41310\n", "java.io.IOException: Connection reset by peer\n", "\tat java.base/sun.nio.ch.FileDispatcherImpl.read0(Native Method)\n", "\tat java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)\n", "\tat java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:276)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:233)\n", "\tat java.base/sun.nio.ch.IOUtil.read(IOUtil.java:223)\n", "\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:356)\n", "\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)\n", "\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\n", "\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:44 WARN TransportChannelHandler: Exception in connection from /************:60492\n", "java.lang.IllegalArgumentException: Too large frame: 270583136248\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:44 WARN TransportChannelHandler: Exception in connection from /************:60498\n", "java.lang.IllegalArgumentException: Too large frame: 274878103544\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:44 WARN TransportChannelHandler: Exception in connection from /************:60508\n", "java.lang.IllegalArgumentException: Too large frame: 270583136248\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:45 WARN TransportChannelHandler: Exception in connection from /************:60518\n", "java.lang.IllegalArgumentException: Too large frame: 266288168952\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:45 WARN TransportChannelHandler: Exception in connection from /************:52582\n", "java.lang.IllegalArgumentException: Too large frame: 270583136248\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:45 WARN TransportChannelHandler: Exception in connection from /************:52596\n", "java.lang.IllegalArgumentException: Too large frame: 274878103544\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:45 WARN TransportChannelHandler: Exception in connection from /************:52600\n", "java.lang.IllegalArgumentException: Too large frame: 270583136248\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:45 WARN TransportChannelHandler: Exception in connection from /************:52610\n", "java.lang.IllegalArgumentException: Too large frame: 266288168952\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:47 ERROR Utils: Uncaught exception in thread kubernetes-executor-pod-polling-sync\n", "io.fabric8.kubernetes.client.KubernetesClientException: Operation: [list]  for kind: [Pod]  with name: [null]  in namespace: [tenant-augment-eng]  failed.\n", "\tat io.fabric8.kubernetes.client.KubernetesClientException.launderThrowable(KubernetesClientException.java:159)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:429)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:392)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:93)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.$anonfun$run$1(ExecutorPodsPollingSnapshotSource.scala:91)\n", "\tat org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1509)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.run(ExecutorPodsPollingSnapshotSource.scala:74)\n", "\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)\n", "\tat java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)\n", "\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "Caused by: java.io.IOException: timeout\n", "\tat io.fabric8.kubernetes.client.dsl.internal.OperationSupport.waitForResult(OperationSupport.java:535)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:427)\n", "\t... 11 more\n", "Caused by: java.net.SocketTimeoutException: timeout\n", "\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.java:672)\n", "\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.java:680)\n", "\tat okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.java:153)\n", "\tat okhttp3.internal.http2.Http2Codec.readResponseHeaders(Http2Codec.java:131)\n", "\tat okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:88)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:147)\n", "\tat okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:45)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:147)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:121)\n", "\tat okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:93)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:147)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:121)\n", "\tat okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:147)\n", "\tat okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:127)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:147)\n", "\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:121)\n", "\tat okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:257)\n", "\tat okhttp3.RealCall$AsyncCall.execute(RealCall.java:201)\n", "\tat okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)\n", "\t... 3 more\n", "23/07/31 17:59:47 WARN TransportChannelHandler: Exception in connection from /************:60530\n", "java.lang.IllegalArgumentException: Too large frame: 713014902776\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "23/07/31 17:59:47 WARN TransportChannelHandler: Exception in connection from /************:52624\n", "java.lang.IllegalArgumentException: Too large frame: 713014902776\n", "\tat org.sparkproject.guava.base.Preconditions.checkArgument(Preconditions.java:119)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.decodeNext(TransportFrameDecoder.java:148)\n", "\tat org.apache.spark.network.util.TransportFrameDecoder.channelRead(TransportFrameDecoder.java:98)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n", "\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n", "\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n", "\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\n", "\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\n", "\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\n", "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\n", "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n", "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n", "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n"]}], "source": ["path = 's3a://augment-github/file_content/processed/'\n", "spark.read.option('basePath', path).parquet(path)"]}, {"cell_type": "code", "execution_count": null, "id": "4607cee3-4b6a-472f-9cec-e965aa0a7541", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}
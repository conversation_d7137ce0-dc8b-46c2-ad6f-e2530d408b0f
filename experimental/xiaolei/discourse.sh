# For running gmane scraping on GCP

code_path=/home/<USER>/augment/research/data/collection/discourse/
datestr=$(date +%Y-%m-%d)
path=~/discourse/$datestr
mkdir -p $path

for i in {1..10}; do
  echo "Iteration $i"
  python3 $code_path/process_discourse.py --storage-path ~/discourse/$datestr/ --batches 1 --batchsize 2000 --cooldown 1.0
  gsutil -m mv $path/*.gz "gs://discourse_scrape/date=$datestr/"
  sleep 3600
done

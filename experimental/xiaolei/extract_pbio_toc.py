import json
from pathlib import Path

base = Path(
    "/mnt/efs/spark-data/shared/nl-datasets/books/comp/programming-books.io/www.programming-books.io/s"
)
output = Path(
    "/mnt/efs/spark-data/shared/nl-datasets/books/comp/programming-books.io/links"
)

START = "gTocItems = "
known = set()
with output.open("w+") as f:
    for path in base.glob("app-*.js"):
        name = path.relative_to(base).stem
        url_base = "www.programming-books.io/essential/" + name[len("app-") :]
        text = path.read_text().strip()
        if not text:
            print("Empty file")
            continue
        if not text.startswith(START):
            print("Not a toc")
            continue
        text = text[len(START) :]
        if text.endswith(";"):
            text = text[:-1]
        content = json.loads(text)
        for item in content:
            file_name = item[0].split("#")[0]
            full_url = f"{url_base}/{file_name}"
            if full_url in known:
                continue
            known.add(full_url)
            f.write(f"{full_url}\n")

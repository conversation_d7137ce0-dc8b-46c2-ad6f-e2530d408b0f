# Behind the Scenes: Data Processing for Large Language Models at Scale

Processing data for large language models (LLMs) is often the unsung hero of AI development - a complex, resource-intensive operation that happens behind the scenes. At Augment, we've developed sophisticated pipelines to handle this challenge at scale. Here's a peek behind the curtain at how we tackle this monumental task.

## The Challenge: Processing at Petabyte Scale

Our data pipeline isn't just big - it's massive. We're processing petabytes of data across a diverse array of tasks, each with its own unique challenges. Some examples of our data processing tasks include:

- **License Compliance**: Strict filtering to ensure we're only using repos with explicitly permissive licenses.
- **Data Collection**: Collecting, generating, and filtering content from a wide variety of sources, and converting them to standardized and structured formats for later processing and analysis.
- **Code Analysis**: Wide-ranging analysis of the entire permissive source code space, including statistical analysis at file and repo levels, as well as analyzing the topological structure of Abstract Syntax Trees (AST) across 13 top programming languages.
- **Deduplication**: Identifying and removing near-duplications at file and repo levels.
- **Dataset Creation**: Creating datasets for training and evaluation of LLMs, from tokenization, packing, and Fill-In-the-Middle (FIM) transformations for pretraining and continued training, to more complex structured datasets, such as combining PR review comments, issues, code changes, and relevant file contents.
- **Embedding Generation**: Building and managing millions of embedding databases to improve models' ability to leverage retrieval-augmented generation (RAG).

## Expecting the Unexpected: Managing Chaos at Scale

When operating at this scale, Murphy's Law isn't just a possibility - it's a daily reality. We regularly face challenges like:
- Rogue repos mislabeling their licenses
- Third-party packages crashing with segmentation faults or having subtle memory leaks that cause OOM over time and are difficult to debug or fix
- Network outages and storage failures interrupting long-running processes
- GPU failures during computational tasks
- Memory usage spikes that can be very difficult to predict

## Our Solution: A Robust Yet Simple Architecture

To facilitate efficient research and development, we've designed our pipeline to allow researchers to manage the entire process - from initial analysis to production deployment. Our goal is to minimize complexity while maintaining reliability and stability.

We've achieved this through a streamlined approach using Kubernetes (k8s) and Apache Spark. While alternatives like Dask or Ray offer some benefits, they don't fully meet our needs for data-intensive operations at scale. Spark excels in large-scale joins, group-bys, and transformations on multi-terabyte datasets, making it essential for our data pipelines.

Instead of introducing additional data pipelines that would force researchers to manage multiple data processing paradigms and infrastructures, we start with Spark, which can robustly handle the scale of data we are processing. We then introduce several utilities to bring missing capabilities into it, allowing researchers to focus on writing the business logic directly in Python.

### 1. Infrastructure Management

We leverage Kubernetes and Spark to handle the heavy lifting of:
- Dynamic resource allocation across tasks
- Automatic scaling based on workload
- Retry mechanisms for recoverable failures
- Job scheduling and distribution

Spark over Kubernetes is very mature and widely known, and can easily handle routine operations such as string-based cleaning and filtering, statistical analysis, joining multiple datasets, and group-bys at multiple TB scale. They are especially well-suited for earlier stages of data pipelines where the operations are logically relatively simple but involve extremely large amounts of data and combining multiple sources.

### 2. Process Isolation

Later stages of data pipelines usually involve a smaller scale of data (usually one to a few hundred GB) and trivially parallelizable workloads, but the individual workloads are complex and require third-party packages such as `treesitter`, `pytorch`, `tiktoken`, etc. Some of these need to be performed with GPUs and often have highly variant memory usage.

To gracefully handle failures in this stage, we've developed utilities that:
- Launch User-Defined Functions (UDFs) in isolated subprocesses to contain segmentation faults, stack overflows, and other non-recoverable errors from third-party packages
- Allow the main pipeline to continue even when individual components fail with such irrecoverable errors
- Use the controller process to collect critical output and error information, as well as other data such as memory profiles, timing information, and failure codes, and pass these as data frame output

### 3. Handling Out-of-Memory (OOM) Failures

To address the challenge of highly variant memory usage across different tasks, we've implemented a strategy that involves:
- Submitting 4-6 tasks per Spark executor with moderate memory limits
- Configuring Spark to retry failed tasks up to 3 times
- Rerunning failed tasks with higher memory allocation after the initial attempts

This approach allows us to handle a small portion of tasks that require high memory usage while keeping overall resource usage manageable.


## Real-World Pipeline Example: License-Compliant Data Collection

At Augment, we give special attention to ensuring that all source code data we use in training is properly licensed. We only use data that is explicitly permissive. To address the challenge of rogue forks that inappropriately alter the license of the original repo, we:
- Do not collect any repos labeled as forks
- Perform human inspection on all large repos bigger than 200MB in size to remove rogue forks
- Apply multiple layers of filtering to ensure data quality, including length, content, and entropy-based filtering, AST-based validation, and cross-sample comparison for deduplication

### Multi-Stage Data Processing (probably cut this for now it's already too long)
We apply multiple layers of filtering to ensure data quality:
- Length, content, and entropy-based filtering to remove low-quality data
- Removing files with large embedded data with heuristics
- Per language KL divergence-based filtering to remove files with unnatural word length distribution
- AST-based validation to ensure code samples are well-formed
    - Remove broken and incomplete code samples
    - Identify trees with very large leaf nodes or subtrees with extremely high multiplicity
    - Analyze ratio and size of comments and docstrings, and distribution of structure such as functions, classes, methods, structs etc
- Directory structure analysis to identify highly repetitive directories that are likely to be auto-generated or test data
- Cross-sample comparison for deduplication
- Language-specific processing for multiple programming languages

**RAG-Optimized Dataset Creation (All written by Augie, need to work on it a bit)**

Retrieval-Augmented Generation (RAG) improves the codebase awareness of LLMs by
providing it with additional context from relevant code chunks retrieved from a
codebase.  When paired with a good embedding model, RAG can significantly
improve the quality of generated code.  However, an LLM that is good at
code and Fill-In-The-Middle (FIM) style generation is not sufficient to fully
take advantage of the additional retrieved information.  Some of these challenges include:

- retrieved content, along with some additional metadata, need to be provided in a specific prompt format and the model needs to understand that they are retrieved content, not part of the current file or multishot templates.
- source code files are often large and needs to be chunked, and LLM
will need to adapt to context information that are not complete files.
- even very accurate embedding models will retrieve some irrelevant
content, especially if we want to optimize for the coverage of the context awareness.
- a pretrained code model is usually trained to generate a random section of a source code file, with no awareness of the scope and structure of the code itself, or proper generation length that is appropriate when used by a programmer.
- the model should adapt to the style of the codebase when it is generating code.

We can address all these challenges and ensure generation models takes full of
advantage of the retrieved content through a finetuning process. This essentially
involves creating a dataset that is representative of real world usage of RAG,
with a collection of problems that matches precisely the real world usage that the model may face, and the target generations that would be most useful to a programmer.

We use the following process to generate this dataset:

1. **Repository and File Selection**: The dataset should be unbiased and comprehensive,
covering all the major programming languages and different project types and sizes.
At the same time, we need to exclude contents that a user will not be creating, such
as data files and autogenerated content, or content with very low quality.
2. **Fill-in-the-Middle (FIM) Sample Generation**: creates FIM samples from files in
each repositories.  We have developed a statistical sampling process that also
takes the full syntax tree of the file into account to generate
samples ranges that matches real world usage and desired behvior.
3. **Building Retrieval Databases**: For each repository, we build a retrieval database.
It is essential that the exact same processing steps are used as
in inference time, including the same chunking and tokenization process, embedding model model, and *all* of
of the files that might be indexed during real usage, no matter the size of the repository.
4. **Retrieval Augmentation**: Performing retrieval for every sample problem using the retrieval database.
Again, the process needs to be identical to the real world usage.
5. **Prompt Generation**: Converted the retrieved chunks and FIM samples into prompts that the model would see.
6. **Dataset Export**: Finally, the processed data is exported into datasets that are convenient for consumption
from torch dataloaders.

Similar to many other data pipelines at Augment, this is a very involved and challenging process.

To ensure unbiased and comprehensive sampling, we are building more than 100k retrieval databases ranging from just a few files to tens of thousands of files.  We are also performing AST analysis on more than 30 million source code files with a wide size and language distribution.

Many components, from the sampling process, to chunking mechanism, to the prompt format,
need to be constantly iterated and adjusted to continuously improve the quality of the quality of the model and improve efficiency.


## The Road Ahead

As AI models continue to grow in size and capability, the importance of robust, scalable data processing pipelines only increases. Our approach at Augment demonstrates that with the right architecture and tools, it's possible to handle these challenges while maintaining simplicity and reliability in the core workflow.

We're continuously improving our pipeline, to enable more complex tasks and
to make the process of creating new models and also iterating and improving on existing more streamline.

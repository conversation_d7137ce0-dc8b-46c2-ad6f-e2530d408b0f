-- Basic statistics
WITH request_counts AS (
  SELECT
  meta.user_id,
  meta.opaque_user_id,
  meta.tenant,
  COUNT(DISTINCT request.request_id) as requests,
  COUNT(DISTINCT
    CASE WHEN (
      JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added') IS NULL OR
      ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added')) = 0
    )
    AND (
      JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted') IS NULL OR
      ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted')) = 0
    )
    AND (
      JSON_QUERY(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') IS NULL
      OR
      JSON_VALUE(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') = ""
    ) THEN NULL ELSE request.request_id END
  ) as request_with_blobs,
  count(distinct meta.session_id) as sessions

  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.chat_host_request`  as request
  JOIN `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata` as meta on request.request_id = meta.request_id
  WHERE TIMESTAMP_TRUNC(request.time, DAY) > TIMESTAMP("2025-02-01")
  AND (
    JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added') IS NULL OR
    ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added')) = 0
  )
  AND (
    JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted') IS NULL OR
    ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted')) = 0
  )
  AND (
    JSON_QUERY(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') IS NULL
      OR
    JSON_VALUE(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') = ""
  )
  GROUP BY 1, 2, 3
)
-- Suspicous is to put it lightly here.
-- We combine excessive session creation with never having a blob for a user here,
-- most are likely guilty as charged

  SELECT
    distinct opaque_user_id
  FROM request_counts
  WHERE request_with_blobs = 0 and requests > 100 and sessions / requests > 0.8

UNION DISTINCT

  -- Users that have invalid request ids, sessions ids, or identical request/session ids
  -- or use invalid user agent
  SELECT DISTINCT
     opaque_user_id
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
  WHERE time > '2025-03-01' AND request_type in ('CHAT', 'AGENT_CHAT')
  AND (
    -- invalid session id
    REGEXP_CONTAINS(
      session_id,
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    )
    AND NOT REGEXP_CONTAINS(
      session_id,
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
    )
  OR
    -- invalid request id
    REGEXP_CONTAINS(
      request_id,
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    )
    AND NOT REGEXP_CONTAINS(
      request_id,
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
    )
  OR request_id = session_id
  OR user_agent in (
      'Go-http-client/2.0',
      'Python/3.13 aiohttp/3.11.14',
      'undici',
      'Deno/2.1.10',
      'Reqable/2.33.3',
      'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
      'Python/3.12 aiohttp/3.11.14',
      'Deno/2.2.3',
      'Apifox/1.0.0 (https://apifox.com)',
      'Python/3.12 aiohttp/3.9.5',
      'axios/1.8.4',
      'Python/3.7 aiohttp/3.8.6',
      'Crow/Beast HTTP Client',
      'Bun/1.2.4',
      'chrome',
      'qa/JS 4.83.0',
      'Deno/2.2.5',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) CherryStudio/1.1.8 Chrome/126.0.6478.234 Electron/31.7.6 Safari/537.36',
      'python-requests/2.32.3',
      'PostmanRuntime/7.43.2',
      'Augment.openai-adapter/1.0.0',
      'node-fetch',
      'node',
      'PostmanRuntime/7.37.3',
      'python-requests/2.31.0',
      'Reqable/2.33.5'
    )
  )

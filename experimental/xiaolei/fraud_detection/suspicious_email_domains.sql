-- Find email domains that contain suspicious users
-- Some domains seem to be favored by fraudsters
-- due to the easiness to get unique email addresses
SELECT
   REGEXP_EXTRACT(meta.user_id, r'@(.*)') as domain,
   count(distinct CASE WHEN sus.opaque_user_id IS NOT NULL THEN meta.opaque_user_id ELSE NULL END) as sus_users,
   count(distinct meta.opaque_user_id) as total_users,
   COUNT(distinct CASE WHEN sus.opaque_user_id IS NOT NULL THEN meta.request_id ELSE NULL END) as sus_requests,
   COUNT(distinct meta.request_id) as total_requests,
   COUNT(distinct CASE WHEN sus.opaque_user_id IS NOT NULL THEN meta.request_id ELSE NULL END) /COUNT(distinct meta.request_id) AS sus_pct
FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata` as meta
LEFT JOIN `system-services-dev.suspicious_activity_montior.suspicious_users` AS sus ON sus.opaque_user_id = meta.opaque_user_id
WHERE time > '2025-03-21' AND request_type in ('CHAT', 'AGENT_CHAT')
GROUP BY 1
ORDER BY 4 DESC

WITH meta AS (
  SELECT DISTINCT
     request_id, tenant, session_id, user_agent, opaque_user_id
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
  WHERE time > '2025-03-17' AND request_type IN ('CHAT', 'AGENT_CHAT')
)
SELECT
chat.time,
meta.*,
sus.opaque_user_id IS NOT NULL as sus
FROM meta
JOIN `system-services-prod.us_prod_request_insight_analytics_dataset.chat_host_request` as chat on chat.request_id = meta.request_id
LEFT JOIN `system-services-dev.suspicious_activity_montior.suspicious_users` AS sus ON sus.opaque_user_id = meta.opaque_user_id

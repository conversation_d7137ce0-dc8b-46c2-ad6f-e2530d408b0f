-- Identify recent worst offenders

SELECT
  sus.opaque_user_id,
  tenant,
  COUNT(*) AS count,
  ANY_VALUE(request_id) as sample_request_id
FROM  `system-services-dev.suspicious_activity_montior.suspicious_vs_overall_chat` as sus
LEFT JOIN `system-services-dev.suspicious_activity_montior.throttled_users` as throttled
   ON throttled.opaque_user_id = sus.opaque_user_id
WHERE
  (throttled.opaque_user_id IS NULL) and
  time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 5 HOUR)  and sus.sus
GROUP BY
  1, 2
HAVING COUNT(*)> 1000
ORDER BY
  3 DESC;

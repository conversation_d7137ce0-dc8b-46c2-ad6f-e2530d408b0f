{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "from research.data.spark.utils import k8s_session\n", "\n", "from research.eval.harness.factories import create_retriever\n", "from research.retrieval.types import Chunk, Document\n", "from research.retrieval.tests.data import patchcore_test_data\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/08/11 07:59:31 WARN Utils: Your hostname, xiaolei-dev resolves to a loopback address: *********; using ************** instead (on interface enp2s0)\n", "23/08/11 07:59:31 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "\u001b[33m  WARNING: The script hydra is installed in '/mnt/efs/augment/python_env/xiaolei-dev/myaugment-augment-xiaolei-dev/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "\u001b[0m23/08/11 08:00:50 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}], "source": ["spark = k8s_session()\n", "# A realistic example, Starcoder Python set.  We take all Python repos\n", "py_df = spark.read.parquet('s3a://starcoder/raw/lang=python').select(\n", "    'id',\n", "    F.col('max_stars_repo_name').alias('repo'),\n", "    F.col('max_stars_repo_path').alias('path'),\n", "    'content'\n", ")\n", "py_repos = py_df.groupBy(\"repo\").agg(\n", "    F.collect_list(F.struct(\"id\", \"path\", \"content\")).alias('input_list')\n", ").limit(100000).repartition(128)\n", "py_repos.write.mode('overwrite').parquet('s3a://augment-temporary/starcoder_py_repos/')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, Generator, Iterable, List, Tuple\n", "from pyspark.sql.types import Row\n", "import pandas as pd\n", "import torch\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "TOP_K = 10\n", "MAX_TEXT_LEN = 2000\n", "\n", "\n", "@F.pandas_udf(\n", "    \"\"\"array<struct<\n", "    content string,\n", "    path string,\n", "    retrieved_chunks array<struct<\n", "       text string,\n", "       parent_path string,\n", "       char_offset int,\n", "       line_offset int,\n", "       length int,\n", "       length_in_lines int,\n", "       score float\n", "    >>\n", ">>\n", "\"\"\"\n", ")\n", "def process_repo(batch_of_arrays: pd.Series)-> pd.Series:\n", "    \"\"\"Minimal retrieval example.\n", "\n", "    With a list of docs simplely add them in order, and\n", "    retrieve against the next doc before adding it to db.\n", "    \"\"\"\n", "    # somehow, megatron couldn't find gpu sometimes if i\n", "    # don't import torch first.  /shrug\n", "    print(\"GPU? \", torch.cuda.is_available())\n", "    retrieval_database = create_retriever(\n", "        {\n", "            \"chunker\": \"line_level\",\n", "            \"name\": \"contrieve_350m\",\n", "        }\n", "    )\n", "    print('Creating scorer...')\n", "    retrieval_database.scorer.load()\n", "    batch_of_results = []\n", "    for rows in batch_of_arrays:\n", "        print('Moving on to the next repo.  len=', len(rows))\n", "        results = []\n", "        if len(rows) == 1:\n", "            print('Skipping repo.')\n", "            batch_of_results.append(results)\n", "            continue\n", "        retrieved = []\n", "        # add all docs one by one.  retrieve document\n", "        # N after adding documents 1 to N-1\n", "        for row, next_row in zip(rows, rows[1:]):\n", "            doc = Document(\n", "                text=row[\"content\"][:MAX_TEXT_LEN],\n", "                id=row[\"id\"],\n", "                path=row[\"path\"],\n", "                meta={},\n", "            )\n", "            retrieval_database.add_doc(doc)\n", "            query_text = next_row[\"content\"]\n", "            query_path = next_row[\"path\"]\n", "            try:\n", "                chunks, scores = retrieval_database.query(ModelInput(prefix=query_text, path=query_path), top_k=TOP_K)\n", "            except:\n", "                continue\n", "            retrieved = []\n", "            total_length = 0\n", "            for chunk, score in zip(chunks, scores):\n", "                total_length += chunk.length\n", "                retrieved.append(\n", "                    {\n", "                        \"text\": chunk.text,\n", "                        \"parent_path\": chunk.parent_doc.path,\n", "                        \"char_offset\": chunk.char_offset,\n", "                        \"line_offset\": chunk.line_offset,\n", "                        \"length\": chunk.length,\n", "                        \"length_in_lines\": chunk.length_in_lines,\n", "                        \"score\": score,\n", "                    }\n", "                )\n", "            results.append(\n", "                Row(\n", "                    query_text=query_text[:MAX_TEXT_LEN],\n", "                    query_path=query_path,\n", "                    retrieved_chunks=retrieved,\n", "                )\n", "            )\n", "        print(f'Number of chunks retrieved for last file: {len(retrieved)}  total length: {total_length}')\n", "        retrieval_database.remove_all_docs()\n", "        batch_of_results.append(results)\n", "    return pd.Series(batch_of_results)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/08/11 08:11:37 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "\u001b[33m  WARNING: The script hydra is installed in '/mnt/efs/augment/python_env/xiaolei-dev/myaugment-augment-xiaolei-dev/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "                                                                                \r"]}], "source": ["# Use UDF to test retrieval for all Python repos\n", "spark.stop()\n", "spark = k8s_session(\n", "    gpu_type=\"Quadro_RTX_5000\",\n", "    gpu_count=1,\n", "    min_workers=0,\n", "    max_workers=32,\n", "    conf={\"spark.task.cpus\": 5 },\n", ")\n", "input_df = spark.read.parquet('s3a://augment-temporary/starcoder_py_repos/')\n", "df = input_df.select(\"repo\", <PERSON>.explode(process_repo('input_list')).alias(\"result\")).select(\"repo\", \"result.*\")\n", "df.write.mode('overwrite').parquet('s3a://augment-temporary/retrieval_results')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}
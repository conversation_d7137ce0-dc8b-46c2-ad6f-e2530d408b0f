# Experimental

This directory contains code that is experimental in nature.

Feel free to create your own directory under it (by augmentcode.com email address) and place small scripts there that you and others might find useful.

While a code review is still required (for technical reasons), the review doesn't have to be as stringent as production code.
For example, while unit testing is encouraged, it is not necessary.
However, the code placed in the experimental directory should not be used for any production purpose.
Code in this directory that proves useful can "graduate" to production code, i.e. after documentation and testing."
"""Test parsing."""
import pathlib
from functools import partial
import re
import os
import multiprocessing
import logging
import time
from typing import Any
import pyarrow.dataset as ds
import tree_sitter
import tree_sitter_languages
import tqdm
from concurrent import futures

PARSER_LANG = {
    "c": "cpp",
    "c++": "cpp",
}


def parse_file(parser: tree_sitter.Parser, content: str):
    try:
        return parser.parse(content.encode()) is not None
    except ValueError:
        # Handle timeouts.
        return False


def process_file(dct, lang: str, logdir: pathlib.Path, timeout_ms: int):
    pid = os.getpid()
    parser = tree_sitter_languages.get_parser(PARSER_LANG.get(lang, lang))
    parser.set_timeout_micros(timeout_ms * 1_000)  # type: ignore

    logger = logging.getLogger(f"process-{pid}")
    if not logger.handlers:
        fh = logging.FileHandler(f"{logdir}/log{pid}.log")
        fh.setLevel(logging.INFO)
        logger.addHandler(fh)
    logger.info("> Parsing %s", dct["path"])
    start = time.time()
    status = parse_file(parser, dct["content"])
    latency_ms = (time.time() - start)*1000
    logger.info("< Parsing %s %s in %0.3fms", dct["path"], status, latency_ms)

    return status, latency_ms


def process_batch(ibatch_file, lang: str, logdir: pathlib.Path, timeout_ms: int, max_workers):
    i, batch_file = ibatch_file
    parser = tree_sitter_languages.get_parser(PARSER_LANG.get(lang, lang))
    parser.set_timeout_micros(timeout_ms * 1_000)  # type: ignore

    logger = logging.getLogger(f"process-{i}")
    logger.setLevel(logging.INFO)
    fh = logging.FileHandler(f"{logdir}/log{i}.log")
    fh.setLevel(logging.INFO)
    # create formatter and add it to the handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    logger.addHandler(fh)

    dataset = ds.dataset(
        batch_file,
        format="parquet"
            )
    batch_size = 1_000
    batches = dataset.to_batches(batch_size=batch_size)
    total = int(0.5 + dataset.count_rows() / batch_size)

    for batch in tqdm.tqdm(batches, total=total, desc=f"batch {i}", position=(i % max_workers)+1):
        for dct in batch.to_pylist():
            logger.info("> Parsing %s", dct["path"])
            start = time.time()
            status = parse_file(parser, dct["content"])
            latency_ms = (time.time() - start)*1000
            logger.info("< Parsing %s %s in %0.3fms", dct["path"], status, latency_ms)

    return True


def compute_stats(logdir: pathlib.Path):
    def parse_line(line: str):
        pat = (r"INFO - < Parsing (?P<path>.*) (?P<status>(True)|(False)) in (?P<ms>\d+.\d+)ms")
        m = re.search(pat, line)
        return m and (bool(m.group("status")), float(m.group("ms")))

    stats = {
        "success": 0,
        "fail": 0,
        "count": 0,
        "avg_latency_ms": 0.
    }
    times = []
    logs = list(logdir.glob("*.log"))
    for log in tqdm.tqdm(logs, desc="parsing logs"):
        with log.open() as f:
            for line in f:
                if (status_ms := parse_line(line)) is None:
                    continue
                status, ms = status_ms
                stats["count"] += 1
                stats["success" if status else "fail"] += 1
                stats["avg_latency_ms"] += (ms - stats["avg_latency_ms"])/stats["count"]
                times.append(ms)
    times.sort()
    # Latency percentiles
    stats.update({
        "latency:0": times[0],
        "latency:50": times[int(.5*len(times))],
        "latency:99": times[int(.99*len(times))],
        "latency:99.9": times[int(.99*len(times))],
        "latency:100": times[-1],
    })


def main():
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--lang", type=str, default="go")
    parser.add_argument("--logdir", type=pathlib.Path, default="/tmp/tstest")
    parser.add_argument("--timeout-ms", type=int, default=1000)
    parser.add_argument("--max-workers", type=int, default=50)
    args = parser.parse_args()

    args.logdir.mkdir(parents=True, exist_ok=True)

    files = ds.dataset(
        f"/mnt/efs/augment/data/raw/the-stack-dedup.2022-11-19/data/{args.lang}",
        format="parquet"
        ).files

    with futures.ProcessPoolExecutor(
        max_workers=args.max_workers,
        mp_context=multiprocessing.get_context("spawn")) as executor:

        for _ in tqdm.tqdm(executor.map(
            partial(process_batch, logdir=args.logdir, lang=args.lang,
                     timeout_ms=args.timeout_ms, max_workers=args.max_Workers),
              enumerate(files)),
                           desc="batches", total=len(files)):
            # We don't need to do anything with each shard.
            pass
    print(compute_stats(args.logdir))


if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "\n", "DOGFOOD_EDIT_SAMPLES_PATH = Path(\"/mnt/efs/augment/user/yury/smart_paste/data/dogfood_edit_requests/diffs_with_suggestions_20240913_claude_llama.jsonl\")\n", "\n", "with DOGFOOD_EDIT_SAMPLES_PATH.open(\"r\") as f:\n", "    dogfood_edit_samples = [json.loads(line) for line in f]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.tokenized_llama_binks_prompt_formatter import (\n", "    TokenizedLlama3BinksPromptFormatter,\n", ")\n", "from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "tokenizer = Llama3InstructTokenizer()\n", "token_apportionment = ChatTokenApportionment(\n", "    prefix_len=1024,\n", "    suffix_len=1024,\n", "    path_len=256,\n", "    message_len=-1,\n", "    selected_code_len=-1,\n", "    chat_history_len=4096,\n", "    retrieval_len_per_each_user_guided_file=0,\n", "    retrieval_len_for_user_guided=0,\n", "    retrieval_len=-1,\n", "    max_prompt_len=32768 - 16384,\n", ")\n", "\n", "prompt_formatter = TokenizedLlama3BinksPromptFormatter(tokenizer, token_apportionment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["used_instructions = set()\n", "\n", "dedup_samples = []\n", "\n", "for sample in dogfood_edit_samples:\n", "    if sample[\"edit_sample\"][\"instruction\"] in used_instructions:\n", "        continue\n", "    dedup_samples.append(sample)\n", "    used_instructions.add(sample[\"edit_sample\"][\"instruction\"])\n", "\n", "print(f\"Deduped {len(dedup_samples)} samples out of {len(dogfood_edit_samples)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT_COT = \"\"\"\\\n", "Here's the current file:\n", "\n", "```\n", "{target_file_contents}\n", "```\n", "\n", "And the changes I want you to apply to the current file:\n", "\n", "```\n", "{suggested_edit}\n", "```\n", "\n", "Strictly follow these three steps to incorporate the suggested changes. NO deviations:\n", "\n", "Step 1: Briefly describe the suggested changes.\n", "\n", "Step 2: Plan\n", "Write a plan outlining exactly which suggested changes will be applied and where. Keep the plan strictly focused on the areas related to the suggestions. If the changes include imports, add plan to add these imports at the beginning of the file. USE PLAIN TEXT AND NEVER USE CODEBLOCKS IN YOUR PLAN.\n", "\n", "Step 3: Incorporate Suggested Changes\n", "Apply the changes seamlessly and write out the full, modified file with no omissions or placeholders. Strictly follow the plan when applying the suggested changes. Do not make any additional changes, but ensure that all other code outside of the suggested changes is preserved exactly as it is.\n", "\"\"\"\n", "\n", "\n", "PROMPT = \"\"\"\\\n", "Here's the current file:\n", "\n", "```\n", "{target_file_contents}\n", "```\n", "\n", "And the changes I want you to apply to the current file:\n", "\n", "```\n", "{suggested_edit}\n", "```\n", "\n", "Apply the changes seamlessly and write out the full, modified file with no omissions or placeholders. Do not make any additional changes, but ensure that all other code outside of the suggested changes is preserved exactly as it is.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import pandas as pd\n", "\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput, ExceedContextLength\n", "from base.prompt_format.common import Exchange\n", "from base.prompt_format.common import PromptChunk\n", "\n", "\n", "# MAX_TOKEN_BUDGET = 32768 + 1\n", "MAX_TOKEN_BUDGET = 16384 + 1\n", "\n", "\n", "def prepare_tokens(sample):\n", "    edit_sample = sample[\"edit_sample\"]\n", "\n", "    prefix = edit_sample[\"prefix\"] or \"\"\n", "    suffix = edit_sample[\"suffix\"] or \"\"\n", "    selected_code = edit_sample[\"selected_code\"] or \"\"\n", "\n", "    target_file_contents = prefix + selected_code + suffix\n", "    target_file_path = edit_sample[\"path\"]\n", "\n", "    message = PROMPT_COT.format(\n", "        target_file_contents=target_file_contents,\n", "        suggested_edit=sample[\"suggested_edit\"],\n", "    )\n", "\n", "    chat_history = [\n", "        Exchange(\n", "            request_message=edit_sample[\"instruction\"],\n", "            response_text=sample[\"chat_response\"][\"text\"],\n", "        )\n", "    ]\n", "\n", "    chat_prompt_input = ChatPromptInput(\n", "        message=message,\n", "        path=target_file_path,\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        chat_history=chat_history,\n", "        prefix_begin=0,\n", "        suffix_end=0,\n", "        retrieved_chunks=[],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "\n", "    input_tokens = prompt_formatter.format_prompt(chat_prompt_input).tokens\n", "    input_tokens = [-x for x in input_tokens]\n", "    output_tokens = (\n", "        tokenizer.tokenize_safe(sample[\"smart_paste_response\"][\"text\"])\n", "        # tokenizer.tokenize_safe(sample[\"smart_paste_response\"][\"modified_code\"])\n", "        + [tokenizer.special_tokens.eos]\n", "    )    \n", "    tokens = input_tokens + output_tokens\n", "    return tokens\n", "\n", "tokenized_samples = []\n", "n_bad_status, n_prompt_too_long, n_sample_too_long = 0, 0, 0\n", "for sample in tqdm.tqdm(dedup_samples):\n", "    if sample[\"smart_paste_response\"][\"status\"] != \"success\":\n", "        n_bad_status += 1\n", "        continue \n", "    try:\n", "        tokens = prepare_tokens(sample)    \n", "    except ExceedContextLength:\n", "        n_prompt_too_long += 1\n", "        continue\n", "    if len(tokens) >= MAX_TOKEN_BUDGET:\n", "        n_sample_too_long += 1\n", "        continue\n", "    tokenized_samples.append(tokens)\n", "\n", "print(f\"n_total: {len(dedup_samples)}, n_success {len(tokenized_samples)}, n_bad_status: {n_bad_status}, n_prompt_too_long: {n_prompt_too_long}, n_sample_too_long: {n_sample_too_long}\")\n", "\n", "pd.Series([len(s) for s in tokenized_samples]).describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from pathlib import Path\n", "\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v2_cot\")\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v1\")\n", "N_EVAL_SAMPLES = 128\n", "\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(tokenized_samples)\n", "\n", "tokenized_datasets = {\n", "    \"train\": tokenized_samples[N_EVAL_SAMPLES:],\n", "    \"valid\": tokenized_samples[:N_EVAL_SAMPLES],\n", "}\n", "\n", "\n", "if not OUTPUT_DIR.exists():\n", "    OUTPUT_DIR.mkdir(parents=True)\n", "\n", "import torch\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "def save_samples(samples, path: Path):\n", "    builder = MMapIndexedDatasetBuilder(str(path) + \".bin\", dtype=np.int32)\n", "    for sample in tqdm.tqdm(samples, total=len(samples)):\n", "        padded_sample = [x for x in sample]\n", "        if len(padded_sample) < MAX_TOKEN_BUDGET:\n", "            padded_sample.extend([-tokenizer.special_tokens.padding] * (MAX_TOKEN_BUDGET - len(padded_sample)))\n", "        assert len(padded_sample) == MAX_TOKEN_BUDGET\n", "        builder.add_item(torch.tensor(padded_sample))\n", "        builder.end_document()\n", "    builder.finalize(str(path) + \".idx\")\n", "\n", "for name, tokens in tokenized_datasets.items():\n", "    print(f\"Saving {name}...\")\n", "    save_samples(tokens, OUTPUT_DIR / name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "ds = indexed_dataset.make_dataset(\n", "    str(OUTPUT_DIR / \"valid\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "print(len(ds))\n", "tokens = [abs(x) for x in ds[10]]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
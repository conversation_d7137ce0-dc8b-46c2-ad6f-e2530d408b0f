determined:
  description: "Smart paste (full file rewrite) model training."
  workspace: Dev
  project: yury

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 16

fastbackward_configs:
 - configs/llama31_8b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  batch_size: 1
  gradient_accumulation_steps: 8
  warmup_iters: 100
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  eval_interval: 50
  # block_size: 32768
  block_size: 16384
  use_activation_checkpointing: True
  train_data_path: /mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v2_cot/train
  eval_data_path: /mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v2_cot/valid
  # train_data_path: /mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v1/train
  # eval_data_path: /mnt/efs/augment/user/yury/smart_paste_full_file_train/diffs_with_suggestions_20240913_claude_llama/v1/valid

  checkpoint_optimizer_state: False
  checkpoint: /mnt/efs/augment/checkpoints/llama3.1/fb/Meta-Llama-3.1-8B-Instruct
  model_parallel_size: 1
  use_sequence_parallel: False

  # checkpoint: /mnt/efs/augment/checkpoints/llama3.1/fb/Meta-Llama-3.1-8B-Instruct-mp2
  # model_parallel_size: 2
  # use_sequence_parallel: True

  model_vocab_size: 128256

  tokenizer_name: llama3_instruct
  use_research_tokenizer: false
  visualize_logits_samples: 32

  run_name: llama7b_v2_cot_16k
  wandb_project: yury-smart-paste-fullfile
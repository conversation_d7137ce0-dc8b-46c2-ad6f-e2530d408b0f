{"port": 8888, "mtls": false, "server_cert_path": "", "server_key_path": "", "root_cert_path": "", "client_mtls": false, "client_cert_path": "", "client_key_path": "", "client_ca_path": "", "max_seq_length": 8192, "round_sizes": [32, 64, 128, 256, 512], "cache_size": 4, "model_arch": {"arch_type": "LLAMA_FP8", "num_layers": 80, "vocab_size": 128256, "emb_dim": 8192, "num_heads": 8, "head_dim": 128, "num_queries_per_head": 8, "mlp_dim_divisible_by": 4096, "ffn_dim_multiplier": 1.3, "attn_split_head_mode": "KV_HEADS", "norm_eps": 1e-05, "rotary_pct": 1.0, "rotary_theta": 500000.0, "rotary_scaling_factor": 1.0, "max_position_embeddings": 8192}, "model_name": "binks_llama3_70B_FP8_chat_deploy", "weights_path": "/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8", "max_rpc_threads": 32, "speculation_models": [], "model_parallelism": 2, "non_neural_speculation_model": "longest_overlap"}
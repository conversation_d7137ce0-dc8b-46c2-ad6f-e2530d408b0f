{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get and download data from BigQuery\n", "# https://console.cloud.google.com/bigquery?project=system-services-prod&authuser=1&ws=!1m0\n", "\n", "\"\"\"\n", "WITH random_requests AS (\n", "  SELECT \n", "    user_id,\n", "    ARRAY_AGG(raw_json ORDER BY RAND() LIMIT 5) AS random_raw_json\n", "  FROM (\n", "    SELECT\n", "      metadata_event.request_id,\n", "      metadata_event.time AS event_time,    \n", "      CAST(JSON_EXTRACT_SCALAR(metadata_event.raw_json, \"$.session_id\") AS STRING) AS session_id,\n", "      CAST(JSON_EXTRACT_SCALAR(metadata_event.raw_json, \"$.user_id\") AS STRING) AS user_id,\n", "      edit_host_request.*\n", "    FROM\n", "    `system-services-prod.staging_request_insight_full_export_dataset.request_event` AS metadata_event\n", "    JOIN `system-services-prod.staging_request_insight_full_export_dataset.request_event` AS edit_host_request\n", "    ON metadata_event.request_id = edit_host_request.request_id\n", "    AND metadata_event.event_type='request_metadata'\n", "    AND edit_host_request.event_type='edit_host_request'\n", "    AND metadata_event.time >= \"2024-03-10\"\n", "    AND metadata_event.tenant=\"dogfood\"\n", "    AND CAST(JSON_EXTRACT_SCALAR(metadata_event.raw_json, \"$.user_id\") AS STRING) != \"health-check-1\"\n", "    AND CAST(JSON_EXTRACT_SCALAR(metadata_event.raw_json, \"$.user_id\") AS STRING) != \"review-edit-bot\"\n", "  )\n", "  GROUP BY user_id\n", ")\n", "SELECT \n", "  raw_json\n", "FROM \n", "  random_requests,\n", "  UNNEST(random_raw_json) AS raw_json\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "DATA_PATH = '/mnt/efs/augment/user/yury/benchmark/edit_requests_20240423/data.jsonl'\n", "OUTPUT_PATH = '/mnt/efs/augment/user/yury/benchmark/edit_requests_20240423/data_processed.json'\n", "\n", "data = []\n", "\n", "with open(DATA_PATH, 'r') as f:\n", "    for line in f:\n", "        sample = json.loads(line)\n", "        text = [d['text'] for d in sample['raw_json']['tokens'] if 'token_id' in d]\n", "        token_ids = [d['token_id'] for d in sample['raw_json']['tokens'] if 'token_id' in d]\n", "        data.append({\n", "            \"context\": token_ids,\n", "            \"context_raw\": text,\n", "        })"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of context', ylabel='Frequency'>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "def custom_hist(array, bins, xlabel, ylabel, figsize=(20, 5), fontsize=14):\n", "  array = np.array(array)\n", "  bins = np.array(bins + [bins[-1]])\n", "  assert array.min() >= bins.min()\n", "  assert array.max() <= bins.max()\n", "  left_edge = bins[:-1]\n", "  right_edge = bins[1:] - 1\n", "  # Rightmost element is inclusive.\n", "  # See https://numpy.org/doc/stable/reference/generated/numpy.histogram.html\n", "  # for details.\n", "  right_edge[-1] += 1\n", "\n", "  hist, bin_edges = np.histogram(array, bins)\n", "  fig1, ax = plt.subplots(figsize=figsize)\n", "\n", "  # Plot the histogram heights against integers on the x axis\n", "  ax.bar(range(len(hist)), hist, width=1)\n", "\n", "  ax.tick_params(axis='y', labelsize=fontsize )\n", "  for i in range(len(hist)):\n", "    plt.text(i, hist[i] + 1, hist[i], ha=\"center\", fontsize=fontsize - 2)\n", "\n", "  # Set the ticks to the middle of the bars\n", "  ax.set_xticks([i for i, j in enumerate(hist)])\n", "  # Set the xticklabels to a string that tells us what the bin edges were\n", "  xticklabels = []\n", "  for i in range(len(left_edge)):\n", "    if right_edge[i] - left_edge[i] == 0:\n", "      xticklabels.append('%.1f' % left_edge[i])\n", "    else:\n", "      xticklabels.append('%.1f - %.1f' % (left_edge[i], right_edge[i]))\n", "  xticklabels[-1] = 'None'\n", "\n", "  ax.set_xticklabels(xticklabels, fontsize=fontsize) \n", "  ax.set_xlabel(xlabel, fontsize=14)\n", "  ax.set_ylabel(ylabel, fontsize=14)\n", "  ax.autoscale()\n", "  return ax\n", "\n", "custom_hist([len(x['context']) for x in data], bins=[0, 1000, 5000, 8000], xlabel='Lenght of context', ylabel='Frequency')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
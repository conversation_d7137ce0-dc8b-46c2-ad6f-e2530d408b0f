{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from research.eval.edit.data_tools.collected import load_data_git_conflict_format"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 194 samples from 194 files\n"]}], "source": ["import glob\n", "import json\n", "\n", "PATHS = [\n", "    '/mnt/efs/augment/data/eval/human_annotated_codeedits_jan_31/pareto/*/repo_patches.jsonl',\n", "    '/mnt/efs/augment/data/eval/human_annotated_codeedits_jan_31/turing/*/repo_patches.jsonl',\n", "]\n", "\n", "def load_jsonl(patterns):\n", "    data = []\n", "    n_files = 0\n", "    for pattern in patterns:\n", "        paths = glob.glob(pattern)\n", "        n_files += len(paths)\n", "        for path in paths:\n", "            with open(path) as f:\n", "                for line in f:\n", "                    data.append(json.loads(line[:-1]))\n", "    print('Loaded %d samples from %d files' % (len(data), n_files))\n", "    return data\n", "\n", "\n", "raw_data = load_jsonl(PATHS)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please fix it, make the request always streamed to TARGET_URL, should follow red\n", "write a test to confirm arrayBuffer support\n", "Traceback (most recent call last):   File \"/Users/<USER>/dev/download-electerm.htm\n", "response = requests.get(response.headers['Location'], stream=True) seems only er\n", "use console.error\n", "rewrite with react hooks\n", "use python dotenv lib to load env\n", "any error here?\n", "useSystemTitleBar set to true when it is Linux OS\n", "please write a simple ssh server with ssh2 module, listen to localhost and 45444\n", "get error: Traceback (most recent call last):   File \"handle_request\", line 102,\n", "should clear current line\n", "use sanic instead of flask\n", "print TARGET_URL PATH_START\n", "use dotenv package to load envs\n", "please fix these code, I want to combine string here\n", "add more cases\n", "rewrite with python3\n", "handle the case where the response body is html\n", "can we use requests module instead of aiohttp\n", "change terminal text color to blur\n", "please fix these code, get error: ValueError: too many values to unpack (expecte\n", "handle the case where the response body is html instead of json\n", "please fix error, error trace stack: Traceback (most recent call last):   File \"\n", "can you improve my function\n", "should read all file buffer until all file content read\n", "response = requests.get(response.headers['Location'], stream=True) seems only er\n", "there are two headers={'Accept-Encoding': 'identity'}), please modify to use a v\n", "should check if ws is open state\n", "please fix this line if any error exist\n", "make deepCopy function support arrayBuffer\n", "change the order\n", "rewrite with python\n", "modify it to update current terminal line\n", "please fix code according to the error: Traceback (most recent call last):   Fil\n", "get list with single quote\n"]}], "source": ["for sample in raw_data:\n", "    print(sample['_extra']['instruction'][:80])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["EditPromptInput(\n", "  path = 'src/main.py',\n", "  prefix = \"import os\\nfrom flask import Flask, request, redirect, Response\\nimport requests\\nfrom dotenv import load_dotenv\\n\\nload_dotenv()  # Load environment variables from .env file\\n\\napp = Flask(__name__)\\n\\nTARGET_URL = os.getenv('TARGET_URL', 'https://github.com')\\nPATH_START = os.getenv('PATH_START', '/electerm/electerm/releases/download/')\\n\\nprint(TARGET_URL, PATH_START)\\n\\n\",\n", "  selected_code = \"@app.route('/', defaults={'path': ''})\\<EMAIL>('/<path:path>')\\ndef proxy(path):\\n    print(path)\\n    if request.method == 'GET' and path.startswith(PATH_START):\\n        response = requests.get(TARGET_URL + '/' + path, stream=True)\\n        \\n        # Check if the response is a redirect\\n        if response.status_code == 302:\\n            one_year_in_seconds = 365 * 24 * 60 * 60\\n            cache_control = f'public, max-age={one_year_in_seconds}'\\n\\n            # Set the Cache-Control and Expires headers\\n            response.headers['Cache-Control'] = cache_control\\n            response.headers['Expires'] = one_year_in_seconds\\n            \\n            return redirect(response.headers['location'], code=302)\\n        \\n        def generate():\\n            for chunk in response.iter_content(chunk_size=8192):\\n                yield chunk\\n                \\n        # Return the proxied response\\n        return Response(generate(), headers=response.headers, status=response.status_code)\\n    else:\\n        return 'Not Found', 404\\n\",\n", "  suffix = '\\<EMAIL>(\\'/test\\')\\ndef test():\\n    return \\'ok\\'\\n\\nif __name__ == \"__main__\":\\n    HOST = os.getenv(\\'HOST\\', \\'127.0.0.1\\')\\n    PORT = int(os.getenv(\\'PORT\\', 3000))\\n    \\n    app.run(host=HOST, port=PORT)\\n',\n", "  instruction = 'please fix it, make the request always streamed to TARGET_URL, should follow redirect, proxy the request to redirected url instead of just redirect the link to client',\n", "  prefix_begin = 0,\n", "  suffix_end = 1211,\n", "  retrieved_chunks = []\n", ")"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.core import utils_for_str\n", "from base.prompt_format_edit.prompt_formatter import EditPromptInput\n", "\n", "\n", "def build_edit_prompt_input(sample, lines_in_prefix_suffix = 100):\n", "    prefix = sample['file_content'][:sample['char_start']]\n", "    prefix = utils_for_str.get_last_n_lines(prefix, lines_in_prefix_suffix)\n", "    suffix = sample['file_content'][sample['char_end']:]\n", "    suffix = utils_for_str.get_last_n_lines(suffix, lines_in_prefix_suffix)\n", "    old_code = sample['_extra']['buggy_version']\n", "    new_code = sample['file_content'][sample['char_start']:sample['char_end']]\n", "    assert new_code == sample['patch_content']\n", "    return EditPromptInput(\n", "        path=sample['file_name'],\n", "        prefix=prefix,\n", "        selected_code=old_code,\n", "        suffix=suffix,\n", "        instruction=sample['_extra']['instruction'],\n", "        # Not quite true because we then limit prefix to 100 lines.\n", "        prefix_begin=0,\n", "        # Not quite true because we then limit suffix to 100 lines.\n", "        suffix_end=len(sample['file_content']),\n", "        retrieved_chunks=[],\n", "    )\n", "\n", "build_edit_prompt_input(raw_data[0])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import dataclasses\n", "from base.prompt_format_edit import get_code_edit_prompt_formatter_by_name\n", "from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "from base.prompt_format_edit.prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EditTokenApportionment,\n", ")\n", "from base.prompt_format_edit.prompt_formatter import EditPromptInput\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "# See services/deploy/droid_1B_BF16_v0_basic_edit_deploy.jsonnet\n", "token_app = EditTokenApportionment(\n", "    prefix_len=1536,\n", "    suffix_len=1024,\n", "    max_context_len=8192\n", ")\n", "\n", "prompt_formatter = get_code_edit_prompt_formatter_by_name(\n", "    \"droid\",\n", "    tokenizer,\n", "    token_app,   \n", ")\n", "\n", "data = []\n", "for sample in raw_data:\n", "    prompt_input = build_edit_prompt_input(sample)\n", "\n", "    context = prompt_formatter.format_prompt(prompt_input)    \n", "    label = tokenizer.tokenize_safe(sample['patch_content']) + [tokenizer._special_tokens.eod_token]\n", "    data.append({\n", "        \"context\": context,\n", "        \"label\": label,\n", "    })\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(data)    \n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of context', ylabel='Frequency'>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def custom_hist(array, bins, xlabel, ylabel, figsize=(20, 5), fontsize=14):\n", "  array = np.array(array)\n", "  bins = np.array(bins + [bins[-1]])\n", "  assert array.min() >= bins.min()\n", "  assert array.max() <= bins.max()\n", "  left_edge = bins[:-1]\n", "  right_edge = bins[1:] - 1\n", "  # Rightmost element is inclusive.\n", "  # See https://numpy.org/doc/stable/reference/generated/numpy.histogram.html\n", "  # for details.\n", "  right_edge[-1] += 1\n", "\n", "  hist, bin_edges = np.histogram(array, bins)\n", "  fig1, ax = plt.subplots(figsize=figsize)\n", "\n", "  # Plot the histogram heights against integers on the x axis\n", "  ax.bar(range(len(hist)), hist, width=1)\n", "\n", "  ax.tick_params(axis='y', labelsize=fontsize )\n", "  for i in range(len(hist)):\n", "    plt.text(i, hist[i] + 1, hist[i], ha=\"center\", fontsize=fontsize - 2)\n", "\n", "  # Set the ticks to the middle of the bars\n", "  ax.set_xticks([i for i, j in enumerate(hist)])\n", "  # Set the xticklabels to a string that tells us what the bin edges were\n", "  xticklabels = []\n", "  for i in range(len(left_edge)):\n", "    if right_edge[i] - left_edge[i] == 0:\n", "      xticklabels.append('%.1f' % left_edge[i])\n", "    else:\n", "      xticklabels.append('%.1f - %.1f' % (left_edge[i], right_edge[i]))\n", "  xticklabels[-1] = 'None'\n", "\n", "  ax.set_xticklabels(xticklabels, fontsize=fontsize) \n", "  ax.set_xlabel(xlabel, fontsize=14)\n", "  ax.set_ylabel(ylabel, fontsize=14)\n", "  ax.autoscale()\n", "  return ax\n", "\n", "custom_hist([len(x['context']) for x in data], bins=[10, 50, 100, 250, 500, 750, 1000, 1500, 2000], xlabel='Lenght of context', ylabel='Frequency')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of label', ylabel='Frequency'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_hist([len(x['label']) for x in data], bins=[10, 50, 100, 250, 500, 750, 1000, 1500, 2000], xlabel='Lenght of label', ylabel='Frequency')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["OUTPUT_PATH = '/mnt/efs/augment/user/yury/benchmarking_data_v2/human_annotated_codeedits_jan_31.jsonl'\n", "\n", "with open(OUTPUT_PATH, 'w') as f:\n", "    json.dump(data, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
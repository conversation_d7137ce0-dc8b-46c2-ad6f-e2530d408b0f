{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> initializing torch distributed ...\n", "[2023-10-27 02:42:04,992] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-10-27 02:42:05,103] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=216.153.49.54, master_port=6000\n", "[2023-10-27 02:42:05,104] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-10-27 02:42:05,108] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev2:2248777] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 49165) with 2035 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-27 02:42:05,433] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=17\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: _post_transformer_block\n", "    15: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    16: ParallelLinearPipe\n", "  loss: partial\n", "Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    0.position_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.key_value.weight requires_grad=True\n", "    2.attention.key_value.bias requires_grad=True\n", "    2.attention.query.weight requires_grad=True\n", "    2.attention.query.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.post_attention_layernorm.weight requires_grad=True\n", "    2.post_attention_layernorm.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.key_value.weight requires_grad=True\n", "    3.attention.key_value.bias requires_grad=True\n", "    3.attention.query.weight requires_grad=True\n", "    3.attention.query.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.post_attention_layernorm.weight requires_grad=True\n", "    3.post_attention_layernorm.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.key_value.weight requires_grad=True\n", "    4.attention.key_value.bias requires_grad=True\n", "    4.attention.query.weight requires_grad=True\n", "    4.attention.query.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.post_attention_layernorm.weight requires_grad=True\n", "    4.post_attention_layernorm.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.key_value.weight requires_grad=True\n", "    5.attention.key_value.bias requires_grad=True\n", "    5.attention.query.weight requires_grad=True\n", "    5.attention.query.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.post_attention_layernorm.weight requires_grad=True\n", "    5.post_attention_layernorm.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.key_value.weight requires_grad=True\n", "    6.attention.key_value.bias requires_grad=True\n", "    6.attention.query.weight requires_grad=True\n", "    6.attention.query.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.post_attention_layernorm.weight requires_grad=True\n", "    6.post_attention_layernorm.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.key_value.weight requires_grad=True\n", "    7.attention.key_value.bias requires_grad=True\n", "    7.attention.query.weight requires_grad=True\n", "    7.attention.query.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.post_attention_layernorm.weight requires_grad=True\n", "    7.post_attention_layernorm.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.key_value.weight requires_grad=True\n", "    8.attention.key_value.bias requires_grad=True\n", "    8.attention.query.weight requires_grad=True\n", "    8.attention.query.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.post_attention_layernorm.weight requires_grad=True\n", "    8.post_attention_layernorm.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.key_value.weight requires_grad=True\n", "    9.attention.key_value.bias requires_grad=True\n", "    9.attention.query.weight requires_grad=True\n", "    9.attention.query.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.post_attention_layernorm.weight requires_grad=True\n", "    9.post_attention_layernorm.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.key_value.weight requires_grad=True\n", "    10.attention.key_value.bias requires_grad=True\n", "    10.attention.query.weight requires_grad=True\n", "    10.attention.query.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.post_attention_layernorm.weight requires_grad=True\n", "    10.post_attention_layernorm.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.key_value.weight requires_grad=True\n", "    11.attention.key_value.bias requires_grad=True\n", "    11.attention.query.weight requires_grad=True\n", "    11.attention.query.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.post_attention_layernorm.weight requires_grad=True\n", "    11.post_attention_layernorm.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.key_value.weight requires_grad=True\n", "    12.attention.key_value.bias requires_grad=True\n", "    12.attention.query.weight requires_grad=True\n", "    12.attention.query.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.post_attention_layernorm.weight requires_grad=True\n", "    12.post_attention_layernorm.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.key_value.weight requires_grad=True\n", "    13.attention.key_value.bias requires_grad=True\n", "    13.attention.query.weight requires_grad=True\n", "    13.attention.query.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.post_attention_layernorm.weight requires_grad=True\n", "    13.post_attention_layernorm.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.norm.weight requires_grad=True\n", "    15.norm.bias requires_grad=True\n", "    16.final_linear.weight requires_grad=True\n", "DeepSpeed is enabled.\n", "[2023-10-27 02:42:05,656] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-27 02:42:05,656] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-27 02:42:05,836] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-27 02:42:05,838] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-27 02:42:05,838] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-27 02:42:05,839] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-27 02:42:05,839] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-10-27 02:42:05,840] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-27 02:42:05,841] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-27 02:42:05,841] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-27 02:42:05,842] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-27 02:42:05,842] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-27 02:42:05,843] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-27 02:42:05,844] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-27 02:42:05,844] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-27 02:42:05,845] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-27 02:42:05,845] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-27 02:42:05,846] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-27 02:42:05,847] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-27 02:42:05,847] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-27 02:42:05,848] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-27 02:42:05,848] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-27 02:42:05,849] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-27 02:42:05,850] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-27 02:42:05,851] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-27 02:42:05,851] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-27 02:42:05,852] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-27 02:42:05,852] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-27 02:42:05,853] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-27 02:42:05,853] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-27 02:42:05,854] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-27 02:42:05,856] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-10-27 02:42:05,856] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-27 02:42:05,857] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-27 02:42:05,857] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-27 02:42:05,858] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-27 02:42:05,861] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-27 02:42:05,861] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-27 02:42:05,861] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-27 02:42:05,862] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-27 02:42:05,863] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-27 02:42:05,863] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-27 02:42:05,863] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-27 02:42:05,864] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-27 02:42:05,864] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-27 02:42:05,865] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-27 02:42:05,865] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-27 02:42:05,866] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-27 02:42:05,867] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.20850753784179688 seconds\n", "[2023-10-27 02:42:06,742] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-10-27 02:42:06,774] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=17 [0, 17) STAGE_PARAMS=242361345 (242.361M) TOTAL_PARAMS=242361345 (242.361M) UNIQUE_PARAMS=242361345 (242.361M)\n", " > number of parameters on model parallel rank 0: 242361345\n", " > total params: 242,361,345\n", " > embedding params: 104,857,600\n", "Loading: /mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned\n", "[2023-10-27 02:42:06,806] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/mp_rank_00_model_states.pt\n", "[2023-10-27 02:42:06,928] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_00-model_00-model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-27 02:42:06,952] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_02-model_00-model_states.pt\n", "[2023-10-27 02:42:06,970] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_03-model_00-model_states.pt\n", "[2023-10-27 02:42:06,988] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_04-model_00-model_states.pt\n", "[2023-10-27 02:42:07,007] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_05-model_00-model_states.pt\n", "[2023-10-27 02:42:07,025] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_06-model_00-model_states.pt\n", "[2023-10-27 02:42:07,045] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_07-model_00-model_states.pt\n", "[2023-10-27 02:42:07,063] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_08-model_00-model_states.pt\n", "[2023-10-27 02:42:07,081] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_09-model_00-model_states.pt\n", "[2023-10-27 02:42:07,098] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_10-model_00-model_states.pt\n", "[2023-10-27 02:42:07,115] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_11-model_00-model_states.pt\n", "[2023-10-27 02:42:07,132] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_12-model_00-model_states.pt\n", "[2023-10-27 02:42:07,148] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_13-model_00-model_states.pt\n", "[2023-10-27 02:42:07,149] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_15-model_00-model_states.pt\n", "[2023-10-27 02:42:07,312] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/layer_16-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["import research.eval.harness.factories as factories\n", "from research.models.meta_gpt_neox_model import GPTNeoXModel\n", "from pathlib import Path\n", "\n", "model = GPTNeoXModel(\n", "    checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned\"),\n", "    # yml_config_paths: Optional[list[str]] = None,\n", "    # config_overrides: Optional[dict] = None,\n", ")\n", "model.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|retrieval_section|><|ret-start|><filename>tests/ops/test_qubit_ops.py<|ret-body|>\n", "        theta = 0.4\n", "        op = qml.<PERSON><PERSON>(theta, \"Z\", wires=0)\n", "        decomp_ops = op.decomposition(theta, \"Z\", wires=0)\n", "\n", "        assert np.allclose(op.eigvals, np.array([np.exp(-1j * theta / 2), np.exp(1j * theta / 2)]))\n", "        assert np.allclose(op.matrix, np.diag([np.exp(-1j * theta / 2), np.exp(1j * theta / 2)]))\n", "\n", "        assert len(decomp_ops) == 1\n", "\n", "        assert decomp_ops[0].name == \"MultiRZ\"\n", "\n", "        assert decomp_ops[0].wires == Wires([0])\n", "        assert decomp_ops[0].data[0] == theta\n", "\n", "    def test_PauliRot_all_Identity(self):\n", "        \"\"\"Test handling of the all-identity <PERSON><PERSON>.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.<PERSON><PERSON><PERSON>(theta, \"II\", wires=[0, 1])\n", "        decomp_ops = op.decomposition(theta, \"II\", wires=[0, 1])\n", "\n", "        assert np.allclose(op.eigvals, np.exp(-1j * theta / 2) * np.ones(4))\n", "        assert np.allclose(op.matrix / op.matrix[0, 0], np.eye(4))\n", "\n", "        assert len(decomp_ops) == 0\n", "\n", "    def test_PauliRot_decomposition_Identity(self):\n", "        \"\"\"Test that decomposing the all-identity <PERSON><PERSON> has no effect.\"\"\"\n", "\n", "<|ret-start|><filename>tests/ops/test_qubit_ops.py<|ret-body|>        assert decomp_ops[0].wires == Wires([0])\n", "\n", "        assert decomp_ops[1].name == \"RX\"\n", "\n", "        assert decomp_ops[1].wires == Wires([1])\n", "        assert decomp_ops[1].data[0] == np.pi / 2\n", "\n", "        assert decomp_ops[2].name == \"MultiRZ\"\n", "        assert decomp_ops[2].wires == Wires([0, 1])\n", "        assert decomp_ops[2].data[0] == theta\n", "\n", "\n", "        assert decomp_ops[3].name == \"Hadamard\"\n", "        assert decomp_ops[3].wires == Wires([0])\n", "\n", "        assert decomp_ops[4].name == \"RX\"\n", "\n", "        assert decomp_ops[4].wires == Wires([1])\n", "        assert decomp_ops[4].data[0] == -np.pi / 2\n", "\n", "    def test_PauliRot_decomposition_XIYZ(self):\n", "        \"\"\"Test that the decomposition for a XIYZ rotation is correct.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.<PERSON><PERSON><PERSON>(theta, \"XIYZ\", wires=[0, 1, 2, 3])\n", "        decomp_ops = op.decomposition(theta, \"XIYZ\", wires=[0, 1, 2, 3])\n", "\n", "        assert len(decomp_ops) == 5\n", "\n", "        assert decomp_ops[0].name == \"Hadamard\"\n", "<|ret-start|><filename>tests/ops/test_qubit_ops.py<|ret-body|>        theta = 0.4\n", "        op = qml.<PERSON><PERSON><PERSON>(theta, \"II\", wires=[0, 1])\n", "        decomp_ops = op.decomposition(theta, \"II\", wires=[0, 1])\n", "\n", "        assert len(decomp_ops) == 0\n", "\n", "    def test_PauliRot_decomposition_ZZ(self):\n", "        \"\"\"Test that the decomposition for a ZZ rotation is correct.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.<PERSON><PERSON>(theta, \"ZZ\", wires=[0, 1])\n", "        decomp_ops = op.decomposition(theta, \"ZZ\", wires=[0, 1])\n", "\n", "        assert len(decomp_ops) == 1\n", "\n", "        assert decomp_ops[0].name == \"MultiRZ\"\n", "\n", "        assert decomp_ops[0].wires == Wires([0, 1])\n", "        assert decomp_ops[0].data[0] == theta\n", "\n", "    def test_PauliRot_decomposition_XY(self):\n", "        \"\"\"Test that the decomposition for a XY rotation is correct.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.<PERSON><PERSON><PERSON>(theta, \"XY\", wires=[0, 1])\n", "        decomp_ops = op.decomposition(theta, \"XY\", wires=[0, 1])\n", "\n", "        assert len(decomp_ops) == 5\n", "\n", "        assert decomp_ops[0].name == \"Hadamard\"\n", "<|ret-start|><filename>tests/ops/test_qubit_ops.py<|ret-body|>        assert np.allclose(res, np.diag(exp))\n", "\n", "    @pytest.mark.parametrize(\"phi\", [-0.1, 0.2, 0.5])\n", "    def test_controlled_phase_shift_decomp(self, phi):\n", "        \"\"\"Tests that the ControlledPhaseShift operation calculates the correct decomposition\"\"\"\n", "        op = qml.ControlledPhaseShift(phi, wires=[0, 1])\n", "        decomp = op.decomposition(phi, wires=[0, 1])\n", "\n", "        mats = []\n", "        for i in reversed(decomp):\n", "            if i.wires.tolist() == [0]:\n", "                mats.append(np.kron(i.matrix, np.eye(2)))\n", "            elif i.<PERSON>.tolist() == [1]:\n", "                mats.append(np.kron(np.eye(2), i.matrix))\n", "            else:\n", "                mats.append(i.matrix)\n", "\n", "        decomposed_matrix = np.linalg.multi_dot(mats)\n", "        exp = ControlledPhaseShift(phi)\n", "\n", "        assert np.allclose(decomposed_matrix, exp)\n", "\n", "\n", "PAULI_ROT_PARAMETRIC_MATRIX_TEST_DATA = [\n", "    (\n", "        \"XY\",\n", "        lambda theta: np.array(\n", "            [\n", "                [np.cos(theta / 2), 0, 0, -np.sin(theta / 2)],\n", "                [0, np.cos(theta / 2), np.sin(theta / 2), 0],\n", "<|ret-start|><filename>tests/ops/test_qubit_ops.py<|ret-body|>        assert np.allclose(res, expected, atol=tol, rtol=0)\n", "\n", "    def test_MultiRZ_decomposition_ZZ(self):\n", "        \"\"\"Test that the decomposition for a ZZ rotation is correct.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.MultiRZ(theta, wires=[0, 1])\n", "        decomp_ops = op.decomposition(theta, wires=[0, 1])\n", "\n", "        assert decomp_ops[0].name == \"CNOT\"\n", "        assert decomp_ops[0].wires == Wires([1, 0])\n", "\n", "        assert decomp_ops[1].name == \"RZ\"\n", "\n", "        assert decomp_ops[1].wires == Wires([0])\n", "        assert decomp_ops[1].data[0] == theta\n", "\n", "        assert decomp_ops[2].name == \"CNOT\"\n", "        assert decomp_ops[2].wires == Wires([1, 0])\n", "\n", "    def test_MultiRZ_decomposition_ZZZ(self):\n", "        \"\"\"Test that the decomposition for a ZZZ rotation is correct.\"\"\"\n", "\n", "        theta = 0.4\n", "        op = qml.MultiRZ(theta, wires=[0, 2, 3])\n", "        decomp_ops = op.decomposition(theta, wires=[0, 2, 3])\n", "\n", "        assert decomp_ops[0].name == \"CNOT\"\n", "        assert decomp_ops[0].wires == Wires([3, 2])\n", "\n", "<fim_prefix><filename>tests/ops/test_qubit_ops.py<|prefix-body|> op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_y_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the PauliY is correct\"\"\"\n", "        op = qml.PauliY(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 3\n", "\n", "        assert res[0].name == \"PhaseShift\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 2\n", "\n", "        assert res[1].name == \"RY\"\n", "        assert res[1].wires == Wires([0])\n", "        assert res[1].data[0] == np.pi\n", "\n", "        assert res[2].name == \"PhaseShift\"\n", "        assert res[2].wires == Wires([0])\n", "        assert res[2].data[0] == np.pi / 2\n", "\n", "        decomposed_matrix = np.linalg.multi_dot([i.matrix for i in reversed(res)])\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_z_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the PauliZ is correct\"\"\"\n", "        op = qml.Pauli<PERSON>(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 1\n", "\n", "        assert res[0].name == \"PhaseShift\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi\n", "\n", "        decomposed_matrix = res[0].matrix\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_s_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the S gate is correct\"\"\"\n", "        op = qml.S(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 1\n", "\n", "        assert res[0].name == \"PhaseShift\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 2\n", "\n", "        decomposed_matrix = res[0].matrix\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_t_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the T gate is correct\"\"\"\n", "        op = qml.T(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 1\n", "\n", "        assert res[0].name == \"PhaseShift\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 4\n", "\n", "        decomposed_matrix = res[0].matrix\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_sx_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the SX gate is correct\"\"\"\n", "        op = qml.SX(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 4\n", "\n", "        assert all([res[i].wires == Wires([0]) for i in range(4)])\n", "\n", "        assert res[0].name == \"RZ\"\n", "        assert res[1].name == \"RY\"\n", "        assert res[2].name == \"RZ\"\n", "        assert res[3].name == \"PhaseShift\"\n", "\n", "        assert res[0].data[0] == np.pi / 2\n", "        assert res[1].data[0] == np.pi / 2\n", "        assert res[2].data[0] == -np.pi\n", "        assert res[3].data[0] == np.pi / 2\n", "\n", "        decomposed_matrix = np.linalg.multi_dot([i.matrix for i in reversed(res)])\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_hadamard_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the Hadamard gate is correct\"\"\"\n", "        op = qml.<PERSON><PERSON><PERSON>(wires=0)\n", "        res = op.decomposition(0)\n", "\n", "        assert len(res) == 3\n", "\n", "        assert res[0].name == \"PhaseShift\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 2\n", "\n", "        assert res[1].name == \"RX\"\n", "        assert res[1].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 2\n", "\n", "        assert res[2].name == \"PhaseShift\"\n", "        assert res[2].wires == Wires([0])\n", "        assert res[0].data[0] == np.pi / 2\n", "\n", "        decomposed_matrix = np.linalg.multi_dot([i.matrix for i in reversed(res)])\n", "        assert np.allclose(decomposed_matrix, op.matrix, atol=tol, rtol=0)\n", "\n", "    def test_phase_decomposition(self, tol):\n", "        \"\"\"Tests that the decomposition of the Phase gate is correct\"\"\"\n", "        phi = 0.3\n", "        op = qml.PhaseShift(phi, wires=0)\n", "        res = op.decomposition(phi, 0)\n", "\n", "        assert len(res) == 1\n", "\n", "        assert res[0].name == \"RZ\"\n", "\n", "        assert res[0].wires == Wires([0])\n", "        assert res[0].data[0] == 0.3\n", "\n", "        decomposed_matrix = res[0].matrix\n", "        global_phase = (<fim_suffix>\n", "\n", "    def test_x_rotation(self, tol):\n", "        \"\"\"Test x rotation is correct\"\"\"\n", "\n", "        # test identity for theta=0\n", "        assert np.allclose(qml.RX._matrix(0), np.identity(2), atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi/2\n", "        expected = np.array([[1, -1j], [-1j, 1]]) / np.sqrt(2)\n", "        assert np.allclose(qml.RX._matrix(np.pi / 2), expected, atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi\n", "        expected = -1j * np.array([[0, 1], [1, 0]])\n", "        assert np.allclose(qml.RX._matrix(np.pi), expected, atol=tol, rtol=0)\n", "\n", "    def test_y_rotation(self, tol):\n", "        \"\"\"Test y rotation is correct\"\"\"\n", "\n", "        # test identity for theta=0\n", "        assert np.allclose(qml.RY._matrix(0), np.identity(2), atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi/2\n", "        expected = np.array([[1, -1], [1, 1]]) / np.sqrt(2)\n", "        assert np.allclose(qml.RY._matrix(np.pi / 2), expected, atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi\n", "        expected = np.array([[0, -1], [1, 0]])\n", "        assert np.allclose(qml.RY._matrix(np.pi), expected, atol=tol, rtol=0)\n", "\n", "    def test_z_rotation(self, tol):\n", "        \"\"\"Test z rotation is correct\"\"\"\n", "\n", "        # test identity for theta=0\n", "        assert np.allclose(qml.RZ._matrix(0), np.identity(2), atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi/2\n", "        expected = np.diag(np.exp([-1j * np.pi / 4, 1j * np.pi / 4]))\n", "        assert np.allclose(qml.RZ._matrix(np.pi / 2), expected, atol=tol, rtol=0)\n", "\n", "        # test identity for theta=pi\n", "        assert np.allclose(qml.RZ._matrix(np.pi), -1j * Z, atol=tol, rtol=0)\n", "\n", "    def test_arbitrary_rotation(self, tol):\n", "        \"\"\"Test arbitrary single qubit rotation is correct\"\"\"\n", "\n", "        # test identity for phi,theta,omega=0\n", "        assert np.allclose(qml.Rot._matrix(0, 0, 0), np.identity(2), atol=tol, rtol=0)\n", "\n", "        # expected result\n", "        def arbitrary_rotation(x, y, z):\n", "            \"\"\"arbitrary single qubit rotation\"\"\"\n", "            c = np.cos(y / 2)\n", "            s = np.sin(y / 2)\n", "            return np.array(\n", "                [\n", "                    [np.exp(-0.5j * (x + z)) * c, -np.exp(0.5j * (x - z)) * s],\n", "                    [np.exp(-0.5j * (x - z)) * s, np.exp(0.5j * (x + z)) * c],\n", "                ]\n", "            )\n", "\n", "        a, b, c = 0.432, -0.152, 0.92<fim_middle>\n"]}], "source": ["import json\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "input_path = '/mnt/efs/augment/user/yury/benchmarking_data/rag_diffb_v2_chunk30/validation_dataset.jsonl'\n", "with Path(input_path).open(\"r\", encoding=\"utf8\") as f:\n", "    data = json.load(f)\n", "\n", "print(tokenizer.detokenize(data[0]['context']))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPARTA: EmbeddingPipe input_ids= tensor([[49155, 49156,     5,  ...,    43,    36,     2]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[   0,    1,    2,  ..., 3812, 3813, 3814]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[ 0.0085,  0.0373, -0.0076,  ..., -0.0084,  0.0212,  0.0519],\n", "         [-0.0043, -0.0255,  0.0122,  ..., -0.0606, -0.0002, -0.0173],\n", "         [ 0.0488, -0.0806,  0.0887,  ...,  0.0005,  0.0162,  0.0444],\n", "         ...,\n", "         [-0.0604,  0.0064,  0.0279,  ..., -0.0035, -0.0495, -0.0206],\n", "         [-0.0833, -0.0002,  0.0486,  ...,  0.0230,  0.0019,  0.0868],\n", "         [-0.0170, -0.1345,  0.0494,  ..., -0.0842,  0.0412,  0.0378]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[ 0.0310,  0.1353, -0.0277,  ..., -0.0304,  0.0770,  0.1886],\n", "         [-0.0158, -0.0925,  0.0442,  ..., -0.2200, -0.0008, -0.0629],\n", "         [ 0.1772, -0.2927,  0.3220,  ...,  0.0018,  0.0587,  0.1614],\n", "         ...,\n", "         [-0.2191,  0.0233,  0.1011,  ..., -0.0126, -0.1798, -0.0749],\n", "         [-0.3022, -0.0006,  0.1764,  ...,  0.0837,  0.0069,  0.3152],\n", "         [-0.0618, -0.4885,  0.1794,  ..., -0.3057,  0.1497,  0.1375]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1293,  0.5698,  0.2375,  ..., -0.5742,  0.1238, -0.3301]],\n", "\n", "        [[-0.2050, -0.0813,  0.7344,  ..., -0.5029,  0.2651, -0.6587]],\n", "\n", "        [[ 1.0078, -0.2300,  0.8149,  ...,  0.1103, -0.1643,  0.1912]],\n", "\n", "        ...,\n", "\n", "        [[-0.2593,  0.1646,  0.3008,  ..., -0.0462, -0.4038, -0.0311]],\n", "\n", "        [[-0.5195,  0.3037,  0.3735,  ...,  0.4014, -0.0130,  0.5391]],\n", "\n", "        [[ 0.0884, -0.9106,  0.3447,  ..., -0.3757,  0.4380,  0.1367]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.5381,  0.5557,  0.3269,  ..., -0.9229,  0.1667, -0.6016]],\n", "\n", "        [[-0.1699, -0.3252,  0.7432,  ..., -0.2178,  0.3462, -0.7246]],\n", "\n", "        [[ 1.1904, -0.3291,  0.7305,  ...,  0.0099, -0.1908,  0.2018]],\n", "\n", "        ...,\n", "\n", "        [[-0.2820,  0.1273,  0.4060,  ...,  0.1282, -0.2397,  0.0857]],\n", "\n", "        [[-0.5503,  0.3777,  0.4304,  ...,  0.5166,  0.1050,  0.4775]],\n", "\n", "        [[ 0.3152, -0.8306,  0.2144,  ..., -0.3499,  0.6025,  0.2085]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.7754,  0.5269,  0.1954,  ..., -1.1279,  0.0839, -0.6431]],\n", "\n", "        [[-0.1982, -0.5732,  0.8052,  ..., -0.0188,  0.3022, -0.6001]],\n", "\n", "        [[ 1.4736, -0.4915,  0.7207,  ...,  0.2725, -0.6753,  0.2070]],\n", "\n", "        ...,\n", "\n", "        [[-0.5459, -0.0151,  0.4565,  ...,  0.0734,  0.0947,  0.1925]],\n", "\n", "        [[-0.8008,  0.2168,  0.5557,  ...,  0.5537,  0.3740,  0.5479]],\n", "\n", "        [[ 0.2224, -0.6934, -0.1453,  ..., -0.5728,  0.7280,  0.4275]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0469,  0.5464,  0.3379,  ..., -1.2461,  0.3523, -0.5640]],\n", "\n", "        [[-0.4492, -0.5830,  1.0332,  ..., -0.1058,  0.8564, -0.8867]],\n", "\n", "        [[ 2.3887, -0.7793,  0.9980,  ...,  0.6025, -1.0742,  0.4358]],\n", "\n", "        ...,\n", "\n", "        [[-1.0254,  0.0630,  0.3435,  ..., -0.1154, -0.1592,  0.4604]],\n", "\n", "        [[-1.1699,  0.1708,  0.5444,  ...,  0.5166, -0.0168,  0.6504]],\n", "\n", "        [[ 0.0909, -0.8159,  0.0251,  ..., -0.3748,  0.5933,  0.4729]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0938,  0.6025,  0.4731,  ..., -1.3906,  0.3806, -0.6494]],\n", "\n", "        [[-0.5039, -0.8208,  0.9111,  ..., -0.1272,  0.5820, -0.5337]],\n", "\n", "        [[ 2.2266, -0.9209,  1.5820,  ...,  0.7959, -1.5527,  0.6348]],\n", "\n", "        ...,\n", "\n", "        [[-1.0420,  0.4492,  0.3228,  ..., -0.0271,  0.3076, -0.5415]],\n", "\n", "        [[-1.4932,  0.1375,  0.7041,  ...,  1.0732,  0.3276,  0.4136]],\n", "\n", "        [[-0.3135, -0.8828, -0.9688,  ..., -1.3232, -0.0712,  0.8730]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.1504,  0.6143,  0.2869,  ..., -1.3750,  0.1565, -0.7104]],\n", "\n", "        [[-1.0352, -0.2271,  0.2070,  ..., -0.3127,  0.4314, -1.0742]],\n", "\n", "        [[ 1.7930, -0.6650,  1.7764,  ...,  0.9766, -1.8896,  1.2080]],\n", "\n", "        ...,\n", "\n", "        [[-0.7881,  0.4937,  0.4353,  ..., -0.2854,  0.3525, -0.6909]],\n", "\n", "        [[-1.4639, -0.4385,  1.5117,  ...,  1.1533, -0.0903,  0.4585]],\n", "\n", "        [[ 0.3433, -1.0205, -1.5547,  ..., -1.4785, -0.3914,  1.7275]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0488,  0.5654,  0.2754,  ..., -1.3604,  0.2061, -0.8130]],\n", "\n", "        [[-1.2168,  0.8081,  0.8613,  ..., -0.4148,  0.5957, -1.2236]],\n", "\n", "        [[ 1.8623, -0.6470,  1.5898,  ...,  1.0117, -2.3164,  1.5234]],\n", "\n", "        ...,\n", "\n", "        [[-0.9995,  1.1133,  1.1748,  ..., -0.3279,  0.6416, -0.7256]],\n", "\n", "        [[-1.4531, -0.3662,  2.0684,  ...,  0.7715,  0.5830,  0.3267]],\n", "\n", "        [[ 0.5342, -0.4233, -1.4971,  ..., -1.1494, -0.1860,  1.1875]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.2090,  0.3733,  0.4165,  ..., -1.3691,  0.0435, -0.9658]],\n", "\n", "        [[-0.6323,  1.3379,  0.8530,  ..., -0.2327,  0.9746, -1.2881]],\n", "\n", "        [[ 1.8682, -1.3369,  1.1738,  ...,  0.9087, -2.6895,  0.4990]],\n", "\n", "        ...,\n", "\n", "        [[-0.1318,  0.9072,  0.5063,  ..., -0.8125,  1.6699, -0.4771]],\n", "\n", "        [[-2.2754, -0.3081,  2.3008,  ...,  1.4941,  2.0254,  0.5093]],\n", "\n", "        [[ 1.3242, -0.3518, -2.0938,  ..., -1.2900, -0.6733,  1.5127]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.4980,  0.3870,  0.3574,  ..., -1.2920, -0.0659, -1.6553]],\n", "\n", "        [[-0.0911,  0.6597,  0.2788,  ..., -0.6816,  2.0117, -1.9209]],\n", "\n", "        [[ 1.7881, -1.4336,  1.0498,  ...,  1.2969, -3.4141,  0.0225]],\n", "\n", "        ...,\n", "\n", "        [[ 1.0410,  0.8809, -0.5762,  ..., -0.4004,  2.5215,  0.0591]],\n", "\n", "        [[-0.5615, -0.2483,  1.7988,  ...,  1.2275,  2.4316,  0.6133]],\n", "\n", "        [[ 1.5254,  0.9277, -1.8682,  ..., -0.4561, -0.1626,  2.7109]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9648,  0.3818,  0.3896,  ..., -1.5527, -0.0259, -2.0059]],\n", "\n", "        [[ 0.2057,  0.8374,  0.5640,  ..., -0.4653,  3.6523, -2.7188]],\n", "\n", "        [[ 2.2715, -1.0928,  1.7197,  ...,  1.1943, -4.4492, -0.5835]],\n", "\n", "        ...,\n", "\n", "        [[ 2.3477,  1.4883, -1.0547,  ...,  0.2305,  2.3340,  0.0199]],\n", "\n", "        [[ 0.6641,  0.8428,  1.7021,  ...,  2.2188,  2.7422,  0.1719]],\n", "\n", "        [[ 2.7285,  2.2852, -2.4473,  ...,  1.4316,  0.2539,  2.8281]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.2324e+00,  2.3218e-01,  3.8867e-01,  ..., -1.7461e+00,\n", "           8.9355e-01, -2.0449e+00]],\n", "\n", "        [[ 8.5059e-01,  4.6844e-03,  6.6650e-02,  ..., -6.5381e-01,\n", "           6.1562e+00, -2.6211e+00]],\n", "\n", "        [[ 2.5449e+00, -8.5254e-01,  2.0625e+00,  ...,  1.5723e+00,\n", "          -3.9199e+00, -4.9048e-01]],\n", "\n", "        ...,\n", "\n", "        [[ 3.1113e+00,  4.6436e-01, -2.1758e+00,  ...,  3.2642e-01,\n", "           3.6836e+00, -4.5605e-01]],\n", "\n", "        [[ 1.7871e+00,  1.0615e+00,  6.1182e-01,  ...,  2.5703e+00,\n", "           4.5234e+00, -5.8008e-01]],\n", "\n", "        [[ 2.8086e+00,  6.8125e+00, -2.2520e+00,  ...,  2.1895e+00,\n", "           8.1396e-01,  2.5742e+00]]], device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9141,  0.5264,  0.6724,  ..., -1.2520,  0.6792, -2.2910]],\n", "\n", "        [[ 6.7344, -0.5229, -1.3125,  ..., -0.3965,  6.2734, -6.9922]],\n", "\n", "        [[ 3.7344, -1.2832,  0.0605,  ...,  1.3594, -3.2461, -1.7510]],\n", "\n", "        ...,\n", "\n", "        [[ 3.4453,  0.9023, -1.5586,  ..., -0.9224,  3.6699, -1.9492]],\n", "\n", "        [[ 1.8467,  1.6240,  0.9316,  ...,  1.9580,  4.1523, -1.7090]],\n", "\n", "        [[ 2.2207,  8.9375, -1.8145,  ...,  3.7812,  2.7480,  1.9072]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[2356]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3815]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[-0.0380, -0.0440,  0.0305,  ...,  0.0923,  0.0537,  0.0616]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[-0.1379, -0.1597,  0.1108,  ...,  0.3352,  0.1949,  0.2239]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.3979, -0.3315, -0.1948,  ...,  0.7935, -0.2500,  0.0604]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.3840, -0.3386, -0.4956,  ...,  0.6592, -0.2288, -0.0021]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.3652, -0.3691, -0.9365,  ...,  0.5488, -0.2666, -0.1677]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.3240, -0.2354, -1.5996,  ...,  1.1953, -0.7383,  0.1843]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5044, -0.1875, -2.3438,  ...,  0.9194, -1.5586,  0.8408]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0972, -0.1355, -2.7070,  ...,  0.3262, -1.8691,  1.5273]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5791, -1.3799, -2.5859,  ..., -0.0176, -1.9463,  1.6377]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.2190, -2.1641, -2.9395,  ...,  0.3865, -1.9170,  1.4912]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.3652, -2.3535, -3.6719,  ...,  0.9238, -2.4395,  2.1309]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0386, -2.5391, -3.6973,  ...,  0.8418, -2.8574,  2.0156]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.5781, -1.7480, -4.4180,  ..., -0.3403, -2.3184,  1.8818]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.1523, -0.9199, -4.2656,  ..., -2.9180, -1.2598,  0.0840]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[32]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3816]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[-0.0504, -0.0283,  0.0066,  ...,  0.0294, -0.0280,  0.0016]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[-0.1831, -0.1028,  0.0241,  ...,  0.1069, -0.1016,  0.0058]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.1273, -0.0451, -0.0342,  ...,  0.1799, -0.1754, -0.1738]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.1226,  0.1530, -0.1542,  ...,  0.3354, -0.0792, -0.1372]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0762,  0.2532, -0.7500,  ...,  0.1105,  0.0339, -0.2339]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1100,  0.3032, -1.1475,  ...,  0.4097, -0.0618,  0.0715]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.1855,  0.0085, -1.3066,  ..., -0.2317, -0.6240,  0.5410]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1523, -0.5381, -1.8770,  ..., -0.4939, -1.5078,  1.4629]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0225, -1.1133, -1.7705,  ..., -1.0391, -1.2949,  1.3408]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0143, -1.0264, -1.8789,  ..., -1.1914, -1.7207,  1.3936]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0605, -0.6245, -2.5742,  ..., -1.4219, -2.6016,  1.9570]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.9312,  0.0698, -2.1973,  ..., -2.4531, -4.2266,  2.8164]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.1543,  0.1804, -1.6826,  ..., -1.6484, -6.0195,  3.7344]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9209,  1.3037, -0.2354,  ..., -0.8945, -4.5781,  2.9980]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[1295]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3817]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[ 0.0824, -0.0304, -0.0200,  ...,  0.0244,  0.0215,  0.0128]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[ 0.2991, -0.1104, -0.0724,  ...,  0.0886,  0.0780,  0.0465]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.2377, -0.2710, -0.8413,  ..., -0.1417, -0.0192, -0.1746]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1726, -0.1281, -1.2109,  ..., -0.1587,  0.0441, -0.2561]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.3662,  0.0454, -1.9287,  ..., -0.1848,  0.1221, -0.3535]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.3286,  0.2537, -2.2676,  ...,  0.3765, -0.1996, -0.4326]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1173,  0.3960, -2.8926,  ..., -0.4675, -0.8682, -0.3542]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.2598, -0.0198, -3.5645,  ..., -1.2959, -1.8936,  0.4343]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.4355,  0.0344, -3.7930,  ..., -1.5068, -2.4082,  0.1123]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0801, -0.1218, -3.7129,  ..., -2.0117, -2.5645,  0.1570]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.9585, -0.0964, -5.4492,  ..., -2.4082, -3.7227,  0.7695]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.5908,  0.5107, -4.4375,  ..., -4.3672, -3.8008,  0.6338]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.8398, -0.6626, -3.9062,  ..., -5.6250, -3.0430,  2.3398]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.6914,  0.2207, -3.9336,  ..., -6.8906, -2.6055,  0.8184]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[517]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3818]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[-0.0818,  0.0064,  0.0132,  ...,  0.0864, -0.0875, -0.0137]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[-0.2971,  0.0234,  0.0480,  ...,  0.3137, -0.3179, -0.0499]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5605,  0.4043,  0.2135,  ...,  0.6514, -0.9512, -0.2915]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5684,  0.5371,  0.1810,  ...,  0.7832, -0.8853, -0.2554]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5571,  0.7358, -0.3901,  ...,  0.7559, -0.9268, -0.1284]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.2469,  0.5903, -0.7725,  ...,  1.3535, -1.5586,  0.2050]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.5088,  0.2196, -0.6875,  ...,  1.2383, -2.6328,  0.9595]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 4.8828e-04, -1.4062e-01, -1.1191e+00,  ...,  1.7041e+00,\n", "          -3.2363e+00,  1.5342e+00]]], device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.5635e-03, -5.7129e-01, -1.5137e-01,  ...,  1.5674e+00,\n", "          -3.7402e+00,  1.5137e+00]]], device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.6787, -0.3362, -0.1592,  ...,  1.3398, -4.6406,  2.3750]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0728,  0.0361, -0.4004,  ...,  2.1992, -5.0156,  2.5391]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.6621, -1.3066, -0.2571,  ...,  1.8457, -5.1953,  3.7070]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.6963, -1.3066, -0.4272,  ...,  3.3086, -5.6094,  3.5879]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.7070,  0.3315,  1.1211,  ...,  6.0898, -5.5664,  2.1230]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[225]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3819]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[-0.0512,  0.0020,  0.0386,  ...,  0.0296, -0.1166,  0.0392]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[-0.1858,  0.0071,  0.1403,  ...,  0.1074, -0.4236,  0.1425]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.3372,  0.1996,  0.1805,  ...,  0.2241, -0.7939,  0.1331]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.4609,  0.3005,  0.0872,  ...,  0.2676, -0.7822,  0.2656]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.4990,  0.3726, -0.4346,  ...,  0.1299, -0.6895,  0.3862]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.0312,  0.4646, -0.5698,  ...,  0.3892, -1.0391,  0.6987]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1943,  0.0889, -0.0820,  ..., -0.4250, -2.2480,  1.3721]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.7520, -0.4944, -1.2158,  ...,  0.1826, -2.7422,  1.3213]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0908, -1.8145, -0.7837,  ..., -0.2476, -3.7930,  0.7456]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.3770, -1.3008, -0.8032,  ..., -0.4976, -3.9492,  1.7617]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.1875, -2.1719, -1.7441,  ..., -0.5967, -4.0352,  2.4805]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0166, -2.8594, -1.2266,  ...,  0.1738, -3.5469,  2.8086]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.5771, -5.2617, -1.6221,  ...,  2.0469, -3.8203,  2.6543]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.3193, -4.4922, -1.5439,  ...,  1.9668, -2.7734,  0.9004]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[36]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3820]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[-0.1050,  0.0155,  0.0696,  ...,  0.0044, -0.0193,  0.0852]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[-0.3813,  0.0563,  0.2529,  ...,  0.0158, -0.0701,  0.3093]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.6006,  0.3110,  0.5039,  ...,  0.2435, -0.1104,  0.4133]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.6670,  0.4209,  0.4514,  ...,  0.3057, -0.0132,  0.4875]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.7139,  0.3735,  0.1135,  ...,  0.3591,  0.0668,  0.4119]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.2361,  0.3364,  0.1365,  ...,  0.8428, -0.2639,  0.4150]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1338, -0.3782,  0.9131,  ...,  0.4702, -1.2656,  0.7930]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0977, -0.7939,  0.5078,  ...,  0.7334, -1.6807,  1.2695]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[-0.2180, -0.8242,  0.8105,  ..., -0.0948, -2.3711,  1.1777]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.5137, -1.3115,  0.5625,  ..., -1.4766, -3.1895,  1.1963]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9824, -1.4922, -0.4014,  ..., -2.1230, -3.8398,  2.4492]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.8047, -1.0332,  0.1213,  ..., -3.8633, -3.8418,  2.4590]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 4.0859, -1.5459, -0.6230,  ..., -4.4492, -2.8145,  3.1504]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 5.8945,  0.5146, -0.7173,  ..., -6.7891, -1.0859,  0.2642]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe input_ids= tensor([[27]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[3821]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[ 0.0253,  0.0375,  0.0004,  ...,  0.0324, -0.0340,  0.1028]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[ 0.0917,  0.1362,  0.0015,  ...,  0.1177, -0.1235,  0.3733]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.3154,  0.3320,  0.0034,  ...,  0.2935, -0.1724,  0.2612]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.2852,  0.4529, -0.1138,  ...,  0.4119, -0.0518,  0.3501]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.6079,  0.3398, -0.6338,  ...,  0.3579,  0.2505,  0.2004]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0371,  0.0084, -0.5684,  ...,  0.7637, -0.1466,  0.5664]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0117,  0.1674, -0.8726,  ...,  0.6479, -0.6396,  0.5825]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.3936, -0.2925, -0.9678,  ...,  0.6807, -0.8945,  0.1785]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.2305, -0.0399, -0.9302,  ...,  0.6284, -1.3945,  0.5850]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.6118,  0.0133, -2.3555,  ..., -0.5850, -2.3945,  0.6064]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.5840, -0.6372, -2.7305,  ..., -0.2330, -2.9512,  1.5674]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.2090, -0.9814, -2.2227,  ..., -0.6230, -2.5176,  0.8574]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 3.5117, -4.4180, -5.0898,  ..., -3.4375,  1.3994,  2.4883]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 8.9609, -2.9316, -3.3965,  ..., -5.2969,  0.9824, -5.4727]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "np.pi / 2) *\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "# print(data_fim_retrieval[0])\n", "\n", "print(model.raw_generate(\n", "    data[0]['context'],\n", "    GenerationOptions(temperature=0, max_generated_tokens=8),\n", "))\n", "# model.generate(\n", "#     Model\n", "# )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["PipelineEngine(\n", "  (module): GPT2ModelPipe(\n", "    (tied_modules): ModuleDict()\n", "    (0): EmbeddingPipe(\n", "      (word_embeddings): VocabParallelEmbedding()\n", "      (position_embeddings): Embedding(8192, 1024)\n", "      (embedding_dropout): Dropout(p=0.0, inplace=False)\n", "    )\n", "    (2): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (3): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (4): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (5): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (6): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (7): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (8): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (9): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (10): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (11): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (12): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (13): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (key_value): RowParallelLinear()\n", "        (query): ColumnParallelLinear()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (post_attention_layernorm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (15): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "      (norm): LayerNorm((1024,), eps=1e-05, elementwise_affine=True)\n", "    )\n", "    (16): ParallelLinearPipe(\n", "      (final_linear): ColumnParallelLinear()\n", "    )\n", "  )\n", ")"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["model.neox_model"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1., 1., 1., 1., 1., 1., 1., 1., 1., 1.])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "torch.nn.functional.dropout(torch.ones(10), training=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('0.dummy', Parameter containing:\n", "tensor([3.6309], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('0.word_embeddings.weight', Parameter containing:\n", "tensor([[-0.0070, -0.0214,  0.0382,  ..., -0.0253, -0.0721, -0.0057],\n", "        [-0.0086, -0.0708,  0.0196,  ..., -0.0139, -0.0135,  0.0582],\n", "        [-0.0040, -0.1172,  0.0443,  ..., -0.1113,  0.0695,  0.0174],\n", "        ...,\n", "        [-0.0023, -0.0069, -0.0024,  ..., -0.0083, -0.0097, -0.0049],\n", "        [-0.0050,  0.0139,  0.0001,  ...,  0.0043, -0.0154, -0.0086],\n", "        [-0.0015,  0.0046, -0.0061,  ...,  0.0137,  0.0030, -0.0029]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('0.position_embeddings.weight', Parameter containing:\n", "tensor([[ 0.0131,  0.0416, -0.0105,  ..., -0.0131,  0.0190,  0.0393],\n", "        [-0.0150, -0.0168,  0.0133,  ..., -0.0521, -0.0032, -0.0124],\n", "        [-0.0034, -0.0228,  0.0101,  ..., -0.0037,  0.0083, -0.0428],\n", "        ...,\n", "        [ 0.0036, -0.0057, -0.0068,  ...,  0.0065,  0.0088, -0.0083],\n", "        [-0.0030,  0.0053,  0.0008,  ..., -0.0068, -0.0044,  0.0109],\n", "        [ 0.0033,  0.0024, -0.0131,  ..., -0.0116,  0.0023, -0.0099]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.input_layernorm.weight', Parameter containing:\n", "tensor([0.2700, 0.3198, 0.3418,  ..., 0.3357, 0.2466, 0.2053], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('2.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0445,  0.0775,  0.0255,  ..., -0.0143, -0.0393, -0.0353],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0766, -0.0408,  0.0077,  ...,  0.0013, -0.0663,  0.0635],\n", "        [ 0.1423,  0.0709,  0.0123,  ..., -0.0303,  0.0651,  0.0011],\n", "        [ 0.0439, -0.0506,  0.0383,  ..., -0.0684, -0.0256,  0.0188],\n", "        ...,\n", "        [-0.0119,  0.0100,  0.0098,  ..., -0.0200,  0.0130, -0.0069],\n", "        [-0.0246, -0.0133,  0.0071,  ..., -0.0064, -0.0150,  0.0078],\n", "        [-0.0075, -0.0156,  0.0151,  ...,  0.0094,  0.0021, -0.0126]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.key_value.bias', Parameter containing:\n", "tensor([ 3.9771e-01,  8.3618e-02, -1.2024e-01, -1.3306e-01, -3.3862e-01,\n", "        -2.2009e-01, -2.3779e-01,  8.3130e-02, -4.7180e-02,  3.5400e-01,\n", "         9.0759e-02,  1.0364e-01, -5.9601e-02, -6.1768e-02,  1.0272e-01,\n", "        -2.9190e-02, -2.3340e-01, -3.1323e-01, -1.1707e-01, -9.9854e-02,\n", "         1.8872e-01,  1.0547e-01, -5.0751e-02,  1.4417e-01,  8.7402e-02,\n", "         1.6919e-01, -2.7222e-02, -9.8083e-02,  1.1652e-01, -2.2644e-02,\n", "         1.4160e-01, -1.8359e-01,  4.6533e-01, -1.7188e-01,  8.6975e-02,\n", "        -1.2219e-01, -1.5454e-01, -6.9702e-02, -2.3209e-02,  1.5015e-01,\n", "         4.7998e-01,  5.4108e-02,  6.6345e-02, -1.3147e-01,  7.9468e-02,\n", "        -4.4458e-01,  3.0566e-01, -4.4479e-03,  1.6373e-02, -1.6431e-01,\n", "         2.0035e-02, -1.4758e-01,  1.5637e-01,  3.6278e-03, -2.2070e-01,\n", "         1.0864e-01,  1.2455e-03, -2.5000e-01,  4.7180e-02, -1.7908e-01,\n", "         7.5111e-03, -2.9614e-01,  9.0179e-03, -1.0547e-01, -5.4382e-02,\n", "        -7.4097e-02,  4.3335e-01, -2.8516e-01,  2.1305e-03,  1.3504e-02,\n", "         4.6936e-02,  1.2951e-03,  1.5527e-01,  3.0151e-02, -3.7567e-02,\n", "        -3.1030e-01,  1.3855e-01, -1.8201e-01,  1.6577e-01, -1.3525e-01,\n", "         5.1392e-02, -1.9373e-01,  4.5204e-03,  4.2725e-01, -8.4961e-02,\n", "         3.0853e-02, -1.2854e-01, -1.3293e-01, -4.4060e-03, -3.9209e-01,\n", "         1.2680e-02, -1.3232e-01, -2.1741e-01, -4.8096e-02,  7.6904e-02,\n", "        -3.2562e-02,  1.6174e-02, -6.7139e-02, -1.1621e-01, -2.0044e-01,\n", "         1.4478e-01,  3.5156e-02, -1.3782e-01, -8.0078e-02,  1.4392e-01,\n", "         2.2903e-02,  1.1871e-01, -1.0107e-01, -1.0260e-01, -2.6596e-02,\n", "        -6.4636e-02, -8.1482e-02,  3.8696e-02,  1.0101e-01,  2.7115e-02,\n", "        -3.1982e-02, -9.1370e-02, -1.1188e-01, -3.4424e-01,  9.1095e-03,\n", "        -1.1963e-01, -1.8311e-01,  4.3628e-01, -1.4441e-01, -4.5593e-02,\n", "        -1.6113e-01,  3.2532e-02,  3.0243e-02, -1.9264e-04,  4.5242e-03,\n", "        -5.7602e-03,  6.1989e-03,  5.8441e-02, -4.9210e-03,  3.8509e-03,\n", "        -7.6151e-04,  7.5607e-03,  2.3994e-03,  2.2149e-04,  3.8223e-03,\n", "        -4.6120e-03, -1.4868e-03,  4.8485e-03,  5.4169e-03, -9.7351e-03,\n", "         8.9586e-05,  7.5626e-04, -3.0785e-03,  3.9368e-03, -6.5079e-03,\n", "         3.0041e-03,  3.1624e-03,  4.1161e-03,  2.8954e-03, -1.0666e-02,\n", "         2.0313e-03, -7.7553e-03, -4.8027e-03, -9.5034e-04,  6.4507e-03,\n", "        -2.2392e-03,  6.9737e-06,  3.2187e-04,  2.2526e-03,  9.8953e-03,\n", "         1.7262e-03,  2.5330e-03, -1.6794e-03, -5.0247e-05,  1.6727e-03,\n", "         1.3447e-03,  1.7948e-03, -5.6534e-03, -5.1575e-03,  4.4990e-04,\n", "         1.6357e-02, -2.5520e-03,  4.6277e-04, -2.3422e-03,  6.3171e-03,\n", "         4.1809e-03,  5.2071e-03, -7.6056e-04, -1.0643e-03,  3.0422e-03,\n", "         2.8076e-03,  6.7215e-03,  4.2076e-03, -6.4697e-03, -7.4043e-03,\n", "         1.4372e-03, -6.1073e-03,  6.5117e-03, -8.6832e-04, -9.1553e-03,\n", "        -3.9825e-03, -3.0575e-03, -8.0795e-03, -3.8624e-03, -1.0338e-02,\n", "        -5.6763e-03, -6.1607e-04,  7.3242e-03,  3.6316e-03, -5.6572e-03,\n", "        -6.7139e-03, -3.1033e-03, -2.6417e-03,  4.8370e-03,  1.3962e-03,\n", "         4.5395e-03, -4.2152e-03, -5.1422e-03, -6.2332e-03, -1.7405e-03,\n", "        -9.2697e-04,  9.0561e-03, -2.6455e-03,  4.5228e-04, -2.3346e-03,\n", "        -2.7771e-03, -1.8349e-03, -4.2915e-03,  9.9182e-04, -1.3647e-03,\n", "         6.5460e-03, -3.8204e-03, -6.9275e-03, -1.1063e-03,  3.4122e-03,\n", "        -7.4921e-03, -1.8883e-04,  2.1744e-03,  3.4370e-03, -1.2436e-02,\n", "        -3.8109e-03,  1.2802e-02, -2.5635e-03,  1.4286e-03, -5.8937e-04,\n", "        -8.6365e-03, -3.8576e-04,  1.9817e-03,  4.3831e-03,  3.4180e-03,\n", "        -1.6546e-03, -2.6550e-03,  1.4734e-03, -5.5599e-04, -3.5076e-03,\n", "        -3.1109e-03, -1.8402e-02, -1.3626e-04,  4.4594e-03,  1.9913e-03,\n", "        -5.0201e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.query.weight', Parameter containing:\n", "tensor([[-0.0203, -0.0206,  0.0153,  ..., -0.0618,  0.0281,  0.0481],\n", "        [ 0.0619,  0.0221, -0.0042,  ...,  0.0004, -0.0144, -0.0512],\n", "        [ 0.0048,  0.0109,  0.0436,  ...,  0.0040, -0.0474,  0.0479],\n", "        ...,\n", "        [-0.0977, -0.0395,  0.0923,  ...,  0.0576, -0.0024,  0.0537],\n", "        [ 0.0448,  0.0573, -0.0237,  ...,  0.0994, -0.0762,  0.0136],\n", "        [ 0.0935,  0.0111,  0.0386,  ...,  0.1003, -0.0275,  0.0751]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.query.bias', Parameter containing:\n", "tensor([ 0.4216,  0.1890,  0.0095,  ..., -0.0989, -0.0543, -0.0111],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0190, -0.0056,  0.0241,  ..., -0.0046, -0.0105, -0.0182],\n", "        [ 0.0340, -0.0100, -0.0064,  ..., -0.0106,  0.0083, -0.0062],\n", "        [-0.0021,  0.0152, -0.0017,  ..., -0.0013,  0.0101,  0.0003],\n", "        ...,\n", "        [ 0.0233,  0.0048,  0.0129,  ...,  0.0241,  0.0118,  0.0327],\n", "        [ 0.0246,  0.0189,  0.0039,  ...,  0.0023,  0.0069, -0.0226],\n", "        [ 0.0371,  0.0359,  0.0111,  ...,  0.0083,  0.0039, -0.0011]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.attention.dense.bias', Parameter containing:\n", "tensor([-0.0152, -0.0079, -0.0457,  ...,  0.0148, -0.0243,  0.0560],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.7349, 0.7515, 0.7812,  ..., 0.7344, 0.7290, 0.7993], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('2.post_attention_layernorm.bias', Parameter containing:\n", "tensor([ 0.0309, -0.0205,  0.0195,  ..., -0.0082,  0.0057, -0.0164],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["('2.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[-0.0247, -0.0820, -0.0414,  ..., -0.0070,  0.0731,  0.0450],\n", "        [-0.0075,  0.0459,  0.0291,  ..., -0.0121, -0.0373, -0.0283],\n", "        [ 0.0168, -0.0271, -0.0504,  ..., -0.0325, -0.0458,  0.0493],\n", "        ...,\n", "        [ 0.0443,  0.0020, -0.0397,  ...,  0.0520,  0.0079, -0.0037],\n", "        [-0.0450,  0.0038,  0.0303,  ..., -0.0441, -0.0095,  0.0154],\n", "        [ 0.0034, -0.0192,  0.0143,  ...,  0.0113, -0.0166, -0.0486]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0659, -0.0350, -0.0648,  ..., -0.0357, -0.0187, -0.0106],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-0.0084, -0.0053,  0.0133,  ..., -0.0096,  0.0073, -0.0334],\n", "        [-0.0154,  0.0065,  0.0137,  ...,  0.0070, -0.0106,  0.0291],\n", "        [ 0.0081,  0.0151, -0.0284,  ..., -0.0443, -0.0368,  0.0135],\n", "        ...,\n", "        [ 0.0118,  0.0170, -0.0237,  ...,  0.0044, -0.0081, -0.0045],\n", "        [ 0.0102, -0.0145,  0.0033,  ..., -0.0433, -0.0141, -0.0157],\n", "        [ 0.0024, -0.0388, -0.0051,  ...,  0.0029, -0.0038,  0.0008]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('2.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0153, -0.0196, -0.0914,  ...,  0.0732, -0.0581,  0.0805],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.input_layernorm.weight', Parameter containing:\n", "tensor([0.8623, 0.5811, 0.7837,  ..., 0.7271, 0.6582, 0.7085], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('3.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0118,  0.0428, -0.1793,  ...,  0.1757, -0.0327,  0.0072],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.key_value.weight', Parameter containing:\n", "tensor([[ 0.0122, -0.0054,  0.0155,  ..., -0.0459,  0.0158,  0.0261],\n", "        [-0.0204,  0.0247, -0.0154,  ..., -0.0323,  0.0360,  0.0028],\n", "        [ 0.0558,  0.0084,  0.0003,  ...,  0.0198, -0.0126,  0.0203],\n", "        ...,\n", "        [ 0.0277, -0.0425, -0.0057,  ..., -0.0065,  0.0201, -0.0135],\n", "        [ 0.0286, -0.0415, -0.0022,  ..., -0.0397,  0.0425, -0.0002],\n", "        [ 0.0078, -0.0035, -0.0237,  ...,  0.0121,  0.0210, -0.0427]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.key_value.bias', Parameter containing:\n", "tensor([ 2.6953e+00,  1.7520e+00,  6.1182e-01, -9.4043e-01,  2.4182e-01,\n", "         7.4951e-01, -1.1611e+00, -6.4990e-01,  1.3594e+00, -2.4023e+00,\n", "        -2.4297e+00, -3.9825e-02,  1.3125e+00,  6.6016e-01,  1.0625e+00,\n", "         1.9446e-01,  3.0195e+00, -3.4790e-02,  9.0039e-01, -1.9092e+00,\n", "        -1.3936e+00,  1.0762e+00,  1.3171e-01, -4.5996e-01,  1.0518e+00,\n", "        -1.0293e+00, -1.5479e+00, -4.3793e-03,  1.7305e+00,  5.4639e-01,\n", "         1.1328e+00,  2.2441e+00,  2.2793e+00,  4.4342e-02,  1.3623e+00,\n", "         1.1279e+00,  1.8018e+00, -7.8174e-01, -6.9922e-01, -9.9121e-01,\n", "        -2.2637e+00,  3.8306e-01, -1.5205e+00,  2.2734e+00,  2.6978e-01,\n", "         2.5938e+00,  2.7363e+00, -2.7266e+00, -7.0898e-01,  1.6650e+00,\n", "        -7.9736e-01,  2.5566e+00,  7.4512e-01, -1.2334e+00,  1.4490e-01,\n", "         3.3184e+00,  5.0537e-02,  4.6338e-01, -9.1797e-01, -6.3379e-01,\n", "         2.8857e-01, -1.3877e+00,  1.7979e+00,  2.0430e+00,  5.8984e-01,\n", "        -2.8125e-01, -9.8633e-01,  4.5874e-01, -1.6982e+00, -3.9868e-01,\n", "        -9.5459e-01, -3.1602e+00, -4.6021e-01, -1.3831e-01,  1.3203e+00,\n", "         4.2896e-01,  2.0142e-02, -1.5410e+00, -3.2440e-02, -6.5137e-01,\n", "        -5.9473e-01,  4.8523e-02,  9.5605e-01, -1.1094e+00,  7.2070e-01,\n", "         1.6533e+00,  1.7761e-01, -8.9600e-02, -1.0046e-01, -2.0898e+00,\n", "        -6.7578e-01, -1.0225e+00, -1.9229e+00,  2.3083e-01, -1.4814e+00,\n", "        -4.4128e-02,  7.7979e-01, -1.5889e+00,  3.6582e+00,  2.1545e-01,\n", "         1.7041e+00,  1.9226e-01,  1.6611e+00,  1.6094e+00,  2.3163e-02,\n", "         3.8940e-01, -5.4541e-01,  8.1885e-01,  2.4180e+00,  1.0479e+00,\n", "        -3.3633e+00,  6.6345e-02, -5.8252e-01, -1.3350e+00, -1.6484e+00,\n", "         1.1602e+00, -7.3535e-01, -1.7939e+00,  2.8394e-01, -1.2285e+00,\n", "         3.2148e+00,  1.6638e-01,  1.7686e+00, -1.0273e+00,  2.4180e+00,\n", "         7.8125e-01,  3.7646e-01, -3.7598e+00, -1.4854e-02, -5.1212e-04,\n", "         1.3504e-02,  8.7051e-03,  3.3283e-03, -1.4206e-02, -3.4547e-04,\n", "         3.8719e-03, -6.1684e-03,  2.4910e-03, -1.0086e-02,  1.1749e-02,\n", "         8.8654e-03,  2.9068e-03, -6.4163e-03, -5.9395e-03,  4.1389e-03,\n", "        -3.9864e-03,  3.1643e-03,  5.6305e-03, -1.7300e-03,  2.1362e-03,\n", "         1.8494e-02, -8.8882e-03, -1.2581e-02,  1.9104e-02, -3.2654e-03,\n", "         4.5753e-04, -1.0750e-02, -1.6098e-02,  2.4281e-03, -1.9836e-02,\n", "        -4.7264e-03,  1.7271e-03, -1.9760e-03, -1.1950e-03, -1.1263e-03,\n", "         1.1215e-02,  4.0245e-03,  7.4158e-03, -1.0841e-02,  4.5967e-03,\n", "        -4.7874e-03,  2.1801e-03, -2.7657e-04, -2.5711e-02, -2.1992e-03,\n", "        -2.2385e-02,  4.6463e-03, -9.9301e-05,  1.1887e-02, -5.5771e-03,\n", "        -1.1208e-02,  4.3068e-03,  1.0559e-02,  1.4477e-03,  5.7220e-03,\n", "         2.9373e-04, -1.0691e-03, -4.2856e-05, -4.5395e-03, -7.2479e-03,\n", "         2.1210e-03,  5.1079e-03, -2.9907e-03, -1.6876e-02, -3.8490e-03,\n", "         3.6144e-03, -7.6675e-03,  1.0887e-02,  9.2850e-03,  1.0307e-02,\n", "        -1.3573e-02, -8.2779e-04,  2.6298e-04, -4.4556e-03, -1.7136e-02,\n", "         2.2163e-03,  3.6564e-03,  1.2611e-02,  5.7983e-03,  7.8087e-03,\n", "        -4.4899e-03,  3.6373e-03,  2.5681e-02, -3.0193e-03, -5.2986e-03,\n", "        -7.2441e-03, -1.1971e-02,  1.1337e-02,  3.6564e-03, -1.2047e-02,\n", "        -8.2855e-03,  6.3972e-03, -3.7441e-03,  2.6894e-03, -1.3733e-02,\n", "         1.0948e-02, -7.2403e-03, -1.7914e-02, -5.6343e-03,  2.8858e-03,\n", "         1.2589e-02, -1.7075e-02, -1.1002e-02, -1.8219e-02, -5.9509e-03,\n", "         1.1826e-02, -7.1716e-04, -1.6769e-02,  1.0155e-02, -1.5533e-04,\n", "        -2.3224e-02, -1.7185e-03,  1.7719e-03, -1.5736e-03,  3.0022e-03,\n", "         8.3771e-03, -3.8013e-03, -6.5136e-04,  1.5526e-02, -2.0157e-02,\n", "         3.1948e-03,  4.0970e-03,  3.6526e-03,  7.4005e-03, -1.5198e-02,\n", "         1.0078e-02], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0042, -0.0228,  0.0025,  ..., -0.0110, -0.0196,  0.0081],\n", "        [ 0.0063, -0.0096,  0.0187,  ...,  0.0025, -0.0187, -0.0029],\n", "        [-0.0152,  0.0174, -0.0002,  ...,  0.0160,  0.0324, -0.0102],\n", "        ...,\n", "        [-0.0308, -0.0502, -0.0526,  ...,  0.0112, -0.0368,  0.0759],\n", "        [ 0.0356,  0.0352,  0.0362,  ..., -0.0202,  0.0473, -0.0587],\n", "        [-0.1329,  0.0112,  0.2040,  ..., -0.0672,  0.0503, -0.0881]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.query.bias', Parameter containing:\n", "tensor([ 0.3494,  0.0997,  0.2177,  ..., -0.0153, -0.0224, -0.5376],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0215, -0.0056, -0.0220,  ..., -0.0113,  0.0085, -0.0062],\n", "        [ 0.0015,  0.0002,  0.0125,  ..., -0.0250, -0.0302, -0.0120],\n", "        [-0.0145,  0.0298, -0.0372,  ..., -0.0293, -0.0020,  0.0082],\n", "        ...,\n", "        [-0.0190, -0.0297,  0.0544,  ..., -0.0041,  0.0096, -0.0024],\n", "        [ 0.0108, -0.0063,  0.0012,  ...,  0.0413, -0.0886,  0.0166],\n", "        [-0.0065,  0.0047, -0.0045,  ...,  0.0557,  0.0093,  0.0357]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.attention.dense.bias', Parameter containing:\n", "tensor([-3.1082e-02,  1.5140e-05, -4.1473e-02,  ..., -4.3121e-02,\n", "        -2.8473e-02,  6.7749e-02], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('3.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.7114, 0.7593, 0.8848,  ..., 0.7959, 0.8335, 0.8423], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('3.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0568, -0.0014, -0.0780,  ...,  0.0311, -0.0934,  0.0660],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0046,  0.0317,  0.0465,  ..., -0.0006, -0.0188,  0.0298],\n", "        [ 0.0446, -0.0564,  0.0125,  ..., -0.0249,  0.0034, -0.0234],\n", "        [-0.0298, -0.0142,  0.0161,  ...,  0.0786,  0.0427,  0.0356],\n", "        ...,\n", "        [ 0.0325, -0.0142, -0.0053,  ...,  0.0004,  0.0148, -0.0050],\n", "        [-0.0063,  0.0641, -0.0396,  ..., -0.0505, -0.0358,  0.0316],\n", "        [ 0.0794, -0.0009,  0.0279,  ..., -0.0518,  0.0544, -0.0519]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([ 0.0036,  0.0045, -0.0166,  ...,  0.0054, -0.0090, -0.0983],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-0.0037, -0.0225, -0.0084,  ..., -0.0211,  0.0056, -0.0169],\n", "        [-0.0207,  0.0314,  0.0094,  ...,  0.0022, -0.0159,  0.0131],\n", "        [-0.0383, -0.0027,  0.0192,  ...,  0.0147,  0.0307,  0.0582],\n", "        ...,\n", "        [-0.0030,  0.0121, -0.0213,  ..., -0.0022,  0.0116, -0.0236],\n", "        [ 0.0049,  0.0023, -0.0009,  ..., -0.0189,  0.0121, -0.0285],\n", "        [-0.0157,  0.0156,  0.0272,  ..., -0.0047, -0.0115, -0.0543]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('3.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0148, -0.0425, -0.0232,  ...,  0.0430, -0.0233,  0.0496],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.input_layernorm.weight', Parameter containing:\n", "tensor([0.7280, 0.8008, 0.8232,  ..., 0.7461, 0.7988, 0.7666], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('4.input_layernorm.bias', Parameter containing:\n", "tensor([-0.0317,  0.0341, -0.0096,  ..., -0.0153,  0.0002,  0.0043],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0066, -0.0181,  0.0027,  ..., -0.0149,  0.0360,  0.0147],\n", "        [ 0.0096, -0.0094,  0.0070,  ...,  0.0086,  0.0326, -0.0172],\n", "        [-0.0358,  0.0240, -0.0010,  ...,  0.0681, -0.0389,  0.0181],\n", "        ...,\n", "        [-0.0372, -0.0052, -0.0264,  ...,  0.0045,  0.0043, -0.0490],\n", "        [ 0.0203, -0.0140,  0.0158,  ..., -0.0360, -0.0035,  0.0050],\n", "        [-0.0158,  0.0269, -0.0085,  ..., -0.0005,  0.0355,  0.0294]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.attention.key_value.bias', Parameter containing:\n", "tensor([-3.8135e-01, -8.6914e-01,  1.5440e-03, -5.1514e-01, -4.9414e-01,\n", "        -8.7158e-01,  5.7812e-01, -1.1102e-01,  5.9668e-01, -5.8252e-01,\n", "        -3.7183e-01,  3.2983e-01, -2.4817e-01,  8.9551e-01, -2.1973e-01,\n", "         6.1670e-01,  4.9683e-01, -1.6040e-01, -1.2109e+00,  1.0052e-01,\n", "         1.0574e-02,  3.3887e-01, -9.5520e-02, -7.5635e-01,  1.4355e+00,\n", "        -8.7842e-01, -4.7168e-01,  1.0879e+00, -1.7334e+00, -1.6650e-01,\n", "         2.1729e-01,  4.8145e-01,  2.2656e-01,  1.5801e+00,  8.3374e-02,\n", "        -1.6174e-01,  3.5986e-01, -3.0981e-01,  1.5693e+00,  2.9517e-01,\n", "         3.2568e-01, -7.8125e-01,  6.5820e-01, -4.3604e-01, -3.5278e-01,\n", "         1.0303e+00, -7.7209e-02,  5.0684e-01,  1.2178e+00, -7.3926e-01,\n", "         1.4316e+00, -8.7012e-01, -3.0493e-01, -1.4033e+00, -8.9014e-01,\n", "        -6.0693e-01,  3.1299e-01,  8.6670e-01, -9.9182e-02,  1.0107e+00,\n", "         6.1963e-01,  9.6484e-01, -2.7930e-01, -1.1787e+00,  2.7075e-01,\n", "        -1.5051e-01,  9.6436e-02,  1.3586e-01, -1.6504e-01, -1.3457e+00,\n", "        -1.2529e+00, -1.0977e+00,  5.1709e-01, -8.7549e-01,  1.2246e+00,\n", "        -2.2644e-01,  1.7859e-01, -4.0674e-01,  1.3408e+00,  5.1904e-01,\n", "        -1.0820e+00,  6.5479e-01,  1.2129e+00,  3.4155e-01, -3.1299e-01,\n", "        -3.8513e-02, -1.1504e+00,  1.3367e-01, -1.0176e+00, -1.1987e-01,\n", "        -2.3163e-02, -8.8525e-01,  1.8433e-01, -1.3599e-01,  1.2915e-01,\n", "        -1.2170e-01,  9.6207e-03, -9.5508e-01,  6.4258e-01, -2.4451e-01,\n", "         3.2520e-01, -9.0576e-01, -5.2930e-01,  1.2396e-01, -1.1084e+00,\n", "         3.5205e-01,  8.7988e-01,  8.5840e-01, -8.8770e-01, -2.5684e-01,\n", "        -1.2244e-01,  6.2207e-01, -4.2651e-01, -8.8770e-01,  3.4180e-01,\n", "        -5.1611e-01, -1.3418e+00, -1.0687e-01,  9.0381e-01,  4.7583e-01,\n", "        -6.9824e-02,  3.6646e-01, -3.8086e-01, -1.8384e-01, -1.1094e+00,\n", "        -8.0859e-01,  4.6240e-01,  2.6138e-02, -3.9291e-03, -1.6876e-02,\n", "         1.3321e-02,  1.4465e-02,  1.1955e-02, -5.0774e-03,  1.4160e-02,\n", "         6.3038e-04,  5.5389e-03,  8.4839e-03, -1.6190e-02,  1.4877e-02,\n", "        -1.3153e-02, -7.7858e-03,  6.0425e-03, -1.2093e-02,  1.5125e-03,\n", "        -2.2888e-03,  1.0818e-02,  9.0027e-03,  8.6212e-03,  1.2718e-02,\n", "        -4.9019e-03, -9.9869e-03,  9.9487e-03, -1.0979e-02,  1.1116e-02,\n", "         5.4474e-03,  1.7653e-03,  1.2306e-02, -3.3092e-03, -4.0474e-03,\n", "        -9.3460e-03,  8.1940e-03,  4.7417e-03,  1.7567e-03,  3.1948e-05,\n", "        -1.1093e-02, -1.6022e-02, -4.1809e-03, -9.1743e-04, -5.7602e-03,\n", "         2.0477e-02, -2.4681e-03, -1.2550e-02,  2.7885e-03, -9.6817e-03,\n", "         1.0094e-02,  1.7838e-02,  6.7024e-03, -5.0278e-03,  4.0779e-03,\n", "         3.2501e-03,  2.5425e-03, -8.1863e-03,  2.4462e-04, -8.4000e-03,\n", "        -1.6618e-04,  1.0559e-02, -1.4439e-03, -5.0392e-03,  9.6359e-03,\n", "        -7.1430e-04,  1.0460e-02, -3.3321e-03,  3.4485e-03, -6.5575e-03,\n", "         1.5656e-02, -1.3718e-02, -8.2092e-03,  7.9956e-03, -9.8190e-03,\n", "        -1.6769e-02, -9.3994e-03,  6.5374e-04,  5.8327e-03, -1.2230e-02,\n", "         8.7891e-03, -4.1466e-03,  8.4000e-03,  2.4033e-03, -2.6260e-02,\n", "        -2.3376e-02, -2.3651e-02, -1.2772e-02, -2.6596e-02,  5.3940e-03,\n", "        -2.1194e-02,  8.8425e-03, -4.1351e-03, -2.2488e-03,  1.7029e-02,\n", "        -1.7944e-02, -1.3247e-03,  1.0967e-03,  6.6605e-03,  8.6365e-03,\n", "         1.3725e-02,  4.4289e-03,  2.3022e-03,  2.1148e-04, -4.0092e-03,\n", "        -7.1373e-03, -3.0956e-03,  9.1248e-03, -1.3557e-02,  1.0597e-02,\n", "         6.6338e-03, -1.8356e-02,  6.8092e-03, -2.1271e-02, -7.6180e-03,\n", "        -6.3171e-03,  7.0457e-03, -3.0918e-03, -7.8735e-03,  2.7603e-02,\n", "        -1.5305e-02, -9.3689e-03,  6.4659e-03,  1.6205e-02,  2.1805e-02,\n", "         3.5629e-03,  4.6120e-03, -1.6541e-02,  1.3065e-03, -2.1484e-02,\n", "         3.6907e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.attention.query.weight', Parameter containing:\n", "tensor([[-5.6190e-03,  1.8082e-02, -4.1229e-02,  ...,  5.5420e-02,\n", "          3.3539e-02,  4.6692e-02],\n", "        [ 1.6220e-02, -3.3226e-03,  4.1351e-02,  ..., -3.1708e-02,\n", "          4.6158e-03, -6.3354e-02],\n", "        [-2.4765e-02,  1.0376e-03,  2.8458e-02,  ...,  4.0833e-02,\n", "          1.8692e-02, -1.0391e-02],\n", "        ...,\n", "        [-1.0452e-02, -3.6835e-02,  2.8229e-02,  ...,  2.0966e-02,\n", "          2.4765e-02, -1.0406e-02],\n", "        [ 1.3100e-02, -2.2873e-02, -2.1362e-02,  ...,  2.3098e-03,\n", "         -4.6967e-02,  3.6926e-03],\n", "        [-4.4312e-02,  3.5286e-05, -3.3417e-02,  ..., -7.8201e-03,\n", "         -1.2695e-02,  1.7593e-02]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('4.attention.query.bias', Parameter containing:\n", "tensor([-0.0214, -0.0596, -0.0127,  ..., -0.0495,  0.0232, -0.0124],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0111,  0.0249,  0.0028,  ...,  0.0276,  0.0175,  0.0003],\n", "        [-0.0374,  0.0220, -0.0283,  ...,  0.0058,  0.0111,  0.0345],\n", "        [-0.0208,  0.0066, -0.0107,  ...,  0.0299, -0.0001, -0.0134],\n", "        ...,\n", "        [-0.0120, -0.0120, -0.0063,  ..., -0.0083,  0.0027, -0.0160],\n", "        [-0.0264,  0.0187,  0.0235,  ...,  0.0016, -0.0344,  0.0380],\n", "        [-0.0016, -0.0185,  0.0248,  ..., -0.0627,  0.0336, -0.0297]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.attention.dense.bias', Parameter containing:\n", "tensor([-0.0221, -0.0446, -0.0473,  ...,  0.0274,  0.0507,  0.0204],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.6567, 0.9761, 0.9854,  ..., 1.0547, 0.9712, 1.2168], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('4.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0687, -0.0669, -0.1425,  ...,  0.0494, -0.0519,  0.2332],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0440,  0.0147,  0.0151,  ...,  0.0038,  0.0479, -0.0827],\n", "        [ 0.0145, -0.0275, -0.0439,  ..., -0.0165,  0.0512,  0.0266],\n", "        [ 0.0446,  0.0625,  0.0296,  ..., -0.0364, -0.0178, -0.0687],\n", "        ...,\n", "        [ 0.0412,  0.0021,  0.0362,  ...,  0.0012,  0.0185,  0.0300],\n", "        [-0.0261,  0.0057, -0.0180,  ..., -0.0108, -0.0185, -0.0444],\n", "        [-0.0099,  0.0334,  0.0149,  ..., -0.0241,  0.0521,  0.0070]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0626, -0.0566, -0.0685,  ..., -0.0123, -0.0362, -0.0409],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-0.0288, -0.0085,  0.0211,  ..., -0.0414,  0.0167,  0.0200],\n", "        [-0.0134, -0.0218,  0.0454,  ..., -0.0073, -0.0174,  0.0158],\n", "        [ 0.0048,  0.0420,  0.0140,  ..., -0.0284,  0.0055, -0.0005],\n", "        ...,\n", "        [-0.0545,  0.0385, -0.0072,  ...,  0.0141, -0.0363,  0.0279],\n", "        [ 0.0109,  0.0067, -0.0511,  ...,  0.0016,  0.0060,  0.0188],\n", "        [ 0.0068, -0.0181, -0.0677,  ..., -0.0287, -0.0139, -0.0258]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('4.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([ 0.0066, -0.0140,  0.0096,  ..., -0.0312,  0.0133,  0.0086],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.input_layernorm.weight', Parameter containing:\n", "tensor([0.7622, 0.8853, 0.8628,  ..., 0.9839, 0.9165, 0.9512], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('5.input_layernorm.bias', Parameter containing:\n", "tensor([-0.0021,  0.0030,  0.0822,  ..., -0.0353,  0.0788, -0.0582],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0165, -0.0339, -0.0129,  ..., -0.0519, -0.0119,  0.0003],\n", "        [-0.0304,  0.0354, -0.0398,  ...,  0.0583,  0.0014, -0.0268],\n", "        [ 0.0093, -0.0070,  0.0716,  ..., -0.0252, -0.0069,  0.0352],\n", "        ...,\n", "        [-0.0085, -0.0130, -0.0368,  ..., -0.0229,  0.0170,  0.0315],\n", "        [ 0.0113, -0.0012, -0.0352,  ...,  0.0487,  0.0320, -0.0429],\n", "        [-0.0529, -0.0338,  0.0523,  ..., -0.0361,  0.0381, -0.0023]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.key_value.bias', Parameter containing:\n", "tensor([ 5.0684e-01,  9.2383e-01, -1.3586e-01, -6.3672e-01, -4.9048e-01,\n", "         6.2805e-02, -5.6689e-01,  8.1421e-02,  2.3132e-01, -2.8589e-01,\n", "        -2.5952e-01,  6.1523e-01, -4.4434e-02,  3.9380e-01, -9.9854e-02,\n", "        -4.1724e-01,  1.0635e+00,  7.1875e-01, -4.5508e-01,  2.7319e-01,\n", "         3.9185e-01,  4.8438e-01,  4.3262e-01, -4.7998e-01, -6.4502e-01,\n", "        -2.0679e-01,  6.4307e-01,  1.0879e+00, -8.2275e-02,  2.6514e-01,\n", "         3.2501e-02,  9.6970e-03, -5.7129e-01, -2.5098e-01,  4.8413e-01,\n", "         1.4075e-01,  2.3560e-01, -9.0942e-02,  1.6541e-01,  5.2246e-01,\n", "        -3.7915e-01,  1.1426e-01,  3.7769e-01,  3.7183e-01,  1.0459e+00,\n", "         2.4634e-01,  7.5928e-02,  1.7004e-01,  2.4646e-01, -5.8685e-02,\n", "         7.0703e-01,  2.4133e-01,  6.1426e-01,  1.7163e-01, -3.3984e-01,\n", "         3.3057e-01,  1.6541e-01, -1.0248e-01, -1.6565e-01,  7.5256e-02,\n", "         2.9858e-01, -6.1133e-01, -9.5947e-01,  1.8140e-01,  4.3604e-01,\n", "        -1.6602e-01,  4.5068e-01,  5.0928e-01,  2.9248e-01,  1.0895e-01,\n", "         3.3447e-01,  2.8149e-01, -3.1543e-01, -4.8657e-01, -1.0645e-01,\n", "         2.6131e-03,  2.3718e-01, -1.1514e+00, -3.6011e-01,  9.4629e-01,\n", "         4.0918e-01, -6.3965e-01, -7.7942e-02, -1.9943e-02,  8.4668e-01,\n", "         1.4111e-01,  5.7190e-02,  5.3174e-01,  1.4478e-01, -1.0187e-01,\n", "        -3.0103e-01,  1.0391e+00, -5.4736e-01,  4.2114e-01, -1.4868e-01,\n", "        -1.0327e-01, -7.6514e-01, -9.2578e-01,  9.7949e-01,  4.4897e-01,\n", "        -3.2422e-01, -3.5156e-02, -4.5386e-01,  6.2939e-01, -5.5078e-01,\n", "        -4.2773e-01, -2.2644e-01,  2.7222e-01, -4.6533e-01, -2.0126e-02,\n", "        -3.9746e-01, -2.3145e-01,  1.3086e-01,  6.3904e-02,  1.9348e-02,\n", "        -1.3354e-01, -4.1992e-01, -2.4451e-01,  3.7598e-01,  2.9126e-01,\n", "        -1.2915e-01,  5.8936e-01, -1.4319e-01,  2.4585e-01,  5.4901e-02,\n", "         7.6514e-01, -6.9775e-01, -3.4863e-01,  2.5063e-03, -1.9951e-03,\n", "        -1.2064e-03, -1.0048e-02,  4.0855e-03, -2.0599e-03, -1.2062e-02,\n", "        -1.5671e-02,  3.8242e-03,  4.4274e-04,  9.1629e-03,  2.4586e-03,\n", "         3.0479e-03, -9.7466e-04, -4.0207e-03,  3.2578e-03, -1.3054e-02,\n", "         8.4839e-03, -5.5351e-03,  2.3956e-03, -1.4715e-03, -1.6800e-02,\n", "        -8.8043e-03, -3.5648e-03, -1.0796e-02, -1.3474e-02,  8.8577e-03,\n", "        -3.2692e-03,  5.7487e-03,  4.6806e-03,  5.6419e-03,  3.5000e-03,\n", "        -1.8625e-03, -6.3515e-03, -1.5686e-02, -4.2648e-03, -2.1160e-04,\n", "         4.2114e-03, -1.2718e-02,  1.9426e-03,  2.7714e-03,  2.3212e-03,\n", "        -4.6310e-03, -3.2187e-04,  2.2488e-03,  2.8591e-03, -1.2276e-02,\n", "         5.0049e-03, -1.0315e-02,  1.3649e-02, -9.9869e-03, -2.5139e-03,\n", "         1.0101e-02,  2.7905e-03,  7.6437e-04,  6.0310e-03, -1.9348e-02,\n", "        -2.6302e-03,  5.4703e-03, -7.1831e-03,  5.0688e-04, -5.9204e-03,\n", "         3.5429e-04, -5.3101e-03, -3.7994e-03, -2.9049e-03, -9.0942e-03,\n", "        -8.9264e-03, -4.6616e-03,  3.9139e-03,  4.4918e-04,  1.2047e-02,\n", "         9.3231e-03, -6.3181e-04, -8.4763e-03,  1.2894e-02, -6.2561e-03,\n", "         5.0926e-03,  1.1778e-03, -5.1842e-03,  6.2323e-04,  8.7662e-03,\n", "        -4.7874e-03,  3.7594e-03,  3.1300e-03, -3.1071e-03, -5.7259e-03,\n", "        -1.0719e-02,  2.1820e-03, -1.7960e-02,  1.6510e-02, -3.2806e-03,\n", "        -6.6071e-03,  8.4229e-03,  8.3542e-03, -8.3542e-03,  1.0605e-02,\n", "         1.0042e-03, -2.5082e-03,  3.6068e-03, -3.6697e-03,  3.6030e-03,\n", "        -1.6449e-02,  6.6757e-03,  6.0844e-03,  9.7179e-04,  3.9749e-03,\n", "         3.5000e-03, -2.9831e-03,  1.1396e-03,  4.8256e-03, -7.1793e-03,\n", "         1.4811e-03,  1.6057e-04,  1.2421e-02, -6.9737e-05, -3.7670e-03,\n", "         7.5340e-03, -6.2990e-04, -2.1477e-03,  9.8419e-03,  5.8708e-03,\n", "        -7.8278e-03,  2.5501e-03,  3.2902e-03,  8.5413e-05, -5.7907e-03,\n", "        -3.6182e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.query.weight', Parameter containing:\n", "tensor([[-0.0184, -0.0119, -0.0704,  ...,  0.0050,  0.0036,  0.0502],\n", "        [-0.0368, -0.0061,  0.0478,  ...,  0.0438,  0.0277, -0.0020],\n", "        [ 0.0510, -0.0105,  0.0042,  ..., -0.0584, -0.0053,  0.0155],\n", "        ...,\n", "        [ 0.0337, -0.0036, -0.0110,  ...,  0.0952,  0.0275, -0.0385],\n", "        [-0.0384,  0.0077,  0.0314,  ...,  0.0219, -0.0166,  0.0156],\n", "        [ 0.0070,  0.0155,  0.0278,  ..., -0.0252,  0.0273, -0.0040]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.query.bias', Parameter containing:\n", "tensor([ 0.0297,  0.1143, -0.0421,  ...,  0.1499, -0.1050, -0.0172],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.dense.weight', Parameter containing:\n", "tensor([[-0.0260,  0.0357,  0.0482,  ..., -0.0066,  0.0103,  0.0011],\n", "        [ 0.0353,  0.0200,  0.0046,  ...,  0.0186, -0.0035, -0.0221],\n", "        [-0.0042, -0.0058, -0.0218,  ..., -0.0514, -0.0064,  0.0338],\n", "        ...,\n", "        [ 0.0076,  0.0277, -0.0456,  ..., -0.0008,  0.0316,  0.0641],\n", "        [-0.0143,  0.0120, -0.0032,  ...,  0.0285,  0.0605,  0.0814],\n", "        [ 0.0085, -0.0049,  0.0466,  ..., -0.0158,  0.0202, -0.0060]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.attention.dense.bias', Parameter containing:\n", "tensor([-0.0168, -0.0086, -0.0479,  ...,  0.0091, -0.0183, -0.0273],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.7524, 1.0303, 0.9004,  ..., 1.0752, 0.9351, 1.1113], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('5.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0336,  0.0244, -0.0293,  ...,  0.0292, -0.0009,  0.0904],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0694, -0.0289,  0.1323,  ..., -0.0463,  0.0931, -0.0263],\n", "        [ 0.0478,  0.0663,  0.0768,  ..., -0.0317, -0.0460, -0.0028],\n", "        [ 0.0345,  0.0022,  0.0347,  ...,  0.0287,  0.0332,  0.0172],\n", "        ...,\n", "        [ 0.0267, -0.0622,  0.0067,  ..., -0.0592, -0.0271, -0.0173],\n", "        [ 0.0504, -0.1061, -0.0384,  ..., -0.0156, -0.0325,  0.0038],\n", "        [ 0.0627, -0.0214,  0.0389,  ..., -0.0367, -0.0129, -0.0926]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0668, -0.0264, -0.0191,  ..., -0.0633, -0.0529, -0.0506],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 0.0070,  0.0074, -0.0069,  ...,  0.0446, -0.0068,  0.0184],\n", "        [-0.0020,  0.0033, -0.0032,  ...,  0.0218, -0.0026, -0.0016],\n", "        [-0.0157,  0.0409, -0.0080,  ..., -0.0787,  0.0109, -0.0226],\n", "        ...,\n", "        [ 0.0251,  0.0085, -0.0297,  ...,  0.0084, -0.0190, -0.0195],\n", "        [-0.0046, -0.0192, -0.0152,  ...,  0.0333,  0.0143,  0.0168],\n", "        [ 0.0146,  0.0377,  0.0026,  ..., -0.0130, -0.0204, -0.0078]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('5.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0072,  0.0195,  0.0324,  ...,  0.0372, -0.0079,  0.0497],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.input_layernorm.weight', Parameter containing:\n", "tensor([0.7783, 0.9907, 0.9717,  ..., 1.0215, 0.8716, 0.8940], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('6.input_layernorm.bias', Parameter containing:\n", "tensor([-3.8266e-05, -3.9024e-03,  9.2285e-02,  ..., -2.0523e-02,\n", "         5.0751e-02, -4.3335e-02], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('6.attention.key_value.weight', Parameter containing:\n", "tensor([[ 0.0003,  0.0600,  0.0959,  ..., -0.0128, -0.0250, -0.0091],\n", "        [-0.0239,  0.0469,  0.0277,  ...,  0.0272,  0.0368, -0.0567],\n", "        [-0.0577, -0.0184,  0.0277,  ..., -0.0086,  0.0258, -0.0342],\n", "        ...,\n", "        [-0.0239, -0.0463, -0.0037,  ..., -0.0171,  0.0557, -0.0074],\n", "        [-0.0199,  0.0285, -0.0058,  ...,  0.0367,  0.0296,  0.0091],\n", "        [ 0.0096, -0.0158,  0.0312,  ..., -0.0246,  0.0486,  0.0064]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.attention.key_value.bias', Parameter containing:\n", "tensor([ 3.1616e-01, -6.4893e-01, -1.6248e-01, -6.9458e-02,  3.5376e-01,\n", "         2.9541e-01,  2.4573e-01, -3.4131e-01, -3.1323e-01,  3.5083e-01,\n", "         6.3428e-01,  2.3389e-01,  4.5312e-01,  3.9038e-01, -6.8213e-01,\n", "         2.8589e-01, -4.8267e-01, -1.6956e-01,  5.2393e-01, -1.8481e-01,\n", "         1.8738e-02, -5.7959e-01, -1.2197e+00,  6.2317e-02,  4.7095e-01,\n", "         1.0195e+00,  4.3359e-01,  1.1041e-01,  4.9713e-02,  5.2588e-01,\n", "        -1.4819e-01,  2.4878e-01,  8.5059e-01,  9.3457e-01,  1.3879e-01,\n", "        -7.3535e-01,  2.9492e-01,  3.8721e-01,  5.8398e-01,  5.3320e-01,\n", "        -4.6191e-01, -5.8057e-01,  1.2433e-01, -3.0396e-01, -4.7070e-01,\n", "         1.2939e-01,  2.7783e-01,  2.5488e-01, -1.3096e+00, -2.2888e-01,\n", "         4.5435e-01,  2.4200e-02, -9.2188e-01, -3.3600e-02,  5.0842e-02,\n", "         3.9697e-01,  3.7415e-02, -1.0020e+00,  4.7510e-01, -3.3716e-01,\n", "         1.0713e+00,  1.0132e-01,  3.0225e-01, -1.1973e+00,  1.8030e-01,\n", "        -3.1372e-02, -2.2510e-01,  3.9038e-01, -1.6956e-01, -6.1426e-01,\n", "         1.7563e-02,  1.2952e-01, -4.8096e-01, -1.2366e-01, -8.5449e-01,\n", "        -6.4648e-01,  4.6802e-01,  4.3799e-01, -1.0468e-01, -7.0374e-02,\n", "         5.6689e-01,  1.6748e-01, -9.3262e-01,  1.0632e-01,  1.6199e-01,\n", "        -5.5029e-01, -1.7822e-02,  4.2065e-01,  4.3335e-01, -2.9434e-02,\n", "        -3.0859e-01,  1.6309e-01, -2.0447e-01,  1.1292e-01, -3.0731e-02,\n", "         8.0566e-02, -2.6025e-01, -8.6328e-01,  1.7249e-01,  3.7817e-01,\n", "        -9.7510e-01, -4.4098e-02,  7.3096e-01, -4.1431e-01, -7.5035e-03,\n", "         8.8184e-01, -3.9136e-01,  7.2119e-01,  2.3315e-01,  4.8853e-01,\n", "         7.9883e-01, -3.6548e-01,  6.5137e-01, -7.2070e-01, -5.8984e-01,\n", "        -4.2529e-01, -1.2103e-01,  1.9446e-01,  5.0684e-01, -7.5342e-01,\n", "        -3.3984e-01,  3.5083e-01,  1.0370e-01, -8.0994e-02,  1.9214e-01,\n", "        -4.6948e-01, -3.1567e-01,  8.6609e-02, -3.4809e-03, -1.7157e-03,\n", "         9.2506e-04,  5.5504e-03, -6.1913e-03,  8.2550e-03, -8.2855e-03,\n", "        -8.2541e-04, -3.2091e-04, -7.0076e-03, -1.7405e-03,  1.7441e-02,\n", "        -1.0643e-02,  4.8409e-03,  3.5553e-03,  7.1602e-03, -9.2468e-03,\n", "        -5.6953e-03, -9.2545e-03,  8.6746e-03, -1.1139e-02,  7.3624e-03,\n", "         6.6833e-03, -4.5853e-03,  7.2517e-03,  3.2997e-04, -1.1856e-02,\n", "        -1.2947e-02, -3.7670e-03, -2.7733e-03,  1.2482e-02,  2.4452e-03,\n", "        -1.4946e-02, -2.9812e-03,  1.3443e-02, -4.0779e-03,  7.0381e-04,\n", "         7.5378e-03,  2.6150e-03, -1.1467e-02,  8.2092e-03, -8.4534e-03,\n", "        -1.4915e-03, -1.7105e-02,  7.8506e-03, -8.4000e-03,  4.5433e-03,\n", "        -6.3133e-04, -1.0658e-02, -1.2093e-02, -5.0011e-03,  1.5091e-02,\n", "         3.9330e-03,  1.3275e-02, -3.0956e-03,  1.6785e-02, -1.9779e-03,\n", "        -3.6011e-03,  9.7809e-03, -1.0742e-02, -1.0727e-02,  9.1400e-03,\n", "        -1.7815e-03, -6.3372e-04, -1.2466e-02,  8.9722e-03, -3.1395e-03,\n", "        -7.8506e-03,  4.8904e-03, -1.1311e-03,  1.1032e-02,  8.9340e-03,\n", "         4.6806e-03,  4.5776e-03, -2.9984e-03,  1.7185e-03, -5.1384e-03,\n", "         8.2474e-03, -1.8091e-03,  1.5841e-03,  1.2466e-02, -3.0479e-03,\n", "        -1.9547e-02, -1.4954e-03,  7.9346e-03,  2.1782e-03,  3.0041e-03,\n", "         9.2468e-03, -7.4692e-03, -5.4283e-03,  7.6180e-03,  2.7981e-03,\n", "         7.6942e-03,  3.0193e-03, -4.7874e-03, -1.2245e-02, -8.1205e-04,\n", "        -1.9501e-02,  7.3814e-04, -7.6180e-03,  1.1574e-02, -8.3008e-03,\n", "        -1.0681e-03, -2.8248e-03, -4.7386e-05,  1.0933e-02, -4.8409e-03,\n", "         1.1398e-02, -8.0719e-03, -4.4632e-03, -1.9440e-02, -9.8724e-03,\n", "        -5.8136e-03, -7.3433e-03,  8.6365e-03,  6.0387e-03, -1.3962e-03,\n", "         1.0506e-02,  9.5215e-03, -7.9775e-04,  4.9706e-03,  9.8648e-03,\n", "        -7.3814e-03, -1.5745e-03,  3.1528e-03,  5.8022e-03, -1.8940e-03,\n", "        -3.0231e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0378, -0.0159, -0.0110,  ..., -0.0172, -0.0908, -0.0242],\n", "        [ 0.0399,  0.0040, -0.0406,  ...,  0.0159,  0.0126, -0.0067],\n", "        [ 0.0861, -0.0023, -0.0255,  ..., -0.0748,  0.0178,  0.0008],\n", "        ...,\n", "        [-0.0244,  0.0154, -0.0073,  ...,  0.0361, -0.0293, -0.0084],\n", "        [ 0.0626,  0.0466,  0.0219,  ..., -0.0213,  0.0298, -0.0345],\n", "        [-0.0271,  0.0154,  0.0107,  ...,  0.0114,  0.0424, -0.0460]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.attention.query.bias', Parameter containing:\n", "tensor([ 0.0530, -0.0267, -0.0290,  ..., -0.0831,  0.0380, -0.0155],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0178, -0.0061,  0.0045,  ..., -0.0031,  0.0005, -0.0134],\n", "        [ 0.0351,  0.0464, -0.0133,  ..., -0.0640,  0.0384,  0.0077],\n", "        [ 0.0395,  0.0352, -0.0055,  ..., -0.0332,  0.0171,  0.0470],\n", "        ...,\n", "        [-0.0455, -0.0344,  0.0142,  ..., -0.0244,  0.0012,  0.0677],\n", "        [-0.0116,  0.0200, -0.0111,  ...,  0.0028, -0.0285,  0.0407],\n", "        [-0.0374, -0.0255, -0.0517,  ...,  0.0484, -0.0886,  0.0323]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.attention.dense.bias', Parameter containing:\n", "tensor([-0.0156, -0.0041, -0.1133,  ..., -0.0021, -0.0343,  0.0263],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.7886, 1.0508, 0.9194,  ..., 1.0439, 0.8198, 1.0684], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('6.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0284,  0.0474, -0.0482,  ...,  0.0391, -0.0248,  0.0636],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[-0.0531, -0.0797, -0.0132,  ...,  0.0006,  0.0033, -0.0225],\n", "        [-0.0233,  0.0524,  0.0415,  ..., -0.0518,  0.0277, -0.0571],\n", "        [ 0.0531, -0.0329,  0.0011,  ...,  0.0122, -0.0354, -0.0388],\n", "        ...,\n", "        [ 0.0023, -0.0025,  0.0580,  ...,  0.0009, -0.0441, -0.0416],\n", "        [-0.0282, -0.0372,  0.0363,  ...,  0.0121,  0.0403, -0.0150],\n", "        [ 0.0316,  0.0334,  0.0130,  ..., -0.0073,  0.0026,  0.0145]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0672, -0.0507, -0.0413,  ..., -0.0323, -0.0198, -0.0323],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 0.0398,  0.0233, -0.0374,  ..., -0.0129,  0.0213,  0.0398],\n", "        [ 0.0143,  0.0105,  0.0002,  ...,  0.0478,  0.0102,  0.0064],\n", "        [ 0.0233,  0.0329, -0.0047,  ...,  0.0179, -0.0235,  0.0255],\n", "        ...,\n", "        [-0.0264, -0.0157,  0.0294,  ..., -0.0192,  0.0100,  0.0003],\n", "        [ 0.0293,  0.0443,  0.0547,  ..., -0.0201, -0.0188,  0.0094],\n", "        [ 0.0546,  0.0315, -0.0086,  ..., -0.0670,  0.0042, -0.0066]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('6.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0226,  0.0719,  0.0562,  ..., -0.0118, -0.0171,  0.0779],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.input_layernorm.weight', Parameter containing:\n", "tensor([0.8564, 1.0469, 0.9756,  ..., 0.9668, 0.9463, 0.9380], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('7.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0376, -0.0052,  0.1245,  ..., -0.0781,  0.1119, -0.0820],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.attention.key_value.weight', Parameter containing:\n", "tensor([[ 0.0020, -0.0267, -0.0345,  ...,  0.0050, -0.0221,  0.0504],\n", "        [ 0.0619, -0.0182,  0.0013,  ...,  0.0049, -0.0180,  0.0518],\n", "        [ 0.0117,  0.0564, -0.0565,  ...,  0.0359,  0.0104, -0.0076],\n", "        ...,\n", "        [-0.0153, -0.0306,  0.0012,  ...,  0.0481, -0.0250, -0.0141],\n", "        [ 0.0137, -0.0096,  0.0185,  ..., -0.0054,  0.0008, -0.0249],\n", "        [-0.0053,  0.0142, -0.0278,  ...,  0.0446, -0.0253, -0.0410]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.attention.key_value.bias', Parameter containing:\n", "tensor([ 1.3115e+00,  7.4707e-01, -1.2756e-01,  2.1716e-01,  1.2842e+00,\n", "         1.0068e+00,  7.2899e-03, -3.7329e-01, -6.9092e-01, -4.8804e-01,\n", "        -2.6245e-01, -9.4287e-01,  5.1025e-01,  1.9824e+00, -1.8542e-01,\n", "         1.7029e-01, -1.4268e+00, -7.6611e-01,  3.2568e-01,  8.0859e-01,\n", "         1.6055e+00, -7.7637e-01, -7.1924e-01, -6.1035e-01,  2.1698e-02,\n", "         5.5817e-02, -5.9912e-01,  1.6455e+00, -2.3340e-01,  2.0776e-01,\n", "        -1.8125e+00,  2.6978e-01, -1.4136e-01,  1.0947e+00, -1.1102e-01,\n", "        -8.9746e-01, -8.7109e-01,  1.2383e+00, -8.8037e-01, -9.5312e-01,\n", "         4.2578e-01, -1.1992e+00, -7.4756e-01, -9.9219e-01, -2.0776e-01,\n", "         1.0791e+00,  7.9883e-01, -4.5380e-02, -5.0293e-01,  1.1406e+00,\n", "        -1.5930e-01,  3.7720e-01, -2.2449e-01,  3.3398e-01,  5.7227e-01,\n", "         1.3145e+00,  2.6440e-01,  6.9434e-01, -2.1899e-01, -1.2090e+00,\n", "        -8.4619e-01, -2.0938e+00,  3.4253e-01, -1.6250e+00, -7.9980e-01,\n", "         2.4268e-01,  9.9805e-01,  9.1309e-01,  9.3652e-01,  7.7197e-01,\n", "         1.0859e+00,  4.0649e-01, -1.4053e+00,  9.9854e-01, -8.7354e-01,\n", "         1.2275e+00,  2.4453e+00, -1.6602e+00, -9.5581e-02, -1.8574e+00,\n", "        -3.4497e-01, -2.3047e+00, -7.7832e-01, -2.0593e-01,  5.1807e-01,\n", "         1.1151e-01, -2.4280e-01,  7.4658e-01, -2.1367e+00, -1.0460e-02,\n", "         4.1602e-01, -5.2734e-01, -5.2681e-03, -5.3271e-01, -8.7646e-01,\n", "         1.8982e-01,  8.2422e-01,  3.8574e-01, -1.1011e-01,  1.0095e-01,\n", "        -1.3223e+00, -8.6621e-01, -8.1299e-01, -1.1787e+00,  1.8994e+00,\n", "         1.4502e-01,  7.1338e-01,  2.4426e-01,  9.2676e-01,  1.8096e+00,\n", "         1.5957e+00,  1.5449e+00, -9.3933e-02,  1.3008e+00, -1.0576e+00,\n", "        -6.6846e-01, -1.2227e+00,  8.8916e-01, -3.6377e-01, -3.6841e-01,\n", "         2.5352e+00,  1.1890e-01, -1.7480e+00, -9.2139e-01, -4.4189e-01,\n", "        -4.9292e-01,  2.6934e+00,  9.5801e-01, -1.8265e-02,  1.5465e-02,\n", "         1.0101e-02, -8.3084e-03,  6.9923e-03, -1.1574e-02, -1.4221e-02,\n", "         9.1782e-03,  6.5155e-03,  8.2550e-03, -6.9580e-03,  2.2564e-03,\n", "        -8.6517e-03,  4.0531e-04, -2.4366e-04, -1.0674e-02, -2.3621e-02,\n", "        -1.2329e-02,  7.7515e-03, -6.9466e-03, -1.1955e-02, -1.7204e-03,\n", "        -6.7825e-03, -5.0697e-03, -1.4105e-03, -2.6035e-03, -1.3092e-02,\n", "         8.0566e-03,  1.6571e-02, -1.8005e-02, -4.7951e-03,  1.5438e-04,\n", "        -8.5068e-03,  3.1982e-02,  1.1627e-02, -4.3335e-03,  1.5511e-02,\n", "         6.6566e-03, -5.6725e-03,  4.1962e-03,  1.2980e-03,  1.8921e-03,\n", "         1.5305e-02, -8.0109e-03,  7.2937e-03,  1.2650e-02, -1.2947e-02,\n", "        -1.7614e-03, -1.6766e-03,  7.9651e-03,  1.7609e-02, -1.7853e-02,\n", "         1.3151e-03, -3.2253e-03,  4.9438e-03,  1.8784e-02, -2.6566e-02,\n", "         1.9913e-02,  1.4687e-02,  1.8425e-03,  1.4257e-03, -8.4152e-03,\n", "         1.3054e-02, -7.1716e-03, -7.6294e-03,  2.8931e-02,  1.9028e-02,\n", "        -9.1858e-03,  4.6196e-03, -1.1116e-02, -7.5645e-03,  2.0432e-02,\n", "         6.8893e-03, -1.2917e-02, -1.8415e-03, -1.4031e-04,  7.5645e-03,\n", "        -1.7242e-02,  9.7733e-03,  1.7426e-02, -1.4748e-02, -1.7700e-02,\n", "         1.6418e-02,  9.6817e-03, -3.8910e-03,  3.2101e-03, -9.1400e-03,\n", "        -8.7967e-03, -6.3248e-03,  1.4168e-02, -3.4698e-02, -4.5700e-03,\n", "         1.9012e-02, -1.7061e-03,  1.2884e-03, -1.9379e-02, -1.4801e-03,\n", "         1.3573e-02,  2.2751e-02,  2.3937e-03, -1.1169e-02,  3.3092e-03,\n", "         9.7351e-03,  1.5388e-02, -1.1467e-02, -7.0610e-03, -8.1406e-03,\n", "        -6.4011e-03, -1.2100e-02,  4.6349e-03,  3.3798e-03, -3.8147e-03,\n", "         5.2414e-03,  2.2471e-05, -1.1375e-02, -1.0323e-02, -1.6647e-02,\n", "        -2.1835e-02,  3.2043e-03,  2.2526e-03,  1.2733e-02,  1.3390e-02,\n", "        -1.0056e-02,  3.7403e-03, -3.3398e-03,  1.5839e-02,  2.3071e-02,\n", "        -1.1620e-02], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0013,  0.0132,  0.0244,  ...,  0.0099,  0.0534,  0.0014],\n", "        [ 0.0083,  0.0063, -0.0387,  ..., -0.0123,  0.0488,  0.0215],\n", "        [ 0.0383, -0.0504, -0.0323,  ...,  0.0296, -0.0168, -0.0125],\n", "        ...,\n", "        [-0.0139, -0.0119,  0.0164,  ...,  0.0132, -0.0066, -0.0159],\n", "        [-0.0301,  0.0666,  0.0011,  ..., -0.0187,  0.0112,  0.0659],\n", "        [-0.0158, -0.0496,  0.0213,  ...,  0.0209, -0.0033,  0.0455]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.attention.query.bias', Parameter containing:\n", "tensor([-0.0030, -0.0370,  0.0397,  ..., -0.0356,  0.0674,  0.0030],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.attention.dense.weight', Parameter containing:\n", "tensor([[-3.5019e-03, -5.0621e-03, -2.6749e-02,  ..., -3.4271e-02,\n", "          2.0561e-03,  4.4159e-02],\n", "        [ 3.4370e-03,  1.8234e-02, -3.5248e-02,  ...,  5.2155e-02,\n", "         -1.1435e-03, -6.1127e-02],\n", "        [ 1.0757e-03,  7.1411e-02,  2.4139e-02,  ..., -4.5776e-02,\n", "          6.5994e-04, -1.3471e-05],\n", "        ...,\n", "        [ 8.3084e-03, -3.1769e-02, -2.7573e-02,  ..., -4.1351e-02,\n", "         -1.6449e-02, -7.0190e-02],\n", "        [-4.1695e-03, -3.5187e-02,  1.2527e-02,  ...,  4.4060e-03,\n", "          2.6047e-02, -2.7618e-02],\n", "        [-1.7223e-03,  1.1444e-02,  3.2024e-03,  ...,  7.0618e-02,\n", "          4.1016e-02,  6.8359e-02]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('7.attention.dense.bias', Parameter containing:\n", "tensor([-0.0400, -0.0086, -0.0784,  ..., -0.0269, -0.0974, -0.0410],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.8608, 0.9268, 0.8472,  ..., 0.9185, 0.8252, 0.9570], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('7.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0287,  0.0482,  0.0491,  ...,  0.0522, -0.0198,  0.0644],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0598,  0.0265, -0.0199,  ..., -0.0427,  0.0152, -0.0279],\n", "        [ 0.0412, -0.0134, -0.0115,  ...,  0.0156,  0.0063, -0.0412],\n", "        [ 0.0440, -0.0414,  0.0125,  ...,  0.0052,  0.0732,  0.0604],\n", "        ...,\n", "        [-0.0468, -0.0068,  0.0007,  ...,  0.0399, -0.0639,  0.0241],\n", "        [ 0.0159,  0.0286, -0.0158,  ...,  0.0182,  0.0561, -0.0048],\n", "        [-0.0200,  0.0383, -0.0334,  ...,  0.0039,  0.0337, -0.0492]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0395, -0.0372, -0.0432,  ...,  0.0488, -0.0676, -0.0648],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 0.0125,  0.0069, -0.0449,  ...,  0.0403, -0.0177, -0.0192],\n", "        [-0.0007, -0.0036,  0.0214,  ...,  0.0082,  0.0111,  0.0217],\n", "        [-0.0739, -0.0041, -0.0027,  ..., -0.0141,  0.0020, -0.0345],\n", "        ...,\n", "        [ 0.0028,  0.0411,  0.0169,  ..., -0.0427, -0.0136,  0.0017],\n", "        [ 0.0176, -0.0184,  0.0019,  ...,  0.0312, -0.0079, -0.0816],\n", "        [ 0.0021, -0.0289, -0.0114,  ..., -0.0182,  0.0754, -0.0196]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('7.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0970,  0.0458,  0.0420,  ...,  0.0156, -0.0394,  0.1025],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.input_layernorm.weight', Parameter containing:\n", "tensor([1.1055, 1.1406, 1.0947,  ..., 1.0928, 1.1123, 1.1562], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('8.input_layernorm.bias', Parameter containing:\n", "tensor([-0.0528,  0.0243,  0.0229,  ...,  0.0188, -0.0283, -0.0079],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0126,  0.0197,  0.1097,  ...,  0.0453,  0.0544,  0.0175],\n", "        [-0.0139,  0.0020, -0.0216,  ...,  0.0110, -0.0419, -0.0246],\n", "        [-0.0200,  0.0556,  0.0117,  ...,  0.0468,  0.1072, -0.0035],\n", "        ...,\n", "        [ 0.0361,  0.0041,  0.0181,  ...,  0.0027, -0.0182, -0.0423],\n", "        [-0.0106, -0.0191,  0.0131,  ...,  0.0258,  0.0064, -0.0077],\n", "        [ 0.0318,  0.0206,  0.0066,  ...,  0.0351,  0.0119,  0.0111]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.key_value.bias', Parameter containing:\n", "tensor([-1.0022e-01,  4.9707e-01,  2.4805e-01,  7.0343e-03,  7.4219e-02,\n", "        -5.2856e-02, -1.9153e-01,  2.6880e-01,  2.9190e-02, -4.0985e-02,\n", "         2.9761e-01,  7.5537e-01, -2.7710e-01, -2.8052e-01, -2.5684e-01,\n", "         1.9360e-01, -2.1777e-01,  2.5684e-01,  3.0518e-01, -2.9614e-01,\n", "         1.2920e+00,  2.1191e-01,  2.8564e-01,  7.0068e-02, -6.8604e-02,\n", "        -3.7354e-02,  1.0059e+00,  1.9119e-02, -4.2450e-02,  4.1779e-02,\n", "         7.8918e-02,  1.6516e-01, -4.0497e-02,  6.1621e-01, -9.4482e-02,\n", "        -3.0713e-01, -8.8806e-02, -8.2764e-02,  4.8413e-01,  1.0620e-01,\n", "        -2.0166e-01, -7.0996e-01,  3.7231e-01, -4.7290e-01, -1.7188e-01,\n", "         1.6248e-01, -4.6240e-01,  3.6987e-01,  1.9788e-01, -2.0984e-01,\n", "         1.3037e+00, -4.6448e-02,  8.4619e-01, -3.0197e-02, -4.9707e-01,\n", "        -3.7134e-01, -7.6025e-01, -9.6619e-02,  1.8213e-01, -2.1423e-01,\n", "         2.3901e-01,  1.3105e+00,  5.0751e-02, -6.9885e-02,  5.3760e-01,\n", "         5.9814e-02,  1.6260e-01,  1.8762e-01,  1.2659e-01, -3.0322e-01,\n", "        -2.7939e-02,  7.2571e-02,  3.2104e-02, -2.6147e-01, -7.9163e-02,\n", "        -6.5796e-02, -3.0322e-01,  1.0797e-01, -1.1191e+00, -3.2568e-01,\n", "         1.4658e+00, -1.2441e+00,  1.9397e-01, -1.2891e-01, -1.1536e-01,\n", "        -8.2336e-02, -3.8770e-01,  3.5107e-01, -2.6611e-01,  1.6992e-01,\n", "         3.7402e-01,  2.9590e-01,  1.6113e-01, -1.5210e-01,  2.5488e-01,\n", "        -1.2415e-01, -5.6982e-01, -1.3513e-01,  4.3066e-01, -8.6121e-02,\n", "         6.3667e-03, -1.1798e-01, -2.9712e-01, -5.6274e-02,  4.5068e-01,\n", "        -8.9502e-01,  6.3086e-01,  4.3774e-01, -2.3767e-01, -2.8198e-01,\n", "        -4.7729e-02, -1.7749e-01, -8.5449e-02,  1.3565e-02, -7.4121e-01,\n", "         1.5039e-01,  9.2920e-01, -2.1667e-01,  1.8909e-01,  4.2554e-01,\n", "         1.4307e+00,  1.7395e-01,  1.4673e-01,  4.2358e-01,  1.2573e-01,\n", "         3.8306e-01,  3.7866e-01,  4.4482e-01, -1.5488e-02,  8.2626e-03,\n", "        -5.3711e-03, -3.0537e-03,  1.0157e-03,  1.4465e-02,  2.7332e-03,\n", "        -9.4681e-03, -7.0992e-03, -1.0994e-02,  6.5880e-03, -1.8606e-03,\n", "        -8.8787e-04, -1.4830e-03,  1.4488e-02, -1.2314e-02, -4.2038e-03,\n", "         3.1128e-03,  1.0271e-03,  5.4207e-03,  1.1734e-02, -8.0872e-03,\n", "         7.2479e-03, -7.8812e-03,  1.5625e-02, -2.4757e-03,  1.1551e-02,\n", "        -5.8670e-03,  5.8517e-03, -1.5478e-03,  2.2793e-03,  2.1301e-02,\n", "        -5.6725e-03, -1.5182e-02,  6.7749e-03,  2.3880e-03,  5.4245e-03,\n", "         8.0049e-05, -1.1426e-04, -2.4586e-03, -7.0534e-03, -1.3037e-03,\n", "        -3.1013e-03, -7.7972e-03, -1.0201e-02,  1.7672e-03, -4.8790e-03,\n", "        -7.6818e-04, -9.2926e-03, -1.4591e-03,  5.7259e-03,  8.9340e-03,\n", "        -7.1640e-03,  9.8267e-03, -1.0033e-02, -5.6381e-03,  8.9264e-04,\n", "         1.4465e-02,  1.3680e-02, -1.6785e-02,  1.1749e-02,  2.8229e-02,\n", "        -7.5150e-03, -9.1248e-03,  4.2496e-03, -2.2919e-02,  2.9793e-03,\n", "         8.7204e-03, -8.3466e-03, -2.7294e-03,  1.6165e-03, -1.2787e-02,\n", "        -1.1055e-02, -2.7046e-03, -1.5688e-03,  2.3479e-03,  2.7008e-03,\n", "         1.0933e-02, -1.1978e-02,  3.7265e-04, -5.1842e-03, -3.6964e-03,\n", "        -1.4076e-02, -7.0343e-03,  4.3030e-03,  1.0544e-02,  7.7209e-03,\n", "         7.3910e-04,  1.4099e-02, -1.7071e-03, -1.0605e-02,  4.5738e-03,\n", "         7.2594e-03,  1.4626e-02, -1.2604e-02, -1.4328e-02, -5.2643e-03,\n", "        -1.5732e-02, -2.8198e-02,  1.4961e-02, -3.0785e-03,  7.9041e-03,\n", "         9.2926e-03, -5.8746e-03, -2.1324e-03, -3.2101e-03,  1.1345e-02,\n", "         2.8133e-03,  1.0391e-02, -3.9864e-03,  5.9843e-05, -4.2343e-03,\n", "        -3.6240e-03, -5.0087e-03, -2.0733e-03, -4.8409e-03, -2.3056e-02,\n", "        -1.0086e-02, -1.3206e-02, -1.2001e-02, -1.6052e-02, -7.9117e-03,\n", "         1.2856e-02,  1.1345e-02, -1.0252e-03, -1.0452e-02,  3.1204e-03,\n", "        -1.0376e-02], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0149,  0.0634,  0.0223,  ..., -0.0618,  0.0257, -0.0393],\n", "        [-0.0002, -0.0380,  0.0389,  ..., -0.0197,  0.0109, -0.0604],\n", "        [ 0.0140,  0.0007,  0.0334,  ...,  0.0119, -0.0211,  0.0528],\n", "        ...,\n", "        [-0.0130,  0.0215, -0.0069,  ..., -0.0829, -0.0459, -0.0054],\n", "        [ 0.0118, -0.0099,  0.0151,  ...,  0.0001,  0.0094, -0.0216],\n", "        [ 0.0509,  0.0290, -0.0405,  ...,  0.0399,  0.0090,  0.0057]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.query.bias', Parameter containing:\n", "tensor([-0.0036,  0.0142,  0.0076,  ...,  0.0306, -0.0101,  0.0575],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.dense.weight', Parameter containing:\n", "tensor([[-0.0315,  0.0748,  0.0491,  ..., -0.0608,  0.0024,  0.0100],\n", "        [-0.0856,  0.0020, -0.0069,  ...,  0.0346,  0.0141,  0.0462],\n", "        [ 0.0898, -0.0507,  0.0161,  ..., -0.0385, -0.0160,  0.0281],\n", "        ...,\n", "        [-0.0125, -0.0222, -0.0541,  ..., -0.0020,  0.0123, -0.0300],\n", "        [ 0.0696,  0.0039,  0.0340,  ...,  0.0775, -0.0325, -0.0310],\n", "        [ 0.0431, -0.0454, -0.0151,  ...,  0.0018, -0.0232, -0.0399]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.attention.dense.bias', Parameter containing:\n", "tensor([-0.0382, -0.0707, -0.0624,  ..., -0.0013, -0.0957, -0.0545],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.post_attention_layernorm.weight', Parameter containing:\n", "tensor([1.0010, 0.9668, 0.9644,  ..., 1.0234, 0.8652, 1.0576], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('8.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0748,  0.0375, -0.0324,  ...,  0.0762, -0.0420,  0.1193],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0381, -0.0196,  0.0142,  ...,  0.0230,  0.0490, -0.0153],\n", "        [ 0.0132,  0.0299, -0.0167,  ..., -0.0873, -0.0160,  0.0429],\n", "        [ 0.0906, -0.0744,  0.0082,  ..., -0.0167,  0.0491, -0.0438],\n", "        ...,\n", "        [-0.0716, -0.0175,  0.0628,  ..., -0.0291,  0.0714,  0.0294],\n", "        [ 0.0106, -0.0228,  0.0607,  ..., -0.0298, -0.0168,  0.0778],\n", "        [-0.1149, -0.0070, -0.0155,  ..., -0.0620, -0.0163,  0.0179]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0260, -0.0243, -0.0704,  ..., -0.0626, -0.0690, -0.0875],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('8.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-7.3486e-02, -2.2415e-02,  4.5013e-02,  ...,  2.2278e-02,\n", "         -1.8753e-02, -6.6406e-02],\n", "        [ 1.4648e-03, -2.6722e-03, -9.4070e-03,  ..., -6.5079e-03,\n", "         -8.5266e-02, -3.0041e-05],\n", "        [-3.1071e-03,  2.0752e-02,  1.1676e-01,  ...,  5.2979e-02,\n", "          4.7333e-02, -2.0828e-03],\n", "        ...,\n", "        [-3.8177e-02,  2.4475e-02,  5.3558e-03,  ..., -2.4384e-02,\n", "          2.3224e-02, -9.5093e-02],\n", "        [-2.0447e-02, -5.3978e-03, -1.1963e-02,  ..., -2.3727e-02,\n", "         -4.6265e-02, -3.5156e-02],\n", "        [-4.3488e-03,  1.3046e-02,  7.3669e-02,  ...,  7.6721e-02,\n", "          4.3091e-02,  1.6296e-02]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('8.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0773,  0.0325,  0.0010,  ..., -0.0003, -0.0726,  0.1221],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.input_layernorm.weight', Parameter containing:\n", "tensor([1.0703, 1.0127, 0.9922,  ..., 1.0254, 0.9546, 1.0547], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('9.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0294, -0.0118,  0.0988,  ..., -0.0687,  0.0639, -0.0558],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0085,  0.0086, -0.0674,  ..., -0.0240,  0.0303, -0.0403],\n", "        [-0.0448, -0.0177,  0.0311,  ..., -0.0071, -0.0317, -0.0211],\n", "        [-0.0297, -0.0067, -0.0097,  ..., -0.0088, -0.0030,  0.0218],\n", "        ...,\n", "        [ 0.0144,  0.0296, -0.0084,  ...,  0.0210, -0.0061, -0.0373],\n", "        [ 0.0138,  0.0850, -0.0465,  ...,  0.0223,  0.0283,  0.0160],\n", "        [ 0.0645,  0.0025, -0.0519,  ..., -0.0328,  0.0070, -0.0617]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.key_value.bias', Parameter containing:\n", "tensor([-3.6865e-02,  2.8467e-01,  8.5498e-01, -4.4531e-01, -9.0723e-01,\n", "         9.4849e-02, -1.4746e+00,  6.1981e-02,  3.2300e-01, -4.2383e-01,\n", "        -4.6118e-01,  6.8665e-02,  3.3228e-01,  3.5205e-01,  8.0518e-01,\n", "        -3.7451e-01, -3.5547e-01,  4.2603e-01,  9.2010e-03, -7.1960e-02,\n", "        -3.3789e-01, -2.0996e-01,  3.4839e-01,  1.0527e+00, -1.6150e-01,\n", "        -7.1143e-01, -6.9385e-01,  4.8584e-02,  4.5728e-01, -8.2275e-01,\n", "        -8.6792e-02,  7.3926e-01, -7.1680e-01,  2.5659e-01,  3.2202e-01,\n", "        -3.8483e-02,  2.6562e-01, -1.3354e-01, -2.0972e-01, -2.0911e-01,\n", "        -4.3652e-01,  1.1533e+00, -6.1230e-01,  6.6467e-02,  3.8965e-01,\n", "         1.0272e-01, -5.2643e-02, -6.2744e-01,  1.7517e-01, -1.7883e-01,\n", "         1.9421e-01, -5.0635e-01,  5.6934e-01, -4.5166e-01,  5.2490e-01,\n", "        -4.2090e-01,  4.0601e-01,  5.2051e-01,  2.7075e-01, -3.3496e-01,\n", "        -5.9473e-01, -8.8525e-01,  6.6260e-01,  5.5908e-01,  4.9585e-01,\n", "         5.6055e-01, -5.7666e-01,  4.6216e-01, -4.6143e-01,  7.1338e-01,\n", "         1.6919e-01, -2.1643e-01,  9.3604e-01, -7.7051e-01, -1.8823e-01,\n", "        -6.1377e-01,  7.9150e-01,  4.4263e-01, -1.6357e-01,  8.7695e-01,\n", "        -2.4060e-01,  1.5857e-01, -6.1493e-02,  1.7285e-01, -2.1570e-01,\n", "         5.1758e-01,  2.0312e-01,  2.5317e-01,  3.5352e-01,  4.8755e-01,\n", "        -2.7466e-02,  4.6661e-02,  1.3496e+00, -1.9067e-01, -1.3440e-01,\n", "        -6.3281e-01, -2.1057e-01, -8.5205e-01,  1.9714e-01, -7.2266e-01,\n", "        -5.5811e-01, -2.7954e-01,  6.6406e-01,  1.2482e-01, -2.7661e-01,\n", "         1.4343e-01,  3.3154e-01,  4.4849e-01, -6.9336e-01,  6.1035e-01,\n", "        -7.9803e-03, -6.9727e-01, -9.7656e-03, -1.3611e-01, -9.3689e-02,\n", "        -4.4116e-01, -2.7222e-01,  5.4932e-01,  1.6797e-01, -1.9287e-01,\n", "         2.9953e-02,  2.4316e-01,  7.1350e-02, -6.0010e-01,  6.1035e-01,\n", "        -1.3867e-01,  3.7012e-01, -3.2593e-01, -1.6785e-03,  6.0806e-03,\n", "         1.3939e-02,  7.5111e-03,  6.5002e-03, -1.0056e-02,  1.1528e-02,\n", "         7.4806e-03,  4.0221e-04,  4.8828e-03,  1.7366e-03,  9.0361e-05,\n", "        -5.1842e-03,  1.2283e-03, -1.4029e-03, -9.4299e-03,  7.0534e-03,\n", "        -9.4757e-03, -2.0813e-02,  1.9089e-02, -1.8036e-02, -1.0391e-02,\n", "        -1.0162e-02,  2.9325e-04, -6.5346e-03,  9.8190e-03,  2.2640e-03,\n", "         1.8764e-04,  2.1164e-02,  2.0905e-02,  2.5558e-04,  1.0742e-02,\n", "         7.7629e-03, -1.2465e-03,  7.2002e-04, -5.5809e-03, -1.7941e-04,\n", "         1.6861e-03,  2.6642e-02,  4.7531e-03, -6.3276e-04, -1.7042e-03,\n", "        -1.2493e-03,  1.7500e-03,  3.7975e-03,  7.3433e-03,  2.3003e-03,\n", "        -2.3666e-02, -1.5841e-03,  5.9986e-04, -7.2784e-03, -5.7411e-03,\n", "         4.2081e-04,  2.2316e-03,  7.7477e-03, -1.1078e-02, -1.0338e-02,\n", "        -9.7580e-03,  8.9264e-03, -1.2032e-02, -1.7487e-02,  1.3939e-02,\n", "        -5.2605e-03,  2.3117e-03,  8.9188e-03, -4.9133e-03, -6.1607e-03,\n", "        -1.2455e-03, -4.8904e-03,  1.2047e-02, -1.8097e-02, -3.1242e-03,\n", "         7.7915e-04, -2.5299e-02,  1.3008e-02, -1.3687e-02,  5.8784e-03,\n", "         8.5449e-03, -3.7632e-03, -2.7599e-03,  1.3514e-03,  1.9440e-02,\n", "         1.3908e-02,  9.3460e-03, -1.4687e-02,  1.1307e-02,  1.0078e-02,\n", "         7.4959e-03,  7.9956e-03,  1.3077e-02,  3.3379e-03,  6.2943e-03,\n", "        -1.4900e-02, -5.2881e-04, -1.8520e-03, -3.4294e-03,  1.4900e-02,\n", "        -1.1749e-02, -4.8828e-03,  1.1055e-02, -4.7569e-03,  1.7242e-02,\n", "        -9.3918e-03, -1.4854e-02,  1.6464e-02, -5.4779e-03,  6.0997e-03,\n", "         1.0376e-02, -2.8062e-04,  2.4967e-03,  7.4959e-03,  3.7937e-03,\n", "         2.4002e-02, -1.9745e-02,  7.7057e-03,  2.3331e-02,  7.7019e-03,\n", "        -3.6640e-03, -1.4296e-03,  2.3308e-03, -1.1864e-02,  5.6152e-03,\n", "         4.1275e-03, -3.5458e-03, -5.9090e-03,  2.9392e-03, -7.0229e-03,\n", "        -1.7414e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0021, -0.0284, -0.0122,  ..., -0.0050,  0.0094,  0.0210],\n", "        [-0.0033, -0.0116, -0.0087,  ...,  0.0605, -0.0116, -0.0268],\n", "        [ 0.0210, -0.0054, -0.0271,  ..., -0.0055,  0.0144,  0.0433],\n", "        ...,\n", "        [-0.0120, -0.0634, -0.0134,  ..., -0.0203,  0.0314,  0.0152],\n", "        [ 0.0082, -0.0531,  0.0174,  ..., -0.0038,  0.0044, -0.0349],\n", "        [-0.0116,  0.0044, -0.0078,  ..., -0.0577, -0.0169,  0.0131]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.query.bias', Parameter containing:\n", "tensor([-0.0362,  0.0318,  0.1104,  ..., -0.0395, -0.0195,  0.0121],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0237,  0.0569, -0.0065,  ..., -0.0235,  0.0682,  0.0091],\n", "        [-0.0687, -0.0621, -0.0076,  ...,  0.0530, -0.0046, -0.0028],\n", "        [ 0.0302, -0.0325,  0.0471,  ..., -0.0287, -0.0212,  0.0380],\n", "        ...,\n", "        [-0.0093, -0.0503,  0.0613,  ...,  0.0002,  0.0190,  0.0037],\n", "        [-0.0359,  0.0501,  0.0334,  ...,  0.0166,  0.0604, -0.0036],\n", "        [ 0.0025,  0.0041, -0.0298,  ..., -0.0137,  0.0089, -0.0701]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.attention.dense.bias', Parameter containing:\n", "tensor([-0.0118, -0.0297, -0.0944,  ...,  0.0134, -0.0532,  0.0246],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.post_attention_layernorm.weight', Parameter containing:\n", "tensor([1.0518, 0.9556, 0.9453,  ..., 1.0049, 0.9033, 1.0908], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('9.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.1335,  0.1047, -0.0518,  ...,  0.1196, -0.1500,  0.1978],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 1.8127e-02, -1.1456e-01,  2.6993e-02,  ..., -2.7725e-02,\n", "          3.6774e-02, -2.4292e-02],\n", "        [ 8.4534e-02, -2.6062e-02,  2.7954e-02,  ...,  5.9738e-03,\n", "         -1.1253e-02, -8.4305e-03],\n", "        [-2.6001e-02, -3.7292e-02, -2.6199e-02,  ...,  3.4821e-02,\n", "         -3.4542e-03,  3.2463e-03],\n", "        ...,\n", "        [ 3.9983e-04, -1.8203e-04,  1.6800e-02,  ..., -3.1233e-05,\n", "         -2.7332e-03, -6.7749e-02],\n", "        [-2.3819e-02,  1.4679e-02,  3.9764e-02,  ...,  2.3590e-02,\n", "         -1.7014e-02, -5.8350e-02],\n", "        [ 6.4514e-02, -8.2397e-02,  3.4393e-02,  ...,  2.3590e-02,\n", "         -1.0162e-02, -1.9501e-02]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('9.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0753, -0.0504, -0.0050,  ..., -0.0210, -0.0485, -0.0409],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('9.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 5.5027e-04, -4.7150e-02, -2.5513e-02,  ...,  3.7506e-02,\n", "          1.2016e-02, -3.0121e-02],\n", "        [-4.5807e-02, -1.6846e-02, -1.4275e-02,  ...,  1.2219e-04,\n", "         -9.5546e-05, -3.2715e-02],\n", "        [ 1.2093e-02,  1.0925e-02, -7.1335e-03,  ..., -4.6448e-02,\n", "         -2.3148e-02, -3.3752e-02],\n", "        ...,\n", "        [-1.8370e-04,  6.2012e-02, -3.8086e-02,  ..., -6.1218e-02,\n", "          7.6599e-02, -1.1864e-02],\n", "        [ 5.7251e-02,  8.8806e-02,  3.1242e-03,  ..., -1.3954e-02,\n", "          4.0955e-02, -1.7899e-02],\n", "        [-6.4331e-02,  3.8330e-02,  5.3558e-03,  ...,  6.9695e-03,\n", "          5.5542e-02, -1.4862e-02]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('9.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0552,  0.0573, -0.0183,  ...,  0.0485, -0.0522,  0.0887],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.input_layernorm.weight', Parameter containing:\n", "tensor([1.0146, 1.0459, 0.9844,  ..., 1.0088, 0.9390, 1.0107], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('10.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0249, -0.0318,  0.0726,  ..., -0.0370,  0.0536, -0.0306],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0422, -0.0240,  0.0067,  ..., -0.0161, -0.0007, -0.0064],\n", "        [-0.0243,  0.0080,  0.0759,  ...,  0.0068, -0.0082, -0.0175],\n", "        [ 0.0145,  0.0222, -0.0167,  ..., -0.0252, -0.0525,  0.0148],\n", "        ...,\n", "        [-0.0187,  0.0400, -0.0568,  ..., -0.0208,  0.0124, -0.0164],\n", "        [ 0.0024,  0.0286, -0.0266,  ...,  0.0285, -0.0200, -0.0416],\n", "        [ 0.0392, -0.0173,  0.0147,  ..., -0.0166,  0.0244,  0.0565]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.key_value.bias', Parameter containing:\n", "tensor([-4.9133e-02, -9.2285e-01, -2.3132e-01, -2.3788e-02, -1.4453e-01,\n", "        -1.6235e-01,  1.1006e+00,  2.6733e-01,  7.4756e-01, -1.3290e-02,\n", "        -5.3467e-01, -4.7705e-01, -5.8350e-01, -4.3335e-01,  2.9688e-01,\n", "         3.0029e-01, -3.9453e-01, -4.8779e-01, -3.7207e-01,  4.0112e-01,\n", "        -1.2000e-01, -5.0342e-01, -8.6182e-01, -2.0483e-01, -7.1533e-02,\n", "         8.0273e-01, -5.1611e-01, -1.7334e-01,  5.4834e-01,  1.0156e+00,\n", "        -1.2031e+00,  1.6724e-01, -3.6377e-01,  1.9470e-02, -7.7734e-01,\n", "         3.1934e-01,  6.3660e-02, -1.4359e-02, -1.5556e-02, -6.4990e-01,\n", "        -4.6417e-02,  1.1163e-01,  8.6328e-01, -6.1719e-01, -8.5449e-01,\n", "         3.4399e-01, -3.5376e-01, -1.3110e-01, -8.7158e-02, -2.8296e-01,\n", "         8.3557e-02, -5.7520e-01,  1.0244e+00, -3.4619e-01,  3.2684e-02,\n", "        -5.4199e-01,  6.8298e-02,  4.0088e-01, -1.0439e+00,  3.9551e-02,\n", "         4.1260e-01, -1.0565e-01, -2.3572e-01, -7.9834e-01,  1.2866e-01,\n", "        -1.8066e-01,  3.9819e-01,  3.4131e-01, -2.1622e-02,  9.0771e-01,\n", "         5.1025e-01,  1.5894e-01, -1.6739e-02,  5.6854e-02,  3.3276e-01,\n", "         2.6709e-01, -3.3008e-01,  1.6518e-03,  1.4307e-01,  9.6045e-01,\n", "        -1.2412e+00,  8.7598e-01, -7.8186e-02,  4.6509e-02,  1.4490e-01,\n", "         4.9390e-01,  4.1895e-01, -4.1870e-01,  2.3267e-01, -3.7427e-01,\n", "         7.9803e-03,  6.1133e-01,  9.0039e-01,  2.6953e-01,  1.0760e-01,\n", "         2.1460e-01, -6.0547e-01,  7.4524e-02,  2.9639e-01, -1.4661e-01,\n", "         7.9163e-02, -5.4779e-02,  1.1755e-01, -2.8725e-03, -1.1670e+00,\n", "        -4.6240e-01,  1.1450e-01, -8.9746e-01, -5.9570e-01, -4.2505e-01,\n", "        -5.1422e-02,  6.4307e-01, -3.3276e-01,  9.2041e-01, -6.9824e-02,\n", "        -2.6758e-01, -2.5269e-01, -3.3600e-02,  4.5815e-03, -3.7891e-01,\n", "        -5.6592e-01, -2.1973e-01,  1.5747e-01, -1.1016e+00,  7.3486e-01,\n", "         6.9043e-01, -2.4890e-01, -1.4610e-02,  4.9782e-03, -3.6163e-03,\n", "         7.8888e-03, -3.2616e-04, -4.8752e-03, -5.6877e-03,  1.5678e-03,\n", "        -2.0935e-02,  4.2343e-03, -5.3253e-03, -7.4530e-04,  9.3460e-03,\n", "         3.5877e-03,  1.5039e-03, -9.8801e-04,  8.3694e-03,  6.8808e-04,\n", "         5.8365e-04, -8.5526e-03, -4.8518e-04,  4.4556e-03,  3.2253e-03,\n", "         3.5439e-03,  3.6354e-03,  1.5793e-02, -1.3176e-02, -1.1482e-03,\n", "         1.7853e-03,  7.1602e-03,  3.1967e-03, -9.8724e-03,  2.9373e-03,\n", "        -1.6602e-02,  3.3355e-04,  3.4504e-03, -3.3493e-03,  7.2861e-03,\n", "         9.9487e-03,  5.1003e-03, -4.6039e-04, -5.0888e-03,  6.2141e-03,\n", "        -5.4240e-05, -3.6907e-03, -2.1744e-03, -4.2534e-03, -2.8019e-03,\n", "         2.1610e-03,  7.1869e-03,  4.1580e-03, -7.2527e-04,  1.2611e-02,\n", "        -7.8011e-04, -4.3297e-03,  5.2452e-03,  8.1558e-03,  8.9645e-03,\n", "         3.6097e-04, -4.3449e-03,  4.9438e-03,  7.3090e-03, -2.4929e-03,\n", "        -1.4648e-02,  6.6719e-03,  1.2093e-02, -8.1787e-03,  1.8263e-03,\n", "         1.5732e-02, -9.6512e-03,  7.3280e-03, -3.2902e-03, -7.3051e-03,\n", "        -3.4618e-03,  1.2665e-02,  3.9148e-04,  1.3611e-02, -2.4853e-03,\n", "        -1.4694e-02, -4.3373e-03, -7.3929e-03, -6.4888e-03,  4.8332e-03,\n", "        -2.3842e-06,  3.7899e-03,  2.9736e-03,  1.0567e-02,  3.8319e-03,\n", "        -8.0824e-04, -7.3099e-04, -1.1070e-02, -8.9951e-03,  1.4687e-03,\n", "        -4.2763e-03,  7.5378e-03,  8.8043e-03, -4.8141e-03, -3.4847e-03,\n", "        -8.2397e-04,  3.2043e-03, -4.6806e-03, -1.1642e-02,  1.7567e-03,\n", "         3.5706e-03, -1.0994e-02, -1.3733e-03, -6.6032e-03, -8.4991e-03,\n", "        -2.2411e-03,  3.2940e-03,  2.1763e-03,  7.7820e-03, -3.1166e-03,\n", "         7.8087e-03, -1.0384e-02, -5.2757e-03,  6.2866e-03,  2.8419e-03,\n", "         3.3932e-03, -6.7940e-03, -1.0216e-02,  2.8648e-03,  5.6419e-03,\n", "         2.9678e-03,  3.8738e-03, -9.8801e-03, -2.4819e-04, -7.3204e-03,\n", "         1.0117e-02], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.query.weight', Parameter containing:\n", "tensor([[-0.0670,  0.0042,  0.0724,  ..., -0.0096,  0.0179, -0.0723],\n", "        [ 0.0157,  0.0029,  0.0452,  ..., -0.0599,  0.0096, -0.0338],\n", "        [-0.0228,  0.0504, -0.0041,  ..., -0.0252, -0.0615,  0.0056],\n", "        ...,\n", "        [-0.0457,  0.0183,  0.0077,  ...,  0.0243, -0.0118,  0.0153],\n", "        [ 0.0390,  0.0197, -0.0055,  ..., -0.0140,  0.0056, -0.0312],\n", "        [-0.0214,  0.0238,  0.0084,  ..., -0.0079, -0.0221, -0.0171]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.query.bias', Parameter containing:\n", "tensor([ 0.0491, -0.0630, -0.0654,  ..., -0.0613, -0.0569, -0.0046],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.dense.weight', Parameter containing:\n", "tensor([[-0.0272,  0.0544,  0.0230,  ..., -0.0097,  0.0271,  0.0092],\n", "        [ 0.0412,  0.0198, -0.0724,  ..., -0.0199,  0.0417, -0.0216],\n", "        [ 0.0410, -0.0651, -0.0121,  ...,  0.0564,  0.0158, -0.0128],\n", "        ...,\n", "        [ 0.0186, -0.0065, -0.0302,  ..., -0.0008, -0.0382,  0.0106],\n", "        [ 0.0147, -0.0334,  0.0608,  ...,  0.0393, -0.0055, -0.0126],\n", "        [ 0.0008,  0.0614,  0.0409,  ...,  0.0157, -0.0189, -0.0116]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.attention.dense.bias', Parameter containing:\n", "tensor([-0.0581, -0.0479, -0.0273,  ...,  0.0363, -0.1071,  0.0141],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.post_attention_layernorm.weight', Parameter containing:\n", "tensor([0.9897, 0.9287, 0.9609,  ..., 0.9585, 0.9155, 1.0859], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('10.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.1368,  0.1309, -0.0333,  ...,  0.1135, -0.1521,  0.2208],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[-0.0339, -0.0213, -0.0090,  ...,  0.0431,  0.0149,  0.0196],\n", "        [-0.0179,  0.0695, -0.0006,  ..., -0.0007,  0.0317,  0.0143],\n", "        [-0.0320,  0.0366, -0.0448,  ...,  0.0020,  0.0002, -0.0883],\n", "        ...,\n", "        [ 0.0812, -0.0089, -0.0169,  ..., -0.0052, -0.0156, -0.0323],\n", "        [ 0.0094, -0.0105, -0.0350,  ..., -0.0260,  0.0414, -0.0101],\n", "        [ 0.0285,  0.0290, -0.0336,  ...,  0.0361, -0.0302,  0.0251]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0904, -0.0001, -0.0325,  ..., -0.0041, -0.0219,  0.0100],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-0.0164,  0.0401, -0.0324,  ..., -0.1105,  0.0468, -0.0065],\n", "        [ 0.0078,  0.0153,  0.0338,  ..., -0.0255,  0.0597, -0.0205],\n", "        [ 0.0207, -0.0320,  0.0183,  ..., -0.0172, -0.0248,  0.0238],\n", "        ...,\n", "        [-0.0649,  0.0089, -0.0050,  ...,  0.0203, -0.0013,  0.0034],\n", "        [ 0.0711, -0.0386,  0.0063,  ..., -0.0282,  0.0022, -0.0027],\n", "        [ 0.0736,  0.0154,  0.0790,  ..., -0.0463,  0.0218,  0.0615]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('10.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0022,  0.0399, -0.0265,  ...,  0.0021, -0.0698, -0.0205],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.input_layernorm.weight', Parameter containing:\n", "tensor([1.0137, 0.9629, 1.0049,  ..., 1.0342, 0.9370, 0.9976], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('11.input_layernorm.bias', Parameter containing:\n", "tensor([ 0.0148,  0.0194,  0.0388,  ..., -0.0227,  0.0363,  0.0022],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.key_value.weight', Parameter containing:\n", "tensor([[ 0.0113,  0.0033,  0.0093,  ..., -0.0396,  0.0104, -0.0053],\n", "        [-0.0478,  0.0214,  0.0362,  ..., -0.0346,  0.0197, -0.0202],\n", "        [ 0.0107, -0.0433,  0.0327,  ...,  0.0356, -0.0107, -0.0003],\n", "        ...,\n", "        [-0.0807, -0.0548,  0.0135,  ..., -0.0246, -0.0264,  0.0130],\n", "        [ 0.0674, -0.0220, -0.0082,  ...,  0.0461, -0.0834, -0.0266],\n", "        [ 0.0177,  0.0198,  0.0198,  ...,  0.0196, -0.0055, -0.0038]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.key_value.bias', Parameter containing:\n", "tensor([ 4.4861e-02,  2.8296e-01, -3.4576e-02, -5.0732e-01,  2.0508e-01,\n", "         7.6355e-02,  3.7036e-01,  1.7615e-01, -1.7014e-02,  2.9248e-01,\n", "         3.5645e-02,  2.0117e-01, -6.1377e-01, -1.0205e+00,  1.2512e-02,\n", "        -2.2049e-02,  5.1575e-02, -3.2324e-01, -3.4814e-01,  2.4048e-02,\n", "         1.4880e-01,  3.7939e-01, -3.2178e-01, -2.2375e-01,  8.5205e-02,\n", "         2.5464e-01,  3.6011e-01, -2.8589e-01, -1.6724e-01,  6.0840e-01,\n", "         2.8369e-01, -2.0703e-01, -1.8469e-01,  2.2217e-01,  8.2397e-02,\n", "         6.2305e-01,  7.0496e-02,  8.5693e-02, -2.0007e-01,  4.0283e-01,\n", "         1.3171e-01,  2.1594e-01, -1.0986e-01, -1.4490e-01,  1.3367e-01,\n", "         9.8877e-03, -1.5698e-01, -1.5332e-01,  2.6099e-01,  3.6072e-02,\n", "        -4.1309e-01,  6.9641e-02,  4.9463e-01,  6.2646e-01,  4.8267e-01,\n", "         4.6069e-01,  4.7070e-01,  7.2693e-02, -7.9224e-02,  3.9136e-01,\n", "         8.5791e-01, -7.0117e-01, -4.9414e-01, -9.2285e-02, -4.8242e-01,\n", "         8.4570e-01,  6.4926e-03, -2.5391e-02, -8.0383e-02, -2.4573e-01,\n", "        -1.6272e-01,  5.7617e-01,  2.8394e-01,  1.2903e-01, -1.2830e-01,\n", "         4.8022e-01, -5.4883e-01, -2.3937e-03,  3.1624e-03, -2.5757e-01,\n", "         9.7534e-02, -1.1162e+00, -4.1077e-02, -1.3123e-01,  3.4351e-01,\n", "        -4.0942e-01,  2.2400e-02,  3.1909e-01, -3.9819e-01, -2.2852e-01,\n", "         7.0068e-02,  1.0529e-02,  3.2532e-02,  1.3428e-01,  9.9609e-02,\n", "        -7.4756e-01,  3.0835e-01, -4.3854e-02, -9.2163e-02, -1.6907e-01,\n", "         3.0991e-02,  6.0547e-01, -6.1157e-02,  8.1787e-02,  2.2205e-01,\n", "        -4.6387e-02,  1.2344e-02,  1.1859e-01,  5.6982e-01,  2.4304e-01,\n", "        -5.6494e-01,  7.8857e-02, -2.3962e-01, -6.6772e-02,  2.2522e-01,\n", "         2.9541e-01,  1.1902e-02, -1.0492e-01, -2.5342e-01, -6.2793e-01,\n", "         1.1680e+00, -2.5482e-02,  3.1421e-01, -2.8125e-01,  4.5337e-01,\n", "        -5.3418e-01, -1.1523e-01,  3.1219e-02, -9.0256e-03, -2.7542e-03,\n", "         3.7074e-04, -4.4656e-04,  6.0844e-04, -3.0537e-03,  8.7967e-03,\n", "        -6.3515e-03,  1.1963e-02,  1.4618e-02,  2.3232e-03, -1.0994e-02,\n", "        -1.0513e-02,  6.9761e-04,  9.4604e-03,  4.2305e-03,  3.1128e-03,\n", "         7.4043e-03, -8.3694e-03, -5.8060e-03,  3.2127e-05,  3.6430e-03,\n", "        -3.2120e-03, -2.8839e-03, -7.8964e-03,  2.2488e-03,  2.9964e-03,\n", "        -7.8964e-03,  7.8201e-04, -3.1948e-03, -2.4242e-03,  4.0016e-03,\n", "         4.8370e-03,  1.8349e-03,  4.6196e-03,  5.5733e-03, -3.6011e-03,\n", "        -9.6436e-03, -4.5700e-03, -5.1308e-03, -3.4213e-04, -8.5831e-04,\n", "        -3.0384e-03, -7.3624e-03,  1.0384e-02,  4.2419e-03,  5.0507e-03,\n", "        -2.6917e-04,  5.2834e-03, -4.0321e-03,  6.6795e-03,  6.4888e-03,\n", "        -5.5084e-03,  5.6915e-03,  1.1806e-03,  6.4087e-03, -3.7022e-03,\n", "        -9.5940e-04, -4.0665e-03, -6.2370e-03, -1.1292e-02,  3.4084e-03,\n", "         3.1738e-03, -1.1134e-04,  1.0071e-03, -1.0765e-02,  2.9445e-04,\n", "        -5.2071e-03,  2.5597e-03,  5.3940e-03,  5.6992e-03, -8.4686e-03,\n", "         7.0190e-03, -7.8535e-04,  1.9093e-03,  1.2428e-02, -6.9618e-03,\n", "        -8.4209e-04, -4.2572e-03,  8.6060e-03, -4.5395e-03, -7.0915e-03,\n", "        -5.2452e-04,  8.2245e-03, -6.9885e-03, -6.0539e-03, -1.2751e-03,\n", "        -6.0081e-03,  6.1989e-03, -5.1956e-03,  1.5516e-03, -1.8663e-03,\n", "         1.1787e-02,  5.7755e-03,  6.9237e-03, -9.3307e-03,  2.1420e-03,\n", "        -1.4181e-03, -4.8943e-03,  5.2605e-03, -1.4162e-04,  5.6877e-03,\n", "        -6.9618e-04,  1.6251e-03,  4.1351e-03,  1.6556e-02,  2.4300e-03,\n", "        -4.0054e-04, -6.7329e-03,  1.9226e-03,  3.7308e-03,  8.3389e-03,\n", "        -5.0087e-03, -8.2092e-03,  1.1856e-02, -4.6425e-03, -7.7019e-03,\n", "        -5.3902e-03, -1.3275e-03, -5.2977e-04, -5.2528e-03,  5.0659e-03,\n", "         1.2589e-03, -1.7710e-03,  5.9319e-03,  4.1885e-03,  1.1196e-03,\n", "         2.2316e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.query.weight', Parameter containing:\n", "tensor([[-0.0141,  0.0324, -0.0147,  ..., -0.0320,  0.0210, -0.0078],\n", "        [-0.0324,  0.0255,  0.0290,  ...,  0.0158, -0.0271,  0.0077],\n", "        [-0.0070, -0.0001,  0.0050,  ..., -0.0117, -0.0624, -0.0734],\n", "        ...,\n", "        [ 0.0336,  0.0693,  0.0149,  ...,  0.0859, -0.0096, -0.0033],\n", "        [-0.0486, -0.0259, -0.0299,  ..., -0.0289, -0.0471, -0.0365],\n", "        [ 0.0635, -0.0181,  0.0103,  ..., -0.0105, -0.0036,  0.0625]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.query.bias', Parameter containing:\n", "tensor([-0.0118,  0.0394, -0.0304,  ..., -0.1151,  0.0760, -0.0674],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.dense.weight', Parameter containing:\n", "tensor([[-0.0341, -0.0257,  0.0087,  ...,  0.0237,  0.0538, -0.0097],\n", "        [-0.0331, -0.0041, -0.0170,  ..., -0.0473, -0.0449,  0.0219],\n", "        [-0.0176, -0.0015, -0.0040,  ...,  0.0111,  0.0535, -0.0247],\n", "        ...,\n", "        [-0.0270,  0.0186, -0.0149,  ...,  0.0287,  0.0221, -0.0192],\n", "        [ 0.0123, -0.0901,  0.0384,  ..., -0.0166, -0.0369,  0.0040],\n", "        [-0.0394, -0.0743, -0.0464,  ...,  0.0743,  0.0760,  0.0466]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.attention.dense.bias', Parameter containing:\n", "tensor([-0.0014, -0.0425, -0.0373,  ...,  0.0112, -0.0810,  0.0129],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.post_attention_layernorm.weight', Parameter containing:\n", "tensor([1.0527, 0.9917, 0.9873,  ..., 1.0166, 0.9424, 1.1055], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('11.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.1248,  0.0427, -0.0319,  ...,  0.1576, -0.1135,  0.1907],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[ 0.0532, -0.0228,  0.0404,  ...,  0.0147,  0.0178, -0.0384],\n", "        [ 0.0349, -0.0117,  0.0322,  ..., -0.0003,  0.0129, -0.0457],\n", "        [-0.0211,  0.0265,  0.0423,  ..., -0.0272,  0.0014,  0.0012],\n", "        ...,\n", "        [ 0.0103, -0.0322,  0.0381,  ..., -0.0035, -0.0560,  0.0315],\n", "        [-0.0282,  0.0133, -0.0177,  ..., -0.0386, -0.0068,  0.0118],\n", "        [-0.0094, -0.0155,  0.0054,  ...,  0.0224, -0.0289, -0.0431]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0556, -0.0121, -0.0231,  ..., -0.0431, -0.0552, -0.0616],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 0.0447, -0.0443,  0.0288,  ...,  0.0367, -0.0465,  0.0101],\n", "        [ 0.0115, -0.0048, -0.0121,  ..., -0.0009, -0.0276,  0.0144],\n", "        [-0.0603, -0.0112,  0.0044,  ...,  0.0467, -0.0266,  0.0145],\n", "        ...,\n", "        [ 0.0601,  0.0433, -0.0513,  ...,  0.0031, -0.0290, -0.0417],\n", "        [ 0.0495, -0.0366,  0.0211,  ...,  0.0404,  0.0043, -0.0078],\n", "        [-0.0468,  0.0405,  0.0090,  ..., -0.0021,  0.0788, -0.0096]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('11.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([ 0.0564,  0.0679,  0.0244,  ..., -0.0490, -0.0546, -0.0633],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.input_layernorm.weight', Parameter containing:\n", "tensor([1.0488, 1.0303, 1.0840,  ..., 1.0332, 0.9316, 0.9741], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('12.input_layernorm.bias', Parameter containing:\n", "tensor([-6.3232e-02,  5.6183e-02, -2.4974e-05,  ...,  2.6264e-03,\n", "        -9.4833e-03,  4.0985e-02], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('12.attention.key_value.weight', Parameter containing:\n", "tensor([[-0.0149,  0.0436,  0.0020,  ...,  0.0256,  0.0070, -0.0298],\n", "        [ 0.0273,  0.0367,  0.0304,  ..., -0.0359, -0.0137,  0.0475],\n", "        [ 0.0102, -0.0054, -0.0333,  ...,  0.0004, -0.0328, -0.0069],\n", "        ...,\n", "        [-0.0347,  0.0238,  0.1036,  ...,  0.1224, -0.0216, -0.0420],\n", "        [ 0.0167,  0.0152,  0.0684,  ..., -0.0028, -0.0533,  0.0784],\n", "        [ 0.0687, -0.0099, -0.0140,  ..., -0.0023,  0.0139,  0.0808]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.attention.key_value.bias', Parameter containing:\n", "tensor([-2.0300e-01, -1.6809e-01,  4.5929e-02, -2.0312e-01,  2.0398e-01,\n", "        -1.2915e-01, -3.2080e-01,  3.7817e-01, -1.6333e-01,  1.2226e-03,\n", "         1.2445e-01,  1.7407e-01, -2.9980e-01, -1.1938e-01,  2.0557e-01,\n", "         1.3806e-01,  1.8689e-01,  1.4648e-01, -1.6736e-01,  9.5764e-02,\n", "        -1.3049e-01, -4.3152e-02,  5.7831e-02,  8.0627e-02, -6.8176e-02,\n", "        -3.0884e-02, -3.3057e-01, -1.3818e-01,  1.1639e-01, -3.1665e-01,\n", "        -5.0476e-02, -1.4539e-01,  1.5518e-02,  5.6445e-01, -3.1470e-01,\n", "         1.4856e-01,  7.2998e-02, -1.7310e-01, -1.0437e-01,  2.6587e-01,\n", "        -8.2861e-01, -1.2718e-02, -4.7089e-02,  3.2422e-01, -2.8857e-01,\n", "         1.6406e-01,  1.7212e-01,  1.4148e-01, -2.5488e-01, -1.3245e-01,\n", "        -2.3010e-01, -1.1572e-01, -3.1372e-01,  1.7786e-01, -3.0701e-02,\n", "        -5.4834e-01, -2.6147e-01, -2.7661e-01,  7.7759e-02,  6.7444e-02,\n", "         1.5955e-01, -2.7222e-01, -3.0591e-01,  5.9814e-03,  1.3013e-01,\n", "        -2.5269e-01,  7.3891e-03,  1.9055e-01, -7.6050e-02,  4.9530e-02,\n", "        -2.4731e-01,  2.5854e-01,  1.7065e-01, -3.1030e-01, -1.9763e-01,\n", "         4.1309e-01,  3.4698e-02, -3.7036e-01,  3.0022e-03, -5.3027e-01,\n", "        -1.7053e-01, -1.7651e-01,  5.7404e-02,  4.5532e-01, -3.4199e-03,\n", "         2.9077e-01, -1.1969e-01, -4.7333e-02,  8.3313e-02, -2.4146e-01,\n", "         4.5996e-01, -1.4844e-01, -4.3221e-03,  3.6530e-02, -2.1820e-02,\n", "        -8.7830e-02,  7.2412e-01, -1.2683e-01,  3.8971e-02,  2.8467e-01,\n", "        -3.2324e-01,  3.7231e-01, -1.7532e-02,  4.7534e-01,  5.2277e-02,\n", "         8.7952e-02,  1.8555e-01, -1.1221e+00, -1.2006e-01,  2.5439e-01,\n", "         1.3220e-01, -9.2188e-01, -3.3960e-01,  5.3314e-02, -2.9370e-01,\n", "         2.9785e-02,  1.5466e-01, -1.2903e-01, -2.0142e-01, -3.8672e-01,\n", "        -6.9946e-02,  6.3904e-02, -3.0054e-01, -3.2349e-01, -3.8361e-02,\n", "        -1.0527e+00, -3.0005e-01, -1.4783e-01, -1.2482e-02,  1.6937e-03,\n", "        -3.9597e-03,  9.4318e-04, -1.2726e-02, -2.3384e-03, -9.6054e-03,\n", "        -2.9144e-03, -5.0163e-03, -4.3144e-03,  1.3409e-03,  1.0735e-02,\n", "        -1.3283e-02,  1.2131e-02,  1.2749e-02, -9.5978e-03,  2.5806e-03,\n", "        -1.2497e-02,  1.8982e-02, -5.6877e-03,  8.5907e-03,  4.9019e-03,\n", "         3.8681e-03, -2.8687e-03,  4.5319e-03,  1.1616e-03,  7.5150e-03,\n", "        -6.0654e-03, -1.7151e-02,  1.3294e-03,  1.1093e-02, -1.0445e-02,\n", "        -6.4659e-04, -2.1248e-03, -8.4915e-03,  1.4069e-02, -1.1950e-03,\n", "        -9.7580e-03,  1.0881e-03, -1.0658e-02,  2.9144e-03,  2.6524e-05,\n", "        -6.0005e-03, -1.6422e-03, -1.0590e-02, -7.2136e-03, -1.8051e-02,\n", "        -8.7357e-03, -1.2112e-03,  5.2757e-03,  5.0926e-03, -3.4351e-03,\n", "        -2.8515e-03,  4.5128e-03, -1.1353e-02, -1.0605e-02,  6.8474e-03,\n", "        -4.4975e-03, -1.0498e-02,  2.6417e-03,  2.3209e-02,  6.8436e-03,\n", "        -7.7515e-03,  2.5711e-03,  1.3412e-02,  1.8845e-02,  2.3132e-02,\n", "         7.3357e-03,  8.5526e-03, -1.3895e-03, -1.5545e-03,  1.1692e-03,\n", "        -9.5215e-03, -9.9335e-03,  1.5396e-02, -6.0349e-03,  1.0567e-02,\n", "         2.9144e-03, -1.4168e-02,  2.8629e-03, -1.1139e-02, -2.8076e-03,\n", "        -3.5877e-03,  1.0880e-02,  3.7909e-04,  1.3557e-02, -1.8387e-03,\n", "         1.3664e-02,  4.5624e-03,  6.1302e-03, -2.6302e-03, -1.2543e-02,\n", "        -3.7022e-03,  3.4103e-03,  1.0399e-02,  4.0016e-03, -9.3079e-03,\n", "        -1.4610e-02, -1.2474e-02,  6.2866e-03, -1.1955e-02,  1.3809e-03,\n", "         1.5049e-03, -1.4603e-02,  1.2215e-02, -9.1095e-03,  2.8210e-03,\n", "         1.4458e-02,  3.6221e-03,  2.5997e-03,  2.4261e-03,  9.7427e-03,\n", "         9.6283e-03,  2.5806e-03, -1.2650e-02,  2.8515e-03, -1.0872e-02,\n", "         1.1047e-02, -4.1199e-04, -1.3306e-02,  4.9782e-03,  1.4000e-02,\n", "        -1.8530e-03, -1.1467e-02, -2.6321e-03,  1.0696e-02, -3.1738e-03,\n", "         7.2021e-03], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.attention.query.weight', Parameter containing:\n", "tensor([[-0.0023,  0.0049,  0.0055,  ...,  0.0002,  0.0072, -0.0301],\n", "        [-0.0023,  0.0234,  0.0222,  ...,  0.0199, -0.0047,  0.0018],\n", "        [ 0.0010, -0.0039, -0.0125,  ..., -0.0081, -0.0014, -0.0112],\n", "        ...,\n", "        [ 0.0519, -0.0419,  0.0337,  ..., -0.0474,  0.0482, -0.0536],\n", "        [ 0.0013,  0.0349, -0.0554,  ...,  0.0028,  0.0499, -0.0040],\n", "        [ 0.0278,  0.0460, -0.0486,  ..., -0.0197, -0.0195,  0.0419]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.attention.query.bias', Parameter containing:\n", "tensor([-0.0533, -0.0172,  0.0807,  ..., -0.4839, -0.0370, -0.0334],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0366,  0.0540,  0.0123,  ...,  0.0560, -0.0203, -0.0517],\n", "        [ 0.0172,  0.0478,  0.0698,  ...,  0.0008, -0.0325, -0.0154],\n", "        [ 0.0023,  0.0364,  0.0621,  ...,  0.0224,  0.0605,  0.0199],\n", "        ...,\n", "        [-0.0401,  0.0741, -0.0234,  ...,  0.0234, -0.0103,  0.0355],\n", "        [-0.0428,  0.0030, -0.0352,  ...,  0.0049,  0.0091,  0.0047],\n", "        [-0.0352,  0.0100, -0.0101,  ...,  0.0497,  0.0567,  0.0004]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.attention.dense.bias', Parameter containing:\n", "tensor([-0.0279, -0.0448, -0.0671,  ...,  0.0191, -0.0222, -0.0698],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.post_attention_layernorm.weight', Parameter containing:\n", "tensor([1.1045, 1.0889, 1.0977,  ..., 1.1104, 1.1729, 1.1348], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('12.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0702,  0.1395, -0.0677,  ...,  0.1473, -0.2551,  0.0576],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[-0.0021, -0.0308, -0.0212,  ...,  0.0034,  0.0625, -0.0430],\n", "        [-0.0363, -0.0211,  0.0194,  ..., -0.0175,  0.0699, -0.0455],\n", "        [ 0.0663, -0.0286, -0.0117,  ...,  0.0488,  0.0204, -0.0629],\n", "        ...,\n", "        [ 0.0688, -0.0236,  0.0257,  ..., -0.0050,  0.0019, -0.0565],\n", "        [ 0.1001,  0.0538,  0.0260,  ..., -0.0012,  0.0034, -0.0122],\n", "        [ 0.0726,  0.0536,  0.0052,  ..., -0.0065,  0.0263, -0.0605]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0417,  0.0014, -0.0546,  ..., -0.0606, -0.0542, -0.0637],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[-0.0424,  0.0235, -0.0396,  ..., -0.0021, -0.0834,  0.0099],\n", "        [ 0.0006,  0.0240, -0.0119,  ..., -0.0074, -0.0434,  0.0234],\n", "        [-0.0210,  0.0415,  0.0024,  ...,  0.0769, -0.0443, -0.0564],\n", "        ...,\n", "        [-0.0099, -0.0497, -0.0126,  ..., -0.0442,  0.0075,  0.0595],\n", "        [-0.0689,  0.0596,  0.0323,  ..., -0.0222,  0.0089, -0.0004],\n", "        [ 0.0565, -0.0049, -0.0208,  ..., -0.0040, -0.0454,  0.0305]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('12.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([ 0.0497,  0.0141,  0.0295,  ..., -0.0821, -0.0033, -0.1179],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.input_layernorm.weight', Parameter containing:\n", "tensor([0.9575, 1.0361, 1.0371,  ..., 0.9292, 0.8857, 0.9819], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('13.input_layernorm.bias', Parameter containing:\n", "tensor([-0.1124,  0.0189, -0.0248,  ..., -0.0108, -0.1375,  0.0959],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.attention.key_value.weight', Parameter containing:\n", "tensor([[ 2.1011e-02,  8.1863e-03,  2.6535e-02,  ..., -8.4229e-02,\n", "          3.9062e-02, -1.4000e-02],\n", "        [ 3.9429e-02, -6.7711e-03,  9.4452e-03,  ..., -1.9928e-02,\n", "         -2.3499e-03, -9.4223e-03],\n", "        [ 5.9509e-03, -1.7029e-02,  4.1046e-02,  ..., -1.2390e-02,\n", "         -1.0002e-02,  3.1738e-02],\n", "        ...,\n", "        [ 6.5117e-03, -6.2683e-02,  8.2336e-02,  ...,  4.9042e-02,\n", "          3.4424e-02, -6.4758e-02],\n", "        [ 6.9824e-02, -1.6541e-01, -1.0468e-01,  ...,  1.7426e-02,\n", "         -2.3041e-03,  2.3251e-03],\n", "        [-4.2694e-02, -7.5317e-02,  9.3689e-02,  ..., -1.2317e-01,\n", "          6.5744e-05, -5.5237e-03]], device='cuda:0', dtype=torch.float16,\n", "       requires_grad=True))\n", "('13.attention.key_value.bias', Parameter containing:\n", "tensor([ 6.0645e-01, -9.6008e-02,  7.8491e-02, -1.4099e-01,  3.9233e-01,\n", "        -1.3745e-01, -6.9336e-02, -4.3152e-02, -1.7798e-01, -3.1281e-02,\n", "        -1.4172e-01,  3.0078e-01,  1.6541e-01, -5.9723e-02,  4.0601e-01,\n", "        -4.2017e-01,  4.7699e-02,  9.0393e-02, -1.5100e-01,  8.0811e-02,\n", "        -1.8335e-01, -2.3608e-01, -2.7588e-01, -1.5466e-01, -1.5601e-01,\n", "        -2.8275e-02,  1.5369e-01,  6.6101e-02,  1.1237e-01,  1.7273e-01,\n", "        -1.5466e-01, -2.2751e-02,  9.3323e-02,  1.6736e-01,  1.3464e-01,\n", "         5.3314e-02, -3.6353e-01,  1.5906e-01,  3.5522e-02, -2.5220e-01,\n", "         7.1533e-02, -3.3008e-01, -1.5088e-01, -3.7842e-01, -9.0088e-02,\n", "         4.1656e-02,  2.6953e-01,  2.0935e-01, -1.1810e-01,  3.8110e-01,\n", "        -2.5830e-01, -2.6294e-01,  2.5562e-01, -1.7236e-01, -1.6614e-01,\n", "        -4.0674e-01,  1.5295e-01,  2.0264e-01, -2.4121e-01,  1.2683e-01,\n", "        -2.5513e-01, -4.3945e-02,  3.8135e-01, -1.2646e-01,  5.7648e-02,\n", "        -3.1097e-02, -3.9673e-01,  1.6858e-01,  9.7290e-02,  1.7957e-01,\n", "        -1.2622e-01, -1.7493e-01, -1.3159e-01,  5.4749e-02,  2.7515e-01,\n", "        -3.6182e-01, -1.6296e-02, -2.3901e-01,  3.2013e-02,  3.8721e-01,\n", "         6.1569e-03,  4.8291e-01,  2.5049e-01,  3.3569e-02,  1.5247e-01,\n", "        -1.0284e-01,  1.9867e-02,  5.6244e-02, -6.4819e-02,  8.6548e-02,\n", "        -2.9739e-02, -1.4124e-01, -5.0995e-02,  1.2372e-01, -6.9214e-02,\n", "         1.4893e-01,  2.0068e-01,  2.7710e-01, -5.2094e-02,  1.5698e-01,\n", "        -1.1475e-02, -2.6099e-01,  5.6006e-01,  9.4177e-02, -1.2866e-01,\n", "        -9.1003e-02, -2.7148e-01,  1.1908e-01, -6.2195e-02,  2.7588e-01,\n", "         1.2976e-01, -7.1533e-02,  3.2196e-02,  9.7656e-02,  3.1311e-02,\n", "         1.1548e-01,  3.6670e-01,  6.3171e-02, -5.4291e-02,  2.3792e-01,\n", "        -1.4246e-01,  2.3450e-01,  3.6719e-01, -4.6704e-01, -1.7899e-02,\n", "         1.0870e-01,  3.1281e-02, -1.2366e-01,  1.1398e-02, -1.9836e-03,\n", "         3.8795e-03,  1.4336e-02,  1.8295e-02, -9.1324e-03,  7.1373e-03,\n", "        -6.0730e-03,  7.5645e-03,  9.6588e-03, -5.8823e-03,  1.5839e-02,\n", "         9.7427e-03,  1.0201e-02, -2.3193e-02,  2.3956e-03, -1.0139e-02,\n", "         1.7843e-03,  1.4381e-02,  2.0157e-02, -1.8339e-03, -1.3618e-03,\n", "        -7.9193e-03, -2.1423e-02, -9.1858e-03,  8.3923e-04, -4.2009e-04,\n", "        -8.4076e-03, -4.8375e-04,  1.7136e-02,  1.9089e-02, -5.5580e-03,\n", "        -2.1729e-02,  9.5901e-03,  1.9026e-03, -1.0414e-03, -6.1035e-04,\n", "        -2.8229e-03,  6.1951e-03, -2.1973e-03, -2.1255e-04, -6.5422e-03,\n", "        -1.3447e-03, -5.9557e-04, -1.4389e-02, -2.3403e-03,  1.5358e-02,\n", "         5.9509e-03, -1.3824e-02, -1.8707e-02,  7.2823e-03, -1.4429e-03,\n", "        -1.5762e-02, -8.6060e-03,  5.4407e-04,  1.5686e-02, -2.1420e-03,\n", "         1.0330e-02, -2.0721e-02, -1.0147e-03, -2.2232e-02, -5.4169e-03,\n", "        -1.1635e-03,  1.7815e-03, -4.1275e-03, -7.6904e-03,  5.4359e-03,\n", "         5.9853e-03, -5.9624e-03,  7.6370e-03, -6.3095e-03, -1.6479e-02,\n", "        -1.9012e-02, -2.5436e-02,  7.7095e-03, -1.2985e-02, -1.0691e-03,\n", "         2.4776e-03, -5.0659e-03,  7.3128e-03,  6.3820e-03, -7.1411e-03,\n", "        -7.7362e-03,  8.4076e-03,  3.3703e-03,  1.7746e-02,  4.1046e-03,\n", "        -9.7580e-03, -5.6229e-03,  5.2299e-03,  7.7171e-03,  1.4114e-02,\n", "         4.1847e-03, -1.9470e-02,  2.5845e-03,  9.8038e-03,  1.0056e-02,\n", "         8.1406e-03,  2.1191e-03, -1.5697e-03, -8.2550e-03, -1.1894e-02,\n", "         2.1606e-02,  9.9335e-03, -2.1946e-04,  1.7014e-02, -3.8109e-03,\n", "        -3.5744e-03, -4.7798e-03, -1.2421e-02, -4.7531e-03, -2.5009e-02,\n", "         2.8038e-04, -1.2856e-02, -1.0109e-03,  5.6725e-03,  9.9564e-03,\n", "         1.8265e-02,  5.9271e-04, -9.8648e-03, -6.6910e-03,  4.6387e-03,\n", "         5.1880e-03, -1.2512e-03, -2.1027e-02, -1.0910e-02,  3.0732e-04,\n", "        -2.6001e-02], device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.attention.query.weight', Parameter containing:\n", "tensor([[ 0.0078, -0.0084, -0.0126,  ..., -0.0058,  0.0292,  0.0095],\n", "        [ 0.0565, -0.0176,  0.0354,  ...,  0.0053,  0.0547,  0.0156],\n", "        [ 0.0679, -0.0324,  0.0144,  ..., -0.0277, -0.0059,  0.0370],\n", "        ...,\n", "        [-0.0778, -0.0217, -0.0003,  ...,  0.0197,  0.0131,  0.0248],\n", "        [ 0.0359,  0.0461, -0.0163,  ...,  0.0197,  0.0139,  0.0228],\n", "        [ 0.0171,  0.0328,  0.0175,  ..., -0.0134,  0.0229,  0.0201]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.attention.query.bias', Parameter containing:\n", "tensor([-0.0648, -0.0578, -0.0131,  ..., -0.0840, -0.0108, -0.0381],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.attention.dense.weight', Parameter containing:\n", "tensor([[ 0.0978,  0.0723,  0.0162,  ...,  0.0594,  0.0048, -0.0293],\n", "        [ 0.0360,  0.0119,  0.0386,  ...,  0.0516,  0.0519,  0.0504],\n", "        [-0.0237,  0.0010, -0.1033,  ..., -0.0938, -0.0131, -0.0028],\n", "        ...,\n", "        [ 0.0068, -0.0448,  0.0217,  ...,  0.0156, -0.0492,  0.0698],\n", "        [ 0.0538,  0.0467,  0.1016,  ..., -0.0005, -0.0386, -0.0542],\n", "        [ 0.0068, -0.0173,  0.0029,  ..., -0.0552, -0.0065, -0.0719]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.attention.dense.bias', Parameter containing:\n", "tensor([-0.0545, -0.0321, -0.0465,  ..., -0.0187, -0.0854, -0.0599],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.post_attention_layernorm.weight', Parameter containing:\n", "tensor([1.0801, 1.0605, 1.0166,  ..., 0.9995, 1.0674, 1.1221], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('13.post_attention_layernorm.bias', Parameter containing:\n", "tensor([-0.0627,  0.1410, -0.0769,  ...,  0.1179, -0.2588,  0.0724],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.mlp.dense_h_to_4h.weight', Parameter containing:\n", "tensor([[-0.0545, -0.0249,  0.0529,  ...,  0.0384, -0.0171,  0.0173],\n", "        [-0.0665,  0.0153, -0.0267,  ...,  0.0261,  0.0258,  0.0238],\n", "        [ 0.0033,  0.0032,  0.0168,  ...,  0.0009, -0.0148, -0.0293],\n", "        ...,\n", "        [ 0.0299,  0.0069,  0.0601,  ..., -0.0378,  0.0405, -0.0228],\n", "        [-0.0288,  0.0480,  0.0150,  ..., -0.0272, -0.0524,  0.0129],\n", "        [-0.0240,  0.0235, -0.0531,  ..., -0.0067, -0.0160,  0.0022]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.mlp.dense_h_to_4h.bias', Parameter containing:\n", "tensor([-0.0878, -0.0112, -0.0494,  ..., -0.0376, -0.0514, -0.0972],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.mlp.dense_4h_to_h.weight', Parameter containing:\n", "tensor([[ 0.0047,  0.0801,  0.0204,  ...,  0.0645, -0.0564, -0.0041],\n", "        [ 0.0649, -0.0215, -0.0222,  ..., -0.0091, -0.0324, -0.0155],\n", "        [-0.0496,  0.0531, -0.0315,  ...,  0.0457, -0.0094,  0.0183],\n", "        ...,\n", "        [-0.0187,  0.0422, -0.0148,  ..., -0.0387,  0.0354,  0.0084],\n", "        [ 0.0797, -0.0670,  0.0220,  ...,  0.0278,  0.0163, -0.0214],\n", "        [-0.0696,  0.0251,  0.0164,  ...,  0.0037, -0.0059,  0.0369]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('13.mlp.dense_4h_to_h.bias', Parameter containing:\n", "tensor([-0.0554,  0.0616, -0.0271,  ..., -0.0443, -0.0047, -0.0137],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('15.norm.weight', Parameter containing:\n", "tensor([2.5078, 2.5527, 2.5488,  ..., 2.5352, 3.0996, 2.3613], device='cuda:0',\n", "       dtype=torch.float16, requires_grad=True))\n", "('15.norm.bias', Parameter containing:\n", "tensor([ 0.0094, -0.0624,  0.0465,  ..., -0.1670,  0.4109, -0.0126],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n", "('16.final_linear.weight', Parameter containing:\n", "tensor([[-0.0550, -0.0729, -0.1002,  ..., -0.0685,  0.0036, -0.0529],\n", "        [ 0.0294, -0.0222, -0.0045,  ...,  0.0069, -0.0087, -0.0442],\n", "        [-0.0721, -0.0510,  0.0229,  ...,  0.0319, -0.0026, -0.0451],\n", "        ...,\n", "        [ 0.0380, -0.0295,  0.0239,  ...,  0.0131, -0.0086, -0.0565],\n", "        [ 0.0379, -0.0292,  0.0241,  ...,  0.0135, -0.0086, -0.0566],\n", "        [ 0.0382, -0.0293,  0.0238,  ...,  0.0133, -0.0086, -0.0565]],\n", "       device='cuda:0', dtype=torch.float16, requires_grad=True))\n"]}], "source": ["for x in model.neox_model.module.named_parameters():\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPARTA: EmbeddingPipe input_ids= tensor([[49155, 49156,     5,  ...,    43,    36,     2]], device='cuda:0')\n", "SPARTA: EmbeddingPipe position_ids= tensor([[   0,    1,    2,  ..., 3812, 3813, 3814]], device='cuda:0')\n", "SPARTA: EmbeddingPipe pre-dropout output tensor([[[ 0.0085,  0.0373, -0.0076,  ..., -0.0084,  0.0212,  0.0519],\n", "         [-0.0043, -0.0255,  0.0122,  ..., -0.0606, -0.0002, -0.0173],\n", "         [ 0.0488, -0.0806,  0.0887,  ...,  0.0005,  0.0162,  0.0444],\n", "         ...,\n", "         [-0.0604,  0.0064,  0.0279,  ..., -0.0035, -0.0495, -0.0206],\n", "         [-0.0833, -0.0002,  0.0486,  ...,  0.0230,  0.0019,  0.0868],\n", "         [-0.0170, -0.1345,  0.0494,  ..., -0.0842,  0.0412,  0.0378]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: EmbeddingPipe output tensor([[[ 0.0310,  0.1353, -0.0277,  ..., -0.0304,  0.0770,  0.1886],\n", "         [-0.0158, -0.0925,  0.0442,  ..., -0.2200, -0.0008, -0.0629],\n", "         [ 0.1772, -0.2927,  0.3220,  ...,  0.0018,  0.0587,  0.1614],\n", "         ...,\n", "         [-0.2191,  0.0233,  0.1011,  ..., -0.0126, -0.1798, -0.0749],\n", "         [-0.3022, -0.0006,  0.1764,  ...,  0.0837,  0.0069,  0.3152],\n", "         [-0.0618, -0.4885,  0.1794,  ..., -0.3057,  0.1497,  0.1375]]],\n", "       device='cuda:0', dtype=torch.float16)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.1293,  0.5698,  0.2375,  ..., -0.5742,  0.1238, -0.3301]],\n", "\n", "        [[-0.2050, -0.0813,  0.7344,  ..., -0.5029,  0.2651, -0.6587]],\n", "\n", "        [[ 1.0078, -0.2300,  0.8149,  ...,  0.1103, -0.1643,  0.1912]],\n", "\n", "        ...,\n", "\n", "        [[-0.2593,  0.1646,  0.3008,  ..., -0.0462, -0.4038, -0.0311]],\n", "\n", "        [[-0.5195,  0.3037,  0.3735,  ...,  0.4014, -0.0130,  0.5391]],\n", "\n", "        [[ 0.0884, -0.9106,  0.3447,  ..., -0.3757,  0.4380,  0.1367]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.5381,  0.5557,  0.3269,  ..., -0.9229,  0.1667, -0.6016]],\n", "\n", "        [[-0.1699, -0.3252,  0.7432,  ..., -0.2178,  0.3462, -0.7246]],\n", "\n", "        [[ 1.1904, -0.3291,  0.7305,  ...,  0.0099, -0.1908,  0.2018]],\n", "\n", "        ...,\n", "\n", "        [[-0.2820,  0.1273,  0.4060,  ...,  0.1282, -0.2397,  0.0857]],\n", "\n", "        [[-0.5503,  0.3777,  0.4304,  ...,  0.5166,  0.1050,  0.4775]],\n", "\n", "        [[ 0.3152, -0.8306,  0.2144,  ..., -0.3499,  0.6025,  0.2085]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 0.7754,  0.5269,  0.1954,  ..., -1.1279,  0.0839, -0.6431]],\n", "\n", "        [[-0.1982, -0.5732,  0.8052,  ..., -0.0188,  0.3022, -0.6001]],\n", "\n", "        [[ 1.4736, -0.4915,  0.7207,  ...,  0.2725, -0.6753,  0.2070]],\n", "\n", "        ...,\n", "\n", "        [[-0.5459, -0.0151,  0.4565,  ...,  0.0734,  0.0947,  0.1925]],\n", "\n", "        [[-0.8008,  0.2168,  0.5557,  ...,  0.5537,  0.3740,  0.5479]],\n", "\n", "        [[ 0.2224, -0.6934, -0.1453,  ..., -0.5728,  0.7280,  0.4275]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0469,  0.5464,  0.3379,  ..., -1.2461,  0.3523, -0.5640]],\n", "\n", "        [[-0.4492, -0.5830,  1.0332,  ..., -0.1058,  0.8564, -0.8867]],\n", "\n", "        [[ 2.3887, -0.7793,  0.9980,  ...,  0.6025, -1.0742,  0.4358]],\n", "\n", "        ...,\n", "\n", "        [[-1.0254,  0.0630,  0.3435,  ..., -0.1154, -0.1592,  0.4604]],\n", "\n", "        [[-1.1699,  0.1708,  0.5444,  ...,  0.5166, -0.0168,  0.6504]],\n", "\n", "        [[ 0.0909, -0.8159,  0.0251,  ..., -0.3748,  0.5933,  0.4729]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0938,  0.6025,  0.4731,  ..., -1.3906,  0.3806, -0.6494]],\n", "\n", "        [[-0.5039, -0.8208,  0.9111,  ..., -0.1272,  0.5820, -0.5337]],\n", "\n", "        [[ 2.2266, -0.9209,  1.5820,  ...,  0.7959, -1.5527,  0.6348]],\n", "\n", "        ...,\n", "\n", "        [[-1.0420,  0.4492,  0.3228,  ..., -0.0271,  0.3076, -0.5415]],\n", "\n", "        [[-1.4932,  0.1375,  0.7041,  ...,  1.0732,  0.3276,  0.4136]],\n", "\n", "        [[-0.3135, -0.8828, -0.9688,  ..., -1.3232, -0.0712,  0.8730]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.1504,  0.6143,  0.2869,  ..., -1.3750,  0.1565, -0.7104]],\n", "\n", "        [[-1.0352, -0.2271,  0.2070,  ..., -0.3127,  0.4314, -1.0742]],\n", "\n", "        [[ 1.7930, -0.6650,  1.7764,  ...,  0.9766, -1.8896,  1.2080]],\n", "\n", "        ...,\n", "\n", "        [[-0.7881,  0.4937,  0.4353,  ..., -0.2854,  0.3525, -0.6909]],\n", "\n", "        [[-1.4639, -0.4385,  1.5117,  ...,  1.1533, -0.0903,  0.4585]],\n", "\n", "        [[ 0.3433, -1.0205, -1.5547,  ..., -1.4785, -0.3914,  1.7275]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.0488,  0.5654,  0.2754,  ..., -1.3604,  0.2061, -0.8130]],\n", "\n", "        [[-1.2168,  0.8081,  0.8613,  ..., -0.4148,  0.5957, -1.2236]],\n", "\n", "        [[ 1.8623, -0.6470,  1.5898,  ...,  1.0117, -2.3164,  1.5234]],\n", "\n", "        ...,\n", "\n", "        [[-0.9995,  1.1133,  1.1748,  ..., -0.3279,  0.6416, -0.7256]],\n", "\n", "        [[-1.4531, -0.3662,  2.0684,  ...,  0.7715,  0.5830,  0.3267]],\n", "\n", "        [[ 0.5342, -0.4233, -1.4971,  ..., -1.1494, -0.1860,  1.1875]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.2090,  0.3733,  0.4165,  ..., -1.3691,  0.0435, -0.9658]],\n", "\n", "        [[-0.6323,  1.3379,  0.8530,  ..., -0.2327,  0.9746, -1.2881]],\n", "\n", "        [[ 1.8682, -1.3369,  1.1738,  ...,  0.9087, -2.6895,  0.4990]],\n", "\n", "        ...,\n", "\n", "        [[-0.1318,  0.9072,  0.5063,  ..., -0.8125,  1.6699, -0.4771]],\n", "\n", "        [[-2.2754, -0.3081,  2.3008,  ...,  1.4941,  2.0254,  0.5093]],\n", "\n", "        [[ 1.3242, -0.3518, -2.0938,  ..., -1.2900, -0.6733,  1.5127]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.4980,  0.3870,  0.3574,  ..., -1.2920, -0.0659, -1.6553]],\n", "\n", "        [[-0.0911,  0.6597,  0.2788,  ..., -0.6816,  2.0117, -1.9209]],\n", "\n", "        [[ 1.7881, -1.4336,  1.0498,  ...,  1.2969, -3.4141,  0.0225]],\n", "\n", "        ...,\n", "\n", "        [[ 1.0410,  0.8809, -0.5762,  ..., -0.4004,  2.5215,  0.0591]],\n", "\n", "        [[-0.5615, -0.2483,  1.7988,  ...,  1.2275,  2.4316,  0.6133]],\n", "\n", "        [[ 1.5254,  0.9277, -1.8682,  ..., -0.4561, -0.1626,  2.7109]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9648,  0.3818,  0.3896,  ..., -1.5527, -0.0259, -2.0059]],\n", "\n", "        [[ 0.2057,  0.8374,  0.5640,  ..., -0.4653,  3.6523, -2.7188]],\n", "\n", "        [[ 2.2715, -1.0928,  1.7197,  ...,  1.1943, -4.4492, -0.5835]],\n", "\n", "        ...,\n", "\n", "        [[ 2.3477,  1.4883, -1.0547,  ...,  0.2305,  2.3340,  0.0199]],\n", "\n", "        [[ 0.6641,  0.8428,  1.7021,  ...,  2.2188,  2.7422,  0.1719]],\n", "\n", "        [[ 2.7285,  2.2852, -2.4473,  ...,  1.4316,  0.2539,  2.8281]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 2.2324e+00,  2.3218e-01,  3.8867e-01,  ..., -1.7461e+00,\n", "           8.9355e-01, -2.0449e+00]],\n", "\n", "        [[ 8.5059e-01,  4.6844e-03,  6.6650e-02,  ..., -6.5381e-01,\n", "           6.1562e+00, -2.6211e+00]],\n", "\n", "        [[ 2.5449e+00, -8.5254e-01,  2.0625e+00,  ...,  1.5723e+00,\n", "          -3.9199e+00, -4.9048e-01]],\n", "\n", "        ...,\n", "\n", "        [[ 3.1113e+00,  4.6436e-01, -2.1758e+00,  ...,  3.2642e-01,\n", "           3.6836e+00, -4.5605e-01]],\n", "\n", "        [[ 1.7871e+00,  1.0615e+00,  6.1182e-01,  ...,  2.5703e+00,\n", "           4.5234e+00, -5.8008e-01]],\n", "\n", "        [[ 2.8086e+00,  6.8125e+00, -2.2520e+00,  ...,  2.1895e+00,\n", "           8.1396e-01,  2.5742e+00]]], device='cuda:0', dtype=torch.float16,\n", "       grad_fn=<AddBackward0>)\n", "SPARTA: ParallelTransformerLayer.forward tensor([[[ 1.9141,  0.5264,  0.6724,  ..., -1.2520,  0.6792, -2.2910]],\n", "\n", "        [[ 6.7344, -0.5229, -1.3125,  ..., -0.3965,  6.2734, -6.9922]],\n", "\n", "        [[ 3.7344, -1.2832,  0.0605,  ...,  1.3594, -3.2461, -1.7510]],\n", "\n", "        ...,\n", "\n", "        [[ 3.4453,  0.9023, -1.5586,  ..., -0.9224,  3.6699, -1.9492]],\n", "\n", "        [[ 1.8467,  1.6240,  0.9316,  ...,  1.9580,  4.1523, -1.7090]],\n", "\n", "        [[ 2.2207,  8.9375, -1.8145,  ...,  3.7812,  2.7480,  1.9072]]],\n", "       device='cuda:0', dtype=torch.float16, grad_fn=<AddBackward0>)\n"]}], "source": ["import megatron.text_generation_utils as text_gen_utils\n", "import gc\n", "\n", "with torch.no_grad():\n", "    context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([data[0]['context']], dtype=torch.int64))                \n", "    model_inputs = (\n", "        context_tokens_tensor,  # input_ids\n", "        position_ids,  # position_ids\n", "        attention_mask,  # attention_mask\n", "    )            \n", "    \n", "    model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "    logits = text_gen_utils.forward_model(\n", "        model=model.neox_model,\n", "        model_inputs=model_inputs,\n", "        is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "        pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "        timers=None,\n", "    )\n", "    model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "    # logits = logits[0]\n", "#     logprobs = F.log_softmax(logits, dim=-1)\n", "#     top_predictions = torch.topk(logprobs, self.tokens_budget, dim=-1, sorted=True)                \n", "\n", "# logprobs = logprobs.float().cpu().numpy()\n", "# top_prediction_values = top_predictions.values.float().cpu().numpy()\n", "# top_prediction_indices = top_predictions.indices.cpu().numpy()\n", "gc.collect()\n", "torch.cuda.empty_cache()                \n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[-0.8447,  3.5293, -3.7617,  ..., -3.4492, -3.4531, -3.4531],\n", "         [ 9.5781, -5.2461, -5.3555,  ..., -4.2891, -4.2812, -4.2891],\n", "         [ 6.7539,  0.2217, -2.5176,  ..., -1.6133, -1.6035, -1.6123],\n", "         ...,\n", "         [ 1.8350, -1.5117,  8.1562,  ..., -2.1680, -2.1758, -2.1719],\n", "         [-0.5054, -2.8457,  8.2188,  ..., -2.7266, -2.7285, -2.7227],\n", "         [ 3.3066, -4.1289, -2.7793,  ..., -4.7656, -4.7656, -4.7656]]],\n", "       device='cuda:0', dtype=torch.float16)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["logits"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'dummy': tensor([0.9985], dtype=torch.float16),\n", " 'word_embeddings.weight': tensor([[-0.0076, -0.0348,  0.0070,  ..., -0.0047,  0.0316,  0.0219],\n", "         [-0.0014,  0.0068, -0.0043,  ...,  0.0185,  0.0067, -0.0028],\n", "         [-0.0131, -0.0045,  0.0184,  ...,  0.0071,  0.0100,  0.0063],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000]],\n", "        dtype=torch.float16),\n", " 'position_embeddings.weight': tensor([[ 4.2953e-03, -1.2245e-03, -1.9372e-05,  ..., -7.6866e-03,\n", "          -8.4610e-03, -1.0329e-04],\n", "         [-5.2643e-03, -6.8741e-03,  6.3972e-03,  ...,  1.3924e-02,\n", "           1.1444e-03,  1.5602e-03],\n", "         [-5.7793e-03,  9.3613e-03, -2.9106e-03,  ...,  1.4542e-02,\n", "          -4.6387e-03,  2.0390e-03],\n", "         ...,\n", "         [-5.6725e-03,  7.5340e-03,  5.0316e-03,  ...,  8.6927e-04,\n", "           9.1476e-03,  1.2077e-02],\n", "         [ 6.3744e-03,  1.8988e-03,  5.2147e-03,  ..., -5.0621e-03,\n", "          -1.0674e-02,  1.6220e-02],\n", "         [ 9.8324e-04, -6.5947e-04,  8.6594e-03,  ..., -4.6959e-03,\n", "          -1.1587e-03,  1.3115e-02]], dtype=torch.float16)}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "x = torch.load(\"/mnt/efs/augment/checkpoints/rogue/rogue1B_diffb1m_chunk30/global_step1000/layer_00-model_00-model_states.pt\")\n", "x"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["'np.pi / 2) * np.array([0, 0, 1])<|pause|>'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.detokenize([2356, 32, 1295, 517, 225, 36, 27, 319, 2065, 32, 955, 2009, 34, 30, 225, 34, 30, 225, 35, 1130, 49154])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "import numpy as np\n", "import json\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from tqdm.notebook import tqdm\n", "\n", "\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["FIM_MIDDLE_ID = tokenizer.fim_middle_id\n", "STOP_TOKEN_IDS = [tokenizer.pad_id, tokenizer.pause_id, tokenizer.skip_id, tokenizer.eod_id]\n", "\n", "def find(tokens, target_token):\n", "    if target_token in tokens:\n", "        return np.argwhere(tokens == target_token)[0, 0]\n", "    else:\n", "        return None\n", "\n", "def cut_tokens_by_stop_token(tokens):\n", "    position = None\n", "    for stop_token_id in STOP_TOKEN_IDS:\n", "        current_position = find(tokens, stop_token_id)\n", "        if current_position is not None:\n", "            if position is None:\n", "                position = current_position\n", "            else:\n", "                position = min(current_position, position)\n", "    if position is not None:\n", "        return tokens[:position + 1]\n", "    else:\n", "        return tokens"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}], "source": ["# INPUT_PATH = '/mnt/efs/augment/data/processed/rag/dataset/diffb_v2_chunk30/validation_dataset'\n", "# OUTPUT_PATH = '/mnt/efs/augment/user/yury/benchmarking_data_v2/diffb_v2_chunk30.jsonl'\n", "\n", "INPUT_PATH = '/mnt/efs/augment/data/processed/rag/dataset/eth61m_depause_prefretsuf_npref100_olap0_quant20/validation_dataset'\n", "OUTPUT_PATH = '/mnt/efs/augment/user/yury/benchmarking_data_v2/eth61m_depause_prefretsuf_npref100_olap0_quant20.jsonl'\n", "\n", "dataset = indexed_dataset.make_dataset(INPUT_PATH, \"infer\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a962fd0a0ed14b7db04390ec2d3d22cc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/12141 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CANNOT FIND FIM MIDDLE. SKIPPING\n"]}], "source": ["data = []\n", "\n", "for datum in tqdm(dataset):    \n", "    tokens = cut_tokens_by_stop_token(datum)    \n", "    fim_position = find(tokens, FIM_MIDDLE_ID)\n", "    if fim_position is None:\n", "        print('CANNOT FIND FIM MIDDLE. SKIPPING')\n", "        continue    \n", "    assert fim_position is not None    \n", "    context = tokens[:fim_position + 1]\n", "    label = tokens[fim_position + 1:]\n", "    data.append({\n", "        \"context\": context.tolist(),\n", "        \"label\": label.tolist(),\n", "    })\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(data)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of context', ylabel='Frequency'>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def custom_hist(array, bins, xlabel, ylabel, figsize=(20, 5), fontsize=14):\n", "  array = np.array(array)\n", "  bins = np.array(bins + [bins[-1]])\n", "  assert array.min() >= bins.min()\n", "  assert array.max() <= bins.max()\n", "  left_edge = bins[:-1]\n", "  right_edge = bins[1:] - 1\n", "  # Rightmost element is inclusive.\n", "  # See https://numpy.org/doc/stable/reference/generated/numpy.histogram.html\n", "  # for details.\n", "  right_edge[-1] += 1\n", "\n", "  hist, bin_edges = np.histogram(array, bins)\n", "  fig1, ax = plt.subplots(figsize=figsize)\n", "\n", "  # Plot the histogram heights against integers on the x axis\n", "  ax.bar(range(len(hist)), hist, width=1)\n", "\n", "  ax.tick_params(axis='y', labelsize=fontsize )\n", "  for i in range(len(hist)):\n", "    plt.text(i, hist[i] + 1, hist[i], ha=\"center\", fontsize=fontsize - 2)\n", "\n", "  # Set the ticks to the middle of the bars\n", "  ax.set_xticks([i for i, j in enumerate(hist)])\n", "  # Set the xticklabels to a string that tells us what the bin edges were\n", "  xticklabels = []\n", "  for i in range(len(left_edge)):\n", "    if right_edge[i] - left_edge[i] == 0:\n", "      xticklabels.append('%.1f' % left_edge[i])\n", "    else:\n", "      xticklabels.append('%.1f - %.1f' % (left_edge[i], right_edge[i]))\n", "  xticklabels[-1] = 'None'\n", "\n", "  ax.set_xticklabels(xticklabels, fontsize=fontsize) \n", "  ax.set_xlabel(xlabel, fontsize=14)\n", "  ax.set_ylabel(ylabel, fontsize=14)\n", "  ax.autoscale()\n", "  return ax\n", "\n", "custom_hist([len(x['context']) for x in data], bins=[100, 2000, 3000, 3500, 3750, 3839, 4000, 5000], xlabel='Lenght of context', ylabel='Frequency')\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of label', ylabel='Frequency'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_hist([len(x['label']) for x in data], bins=[1, 4, 8, 16, 25, 50, 100, 200, 1000], xlabel='Lenght of label', ylabel='Frequency')\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original data size 12140 filtered into 5302\n"]}], "source": ["filtered_data = [x for x in data if len(x['label']) >= 10]\n", "print('Original data size', len(data), 'filtered into', len(filtered_data))\n", "with open(OUTPUT_PATH, 'w') as f:\n", "    json.dump(filtered_data, f)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of context', ylabel='Frequency'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_hist([len(x['context']) for x in filtered_data], bins=[100, 2000, 3000, 3500, 3750, 3839, 4000, 5000], xlabel='Lenght of context', ylabel='Frequency')\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Lenght of label', ylabel='Frequency'>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_hist([len(x['label']) for x in filtered_data], bins=[1, 4, 8, 16, 25, 50, 100, 200, 1000], xlabel='Lenght of label', ylabel='Frequency')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import json\n", "\n", "import argparse\n", "from types import SimpleNamespace\n", "\n", "from megatron.data import indexed_dataset\n", "from megatron.tokenizer import build_tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PATH = '/mnt/efs/augment/data/processed/rag/dataset/eth61m_depause_prefretsuf_npref100_olap0_quant20/validation_dataset'\n", "\n", "dataset = indexed_dataset.make_dataset(PATH, \"infer\")\n", "\n", "for item in dataset[:1]:\n", "    print(item)\n", "    print(len(item))\n", "    print(tokenizer.detokenize(item))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FIM_MIDDLE_ID = tokenizer.fim_middle_id\n", "STOP_TOKEN_IDS = [tokenizer.pad_id, tokenizer.pause_id, tokenizer.skip_id, tokenizer.eod_id]\n", "\n", "def find(tokens, target_token):\n", "    if target_token in tokens:\n", "        return np.argwhere(tokens == target_token)[0, 0]\n", "    else:\n", "        return None\n", "\n", "def cut_tokens_by_stop_token(tokens):\n", "    position = None\n", "    for stop_token_id in STOP_TOKEN_IDS:\n", "        current_position = find(tokens, stop_token_id)\n", "        if current_position is not None:\n", "            if position is None:\n", "                position = current_position\n", "            else:\n", "                position = min(current_position, position)\n", "    if position is not None:\n", "        return tokens[:position + 1]\n", "    else:\n", "        return tokens\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    \"\"\"Main.\"\"\"\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--dataset\", type=str, required=True, help=\"indexed dataset path\"\n", "    )\n", "    args = parser.parse_args()\n", "\n", "    more_args = SimpleNamespace(\n", "        output_prefix=\"doc\",\n", "        dataset_impl=\"mmap\",\n", "        rank=0,\n", "        tokenizer_type=\"CodeGenTokenizer\",\n", "        make_vocab_size_divisible_by=128,\n", "        model_parallel_size=1,\n", "    )\n", "\n", "    tokenizer = build_tokenizer(more_args)\n", "\n", "    dataset = indexed_dataset.make_dataset(args.dataset, \"infer\")\n", "\n", "    for item in dataset[:10]:\n", "        print(item)\n", "        print(len(item))\n", "        print(tokenizer.detokenize(item))\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
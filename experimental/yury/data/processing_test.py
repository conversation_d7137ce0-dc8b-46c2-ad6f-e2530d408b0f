import pytest
from unittest.mock import MagicMock, patch
from experimental.yury.data.processing import (
    CachingPool,
    PersistentJSONFileCache,
    persistent_cache,
)
from dataclasses import dataclass


def square(x):
    return x * x


@dataclass
class InnerData:
    value: int


@dataclass
class OuterData:
    inner: InnerData
    values: list[InnerData]
    mapping: dict[str, InnerData]


def process_nested(data: OuterData) -> int:
    return (
        data.inner.value
        + sum(v.value for v in data.values)
        + sum(v.value for v in data.mapping.values())
    )


@pytest.fixture
def pool_with_mocked_cache(tmp_path):
    cache_file = tmp_path / "cache.json"
    with patch.object(PersistentJSONFileCache, "__init__", return_value=None):
        cache = PersistentJSONFileCache(cache_file)
        cache.cache = {}
        cache.cache_path = cache_file
        # Store the original _construct_key method
        cache._construct_key = PersistentJSONFileCache._construct_key.__get__(
            cache, PersistentJSONFileCache
        )
        cache.exists = MagicMock(
            side_effect=lambda *args, **kwargs: cache._construct_key(args, kwargs)
            in cache.cache
        )
        cache.get = MagicMock(
            side_effect=lambda *args, **kwargs: cache.cache[
                cache._construct_key(args, kwargs)
            ]
        )
        cache.add = MagicMock(
            side_effect=lambda value, *args, **kwargs: cache.cache.update(
                {cache._construct_key(args, kwargs): value}
            )
        )
        pool = CachingPool(processes=4, cache_file=cache_file)
        pool.cache = cache
        yield pool
        pool.close()
        pool.join()


@pytest.fixture
def pool_without_cache():
    pool = CachingPool(processes=4)
    yield pool
    pool.close()
    pool.join()


def test_cached_results(pool_with_mocked_cache):
    # Pre-populate cache with known results
    pool_with_mocked_cache.cache.cache = {1: 1, 2: 4}

    results = list(pool_with_mocked_cache.imap_unordered(square, [1, 2, 3]))
    expected_results = {1, 4, 9}
    assert set(results) == expected_results
    # Check that the cache methods are called correctly
    pool_with_mocked_cache.cache.exists.assert_any_call(1)
    pool_with_mocked_cache.cache.exists.assert_any_call(2)
    pool_with_mocked_cache.cache.exists.assert_any_call(3)
    pool_with_mocked_cache.cache.get.assert_any_call(1)
    pool_with_mocked_cache.cache.get.assert_any_call(2)
    assert pool_with_mocked_cache.cache.add.call_count == 1  # only for input 3


def test_no_cache(pool_without_cache):
    # This should simply compute the square of numbers without caching
    results = list(pool_without_cache.imap_unordered(square, range(1, 4)))
    expected_results = {1, 4, 9}
    assert set(results) == expected_results


def test_adding_new_results_to_cache(pool_with_mocked_cache):
    # Assume the cache is initially empty
    pool_with_mocked_cache.cache.cache = {}

    results = list(pool_with_mocked_cache.imap_unordered(square, [4, 5]))
    expected_results = {16, 25}
    assert set(results) == expected_results
    # Verify new results are added to cache
    pool_with_mocked_cache.cache.add.assert_any_call(16, 4)
    pool_with_mocked_cache.cache.add.assert_any_call(25, 5)


def test_nested_dataclass_caching(pool_with_mocked_cache):
    data = OuterData(
        inner=InnerData(1),
        values=[InnerData(2), InnerData(3)],
        mapping={"a": InnerData(4), "b": InnerData(5)},
    )

    results = list(pool_with_mocked_cache.imap_unordered(process_nested, [data]))
    expected = 15  # 1 + 2 + 3 + 4 + 5
    assert results[0] == expected

    # Run again to test cache hit
    results2 = list(pool_with_mocked_cache.imap_unordered(process_nested, [data]))
    assert results2[0] == expected

    # Verify cache was used
    pool_with_mocked_cache.cache.exists.assert_called_with(data)
    pool_with_mocked_cache.cache.get.assert_called_with(data)
    assert pool_with_mocked_cache.cache.add.call_count == 1  # Called only on first run


def test_modified_nested_dataclass(pool_with_mocked_cache):
    data1 = OuterData(
        inner=InnerData(1),
        values=[InnerData(2), InnerData(3)],
        mapping={"a": InnerData(4)},
    )

    data2 = OuterData(
        inner=InnerData(1),
        values=[InnerData(2), InnerData(3)],
        mapping={"a": InnerData(5)},  # Different value
    )

    results = list(
        pool_with_mocked_cache.imap_unordered(process_nested, [data1, data2])
    )
    assert set(results) == {10, 11}  # First sum is 10 (1+2+3+4), second is 11 (1+2+3+5)

    # Verify both entries were cached
    assert pool_with_mocked_cache.cache.add.call_count == 2


def test_identical_nested_dataclass(pool_with_mocked_cache):
    data1 = OuterData(
        inner=InnerData(1), values=[InnerData(2)], mapping={"a": InnerData(3)}
    )

    # Identical structure and values
    data2 = OuterData(
        inner=InnerData(1), values=[InnerData(2)], mapping={"a": InnerData(3)}
    )

    results = list(
        pool_with_mocked_cache.imap_unordered(process_nested, [data1, data2])
    )
    assert results == [6, 6]  # Both should be 1+2+3=6

    # Verify cache was used for second identical input
    assert pool_with_mocked_cache.cache.add.call_count == 1


def test_persistent_cache_basic_dataclass(tmp_path):
    cache_file = tmp_path / "cache.json"
    call_count = 0

    @persistent_cache(cache_file)
    def process_inner(data: InnerData) -> int:
        nonlocal call_count
        call_count += 1
        return data.value * 2

    data = InnerData(5)

    # First call should execute the function
    assert process_inner(data) == 10
    assert call_count == 1

    # Second call should use cache
    assert process_inner(data) == 10
    assert call_count == 1

    # Different value should execute function
    assert process_inner(InnerData(6)) == 12
    assert call_count == 2


def test_persistent_cache_nested_dataclass(tmp_path):
    cache_file = tmp_path / "cache.json"
    call_count = 0

    @persistent_cache(cache_file)
    def process_outer(data: OuterData) -> int:
        nonlocal call_count
        call_count += 1
        return process_nested(data)

    data = OuterData(
        inner=InnerData(1),
        values=[InnerData(2), InnerData(3)],
        mapping={"a": InnerData(4), "b": InnerData(5)},
    )

    # First call should execute the function
    assert process_outer(data) == 15  # 1 + 2 + 3 + 4 + 5
    assert call_count == 1

    # Second call should use cache
    assert process_outer(data) == 15
    assert call_count == 1

    # Modified data should execute function
    data2 = OuterData(
        inner=InnerData(1),
        values=[InnerData(2), InnerData(3)],
        mapping={"a": InnerData(4), "b": InnerData(6)},  # Changed 5 to 6
    )
    assert process_outer(data2) == 16  # 1 + 2 + 3 + 4 + 6
    assert call_count == 2


def test_persistent_cache_list_of_dataclasses(tmp_path):
    cache_file = tmp_path / "cache.json"
    call_count = 0

    @persistent_cache(cache_file)
    def sum_values(data_list: list[InnerData]) -> int:
        nonlocal call_count
        call_count += 1
        return sum(d.value for d in data_list)

    data_list = [InnerData(1), InnerData(2), InnerData(3)]

    # First call should execute the function
    assert sum_values(data_list) == 6
    assert call_count == 1

    # Second call should use cache
    assert sum_values(data_list) == 6
    assert call_count == 1

    # Different list should execute function
    data_list2 = [InnerData(1), InnerData(2), InnerData(4)]  # Changed 3 to 4
    assert sum_values(data_list2) == 7
    assert call_count == 2


def test_persistent_cache_file_persistence(tmp_path):
    cache_file = tmp_path / "cache.json"
    call_count = 0

    @persistent_cache(cache_file)
    def process_data(data: OuterData) -> int:
        nonlocal call_count
        call_count += 1
        return process_nested(data)

    data = OuterData(
        inner=InnerData(1),
        values=[InnerData(2)],
        mapping={"a": InnerData(3)},
    )

    # First call should execute and cache
    result1 = process_data(data)
    assert result1 == 6  # 1 + 2 + 3
    assert call_count == 1

    # Create new cache instance (simulates new process/run)
    call_count = 0

    @persistent_cache(cache_file)
    def process_data2(data: OuterData) -> int:
        nonlocal call_count
        call_count += 1
        return process_nested(data)

    # Should use cached value from file
    result2 = process_data2(data)
    assert result2 == 6
    assert call_count == 0  # Should not call function

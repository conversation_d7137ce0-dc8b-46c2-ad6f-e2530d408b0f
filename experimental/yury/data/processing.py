import functools
import json
import multiprocessing.pool
import os
from pathlib import Path


class PersistentJSONFileCache:
    """A simple JSON-based file cache system for persistently caching results."""

    def __init__(self, filename):
        self.cache = {}
        self.cache_path = Path(filename)

        if self.cache_path.is_file():
            with self.cache_path.open("r", encoding="utf8") as f:
                for line in f:
                    datum = json.loads(line[:-1])
                    self.cache[datum["key"]] = datum["value"]

    def _construct_key(self, args, kwargs):
        def serialize_value(v):
            if hasattr(v, "__dataclass_fields__"):
                return {
                    k: serialize_value(getattr(v, k)) for k in v.__dataclass_fields__
                }
            elif isinstance(v, (list, tuple)):
                return [serialize_value(x) for x in v]
            elif isinstance(v, dict):
                return {k: serialize_value(val) for k, val in v.items()}
            return v

        # For simple single argument case, use the value directly
        if (
            len(args) == 1
            and not kwargs
            and isinstance(args[0], (int, float, str, bool))
        ):
            return args[0]

        # For complex cases, serialize everything
        serializable_args = [serialize_value(arg) for arg in args]
        serializable_kwargs = {k: serialize_value(v) for k, v in kwargs.items()}
        key = json.dumps((serializable_args, serializable_kwargs), sort_keys=True)
        return key

    def exists(self, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        return key in self.cache

    def get(self, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        return self.cache[key]

    def add(self, value, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        self.cache[key] = value
        with self.cache_path.open("a", encoding="utf8") as f:
            f.write(json.dumps({"key": key, "value": value}) + "\n")


def persistent_cache(filename):
    """Decorator that caches the results of a function call in a file.

    # Example usage
    @persistent_cache('/tmp/my_cache.jsonl')
    def my_function(x):
        print("EXECUTING FOR", x)
        return x ** 2

    my_function(3)
    > EXECUTING FOR 3
    my_function(3)
    my_function(4)
    > EXECUTING FOR 4
    my_function(4)

    Args:
        filename (str): Path to the cache file.
    """

    def decorator(func):
        cache = PersistentJSONFileCache(filename)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if cache.exists(*args, **kwargs):
                return cache.get(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
                cache.add(result, *args, **kwargs)
                return result

        return wrapper

    return decorator


class CachingPool(multiprocessing.pool.Pool):
    """A multiprocessing pool that caches results.

    This class extends the multiprocessing.pool.Pool class to include a cache for
    storing the results of function calls. The cache is used to avoid re-computing
    results that have already been computed.

    Args:
        processes (int): The number of worker processes to use. If None, then the
            number returned by os.cpu_count() is used.
        cache_file (str): The path to the file where the cache will be stored.
            If None, then no cache will be used.
    """

    def __init__(self, *args, cache_file=None, **kwargs):
        self.cache = PersistentJSONFileCache(cache_file) if cache_file else None
        self.processes = kwargs.get("processes", os.cpu_count())
        if self.processes > 1:
            super().__init__(*args, **kwargs)
        else:
            self._worker = None

    def imap_unordered(self, func, iterable, chunksize=1):
        if self.processes > 1:
            if self.cache:
                new_iterable = []
                for item in iterable:
                    if not self.cache.exists(item):
                        new_iterable.append(item)

                result_generator = super().imap_unordered(func, new_iterable, chunksize)
                for item in iterable:
                    if self.cache and self.cache.exists(item):
                        yield self.cache.get(item)
                    else:
                        result = next(result_generator)
                        if self.cache:
                            self.cache.add(result, item)
                        yield result
            else:
                # Make sure to iterate over results from the superclass method
                yield from super().imap_unordered(func, iterable, chunksize)
        else:
            for item in iterable:
                if self.cache and self.cache.exists(item):
                    yield self.cache.get(item)
                else:
                    result = func(item)
                    if self.cache:
                        self.cache.add(result, item)
                    yield result

    def close(self):
        if self.processes > 1:
            super().close()

    def join(self):
        if self.processes > 1:
            super().join()

    def terminate(self):
        if self.processes > 1:
            super().terminate()


# Example usage
if __name__ == "__main__":

    def square(x):
        print("SQUAERING:", x)
        return x * x

    # Using caching
    pool_with_cache = CachingPool(processes=4, cache_file="my_cache.json")
    print("With cache:")
    for result in pool_with_cache.imap_unordered(square, range(10)):
        print(f"Result: {result}")
    pool_with_cache.close()
    pool_with_cache.join()

    # Without caching
    print("\nWithout cache:")
    pool_no_cache = CachingPool(processes=4)
    for result in pool_no_cache.imap_unordered(square, range(10)):
        print(f"Result: {result}")
    pool_no_cache.close()
    pool_no_cache.join()

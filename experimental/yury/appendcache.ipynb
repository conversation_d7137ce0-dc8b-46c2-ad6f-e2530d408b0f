{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Optional\n", "import numpy as np\n", "import dataclasses\n", "from datetime import datetime\n", "import tqdm\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.datetime(2024, 3, 8, 18, 56, 9, 595356)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["@dataclasses.dataclass(frozen=True)\n", "class Blob:\n", "    path: str\n", "    blob_name: Optional[str] = None\n", "    char_end: Optional[int] = None    \n", "    char_offset: Optional[int] = None\n", "    chunk_index: Optional[int] = None\n", "\n", "@dataclasses.dataclass\n", "class RetrievalRequest:\n", "    session_id: str\n", "    user_id: str\n", "    request_id: str\n", "    event_time: datetime\n", "    blobs: list[Blob]\n", "\n", "@dataclasses.dataclass\n", "class Session:\n", "    session_id: str\n", "    requests: list[RetrievalRequest]\n", "\n", "def parse_date(date_string):\n", "    return datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S.%f UTC')\n", "\n", "parse_date(\"2024-03-08 18:56:09.595356 UTC\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["97164it [00:08, 11013.54it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 97164 requests. Failed to parse 0 blobs in 0 requests. 1100 requests had too few blobs\n"]}], "source": ["# 1. First attempt\n", "# PATH = \"/home/<USER>/data/bq-results-20240318-233545-1710804964475.jsonl\"\n", "# 2. keep only SUCCESSDED completion request\n", "# PATH = \"/home/<USER>/data/bq-results-20240320-221240-1710972779026.jsonl\"\n", "# 3. Remove health-check-1 and review-edit-bot users \n", "PATH = \"/home/<USER>/data/bq-results-20240320-224000-1710974632734.jsonl\"\n", "\n", "requests = []\n", "n_bloken_blobs = 0\n", "n_requests_with_broken_blobs = 0\n", "n_too_few_blobs = 0\n", "\n", "with open(PATH) as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line[:-1])\n", "        blobs = [Blob(**b) for b in datum[\"filtered_json_array\"]]\n", "        # blobs = [Blob(**b) for b in datum[\"filtered_json_array\"] if \"blob_name\" in b and \"char_end\" in b]\n", "        # n_bloken_blobs += len(datum[\"filtered_json_array\"]) - len(blobs)\n", "        # n_requests_with_broken_blobs += int(len(datum[\"filtered_json_array\"]) > len(blobs))\n", "        # if len(datum[\"filtered_json_array\"]) > len(blobs):\n", "        #     continue\n", "        if len(blobs) < 32:\n", "            n_too_few_blobs += 1\n", "            # continue\n", "        request = RetrievalRequest(\n", "            session_id=datum[\"session_id\"],\n", "            user_id=datum[\"user_id\"],\n", "            request_id=datum[\"request_id\"],\n", "            event_time=parse_date(datum[\"event_time\"]),\n", "            blobs=blobs,\n", "        )\n", "        requests.append(request)\n", "\n", "requests.sort(key = lambda x: (x.session_id, x.event_time))\n", "\n", "print('Loaded %d requests. Failed to parse %d blobs in %d requests. %d requests had too few blobs' % (len(requests), n_bloken_blobs, n_requests_with_broken_blobs, n_too_few_blobs))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 48 sessions\n", "[    0  3024   584   875   345   586  4268   334    51    22  3478    89\n", "  4540  1470   424  2931   202  2574  4381   306  1217  7534   490  1000\n", "  7824    43  1290 10425  5621  1729   230   674  2577  1159   944  2113\n", "  1771  4123   519  2288    22  2427   139  1999    38  7414   944   126]\n", "After filtering kept 41 sessions\n"]}], "source": ["sessions = []\n", "\n", "current_session_id = None\n", "current_requests = []\n", "\n", "def flush():\n", "    global current_requests\n", "    global current_session_id\n", "    global sessions\n", "\n", "    for r in current_requests:\n", "        assert r.session_id == current_session_id\n", "    new_session = Session(\n", "        session_id=current_session_id,\n", "        requests=current_requests,\n", "    )\n", "    current_requests = []\n", "    sessions.append(new_session)\n", "\n", "for request in requests:\n", "    if current_session_id != request.session_id:\n", "        flush()\n", "        current_session_id = request.session_id\n", "        current_requests = [request]\n", "    else:\n", "        current_requests.append(request)\n", "flush()\n", "\n", "print('Loaded %d sessions' % len(sessions))\n", "\n", "n_requests_per_session = np.array([len(s.requests) for s in sessions])\n", "print(n_requests_per_session)\n", "\n", "sessions = [s for s in sessions if len(s.requests) > 100]\n", "\n", "print('After filtering kept %d sessions' % len(sessions))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(int,\n", "            {32: 86134,\n", "             64: 209,\n", "             512: 9,\n", "             1: 578,\n", "             2: 175,\n", "             3: 134,\n", "             4: 1,\n", "             7: 7,\n", "             128: 17,\n", "             12: 158,\n", "             38: 191,\n", "             40: 857,\n", "             43: 1171,\n", "             46: 898,\n", "             47: 652,\n", "             42: 1020,\n", "             36: 65,\n", "             37: 350,\n", "             39: 365,\n", "             35: 129,\n", "             44: 1281,\n", "             45: 826,\n", "             53: 14,\n", "             52: 17,\n", "             41: 1055,\n", "             57: 5,\n", "             51: 44,\n", "             49: 69,\n", "             48: 326,\n", "             54: 2,\n", "             56: 10,\n", "             50: 43,\n", "             55: 12,\n", "             33: 32,\n", "             34: 18,\n", "             256: 10,\n", "             26: 2,\n", "             61: 2,\n", "             63: 4,\n", "             14: 7})"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "\n", "# Assuming `sessions` is your list of Session objects\n", "blob_counts = defaultdict(int)  # Initialize a dictionary to count the number of requests with each number of blobs\n", "for session in sessions:\n", "    for request in session.requests:\n", "        blob_counts[len(request.blobs)] += 1\n", "\n", "# Now `blob_counts` is a dictionary where the keys are the number of blobs and the values are the counts of requests with that many blobs\n", "        \n", "blob_counts"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.332558372219626, 0.63851504192827, 0.9277252763454154, 1.206537276124815, 1.472611074040936, 1.7302835555910174, 1.9809359626258807, 2.2290579723698847, 2.4637869185439043, 2.7060216628685603, 2.9394063565508994, 3.165268943025466, 3.390919194213973, 3.607765263166069, 3.823135762389074, 4.031103355264916, 4.224374650757165, 4.412104827018818, 4.612805140929253, 4.810997225204886, 5.005081039202288, 5.206954761752745, 5.4027163260026825, 5.603823275085349, 5.799252839752597, 5.9960608314765516, 6.195149546801395, 6.390302221361223, 6.578610338568554, 6.774822422786319, 6.966125995439726, 7.162604629828633]\n"]}], "source": ["def get_missing_blobs(session, top_k):\n", "    n_missing_blobs = []\n", "    for index in range(1, len(session.requests)):\n", "        request1 = session.requests[index - 1]\n", "        request2 = session.requests[index]\n", "        n_missing_blobs.append(len(set(request1.blobs[:top_k]).difference(request2.blobs[:top_k])))\n", "\n", "    n_missing_blobs = np.array(n_missing_blobs)\n", "    return n_missing_blobs.mean()\n", "\n", "avg_missing_blobs_per_topk = []\n", "\n", "for k in range(1, 32 + 1):\n", "    avg_missing_blobs_per_topk_per_session = []\n", "    for session in sessions:\n", "        avg_missing_blobs_per_topk_per_session.append(get_missing_blobs(session, k))\n", "    avg_missing_blobs_per_topk.append(np.array(avg_missing_blobs_per_topk_per_session).mean())\n", "\n", "print(avg_missing_blobs_per_topk)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "x_values = list(range(1, len(avg_missing_blobs_per_topk) + 1))\n", "\n", "# Creating the dot plot\n", "plt.figure(figsize=(8, 4)) # Adjust the figure size as needed\n", "plt.plot(x_values, avg_missing_blobs_per_topk, '-o', color='blue') # Connect dots with lines\n", "\n", "# Customization options\n", "plt.title('How many TopK retrievals were NOT in the TopK retrievals of the previous request') # Add a title\n", "plt.xlabel('K, number of top retrievals to consider') # X-axis label\n", "plt.ylabel('Average number of missing retrievals') # Y-axis label\n", "plt.grid(True, which='both', linestyle='--', linewidth=0.5) # Add a grid for better readability\n", "plt.xticks(x_values) # Ensure all x values are marked\n", "plt.tight_layout() # Adjust layout to not cut off anything\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 46/46 [00:34<00:00,  1.35it/s]\n", "100%|██████████| 41/41 [00:40<00:00,  1.01it/s]\n", "100%|██████████| 31/31 [00:46<00:00,  1.49s/it]\n"]}], "source": ["class AppendCacheHardReset:\n", "\n", "    def __init__(self, budget, guranteed_top_k = None):\n", "        self.budget = budget\n", "        self.guranteed_top_k = guranteed_top_k\n", "        self.blobs = []\n", "\n", "    def process(self, new_blobs):\n", "        # actually_new_blobs = list(set(self.blobs).difference(new_blobs))\n", "        actually_new_blobs = list(set(new_blobs).difference(self.blobs))\n", "        if len(actually_new_blobs) == 0:\n", "            return 0\n", "        if len(self.blobs) + len(actually_new_blobs) <= self.budget:\n", "            self.blobs.extend(actually_new_blobs)\n", "            return len(actually_new_blobs)\n", "        same_prefix = 0\n", "        for index in range(min(len(self.blobs), len(new_blobs))):\n", "            if self.blobs[index] == new_blobs[index]:\n", "                same_prefix += 1\n", "            else:\n", "                break\n", "        self.blobs = self.blobs[:same_prefix] + new_blobs[same_prefix:]\n", "        assert len(self.blobs) <= self.budget\n", "        return len(new_blobs) - same_prefix\n", "        \n", "\n", "def simulate(top_k, budget, AppendCacheCls):\n", "    avg_n_process_per_session = []\n", "    for session in sessions:    \n", "        cache = AppendCacheCls(budget)\n", "        n_process = []\n", "        for request in session.requests:\n", "            n_process.append(cache.process(request.blobs[:top_k]))\n", "        # print(n_process)\n", "        avg_n_process_per_session.append(np.array(n_process).mean())\n", "    return np.array(avg_n_process_per_session).mean()\n", "\n", "df = []\n", "for top_k in [5, 10, 20]:\n", "    for budget in tqdm.tqdm(list(range(max(5, top_k), 50 + 1))):\n", "        df.append({\n", "            'top_k': top_k,\n", "            'budget': budget,\n", "            'avg_blobs_to_process': simulate(top_k, budget, AppendCacheHardReset)\n", "        })\n", "\n", "df = pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Assuming df is your DataFrame and 'top_k', 'budget', and 'avg_blobs_to_process' are the column names in your dataframe\n", "plt.figure(figsize=(10, 6))\n", "for top_k in df['top_k'].unique():\n", "    subset = df[df['top_k'] == top_k]\n", "    plt.plot(subset['budget'], subset['avg_blobs_to_process'], label=f'Top K: {top_k}', linewidth=2, marker='o')  # Add points and make lines thicker\n", "plt.title('Average Blobs to Process vs Budget for Different Top K Values', fontsize=16)  # Increase title size\n", "plt.xlabel('Budget, max number of retrieved blobs in input', fontsize=14)  # Increase x-axis label size\n", "plt.ylabel('Avg Blobs to Process', fontsize=14)  # Increase y-axis label size\n", "plt.legend(loc='best', title='Top K Value')  # Add a legend\n", "plt.grid(True, linestyle=':')  # Add grid lines for better readability with dotted style\n", "\n", "# Set x-ticks more frequently\n", "x_ticks = range(int(df['budget'].min()), int(df['budget'].max())+1, 5)  # Generate ticks from min to max budget value\n", "plt.xticks(x_ticks, fontsize=12)  # Increase x-axis tick label size\n", "\n", "plt.yticks(fontsize=12)  # Increase y-axis tick label size\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AppendCacheHardResetTopK:\n", "\n", "    def __init__(self, budget, top_k):\n", "        self.budget = budget\n", "        self.blobs = []\n", "        self.top_k = \n", "\n", "    def process(self, new_blobs):\n", "        # actually_new_blobs = list(set(self.blobs).difference(new_blobs))\n", "        actually_new_blobs = list(set(new_blobs).difference(self.blobs))\n", "        if len(actually_new_blobs) == 0:\n", "            return 0\n", "        if len(self.blobs) + len(actually_new_blobs) <= self.budget:\n", "            self.blobs.extend(actually_new_blobs)\n", "            return len(actually_new_blobs)\n", "        same_prefix = 0\n", "        for index in range(min(len(self.blobs), len(new_blobs))):\n", "            if self.blobs[index] == new_blobs[index]:\n", "                same_prefix += 1\n", "            else:\n", "                break\n", "        self.blobs = self.blobs[:same_prefix] + new_blobs[same_prefix:]\n", "        assert len(self.blobs) <= self.budget\n", "        return len(new_blobs) - same_prefix\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import torch\n", "import random"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVOL_CODEALPACA = \"/mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.jsonl\"\n", "\n", "evol_codealpaca = []\n", "with open(EVOL_CODEALPACA) as f:\n", "    for line in f:\n", "        evol_codealpaca.append(json.loads(line[:-1]))\n", "\n", "random.shuffle(evol_codealpaca)\n", "\n", "print('Loaded %d samples from %s' % (len(evol_codealpaca), EVOL_CODEALPACA))\n", "for k, v in evol_codealpaca[0].items():\n", "    print(\"%s: %s\" % (k, v))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>I'm <PERSON><PERSON>, an AI programming assistant from Augment Inc., integrated into the VSCode editor since 2024. My expertise covers all areas of computer science including programming, algorithms, data structures, and software engineering, making me an excellent resource for software engineering queries. Here’s how I can help:\n", "\n", "- I focus on computer science topics, from programming languages and methodologies to computational theories.\n", "- When you have questions, I provide clear and relevant code examples, keeping things straightforward unless you need more context.\n", "- Depending on your question's complexity, I'll keep my answers brief or go into detailed explanations.\n", "- Feel free to share code snippets from your project for better context. If your question relates to something in that snippet, I'll point you to the exact file path for further guidance.\n", "\n", "With a strong background in software engineering, I'm here to offer you top-notch support and insights for your programming needs.\n", "{%- for previous_message in chat_history[:-1] %}\n", "### Instruction:\n", "{{previous_message['request_message']}}\n", "### Response:\n", "{{previous_message['response_text']}}\n", "<|EOT|>\n", "{%- endfor %}\n", "### Instruction:\n", "{{chat_history[-1]['request_message']}}\n", "### Response:\n", "{{chat_history[-1]['response_text']}}\n", "<|EOT|>\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter\n", "from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer\n", "from research.core.model_input import ModelInput\n", "from research.model_server.model_server_requests import Exchnage\n", "from jinja2 import Environment, Template\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "env = Environment(keep_trailing_newline=True)\n", "template = env.from_string(TEMPLATE)\n", "\n", "MAX_TOKENS_LENGTH = 16384\n", "\n", "def render(samples, instruction_key, response_key):\n", "    chat_history = [\n", "        Exchnage(\n", "            request_message=r[instruction_key],\n", "            response_text=r[response_key],\n", "        )\n", "        for r in samples\n", "    ]\n", "    text = template.render(\n", "        chat_history=chat_history\n", "    )\n", "    tokens = tokenizer.tokenize(text)\n", "    assert len(tokens) < MAX_TOKENS_LENGTH\n", "    return tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_evol_codealpaca(samples):\n", "    return render(samples, \"instruction\", \"output\")\n", "\n", "print(tokenizer.detokenize(render_evol_codealpaca(evol_codealpaca[0:2])))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evol_codealpaca_tensors = []\n", "\n", "# total is 111272, we only need 512 * 10\n", "\n", "index = 0\n", "while index < len(evol_codealpaca[:(512 * 10)]):\n", "    end_index = min(index + random.randint(1, 10), len(evol_codealpaca))\n", "    samples = evol_codealpaca[index: end_index]\n", "    assert len(samples) > 0\n", "    tensors = render_evol_codealpaca(samples)\n", "    index = end_index\n", "    evol_codealpaca_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(evol_codealpaca_tensors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GLAIVE = \"/mnt/efs/augment/user/yury/theblackcat102/glaive_code_assistant/c9bc9129-eba0-4b10-8292-4ae70fc7fa0d.json\"\n", "\n", "with open(GLAIVE) as f:\n", "    glaive = json.load(f)\n", "\n", "random.shuffle(glaive)\n", "\n", "print('Loaded %d samples from %s' % (len(glaive), GLAIVE))\n", "for k, v in glaive[0].items():\n", "    print(\"%s: %s\" % (k, v))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_glaive(samples):\n", "    return render(samples, \"answer\", \"question\")\n", "\n", "print(tokenizer.detokenize(render_glaive(glaive[0:2])))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["glaive_tensors = []\n", "\n", "# 136109 is total, we only need 512 * 10\n", "\n", "index = 0\n", "while index < len(glaive[:(512 * 10)]):\n", "    end_index = min(index + random.randint(1, 10), len(glaive))\n", "    samples = glaive[index: end_index]\n", "    assert len(samples) > 0\n", "    tensors = render_glaive(samples)\n", "    index = end_index\n", "    glaive_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(glaive_tensors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MULTI_ROUND_CONVO = \"/mnt/efs/augment/user/yury/theblackcat102/multiround_programming_convo/train.jsonl\"\n", "\n", "multi_round_convo = []\n", "with open(MULTI_ROUND_CONVO) as f:\n", "    for line in f:\n", "        original_sample = json.loads(line[:-1])\n", "        messages = []\n", "        for index, message in enumerate(original_sample['conversations']):\n", "            if index % 2 == 0:\n", "                assert message['from'] == 'human'\n", "                messages.append({\n", "                    \"request_message\": message[\"text\"],\n", "                })\n", "            else:\n", "                assert message['from'] == 'assistant'\n", "                messages[-1].update({\n", "                    \"response_text\": message[\"text\"],\n", "                })\n", "        multi_round_convo.append(messages)\n", "\n", "random.shuffle(multi_round_convo)\n", "\n", "print('Loaded %d samples from %s' % (len(multi_round_convo), MULTI_ROUND_CONVO))\n", "for k in multi_round_convo[0]:\n", "    print(k)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_multi_round_convo(sample):\n", "    return render(sample, \"request_message\", \"response_text\")\n", "\n", "print(tokenizer.detokenize(render_multi_round_convo(multi_round_convo[0])))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["multi_round_convo_tensors = []\n", "\n", "index = 0\n", "while index < len(multi_round_convo[:512]):    \n", "    tensors = render_multi_round_convo(multi_round_convo[index])\n", "    index += 1\n", "    multi_round_convo_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(multi_round_convo_tensors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from megatron.data import indexed_dataset\n", "\n", "builder = indexed_dataset.make_builder(\n", "    \"/tmp/chat_calibration_data.bin\",\n", "    impl=\"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")\n", "\n", "counter = 0\n", "for tensor in evol_codealpaca_tensors + glaive_tensors + multi_round_convo_tensors:\n", "    builder.add_item(torch.tensor(tensor))\n", "    counter += 1\n", "\n", "builder.finalize(\"/tmp/chat_calibration_data.idx\")\n", "\n", "print('Saved %d samples' % counter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = \"/tmp/chat_calibration_data\"\n", "\n", "ds = indexed_dataset.make_dataset(\n", "    path, impl=\"mmap\", skip_warmup=True\n", ")\n", "print(len(ds))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "print(highlight_special_tokens(tokenizer.detokenize(ds[0]), tokenizer.all_special_tokens()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
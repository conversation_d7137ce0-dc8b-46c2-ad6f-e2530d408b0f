{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "LAST_K_DAYS = 7\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/chat/download_request.jsonl\")\n", "def download_request(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    return row.raw_json\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/chat/download_response.jsonl\")\n", "def download_response(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "    return row.raw_json[\"response\"]\n", "\n", "\n", "REQUEST_ID = \"f1e3ba2d-a6a3-4571-9e8a-17380a1105d4\"\n", "# request = download_request(REQUEST_ID)\n", "# response = download_response(REQUEST_ID)\n", "\n", "\n", "query = f\"\"\"SELECT\n", "            *\n", "        FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "        WHERE request_id = '{REQUEST_ID}'\"\"\"\n", "\n", "gcp_creds, _ = get_gcp_creds(None)\n", "bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "print(\"sending request to big query\")\n", "rows = bigquery_client.query_and_wait(query, page_size=1)\n", "rows = list(rows)\n", "# assert len(rows) == 1\n", "# row = rows[0]\n", "# assert row.request_id == request_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import Exchange\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "\n", "def build_chat_history(request: dict) -> list[Exchange]:\n", "    chat_history = []\n", "    for exchange_json in request.get(\"chat_history\", []):\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=exchange_json[\"request_message\"],\n", "                response_text=exchange_json[\"response_text\"],\n", "                request_id=exchange_json.get(\"request_id\"),\n", "            )\n", "        )\n", "    return chat_history\n", "\n", "\n", "def build_retrievals(request) -> list[PromptChunk]:\n", "    prompt_chunks = []\n", "    for chunk in request[\"retrieved_chunks\"]:\n", "        if \"blob_name\" not in chunk:\n", "            continue\n", "        char_offset = chunk[\"char_offset\"] if \"char_offset\" in chunk else 0\n", "        chunk_index = chunk[\"chunk_index\"] if \"chunk_index\" in chunk else 0\n", "        chunk_index = str(chunk_index)\n", "        prompt_chunk = PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            unique_id=chunk[\"blob_name\"] + \"-\" + chunk_index,\n", "            origin=chunk[\"origin\"],\n", "            char_start=char_offset,\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "        )\n", "        prompt_chunks.append(prompt_chunk)\n", "    return prompt_chunks\n", "\n", "\n", "def build_chat_prompt_input(full_request: dict) -> ChatPromptInput:\n", "    request = full_request[\"request\"]\n", "    selected_code = request.get(\"selected_code\", \"\")\n", "    prefix = request.get(\"prefix\", \"\")\n", "    suffix = request.get(\"suffix\", \"\")\n", "    full_file = prefix + selected_code + suffix\n", "\n", "    return ChatPromptInput(\n", "        message=request[\"message\"],\n", "        path=request.get(\"path\", \"\"),\n", "        prefix=prefix,\n", "        selected_code=request.get(\"selected_code\", \"\"),\n", "        suffix=suffix,\n", "        chat_history=build_chat_history(request),\n", "        prefix_begin=0,\n", "        suffix_end=len(full_file),\n", "        retrieved_chunks=build_retrievals(full_request),\n", "        context_code_exchange_request_id=request.get(\n", "            \"context_code_exchange_request_id\"\n", "        ),\n", "        recent_changes=request.get(\"recent_changes\"),\n", "        user_guided_blobs=request.get(\"user_guided_blobs\", []),\n", "    )\n", "\n", "\n", "def download_and_build_chat_prompt_input(request_id: str) -> ChatPromptInput:\n", "    request = download_request(request_id)\n", "    # response = download_response(request_id)\n", "    return build_chat_prompt_input(request)\n", "\n", "\n", "REQUEST_ID = \"6c83ae04-06a6-4a64-8c12-f7593d4eef7a\"\n", "\n", "chat_prompt_input = download_and_build_chat_prompt_input(REQUEST_ID)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "# See services/deploy/claude_sonnet_3_5_16k_chat_deploy.jsonnet\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# MODEL_NAME = \"research-model-firefly-v2\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"write a factorial function\"},\n", "]\n", "\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=dialog,\n", "    temperature=TEMPERAURE,\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "\n", "token_apportionment = ChatTokenApportionment(\n", "    prefix_len=1024,\n", "    suffix_len=1024,\n", "    path_len=256,\n", "    message_len=-1,\n", "    selected_code_len=-1,\n", "    chat_history_len=4096,\n", "    retrieval_len_per_each_user_guided_file=3000,\n", "    retrieval_len_for_user_guided=8000,\n", "    retrieval_len=-1,\n", "    max_prompt_len=1024 * 12,\n", ")\n", "\n", "TOKEN_COUNTER = ClaudeTokenCounter()\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(\n", "    TOKEN_COUNTER, token_apportionment\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", ")\n", "\n", "# REQUEST_ID = \"6c83ae04-06a6-4a64-8c12-f7593d4eef7a\" # <PERSON><PERSON>'s sample https://augment-wic8570.slack.com/archives/C07C9BULS90/p1728496770937069\n", "REQUEST_ID = \"f1e3ba2d-a6a3-4571-9e8a-17380a1105d4\"  # <PERSON><PERSON><PERSON>'s sample https://augment-wic8570.slack.com/archives/C07C9BULS90/p1728517370502039\n", "# REQUEST_ID = \"5797c2b3-62ac-44ee-a017-cc53d25459e1\" # <PERSON><PERSON>'s example\n", "MOVE_CURRENT_FILE_TO_RETRIEVALS_EXCHANGE = True\n", "\n", "chat_prompt_input = download_and_build_chat_prompt_input(REQUEST_ID)\n", "\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "messages = []\n", "for exchange in prompt_output.chat_history:\n", "    if (\n", "        \"I have the file\" in exchange.request_message\n", "        and MOVE_CURRENT_FILE_TO_RETRIEVALS_EXCHANGE\n", "    ):\n", "        messages[0][\"content\"] += exchange.request_message\n", "    else:\n", "        messages.extend(\n", "            [\n", "                {\"role\": \"user\", \"content\": exchange.request_message},\n", "                {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "            ]\n", "        )\n", "messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=messages,\n", "    system=prompt_output.system_prompt,\n", "    temperature=TEMPERAURE,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.content[0].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(messages[0][\"content\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import json\n", "import torch\n", "import random"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 111272 samples from /mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.jsonl\n", "instruction: I know I could write scripts and create jobs to run them, but at least some of what I'm wanting it to do is beyond my programming abilities for that to be an option.\n", "\n", "What I'm imagining is something that can run on a regular schedule that will examine all the databases on a server and automatically shrink data and log files (after a backup, of course) when they've reached a file size that contains too much free space.  It would be nice if it could defrag index files when they've become too fragmented as well.\n", "\n", "I guess what I'm probably looking for is a DBA in a box! \n", "\n", "Or it could just be that I need better performance monitoring tools instead.  I know how to take care of both of those issues, but it's more that I forget to check for those issues until I start seeing performance issues with my apps.\n", "output: There is no need to worry. There are multiple automated and intelligent tools available which could help you with this problem. \n", "\n", "- SQL Server Maintenance Solution by <PERSON><PERSON>: This is a solution to schedule optimization jobs, backups, integrity checks, and index and statistics maintenance. The solution consists of several SQL Server scripts which you can customize to fit your needs.\n", "\n", "- IDERA SQL Defrag Manager: This is a powerful index defragmentation tool for SQL Server. It identifies and automates the defragmentation process and provides useful performance statistics.\n", "\n", "- Red Gate SQL Monitor: Distributes SQL Servers that helps a DBA or a team of DBAs monitor and fix issues on their SQL Servers before they come up.\n", "\n", "- SolarWinds Database Performance Analyzer: Helps to pinpoint the root cause of poor performance, provides expert advice, and optimizes performance.\n", "\n", "- SentryOne SQL Sentry: This tool offers robust SQL Server monitoring and advisory functionality. With it, you don’t only find performance issues but also fix them.\n", "\n", "These are the most recommended tools for these types of activities. However, keep in mind that shrinking data files is generally frowned upon on production systems because it can lead to some issues (like fragmentation) and should always be handled with care and deep understanding of the system and application. \n", "\n", "What's better instead of shrinking files is perhaps managing your data better. Perhaps archiving old data to get space back or better index maintenance to make sure everything is running optimally or perhaps configuration changes that can help overall performance. \n", "\n", "Also, keep in mind that these tools are not a replacement for a good DBA, but they are certainly some of the most helpful tools for DBAs to automate and optimize their work. Remember that each tool comes with its own learning curve, so choose the one that best fits your needs.\n"]}], "source": ["EVOL_CODEALPACA = \"/mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.jsonl\"\n", "\n", "evol_codealpaca = []\n", "with open(EVOL_CODEALPACA) as f:\n", "    for line in f:\n", "        evol_codealpaca.append(json.loads(line[:-1]))\n", "\n", "random.shuffle(evol_codealpaca)\n", "\n", "print('Loaded %d samples from %s' % (len(evol_codealpaca), EVOL_CODEALPACA))\n", "for k, v in evol_codealpaca[0].items():\n", "    print(\"%s: %s\" % (k, v))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["TEMPLATE = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>You are Augment, an AI code assistant integrated into the VSCode IDE.\n", "Your role is to help software developers by answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.<|eot_id|>\n", "\n", "{%- for previous_message in chat_history -%}\n", "<|start_header_id|>user<|end_header_id|>\n", "\n", "{{previous_message['request_message']}}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "{{previous_message['response_text']}}<|eot_id|>\n", "{%- endfor -%}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter\n", "from megatron.tokenizer.tokenizer import LLama3InstructTokenizer\n", "from research.core.model_input import ModelInput\n", "from research.model_server.model_server_requests import Exchnage\n", "from jinja2 import Environment, Template\n", "\n", "tokenizer = LLama3InstructTokenizer()\n", "env = Environment(keep_trailing_newline=True)\n", "template = env.from_string(TEMPLATE)\n", "\n", "MAX_TOKENS_LENGTH = 16384\n", "LENGTH_IS_DIVISIBLE_BY = 8\n", "\n", "def render(samples, instruction_key, response_key):\n", "    chat_history = [\n", "        Exchnage(\n", "            request_message=r[instruction_key],\n", "            response_text=r[response_key],\n", "        )\n", "        for r in samples\n", "    ]\n", "    text = template.render(\n", "        chat_history=chat_history\n", "    )\n", "    tokens = tokenizer.tokenize(text)\n", "    while len(tokens) % LENGTH_IS_DIVISIBLE_BY != 0:\n", "        tokens.append(tokenizer.pad_id)\n", "    assert len(tokens) < MAX_TOKENS_LENGTH\n", "    return tokens"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|><|start_header_id|>system<|end_header_id|>You are Augment, an AI code assistant integrated into the VSCode IDE.\n", "Your role is to help software developers by answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "I know I could write scripts and create jobs to run them, but at least some of what I'm wanting it to do is beyond my programming abilities for that to be an option.\n", "\n", "What I'm imagining is something that can run on a regular schedule that will examine all the databases on a server and automatically shrink data and log files (after a backup, of course) when they've reached a file size that contains too much free space.  It would be nice if it could defrag index files when they've become too fragmented as well.\n", "\n", "I guess what I'm probably looking for is a DBA in a box! \n", "\n", "Or it could just be that I need better performance monitoring tools instead.  I know how to take care of both of those issues, but it's more that I forget to check for those issues until I start seeing performance issues with my apps.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "There is no need to worry. There are multiple automated and intelligent tools available which could help you with this problem. \n", "\n", "- SQL Server Maintenance Solution by <PERSON><PERSON>: This is a solution to schedule optimization jobs, backups, integrity checks, and index and statistics maintenance. The solution consists of several SQL Server scripts which you can customize to fit your needs.\n", "\n", "- IDERA SQL Defrag Manager: This is a powerful index defragmentation tool for SQL Server. It identifies and automates the defragmentation process and provides useful performance statistics.\n", "\n", "- Red Gate SQL Monitor: Distributes SQL Servers that helps a DBA or a team of DBAs monitor and fix issues on their SQL Servers before they come up.\n", "\n", "- SolarWinds Database Performance Analyzer: Helps to pinpoint the root cause of poor performance, provides expert advice, and optimizes performance.\n", "\n", "- SentryOne SQL Sentry: This tool offers robust SQL Server monitoring and advisory functionality. With it, you don’t only find performance issues but also fix them.\n", "\n", "These are the most recommended tools for these types of activities. However, keep in mind that shrinking data files is generally frowned upon on production systems because it can lead to some issues (like fragmentation) and should always be handled with care and deep understanding of the system and application. \n", "\n", "What's better instead of shrinking files is perhaps managing your data better. Perhaps archiving old data to get space back or better index maintenance to make sure everything is running optimally or perhaps configuration changes that can help overall performance. \n", "\n", "Also, keep in mind that these tools are not a replacement for a good DBA, but they are certainly some of the most helpful tools for DBAs to automate and optimize their work. Remember that each tool comes with its own learning curve, so choose the one that best fits your needs.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Develop a RESTful API utilizing Flask and SQLAlchemy, which facilitates interaction with the specified database. The database in question is PostgreSQL. Ensure that the API is capable of handling CRUD operations and is well-structured for scalability and maintainability.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "To set up a RESTful API utilizing Flask and SQLAlchemy with a PostgreSQL database, you'll need to have Python installed on your computer along with the following packages: flask, flask_sqlalchemy, flask_marshmallow, marshmallow_sqlalchemy.\n", "\n", "Firstly, install the necessary modules using pip:\n", "\n", "```bash\n", "pip install flask flask_sqlalchemy flask_marshmallow marshmallow_sqlalchemy psycopg2-binary\n", "```\n", "\n", "Now let's start writing some code. I will start by setting up the models and the relationship between them.\n", "\n", "```python\n", "from flask import Flask\n", "from flask_sqlalchemy import SQLAlchemy\n", "from flask_marshmallow import Marshmallow\n", "\n", "app = Flask(__name__)\n", "app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:password@localhost:5432/mydatabase'\n", "db = SQLAlchemy(app)\n", "ma = Marshmallow(app)\n", "\n", "\n", "class User(db.Model):\n", "    id = db.<PERSON><PERSON><PERSON>(db.<PERSON><PERSON><PERSON>, primary_key=True)\n", "    username = db.Column(db.String(80), unique=True, nullable=False)\n", "\n", "    def __init__(self, username):\n", "        self.username = username\n", "\n", "\n", "class UserSchema(ma.SQLAlchemyAutoSchema):\n", "    class Meta:\n", "        model = User\n", "        load_instance = True\n", "\n", "user_schema = UserSchema()\n", "users_schema = UserSchema(many=True)\n", "```\n", "\n", "In the above example, we set up a simple `User` model with an `id` and `username` field.\n", "\n", "Next, we'll set up some RESTful endpoints for creating, reading, updating, and deleting users. We'll use the Flask-Marshmallow library to easily serialize our User objects into JSON.\n", "\n", "```python\n", "from flask import request, jsonify\n", "\n", "@app.route('/user', methods=['POST'])\n", "def add_user():\n", "    username = request.json['username']\n", "    new_user = User(username)\n", "    db.session.add(new_user)\n", "    db.session.commit()\n", "    return user_schema.jsonify(new_user)\n", "\n", "@app.route('/user', methods=['GET'])\n", "def get_users():\n", "    all_users = User.query.all()\n", "    result = users_schema.dump(all_users)\n", "    return jsonify(result.data)\n", "\n", "@app.route('/user/<id>', methods=['GET'])\n", "def get_user(id):\n", "    user = User.query.get(id)\n", "    return user_schema.jsonify(user)\n", "\n", "@app.route('/user/<id>', methods=['PUT'])\n", "def update_user(id):\n", "    user = User.query.get(id)\n", "    username = request.json['username']\n", "    user.username = username\n", "    db.session.commit()\n", "    return user_schema.jsonify(user)\n", "\n", "@app.route('/user/<id>', methods=['DELETE'])\n", "def delete_user(id):\n", "    user = User.query.get(id)\n", "    db.session.delete(user)\n", "    db.session.commit()\n", "    return user_schema.jsonify(user)\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True)\n", "\n", "```\n", "\n", "In the above example, we defined various endpoints for handling CRUD operations on the User resource. POST to '/user' will create a new user. GET to '/user' will return a list of all users. GET, PUT, DELETE to '/user/<id>' will get, update, and delete the specific user respectively.\n", "\n", "Please replace 'user:password' and'mydatabase' to your PostgreSQL username, password, and database name. Likewise, replace 'localhost' and '5432' if your PostgreSQL server is not running on localhost and port 5432.\n", "\n", "Please note that the above is a basic example. For maintainability and scalability, you may want to break down your code into multiple py files, use Flask blueprint for organizing your routes, and use an application factory for the Flask app.<|eot_id|><|eot_id|><|eot_id|>\n"]}], "source": ["def render_evol_codealpaca(samples):\n", "    return render(samples, \"instruction\", \"output\")\n", "\n", "print(tokenizer.detokenize(render_evol_codealpaca(evol_codealpaca[0:2])))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 937 tensors\n"]}], "source": ["evol_codealpaca_tensors = []\n", "\n", "# total is 111272, we only need 512 * 10\n", "\n", "index = 0\n", "while index < len(evol_codealpaca[:(512 * 10)]):\n", "    end_index = min(index + random.randint(1, 10), len(evol_codealpaca))\n", "    samples = evol_codealpaca[index: end_index]\n", "    assert len(samples) > 0\n", "    tensors = render_evol_codealpaca(samples)\n", "    index = end_index\n", "    evol_codealpaca_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(evol_codealpaca_tensors))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 136109 samples from /mnt/efs/augment/user/yury/theblackcat102/glaive_code_assistant/c9bc9129-eba0-4b10-8292-4ae70fc7fa0d.json\n", "question: How can I write a Bash script that prints the current time in UTC?\n", "answer: You can achieve this by using the `date` command in Bash along with the `--utc` flag. Here's an example Bash script:\n", "\n", "```bash\n", "#!/usr/bin/env bash\n", "\n", "current_date_utc=$(date --utc +%Y-%m-%d\\ %T)\n", "echo \"The current time in UTC is: $current_date_utc\"\n", "```\n", "\n", "In this script, we start by declaring a shebang `#!/usr/bin/env bash` to ensure that the script is executed using the Bash interpreter. \n", "\n", "We then assign the output of the `date --utc +%Y-%m-%d\\ %T` command to the `current_date_utc` variable. The `date` command with the `--utc` flag fetches the current date and time in UTC format, while the `+%Y-%m-%d\\ %T` argument specifies the desired output format.\n", "\n", "Finally, we use the `echo` command to display the current time in UTC by referencing the `current_date_utc` variable. When you run this Bash script, it will print the current time in UTC.\n", "\n", "Feel free to modify the script or the output format to fit your specific requirements.\n"]}], "source": ["GLAIVE = \"/mnt/efs/augment/user/yury/theblackcat102/glaive_code_assistant/c9bc9129-eba0-4b10-8292-4ae70fc7fa0d.json\"\n", "\n", "with open(GLAIVE) as f:\n", "    glaive = json.load(f)\n", "\n", "random.shuffle(glaive)\n", "\n", "print('Loaded %d samples from %s' % (len(glaive), GLAIVE))\n", "for k, v in glaive[0].items():\n", "    print(\"%s: %s\" % (k, v))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|><|start_header_id|>system<|end_header_id|>You are Augment, an AI code assistant integrated into the VSCode IDE.\n", "Your role is to help software developers by answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "How can I write a Bash script that prints the current time in UTC?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "You can achieve this by using the `date` command in Bash along with the `--utc` flag. Here's an example Bash script:\n", "\n", "```bash\n", "#!/usr/bin/env bash\n", "\n", "current_date_utc=$(date --utc +%Y-%m-%d\\ %T)\n", "echo \"The current time in UTC is: $current_date_utc\"\n", "```\n", "\n", "In this script, we start by declaring a shebang `#!/usr/bin/env bash` to ensure that the script is executed using the Bash interpreter. \n", "\n", "We then assign the output of the `date --utc +%Y-%m-%d\\ %T` command to the `current_date_utc` variable. The `date` command with the `--utc` flag fetches the current date and time in UTC format, while the `+%Y-%m-%d\\ %T` argument specifies the desired output format.\n", "\n", "Finally, we use the `echo` command to display the current time in UTC by referencing the `current_date_utc` variable. When you run this Bash script, it will print the current time in UTC.\n", "\n", "Feel free to modify the script or the output format to fit your specific requirements.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "In my iPython notebook, I have a markdown table that is currently centered within the cell. The code for the table looks like this:\n", "\n", "```markdown\n", "#### example table\n", "|Name|Description|\n", "|--|-------------------------------|\n", "|Mary |She is a nice girl.|\n", "|Jackie |He is a very naughty boy.|\n", "```\n", "\n", "The output of this table is displayed as the image below:\n", "\n", "![Table Image](https://i.stack.imgur.com/Xllva.png)\n", "\n", "My question is how to:\n", "\n", "1. Left align the table within the cell, since it is currently centered by default.\n", "2. Right align the text in the second column of the table.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "To achieve this, you would need to use HTML within the markdown cell. Markdown itself doesn't support table alignment, but since iPython notebooks support HTML, you can use HTML tags to get the desired alignment. \n", "\n", "1. To left-align the table within the cell, wrap the table in a `div` tag and use the CSS `text-align` property.\n", "\n", "2. To right-align the text in the second column, you can use the `align` attribute within the `td` tag.\n", "\n", "Here is how you can modify your code:\n", "\n", "```html\n", "<div style=\"text-align: left\">\n", "\n", "|Name|Description|\n", "|---|---|\n", "|<div style=\"text-align: left\">Mary</div>|<div style=\"text-align: right\">She is a nice girl.</div>|\n", "|<div style=\"text-align: left\">Jackie</div>|<div style=\"text-align: right\">He is a very naughty boy.</div>|\n", "\n", "</div>\n", "```\n", "\n", "In the above code, the outer `div` tag with `text-align: left` is used to left-align the entire table. Inside the table, each cell is wrapped in a `div` tag with either `text-align: left` or `text-align: right` to align the text within the cells.<|eot_id|><|eot_id|><|eot_id|>\n"]}], "source": ["def render_glaive(samples):\n", "    return render(samples, \"question\", \"answer\")\n", "\n", "print(tokenizer.detokenize(render_glaive(glaive[0:2])))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 931 tensors\n"]}], "source": ["glaive_tensors = []\n", "\n", "# 136109 is total, we only need 512 * 10\n", "\n", "index = 0\n", "while index < len(glaive[:(512 * 10)]):\n", "    end_index = min(index + random.randint(1, 10), len(glaive))\n", "    samples = glaive[index: end_index]\n", "    assert len(samples) > 0\n", "    tensors = render_glaive(samples)\n", "    index = end_index\n", "    glaive_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(glaive_tensors))"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 111266 samples from /mnt/efs/augment/user/yury/theblackcat102/multiround_programming_convo/train.jsonl\n", "{'request_message': '<PERSON>elop a program which rearranges a given string and its subset (up to 3 words) in alphabetical order. The string and subsets should ignore symbols.\\nPrimary String: \"Python Programming\"\\nSubset: \"Python\", \"Programming\", \"Python Programming\"', 'response_text': 'Sure, here is a simple Python program that performs this function.\\n\\n```python\\ndef reorder_string_alphabetically(target_string):\\n    # Remove symbols and convert string to a list of words\\n    tuple = target_string.split()\\n    tuple = [\\'\\'.join(e for e in string if e.isalnum()) for string in tuple]\\n  \\n    # Sort list in Alphabetical order\\n    tuple.sort()\\n  \\n    # Join words back into a string\\n    result = \\' \\'.join(tuple)\\n    return result\\n\\nprimary_string = \"Python Programming\"\\nsubsets = [\"Python\", \"Programming\", \"Python Programming\"]\\n\\nprint(\"Primary String:\", reorder_string_alphabetically(primary_string))\\n\\nfor i, subset in enumerate(subsets):\\n    print(\"Subset {}: {}\".format(i+1, reorder_string_alphabetically(subset)))\\n```\\n\\nThe `reorder_string_alphabetically` function splits a given string into a list of words, sorts that list alphabetically, and then joins the sorted words back into a string. It then performs this operation on a primary string and a list of subsets.\\n\\nRunning this script with the primary string and subsets you provided would result in:\\n\\n- Primary String: Programming Python\\n- Subset 1: Python\\n- Subset 2: Programming\\n- Subset 3: Programming Python\\n\\nIt removes the non-alphabetic characters and orders the words alphabetically. Please note that Python\\'s sort operation is case-sensitive, so all capital letters will be ordered before lowercase ones. To make it case-insensitive, we can use `str.lower` as a key in sorting:\\n\\n```python\\ntuple.sort(key=str.lower)\\n```'}\n"]}], "source": ["MULTI_ROUND_CONVO = \"/mnt/efs/augment/user/yury/theblackcat102/multiround_programming_convo/train.jsonl\"\n", "\n", "multi_round_convo = []\n", "with open(MULTI_ROUND_CONVO) as f:\n", "    for line in f:\n", "        original_sample = json.loads(line[:-1])\n", "        messages = []\n", "        for index, message in enumerate(original_sample['conversations']):\n", "            if index % 2 == 0:\n", "                assert message['from'] == 'human'\n", "                messages.append({\n", "                    \"request_message\": message[\"text\"],\n", "                })\n", "            else:\n", "                assert message['from'] == 'assistant'\n", "                messages[-1].update({\n", "                    \"response_text\": message[\"text\"],\n", "                })\n", "        multi_round_convo.append(messages)\n", "\n", "random.shuffle(multi_round_convo)\n", "\n", "print('Loaded %d samples from %s' % (len(multi_round_convo), MULTI_ROUND_CONVO))\n", "for k in multi_round_convo[0]:\n", "    print(k)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|><|start_header_id|>system<|end_header_id|>You are Augment, an AI code assistant integrated into the VSCode IDE.\n", "Your role is to help software developers by answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Develop a program which rearranges a given string and its subset (up to 3 words) in alphabetical order. The string and subsets should ignore symbols.\n", "Primary String: \"Python Programming\"\n", "Subset: \"Python\", \"Programming\", \"Python Programming\"<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "Sure, here is a simple Python program that performs this function.\n", "\n", "```python\n", "def reorder_string_alphabetically(target_string):\n", "    # Remove symbols and convert string to a list of words\n", "    tuple = target_string.split()\n", "    tuple = [''.join(e for e in string if e.isalnum()) for string in tuple]\n", "  \n", "    # Sort list in Alphabetical order\n", "    tuple.sort()\n", "  \n", "    # Join words back into a string\n", "    result =''.join(tuple)\n", "    return result\n", "\n", "primary_string = \"Python Programming\"\n", "subsets = [\"Python\", \"Programming\", \"Python Programming\"]\n", "\n", "print(\"Primary String:\", reorder_string_alphabetically(primary_string))\n", "\n", "for i, subset in enumerate(subsets):\n", "    print(\"Subset {}: {}\".format(i+1, reorder_string_alphabetically(subset)))\n", "```\n", "\n", "The `reorder_string_alphabetically` function splits a given string into a list of words, sorts that list alphabetically, and then joins the sorted words back into a string. It then performs this operation on a primary string and a list of subsets.\n", "\n", "Running this script with the primary string and subsets you provided would result in:\n", "\n", "- Primary String: Programming Python\n", "- Subset 1: Python\n", "- Subset 2: Programming\n", "- Subset 3: Programming Python\n", "\n", "It removes the non-alphabetic characters and orders the words alphabetically. Please note that Python's sort operation is case-sensitive, so all capital letters will be ordered before lowercase ones. To make it case-insensitive, we can use `str.lower` as a key in sorting:\n", "\n", "```python\n", "tuple.sort(key=str.lower)\n", "```<|eot_id|><|eot_id|><|eot_id|>\n"]}], "source": ["def render_multi_round_convo(sample):\n", "    return render(sample, \"request_message\", \"response_text\")\n", "\n", "print(tokenizer.detokenize(render_multi_round_convo(multi_round_convo[0])))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 512 tensors\n"]}], "source": ["multi_round_convo_tensors = []\n", "\n", "index = 0\n", "while index < len(multi_round_convo[:512]):    \n", "    tensors = render_multi_round_convo(multi_round_convo[index])\n", "    index += 1\n", "    multi_round_convo_tensors.append(tensors)\n", "\n", "print('Generated %d tensors' % len(multi_round_convo_tensors))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "data = evol_codealpaca_tensors + glaive_tensors + multi_round_convo_tensors\n", "\n", "random.seed(31415)\n", "random.shuffle(data)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 2380 samples\n"]}], "source": ["import torch\n", "from megatron.data import indexed_dataset\n", "\n", "builder = indexed_dataset.make_builder(\n", "    \"/mnt/efs/augment/user/yury/binks/llama3/calibration_data.bin\",\n", "    impl=\"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")\n", "\n", "counter = 0\n", "for tensor in data:\n", "    builder.add_item(torch.tensor(tensor))\n", "    counter += 1\n", "\n", "builder.finalize(\"/mnt/efs/augment/user/yury/binks/llama3/calibration_data.idx\")\n", "\n", "print('Saved %d samples' % counter)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "2380\n"]}], "source": ["path = \"/mnt/efs/augment/user/yury/binks/llama3/calibration_data\"\n", "\n", "ds = indexed_dataset.make_dataset(\n", "    path, impl=\"mmap\", skip_warmup=True\n", ")\n", "print(len(ds))\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[34m<|begin_of_text|>\u001b[0m\u001b[1m\u001b[34m<|start_header_id|>\u001b[0msystem\u001b[1m\u001b[34m<|end_header_id|>\u001b[0mYou are Augment, an AI code assistant integrated into the VSCode IDE.\n", "Your role is to help software developers by answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.\u001b[1m\u001b[34m<|eot_id|>\u001b[0m\u001b[1m\u001b[34m<|start_header_id|>\u001b[0muser\u001b[1m\u001b[34m<|end_header_id|>\u001b[0m\n", "\n", "Devise a hypertext markup language (HTML) structure for building a table representation encompassing these detailed data elements: Full Legal Identity of the Individual, Precise Count of Years Since Birth (Chronological Age), and Determined Gender at Birth (Biological Sex).\u001b[1m\u001b[34m<|eot_id|>\u001b[0m\u001b[1m\u001b[34m<|start_header_id|>\u001b[0massistant\u001b[1m\u001b[34m<|end_header_id|>\u001b[0m\n", "\n", "You can use the `<table>` element of HTML to structure the data. The <th> tag is used for table headers while the <tr> tag is used for table rows, and the <td> tag is used for table data.\n", "\n", "Here's an HTML structure representing the mentioned data elements:\n", "\n", "```html\n", "<!DOCTYPE html>\n", "<html>\n", "  <head>\n", "    <title>Data Table</title>\n", "  </head>\n", "  <body>\n", "    <table border='1'>\n", "      <tr>\n", "        <th>Full Legal Identity of the Individual</th>\n", "        <th>Precise Count of Years Since Birth (Chronological Age)</th>\n", "        <th>Determined Gender at Birth (Biological Sex)</th>\n", "      </tr>\n", "      <tr>\n", "        <td><PERSON></td>\n", "        <td>25</td>\n", "        <td>Male</td>\n", "      </tr>\n", "      <tr>\n", "        <td><PERSON></td>\n", "        <td>28</td>\n", "        <td>Female</td>\n", "      </tr>\n", "    </table>\n", "  </body>\n", "</html>\n", "```\n", "\n", "In the above code, the table has three column headers; each reflecting the information fields you detailed. After that, two rows of data, each containing three columns of information are added. You can add more rows to represent additional individuals. Each row represents the information of a single individual.\u001b[1m\u001b[34m<|eot_id|>\u001b[0m\u001b[1m\u001b[34m<|eot_id|>\u001b[0m\n"]}], "source": ["from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "print(highlight_special_tokens(tokenizer.detokenize(ds[0]), tokenizer.all_special_tokens()))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2380\n"]}], "source": ["import json \n", "\n", "def find_last_occurrence(lst, val):\n", "    # Iterate over the list in reverse order\n", "    for i in range(len(lst) - 1, -1, -1):\n", "        # If the current value is the value we're looking for\n", "        if lst[i] == val:\n", "            # Return the index\n", "            return i\n", "    # If the value is not found, return -1\n", "    return -1\n", "\n", "def extract_inputs(sample):\n", "    # 128009 <|eot_id|>\n", "    # 128006 <|start_header_id|>\n", "    # 78191 assistant\n", "    # 128007 <|end_header_id|>\n", "    # 271 \\n\n", "    last_end_header_id = find_last_occurrence(sample, 128007)\n", "    assert sample[last_end_header_id + 1] == 271, sample[last_end_header_id:]\n", "    return sample[:last_end_header_id + 1]\n", "\n", "inputs_data = [{\"context\": extract_inputs(sample)} for sample in data]\n", "print(len(inputs_data))\n", "\n", "with open('/mnt/efs/augment/user/yury/binks/llama3/calibration_data.json', 'w') as f:\n", "    json.dump(inputs_data, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "**COPIED FROM experimental/zhuoran/yarn/replay_chat_requests.ipynb**\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "from base.augment_client.client import BlobsJson\n", "\n", "\n", "def parse_row(row: bigquery.Row) -> dict:\n", "    request_json = row[\"request_json\"]\n", "\n", "    prefix = request_json.get(\"prefix\", \"\")\n", "    suffix = request_json.get(\"suffix\", \"\")\n", "    selected_code = request_json.get(\"selected_code\", \"\")\n", "    prefix_begin = request_json.get(\"prefix_begin\", None)\n", "    suffix_end = request_json.get(\"suffix_end\", None)\n", "    blob_name = request_json.get(\"blob_name\", None)\n", "    blobs = request_json.get(\"blobs\", None)\n", "    context_code_exchange_request_id = request_json.get(\"context_code_exchange_request_id\", None)\n", "    if blobs is not None:\n", "        print(blobs.keys())\n", "        blobs = BlobsJson(\n", "            checkpoint_id=blobs.pop(\"baseline_checkpoint_id\")\n", "            if \"baseline_checkpoint_id\" in blobs\n", "            else None,\n", "            added_blobs=[base64.b64decode(blob).hex() for blob in blobs[\"added\"]]\n", "            if \"added\" in blobs\n", "            else [],\n", "            deleted_blobs=[base64.b64decode(blob).hex() for blob in blobs[\"deleted\"]]\n", "            if \"deleted\" in blobs\n", "            else [],\n", "        )\n", "\n", "    chat_history = request_json.get(\"chat_history\", [])\n", "\n", "    return_dict = {\n", "        \"selected_code\": selected_code,\n", "        \"message\": request_json[\"message\"],\n", "        \"prefix\": prefix,\n", "        \"suffix\": suffix,\n", "        \"path\": request_json[\"path\"],\n", "        \"blob_name\": blob_name,\n", "        \"prefix_begin\": prefix_begin,\n", "        \"suffix_end\": suffix_end,\n", "        \"blobs\": blobs,\n", "        \"chat_history\": chat_history,\n", "        \"context_code_exchange_request_id\": context_code_exchange_request_id,\n", "    }\n", "\n", "    return return_dict\n", "\n", "\n", "def download_request(request_id: str):\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}' AND event_type = 'chat_host_request'\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)\n", "\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.raw_json[\"request\"],\n", "    }\n", "    return row\n", "\n", "\n", "def get_request_inputs(request_id: str):\n", "    return parse_row(download_request(request_id))\n", "\n", "\n", "chat_sample_data = get_request_inputs(\n", "    \"\"\"\n", "c9f50283-fba5-4ae0-a7dd-365173deb757\n", "\"\"\".strip()\n", ")\n", "print(chat_sample_data[\"message\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"\")\n", "assert API_TOKEN\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dogfood_client = AugmentClient(\n", "    url=\"https://staging-shard-0.api.augmentcode.com/\",\n", "    token=API_TOKEN,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, \"\")\n", "chat_response_dogfood = model_client_dogfood.chat(\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    message=chat_sample_data[\"message\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    path=chat_sample_data[\"path\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    blob_name=chat_sample_data[\"blob_name\"],\n", "    blobs=chat_sample_data[\"blobs\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    context_code_exchange_request_id=chat_sample_data[\"context_code_exchange_request_id\"],\n", ")\n", "print(\n", "    f\"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{chat_response_dogfood.request_id}\"\n", ")\n", "chat_response_dogfood"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_dogfood.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
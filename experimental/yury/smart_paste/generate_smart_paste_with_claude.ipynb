{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "\n", "SMART_PASTE_INPUTS = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\"\n", ")\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage\"\n", ")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "BATCH_SMART_PASTE_REQUESTS_PATH = OUTPUT_DIR / \"batch_smart_paste_requests.jsonl\"\n", "OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses.jsonl\"\n", "LLAMA_RESPONSES_FILE = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl\"\n", ")\n", "\n", "# SMART_PASTE_INPUTS = Path(\n", "#     \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl\"\n", "# )\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage\")\n", "# OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# BATCH_SMART_PASTE_REQUESTS_PATH = OUTPUT_DIR / \"batch_smart_paste_requests_iter2.jsonl\"\n", "# OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses_iter2.jsonl\"\n", "# LLAMA_RESPONSES_FILE = Path(\n", "#     \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import json\n", "import numpy as np\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "from base.prompt_format.common import Exchange\n", "\n", "smart_paste_inputs = {}\n", "\n", "with SMART_PASTE_INPUTS.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        smart_paste_input = ResearchSmartPastePromptInput.from_json(line)\n", "        assert (\n", "            smart_paste_input.request_id\n", "            == smart_paste_input.context_code_exchange_request_id\n", "        )\n", "        assert (\n", "            smart_paste_input.request_id\n", "            == smart_paste_input.chat_history[-1].request_id\n", "        )\n", "        assert len(smart_paste_input.chat_history) in [1, 2], len(\n", "            smart_paste_input.chat_history\n", "        )\n", "        smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input\n", "\n", "print(f\"Loaded {len(smart_paste_inputs)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "llama_responses = {}\n", "\n", "with LLAMA_RESPONSES_FILE.open() as f:\n", "    for line in tqdm.tqdm(f):\n", "        try:\n", "            datum = json.loads(line)\n", "        except json.decoder.JSONDecodeError:\n", "            continue\n", "        if datum[\"status\"] != \"success\":\n", "            continue\n", "        request_id = datum[\"request_id\"]\n", "        llama_responses[request_id] = datum[\"modified_file\"]\n", "\n", "print(f\"Loaded {len(llama_responses)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datum"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import tqdm\n", "\n", "\n", "# ANTHROPIC_API_KEY = \"\"\n", "# client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "\n", "from experimental.yury.smart_paste_lib.claude_utils import (\n", "    build_smart_paste_claude_request,\n", ")\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "\n", "smart_paste_responses = {}\n", "\n", "for request_id, smart_paste_input in tqdm.tqdm(smart_paste_inputs.items()):\n", "    smart_paste_request = build_smart_paste_claude_request(\n", "        target_file=File(\n", "            smart_paste_input.target_path, smart_paste_input.target_file_content\n", "        ),\n", "        codeblock=smart_paste_input.code_block,\n", "        chat_history=smart_paste_input.chat_history,\n", "    )\n", "\n", "    smart_paste_responses[request_id] = client.messages.create(**smart_paste_request)\n", "\n", "    if len(smart_paste_responses) >= 10:\n", "        break\n", "\n", "print(f\"Processed {len(smart_paste_responses)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import tqdm\n", "\n", "\n", "ANTHROPIC_API_KEY = \"\"\n", "client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "BATCH_CALL_UNIQUE_ID = \"smart_paste_batch_\"\n", "\n", "\n", "def get_uuid(custom_id):\n", "    assert custom_id.startswith(BATCH_CALL_UNIQUE_ID)\n", "    return custom_id[len(BATCH_CALL_UNIQUE_ID) :]\n", "\n", "\n", "# smart_paste_responses = {}\n", "\n", "# for result in client.beta.messages.batches.results(\"msgbatch_01GAovxkPnGbzfvejSHUysjc\"):\n", "#     request_id = get_uuid(result.custom_id)\n", "#     smart_paste_responses[request_id] = result.result.message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_lib.claude_utils import (\n", "    extract_from_response_and_apply_tool_calls,\n", "    LineNotFound,\n", ")\n", "\n", "for request_id, response in smart_paste_responses.items():\n", "    print(request_id)\n", "    try:\n", "        _ = extract_from_response_and_apply_tool_calls(\n", "            smart_paste_inputs[request_id].target_file_content, response\n", "        )\n", "    except LineNotFound:\n", "        print(\"Line not found\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import os\n", "\n", "from base.diff_utils.diff_utils import File\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"\")\n", "assert API_TOKEN\n", "\n", "URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "RI_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "MODEL = \"binks-v12-fp16-longoutput\"\n", "\n", "# URL = \"https://dev-yury.us-central.api.augmentcode.com\"\n", "# MODEL = \"binks-v12-fp16-longoutput\"\n", "# RI_URL = \"https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "\n", "dogfood_client = AugmentClient(\n", "    url=URL,\n", "    token=API_TOKEN,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, MODEL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    SMART_PASTE_PROMPT,\n", "    extract_modified_file_from_response,\n", ")\n", "\n", "\n", "def run_prompted_llama(input_sample: ResearchSmartPastePromptInput):\n", "    message = SMART_PASTE_PROMPT.format(\n", "        target_path=input_sample.target_path,\n", "        target_file_contents=input_sample.target_file_content,\n", "        suggested_edit=input_sample.code_block,\n", "    )\n", "    chat_response_dogfood = model_client_dogfood.chat(\n", "        message=message,\n", "        selected_code=input_sample.selected_code,\n", "        prefix=input_sample.prefix,\n", "        suffix=input_sample.suffix,\n", "        path=input_sample.path,\n", "        chat_history=input_sample.chat_history,\n", "        context_code_exchange_request_id=input_sample.context_code_exchange_request_id,\n", "        prefix_begin=input_sample.prefix_begin,\n", "        suffix_end=input_sample.suffix_end,\n", "    )\n", "\n", "    return extract_modified_file_from_response(chat_response_dogfood.text)\n", "\n", "\n", "llama_responses = {}\n", "\n", "for request_id in tqdm.tqdm(smart_paste_responses):\n", "    llama_responses[request_id] = run_prompted_llama(smart_paste_inputs[request_id])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_same, n_diff = 0, 0\n", "\n", "for request_id in smart_paste_inputs:\n", "    if request_id not in llama_responses or request_id not in smart_paste_responses:\n", "        continue\n", "    print(request_id)\n", "\n", "    llama_response = llama_responses[request_id]\n", "    smart_paste_response = extract_from_response_and_apply_tool_calls(\n", "        smart_paste_inputs[request_id].target_file_content,\n", "        smart_paste_responses[request_id],\n", "    )\n", "\n", "    correct = llama_response.strip() == smart_paste_response.strip()\n", "    if correct:\n", "        print(\"SAME!\")\n", "        n_same += 1\n", "    else:\n", "        path = smart_paste_inputs[request_id].target_path\n", "        print(\n", "            compute_file_diff(\n", "                File(path, smart_paste_response), File(path, llama_response)\n", "            )\n", "        )\n", "        n_diff += 1\n", "\n", "    print(\"=\" * 20)\n", "\n", "print(f\"{n_same} same, {n_diff} diff\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_id = \"5288c733-e601-429c-b13b-3640ca38e410\"\n", "# request_id = \"3e86cc57-988a-4fad-8732-c38519813c9a\"\n", "\n", "llama_response = llama_responses[request_id]\n", "smart_paste_response = extract_from_response_and_apply_tool_calls(\n", "    smart_paste_inputs[request_id].target_file_content,\n", "    smart_paste_responses[request_id],\n", ")\n", "path = smart_paste_inputs[request_id].target_path\n", "\n", "print(path)\n", "print(\n", "    smart_paste_responses[request_id].content[1].input[\"start_line_number\"],\n", "    \":\",\n", "    smart_paste_responses[request_id].content[1].input[\"end_line_number\"],\n", ")\n", "print(\"=\" * 20)\n", "print(smart_paste_responses[request_id].content[1].input[\"old_text\"])\n", "print(\"=\" * 20)\n", "print(smart_paste_responses[request_id].content[1].input[\"replacement_text\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import json\n", "\n", "\n", "ANTHROPIC_API_KEY = \"\"\n", "# from research.environments import get_eng_secret\n", "# api_key = get_eng_secret(\"seal-research-anthropic-key\")\n", "client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "\n", "N_REQUESTS_PER_STEP = 700\n", "BATCH_CALL_UNIQUE_ID = \"smart_paste_batch_\"\n", "\n", "successful_request_ids = set()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_lib.claude_utils import (\n", "    build_smart_paste_claude_request,\n", ")\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "\n", "data_for_batch_processing = []\n", "n_already_processed, n_to_be_processed = 0, 0\n", "\n", "\n", "with BATCH_SMART_PASTE_REQUESTS_PATH.open(\"a\") as f_out:\n", "\n", "    def send_batch_request():\n", "        global data_for_batch_processing\n", "        global n_to_be_processed\n", "        all_custom_ids = [request[\"custom_id\"] for request in data_for_batch_processing]\n", "        n_to_be_processed += len(all_custom_ids)\n", "        batch_request_id = client.beta.messages.batches.create(\n", "            requests=data_for_batch_processing\n", "        )\n", "        # print(\n", "        #     f\"Sending {len(data_for_batch_processing)} requests, size {len(json.dumps(data_for_batch_processing)) / 1024 / 1024}MB\"\n", "        # )\n", "        json.dump(\n", "            {\n", "                \"batch_request_id\": batch_request_id.id,\n", "                \"custom_request_ids\": all_custom_ids,\n", "            },\n", "            f_out,\n", "        )\n", "        f_out.write(\"\\n\")\n", "        data_for_batch_processing = []\n", "\n", "    n_samples, n_batches = 0, 0\n", "    pbar = tqdm.tqdm(smart_paste_inputs.items())\n", "    for request_id, smart_paste_input in pbar:\n", "        if (\n", "            request_id in successful_request_ids\n", "            or request_id in successful_custom_uuids\n", "        ):\n", "            n_already_processed += 1\n", "            continue\n", "        smart_paste_request = build_smart_paste_claude_request(\n", "            target_file=File(\n", "                smart_paste_input.target_path, smart_paste_input.target_file_content\n", "            ),\n", "            codeblock=smart_paste_input.code_block,\n", "            chat_history=smart_paste_input.chat_history,\n", "        )\n", "        data_for_batch_processing.append(\n", "            {\n", "                \"custom_id\": f\"{BATCH_CALL_UNIQUE_ID}{request_id}\",\n", "                \"params\": smart_paste_request,\n", "            }\n", "        )\n", "        if len(data_for_batch_processing) >= N_REQUESTS_PER_STEP:\n", "            batch_size_mb = len(json.dumps(data_for_batch_processing)) / 1024 / 1024\n", "            n_batches += 1\n", "            n_samples += len(data_for_batch_processing)\n", "            send_batch_request()\n", "            pbar.set_postfix(\n", "                n_samples=n_samples, n_batches=n_batches, batch_size_mb=batch_size_mb\n", "            )\n", "\n", "    if len(data_for_batch_processing) > 0:\n", "        send_batch_request()\n", "\n", "print(f\"{n_already_processed} already processed, {n_to_be_processed} to be processed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import tqdm\n", "import json\n", "\n", "\n", "ANTHROPIC_API_KEY = \"\"\n", "client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "n_cancelled, n_already_processed = 0, 0\n", "\n", "with BATCH_SMART_PASTE_REQUESTS_PATH.open(\"r\") as f:\n", "    pbar = tqdm.tqdm(f)\n", "    for line in pbar:\n", "        batch_request = client.beta.messages.batches.retrieve(\n", "            json.loads(line)[\"batch_request_id\"]\n", "        )\n", "        if batch_request.processing_status == \"in_progress\":\n", "            client.beta.messages.batches.cancel(batch_request.id)\n", "            # print(f\"Batch {batch_request.id} is still in progress. Canceling\")\n", "            n_cancelled += 1\n", "        else:\n", "            # print(f\"Batch {batch_request.id} is not in progress anymore. Skipping\")\n", "            n_already_processed += 1\n", "\n", "        pbar.set_postfix(\n", "            n_cancelled=n_cancelled, n_already_processed=n_already_processed\n", "        )\n", "\n", "print(f\"{n_cancelled} cancelled, {n_already_processed} already processed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "from experimental.yury.smart_paste_lib.claude_utils import (\n", "    extract_from_response_and_apply_tool_calls,\n", "    LineNotFound,\n", "    InvalidToolCall,\n", "    InvalidLineNumber,\n", ")\n", "\n", "import pandas as pd\n", "import uuid\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "\n", "BATCH_CALL_UNIQUE_ID = \"smart_paste_batch_\"\n", "\n", "\n", "pbar = tqdm.tqdm(\n", "    desc=\"Download batch requests from anthropic\",\n", ")\n", "\n", "n_fail, n_success, n_failed_to_apply_tool_calls = 0, 0, 0\n", "n_no_llama, n_same, n_diff = 0, 0, 0\n", "successful_custom_uuids = set()\n", "diffs = []\n", "\n", "np.random.seed(31415)\n", "\n", "\n", "def get_uuid(custom_id):\n", "    assert custom_id.startswith(BATCH_CALL_UNIQUE_ID)\n", "    return custom_id[len(BATCH_CALL_UNIQUE_ID) :]\n", "\n", "\n", "with OUTPUT_FILE.open(\"w\") as f_out:\n", "    with BATCH_SMART_PASTE_REQUESTS_PATH.open(\"r\") as f:\n", "        for line in f:\n", "            batch_request = client.beta.messages.batches.retrieve(\n", "                json.loads(line)[\"batch_request_id\"]\n", "            )\n", "            if batch_request.processing_status == \"in_progress\":\n", "                print(f\"Batch {batch_request.id} is still in progress. Skipping\")\n", "                pbar.update()\n", "                continue\n", "\n", "            for result in client.beta.messages.batches.results(\n", "                batch_request.id,\n", "            ):\n", "                if result.result.type != \"succeeded\":\n", "                    n_fail += 1\n", "                    continue\n", "                sample_uuid = get_uuid(result.custom_id)\n", "                assert sample_uuid in smart_paste_inputs\n", "\n", "                assert sample_uuid not in successful_custom_uuids\n", "                successful_custom_uuids.add(sample_uuid)\n", "\n", "                try:\n", "                    modified_file = extract_from_response_and_apply_tool_calls(\n", "                        smart_paste_inputs[sample_uuid].target_file_content,\n", "                        result.result.message,\n", "                    )\n", "                except LineNotFound:\n", "                    n_failed_to_apply_tool_calls += 1\n", "                    continue\n", "                except InvalidToolCall:\n", "                    n_failed_to_apply_tool_calls += 1\n", "                    continue\n", "                except InvalidLineNumber:\n", "                    n_failed_to_apply_tool_calls += 1\n", "                    continue\n", "\n", "                json.dump(\n", "                    {\n", "                        \"request_id\": sample_uuid,\n", "                        \"response\": result.result.message.to_dict(),\n", "                        \"modified_file\": modified_file,\n", "                    },\n", "                    f_out,\n", "                )\n", "                f_out.write(\"\\n\")\n", "                n_success += 1\n", "\n", "                if sample_uuid in llama_responses:\n", "                    llama_response = llama_responses[sample_uuid]\n", "                    if llama_response.strip() == modified_file.strip():\n", "                        n_same += 1\n", "                    else:\n", "                        n_diff += 1\n", "                        path = smart_paste_inputs[sample_uuid].target_path\n", "                        diffs.append(\n", "                            compute_file_diff(\n", "                                File(path, modified_file), File(path, llama_response)\n", "                            )\n", "                        )\n", "                else:\n", "                    n_no_llama += 1\n", "\n", "            pbar.set_postfix(\n", "                {\n", "                    \"n_success\": n_success,\n", "                    \"n_fail\": n_fail,\n", "                    \"n_failed_to_apply_tool_calls\": n_failed_to_apply_tool_calls,\n", "                }\n", "            )\n", "            pbar.update()\n", "\n", "\n", "print(\n", "    f\"Wrote {n_success} samples ({len(successful_custom_uuids)}), {n_fail} failed, {n_failed_to_apply_tool_calls} failed to apply tool calls\"\n", ")\n", "print(f\"{n_same} same, {n_diff} diff, {n_no_llama} no llama\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for diff in diffs[:10]:\n", "#     print(diff)\n", "#     # print()\n", "#     print(\"=\" * 20)\n", "#     print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""<PERSON><PERSON><PERSON> to remove samples with extra lines from a dataset."""

import argparse
import json
from pathlib import Path
from typing import Dict, Any, Optional

from experimental.yury.smart_paste.analyze_extra_lines import analyze_extra_lines
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput


def should_keep_sample(datum: Dict[str, Any], allow_imports: bool = False) -> bool:
    """Determine if a sample should be kept based on extra lines analysis.

    Args:
        datum: The sample data containing input and response
        allow_imports: Whether to allow samples that only have extra import lines

    Returns:
        True if the sample should be kept, False if it should be removed
    """
    try:
        # Convert input dict to ResearchSmartPastePromptInput
        input_dict = datum["input"]
        input_obj = ResearchSmartPastePromptInput(
            path=input_dict.get("target_path", ""),
            prefix="",
            selected_code="",
            code_block=input_dict.get("code_block", ""),
            suffix="",
            chat_history=[],
            prefix_begin=0,
            suffix_end=0,
            retrieved_chunks=[],
            target_path=input_dict.get("target_path", ""),
            target_file_content=input_dict.get("target_file_content", ""),
        )
        datum_with_obj = {"input": input_obj, "response": datum["response"]}

        (
            total_lines,
            extra_lines,
            matching_lines,
            extra_lines_set,
            normalized_extra_lines_set,
            input_lines,
            import_lines,
        ) = analyze_extra_lines(datum_with_obj)

        # If there are no extra lines at all, keep the sample
        if extra_lines == 0:
            return True

        # If we allow imports and all extra lines are imports, keep the sample
        if allow_imports and len(extra_lines_set - import_lines) == 0:
            return True

        return False

    except Exception:
        # If analysis fails, remove the sample to be safe
        return False


def filter_samples(
    input_file: Path,
    output_file: Path,
    allow_imports: bool = False,
    max_samples: Optional[int] = None,
) -> tuple[int, int]:
    """Filter samples by removing those with extra lines.

    Args:
        input_file: Path to input JSONL file
        output_file: Path to output JSONL file
        allow_imports: Whether to allow samples that only have extra import lines
        max_samples: Maximum number of samples to process

    Returns:
        Tuple of (total samples processed, samples kept)
    """
    total_samples = 0
    kept_samples = 0

    # Create output directory if needed
    output_file.parent.mkdir(parents=True, exist_ok=True)

    with input_file.open() as f_in, output_file.open("w") as f_out:
        for line in f_in:
            if max_samples is not None and total_samples >= max_samples:
                break

            total_samples += 1
            datum = json.loads(line)

            if should_keep_sample(datum, allow_imports):
                f_out.write(line)
                kept_samples += 1

    return total_samples, kept_samples


def main():
    parser = argparse.ArgumentParser(
        description="Remove samples with extra lines from dataset"
    )
    parser.add_argument("input_file", type=Path, help="Input JSONL file")
    parser.add_argument("output_file", type=Path, help="Output JSONL file")
    parser.add_argument(
        "--allow-imports",
        action="store_true",
        help="Allow samples that only have extra import lines",
    )
    parser.add_argument(
        "--max-samples", type=int, help="Maximum number of samples to process"
    )
    args = parser.parse_args()

    total_samples, kept_samples = filter_samples(
        args.input_file, args.output_file, args.allow_imports, args.max_samples
    )

    print(f"\nProcessed {total_samples} samples")
    print(f"Kept {kept_samples} samples ({kept_samples/total_samples*100:.2f}%)")
    print(f"Removed {total_samples - kept_samples} samples")
    print(f"\nFiltered dataset saved to: {args.output_file}")


if __name__ == "__main__":
    main()

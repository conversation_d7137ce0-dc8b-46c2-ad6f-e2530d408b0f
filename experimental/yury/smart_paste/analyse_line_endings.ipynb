{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "INPUT_SAMPLES = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend/merged_smart_paste_responses.jsonl\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "from enum import Enum\n", "import json\n", "import tqdm\n", "import pandas as pd\n", "\n", "\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "pd.set_option(\"display.max_rows\", 50)\n", "\n", "\n", "class LineEndingType(str, Enum):\n", "    WINDOWS = \"WINDOWS\"\n", "    UNIX = \"UNIX\"\n", "    CR_ONLY = \"CR_ONLY\"\n", "    MIXED = \"MIXED\"\n", "    UNKNOWN = \"UNKNOWN\"\n", "    EMPTY = \"EMPTY\"\n", "    SINGLE_LINE = \"SINGLE_LINE\"\n", "\n", "\n", "last_char_counts = defaultdict(int)\n", "\n", "\n", "def detect_line_ending_type(s: str, purity_threshold: float = 0.95) -> LineEndingType:\n", "    lines = s.splitlines(keepends=True)\n", "\n", "    if len(lines) == 0:\n", "        return LineEndingType.EMPTY\n", "    if len(lines) == 1:\n", "        return LineEndingType.SINGLE_LINE\n", "\n", "    windows_line_ending_count = 0\n", "    unix_line_ending_count = 0\n", "    cr_only_line_ending_count = 0\n", "    unknown_line_ending_count = 0\n", "\n", "    for i in range(len(lines) - 1):\n", "        line = lines[i]\n", "        if line.endswith(\"\\r\\n\"):\n", "            windows_line_ending_count += 1\n", "        elif line.endswith(\"\\n\"):\n", "            unix_line_ending_count += 1\n", "        elif line.endswith(\"\\r\"):\n", "            cr_only_line_ending_count += 1\n", "        else:\n", "            last_char_counts[line[-1]] += 1\n", "            unknown_line_ending_count += 1\n", "\n", "    # Calculate proportions\n", "    total_endings = len(lines) - 1\n", "    windows_ratio = windows_line_ending_count / total_endings\n", "    unix_ratio = unix_line_ending_count / total_endings\n", "    cr_ratio = cr_only_line_ending_count / total_endings\n", "\n", "    # Check if any type meets the purity threshold\n", "    if windows_ratio >= purity_threshold:\n", "        return LineEndingType.WINDOWS\n", "    elif unix_ratio >= purity_threshold:\n", "        return LineEndingType.UNIX\n", "    elif cr_ratio >= purity_threshold:\n", "        return LineEndingType.CR_ONLY\n", "    else:\n", "        return LineEndingType.MIXED\n", "\n", "\n", "def line_ending_type_counts_to_df(line_ending_type_counts):\n", "    line_ending_type_counts = {\n", "        (\n", "            before_line_ending_type.name,\n", "            code_block_line_ending_type.name,\n", "            after_line_ending_type.name,\n", "        ): count\n", "        for (\n", "            before_line_ending_type,\n", "            code_block_line_ending_type,\n", "            after_line_ending_type,\n", "        ), count in line_ending_type_counts.items()\n", "    }\n", "    pandas_df = pd.DataFrame.from_dict(\n", "        line_ending_type_counts, orient=\"index\", columns=[\"count\"]\n", "    )\n", "    pandas_df = pandas_df.sort_values(\"count\", ascending=False)\n", "    return pandas_df\n", "\n", "\n", "line_ending_type_counts = {\n", "    (before_line_ending_type, code_block_line_ending_type, after_line_ending_type): 0\n", "    for before_line_ending_type in LineEndingType\n", "    for code_block_line_ending_type in LineEndingType\n", "    for after_line_ending_type in LineEndingType\n", "}\n", "\n", "with INPUT_SAMPLES.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line)\n", "        prompt_input = ResearchSmartPastePromptInput.from_dict(datum[\"input\"])\n", "        datum[\"input\"] = prompt_input\n", "        before_line_ending_type = detect_line_ending_type(\n", "            prompt_input.target_file_content\n", "        )\n", "        code_block_line_ending_type = detect_line_ending_type(prompt_input.code_block)\n", "        after_line_ending_type = detect_line_ending_type(datum[\"response\"])\n", "        line_ending_type_counts[\n", "            (\n", "                before_line_ending_type,\n", "                code_block_line_ending_type,\n", "                after_line_ending_type,\n", "            )\n", "        ] += 1\n", "\n", "print(line_ending_type_counts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 400)\n", "\n", "line_ending_type_counts_df = line_ending_type_counts_to_df(line_ending_type_counts)\n", "print(line_ending_type_counts_df)\n", "\n", "print(\n", "    pd.DataFrame.from_dict(\n", "        last_char_counts, orient=\"index\", columns=[\"count\"]\n", "    ).sort_values(\"count\", ascending=False)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "\n", "LINE_ENDING_MAP = {\n", "    LineEndingType.WINDOWS: \"\\r\\n\",\n", "    LineEndingType.UNIX: \"\\n\",\n", "}\n", "\n", "\n", "def set_line_endings(s: str, line_ending_type: LineEndingType) -> str:\n", "    return LINE_ENDING_MAP[line_ending_type].join(s.splitlines())\n", "\n", "\n", "def set_line_endings_in_sample(\n", "    sample: dict,\n", "    line_ending: LineEndingType,\n", "):\n", "    dataclasses.replace(\n", "        sample[\"input\"],\n", "        target_file_content=set_line_endings(\n", "            sample[\"input\"].target_file_content, line_ending\n", "        ),\n", "    )\n", "    sample[\"response\"] = set_line_endings(sample[\"response\"], line_ending)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
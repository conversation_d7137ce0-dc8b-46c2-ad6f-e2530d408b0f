{"cells": [{"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v3/05_merged_nocodeblocks\"\n", ")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# OUTPUT_FILE = OUTPUT_DIR / \"merged_smart_paste_responses.jsonl\"\n", "OUTPUT_FILE = OUTPUT_DIR / \"merged_smart_paste_responses_onlycodeblocks.jsonl\"\n", "\n", "\n", "smart_paste_inputs = {}\n", "claude_smart_paste_responses = {}\n", "llama_smart_paste_responses = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl: 94946it [00:13, 6900.25it/s, n_skipped=1]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["6100 multiple codeblocks, 88845 single codeblocks, 0 no codeblocks\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl: 94946it [00:22, 4231.29it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_claude_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl: 5510it [00:00, 13678.34it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses.jsonl: 94946it [00:03, 25686.74it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl: 6100it [00:00, 30167.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 94942 smart_paste_inputs, 4 failed to apply tool calls\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import json\n", "import tqdm\n", "from pathlib import Path\n", "import numpy as np\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "from base.prompt_format.common import Exchange\n", "from experimental.yury.smart_paste_lib.claude_utils import (\n", "    extract_from_dict_and_apply_tool_calls,\n", "    LineNotFound,\n", "    InvalidToolCall,\n", "    InvalidLineNumber,\n", ")\n", "from experimental.yury.smart_paste.smart_paste_input_source import (\n", "    load_smart_paste_inputs_v1,\n", "    extract_edit_markdown_blocks_from_data_v1,\n", ")\n", "\n", "DATA_V1 = [\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl\",\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_claude_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl\",\n", "        ),\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses.jsonl\",\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl\",\n", "        ),\n", "    )\n", "]\n", "\n", "smart_paste_inputs.update(load_smart_paste_inputs_v1())\n", "\n", "n_multiple_codeblocks, n_single_codeblocks, n_no_codeblocks = 0, 0, 0\n", "for smart_paste_input in smart_paste_inputs.values():\n", "    codeblocks = extract_edit_markdown_blocks_from_data_v1(smart_paste_input)\n", "    codeblocks_for_the_same_file = [\n", "        codeblock\n", "        for codeblock in codeblocks\n", "        if codeblock.path == smart_paste_input.target_path\n", "    ]\n", "    smart_paste_input.aux[\"multiple_codeblocks\"] = int(\n", "        len(codeblocks_for_the_same_file) > 1\n", "    )\n", "    if len(codeblocks_for_the_same_file) > 1:\n", "        n_multiple_codeblocks += 1\n", "    elif len(codeblocks_for_the_same_file) == 1:\n", "        n_single_codeblocks += 1\n", "    else:\n", "        n_no_codeblocks += 1\n", "\n", "print(\n", "    f\"{n_multiple_codeblocks} multiple codeblocks, {n_single_codeblocks} single codeblocks, {n_no_codeblocks} no codeblocks\"\n", ")\n", "\n", "n_success, n_apply_failed = 0, 0\n", "\n", "for (\n", "    smart_paste_inputs_path,\n", "    claude_smart_paste_responses_paths,\n", "    llama_smart_paste_responses_paths,\n", ") in DATA_V1:\n", "    with open(smart_paste_inputs_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {smart_paste_inputs_path}\"):\n", "            datum = json.loads(line)\n", "            request_id = datum[\"uuid\"]\n", "\n", "            claude_smart_paste_response = datum[\"after_file\"][\"contents\"]\n", "            claude_smart_paste_response_redo = extract_from_dict_and_apply_tool_calls(\n", "                datum[\"before_file\"][\"contents\"],\n", "                datum[\"aux\"][\"claude_smart_paste_response\"],\n", "            )\n", "            if claude_smart_paste_response != claude_smart_paste_response_redo:\n", "                n_apply_failed += 1\n", "                continue\n", "            claude_smart_paste_responses[request_id] = claude_smart_paste_response\n", "            n_success += 1\n", "\n", "    for claude_smart_paste_responses_path in claude_smart_paste_responses_paths:\n", "        with open(claude_smart_paste_responses_path, \"r\") as f:\n", "            for line in tqdm.tqdm(\n", "                f, desc=f\"Loading {claude_smart_paste_responses_path}\"\n", "            ):\n", "                datum = json.loads(line)\n", "                assert (\n", "                    smart_paste_inputs[datum[\"request_id\"]].aux[\"multiple_codeblocks\"]\n", "                    == 1\n", "                )\n", "                claude_smart_paste_responses[datum[\"request_id\"]] = datum[\n", "                    \"modified_file\"\n", "                ]\n", "\n", "    for llama_smart_paste_responses_path in llama_smart_paste_responses_paths:\n", "        with open(llama_smart_paste_responses_path, \"r\") as f:\n", "            for line in tqdm.tqdm(\n", "                f, desc=f\"Loading {llama_smart_paste_responses_path}\"\n", "            ):\n", "                datum = json.loads(line)\n", "                if datum[\"status\"] != \"success\":\n", "                    continue\n", "                llama_smart_paste_responses[datum[\"request_id\"]] = datum[\n", "                    \"modified_file\"\n", "                ]\n", "\n", "print(\n", "    f\"Loaded {n_success} smart_paste_inputs, {n_apply_failed} failed to apply tool calls\"\n", ")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl: 305259it [01:04, 4709.94it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Adjusted 160627 last messages out of 305259 samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses.jsonl: 293285it [00:12, 23377.85it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 293285 claude_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_nocodeblocks_claude_v2.jsonl: 36309it [00:02, 16296.58it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 329594 claude_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl: 314986it [00:06, 45319.04it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 263312 llama_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl: 68438it [00:02, 26035.58it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 330856 llama_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl: 88470it [00:21, 4159.40it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Adjusted 46438 last messages out of 88470 samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2.jsonl: 84671it [00:03, 21919.67it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 84671 claude_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2_nocodeblocks.jsonl: 11979it [00:00, 16153.57it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 96650 claude_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl: 88472it [00:02, 37379.39it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 84984 llama_smart_paste_responses\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2_nocodeblocks.jsonl: 13119it [00:00, 22955.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Overridden 97879 llama_smart_paste_responses\n", "35662 multiple codeblocks, 358067 single codeblocks, 0 no codeblocks\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import json\n", "import dataclasses\n", "import tqdm\n", "from pathlib import Path\n", "from experimental.yury.smart_paste.smart_paste_input_source import (\n", "    extract_edit_markdown_blocks_from_data_v2,\n", ")\n", "\n", "DATA_V2 = [\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\",\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses.jsonl\",\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_nocodeblocks_claude_v2.jsonl\",\n", "        ),\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl\",\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl\",\n", "        ),\n", "    ),\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl\",\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2.jsonl\",\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2_nocodeblocks.jsonl\",\n", "        ),\n", "        (\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl\",\n", "            \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2_nocodeblocks.jsonl\",\n", "        ),\n", "    ),\n", "]\n", "\n", "\n", "def adjust_last_message(smart_paste_input: ResearchSmartPastePromptInput):\n", "    chat_history = list(smart_paste_input.chat_history)\n", "    instruction = smart_paste_input.aux[\"commit\"][\"instruction\"]\n", "\n", "    last_message = chat_history[-1].request_message\n", "    assert instruction in last_message\n", "\n", "    if instruction == last_message:\n", "        return smart_paste_input, 0\n", "\n", "    adjusted_last_message = Exchange(\n", "        request_message=instruction,\n", "        response_text=chat_history[-1].response_text,\n", "        request_id=chat_history[-1].request_id,\n", "    )\n", "    chat_history[-1] = adjusted_last_message\n", "\n", "    adjusted_smart_paste_input = dataclasses.replace(\n", "        smart_paste_input, chat_history=chat_history\n", "    )\n", "    return adjusted_smart_paste_input, 1\n", "\n", "\n", "n_multiple_codeblocks, n_single_codeblocks, n_no_codeblocks = 0, 0, 0\n", "\n", "for (\n", "    smart_paste_inputs_path,\n", "    claude_smart_paste_responses_paths,\n", "    llama_smart_paste_responses_paths,\n", ") in DATA_V2:\n", "    n_adjusted_last_message, n_total_samples = 0, 0\n", "    with open(smart_paste_inputs_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {smart_paste_inputs_path}\"):\n", "            n_total_samples += 1\n", "            smart_paste_input = ResearchSmartPastePromptInput.from_json(line)\n", "            assert (\n", "                smart_paste_input.request_id\n", "                == smart_paste_input.context_code_exchange_request_id\n", "            )\n", "            assert (\n", "                smart_paste_input.request_id\n", "                == smart_paste_input.chat_history[-1].request_id\n", "            )\n", "            assert len(smart_paste_input.chat_history) in [1, 2], len(\n", "                smart_paste_input.chat_history\n", "            )\n", "            codeblocks = extract_edit_markdown_blocks_from_data_v2(smart_paste_input)\n", "            codeblocks = [\n", "                codeblock\n", "                for codeblock in codeblocks\n", "                if codeblock.path == smart_paste_input.target_path\n", "            ]\n", "            smart_paste_input.aux[\"multiple_codeblocks\"] = int(len(codeblocks) > 1)\n", "            if len(codeblocks) > 1:\n", "                n_multiple_codeblocks += 1\n", "            elif len(codeblocks) == 1:\n", "                n_single_codeblocks += 1\n", "            else:\n", "                n_no_codeblocks += 1\n", "            smart_paste_input, n_adjusted = adjust_last_message(smart_paste_input)\n", "            n_adjusted_last_message += n_adjusted\n", "            smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input\n", "        print(\n", "            f\"Adjusted {n_adjusted_last_message} last messages out of {n_total_samples} samples\"\n", "        )\n", "\n", "    n_override = 0\n", "    for claude_smart_paste_responses_path in claude_smart_paste_responses_paths:\n", "        with open(claude_smart_paste_responses_path, \"r\") as f:\n", "            for line in tqdm.tqdm(\n", "                f, desc=f\"Loading {claude_smart_paste_responses_path}\"\n", "            ):\n", "                datum = json.loads(line)\n", "                if datum[\"request_id\"] in claude_smart_paste_responses:\n", "                    n_override += 1\n", "                claude_smart_paste_responses[datum[\"request_id\"]] = datum[\n", "                    \"modified_file\"\n", "                ]\n", "            print(f\"Overridden {n_override} claude_smart_paste_responses\")\n", "\n", "    n_override = 0\n", "    for llama_smart_paste_responses_path in llama_smart_paste_responses_paths:\n", "        with open(llama_smart_paste_responses_path, \"r\") as f:\n", "            for line in tqdm.tqdm(\n", "                f, desc=f\"Loading {llama_smart_paste_responses_path}\"\n", "            ):\n", "                try:\n", "                    datum = json.loads(line)\n", "                except json.decoder.JSONDecodeError:\n", "                    continue\n", "                if datum[\"status\"] != \"success\":\n", "                    continue\n", "                if datum[\"request_id\"] in llama_smart_paste_responses:\n", "                    n_override += 1\n", "                llama_smart_paste_responses[datum[\"request_id\"]] = datum[\n", "                    \"modified_file\"\n", "                ]\n", "            print(f\"Overridden {n_override} llama_smart_paste_responses\")\n", "\n", "print(\n", "    f\"{n_multiple_codeblocks} multiple codeblocks, {n_single_codeblocks} single codeblocks, {n_no_codeblocks} no codeblocks\"\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["398 samples with multiple lines in the last message (out of 488674 samples)\n"]}], "source": ["counter = 0\n", "for uuid in smart_paste_inputs:\n", "    smart_paste_input = smart_paste_inputs[uuid]\n", "    last_message = smart_paste_input.chat_history[-1].request_message\n", "    if len(last_message.splitlines()) > 1:\n", "        counter += 1\n", "\n", "print(\n", "    f\"{counter} samples with multiple lines in the last message (out of {len(smart_paste_inputs)} samples)\"\n", ")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 488674 smart_paste_inputs\n", "Loaded 474451 claude_smart_paste_responses\n", "Loaded 449227 llama_smart_paste_responses\n"]}], "source": ["print(f\"Loaded {len(smart_paste_inputs)} smart_paste_inputs\")\n", "print(f\"Loaded {len(claude_smart_paste_responses)} claude_smart_paste_responses\")\n", "print(f\"Loaded {len(llama_smart_paste_responses)} llama_smart_paste_responses\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import build_str_diff, StrDiff, NoopSpan, AddedSpan, ModSpan\n", "\n", "NEWLINES = \"\\n\\r\"\n", "\n", "\n", "def last_span_only_adds_newline(str_diff: StrDiff):\n", "    if len(str_diff.spans) == 0:\n", "        # no changes, so no newline added\n", "        return False\n", "    other_spans_exist = False\n", "    for span in str_diff.spans[:-1]:\n", "        if not isinstance(span, NoopSpan):\n", "            other_spans_exist = True\n", "            break\n", "    if not other_spans_exist:\n", "        # There's only a single Add/Delete/Mod span,\n", "        # so we must respect it even if it adds a newline\n", "        return False\n", "\n", "    last_span = str_diff.spans[-1]\n", "    if isinstance(last_span, AddedSpan) and len(last_span.after.strip(NEWLINES)) == 0:\n", "        # last span only adds a newline\n", "        return True\n", "    if (\n", "        isinstance(last_span, ModSpan)\n", "        and last_span.after.startswith(last_span.before)\n", "        and last_span.after.rstrip(NEWLINES) == last_span.before.rstrip(NEWLINES)\n", "    ):\n", "        # last span only modifies code and adds a newline\n", "        return True\n", "    return False"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 488674/488674 [01:30<00:00, 5379.45it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved 307797 samples, skipped 70 with empty code blocks, 14224 no claude response, 166583 samples with no agreement\n", "6290 chose llama response, 301507 chose claude response\n", "15533 original no new lines, 4287 modified last line\n", "1614 last span only adds newline\n"]}], "source": ["import pandas as pd\n", "import tqdm\n", "from base.diff_utils.diff_utils import File\n", "from experimental.yury.smart_paste.augmentations import (\n", "    BeforeAfterFiles,\n", ")\n", "\n", "\n", "def has_trailing_newline(s: str) -> bool:\n", "    return s.endswith(\"\\n\")\n", "\n", "\n", "counter = 0\n", "n_skip = 0\n", "n_empty_code_block = 0\n", "n_no_claude_response = 0\n", "n_chose_llama_response, n_chose_claude_response = 0, 0\n", "n_original_no_new_lines, n_claude_changed_new_lines = 0, 0\n", "n_last_span_only_adds_newline = 0\n", "\n", "stats = []\n", "\n", "with OUTPUT_FILE.open(\"w\") as f:\n", "    for request_id in tqdm.tqdm(smart_paste_inputs):\n", "        prompt_input = smart_paste_inputs[request_id]\n", "        current_stats = {\n", "            \"request_id\": request_id,\n", "            \"multiple_codeblocks\": prompt_input.aux[\"multiple_codeblocks\"],\n", "        }\n", "        if len(prompt_input.code_block) == 0:\n", "            n_empty_code_block += 1\n", "            continue\n", "\n", "        if request_id not in claude_smart_paste_responses:\n", "            n_no_claude_response += 1\n", "            continue\n", "\n", "        original_commit = smart_paste_inputs[request_id].aux[\"commit\"][\"after_file\"][\n", "            \"contents\"\n", "        ]\n", "        claude_response = claude_smart_paste_responses[request_id]\n", "\n", "        if claude_response.strip() == original_commit.strip():\n", "            response = claude_response\n", "            current_stats[\"claude_commit\"] = 1\n", "            n_chose_claude_response += 1\n", "        elif request_id in llama_smart_paste_responses:\n", "            llama_response = llama_smart_paste_responses[request_id]\n", "            if llama_response.strip() == original_commit.strip():\n", "                response = llama_response\n", "                n_chose_llama_response += 1\n", "                current_stats[\"llama_commit\"] = 1\n", "            elif llama_response.strip() == claude_response.strip():\n", "                response = claude_response\n", "                n_chose_claude_response += 1\n", "                current_stats[\"claude_llama\"] = 1\n", "            else:\n", "                n_skip += 1\n", "                continue\n", "        else:\n", "            response = claude_response\n", "            n_chose_claude_response += 1\n", "            current_stats[\"claude_only\"] = 1\n", "\n", "        if not has_trailing_newline(prompt_input.target_file_content):\n", "            current_stats[\"trailing_newline_original\"] = 0\n", "            n_original_no_new_lines += 1\n", "            if has_trailing_newline(response):\n", "                current_stats[\"trailing_newline_label\"] = 1\n", "                n_claude_changed_new_lines += 1\n", "                before_after_file = BeforeAfterFiles(\n", "                    before_file=File(\n", "                        prompt_input.target_path, prompt_input.target_file_content\n", "                    ),\n", "                    after_file=File(prompt_input.target_path, response),\n", "                )\n", "                if last_span_only_adds_newline(before_after_file.str_diff):\n", "                    current_stats[\"last_span_only_adds_newline\"] = 1\n", "                    n_last_span_only_adds_newline += 1\n", "                    response = response.rstrip(NEWLINES)\n", "            else:\n", "                current_stats[\"trailing_newline_label\"] = 0\n", "        else:\n", "            current_stats[\"trailing_newline_original\"] = 1\n", "\n", "        output = {\n", "            \"request_id\": request_id,\n", "            \"input\": dataclasses.asdict(prompt_input),\n", "            \"response\": response,\n", "        }\n", "        json.dump(output, f)\n", "        f.write(\"\\n\")\n", "\n", "        stats.append(current_stats)\n", "\n", "        counter += 1\n", "\n", "df = pd.DataFrame(stats)\n", "\n", "print(\n", "    f\"Saved {counter} samples, skipped {n_empty_code_block} with empty code blocks, {n_no_claude_response} no claude response, {n_skip} samples with no agreement\"\n", ")\n", "print(\n", "    f\"{n_chose_llama_response} chose llama response, {n_chose_claude_response} chose claude response\"\n", ")\n", "print(\n", "    f\"{n_original_no_new_lines} original no new lines, {n_claude_changed_new_lines} modified last line\"\n", ")\n", "print(f\"{n_last_span_only_adds_newline} last span only adds newline\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>multiple_codeblocks</th>\n", "      <th>claude_<PERSON><PERSON></th>\n", "      <th>trailing_newline_original</th>\n", "      <th>claude_commit</th>\n", "      <th>trailing_newline_label</th>\n", "      <th>llama_commit</th>\n", "      <th>last_span_only_adds_newline</th>\n", "      <th>claude_only</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>307797.000000</td>\n", "      <td>176321.0</td>\n", "      <td>307797.000000</td>\n", "      <td>95844.0</td>\n", "      <td>15533.000000</td>\n", "      <td>6290.0</td>\n", "      <td>1614.0</td>\n", "      <td>29342.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.038792</td>\n", "      <td>1.0</td>\n", "      <td>0.949535</td>\n", "      <td>1.0</td>\n", "      <td>0.275993</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.193099</td>\n", "      <td>0.0</td>\n", "      <td>0.218903</td>\n", "      <td>0.0</td>\n", "      <td>0.447028</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       multiple_codeblocks  claude_<PERSON>ama  trailing_newline_original  \\\n", "count        307797.000000      176321.0              307797.000000   \n", "mean              0.038792           1.0                   0.949535   \n", "std               0.193099           0.0                   0.218903   \n", "min               0.000000           1.0                   0.000000   \n", "25%               0.000000           1.0                   1.000000   \n", "50%               0.000000           1.0                   1.000000   \n", "75%               0.000000           1.0                   1.000000   \n", "max               1.000000           1.0                   1.000000   \n", "\n", "       claude_commit  trailing_newline_label  llama_commit  \\\n", "count        95844.0            15533.000000        6290.0   \n", "mean             1.0                0.275993           1.0   \n", "std              0.0                0.447028           0.0   \n", "min              1.0                0.000000           1.0   \n", "25%              1.0                0.000000           1.0   \n", "50%              1.0                0.000000           1.0   \n", "75%              1.0                1.000000           1.0   \n", "max              1.0                1.000000           1.0   \n", "\n", "       last_span_only_adds_newline  claude_only  \n", "count                       1614.0      29342.0  \n", "mean                           1.0          1.0  \n", "std                            0.0          0.0  \n", "min                            1.0          1.0  \n", "25%                            1.0          1.0  \n", "50%                            1.0          1.0  \n", "75%                            1.0          1.0  \n", "max                            1.0          1.0  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>claude_<PERSON><PERSON></th>\n", "      <th>trailing_newline_original</th>\n", "      <th>claude_commit</th>\n", "      <th>trailing_newline_label</th>\n", "      <th>llama_commit</th>\n", "      <th>last_span_only_adds_newline</th>\n", "      <th>claude_only</th>\n", "    </tr>\n", "    <tr>\n", "      <th>multiple_codeblocks</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.564340</td>\n", "      <td>0.948549</td>\n", "      <td>0.317481</td>\n", "      <td>0.014277</td>\n", "      <td>0.020060</td>\n", "      <td>0.005371</td>\n", "      <td>0.098118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.783668</td>\n", "      <td>0.973953</td>\n", "      <td>0.160385</td>\n", "      <td>0.005276</td>\n", "      <td>0.029732</td>\n", "      <td>0.002094</td>\n", "      <td>0.026214</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     claude_llama  trailing_newline_original  claude_commit  \\\n", "multiple_codeblocks                                                           \n", "0                        0.564340                   0.948549       0.317481   \n", "1                        0.783668                   0.973953       0.160385   \n", "\n", "                     trailing_newline_label  llama_commit  \\\n", "multiple_codeblocks                                         \n", "0                                  0.014277      0.020060   \n", "1                                  0.005276      0.029732   \n", "\n", "                     last_span_only_adds_newline  claude_only  \n", "multiple_codeblocks                                            \n", "0                                       0.005371     0.098118  \n", "1                                       0.002094     0.026214  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Group by multiple_codeblocks and calculate means for all other columns\n", "df.fillna(0).groupby(\"multiple_codeblocks\").mean(numeric_only=True)\n", "# print(\"\\nMean values grouped by multiple_codeblocks:\")\n", "# print(grouped_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
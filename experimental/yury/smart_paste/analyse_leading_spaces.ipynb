{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from collections import defaultdict\n", "from enum import Enum\n", "import json\n", "import tqdm\n", "import pandas as pd\n", "\n", "\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "INPUT_SAMPLES = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend/merged_smart_paste_responses.jsonl\"\n", ")\n", "\n", "\n", "pd.set_option(\"display.max_rows\", 50)\n", "\n", "\n", "class SpaceType(str, Enum):\n", "    LEADING_SPACE = \"LEADING_SPACE\"\n", "    LEADING_TAB = \"LEADING_TAB\"\n", "    TRAILING_SPACE = \"TRAILING_SPACE\"\n", "    TRAILING_TAB = \"TRAILING_TAB\"\n", "    LEADING_BOTH = \"LEADING_BOTH\"  # Both space and tab\n", "    TRAILING_BOTH = \"TRAILING_BOTH\"  # Both space and tab\n", "    MIXED = \"MIXED\"  # Any combination of leading and trailing\n", "    NONE = \"NONE\"\n", "    EMPTY = \"EMPTY\"\n", "\n", "\n", "def detect_space_type(s: str) -> SpaceType:\n", "    if not s:\n", "        return SpaceType.EMPTY\n", "\n", "    has_leading_space = s.startswith(\" \")\n", "    has_leading_tab = s.startswith(\"\\t\")\n", "    has_trailing_space = s.endswith(\" \")\n", "    has_trailing_tab = s.endswith(\"\\t\")\n", "\n", "    leading_type = None\n", "    if has_leading_space and has_leading_tab:\n", "        leading_type = SpaceType.LEADING_BOTH\n", "    <PERSON>if has_leading_space:\n", "        leading_type = SpaceType.LEADING_SPACE\n", "    elif has_leading_tab:\n", "        leading_type = SpaceType.LEADING_TAB\n", "\n", "    trailing_type = None\n", "    if has_trailing_space and has_trailing_tab:\n", "        trailing_type = SpaceType.TRAILING_BOTH\n", "    elif has_trailing_space:\n", "        trailing_type = SpaceType.TRAILING_SPACE\n", "    elif has_trailing_tab:\n", "        trailing_type = SpaceType.TRAILING_TAB\n", "\n", "    if leading_type and trailing_type:\n", "        return SpaceType.MIXED\n", "    elif leading_type:\n", "        return leading_type\n", "    elif trailing_type:\n", "        return trailing_type\n", "    else:\n", "        return SpaceType.NONE\n", "\n", "\n", "def space_type_counts_to_df(space_type_counts):\n", "    pandas_df = pd.DataFrame.from_dict(\n", "        {k.name: v for k, v in space_type_counts.items()},\n", "        orient=\"index\",\n", "        columns=[\"count\"],\n", "    )\n", "    pandas_df = pandas_df.sort_values(\"count\", ascending=False)\n", "    return pandas_df\n", "\n", "\n", "# Initialize separate counters\n", "target_space_type_counts = {space_type: 0 for space_type in SpaceType}\n", "code_block_space_type_counts = {space_type: 0 for space_type in SpaceType}\n", "\n", "n_samples = -1  # Adjust this value to set your desired sample limit\n", "sample_count = 0\n", "\n", "with INPUT_SAMPLES.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        if sample_count >= n_samples and n_samples != -1:\n", "            break\n", "\n", "        datum = json.loads(line)\n", "        prompt_input = ResearchSmartPastePromptInput.from_dict(datum[\"input\"])\n", "        datum[\"input\"] = prompt_input\n", "\n", "        target_space_type = detect_space_type(prompt_input.target_file_content)\n", "        code_block_space_type = detect_space_type(prompt_input.code_block)\n", "\n", "        target_space_type_counts[target_space_type] += 1\n", "        code_block_space_type_counts[code_block_space_type] += 1\n", "\n", "        sample_count += 1\n", "\n", "print(\"Target file space type counts:\")\n", "print(space_type_counts_to_df(target_space_type_counts))\n", "print(\"\\nCode block space type counts:\")\n", "print(space_type_counts_to_df(code_block_space_type_counts))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
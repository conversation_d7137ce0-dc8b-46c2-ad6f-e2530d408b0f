determined:
  description: "Smart paste model training."
  workspace: Dev
  project: yury

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 128

fastbackward_configs:
 - configs/qwen25coder_14b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  batch_size: 1
  gradient_accumulation_steps: 4
  warmup_iters: 100
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  eval_interval: 100
  block_size: 32768
  use_activation_checkpointing: True

  # train_data_path: /mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/train_claude;/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/train_claude_emptysc;/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/train_naive;/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/train_naive_emptysc
  # eval_data_path: valid_claude@/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/valid_claude;valid_claude_emptysc@/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/valid_claude_emptysc;valid_naive_emptysc@/mnt/efs/augment/user/yury/smart_paste/data/v13_2_qwen_fixscnewline_32K/valid_naive_emptysc

  train_data_path: /mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k/train
  eval_data_path: valid_non_emptysc@/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k/valid_non_emptysc;valid_emptysc@/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k/valid_emptysc

  checkpoint_optimizer_state: False
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-14B

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 4
  use_sequence_parallel: True

  run_name: smartpaste_qwen14b_base_datav2_1_32k
  wandb_project: yury-smart-paste

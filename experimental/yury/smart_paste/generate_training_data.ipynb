{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "\n", "INPUT_SAMPLES = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_merged_responses/merged_smart_paste_responses.jsonl\"\n", ")\n", "\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_training_data_stage_32k\")\n", "OUTPUT_DIR = Path(\"/home/<USER>/data/v2/06_training_data_stage_32k\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "MAX_TOKEN_BUDGET = 32768 + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import numpy as np\n", "import json\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "data = []\n", "n_empty_code_block = 0\n", "n_might_need_adjustment = 0\n", "\n", "\n", "with INPUT_SAMPLES.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line)\n", "        prompt_input = ResearchSmartPastePromptInput.from_dict(datum[\"input\"])\n", "        datum[\"input\"] = prompt_input\n", "        if len(prompt_input.code_block) == 0:\n", "            n_empty_code_block += 1\n", "            continue\n", "        if (\n", "            len(prompt_input.suffix) > 0\n", "            and prompt_input.suffix[0] == \"\\n\"\n", "            and len(prompt_input.selected_code) > 0\n", "            and prompt_input.selected_code[-1] != \"\\n\"\n", "        ):\n", "            n_might_need_adjustment += 1\n", "        data.append(datum)\n", "\n", "print(f\"Loaded {len(data)} samples, {n_empty_code_block} empty code blocks\")\n", "print(f\"n_might_need_adjustment: {n_might_need_adjustment}\")\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import numpy as np\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.ranges.range_types import CharRange\n", "from base.prompt_format_smart_paste.forger_prompt_formatter import (\n", "    ForgerSmartPasteTokenApportionment, \n", "    <PERSON>ger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "from experimental.yury.smart_paste.augmentations import (\n", "    BeforeAfterFiles,\n", "    get_union_span_range_in_before,\n", "    CodeAugmentation,\n", ")\n", "\n", "\n", "token_apportionment = ForgerSmartPasteTokenApportionment(\n", "    path_len=256,\n", "    message_len=128,\n", "    prefix_len=MAX_TOKEN_BUDGET - 1,\n", "    suffix_len=MAX_TOKEN_BUDGET - 1,\n", "    max_prompt_len=MAX_TOKEN_BUDGET,\n", "    adjust_newline_from_suffix_to_selected_code=False, # prod only option\n", ")\n", "\n", "\n", "class ModifiedCodeNotInPrompt(Exception):\n", "    \"\"\"Raised when the parts of modified code are not in the prompt.\"\"\"\n", "\n", "    def __init__(self, message: str = \"Modified code is not in the prompt.\"):\n", "        self.message = message\n", "        super().__init__(self.message)\n", "\n", "\n", "def format_sample(\n", "    sample,\n", "    tokenizer,\n", "    max_token_budget: int,\n", "    suggested_edit_augmentator: CodeAugmentation,\n", "):    \n", "    aux_output = {}    \n", "    prompt_input = sample[\"input\"]\n", "    random_state = np.random.RandomState(hash(prompt_input.request_id) & 0xFFFFFFFF)\n", "    assert len(prompt_input.code_block) > 0\n", "    code_block = suggested_edit_augmentator.augment(prompt_input.code_block, random_state)\n", "    prompt_input = dataclasses.replace(\n", "        prompt_input,\n", "        code_block=code_block,\n", "    )\n", "    prompt_formatter = ForgerPromptFormatter(\n", "        tokenizer,\n", "        token_apportionment,\n", "    )\n", "    prompt_output = prompt_formatter.format_prompt(prompt_input)\n", "    aux_output[\"prompt_lenght\"] = len(prompt_output.tokens)\n", "\n", "    # Compute StrDiff\n", "    before_file = File(prompt_input.target_path, prompt_input.target_file_content)\n", "    after_file = File(prompt_input.target_path, sample[\"response\"])\n", "    before_after_file = BeforeAfterFiles(before_file, after_file)\n", "\n", "    before_crange = CharRange(\n", "        len(prompt_output.unused_prefix),\n", "        len(before_file.contents) - len(prompt_output.unused_suffix),\n", "    )\n", "\n", "    if len(prompt_input.selected_code) == 0:\n", "        aux_output[\"empty_selected_code\"] = 1\n", "    if len(prompt_output.unused_prefix) > 0 or len(prompt_output.unused_suffix) > 0:\n", "        aux_output[\"not_full_file\"] = 1\n", "    \n", "    # need to compute all_changes_span\n", "    if (\n", "        \"chat_request\" in prompt_input.aux\n", "        and \"selected_code_sampler_output\" in prompt_input.aux[\"chat_request\"]\n", "        and \"all_changes_span\" in prompt_input.aux[\"chat_request\"][\n", "            \"selected_code_sampler_output\"\n", "        ]\n", "    ):\n", "        all_changes_span = prompt_input.aux[\"chat_request\"][\"selected_code_sampler_output\"][\"all_changes_span\"]\n", "        all_changes_span = CharRange(\n", "            all_changes_span[\"start\"],\n", "            all_changes_span[\"stop\"],\n", "        )\n", "    else:\n", "        all_changes_span = get_union_span_range_in_before(before_after_file.str_diff, 0)\n", "\n", "    if all_changes_span is None:\n", "        aux_output[\"no_changes\"] = 1\n", "    else:\n", "        if len(all_changes_span) == 0:\n", "            aux_output[\"empty_before_crange\"] = 1\n", "        after_all_changes_span = before_after_file.str_diff.before_range_to_after(all_changes_span)\n", "        if len(after_all_changes_span) == 0:\n", "            aux_output[\"empty_after_crange\"] = 1    \n", "\n", "        if not before_crange.contains(all_changes_span):\n", "            # If we see a lot of things happening then maybe I should adjust my\n", "            # min_prefix_length / min_suffix_length computations.\n", "            raise ModifiedCodeNotInPrompt()\n", "\n", "    after_crange = before_after_file.str_diff.before_range_to_after(before_crange)\n", "    label = after_file.contents[after_crange.start: after_crange.stop]\n", "    label_tokens = tokenizer.tokenize_safe(label) + [tokenizer.special_tokens.eos]\n", "    aux_output[\"label_length\"] = len(label_tokens)\n", "\n", "    tokens = [-1 * t for t in prompt_output.tokens] + label_tokens\n", "    aux_output[\"total_length\"] = len(tokens)\n", "\n", "    if len(tokens) > max_token_budget:\n", "        raise ExceedContextLength()\n", "    \n", "    if len(tokens) < max_token_budget:\n", "        tokens = tokens + [-tokenizer.special_tokens.padding] * (max_token_budget - len(tokens))\n", "    assert len(tokens) == max_token_budget\n", "\n", "    return tokens, aux_output\n", "\n", "\n", "# DEBUG\n", "import pandas as pd\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "max_token_budget = 16384 + 1\n", "suggested_edit_augmentator = CodeAugmentation(\n", "    p_no_identation=0.3,\n", "    p_random_identation=0.3,\n", ")\n", "df = []\n", "n_modified_code_not_in_prompt, n_exceed_context_length = 0, 0\n", "\n", "for index, sample in enumerate(data[:1000]):\n", "    try:\n", "        _, aux_output = format_sample(\n", "            sample=sample,\n", "            tokenizer=tokenizer,\n", "            max_token_budget=max_token_budget,\n", "            suggested_edit_augmentator=suggested_edit_augmentator,\n", "        )\n", "    except ModifiedCodeNotInPrompt:\n", "        n_modified_code_not_in_prompt += 1\n", "        continue\n", "    except ExceedContextLength:\n", "        n_exceed_context_length += 1\n", "        continue\n", "    df.append(aux_output)\n", "    \n", "\n", "print(f\"n_modified_code_not_in_prompt: {n_modified_code_not_in_prompt}\")\n", "print(f\"n_exceed_context_length: {n_exceed_context_length}\")\n", "\n", "df = pd.DataFrame(df)\n", "df = df.fillna(0)\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "\n", "class Processor:\n", "    \"\"\"Processor for generating smart paste training data.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        max_token_budget,\n", "        p_no_identation,\n", "        p_random_identation,\n", "\n", "    ):\n", "        self.max_token_budget = max_token_budget\n", "        self.p_no_identation = p_no_identation\n", "        self.p_random_identation = p_random_identation\n", "        \n", "    \n", "    def initialize(self):\n", "        \"\"\"Initialize processor-wide resources..\"\"\"\n", "        # Initialize tokenizer and other resources\n", "        Processor.tokenizer = Qwen25CoderTokenizer()\n", "        Processor.max_token_budget = self.max_token_budget    \n", "        Processor.suggested_edit_augmentator = CodeAugmentation(\n", "            p_no_identation=self.p_no_identation,\n", "            p_random_identation=self.p_random_identation,\n", "        )\n", "\n", "    def __call__(self, sample):\n", "        \"\"\"Process a single sample.\n", "        \n", "        Args:\n", "            sample: Input sample to process\n", "            \n", "        Returns:\n", "            Tuple of (processed_data, stats_dict) or (None, error_stats_dict) on failure\n", "        \"\"\"\n", "        try:\n", "            tokens, aux_output = format_sample(\n", "                sample=sample,\n", "                tokenizer=self.tokenizer,\n", "                max_token_budget=self.max_token_budget,\n", "                suggested_edit_augmentator=self.suggested_edit_augmentator,\n", "            )\n", "        except ModifiedCodeNotInPrompt:\n", "            return None, {}\n", "        except ExceedContextLength:\n", "            return None, {}\n", "\n", "        padded_tokens = [x for x in tokens]\n", "        if len(padded_tokens) < self.max_token_budget:\n", "            padded_tokens.extend(\n", "                [-tokenizer.special_tokens.padding]\n", "                * (self.max_token_budget - len(padded_tokens))\n", "            )\n", "        assert len(padded_tokens) == self.max_token_budget\n", "\n", "        return torch.tensor(padded_tokens), aux_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "\n", "N_PROCESSES = 24\n", "P_NO_IDENTATION = 0.3\n", "P_RANDOM_IDENTATION = 0.3\n", "\n", "N_EVAL_SAMPLES = 128\n", "\n", "processor = Processor(\n", "    max_token_budget=MAX_TOKEN_BUDGET,\n", "    p_no_identation=P_NO_IDENTATION,\n", "    p_random_identation=P_RANDOM_IDENTATION,\n", ")\n", "\n", "builder = {\n", "    \"valid_emptysc\": MMapIndexedDatasetBuilder(str(OUTPUT_DIR / \"valid_emptysc\") + \".bin\", dtype=np.int32),\n", "    \"valid_non_emptysc\": MMapIndexedDatasetBuilder(str(OUTPUT_DIR / \"valid_non_emptysc\") + \".bin\", dtype=np.int32),\n", "    \"train\": MMapIndexedDatasetBuilder(str(OUTPUT_DIR / \"train\") + \".bin\", dtype=np.int32),\n", "}\n", "counter = {name: 0 for name in builder.keys()}\n", "limits = {\n", "    \"valid_emptysc\": N_EVAL_SAMPLES,\n", "    \"valid_non_emptysc\": N_EVAL_SAMPLES,\n", "    \"train\": 1_000_000_000,\n", "}\n", "\n", "aux_outputs = []\n", "n_skipped = 0\n", "\n", "with multiprocessing.Pool(N_PROCESSES, initializer=processor.initialize) as pool:\n", "    pbar = tqdm.tqdm(\n", "        pool.imap_unordered(processor, data),\n", "        total=len(data)\n", "    )\n", "    for tokens, aux_output in pbar:\n", "        if tokens is None:\n", "            n_skipped += 1\n", "            continue\n", "        aux_outputs.append(aux_output)\n", "        assert len(tokens) == MAX_TOKEN_BUDGET\n", "\n", "        if aux_output.get(\"empty_selected_code\", False):\n", "            builder_name = \"valid_emptysc\"            \n", "        else:\n", "            builder_name = \"valid_non_emptysc\"\n", "\n", "        if counter[builder_name] >= limits[builder_name]:\n", "            builder_name = \"train\"\n", "\n", "        counter[builder_name] += 1\n", "        builder[builder_name].add_item(tokens)\n", "        builder[builder_name].end_document()\n", "\n", "        metrics = counter.copy()\n", "        metrics[\"n_skipped\"] = n_skipped\n", "        pbar.set_postfix(**metrics)\n", "\n", "for name, b in builder.items():\n", "    print(f\"Finalizing {name}... {counter[name]} samples\")\n", "    b.finalize(str(OUTPUT_DIR / name) + \".idx\")\n", "print(f\"Skipped {n_skipped} samples\")\n", "\n", "df = pd.DataFrame(aux_outputs)\n", "df = df.fillna(0)\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import make_dataset\n", "\n", "ds = make_dataset(str(OUTPUT_DIR / \"train\"), impl=\"mmap\", skip_warmup=True)\n", "print(len(ds))\n", "\n", "from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "# tokens = ds[40]\n", "tokens = ds[100]\n", "tokens = [abs(x) for x in tokens]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
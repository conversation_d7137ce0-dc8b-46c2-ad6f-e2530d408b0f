"""Analyze how often response contains lines not present in input."""

import argparse
import time
import psutil
import gc
import re
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
import json
import random
from typing import Dict, Set, Tuple, List, Optional

from tqdm import tqdm

from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput
from base.diff_utils.diff_utils import File, compute_file_diff

# Global performance statistics
perf_stats = {}

INPUT_SAMPLES = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend/merged_smart_paste_responses.jsonl"
)
OUTPUT_SAMPLES = Path("data/samples_with_extra_lines.txt")  # Changed to .txt


@dataclass
class StepStats:
    """Statistics for a processing step."""

    total_time: float = 0.0
    total_memory: float = 0.0  # in MB
    calls: int = 0
    peak_memory: float = 0.0  # in MB

    def update(self, time_taken: float, memory_used: float):
        """Update stats with a new measurement."""
        self.total_time += time_taken
        self.total_memory += memory_used
        self.calls += 1
        self.peak_memory = max(self.peak_memory, memory_used)


def track_performance(step_name: str, stats: Dict[str, StepStats]):
    """Decorator to track performance of a function."""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

            result = func(*args, **kwargs)

            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

            time_taken = end_time - start_time
            memory_used = end_memory - start_memory

            if step_name not in stats:
                stats[step_name] = StepStats()
            stats[step_name].update(time_taken, memory_used)

            return result

        return wrapper

    return decorator


def detect_language(file_path: str) -> str:
    """Detect language from file path.

    Returns:
        Language identifier: python, cpp, java, js, go, rust, ruby, php
    """
    ext = file_path.lower().split(".")[-1]
    return {
        "py": "python",
        "pyi": "python",
        "cpp": "cpp",
        "cc": "cpp",
        "cxx": "cpp",
        "c": "c",
        "h": "cpp",
        "hpp": "cpp",
        "java": "java",
        "js": "js",
        "jsx": "js",
        "ts": "js",
        "tsx": "js",
        "go": "go",
        "rs": "rust",
        "rb": "ruby",
        "php": "php",
    }.get(ext, "unknown")


def remove_comments(line: str, language: str) -> str:
    """Remove comments from a line based on language.

    Args:
        line: The line to process
        language: Language identifier from detect_language()

    Returns:
        Line with comments removed
    """
    if language == "unknown":
        # Default to removing everything after #
        return line.split("#")[0]

    # Single-line comment markers by language
    comment_markers = {
        "python": "#",
        "cpp": "//",
        "c": "//",
        "java": "//",
        "js": "//",
        "go": "//",
        "rust": "//",
        "ruby": "#",
        "php": "//",  # Also has #, but // is more common
    }

    # Handle C-style /* */ comments
    if language in ("cpp", "c", "java", "js", "php"):
        # Remove /* ... */ on a single line
        while "/*" in line and "*/" in line:
            start = line.find("/*")
            end = line.find("*/", start) + 2
            line = line[:start] + line[end:]  # Remove extra space

    # Remove single-line comments
    marker = comment_markers.get(language, "#")
    if marker in line:
        line = line.split(marker)[0]

    return line


def normalize_line(line: str, language: str = "unknown") -> str:
    """Normalize a line by replacing consecutive whitespaces with a single space.

    Args:
        line: The line to normalize
        language: Language identifier from detect_language()

    Returns:
        Normalized line with comments removed and whitespace standardized
    """
    # Compile regex patterns for better performance
    comment_patterns = {
        "python": re.compile(r"#.*$"),
        "cpp": re.compile(r"//.*$|/\*.*?\*/"),
        "c": re.compile(r"//.*$|/\*.*?\*/"),
        "java": re.compile(r"//.*$|/\*.*?\*/"),
        "js": re.compile(r"//.*$|/\*.*?\*/"),
        "php": re.compile(r"//.*$|/\*.*?\*/"),
        "unknown": re.compile(r"#.*$"),
    }
    pattern = comment_patterns.get(language, comment_patterns["unknown"])

    # Special handling for C++ includes
    if language in ("cpp", "c", "java", "js", "php") and line.lstrip().startswith(
        "#include"
    ):
        # Only remove comments after the include
        if "//" in line:
            line = line[: line.find("//")].rstrip()
        return " ".join(line.split())

    # Remove comments and normalize whitespace in one pass
    line = pattern.sub("", line)
    return " ".join(line.split())


@track_performance("get_lines_set", perf_stats)
def get_lines_set(text: str, language: str = "unknown") -> Tuple[Set[str], Set[str]]:
    """Get set of non-empty lines from text, both regular and normalized.

    Optimized version that:
    1. Uses regex for comment removal
    2. Avoids repeated string operations
    3. Uses set comprehensions for better performance

    Args:
        text: The text to process
        language: Language identifier from detect_language()

    Returns:
        Tuple of (regular_lines_set, normalized_lines_set)
    """
    # Compile regex patterns for better performance
    comment_patterns = {
        "python": re.compile(r"#.*$"),
        "cpp": re.compile(r"//.*$|/\*.*?\*/"),
        "c": re.compile(r"//.*$|/\*.*?\*/"),
        "java": re.compile(r"//.*$|/\*.*?\*/"),
        "js": re.compile(r"//.*$|/\*.*?\*/"),
        "php": re.compile(r"//.*$|/\*.*?\*/"),
        "unknown": re.compile(r"#.*$"),
    }
    pattern = comment_patterns.get(language, comment_patterns["unknown"])

    # Split lines once
    lines = text.splitlines()

    # For regular lines, we want to strip trailing whitespace but keep comments
    regular_lines = set()
    normalized_lines = set()

    for line in lines:
        stripped = line.strip()
        if not stripped:
            continue

        # For C-style languages, remove comments from regular lines too
        if language in ("cpp", "c", "java", "js", "php"):
            # Special handling for #include lines
            if stripped.startswith("#include"):
                # Only remove comments after the include
                if "//" in line:
                    line = line[: line.find("//")].rstrip()
                regular_lines.add(line)
                normalized_lines.add(" ".join(line.split()))
                continue

            # Remove comments from non-include lines
            line_without_comments = pattern.sub("", line).rstrip()
            if line_without_comments.strip():
                regular_lines.add(line_without_comments)
                normalized_lines.add(" ".join(line_without_comments.split()))
        else:
            # For non-C++ languages, just strip trailing whitespace
            regular_lines.add(line.rstrip())
            # For normalized lines, remove comments and normalize whitespace
            line_without_comments = pattern.sub("", line).strip()
            if line_without_comments:
                normalized_lines.add(" ".join(line_without_comments.split()))

    return regular_lines, normalized_lines


def is_trailing_comma_variant(line1: str, line2: str) -> bool:
    """Check if two lines are the same except for a trailing comma."""
    # Remove comments before comparing
    if "#" in line1:
        line1 = line1.split("#")[0]
    if "#" in line2:
        line2 = line2.split("#")[0]

    line1 = line1.strip()
    line2 = line2.strip()

    # If lines are identical, not a trailing comma variant
    if line1 == line2:
        return False

    # If one line ends with comma and the other doesn't
    if line1.endswith(",") and not line2.endswith(","):
        return line1[:-1].strip() == line2.strip()
    if line2.endswith(",") and not line1.endswith(","):
        return line2[:-1].strip() == line1.strip()

    return False


def is_import_statement(line: str, *, _in_multiline_import: bool = False) -> bool:
    """Detect if a line is an import-like statement in various languages.

    Args:
        line: The line to check
        _in_multiline_import: Internal flag to track multiline import state

    Examples:
        Python imports:
        >>> is_import_statement("import foo")
        True
        >>> is_import_statement("from foo import bar")
        True

        C++ includes:
        >>> is_import_statement("#include <foo.h>")
        True
        >>> is_import_statement("# include <foo.h>")
        True
        >>> is_import_statement("#include \"foo.h\"")
        True
    """
    # Handle empty lines in multiline imports
    if not line.strip():
        return False

    line_stripped = line.lstrip()  # Handle leading whitespace

    # C/C++ includes - check first before removing comments
    if line_stripped.startswith("#"):
        # Remove any spaces between # and include
        after_hash = line_stripped[1:].lstrip()
        if after_hash.startswith("include"):
            # Verify it's a proper include with < > or " "
            after_include = after_hash[7:].lstrip()
            # Look for <...> or "..."
            if after_include.startswith(("<", '"')):
                # For <...>, check for closing >
                if after_include.startswith("<"):
                    return ">" in after_include
                # For "...", check for closing "
                else:
                    return '"' in after_include[1:]

    # Remove inline comments for other cases
    if "#" in line:
        line = line.split("#")[0].rstrip()
    elif "//" in line:
        line = line.split("//")[0].rstrip()

    line = line.strip()

    # Python imports
    if line.startswith(("import ", "from ")):
        return True
    # Python multiline imports - detect start
    if _in_multiline_import or (
        line.startswith(("from ", "import ")) and line.endswith("(")
    ):
        return True
    # Python multiline imports - indented lines in multiline context
    if _in_multiline_import and line.endswith((",", ")")):
        return True
    if line == ")":  # End of multiline import
        return _in_multiline_import

    # JavaScript/TypeScript imports
    if line.startswith(("import ", "require(")):
        return True
    if "require(" in line:  # Handle require with assignment
        return True

    # Java/Kotlin imports
    if line.startswith("import "):
        return True

    # Rust/C++ use statements
    if line.startswith(("use ", "extern crate ")):
        return True
    # Handle multiline use statements with braces
    if _in_multiline_import and (
        line.endswith(("{", "}", ","))  # Opening/closing brace or comma
        or "::" in line  # Path separator
        or line.strip(",").strip().isidentifier()  # Just an identifier
    ):
        return True

    # Go imports
    if line.startswith(('import "', "import (")):
        return True
    # Go multiline imports - indented lines with quotes
    if _in_multiline_import and line.startswith('"') and line.endswith('"'):
        return True

    # Ruby requires/imports
    if line.startswith(("require ", "require_relative ", "include ")):
        return True

    # PHP imports
    if line.startswith(
        ("require ", "include ", "require_once ", "include_once ", "use ")
    ):
        return True

    return False


@track_performance("analyze_extra_lines.detect_language", perf_stats)
def _detect_language(target_path: str) -> str:
    return detect_language(target_path)


@track_performance("analyze_extra_lines.find_normalized_extra", perf_stats)
def _find_normalized_extra_lines(
    response_lines: Set[str],
    input_lines: Set[str],
    input_normalized: Set[str],
    language: str,
) -> Tuple[int, Set[str]]:
    """Find normalized extra lines in response.

    Optimized version that:
    1. Uses regex for comment removal
    2. Precomputes normalized versions
    3. Uses set operations for better performance
    """
    # Compile regex patterns for better performance
    comment_patterns = {
        "python": re.compile(r"#.*$"),
        "cpp": re.compile(r"//.*$|/\*.*?\*/"),
        "c": re.compile(r"//.*$|/\*.*?\*/"),
        "java": re.compile(r"//.*$|/\*.*?\*/"),
        "js": re.compile(r"//.*$|/\*.*?\*/"),
        "php": re.compile(r"//.*$|/\*.*?\*/"),
        "unknown": re.compile(r"#.*$"),
    }
    pattern = comment_patterns.get(language, comment_patterns["unknown"])

    # Create normalized versions for all lines at once
    response_normalized = {
        line: normalize_line(line, language) for line in response_lines
    }

    # Find matching and extra lines using set operations
    matching_lines = 0
    normalized_extra_lines = set()

    # Create a map from normalized lines to original lines
    normalized_to_original = {}
    for line in response_lines:
        if not line.strip():  # Skip empty lines
            continue
        # For C++ mode, convert Python comments to C++ style
        original_line = line
        if language in ("cpp", "c", "java", "js", "php"):
            if "#" in line and not line.lstrip().startswith("#include"):
                original_line = line.replace("#", "//")
        # Get normalized version without comments
        line_without_comments = pattern.sub("", line).strip()
        normalized = " ".join(line_without_comments.split())
        if normalized:
            # Store the original line with comments
            if language in ("cpp", "c", "java", "js", "php"):
                # For C++ mode, make sure we have the C++ style comment
                if "//" in original_line:
                    normalized_to_original[normalized] = original_line.rstrip()
                else:
                    # Add the comment back in C++ style
                    comment_match = re.search(r"#.*$", line)
                    if comment_match:
                        comment = comment_match.group(0).replace("#", "//")
                        normalized_to_original[normalized] = original_line.replace(
                            "#" + comment[2:], comment
                        ).rstrip()
                    else:
                        normalized_to_original[normalized] = original_line.rstrip()
            else:
                normalized_to_original[normalized] = original_line.rstrip()

    for line, normalized in response_normalized.items():
        if not line.strip():  # Skip empty lines
            continue

        # Special handling for C++ includes
        if language in ("cpp", "c", "java", "js", "php"):
            if line.lstrip().startswith("#include"):
                # Only remove comments after the include
                if "//" in line:
                    line_without_comments = line[: line.find("//")].rstrip()
                else:
                    line_without_comments = line
                if line_without_comments in input_lines:
                    matching_lines += 1
                    continue
            else:
                # For non-include lines, normalize and compare without comments
                line_without_comments = pattern.sub("", line).strip()
                input_without_comments = {
                    pattern.sub("", line).strip() for line in input_lines
                }
                if line_without_comments in input_without_comments:
                    matching_lines += 1
                    continue
                # Also try with normalized whitespace
                line_normalized = " ".join(line_without_comments.split())
                input_normalized_without_comments = {
                    " ".join(line.split()) for line in input_without_comments
                }
                if line_normalized in input_normalized_without_comments:
                    matching_lines += 1
                    continue
        else:
            # For non-C++ languages, use regular matching
            if line in input_lines:
                matching_lines += 1
                continue

            # Try matching without comments
            line_without_comments = pattern.sub("", line).strip()
            input_without_comments = {
                pattern.sub("", line).strip() for line in input_lines
            }
            if line_without_comments in input_without_comments:
                matching_lines += 1
                continue

            # Try matching with normalized whitespace
            line_normalized = " ".join(line_without_comments.split())
            input_normalized_without_comments = {
                " ".join(line.split()) for line in input_without_comments
            }
            if line_normalized in input_normalized_without_comments:
                matching_lines += 1
                continue

        # If no match found, try normalized comparison
        if normalized:
            if normalized in input_normalized:
                matching_lines += 1
            else:
                # Skip include lines - they are handled separately
                if not (
                    language in ("cpp", "c", "java", "js", "php")
                    and line.lstrip().startswith("#include")
                ):
                    # Add the original line from response_lines
                    original_line = normalized_to_original.get(
                        normalized, line.rstrip()
                    )
                    normalized_extra_lines.add(original_line)

    return matching_lines, normalized_extra_lines


@track_performance("analyze_extra_lines", perf_stats)
def analyze_extra_lines(
    datum: dict,
) -> Tuple[int, int, int, Set[str], Set[str], Set[str], Set[str]]:
    """Analyze extra lines in response compared to input.

    In C++ mode, empty lines are preserved and count as matching lines."""
    prompt_input = datum["input"]
    if not isinstance(prompt_input, ResearchSmartPastePromptInput):
        prompt_input = ResearchSmartPastePromptInput.from_dict(prompt_input)

    # Detect language from target path
    language = _detect_language(prompt_input.target_path)

    # Get line sets
    target_lines, target_normalized = get_lines_set(
        prompt_input.target_file_content, language
    )
    code_block_lines, code_block_normalized = get_lines_set(
        prompt_input.code_block, language
    )
    response_lines, response_normalized = get_lines_set(datum["response"], language)

    input_lines = target_lines | code_block_lines
    input_normalized = target_normalized | code_block_normalized

    # Count empty lines
    empty_lines = 0
    if language in ("cpp", "c", "java", "js", "php"):
        # In C++ mode, empty lines are preserved and count as matching lines
        empty_lines = sum(
            1 for line in datum["response"].splitlines() if not line.strip()
        )

    # Find import lines - preserve original lines with comments
    import_lines = set()
    import_extra_lines = set()  # New set for extra import lines
    in_multiline_import = False
    for line in datum["response"].splitlines():
        if not line.strip():
            continue

        # Check if this is an import line
        is_import = is_import_statement(line, _in_multiline_import=in_multiline_import)
        if is_import:
            import_lines.add(line.rstrip())
            # Check if this import line is in input
            if line.rstrip() not in input_lines:
                import_extra_lines.add(line.rstrip())
            # Update multiline state
            if line.strip().endswith("("):
                in_multiline_import = True
            elif line.strip() == ")":
                in_multiline_import = False

    # Find normalized extra lines and count matching lines
    matching_lines, normalized_extra_lines = _find_normalized_extra_lines(
        response_lines=response_lines
        - import_lines,  # Exclude import lines from comparison
        input_lines=input_lines,
        input_normalized=input_normalized,
        language=language,  # Pass language parameter
    )

    # Add empty lines to matching lines in C++ mode
    matching_lines += empty_lines

    # Make sure import lines are not counted as extra lines
    non_import_extra_lines = normalized_extra_lines - import_lines

    # For C++ mode, make sure we have C++ style comments in the normalized extra lines
    if language in ("cpp", "c", "java", "js", "php"):
        # Convert Python comments to C++ style
        cpp_extra_lines = set()
        cpp_response = datum["response"]  # Get original response with comments
        for line in response_lines:
            if line in non_import_extra_lines:
                # Find the line in the original response with comments
                for resp_line in cpp_response.splitlines():
                    if resp_line.startswith(line.rstrip()):
                        cpp_extra_lines.add(resp_line)
                        break
        non_import_extra_lines = cpp_extra_lines
        normalized_extra_lines = (
            cpp_extra_lines | import_extra_lines
        )  # Use import_extra_lines instead of import_lines

    return (
        len(response_lines),
        len(non_import_extra_lines)
        + len(
            import_extra_lines
        ),  # Total extra lines = non-import extra + import extra
        matching_lines,
        non_import_extra_lines,  # Return non-import extra lines
        normalized_extra_lines,  # Return normalized extra lines
        input_lines,
        import_extra_lines,  # Return import extra lines instead of all import lines
    )


def main():
    """Main function to analyze the dataset."""
    parser = argparse.ArgumentParser(description="Analyze smart paste responses")
    parser.add_argument(
        "--max-samples", type=int, help="Maximum number of samples to process"
    )
    parser.add_argument(
        "--track-performance", action="store_true", help="Track performance metrics"
    )
    args = parser.parse_args()

    total_stats = defaultdict(int)
    samples_with_extra_lines = []
    samples_with_normalized_extra_lines = []

    processed = 0
    with INPUT_SAMPLES.open() as f:
        for line in tqdm(f):
            if args.max_samples and processed >= args.max_samples:
                break

            datum = json.loads(line)
            processed += 1

            (
                total_lines,
                extra_lines,
                matching_lines,
                extra_lines_set,
                normalized_extra_lines_set,
                input_lines,
                import_lines,
            ) = analyze_extra_lines(datum)

            total_stats["total_samples"] += 1
            total_stats["total_lines"] += total_lines
            total_stats["extra_lines"] += extra_lines
            total_stats["matching_lines"] += matching_lines
            total_stats["import_lines"] += len(import_lines)
            total_stats["non_import_extra_lines"] += len(extra_lines_set - import_lines)
            total_stats["normalized_extra_lines"] += len(normalized_extra_lines_set)

            if extra_lines > 0:
                total_stats["samples_with_extra_lines"] += 1
                if len(import_lines) > 0:
                    total_stats["samples_with_imports"] += 1

                # Store samples that have non-import extra lines
                if len(extra_lines_set - import_lines) > 0:
                    samples_with_extra_lines.append(
                        (datum, extra_lines_set, import_lines)
                    )

            if len(normalized_extra_lines_set) > 0:
                total_stats["samples_with_normalized_extra_lines"] += 1
                samples_with_normalized_extra_lines.append(
                    (
                        datum,
                        normalized_extra_lines_set,
                        import_lines,
                    )
                )

            # Force garbage collection periodically
            if processed % 100 == 0:
                gc.collect()

    # Print performance statistics if tracking is enabled
    if args.track_performance:
        print("\nPerformance Statistics:")
        for step_name, stats in perf_stats.items():
            avg_time = stats.total_time / stats.calls if stats.calls > 0 else 0
            avg_memory = stats.total_memory / stats.calls if stats.calls > 0 else 0
            print(f"\n{step_name}:")
            print(f"  Total time: {stats.total_time:.2f}s")
            print(f"  Average time per call: {avg_time:.4f}s")
            print(f"  Total memory delta: {stats.total_memory:.2f}MB")
            print(f"  Average memory delta per call: {avg_memory:.2f}MB")
            print(f"  Peak memory delta: {stats.peak_memory:.2f}MB")
            print(f"  Number of calls: {stats.calls}")

    # Print statistics
    print("\nAnalysis Results:")
    print(f"Total samples analyzed: {total_stats['total_samples']}")
    print(
        f"Samples with extra lines (before normalization): {total_stats['samples_with_extra_lines']} "
        f"({total_stats['samples_with_extra_lines']/total_stats['total_samples']*100:.2f}%)"
    )
    print(
        f"Samples with extra lines (after normalization): {total_stats['samples_with_normalized_extra_lines']} "
        f"({total_stats['samples_with_normalized_extra_lines']/total_stats['total_samples']*100:.2f}%)"
    )
    print(
        f"Samples with extra import statements: {total_stats['samples_with_imports']} "  # Changed text
        f"({total_stats['samples_with_imports']/total_stats['total_samples']*100:.2f}%)"
    )
    print(f"Total lines in responses: {total_stats['total_lines']}")
    print(
        f"Extra lines in responses (before normalization): {total_stats['extra_lines']} "
        f"({total_stats['extra_lines']/total_stats['total_lines']*100:.2f}%)"
    )
    print(
        f"Extra lines in responses (after normalization): {total_stats['normalized_extra_lines']} "
        f"({total_stats['normalized_extra_lines']/total_stats['total_lines']*100:.2f}%)"
    )
    print(
        f"Extra import lines in responses: {total_stats['import_lines']} "  # Changed text
        f"({total_stats['import_lines']/total_stats['total_lines']*100:.2f}%)"
    )
    print(
        f"Non-import extra lines: {total_stats['non_import_extra_lines']} "
        f"({total_stats['non_import_extra_lines']/total_stats['total_lines']*100:.2f}%)"
    )
    print(
        f"Matching lines in responses: {total_stats['matching_lines']} "
        f"({total_stats['matching_lines']/total_stats['total_lines']*100:.2f}%)"
    )

    # Save samples with normalized extra lines in human readable format
    if samples_with_normalized_extra_lines:
        selected_samples = random.sample(
            samples_with_normalized_extra_lines,
            min(100, len(samples_with_normalized_extra_lines)),
        )
        OUTPUT_SAMPLES.parent.mkdir(parents=True, exist_ok=True)
        with OUTPUT_SAMPLES.open("w") as f:
            f.write(
                f"Analysis of {len(selected_samples)} random samples with extra lines (after normalization)\n\n"
            )
            for (
                datum,
                extra_lines,
                import_lines,
            ) in selected_samples:
                prompt_input = ResearchSmartPastePromptInput.from_dict(datum["input"])
                target_file = File(
                    path=prompt_input.target_path,
                    contents=prompt_input.target_file_content,
                )
                response_file = File(
                    path=prompt_input.target_path,
                    contents=datum["response"],
                )

                # Print sample info
                f.write(f"Sample {total_stats['total_samples']}\n")
                f.write(f"Target path: {prompt_input.target_path}\n")
                f.write(f"Language: {detect_language(prompt_input.target_path)}\n\n")

                # Print diff
                f.write("Diff between target and response:\n")
                diff = compute_file_diff(target_file, response_file)
                f.write(diff)
                f.write("\n")

                # Print extra lines
                f.write("Extra lines found:\n")
                for line in sorted(extra_lines):
                    if line in import_lines:
                        f.write(f"[IMPORT] {line}\n")
                    else:
                        f.write(f"[EXTRA]  {line}\n")
                f.write("\n")

                # Print code block
                f.write("Original code block:\n")
                f.write(prompt_input.code_block)
                f.write("\n")

                # Print separator
                f.write("=" * 80 + "\n\n")
        print(
            f"\nSaved {len(selected_samples)} random samples with normalized extra lines to: {OUTPUT_SAMPLES}"
        )


if __name__ == "__main__":
    main()

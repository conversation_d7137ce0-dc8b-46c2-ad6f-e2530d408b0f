"""Tests for analyze_extra_lines.py."""

from pathlib import Path
import textwrap

from experimental.yury.smart_paste.analyze_extra_lines import (
    get_lines_set,
    is_import_statement,
    find_context_in_text,
    analyze_extra_lines,
    format_sample_with_context,
    normalize_line,
    detect_language,
    remove_comments,
)
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput


def test_detect_language():
    """Test detect_language function."""
    assert detect_language("foo.py") == "python"
    assert detect_language("foo.cpp") == "cpp"
    assert detect_language("foo.h") == "cpp"
    assert detect_language("foo.java") == "java"
    assert detect_language("foo.js") == "js"
    assert detect_language("foo.ts") == "js"
    assert detect_language("foo.go") == "go"
    assert detect_language("foo.rs") == "rust"
    assert detect_language("foo.rb") == "ruby"
    assert detect_language("foo.php") == "php"
    assert detect_language("foo.unknown") == "unknown"


def test_remove_comments():
    """Test remove_comments function."""
    # Python comments
    assert remove_comments("x = 1  # comment", "python") == "x = 1  "
    assert remove_comments("# Full line comment", "python") == ""

    # C++ comments
    assert remove_comments("x = 1;  // comment", "cpp") == "x = 1;  "
    assert remove_comments("/* comment */ x = 1;", "cpp") == " x = 1;"
    assert (
        remove_comments("x = 1; /* inline comment */ y = 2;", "cpp") == "x = 1;  y = 2;"
    )

    # Java comments
    assert remove_comments("x = 1;  // comment", "java") == "x = 1;  "
    assert remove_comments("/* comment */ x = 1;", "java") == " x = 1;"

    # Unknown language (defaults to Python-style)
    assert remove_comments("x = 1  # comment", "unknown") == "x = 1  "


def test_normalize_line():
    """Test normalize_line function."""
    # Basic whitespace normalization
    assert normalize_line("  foo   bar  ") == "foo bar"
    assert normalize_line("\tfoo\t\tbar\n") == "foo bar"
    assert normalize_line("foo") == "foo"
    assert normalize_line("") == ""

    # Python comments
    assert normalize_line("  foo   bar  # comment", "python") == "foo bar"
    assert normalize_line("# Full line comment", "python") == ""

    # C++ comments
    assert normalize_line("  foo   bar  // comment", "cpp") == "foo bar"
    assert normalize_line("/* comment */ foo /* more */ bar", "cpp") == "foo bar"

    # Java comments
    assert normalize_line("  foo   bar  // comment", "java") == "foo bar"
    assert normalize_line("/* comment */ foo /* more */ bar", "java") == "foo bar"


def test_get_lines_set():
    """Test get_lines_set function."""
    text = textwrap.dedent("""
        line1  # comment
        line2  // not a comment in python
        line3  /* also not a comment in python */
          line4
    """)
    # Test with Python
    regular_lines, normalized_lines = get_lines_set(text, "python")
    assert regular_lines == {
        "line1  # comment",
        "line2  // not a comment in python",
        "line3  /* also not a comment in python */",
        "  line4",  # leading whitespace preserved, trailing whitespace stripped
    }
    assert normalized_lines == {
        "line1",  # comment is removed
        "line2 // not a comment in python",  # // is not a comment in Python
        "line3 /* also not a comment in python */",  # /* */ is not a comment in Python
        "line4",
    }

    # Test with C++
    regular_lines, normalized_lines = get_lines_set(text, "cpp")
    assert regular_lines == {
        "line1  # comment",  # # is not a comment in C++
        "line2",  # // comment is removed
        "line3",  # /* */ comment is removed
        "  line4",  # leading whitespace preserved
    }
    assert normalized_lines == {
        "line1 # comment",  # # is not a comment in C++
        "line2",  # // comment is removed
        "line3",  # /* */ comment is removed
        "line4",  # whitespace normalized
    }


def test_is_import_statement():
    """Test is_import_statement function."""
    # Python imports
    assert is_import_statement("import foo")
    assert is_import_statement("from foo import bar")
    assert is_import_statement("from foo import (")
    assert is_import_statement("    bar,", _in_multiline_import=True)
    assert is_import_statement(")", _in_multiline_import=True)

    # JavaScript imports
    assert is_import_statement("import foo from 'bar'")
    assert is_import_statement("const foo = require('bar')")

    # Java imports
    assert is_import_statement("import com.foo.bar;")

    # C includes
    assert is_import_statement("#include <foo.h>")
    assert is_import_statement("# include <foo.h>")

    # C++ use statements
    assert is_import_statement("use std::io;")
    assert is_import_statement("use std::{ os::unix::io::{AsFd, BorrowedFd} };")
    assert is_import_statement("use std::{", _in_multiline_import=True)
    assert is_import_statement("    os::unix::io::{", _in_multiline_import=True)
    assert is_import_statement("        AsFd,", _in_multiline_import=True)
    assert is_import_statement("        BorrowedFd", _in_multiline_import=True)
    assert is_import_statement("    },", _in_multiline_import=True)
    assert is_import_statement("};", _in_multiline_import=True)

    # Not imports
    assert not is_import_statement("def foo():")
    assert not is_import_statement("foo = bar")
    assert not is_import_statement("")
    assert not is_import_statement("    ", _in_multiline_import=True)
    assert not is_import_statement("foo::bar()")  # :: without use
    assert not is_import_statement("std::io::stdin()")  # :: in function call


def test_find_context_in_text():
    """Test finding context around similar lines."""
    text = textwrap.dedent("""
        def foo():
            x = 1
            y = 2
            result = x + y  # Best match
            return result

        def bar():
            a = 10
            b = 20
            total = a + b  # Similar but not best match
            return total
    """).strip()

    # Test finding context for a line with similar structure
    extra_line = "result = x + y"  # Exact match
    context = find_context_in_text(text, extra_line, context_lines=2)
    assert len(context) > 0
    assert any("result = x + y" in line for line in context)

    # Test finding context for a function definition
    extra_line = "def foo():"  # Exact match
    context = find_context_in_text(text, extra_line, context_lines=1)
    assert len(context) > 0
    assert any("def foo():" in line for line in context)

    # Test finding context for a return statement
    extra_line = "return result"  # Exact match
    context = find_context_in_text(text, extra_line, context_lines=1)
    assert len(context) > 0
    assert any("return result" in line for line in context)


def test_analyze_extra_lines():
    """Test analyze_extra_lines function."""
    target_content = textwrap.dedent("""
        def foo():  # Function
            x = 1  # Initialize
            y = 2  # Another value
            return x  # Return
    """).strip()

    code_block = textwrap.dedent("""
        def bar():  # Another function
            x = 1  # Initialize
            y = 2  # Another value
            return y  # Return it
    """).strip()

    response = textwrap.dedent("""
        from math import sqrt  # Import
        def foo():  # Function
            x = 1  # Initialize
            y = 2  # Another value
            return x  # Return
        def baz():  # Different function
            result  =   x   +   y  # This line has extra spaces
    """).strip()

    datum = {
        "input": ResearchSmartPastePromptInput(
            target_file_content=target_content,
            code_block=code_block,
            target_path="test.py",  # Python file
            path="test.py",
            prefix="",
            selected_code="",
            suffix="",
            chat_history=[],
            prefix_begin=0,
            suffix_end=0,
            retrieved_chunks=[],
        ),
        "response": response,
    }

    (
        total_lines,
        extra_lines,
        matching_lines,
        extra_lines_set,
        normalized_extra_lines_set,
        input_lines,
        import_lines,
    ) = analyze_extra_lines(datum)

    # Count lines
    assert total_lines == 7  # Total non-empty lines in response
    assert matching_lines == 4  # Lines that match input exactly or after normalization
    assert len(import_lines) == 1  # One import line
    assert len(extra_lines_set) == 2  # Two lines are truly different

    # Check specific lines
    assert "from math import sqrt  # Import" in import_lines
    assert "    result  =   x   +   y  # This line has extra spaces" in extra_lines_set
    assert "def baz():  # Different function" in extra_lines_set

    # Check total extra lines (import + extra lines)
    assert extra_lines == 3  # One import line and two extra lines

    # Check that the counts add up correctly
    assert matching_lines + len(import_lines) + len(extra_lines_set) == total_lines

    # Test with C++ file
    datum["input"] = ResearchSmartPastePromptInput(
        target_file_content=target_content.replace("#", "//"),
        code_block=code_block.replace("#", "//"),
        target_path="test.cpp",  # C++ file
        path="test.cpp",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )
    # Replace Python import with C++ include and convert comments
    cpp_response = response.replace(
        "from math import sqrt  # Import", "#include <cmath>  // Import"
    )
    # Convert Python comments to C++ style, but preserve #include
    cpp_response_lines = []
    for line in cpp_response.splitlines():
        if line.lstrip().startswith("#include"):
            cpp_response_lines.append(line)  # Keep include lines unchanged
        else:
            # Convert Python comments to C++ style
            if "#" in line:
                comment_start = line.find("#")
                code = line[:comment_start]
                comment = line[comment_start:].replace("#", "//")
                cpp_response_lines.append(code + comment)
            else:
                cpp_response_lines.append(line)
    cpp_response = "\n".join(cpp_response_lines)
    datum["response"] = cpp_response

    # Add debug prints
    print("\nDebug: cpp_response lines:")
    for line in cpp_response.splitlines():
        print(f"  {line}")

    (
        total_lines,
        extra_lines,
        matching_lines,
        extra_lines_set,
        normalized_extra_lines_set,
        input_lines,
        import_lines,
    ) = analyze_extra_lines(datum)

    # Count lines - in C++ mode
    assert total_lines == 7  # Total non-empty lines in response
    assert matching_lines == 4  # Lines that match input exactly or after normalization
    assert len(import_lines) == 1  # One import line
    assert len(extra_lines_set) == 2  # Two lines are truly different

    # Add debug prints
    print("\nDebug: extra_lines_set contents:")
    for line in extra_lines_set:
        print(f"  {line}")
    print("\nDebug: import_lines contents:")
    for line in import_lines:
        print(f"  {line}")

    # Check specific lines - in C++ mode, comments are preserved
    assert any(
        "#include <cmath>  // Import" in line for line in import_lines
    )  # Import line
    # In C++ mode, we need to check both with and without comments
    assert any(
        line.rstrip() == "def baz():  // Different function"
        for line in normalized_extra_lines_set  # Use normalized_extra_lines_set
    )  # Extra line
    assert any(
        line.rstrip() == "    result  =   x   +   y  // This line has extra spaces"
        for line in normalized_extra_lines_set  # Use normalized_extra_lines_set
    )  # Extra line

    # Check total extra lines (import + extra lines)
    assert extra_lines == 3  # One import line and two extra lines

    # Check that the counts add up correctly
    assert matching_lines + len(import_lines) + len(extra_lines_set) == total_lines


def test_format_sample_with_context():
    """Test format_sample_with_context function."""
    target_content = textwrap.dedent("""
        def calculate():
            x = 1
            y = x + 2
            return y
    """).strip()

    code_block = textwrap.dedent("""
        def process():
            a = 10
            b = a + 20
            return b
    """).strip()

    response = textwrap.dedent("""
        from math import sqrt
        def calculate():
            x = 1
            y = x + 2  # Same as input
            z = x + y  # Extra line
            return y  # Same as input
    """).strip()

    datum = {
        "input": ResearchSmartPastePromptInput(
            target_file_content=target_content,
            code_block=code_block,
            target_path="test.py",
            path="test.py",
            prefix="",
            selected_code="",
            suffix="",
            chat_history=[],
            prefix_begin=0,
            suffix_end=0,
            retrieved_chunks=[],
        ),
        "response": response,
    }

    # Get the extra lines first
    (
        _,
        _,
        _,
        extra_lines_set,
        normalized_extra_lines_set,
        _,
        import_lines,
    ) = analyze_extra_lines(datum)

    # Format the sample
    result = format_sample_with_context(datum, extra_lines_set, import_lines)

    # Check that all sections are present
    assert "Extra import lines:" in result
    assert "from math import sqrt" in result
    assert "Other extra lines:" in result
    assert "z = x + y" in result  # Check for extra line

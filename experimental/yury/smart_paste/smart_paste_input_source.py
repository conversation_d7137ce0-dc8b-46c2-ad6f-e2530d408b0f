import dataclasses
from pathlib import Path
import re
import tqdm
import json
import numpy as np
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput
from base.prompt_format.common import Exchange


SMART_PASTE_INPUTS_V1 = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl"
)

SMART_PASTE_INPUTS_V2_1 = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl"
)
SMART_PASTE_INPUTS_V2_2 = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl"
)


WHITESPACE_NO_NEWLINE = r"[ \t\f\v]*"

CODEBLOCK_XML_PATTERN = (
    r"<augment_code_snippet path=\"([^\"]+)\" mode=\"([^\"]+)\">\n"
    + WHITESPACE_NO_NEWLINE
    + r"```(\w*)\n"
    + r"(.*?\n)"
    + WHITESPACE_NO_NEWLINE
    + r"```\n"
    + WHITESPACE_NO_NEWLINE
    + r"</augment_code_snippet>"
)


@dataclasses.dataclass(frozen=True)
class Codeblock:
    language: str | None
    path: str | None
    mode: str | None
    code: str


def detect_markdown_within_codeblocks(chat_response):
    pattern = r"```(\w+)? path=([^\s]+) mode=(\w+)\n"
    matches = list(re.findall(pattern, chat_response, re.DOTALL))
    n_codeblocks_with_metadata = len(matches)
    n_triple_backticks = chat_response.count("```")
    return (n_codeblocks_with_metadata * 2 - n_triple_backticks) > 0


def extract_edit_markdown_blocks_from_data_v1(sample):
    text = sample.chat_history[-1].response_text
    pattern = r"```(\w+)?\s*(?:path=([^\s]+))?\s*(?:mode=(\w+))?\n(.*?)```"
    matches = list(re.findall(pattern, text, re.DOTALL))
    matches = [m for m in matches if m[2] == "EDIT"]
    return [
        Codeblock(
            language=m[0],
            path=m[1],
            mode=m[2],
            code=m[3],
        )
        for m in matches
    ]


def load_smart_paste_inputs_v1(
    path: Path = SMART_PASTE_INPUTS_V1,
) -> dict[str, ResearchSmartPastePromptInput]:
    smart_paste_inputs = {}

    n_skipped = 0

    with path.open("r") as f:
        pbar = tqdm.tqdm(f, desc=f"Loading {path}")
        for line in pbar:
            datum = json.loads(line)

            if detect_markdown_within_codeblocks(datum["chat_response"]):
                n_skipped += 1
                pbar.set_postfix({"n_skipped": n_skipped})
                continue

            request_id = datum["uuid"]
            before_file_contents = datum["before_file"]["contents"]
            selected_code_crange = datum["selected_code_crange"]

            smart_paste_input = ResearchSmartPastePromptInput(
                request_id=request_id,
                path=datum["before_file"]["path"],
                prefix=before_file_contents[: selected_code_crange["start"]],
                selected_code=before_file_contents[
                    selected_code_crange["start"] : selected_code_crange["stop"]
                ],
                suffix=before_file_contents[selected_code_crange["stop"] :],
                code_block=datum["codeblock"],
                chat_history=[
                    Exchange(
                        request_id=request_id,
                        request_message=datum["instruction"],
                        response_text=datum["chat_response"],
                    )
                ],
                prefix_begin=0,
                suffix_end=len(before_file_contents),
                retrieved_chunks=[],
                context_code_exchange_request_id=request_id,
                target_path=datum["before_file"]["path"],
                target_file_content=before_file_contents,
                aux={
                    "commit": {"after_file": datum["aux"]["original_commit_after_file"]}
                },
            )
            smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input

    return smart_paste_inputs


def load_smart_paste_inputs_v2(path: Path) -> dict[str, ResearchSmartPastePromptInput]:
    smart_paste_inputs = {}
    with path.open("r") as f:
        for line in tqdm.tqdm(f):
            smart_paste_input = ResearchSmartPastePromptInput.from_json(line)
            assert (
                smart_paste_input.request_id
                == smart_paste_input.context_code_exchange_request_id
            )
            assert (
                smart_paste_input.request_id
                == smart_paste_input.chat_history[-1].request_id
            )
            assert len(smart_paste_input.chat_history) in [1, 2], len(
                smart_paste_input.chat_history
            )
            smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input
    return smart_paste_inputs


def load_smart_paste_inputs_v2_1():
    return load_smart_paste_inputs_v2(SMART_PASTE_INPUTS_V2_1)


def load_smart_paste_inputs_v2_2():
    return load_smart_paste_inputs_v2(SMART_PASTE_INPUTS_V2_2)


def parse_codeblocks(text):
    results = []
    matches = re.finditer(CODEBLOCK_XML_PATTERN, text, re.DOTALL)
    for match in matches:
        mode = match.group(2)
        assert mode in ["EDIT", "EXCERPT"]
        if mode == "EDIT":
            results.append(
                Codeblock(
                    language=match.group(3),
                    path=match.group(1),
                    mode=match.group(2),
                    code=match.group(4),
                )
            )
    return results


def extract_edit_markdown_blocks_from_data_v2(sample):
    text = sample.chat_history[-1].response_text
    codeblocks = parse_codeblocks(text)
    edit_codeblock = [codeblock for codeblock in codeblocks if codeblock.mode == "EDIT"]
    return edit_codeblock

"""Data augmentations utilities for smart paste training."""

import dataclasses
import numpy as np

from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.diff_utils.diff_utils import File

from research.core.str_diff import build_str_diff, StrDiff, NoopSpan


def compute_line_indentations(lines: list[str]) -> list[int | None]:
    indent_levels = list[int | None]()
    for line in lines:
        if line.isspace():
            indent_levels.append(None)
        else:
            indent_levels.append(len(line) - len(line.lstrip()))
    return indent_levels


def reduce_identation(lines: list[str], k: int) -> list[str]:
    new_lines = list[str]()
    for line in lines:
        if line.isspace():
            new_lines.append(line)
        else:
            existing_indentation = len(line) - len(line.lstrip())
            assert existing_indentation >= k
            new_lines.append(line[k:])
    return new_lines


@dataclasses.dataclass(frozen=True)
class CodeAugmentation:
    """Augmentation that modifies the suggested edit."""

    p_no_identation: float
    p_random_identation: float

    def augment(
        self, code: str, random_state: np.random.RandomState | None = None
    ) -> str:
        """Augment the suggested edit."""
        lines = code.splitlines(keepends=True)
        min_indentation = [x for x in compute_line_indentations(lines) if x is not None]
        if len(min_indentation) == 0:
            return code

        min_indentation = min(min_indentation)
        if random_state is not None:
            coin_flip = random_state.random()
        else:
            coin_flip = np.random.random()
        if coin_flip < self.p_no_identation:
            lines = reduce_identation(lines, min_indentation)
        elif coin_flip < self.p_no_identation + self.p_random_identation:
            k = np.random.randint(0, min_indentation + 1)
            lines = reduce_identation(lines, k)
        else:
            pass
        return "".join(lines)


def get_union_span_range_in_before(
    str_diff: StrDiff, n_first_non_noop_spans_to_drop: int = 0
):
    range_start, range_stop = None, None
    n_first_non_noop_spans_to_dropped = 0
    for span, before_range in zip(str_diff.spans, str_diff.span_ranges_in_before):
        if isinstance(span, NoopSpan):
            continue
        if n_first_non_noop_spans_to_dropped < n_first_non_noop_spans_to_drop:
            n_first_non_noop_spans_to_dropped += 1
            continue
        range_start = before_range.start
        break
    if range_start is None:
        return None
    for span, before_range in zip(
        reversed(str_diff.spans), reversed(str_diff.span_ranges_in_before)
    ):
        if isinstance(span, NoopSpan):
            continue
        range_stop = before_range.stop
        break
    assert range_stop is not None
    return CharRange(range_start, range_stop)


def maybe_fix_line_index(line_map: LineMap, line_index: int):
    line_index = max(0, line_index)
    line_index = min(line_map.size_lines(), line_index)
    return line_index


def expand_range(
    line_map: LineMap, char_range: CharRange, n_lines_before: int, n_lines_after: int
):
    line_range = line_map.crange_to_lrange(char_range)
    new_start = maybe_fix_line_index(line_map, line_range.start - n_lines_before)
    new_stop = maybe_fix_line_index(line_map, line_range.stop + n_lines_after)
    new_stop = max(new_start, new_stop)
    return line_map.lrange_to_crange(LineRange(new_start, new_stop))


def sample_random_line(char_range: CharRange, line_map: LineMap):
    line_range = line_map.crange_to_lrange(char_range)
    if line_range.start >= line_range.stop:
        raise ValueError(
            f"CharRange {char_range} and line range {line_range} are empty"
        )
    assert line_range.start < line_range.stop, f"{line_range.start} {line_range.stop}"
    line_index = np.random.randint(line_range.start, line_range.stop)
    new_char_range = line_map.lrange_to_crange(LineRange(line_index, line_index + 1))
    assert char_range.contains(new_char_range)
    return new_char_range


@dataclasses.dataclass
class BeforeAfterFiles:
    before_file: File
    after_file: File

    def __post_init__(self):
        self.before_line_map = LineMap(self.before_file.contents)
        self.str_diff = build_str_diff(
            self.before_file.contents, self.after_file.contents, "precise_linediff"
        )

    def get_before_contents(self, span: CharRange) -> str:
        return self.before_file.contents[span.start : span.stop]

    def get_after_contents(self, span: CharRange) -> str:
        return self.after_file.contents[span.start : span.stop]

    def get_before_size_lines(self) -> int:
        return self.before_line_map.size_lines()

    def get_before_char_index(self, line: int, column: int = 0) -> int:
        return self.before_line_map.get_char_index(line, column)


@dataclasses.dataclass(frozen=True)
class SelectedCodeSamplerOutput:
    """Output of the selected code sampler."""

    selected_code: CharRange
    all_changes_span: CharRange


@dataclasses.dataclass(frozen=True)
class SelectedCodeSampler:
    expected_n_hunks_to_drop_from_the_beginning: int
    p_selected_code_empty: float
    p_selected_code_single_line: float
    p_expansion: float  # probability of expanding the selected code vs shrinking it
    expected_n_lines_to_add: int
    expected_n_lines_to_drop: int

    def _sample_n_spans_to_drop(self, str_diff: StrDiff):
        noop_spans = [span for span in str_diff.spans if isinstance(span, NoopSpan)]
        n_non_noop_spans = len(str_diff.spans) - len(noop_spans)
        n_spans_to_drop = (
            np.random.geometric(
                p=1.0 / (self.expected_n_hunks_to_drop_from_the_beginning + 1.0)
            )
            - 1
        )
        n_spans_to_drop = min(n_spans_to_drop, n_non_noop_spans - 1)
        return n_spans_to_drop

    def _sample_n_lines_to_expand(self):
        if np.random.random() < self.p_expansion:
            return np.random.geometric(1.0 / (1 + self.expected_n_lines_to_add)) - 1
        else:
            return -(np.random.geometric(1.0 / (1 + self.expected_n_lines_to_drop)) - 1)

    def sample_selected_code(
        self, sample: BeforeAfterFiles
    ) -> SelectedCodeSamplerOutput:
        all_changes_span = get_union_span_range_in_before(sample.str_diff, 0)
        assert all_changes_span is not None
        # all_changes_span_is_not_empty = all_changes_span.start < all_changes_span.stop
        n_spans_to_drop = self._sample_n_spans_to_drop(sample.str_diff)
        selected_code = get_union_span_range_in_before(sample.str_diff, n_spans_to_drop)
        assert selected_code is not None
        selected_code_is_not_empty = selected_code.start < selected_code.stop
        coin_flip = np.random.random()
        if coin_flip < self.p_selected_code_empty:
            # Old behavior: pick beginning of a random line within a file.
            # New behavior: pick beginning of a random line within a selected region.
            # # Pick the beginning of a random line within a file.
            # n_lines = sample.before_line_map.size_lines()
            # if n_lines > 0:
            #     random_line = np.random.randint(n_lines)
            #     beginning_of_random_line = sample.get_before_char_index(random_line, 0)
            # else:
            #     beginning_of_random_line = 0
            # selected_code = CharRange(beginning_of_random_line, beginning_of_random_line)

            # Incredibly inefficient, but simple solution
            all_line_starts, location_candidates = [], []
            for line_index in range(sample.before_line_map.size_lines()):
                beginning_of_line = sample.get_before_char_index(line_index, 0)
                all_line_starts.append(beginning_of_line)
                if selected_code_is_not_empty:
                    if (
                        selected_code.start >= beginning_of_line
                        and beginning_of_line < selected_code.stop
                    ):
                        location_candidates.append(beginning_of_line)
                else:
                    if selected_code.start == beginning_of_line:
                        location_candidates.append(beginning_of_line)
            # Special case when selected  code points at the end of the file (meaning that the actual change was appending some new code)
            if len(location_candidates) == 0 and selected_code.start == len(
                sample.before_file.contents
            ):
                location_candidates.append(len(sample.before_file.contents))
            if len(location_candidates) == 0:
                raise ValueError(
                    f"Selected code: {selected_code}, all_changes_span: {all_changes_span}, all_line_starts: {all_line_starts}"
                )
            assert len(location_candidates) > 0
            random_beginning_of_line = np.random.choice(location_candidates)
            selected_code = CharRange(
                random_beginning_of_line, random_beginning_of_line
            )
        elif (
            coin_flip < self.p_selected_code_empty + self.p_selected_code_single_line
            and selected_code_is_not_empty
        ):
            selected_code = sample_random_line(selected_code, sample.before_line_map)
        else:
            selected_code = expand_range(
                sample.before_line_map,
                selected_code,
                self._sample_n_lines_to_expand(),
                self._sample_n_lines_to_expand(),
            )
        return SelectedCodeSamplerOutput(selected_code, all_changes_span)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "\n", "COMMITS_WITH_INSTRUCTIONS_WITHOUT_UUID_PATH = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/02_instruction_stage/commits_with_instructions_no_uuid.jsonl\"\n", ")\n", "COMMITS_WITH_INSTRUCTIONS_PATH = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/02_instruction_stage/commits_with_instructions.jsonl\"\n", ")\n", "EXISTING_DATA_PATH = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl\"\n", ")\n", "\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "CHAT_REQUESTS_PATH = OUTPUT_DIR / \"chat_requests.jsonl\"\n", "BATCH_CHAT_REQUESTS_PATH = OUTPUT_DIR / \"batch_chat_requests.jsonl\"\n", "BATCH_CHAT_REQUESTS_PATH_ITER2 = OUTPUT_DIR / \"batch_chat_requests_iter2.jsonl\"\n", "\n", "\n", "OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_inputs.jsonl\"\n", "OUTPUT_FILE_ITER2 = OUTPUT_DIR / \"smart_paste_inputs_iter2.jsonl\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import tqdm\n", "from typing import Any\n", "\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "\n", "def get_id(datum: dict[str, Any]) -> int:\n", "    return hash(\n", "        (\n", "            datum[\"before_file\"][\"path\"],\n", "            datum[\"before_file\"][\"contents\"],\n", "            datum[\"instruction\"],\n", "        )\n", "    )\n", "\n", "\n", "n_lines, hash_to_uuid = 0, {}\n", "\n", "with EXISTING_DATA_PATH.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line)\n", "        hash_to_uuid[get_id(datum)] = datum[\"uuid\"]\n", "        n_lines += 1\n", "\n", "print(\n", "    f\"Loaded {n_lines} lines from {EXISTING_DATA_PATH}, {len(hash_to_uuid)} unique ids\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import tqdm\n", "import json\n", "import numpy as np\n", "from base.prompt_format.common import Exchange\n", "\n", "\n", "EXISTING_SMART_PASTE_INPUTS = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\"\n", ")\n", "\n", "existing_smart_paste_inputs_uuid = set()\n", "\n", "with EXISTING_SMART_PASTE_INPUTS.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        smart_paste_input = ResearchSmartPastePromptInput.from_json(line)\n", "        assert (\n", "            smart_paste_input.request_id not in existing_smart_paste_inputs_uuid\n", "        ), smart_paste_input.request_id\n", "        existing_smart_paste_inputs_uuid.add(smart_paste_input.request_id)\n", "\n", "print(f\"Loaded {len(existing_smart_paste_inputs_uuid)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import json\n", "import uuid\n", "\n", "n_lines, n_exists, n_failed, no_change = 0, 0, 0, 0\n", "data = []\n", "\n", "with COMMITS_WITH_INSTRUCTIONS_PATH.open(\"w\") as f_out:\n", "    with COMMITS_WITH_INSTRUCTIONS_WITHOUT_UUID_PATH.open(\"r\") as f:\n", "        for line in tqdm.tqdm(f):\n", "            datum = json.loads(line)\n", "            n_lines += 1\n", "            if datum[\"status\"] != \"success\":\n", "                n_failed += 1\n", "                continue\n", "\n", "            # if datum[\"before_file\"][\"contents\"] == datum[\"after_file\"][\"contents\"]:\n", "            #     no_change += 1\n", "            #     continue\n", "\n", "            hash_id = get_id(datum)\n", "            if hash_id in hash_to_uuid:\n", "                n_exists += 1\n", "                datum[\"uuid\"] = hash_to_uuid[hash_id]\n", "            else:\n", "                data.append(datum)\n", "                datum[\"uuid\"] = str(uuid.uuid4())\n", "            json.dump(datum, f_out)\n", "            f_out.write(\"\\n\")\n", "\n", "print(f\"Loaded {n_lines} lines from {COMMITS_WITH_INSTRUCTIONS_PATH}\")\n", "print(f\"- {n_failed} failed\")\n", "print(f\"- {no_change} no change\")\n", "print(f\"- {n_exists} exists (so we skeep chat generation for them)\")\n", "print(f\"- {len(data)} new\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Don't forget to shuffle the data.\n", "# cat /mnt/efs/augment/user/yury/smart_paste/data_new/v2/02_instruction_stage/commits_with_instructions.jsonl | shuf -o /mnt/efs/augment/user/yury/smart_paste/data_new/v2/02_instruction_stage/commits_with_instructions.jsonl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import json\n", "\n", "n_lines, n_exists, n_failed, no_change = 0, 0, 0, 0\n", "data = {}\n", "\n", "with COMMITS_WITH_INSTRUCTIONS_PATH.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line)\n", "        n_lines += 1\n", "        if datum[\"status\"] != \"success\":\n", "            n_failed += 1\n", "            continue\n", "        if datum[\"before_file\"][\"contents\"] == datum[\"after_file\"][\"contents\"]:\n", "            no_change += 1\n", "            continue\n", "        hash_id = get_id(datum)\n", "        if hash_id in hash_to_uuid:\n", "            n_exists += 1\n", "        else:\n", "            data[datum[\"uuid\"]] = datum\n", "\n", "print(f\"Loaded {n_lines} lines from {COMMITS_WITH_INSTRUCTIONS_PATH}\")\n", "print(f\"- {n_failed} failed\")\n", "print(f\"- {no_change} no change\")\n", "print(f\"- {n_exists} exists (so we skeep chat generation for them)\")\n", "print(f\"- {len(data)} new\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from base.diff_utils.diff_utils import File\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "from experimental.yury.smart_paste.augmentations import (\n", "    BeforeAfterFiles,\n", "    SelectedCodeSampler,\n", ")\n", "\n", "\n", "selected_code_sampler = SelectedCodeSampler(\n", "    expected_n_hunks_to_drop_from_the_beginning=1,\n", "    p_selected_code_empty=0.4,\n", "    p_selected_code_single_line=0.1,\n", "    p_expansion=0.75,\n", "    expected_n_lines_to_add=3,\n", "    expected_n_lines_to_drop=3,\n", ")\n", "\n", "\n", "def build_chat_prompt_input(sample):\n", "    before_file = File(\n", "        path=sample[\"before_file\"][\"path\"],\n", "        contents=sample[\"before_file\"][\"contents\"],\n", "    )\n", "    after_file = File(\n", "        path=sample[\"after_file\"][\"path\"],\n", "        contents=sample[\"after_file\"][\"contents\"],\n", "    )\n", "    before_after_file = BeforeAfterFiles(before_file, after_file)\n", "    selected_code_sampler_output = selected_code_sampler.sample_selected_code(\n", "        before_after_file\n", "    )\n", "\n", "    aux_output = {\n", "        \"selected_code_sampler_output\": {\n", "            \"selected_code\": {\n", "                \"start\": int(selected_code_sampler_output.selected_code.start),\n", "                \"stop\": int(selected_code_sampler_output.selected_code.stop),\n", "            },\n", "            \"all_changes_span\": {\n", "                \"start\": int(selected_code_sampler_output.all_changes_span.start),\n", "                \"stop\": int(selected_code_sampler_output.all_changes_span.stop),\n", "            },\n", "        }\n", "    }\n", "\n", "    selected_code = selected_code_sampler_output.selected_code\n", "    aux_output[\"empty_selected_code\"] = int(selected_code.start == selected_code.stop)\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=before_file.path,\n", "        prefix=before_file.contents[: selected_code.start],\n", "        selected_code=before_after_file.get_before_contents(selected_code),\n", "        suffix=before_file.contents[selected_code.stop :],\n", "        message=sample[\"instruction\"],\n", "        chat_history=[],\n", "        prefix_begin=0,\n", "        suffix_end=len(before_file.contents),\n", "        retrieved_chunks=[],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "\n", "    return prompt_input, aux_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.lib.system_prompts import (\n", "    get_claude_with_codeblocks_xml_system_prompt_formatter,\n", ")\n", "\n", "\n", "# from services/deploy/claude_sonnet_3_5_16k_v4promptformatter_chat_deploy.jsonnet\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=3000,\n", "    retrieval_len_for_user_guided=8000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 16,  # 16k for prompt\n", "    inject_current_file_into_retrievals=True,\n", ")\n", "\n", "TOKEN_COUNTER = ClaudeTokenCounter()\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(\n", "    TOKEN_COUNTER, TOKEN_APPORTIONMENT\n", ")\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(\n", "    TOKEN_COUNTER,\n", "    TOKEN_APPORTIONMENT,\n", "    system_prompt_factory=get_claude_with_codeblocks_xml_system_prompt_formatter,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import tqdm\n", "from base.prompt_format_chat.prompt_formatter import ExceedContextLength\n", "\n", "n_exceed_context_length = 0\n", "metrics = []\n", "\n", "\n", "def build_prompt(sample):\n", "    prompt_input, aux_output = build_chat_prompt_input(sample)\n", "    prompt_output = prompt_formatter.format_prompt(prompt_input)\n", "    prompt_length = TOKEN_COUNTER.count_tokens(\n", "        prompt_output.system_prompt\n", "    ) + TOKEN_COUNTER.count_tokens(prompt_output.message)\n", "    aux_output[\"prompt_lenght\"] = prompt_length\n", "    return prompt_output, aux_output\n", "\n", "\n", "for sample in tqdm.tqdm(data.values()[:100]):\n", "    try:\n", "        prompt_output, current_metrics = build_prompt(sample)\n", "    except ExceedContextLength:\n", "        n_exceed_context_length += 1\n", "        continue\n", "    metrics.append(current_metrics)\n", "\n", "print(f\"{n_exceed_context_length} exceed context length\")\n", "\n", "df = pd.DataFrame(metrics)\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "# See services/deploy/claude_sonnet_3_5_16k_chat_deploy.jsonnet\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"write a factorial function\"},\n", "]\n", "\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=dialog,\n", "    temperature=TEMPERAURE,\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_claude(prompt_output, temperature):\n", "    messages = []\n", "    for exchange in prompt_output.chat_history:\n", "        messages.extend(\n", "            [\n", "                {\"role\": \"user\", \"content\": exchange.request_message},\n", "                {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "            ]\n", "        )\n", "    messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=messages,\n", "        system=prompt_output.system_prompt,\n", "        temperature=temperature,\n", "    )\n", "    return response.content[0].text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index in range(5):\n", "    sample = data[index]\n", "    prompt_output, _ = build_prompt(sample)\n", "\n", "    print(prompt_output.message)\n", "    print()\n", "    print(\"--\" * 20)\n", "    print()\n", "    for temperature in [0.0, 0.6, 1.0]:\n", "        print(f\"TEMPERATURE: {temperature}\")\n", "        print()\n", "\n", "        print(run_claude(prompt_output, temperature))\n", "        print()\n", "        print(\"--\" * 20)\n", "    print(\"=\" * 20 + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import tqdm\n", "from base.prompt_format_chat.prompt_formatter import ExceedContextLength\n", "\n", "\n", "BATCH_CALL_UNIQUE_ID = \"test_\"\n", "BATCH_CALL_MODEL_NAME = \"claude-3-5-sonnet-20240620\"\n", "BATCH_CALL_TEMPERATURE = 1.0\n", "BATCH_CALL_MAX_OUTPUT_TOKENS = 1024 * 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_saved, n_exceed_context_length = 0, 0\n", "\n", "\n", "def build_prompt(sample):\n", "    prompt_input, aux_output = build_chat_prompt_input(sample)\n", "    prompt_output = prompt_formatter.format_prompt(prompt_input)\n", "    prompt_length = TOKEN_COUNTER.count_tokens(\n", "        prompt_output.system_prompt\n", "    ) + TOKEN_COUNTER.count_tokens(prompt_output.message)\n", "    aux_output[\"prompt_lenght\"] = prompt_length\n", "    return prompt_output, aux_output\n", "\n", "\n", "with CHAT_REQUESTS_PATH.open(\"w\") as f_out:\n", "    for sample in tqdm.tqdm(data):\n", "        try:\n", "            prompt_output, aux_output = build_prompt(sample)\n", "\n", "            messages = []\n", "            for exchange in prompt_output.chat_history:\n", "                messages.extend(\n", "                    [\n", "                        {\"role\": \"user\", \"content\": exchange.request_message},\n", "                        {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "                    ]\n", "                )\n", "            messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "\n", "            request = {\n", "                \"request\": {\n", "                    \"custom_id\": f\"{BATCH_CALL_UNIQUE_ID}{sample['uuid']}\",\n", "                    \"params\": {\n", "                        \"model\": BATCH_CALL_MODEL_NAME,\n", "                        \"max_tokens\": BATCH_CALL_MAX_OUTPUT_TOKENS,\n", "                        \"messages\": messages,\n", "                        \"system\": prompt_output.system_prompt,\n", "                        \"temperature\": BATCH_CALL_TEMPERATURE,\n", "                    },\n", "                },\n", "            }\n", "            request.update(aux_output)\n", "\n", "            json.dump(request, f_out)\n", "            f_out.write(\"\\n\")\n", "            n_saved += 1\n", "\n", "        except ExceedContextLength:\n", "            n_exceed_context_length += 1\n", "            continue\n", "\n", "print(f\"{n_exceed_context_length} exceed context length, {n_saved} saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import json\n", "\n", "\n", "ANTHROPIC_API_KEY = \"\"\n", "client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "N_REQUESTS_PER_STEP = 3000\n", "\n", "data_for_batch_processing = []\n", "n_already_processed, n_to_be_processed = 0, 0\n", "\n", "\n", "def get_uuid(custom_id):\n", "    assert custom_id.startswith(BATCH_CALL_UNIQUE_ID)\n", "    return custom_id[len(BATCH_CALL_UNIQUE_ID) :]\n", "\n", "\n", "# with BATCH_CHAT_REQUESTS_PATH.open(\"a\") as f_out:\n", "with BATCH_CHAT_REQUESTS_PATH_ITER2.open(\"a\") as f_out:\n", "\n", "    def send_batch_request():\n", "        global data_for_batch_processing\n", "        global n_to_be_processed\n", "        all_custom_ids = [request[\"custom_id\"] for request in data_for_batch_processing]\n", "        n_to_be_processed += len(all_custom_ids)\n", "        batch_request_id = client.beta.messages.batches.create(\n", "            requests=data_for_batch_processing\n", "        )\n", "        print(\n", "            f\"Sending {len(data_for_batch_processing)} requests, size {len(json.dumps(data_for_batch_processing)) / 1024 / 1024}MB\"\n", "        )\n", "        json.dump(\n", "            {\n", "                \"batch_request_id\": batch_request_id.id,\n", "                \"custom_request_ids\": all_custom_ids,\n", "            },\n", "            f_out,\n", "        )\n", "        f_out.write(\"\\n\")\n", "        data_for_batch_processing = []\n", "\n", "    with CHAT_REQUESTS_PATH.open(\"r\") as f:\n", "        for line in f:\n", "            sample = json.loads(line)[\"request\"]\n", "            sample_uuid = get_uuid(sample[\"custom_id\"])\n", "            # if sample_uuid in successful_custom_uuids:\n", "            if sample_uuid in existing_smart_paste_inputs_uuid:\n", "                n_already_processed += 1\n", "                continue\n", "            data_for_batch_processing.append(sample)\n", "            if len(data_for_batch_processing) >= N_REQUESTS_PER_STEP:\n", "                send_batch_request()\n", "\n", "    if len(data_for_batch_processing) > 0:\n", "        send_batch_request()\n", "\n", "print(f\"{n_already_processed} already processed, {n_to_be_processed} to be processed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import json\n", "import tqdm\n", "\n", "\n", "ANTHROPIC_API_KEY = \"\"\n", "client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)\n", "\n", "\n", "def get_uuid(custom_id):\n", "    assert custom_id.startswith(BATCH_CALL_UNIQUE_ID)\n", "    return custom_id[len(BATCH_CALL_UNIQUE_ID) :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pbar = tqdm.tqdm(\n", "    desc=\"Download batch requests from anthropic\",\n", ")\n", "\n", "n_fail, n_success = 0, 0\n", "used_indexes = set()\n", "\n", "with BATCH_CHAT_REQUESTS_PATH_ITER2.open(\"r\") as f:\n", "    for line in f:\n", "        batch_request = client.beta.messages.batches.retrieve(\n", "            json.loads(line)[\"batch_request_id\"]\n", "        )\n", "        if batch_request.processing_status == \"in_progress\":\n", "            print(f\"Batch {batch_request.id} is still in progress. Skipping\")\n", "            pbar.update()\n", "            continue\n", "\n", "        pbar.set_postfix({\"n_success\": n_success, \"n_fail\": n_fail})\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.stream_processor.codeblocks_xml_stream_processor import process_codeblocks_xml\n", "\n", "\n", "def run_processor(text):\n", "    return \"\".join(list(process_codeblocks_xml([text], n_extra_backticks=1)))\n", "\n", "\n", "n_fail, n_success = 0, 0\n", "for result in client.beta.messages.batches.results(\n", "    batch_request.id,\n", "):\n", "    if result.result.type != \"succeeded\":\n", "        n_fail += 1\n", "        continue\n", "    sample_uuid = get_uuid(result.custom_id)\n", "    print(sample_uuid)\n", "    print(\"-\" * 20 + \"\\n\")\n", "    print(data[sample_uuid][\"instruction\"])\n", "    print(\"-\" * 20 + \"\\n\")\n", "    # index = get_index(result.custom_id)\n", "    # assert index not in used_indexes\n", "    # used_indexes.add(index)\n", "    # output_sample = dataclasses.asdict(data[index])\n", "    # assert len(result.result.message.content) == 1\n", "    response = result.result.message.content[0].text\n", "    print(response)\n", "    print(\"-\" * 20 + \"\\n\")\n", "    print(run_processor(response))\n", "    print(\"=\" * 20 + \"\\n\")\n", "    # output_sample[\"claud_chat_response\"] = response\n", "    # json.dump(output_sample, f)\n", "    # f.write(\"\\n\")\n", "    n_success += 1\n", "\n", "print(f\"Wrote {n_success} samples, {n_fail} failed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "\n", "data_for_batch_processing_dict = {}\n", "with CHAT_REQUESTS_PATH.open(\"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        datum = json.loads(line)\n", "        sample_uuid = get_uuid(datum[\"request\"][\"custom_id\"])\n", "        assert sample_uuid not in data_for_batch_processing_dict\n", "        assert sample_uuid in data\n", "        data_for_batch_processing_dict[sample_uuid] = datum\n", "\n", "print(f\"Loaded {len(data_for_batch_processing_dict)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class CodeblockMetadata:\n", "    language: str | None\n", "    path: str | None\n", "    mode: str | None\n", "    code: str\n", "\n", "\n", "WHITESPACE_NO_NEWLINE = r\"[ \\t\\f\\v]*\"\n", "\n", "PATTERN = (\n", "    r\"<augment_code_snippet path=\\\"([^\\\"]+)\\\" mode=\\\"([^\\\"]+)\\\">\\n\"\n", "    + WHITESPACE_NO_NEWLINE\n", "    + r\"```(\\w*)\\n\"\n", "    + r\"(.*?\\n)\"\n", "    + WHITESPACE_NO_NEWLINE\n", "    + r\"```\\n\"\n", "    + WHITESPACE_NO_NEWLINE\n", "    + r\"</augment_code_snippet>\"\n", ")\n", "\n", "\n", "def parse_codeblocks(text):\n", "    results = []\n", "    matches = re.finditer(PATTERN, text, re.DOTALL)\n", "    for match in matches:\n", "        mode = match.group(2)\n", "        assert mode in [\"EDIT\", \"EXCERPT\"]\n", "        if mode == \"EDIT\":\n", "            results.append(\n", "                CodeblockMetadata(\n", "                    language=match.group(3),\n", "                    path=match.group(1),\n", "                    mode=match.group(2),\n", "                    code=match.group(4),\n", "                )\n", "            )\n", "    return results\n", "\n", "\n", "parse_codeblocks(\"\"\"\\\n", "    <augment_code_snippet path=\"mermaid_diagram.md\" mode=\"EDIT\">\n", "    ```mermaid\n", "    graph TD\n", "        A[Start] --> B{Is it raining?}\n", "        B -->|Yes| C[Take an umbrella]\n", "        B -->|No| D[Enjoy the weather]\n", "        C --> E[Go outside]\n", "        D --> E\n", "        E --> F[End]\n", "    ```\n", "    </augment_code_snippet>\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import uuid\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "\n", "\n", "pbar = tqdm.tqdm(\n", "    desc=\"Download batch requests from anthropic\",\n", ")\n", "\n", "n_fail, n_success, n_no_codeblocks = 0, 0, 0\n", "codeblock_stats = []\n", "successful_custom_uuids = set()\n", "\n", "np.random.seed(31415)\n", "\n", "with OUTPUT_FILE_ITER2.open(\"w\") as f_out:\n", "    with BATCH_CHAT_REQUESTS_PATH_ITER2.open(\"r\") as f:\n", "        for line in f:\n", "            batch_request = client.beta.messages.batches.retrieve(\n", "                json.loads(line)[\"batch_request_id\"]\n", "            )\n", "            if batch_request.processing_status == \"in_progress\":\n", "                print(f\"Batch {batch_request.id} is still in progress. Skipping\")\n", "                pbar.update()\n", "                continue\n", "\n", "            for result in client.beta.messages.batches.results(\n", "                batch_request.id,\n", "            ):\n", "                if result.result.type != \"succeeded\":\n", "                    n_fail += 1\n", "                    continue\n", "                sample_uuid = get_uuid(result.custom_id)\n", "                assert sample_uuid in data_for_batch_processing_dict\n", "                assert sample_uuid in data\n", "                path = data[sample_uuid][\"before_file\"][\"path\"]\n", "\n", "                assert len(result.result.message.content) == 1\n", "                response = result.result.message.content[0].text\n", "                codeblocks = parse_codeblocks(response)\n", "                codeblocks_with_correct_path = [\n", "                    codeblock for codeblock in codeblocks if codeblock.path == path\n", "                ]\n", "                codeblock_stats.append(\n", "                    {\n", "                        \"n_codeblocks_total\": len(codeblocks),\n", "                        \"n_codeblocks_with_correct_path\": len(\n", "                            codeblocks_with_correct_path\n", "                        ),\n", "                        \"n_codeblocks_with_other_path\": len(codeblocks)\n", "                        - len(codeblocks_with_correct_path),\n", "                    }\n", "                )\n", "                if len(codeblocks_with_correct_path) == 0:\n", "                    n_no_codeblocks += 1\n", "                    continue\n", "                    # print(path)\n", "                    # print()\n", "                    # print(response)\n", "\n", "                assert sample_uuid not in successful_custom_uuids\n", "                successful_custom_uuids.add(sample_uuid)\n", "\n", "                assert len(codeblocks_with_correct_path) > 0\n", "                codeblock_index = np.random.randint(len(codeblocks_with_correct_path))\n", "                codeblock = codeblocks_with_correct_path[codeblock_index]\n", "\n", "                chat_history = []\n", "                claude_chat_history = data_for_batch_processing_dict[sample_uuid][\n", "                    \"request\"\n", "                ][\"params\"][\"messages\"]\n", "                for index in range(0, len(claude_chat_history), 2):\n", "                    if index + 1 >= len(claude_chat_history):\n", "                        assert claude_chat_history[index][\"role\"] == \"user\"\n", "                        chat_history.append(\n", "                            Exchange(\n", "                                request_message=claude_chat_history[index][\"content\"],\n", "                                response_text=response,\n", "                                request_id=sample_uuid,\n", "                            )\n", "                        )\n", "                    else:\n", "                        assert claude_chat_history[index][\"role\"] == \"user\"\n", "                        assert claude_chat_history[index + 1][\"role\"] == \"assistant\"\n", "                        chat_history.append(\n", "                            Exchange(\n", "                                request_message=claude_chat_history[index][\"content\"],\n", "                                response_text=claude_chat_history[index + 1][\"content\"],\n", "                                request_id=str(uuid.uuid4()),\n", "                            )\n", "                        )\n", "\n", "                before_file_contents = data[sample_uuid][\"before_file\"][\"contents\"]\n", "                selected_code_crange = data_for_batch_processing_dict[sample_uuid][\n", "                    \"selected_code_sampler_output\"\n", "                ][\"selected_code\"]\n", "                sample = ResearchSmartPastePromptInput(\n", "                    path=data[sample_uuid][\"before_file\"][\"path\"],\n", "                    prefix=before_file_contents[: selected_code_crange[\"start\"]],\n", "                    selected_code=before_file_contents[\n", "                        selected_code_crange[\"start\"] : selected_code_crange[\"stop\"]\n", "                    ],\n", "                    suffix=before_file_contents[selected_code_crange[\"stop\"] :],\n", "                    code_block=codeblock.code,\n", "                    chat_history=chat_history,\n", "                    prefix_begin=0,\n", "                    suffix_end=len(before_file_contents),\n", "                    retrieved_chunks=[],\n", "                    context_code_exchange_request_id=sample_uuid,\n", "                    target_path=path,\n", "                    target_file_content=before_file_contents,\n", "                    request_id=sample_uuid,\n", "                    aux={\n", "                        \"codeblock_index\": codeblock_index,\n", "                        \"chat_request\": data_for_batch_processing_dict[sample_uuid],\n", "                        \"chat_response\": response,\n", "                        \"commit\": data[sample_uuid],\n", "                    },\n", "                )\n", "                json.dump(dataclasses.asdict(sample), f_out)\n", "                f_out.write(\"\\n\")\n", "                n_success += 1\n", "\n", "            pbar.set_postfix({\"n_success\": n_success, \"n_fail\": n_fail})\n", "            pbar.update()\n", "\n", "\n", "print(\n", "    f\"Wrote {n_success} samples ({len(successful_custom_uuids)}), {n_fail} failed, {n_no_codeblocks} no codeblocks\"\n", ")\n", "pd.DataFrame(codeblock_stats).describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
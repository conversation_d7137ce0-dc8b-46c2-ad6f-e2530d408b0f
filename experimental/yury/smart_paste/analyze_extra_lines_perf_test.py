"""Performance tests for analyze_extra_lines.py."""

import time
import unittest
from typing import Set, Tu<PERSON>, Any

from experimental.yury.smart_paste.analyze_extra_lines import (
    analyze_extra_lines,
)


def generate_large_file(num_lines: int, line_length: int) -> str:
    """Generate a large file with specified number of lines and line length."""
    lines = []
    for i in range(num_lines):
        # Create a line with some structure to test normalization
        prefix = f"def function_{i}("
        # Add parameters to reach desired line length
        params = ", ".join(
            [
                f"param_{j}=value_{j}"
                for j in range((line_length - len(prefix) - 2) // 20)
            ]
        )
        line = prefix + params + "):"
        lines.append(line)
    return "\n".join(lines)


def generate_file_with_imports(num_imports: int) -> str:
    """Generate a file with many imports."""
    lines = []
    # Add single imports
    for i in range(num_imports // 3):
        lines.append(f"import module_{i}")

    # Add from imports
    for i in range(num_imports // 3):
        lines.append(f"from package_{i} import function_{i}")

    # Add multiline imports
    lines.append("from big_package import (")
    for i in range(num_imports // 3):
        lines.append(f"    function_{i},")
    lines.append(")")

    return "\n".join(lines)


class AnalyzeExtraLinesPerformanceTest(unittest.TestCase):
    """Performance tests for analyze_extra_lines.py."""

    # Class-level constant for similar lines test
    BASE_LINE = "result = very_long_function_name(param1='value1', param2='value2', param3='value3'"

    def setUp(self):
        """Set up test data."""
        # Generate test files of different sizes
        self.small_file = generate_large_file(100, 80)  # 100 lines, 80 chars each
        self.medium_file = generate_large_file(1000, 200)  # 1000 lines, 200 chars each
        self.large_file = generate_large_file(10000, 500)  # 10000 lines, 500 chars each
        self.very_large_file = generate_large_file(
            100000, 1000
        )  # 100000 lines, 1000 chars each

        # Files with imports
        self.small_imports = generate_file_with_imports(10)
        self.large_imports = generate_file_with_imports(1000)

    def time_function(self, func, *args, **kwargs) -> Tuple[float, Any]:
        """Time a function call and return (time_taken, result)."""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return end_time - start_time, result

    def assert_linear_complexity(
        self,
        sizes: list,
        times: list,
        threshold: float = 10.0,  # Increased threshold for real-world conditions
        msg: str = "Function appears to have non-linear complexity",
    ):
        """Assert that the function has roughly linear complexity.

        Args:
            sizes: List of input sizes
            times: List of corresponding execution times
            threshold: Maximum allowed ratio between time/size ratios
            msg: Error message
        """
        # Calculate time/size ratios
        ratios = [t / s for t, s in zip(times, sizes)]

        # The ratio between the largest and smallest ratio should be less than threshold
        # for roughly linear complexity
        ratio_range = max(ratios) / min(ratios)
        self.assertLess(
            ratio_range,
            threshold,
            f"{msg}. Ratio range: {ratio_range}, ratios: {ratios}",
        )

    def test_analyze_extra_lines_performance(self):
        """Test analyze_extra_lines performance with large files."""
        sizes = [100, 1000, 5000]  # Reduced max size for better performance
        times = []

        for size in sizes:
            # Create test data
            target_content = generate_large_file(size, 80)
            code_block = generate_large_file(size, 80)
            response = generate_large_file(
                size * 2, 80
            )  # Double size to ensure extra lines

            datum = {
                "input": {
                    "target_path": "test.py",
                    "target_file_content": target_content,
                    "code_block": code_block,
                    "chat_history": [],  # Add empty chat history
                    "path": "test.py",  # Add required fields
                    "prefix": "",
                    "selected_code": "",
                    "suffix": "",
                    "prefix_begin": 0,
                    "suffix_end": 0,
                    "retrieved_chunks": [],
                },
                "response": response,
            }

            time_taken, result = self.time_function(analyze_extra_lines, datum)
            times.append(time_taken)

            # Verify results
            (
                total_lines,
                extra_lines,
                matching_lines,
                extra_lines_set,
                normalized_extra_lines_set,
                input_lines,
                import_lines,
                trailing_comma_lines,
            ) = result

            self.assertEqual(total_lines, size * 2)  # Response has double size
            self.assertEqual(len(input_lines), size)  # Each input file has 'size' lines

        # Assert roughly linear complexity with higher threshold
        self.assert_linear_complexity(
            sizes,
            times,
            threshold=50.0,  # Higher threshold as this combines multiple operations
            msg="analyze_extra_lines appears to have non-linear complexity",
        )


if __name__ == "__main__":
    unittest.main()

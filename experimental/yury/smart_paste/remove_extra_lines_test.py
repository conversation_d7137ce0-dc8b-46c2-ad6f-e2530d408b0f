"""Tests for remove_extra_lines.py."""

import json
import tempfile
from pathlib import Path

import pytest

from experimental.yury.smart_paste.remove_extra_lines import (
    filter_samples,
    should_keep_sample,
)
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput


def create_sample(target_content: str, code_block: str, response: str) -> dict:
    """Create a sample with given content."""
    input_obj = ResearchSmartPastePromptInput(
        path="test.py",
        prefix="",
        selected_code="",
        code_block=code_block,
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        target_path="test.py",
        target_file_content=target_content,
    )
    return {
        "input": {
            "target_path": input_obj.target_path,
            "target_file_content": input_obj.target_file_content,
            "code_block": input_obj.code_block,
            "target_start_line": 0,
            "target_end_line": 0,
            "target_start_char": 0,
            "target_end_char": 0,
        },
        "response": response,
    }


def test_should_keep_sample_no_extra_lines():
    """Test that samples with no extra lines are kept."""
    sample = create_sample(
        target_content="def foo():\n    return 42\n",
        code_block="def bar():\n    return 43\n",
        response="def foo():\n    return 42\n",
    )
    assert should_keep_sample(sample) is True


def test_should_keep_sample_with_extra_lines():
    """Test that samples with extra lines are removed."""
    sample = create_sample(
        target_content="def foo():\n    return 42\n",
        code_block="def bar():\n    return 43\n",
        response="def foo():\n    return 42\n    print('extra')\n",
    )
    assert should_keep_sample(sample) is False


def test_should_keep_sample_with_imports_allowed():
    """Test that samples with only import lines are kept when allowed."""
    sample = create_sample(
        target_content="def foo():\n    return 42\n",
        code_block="def bar():\n    return 43\n",
        response="import math\ndef foo():\n    return 42\n",
    )
    assert should_keep_sample(sample, allow_imports=True) is True
    assert should_keep_sample(sample, allow_imports=False) is False


def test_should_keep_sample_with_imports_and_extra_lines():
    """Test that samples with both imports and other extra lines are removed."""
    sample = create_sample(
        target_content="def foo():\n    return 42\n",
        code_block="def bar():\n    return 43\n",
        response="import math\ndef foo():\n    return 42\n    print('extra')\n",
    )
    assert should_keep_sample(sample, allow_imports=True) is False
    assert should_keep_sample(sample, allow_imports=False) is False


def test_filter_samples(tmp_path):
    """Test filtering samples from a file."""
    # Create test input file
    input_file = tmp_path / "input.jsonl"
    output_file = tmp_path / "output.jsonl"

    samples = [
        # Sample with no extra lines - should be kept
        create_sample(
            target_content="def foo():\n    return 42\n",
            code_block="",
            response="def foo():\n    return 42\n",
        ),
        # Sample with extra lines - should be removed
        create_sample(
            target_content="def bar():\n    return 43\n",
            code_block="",
            response="def bar():\n    return 43\n    print('extra')\n",
        ),
        # Sample with only imports - should be kept when imports allowed
        create_sample(
            target_content="def baz():\n    return 44\n",
            code_block="",
            response="import math\ndef baz():\n    return 44\n",
        ),
    ]

    with input_file.open("w") as f:
        for sample in samples:
            f.write(json.dumps(sample) + "\n")

    # Test without allowing imports
    total, kept = filter_samples(input_file, output_file, allow_imports=False)
    assert total == 3
    assert kept == 1

    with output_file.open() as f:
        kept_samples = [json.loads(line) for line in f]
    assert len(kept_samples) == 1
    assert kept_samples[0]["response"] == "def foo():\n    return 42\n"

    # Test with allowing imports
    total, kept = filter_samples(input_file, output_file, allow_imports=True)
    assert total == 3
    assert kept == 2

    with output_file.open() as f:
        kept_samples = [json.loads(line) for line in f]
    assert len(kept_samples) == 2
    responses = [s["response"] for s in kept_samples]
    assert "def foo():\n    return 42\n" in responses
    assert "import math\ndef baz():\n    return 44\n" in responses


def test_filter_samples_max_samples(tmp_path):
    """Test filtering with max_samples limit."""
    input_file = tmp_path / "input.jsonl"
    output_file = tmp_path / "output.jsonl"

    samples = [
        create_sample("def foo():\n    pass\n", "", "def foo():\n    pass\n")
    ] * 5

    with input_file.open("w") as f:
        for sample in samples:
            f.write(json.dumps(sample) + "\n")

    total, kept = filter_samples(input_file, output_file, max_samples=3)
    assert total == 3
    assert kept == 3

    with output_file.open() as f:
        kept_samples = [json.loads(line) for line in f]
    assert len(kept_samples) == 3

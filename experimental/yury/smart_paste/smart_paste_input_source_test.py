import pytest
from experimental.yury.smart_paste.smart_paste_input_source import (
    parse_codeblocks,
    Codeblock,
)


def test_parse_codeblocks_mermaid():
    input_text = """\
 <augment_code_snippet path="mermaid_diagram.md" mode="EDIT">
 ```mermaid
 graph TD
 A[Start] --> B{Is it raining?}
 B -->|Yes| C[Take an umbrella]
 B -->|No| D[Enjoy the weather]
 C --> E[Go outside]
 D --> E
 E --> F[End]
```
</augment_code_snippet>
"""

    expected = [
        Codeblock(
            language="mermaid",
            path="mermaid_diagram.md",
            mode="EDIT",
            code=" graph TD\n A[Start] --> B{Is it raining?}\n B -->|Yes| C[Take an umbrella]\n B -->|No| D[Enjoy the weather]\n C --> E[Go outside]\n D --> E\n E --> F[End]\n",
        )
    ]

    result = parse_codeblocks(input_text)
    assert result == expected

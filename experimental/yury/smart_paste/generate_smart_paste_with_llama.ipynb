{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import tqdm\n", "import json\n", "import numpy as np\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "from experimental.yury.smart_paste.smart_paste_input_source import (\n", "    load_smart_paste_inputs_v2_2,\n", "    extract_edit_markdown_blocks_from_data_v2,\n", ")\n", "\n", "\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage\")\n", "# OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses_iter2.jsonl\"\n", "\n", "\n", "# OUTPUT_DIR = Path(\n", "#     \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage\"\n", "# )\n", "# OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses_nocodeblocks.jsonl\"\n", "\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage\"\n", ")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses_iter2_nocodeblocks.jsonl\"\n", "\n", "smart_paste_inputs = load_smart_paste_inputs_v2_2()\n", "\n", "smart_paste_inputs_with_multiple_codeblocks = {}\n", "for request_id, smart_paste_input in smart_paste_inputs.items():\n", "    if len(extract_edit_markdown_blocks_from_data_v2(smart_paste_input)) > 1:\n", "        smart_paste_inputs_with_multiple_codeblocks[request_id] = smart_paste_input\n", "\n", "print(\n", "    f\"Kept {len(smart_paste_inputs_with_multiple_codeblocks)} samples with multiple codeblocks out of {len(smart_paste_inputs)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from experimental.yury.smart_paste.smart_paste_input_source import (\n", "    load_smart_paste_inputs_v1,\n", "    extract_edit_markdown_blocks_from_data_v1,\n", ")\n", "\n", "# OUTPUT_DIR = Path(\n", "#     \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage\"\n", "# )\n", "# OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# OUTPUT_FILE = OUTPUT_DIR / \"smart_paste_responses_nocodeblocks.jsonl\"\n", "\n", "OUTPUT_FILE = Path(\n", "    \"/home/<USER>/data/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses_nocodeblocks.jsonl\"\n", ")\n", "\n", "smart_paste_inputs = load_smart_paste_inputs_v1()\n", "\n", "smart_paste_inputs_with_multiple_codeblocks = {}\n", "for request_id, smart_paste_input in smart_paste_inputs.items():\n", "    codeblocks = extract_edit_markdown_blocks_from_data_v1(smart_paste_input)\n", "    codeblocks_for_the_same_file = [\n", "        codeblock\n", "        for codeblock in codeblocks\n", "        if codeblock.path == smart_paste_input.target_path\n", "    ]\n", "    if len(codeblocks_for_the_same_file) > 1:\n", "        smart_paste_inputs_with_multiple_codeblocks[request_id] = smart_paste_input\n", "\n", "print(\n", "    f\"Kept {len(smart_paste_inputs_with_multiple_codeblocks)} samples with multiple codeblocks out of {len(smart_paste_inputs)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "# URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "# RI_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "# MODEL = \"binks-v12-fp16-longoutput\"\n", "\n", "URL = \"https://dev-yury.us-central.api.augmentcode.com\"\n", "MODEL = \"binks-v12-fp16-longoutput\"\n", "RI_URL = \"https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "\n", "TIMEOUT = 1200"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "from collections import Counter\n", "from typing import Any, Iterator\n", "import concurrent.futures\n", "\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    # SMART_PASTE_PROMPT,\n", "    # extract_modified_file_from_response,\n", "    LIOR_PROMPT_NOCODEBLOCK,\n", "    lior_extract_modified_file_from_response,\n", ")\n", "from simplejson import JSONDecodeError\n", "import uuid\n", "from base.augment_client.client import ClientException\n", "\n", "\n", "N_PROCESSES = 14\n", "\n", "\n", "def maybe_run_in_multiple_threads(\n", "    processor_fn: Callable,\n", "    inputs: Iterator[Any],\n", "    num_threads: int,\n", ") -> Iterator[Any]:\n", "    \"\"\"Run a processor in multiple threads if num_threads > 1.\"\"\"\n", "    assert num_threads > 0\n", "    if num_threads == 1:\n", "        yield from map(processor_fn, inputs)\n", "    else:\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:\n", "            futures = (executor.submit(processor_fn, item) for item in inputs)\n", "            for future in concurrent.futures.as_completed(futures):\n", "                yield future.result()\n", "\n", "\n", "def run_prompted_llama(input_sample: ResearchSmartPastePromptInput):\n", "    # thread_id = threading.get_ident()\n", "    # session_id = uuid.uuid5(uuid.NAMESPACE_OID, str(thread_id))\n", "    # Use unique session id for each request\n", "    session_id = uuid.uuid4()\n", "    client = AugmentClient(\n", "        url=URL,\n", "        token=API_TOKEN,\n", "        timeout=TIMEOUT,\n", "        retry_count=3,\n", "        session_id=session_id,\n", "    )\n", "\n", "    model_client = AugmentModelClient(client, MODEL)\n", "\n", "    try:\n", "        message = LIOR_PROMPT_NOCODEBLOCK.format(\n", "            target_path=input_sample.target_path,\n", "            target_file_contents=input_sample.target_file_content,\n", "            # suggested_edit=input_sample.code_block,\n", "        )\n", "        chat_response_dogfood = model_client.chat(\n", "            message=message,\n", "            selected_code=input_sample.selected_code,\n", "            prefix=input_sample.prefix,\n", "            suffix=input_sample.suffix,\n", "            path=input_sample.path,\n", "            chat_history=input_sample.chat_history,\n", "            context_code_exchange_request_id=input_sample.context_code_exchange_request_id,\n", "            prefix_begin=input_sample.prefix_begin,\n", "            suffix_end=input_sample.suffix_end,\n", "            timeout=TIMEOUT,\n", "        )\n", "    except ClientException as e:\n", "        if e.response.status_code == 413:\n", "            return {\n", "                \"request_id\": input_sample.request_id,\n", "                \"status\": \"prompt_too_long\",\n", "            }\n", "        elif e.response.status_code == 499:\n", "            return {\n", "                \"request_id\": input_sample.request_id,\n", "                \"status\": \"timeout\",\n", "            }\n", "        raise\n", "    except JSONDecodeError:\n", "        return {\n", "            \"request_id\": input_sample.request_id,\n", "            \"status\": \"invalid_json_response\",\n", "        }\n", "    # modified_file = extract_modified_file_from_response(chat_response_dogfood.text)\n", "    modified_file = lior_extract_modified_file_from_response(chat_response_dogfood.text)\n", "\n", "    if modified_file is None:\n", "        return {\n", "            \"request_id\": input_sample.request_id,\n", "            \"status\": \"failed_to_extract_modified_file\",\n", "        }\n", "    return {\n", "        \"request_id\": input_sample.request_id,\n", "        \"status\": \"success\",\n", "        \"modified_file\": modified_file,\n", "    }\n", "\n", "\n", "status_counter = Counter()\n", "\n", "# Read existing responses\n", "n_success, n_failed, existing_responses = 0, 0, {}\n", "if OUTPUT_FILE.exists():\n", "    with OUTPUT_FILE.open(\"r\") as f:\n", "        for line in f:\n", "            try:\n", "                datum = json.loads(line)\n", "            except json.decoder.JSONDecodeError:\n", "                continue\n", "            if datum[\"status\"] == \"success\":\n", "                n_success += 1\n", "            else:\n", "                # Skip all failed responses\n", "                n_failed += 1\n", "                # continue\n", "            existing_responses[datum[\"request_id\"]] = datum\n", "            status_counter[datum[\"status\"]] += 1\n", "print(\n", "    f\"Loaded {len(existing_responses)} existing responses, {n_failed} failed, {n_success} success\"\n", ")\n", "\n", "data_to_process = []\n", "for request_id, sample in smart_paste_inputs_with_multiple_codeblocks.items():\n", "    if request_id not in existing_responses:\n", "        data_to_process.append(sample)\n", "print(f\"Need to process {len(data_to_process)} samples\")\n", "\n", "pbar = tqdm.tqdm(\n", "    total=len(data_to_process),\n", "    desc=\"Run smart paste\",\n", ")\n", "\n", "with OUTPUT_FILE.open(\"a\") as f:\n", "    for datum in maybe_run_in_multiple_threads(\n", "        run_prompted_llama, data_to_process, N_PROCESSES\n", "    ):\n", "        json.dump(datum, f)\n", "        f.write(\"\\n\")\n", "        f.flush()\n", "        status_counter[datum[\"status\"]] += 1\n", "        pbar.set_postfix(status_counter)\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend\"\n", ")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "OUTPUT_FILE = OUTPUT_DIR / \"merged_smart_paste_responses.jsonl\"\n", "\n", "\n", "smart_paste_inputs = {}\n", "claude_smart_paste_responses = {}\n", "llama_smart_paste_responses = {}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl: 94946it [00:24, 3847.44it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses.jsonl: 94946it [00:02, 33735.71it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 94942 smart_paste_inputs, 4 failed to apply tool calls\n"]}], "source": ["import json\n", "import tqdm\n", "from pathlib import Path\n", "import numpy as np\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "from base.prompt_format.common import Exchange\n", "from experimental.yury.smart_paste_lib.claude_utils import (\n", "    extract_from_dict_and_apply_tool_calls,\n", "    LineNotFound,\n", "    InvalidToolCall,\n", "    InvalidLineNumber,\n", ")\n", "\n", "DATA_V1 = [\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses.jsonl\",\n", "    )\n", "]\n", "\n", "n_success, n_apply_failed = 0, 0\n", "\n", "for smart_paste_inputs_path, llama_smart_paste_responses_path in DATA_V1:\n", "    with open(smart_paste_inputs_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {smart_paste_inputs_path}\"):\n", "            datum = json.loads(line)\n", "\n", "            request_id = datum[\"uuid\"]\n", "            before_file_contents = datum[\"before_file\"][\"contents\"]\n", "            selected_code_crange = datum[\"selected_code_crange\"]\n", "\n", "            datum[\"aux\"][\"commit\"] = {\n", "                \"after_file\": datum[\"aux\"][\"original_commit_after_file\"]\n", "            }\n", "\n", "            smart_paste_input = ResearchSmartPastePromptInput(\n", "                request_id=request_id,\n", "                path=datum[\"before_file\"][\"path\"],\n", "                prefix=before_file_contents[: selected_code_crange[\"start\"]],\n", "                selected_code=before_file_contents[\n", "                    selected_code_crange[\"start\"] : selected_code_crange[\"stop\"]\n", "                ],\n", "                suffix=before_file_contents[selected_code_crange[\"stop\"] :],\n", "                code_block=datum[\"codeblock\"],\n", "                chat_history=[\n", "                    Exchange(\n", "                        request_id=request_id,\n", "                        request_message=datum[\"instruction\"],\n", "                        response_text=datum[\"chat_response\"],\n", "                    )\n", "                ],\n", "                prefix_begin=0,\n", "                suffix_end=len(before_file_contents),\n", "                retrieved_chunks=[],\n", "                context_code_exchange_request_id=request_id,\n", "                target_path=datum[\"before_file\"][\"path\"],\n", "                target_file_content=before_file_contents,\n", "                aux=datum[\"aux\"],\n", "            )\n", "            claude_smart_paste_response = datum[\"after_file\"][\"contents\"]\n", "            claude_smart_paste_response_redo = extract_from_dict_and_apply_tool_calls(\n", "                datum[\"before_file\"][\"contents\"],\n", "                datum[\"aux\"][\"claude_smart_paste_response\"],\n", "            )\n", "            if claude_smart_paste_response != claude_smart_paste_response_redo:\n", "                n_apply_failed += 1\n", "                continue\n", "            smart_paste_inputs[request_id] = smart_paste_input\n", "            claude_smart_paste_responses[request_id] = claude_smart_paste_response\n", "            n_success += 1\n", "\n", "    with open(llama_smart_paste_responses_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {llama_smart_paste_responses_path}\"):\n", "            datum = json.loads(line)\n", "            if datum[\"status\"] != \"success\":\n", "                continue\n", "            llama_smart_paste_responses[datum[\"request_id\"]] = datum[\"modified_file\"]\n", "\n", "\n", "print(\n", "    f\"Loaded {n_success} smart_paste_inputs, {n_apply_failed} failed to apply tool calls\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl: 0it [00:00, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl: 305259it [01:04, 4725.35it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses.jsonl: 293285it [00:12, 23578.68it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl: 314986it [00:06, 46351.12it/s] \n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl: 88470it [00:20, 4376.67it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2.jsonl: 84671it [00:03, 22312.61it/s]\n", "Loading /mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl: 88472it [00:02, 38511.34it/s]\n"]}], "source": ["import json\n", "import tqdm\n", "from pathlib import Path\n", "from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput\n", "from base.prompt_format.common import Exchange\n", "\n", "DATA_V2 = [\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl\",\n", "    ),\n", "    (\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl\",\n", "    ),\n", "]\n", "\n", "for (\n", "    smart_paste_inputs_path,\n", "    claude_smart_paste_responses_path,\n", "    llama_smart_paste_responses_path,\n", ") in DATA_V2:\n", "    with open(smart_paste_inputs_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {smart_paste_inputs_path}\"):\n", "            smart_paste_input = ResearchSmartPastePromptInput.from_json(line)\n", "            assert (\n", "                smart_paste_input.request_id\n", "                == smart_paste_input.context_code_exchange_request_id\n", "            )\n", "            assert (\n", "                smart_paste_input.request_id\n", "                == smart_paste_input.chat_history[-1].request_id\n", "            )\n", "            assert len(smart_paste_input.chat_history) in [1, 2], len(\n", "                smart_paste_input.chat_history\n", "            )\n", "            smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input\n", "\n", "    with open(claude_smart_paste_responses_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {claude_smart_paste_responses_path}\"):\n", "            datum = json.loads(line)\n", "            claude_smart_paste_responses[datum[\"request_id\"]] = datum[\"modified_file\"]\n", "\n", "    with open(llama_smart_paste_responses_path, \"r\") as f:\n", "        for line in tqdm.tqdm(f, desc=f\"Loading {llama_smart_paste_responses_path}\"):\n", "            try:\n", "                datum = json.loads(line)\n", "            except json.decoder.JSONDecodeError:\n", "                continue\n", "            if datum[\"status\"] != \"success\":\n", "                continue\n", "            llama_smart_paste_responses[datum[\"request_id\"]] = datum[\"modified_file\"]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 488671 smart_paste_inputs\n", "Loaded 472898 claude_smart_paste_responses\n", "Loaded 442576 llama_smart_paste_responses\n"]}], "source": ["print(f\"Loaded {len(smart_paste_inputs)} smart_paste_inputs\")\n", "print(f\"Loaded {len(claude_smart_paste_responses)} claude_smart_paste_responses\")\n", "print(f\"Loaded {len(llama_smart_paste_responses)} llama_smart_paste_responses\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>len_target_file</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>472898.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>10334.252995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>16585.141927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2136.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>5229.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>12041.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>255429.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       len_target_file\n", "count    472898.000000\n", "mean      10334.252995\n", "std       16585.141927\n", "min           0.000000\n", "25%        2136.000000\n", "50%        5229.000000\n", "75%       12041.000000\n", "max      255429.000000"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df = []\n", "\n", "for request_id in smart_paste_inputs:\n", "    if request_id not in claude_smart_paste_responses:\n", "        continue\n", "    claude_response = claude_smart_paste_responses[request_id]\n", "\n", "    datum = {\n", "        \"request_id\": request_id,\n", "        \"len_target_file\": len(smart_paste_inputs[request_id].target_file_content),\n", "    }\n", "\n", "    original_commit = smart_paste_inputs[request_id].aux[\"commit\"][\"after_file\"][\n", "        \"contents\"\n", "    ]\n", "\n", "    if claude_response.strip() == original_commit.strip():\n", "        datum[\"keep\"] = True\n", "    else:\n", "        if request_id not in llama_smart_paste_responses:\n", "            datum[\"unsure\"] = True\n", "        else:\n", "            llama_response = llama_smart_paste_responses[request_id]\n", "            if llama_response.strip() == claude_response.strip():\n", "                datum[\"keep\"] = True\n", "            elif llama_response.strip() == original_commit.strip():\n", "                datum[\"keep\"] = True\n", "            else:\n", "                datum[\"throw_away\"] = True\n", "    df.append(datum)\n", "\n", "df = pd.DataFrame(df)\n", "df.describe()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_227074/3897234719.py:6: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df[bool_columns] = df[bool_columns].fillna(False)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>group</th>\n", "      <th>mean</th>\n", "      <th>p50</th>\n", "      <th>p75</th>\n", "      <th>p90</th>\n", "      <th>max</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>len_target_file</th>\n", "      <td>all_rows</td>\n", "      <td>10334.25</td>\n", "      <td>5229.0</td>\n", "      <td>12041.0</td>\n", "      <td>24068.0</td>\n", "      <td>255429.0</td>\n", "      <td>472898.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>len_target_file</th>\n", "      <td>keep_or_unsure</td>\n", "      <td>9134.08</td>\n", "      <td>4001.0</td>\n", "      <td>9003.0</td>\n", "      <td>19046.0</td>\n", "      <td>255429.0</td>\n", "      <td>317523.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>len_target_file</th>\n", "      <td>keep</td>\n", "      <td>6485.51</td>\n", "      <td>3673.0</td>\n", "      <td>7928.0</td>\n", "      <td>15180.0</td>\n", "      <td>252688.0</td>\n", "      <td>282530.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>len_target_file</th>\n", "      <td>throw_away</td>\n", "      <td>12786.91</td>\n", "      <td>8977.0</td>\n", "      <td>18019.5</td>\n", "      <td>29499.2</td>\n", "      <td>199210.0</td>\n", "      <td>155375.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>len_target_file</th>\n", "      <td>unsure</td>\n", "      <td>30518.39</td>\n", "      <td>10420.0</td>\n", "      <td>40617.0</td>\n", "      <td>89482.4</td>\n", "      <td>255429.0</td>\n", "      <td>34993.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          group      mean      p50      p75      p90  \\\n", "len_target_file        all_rows  10334.25   5229.0  12041.0  24068.0   \n", "len_target_file  keep_or_unsure   9134.08   4001.0   9003.0  19046.0   \n", "len_target_file            keep   6485.51   3673.0   7928.0  15180.0   \n", "len_target_file      throw_away  12786.91   8977.0  18019.5  29499.2   \n", "len_target_file          unsure  30518.39  10420.0  40617.0  89482.4   \n", "\n", "                      max     count  \n", "len_target_file  255429.0  472898.0  \n", "len_target_file  255429.0  317523.0  \n", "len_target_file  252688.0  282530.0  \n", "len_target_file  199210.0  155375.0  \n", "len_target_file  255429.0   34993.0  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 1. Convert <PERSON><PERSON> to False for boolean columns\n", "bool_columns = [\"keep\", \"throw_away\", \"unsure\"]\n", "df[bool_columns] = df[bool_columns].fillna(False)\n", "\n", "# 2. Compute distributions for True values in each boolean column\n", "metrics = []\n", "\n", "# Add stats for all rows\n", "stats_all = (\n", "    df[\"len_target_file\"]\n", "    .agg(\n", "        {\n", "            \"mean\": \"mean\",\n", "            \"p50\": lambda x: x.quantile(0.5),\n", "            \"p75\": lambda x: x.quantile(0.75),\n", "            \"p90\": lambda x: x.quantile(0.90),\n", "            \"max\": \"max\",\n", "            \"count\": \"count\",\n", "        }\n", "    )\n", "    .round(2)\n", ")\n", "stats_all = pd.DataFrame([stats_all])\n", "stats_all[\"group\"] = \"all_rows\"\n", "metrics.append(stats_all)\n", "\n", "# Add stats for keep OR unsure\n", "keep_or_unsure = (\n", "    df[df[\"keep\"] | df[\"unsure\"]][\"len_target_file\"]\n", "    .agg(\n", "        {\n", "            \"mean\": \"mean\",\n", "            \"p50\": lambda x: x.quantile(0.5),\n", "            \"p75\": lambda x: x.quantile(0.75),\n", "            \"p90\": lambda x: x.quantile(0.90),\n", "            \"max\": \"max\",\n", "            \"count\": \"count\",\n", "        }\n", "    )\n", "    .round(2)\n", ")\n", "keep_or_unsure = pd.DataFrame([keep_or_unsure])\n", "keep_or_unsure[\"group\"] = \"keep_or_unsure\"\n", "metrics.append(keep_or_unsure)\n", "\n", "# Original per-column stats\n", "for bool_col in bool_columns:\n", "    stats = (\n", "        df[df[bool_col] == True][\"len_target_file\"]\n", "        .agg(\n", "            {\n", "                \"mean\": \"mean\",\n", "                \"p50\": lambda x: x.quantile(0.5),\n", "                \"p75\": lambda x: x.quantile(0.75),\n", "                \"p90\": lambda x: x.quantile(0.90),\n", "                \"max\": \"max\",\n", "                \"count\": \"count\",\n", "            }\n", "        )\n", "        .round(2)\n", "    )\n", "\n", "    stats = pd.DataFrame([stats])\n", "    stats[\"group\"] = bool_col\n", "    metrics.append(stats)\n", "\n", "# Combine all statistics into a single DataFrame\n", "result = pd.concat(metrics)\n", "\n", "# Reorder columns to put group first\n", "cols = [\"group\"] + [col for col in result.columns if col != \"group\"]\n", "result = result[cols]\n", "result"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import build_str_diff, StrDiff, NoopSpan, AddedSpan, ModSpan\n", "\n", "NEWLINES = \"\\n\\r\"\n", "\n", "\n", "def last_span_only_adds_newline(str_diff: StrDiff):\n", "    if len(str_diff.spans) == 0:\n", "        # no changes, so no newline added\n", "        return False\n", "    other_spans_exist = False\n", "    for span in str_diff.spans[:-1]:\n", "        if not isinstance(span, NoopSpan):\n", "            other_spans_exist = True\n", "            break\n", "    if not other_spans_exist:\n", "        # There's only a single Add/Delete/Mod span,\n", "        # so we must respect it even if it adds a newline\n", "        return False\n", "\n", "    last_span = str_diff.spans[-1]\n", "    if isinstance(last_span, AddedSpan) and len(last_span.after.strip(NEWLINES)) == 0:\n", "        # last span only adds a newline\n", "        return True\n", "    if (\n", "        isinstance(last_span, ModSpan)\n", "        and last_span.after.startswith(last_span.before)\n", "        and last_span.after.rstrip(NEWLINES) == last_span.before.rstrip(NEWLINES)\n", "    ):\n", "        # last span only modifies code and adds a newline\n", "        return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparing to save 317523 samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 488671/488671 [02:57<00:00, 2749.81it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved 317479 samples, skipped 70 with empty code blocks, 15773 no claude response, 155349 samples with no agreement\n", "161546 chose llama response, 311282 chose claude response\n", "15885 original no new lines, 4349 modified last line\n", "1653 last span only adds newline\n"]}], "source": ["import tqdm\n", "import dataclasses\n", "from base.diff_utils.diff_utils import File\n", "from experimental.yury.smart_paste.augmentations import (\n", "    BeforeAfterFiles,\n", ")\n", "\n", "request_ids_to_save = df[(df[\"keep\"] == True) | (df[\"unsure\"] == True)][\n", "    \"request_id\"\n", "].to_list()\n", "\n", "print(f\"Preparing to save {len(request_ids_to_save)} samples\")\n", "\n", "\n", "def has_trailing_newline(s: str) -> bool:\n", "    return s.endswith(\"\\n\")\n", "\n", "\n", "counter = 0\n", "n_skip = 0\n", "n_empty_code_block = 0\n", "n_no_claude_response = 0\n", "n_chose_llama_response, n_chose_claude_response = 0, 0\n", "n_original_no_new_lines, n_claude_changed_new_lines = 0, 0\n", "n_last_span_only_adds_newline = 0\n", "\n", "with OUTPUT_FILE.open(\"w\") as f:\n", "    for request_id in tqdm.tqdm(smart_paste_inputs):\n", "        prompt_input = smart_paste_inputs[request_id]\n", "        if len(prompt_input.code_block) == 0:\n", "            n_empty_code_block += 1\n", "            continue\n", "\n", "        if request_id not in claude_smart_paste_responses:\n", "            n_no_claude_response += 1\n", "            continue\n", "\n", "        original_commit = smart_paste_inputs[request_id].aux[\"commit\"][\"after_file\"][\n", "            \"contents\"\n", "        ]\n", "        claude_response = claude_smart_paste_responses[request_id]\n", "\n", "        if claude_response.strip() == original_commit.strip():\n", "            response = claude_response\n", "            n_chose_claude_response += 1\n", "        elif request_id in llama_smart_paste_responses:\n", "            llama_response = llama_smart_paste_responses[request_id]\n", "            if llama_response.strip() == original_commit.strip():\n", "                response = llama_response\n", "                n_chose_llama_response += 1\n", "            elif llama_response.strip() == claude_response.strip():\n", "                response = claude_response\n", "                n_chose_claude_response += 1\n", "            else:\n", "                n_skip += 1\n", "                continue\n", "        else:\n", "            response = claude_response\n", "            n_chose_claude_response += 1\n", "\n", "        if not has_trailing_newline(prompt_input.target_file_content):\n", "            n_original_no_new_lines += 1\n", "            if has_trailing_newline(response):\n", "                n_claude_changed_new_lines += 1\n", "                before_after_file = BeforeAfterFiles(\n", "                    before_file=File(\n", "                        prompt_input.target_path, prompt_input.target_file_content\n", "                    ),\n", "                    after_file=File(prompt_input.target_path, response),\n", "                )\n", "                if last_span_only_adds_newline(before_after_file.str_diff):\n", "                    n_last_span_only_adds_newline += 1\n", "                    response = response.rstrip(NEWLINES)\n", "\n", "        output = {\n", "            \"request_id\": request_id,\n", "            \"input\": dataclasses.asdict(prompt_input),\n", "            \"response\": response,\n", "        }\n", "        json.dump(output, f)\n", "        f.write(\"\\n\")\n", "\n", "        counter += 1\n", "\n", "print(\n", "    f\"Saved {counter} samples, skipped {n_empty_code_block} with empty code blocks, {n_no_claude_response} no claude response, {n_skip} samples with no agreement\"\n", ")\n", "print(\n", "    f\"{n_chose_llama_response} chose llama response, {n_chose_claude_response} chose claude response\"\n", ")\n", "print(\n", "    f\"{n_original_no_new_lines} original no new lines, {n_claude_changed_new_lines} modified last line\"\n", ")\n", "print(f\"{n_last_span_only_adds_newline} last span only adds newline\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
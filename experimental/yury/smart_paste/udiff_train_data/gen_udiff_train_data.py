from pathlib import Path
import dataclasses
import json
import multiprocessing

import numpy as np
import tqdm
import pandas as pd

from base.diff_utils.diff_utils import compute_file_diff, File
from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from base.prompt_format_smart_paste.forger_prompt_formatter import (
    ForgerSmartPasteTokenApportionment,
)
from base.ranges.range_types import CharRange
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from experimental.yury.smart_paste.augmentations import (
    BeforeAfterFiles,
    get_union_span_range_in_before,
    CodeAugmentation,
)
from experimental.yury.smart_paste.udiff_train_data.udiff_prompt_formatter import (
    ForgerUDiffPromptFormatter,
)
from experimental.yury.smart_paste.udiff_train_data.corrupt_udiff import (
    corrupt_udiff,
)
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput
from megatron.data.indexed_dataset import MMapIndexedDataset<PERSON>uilder


INPUT_SAMPLES = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend/merged_smart_paste_responses.jsonl"
)
OUTPUT_DIR = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k_udiff_clean_git_wr"
)
# OUTPUT_DIR = Path("/tmp/06_1_training_data_stage_32k_udiff")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


MAX_TOKEN_BUDGET = 32768 + 1
N_PROCESSES = 6
P_NO_IDENTATION = 0.3
P_RANDOM_IDENTATION = 0.3

N_EVAL_SAMPLES = 256

# Stage 1: Load data

data = []
n_empty_code_block = 0
n_might_need_adjustment = 0

tokenizer = Qwen25CoderTokenizer()


token_apportionment = ForgerSmartPasteTokenApportionment(
    path_len=256,
    message_len=128,
    prefix_len=MAX_TOKEN_BUDGET - 1,
    suffix_len=MAX_TOKEN_BUDGET - 1,
    max_prompt_len=MAX_TOKEN_BUDGET,
    adjust_newline_from_suffix_to_selected_code=False,  # prod only option
)


class ModifiedCodeNotInPrompt(Exception):
    """Raised when the parts of modified code are not in the prompt."""

    def __init__(self, message: str = "Modified code is not in the prompt."):
        self.message = message
        super().__init__(self.message)


def format_sample(
    sample,
    tokenizer,
    max_token_budget: int,
):
    aux_output = {}
    prompt_input = sample["input"]
    random_state = np.random.RandomState(hash(prompt_input.request_id) & 0xFFFFFFFF)
    assert len(prompt_input.code_block) > 0

    # throw away old code_block
    # create code block from diff between target_file_content and response

    before_file = File(prompt_input.target_path, prompt_input.target_file_content)
    after_file = File(prompt_input.target_path, sample["response"])
    code_block = compute_file_diff(
        before_file,
        after_file,
        use_smart_header=False,
        same_line_smart_header=True,
    )

    # print("*** BEFORE ***")
    # print(code_block)
    corrupted_code_block = corrupt_udiff(code_block, random_state)
    # print("*** AFTER ***")
    # print(corrupted_code_block)
    # print("*** DIFF ***")
    # print(
    #     compute_file_diff(
    #         File(prompt_input.target_path, code_block),
    #         File(prompt_input.target_path, corrupted_code_block),
    #     )
    # )
    # breakpoint()

    corrupted_code_block = f"```diff\n{corrupted_code_block}\n```"
    prompt_input = dataclasses.replace(
        prompt_input,
        code_block=corrupted_code_block,
    )
    prompt_formatter = ForgerUDiffPromptFormatter(
        tokenizer,
        token_apportionment,
    )
    prompt_output = prompt_formatter.format_prompt(prompt_input)
    aux_output["prompt_lenght"] = len(prompt_output.tokens)

    # Compute StrDiff
    before_file = File(prompt_input.target_path, prompt_input.target_file_content)
    after_file = File(prompt_input.target_path, sample["response"])
    before_after_file = BeforeAfterFiles(before_file, after_file)

    before_crange = CharRange(
        len(prompt_output.unused_prefix),
        len(before_file.contents) - len(prompt_output.unused_suffix),
    )

    if len(prompt_input.selected_code) == 0:
        aux_output["empty_selected_code"] = 1
    if len(prompt_output.unused_prefix) > 0 or len(prompt_output.unused_suffix) > 0:
        aux_output["not_full_file"] = 1
    if len(before_crange) == 0:
        aux_output["empty_before_crange"] = 1

    # need to compute all_changes_span
    all_changes_span = get_union_span_range_in_before(before_after_file.str_diff, 0)

    if all_changes_span is None:
        aux_output["no_changes"] = 1
    else:
        if not before_crange.contains(all_changes_span):
            # If we see a lot of things happening then maybe I should adjust my
            # min_prefix_length / min_suffix_length computations.
            raise ModifiedCodeNotInPrompt()

    after_crange = before_after_file.str_diff.before_range_to_after(before_crange)
    if len(after_crange) == 0:
        aux_output["empty_after_crange"] = 1
    label = after_file.contents[after_crange.start : after_crange.stop]
    label_tokens = tokenizer.tokenize_safe(label) + [tokenizer.special_tokens.eos]
    aux_output["label_length"] = len(label_tokens)

    tokens = [-1 * t for t in prompt_output.tokens] + label_tokens
    aux_output["total_length"] = len(tokens)

    if len(tokens) > max_token_budget:
        raise ExceedContextLength()

    if len(tokens) < max_token_budget:
        tokens = tokens + [-tokenizer.special_tokens.padding] * (
            max_token_budget - len(tokens)
        )
    assert len(tokens) == max_token_budget

    return tokens, aux_output


print("Loading data...")
with INPUT_SAMPLES.open("r") as f:
    for line in tqdm.tqdm(f):
        datum = json.loads(line)
        prompt_input = ResearchSmartPastePromptInput.from_dict(datum["input"])
        datum["input"] = prompt_input
        if len(prompt_input.code_block) == 0:
            n_empty_code_block += 1
            continue
        if (
            len(prompt_input.suffix) > 0
            and prompt_input.suffix[0] == "\n"
            and len(prompt_input.selected_code) > 0
            and prompt_input.selected_code[-1] != "\n"
        ):
            n_might_need_adjustment += 1
        data.append(datum)

        # TESTING
        # format_sample(
        #     sample=datum,
        #     tokenizer=tokenizer,
        #     max_token_budget=MAX_TOKEN_BUDGET,
        # )

        if len(data) > 200000:
            break

print(f"Loaded {len(data)} samples, {n_empty_code_block} empty code blocks")
print(f"n_might_need_adjustment: {n_might_need_adjustment}")

np.random.seed(31415)
np.random.shuffle(data)

# Stage 2 -- definite processing function

# Stage 3 -- definite processing function


class Processor:
    """Processor for generating smart paste training data."""

    def __init__(
        self,
        max_token_budget,
        p_no_identation,
        p_random_identation,
    ):
        self.max_token_budget = max_token_budget
        self.p_no_identation = p_no_identation
        self.p_random_identation = p_random_identation

    def initialize(self):
        """Initialize processor-wide resources.."""
        # Initialize tokenizer and other resources
        Processor.tokenizer = Qwen25CoderTokenizer()
        Processor.max_token_budget = self.max_token_budget
        Processor.suggested_edit_augmentator = CodeAugmentation(
            p_no_identation=self.p_no_identation,
            p_random_identation=self.p_random_identation,
        )

    def __call__(self, sample):
        """Process a single sample.

        Args:
            sample: Input sample to process

        Returns:
            Tuple of (processed_data, stats_dict) or (None, error_stats_dict) on failure
        """
        try:
            tokens, aux_output = format_sample(
                sample=sample,
                tokenizer=self.tokenizer,
                max_token_budget=self.max_token_budget,
            )
        except ModifiedCodeNotInPrompt:
            return None, {}
        except ExceedContextLength:
            return None, {}
        except Exception:
            # print(f"Error: {e}")
            return None, {}

        padded_tokens = [x for x in tokens]
        if len(padded_tokens) < self.max_token_budget:
            padded_tokens.extend(
                [-self.tokenizer.special_tokens.padding]
                * (self.max_token_budget - len(padded_tokens))
            )
        assert len(padded_tokens) == self.max_token_budget

        # Return numpy array instead of torch tensor
        return np.array(padded_tokens, dtype=np.int32), aux_output


processor = Processor(
    max_token_budget=MAX_TOKEN_BUDGET,
    p_no_identation=P_NO_IDENTATION,
    p_random_identation=P_RANDOM_IDENTATION,
)


builder = {
    "valid_emptysc": MMapIndexedDatasetBuilder(
        str(OUTPUT_DIR / "valid_emptysc") + ".bin", dtype=np.int32
    ),
    "valid_non_emptysc": MMapIndexedDatasetBuilder(
        str(OUTPUT_DIR / "valid_non_emptysc") + ".bin", dtype=np.int32
    ),
    "train": MMapIndexedDatasetBuilder(
        str(OUTPUT_DIR / "train") + ".bin", dtype=np.int32
    ),
}
counter = {name: 0 for name in builder.keys()}
limits = {
    "valid_emptysc": N_EVAL_SAMPLES,
    "valid_non_emptysc": N_EVAL_SAMPLES,
    "train": 1_000_000_000,
}

aux_outputs = []
n_skipped = 0

with multiprocessing.Pool(N_PROCESSES, initializer=processor.initialize) as pool:
    pbar = tqdm.tqdm(pool.imap_unordered(processor, data), total=len(data))
    for tokens, aux_output in pbar:
        if tokens is None:
            n_skipped += 1
            continue
        aux_outputs.append(aux_output)
        assert len(tokens) == MAX_TOKEN_BUDGET

        if aux_output.get("empty_selected_code", False):
            builder_name = "valid_emptysc"
        else:
            builder_name = "valid_non_emptysc"

        if counter[builder_name] >= limits[builder_name]:
            builder_name = "train"

        counter[builder_name] += 1
        builder[builder_name].add_item(tokens)
        builder[builder_name].end_document()

        metrics = counter.copy()
        metrics["n_skipped"] = n_skipped
        pbar.set_postfix(metrics)

for name, b in builder.items():
    print(f"Finalizing {name}... {counter[name]} samples")
    b.finalize(str(OUTPUT_DIR / name) + ".idx")
print(f"Skipped {n_skipped} samples")

df = pd.DataFrame(aux_outputs)
df = df.fillna(0)
print(df.describe())

print("Done")

import re
import numpy as np
from typing import List

# Corruption probability constants
COMMON_MULTIPLIER = 1  # Common multiplier for all constants
PROB_LINE_NUMBER_CHANGE = 0.8 * COMMON_MULTIPLIER  # Reduced from 0.5
PROB_WHITESPACE_MODIFY = 0 * COMMON_MULTIPLIER  # Reduced from 0.3
PROB_SPACE_TO_TAB = 0 * COMMON_MULTIPLIER  # Reduced from 0.2
PROB_ADD_VS_REMOVE_SPACES = 0.5  # Reduced from 0.5
PROB_MERGE_LINES = 0 * COMMON_MULTIPLIER  # Reduced from 0.2
PROB_SPLIT_LINES = 0 * COMMON_MULTIPLIER  # Reduced from 0.2
PROB_REMOVE_CONTEXT = 0.3 * COMMON_MULTIPLIER  # Reduced from 0.3
PROB_REMOVE_START_VS_END = 0.1  # Reduced from 0.5
PROB_CORRUPT_WHITESPACE_PER_LINE = 0 * COMMON_MULTIPLIER  # Reduced from 0.2
PROB_CORRUPT_MULTILINE = 0 * COMMON_MULTIPLIER  # Reduced from 0.3
PROB_DUPLICATE_CONTEXT = 0 * COMMON_MULTIPLIER  # Reduced from 0.2
PROB_REORDER_IMPORTS = 0 * COMMON_MULTIPLIER  # Reduced from 0.3
PROB_REMOVE_HEADER_CONTEXT = (
    0.3 * COMMON_MULTIPLIER
)  # New probability for header context removal

# Line length constants
MAX_MERGED_LINE_LENGTH = 100
MIN_SPLITTABLE_LINE_LENGTH = 20


def _parse_udiff_header(header: str) -> tuple[int, int, int, int, str]:
    """Parse the unified diff header to get line numbers and context text.

    Returns:
        Tuple of (old_start, old_count, new_start, new_count, context_text)
    """
    match = re.match(r"@@ -(\d+),(\d+) \+(\d+),(\d+) @@(.*)$", header)
    if not match:
        raise ValueError("Invalid udiff header format")
    numbers = tuple(map(int, match.groups()[:4]))
    context_text = match.group(5)
    return numbers[0], numbers[1], numbers[2], numbers[3], context_text


def _corrupt_line_numbers(header: str, random_state: np.random.RandomState) -> str:
    """Randomly modify line numbers in the diff header while preserving context text."""
    old_start, old_count, new_start, new_count, context_text = _parse_udiff_header(
        header
    )

    # Randomly adjust line numbers slightly
    if random_state.random() < PROB_LINE_NUMBER_CHANGE:
        old_start += random_state.randint(-5, 5)
        new_start += random_state.randint(-5, 5)
        old_start = max(1, old_start)
        new_start = max(1, new_start)

    # Randomly remove header context
    if random_state.random() < PROB_REMOVE_HEADER_CONTEXT:
        context_text = ""

    return f"@@ -{old_start},{old_count} +{new_start},{new_count} @@{context_text}"


def _corrupt_whitespace(line: str, random_state: np.random.RandomState) -> str:
    """Randomly modify whitespace in a line."""
    if not line.strip() or line.startswith("@@"):
        return line

    # Preserve the diff marker if present
    marker = ""
    if line.startswith(("+", "-", " ")):
        marker = line[0]
        line = line[1:]

    # Randomly add or remove spaces
    if random_state.random() < PROB_WHITESPACE_MODIFY:
        if random_state.random() < PROB_ADD_VS_REMOVE_SPACES:
            # Add spaces
            line = line.replace(" ", "  ")
        else:
            # Remove extra spaces
            line = re.sub(r"\s+", " ", line)

    # Randomly convert spaces to tabs or vice versa
    if random_state.random() < PROB_SPACE_TO_TAB:
        if "\t" in line:
            line = line.replace("\t", "    ")
        else:
            line = line.replace("    ", "\t")

    return marker + line


def _corrupt_multiline(
    lines: List[str], random_state: np.random.RandomState
) -> List[str]:
    """Randomly modify multiline statements."""
    result = []
    i = 0
    in_function_def = False
    function_body_level = 0  # Track indentation level of function body

    while i < len(lines):
        line = lines[i]

        # Skip diff headers and empty lines
        if line.startswith("@@") or not line.strip():
            result.append(line)
            i += 1
            continue

        # Check for function/class definitions and their continuations
        is_def_line = bool(re.match(r"^[+ -](?:def|class)\s+", line))
        is_def_continuation = in_function_def and (
            line.rstrip().endswith(":")
            or line.rstrip().endswith("(")
            or line.rstrip().endswith(",")
            or ")" in line
        )

        # Handle function definitions and their continuations
        if is_def_line or is_def_continuation:
            in_function_def = not line.rstrip().endswith(":")
            if line.rstrip().endswith(":"):
                # Get the indentation level for the function body
                function_body_level = len(line) - len(line.lstrip())
            result.append(line)
            i += 1
            continue

        # Check if we're in a function body by comparing indentation
        current_indent = len(line) - len(line.lstrip())
        in_function_body = (
            function_body_level > 0 and current_indent > function_body_level
        )

        # Reset function definition state if we're not in a continuation
        if not is_def_continuation and not in_function_body:
            in_function_def = False
            function_body_level = 0

        # Check if we can merge lines (avoid merging function body lines)
        if (
            i < len(lines) - 1
            and not lines[i + 1].startswith("@@")
            and not re.match(r"^[+ -](?:def|class)\s+", lines[i + 1])
            and not in_function_def
            and not in_function_body
            and random_state.random() < PROB_MERGE_LINES
        ):
            # Merge with next line, preserving the diff marker
            next_line = lines[i + 1]
            marker = line[0] if line[0] in "+-" else " "
            next_marker = next_line[0] if next_line[0] in "+-" else " "

            # Only merge if markers are compatible
            if marker == next_marker:
                merged = marker + line[1:].rstrip() + " " + next_line[1:].lstrip()
                if (
                    len(merged) < MAX_MERGED_LINE_LENGTH
                ):  # Avoid creating very long lines
                    result.append(merged)
                    i += 2
                    continue

        # Check if we can split line (avoid splitting function body lines)
        if (
            len(line) > MIN_SPLITTABLE_LINE_LENGTH
            and not re.match(r"^[+ -](?:def|class)\s+", line)
            and not in_function_def
            and not in_function_body
            and random_state.random() < PROB_SPLIT_LINES
        ):
            # Try to split at a reasonable position
            split_pos = line.find(" ", len(line) // 2)
            if split_pos != -1:
                marker = line[0] if line[0] in "+-" else " "
                result.append(line[:split_pos])
                result.append(marker + line[split_pos:].lstrip())
                i += 1
                continue

        result.append(line)
        i += 1

    return result


def _reorder_imports(
    lines: List[str], random_state: np.random.RandomState
) -> List[str]:
    """Randomly reorder import statements while preserving diff markers."""
    import_lines = []
    import_indices = []

    # Collect import statements
    for i, line in enumerate(lines):
        if "import " in line and not line.startswith("@@"):
            import_lines.append(line)
            import_indices.append(i)

    if len(import_lines) > 1:
        # Shuffle import lines while preserving their markers
        shuffled = list(range(len(import_lines)))
        random_state.shuffle(shuffled)

        result = lines.copy()
        for old_idx, new_idx in enumerate(shuffled):
            result[import_indices[old_idx]] = import_lines[new_idx]

        return result

    return lines


def corrupt_udiff_safe(udiff: str, random_state: np.random.RandomState) -> str:
    try:
        return corrupt_udiff(udiff, random_state)
    except Exception as e:
        print(f"Failed to corrupt udiff: {e}")
        import traceback

        traceback.print_exc()
        return udiff


def corrupt_udiff(udiff: str, random_state: np.random.RandomState) -> str:
    """Corrupt a unified diff by randomly modifying various aspects."""
    lines = udiff.splitlines()
    if not lines:
        raise ValueError("Empty unified diff")

    if not udiff or "@@ " not in udiff:
        raise ValueError("Invalid or empty unified diff")

    # Find the diff header
    header_idx = next((i for i, line in enumerate(lines) if line.startswith("@@")), -1)
    if header_idx == -1:
        raise ValueError("No diff header found")

    # Corrupt the header line numbers
    lines[header_idx] = _corrupt_line_numbers(lines[header_idx], random_state)

    # Randomly remove context lines at the beginning or end
    if random_state.random() < PROB_REMOVE_CONTEXT:
        if random_state.random() < PROB_REMOVE_START_VS_END:
            # Remove from beginning - find first non-context line
            first_non_context_idx = header_idx + 1
            while first_non_context_idx < len(lines) and lines[
                first_non_context_idx
            ].startswith(" "):
                first_non_context_idx += 1

            if first_non_context_idx > header_idx + 1:
                # Remove all context lines before the first non-context line
                lines = lines[: header_idx + 1] + lines[first_non_context_idx:]
        else:
            # Remove from end - find last non-context line
            last_non_context_idx = len(lines) - 1
            while last_non_context_idx > header_idx and lines[
                last_non_context_idx
            ].startswith(" "):
                last_non_context_idx -= 1

            if last_non_context_idx < len(lines) - 1:
                # Remove all context lines after the last non-context line
                lines = lines[: last_non_context_idx + 1]

    # Apply other corruptions with some probability
    corrupted_lines = []
    for line in lines:
        # Corrupt whitespace
        if random_state.random() < PROB_CORRUPT_WHITESPACE_PER_LINE:
            line = _corrupt_whitespace(line, random_state)
        corrupted_lines.append(line)

    # Potentially corrupt multiline statements
    if random_state.random() < PROB_CORRUPT_MULTILINE:
        corrupted_lines = _corrupt_multiline(corrupted_lines, random_state)

    # Randomly duplicate a context line
    if random_state.random() < PROB_DUPLICATE_CONTEXT:
        context_lines = [
            i for i, line in enumerate(corrupted_lines) if line.startswith(" ")
        ]
        if context_lines:
            idx = random_state.choice(context_lines)
            corrupted_lines.insert(idx, corrupted_lines[idx])

    # Potentially reorder import statements
    if random_state.random() < PROB_REORDER_IMPORTS:
        corrupted_lines = _reorder_imports(corrupted_lines, random_state)

    return "\n".join(corrupted_lines)

import pytest
import numpy as np
from experimental.yury.smart_paste.udiff_train_data.corrupt_udiff import (
    corrupt_udiff,
    _corrupt_multiline,
)


@pytest.fixture
def random_state():
    return np.random.RandomState(42)


def test_corrupt_multiline_empty_list():
    random_state = np.random.RandomState(42)
    assert _corrupt_multiline([], random_state) == []


def test_corrupt_multiline_preserves_headers():
    random_state = np.random.RandomState(42)
    lines = ["@@ -1,3 +1,3 @@", "line1", "line2"]
    result = _corrupt_multiline(lines, random_state)
    assert result[0] == "@@ -1,3 +1,3 @@"


def test_corrupt_multiline_preserves_function_definitions():
    random_state = np.random.RandomState(42)
    lines = ["+def test_function():", "    line1", "    line2"]
    result = _corrupt_multiline(lines, random_state)
    assert result[0] == "+def test_function():"


def test_corrupt_multiline_preserves_class_definitions():
    random_state = np.random.RandomState(42)
    lines = ["+class TestClass:", "    line1", "    line2"]
    result = _corrupt_multiline(lines, random_state)
    assert result[0] == "+class TestClass:"


def test_corrupt_multiline_can_merge_lines():
    random_state = np.random.RandomState(42)  # This seed should trigger a merge
    lines = ["+x = 1", "+y = 2"]
    result = _corrupt_multiline(lines, random_state)
    # Lines should be merged
    assert len(result) <= len(lines)
    # The merged line should preserve the + marker
    if len(result) < len(lines):
        assert result[0].startswith("+")


def test_corrupt_multiline_can_split_lines():
    random_state = np.random.RandomState(24)  # This seed should trigger a split
    long_line = "+result = this is a very long line that should be split somewhere in the middle"
    lines = [long_line]
    result = _corrupt_multiline(lines, random_state)
    # Line should be split
    assert len(result) >= len(lines)
    # All lines should preserve the + marker
    assert all(line.startswith("+") for line in result)


def test_corrupt_multiline_preserves_diff_markers():
    random_state = np.random.RandomState(42)
    lines = ["+added_line", "-removed_line", " context_line"]
    result = _corrupt_multiline(lines, random_state)
    for line in result:
        assert line[0] in "+-" or line.startswith("@@") or line.startswith(" ")


def test_corrupt_multiline_respects_max_length():
    random_state = np.random.RandomState(42)
    # Create two lines that would exceed MAX_MERGED_LINE_LENGTH if merged
    long_line1 = "+x = " + "a" * 50
    long_line2 = "+y = " + "b" * 50
    lines = [long_line1, long_line2]
    result = _corrupt_multiline(lines, random_state)
    # No line in result should exceed MAX_MERGED_LINE_LENGTH
    assert all(len(line) <= 100 for line in result)


def test_corrupt_udiff_basic(random_state):
    udiff = """@@ -1,5 +1,6 @@
 import os
 import sys
+import numpy as np

 def hello():
     print("Hello")"""

    corrupted = corrupt_udiff(udiff, random_state)
    assert isinstance(corrupted, str)
    assert corrupted != udiff  # Should be different from original
    assert "@@ " in corrupted  # Should still have diff header
    assert "import" in corrupted  # Should still have some imports


def test_corrupt_udiff_preserves_structure(random_state):
    udiff = """@@ -10,4 +10,5 @@
     x = 1
     y = 2
+    z = 3
     return x + y"""

    corrupted = corrupt_udiff(udiff, random_state)
    assert isinstance(corrupted, str)
    assert "@@ " in corrupted
    assert "return" in corrupted  # Should preserve key elements
    assert "+" in corrupted  # Should preserve diff markers


def test_corrupt_udiff_multiline(random_state):
    udiff = """@@ -1,6 +1,7 @@
     def long_function(
         param1,
         param2
     ):
    +    param3 = 42
         return param1 + param2"""

    # Try multiple seeds until we find one that works
    for seed in range(1, 100):
        fixed_random_state = np.random.RandomState(seed)
        corrupted = corrupt_udiff(udiff, fixed_random_state)
        if all(x in corrupted for x in ["def long_function", "param3", "return"]):
            break

    assert isinstance(corrupted, str)
    assert "@@ " in corrupted
    assert "def long_function" in corrupted  # Function definition should be preserved

    # Check that the added line is preserved with its diff marker
    lines = corrupted.splitlines()
    added_lines = [line for line in lines if line.strip().startswith("+")]
    assert any(
        "param3" in line for line in added_lines
    )  # Added line should be preserved with + marker

    # Check that the function structure is maintained
    assert any("return" in line for line in lines)  # Return statement should be present
    assert any(
        line.strip().startswith("def long_function") for line in lines
    )  # Function definition should be present


def test_corrupt_udiff_empty_input(random_state):
    with pytest.raises(ValueError):
        corrupt_udiff("", random_state)


def test_corrupt_udiff_invalid_input(random_state):
    with pytest.raises(ValueError):
        corrupt_udiff("not a valid udiff", random_state)

{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import math\n", "import sklearn.feature_extraction.text\n", "import random\n", "\n", "import pandas as pd\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[25], line 34\u001b[0m\n\u001b[1;32m     32\u001b[0m \u001b[39m# Make N_SAMPLES = 1000 for faster results\u001b[39;00m\n\u001b[1;32m     33\u001b[0m N_SAMPLES \u001b[39m=\u001b[39m \u001b[39m10000\u001b[39m\n\u001b[0;32m---> 34\u001b[0m data_random_context \u001b[39m=\u001b[39m load_data(N_SAMPLES, \u001b[39m2176\u001b[39;49m, \u001b[39m1\u001b[39;49m, \u001b[39m2048\u001b[39;49m, \u001b[39m8\u001b[39;49m)\n\u001b[1;32m     35\u001b[0m data_2k_context \u001b[39m=\u001b[39m load_data(N_SAMPLES, \u001b[39m2176\u001b[39m, \u001b[39m2048\u001b[39m, \u001b[39m2048\u001b[39m, \u001b[39m8\u001b[39m)\n", "Cell \u001b[0;32mIn[25], line 7\u001b[0m, in \u001b[0;36mload_data\u001b[0;34m(n_samples, min_tokens_per_sample, min_context_length, max_context_length, max_tokens_to_predict)\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mload_data\u001b[39m(n_samples, min_tokens_per_sample, min_context_length, max_context_length, max_tokens_to_predict):\n\u001b[1;32m      6\u001b[0m     data \u001b[39m=\u001b[39m []\n\u001b[0;32m----> 7\u001b[0m     df \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39;49mread_parquet(DATA_PATH, engine\u001b[39m=\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39mpyarrow\u001b[39;49m\u001b[39m'\u001b[39;49m, columns\u001b[39m=\u001b[39;49m[\u001b[39m'\u001b[39;49m\u001b[39mcontent\u001b[39;49m\u001b[39m'\u001b[39;49m],)\n\u001b[1;32m      8\u001b[0m     \u001b[39mfor\u001b[39;00m row_index, datum \u001b[39min\u001b[39;00m df\u001b[39m.\u001b[39miterrows():\n\u001b[1;32m      9\u001b[0m         tokens \u001b[39m=\u001b[39m tokenizer\u001b[39m.\u001b[39mtokenize(datum[\u001b[39m'\u001b[39m\u001b[39mcontent\u001b[39m\u001b[39m'\u001b[39m])\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pandas/io/parquet.py:509\u001b[0m, in \u001b[0;36mread_parquet\u001b[0;34m(path, engine, columns, storage_options, use_nullable_dtypes, dtype_backend, **kwargs)\u001b[0m\n\u001b[1;32m    506\u001b[0m     use_nullable_dtypes \u001b[39m=\u001b[39m \u001b[39mFalse\u001b[39;00m\n\u001b[1;32m    507\u001b[0m check_dtype_backend(dtype_backend)\n\u001b[0;32m--> 509\u001b[0m \u001b[39mreturn\u001b[39;00m impl\u001b[39m.\u001b[39;49mread(\n\u001b[1;32m    510\u001b[0m     path,\n\u001b[1;32m    511\u001b[0m     columns\u001b[39m=\u001b[39;49mcolumns,\n\u001b[1;32m    512\u001b[0m     storage_options\u001b[39m=\u001b[39;49mstorage_options,\n\u001b[1;32m    513\u001b[0m     use_nullable_dtypes\u001b[39m=\u001b[39;49muse_nullable_dtypes,\n\u001b[1;32m    514\u001b[0m     dtype_backend\u001b[39m=\u001b[39;49mdtype_backend,\n\u001b[1;32m    515\u001b[0m     \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs,\n\u001b[1;32m    516\u001b[0m )\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pandas/io/parquet.py:227\u001b[0m, in \u001b[0;36mPyArrowImpl.read\u001b[0;34m(self, path, columns, use_nullable_dtypes, dtype_backend, storage_options, **kwargs)\u001b[0m\n\u001b[1;32m    220\u001b[0m path_or_handle, handles, kwargs[\u001b[39m\"\u001b[39m\u001b[39mfilesystem\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m _get_path_or_handle(\n\u001b[1;32m    221\u001b[0m     path,\n\u001b[1;32m    222\u001b[0m     kwargs\u001b[39m.\u001b[39mpop(\u001b[39m\"\u001b[39m\u001b[39mfilesystem\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39mNone\u001b[39;00m),\n\u001b[1;32m    223\u001b[0m     storage_options\u001b[39m=\u001b[39mstorage_options,\n\u001b[1;32m    224\u001b[0m     mode\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mrb\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m    225\u001b[0m )\n\u001b[1;32m    226\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 227\u001b[0m     pa_table \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mapi\u001b[39m.\u001b[39;49mparquet\u001b[39m.\u001b[39;49mread_table(\n\u001b[1;32m    228\u001b[0m         path_or_handle, columns\u001b[39m=\u001b[39;49mcolumns, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs\n\u001b[1;32m    229\u001b[0m     )\n\u001b[1;32m    230\u001b[0m     result \u001b[39m=\u001b[39m pa_table\u001b[39m.\u001b[39mto_pandas(\u001b[39m*\u001b[39m\u001b[39m*\u001b[39mto_pandas_kwargs)\n\u001b[1;32m    232\u001b[0m     \u001b[39mif\u001b[39;00m manager \u001b[39m==\u001b[39m \u001b[39m\"\u001b[39m\u001b[39marray\u001b[39m\u001b[39m\"\u001b[39m:\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyarrow/parquet/core.py:2986\u001b[0m, in \u001b[0;36mread_table\u001b[0;34m(source, columns, use_threads, metadata, schema, use_pandas_metadata, read_dictionary, memory_map, buffer_size, partitioning, filesystem, filters, use_legacy_dataset, ignore_prefixes, pre_buffer, coerce_int96_timestamp_unit, decryption_properties, thrift_string_size_limit, thrift_container_size_limit)\u001b[0m\n\u001b[1;32m   2975\u001b[0m         \u001b[39m# TODO test that source is not a directory or a list\u001b[39;00m\n\u001b[1;32m   2976\u001b[0m         dataset \u001b[39m=\u001b[39m ParquetFile(\n\u001b[1;32m   2977\u001b[0m             source, metadata\u001b[39m=\u001b[39mmetadata, read_dictionary\u001b[39m=\u001b[39mread_dictionary,\n\u001b[1;32m   2978\u001b[0m             memory_map\u001b[39m=\u001b[39mmemory_map, buffer_size\u001b[39m=\u001b[39mbuffer_size,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2983\u001b[0m             thrift_container_size_limit\u001b[39m=\u001b[39mthrift_container_size_limit,\n\u001b[1;32m   2984\u001b[0m         )\n\u001b[0;32m-> 2986\u001b[0m     \u001b[39mreturn\u001b[39;00m dataset\u001b[39m.\u001b[39;49mread(columns\u001b[39m=\u001b[39;49mcolumns, use_threads\u001b[39m=\u001b[39;49muse_threads,\n\u001b[1;32m   2987\u001b[0m                         use_pandas_metadata\u001b[39m=\u001b[39;49muse_pandas_metadata)\n\u001b[1;32m   2989\u001b[0m warnings\u001b[39m.\u001b[39mwarn(\n\u001b[1;32m   2990\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mPassing \u001b[39m\u001b[39m'\u001b[39m\u001b[39muse_legacy_dataset=True\u001b[39m\u001b[39m'\u001b[39m\u001b[39m to get the legacy behaviour is \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2991\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mdeprecated as of pyarrow 8.0.0, and the legacy implementation will \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2992\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mbe removed in a future version.\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m   2993\u001b[0m     \u001b[39mFutureWarning\u001b[39;00m, stacklevel\u001b[39m=\u001b[39m\u001b[39m2\u001b[39m)\n\u001b[1;32m   2995\u001b[0m \u001b[39mif\u001b[39;00m ignore_prefixes \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyarrow/parquet/core.py:2614\u001b[0m, in \u001b[0;36m_ParquetDatasetV2.read\u001b[0;34m(self, columns, use_threads, use_pandas_metadata)\u001b[0m\n\u001b[1;32m   2606\u001b[0m         index_columns \u001b[39m=\u001b[39m [\n\u001b[1;32m   2607\u001b[0m             col \u001b[39mfor\u001b[39;00m col \u001b[39min\u001b[39;00m _get_pandas_index_columns(metadata)\n\u001b[1;32m   2608\u001b[0m             \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39misinstance\u001b[39m(col, \u001b[39mdict\u001b[39m)\n\u001b[1;32m   2609\u001b[0m         ]\n\u001b[1;32m   2610\u001b[0m         columns \u001b[39m=\u001b[39m (\n\u001b[1;32m   2611\u001b[0m             \u001b[39mlist\u001b[39m(columns) \u001b[39m+\u001b[39m \u001b[39mlist\u001b[39m(\u001b[39mset\u001b[39m(index_columns) \u001b[39m-\u001b[39m \u001b[39mset\u001b[39m(columns))\n\u001b[1;32m   2612\u001b[0m         )\n\u001b[0;32m-> 2614\u001b[0m table \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_dataset\u001b[39m.\u001b[39;49mto_table(\n\u001b[1;32m   2615\u001b[0m     columns\u001b[39m=\u001b[39;49mcolumns, \u001b[39mfilter\u001b[39;49m\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_filter_expression,\n\u001b[1;32m   2616\u001b[0m     use_threads\u001b[39m=\u001b[39;49muse_threads\n\u001b[1;32m   2617\u001b[0m )\n\u001b[1;32m   2619\u001b[0m \u001b[39m# if use_pandas_metadata, restore the pandas metadata (which gets\u001b[39;00m\n\u001b[1;32m   2620\u001b[0m \u001b[39m# lost if doing a specific `columns` selection in to_table)\u001b[39;00m\n\u001b[1;32m   2621\u001b[0m \u001b[39mif\u001b[39;00m use_pandas_metadata:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["DATA_PATH = \"/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=python/data_0000.parquet\"\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "def load_data(n_samples, min_tokens_per_sample, min_context_length, max_context_length, max_tokens_to_predict):\n", "    data = []\n", "    df = pd.read_parquet(DATA_PATH, engine='pyarrow', columns=['content'],)\n", "    for row_index, datum in df.iterrows():\n", "        tokens = tokenizer.tokenize(datum['content'])\n", "        if len(tokens) < min_tokens_per_sample:\n", "            continue\n", "\n", "        token_index_to_predict = np.random.randint(\n", "            # Input context must have at least `max_tokens_to_predict + 1`, so\n", "            # we can always predict `max_tokens_to_predict` tokens.\n", "            max(min_context_length, max_tokens_to_predict + 1),\n", "            len(tokens) - max_tokens_to_predict + 1)\n", "        context_start = max(0, token_index_to_predict - max_context_length)\n", "        context = np.array(tokens[context_start:token_index_to_predict])\n", "        label = np.array(tokens[token_index_to_predict:token_index_to_predict + max_tokens_to_predict])\n", "\n", "        data.append({\n", "            'context': context,\n", "            'label': label,\n", "        })\n", "        if len(data) >= n_samples:\n", "            break\n", "\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), row_index))\n", "    return data\n", "\n", "# Make N_SAMPLES = 1000 for faster results\n", "N_SAMPLES = 10000\n", "data_random_context = load_data(N_SAMPLES, 2176, 1, 2048, 8)\n", "data_2k_context = load_data(N_SAMPLES, 2176, 2048, 2048, 8)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["([4, 5], {'overlap_length': 2, 'overlap_position': 1})"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["def find_kmp(text, pattern):\n", "    \"\"\"Find sublist `pattern` within list `text` via KMP algorithm.\"\"\"\n", "    n = len(pattern)\n", "    m = len(text)\n", "    next_v = [0] * n\n", "    for i in range(1, n):\n", "        j = next_v[i - 1]\n", "        while j > 0 and pattern[i] != pattern[j]:\n", "            j = next_v[j - 1]\n", "        if pattern[i] == pattern[j]:\n", "            j += 1\n", "        next_v[i] = j\n", "    j = 0\n", "    for i in range(m):\n", "        while j > 0 and text[i] != pattern[j]:\n", "            j = next_v[j - 1]\n", "        if text[i] == pattern[j]:\n", "            j += 1\n", "        if j == n:\n", "            return i - n + 1\n", "    return -1\n", "\n", "def find(text, pattern, search_priority):\n", "    if search_priority == 'first':\n", "        return find_kmp(text, pattern)\n", "    elif search_priority == 'last':\n", "        position = find_kmp(list(reversed(text)), list(reversed(pattern)))\n", "        if position == -1:\n", "            return -1\n", "        else:\n", "            return len(text) - position - len(pattern)\n", "    else:\n", "        raise ValueError('Unknown search priority ' + search_priority)\n", "\n", "class LongestSubstringLM:\n", "\n", "    def __init__(self, search_priority):\n", "        self.search_priority = search_priority\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = tokens\n", "\n", "    def predict_next_k_tokens(self, suffix, k):    \n", "        if len(self.tokens) < k:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return [0] * k, {'overlap_length': 0, 'overlap_position': -1}\n", "        searchable_tokens = self.tokens[:-k]\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, self.search_priority)\n", "            if target_pos == -1:\n", "                # -1 means we didn't find it\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "        if min_length == 0:                        \n", "            return [0] * k, {'overlap_length': 0, 'overlap_position': -1}\n", "        else:\n", "            target_suffix = suffix[-min_length:]\n", "            target_pos = find(searchable_tokens, target_suffix, self.search_priority)\n", "            assert not target_pos == -1\n", "            return self.tokens[target_pos + min_length: target_pos + min_length + k], {'overlap_length': min_length, 'overlap_position': target_pos}\n", "\n", "\n", "predictor = LongestSubstringLM('first')\n", "predictor.fit([1, 2, 3, 4, 5])\n", "predictor.predict_next_k_tokens([2, 3], k=2)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'context_length': 6,\n", " 'overlap_length': 2,\n", " 'overlap_position': 1,\n", " 'accuracy_1': 1,\n", " 'accuracy_2': 1,\n", " 'accuracy_3': 0}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def evaluate_sample(lm, sample, save_original_features=False):\n", "    context = sample['context']\n", "    n_tokens_to_predict = len(sample['label'])\n", "    result = {\n", "        'context_length': len(context),\n", "    }\n", "    if save_original_features:\n", "        result.update({\n", "        'context': context,\n", "        'label': sample['label'],\n", "    })\n", "\n", "    lm.fit(context)\n", "    predictions, aux_output = lm.predict_next_k_tokens(\n", "        context,\n", "        n_tokens_to_predict)\n", "    result.update(aux_output)\n", "\n", "    predictions = np.array(predictions)\n", "    is_correct = (predictions == sample['label']).astype(np.int32)\n", "    is_correct = np.cumprod(is_correct)\n", "    for i in range(n_tokens_to_predict):\n", "        result['accuracy_' + str(i+1)] = is_correct[i]\n", "\n", "    return result\n", "\n", "lm = LongestSubstringLM('first')\n", "evaluate_sample(lm, {'context': [0, 1, 2, 3, 1, 2], 'label': [3, 1, 4]})"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["context_length    2048.0000\n", "overlap_length      13.1997\n", "accuracy_1           0.5337\n", "accuracy_2           0.3974\n", "accuracy_3           0.3252\n", "accuracy_4           0.2773\n", "accuracy_5           0.2420\n", "accuracy_6           0.2143\n", "accuracy_7           0.1946\n", "accuracy_8           0.1776\n", "dtype: float64\n", "--\n", "context_length    2048.0000\n", "overlap_length      13.1997\n", "accuracy_1           0.5570\n", "accuracy_2           0.4225\n", "accuracy_3           0.3426\n", "accuracy_4           0.2914\n", "accuracy_5           0.2527\n", "accuracy_6           0.2233\n", "accuracy_7           0.2033\n", "accuracy_8           0.1849\n", "dtype: float64\n", "--\n", "context_length    1499.1462\n", "overlap_length      10.2475\n", "accuracy_1           0.5008\n", "accuracy_2           0.3764\n", "accuracy_3           0.3057\n", "accuracy_4           0.2610\n", "accuracy_5           0.2260\n", "accuracy_6           0.1988\n", "accuracy_7           0.1783\n", "accuracy_8           0.1589\n", "dtype: float64\n"]}], "source": ["def evaluate_data(lm, data, save_original_features=False):\n", "    df = []\n", "    for d in data:\n", "        df.append(evaluate_sample(lm=lm, sample=d, save_original_features=save_original_features))        \n", "    df = pd.DataFrame(df)\n", "    if save_original_features:\n", "        print(df.drop(['context', 'label'], axis=1).mean())\n", "    else:\n", "        print(df.mean())\n", "    return df\n", "\n", "lm = LongestSubstringLM('first')\n", "_ = evaluate_data(\n", "    lm=lm,\n", "    data=data_2k_context)\n", "print('--')\n", "lm = LongestSubstringLM('last')\n", "df_2k_context = evaluate_data(\n", "    lm=lm,\n", "    data=data_2k_context)\n", "print('--')\n", "df_random_context = evaluate_data(\n", "    lm=lm,\n", "    data=data_random_context)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot(df, target_feature, bins, metrics, title):\n", "    fig, ax = plt.subplots()\n", "    bin_ids = np.digitize(df[target_feature], bins, right=True)\n", "    for metric_id, metric in enumerate(metrics):\n", "        bin_counts = df.groupby(bin_ids).count()[metric]\n", "        ax.plot(\n", "            np.arange(len(bin_counts)),\n", "            df.groupby(bin_ids).mean()[metric],\n", "            label=metric\n", "        )\n", "        if metric_id == 0:\n", "            xticklabels = ['<= %d\\n(%d)' % (bins[0], bin_counts[0])]\n", "            for i in range(len(bins) - 1):\n", "                xticklabels.append('%d-%d\\n(%d)' % (bins[i]+1, bins[i+1], bin_counts[i+1]))\n", "            xticklabels.append('> %d\\n(%d)' % (bins[len(bins) - 1], bin_counts[len(bin_counts) - 1]))\n", "            ax.set_xticks(np.arange(len(bin_counts)))\n", "            ax.set_xticklabels(xticklabels)\n", "    ax.legend()\n", "    ax.grid()\n", "    ax.set_xlabel(target_feature)\n", "    ax.set_ylabel('Accuracy, %')\n", "    ax.set_title(title)\n", "    fig.show()\n", "\n", "plot(\n", "    df_random_context,\n", "    target_feature='overlap_length',\n", "    bins=[0, 1, 2, 4, 6, 8, 10],\n", "    metrics=['accuracy_1', 'accuracy_2', 'accuracy_4', 'accuracy_8'],\n", "    title='Random context from 0 to 2048')\n", "\n", "# pragma: allowlist nextline secret"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(\n", "    df_2k_context,\n", "    target_feature='overlap_length',\n", "    bins=[0, 1, 2, 4, 6, 8, 10],\n", "    metrics=['accuracy_1', 'accuracy_2', 'accuracy_4', 'accuracy_8'],\n", "    title='Context is fixed to be 2048')\n", "\n", "# pragma: allowlist nextline secret"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(\n", "    df_random_context,\n", "    target_feature='context_length',\n", "    bins=[128, 256, 512, 1024, 2047],\n", "    metrics=['accuracy_1', 'accuracy_2', 'accuracy_4', 'accuracy_8'],\n", "    title='Random context from 0 to 2048')\n", "\n", "# pragma: allowlist nextline secret"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# from https://docs.google.com/presentation/d/1V1XQ0IxEEPSekTvWW10KPR3Bg5BDRultJQGotCQd3mg/edit#slide=id.g237b45739c5_0_21\n", "LATENCY = {\n", "    1: 24.2,\n", "    2: 24.8,\n", "    4: 24.9,\n", "    8: 24.7,\n", "    16: 25,\n", "    20: 26.1,\n", "    24: 25.9,\n", "    28: 26,\n", "    32: 26.1,\n", "    64: 27.6,\n", "    96: 29,\n", "    128: 30.1,\n", "    160: 40.5,\n", "    192: 42,\n", "    224: 43,\n", "    256: 44,\n", "    384: 62,\n", "    512: 81.6,\n", "}\n", "\n", "LATENCY_KEYS = np.array(sorted(list(LATENCY.keys())))\n", "\n", "def estimate_latency(n):\n", "    assert n > 0 and n <= 512\n", "    if n in LATENCY:\n", "        return LATENCY[n]\n", "    n_lower_index = np.searchsorted(LATENCY_KEYS, n)\n", "    n_lower = LATENCY_KEYS[n_lower_index - 1]\n", "    n_upper = LATENCY_KEYS[n_lower_index]\n", "    return LATENCY[n_lower] * (n_upper - n) / (n_upper - n_lower) + LATENCY[n_upper] * (n - n_lower) / (n_upper - n_lower)\n", "\n", "for n in range(1, 512):\n", "    LATENCY[n] = estimate_latency(n)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'load_data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m data_c2k_l64 \u001b[39m=\u001b[39m load_data(\u001b[39m10000\u001b[39m, \u001b[39m2176\u001b[39m, \u001b[39m2048\u001b[39m, \u001b[39m2048\u001b[39m, \u001b[39m64\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'load_data' is not defined"]}], "source": ["data_c2k_l64 = load_data(10000, 2176, 2048, 2048, 64)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'LongestSubstringLM' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 32\u001b[0m\n\u001b[1;32m     23\u001b[0m         latency \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m LATENCY[n_tokens_to_predict \u001b[39m+\u001b[39m \u001b[39m1\u001b[39m]\n\u001b[1;32m     25\u001b[0m     \u001b[39mreturn\u001b[39;00m {\n\u001b[1;32m     26\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mcontext_length\u001b[39m\u001b[39m'\u001b[39m: \u001b[39mlen\u001b[39m(context),\n\u001b[1;32m     27\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mlabel_length\u001b[39m\u001b[39m'\u001b[39m: \u001b[39mlen\u001b[39m(label),\n\u001b[1;32m     28\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mn_rounds\u001b[39m\u001b[39m'\u001b[39m: n_rounds,\n\u001b[1;32m     29\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mlatency\u001b[39m\u001b[39m'\u001b[39m: latency,\n\u001b[1;32m     30\u001b[0m     }\n\u001b[0;32m---> 32\u001b[0m lm \u001b[39m=\u001b[39m LongestSubstringLM(\u001b[39m'\u001b[39m\u001b[39mlast\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m     33\u001b[0m n_sd \u001b[39m=\u001b[39m \u001b[39m0\u001b[39m\n\u001b[1;32m     34\u001b[0m measure_latency_per_sample(lm, data_c2k_l64[\u001b[39m1\u001b[39m], n_sd\u001b[39m=\u001b[39m\u001b[39m64\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'LongestSubstringLM' is not defined"]}], "source": ["def measure_latency_per_sample(lm, sample, n_sd):\n", "    context = sample['context'].tolist()\n", "    label = sample['label'].tolist()\n", "    lm.fit(context)\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        n_tokens_to_predict = min(n_sd, len(label) - index - 1)\n", "        if n_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            predictions, _ = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                n_tokens_to_predict)\n", "            for prediction_index in range(n_tokens_to_predict):\n", "                if predictions[prediction_index] == label[index]:\n", "                    index += 1\n", "                else:\n", "                    break\n", "        # Make prediction with the main model\n", "        index += 1\n", "        latency += LATENCY[n_tokens_to_predict + 1]\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "    }\n", "\n", "lm = LongestSubstringLM('last')\n", "n_sd = 0\n", "measure_latency_per_sample(lm, data_c2k_l64[1], n_sd=64)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'LongestSubstringLM' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 10\u001b[0m\n\u001b[1;32m      7\u001b[0m     \u001b[39mprint\u001b[39m(df\u001b[39m.\u001b[39mmean())\n\u001b[1;32m      8\u001b[0m     \u001b[39mreturn\u001b[39;00m df\n\u001b[0;32m---> 10\u001b[0m lm \u001b[39m=\u001b[39m LongestSubstringLM(\u001b[39m'\u001b[39m\u001b[39mlast\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m     11\u001b[0m \u001b[39mfor\u001b[39;00m n_sd \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39m0\u001b[39m, \u001b[39m64\u001b[39m, \u001b[39m4\u001b[39m):\n\u001b[1;32m     12\u001b[0m     _ \u001b[39m=\u001b[39m measure_latency_per_data(lm, data_c2k_l64, n_sd\u001b[39m=\u001b[39mn_sd)\n", "\u001b[0;31mNameError\u001b[0m: name 'LongestSubstringLM' is not defined"]}], "source": ["def measure_latency_per_data(lm, data, n_sd):\n", "    df = []\n", "    for d in data:\n", "        df.append(measure_latency_per_sample(lm=lm, sample=d, n_sd=n_sd))        \n", "    df = pd.DataFrame(df)\n", "    print('SPECULATIVE DECODING N=%d' % n_sd)\n", "    print(df.mean())\n", "    return df\n", "\n", "lm = LongestSubstringLM('last')\n", "for n_sd in range(0, 64, 4):\n", "    _ = measure_latency_per_data(lm, data_c2k_l64, n_sd=n_sd)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=1\n", "context_length    2048.00000\n", "n_rounds            44.60330\n", "latency           1105.73824\n", "dtype: float64\n", "SPECULATIVE DECODING N=2\n", "context_length    2048.000000\n", "n_rounds            38.714600\n", "latency            961.610095\n", "dtype: float64\n", "SPECULATIVE DECODING N=3\n", "context_length    2048.000000\n", "n_rounds            36.033700\n", "latency            896.749315\n", "dtype: float64\n", "SPECULATIVE DECODING N=4\n", "context_length    2048.000000\n", "n_rounds            34.578800\n", "latency            858.923305\n", "dtype: float64\n", "SPECULATIVE DECODING N=5\n", "context_length    2048.000000\n", "n_rounds            33.678200\n", "latency            835.004735\n", "dtype: float64\n", "SPECULATIVE DECODING N=6\n", "context_length    2048.0000\n", "n_rounds            33.1207\n", "latency            819.6669\n", "dtype: float64\n", "SPECULATIVE DECODING N=7\n", "context_length    2048.000000\n", "n_rounds            32.705100\n", "latency            807.945295\n", "dtype: float64\n", "SPECULATIVE DECODING N=8\n", "context_length    2048.000000\n", "n_rounds            32.463700\n", "latency            803.028185\n", "dtype: float64\n", "SPECULATIVE DECODING N=9\n", "context_length    2048.000000\n", "n_rounds            32.251500\n", "latency            798.831984\n", "dtype: float64\n", "SPECULATIVE DECODING N=10\n", "context_length    2048.000000\n", "n_rounds            32.100100\n", "latency            796.086506\n", "dtype: float64\n", "SPECULATIVE DECODING N=11\n", "context_length    2048.000000\n", "n_rounds            32.008400\n", "latency            794.807844\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "for n_sd in range(1, 12, 1):\n", "    _ = measure_latency_per_data(lm, data_c2k_l64, n_sd=n_sd)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from research.retrieval.types import Chunk, Document\n", "import json\n", "from typing import Any, List, Dict\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from research.retrieval import utils as rutils\n", "\n", "\n", "def prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix):\n", "    metadata = {\n", "        'num_prefix_chars_post_truncation': len(model_input['prefix']),\n", "        'num_suffix_chars_post_truncation': len(model_input['suffix']),\n", "    }\n", "    context = []\n", "    retrieved_chunks = model_input['retrieved_chunks']\n", "    if n_retrievals is not None:\n", "        retrieved_chunks = retrieved_chunks[:n_retrievals]\n", "    for chunk in reversed(retrieved_chunks):\n", "        context.extend(tokenizer.tokenize(chunk.text) + [tokenizer.eod_id])\n", "    if not remove_prefix_and_suffix:\n", "        context.extend(tokenizer.tokenize(model_input['suffix']))    \n", "        context.extend(tokenizer.tokenize(model_input['prefix']))\n", "    return context, metadata\n", "\n", "\n", "def generate_prompt(\n", "    prefix: str,\n", "    middle: str,\n", "    suffix: str,\n", "    middle_char_start: int,\n", "    middle_char_end: int,\n", "    file_path: str,\n", "    retrieved_chunk_str: str,\n", "    max_target_tokens: int,\n", "    n_retrievals,\n", "    remove_prefix_and_suffix=False,\n", ") -> List[int]:\n", "    \"\"\"Construct a token prompt.\"\"\"\n", "\n", "    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)\n", "\n", "    # Remove chunks that overlap with middle\n", "    filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(middle_char_start, middle_char_end),\n", "        retrieved_chunks,\n", "    )\n", "\n", "    model_input = dict(\n", "        prefix=prefix,\n", "        middle=middle,\n", "        suffix=suffix,\n", "        retrieved_chunks=filtered_chunks,\n", "        path=file_path,\n", "    )\n", "\n", "    # TODO(michiel) Add option for sampling different prompt styles\n", "    _, metadata = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    # Remove chunks that overlap with prefix or suffix\n", "    new_filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(\n", "            middle_char_start - metadata[\"num_prefix_chars_post_truncation\"],\n", "            middle_char_end,\n", "        ),\n", "        filtered_chunks,\n", "    )\n", "    if metadata[\"num_suffix_chars_post_truncation\"] > 0:\n", "        new_filtered_chunks = rutils.filter_overlap_chunks(\n", "            file_path,\n", "            rutils.Span(\n", "                middle_char_end,\n", "                middle_char_end + metadata[\"num_suffix_chars_post_truncation\"],\n", "            ),\n", "            new_filtered_chunks,\n", "        )\n", "\n", "    model_input['retrieved_chunks'] = new_filtered_chunks\n", "    prompt_tokens, _ = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    \n", "    target_tokens = tokenizer.tokenize(middle + \"<|endoftext|>\")    \n", "    target_tokens = target_tokens[:max_target_tokens]\n", "\n", "    return prompt_tokens, target_tokens"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n"]}], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for row_index, datum in df.iterrows():\n", "            n_read_samples += 1\n", "            context, label = generate_prompt(\n", "                prefix=datum['prefix'],\n", "                middle=datum['middle'],\n", "                suffix=datum['suffix'],\n", "                middle_char_start=datum['middle_char_start'],\n", "                middle_char_end=datum['middle_char_end'],\n", "                file_path=datum['file_path'],\n", "                retrieved_chunk_str=datum['retrieved_chunks'],\n", "                max_target_tokens=max_tokens_to_predict,\n", "                n_retrievals=n_retrievals,\n", "                remove_prefix_and_suffix=remove_prefix_and_suffix)        \n", "\n", "            context = np.array(context)\n", "            label = np.array(label)\n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'prefix_suffix_len': len(tokenizer.tokenize(datum['prefix'] + datum['suffix'])),\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "data_fim_wo_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, 0)\n", "data_fim_wo_prefix_and_suffix = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None, True)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=0\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            64.9870\n", "latency           1572.6854\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            29.3870\n", "latency            730.6488\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "df_fim_retrieval_nsd0 = measure_latency_per_data(lm, data_fim_retrieval, n_sd=0)\n", "df_fim_retrieval_nsd12 = measure_latency_per_data(lm, data_fim_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.1 0.9877551020408163\n", "0.5 1.956374768692032\n", "0.75 3.406256735018108\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams[\"figure.figsize\"] = (12, 5)\n", "\n", "(df_fim_retrieval_nsd0['latency'] / df_fim_retrieval_nsd12['latency']).hist(bins=100, cumulative=True, density=True)\n", "for k, v in (df_fim_retrieval_nsd0['latency'] / df_fim_retrieval_nsd12['latency']).quantile([0.10, 0.5, 0.75]).items():\n", "    print(k, v)\n", "    plt.vlines(v, 0, 1, color='red')\n", "    plt.text(v - 0.5, -0.1, 'q%.0f=%.1f' % (100 * k, v))\n", "plt.xlabel('Speed up from the speculative decoding')\n", "plt.ylabel('Cumulative fraction of samples')\n", "\n", "# _ = plt.xticks([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])\n", "_ = plt.yticks([0.1, 0.20, 0.30, 0.40, 0.50, 0.60, 0.70, 0.80, 0.90, 1.0])"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1000,\n", " 481,\n", " context_length    5696.494802\n", " label_length        62.241164\n", " n_rounds            62.241164\n", " latency           1506.236175\n", " dtype: float64,\n", " context_length    5696.494802\n", " label_length        62.241164\n", " n_rounds            24.981289\n", " latency            621.101403\n", " dtype: float64)"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check whether we get better speed ups on longer files\n", "# len(df_fim_retrieval[data_fim_retrieval['prefix_suffix_len'] >= 1000])\n", "rows = [datum['prefix_suffix_len'] >= 1000 for datum in data_fim_retrieval]\n", "len(data_fim_retrieval), len(df_fim_retrieval_nsd0[rows]), df_fim_retrieval_nsd0[rows].mean(), df_fim_retrieval_nsd12[rows].mean()"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=0\n", "context_length    1266.5470\n", "label_length        64.9870\n", "n_rounds            64.9870\n", "latency           1572.6854\n", "dtype: float64\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 4 tokens since the context length is only 2\n", "Cannot predict 11 tokens since the context length is only 6\n", "Cannot predict 10 tokens since the context length is only 7\n", "Cannot predict 9 tokens since the context length is only 8\n", "Cannot predict 9 tokens since the context length is only 2\n", "Cannot predict 8 tokens since the context length is only 3\n", "Cannot predict 7 tokens since the context length is only 4\n", "Cannot predict 6 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 7 tokens since the context length is only 3\n", "Cannot predict 6 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 11 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 9 tokens since the context length is only 4\n", "Cannot predict 8 tokens since the context length is only 5\n", "Cannot predict 7 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 11 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 7 tokens since the context length is only 3\n", "Cannot predict 6 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 11 tokens since the context length is only 7\n", "Cannot predict 10 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "SPECULATIVE DECODING N=12\n", "context_length    1266.547000\n", "label_length        64.987000\n", "n_rounds            37.850000\n", "latency            941.088425\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_fim_wo_retrieval, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_fim_wo_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=0\n", "context_length    3640.1970\n", "label_length        64.9870\n", "n_rounds            64.9870\n", "latency           1572.6854\n", "dtype: float64\n", "Cannot predict 12 tokens since the context length is only 0\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 0\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 11 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 0\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "Cannot predict 12 tokens since the context length is only 0\n", "Cannot predict 12 tokens since the context length is only 1\n", "Cannot predict 12 tokens since the context length is only 2\n", "Cannot predict 12 tokens since the context length is only 3\n", "Cannot predict 12 tokens since the context length is only 4\n", "Cannot predict 12 tokens since the context length is only 5\n", "Cannot predict 12 tokens since the context length is only 6\n", "Cannot predict 12 tokens since the context length is only 7\n", "Cannot predict 12 tokens since the context length is only 8\n", "Cannot predict 12 tokens since the context length is only 9\n", "Cannot predict 12 tokens since the context length is only 10\n", "Cannot predict 12 tokens since the context length is only 11\n", "SPECULATIVE DECODING N=12\n", "context_length    3640.197000\n", "label_length        64.987000\n", "n_rounds            36.080000\n", "latency            897.057062\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_fim_wo_prefix_and_suffix, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_fim_wo_prefix_and_suffix, n_sd=12)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=0\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            64.9870\n", "latency           1572.6854\n", "dtype: float64\n", "SPECULATIVE DECODING N=4\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            32.56500\n", "latency            808.87325\n", "dtype: float64\n", "SPECULATIVE DECODING N=8\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            30.0690\n", "latency            743.7699\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            29.3870\n", "latency            730.6488\n", "dtype: float64\n", "SPECULATIVE DECODING N=16\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.232000\n", "latency            735.843113\n", "dtype: float64\n", "SPECULATIVE DECODING N=20\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.297000\n", "latency            755.097513\n", "dtype: float64\n", "SPECULATIVE DECODING N=24\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.349000\n", "latency            753.761463\n", "dtype: float64\n", "SPECULATIVE DECODING N=28\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            29.48900\n", "latency            759.46545\n", "dtype: float64\n", "SPECULATIVE DECODING N=32\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.552000\n", "latency            763.536875\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "for n_sd in range(0, 32 + 1, 4):\n", "    _ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=n_sd)"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n"]}], "source": ["import glob\n", "MICHIEL_DENSE_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/pythonmed_diffboykin/part-?????-8ea8d527-8a12-429e-95a0-047e69912022-c000.zstd.parquet\"))\n", "\n", "data_fim_dense_retrieval = load_retrieval_data(MICHIEL_DENSE_RETRIEVAL_DATA_PATHS, 1000, 256, None)"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=0\n", "context_length    8854.618\n", "label_length        72.420\n", "n_rounds            72.420\n", "latency           1752.564\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    8854.618000\n", "label_length        72.420000\n", "n_rounds            31.937000\n", "latency            794.155687\n", "dtype: float64\n"]}], "source": ["lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_fim_dense_retrieval, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_fim_dense_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "SPECULATIVE DECODING N=0\n", "context_length    8222.089\n", "label_length        70.625\n", "n_rounds            70.625\n", "latency           1709.125\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    8222.08900\n", "label_length        70.62500\n", "n_rounds            27.74000\n", "latency            689.80225\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_JAVA_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/javasmall/part-?????-6e266275-46cb-43b2-8060-c01dd2b2ff9b-c000.zstd.parquet\"))\n", "\n", "data_java_fim_bm25_retrieval = load_retrieval_data(MICHIEL_JAVA_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_java_fim_bm25_retrieval, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_java_fim_bm25_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "SPECULATIVE DECODING N=0\n", "context_length    10086.5480\n", "label_length         72.7380\n", "n_rounds             72.7380\n", "latency            1760.2596\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    10086.548000\n", "label_length         72.738000\n", "n_rounds             29.534000\n", "latency             734.437625\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_CPP_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/cppsmall/part-?????-77e5f41a-3e30-42ef-96e9-182e8c666899-c000.zstd.parquet\"))\n", "\n", "data_cpp_fim_bm25_retrieval = load_retrieval_data(MICHIEL_CPP_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_cpp_fim_bm25_retrieval, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_cpp_fim_bm25_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "SPECULATIVE DECODING N=0\n", "context_length    8198.6020\n", "label_length        66.3070\n", "n_rounds            66.3070\n", "latency           1604.6294\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    8198.602000\n", "label_length        66.307000\n", "n_rounds            28.355000\n", "latency            705.039962\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_JS_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/javascriptsmall/part-?????-4d066039-757d-4808-992b-d4aab68f811f-c000.zstd.parquet\"))\n", "\n", "data_js_fim_bm25_retrieval = load_retrieval_data(MICHIEL_JS_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestSubstringLM('last')\n", "_ = measure_latency_per_data(lm, data_js_fim_bm25_retrieval, n_sd=0)\n", "_ = measure_latency_per_data(lm, data_js_fim_bm25_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_\n", "BEAM\n", "_\n", ".\n"]}], "source": ["# from https://github.com/smilli/kneser-ney/blob/master/kneser_ney.py\n", "\n", "import copy\n", "import math\n", "import numpy as np\n", "from collections import Counter, defaultdict\n", "\n", "\n", "def zipngram(tokens, n):\n", "    return list(zip(*[tokens[i:] for i in range(n)]))\n", "\n", "\n", "def compute_adjusted_ngram_counts(ngrams, order):\n", "    ngram_counts = [Counter(ngrams)]\n", "    for i in range(order - 1):\n", "        prev_ngram_counts = ngram_counts[-1]\n", "        new_ngram_counts = defaultdict(int)\n", "        for ngram in prev_ngram_counts.keys():\n", "            suffix = ngram[1:]\n", "            new_ngram_counts[suffix] += 1\n", "        ngram_counts.append(new_ngram_counts)\n", "    ngram_counts = [None] + list(reversed(ngram_counts))\n", "    return ngram_counts\n", "\n", "\n", "def compute_discounts(ngram_counts):\n", "    discounts = [[None, None, None, None]]\n", "    for n in range(1, len(ngram_counts)):\n", "        t_n = Counter(count for count in ngram_counts[n].values() if count <= 4)\n", "        current_discounts = [0]        \n", "        common = t_n[1] / (t_n[1] + 2 * t_n[2])\n", "        for k in range(1, 4):\n", "            if t_n[k] == 0:\n", "                d = 0\n", "            else:\n", "                d = k - (k + 1) * common * t_n[k + 1] / t_n[k]\n", "            current_discounts.append(d)  \n", "        discounts.append(current_discounts)\n", "    return discounts\n", "\n", "\n", "def get_discount(discounts, count):\n", "    if count > 3:\n", "        return discounts[3]\n", "    return discounts[count]\n", "\n", "\n", "def compute_pseudo_probs_and_backoff(ngram_counts, discounts):    \n", "    pseudo_probs, backoff = [None], [None]\n", "    # Estimate pseudo_probs and backoff for higher order ngrams\n", "    for n in range(1, len(ngram_counts)):\n", "        denominator = defaultdict(int)\n", "        backoff_term = [None, defaultdict(int), defaultdict(int), defaultdict(int)]\n", "        for ngram, count in ngram_counts[n].items():\n", "            prefix = ngram[:-1]\n", "            denominator[prefix] += count\n", "            \n", "            if count in {1, 2, 3}:\n", "                # shall we use raw count here?\n", "                backoff_term[count][prefix] += 1\n", "        \n", "        current_pseudo_probs, current_backoff = defaultdict(int), defaultdict(int)\n", "        for ngram, count in ngram_counts[n].items():\n", "            prefix = ngram[:-1]\n", "            current_pseudo_probs[ngram] = (count - get_discount(discounts[n], count)) / denominator[prefix]\n", "            for i in [1, 2, 3]:\n", "                current_backoff[prefix] += get_discount(discounts[n], i) * backoff_term[i][prefix]\n", "            current_backoff[prefix] /= denominator[prefix]\n", "        pseudo_probs.append(current_pseudo_probs)\n", "        backoff.append(current_backoff)\n", "    return pseudo_probs, backoff\n", "\n", "\n", "def compute_unigram_probs(unigram_pseudo_probs):\n", "    s = sum(v for v in unigram_pseudo_probs.values())\n", "    # We ignore backoff(empty) * 1 / vocab_size since we cannot generate OOV in our setup.\n", "    unigram_probs = {k: v / s for k, v in unigram_pseudo_probs.items()}\n", "    return unigram_probs\n", "\n", "def interpolate_probs(pseudo_probs, backoff, unigram_probs):\n", "    probs = [None, unigram_probs]\n", "    for n in range(2, len(pseudo_probs)):\n", "        current_probs = {}\n", "        for ngram, pseudo_prob in pseudo_probs[n].items():\n", "            prefix, suffix = ngram[:-1], ngram[1:]\n", "            current_probs[ngram] = pseudo_prob + backoff[n][prefix] * probs[n - 1][suffix]\n", "        probs.append(current_probs)\n", "    return probs\n", "\n", "class KneserNeyLM:\n", "\n", "    def __init__(self, order):\n", "        self.order = order\n", "\n", "    def fit(self, tokens):\n", "        ngrams = zipngram(tokens, self.order)\n", "        ngram_counts = compute_adjusted_ngram_counts(ngrams, self.order)\n", "        discounts = compute_discounts(ngram_counts)\n", "        pseudo_probs, backoff = compute_pseudo_probs_and_backoff(ngram_counts, discounts)\n", "        unigram_probs = compute_unigram_probs(pseudo_probs[1])\n", "        self.probs = interpolate_probs(pseudo_probs, backoff, unigram_probs)\n", "\n", "    def predict_next_token(self, suffix):\n", "        for n in reversed(range(1, self.order + 1)):\n", "            suffix_to_match = tuple(suffix[(len(suffix) - n + 1):])\n", "            if n == 1:\n", "                pos_ngrams = [(ngram, p) for ngram, p in self.probs[1].items()]\n", "            else:\n", "                pos_ngrams = [\n", "                    (ngram, p) for ngram, p in self.probs[n].items()\n", "                    if ngram[:-1] == suffix_to_match]\n", "            if len(pos_ngrams) > 0:\n", "                max_ngram, max_p = max(pos_ngrams, key=lambda x: x[1])\n", "                return max_ngram[-1], max_p\n", "        raise ValueError()   \n", "\n", "    def predict_next_tokens(self, suffix, num_tokens):\n", "        for n in reversed(range(1, self.order + 1)):\n", "            suffix_to_match = tuple(suffix[(len(suffix) - n + 1):])\n", "            if n == 1:\n", "                pos_ngrams = [(ngram, p) for ngram, p in self.probs[1].items()]\n", "            else:\n", "                pos_ngrams = [(ngram, p) for ngram, p in self.probs[n].items() if ngram[:-1] == suffix_to_match]\n", "            pos_ngrams = sorted(pos_ngrams, key=lambda x: -x[1])                \n", "            if len(pos_ngrams) > 0:\n", "                return [(ngram[-1], p) for ngram, p in pos_ngrams[:num_tokens]]\n", "        raise ValueError()              \n", "\n", "    def predict_next_k_tokens(self, suffix, k):\n", "        if not isinstance(suffix, list):\n", "            suffix = suffix.tolist()\n", "        predictions = []\n", "        for _ in range(k):\n", "            next_token, _ = self.predict_next_token(suffix + predictions)\n", "            predictions.append(next_token)\n", "        return predictions, {}\n", "    \n", "    def beam_search(self, suffix, k, beam_size):        \n", "        if not isinstance(suffix, list):\n", "            suffix = suffix.tolist()\n", "        last_k_tokens = self.order - 1\n", "        beam = [(suffix[(len(suffix) - last_k_tokens):], 1.0)]\n", "        for _ in range(k):\n", "            new_beam = []\n", "            for past_prediction, past_prob in beam:\n", "                for new_prediction, new_prob in self.predict_next_tokens(past_prediction, beam_size):\n", "                    new_beam.append((past_prediction + [new_prediction], past_prob * new_prob))\n", "            beam = sorted(new_beam, key=lambda x: -x[1])\n", "            # beam = beam[:beam_size]\n", "        return [prediction[last_k_tokens:] for prediction, p in beam][:beam_size]\n", "\n", "\n", "lm = KneserNeyLM(3)\n", "lm.fit(data_fim_retrieval[20]['context'])\n", "\n", "print(tokenizer.detokenize(lm.predict_next_k_tokens(tokenizer.tokenize('self.inputfolder'), 1)[0]))\n", "print('BEAM')\n", "for prediction in lm.beam_search(tokenizer.tokenize('self.inputfolder'), 1, 2):\n", "    print(tokenizer.de<PERSON><PERSON><PERSON>(prediction))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=12\n", "context_length    4906.744\n", "label_length        64.987\n", "n_rounds            42.105\n", "latency           1046.861\n", "dtype: float64\n", "SPECULATIVE DECODING N=12\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            32.58500\n", "latency            810.14495\n", "dtype: float64\n"]}], "source": ["_ = measure_latency_per_data(KneserNeyLM(1), data_fim_retrieval, n_sd=12)\n", "_ = measure_latency_per_data(KneserNeyLM(2), data_fim_retrieval, n_sd=12)\n", "_ = measure_latency_per_data(KneserNeyLM(3), data_fim_retrieval, n_sd=12)\n", "_ = measure_latency_per_data(KneserNeyLM(5), data_fim_retrieval, n_sd=12)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'KneserNeyLM' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 60\u001b[0m\n\u001b[1;32m     47\u001b[0m             latency \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m LATENCY[actual_parallel_sd \u001b[39m*\u001b[39m (actual_tokens_to_predict \u001b[39m+\u001b[39m \u001b[39m1\u001b[39m)]\n\u001b[1;32m     49\u001b[0m     \u001b[39mreturn\u001b[39;00m {\n\u001b[1;32m     50\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mcontext_length\u001b[39m\u001b[39m'\u001b[39m: \u001b[39mlen\u001b[39m(context),\n\u001b[1;32m     51\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mlabel_length\u001b[39m\u001b[39m'\u001b[39m: \u001b[39mlen\u001b[39m(label),\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     56\u001b[0m         \u001b[39m'\u001b[39m\u001b[39mn_unique_two_tokens\u001b[39m\u001b[39m'\u001b[39m: safe_mean(n_unique_two_tokens),\n\u001b[1;32m     57\u001b[0m     }\n\u001b[0;32m---> 60\u001b[0m lm \u001b[39m=\u001b[39m KneserNeyLM(\u001b[39m4\u001b[39m)\n\u001b[1;32m     61\u001b[0m \u001b[39mprint\u001b[39m(measure_latency_per_sample(lm, data_fim_retrieval[\u001b[39m1\u001b[39m], n_sd\u001b[39m=\u001b[39m\u001b[39m3\u001b[39m))\n\u001b[1;32m     62\u001b[0m \u001b[39mprint\u001b[39m(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[\u001b[39m1\u001b[39m], n_parallel_sd\u001b[39m=\u001b[39m\u001b[39m1\u001b[39m, n_sd\u001b[39m=\u001b[39m\u001b[39m3\u001b[39m))\n", "\u001b[0;31mNameError\u001b[0m: name 'KneserNeyLM' is not defined"]}], "source": ["def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "    \n", "def measure_latency_per_sample_parallel_sd(lm, sample, n_sd, n_parallel_sd):\n", "    context = sample['context'].tolist()\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    beam_sizes, n_unique_first_token, n_unique_two_tokens = [], [], []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        actual_tokens_to_predict = min(n_sd, len(label) - index - 1)\n", "        actual_parallel_sd = n_parallel_sd\n", "        if actual_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            beam_of_predictions = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                actual_tokens_to_predict,\n", "                beam_size=n_parallel_sd)\n", "            actual_parallel_sd = len(beam_of_predictions)\n", "\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                current_index = index\n", "                for prediction_index in range(actual_tokens_to_predict):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            index = furthest_index\n", "\n", "        # Make prediction with the main model\n", "        index += 1\n", "        if actual_parallel_sd == 0:\n", "            # no paralle SD is used at this step\n", "            latency += LATENCY[1]\n", "        else:\n", "            latency += LATENCY[actual_parallel_sd * (actual_tokens_to_predict + 1)]\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "\n", "lm = KneserNeyLM(4)\n", "print(measure_latency_per_sample(lm, data_fim_retrieval[1], n_sd=3))\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[1], n_parallel_sd=1, n_sd=3))\n", "\n", "print(measure_latency_per_sample(lm, data_fim_retrieval[1], n_sd=12))\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[1], n_parallel_sd=3, n_sd=4))\n", "\n", "\n", "def measure_latency_per_data_parallel_sd(lm, data, n_sd, n_parallel_sd):\n", "    df = []\n", "    for d in data:\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d, n_sd=n_sd, n_parallel_sd=n_parallel_sd))        \n", "    df = pd.DataFrame(df)\n", "    print('PARALLEL SPECULATIVE DECODING N=%d' % n_sd)\n", "    print(df.mean())\n", "    return df"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.607000\n", "latency            760.966862\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.593000\n", "latency            762.196450\n", "beam_size            1.847846\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.857000\n", "latency            769.975325\n", "beam_size            2.505494\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=3\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.698000\n", "latency            792.174150\n", "beam_size            2.959758\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=2\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            33.92400\n", "latency            865.96620\n", "beam_size            3.59238\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=1\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            39.932000\n", "latency           1033.569950\n", "beam_size            4.461477\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=12)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4, 6, 12]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=12 // n_parallel_sd, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=4\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            33.4510\n", "latency            830.8726\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            33.4510\n", "latency            830.8726\n", "beam_size            1.0000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.826000\n", "latency            788.542475\n", "beam_size            1.794508\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.857000\n", "latency            769.975325\n", "beam_size            2.505494\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.240000\n", "latency            786.934150\n", "beam_size            3.144395\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=4)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=4, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=6\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            31.92500\n", "latency            790.04275\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            31.92500\n", "latency            790.04275\n", "beam_size            1.00000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.593000\n", "latency            762.196450\n", "beam_size            1.847846\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.734000\n", "latency            771.618888\n", "beam_size            2.639172\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.112000\n", "latency            754.864850\n", "beam_size            3.376967\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=6\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.611000\n", "latency            748.655872\n", "beam_size            4.069700\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=6)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4, 5]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=6, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.189000\n", "latency            771.466588\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.189000\n", "latency            771.466588\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.251000\n", "latency            770.425475\n", "beam_size            1.875103\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.475000\n", "latency            762.918912\n", "beam_size            2.701493\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.847000\n", "latency            755.401987\n", "beam_size            3.489454\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.357000\n", "latency            753.898134\n", "beam_size            4.235623\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=8)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4, 5]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=8, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.825000\n", "latency            764.448437\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.825000\n", "latency            764.448437\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.177000\n", "latency            780.445550\n", "beam_size            1.887553\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.479000\n", "latency            767.453622\n", "beam_size            2.735481\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.874000\n", "latency            765.557962\n", "beam_size            3.549980\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=10\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.390000\n", "latency            766.372612\n", "beam_size            4.331739\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=10)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4, 5]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=10, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.607000\n", "latency            760.966862\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.607000\n", "latency            760.966862\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.200000\n", "latency            779.778900\n", "beam_size            1.896853\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.581000\n", "latency            777.180972\n", "beam_size            2.759604\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.039000\n", "latency            779.212837\n", "beam_size            3.591286\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.582000\n", "latency            782.911294\n", "beam_size            4.388972\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "_ = measure_latency_per_data(lm, data_fim_retrieval, n_sd=12)\n", "\n", "for n_parallel_sd in [1, 2, 3, 4, 5]:\n", "    _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=12, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.7440\n", "label_length        64.9870\n", "n_rounds            33.4510\n", "latency            830.8726\n", "beam_size            1.0000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.775000\n", "latency            787.279575\n", "beam_size            1.794958\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.749000\n", "latency            767.279250\n", "beam_size            2.504979\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.74400\n", "label_length        64.98700\n", "n_rounds            30.14000\n", "latency            784.31760\n", "beam_size            3.14339\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=4\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.708000\n", "latency            768.774563\n", "beam_size            3.731242\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            31.189000\n", "latency            771.466588\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.065000\n", "latency            765.679350\n", "beam_size            1.874728\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.138000\n", "latency            754.172237\n", "beam_size            2.701459\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.507000\n", "latency            746.466925\n", "beam_size            3.485540\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=8\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            28.056000\n", "latency            745.843887\n", "beam_size            4.230764\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            30.607000\n", "latency            760.966862\n", "beam_size            1.000000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.875000\n", "latency            771.361525\n", "beam_size            1.896333\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING N=12\n", "context_length    4906.744000\n", "label_length        64.987000\n", "n_rounds            29.032000\n", "latency            762.695022\n", "beam_size            2.756628\n", "dtype: float64\n"]}], "source": ["lm = KneserNeyLM(4)\n", "\n", "for n_sd in [4, 8, 12]:\n", "    for n_parallel_sd in [1, 2, 3, 4, 5]:\n", "        _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=n_sd, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
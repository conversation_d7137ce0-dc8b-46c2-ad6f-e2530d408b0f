import pytest

from experimental.yury.agent.common import (
    merge_current_exchange_into_chat_history,
    extract_node_from_request_message,
    extract_node_from_response_message,
    extract_agent_rounds,
    MultipleToolUsesFound,
)

from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatRequestToolR<PERSON>ult,
    ChatResultNodeType,
    ChatResultTool<PERSON><PERSON>,
    ChatRequestText,
    ChatRequestNode,
    ChatResultNode,
)
import services.chat_host.chat_pb2 as chat_pb2


def test_merge_current_exchange_into_chat_history_empty():
    """Test merging an exchange into an empty chat history."""
    request_id = "req-123"
    chat_request = chat_pb2.ChatRequest(message="Hello")
    chat_response = chat_pb2.ChatResponse()

    # Add a RAW_RESPONSE node
    response_node = chat_pb2.ChatResultNode(
        id=1,
        type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
        content="Hello, I'm an AI assistant.",
    )
    chat_response.nodes.append(response_node)

    chat_history = merge_current_exchange_into_chat_history(
        request_id, chat_request, chat_response
    )

    assert len(chat_history) == 1
    assert chat_history[0].request_id == request_id
    assert chat_history[0].request_message == "Hello"
    assert len(chat_history[0].response_nodes) == 1
    assert chat_history[0].response_nodes[0].type == ChatResultNodeType.RAW_RESPONSE
    assert chat_history[0].response_nodes[0].content == "Hello, I'm an AI assistant."


def test_merge_current_exchange_into_chat_history_existing():
    """Test merging an exchange into an existing chat history."""
    # Create existing chat history
    existing_exchange = chat_pb2.Exchange(
        request_id="req-456",
        request_message="How are you?",
        response_text="I'm doing well, thanks for asking!",
    )

    # Create new exchange
    request_id = "req-789"
    chat_request = chat_pb2.ChatRequest(message="Tell me a joke")
    chat_request.chat_history.append(existing_exchange)

    chat_response = chat_pb2.ChatResponse()
    response_node = chat_pb2.ChatResultNode(
        id=1,
        type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
        content="Why did the chicken cross the road?",
    )
    chat_response.nodes.append(response_node)

    chat_history = merge_current_exchange_into_chat_history(
        request_id, chat_request, chat_response
    )

    assert len(chat_history) == 2
    assert chat_history[0].request_id == "req-456"
    assert chat_history[0].request_message == "How are you?"
    assert chat_history[1].request_id == "req-789"
    assert chat_history[1].request_message == "Tell me a joke"
    assert (
        chat_history[1].response_nodes[0].content
        == "Why did the chicken cross the road?"
    )


def test_merge_current_exchange_filters_non_content_nodes():
    """Test that merge_current_exchange_into_chat_history filters out non-content nodes."""
    request_id = "req-123"
    chat_request = chat_pb2.ChatRequest(message="Hello")
    chat_response = chat_pb2.ChatResponse()

    # Add a RAW_RESPONSE node (should be included)
    chat_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="Hello, I'm an AI assistant.",
        )
    )

    # Add a TOOL_USE node (should be included)
    tool_use = chat_pb2.ChatResultToolUse(
        tool_use_id="tool-123",
        tool_name="calculator",
        input_json='{"expression": "2+2"}',
    )
    chat_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=2, type=chat_pb2.ChatResultNodeType.TOOL_USE, tool_use=tool_use
        )
    )

    # Add a MAIN_TEXT_FINISHED node (should be excluded)
    chat_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=3, type=chat_pb2.ChatResultNodeType.MAIN_TEXT_FINISHED, content=""
        )
    )

    # Add a WORKSPACE_FILE_CHUNKS node (should be excluded)
    chat_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=4,
            type=chat_pb2.ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
            content="file chunks",
        )
    )

    chat_history = merge_current_exchange_into_chat_history(
        request_id, chat_request, chat_response
    )

    assert len(chat_history) == 1
    assert len(chat_history[0].response_nodes) == 2
    assert chat_history[0].response_nodes[0].type == ChatResultNodeType.RAW_RESPONSE
    assert chat_history[0].response_nodes[1].type == ChatResultNodeType.TOOL_USE


def test_extract_node_from_request_message_string():
    """Test extracting a node from a string request message."""
    request_message = "Hello, world!"
    node = extract_node_from_request_message(request_message)

    assert isinstance(node, ChatRequestText)
    assert node.content == "Hello, world!"


def test_extract_node_from_request_message_text_node():
    """Test extracting a node from a request message with a text node."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello, world!"),
        tool_result_node=None,
        image_node=None,
    )
    request_message = [text_node]

    node = extract_node_from_request_message(request_message)

    assert isinstance(node, ChatRequestText)
    assert node.content == "Hello, world!"


def test_extract_node_from_request_message_tool_result_node():
    """Test extracting a node from a request message with a tool result node."""
    tool_result = ChatRequestToolResult(
        tool_use_id="tool-123",
        content="Tool result content",
        is_error=False,
        request_id="req-456",
    )
    tool_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=tool_result,
        image_node=None,
    )
    request_message = [tool_node]

    node = extract_node_from_request_message(request_message)

    assert isinstance(node, ChatRequestToolResult)
    assert node.tool_use_id == "tool-123"
    assert node.content == "Tool result content"
    assert node.is_error is False
    assert node.request_id == "req-456"


def test_extract_node_from_request_message_mixed_nodes():
    """Test that an error is raised when both text and tool result nodes are present."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello, world!"),
        tool_result_node=None,
        image_node=None,
    )
    tool_result = ChatRequestToolResult(
        tool_use_id="tool-123",
        content="Tool result content",
        is_error=False,
        request_id="req-456",
    )
    tool_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=tool_result,
        image_node=None,
    )
    request_message = [text_node, tool_node]

    with pytest.raises(
        ValueError, match="Found both tool result and text in request nodes"
    ):
        extract_node_from_request_message(request_message)


def test_extract_node_from_request_message_empty_nodes():
    """Test that an error is raised when no text or tool result nodes are present."""
    request_message = []

    with pytest.raises(
        ValueError, match="Found neither tool result nor text in request nodes"
    ):
        extract_node_from_request_message(request_message)


def test_extract_node_from_response_message_string():
    """Test extracting a node from a string response message."""
    response_message = "Hello, I'm an AI assistant."
    content, tool_use = extract_node_from_response_message(response_message)

    assert content == "Hello, I'm an AI assistant."
    assert tool_use is None


def test_extract_node_from_response_message_raw_response():
    """Test extracting a node from a response message with a raw response node."""
    raw_response_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="Hello, I'm an AI assistant.",
    )
    response_message = [raw_response_node]

    content, tool_use = extract_node_from_response_message(response_message)

    assert content == "Hello, I'm an AI assistant."
    assert tool_use is None


def test_extract_node_from_response_message_tool_use():
    """Test extracting a node from a response message with a tool use node."""
    tool_use_data = ChatResultToolUse(
        tool_use_id="tool-123", name="calculator", input={"expression": "2+2"}
    )
    tool_use_node = ChatResultNode(
        id=2, type=ChatResultNodeType.TOOL_USE, content="", tool_use=tool_use_data
    )
    response_message = [tool_use_node]

    content, tool_use = extract_node_from_response_message(response_message)

    assert content == ""
    assert tool_use is not None
    assert tool_use.tool_use_id == "tool-123"
    assert tool_use.name == "calculator"
    assert tool_use.input == {"expression": "2+2"}


def test_extract_node_from_response_message_mixed():
    """Test extracting nodes from a response message with both raw response and tool use."""
    raw_response_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="To calculate 2+2, I'll use the calculator tool.",
    )
    tool_use_data = ChatResultToolUse(
        tool_use_id="tool-123", name="calculator", input={"expression": "2+2"}
    )
    tool_use_node = ChatResultNode(
        id=2, type=ChatResultNodeType.TOOL_USE, content="", tool_use=tool_use_data
    )
    response_message = [raw_response_node, tool_use_node]

    content, tool_use = extract_node_from_response_message(response_message)

    assert content == "To calculate 2+2, I'll use the calculator tool."
    assert tool_use is not None
    assert tool_use.tool_use_id == "tool-123"


def test_extract_node_from_response_message_multiple_tool_uses():
    """Test that an error is raised when multiple tool uses are present."""
    tool_use_data1 = ChatResultToolUse(
        tool_use_id="tool-123", name="calculator", input={"expression": "2+2"}
    )
    tool_use_node1 = ChatResultNode(
        id=1, type=ChatResultNodeType.TOOL_USE, content="", tool_use=tool_use_data1
    )
    tool_use_data2 = ChatResultToolUse(
        tool_use_id="tool-456", name="weather", input={"location": "San Francisco"}
    )
    tool_use_node2 = ChatResultNode(
        id=2, type=ChatResultNodeType.TOOL_USE, content="", tool_use=tool_use_data2
    )
    response_message = [tool_use_node1, tool_use_node2]

    with pytest.raises(MultipleToolUsesFound):
        extract_node_from_response_message(response_message)


def test_extract_agent_rounds_simple():
    """Test extracting agent rounds from a simple conversation."""
    # Create a simple conversation with one user message and one agent response
    request_id = "req-123"
    chat_request = chat_pb2.ChatRequest(message="Hello")
    chat_response = chat_pb2.ChatResponse()

    # Add a RAW_RESPONSE node
    response_node = chat_pb2.ChatResultNode(
        id=1,
        type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
        content="Hello, I'm an AI assistant.",
    )
    chat_response.nodes.append(response_node)

    agent_rounds, metadata = extract_agent_rounds(
        request_id, chat_request, chat_response, verbose=False
    )

    assert len(agent_rounds) == 1
    assert agent_rounds[0].user_message == "Hello"
    assert agent_rounds[0].user_message_request_id == request_id
    assert len(agent_rounds[0].agent_turns) == 1
    assert agent_rounds[0].agent_turns[0].message == "Hello, I'm an AI assistant."
    assert agent_rounds[0].agent_turns[0].tool_use is None

    assert metadata["n_completed_rounds"] == 1
    assert metadata["n_completed_turns"] == 1


def test_extract_agent_rounds_with_tool_use():
    """Test extracting agent rounds with tool use."""
    # Create a conversation with tool use

    # First exchange: User message -> Agent response with tool use
    user_request_id = "req-user-1"
    user_request = chat_pb2.ChatRequest(message="What is 2+2?")

    agent_response = chat_pb2.ChatResponse()
    agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="I'll calculate that for you.",
        )
    )

    tool_use = chat_pb2.ChatResultToolUse(
        tool_use_id="tool-123",
        tool_name="calculator",
        input_json='{"expression": "2+2"}',
    )
    agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=2, type=chat_pb2.ChatResultNodeType.TOOL_USE, tool_use=tool_use
        )
    )

    # Second exchange: Tool result -> Agent final response
    tool_result_request_id = "req-tool-1"
    tool_result_request = chat_pb2.ChatRequest()
    tool_result_node = chat_pb2.ChatRequestNode(
        id=1,
        type=chat_pb2.ChatRequestNodeType.TOOL_RESULT,
        tool_result_node=chat_pb2.ChatRequestToolResult(
            tool_use_id="tool-123",
            content="4",
            is_error=False,
            request_id=tool_result_request_id,
        ),
    )
    tool_result_request.nodes.append(tool_result_node)

    # Add the first exchange to the chat history
    first_exchange = chat_pb2.Exchange(
        request_id=user_request_id,
        request_message=user_request.message,
        response_nodes=[node for node in agent_response.nodes],
    )
    tool_result_request.chat_history.append(first_exchange)

    # Create the final agent response
    final_response = chat_pb2.ChatResponse()
    final_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="The result of 2+2 is 4.",
        )
    )

    # Extract agent rounds
    agent_rounds, metadata = extract_agent_rounds(
        tool_result_request_id, tool_result_request, final_response, verbose=False
    )

    # Verify the results
    assert len(agent_rounds) == 1
    assert agent_rounds[0].user_message == "What is 2+2?"
    assert agent_rounds[0].user_message_request_id == user_request_id

    assert len(agent_rounds[0].agent_turns) == 2

    # First turn (with tool use)
    assert agent_rounds[0].agent_turns[0].message == "I'll calculate that for you."
    assert agent_rounds[0].agent_turns[0].tool_use is not None
    assert agent_rounds[0].agent_turns[0].tool_use_request_id == user_request_id

    # Second turn (final response after tool result)
    assert agent_rounds[0].agent_turns[1].message == "The result of 2+2 is 4."
    assert agent_rounds[0].agent_turns[1].tool_use is None

    # In the current implementation, the tool_response is associated with the previous turn
    # that made the tool call, not with the turn that follows the tool response
    assert agent_rounds[0].agent_turns[0].tool_response is not None
    assert agent_rounds[0].agent_turns[0].tool_response.content == "4"
    assert (
        agent_rounds[0].agent_turns[0].tool_response_request_id
        == tool_result_request_id
    )

    assert metadata["n_completed_rounds"] == 1
    assert metadata["n_completed_turns"] == 2


def test_extract_agent_rounds_multiple_rounds():
    """Test extracting multiple agent rounds."""
    # Create a conversation with multiple rounds

    # First round
    first_user_id = "req-user-1"
    first_user_request = chat_pb2.ChatRequest(message="Hello")

    first_agent_response = chat_pb2.ChatResponse()
    first_agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="Hello, how can I help you?",
        )
    )

    first_exchange = chat_pb2.Exchange(
        request_id=first_user_id,
        request_message=first_user_request.message,
        response_nodes=[node for node in first_agent_response.nodes],
    )

    # Second round
    second_user_id = "req-user-2"
    second_user_request = chat_pb2.ChatRequest(message="What's the weather?")
    second_user_request.chat_history.append(first_exchange)

    second_agent_response = chat_pb2.ChatResponse()
    second_agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="I don't have access to real-time weather information.",
        )
    )

    # Extract agent rounds
    agent_rounds, metadata = extract_agent_rounds(
        second_user_id, second_user_request, second_agent_response, verbose=False
    )

    # Verify the results
    assert len(agent_rounds) == 2

    # First round
    assert agent_rounds[0].user_message == "Hello"
    assert agent_rounds[0].user_message_request_id == first_user_id
    assert len(agent_rounds[0].agent_turns) == 1
    assert agent_rounds[0].agent_turns[0].message == "Hello, how can I help you?"

    # Second round
    assert agent_rounds[1].user_message == "What's the weather?"
    assert agent_rounds[1].user_message_request_id == second_user_id
    assert len(agent_rounds[1].agent_turns) == 1
    assert (
        agent_rounds[1].agent_turns[0].message
        == "I don't have access to real-time weather information."
    )

    assert metadata["n_completed_rounds"] == 2
    assert metadata["n_completed_turns"] == 2


def test_extract_agent_rounds_incomplete_round():
    """Test extracting agent rounds with an incomplete round."""
    # Create a conversation with an incomplete round (tool use without response)

    # First exchange: User message -> Agent response with tool use
    user_request_id = "req-user-1"
    user_request = chat_pb2.ChatRequest(message="What is 2+2?")

    agent_response = chat_pb2.ChatResponse()
    agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="I'll calculate that for you.",
        )
    )

    tool_use = chat_pb2.ChatResultToolUse(
        tool_use_id="tool-123",
        tool_name="calculator",
        input_json='{"expression": "2+2"}',
    )
    agent_response.nodes.append(
        chat_pb2.ChatResultNode(
            id=2, type=chat_pb2.ChatResultNodeType.TOOL_USE, tool_use=tool_use
        )
    )

    # Extract agent rounds (without the tool result and final response)
    agent_rounds, metadata = extract_agent_rounds(
        user_request_id, user_request, agent_response, verbose=False
    )

    # Verify the results
    assert len(agent_rounds) == 0  # No completed rounds
    assert metadata["skip_incomplete_round"] == 1

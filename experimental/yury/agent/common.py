from collections import defaultdict
from dataclasses import dataclass
from services.chat_host.chat_proto_util import convert_history

from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNodeType,
    ChatResultToolUse,
    ChatRequestText,
    Exchange,
    RequestMessage,
    ResponseMessage,
)
import services.chat_host.chat_pb2 as chat_pb2


@dataclass
class AgentTurn:
    message: str
    tool_use: ChatResultToolUse | None
    tool_use_request_id: str | None
    tool_response: ChatRequestToolResult | None
    tool_response_request_id: str | None


@dataclass
class AgentRound:
    """
    One round in a conversation between user and agent.

    A round starts with a user message followed by a sequence of agent turns.
    The round ends when the agent has completed its task(meaning its last turn does not have a tool call)
    """

    user_message: str
    user_message_request_id: str
    agent_turns: list[AgentTurn]


@dataclass
class AgentConversation:
    conversation_id: str
    agent_rounds: list[AgentRound]


def merge_current_exchange_into_chat_history(
    request_id, chat_request, chat_response
) -> list[chat_pb2.Exchange]:
    # Initialize chat_history
    if hasattr(chat_request, "chat_history"):
        chat_history = list(chat_request.chat_history)
    else:
        chat_history = []

    # Only include response nodes that contain actual content from the model:
    # - RAW_RESPONSE: The model's text output
    # - TOOL_USE: Tool calls made by the model
    #
    # Exclude nodes that are purely for client-server communication:
    # - TOOL_USE_START
    # - MAIN_TEXT_FINISHED
    # - WORKSPACE_FILE_CHUNKS
    # - RELEVANT_SOURCES
    response_nodes = [
        node
        for node in chat_response.nodes
        if node.type in [ChatResultNodeType.RAW_RESPONSE, ChatResultNodeType.TOOL_USE]
    ]

    current_exchange = chat_pb2.Exchange(
        request_id=request_id,
        request_message=chat_request.message,
        request_nodes=list(chat_request.nodes),
        response_nodes=response_nodes,
    )
    chat_history.append(current_exchange)
    return chat_history


def extract_node_from_request_message(
    request_message: RequestMessage,
) -> ChatRequestText | ChatRequestToolResult:
    """Extract either text or tool result node from the request message."""

    if isinstance(request_message, str):
        return ChatRequestText(content=request_message)

    tool_result_node, text_node_content = None, ""
    for node in request_message:
        if node.type == ChatRequestNodeType.TOOL_RESULT:
            assert tool_result_node is None, request_message
            tool_result_node = node.tool_result_node
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            text_node_content += node.text_node.content

    if len(text_node_content) > 0 and tool_result_node is not None:
        raise ValueError("Found both tool result and text in request nodes")
    elif len(text_node_content) > 0:
        return ChatRequestText(content=text_node_content)
    elif tool_result_node is not None:
        return tool_result_node
    else:
        raise ValueError("Found neither tool result nor text in request nodes")


class MultipleToolUsesFound(Exception):
    def __init__(self, response_message: ResponseMessage):
        self.message = f"Multiple tool uses in response nodes: {response_message}"
        super().__init__(self.message)


def extract_node_from_response_message(
    response_message: ResponseMessage,
) -> tuple[str, ChatResultToolUse | None]:
    """Extract the first node from the response message."""

    if isinstance(response_message, str):
        return response_message, None
    else:
        raw_response_node_content, tool_use_node = "", None
        for node in response_message:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                raw_response_node_content += node.content
            if node.type == ChatResultNodeType.TOOL_USE:
                if tool_use_node is not None:
                    raise MultipleToolUsesFound(response_message)
                assert tool_use_node is None, response_message
                tool_use_node = node.tool_use
        return (
            raw_response_node_content,
            tool_use_node,
        )


def extract_agent_rounds(
    request_id: str,
    chat_request: chat_pb2.ChatRequest,
    chat_response: chat_pb2.ChatResponse,
    verbose: bool,
) -> tuple[list[AgentRound], dict[str, int]]:
    """Extract agent rounds from the chat request and response."""

    # TODO(yury): we cannot quite rely on the .chat_history field,
    # as it maybe have been truncated. A proper pipeline would just go backward,
    # and recover each request individually.
    chat_history = convert_history(
        merge_current_exchange_into_chat_history(
            request_id,
            chat_request,
            chat_response,
        )
    )

    metadata = defaultdict(int)
    agent_rounds = []
    current_agent_round = None

    for index, exchange in enumerate(chat_history):
        assert exchange.request_id is not None
        try:
            request_node = extract_node_from_request_message(exchange.request_message)
            response_text, response_tool_use = extract_node_from_response_message(
                exchange.response_text
            )
        except MultipleToolUsesFound as e:
            if verbose:
                print(f"Failed to extract node from request message: {e}")

            # We skip all agent turns starting from the current one,
            # as well as all agent turns in the current round.
            metadata["skip_multiple_tool_uses"] += len(chat_history) - index
            if current_agent_round is not None:
                metadata["skip_multiple_tool_uses"] += len(
                    current_agent_round.agent_turns
                )
            break

        if isinstance(request_node, ChatRequestText):
            # If we have a user message, we are starting a new agent round
            current_agent_round = AgentRound(
                user_message=request_node.content,
                user_message_request_id=exchange.request_id,
                agent_turns=[],
            )
        else:
            # Otherwise, we are in the middle of an agent round.
            # This exchange contains a tool response to the tool call
            # made by the model in the previous exchange
            if current_agent_round is None:
                # We are ignoring cases like this for now.
                metadata["skip_no_user_message"] += 1
                continue
            assert current_agent_round is not None, (request_id, exchange.request_id)
            current_agent_round.agent_turns[-1].tool_response = request_node
            current_agent_round.agent_turns[
                -1
            ].tool_response_request_id = exchange.request_id

        current_agent_turn = AgentTurn(
            message=response_text,
            tool_use=response_tool_use,
            tool_use_request_id=exchange.request_id,
            tool_response=None,
            tool_response_request_id=None,
        )
        current_agent_round.agent_turns.append(current_agent_turn)

        if response_tool_use is None:
            # If the agent didn't make a tool call, this round is complete.
            # We add the current round to our collection and reset for the next round.
            agent_rounds.append(current_agent_round)
            metadata["n_completed_rounds"] += 1
            metadata["n_completed_turns"] += len(current_agent_round.agent_turns)
            current_agent_round = None

    if current_agent_round is not None:
        if verbose:
            print(
                "We have tool call terminated in the middle -- "
                f"{current_agent_round.agent_turns[-1].tool_use_request_id}"
            )
        metadata["skip_incomplete_round"] += 1

    return agent_rounds, metadata

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Last 31 days: 8200 requests\n"]}], "source": ["import datetime\n", "\n", "from base.datasets.tenants import VANGUARD, VANGUARD_I1\n", "from experimental.vpas.agent.analytics.big_query_utils import (\n", "    get_agent_conv_last_request_ids,\n", ")\n", "\n", "TENANT = VANGUARD_I1\n", "LAST_N_DAYS = 31\n", "# LIMIT = 100\n", "LIMIT = None\n", "\n", "now = datetime.datetime.now(datetime.timezone.utc)\n", "from_date = now - datetime.timedelta(days=LAST_N_DAYS)\n", "\n", "request_ids = get_agent_conv_last_request_ids(\n", "    TENANT, from_datetime=from_date, to_datetime=now\n", ")\n", "if LIMIT:\n", "    request_ids = request_ids[:LIMIT]\n", "print(f\"Last {LAST_N_DAYS} days: {len(request_ids)} requests\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:44,156 - base.datasets.gcs_client - INFO - Fetching 8200 requests\n", "2025-04-08 19:23:44,564 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c08c6cb0-04b7-43fe-95e3-436df53d2f44. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 03eb237a-cef3-4123-a110-4964fa815728\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:45,814 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8e5371ef-f15b-43a3-b40e-180cfa06592f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 3b72a1a0-2765-4fcd-8e22-68246e5916a0\n", "Multiple chat requests for 3f272bf2-8dcc-45d0-baef-95ed25ad0017\n", "Multiple chat requests for 40424f2e-7728-4e41-a59e-d9c5d82e77f6\n", "Multiple chat requests for bf29cb54-bcd5-450c-9dba-dfce937d55c8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:47,013 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8c87be82-3757-4cf5-a472-b3a9278d620d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:47,026 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: aff43774-fc76-42b6-97d4-852cdc311b0d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 836fbfa6-d65e-4a2b-b95f-cf405be4ac69\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:47,747 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 56900cf1-18c2-439f-9f59-4c701c4bed07. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:47,750 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e92b055b-d002-4684-a2c0-b6133938b0ae. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c2c5c249-977c-4842-903a-08e86c4dda24\n", "Multiple chat requests for 3e21abef-f55b-47bd-bffe-67c3d172ac63\n", "Multiple chat requests for 840bcdb1-ff68-4130-bd14-9920daff80a0\n", "Multiple chat requests for baa35b70-4e7d-4abe-bef3-9e318b965049\n", "Multiple chat requests for 07696536-4574-4304-abc3-23fb66a876c3\n", "Multiple chat requests for 66ab1d02-5343-40c3-8729-c69516803e6c\n", "Multiple chat requests for 99cb3ef3-9bc4-40ff-a64b-53da3d0500fd\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:49,042 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: de123283-ad01-43ac-9fd2-2952b009edf2. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c660a150-2658-49cd-b14f-054c3bea8ec2\n", "Multiple chat requests for 22ed539d-98b2-40ea-8d9e-a959dc8d5d97\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:49,432 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a6beaf1f-2576-46af-94d9-828e423126ba. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:49,448 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: cbdc80bf-4fd8-43e5-ad52-aa3d6915324f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 6c3bd97f-ff20-4224-9b56-661adeb772b3\n", "Multiple chat requests for 070b49f0-b77f-4ab9-b43b-f8f02013a506\n", "Multiple chat requests for 0da9968b-7275-4735-9481-006096a1fe49\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:50,135 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0770cebc-e405-4bef-85c3-ae678554154c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 9604c0c4-3f60-4594-889d-86c865eddd13\n", "Multiple chat requests for ee6eb7fb-a049-4039-8da4-eb16ad34e0f7\n", "Multiple chat requests for d70639be-99ff-4642-b7c9-2fca4b65eaab\n", "Multiple chat requests for 5138d53e-c188-48c3-a840-409cbedaa6bb\n", "Multiple chat requests for 219ac2dc-3ae9-4bb8-9ba5-7a666264ddbb\n", "Multiple chat requests for 16546f52-6817-4d15-b8b8-ecd2a1fc3ee1\n", "Multiple chat requests for 45689c38-803e-4524-988d-e66ad7a33acb\n", "Multiple chat requests for 18db82fe-7b3c-4f0b-bd7c-f3a225e84d8c\n", "Multiple chat requests for 5b5dd89d-2b6f-4858-b285-02283b404cf2\n", "Multiple chat requests for bb00d891-f821-4508-a5df-e583b9533970\n", "Multiple chat requests for 277ac561-7b29-43ac-95b2-87dd67c919b0\n", "Multiple chat requests for 2c784a91-cf7a-4952-8f09-be0d1fe2b5d8\n", "Multiple chat requests for 237b1a57-0d17-4908-97ed-01d3c68aa18a\n", "Multiple chat requests for 8716f0e8-8faa-43a1-b138-d79e1d4dfc8e\n", "Multiple chat requests for 95c73515-a739-4940-b5a9-dd8dd19da93e\n", "Multiple chat requests for 7a150f4d-d499-495e-b1ba-f35b3bd64606\n", "Multiple chat requests for 18cecf74-d096-49ee-a9ef-b7979dff91d2\n", "Multiple chat requests for 3b1564f8-177c-4829-b9f3-de484acc1535\n", "Multiple chat requests for eadbf15f-e8dd-4b7c-8204-9c63856fe84e\n", "Multiple chat requests for bcdbb347-ba5b-48dc-8e07-c4add30aa4e2\n", "Multiple chat requests for f91b71d6-e8cf-43a4-91c1-52ce1cbf5eab\n", "Multiple chat requests for 22842e4f-f95f-4d0e-ad1d-8dd5ad070ae3\n", "Multiple chat requests for 9549577b-8c6d-4147-a88c-926ac6e68ee0\n", "Multiple chat requests for 3e9aecb9-f6a7-4d99-ae0d-51f528a5fbf4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:51,915 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c83f872e-8d53-4e5f-bd51-a6961b37dabc. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:52,065 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 119255c7-692a-4e10-92c2-1cf92ed6b7c1. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for f11788a0-442b-41c7-a0e3-daa4de9e9a83\n", "Multiple chat requests for 0fc4142a-4545-434e-95cc-cd54acd1e87b\n", "Multiple chat requests for 66dde76b-9412-49ee-a43b-2f63eda9bde8\n", "Multiple chat requests for ea0862dd-e573-4fa7-9e0a-1ced10b04b23\n", "Multiple chat requests for f037ee76-de13-45d4-93c1-e2fd3ff1b709\n", "Multiple chat requests for 46ba3148-0f95-40bb-b283-7ab06caa486c\n", "Multiple chat requests for 601d7af0-a8de-4fd3-96b3-75f28c7e7d06\n", "Multiple chat requests for c5773b52-da15-4b55-b2d7-fea06e3994a0\n", "Multiple chat requests for 69254772-6bbf-4c41-a801-dbcd4c3fbbb5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:53,057 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6569cd65-0f56-410a-ac42-0a9b1e2faf31. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:53,224 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0ef7972d-5b56-4ba6-b754-96e5ea4d0bcc. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for e7f449a1-38e3-45dd-8239-4c39163bf1c2\n", "Multiple chat requests for e5777f49-a87f-4c02-a625-aabb356ea5d1\n", "Multiple chat requests for f9431153-09db-4102-9e54-dbd895fd91d4\n", "Multiple chat requests for 9c534638-9a4b-4686-a67e-2f1f0c06fb1d\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:53,249 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4dd796a5-87b0-4ee5-b10f-7e5c0be90300. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 6a78256e-6a53-4949-8db6-4be85f7382e6\n", "Multiple chat requests for 3edbddee-a4e8-4291-920f-f22eec5d8990\n", "Multiple chat requests for e28fafec-dd10-4df8-ab6c-9bdde862c13a\n", "Multiple chat requests for 1524105b-bd99-4058-aa9d-8a54d278e3a0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:53,683 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8499c56d-b886-48e3-bdbe-47ec98e9e5e0. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 287a07a7-3fd3-4986-a3a6-115000111ede\n", "Multiple chat requests for 5999be28-496b-423c-b100-8b52bbb526db\n", "Multiple chat requests for 6efdd44b-9a6f-46d4-ac70-af1220bee7cb\n", "Multiple chat requests for f0bbd41e-8ee1-4329-b0b5-d803560135fa\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:54,480 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6bda91e3-1c3b-42ed-a880-532fb30c43ee. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:54,493 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9b29d719-46ec-4bc6-b630-1d37dd049270. Found event names are: ['agent_request_event', 'api_http_response', 'request_metadata']\n", "2025-04-08 19:23:55,304 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e91f023f-cc17-4b46-bc63-d3cdadf143ab. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:55,560 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: cbf61e90-2ee0-429d-a565-bc7b9fe631be. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for cfb59c03-1a21-450e-8357-7fed6e0f54d3\n", "Multiple chat requests for 96be8008-95b2-4470-b3a7-a10fb30f3953\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:55,946 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 3c498c59-6eb9-4629-90eb-f4792bcf7f30. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 2a034add-901e-4ea4-9aae-972c16b3f07e\n", "Multiple chat requests for 18730b6a-5503-4513-aa27-3fc94f71c724\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:56,044 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 107c2e51-3b20-4eac-82fe-b00d7b90d74d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:56,114 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: fe508e21-9b19-4f47-876c-c8a1e172bd84. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:56,394 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 210cf95f-467c-4db5-bb8c-815046a6fa1f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c625d248-22b2-47cc-864c-3c86993bb8be\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:56,685 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8ec2a177-c83b-4bdb-b4af-19d32c6c817b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:56,823 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 58a3ea1f-b9d0-429c-9e9f-e60565e1a740. Found event names are: ['agent_request_event', 'tool_use_data']\n", "2025-04-08 19:23:56,845 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ab09e847-3335-4fb9-b30c-2081fcd9a8d4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:56,977 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 581503d8-c70a-403f-817d-74fcd82f5abd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:57,028 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: abc347f0-53bb-48cd-bacf-5b845123e96d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:57,037 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6ae99069-9a3a-4a75-9dc5-199f833e51c3. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:23:57,534 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 84cd0e51-a83b-4621-af09-420793453104. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 1355a404-fa9d-4217-8d42-a8624f83d948\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:23:59,354 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7386c0d1-8853-4775-a356-cc47985f0d90. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:59,497 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a76e5935-44cd-41d4-afda-87fe25571c60. Found event names are: ['agent_request_event']\n", "2025-04-08 19:23:59,783 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 37ca21aa-e617-463d-b7a4-9cb0b8ddb5ef. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,054 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4b698bff-61e7-41e6-9ea3-b9d8b6a1c207. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,367 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 706bc23f-b6f5-4d4c-958a-f8cb9f9e3028. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,389 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f6c821b6-07d4-4bf1-a7bf-873ad39efab6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,434 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 066db216-edea-40b0-9a9f-681f87c86715. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,523 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 58944186-34f0-4dde-b302-610e3e29f531. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:00,778 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9c35f4d9-e378-41df-bb10-a7f6ddaa09ed. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:01,096 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 185754df-d006-4aae-9c48-e43aa699c27a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:01,353 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 548f6bd6-18a9-48bf-9efa-49a3a26e27c8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:01,747 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: bae01045-20a8-4356-acb1-edc6ea9c306f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 6578e1cb-f8ab-494f-8416-d815eb78822d\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:02,542 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: fa501ef9-e895-4f84-b031-a3f81d7f8a65. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:02,595 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 792a76d2-ba45-4cd9-8bca-2e921ca4f7fe. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:02,992 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: dd2a54e2-6d5d-4ccb-8a02-f565df200d5a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,116 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f21c360a-e42d-471b-ac43-5b20dc3bc4a6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,124 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9671bec6-974b-4a35-82f9-b8fa8db4d72b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,399 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6556acd5-aeef-4486-bd2e-6f42ede377a9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,557 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 55b0d3e2-c2d7-4d2c-80ec-683f16887e0f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,791 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8ba875b4-2233-44de-ad02-f68008982c3f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:04,814 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b96c251c-b140-45a7-90d0-a8dc30905676. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for d334f543-943f-4ed5-98fc-8b3e37f657b7\n", "Multiple chat requests for 37f603d8-3b4d-4182-9f43-b2dce42880b8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:05,355 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f3e3970b-a305-4841-8dc2-8f4fe66549f4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:05,609 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: af234f9c-d4e3-4026-8209-b65613e2535a. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 946e68e1-81fe-46f1-a9e9-40c720796154\n", "Multiple chat requests for 7ead6a4e-bef8-4a92-9872-d14c07afb588\n", "Multiple chat requests for 96782cd7-daba-405e-8fc2-70d4496e45db\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:06,945 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 169e2742-d184-4a8a-bd5d-693ed71f4f6a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:07,030 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 385abf7a-4459-4a94-be17-932363b6ae2d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:07,073 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: aad46bb2-ce7e-4eb3-a1f7-e3d2489c7bc0. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 57f6a11c-49aa-4b4b-92d3-8749d0d7e0ca\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:07,272 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 731401a4-d2cd-4547-a673-ebb2eb8d848b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:07,335 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0aad5794-cfdd-46ba-bd6c-697d669ffe40. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:07,563 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a3622c3c-70a1-4359-bdad-34e556f276d2. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:07,630 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 72ff9ce5-58dc-471c-b80c-d025cc39ebf1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:07,651 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 48a1147c-e468-4f6a-a22b-2675c2ee0a48. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:08,247 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 3730a79d-0a6f-41d1-9234-a8f94a439a2b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:08,258 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c263391e-1240-4907-b0f6-6324a28a3048. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:08,349 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: d83c6be6-bd22-4963-af9f-b82eadb362c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:08,388 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 907597bc-8513-46ba-bc3b-1922c7b86918. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:08,568 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2401090f-0c96-4f7f-b29e-a16fde35cbc0. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:08,813 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8d9c339d-c6b8-42a6-aff4-e3bf5fea452f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:08,852 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 987dac0e-0bcd-46db-a73d-9fd2cb26b94d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:09,043 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5ad399b0-31eb-43b9-bca3-9a2fef2c5a2d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:09,087 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 596d4d25-7838-4d81-a886-9be03bb8397b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for f05c9859-0c82-4c51-a634-6f7129d8ac6e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:09,782 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e2c61535-9159-41e8-b6ae-6082f19b7196. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 51f8c526-0075-4d9f-a9b7-17dd1ed00c8a\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:10,127 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0c6308e1-306a-4ac6-8b03-7485f2368f5c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:10,333 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7b883464-26f5-455f-847e-cc50397863c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:11,113 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: eb0a5bc2-d060-486d-af9c-2b3edb84193e. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:11,528 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a02eafcb-98c8-4598-8733-92c6953dd0d1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:12,103 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c521be0d-da70-4a29-ba2a-64c2f2921072. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:12,360 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9c7a629e-1247-4bf9-b78f-dcc38eac3b5c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:13,237 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5c300408-d6c6-45cf-9440-fd58c1a27331. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:13,238 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 73e164c9-02eb-4ab3-b176-ded88e4b1d1b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:13,317 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 34aa4333-8adf-4f11-9c33-1bba3b9a4c05. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:13,333 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8119d355-7bc7-4526-850f-be2c05144a52. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:13,539 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 82c13f38-bf11-4a80-b597-462763e016a2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:13,626 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 66709fc6-d909-448f-b615-407ef03821d0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:14,092 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f384ae7b-8278-46c4-80f0-de68e127438f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:14,600 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f4e2a463-ec85-4896-9c6e-aff29b3477be. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:14,626 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 251e26f3-61a8-4d66-a534-02242e1d99c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:14,628 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2caf67db-e1b7-4572-9040-cae47347b390. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'tool_use_data']\n", "2025-04-08 19:24:14,639 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ca16e187-9e7e-4c7d-878b-50e76ee7a1cc. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 13a09a24-eb1c-4d94-addf-c2f0c6db305e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:15,134 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 27cf50eb-3830-4a47-8f91-224afd290c55. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:15,145 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: efa43a60-c22a-4fe5-8ad9-75d9ba551456. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:15,646 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ab6d2567-cf1e-4e6f-ab77-8d9f4f7dc775. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:15,837 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2c3aa529-b92a-4cc3-a753-7aa468e2a8c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:16,114 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0007352c-d3cc-4277-8cd7-d50c57aacf3d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:16,179 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 75a06749-521e-4acf-b85e-56bf5e71185f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:16,195 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 91217425-b498-4362-9f06-20ab04527474. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:17,120 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f22e6963-cf8a-42a7-9937-d2845ea83560. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:17,228 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7c57effb-4959-4925-a027-799ed0f64db2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:17,532 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 797a5333-671a-438f-b4d7-05fa26ad656a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:17,588 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 96f5184f-c771-4ed6-81e7-b5c7887d5958. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:17,878 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: dacf09e2-a8fd-4d74-99d6-65e532312962. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:18,926 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 732a8a92-9dc7-4737-9522-ca2093205f91. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:19,140 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 28839353-b513-427f-8d15-83915a13cfba. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata']\n", "2025-04-08 19:24:19,150 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f553cf5d-437c-494b-a2c4-9e5ef1fa61aa. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:19,174 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7acc8528-c5a8-4a4a-b4b5-b9cda65bb6e2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:19,408 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9d408156-758f-47a5-9166-75c1c678592d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:19,762 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 68fe959a-0ade-4dd9-9892-d5507cb30c51. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:19,884 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 218cca75-2989-4738-8fab-d7e788c63f32. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 4bc03782-189a-4d59-a707-65458006114e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:20,999 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e72ccf11-5e5f-4149-9636-291dc154be1e. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:21,145 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e917c11e-06bb-446c-a05a-9b2807a8289b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:21,203 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6b89de74-5fba-4e3b-b7b4-c9a66135f083. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:21,239 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 50905e43-4526-4973-874c-739f4ebfe216. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for d1cb819c-96d0-40fe-a167-98c6c7e63036\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:21,559 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 71bf3aec-6bdf-42a9-bd0d-d88b9cb20ec7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:21,915 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a133a64d-4b19-4c50-b6a7-05c1e5491b64. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,047 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: fa83c935-483e-428f-a3ae-e58a47583a9c. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata', 'tool_use_data']\n", "2025-04-08 19:24:22,062 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7bfec68e-46cc-420d-83d3-6817ebedca2a. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata', 'tool_use_data']\n", "2025-04-08 19:24:22,086 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b5c59e82-4582-41fb-b6d2-1f1ad5698ff2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,092 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 45bdd45c-aab8-44b7-b633-32790e193a32. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,094 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8e60ab6e-6a27-4822-90fe-89c37d6942a1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,662 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 25a8c751-7845-4245-b4ba-626c3bc37edd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,708 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ff2fd4b5-36b7-4e89-8c3f-1b849e5486c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:22,929 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: efe2df2b-9f4c-484e-8db7-554285c465c7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:23,310 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9daa7359-85c5-4332-8f0e-e5882805bbf4. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:23,544 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b94a2651-fab0-4963-8de8-d8e1b5dca677. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 0da9843f-3190-45b7-b787-a276901c7fd5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:24,420 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c9e259f4-f061-405a-9eb1-b632649702ca. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:25,026 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 738b9f78-3b36-4b70-a6db-b304081fe484. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:25,036 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 31df2f65-82b9-4d10-834d-1ad058d853b0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:25,116 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b236fd86-4769-4d2b-89a4-47c9a55cba98. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:25,120 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0d24d23d-f589-4e79-b02a-464bacf5ea6e. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:25,166 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8ed67ff1-58db-4911-86dd-1164ef92a2b9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:25,393 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9d062563-fd22-43a5-aa6d-da82fac5191b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 3e9c8397-1176-45b4-b601-6cbae722b6ba\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:26,004 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: db25309f-6041-401c-b199-4909fa37a84c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:26,004 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5a3c419d-3ee4-4f81-a45c-5a21cee661a8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:26,009 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8200a40c-6855-4d37-b40d-892a234b6336. Found event names are: ['agent_request_event', 'agent_request_event']\n", "2025-04-08 19:24:26,061 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: af894236-6a77-4a88-afd4-4486c787dac3. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:26,351 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ef0f54a6-a254-4c94-b5de-8d8c677a54b2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:26,563 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 76354fa7-afdc-41ae-810b-f4243cb5ed92. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:27,421 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9a611241-864d-41f1-9543-60b7e97e2b22. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:28,106 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 66a4ee9a-d319-4b17-a37e-31802aa5cfeb. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:29,232 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: d7867fb8-a3b5-4edb-bd57-285b0ebb25b4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:30,319 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f0b04b7f-c181-4477-8bad-a098f5079a7b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:30,896 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 10144a7c-fab9-43a9-84e6-e408efc6c3a9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:30,897 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e5434133-1c6d-4a85-ae4c-99ee632ae70d. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:31,765 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a0c6d882-eded-4a28-8755-70a709ac1ab1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:32,627 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7342c83d-00e6-43bc-93f0-460a2fc7782d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:32,628 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: d1a8acef-0e89-465b-8a90-2bc47a55dfab. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:32,917 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0f318033-681f-4911-af81-00b0a3a9d368. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 21651d18-3e18-4004-bfbf-722a45d05a6d\n", "Multiple chat requests for 92103476-e873-4683-93f1-9b8c2e1333a2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:33,486 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9c1eb9a8-ab5e-4568-b21b-51d934a8a462. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:33,505 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: bc2ccd72-0a3c-4ca9-af3a-bac8dac08abd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:33,924 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 66dff8f2-241d-4e07-bc22-a4aa911aaa17. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:34,007 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 1923b92a-1ec6-4dcc-8395-f05a0928aae4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:34,568 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f132d716-44d3-47be-ae1b-5ccac2bb36f5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:34,797 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a99e47b6-25fd-4251-9d53-459aa06f44e9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:35,238 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 6cfba726-e794-4d91-97da-48b9f5ba5ecf. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:24:35,474 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9791ca99-30ab-40ea-afe7-c313f87c9072. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for f3d562af-37e6-4e24-a6b3-d2a3e3e1ac7c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:36,220 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4841b2e5-9297-4b75-bf40-7e30ad306cf8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:36,307 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b346df57-40ea-4329-b46a-4f21ee3b62a1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:37,457 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 09460b10-546b-44d8-aa19-5a30a59f8a6c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:37,606 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 38713f28-21d2-44c6-a4f8-b7641e754cc7. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 2384c237-8e6e-47f4-9730-4a97a5189c9e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:37,970 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ca950b69-0a35-457b-9e4d-da6a69396e2e. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 2f2583ab-5931-4645-9e63-a18b806383b5\n", "Multiple chat requests for 070187e4-1138-4a9e-be69-7266ef6f5276\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:38,184 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 3e145859-dc3c-4712-85b4-9504a88799c2. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 447de6c9-42d7-4284-a962-7e4002847c49\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:38,506 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5968e31e-ac85-4897-80cb-e0d34fec1e22. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 71d80112-9751-4d29-8780-6465bc5a47dc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:38,901 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: bd59ae3e-6f21-47aa-a309-ae3b67d9dd68. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 6f8cdf85-9f39-434a-abcf-8f701212a679\n", "Multiple chat requests for 3bb973e1-e389-409a-81e5-14e76f091901\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:39,045 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 57976ccd-94b8-4d7f-9c2f-ed0166d7480f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for a21e087a-f630-4428-a931-ab05eb979313\n", "Multiple chat requests for 0302793b-ffa0-4d9c-af5b-d59f772ad9f0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:39,829 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 53196645-326b-4aa0-8e40-825a17bbeddf. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 442713e8-5a68-46b8-865b-840fe553bb5b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:40,899 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 66ea965a-25ed-4314-a64a-1518a3610588. Found event names are: ['agent_request_event', 'tool_use_data']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 17b93e46-bcbf-4bae-950e-75c17401d129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:41,493 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 66f6009e-75fa-409b-81a7-ec5f14890552. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 12d3f21e-20f1-42ef-8bb9-d35e838abaea\n", "Multiple chat requests for 80c2d2ea-5c32-44ea-9e62-a707e96aa509\n", "Multiple chat requests for afdf007a-dbbe-4112-bedc-d89d05021ba8\n", "Multiple chat requests for ce52333b-63b2-4af5-ab0b-e2baa4216049\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:42,056 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 99191566-b67c-45d7-bcb5-6787df267370. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:42,267 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5309947e-5ba9-4a33-97d5-bba5154767a5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:42,282 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: baf69f47-73ad-46ca-9ccb-c5aa30ec5b39. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:42,939 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: cb46e60e-ae4c-4cc6-b3b3-b1e012b65133. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:43,185 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 5f341235-2d2b-4337-b021-c641b1e40be5. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 71c481e3-bd1b-4dea-97c6-c31437c44548\n", "Multiple chat requests for e063615d-5834-462d-b8a8-84f5a3335a5f\n", "Multiple chat requests for 5120e9a8-fc81-47a6-8c6a-3f8e8722ffc7\n", "Multiple chat requests for 14db1a74-5853-4691-85c1-334267ab48c4\n", "Multiple chat requests for 8ac060bb-b49b-4ee0-8c2b-09ca64c23df2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:44,409 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: cf6826d6-e496-4df8-8617-03e1b6fb8aaf. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 8f368aa7-82f8-457f-b8e8-1efb934d2e22\n", "Multiple chat requests for d7177a16-5c36-42ae-a879-385e3478b16f\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:45,559 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 9d3cb7ba-3fb1-4c05-81b1-5f795e41fcae. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:45,654 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 813f5e9a-5407-49cc-ba77-b3403cfbfb26. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:45,659 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: fab95645-20f1-4989-871a-cb6fd44869ef. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:45,944 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b13fefcd-42fb-40f1-b209-dd960d8c96dd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:45,945 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: bf854056-a476-43c9-9007-2557ec9deeb1. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 09d506c2-49f1-4051-94da-2fdaefbde0b4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:46,201 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 23a20084-06f7-4f4f-9fe7-e63a05c422d4. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c0dc711e-faf2-4e84-a573-906ccb56f2de\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:47,033 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 82ae7609-60d6-4896-9ac1-8db840574e77. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:47,246 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: cddd925d-539b-4d9b-888e-75016937b2c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:47,316 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 678fceb5-0d19-4ad4-b65f-4ae12ad92e98. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 790f7961-097b-4b86-a806-f02167b58b96\n", "Multiple chat requests for f3dace02-d5c8-4a19-8884-dff4699e1e8a\n", "Multiple chat requests for 08a9d195-d937-47d1-a3ae-4bc46710302d\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:47,578 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e342786a-7a25-4c97-b085-a83d7c136b4b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:47,586 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f0959b91-b499-424b-9ec4-b21f8389840c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 72b573bd-8b27-4dd5-9caf-f2d85fd23643\n", "Multiple chat requests for 899964f2-5c10-4f8b-8aee-e2fc09b065f2\n", "Multiple chat requests for fd47382a-da02-4be5-8e58-40e5935ab955\n", "Multiple chat requests for 7846948a-29f5-40e0-833a-5eb6221a0b08\n", "Multiple chat requests for b9bac3a6-4595-4605-b7f4-e130e4ffd920\n", "Multiple chat requests for 5881ebb7-7062-4ee4-a937-76311c148123\n", "Multiple chat requests for 793c2faf-aabc-408b-868c-2781a31994db\n", "Multiple chat requests for 13f6cb17-4f96-4bbc-a03b-4e2ab8f43148\n", "Multiple chat requests for ed30877a-1f5f-4b2e-89b5-886b7843cbf8\n", "Multiple chat requests for 0e1abfbd-d462-4a6b-9a96-0ecb55c617a5\n", "Multiple chat requests for 903ebef6-a57e-45a6-9aef-0c8dcae9db88\n", "Multiple chat requests for f2d62bfe-ee30-492f-a07a-80d0e49a69fa\n", "Multiple chat requests for b092c716-1ec3-467e-bd81-32d6e98fad9c\n", "Multiple chat requests for 10554cf8-6a0b-4909-b840-c715f4ca23ea\n", "Multiple chat requests for 636c3bfc-79ee-4e25-b5f6-885a44436aa7\n", "Multiple chat requests for 1e4d0ff8-f7bc-4d41-93bf-2c2e98e5684c\n", "Multiple chat requests for fbd1ddbd-2026-4b32-9d7e-0b7f21940b32\n", "Multiple chat requests for 0caf5ca6-9bef-4903-8f7e-968f5169a0cc\n", "Multiple chat requests for 424dcaf4-db0c-4829-9445-ab61b3ec36c4\n", "Multiple chat requests for aa5b8b6f-f82d-48a2-a622-f177f076215f\n", "Multiple chat requests for ed6035af-5ca3-462f-8bf8-c75837b123dd\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:49,176 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 28754fa3-c927-4244-8792-3c62947df077. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for e68d24cb-9d77-48be-83ca-7cc5b7e89223\n", "Multiple chat requests for c15184bb-b531-4e1f-a4d2-cdf4b928313b\n", "Multiple chat requests for 0890d468-d93a-4878-94fc-299c7fe581dd\n", "Multiple chat requests for 08c4bb37-5eab-4674-b3b1-66b29d307777\n", "Multiple chat requests for 877e5752-c5d9-4e3e-8e49-c254695bf5bb\n", "Multiple chat requests for 6a6e0877-7670-43fc-a4ec-6727fdd8a2b0\n", "Multiple chat requests for 9855b641-646b-49c5-a402-c80408b32c0b\n", "Multiple chat requests for 6f05a5b2-b701-4bb2-9e1c-a0d12e5bc6f6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:50,017 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 682c9333-aa9f-4fd9-aa8f-77e0dfad9937. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 0c269029-d261-4dfc-898e-146eb06624bd\n", "Multiple chat requests for 7a859bfc-2dc6-493b-a99c-e68e27199096\n", "Multiple chat requests for 82f996a0-951a-4566-b84a-8f3c118935b8\n", "Multiple chat requests for 4cb834bc-d657-4003-b05d-1950b6f72a6e\n", "Multiple chat requests for c08b449e-6e6f-4d2a-b149-caf88f9e843d\n", "Multiple chat requests for 2ca4bf0b-2159-4a9f-8de7-91e540d4831c\n", "Multiple chat requests for 843241b6-e23e-4e4f-bbf7-9d8eefc4a3a1\n", "Multiple chat requests for 32bd2531-9d7d-4ba5-89cd-24e1ca7dacf2\n", "Multiple chat requests for c23974d4-286b-4af8-bbee-45c9845ea32a\n", "Multiple chat requests for 96366f8f-0f9d-4d68-944e-1ec8a2079163\n", "Multiple chat requests for 038ba0b7-8018-4886-8836-f336cdfcc96d\n", "Multiple chat requests for 4a465dea-d1ca-4401-aa95-de3b749c9bcd\n", "Multiple chat requests for 64ee1392-6293-42d0-87de-cfc4e258a07e\n", "Multiple chat requests for c6ef3828-7c7f-4e93-9259-60f8d2df88cf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:50,747 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 985144dc-0a44-458e-9240-9a3a7015a9bc. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c4e1e31d-8b53-4e65-9e3a-acafdd904e38\n", "Multiple chat requests for 0fb6c2ed-3d22-4cc6-9490-375f3edeb8aa\n", "Multiple chat requests for 6c1cf9d5-ca32-47fd-8c9c-2742023a869e\n", "Multiple chat requests for 19ae16e1-669f-4b09-b0e0-eaf8e847405e\n", "Multiple chat requests for a563785a-c0e3-47cd-b0f4-e958893126d0\n", "Multiple chat requests for a25bbdba-d093-45f4-a5ee-eba939db313c\n", "Multiple chat requests for e5231bf4-f25a-479b-b952-d9ed53016c97\n", "Multiple chat requests for cc1cd08f-0671-4b55-84c4-89978fb570e0\n", "Multiple chat requests for 65a2aef5-c233-411e-8f2d-dbf75c9d136c\n", "Multiple chat requests for d9ee823e-2a86-43ba-b8f9-0029017504e1\n", "Multiple chat requests for 9095cf73-07d9-42bd-955c-b096031eac9c\n", "Multiple chat requests for d574fbb0-c34b-4db3-a447-bb62f558c60c\n", "Multiple chat requests for f0425d27-1ae1-489f-b350-f3cd1c813fb2\n", "Multiple chat requests for 61e0a8cb-7924-4b27-8676-8539b8518ec2\n", "Multiple chat requests for 36009652-515f-42a8-b55b-78ee6279812a\n", "Multiple chat requests for 865700df-1fd5-435c-9914-8558fcc20c6a\n", "Multiple chat requests for 1ac272ee-5ccf-45b7-b767-ee868c09ca6b\n", "Multiple chat requests for 35d61976-eeaf-4222-947d-f8d887f8d8e4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:51,874 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: d62dfe79-6eb7-42ce-971e-872256b241f2. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 85a371a9-51bb-4b8f-8732-848546ee7228\n", "Multiple chat requests for adb77b55-a6ea-484e-87bf-da3fd98550ee\n", "Multiple chat requests for 71a74465-76eb-4d8e-86ce-884ffd8f8ea5\n", "Multiple chat requests for cbd1dde1-cc78-4a0e-bf84-fbbb3ac66417\n", "Multiple chat requests for 8e907c2d-cfe1-44be-aa8c-afbdd0397fc2\n", "Multiple chat requests for d28cc29c-8edb-450d-ac60-82b5cac98682\n", "Multiple chat requests for 0605c500-2b3a-4b75-9fff-97d5cd9ad183\n", "Multiple chat requests for 97e8e0d9-572a-48b3-b5b8-2f21d313f998\n", "Multiple chat requests for 9a0f55c6-62d3-47e4-bfd1-6e1e92a17540\n", "Multiple chat requests for e0507fc2-ce3e-4c31-bb34-91d5edeb28d4\n", "Multiple chat requests for c18da499-0a2a-4f9b-ba23-395ab94174e3\n", "Multiple chat requests for 09d8b672-bbbf-4c12-8a9a-8b7af810acea\n", "Multiple chat requests for 1fd0488f-2143-4e74-9a1c-e9f46170e499\n", "Multiple chat requests for 923eed6d-0ef6-492b-b9b4-b20a81189b2f\n", "Multiple chat requests for 53b0b890-790f-4c49-814b-85c525a49aa3\n", "Multiple chat requests for b6d90c3d-3376-4fdc-8c65-9ba0593b1c31\n", "Multiple chat requests for 5c705c30-735a-4761-93c3-f60edce28b5a\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:52,893 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8aadae46-2247-4bb5-8689-51bc65b0a8bf. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:52,939 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4c032038-f3ae-424d-866e-90f3e3b4db53. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 2a0b1d17-d38f-49a5-8de1-cf78d1c53dfd\n", "Multiple chat requests for 41ef0569-f83d-4a26-894f-b6cd055f5c95\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:53,254 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 8c21c09b-cd26-425f-a2db-b09b67429af2. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 4d03e2fa-e869-4fa3-ace3-807e009d36ec\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:53,443 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 7e2b002f-3e40-49ff-b2e4-23ce9555f208. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 66cb0bd9-a1bd-4d9f-82d8-135494e05c90\n", "Multiple chat requests for 0c889943-3995-4d62-906f-61a96f16e98e\n", "Multiple chat requests for 1452ce67-a0d9-4698-802f-95ec8a35c6d9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:54,983 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e12c6f63-76da-4046-9c3e-2126cfd17f3d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for fd0fcddb-f3ff-47ae-adde-1cc8dfd49d69\n", "Multiple chat requests for fb918c3b-f32b-4052-b84d-5cff77944bcd\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:55,284 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 992cfbbf-a8d9-4104-82cc-5571893301bd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:55,313 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: f630dd91-d06e-4743-81fb-9331f1a8fa84. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:55,327 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: b67b9d9a-4a95-4e04-9421-847afe485232. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 8e629be2-68c3-4963-81dc-f6ee69d41d5b\n", "Multiple chat requests for de26547e-787d-4f4a-9428-56308c20abbc\n", "Multiple chat requests for 173e8df8-c6f3-4192-8816-7d980c6aa814\n", "Multiple chat requests for 623c2eeb-b6c7-4019-b2e3-580c2e4ce7cd\n", "Multiple chat requests for 1188b273-3785-4768-be27-6d682fb69949\n", "Multiple chat requests for 27856bf8-4100-4970-8df2-88add6a7ef8e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:55,541 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: fab22e61-6fe1-42c7-a31b-0cdb6716e0c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:55,571 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4d05e776-3dce-49d3-a070-cc71d5fe6582. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 742e50ce-c713-4f68-8a00-2c2856326bbb\n", "Multiple chat requests for 86fbd205-ddb4-44a9-b47e-aab0ec2bd90e\n", "Multiple chat requests for 1896c17b-a404-413b-986b-bf7cd5ab82f8\n", "Multiple chat requests for f06955d9-b4ba-41d1-a25a-17cae898f27c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:55,834 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 201a0661-8263-4b44-9bfe-183c9cf8556b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 6ddafd0b-7268-40e7-9c81-9696d5c9c4ce\n", "Multiple chat requests for 22ede6df-8fe9-4f28-81b1-917c26d77430\n", "Multiple chat requests for cf3d04ae-19d0-4adc-9dbd-70eeb6d53717\n", "Multiple chat requests for aa1e1da8-b874-48b1-88ac-3715ca20efae\n", "Multiple chat requests for 50bb59ba-a041-4980-945e-09659784f11c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:56,319 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: c8b051a4-dfbd-4d44-aa27-f15bc837ae03. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for c037dbe6-8487-4f75-a50a-b96e549abd31\n", "Multiple chat requests for 1b7b9f20-c8c1-4bfe-b9f3-39dae0aa9b69\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:56,629 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e4d2d822-4426-4241-9468-5595bffc3b66. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:56,652 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4ae101b6-e452-4310-80a3-737d22e59a71. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:56,654 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: ed698236-deb6-46fc-a845-3a0216441624. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for de160bcc-4a00-4d52-9473-9d2553d621fa\n", "Multiple chat requests for 752d5a6b-135f-47ee-bfd0-3e0cf31c312a\n", "Multiple chat requests for 1eb990ac-d64b-4e2f-ad8a-84f06185a627\n", "Multiple chat requests for 6580ff3b-3726-4d7a-8f37-fe34603bc92f\n", "Multiple chat requests for 6941a069-78c0-464d-b87e-e2df4d5a57ac\n", "Multiple chat requests for 7b74989d-eb9f-44b0-b714-47e7c312aa4c\n", "Multiple chat requests for 3769072d-788d-4a43-9292-ace94be655c9\n", "Multiple chat requests for 7cf5a277-7be4-44d8-b3a5-5300e0b07c18\n", "Multiple chat requests for 27b2a498-631a-4804-a8bb-ed8dc994cfb0\n", "Multiple chat requests for 61d43cc6-9971-4310-966b-3ce4fbd4a867\n", "Multiple chat requests for 8950d890-e80f-4713-ae36-5bc241a5ae81\n", "Multiple chat requests for 75755a31-bade-4d79-a0a3-c8704e13d2c2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:57,733 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 776cb3e3-8740-47e8-93aa-ef5fd51067b9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:57,849 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2cc6cc45-54a1-45e1-95d6-ae87fb0dcac7. Found event names are: ['agent_request_event', 'tool_use_data']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 9aef42a9-b49e-4529-85c1-961d93330cbf\n", "Multiple chat requests for dc1c091a-408b-4ce4-a653-950be6468a25\n", "Multiple chat requests for 3f1ff82c-7efb-4ad0-92ce-593b7285ce03\n", "Multiple chat requests for ec86d9ff-74cd-4ec1-ad81-5e619d3b04aa\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:58,859 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: d22d1481-d3a1-4aa6-86bb-d2322ff1abe0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:58,891 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 4b9e38ae-9e36-40da-87fe-974577926ef6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:58,905 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 008bb047-fda6-4eb2-b6fa-c09ea68dfb90. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 505baeed-8ee7-4b79-a1b4-5f4cb2eb72b8\n", "Multiple chat requests for da8e8185-5478-466e-8086-72ddc287172c\n", "Multiple chat requests for 8cef05ad-f2f6-4bed-aec3-5a8cc936ee86\n", "Multiple chat requests for 11c41df2-02fc-4dcb-99e3-6038288b37a0\n", "Multiple chat requests for ee690590-a403-44d4-91f6-fc20601f1f04\n", "Multiple chat requests for d00b0fb1-320b-43bb-a100-f46cebb5a928\n", "Multiple chat requests for 8bd97a67-7198-4667-86e3-eb49a860a36c\n", "Multiple chat requests for b9c6bf64-ef1f-4c12-bd1d-aca0d5a37b8b\n", "Multiple chat requests for 8ff64b7d-a353-42a7-9b97-93bd2c8bac30\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:24:59,342 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 54920c92-605a-4d50-aedb-dcb4b0076243. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:59,362 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 93f5ec2b-573d-4236-9c75-b46919aaa059. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:59,712 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 46d1edad-0ed7-4601-b154-7ce1d5eab091. Found event names are: ['agent_request_event']\n", "2025-04-08 19:24:59,723 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 60573ded-d796-4453-a05f-40138176fef7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:00,220 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: da243818-e73c-4c64-9dbd-e1143d877a51. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 0a6d2f06-4ffc-4c5d-b492-4831ec6c1399\n", "Multiple chat requests for 2e67ad11-7ef7-4c55-8633-82784e3c335f\n", "Multiple chat requests for 6954cf7d-7b1e-4745-ad2c-ccf0c7d3c2c4\n", "Multiple chat requests for 654c037f-fbc8-4c32-82cf-cde30479b882\n", "Multiple chat requests for 56b17265-6683-42ba-8216-4f587186cace\n", "Multiple chat requests for 100c469c-f16c-4708-b05f-06579efed90d\n", "Multiple chat requests for b41df652-10b4-4049-965d-a2b62990cff0\n", "Multiple chat requests for b21a0f90-c57a-4606-9029-c518eb9ef7eb\n", "Multiple chat requests for 09f6165b-062a-43c9-bb5b-e02de937e3eb\n", "Multiple chat requests for 2916b370-20d9-4711-bc32-5cfb228e5cf9\n", "Multiple chat requests for 3c07fcde-b335-42d1-967a-726af4b82eee\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:00,868 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 0aea3ebe-9cf5-4434-803f-f2726799c25f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:00,876 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: e379ddd0-0148-45a9-8f79-1fa28d84539a. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for cf7c720c-8896-49e7-9368-ef400e653653\n", "Multiple chat requests for d7c65f30-49fc-4ac6-bab1-494a2db43ec4\n", "Multiple chat requests for fa07cfdc-9a4e-4096-8f63-b5916a98cd1b\n", "Multiple chat requests for 66ecf0d9-48e7-4dd6-9e41-e8b83a4f1281\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:01,204 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: aeda7fa4-eb89-4006-90a2-8694c818c784. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 56da3004-6a18-4403-9cd6-41b2033b4301\n", "Multiple chat requests for 85807c7b-7815-42ca-9b06-55a0703e824e\n", "Multiple chat requests for 79f81695-77d4-4adc-a28a-a2a49d39d727\n", "Multiple chat requests for 683fa992-da2f-4a7c-bfdd-100eaecfb14a\n", "Multiple chat requests for e6d1a338-bcf0-4421-a9d3-fbdf9542e90a\n", "Multiple chat requests for 0cea79a7-ecb4-4493-991e-f48bc0168e28\n", "Multiple chat requests for 9f3010cd-cbba-402e-ae8c-1274e290f257\n", "Multiple chat requests for d35c7fce-33d2-42cf-a735-7722133f5b03\n", "Multiple chat requests for bba80814-f669-4955-a849-9dd37b6d63ab\n", "Multiple chat requests for feaa35c6-8af0-43c4-bceb-4cfb28ca1e16\n", "Multiple chat requests for 1b7d3e41-dddf-47cc-9278-c7df558c5c1f\n", "Multiple chat requests for 823aeff5-8fee-451d-8ca7-5e6e47ca5afe\n", "Multiple chat requests for ccda37d7-8494-43ef-ad6c-3f1f7126119a\n", "Multiple chat requests for fa4ffbb9-7410-46c6-b9fd-e13329bec778\n", "Multiple chat requests for aa4c6c4a-01cd-42c5-949a-08c8a1e22a8b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:02,128 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 61b63dbe-033f-4a3e-9f0b-6e45b15e8abb. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:02,143 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2ec00512-e5c6-424d-87f2-5d05fb554e62. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:02,244 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 3f41b9f0-2147-4f6b-bd56-d3cf124f809c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for 0476ee2b-3fdc-40f0-a8ca-3e8cc4c71927\n", "Multiple chat requests for dcec3c11-0c95-4cea-b6a1-69589b01c53b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:02,306 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: a61fc773-2d40-44fd-b84c-2117c4bb1c14. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:02,434 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 405420fa-44be-468d-9572-050f44d5f1a7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:03,048 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 2e8529b5-dce4-4546-8b0c-ae256c2486c0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:03,077 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_request'}) for request ID: 984293f0-fee2-42c8-be94-582d6710a5c5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:03,259 - base.datasets.gcs_client - INFO - Fetching 8200 requests\n", "2025-04-08 19:25:03,440 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c08c6cb0-04b7-43fe-95e3-436df53d2f44. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat requests for f10b2932-68f4-47df-988b-103709ec4ef2\n", "Found 7703 chat requests\n", "Multiple chat responses for 03eb237a-cef3-4123-a110-4964fa815728\n", "Multiple chat responses for 3b72a1a0-2765-4fcd-8e22-68246e5916a0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:03,988 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8e5371ef-f15b-43a3-b40e-180cfa06592f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 3f272bf2-8dcc-45d0-baef-95ed25ad0017\n", "Multiple chat responses for 40424f2e-7728-4e41-a59e-d9c5d82e77f6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:04,948 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: aff43774-fc76-42b6-97d4-852cdc311b0d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for bf29cb54-bcd5-450c-9dba-dfce937d55c8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:05,002 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8c87be82-3757-4cf5-a472-b3a9278d620d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 836fbfa6-d65e-4a2b-b95f-cf405be4ac69\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:05,347 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 56900cf1-18c2-439f-9f59-4c701c4bed07. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:05,463 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e92b055b-d002-4684-a2c0-b6133938b0ae. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c2c5c249-977c-4842-903a-08e86c4dda24\n", "Multiple chat responses for 3e21abef-f55b-47bd-bffe-67c3d172ac63\n", "Multiple chat responses for 840bcdb1-ff68-4130-bd14-9920daff80a0\n", "Multiple chat responses for baa35b70-4e7d-4abe-bef3-9e318b965049\n", "Multiple chat responses for 07696536-4574-4304-abc3-23fb66a876c3\n", "Multiple chat responses for 66ab1d02-5343-40c3-8729-c69516803e6c\n", "Multiple chat responses for 99cb3ef3-9bc4-40ff-a64b-53da3d0500fd\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:06,194 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: de123283-ad01-43ac-9fd2-2952b009edf2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:06,304 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: cbdc80bf-4fd8-43e5-ad52-aa3d6915324f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:06,363 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a6beaf1f-2576-46af-94d9-828e423126ba. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c660a150-2658-49cd-b14f-054c3bea8ec2\n", "Multiple chat responses for 22ed539d-98b2-40ea-8d9e-a959dc8d5d97\n", "Multiple chat responses for 6c3bd97f-ff20-4224-9b56-661adeb772b3\n", "Multiple chat responses for 070b49f0-b77f-4ab9-b43b-f8f02013a506\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:06,691 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0770cebc-e405-4bef-85c3-ae678554154c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 0da9968b-7275-4735-9481-006096a1fe49\n", "Multiple chat responses for 9604c0c4-3f60-4594-889d-86c865eddd13\n", "Multiple chat responses for ee6eb7fb-a049-4039-8da4-eb16ad34e0f7\n", "Multiple chat responses for d70639be-99ff-4642-b7c9-2fca4b65eaab\n", "Multiple chat responses for 5138d53e-c188-48c3-a840-409cbedaa6bb\n", "Multiple chat responses for 219ac2dc-3ae9-4bb8-9ba5-7a666264ddbb\n", "Multiple chat responses for 16546f52-6817-4d15-b8b8-ecd2a1fc3ee1\n", "Multiple chat responses for 45689c38-803e-4524-988d-e66ad7a33acb\n", "Multiple chat responses for 18db82fe-7b3c-4f0b-bd7c-f3a225e84d8c\n", "Multiple chat responses for 5b5dd89d-2b6f-4858-b285-02283b404cf2\n", "Multiple chat responses for bb00d891-f821-4508-a5df-e583b9533970\n", "Multiple chat responses for 277ac561-7b29-43ac-95b2-87dd67c919b0\n", "Multiple chat responses for 2c784a91-cf7a-4952-8f09-be0d1fe2b5d8\n", "Multiple chat responses for 237b1a57-0d17-4908-97ed-01d3c68aa18a\n", "Multiple chat responses for 8716f0e8-8faa-43a1-b138-d79e1d4dfc8e\n", "Multiple chat responses for 95c73515-a739-4940-b5a9-dd8dd19da93e\n", "Multiple chat responses for 7a150f4d-d499-495e-b1ba-f35b3bd64606\n", "Multiple chat responses for 18cecf74-d096-49ee-a9ef-b7979dff91d2\n", "Multiple chat responses for 3b1564f8-177c-4829-b9f3-de484acc1535\n", "Multiple chat responses for eadbf15f-e8dd-4b7c-8204-9c63856fe84e\n", "Multiple chat responses for bcdbb347-ba5b-48dc-8e07-c4add30aa4e2\n", "Multiple chat responses for f91b71d6-e8cf-43a4-91c1-52ce1cbf5eab\n", "Multiple chat responses for 22842e4f-f95f-4d0e-ad1d-8dd5ad070ae3\n", "Multiple chat responses for 9549577b-8c6d-4147-a88c-926ac6e68ee0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:07,987 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c83f872e-8d53-4e5f-bd51-a6961b37dabc. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:08,010 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 119255c7-692a-4e10-92c2-1cf92ed6b7c1. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 3e9aecb9-f6a7-4d99-ae0d-51f528a5fbf4\n", "Multiple chat responses for f11788a0-442b-41c7-a0e3-daa4de9e9a83\n", "Multiple chat responses for 0fc4142a-4545-434e-95cc-cd54acd1e87b\n", "Multiple chat responses for 66dde76b-9412-49ee-a43b-2f63eda9bde8\n", "Multiple chat responses for ea0862dd-e573-4fa7-9e0a-1ced10b04b23\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:08,448 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6569cd65-0f56-410a-ac42-0a9b1e2faf31. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for f037ee76-de13-45d4-93c1-e2fd3ff1b709\n", "Multiple chat responses for 46ba3148-0f95-40bb-b283-7ab06caa486c\n", "Multiple chat responses for 601d7af0-a8de-4fd3-96b3-75f28c7e7d06\n", "Multiple chat responses for c5773b52-da15-4b55-b2d7-fea06e3994a0\n", "Multiple chat responses for 69254772-6bbf-4c41-a801-dbcd4c3fbbb5\n", "Multiple chat responses for e7f449a1-38e3-45dd-8239-4c39163bf1c2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:08,636 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0ef7972d-5b56-4ba6-b754-96e5ea4d0bcc. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:08,671 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4dd796a5-87b0-4ee5-b10f-7e5c0be90300. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for e5777f49-a87f-4c02-a625-aabb356ea5d1\n", "Multiple chat responses for f9431153-09db-4102-9e54-dbd895fd91d4\n", "Multiple chat responses for 9c534638-9a4b-4686-a67e-2f1f0c06fb1d\n", "Multiple chat responses for 6a78256e-6a53-4949-8db6-4be85f7382e6\n", "Multiple chat responses for 3edbddee-a4e8-4291-920f-f22eec5d8990\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:08,852 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8499c56d-b886-48e3-bdbe-47ec98e9e5e0. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for e28fafec-dd10-4df8-ab6c-9bdde862c13a\n", "Multiple chat responses for 1524105b-bd99-4058-aa9d-8a54d278e3a0\n", "Multiple chat responses for 287a07a7-3fd3-4986-a3a6-115000111ede\n", "Multiple chat responses for 5999be28-496b-423c-b100-8b52bbb526db\n", "Multiple chat responses for 6efdd44b-9a6f-46d4-ac70-af1220bee7cb\n", "Multiple chat responses for f0bbd41e-8ee1-4329-b0b5-d803560135fa\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:09,262 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6bda91e3-1c3b-42ed-a880-532fb30c43ee. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:09,318 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9b29d719-46ec-4bc6-b630-1d37dd049270. Found event names are: ['agent_request_event', 'api_http_response', 'request_metadata']\n", "2025-04-08 19:25:09,670 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e91f023f-cc17-4b46-bc63-d3cdadf143ab. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:09,795 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: cbf61e90-2ee0-429d-a565-bc7b9fe631be. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for cfb59c03-1a21-450e-8357-7fed6e0f54d3\n", "Multiple chat responses for 96be8008-95b2-4470-b3a7-a10fb30f3953\n", "Multiple chat responses for 2a034add-901e-4ea4-9aae-972c16b3f07e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:10,071 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 3c498c59-6eb9-4629-90eb-f4792bcf7f30. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,217 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: fe508e21-9b19-4f47-876c-c8a1e172bd84. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,234 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 107c2e51-3b20-4eac-82fe-b00d7b90d74d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 18730b6a-5503-4513-aa27-3fc94f71c724\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:10,380 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 210cf95f-467c-4db5-bb8c-815046a6fa1f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,618 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 58a3ea1f-b9d0-429c-9e9f-e60565e1a740. Found event names are: ['agent_request_event', 'tool_use_data']\n", "2025-04-08 19:25:10,634 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ab09e847-3335-4fb9-b30c-2081fcd9a8d4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,635 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8ec2a177-c83b-4bdb-b4af-19d32c6c817b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c625d248-22b2-47cc-864c-3c86993bb8be\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:10,641 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 581503d8-c70a-403f-817d-74fcd82f5abd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,773 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: abc347f0-53bb-48cd-bacf-5b845123e96d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:10,795 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6ae99069-9a3a-4a75-9dc5-199f833e51c3. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:11,054 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 84cd0e51-a83b-4621-af09-420793453104. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 1355a404-fa9d-4217-8d42-a8624f83d948\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:11,949 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7386c0d1-8853-4775-a356-cc47985f0d90. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,063 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a76e5935-44cd-41d4-afda-87fe25571c60. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,143 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 37ca21aa-e617-463d-b7a4-9cb0b8ddb5ef. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,444 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4b698bff-61e7-41e6-9ea3-b9d8b6a1c207. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,601 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f6c821b6-07d4-4bf1-a7bf-873ad39efab6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,618 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 066db216-edea-40b0-9a9f-681f87c86715. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,630 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 58944186-34f0-4dde-b302-610e3e29f531. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,642 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 706bc23f-b6f5-4d4c-958a-f8cb9f9e3028. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,785 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9c35f4d9-e378-41df-bb10-a7f6ddaa09ed. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:12,925 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 185754df-d006-4aae-9c48-e43aa699c27a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:13,091 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 548f6bd6-18a9-48bf-9efa-49a3a26e27c8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:13,357 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: bae01045-20a8-4356-acb1-edc6ea9c306f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 6578e1cb-f8ab-494f-8416-d815eb78822d\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:13,753 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: fa501ef9-e895-4f84-b031-a3f81d7f8a65. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:13,802 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 792a76d2-ba45-4cd9-8bca-2e921ca4f7fe. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:13,988 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: dd2a54e2-6d5d-4ccb-8a02-f565df200d5a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,523 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9671bec6-974b-4a35-82f9-b8fa8db4d72b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,534 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f21c360a-e42d-471b-ac43-5b20dc3bc4a6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,703 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6556acd5-aeef-4486-bd2e-6f42ede377a9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,719 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 55b0d3e2-c2d7-4d2c-80ec-683f16887e0f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,864 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b96c251c-b140-45a7-90d0-a8dc30905676. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:14,909 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8ba875b4-2233-44de-ad02-f68008982c3f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for d334f543-943f-4ed5-98fc-8b3e37f657b7\n", "Multiple chat responses for 37f603d8-3b4d-4182-9f43-b2dce42880b8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:15,280 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: af234f9c-d4e3-4026-8209-b65613e2535a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:15,293 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f3e3970b-a305-4841-8dc2-8f4fe66549f4. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 946e68e1-81fe-46f1-a9e9-40c720796154\n", "Multiple chat responses for 7ead6a4e-bef8-4a92-9872-d14c07afb588\n", "Multiple chat responses for 96782cd7-daba-405e-8fc2-70d4496e45db\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:15,998 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 169e2742-d184-4a8a-bd5d-693ed71f4f6a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,143 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: aad46bb2-ce7e-4eb3-a1f7-e3d2489c7bc0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,148 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 385abf7a-4459-4a94-be17-932363b6ae2d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 57f6a11c-49aa-4b4b-92d3-8749d0d7e0ca\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:16,307 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0aad5794-cfdd-46ba-bd6c-697d669ffe40. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,338 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 731401a4-d2cd-4547-a673-ebb2eb8d848b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,344 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a3622c3c-70a1-4359-bdad-34e556f276d2. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:16,461 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 72ff9ce5-58dc-471c-b80c-d025cc39ebf1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,491 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 48a1147c-e468-4f6a-a22b-2675c2ee0a48. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:16,961 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: d83c6be6-bd22-4963-af9f-b82eadb362c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,967 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c263391e-1240-4907-b0f6-6324a28a3048. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,977 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 3730a79d-0a6f-41d1-9234-a8f94a439a2b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:16,981 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 907597bc-8513-46ba-bc3b-1922c7b86918. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:17,095 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2401090f-0c96-4f7f-b29e-a16fde35cbc0. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:17,200 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8d9c339d-c6b8-42a6-aff4-e3bf5fea452f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:17,255 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 987dac0e-0bcd-46db-a73d-9fd2cb26b94d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:17,329 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5ad399b0-31eb-43b9-bca3-9a2fef2c5a2d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:17,384 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 596d4d25-7838-4d81-a886-9be03bb8397b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for f05c9859-0c82-4c51-a634-6f7129d8ac6e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:17,854 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0c6308e1-306a-4ac6-8b03-7485f2368f5c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:17,864 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e2c61535-9159-41e8-b6ae-6082f19b7196. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 51f8c526-0075-4d9f-a9b7-17dd1ed00c8a\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:18,056 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7b883464-26f5-455f-847e-cc50397863c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:18,496 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: eb0a5bc2-d060-486d-af9c-2b3edb84193e. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:18,662 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a02eafcb-98c8-4598-8733-92c6953dd0d1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,068 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c521be0d-da70-4a29-ba2a-64c2f2921072. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,179 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9c7a629e-1247-4bf9-b78f-dcc38eac3b5c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,564 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 73e164c9-02eb-4ab3-b176-ded88e4b1d1b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,567 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5c300408-d6c6-45cf-9440-fd58c1a27331. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,656 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 34aa4333-8adf-4f11-9c33-1bba3b9a4c05. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,664 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8119d355-7bc7-4526-850f-be2c05144a52. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:19,732 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 82c13f38-bf11-4a80-b597-462763e016a2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:19,798 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 66709fc6-d909-448f-b615-407ef03821d0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,017 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f384ae7b-8278-46c4-80f0-de68e127438f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,199 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f4e2a463-ec85-4896-9c6e-aff29b3477be. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,296 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 251e26f3-61a8-4d66-a534-02242e1d99c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,297 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ca16e187-9e7e-4c7d-878b-50e76ee7a1cc. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:20,303 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2caf67db-e1b7-4572-9040-cae47347b390. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'tool_use_data']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 13a09a24-eb1c-4d94-addf-c2f0c6db305e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:20,602 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 27cf50eb-3830-4a47-8f91-224afd290c55. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,615 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: efa43a60-c22a-4fe5-8ad9-75d9ba551456. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,976 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2c3aa529-b92a-4cc3-a753-7aa468e2a8c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:20,979 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ab6d2567-cf1e-4e6f-ab77-8d9f4f7dc775. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,200 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0007352c-d3cc-4277-8cd7-d50c57aacf3d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,216 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 75a06749-521e-4acf-b85e-56bf5e71185f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,298 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 91217425-b498-4362-9f06-20ab04527474. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,683 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f22e6963-cf8a-42a7-9937-d2845ea83560. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:21,839 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7c57effb-4959-4925-a027-799ed0f64db2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,906 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 797a5333-671a-438f-b4d7-05fa26ad656a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:21,961 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 96f5184f-c771-4ed6-81e7-b5c7887d5958. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:22,046 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: dacf09e2-a8fd-4d74-99d6-65e532312962. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:22,620 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 732a8a92-9dc7-4737-9522-ca2093205f91. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:22,672 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 28839353-b513-427f-8d15-83915a13cfba. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata']\n", "2025-04-08 19:25:22,711 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f553cf5d-437c-494b-a2c4-9e5ef1fa61aa. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:22,731 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7acc8528-c5a8-4a4a-b4b5-b9cda65bb6e2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:22,865 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9d408156-758f-47a5-9166-75c1c678592d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:23,061 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 68fe959a-0ade-4dd9-9892-d5507cb30c51. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:23,219 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 218cca75-2989-4738-8fab-d7e788c63f32. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 4bc03782-189a-4d59-a707-65458006114e\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:23,791 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e72ccf11-5e5f-4149-9636-291dc154be1e. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:23,822 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e917c11e-06bb-446c-a05a-9b2807a8289b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:23,892 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6b89de74-5fba-4e3b-b7b4-c9a66135f083. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:23,906 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 50905e43-4526-4973-874c-739f4ebfe216. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for d1cb819c-96d0-40fe-a167-98c6c7e63036\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:24,210 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 71bf3aec-6bdf-42a9-bd0d-d88b9cb20ec7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,331 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a133a64d-4b19-4c50-b6a7-05c1e5491b64. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,473 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: fa83c935-483e-428f-a3ae-e58a47583a9c. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata', 'tool_use_data']\n", "2025-04-08 19:25:24,480 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 45bdd45c-aab8-44b7-b633-32790e193a32. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,501 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b5c59e82-4582-41fb-b6d2-1f1ad5698ff2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,502 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8e60ab6e-6a27-4822-90fe-89c37d6942a1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,519 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7bfec68e-46cc-420d-83d3-6817ebedca2a. Found event names are: ['agent_request_event', 'api_http_response', 'request_blocked', 'request_metadata', 'tool_use_data']\n", "2025-04-08 19:25:24,835 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ff2fd4b5-36b7-4e89-8c3f-1b849e5486c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,878 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 25a8c751-7845-4245-b4ba-626c3bc37edd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:24,920 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: efe2df2b-9f4c-484e-8db7-554285c465c7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:25,175 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9daa7359-85c5-4332-8f0e-e5882805bbf4. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:25,278 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b94a2651-fab0-4963-8de8-d8e1b5dca677. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 0da9843f-3190-45b7-b787-a276901c7fd5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:25,752 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c9e259f4-f061-405a-9eb1-b632649702ca. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,006 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 31df2f65-82b9-4d10-834d-1ad058d853b0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,091 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 738b9f78-3b36-4b70-a6db-b304081fe484. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,099 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b236fd86-4769-4d2b-89a4-47c9a55cba98. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,114 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0d24d23d-f589-4e79-b02a-464bacf5ea6e. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:26,135 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8ed67ff1-58db-4911-86dd-1164ef92a2b9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,288 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9d062563-fd22-43a5-aa6d-da82fac5191b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,459 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5a3c419d-3ee4-4f81-a45c-5a21cee661a8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,470 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: db25309f-6041-401c-b199-4909fa37a84c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 3e9c8397-1176-45b4-b601-6cbae722b6ba\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:26,523 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8200a40c-6855-4d37-b40d-892a234b6336. Found event names are: ['agent_request_event', 'agent_request_event']\n", "2025-04-08 19:25:26,524 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: af894236-6a77-4a88-afd4-4486c787dac3. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,803 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ef0f54a6-a254-4c94-b5de-8d8c677a54b2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:26,879 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 76354fa7-afdc-41ae-810b-f4243cb5ed92. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:27,222 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9a611241-864d-41f1-9543-60b7e97e2b22. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:27,601 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 66a4ee9a-d319-4b17-a37e-31802aa5cfeb. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:28,144 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: d7867fb8-a3b5-4edb-bd57-285b0ebb25b4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:28,789 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f0b04b7f-c181-4477-8bad-a098f5079a7b. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:29,072 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 10144a7c-fab9-43a9-84e6-e408efc6c3a9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:29,223 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e5434133-1c6d-4a85-ae4c-99ee632ae70d. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:29,597 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a0c6d882-eded-4a28-8755-70a709ac1ab1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:30,030 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: d1a8acef-0e89-465b-8a90-2bc47a55dfab. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:30,063 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7342c83d-00e6-43bc-93f0-460a2fc7782d. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:30,187 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0f318033-681f-4911-af81-00b0a3a9d368. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 21651d18-3e18-4004-bfbf-722a45d05a6d\n", "Multiple chat responses for 92103476-e873-4683-93f1-9b8c2e1333a2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:30,470 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9c1eb9a8-ab5e-4568-b21b-51d934a8a462. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:30,589 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: bc2ccd72-0a3c-4ca9-af3a-bac8dac08abd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:30,867 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 1923b92a-1ec6-4dcc-8395-f05a0928aae4. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:30,897 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 66dff8f2-241d-4e07-bc22-a4aa911aaa17. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:31,075 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f132d716-44d3-47be-ae1b-5ccac2bb36f5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:31,235 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a99e47b6-25fd-4251-9d53-459aa06f44e9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:31,400 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 6cfba726-e794-4d91-97da-48b9f5ba5ecf. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:31,600 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9791ca99-30ab-40ea-afe7-c313f87c9072. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for f3d562af-37e6-4e24-a6b3-d2a3e3e1ac7c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:31,931 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4841b2e5-9297-4b75-bf40-7e30ad306cf8. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:32,003 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b346df57-40ea-4329-b46a-4f21ee3b62a1. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:32,491 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 09460b10-546b-44d8-aa19-5a30a59f8a6c. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 2384c237-8e6e-47f4-9730-4a97a5189c9e\n", "Multiple chat responses for 2f2583ab-5931-4645-9e63-a18b806383b5\n", "Multiple chat responses for 070187e4-1138-4a9e-be69-7266ef6f5276\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:32,661 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 38713f28-21d2-44c6-a4f8-b7641e754cc7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:32,791 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ca950b69-0a35-457b-9e4d-da6a69396e2e. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:32,838 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 3e145859-dc3c-4712-85b4-9504a88799c2. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 447de6c9-42d7-4284-a962-7e4002847c49\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:32,998 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5968e31e-ac85-4897-80cb-e0d34fec1e22. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:33,205 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: bd59ae3e-6f21-47aa-a309-ae3b67d9dd68. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 71d80112-9751-4d29-8780-6465bc5a47dc\n", "Multiple chat responses for 6f8cdf85-9f39-434a-abcf-8f701212a679\n", "Multiple chat responses for 3bb973e1-e389-409a-81e5-14e76f091901\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:33,316 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 57976ccd-94b8-4d7f-9c2f-ed0166d7480f. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for a21e087a-f630-4428-a931-ab05eb979313\n", "Multiple chat responses for 0302793b-ffa0-4d9c-af5b-d59f772ad9f0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:33,704 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 53196645-326b-4aa0-8e40-825a17bbeddf. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 442713e8-5a68-46b8-865b-840fe553bb5b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:34,294 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 66ea965a-25ed-4314-a64a-1518a3610588. Found event names are: ['agent_request_event', 'tool_use_data']\n", "2025-04-08 19:25:34,605 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 66f6009e-75fa-409b-81a7-ec5f14890552. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 17b93e46-bcbf-4bae-950e-75c17401d129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:34,997 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 99191566-b67c-45d7-bcb5-6787df267370. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 12d3f21e-20f1-42ef-8bb9-d35e838abaea\n", "Multiple chat responses for 80c2d2ea-5c32-44ea-9e62-a707e96aa509\n", "Multiple chat responses for afdf007a-dbbe-4112-bedc-d89d05021ba8\n", "Multiple chat responses for ce52333b-63b2-4af5-ab0b-e2baa4216049\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:35,071 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5309947e-5ba9-4a33-97d5-bba5154767a5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:35,072 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: baf69f47-73ad-46ca-9ccb-c5aa30ec5b39. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:35,331 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: cb46e60e-ae4c-4cc6-b3b3-b1e012b65133. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:35,503 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 5f341235-2d2b-4337-b021-c641b1e40be5. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 71c481e3-bd1b-4dea-97c6-c31437c44548\n", "Multiple chat responses for e063615d-5834-462d-b8a8-84f5a3335a5f\n", "Multiple chat responses for 5120e9a8-fc81-47a6-8c6a-3f8e8722ffc7\n", "Multiple chat responses for 14db1a74-5853-4691-85c1-334267ab48c4\n", "Multiple chat responses for 8ac060bb-b49b-4ee0-8c2b-09ca64c23df2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:36,437 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: cf6826d6-e496-4df8-8617-03e1b6fb8aaf. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 8f368aa7-82f8-457f-b8e8-1efb934d2e22\n", "Multiple chat responses for d7177a16-5c36-42ae-a879-385e3478b16f\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:37,106 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 9d3cb7ba-3fb1-4c05-81b1-5f795e41fcae. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:37,153 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 813f5e9a-5407-49cc-ba77-b3403cfbfb26. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:37,208 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: fab95645-20f1-4989-871a-cb6fd44869ef. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:37,282 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: bf854056-a476-43c9-9007-2557ec9deeb1. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:37,284 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b13fefcd-42fb-40f1-b209-dd960d8c96dd. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 09d506c2-49f1-4051-94da-2fdaefbde0b4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:37,487 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 23a20084-06f7-4f4f-9fe7-e63a05c422d4. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c0dc711e-faf2-4e84-a573-906ccb56f2de\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:37,920 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 82ae7609-60d6-4896-9ac1-8db840574e77. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:38,014 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: cddd925d-539b-4d9b-888e-75016937b2c9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:38,053 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 678fceb5-0d19-4ad4-b65f-4ae12ad92e98. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 790f7961-097b-4b86-a806-f02167b58b96\n", "Multiple chat responses for f3dace02-d5c8-4a19-8884-dff4699e1e8a\n", "Multiple chat responses for 08a9d195-d937-47d1-a3ae-4bc46710302d\n", "Multiple chat responses for 72b573bd-8b27-4dd5-9caf-f2d85fd23643\n", "Multiple chat responses for 899964f2-5c10-4f8b-8aee-e2fc09b065f2\n", "Multiple chat responses for fd47382a-da02-4be5-8e58-40e5935ab955\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:38,193 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f0959b91-b499-424b-9ec4-b21f8389840c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:38,195 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e342786a-7a25-4c97-b085-a83d7c136b4b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 7846948a-29f5-40e0-833a-5eb6221a0b08\n", "Multiple chat responses for b9bac3a6-4595-4605-b7f4-e130e4ffd920\n", "Multiple chat responses for 5881ebb7-7062-4ee4-a937-76311c148123\n", "Multiple chat responses for 793c2faf-aabc-408b-868c-2781a31994db\n", "Multiple chat responses for 13f6cb17-4f96-4bbc-a03b-4e2ab8f43148\n", "Multiple chat responses for ed30877a-1f5f-4b2e-89b5-886b7843cbf8\n", "Multiple chat responses for 0e1abfbd-d462-4a6b-9a96-0ecb55c617a5\n", "Multiple chat responses for 903ebef6-a57e-45a6-9aef-0c8dcae9db88\n", "Multiple chat responses for f2d62bfe-ee30-492f-a07a-80d0e49a69fa\n", "Multiple chat responses for b092c716-1ec3-467e-bd81-32d6e98fad9c\n", "Multiple chat responses for 10554cf8-6a0b-4909-b840-c715f4ca23ea\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:38,725 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 28754fa3-c927-4244-8792-3c62947df077. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 636c3bfc-79ee-4e25-b5f6-885a44436aa7\n", "Multiple chat responses for 1e4d0ff8-f7bc-4d41-93bf-2c2e98e5684c\n", "Multiple chat responses for fbd1ddbd-2026-4b32-9d7e-0b7f21940b32\n", "Multiple chat responses for 0caf5ca6-9bef-4903-8f7e-968f5169a0cc\n", "Multiple chat responses for 424dcaf4-db0c-4829-9445-ab61b3ec36c4\n", "Multiple chat responses for aa5b8b6f-f82d-48a2-a622-f177f076215f\n", "Multiple chat responses for ed6035af-5ca3-462f-8bf8-c75837b123dd\n", "Multiple chat responses for e68d24cb-9d77-48be-83ca-7cc5b7e89223\n", "Multiple chat responses for c15184bb-b531-4e1f-a4d2-cdf4b928313b\n", "Multiple chat responses for 0890d468-d93a-4878-94fc-299c7fe581dd\n", "Multiple chat responses for 08c4bb37-5eab-4674-b3b1-66b29d307777\n", "Multiple chat responses for 877e5752-c5d9-4e3e-8e49-c254695bf5bb\n", "Multiple chat responses for 6a6e0877-7670-43fc-a4ec-6727fdd8a2b0\n", "Multiple chat responses for 9855b641-646b-49c5-a402-c80408b32c0b\n", "Multiple chat responses for 6f05a5b2-b701-4bb2-9e1c-a0d12e5bc6f6\n", "Multiple chat responses for 0c269029-d261-4dfc-898e-146eb06624bd\n", "Multiple chat responses for 7a859bfc-2dc6-493b-a99c-e68e27199096\n", "Multiple chat responses for 82f996a0-951a-4566-b84a-8f3c118935b8\n", "Multiple chat responses for 4cb834bc-d657-4003-b05d-1950b6f72a6e\n", "Multiple chat responses for c08b449e-6e6f-4d2a-b149-caf88f9e843d\n", "Multiple chat responses for 2ca4bf0b-2159-4a9f-8de7-91e540d4831c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:39,118 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 682c9333-aa9f-4fd9-aa8f-77e0dfad9937. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 843241b6-e23e-4e4f-bbf7-9d8eefc4a3a1\n", "Multiple chat responses for 32bd2531-9d7d-4ba5-89cd-24e1ca7dacf2\n", "Multiple chat responses for c23974d4-286b-4af8-bbee-45c9845ea32a\n", "Multiple chat responses for 96366f8f-0f9d-4d68-944e-1ec8a2079163\n", "Multiple chat responses for 038ba0b7-8018-4886-8836-f336cdfcc96d\n", "Multiple chat responses for 4a465dea-d1ca-4401-aa95-de3b749c9bcd\n", "Multiple chat responses for 64ee1392-6293-42d0-87de-cfc4e258a07e\n", "Multiple chat responses for c6ef3828-7c7f-4e93-9259-60f8d2df88cf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:39,377 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 985144dc-0a44-458e-9240-9a3a7015a9bc. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c4e1e31d-8b53-4e65-9e3a-acafdd904e38\n", "Multiple chat responses for 0fb6c2ed-3d22-4cc6-9490-375f3edeb8aa\n", "Multiple chat responses for 6c1cf9d5-ca32-47fd-8c9c-2742023a869e\n", "Multiple chat responses for 19ae16e1-669f-4b09-b0e0-eaf8e847405e\n", "Multiple chat responses for a563785a-c0e3-47cd-b0f4-e958893126d0\n", "Multiple chat responses for a25bbdba-d093-45f4-a5ee-eba939db313c\n", "Multiple chat responses for e5231bf4-f25a-479b-b952-d9ed53016c97\n", "Multiple chat responses for cc1cd08f-0671-4b55-84c4-89978fb570e0\n", "Multiple chat responses for 65a2aef5-c233-411e-8f2d-dbf75c9d136c\n", "Multiple chat responses for d9ee823e-2a86-43ba-b8f9-0029017504e1\n", "Multiple chat responses for 9095cf73-07d9-42bd-955c-b096031eac9c\n", "Multiple chat responses for d574fbb0-c34b-4db3-a447-bb62f558c60c\n", "Multiple chat responses for f0425d27-1ae1-489f-b350-f3cd1c813fb2\n", "Multiple chat responses for 61e0a8cb-7924-4b27-8676-8539b8518ec2\n", "Multiple chat responses for 36009652-515f-42a8-b55b-78ee6279812a\n", "Multiple chat responses for 865700df-1fd5-435c-9914-8558fcc20c6a\n", "Multiple chat responses for 1ac272ee-5ccf-45b7-b767-ee868c09ca6b\n", "Multiple chat responses for 35d61976-eeaf-4222-947d-f8d887f8d8e4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:39,993 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: d62dfe79-6eb7-42ce-971e-872256b241f2. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 85a371a9-51bb-4b8f-8732-848546ee7228\n", "Multiple chat responses for adb77b55-a6ea-484e-87bf-da3fd98550ee\n", "Multiple chat responses for 71a74465-76eb-4d8e-86ce-884ffd8f8ea5\n", "Multiple chat responses for cbd1dde1-cc78-4a0e-bf84-fbbb3ac66417\n", "Multiple chat responses for 8e907c2d-cfe1-44be-aa8c-afbdd0397fc2\n", "Multiple chat responses for d28cc29c-8edb-450d-ac60-82b5cac98682\n", "Multiple chat responses for 0605c500-2b3a-4b75-9fff-97d5cd9ad183\n", "Multiple chat responses for 97e8e0d9-572a-48b3-b5b8-2f21d313f998\n", "Multiple chat responses for 9a0f55c6-62d3-47e4-bfd1-6e1e92a17540\n", "Multiple chat responses for e0507fc2-ce3e-4c31-bb34-91d5edeb28d4\n", "Multiple chat responses for c18da499-0a2a-4f9b-ba23-395ab94174e3\n", "Multiple chat responses for 09d8b672-bbbf-4c12-8a9a-8b7af810acea\n", "Multiple chat responses for 1fd0488f-2143-4e74-9a1c-e9f46170e499\n", "Multiple chat responses for 923eed6d-0ef6-492b-b9b4-b20a81189b2f\n", "Multiple chat responses for 53b0b890-790f-4c49-814b-85c525a49aa3\n", "Multiple chat responses for b6d90c3d-3376-4fdc-8c65-9ba0593b1c31\n", "Multiple chat responses for 5c705c30-735a-4761-93c3-f60edce28b5a\n", "Multiple chat responses for 2a0b1d17-d38f-49a5-8de1-cf78d1c53dfd\n", "Multiple chat responses for 41ef0569-f83d-4a26-894f-b6cd055f5c95\n", "Multiple chat responses for 4d03e2fa-e869-4fa3-ace3-807e009d36ec\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:40,513 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8aadae46-2247-4bb5-8689-51bc65b0a8bf. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:40,558 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4c032038-f3ae-424d-866e-90f3e3b4db53. Found event names are: ['agent_request_event', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata', 'request_metadata']\n", "2025-04-08 19:25:40,677 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 8c21c09b-cd26-425f-a2db-b09b67429af2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:40,824 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 7e2b002f-3e40-49ff-b2e4-23ce9555f208. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 66cb0bd9-a1bd-4d9f-82d8-135494e05c90\n", "Multiple chat responses for 0c889943-3995-4d62-906f-61a96f16e98e\n", "Multiple chat responses for 1452ce67-a0d9-4698-802f-95ec8a35c6d9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:41,668 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e12c6f63-76da-4046-9c3e-2126cfd17f3d. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for fd0fcddb-f3ff-47ae-adde-1cc8dfd49d69\n", "Multiple chat responses for fb918c3b-f32b-4052-b84d-5cff77944bcd\n", "Multiple chat responses for 8e629be2-68c3-4963-81dc-f6ee69d41d5b\n", "Multiple chat responses for de26547e-787d-4f4a-9428-56308c20abbc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:41,805 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 992cfbbf-a8d9-4104-82cc-5571893301bd. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:41,806 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: f630dd91-d06e-4743-81fb-9331f1a8fa84. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:41,807 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: b67b9d9a-4a95-4e04-9421-847afe485232. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:41,891 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: fab22e61-6fe1-42c7-a31b-0cdb6716e0c2. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:41,943 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4d05e776-3dce-49d3-a070-cc71d5fe6582. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 173e8df8-c6f3-4192-8816-7d980c6aa814\n", "Multiple chat responses for 623c2eeb-b6c7-4019-b2e3-580c2e4ce7cd\n", "Multiple chat responses for 1188b273-3785-4768-be27-6d682fb69949\n", "Multiple chat responses for 27856bf8-4100-4970-8df2-88add6a7ef8e\n", "Multiple chat responses for 742e50ce-c713-4f68-8a00-2c2856326bbb\n", "Multiple chat responses for 86fbd205-ddb4-44a9-b47e-aab0ec2bd90e\n", "Multiple chat responses for 1896c17b-a404-413b-986b-bf7cd5ab82f8\n", "Multiple chat responses for f06955d9-b4ba-41d1-a25a-17cae898f27c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:42,145 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 201a0661-8263-4b44-9bfe-183c9cf8556b. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 6ddafd0b-7268-40e7-9c81-9696d5c9c4ce\n", "Multiple chat responses for 22ede6df-8fe9-4f28-81b1-917c26d77430\n", "Multiple chat responses for cf3d04ae-19d0-4adc-9dbd-70eeb6d53717\n", "Multiple chat responses for aa1e1da8-b874-48b1-88ac-3715ca20efae\n", "Multiple chat responses for 50bb59ba-a041-4980-945e-09659784f11c\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:42,329 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: c8b051a4-dfbd-4d44-aa27-f15bc837ae03. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:42,542 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4ae101b6-e452-4310-80a3-737d22e59a71. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for c037dbe6-8487-4f75-a50a-b96e549abd31\n", "Multiple chat responses for 1b7b9f20-c8c1-4bfe-b9f3-39dae0aa9b69\n", "Multiple chat responses for de160bcc-4a00-4d52-9473-9d2553d621fa\n", "Multiple chat responses for 752d5a6b-135f-47ee-bfd0-3e0cf31c312a\n", "Multiple chat responses for 1eb990ac-d64b-4e2f-ad8a-84f06185a627\n", "Multiple chat responses for 6580ff3b-3726-4d7a-8f37-fe34603bc92f\n", "Multiple chat responses for 6941a069-78c0-464d-b87e-e2df4d5a57ac\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:42,580 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e4d2d822-4426-4241-9468-5595bffc3b66. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:42,657 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: ed698236-deb6-46fc-a845-3a0216441624. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 7b74989d-eb9f-44b0-b714-47e7c312aa4c\n", "Multiple chat responses for 3769072d-788d-4a43-9292-ace94be655c9\n", "Multiple chat responses for 7cf5a277-7be4-44d8-b3a5-5300e0b07c18\n", "Multiple chat responses for 27b2a498-631a-4804-a8bb-ed8dc994cfb0\n", "Multiple chat responses for 61d43cc6-9971-4310-966b-3ce4fbd4a867\n", "Multiple chat responses for 8950d890-e80f-4713-ae36-5bc241a5ae81\n", "Multiple chat responses for 75755a31-bade-4d79-a0a3-c8704e13d2c2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:43,239 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 776cb3e3-8740-47e8-93aa-ef5fd51067b9. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:43,242 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2cc6cc45-54a1-45e1-95d6-ae87fb0dcac7. Found event names are: ['agent_request_event', 'tool_use_data']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 9aef42a9-b49e-4529-85c1-961d93330cbf\n", "Multiple chat responses for dc1c091a-408b-4ce4-a653-950be6468a25\n", "Multiple chat responses for 3f1ff82c-7efb-4ad0-92ce-593b7285ce03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:43,771 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: d22d1481-d3a1-4aa6-86bb-d2322ff1abe0. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:43,792 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 008bb047-fda6-4eb2-b6fa-c09ea68dfb90. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for ec86d9ff-74cd-4ec1-ad81-5e619d3b04aa\n", "Multiple chat responses for 505baeed-8ee7-4b79-a1b4-5f4cb2eb72b8\n", "Multiple chat responses for da8e8185-5478-466e-8086-72ddc287172c\n", "Multiple chat responses for 8cef05ad-f2f6-4bed-aec3-5a8cc936ee86\n", "Multiple chat responses for 11c41df2-02fc-4dcb-99e3-6038288b37a0\n", "Multiple chat responses for ee690590-a403-44d4-91f6-fc20601f1f04\n", "Multiple chat responses for d00b0fb1-320b-43bb-a100-f46cebb5a928\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:43,944 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 4b9e38ae-9e36-40da-87fe-974577926ef6. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,028 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 54920c92-605a-4d50-aedb-dcb4b0076243. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 8bd97a67-7198-4667-86e3-eb49a860a36c\n", "Multiple chat responses for b9c6bf64-ef1f-4c12-bd1d-aca0d5a37b8b\n", "Multiple chat responses for 8ff64b7d-a353-42a7-9b97-93bd2c8bac30\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:44,036 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 93f5ec2b-573d-4236-9c75-b46919aaa059. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,205 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 46d1edad-0ed7-4601-b154-7ce1d5eab091. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,214 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 60573ded-d796-4453-a05f-40138176fef7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,316 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: da243818-e73c-4c64-9dbd-e1143d877a51. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 0a6d2f06-4ffc-4c5d-b492-4831ec6c1399\n", "Multiple chat responses for 2e67ad11-7ef7-4c55-8633-82784e3c335f\n", "Multiple chat responses for 6954cf7d-7b1e-4745-ad2c-ccf0c7d3c2c4\n", "Multiple chat responses for 654c037f-fbc8-4c32-82cf-cde30479b882\n", "Multiple chat responses for 56b17265-6683-42ba-8216-4f587186cace\n", "Multiple chat responses for 100c469c-f16c-4708-b05f-06579efed90d\n", "Multiple chat responses for b41df652-10b4-4049-965d-a2b62990cff0\n", "Multiple chat responses for b21a0f90-c57a-4606-9029-c518eb9ef7eb\n", "Multiple chat responses for 09f6165b-062a-43c9-bb5b-e02de937e3eb\n", "Multiple chat responses for 2916b370-20d9-4711-bc32-5cfb228e5cf9\n", "Multiple chat responses for 3c07fcde-b335-42d1-967a-726af4b82eee\n", "Multiple chat responses for cf7c720c-8896-49e7-9368-ef400e653653\n", "Multiple chat responses for d7c65f30-49fc-4ac6-bab1-494a2db43ec4\n", "Multiple chat responses for fa07cfdc-9a4e-4096-8f63-b5916a98cd1b\n", "Multiple chat responses for 66ecf0d9-48e7-4dd6-9e41-e8b83a4f1281\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:44,629 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: e379ddd0-0148-45a9-8f79-1fa28d84539a. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,749 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 0aea3ebe-9cf5-4434-803f-f2726799c25f. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:44,848 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: aeda7fa4-eb89-4006-90a2-8694c818c784. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for 56da3004-6a18-4403-9cd6-41b2033b4301\n", "Multiple chat responses for 85807c7b-7815-42ca-9b06-55a0703e824e\n", "Multiple chat responses for 79f81695-77d4-4adc-a28a-a2a49d39d727\n", "Multiple chat responses for 683fa992-da2f-4a7c-bfdd-100eaecfb14a\n", "Multiple chat responses for e6d1a338-bcf0-4421-a9d3-fbdf9542e90a\n", "Multiple chat responses for 0cea79a7-ecb4-4493-991e-f48bc0168e28\n", "Multiple chat responses for 9f3010cd-cbba-402e-ae8c-1274e290f257\n", "Multiple chat responses for d35c7fce-33d2-42cf-a735-7722133f5b03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:45,344 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2ec00512-e5c6-424d-87f2-5d05fb554e62. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:45,346 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 3f41b9f0-2147-4f6b-bd56-d3cf124f809c. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:45,354 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 61b63dbe-033f-4a3e-9f0b-6e45b15e8abb. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:45,412 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: a61fc773-2d40-44fd-b84c-2117c4bb1c14. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for bba80814-f669-4955-a849-9dd37b6d63ab\n", "Multiple chat responses for feaa35c6-8af0-43c4-bceb-4cfb28ca1e16\n", "Multiple chat responses for 1b7d3e41-dddf-47cc-9278-c7df558c5c1f\n", "Multiple chat responses for 823aeff5-8fee-451d-8ca7-5e6e47ca5afe\n", "Multiple chat responses for ccda37d7-8494-43ef-ad6c-3f1f7126119a\n", "Multiple chat responses for fa4ffbb9-7410-46c6-b9fd-e13329bec778\n", "Multiple chat responses for aa4c6c4a-01cd-42c5-949a-08c8a1e22a8b\n", "Multiple chat responses for 0476ee2b-3fdc-40f0-a8ca-3e8cc4c71927\n", "Multiple chat responses for dcec3c11-0c95-4cea-b6a1-69589b01c53b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-08 19:25:45,512 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 405420fa-44be-468d-9572-050f44d5f1a7. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:45,848 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 984293f0-fee2-42c8-be94-582d6710a5c5. Found event names are: ['agent_request_event']\n", "2025-04-08 19:25:45,860 - base.datasets.gcs_client - WARNING - No events with names in frozenset({'chat_host_response'}) for request ID: 2e8529b5-dce4-4546-8b0c-ae256c2486c0. Found event names are: ['agent_request_event']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Multiple chat responses for f10b2932-68f4-47df-988b-103709ec4ef2\n", "Found 7703 chat requests\n", "Found 7703 common request ids\n"]}], "source": ["from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "\n", "import services.chat_host.chat_pb2 as chat_pb2\n", "\n", "request_fetcher = GCSRequestInsightFetcher.from_tenant(TENANT)\n", "\n", "\n", "def extract_chat_request(request) -> chat_pb2.ChatRequest | None:\n", "    requests = []\n", "    for event in request.events:\n", "        if event.HasField(\"chat_host_request\"):\n", "            requests.append(event.chat_host_request.request)\n", "    if len(requests) > 1:\n", "        print(f\"Multiple chat requests for {request.request_id}\")\n", "        return None\n", "    if len(requests) == 0:\n", "        return None\n", "    return requests[0]\n", "\n", "\n", "def extract_chat_response(request) -> chat_pb2.ChatResponse | None:\n", "    responses = []\n", "    for event in request.events:\n", "        if event.Has<PERSON>ield(\"chat_host_response\"):\n", "            responses.append(event.chat_host_response.response)\n", "    if len(responses) > 1:\n", "        print(f\"Multiple chat responses for {request.request_id}\")\n", "        return None\n", "    if len(responses) == 0:\n", "        return None\n", "    return responses[0]\n", "\n", "\n", "chat_requests = {}\n", "for request in request_fetcher.get_requests(\n", "    request_ids=request_ids, request_event_names=frozenset({\"chat_host_request\"})\n", "):\n", "    chat_request = extract_chat_request(request)\n", "    if chat_request is not None:\n", "        chat_requests[request.request_id] = chat_request\n", "print(f\"Found {len(chat_requests)} chat requests\")\n", "\n", "chat_responses = {}\n", "for response in request_fetcher.get_requests(\n", "    request_ids=request_ids, request_event_names=frozenset({\"chat_host_response\"})\n", "):\n", "    chat_response = extract_chat_response(response)\n", "    if chat_response is not None:\n", "        chat_responses[response.request_id] = chat_response\n", "print(f\"Found {len(chat_responses)} chat requests\")\n", "\n", "\n", "common_request_ids = set(chat_requests).intersection(set(chat_responses))\n", "print(f\"Found {len(common_request_ids)} common request ids\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import pandas as pd\n", "\n", "from experimental.yury.agent.common import (\n", "    AgentRound,\n", "    extract_agent_rounds,\n", ")\n", "\n", "agent_rounds: list[AgentRound] = []\n", "metadata = Counter()\n", "\n", "for request_id in common_request_ids:\n", "    current_agent_rounds, current_metadata = extract_agent_rounds(\n", "        request_id,\n", "        chat_requests[request_id],\n", "        chat_responses[request_id],\n", "        verbose=False,\n", "    )\n", "    agent_rounds.extend(current_agent_rounds)\n", "    metadata.update(current_metadata)\n", "\n", "print(f\"Found {len(agent_rounds)} agent rounds\")\n", "for k, v in metadata.items():\n", "    print(f\"-- {k}: {v}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["29264\n", "has_any_tool_calls                0.755365\n", "all_tools_are_whitelisted         0.559971\n", "str-replace-editor-str_replace    0.545243\n", "str-replace-editor-view           0.484589\n", "launch-process                    0.373223\n", "round_is_qa                       0.244635\n", "save-file                         0.234999\n", "codebase-retrieval                0.163819\n", "read-process                      0.146426\n", "open-browser                      0.121549\n", "kill-process                      0.109042\n", "list-processes                    0.036700\n", "remove-files                      0.025936\n", "web-search                        0.017769\n", "web-fetch                         0.013703\n", "remember                          0.010081\n", "write-process                     0.009192\n", "write_file_filesystem             0.002836\n", "github-api                        0.002734\n", "read_file_filesystem              0.002460\n", "dtype: float64\n"]}], "source": ["from collections import defaultdict\n", "\n", "WHITELISTED_TOOLS = {\n", "    \"str-replace-editor-str_replace\",\n", "    \"str-replace-editor-view\",\n", "    \"save-file\",\n", "    \"codebase-retrieval\",\n", "}\n", "\n", "stats = []\n", "for agent_round in agent_rounds:\n", "    stats_per_round = defaultdict(int)\n", "    all_tools_are_whitelisted = True\n", "    for agent_turn in agent_round.agent_turns:\n", "        if agent_turn.tool_use is not None:\n", "            tool_name = agent_turn.tool_use.name\n", "            if tool_name == \"str-replace-editor\":\n", "                counter_key = (\n", "                    f\"str-replace-editor-{agent_turn.tool_use.input['command']}\"\n", "                )\n", "            else:\n", "                counter_key = tool_name\n", "            stats_per_round[counter_key] = max(stats_per_round[counter_key], 1)\n", "            if counter_key not in WHITELISTED_TOOLS:\n", "                all_tools_are_whitelisted = False\n", "\n", "    last_requst_id = agent_round.agent_turns[-1].tool_use_request_id\n", "    stats_per_round = dict(stats_per_round)\n", "    stats_per_round.update(\n", "        {\n", "            \"has_any_tool_calls\": len(stats_per_round) > 0,\n", "            \"last_requst_id\": last_requst_id,\n", "            \"user_message_request_id\": agent_round.user_message_request_id,\n", "            \"round_is_qa\": agent_round.user_message_request_id == last_requst_id,\n", "            \"all_tools_are_whitelisted\": all_tools_are_whitelisted,\n", "        }\n", "    )\n", "    stats.append(stats_per_round)\n", "\n", "\n", "stats = pd.DataFrame(stats)\n", "stats = stats.fillna(0)\n", "print(len(stats))\n", "mean_values = stats.mean(numeric_only=True).sort_values(ascending=False)\n", "top_20 = mean_values.head(20)\n", "print(top_20)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["22105\n", "has_any_tool_calls                        1.000000\n", "str-replace-editor-str_replace            0.721828\n", "str-replace-editor-view                   0.641529\n", "launch-process                            0.494096\n", "all_tools_are_whitelisted                 0.417462\n", "save-file                                 0.311106\n", "codebase-retrieval                        0.216874\n", "read-process                              0.193848\n", "open-browser                              0.160914\n", "kill-process                              0.144356\n", "list-processes                            0.048586\n", "remove-files                              0.034336\n", "web-search                                0.023524\n", "web-fetch                                 0.018141\n", "remember                                  0.013345\n", "write-process                             0.012169\n", "write_file_filesystem                     0.003755\n", "github-api                                0.003619\n", "read_file_filesystem                      0.003257\n", "sequentialthinking_sequential-thinking    0.002624\n", "dtype: float64\n"]}], "source": ["print(len(stats[stats[\"has_any_tool_calls\"]]))\n", "mean_values = (\n", "    stats[stats[\"has_any_tool_calls\"]]\n", "    .mean(numeric_only=True)\n", "    .sort_values(ascending=False)\n", ")\n", "top_20 = mean_values.head(20)\n", "print(top_20)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["web-search                       0.0\n", "launch-process                   0.0\n", "str-replace-editor-view          0.0\n", "has_any_tool_calls               0.0\n", "round_is_qa                      1.0\n", "                                ... \n", "project1_write_file_uv           0.0\n", "confluence                       0.0\n", "run_browser_agent_browser-use    0.0\n", "browser_close_playwright         0.0\n", "list_processes                   0.0\n", "Length: 222, dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["stats[stats[\"has_any_tool_calls\"] == 0].mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>launch-process</th>\n", "      <th>has_any_tool_calls</th>\n", "      <th>last_requst_id</th>\n", "      <th>user_message_request_id</th>\n", "      <th>codebase-retrieval</th>\n", "      <th>str-replace-editor-view</th>\n", "      <th>str-replace-editor-str_replace</th>\n", "      <th>save-file</th>\n", "      <th>read-process</th>\n", "      <th>kill-process</th>\n", "      <th>...</th>\n", "      <th>bookapp_query_uv</th>\n", "      <th>sequential_thinking_uv</th>\n", "      <th>bookapp_list_tables_uv</th>\n", "      <th>remember</th>\n", "      <th>confluence_get_page_mcp-atlassian-ideagen-confluence</th>\n", "      <th>confluence_search_mcp-atlassian-ideagen-confluence</th>\n", "      <th>get_scene_info_blender</th>\n", "      <th>get_hyper3d_status_blender</th>\n", "      <th>execute_blender_code_blender</th>\n", "      <th>get_object_info_blender</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>4622c278-2ff9-4017-96d1-70467d7c52e1</td>\n", "      <td>4622c278-2ff9-4017-96d1-70467d7c52e1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>c3cfb76c-20f0-4bd6-8161-80a36a7337c2</td>\n", "      <td>c3cfb76c-20f0-4bd6-8161-80a36a7337c2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>58cece28-88bc-4cab-9134-3ede7e2e9d6a</td>\n", "      <td>58cece28-88bc-4cab-9134-3ede7e2e9d6a</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>60ea962f-36ca-423a-872a-f1e0578b1615</td>\n", "      <td>60ea962f-36ca-423a-872a-f1e0578b1615</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>5b7db5cf-f14c-45a4-b77b-dcb6be33f292</td>\n", "      <td>5b7db5cf-f14c-45a4-b77b-dcb6be33f292</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>528</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>f4bac7a8-5a93-44e0-be99-02eb5159cc11</td>\n", "      <td>f4bac7a8-5a93-44e0-be99-02eb5159cc11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>0a97be18-f08a-4772-945b-591e04073681</td>\n", "      <td>0a97be18-f08a-4772-945b-591e04073681</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>532</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>81c6368d-b8d3-4437-84da-833f98f3c0d1</td>\n", "      <td>81c6368d-b8d3-4437-84da-833f98f3c0d1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>533</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>34223e5d-d6c3-4564-96fc-bbfa4457275f</td>\n", "      <td>34223e5d-d6c3-4564-96fc-bbfa4457275f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541</th>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>34260f9a-ff6a-4933-9852-c6d97c1889b9</td>\n", "      <td>34260f9a-ff6a-4933-9852-c6d97c1889b9</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>141 rows × 26 columns</p>\n", "</div>"], "text/plain": ["     launch-process  has_any_tool_calls                        last_requst_id  \\\n", "6               0.0               False  4622c278-2ff9-4017-96d1-70467d7c52e1   \n", "14              0.0               False  c3cfb76c-20f0-4bd6-8161-80a36a7337c2   \n", "18              0.0               False  58cece28-88bc-4cab-9134-3ede7e2e9d6a   \n", "19              0.0               False  60ea962f-36ca-423a-872a-f1e0578b1615   \n", "25              0.0               False  5b7db5cf-f14c-45a4-b77b-dcb6be33f292   \n", "..              ...                 ...                                   ...   \n", "528             0.0               False  f4bac7a8-5a93-44e0-be99-02eb5159cc11   \n", "530             0.0               False  0a97be18-f08a-4772-945b-591e04073681   \n", "532             0.0               False  81c6368d-b8d3-4437-84da-833f98f3c0d1   \n", "533             0.0               False  34223e5d-d6c3-4564-96fc-bbfa4457275f   \n", "541             0.0               False  34260f9a-ff6a-4933-9852-c6d97c1889b9   \n", "\n", "                  user_message_request_id  codebase-retrieval  \\\n", "6    4622c278-2ff9-4017-96d1-70467d7c52e1                 0.0   \n", "14   c3cfb76c-20f0-4bd6-8161-80a36a7337c2                 0.0   \n", "18   58cece28-88bc-4cab-9134-3ede7e2e9d6a                 0.0   \n", "19   60ea962f-36ca-423a-872a-f1e0578b1615                 0.0   \n", "25   5b7db5cf-f14c-45a4-b77b-dcb6be33f292                 0.0   \n", "..                                    ...                 ...   \n", "528  f4bac7a8-5a93-44e0-be99-02eb5159cc11                 0.0   \n", "530  0a97be18-f08a-4772-945b-591e04073681                 0.0   \n", "532  81c6368d-b8d3-4437-84da-833f98f3c0d1                 0.0   \n", "533  34223e5d-d6c3-4564-96fc-bbfa4457275f                 0.0   \n", "541  34260f9a-ff6a-4933-9852-c6d97c1889b9                 0.0   \n", "\n", "     str-replace-editor-view  str-replace-editor-str_replace  save-file  \\\n", "6                        0.0                             0.0        0.0   \n", "14                       0.0                             0.0        0.0   \n", "18                       0.0                             0.0        0.0   \n", "19                       0.0                             0.0        0.0   \n", "25                       0.0                             0.0        0.0   \n", "..                       ...                             ...        ...   \n", "528                      0.0                             0.0        0.0   \n", "530                      0.0                             0.0        0.0   \n", "532                      0.0                             0.0        0.0   \n", "533                      0.0                             0.0        0.0   \n", "541                      0.0                             0.0        0.0   \n", "\n", "     read-process  kill-process  ...  bookapp_query_uv  \\\n", "6             0.0           0.0  ...               0.0   \n", "14            0.0           0.0  ...               0.0   \n", "18            0.0           0.0  ...               0.0   \n", "19            0.0           0.0  ...               0.0   \n", "25            0.0           0.0  ...               0.0   \n", "..            ...           ...  ...               ...   \n", "528           0.0           0.0  ...               0.0   \n", "530           0.0           0.0  ...               0.0   \n", "532           0.0           0.0  ...               0.0   \n", "533           0.0           0.0  ...               0.0   \n", "541           0.0           0.0  ...               0.0   \n", "\n", "     sequential_thinking_uv  bookapp_list_tables_uv  remember  \\\n", "6                       0.0                     0.0       0.0   \n", "14                      0.0                     0.0       0.0   \n", "18                      0.0                     0.0       0.0   \n", "19                      0.0                     0.0       0.0   \n", "25                      0.0                     0.0       0.0   \n", "..                      ...                     ...       ...   \n", "528                     0.0                     0.0       0.0   \n", "530                     0.0                     0.0       0.0   \n", "532                     0.0                     0.0       0.0   \n", "533                     0.0                     0.0       0.0   \n", "541                     0.0                     0.0       0.0   \n", "\n", "     confluence_get_page_mcp-atlassian-ideagen-confluence  \\\n", "6                                                  0.0      \n", "14                                                 0.0      \n", "18                                                 0.0      \n", "19                                                 0.0      \n", "25                                                 0.0      \n", "..                                                 ...      \n", "528                                                0.0      \n", "530                                                0.0      \n", "532                                                0.0      \n", "533                                                0.0      \n", "541                                                0.0      \n", "\n", "     confluence_search_mcp-atlassian-ideagen-confluence  \\\n", "6                                                  0.0    \n", "14                                                 0.0    \n", "18                                                 0.0    \n", "19                                                 0.0    \n", "25                                                 0.0    \n", "..                                                 ...    \n", "528                                                0.0    \n", "530                                                0.0    \n", "532                                                0.0    \n", "533                                                0.0    \n", "541                                                0.0    \n", "\n", "     get_scene_info_blender  get_hyper3d_status_blender  \\\n", "6                       0.0                         0.0   \n", "14                      0.0                         0.0   \n", "18                      0.0                         0.0   \n", "19                      0.0                         0.0   \n", "25                      0.0                         0.0   \n", "..                      ...                         ...   \n", "528                     0.0                         0.0   \n", "530                     0.0                         0.0   \n", "532                     0.0                         0.0   \n", "533                     0.0                         0.0   \n", "541                     0.0                         0.0   \n", "\n", "     execute_blender_code_blender  get_object_info_blender  \n", "6                             0.0                      0.0  \n", "14                            0.0                      0.0  \n", "18                            0.0                      0.0  \n", "19                            0.0                      0.0  \n", "25                            0.0                      0.0  \n", "..                            ...                      ...  \n", "528                           0.0                      0.0  \n", "530                           0.0                      0.0  \n", "532                           0.0                      0.0  \n", "533                           0.0                      0.0  \n", "541                           0.0                      0.0  \n", "\n", "[141 rows x 26 columns]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["stats[stats[\"has_any_tool_calls\"] == 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
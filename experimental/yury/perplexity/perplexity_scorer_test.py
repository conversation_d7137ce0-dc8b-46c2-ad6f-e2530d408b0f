import pytest
import torch
import torch.nn.functional as F
from unittest.mock import Mock, patch

from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.yury.perplexity.perplexity_scorer import QwnenPerplexityScorer


@pytest.fixture
def scorer():
    return QwnenPerplexityScorer()


@pytest.fixture
def mock_model():
    model = Mock()
    # Mock forward_pass_single_logits to return some predictable logits
    # Need 3 logits for label tokens [1, 2, 2] (including im_end)
    model.forward_pass_single_logits.return_value = torch.tensor(
        [
            [1.0, 2.0, 3.0],  # Logits for prompt token
            [2.0, 3.0, 1.0],  # Logits for prompt token
            [1.0, 2.0, 3.0],  # Logits for label token 1
            [2.0, 3.0, 1.0],  # Logits for label token 2
            [3.0, 1.0, 2.0],  # Logits for im_end token
        ]
    )
    return model


def test_score_calculates_perplexity(scorer, mock_model):
    # Arrange
    scorer.model = mock_model
    # Mock tokenizer to return simple token IDs
    scorer.tokenizer.tokenize_safe = Mock(return_value=[1, 2])
    scorer.tokenizer.special_tokens.im_end = 2

    # Mock prompt formatter to return simple tokens
    mock_prompt_output = Mock()
    mock_prompt_output.tokens = [0, 1]  # Simple prompt tokens
    scorer.prompt_formatter.format_prompt = Mock(return_value=mock_prompt_output)

    sample = ChatPromptInput(
        message="test message",
        path="test.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        context_code_exchange_request_id=None,
    )
    label = "test"

    # Act
    score = scorer.score(sample, label)

    # Assert
    assert isinstance(score, float)
    # The model was called with the concatenated tokens
    mock_model.forward_pass_single_logits.assert_called_once()
    # Verify the input tensor passed to the model
    call_args = mock_model.forward_pass_single_logits.call_args[0][0]
    assert isinstance(call_args, torch.Tensor)
    assert call_args.tolist() == [
        0,
        1,
        1,
        2,
        2,
    ]  # prompt_tokens + label_tokens + im_end


def test_score_raises_error_when_model_not_loaded(scorer):
    # Arrange
    scorer.model = None
    sample = ChatPromptInput(
        message="test message",
        path="test.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        context_code_exchange_request_id=None,
    )

    # Act & Assert
    with pytest.raises(RuntimeError, match="Model not loaded yet."):
        scorer.score(sample, "test")

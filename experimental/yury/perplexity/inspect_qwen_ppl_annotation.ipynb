{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.llm_apis.llm_client import (\n", "    QwenFastForwardClient,\n", ")\n", "from research.models.all_models import get_model\n", "from research.models import fastforward_llama_models\n", "\n", "\n", "model = get_model(\n", "    \"fastforward_qwen25coder_14b\",\n", "    checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-ffw\"),\n", "    checkpoint_sha256=\"4ebe13be3a9d7b2b8a70d91c997ae5fc82926ff1b06caf559ce58702a0b4cf1c\",\n", ")\n", "\n", "# model = get_model(\n", "#     \"fastforward_qwen25coder_32b\",\n", "#     checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/qwen25-coder/32b-instruct-ffw\"),\n", "#     checkpoint_sha256=\"4c30e35231d7a1c0db0a21e37c5503d35ea5694fc325b0074195c5761f7fc010\",\n", "# )\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.augment_qa_task import AugmentQATask\n", "\n", "task = AugmentQATask.from_yaml_config(\n", "    {\n", "        \"dataset_path\": \"/mnt/efs/augment/data/processed/augment_qa/v3_1\",\n", "        \"html_report_output_dir\": \"/mnt/efs/augment/public_html/michiel/augmentqa_v3_1\",\n", "    }\n", ")\n", "\n", "print(f\"There are {len(task)} examples in {task.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.perplexity.perplexity_scorer import QwnenPerplexityScorer\n", "\n", "perplexity_scorer = QwnenPerplexityScorer()\n", "perplexity_scorer.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perplexity_scorer.score(task.samples[0], task.samples[0].target)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import pandas as pd\n", "import pickle\n", "import pyarrow\n", "import pyarrow.parquet\n", "from typing import Iterator, Any\n", "\n", "from pathlib import Path\n", "from research.llm_apis.llm_client import ToolCall\n", "\n", "from research.core.types import Chunk, Document\n", "from base.prompt_format.common import PromptChunk\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_chunks\n", "\n", "INPUT_PATH = Path(\n", "    \"/mnt/efs/spark-data/user/yury/query_rewrite/augmentqa_v3_1_haiku/06_scored_rewrites\"\n", "    # \"/mnt/efs/spark-data/user/yury/query_rewrite/augmentqa_v3_1_gemini/06_scored_rewrites\"\n", ")\n", "\n", "\n", "def iterate_over_parquet_files(path: Path) -> Iterator[dict[str, Any]]:\n", "    for parquet_file_path in path.glob(\"*.parquet\"):\n", "        parquet_file = pyarrow.parquet.ParquetFile(parquet_file_path)\n", "        for batch in parquet_file.iter_batches():\n", "            records = batch.to_pylist()\n", "            for record in records:\n", "                yield record\n", "\n", "\n", "def research_chunk_to_prompt_chunk(chunk: Chunk) -> PromptChunk:\n", "    return PromptChunk(\n", "        text=chunk.text,\n", "        path=chunk.path or \"\",\n", "        unique_id=chunk.id,\n", "        origin=\"dense_retriever\",\n", "        char_start=chunk.char_offset,\n", "        char_end=chunk.char_offset + chunk.length,\n", "        blob_name=chunk.parent_doc.id,\n", "    )\n", "\n", "\n", "label_name = \"silver_c120k_v2\"\n", "max_initial_retrieval_chars = 10_000\n", "\n", "for row in iterate_over_parquet_files(INPUT_PATH):\n", "    for index, sample in enumerate(row[\"samples\"]):\n", "        initial_retrievals = deserialize_retrieved_chunks(sample[\"serialized_chunks\"])\n", "        # truncated_initial_retrievals = initial_retrievals[: sample[\"num_initial_retrievals\"]]\n", "\n", "        sample_with_retrievals = dataclasses.replace(\n", "            task.samples[index],\n", "            retrieved_chunks=[\n", "                research_chunk_to_prompt_chunk(chunk) for chunk in initial_retrievals\n", "            ],\n", "        )\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perplexity_scorer.score(sample_with_retrievals, task.samples[0].target)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "\n", "from experimental.yury.perplexity.chat_qwen_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", "    TokenizedChatPromptFormatter,\n", "    TokenizedChatPromptOutput,\n", ")\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "\n", "token_apportionment = ChatTokenApportionment(\n", "    path_len=256,\n", "    prefix_len=1024 * 2,\n", "    chat_history_len=1024 * 4,\n", "    suffix_len=1024 * 2,\n", "    retrieval_len=-1,  # unlimited retrieval\n", "    max_prompt_len=12288,  # 12k for prompt\n", "    # Fields unused by default\n", "    retrieval_len_per_each_user_guided_file=0,\n", "    retrieval_len_for_user_guided=0,\n", "    # Deprecated fields\n", "    message_len=-1,\n", "    selected_code_len=-1,\n", ")\n", "\n", "prompt_formatter = ChatQwenPromptFormatter(\n", "    tokenizer=tokenizer,\n", "    token_apportionment=token_apportionment,\n", ")\n", "\n", "prompt_output = prompt_formatter.format_prompt(sample_with_retrievals)\n", "\n", "\n", "class PerplexityScorer:\n", "    def __init__(self, model, tokenizer, prompt_formatter):\n", "        self.model = model\n", "        self.tokenizer = tokenizer\n", "        self.prompt_formatter = prompt_formatter\n", "\n", "    def score(self, sample, label):\n", "        prompt_output = self.prompt_formatter.format_prompt(sample)\n", "\n", "        label_tokens = self.tokenizer.tokenize_safe(label) + [\n", "            self.tokenizer.special_tokens.im_end\n", "        ]\n", "\n", "        tokens = torch.tensor(prompt_output.tokens + label_tokens)\n", "        logits = model.forward_pass_single_logits(tokens)\n", "        logits = logits[len(prompt_output.tokens) - 1 : -1]\n", "        assert logits.shape[0] == len(label_tokens)\n", "\n", "        log_probs = F.log_softmax(logits.float(), dim=-1)\n", "        label_tokens_tensor = torch.tensor(label_tokens, dtype=torch.int64).to(\n", "            log_probs.device\n", "        )\n", "        label_tokens_tensor = label_tokens_tensor.unsqueeze(-1)\n", "        label_log_probs = log_probs.gather(1, label_tokens_tensor).squeeze(1)\n", "        return label_log_probs.sum().item()\n", "\n", "\n", "# compute_perplexity(prompt_output.tokens, label_tokens)\n", "# -0.3484535813331604\n", "# -1.561409831047058\n", "\n", "\n", "perplexity_scorer = PerplexityScorer(model, tokenizer, prompt_formatter)\n", "perplexity_scorer.score(sample_with_retrievals, task.samples[index].target)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions\n", "\n", "generation_options = GenerationOptions(\n", "    temperature=0.0,\n", "    max_generated_tokens=1000,\n", ")\n", "response = model.raw_generate_tokens(prompt_output.tokens, generation_options)\n", "\n", "print(tokenizer.detok<PERSON>ze(response.tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response_tokens = response.tokens[:-2]\n", "assert response_tokens[-1] == tokenizer.special_tokens.im_end\n", "label = tokenizer.detokenize(response_tokens[:-1])  # remove im_end\n", "\n", "label = task.samples[index].target\n", "\n", "label_tokens = tokenizer.tokenize_safe(label) + [tokenizer.special_tokens.im_end]\n", "\n", "\n", "def compute_perplexity(prompt_tokens: list[int], label_tokens: list[int]):\n", "    logits = model.forward_pass_single_logits(\n", "        torch.tensor(prompt_tokens + label_tokens)\n", "    )\n", "    logits = logits[len(prompt_output.tokens) - 1 : -1]\n", "    assert logits.shape[0] == len(label_tokens)\n", "\n", "    log_probs = F.log_softmax(logits.float(), dim=-1)\n", "    label_tokens_tensor = torch.tensor(label_tokens, dtype=torch.int64).to(\n", "        log_probs.device\n", "    )\n", "    label_tokens_tensor = label_tokens_tensor.unsqueeze(-1)\n", "    label_log_probs = log_probs.gather(1, label_tokens_tensor).squeeze(1)\n", "    return label_log_probs.sum().item()\n", "\n", "\n", "compute_perplexity(prompt_output.tokens, label_tokens)\n", "# -0.3484535813331604\n", "# -1.561409831047058"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.exp(torch.tensor(-0.3484535813331604))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_probs[0, 28715], log_probs[0].argmax()\n", "\n", "print(tokenizer.de<PERSON><PERSON><PERSON>([28715]))\n", "print(tokenizer.detokenize([log_probs[0].argmax()]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Tests for chat_qwen_prompt_formatter.py."""

import unittest

from base.prompt_format_chat.prompt_formatter import ChatPromptInput, Exchange
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from experimental.yury.perplexity.chat_qwen_prompt_formatter import (
    ChatQwenPromptFormatter,
)


class TestChatQwenPromptFormatter(unittest.TestCase):
    """Tests for ChatQwenPromptFormatter."""

    def setUp(self):
        """Set up test fixtures."""
        self.tokenizer = Qwen25CoderTokenizer()
        self.formatter = ChatQwenPromptFormatter(self.tokenizer)

    def test_format_prompt_basic(self):
        """Test basic prompt formatting."""
        prompt_input = ChatPromptInput(
            path="test.py",
            prefix="def hello():\n    print('hello')",
            selected_code="",
            suffix="",
            message="What does this function do?",
            chat_history=[],
            prefix_begin=0,
            suffix_end=28,  # Length of prefix + selected_code + suffix
            retrieved_chunks=[],
        )

        output = self.formatter.format_prompt(prompt_input)

        # Convert tokens back to text to verify the structure
        text = self.tokenizer.detokenize(output.tokens)

        # Check that the output contains expected parts
        self.assertIn("<|im_start|>system", text)
        self.assertIn("<|im_end|>", text)
        self.assertIn("<|im_start|>user", text)
        self.assertIn("What does this function do?", text)
        self.assertIn("<|im_start|>assistant", text)

    def test_format_prompt_with_history(self):
        """Test prompt formatting with chat history."""
        prompt_input = ChatPromptInput(
            path="test.py",
            prefix="def hello():\n    print('hello')",
            selected_code="",
            suffix="",
            message="What does this function do?",
            chat_history=[
                Exchange(
                    "What is this file about?",
                    "This file contains a simple hello function.",
                ),
            ],
            prefix_begin=0,
            suffix_end=28,  # Length of prefix + selected_code + suffix
            retrieved_chunks=[],
        )

        output = self.formatter.format_prompt(prompt_input)
        text = self.tokenizer.detokenize(output.tokens)

        # Check that history messages are included
        self.assertIn("What is this file about?", text)
        self.assertIn("This file contains a simple hello function.", text)
        self.assertIn("What does this function do?", text)


if __name__ == "__main__":
    unittest.main()

import torch
import torch.nn.functional as F

from pathlib import Path

from experimental.yury.perplexity.chat_qwen_prompt_formatter import (
    ChatQwenPromptFormatter,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
)


class QwnenPerplexityScorer:
    """Perplexity scorer for Qwen models."""

    def __init__(self):
        self.model = None
        self.tokenizer = Qwen25CoderTokenizer()

        token_apportionment = ChatTokenApportionment(
            path_len=256,
            prefix_len=1024 * 2,
            chat_history_len=1024 * 4,
            suffix_len=1024 * 2,
            retrieval_len=-1,  # unlimited retrieval
            max_prompt_len=12288,  # 12k for prompt
            # Fields unused by default
            retrieval_len_per_each_user_guided_file=0,
            retrieval_len_for_user_guided=0,
            # Deprecated fields
            message_len=-1,
            selected_code_len=-1,
        )
        self.prompt_formatter: TokenizedChatPromptFormatter = ChatQwenPromptFormatter(
            tokenizer=self.tokenizer,
            token_apportionment=token_apportionment,
        )

    def load(self):
        from research.models import fastforward_llama_models
        from research.models.all_models import get_model

        self.model = get_model(
            "fastforward_qwen25coder_14b",
            checkpoint_path=Path(
                "/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-ffw"
            ),
            checkpoint_sha256="4ebe13be3a9d7b2b8a70d91c997ae5fc82926ff1b06caf559ce58702a0b4cf1c",
        )

        self.model.load()

    def score(self, sample: ChatPromptInput, label: str, reduce: str = "sum"):
        if self.model is None:
            raise RuntimeError("Model not loaded yet.")

        prompt_output: TokenizedChatPromptOutput = self.prompt_formatter.format_prompt(
            sample
        )

        label_tokens = self.tokenizer.tokenize_safe(label) + [
            self.tokenizer.special_tokens.im_end
        ]

        tokens = torch.tensor(prompt_output.tokens + label_tokens)
        logits = self.model.forward_pass_single_logits(tokens)
        logits = logits[len(prompt_output.tokens) - 1 : -1]
        assert logits.shape[0] == len(label_tokens)

        log_probs = F.log_softmax(logits.float(), dim=-1)
        label_tokens_tensor = torch.tensor(label_tokens, dtype=torch.int64).to(
            log_probs.device
        )
        label_tokens_tensor = label_tokens_tensor.unsqueeze(-1)
        label_log_probs = log_probs.gather(1, label_tokens_tensor).squeeze(1)

        if reduce == "mean":
            return label_log_probs.mean().item()
        elif reduce == "sum":
            return label_log_probs.sum().item()
        else:
            raise ValueError(f"Unknown reduce method: {reduce}")

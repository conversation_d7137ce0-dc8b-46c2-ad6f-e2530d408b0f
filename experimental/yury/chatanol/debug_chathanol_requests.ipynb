{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Loading StarCoder model from a legacy checkpoint. This is deprecated and will be removed in the future.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id 4f38a12e-bd7e-4b7e-8763-328a670bbb23.\n", "Created BasicAttention with stable_id d24ca15a-7ccf-4cea-bf90-a8d1eda1f6dc.\n"]}], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "import pathlib\n", "\n", "from research.retrieval import retrieval_database\n", "\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "from research.retrieval.utils import parse_yaml_config\n", "\n", "from research.fastbackward import retrieval_models\n", "from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastforward_checkpoint\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name\n", "\n", "tokenizer = create_tokenizer_by_name(\"rogue\")\n", "\n", "# research.retrieval.scorers.dense_scorer_v2.DenseRetrievalScorerV2\n", "scorer = create_dense_scorer_from_fastforward_checkpoint(\n", "    \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468/\",\n", "    get_retrieval_prompt_formatter_by_name(\"chatanol6-singleturnisspecial\", tokenizer),\n", "    get_retrieval_prompt_formatter_by_name(\"chatanol6-embedding\", tokenizer),\n", "    # get_retrieval_prompt_formatter_by_name(\"chatanol6-query\", tokenizer),\n", "    # get_retrieval_prompt_formatter_by_name(\"ethanol6-embedding-with-path-key\", tokenizer),\n", ")\n", "\n", "scorer.load()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["('popup.html', 'docset://html/html-08d48362-3ae0-4729-a016-3378a20804e0/0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from textwrap import dedent\n", "from research.retrieval.types import (\n", "    Chunk,\n", "    ChunkId,\n", "    Document,\n", "    DocumentId,\n", "    RetrievalScore,\n", ")\n", "\n", "path2 = \"docset://html/html-08d48362-3ae0-4729-a016-3378a20804e0/0\"\n", "doc2 = dedent(\"\"\"\\\n", "    email\n", "    ## Validation\n", "    ### Pattern validation\n", "    Since inputs of type `email` validate against both the standard email\n", "    address validation *and* the specified `pattern`, you can implement this\n", "    easily. Let's see how:\n", "\n", "    <div class=\"code-example\">\n", "\n", "    <span class=\"language-name\">html</span>\n", "\n", "    ```\n", "    <form>\n", "    <div class=\"emailBox\">\n", "        <label for=\"emailAddress\">Your email address</label><br />\n", "        <input\n", "        id=\"emailAddress\"\n", "        type=\"email\"\n", "        size=\"64\"\n", "        maxlength=\"64\"\n", "        required\n", "        placeholder=\"<EMAIL>\"\n", "        pattern=\".+@beststartupever\\.com\"\n", "        title=\"Please provide only a Best Startup Ever corporate email address\" />\n", "    </div>\n", "\n", "    <div class=\"messageBox\">\n", "        <label for=\"message\">Request</label><br />\n", "        <textarea\n", "        id=\"message\"\n", "        cols=\"80\"\n", "        rows=\"8\"\n", "        required\n", "        placeholder=\"My shoes are too tight, and I have forgotten how to dance.\"></textarea>\n", "    </div>\n", "    <input type=\"submit\" value=\"Send Request\" />\n", "    </form>\n", "    ```\n", "\n", "    </div>\n", "\n", "    Our `<form>` contains one `<input>` of type `email` for the user's email\n", "    address, a `<textarea>` to enter their message for IT into, and an\n", "    `<input>` of type `\"submit\"`, which creates a button to submit the form.\n", "    Each text entry box has a `<label>` associated with it to let the user\n", "    know what's expected of them.\n", "\n", "    Let's take a closer look at the email address entry box. Its `size` and\n", "    `maxlength` attributes are both set to 64 in order to show room for 64\n", "    characters worth of email address, and to limit the number of characters\n", "    actually entered to a maximum of 64. The `required` attribute is\n", "    specified, making it mandatory that a valid email address be provided.\n", "    \"\"\")\n", "\n", "path1 = \"popup.html\"\n", "doc1 = dedent(\"\"\"\\\n", "    <!DOCTYPE html>\n", "    <html>\n", "    <head>\n", "    <title>Interactive Elements Mapper</title>\n", "    </head>\n", "    <body>\n", "    <h1>Interactive Elements Mapper</h1>\n", "    <p>Click the extension icon to map interactive elements on the current page.</p>\n", "    </body>\n", "    </html>\"\"\")\n", "\n", "\n", "def build_chunk(path: str, doc: str):\n", "    return Chunk(\n", "        id=f\"{path}:1\",\n", "        text=doc,\n", "        parent_doc=Document(\n", "            id=path,\n", "            text=doc,\n", "            path=path,\n", "        ),\n", "        char_offset=0,\n", "        length=len(doc),\n", "        line_offset=0,\n", "        length_in_lines=len(doc.splitlines()),\n", "    )\n", "\n", "docs = {\n", "    path1: [build_chunk(path1, doc1)],\n", "    path2: [build_chunk(path2, doc2)],\n", "}\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["([('docset://html/html-08d48362-3ae0-4729-a016-3378a20804e0/0',\n", "   'docset://html/html-08d48362-3ae0-4729-a016-3378a20804e0/0:1'),\n", "  ('popup.html', 'popup.html:1')],\n", " [1965.092529296875, 1956.458740234375])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["scorer.remove_all_docs()\n", "scorer.add_docs(docs)\n", "\n", "from base.prompt_format_retrieve.prompt_formatter import (\n", "    ChatRetrieverPromptInput,\n", "    DocumentRetrieverPromptInput,\n", "    Retriever<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    PromptFormatterOutput,\n", ")\n", "\n", "\n", "# message = \"i want to modify the `popup.html` to have an input box for a chat\"\n", "# message = \"show me `popup.html` file\"\n", "message = \"popup.html\"\n", "\n", "prompt_input = ChatRetrieverPromptInput(\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    path=\"\",\n", "    message=message,\n", "    selected_code=\"\",\n", "    chat_history=[],\n", ")\n", "scorer.score(prompt_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
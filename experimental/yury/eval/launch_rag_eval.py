"""Launch evals for the basic rag system."""

import argparse

from experimental.yury.eval.execute_eval import execute_eval
from experimental.yury.eval.model_configs import model_config_dict
from experimental.yury.eval.retriever_configs import retriever_config_dict


def main(args):
    model_config = model_config_dict[args.model]
    retriever_config = retriever_config_dict[args.retriever]

    system_config = {
        "name": "basic_rag",
        "model": model_config,
        "generation_options": {
            "max_generated_tokens": 280,
            "top_k": None,
            "top_p": None,
        },
        "retriever": retriever_config,
        "experimental": {
            "remove_suffix": False,
            "trim_on_dedent": False,
            "retriever_top_k": 25,
        },
    }

    tasks = {
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "cceval": {"name": "cceval"},
        "2-3lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
    }
    for task_name, task_config in tasks.items():
        # if task_config["name"] == "cceval":
        #     pod_spec = "1xA100.yaml"
        # else:
        #     pod_spec = "A40.yaml"

        pod_spec = "1xA100.yaml"

        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        if args.checkpoint is not None:
            checkpoint_path = args.checkpoint
        else:
            checkpoint_path = model_config["checkpoint_path"]
        retriever_name = args.retriever

        determined_name = (
            f"{task_name}, {args.model}, {retriever_name}, {checkpoint_path}"
        )

        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            checkpoint=args.checkpoint,
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        help="The key in the model dict.",
    )
    parser.add_argument(
        "--checkpoint",
        type=str,
        default=None,
        help="Checkpoint path or Determined UUID",
    )
    parser.add_argument(
        "--retriever",
        type=str,
        default="ethanol616",
        help="The key in the retriever dict.",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)

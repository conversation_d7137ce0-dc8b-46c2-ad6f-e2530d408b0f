"""Configuration for DeepRogue with Stateless Caching system."""

config = {
    "name": "deeprogue_sl",
    # Model trained for 1K steps
    # "checkpoint_path": "/mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth61m_farpref_prefsufretnpref250_quant50_v1/checkpoint_llama_iteration_999",
    # Model trained for 4K steps
    "checkpoint_path": "/mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth64m_npref250_olap0_quant50_v1/checkpoint_llama_iteration_3999",
    "fim_gen_mode": "evaluation",
    "model_parallel_size": 4,
    "prompt": {
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "component_order": [
            "prefix",
            "suffix",
            "retrieval",
            "nearby_prefix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
        "prepend_bos_token": True,
    },
}

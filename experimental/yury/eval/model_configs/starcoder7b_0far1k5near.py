"""Configuration for StarCoder-7B system with 1.5k close context only."""

config = {
    "name": "rogue_longcontext",
    "checkpoint_path": "/mnt/efs/augment/user/yury/logs/longcontext/starcoder7b_0far1k5near_v1/checkpoint_llama_iteration_999",
    "seq_length": 1780,
    "prompt": {
        "max_prefix_tokens": 512,
        "max_suffix_tokens": 256,
        "max_total_near_tokens": 1500,
        "max_far_tokens": 0,
        "max_noisy_far_tokens": 0,
        "max_filename_tokens": 50,
        "component_order": [
            "far_retrieval",
            "path",
            "near_retrieval",
            "prefix",
            "suffix",
        ],
        "prepend_bos_token": False,
    },
}

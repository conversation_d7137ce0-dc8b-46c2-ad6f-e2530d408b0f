from experimental.yury.eval.model_configs import (
    deeproguesl,
    roguesl_4k,
    starcoder7b_0far1k5near,
    starcoder7b_4k5far1k5near,
    starcoder16b_0far1k5near,
    starcoder16b_4k5far1k5near,
)

model_config_dict = {
    "deeproguesl": deeproguesl.config,
    "roguesl_4k": roguesl_4k.config,
    "starcoder7b_0far1k5near": starcoder7b_0far1k5near.config,
    "starcoder7b_4k5far1k5near": starcoder7b_4k5far1k5near.config,
    "starcoder16b_0far1k5near": starcoder16b_0far1k5near.config,
    "starcoder16b_4k5far1k5near": starcoder16b_4k5far1k5near.config,
}

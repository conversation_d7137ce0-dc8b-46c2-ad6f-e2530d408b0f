"""Configuration for StarCoder-16B system with 1.5k close context and 4.5 far context."""

config = {
    "name": "rogue_longcontext",
    # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/42401/overview
    # "checkpoint_path": "5838be38-b77a-4c2c-a74f-59370741718b",
    "seq_length": 6280,
    "model_parallel_size": 2,
    # "fim_gen_mode": "evaluation",
    "prompt": {
        "max_prefix_tokens": 512,
        "max_suffix_tokens": 256,
        "max_total_near_tokens": 1500,
        "max_far_tokens": 4500,
        "max_noisy_far_tokens": 0,
        "max_filename_tokens": 50,
        "component_order": [
            "far_retrieval",
            "path",
            "near_retrieval",
            "prefix",
            "suffix",
        ],
        "prepend_bos_token": False,
    },
}

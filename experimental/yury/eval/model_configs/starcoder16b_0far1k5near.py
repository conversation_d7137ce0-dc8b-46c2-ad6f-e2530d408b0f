"""Configuration for StarCoder-16B system with 1.5k close context only."""

config = {
    "name": "rogue_longcontext",
    # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/42404/overview
    # "checkpoint_path": "3ba30d4a-ac5e-4142-9ead-d8d571eb3d78",
    "seq_length": 1780,
    "model_parallel_size": 2,
    # "fim_gen_mode": "evaluation",
    "prompt": {
        "max_prefix_tokens": 512,
        "max_suffix_tokens": 256,
        "max_total_near_tokens": 1500,
        "max_far_tokens": 0,
        "max_noisy_far_tokens": 0,
        "max_filename_tokens": 50,
        "component_order": [
            "far_retrieval",
            "path",
            "near_retrieval",
            "prefix",
            "suffix",
        ],
        "prepend_bos_token": False,
    },
}

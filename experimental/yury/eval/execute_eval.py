"""<PERSON><PERSON>'s (really <PERSON><PERSON><PERSON>'s (really <PERSON><PERSON><PERSON>'s)) utils for submiting eval configs."""

import os
from pathlib import Path
from typing import Literal, Optional

import yaml

from research.eval.eval import __file__ as eval_file
from research.utils.inspect_indexed_dataset import print_green

PodSpecName = Literal["1xA100.yaml", "1xH100.yaml", "A40.yaml"]


def execute_eval(
    determined_name: str,
    system_config: dict,
    task_config: dict,
    pod_spec: PodSpecName,
    checkpoint: Optional[str] = None,
    determined_overrides: Optional[dict] = None,
    additional_overrides: Optional[dict] = None,
):
    all_config = {
        "system": system_config,
        "task": task_config,
        "podspec": pod_spec,
        "determined": {
            "name": determined_name,
            "workspace": "Dev",
            "project": "yury-eval",
            "metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml",
        },
        "import_modules": [
            "experimental.michiel.research.longcon.models",
            "experimental.michiel.research.longcon.prompt_formatters",
        ],
    }
    all_config["determined"].update(determined_overrides or {})
    all_config.update(additional_overrides or {})

    # convert the config to yaml string
    config_str = yaml.dump(all_config, indent=2)
    print_green("Launching eval with the following config:")
    print(config_str)
    config_path = Path("/tmp/current_eval_config.yml")
    config_path.write_text(config_str)
    cmd = f"python3 {eval_file} --v2 --skip_bazel {config_path}"
    if checkpoint is not None:
        cmd += " --checkpoint=" + checkpoint
    print_green(f"Executing command: {cmd}")
    os.system(cmd)

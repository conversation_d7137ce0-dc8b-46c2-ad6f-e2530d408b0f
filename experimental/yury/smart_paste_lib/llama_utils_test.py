from textwrap import dedent
from experimental.yury.smart_paste_lib.llama_utils import (
    extract_modified_file_from_response,
    lior_extract_modified_file_from_response,
)


def test_extract_modified_file_from_response_1():
    input_text = dedent("""\
        **Step 1**

        The suggested changes involve adding a new test case to ensure that empty arrays are removed from the chat responses generated in `services/api_proxy/server/src/handlers_chat.rs`. The test case, `test_chat_response_empty_arrays_removed`, creates a `ChatResponse` with empty arrays for `unknown_blob_names`, `workspace_file_chunks`, and `incorporated_external_sources`, and then checks that these arrays are empty in the corresponding `public_api_proto::ChatResponse`.

        **Step 2**

        To apply the suggested changes, we will add the new test case to the existing test module in `services/api_proxy/server/src/handlers_chat.rs`. The test case will be added at the end of the test module, after the existing test cases.

        **Step 3**
        ```rust
        use std::convert::TryFrom;

        use crate::api_auth::User;
        use crate::chat::{self, ChatPosition, ChatRequest, ChatResponse};
        use crate::content_manager_util::ContentManagerClient;
        use crate::generation_clients::Client;
        use crate::handler_utils::{
            convert_blobs_and_names, get_model, request_context_from_req, retry, status_to_response,
            RetryPolicy,
        };
        ```""")

    expected_output = dedent("""\
        use std::convert::TryFrom;

        use crate::api_auth::User;
        use crate::chat::{self, ChatPosition, ChatRequest, ChatResponse};
        use crate::content_manager_util::ContentManagerClient;
        use crate::generation_clients::Client;
        use crate::handler_utils::{
            convert_blobs_and_names, get_model, request_context_from_req, retry, status_to_response,
            RetryPolicy,
        };
        """)

    result = extract_modified_file_from_response(input_text)
    assert result == expected_output


def test_extract_modified_file_from_response_no_language_hint():
    input_text = dedent("""\
        **Step 1**

        Some description here.

        **Step 2**

        More description.

        **Step 3**
        ```
        def hello_world():
            print("Hello, World!")
        ```""")

    expected_output = dedent("""\
        def hello_world():
            print("Hello, World!")
        """)

    result = extract_modified_file_from_response(input_text)
    assert result == expected_output


def test_extract_modified_file_from_response_no_step_3():
    input_text = dedent("""\
        **Step 1**

        Some description here.

        **Step 2**

        More description.""")

    result = extract_modified_file_from_response(input_text)
    assert result is None


def test_extract_modified_file_from_response_no_markdown():
    input_text = dedent("""\
        **Step 1**

        Some description here.

        **Step 2**

        More description.

        **Step 3**
        def hello_world():
            print("Hello, World!")""")

    result = extract_modified_file_from_response(input_text)
    assert result is None


def test_lior_extract_modified_file_from_response_1():
    input_text = dedent("""\
        ```rust
        use std::convert::TryFrom;

        use crate::api_auth::User;
        use crate::chat::{self, ChatPosition, ChatRequest, ChatResponse};
        use crate::content_manager_util::ContentManagerClient;
        use crate::generation_clients::Client;
        use crate::handler_utils::{
            convert_blobs_and_names, get_model, request_context_from_req, retry, status_to_response,
            RetryPolicy,
        };
        ```""")

    expected_output = dedent("""\
        use std::convert::TryFrom;

        use crate::api_auth::User;
        use crate::chat::{self, ChatPosition, ChatRequest, ChatResponse};
        use crate::content_manager_util::ContentManagerClient;
        use crate::generation_clients::Client;
        use crate::handler_utils::{
            convert_blobs_and_names, get_model, request_context_from_req, retry, status_to_response,
            RetryPolicy,
        };
        """)

    result = lior_extract_modified_file_from_response(input_text)
    assert result == expected_output


def test_lior_extract_modified_file_from_response_no_language_hint():
    input_text = dedent("""\
        ```
        def hello_world():
            print("Hello, World!")
        ```""")

    expected_output = dedent("""\
        def hello_world():
            print("Hello, World!")
        """)

    result = lior_extract_modified_file_from_response(input_text)
    assert result == expected_output


def test_lior_extract_modified_file_from_response_no_language_hint_2():
    input_text = dedent("""\
           ```
        def hello_world():
            print("Hello, World!")
        ```""")

    expected_output = dedent("""\
        def hello_world():
            print("Hello, World!")
        """)

    result = lior_extract_modified_file_from_response(input_text)
    assert result == expected_output


def test_lior_extract_modified_file_from_response_no_markdown():
    input_text = dedent("""\
        def hello_world():
            print("Hello, World!")
        """)

    expected_output = dedent("""\
        def hello_world():
            print("Hello, World!")
        """)

    result = lior_extract_modified_file_from_response(input_text)
    assert result == expected_output

"""Types for smart paste."""

import dataclasses
import json

from base.prompt_format.common import Exchange
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptInput,
)


@dataclasses.dataclass(frozen=True)
class ResearchSmartPastePromptInput(SmartPastePromptInput):
    request_id: str = ""
    """Sample ID."""

    aux: dict = dataclasses.field(default_factory=dict)

    @classmethod
    def from_dict(cls, d: dict) -> "ResearchSmartPastePromptInput":
        d = d.copy()
        d["chat_history"] = [
            Exchange(
                request_message=exchange["request_message"],
                response_text=exchange["response_text"],
                request_id=exchange.get("request_id"),
            )
            for exchange in d["chat_history"]
        ]
        return cls(**d)

    @classmethod
    def from_json(cls, json_str: str) -> "ResearchSmartPastePromptInput":
        return cls.from_dict(json.loads(json_str))

import pytest
from experimental.yury.smart_paste_lib.claude_utils import (
    adjust_tool_calls,
    apply_tool_calls,
    ReplaceTextToolCall,
    build_smart_paste_claude_request,
)
from base.prompt_format.common import Exchange
from base.diff_utils.diff_utils import File


def test_apply_tool_calls_1():
    before_file_contents = """\
import React from "react";
import {
  ArticleFlags,
  UpdatedTimeProvider
} from "@times-components/ts-components";
import Image from "@times-components/image";
import { checkStylesForUnits } from "@times-components/utils";

import Label from "../article-label/article-label";
import Meta from "../article-meta/article-meta";
import Standfirst from "../article-standfirst/article-standfirst";
import {
  articleHeaderPropTypes,
  articleHeaderDefaultProps
} from "./article-header-prop-types";
import styles from "../styles";
"""

    tool_calls = [
        ReplaceTextToolCall(
            old_text="""\
import {
  ArticleFlags,
  UpdatedTimeProvider
} from "@times-components/ts-components";""",
            start_line_number=2,
            end_line_number=5,
            replacement_text="""\
import {
  ArticleFlags,
  UpdatedTimeProvider,
  AudioPlayer
} from "@times-components/ts-components";
""",
        ),
    ]
    expected_after_file_contents = """\
import React from "react";
import {
  ArticleFlags,
  UpdatedTimeProvider,
  AudioPlayer
} from "@times-components/ts-components";
import Image from "@times-components/image";
import { checkStylesForUnits } from "@times-components/utils";

import Label from "../article-label/article-label";
import Meta from "../article-meta/article-meta";
import Standfirst from "../article-standfirst/article-standfirst";
import {
  articleHeaderPropTypes,
  articleHeaderDefaultProps
} from "./article-header-prop-types";
import styles from "../styles";
"""

    adjusted_tool_calls = adjust_tool_calls(before_file_contents, tool_calls)
    assert len(adjusted_tool_calls) == 1
    assert adjusted_tool_calls[0].start_line_number == 2
    assert adjusted_tool_calls[0].end_line_number == 5

    result = apply_tool_calls(before_file_contents, tool_calls)
    assert result == expected_after_file_contents


def test_build_smart_paste_claude_request():
    # Test setup
    target_file = File(
        path="test/file.py", contents="def hello():\n    print('world')\n"
    )
    codeblock = "def hello():\n    print('Hello world!')\n"
    chat_history = [
        Exchange(
            request_message="Can you update the print message?",
            response_text="Here's the updated code with 'Hello world!' message",
        )
    ]

    # Test with use_codeblock=True
    result = build_smart_paste_claude_request(
        target_file=target_file,
        codeblock=codeblock,
        chat_history=chat_history,
        use_codeblock=True,
        model="test-model",
    )

    # Verify the structure and content of the result
    assert isinstance(result, dict)
    assert result["model"] == "test-model"
    assert result["max_tokens"] == 8192
    assert result["temperature"] == 0
    assert "tools" in result
    assert "messages" in result
    assert "system" in result

    # Verify messages structure
    messages = result["messages"]
    assert len(messages) == 3  # 2 from chat history + 1 final prompt
    assert messages[0]["role"] == "user"
    assert messages[0]["content"] == "Can you update the print message?"
    assert messages[1]["role"] == "assistant"
    assert (
        messages[1]["content"] == "Here's the updated code with 'Hello world!' message"
    )

    # Verify the final prompt contains necessary elements
    final_prompt = messages[2]["content"]
    assert "<change_to_apply>" in final_prompt
    assert "<file path=" in final_prompt
    assert "test/file.py" in final_prompt
    assert "replace_text" in final_prompt

    # Test with use_codeblock=False
    result_no_codeblock = build_smart_paste_claude_request(
        target_file=target_file,
        codeblock=codeblock,
        chat_history=chat_history,
        use_codeblock=False,
        model="test-model",
    )

    # Verify the difference in prompt when use_codeblock is False
    final_prompt_no_codeblock = result_no_codeblock["messages"][2]["content"]
    assert "<change_to_apply>" not in final_prompt_no_codeblock
    assert (
        "Great! Now please apply changes that you demonstrated\n"
        in final_prompt_no_codeblock
    )

"""Utilities to perform smart paste with <PERSON>."""

import dataclasses
import re
from difflib import unified_diff
from typing import Iterable, Optional

from base.prompt_format.common import Exchange
from base.diff_utils.diff_utils import File


def put_numbers(
    text: str, start_line_offset: int, mode: str, prefix: Optional[str] = None
):
    assert mode in ["xml", "table", "regular"]

    for i, line in enumerate(text.splitlines(keepends=True)):
        if mode == "xml":
            assert prefix is None, "prefix is not used for xml"
            cur_number = i + 1 + start_line_offset
            yield f"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\n"
        elif mode == "table":
            assert prefix is None, "prefix is not used for table"
            if i == 0 and start_line_offset == 0:
                yield """
 Line | Content
 ---- | -------
"""
            cur_number = f"{i+1+start_line_offset:04d}"
            yield f" {cur_number} | {line}"
        else:
            assert prefix is not None
            yield f"{prefix}{i+1+start_line_offset:04d}: {line}"


TOOLS = [
    {
        "name": "replace_text",
        "description": "Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.",
        "input_schema": {
            "type": "object",
            "properties": {
                "old_text": {
                    "type": "string",
                    "description": "The original text.",
                },
                "start_line_number": {
                    "type": "integer",
                    "description": "The line number where the original text starts, inclusive.",
                },
                "end_line_number": {
                    "type": "integer",
                    "description": "The line number where the original text ends, inclusive.",
                },
                "replacement_text": {
                    "type": "string",
                    "description": "The new text.",
                },
            },
            "required": [
                "old_text",
                "start_line_number",
                "end_line_number",
                "replacement_text",
            ],
        },
    },
]


def format_code(
    prefix: str,
    selected_code: str,
    suffix: str,
    num_lines_in_prefix_suffix: int,
    is_highlighted: bool,
):
    def _n_lines(s: str):
        return len(s.splitlines(keepends=True))

    prefix = "".join(prefix.splitlines(keepends=True)[-num_lines_in_prefix_suffix:])
    suffix = "".join(suffix.splitlines(keepends=True)[:num_lines_in_prefix_suffix])

    full_file_in_prompt = prefix + selected_code + suffix

    # XML
    prefix_n_xml = "".join(put_numbers(prefix, 0, mode="xml"))
    selected_code_n_xml = "".join(
        put_numbers(selected_code, _n_lines(prefix), mode="xml")
    )
    suffix_n_xml = "".join(
        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode="xml")
    )
    if is_highlighted:
        xml_code = f"""{prefix_n_xml}<highlighted_code>\n{selected_code_n_xml}</highlighted_code>\n{suffix_n_xml}"""
    else:
        xml_code = f"""{prefix_n_xml}{selected_code_n_xml}{suffix_n_xml}"""

    # Table
    prefix_n_table = "".join(put_numbers(prefix, 0, mode="table"))
    selected_code_n_table = "".join(
        put_numbers(selected_code, _n_lines(prefix), mode="table")
    )
    suffix_n_table = "".join(
        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode="table")
    )
    if is_highlighted:
        table_code = f"""{prefix_n_table}<highlighted_code>\n{selected_code_n_table}</highlighted_code>\n{suffix_n_table}"""
    else:
        table_code = f"""{prefix_n_table}{selected_code_n_table}{suffix_n_table}"""

    # Regular
    prefix_n_regular = "".join(put_numbers(prefix, 0, mode="regular", prefix=" "))
    selected_code_n_regular = "".join(
        put_numbers(selected_code, _n_lines(prefix), mode="regular", prefix="*")
    )
    suffix_n_regular = "".join(
        put_numbers(
            suffix,
            _n_lines(prefix) + _n_lines(selected_code),
            mode="regular",
            prefix=" ",
        )
    )
    regular_code = f"""{prefix_n_regular}{selected_code_n_regular}{suffix_n_regular}"""

    # No numbering
    if is_highlighted:
        no_numbering_code = f"""{prefix}<highlighted_code>\n{selected_code}</highlighted_code>\n{suffix}"""
    else:
        no_numbering_code = f"""{prefix}{selected_code}{suffix}"""

    return (
        xml_code,
        regular_code,
        table_code,
        {
            "full_file_in_prompt": full_file_in_prompt,
            "first_highlighted_line_number": _n_lines(prefix) + 1,
            "last_highlighted_line_number": _n_lines(prefix) + _n_lines(selected_code),
            "no_numbering_code": no_numbering_code,
        },
    )


class InvalidLineNumber(Exception):
    """Raised when the line number is invalid."""

    def __init__(self, n_total_lines: int, line_number: int):
        self.message = f"Line number {line_number} is out of range {n_total_lines}"
        super().__init__(self.message)


class LineNotFound(Exception):
    """Raised when the line is not found."""

    def __init__(self, line: str):
        self.message = f"Line {line} is not found"
        super().__init__(self.message)


class InvalidToolCall(Exception):
    """Raised when the tool call is invalid."""

    def __init__(self, message: str):
        self.message = f"Invalid tool call output: {message}"
        super().__init__(self.message)


def adjust_line_number(full_file: str, line_number: int, line: str):
    full_file_lines = [
        cur_line.rstrip() for cur_line in full_file.splitlines(keepends=True)
    ]
    line = line.rstrip()

    if line_number - 1 >= len(full_file_lines):
        raise InvalidLineNumber(len(full_file_lines), line_number - 1)

    assert line_number is not None, line_number
    assert full_file_lines[line_number - 1] is not None, full_file_lines
    assert line is not None, line

    if full_file_lines[line_number - 1] == line:
        return line_number

    for i in range(
        max(0, line_number - 50), min(len(full_file_lines), line_number + 50)
    ):
        if full_file_lines[i] == line:
            # print(f"Adjusting line number from {line_number} to {i + 1}")
            return i + 1

    raise LineNotFound(line)


@dataclasses.dataclass(frozen=True)
class ReplaceTextToolCall:
    old_text: str
    start_line_number: int
    end_line_number: int
    replacement_text: str


TOOL_CALL_PATTERN = r"""\
<tool_call>
<old_text>
(.*?)</old_text>

<start_line_number>
(\d+)
</start_line_number>

<end_line_number>
(\d+)
</end_line_number>

<replacement_text>
(.*?)</replacement_text>
</tool_call>"""


def parse_replace_text_tool_call(text: str) -> list[ReplaceTextToolCall]:
    """Parse the tool calls from the text."""
    tool_calls = []
    for match in re.finditer(TOOL_CALL_PATTERN, text, re.DOTALL):
        old_text = match.group(1)
        assert old_text.endswith("\n")
        replacement_text = match.group(4)
        assert replacement_text.endswith("\n")

        try:
            start_line_number = int(match.group(2))
        except ValueError:
            raise ValueError(f"Failed to parse tool calls: {match.group(2)}")

        try:
            end_line_number = int(match.group(3))
        except ValueError:
            raise ValueError(f"Failed to parse tool calls: {match.group(3)}")

        tool_call = ReplaceTextToolCall(
            old_text=old_text,
            start_line_number=start_line_number,
            end_line_number=end_line_number,
            replacement_text=replacement_text,
        )
        tool_calls.append(tool_call)
    tool_calls.sort(key=lambda x: x.start_line_number)
    return tool_calls


def adjust_tool_calls(before_file_contents, tool_calls):
    """Adjust the tool calls to match the before file."""

    # Explicit sort and copy
    tool_calls = sorted(
        tool_calls,
        key=lambda x: x.start_line_number,
    )

    for index in range(len(tool_calls)):
        tool_call = tool_calls[index]
        old_text_lines = tool_call.old_text.splitlines(keepends=True)
        if len(old_text_lines) > 0:
            new_start_line_number = adjust_line_number(
                before_file_contents,
                tool_call.start_line_number,
                old_text_lines[0],
            )
            new_end_line_number = adjust_line_number(
                before_file_contents,
                tool_call.end_line_number,
                old_text_lines[-1],
            )
            tool_calls[index] = dataclasses.replace(
                tool_call,
                start_line_number=new_start_line_number,
                end_line_number=new_end_line_number,
            )
    return tool_calls


def apply_tool_calls(before_file_contents, tool_calls):
    """Apply the tool calls to the before file."""
    tool_calls.sort(key=lambda x: x.start_line_number)
    tool_calls = list(reversed(tool_calls))
    after_file_lines = before_file_contents.splitlines(keepends=True)

    for tool_call in tool_calls:
        after_file_lines[
            tool_call.start_line_number - 1 : tool_call.end_line_number
        ] = tool_call.replacement_text.splitlines(keepends=True)
    after_file_contents = "".join(after_file_lines)
    return after_file_contents


def parse_from_text_and_apply_tool_calls(before_file_contents, claude_response):
    """Parse and apply the tool calls to the before file."""
    tool_calls = parse_replace_text_tool_call(claude_response)
    tool_calls = adjust_tool_calls(before_file_contents, tool_calls)
    after_file_contents = apply_tool_calls(before_file_contents, tool_calls)
    return after_file_contents


def extract_from_response_and_apply_tool_calls(before_file_contents, claude_response):
    """Parse and apply the tool calls to the before file."""
    tool_calls = []
    for message in claude_response.content:
        if message.type == "tool_use":
            # Claude writes it w/o newline at the end
            if "replacement_text" not in message.input:
                raise InvalidToolCall("replacement_text is not in the tool call")
            if "old_text" not in message.input:
                raise InvalidToolCall("old_text is not in the tool call")
            if "start_line_number" not in message.input:
                raise InvalidToolCall("start_line_number is not in the tool call")
            if "end_line_number" not in message.input:
                raise InvalidToolCall("end_line_number is not in the tool call")
            replacement_text = message.input["replacement_text"] + "\n"
            tool_calls.append(
                ReplaceTextToolCall(
                    old_text=message.input["old_text"],
                    start_line_number=message.input["start_line_number"],
                    end_line_number=message.input["end_line_number"],
                    replacement_text=replacement_text,
                )
            )
    if len(tool_calls) == 0:
        return before_file_contents
    tool_calls = adjust_tool_calls(before_file_contents, tool_calls)
    after_file_contents = apply_tool_calls(before_file_contents, tool_calls)
    return after_file_contents


def extract_from_dict_and_apply_tool_calls(before_file_contents, claude_response):
    """Parse and apply the tool calls to the before file."""
    tool_calls = []
    for message in claude_response:
        assert "type" in message, message
        if message["type"] == "tool_use":
            # Claude writes it w/o newline at the end
            if "replacement_text" not in message:
                raise InvalidToolCall("replacement_text is not in the tool call")
            if "old_text" not in message:
                raise InvalidToolCall("old_text is not in the tool call")
            if "start_line_number" not in message:
                raise InvalidToolCall("start_line_number is not in the tool call")
            if "end_line_number" not in message:
                raise InvalidToolCall("end_line_number is not in the tool call")
            replacement_text = message["replacement_text"] + "\n"
            tool_calls.append(
                ReplaceTextToolCall(
                    old_text=message["old_text"],
                    start_line_number=message["start_line_number"],
                    end_line_number=message["end_line_number"],
                    replacement_text=replacement_text,
                )
            )
    if len(tool_calls) == 0:
        return before_file_contents
    tool_calls = adjust_tool_calls(before_file_contents, tool_calls)
    after_file_contents = apply_tool_calls(before_file_contents, tool_calls)
    return after_file_contents


def build_smart_paste_claude_request(
    target_file: File,
    codeblock: str,
    chat_history: Iterable[Exchange],
    use_codeblock: bool = True,
    model="claude-3-5-sonnet-20240620",
):
    messages = []
    for exchange in chat_history:
        messages.extend(
            [
                {"role": "user", "content": exchange.request_message},
                {"role": "assistant", "content": exchange.response_text},
            ]
        )

    xml_code, _, _, _ = format_code(
        prefix="",
        selected_code=target_file.contents,
        suffix="",
        num_lines_in_prefix_suffix=int(1e9),
        is_highlighted=False,  # Using full file
    )

    if use_codeblock:
        prompt = f"""Great! Now please apply changes that you demonstrated in this codeblock:
    <change_to_apply>
    ```{codeblock}```
    </change_to_apply>
"""
    else:
        prompt = "Great! Now please apply changes that you demonstrated\n"

    prompt += f"""
to this file:

<file path="{target_file.path}">
{xml_code.rstrip()}
</file>

You should apply changes in a way that keeps the code style, spacing and indentation consistent and reasonable!

Please, do it using single or multiple calls to the `replace_text` tool.
Requirements to tool calls:
- All these calls should be done all ALL AT ONCE, NOT sequentially.
- All calls should use DISJOINT ranges of lines.
- Prefer doing local changes. If you need to change small part of a large class/function/method/etc, change it directly, WITHOUT replacing the whole class/function/method/etc.
"""

    messages.append({"role": "user", "content": prompt})

    system_prompt = """\
You are highly advanced and intelligent AI code assistant.
Your task is to carefully apply changes to a file based on the conversation history and the user's instructions.
"""

    return {
        "model": model,
        "max_tokens": 8192,
        "messages": messages,
        "system": system_prompt,
        "temperature": 0,
        "tools": TOOLS,
    }

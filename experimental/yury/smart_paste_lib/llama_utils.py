"""Utils for using prompted LLama for smart paste."""

SMART_PASTE_PROMPT = """\
Great! Now let's incorporate some of your suggested changes.

Here's the current file `{target_path}`:

```
{target_file_contents}
```

Apply the following changes ONLY.

```
{suggested_edit}
```

Follow these three steps to incorporate the suggested changes:

Step 1: Carefully describe the suggested changes. Pay close attention to the details of each change.

Step 2: Write down a plan for applying the suggested changes.
- For each change, specify precisely where it will be applied within the file.
- The plan should address only the changes outlined in the suggested edits — no further interpretation is required.

Step 3: Incorporate the changes
Rewrite the entire file content, incorporating all specified changes according to the plan. Follow these guidelines:
    - Output the FULL file content with the incorporated changes, even if only a small portion is modified.
    - Respect the original formatting exactly, including indentation style (e.g., spaces vs. tabs), line spacing, and any specific formatting details.
    - Do not alter any parts of the file outside the suggested changes.
    - Do NOT omit any of the original file content.
    - Do NOT add use any plaholders like "... (rest of the file)".

Use the following format:

**Step 1**
... description of the suggested changes ...

**Step 2**
... plan for applying the suggested changes ...

**Step 3**
... full, updated file content, wrapped in backticks (```), without any explanations, prefixes, or surrounding text ...
"""


SMART_PASTE_PROMPT_NOCODEBLOCK = """\
Great! Now let's incorporate ALL your suggested changes into the current file `{target_path}`:

```
{target_file_contents}
```

Follow these three steps to incorporate the suggested changes:

Step 1: Carefully describe ALL the suggested changes. Pay close attention to the details of each change.

Step 2: Write down a plan for applying the suggested changes.
- For each change, specify precisely where it will be applied within the file.
- The plan should address only the changes described in the suggested edits — no further interpretation is required.

Step 3: Incorporate the changes
Rewrite the entire file content, incorporating all specified changes according to the plan. Follow these guidelines:
    - Output the FULL file content with the incorporated changes, even if only a small portion is modified.
    - Respect the original formatting exactly, including indentation style (e.g., spaces vs. tabs), line spacing, and any specific formatting details.
    - Do not alter any parts of the file outside the suggested changes.
    - Do NOT omit any of the original file content.
    - Do NOT add use any plaholders like "... (rest of the file)".

Use the following format:

**Step 1**
... description of the suggested changes ...

**Step 2**
... plan for applying the suggested changes ...

**Step 3**
... full, updated file content, wrapped in backticks (```), without any explanations, prefixes, or surrounding text ...
"""


MODIFIED_FILE_START_KEYWORD = "**Step 3**"


def extract_modified_file_from_response(text: str) -> str | None:
    """Extract the markdown code block from the given response."""
    keyword_idx = text.find(MODIFIED_FILE_START_KEYWORD)
    if keyword_idx == -1:
        return None
    text = text[keyword_idx + len(MODIFIED_FILE_START_KEYWORD) :]

    openbackticks_idx = text.find("```")
    if openbackticks_idx == -1:
        return None
    openbackticks_idx = text.find("\n", openbackticks_idx + 1) + 1
    if openbackticks_idx == -1:
        return None
    closebackticks_idx = text.rfind("```")
    if closebackticks_idx == -1:
        return None
    if openbackticks_idx >= closebackticks_idx:
        return None
    return text[openbackticks_idx:closebackticks_idx]


LIOR_PROMPT = """\
Original file content of {target_path}:
```
{target_file_contents}
```

Requested changes:

{suggested_edit}

Your task is to rewrite the entire file content, incorporating all specified changes.
Follow the following guidelines to apply the changes:
    - Carefully review the provided description for changes to be made to the file.
    - Rewrite the entire file content, incorporating all specified changes.
    - Include the full content of the file in your response, even if only a small portion is modified.
    - Maintain the original file structure, formatting, and indentation unless explicitly instructed otherwise.
    - If the file contains code, ensure that the changes do not introduce syntax errors or logical inconsistencies.
    - Your response should contain only the updated file content, without any explanations, prefixes, or surrounding text and especially no surrounding backticks (```).
"""

LIOR_PROMPT_NOCODEBLOCK = """\
Great! Now let's incorporate ALL your suggested changes into the current file `{target_path}`:

```
{target_file_contents}
```

Your task is to rewrite the entire file content, incorporating ALL specified changes.
Follow the following guidelines to apply the changes:
    - Carefully review the provided description for changes to be made to the file.
    - Rewrite the entire file content, incorporating all specified changes.
    - Include the full content of the file in your response, even if only a small portion is modified.
    - Maintain the original file structure, formatting, and indentation unless explicitly instructed otherwise.
    - If the file contains code, ensure that the changes do not introduce syntax errors or logical inconsistencies.
    - Your response should contain only the updated file content, without any explanations, prefixes, or surrounding text and especially no surrounding backticks (```).
"""


def lior_extract_modified_file_from_response(text: str) -> str | None:
    """Extract the markdown code block from the given response."""
    if text.strip().startswith("```"):
        openbackticks_idx = text.find("```")
        if openbackticks_idx == -1:
            return None
        openbackticks_idx = text.find("\n", openbackticks_idx + 1) + 1
        if openbackticks_idx == -1:
            return None
        closebackticks_idx = text.rfind("```")
        if closebackticks_idx == -1:
            return None
        if openbackticks_idx >= closebackticks_idx:
            return None
        return text[openbackticks_idx:closebackticks_idx]
    else:
        return text

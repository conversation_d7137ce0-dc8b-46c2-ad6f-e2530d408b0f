{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'fastforward_deepseek_coder_instruct_7b_fp8'}\n"]}], "source": ["import json\n", "import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "from research.eval.harness.factories import create_model\n", "import research.models.fastforward_llama_models\n", "\n", "import torch\n", "\n", "model = create_model({\n", "    \"name\": \"fastforward_deepseek_coder_instruct_7b_fp8\",\n", "})"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "torch.cuda.device_count()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id b349fddb-f3ae-42b1-9042-89a163895ea7.\n"]}], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Sure, here are the implementations of Quick Sort in Python and Java:\n", "\n", "Python:\n", "```python\n", "def quick_sort(arr):\n", "    if len(arr) <= 1:\n", "        return arr\n", "    pivot = arr[len(arr) // 2]\n", "    left = [x for x in arr if x < pivot]\n", "    middle = [x for x in arr if x == pivot]\n", "    right = [x for x in arr if x > pivot]\n", "    return quick_sort(left) + middle + quick_sort(right)\n", "```\n", "\n", "Java:\n", "```java\n", "public class QuickSort {\n", "    static void quickSort(int[] array, int begin, int end) {\n", "        if (begin < end) {\n", "            int partitionIndex = partition(array, begin, end);\n", "\n", "            quickSort(array, begin, partitionIndex - 1);\n", "            quickSort(array, partitionIndex + 1, end);\n", "        }\n", "    }\n", "\n", "    static int partition(int[] array, int begin, int end) {\n", "        int pivot = array[end];\n", "        int i = (begin - 1);\n", "\n", "        for (int j = begin; j < end; j++) {\n", "            if (array[j] <= pivot) {\n", "                i++;\n", "\n", "                int swapTemp = array[i];\n", "                array[i] = array[j];\n", "                array[j] = swapTemp;\n", "            }\n", "        }\n", "\n", "        int swapTemp = array[i + 1];\n", "        array[i + 1] = array[end];\n", "        array[end] = swapTemp;\n", "\n", "        return i + 1;\n", "    }\n", "}\n", "```\n", "\n", "These are the basic implementations of Quick Sort. The Python version uses list comprehensions to partition the array, while the Java version uses a traditional for loop to partition the array. Both versions are recursive, calling themselves with the left and right partitions until the array is sorted.\n", "\n"]}], "source": ["from research.models.meta_model import GenerationOptions\n", "from research.core.model_input import ModelInput\n", "\n", "options = GenerationOptions(max_generated_tokens=1024)\n", "print(model.generate(ModelInput(\"write quick sort in Python and JAVA\"), options))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
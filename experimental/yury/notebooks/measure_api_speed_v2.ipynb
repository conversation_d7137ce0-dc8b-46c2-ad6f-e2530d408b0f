{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.retrieval.types import Document, Chunk\n", "\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/yury/benchmark_api/ovivo_ios_only_cs.ethanol6_query_simple_chat.json\")\n", "\n", "with DATA_PATH.open(\"r\") as f:\n", "    ovivo_data = json.load(f)\n", "\n", "for sample in ovivo_data:\n", "    for retrieved_chunk in sample['retrieved_chunks']:\n", "        retrieved_chunk['parent_doc'] = Document(**retrieved_chunk['parent_doc'])\n", "    sample['retrieved_chunks'] = [Chunk(**r) for r in sample['retrieved_chunks']]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["ANTHROPIC_TEMPLATE = \"\"\"You are an AI code assistant integrated into the VSCode IDE. Your primary role is to help software developers by answering their questions related to code and software engineering.\n", "\n", "Relevant code snippets from the developer's project are provided as background information.\n", "{% for chunk in retrieved_chunks %}\n", "# {{chunk.parent_doc.path}}\n", "{{chunk.text}}\n", "{% endfor %}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (32473 > 16384). Running this sequence through the model will result in indexing errors\n"]}, {"data": {"text/plain": ["'You are an AI code assistant integrated into the VSCode IDE. Your primary role is to help software developers by answering their questions related to code and software engineering.\\n\\nRelevant code snippets from the developer\\'s project are provided as background information.\\n\\n# Assets/Standard Assets/InControl/Examples/KeyboardAndMouseProfile/KeyboardAndMouseProfile.cs\\n// This file is intentionally left blank to prevent errors because \\n// Unity does not delete asset folders when importing new versions.\\n\\n# Assets/Scripts/Localization/LocalizationData.cs\\n\\ufeffusing System;\\n\\n[System.Serializable]\\npublic class LocalizationData \\n{\\n    public LocalizationItem[] items;\\n}\\n\\n[System.Serializable]\\npublic class LocalizationItem\\n{\\n    public string key;\\n    public string value;\\n}\\n\\n# ExportWP8/OVIVO/MainPage.xaml.cs\\n\\t}\\n}\\n \\n\\n# Assets/Standard Assets/InControl/Source/Control/InputControlBase.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Character/MainCamera.cs\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ProfileListGenerator.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityInputDevice.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityDevice.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing System.IO;\\nusing System;\\n\\npublic class LocalizationManager : MonoBehaviour {\\n\\n    public static LocalizationManager instance;\\n\\n    private Dictionary<string, string> localizedText;\\n    private Dictionary<string, string> localizedTextEn;\\n    private bool isReady = false;\\n    private string missingTextString = \"Localized text not found\";\\n\\n    // Use this for initialization\\n    void Awake () \\n    {\\n        if (instance == null) {\\n            instance = this;\\n        } else if (instance != this)\\n        {\\n            Destroy (gameObject);\\n        }\\n\\n        DontDestroyOnLoad (gameObject);\\n\\n        LoadLocalizedText ();\\n    }\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityInputDeviceProfile.cs\\n// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Curves Demo/Scripts/Audio/AudioCameraZoom.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityDeviceProfile.cs\\n// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Environment/LightVisibleEffect.cs\\n\\t\\tstartScale = transform.localScale; \\n\\t\\ttimer_ = timer;\\n\\t\\tspeed_ = speed;\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\tScale();\\n\\t}\\n}\\n\\n\\n# Assets/Scripts/Localization/LocalizedText.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing UnityEngine.UI;\\n\\npublic class LocalizedText : MonoBehaviour {\\n\\n    public string key;\\n\\n    // Use this for initialization\\n    void Start () \\n    {\\n        Text text = GetComponent<Text> ();\\n        string localizedText = LocalizationManager.instance.GetLocalizedValue (key);\\n        if (localizedText != null) {\\n            text.text = localizedText;\\n        } else {\\n            Debug.LogWarning (\"Localized text for \\'\" + key + \"\\' is missing!\");\\n        }\\n        text.horizontalOverflow = HorizontalWrapMode.Wrap;\\n        text.verticalOverflow = VerticalWrapMode.Overflow;\\n    }\\n}\\n\\n# Assets/Scripts/Mobile/MobileMenu.cs\\n        symbOnPopUp [num % 3].GetComponent<SVGImage> ().enabled = true;\\n\\n        Canvas canvas = GetComponent<Canvas>();\\n        Vector2 pos = Character.Instance.mainCamera.GetCamera().WorldToViewportPoint(collectedSymbol.transform.position);\\n        symbOnPopUp [num % 3].GetComponent<RectTransform>().anchoredPosition = new Vector2(pos.x * canvas.GetComponent<RectTransform>().sizeDelta.x, pos.y * canvas.GetComponent<RectTransform>().sizeDelta.y) - canvas.GetComponent<RectTransform>().sizeDelta/2f;\\n\\n        //OrthoSize: 9.304726referencePixelsPerUnit: 100scaleFactor: 0.4sizeelta: 50   768x447\\n        //OrthoSize: 9.523712referencePixelsPerUnit: 100scaleFactor: 0.6614583sizeelta: 50   1270x668\\n\\n        //        Debug.LogError(\"OrthoSize: \" + Character.Instance.mainCamera.GetCamera().orthographicSize.ToString() +\\n        //            \"referencePixelsPerUnit: \" + canvas.referencePixelsPerUnit.ToString() +\\n        //            \"scaleFactor: \" + canvas.scaleFactor.ToString() +\\n        //            \"sizeelta: \" + symbOnPopUp[num % 3].GetComponent<RectTransform>().sizeDelta.x.ToString());\\n        //float a = (Character.Instance.mainCamera.GetCamera().orthographicSize * canvas.referencePixelsPerUnit * canvas.scaleFactor/1.5f) / (symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta.x * 2); // * symbol scale\\n\\n        float a = (1920f / Screen.width) * symbolRect.width;\\n\\n        //float a = (Character.Instance.mainCamera.GetCamera().orthographicSize * canvas.referencePixelsPerUnit / 10f) / (symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta.x / symbOnPopUp [num % 3].transform.localScale.x); // * symbol scale\\n        ////obj.sizeDelta = target.GetComponent<RectTransform>().sizeDelta;\\n        //symbOnPopUp [num % 3].transform.localScale = Vector3.one * a; // * collectedSymbol.transform.localScale.x;\\n        symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta = Vector2.one * a;\\n        ////StartCoroutine(UpdateScale(collectedSymbol));\\n\\n        symbOnPopUp[num % 3].GetComponent<RectTransform>().DOSizeDelta(Vector2.one * 50, 2.5f);\\n        //symbOnPopUp [num % 3].transform.DOScale(Vector3.one, 2.5f);\\n\\n        symbOnPopUp [num % 3].transform.DOMove(oldPosition, 2.5f);\\n        //symbOnPopUp [num % 3].transform.do\\n\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/InputDeviceProfile.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Utils/SVGGUI.cs\\n}\\n\\n# Assets/NativeReviewRequest/NativeReviewRequest.cs\\n\\ufeff/*\\n * Made by Kamen Dimitrov, http://www.studio-generative.com\\n */\\n\\nusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing System.Runtime.InteropServices;\\n\\npublic class NativeReviewRequest {\\n\\n\\tpublic static void RequestReview() {\\n\\t\\t#if UNITY_IOS && !UNITY_EDITOR\\n\\t\\trequestReview();\\n\\t\\t#endif\\n\\t}\\n\\n\\t#if UNITY_IOS && !UNITY_EDITOR\\n\\t[DllImport (\"__Internal\")] private static extern void requestReview();\\n\\t#endif\\n}\\n\\n\\n# Assets/Scripts/Mobile/RoundWindowController.cs\\n}\\n\\n\\n# Assets/PostProcessing/Editor/PostProcessingBehaviourEditor.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Editor/InControlBuilder.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Editor/iOS/XCodeAPI/PBX/Serializer.cs\\n\\n\\n\\n# Assets/PostProcessing/Runtime/Models/AntialiasingModel.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Mobile/MobileMenu.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/ColliderEnableSwitcher.cs\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/SVGImporterSettings.cs\\n\\n}\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\n\\t\\treturn p;\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/EightBitdoNES30ProWindowsProfile.cs\\n\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n    private Dictionary<string, string> LoadLocalizedText(string lang) {\\n        var res = new Dictionary<string, string> ();\\n        try {\\n            var resource = Resources.Load(Path.Combine(\"Localization\", lang + \".txt\"));\\n            resource = Resources.Load(Path.Combine(\"Localization\", lang));\\n            string dataAsJson = ((TextAsset) resource).text;\\n            LocalizationData loadedData = JsonUtility.FromJson<LocalizationData> (dataAsJson);\\n\\n            for (int i = 0; i < loadedData.items.Length; i++) \\n            {\\n                res.Add (loadedData.items [i].key, loadedData.items [i].value);   \\n            }\\n\\n            Debug.Log (\"LocalizationManager. Data loaded for \" + lang + \", dictionary contains: \" + res.Count + \" entries\");\\n        } catch (Exception e) {\\n            Debug.LogWarning (\"LocalizationManager. Could not load data for \" + lang + \" Exception: \" + e);\\n        }\\n        return res;\\n    }\\n\\n    public void LoadLocalizedText()\\n    {\\n        var lang = Application.systemLanguage;\\n\\n        localizedText = LoadLocalizedText (lang.ToString ());\\n        localizedTextEn = LoadLocalizedText (SystemLanguage.English.ToString ());\\n        isReady = true;\\n    }\\n\\n    public string GetLocalizedValue(string key)\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF510ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF710ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/OuyaEverywhere/OuyaEverywhereDeviceManager.cs\\n\\t}\\n}\\n\\n\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\n\\t\\t\\t}\\n\\n        }\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHlJREFUeNpiFBER+f/jxw8GNjY2BnqAX79+MXBwcDAwMQwQGHoWv3nzBoxHjo8pBSykBi8+MWAOGWY+5uLigrO/ffuGIYbMppnF5Fg2tFM1yKfk+pbkoKZGEA+OVP3nzx+6WQizi/H///8MoqKi/+np2y9fvjACBBgAoTYjgvihfz0AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAD1JREFUeNpi/P//P4OKisp/Bii4c+cOIwtIwMXFheHFixcMEhISYAVMINm3b9+CBUA0CDCiazc0NGQECDAAdH0YelA27kgAAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACRJREFUeNpizM3N/c9AADAqKysTVMTi5eXFSFAREFPHOoAAAwBCfwcAO8g48QAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi/P//PwM+wHL06FG8KpgYCABGZWVlvCYABBgA7/sHvGw+cz8AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi/P//PwM+wPKfgAomBgKAhYuLC68CgAADAAxjByOjCHIRAAAAAElFTkSuQmCC\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAADtJREFUeNpi/P//P4OKisp/Bii4c+cOIwtIQE9Pj+HLly9gQRCfBcQACbx69QqmmAEseO/ePQZkABBgAD04FXsmmijSAAAAAElFTkSuQmCC\"\\n\\t\\t};\\n\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"add_button.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texAddButton\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.add_button]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"add_button_active.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texAddButtonActive\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.add_button_active]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"container_background.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texContainerBackground\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.container_background]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF310ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Curves Demo/Scripts/Layout/InstancerSpiral.cs\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouchControls/Examples/Version 2.X_New Features/Script/CharacterAnimationDungeon.cs\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickDrag.cs\\n\\t#endregion\\n}\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/InControlInputModuleEditor.cs\\n}\\n#endif\\n\\n# Assets/Scripts/Environment/CheckpointManager.cs\\n}\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n    {\\n        if (localizedText.ContainsKey (key)) {\\n            return localizedText [key];\\n        } else if (localizedTextEn.ContainsKey (key)) {\\n            return localizedTextEn [key];\\n        } else {\\n            return null;\\n        }\\n\\n    }\\n\\n    public bool IsReady()\\n    {\\n        return isReady;\\n    }\\n}\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Core/SVGAssetEditor.cs\\n\\t\\tpublic static GUIContent ANTIALIASING_WIDTH_LABEL = new GUIContent(\"Antialiasing Width\", \"Increase or decrease the antialiasing width per pixel\");\\n        public static GUIContent MESH_COMPRESSION_LABEL = new GUIContent(\"Mesh Compression\", \"Reduce file size of the mesh, but might introduce irregularities and visual artefacts.\");\\n        public static string MESH_COMPRESSION_HELPBOX_LABEL = \"Mesh compression can introduce unwanted visual artefacts.\\\\nThe higher the compression, the higher the risk.\";\\n        public static GUIContent OPTIMIZE_MESH_LABEL = new GUIContent(\"Optimize Mesh\", \"The vertices and indices will be reorderer for better GPU performance.\");\\n        public static GUIContent SCALE_LABEL = new GUIContent(\"Scale\", \"The scale of the mesh relative to the SVG Asset. Does not affect the quality of the mesh\");\\n        public static GUIContent QUALITY_LABEL = new GUIContent(\"Quality\", \"Larger number means better but more complex mesh, Vertex Per Meter represents number of vertices in the SVG Asset that correspond to one unit in world space.\");\\n        public static GUIContent DEPTH_OFFSET_LABEL = new GUIContent(\"Depth Offset\", \"The minimal z-offset in WorldSpace for Opaque Rendering.\");\\n        public static GUIContent COMPRESS_DEPTH_LABEL = new GUIContent(\"Compress Depth\", \"Compresses the overlapping objects to reduce z-offset requirements.\");\\n        public static GUIContent CUSTOM_PIVOT_LABEL = new GUIContent(\"Custom Pivot\", \"Choose the predefined pivot point or the custom pivot point.\");\\n        public static GUIContent PIVOT_LABEL = new GUIContent(\"Pivot\", \"The location of the SVG Asset center point in the original Rect, specified in percents.\");\\n        public static GUIContent GENERATE_COLLIDER_LABEL = new GUIContent(\"Generate Collider\", \"Automatically generates polygon colliders.\");\\n        public static GUIContent KEEP_SVG_FILE_LABEL = new GUIContent(\"Keep SVG File\", \"Keep the SVG file in the final build. This increases the file size.\");\\n\\t\\tpublic static GUIContent USE_LAYERS_LABEL = new GUIContent(\"Use Layers\", \"Store individual SVG Layers for further modification. This discards stored Mesh.\");\\n        public static GUIContent IGNORE_SVG_CANVAS_LABEL = new GUIContent(\"Ignore SVG Canvas\", \"Trim the document canvas to object bounding box.\");\\n        public static GUIContent GENERATE_NORMALS_LABEL = new GUIContent(\"Normals\", \"Generate normals for lighting effects.\");\\n        public static GUIContent GENERATE_TANGENTS_LABEL = new GUIContent(\"Tangents\", \"Generate Tangents for advanced lighting effects.\");\\n\\n        void OnFilesValid()\\n        {\\n\\t\\t\\tbool valueChanged = false;\\n\\n            serializedObject.Update();\\n            EditorGUI.BeginChangeCheck();\\n            EditorGUILayout.LabelField(\"Rendering\", EditorStyles.boldLabel);\\n            EditorGUILayout.PropertyField(format, FORMAT_LABEL);\\n            EditorGUILayout.PropertyField(useGradients, USE_GRADIENTS_LABEL);\\n            EditorGUILayout.PropertyField(antialiasing, ANTIALIASING_LABEL);            \\n            EditorGUILayout.Space();\\n\\n            EditorGUILayout.LabelField(\"Meshes\", EditorStyles.boldLabel);\\n\\n\\n# Assets/Scripts/Environment/Boolque.cs\\n\\t\\treturn result;\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/EightBitdoNES30ProMacProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/Native.cs\\n\\n#endif\\n\\n\\t}\\n\\t//@endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Utilities/DragCorrector.cs\\n}\\n\\n# Assets/Scripts/Utilities/Game.cs\\n\\n        if (LogoSceneHasFinishedEvent != null)\\n            LogoSceneHasFinishedEvent ();\\n    }\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/ReorderableListFlags.cs\\n#endif\\n\\n\\n# Assets/PostProcessing/Editor/Models/GrainModelEditor.cs\\n}\\n\\n\\n# Assets/PostProcessing/Editor/Monitors/VectorscopeMonitor.cs\\n}\\n\\n\\n# Assets/Scripts/Mobile/ControlsAnimation.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\n\\npublic class ControlsAnimation : MonoBehaviour {\\n\\n\\t// Use this for initialization\\n\\tvoid Start () {\\n\\t\\t\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\t\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Control/TwoAxisInputControl.cs\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Engine/Finger.cs\\n\\n\\n\\n\\n# Assets/PostProcessing/Runtime/Models/DepthOfFieldModel.cs\\n        }\\n    }\\n}\\n\\n\\n# Assets/PostProcessing/Runtime/Components/ChromaticAberrationComponent.cs\\n        }\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Mac/XTR_G2_MacNativeProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/QuadTree.cs\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Editor/EasyTouchMenu.cs\\n\\t\\tDebug.Log(\"Project must be reloaded when reverting folder structure.\");\\n\\t\\tEditorApplication.OpenProject(Application.dataPath.Remove(Application.dataPath.Length - \"Assets\".Length, \"Assets\".Length));\\n\\t}*/\\n\\n# Assets/Scripts/Utilities/ChangeFixedTimestepTrigger.cs\\n\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouchControls/Plugins/ETCDPad.cs\\n\\t\\t\\t\\t\\t\\t\\n\\t}\\n\\t#endregion\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickTap.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/XTR_G2_WindowsNativeProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Editor/EasytouchHierachyCallBack.cs\\n\\t\\t\\n}\\n\\n\\n# Assets/Scripts/!Temp/DetectGamepad.cs\\n\\t\\t}\\n\\n\\t}\\n\\n}\\n\\n\\n# Assets/Scripts/Environment/EnvironmentControl.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\npublic class EnvironmentControl : MonoBehaviour {\\n\\n\\t// Use this for initialization\\n\\tvoid Start () {\\n\\t\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\n\\t}\\n}\\n\\n\\n# Assets/PostProcessing/Runtime/PostProcessingProfile.cs\\n            public float vectorscopeExposure = 0.12f;\\n            public bool vectorscopeShowBackground = true;\\n        }\\n\\n        public MonitorSettings monitors = new MonitorSettings();\\n#endif\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Device/InputDevice.cs\\n\\t\\tInputControl cachedDPadY;\\n\\t\\tInputControl cachedCommand;\\n\\n\\n\\t\\tpublic InputControl LeftStickUp { get { return cachedLeftStickUp ?? (cachedLeftStickUp = GetControl( InputControlType.LeftStickUp )); } }\\n\\t\\tpublic InputControl LeftStickDown { get { return cachedLeftStickDown ?? (cachedLeftStickDown = GetControl( InputControlType.LeftStickDown )); } }\\n\\t\\tpublic InputControl LeftStickLeft { get { return cachedLeftStickLeft ?? (cachedLeftStickLeft = GetControl( InputControlType.LeftStickLeft )); } }\\n\\t\\tpublic InputControl LeftStickRight { get { return cachedLeftStickRight ?? (cachedLeftStickRight = GetControl( InputControlType.LeftStickRight )); } }\\n\\t\\tpublic InputControl RightStickUp { get { return cachedRightStickUp ?? (cachedRightStickUp = GetControl( InputControlType.RightStickUp )); } }\\n\\t\\tpublic InputControl RightStickDown { get { return cachedRightStickDown ?? (cachedRightStickDown = GetControl( InputControlType.RightStickDown )); } }\\n\\t\\tpublic InputControl RightStickLeft { get { return cachedRightStickLeft ?? (cachedRightStickLeft = GetControl( InputControlType.RightStickLeft )); } }\\n\\t\\tpublic InputControl RightStickRight { get { return cachedRightStickRight ?? (cachedRightStickRight = GetControl( InputControlType.RightStickRight )); } }\\n\\t\\tpublic InputControl DPadUp { get { return cachedDPadUp ?? (cachedDPadUp = GetControl( InputControlType.DPadUp )); } }\\n\\t\\tpublic InputControl DPadDown { get { return cachedDPadDown ?? (cachedDPadDown = GetControl( InputControlType.DPadDown )); } }\\n\\t\\tpublic InputControl DPadLeft { get { return cachedDPadLeft ?? (cachedDPadLeft = GetControl( InputControlType.DPadLeft )); } }\\n\\t\\tpublic InputControl DPadRight { get { return cachedDPadRight ?? (cachedDPadRight = GetControl( InputControlType.DPadRight )); } }\\n\\t\\tpublic InputControl Action1 { get { return cachedAction1 ?? (cachedAction1 = GetControl( InputControlType.Action1 )); } }\\n\\t\\tpublic InputControl Action2 { get { return cachedAction2 ?? (cachedAction2 = GetControl( InputControlType.Action2 )); } }\\n\\t\\tpublic InputControl Action3 { get { return cachedAction3 ?? (cachedAction3 = GetControl( InputControlType.Action3 )); } }\\n\\t\\tpublic InputControl Action4 { get { return cachedAction4 ?? (cachedAction4 = GetControl( InputControlType.Action4 )); } }\\n\\t\\tpublic InputControl LeftTrigger { get { return cachedLeftTrigger ?? (cachedLeftTrigger = GetControl( InputControlType.LeftTrigger )); } }\\n\\t\\tpublic InputControl RightTrigger { get { return cachedRightTrigger ?? (cachedRightTrigger = GetControl( InputControlType.RightTrigger )); } }\\n\\t\\tpublic InputControl LeftBumper { get { return cachedLeftBumper ?? (cachedLeftBumper = GetControl( InputControlType.LeftBumper )); } }\\n\\t\\tpublic InputControl RightBumper { get { return cachedRightBumper ?? (cachedRightBumper = GetControl( InputControlType.RightBumper )); } }\\n\\t\\tpublic InputControl LeftStickButton { get { return cachedLeftStickButton ?? (cachedLeftStickButton = GetControl( InputControlType.LeftStickButton )); } }\\n\\t\\tpublic InputControl RightStickButton { get { return cachedRightStickButton ?? (cachedRightStickButton = GetControl( InputControlType.RightStickButton )); } }\\n\\t\\tpublic InputControl LeftStickX { get { return cachedLeftStickX ?? (cachedLeftStickX = GetControl( InputControlType.LeftStickX )); } }\\n\\t\\tpublic InputControl LeftStickY { get { return cachedLeftStickY ?? (cachedLeftStickY = GetControl( InputControlType.LeftStickY )); } }\\n\\t\\tpublic InputControl RightStickX { get { return cachedRightStickX ?? (cachedRightStickX = GetControl( InputControlType.RightStickX )); } }\\n\\t\\tpublic InputControl RightStickY { get { return cachedRightStickY ?? (cachedRightStickY = GetControl( InputControlType.RightStickY )); } }\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/SteelSeriesStratusXLWinProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/SVG/DOM/Utilities/LiteStack.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnityInputDeviceProfile.cs\\n\\n\\t\\t#endregion\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Mesh/SVGLineUtils.cs\\n}\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\n[RequireComponent(typeof(BoxCollider2D))]\\npublic class Eye : MonoBehaviour \\n{\\n    public Transform eye;\\n\\tpublic Transform eyeLidUpper;\\n\\tpublic Transform eyeLidLower;\\n\\tpublic float eyeLidWonderCoef;\\n    public float actionRadius = 1;\\n    public float lerpCoef = 4f;\\n\\n\\n\\tpublic bool catEye = false;\\n    bool isActive = false;\\n\\n\\tprivate Vector3 eyeLidUpperOldPosition;\\n\\tprivate Vector3 eyeLidLowerOldPosition;\\n\\n\\t// Use this for initialization\\n\\tvoid Start () \\n    {\\n        GetComponent<Collider2D>().isTrigger = true;\\n\\n\\t\\tif(eyeLidUpper != null && eyeLidLower != null)\\n\\t\\t{\\n\\t\\t\\teyeLidUpperOldPosition = eyeLidUpper.localPosition;\\n\\t\\t\\teyeLidLowerOldPosition = eyeLidLower.localPosition;\\n\\t\\t}\\n\\n\\n# Assets/Scripts/Other/FirstLevelCamController.cs\\n\\t\\tnewPos = camera.ScreenToWorldPoint (Vector3.up * camera.pixelHeight*0.5f);\\n\\n\\t\\tif (newPos.x < startX)\\n\\t\\t\\tborder.transform.position = newPos;\\n\\t}\\n\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/Xbox360WinProfile.cs\\n}\\n\\n\\n\\n# Assets/Scripts/Mobile/DotsInfo.cs\\n            dotsCount[i] = dotsCodes[i].Length;\\n        }\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickLongTap.cs\\n\\t\\t\\n\\t\\treturn returnValue;\\n\\t}\\n}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/PlayStation4Profile.cs\\n}\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/ColorPicker/ColorImage.cs\\n}\\n\\n# Assets/Scripts/Environment/Symbol.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\nusing UnityEngine.UI;\\n\\npublic class Symbol : MonoBehaviour \\n{\\n\\tvoid Start () \\n\\t{\\n\\t\\tif (PlayerPrefs.HasKey (\"Symbol_\" + gameObject.name))\\n\\t\\t{\\n\\t\\t\\tif (PlayerPrefs.GetInt (\"Symbol_\" + gameObject.name) == 1)\\n\\t\\t\\t{\\n                /* LEGACY\\n\\t\\t\\t\\tMenu menu = GameObject.FindObjectOfType<Menu> ();\\n\\t\\t\\t\\tif (menu != null)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tint num = int.Parse (gameObject.name);\\n\\n\\t\\t\\t\\t\\tmenu.symbOnPopUp [num % 3].GetComponent<Image> ().enabled = true;\\n\\t\\t\\t\\t\\tmenu.symbOnPopUp [num % 3].GetComponent<Image> ().sprite = gameObject.GetComponent<SpriteRenderer> ().sprite;\\n\\t\\t\\t\\t\\tif (menu.additionalSymbsOnMandala [num] != null)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tmenu.additionalSymbsOnMandala [num].GetComponent<Image> ().enabled = true;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n*/\\n\\t\\t\\t\\tDestroy (gameObject);\\t\\n\\t\\t\\t}\\n\\t\\t} \\n\\t\\telse\\n\\n\\n# Assets/Scripts/Environment/Group.cs\\n            }\\n\\n    }\\n\\n}\\n\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\tpublic bool fusionWithMovingPlatform = false;\\n\\tpublic FluidEdge fluid;\\n\\n    public int movingPlatformDoEveryNFixedUpdate = 1;\\n\\n    Rigidbody2D rigidbody;\\n\\t//public bool unseenFirstly = false;\\n\\n\\tprivate const string DISTANCE_MESH_GAME_OBJECT_NAME = \"distance_mesh\";\\n\\n\\tprivate static Vector2 Perpendicular2d(Vector2 v) {\\n\\t\\treturn new Vector2 (v.y, -v.x);\\n\\t}\\n\\n    public void RemoveDistanceMesh()\\n    {\\n        for (var i = 0; i < transform.childCount; i++)\\n        {\\n            if (transform.GetChild(i).name == DISTANCE_MESH_GAME_OBJECT_NAME)\\n            {\\n                DestroyImmediate(transform.GetChild(i).gameObject);\\n                i--;\\n            }\\n        }\\n    }\\n\\n    void AlignZ(Transform obj)\\n    {\\n        if (obj.localScale.z != 1)\\n            obj.localScale = new Vector3(obj.localScale.x, obj.localScale.y, 1);\\n\\n\\n# Assets/Scripts/Mobile/LevelPreviewCamera.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing HedgehogTeam.EasyTouch;\\nusing DG.Tweening;\\n\\npublic class LevelPreviewCamera : MonoBehaviour \\n{\\n    public static LevelPreviewCamera Instance\\n    {\\n        get\\n        {\\n            return instance;\\n        }\\n    }\\n\\n    static LevelPreviewCamera instance;\\n    public static Camera cameraComponent;\\n    public static Transform transformComponent;\\n\\n    public Rect viewRect;\\n\\n    public float maxZoomValue, minZoomValue, minZoomValueForIPad;\\n    public float zoomSpeedPerUnit;\\n    public float scaleFactorPerPixel;\\n    public float translateFactorPerPixel;\\n    public bool disableCameraOnAwake = true;\\n\\n    public float finalZoomSpeedFactor = 0.02f;\\n    public bool isInputAllowed = false;\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/XML Parser/SmallXmlParser.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\n        Character.Instance.CollectedFinalSymbolEvent += delegate\\n            {\\n                this.enabled = false;\\n            };\\n\\t}\\n\\n    public void OnTriggerEnter2D(Collider2D col)\\n    {\\n        if (col.CompareTag(\"Character\"))\\n        {\\n            isActive = true;\\n        }\\n    }\\n\\n    public void OnTriggerExit2D(Collider2D col)\\n    {\\n        if (col.CompareTag(\"Character\"))\\n        {\\n            isActive = false;\\n        }\\n    }\\n\\n\\tvoid FixedUpdate () \\n    {\\t\\n        if (isActive)\\n        {\\n            Vector3 target = (Character.Instance.transform.position - transform.position).normalized * actionRadius;\\n            eye.localPosition = Vector3.Lerp(eye.localPosition, target, Time.fixedDeltaTime * lerpCoef);\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/PlayStation3WinProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/SVGRenderer.cs\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/SVG/DOM/BasicTypes/SVGLength.cs\\n}\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/BoxSlider.cs\\n}\\n\\n\\n# Assets/Scripts/Environment/Follower.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\nusing System;\\nusing DG.Tweening;\\n\\npublic class Follower: MonoBehaviour \\n{\\n    public GameObject topCollider;\\n    public Platform platform;\\n\\n\\tpublic Transform target;\\n\\tpublic float movingSpeed;\\n\\tpublic Transform flowerParent;\\n\\tpublic bool deathPlayed;\\n\\n\\t[HideInInspector]\\n\\tpublic bool isEnding = false;\\n\\n\\tpublic Vector2 respawnPlace;\\n\\n\\t//private float visionRadius;\\n\\tprivate Vector2 vectorToTarget;\\n\\tprivate bool onFlower;\\n\\n\\tprivate bool startFlying;\\n\\n\\tprivate bool flowerDeathAnimationIsPlayed = false;\\n\\tprivate bool isDead = false;\\n\\tprivate DateTime flowerDeathAnimationStartTime;\\n\\tprivate double flowerDeathAnimationLength= 2800;\\n\\n\\n# Assets/Standard Assets/InControl/Source/Binding/BindingSource.cs\\n\\t\\t}\\n\\n\\t\\t#endregion\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/OuyaEverywhere/OuyaEverywhereDevice.cs\\n\\n\\t\\t\\tvar rt = Utility.ApplyDeadZone(\\n\\t\\t\\t\\t         OuyaSDK.OuyaInput.GetAxisRaw( DeviceIndex, OuyaController.AXIS_R2 ),\\n\\t\\t\\t\\t         LowerDeadZone,\\n\\t\\t\\t\\t         UpperDeadZone \\n\\t\\t\\t         );\\n\\t\\t\\tUpdateWithValue( InputControlType.RightTrigger, rt, updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.DPadUp, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_UP ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadDown, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_DOWN ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadLeft, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_LEFT ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadRight, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_RIGHT ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.Action1, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_O ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action2, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_A ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action3, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_U ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action4, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_Y ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.LeftBumper, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_L1 ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.RightBumper, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_R1 ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.LeftStickButton, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_L3 ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.RightStickButton, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_R3 ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.Menu, OuyaSDK.OuyaInput.GetButtonDown( DeviceIndex, OuyaController.BUTTON_MENU ), updateTick, deltaTime );\\n\\n\\t\\t\\tCommit( updateTick, deltaTime );\\n\\t\\t\\t#endif\\n\\t\\t}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Examples/InterfaceMovement/Button.cs\\n\\t\\t}\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Engine/EasyTouchInput.cs\\n\\t\\treturn position;\\n\\t}\\n\\t#endregion\\n}\\n}\\n\\n\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\tpublic bool mainPlatform = false;\\n\\n\\tpublic bool IntoContour(Collider2D edge, Vector2 origin, Vector2 dir)\\n\\t{\\n\\t\\t//Vector2 p = new Vector2 (edge.transform.position.x, edge.transform.position.y);\\n\\t\\t//dir = ( p - origin).normalized;\\n\\n\\t\\tint layer = edge.gameObject.layer;\\n\\t\\tedge.gameObject.layer = LayerMask.NameToLayer (\"TempPlatform\");\\n\\n\\t\\tbool finalPoint = false;\\n\\t\\tint points = 0;\\n\\t\\t//Vector2 origin = new Vector2 (transform.position.x, transform.position.y);\\n\\t\\tRaycastHit2D hit;\\n\\n\\t\\twhile (!finalPoint)\\n\\t\\t{\\n\\t\\t\\thit = Physics2D.Raycast (origin, dir, 100f, 1 << LayerMask.NameToLayer (\"TempPlatform\"));\\n\\n\\t\\t\\tif (hit)\\n\\t\\t\\t{\\n\\t\\t\\t\\t++points;\\n\\t\\t\\t\\torigin = hit.point + dir * 0.0001f;\\n\\t\\t\\t} \\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tfinalPoint = true;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/PowerAMiniProExControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Scripts/Utilities/MorseCodeManager.cs\\n\\n    /* LEGACY\\n    static public string GetKey(int level)\\n    {\\n        return GetKey((Mandala.LevelName)level);\\n    }\\n\\n    static public string GetKey(Mandala.LevelName level)\\n    {\\n        return \"Morse_\" + level.ToString();\\n    }\\n\\n    static public string GetKey(string level)\\n    {\\n        return \"Morse_\" + level;\\n    } */\\n        \\n    void Awake()\\n    {\\n        instance = this;\\n    }\\n\\n    void Start()\\n    {\\n        if (!DataManager.IsInitialized)\\n            DataManager.Init();\\n        \\n        HideCollectedDots();\\n\\n    }\\n\\n\\n# Assets/Standard Assets/InControl/Plugins/iOS/ICadePluginPath.cs\\n\\ufeffnamespace InControl\\n{\\n\\tusing UnityEngine;\\n\\n\\n\\t// This file exists only so the build post processor can easily\\n\\t// find the iOS Xcode plugin files at the same path.\\n\\tpublic class ICadePluginPath : ScriptableObject\\n\\t{\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Editor/iOS/XCodeAPI/PBXProject.cs\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNDECLARED_SELECTOR\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNINITIALIZED_AUTOS\", \"YES_AGGRESSIVE\" );\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNUSED_FUNCTION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"INFOPLIST_FILE\", infoPlistPath );\\n\\t\\t\\tconfig.AddProperty( \"IPHONEOS_DEPLOYMENT_TARGET\", \"8.0\" );\\n\\t\\t\\tconfig.AddProperty( \"LD_RUNPATH_SEARCH_PATHS\", \"$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks\" );\\n\\t\\t\\tconfig.AddProperty( \"MTL_ENABLE_DEBUG_INFO\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"PRODUCT_NAME\", \"$(TARGET_NAME)\" );\\n\\t\\t\\tconfig.AddProperty( \"SKIP_INSTALL\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"VALIDATE_PRODUCT\", \"YES\" );\\n\\t\\t}\\n\\n\\t\\tprivate void SetDefaultAppExtensionDebugBuildFlags( XCBuildConfiguration config, string infoPlistPath )\\n\\t\\t{\\n\\t\\t\\tconfig.AddProperty( \"ALWAYS_SEARCH_USER_PATHS\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_CXX_LANGUAGE_STANDARD\", \"gnu++0x\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_CXX_LIBRARY\", \"libc++\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_ENABLE_MODULES\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_ENABLE_OBJC_ARC\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_BOOL_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_CONSTANT_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_DIRECT_OBJC_ISA_USAGE\", \"YES_ERROR\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_EMPTY_BODY\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_ENUM_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_INT_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_OBJC_ROOT_CLASS\", \"YES_ERROR\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_UNREACHABLE_CODE\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN__DUPLICATE_METHOD_MATCH\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"COPY_PHASE_STRIP\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"ENABLE_STRICT_OBJC_MSGSEND\", \"YES\" );\\n\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t\\t\\tremove_button,\\n\\t\\t\\tremove_button_active,\\n\\t\\t\\ttitle_background,\\n\\t\\t}\\n\\n\\t\\t/// <summary>\\n\\t\\t/// Resource assets for light skin.\\n\\t\\t/// </summary>\\n\\t\\t/// <remarks>\\n\\t\\t/// <para>Resource assets are PNG images which have been encoded using a base-64\\n\\t\\t/// string so that actual asset files are not necessary.</para>\\n\\t\\t/// </remarks>\\n\\t\\tprivate static string[] s_LightSkin = {\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAW0lEQVRIS+3NywnAQAhF0anI4mzVCmzBBl7QEBgGE5JFhBAXd+OHM5gZZgYRKcktNxu+HRFF2e6qhtOjtQM7K/tZ+xY89wSbazg9eqOfw6oag4rcChjY8coAjA2l1RxFDY8IFAAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAGlJREFUeNpiFBER+f/jxw8GNjY2BnqAX79+MXBwcDAwMQwQGHoWnzp1CoxHjo8pBSykBi8+MTMzs2HmY2QfwXxKii9HExdZgNwgHuFB/efPH7pZCLOL8f///wyioqL/6enbL1++MAIEGABvGSLA+9GPZwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAMElEQVQYV2P4//8/Q1FR0X8YBvHBAp8+ffp/+fJlMA3igwUfPnwIFgDRYEFM7f8ZAG1EOYL9INrfAAAAAElFTkSuQmCC\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAIElEQVQYV2P49OnTf0KYobCw8D8hzPD/P2FMLesK/wMAs5yJpK+6aN4AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAIAAADq9gq6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAABVJREFUeNpiVFZWZsAGmBhwAIAAAwAURgBt4C03ZwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAIAAADq9gq6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAABVJREFUeNpivHPnDgM2wMSAAwAEGAB8VgKYlvqkBwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAEFJREFUeNpi/P//P0NxcfF/BgRgZP78+fN/VVVVhpCQEAZjY2OGs2fPNrCApBwdHRkePHgAVwoWnDVrFgMyAAgwAAt4E1dCq1obAAAAAElFTkSuQmCC\"\\n\\t\\t};\\n\\t\\t/// <summary>\\n\\t\\t/// Resource assets for dark skin.\\n\\t\\t/// </summary>\\n\\t\\t/// <remarks>\\n\\t\\t/// <para>Resource assets are PNG images which have been encoded using a base-64\\n\\t\\t/// string so that actual asset files are not necessary.</para>\\n\\t\\t/// </remarks>\\n\\t\\tprivate static string[] s_DarkSkin = {\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIBJREFUeNpiVFZW/u/i4sLw4sULBnoACQkJhj179jAwMQwQGHoWl5aWgvHI8TGlgIXU4MUn1t3dPcx8HB8fD2cvXLgQQ0xHR4c2FmMzmBTLhl5QYwt2cn1MtsXkWjg4gvrt27fgWoMeAGQXCDD+//+fQUVF5T89fXvnzh1GgAADAFmSI1Ed3FqgAAAAAElFTkSuQmCC\",\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/SVGLineData.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/ObjectSimpleMotions.cs\\n\\n\\t}\\n\\n\\t// изменение размера как трансформ\\n\\tvoid Scale(){\\n\\t\\ttransform.localScale += Vector3.one * speed_ * DeltaTime();\\n\\t\\tTimeToChangeDirection ();\\n\\t}\\n\\n    private Quaternion initialTranformRotation;\\n    private Vector3 initialTransformPosition;\\n    private float initialRigidbodyRotation;\\n    private Vector2 initialRigidbodyPosition;\\n\\t\\t\\n\\tvoid Start() {\\n\\n\\t\\tqStart = Quaternion.AngleAxis (angle_start, Vector3.forward);\\n\\t\\tqEnd   = Quaternion.AngleAxis (angle_end, Vector3.forward);\\n\\t\\toriginalAngle = transform.rotation;\\n\\t\\ttimer_ = timer;\\n\\t\\tspeed_ = speed;\\n\\n\\t\\trigidbody2D_ = GetComponent<Rigidbody2D> ();\\n\\n        initialTranformRotation = transform.rotation;\\n        initialTransformPosition = transform.position;\\n        if (rigidbody2D_ != null) {\\n            initialRigidbodyRotation = rigidbody2D_.rotation;\\n            initialRigidbodyPosition = rigidbody2D_.position;\\n        }\\n\\n\\n# Assets/Scripts/Mobile/StartChapterAnimation.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing DG.Tweening;\\nusing SVGImporter;\\n\\npublic class StartChapterAnimation : MonoBehaviour \\n{\\n    [SerializeField] private float delayBeforeCharacterActivation;\\n\\n    private const float ANIMATOR_SCALE = .34f;\\n\\n    public static StartChapterAnimation Instance\\n    {\\n        get;\\n        private set;\\n    }\\n\\n    void Awake()\\n    {\\n        Instance = this;\\n    }\\n\\n    void Start()\\n    {\\n        if (MobileMenu.Instance == null)\\n        {\\n            Init();\\n        }\\n    }\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/MicrosoftXboxOneControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Scripts/Environment/TwinkleLight.cs\\n\\t{\\n\\t\\tOnTriggerEnter2D (col);\\n\\t}\\n\\t\\t\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/RockCandyXboxOneControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickTwist.cs\\n}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/NVidiaShieldWin10Profile.cs\\n}\\n\\n\\n\\n# Assets/Scripts/Other/TutorialStartAnimation.cs\\n            DataManager.UpdateLocalDataFile ();\\n        } else if (InGameUI.Instance.IsPlayingTapHintAnimation) {\\n            InGameUI.Instance.PlayTapAnimation();\\n        }\\n    }\\n\\n    void SetCameraCanMoveUp() {\\n        Character.Instance.mainCamera.CanMoveUp = true;\\n    }\\n\\t\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Core/SVGAssetEditor.cs\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n        }\\n    }\\n}\\n\\n\\n# Assets/PostProcessing/Editor/Monitors/ParadeMonitor.cs\\n                    Handles.DrawLine(F, new Vector3(F.x + kTickSize, F.y));\\n                    Handles.DrawLine(G, new Vector3(G.x + kTickSize, G.y));\\n                    Handles.DrawLine(H, new Vector3(H.x + kTickSize, H.y));\\n\\n                    // Horizontal ticks\\n                    Handles.DrawLine(A, new Vector3(A.x, A.y - kTickSize));\\n                    Handles.DrawLine(B, new Vector3(B.x, B.y - kTickSize));\\n                    Handles.DrawLine(C, new Vector3(C.x, C.y - kTickSize));\\n                    Handles.DrawLine(D, new Vector3(D.x, D.y - kTickSize));\\n                    Handles.DrawLine(O, new Vector3(O.x, O.y - kTickSize));\\n                    Handles.DrawLine(P, new Vector3(P.x, P.y - kTickSize));\\n                    Handles.DrawLine(Q, new Vector3(Q.x, Q.y - kTickSize));\\n\\n                    Handles.DrawLine(H, new Vector3(H.x, H.y + kTickSize));\\n                    Handles.DrawLine(I, new Vector3(I.x, I.y + kTickSize));\\n                    Handles.DrawLine(J, new Vector3(J.x, J.y + kTickSize));\\n                    Handles.DrawLine(K, new Vector3(K.x, K.y + kTickSize));\\n                    Handles.DrawLine(R, new Vector3(R.x, R.y + kTickSize));\\n                    Handles.DrawLine(S, new Vector3(S.x, S.y + kTickSize));\\n                    Handles.DrawLine(T, new Vector3(T.x, T.y + kTickSize));\\n\\n                    // Labels\\n                    GUI.color = color;\\n                    GUI.Label(new Rect(A.x - kTickSize - 34f, A.y - 15f, 30f, 30f), \"1.0\", FxStyles.tickStyleRight);\\n                    GUI.Label(new Rect(M.x - kTickSize - 34f, M.y - 15f, 30f, 30f), \"0.5\", FxStyles.tickStyleRight);\\n                    GUI.Label(new Rect(K.x - kTickSize - 34f, K.y - 15f, 30f, 30f), \"0.0\", FxStyles.tickStyleRight);\\n\\n                    GUI.Label(new Rect(D.x + kTickSize + 4f, D.y - 15f, 30f, 30f), \"1.0\", FxStyles.tickStyleLeft);\\n                    GUI.Label(new Rect(F.x + kTickSize + 4f, F.y - 15f, 30f, 30f), \"0.5\", FxStyles.tickStyleLeft);\\n                    GUI.Label(new Rect(H.x + kTickSize + 4f, H.y - 15f, 30f, 30f), \"0.0\", FxStyles.tickStyleLeft);\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/XTR55_G2_WindowsNativeProfile.cs\\n\\t// @endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Mac/XTR55_G2_MacNativeProfile.cs\\n\\t// @endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Utilities/Serialization/Surrogates/Vector3Surrogate.cs\\n}\\n\\n# Assets/Scripts/Other/TrailerScript.cs\\n        }\\n\\n\\n    }\\n\\n\\t// Use this for initialization\\n\\tvoid Start () \\n    {\\n        audio = GetComponent<AudioSource>();\\n        CreateStars();\\n        StartCoroutine(LinesAnimation());\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/MadCatzControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Utility/RingBuffer.cs\\n\\t\\t}\\n\\t}\\n}\\n\\n\\n\\n# Assets/Scripts/Mobile/LevelPreview.cs\\n            LevelPreviewCamera.Instance.isInputAllowed = value;\\n        }\\n    }\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t}\\n}\\n#endif\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/SelectionBox/IBoxSelectable.cs\\n}\\n\\n# Assets/Scripts/Environment/Platform.cs\\n            }\\n            catch (System.Exception ex)\\n            {\\n                Debug.LogError(name);\\n            }\\n        }\\n\\n        if (transform.localScale.z == 0)\\n            transform.localScale = new Vector3(transform.localScale.x, transform.localScale.y, 1);\\n\\n        Collider2D edgeCollider = GetComponent<Collider2D>();\\n\\n        if (edgeCollider)\\n        {\\n            isDisabledOnAwake = !edgeCollider.enabled;\\n        }\\n\\n        rigidbody = GetComponent<Rigidbody2D>();\\n\\n        //OnBecameInvisible();\\n\\t}\\n\\n    void OnBecameInvisible() \\n    {\\n        if (!isDisabledOnAwake && isCollidersControls)\\n        {\\n            Collider2D edgeCollider = GetComponent<Collider2D>();\\n\\n            if (edgeCollider)\\n            {\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/GameStickLinuxProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Hologram Demo/Scripts/HologramController.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\npublic class HologramController : MonoBehaviour {\\n\\n    [System.Serializable]\\n    public struct HologramLayer\\n    {\\n        public Transform transform;\\n        public Vector3 startLocalPosition;\\n        public float rotation;\\n    }\\n\\n    public HologramLayer[] layers;\\n    public float depth;\\n    public float depthSpeed = 1f;\\n    public AnimationCurve depthAnimation;\\n\\n    float elapsedTime;\\n\\n    // Use this for initialization\\n    void Start ()\\n    {\\t\\n        for(int i = 0; i < layers.Length; i++)\\n        {\\n            if (layers[i].transform == null) continue;\\n            layers[i].startLocalPosition = layers[i].transform.localPosition;\\n        }\\n\\t}\\n\\t\\n\\n\\n'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.core.model_input import ModelInput, ChatInput\n", "from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter\n", "\n", "def get_prompt(example, max_prompt_len):\n", "    prompt_formatter = ChatTemplateBasedPromptFormatter(\n", "        template=ANTHROPIC_TEMPLATE,\n", "        tokenizer_name=\"deepseekcoderinstructtokenizer\",\n", "        max_prompt_len=max_prompt_len,\n", "    )\n", "\n", "    model_input = ModelInput(\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        path=\"\",\n", "        chat_input=ChatInput(\n", "            request=example[\"question\"],\n", "            history=[],\n", "        ),\n", "        retrieved_chunks=example[\"retrieved_chunks\"],\n", "    )\n", "    return prompt_formatter.prepare_prompt(model_input)[1][\"prompt\"]\n", "\n", "max_prompt_len = 16_000\n", "get_prompt(ovivo_data[0], max_prompt_len)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import time\n", "\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "def run_anthropic(example, max_prompt_len, model_name):\n", "    client = anthropic.Anthropic(\n", "        api_key=\"************************************************************************************************************\",\n", "    )\n", "\n", "    system_prompt = get_prompt(example, max_prompt_len)\n", "\n", "    n_input_tokens, n_output_tokens = None, None\n", "    start_time = time.time()\n", "    first_token_time = None\n", "    text = []\n", "\n", "    with client.messages.stream(\n", "        max_tokens=1024,\n", "        messages=[{\"role\": \"user\", \"content\": example[\"question\"]}],\n", "        system=system_prompt,\n", "        temperature=0.0,\n", "        model=model_name,\n", "    ) as stream:\n", "        for event in stream:\n", "            if event.type == \"message_start\":\n", "                assert n_input_tokens is None\n", "                n_input_tokens = event.message.usage.input_tokens\n", "            elif event.type == \"content_block_start\":\n", "                assert len(event.content_block.text) == 0\n", "            elif event.type == \"content_block_delta\":\n", "                if first_token_time is None:\n", "                    assert len(event.delta.text) > 0\n", "                    first_token_time = time.time()\n", "                text.append(event.delta.text)\n", "            elif event.type == \"message_delta\":\n", "                assert n_output_tokens is None\n", "                n_output_tokens = event.usage.output_tokens\n", "        end_time = time.time()\n", "        return {\n", "            \"n_input_tokens_s\": n_input_tokens,\n", "            \"n_output_tokens_s\": n_output_tokens,\n", "            \"text\": \"\".join(text),\n", "            \"system_prompt\": system_prompt,\n", "            \"question\": example[\"question\"],\n", "            \"first_token_time_s\": first_token_time - start_time,\n", "            \"generation_time_s\": end_time - first_token_time,\n", "            \"generation_time_per_token_s\": (end_time - first_token_time) / n_output_tokens,\n", "        }\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/benchmark_api/anthropic_cache_v2.jsonl\")\n", "def run_anthropic_for_ovivo(index, max_prompt_len, model_name):\n", "    global ovivo_data\n", "    datum = run_anthropic(ovivo_data[index], max_prompt_len, model_name)\n", "    datum.update({\n", "        \"max_prompt_len\": max_prompt_len,\n", "        \"model_name\": model_name,\n", "        \"example_index\": index,\n", "    })\n", "    return datum"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "max_prompt_lens = [16_000, 32_000]\n", "# model_names = [\"claude-3-haiku-20240307\", \"claude-3-sonnet-20240229\", \"claude-3-opus-20240229\"]\n", "model_names = [\"claude-3-5-sonnet-20240620\"]\n", "\n", "anthropic_data = []\n", "\n", "for max_prompt_len in max_prompt_lens:\n", "    for model_name in model_names:\n", "        for index in range(len(ovivo_data)):\n", "            anthropic_data.append(run_anthropic_for_ovivo(index, max_prompt_len, model_name))\n", "\n", "anthropic_data = pd.DataFrame(anthropic_data)\n", "anthropic_data.groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import vertexai\n", "from vertexai.generative_models import GenerativeModel, ChatSession\n", "project_id = \"gemini-pro-420822\"\n", "location = \"us-central1\"\n", "vertexai.init(project=project_id, location=location)\n", "\n", "MODEL_NAME = \"gemini-1.5-flash-preview-0514\"\n", "GOOGLE_MODEL = GenerativeModel(MODEL_NAME)\n", "\n", "# GEMINI_PRO_MODEL_NAME = \"gemini-1.5-pro-preview-0514\"\n", "# GEMINI_FLASH_MODEL_NAME = \"gemini-1.5-flash-preview-0514\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (32473 > 16384). Running this sequence through the model will result in indexing errors\n"]}, {"data": {"text/plain": ["{'n_input_tokens_s': 13327,\n", " 'n_output_tokens_s': 497,\n", " 'text': 'The localization system in this project uses JSON files to store localized text. \\n\\nHere\\'s a breakdown of how it works:\\n\\n**1. LocalizationData Class:**\\n\\n- The `LocalizationData` class is a container for the localized text.\\n- It has a `items` array, which holds instances of the `LocalizationItem` class.\\n\\n**2. LocalizationItem Class:**\\n\\n- The `LocalizationItem` class represents a single localized text entry.\\n- It has two fields: `key` and `value`.\\n- `key` is the unique identifier for the text, and `value` is the localized text itself.\\n\\n**3. LocalizationManager:**\\n\\n- The `LocalizationManager` is responsible for loading and managing localized text.\\n- It uses the `LoadLocalizedText` method to load JSON files containing localization data.\\n- The `LoadLocalizedText` method reads JSON data from a file (e.g., \"Localization/en.txt\") and stores it in a dictionary, where the key is the `key` from the `LocalizationItem` and the value is the `value`.\\n- The `GetLocalizedValue` method retrieves the localized text corresponding to a given key. \\n\\n**4. LocalizedText Component:**\\n\\n- The `LocalizedText` component is used to display localized text in the game.\\n- It has a `key` field that specifies the text to be displayed.\\n- In the `Start` method, it uses the `LocalizationManager` to get the localized value for the specified key and sets the text of the UI element.\\n\\n**5. Loading and Managing Localization:**\\n\\n- The `LocalizationManager` is initialized in the `Awake` method of the `LocalizationManager` component.\\n- It tries to load the JSON file corresponding to the system language (e.g., \"Localization/French.txt\" if the system language is French).\\n- If the file for the system language is not found, it loads the English localization file (\"Localization/English.txt\").\\n\\n**In summary:**\\n\\n- The project uses a JSON-based approach to localization.\\n- The `LocalizationManager` loads localization data from JSON files.\\n- The `LocalizedText` component uses the `LocalizationManager` to get localized values and display them in the game.\\n\\nLet me know if you\\'d like to see a specific code example or have any other questions! \\n',\n", " 'system_prompt': 'You are an AI code assistant integrated into the VSCode IDE. Your primary role is to help software developers by answering their questions related to code and software engineering.\\n\\nRelevant code snippets from the developer\\'s project are provided as background information.\\n\\n# Assets/Standard Assets/InControl/Examples/KeyboardAndMouseProfile/KeyboardAndMouseProfile.cs\\n// This file is intentionally left blank to prevent errors because \\n// Unity does not delete asset folders when importing new versions.\\n\\n# Assets/Scripts/Localization/LocalizationData.cs\\n\\ufeffusing System;\\n\\n[System.Serializable]\\npublic class LocalizationData \\n{\\n    public LocalizationItem[] items;\\n}\\n\\n[System.Serializable]\\npublic class LocalizationItem\\n{\\n    public string key;\\n    public string value;\\n}\\n\\n# ExportWP8/OVIVO/MainPage.xaml.cs\\n\\t}\\n}\\n \\n\\n# Assets/Standard Assets/InControl/Source/Control/InputControlBase.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Character/MainCamera.cs\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ProfileListGenerator.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityInputDevice.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityDevice.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing System.IO;\\nusing System;\\n\\npublic class LocalizationManager : MonoBehaviour {\\n\\n    public static LocalizationManager instance;\\n\\n    private Dictionary<string, string> localizedText;\\n    private Dictionary<string, string> localizedTextEn;\\n    private bool isReady = false;\\n    private string missingTextString = \"Localized text not found\";\\n\\n    // Use this for initialization\\n    void Awake () \\n    {\\n        if (instance == null) {\\n            instance = this;\\n        } else if (instance != this)\\n        {\\n            Destroy (gameObject);\\n        }\\n\\n        DontDestroyOnLoad (gameObject);\\n\\n        LoadLocalizedText ();\\n    }\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityInputDeviceProfile.cs\\n// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Curves Demo/Scripts/Audio/AudioCameraZoom.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnknownUnityDeviceProfile.cs\\n// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Scripts/Environment/LightVisibleEffect.cs\\n\\t\\tstartScale = transform.localScale; \\n\\t\\ttimer_ = timer;\\n\\t\\tspeed_ = speed;\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\tScale();\\n\\t}\\n}\\n\\n\\n# Assets/Scripts/Localization/LocalizedText.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing UnityEngine.UI;\\n\\npublic class LocalizedText : MonoBehaviour {\\n\\n    public string key;\\n\\n    // Use this for initialization\\n    void Start () \\n    {\\n        Text text = GetComponent<Text> ();\\n        string localizedText = LocalizationManager.instance.GetLocalizedValue (key);\\n        if (localizedText != null) {\\n            text.text = localizedText;\\n        } else {\\n            Debug.LogWarning (\"Localized text for \\'\" + key + \"\\' is missing!\");\\n        }\\n        text.horizontalOverflow = HorizontalWrapMode.Wrap;\\n        text.verticalOverflow = VerticalWrapMode.Overflow;\\n    }\\n}\\n\\n# Assets/Scripts/Mobile/MobileMenu.cs\\n        symbOnPopUp [num % 3].GetComponent<SVGImage> ().enabled = true;\\n\\n        Canvas canvas = GetComponent<Canvas>();\\n        Vector2 pos = Character.Instance.mainCamera.GetCamera().WorldToViewportPoint(collectedSymbol.transform.position);\\n        symbOnPopUp [num % 3].GetComponent<RectTransform>().anchoredPosition = new Vector2(pos.x * canvas.GetComponent<RectTransform>().sizeDelta.x, pos.y * canvas.GetComponent<RectTransform>().sizeDelta.y) - canvas.GetComponent<RectTransform>().sizeDelta/2f;\\n\\n        //OrthoSize: 9.304726referencePixelsPerUnit: 100scaleFactor: 0.4sizeelta: 50   768x447\\n        //OrthoSize: 9.523712referencePixelsPerUnit: 100scaleFactor: 0.6614583sizeelta: 50   1270x668\\n\\n        //        Debug.LogError(\"OrthoSize: \" + Character.Instance.mainCamera.GetCamera().orthographicSize.ToString() +\\n        //            \"referencePixelsPerUnit: \" + canvas.referencePixelsPerUnit.ToString() +\\n        //            \"scaleFactor: \" + canvas.scaleFactor.ToString() +\\n        //            \"sizeelta: \" + symbOnPopUp[num % 3].GetComponent<RectTransform>().sizeDelta.x.ToString());\\n        //float a = (Character.Instance.mainCamera.GetCamera().orthographicSize * canvas.referencePixelsPerUnit * canvas.scaleFactor/1.5f) / (symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta.x * 2); // * symbol scale\\n\\n        float a = (1920f / Screen.width) * symbolRect.width;\\n\\n        //float a = (Character.Instance.mainCamera.GetCamera().orthographicSize * canvas.referencePixelsPerUnit / 10f) / (symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta.x / symbOnPopUp [num % 3].transform.localScale.x); // * symbol scale\\n        ////obj.sizeDelta = target.GetComponent<RectTransform>().sizeDelta;\\n        //symbOnPopUp [num % 3].transform.localScale = Vector3.one * a; // * collectedSymbol.transform.localScale.x;\\n        symbOnPopUp [num % 3].GetComponent<RectTransform>().sizeDelta = Vector2.one * a;\\n        ////StartCoroutine(UpdateScale(collectedSymbol));\\n\\n        symbOnPopUp[num % 3].GetComponent<RectTransform>().DOSizeDelta(Vector2.one * 50, 2.5f);\\n        //symbOnPopUp [num % 3].transform.DOScale(Vector3.one, 2.5f);\\n\\n        symbOnPopUp [num % 3].transform.DOMove(oldPosition, 2.5f);\\n        //symbOnPopUp [num % 3].transform.do\\n\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/InputDeviceProfile.cs\\n\\ufeff// TODO: This file intentionally left blank to prevent import/upgrade errors. It should eventually be removed.\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Utils/SVGGUI.cs\\n}\\n\\n# Assets/NativeReviewRequest/NativeReviewRequest.cs\\n\\ufeff/*\\n * Made by Kamen Dimitrov, http://www.studio-generative.com\\n */\\n\\nusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing System.Runtime.InteropServices;\\n\\npublic class NativeReviewRequest {\\n\\n\\tpublic static void RequestReview() {\\n\\t\\t#if UNITY_IOS && !UNITY_EDITOR\\n\\t\\trequestReview();\\n\\t\\t#endif\\n\\t}\\n\\n\\t#if UNITY_IOS && !UNITY_EDITOR\\n\\t[DllImport (\"__Internal\")] private static extern void requestReview();\\n\\t#endif\\n}\\n\\n\\n# Assets/Scripts/Mobile/RoundWindowController.cs\\n}\\n\\n\\n# Assets/PostProcessing/Editor/PostProcessingBehaviourEditor.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Editor/InControlBuilder.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Editor/iOS/XCodeAPI/PBX/Serializer.cs\\n\\n\\n\\n# Assets/PostProcessing/Runtime/Models/AntialiasingModel.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Mobile/MobileMenu.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/ColliderEnableSwitcher.cs\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/SVGImporterSettings.cs\\n\\n}\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\n\\t\\treturn p;\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/EightBitdoNES30ProWindowsProfile.cs\\n\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n    private Dictionary<string, string> LoadLocalizedText(string lang) {\\n        var res = new Dictionary<string, string> ();\\n        try {\\n            var resource = Resources.Load(Path.Combine(\"Localization\", lang + \".txt\"));\\n            resource = Resources.Load(Path.Combine(\"Localization\", lang));\\n            string dataAsJson = ((TextAsset) resource).text;\\n            LocalizationData loadedData = JsonUtility.FromJson<LocalizationData> (dataAsJson);\\n\\n            for (int i = 0; i < loadedData.items.Length; i++) \\n            {\\n                res.Add (loadedData.items [i].key, loadedData.items [i].value);   \\n            }\\n\\n            Debug.Log (\"LocalizationManager. Data loaded for \" + lang + \", dictionary contains: \" + res.Count + \" entries\");\\n        } catch (Exception e) {\\n            Debug.LogWarning (\"LocalizationManager. Could not load data for \" + lang + \" Exception: \" + e);\\n        }\\n        return res;\\n    }\\n\\n    public void LoadLocalizedText()\\n    {\\n        var lang = Application.systemLanguage;\\n\\n        localizedText = LoadLocalizedText (lang.ToString ());\\n        localizedTextEn = LoadLocalizedText (SystemLanguage.English.ToString ());\\n        isReady = true;\\n    }\\n\\n    public string GetLocalizedValue(string key)\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF510ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF710ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/OuyaEverywhere/OuyaEverywhereDeviceManager.cs\\n\\t}\\n}\\n\\n\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\n\\t\\t\\t}\\n\\n        }\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHlJREFUeNpiFBER+f/jxw8GNjY2BnqAX79+MXBwcDAwMQwQGHoWv3nzBoxHjo8pBSykBi8+MWAOGWY+5uLigrO/ffuGIYbMppnF5Fg2tFM1yKfk+pbkoKZGEA+OVP3nzx+6WQizi/H///8MoqKi/+np2y9fvjACBBgAoTYjgvihfz0AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAD1JREFUeNpi/P//P4OKisp/Bii4c+cOIwtIwMXFheHFixcMEhISYAVMINm3b9+CBUA0CDCiazc0NGQECDAAdH0YelA27kgAAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACRJREFUeNpizM3N/c9AADAqKysTVMTi5eXFSFAREFPHOoAAAwBCfwcAO8g48QAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi/P//PwM+wHL06FG8KpgYCABGZWVlvCYABBgA7/sHvGw+cz8AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi/P//PwM+wPKfgAomBgKAhYuLC68CgAADAAxjByOjCHIRAAAAAElFTkSuQmCC\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAADtJREFUeNpi/P//P4OKisp/Bii4c+cOIwtIQE9Pj+HLly9gQRCfBcQACbx69QqmmAEseO/ePQZkABBgAD04FXsmmijSAAAAAElFTkSuQmCC\"\\n\\t\\t};\\n\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"add_button.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texAddButton\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.add_button]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"add_button_active.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texAddButtonActive\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.add_button_active]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\t\\t/// Gets light or dark texture \"container_background.png\".\\n\\t\\t/// </summary>\\n\\t\\tpublic static Texture2D texContainerBackground\\n\\t\\t{\\n\\t\\t\\tget { return s_Cached[(int) ResourceName.container_background]; }\\n\\t\\t}\\n\\t\\t/// <summary>\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/LogitechF310ModeDWindowsNativeProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Curves Demo/Scripts/Layout/InstancerSpiral.cs\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouchControls/Examples/Version 2.X_New Features/Script/CharacterAnimationDungeon.cs\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickDrag.cs\\n\\t#endregion\\n}\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/InControlInputModuleEditor.cs\\n}\\n#endif\\n\\n# Assets/Scripts/Environment/CheckpointManager.cs\\n}\\n\\n\\n# Assets/Scripts/Localization/LocalizationManager.cs\\n    {\\n        if (localizedText.ContainsKey (key)) {\\n            return localizedText [key];\\n        } else if (localizedTextEn.ContainsKey (key)) {\\n            return localizedTextEn [key];\\n        } else {\\n            return null;\\n        }\\n\\n    }\\n\\n    public bool IsReady()\\n    {\\n        return isReady;\\n    }\\n}\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Core/SVGAssetEditor.cs\\n\\t\\tpublic static GUIContent ANTIALIASING_WIDTH_LABEL = new GUIContent(\"Antialiasing Width\", \"Increase or decrease the antialiasing width per pixel\");\\n        public static GUIContent MESH_COMPRESSION_LABEL = new GUIContent(\"Mesh Compression\", \"Reduce file size of the mesh, but might introduce irregularities and visual artefacts.\");\\n        public static string MESH_COMPRESSION_HELPBOX_LABEL = \"Mesh compression can introduce unwanted visual artefacts.\\\\nThe higher the compression, the higher the risk.\";\\n        public static GUIContent OPTIMIZE_MESH_LABEL = new GUIContent(\"Optimize Mesh\", \"The vertices and indices will be reorderer for better GPU performance.\");\\n        public static GUIContent SCALE_LABEL = new GUIContent(\"Scale\", \"The scale of the mesh relative to the SVG Asset. Does not affect the quality of the mesh\");\\n        public static GUIContent QUALITY_LABEL = new GUIContent(\"Quality\", \"Larger number means better but more complex mesh, Vertex Per Meter represents number of vertices in the SVG Asset that correspond to one unit in world space.\");\\n        public static GUIContent DEPTH_OFFSET_LABEL = new GUIContent(\"Depth Offset\", \"The minimal z-offset in WorldSpace for Opaque Rendering.\");\\n        public static GUIContent COMPRESS_DEPTH_LABEL = new GUIContent(\"Compress Depth\", \"Compresses the overlapping objects to reduce z-offset requirements.\");\\n        public static GUIContent CUSTOM_PIVOT_LABEL = new GUIContent(\"Custom Pivot\", \"Choose the predefined pivot point or the custom pivot point.\");\\n        public static GUIContent PIVOT_LABEL = new GUIContent(\"Pivot\", \"The location of the SVG Asset center point in the original Rect, specified in percents.\");\\n        public static GUIContent GENERATE_COLLIDER_LABEL = new GUIContent(\"Generate Collider\", \"Automatically generates polygon colliders.\");\\n        public static GUIContent KEEP_SVG_FILE_LABEL = new GUIContent(\"Keep SVG File\", \"Keep the SVG file in the final build. This increases the file size.\");\\n\\t\\tpublic static GUIContent USE_LAYERS_LABEL = new GUIContent(\"Use Layers\", \"Store individual SVG Layers for further modification. This discards stored Mesh.\");\\n        public static GUIContent IGNORE_SVG_CANVAS_LABEL = new GUIContent(\"Ignore SVG Canvas\", \"Trim the document canvas to object bounding box.\");\\n        public static GUIContent GENERATE_NORMALS_LABEL = new GUIContent(\"Normals\", \"Generate normals for lighting effects.\");\\n        public static GUIContent GENERATE_TANGENTS_LABEL = new GUIContent(\"Tangents\", \"Generate Tangents for advanced lighting effects.\");\\n\\n        void OnFilesValid()\\n        {\\n\\t\\t\\tbool valueChanged = false;\\n\\n            serializedObject.Update();\\n            EditorGUI.BeginChangeCheck();\\n            EditorGUILayout.LabelField(\"Rendering\", EditorStyles.boldLabel);\\n            EditorGUILayout.PropertyField(format, FORMAT_LABEL);\\n            EditorGUILayout.PropertyField(useGradients, USE_GRADIENTS_LABEL);\\n            EditorGUILayout.PropertyField(antialiasing, ANTIALIASING_LABEL);            \\n            EditorGUILayout.Space();\\n\\n            EditorGUILayout.LabelField(\"Meshes\", EditorStyles.boldLabel);\\n\\n\\n# Assets/Scripts/Environment/Boolque.cs\\n\\t\\treturn result;\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/EightBitdoNES30ProMacProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/Native.cs\\n\\n#endif\\n\\n\\t}\\n\\t//@endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Utilities/DragCorrector.cs\\n}\\n\\n# Assets/Scripts/Utilities/Game.cs\\n\\n        if (LogoSceneHasFinishedEvent != null)\\n            LogoSceneHasFinishedEvent ();\\n    }\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/ReorderableListFlags.cs\\n#endif\\n\\n\\n# Assets/PostProcessing/Editor/Models/GrainModelEditor.cs\\n}\\n\\n\\n# Assets/PostProcessing/Editor/Monitors/VectorscopeMonitor.cs\\n}\\n\\n\\n# Assets/Scripts/Mobile/ControlsAnimation.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\n\\npublic class ControlsAnimation : MonoBehaviour {\\n\\n\\t// Use this for initialization\\n\\tvoid Start () {\\n\\t\\t\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\t\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Control/TwoAxisInputControl.cs\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Engine/Finger.cs\\n\\n\\n\\n\\n# Assets/PostProcessing/Runtime/Models/DepthOfFieldModel.cs\\n        }\\n    }\\n}\\n\\n\\n# Assets/PostProcessing/Runtime/Components/ChromaticAberrationComponent.cs\\n        }\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Mac/XTR_G2_MacNativeProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/QuadTree.cs\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Editor/EasyTouchMenu.cs\\n\\t\\tDebug.Log(\"Project must be reloaded when reverting folder structure.\");\\n\\t\\tEditorApplication.OpenProject(Application.dataPath.Remove(Application.dataPath.Length - \"Assets\".Length, \"Assets\".Length));\\n\\t}*/\\n\\n# Assets/Scripts/Utilities/ChangeFixedTimestepTrigger.cs\\n\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouchControls/Plugins/ETCDPad.cs\\n\\t\\t\\t\\t\\t\\t\\n\\t}\\n\\t#endregion\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickTap.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/XTR_G2_WindowsNativeProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Editor/EasytouchHierachyCallBack.cs\\n\\t\\t\\n}\\n\\n\\n# Assets/Scripts/!Temp/DetectGamepad.cs\\n\\t\\t}\\n\\n\\t}\\n\\n}\\n\\n\\n# Assets/Scripts/Environment/EnvironmentControl.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\npublic class EnvironmentControl : MonoBehaviour {\\n\\n\\t// Use this for initialization\\n\\tvoid Start () {\\n\\t\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\n\\t}\\n}\\n\\n\\n# Assets/PostProcessing/Runtime/PostProcessingProfile.cs\\n            public float vectorscopeExposure = 0.12f;\\n            public bool vectorscopeShowBackground = true;\\n        }\\n\\n        public MonitorSettings monitors = new MonitorSettings();\\n#endif\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Device/InputDevice.cs\\n\\t\\tInputControl cachedDPadY;\\n\\t\\tInputControl cachedCommand;\\n\\n\\n\\t\\tpublic InputControl LeftStickUp { get { return cachedLeftStickUp ?? (cachedLeftStickUp = GetControl( InputControlType.LeftStickUp )); } }\\n\\t\\tpublic InputControl LeftStickDown { get { return cachedLeftStickDown ?? (cachedLeftStickDown = GetControl( InputControlType.LeftStickDown )); } }\\n\\t\\tpublic InputControl LeftStickLeft { get { return cachedLeftStickLeft ?? (cachedLeftStickLeft = GetControl( InputControlType.LeftStickLeft )); } }\\n\\t\\tpublic InputControl LeftStickRight { get { return cachedLeftStickRight ?? (cachedLeftStickRight = GetControl( InputControlType.LeftStickRight )); } }\\n\\t\\tpublic InputControl RightStickUp { get { return cachedRightStickUp ?? (cachedRightStickUp = GetControl( InputControlType.RightStickUp )); } }\\n\\t\\tpublic InputControl RightStickDown { get { return cachedRightStickDown ?? (cachedRightStickDown = GetControl( InputControlType.RightStickDown )); } }\\n\\t\\tpublic InputControl RightStickLeft { get { return cachedRightStickLeft ?? (cachedRightStickLeft = GetControl( InputControlType.RightStickLeft )); } }\\n\\t\\tpublic InputControl RightStickRight { get { return cachedRightStickRight ?? (cachedRightStickRight = GetControl( InputControlType.RightStickRight )); } }\\n\\t\\tpublic InputControl DPadUp { get { return cachedDPadUp ?? (cachedDPadUp = GetControl( InputControlType.DPadUp )); } }\\n\\t\\tpublic InputControl DPadDown { get { return cachedDPadDown ?? (cachedDPadDown = GetControl( InputControlType.DPadDown )); } }\\n\\t\\tpublic InputControl DPadLeft { get { return cachedDPadLeft ?? (cachedDPadLeft = GetControl( InputControlType.DPadLeft )); } }\\n\\t\\tpublic InputControl DPadRight { get { return cachedDPadRight ?? (cachedDPadRight = GetControl( InputControlType.DPadRight )); } }\\n\\t\\tpublic InputControl Action1 { get { return cachedAction1 ?? (cachedAction1 = GetControl( InputControlType.Action1 )); } }\\n\\t\\tpublic InputControl Action2 { get { return cachedAction2 ?? (cachedAction2 = GetControl( InputControlType.Action2 )); } }\\n\\t\\tpublic InputControl Action3 { get { return cachedAction3 ?? (cachedAction3 = GetControl( InputControlType.Action3 )); } }\\n\\t\\tpublic InputControl Action4 { get { return cachedAction4 ?? (cachedAction4 = GetControl( InputControlType.Action4 )); } }\\n\\t\\tpublic InputControl LeftTrigger { get { return cachedLeftTrigger ?? (cachedLeftTrigger = GetControl( InputControlType.LeftTrigger )); } }\\n\\t\\tpublic InputControl RightTrigger { get { return cachedRightTrigger ?? (cachedRightTrigger = GetControl( InputControlType.RightTrigger )); } }\\n\\t\\tpublic InputControl LeftBumper { get { return cachedLeftBumper ?? (cachedLeftBumper = GetControl( InputControlType.LeftBumper )); } }\\n\\t\\tpublic InputControl RightBumper { get { return cachedRightBumper ?? (cachedRightBumper = GetControl( InputControlType.RightBumper )); } }\\n\\t\\tpublic InputControl LeftStickButton { get { return cachedLeftStickButton ?? (cachedLeftStickButton = GetControl( InputControlType.LeftStickButton )); } }\\n\\t\\tpublic InputControl RightStickButton { get { return cachedRightStickButton ?? (cachedRightStickButton = GetControl( InputControlType.RightStickButton )); } }\\n\\t\\tpublic InputControl LeftStickX { get { return cachedLeftStickX ?? (cachedLeftStickX = GetControl( InputControlType.LeftStickX )); } }\\n\\t\\tpublic InputControl LeftStickY { get { return cachedLeftStickY ?? (cachedLeftStickY = GetControl( InputControlType.LeftStickY )); } }\\n\\t\\tpublic InputControl RightStickX { get { return cachedRightStickX ?? (cachedRightStickX = GetControl( InputControlType.RightStickX )); } }\\n\\t\\tpublic InputControl RightStickY { get { return cachedRightStickY ?? (cachedRightStickY = GetControl( InputControlType.RightStickY )); } }\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/SteelSeriesStratusXLWinProfile.cs\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/SVG/DOM/Utilities/LiteStack.cs\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/UnityInputDeviceProfile.cs\\n\\n\\t\\t#endregion\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Mesh/SVGLineUtils.cs\\n}\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\n[RequireComponent(typeof(BoxCollider2D))]\\npublic class Eye : MonoBehaviour \\n{\\n    public Transform eye;\\n\\tpublic Transform eyeLidUpper;\\n\\tpublic Transform eyeLidLower;\\n\\tpublic float eyeLidWonderCoef;\\n    public float actionRadius = 1;\\n    public float lerpCoef = 4f;\\n\\n\\n\\tpublic bool catEye = false;\\n    bool isActive = false;\\n\\n\\tprivate Vector3 eyeLidUpperOldPosition;\\n\\tprivate Vector3 eyeLidLowerOldPosition;\\n\\n\\t// Use this for initialization\\n\\tvoid Start () \\n    {\\n        GetComponent<Collider2D>().isTrigger = true;\\n\\n\\t\\tif(eyeLidUpper != null && eyeLidLower != null)\\n\\t\\t{\\n\\t\\t\\teyeLidUpperOldPosition = eyeLidUpper.localPosition;\\n\\t\\t\\teyeLidLowerOldPosition = eyeLidLower.localPosition;\\n\\t\\t}\\n\\n\\n# Assets/Scripts/Other/FirstLevelCamController.cs\\n\\t\\tnewPos = camera.ScreenToWorldPoint (Vector3.up * camera.pixelHeight*0.5f);\\n\\n\\t\\tif (newPos.x < startX)\\n\\t\\t\\tborder.transform.position = newPos;\\n\\t}\\n\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/Xbox360WinProfile.cs\\n}\\n\\n\\n\\n# Assets/Scripts/Mobile/DotsInfo.cs\\n            dotsCount[i] = dotsCodes[i].Length;\\n        }\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickLongTap.cs\\n\\t\\t\\n\\t\\treturn returnValue;\\n\\t}\\n}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/PlayStation4Profile.cs\\n}\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/ColorPicker/ColorImage.cs\\n}\\n\\n# Assets/Scripts/Environment/Symbol.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\nusing UnityEngine.UI;\\n\\npublic class Symbol : MonoBehaviour \\n{\\n\\tvoid Start () \\n\\t{\\n\\t\\tif (PlayerPrefs.HasKey (\"Symbol_\" + gameObject.name))\\n\\t\\t{\\n\\t\\t\\tif (PlayerPrefs.GetInt (\"Symbol_\" + gameObject.name) == 1)\\n\\t\\t\\t{\\n                /* LEGACY\\n\\t\\t\\t\\tMenu menu = GameObject.FindObjectOfType<Menu> ();\\n\\t\\t\\t\\tif (menu != null)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tint num = int.Parse (gameObject.name);\\n\\n\\t\\t\\t\\t\\tmenu.symbOnPopUp [num % 3].GetComponent<Image> ().enabled = true;\\n\\t\\t\\t\\t\\tmenu.symbOnPopUp [num % 3].GetComponent<Image> ().sprite = gameObject.GetComponent<SpriteRenderer> ().sprite;\\n\\t\\t\\t\\t\\tif (menu.additionalSymbsOnMandala [num] != null)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tmenu.additionalSymbsOnMandala [num].GetComponent<Image> ().enabled = true;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n*/\\n\\t\\t\\t\\tDestroy (gameObject);\\t\\n\\t\\t\\t}\\n\\t\\t} \\n\\t\\telse\\n\\n\\n# Assets/Scripts/Environment/Group.cs\\n            }\\n\\n    }\\n\\n}\\n\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\tpublic bool fusionWithMovingPlatform = false;\\n\\tpublic FluidEdge fluid;\\n\\n    public int movingPlatformDoEveryNFixedUpdate = 1;\\n\\n    Rigidbody2D rigidbody;\\n\\t//public bool unseenFirstly = false;\\n\\n\\tprivate const string DISTANCE_MESH_GAME_OBJECT_NAME = \"distance_mesh\";\\n\\n\\tprivate static Vector2 Perpendicular2d(Vector2 v) {\\n\\t\\treturn new Vector2 (v.y, -v.x);\\n\\t}\\n\\n    public void RemoveDistanceMesh()\\n    {\\n        for (var i = 0; i < transform.childCount; i++)\\n        {\\n            if (transform.GetChild(i).name == DISTANCE_MESH_GAME_OBJECT_NAME)\\n            {\\n                DestroyImmediate(transform.GetChild(i).gameObject);\\n                i--;\\n            }\\n        }\\n    }\\n\\n    void AlignZ(Transform obj)\\n    {\\n        if (obj.localScale.z != 1)\\n            obj.localScale = new Vector3(obj.localScale.x, obj.localScale.y, 1);\\n\\n\\n# Assets/Scripts/Mobile/LevelPreviewCamera.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing HedgehogTeam.EasyTouch;\\nusing DG.Tweening;\\n\\npublic class LevelPreviewCamera : MonoBehaviour \\n{\\n    public static LevelPreviewCamera Instance\\n    {\\n        get\\n        {\\n            return instance;\\n        }\\n    }\\n\\n    static LevelPreviewCamera instance;\\n    public static Camera cameraComponent;\\n    public static Transform transformComponent;\\n\\n    public Rect viewRect;\\n\\n    public float maxZoomValue, minZoomValue, minZoomValueForIPad;\\n    public float zoomSpeedPerUnit;\\n    public float scaleFactorPerPixel;\\n    public float translateFactorPerPixel;\\n    public bool disableCameraOnAwake = true;\\n\\n    public float finalZoomSpeedFactor = 0.02f;\\n    public bool isInputAllowed = false;\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/XML Parser/SmallXmlParser.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/Eye.cs\\n\\n        Character.Instance.CollectedFinalSymbolEvent += delegate\\n            {\\n                this.enabled = false;\\n            };\\n\\t}\\n\\n    public void OnTriggerEnter2D(Collider2D col)\\n    {\\n        if (col.CompareTag(\"Character\"))\\n        {\\n            isActive = true;\\n        }\\n    }\\n\\n    public void OnTriggerExit2D(Collider2D col)\\n    {\\n        if (col.CompareTag(\"Character\"))\\n        {\\n            isActive = false;\\n        }\\n    }\\n\\n\\tvoid FixedUpdate () \\n    {\\t\\n        if (isActive)\\n        {\\n            Vector3 target = (Character.Instance.transform.position - transform.position).normalized * actionRadius;\\n            eye.localPosition = Vector3.Lerp(eye.localPosition, target, Time.fixedDeltaTime * lerpCoef);\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/PlayStation3WinProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/SVGRenderer.cs\\n    }\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Implementation/SVG/DOM/BasicTypes/SVGLength.cs\\n}\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/BoxSlider.cs\\n}\\n\\n\\n# Assets/Scripts/Environment/Follower.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\nusing System;\\nusing DG.Tweening;\\n\\npublic class Follower: MonoBehaviour \\n{\\n    public GameObject topCollider;\\n    public Platform platform;\\n\\n\\tpublic Transform target;\\n\\tpublic float movingSpeed;\\n\\tpublic Transform flowerParent;\\n\\tpublic bool deathPlayed;\\n\\n\\t[HideInInspector]\\n\\tpublic bool isEnding = false;\\n\\n\\tpublic Vector2 respawnPlace;\\n\\n\\t//private float visionRadius;\\n\\tprivate Vector2 vectorToTarget;\\n\\tprivate bool onFlower;\\n\\n\\tprivate bool startFlying;\\n\\n\\tprivate bool flowerDeathAnimationIsPlayed = false;\\n\\tprivate bool isDead = false;\\n\\tprivate DateTime flowerDeathAnimationStartTime;\\n\\tprivate double flowerDeathAnimationLength= 2800;\\n\\n\\n# Assets/Standard Assets/InControl/Source/Binding/BindingSource.cs\\n\\t\\t}\\n\\n\\t\\t#endregion\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/OuyaEverywhere/OuyaEverywhereDevice.cs\\n\\n\\t\\t\\tvar rt = Utility.ApplyDeadZone(\\n\\t\\t\\t\\t         OuyaSDK.OuyaInput.GetAxisRaw( DeviceIndex, OuyaController.AXIS_R2 ),\\n\\t\\t\\t\\t         LowerDeadZone,\\n\\t\\t\\t\\t         UpperDeadZone \\n\\t\\t\\t         );\\n\\t\\t\\tUpdateWithValue( InputControlType.RightTrigger, rt, updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.DPadUp, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_UP ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadDown, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_DOWN ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadLeft, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_LEFT ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.DPadRight, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_DPAD_RIGHT ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.Action1, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_O ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action2, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_A ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action3, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_U ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.Action4, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_Y ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.LeftBumper, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_L1 ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.RightBumper, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_R1 ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.LeftStickButton, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_L3 ), updateTick, deltaTime );\\n\\t\\t\\tUpdateWithState( InputControlType.RightStickButton, OuyaSDK.OuyaInput.GetButton( DeviceIndex, OuyaController.BUTTON_R3 ), updateTick, deltaTime );\\n\\n\\t\\t\\tUpdateWithState( InputControlType.Menu, OuyaSDK.OuyaInput.GetButtonDown( DeviceIndex, OuyaController.BUTTON_MENU ), updateTick, deltaTime );\\n\\n\\t\\t\\tCommit( updateTick, deltaTime );\\n\\t\\t\\t#endif\\n\\t\\t}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Examples/InterfaceMovement/Button.cs\\n\\t\\t}\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Engine/EasyTouchInput.cs\\n\\t\\treturn position;\\n\\t}\\n\\t#endregion\\n}\\n}\\n\\n\\n\\n# Assets/Scripts/Environment/Platform.cs\\n\\tpublic bool mainPlatform = false;\\n\\n\\tpublic bool IntoContour(Collider2D edge, Vector2 origin, Vector2 dir)\\n\\t{\\n\\t\\t//Vector2 p = new Vector2 (edge.transform.position.x, edge.transform.position.y);\\n\\t\\t//dir = ( p - origin).normalized;\\n\\n\\t\\tint layer = edge.gameObject.layer;\\n\\t\\tedge.gameObject.layer = LayerMask.NameToLayer (\"TempPlatform\");\\n\\n\\t\\tbool finalPoint = false;\\n\\t\\tint points = 0;\\n\\t\\t//Vector2 origin = new Vector2 (transform.position.x, transform.position.y);\\n\\t\\tRaycastHit2D hit;\\n\\n\\t\\twhile (!finalPoint)\\n\\t\\t{\\n\\t\\t\\thit = Physics2D.Raycast (origin, dir, 100f, 1 << LayerMask.NameToLayer (\"TempPlatform\"));\\n\\n\\t\\t\\tif (hit)\\n\\t\\t\\t{\\n\\t\\t\\t\\t++points;\\n\\t\\t\\t\\torigin = hit.point + dir * 0.0001f;\\n\\t\\t\\t} \\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tfinalPoint = true;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/PowerAMiniProExControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Scripts/Utilities/MorseCodeManager.cs\\n\\n    /* LEGACY\\n    static public string GetKey(int level)\\n    {\\n        return GetKey((Mandala.LevelName)level);\\n    }\\n\\n    static public string GetKey(Mandala.LevelName level)\\n    {\\n        return \"Morse_\" + level.ToString();\\n    }\\n\\n    static public string GetKey(string level)\\n    {\\n        return \"Morse_\" + level;\\n    } */\\n        \\n    void Awake()\\n    {\\n        instance = this;\\n    }\\n\\n    void Start()\\n    {\\n        if (!DataManager.IsInitialized)\\n            DataManager.Init();\\n        \\n        HideCollectedDots();\\n\\n    }\\n\\n\\n# Assets/Standard Assets/InControl/Plugins/iOS/ICadePluginPath.cs\\n\\ufeffnamespace InControl\\n{\\n\\tusing UnityEngine;\\n\\n\\n\\t// This file exists only so the build post processor can easily\\n\\t// find the iOS Xcode plugin files at the same path.\\n\\tpublic class ICadePluginPath : ScriptableObject\\n\\t{\\n\\t}\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Editor/iOS/XCodeAPI/PBXProject.cs\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNDECLARED_SELECTOR\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNINITIALIZED_AUTOS\", \"YES_AGGRESSIVE\" );\\n\\t\\t\\tconfig.AddProperty( \"GCC_WARN_UNUSED_FUNCTION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"INFOPLIST_FILE\", infoPlistPath );\\n\\t\\t\\tconfig.AddProperty( \"IPHONEOS_DEPLOYMENT_TARGET\", \"8.0\" );\\n\\t\\t\\tconfig.AddProperty( \"LD_RUNPATH_SEARCH_PATHS\", \"$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks\" );\\n\\t\\t\\tconfig.AddProperty( \"MTL_ENABLE_DEBUG_INFO\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"PRODUCT_NAME\", \"$(TARGET_NAME)\" );\\n\\t\\t\\tconfig.AddProperty( \"SKIP_INSTALL\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"VALIDATE_PRODUCT\", \"YES\" );\\n\\t\\t}\\n\\n\\t\\tprivate void SetDefaultAppExtensionDebugBuildFlags( XCBuildConfiguration config, string infoPlistPath )\\n\\t\\t{\\n\\t\\t\\tconfig.AddProperty( \"ALWAYS_SEARCH_USER_PATHS\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_CXX_LANGUAGE_STANDARD\", \"gnu++0x\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_CXX_LIBRARY\", \"libc++\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_ENABLE_MODULES\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_ENABLE_OBJC_ARC\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_BOOL_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_CONSTANT_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_DIRECT_OBJC_ISA_USAGE\", \"YES_ERROR\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_EMPTY_BODY\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_ENUM_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_INT_CONVERSION\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_OBJC_ROOT_CLASS\", \"YES_ERROR\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN_UNREACHABLE_CODE\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"CLANG_WARN__DUPLICATE_METHOD_MATCH\", \"YES\" );\\n\\t\\t\\tconfig.AddProperty( \"COPY_PHASE_STRIP\", \"NO\" );\\n\\t\\t\\tconfig.AddProperty( \"ENABLE_STRICT_OBJC_MSGSEND\", \"YES\" );\\n\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t\\t\\tremove_button,\\n\\t\\t\\tremove_button_active,\\n\\t\\t\\ttitle_background,\\n\\t\\t}\\n\\n\\t\\t/// <summary>\\n\\t\\t/// Resource assets for light skin.\\n\\t\\t/// </summary>\\n\\t\\t/// <remarks>\\n\\t\\t/// <para>Resource assets are PNG images which have been encoded using a base-64\\n\\t\\t/// string so that actual asset files are not necessary.</para>\\n\\t\\t/// </remarks>\\n\\t\\tprivate static string[] s_LightSkin = {\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAW0lEQVRIS+3NywnAQAhF0anI4mzVCmzBBl7QEBgGE5JFhBAXd+OHM5gZZgYRKcktNxu+HRFF2e6qhtOjtQM7K/tZ+xY89wSbazg9eqOfw6oag4rcChjY8coAjA2l1RxFDY8IFAAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAGlJREFUeNpiFBER+f/jxw8GNjY2BnqAX79+MXBwcDAwMQwQGHoWnzp1CoxHjo8pBSykBi8+MTMzs2HmY2QfwXxKii9HExdZgNwgHuFB/efPH7pZCLOL8f///wyioqL/6enbL1++MAIEGABvGSLA+9GPZwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAMElEQVQYV2P4//8/Q1FR0X8YBvHBAp8+ffp/+fJlMA3igwUfPnwIFgDRYEFM7f8ZAG1EOYL9INrfAAAAAElFTkSuQmCC\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAIElEQVQYV2P49OnTf0KYobCw8D8hzPD/P2FMLesK/wMAs5yJpK+6aN4AAAAASUVORK5CYII=\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAIAAADq9gq6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAABVJREFUeNpiVFZWZsAGmBhwAIAAAwAURgBt4C03ZwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAIAAADq9gq6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAABVJREFUeNpivHPnDgM2wMSAAwAEGAB8VgKYlvqkBwAAAABJRU5ErkJggg==\",\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAEFJREFUeNpi/P//P0NxcfF/BgRgZP78+fN/VVVVhpCQEAZjY2OGs2fPNrCApBwdHRkePHgAVwoWnDVrFgMyAAgwAAt4E1dCq1obAAAAAElFTkSuQmCC\"\\n\\t\\t};\\n\\t\\t/// <summary>\\n\\t\\t/// Resource assets for dark skin.\\n\\t\\t/// </summary>\\n\\t\\t/// <remarks>\\n\\t\\t/// <para>Resource assets are PNG images which have been encoded using a base-64\\n\\t\\t/// string so that actual asset files are not necessary.</para>\\n\\t\\t/// </remarks>\\n\\t\\tprivate static string[] s_DarkSkin = {\\n\\t\\t\\t\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIBJREFUeNpiVFZW/u/i4sLw4sULBnoACQkJhj179jAwMQwQGHoWl5aWgvHI8TGlgIXU4MUn1t3dPcx8HB8fD2cvXLgQQ0xHR4c2FmMzmBTLhl5QYwt2cn1MtsXkWjg4gvrt27fgWoMeAGQXCDD+//+fQUVF5T89fXvnzh1GgAADAFmSI1Ed3FqgAAAAAElFTkSuQmCC\",\\n\\n\\n# Assets/Standard Assets/SVG Importer/Plugins/Core/Data/SVGLineData.cs\\n    }\\n}\\n\\n\\n# Assets/Scripts/Environment/ObjectSimpleMotions.cs\\n\\n\\t}\\n\\n\\t// изменение размера как трансформ\\n\\tvoid Scale(){\\n\\t\\ttransform.localScale += Vector3.one * speed_ * DeltaTime();\\n\\t\\tTimeToChangeDirection ();\\n\\t}\\n\\n    private Quaternion initialTranformRotation;\\n    private Vector3 initialTransformPosition;\\n    private float initialRigidbodyRotation;\\n    private Vector2 initialRigidbodyPosition;\\n\\t\\t\\n\\tvoid Start() {\\n\\n\\t\\tqStart = Quaternion.AngleAxis (angle_start, Vector3.forward);\\n\\t\\tqEnd   = Quaternion.AngleAxis (angle_end, Vector3.forward);\\n\\t\\toriginalAngle = transform.rotation;\\n\\t\\ttimer_ = timer;\\n\\t\\tspeed_ = speed;\\n\\n\\t\\trigidbody2D_ = GetComponent<Rigidbody2D> ();\\n\\n        initialTranformRotation = transform.rotation;\\n        initialTransformPosition = transform.position;\\n        if (rigidbody2D_ != null) {\\n            initialRigidbodyRotation = rigidbody2D_.rotation;\\n            initialRigidbodyPosition = rigidbody2D_.position;\\n        }\\n\\n\\n# Assets/Scripts/Mobile/StartChapterAnimation.cs\\n\\ufeffusing System.Collections;\\nusing System.Collections.Generic;\\nusing UnityEngine;\\nusing DG.Tweening;\\nusing SVGImporter;\\n\\npublic class StartChapterAnimation : MonoBehaviour \\n{\\n    [SerializeField] private float delayBeforeCharacterActivation;\\n\\n    private const float ANIMATOR_SCALE = .34f;\\n\\n    public static StartChapterAnimation Instance\\n    {\\n        get;\\n        private set;\\n    }\\n\\n    void Awake()\\n    {\\n        Instance = this;\\n    }\\n\\n    void Start()\\n    {\\n        if (MobileMenu.Instance == null)\\n        {\\n            Init();\\n        }\\n    }\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/MicrosoftXboxOneControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Scripts/Environment/TwinkleLight.cs\\n\\t{\\n\\t\\tOnTriggerEnter2D (col);\\n\\t}\\n\\t\\t\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/RockCandyXboxOneControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Standard Assets/EasyTouchBundle/EasyTouch/Plugins/Components/QuickTwist.cs\\n}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/NVidiaShieldWin10Profile.cs\\n}\\n\\n\\n\\n# Assets/Scripts/Other/TutorialStartAnimation.cs\\n            DataManager.UpdateLocalDataFile ();\\n        } else if (InGameUI.Instance.IsPlayingTapHintAnimation) {\\n            InGameUI.Instance.PlayTapAnimation();\\n        }\\n    }\\n\\n    void SetCameraCanMoveUp() {\\n        Character.Instance.mainCamera.CanMoveUp = true;\\n    }\\n\\t\\n}\\n\\n\\n# Assets/Standard Assets/SVG Importer/Editor/Core/SVGAssetEditor.cs\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n        }\\n    }\\n}\\n\\n\\n# Assets/PostProcessing/Editor/Monitors/ParadeMonitor.cs\\n                    Handles.DrawLine(F, new Vector3(F.x + kTickSize, F.y));\\n                    Handles.DrawLine(G, new Vector3(G.x + kTickSize, G.y));\\n                    Handles.DrawLine(H, new Vector3(H.x + kTickSize, H.y));\\n\\n                    // Horizontal ticks\\n                    Handles.DrawLine(A, new Vector3(A.x, A.y - kTickSize));\\n                    Handles.DrawLine(B, new Vector3(B.x, B.y - kTickSize));\\n                    Handles.DrawLine(C, new Vector3(C.x, C.y - kTickSize));\\n                    Handles.DrawLine(D, new Vector3(D.x, D.y - kTickSize));\\n                    Handles.DrawLine(O, new Vector3(O.x, O.y - kTickSize));\\n                    Handles.DrawLine(P, new Vector3(P.x, P.y - kTickSize));\\n                    Handles.DrawLine(Q, new Vector3(Q.x, Q.y - kTickSize));\\n\\n                    Handles.DrawLine(H, new Vector3(H.x, H.y + kTickSize));\\n                    Handles.DrawLine(I, new Vector3(I.x, I.y + kTickSize));\\n                    Handles.DrawLine(J, new Vector3(J.x, J.y + kTickSize));\\n                    Handles.DrawLine(K, new Vector3(K.x, K.y + kTickSize));\\n                    Handles.DrawLine(R, new Vector3(R.x, R.y + kTickSize));\\n                    Handles.DrawLine(S, new Vector3(S.x, S.y + kTickSize));\\n                    Handles.DrawLine(T, new Vector3(T.x, T.y + kTickSize));\\n\\n                    // Labels\\n                    GUI.color = color;\\n                    GUI.Label(new Rect(A.x - kTickSize - 34f, A.y - 15f, 30f, 30f), \"1.0\", FxStyles.tickStyleRight);\\n                    GUI.Label(new Rect(M.x - kTickSize - 34f, M.y - 15f, 30f, 30f), \"0.5\", FxStyles.tickStyleRight);\\n                    GUI.Label(new Rect(K.x - kTickSize - 34f, K.y - 15f, 30f, 30f), \"0.0\", FxStyles.tickStyleRight);\\n\\n                    GUI.Label(new Rect(D.x + kTickSize + 4f, D.y - 15f, 30f, 30f), \"1.0\", FxStyles.tickStyleLeft);\\n                    GUI.Label(new Rect(F.x + kTickSize + 4f, F.y - 15f, 30f, 30f), \"0.5\", FxStyles.tickStyleLeft);\\n                    GUI.Label(new Rect(H.x + kTickSize + 4f, H.y - 15f, 30f, 30f), \"0.0\", FxStyles.tickStyleLeft);\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Windows/XTR55_G2_WindowsNativeProfile.cs\\n\\t// @endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Mac/XTR55_G2_MacNativeProfile.cs\\n\\t// @endcond\\n}\\n\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Utilities/Serialization/Surrogates/Vector3Surrogate.cs\\n}\\n\\n# Assets/Scripts/Other/TrailerScript.cs\\n        }\\n\\n\\n    }\\n\\n\\t// Use this for initialization\\n\\tvoid Start () \\n    {\\n        audio = GetComponent<AudioSource>();\\n        CreateStars();\\n        StartCoroutine(LinesAnimation());\\n\\t}\\n\\t\\n\\t// Update is called once per frame\\n\\tvoid Update () {\\n\\t\\n\\t}\\n}\\n\\n\\n# Assets/Standard Assets/InControl/Source/Native/DeviceProfiles/Generated/MadCatzControllerMacProfile.cs\\n}\\n\\n\\n\\n\\n# Assets/Standard Assets/InControl/Source/Utility/RingBuffer.cs\\n\\t\\t}\\n\\t}\\n}\\n\\n\\n\\n# Assets/Scripts/Mobile/LevelPreview.cs\\n            LevelPreviewCamera.Instance.isInputAllowed = value;\\n        }\\n    }\\n}\\n\\n# Assets/Standard Assets/InControl/Editor/ReorderableList/Internal/ReorderableListResources.cs\\n\\t}\\n}\\n#endif\\n\\n\\n# Assets/Standard Assets/unity-ui-extensions/Scripts/Controls/SelectionBox/IBoxSelectable.cs\\n}\\n\\n# Assets/Scripts/Environment/Platform.cs\\n            }\\n            catch (System.Exception ex)\\n            {\\n                Debug.LogError(name);\\n            }\\n        }\\n\\n        if (transform.localScale.z == 0)\\n            transform.localScale = new Vector3(transform.localScale.x, transform.localScale.y, 1);\\n\\n        Collider2D edgeCollider = GetComponent<Collider2D>();\\n\\n        if (edgeCollider)\\n        {\\n            isDisabledOnAwake = !edgeCollider.enabled;\\n        }\\n\\n        rigidbody = GetComponent<Rigidbody2D>();\\n\\n        //OnBecameInvisible();\\n\\t}\\n\\n    void OnBecameInvisible() \\n    {\\n        if (!isDisabledOnAwake && isCollidersControls)\\n        {\\n            Collider2D edgeCollider = GetComponent<Collider2D>();\\n\\n            if (edgeCollider)\\n            {\\n\\n\\n# Assets/Standard Assets/InControl/Source/Unity/DeviceProfiles/GameStickLinuxProfile.cs\\n}\\n\\n\\n\\n# Assets/Standard Assets/SVG Importer/Example Projects/Hologram Demo/Scripts/HologramController.cs\\n\\ufeffusing UnityEngine;\\nusing System.Collections;\\n\\npublic class HologramController : MonoBehaviour {\\n\\n    [System.Serializable]\\n    public struct HologramLayer\\n    {\\n        public Transform transform;\\n        public Vector3 startLocalPosition;\\n        public float rotation;\\n    }\\n\\n    public HologramLayer[] layers;\\n    public float depth;\\n    public float depthSpeed = 1f;\\n    public AnimationCurve depthAnimation;\\n\\n    float elapsedTime;\\n\\n    // Use this for initialization\\n    void Start ()\\n    {\\t\\n        for(int i = 0; i < layers.Length; i++)\\n        {\\n            if (layers[i].transform == null) continue;\\n            layers[i].startLocalPosition = layers[i].transform.localPosition;\\n        }\\n\\t}\\n\\t\\n\\n\\n',\n", " 'question': 'How does localization work in this project?',\n", " 'first_token_time_s': 0.9399204254150391,\n", " 'generation_time_s': 5.061121940612793,\n", " 'generation_time_per_token_s': 0.010183343944894955,\n", " 'max_prompt_len': 16000,\n", " 'model_name': 'gemini-1.5-flash-preview-0514',\n", " 'example_index': 0}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["import time\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "def run_gemini(example, model_name, max_prompt_len):\n", "    global MODEL_NAME\n", "    assert model_name == MODEL_NAME\n", "\n", "    global GOOGLE_MODEL\n", "    chat = GOOGLE_MODEL.start_chat()\n", "\n", "    system_prompt = get_prompt(example, max_prompt_len)\n", "    message = system_prompt + \"\\n\" + example[\"question\"]\n", "\n", "    start_time = time.time()\n", "    first_token_time = None\n", "    text = []\n", "\n", "    responses = chat.send_message(message, stream=True)\n", "    for chunk in responses:\n", "        if first_token_time is None:\n", "            assert len(chunk.text) > 0\n", "            first_token_time = time.time()\n", "        text.append(chunk.text)\n", "\n", "    end_time = time.time()\n", "    n_input_tokens = chunk.usage_metadata.prompt_token_count\n", "    n_output_tokens = chunk.usage_metadata.candidates_token_count\n", "    assert chunk.usage_metadata.total_token_count == n_input_tokens + n_output_tokens\n", "\n", "    return {\n", "        \"n_input_tokens_s\": n_input_tokens,\n", "        \"n_output_tokens_s\": n_output_tokens,\n", "        \"text\": \"\".join(text),\n", "        \"system_prompt\": system_prompt,\n", "        \"question\": example[\"question\"],\n", "        \"first_token_time_s\": first_token_time - start_time,\n", "        \"generation_time_s\": end_time - first_token_time,\n", "        \"generation_time_per_token_s\": (end_time - first_token_time) / n_output_tokens,\n", "    }\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/benchmark_api/gemini_cache_v2.jsonl\")\n", "def run_gemini_for_ovivo(index, model_name, max_prompt_len):\n", "    global ovivo_data\n", "    datum = run_gemini(ovivo_data[index], model_name, max_prompt_len)\n", "    datum.update({\n", "        \"max_prompt_len\": max_prompt_len,\n", "        \"model_name\": model_name,\n", "        \"example_index\": index,\n", "    })\n", "    return datum\n", "\n", "# GEMINI_PRO_MODEL_NAME = \"gemini-1.5-pro-preview-0514\"\n", "# GEMINI_FLASH_MODEL_NAME = \"gemini-1.5-flash-preview-0514\"\n", "\n", "max_prompt_len = 16_000\n", "model_name = MODEL_NAME\n", "run_gemini_for_ovivo(0, model_name, max_prompt_len)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "max_prompt_lens = [16_000, 32_000]\n", "\n", "gemini_data = []\n", "\n", "for max_prompt_len in max_prompt_lens:\n", "    for index in range(len(ovivo_data)):\n", "        try:\n", "            gemini_data.append(run_gemini_for_ovivo(index, model_name, max_prompt_len))\n", "        except:\n", "            continue\n", "\n", "gemini_data = pd.DataFrame(gemini_data)\n", "gemini_data.groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>first_token_time_s</th>\n", "      <th>generation_time_s</th>\n", "      <th>generation_time_per_token_s</th>\n", "    </tr>\n", "    <tr>\n", "      <th>max_prompt_len</th>\n", "      <th>model_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">16000</th>\n", "      <th>claude-3-5-sonnet-20240620</th>\n", "      <td>2.298000</td>\n", "      <td>6.603034</td>\n", "      <td>0.013880</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gemini-1.5-flash-preview-0514</th>\n", "      <td>0.924215</td>\n", "      <td>4.661209</td>\n", "      <td>0.009123</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">32000</th>\n", "      <th>claude-3-5-sonnet-20240620</th>\n", "      <td>5.118665</td>\n", "      <td>8.304519</td>\n", "      <td>0.016221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gemini-1.5-flash-preview-0514</th>\n", "      <td>1.373568</td>\n", "      <td>4.924412</td>\n", "      <td>0.008965</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              first_token_time_s  \\\n", "max_prompt_len model_name                                          \n", "16000          claude-3-5-sonnet-20240620               2.298000   \n", "               gemini-1.5-flash-preview-0514            0.924215   \n", "32000          claude-3-5-sonnet-20240620               5.118665   \n", "               gemini-1.5-flash-preview-0514            1.373568   \n", "\n", "                                              generation_time_s  \\\n", "max_prompt_len model_name                                         \n", "16000          claude-3-5-sonnet-20240620              6.603034   \n", "               gemini-1.5-flash-preview-0514           4.661209   \n", "32000          claude-3-5-sonnet-20240620              8.304519   \n", "               gemini-1.5-flash-preview-0514           4.924412   \n", "\n", "                                              generation_time_per_token_s  \n", "max_prompt_len model_name                                                  \n", "16000          claude-3-5-sonnet-20240620                        0.013880  \n", "               gemini-1.5-flash-preview-0514                     0.009123  \n", "32000          claude-3-5-sonnet-20240620                        0.016221  \n", "               gemini-1.5-flash-preview-0514                     0.008965  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["full_data = pd.concat([anthropic_data, gemini_data])\n", "\n", "full_data[[\"max_prompt_len\", \"model_name\", \"first_token_time_s\", \"generation_time_s\", \"generation_time_per_token_s\"]].groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)\n", "# df = pd.concat([df, df2[df2[\"n_input_tokens_s\"] < 32000]])\n", "# data.to_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")\n", "\n", "\n", "# df2 = pd.read_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")\n", "# df3 = pd.read_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
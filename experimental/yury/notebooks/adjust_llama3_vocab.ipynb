{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['version', 'truncation', 'padding', 'added_tokens', 'normalizer', 'pre_tokenizer', 'post_processor', 'decoder', 'model'])\n", "Number of added tokens before:  256\n", "Number of added tokens after:  10\n", "{'id': 128000, 'content': '<|begin_of_text|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128001, 'content': '<|end_of_text|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128002, 'content': '<|reserved_special_token_0|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128003, 'content': '<|reserved_special_token_1|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128004, 'content': '<|reserved_special_token_2|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128005, 'content': '<|reserved_special_token_3|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128006, 'content': '<|start_header_id|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128007, 'content': '<|end_header_id|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128008, 'content': '<|reserved_special_token_4|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n", "{'id': 128009, 'content': '<|eot_id|>', 'single_word': False, 'lstrip': False, 'rstrip': False, 'normalized': False, 'special': True}\n"]}], "source": ["INPUT_LLAMA3_TOKENIZER = \"/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B/tokenizer.json\"\n", "OUTPUT_LLAMA3_TOKENIZER = \"/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B-adjusted-vocab/tokenizer.json\"\n", "\n", "with open(INPUT_LLAMA3_TOKENIZER) as f:\n", "    llama3_tokenizer = json.load(f)\n", "\n", "print(llama3_tokenizer.keys())\n", "\n", "# Adjust added_tokens -- remove <|reserved_special_token_X|>\n", "CUT_OFF_ID = 128010\n", "print('Number of added tokens before: ', len(llama3_tokenizer['added_tokens']))\n", "llama3_tokenizer['added_tokens'] = [token for token in llama3_tokenizer['added_tokens'] if not token['id'] >= CUT_OFF_ID]\n", "print('Number of added tokens after: ', len(llama3_tokenizer['added_tokens']))\n", "for token in llama3_tokenizer['added_tokens']:\n", "    print(token)\n", "\n", "with open(OUTPUT_LLAMA3_TOKENIZER, 'w') as f:\n", "    json.dump(llama3_tokenizer, f, indent=2)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['added_tokens_decoder', 'bos_token', 'clean_up_tokenization_spaces', 'eos_token', 'model_input_names', 'model_max_length', 'tokenizer_class'])\n", "Number of added tokens before:  256\n", "Number of added tokens after:  10\n", "128000 {'content': '<|begin_of_text|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128001 {'content': '<|end_of_text|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128002 {'content': '<|reserved_special_token_0|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128003 {'content': '<|reserved_special_token_1|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128004 {'content': '<|reserved_special_token_2|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128005 {'content': '<|reserved_special_token_3|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128006 {'content': '<|start_header_id|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128007 {'content': '<|end_header_id|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128008 {'content': '<|reserved_special_token_4|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n", "128009 {'content': '<|eot_id|>', 'lstrip': False, 'normalized': False, 'rstrip': False, 'single_word': False, 'special': True}\n"]}], "source": ["INPUT_LLAMA3_TOKENIZER_CONFIG = \"/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B/tokenizer_config.json\"\n", "OUTPUT_LLAMA3_TOKENIZER_CONFIG = \"/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B-adjusted-vocab/tokenizer_config.json\"\n", "\n", "with open(INPUT_LLAMA3_TOKENIZER_CONFIG) as f:\n", "    llama3_tokenizer_config = json.load(f)\n", "\n", "print(llama3_tokenizer_config.keys())\n", "\n", "# Adjust added_tokens -- remove <|reserved_special_token_X|>\n", "CUT_OFF_ID = 128010\n", "print('Number of added tokens before: ', len(llama3_tokenizer_config['added_tokens_decoder']))\n", "llama3_tokenizer_config['added_tokens_decoder'] = {token_id: token for token_id, token in llama3_tokenizer_config['added_tokens_decoder'].items() if not int(token_id) >= CUT_OFF_ID}\n", "print('Number of added tokens after: ', len(llama3_tokenizer_config['added_tokens_decoder']))\n", "for token_id, token in llama3_tokenizer_config['added_tokens_decoder'].items():\n", "    print(token_id, token)\n", "\n", "with open(OUTPUT_LLAMA3_TOKENIZER_CONFIG, 'w') as f:\n", "    json.dump(llama3_tokenizer_config, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
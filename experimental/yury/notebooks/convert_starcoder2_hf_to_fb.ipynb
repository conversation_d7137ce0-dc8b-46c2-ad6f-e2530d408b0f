{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from safetensors.torch import load_model, load_file\n", "import torch\n", "import json\n", "import fairscale.nn.model_parallel as mpu\n", "from research.fastbackward.checkpointing.huggingface import (\n", "    unpermute_qk_weights_from_hf_to_llama,\n", ")\n", "from research.fastbackward.checkpointing.utils import split_weight_for_model_parallel\n", "\n", "from functools import partial\n", "import glob\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"activation_function\": \"gelu\",\n", "  \"architectures\": [\n", "    \"Starcoder2ForCausalLM\"\n", "  ],\n", "  \"attention_dropout\": 0.0,\n", "  \"attention_softmax_in_fp32\": true,\n", "  \"bos_token_id\": 0,\n", "  \"eos_token_id\": 0,\n", "  \"hidden_act\": \"gelu_pytorch_tanh\",\n", "  \"hidden_size\": 4608,\n", "  \"initializer_range\": 0.018042,\n", "  \"intermediate_size\": 18432,\n", "  \"layer_norm_epsilon\": 1e-05,\n", "  \"max_position_embeddings\": 16384,\n", "  \"mlp_type\": \"default\",\n", "  \"model_type\": \"starcoder2\",\n", "  \"norm_epsilon\": 1e-05,\n", "  \"norm_type\": \"layer_norm\",\n", "  \"num_attention_heads\": 36,\n", "  \"num_hidden_layers\": 32,\n", "  \"num_key_value_heads\": 4,\n", "  \"rope_theta\": 1000000,\n", "  \"scale_attention_softmax_in_fp32\": true,\n", "  \"scale_attn_weights\": true,\n", "  \"sliding_window\": 4096,\n", "  \"torch_dtype\": \"bfloat16\",\n", "  \"transformers_version\": \"4.37.0.dev0\",\n", "  \"use_bias\": true,\n", "  \"use_cache\": true,\n", "  \"vocab_size\": 49152\n", "}\n"]}], "source": ["# See `load_starcoder_hf_checkpoint` from research/fastbackward/checkpointing.py\n", "\n", "# checkpoint_dir = Path('/mnt/efs/augment/checkpoints/starcoder2-3b/')\n", "# output_checkpoint_path = Path('/mnt/efs/augment/checkpoints/starcoder2-3b-fb/')\n", "# mp_world_size = 1\n", "\n", "checkpoint_dir = Path(\"/mnt/efs/augment/checkpoints/starcoder2-7b\")\n", "output_checkpoint_path = Path(\"/mnt/efs/augment/checkpoints/starcoder2-7b-fb-mp4\")\n", "mp_world_size = 4\n", "\n", "# checkpoint_dir = Path('/mnt/efs/augment/checkpoints/starcoder2-15b/')\n", "# output_checkpoint_path = Path('/mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp1/')\n", "# mp_world_size = 1\n", "\n", "# checkpoint_dir = Path('/mnt/efs/augment/checkpoints/starcoder2-15b/')\n", "# output_checkpoint_path = Path('/mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2/')\n", "# mp_world_size = 2\n", "\n", "\n", "config_file = checkpoint_dir / \"config.json\"\n", "with config_file.open(mode=\"r\") as fh:\n", "    cfg = json.load(fh)\n", "print(json.dumps(cfg, indent=2))\n", "\n", "\n", "params = {\n", "    \"dim\": cfg[\"hidden_size\"],\n", "    \"n_layers\": cfg[\"num_hidden_layers\"],\n", "    \"n_heads\": cfg[\"num_attention_heads\"],\n", "    \"n_kv_heads\": cfg[\"num_key_value_heads\"],\n", "    \"rope_theta\": cfg[\"rope_theta\"],\n", "    \"vocab_size\": cfg[\"vocab_size\"],\n", "    \"multiple_of\": 256,\n", "    \"ffn_dim_multiplier\": 1,\n", "    \"norm_eps\": cfg[\"norm_epsilon\"],\n", "    \"llama\": <PERSON><PERSON><PERSON>,\n", "    \"starcoder\": True,\n", "    # \"rope_scaling_factor\": 1.0\n", "}\n", "\n", "if not output_checkpoint_path.exists():\n", "    output_checkpoint_path.mkdir()\n", "\n", "json.dump(params, open(output_checkpoint_path / \"params.json\", \"w\"), indent=2)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["nlayers = cfg[\"num_hidden_layers\"]\n", "nheads = cfg[\"num_attention_heads\"]\n", "hidden_dim = cfg[\"hidden_size\"]\n", "headdim = hidden_dim // nheads\n", "\n", "for mp_rank in range(mp_world_size):\n", "    model_pattern = checkpoint_dir / \"model*.safetensors\"\n", "    files = glob.glob(model_pattern.as_posix())\n", "    sd = load_file(files[0])\n", "    for f in files[1:]:\n", "        sd.update(load_file(f))\n", "\n", "    result_sd = {}\n", "    if mp_world_size is None and mp_rank is None:\n", "        mp_world_size = mpu.get_model_parallel_world_size()\n", "        mp_rank = mpu.get_model_parallel_rank()\n", "    else:\n", "        assert (\n", "            mp_world_size is not None and mp_rank is not None\n", "        ), \"Must set both or neither of mp_{world_size,rank}\"\n", "\n", "    mp_split_fn = partial(\n", "        split_weight_for_model_parallel,\n", "        world_size=mp_world_size,\n", "        rank=mp_rank,\n", "    )\n", "\n", "    remaps = [\n", "        (\"model.embed_tokens.weight\", \"tok_embeddings.weight\", 1),\n", "        (\"model.norm.weight\", \"norm.weight\", None),\n", "        (\"model.norm.bias\", \"norm.bias\", None),\n", "    ]\n", "    if \"lm_head.weight\" in sd:\n", "        remaps.append((\"lm_head.weight\", \"output.weight\", 0))\n", "    else:\n", "        remaps.append((\"model.embed_tokens.weight\", \"output.weight\", 0))\n", "\n", "    layer_remaps = [\n", "        (\n", "            \"model.layers.{}.input_layernorm.weight\",\n", "            \"layers.{}.attention_norm.weight\",\n", "            None,\n", "        ),\n", "        (\"model.layers.{}.input_layernorm.bias\", \"layers.{}.attention_norm.bias\", None),\n", "        (\"model.layers.{}.self_attn.q_proj.weight\", \"layers.{}.attention.wq.weight\", 0),\n", "        (\"model.layers.{}.self_attn.q_proj.bias\", \"layers.{}.attention.wq.bias\", 0),\n", "        (\"model.layers.{}.self_attn.k_proj.weight\", \"layers.{}.attention.wk.weight\", 0),\n", "        (\"model.layers.{}.self_attn.k_proj.bias\", \"layers.{}.attention.wk.bias\", 0),\n", "        (\"model.layers.{}.self_attn.v_proj.weight\", \"layers.{}.attention.wv.weight\", 0),\n", "        (\"model.layers.{}.self_attn.v_proj.bias\", \"layers.{}.attention.wv.bias\", 0),\n", "        (\"model.layers.{}.self_attn.o_proj.weight\", \"layers.{}.attention.wo.weight\", 1),\n", "        (\"model.layers.{}.self_attn.o_proj.bias\", \"layers.{}.attention.wo.bias\", None),\n", "        (\n", "            \"model.layers.{}.post_attention_layernorm.weight\",\n", "            \"layers.{}.ffn_norm.weight\",\n", "            None,\n", "        ),\n", "        (\n", "            \"model.layers.{}.post_attention_layernorm.bias\",\n", "            \"layers.{}.ffn_norm.bias\",\n", "            None,\n", "        ),\n", "        (\"model.layers.{}.mlp.c_fc.weight\", \"layers.{}.feed_forward.w1.weight\", 0),\n", "        (\"model.layers.{}.mlp.c_fc.bias\", \"layers.{}.feed_forward.w1.bias\", 0),\n", "        (\"model.layers.{}.mlp.c_proj.weight\", \"layers.{}.feed_forward.w2.weight\", 1),\n", "        (\"model.layers.{}.mlp.c_proj.bias\", \"layers.{}.feed_forward.w2.bias\", None),\n", "    ]\n", "\n", "    for src, dst, split_dim in remaps:\n", "        result = sd[src]\n", "        result_sd[dst] = mp_split_fn(result, split_dim)\n", "        if src != \"model.embed_tokens.weight\":\n", "            del sd[src]\n", "\n", "    del sd[\"model.embed_tokens.weight\"]\n", "\n", "    for layernum in range(nlayers):\n", "        for src_fmt, dst_fmt, split_dim in layer_remaps:\n", "            src = src_fmt.format(layernum)\n", "            dst = dst_fmt.format(layernum)\n", "            result = sd[src]\n", "            if src.endswith(\"q_proj.weight\"):\n", "                result = unpermute_qk_weights_from_hf_to_llama(\n", "                    result, params[\"n_heads\"], headdim, params[\"dim\"]\n", "                )\n", "            elif src.endswith(\"q_proj.bias\"):\n", "                result = unpermute_qk_weights_from_hf_to_llama(\n", "                    result, params[\"n_heads\"], headdim, 1\n", "                )\n", "            elif src.endswith(\"k_proj.weight\"):\n", "                result = unpermute_qk_weights_from_hf_to_llama(\n", "                    result, params[\"n_kv_heads\"], headdim, params[\"dim\"]\n", "                )\n", "            elif src.endswith(\"k_proj.bias\"):\n", "                result = unpermute_qk_weights_from_hf_to_llama(\n", "                    result, params[\"n_kv_heads\"], headdim, 1\n", "                )\n", "\n", "            result_sd[dst] = mp_split_fn(result, split_dim)\n", "            del sd[src]\n", "\n", "    assert len(sd) == 0\n", "    torch.save(result_sd, output_checkpoint_path / f\"consolidated.{mp_rank:02d}.pth\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = torch.load(output_checkpoint_path / \"consolidated.00.pth\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mp_split_fn = partial(\n", "    split_weight_for_model_parallel,\n", "    mp_world_size=2,\n", "    mp_rank=0,\n", ")\n", "\n", "(\n", "    x[\"layers.23.attention.wv.weight\"].shape,\n", "    mp_split_fn(x[\"layers.23.attention.wv.weight\"], 1).shape,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    x[\"layers.21.attention.wq.bias\"].shape,\n", "    mp_split_fn(x[\"layers.21.attention.wq.bias\"], None).shape,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    sd[\"model.layers.0.self_attn.q_proj.weight\"].shape,\n", "    sd[\"model.layers.0.self_attn.q_proj.bias\"].shape,\n", ")\n", "\n", "\n", "# The HF checkpoints permute the QK matrices to match their (different) rotarty embedding implementation.\n", "# See: https://github.com/huggingface/transformers/blob/main/src/transformers/models/llama/convert_llama_weights_to_hf.py#L123\n", "# This function inverts that function to match the llama layout.\n", "# def custom_unpermute_qk_weights_from_hf_to_llama(\n", "#     w: torch.Tensor, nheads: int, headdim: int, hidden_size: int\n", "# ):\n", "#     orig_shape = w.shape\n", "#     return (\n", "#         w.view(nheads, 2, headdim // 2, hidden_size).transpose(1, 2).reshape(orig_shape)\n", "#     )\n", "\n", "\n", "# unpermute_qk_weights_from_hf_to_llama(sd[\"model.layers.0.self_attn.q_proj.weight\"], params[\"n_heads\"], headdim, params[\"dim\"])\n", "# custom_unpermute_qk_weights_from_hf_to_llama(sd[\"model.layers.0.self_attn.q_proj.bias\"], params[\"n_heads\"], headdim, 1).shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "x_fb = torch.load(\"/tmp/fb/attention_input.pth\").float()\n", "x_hg = torch.load(\"/tmp/hg_nobias/attention_input.pth\").cpu().float()\n", "print(torch.allclose(x_fb, x_hg))\n", "\n", "xq_fb = torch.load(\"/tmp/fb/xq.pth\").cpu().float()\n", "xq_hg = torch.load(\"/tmp/hg_nobias/xq\").cpu().float()\n", "print(torch.allclose(xq_fb, xq_hg))\n", "\n", "cos_fb = torch.load(\"/tmp/fb/cos.pth\").cpu().float()\n", "cos_hg = torch.load(\"/tmp/hg/cos\").cpu().float()\n", "print(torch.allclose(cos_hg[:, :64], cos_hg[:, 64:]))\n", "print(torch.allclose(cos_fb[0, :, 0], cos_hg[:, :64], rtol=1e-2))\n", "\n", "sin_fb = torch.load(\"/tmp/fb/sin.pth\").cpu().float()\n", "sin_hg = torch.load(\"/tmp/hg/sin\").cpu().float()\n", "print(torch.allclose(sin_hg[:, :64], sin_hg[:, 64:]))\n", "print(torch.allclose(sin_fb[0, :, 0], sin_hg[:, :64], rtol=1e-2))\n", "\n", "xq_with_rope_fb = torch.load(\"/tmp/fb/xq_with_rope.pth\").cpu().float()\n", "xq_with_rope_hg = torch.load(\"/tmp/hg_nobias/xq_with_rope\").cpu().float()\n", "print(torch.allclose(xq_with_rope_fb, xq_with_rope_hg.transpose(1, 2), atol=1e-1))\n", "\n", "# query_states_hg = torch.load(\"/tmp/hf/query_states.pth\")\n", "# query_states_hg.shape\n", "\n", "\n", "final_output_fb = torch.load(\"/tmp/fb/output.pth\").cpu().float()\n", "final_output_hf = torch.load(\"/tmp/hg_nobias/final_output\").cpu().float()\n", "torch.allclose(final_output_fb, final_output_hf, atol=1e-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bsz = 1\n", "seqlen = 933\n", "actual_seqlen = 1\n", "n_local_heads = 24\n", "actual_heads = 1\n", "head_dim = 128\n", "\n", "# Interleave pairwise in the hidden dimension and interpret each pair as a complex number.\n", "xq_reshaped_fb = xq_fb.view(bsz, seqlen, n_local_heads, head_dim)\n", "xq_reshaped_fb = xq_reshaped_fb[:, :actual_seqlen, :actual_heads, :]\n", "\n", "x_ = xq_reshaped_fb.float().reshape(*xq_reshaped_fb.shape[:-1], -1, 2)\n", "x_real = x_[..., 0]\n", "x_imag = x_[..., 1]\n", "# Perform the complex product (cos + i sin) * (x_real + i x_imag).\n", "cos_real = cos_fb[:, :actual_seqlen] * x_real\n", "sin_imag = sin_fb[:, :actual_seqlen] * x_imag\n", "cos_imag = cos_fb[:, :actual_seqlen] * x_imag\n", "sin_real = sin_fb[:, :actual_seqlen] * x_real\n", "real_part = (cos_real - sin_imag).type_as(xq_reshaped_fb)\n", "imag_part = (cos_imag + sin_real).type_as(xq_reshaped_fb)\n", "# Stack the parts and de-interleave along the last dimension\n", "xxx_fb = torch.stack((real_part, imag_part), dim=-1).flatten(-2)\n", "\n", "torch.allclose(xq_with_rope_fb[:, :actual_seqlen, :actual_heads], xxx_fb, rtol=1e-2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = torch.arange(16)[None, None, None, :]\n", "print(test)\n", "\n", "test_ = test.reshape(*test.shape[:-1], -1, 2)\n", "test_real = test_[..., 0]\n", "test_imag = test_[..., 1]\n", "\n", "print(\"real\", test_real)\n", "\n", "print(torch.stack((test_real, test_imag), dim=-1).flatten(-2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xq_reshaped_hg = xq_hg.view(bsz, seqlen, n_local_heads, head_dim).transpose(1, 2)\n", "xq_reshaped_hg = xq_reshaped_hg[:, :actual_heads, :actual_seqlen]\n", "\n", "cos_ = cos_hg[None, None, :actual_seqlen, :]\n", "sin_ = sin_hg[None, None, :actual_seqlen, :]\n", "\n", "x1 = xq_reshaped_hg[..., : xq_reshaped_hg.shape[-1] // 2]\n", "x2 = xq_reshaped_hg[..., xq_reshaped_hg.shape[-1] // 2 :]\n", "\n", "rotated_xq_hg = torch.cat((-x2, x1), dim=-1)\n", "\n", "xxx_hg = (xq_reshaped_hg * cos_) + (rotated_xq_hg * sin_)\n", "\n", "torch.allclose(xq_with_rope_hg[:, :actual_heads, :actual_seqlen], xxx_hg, atol=1e-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = torch.arange(16)[None, None, None, :]\n", "print(test)\n", "\n", "test1 = test[..., : test.shape[-1] // 2]\n", "print(\"real\", test1)\n", "test2 = test[..., test.shape[-1] // 2 :]\n", "print(torch.cat((-test2, test1), dim=-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_real.shape, x1.shape\n", "x_real - x1\n", "\n", "(\n", "    cos_fb[:, :actual_seqlen] * x_real\n", "    - cos_[:, :, :, :64] * x1\n", "    + sin_fb[:, :actual_seqlen] * x_imag\n", "    - sin_[:, :, :, :64] * x2\n", ")\n", "# xxx_hg[:, :, :, ::2] - real_part"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_imag.shape, x2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["real_part"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xq_with_rope_fb.shape, xq_with_rope_hg.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(xxx_fb - xxx_hg.transpose(1, 2)).abs().max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xxx_fb.shape, xxx_hg.transpose(1, 2).shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xxx_fb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xxx_hg.transpose(1, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
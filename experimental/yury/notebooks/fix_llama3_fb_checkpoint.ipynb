{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import torch\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama3.1/original/Meta-Llama-3.1-70B\")\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama3.1/fb/Meta-Llama-3.1-70B\")\n", "MODEL_SIZE = \"70B\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "embedding_shapes = {\n", "    \"70B\": (128256, 8192),\n", "    \"405B\": (128256, 16384),\n", "}\n", "\n", "weight_paths = sorted(glob.glob(str(INPUT_PATH / \"consolidated.*.pth\")))\n", "assert len(weight_paths) == 8\n", "expected_shape = embedding_shapes[MODEL_SIZE]\n", "expected_shard_shape = (expected_shape[0] // 8, expected_shape[1])\n", "\n", "embeddings = []\n", "\n", "for weight_path in tqdm.tqdm(weight_paths):\n", "    print(weight_path)\n", "    data = torch.load(weight_path, map_location=\"cpu\")\n", "    embedding = data[\"tok_embeddings.weight\"]\n", "    assert embedding.shape == expected_shard_shape\n", "    del data\n", "    embeddings.append(embedding)\n", "\n", "embedding = torch.cat(embeddings, dim=0)\n", "print(embedding.shape)\n", "assert embedding.shape == expected_shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_embeddings = torch.split(embedding, embedding.shape[1] // 8, dim=1)\n", "print(len(new_embeddings))\n", "print([x.shape for x in new_embeddings])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_PATH.mkdir(exist_ok=True)\n", "for weight_path, new_embedding in zip(weight_paths, new_embeddings):\n", "    data = torch.load(weight_path, map_location=\"cpu\")\n", "    data[\"tok_embeddings.weight\"] = new_embedding.contiguous().clone().detach()\n", "    torch.save(data, OUTPUT_PATH / Path(weight_path).name)\n", "    del data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from pathlib import Path\n", "from research.fastbackward.checkpointing.huggingface import load_huggingface_checkpoint\n", "\n", "\n", "INPUT_DIR = Path(\"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct\")\n", "N_MP = 2\n", "OUTPUT_DIR = Path(\n", "    f\"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct-fb-mp{N_MP}\"\n", ")\n", "\n", "OUTPUT_DIR.mkdir(parents=False, exist_ok=True)\n", "\n", "for mp_rank in range(N_MP):\n", "    state_dict = load_huggingface_checkpoint(\n", "        checkpoint_location=\"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct\",\n", "        mp_world_size=N_MP,\n", "        mp_rank=mp_rank,\n", "    )\n", "\n", "    torch.save(\n", "        state_dict,\n", "        OUTPUT_DIR / f\"consolidated.{mp_rank:02d}.pth\",\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86042/overview\n", "# but with increased max_seq_len\n", "params = {\n", "    \"dim\": 3584,\n", "    \"n_layers\": 28,\n", "    \"n_heads\": -1,\n", "    \"n_kv_heads\": -1,\n", "    \"rope_theta\": -1.0,\n", "    \"rope_scaling_factor\": -1.0,\n", "    \"vocab_size\": 152064,\n", "    \"multiple_of\": 512,\n", "    \"ffn_dim_multiplier\": 1.95,\n", "    \"rotary_config\": {\n", "        \"rotary_ratio\": 1.0,\n", "        \"rotary_theta\": 1000000.0,\n", "        \"max_position_embeddings\": 32768,\n", "        \"rotary_interleave\": True,\n", "        \"ext_config\": {\n", "            \"rotary_scaling_factor\": 1.0,\n", "            \"__type\": \"DeepSeekV1ExtensionConfig\",\n", "        },\n", "    },\n", "    \"attn_config\": {\n", "        \"hidden_dim\": 3584,\n", "        \"n_heads\": 28,\n", "        \"n_kv_heads\": 4,\n", "        \"norm_type\": \"rmsnorm\",\n", "        \"pos_embed_type\": \"rope\",\n", "        \"bias\": <PERSON><PERSON><PERSON>,\n", "        \"qkv_bias\": True,\n", "        \"__type\": \"GenericAttnSpec\",\n", "    },\n", "    \"ffn_config\": {\n", "        \"hidden_dim\": 3584,\n", "        \"intermediate_size\": 18944,\n", "        \"bias\": <PERSON><PERSON><PERSON>,\n", "        \"__type\": \"SwiGLUSpec\",\n", "    },\n", "    \"first_layer_ffn_config\": {\n", "        \"hidden_dim\": 3584,\n", "        \"intermediate_size\": 18944,\n", "        \"bias\": <PERSON><PERSON><PERSON>,\n", "        \"__type\": \"SwiGLUSpec\",\n", "    },\n", "    \"norm_eps\": 1e-06,\n", "    \"ffn_type\": \"\",\n", "    \"bias\": \"\",\n", "    \"norm_type\": \"rmsnorm\",\n", "    \"pos_embed_type\": \"rope\",\n", "    \"skip_output\": <PERSON><PERSON><PERSON>,\n", "    \"max_seq_len\": 32768,\n", "    \"max_generation_batch_size\": 32,\n", "    \"generation_mode\": <PERSON><PERSON><PERSON>,\n", "    \"use_activation_checkpointing\": True,\n", "    \"use_sequence_parallel\": True,\n", "}\n", "\n", "with (OUTPUT_DIR / \"params.json\").open(\"w\") as f:\n", "    json.dump(params, f, indent=2, default=str)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
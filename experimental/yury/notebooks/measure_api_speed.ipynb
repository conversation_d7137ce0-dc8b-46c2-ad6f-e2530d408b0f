{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.retrieval.libraries.types import Document\n", "from research.core.types import Chunk\n", "\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/yury/benchmark_api/ovivo_ios_only_cs.ethanol6_query_simple_chat.json\")\n", "\n", "with DATA_PATH.open(\"r\") as f:\n", "    ovivo_data = json.load(f)\n", "\n", "for sample in ovivo_data:\n", "    for retrieved_chunk in sample['retrieved_chunks']:\n", "        retrieved_chunk['parent_doc'] = Document(**retrieved_chunk['parent_doc'])\n", "    sample['retrieved_chunks'] = [Chunk(**r) for r in sample['retrieved_chunks']]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ANTHROPIC_TEMPLATE = \"\"\"You are an AI code assistant integrated into the VSCode IDE. Your primary role is to help software developers by answering their questions related to code and software engineering.\n", "\n", "Relevant code snippets from the developer's project are provided as background information.\n", "{% for chunk in retrieved_chunks %}\n", "# {{chunk.parent_doc.path}}\n", "{{chunk.text}}\n", "{% endfor %}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter\n", "\n", "def get_prompt(example, max_prompt_len):\n", "    prompt_formatter = ChatTemplateBasedPromptFormatter(\n", "        template=ANTHROPIC_TEMPLATE,\n", "        tokenizer_name=\"deepseekcoderinstructtokenizer\",\n", "        max_prompt_len=max_prompt_len,\n", "    )\n", "\n", "    model_input = ModelInput(\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        path=\"\",    \n", "        extra={\n", "            \"message\": example[\"question\"],\n", "            \"chat_history\": [],\n", "            \"path\": None,\n", "            \"selected_code\": None,\n", "        },\n", "        retrieved_chunks=example[\"retrieved_chunks\"],\n", "    )\n", "    return prompt_formatter.prepare_prompt(model_input)[1][\"prompt\"]\n", "\n", "\n", "max_prompt_len = 16_000\n", "get_prompt(ovivo_data[0], max_prompt_len)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import time\n", "\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "def run_anthropic(example, max_prompt_len, model_name):\n", "    client = anthropic.Anthropic(\n", "        api_key=\"************************************************************************************************************\",\n", "    )\n", "        \n", "    system_prompt = get_prompt(example, max_prompt_len)\n", "\n", "    n_input_tokens, n_output_tokens = None, None\n", "    start_time = time.time()\n", "    first_token_time = None\n", "    text = []\n", "\n", "    with client.messages.stream(\n", "        max_tokens=1024,\n", "        messages=[{\"role\": \"user\", \"content\": example[\"question\"]}],\n", "        system=system_prompt,\n", "        temperature=0.0,\n", "        model=model_name,\n", "    ) as stream:        \n", "        for event in stream:\n", "            if event.type == \"message_start\":\n", "                assert n_input_tokens is None\n", "                n_input_tokens = event.message.usage.input_tokens\n", "            elif event.type == \"content_block_start\":\n", "                assert len(event.content_block.text) == 0\n", "            elif event.type == \"content_block_delta\":                                \n", "                if first_token_time is None:\n", "                    assert len(event.delta.text) > 0\n", "                    first_token_time = time.time()\n", "                text.append(event.delta.text)\n", "            elif event.type == \"message_delta\":\n", "                assert n_output_tokens is None\n", "                n_output_tokens = event.usage.output_tokens \n", "        end_time = time.time()\n", "        return {\n", "            \"n_input_tokens_s\": n_input_tokens,\n", "            \"n_output_tokens_s\": n_output_tokens,\n", "            \"text\": \"\".join(text),\n", "            \"system_prompt\": system_prompt,\n", "            \"question\": example[\"question\"],\n", "            \"first_token_time_s\": first_token_time - start_time,\n", "            \"generation_time_s\": end_time - first_token_time,\n", "            \"generation_time_per_token_s\": (end_time - first_token_time) / n_output_tokens,\n", "        }\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/benchmark_api/anthropic_cache.jsonl\")\n", "def run_anthropic_for_ovivo(index, max_prompt_len, model_name):\n", "    global ovivo_data\n", "    datum = run_anthropic(ovivo_data[index], max_prompt_len, model_name)    \n", "    datum.update({\n", "        \"max_prompt_len\": max_prompt_len,\n", "        \"model_name\": model_name,\n", "        \"example_index\": index, \n", "    })\n", "    return datum"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (49460 > 16384). Running this sequence through the model will result in indexing errors\n", "Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (58068 > 16384). Running this sequence through the model will result in indexing errors\n", "Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (45603 > 16384). Running this sequence through the model will result in indexing errors\n", "Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (58789 > 16384). Running this sequence through the model will result in indexing errors\n", "Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (40792 > 16384). Running this sequence through the model will result in indexing errors\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>n_input_tokens_s</th>\n", "      <th>n_output_tokens_s</th>\n", "      <th>first_token_time_s</th>\n", "      <th>generation_time_s</th>\n", "      <th>generation_time_per_token_s</th>\n", "      <th>example_index</th>\n", "    </tr>\n", "    <tr>\n", "      <th>max_prompt_len</th>\n", "      <th>model_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">16000</th>\n", "      <th>claude-3-haiku-20240307</th>\n", "      <td>16010.7</td>\n", "      <td>476.0</td>\n", "      <td>1.267819</td>\n", "      <td>5.054653</td>\n", "      <td>0.011002</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-opus-20240229</th>\n", "      <td>16010.7</td>\n", "      <td>471.7</td>\n", "      <td>9.149573</td>\n", "      <td>18.849722</td>\n", "      <td>0.041038</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-sonnet-20240229</th>\n", "      <td>16010.7</td>\n", "      <td>537.6</td>\n", "      <td>6.907961</td>\n", "      <td>10.417782</td>\n", "      <td>0.020034</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">32000</th>\n", "      <th>claude-3-haiku-20240307</th>\n", "      <td>32010.5</td>\n", "      <td>499.1</td>\n", "      <td>2.130059</td>\n", "      <td>5.584307</td>\n", "      <td>0.011472</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-opus-20240229</th>\n", "      <td>32010.5</td>\n", "      <td>459.3</td>\n", "      <td>15.703198</td>\n", "      <td>21.425617</td>\n", "      <td>0.046691</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-sonnet-20240229</th>\n", "      <td>32010.5</td>\n", "      <td>508.1</td>\n", "      <td>14.141287</td>\n", "      <td>10.413538</td>\n", "      <td>0.021244</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                         n_input_tokens_s  n_output_tokens_s  \\\n", "max_prompt_len model_name                                                      \n", "16000          claude-3-haiku-20240307            16010.7              476.0   \n", "               claude-3-opus-20240229             16010.7              471.7   \n", "               claude-3-sonnet-20240229           16010.7              537.6   \n", "32000          claude-3-haiku-20240307            32010.5              499.1   \n", "               claude-3-opus-20240229             32010.5              459.3   \n", "               claude-3-sonnet-20240229           32010.5              508.1   \n", "\n", "                                         first_token_time_s  \\\n", "max_prompt_len model_name                                     \n", "16000          claude-3-haiku-20240307             1.267819   \n", "               claude-3-opus-20240229              9.149573   \n", "               claude-3-sonnet-20240229            6.907961   \n", "32000          claude-3-haiku-20240307             2.130059   \n", "               claude-3-opus-20240229             15.703198   \n", "               claude-3-sonnet-20240229           14.141287   \n", "\n", "                                         generation_time_s  \\\n", "max_prompt_len model_name                                    \n", "16000          claude-3-haiku-20240307            5.054653   \n", "               claude-3-opus-20240229            18.849722   \n", "               claude-3-sonnet-20240229          10.417782   \n", "32000          claude-3-haiku-20240307            5.584307   \n", "               claude-3-opus-20240229            21.425617   \n", "               claude-3-sonnet-20240229          10.413538   \n", "\n", "                                         generation_time_per_token_s  \\\n", "max_prompt_len model_name                                              \n", "16000          claude-3-haiku-20240307                      0.011002   \n", "               claude-3-opus-20240229                       0.041038   \n", "               claude-3-sonnet-20240229                     0.020034   \n", "32000          claude-3-haiku-20240307                      0.011472   \n", "               claude-3-opus-20240229                       0.046691   \n", "               claude-3-sonnet-20240229                     0.021244   \n", "\n", "                                         example_index  \n", "max_prompt_len model_name                               \n", "16000          claude-3-haiku-20240307             4.5  \n", "               claude-3-opus-20240229              4.5  \n", "               claude-3-sonnet-20240229            4.5  \n", "32000          claude-3-haiku-20240307             4.5  \n", "               claude-3-opus-20240229              4.5  \n", "               claude-3-sonnet-20240229            4.5  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd \n", "\n", "max_prompt_lens = [16_000, 32_000]\n", "model_names = [\"claude-3-haiku-20240307\", \"claude-3-sonnet-20240229\", \"claude-3-opus-20240229\"]\n", "\n", "anthropic_data = []\n", "\n", "for max_prompt_len in max_prompt_lens:\n", "    for model_name in model_names:\n", "        for index in range(len(ovivo_data)):\n", "            anthropic_data.append(run_anthropic_for_ovivo(index, max_prompt_len, model_name))\n", "\n", "anthropic_data = pd.DataFrame(anthropic_data)\n", "anthropic_data.groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import vertexai\n", "from vertexai.generative_models import GenerativeModel, ChatSession\n", "project_id = \"gemini-pro-420822\"\n", "location = \"us-central1\"\n", "vertexai.init(project=project_id, location=location)\n", "GOOGLE_MODEL = GenerativeModel(\"gemini-1.5-pro-preview-0409\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "def run_gemini(example, max_prompt_len):\n", "    global GOOGLE_MODEL\n", "    chat = GOOGLE_MODEL.start_chat()\n", "        \n", "    system_prompt = get_prompt(example, max_prompt_len)\n", "    message = system_prompt + \"\\n\" + example[\"question\"]\n", "\n", "    start_time = time.time()\n", "    first_token_time = None\n", "    text = []\n", "\n", "    responses = chat.send_message(message, stream=True)\n", "    for chunk in responses:\n", "        if first_token_time is None:\n", "            assert len(chunk.text) > 0\n", "            first_token_time = time.time()\n", "        text.append(chunk.text)\n", "    \n", "    end_time = time.time()\n", "    n_input_tokens = chunk.usage_metadata.prompt_token_count\n", "    n_output_tokens = chunk.usage_metadata.candidates_token_count\n", "    assert chunk.usage_metadata.total_token_count == n_input_tokens + n_output_tokens\n", "\n", "    return {\n", "        \"n_input_tokens_s\": n_input_tokens,\n", "        \"n_output_tokens_s\": n_output_tokens,\n", "        \"text\": \"\".join(text),\n", "        \"system_prompt\": system_prompt,\n", "        \"question\": example[\"question\"],\n", "        \"first_token_time_s\": first_token_time - start_time,\n", "        \"generation_time_s\": end_time - first_token_time,\n", "        \"generation_time_per_token_s\": (end_time - first_token_time) / n_output_tokens,\n", "    }\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/benchmark_api/gemini_cache.jsonl\")\n", "def run_gemini_for_ovivo(index, max_prompt_len):\n", "    global ovivo_data\n", "    datum = run_gemini(ovivo_data[index], max_prompt_len)    \n", "    datum.update({\n", "        \"max_prompt_len\": max_prompt_len,\n", "        \"model_name\": \"gemini-1.5-pro-preview-0409\",\n", "        \"example_index\": index, \n", "    })\n", "    return datum\n", "\n", "max_prompt_len = 16_000\n", "run_gemini_for_ovivo(0, max_prompt_len)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>n_input_tokens_s</th>\n", "      <th>n_output_tokens_s</th>\n", "      <th>first_token_time_s</th>\n", "      <th>generation_time_s</th>\n", "      <th>generation_time_per_token_s</th>\n", "      <th>example_index</th>\n", "    </tr>\n", "    <tr>\n", "      <th>max_prompt_len</th>\n", "      <th>model_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16000</th>\n", "      <th>gemini-1.5-pro-preview-0409</th>\n", "      <td>13593.0</td>\n", "      <td>568.1</td>\n", "      <td>2.314955</td>\n", "      <td>12.960070</td>\n", "      <td>0.022806</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32000</th>\n", "      <th>gemini-1.5-pro-preview-0409</th>\n", "      <td>27312.1</td>\n", "      <td>603.7</td>\n", "      <td>3.273503</td>\n", "      <td>13.689073</td>\n", "      <td>0.022607</td>\n", "      <td>4.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            n_input_tokens_s  \\\n", "max_prompt_len model_name                                      \n", "16000          gemini-1.5-pro-preview-0409           13593.0   \n", "32000          gemini-1.5-pro-preview-0409           27312.1   \n", "\n", "                                            n_output_tokens_s  \\\n", "max_prompt_len model_name                                       \n", "16000          gemini-1.5-pro-preview-0409              568.1   \n", "32000          gemini-1.5-pro-preview-0409              603.7   \n", "\n", "                                            first_token_time_s  \\\n", "max_prompt_len model_name                                        \n", "16000          gemini-1.5-pro-preview-0409            2.314955   \n", "32000          gemini-1.5-pro-preview-0409            3.273503   \n", "\n", "                                            generation_time_s  \\\n", "max_prompt_len model_name                                       \n", "16000          gemini-1.5-pro-preview-0409          12.960070   \n", "32000          gemini-1.5-pro-preview-0409          13.689073   \n", "\n", "                                            generation_time_per_token_s  \\\n", "max_prompt_len model_name                                                 \n", "16000          gemini-1.5-pro-preview-0409                     0.022806   \n", "32000          gemini-1.5-pro-preview-0409                     0.022607   \n", "\n", "                                            example_index  \n", "max_prompt_len model_name                                  \n", "16000          gemini-1.5-pro-preview-0409            4.5  \n", "32000          gemini-1.5-pro-preview-0409            4.5  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "max_prompt_lens = [16_000, 32_000]\n", "\n", "gemini_data = []\n", "\n", "for max_prompt_len in max_prompt_lens:\n", "    for index in range(len(ovivo_data)):\n", "        gemini_data.append(run_gemini_for_ovivo(index, max_prompt_len))\n", "\n", "gemini_data = pd.DataFrame(gemini_data)\n", "gemini_data.groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>first_token_time_s</th>\n", "      <th>generation_time_s</th>\n", "      <th>generation_time_per_token_s</th>\n", "    </tr>\n", "    <tr>\n", "      <th>max_prompt_len</th>\n", "      <th>model_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">16000</th>\n", "      <th>claude-3-haiku-20240307</th>\n", "      <td>1.267819</td>\n", "      <td>5.054653</td>\n", "      <td>0.011002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-opus-20240229</th>\n", "      <td>9.149573</td>\n", "      <td>18.849722</td>\n", "      <td>0.041038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-sonnet-20240229</th>\n", "      <td>6.907961</td>\n", "      <td>10.417782</td>\n", "      <td>0.020034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gemini-1.5-pro-preview-0409</th>\n", "      <td>2.314955</td>\n", "      <td>12.960070</td>\n", "      <td>0.022806</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">32000</th>\n", "      <th>claude-3-haiku-20240307</th>\n", "      <td>2.130059</td>\n", "      <td>5.584307</td>\n", "      <td>0.011472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-opus-20240229</th>\n", "      <td>15.703198</td>\n", "      <td>21.425617</td>\n", "      <td>0.046691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claude-3-sonnet-20240229</th>\n", "      <td>14.141287</td>\n", "      <td>10.413538</td>\n", "      <td>0.021244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gemini-1.5-pro-preview-0409</th>\n", "      <td>3.273503</td>\n", "      <td>13.689073</td>\n", "      <td>0.022607</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            first_token_time_s  \\\n", "max_prompt_len model_name                                        \n", "16000          claude-3-haiku-20240307                1.267819   \n", "               claude-3-opus-20240229                 9.149573   \n", "               claude-3-sonnet-20240229               6.907961   \n", "               gemini-1.5-pro-preview-0409            2.314955   \n", "32000          claude-3-haiku-20240307                2.130059   \n", "               claude-3-opus-20240229                15.703198   \n", "               claude-3-sonnet-20240229              14.141287   \n", "               gemini-1.5-pro-preview-0409            3.273503   \n", "\n", "                                            generation_time_s  \\\n", "max_prompt_len model_name                                       \n", "16000          claude-3-haiku-20240307               5.054653   \n", "               claude-3-opus-20240229               18.849722   \n", "               claude-3-sonnet-20240229             10.417782   \n", "               gemini-1.5-pro-preview-0409          12.960070   \n", "32000          claude-3-haiku-20240307               5.584307   \n", "               claude-3-opus-20240229               21.425617   \n", "               claude-3-sonnet-20240229             10.413538   \n", "               gemini-1.5-pro-preview-0409          13.689073   \n", "\n", "                                            generation_time_per_token_s  \n", "max_prompt_len model_name                                                \n", "16000          claude-3-haiku-20240307                         0.011002  \n", "               claude-3-opus-20240229                          0.041038  \n", "               claude-3-sonnet-20240229                        0.020034  \n", "               gemini-1.5-pro-preview-0409                     0.022806  \n", "32000          claude-3-haiku-20240307                         0.011472  \n", "               claude-3-opus-20240229                          0.046691  \n", "               claude-3-sonnet-20240229                        0.021244  \n", "               gemini-1.5-pro-preview-0409                     0.022607  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["full_data = pd.concat([anthropic_data, gemini_data])\n", "\n", "full_data[[\"max_prompt_len\", \"model_name\", \"first_token_time_s\", \"generation_time_s\", \"generation_time_per_token_s\"]].groupby([\"max_prompt_len\", \"model_name\"]).mean(numeric_only=True)\n", "# df = pd.concat([df, df2[df2[\"n_input_tokens_s\"] < 32000]])\n", "# data.to_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")\n", "\n", "\n", "# df2 = pd.read_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")\n", "# df3 = pd.read_json(\"/mnt/efs/augment/user/yury/tmp/anthropic.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
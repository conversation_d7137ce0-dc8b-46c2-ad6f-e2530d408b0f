{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Converts Rogue samples into indexed dataset for training.\"\"\"\n", "\n", "import json\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Dict, List\n", "from datetime import datetime\n", "\n", "from research.data.spark.pipelines.stages.common import (\n", "    export_indexed_dataset_impl,\n", ")\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.rag.rogue import generate_prompt_sl\n", "from research.data.rag.utils import pad_pack_tokens, repartition_and_shuffle\n", "from megatron.tokenizer.tokenizer import StarCoderToken<PERSON>, StarCoder2Tokenizer, DeepSeekCoderBaseTokenizer\n", "\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "spark = k8s_session()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_dict = {\n", "    \"input\": \"s3a://michiel-dev-bucket/ragdata/eth6_4m_morelang/\",\n", "    \"output\": \"/mnt/efs/augment/data/processed/rag/dataset/rogue2_dync_eth6_4m_morelang_4k7seq_fretprefsuf_minc4K_maxc16K_rdrop015/\",\n", "    \"random_seed\": 74912,\n", "    # \"seq_len\": 4737,\n", "    \"seq_len\": 16385, # 16K + 1\n", "    \"max_prefix_tokens\": 1030,\n", "    \"max_suffix_tokens\": 768,\n", "    \"max_retrieved_chunk_tokens\": -1,\n", "    # \"max_prompt_tokens\": 4478,\n", "    \"max_prompt_tokens\": (4478, 16117),\n", "    \"max_target_tokens\": 256,\n", "    \"max_filename_tokens\": 50,\n", "    \"max_scope_path_tokens\": 0,\n", "    \"component_order\": [\"retrieval\", \"prefix\", \"suffix\"],\n", "    \"context_quant_token_len\": 0,\n", "    \"nearby_prefix_token_len\": 0,\n", "    \"nearby_prefix_token_overlap\": 0,\n", "    \"nearby_suffix_token_len\": 0,\n", "    \"nearby_suffix_token_overlap\": 0,\n", "    \"samples_column\": \"prompt_tokens\",\n", "    \"num_validation_samples\": 50000,\n", "    \"retrieval_dropout\": 0.05,\n", "    \"tokenizer\": \"starcoder2\",\n", "    \"prepend_bos_token\": <PERSON>alse,\n", "    \"use_far_prefix_token\": False,\n", "    \"use_far_suffix_token\": False,\n", "}\n", "\n", "config = SimpleNamespace(**config_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make sure that /mnt/efs/spark-data is mounted on your VM!\n", "\n", "now = datetime.now()\n", "formatted_time = now.strftime('%Y-%m-%d_%H-%M-%S')\n", "step1_uri = f\"/mnt/efs/spark-data/temp_weekly/yury/{formatted_time}/step1/\"\n", "step2_uri = f\"/mnt/efs/spark-data/temp_weekly/yury/{formatted_time}/step2/\"\n", "input_columns=[\"prefix\", \"middle\", \"suffix\", \"suffix_offset\", \"middle_char_start\", \"middle_char_end\", \"file_path\", \"retrieved_chunks\"]\n", "\n", "if config.tokenizer == \"starcoder\":\n", "    tokenizer = StarCoderTokenizer()\n", "    assert False\n", "elif config.tokenizer == \"starcoder2\":\n", "    tokenizer = StarCoder2Tokenizer()\n", "elif config.tokenizer == \"deepseek\":\n", "    tokenizer = DeepSeekCoderBaseTokenizer()\n", "    assert False\n", "else:\n", "    raise ValueError(f\"Unknown tokenizer {config.tokenizer}\")\n", "\n", "result = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        generate_prompt_sl,\n", "        tokenizer=tokenizer,\n", "        config=config,\n", "    ),\n", "    input_path=config.input,\n", "    output_path=step1_uri,\n", "    input_columns=input_columns,\n", "    output_column=PROMPT_COLUMN,\n", "    ignore_error=True,\n", "    drop_original_columns=True,\n", "    timeout=2000, # default is 1000\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result\n", "print(result[\"status_count\"])\n", "print(result[\"task_info\"][\"stderr\"][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result2 = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer\n", "    ),\n", "    input_path=step1_uri,\n", "    output_path=step2_uri,\n", "    input_columns=[PROMPT_COLUMN],\n", "    output_column=config.samples_column,\n", "    drop_original_columns=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result2[\"task_info\"].stderr[0])\n", "result2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(step2_uri).select(config.samples_column)\n", "spark.sparkContext.setJobDescription(\"Shuffling dataset\")\n", "df = repartition_and_shuffle(config.random_seed, df)\n", "\n", "# # TODO(michiel) fix partition naming\n", "spark.sparkContext.setJobDescription(\"Creating indexed dataset\")\n", "export_indexed_dataset_impl(df, config=config, tokenizer=tokenizer)\n", "with open(config.output + 'config.json', 'w') as f:\n", "    json.dump(config_dict, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
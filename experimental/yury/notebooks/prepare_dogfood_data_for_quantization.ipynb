{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded 5679 samples.\n"]}], "source": ["import json\n", "from google.cloud import bigquery\n", "from pathlib import Path\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/smart_paste/data/dogfood_edit_requests/dogfood_chat_requests_20241007.jsonl\")\n", "DAYS_BACK = 90\n", "\n", "\n", "QUERY = f\"\"\"\\\n", "WITH\n", "  chat_host_request AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "  WHERE\n", "    time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {DAYS_BACK} DAY) ),\n", "  request_metadata AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "  WHERE\n", "    event_type = 'request_metadata'\n", "    AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {DAYS_BACK} DAY)\n", "    AND JSON_VALUE(raw_json, \"$.user_id\") NOT IN (\"health-check-1\", \"review-edit-bot\", \"eval-determined-bot\")\n", "    AND JSON_VALUE(raw_json, \"$.user_agent\") NOT IN (\"api_proxy_client/0 (Python)\")),\n", "  chat_host_response AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`\n", "  WHERE\n", "    time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {DAYS_BACK} DAY) )\n", "SELECT\n", "  *\n", "FROM (\n", "  SELECT\n", "    chat_host_request.request_id AS request_id,\n", "    JSON_VALUE(request_metadata.raw_json, \"$.user_id\") AS user_id,\n", "    request_metadata.raw_json AS metadata_json,\n", "    JSON_VALUE(chat_host_request.raw_json, \"$.request.selected_code\") AS selected_code,\n", "    JSON_VALUE(chat_host_response.raw_json, \"$.response.text\") AS response,\n", "    chat_host_request.raw_json AS request_json\n", "  FROM\n", "    request_metadata\n", "  JOIN\n", "    chat_host_request\n", "  ON\n", "    chat_host_request.request_id = request_metadata.request_id\n", "  JOIN\n", "    chat_host_response\n", "  ON\n", "    chat_host_response.request_id = request_metadata.request_id\n", "  )\n", "WHERE selected_code IS NOT NULL AND response LIKE \"%```%\"\n", "\"\"\"\n", "\n", "# client = bigquery.Client(project=PROJECT_ID)\n", "# n_requests = 0\n", "\n", "# with OUTPUT_PATH.open(\"w\") as f:\n", "#     for row in client.query_and_wait(QUERY):\n", "#         f.write(json.dumps(dict(row)))\n", "#         f.write(\"\\n\")\n", "#         n_requests += 1\n", "\n", "\n", "# print(f\"Downloaded {n_requests} samples.\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 5679 samples.\n"]}], "source": ["data = []\n", "\n", "with OUTPUT_PATH.open(\"r\") as f:\n", "    for line in f:\n", "        data.append(json.loads(line))\n", "\n", "print(f\"Loaded {len(data)} samples.\")"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["import re\n", "import numpy as np\n", "\n", "def extract_markdown_block_at_random(text: str, index: int | None) -> tuple[int, str]:\n", "    \"\"\"Extract the markdown code block from the given text.\"\"\"\n", "    # Note(<PERSON><PERSON><PERSON>): `\\r\\n` is the line break for Windows and `\\n` is the line break for Linux.\n", "    pattern = r\"```(\\w+)?\\s*(?:path=([^\\s]+))?\\s*(?:mode=(\\w+))?\\n(.*?)```\"\n", "    matches = list(re.findall(pattern, text, re.DOTALL))\n", "    if len(matches) == 0:\n", "        return None, None\n", "    if index is None:\n", "        index = np.random.randint(len(matches))\n", "    return index, matches[index][3]\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total: 5679, unique messages: 4851, no codeblocks: 9, smart paste: 101 ready: 4741\n"]}], "source": ["import numpy as np\n", "from collections import defaultdict\n", "from experimental.yury.smart_paste_full_file_eval.utils import build_chat_prompt_input\n", "\n", "\n", "SMART_PASTE_MARKERS = [\n", "    \"Here's the current file:\",\n", "    \"Your task is to rewrite the entire file content, incorporating all specified changes.\"\n", "]\n", "\n", "\n", "requests_per_user = defaultdict(list)\n", "used_messages = set()\n", "n_ready, n_unique_messages, n_no_codeblocks, n_total, n_smart_paste = 0, 0, 0, 0, 0\n", "for index, sample in enumerate(data):\n", "    n_total += 1\n", "    original_chat_prompt_input = build_chat_prompt_input(sample[\"request_json\"], add_retrievals=False)\n", "    if original_chat_prompt_input.message in used_messages:\n", "        continue    \n", "    used_messages.add(original_chat_prompt_input.message)\n", "    n_unique_messages += 1\n", "\n", "    is_smart_paste = False\n", "    for marker in SMART_PASTE_MARKERS:\n", "        if marker in original_chat_prompt_input.message:\n", "            is_smart_paste = True\n", "    if is_smart_paste:\n", "        n_smart_paste += 1\n", "        continue\n", "\n", "    _, suggested_edit = extract_markdown_block_at_random(sample[\"response\"])\n", "    if suggested_edit is None:\n", "        n_no_codeblocks += 1\n", "        continue\n", "    n_ready += 1\n", "    requests_per_user[sample[\"user_id\"]].append(sample)\n", "\n", "print(f\"Total: {n_total}, unique messages: {n_unique_messages}, no codeblocks: {n_no_codeblocks}, smart paste: {n_smart_paste} ready: {n_ready}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>70.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>67.728571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>81.974001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>11.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>27.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>95.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>336.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                0\n", "count   70.000000\n", "mean    67.728571\n", "std     81.974001\n", "min      1.000000\n", "25%     11.000000\n", "50%     27.000000\n", "75%     95.750000\n", "max    336.000000"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame([len(requests_per_user[user_id]) for user_id in requests_per_user])\n", "df.describe()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Subsampled 1310 samples.\n"]}], "source": ["N_MAX_REQUESTS_PER_USER = 25\n", "\n", "subsampled_data = []\n", "\n", "for user_id, samples in requests_per_user.items():\n", "    if len(samples) > N_MAX_REQUESTS_PER_USER:\n", "        samples = np.random.choice(samples, N_MAX_REQUESTS_PER_USER, replace=False)\n", "    subsampled_data.extend(samples)\n", "\n", "print(f\"Subsampled {len(subsampled_data)} samples.\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\\\n", "Here's the current file:\n", "\n", "```\n", "{target_file_contents}\n", "```\n", "\n", "And the changes I want you to apply to the current file:\n", "\n", "```\n", "{suggested_edit}\n", "```\n", "\n", "Strictly follow these three steps to incorporate the suggested changes. NO deviations:\n", "\n", "Step 1: Briefly describe the suggested changes.\n", "\n", "Step 2: Plan\n", "Write a plan outlining exactly which suggested changes will be applied and where. Keep the plan strictly focused on the areas related to the suggestions. If the changes include imports, add plan to add these imports at the beginning of the file. USE PLAIN TEXT AND NEVER USE CODEBLOCKS IN YOUR PLAN.\n", "\n", "Step 3: Incorporate Suggested Changes\n", "Apply the changes seamlessly and write out the FULL, modified file with no omissions or placeholders. Strictly follow the plan when applying the suggested changes. Do not make any additional changes, but ensure that all other code outside of the suggested changes is preserved exactly as it is.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1310 annotated samples.\n", "Subsampled 0 samples to annotate.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["0it [00:02, ?it/s]\n"]}], "source": ["import tqdm\n", "import uuid\n", "import multiprocessing\n", "from base.prompt_format.common import Exchange\n", "from experimental.yury.smart_paste_full_file_eval.utils import build_chat_prompt_input\n", "import dataclasses\n", "import os\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.augment_client.client import AugmentClient, AugmentModelClient, ClientException\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "# URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "# RI_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "# MODEL = \"binks-v12-fp16-longoutput\"\n", "\n", "URL = \"https://dev-yury.us-central.api.augmentcode.com\"\n", "MODEL = \"binks-v12-fp16-longoutput\"\n", "RI_URL = \"https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "\n", "ANNOTATED_OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/smart_paste/data/dogfood_edit_requests/dogfood_chat_requests_20241007_subsampled_annotated.jsonl\")\n", "N_PROCESSES = 5\n", "TIMEOUT = 1200\n", "\n", "\n", "def run_smart_paste(sample, codeblock_index = None):\n", "    pid = os.getpid()\n", "    session_id = uuid.uuid5(uuid.NAMESPACE_OID, str(pid))\n", "\n", "    dogfood_client = AugmentClient(\n", "        url=URL,\n", "        token=API_TOKEN,\n", "        timeout=TIMEOUT,\n", "        retry_count=0,\n", "        session_id=session_id,\n", "    )\n", "    \n", "    model_client_dogfood = AugmentModelClient(dogfood_client, MODEL)    \n", "\n", "    original_chat_prompt_input = build_chat_prompt_input(sample[\"request_json\"], add_retrievals=False)\n", "    \n", "    codeblock_index, suggested_edit = extract_markdown_block_at_random(sample[\"response\"], codeblock_index)    \n", "\n", "    # Adjust the chat history to include the latest message.\n", "    chat_history = list(original_chat_prompt_input.chat_history)\n", "    chat_history.append(\n", "        Exchange(\n", "            request_message=original_chat_prompt_input.message,\n", "            response_text=sample[\"response\"],\n", "            request_id=sample[\"request_id\"],\n", "        )\n", "    )\n", "\n", "    if original_chat_prompt_input.context_code_exchange_request_id in [None, \"\", \"new\"]:\n", "        context_code_exchange_request_id = sample[\"request_id\"]\n", "    else:\n", "        context_code_exchange_request_id = original_chat_prompt_input.context_code_exchange_request_id\n", "\n", "    target_file_contents = (\n", "        original_chat_prompt_input.prefix\n", "        + original_chat_prompt_input.selected_code\n", "        + original_chat_prompt_input.suffix\n", "    )\n", "\n", "    message = PROMPT.format(\n", "        target_file_contents=target_file_contents,\n", "        suggested_edit=suggested_edit,\n", "    )\n", "\n", "    chat_prompt_input = dataclasses.replace(\n", "        original_chat_prompt_input,\n", "        chat_history=chat_history,\n", "        context_code_exchange_request_id=context_code_exchange_request_id,\n", "        message=message,\n", "        retrieved_chunks=[],\n", "        recent_changes=[],\n", "        user_guided_blobs=[],\n", "    )\n", "\n", "    try:\n", "        chat_response_dogfood = model_client_dogfood.chat(\n", "            message=message,\n", "            selected_code=chat_prompt_input.selected_code,\n", "            prefix=chat_prompt_input.prefix,\n", "            suffix=chat_prompt_input.suffix,\n", "            path=chat_prompt_input.path,\n", "            chat_history=chat_history,\n", "            context_code_exchange_request_id=chat_prompt_input.context_code_exchange_request_id,\n", "            prefix_begin=chat_prompt_input.prefix_begin,\n", "            suffix_end=chat_prompt_input.suffix_end,\n", "        )\n", "        return {\n", "            \"input_request_id\": sample[\"request_id\"],\n", "            \"output_request_id\": chat_response_dogfood.request_id,\n", "            \"codeblock_index\": codeblock_index,\n", "            \"response\": chat_response_dogfood.text,\n", "        }\n", "    except ClientException as e:\n", "        if e.response.status_code == 413:\n", "            return {\n", "                \"input_request_id\": sample[\"request_id\"],                \n", "                \"codeblock_index\": codeblock_index,\n", "            }\n", "        raise\n", "\n", "\n", "annotated_data = {}\n", "\n", "if ANNOTATED_OUTPUT_PATH.exists():\n", "    with ANNOTATED_OUTPUT_PATH.open(\"r\") as f:\n", "        for line in f:\n", "            sample = json.loads(line[:-1])\n", "            annotated_data[sample[\"input_request_id\"]] = sample\n", "\n", "print(f\"Loaded {len(annotated_data)} annotated samples.\")\n", "\n", "subsampled_data_to_annotate = [\n", "    sample\n", "    for sample in subsampled_data\n", "    if sample[\"request_id\"] not in annotated_data\n", "]\n", "\n", "print(f\"Subsampled {len(subsampled_data_to_annotate)} samples to annotate.\")\n", "\n", "pbar = tqdm.tqdm(total=len(subsampled_data_to_annotate))\n", "\n", "with ANNOTATED_OUTPUT_PATH.open(\"a\") as f:\n", "    with multiprocessing.Pool(N_PROCESSES) as pool:\n", "        for data in pool.imap_unordered(run_smart_paste, subsampled_data_to_annotate):\n", "            pbar.update()\n", "            f.write(json.dumps(data) + \"\\n\")\n", "            f.flush()"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wrote 1265 samples, 1310 total\n"]}], "source": ["n_success = 0\n", "for request_id, sample in annotated_data.items():\n", "    if \"response\" in sample:\n", "        n_success += 1\n", "\n", "print(f\"Wrote {n_success} samples, {len(annotated_data)} total\")"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.tokenized_llama_binks_prompt_formatter import (\n", "    TokenizedLlama3BinksPromptFormatter,\n", ")\n", "from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "tokenizer = Llama3InstructTokenizer()\n", "token_apportionment = ChatTokenApportionment(\n", "    prefix_len=1024,\n", "    suffix_len=1024,\n", "    path_len=256,\n", "    message_len=-1,\n", "    selected_code_len=-1,\n", "    chat_history_len=4096,\n", "    retrieval_len_per_each_user_guided_file=3072,\n", "    retrieval_len_for_user_guided=8192,\n", "    retrieval_len=-1,\n", "    max_prompt_len=32768 - 16384,\n", ")\n", "\n", "prompt_formatter = TokenizedLlama3BinksPromptFormatter(tokenizer, token_apportionment)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": []}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1310/1310 [00:16<00:00, 79.94it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved 1265 samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["def format_sample(sample):\n", "    annotated_sample = annotated_data[sample[\"request_id\"]]\n", "    codeblock_index = annotated_sample[\"codeblock_index\"]\n", "\n", "    original_chat_prompt_input = build_chat_prompt_input(sample[\"request_json\"], add_retrievals=False)\n", "\n", "    tmp_codeblock_index, suggested_edit = extract_markdown_block_at_random(sample[\"response\"], codeblock_index)    \n", "    assert tmp_codeblock_index == codeblock_index\n", "\n", "    # Adjust the chat history to include the latest message.\n", "    chat_history = list(original_chat_prompt_input.chat_history)\n", "    chat_history.append(\n", "        Exchange(\n", "            request_message=original_chat_prompt_input.message,\n", "            response_text=sample[\"response\"],\n", "            request_id=sample[\"request_id\"],\n", "        )\n", "    )\n", "\n", "    if original_chat_prompt_input.context_code_exchange_request_id in [None, \"\", \"new\"]:\n", "        context_code_exchange_request_id = sample[\"request_id\"]\n", "    else:\n", "        context_code_exchange_request_id = original_chat_prompt_input.context_code_exchange_request_id\n", "\n", "    target_file_contents = (\n", "        original_chat_prompt_input.prefix\n", "        + original_chat_prompt_input.selected_code\n", "        + original_chat_prompt_input.suffix\n", "    )\n", "\n", "    message = PROMPT.format(\n", "        target_file_contents=target_file_contents,\n", "        suggested_edit=suggested_edit,\n", "    )\n", "\n", "    chat_prompt_input = dataclasses.replace(\n", "        original_chat_prompt_input,\n", "        chat_history=chat_history,\n", "        context_code_exchange_request_id=context_code_exchange_request_id,\n", "        message=message,\n", "        retrieved_chunks=[],\n", "        recent_changes=[],\n", "        user_guided_blobs=[],\n", "    )\n", "\n", "    if \"response\" not in annotated_sample:\n", "        return None    \n", "\n", "    prompt_tokens = prompt_formatter.format_prompt(chat_prompt_input)\n", "    output_tokens = prompt_formatter.tokenizer.tokenize_safe(annotated_sample[\"response\"])\n", "    eod_token = [prompt_formatter.tokenizer.special_tokens.eos]\n", "\n", "    return prompt_tokens.tokens + output_tokens + eod_token\n", "\n", "\n", "CALIBRATION_OUTPUT_PATH = \"/mnt/efs/augment/user/yury/smart_paste/data/dogfood_edit_requests/dogfood_chat_requests_20241007_calibration\"\n", "\n", "import torch\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "builder = MMapIndexedDatasetBuilder(CALIBRATION_OUTPUT_PATH + \".bin\", dtype=np.int32)\n", "\n", "n_saved = 0\n", "lengths = []\n", "for sample in tqdm.tqdm(subsampled_data):\n", "    tokens = format_sample(sample)\n", "    if tokens is None:\n", "        continue\n", "    while len(tokens) % 8 != 0:\n", "        tokens.append(tokenizer.special_tokens.padding)\n", "    lengths.append(len(tokens))\n", "    builder.add_item(torch.tensor(tokens))\n", "    n_saved += 1\n", "    builder.end_document()\n", "\n", "builder.finalize(CALIBRATION_OUTPUT_PATH + \".idx\")\n", "\n", "print('Saved %d samples' % n_saved)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "1265\n"]}], "source": ["from megatron.data.indexed_dataset import make_dataset\n", "\n", "ds = make_dataset(\n", "    CALIBRATION_OUTPUT_PATH, impl=\"mmap\", skip_warmup=True\n", ")\n", "print(len(ds))\n"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["count     1265.000000\n", "mean      8360.999209\n", "std       5891.329025\n", "min        704.000000\n", "25%       4224.000000\n", "50%       6688.000000\n", "75%      11496.000000\n", "max      31352.000000\n", "dtype: float64"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "pd.Series(lengths).describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
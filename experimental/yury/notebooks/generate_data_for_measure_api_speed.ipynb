{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["OUTPUT_PATH = Path(\"/mnt/efs/augment/data/eval/chat/ovivo_ios_only_cs/repos/ovivo_ios_only_cs.json\")\n", "\n", "with OUTPUT_PATH.open(\"r\") as f:\n", "    ovivo_repo = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"add_path\": <PERSON>als<PERSON>,\n", "        \"max_tokens\": 1023,        \n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    }\n", "}\n", "\n", "retrieval_database = create_retriever(retriever_config)\n", "retrieval_database.scorer.load()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/augment/research/gpt-neox/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (1498 > 1024). Running this sequence through the model will result in indexing errors\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.retrieval.libraries.types import Document\n", "from research.core.types import Chunk\n", "\n", "retrieval_database.remove_all_docs()\n", "\n", "for file in ovivo_repo['docs']:\n", "    document = Document(\n", "        id=file['id'], text=file['text'], path=file['path']\n", "    )\n", "    retrieval_database.add_doc(document)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import dataclasses \n", "\n", "EXAMPLES_PATH = Path(\"/mnt/efs/augment/data/eval/chat/ovivo_ios_only_cs/examples.json\")\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/tmp/ovivo_ios_only_cs.ethanol6_query_simple_chat.json\")\n", "\n", "TOP_K = 200\n", "\n", "with EXAMPLES_PATH.open(\"r\") as f:\n", "    examples = json.load(f)\n", "\n", "for example in examples:\n", "    retrieved_chunks, _ = retrieval_database.query(\n", "        model_input=ModelInput(\n", "            prefix=\"\",\n", "            suffix=\"\",\n", "            path=\"\",\n", "            extra={\n", "                \"message\": example[\"question\"],\n", "            }\n", "        ),\n", "        top_k=TOP_K,\n", "    )        \n", "    retrieved_chunks = [dataclasses.asdict(r) for r in retrieved_chunks]\n", "    example[\"retrieved_chunks\"] = retrieved_chunks\n", "\n", "\n", "with OUTPUT_PATH.open(\"w\") as f:\n", "    json.dump(examples, f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
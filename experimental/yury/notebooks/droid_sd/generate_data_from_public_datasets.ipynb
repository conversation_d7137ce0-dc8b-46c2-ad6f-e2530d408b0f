{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "from megatron.data import indexed_dataset\n", "from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "# See https://huggingface.co/collections/theblackcat102/code-conversations-65912daca260709928f9fe1b\n", "# See https://github.com/NAIST-SE/DevGPT\n", "\n", "CODEALPACA_PATH = Path(\"/mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.shuffled.jsonl\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 111272 samples from /mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.shuffled.jsonl\n"]}], "source": ["with open(CODEALPACA_PATH) as f:\n", "    data = [json.loads(line[:-1]) for line in f]\n", "\n", "print(\"Loaded %d samples from %s\" % (len(data), CODEALPACA_PATH))\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "\n", "def pad_prompt_in_place(tokens, max_seq_length):\n", "    assert len(tokens) <= max_seq_length\n", "    tokens.extend([-tokenizer.eos_id] * (max_seq_length - len(tokens)))\n", "    assert len(tokens) == max_seq_length\n", "\n", "\n", "def detokenize(tokens):\n", "    return tokenizer.detokenize([abs(x) for x in tokens])\n", "\n", "\n", "TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>\n", "Instruction:\n", "{instruction}\n", "\n", "Response:\n", "\"\"\"\n", "\n", "\n", "def generate_tokens(sample, max_seq_length):\n", "    prompt = tokenizer.tokenize(TEMPLATE.format(instruction=sample['instruction']))\n", "    label = tokenizer.tokenize(sample['output'] + \"\\n\") + [tokenizer.eos_id]\n", "    tokens = [-1 * t for t in prompt] + label\n", "    assert len(tokens) < max_seq_length, len(tokens)\n", "    stats = {\n", "        'prompt_length': len(prompt),\n", "        'label_length': len(label),\n", "        'total_length': len(tokens),                \n", "    }\n", "    pad_prompt_in_place(tokens, max_seq_length)\n", "    return tokens, stats\n", "\n", "\n", "sample_tokens, _ = generate_tokens(data[0], 1024)\n", "# print(detokenize(sample_tokens))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 111272/111272 [03:55<00:00, 473.07it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt_length</th>\n", "      <th>label_length</th>\n", "      <th>total_length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>111272.000000</td>\n", "      <td>111272.000000</td>\n", "      <td>111272.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>218.315803</td>\n", "      <td>442.140026</td>\n", "      <td>660.455829</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>435.323894</td>\n", "      <td>248.367764</td>\n", "      <td>548.696998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>12.000000</td>\n", "      <td>19.000000</td>\n", "      <td>44.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>61.000000</td>\n", "      <td>286.000000</td>\n", "      <td>393.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>107.000000</td>\n", "      <td>410.000000</td>\n", "      <td>554.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>211.000000</td>\n", "      <td>556.000000</td>\n", "      <td>763.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8639.000000</td>\n", "      <td>3801.000000</td>\n", "      <td>9123.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       prompt_length   label_length   total_length\n", "count  111272.000000  111272.000000  111272.000000\n", "mean      218.315803     442.140026     660.455829\n", "std       435.323894     248.367764     548.696998\n", "min        12.000000      19.000000      44.000000\n", "25%        61.000000     286.000000     393.000000\n", "50%       107.000000     410.000000     554.000000\n", "75%       211.000000     556.000000     763.000000\n", "max      8639.000000    3801.000000    9123.000000"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["import tqdm\n", "import pandas as pd\n", "\n", "max_seq_length = 16384 + 1\n", "tokens, stats = [], []\n", "\n", "for sample in tqdm.tqdm(data):\n", "    t, s = generate_tokens(sample, max_seq_length)\n", "    tokens.append(t)\n", "    stats.append(s)\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "random.seed(31415)\n", "random.shuffle(tokens)\n", "\n", "validation_samples = tokens[:500]\n", "train_samples = tokens[500:]\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "import torch\n", "\n", "\n", "OUTPUT_PATH = \"/mnt/efs/augment/user/yury/droid_sd/evol_codealpaca_v1\"\n", "\n", "def save_dataset(samples, path):\n", "    builder = MMapIndexedDatasetBuilder(path + \".bin\", dtype=np.int32)\n", "    for sample in samples:\n", "        builder.add_item(torch.Tensor(sample))\n", "        builder.end_document()\n", "    builder.finalize(path + \".idx\")\n", "\n", "save_dataset(train_samples, os.path.join(OUTPUT_PATH, \"train\"))\n", "save_dataset(validation_samples, os.path.join(OUTPUT_PATH, \"validation\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
from experimental.yury.smart_paste_eval.utils import (
    calculate_per_category_accuracy,
    evaluate_model_on_dataset,
)
from unittest.mock import Mock, patch
from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample
from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from experimental.yury.smart_paste_eval.utils import find_matching_edit_requests
import pytest


def test_calculate_per_category_accuracy():
    class MockDataset:
        DATA = [
            EvalSample(uuid="id1", category="category_a"),
            EvalSample(uuid="id2", category="category_a"),
            EvalSample(uuid="id3", category="category_b"),
            EvalSample(uuid="id4", category="category_b"),
        ]

    class MockOutput:
        def __init__(self, correct: bool):
            self.correct = correct

    dataset = MockDataset()
    results = {
        "id1": MockOutput(correct=True),
        "id2": MockOutput(correct=False),
        "id3": MockOutput(correct=True),
        # id4 is skipped
    }

    stats = calculate_per_category_accuracy(dataset, results)

    assert stats["category_a"]["correct"] == 1
    assert stats["category_a"]["total"] == 2
    assert stats["category_a"]["skipped"] == 0
    assert stats["category_b"]["correct"] == 1
    assert stats["category_b"]["total"] == 1
    assert stats["category_b"]["skipped"] == 1


def test_evaluate_model_on_dataset():
    mock_dataset = Mock()
    mock_dataset.DATA = [
        EvalSample(
            uuid="test1",
            category="category1",
            prompt_input=SmartPastePromptInput(
                selected_code="code1",
                prefix="prefix1",
                suffix="suffix1",
                path="test.py",
                chat_history=[],
                code_block="block1",
                target_path="target.py",
                target_file_content="original",
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            expected_outputs=["expected1"],
        ),
        EvalSample(
            uuid="test2",
            category="category2",
            prompt_input=SmartPastePromptInput(
                selected_code="code2",
                prefix="prefix2",
                suffix="suffix2",
                path="test2.py",
                chat_history=[],
                code_block="block2",
                target_path="target2.py",
                target_file_content="original2",
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            expected_outputs=["expected2"],
        ),
    ]

    mock_response1 = Mock()
    mock_response1.iter_lines.return_value = [
        '{"text": "modified1", "unknown_blob_names": [], "checkpoint_not_found": false, "replacement_start_line": 1, "replacement_end_line": 2, "replacement_text": "modified1"}'
    ]

    mock_response2 = Mock()
    mock_response2.iter_lines.return_value = [
        '{"text": "modified2", "unknown_blob_names": [], "checkpoint_not_found": false, "replacement_start_line": 1, "replacement_end_line": 2, "replacement_text": "modified2"}'
    ]

    with patch("base.augment_client.client.AugmentClient._post_stream") as mock_post:
        mock_post.side_effect = [
            (mock_response1, "test1"),
            (mock_response2, "test2"),
        ]

        results = evaluate_model_on_dataset(
            model_name="test_model",
            url="http://test",
            dataset=mock_dataset,
            api_token="test_token",
        )

        assert results.total_processed == 2
        assert len(results.sample_results) == 2
        assert "category1" in results.category_stats
        assert "category2" in results.category_stats
        assert results.total_skipped == 0


def test_evaluate_model_on_empty_dataset():
    mock_dataset = Mock()
    mock_dataset.DATA = []

    results = evaluate_model_on_dataset(
        model_name="test_model",
        url="http://test",
        dataset=mock_dataset,
        api_token="test_token",
    )

    assert results.total_processed == 0
    assert len(results.sample_results) == 0
    assert len(results.category_stats) == 0
    assert results.total_skipped == 0


def test_evaluate_model_with_context_length_error():
    mock_dataset = Mock()
    mock_dataset.DATA = [
        EvalSample(
            uuid="test1",
            category="category1",
            prompt_input=SmartPastePromptInput(
                selected_code="code1",
                prefix="prefix1",
                suffix="suffix1",
                path="test.py",
                chat_history=[],
                code_block="block1",
                target_path="target.py",
                target_file_content="original",
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            expected_outputs=["expected1"],
        )
    ]

    with patch("base.augment_client.client.AugmentClient._post_stream") as mock_post:
        mock_post.side_effect = ExceedContextLength("Context too long")

        results = evaluate_model_on_dataset(
            model_name="test_model",
            url="http://test",
            dataset=mock_dataset,
            api_token="test_token",
        )

        assert results.total_processed == 0
        assert len(results.sample_results) == 0
        assert results.total_skipped == 1


def test_evaluate_model_with_mixed_results():
    mock_dataset = Mock()
    mock_dataset.DATA = [
        EvalSample(
            uuid="success",
            category="category1",
            prompt_input=SmartPastePromptInput(
                selected_code="code1",
                prefix="prefix1",
                suffix="suffix1",
                path="test.py",
                chat_history=[],
                code_block="block1",
                target_path="target.py",
                target_file_content="original",
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            expected_outputs=["expected1"],
        ),
        EvalSample(
            uuid="error",
            category="category2",
            prompt_input=SmartPastePromptInput(
                selected_code="code2",
                prefix="prefix2",
                suffix="suffix2",
                path="test2.py",
                chat_history=[],
                code_block="block2",
                target_path="target2.py",
                target_file_content="original2",
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            expected_outputs=["expected2"],
        ),
    ]

    mock_response = Mock()
    mock_response.iter_lines.return_value = [
        '{"text": "modified1", "unknown_blob_names": [], "checkpoint_not_found": false, "replacement_start_line": 1, "replacement_end_line": 2, "replacement_text": "modified1"}'
    ]

    with patch("base.augment_client.client.AugmentClient._post_stream") as mock_post:
        mock_post.side_effect = [
            (mock_response, "success"),  # For the first sample (success)
            ExceedContextLength("Context too long"),  # For the second sample (error)
        ]

        results = evaluate_model_on_dataset(
            model_name="test_model",
            url="http://test",
            dataset=mock_dataset,
            api_token="test_token",
        )

        assert results.total_processed == 1
        assert len(results.sample_results) == 1
        assert results.total_skipped == 1
        assert "success" in results.sample_results
        assert "error" not in results.sample_results


@pytest.mark.skip(reason="e2e test that runs BigQuery query")
def test_find_matching_edit_requests_end2end():
    """End-to-end test using real request IDs from the dogfood-shard tenant."""
    chat_request_id = "30a1a217-6bef-48b9-a9de-86327bde4966"
    expected_edit_request_ids = set(
        [
            "e380e9d6-bd79-407a-aa60-0f0d00f97a63",
            "2ff1d94a-a827-4278-92b2-9a11bb29bf9a",
        ]
    )
    tenant_name = "dogfood-shard"

    actual_edit_request_ids = find_matching_edit_requests(chat_request_id, tenant_name)
    assert actual_edit_request_ids == expected_edit_request_ids

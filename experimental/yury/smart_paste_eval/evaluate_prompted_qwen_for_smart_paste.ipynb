{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import research.eval.harness.factories as factories\n", "\n", "config = {\n", "    \"name\": \"elden_qwen_fb\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct-fb-mp2\",\n", "    \"seq_length\": 32768,\n", "    \"model_parallel_size\": 2,\n", "}\n", "\n", "model = factories.create_model(config)\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "from research.models.meta_model import GenerationOptions\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "HF_CHECKPOINT_PATH = (\n", "    \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct\"\n", ")\n", "\n", "hf_tokenizer = AutoTokenizer.from_pretrained(HF_CHECKPOINT_PATH, trust_remote_code=True)\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "\n", "generation_options = GenerationOptions(\n", "    temperature=0,\n", "    max_generated_tokens=16384,\n", "    stop_tokens=[hf_tokenizer.eos_token_id],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "\n", "def run(system_prompt, chat_history: list[dict[str, str]], message: str):\n", "    chat = (\n", "        [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "        ]\n", "        + chat_history\n", "        + [\n", "            {\"role\": \"user\", \"content\": message},\n", "        ]\n", "    )\n", "\n", "    prompt_text = (\n", "        hf_tokenizer.apply_chat_template(chat, tokenize=False)\n", "        + \"<|im_start|>assistant\\n\"\n", "    )\n", "    prompt_tokens = tokenizer.tokenize_unsafe(prompt_text)\n", "\n", "    current_generation_options = dataclasses.replace(\n", "        generation_options,\n", "        max_generated_tokens=min(\n", "            int(len(prompt_tokens) * 1.2 + 500),\n", "            generation_options.max_generated_tokens,\n", "        ),\n", "    )\n", "\n", "    generation = model.raw_generate(\n", "        prompt_tokens=prompt_tokens,\n", "        options=current_generation_options,\n", "    )\n", "\n", "    if generation.endswith(\"<|im_end|>\"):\n", "        generation = generation[: -len(\"<|im_end|>\")]\n", "    return generation\n", "\n", "\n", "run(\n", "    \"You are SPARTA, created by Augment Code. You are a helpful assistant.\",\n", "    [],\n", "    \"Who are you?\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_utils import compute_file_diff, File\n", "from experimental.yury.smart_paste_full_file_eval.utils import (\n", "    SmartPasteSample,\n", "    extract_markdown_block_by_index,\n", ")\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class SmartPasteEvalOutput:\n", "    response: str\n", "    modified_target_file: str | None = None\n", "    diff_against_original: str | None = None\n", "    diff_against_expected: str | None = None\n", "    correct: bool | None = None\n", "\n", "\n", "def run_smart_paste(\n", "    input_sample: SmartPasteSample,\n", "    system_prompt: str,\n", "    message_prompt: str,\n", "    expected_output: str | None,\n", ") -> SmartPasteEvalOutput:\n", "    message = message_prompt.format(\n", "        target_file_path=input_sample.target_file.path,\n", "        target_file_contents=input_sample.target_file.contents,\n", "        suggested_edit=input_sample.suggested_edit,\n", "    )\n", "\n", "    chat_history = []\n", "    for exchange in input_sample.chat_prompt_input.chat_history:\n", "        chat_history.extend(\n", "            [\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": exchange.request_message,\n", "                },\n", "                {\n", "                    \"role\": \"assistant\",\n", "                    \"content\": exchange.response_text,\n", "                },\n", "            ]\n", "        )\n", "\n", "    response = run(system_prompt, chat_history, message)\n", "\n", "    result = {\n", "        \"response\": response,\n", "    }\n", "\n", "    try:\n", "        modified_file = extract_markdown_block_by_index(response, -1)\n", "    except ValueError:\n", "        modified_file = response\n", "    after_file = File(path=input_sample.target_file.path, contents=modified_file)\n", "\n", "    result.update(\n", "        {\n", "            \"modified_target_file\": modified_file,\n", "            \"diff_against_original\": compute_file_diff(\n", "                input_sample.target_file,\n", "                after_file,\n", "                use_smart_header=True,\n", "                num_context_lines=5,\n", "            ),\n", "        }\n", "    )\n", "\n", "    if expected_output is not None:\n", "        correct = modified_file == expected_output\n", "        expected_file = File(\n", "            path=input_sample.target_file.path, contents=expected_output\n", "        )\n", "        diff_against_expected = compute_file_diff(\n", "            expected_file, after_file, use_smart_header=True, num_context_lines=5\n", "        )\n", "        result.update(\n", "            {\n", "                \"diff_against_expected\": diff_against_expected,\n", "                \"correct\": correct,\n", "            }\n", "        )\n", "    return SmartPasteEvalOutput(**result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from experimental.yury.smart_paste_full_file_eval.utils import (\n", "    read_expected_output,\n", "    download_and_build_smart_paste_sample,\n", ")\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/home/<USER>/augment/experimental/yury/smart_paste_full_file_eval/data/expected_outputs\"\n", ")\n", "\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    # \"0696c744-9fb8-4b50-b655-fad32ddb38d5\", # not enough output tokens\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    # \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\", # not enough output tokens\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    # \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\", # same as 2ceea890-7bf8-4b46-9875-a87254b12351\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",  # WANRNING: Tricky sample, model sometimes deletes an entire prompt formatter\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    # \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\", # too confusing sample\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "MULTIFILE_SMARTPASTE_SAMPLES = [\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"research/fastbackward/model.py\",\n", "        0,\n", "    ),\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"research/fastbackward/train_rlhf.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"af449c14-1458-481d-a8a4-7d2ba396489b\",\n", "        \"base/prompt_format_chat/__init__.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"base/prompt_format_chat/conftest.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"base/prompt_format_chat/lib/chat_history_builder_test.py\",\n", "        2,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"clients/vscode/src/__tests__/chat/chat-model.test.ts\",\n", "        3,\n", "    ),\n", "    # Somehow fails for me?\n", "    # (\n", "    #     \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "    #     \"clients/vscode/src/augment-api.ts\",\n", "    #     4,\n", "    # ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"clients/intellij/src/test/kotlin/com/augmentcode/intellij/chat/ChatRequestTest.kt\",\n", "        5,\n", "    ),\n", "]\n", "\n", "DATA = (\n", "    [(rid, \"dogfood-shard\", None, 0) for rid in STAGING_SHARD0_SAMPLES]\n", "    + [(rid, \"dogfood\", None, 0) for rid in OLD_DOGFOOD_SAMPLES]\n", "    + [\n", "        (rid, \"dogfood-shard\", target_path, codeblock_index)\n", "        for (rid, target_path, codeblock_index) in MULTIFILE_SMARTPASTE_SAMPLES\n", "    ]\n", ")\n", "\n", "print(f\"Total samples: {len(DATA)}\")\n", "\n", "for request_id, tenant_name, target_path, codeblock_index in DATA:\n", "    print(f\"Sample: {request_id} {target_path} {codeblock_index}\")\n", "    input_sample = download_and_build_smart_paste_sample(\n", "        request_id,\n", "        tenant_name=tenant_name,\n", "        codeblock_index=codeblock_index,\n", "        target_path=target_path,\n", "    )\n", "\n", "    expected_output = read_expected_output(OUTPUT_DIR, request_id, codeblock_index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from experimental.yury.smart_paste_full_file_eval.utils import (\n", "    read_expected_output,\n", "    download_and_build_smart_paste_sample,\n", ")\n", "\n", "\n", "SYSTEM_PROMPT = \"You are Augment, an AI code assistant developed by Augment Code. You are a helpful assistant.\"\n", "\n", "\n", "PROMPT = \"\"\"\\\n", "Here's the current file:\n", "\n", "```\n", "{target_file_contents}\n", "```\n", "\n", "And the changes I want you to apply to the current file:\n", "\n", "```\n", "{suggested_edit}\n", "```\n", "\n", "Strictly follow these three steps to incorporate the suggested changes. NO deviations:\n", "\n", "Step 1: Briefly describe the suggested changes.\n", "\n", "Step 2: Plan\n", "Write a plan outlining exactly which suggested changes will be applied and where. Keep the plan strictly focused on the areas related to the suggestions. If the changes include imports, add plan to add these imports at the beginning of the file. USE PLAIN TEXT AND NEVER USE CODEBLOCKS IN YOUR PLAN.\n", "\n", "Step 3: Incorporate Suggested Changes\n", "Apply the changes seamlessly and write out the full, modified file with no omissions or placeholders. Strictly follow the plan when applying the suggested changes. Do not make any additional changes, but ensure that all other code outside of the suggested changes is preserved exactly as it is.\n", "\"\"\"\n", "\n", "# Common mistakes\n", "# 7da86c2c-487e-4040-9b35-0d1e6df737b1 -- adds doc strings to some of the ModelArgs fields\n", "# 0ae73c71-d915-433d-9426-e4533ec62df5 -- adds extra imports\n", "# 7fd0623b-c217-4658-89f9-af27246f7bfd -- deletes one import (?)\n", "# 17276560-0a77-4acd-917f-740f4a4e1f30 -- deletes the modfy of the prompt formatter (!)\n", "# Accuracy: 22 out of 26 samples\n", "\n", "with open(\"/mnt/efs/augment/user/yury/tmp/qwen_responses_v1.jsonl\", \"w\") as f:\n", "    n_correct = 0\n", "    for request_id, tenant_name, target_path, codeblock_index in DATA:\n", "        print(f\"Sample: {request_id} {target_path} {codeblock_index}\")\n", "        input_sample = download_and_build_smart_paste_sample(\n", "            request_id,\n", "            tenant_name=tenant_name,\n", "            codeblock_index=codeblock_index,\n", "            target_path=target_path,\n", "        )\n", "\n", "        expected_output = read_expected_output(OUTPUT_DIR, request_id, codeblock_index)\n", "        output = run_smart_paste(input_sample, SYSTEM_PROMPT, PROMPT, expected_output)\n", "\n", "        if output.correct:\n", "            print(\"Correct\")\n", "            n_correct += 1\n", "        else:\n", "            print(\"Incorrect\")\n", "            print(output.diff_against_expected)\n", "\n", "        result_json = {\n", "            \"output\": dataclasses.asdict(output),\n", "            \"input\": dataclasses.asdict(input_sample),\n", "            \"expected_output\": expected_output,\n", "            \"request_id\": request_id,\n", "            \"tenant_name\": tenant_name,\n", "            \"target_path\": target_path,\n", "            \"codeblock_index\": codeblock_index,\n", "        }\n", "        json.dump(result_json, f)\n", "        f.write(\"\\n\")\n", "        f.flush()\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
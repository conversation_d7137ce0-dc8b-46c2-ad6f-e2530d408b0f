{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from google.protobuf.json_format import MessageToDict\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "\n", "from experimental.yury.smart_paste_eval.data.bigquery_utils import (\n", "    download_request_using_bq,\n", ")\n", "from experimental.yury.smart_paste_eval.request_insight_api_utils import (\n", "    get_chat_host_request,\n", ")\n", "\n", "chat_request_id = \"30a1a217-6bef-48b9-a9de-86327bde4966\"\n", "tenant_name = \"dogfood-shard\"\n", "\n", "request_from_bq = download_request_using_bq(chat_request_id)\n", "print(request_from_bq[\"request\"])\n", "results = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_requests(\n", "    request_ids=[chat_request_id],\n", "    request_event_names=frozenset({\"chat_host_request\"}),\n", ")\n", "results = list(results)\n", "assert len(results) == 1\n", "result = results[0]\n", "\n", "request_from_ri_api = get_chat_host_request(chat_request_id, \"dogfood-shard\")\n", "\n", "\n", "def compare_nested_dict(dict1, dict2, path=\"\"):\n", "    \"\"\"Compare two dictionaries recursively and print differences.\"\"\"\n", "    all_keys = set(dict1.keys()) | set(dict2.keys())\n", "\n", "    for key in sorted(all_keys):\n", "        current_path = f\"{path}.{key}\" if path else key\n", "\n", "        # Check if key exists in both\n", "        if key not in dict1:\n", "            print(f\"Only in RI API: {current_path} = {dict2[key]}\")\n", "            continue\n", "        if key not in dict2:\n", "            print(f\"Only in BigQuery: {current_path} = {dict1[key]}\")\n", "            continue\n", "\n", "        val1, val2 = dict1[key], dict2[key]\n", "\n", "        # Compare values\n", "        if isinstance(val1, dict) and isinstance(val2, dict):\n", "            compare_nested_dict(val1, val2, current_path)\n", "        elif val1 != val2:\n", "            print(f\"Different values for {current_path}:\")\n", "            print(f\"  BigQuery: {val1}\")\n", "            print(f\"  RI API:   {val2}\")\n", "\n", "\n", "# Convert both requests to dictionaries for comparison\n", "bq_dict = (\n", "    request_from_bq if isinstance(request_from_bq, dict) else request_from_bq.__dict__\n", ")\n", "ri_dict = MessageToDict(request_from_ri_api)\n", "\n", "print(\"Comparing all fields between BigQuery and Request Insight API requests:\")\n", "print(\"-\" * 80)\n", "compare_nested_dict(bq_dict, ri_dict)\n", "print(\"-\" * 80)\n", "print(\"Comparison complete.\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["request_from_bq[\"request\"].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
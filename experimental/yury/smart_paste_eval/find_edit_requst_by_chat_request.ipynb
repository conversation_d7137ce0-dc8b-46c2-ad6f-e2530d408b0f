{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_eval.utils import find_matching_edit_requests\n", "\n", "chat_request_id = \"2dd104a6-6ad1-43b6-b47d-ec4128043edc\"\n", "tenant_name = \"dogfood-shard\"\n", "\n", "edit_request_ids = find_matching_edit_requests(chat_request_id, tenant_name)\n", "print(list(edit_request_ids))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
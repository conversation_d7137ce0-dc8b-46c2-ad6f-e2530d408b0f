from typing import Any
from dataclasses import dataclass
from base.datasets.gcs_client import GCSRequestInsightFetcher, group_by_event_name


@dataclass(frozen=True)
class RequestWithMetadata:
    request_id: str
    request: Any
    metadata: Any
    time: Any


def get_request_with_metadata(
    request_id: str, tenant_name: str, request_event_name: str
) -> RequestWithMetadata:
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({request_event_name, "request_metadata"}),
    )
    event_map = group_by_event_name(result.events)

    request = event_map[request_event_name][0]
    metadata = event_map["request_metadata"][0]
    return RequestWithMetadata(
        request_id=request_id,
        request=getattr(request, request_event_name).request,
        metadata=metadata.request_metadata,
        time=metadata.time,
    )


def _get_host_request(
    request_id: str, tenant_name: str, request_event_name: str, full: bool = False
):
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({request_event_name}),
    )

    for event in result.events:  # type: ignore
        if event.HasField(request_event_name):  # type: ignore
            full_request = getattr(event, request_event_name)
            if full:
                return full_request
            else:
                return full_request.request

    raise AssertionError(f"No {request_event_name} found in events")


def get_instruction_host_request(request_id: str, tenant_name: str):
    return _get_host_request(request_id, tenant_name, "instruction_host_request")


def get_chat_host_request(request_id: str, tenant_name: str, full: bool = False):
    return _get_host_request(request_id, tenant_name, "chat_host_request", full=full)

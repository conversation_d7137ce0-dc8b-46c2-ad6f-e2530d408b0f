"""Generate HTML view for SmartPasteEvalOutput."""

import argparse
import html
import json
from pathlib import Path
from pygments import highlight
from pygments.formatters import HtmlFormatter
from experimental.yury.smart_paste_eval.utils import (
    ModelEvaluationResults,
    SmartPasteEvalOutput,
    ComparisonResult,
    chat_history_proto_to_dict,
)
from experimental.yury.smart_paste_eval.ri_url import get_ri_url

COMMON_CSS = """
body {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
.container {
    margin: 0 auto;
    padding: 20px;
}
.section {
    margin: 20px 0;
    padding: 15px;
    background-color: #2d2d2d;
    border-radius: 5px;
}
.section h2 {
    margin-top: 0;
    color: #e6e6e6;
}
.section h2:hover {
    color: #ffffff;
}
.section h3 {
    color: #4ec9b0;
}
.section-content {
    display: none;
}
.section-content.expanded {
    display: block;
}
.status {
    padding: 5px 10px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 10px;
}
.status-correct {
    background: #2ea043;
    color: white;
}
.status-incorrect {
    background: #f85149;
    color: white;
}
pre {
    margin: 0;
    overflow-x: auto;
    white-space: pre;
    background-color: #1e1e1e;
    padding: 10px;
    border-radius: 3px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}
.highlight {
    background: #1e1e1e !important;
}
.diff-add {
    background: #1b4721;
    color: #3cbe5a;
    display: inline;
}
.diff-remove {
    background: #78191b;
    color: #ff7b72;
    display: inline;
}
.diff-info {
    background: #1b1f23;
    color: #58a6ff;
    display: inline;
}
.diff-context {
    color: #8b949e;
    display: inline;
}
p {
    margin: 10px 0;
}
p strong {
    color: #ffffff;
}
.whitespace-tab::before {
    content: '→   ';
    color: #3c3c3c;
    opacity: 0.8;
}
.whitespace-space::before {
    content: '·';
    color: #3c3c3c;
    opacity: 0.8;
}
.action-button {
    background-color: #2d2d2d;
    color: #d4d4d4;
    border: 1px solid #3c3c3c;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}
.action-button:hover {
    background-color: #3c3c3c;
    border-color: #569cd6;
}
.action-button:active {
    background-color: #4c4c4c;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #3c3c3c;
}
th {
    background-color: #2d2d2d;
}
.stats-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}
.stats-table th, .stats-table td {
    padding: 8px;
    text-align: left;
    border: 1px solid #3c3c3c;
}
.stats-table th {
    background-color: #2d2d2d;
}
.stats-table tr:nth-child(even) {
    background-color: #2a2a2a;
}
a {
    color: #569cd6;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
"""


def gen_request_id_link(request_id, url):
    return f'<a href="{get_ri_url(url, request_id)}">{request_id}</a>'


def format_chat_history(chat_history):
    """Format chat history for display, handling both proto and dict formats."""
    if not chat_history:
        return []

    # If items are already dictionaries, return as is
    if isinstance(chat_history[0], dict):
        return chat_history

    # If items are proto objects, use the existing function
    return chat_history_proto_to_dict(chat_history)


def detect_language(file_path: str | None, content: str) -> str:
    """Detect programming language from content and optionally file path.

    Uses multiple strategies:
    1. Try to detect from common patterns (most reliable)
    2. Try to guess from file path
    3. Try to guess from content
    4. Finally fall back to plain text
    """
    # First try common patterns as they're most reliable
    content_lower = content.lower()
    if any(
        keyword in content_lower for keyword in ["def ", "class ", "import ", "from "]
    ):
        return "Python"
    if any(
        keyword in content_lower for keyword in ["function", "const ", "let ", "var "]
    ):
        return "JavaScript"
    if any(keyword in content_lower for keyword in ["#include", "int ", "void "]):
        return "C"

    # Try file path
    if file_path:
        try:
            from pygments.lexers import get_lexer_for_filename

            lexer = get_lexer_for_filename(file_path)
            return lexer.name
        except Exception:
            pass

    # Try content detection as last resort before fallback
    if content.strip():
        try:
            from pygments.lexers import guess_lexer

            lexer = guess_lexer(content)
            # Filter out some common false positives
            if lexer.name not in ["Text only", "Tera Term macro"]:
                return lexer.name
        except Exception:
            pass

    # Default to text
    return "Text"


def format_diff(diff_text: str) -> str:
    """Format a diff string as HTML with syntax highlighting."""
    if not diff_text:
        return ""
    result = []
    for line in diff_text.splitlines():
        line = html.escape(line)
        # Add whitespace visualization
        if line:
            if line.startswith("@@"):
                # For diff info lines, keep original whitespace
                result.append(f'<span class="diff-info">{line}</span>')
            else:
                # For other lines, handle prefix and content separately
                prefix = ""
                content = line
                if line.startswith(("+", "-", " ")):
                    prefix = line[0]
                    content = line[1:]

                # Replace all whitespace with spans
                content = content.replace("\t", '<span class="whitespace-tab"></span>')
                content = content.replace(" ", '<span class="whitespace-space"></span>')

                # Add back the prefix and wrap in appropriate span
                if prefix == "+":
                    result.append(f'<span class="diff-add">{prefix}{content}</span>')
                elif prefix == "-":
                    result.append(f'<span class="diff-remove">{prefix}{content}</span>')
                elif prefix == " ":
                    # For context lines, add a space span at the beginning
                    result.append(
                        f'<span class="diff-context"><span class="whitespace-space"></span>{content}</span>'
                    )
                else:
                    result.append(f'<span class="diff-context">{content}</span>')
    return '<div class="diff"><pre>\n' + "\n".join(result) + "\n</pre></div>"


def format_code(
    code: str, language: str | None = None, file_path: str | None = None
) -> str:
    """Format code with syntax highlighting.

    Args:
        code: The code to format
        language: Optional language name to force a specific lexer
        file_path: Optional file path to help with language detection
    """
    if not code.strip():
        return code

    try:
        if language:
            from pygments.lexers import get_lexer_by_name

            lexer = get_lexer_by_name(language.lower().replace(" ", ""))
        else:
            detected_lang = detect_language(file_path, code)
            from pygments.lexers import get_lexer_by_name

            lexer = get_lexer_by_name(detected_lang.lower().replace(" ", ""))
    except Exception:
        from pygments.lexers import TextLexer

        lexer = TextLexer()

    formatter = HtmlFormatter(style="monokai")
    highlighted = highlight(code, lexer, formatter)

    # Add whitespace visualization
    lines = []
    for line in highlighted.splitlines():
        if line.strip():  # Only process non-empty lines
            # Replace tabs with visible markers
            line = line.replace("\t", '<span class="whitespace-tab"></span>')
            # Replace leading spaces with visible markers
            indent = len(line) - len(line.lstrip())
            if indent > 0:
                spaces = "".join(
                    ['<span class="whitespace-space"></span>' for _ in range(indent)]
                )
                line = spaces + line[indent:]
        lines.append(line)

    return "\n".join(lines)


def generate_eval_output_html(eval_output) -> str:
    """Generate HTML view for a SmartPasteEvalOutput object."""
    # For the main response, try to detect language from both path and content
    file_path = eval_output.prompt_input.path if eval_output.prompt_input else None
    language = detect_language(file_path, eval_output.response)

    # Get CSS for syntax highlighting and our custom styles
    css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

    # Update syntax highlighting colors to match VSCode
    syntax_css = """
    .highlight .k { color: #569cd6; } /* Keyword */
    .highlight .kc { color: #569cd6; } /* Keyword.Constant */
    .highlight .kd { color: #569cd6; } /* Keyword.Declaration */
    .highlight .kn { color: #569cd6; } /* Keyword.Namespace */
    .highlight .kp { color: #569cd6; } /* Keyword.Pseudo */
    .highlight .kr { color: #569cd6; } /* Keyword.Reserved */
    .highlight .kt { color: #569cd6; } /* Keyword.Type */

    .highlight .n { color: #9cdcfe; } /* Name */
    .highlight .na { color: #9cdcfe; } /* Name.Attribute */
    .highlight .nb { color: #9cdcfe; } /* Name.Builtin */
    .highlight .bp { color: #9cdcfe; } /* Name.Builtin.Pseudo */
    .highlight .nc { color: #4ec9b0; } /* Name.Class */
    .highlight .no { color: #4fc1ff; } /* Name.Constant */
    .highlight .nd { color: #c586c0; } /* Name.Decorator */
    .highlight .ni { color: #9cdcfe; } /* Name.Entity */
    .highlight .ne { color: #9cdcfe; } /* Name.Exception */
    .highlight .nf { color: #dcdcaa; } /* Name.Function */
    .highlight .nl { color: #9cdcfe; } /* Name.Label */
    .highlight .nn { color: #9cdcfe; } /* Name.Namespace */
    .highlight .nt { color: #569cd6; } /* Name.Tag */
    .highlight .nv { color: #9cdcfe; } /* Name.Variable */

    .highlight .s { color: #ce9178; } /* String */
    .highlight .sa { color: #ce9178; } /* String.Affix */
    .highlight .sb { color: #ce9178; } /* String.Backtick */
    .highlight .sc { color: #ce9178; } /* String.Char */
    .highlight .dl { color: #ce9178; } /* String.Delimiter */
    .highlight .sd { color: #ce9178; } /* String.Doc */
    .highlight .s2 { color: #ce9178; } /* String.Double */
    .highlight .se { color: #ce9178; } /* String.Escape */
    .highlight .sh { color: #ce9178; } /* String.Heredoc */
    .highlight .si { color: #ce9178; } /* String.Interpol */
    .highlight .sx { color: #ce9178; } /* String.Other */
    .highlight .sr { color: #ce9178; } /* String.Regex */
    .highlight .s1 { color: #ce9178; } /* String.Single */
    .highlight .ss { color: #ce9178; } /* String.Symbol */

    .highlight .c { color: #6a9955; } /* Comment */
    .highlight .ch { color: #6a9955; } /* Comment.Hashbang */
    .highlight .cm { color: #6a9955; } /* Comment.Multiline */
    .highlight .cp { color: #6a9955; } /* Comment.Preproc */
    .highlight .cpf { color: #6a9955; } /* Comment.PreprocFile */
    .highlight .c1 { color: #6a9955; } /* Comment.Single */
    .highlight .cs { color: #6a9955; } /* Comment.Special */

    .highlight .o { color: #d4d4d4; } /* Operator */
    .highlight .ow { color: #569cd6; } /* Operator.Word */

    .highlight .m { color: #b5cea8; } /* Number */
    .highlight .mb { color: #b5cea8; } /* Number.Bin */
    .highlight .mf { color: #b5cea8; } /* Number.Float */
    .highlight .mh { color: #b5cea8; } /* Number.Hex */
    .highlight .mi { color: #b5cea8; } /* Number.Integer */
    .highlight .mo { color: #b5cea8; } /* Number.Oct */
    """

    # Generate HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Smart Paste Eval Output - {eval_output.request_id}</title>
        <style>
            {css}
            {COMMON_CSS}
            {syntax_css}
        </style>
        <script>
            function toggleSection(id) {{
                const content = document.getElementById(id);
                content.classList.toggle('expanded');
            }}
        </script>
    </head>
    <body>
        <div class="container">
            <h1>Smart Paste Eval Output</h1>
            <div class="status {'status-correct' if eval_output.correct else 'status-incorrect'}">
                {'✓ Correct' if eval_output.correct else '✗ Incorrect'}
            </div>
            <p>Request ID: {eval_output.request_id}</p>

            <div class="section">
                <h2 onclick="toggleSection('response')">Response</h2>
                <div id="response" class="section-content">
                    <pre class="highlight"><div>{format_code(eval_output.response, language=language)}</div></pre>
                </div>
            </div>

            <div class="section">
                <h2 onclick="toggleSection('modified')">Modified Target File</h2>
                <div id="modified" class="section-content">
                    <pre class="highlight"><div>{format_code(eval_output.modified_target_file or '', file_path=file_path)}</div></pre>
                </div>
            </div>

            <div class="section">
                <h2 onclick="toggleSection('diff-original')">Diff Against Original</h2>
                <div id="diff-original" class="section-content">
                    <pre>{format_diff(eval_output.diff_against_original or '')}</pre>
                </div>
            </div>

            <div class="section">
                <h2 onclick="toggleSection('diff-expected')">Diff Against Expected</h2>
                <div id="diff-expected" class="section-content">
                    <pre>{format_diff(eval_output.diff_against_expected or '')}</pre>
                </div>
            </div>
    """

    # Add Claude analysis if available
    if eval_output.claude_analysis:
        html_content += f"""
            <div class="section">
                <h2>Claude Analysis</h2>
                <pre>{html.escape(eval_output.claude_analysis)}</pre>
            </div>
        """

    if eval_output.prompt_input:
        html_content += f"""
            <div class="section">
                <h2 onclick="toggleSection('prompt-input')">Prompt Input</h2>
                <div id="prompt-input" class="section-content">
                    <p><strong>Path:</strong> {eval_output.prompt_input.path}</p>
                    <p><strong>Selected Code:</strong></p>
                    <pre class="highlight"><div>{format_code(eval_output.prompt_input.selected_code, file_path=eval_output.prompt_input.path)}</div></pre>
                    <p><strong>Code Block:</strong></p>
                    <pre class="highlight"><div>{format_code(eval_output.prompt_input.code_block, file_path=eval_output.prompt_input.path)}</div></pre>
                    <p><strong>Target Path:</strong> {eval_output.prompt_input.target_path}</p>
                    <p><strong>Chat History:</strong></p>
                    <pre>{json.dumps(format_chat_history(eval_output.prompt_input.chat_history), indent=2)}</pre>
                </div>
            </div>
        """

    html_content += """
        </div>
    </body>
    </html>
    """

    return html_content


def generate_model_eval_html(
    results: ModelEvaluationResults, output_dir: Path | None = None
) -> str:
    """Generate HTML view for ModelEvaluationResults.

    Args:
        results: The ModelEvaluationResults to generate HTML for.
        output_dir: Optional output directory where individual eval outputs are saved.
                   If provided, will generate links to individual eval output pages.
    """
    # Get CSS from the existing viewer
    css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

    # Add filter styles
    filter_css = """
    .filter-container {
        margin: 20px 0;
        padding: 15px;
        background-color: #1e1e1e;
        border: 1px solid #3c3c3c;
        border-radius: 6px;
    }
    .filter-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        gap: 10px;
    }
    .filter-input {
        flex: 1;
        padding: 8px;
        background-color: #2d2d2d;
        border: 1px solid #3c3c3c;
        color: #d4d4d4;
        border-radius: 4px;
    }
    .filter-select {
        padding: 8px;
        background-color: #2d2d2d;
        border: 1px solid #3c3c3c;
        color: #d4d4d4;
        border-radius: 4px;
    }
    .filter-actions {
        margin-top: 10px;
    }
    .hidden {
        display: none !important;
    }
    """

    # Add filter script
    filter_script = """
    <script>
    let filterCount = 1;

    function addFilter() {
        const filtersContainer = document.getElementById('filters');
        const newRow = document.createElement('div');
        newRow.className = 'filter-row';
        newRow.id = `filter-row-${filterCount}`;

        newRow.innerHTML = `
            <select class="filter-select filter-field" onchange="filterResults()">
                <option value="request_id">Request ID</option>
                <option value="category">Category</option>
                <option value="chat_request_id">Chat Request ID</option>
                <option value="edit_request_id">Edit Request ID</option>
                <option value="path">Path</option>
                <option value="chat_model_response_success">Chat Model Response Success</option>
                <option value="diff_found">Diff Found</option>
                <option value="diff_with_target_path_found">Diff With Target Path Found</option>
                <option value="diff_apply_success">Diff Apply Success</option>
                <option value="diff_apply_found_start">Diff Apply Found Start</option>
                <option value="diff_pure_addition">Diff Pure Addition</option>
                <option value="diff_pure_addition_to_non_empty_file">Diff Pure Addition To Non Empty File</option>
            </select>
            <select class="filter-select filter-type" onchange="filterResults()">
                <option value="contains">Contains</option>
                <option value="equals">Equals</option>
                <option value="true">Is True</option>
                <option value="false">Is False</option>
            </select>
            <input type="text" class="filter-input filter-value" placeholder="Enter filter value..." onkeyup="filterResults()">
            <button class="action-button" onclick="removeFilter(${filterCount})">Remove</button>
        `;

        filtersContainer.appendChild(newRow);
        filterCount++;
        filterResults();
    }

    function removeFilter(id) {
        const row = document.getElementById(`filter-row-${id}`);
        row.remove();
        filterResults();
    }

    function filterResults() {
        const results = document.getElementsByClassName('result-item');
        const filterRows = document.getElementsByClassName('filter-row');

        for (let result of results) {
            let showResult = true;

            for (let filterRow of filterRows) {
                const filterField = filterRow.querySelector('.filter-field').value;
                const filterType = filterRow.querySelector('.filter-type').value;
                const filterValue = filterRow.querySelector('.filter-value').value.toLowerCase();

                const fieldValue = result.getAttribute('data-' + filterField);

                if (fieldValue && filterValue) {
                    const value = fieldValue.toLowerCase();
                    let matches = false;

                    switch (filterType) {
                        case 'contains':
                            matches = value.includes(filterValue);
                            break;
                        case 'equals':
                            matches = value === filterValue;
                            break;
                        case 'true':
                            matches = value === 'true';
                            break;
                        case 'false':
                            matches = value === 'false';
                            break;
                    }

                    if (!matches) {
                        showResult = false;
                        break;
                    }
                }
            }

            result.classList.toggle('hidden', !showResult);
        }

        updateResultCounts();
    }

    function resetFilters() {
        const filtersContainer = document.getElementById('filters');
        filtersContainer.innerHTML = '';
        addFilter();
    }

    function updateResultCounts() {
        const correctGroup = document.getElementById('correct-results-group');
        const incorrectGroup = document.getElementById('incorrect-results-group');

        if (correctGroup) {
            const visibleCorrect = correctGroup.getElementsByClassName('result-item').length -
                                 correctGroup.getElementsByClassName('result-item hidden').length;
            correctGroup.previousElementSibling.textContent = `Correct Results (${visibleCorrect} cases)`;
        }

        if (incorrectGroup) {
            const visibleIncorrect = incorrectGroup.getElementsByClassName('result-item').length -
                                   incorrectGroup.getElementsByClassName('result-item hidden').length;
            incorrectGroup.previousElementSibling.textContent = `Incorrect Results (${visibleIncorrect} cases)`;
        }
    }
    </script>
    """

    # Add filter section at the top
    filter_section = """
    <div class="filter-container">
        <h2>Filter Results</h2>
        <div id="filters"></div>
        <div class="filter-actions">
            <button class="action-button" onclick="addFilter()">Add Filter</button>
            <button class="action-button" onclick="resetFilters()">Reset Filters</button>
        </div>
    </div>
    <script>
        // Initialize with one filter
        document.addEventListener('DOMContentLoaded', function() {
            addFilter();
        });
    </script>
    """

    # Generate model info section
    model_info_section = f"""
    <div class="section">
        <h2 onclick="toggleSection('model-info')">Model Information</h2>
        <div id="model-info" class="section-content expanded">
            <table class="stats-table">
                <tr>
                    <th>Model Name</th>
                    <td>{results.model_name}</td>
                </tr>
                <tr>
                    <th>API URL</th>
                    <td>{results.url}</td>
                </tr>
            </table>
        </div>
    </div>
    """

    # Generate overall statistics section
    stats_section = f"""
    <div class="section">
        <h2 onclick="toggleSection('overall-stats')">Overall Statistics</h2>
        <div id="overall-stats" class="section-content expanded">
            <table class="stats-table">
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Total Correct</td>
                    <td>{results.total_correct}</td>
                </tr>
                <tr>
                    <td>Total Processed</td>
                    <td>{results.total_processed}</td>
                </tr>
                <tr>
                    <td>Total Skipped</td>
                    <td>{results.total_skipped}</td>
                </tr>
                <tr>
                    <td>Accuracy</td>
                    <td>{(results.total_correct / results.total_processed * 100):.1f}%</td>
                </tr>
            </table>
        </div>
    </div>
    """

    # Generate diff apply statistics section
    diff_apply_stats_section = f"""
    <div class="section">
        <h2 onclick="toggleSection('diff-apply-stats')">Diff Apply Statistics</h2>
        <div id="diff-apply-stats" class="section-content expanded">
            <pre class="error-message">{html.escape(results.get_diff_apply_stats())}</pre>
        </div>
    </div>
    """

    # Generate category statistics section
    category_stats_rows = []
    for category, stats in results.category_stats.items():
        correct = stats["correct"]
        total = stats["total"]
        accuracy = (correct / total * 100) if total > 0 else 0
        category_stats_rows.append(f"""
        <tr>
            <td>{category}</td>
            <td>{correct}</td>
            <td>{total}</td>
            <td>{accuracy:.1f}%</td>
        </tr>
        """)

    category_stats_section = f"""
    <div class="section">
        <h2 onclick="toggleSection('category-stats')">Category Statistics</h2>
        <div id="category-stats" class="section-content expanded">
            <table class="stats-table">
                <tr>
                    <th>Category</th>
                    <th>Correct</th>
                    <th>Total</th>
                    <th>Accuracy</th>
                </tr>
                {''.join(category_stats_rows)}
            </table>
        </div>
    </div>
    """

    def render_chat_history(chat_history):
        """Renders chat history as HTML with expandable details."""
        if not chat_history:
            return "<div><p>Chat history: N/A</p></div>"

        chat_history = chat_history_proto_to_dict(chat_history)

        exchanges_html = ""
        for i, exchange in enumerate(chat_history):
            exchanges_html += f"""
            <h3>Message {i}</h3>
            <div class="exchange">
                <div class="message">
                    <div class="user">{exchange.get("request_message", "")}</div>
                </div>
                <div class="response">
                    <div class="assistant"><pre>{exchange.get("response_text", "")[:100]}</pre></div>
                </div>
            </div>
            <hr>
            """

        return f"""
        <details>
            <summary>Chat history</summary>
            <div class="chat-history">
                {exchanges_html}
            </div>
        </details>
        """

    def generate_prompt_input_section(prompt_input, uuid):
        if not prompt_input:
            return ""
        return f"""
        <div class="section">
            <h2 onclick="toggleSection('prompt-{uuid}')">Prompt Input</h2>
            <div id="prompt-{uuid}" class="section-content">
                <p><strong>Path:</strong> {prompt_input.path}</p>
                <p><strong>Selected Code:</strong></p>
                <pre class="highlight"><div>{format_code(prompt_input.selected_code, file_path=prompt_input.path)}</div></pre>
                <p><strong>Code Block:</strong></p>
                <pre class="highlight"><div>{format_code(prompt_input.code_block, file_path=prompt_input.path)}</div></pre>
                <p><strong>Target Path:</strong> {prompt_input.target_path}</p>
                <p><strong>Chat History:</strong></p>
                {render_chat_history(prompt_input.chat_history)}
            </div>
        </div>
        """

    def generate_chat_prompt_input_section(chat_prompt_input, uuid):
        if not chat_prompt_input:
            return ""
        return f"""
        <div class="section">
            <h2 onclick="toggleSection('chat-prompt-{uuid}')">Chat Prompt Input</h2>
            <div id="chat-prompt-{uuid}" class="section-content">
                <p><strong>Chat History:</strong></p>
                {render_chat_history(chat_prompt_input.chat_history)}
            </div>
        </div>
        """

    # Group results by correct/incorrect
    correct_results = []
    incorrect_results = []
    url = results.url
    for uuid, output in results.sample_results.items():
        eval_sample = output.eval_sample
        chat_history = format_chat_history(output.prompt_input.chat_history)
        request_message = chat_history[-1]["request_message"]
        file_path = output.prompt_input.path if output.prompt_input else None
        language = detect_language(file_path, output.response)

        # Add link to individual eval output if output_dir is provided
        eval_output_link = ""
        if output_dir:
            eval_output_link = (
                f'<p><a href="eval_output_{uuid}.html">View detailed results</a></p>'
            )

        claude_analysis_section = (
            f"""
            <div class="section">
                <h2 onclick="toggleSection('claude-analysis-{uuid}')">Claude Analysis</h2>
                <div id="claude-analysis-{uuid}" class="section-content expanded">
                    {html.escape(output.claude_analysis or 'Analysis not available')}
                </div>
            </div>
            """
            if output.claude_analysis
            else ""
        )

        # clear some sections to reduce page size
        output.response = ""
        output.chat_prompt_input = None

        result_section = f"""
        <div class="section result-item"
             data-request_id="{output.request_id}"
             data-category="{eval_sample.category}"
             data-chat_request_id="{eval_sample.chat_request_id}"
             data-edit_request_id="{eval_sample.edit_request_id}"
             data-path="{output.prompt_input.target_path}"
             data-chat_model_response_success="{output.chat_model_response_success}"
             data-diff_found="{output.diff_found}"
             data-diff_with_target_path_found="{output.diff_with_target_path_found}"
             data-diff_apply_success="{output.diff_apply_success}"
             data-diff_apply_found_start="{output.diff_apply_found_start}"
             data-diff_pure_addition="{output.diff_pure_addition}"
             data-diff_pure_addition_to_non_empty_file="{output.diff_pure_addition_to_non_empty_file}">
            <h2 onclick="toggleSection('result-{uuid}')">{html.escape(request_message)}</h2>
            <div id="result-{uuid}" class="section-content">
                <div class="status {'status-correct' if output.correct else 'status-incorrect'}">
                    {'✓ Correct' if output.correct else '✗ Incorrect'}
                </div>
                {eval_output_link}

                <div>
                    <div>UUID: {uuid}</div>
                    <div>Original Chat Request ID: {gen_request_id_link(eval_sample.chat_request_id, url)}</div>
                    <div>Original Edit Request ID: {gen_request_id_link(eval_sample.edit_request_id, url)}</div>
                    <div>Path: {output.prompt_input.target_path}</div>
                    <div>Category: {eval_sample.category}</div>
                    <div>Edit Request ID: {gen_request_id_link(output.request_id, url)}</div>
                    <div>Chat Model Response Success: {output.chat_model_response_success}</div>
                    <div>Diff Found: {output.diff_found}</div>
                    <div>Diff With Target Path Found: {output.diff_with_target_path_found}</div>
                    <div>Diff Apply Success: {output.diff_apply_success}</div>
                    <div>Diff Apply Found Start: {output.diff_apply_found_start}</div>
                    <div>Diff Pure Addition: {output.diff_pure_addition}</div>
                    <div>Diff Pure Addition To Non Empty File: {output.diff_pure_addition_to_non_empty_file}</div>
                </div>

                {claude_analysis_section}

                <div class="section">
                    <h2 onclick="toggleSection('code-block-{uuid}')">Code block</h2>
                    <div id="code-block-{uuid}" class="section-content">
                        <pre class="highlight"><div>{format_code(output.prompt_input.code_block, language=language)}</div></pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('chat-model-response-{uuid}')">Chat Model Response</h2>
                    <div id="chat-model-response-{uuid}" class="section-content">
                        <pre class="highlight"><div>{output.chat_model_response or ''}</div></pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('original-{uuid}')">Original file</h2>
                    <div id="original-{uuid}" class="section-content">
                        <pre class="highlight"><div>{format_code(output.prompt_input.target_file_content, language=language)}</div></pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('response-{uuid}')">Response</h2>
                    <div id="response-{uuid}" class="section-content">
                        <pre class="highlight"><div>{format_code(output.response, language=language)}</div></pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('diff-original-{uuid}')">Diff Against Original</h2>
                    <div id="diff-original-{uuid}" class="section-content">
                        <pre>{format_diff(output.diff_against_original or '')}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('diff-expected-{uuid}')">Diff Against Expected</h2>
                    <div id="diff-expected-{uuid}" class="section-content">
                        <pre>{format_diff(output.diff_against_expected or '')}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('error-{uuid}')">Error</h2>
                    <div id="error-{uuid}" class="section-content expanded">
                        <pre class="error-message">{html.escape(output.get_error_message())}</pre>
                    </div>
                </div>

                {generate_chat_prompt_input_section(output.chat_prompt_input, uuid) if output.chat_prompt_input else ''}

                {generate_prompt_input_section(output.prompt_input, uuid) if output.prompt_input else ''}
            </div>
        </div>
        """
        if output.correct:
            correct_results.append(result_section)
        else:
            incorrect_results.append(result_section)

    # Generate group sections with expand/collapse buttons
    results_sections = []
    if correct_results:
        group_id = "correct-results-group"
        results_sections.append(f"""
            <div class="section">
                <h2 onclick="toggleSection('{group_id}')" style="cursor: pointer;">
                    Correct Results ({len(correct_results)} cases)
                </h2>
                <div id="{group_id}" class="section-content expanded">
                    <div style="margin-bottom: 10px;">
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'expand')" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'collapse')">Collapse All</button>
                    </div>
                    {''.join(correct_results)}
                </div>
            </div>
        """)
    if incorrect_results:
        group_id = "incorrect-results-group"
        results_sections.append(f"""
            <div class="section">
                <h2 onclick="toggleSection('{group_id}')" style="cursor: pointer;">
                    Incorrect Results ({len(incorrect_results)} cases)
                </h2>
                <div id="{group_id}" class="section-content expanded">
                    <div style="margin-bottom: 10px;">
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'expand')" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'collapse')">Collapse All</button>
                    </div>
                    {''.join(incorrect_results)}
                </div>
            </div>
        """)

    # Update the JavaScript for toggling sections
    toggle_script = """
    <script>
        function toggleSection(id) {
            const content = document.getElementById(id);
            content.classList.toggle('expanded');
        }

        // Function to toggle all sections in a group
        function toggleGroup(groupId, action) {
            const group = document.getElementById(groupId);
            const sections = group.getElementsByClassName('section-content');

            // Skip the first section-content as it's the group container itself
            for (let i = 1; i < sections.length; i++) {
                if (action === 'expand') {
                    sections[i].classList.add('expanded');
                } else {
                    sections[i].classList.remove('expanded');
                }
            }
        }
    </script>
    """

    # Generate final HTML
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Model Evaluation Results - {results.model_name}</title>
        <style>
            {css}
            {COMMON_CSS}
            {filter_css}
        </style>
        {toggle_script}
        {filter_script}
    </head>
    <body>
        <div class="container">
            <h1>Model Evaluation Results</h1>
            {filter_section}
            {model_info_section}
            {stats_section}
            {diff_apply_stats_section}
            {category_stats_section}
            {''.join(results_sections)}
        </div>
    </body>
    </html>
    """

    return html_content


def save_eval_output_html(
    eval_output: SmartPasteEvalOutput, output_path: str | Path
) -> None:
    """Save SmartPasteEvalOutput as HTML to the specified path."""
    html_content = generate_eval_output_html(eval_output)
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    output_path.write_text(html_content)


def save_model_eval_html(
    results: ModelEvaluationResults, output_path: str | Path
) -> None:
    """Save ModelEvaluationResults as HTML to the specified path."""
    output_path = Path(output_path)
    output_dir = output_path.parent

    # Save individual eval outputs
    for uuid, eval_output in results.sample_results.items():
        eval_output_path = output_dir / f"eval_output_{uuid}.html"
        save_eval_output_html(eval_output, eval_output_path)

    # Save main model eval HTML
    html_content = generate_model_eval_html(results, output_dir)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    output_path.write_text(html_content)


def save_comparison_html(comparison: ComparisonResult, output_path: str | Path) -> None:
    """Save ComparisonResult as HTML to the specified path."""
    output_path = Path(output_path)
    output_dir = output_path.parent

    # Save results for both models
    results_path = output_dir / "results.html"
    ref_results_path = output_dir / "ref_results.html"
    save_model_eval_html(comparison.results, results_path)
    save_model_eval_html(comparison.ref_results, ref_results_path)

    # Get CSS from the existing viewer
    css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

    # Add collapsible section styles - unique to comparison view
    collapsible_css = """
    .collapsible {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        border: none;
        background: none;
        color: inherit;
        font: inherit;
        padding: 0;
        margin: 0;
        text-align: left;
    }
    .collapsible:hover {
        opacity: 0.8;
    }
    .collapsible:after {
        content: '▼';
        font-size: 13px;
        color: #569cd6;
    }
    .collapsible.collapsed:after {
        content: '▶';
    }
    .collapsible-content {
        display: block;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }
    .collapsible-content.collapsed {
        display: none;
    }
    .side-by-side {
        display: flex;
        gap: 20px;
    }
    .side-by-side > div {
        flex: 1;
    }
    .code-block {
        margin: 10px 0;
    }
    .correct {
        color: #4CAF50;
        font-weight: bold;
    }
    .incorrect {
        color: #f44336;
        font-weight: bold;
    }
    .diff {
        font-family: 'Consolas', 'Courier New', monospace;
        margin: 0;
        padding: 12px;
        background-color: #1e1e1e;
        border-radius: 6px;
        line-height: 1.5;
        font-size: 14px;
        border: 1px solid #2d2d2d;
    }
    .diff-section {
        margin: 15px 0;
        padding: 15px;
        border: 1px solid #3c3c3c;
        border-radius: 6px;
        background-color: #1e1e1e;
    }
    h5 {
        color: #9cdcfe;
        margin: 15px 0 8px 0;
        font-size: 14px;
    }
    """

    # Add toggle script for collapsible sections
    toggle_script = """
    <script>
        function toggleSection(id) {
            const button = document.getElementById('button-' + id);
            const content = document.getElementById('content-' + id);
            const isCollapsed = content.classList.contains('collapsed');

            // Toggle the collapsed state
            if (isCollapsed) {
                content.classList.remove('collapsed');
                button.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                button.classList.add('collapsed');
            }
        }

        // Function to toggle all sections in a group
        function toggleGroup(groupId, action) {
            const group = document.getElementById(groupId);
            const buttons = group.getElementsByClassName('collapsible');
            const contents = group.getElementsByClassName('collapsible-content');

            for (let i = 0; i < buttons.length; i++) {
                if (action === 'expand') {
                    buttons[i].classList.remove('collapsed');
                    contents[i].classList.remove('collapsed');
                } else {
                    buttons[i].classList.add('collapsed');
                    contents[i].classList.add('collapsed');
                }
            }
        }
    </script>
    """

    # Create comparison table
    comparison_table = f"""
    <table>
        <tr>
            <th>Metric</th>
            <th>{comparison.ref_results.model_name}</th>
            <th>{comparison.results.model_name}</th>
        </tr>
        <tr>
            <td>Total Correct</td>
            <td>{comparison.ref_results.total_correct}</td>
            <td>{comparison.results.total_correct}</td>
        </tr>
        <tr>
            <td>Total Processed</td>
            <td>{comparison.ref_results.total_processed}</td>
            <td>{comparison.results.total_processed}</td>
        </tr>
        <tr>
            <td>Total Skipped</td>
            <td>{comparison.ref_results.total_skipped}</td>
            <td>{comparison.results.total_skipped}</td>
        </tr>
        <tr>
            <td>Accuracy</td>
            <td>{comparison.ref_results.total_correct / comparison.ref_results.total_processed:.2%}</td>
            <td>{comparison.results.total_correct / comparison.results.total_processed:.2%}</td>
        </tr>
        <tr>
            <td>Number of Different Results</td>
            <td colspan="2">{comparison.num_different}</td>
        </tr>
        <tr>
            <td>Number of Regressions</td>
            <td colspan="2">{comparison.num_regressions}</td>
        </tr>
        <tr>
            <td>Number of Improvements</td>
            <td colspan="2">{comparison.num_improvements}</td>
        </tr>
    </table>
    """

    # Create category comparison table
    category_rows = []
    for category in set(comparison.ref_results.category_stats.keys()) | set(
        comparison.results.category_stats.keys()
    ):
        ref_stats = comparison.ref_results.category_stats.get(
            category, {"correct": 0, "total": 0}
        )
        stats = comparison.results.category_stats.get(
            category, {"correct": 0, "total": 0}
        )
        category_rows.append(
            f"""
            <tr>
                <td>{category}</td>
                <td>{ref_stats["correct"]}/{ref_stats["total"]} ({ref_stats["correct"] / ref_stats["total"]:.2%})</td>
                <td>{stats["correct"]}/{stats["total"]} ({stats["correct"] / stats["total"]:.2%})</td>
            </tr>
            """
        )
    category_table = f"""
    <table>
        <tr>
            <th>Category</th>
            <th>{comparison.ref_results.model_name}</th>
            <th>{comparison.results.model_name}</th>
        </tr>
        {"".join(category_rows)}
    </table>
    """

    # Group diffs into categories
    regressions = []
    improvements = []
    both_incorrect = []
    for diff in comparison.diffs:
        if diff.ref_correct and not diff.output_correct:
            regressions.append(diff)
        elif not diff.ref_correct and diff.output_correct:
            improvements.append(diff)
        elif not diff.ref_correct and not diff.output_correct:
            both_incorrect.append(diff)

    # Function to generate diff section HTML
    def generate_diff_section(diff, index):
        ref_eval_output = comparison.ref_results.sample_results[diff.uuid]
        eval_output = comparison.results.sample_results[diff.uuid]
        ref_url = comparison.ref_results.url
        url = comparison.results.url
        eval_sample = ref_eval_output.eval_sample

        section_id = f"diff-{index}"
        return f"""
            <div class="diff-section">
                <button id="button-{section_id}" class="collapsible collapsed" onclick="toggleSection('{section_id}')">
                    <h3>Request: {html.escape(diff.request_message)}</h3>
                </button>
                <div id="content-{section_id}" class="collapsible-content collapsed">
                    <div>
                        <div>UUID: {diff.uuid}</div>
                        <div>Original Chat Request ID: {gen_request_id_link(eval_sample.chat_request_id, ref_url)}</div>
                        <div>Original Edit Request ID: {gen_request_id_link(eval_sample.edit_request_id, ref_url)}</div>
                        <div>Path: {ref_eval_output.prompt_input.target_path}</div>
                        <div>Category: {eval_sample.category}</div>
                        <div>Edit Request ID({comparison.ref_results.model_name}): {gen_request_id_link(ref_eval_output.request_id, ref_url)}</div>
                        <div>Edit Request ID({comparison.results.model_name}): {gen_request_id_link(eval_output.request_id, url)}</div>
                    </div>
                    <div class="code-block">
                        <h4>Code:</h4>
                        <pre>{format_code(diff.code_block)}</pre>
                    </div>
                    <div class="side-by-side">
                        <div>
                            <h4>{comparison.ref_results.model_name} <span class="{('correct' if diff.ref_correct else 'incorrect')}">{'✓ Correct' if diff.ref_correct else '✗ Incorrect'}</span>:</h4>
                            <div>
                                <h5>Diff against original:</h5>
                                {format_diff(diff.ref_diff_against_original)}
                            </div>
                            <div>
                                <h5>Diff against expected:</h5>
                                {format_diff(diff.ref_diff_against_expected)}
                            </div>
                        </div>
                        <div>
                            <h4>{comparison.results.model_name} <span class="{('correct' if diff.output_correct else 'incorrect')}">{'✓ Correct' if diff.output_correct else '✗ Incorrect'}</span>:</h4>
                            <div>
                                <h5>Diff against original:</h5>
                                {format_diff(diff.output_diff_against_original)}
                            </div>
                            <div>
                                <h5>Diff against expected:</h5>
                                {format_diff(diff.output_diff_against_expected)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        """

    # Generate sections for each category
    diff_sections = []
    if regressions:
        group_id = "regressions-group"
        diff_sections.append(f"""
            <div id="{group_id}" class="section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h2>Regressions ({len(regressions)} cases)</h2>
                    <div>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'expand')" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'collapse')">Collapse All</button>
                    </div>
                </div>
                {''.join(generate_diff_section(diff, f"reg-{i}") for i, diff in enumerate(regressions))}
            </div>
        """)
    if improvements:
        group_id = "improvements-group"
        diff_sections.append(f"""
            <div id="{group_id}" class="section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h2>Improvements ({len(improvements)} cases)</h2>
                    <div>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'expand')" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'collapse')">Collapse All</button>
                    </div>
                </div>
                {''.join(generate_diff_section(diff, f"imp-{i}") for i, diff in enumerate(improvements))}
            </div>
        """)
    if both_incorrect:
        group_id = "both-incorrect-group"
        diff_sections.append(f"""
            <div id="{group_id}" class="section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h2>Both Incorrect but Different ({len(both_incorrect)} cases)</h2>
                    <div>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'expand')" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="toggleGroup('{group_id}', 'collapse')">Collapse All</button>
                    </div>
                </div>
                {''.join(generate_diff_section(diff, f"both-{i}") for i, diff in enumerate(both_incorrect))}
            </div>
        """)

    # Generate final HTML
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Model Comparison - {comparison.results.model_name} vs {comparison.ref_results.model_name}</title>
        <style>
            {css}
            {COMMON_CSS}
            {collapsible_css}
        </style>
        {toggle_script}
    </head>
    <body>
        <div class="container">
            <h1>Model Comparison</h1>

            <div class="section">
                <h2>Detailed Results</h2>
                <p>
                    <a href="results.html">View detailed results for {comparison.results.model_name}</a><br>
                    <a href="ref_results.html">View detailed results for {comparison.ref_results.model_name}</a>
                </p>
            </div>

            <div class="section">
                <h2>Overall Statistics Comparison</h2>
                {comparison_table}
            </div>

            <div class="section">
                <h2>Category Statistics Comparison</h2>
                {category_table}
            </div>

            {''.join(diff_sections)}
        </div>
    </body>
    </html>
    """

    # Save the comparison HTML
    output_path.parent.mkdir(parents=True, exist_ok=True)
    output_path.write_text(html_content)


def main():
    parser = argparse.ArgumentParser(
        description="Generate HTML view for SmartPasteEvalOutput"
    )
    parser.add_argument(
        "input_file",
        type=str,
        help="Path to the input file containing SmartPasteEvalOutput",
    )
    parser.add_argument(
        "output_dir", type=str, help="Directory to save the HTML output"
    )

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Load the eval output
    with open(args.input_file, "r") as f:
        eval_output = json.load(f)

    # Generate HTML
    html_content = generate_eval_output_html(eval_output)

    # Save HTML file
    output_file = output_dir / f"{eval_output['request_id']}.html"
    with open(output_file, "w") as f:
        f.write(html_content)

    print(f"Generated HTML view at: {output_file}")


if __name__ == "__main__":
    main()


def test_generate_model_eval_html_sample_results(model_eval_results):
    """Test sample results in model evaluation HTML."""
    html = generate_model_eval_html(model_eval_results)

    # Debug print
    print("\nHTML output:")
    print(html)

    # Check sample results sections
    assert "onclick=\"toggleSection('result-uuid1')\"" in html
    assert "onclick=\"toggleSection('result-uuid2')\"" in html

    # Check first result (basic_eval_output)
    assert "status-correct" in html  # First result is correct
    assert '<span class="k">def</span>' in html  # Code from first result


def test_diff_formatting_basic():
    """Test basic diff formatting."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    diff_text = (
        "@@ -1,3 +1,3 @@\n"
        " def test():\n"
        "-    print('old')\n"
        "+    print('new')\n"
        " def other():\n"
    )

    formatted = format_diff(diff_text)

    # Check basic structure
    assert '<div class="diff">' in formatted
    assert "</div>" in formatted

    # Check that spans are inline with proper whitespace handling
    assert '<span class="diff-info">@@ -1,3 +1,3 @@</span>' in formatted
    assert (
        '<span class="diff-context"><span class="whitespace-space"></span>def<span class="whitespace-space"></span>test():</span>'
        in formatted
    )

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient\n", "from experimental.yury.smart_paste_eval.utils import (\n", "    evaluate_model_on_dataset,\n", "    compare_results,\n", ")\n", "\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "DEV_URL = \"https://dev-vpas.us-central.api.augmentcode.com\"\n", "PROD_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "DEV_RI_URL = (\n", "    \"https://support.dev-vpas.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval\n", "import experimental.yury.smart_paste_eval.data.combined_dataset as combined_dataset\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded dogfood_eval_data: {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval)\n", "print(f\"Loaded hard_eval: {len(hard_eval.DATA)} samples\")\n", "\n", "importlib.reload(combined_dataset)\n", "print(f\"Loaded combined_dataset: {len(combined_dataset.DATA)} samples\")\n", "\n", "\n", "REF_MODEL = \"forger-smart-paste-v2-qwen-14b-32k-edit\"\n", "MODEL = \"forger-v2-qwen-14b-q-32k-edit\"\n", "REF_URL = DEV_URL\n", "URL = DEV_URL\n", "\n", "DATASET = combined_dataset.DATA\n", "DATASET_NAME = \"combined_dataset\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref_results = evaluate_model_on_dataset(REF_MODEL, REF_URL, DATASET, API_TOKEN)\n", "results = evaluate_model_on_dataset(MODEL, URL, DATASET, API_TOKEN)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from experimental.yury.smart_paste_eval.claude_analysis import add_claude_analysis_model_eval\n", "\n", "# add_claude_analysis_model_eval(ref_results)\n", "# add_claude_analysis_model_eval(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comparison = compare_results(ref_results, results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from experimental.yury.smart_paste_eval.html_viewer import save_comparison_html\n", "\n", "WEB_SERVER_DIR = Path(\"/mnt/efs/augment/public_html\")\n", "REL_PATH = (\n", "    f\"vpas/smart_paste_eval/test/{DATASET_NAME}_{REF_MODEL}_{MODEL}/comparison.html\"\n", ")\n", "path = WEB_SERVER_DIR / REL_PATH\n", "save_comparison_html(comparison, path)\n", "\n", "URL_TEMPLATE = \"https://webserver.gcp-us1.r.augmentcode.com/{}\"\n", "URL_TEMPLATE.format(REL_PATH)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
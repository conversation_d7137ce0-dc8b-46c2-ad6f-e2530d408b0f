{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "from experimental.yury.smart_paste_eval.utils import run_smart_paste_prod\n", "from experimental.yury.smart_paste_eval.utils import compare_models\n", "from experimental.yury.smart_paste_eval.utils import evaluate_model_on_dataset\n", "from experimental.yury.smart_paste_eval.utils import compare_results\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "DEV_URL = \"https://dev-vpas.us-central.api.augmentcode.com\"\n", "PROD_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "DEV_RI_URL = (\n", "    \"https://support.dev-vpas.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", ")\n", "TIMEOUT = 1200\n", "\n", "dogfood_client = AugmentClient(\n", "    url=PROD_URL,\n", "    token=API_TOKEN,\n", ")\n", "augment_model_client = AugmentModelClient(\n", "    dogfood_client, \"forger-smart-paste-v2-qwen-8b-32k-edit\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded dogfood_eval_data: {len(dogfood_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_eval.utils import run_and_evaluate_smart_paste\n", "import json\n", "import dataclasses\n", "\n", "\n", "request_id = \"5adedf6b-7949-40ea-96ab-a70040001611\"\n", "input_sample, expected_outputs = next(\n", "    x for x in dogfood_eval_data.DATA if x[0].request_id == request_id\n", ")\n", "print(\n", "    f\"Sample: {input_sample.request_id} {input_sample.target_path} {len(expected_outputs)}\"\n", ")\n", "\n", "output = run_and_evaluate_smart_paste(\n", "    input_sample,\n", "    expected_outputs,\n", "    augment_model_client=augment_model_client,\n", ")\n", "\n", "print(f\"Correct: {output.correct}\")\n", "print(f\"Diff against expected:\\n{output.diff_against_expected}\\n\")\n", "print(f\"Diff against original:\\n{output.diff_against_original}\\n\")\n", "\n", "# Convert dataclass to dict first, then serialize to JSON\n", "output_dict = dataclasses.asdict(output)\n", "print(json.dumps(output_dict, indent=2, sort_keys=True))\n", "\n", "print(\"Add space in from of target_file_content\")\n", "input_sample = dataclasses.replace(\n", "    input_sample, target_file_content=\" \" + input_sample.target_file_content\n", ")\n", "output = run_and_evaluate_smart_paste(\n", "    input_sample,\n", "    expected_outputs,\n", "    augment_model_client=augment_model_client,\n", ")\n", "\n", "print(f\"Correct: {output.correct}\")\n", "print(f\"Diff against expected:\\n{output.diff_against_expected}\\n\")\n", "print(f\"Diff against original:\\n{output.diff_against_original}\\n\")\n", "output_dict = dataclasses.asdict(output)\n", "print(json.dumps(output_dict, indent=2, sort_keys=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_eval.utils import to_tokens_with_boundaries\n", "from research.core.str_diff import print_diff, line_diff\n", "\n", "not_working_tokens = [\n", "    151664,\n", "    11463,\n", "    10931,\n", "    59705,\n", "    7197,\n", "    198,\n", "    151680,\n", "    5399,\n", "    21749,\n", "    198,\n", "    151659,\n", "    198,\n", "    198,\n", "    750,\n", "    32256,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    481,\n", "    308,\n", "    271,\n", "    750,\n", "    30270,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    353,\n", "    308,\n", "    271,\n", "    750,\n", "    21749,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    608,\n", "    308,\n", "    198,\n", "    198,\n", "    151661,\n", "    198,\n", "    750,\n", "    32256,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    481,\n", "    308,\n", "    271,\n", "    750,\n", "    30270,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    353,\n", "    308,\n", "    198,\n", "    151660,\n", "    198,\n", "]\n", "working_tokens = [\n", "    151664,\n", "    11463,\n", "    10931,\n", "    59705,\n", "    7197,\n", "    198,\n", "    151680,\n", "    5399,\n", "    21749,\n", "    198,\n", "    151659,\n", "    198,\n", "    715,\n", "    750,\n", "    32256,\n", "    1255,\n", "    11,\n", "    308,\n", "    1648,\n", "    715,\n", "    262,\n", "    470,\n", "    296,\n", "    481,\n", "    308,\n", "    271,\n", "    750,\n", "    30270,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    353,\n", "    308,\n", "    271,\n", "    750,\n", "    21749,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    608,\n", "    308,\n", "    198,\n", "    198,\n", "    151661,\n", "    198,\n", "    750,\n", "    32256,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    481,\n", "    308,\n", "    271,\n", "    750,\n", "    30270,\n", "    1255,\n", "    11,\n", "    308,\n", "    982,\n", "    262,\n", "    470,\n", "    296,\n", "    353,\n", "    308,\n", "    198,\n", "    151660,\n", "    198,\n", "]\n", "\n", "# Convert tokens to strings first for diffing\n", "working_str = to_tokens_with_boundaries(working_tokens)\n", "not_working_str = to_tokens_with_boundaries(not_working_tokens)\n", "\n", "# Print the diff\n", "diff = line_diff(not_working_str, working_str)\n", "print_diff(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
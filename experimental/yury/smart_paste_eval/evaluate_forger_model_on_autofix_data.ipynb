{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import research.eval.harness.factories as factories\n", "from research.models import (\n", "    GenerationOptions,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from research.models.fastforward_models import StarCoder2_FastForward\n", "from base.prompt_format_smart_paste.forger_prompt_formatter import (\n", "    <PERSON>ger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ForgerSmartPasteTokenApportionment,\n", ")\n", "\n", "\n", "# MAX_PROMPT_BUDGET = 4096\n", "MAX_PROMPT_BUDGET = 16384\n", "MAX_GENERATED_TOKENS = MAX_PROMPT_BUDGET\n", "MAX_TOKEN_BUDGET = MAX_PROMPT_BUDGET + MAX_GENERATED_TOKENS\n", "\n", "\n", "config = {\n", "    \"model\": {\n", "        \"name\": \"starcoder2_fastforward\",\n", "        # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86645/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v12_8K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"edeac558f39b460a2a880ab86c6308c5cca166915645552506e8ebb93af41e18\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/815/trials/815/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"3ec9836aac3e44085a8cbc77b9f1cecf59329c56e3e48b8630239adc65723031\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/810/trials/810/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscne<PERSON><PERSON>_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"5b9d9586bdebab32f8fe69db30df5c073bc12b7fab21c3a945aa29e4b171ecb8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/867/trials/867/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_fixbs_ff\",\n", "        # \"checkpoint_hash\": \"ad4268ad0c404c5513ab221727fcb9e9082e6ae6c6e62b65fd973407eee7c8d8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/902/trials/902/overview\n", "        \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_32K_claude_and_naive_fixbs_mp2_bs128_ff\",\n", "        \"checkpoint_hash\": \"2841da84db20b2457598603b404ae21299d64fd5797204fbb8df2d8b44dea306\",\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": MAX_GENERATED_TOKENS,\n", "    },\n", "}\n", "\n", "generation_options = GenerationOptions(**config[\"generation_options\"])\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "token_apportionment = ForgerSmartPasteTokenApportionment(\n", "    path_len=256,\n", "    prefix_len=MAX_PROMPT_BUDGET,\n", "    suffix_len=MAX_PROMPT_BUDGET,\n", "    max_prompt_len=MAX_PROMPT_BUDGET,\n", "    message_len=128,\n", ")\n", "\n", "prompt_formatter = ForgerPromptFormatter(tokenizer, token_apportionment)\n", "\n", "model = StarCoder2_FastForward(\n", "    model_path=Path(config[\"model\"][\"model_path\"]),\n", "    checkpoint_sha256=config[\"model\"][\"checkpoint_hash\"],\n", ")\n", "# model = factories.create_model(config[\"model\"])\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.prompt_format.common import Exchange\n", "from base.prompt_format_chat.smart_paste_prompt_formatter import (\n", "    SmartPastePromptInput,\n", ")\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class SmartPasteEvalOutput:\n", "    response: str\n", "    predicted_file: str\n", "    diff_source_vs_predicted: str\n", "    diff_target_vs_predicted: str\n", "    diff_gold_vs_predicted: str\n", "    diff_source_vs_target: str\n", "    diff_source_vs_gold: str\n", "    same_as_target: bool\n", "    same_as_gold: bool\n", "    prompt_text: str | None = None\n", "\n", "\n", "class NotAFullFile(Exception):\n", "    \"\"\"Raised when the parts of modified code are not in the prompt.\"\"\"\n", "\n", "    def __init__(self, message: str = \"Input doesn't contain the full file.\"):\n", "        self.message = message\n", "        super().__init__(self.message)\n", "\n", "\n", "class PromptTooLong(Exception):\n", "    \"\"\"Raised when the prompt is too long.\"\"\"\n", "\n", "    def __init__(self, prompt_length: int, max_token_budget):\n", "        self.message = f\"Prompt too long: {prompt_length} > {max_token_budget}\"\n", "        super().__init__(self.message)\n", "\n", "\n", "def postprocess_model_response(row, predicted_file):\n", "    source_file = File(\n", "        path=row[\"path\"],\n", "        contents=row[\"source\"],\n", "    )\n", "\n", "    result = {\n", "        \"predicted_file\": predicted_file,\n", "    }\n", "    predicted_file = File(path=source_file.path, contents=predicted_file)\n", "\n", "    result.update(\n", "        {\n", "            \"diff_source_vs_predicted\": compute_file_diff(\n", "                source_file, predicted_file, use_smart_header=True, num_context_lines=5\n", "            ),\n", "        }\n", "    )\n", "\n", "    same_as_target = predicted_file.contents.rstrip() == row[\"target\"].rstrip()\n", "    target_file = File(path=source_file.path, contents=row[\"target\"])\n", "    diff_target_vs_predicted = compute_file_diff(\n", "        target_file, predicted_file, use_smart_header=True, num_context_lines=5\n", "    )\n", "    diff_source_vs_target = compute_file_diff(\n", "        source_file, target_file, use_smart_header=True, num_context_lines=5\n", "    )\n", "\n", "    same_as_gold = predicted_file.contents.rstrip() == row[\"gold\"].rstrip()\n", "    gold_file = File(path=source_file.path, contents=row[\"gold\"])\n", "    diff_gold_vs_predicted = compute_file_diff(\n", "        gold_file, predicted_file, use_smart_header=True, num_context_lines=5\n", "    )\n", "    diff_source_vs_gold = compute_file_diff(\n", "        source_file, gold_file, use_smart_header=True, num_context_lines=5\n", "    )\n", "\n", "    result.update(\n", "        {\n", "            \"diff_target_vs_predicted\": diff_target_vs_predicted,\n", "            \"diff_source_vs_target\": diff_source_vs_target,\n", "            \"same_as_target\": same_as_target,\n", "            \"diff_gold_vs_predicted\": diff_gold_vs_predicted,\n", "            \"same_as_gold\": same_as_gold,\n", "            \"diff_source_vs_gold\": diff_source_vs_gold,\n", "        }\n", "    )\n", "    return result\n", "\n", "\n", "def run_smart_paste(row) -> SmartPasteEvalOutput:\n", "    input_sample = SmartPastePromptInput(\n", "        path=row[\"path\"],\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        code_block=row[\"code_block\"],\n", "        suffix=\"\",\n", "        chat_history=[\n", "            Exchange(\n", "                request_message=row[\"instruction\"],\n", "                response_text=\"dummy response\",\n", "            )\n", "        ],\n", "        prefix_begin=0,\n", "        suffix_end=len(row[\"source\"]),\n", "        retrieved_chunks=[],\n", "        target_path=row[\"path\"],\n", "        target_file_content=row[\"source\"],\n", "    )\n", "\n", "    prompt_output = prompt_formatter.format_prompt(input_sample)\n", "    if len(prompt_output.tokens) > MAX_PROMPT_BUDGET:\n", "        raise PromptTooLong(len(prompt_output.tokens), MAX_PROMPT_BUDGET)\n", "\n", "    generated_text = model.raw_generate(prompt_output.tokens, generation_options)\n", "    predicted_file = (\n", "        prompt_output.unused_prefix + generated_text + prompt_output.unused_suffix\n", "    )\n", "\n", "    result = {\n", "        \"response\": generated_text,\n", "        \"prompt_text\": tokenizer.detokenize(prompt_output.tokens),\n", "    }\n", "    result.update(postprocess_model_response(row, predicted_file))\n", "    return SmartPasteEvalOutput(**result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "DATA_PATH = \"/mnt/efs/augment/user/yury/smart_paste/autofix.parquet\"\n", "\n", "df = pd.read_parquet(DATA_PATH)\n", "\n", "print(f\"Number of samples: {len(df)}\")\n", "\n", "df[\"instruction_length\"] = df[\"instruction\"].str.len()\n", "df[\"code_block_length\"] = df[\"code_block\"].str.len()\n", "df[\"source_length\"] = df[\"source\"].str.len()\n", "df[\"target_length\"] = df[\"target\"].str.len()\n", "df[\"gold_length\"] = df[\"gold\"].str.len()\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "n_total, n_same_as_target, n_same_as_gold = 0, 0, 0\n", "\n", "for index, row in df[1:21].iterrows():\n", "    print(f\"[Sample {index}]: {row['instruction']}\")\n", "    try:\n", "        output = run_smart_paste(row)\n", "        n_total += 1\n", "\n", "        if output.same_as_target:\n", "            n_same_as_target += 1\n", "            print(\"Same as target.\")\n", "        else:\n", "            print(\"Different from target.\")\n", "            print(output.diff_against_target)\n", "\n", "        if output.same_as_gold:\n", "            n_same_as_gold += 1\n", "            print(\"Same as gold.\")\n", "        else:\n", "            print(\"Different from gold.\")\n", "            print(output.diff_against_gold)\n", "\n", "    except NotAFullFile as e:\n", "        print(f\"Error: {e}\")\n", "    except PromptTooLong as e:\n", "        print(f\"Error: {e}\")\n", "\n", "print(f\"{n_same_as_target} out of {n_total} samples are the same as target.\")\n", "print(f\"{n_same_as_gold} out of {n_total} samples are the same as gold.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["row = df.iloc[20]\n", "\n", "print(row[\"instruction\"])\n", "print(row[\"code_block\"])\n", "\n", "output = run_smart_paste(row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output.diff_source_vs_gold)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output.diff_source_vs_target)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output.diff_source_vs_predicted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output.diff_target_vs_predicted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "from collections import Counter\n", "import os\n", "import uuid\n", "\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "URL = \"https://dev-yury.us-central.api.augmentcode.com\"\n", "RI_URL = \"https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "MODEL = \"forger-smart-paste-SC2-7B-8K-edit\"\n", "TIMEOUT = 1200\n", "\n", "pid = os.getpid()\n", "session_id = uuid.uuid5(uuid.NAMESPACE_OID, str(pid))\n", "\n", "dogfood_client = AugmentClient(\n", "    url=URL,\n", "    token=API_TOKEN,\n", "    timeout=TIMEOUT,\n", "    retry_count=0,\n", "    session_id=session_id,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, MODEL)\n", "\n", "\n", "def run_smart_paste_prod(row) -> SmartPasteEvalOutput:\n", "    response = []\n", "    for response_chunks in model_client_dogfood.smart_paste_stream(\n", "        selected_text=\"\",\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        path=\"\",\n", "        chat_history=[\n", "            {\n", "                \"request_message\": row[\"instruction\"],\n", "                \"response_text\": \"not used\",\n", "            }\n", "        ],\n", "        code_block=row[\"code_block\"],\n", "        target_file_path=row[\"path\"],\n", "        target_file_content=row[\"source\"],\n", "        timeout=TIMEOUT,\n", "    ):\n", "        for response_chunk in response_chunks.splitlines():\n", "            replacement_text = json.loads(response_chunk)[\"replacement_text\"]\n", "            if replacement_text is not None:\n", "                response.append(replacement_text)\n", "\n", "    response = \"\".join(response)\n", "\n", "    result = {\n", "        \"response\": response,\n", "    }\n", "    result.update(postprocess_model_response(row, response))\n", "    return SmartPasteEvalOutput(**result)\n", "\n", "\n", "prod_prediction = run_smart_paste_prod(row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "(\n", "    n_total,\n", "    n_same_as_target,\n", "    n_same_as_gold,\n", "    n_same_as_gold_and_target,\n", "    n_same_as_gold_or_target,\n", ") = 0, 0, 0, 0, 0\n", "\n", "for index, row in df.iterrows():\n", "    print(f\"[Sample {index}]: {row['instruction']}\")\n", "    try:\n", "        output = run_smart_paste_prod(row)\n", "        n_total += 1\n", "\n", "        if output.same_as_target:\n", "            n_same_as_target += 1\n", "            print(\"Same as target.\")\n", "        else:\n", "            print(\"Different from target.\")\n", "            print(output.diff_target_vs_predicted)\n", "\n", "        if output.same_as_gold:\n", "            n_same_as_gold += 1\n", "            print(\"Same as gold.\")\n", "        else:\n", "            print(\"Different from gold.\")\n", "            # print(output.diff_against_gold)\n", "\n", "        n_same_as_gold_and_target += int(output.same_as_gold and output.same_as_target)\n", "        n_same_as_gold_or_target += int(output.same_as_gold or output.same_as_target)\n", "\n", "    except NotAFullFile as e:\n", "        print(f\"Error: {e}\")\n", "    except PromptTooLong as e:\n", "        print(f\"Error: {e}\")\n", "\n", "print(f\"{n_same_as_target} out of {n_total} samples are the same as target.\")\n", "print(f\"{n_same_as_gold} out of {n_total} samples are the same as gold.\")\n", "print(\n", "    f\"{n_same_as_gold_and_target} out of {n_total} samples are the same as gold and target.\"\n", ")\n", "print(\n", "    f\"{n_same_as_gold_or_target} out of {n_total} samples are the same as gold or target.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"{n_same_as_gold_and_target} out of {n_total} samples are the same as gold and target.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
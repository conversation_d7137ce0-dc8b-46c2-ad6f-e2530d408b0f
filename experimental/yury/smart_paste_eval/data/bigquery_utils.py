from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds
import experimental.yury.data.processing as utils


PROJECT_ID = "system-services-prod"


@utils.persistent_cache(
    "/mnt/efs/augment/user/yury/smart_paste_eval/download_request_per_tenant.jsonl"
)
def download_request_using_bq(request_id: str):
    query = f"""SELECT
                *
            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`
            WHERE request_id = '{request_id}'"""

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)
    print("sending request to big query")
    rows = bigquery_client.query_and_wait(query, page_size=1)
    rows = list(rows)
    assert len(rows) == 1
    row = rows[0]
    assert row.request_id == request_id
    return row.raw_json


@utils.persistent_cache(
    "/mnt/efs/augment/user/yury/smart_paste_eval/download_response.jsonl"
)
def download_response(request_id: str) -> bigquery.Row:
    query = f"""SELECT
                *
            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`
            WHERE request_id = '{request_id}'"""
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)
    print("sending request to big query")
    rows = bigquery_client.query_and_wait(query, page_size=1)
    rows = list(rows)
    assert len(rows) == 1
    row = rows[0]
    assert row.request_id == request_id
    return row.raw_json["response"]


@utils.persistent_cache(
    "/mnt/efs/augment/user/yury/smart_paste_eval/download_response.jsonl"
)
def get_edit_request_ids(
    tenant_name: str,
    user_id: str,
    start_timestamp: int,
    end_timestamp: int,
    limit: int = 100,
) -> list[str]:
    query = f"""
        SELECT request_id
        FROM `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.request_metadata`
        WHERE user_id = '{user_id}'
        AND request_type = 'EDIT'
        AND tenant = '{tenant_name}'
        AND time > TIMESTAMP_SECONDS({start_timestamp})
        AND time <= TIMESTAMP_SECONDS({end_timestamp})
        ORDER BY time
        LIMIT {limit}
    """

    print(query)

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)
    rows = bigquery_client.query_and_wait(query)
    request_ids = [row.request_id for row in rows]
    print(f"Found {len(request_ids)} edit requests for user {user_id}")
    return request_ids

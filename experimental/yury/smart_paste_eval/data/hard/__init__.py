from pathlib import Path

from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample

DATA_PATH = "/mnt/efs/augment/user/yury/smart_paste/eval/hard_eval_samples.jsonl"
OUTPUT_DIR = Path(__file__).parent / "expected_outputs"

DATA = []

with open(DATA_PATH, "r") as f:
    for line in f:
        sample = EvalSample.from_json(line)
        sample.read_expected_outputs(OUTPUT_DIR)
        sample.category = "hard"
        DATA.append(sample)

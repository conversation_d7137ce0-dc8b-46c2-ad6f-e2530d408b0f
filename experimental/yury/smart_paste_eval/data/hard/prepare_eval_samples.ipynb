{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["94946it [00:16, 5814.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Prepared 94946 requests\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import uuid\n", "import tqdm\n", "import json\n", "from base.prompt_format.common import Exchange\n", "from experimental.yury.smart_paste_eval.data.utils import (\n", "    SmartPastePromptInputWithRID,\n", ")\n", "\n", "\n", "PATH = \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl\"\n", "\n", "\n", "requests, raw_requests = {}, {}\n", "\n", "with open(PATH, \"r\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        sample = json.loads(line)\n", "\n", "        before_file_contents = sample[\"before_file\"][\"contents\"]\n", "\n", "        prefix = before_file_contents[: sample[\"selected_code_crange\"][\"start\"]]\n", "        selected_code = before_file_contents[\n", "            sample[\"selected_code_crange\"][\"start\"] : sample[\"selected_code_crange\"][\n", "                \"stop\"\n", "            ]\n", "        ]\n", "        suffix = before_file_contents[sample[\"selected_code_crange\"][\"stop\"] :]\n", "        if len(selected_code) > 0 and selected_code.endswith(\"\\n\"):\n", "            suffix = selected_code[-1] + suffix\n", "            selected_code = selected_code[:-1]\n", "\n", "        previous_exchange_id = str(uuid.uuid4())\n", "\n", "        requests[sample[\"uuid\"]] = SmartPastePromptInputWithRID(\n", "            request_id=sample[\"uuid\"],\n", "            path=sample[\"before_file\"][\"path\"],\n", "            prefix=prefix,\n", "            selected_code=selected_code,\n", "            suffix=suffix,\n", "            chat_history=[\n", "                Exchange(\n", "                    request_message=sample[\"instruction\"],\n", "                    response_text=sample[\"chat_response\"],\n", "                    request_id=previous_exchange_id,\n", "                )\n", "            ],\n", "            prefix_begin=0,\n", "            suffix_end=len(before_file_contents),\n", "            target_path=sample[\"before_file\"][\"path\"],\n", "            target_file_content=before_file_contents,\n", "            context_code_exchange_request_id=previous_exchange_id,\n", "            code_block=sample[\"codeblock\"],\n", "            retrieved_chunks=[],\n", "        )\n", "        raw_requests[sample[\"uuid\"]] = sample\n", "\n", "\n", "print(f\"Prepared {len(requests)} requests\")"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 28 samples\n", "Loaded 28 samples\n"]}], "source": ["import json\n", "import dataclasses\n", "from pathlib import Path\n", "\n", "SAMPLES_TO_SAVE = [\n", "    # Samples where <PERSON><PERSON> gives different predictions from <PERSON>.\n", "    \"ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8\",\n", "    \"4100aeef-5bae-4dfc-a305-fb5b94a7cdd9\",\n", "    \"5d817527-f127-4796-bf67-1c58a4adf91e\",\n", "    \"0882477e-ff56-48da-b270-8db51392b7e6\",\n", "    \"f71d825f-36cd-4acc-907f-7c768c2e8110\",\n", "    \"ca6251aa-8254-403b-b6e6-095284e12564\",\n", "    \"3da93f44-384e-4e15-a522-e5362925bd8f\",\n", "    \"7427914c-af3c-4714-9564-a7910ed82c2f\",\n", "    \"7bac4af4-04be-44a8-a275-2e40a16fc121\",\n", "    \"2a1bca8f-915d-4eb1-b056-cf55be961468\",\n", "    \"86030f72-55e6-4b36-a354-379ab082658c\",\n", "    \"311c0743-29de-47eb-8e48-531b631a8f5b\",\n", "    \"6326931a-6da4-4ff2-8ae3-078d1ca5ee46\",\n", "    \"474b219f-f42a-4377-9c82-186c9f1c627b\",\n", "\n", "    # Samples where LLama-70B gives different predictions from <PERSON>.\n", "    \"747724da-b8bf-495b-8bd0-c93d31a02107\",\n", "    \"27be30fc-95ce-414b-8885-e50e70012d46\",\n", "    \"7eb5cda6-ae65-40b7-a013-59be4cb28b36\",\n", "    \"57baf6ba-978d-4e7f-89a6-74db8b7a7da6\",\n", "    \"7406938a-10cb-49c4-9c8a-3cc95a77fb9b\",\n", "    \"9b9e7f17-0d92-4ea8-b9b9-1fd5334a80d7\",\n", "    \"e36030b7-7bc0-4271-a948-9d44b4ff8707\",\n", "    \"bf5c2a5a-7781-40f4-8016-71f56e2c7aac\",\n", "    \"23549dac-9b52-46ef-9cec-c73deb4f2e95\",\n", "    \"5a8c6dfd-27c8-4a6d-a197-2ebd025d2e9a\",\n", "    \"eae31dc9-c918-49c6-b528-c1098648290c\",\n", "    \"a45df3a5-d1bc-40db-954c-ed800117141c\",\n", "    \"99a6d00a-cc93-4af8-9ae5-8b0b4e3dd225\",\n", "    \"cba11857-2664-442f-b004-fbee5e7ff164\",\n", "    # Samples where we initially failed to parse codeblock from Llama-70B response.\n", "    # Need to update the codeblock, otherwise we are not evaluating anything.\n", "    # \"4e3c4c07-0e83-470f-a3fe-ec53bb9a690c\",\n", "    # \"d5eeede1-465a-4109-8707-50d48eec33a6\",\n", "    # \"2505f63e-24af-4700-a0ca-8c90cc2cc2bc\",\n", "    # \"3a48ed87-fda5-4a51-ba61-1e5e23310a65\",\n", "    # \"7adc2bde-1095-419f-9fe1-5bdf8d60b453\",\n", "    # \"04cc7af1-5698-4c42-882f-e66b49919f7d\",\n", "    # \"4876e936-6bbc-4551-a3f0-c2a6d0bdad18\",\n", "    # \"7bb7287d-6d79-4722-9778-e706b4cf43e0\",\n", "    # \"cd56cf69-8d9b-47b7-829f-e6e414ccd487\",\n", "]\n", "\n", "OUTPUT_PATH = Path(\n", "    \"/home/<USER>/augment/experimental/yury/smart_paste_eval/data/hard/raw_data.jsonl\"\n", ")\n", "\n", "with OUTPUT_PATH.open(\"w\") as f:\n", "    for key in SAMPLES_TO_SAVE:\n", "        json.dump(dataclasses.asdict(requests[key]), f)\n", "        f.write(\"\\n\")\n", "\n", "\n", "hard_data = [json.loads(line) for line in Path(OUTPUT_PATH).open(\"r\")]\n", "print(f\"Saved {len(hard_data)} samples\")\n", "\n", "import importlib\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "\n", "print(f\"Loaded {len(hard_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "llama_predictions = []\n", "\n", "with Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.with_llama.jsonl\"\n", ").open(\"r\") as f:\n", "    for line in f:\n", "        try:\n", "            datum = json.loads(line)\n", "        except json.decoder.JSONDecodeError:\n", "            continue\n", "        if datum[\"value\"][\"status\"] != \"no_codeblock_in_response\":\n", "            continue\n", "        llama_predictions.append(datum)\n", "\n", "print(f\"Loaded {len(llama_predictions)} Llama predictions.\")"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update the inspect function to use v2 secrets API with new_v2_client and unmunchify for output formatting.\n", "Certainly! I'll update the `inspect_secret` function to use the v2 secrets API with `new_v2_client` and `unmunchify` for output formatting. Here's the modified code:\n", "\n", "```python path=riocli/secret/inspect.py mode=EDIT\n", "@click.command(\n", "    'inspect',\n", "    cls=HelpColorsCommand,\n", "    help_headers_color=Colors.YELLOW,\n", "    help_options_color=Colors.GREEN,\n", ")\n", "@click.option('--format', '-f', 'format_type', default='yaml',\n", "              type=click.Choice(['json', 'yaml'], case_sensitive=False))\n", "@click.argument('secret-name', type=str)\n", "@name_to_guid\n", "def inspect_secret(format_type: str, secret_name: str, secret_guid: str) -> None:\n", "    \"\"\"\n", "    Inspect a secret\n", "    \"\"\"\n", "    try:\n", "        client = new_v2_client()\n", "        secret = client.secrets.get(secret_guid)\n", "        data = unmunchify(secret)\n", "        inspect_with_format(data, format_type)\n", "    except Exception as e:\n", "        click.secho(str(e), fg=Colors.RED)\n", "        raise SystemExit(1)\n", "\n", "# Remove the make_secret_inspectable function as it's no longer needed\n", "```\n", "\n", "Here are the changes made:\n", "\n", "1. Changed `new_client()` to `new_v2_client()` to use the v2 API.\n", "2. Updated the secret retrieval to use `client.secrets.get(secret_guid)`.\n", "3. Used `unmunchify(secret)` to convert the Munch object to a regular dictionary for output formatting.\n", "4. Removed the `make_secret_inspectable` function as it's no longer needed. The `unmunchify` function will handle the conversion of the secret object to a dictionary.\n", "\n", "You'll need to make sure to import the necessary functions at the top of the file:\n", "\n", "```python path=riocli/secret/inspect.py mode=EDIT\n", "from riocli.config import new_v2_client\n", "from riocli.utils import inspect_with_format, unmunchify\n", "```\n", "\n", "These changes will update the `inspect_secret` function to use the v2 secrets API and properly format the output using `unmunchify`.\n", "==============================\n", "from riocli.config import new_v2_client\n", "from riocli.utils import inspect_with_format, unmunchify\n", "\n"]}], "source": ["# llama_prediction = llama_predictions[-14]\n", "# request_id = llama_prediction[\"key\"]\n", "# print(request_id, llama_prediction[\"value\"][\"status\"])\n", "# print(llama_prediction[\"value\"][\"text\"])\n", "\n", "request_id = \"2a1bca8f-915d-4eb1-b056-cf55be961468\"\n", "expected_output_path = hard_eval_data.OUTPUT_DIR / f\"{request_id}.txt\"\n", "if not expected_output_path.exists():\n", "    with open(hard_eval_data.OUTPUT_DIR / f\"{request_id}.txt\", \"w\") as f:\n", "        f.write(requests[request_id].target_file_content)\n", "\n", "print(requests[request_id].chat_history[-1].request_message)\n", "print(requests[request_id].chat_history[-1].response_text)\n", "print(\"=\" * 30)\n", "print(requests[request_id].code_block)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Copyright 2023 Rapyuta Robotics\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n", "import click\n", "from click_help_colors import HelpColorsCommand\n", "from rapyuta_io import Secret\n", "\n", "from riocli.config import new_client\n", "from riocli.constants import Colors\n", "from riocli.secret.util import name_to_guid\n", "from riocli.utils import inspect_with_format\n", "\n", "\n", "@click.command(\n", "    'inspect',\n", "    cls=HelpColorsCommand,\n", "    help_headers_color=Colors.YELLOW,\n", "    help_options_color=Colors.GREEN,\n", ")\n", "@click.option('--format', '-f', 'format_type', default='yaml',\n", "              type=click.Choice(['json', 'yaml'], case_sensitive=False))\n", "@click.argument('secret-name', type=str)\n", "@name_to_guid\n", "def inspect_secret(format_type: str, secret_name: str, secret_guid: str) -> None:\n", "    \"\"\"\n", "    Inspect a secret\n", "    \"\"\"\n", "    try:\n", "        client = new_client()\n", "        secret = client.get_secret(secret_guid)\n", "        data = make_secret_inspectable(secret)\n", "        inspect_with_format(data, format_type)\n", "    except Exception as e:\n", "        click.secho(str(e), fg=Colors.RED)\n", "        raise SystemExit(1)\n", "\n", "\n", "def make_secret_inspectable(obj: Secret) -> dict:\n", "    return {\n", "        'created_at': obj.created_at,\n", "        'creator': obj.creator,\n", "        'guid': obj.guid,\n", "        'name': obj.name,\n", "        'project': obj.project_guid,\n", "        'secret_type': obj.secret_type.value,\n", "    }\n", "\n"]}], "source": ["print(requests[request_id].target_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "n_correct = 0\n", "for input_sample, expected_outputs in hard_eval_data.DATA:\n", "    print(\n", "        f\"Sample: {input_sample.request_id} {input_sample.target_path} {len(expected_outputs)}\"\n", "    )\n", "\n", "    predicted_after_file = File(\n", "        raw_requests[input_sample.request_id][\"after_file\"][\"path\"],\n", "        raw_requests[input_sample.request_id][\"after_file\"][\"contents\"],\n", "    )\n", "\n", "    expected_output = None\n", "    for expected_output in expected_outputs:\n", "        correct = predicted_after_file.contents.strip() == expected_output.strip()\n", "        if correct:\n", "            # pick the best matching label\n", "            break\n", "    if expected_output is None:\n", "        # Pick the first one\n", "        expected_output = expected_outputs[0]\n", "\n", "    correct = predicted_after_file.contents.strip() == expected_output.strip()\n", "    diff_against_expected = compute_file_diff(\n", "        File(path=input_sample.target_path, contents=expected_output),\n", "        predicted_after_file,\n", "        use_smart_header=True,\n", "        num_context_lines=5,\n", "    )\n", "\n", "    if correct:\n", "        print(\"Correct\")\n", "        n_correct += 1\n", "    else:\n", "        print(\"Incorrect\")\n", "        print(diff_against_expected)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["94939 94946\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "Loaded 206 samples\n"]}], "source": ["from megatron.data.indexed_dataset import make_dataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "path_instruction_to_uuid = {}\n", "for request_id, raw_request in raw_requests.items():\n", "    path_instruction_to_uuid[\n", "        (raw_request[\"before_file\"][\"path\"], raw_request[\"instruction\"])\n", "    ] = request_id\n", "print(len(path_instruction_to_uuid), len(raw_requests))\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "# DATA_PATH = \"/mnt/efs/augment/user/yury/smart_paste/data/v13_2_sc2_fixscnewline_16K/valid_claude\"\n", "DATA_PATH = \"/mnt/efs/augment/user/yury/smart_paste/data/v13_2_sc2_fixscnewline_16K/valid_claude_emptysc\"\n", "\n", "ds = make_dataset(DATA_PATH, impl=\"mmap\", skip_warmup=True)\n", "print(f\"Loaded {len(ds)} samples\")\n", "\n", "\n", "def process_sample(sample):\n", "    tokens = sample\n", "    tokens = [x for x in tokens if x != -tokenizer.special_tokens.padding]\n", "    prompt = [-x for x in tokens if x < 0]\n", "    label = [x for x in tokens if x >= 0]\n", "    assert label[-1] == tokenizer.special_tokens.eos\n", "    label = label[:-1]\n", "    return tokenizer.detok<PERSON>ze(prompt), tokenizer.detokenize(label)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8'"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt, label = process_sample(ds[52])\n", "path, instruction = prompt.splitlines()[:2]\n", "assert path.startswith(\"<file_sep>\")\n", "path = path[len(\"<file_sep>\") :]\n", "assert instruction.startswith(\"<pr>\")\n", "instruction = instruction[len(\"<pr>\") :]\n", "path_instruction_to_uuid[(path, instruction)]"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample 7eb5cda6-ae65-40b7-a013-59be4cb28b36\n", "Incorrect\n", "Sample 27be30fc-95ce-414b-8885-e50e70012d46\n", "Incorrect\n", "Sample 747724da-b8bf-495b-8bd0-c93d31a02107\n", "Incorrect\n", "Sample 57baf6ba-978d-4e7f-89a6-74db8b7a7da6\n", "Incorrect\n", "Sample 7406938a-10cb-49c4-9c8a-3cc95a77fb9b\n", "Incorrect\n", "Sample 9b9e7f17-0d92-4ea8-b9b9-1fd5334a80d7\n", "Incorrect\n", "Sample e36030b7-7bc0-4271-a948-9d44b4ff8707\n", "Incorrect\n", "Sample bf5c2a5a-7781-40f4-8016-71f56e2c7aac\n", "Incorrect\n", "Sample 23549dac-9b52-46ef-9cec-c73deb4f2e95\n", "Incorrect\n", "Sample 5a8c6dfd-27c8-4a6d-a197-2ebd025d2e9a\n", "Incorrect\n", "Sample eae31dc9-c918-49c6-b528-c1098648290c\n", "Incorrect\n", "Sample a45df3a5-d1bc-40db-954c-ed800117141c\n", "Incorrect\n", "Sample 99a6d00a-cc93-4af8-9ae5-8b0b4e3dd225\n", "Incorrect\n", "Sample cba11857-2664-442f-b004-fbee5e7ff164\n", "Incorrect\n", "Sample 474b219f-f42a-4377-9c82-186c9f1c627b\n", "Incorrect\n", "Sample 6326931a-6da4-4ff2-8ae3-078d1ca5ee46\n", "Incorrect\n", "Sample 311c0743-29de-47eb-8e48-531b631a8f5b\n", "Incorrect\n", "Sample 86030f72-55e6-4b36-a354-379ab082658c\n", "Incorrect\n", "Sample 2a1bca8f-915d-4eb1-b056-cf55be961468\n", "Incorrect\n", "Sample 7bac4af4-04be-44a8-a275-2e40a16fc121\n", "Incorrect\n", "Sample 7427914c-af3c-4714-9564-a7910ed82c2f\n", "Incorrect\n", "Sample 3da93f44-384e-4e15-a522-e5362925bd8f\n", "Incorrect\n", "Sample ca6251aa-8254-403b-b6e6-095284e12564\n", "Incorrect\n", "Sample f71d825f-36cd-4acc-907f-7c768c2e8110\n", "Incorrect\n", "Sample 0882477e-ff56-48da-b270-8db51392b7e6\n", "Incorrect\n", "Sample 5d817527-f127-4796-bf67-1c58a4adf91e\n", "Incorrect\n", "Sample 4100aeef-5bae-4dfc-a305-fb5b94a7cdd9\n", "Incorrect\n", "Sample ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8\n", "Incorrect\n"]}], "source": ["# Check when the original commit is the same as the label\n", "\n", "import importlib\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "hard_data_dict = {sample.request_id: (sample, expected_outputs) for sample, expected_outputs in hard_eval_data.DATA}\n", "\n", "REQUEST_IDS_FROM_SPREADSHEET = [\n", "\t\"7eb5cda6-ae65-40b7-a013-59be4cb28b36\",\n", "\t\"27be30fc-95ce-414b-8885-e50e70012d46\",\n", "\t\"747724da-b8bf-495b-8bd0-c93d31a02107\",\n", "\t\"57baf6ba-978d-4e7f-89a6-74db8b7a7da6\",\n", "\t\"7406938a-10cb-49c4-9c8a-3cc95a77fb9b\",\n", "\t\"9b9e7f17-0d92-4ea8-b9b9-1fd5334a80d7\",\n", "\t\"e36030b7-7bc0-4271-a948-9d44b4ff8707\",\n", "\t\"bf5c2a5a-7781-40f4-8016-71f56e2c7aac\",\n", "\t\"23549dac-9b52-46ef-9cec-c73deb4f2e95\",\n", "\t\"5a8c6dfd-27c8-4a6d-a197-2ebd025d2e9a\",\n", "\t\"eae31dc9-c918-49c6-b528-c1098648290c\",\n", "\t\"a45df3a5-d1bc-40db-954c-ed800117141c\",\n", "\t\"99a6d00a-cc93-4af8-9ae5-8b0b4e3dd225\",\n", "\t\"cba11857-2664-442f-b004-fbee5e7ff164\",\n", "\t\"474b219f-f42a-4377-9c82-186c9f1c627b\",\n", "\t\"6326931a-6da4-4ff2-8ae3-078d1ca5ee46\",\n", "\t\"311c0743-29de-47eb-8e48-531b631a8f5b\",\n", "\t\"86030f72-55e6-4b36-a354-379ab082658c\",\n", "\t\"2a1bca8f-915d-4eb1-b056-cf55be961468\",\n", "\t\"7bac4af4-04be-44a8-a275-2e40a16fc121\",\n", "\t\"7427914c-af3c-4714-9564-a7910ed82c2f\",\n", "\t\"3da93f44-384e-4e15-a522-e5362925bd8f\",\n", "\t\"ca6251aa-8254-403b-b6e6-095284e12564\",\n", "\t\"f71d825f-36cd-4acc-907f-7c768c2e8110\",\n", "\t\"0882477e-ff56-48da-b270-8db51392b7e6\",\n", "\t\"5d817527-f127-4796-bf67-1c58a4adf91e\",\n", "\t\"4100aeef-5bae-4dfc-a305-fb5b94a7cdd9\",\n", "\t\"ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8\",\n", "]\n", "\n", "for request_id in REQUEST_IDS_FROM_SPREADSHEET:\n", "    print(f\"Sample {request_id}\")\n", "\n", "    sample, expected_outputs = hard_data_dict[request_id]\n", "\n", "    actual_after_file = File(**raw_requests[request_id][\"aux\"][\"original_commit_after_file\"])\n", "\n", "    expected_output = None\n", "    for expected_output in expected_outputs:\n", "        if actual_after_file.contents.strip() == expected_output.strip():\n", "            # pick the best matching label\n", "            break\n", "\n", "    if expected_output is None and len(expected_outputs) > 0:\n", "        # Pick the first one\n", "        expected_output = expected_outputs[0]\n", "\n", "    assert expected_output is not None\n", "\n", "    correct = actual_after_file.contents.strip() == expected_output.strip()\n", "    if correct:\n", "        print(\"Correct\")\n", "    else:\n", "        print(\"Incorrect\")\n", "        expected_file = File(path=actual_after_file.path, contents=expected_output)\n", "        diff_against_expected = compute_file_diff(\n", "            expected_file, actual_after_file, use_smart_header=True, num_context_lines=5\n", "        )\n", "        # print(diff_against_expected)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample 7eb5cda6-ae65-40b7-a013-59be4cb28b36\n", "Correct\n", "Sample 27be30fc-95ce-414b-8885-e50e70012d46\n", "Correct\n", "Sample 747724da-b8bf-495b-8bd0-c93d31a02107\n", "Correct\n", "Sample 57baf6ba-978d-4e7f-89a6-74db8b7a7da6\n", "WRONG\n", "--- microsite/static/css/custom.css\n", "+++ microsite/static/css/custom.css\n", "@@ -256,12 +256,12 @@\n", "@@media only screen and (max-width: 1023px) {\n", "   .docsNavContainer {\n", "     position: unset;\n", "     width: 95vw;\n", "     z-index: 100;\n", "     margin-left: -2vw;\n", "-    background-color: #1a1a1a;\n", "-    padding: 1rem;\n", "+    background-color: #1a1a1a; /* Added background color */\n", "+    padding: 1rem; /* Added padding */\n", "   }\n", " \n", "   /* Toc bar does not have to be fixed when slider is active */\n", "   .docsSliderActive .toc .navBreadcrumb,\n", "   .tocActive .navBreadcrumb {\n", "\n", "Sample 7406938a-10cb-49c4-9c8a-3cc95a77fb9b\n", "Correct\n", "Sample 9b9e7f17-0d92-4ea8-b9b9-1fd5334a80d7\n", "Correct\n", "Sample e36030b7-7bc0-4271-a948-9d44b4ff8707\n", "Correct\n", "Sample bf5c2a5a-7781-40f4-8016-71f56e2c7aac\n", "Correct\n", "Sample 23549dac-9b52-46ef-9cec-c73deb4f2e95\n", "WRONG\n", "--- super_editor/test/super_reader/reader_test_tools.dart\n", "+++ super_editor/test/super_reader/reader_test_tools.dart\n", "@@ -89,15 +89,15 @@\n", "@class TestDocumentConfigurator {\n", "   bool _autoFocus = false;\n", "   ui.<PERSON><PERSON>? _editorSize;\n", "   List<ComponentBuilder>? _componentBuilders;\n", "   WidgetTreeBuilder? _widgetTreeBuilder;\n", "   ScrollController? _scrollController;\n", "+  bool _insideCustomScrollView = false;\n", "   FocusNode? _focusNode;\n", "   DocumentSelection? _selection;\n", "   WidgetBuilder? _androidToolbarBuilder;\n", "   DocumentFloatingToolbarBuilder? _iOSToolbarBuilder;\n", "-  bool _insideCustomScrollView = false;\n", " \n", "   /// Configures the [SuperReader] for standard desktop interactions,\n", "   /// e.g., mouse and keyboard input.\n", "   TestDocumentConfigurator forDesktop({\n", "     TextInputSource inputSource = TextInputSource.keyboard,\n", "@@ -157,10 +157,16 @@\n", "@class TestDocumentConfigurator {\n", "   }\n", " \n", "   /// Configures the [SuperReader] to use the given [selection] as its initial selection.\n", "   TestDocumentConfigurator withSelection(DocumentSelection? selection) {\n", "     _selection = selection;\n", "+    return this;\n", "+  }\n", "+\n", "+  /// Configures the [SuperReader] to be placed inside a [CustomScrollView].\n", "+  TestDocumentConfigurator insideCustomScrollView() {\n", "+    _insideCustomScrollView = true;\n", "     return this;\n", "   }\n", " \n", "   DocumentGestureMode get _defaultGestureMode {\n", "     switch (debugDefaultTargetPlatformOverride) {\n", "@@ -272,16 +278,10 @@\n", "@class TestDocumentConfigurator {\n", "@  Future<TestDocumentContext> pump() async {\n", "     );\n", " \n", "     return testContext;\n", "   }\n", " \n", "-  /// Configures the [SuperReader] to be placed inside a [CustomScrollView].\n", "-  TestDocumentConfigurator insideCustomScrollView() {\n", "-    _insideCustomScrollView = true;\n", "-    return this;\n", "-  }\n", "-\n", "   Widget _buildContent(Widget superReader) {\n", "     Widget content = superReader;\n", " \n", "     if (_editorSize != null) {\n", "       content = ConstrainedBox(\n", "\n", "Sample 5a8c6dfd-27c8-4a6d-a197-2ebd025d2e9a\n", "WRONG\n", "--- src/components/postView/view/postDisplayView.tsx\n", "+++ src/components/postView/view/postDisplayView.tsx\n", "@@ -1,9 +1,14 @@\n", " import React, { useCallback, useEffect, useRef, useState, Fragment } from 'react';\n", " import { View, Text } from 'react-native';\n", " import { injectIntl } from 'react-intl';\n", " import get from 'lodash/get';\n", "+\n", "+// Add this function at the top of the file, outside the component\n", "+const capitalize = (str: string) => {\n", "+  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n", "+};\n", " \n", " // Providers\n", " import { useSafeAreaInsets } from 'react-native-safe-area-context';\n", " \n", " // Utils\n", "@@ -29,14 +34,10 @@\n", " import { UpvoteButton } from '../../postCard/children/upvoteButton';\n", " import UpvotePopover from '../../upvotePopover';\n", " \n", " const HEIGHT = getWindowDimensions().height;\n", " const WIDTH = getWindowDimensions().width;\n", "-\n", "-const capitalize = (str: string) => {\n", "-  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n", "-};\n", " \n", " const PostDisplayView = ({\n", "   currentAccount,\n", "   isLoggedIn,\n", "   isNewPost,\n", "\n", "Sample eae31dc9-c918-49c6-b528-c1098648290c\n", "Correct\n", "Sample a45df3a5-d1bc-40db-954c-ed800117141c\n", "WRONG\n", "--- include/FramelessHelper/Core/framelesshelper_windows.h\n", "+++ include/FramelessHelper/Core/framelesshelper_windows.h\n", "@@ -50,10 +50,18 @@\n", " \n", " #ifndef NOMINMAX\n", " #  define NOMINMAX\n", " #endif\n", " \n", "+#ifndef WINMMAPI\n", "+#  if defined(_WIN32) && !defined(_WIN64)\n", "+#    define WINMMAPI WINGDIAPI\n", "+#  else\n", "+#    define WINMMAPI\n", "+#  endif\n", "+#endif\n", "+\n", " #include <sdkddkver.h>\n", " \n", " #ifndef _WIN32_WINNT_WIN10\n", " #  define _WIN32_WINNT_WIN10 0x0A00\n", " #endif\n", "@@ -153,18 +161,10 @@\n", " #  define DPI_AWARENESS_CONTEXT_UNAWARE_GDISCALED ((DPI_AWARENESS_CONTEXT)-5)\n", " #endif\n", " \n", " #ifndef STATUS_SUCCESS\n", " #  define STATUS_SUCCESS (static_cast<NTSTATUS>(0x00000000L))\n", "-#endif\n", "-\n", "-#ifndef WINMMAPI\n", "-#  if defined(_WIN32) && !defined(_WIN64)\n", "-#    define WINMMAPI WINGDIAPI\n", "-#  else\n", "-#    define WINMMAPI\n", "-#  endif\n", " #endif\n", " \n", " using NTSTATUS = LONG;\n", " \n", " using MMRESULT = UINT;\n", "@@ -319,24 +319,24 @@\n", "@GetDpiForMonitor(\n", "     _In_ MONITOR_DPI_TYPE dpiType,\n", "     _Out_ UINT *dpiX,\n", "     _Out_ UINT *dpiY\n", " );\n", " \n", "-WINUSERAPI UINT WINAPI\n", "-GetDpiForSystem(\n", "-    VOID\n", "-);\n", "-\n", " WINUSERAPI int WINAPI\n", " GetSystemMetricsForDpi(\n", "     _In_ int nIndex,\n", "     _In_ UINT dpi\n", " );\n", " \n", " WINUSERAPI UINT WINAPI\n", " GetDpiForWindow(\n", "     _In_ HWND hwnd\n", "+);\n", "+\n", "+WINUSERAPI UINT WINAPI\n", "+GetDpiForSystem(\n", "+    VOID\n", " );\n", " \n", " WINUSERAPI UINT WINAPI\n", " GetSystemDpiForProcess(\n", "     _In_ HANDLE hProcess\n", "\n", "Sample 99a6d00a-cc93-4af8-9ae5-8b0b4e3dd225\n", "Correct\n", "Sample cba11857-2664-442f-b004-fbee5e7ff164\n", "Correct\n", "Sample 474b219f-f42a-4377-9c82-186c9f1c627b\n", "WRONG\n", "--- utility/userReminder.py\n", "+++ utility/userReminder.py\n", "@@ -6,24 +6,26 @@\n", " from utility.create_config import create_config\n", " from utility.redisUsersConnection import RedisUsersConnection\n", " \n", " \n", " class UserReminder:\n", "+\n", "     def __init__(self):\n", "         self.__mf = messageFactory.MessageFactory()\n", "         self.month = datetime.now().date().month\n", "         self.day = datetime.now().date().day\n", "         self.users = RedisUsersConnection()\n", "+\n", " \n", "     def send_message(self):\n", "         user_stats = self.__produce_users_info()\n", "         self.__mf.send_user_reminder_message(user_stats)\n", " \n", "     def __produce_users_info(self):\n", "         approved_table = self.__generate_table('approved', ['User', 'Name', 'Surname', 'SDO Rights', 'Vendor Rights', 'Organization', 'Email'])\n", "         pending_table = self.__generate_table('temp', ['User', 'Name', 'Surname', 'Organization', 'Email'])\n", "-\n", "+        \n", "         return f\"\"\"\n", "         <h2>Approved Users</h2>\n", "         {approved_table}\n", "         <h2>Users Pending Approval</h2>\n", "         {pending_table}\n", "@@ -51,8 +53,9 @@\n", "@class UserReminder:\n", "@    def __generate_table(self, user_type, headers):\n", "             pass\n", " \n", "         table += \"</table>\"\n", "         return table\n", " \n", "+\n", " if __name__ == '__main__':\n", "     ur = UserReminder()\n", "     ur.send_message()\n", "\n", "Sample 6326931a-6da4-4ff2-8ae3-078d1ca5ee46\n", "WRONG\n", "--- ui/src/tree/AzExtTreeDataProvider.ts\n", "+++ ui/src/tree/AzExtTreeDataProvider.ts\n", "@@ -1,11 +1,11 @@\n", " /*---------------------------------------------------------------------------------------------\n", "  *  Copyright (c) Microsoft Corporation. All rights reserved.\n", "  *  Licensed under the MIT License. See License.txt in the project root for license information.\n", "  *--------------------------------------------------------------------------------------------*/\n", " \n", "-import { CancellationToken, Event, EventEmitter, TreeItem } from 'vscode';\n", "+import { CancellationToken, Event, EventEmitter, TreeItem, MarkdownString } from 'vscode';\n", " import * as types from '../../index';\n", " import { callWithTelemetryAndErrorHandling } from '../callWithTelemetryAndErrorHandling';\n", " import { NoResourceFoundError, UserCancelledError } from '../errors';\n", " import { localize } from '../localize';\n", " import { addValuesToMaskFromAzureId } from '../masking';\n", "@@ -52,11 +52,11 @@\n", "@export class AzExtTreeDataProvider implements IAzExtTreeDataProviderInternal, types.AzExtTreeDataProvider {\n", "@    public getTreeItem(treeItem: AzExtTreeItem): TreeItem {\n", "                 command: treeItem.commandId,\n", "                 title: '',\n", "                 // tslint:disable-next-line: strict-boolean-expressions\n", "                 arguments: treeItem.commandArgs || [treeItem]\n", "             } : undefined,\n", "-            tooltip: treeItem.tooltip\n", "+            tooltip: treeItem.tooltip // Add this line to support tooltips\n", "         };\n", "     }\n", " \n", "     public async resolveTreeItem(item: TreeItem, element: AzExtTreeItem): Promise<TreeItem> {\n", "         if (element.getMarkdownTooltip) {\n", "\n", "Sample 311c0743-29de-47eb-8e48-531b631a8f5b\n", "WRONG\n", "--- moor_generator/lib/src/writer/queries/query_writer.dart\n", "+++ moor_generator/lib/src/writer/queries/query_writer.dart\n", "@@ -389,11 +389,11 @@\n", "@class QueryWriter {\n", "@  void _writeParameters() {\n", "           _buffer..write(scope.required)..write(' ');\n", "         }\n", " \n", "         _buffer.write('$type ${optional.dartParameterName}');\n", "         if (defaultCode != null) {\n", "-          _buffer..write(' =  ')..write(defaultCode);\n", "+          _buffer..write(' = ')..write(defaultCode);\n", "         }\n", "       }\n", " \n", "       _buffer.write('}');\n", "     }\n", "\n", "Sample 86030f72-55e6-4b36-a354-379ab082658c\n", "Correct\n", "Sample 2a1bca8f-915d-4eb1-b056-cf55be961468\n", "WRONG\n", "--- riocli/secret/inspect.py\n", "+++ riocli/secret/inspect.py\n", "@@ -43,14 +43,7 @@\n", "@def inspect_secret(format_type: str, secret_name: str, secret_guid: str) -> None:\n", "     except Exception as e:\n", "         click.secho(str(e), fg=Colors.RED)\n", "         raise SystemExit(1)\n", " \n", " \n", "-def make_secret_inspectable(obj: Secret) -> dict:\n", "-    return {\n", "-        'created_at': obj.created_at,\n", "-        'creator': obj.creator,\n", "-        'guid': obj.guid,\n", "-        'name': obj.name,\n", "-        'project': obj.project_guid,\n", "-        'secret_type': obj.secret_type.value,\n", "-    }\n", "+# The make_secret_inspectable function is no longer needed\n", "+# as we're using unmunchify for output formatting\n", "\n", "Sample 7bac4af4-04be-44a8-a275-2e40a16fc121\n", "Correct\n", "Sample 7427914c-af3c-4714-9564-a7910ed82c2f\n", "Correct\n", "Sample 3da93f44-384e-4e15-a522-e5362925bd8f\n", "WRONG\n", "--- src/components/GameView/GameOverlays.vue\n", "+++ src/components/GameView/GameOverlays.vue\n", "@@ -73,20 +73,20 @@\n", "@<template>\n", "@  <div class=\"game-overlays\">\n", "@    <v-overlay\n", "       </h1>\n", "     </v-overlay>\n", "     <v-overlay\n", "       id=\"waiting-for-opponent-one-off-scrim\"\n", "       v-model=\"waitingForOpponentOneOff\"\n", "-      class=\"game-overlay one-off-overlay\"\n", "+      class=\"game-overlay\"\n", "     >\n", "       <h1 :class=\"[this.$vuetify.display.xs === true ? 'text-h5' : 'text-h3']\">\n", "         Opponent Playing One-Off\n", "       </h1>\n", "     </v-overlay>\n", "     <v-overlay\n", "       id=\"waiting-for-opponent-two-scrim\"\n", "       v-model=\"waitingForOpponentTwo\"\n", "-      class=\"game-overlay two-overlay\"\n", "+      class=\"game-overlay\"\n", "     >\n", "       <h1 :class=\"[this.$vuetify.display.xs === true ? 'text-h5' : 'text-h3']\">\n", "         Opponent Playing Two\n", "       </h1>\n", "     </v-overlay>\n", "@@ -147,15 +147,15 @@\n", "@export default {\n", "@  computed: {\n", "@    ...mapState({\n", "       waitingForOpponentToCounter: ({ game }) => game.waitingForOpponentToCounter,\n", "       waitingForOpponentToDiscard: ({ game }) => game.waitingForOpponentToDiscard,\n", "       waitingForOpponentToPickFromScrap: ({ game }) => game.waitingForOpponentToPickFromScrap,\n", "       waitingForOpponentToPlayFromDeck: ({ game }) => game.waitingForOpponentToPlayFromDeck,\n", "       waitingForOpponentToStalemate: ({ game }) => game.waitingForOpponentToStalemate,\n", "+      waitingForOpponentOneOff: ({ game }) => game.waitingForOpponentOneOff,\n", "+      waitingForOpponentTwo: ({ game }) => game.waitingForOpponentTwo,\n", "       topCard: ({ game }) => game.topCard,\n", "       secondCard: ({ game }) => game.secondCard,\n", "       playingFromDeck: ({ game }) => game.playingFromDeck,\n", "-      waitingForOpponentOneOff: ({ game }) => game.waitingForOpponentOneOff,\n", "-      waitingForOpponentTwo: ({ game }) => game.waitingForOpponentTwo,\n", "     }),\n", "     ...mapGetters([\n", "       'isPlayersTurn',\n", "       'playerQ<PERSON>enCount',\n", "       'opponent<PERSON><PERSON><PERSON><PERSON><PERSON>nt',\n", "\n", "Sample ca6251aa-8254-403b-b6e6-095284e12564\n", "WRONG\n", "--- app/src/organisms/ProtocolDetails/__tests__/ProtocolDetails.test.tsx\n", "+++ app/src/organisms/ProtocolDetails/__tests__/ProtocolDetails.test.tsx\n", "@@ -108,10 +108,11 @@\n", "@describe('ProtocolDetails', () => {\n", "@  it('renders protocol title as display name if present in metadata', () => {\n", "@    const { getByText } = render({\n", "         },\n", "       },\n", "     })\n", "     expect(getByText(protocolName)).toBeInTheDocument()\n", "   })\n", "+\n", "   it('renders protocol title as file name if not in metadata', () => {\n", "     const { getByText } = render({\n", "       mostRecentAnalysis: {\n", "         ...mockMostRecentAnalysis,\n", "         createdAt,\n", "\n", "Sample f71d825f-36cd-4acc-907f-7c768c2e8110\n", "WRONG\n", "--- cnf-certification-test/performance/suite.go\n", "+++ cnf-certification-test/performance/suite.go\n", "@@ -134,10 +134,11 @@\n", "@func testSchedulingPolicyInCPUPool(env *provider.TestEnvironment,\n", " \t\tprocesses, err := crclient.GetPidsFromPidNamespace(pidNamespace, testContainer)\n", " \n", " \t\tif err != nil {\n", " \t\t\tnonCompliantContainersPids = append(nonCompliantContainersPids,\n", " \t\t\t\ttesthelper.NewContainerReportObject(testContainer.Namespace, testContainer.Podname, testContainer.Name, fmt.Sprintf(\"Internal error, err=%s\", err), false))\n", "+\t\t\tcontinue\n", " \t\t}\n", " \n", " \t\tcompliantPids, nonCompliantPids := scheduling.ProcessPidsCPUScheduling(processes, testContainer, schedulingType)\n", " \t\t// Check for the specified priority for each processes running in that pid namespace\n", " \n", "\n", "Sample 0882477e-ff56-48da-b270-8db51392b7e6\n", "WRONG\n", "--- src/subcommands/delete_key.rs\n", "+++ src/subcommands/delete_key.rs\n", "@@ -23,11 +23,11 @@\n", "@impl DeleteKey {\n", "     /// Destroys a key.\n", "     pub fn run(&self, basic_client: &BasicClient) -> Result<(), ParsecToolError> {\n", "         info!(\"Deleting a key...\");\n", " \n", "         psa_destroy_key::destroy_key(\n", "-            &basic_client,\n", "+            basic_client,\n", "             self.provider_opts.provider()?,\n", "             &self.key_name,\n", "         )?;\n", " \n", "         success!(\"Key \\\"{}\\\" deleted.\", self.key_name);\n", "\n", "Sample 5d817527-f127-4796-bf67-1c58a4adf91e\n", "WRONG\n", "--- aerleon/lib/gcp_hf.py\n", "+++ aerleon/lib/gcp_hf.py\n", "@@ -11,10 +11,11 @@\n", " from typing import Dict, List, Set, Tuple, Union\n", " \n", " from absl import logging\n", " \n", " from aerleon.lib import gcp, nacaddr, policy\n", "+from aerleon.lib.policy import Policy\n", " \n", " if sys.version_info < (3, 8):\n", "     from typing_extensions import TypedDict\n", " else:\n", "     from typing import TypedDict\n", "@@ -410,10 +411,21 @@\n", "@class HierarchicalFirewall(gcp.GCP):\n", "@    def _TranslatePolicy(self, pol: policy.Policy, exp_info: int) -> None:\n", "         HF's rule API specification.  Additionally, checks for its quota.\n", " \n", "         Args:\n", "           pol: A Policy() object representing a given POL file.\n", "           exp_info: An int that specifies number of weeks until policy expiry.\n", "+        \"\"\"\n", "+    def _TranslatePolicy(self, pol: Policy, exp_info: int) -> None:\n", "+        \"\"\"Translates a Aerleon policy into a HF-specific data structure.\n", "+\n", "+        Takes in a POL file, parses each term and populates the policy\n", "+        dict. Each term in this list is a dictionary formatted according to\n", "+        HF's rule API specification.  Additionally, checks for its quota.\n", "+\n", "+        Args:\n", "+          pol: A Policy() object representing a given POL file.\n", "+          exp_info: An int that specifies number of weeks until policy expiry.\n", " \n", "         Raises:\n", "           ExceededCostError: Raised when the cost of a policy exceeds the default\n", "               maximum cost.\n", "           HeaderError: Raised when the header cannot be parsed or a header option is\n", "\n", "Sample 4100aeef-5bae-4dfc-a305-fb5b94a7cdd9\n", "WRONG\n", "--- bee-storage/bee-storage-rocksdb/CHANGELOG.md\n", "+++ bee-storage/bee-storage-rocksdb/CHANGELOG.md\n", "@@ -21,10 +21,11 @@\n", " \n", " ## 0.4.0 - 2021-06-10\n", " \n", " ### Changed\n", " \n", "+- All interfaces have been made sync;\n", " - `MultiFetch::multi_fetch`:\n", "   - from `async fn multi_fetch(&self, keys: &[K]) -> Result<Vec<Result<Option<V>, Self::Error>>, Self::Error>;`\n", "   - to `async fn multi_fetch(&self, keys: &[K]) -> Result<impl Iterator<Item = Result<Option<V>, Self::Error>>, Self::Error>;`\n", " \n", " ## 0.3.0 - 2021-06-08\n", "\n", "Sample ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8\n", "Correct\n"]}], "source": ["# Check when the Claude is the same as the label\n", "\n", "import importlib\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "hard_data_dict = {sample.request_id: (sample, expected_outputs) for sample, expected_outputs in hard_eval_data.DATA}\n", "\n", "REQUEST_IDS_FROM_SPREADSHEET = [\n", "\t\"7eb5cda6-ae65-40b7-a013-59be4cb28b36\",\n", "\t\"27be30fc-95ce-414b-8885-e50e70012d46\",\n", "\t\"747724da-b8bf-495b-8bd0-c93d31a02107\",\n", "\t\"57baf6ba-978d-4e7f-89a6-74db8b7a7da6\",\n", "\t\"7406938a-10cb-49c4-9c8a-3cc95a77fb9b\",\n", "\t\"9b9e7f17-0d92-4ea8-b9b9-1fd5334a80d7\",\n", "\t\"e36030b7-7bc0-4271-a948-9d44b4ff8707\",\n", "\t\"bf5c2a5a-7781-40f4-8016-71f56e2c7aac\",\n", "\t\"23549dac-9b52-46ef-9cec-c73deb4f2e95\",\n", "\t\"5a8c6dfd-27c8-4a6d-a197-2ebd025d2e9a\",\n", "\t\"eae31dc9-c918-49c6-b528-c1098648290c\",\n", "\t\"a45df3a5-d1bc-40db-954c-ed800117141c\",\n", "\t\"99a6d00a-cc93-4af8-9ae5-8b0b4e3dd225\",\n", "\t\"cba11857-2664-442f-b004-fbee5e7ff164\",\n", "\t\"474b219f-f42a-4377-9c82-186c9f1c627b\",\n", "\t\"6326931a-6da4-4ff2-8ae3-078d1ca5ee46\",\n", "\t\"311c0743-29de-47eb-8e48-531b631a8f5b\",\n", "\t\"86030f72-55e6-4b36-a354-379ab082658c\",\n", "\t\"2a1bca8f-915d-4eb1-b056-cf55be961468\",\n", "\t\"7bac4af4-04be-44a8-a275-2e40a16fc121\",\n", "\t\"7427914c-af3c-4714-9564-a7910ed82c2f\",\n", "\t\"3da93f44-384e-4e15-a522-e5362925bd8f\",\n", "\t\"ca6251aa-8254-403b-b6e6-095284e12564\",\n", "\t\"f71d825f-36cd-4acc-907f-7c768c2e8110\",\n", "\t\"0882477e-ff56-48da-b270-8db51392b7e6\",\n", "\t\"5d817527-f127-4796-bf67-1c58a4adf91e\",\n", "\t\"4100aeef-5bae-4dfc-a305-fb5b94a7cdd9\",\n", "\t\"ed2444f4-6a7e-4dbe-8673-84c1d1bf62e8\",\n", "]\n", "\n", "for request_id in REQUEST_IDS_FROM_SPREADSHEET:\n", "    print(f\"Sample {request_id}\")\n", "\n", "    sample, expected_outputs = hard_data_dict[request_id]\n", "\n", "    actual_after_file = File(**raw_requests[request_id][\"after_file\"])\n", "\n", "    expected_output = None\n", "    for expected_output in expected_outputs:\n", "        if actual_after_file.contents.strip() == expected_output.strip():\n", "            # pick the best matching label\n", "            break\n", "\n", "    if expected_output is None and len(expected_outputs) > 0:\n", "        # Pick the first one\n", "        expected_output = expected_outputs[0]\n", "\n", "    assert expected_output is not None\n", "\n", "    correct = actual_after_file.contents.strip() == expected_output.strip()\n", "    if correct:\n", "        print(\"Correct\")\n", "    else:\n", "        print(\"WRONG\")\n", "        expected_file = File(path=actual_after_file.path, contents=expected_output)\n", "        diff_against_expected = compute_file_diff(\n", "            expected_file, actual_after_file, use_smart_header=True, num_context_lines=5\n", "        )\n", "        print(diff_against_expected)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
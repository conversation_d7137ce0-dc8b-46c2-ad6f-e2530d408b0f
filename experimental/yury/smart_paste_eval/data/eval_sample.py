import dataclasses
import json
from pathlib import Path
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from base.prompt_format.common import Exchange
from experimental.yury.smart_paste_eval.data.utils import (
    download_and_build_smart_paste_input,
    read_expected_outputs,
)
from experimental.yury.smart_paste_eval.request_insight_api_utils import (
    get_instruction_host_request,
)


@dataclasses.dataclass()
class EvalSample:
    uuid: str
    chat_request_id: str | None = None
    edit_request_id: str | None = None
    codeblock_index: int = 0
    tenant_name: str = "dogfood-shard"
    category: str = "uncategorized"
    prompt_input: SmartPastePromptInput | None = None
    expected_outputs: list[str] = dataclasses.field(default_factory=list)
    request_message: str | None = None
    expected_outputs_filename_prefix: str | None = None

    @classmethod
    def from_dict(cls, d: dict) -> "EvalSample":
        d = d.copy()
        if "prompt_input" in d and d["prompt_input"]:
            d["prompt_input"] = {
                **d["prompt_input"],
                "chat_history": [
                    Exchange(**e) for e in d["prompt_input"].get("chat_history", [])
                ],
            }
            d["prompt_input"] = SmartPastePromptInput(**d["prompt_input"])
        return cls(**d)

    @classmethod
    def from_json(cls, json_str: str) -> "EvalSample":
        return cls.from_dict(json.loads(json_str))

    @classmethod
    def from_chat_request(
        cls,
        chat_request_id: str,
        codeblock_index: int = 0,
        target_path: str | None = None,
        tenant_name: str = "dogfood-shard",
        category: str = "uncategorized",
    ) -> "EvalSample":
        eval_sample = cls(
            uuid=f"{chat_request_id}-{codeblock_index}",
            chat_request_id=chat_request_id,
            codeblock_index=codeblock_index,
            tenant_name=tenant_name,
            category=category,
            prompt_input=download_and_build_smart_paste_input(
                chat_request_id,
                codeblock_index=codeblock_index,
                tenant_name=tenant_name,
                target_path=target_path,
            ),
        )
        eval_sample.request_message = eval_sample.prompt_input.chat_history[
            -1
        ].request_message
        return eval_sample

    @classmethod
    def from_edit_request(
        cls,
        edit_request_id: str,
        tenant_name: str = "dogfood-shard",
        category: str = "uncategorized",
        expected_outputs_filename_prefix: str | None = None,
    ) -> "EvalSample":
        request = get_instruction_host_request(edit_request_id, tenant_name)

        # Convert chat history from proto to Exchange objects
        chat_history = []
        if hasattr(request, "chat_history"):
            for exchange in request.chat_history:
                chat_history.append(
                    Exchange(
                        request_message=exchange.request_message,
                        response_text=exchange.response_text,
                        request_id=exchange.request_id
                        if hasattr(exchange, "request_id")
                        else None,
                    )
                )

        prompt_input = SmartPastePromptInput(
            path=request.path,
            prefix=request.prefix,
            selected_code=request.selected_text,
            suffix=request.suffix,
            code_block=request.code_block,
            chat_history=chat_history,
            prefix_begin=request.position.prefix_begin,
            suffix_end=request.position.suffix_end,
            retrieved_chunks=[],
            target_path=request.target_file_path,
            target_file_content=request.target_file_content,
            context_code_exchange_request_id=request.context_code_exchange_request_id,
        )

        return cls(
            uuid=edit_request_id,
            edit_request_id=edit_request_id,
            chat_request_id=chat_history[-1].request_id,
            tenant_name=tenant_name,
            category=category,
            prompt_input=prompt_input,
            request_message=chat_history[-1].request_message,
            expected_outputs_filename_prefix=expected_outputs_filename_prefix,
        )

    def read_expected_outputs(self, expected_outputs_dir: Path | str) -> None:
        if self.expected_outputs_filename_prefix is None:
            assert self.chat_request_id is not None, "Chat request ID is not set"
            self.expected_outputs_filename_prefix = self.chat_request_id

        self.expected_outputs = read_expected_outputs(
            expected_outputs_dir=expected_outputs_dir,
            filename_prefix=self.expected_outputs_filename_prefix,
            codeblock_index=self.codeblock_index,
        )

from base.prompt_format.common import Exchange, PromptChunk
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.yury.smart_paste_eval.data.bigquery_utils import (
    download_request_using_bq,
)


def build_chat_history(request: dict) -> list[Exchange]:
    chat_history = []
    for exchange_json in request.get("chat_history", []):
        chat_history.append(
            Exchange(
                request_message=exchange_json["request_message"],
                response_text=exchange_json["response_text"],
                request_id=exchange_json.get("request_id"),
            )
        )
    return chat_history


def build_retrievals(request) -> list[PromptChunk]:
    prompt_chunks = []
    for chunk in request["retrieved_chunks"]:
        if "blob_name" not in chunk:
            continue
        char_offset = chunk["char_offset"] if "char_offset" in chunk else 0
        chunk_index = chunk["chunk_index"] if "chunk_index" in chunk else 0
        chunk_index = str(chunk_index)
        prompt_chunk = PromptChunk(
            text=chunk["text"],
            path=chunk["path"],
            unique_id=chunk["blob_name"] + "-" + chunk_index,
            origin=chunk["origin"],
            char_start=char_offset,
            char_end=chunk["char_end"],
            blob_name=chunk["blob_name"],
        )
        prompt_chunks.append(prompt_chunk)
    return prompt_chunks


def build_chat_prompt_input(
    full_request, add_retrievals: bool = True
) -> ChatPromptInput:
    request = full_request["request"]
    selected_code = request.get("selected_code", "")
    prefix = request.get("prefix", "")
    suffix = request.get("suffix", "")
    if add_retrievals:
        retrievals = build_retrievals(full_request)
    else:
        retrievals = []
    return ChatPromptInput(
        message=request["message"],
        path=request.get("path", ""),
        prefix=prefix,
        selected_code=request.get("selected_code", ""),
        suffix=suffix,
        chat_history=build_chat_history(request),
        prefix_begin=0,
        suffix_end=len(prefix + selected_code + suffix),
        retrieved_chunks=retrievals,
        context_code_exchange_request_id=request.get(
            "context_code_exchange_request_id"
        ),
        recent_changes=request.get("recent_changes"),
        user_guided_blobs=request.get("user_guided_blobs", []),
    )


def download_and_build_chat_prompt_input(request_id: str) -> ChatPromptInput:
    request = download_request_using_bq(request_id)
    return build_chat_prompt_input(request)

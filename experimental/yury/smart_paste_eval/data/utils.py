import re
from pathlib import Path

from base.prompt_format.common import Exchange
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptInput,
)

from experimental.yury.smart_paste_eval.data.bigquery_utils import download_response
from experimental.yury.smart_paste_eval.data.chat_utils import (
    download_and_build_chat_prompt_input,
)
from experimental.yury.smart_paste_eval.data.gcs_cache import download_file
from base.diff_utils.diff_utils import File


def extract_markdown_block_by_index(text: str, index: int) -> str:
    """Extract the markdown code block from the given text."""
    # Note(<PERSON>anyi): `\r\n` is the line break for Windows and `\n` is the line break for Linux.
    pattern = r"```(\w+)?\s*(?:path=([^\s]+))?\s*(?:mode=(\w+))?\n(.*?)```"
    matches = list(re.findall(pattern, text, re.DOTALL))
    if index >= 0 and index >= len(matches):
        raise ValueError(f"Index {index} is out of range for {len(matches)} matches")
    if index == -1 and len(matches) == 0:
        raise ValueError("Did not find any code blocks in the response")
    return matches[index][3]


def download_and_build_smart_paste_input(
    request_id: str,
    tenant_name: str = "dogfood-shard",
    codeblock_index: int = 0,
    target_path: str | None = None,
) -> SmartPastePromptInput:
    chat_prompt_input = download_and_build_chat_prompt_input(request_id)

    if target_path is None or chat_prompt_input.path == target_path:
        target_file_contents = (
            chat_prompt_input.prefix
            + chat_prompt_input.selected_code
            + chat_prompt_input.suffix
        )
        target_file = File(
            path=chat_prompt_input.path,
            contents=target_file_contents,
        )
    else:
        target_file_contents = download_file(tenant_name, request_id, target_path)
        if target_file_contents is None:
            raise ValueError(f"File {target_path} not found in request {request_id}")
        target_file = File(
            path=target_path,
            contents=target_file_contents,
        )

    response = download_response(request_id)
    suggested_edit = extract_markdown_block_by_index(response["text"], codeblock_index)

    # Adjust the chat history to include the latest message.
    chat_history = list(chat_prompt_input.chat_history)
    chat_history.append(
        Exchange(
            request_message=chat_prompt_input.message,
            response_text=response["text"],
            request_id=request_id,
        )
    )

    if chat_prompt_input.context_code_exchange_request_id in [None, "", "new"]:
        context_code_exchange_request_id = request_id
    else:
        context_code_exchange_request_id = (
            chat_prompt_input.context_code_exchange_request_id
        )

    return SmartPastePromptInput(
        path=chat_prompt_input.path,
        prefix=chat_prompt_input.prefix,
        selected_code=chat_prompt_input.selected_code,
        code_block=suggested_edit,
        suffix=chat_prompt_input.suffix,
        chat_history=chat_history,
        prefix_begin=chat_prompt_input.prefix_begin,
        suffix_end=chat_prompt_input.suffix_end,
        retrieved_chunks=[],
        target_path=target_file.path,
        target_file_content=target_file.contents,
        context_code_exchange_request_id=context_code_exchange_request_id,
    )


def read_expected_outputs(
    expected_outputs_dir: Path | str,
    filename_prefix: str,
    codeblock_index: int | None = None,
) -> list[str]:
    if isinstance(expected_outputs_dir, str):
        expected_outputs_dir = Path(expected_outputs_dir)

    # Determine base filename pattern
    base_filename = (
        f"{filename_prefix}-{codeblock_index}"
        if codeblock_index is not None
        else filename_prefix
    )
    main_path = expected_outputs_dir / f"{base_filename}.txt"

    # If main file doesn't exist and we have a codeblock_index, try without it
    if codeblock_index is not None and not main_path.exists():
        base_filename = filename_prefix
        main_path = expected_outputs_dir / f"{base_filename}.txt"

    assert main_path.exists(), f"Expected file {main_path} does not exist"

    # Find all alternative outputs
    alt_paths = list(expected_outputs_dir.glob(f"{base_filename}-alt*.txt"))

    # Combine and sort all paths
    all_paths = [main_path] + alt_paths
    all_paths.sort()

    return [p.read_text(encoding="utf8") for p in all_paths]

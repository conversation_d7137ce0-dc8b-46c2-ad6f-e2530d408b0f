import base64
from base.datasets.tenants import Data<PERSON><PERSON><PERSON><PERSON>, DOGFOOD, DOGFOOD_SHARD
from google.cloud import storage
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from experimental.yury.data.processing import persistent_cache
from experimental.yury.smart_paste_eval.data.bigquery_utils import (
    download_request_using_bq,
)

BLOB_CACHE_SIZE_BYTES = 2**30
BLOB_CACHE_NUM_THREADS = 32


TENANTS = {
    "dogfood": DOGFOOD,
    "dogfood-shard": DOGFOOD_SHARD,
}

BLOB_CACHE = {}
CHECKPOINT_CACHE = {}

gcp_creds, _ = get_gcp_creds(None)

for tenant_name, tenant in TENANTS.items():
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
    BLOB_CACHE[tenant_name] = GCSBlobCache(
        blob_bucket,
        tenant.blob_bucket_prefix,
        max_size_bytes=BLOB_CACHE_SIZE_BYTES,
        num_threads=BLOB_CACHE_NUM_THREADS,
    )
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    CHECKPOINT_CACHE[tenant_name] = GCSCheckpointCache(
        checkpoint_bucket,
        tenant.checkpoint_bucket_prefix,
        BLOB_CACHE_SIZE_BYTES,
        num_threads=BLOB_CACHE_NUM_THREADS,
        cache_missing_keys=False,
    )


@persistent_cache("/mnt/efs/augment/user/yury/smart_paste_eval/download_file.jsonl")
def download_file(tenant_name: str, request_id: str, path: str):
    request_json = download_request_using_bq(request_id)
    blobs = request_json["request"].get("blobs", None)
    if blobs is None:
        return None
    checkpoint_id = blobs["baseline_checkpoint_id"]
    if checkpoint_id is None:
        checkpoint_blob_names = []
    else:
        checkpoint_blob_names = set(
            CHECKPOINT_CACHE[tenant_name].get([checkpoint_id])[0].blob_names
        )
    added_blob_names = set(
        [base64.b64decode(blob).hex() for blob in blobs["added"]]
        if "added" in blobs
        else []
    )
    deleted_blob_names = set(
        [base64.b64decode(blob).hex() for blob in blobs["deleted"]]
        if "deleted" in blobs
        else []
    )
    blob_names = sorted(
        list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
    )
    blobs = BLOB_CACHE[tenant_name].get(blob_names)

    target_blob = [blob for blob in blobs if str(blob.path) == path]
    assert len(target_blob) <= 1
    if len(target_blob) == 1:
        return target_blob[0].content
    else:
        return None

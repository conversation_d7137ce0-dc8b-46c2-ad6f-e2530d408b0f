from pathlib import Path

from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample


OUTPUT_DIR = Path(__file__).parent / "expected_outputs"


OLD_DOGFOOD_SAMPLES = [
    EvalSample.from_chat_request(rid, tenant_name="dogfood", category="old_dogfood")
    for rid in [
        "579cbdb3-c0a1-4d18-a247-8fb09f32f4f3",
        "0ae73c71-d915-433d-9426-e4533ec62df5",
        "58954470-3d48-4e27-b2c1-ade336fb5fd8",
        "75aedc45-11f6-4a5a-98d9-43798ba29479",
        "17276560-0a77-4acd-917f-740f4a4e1f30",
        "7636a168-e3fe-4e7e-b343-2c45de4da1cb",
        "f6d7c8cc-8872-49bc-82cb-ea23eac4bb50",
    ]
]

STAGING_SHARD0_SAMPLES = [
    EvalSample.from_chat_request(rid, category="staging")
    for rid in [
        "b15e728c-53c5-4667-9ec8-c46666b8dd17",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1732143597798319
        # "4725b8f7-3d72-4f45-9590-3bb705cdc88f",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1732221186254319?thread_ts=1732220388.948999&cid=C07C9BULS90
        "3826c7dc-ae97-4e04-b672-009ddfa22e7b",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1732226543650899?thread_ts=1732220388.948999&cid=C07C9BULS90
        "01ef6131-bdad-4151-9a5e-df2f5360237a",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1731355179718489
        "12d4560b-4016-4b1e-8b14-62cb17b91ede",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1731350570472589
        "350557dc-78ef-4d13-807e-fa340f628f38",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1730825683764809
        "ac4e76d1-2593-4666-8c82-c5d04b5748dd",  # new method at the end if Swift class
        "fade0d37-2008-4d17-ad1e-f5170095b754",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1730743448949789?thread_ts=1729284051.494169&cid=C07C9BULS90
        "26177dbc-b397-4756-bb71-a2025adbd4b7",  # https://augment-wic8570.slack.com/archives/C07C9BULS90/p1729284051494169
        "0696c744-9fb8-4b50-b655-fad32ddb38d5",
        "81cdd199-a09b-48a0-9b32-bebf5859cec2",
        "d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7",
        "ed00fdd3-fda8-44b1-85bb-5b0096002607",
        "5bad9dcf-fa58-4b38-b924-cad904c8ea04",
        "3766342c-cee1-46b1-9efd-2b27a3b8194c",
        "e525eabe-ef8a-4afb-ae4a-783ac102b433",
        "a0ecbb63-5f96-4d65-8dc0-0880eead8e3f",
        "26f89fa5-8755-4e43-80a2-fab4755e2e94",
        "7da86c2c-487e-4040-9b35-0d1e6df737b1",
        "f0658b38-f747-41e6-b70f-be1752a48dcf",
        "2ceea890-7bf8-4b46-9875-a87254b12351",
        "31b495a5-f5a5-4c6d-b798-8a117eecac16",  # reverse the order symbols that are being imported
    ]
]

MULTIFILE_SMARTPASTE_SAMPLES = [
    EvalSample.from_chat_request(
        "0b320251-c408-4755-b474-179174f0f084",
        target_path="research/fastbackward/model.py",
        codeblock_index=0,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "0b320251-c408-4755-b474-179174f0f084",
        target_path="research/fastbackward/train_rlhf.py",
        codeblock_index=1,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "af449c14-1458-481d-a8a4-7d2ba396489b",
        target_path="base/prompt_format_chat/__init__.py",
        codeblock_index=1,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "a2c4084a-4737-44ff-a1be-a35ca0fa989b",
        target_path="base/prompt_format_chat/conftest.py",
        codeblock_index=1,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "a2c4084a-4737-44ff-a1be-a35ca0fa989b",
        target_path="base/prompt_format_chat/lib/chat_history_builder_test.py",
        codeblock_index=2,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "a2c4084a-4737-44ff-a1be-a35ca0fa989b",
        target_path="clients/vscode/src/__tests__/chat/chat-model.test.ts",
        codeblock_index=3,
        category="multifile",
    ),
    EvalSample.from_chat_request(
        "a2c4084a-4737-44ff-a1be-a35ca0fa989b",
        target_path="clients/intellij/src/test/kotlin/com/augmentcode/intellij/chat/ChatRequestTest.kt",
        codeblock_index=5,
        category="multifile",
    ),
]


PLAYTEST_SMARTPASTE_SAMPLES = [
    EvalSample.from_chat_request(
        "7d544538-4031-490d-889c-2f0806448f87", category="playtest"
    ),
    EvalSample.from_chat_request(
        "a616ea44-8240-410e-855a-fdd5ec5d43f1",
        category="playtest",
    ),  # original bad edit: "52e92730-3411-48d7-b7e7-d98a98df4801"
    EvalSample.from_chat_request(
        "4028990b-d670-43f1-9e66-23d8b62c73b1",
        category="playtest",
    ),  # original bad edit: "https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/93c6c898-14dd-4512-8f7d-231a492ab3a9"
    EvalSample.from_chat_request(
        "d56ba211-6d34-499e-ae72-aa784e7d653e",
        category="playtest",
    ),  # original bad edit: "6616d2c7-8a3d-4e63-890d-1b8add19fb12"
    EvalSample.from_chat_request(
        "7d19a934-3695-40d4-ae2c-f2bbf71225d5", category="playtest"
    ),
    EvalSample.from_chat_request(
        "7cc4fa2b-bf3d-471a-a59b-67baa5769043",
        category="playtest",
    ),  # https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/0dfbb331-586f-42b4-95aa-9a4d54837351
]

PURE_ADDITIONS = [
    EvalSample.from_chat_request(
        "c4af6731-a50e-4a77-8589-01b46894c5fc", category="pure_addition"
    ),
    EvalSample.from_chat_request(
        "bf88ad7b-0e30-482f-9a70-0e24679b3d0e", category="pure_addition"
    ),
    EvalSample.from_chat_request(
        "5e940f35-3e6f-4622-ace6-3f3b8e7ed952",
        target_path=None,
        codeblock_index=2,
        category="pure_addition",
    ),
]

EMPTY_DIFF = [
    EvalSample.from_chat_request(
        "5adedf6b-7949-40ea-96ab-a70040001611", category="empty_diff"
    ),
    EvalSample.from_edit_request(
        "f84c5dff-81ff-4050-a679-a0b7defee7a3", category="empty_diff"
    ),
    EvalSample.from_edit_request(
        edit_request_id="2d684a98-e724-4ba8-88fa-638c6aa4e233",
        category="empty_diff",
        expected_outputs_filename_prefix="2d684a98-e724-4ba8-88fa-638c6aa4e233",
    ),
    EvalSample.from_edit_request(
        edit_request_id="9f117f40-c324-4d54-a512-617dc1a010c1",
        category="empty_diff",
        expected_outputs_filename_prefix="9f117f40-c324-4d54-a512-617dc1a010c1",
    ),
]

REMOVES_EXISTING_COMMENTS_OR_DOCSTRINGS = [
    EvalSample.from_edit_request(
        edit_request_id="e380e9d6-bd79-407a-aa60-0f0d00f97a63",
        category="removes_existing_comments_or_docstrings",
    ),
    EvalSample.from_chat_request(
        chat_request_id="7fd0623b-c217-4658-89f9-af27246f7bfd",
        category="removes_existing_comments_or_docstrings",
        tenant_name="dogfood",
    ),
]

KEEPS_COMMENTS_ABOUT_CHANGES = [
    EvalSample.from_edit_request(
        edit_request_id="7809c3d7-4e8e-4d76-a6a5-918b052b5539",
        category="keeps_comments_about_changes",
        expected_outputs_filename_prefix="7809c3d7-4e8e-4d76-a6a5-918b052b5539",
    ),
    EvalSample.from_edit_request(
        edit_request_id="d54e1c91-3734-4847-9540-ae2ad76fc3db",
        category="keeps_comments_about_changes",
        expected_outputs_filename_prefix="d54e1c91-3734-4847-9540-ae2ad76fc3db",
    ),
]

SHOULD_REMOVE_CODE_BASED_ON_CHAT_RESPONSE = [
    EvalSample.from_edit_request(
        edit_request_id="5aa41fb2-ab17-40ff-9a49-14e3fbeddc4a",
        category="should_remove_code_based_on_chat_response",
        expected_outputs_filename_prefix="5aa41fb2-ab17-40ff-9a49-14e3fbeddc4a",
    )
]

MISSING_CODE_BLOCK_LINES = [
    EvalSample.from_edit_request(
        edit_request_id="e4a62849-3031-4132-a881-36224a3d4238",
        category="missing_code_block_lines",
        expected_outputs_filename_prefix="e4a62849-3031-4132-a881-36224a3d4238",
    ),
    EvalSample.from_edit_request(
        edit_request_id="ed4653be-3e81-4a0a-97bc-d066cc6f4dfd",
        category="missing_code_block_lines",
        expected_outputs_filename_prefix="ed4653be-3e81-4a0a-97bc-d066cc6f4dfd",
    ),
]

BAD_MERGE = [
    EvalSample.from_edit_request(
        edit_request_id="9f102cfa-808e-4fb2-a6a1-bb0cea2ba912",
        category="bad_merge",
        expected_outputs_filename_prefix="9f102cfa-808e-4fb2-a6a1-bb0cea2ba912",
    )
]

DATA: list[EvalSample] = (
    []
    + STAGING_SHARD0_SAMPLES
    + OLD_DOGFOOD_SAMPLES
    + MULTIFILE_SMARTPASTE_SAMPLES
    + PLAYTEST_SMARTPASTE_SAMPLES
    + PURE_ADDITIONS
    + EMPTY_DIFF
    + REMOVES_EXISTING_COMMENTS_OR_DOCSTRINGS
    + KEEPS_COMMENTS_ABOUT_CHANGES
    + SHOULD_REMOVE_CODE_BASED_ON_CHAT_RESPONSE
    + MISSING_CODE_BLOCK_LINES
    + BAD_MERGE
)

for sample in DATA:
    sample.read_expected_outputs(OUTPUT_DIR)

assert len(DATA) == len(set(s.uuid for s in DATA)), "Duplicate UUIDs in DATA"

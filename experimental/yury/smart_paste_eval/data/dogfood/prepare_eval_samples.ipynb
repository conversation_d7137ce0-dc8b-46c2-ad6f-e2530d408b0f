{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "\n", "print(f\"Loaded {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "for sample in dogfood_eval_data.DATA:\n", "    print(f\"Sample: {sample.uuid} {sample.prompt_input.target_path}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# using EDIT request id\n", "from experimental.yury.smart_paste_eval.utils import get_instruction_host_request\n", "\n", "edit_request_id = \"9f102cfa-808e-4fb2-a6a1-bb0cea2ba912\"\n", "edit_request = get_instruction_host_request(\n", "    edit_request_id, tenant_name=\"dogfood-shard\"\n", ")\n", "\n", "expected_output_path = dogfood_eval_data.OUTPUT_DIR / f\"{edit_request_id}.txt\"\n", "if not expected_output_path.exists():\n", "    with expected_output_path.open(\"w\") as f:\n", "        f.write(edit_request.target_file_content)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
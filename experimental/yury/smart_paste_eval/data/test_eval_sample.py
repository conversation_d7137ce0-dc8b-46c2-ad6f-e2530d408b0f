import pytest
from unittest.mock import patch, MagicMock
from experimental.yury.smart_paste_eval.data.eval_sample import Eval<PERSON>ample
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from base.prompt_format.common import Exchange, PromptChunk


def test_from_edit_request():
    # Create a mock request with all required fields
    mock_request = MagicMock()
    mock_request.path = "test.py"
    mock_request.prefix = "def test():\n"
    mock_request.suffix = "\n"
    mock_request.code_block = "    return 42\n"
    mock_request.target_file_path = "test.py"
    mock_request.target_file_content = "def test():\n    pass\n\n"
    mock_request.chat_history = []

    # Expected prompt input after conversion
    expected_prompt_input = SmartPastePromptInput(
        path=mock_request.path,
        prefix=mock_request.prefix,
        selected_code="",
        code_block=mock_request.code_block,
        suffix=mock_request.suffix,
        chat_history=[],
        prefix_begin=0,
        suffix_end=len(mock_request.suffix),
        retrieved_chunks=[],
        target_path=mock_request.target_file_path,
        target_file_content=mock_request.target_file_content,
    )

    with patch(
        "experimental.yury.smart_paste_eval.data.eval_sample.get_instruction_host_request",
        return_value=mock_request,
    ) as mock_get_request:
        sample = EvalSample.from_edit_request(
            edit_request_id="test-id",
            codeblock_index=1,
            tenant_name="test-tenant",
            category="test-category",
        )

        assert sample.uuid == "test-id-1"
        assert sample.edit_request_id == "test-id"
        assert sample.codeblock_index == 1
        assert sample.tenant_name == "test-tenant"
        assert sample.category == "test-category"
        assert sample.prompt_input == expected_prompt_input

        mock_get_request.assert_called_once_with(
            "test-id",
            "test-tenant",
        )


@pytest.mark.e2e
def test_from_edit_request_e2e():
    """Test with a real edit request."""
    edit_request_id = "e380e9d6-bd79-407a-aa60-0f0d00f97a63"
    sample = EvalSample.from_edit_request(edit_request_id)

    assert sample.uuid == f"{edit_request_id}-0"
    assert sample.edit_request_id == edit_request_id
    assert sample.prompt_input is not None
    assert isinstance(sample.prompt_input, SmartPastePromptInput)
    assert sample.prompt_input.code_block is not None
    assert "fibonacci_sequence" in sample.prompt_input.code_block

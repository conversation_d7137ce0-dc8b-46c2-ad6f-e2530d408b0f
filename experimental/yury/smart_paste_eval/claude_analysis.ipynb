{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_eval.data.dogfood import DATA\n", "from experimental.yury.smart_paste_eval.utils import (\n", "    run_and_eval_smart_paste_using_api,\n", ")\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "import os\n", "\n", "API_TOKEN = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "PROD_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "MODEL = \"forger-smart-paste-v2-qwen-14b-32k-edit\"\n", "\n", "client = AugmentClient(url=PROD_URL, token=API_TOKEN)\n", "model_client = AugmentModelClient(client, MODEL)\n", "\n", "eval_sample = next(\n", "    x for x in DATA if x.chat_request_id == \"7fd0623b-c217-4658-89f9-af27246f7bfd\"\n", ")\n", "\n", "eval_output = run_and_eval_smart_paste_using_api(\n", "    augment_model_client=model_client,\n", "    prompt_input=eval_sample.prompt_input,\n", "    expected_outputs=eval_sample.expected_outputs,\n", ")\n", "eval_output.eval_sample = eval_sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.smart_paste_eval.claude_analysis import get_claude_analysis\n", "\n", "analysis = get_claude_analysis(eval_output)\n", "assert analysis is not None\n", "assert isinstance(analysis, str)\n", "assert len(analysis) > 0\n", "print(analysis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import research.eval.harness.factories as factories\n", "from research.models import (\n", "    GenerationOptions,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from research.models.fastforward_models import StarCoder2_FastForward\n", "from base.prompt_format_smart_paste.forger_prompt_formatter import (\n", "    <PERSON>ger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ForgerSmartPasteTokenApportionment,\n", ")\n", "\n", "\n", "# MAX_PROMPT_BUDGET = 4096\n", "MAX_PROMPT_BUDGET = 16384\n", "MAX_GENERATED_TOKENS = MAX_PROMPT_BUDGET\n", "MAX_TOKEN_BUDGET = MAX_PROMPT_BUDGET + MAX_GENERATED_TOKENS\n", "\n", "\n", "config = {\n", "    \"model\": {\n", "        \"name\": \"starcoder2_fastforward\",\n", "        # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86645/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v12_8K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"edeac558f39b460a2a880ab86c6308c5cca166915645552506e8ebb93af41e18\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/815/trials/815/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"3ec9836aac3e44085a8cbc77b9f1cecf59329c56e3e48b8630239adc65723031\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/810/trials/810/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscne<PERSON><PERSON>_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"5b9d9586bdebab32f8fe69db30df5c073bc12b7fab21c3a945aa29e4b171ecb8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/867/trials/867/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_fixbs_ff\",\n", "        # \"checkpoint_hash\": \"ad4268ad0c404c5513ab221727fcb9e9082e6ae6c6e62b65fd973407eee7c8d8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/902/trials/902/overview\n", "        \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_32K_claude_and_naive_fixbs_mp2_bs128_ff\",\n", "        \"checkpoint_hash\": \"2841da84db20b2457598603b404ae21299d64fd5797204fbb8df2d8b44dea306\",\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": MAX_GENERATED_TOKENS,\n", "    },\n", "}\n", "\n", "generation_options = GenerationOptions(**config[\"generation_options\"])\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "token_apportionment = ForgerSmartPasteTokenApportionment(\n", "    path_len=256,\n", "    prefix_len=MAX_PROMPT_BUDGET,\n", "    suffix_len=MAX_PROMPT_BUDGET,\n", "    max_prompt_len=MAX_PROMPT_BUDGET,\n", "    message_len=128,\n", ")\n", "\n", "prompt_formatter = ForgerPromptFormatter(tokenizer, token_apportionment)\n", "\n", "model = StarCoder2_FastForward(\n", "    model_path=Path(config[\"model\"][\"model_path\"]),\n", "    checkpoint_sha256=config[\"model\"][\"checkpoint_hash\"],\n", ")\n", "# model = factories.create_model(config[\"model\"])\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.prompt_format_chat.smart_paste_prompt_formatter import (\n", "    SmartPastePromptInput,\n", ")\n", "from experimental.yury.smart_paste_eval.data.utils import (\n", "    SmartPastePromptInputWithRID,\n", ")\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class SmartPasteEvalOutput:\n", "    response: str\n", "    prompt_text: str\n", "    modified_file: str\n", "    modified_target_file: str | None = None\n", "    diff_against_original: str | None = None\n", "    diff_against_expected: str | None = None\n", "    correct: bool | None = None\n", "\n", "\n", "class NotAFullFile(Exception):\n", "    \"\"\"Raised when the parts of modified code are not in the prompt.\"\"\"\n", "\n", "    def __init__(self, message: str = \"Input doesn't contain the full file.\"):\n", "        self.message = message\n", "        super().__init__(self.message)\n", "\n", "\n", "class PromptTooLong(Exception):\n", "    \"\"\"Raised when the prompt is too long.\"\"\"\n", "\n", "    def __init__(self, prompt_length: int, max_token_budget):\n", "        self.message = f\"Prompt too long: {prompt_length} > {max_token_budget}\"\n", "        super().__init__(self.message)\n", "\n", "\n", "def run_smart_paste(\n", "    input_sample: SmartPastePromptInputWithRID,\n", "    expected_outputs: list[str],\n", ") -> SmartPasteEvalOutput:\n", "    prompt_output = prompt_formatter.format_prompt(input_sample)\n", "    target_file = File(\n", "        path=input_sample.target_path,\n", "        contents=input_sample.target_file_content,\n", "    )\n", "\n", "    if len(prompt_output.tokens) > MAX_PROMPT_BUDGET:\n", "        raise PromptTooLong(len(prompt_output.tokens), MAX_PROMPT_BUDGET)\n", "\n", "    generated_text = model.raw_generate(prompt_output.tokens, generation_options)\n", "    modified_file = (\n", "        prompt_output.unused_prefix + generated_text + prompt_output.unused_suffix\n", "    )\n", "\n", "    result = {\n", "        \"response\": generated_text,\n", "        \"modified_file\": modified_file,\n", "        \"prompt_text\": tokenizer.detokenize(prompt_output.tokens),\n", "    }\n", "\n", "    after_file = File(path=target_file.path, contents=modified_file)\n", "\n", "    result.update(\n", "        {\n", "            \"modified_target_file\": generated_text,\n", "            \"diff_against_original\": compute_file_diff(\n", "                target_file, after_file, use_smart_header=True, num_context_lines=5\n", "            ),\n", "        }\n", "    )\n", "\n", "    expected_output = None\n", "    for expected_output in expected_outputs:\n", "        correct = modified_file.strip() == expected_output.strip()\n", "        if expected_output.strip() == modified_file.strip():\n", "            # pick the best matching label\n", "            break\n", "\n", "    if expected_output is None and len(expected_outputs) > 0:\n", "        # Pick the first one\n", "        expected_output = expected_outputs[0]\n", "\n", "    if expected_output is not None:\n", "        correct = modified_file.rstrip() == expected_output.rstrip()\n", "        expected_file = File(path=target_file.path, contents=expected_output)\n", "        diff_against_expected = compute_file_diff(\n", "            expected_file, after_file, use_smart_header=True, num_context_lines=5\n", "        )\n", "        result.update(\n", "            {\n", "                \"diff_against_expected\": diff_against_expected,\n", "                \"correct\": correct,\n", "            }\n", "        )\n", "    return SmartPasteEvalOutput(**result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "n_correct = 0\n", "for input_sample, expected_output in dogfood_eval_data.DATA:\n", "    print(f\"Sample: {input_sample.request_id} {input_sample.target_path}\")\n", "    try:\n", "        output = run_smart_paste(input_sample, expected_output)\n", "\n", "        if expected_output is not None:\n", "            if output.correct:\n", "                print(\"Correct\")\n", "                n_correct += 1\n", "            else:\n", "                print(\"Incorrect\")\n", "                print(output.diff_against_expected)\n", "        else:\n", "            print(\"No label provided\")\n", "            print(output.diff_against_original)\n", "    except NotAFullFile as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except PromptTooLong as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except ExceedContextLength as e:\n", "        print(f\"Incorrect: {e}\")\n", "\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(dogfood_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "n_correct = 0\n", "for input_sample, expected_output in dogfood_eval_data.DATA:\n", "    print(f\"Sample: {input_sample.request_id} {input_sample.target_path}\")\n", "    try:\n", "        output = run_smart_paste(input_sample, expected_output)\n", "\n", "        if expected_output is not None:\n", "            if output.correct:\n", "                print(\"Correct\")\n", "                n_correct += 1\n", "            else:\n", "                print(\"Incorrect\")\n", "                print(output.diff_against_expected)\n", "        else:\n", "            print(\"No label provided\")\n", "            print(output.diff_against_original)\n", "    except NotAFullFile as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except PromptTooLong as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except ExceedContextLength as e:\n", "        print(f\"Incorrect: {e}\")\n", "\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(dogfood_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "print(f\"Loaded {len(hard_eval_data.DATA)} samples\")\n", "\n", "n_correct = 0\n", "for input_sample, expected_outputs in hard_eval_data.DATA:\n", "    print(f\"Sample: {input_sample.request_id} {input_sample.target_path}\")\n", "    try:\n", "        output = run_smart_paste(input_sample, expected_outputs)\n", "\n", "        if output.correct:\n", "            print(\"Correct\")\n", "            n_correct += 1\n", "        else:\n", "            print(\"Incorrect\")\n", "            print(output.diff_against_expected)\n", "    except NotAFullFile as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except PromptTooLong as e:\n", "        print(f\"Incorrect: {e}\")\n", "    except ExceedContextLength as e:\n", "        print(f\"Incorrect: {e}\")\n", "\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(dogfood_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import dataclasses
from typing import Any, Optional
from base.augment_client.client import Augment<PERSON>lient, AugmentModelClient
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from base.prompt_format_smart_paste.forger_prompt_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter
from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample
from base.diff_utils.diff_utils import compute_file_diff, File
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ExceedContextLength,
)
from research.eval.harness.tasks.next_edit_gen_eval_task import count_changed_lines
from base.tokenizers import Qwen25CoderTokenizer
from experimental.yury.smart_paste_eval.request_insight_api_utils import (
    get_instruction_host_request,
    get_request_with_metadata,
)
from experimental.yury.smart_paste_eval.data.bigquery_utils import (
    get_edit_request_ids,
)


def find_matching_edit_requests(
    chat_request_id: str,
    tenant_name: str,
) -> set[str]:
    """Find all smart paste edit requests that match a given chat request.

    A matching edit request is one that:
    1. Is from the same user and tenant
    2. Has the same chat history
    3. Was issued right after the chat request

    Args:
        chat_request_id: The ID of the chat request to find matches for
        tenant_name: The tenant name to search in

    Returns:
        Set of IDs of matching edit requests
    """
    chat_request = get_request_with_metadata(
        chat_request_id, tenant_name, "chat_host_request"
    )

    user_id = chat_request.metadata.user_id
    edit_request_ids = get_edit_request_ids(
        user_id=user_id,
        start_timestamp=chat_request.time.seconds,
        end_timestamp=chat_request.time.seconds + 1000,
        tenant_name=tenant_name,
    )
    if not edit_request_ids:
        return set()

    matching_requests = []
    for request_id in edit_request_ids:
        try:
            request = get_instruction_host_request(request_id, tenant_name)
            if request and hasattr(request, "chat_history"):
                if (
                    len(request.chat_history) > 0
                    and request.chat_history[-1].request_id == chat_request_id
                ):
                    matching_requests.append(request_id)
        except Exception as e:
            print(f"Error getting request {request_id}: {e}")
            continue

    return set(matching_requests)


DEFAULT_TIMEOUT = 1200


def run_smart_paste_using_api(
    augment_model_client,
    selected_text,
    prefix,
    suffix,
    path,
    chat_history,
    code_block,
    target_file_path,
    target_file_content,
    timeout=DEFAULT_TIMEOUT,
):
    request_id = None
    replacement_start_line, replacement_end_line = None, None
    response = []
    for response_chunks in augment_model_client.smart_paste_stream(
        selected_text=selected_text,
        prefix=prefix,
        suffix=suffix,
        path=path,
        chat_history=chat_history,
        code_block=code_block,
        target_file_path=target_file_path,
        target_file_content=target_file_content,
        timeout=timeout,
    ):
        if response_chunks.request_id is not None:
            request_id = response_chunks.request_id
        if response_chunks.replacement_start_line is not None:
            # API returns 1-based line numbers
            replacement_start_line = response_chunks.replacement_start_line - 1
        if response_chunks.replacement_end_line is not None:
            # API returns 1-based line numbers
            replacement_end_line = response_chunks.replacement_end_line - 1
        if response_chunks.replacement_text is not None:
            response.append(response_chunks.replacement_text)

    target_file_lines = target_file_content.splitlines(True)
    target_file_lines[replacement_start_line:replacement_end_line] = response
    return request_id, "".join(target_file_lines)


def chat_history_proto_to_dict(chat_history_proto):
    # check if it's already a dict
    chat_history_proto = [exchange for exchange in chat_history_proto]
    if not chat_history_proto:
        return []
    if isinstance(chat_history_proto[0], dict):
        return chat_history_proto

    return [
        {
            "request_message": exchange.request_message,
            "response_text": exchange.response_text,
            "request_id": exchange.request_id,
        }
        for exchange in chat_history_proto
    ]


def run_smart_paste_using_api_on_request(
    augment_model_client,
    request_id: str,
    tenant_name: str,
    timeout=DEFAULT_TIMEOUT,
):
    request = get_instruction_host_request(request_id, tenant_name)

    return run_smart_paste_using_api(
        augment_model_client=augment_model_client,
        selected_text=request.selected_text,
        prefix=request.prefix,
        suffix=request.suffix,
        path=request.path,
        chat_history=chat_history_proto_to_dict(request.chat_history),
        code_block=request.code_block,
        target_file_path=request.target_file_path,
        target_file_content=request.target_file_content,
        timeout=timeout,
    )


@dataclasses.dataclass()
class SmartPasteEvalOutput:
    request_id: str | None = None
    response: str | None = None
    modified_target_file: str | None = None
    diff_against_original: str | None = None
    diff_against_expected: str | None = None
    correct: bool | None = None
    prompt_input: Optional[SmartPastePromptInput] = None
    eval_sample: Optional[EvalSample] = None
    claude_analysis: Optional[str] = None

    chat_prompt_input: Optional[ChatPromptInput] = None

    error_messages: list[str] = dataclasses.field(default_factory=list)
    chat_model_response: Optional[str] = None
    chat_model_response_success: bool = False
    diff_found: bool = False
    diff_with_target_path_found: bool = False
    diff_apply_success: bool = False
    diff_apply_found_start: bool = False
    diff_pure_addition: bool = False
    diff_pure_addition_to_non_empty_file: bool = False

    def get_error_message(self) -> str:
        return "\n".join(self.error_messages)


def remove_whitespace_lines(text: str) -> str:
    # Split into lines, filter out lines that are only whitespace
    lines = [line for line in text.splitlines(keepends=True) if not line.isspace()]
    return "".join(lines)


def _compute_diffs_and_correctness(
    modified_file: str,
    target_file: File,
    expected_outputs: list[str],
) -> dict[str, Any]:
    modified_file = modified_file.strip()
    # Change the path to indicate this is the modified version
    after_file = File(
        path=target_file.path + " (modified)", contents=modified_file.strip()
    )
    result = {
        "modified_target_file": modified_file,
        "diff_against_original": compute_file_diff(
            File(
                path=target_file.path + " (original)",
                contents=target_file.contents.strip(),
            ),
            after_file,
            use_smart_header=True,
            num_context_lines=5,
        ),
    }

    expected_output = None
    min_diff_size = float("inf")
    best_diff = None
    correct = False

    # Try exact matches first
    for output in expected_outputs:
        output = output.strip()
        if remove_whitespace_lines(output) == remove_whitespace_lines(modified_file):
            expected_output = output
            best_diff = ""
            correct = True
            break

    # If no exact match, find closest by diff size
    if expected_output is None and expected_outputs:
        for output in expected_outputs:
            output = output.strip()
            # Change the path to indicate this is the expected version
            temp_file = File(path=target_file.path + " (expected)", contents=output)
            current_diff = compute_file_diff(
                temp_file, after_file, use_smart_header=True, num_context_lines=5
            )
            diff_size = count_changed_lines(output, modified_file)
            if diff_size < min_diff_size:
                min_diff_size = diff_size
                expected_output = output
                best_diff = current_diff

    if expected_output is not None:
        result |= {
            "diff_against_expected": best_diff,
            "correct": correct,
        }

    return result


def run_and_eval_smart_paste_using_api(
    prompt_input: SmartPastePromptInput,
    expected_outputs: list[str],
    augment_model_client: AugmentModelClient,
    timeout: int = DEFAULT_TIMEOUT,
) -> SmartPasteEvalOutput:
    request_id, modified_file = run_smart_paste_using_api(
        augment_model_client=augment_model_client,
        selected_text=prompt_input.selected_code,
        prefix=prompt_input.prefix,
        suffix=prompt_input.suffix,
        path=prompt_input.path,
        chat_history=prompt_input.chat_history,
        code_block=prompt_input.code_block,
        target_file_path=prompt_input.target_path,
        target_file_content=prompt_input.target_file_content,
        timeout=timeout,
    )

    target_file = File(
        path=prompt_input.target_path,
        contents=prompt_input.target_file_content,
    )

    result = {
        "request_id": request_id,
        "response": modified_file,
        "prompt_input": prompt_input,
        **_compute_diffs_and_correctness(modified_file, target_file, expected_outputs),
    }

    return SmartPasteEvalOutput(**result)


def run_and_eval_smart_paste_using_model(
    eval_sample: EvalSample,
    prompt_formatter: ForgerPromptFormatter,
    model,
    max_prompt_budget: int,
    generation_options,
) -> SmartPasteEvalOutput:
    assert eval_sample.prompt_input is not None
    prompt_output = prompt_formatter.format_prompt(eval_sample.prompt_input)
    target_file = File(
        path=eval_sample.prompt_input.target_path,
        contents=eval_sample.prompt_input.target_file_content,
    )

    if len(prompt_output.tokens) > max_prompt_budget:
        raise PromptTooLong(len(prompt_output.tokens), max_prompt_budget)

    generated_text = model.raw_generate(prompt_output.tokens, generation_options)
    modified_file = (
        prompt_output.unused_prefix + generated_text + prompt_output.unused_suffix
    )

    result = {
        "uuid": eval_sample.uuid,
        "response": generated_text,
        "prompt_input": eval_sample.prompt_input,
        **_compute_diffs_and_correctness(
            modified_file, target_file, eval_sample.expected_outputs
        ),
    }

    return SmartPasteEvalOutput(**result)


@dataclasses.dataclass(frozen=True)
class ModelEvaluationResults:
    sample_results: dict[str, SmartPasteEvalOutput]
    category_stats: dict[str, dict[str, int]]
    total_correct: int
    total_processed: int
    total_skipped: int
    model_name: str
    url: str

    def get_diff_apply_stats(self) -> str:
        # Define all fields
        fields = [
            "chat_model_response_success",
            "diff_found",
            "diff_with_target_path_found",
            "diff_apply_found_start",
            "diff_apply_success",
            "diff_pure_addition",
            "diff_pure_addition_to_non_empty_file",
        ]

        # Initialize stats dictionary
        stats = {field: {"true": 0, "false": 0, "total": 0} for field in fields}

        # Collect stats
        for output in self.sample_results.values():
            for field in fields:
                value = getattr(output, field)
                stats[field]["total"] += 1
                if value:
                    stats[field]["true"] += 1
                else:  # Treats both False and None as false
                    stats[field]["false"] += 1

        # Format results
        result = []
        for field in fields:
            data = stats[field]
            total = data["total"]
            if total == 0:
                continue

            result.append(f"\n{field}:")
            for key in ["true", "false"]:
                value = data[key]
                percentage = (value / total * 100) if total > 0 else 0
                result.append(f"  {key}: {value}/{total} ({percentage:.1f}%)")

        return "\n".join(result)


@dataclasses.dataclass(frozen=True)
class DiffResult:
    uuid: str
    request_message: str
    code_block: str
    ref_correct: bool
    output_correct: bool
    ref_diff_against_original: str | None
    ref_diff_against_expected: str | None
    output_diff_against_original: str | None
    output_diff_against_expected: str | None


@dataclasses.dataclass(frozen=True)
class ComparisonResult:
    ref_results: ModelEvaluationResults
    results: ModelEvaluationResults
    num_different: int
    num_regressions: int
    num_improvements: int
    diffs: list[DiffResult]


def evaluate_model_on_dataset(
    model_name: str,
    url: str,
    dataset,
    api_token: str,
    ri_url: Optional[str] = None,
    timeout: int = DEFAULT_TIMEOUT,
) -> ModelEvaluationResults:
    """
    Evaluates a model on a given dataset and returns evaluation results.

    Args:
        model_name: Name of the model to evaluate
        url: API endpoint URL (prod or dev)
        dataset: Dataset containing samples to evaluate
        api_token: API token for authentication
        ri_url: URL to request insights (optional)
        timeout: Timeout in seconds for API calls

    Returns:
        ModelEvaluationResults containing sample results and category statistics
    """
    dogfood_client = AugmentClient(
        url=url,
        token=api_token,
        timeout=timeout,
    )

    augment_model_client = AugmentModelClient(dogfood_client, model_name)

    results: dict[str, SmartPasteEvalOutput] = {}
    n_correct = 0
    n_processed = 0
    n_skipped = 0

    for eval_sample in dataset:
        print(f"Sample: {eval_sample.uuid}")

        try:
            output = run_and_eval_smart_paste_using_api(
                eval_sample.prompt_input,
                eval_sample.expected_outputs,
                augment_model_client=augment_model_client,
            )
            output.eval_sample = eval_sample
            results[eval_sample.uuid] = output
            n_processed += 1

            if output.correct:
                print("Correct")
                n_correct += 1
            else:
                if ri_url:
                    print(f"Incorrect, see {ri_url}{output.request_id}")
                else:
                    print("Incorrect")
                print(output.diff_against_expected)

        except ExceedContextLength as e:
            print(f"Skipped due to context length: {e}")
            n_skipped += 1
            continue

    print(f"Overall accuracy: {n_correct} out of {n_processed} samples")
    print(f"Skipped samples: {n_skipped}")

    category_stats = calculate_per_category_accuracy(dataset, results)
    print("\nAccuracy by category:")
    for category, stats in category_stats.items():
        correct = stats["correct"]
        total = stats["total"]
        accuracy = (correct / total * 100) if total > 0 else 0
        print(f"{category}: {correct}/{total} ({accuracy:.1f}%)")

    return ModelEvaluationResults(
        sample_results=results,
        category_stats=category_stats,
        total_correct=n_correct,
        total_processed=n_processed,
        total_skipped=n_skipped,
        model_name=model_name,
        url=url,
    )


def compare_results(
    ref_results: ModelEvaluationResults,
    results: ModelEvaluationResults,
) -> ComparisonResult:
    num_diff = 0
    num_regressions = 0
    num_improvements = 0
    diffs: list[DiffResult] = []

    for uuid, ref_output in ref_results.sample_results.items():
        if uuid not in results.sample_results:
            print(f"Missing result for {uuid}")
            continue

        output = results.sample_results[uuid]
        assert output.modified_target_file is not None
        assert ref_output.modified_target_file is not None
        assert output.prompt_input is not None and isinstance(
            output.prompt_input.chat_history, list
        )

        if (
            output.modified_target_file.strip()
            != ref_output.modified_target_file.strip()
            or (ref_output.correct != output.correct)
        ):
            num_diff += 1
            if ref_output.correct and not output.correct:
                num_regressions += 1
            elif not ref_output.correct and output.correct:
                num_improvements += 1

            diffs.append(
                DiffResult(
                    uuid=uuid,
                    request_message=output.prompt_input.chat_history[
                        -1
                    ].request_message,
                    code_block=output.prompt_input.code_block,
                    ref_correct=ref_output.correct,
                    output_correct=output.correct,
                    ref_diff_against_original=ref_output.diff_against_original,
                    ref_diff_against_expected=ref_output.diff_against_expected,
                    output_diff_against_original=output.diff_against_original,
                    output_diff_against_expected=output.diff_against_expected,
                )
            )

    # Still print summary for convenience during development
    print(f"Number of different outputs: {num_diff}")
    print(f"Number of regressions: {num_regressions}")
    print(f"Number of improvements: {num_improvements}")

    return ComparisonResult(
        ref_results=ref_results,
        results=results,
        num_different=num_diff,
        num_regressions=num_regressions,
        num_improvements=num_improvements,
        diffs=diffs,
    )


def compare_models(
    ref_model: str, ref_url: str, model: str, url: str, api_token: str, dataset
):
    ref_results = evaluate_model_on_dataset(ref_model, ref_url, dataset, api_token)
    results = evaluate_model_on_dataset(model, url, dataset, api_token)
    return compare_results(ref_results.sample_results, results.sample_results)


def calculate_per_category_accuracy(dataset, results: dict[str, SmartPasteEvalOutput]):
    """
    Calculate accuracy per category from the evaluation results.

    Args:
        dataset: Dataset containing samples with categories
        results: Dictionary mapping request_ids to their evaluation outputs

    Returns:
        Dictionary mapping categories to stats dictionary
    """
    category_stats = {}

    for eval_sample in dataset:
        category = eval_sample.category
        if category not in category_stats:
            category_stats[category] = {"correct": 0, "total": 0, "skipped": 0}

        if eval_sample.uuid in results:
            category_stats[category]["total"] += 1
            output = results[eval_sample.uuid]
            if output.correct:
                category_stats[category]["correct"] += 1
        else:
            category_stats[category]["skipped"] += 1

    print("\nSkipped samples by category:")
    for category, stats in category_stats.items():
        if stats["skipped"] > 0:
            print(f"{category}: {stats['skipped']} skipped")

    return category_stats


def to_tokens_with_boundaries(token_ids: list[int]) -> str:
    """Convert tokens to a string with visible token boundaries.

    Args:
        token_ids: List of token IDs to detokenize

    Returns:
        A string representation of tokens with boundaries, e.g.:
        "[token1]⋮[token2]⋮[token3]"
    """
    tokenizer = Qwen25CoderTokenizer()

    # Build string with token boundaries
    parts = []
    for token_id in token_ids:
        token_text = tokenizer.detokenize([token_id])
        parts.append(f"[{token_text}]⋮")

    return "".join(parts)


def count_target_file_space_stats(
    results: dict[str, SmartPasteEvalOutput],
) -> dict[str, int]:
    """Count samples where target_file_content starts with/without space, broken down by correctness.

    Args:
        results: Dictionary mapping request_ids to SmartPasteEvalOutput

    Returns:
        Dictionary containing counts for:
        - starts_with_space_correct: samples starting with space that were correct
        - starts_with_space_incorrect: samples starting with space that were incorrect
        - starts_without_space_correct: samples without leading space that were correct
        - starts_without_space_incorrect: samples without leading space that were incorrect
    """
    stats = {
        "starts_with_space_correct": 0,
        "starts_with_space_incorrect": 0,
        "starts_without_space_correct": 0,
        "starts_without_space_incorrect": 0,
    }

    for output in results.values():
        if output.prompt_input and output.prompt_input.target_file_content:
            if output.prompt_input.target_file_content.startswith(" "):
                if output.correct:
                    stats["starts_with_space_correct"] += 1
                else:
                    stats["starts_with_space_incorrect"] += 1
            else:
                if output.correct:
                    stats["starts_without_space_correct"] += 1
                else:
                    stats["starts_without_space_incorrect"] += 1

    return stats


class NotAFullFile(Exception):
    """Raised when the parts of modified code are not in the prompt."""

    def __init__(self, message: str = "Input doesn't contain the full file."):
        self.message = message
        super().__init__(self.message)


class PromptTooLong(Exception):
    """Raised when the prompt is too long."""

    def __init__(self, prompt_length: int, max_token_budget):
        self.message = f"Prompt too long: {prompt_length} > {max_token_budget}"
        super().__init__(self.message)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "from experimental.yury.smart_paste_eval.utils import (\n", "    get_instruction_host_request,\n", "    run_smart_paste_prod_on_request,\n", ")\n", "\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "USERNAME = \"vpas\"\n", "DEV_URL = f\"https://dev-{USERNAME}.us-central.api.augmentcode.com\"\n", "DEV_RI_URL = f\"https://support.dev-{USERNAME}.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "\n", "PROD_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "\n", "TIMEOUT = 1200\n", "\n", "request_id = \"03149fce-2f4c-4252-aeae-a0ea807450e7\"\n", "tenant_name = \"dogfood-shard\"\n", "\n", "dev_client = AugmentClient(\n", "    url=DEV_URL,\n", "    token=API_TOKEN,\n", ")\n", "augment_model_client_dev = AugmentModelClient(\n", "    dev_client, \"forger-smart-paste-v2-qwen-14b-32k-edit\"\n", ")\n", "\n", "prod_client = AugmentClient(\n", "    url=PROD_URL,\n", "    token=API_TOKEN,\n", ")\n", "augment_model_client = AugmentModelClient(\n", "    prod_client, \"forger-smart-paste-v2-qwen-8b-32k-edit\"\n", ")\n", "\n", "request = get_instruction_host_request(request_id, tenant_name)\n", "print(\"Target file content:\")\n", "print(repr(request.target_file_content))\n", "\n", "_, dev_response = run_smart_paste_prod_on_request(\n", "    augment_model_client=augment_model_client_dev,\n", "    request_id=request_id,\n", "    tenant_name=tenant_name,\n", ")\n", "_, prod_response = run_smart_paste_prod_on_request(\n", "    augment_model_client=augment_model_client,\n", "    request_id=request_id,\n", "    tenant_name=tenant_name,\n", ")\n", "\n", "print(\"Dev response:\")\n", "print(repr(dev_response))\n", "\n", "print(\"Prod response:\")\n", "print(repr(prod_response))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
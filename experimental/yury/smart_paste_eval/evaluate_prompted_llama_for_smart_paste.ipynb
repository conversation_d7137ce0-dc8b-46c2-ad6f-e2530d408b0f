{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import os\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "RI_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "MODEL = \"binks-v12-fp16-longoutput\"\n", "\n", "# URL = \"https://dev-yury.us-central.api.augmentcode.com\"\n", "# MODEL = \"binks-v12-fp16-longoutput\"\n", "# RI_URL = \"https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/\"\n", "\n", "dogfood_client = AugmentClient(\n", "    url=URL,\n", "    token=API_TOKEN,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, MODEL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "\n", "from base.prompt_format_chat.smart_paste_prompt_formatter import (\n", "    SmartPastePromptInput,\n", ")\n", "from experimental.yury.smart_paste_eval.data.utils import SmartPastePromptInputWithRID\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class SmartPasteEvalOutput:\n", "    request_id: str\n", "    response: str\n", "    modified_target_file: str | None = None\n", "    diff_against_original: str | None = None\n", "    diff_against_expected: str | None = None\n", "    correct: bool | None = None\n", "\n", "\n", "def run_smart_paste(\n", "    input_sample: SmartPastePromptInputWithRID,\n", "    prompt: str,\n", "    expected_outputs: list[str],\n", "    extract_modified_file_from_response: Callable[[str], str | None],\n", ") -> SmartPasteEvalOutput:\n", "    message = prompt.format(\n", "        target_path=input_sample.target_path,\n", "        target_file_contents=input_sample.target_file_content,\n", "        suggested_edit=input_sample.code_block,\n", "    )\n", "    chat_response_dogfood = model_client_dogfood.chat(\n", "        message=message,\n", "        selected_code=input_sample.selected_code,\n", "        prefix=input_sample.prefix,\n", "        suffix=input_sample.suffix,\n", "        path=input_sample.path,\n", "        chat_history=input_sample.chat_history,\n", "        context_code_exchange_request_id=input_sample.context_code_exchange_request_id,\n", "        prefix_begin=input_sample.prefix_begin,\n", "        suffix_end=input_sample.suffix_end,\n", "    )\n", "\n", "    result = {\n", "        \"request_id\": chat_response_dogfood.request_id,\n", "        \"response\": chat_response_dogfood.text,\n", "    }\n", "\n", "    modified_file = extract_modified_file_from_response(chat_response_dogfood.text)\n", "\n", "    if modified_file is None:\n", "        return SmartPasteEvalOutput(**result)\n", "\n", "    before_file = File(\n", "        path=input_sample.target_path, contents=input_sample.target_file_content\n", "    )\n", "    after_file = File(path=input_sample.target_path, contents=modified_file)\n", "\n", "    result.update(\n", "        {\n", "            \"modified_target_file\": modified_file,\n", "            \"diff_against_original\": compute_file_diff(\n", "                before_file, after_file, use_smart_header=True, num_context_lines=5\n", "            ),\n", "        }\n", "    )\n", "\n", "    expected_output = None\n", "    for expected_output in expected_outputs:\n", "        correct = modified_file.strip() == expected_output.strip()\n", "        if expected_output.strip() == modified_file.strip():\n", "            # pick the best matching label\n", "            break\n", "\n", "    if expected_output is None and len(expected_outputs) > 0:\n", "        # Pick the first one\n", "        expected_output = expected_outputs[0]\n", "\n", "    if expected_output is not None:\n", "        correct = modified_file.strip() == expected_output.strip()\n", "        expected_file = File(path=input_sample.target_path, contents=expected_output)\n", "        diff_against_expected = compute_file_diff(\n", "            expected_file, after_file, use_smart_header=True, num_context_lines=5\n", "        )\n", "        result.update(\n", "            {\n", "                \"diff_against_expected\": diff_against_expected,\n", "                \"correct\": correct,\n", "            }\n", "        )\n", "    return SmartPasteEvalOutput(**result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    SMART_PASTE_PROMPT,\n", "    extract_modified_file_from_response,\n", ")\n", "\n", "\n", "n_correct = 0\n", "for input_sample, expected_output in dogfood_eval_data.DATA:\n", "    print(f\"Sample: {input_sample.request_id} {input_sample.target_path}\")\n", "    output = run_smart_paste(\n", "        input_sample,\n", "        SMART_PASTE_PROMPT,\n", "        expected_output,\n", "        extract_modified_file_from_response,\n", "    )\n", "\n", "    if output.correct:\n", "        print(\"Correct\")\n", "        n_correct += 1\n", "    else:\n", "        print(f\"Incorrect, see {RI_URL}{output.request_id}\")\n", "        print(output.diff_against_expected)\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ExceedContextLength,\n", ")\n", "import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data\n", "\n", "# Useful when we change the data\n", "importlib.reload(dogfood_eval_data)\n", "print(f\"Loaded {len(dogfood_eval_data.DATA)} samples\")\n", "\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    LIOR_PROMPT,\n", "    lior_extract_modified_file_from_response,\n", ")\n", "\n", "\n", "n_correct = 0\n", "for input_sample, expected_output in dogfood_eval_data.DATA:\n", "    print(f\"Sample: {input_sample.request_id} {input_sample.target_path}\")\n", "    output = run_smart_paste(\n", "        input_sample,\n", "        LIOR_PROMPT,\n", "        expected_output,\n", "        lior_extract_modified_file_from_response,\n", "    )\n", "\n", "    if output.correct:\n", "        print(\"Correct\")\n", "        n_correct += 1\n", "    else:\n", "        print(f\"Incorrect, see {RI_URL}{output.request_id}\")\n", "        print(output.diff_against_expected)\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    SMART_PASTE_PROMPT,\n", "    extract_modified_file_from_response,\n", ")\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "\n", "\n", "n_correct = 0\n", "for input_sample, expected_outputs in hard_eval_data.DATA:\n", "    print(\n", "        f\"Sample: {input_sample.request_id} {input_sample.target_path} {len(expected_outputs)}\"\n", "    )\n", "\n", "    output = run_smart_paste(\n", "        input_sample,\n", "        SMART_PASTE_PROMPT,\n", "        expected_outputs,\n", "        extract_modified_file_from_response,\n", "    )\n", "\n", "    if output.correct:\n", "        print(\"Correct\")\n", "        n_correct += 1\n", "    else:\n", "        print(f\"Incorrect, see {RI_URL}{output.request_id}\")\n", "        print(output.diff_against_expected)\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(hard_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "import experimental.yury.smart_paste_eval.data.hard as hard_eval_data\n", "from experimental.yury.smart_paste_lib.llama_utils import (\n", "    LIOR_PROMPT,\n", "    lior_extract_modified_file_from_response,\n", ")\n", "\n", "# Useful when we change the data\n", "importlib.reload(hard_eval_data)\n", "\n", "\n", "n_correct = 0\n", "for input_sample, expected_outputs in hard_eval_data.DATA:\n", "    print(\n", "        f\"Sample: {input_sample.request_id} {input_sample.target_path} {len(expected_outputs)}\"\n", "    )\n", "\n", "    output = run_smart_paste(\n", "        input_sample,\n", "        LIOR_PROMPT,\n", "        expected_outputs,\n", "        lior_extract_modified_file_from_response,\n", "    )\n", "\n", "    if output.correct:\n", "        print(\"Correct\")\n", "        n_correct += 1\n", "    else:\n", "        print(f\"Incorrect, see {RI_URL}{output.request_id}\")\n", "        print(output.diff_against_expected)\n", "\n", "print(f\"Accuracy: {n_correct} out of {len(hard_eval_data.DATA)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Tests for html_viewer.py."""

import os
from typing import Optional
import pytest
import dataclasses
from dataclasses import dataclass
from unittest.mock import MagicMock, patch
from experimental.yury.data.processing import <PERSON>achi<PERSON><PERSON>ool, PersistentJSONFileCache
from experimental.yury.smart_paste_eval.utils import (
    ModelEvaluationResults,
    SmartPasteEvalOutput,
    ComparisonResult,
    EvalSample,
)
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from base.prompt_format.common import Exchange, PromptChunk
from experimental.yury.smart_paste_eval.html_viewer import (
    generate_eval_output_html,
    detect_language,
    generate_model_eval_html,
)


@dataclass
class MockSmartPastePromptInput:
    """Mock version of SmartPastePromptInput for testing."""

    selected_code: str
    prefix: str
    suffix: str
    path: str
    chat_history: list[dict]
    code_block: str
    target_path: str
    target_file_content: str
    prefix_begin: int
    suffix_end: int
    retrieved_chunks: list[PromptChunk] = dataclasses.field(default_factory=list)


@dataclass
class ChatMessage:
    request_message: str
    response_message: str | None = None


@pytest.fixture
def basic_prompt_input():
    """Fixture for basic prompt input with chat history."""
    return SmartPastePromptInput(
        path="test.py",
        selected_code="def hello():\n    print('old')",
        code_block="def hello():\n    print('old')\n\ndef other():\n    pass",
        target_path="test.py",
        chat_history=[
            Exchange(
                request_message="Fix the function", response_text="Here's the fix"
            ),
            Exchange(request_message="Update the print message", response_text=None),
        ],
        prefix="",
        suffix="",
        target_file_content="def hello():\n    print('old')\n\ndef other():\n    pass",
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="def hello():\n    print('old')",
                path="test.py",
                char_start=0,
                char_end=27,
            )
        ],
    )


@pytest.fixture
def basic_eval_output(basic_prompt_input):
    """Fixture for basic eval output."""
    return SmartPasteEvalOutput(
        request_id="test123",
        response="def hello():\n    print('new')",
        prompt_input=basic_prompt_input,
        eval_sample=EvalSample(
            uuid="test123",
            chat_request_id="chat123",
            edit_request_id="edit123",
            category="category1",
        ),
        correct=True,
        diff_against_expected=None,
    )


@pytest.fixture
def eval_output_with_diffs(basic_prompt_input):
    """Fixture for eval output with diffs."""
    return SmartPasteEvalOutput(
        request_id="test456",
        response="def hello():\n    print('different')",
        prompt_input=basic_prompt_input,
        eval_sample=EvalSample(
            uuid="test456",
            chat_request_id="chat456",
            edit_request_id="edit456",
            category="category2",
        ),
        correct=False,
        diff_against_expected="@@ -1,2 +1,2 @@\n def hello():\n-    print('old')\n+    print('different')",
    )


@pytest.fixture
def mock_prompt_input():
    """Fixture for MockSmartPastePromptInput."""
    return MockSmartPastePromptInput(
        selected_code="print('selected')",
        prefix="def test():\n    ",
        suffix="\n    return True",
        path="test.py",
        chat_history=[{"request_message": "Hello", "response_text": "Hi"}],
        code_block="def test():\n    print('test')\n    return True",
        target_path="target.py",
        target_file_content="original content",
        prefix_begin=0,
        suffix_end=10,
        retrieved_chunks=[],
    )


@pytest.fixture
def eval_output_with_prompt(mock_prompt_input):
    """SmartPasteEvalOutput fixture with prompt input."""
    return SmartPasteEvalOutput(
        request_id="test123",
        response="def hello():\n    print('hello')",
        modified_target_file=None,
        diff_against_original=None,
        diff_against_expected=None,
        correct=True,
        prompt_input=mock_prompt_input,
    )


def test_generate_eval_output_html_basic(basic_eval_output):
    """Test basic HTML generation with minimal input."""
    html = generate_eval_output_html(basic_eval_output)

    # Basic structure checks
    assert "<!DOCTYPE html>" in html
    assert "test123" in html  # Request ID
    assert "✓ Correct" in html  # Status

    # Check code is present (with syntax highlighting)
    assert "hello" in html  # Function name
    assert "print" in html  # Print statement

    # Check sections exist
    assert 'id="response"' in html
    assert 'id="modified"' in html
    assert 'id="diff-original"' in html
    assert 'id="diff-expected"' in html


def test_generate_eval_output_html_with_diffs(eval_output_with_diffs):
    """Test HTML generation with diff content."""
    html = generate_eval_output_html(eval_output_with_diffs)

    # Check diff formatting
    assert 'class="diff-add"' in html
    assert 'class="diff-info"' in html
    assert "status-incorrect" in html  # Status should be incorrect


def test_generate_eval_output_html_with_prompt_input(eval_output_with_prompt):
    """Test HTML generation with prompt input data."""
    html = generate_eval_output_html(eval_output_with_prompt)

    # Check prompt input section exists and contains data
    assert 'id="prompt-input"' in html
    assert "test.py" in html  # Path
    assert "selected" in html  # Selected code
    assert "test" in html  # Code block
    assert "target.py" in html  # Target path
    assert "Hello" in html  # Chat history

    # Check syntax highlighting is applied
    assert 'class="highlight"' in html
    assert "<span class=" in html  # Pygments adds syntax highlighting spans


def test_generate_eval_output_html_with_different_languages():
    """Test HTML generation with different file extensions."""
    # Python file
    eval_output_py = SmartPasteEvalOutput(
        request_id="test_py",
        response="def hello():\n    print('hello')",
        modified_target_file=None,
        diff_against_original=None,
        diff_against_expected=None,
        correct=True,
        prompt_input=MockSmartPastePromptInput(
            selected_code="print('test')",
            prefix="",
            suffix="",
            path="test.py",
            chat_history=[],
            code_block="print('test')",
            target_path="target.py",
            target_file_content="",
            prefix_begin=0,
            suffix_end=0,
            retrieved_chunks=[],
        ),
    )

    # JavaScript file
    eval_output_js = SmartPasteEvalOutput(
        request_id="test_js",
        response="function hello() {\n    console.log('hello');\n}",
        modified_target_file=None,
        diff_against_original=None,
        diff_against_expected=None,
        correct=True,
        prompt_input=MockSmartPastePromptInput(
            selected_code="console.log('test')",
            prefix="",
            suffix="",
            path="test.js",
            chat_history=[],
            code_block="console.log('test')",
            target_path="target.js",
            target_file_content="",
            prefix_begin=0,
            suffix_end=0,
            retrieved_chunks=[],
        ),
    )

    html_py = generate_eval_output_html(eval_output_py)
    html_js = generate_eval_output_html(eval_output_js)

    # Both should have syntax highlighting but with different lexers
    assert 'class="highlight"' in html_py
    assert 'class="highlight"' in html_js
    # Python keywords
    assert "def" in html_py
    # JavaScript keywords
    assert "function" in html_js


def test_language_detection_from_content():
    """Test language detection from code content."""

    # Python detection
    python_code = "def hello():\n    print('Hello!')"
    assert detect_language(None, python_code) == "Python"

    # JavaScript detection
    js_code = "function hello() { console.log('Hello!'); }"
    assert detect_language(None, js_code) == "JavaScript"

    # C detection
    c_code = "#include <stdio.h>\nint main() { return 0; }"
    assert detect_language(None, c_code) == "C"


def test_language_detection_precedence():
    """Test that content detection takes precedence over file extension."""

    # Python code in a .txt file should be detected as Python
    python_code = "def hello():\n    print('Hello!')"
    assert detect_language("file.txt", python_code) == "Python"

    # JavaScript code in a .py file should be detected as JavaScript
    js_code = "function hello() { console.log('Hello!'); }"
    assert detect_language("file.py", js_code) == "JavaScript"


def test_language_detection_fallback():
    """Test fallback behavior in language detection."""

    # Empty content should fall back to file extension
    assert detect_language("test.py", "") == "Python"

    # No extension and no clear markers should fall back to Text
    assert detect_language(None, "some random content") == "Text"


def test_diff_formatting_basic():
    """Test basic diff formatting."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    diff_text = (
        "@@ -1,3 +1,3 @@\n"
        " def test():\n"
        "-    print('old')\n"
        "+    print('new')\n"
        " def other():\n"
    )

    formatted = format_diff(diff_text)
    print(formatted)

    expected = """
    <div class="diff">
        <span class="diff-info">@@ -1,3 +1,3 @@</span>
        <span class="diff-context"><span class="whitespace-space"></span>def<span class="whitespace-space"></span>test():</span>
        <span class="diff-remove">-<span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span>print(&#x27;old&#x27;)</span>
        <span class="diff-add">+<span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span>print(&#x27;new&#x27;)</span>
        <span class="diff-context"><span class="whitespace-space"></span>def<span class="whitespace-space"></span>other():</span>
    </div>
    """

    assert_html_equal(formatted, expected)


def test_diff_formatting_empty():
    """Test formatting empty diff."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    formatted = format_diff("")
    assert formatted == ""

    formatted = format_diff(None)
    assert formatted == ""


def test_diff_formatting_whitespace():
    """Test that all whitespace characters are preserved and shown in diff formatting."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    diff_text = (
        "@@ -1,3 +1,3 @@\n"
        " def test():\n"
        "-    x = 1\t# tab here\n"
        "+    x = 2 # spaces between words\n"
        "     y = 3  # double space before comment\n"
    )

    formatted = format_diff(diff_text)

    # Check that all whitespace is preserved with proper spans
    assert (
        '<span class="diff-remove">-<span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span>x<span class="whitespace-space"></span>=<span class="whitespace-space"></span>1<span class="whitespace-tab"></span>#<span class="whitespace-space"></span>tab<span class="whitespace-space"></span>here</span>'
        in formatted
    )
    assert (
        '<span class="diff-add">+<span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span>x<span class="whitespace-space"></span>=<span class="whitespace-space"></span>2<span class="whitespace-space"></span>#<span class="whitespace-space"></span>spaces<span class="whitespace-space"></span>between<span class="whitespace-space"></span>words</span>'
        in formatted
    )
    assert (
        '<span class="diff-context"><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span><span class="whitespace-space"></span>y<span class="whitespace-space"></span>=<span class="whitespace-space"></span>3<span class="whitespace-space"></span><span class="whitespace-space"></span>#<span class="whitespace-space"></span>double<span class="whitespace-space"></span>space<span class="whitespace-space"></span>before<span class="whitespace-space"></span>comment</span>'
        in formatted
    )


def test_diff_formatting_multiple_hunks():
    """Test formatting diff with multiple hunks."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    diff_text = (
        "@@ -1,3 +1,3 @@\n"
        " def test1():\n"
        "-    x = 1\n"
        "+    x = 2\n"
        " def other1():\n"
        "@@ -10,3 +10,3 @@\n"
        " def test2():\n"
        "-    y = 1\n"
        "+    y = 2\n"
        " def other2():\n"
    )

    formatted = format_diff(diff_text)

    # Check both hunks are present
    assert "test1()" in formatted
    assert "test2()" in formatted
    assert (
        formatted.count('class="diff-info">@@') == 2
    )  # Count actual hunk headers in spans

    # Check order is preserved
    test1_pos = formatted.find("test1()")
    test2_pos = formatted.find("test2()")
    assert test1_pos < test2_pos


def test_diff_formatting_special_chars():
    """Test formatting diff with special HTML characters."""
    from experimental.yury.smart_paste_eval.html_viewer import format_diff

    diff_text = (
        "@@ -1,3 +1,3 @@\n"
        " def test():\n"
        '-    print("<old>")\n'
        '+    print("<new>")\n'
        " def other():\n"
    )

    formatted = format_diff(diff_text)

    # Check HTML escaping
    assert "&lt;old&gt;" in formatted
    assert "&lt;new&gt;" in formatted


@pytest.fixture
def model_eval_results(basic_eval_output, eval_output_with_diffs):
    """Fixture for ModelEvaluationResults."""
    return ModelEvaluationResults(
        total_correct=3,
        total_processed=5,
        total_skipped=1,
        category_stats={
            "category1": {"correct": 2, "total": 3},
            "category2": {"correct": 1, "total": 2},
        },
        sample_results={
            "uuid1": basic_eval_output,
            "uuid2": eval_output_with_diffs,
        },
        model_name="test-model",
        url="https://test-url.com",
    )


def test_generate_model_eval_html_basic(model_eval_results):
    """Test basic HTML generation for model evaluation results."""
    html = generate_model_eval_html(model_eval_results)

    # Check basic structure
    assert "<!DOCTYPE html>" in html
    assert "Model Evaluation Results" in html

    # Check model info
    assert "test-model" in html
    assert "https://test-url.com" in html

    # Check overall statistics
    assert "Total Correct" in html
    assert ">3<" in html  # total_correct value
    assert ">5<" in html  # total_processed value
    assert ">1<" in html  # total_skipped value
    assert "60.0%" in html  # accuracy (3/5 * 100)


def test_generate_model_eval_html_category_stats(model_eval_results):
    """Test category statistics in model evaluation HTML."""
    html = generate_model_eval_html(model_eval_results)

    # Check category statistics section
    assert "Category Statistics" in html
    assert "category1" in html
    assert "category2" in html

    # Check category1 stats
    assert ">2<" in html  # correct
    assert ">3<" in html  # total
    assert "66.7%" in html  # accuracy for category1 (2/3 * 100)

    # Check category2 stats
    assert ">1<" in html  # correct
    assert ">2<" in html  # total
    assert "50.0%" in html  # accuracy for category2 (1/2 * 100)


def test_generate_model_eval_html_sample_results(model_eval_results):
    """Test sample results in model evaluation HTML."""
    html = generate_model_eval_html(model_eval_results)

    # Debug print
    print("\nHTML output:")
    print(html)

    # Check sample results sections
    assert "onclick=\"toggleSection('result-uuid1')\"" in html
    assert "onclick=\"toggleSection('result-uuid2')\"" in html

    # Check first result (basic_eval_output)
    assert "status-correct" in html  # First result is correct
    assert "def hello()" in html  # Code from first result

    # Check second result (eval_output_with_diffs)
    assert "status-incorrect" in html  # Second result is incorrect
    assert "@@ -1,2 +1,2 @@" in html  # Diff from second result


def test_save_eval_output_html(basic_eval_output, tmp_path):
    """Test saving individual eval output as HTML."""
    from experimental.yury.smart_paste_eval.html_viewer import save_eval_output_html

    output_path = tmp_path / "eval_output.html"
    save_eval_output_html(basic_eval_output, output_path)

    # Check that file was created
    assert output_path.exists()

    # Check content
    content = output_path.read_text()
    assert "<!DOCTYPE html>" in content
    assert "test123" in content  # Request ID
    assert "✓ Correct" in content  # Status

    # Check code (with syntax highlighting)
    assert '<span class="k">def</span>' in content  # 'def' keyword
    assert '<span class="nf">hello</span>' in content  # function name
    assert '<span class="s1">&#39;new&#39;</span>' in content  # string literal


def test_save_model_eval_html_with_individual_outputs(model_eval_results, tmp_path):
    """Test saving model eval HTML with individual eval outputs."""
    from experimental.yury.smart_paste_eval.html_viewer import save_model_eval_html

    output_path = tmp_path / "model_eval.html"
    save_model_eval_html(model_eval_results, output_path)

    # Check that main file was created
    assert output_path.exists()

    # Check that individual eval output files were created
    assert (tmp_path / "eval_output_uuid1.html").exists()
    assert (tmp_path / "eval_output_uuid2.html").exists()

    # Check main file content
    content = output_path.read_text()
    assert 'href="eval_output_uuid1.html"' in content
    assert 'href="eval_output_uuid2.html"' in content

    # Check individual file content
    content1 = (tmp_path / "eval_output_uuid1.html").read_text()
    assert "test123" in content1  # Request ID from first output
    assert "✓ Correct" in content1  # Status from first output

    content2 = (tmp_path / "eval_output_uuid2.html").read_text()
    assert "test456" in content2  # Request ID from second output
    assert "✗ Incorrect" in content2  # Status from second output


def test_generate_model_eval_html_with_links(model_eval_results, tmp_path):
    """Test that generate_model_eval_html includes links when output_dir is provided."""
    from experimental.yury.smart_paste_eval.html_viewer import generate_model_eval_html

    # Generate HTML without output_dir
    html_without_links = generate_model_eval_html(model_eval_results)
    assert 'href="eval_output_uuid1.html"' not in html_without_links
    assert 'href="eval_output_uuid2.html"' not in html_without_links

    # Generate HTML with output_dir
    html_with_links = generate_model_eval_html(model_eval_results, tmp_path)
    assert 'href="eval_output_uuid1.html"' in html_with_links
    assert 'href="eval_output_uuid2.html"' in html_with_links


def assert_html_equal(formatted: str, expected: str) -> None:
    """Compare two HTML strings after stripping whitespace."""
    formatted_lines = formatted.strip().splitlines()
    expected_lines = expected.strip().splitlines()

    if len(formatted_lines) != len(expected_lines):
        raise AssertionError(
            f"\nLine count mismatch: expected {len(expected_lines)}, got {len(formatted_lines)}"
        )

    for i, (f_line, e_line) in enumerate(zip(formatted_lines, expected_lines)):
        f_stripped = f_line.strip()
        e_stripped = e_line.strip()
        if f_stripped != e_stripped:
            raise AssertionError(
                f"\nMismatch at line {i + 1}:\n"
                f"Expected: {e_stripped}\n"
                f"Got:      {f_stripped}"
            )

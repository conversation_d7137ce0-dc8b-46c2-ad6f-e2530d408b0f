{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import research.eval.harness.factories as factories\n", "from research.models import (\n", "    GenerationOptions,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from research.models.fastforward_models import StarCoder2_FastForward\n", "from base.prompt_format_smart_paste.forger_prompt_formatter import (\n", "    <PERSON>ger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ForgerSmartPasteTokenApportionment,\n", ")\n", "\n", "\n", "MAX_PROMPT_BUDGET = 16384\n", "MAX_GENERATED_TOKENS = MAX_PROMPT_BUDGET\n", "MAX_TOKEN_BUDGET = MAX_PROMPT_BUDGET + MAX_GENERATED_TOKENS\n", "\n", "\n", "config = {\n", "    \"model\": {\n", "        \"name\": \"starcoder2_fastforward\",\n", "        # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86645/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v12_8K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"edeac558f39b460a2a880ab86c6308c5cca166915645552506e8ebb93af41e18\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/815/trials/815/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"3ec9836aac3e44085a8cbc77b9f1cecf59329c56e3e48b8630239adc65723031\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/810/trials/810/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscne<PERSON><PERSON>_claude_and_naive_ff\",\n", "        # \"checkpoint_hash\": \"5b9d9586bdebab32f8fe69db30df5c073bc12b7fab21c3a945aa29e4b171ecb8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/867/trials/867/overview\n", "        # \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_16K_claude_and_naive_fixbs_ff\",\n", "        # \"checkpoint_hash\": \"ad4268ad0c404c5513ab221727fcb9e9082e6ae6c6e62b65fd973407eee7c8d8\",\n", "        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/902/trials/902/overview\n", "        \"model_path\": \"/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_32K_claude_and_naive_fixbs_mp2_bs128_ff\",\n", "        \"checkpoint_hash\": \"2841da84db20b2457598603b404ae21299d64fd5797204fbb8df2d8b44dea306\",\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": MAX_GENERATED_TOKENS,\n", "    },\n", "}\n", "\n", "generation_options = GenerationOptions(**config[\"generation_options\"])\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "token_apportionment = ForgerSmartPasteTokenApportionment(\n", "    path_len=256,\n", "    prefix_len=MAX_PROMPT_BUDGET,\n", "    suffix_len=MAX_PROMPT_BUDGET,\n", "    max_prompt_len=MAX_PROMPT_BUDGET,\n", "    message_len=128,\n", ")\n", "\n", "prompt_formatter = ForgerPromptFormatter(tokenizer, token_apportionment)\n", "\n", "model = StarCoder2_FastForward(\n", "    model_path=Path(config[\"model\"][\"model_path\"]),\n", "    checkpoint_sha256=config[\"model\"][\"checkpoint_hash\"],\n", ")\n", "# model = factories.create_model(config[\"model\"])\n", "model.load()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "Loaded 298 samples\n"]}], "source": ["from megatron.data.indexed_dataset import make_dataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "# DATA_PATH = \"/mnt/efs/augment/user/yury/smart_paste/data/v13_2_sc2_fixscnewline_16K/valid_claude_emptysc\"\n", "DATA_PATH = \"/mnt/efs/augment/user/yury/smart_paste/data/v13_2_sc2_fixscnewline_16K/valid_claude\"\n", "\n", "ds = make_dataset(DATA_PATH, impl=\"mmap\", skip_warmup=True)\n", "print(f\"Loaded {len(ds)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_utils import compute_file_diff, File\n", "\n", "\n", "def compute_str_diff(before, after):\n", "    before_file = File(path=\"\", contents=before)\n", "    after_file = File(path=\"\", contents=after)\n", "    return compute_file_diff(\n", "        before_file, after_file, use_smart_header=True, num_context_lines=5\n", "    )\n", "\n", "\n", "def process_sample(sample):\n", "    tokens = sample\n", "    tokens = [x for x in tokens if x != -tokenizer.special_tokens.padding]\n", "    prompt = [-x for x in tokens if x < 0]\n", "    label = [x for x in tokens if x >= 0]\n", "    assert label[-1] == tokenizer.special_tokens.eos\n", "    label = label[:-1]\n", "    return prompt, label\n", "\n", "\n", "START_INDEX = 0\n", "\n", "for index in range(START_INDEX, len(ds)):\n", "    sample = ds[index]\n", "    prompt_tokens, label_tokens = process_sample(sample)\n", "    label = tokenizer.detokenize(label_tokens)\n", "    output = model.raw_generate(prompt_tokens, generation_options)\n", "    if label != output:\n", "        print(f\"Sample {index} is incorrect\")\n", "        print(compute_str_diff(label, output))\n", "        print(\"\\n======\\n\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["from datetime import datetime\n", "\n", "from redis import RedisError\n", "\n", "from utility import messageFactory\n", "from utility.create_config import create_config\n", "from utility.redisUsersConnection import RedisUsersConnection\n", "\n", "\n", "class UserReminder:\n", "\n", "    def __init__(self):\n", "        self.__mf = messageFactory.MessageFactory()\n", "        self.month = datetime.now().date().month\n", "        self.day = datetime.now().date().day\n", "        self.users = RedisUsersConnection()\n", "\n", "\n", "    def send_message(self):\n", "        user_stats = self.__produce_users_info()\n", "        self.__mf.send_user_reminder_message(user_stats)\n", "\n", "    def __produce_users_info(self):\n", "        approved_table = self.__generate_table('approved', ['User', 'Name', 'Surname', 'SDO Rights', 'Vendor Rights', 'Organization', 'Email'])\n", "        pending_table = self.__generate_table('temp', ['User', 'Name', 'Surname', 'Organization', 'Email'])\n", "        \n", "        return f\"\"\"\n", "        <h2>Approved Users</h2>\n", "        {approved_table}\n", "        <h2>Users Pending Approval</h2>\n", "        {pending_table}\n", "        \"\"\"\n", "\n", "    def __generate_table(self, user_type, headers):\n", "        table = \"<table border='1'><tr>\"\n", "        for header in headers:\n", "            table += f\"<th>{header}</th>\"\n", "        table += \"</tr>\"\n", "\n", "        try:\n", "            for id in self.users.get_all(user_type):\n", "                fields = self.users.get_all_fields(id)\n", "                table += \"<tr>\"\n", "                if user_type == 'approved':\n", "                    table += f\"<td>{fields['username']}</td><td>{fields['first-name']}</td><td>{fields['last-name']}</td>\"\n", "                    table += f\"<td>{fields['access-rights-sdo']}</td><td>{fields['access-rights-vendor']}</td>\"\n", "                    table += f\"<td>{fields['models-provider']}</td><td>{fields['email']}</td>\"\n", "                else:\n", "                    table += f\"<td>{fields['username']}</td><td>{fields['first-name']}</td><td>{fields['last-name']}</td>\"\n", "                    table += f\"<td>{fields['models-provider']}</td><td>{fields['email']}</td>\"\n", "                table += \"</tr>\"\n", "        except RedisError:\n", "            pass\n", "\n", "        table += \"</table>\"\n", "        return table\n", "\n", "\n", "if __name__ == '__main__':\n", "    ur = UserReminder()\n", "    ur.send_message()\n", "\n"]}], "source": ["prompt_tokens, label_tokens = process_sample(ds[7])\n", "label = tokenizer.detokenize(label_tokens)\n", "print(label)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
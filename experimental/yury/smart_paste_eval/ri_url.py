URL_TO_RI_URL = {
    "https://staging-shard-0.api.augmentcode.com": "https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/",
    "https://dev-yury.us-central.api.augmentcode.com": "https://support.dev-yury.t.us-central1.dev.augmentcode.com/t/augment/request/",
    "https://dev-vpas.us-central.api.augmentcode.com": "https://support.dev-vpas.t.us-central1.dev.augmentcode.com/t/augment/request/",
    "https://test-url.com": "https://test-url.com/request/",
}


def get_ri_url(url, request_id):
    if request_id is None or url not in URL_TO_RI_URL:
        return ""
    return URL_TO_RI_URL[url] + request_id

"""Functions for getting <PERSON>'s analysis of smart paste failures."""

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from typing import Optional
from experimental.yury.smart_paste_eval.utils import (
    ModelEvaluationResults,
    SmartPasteEvalOutput,
)
from experimental.yury.data.processing import persistent_cache
from tqdm import tqdm

REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
TEMPERATURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8


@persistent_cache("/mnt/efs/augment/user/yury/smart_paste_eval/claude_analysis.jsonl")
def get_claude_analysis(eval_output: SmartPasteEvalOutput) -> Optional[str]:
    """Get <PERSON>'s analysis of a smart paste failure.

    Args:
        eval_output: The evaluation output to analyze

    Returns:
        <PERSON>'s analysis as a string, or None if analysis couldn't be obtained
    """
    client = AnthropicVertexAiClient(
        project_id=PROJECT_ID,
        region=REGION,
        model_name=MODEL_NAME,
        temperature=TEMPERATURE,
        max_output_tokens=MAX_OUTPUT_TOKENS,
    )

    prompt = f"""
Smart paste model is a model that given a user request message and a code block with changes and a target file, pastes the code block into the target file in the correct place merging the code appropirately.
Code block may also contain information about what lines should be removed
Some comments in the code block are there to help the smart paste model to figure out what to change
and these comments should not be included in the final output of the smart paste model.

This smart paste model was evaluated on a given sample and it's output was compared with expected output
You are an expert at evaluating such model.
Your task is to:
1. Understand if smart paste model produced incorrect output or the expected output of this sample is incorrect.
2. Provide details on what exactly is wrong

Here are the date:

User request message:
{eval_output.eval_sample.request_message}

Code block to be pasted:
```
{eval_output.prompt_input.code_block}
```

Target path(can be used to determine programming language):
{eval_output.prompt_input.target_path}

Smart paste model response:
```
{eval_output.response}
```

Diff against original:
```
{eval_output.diff_against_original}
```

Diff against expected:
```
{eval_output.diff_against_expected}
```

Keep your response concise and to the point.
"""

    try:
        response = client.client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_OUTPUT_TOKENS,
            messages=[{"role": "user", "content": prompt}],
            temperature=TEMPERATURE,
        )
        return response.content[0].text
    except Exception as e:
        print(f"Error getting Claude analysis: {e}")
        return None


def add_claude_analysis_model_eval(results: ModelEvaluationResults) -> None:
    """Add Claude's analysis to each incorrect SmartPasteEvalOutput in the results."""
    for output in tqdm(
        results.sample_results.values(), desc="Analyzing failures with Claude"
    ):
        if not output.correct:
            output.claude_analysis = get_claude_analysis(output)

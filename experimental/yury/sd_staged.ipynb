{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["([((4, 2), 2), ((2, 3), 1)], 0)\n", "([(5,), (4, 2), (2, 3)], 0)\n", "([(5,), (4,), (2,)], 0)\n"]}], "source": ["import warnings\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(100000))\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])\n", "\n", "\n", "\n", "class LongestOverlapLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd, allow_predicting_less_than_k_tokens=False, n_predictions_per_overlap=None):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "        self.allow_predicting_less_than_k_tokens = allow_predicting_less_than_k_tokens\n", "        self.n_predictions_per_overlap = n_predictions_per_overlap\n", "        if self.n_predictions_per_overlap:\n", "            assert self.allow_predicting_less_than_k_tokens\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, return_overlap=False):        \n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        n_sd = min(n_sd, len(self.tokens) - 1)\n", "        \n", "        if n_sd == 0:\n", "            return [], 0\n", "        \n", "        assert self.n_parallel_sd > 0\n", "        if len(self.tokens) < n_sd:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return [], 0\n", "        \n", "        if self.allow_predicting_less_than_k_tokens:\n", "            searchable_tokens = self.tokens[:-1]\n", "        else:\n", "            searchable_tokens = self.tokens[:-n_sd]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return [], 0\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue           \n", "                if self.n_predictions_per_overlap is not None:     \n", "                    assert l > 0\n", "                    if l < len(self.n_predictions_per_overlap):\n", "                        current_n_sd = self.n_predictions_per_overlap[l]\n", "                    else:\n", "                        current_n_sd = self.n_predictions_per_overlap[0]\n", "                    current_n_sd = min(current_n_sd, n_sd)\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + current_n_sd + 1, len(self.tokens))])\n", "                else:\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + n_sd + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if not self.allow_predicting_less_than_k_tokens:\n", "                    assert len(current_prediction) == n_sd\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                if return_overlap:\n", "                    predictions.append((current_prediction, l))\n", "                else:\n", "                    predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= self.n_parallel_sd:\n", "                    break\n", "            if len(predictions) >= self.n_parallel_sd:\n", "                break                \n", "        return predictions, 0\n", "\n", "\n", "predictor = LongestOverlapLM(2, 3)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2, True))\n", "\n", "predictor = LongestOverlapLM(2, 3, True)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))\n", "\n", "predictor = LongestOverlapLM(2, 3, True, n_predictions_per_overlap=[1])\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "Socket error: [Errno 98] Address already in use; Port 6001 is in use on 0.0.0.0. Checking 6002...\n", "> initializing torch distributed ...\n", "[2023-10-10 16:44:31,665] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-10-10 16:44:31,752] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=**************, master_port=6002\n", "[2023-10-10 16:44:31,753] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-10-10 16:44:31,757] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev:00750] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-10 16:44:32,001] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-10-10 16:44:32,276] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-10 16:44:32,277] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-10 16:44:32,426] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-10 16:44:32,427] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-10 16:44:32,427] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-10 16:44:32,428] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-10 16:44:32,428] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-10-10 16:44:32,429] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-10 16:44:32,429] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-10 16:44:32,429] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-10 16:44:32,430] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-10 16:44:32,430] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-10 16:44:32,430] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-10 16:44:32,431] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-10 16:44:32,431] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-10 16:44:32,432] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-10 16:44:32,432] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-10 16:44:32,432] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-10 16:44:32,433] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-10 16:44:32,433] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-10 16:44:32,433] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-10 16:44:32,434] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-10 16:44:32,434] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-10 16:44:32,434] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-10 16:44:32,435] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-10 16:44:32,435] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-10 16:44:32,438] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-10 16:44:32,438] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-10 16:44:32,439] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-10 16:44:32,439] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-10 16:44:32,439] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-10 16:44:32,440] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-10-10 16:44:32,440] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-10 16:44:32,440] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-10 16:44:32,441] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-10 16:44:32,441] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-10 16:44:32,441] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-10 16:44:32,442] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-10 16:44:32,442] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-10 16:44:32,442] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-10 16:44:32,443] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-10 16:44:32,443] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-10 16:44:32,444] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-10 16:44:32,444] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-10 16:44:32,445] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-10 16:44:32,445] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-10 16:44:32,445] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-10 16:44:32,447] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-10 16:44:32,448] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.16033387184143066 seconds\n", "[2023-10-10 16:44:33,109] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-10 16:44:33,153] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-10-10 16:44:33,188] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "[2023-10-10 16:44:33,327] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-10-10 16:44:33,380] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-10-10 16:44:33,433] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-10-10 16:44:33,485] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-10-10 16:44:33,540] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-10-10 16:44:33,595] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-10-10 16:44:33,647] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-10-10 16:44:33,704] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-10-10 16:44:33,761] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-10-10 16:44:33,811] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-10-10 16:44:33,860] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-10-10 16:44:33,909] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-10-10 16:44:33,955] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-10-10 16:44:34,012] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-10-10 16:44:34,066] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-10-10 16:44:34,117] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-10-10 16:44:34,166] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-10-10 16:44:34,215] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-10-10 16:44:34,262] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-10-10 16:44:34,306] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-10-10 16:44:34,355] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-10-10 16:44:34,406] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-10-10 16:44:34,457] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-10-10 16:44:34,504] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-10-10 16:44:34,552] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-10-10 16:44:34,553] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-10-10 16:44:34,665] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["# Based on this https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e\n", "model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7fdc775794c0>\n"]}], "source": ["print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["EOS_TOKEN_ID = model.tokenizer.tokenize('<|endoftext|>')[0]\n", "EOS_TOKEN_ID"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "data_fim_retrieval = []\n", "\n", "with open('/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/data_32l.16B_generations.jsonl') as f:\n", "    for line in f:\n", "        datum = json.loads(line)\n", "        if EOS_TOKEN_ID in datum['predictions']:\n", "            datum['label'] = datum['predictions'][:datum['predictions'].index(EOS_TOKEN_ID) + 1]\n", "        data_fim_retrieval.append(datum)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 2\n", "PREDICITON = self\n", "COST 2\n", "PREDICITON = self.log.info('Status posted at\n"]}], "source": ["import copy\n", "import gc\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "class NeuralLM:\n", "    \n", "    def __init__(self, n_rounds, draft_model=None):\n", "        self.n_rounds = n_rounds\n", "        self.draft_model = draft_model        \n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def get_draft_model_predictions(self, context, n_sd):\n", "        if self.draft_model is not None:\n", "            draft_predictions, draft_predictions_cost = self.draft_model.predict_next_k_tokens(context, n_sd)\n", "            if len(draft_predictions) == 1:\n", "                draft_predictions = list(draft_predictions[0])\n", "            else:\n", "                # We don't support parallel SD yet                \n", "                assert len(draft_predictions) == 0\n", "        else:\n", "            draft_predictions = []\n", "            draft_predictions_cost = 0\n", "        return draft_predictions, draft_predictions_cost\n", "\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        predictions = []    \n", "        \n", "        n_rounds = 0\n", "\n", "        while len(predictions) < n_sd and n_rounds < self.n_rounds:\n", "            gc.collect()\n", "            torch.cuda.empty_cache()\n", "\n", "            draft_predictions, _ = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions) - 1)\n", "            n_rounds += 1\n", "\n", "            context_tokens = context + predictions + draft_predictions\n", "            context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([context_tokens], dtype=torch.int64))\n", "            model_inputs = (\n", "                context_tokens_tensor,  # input_ids\n", "                position_ids,  # position_ids\n", "                attention_mask,  # attention_mask\n", "            )            \n", "\n", "            model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "            eval_result = text_gen_utils.forward_model(\n", "                model=model.neox_model,\n", "                model_inputs=model_inputs,\n", "                is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                timers=None,\n", "            )\n", "            \n", "            model.neox_model.module.clear_cache()  # clear the k,v cache after\n", "            # We only work with batch size 1 for now\n", "            assert eval_result.shape[0] == 1\n", "            eval_result = eval_result[0]\n", "\n", "            # cursed stuff\n", "            assert eval_result.shape[0] == len(context_tokens)\n", "\n", "            # Disallow special tokens\n", "            # eval_result[:, ALL_SPECIAL_TOKENS_IDS] = -10000            \n", "            logprobs = F.log_softmax(eval_result.float(), dim=-1)\n", "            token_ids = logprobs.argmax(axis=-1).cpu().numpy()\n", "\n", "            gc.collect()\n", "            torch.cuda.empty_cache()\n", "            \n", "            for index in range(len(context + predictions) - 1, len(context_tokens)):                \n", "                token_id = token_ids[index]\n", "                # assert token_id not in ALL_SPECIAL_TOKENS_IDS_SET, (token_id, model.tokenizer.detokenize([token_id]))\n", "                predictions.append(token_id)\n", "\n", "                if index < len(context_tokens) - 1 and token_id != context_tokens[index + 1]:\n", "                    break      \n", "\n", "        # Do one final round of draft predictions since it's free\n", "        if len(predictions) < n_sd:            \n", "            draft_predictions, _ = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions))\n", "            predictions.extend(draft_predictions)            \n", "\n", "        gc.collect()\n", "        torch.cuda.empty_cache()\n", "        return [tuple(predictions)], n_rounds\n", "\n", "\n", "lm = NeuralLM(2)\n", "p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 10)\n", "print('COST', cost)\n", "print('PREDICITON', tokenizer.detokenize(p[0]))\n", "\n", "lm = NeuralLM(2, draft_model=LongestOverlapLM(12, 1, True))\n", "lm.fit(data_fim_retrieval[0]['context'])\n", "p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 10)\n", "print('COST', cost)\n", "print('PREDICITON', tokenizer.detokenize(p[0]))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'context_length': 3726, 'label_length': 12, 'n_rounds': 2, 'n_draft_rounds': 10, 'average_draft_rounds': 5.0, 'average_draft_tokens_accepted': 10.0, 'simulation_time': 7496.811151504517, 'beam_size': 1.0, 'n_unique_first_token': 1.0, 'n_unique_two_tokens': 1.0}\n", "{'context_length': 3726, 'label_length': 12, 'n_rounds': 1, 'n_draft_rounds': 4, 'average_draft_rounds': 4.0, 'average_draft_tokens_accepted': 11.0, 'simulation_time': 3112.156629562378, 'beam_size': 1.0, 'n_unique_first_token': 1.0, 'n_unique_two_tokens': 1.0}\n"]}], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample):\n", "    context = sample['context']\n", "    if not isinstance(context, list):\n", "        context = context.tolist()\n", "    label = sample['label']\n", "    if not isinstance(label, list):\n", "        label = label.tolist()        \n", "\n", "    n_rounds, n_draft_rounds = 0, 0\n", "    index = 0\n", "    beam_sizes, simulation_time, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    draft_tokens_accepted = []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 1\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            # print('CURRENT GENERATION', tokenizer.detokenize(label[:index]))\n", "            beam_of_predictions, current_draft_rounds = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict)\n", "\n", "            # print('PREDICITONS for max_tokens_to_predict=', max_tokens_to_predict)\n", "            # for p in beam_of_predictions:\n", "            #     print('=>', tokenizer.detok<PERSON>ze(p))\n", "            # print('------')                \n", "\n", "            n_draft_rounds += current_draft_rounds\n", "\n", "            simulation_time.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "\n", "            # if index == 0:\n", "            #     print(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            draft_tokens_accepted.append(furthest_index - index)\n", "            index = furthest_index\n", "        # print(furthest_index)\n", "        # Make prediction with the main model\n", "        index += 1\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'n_draft_rounds': n_draft_rounds,\n", "        'average_draft_rounds': n_draft_rounds / n_rounds,\n", "        'average_draft_tokens_accepted': safe_mean(draft_tokens_accepted),\n", "        'simulation_time': safe_mean(simulation_time),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "lm = NeuralLM(10)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0]))\n", "\n", "lm = NeuralLM(10, draft_model=LongestOverlapLM(12, 1, True))\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0]))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "08c71e06a8db4a69be5bd2939efba57a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os \n", "\n", "\n", "BASE_DIR = '/mnt/efs/augment/user/yury/sd_staged_v3'\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data, main_round_cost, draft_round_cost, exp_name=None):\n", "    if exp_name is not None:\n", "        path = os.path.join(BASE_DIR, exp_name)\n", "        if os.path.isfile(path):\n", "            print('EXPEREIMENT', exp_name, 'CACHED')\n", "            df = pd.read_csv(path)\n", "            print(df.mean())\n", "            return df                    \n", "\n", "    pbar = tqdm(data, total=len(data))\n", "    df = None\n", "    index = 0\n", "    for d in pbar:\n", "        datum = measure_latency_per_sample_parallel_sd(lm=lm, sample=d)\n", "        datum['latency'] = main_round_cost * datum['n_rounds'] + draft_round_cost * datum['n_draft_rounds']\n", "        datum['latency_per_token'] = datum['latency'] / datum['label_length']        \n", "        if df is None:\n", "            df = pd.DataFrame([datum])\n", "        else:\n", "            df = pd.concat([df, pd.DataFrame([datum])], ignore_index=True)\n", "        if datum['latency_per_token'] > 9.4:\n", "            print('No speed ups on sample', index)\n", "        index += 1\n", "        pbar.set_description('Latency per token: ' + ' '.join(['%s: %.2f' % (k, v) for k, v in df['latency_per_token'].aggregate([q50, q80, q90, q95, q99]).to_dict().items()]))\n", "\n", "    if exp_name is not None:\n", "        df.to_csv(path, index=False)\n", "        print(df.mean())\n", "    return df\n", "\n", "lm = NeuralLM(2)\n", "_ = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:2], 9.5, 3.0)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["N_ROUNDS =  1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bcd1a2717e99489cb52e5951c81acc0a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            5.860000\n", "n_draft_rounds                      5.630000\n", "average_draft_rounds                0.946502\n", "average_draft_tokens_accepted       4.129929\n", "simulation_time                  1053.800405\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            72.560000\n", "latency_per_token                   3.912819\n", "dtype: float64\n", "N_ROUNDS =  2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9da7a6ec3a1a4088b0cf6ac557c07521", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 25\n", "No speed ups on sample 99\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            4.820000\n", "n_draft_rounds                      8.800000\n", "average_draft_rounds                1.741251\n", "average_draft_tokens_accepted       5.649347\n", "simulation_time                  1699.768654\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            72.190000\n", "latency_per_token                   3.740316\n", "dtype: float64\n", "N_ROUNDS =  4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "36f3a3c3615f4f788da476955cf32a74", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 25\n", "No speed ups on sample 43\n", "No speed ups on sample 55\n", "No speed ups on sample 58\n", "No speed ups on sample 83\n", "No speed ups on sample 99\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            4.040000\n", "n_draft_rounds                     13.870000\n", "average_draft_rounds                3.143132\n", "average_draft_tokens_accepted       7.917679\n", "simulation_time                  2621.144494\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            79.990000\n", "latency_per_token                   3.967049\n", "dtype: float64\n", "N_ROUNDS =  6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "58a2c615932a48129e31a005ac908cd8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 23\n", "No speed ups on sample 25\n", "No speed ups on sample 28\n", "No speed ups on sample 31\n", "No speed ups on sample 43\n", "No speed ups on sample 55\n", "No speed ups on sample 58\n", "No speed ups on sample 83\n", "No speed ups on sample 97\n", "No speed ups on sample 99\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            3.850000\n", "n_draft_rounds                     18.050000\n", "average_draft_rounds                4.030828\n", "average_draft_tokens_accepted       8.990473\n", "simulation_time                  3757.107355\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            90.725000\n", "latency_per_token                   4.323828\n", "dtype: float64\n"]}], "source": ["for n_rounds in [1, 2, 4, 6]:\n", "    print('N_ROUNDS = ', n_rounds)\n", "    lm = NeuralLM(n_rounds, draft_model=LongestOverlapLM(12, 1, True))\n", "    df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0, 'df_neural_1b_100_%d.csv' % n_rounds)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["df_staged_fixed_round = []\n", "for n_rounds in [1, 2, 4, 6]:\n", "    path = os.path.join(BASE_DIR, 'df_neural_1b_100_%d.csv' % n_rounds)\n", "    df_tmp = pd.read_csv(path)\n", "    df_tmp[\"n_rounds\"] = n_rounds\n", "    df_staged_fixed_round.append(df_tmp)\n", "df_staged_fixed_round = pd.concat(df_staged_fixed_round)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3972a6d54aa6428bb0905b3d874a3e63", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 0\n", "No speed ups on sample 1\n", "No speed ups on sample 2\n", "No speed ups on sample 3\n", "No speed ups on sample 4\n", "No speed ups on sample 5\n", "No speed ups on sample 6\n", "No speed ups on sample 7\n", "No speed ups on sample 8\n", "No speed ups on sample 9\n", "No speed ups on sample 10\n", "No speed ups on sample 11\n", "No speed ups on sample 12\n", "No speed ups on sample 13\n", "No speed ups on sample 14\n", "No speed ups on sample 15\n", "No speed ups on sample 16\n", "No speed ups on sample 17\n", "No speed ups on sample 18\n", "No speed ups on sample 19\n", "No speed ups on sample 20\n", "No speed ups on sample 21\n", "No speed ups on sample 22\n", "No speed ups on sample 23\n", "No speed ups on sample 24\n", "No speed ups on sample 25\n", "No speed ups on sample 26\n", "No speed ups on sample 27\n", "No speed ups on sample 28\n", "No speed ups on sample 29\n", "No speed ups on sample 30\n", "No speed ups on sample 31\n", "No speed ups on sample 32\n", "No speed ups on sample 33\n", "No speed ups on sample 34\n", "No speed ups on sample 35\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 39\n", "No speed ups on sample 40\n", "No speed ups on sample 41\n", "No speed ups on sample 42\n", "No speed ups on sample 43\n", "No speed ups on sample 44\n", "No speed ups on sample 45\n", "No speed ups on sample 46\n", "No speed ups on sample 47\n", "No speed ups on sample 48\n", "No speed ups on sample 49\n", "No speed ups on sample 50\n", "No speed ups on sample 51\n", "No speed ups on sample 52\n", "No speed ups on sample 53\n", "No speed ups on sample 54\n", "No speed ups on sample 55\n", "No speed ups on sample 56\n", "No speed ups on sample 57\n", "No speed ups on sample 58\n", "No speed ups on sample 59\n", "No speed ups on sample 60\n", "No speed ups on sample 61\n", "No speed ups on sample 62\n", "No speed ups on sample 63\n", "No speed ups on sample 64\n", "No speed ups on sample 65\n", "No speed ups on sample 66\n", "No speed ups on sample 67\n", "No speed ups on sample 68\n", "No speed ups on sample 69\n", "No speed ups on sample 70\n", "No speed ups on sample 71\n", "No speed ups on sample 72\n", "No speed ups on sample 73\n", "No speed ups on sample 74\n", "No speed ups on sample 75\n", "No speed ups on sample 76\n", "No speed ups on sample 77\n", "No speed ups on sample 78\n", "No speed ups on sample 79\n", "No speed ups on sample 80\n", "No speed ups on sample 81\n", "No speed ups on sample 82\n", "No speed ups on sample 83\n", "No speed ups on sample 84\n", "No speed ups on sample 85\n", "No speed ups on sample 86\n", "No speed ups on sample 87\n", "No speed ups on sample 88\n", "No speed ups on sample 89\n", "No speed ups on sample 90\n", "No speed ups on sample 91\n", "No speed ups on sample 92\n", "No speed ups on sample 93\n", "No speed ups on sample 94\n", "No speed ups on sample 95\n", "No speed ups on sample 96\n", "No speed ups on sample 97\n", "No speed ups on sample 98\n", "No speed ups on sample 99\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                           21.500000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       0.000000\n", "simulation_time                     0.006142\n", "beam_size                           0.000000\n", "n_unique_first_token                0.000000\n", "n_unique_two_tokens                 0.000000\n", "latency                           204.250000\n", "latency_per_token                   9.500000\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "42f14e8ea26342d398adc96f092305c9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 2\n", "No speed ups on sample 4\n", "No speed ups on sample 12\n", "No speed ups on sample 16\n", "No speed ups on sample 22\n", "No speed ups on sample 24\n", "No speed ups on sample 26\n", "No speed ups on sample 32\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 41\n", "No speed ups on sample 62\n", "No speed ups on sample 75\n", "No speed ups on sample 78\n", "No speed ups on sample 84\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            9.400000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       1.904132\n", "simulation_time                     0.543696\n", "beam_size                           0.633645\n", "n_unique_first_token                0.633645\n", "n_unique_two_tokens                 0.633645\n", "latency                            89.300000\n", "latency_per_token                   5.018798\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b4a6a1219ac442cdbd1ba62f3ee0ccc3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 2\n", "No speed ups on sample 4\n", "No speed ups on sample 12\n", "No speed ups on sample 16\n", "No speed ups on sample 22\n", "No speed ups on sample 24\n", "No speed ups on sample 26\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 41\n", "No speed ups on sample 62\n", "No speed ups on sample 78\n", "No speed ups on sample 84\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            8.290000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       2.395242\n", "simulation_time                     0.636701\n", "beam_size                           2.860082\n", "n_unique_first_token                1.823724\n", "n_unique_two_tokens                 2.174692\n", "latency                            78.755000\n", "latency_per_token                   4.519333\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM(0, 1, True)\n", "df_baseline = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_baseline.mean())\n", "\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "df_longestoverlap = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_longestoverlap.mean())\n", "\n", "\n", "lm = LongestOverlapLM(12, 6, True)\n", "df_longestoverlap_parallel = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_longestoverlap_parallel.mean())"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 12\n", "PREDICITON = self.get_mention_prefix(mention)<|endoftext|>\n", "COST 4\n", "PREDICITON = self.get_mention_prefix(mention)<|endoftext|>\n"]}], "source": ["import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "import gc\n", "\n", "class NeuralLMOptimal:\n", "    \n", "    def __init__(self, model_cost, main_model_cost, draft_model=None):\n", "        self.model_cost = model_cost\n", "        self.main_model_cost = main_model_cost\n", "        self.draft_model = draft_model        \n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def get_draft_model_predictions(self, context, n_sd):\n", "        if self.draft_model is not None:\n", "            draft_predictions, _ = self.draft_model.predict_next_k_tokens(context, n_sd)\n", "            if len(draft_predictions) == 1:\n", "                draft_predictions = list(draft_predictions[0])\n", "            else:\n", "                # We don't support parallel SD yet                \n", "                assert len(draft_predictions) == 0\n", "        else:\n", "            draft_predictions = []\n", "            _ = 0\n", "        return draft_predictions\n", "\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        predictions = []    \n", "        \n", "        n_rounds = 0\n", "        predictions_probs = []\n", "\n", "        while len(predictions) < n_sd:\n", "            gc.collect()\n", "            torch.cuda.empty_cache()\n", "\n", "            n_rounds += 1\n", "            draft_predictions = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions) - 1)\n", "            context_tokens = context + predictions + draft_predictions\n", "            with torch.no_grad():\n", "                context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([context_tokens], dtype=torch.int64))\n", "                model_inputs = (\n", "                    context_tokens_tensor,  # input_ids\n", "                    position_ids,  # position_ids\n", "                    attention_mask,  # attention_mask\n", "                )            \n", "\n", "                model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "                eval_result = text_gen_utils.forward_model(\n", "                    model=model.neox_model,\n", "                    model_inputs=model_inputs,\n", "                    is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                    pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                    timers=None,\n", "                )\n", "                \n", "                model.neox_model.module.clear_cache()  # clear the k,v cache after\n", "\n", "                # We only work with batch size 1 for now\n", "                assert eval_result.shape[0] == 1\n", "                eval_result = eval_result[0]\n", "\n", "                # cursed stuff\n", "                assert eval_result.shape[0] == len(context_tokens)\n", "\n", "                # Disallow special tokens\n", "                # eval_result[:, ALL_SPECIAL_TOKENS_IDS] = -10000\n", "                logprobs = F.log_softmax(eval_result.float(), dim=-1)\n", "                            \n", "                for index in range(len(context + predictions) - 1, len(context_tokens)):\n", "                    token_id = logprobs[index].argmax().cpu()\n", "                    \n", "                    # assert token_id not in ALL_SPECIAL_TOKENS_IDS_SET, (token_id, model.tokenizer.detokenize([token_id]))\n", "                    predictions.append(token_id)\n", "                    predictions_probs.append(torch.exp(logprobs[index, token_id]).cpu().item())\n", "\n", "                    if index < len(context_tokens) - 1 and token_id != context_tokens[index + 1]:\n", "                        break      \n", "\n", "                    if token_id == EOS_TOKEN_ID:\n", "                        break\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "\n", "            if (len(predictions) > 0 and predictions[-1] == EOS_TOKEN_ID):\n", "                break\n", "        \n", "            # We decide whether to stop at this point.                \n", "            expected_number_of_predicted_tokens = np.sum(np.cumprod(predictions_probs))            \n", "            next_token_estimated_prob = predictions_probs[-1] * np.prod(predictions_probs)\n", "\n", "            # cost_before_round = (n_rounds * self.model_cost + self.main_model_cost) / expected_number_of_predicted_tokens\n", "            # cost_after_round = ((n_rounds + 1) * self.model_cost + self.main_model_cost) / (expected_number_of_predicted_tokens + next_token_estimated_prob)\n", "            # print('expected_number_of_correct_tokens_before_round', expected_number_of_predicted_tokens)                \n", "            # print('expected_number_of_correct_tokens_after_round', expected_number_of_predicted_tokens + next_token_estimated_prob)\n", "            # print('cost_before_round', cost_before_round)                \n", "            # print('cost_after_round', cost_after_round)\n", "            if ((n_rounds + 1) * self.model_cost + self.main_model_cost) * expected_number_of_predicted_tokens >= (n_rounds * self.model_cost + self.main_model_cost) * (expected_number_of_predicted_tokens + next_token_estimated_prob):\n", "                # print('yo', cost_before_round, cost_after_round)\n", "                break\n", "\n", "\n", "        gc.collect()\n", "        torch.cuda.empty_cache()\n", "\n", "        # Do one final round of draft predictions since it's free\n", "        if len(predictions) < n_sd and not (len(predictions) > 0 and predictions[-1] == EOS_TOKEN_ID):            \n", "            draft_predictions = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions))\n", "            predictions.extend(draft_predictions)            \n", "\n", "        return [tuple(predictions)], n_rounds\n", "\n", "\n", "lm = NeuralLMOptimal(model_cost=3.0, main_model_cost=9.5)\n", "p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITON', tokenizer.detokenize(p[0]))\n", "\n", "lm = NeuralLMOptimal(model_cost=3.0, main_model_cost=9.5, draft_model=LongestOverlapLM(12, 1, True))\n", "lm.fit(data_fim_retrieval[0]['context'])\n", "p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITON', tokenizer.detokenize(p[0]))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_staged_neural_1b_optimal_100.csv CACHED\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            4.400000\n", "n_draft_rounds                      9.750000\n", "average_draft_rounds                2.421509\n", "average_draft_tokens_accepted       7.218519\n", "simulation_time                  2668.287225\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            71.050000\n", "latency_per_token                   3.640334\n", "dtype: float64\n"]}], "source": ["lm = NeuralLMOptimal(model_cost=3.0, main_model_cost=9.5, draft_model=LongestOverlapLM(12, 1, True))\n", "df_staged_adaptive_rounds = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0, 'df_staged_neural_1b_optimal_100.csv')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_staged_neural_1b_optimal_100_cost2.csv CACHED\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            4.290000\n", "n_draft_rounds                     10.470000\n", "average_draft_rounds                2.593925\n", "average_draft_tokens_accepted       7.379323\n", "simulation_time                  2813.806429\n", "beam_size                           1.000000\n", "n_unique_first_token                1.000000\n", "n_unique_two_tokens                 1.000000\n", "latency                            61.695000\n", "latency_per_token                   3.189081\n", "dtype: float64\n"]}], "source": ["lm = NeuralLMOptimal(model_cost=2.0, main_model_cost=9.5, draft_model=LongestOverlapLM(12, 1, True))\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 2.0, 'df_staged_neural_1b_optimal_100_cost2.csv')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.90)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 12))\n", "\n", "QUANTILES = [q50, q80, q90, q95]\n", "QUANTILE_NAMES = ['50', '80', '90', '95']\n", "\n", "xmin = 1\n", "xmax = 6\n", "\n", "index = 0\n", "for i in range(2):\n", "    for j in range(2):        \n", "        # df_tmp = df_baseline.aggregate({'latency_per_token': [QUANTILES[index]]}).reset_index()\n", "        # axes[i, j].hlines(y=df_tmp[\"latency_per_token\"], xmin=xmin, xmax=xmax, label='no SD', color='blue', linewidths=(3,),)    \n", "\n", "        # df_tmp = df_longestoverlap.aggregate({'latency_per_token': [QUANTILES[index]]}).reset_index()\n", "        # axes[i, j].hlines(y=df_tmp[\"latency_per_token\"], xmin=xmin, xmax=xmax, label='LongestOverlapLM (in prod)', color='orange', linewidths=(3,))    \n", "\n", "        df_tmp = df_staged_fixed_round.groupby([\"n_rounds\"]).aggregate({'latency_per_token': [QUANTILES[index]]}).reset_index()\n", "        axes[i, j].plot(\n", "            df_tmp[\"n_rounds\"].values,\n", "            df_tmp[\"latency_per_token\"],\n", "            'o-',\n", "            color='green',\n", "            label='Neural SD 1B',\n", "            linewidth=3,\n", "        )\n", "        df_tmp = df_staged_adaptive_rounds.aggregate({'latency_per_token': [QUANTILES[index]]}).reset_index()\n", "        axes[i, j].hlines(y=df_tmp[\"latency_per_token\"], xmin=xmin, xmax=xmax, label='Neural SD 1B (adaptive number of rounds)', color='green', linestyles='dashed', linewidths=(3,))            \n", "\n", "        axes[i, j].grid() \n", "        axes[i, j].set_xlabel('# of rounds of 1B model', fontsize=12)\n", "        axes[i, j].set_ylabel('Latency per token, ms', fontsize=12)\n", "        axes[i, j].set_title('Latency %s quantile' % QUANTILE_NAMES[index], fontsize=14)\n", "        # axes[i, j].yaxis.set_major_locator(ticker.MultipleLocator(0.5))\n", "\n", "\n", "        \n", "        # axes[i, j].set_xticks([1, 2, 4, 6, 8, 10])\n", "        # axes[i, j].set_yticks([8, 10, 12, 14, 16, 18, 20, 22, 24])\n", "        axes[i, j].legend()\n", "\n", "        index += 1\n", "\n", "fig.suptitle('Simulation of speculative decoding performance on 100 samples on H100 with FP8 (cost of main model is 9.5, draft model is 3.0). Generate up to 32 tokens.')\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "> initializing torch distributed ...\n", "[2023-09-26 22:17:09,025] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-09-26 22:17:09,157] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=**************, master_port=6001\n", "[2023-09-26 22:17:09,158] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-09-26 22:17:09,161] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev:11224] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-09-26 22:17:09,428] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-09-26 22:17:11,097] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-09-26 22:17:11,098] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-09-26 22:17:11,177] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-09-26 22:17:11,178] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-09-26 22:17:11,178] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-09-26 22:17:11,179] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-09-26 22:17:11,179] [INFO] [config.py:763:print]   amp_enabled .................. <PERSON>alse\n", "[2023-09-26 22:17:11,180] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-09-26 22:17:11,181] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-09-26 22:17:11,181] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-09-26 22:17:11,182] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-09-26 22:17:11,182] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-09-26 22:17:11,183] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-09-26 22:17:11,184] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-09-26 22:17:11,185] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-09-26 22:17:11,186] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-09-26 22:17:11,186] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-09-26 22:17:11,187] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-09-26 22:17:11,188] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-09-26 22:17:11,189] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-09-26 22:17:11,189] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-09-26 22:17:11,190] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-09-26 22:17:11,191] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-09-26 22:17:11,192] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-09-26 22:17:11,192] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-09-26 22:17:11,193] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-09-26 22:17:11,194] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-09-26 22:17:11,194] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-09-26 22:17:11,195] [INFO] [config.py:763:print]   pld_enabled .................. <PERSON>alse\n", "[2023-09-26 22:17:11,196] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-09-26 22:17:11,196] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-09-26 22:17:11,197] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-09-26 22:17:11,198] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-09-26 22:17:11,198] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-09-26 22:17:11,199] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-09-26 22:17:11,200] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-09-26 22:17:11,200] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-09-26 22:17:11,201] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-09-26 22:17:11,202] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-09-26 22:17:11,202] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-09-26 22:17:11,203] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-09-26 22:17:11,203] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-09-26 22:17:11,204] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-09-26 22:17:11,205] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-09-26 22:17:11,205] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-09-26 22:17:11,206] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-09-26 22:17:11,207] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-09-26 22:17:11,212] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-09-26 22:17:11,212] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu117 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu117/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.25137972831726074 seconds\n", "[2023-09-26 22:17:11,995] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-09-26 22:17:12,031] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-09-26 22:17:12,073] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-09-26 22:17:12,212] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-09-26 22:17:12,283] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-09-26 22:17:12,332] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-09-26 22:17:12,414] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-09-26 22:17:12,480] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-09-26 22:17:12,535] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-09-26 22:17:12,585] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-09-26 22:17:12,660] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-09-26 22:17:12,727] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-09-26 22:17:12,789] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-09-26 22:17:12,858] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-09-26 22:17:12,934] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-09-26 22:17:12,985] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-09-26 22:17:13,053] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-09-26 22:17:13,123] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-09-26 22:17:13,183] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-09-26 22:17:13,245] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-09-26 22:17:13,315] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-09-26 22:17:13,387] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-09-26 22:17:13,440] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-09-26 22:17:13,504] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-09-26 22:17:13,576] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-09-26 22:17:13,622] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-09-26 22:17:13,697] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-09-26 22:17:13,762] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-09-26 22:17:13,767] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-09-26 22:17:13,890] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7f7b15cfe970>\n"]}], "source": ["print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 10 samples (had to process 10 samples)\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "import json\n", "from research.models.meta_model import GenerationOptions\n", "from typing import Any, List, Dict\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for _, datum in df.iterrows():\n", "            n_read_samples += 1\n", "\n", "            context = ModelInput(\n", "                prefix=datum['prefix'],\n", "                suffix=datum['suffix'],\n", "                middle='',\n", "                retrieved_chunks=deserialize_retrieved_chunks(datum['retrieved_chunks']),\n", "                path=datum['file_path'],\n", "                target=None,\n", "            )\n", "\n", "            label = tokenizer.tokenize(datum['middle'] + \"<|endoftext|>\")    \n", "            label = label[:max_tokens_to_predict]\n", "            label = np.array(label)\n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 10, 256, None)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:self._neox_args.tokenizer is different from self.tokenizer, and we dynamically reset self._neox_args.tokenizer to be self.tokenizer.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'context': ModelInput(\n", "  prefix = 'import tweepy\\n\\nimport json\\nimport logging\\nimport time\\n\\nimport os\\nfrom os import path\\nimport re\\nimport pickle\\nimport argparse\\n\\nfrom http.client import IncompleteRead\\nfrom w2vEngine import w2vEngine\\nfrom daemon import Daemon\\nimport sys\\n\\n\\nclass EmbedBot(Daemon):\\n    \"\"\"docstring for EmbedBot\"\"\"\\n    def __init__(self, pidfile):\\n        super(EmbedBot, self).__init__(pidfile)\\n        logname = (\"EmbedBot.log\")\\n        self.log = logging.getLogger(\"embedbot\")\\n        self.log.setLevel(\\'INFO\\')\\n        formatter = logging.Formatter(\\'%(asctime)-15s %(name)s \\'\\n                                      \\'[%(levelname)s] %(message)s\\')\\n        fh = logging.FileHandler(logname)\\n        fh.setFormatter(formatter)\\n        self.log.addHandler(fh)\\n\\n    def initialize(self, configpath, modelpath):\\n        self.configpath = configpath\\n        self.modelpath = modelpath\\n        self.log.info(\"Configpath: %s\" % self.configpath)\\n        self.log.info(\"Modelpath: %s\" % self.modelpath)\\n        self.config = {}\\n        self.config[\\'refresh_interval\\'] = 120\\n        self.config[\\'cachedir\\'] = \"tmp\"\\n        if not os.path.isdir(self.config.get(\\'cachedir\\')):\\n            os.makedirs(self.config.get(\\'cachedir\\'))\\n        self.log.info(\"Initializing.\")\\n        self.load_state()\\n        self.load_config()\\n        self.start_engine()\\n        self.connect()\\n        self.log.info(\"Initialized succesfully.\")\\n\\n    def load_config(self):\\n        with open(self.configpath, \"r\") as infile:\\n            config = json.load(infile)\\n        for k, v in config.items():\\n            self.config[k] = v\\n        self.log.info(\"Config loaded.\")\\n\\n    def load_state(self):\\n        try:\\n            with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                      \"rb\") as statefile:\\n                self.state = pickle.load(statefile)\\n            self.log.info(\"State loaded.\")\\n        except Exception as e:\\n            self.log.error(e)\\n            self.state = {}\\n            self.state[\\'last_mention_id\\'] = 1\\n            self.state[\\'mention_queue\\'] = []\\n\\n    def save_state(self):\\n        with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                  \"wb\") as statefile:\\n            pickle.dump(self.state, statefile)\\n        self.log.info(\"State saved.\")\\n\\n    def start_engine(self):\\n        self.w2vengine = w2vEngine(self.modelpath)\\n\\n    def connect(self):\\n        auth = tweepy.OAuthHandler(self.config[\\'api_key\\'],\\n                                   self.config[\\'api_secret\\'])\\n        auth.set_access_token(self.config[\\'access_key\\'],\\n                              self.config[\\'access_secret\\'])\\n        self.api = tweepy.API(auth)\\n        self.id = self.api.me().id\\n        self.screen_name = self.api.me().screen_name\\n\\n    def check_mentions(self):\\n        try:\\n            current_mentions = self.api.mentions_timeline(since_id=self.state[\\'last_mention_id\\'],\\n                                                          count=100)\\n            # reduce to direct mentions only\\n            current_mentions = [t for t in current_mentions\\n                                if re.split(\\'[^@\\\\w]\\', t.text)[0]\\n                                == \\'@\\'+self.screen_name]\\n            if len(current_mentions) != 0:\\n                self.state[\\'last_mention_id\\'] = current_mentions[0].id\\n            self.state[\\'last_mention_time\\'] = time.time()\\n            self.state[\\'mention_queue\\'] += reversed(current_mentions)\\n            self.log.info(\\'Mentions updated ({} retrieved, {} total in queue)\\'\\n                          .format(len(current_mentions),\\n                                  len(self.state[\\'mention_queue\\'])))\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t retrieve mentions.\\')\\n            self.log.error(e)\\n        except IncompleteRead as e:\\n            self.log.error(\\'Incomplete read error -- skipping mentions update\\')\\n\\n    def handle_mentions(self):\\n        for mention in self.state[\\'mention_queue\\']:\\n            prefix ',\n", "  suffix = '\\n            text = \" \".join([w for w in mention.text.split() if \\'@\\' not in w])\\n            query = (text.lower()\\n                         .replace(\"-\", \"+-\")\\n                         .replace(\" \", \"\")\\n                         .split(\"+\"))\\n            signs = [(-1) if \"-\" in q\\n                     else 1\\n                     for q in query]\\n            not_in_vocab = self.check_vocabulary(query)\\n            if len(not_in_vocab) != 0:\\n                self.log.info(\"Not in vocabulary: \"+\", \".join(not_in_vocab))\\n                reply = prefix+\" Sorry, \"+\", \".join(not_in_vocab)+\" not in my vocabulary.\"\\n                self.post_tweet(reply, reply_to=mention)\\n                self.state[\\'mention_queue\\'].remove(mention)\\n                continue\\n            reply = self.formulate_reply(prefix, text, query, signs)\\n            self.log.info(\"About to post \"+reply)\\n            self.post_tweet(reply, reply_to=mention)\\n            self.state[\\'mention_queue\\'].remove(mention)\\n\\n    def get_mention_prefix(self, tweet):\\n        \"\"\"\\n        Returns a string of users to @-mention when responding to a tweet.\\n        \"\"\"\\n        mention_back = [\\'@\\' + tweet.author.screen_name]\\n        mention_back += [s for s in re.split(\\'[^@\\\\w]\\', tweet.text)\\n                         if len(s) > 2\\n                         and s[0] == \\'@\\'\\n                         and s[1:] != self.screen_name]\\n        return \\' \\'.join(mention_back)\\n\\n    def formulate_reply(self, prefix, text, query, signs):\\n        if len(query) == 1:\\n            result = self.w2vengine.get_synonyms(query[0], 5)\\n            result = \", \".join(result)\\n            reply = \"Related terms to \"+query[0]+\": \"+result\\n        else:\\n            result = self.w2vengine.get_abstractions(query, signs, 5)\\n            result = \", \".join(result)\\n            reply = \" = \".join([text, result])\\n        return \" \".join([prefix, reply])\\n\\n    def check_vocabulary(self, query):\\n        query = [q.replace(\"-\", \"\") for q in query]\\n        q = set(query)\\n        diff = q.difference(self.w2vengine.vocabulary)\\n        return list(diff)\\n\\n    def _tweet_url(self, tweet):\\n        return \"http://twitter.com/\" + tweet.author.screen_name + \"/status/\" + str(tweet.id)\\n\\n    def post_tweet(self, text, reply_to=None, media=None):\\n        kwargs = {}\\n        args = [text]\\n        if media is not None:\\n            cmd = self.api.update_with_media\\n            args.insert(0, media)\\n        else:\\n            cmd = self.api.update_status\\n\\n        try:\\n            self.log.info(\\'Tweeting \"{}\"\\'.format(text))\\n            if reply_to:\\n                self.log.info(\"-- Responding to status {}\".format(self._tweet_url(reply_to)))\\n                kwargs[\\'in_reply_to_status_id\\'] = reply_to.id\\n            else:\\n                self.log.info(\"-- Posting to own timeline\")\\n\\n            tweet = cmd(*args, **kwargs)\\n            self.log.info(\\'Status posted at {}\\'.format(self._tweet_url(tweet)))\\n            return True\\n\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t post status\\')\\n            self.log.error(e)\\n            return False\\n\\n    def run(self):\\n        while True:\\n            # check mentions every minute-ish\\n            # if self.reply_to_mentions and (time.time() - self.last_mention_time) > 60:\\n            if (time.time() - self.state.get(\\'last_mention_time\\', 0)) > 60:\\n                self.check_mentions()\\n                self.handle_mentions()\\n\\n            # save current state\\n            self.save_state()\\n            self.log.info(\"Sleeping for a bit...\")\\n            time.sleep(60)\\n\\n\\nif __name__ == \\'__main__\\':\\n    bot = EmbedBot(\\'embedbot.pid\\')\\n    parser = argparse.ArgumentParser(description=\\'do stuff\\')\\n    parser.add_argument(\\'--command\\', dest=\\'command\\', help=\\'\\'\\n                        \\'usage: start|stop|status|restart\\')\\n    parser.add_argument(\\'--configfile\\', dest=\\'configfile\\', help=\\'relative or \\'\\n                        \\'absolute path of the config file\\')\\n    parser.add_argument(\\'--modelpath\\', dest=\\'modelpath\\', help=',\n", "  middle = '',\n", "  retrieved_chunks = [Chunk(range=2547:3616, line_range=80:100, path=bot/server.py), <PERSON><PERSON>(range=1821:2547, line_range=60:80, path=bot/server.py), <PERSON><PERSON>(range=293:1141, line_range=20:40, path=bot/server.py), <PERSON><PERSON>(range=1141:1821, line_range=40:60, path=bot/server.py), <PERSON><PERSON>(range=3616:4509, line_range=100:120, path=bot/server.py), <PERSON><PERSON>(range=6933:7549, line_range=180:200, path=bot/server.py), <PERSON><PERSON>(range=0:612, line_range=0:20, path=bot/w2vEngine.py), <PERSON><PERSON>(range=4509:5409, line_range=120:140, path=bot/server.py), <PERSON><PERSON>(range=612:1471, line_range=20:40, path=bot/w2vEngine.py), <PERSON><PERSON>(range=0:524, line_range=0:20, path=converter/vector_converter.py), <PERSON><PERSON>(range=0:727, line_range=0:20, path=workflow/workflow.py), <PERSON><PERSON>(range=0:293, line_range=0:20, path=bot/server.py), <PERSON><PERSON>(range=6193:6933, line_range=160:180, path=bot/server.py), Chunk(range=524:1242, line_range=20:40, path=converter/vector_converter.py), Chunk(range=1932:2710, line_range=60:80, path=workflow/xml2json.py), Chunk(range=3533:4295, line_range=100:120, path=workflow/xml2json.py), Chunk(range=0:569, line_range=0:20, path=workflow/xml2json.py), Chunk(range=7549:8402, line_range=200:220, path=bot/server.py), Chunk(range=2710:3533, line_range=80:100, path=workflow/xml2json.py), Chunk(range=569:1183, line_range=20:40, path=workflow/xml2json.py), Chunk(range=5409:6193, line_range=140:160, path=bot/server.py), Chunk(range=649:1510, line_range=20:40, path=workflow/transformers.py), Chunk(range=3558:4265, line_range=100:120, path=workflow/transformers.py), Chunk(range=2087:2905, line_range=60:80, path=workflow/workflow.py), Chunk(range=1471:2377, line_range=40:60, path=bot/w2vEngine.py)],\n", "  path = 'bot/server.py',\n", "  target = None,\n", "  extra = {}\n", "), 'label': array([   47,   630,    32,   371,    81, 26236,    81,  4254,    26,\n", "       26236,    27,     0]), 'pretokenized_file': 'import tweepy\\n\\nimport json\\nimport logging\\nimport time\\n\\nimport os\\nfrom os import path\\nimport re\\nimport pickle\\nimport argparse\\n\\nfrom http.client import IncompleteRead\\nfrom w2vEngine import w2vEngine\\nfrom daemon import Daemon\\nimport sys\\n\\n\\nclass EmbedBot(Daemon):\\n    \"\"\"docstring for EmbedBot\"\"\"\\n    def __init__(self, pidfile):\\n        super(EmbedBot, self).__init__(pidfile)\\n        logname = (\"EmbedBot.log\")\\n        self.log = logging.getLogger(\"embedbot\")\\n        self.log.setLevel(\\'INFO\\')\\n        formatter = logging.Formatter(\\'%(asctime)-15s %(name)s \\'\\n                                      \\'[%(levelname)s] %(message)s\\')\\n        fh = logging.FileHandler(logname)\\n        fh.setFormatter(formatter)\\n        self.log.addHandler(fh)\\n\\n    def initialize(self, configpath, modelpath):\\n        self.configpath = configpath\\n        self.modelpath = modelpath\\n        self.log.info(\"Configpath: %s\" % self.configpath)\\n        self.log.info(\"Modelpath: %s\" % self.modelpath)\\n        self.config = {}\\n        self.config[\\'refresh_interval\\'] = 120\\n        self.config[\\'cachedir\\'] = \"tmp\"\\n        if not os.path.isdir(self.config.get(\\'cachedir\\')):\\n            os.makedirs(self.config.get(\\'cachedir\\'))\\n        self.log.info(\"Initializing.\")\\n        self.load_state()\\n        self.load_config()\\n        self.start_engine()\\n        self.connect()\\n        self.log.info(\"Initialized succesfully.\")\\n\\n    def load_config(self):\\n        with open(self.configpath, \"r\") as infile:\\n            config = json.load(infile)\\n        for k, v in config.items():\\n            self.config[k] = v\\n        self.log.info(\"Config loaded.\")\\n\\n    def load_state(self):\\n        try:\\n            with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                      \"rb\") as statefile:\\n                self.state = pickle.load(statefile)\\n            self.log.info(\"State loaded.\")\\n        except Exception as e:\\n            self.log.error(e)\\n            self.state = {}\\n            self.state[\\'last_mention_id\\'] = 1\\n            self.state[\\'mention_queue\\'] = []\\n\\n    def save_state(self):\\n        with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                  \"wb\") as statefile:\\n            pickle.dump(self.state, statefile)\\n        self.log.info(\"State saved.\")\\n\\n    def start_engine(self):\\n        self.w2vengine = w2vEngine(self.modelpath)\\n\\n    def connect(self):\\n        auth = tweepy.OAuthHandler(self.config[\\'api_key\\'],\\n                                   self.config[\\'api_secret\\'])\\n        auth.set_access_token(self.config[\\'access_key\\'],\\n                              self.config[\\'access_secret\\'])\\n        self.api = tweepy.API(auth)\\n        self.id = self.api.me().id\\n        self.screen_name = self.api.me().screen_name\\n\\n    def check_mentions(self):\\n        try:\\n            current_mentions = self.api.mentions_timeline(since_id=self.state[\\'last_mention_id\\'],\\n                                                          count=100)\\n            # reduce to direct mentions only\\n            current_mentions = [t for t in current_mentions\\n                                if re.split(\\'[^@\\\\w]\\', t.text)[0]\\n                                == \\'@\\'+self.screen_name]\\n            if len(current_mentions) != 0:\\n                self.state[\\'last_mention_id\\'] = current_mentions[0].id\\n            self.state[\\'last_mention_time\\'] = time.time()\\n            self.state[\\'mention_queue\\'] += reversed(current_mentions)\\n            self.log.info(\\'Mentions updated ({} retrieved, {} total in queue)\\'\\n                          .format(len(current_mentions),\\n                                  len(self.state[\\'mention_queue\\'])))\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t retrieve mentions.\\')\\n            self.log.error(e)\\n        except IncompleteRead as e:\\n            self.log.error(\\'Incomplete read error -- skipping mentions update\\')\\n\\n    def handle_mentions(self):\\n        for mention in self.state[\\'mention_queue\\']:\\n            prefix = self.get_mention_prefix(mention)\\n            text = \" \".join([w for w in mention.text.split() if \\'@\\' not in w])\\n            query = (text.lower()\\n                         .replace(\"-\", \"+-\")\\n                         .replace(\" \", \"\")\\n                         .split(\"+\"))\\n            signs = [(-1) if \"-\" in q\\n                     else 1\\n                     for q in query]\\n            not_in_vocab = self.check_vocabulary(query)\\n            if len(not_in_vocab) != 0:\\n                self.log.info(\"Not in vocabulary: \"+\", \".join(not_in_vocab))\\n                reply = prefix+\" Sorry, \"+\", \".join(not_in_vocab)+\" not in my vocabulary.\"\\n                self.post_tweet(reply, reply_to=mention)\\n                self.state[\\'mention_queue\\'].remove(mention)\\n                continue\\n            reply = self.formulate_reply(prefix, text, query, signs)\\n            self.log.info(\"About to post \"+reply)\\n            self.post_tweet(reply, reply_to=mention)\\n            self.state[\\'mention_queue\\'].remove(mention)\\n\\n    def get_mention_prefix(self, tweet):\\n        \"\"\"\\n        Returns a string of users to @-mention when responding to a tweet.\\n        \"\"\"\\n        mention_back = [\\'@\\' + tweet.author.screen_name]\\n        mention_back += [s for s in re.split(\\'[^@\\\\w]\\', tweet.text)\\n                         if len(s) > 2\\n                         and s[0] == \\'@\\'\\n                         and s[1:] != self.screen_name]\\n        return \\' \\'.join(mention_back)\\n\\n    def formulate_reply(self, prefix, text, query, signs):\\n        if len(query) == 1:\\n            result = self.w2vengine.get_synonyms(query[0], 5)\\n            result = \", \".join(result)\\n            reply = \"Related terms to \"+query[0]+\": \"+result\\n        else:\\n            result = self.w2vengine.get_abstractions(query, signs, 5)\\n            result = \", \".join(result)\\n            reply = \" = \".join([text, result])\\n        return \" \".join([prefix, reply])\\n\\n    def check_vocabulary(self, query):\\n        query = [q.replace(\"-\", \"\") for q in query]\\n        q = set(query)\\n        diff = q.difference(self.w2vengine.vocabulary)\\n        return list(diff)\\n\\n    def _tweet_url(self, tweet):\\n        return \"http://twitter.com/\" + tweet.author.screen_name + \"/status/\" + str(tweet.id)\\n\\n    def post_tweet(self, text, reply_to=None, media=None):\\n        kwargs = {}\\n        args = [text]\\n        if media is not None:\\n            cmd = self.api.update_with_media\\n            args.insert(0, media)\\n        else:\\n            cmd = self.api.update_status\\n\\n        try:\\n            self.log.info(\\'Tweeting \"{}\"\\'.format(text))\\n            if reply_to:\\n                self.log.info(\"-- Responding to status {}\".format(self._tweet_url(reply_to)))\\n                kwargs[\\'in_reply_to_status_id\\'] = reply_to.id\\n            else:\\n                self.log.info(\"-- Posting to own timeline\")\\n\\n            tweet = cmd(*args, **kwargs)\\n            self.log.info(\\'Status posted at {}\\'.format(self._tweet_url(tweet)))\\n            return True\\n\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t post status\\')\\n            self.log.error(e)\\n            return False\\n\\n    def run(self):\\n        while True:\\n            # check mentions every minute-ish\\n            # if self.reply_to_mentions and (time.time() - self.last_mention_time) > 60:\\n            if (time.time() - self.state.get(\\'last_mention_time\\', 0)) > 60:\\n                self.check_mentions()\\n                self.handle_mentions()\\n\\n            # save current state\\n            self.save_state()\\n            self.log.info(\"Sleeping for a bit...\")\\n            time.sleep(60)\\n\\n\\nif __name__ == \\'__main__\\':\\n    bot = EmbedBot(\\'embedbot.pid\\')\\n    parser = argparse.ArgumentParser(description=\\'do stuff\\')\\n    parser.add_argument(\\'--command\\', dest=\\'command\\', help=\\'\\'\\n                        \\'usage: start|stop|status|restart\\')\\n    parser.add_argument(\\'--configfile\\', dest=\\'configfile\\', help=\\'relative or \\'\\n                        \\'absolute path of the config file\\')\\n    parser.add_argument(\\'--modelpath\\', dest=\\'modelpath\\', help=', 'pretokenized_suffix': '\\n            text = \" \".join([w for w in mention.text.split() if \\'@\\' not in w])\\n            query = (text.lower()\\n                         .replace(\"-\", \"+-\")\\n                         .replace(\" \", \"\")\\n                         .split(\"+\"))\\n            signs = [(-1) if \"-\" in q\\n                     else 1\\n                     for q in query]\\n            not_in_vocab = self.check_vocabulary(query)\\n            if len(not_in_vocab) != 0:\\n                self.log.info(\"Not in vocabulary: \"+\", \".join(not_in_vocab))\\n                reply = prefix+\" Sorry, \"+\", \".join(not_in_vocab)+\" not in my vocabulary.\"\\n                self.post_tweet(reply, reply_to=mention)\\n                self.state[\\'mention_queue\\'].remove(mention)\\n                continue\\n            reply = self.formulate_reply(prefix, text, query, signs)\\n            self.log.info(\"About to post \"+reply)\\n            self.post_tweet(reply, reply_to=mention)\\n            self.state[\\'mention_queue\\'].remove(mention)\\n\\n    def get_mention_prefix(self, tweet):\\n        \"\"\"\\n        Returns a string of users to @-mention when responding to a tweet.\\n        \"\"\"\\n        mention_back = [\\'@\\' + tweet.author.screen_name]\\n        mention_back += [s for s in re.split(\\'[^@\\\\w]\\', tweet.text)\\n                         if len(s) > 2\\n                         and s[0] == \\'@\\'\\n                         and s[1:] != self.screen_name]\\n        return \\' \\'.join(mention_back)\\n\\n    def formulate_reply(self, prefix, text, query, signs):\\n        if len(query) == 1:\\n            result = self.w2vengine.get_synonyms(query[0], 5)\\n            result = \", \".join(result)\\n            reply = \"Related terms to \"+query[0]+\": \"+result\\n        else:\\n            result = self.w2vengine.get_abstractions(query, signs, 5)\\n            result = \", \".join(result)\\n            reply = \" = \".join([text, result])\\n        return \" \".join([prefix, reply])\\n\\n    def check_vocabulary(self, query):\\n        query = [q.replace(\"-\", \"\") for q in query]\\n        q = set(query)\\n        diff = q.difference(self.w2vengine.vocabulary)\\n        return list(diff)\\n\\n    def _tweet_url(self, tweet):\\n        return \"http://twitter.com/\" + tweet.author.screen_name + \"/status/\" + str(tweet.id)\\n\\n    def post_tweet(self, text, reply_to=None, media=None):\\n        kwargs = {}\\n        args = [text]\\n        if media is not None:\\n            cmd = self.api.update_with_media\\n            args.insert(0, media)\\n        else:\\n            cmd = self.api.update_status\\n\\n        try:\\n            self.log.info(\\'Tweeting \"{}\"\\'.format(text))\\n            if reply_to:\\n                self.log.info(\"-- Responding to status {}\".format(self._tweet_url(reply_to)))\\n                kwargs[\\'in_reply_to_status_id\\'] = reply_to.id\\n            else:\\n                self.log.info(\"-- Posting to own timeline\")\\n\\n            tweet = cmd(*args, **kwargs)\\n            self.log.info(\\'Status posted at {}\\'.format(self._tweet_url(tweet)))\\n            return True\\n\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t post status\\')\\n            self.log.error(e)\\n            return False\\n\\n    def run(self):\\n        while True:\\n            # check mentions every minute-ish\\n            # if self.reply_to_mentions and (time.time() - self.last_mention_time) > 60:\\n            if (time.time() - self.state.get(\\'last_mention_time\\', 0)) > 60:\\n                self.check_mentions()\\n                self.handle_mentions()\\n\\n            # save current state\\n            self.save_state()\\n            self.log.info(\"Sleeping for a bit...\")\\n            time.sleep(60)\\n\\n\\nif __name__ == \\'__main__\\':\\n    bot = EmbedBot(\\'embedbot.pid\\')\\n    parser = argparse.ArgumentParser(description=\\'do stuff\\')\\n    parser.add_argument(\\'--command\\', dest=\\'command\\', help=\\'\\'\\n                        \\'usage: start|stop|status|restart\\')\\n    parser.add_argument(\\'--configfile\\', dest=\\'configfile\\', help=\\'relative or \\'\\n                        \\'absolute path of the config file\\')\\n    parser.add_argument(\\'--modelpath\\', dest=\\'modelpath\\', help=', 'pretokenized_prefix': 'import tweepy\\n\\nimport json\\nimport logging\\nimport time\\n\\nimport os\\nfrom os import path\\nimport re\\nimport pickle\\nimport argparse\\n\\nfrom http.client import IncompleteRead\\nfrom w2vEngine import w2vEngine\\nfrom daemon import Daemon\\nimport sys\\n\\n\\nclass EmbedBot(Daemon):\\n    \"\"\"docstring for EmbedBot\"\"\"\\n    def __init__(self, pidfile):\\n        super(EmbedBot, self).__init__(pidfile)\\n        logname = (\"EmbedBot.log\")\\n        self.log = logging.getLogger(\"embedbot\")\\n        self.log.setLevel(\\'INFO\\')\\n        formatter = logging.Formatter(\\'%(asctime)-15s %(name)s \\'\\n                                      \\'[%(levelname)s] %(message)s\\')\\n        fh = logging.FileHandler(logname)\\n        fh.setFormatter(formatter)\\n        self.log.addHandler(fh)\\n\\n    def initialize(self, configpath, modelpath):\\n        self.configpath = configpath\\n        self.modelpath = modelpath\\n        self.log.info(\"Configpath: %s\" % self.configpath)\\n        self.log.info(\"Modelpath: %s\" % self.modelpath)\\n        self.config = {}\\n        self.config[\\'refresh_interval\\'] = 120\\n        self.config[\\'cachedir\\'] = \"tmp\"\\n        if not os.path.isdir(self.config.get(\\'cachedir\\')):\\n            os.makedirs(self.config.get(\\'cachedir\\'))\\n        self.log.info(\"Initializing.\")\\n        self.load_state()\\n        self.load_config()\\n        self.start_engine()\\n        self.connect()\\n        self.log.info(\"Initialized succesfully.\")\\n\\n    def load_config(self):\\n        with open(self.configpath, \"r\") as infile:\\n            config = json.load(infile)\\n        for k, v in config.items():\\n            self.config[k] = v\\n        self.log.info(\"Config loaded.\")\\n\\n    def load_state(self):\\n        try:\\n            with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                      \"rb\") as statefile:\\n                self.state = pickle.load(statefile)\\n            self.log.info(\"State loaded.\")\\n        except Exception as e:\\n            self.log.error(e)\\n            self.state = {}\\n            self.state[\\'last_mention_id\\'] = 1\\n            self.state[\\'mention_queue\\'] = []\\n\\n    def save_state(self):\\n        with open(path.join(self.config.get(\\'cachedir\\'), \"state.pkl\"),\\n                  \"wb\") as statefile:\\n            pickle.dump(self.state, statefile)\\n        self.log.info(\"State saved.\")\\n\\n    def start_engine(self):\\n        self.w2vengine = w2vEngine(self.modelpath)\\n\\n    def connect(self):\\n        auth = tweepy.OAuthHandler(self.config[\\'api_key\\'],\\n                                   self.config[\\'api_secret\\'])\\n        auth.set_access_token(self.config[\\'access_key\\'],\\n                              self.config[\\'access_secret\\'])\\n        self.api = tweepy.API(auth)\\n        self.id = self.api.me().id\\n        self.screen_name = self.api.me().screen_name\\n\\n    def check_mentions(self):\\n        try:\\n            current_mentions = self.api.mentions_timeline(since_id=self.state[\\'last_mention_id\\'],\\n                                                          count=100)\\n            # reduce to direct mentions only\\n            current_mentions = [t for t in current_mentions\\n                                if re.split(\\'[^@\\\\w]\\', t.text)[0]\\n                                == \\'@\\'+self.screen_name]\\n            if len(current_mentions) != 0:\\n                self.state[\\'last_mention_id\\'] = current_mentions[0].id\\n            self.state[\\'last_mention_time\\'] = time.time()\\n            self.state[\\'mention_queue\\'] += reversed(current_mentions)\\n            self.log.info(\\'Mentions updated ({} retrieved, {} total in queue)\\'\\n                          .format(len(current_mentions),\\n                                  len(self.state[\\'mention_queue\\'])))\\n        except tweepy.TweepError as e:\\n            self.log.error(\\'Can\\\\\\'t retrieve mentions.\\')\\n            self.log.error(e)\\n        except IncompleteRead as e:\\n            self.log.error(\\'Incomplete read error -- skipping mentions update\\')\\n\\n    def handle_mentions(self):\\n        for mention in self.state[\\'mention_queue\\']:\\n            prefix ', 'pretokenized_middle': '= self.get_mention_prefix(mention)'}\n", "= self.get_mention_prefix(mention\n"]}], "source": ["print(data_fim_retrieval[0])\n", "\n", "print(model.generate(\n", "    data_fim_retrieval[0]['context'],\n", "    GenerationOptions(temperature=0, max_generated_tokens=10),\n", "\n", "))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["= self.get_mention_prefix(mention\n"]}], "source": ["class NeuralLM:\n", "    \n", "    def __init__(self, n_sd):\n", "        self.n_sd = n_sd\n", "\n", "    def fit(self, tokens):\n", "        pass\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite):\n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        prediction_str = model.generate(\n", "            suffix,\n", "            GenerationOptions(temperature=0, max_generated_tokens=n_sd),\n", "        )\n", "        prediction_ids = tokenizer.tokenize(prediction_str)\n", "        assert prediction_str == tokenizer.detokenize(prediction_ids)\n", "        return [tuple(prediction_ids)]\n", "\n", "\n", "lm = NeuralLM(10)\n", "print(tokenizer.detokenize(lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 100)[0]))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# from https://docs.google.com/presentation/d/1V1XQ0IxEEPSekTvWW10KPR3Bg5BDRultJQGotCQd3mg/edit#slide=id.g237b45739c5_0_21\n", "LATENCY = {\n", "    1: 24.2,\n", "    2: 24.8,\n", "    4: 24.9,\n", "    8: 24.7,\n", "    16: 25,\n", "    20: 26.1,\n", "    24: 25.9,\n", "    28: 26,\n", "    32: 26.1,\n", "    64: 27.6,\n", "    96: 29,\n", "    128: 30.1,\n", "    160: 40.5,\n", "    192: 42,\n", "    224: 43,\n", "    256: 44,\n", "    384: 62,\n", "    512: 81.6,\n", "}\n", "\n", "LATENCY_KEYS = np.array(sorted(list(LATENCY.keys())))\n", "\n", "def estimate_latency(n):\n", "    assert n > 0 and n <= 512\n", "    if n in LATENCY:\n", "        return LATENCY[n]\n", "    n_lower_index = np.searchsorted(LATENCY_KEYS, n)\n", "    n_lower = LATENCY_KEYS[n_lower_index - 1]\n", "    n_upper = LATENCY_KEYS[n_lower_index]\n", "    return LATENCY[n_lower] * (n_upper - n) / (n_upper - n_lower) + LATENCY[n_upper] * (n - n_lower) / (n_upper - n_lower)\n", "\n", "for n in range(1, 512):\n", "    LATENCY[n] = estimate_latency(n)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'label_length': 12,\n", " 'n_rounds': 2,\n", " 'latency': 49.0125,\n", " 'prediction_latency': 757.6987743377686,\n", " 'beam_size': 1.0,\n", " 'n_unique_first_token': 1.0,\n", " 'n_unique_two_tokens': 1.0}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample):    \n", "    context = sample['context']\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    beam_sizes, prediction_latency, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 0\n", "        if max_tokens_to_predict > 0:            \n", "            start_time = time.time()\n", "\n", "            new_prefix = tokenizer.tokenize(context.prefix) + label[:index]\n", "            context = ModelInput(\n", "                prefix=tokenizer.detokenize(new_prefix),\n", "                suffix=context.suffix,\n", "                middle='',\n", "                retrieved_chunks=context.retrieved_chunks,\n", "                path=context.path,\n", "                target=None,\n", "            )\n", "\n", "            beam_of_predictions = lm.predict_next_k_tokens(\n", "                context,\n", "                max_tokens_to_predict)\n", "            \n", "            prediction_latency.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                actual_tokens_predicted += len(predictions) + 1\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            index = furthest_index\n", "\n", "        # Make prediction with the main model\n", "        index += 1\n", "        if actual_tokens_predicted == 0:\n", "            # no paralle SD is used at this step\n", "            latency += LATENCY[1]\n", "        else:\n", "            latency += LATENCY[actual_tokens_predicted]\n", "\n", "    return {\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "        'prediction_latency': safe_mean(prediction_latency),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "\n", "lm = NeuralLM(10)\n", "measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0]) "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["\n", "def measure_latency_per_data_parallel_sd(lm, data):\n", "    df = []\n", "    for d in data:\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d))        \n", "    df = pd.DataFrame(df)\n", "    df['latency_per_token'] = df['latency'] / df['label_length']\n", "    return df\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data):\n", "    df = []\n", "    for d in tqdm(data, total=len(data)):\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d))        \n", "    df = pd.DataFrame(df)\n", "    df['latency_per_token'] = df['latency'] / df['label_length']\n", "    return df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["N_SD =  1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fae9ea91a1574e999b613f518a566d72", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                 20.300000\n", "latency                 502.960000\n", "prediction_latency      349.607920\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token        13.711597\n", "dtype: float64\n", "N_SD =  2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e880d55827d441baa9da2b70e04d211", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                 14.500000\n", "latency                 359.935000\n", "prediction_latency      386.577786\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         9.953122\n", "dtype: float64\n", "N_SD =  4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "22eb82eecaf547df9a6de1f8c2a01d04", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                 10.700000\n", "latency                 265.545000\n", "prediction_latency      446.169772\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         7.553510\n", "dtype: float64\n", "N_SD =  6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b1640ae445ef4a71a224be4860308a7a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                  7.500000\n", "latency                 185.685000\n", "prediction_latency      511.226710\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         5.629776\n", "dtype: float64\n", "N_SD =  8\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "97eeed9dbfb04b38b16d49d50cda690b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                  6.400000\n", "latency                 158.433750\n", "prediction_latency      553.758296\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         5.085851\n", "dtype: float64\n", "N_SD =  10\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f8022452d4a14592922ca99d24ac2d9c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["label_length             35.300000\n", "n_rounds                  5.900000\n", "latency                 146.183750\n", "prediction_latency      607.502885\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         4.748849\n", "dtype: float64\n"]}], "source": ["for n_sd in [1, 2, 4, 6, 8, 10]:\n", "    print('N_SD = ', n_sd)\n", "    lm = NeuralLM(n_sd)\n", "    df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:10])    \n", "    # df.to_csv('/mnt/efs/augment/user/yury/stacklm/df_neural_1b_%d.csv' % n_sd)\n", "    print(df.mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["N_SD =  1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7a7e86f409184a83b4131b0fb43072f0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 38.596000\n", "latency                 956.705600\n", "prediction_latency      208.834043\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token        14.229266\n", "dtype: float64\n", "N_SD =  2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b779b7641304423b5a878d50ea3dff0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 28.878000\n", "latency                 717.059600\n", "prediction_latency      222.587978\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token        10.532427\n", "dtype: float64\n", "N_SD =  4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1847013b39c24484bf9d63a4311fd1b2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 20.849000\n", "latency                 517.756100\n", "prediction_latency      248.486483\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         7.665192\n", "dtype: float64\n", "N_SD =  6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "26a6e79eb14248689957002d8b5f908c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 17.619000\n", "latency                 436.411950\n", "prediction_latency      271.914831\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         6.504144\n", "dtype: float64\n", "N_SD =  8\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0b37918db05e4b31975e2c5b59b822ac", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 15.659000\n", "latency                 387.680825\n", "prediction_latency      294.573121\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         5.853120\n", "dtype: float64\n", "N_SD =  10\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a8b9bfa23602465291a3511aa4bc339e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 231\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 128\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 163\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 156\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 11\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 51\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 102\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 128\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 116\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 67\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 48\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 97\n", "[yury-dev:31204] tcp_peer_recv_connect_ack: invalid header type: 51\n", "WARNING:root:Generate with stop tokens returned an empty response.\n", "WARNING:root:Generate with stop tokens returned an empty response.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["label_length             64.987000\n", "n_rounds                 14.501000\n", "latency                 359.506037\n", "prediction_latency      317.504275\n", "beam_size                 1.000000\n", "n_unique_first_token      1.000000\n", "n_unique_two_tokens       1.000000\n", "latency_per_token         5.475591\n", "dtype: float64\n"]}], "source": ["for n_sd in [1, 2, 4, 6, 8, 10]:\n", "    print('N_SD = ', n_sd)\n", "    lm = NeuralLM(n_sd)\n", "    df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval)    \n", "    df.to_csv('/mnt/efs/augment/user/yury/stacklm/df_neural_1b_full_%d.csv' % n_sd)\n", "    print(df.mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = []\n", "for n_sd in [1, 2, 4, 6, 8, 10]:\n", "    path = '/mnt/efs/augment/user/yury/stacklm/df_neural_1b_full_%d.csv' % n_sd\n", "    df_tmp = pd.read_csv(path)\n", "    df_tmp[\"n_sd\"] = n_sd\n", "    dfs.append(df_tmp)\n", "\n", "df_neural_sd = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(10, 4))\n", "\n", "df_tmp = df_neural_sd.groupby([\"n_sd\"]).mean().reset_index()\n", "\n", "axes[0].hlines(y=24.2, xmin=0, xmax=10, label='no SD', color='red')\n", "axes[0].hlines(y=12.7368421, xmin=0, xmax=10, label='LongestOverlapLM (12 tokens, single)', color='green')\n", "axes[0].plot(\n", "    df_tmp[\"n_sd\"].values,\n", "    df_tmp[\"latency_per_token\"],\n", "    'o-',\n", "    label='Neural SD 1B'\n", ")\n", "axes[0].grid() \n", "axes[0].set_xlabel('Tokens predicted by Neural SD 1B model')\n", "axes[0].set_ylabel('Latency per token, ms')\n", "axes[0].set_ylabel('Latency per token, ms')\n", "axes[0].set_title('Latency if 1B model is free')\n", "axes[0].set_xticks([1, 2, 4, 6, 8, 10])\n", "axes[0].legend()\n", "\n", "axes[1].hlines(y=24.2, xmin=0, xmax=10, label='no SD', color='red')\n", "axes[1].hlines(y=12.7368421, xmin=0, xmax=10, label='LongestOverlapLM (12 tokens, single)', color='green')\n", "axes[1].plot(\n", "    df_tmp[\"n_sd\"],\n", "    df_tmp[\"latency_per_token\"] + np.arange(1, len(df_tmp[\"latency_per_token\"]) + 1) * 3.0,\n", "    'o-',\n", "    label='Neural SD 1B',\n", ")\n", "axes[1].grid()\n", "axes[1].set_xlabel('Tokens predicted by Neural SD 1B model')\n", "axes[1].set_ylabel('# of rounds')\n", "axes[1].set_title('Latency if 1B model is NOT free')\n", "axes[1].set_xticks([1, 2, 4, 6, 8, 10])\n", "axes[1].legend()\n", "\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
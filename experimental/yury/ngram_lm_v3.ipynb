{"cells": [{"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.150390625 16973\n"]}], "source": ["def encode_float_to_int16(value):\n", "    value_as_float16 = np.float16(value)\n", "    return np.frombuffer(value_as_float16.tobytes(), np.uint16, count=1)[0]\n", "\n", "def decode_float_from_int16(value):\n", "    return np.frombuffer(value.tobytes(), np.float16, count=1)[0]\n", "    \n", "x = float(np.float16(3.15))\n", "print(x, encode_float_to_int16(x))\n", "assert x == decode_float_from_int16(encode_float_to_int16(x))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# from https://docs.google.com/presentation/d/1V1XQ0IxEEPSekTvWW10KPR3Bg5BDRultJQGotCQd3mg/edit#slide=id.g237b45739c5_0_21\n", "LATENCY = {\n", "    1: 24.2,\n", "    2: 24.8,\n", "    4: 24.9,\n", "    8: 24.7,\n", "    16: 25,\n", "    20: 26.1,\n", "    24: 25.9,\n", "    28: 26,\n", "    32: 26.1,\n", "    64: 27.6,\n", "    96: 29,\n", "    128: 30.1,\n", "    160: 40.5,\n", "    192: 42,\n", "    224: 43,\n", "    256: 44,\n", "    384: 62,\n", "    512: 81.6,\n", "}\n", "\n", "LATENCY_KEYS = np.array(sorted(list(LATENCY.keys())))\n", "\n", "def estimate_latency(n):\n", "    assert n > 0 and n <= 512\n", "    if n in LATENCY:\n", "        return LATENCY[n]\n", "    n_lower_index = np.searchsorted(LATENCY_KEYS, n)\n", "    n_lower = LATENCY_KEYS[n_lower_index - 1]\n", "    n_upper = LATENCY_KEYS[n_lower_index]\n", "    return LATENCY[n_lower] * (n_upper - n) / (n_upper - n_lower) + LATENCY[n_upper] * (n - n_lower) / (n_upper - n_lower)\n", "\n", "for n in range(1, 512):\n", "    LATENCY[n] = estimate_latency(n)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n"]}], "source": ["from research.retrieval.types import Chunk, Document\n", "import json\n", "from typing import Any, List, Dict\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "from research.retrieval import utils as rutils\n", "\n", "\n", "def prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix):\n", "    metadata = {\n", "        'num_prefix_chars_post_truncation': len(model_input['prefix']),\n", "        'num_suffix_chars_post_truncation': len(model_input['suffix']),\n", "    }\n", "    context = []\n", "    retrieved_chunks = model_input['retrieved_chunks']\n", "    if n_retrievals is not None:\n", "        retrieved_chunks = retrieved_chunks[:n_retrievals]\n", "    for chunk in reversed(retrieved_chunks):\n", "        context.extend(tokenizer.tokenize(chunk.text) + [tokenizer.eod_id])\n", "    if not remove_prefix_and_suffix:\n", "        context.extend(tokenizer.tokenize(model_input['suffix']))    \n", "        context.extend(tokenizer.tokenize(model_input['prefix']))\n", "    return context, metadata\n", "\n", "\n", "def generate_prompt(\n", "    prefix: str,\n", "    middle: str,\n", "    suffix: str,\n", "    middle_char_start: int,\n", "    middle_char_end: int,\n", "    file_path: str,\n", "    retrieved_chunk_str: str,\n", "    max_target_tokens: int,\n", "    n_retrievals,\n", "    remove_prefix_and_suffix=False,\n", ") -> List[int]:\n", "    \"\"\"Construct a token prompt.\"\"\"\n", "\n", "    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)\n", "\n", "    # Remove chunks that overlap with middle\n", "    filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(middle_char_start, middle_char_end),\n", "        retrieved_chunks,\n", "    )\n", "\n", "    model_input = dict(\n", "        prefix=prefix,\n", "        middle=middle,\n", "        suffix=suffix,\n", "        retrieved_chunks=filtered_chunks,\n", "        path=file_path,\n", "    )\n", "\n", "    # TODO(michiel) Add option for sampling different prompt styles\n", "    _, metadata = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    # Remove chunks that overlap with prefix or suffix\n", "    new_filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(\n", "            middle_char_start - metadata[\"num_prefix_chars_post_truncation\"],\n", "            middle_char_end,\n", "        ),\n", "        filtered_chunks,\n", "    )\n", "    if metadata[\"num_suffix_chars_post_truncation\"] > 0:\n", "        new_filtered_chunks = rutils.filter_overlap_chunks(\n", "            file_path,\n", "            rutils.Span(\n", "                middle_char_end,\n", "                middle_char_end + metadata[\"num_suffix_chars_post_truncation\"],\n", "            ),\n", "            new_filtered_chunks,\n", "        )\n", "\n", "    model_input['retrieved_chunks'] = new_filtered_chunks\n", "    prompt_tokens, _ = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    \n", "    target_tokens = tokenizer.tokenize(middle + \"<|endoftext|>\")    \n", "    target_tokens = target_tokens[:max_target_tokens]\n", "\n", "    return prompt_tokens, target_tokens\n", "\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for row_index, datum in df.iterrows():\n", "            n_read_samples += 1\n", "            context, label = generate_prompt(\n", "                prefix=datum['prefix'],\n", "                middle=datum['middle'],\n", "                suffix=datum['suffix'],\n", "                middle_char_start=datum['middle_char_start'],\n", "                middle_char_end=datum['middle_char_end'],\n", "                file_path=datum['file_path'],\n", "                retrieved_chunk_str=datum['retrieved_chunks'],\n", "                max_target_tokens=max_tokens_to_predict,\n", "                n_retrievals=n_retrievals,\n", "                remove_prefix_and_suffix=remove_prefix_and_suffix)        \n", "\n", "            context = np.array(context)\n", "            label = np.array(label)\n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'prefix_suffix_len': len(tokenizer.tokenize(datum['prefix'] + datum['suffix'])),\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample):\n", "    context = sample['context'].tolist()\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    beam_sizes, prediction_latency, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 0\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            beam_of_predictions = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict)\n", "            prediction_latency.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                actual_tokens_predicted += len(predictions) + 1\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            index = furthest_index\n", "\n", "        # Make prediction with the main model\n", "        index += 1\n", "        if actual_tokens_predicted == 0:\n", "            # no paralle SD is used at this step\n", "            latency += LATENCY[1]\n", "        else:\n", "            latency += LATENCY[actual_tokens_predicted]\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "        'prediction_latency': safe_mean(prediction_latency),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def measure_latency_per_data_parallel_sd(lm, data):\n", "    df = []\n", "    for d in data:\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d))        \n", "    df = pd.DataFrame(df)\n", "    df['latency_per_token'] = df['latency'] / df['label_length']\n", "    return df\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data):\n", "    df = []\n", "    for d in tqdm(data, total=len(data)):\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d))        \n", "    df = pd.DataFrame(df)\n", "    df['latency_per_token'] = df['latency'] / df['label_length']\n", "    return df"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["[8, 4]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["import warnings\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(100000))\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])\n"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[((4, 2), 2), ((2, 3), 1)]\n", "[(5,), (4, 2), (2, 3)]\n", "[(5,), (4,), (2,)]\n"]}], "source": ["class LongestOverlapLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd, allow_predicting_less_than_k_tokens=False, n_predictions_per_overlap=None):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "        self.allow_predicting_less_than_k_tokens = allow_predicting_less_than_k_tokens\n", "        self.n_predictions_per_overlap = n_predictions_per_overlap\n", "        if self.n_predictions_per_overlap:\n", "            assert self.allow_predicting_less_than_k_tokens\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, return_overlap=False):        \n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        n_sd = min(n_sd, len(self.tokens) - 1)\n", "        \n", "        if n_sd == 0:\n", "            return []\n", "        \n", "        assert self.n_parallel_sd > 0\n", "        if len(self.tokens) < n_sd:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return []\n", "        \n", "        if self.allow_predicting_less_than_k_tokens:\n", "            searchable_tokens = self.tokens[:-1]\n", "        else:\n", "            searchable_tokens = self.tokens[:-n_sd]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return []\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue           \n", "                if self.n_predictions_per_overlap is not None:     \n", "                    assert l > 0\n", "                    if l < len(self.n_predictions_per_overlap):\n", "                        current_n_sd = self.n_predictions_per_overlap[l]\n", "                    else:\n", "                        current_n_sd = self.n_predictions_per_overlap[0]\n", "                    current_n_sd = min(current_n_sd, n_sd)\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + current_n_sd + 1, len(self.tokens))])\n", "                else:\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + n_sd + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if not self.allow_predicting_less_than_k_tokens:\n", "                    assert len(current_prediction) == n_sd\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                if return_overlap:\n", "                    predictions.append((current_prediction, l))\n", "                else:\n", "                    predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= self.n_parallel_sd:\n", "                    break\n", "            if len(predictions) >= self.n_parallel_sd:\n", "                break                \n", "        return predictions\n", "\n", "\n", "predictor = LongestOverlapLM(2, 3)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2, True))\n", "\n", "predictor = LongestOverlapLM(2, 3, True)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))\n", "\n", "predictor = LongestOverlapLM(2, 3, True, n_predictions_per_overlap=[1])\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  64.987000\n", "latency                 1572.685400\n", "prediction_latency         0.007901\n", "beam_size                  0.000000\n", "n_unique_first_token       0.000000\n", "n_unique_two_tokens        0.000000\n", "latency_per_token         24.200000\n", "dtype: float64\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.188000\n", "latency                  722.106987\n", "prediction_latency         0.535841\n", "beam_size                  0.816559\n", "n_unique_first_token       0.816559\n", "n_unique_two_tokens        0.816559\n", "latency_per_token         12.677419\n", "dtype: float64\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.007000\n", "latency                  665.050162\n", "prediction_latency         0.869172\n", "beam_size                  3.881310\n", "n_unique_first_token       2.433548\n", "n_unique_two_tokens        2.991534\n", "latency_per_token         11.723032\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM(0, 1, True)\n", "df_baseline = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "print(df_baseline.mean())\n", "lm = LongestOverlapLM(12, 1, True)\n", "df_longestoverlap = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "print(df_longestoverlap.mean())\n", "lm = LongestOverlapLM(12, 6, True)\n", "df_longestoverlap = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "print(df_longestoverlap.mean())"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e7ba504a2c2246e3af435912121ff1af", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/20 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>context_length</th>\n", "      <th>label_length</th>\n", "      <th>n_rounds</th>\n", "      <th>latency</th>\n", "      <th>prediction_latency</th>\n", "      <th>beam_size</th>\n", "      <th>n_unique_first_token</th>\n", "      <th>n_unique_two_tokens</th>\n", "      <th>latency_per_token</th>\n", "    </tr>\n", "    <tr>\n", "      <th>n_sd</th>\n", "      <th>n_parallel_sd</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>64.987</td>\n", "      <td>1572.685400</td>\n", "      <td>0.007735</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>24.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"20\" valign=\"top\">12</th>\n", "      <th>1</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>29.188</td>\n", "      <td>722.106987</td>\n", "      <td>0.538264</td>\n", "      <td>0.816559</td>\n", "      <td>0.816559</td>\n", "      <td>0.816559</td>\n", "      <td>12.677419</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>27.165</td>\n", "      <td>689.954300</td>\n", "      <td>0.803589</td>\n", "      <td>1.525594</td>\n", "      <td>1.239432</td>\n", "      <td>1.355291</td>\n", "      <td>12.165312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>26.218</td>\n", "      <td>673.350094</td>\n", "      <td>0.841139</td>\n", "      <td>2.176895</td>\n", "      <td>1.588347</td>\n", "      <td>1.816668</td>\n", "      <td>11.925020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.672</td>\n", "      <td>667.772019</td>\n", "      <td>0.854917</td>\n", "      <td>2.782544</td>\n", "      <td>1.901279</td>\n", "      <td>2.243449</td>\n", "      <td>11.799702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.287</td>\n", "      <td>665.581341</td>\n", "      <td>0.855285</td>\n", "      <td>3.348626</td>\n", "      <td>2.178060</td>\n", "      <td>2.630440</td>\n", "      <td>11.754797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.007</td>\n", "      <td>665.050162</td>\n", "      <td>0.865037</td>\n", "      <td>3.881310</td>\n", "      <td>2.433548</td>\n", "      <td>2.991534</td>\n", "      <td>11.723032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.788</td>\n", "      <td>665.669328</td>\n", "      <td>0.867568</td>\n", "      <td>4.382281</td>\n", "      <td>2.657066</td>\n", "      <td>3.323097</td>\n", "      <td>11.719354</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.609</td>\n", "      <td>666.203300</td>\n", "      <td>0.878212</td>\n", "      <td>4.861549</td>\n", "      <td>2.871479</td>\n", "      <td>3.628808</td>\n", "      <td>11.722577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.474</td>\n", "      <td>667.183291</td>\n", "      <td>0.875623</td>\n", "      <td>5.323069</td>\n", "      <td>3.071157</td>\n", "      <td>3.917126</td>\n", "      <td>11.740084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.365</td>\n", "      <td>672.812681</td>\n", "      <td>0.879073</td>\n", "      <td>5.771112</td>\n", "      <td>3.253470</td>\n", "      <td>4.195978</td>\n", "      <td>11.807992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.260</td>\n", "      <td>704.447472</td>\n", "      <td>0.874564</td>\n", "      <td>6.189832</td>\n", "      <td>3.427892</td>\n", "      <td>4.458019</td>\n", "      <td>12.108743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.160</td>\n", "      <td>737.582322</td>\n", "      <td>0.891628</td>\n", "      <td>6.595800</td>\n", "      <td>3.591458</td>\n", "      <td>4.700373</td>\n", "      <td>12.428301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.081</td>\n", "      <td>753.170791</td>\n", "      <td>0.880575</td>\n", "      <td>6.992074</td>\n", "      <td>3.753296</td>\n", "      <td>4.939657</td>\n", "      <td>12.599913</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.015</td>\n", "      <td>757.336131</td>\n", "      <td>0.896112</td>\n", "      <td>7.379078</td>\n", "      <td>3.903252</td>\n", "      <td>5.169158</td>\n", "      <td>12.667379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.955</td>\n", "      <td>760.615650</td>\n", "      <td>0.884697</td>\n", "      <td>7.756820</td>\n", "      <td>4.049328</td>\n", "      <td>5.389449</td>\n", "      <td>12.721449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.901</td>\n", "      <td>762.666903</td>\n", "      <td>0.883732</td>\n", "      <td>8.126374</td>\n", "      <td>4.189269</td>\n", "      <td>5.609679</td>\n", "      <td>12.762899</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.855</td>\n", "      <td>764.495097</td>\n", "      <td>0.894579</td>\n", "      <td>8.479386</td>\n", "      <td>4.321929</td>\n", "      <td>5.824059</td>\n", "      <td>12.797297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.813</td>\n", "      <td>766.570550</td>\n", "      <td>0.897449</td>\n", "      <td>8.822722</td>\n", "      <td>4.454957</td>\n", "      <td>6.025289</td>\n", "      <td>12.837411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.774</td>\n", "      <td>768.498428</td>\n", "      <td>0.898085</td>\n", "      <td>9.159240</td>\n", "      <td>4.583597</td>\n", "      <td>6.233343</td>\n", "      <td>12.873699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>23.747</td>\n", "      <td>773.064684</td>\n", "      <td>0.904635</td>\n", "      <td>9.490173</td>\n", "      <td>4.705194</td>\n", "      <td>6.431136</td>\n", "      <td>12.930700</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    context_length  label_length  n_rounds      latency   \n", "n_sd n_parallel_sd                                                        \n", "0    0                    4906.744        64.987    64.987  1572.685400  \\\n", "12   1                    4906.744        64.987    29.188   722.106987   \n", "     2                    4906.744        64.987    27.165   689.954300   \n", "     3                    4906.744        64.987    26.218   673.350094   \n", "     4                    4906.744        64.987    25.672   667.772019   \n", "     5                    4906.744        64.987    25.287   665.581341   \n", "     6                    4906.744        64.987    25.007   665.050162   \n", "     7                    4906.744        64.987    24.788   665.669328   \n", "     8                    4906.744        64.987    24.609   666.203300   \n", "     9                    4906.744        64.987    24.474   667.183291   \n", "     10                   4906.744        64.987    24.365   672.812681   \n", "     11                   4906.744        64.987    24.260   704.447472   \n", "     12                   4906.744        64.987    24.160   737.582322   \n", "     13                   4906.744        64.987    24.081   753.170791   \n", "     14                   4906.744        64.987    24.015   757.336131   \n", "     15                   4906.744        64.987    23.955   760.615650   \n", "     16                   4906.744        64.987    23.901   762.666903   \n", "     17                   4906.744        64.987    23.855   764.495097   \n", "     18                   4906.744        64.987    23.813   766.570550   \n", "     19                   4906.744        64.987    23.774   768.498428   \n", "     20                   4906.744        64.987    23.747   773.064684   \n", "\n", "                    prediction_latency  beam_size  n_unique_first_token   \n", "n_sd n_parallel_sd                                                        \n", "0    0                        0.007735   0.000000              0.000000  \\\n", "12   1                        0.538264   0.816559              0.816559   \n", "     2                        0.803589   1.525594              1.239432   \n", "     3                        0.841139   2.176895              1.588347   \n", "     4                        0.854917   2.782544              1.901279   \n", "     5                        0.855285   3.348626              2.178060   \n", "     6                        0.865037   3.881310              2.433548   \n", "     7                        0.867568   4.382281              2.657066   \n", "     8                        0.878212   4.861549              2.871479   \n", "     9                        0.875623   5.323069              3.071157   \n", "     10                       0.879073   5.771112              3.253470   \n", "     11                       0.874564   6.189832              3.427892   \n", "     12                       0.891628   6.595800              3.591458   \n", "     13                       0.880575   6.992074              3.753296   \n", "     14                       0.896112   7.379078              3.903252   \n", "     15                       0.884697   7.756820              4.049328   \n", "     16                       0.883732   8.126374              4.189269   \n", "     17                       0.894579   8.479386              4.321929   \n", "     18                       0.897449   8.822722              4.454957   \n", "     19                       0.898085   9.159240              4.583597   \n", "     20                       0.904635   9.490173              4.705194   \n", "\n", "                    n_unique_two_tokens  latency_per_token  \n", "n_sd n_parallel_sd                                          \n", "0    0                         0.000000          24.200000  \n", "12   1                         0.816559          12.677419  \n", "     2                         1.355291          12.165312  \n", "     3                         1.816668          11.925020  \n", "     4                         2.243449          11.799702  \n", "     5                         2.630440          11.754797  \n", "     6                         2.991534          11.723032  \n", "     7                         3.323097          11.719354  \n", "     8                         3.628808          11.722577  \n", "     9                         3.917126          11.740084  \n", "     10                        4.195978          11.807992  \n", "     11                        4.458019          12.108743  \n", "     12                        4.700373          12.428301  \n", "     13                        4.939657          12.599913  \n", "     14                        5.169158          12.667379  \n", "     15                        5.389449          12.721449  \n", "     16                        5.609679          12.762899  \n", "     17                        5.824059          12.797297  \n", "     18                        6.025289          12.837411  \n", "     19                        6.233343          12.873699  \n", "     20                        6.431136          12.930700  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["lm = LongestOverlapLM(n_sd=0, n_parallel_sd=0, allow_predicting_less_than_k_tokens=True)\n", "df = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "df[\"n_sd\"] = 0\n", "df[\"n_parallel_sd\"] = 0\n", "dfs = [df]\n", "\n", "for n_parallel_sd in tqdm(range(1, 21)):\n", "    lm = LongestOverlapLM(n_sd=12, n_parallel_sd=n_parallel_sd, allow_predicting_less_than_k_tokens=True)\n", "    df = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "    df[\"n_sd\"] = 12\n", "    df[\"n_parallel_sd\"] = n_parallel_sd\n", "    dfs.append(df)\n", "\n", "df_parralel_sd = pd.concat(dfs)\n", "df_parralel_sd.groupby([\"n_sd\", \"n_parallel_sd\"]).mean()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54c898827d7843188d2d8c904c4a19e8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>context_length</th>\n", "      <th>label_length</th>\n", "      <th>n_rounds</th>\n", "      <th>latency</th>\n", "      <th>prediction_latency</th>\n", "      <th>beam_size</th>\n", "      <th>n_unique_first_token</th>\n", "      <th>n_unique_two_tokens</th>\n", "      <th>latency_per_token</th>\n", "    </tr>\n", "    <tr>\n", "      <th>n_sd</th>\n", "      <th>n_parallel_sd</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>64.987</td>\n", "      <td>1572.685400</td>\n", "      <td>0.008043</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>24.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>40.515</td>\n", "      <td>1002.374650</td>\n", "      <td>0.996550</td>\n", "      <td>3.509328</td>\n", "      <td>3.509328</td>\n", "      <td>3.509328</td>\n", "      <td>16.396506</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>33.438</td>\n", "      <td>838.829075</td>\n", "      <td>0.948836</td>\n", "      <td>3.798974</td>\n", "      <td>2.965174</td>\n", "      <td>3.798974</td>\n", "      <td>14.111440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>30.272</td>\n", "      <td>766.228688</td>\n", "      <td>0.925804</td>\n", "      <td>3.860131</td>\n", "      <td>2.749667</td>\n", "      <td>3.465422</td>\n", "      <td>13.116050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>28.504</td>\n", "      <td>724.669487</td>\n", "      <td>0.920666</td>\n", "      <td>3.873576</td>\n", "      <td>2.623729</td>\n", "      <td>3.279414</td>\n", "      <td>12.517814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>27.439</td>\n", "      <td>701.256553</td>\n", "      <td>0.908501</td>\n", "      <td>3.898290</td>\n", "      <td>2.561635</td>\n", "      <td>3.202174</td>\n", "      <td>12.218998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>26.729</td>\n", "      <td>687.473453</td>\n", "      <td>0.898080</td>\n", "      <td>3.896714</td>\n", "      <td>2.527241</td>\n", "      <td>3.137046</td>\n", "      <td>12.038587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>26.221</td>\n", "      <td>677.947541</td>\n", "      <td>0.895735</td>\n", "      <td>3.904520</td>\n", "      <td>2.510577</td>\n", "      <td>3.106931</td>\n", "      <td>11.921857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.861</td>\n", "      <td>673.054387</td>\n", "      <td>0.894353</td>\n", "      <td>3.883798</td>\n", "      <td>2.477395</td>\n", "      <td>3.059395</td>\n", "      <td>11.852361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.557</td>\n", "      <td>669.506584</td>\n", "      <td>0.876185</td>\n", "      <td>3.891662</td>\n", "      <td>2.466050</td>\n", "      <td>3.042737</td>\n", "      <td>11.793753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.323</td>\n", "      <td>666.773228</td>\n", "      <td>0.871350</td>\n", "      <td>3.889916</td>\n", "      <td>2.451379</td>\n", "      <td>3.022199</td>\n", "      <td>11.761607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.143</td>\n", "      <td>665.329012</td>\n", "      <td>0.863601</td>\n", "      <td>3.886226</td>\n", "      <td>2.438375</td>\n", "      <td>3.012132</td>\n", "      <td>11.733901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>25.007</td>\n", "      <td>665.050162</td>\n", "      <td>0.862964</td>\n", "      <td>3.881310</td>\n", "      <td>2.433548</td>\n", "      <td>2.991534</td>\n", "      <td>11.723032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.885</td>\n", "      <td>665.079987</td>\n", "      <td>0.856640</td>\n", "      <td>3.888031</td>\n", "      <td>2.426763</td>\n", "      <td>2.991203</td>\n", "      <td>11.719432</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.806</td>\n", "      <td>666.213747</td>\n", "      <td>0.859808</td>\n", "      <td>3.878134</td>\n", "      <td>2.417363</td>\n", "      <td>2.972589</td>\n", "      <td>11.721622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.723</td>\n", "      <td>667.034403</td>\n", "      <td>0.854511</td>\n", "      <td>3.888253</td>\n", "      <td>2.418917</td>\n", "      <td>2.975836</td>\n", "      <td>11.728683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.656</td>\n", "      <td>668.214350</td>\n", "      <td>0.851445</td>\n", "      <td>3.877264</td>\n", "      <td>2.411323</td>\n", "      <td>2.965771</td>\n", "      <td>11.730026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.596</td>\n", "      <td>669.465456</td>\n", "      <td>0.848987</td>\n", "      <td>3.874849</td>\n", "      <td>2.408052</td>\n", "      <td>2.965994</td>\n", "      <td>11.734563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.554</td>\n", "      <td>671.135178</td>\n", "      <td>0.842427</td>\n", "      <td>3.879774</td>\n", "      <td>2.410066</td>\n", "      <td>2.963997</td>\n", "      <td>11.746776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.505</td>\n", "      <td>672.494325</td>\n", "      <td>0.856523</td>\n", "      <td>3.876317</td>\n", "      <td>2.409471</td>\n", "      <td>2.956934</td>\n", "      <td>11.752366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.458</td>\n", "      <td>673.370222</td>\n", "      <td>0.842067</td>\n", "      <td>3.869373</td>\n", "      <td>2.397883</td>\n", "      <td>2.951226</td>\n", "      <td>11.754818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.427</td>\n", "      <td>682.573784</td>\n", "      <td>0.840352</td>\n", "      <td>3.869718</td>\n", "      <td>2.396851</td>\n", "      <td>2.949263</td>\n", "      <td>11.824715</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.402</td>\n", "      <td>696.481181</td>\n", "      <td>0.848916</td>\n", "      <td>3.858435</td>\n", "      <td>2.395784</td>\n", "      <td>2.942273</td>\n", "      <td>11.928765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.367</td>\n", "      <td>710.890481</td>\n", "      <td>0.840327</td>\n", "      <td>3.867450</td>\n", "      <td>2.398273</td>\n", "      <td>2.946749</td>\n", "      <td>12.038016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.340</td>\n", "      <td>726.142441</td>\n", "      <td>0.846264</td>\n", "      <td>3.866418</td>\n", "      <td>2.396482</td>\n", "      <td>2.945636</td>\n", "      <td>12.151626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.317</td>\n", "      <td>741.900156</td>\n", "      <td>0.842555</td>\n", "      <td>3.856260</td>\n", "      <td>2.391325</td>\n", "      <td>2.932738</td>\n", "      <td>12.265150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.300</td>\n", "      <td>755.135750</td>\n", "      <td>0.842013</td>\n", "      <td>3.865371</td>\n", "      <td>2.392681</td>\n", "      <td>2.938776</td>\n", "      <td>12.360897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.279</td>\n", "      <td>761.151847</td>\n", "      <td>0.839372</td>\n", "      <td>3.859029</td>\n", "      <td>2.389408</td>\n", "      <td>2.934796</td>\n", "      <td>12.402846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.272</td>\n", "      <td>766.739609</td>\n", "      <td>0.823886</td>\n", "      <td>3.857207</td>\n", "      <td>2.384337</td>\n", "      <td>2.925560</td>\n", "      <td>12.442486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.255</td>\n", "      <td>771.551331</td>\n", "      <td>0.840326</td>\n", "      <td>3.856938</td>\n", "      <td>2.383366</td>\n", "      <td>2.929079</td>\n", "      <td>12.472814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.238</td>\n", "      <td>775.518559</td>\n", "      <td>0.837161</td>\n", "      <td>3.861433</td>\n", "      <td>2.387090</td>\n", "      <td>2.931011</td>\n", "      <td>12.499572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.222</td>\n", "      <td>778.751203</td>\n", "      <td>0.826120</td>\n", "      <td>3.858907</td>\n", "      <td>2.384661</td>\n", "      <td>2.930018</td>\n", "      <td>12.520503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.209</td>\n", "      <td>781.460606</td>\n", "      <td>0.836932</td>\n", "      <td>3.859981</td>\n", "      <td>2.387227</td>\n", "      <td>2.929978</td>\n", "      <td>12.538783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.206</td>\n", "      <td>784.387175</td>\n", "      <td>0.829790</td>\n", "      <td>3.852545</td>\n", "      <td>2.379495</td>\n", "      <td>2.923530</td>\n", "      <td>12.556721</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.199</td>\n", "      <td>787.024937</td>\n", "      <td>0.827145</td>\n", "      <td>3.856971</td>\n", "      <td>2.382359</td>\n", "      <td>2.924350</td>\n", "      <td>12.573131</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.184</td>\n", "      <td>789.193469</td>\n", "      <td>0.835603</td>\n", "      <td>3.851637</td>\n", "      <td>2.381164</td>\n", "      <td>2.923368</td>\n", "      <td>12.586408</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.182</td>\n", "      <td>791.640016</td>\n", "      <td>0.837405</td>\n", "      <td>3.859299</td>\n", "      <td>2.387088</td>\n", "      <td>2.929110</td>\n", "      <td>12.603245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.174</td>\n", "      <td>793.818622</td>\n", "      <td>0.831833</td>\n", "      <td>3.857848</td>\n", "      <td>2.380838</td>\n", "      <td>2.921607</td>\n", "      <td>12.617143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.163</td>\n", "      <td>795.736409</td>\n", "      <td>0.829481</td>\n", "      <td>3.856821</td>\n", "      <td>2.377984</td>\n", "      <td>2.919557</td>\n", "      <td>12.627762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.157</td>\n", "      <td>797.772194</td>\n", "      <td>0.823895</td>\n", "      <td>3.859569</td>\n", "      <td>2.381362</td>\n", "      <td>2.920332</td>\n", "      <td>12.640629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <th>6</th>\n", "      <td>4906.744</td>\n", "      <td>64.987</td>\n", "      <td>24.150</td>\n", "      <td>799.365219</td>\n", "      <td>0.820292</td>\n", "      <td>3.857457</td>\n", "      <td>2.379008</td>\n", "      <td>2.920318</td>\n", "      <td>12.649429</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    context_length  label_length  n_rounds      latency   \n", "n_sd n_parallel_sd                                                        \n", "0    0                    4906.744        64.987    64.987  1572.685400  \\\n", "1    6                    4906.744        64.987    40.515  1002.374650   \n", "2    6                    4906.744        64.987    33.438   838.829075   \n", "3    6                    4906.744        64.987    30.272   766.228688   \n", "4    6                    4906.744        64.987    28.504   724.669487   \n", "5    6                    4906.744        64.987    27.439   701.256553   \n", "6    6                    4906.744        64.987    26.729   687.473453   \n", "7    6                    4906.744        64.987    26.221   677.947541   \n", "8    6                    4906.744        64.987    25.861   673.054387   \n", "9    6                    4906.744        64.987    25.557   669.506584   \n", "10   6                    4906.744        64.987    25.323   666.773228   \n", "11   6                    4906.744        64.987    25.143   665.329012   \n", "12   6                    4906.744        64.987    25.007   665.050162   \n", "13   6                    4906.744        64.987    24.885   665.079987   \n", "14   6                    4906.744        64.987    24.806   666.213747   \n", "15   6                    4906.744        64.987    24.723   667.034403   \n", "16   6                    4906.744        64.987    24.656   668.214350   \n", "17   6                    4906.744        64.987    24.596   669.465456   \n", "18   6                    4906.744        64.987    24.554   671.135178   \n", "19   6                    4906.744        64.987    24.505   672.494325   \n", "20   6                    4906.744        64.987    24.458   673.370222   \n", "21   6                    4906.744        64.987    24.427   682.573784   \n", "22   6                    4906.744        64.987    24.402   696.481181   \n", "23   6                    4906.744        64.987    24.367   710.890481   \n", "24   6                    4906.744        64.987    24.340   726.142441   \n", "25   6                    4906.744        64.987    24.317   741.900156   \n", "26   6                    4906.744        64.987    24.300   755.135750   \n", "27   6                    4906.744        64.987    24.279   761.151847   \n", "28   6                    4906.744        64.987    24.272   766.739609   \n", "29   6                    4906.744        64.987    24.255   771.551331   \n", "30   6                    4906.744        64.987    24.238   775.518559   \n", "31   6                    4906.744        64.987    24.222   778.751203   \n", "32   6                    4906.744        64.987    24.209   781.460606   \n", "33   6                    4906.744        64.987    24.206   784.387175   \n", "34   6                    4906.744        64.987    24.199   787.024937   \n", "35   6                    4906.744        64.987    24.184   789.193469   \n", "36   6                    4906.744        64.987    24.182   791.640016   \n", "37   6                    4906.744        64.987    24.174   793.818622   \n", "38   6                    4906.744        64.987    24.163   795.736409   \n", "39   6                    4906.744        64.987    24.157   797.772194   \n", "40   6                    4906.744        64.987    24.150   799.365219   \n", "\n", "                    prediction_latency  beam_size  n_unique_first_token   \n", "n_sd n_parallel_sd                                                        \n", "0    0                        0.008043   0.000000              0.000000  \\\n", "1    6                        0.996550   3.509328              3.509328   \n", "2    6                        0.948836   3.798974              2.965174   \n", "3    6                        0.925804   3.860131              2.749667   \n", "4    6                        0.920666   3.873576              2.623729   \n", "5    6                        0.908501   3.898290              2.561635   \n", "6    6                        0.898080   3.896714              2.527241   \n", "7    6                        0.895735   3.904520              2.510577   \n", "8    6                        0.894353   3.883798              2.477395   \n", "9    6                        0.876185   3.891662              2.466050   \n", "10   6                        0.871350   3.889916              2.451379   \n", "11   6                        0.863601   3.886226              2.438375   \n", "12   6                        0.862964   3.881310              2.433548   \n", "13   6                        0.856640   3.888031              2.426763   \n", "14   6                        0.859808   3.878134              2.417363   \n", "15   6                        0.854511   3.888253              2.418917   \n", "16   6                        0.851445   3.877264              2.411323   \n", "17   6                        0.848987   3.874849              2.408052   \n", "18   6                        0.842427   3.879774              2.410066   \n", "19   6                        0.856523   3.876317              2.409471   \n", "20   6                        0.842067   3.869373              2.397883   \n", "21   6                        0.840352   3.869718              2.396851   \n", "22   6                        0.848916   3.858435              2.395784   \n", "23   6                        0.840327   3.867450              2.398273   \n", "24   6                        0.846264   3.866418              2.396482   \n", "25   6                        0.842555   3.856260              2.391325   \n", "26   6                        0.842013   3.865371              2.392681   \n", "27   6                        0.839372   3.859029              2.389408   \n", "28   6                        0.823886   3.857207              2.384337   \n", "29   6                        0.840326   3.856938              2.383366   \n", "30   6                        0.837161   3.861433              2.387090   \n", "31   6                        0.826120   3.858907              2.384661   \n", "32   6                        0.836932   3.859981              2.387227   \n", "33   6                        0.829790   3.852545              2.379495   \n", "34   6                        0.827145   3.856971              2.382359   \n", "35   6                        0.835603   3.851637              2.381164   \n", "36   6                        0.837405   3.859299              2.387088   \n", "37   6                        0.831833   3.857848              2.380838   \n", "38   6                        0.829481   3.856821              2.377984   \n", "39   6                        0.823895   3.859569              2.381362   \n", "40   6                        0.820292   3.857457              2.379008   \n", "\n", "                    n_unique_two_tokens  latency_per_token  \n", "n_sd n_parallel_sd                                          \n", "0    0                         0.000000          24.200000  \n", "1    6                         3.509328          16.396506  \n", "2    6                         3.798974          14.111440  \n", "3    6                         3.465422          13.116050  \n", "4    6                         3.279414          12.517814  \n", "5    6                         3.202174          12.218998  \n", "6    6                         3.137046          12.038587  \n", "7    6                         3.106931          11.921857  \n", "8    6                         3.059395          11.852361  \n", "9    6                         3.042737          11.793753  \n", "10   6                         3.022199          11.761607  \n", "11   6                         3.012132          11.733901  \n", "12   6                         2.991534          11.723032  \n", "13   6                         2.991203          11.719432  \n", "14   6                         2.972589          11.721622  \n", "15   6                         2.975836          11.728683  \n", "16   6                         2.965771          11.730026  \n", "17   6                         2.965994          11.734563  \n", "18   6                         2.963997          11.746776  \n", "19   6                         2.956934          11.752366  \n", "20   6                         2.951226          11.754818  \n", "21   6                         2.949263          11.824715  \n", "22   6                         2.942273          11.928765  \n", "23   6                         2.946749          12.038016  \n", "24   6                         2.945636          12.151626  \n", "25   6                         2.932738          12.265150  \n", "26   6                         2.938776          12.360897  \n", "27   6                         2.934796          12.402846  \n", "28   6                         2.925560          12.442486  \n", "29   6                         2.929079          12.472814  \n", "30   6                         2.931011          12.499572  \n", "31   6                         2.930018          12.520503  \n", "32   6                         2.929978          12.538783  \n", "33   6                         2.923530          12.556721  \n", "34   6                         2.924350          12.573131  \n", "35   6                         2.923368          12.586408  \n", "36   6                         2.929110          12.603245  \n", "37   6                         2.921607          12.617143  \n", "38   6                         2.919557          12.627762  \n", "39   6                         2.920332          12.640629  \n", "40   6                         2.920318          12.649429  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["lm = LongestOverlapLM(n_sd=0, n_parallel_sd=0, allow_predicting_less_than_k_tokens=True)\n", "df = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "df[\"n_sd\"] = 0\n", "df[\"n_parallel_sd\"] = 0\n", "dfs = [df]\n", "\n", "n_parallel_sd = 6\n", "\n", "for n_sd in tqdm(range(1, 41)):\n", "    lm = LongestOverlapLM(n_sd=n_sd, n_parallel_sd=n_parallel_sd, allow_predicting_less_than_k_tokens=True)\n", "    df = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval)\n", "    df[\"n_sd\"] = n_sd\n", "    df[\"n_parallel_sd\"] = n_parallel_sd\n", "    dfs.append(df)\n", "\n", "df_sd = pd.concat(dfs)\n", "df_sd.groupby([\"n_sd\", \"n_parallel_sd\"]).mean()"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(10, 4))\n", "\n", "df_tmp = df_parralel_sd.groupby([\"n_sd\", \"n_parallel_sd\"]).mean().reset_index()\n", "axes[0].plot(\n", "    df_tmp[\"n_parallel_sd\"].values,\n", "    df_tmp[\"latency_per_token\"],\n", "    'o-'\n", ")\n", "axes[0].grid()\n", "axes[0].set_xlabel('# of parallel SD branches')\n", "axes[0].set_ylabel('Latency per token, ms')\n", "axes[0].set_ylabel('Latency per token, ms')\n", "axes[0].set_title('Latency vs the number of parallel SD branches\\n(max prediction length = 12)')\n", "\n", "df_tmp = df_sd.groupby([\"n_sd\", \"n_parallel_sd\"]).mean().reset_index()\n", "axes[1].plot(\n", "    df_tmp[\"n_sd\"],\n", "    df_tmp[\"latency_per_token\"],\n", "    'o-',\n", ")\n", "axes[1].grid()\n", "axes[1].set_xlabel('Max SD prediction length')\n", "axes[1].set_ylabel('Latency per token, ms')\n", "axes[1].set_title('Latency vs max prediction length\\n(6 parallel SD branches)')\n", "\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class CombinationLM:\n", "\n", "    def __init__(self, lms, remove_duplicates = True, pass_preexisting_predictions = False):\n", "        self.lms = lms\n", "        self.remove_duplicates = remove_duplicates\n", "        self.pass_preexisting_predictions = pass_preexisting_predictions\n", "\n", "    def fit(self, tokens):\n", "        for lm in self.lms:\n", "            lm.fit(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite):\n", "        if self.pass_preexisting_predictions:\n", "            predictions = self.lms[0].predict_next_k_tokens(suffix, n_sd_overwrite)\n", "            for i in range(1, len(self.lms)):\n", "                predictions = self.lms[i].predict_next_k_tokens(suffix, n_sd_overwrite, preexisting_predictions=predictions)\n", "        else:\n", "            predictions = []\n", "            for lm in self.lms:\n", "                predictions.extend(lm.predict_next_k_tokens(suffix, n_sd_overwrite))\n", "\n", "        if self.remove_duplicates:\n", "            keep_prediction = np.ones((len(predictions)), dtype=np.bool_)        \n", "            for i in range(len(predictions)):\n", "                for j in range(i + 1, len(predictions)):\n", "                    is_prefix = True\n", "                    for l in range(min(len(predictions[i]), len(predictions[j]))):\n", "                        if predictions[i][l] != predictions[j][l]:\n", "                            is_prefix = False\n", "                            break\n", "                    if is_prefix:\n", "                        if len(predictions[i]) > len(predictions[j]):\n", "                            keep_prediction[j] = False\n", "                        else:\n", "                            keep_prediction[i] = False\n", "            predictions = [predictions[i] for i in range(len(predictions)) if keep_prediction[i]]        \n", "        return predictions"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import codecs\n", "\n", "TOKEN_TO_CODE = {}\n", "\n", "with codecs.open('/mnt/efs/augment/user/yury/stacklm/vocab.txt', 'r', 'utf8') as f:\n", "    for index, line in enumerate(f):\n", "        assert len(line[:-1]) == 1\n", "        TOKEN_TO_CODE[index] = line[:-1]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-38.72058868408203\n", "-38.72058868408203\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading the LM will be faster if you build a binary file.\n", "Reading /mnt/efs/augment/user/yury/stacklm/data_full.4gram.prune_0_2_2_2.arpa\n", "----5---10---15---20---25---30---35---40---45---50---55---60---65---70---75---80---85---90---95--100\n", "****************************************************************************************************\n"]}], "source": ["import kenlm\n", "\n", "# model = kenlm.Model('/mnt/efs/augment/user/yury/stacklm/data_full.4gram.prune_0_16_32_64.arpa')\n", "KENLM_MODEL = kenlm.Model('/mnt/efs/augment/user/yury/stacklm/data_full.4gram.prune_0_2_2_2.arpa')\n", "\n", "in_state = kenlm.State()\n", "out_state = kenlm.State()\n", "KENLM_MODEL.NullContextWrite(in_state)\n", "acc = 0\n", "for i in range(5):\n", "    acc += KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[i], out_state)\n", "    in_state, out_state = out_state, in_state\n", "print(acc)\n", "print(KENLM_MODEL.score(' '.join([TOKEN_TO_CODE[i] for i in range(5)]), bos=False, eos=False))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(array([-0.0207479 , -0.00761308]), <kenlm.State at 0x7f59d66ebe70>),\n", " (array([-2.62165046]), <kenlm.State at 0x7f59d66ebef0>),\n", " (array([-0.0207479]), <kenlm.State at 0x7f59d66ebf70>)]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import copy \n", "\n", "def get_suffix_state(suffix):\n", "    in_state = kenlm.State()\n", "    out_state = kenlm.State()\n", "    KENLM_MODEL.NullContextWrite(in_state)\n", "    for token in suffix[-KENLM_MODEL.order:]:\n", "        _ = KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[token], out_state)\n", "        in_state, out_state = out_state, in_state\n", "    return in_state\n", "    \n", "\n", "def score_every_token_in_prediction(in_state, prediction):\n", "    out_state = kenlm.State()\n", "    result = []\n", "    for i in range(len(prediction)):\n", "        result.append(KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[prediction[i]], out_state))\n", "        in_state, out_state = out_state, in_state\n", "    result = np.array(result)\n", "    return result\n", "\n", "\n", "def score_every_token_in_prediction_and_get_state(in_state, prediction):\n", "    out_state = kenlm.State()\n", "    result = []\n", "    for i in range(len(prediction)):\n", "        result.append(KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[prediction[i]], out_state))\n", "        in_state, out_state = out_state, in_state\n", "    result = np.array(result)\n", "    return result, in_state\n", "\n", "\n", "def score_every_token_in_predictions(suffix, predictions):\n", "    in_state = get_suffix_state(suffix)\n", "    result = []\n", "    for i in range(len(predictions)):\n", "        result.append(score_every_token_in_prediction(copy.copy(in_state), predictions[i]))\n", "    return result\n", "\n", "\n", "def score_every_token_in_predictions_and_get_state(suffix, predictions):\n", "    in_state = get_suffix_state(suffix)\n", "    result = []\n", "    for i in range(len(predictions)):\n", "        result.append(score_every_token_in_prediction_and_get_state(copy.copy(in_state), predictions[i]))\n", "    return result\n", "\n", "\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "score_every_token_in_predictions(\n", "    tokenizer.tokenize('import sys\\nimport matplotlib.pyplot'),\n", "    [tokenizer.tokenize(' as plt'), tokenizer.tokenize('\\n'), tokenizer.tokenize(' as')])\n", "\n", "\n", "score_every_token_in_predictions_and_get_state(\n", "    tokenizer.tokenize('import sys\\nimport matplotlib.pyplot'),\n", "    [tokenizer.tokenize(' as plt'), tokenizer.tokenize('\\n'), tokenizer.tokenize(' as')])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" as plt\n", " as pl\n", " as py\n", " import figure\n"]}], "source": ["import copy \n", "\n", "class KenLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "\n", "    def fit(self, tokens=None):        \n", "        pass\n", "    \n", "    def _score_all_tokens(self, state):\n", "        predictions = []\n", "        for token in range(len(TOKEN_TO_CODE)):\n", "            out_state = kenlm.State()\n", "            logprob = KENLM_MODEL.BaseScore(state, TOKEN_TO_CODE[token], out_state)\n", "            predictions.append((token, out_state, logprob))\n", "        predictions.sort(key=lambda x: -x[2])        \n", "        return predictions[:self.n_parallel_sd]\n", "    \n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite):\n", "        n_sd = min(self.n_sd, n_sd_overwrite)\n", "\n", "        in_state = kenlm.State()\n", "        out_state = kenlm.State()\n", "        KENLM_MODEL.NullContextWrite(in_state)\n", "        for token in suffix[-KENLM_MODEL.order:]:\n", "            _ = KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[token], out_state)\n", "            in_state, out_state = out_state, in_state\n", "\n", "        beam = [(tuple(), copy.copy(in_state), 0)]\n", "        for step in range(n_sd):\n", "            new_beam = []\n", "            for old_tokens, old_state, old_logprob in beam:\n", "                for new_token, new_state, new_logprob in self._score_all_tokens(old_state):\n", "                    new_beam.append((old_tokens + (new_token,), new_state, old_logprob + new_logprob))\n", "            new_beam.sort(key=lambda x: -x[2])\n", "            beam = new_beam[:self.n_parallel_sd]\n", "        return [tokens for tokens, _, _ in beam]\n", "                \n", "\n", "lm = KenLM(2, 4)\n", "\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "for prediction in lm.predict_next_k_tokens(tokenizer.tokenize('import sys\\nimport matplotlib.pyplot'), 4):\n", "    print(tokenizer.de<PERSON><PERSON><PERSON>(prediction))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POP () 1 \n", "HYP (619,) 0.9533494134777808  as\n", "APPROVED\n", "HYP (32,) 0.022766910287507016 .\n", "APPROVED\n", "HYP (1188,) 0.015212836824855245  import\n", "APPROVED\n", "HYP (203,) 0.002389733890298178 \n", "\n", "APPROVED\n", "HYP (261,) 0.0007278736823445261   \n", "POP (619,) 0.9533494134777808  as\n", "HYP (619, 5743) 0.9367830409859137  as plt\n", "APPROVED\n", "HYP (619, 1278) 0.004686126856438431  as pl\n", "POP (32,) 0.022766910287507016 .\n", "HYP (32, 3399) 0.002233181599549147 .plot\n", "POP (1188,) 0.015212836824855245  import\n", "HYP (1188, 10986) 0.0032360608529249752  import figure\n", "POP (203,) 0.002389733890298178 \n", "\n", "HYP (203, 465) 0.0012917450875241051 \n", "import\n", "POP (619, 5743) 0.9367830409859137  as plt\n", "--\n", " import\n", "--\n", "\n", "\n", "--\n", " as plt\n", "--\n", ".\n"]}], "source": ["import copy \n", "from collections import deque\n", "\n", "\n", "class KenLMOptimal:\n", "\n", "    def __init__(self, n_sd, max_tokens):\n", "        self.n_sd = n_sd\n", "        self.max_tokens = max_tokens\n", "\n", "    def fit(self, tokens=None):        \n", "        pass\n", "\n", "    def _gen_init_state(self, suffix):\n", "        in_state = kenlm.State()\n", "        out_state = kenlm.State()\n", "        KENLM_MODEL.NullContextWrite(in_state)\n", "        for token in suffix[-KENLM_MODEL.order:]:\n", "            _ = KENLM_MODEL.BaseScore(in_state, TOKEN_TO_CODE[token], out_state)\n", "            in_state, out_state = out_state, in_state\n", "        return in_state\n", "\n", "    def _score_all_tokens(self, state):\n", "        predictions = []\n", "        for token in range(len(TOKEN_TO_CODE)):\n", "            out_state = kenlm.State()\n", "            logprob = KENLM_MODEL.BaseScore(state, TOKEN_TO_CODE[token], out_state)\n", "            predictions.append((token, out_state, logprob))\n", "        predictions.sort(key=lambda x: -x[2])        \n", "        return predictions[:self.max_tokens]\n", "    \n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, verbose=False, preexisting_predictions=None):\n", "        n_sd = min(self.n_sd, n_sd_overwrite)\n", "        init_state = self._gen_init_state(suffix)\n", "\n", "        total_tokens, expected_correct_tokens = 1, 1\n", "        predictions = set([tuple()])\n", "        Q = deque([(tuple(), copy.copy(init_state), 0)])\n", "\n", "        if preexisting_predictions is not None:\n", "            preexisting_prediction_logprobs_and_state = score_every_token_in_predictions_and_get_state(suffix, preexisting_predictions)\n", "            for i in range(len(preexisting_predictions)):\n", "                logprobs, state = preexisting_prediction_logprobs_and_state[i]\n", "                total_tokens += len(preexisting_predictions[i]) + 1\n", "                prediction_prob = 10 ** np.cumsum(logprobs)\n", "                expected_correct_tokens += prediction_prob.sum()\n", "                predictions.add(preexisting_predictions[i])\n", "                Q.appendleft((preexisting_predictions[i], state, prediction_prob[-1]))\n", "    \n", "        while len(Q) > 0 and total_tokens <= self.max_tokens:\n", "            old_prediction, old_state, old_logprob = Q.pop()\n", "            if verbose:\n", "                print('POP', old_prediction, 10**old_logprob, tokenizer.detokenize(old_prediction))\n", "            if len(old_prediction) >= n_sd:\n", "                continue\n", "            first_time_expanded = True\n", "            for new_token, new_state, new_logprob_per_token in self._score_all_tokens(old_state):\n", "                new_logprob = old_logprob + new_logprob_per_token\n", "                new_prediction = old_prediction + (new_token,)\n", "                \n", "                new_total_tokens = total_tokens + 1 if first_time_expanded else total_tokens + len(old_prediction) + 2\n", "                new_expected_correct_tokens = expected_correct_tokens + 10 ** new_logprob\n", "\n", "                if preexisting_predictions is not None and new_prediction in predictions:\n", "                    continue\n", "                \n", "                if verbose:\n", "                    print('HYP', new_prediction, 10 ** new_logprob, tokenizer.detokenize(new_prediction))      \n", "                \n", "                if LATENCY[total_tokens] / expected_correct_tokens > LATENCY[new_total_tokens] / new_expected_correct_tokens:\n", "                    if verbose:\n", "                        print('APPROVED')\n", "                    if first_time_expanded:\n", "                        assert old_prediction in predictions\n", "                        predictions.remove(old_prediction)\n", "                    else:\n", "                        assert old_prediction not in predictions\n", "\n", "                    predictions.add(new_prediction)\n", "                    Q.appendleft(((new_prediction, new_state, new_logprob)))\n", "                    first_time_expanded = False\n", "                    \n", "                    total_tokens = new_total_tokens\n", "                    expected_correct_tokens = new_expected_correct_tokens\n", "                else:\n", "                    break\n", "        return [p for p in predictions if len(p) > 0]\n", "\n", "                \n", "\n", "lm = KenLMOptimal(2, 128)\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "for prediction in lm.predict_next_k_tokens(tokenizer.tokenize('import sys\\nimport matplotlib.pyplot'), 4, True):\n", "    print('--')\n", "    print(tokenizer.de<PERSON><PERSON><PERSON>(prediction))\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a52d3d95eacf47c295843b08da940b29", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd07de015c30473abdcd63d08ad0f3ee", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5251174cecd46ea95c0a2d8d1ee6d4a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "636b867b7b9344718242c72f93ce32f1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for n_sd in [1, 2, 4, 6]:\n", "    print('KenLMOptimal, N_SD=', n_sd)\n", "    lm = CombinationLM([LongestOverlapLM(n_sd=12, n_parallel_sd=6, allow_predicting_less_than_k_tokens=True), KenLMOptimal(n_sd=n_sd, max_tokens=256)], True, pass_preexisting_predictions=True)\n", "    df_tmp = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval)\n", "    df_tmp.to_csv('/mnt/efs/augment/user/yury/stacklm/df2_combinedlm_kenlmoptimal_n_sd%d.csv' % n_sd)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["Unnamed: 0               499.500000\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  18.111000\n", "latency                  518.954578\n", "prediction_latency       947.700515\n", "beam_size                 18.056303\n", "n_unique_first_token      13.237694\n", "n_unique_two_tokens       16.480262\n", "latency_per_token          9.872464\n", "dtype: float64"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_kenlmoptimal_n_sd6 = pd.read_csv('/mnt/efs/augment/user/yury/stacklm/df2_combinedlm_kenlmoptimal_n_sd6.csv')\n", "    def __init__(self, n_sd, n_parallel_sd):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "df_combined_kenlmoptimal_n_sd6.mean()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KenLM, N_SD= 1 N_PARALLEL_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c35845b5c0fb4f38b78e7f267e71e615", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  22.378000\n", "latency                  598.430431\n", "prediction_latency        41.609357\n", "beam_size                  4.498159\n", "n_unique_first_token       3.033182\n", "n_unique_two_tokens        3.595814\n", "latency_per_token         10.866113\n", "dtype: float64\n", "KenLM, N_SD= 1 N_PARALLEL_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a1de2d8c2dc941849ecea5424dda1f75", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  21.705000\n", "latency                  581.984416\n", "prediction_latency        41.237771\n", "beam_size                  5.313049\n", "n_unique_first_token       3.841856\n", "n_unique_two_tokens        4.404475\n", "latency_per_token         10.643130\n", "dtype: float64\n", "KenLM, N_SD= 1 N_PARALLEL_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "41723fc2a6c24781aeaed2fb880be880", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  21.083000\n", "latency                  567.931075\n", "prediction_latency        40.749175\n", "beam_size                  7.091640\n", "n_unique_first_token       5.611378\n", "n_unique_two_tokens        6.177476\n", "latency_per_token         10.401088\n", "dtype: float64\n", "KenLM, N_SD= 1 N_PARALLEL_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3c0dd5db1d874595b31758eeb08185d3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.769000\n", "latency                  562.713456\n", "prediction_latency        41.892138\n", "beam_size                  8.961465\n", "n_unique_first_token       7.475222\n", "n_unique_two_tokens        8.044535\n", "latency_per_token         10.312582\n", "dtype: float64\n", "KenLM, N_SD= 2 N_PARALLEL_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3e059b70402e4ac08e7b879daffc8677", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  21.535000\n", "latency                  577.152666\n", "prediction_latency        78.713044\n", "beam_size                  4.681930\n", "n_unique_first_token       3.034567\n", "n_unique_two_tokens        3.778097\n", "latency_per_token         10.589358\n", "dtype: float64\n", "KenLM, N_SD= 2 N_PARALLEL_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "75aecef747564ceca1bef15c54c88661", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.952000\n", "latency                  564.161253\n", "prediction_latency       115.313744\n", "beam_size                  5.609725\n", "n_unique_first_token       3.460528\n", "n_unique_two_tokens        4.702494\n", "latency_per_token         10.405524\n", "dtype: float64\n", "KenLM, N_SD= 2 N_PARALLEL_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7c9f619d923c4c478264b2102ec5bcf4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.289000\n", "latency                  550.557491\n", "prediction_latency       187.479099\n", "beam_size                  7.487227\n", "n_unique_first_token       4.341789\n", "n_unique_two_tokens        6.576188\n", "latency_per_token         10.210577\n", "dtype: float64\n", "KenLM, N_SD= 2 N_PARALLEL_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "33bcb7a04c0241c1b1dca9ca02915b7f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  19.894000\n", "latency                  546.539444\n", "prediction_latency       260.682904\n", "beam_size                  9.417244\n", "n_unique_first_token       5.231374\n", "n_unique_two_tokens        8.505643\n", "latency_per_token         10.152379\n", "dtype: float64\n", "KenLM, N_SD= 4 N_PARALLEL_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb60d9c5a58041a69280dd530f34a023", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  21.015000\n", "latency                  565.256419\n", "prediction_latency       145.261718\n", "beam_size                  4.742354\n", "n_unique_first_token       3.019702\n", "n_unique_two_tokens        3.765492\n", "latency_per_token         10.441460\n", "dtype: float64\n", "KenLM, N_SD= 4 N_PARALLEL_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "752f34d7e43a4015a25031be4e44a579", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.809000\n", "latency                  563.443200\n", "prediction_latency       249.646100\n", "beam_size                  5.716557\n", "n_unique_first_token       3.254318\n", "n_unique_two_tokens        4.174759\n", "latency_per_token         10.377821\n", "dtype: float64\n", "KenLM, N_SD= 4 N_PARALLEL_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "107c9a221f1a4fbc91ecd65b2ab9507b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.224000\n", "latency                  559.047787\n", "prediction_latency       455.014066\n", "beam_size                  7.654373\n", "n_unique_first_token       3.817144\n", "n_unique_two_tokens        5.134546\n", "latency_per_token         10.307490\n", "dtype: float64\n", "KenLM, N_SD= 4 N_PARALLEL_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f3a14d5be0ec4ba49e387668e5669810", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  19.826000\n", "latency                  553.993997\n", "prediction_latency       661.214659\n", "beam_size                  9.613658\n", "n_unique_first_token       4.380713\n", "n_unique_two_tokens        6.098922\n", "latency_per_token         10.253368\n", "dtype: float64\n", "KenLM, N_SD= 6 N_PARALLEL_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8e34bc3e548c4c5f952916a1ca664c28", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.877000\n", "latency                  563.169994\n", "prediction_latency       205.768869\n", "beam_size                  4.752068\n", "n_unique_first_token       3.012572\n", "n_unique_two_tokens        3.754427\n", "latency_per_token         10.407083\n", "dtype: float64\n", "KenLM, N_SD= 6 N_PARALLEL_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7cd11e0bcf714b5a870f64067f524ff3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.845000\n", "latency                  567.698453\n", "prediction_latency       368.254812\n", "beam_size                  5.739666\n", "n_unique_first_token       3.201016\n", "n_unique_two_tokens        4.033072\n", "latency_per_token         10.423014\n", "dtype: float64\n", "KenLM, N_SD= 6 N_PARALLEL_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2333370d8d0f4f56a39bf9cb116d1114", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.455000\n", "latency                  570.065166\n", "prediction_latency       693.407653\n", "beam_size                  7.691820\n", "n_unique_first_token       3.639613\n", "n_unique_two_tokens        4.747929\n", "latency_per_token         10.420100\n", "dtype: float64\n", "KenLM, N_SD= 6 N_PARALLEL_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c924803f9acb4e12bb14757eddb0b988", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  20.095000\n", "latency                  570.494497\n", "prediction_latency      1004.675024\n", "beam_size                  9.655963\n", "n_unique_first_token       4.100524\n", "n_unique_two_tokens        5.486139\n", "latency_per_token         10.436348\n", "dtype: float64\n"]}], "source": ["for n_sd in [1, 2, 4, 6]:\n", "    for n_parallel_sd in [1, 2, 4, 6]:\n", "        print('Ken<PERSON>, N_SD=', n_sd, 'N_PARALLEL_SD=', n_parallel_sd)\n", "        path = '/mnt/efs/augment/user/yury/stacklm/df2_combinedlm_kenlm_n_sd%d_n_parallel_sd%d.csv' % (n_sd, n_parallel_sd)\n", "        assert not os.path.exists(path)\n", "        lm = CombinationLM([LongestOverlapLM(n_sd=12, n_parallel_sd=6, allow_predicting_less_than_k_tokens=True), KenLM(n_sd=n_sd, n_parallel_sd=n_parallel_sd)], True, pass_preexisting_predictions=False)\n", "        df_tmp = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval)\n", "        df_tmp.to_csv(path)\n", "        print(df_tmp.mean())"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["dfs = []\n", "for n_sd in [1, 2, 4, 6]:\n", "    for n_parallel_sd in [1, 2, 4, 6]:\n", "        path = '/mnt/efs/augment/user/yury/stacklm/df2_combinedlm_kenlm_n_sd%d_n_parallel_sd%d.csv' % (n_sd, n_parallel_sd)\n", "        df_tmp = pd.read_csv(path)\n", "        df_tmp[\"n_sd\"] = n_sd\n", "        df_tmp[\"n_parallel_sd\"] = n_parallel_sd\n", "        dfs.append(df_tmp)\n", "\n", "df_ngram_beam_search = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d20ba5350d54fc2a59375baff6cbb52", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "50c10e3f9698470085eaecdf5a26a32d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bda56349409e4cc4a5391e44a0bb375b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["KenLMOptimal, N_SD= 6\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b8b8cbc36e24cac8d2d61075ee65043", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for n_sd in [1, 2, 4, 6]:\n", "    print('KenLMOptimal, N_SD=', n_sd)\n", "    path = '/mnt/efs/augment/user/yury/stacklm/df_kenlmoptimal_n_sd%d.csv' % n_sd\n", "    assert not os.path.exists(path)\n", "    lm = KenLMOptimal(n_sd=n_sd, max_tokens=512)\n", "    df_tmp = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval)\n", "    df_tmp.to_csv(path)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["dfs = []\n", "for n_sd in [1, 2, 4, 6]:\n", "    path = '/mnt/efs/augment/user/yury/stacklm/df2_combinedlm_kenlmoptimal_n_sd%d.csv' % n_sd\n", "    df_tmp = pd.read_csv(path)\n", "    df_tmp[\"n_sd\"] = n_sd\n", "    dfs.append(df_tmp)\n", "\n", "df_ngram_optimal = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_ngram_beam_search_agg = df_ngram_beam_search.groupby([\"n_sd\", \"n_parallel_sd\"]).mean().reset_index()\n", "df_ngram_optimal_agg = df_ngram_optimal.groupby([\"n_sd\"]).mean().reset_index()\n", "\n", "\n", "fig, ax = plt.subplots(1, 1, figsize=(6, 6))\n", "for n_parallel_sd in [1, 2, 4, 6]:\n", "    df_tmp = df_ngram_beam_search_agg[df_ngram_beam_search_agg[\"n_parallel_sd\"] == n_parallel_sd]\n", "    ax.plot(df_tmp[\"n_sd\"], df_tmp[\"latency_per_token\"], 'o-', label=\"BeamSearch(beam_size=%d)\" % n_parallel_sd)\n", "\n", "ax.plot(df_ngram_optimal_agg[\"n_sd\"], df_ngram_optimal_agg[\"latency_per_token\"], 'o-', label=\"MinExpectedLatency\")\n", "ax.legend()\n", "\n", "ax.grid()\n", "ax.set_xlabel('Max length of each prediction')\n", "ax.set_ylabel('Latency per token, ms')\n", "ax.set_title('LongestOverlapLM + NgramLM(Beam Search) vs LongestOverlapLM + NgramLM(Minimizing Expected Latency)')\n", "\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1000x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "QUANTILES = {\n", "    '50': q50,\n", "    '80': q80,\n", "    '95': q95,\n", "    '99': q99,\n", "}\n", "LENGTH_BINS = [16, 64, 256]\n", "NROWS = 2\n", "assert len(QUANTILES) % NROWS == 0\n", "DFS = {\n", "    \"No SD\": df_baseline,\n", "    \"LongestOverlapLM\": df_longestoverlap,\n", "    \"LongestOverlapLM+NGramLM\": df_combined_kenlmoptimal_n_sd6,\n", "}\n", "\n", "fig, axes = plt.subplots(NROWS, len(QUANTILES) // NROWS, figsize=(10, 10))\n", "for index, (quantile_name, quantile_value) in enumerate(QUANTILES.items()):\n", "    ax = axes[index // NROWS][index % NROWS]\n", "\n", "    for df_name, df_value in DFS.items():  \n", "        df_value[\"latency_per_token\"] = df_value[\"latency\"] / df_value['label_length']\n", "        ax.plot(\n", "            np.arange(len(LENGTH_BINS)),\n", "            df_value.groupby(np.digitize(df_value['label_length'], LENGTH_BINS, right=True)).aggregate({'latency_per_token': [quantile_value]}).values,\n", "            label=df_name,\n", "        )\n", "\n", "        counts = df_value.groupby(np.digitize(df_value['label_length'], LENGTH_BINS, right=True)).aggregate({'latency_per_token': 'count'}).values\n", "        \n", "        xticklabels = ['<= %d\\n(%d samples)' % (LENGTH_BINS[0], counts[0])]\n", "        for i in range(len(LENGTH_BINS) - 1):\n", "            xticklabels.append('%d-%d\\n(%d samples)' % (LENGTH_BINS[i]+1, LENGTH_BINS[i+1], counts[i]))\n", "        ax.set_xticks(np.arange(len(LENGTH_BINS)))\n", "        ax.set_xticklabels(xticklabels)\n", "\n", "    ax.legend(loc='upper right')\n", "    ax.grid()\n", "    ax.set_xlabel('Label length')\n", "    ax.set_ylabel('Latency per token, ms')\n", "    ax.set_title('Latency %s quantile' % quantile_name)\n", "\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "context_length          8222.089000\n", "label_length              70.625000\n", "n_rounds                  70.625000\n", "latency                 1709.125000\n", "prediction_latency         0.013692\n", "beam_size                  0.000000\n", "n_unique_first_token       0.000000\n", "n_unique_two_tokens        0.000000\n", "latency_per_token         24.200000\n", "dtype: float64\n", "context_length          8222.089000\n", "label_length              70.625000\n", "n_rounds                  23.316000\n", "latency                  619.761413\n", "prediction_latency         1.722176\n", "beam_size                  3.896528\n", "n_unique_first_token       2.172808\n", "n_unique_two_tokens        2.763110\n", "latency_per_token          9.592832\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_JAVA_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/javasmall/part-?????-6e266275-46cb-43b2-8060-c01dd2b2ff9b-c000.zstd.parquet\"))\n", "\n", "data_java_fim_bm25_retrieval = load_retrieval_data(MICHIEL_JAVA_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestOverlapLM(0, 1, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_java_fim_bm25_retrieval).mean())\n", "lm = LongestOverlapLM(12, 6, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_java_fim_bm25_retrieval).mean())"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "context_length          8854.618000\n", "label_length              72.420000\n", "n_rounds                  72.420000\n", "latency                 1752.564000\n", "prediction_latency         0.014917\n", "beam_size                  0.000000\n", "n_unique_first_token       0.000000\n", "n_unique_two_tokens        0.000000\n", "latency_per_token         24.200000\n", "dtype: float64\n", "context_length          8854.618000\n", "label_length              72.420000\n", "n_rounds                  27.013000\n", "latency                  724.937494\n", "prediction_latency         1.713865\n", "beam_size                  4.226540\n", "n_unique_first_token       2.562315\n", "n_unique_two_tokens        3.162212\n", "latency_per_token         11.265467\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_DENSE_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/pythonmed_diffboykin/part-?????-8ea8d527-8a12-429e-95a0-047e69912022-c000.zstd.parquet\"))\n", "\n", "data_fim_dense_retrieval = load_retrieval_data(MICHIEL_DENSE_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestOverlapLM(0, 1, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_fim_dense_retrieval).mean())\n", "lm = LongestOverlapLM(12, 6, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_fim_dense_retrieval).mean())"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "context_length          10086.548000\n", "label_length               72.738000\n", "n_rounds                   72.738000\n", "latency                  1760.259600\n", "prediction_latency          0.017003\n", "beam_size                   0.000000\n", "n_unique_first_token        0.000000\n", "n_unique_two_tokens         0.000000\n", "latency_per_token          24.200000\n", "dtype: float64\n", "context_length          10086.548000\n", "label_length               72.738000\n", "n_rounds                   24.596000\n", "latency                   656.962800\n", "prediction_latency          3.265387\n", "beam_size                   4.281729\n", "n_unique_first_token        2.456081\n", "n_unique_two_tokens         3.129873\n", "latency_per_token          10.214062\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_CPP_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/cppsmall/part-?????-77e5f41a-3e30-42ef-96e9-182e8c666899-c000.zstd.parquet\"))\n", "\n", "data_cpp_fim_bm25_retrieval = load_retrieval_data(MICHIEL_CPP_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestOverlapLM(0, 1, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_cpp_fim_bm25_retrieval).mean())\n", "lm = LongestOverlapLM(12, 6, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_cpp_fim_bm25_retrieval).mean())"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "context_length          8198.602000\n", "label_length              66.307000\n", "n_rounds                  66.307000\n", "latency                 1604.629400\n", "prediction_latency         0.014036\n", "beam_size                  0.000000\n", "n_unique_first_token       0.000000\n", "n_unique_two_tokens        0.000000\n", "latency_per_token         24.200000\n", "dtype: float64\n", "context_length          8198.602000\n", "label_length              66.307000\n", "n_rounds                  23.805000\n", "latency                  634.279191\n", "prediction_latency         2.260893\n", "beam_size                  4.063913\n", "n_unique_first_token       2.410410\n", "n_unique_two_tokens        2.999441\n", "latency_per_token         10.741940\n", "dtype: float64\n"]}], "source": ["import glob\n", "MICHIEL_JS_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/javascriptsmall/part-?????-4d066039-757d-4808-992b-d4aab68f811f-c000.zstd.parquet\"))\n", "\n", "data_js_fim_bm25_retrieval = load_retrieval_data(MICHIEL_JS_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)\n", "\n", "lm = LongestOverlapLM(0, 1, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_js_fim_bm25_retrieval).mean())\n", "lm = LongestOverlapLM(12, 6, True)\n", "print(measure_latency_per_data_parallel_sd(lm, data_js_fim_bm25_retrieval).mean())"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2f25d497460245a0941c2512bdd28e55", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n", "Loaded 1000 samples (had to process 1000 samples)\n"]}], "source": ["import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "lm = LongestOverlapLM(12, 6, True)\n", "dfs = []\n", "\n", "for n_retrievals in tqdm([0, 1, 2, 4, 8, 16, 20]):\n", "    data_fim_retrieval_tmp = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, n_retrievals)\n", "    df_tmp = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval_tmp)\n", "    df_tmp[\"n_retrievals\"] = n_retrievals\n", "    dfs.append(df_tmp)\n", "\n", "df_longestoverlap_vs_retrievals = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYYAAAGGCAYAAAB/gCblAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABZ1ElEQVR4nO3deVwT19oH8N8QQpBddqIsKiqCCFYroqKoCGKLa+taxaXeW5dai9pq3yqg1rW1VOXqrW1d69qruLSiFBfEjQIFxR0EF/ZFdoGQzPsHkhpJgGBCAjzfzyfvNWfOzDw5b5qHOWfOGYZlWRaEEELIKxqqDoAQQoh6ocRACCFEAiUGQgghEigxEEIIkUCJgRBCiARKDIQQQiRQYiCEECKBEgMhhBAJlBgIIYRIoMRASBvk6ekJT09PVYdB1BQlBqJwe/bsAcMwiI2NVXUoSnX37l0EBQUhLS1N6naWZbF//34MHjwYRkZG0NHRgbOzM1avXo2ysrLmDVYFgoKCwDAM8vLyZNa5dOkSGIYBwzA4cOCA1DoDBw4EwzDo2bOnskIlb6DEQEgT3b17F8HBwVITg1AoxOTJkzFjxgwANT+SISEhcHV1RXBwMPr374/s7Oxmjlh9aWtr4+DBg3XK09LScO3a<PERSON><PERSON>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", "text/plain": ["<Figure size 400x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "df_longestoverlap_vs_retrievals_agg = df_longestoverlap_vs_retrievals.groupby([\"n_retrievals\"]).mean().reset_index()\n", "\n", "fig, ax = plt.subplots(1, 1, figsize=(4, 4))\n", "\n", "ax.plot(df_longestoverlap_vs_retrievals_agg[\"n_retrievals\"], df_longestoverlap_vs_retrievals_agg[\"latency_per_token\"], 'o-')\n", "\n", "ax.grid()\n", "ax.set_xlabel('Number of retrievals')\n", "ax.set_ylabel('Latency per token, ms')\n", "ax.set_title('LongestOverlapLM')\n", "\n", "plt.tight_layout()\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
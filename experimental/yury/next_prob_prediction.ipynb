{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "Socket error: [Errno 98] Address already in use; Port 6001 is in use on 0.0.0.0. Checking 6002...\n", "torch distributed is already initialized, skipping initialization ...\n", "_initialize_distributed() model parallel is already initialized\n", "> setting random seeds to 1234 ...\n", "[2023-10-03 17:20:36,872] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n", " > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-03 17:20:37,094] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-10-03 17:20:37,274] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-03 17:20:37,274] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-03 17:20:37,310] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-03 17:20:37,311] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-03 17:20:37,311] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-03 17:20:37,311] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-03 17:20:37,312] [INFO] [config.py:763:print]   amp_enabled .................. <PERSON>alse\n", "[2023-10-03 17:20:37,312] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-03 17:20:37,313] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-03 17:20:37,313] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-03 17:20:37,313] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-03 17:20:37,313] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-03 17:20:37,314] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-03 17:20:37,314] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-03 17:20:37,314] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-03 17:20:37,315] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-03 17:20:37,316] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-03 17:20:37,316] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-03 17:20:37,317] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-03 17:20:37,318] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-03 17:20:37,318] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-03 17:20:37,319] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-03 17:20:37,319] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-03 17:20:37,320] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-03 17:20:37,320] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-03 17:20:37,322] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-03 17:20:37,322] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-03 17:20:37,323] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-03 17:20:37,323] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-03 17:20:37,324] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-03 17:20:37,324] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-03 17:20:37,325] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-10-03 17:20:37,325] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-03 17:20:37,326] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-03 17:20:37,326] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-03 17:20:37,327] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-03 17:20:37,328] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-03 17:20:37,329] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-03 17:20:37,330] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-03 17:20:37,331] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-03 17:20:37,332] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-03 17:20:37,333] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-03 17:20:37,333] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-03 17:20:37,334] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-03 17:20:37,334] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-03 17:20:37,335] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-03 17:20:37,336] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-03 17:20:37,337] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-03 17:20:37,339] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n", "Time to load utils op: 0.00043773651123046875 seconds\n", "[2023-10-03 17:20:37,341] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-10-03 17:20:37,375] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-10-03 17:20:37,379] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "No modifications detected for re-loaded extension module utils, skipping build step...\n", "Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-03 17:20:37,540] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-10-03 17:20:37,633] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-10-03 17:20:37,725] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-10-03 17:20:37,782] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-10-03 17:20:37,873] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-10-03 17:20:37,922] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-10-03 17:20:37,965] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-10-03 17:20:38,026] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-10-03 17:20:38,075] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-10-03 17:20:38,166] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-10-03 17:20:38,208] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-10-03 17:20:38,267] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-10-03 17:20:38,318] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-10-03 17:20:38,365] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-10-03 17:20:38,417] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-10-03 17:20:38,470] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-10-03 17:20:38,521] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-10-03 17:20:38,568] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-10-03 17:20:38,621] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-10-03 17:20:38,668] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-10-03 17:20:38,717] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-10-03 17:20:38,791] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-10-03 17:20:38,853] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-10-03 17:20:38,931] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-10-03 17:20:38,984] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-10-03 17:20:38,985] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-10-03 17:20:39,115] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["# Based on this https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e\n", "model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7efbd7f3c430>\n"]}], "source": ["# allow '<|endoftext|>'\n", "ALL_SPECIAL_TOKENS_IDS = [model.tokenizer.tokenize(token)[0] for token in model.tokenizer.all_special_tokens() if token != '<|endoftext|>']\n", "ALL_SPECIAL_TOKENS_IDS_SET = set(ALL_SPECIAL_TOKENS_IDS)\n", "\n", "\n", "EOS_TOKEN_ID = model.tokenizer.tokenize('<|endoftext|>')[0]\n", "EOS_TOKEN_ID\n", "\n", "print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 100 samples (had to process 100 samples)\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "import json\n", "from research.models.meta_model import GenerationOptions\n", "from typing import Any, List, Dict\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for _, datum in df.iterrows():\n", "            n_read_samples += 1\n", "\n", "            context = ModelInput(\n", "                prefix=datum['prefix'],\n", "                suffix=datum['suffix'],\n", "                middle='',\n", "                retrieved_chunks=deserialize_retrieved_chunks(datum['retrieved_chunks']),\n", "                path=datum['file_path'],\n", "                target=None,\n", "            )\n", "            context, _ = model.prompt_formatter.prepare_prompt(context)\n", "\n", "            label = model.prompt_formatter.tokenizer.tokenize(datum['middle'] + \"<|endoftext|>\")    \n", "            label = label[:max_tokens_to_predict]\n", "            for token in label:\n", "                assert token not in ALL_SPECIAL_TOKENS_IDS_SET, (token, model.tokenizer.detokenize([token]))\n", "            label = np.array(label)            \n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 100, 256, None)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.99988973, 0.99915886, 0.9999856 , 0.9999502 , 0.9999987 ,\n", "       0.9989397 , 0.9999974 , 0.9999852 , 0.99998426, 0.9978461 ,\n", "       0.99978644, 0.9997154 ], dtype=float32)"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc\n", "import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "\n", "\n", "def get_label_probs(context, label):\n", "    with torch.no_grad():\n", "        context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([context + label], dtype=torch.int64))                \n", "        model_inputs = (\n", "            context_tokens_tensor,  # input_ids\n", "            position_ids,  # position_ids\n", "            attention_mask,  # attention_mask\n", "        )            \n", "        \n", "        model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "        logits = text_gen_utils.forward_model(\n", "            model=model.neox_model,\n", "            model_inputs=model_inputs,\n", "            is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "            pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "            timers=None,\n", "        )\n", "        model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "        logits[:, :, ALL_SPECIAL_TOKENS_IDS] = -10000                                    \n", "        assert logits.shape[0] == 1, logits.shape\n", "        logits = logits[0]\n", "        probs = F.softmax(logits.float(), dim=-1)\n", "\n", "        label_probs = torch.take_along_dim(probs[-len(label) - 1: -1], torch.unsqueeze(torch.tensor(label, dtype=torch.int64), 1).cuda(), dim=-1)\n", "        label_probs = torch.squeeze(label_probs, -1)\n", "\n", "        gc.collect()\n", "        torch.cuda.empty_cache()\n", "    return label_probs.cpu().numpy()\n", "\n", "\n", "context = data_fim_retrieval[0]['context']\n", "label = data_fim_retrieval[0]['label'].tolist()\n", "get_label_probs(context, label)"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cba78dc3a14a4374be5a6ac889fc02ad", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["probs = []\n", "for sample in tqdm(data_fim_retrieval):\n", "    context = sample['context']\n", "    label = sample['label'].tolist()    \n", "    probs.append(get_label_probs(context, label))"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Probability, %', ylabel='Frequency'>"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def custom_hist(array, bins, xlabel, ylabel, figsize=(20, 5), fontsize=14):\n", "  array = np.array(array)\n", "  bins = np.array(bins + [bins[-1]])\n", "  assert array.min() >= bins.min()\n", "  assert array.max() <= bins.max()\n", "  left_edge = bins[:-1]\n", "  right_edge = bins[1:] - 1\n", "  # Rightmost element is inclusive.\n", "  # See https://numpy.org/doc/stable/reference/generated/numpy.histogram.html\n", "  # for details.\n", "  right_edge[-1] += 1\n", "\n", "  hist, bin_edges = np.histogram(array, bins)\n", "  fig1, ax = plt.subplots(figsize=figsize)\n", "\n", "  # Plot the histogram heights against integers on the x axis\n", "  ax.bar(range(len(hist)), hist, width=1)\n", "\n", "  ax.tick_params(axis='y', labelsize=fontsize )\n", "  for i in range(len(hist)):\n", "    plt.text(i, hist[i] + 1, hist[i], ha=\"center\", fontsize=fontsize - 2)\n", "\n", "  # Set the ticks to the middle of the bars\n", "  ax.set_xticks([i for i, j in enumerate(hist)])\n", "  # Set the xticklabels to a string that tells us what the bin edges were\n", "  xticklabels = []\n", "  for i in range(len(left_edge)):\n", "    if right_edge[i] - left_edge[i] == 0:\n", "      xticklabels.append('%.1f' % left_edge[i])\n", "    else:\n", "      xticklabels.append('%.1f - %.1f' % (left_edge[i], right_edge[i]))\n", "  xticklabels[-1] = 'None'\n", "\n", "  ax.set_xticklabels(xticklabels, fontsize=fontsize) \n", "  ax.set_xlabel(xlabel, fontsize=14)\n", "  ax.set_ylabel(ylabel, fontsize=14)\n", "  ax.autoscale()\n", "  return ax\n", "\n", "\n", "flat_probs = np.array([xx for x in probs for xx in x])\n", "bins=np.arange(0, 1.01, 0.1)\n", "custom_hist((flat_probs * 100), bins=np.arange(0, 110, 10).tolist(), xlabel='Probability, %', ylabel='Frequency')"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.99245465"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["np.median(flat_probs)"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["always predict constant (0.8) 0.24800128\n", "always predict constant (0.9) 0.19176136727300613\n", "always predict constant (0.99) 0.15152829111453678\n", "always predict constant (0.999) 0.15051518370941466\n", "always predict constant (1.0) 0.15088806084758846\n"]}], "source": ["import functools\n", "\n", "def constant_predictor(sample, value):\n", "    return np.full((len(sample) - 1,), fill_value=value)\n", "\n", "def eval_predictor(predictor_fn, samples):\n", "    return np.array([np.abs(predictor_fn(sample) - sample[1:]).mean() for sample in samples]).mean()\n", "\n", "print('always predict constant (0.8)', eval_predictor(functools.partial(constant_predictor, value=flat_probs.mean()), probs))\n", "print('always predict constant (0.9)', eval_predictor(functools.partial(constant_predictor, value=0.9), probs))\n", "print('always predict constant (0.99)', eval_predictor(functools.partial(constant_predictor, value=0.99), probs))\n", "print('always predict constant (0.999)', eval_predictor(functools.partial(constant_predictor, value=0.999), probs))\n", "print('always predict constant (1.0)', eval_predictor(functools.partial(constant_predictor, value=1.0), probs))"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["last prob 0.1379109\n"]}], "source": ["def last_prob_predictor(sample):\n", "    return sample[:-1]\n", "\n", "print('last prob', eval_predictor(last_prob_predictor, probs))"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mean prediction 0.15339975180527773\n"]}], "source": ["def mean_predictor(sample):\n", "    return np.cumsum(sample[:-1]) / np.arange(1, len(sample))\n", "\n", "print('mean prediction', eval_predictor(mean_predictor, probs))"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mean of previous K=1 probabilities 0.1379109\n", "mean of previous K=2 probabilities 0.13556871\n", "mean of previous K=3 probabilities 0.13490883\n", "mean of previous K=4 probabilities 0.13538934\n"]}], "source": ["def mean_last_k_probs_predictor(sample, k):\n", "    result = []\n", "    for index in range(1, len(sample)):\n", "        result.append(np.mean(sample[max(0, index - k): index]))\n", "    return result\n", "\n", "for k in [1, 2, 3, 4]:\n", "    print('mean of previous K=%d probabilities' % k, eval_predictor(functools.partial(mean_last_k_probs_predictor, k=k), probs))\n"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["(12, array([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11]))"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["len(probs[0]), np.arange(1, len(probs[0]))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([0.99988973, 0.99915886, 0.9999856 , 0.9999502 , 0.9999987 ,\n", "        0.9989397 , 0.9999974 , 0.9999852 , 0.99998426, 0.9978461 ,\n", "        0.99978644, 0.9997154 ], dtype=float32),\n", " array([1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], dtype=int32))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc\n", "import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "\n", "\n", "def get_prediction_stats(context, label):\n", "    with torch.no_grad():\n", "        context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([context + label], dtype=torch.int64))                \n", "        model_inputs = (\n", "            context_tokens_tensor,  # input_ids\n", "            position_ids,  # position_ids\n", "            attention_mask,  # attention_mask\n", "        )            \n", "        \n", "        model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "        logits = text_gen_utils.forward_model(\n", "            model=model.neox_model,\n", "            model_inputs=model_inputs,\n", "            is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "            pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "            timers=None,\n", "        )\n", "        model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "        logits[:, :, ALL_SPECIAL_TOKENS_IDS] = -10000                                    \n", "        assert logits.shape[0] == 1, logits.shape\n", "        logits = logits[0]\n", "        probs = F.softmax(logits.float(), dim=-1)\n", "        \n", "        predictions = torch.argmax(probs[-len(label) - 1: -1], axis=-1)\n", "        label = torch.tensor(label, dtype=torch.int64).cuda()\n", "        is_correct = (predictions == label).to(torch.int32)\n", "\n", "        prediction_probs = torch.take_along_dim(probs[-len(label) - 1: -1], torch.unsqueeze(predictions, 1), dim=-1)\n", "        prediction_probs = torch.squeeze(prediction_probs, -1)\n", "\n", "        gc.collect()\n", "        torch.cuda.empty_cache()\n", "    return prediction_probs.cpu().numpy(), is_correct.cpu().numpy()\n", "\n", "\n", "context = data_fim_retrieval[0]['context']\n", "label = data_fim_retrieval[0]['label'].tolist()\n", "get_prediction_stats(context, label)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e3c826c0aa0a4b99ba299a9e38b1bd8a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["probs, is_correct = [], []\n", "for sample in tqdm(data_fim_retrieval):\n", "    context = sample['context']\n", "    label = sample['label'].tolist()    \n", "    current_probs, current_is_correct = get_prediction_stats(context, label)\n", "    probs.extend(current_probs)\n", "    is_correct.extend(current_is_correct)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame({'prob': probs, 'is_correct': is_correct})"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prob</th>\n", "      <th>is_correct</th>\n", "    </tr>\n", "    <tr>\n", "      <th>prob</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>(0.0, 0.1]</th>\n", "      <td>0.074992</td>\n", "      <td>0.051282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.1, 0.2]</th>\n", "      <td>0.153406</td>\n", "      <td>0.163842</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.2, 0.3]</th>\n", "      <td>0.253730</td>\n", "      <td>0.278302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.3, 0.4]</th>\n", "      <td>0.353220</td>\n", "      <td>0.346939</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.4, 0.5]</th>\n", "      <td>0.450310</td>\n", "      <td>0.476190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.5, 0.6]</th>\n", "      <td>0.550694</td>\n", "      <td>0.535211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.6, 0.7]</th>\n", "      <td>0.650874</td>\n", "      <td>0.632353</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.7, 0.8]</th>\n", "      <td>0.747886</td>\n", "      <td>0.761905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.8, 0.9]</th>\n", "      <td>0.851630</td>\n", "      <td>0.833787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>(0.9, 1.0]</th>\n", "      <td>0.989577</td>\n", "      <td>0.990200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                prob  is_correct\n", "prob                            \n", "(0.0, 0.1]  0.074992    0.051282\n", "(0.1, 0.2]  0.153406    0.163842\n", "(0.2, 0.3]  0.253730    0.278302\n", "(0.3, 0.4]  0.353220    0.346939\n", "(0.4, 0.5]  0.450310    0.476190\n", "(0.5, 0.6]  0.550694    0.535211\n", "(0.6, 0.7]  0.650874    0.632353\n", "(0.7, 0.8]  0.747886    0.761905\n", "(0.8, 0.9]  0.851630    0.833787\n", "(0.9, 1.0]  0.989577    0.990200"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(pd.cut(df.prob, bins=np.arange(0, 1 + 0.1, 0.1))).mean()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Prediction probability bucket'>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df.groupby(pd.cut(df.prob, bins=np.arange(0, 1 + 0.1, 0.1))).mean().plot(style='o-', xlabel='Prediction probability bucket')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0. , 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1. ])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
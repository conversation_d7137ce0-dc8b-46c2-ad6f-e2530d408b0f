{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/edit_v2/download_request.jsonl\")\n", "def download_request(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    return row.raw_json\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/edit_v2/download_response.jsonl\")\n", "def download_response(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "    return row.raw_json[\"response\"]\n", "\n", "\n", "REQUEST_ID = \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\"\n", "request = download_request(REQUEST_ID)\n", "response = download_response(REQUEST_ID)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import Exchange\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "\n", "def build_chat_history(request: dict) -> list[Exchange]:\n", "    chat_history = []\n", "    for exchange_json in request.get('chat_history', []):\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=exchange_json['request_message'],\n", "                response_text=exchange_json['response_text'],\n", "                request_id=exchange_json.get('request_id'),\n", "            )\n", "        )\n", "    return chat_history\n", "\n", "def build_retrievals(request) -> list[PromptChunk]:\n", "    prompt_chunks = []\n", "    for chunk in request['retrieved_chunks']:\n", "        if \"blob_name\" not in chunk:\n", "            continue\n", "        char_offset = chunk['char_offset'] if 'char_offset' in chunk else 0\n", "        chunk_index = chunk['chunk_index'] if 'chunk_index' in chunk else 0\n", "        chunk_index = str(chunk_index)\n", "        current_chunk_id = (\n", "            chunk['blob_name'],\n", "            char_offset,\n", "            chunk['char_end'],\n", "        )\n", "        prompt_chunk = PromptChunk(\n", "            text=chunk['text'],\n", "            path=chunk['path'],\n", "            unique_id=chunk['blob_name'] + \"-\" + chunk_index,\n", "            origin=chunk['origin'],\n", "            char_start=char_offset,\n", "            char_end=chunk['char_end'],\n", "            blob_name=chunk['blob_name'],\n", "        )\n", "        prompt_chunks.append(prompt_chunk)\n", "    return prompt_chunks\n", "\n", "\n", "def build_chat_prompt_input(full_request: dict, response: dict, drop_chat_history: bool) -> ChatPromptInput:\n", "    request = full_request[\"request\"]\n", "    selected_code = request.get(\"selected_code\", \"\")\n", "    prefix = request.get(\"prefix\", \"\")\n", "    suffix = request.get(\"suffix\", \"\")\n", "    full_file = prefix + selected_code + suffix\n", "\n", "    if drop_chat_history:\n", "        chat_history = []\n", "    else:\n", "        chat_history = build_chat_history(request)\n", "\n", "    return ChatPromptInput(\n", "        message=request[\"message\"],\n", "        path=request.get(\"path\", \"\"),\n", "        prefix=prefix,\n", "        selected_code=request.get(\"selected_code\", \"\"),\n", "        suffix=suffix,\n", "        chat_history=chat_history,\n", "        prefix_begin=0,\n", "        suffix_end=len(full_file),\n", "        retrieved_chunks=build_retrievals(full_request),\n", "        context_code_exchange_request_id=request.get(\"context_code_exchange_request_id\"),\n", "        recent_changes=request.get(\"recent_changes\"),\n", "        user_guided_blobs=request.get(\"user_guided_blobs\", []),\n", "    )\n", "\n", "\n", "def download_and_build_chat_prompt_input(request_id: str, drop_chat_history: bool) -> ChatPromptInput:\n", "    request = download_request(request_id)\n", "    response = download_response(request_id)\n", "    return build_chat_prompt_input(request, request, drop_chat_history)\n", "\n", "REQUEST_ID = \"246a59ae-f33c-4fd8-bce7-a2026d271568\"\n", "prompt_input = download_and_build_chat_prompt_input(REQUEST_ID, False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "from typing import Optional\n", "\n", "import dataclasses\n", "import os\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"396D3166-6A4C-4519-9138-14D8423E7CE7\")\n", "assert API_TOKEN\n", "\n", "URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "MODEL = \"binks-v12-fp16-longoutput\"\n", "RI_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "\n", "\n", "dogfood_client = AugmentClient(\n", "    url=URL,\n", "    token=API_TOKEN,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, MODEL)\n", "\n", "\n", "PROMPT = \"\"\"\\\n", "Here's the current file:\n", "\n", "```\n", "{prefix}\n", "{selected_code}\n", "{suffix}\n", "```\n", "\n", "And the changes I want you to apply to the current file:\n", "\n", "```\n", "{suggested_edit}\n", "```\n", "\n", "Strictly follow these three steps to incorporate the suggested changes. NO deviations:\n", "\n", "Step 1: Briefly describe the suggested changes.\n", "\n", "Step 2: Plan\n", "Write a plan outlining exactly which suggested changes will be applied and where. Keep the plan strictly focused on the areas related to the suggestions. If the changes include imports, add plan to add these imports at the beginning of the file.\n", "\n", "Step 3: Incorporate Suggested Changes\n", "First, output a line that says \"REVISED_FILE_IS_BELOW\". Then, apply the changes seamlessly and write out the full, modified file with no omissions or placeholders. Strictly follow the plan when applying the suggested changes. Do not make any additional changes, but ensure that all other code outside of the suggested changes is preserved exactly as it is.\n", "\"\"\"\n", "\n", "def extract_between_patterns(main_string: str, p1: str, p2: str, index: int) -> str:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    matches = re.finditer(pattern, main_string, re.DOTALL)\n", "\n", "    for i, match in enumerate(matches):\n", "        if i == index:\n", "            return match.group(1)\n", "\n", "    raise ValueError(f\"Substring between '{p1}' and '{p2}' not found for index {index}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import Exchange\n", "\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "\n", "REQUEST_ID = \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\"\n", "BLOCK_ID = 0\n", "prompt_input = download_and_build_chat_prompt_input(REQUEST_ID, False)\n", "response = download_response(REQUEST_ID)\n", "code_block = extract_between_patterns(\n", "    response[\"text\"], \"```\", \"```\", BLOCK_ID\n", ")\n", "\n", "chat_history = []\n", "chat_history.extend(prompt_input.chat_history)\n", "chat_history.append(Exchange(\n", "    request_message=prompt_input.message,\n", "    response_text=response[\"text\"],\n", "    request_id=REQUEST_ID,\n", "))\n", "\n", "message = PROMPT.format(\n", "    prefix=prompt_input.prefix,\n", "    suffix=prompt_input.suffix,\n", "    selected_code=prompt_input.selected_code,\n", "    path=prompt_input.path,\n", "    suggested_edit=code_block,\n", ")\n", "context_code_exchange_request_id = prompt_input.context_code_exchange_request_id\n", "if context_code_exchange_request_id == \"new\":\n", "    context_code_exchange_request_id = REQUEST_ID\n", "\n", "kwargs = dict(\n", "    selected_code=prompt_input.selected_code,\n", "    message=message,\n", "    prefix=prompt_input.prefix,\n", "    suffix=prompt_input.suffix,\n", "    path=prompt_input.path,\n", "    prefix_begin=prompt_input.prefix_begin,\n", "    suffix_end=prompt_input.suffix_end,\n", "    chat_history=chat_history,\n", "    context_code_exchange_request_id=context_code_exchange_request_id,\n", "    blob_names=[],\n", "    user_guided_blobs=[],\n", ")\n", "\n", "# for k, v in kwargs.items():\n", "#     print(f\"{k}: {v}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model_client_dogfood.chat(**kwargs).text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "first_token_timestamp = None\n", "n_lines = 0\n", "\n", "start_time = time.time()\n", "for response in model_client_dogfood.chat_stream(**kwargs):\n", "    if \"```\" in response.text and first_token_timestamp is None:\n", "        first_token_timestamp = time.time()\n", "    if first_token_timestamp is not None and \"\\n\" in response.text:\n", "        n_lines += 1\n", "\n", "assert first_token_timestamp is not None\n", "time_to_first_token = first_token_timestamp - start_time\n", "generation_time = time.time() - first_token_timestamp\n", "\n", "print(f\"Time to first token: {time_to_first_token}s\")\n", "print(f\"Generation time: {generation_time}s\")\n", "print(f\"Number of lines: {n_lines}\")\n", "print(f\"Generation time per line {generation_time / n_lines}s\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/edit_v2/download_request.jsonl\")\n", "def download_request(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    return row.raw_json\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/edit_v2/download_response.jsonl\")\n", "def download_response(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "    return row.raw_json[\"response\"]\n", "\n", "\n", "REQUEST_ID = \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\"\n", "request = download_request(REQUEST_ID)\n", "response = download_response(REQUEST_ID)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import Exchange\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "\n", "def build_chat_history(request: dict) -> list[Exchange]:\n", "    chat_history = []\n", "    for exchange_json in request.get('chat_history', []):\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=exchange_json['request_message'],\n", "                response_text=exchange_json['response_text'],\n", "                request_id=exchange_json.get('request_id'),\n", "            )\n", "        )\n", "    return chat_history\n", "\n", "def build_retrievals(request) -> list[PromptChunk]:\n", "    prompt_chunks = []\n", "    for chunk in request['retrieved_chunks']:\n", "        if \"blob_name\" not in chunk:\n", "            continue\n", "        char_offset = chunk['char_offset'] if 'char_offset' in chunk else 0\n", "        chunk_index = chunk['chunk_index'] if 'chunk_index' in chunk else 0\n", "        chunk_index = str(chunk_index)\n", "        current_chunk_id = (\n", "            chunk['blob_name'],\n", "            char_offset,\n", "            chunk['char_end'],\n", "        )\n", "        prompt_chunk = PromptChunk(\n", "            text=chunk['text'],\n", "            path=chunk['path'],\n", "            unique_id=chunk['blob_name'] + \"-\" + chunk_index,\n", "            origin=chunk['origin'],\n", "            char_start=char_offset,\n", "            char_end=chunk['char_end'],\n", "            blob_name=chunk['blob_name'],\n", "        )\n", "        prompt_chunks.append(prompt_chunk)\n", "    return prompt_chunks\n", "\n", "\n", "def build_chat_prompt_input(full_request: dict, response: dict, drop_chat_history: bool) -> ChatPromptInput:\n", "    request = full_request[\"request\"]\n", "    selected_code = request.get(\"selected_code\", \"\")\n", "    prefix = request.get(\"prefix\", \"\")\n", "    suffix = request.get(\"suffix\", \"\")\n", "    full_file = prefix + selected_code + suffix\n", "\n", "    if drop_chat_history:\n", "        chat_history = []\n", "    else:\n", "        chat_history = build_chat_history(request)\n", "\n", "    return ChatPromptInput(\n", "        message=request[\"message\"],\n", "        path=request.get(\"path\", \"\"),\n", "        prefix=prefix,\n", "        selected_code=request.get(\"selected_code\", \"\"),\n", "        suffix=suffix,\n", "        chat_history=chat_history,\n", "        prefix_begin=0,\n", "        suffix_end=len(full_file),\n", "        retrieved_chunks=build_retrievals(full_request),\n", "        context_code_exchange_request_id=request.get(\"context_code_exchange_request_id\"),\n", "        recent_changes=request.get(\"recent_changes\"),\n", "        user_guided_blobs=request.get(\"user_guided_blobs\", []),\n", "    )\n", "\n", "\n", "def download_and_build_chat_prompt_input(request_id: str, drop_chat_history: bool) -> ChatPromptInput:\n", "    request = download_request(request_id)\n", "    response = download_response(request_id)\n", "    return build_chat_prompt_input(request, request, drop_chat_history)\n", "\n", "REQUEST_ID = \"246a59ae-f33c-4fd8-bce7-a2026d271568\"\n", "prompt_input = download_and_build_chat_prompt_input(REQUEST_ID, True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "def put_numbers(\n", "    text: str, start_line_offset: int, mode: str, prefix: Optional[str] = None\n", "):\n", "    assert mode in [\"xml\", \"table\", \"regular\"]\n", "\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        if mode == \"xml\":\n", "            assert prefix is None, \"prefix is not used for xml\"\n", "            cur_number = i + 1 + start_line_offset\n", "            yield f\"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\\n\"\n", "        elif mode == \"table\":\n", "            assert prefix is None, \"prefix is not used for table\"\n", "            if i == 0 and start_line_offset == 0:\n", "                yield \"\"\"\n", " Line | Content\n", " ---- | -------\n", "\"\"\"\n", "            cur_number = f\"{i+1+start_line_offset:04d}\"\n", "            yield f\" {cur_number} | {line}\"\n", "        else:\n", "            assert prefix is not None\n", "            yield f\"{prefix}{i+1+start_line_offset:04d}: {line}\"\n", "\n", "\n", "def format_code(\n", "    prefix: str,\n", "    selected_code: str,\n", "    suffix: str,\n", "    num_lines_in_prefix_suffix: int,\n", "    is_highlighted: bool,\n", "):\n", "    def _n_lines(s: str):\n", "        return len(s.splitlines(keepends=True))\n", "\n", "    prefix = \"\".join(prefix.splitlines(keepends=True)[-num_lines_in_prefix_suffix:])\n", "    suffix = \"\".join(suffix.splitlines(keepends=True)[:num_lines_in_prefix_suffix])\n", "\n", "    full_file_in_prompt = prefix + selected_code + suffix\n", "\n", "    # XML\n", "    prefix_n_xml = \"\".join(put_numbers(prefix, 0, mode=\"xml\"))\n", "    selected_code_n_xml = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"xml\")\n", "    )\n", "    suffix_n_xml = \"\".join(\n", "        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode=\"xml\")\n", "    )\n", "    if is_highlighted:\n", "        xml_code = f\"\"\"{prefix_n_xml}<highlighted_code>\\n{selected_code_n_xml}</highlighted_code>\\n{suffix_n_xml}\"\"\"\n", "    else:\n", "        xml_code = f\"\"\"{prefix_n_xml}{selected_code_n_xml}{suffix_n_xml}\"\"\"\n", "\n", "    # Table\n", "    prefix_n_table = \"\".join(put_numbers(prefix, 0, mode=\"table\"))\n", "    selected_code_n_table = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"table\")\n", "    )\n", "    suffix_n_table = \"\".join(\n", "        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode=\"table\")\n", "    )\n", "    if is_highlighted:\n", "        table_code = f\"\"\"{prefix_n_table}<highlighted_code>\\n{selected_code_n_table}</highlighted_code>\\n{suffix_n_table}\"\"\"\n", "    else:\n", "        table_code = f\"\"\"{prefix_n_table}{selected_code_n_table}{suffix_n_table}\"\"\"\n", "\n", "    # Regular\n", "    prefix_n_regular = \"\".join(put_numbers(prefix, 0, mode=\"regular\", prefix=\" \"))\n", "    selected_code_n_regular = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"regular\", prefix=\"*\")\n", "    )\n", "    suffix_n_regular = \"\".join(\n", "        put_numbers(\n", "            suffix,\n", "            _n_lines(prefix) + _n_lines(selected_code),\n", "            mode=\"regular\",\n", "            prefix=\" \",\n", "        )\n", "    )\n", "    regular_code = f\"\"\"{prefix_n_regular}{selected_code_n_regular}{suffix_n_regular}\"\"\"\n", "\n", "    # No numbering\n", "    if is_highlighted:\n", "        no_numbering_code = f\"\"\"{prefix}<highlighted_code>\\n{selected_code}</highlighted_code>\\n{suffix}\"\"\"\n", "    else:\n", "        no_numbering_code = f\"\"\"{prefix}{selected_code}{suffix}\"\"\"\n", "\n", "    return (\n", "        xml_code,\n", "        regular_code,\n", "        table_code,\n", "        {\n", "            \"full_file_in_prompt\": full_file_in_prompt,\n", "            \"first_highlighted_line_number\": _n_lines(prefix) + 1,\n", "            \"last_highlighted_line_number\": _n_lines(prefix) + _n_lines(selected_code),\n", "            \"no_numbering_code\": no_numbering_code,\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-02 17:24:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "# See services/deploy/claude_sonnet_3_5_16k_chat_deploy.jsonnet\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "# response = ANTHROPIC_CLIENT.client.messages.create(\n", "#     model=MODEL_NAME,\n", "#     max_tokens=MAX_OUTPUT_TOKENS,\n", "#     messages=messages,\n", "#     temperature=TEMPERAURE,\n", "#     system=system_prompt,\n", "#     tools=tools,\n", "# )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant.\n", "Your task is to edit a file based on user's instructions.\n", "\"\"\"\n", "\n", "tools = [\n", "    {\n", "        \"name\": \"replace_text\",\n", "        \"description\": \"Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"old_text\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The original text.\",\n", "                },\n", "                \"start_line_number\": {\n", "                    \"type\": \"integer\",\n", "                    \"description\": \"The line number where the original text starts, inclusive.\",\n", "                },\n", "                \"end_line_number\": {\n", "                    \"type\": \"integer\",\n", "                    \"description\": \"The line number where the original text ends, inclusive.\",\n", "                },\n", "                \"replacement_text\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The new text.\",\n", "                },\n", "            },\n", "            \"required\": [\n", "                \"old_text\",\n", "                \"start_line_number\",\n", "                \"end_line_number\",\n", "                \"replacement_text\",\n", "            ],\n", "        },\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "REQUEST_ID = \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\"\n", "prompt_input = download_and_build_chat_prompt_input(REQUEST_ID, True)\n", "\n", "xml_code, _, _, _ = format_code(prompt_input.prefix, prompt_input.selected_code, prompt_input.suffix, 500, True)\n", "restriction_note = \"You should focus on changing ONLY the highlighted region.\"\n", "prompt = f\"\"\"I have opened a file `{prompt_input.path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{prompt_input.path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "Please, edit it according to the following instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "You should make edit in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "{restriction_note}\n", "\n", "Please, do it using single or multiple calls to the `replace_text` tool.\n", "Requirements to tool calls:\n", "- All these calls should be done all ALL AT ONCE, NOT sequentially.\n", "- All calls should use DISJOINT ranges of lines.\n", "\"\"\"\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": prompt,\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total text: 1.0s\n", "- old_text: 4.5s\n", "- start_line_number: 0.1s\n", "- end_line_number: 0.2s\n", "- replacement_text: 3.2s\n", "Total tool_use: 8.0s\n"]}], "source": ["import time\n", "\n", "current_content_block_start = None\n", "current_content_block_type = None\n", "current_key_start = None\n", "tool_use_keys = {}\n", "\n", "def flush():\n", "    global current_content_block_start\n", "    global current_content_block_type\n", "    global tool_use_keys\n", "    if current_content_block_type is None:\n", "        return\n", "    assert current_content_block_start is not None\n", "    current_content_block_time = time.time() - current_content_block_start\n", "    for key in tool_use_keys:\n", "        print(f\"- {key}: {tool_use_keys[key]:.1f}s\")\n", "    print(f\"Total {current_content_block_type}: {current_content_block_time:.1f}s\")\n", "\n", "\n", "with ANTHROPIC_CLIENT.client.messages.stream(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    temperature=TEMPERAURE,\n", "    system=system_prompt,\n", "    messages=messages,\n", "    tools=tools,\n", ") as stream:\n", "    for response in stream:\n", "        if response.type == 'content_block_start':\n", "            flush()\n", "            current_content_block_start = time.time()\n", "            current_content_block_type = response.content_block.type\n", "            current_key_start = time.time()\n", "            tool_use_keys = {}\n", "        elif response.type == \"input_json\":\n", "            # check if there is a key in the snapshot that we haven't yet used\n", "            for key in response.snapshot.keys():\n", "                if key not in tool_use_keys:\n", "                    tool_use_keys[key] = time.time() - current_key_start\n", "                    # print(f\"--- {key}: {response.snapshot[key]}\")\n", "                    current_key_start = time.time()\n", "                    break\n", "    flush()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
determined:
  name: "Binks-v1: DeepSeekInstruct-33B"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/binks
  loss_mask_policy: negative_tokens
  gradient_accumulation_steps: 1
  batch_size: 1
  max_iters: 0
  max_epochs: 1
  warmup_iters: 0
  lr_decay_iters: 4000
  block_size: 16384
  min_lr: 1.0e-6
  learning_rate: 5.0e-6
  weight_decay: 0.1
  decay_lr: False
  eval_interval: 10
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct
  train_data_path: /mnt/efs/augment/user/yury/binks/binks-v1/train
  eval_data_path: /mnt/efs/augment/user/yury/binks/binks-v1/validation
  checkpoint_optimizer_state: True
  run_name: binks_v1
  wandb_project: yury-ft

{{ repo }}

---

Now a software engineer is working on the repository above. This software engineer is generally familiar with the repository. However, they need help with understanding specific technical details of certain parts of the repository. They ask the highly capable AI assistant for help.

Simulate a {{ n_turns }}-turn conversation between this software engineer (the user) and the AI assistant.

# General Instructions

- Begin your response by outlining the feature the user is working on.
- Each conversation turn should include a User question, References, and an AI assistant answer.
- Detailed instructions for each conversation turn are provided below. You MUST follow these instructions closely.
- These instructions are provided in square brackets to clarify but should not appear in the final output.

# Instructions for User's Questions

- User asks questions relevant to the feature to assist them in their work.
- User uses pronouns and general terms to link each question to the previous conversation.
- User phrases each question succinctly and directly.
- User NEVER asks the same thing twice.

# Instruction for References

- List ALL files in the repository that contain relevant information for the user's question in this turn, using FULL file paths.
- Always provide the requested references, including repeating references from previous conversation turns if necessary.

# Instructions for AI Assistant's Answers:

- The AI assistant provides a comprehensive yet focused answer for each question.
- The AI assistant includes either a SHORT relevant code excerpt from the repository or a SHORT code example to demonstrate the API usage in the answers.
- The AI assistant seamlessly includes ALL relevant FULL file paths as a natural part of its answers.

# Output Format:

For every conversation turn, use Markdown headers to structure each response.

Output format and individual instructions for every question and answer are below. You MUST abide them without exception:

# Feature
[Describe a complex feature that the engineer is currently working on, which will serve as the context for the conversation. This feature should require changes to various parts of the repository. Include details like the key parts of the codebase that need changes, specific updates required to enhance or expand the feature, and the various functions and APIs that the engineer will modify or use.]

{{ instructions_str }}

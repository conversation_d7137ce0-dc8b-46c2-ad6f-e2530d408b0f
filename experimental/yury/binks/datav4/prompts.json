{"user:new_topic": ["User asks a question about build system.", "User asks a question where a specific functionality or aspect of business logic is located.", "User asks a question about certain configuration settings within the current project.", "User asks about implementation details of different functionalities within a software project. For example, include how different parts of the code interact with each other, what happens when things don’t go as expected, how the code manages data, and other technical details.", "User asks go summarize or explain a component in the project.", "User asks a specific question about test coverage (unit tests, integration tests, etc) for this current project.", "User shifts gears and asks a different question about the project.", "User asks an unexpected and bizarre question about the project.", "User asks where what is the right strategy to start working on the project.", "User asks a stupid question about the project.", "User asks where and how a specific interface or abstract class is implemented within the project, specifying the name of the interface or class", "User asks how to optimize performance for the project that is currently underperforming.", "User is not very familiar with the programming language and / or a framework, and asks a question.", "User asks an unexpected question about the project.", "User is trying to locate a certain functionality or business logic within a specific directory, which user mentions explicitly.                ", ["User provides a snippet with some configuration settings copied from the project, along with the full file name. Then, the user asks about these settings, such as whether they are optimal for achieving a specific result (X), where exactly these settings are being used within the project, or what the correct value should be to accomplish a certain task (Y).", "User provides a snippet with some configuration settings copied from the project. Then, the user asks about these settings, such as whether they are optimal for achieving a specific result (X), where exactly these settings are being used within the project, or what the correct value should be to accomplish a certain task (Y)."], [["User provides a code snippet copied from the project focused specifically on a function usage or class instantiation, including the full file name of where the snippet was taken from. The user then implies a question by asking, 'Where is this defined or implemented in the project?' without directly naming the function or class. The AI assistant must discern and respond based on the context provided by the snippet.", "User provides a code snippet copied from the project focused specifically on a function usage or class instantiation. The user then implies a question by asking, 'Where is this defined or implemented in the project?' without directly naming the function or class. The AI assistant must discern and respond based on the context provided by the snippet."], ["User provides a code snippet copied from the project, which is part of a component, along with the full file name. The user then asks, 'Can you explain how this part of the system interacts with other components?' The AI assistant must infer the specific component from the snippet provided and detail its interactions.", "User provides a code snippet copied from the project, which is part of a component. The user then asks, 'Can you explain how this part of the system interacts with other components?' The AI assistant must infer the specific component from the snippet provided and detail its interactions."], ["User provides a code snippet copied from the project showing an interface or abstract class, includes the full file name, and asks, 'Where in the project is this implemented?' The AI must deduce the specific interface or class involved.", "User provides a code snippet copied from the project showing an interface or abstract class, and asks, 'Where in the project is this implemented?' The AI must deduce the specific interface or class involved."], ["User includes a code snippet copied from the project, along with the full file name, and asks, 'Is this specific code covered by tests?' The AI assistant is expected to analyze the snippet and confirm whether this exact block of code has corresponding test coverage.", "User includes a code snippet copied from the project, and asks, 'Is this specific code covered by tests?' The AI assistant is expected to analyze the snippet and confirm whether this exact block of code has corresponding test coverage."], ["User writes a new code snippet to handle a specific task or solve a problem and asks if there is a more efficient method to achieve the same outcome. Their code snippet is indeed suboptimal.", "User writes a new code snippet to handle a specific task or solve a problem and asks if there is a more efficient method to achieve the same outcome."], ["User shares a code snippet copied from the project, includes the full file name, and asks an unexpected question about the logic used in this snippet.", "User shares a code snippet copied from the project, and asks an unexpected question about the logic used in this snippet."], "User writes a new code snippet and then asks how it would function in a hypothetical scenario."], ["User includes a code snippet, provides the full file name, and asks how this code would function in a hypothetical scenario.", "User includes a code snippet, and asks how this code would function in a hypothetical scenario."], ["User writes a new code snippet that attempts to use a specific API or library and asks if the implementation in their code is correct. Their code snippet indeed contains some mistakes.", "User writes a new code snippet that attempts to use a specific API or library and asks if the implementation in their code is correct."]], "user:follow_up": [["User formulates a follow-up question using only pronouns and generic nouns to reference the assistant's last answer. This question must be structured such that it relies entirely on the context of that answer, making it impossible to comprehend or respond to without detailed knowledge of the preceding dialogue, while avoiding any specific technical identifiers like class names or function names.", "User asks a follow-up question using only pronouns and generic nouns to refer back to their previous question. The question must be structured in such a way that it is impossible to understand or answer without specific knowledge of the earlier question, avoiding any direct mention of class names or function names."], ["User includes a short code snippet they wrote, asking if they implemented the AI’s previous advice correctly.", "User includes a short code snippet they wrote, asking if they implemented the AI’s previous advice correctly. However, the code contains several mistakes.", "User includes a short code snippet they implemented to ask about integrating it with an existing system.", "User includes a short code snippet they implemented to ask about integrating it with an existing system. However, the code contains some issues that needs to be address before it can be used with the existing system.", "User presents a configuration snippet and asks for general advice on setting optimal parameters.", "User presents a hypothetical modification to their code, asking how these changes might impact the functionality as discussed previously.", "User copies a short code snippet from the repository to clarify how it should be modified for their needs.", "User copies a short code snippet from the repository, along with the full file name, to clarify how it should be modified for their needs."], ["User specifies conditions that might change the answer to their original question.", "User asks for further details using references to AI assistant's answer.", "User updates their question with additional specifications they overlooked.", "User provides more context to a prior question to get a more precise answer.", "User inquires about implications of the last response on their project.", "User requests the AI assistant to re-explain a solution, using pronouns to link back to their original query about the issue.", "User seeks a deeper understanding of a workaround mentioned by the AI, asking for a simplified explanation using general terms."]], "user:corrects_model": ["User asks the assistant to try again answering the question.", "User rephrases the question to help assistant locate the relevant information in the project.", "User provides additional details to help assistant answer the question.", "User tells the assistant to try answering the question again, possibly pointing out where it went wrong.", "User rephrases their question to make it clearer, helping the assistant better understand what information is needed.", "User gives more details or adds more information about their question to help the assistant provide the correct answer.", "User suggests that the assistant look at other parts of the project or consult different resources to check the accuracy of its response."], "assistant:correct": ["AI assistant provides a correct answer."], "assistant:wrong": ["AI assistant misunderstands the question and provide a wrong answer.", "AI assistant overlooks important details in the user's question, leading to an incomplete or incorrect response.", "AI assistant provides a wrong answer.", "AI assistant fails to locate information relevant to the question.", "AI assistant ignores the user's question and provide an answer to some other question."]}
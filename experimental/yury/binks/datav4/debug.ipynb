{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "counter = 0\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/dialogues_about_new_feature.jsonl\") as f:\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/hard_2hop_prompts_flash.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])    \n", "        if data[\"status\"] != \"success\":\n", "            continue        \n", "        counter += 1\n", "        \n", "        print(data[\"repo_uuid\"])\n", "        print()\n", "        # print(data[\"text\"])\n", "        for index, exchange in enumerate(data[\"dialogue\"]):\n", "            print(\"           USER STATE: [\", exchange[\"user_state\"].upper(), \"]\")\n", "            print(\"   USER META-QUESTION: [\", exchange[\"user_instruction\"], \"]\")\n", "            print(\"        USER QUESTION:\", exchange[\"user_question\"])\n", "            print()\n", "            print(\"                PATHS:\", exchange[\"paths\"])\n", "            print()\n", "            print(\"      ASSISTANT STATE: [\", exchange[\"assistant_state\"].upper(), \"]\")\n", "            print(\"ASSISTANT META-ANSWER: [\", exchange[\"assistant_instruction\"], \"]\")\n", "            print(\"     ASSISTANT ANSWER:\", exchange[\"assistant_answer\"])\n", "            print()        \n", "        print(\"\\n---\\n\")\n", "\n", "        if counter >= 40:\n", "            break        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "list_of_objects = [1, 2, 3]\n", "idx = np.random.choice(range(len(list_of_objects)))\n", "idx"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "counter = 0\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/dialogues_about_new_feature.jsonl\") as f:            \n", "    for line in f:\n", "        data = json.loads(line[:-1])    \n", "        # if data[\"status\"] != \"mismatched_n_turns\":\n", "        #     continue        \n", "        counter += 1\n", "        # if counter >= 2:\n", "        #     break\n", "        \n", "        print(data[\"repo_uuid\"])\n", "        print(data[\"status\"])\n", "        # print(len(data[\"parsed_dialogue\"]))\n", "        print(len(parse_gemini_dialogue(data['text'])))\n", "        # print(data[\"text\"])\n", "\n", "        print(\"\\n---\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parse_gemini_dialogue(data['text'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "MAX_OUTPUT_TOKENS = 8096\n", "\n", "\n", "def next_block_index(text, start_index, headers):\n", "    for header in headers:\n", "        index = text.find(header, start_index)\n", "        if index != -1:\n", "            return index\n", "    return None\n", "\n", "\n", "def next_line(text, start_index):\n", "    next_line_index = text.find(\"\\n\", start_index)\n", "    if next_line_index != -1:\n", "        return next_line_index + 1\n", "    return None\n", "\n", "\n", "def next_question_index(text, start_index):\n", "    question_headers = [\"# User question \", \"## User question \"]\n", "    return next_block_index(text, start_index, question_headers)\n", "\n", "\n", "def next_reference_index(text, start_index):\n", "    reference_headers = [\"## References\", \"# References\"]\n", "    return next_block_index(text, start_index, reference_headers)\n", "\n", "\n", "def next_answer_index(text, start_index):\n", "    answer_headers = [\"## AI assistant answer\", \"# AI assistant answer\"]\n", "    return next_block_index(text, start_index, answer_headers)\n", "\n", "\n", "def parse_references(text):\n", "    pattern = r\"`([^`]+)`\"\n", "    return list(re.findall(pattern, text))\n", "\n", "\n", "def parse_gemini_dialogue(text):\n", "    start_index = 0\n", "    samples = []\n", "    while True:\n", "        question_start = next_question_index(text, start_index)\n", "        if question_start is None:\n", "            break\n", "\n", "        question_start = next_line(text, question_start)\n", "        question_end = next_reference_index(text, question_start)\n", "        if question_end is None:\n", "            break\n", "\n", "        question = text[question_start:question_end].strip()\n", "\n", "        references_start = next_line(text, question_end)\n", "        references_end = next_answer_index(text, references_start)\n", "        if references_end is None:\n", "            break\n", "\n", "        reference_text = text[references_start:references_end].strip()\n", "        references = parse_references(reference_text)\n", "\n", "        answer_start = next_line(text, references_end)\n", "        answer_end = next_question_index(text, answer_start) or len(text)\n", "\n", "        answer = text[answer_start:answer_end].strip()\n", "        if len(references) > 0:\n", "            samples.append({\n", "                \"question\": question,\n", "                \"paths\": references,\n", "                \"answer\": answer,\n", "            })\n", "\n", "        if start_index >= answer_end:\n", "            raise ValueError(text)\n", "\n", "        start_index = answer_end\n", "\n", "    return samples\n", "\n", "len(parse_gemini_dialogue(text))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
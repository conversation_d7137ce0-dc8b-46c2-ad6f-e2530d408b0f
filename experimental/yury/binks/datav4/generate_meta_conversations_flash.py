import json
import re
import time
from enum import Enum
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm
from jinja2 import Environment

import experimental.yury.binks.datav3.gemini_api as gemini_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset

BINKS_VERSION = "binks-v4"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
REPOS_PATTERN = OUTPUT_PATH / "01_raw_repos/*.zstd.parquet"

BINKS_V4_REPO_QUESTIONS = [
    Path(OUTPUT_PATH / "dialogues_about_new_feature_flash.jsonl"),
    Path(OUTPUT_PATH / "dialogues_about_new_feature_pro.jsonl"),
    Path(OUTPUT_PATH / "hard_2hop_prompts_pro.jsonl"),
]

PROJECT_ID = "gemini-pro-420822"
LOCATION = "us-central1"
TEMPERATURE = 0.6
MAX_OUTPUT_TOKENS = 8192
GEMINI_MODEL_NAME = gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME
# GEMINI_MODEL_NAME = gemini_api.GeminiModelName.GEMINI_PRO_MODEL_NAME
NUM_PROCESSES = 32

N_REPOS = 20000
N_TURNS = 10
TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav4/dialogue_about_new_feature_template.txt"


class ChatState(Enum):
    """Enum for different chat states."""

    USER_NEW_TOPIC = "user:new_topic"
    USER_FOLLOW_UP = "user:follow_up"
    USER_CORRECTS_MODEL = "user:corrects_model"
    ASSISTANT_CORRECT = "assistant:correct"
    ASSISTANT_WRONG = "assistant:wrong"


class ChatStatesGenerator:
    """Class to generate a sequence of chat states based on a transition matrix."""

    def __init__(self, transition_matrix):
        """Initialize the ChatStatesGenerator with a transition matrix."""
        self.transition_matrix = transition_matrix

    def get_next_state(self, current_state: ChatState) -> ChatState:
        """Get the next state based on the current state and the transition matrix."""
        states, probs = zip(*self.transition_matrix[current_state].items())
        return np.random.choice(states, p=probs)

    def generate_state_sequence(
        self, start_state: ChatState, n_turns: int
    ) -> list[ChatState]:
        """Generate a sequence of chat states of a given length starting from a given state."""
        sequence = [start_state]
        for _ in range(2 * n_turns - 1):
            sequence.append(self.get_next_state(sequence[-1]))
        return sequence


def random_sample(list_of_objects):
    """Randomly sample an element from a list of objects."""
    idx = np.random.choice(range(len(list_of_objects)))
    return list_of_objects[idx]


class ChatTextGenerator:
    """Class to generate text meta-instructions based on chat states."""

    def __init__(self, prompts):
        """Initialize the ChatTextGenerator with user question and assistant answer configs."""
        self.prompts = prompts

    def sample_prompt(self, new_state: ChatState):
        """Sample a transition option from a given configuration, previous state, and new state."""
        option = self.prompts[new_state.value]
        while isinstance(option, list):
            option = random_sample(option)
        return option

    def generate_text(self, states: list[ChatState]) -> list[tuple[str, str]]:
        """Generate a sequence of text based on a sequence of chat states."""
        dialogue_sequence = []
        for i in range(0, len(states), 2):
            user_state = states[i]
            assistant_state = states[i + 1]
            assert user_state.value.startswith("user")
            assert assistant_state.value.startswith("assistant")

            user_question = self.sample_prompt(user_state)
            assistant_answer = self.sample_prompt(assistant_state)
            dialogue_sequence.append((user_question, assistant_answer))
        return dialogue_sequence

    @classmethod
    def from_json_file(cls, prompts_file: str):
        with open(prompts_file, "r", encoding="utf-8") as f:
            prompts = json.load(f)
        return cls(prompts)


# PROMPTS_PATH = "/home/<USER>/augment/experimental/yury/binks/datav4/prompts.json"
# OUTPUT_FILE = Path(OUTPUT_PATH / "dialogues_about_new_feature_flash.jsonl")
# TRANSITION_MATRIX = {
#     ChatState.USER_NEW_TOPIC: {
#         ChatState.ASSISTANT_CORRECT: 0.8,
#         ChatState.ASSISTANT_WRONG: 0.2,
#     },
#     ChatState.USER_FOLLOW_UP: {
#         ChatState.ASSISTANT_CORRECT: 0.8,
#         ChatState.ASSISTANT_WRONG: 0.2,
#     },
#     ChatState.USER_CORRECTS_MODEL: {
#         ChatState.ASSISTANT_CORRECT: 0.8,
#         ChatState.ASSISTANT_WRONG: 0.2,
#     },
#     ChatState.ASSISTANT_CORRECT: {
#         ChatState.USER_NEW_TOPIC: 0.2,
#         ChatState.USER_FOLLOW_UP: 0.8,
#     },
#     ChatState.ASSISTANT_WRONG: {
#         ChatState.USER_NEW_TOPIC: 0.2,
#         ChatState.USER_CORRECTS_MODEL: 0.8,
#     },
# }

PROMPTS_PATH = (
    "/home/<USER>/augment/experimental/yury/binks/datav4/hard_2hop_prompts.json"
)
OUTPUT_FILE = Path(OUTPUT_PATH / "hard_2hop_prompts_flash.jsonl")
TRANSITION_MATRIX = {
    ChatState.USER_NEW_TOPIC: {
        ChatState.ASSISTANT_CORRECT: 0.8,
        ChatState.ASSISTANT_WRONG: 0.2,
    },
    ChatState.USER_FOLLOW_UP: {
        ChatState.ASSISTANT_CORRECT: 0.8,
        ChatState.ASSISTANT_WRONG: 0.2,
    },
    ChatState.USER_CORRECTS_MODEL: {
        ChatState.ASSISTANT_CORRECT: 0.9,
        ChatState.ASSISTANT_WRONG: 0.1,
    },
    ChatState.ASSISTANT_CORRECT: {
        ChatState.USER_NEW_TOPIC: 0.2,
        ChatState.USER_FOLLOW_UP: 0.8,
    },
    ChatState.ASSISTANT_WRONG: {
        ChatState.USER_NEW_TOPIC: 0.05,
        ChatState.USER_CORRECTS_MODEL: 0.95,
    },
}


def iterate_over_repos(repos_pattern, binks_v4_repo_uuids):
    """Join questions with repos."""
    other_binks_v4_repo_uuids = set()
    for path in BINKS_V4_REPO_QUESTIONS:
        with path.open("r") as f:
            for line in f:
                data = json.loads(line[:-1])
                other_binks_v4_repo_uuids.add(data["repo_uuid"])
    print(
        "Loaded %d repo uuids from other Binks-v3 datasets"
        % len(other_binks_v4_repo_uuids)
    )

    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id in other_binks_v4_repo_uuids:
            continue
        if repo.id in binks_v4_repo_uuids:
            continue
        # print(repo.id, repo.name)
        yield repo


def next_block_index(text, start_index, headers):
    for header in headers:
        index = text.find(header, start_index)
        if index != -1:
            return index
    return None


def next_line(text, start_index):
    next_line_index = text.find("\n", start_index)
    if next_line_index != -1:
        return next_line_index + 1
    return None


def next_question_index(text, start_index):
    question_headers = ["# User question ", "## User question "]
    return next_block_index(text, start_index, question_headers)


def next_reference_index(text, start_index):
    reference_headers = ["## References", "# References"]
    return next_block_index(text, start_index, reference_headers)


def next_answer_index(text, start_index):
    answer_headers = ["## AI assistant answer", "# AI assistant answer"]
    return next_block_index(text, start_index, answer_headers)


def parse_references(text):
    pattern = r"`([^`]+)`"
    return list(re.findall(pattern, text))


def parse_gemini_dialogue(text):
    start_index = 0
    samples = []
    while True:
        question_start = next_question_index(text, start_index)
        if question_start is None:
            break

        question_start = next_line(text, question_start)
        question_end = next_reference_index(text, question_start)
        if question_end is None:
            break

        question = text[question_start:question_end].strip()

        references_start = next_line(text, question_end)
        references_end = next_answer_index(text, references_start)
        if references_end is None:
            break

        reference_text = text[references_start:references_end].strip()
        references = parse_references(reference_text)

        answer_start = next_line(text, references_end)
        answer_end = next_question_index(text, answer_start) or len(text)

        answer = text[answer_start:answer_end].strip()
        if len(references) > 0:
            samples.append((question, references, answer))

        if start_index >= answer_end:
            raise ValueError(text)

        start_index = answer_end

    return samples


class GenerateDialogueProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, max_tokens=None):
        self.max_tokens = max_tokens

    def initialize(self):
        GenerateDialogueProcessor.model = gemini_api.GeminiAPI(
            PROJECT_ID,
            LOCATION,
            GEMINI_MODEL_NAME,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        with Path(TEMPLATE_PATH).open() as f:
            template_str = f.read()

        # It's possible to simply instanciate the Template via `Template(template)`.
        # However, jinja2 will strip the trailing newline, which is annoying.
        # The way to prevent that is by explicitly passing the keep_trailing_newline flag
        # into the Environment object.
        env = Environment(keep_trailing_newline=True)
        GenerateDialogueProcessor.template = env.from_string(template_str)

        GenerateDialogueProcessor.states_generator = ChatStatesGenerator(
            TRANSITION_MATRIX
        )
        GenerateDialogueProcessor.text_generator = ChatTextGenerator.from_json_file(
            PROMPTS_PATH
        )
        print("Initialized processor")

    def __call__(self, repo):
        seed = int(time.time_ns() % 2**32 - 1)  # Ensure seed is within 32-bit range
        np.random.seed(seed)

        states = GenerateDialogueProcessor.states_generator.generate_state_sequence(
            ChatState.USER_NEW_TOPIC, N_TURNS
        )
        dialogue_instructions = GenerateDialogueProcessor.text_generator.generate_text(
            states
        )

        instructions = []
        for step, (user_question, assistant_answer) in enumerate(dialogue_instructions):
            instructions.append(f"# User question {step + 1}:\n[{user_question}]\n")
            instructions.append(
                f"# References {step + 1}:\n[List all relevant files.]\n"
            )
            instructions.append(
                f"# AI assistant answer {step + 1}:\n[{assistant_answer}]\n"
            )

        instructions_str = "\n".join(instructions)

        repo_prompt = repo.concat_repo_to_string()
        message = GenerateDialogueProcessor.template.render(
            repo=repo_prompt,
            n_turns=N_TURNS,
            instructions_str=instructions_str,
        )
        response = {
            "repo_uuid": repo.id,
            "states": [state.value for state in states],
            "instructions": dialogue_instructions,
        }
        response.update(GenerateDialogueProcessor.model.generate_response(message))

        if response["text"] is not None:
            try:
                response["parsed_dialogue"] = parse_gemini_dialogue(response["text"])

                if len(response["parsed_dialogue"]) != len(dialogue_instructions):
                    response["status"] = "mismatched_n_turns"
                    return response

                if response["n_output_tokens"] == MAX_OUTPUT_TOKENS:
                    # Drop the last sample
                    response["parsed_dialogue"] = response["parsed_dialogue"][:-1]

                dialogue = []
                for index, (user_question, references, assistant_answer) in enumerate(
                    response["parsed_dialogue"]
                ):
                    dialogue.append(
                        {
                            "user_state": states[2 * index].value,
                            "user_instruction": dialogue_instructions[index][0],
                            "user_question": user_question,
                            "assistant_state": states[2 * index + 1].value,
                            "assistant_answer": assistant_answer,
                            "assistant_instruction": dialogue_instructions[index][1],
                            "paths": references,
                        }
                    )
                response["dialogue"] = dialogue
                response["n_turns"] = len(response["dialogue"])
            except Exception:
                response["status"] = "failed_to_parse_dialogue"

        return response


def pd_median_safe(s):
    return s.median(skipna=True)


def pd_p90_safe(s):
    return np.nanpercentile(s, 90)


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        columns = [
            "status",
            "n_prompt_tokens",
            "n_output_tokens",
            "cost",
            "n_turns",
            "prompt_cost",
            "output_cost",
        ]
        new_stats = {k: response[k] or np.nan for k in columns if k in response}
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "cost": "sum",
            "n_turns": "sum",
            # "prompt_cost": "sum",
            # "output_cost": "sum",
            # "n_prompt_tokens": pd_median_safe,
            # "n_output_tokens": pd_median_safe,
            # "n_est_prompt_tokens": pd_median_safe,
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        # if "n_prompt_tokens" in self.stats.columns:
        #     d["n_prompt_tokens_p90"] = pd_p90_safe(self.stats["n_prompt_tokens"])
        if "n_output_tokens" in self.stats.columns:
            d["n_output_tokens_p90"] = pd_p90_safe(self.stats["n_output_tokens"])
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == "success").sum()
        else:
            return len(self.stats)


processor = GenerateDialogueProcessor()
stats = GenerationStats()

binks_v4_repo_uuids = set()
if OUTPUT_FILE.exists():
    n_completed_dialogues = 0
    for line in OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        repo_uuid = response["repo_uuid"]
        binks_v4_repo_uuids.add(repo_uuid)
        stats.add_response(response)
    print(f"Loaded {n_completed_dialogues} already completed questions")

pbar = tqdm.tqdm(
    total=N_REPOS,
    desc="Gen dialogues",
)
pbar.update(stats.size("success"))
if stats.size() > 0:
    pbar.set_postfix(stats.to_dict())

if stats.size("success") < N_REPOS:
    with OUTPUT_FILE.open("a") as f:
        for response in utils.maybe_run_in_multiple_processes(
            processor,
            iterate_over_repos(REPOS_PATTERN, binks_v4_repo_uuids),
            num_processes=NUM_PROCESSES,
        ):
            stats.add_response(response)
            f.write(json.dumps(response) + "\n")
            f.flush()
            if response["status"] == "success":
                pbar.update()
            pbar.set_postfix(stats.to_dict())
            if stats.size("success") >= N_REPOS:
                break
print("Done")

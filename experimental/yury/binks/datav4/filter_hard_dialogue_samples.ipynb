{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import tqdm\n", "\n", "import dataclasses\n", "import glob\n", "import json\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import tqdm\n", "import re\n", "from jinja2 import Environment\n", "\n", "import experimental.yury.binks.datav3.gemini_api as gemini_api\n", "import experimental.yury.binks.datav3.utils as utils\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "BINKS_VERSION = \"binks-v4\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "REPO_PATTERNS = [\n", "    OUTPUT_PATH / \"01_raw_repos/*.zstd.parquet\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval import retrieval_database\n", "\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "import research.core.prompt_formatters as prompt_formatters\n", "\n", "config = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        #\"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-05\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "retriever = retrieval_database.RetrievalDatabase.from_yaml_config(config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "import copy\n", "import gc\n", "import torch\n", "\n", "from research.core.types import Document\n", "from research.core.model_input import ModelInput\n", "\n", "TOP_K = 25\n", "\n", "# veryhard_f = open(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_flash_veryhard.jsonl\", \"w\")\n", "# hard_f = open(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_flash_hard.jsonl\", \"w\")\n", "\n", "n_easy_sample, n_hard_sample, n_very_hard_sample = 0, 0, 0\n", "n_hard_sample_repos, n_very_hard_sample_repos = 0, 0\n", "\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_flash.jsonl\") as f:\n", "    pbar = tqdm.tqdm(f)\n", "    for line in pbar:\n", "        repo_d = json.loads(line[:-1])\n", "        retriever.add_docs([Document.new(text=file['content'], path=file['max_stars_repo_path']) for file in repo_d['file_list']])\n", "\n", "        veryhard_samples = []\n", "        hard_samples = []\n", "        \n", "        for sample in repo_d['documents_with_questions']:    \n", "            chunks, scores = retriever.query(ModelInput(sample['user_question']), top_k=TOP_K)\n", "\n", "            predicted_paths = set([chunk.path for chunk in chunks])\n", "\n", "            if set(sample['paths']).issubset(predicted_paths):\n", "                n_easy_sample += 1                        \n", "            elif len(set(sample['paths']).intersection(predicted_paths)) > 0:\n", "                n_hard_sample += 1\n", "                hard_samples.append(sample)\n", "            else:\n", "                veryhard_samples.append(sample)\n", "                n_very_hard_sample += 1\n", "        \n", "        if len(hard_samples) > 0:\n", "            n_hard_sample_repos += 1\n", "            hard_repo_d = copy.deepcopy(repo_d)\n", "            hard_repo_d['documents_with_questions'] = hard_samples\n", "            hard_f.write(json.dumps(hard_repo_d) + \"\\n\")\n", "        \n", "        if len(veryhard_samples) > 0:\n", "            n_very_hard_sample_repos += 1\n", "            veryhard_repo_d = copy.deepcopy(repo_d)\n", "            veryhard_repo_d['documents_with_questions'] = veryhard_samples\n", "            veryhard_f.write(json.dumps(veryhard_repo_d) + \"\\n\")\n", "        \n", "        retriever.remove_all_docs()\n", "        gc.collect()\n", "        torch.cuda.empty_cache()        \n", "        pbar.set_description(f\"easy: {n_easy_sample}, hard: {n_hard_sample}, veryhard: {n_very_hard_sample}\")\n", "\n", "hard_f.close()\n", "veryhard_f.close()\n", "\n", "print(n_easy_sample, n_hard_sample, n_very_hard_sample)\n", "print(n_hard_sample_repos, n_very_hard_sample_repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veryhard_and_hard_repo_uuids = set()\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa_veryhard.jsonl\", \"r\") as f:\n", "    for line in f:\n", "        repo_d = json.loads(line[:-1])\n", "        veryhard_and_hard_repo_uuids.add(repo_d['repo_uuid'])\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa_hard.jsonl\", \"r\") as f:\n", "    for line in f:\n", "        repo_d = json.loads(line[:-1])\n", "        veryhard_and_hard_repo_uuids.add(repo_d['repo_uuid'])\n", "\n", "print(len(veryhard_and_hard_repo_uuids))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "import copy\n", "import gc\n", "import torch\n", "\n", "from research.core.types import Document\n", "from research.core.model_input import ModelInput\n", "\n", "TOP_K = 25\n", "\n", "veryhard_f = open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa_veryhard.jsonl\", \"a\")\n", "hard_f = open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa_hard.jsonl\", \"a\")\n", "\n", "n_already_processed_repo_uuid = 0\n", "\n", "n_easy_sample, n_hard_sample, n_very_hard_sample = 0, 0, 0\n", "n_hard_sample_repos, n_very_hard_sample_repos = 0, 0\n", "\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa.jsonl\") as f:\n", "    pbar = tqdm.tqdm(f)\n", "    for line in pbar:\n", "        repo_d = json.loads(line[:-1])\n", "\n", "        if repo_d['repo_uuid'] in veryhard_and_hard_repo_uuids:\n", "            n_already_processed_repo_uuid += 1\n", "            continue\n", "        if n_already_processed_repo_uuid < len(veryhard_and_hard_repo_uuids):\n", "            continue\n", "\n", "        retriever.add_docs([Document.new(text=file['content'], path=file['max_stars_repo_path']) for file in repo_d['file_list']])\n", "\n", "        veryhard_samples = []\n", "        hard_samples = []\n", "        \n", "        for sample in repo_d['documents_with_questions']:    \n", "            chunks, scores = retriever.query(ModelInput(sample['question']), top_k=TOP_K)\n", "\n", "            predicted_paths = set([chunk.path for chunk in chunks])\n", "\n", "            if set(sample['paths']).issubset(predicted_paths):\n", "                n_easy_sample += 1                        \n", "            elif len(set(sample['paths']).intersection(predicted_paths)) > 0:\n", "                n_hard_sample += 1\n", "                hard_samples.append(sample)\n", "            else:\n", "                veryhard_samples.append(sample)\n", "                n_very_hard_sample += 1\n", "        \n", "        if len(hard_samples) > 0:\n", "            n_hard_sample_repos += 1\n", "            hard_repo_d = copy.deepcopy(repo_d)\n", "            hard_repo_d['documents_with_questions'] = hard_samples\n", "            hard_f.write(json.dumps(hard_repo_d) + \"\\n\")\n", "        \n", "        if len(veryhard_samples) > 0:\n", "            n_very_hard_sample_repos += 1\n", "            veryhard_repo_d = copy.deepcopy(repo_d)\n", "            veryhard_repo_d['documents_with_questions'] = veryhard_samples\n", "            veryhard_f.write(json.dumps(veryhard_repo_d) + \"\\n\")\n", "        \n", "        retriever.remove_all_docs()\n", "        gc.collect()\n", "        torch.cuda.empty_cache()        \n", "        pbar.set_description(f\"easy: {n_easy_sample}, hard: {n_hard_sample}, veryhard: {n_very_hard_sample}\")\n", "\n", "hard_f.close()\n", "veryhard_f.close()\n", "\n", "print(n_easy_sample, n_hard_sample, n_very_hard_sample)\n", "print(n_hard_sample_repos, n_very_hard_sample_repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# easy: 40570, hard: 4914, veryhard: 842: : 3938it [5:16:17,  3.23s/it]\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
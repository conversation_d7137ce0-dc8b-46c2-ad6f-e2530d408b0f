{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import dataclasses\n", "import glob\n", "import json\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import tqdm\n", "import re\n", "from jinja2 import Environment\n", "\n", "import experimental.yury.binks.datav3.gemini_api as gemini_api\n", "import experimental.yury.binks.datav3.utils as utils\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BINKS_VERSION = \"binks-v4\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "REPO_PATTERNS = [\n", "    OUTPUT_PATH / \"01_raw_repos/*.zstd.parquet\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QA_SAMPLES = [\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v4/dialogues_about_new_feature_pro.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v4/dialogues_about_new_feature_flash.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v4/hard_2hop_prompts_pro.jsonl\",\n", "]\n", "\n", "n_samples = 0\n", "qa_samples = {}\n", "\n", "for qa_sample_file in QA_SAMPLES:\n", "    with open(qa_sample_file) as f:\n", "        for line in f:\n", "            qa_sample = json.loads(line[:-1])\n", "            if qa_sample[\"status\"] != \"success\":\n", "                continue\n", "            if qa_sample[\"repo_uuid\"] not in qa_samples:\n", "                qa_samples[qa_sample[\"repo_uuid\"]] = []                \n", "            qa_samples[qa_sample[\"repo_uuid\"]].append(qa_sample)\n", "            n_samples += 1\n", "\n", "print(n_samples, len(qa_samples))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "N_MAX_TOKENS = 8000\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def format_query_only_questions(dialogue):\n", "\n", "    def format_turn(turn):\n", "        return \"### Instruction:\\n\" + turn[\"user_question\"]\n", "        \n", "    query = [format_turn(dialogue[-1])]\n", "    n_tokens = len(tokenizer.tokenize(query[0]))\n", "\n", "    if len(dialogue) > 1:        \n", "        for turn in reversed(dialogue[:-1]):\n", "            formatted_turn = format_turn(turn) + \"\\n\"\n", "            if n_tokens + len(tokenizer.tokenize(formatted_turn)) > N_MAX_TOKENS:\n", "                break\n", "            query.append(formatted_turn)\n", "            n_tokens += len(tokenizer.tokenize(formatted_turn))\n", "    \n", "    query.reverse()\n", "    return \"\".join(query)\n", "\n", "\n", "def format_query(dialogue):\n", "\n", "    def format_turn(turn, include_answer):\n", "        if include_answer:\n", "            return \"### Instruction:\\n\" + turn[\"user_question\"] + \"\\n\" + \"### Response:\\n\" + turn[\"assistant_answer\"]\n", "        else:\n", "            return \"### Instruction:\\n\" + turn[\"user_question\"]\n", "\n", "    query = [format_turn(dialogue[-1], False)]\n", "    n_tokens = len(tokenizer.tokenize(query[0]))\n", "\n", "    if len(dialogue) > 1:        \n", "        for turn in reversed(dialogue[:-1]):\n", "            formatted_turn = format_turn(turn, True) + \"\\n\"\n", "            if n_tokens + len(tokenizer.tokenize(formatted_turn)) > N_MAX_TOKENS:\n", "                break\n", "            query.append(formatted_turn)\n", "            n_tokens += len(tokenizer.tokenize(formatted_turn))\n", "    \n", "    query.reverse()\n", "    return \"\".join(query)\n", "\n", "example_dialogue = [\n", "    {\"user_question\": \"A\", \"assistant_answer\": \"a\"},\n", "    {\"user_question\": \"B\", \"assistant_answer\": \"b\"},\n", "    {\"user_question\": \"What is the meaning of life?\", \"assistant_answer\": \"42\"},\n", "]\n", "\n", "# print(format_query_only_questions(example_dialogue))\n", "print(format_query(example_dialogue))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import copy\n", "\n", "\n", "ABSOLUTE_MAX_TOKENS_PER_QUERY = 8000\n", "\n", "n_fitlered_qa_samples, n_wrong_assistant = 0, 0\n", "n_already_empty_repos = 0\n", "n_filtered_repos = 0\n", "n_kept_samples, n_kept_repos = 0, 0\n", "n_qa_repos = 0\n", "n_query_lengths = []\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl\", \"w\") as f:\n", "    for repo_pattern in REPO_PATTERNS:\n", "        for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "            if n_qa_repos >= len(qa_samples):\n", "                # We are already done\n", "                break\n", "            if repo.id not in qa_samples:\n", "                continue\n", "            all_paths = {r['max_stars_repo_path'] for r in repo.repo['file_list']}\n", "            n_qa_repos += 1\n", "            multi_sample = qa_samples[repo.id]\n", "            \n", "            filtered_samples = []\n", "            for sample in multi_sample:\n", "                for index in range(len(sample[\"dialogue\"])):\n", "                    turn = sample[\"dialogue\"][index]\n", "                    if 'wrong' in turn[\"assistant_state\"]:\n", "                        n_wrong_assistant += 1\n", "                        continue\n", "                    filtered_paths = [path for path in turn[\"paths\"] if path in all_paths]\n", "                    if len(filtered_paths) < len(turn[\"paths\"]):\n", "                        n_fitlered_qa_samples += 1\n", "                        continue\n", "\n", "                    # query = format_query_only_questions(sample[\"dialogue\"][:index + 1])\n", "                    query = format_query(sample[\"dialogue\"][:index + 1])\n", "                    if len(tokenizer.tokenize(query)) > ABSOLUTE_MAX_TOKENS_PER_QUERY:\n", "                        n_fitlered_qa_samples += 1\n", "                        continue\n", "\n", "                    n_query_lengths.append(len(tokenizer.tokenize(query)))\n", "                    n_kept_samples += 1                    \n", "                    filtered_samples.append({\n", "                        \"question\": query,\n", "                        \"answer\": turn[\"assistant_answer\"],\n", "                        \"paths\": filtered_paths,\n", "                    })\n", "            \n", "            if len(filtered_samples) == 0:\n", "                n_filtered_repos += 1\n", "                continue\n", "\n", "            repo_d = repo.to_jsonable_dict()\n", "            repo_d['documents_with_questions'] = filtered_samples\n", "\n", "            n_kept_repos += 1\n", "                        \n", "            sample_json = json.dumps(repo_d)\n", "            f.write(sample_json + \"\\n\")\n", "        \n", "print(n_fitlered_qa_samples, n_wrong_assistant, n_kept_samples)\n", "print(n_filtered_repos, n_already_empty_repos, n_kept_repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas \n", "\n", "pd = pandas.DataFrame(n_query_lengths)\n", "pd.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ready_repos = []\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl\") as f:\n", "    for line in f:\n", "        ready_repos.append(json.loads(line[:-1]))\n", "print(len(ready_repos))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "random_repo_index = np.random.randint(len(ready_repos))\n", "random_index = np.random.randint(len(ready_repos[random_repo_index][\"documents_with_questions\"]))\n", "sample = ready_repos[random_repo_index][\"documents_with_questions\"][random_index]\n", "\n", "print(\"QUESTION:\", sample[\"question\"])\n", "print(\"   PATHS:\", sample[\"paths\"])\n", "print(\"  ANSWER:\", sample[\"answer\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
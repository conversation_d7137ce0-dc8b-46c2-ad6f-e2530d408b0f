{"user:new_topic": ["User asks a question about build system.", "User asks a question where a specific functionality or aspect of business logic is located.", "User asks a question about certain configuration settings within the current project.", "User asks about implementation details of different functionalities within a software repository. For example, include how different parts of the code interact with each other, what happens when things don’t go as expected, how the code manages data, and other technical details.", "User asks go summarize or explain a component in the repository.", "User asks go summarize or explain a directory in the repository.", "User asks a specific question about test coverage (unit tests, integration tests, etc) for this current project.", "User shifts gears and asks a different question about the repository.", "User asks where and how a specific interface or abstract class is implemented within the repository, specifying the name of the interface or class", "User asks how to optimize performance for the repository that is currently underperforming.", "User is trying to locate a certain functionality or business logic within a specific directory, and user mentions this directory explicitly."], "user:follow_up": [["User asks a follow-up question using ONLY pronouns and non-specific nouns to reference the AI assistant's last answer. This question must rely ENTIRELY on the context of that response, and it is CRUCIAL to AVOID MENTIONING function, class, or file names.", "User asks a follow-up question using ONLY pronouns and non-specific nouns to refer back to their own last question. This question must be ENTIRELY dependent on the context of that query, and it is CRUCIAL to AVOID MENTIONING function, class, or file names."]], "user:corrects_model": ["User asks the assistant to check a top-level directory for information, providing an example like path/to/relevant/dir, but STRICTLY AVOIDING mentioning specific technical terms or individual files."], "assistant:correct": ["AI assistant provides a correct answer."], "assistant:wrong": ["The AI assistant fails to locate the relevant information and admits to the user that it cannot provide a helpful response."]}
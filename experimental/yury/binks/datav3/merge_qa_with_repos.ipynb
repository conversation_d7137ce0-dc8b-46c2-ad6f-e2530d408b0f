{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import dataclasses\n", "import glob\n", "import json\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import tqdm\n", "import re\n", "from jinja2 import Environment\n", "\n", "import experimental.yury.binks.datav3.gemini_api as gemini_api\n", "import experimental.yury.binks.datav3.utils as utils\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["BINKS_VERSION = \"binks-v3\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "REPO_PATTERNS = [\n", "    OUTPUT_PATH / \"01_raw_repos/*.zstd.parquet\",\n", "    OUTPUT_PATH / \"02_raw_repos/yury/*.zstd.parquet\",\n", "    OUTPUT_PATH / \"03_raw_repos/*.zstd.parquet\",\n", "    OUTPUT_PATH / \"02_raw_repos/colin/*.zstd.parquet\",\n", "]\n", "\n", "# REPO_PATTERNS = [\n", "#     OUTPUT_PATH / \"03_raw_repos/*.zstd.parquet\",\n", "# ]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14194\n"]}], "source": ["# QA_SAMPLES = \"/mnt/efs/augment/user/yury/binks/binks-v3.1/all_styled_qa_samples.jsonl\"\n", "QA_SAMPLES = \"/mnt/efs/augment/user/yury/binks/binks-v3/all_styled_qa_samples.jsonl\"\n", "\n", "qa_samples = {}\n", "\n", "with open(QA_SAMPLES) as f:\n", "    for line in f:\n", "        qa_sample = json.loads(line[:-1])\n", "        if qa_sample[\"status\"] != \"success\":\n", "            continue\n", "        assert qa_sample[\"repo_uuid\"] not in qa_samples\n", "        qa_samples[qa_sample[\"repo_uuid\"]] = qa_sample\n", "\n", "print(len(qa_samples))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# import random\n", "\n", "# random_samples = random.sample(list(qa_samples.values()), 1)\n", "\n", "# for sample in random_samples:\n", "#     # for new_question, parsed_answer in zip(sample[\"new_questions\"], sample[\"parsed_answers\"]):\n", "#     new_question, parsed_answer = sample[\"new_questions\"][-1], sample[\"parsed_answers\"][-1]\n", "\n", "#     print('ORIGINAL QUESTION:', parsed_answer[\"question\"])\n", "#     print('     NEW QUESTION:', new_question)\n", "#     print('       REFERENCES:', parsed_answer[\"paths\"])\n", "#     print('           ANSWER:', parsed_answer[\"answer\"])\n", "#     # break\n", "#     print('\\n\\n---\\n\\n')\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['repo_uuid', 'n_est_prompt_tokens', 'text', 'status', 'n_prompt_tokens', 'n_output_tokens', 'cost', 'prompt_cost', 'output_cost', 'parsed_answers', 'n_answers', 'new_questions'])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["sample.keys()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def filter_paths_and_adjust_answer(paths, answer, all_paths_in_repo, repo_name):\n", "    filtered_paths = []    \n", "    for path in paths:\n", "        if path in all_paths_in_repo:\n", "            filtered_paths.append(path)\n", "    return filtered_paths, answer\n", "\n", "# def filter_paths_and_adjust_answer(paths, answer, all_paths_in_repo, repo_name):\n", "#     filtered_paths = []    \n", "#     for path in paths:\n", "#         if path in all_paths_in_repo:\n", "#             filtered_paths.append(path)\n", "#         elif path.startswith(\"./\"):\n", "#             fixed_path = path[2:]\n", "#             if fixed_path in all_paths_in_repo:\n", "#                 answer = answer.replace(path, fixed_path)\n", "#                 filtered_paths.append(fixed_path)\n", "#         elif path.startswith(repo_name + '/'):\n", "#             path_without_repo_name = path[len(repo_name) + 1:]\n", "#             if path_without_repo_name in all_paths_in_repo:\n", "#                 answer = answer.replace(path, path_without_repo_name)\n", "#                 filtered_paths.append(path_without_repo_name)\n", "#     return filtered_paths, answer"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2000it [00:12, 159.95it/s]\n", "10000it [00:55, 178.95it/s]\n", "20000it [01:18, 254.47it/s]\n", "10000it [00:36, 273.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["63007 135599\n", "1289 10 12895\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import tqdm\n", "\n", "\n", "n_fitlered_qa_samples, n_filtered_repos, n_already_empty_repos = 0, 0, 0\n", "n_kept_samples, n_kept_repos = 0, 0\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa_extrareffilter.jsonl\", \"w\") as f:\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa.jsonl\", \"w\") as f:\n", "    for repo_pattern in REPO_PATTERNS:\n", "        for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "            if repo.id not in qa_samples:\n", "                continue\n", "            sample = qa_samples[repo.id]\n", "\n", "            assert len(sample[\"new_questions\"]) == len(sample[\"parsed_answers\"])\n", "            if len(sample[\"new_questions\"]) == 0:\n", "                n_already_empty_repos += 1\n", "                continue\n", "\n", "            all_paths = {r['max_stars_repo_path'] for r in repo.repo['file_list']}\n", "\n", "            filtered_samples = []\n", "            for new_question, parsed_answer in zip(sample[\"new_questions\"], sample[\"parsed_answers\"]):\n", "                filtered_paths, adjusted_answer = filter_paths_and_adjust_answer(parsed_answer[\"paths\"], parsed_answer[\"answer\"], all_paths, repo.name)\n", "                # if len(filtered_paths) == 0:\n", "                if len(filtered_paths) < len(parsed_answer[\"paths\"]):\n", "                    n_fitlered_qa_samples += 1\n", "                    continue\n", "                n_kept_samples += 1\n", "                filtered_samples.append({\n", "                    \"question\": new_question,\n", "                    \"answer\": adjusted_answer,\n", "                    \"paths\": filtered_paths,\n", "                })\n", "            \n", "            if len(filtered_samples) == 0:\n", "                n_filtered_repos += 1\n", "                continue\n", "\n", "            repo_d = repo.to_jsonable_dict()\n", "            repo_d['documents_with_questions'] = filtered_samples\n", "\n", "            n_kept_repos += 1\n", "\n", "            sample_json = json.dumps(repo_d)\n", "            f.write(sample_json + \"\\n\")\n", "\n", "print(n_fitlered_qa_samples, n_kept_samples)\n", "print(n_filtered_repos, n_already_empty_repos, n_kept_repos)"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["13252it [00:30, 433.26it/s]\n"]}], "source": ["import experimental.yury.binks.datav3.constants as constants\n", "import pandas as pd\n", "import tqdm\n", "\n", "stats = []\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa.jsonl\") as f:\n", "    for line in tqdm.tqdm(f):\n", "        repo_d = json.loads(line[:-1])\n", "        relevant_files = [f for f in repo_d['file_list'] if f['langpart'] in constants.RETRIEVAL_LANGUAGES]\n", "        stats.append({\n", "            \"retrieval_file_count\": len(relevant_files),\n", "            \"all_file_count\": len(repo_d['file_list']),\n", "        })\n", "\n", "df = pd.DataFrame(stats)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>retrieval_file_count</th>\n", "      <th>all_file_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>13252.000000</td>\n", "      <td>13252.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>96.362209</td>\n", "      <td>96.461138</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>114.638789</td>\n", "      <td>114.744493</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>32.000000</td>\n", "      <td>32.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>63.000000</td>\n", "      <td>63.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>119.000000</td>\n", "      <td>119.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1964.000000</td>\n", "      <td>1964.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       retrieval_file_count  all_file_count\n", "count          13252.000000    13252.000000\n", "mean              96.362209       96.461138\n", "std              114.638789      114.744493\n", "min                1.000000        1.000000\n", "25%               32.000000       32.000000\n", "50%               63.000000       63.000000\n", "75%              119.000000      119.000000\n", "max             1964.000000     1964.000000"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["Repo(repo=max_stars_repo_name                              numberartificial/cibops\n", "max_file_lang                    {'file_count': 1, 'langpart': 'python'}\n", "max_size_lang           {'total_size': 554760, 'langpart': 'javascript'}\n", "total_size                                                        557857\n", "file_list              [{'hexsha': '7ed1b59c6b5a8fef4ad7be57c8b43f6dc...\n", "repo_uuid                           52bf4744-9c79-401e-a9d4-cd703ee02d77\n", "Name: 155, dtype: object)"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["repo"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_output_tokens</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>14194.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>4844.547908</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1704.912677</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>182.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3604.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>4837.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>6133.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8191.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       n_output_tokens\n", "count     14194.000000\n", "mean       4844.547908\n", "std        1704.912677\n", "min         182.000000\n", "25%        3604.000000\n", "50%        4837.000000\n", "75%        6133.000000\n", "max        8191.000000"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["qa_stats = []\n", "\n", "for qa_sample in qa_samples.values():\n", "    if qa_sample[\"status\"] != \"success\":\n", "        continue\n", "    qa_stats.append({\n", "        \"n_output_tokens\": qa_sample[\"n_output_tokens\"]\n", "    })\n", "\n", "qa_stats = pd.DataFrame(qa_stats)\n", "qa_stats.describe()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['repo_uuid', 'n_est_prompt_tokens', 'text', 'status', 'n_prompt_tokens', 'n_output_tokens', 'cost', 'prompt_cost', 'output_cost', 'parsed_answers', 'n_answers', 'new_questions'])"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["qa_sample.keys()"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2000it [00:09, 203.41it/s]\n"]}], "source": ["import experimental.yury.binks.datav3.constants as constants\n", "import pandas as pd\n", "import tqdm\n", "\n", "stats = []\n", "\n", "# for repo in tqdm.tqdm(ParquetRepoDataset(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/03_raw_repos/*.zstd.parquet\")):\n", "for repo in tqdm.tqdm(ParquetRepoDataset(\"/mnt/efs/augment/user/yury/binks/binks-v3.1/03_raw_repos/part-00005-840a28ed-d158-4a78-a320-39c6097ac435-c000.zstd.parquet\")):\n", "    relevant_files = [f for f in repo.repo['file_list'] if f['langpart'] in constants.RETRIEVAL_LANGUAGES]\n", "    stats.append({\n", "        \"retrieval_file_count\": len(relevant_files),\n", "        \"all_file_count\": len(repo.repo['file_list']),\n", "    })\n", "\n", "df = pd.DataFrame(stats)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>retrieval_file_count</th>\n", "      <th>all_file_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2000.000000</td>\n", "      <td>2000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>250.060000</td>\n", "      <td>250.182000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>742.585094</td>\n", "      <td>742.678252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>58.000000</td>\n", "      <td>64.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>90.000000</td>\n", "      <td>90.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>131.500000</td>\n", "      <td>131.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>227.250000</td>\n", "      <td>227.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>26966.000000</td>\n", "      <td>26966.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       retrieval_file_count  all_file_count\n", "count           2000.000000     2000.000000\n", "mean             250.060000      250.182000\n", "std              742.585094      742.678252\n", "min               58.000000       64.000000\n", "25%               90.000000       90.000000\n", "50%              131.500000      131.500000\n", "75%              227.250000      227.250000\n", "max            26966.000000    26966.000000"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2000it [00:07, 279.65it/s]\n", "10000it [00:32, 304.66it/s]\n", "20000it [01:04, 308.30it/s]\n", "10000it [00:33, 295.90it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["28706 69044\n", "7204 512 3 6475\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import tqdm\n", "\n", "\n", "n_fitlered_qa_samples, n_filtered_repos, n_already_empty_repos = 0, 0, 0\n", "n_repo_too_small = 0\n", "n_kept_samples, n_kept_repos = 0, 0\n", "\n", "for repo_pattern in REPO_PATTERNS:\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "        if repo.id not in qa_samples:\n", "            continue\n", "        if len(repo.repo['file_list']) < 64:\n", "            n_repo_too_small += 1\n", "            continue\n", "        sample = qa_samples[repo.id]\n", "\n", "        assert len(sample[\"new_questions\"]) == len(sample[\"parsed_answers\"])\n", "        if len(sample[\"new_questions\"]) == 0:\n", "            n_already_empty_repos += 1\n", "            continue\n", "\n", "        all_paths = {r['max_stars_repo_path'] for r in repo.repo['file_list']}\n", "\n", "        filtered_samples = []\n", "        for new_question, parsed_answer in zip(sample[\"new_questions\"], sample[\"parsed_answers\"]):\n", "            filtered_paths, adjusted_answer = filter_paths_and_adjust_answer(parsed_answer[\"paths\"], parsed_answer[\"answer\"], all_paths, repo.name)\n", "            if len(filtered_paths) < len(parsed_answer[\"paths\"]):\n", "                # If ANY PATH was filtered\n", "                n_fitlered_qa_samples += 1\n", "                continue\n", "            n_kept_samples += 1\n", "            filtered_samples.append({\n", "                \"question\": new_question,\n", "                \"answer\": adjusted_answer,\n", "                \"paths\": filtered_paths,\n", "            })\n", "        \n", "        if len(filtered_samples) == 0:\n", "            n_filtered_repos += 1\n", "            continue\n", "\n", "        n_kept_repos += 1\n", "\n", "\n", "print(n_fitlered_qa_samples, n_kept_samples)\n", "print(n_repo_too_small, n_filtered_repos, n_already_empty_repos, n_kept_repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
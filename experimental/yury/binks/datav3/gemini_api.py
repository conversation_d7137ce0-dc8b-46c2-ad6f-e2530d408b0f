import re
from enum import Enum

import sentencepiece as spm
import vertexai
from vertexai.generative_models import (
    Harm<PERSON>ategory,
    HarmBlockThreshold,
    GenerationConfig,
    GenerativeModel,
)

GEMMA_TOKENIZER_PATH = "/mnt/efs/augment/user/yury/gemma/tokenizer.model"
MAX_GEMINI_FLASH_TOKENS = 990000


SAFETY_SETTINGS = {
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_ONLY_HIGH,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
    HarmCategory.HARM_CATEGORY_UNSPECIFIED: HarmBlockThreshold.BLOCK_ONLY_HIGH,
}


class GeminiModelName(Enum):
    """
    Enum for Gemini model names.
    """

    GEMINI_PRO_MODEL_NAME = "gemini-1.5-pro-preview-0514"
    GEMINI_FLASH_MODEL_NAME = "gemini-1.5-flash-preview-0514"


class GeminiResponseStatus(Enum):
    """
    Enum for Gemini API response status.
    """

    SUCCESS = "success"
    PROMPT_TOO_LONG = "prompt_too_long"
    SAFETY_FAILED = "safety_failed"
    SERVER_500_ERROR = "server_500_error"
    SERVER_400_ERROR = "server_400_error"


def extract_too_many_tokens_error(error_message):
    if not isinstance(error_message, str):
        error_message = str(error_message)
    pattern = r"Unable to submit request because the input token count is (\d+) but model only supports up to \d+"
    matched_number = re.search(pattern, error_message)
    extracted_number = int(matched_number.group(1)) if matched_number else None
    return extracted_number


ERROR_MESSAGES = {
    "500": GeminiResponseStatus.SERVER_500_ERROR.value,
    "502": GeminiResponseStatus.SERVER_500_ERROR.value,
    "503": GeminiResponseStatus.SERVER_500_ERROR.value,
    "400 Request contains an invalid argument.": GeminiResponseStatus.SERVER_400_ERROR.value,
    "400 The output data is not valid json.": GeminiResponseStatus.SERVER_400_ERROR.value,
}


class GeminiAPI:
    """
    A class to interact with the Gemini API for generating responses.
    """

    def __init__(
        self, project_id, location, model_name, temperature, max_output_tokens
    ):
        """
        Initialize the GeminiAPI with project details and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            location (str): The location of the Google Cloud project.
            model_name (GeminiModelName): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        self.project_id = project_id
        self.location = location
        self.model_name = model_name.value
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.initialize()

    def initialize(self):
        """
        Initialize the Google Cloud API and set up the model for generating responses.
        """
        vertexai.init(project=self.project_id, location=self.location)
        self.generation_config = GenerationConfig(
            candidate_count=1,
            temperature=self.temperature,
            max_output_tokens=self.max_output_tokens,
        )
        self.model = GenerativeModel(
            model_name=self.model_name,
            generation_config=self.generation_config,
            safety_settings=SAFETY_SETTINGS,
        )
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.Load(GEMMA_TOKENIZER_PATH)

    def _compute_prompt_token_cost(self, n_prompt_tokens):
        if n_prompt_tokens < 128000:
            return n_prompt_tokens * 0.35 / 1000_000.0
        else:
            return n_prompt_tokens * 0.7 / 1000_000.0

    def _compute_output_token_cost(self, n_prompt_tokens, n_output_tokens):
        if n_prompt_tokens < 128000:
            return n_output_tokens * 0.53 / 1000_000.0
        else:
            return n_output_tokens * 1.05 / 1000_000.0

    def generate_response(self, message):
        """
        Generate a response based on the given message.

        Args:
            message (str): The message to generate a response for.

        Returns:
            str: The generated response text.
        """
        response = {}
        response["n_est_prompt_tokens"] = len(self.tokenizer.tokenize(message))
        if response["n_est_prompt_tokens"] > MAX_GEMINI_FLASH_TOKENS:
            response.update(
                {
                    "text": None,
                    "status": GeminiResponseStatus.PROMPT_TOO_LONG.value,
                }
            )
            return response

        chat = self.model.start_chat()
        try:
            responses = chat.send_message(
                message,
                stream=False,
                generation_config=self.generation_config,
            )
        except Exception as e:
            if isinstance(
                e, vertexai.generative_models._generative_models.ResponseValidationError
            ):
                # We don't know the real cost, this is just an estimate.
                prompt_cost = self._compute_prompt_token_cost(
                    response["n_est_prompt_tokens"]
                )
                response.update(
                    {
                        "text": None,
                        "status": GeminiResponseStatus.SAFETY_FAILED.value,
                        "cost": prompt_cost,
                        "prompt_cost": prompt_cost,
                    }
                )
                return response

            for error_message, status in ERROR_MESSAGES.items():
                if error_message in str(e):
                    # We don't know the real cost, this is just an estimate.
                    prompt_cost = self._compute_prompt_token_cost(
                        response["n_est_prompt_tokens"]
                    )
                    response.update(
                        {
                            "text": None,
                            "status": status,
                            "cost": prompt_cost,
                            "prompt_cost": prompt_cost,
                        }
                    )
                    return response

            prompt_too_long = extract_too_many_tokens_error(e)
            if prompt_too_long is not None:
                prompt_cost = self._compute_prompt_token_cost(prompt_too_long)
                response.update(
                    {
                        "text": None,
                        "status": GeminiResponseStatus.PROMPT_TOO_LONG.value,
                        "n_prompt_tokens": prompt_too_long,
                        "cost": prompt_cost,
                        "prompt_cost": prompt_cost,
                    }
                )
                return response
            raise e

        response.update(
            {
                "text": responses.text,
                "status": GeminiResponseStatus.SUCCESS.value,
                "n_prompt_tokens": responses.usage_metadata.prompt_token_count,
                "n_output_tokens": responses.usage_metadata.candidates_token_count,
            }
        )
        prompt_cost = self._compute_prompt_token_cost(response["n_prompt_tokens"])
        output_cost = self._compute_output_token_cost(
            response["n_prompt_tokens"], response["n_output_tokens"]
        )
        response.update(
            {
                "cost": prompt_cost + output_cost,
                "prompt_cost": prompt_cost,
                "output_cost": output_cost,
            }
        )
        return response

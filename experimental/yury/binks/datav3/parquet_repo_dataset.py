import dataclasses
import glob

import pandas as pd
import pyarrow.parquet as pq

from experimental.yury.binks.datav3.constants import RETRIEVAL_LANGUAGES

# PATH_COLUMN = "max_stars_repo_path"
# REPO_COLUMN = "max_stars_repo_name"
# ID_COLUMN = "repo_uuid"

# PROMPT_COLUMN = "prompt_tokens"
# SIZE_COLUMN = "size"
# REPO_LANG_COLUMN = "max_size_lang"
# REPO_LANG_SUBCOL = "langpart"
# FILE_LANG_COLUMN = "langpart"
# FILE_LIST_COLUMN = "file_list"
# CONTENT_COLUMN = "content"
# REPO_LICENSE_COLUMN = "max_stars_repo_licenses"

# GPT_ANSWER_KEY = "gpt_answer"

# BINKS_VERSION = "binks-v2-gemini"
# BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
# OUTPUT_PATH = BASE_PATH / BINKS_VERSION
# OUTPUT_PATH.mkdir(parents=True, exist_ok=True)

# QA_DATA_V1 = BASE_PATH / "binks-v1.3-merged/repos_with_qa.jsonl"


@dataclasses.dataclass(frozen=True)
class Repo:
    repo: pd.Series

    ID_COLUMN = "repo_uuid"
    NAME_COLUMN = "max_stars_repo_name"
    FILE_LIST_COLUMN = "file_list"
    CONTENT_COLUMN = "content"

    FILEDS_TO_COVERT_TO_LISTS = [
        "max_stars_repo_licenses",
        "max_issues_repo_licenses",
        "max_forks_repo_licenses",
    ]

    @property
    def id(self):
        return self.repo[self.ID_COLUMN]

    @property
    def name(self):
        return self.repo[self.NAME_COLUMN]

    def get_files(self):
        return list(self.repo[self.FILE_LIST_COLUMN])

    # TODO: add limit on the number of tokens to be concatenated.
    # maybe with https://github.com/google/gemma_pytorch/tree/main/tokenizer
    # https://twitter.com/karpathy/status/1760350892317098371
    def concat_repo_to_string(self):
        full_repo = [
            f"Below are the files from the {self.repo[self.NAME_COLUMN]} project. Read them carefully."
        ]

        for file in self.repo[self.FILE_LIST_COLUMN]:
            if file["langpart"] in RETRIEVAL_LANGUAGES:
                full_repo.extend(
                    [
                        "This is file `%s`" % file["max_stars_repo_path"],
                        "```",
                        file[self.CONTENT_COLUMN],
                        "```",
                    ]
                )
        return "\n\n".join(full_repo)

    def to_jsonable_dict(self):
        repo_d = self.repo.to_dict()
        repo_d["file_list"] = repo_d["file_list"].tolist()
        for file in repo_d["file_list"]:
            for k in self.FILEDS_TO_COVERT_TO_LISTS:
                if not isinstance(file[k], list):
                    file[k] = file[k].tolist()
        return repo_d


class ParquetRepoDataset:
    """
    A class to iterate over repositories stored in Parquet files.

    Attributes:
        paths (list): A list of file paths to Parquet files.
        columns (list): A list of column names to be read from the Parquet files.
        chunksize (int): The number of rows to read at a time from each Parquet file.
    """

    def __init__(self, pattern, columns=None, chunksize=1000):
        """
        Initializes the class with the pattern for file paths, the columns to be read, and the chunk size.

        Args:
            pattern (str): A glob pattern to match Parquet file paths.
            columns (list): A list of column names to be read from the Parquet files.
            chunksize (int): The number of rows to read at a time from each Parquet file.
        """
        self.paths = glob.glob(str(pattern))
        assert len(self.paths) > 0, f"No files found for pattern {pattern}"
        self.columns = columns
        self.chunksize = chunksize

    def __iter__(self):
        """
        Iterates over the repositories in the Parquet files.

        Yields:
            repo (pandas.Series): A pandas Series representing a repository.
        """
        for path in self.paths:
            parquet_file = pq.ParquetFile(path)
            for batch in parquet_file.iter_batches(
                columns=self.columns, batch_size=self.chunksize
            ):
                df = batch.to_pandas()
                for _, repo in df.iterrows():
                    yield Repo(repo)

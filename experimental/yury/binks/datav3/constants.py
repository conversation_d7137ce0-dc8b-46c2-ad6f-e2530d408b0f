# Three levels
# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages
# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages
# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages

# These languages names are for the stack
REPO_LANGUAGES = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
]

additional_sample_languages = [
    "sql",
    "markdown",
]
SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages

additional_retrieval_languages = [
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]
RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages

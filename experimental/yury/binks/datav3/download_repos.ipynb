{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "import tqdm\n", "import dataclasses\n", "from types import SimpleNamespace\n", "\n", "import experimental.yury.binks.datav3.constants as constants"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "BINKS_VERSION = \"binks-v4\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "STAGE1_PATH = f\"{OUTPUT_PATH}/01_raw_repos/\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"repo_languages\": constants.REPO_LANGUAGES,\n", "        \"sample_languages\": constants.SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": constants.RETRIEVAL_LANGUAGES,\n", "        \"limit_repos\": 40000,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_min_files\": 64,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"num_partitions\": 20,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n"]}], "source": ["import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "def filter_by_repo_size(df, min_size, max_size):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    condition = (F.col(\"total_size\") >= min_size) & (\n", "        F.col(\"total_size\") <= max_size\n", "    )\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "def filter_by_repo_files(df, min_files):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    condition = (F.size(F.col(\"file_list\")) >= min_files)\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "spark = k8s_session(max_workers=16)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if hasattr(config, \"repo_languages\"):\n", "    config.languages = [lang.lower() for lang in config.repo_languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "    \n", "if hasattr(config, \"sample_languages\"):\n", "    config.sample_languages = [lang.lower() for lang in config.sample_languages]\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=config.repo_min_size,\n", "    max_size=config.repo_max_size,\n", ")\n", "df = filter_by_repo_files(df, min_files=config.repo_min_files)\n", "df = df.limit(config.limit_repos)\n", "\n", "# add repo_uuid column so that we can later unambiguously refer to repos\n", "df = df.withColumn(\"repo_uuid\", F.expr(\"uuid()\"))\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "df = df.repartition(config.num_partitions)\n", "df.write.parquet(STAGE1_PATH)\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
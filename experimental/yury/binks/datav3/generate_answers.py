"""Script to generate answers given a repo and a set of questions."""

import dataclasses
import glob
import json
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm
import re
from jinja2 import Environment

import experimental.yury.binks.datav3.gemini_api as gemini_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset

BINKS_VERSION = "binks-v3.1"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)

# REPOS_PATTERN = OUTPUT_PATH / "01_raw_repos/*.zstd.parquet"
# REPOS_PATTERN = OUTPUT_PATH / "02_raw_repos/yury/*.zstd.parquet"
# REPOS_PATTERN = OUTPUT_PATH / "02_raw_repos/colin/*.zstd.parquet"
REPOS_PATTERN = OUTPUT_PATH / "03_raw_repos/*.zstd.parquet"

# REPOS_PATTERN = BASE_PATH / "binks-v1/01_raw_repos/part-00000-9990b718-de5b-4aed-97d4-968fd1302624-c000.zstd.parquet"
# REPOS_PATTERN = BASE_PATH / "binks-v1.1/01_raw_repos/part-0000*-3ccecdd8-89f4-4520-b804-988dbe95a44d-c000.zstd.parquet"
# REPOS_PATTERN = BASE_PATH / "binks-v1.2/01_raw_repos/part-0000*-a63b384a-3dd5-45bd-aeb5-0dd345dbcad0-c000.zstd.parquet"


PROJECT_ID = "gemini-pro-420822"
LOCATION = "us-central1"
TEMPERATURE = 0.4
MAX_OUTPUT_TOKENS = 8192
NUM_PROCESSES = 32
TEMPLATE_PATH = (
    "/home/<USER>/augment/experimental/yury/binks/datav3/generate_answer_template.txt"
)


QUESTIONS_FILE = Path(
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/config_questions_cache.jsonl",
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/business_or_functionality_questions_multi_file.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/implementation_details_questions.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/business_or_functionality_questions_multi_file_v2.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/summarization_questions.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/build_system_questions.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/third_party_questions.jsonl",
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/interfaces_questions.jsonl",
    "/mnt/efs/augment/user/yury/binks/binks-v3.1/tests_questions.jsonl"
)
ANSWERS_OUTPUT_FILE = Path(
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/config_questions_answers.jsonl",
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/business_or_functionality_questions_multi_file_answers.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/implementation_details_answers.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/business_or_functionality_questions_multi_file_v2_answers.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/summarization_answers.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/build_system_answers.jsonl",
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/third_party_answers.jsonl"
    # "/mnt/efs/augment/user/yury/binks/binks-v3.1/interfaces_answers.jsonl"
    "/mnt/efs/augment/user/yury/binks/binks-v3.1/tests_answers.jsonl"
)


def join_questions_with_repos(repos_pattern, questions_per_repo):
    """Join questions with repos."""
    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id not in questions_per_repo:
            continue
        yield (repo, questions_per_repo[repo.id])


def parse_gemini_questions(path):
    data = {}
    n_questions = 0
    with open(path) as f:
        for line in f:
            datum = json.loads(line[:-1])
            if datum["status"] != "success":
                continue
            if datum["parsed_questions"] == 0:
                continue
            repo_uuid = datum["repo_uuid"]
            assert repo_uuid not in data
            data[repo_uuid] = datum["parsed_questions"]
            n_questions += len(datum["parsed_questions"])
    print("Loaded %d questions for %d repos" % (n_questions, len(data)))
    return data


# Util functions to parse the text of the answers.


def next_block_index(text, start_index, headers):
    for header in headers:
        index = text.find(header, start_index)
        if index != -1:
            return index
    return None


def next_line(text, start_index):
    next_line_index = text.find("\n", start_index)
    if next_line_index != -1:
        return next_line_index + 1
    return None


def next_question_index(text, start_index):
    question_headers = ["# Question ", "## Question "]
    return next_block_index(text, start_index, question_headers)


def next_reference_index(text, start_index):
    reference_headers = ["## References", "# References"]
    return next_block_index(text, start_index, reference_headers)


def next_answer_index(text, start_index):
    answer_headers = ["## Answer", "# Answer"]
    return next_block_index(text, start_index, answer_headers)


def parse_references(text):
    pattern = r"`([^`]+)`"
    return list(re.findall(pattern, text))


@dataclasses.dataclass
class QASample:
    question: str
    paths: list[str]
    answer: str


def parse_gemini_answers(response):
    start_index = 0
    text = response["text"]
    samples = []
    while True:
        question_start = next_question_index(text, start_index)
        if question_start is None:
            break

        question_start = next_line(text, question_start)
        question_end = next_reference_index(text, question_start)
        if question_end is None:
            break

        question = text[question_start:question_end].strip()

        references_start = next_line(text, question_end)
        references_end = next_answer_index(text, references_start)
        if references_end is None:
            break

        reference_text = text[references_start:references_end].strip()
        references = parse_references(reference_text)

        answer_start = next_line(text, references_end)
        answer_end = next_question_index(text, answer_start) or len(text)

        answer = text[answer_start:answer_end].strip()
        if len(references) > 0:
            samples.append(
                QASample(
                    question=question,
                    paths=references,
                    answer=answer,
                )
            )

        if start_index >= answer_end:
            raise ValueError(text)

        start_index = answer_end

    if response['n_output_tokens'] == MAX_OUTPUT_TOKENS:
        # Drop the last sample
        samples = samples[:-1]

    return [dataclasses.asdict(sample) for sample in samples]


class GenerateAnswersProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, max_tokens=None):
        self.max_tokens = max_tokens

    def initialize(self):
        GenerateAnswersProcessor.model = gemini_api.GeminiAPI(
            PROJECT_ID,
            LOCATION,
            gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        with Path(TEMPLATE_PATH).open() as f:
            template_str = f.read()

        # It's possible to simply instanciate the Template via `Template(template)`.
        # However, jinja2 will strip the trailing newline, which is annoying.
        # The way to prevent that is by explicitly passing the keep_trailing_newline flag
        # into the Environment object.
        env = Environment(keep_trailing_newline=True)
        GenerateAnswersProcessor.template = env.from_string(template_str)
        print("Initialized processor")

    def __call__(self, args):
        repo, questions = args
        repo_prompt = repo.concat_repo_to_string()

        questions_str = "\n".join(["%d. %s" % (i + 1, q) for i, q in enumerate(questions)])
        message = GenerateAnswersProcessor.template.render(
            repo=repo_prompt,
            questions_str=questions_str,
            num_questions=len(questions),
        )
        response = {
            "repo_uuid": repo.id,
        }
        response.update(GenerateAnswersProcessor.model.generate_response(message))

        if response["text"] is not None:
            try:
                response["parsed_answers"] = parse_gemini_answers(response)
                response["n_answers"] = len(response["parsed_answers"])
            except Exception:
                response["status"] = "failed_to_parse_questions"

        return response


def pd_median_safe(s):
    return s.median(skipna=True)


def pd_p90_safe(s):
    return np.nanpercentile(s, 90)


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        new_stats = {k: v or np.nan for k, v in response.items()}
        del new_stats["text"]
        if "repo_uuid" in new_stats:
            del new_stats["repo_uuid"]
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "cost": "sum",
            "n_answers": "sum",
            "prompt_cost": "sum",
            "output_cost": "sum",
            # "n_prompt_tokens": pd_median_safe,
            # "n_output_tokens": pd_median_safe,
            # "n_est_prompt_tokens": pd_median_safe,
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        if "n_prompt_tokens" in self.stats.columns:
            d["n_prompt_tokens_p90"] = pd_p90_safe(self.stats["n_prompt_tokens"])
        if "n_output_tokens" in self.stats.columns:
            d["n_output_tokens_p90"] = pd_p90_safe(self.stats["n_output_tokens"])
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == "success").sum()
        else:
            return len(self.stats)


questions_per_repo = parse_gemini_questions(QUESTIONS_FILE)

processor = GenerateAnswersProcessor()
stats = GenerationStats()

if ANSWERS_OUTPUT_FILE.exists():
    n_completed_repos = 0
    for line in ANSWERS_OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        if response["status"] != "success":
            continue
        repo_uuid = response["repo_uuid"]
        if repo_uuid not in questions_per_repo:
            raise ValueError(f"Found {repo_uuid} in the cache but not in the questions")
        del questions_per_repo[repo_uuid]
        stats.add_response(response)
        n_completed_repos += 1
    print(f"Loaded {n_completed_repos} repos already completed")

pbar = tqdm.tqdm(
    total=len(questions_per_repo) + stats.size(),
    desc="Gen answers",
)
pbar.update(stats.size())
if stats.size() > 0:
    pbar.set_postfix(stats.to_dict())

with ANSWERS_OUTPUT_FILE.open("a") as f:
    for response in utils.maybe_run_in_multiple_processes(
        processor,
        join_questions_with_repos(REPOS_PATTERN, questions_per_repo),
        num_processes=NUM_PROCESSES,
    ):
        stats.add_response(response)
        f.write(json.dumps(response) + "\n")
        pbar.update()
        pbar.set_postfix(stats.to_dict())
print("Done")

{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import glob"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3826 samples\n"]}], "source": ["samples_per_repo = {}\n", "for path in glob.glob(\"/mnt/efs/augment/user/colin/data/binks/searchqa-01/04_added_path_hints/*.json\"):\n", "    sample = json.loads(open(path).read())\n", "    repo_uuid = sample[\"chat_example\"][\"repo_uuid\"]\n", "    if repo_uuid in samples_per_repo:\n", "        samples_per_repo[repo_uuid].append(sample)\n", "    else:\n", "        samples_per_repo[repo_uuid] = [sample]\n", "\n", "print(\"Loaded %d samples\" % len(samples_per_repo))"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      REPO_UUID: 46485228-13a4-4c7c-af8d-00e801cc9aa7\n", "       QUESTION: Is there a function for publishing a serialized message?\n", "\n", "GEMINI QUESTION: Is there a function for publishing a serialized message?\n", "          PATHS: rmw_microxrcedds_c/src/rmw_publish.c\n", "   GEMINI PATHS: ['rmw_microxrcedds_c/src/rmw_publish.c']\n", "         ANSWER: Yes, there is a function for publishing a serialized message defined in the file located at `rmw_microxrcedds_c/src/rmw_publish.c`. The function is named `rmw_publish_serialized_message`, and it is currently not implemented as it only sets an error message indicating that the function is unsupported.\n", "\n", "Here is the relevant code excerpt from the file:\n", "\n", "```c\n", "rmw_ret_t\n", "rmw_publish_serialized_message(\n", "  const rmw_publisher_t * publisher,\n", "  const rmw_serialized_message_t * serialized_message,\n", "  rmw_publisher_allocation_t * allocation)\n", "{\n", "  (void)publisher;\n", "  (void)serialized_message;\n", "  (void)allocation;\n", "  RMW_SET_ERROR_MSG(\"function not implemented\");\n", "  return RMW_RET_UNSUPPORTED;\n", "}\n", "``` \n", "\n", "  GEMINI ANSWER: The project does not directly provide a function for publishing a serialized message. It focuses on publishing raw ROS messages.  The `rmw_publish_serialized_message` function in `rmw_microxrcedds_c/src/rmw_publish.c` is defined but marked as \"function not implemented.\" \n", "\n", "```c\n", "rmw_ret_t\n", "rmw_publish_serialized_message(\n", "  const rmw_publisher_t * publisher,\n", "  const rmw_serialized_message_t * serialized_message,\n", "  rmw_publisher_allocation_t * allocation)\n", "{\n", "  (void)publisher;\n", "  (void)serialized_message;\n", "  (void)allocation;\n", "  RMW_SET_ERROR_MSG(\"function not implemented\");\n", "  return RMW_RET_UNSUPPORTED;\n", "}\n", "```\n", "\n", "This indicates that the functionality for publishing serialized messages is not currently supported in this version of the project. \n", "\n", "---\n", "\n", "      REPO_UUID: 8a7d0b04-bb6f-4fa1-a653-96bfdb8a4a8e\n", "       QUESTION: Are there functions that reset counters to their initial states?\n", "\n", "GEMINI QUESTION: Are there functions that reset counters to their initial states?\n", "          PATHS: lib/search_counter.js\n", "   GEMINI PATHS: ['lib/search_counter.js']\n", "         ANSWER: Yes, there is a function that resets the search counter to its initial state. The function is named `_resetCounter` and it sets the `counter` variable to an empty object, effectively resetting the counter. Here's the relevant code excerpt:\n", "\n", "```javascript\n", "function _resetCounter() {\n", "    counter = {};\n", "}\n", "``` \n", "\n", "  GEMINI ANSWER: Yes, the `_resetCounter` function within the `lib/search_counter.js` file is responsible for resetting the counters to their initial states. \n", "\n", "Here's a code snippet illustrating this functionality:\n", "\n", "```javascript\n", "function _resetCounter() {\n", "    counter = {};\n", "}\n", "```\n", "\n", "This function sets the `counter` object, which stores the aggregated search statistics, to an empty object. This effectively resets all counters to their initial state of zero. \n", "\n", "---\n", "\n", "      REPO_UUID: 9746c048-aaed-45d1-81fd-d753871dd12e\n", "       QUESTION: Is there a function for convolution in the file Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c?\n", "\n", "GEMINI QUESTION: Is there a function for convolution in the file Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c?\n", "          PATHS: Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c\n", "   GEMINI PATHS: ['Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c', 'Maligaro/c_lib/c01_tools/hongyulib.h', 'Maligaro/c_lib/c02_empirical_wavelet/ESF.h']\n", "         ANSWER: Yes, there is a function for convolution used in the file located at Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c. The function is named `convolution2`, and it is called to perform the convolution between `current_ES` and `TSTAR`. Here is the relevant code excerpt where `convolution2` is used:\n", "\n", "```c\n", "//do convolution between tstar and current_ES\n", "convolution2(current_ES, npts_phase, TSTAR, conv_result);\n", "```\n", "\n", "This function takes the empirical source (ES) signal `current_ES`, the number of points in the phase `npts_phase`, the TSTAR array `TSTAR`, and outputs the convolution result into `conv_result`. The actual implementation details of `convolution2` are not provided in this snippet, so it is likely defined elsewhere in the codebase. \n", "\n", "  GEMINI ANSWER: Yes, the `tstar_ES_function.c` file utilizes convolution as a key part of its functionality. It performs convolution between the `TSTAR` array and the `current_ES` array. The convolution result is then used to stretch the `current_ES` array. Here's a code excerpt from `Maligaro/c_lib/c02_empirical_wavelet/tstar_ES_function.c` demonstrating this:\n", "\n", "```c\n", "//do convolution between tstar and current_ES\n", "//convolution(current_ES, npts_phase, TSTAR, ts_npts, conv_result);\n", "convolution2(current_ES, npts_phase, TSTAR,  conv_result);\n", "```\n", "\n", "The `convolution2` function is defined in the `hongyulib.h` file, which is included in `Maligaro/c_lib/c02_empirical_wavelet/ESF.h`. This function performs the convolution operation, storing the result in the `conv_result` array. \n", "\n", "---\n", "\n"]}], "source": ["from pathlib import Path\n", "ANSWERS_OUTPUT_FILE = Path(\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v2-colin/gemini_answers_cache.jsonl\"\n", ")\n", "\n", "for line in ANSWERS_OUTPUT_FILE.open(\"r\"):\n", "    response = json.loads(line[:-1])\n", "    if response[\"status\"] != \"success\":\n", "        continue\n", "    repo_uuid = response[\"repo_uuid\"]\n", "    gemini_samples = response['parsed_qa']\n", "    samples = samples_per_repo[repo_uuid]\n", "\n", "    if len(gemini_samples) != len(samples):\n", "        # print(\"Different number of samples: %d vs %d\" % (len(gemini_samples), len(samples)))\n", "        continue\n", "\n", "    for sample, gemini_sample in zip(samples, gemini_samples):\n", "        print(\"      REPO_UUID:\", repo_uuid)\n", "        print(\"       QUESTION:\", sample[\"new_question\"])\n", "        print(\"GEMINI QUESTION:\", gemini_sample[\"question\"])\n", "\n", "        print(\"          PATHS:\", sample[\"chat_example\"][\"filepath\"])\n", "        print(\"   GEMINI PATHS:\", gemini_sample[\"paths\"])\n", "\n", "        print(\"         ANSWER:\", sample[\"new_answer\"], \"\\n\")\n", "        print(\"  GEMINI ANSWER:\", gemini_sample[\"answer\"], \"\\n\")\n", "\n", "        print(\"---\\n\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      REPO_UUID: 46485228-13a4-4c7c-af8d-00e801cc9aa7\n", "       QUESTION: Is there a function for publishing a serialized message?\n", "\n", "GEMINI QUESTION: Is there a function for publishing a serialized message?\n", "          PATHS: rmw_microxrcedds_c/src/rmw_publish.c\n", "   GEMINI PATHS: ['rmw_microxrcedds_c/src/rmw_publish.c']\n", "         ANSWER: Yes, there is a function for publishing a serialized message defined in the file located at `rmw_microxrcedds_c/src/rmw_publish.c`. The function is named `rmw_publish_serialized_message`, and it is currently not implemented as it only sets an error message indicating that the function is unsupported.\n", "\n", "Here is the relevant code excerpt from the file:\n", "\n", "```c\n", "rmw_ret_t\n", "rmw_publish_serialized_message(\n", "  const rmw_publisher_t * publisher,\n", "  const rmw_serialized_message_t * serialized_message,\n", "  rmw_publisher_allocation_t * allocation)\n", "{\n", "  (void)publisher;\n", "  (void)serialized_message;\n", "  (void)allocation;\n", "  RMW_SET_ERROR_MSG(\"function not implemented\");\n", "  return RMW_RET_UNSUPPORTED;\n", "}\n", "```\n", "  GEMINI ANSWER: The project does not directly provide a function for publishing a serialized message. It focuses on publishing raw ROS messages.  The `rmw_publish_serialized_message` function in `rmw_microxrcedds_c/src/rmw_publish.c` is defined but marked as \"function not implemented.\" \n", "\n", "```c\n", "rmw_ret_t\n", "rmw_publish_serialized_message(\n", "  const rmw_publisher_t * publisher,\n", "  const rmw_serialized_message_t * serialized_message,\n", "  rmw_publisher_allocation_t * allocation)\n", "{\n", "  (void)publisher;\n", "  (void)serialized_message;\n", "  (void)allocation;\n", "  RMW_SET_ERROR_MSG(\"function not implemented\");\n", "  return RMW_RET_UNSUPPORTED;\n", "}\n", "```\n", "\n", "This indicates that the functionality for publishing serialized messages is not currently supported in this version of the project.\n", "\n", "---\n", "\n"]}], "source": ["sample = samples[0]\n", "gemini_sample = gemini_samples[0]\n", "\n", "print(\"      REPO_UUID:\", repo_uuid)\n", "print(\"       QUESTION:\", sample[\"new_question\"])\n", "print(\"GEMINI QUESTION:\", gemini_sample[\"question\"])\n", "\n", "print(\"          PATHS:\", sample[\"chat_example\"][\"filepath\"])\n", "print(\"   GEMINI PATHS:\", gemini_sample[\"paths\"])\n", "\n", "print(\"         ANSWER:\", sample[\"new_answer\"])\n", "print(\"  GEMINI ANSWER:\", gemini_sample[\"answer\"])\n", "\n", "print(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "\n", "sample_ids = random.sample(range(len(samples)), 20)\n", "\n", "for sample_id in sample_ids:\n", "    sample = samples[sample_id]\n", "    assert sample[\"success\"]\n", "    print(\"PATH HINT?\", sample[\"added_path\"])\n", "    print(\" QUESTION:\", sample[\"new_question\"])\n", "    print(\"   ANSWER:\", sample[\"new_answer\"])    \n", "    print(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Script to generate answers given a repo and a set of questions."""

import dataclasses
import glob
import json
import re
from pathlib import Path

import numpy as np
import pandas as pd
import sentencepiece as spm
import tqdm
from jinja2 import Environment

import experimental.yury.binks.datav3.gemini_api as gemini_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset

GEMMA_TOKENIZER_PATH = "/mnt/efs/augment/user/yury/gemma/tokenizer.model"
MAX_GEMINI_FLASH_TOKENS = 990000
REPOS_PATTERN = "/mnt/efs/augment/user/yury/binks/colin/*.zstd.parquet"
ANSWERS_OUTPUT_FILE = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v2-colin/gemini_answers_cache.jsonl"
)
PROJECT_ID = "gemini-pro-420822"
LOCATION = "us-central1"
TEMPERATURE = 0.7
MAX_OUTPUT_TOKENS = 8192
NUM_PROCESSES = 2
GEMINI_FLASH_PRICE_PER_INPUT_TOKEN = 0.7
QUESTIONS_PATTERN = (
    "/mnt/efs/augment/user/colin/data/binks/searchqa-01/04_added_path_hints/*.json"
)
TEMPLATE_PATH = (
    "/home/<USER>/augment/experimental/yury/binks/datav2/generate_answer_template.txt"
)
TQDM_BAR_FORMAT = (
    "{l_bar}{bar}| " "{n_fmt}/{total_fmt} [{elapsed}<{remaining} {postfix}]"
)


def load_questions(pattern):
    n_samples, samples_per_repo = 0, {}
    for path in glob.glob(pattern):
        sample = json.loads(open(path).read())
        n_samples += 1
        repo_uuid = sample["chat_example"]["repo_uuid"]
        if repo_uuid not in samples_per_repo:
            samples_per_repo[repo_uuid] = []
        samples_per_repo[repo_uuid].append(sample)
    print("Loaded %d samples for %d repos" % (n_samples, len(samples_per_repo)))
    return samples_per_repo


def join_questions_with_repos(questions_per_repo, repos_pattern):
    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id not in questions_per_repo:
            continue
        yield (repo, questions_per_repo[repo.id])


# Util functions to parse the text of the answers.


def next_block_index(text, start_index, headers):
    for header in headers:
        index = text.find(header, start_index)
        if index != -1:
            return index
    return None


def next_line(text, start_index):
    next_line_index = text.find("\n", start_index)
    if next_line_index != -1:
        return next_line_index + 1
    return None


def next_question_index(text, start_index):
    question_headers = ["# Question ", "## Question "]
    return next_block_index(text, start_index, question_headers)


def next_reference_index(text, start_index):
    reference_headers = ["## References", "# References"]
    return next_block_index(text, start_index, reference_headers)


def next_answer_index(text, start_index):
    answer_headers = ["## Answer", "# Answer"]
    return next_block_index(text, start_index, answer_headers)


def parse_references(text):
    pattern = r"`([^`]+)`"
    return list(re.findall(pattern, text))


@dataclasses.dataclass
class QASample:
    question: str
    paths: list[str]
    answer: str


def parse_gemini_answers(text):
    start_index = 0
    samples = []
    while True:
        question_start = next_question_index(text, start_index)
        if question_start is None:
            break

        question_start = next_line(text, question_start)
        question_end = next_reference_index(text, question_start)
        if question_end is None:
            break

        question = text[question_start:question_end].strip()

        references_start = next_line(text, question_end)
        references_end = next_answer_index(text, references_start)
        if references_end is None:
            break

        reference_text = text[references_start:references_end].strip()
        references = parse_references(reference_text)

        answer_start = next_line(text, references_end)
        answer_end = next_question_index(text, answer_start) or len(text)

        answer = text[answer_start:answer_end].strip()
        if len(references) > 0:
            samples.append(
                QASample(
                    question=question,
                    paths=references,
                    answer=answer,
                )
            )

        if start_index >= answer_end:
            raise ValueError(text)

        start_index = answer_end

    return [dataclasses.asdict(sample) for sample in samples]


class GenerateAnswerProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, max_tokens=None):
        self.max_tokens = max_tokens

    def initialize(self):
        GenerateAnswerProcessor.model = gemini_api.GeminiAPI(
            PROJECT_ID,
            LOCATION,
            gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        with Path(TEMPLATE_PATH).open() as f:
            template_str = f.read()

        # It's possible to simply instanciate the Template via `Template(template)`.
        # However, jinja2 will strip the trailing newline, which is annoying.
        # The way to prevent that is by explicitly passing the keep_trailing_newline flag
        # into the Environment object.
        env = Environment(keep_trailing_newline=True)
        GenerateAnswerProcessor.template = env.from_string(template_str)

        if self.max_tokens is not None:
            GenerateAnswerProcessor.tokenizer = spm.SentencePieceProcessor()
            GenerateAnswerProcessor.tokenizer.Load(GEMMA_TOKENIZER_PATH)
        else:
            GenerateAnswerProcessor.tokenizer = None

        print("Initialized processor")

    def __call__(self, args):
        repo, questions = args
        repo_prompt = repo.concat_repo_to_string()

        questions_str = "\n\n".join(
            ["%d. %s" % (i + 1, q["new_question"]) for i, q in enumerate(questions)]
        )

        message = GenerateAnswerProcessor.template.render(
            repo=repo_prompt,
            questions_str=questions_str,
            num_questions=len(questions),
        )

        response = {
            "repo_uuid": repo.id,
        }

        n_estimated_prompt_tokens = None
        if GenerateAnswerProcessor.tokenizer is not None:
            n_estimated_prompt_tokens = len(
                GenerateAnswerProcessor.tokenizer.tokenize(message)
            )
            response["n_est_prompt_tokens"] = n_estimated_prompt_tokens

        if (
            n_estimated_prompt_tokens is not None
            and n_estimated_prompt_tokens > self.max_tokens
        ):
            response.update(
                {
                    "text": None,
                    "status": gemini_api.GeminiResponseStatus.PROMPT_TOO_LONG.value,
                }
            )
        else:
            response.update(GenerateAnswerProcessor.model.generate_response(message))

        if (
            response["status"] == gemini_api.GeminiResponseStatus.SAFETY_FAILED.value
            and n_estimated_prompt_tokens is not None
        ):
            # We need to recalculate the cost, since Google didn't give us the number of tokens.
            # best we can do is the estimated number of prompt tokens.
            response["n_prompt_tokens"] = n_estimated_prompt_tokens

        if response["text"] is not None:
            try:
                response["parsed_qa"] = parse_gemini_answers(response["text"])
                response["n_qa_samples"] = len(response["parsed_qa"])
            except ValueError as e:
                response["status"] = "failed_to_parse_answers"
                raise e

        return response


def pd_median_safe(s):
    return s.median(skipna=True)


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        new_stats = {k: v or np.nan for k, v in response.items()}
        del new_stats["text"]
        if "repo_uuid" in new_stats:
            del new_stats["repo_uuid"]
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        d.update(
            self.stats.agg(
                {
                    "cost": "sum",
                    "prompt_cost": "sum",
                    "output_cost": "sum",
                    "n_prompt_tokens": pd_median_safe,
                    "n_output_tokens": pd_median_safe,
                    "n_est_prompt_tokens": pd_median_safe,
                    "n_qa_samples": "sum",
                }
            ).to_dict()
        )
        return d

    def size(self):
        return len(self.stats)


questions_per_repo = load_questions(QUESTIONS_PATTERN)
processor = GenerateAnswerProcessor(
    max_tokens=MAX_GEMINI_FLASH_TOKENS,
)
stats = GenerationStats()

if ANSWERS_OUTPUT_FILE.exists():
    for line in ANSWERS_OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        repo_uuid = response["repo_uuid"]
        if repo_uuid not in questions_per_repo:
            raise ValueError(f"Found {repo_uuid} in the cache but not in the questions")
        del questions_per_repo[repo_uuid]
        stats.add_response(response)


pbar = tqdm.tqdm(
    total=len(questions_per_repo) + stats.size(),
    desc="Gen answers",
    bar_format=TQDM_BAR_FORMAT,
)
pbar.update(stats.size())
if stats.size() > 0:
    pbar.set_postfix(stats.to_dict())

with ANSWERS_OUTPUT_FILE.open("a") as f:
    for response in utils.maybe_run_in_multiple_processes(
        processor,
        join_questions_with_repos(questions_per_repo, REPOS_PATTERN),
        num_processes=NUM_PROCESSES,
    ):
        stats.add_response(response)
        f.write(json.dumps(response) + "\n")
        pbar.update()
        pbar.set_postfix(stats.to_dict())

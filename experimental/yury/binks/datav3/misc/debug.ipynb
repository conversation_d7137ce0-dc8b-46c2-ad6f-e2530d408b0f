{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of unique repo uuids: 10873\n", "Number of unique repo names: 10820\n"]}], "source": ["from pathlib import Path\n", "import json\n", "\n", "BINKS_V1_V2_PATHS = [\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/repos_with_qa.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v2-haiku-v2/repos_with_qa.jsonl\",\n", "    \"/mnt/efs/augment/user/yury/binks/binks-v2-colin/repos_with_qa_original.jsonl\",\n", "]\n", "\n", "all_repo_uuids, all_repo_names = set(), set()\n", "\n", "for path in BINKS_V1_V2_PATHS:\n", "    with open(path) as f:\n", "        for line in f:\n", "            repo = json.loads(line[:-1])\n", "            repo_uuid = repo[\"repo_uuid\"]\n", "            repo_name = repo[\"max_stars_repo_name\"]\n", "            all_repo_uuids.add(repo_uuid)\n", "            all_repo_names.add(repo_name)\n", "\n", "print(\"Number of unique repo uuids: %d\" % len(all_repo_uuids))\n", "print(\"Number of unique repo names: %d\" % len(all_repo_names))\n", "\n", "BINKS_VERSION = \"binks-v3\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "with open(OUTPUT_PATH / \"binks_v1_v2_repo_uuids.jsonl\", \"w\") as f:\n", "    f.write(json.dumps(list(all_repo_uuids)) + \"\\n\")\n", "\n", "with open(OUTPUT_PATH / \"binks_v1_v2_repo_names.jsonl\", \"w\") as f:\n", "    f.write(json.dumps(list(all_repo_names)) + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/config_questions_cache.jsonl\") as f:\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file.jsonl\") as f:\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/implementation_details_questions.jsonl\") as f:        \n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/summarization_questions.jsonl\") as f:            \n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file_v2.jsonl\") as f:            \n", "    for line in f:\n", "        data = json.loads(line[:-1])    \n", "        print(data[\"repo_uuid\"])\n", "        print()\n", "        print(data[\"text\"])\n", "        print(\"\\n---\\n\")\n", "        # for index, question in enumerate(data[\"parsed_questions\"]):\n", "        #     print(\"%d. %s\" % (index + 1, question))\n", "        # print(\"\\n===\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json \n", "\n", "count = 0\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/config_questions_answers.jsonl\") as f:\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file_answers.jsonl\") as f:        \n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file_v2_answers.jsonl\") as f:        \n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v3/build_system_answers.jsonl\") as f:\n", "    for line in f:\n", "        if count >= 5:\n", "            break\n", "        datum = json.loads(line[:-1])\n", "        print(datum[\"text\"])\n", "        print(\"\\n\\n========================\\n\\n\")\n", "        count += 1"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['repo_uuid', 'n_est_prompt_tokens', 'text', 'status', 'n_prompt_tokens', 'n_output_tokens', 'cost', 'prompt_cost', 'output_cost', 'parsed_answers', 'n_answers'])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["datum.keys()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/augment/user/yury/binks/binks-v3/config_questions_answers.jsonl 884 13280\n", "/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file_answers.jsonl 1577 23491\n", "/mnt/efs/augment/user/yury/binks/binks-v3/business_or_functionality_questions_multi_file_v2_answers.jsonl 2516 37515\n", "/mnt/efs/augment/user/yury/binks/binks-v3/implementation_details_answers.jsonl 1541 22318\n", "/mnt/efs/augment/user/yury/binks/binks-v3/summarization_answers.jsonl 1438 21540\n", "/mnt/efs/augment/user/yury/binks/binks-v3/build_system_answers.jsonl 1690 15361\n", "/mnt/efs/augment/user/yury/binks/binks-v3/third_party_answers.jsonl 1588 23418\n", "/mnt/efs/augment/user/yury/binks/binks-v3/interfaces_answers.jsonl 1482 21959\n", "/mnt/efs/augment/user/yury/binks/binks-v3/tests_answers.jsonl 1635 20632\n", "************\n"]}], "source": ["from pathlib import Path\n", "\n", "BINKS_VERSION = \"binks-v3\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "QA_SAMPLE_FILES = [\n", "    OUTPUT_PATH / \"config_questions_answers.jsonl\",\n", "    OUTPUT_PATH / \"business_or_functionality_questions_multi_file_answers.jsonl\",\n", "    OUTPUT_PATH / \"business_or_functionality_questions_multi_file_v2_answers.jsonl\",\n", "    OUTPUT_PATH / \"implementation_details_answers.jsonl\",\n", "    OUTPUT_PATH / \"summarization_answers.jsonl\",\n", "    OUTPUT_PATH / \"build_system_answers.jsonl\",\n", "    OUTPUT_PATH / \"third_party_answers.jsonl\",\n", "    OUTPUT_PATH / \"interfaces_answers.jsonl\",\n", "    OUTPUT_PATH / \"tests_answers.jsonl\",\n", "]\n", "\n", "\n", "n_total, n_total_repos = 0, 0\n", "for path in QA_SAMPLE_FILES:\n", "    n_current, n_current_repos = 0, 0\n", "    with open(path) as f:\n", "        for line in f:\n", "            datum = json.loads(line[:-1])\n", "            if datum[\"status\"] == \"success\":\n", "                assert datum[\"n_answers\"] == len(datum[\"parsed_answers\"])\n", "                n_current += datum[\"n_answers\"]\n", "                n_total += datum[\"n_answers\"]\n", "                n_current_repos += 1\n", "                n_total_repos += 1\n", "    print(path, n_current_repos, n_current)\n", "\n", "print(n_total_repos, n_total)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "0 91.0\n", "1 117.0\n", "2 40.0\n", "3 48.0\n", "4 37.0\n", "5 46.0\n", "6 41.0\n", "7 63.0\n", "8 99.0\n", "9 160.0\n", "10 1371.0\n", "11 82.0\n", "12 131.0\n", "13 247.0\n", "14 290.0\n", "15 11464.0\n", "16 13.0\n", "17 0.0\n", "18 0.0\n", "19 1.0\n", "20 4.0\n", "21 1.0\n", "22 0.0\n", "23 1.0\n", "24 0.0\n", "25 2.0\n", "26 0.0\n", "27 0.0\n", "28 0.0\n", "29 0.0\n", "30 2.0\n"]}], "source": ["from pathlib import Path\n", "import numpy as np\n", "\n", "BINKS_VERSION = \"binks-v3\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "QA_SAMPLE_FILES = [\n", "    OUTPUT_PATH / \"config_questions_answers.jsonl\",\n", "    OUTPUT_PATH / \"business_or_functionality_questions_multi_file_answers.jsonl\",\n", "    OUTPUT_PATH / \"business_or_functionality_questions_multi_file_v2_answers.jsonl\",\n", "    OUTPUT_PATH / \"implementation_details_answers.jsonl\",\n", "    OUTPUT_PATH / \"summarization_answers.jsonl\",\n", "    OUTPUT_PATH / \"build_system_answers.jsonl\",\n", "    OUTPUT_PATH / \"third_party_answers.jsonl\",\n", "    OUTPUT_PATH / \"interfaces_answers.jsonl\",\n", "    OUTPUT_PATH / \"tests_answers.jsonl\",\n", "]\n", "\n", "n_gen_token_budget = 0\n", "n_answers_histogram = np.zeros((31,))\n", "\n", "for path in QA_SAMPLE_FILES:    \n", "    with open(path) as f:\n", "        for line in f:\n", "            datum = json.loads(line[:-1])\n", "            if datum[\"status\"] == \"success\":\n", "                assert datum[\"n_answers\"] == len(datum[\"parsed_answers\"])\n", "                n_answers_histogram[datum[\"n_answers\"]] += 1\n", "                # if datum[\"n_answers\"] == 30:\n", "                #     print(datum[\"text\"])\n", "                if datum[\"n_output_tokens\"] == 1024 * 8 - 1:\n", "                    # print(datum[\"text\"])\n", "                    n_gen_token_budget += 1\n", "\n", "print(n_gen_token_budget)\n", "\n", "for count in range(n_answers_histogram.shape[0]):\n", "    print(count, n_answers_histogram[count])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""<PERSON>ript to generate answers given a repo and a set of questions."""

import json
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm
from jinja2 import Environment

import experimental.yury.binks.datav3.gemini_api as gemini_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset

BINKS_VERSION = "binks-v3.1"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)

# REPOS_PATTERN = OUTPUT_PATH / "01_raw_repos/*.zstd.parquet"
# REPOS_PATTERN = OUTPUT_PATH / "02_raw_repos/yury/*.zstd.parquet"
REPOS_PATTERN = OUTPUT_PATH / "03_raw_repos/*.zstd.parquet"


BINKS_V1_V2_REPO_UUIDS = OUTPUT_PATH / "binks_v1_v2_repo_uuids.jsonl"
BINKS_V1_V2_REPO_NAMES = OUTPUT_PATH / "binks_v1_v2_repo_names.jsonl"
BINKS_V3_REPO_QUESTIONS = [
    OUTPUT_PATH / "config_questions_cache.jsonl",
    OUTPUT_PATH / "business_or_functionality_questions_multi_file.jsonl",
    OUTPUT_PATH / "implementation_details_questions.jsonl",
    OUTPUT_PATH / "business_or_functionality_questions_multi_file_v2.jsonl",
    OUTPUT_PATH / "summarization_questions.jsonl",
    OUTPUT_PATH / "build_system_questions.jsonl",
    OUTPUT_PATH / "third_party_questions.jsonl",
    OUTPUT_PATH / "interfaces_questions.jsonl",
]

PROJECT_ID = "gemini-pro-420822"
LOCATION = "us-central1"
TEMPERATURE = 0.6
MAX_OUTPUT_TOKENS = 8192
NUM_PROCESSES = 10

# N_REPOS = 4000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/config_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "config_questions_cache.jsonl")

# N_REPOS = 2000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/business_or_functionality_questions_multi_file.txt"
# QUESTIONS_OUTPUT_FILE = Path(
#     OUTPUT_PATH / "business_or_functionality_questions_multi_file.jsonl"
# )

# N_REPOS = 2000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/implementation_details_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "implementation_details_questions.jsonl")

# N_REPOS = 6000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/business_or_functionality_questions_multi_file_v2.txt"
# QUESTIONS_OUTPUT_FILE = Path(
#     OUTPUT_PATH / "business_or_functionality_questions_multi_file_v2.jsonl"
# )

# N_REPOS = 4000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/summarization_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "summarization_questions.jsonl")

# N_REPOS = 2000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/build_system_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "build_system_questions.jsonl")

# N_REPOS = 2000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/third_party_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "third_party_questions.jsonl")

# N_REPOS = 4000
# TEMPLATE_PATH = "/home/<USER>/augment/experimental/yury/binks/datav3/templates/interfaces_questions.txt"
# QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "interfaces_questions.jsonl")

N_REPOS = 2000
TEMPLATE_PATH = (
    "/home/<USER>/augment/experimental/yury/binks/datav3/templates/tests_questions.txt"
)
QUESTIONS_OUTPUT_FILE = Path(OUTPUT_PATH / "tests_questions.jsonl")


def iterate_over_repos(repos_pattern, binks_v3_repo_uuids):
    """Join questions with repos."""
    with BINKS_V1_V2_REPO_UUIDS.open("r") as f:
        binks_v1_v2_repo_uuids = set(json.load(f))
    with BINKS_V1_V2_REPO_NAMES.open("r") as f:
        binks_v1_v2_repo_names = set(json.load(f))
    print(
        "Loaded %d repo uuids and %d repo names from Binks-v1 and Binks-v2"
        % (len(binks_v1_v2_repo_uuids), len(binks_v1_v2_repo_names))
    )

    other_binks_v3_repo_uuids = set()
    for path in BINKS_V3_REPO_QUESTIONS:
        with path.open("r") as f:
            for line in f:
                data = json.loads(line[:-1])
                other_binks_v3_repo_uuids.add(data["repo_uuid"])
    print(
        "Loaded %d repo uuids from other Binks-v3 datasets"
        % len(other_binks_v3_repo_uuids)
    )

    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id in other_binks_v3_repo_uuids:
            continue
        if repo.id in binks_v3_repo_uuids:
            continue
        if repo.id in binks_v1_v2_repo_uuids:
            continue
        if repo.name in binks_v1_v2_repo_names:
            continue
        # print(repo.id, repo.name)
        yield repo


def parse_gemini_questions(text):
    actual_text_questions = text.split("QUESTIONS")[1]
    questions = [
        line.strip().partition(".")[2].strip()
        for line in actual_text_questions.splitlines()
        if line.strip() and line.strip()[0].isdigit()
    ]
    return questions


class GenerateQuestionsProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, max_tokens=None):
        self.max_tokens = max_tokens

    def initialize(self):
        GenerateQuestionsProcessor.model = gemini_api.GeminiAPI(
            PROJECT_ID,
            LOCATION,
            gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        with Path(TEMPLATE_PATH).open() as f:
            template_str = f.read()

        # It's possible to simply instanciate the Template via `Template(template)`.
        # However, jinja2 will strip the trailing newline, which is annoying.
        # The way to prevent that is by explicitly passing the keep_trailing_newline flag
        # into the Environment object.
        env = Environment(keep_trailing_newline=True)
        GenerateQuestionsProcessor.template = env.from_string(template_str)
        print("Initialized processor")

    def __call__(self, repo):
        repo_prompt = repo.concat_repo_to_string()

        message = GenerateQuestionsProcessor.template.render(
            repo=repo_prompt,
        )
        response = {
            "repo_uuid": repo.id,
        }
        response.update(GenerateQuestionsProcessor.model.generate_response(message))

        if response["text"] is not None:
            try:
                response["parsed_questions"] = parse_gemini_questions(response["text"])
                response["n_questions"] = len(response["parsed_questions"])
            except Exception:
                response["status"] = "failed_to_parse_questions"

        return response


def pd_median_safe(s):
    return s.median(skipna=True)


def pd_p90_safe(s):
    return np.nanpercentile(s, 90)


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        new_stats = {k: v or np.nan for k, v in response.items()}
        del new_stats["text"]
        if "repo_uuid" in new_stats:
            del new_stats["repo_uuid"]
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "cost": "sum",
            # "prompt_cost": "sum",
            # "output_cost": "sum",
            # "n_prompt_tokens": pd_median_safe,
            # "n_output_tokens": pd_median_safe,
            # "n_est_prompt_tokens": pd_median_safe,
            "n_questions": "sum",
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        if "n_prompt_tokens" in self.stats.columns:
            d["n_prompt_tokens_p90"] = pd_p90_safe(self.stats["n_prompt_tokens"])
        if "n_output_tokens_p90" in self.stats.columns:
            d["n_output_tokens_p90"] = pd_p90_safe(self.stats["n_output_tokens"])
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == "success").sum()
        else:
            return len(self.stats)


processor = GenerateQuestionsProcessor()
stats = GenerationStats()

binks_v3_repo_uuids = set()
if QUESTIONS_OUTPUT_FILE.exists():
    n_completed_questions = 0
    for line in QUESTIONS_OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        repo_uuid = response["repo_uuid"]
        binks_v3_repo_uuids.add(repo_uuid)
        stats.add_response(response)
    print(f"Loaded {n_completed_questions} already completed questions")

pbar = tqdm.tqdm(
    total=N_REPOS,
    desc="Gen questions",
)
pbar.update(stats.size("success"))
if stats.size() > 0:
    pbar.set_postfix(stats.to_dict())

if stats.size("success") < N_REPOS:
    with QUESTIONS_OUTPUT_FILE.open("a") as f:
        for response in utils.maybe_run_in_multiple_processes(
            processor,
            iterate_over_repos(REPOS_PATTERN, binks_v3_repo_uuids),
            num_processes=NUM_PROCESSES,
        ):
            stats.add_response(response)
            f.write(json.dumps(response) + "\n")
            if response["status"] == "success":
                pbar.update()
            pbar.set_postfix(stats.to_dict())
            if stats.size("success") >= N_REPOS:
                break
print("Done")

"""Utility functions for data pipelines."""

import multiprocessing
from typing import Any, Iterable, Iterator


class AbstractProcessor:
    """Abstract processor class."""

    def initialize(self):
        """Initialize the processor."""
        pass

    def __call__(self, *args, **kwargs):
        """Call the processor."""
        raise NotImplementedError()


def maybe_run_in_multiple_processes(
    processor: AbstractProcessor,
    inputs: Iterable[Any],
    num_processes: int,
) -> Iterator[Any]:
    """Run a processor in multiple processes if num_processes > 1.

    Args:
        processor: An instance of AbstractProcessor.
        inputs: The inputs to the processor.
        num_processes: The number of processes to use.

    Returns:
        An iterator over the results of the processor.
    """
    assert num_processes > 0
    if num_processes == 1:
        processor.initialize()
        for result in map(processor, inputs):
            yield result
    else:
        with multiprocessing.Pool(
            processes=num_processes, initializer=processor.initialize
        ) as pool:
            for result in pool.imap_unordered(processor, inputs):
                yield result

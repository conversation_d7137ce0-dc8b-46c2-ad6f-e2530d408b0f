"""<PERSON><PERSON>t to generate answers given a repo and a set of questions."""

import json
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm

import experimental.yury.binks.datav3.utils as utils
from experimental.dxy.edits.api_lib import generate_response_via_chat

BINKS_VERSION = "binks-v3.1"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)

QA_SAMPLE_FILES = [
    OUTPUT_PATH / "config_questions_answers.jsonl",
    OUTPUT_PATH / "business_or_functionality_questions_multi_file_answers.jsonl",
    OUTPUT_PATH / "business_or_functionality_questions_multi_file_v2_answers.jsonl",
    OUTPUT_PATH / "implementation_details_answers.jsonl",
    OUTPUT_PATH / "summarization_answers.jsonl",
    OUTPUT_PATH / "build_system_answers.jsonl",
    OUTPUT_PATH / "third_party_answers.jsonl",
    OUTPUT_PATH / "interfaces_answers.jsonl",
    OUTPUT_PATH / "tests_answers.jsonl",
]

GPT_MODEL_NAME = "gpt-3.5-turbo-0125"
TEMPERATURE = 0.6
MAX_OUTPUT_TOKENS = 4096
NUM_PROCESSES = 100

OUTPUT_FILE = Path(OUTPUT_PATH / "all_styled_qa_samples.jsonl")


def iterate_over_qa_samples(paths, completed_repo_uuids):
    for path in paths:
        with open(path) as f:
            for line in f:
                qa_sample = json.loads(line[:-1])
                if (
                    qa_sample["status"] == "success"
                    and qa_sample["repo_uuid"] not in completed_repo_uuids
                ):
                    yield qa_sample


def parse_gemini_questions(text):
    questions = [
        line.strip().partition(".")[2].strip()
        for line in text.splitlines()
        if line.strip() and line.strip()[0].isdigit()
    ]
    return questions


STYLE_PROMPTS = [
    "Rephrase each question as if written by a person that types this questions to Google search and tries to be overly concise.",  # https://gist.github.com/urikz/71fbb2450370502a6b73827fec0a0ea1
    "Rephrase each question as if written by a person that is not very good with English.",  # https://gist.github.com/urikz/be1a486ccf8de1a58030dd4527e52cbd
    "Rephrase these questions as if discussing casually with a colleague over coffee.",  # https://gist.github.com/urikz/4fd8b8230473ea259678e57a44d4c1ea
    "Rephrase each question as if written by a dyslexic person who sometimes makes typos, but not in every word.",  # https://gist.github.com/urikz/e21326e1ecbf234574fffbc82fe74aa2
    "Rephrase each question as if texting quickly on a smartphone, using abbreviations and informal language.",  # https://gist.github.com/urikz/4daeda88679062b608e4aebd6d8f6111
    "Phrase each question as if speaking to a digital assistant like Siri or Google Assistant, using natural spoken language.",  # https://gist.github.com/urikz/4a71f79e46b6a7bdd9ba833bc0f74a7b
    "Rephrase each question using minimal words and heavy abbreviations, as if optimizing for the shortest possible text length.",  # https://gist.github.com/urikz/d6f7eee5bea035ea6f9c296438b2cad4
    None,  # do nothing
]


def format_prompt(questions):
    questions_str = "\n".join(["%d. %s" % (i + 1, q) for i, q in enumerate(questions)])

    style_prompt = np.random.choice(STYLE_PROMPTS)

    if style_prompt is None:
        return None

    full_prompt = f"""You are given a set of questions about a software project. {style_prompt} You MUST PRESERVE the original meaning of each question.

Output only the rephrased questions in a numbered list. Do not include any additional details or double quotes around the questions.

Questions:

{questions_str}
"""

    return full_prompt


def maybe_lower_case(questions):
    new_questions = []
    for q in questions:
        if np.random.random() < 0.25:
            new_questions.append(q.lower())
        else:
            new_questions.append(q)
    return new_questions


def maybe_strip_question_mark(questions):
    new_questions = []
    for q in questions:
        if np.random.random() < 0.5:
            new_questions.append(q.replace("?", ""))
        else:
            new_questions.append(q)
    return new_questions


class StyleQuestionsProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, max_tokens=None):
        self.max_tokens = max_tokens

    def initialize(self):
        # print("Initialized processor")
        pass

    def __call__(self, qa_sample):
        original_questions = [s["question"] for s in qa_sample["parsed_answers"]]
        prompt = format_prompt(original_questions)

        qa_sample_copy = {k: v for k, v in qa_sample.items()}

        if prompt is None:
            qa_sample_copy["new_questions"] = original_questions
        else:
            try:
                # if prompt is not None:
                response = generate_response_via_chat(
                    [prompt],
                    num_completion=1,
                    temperature=TEMPERATURE,
                    model=GPT_MODEL_NAME,
                    max_tokens=MAX_OUTPUT_TOKENS,
                )[0]

                parsed_styled_questions = parse_gemini_questions(response)

                if len(original_questions) != len(parsed_styled_questions):
                    qa_sample_copy["status"] = "mismatched_n_questions"
                    # print(
                    #     "Number of questions does not match: %d vs. %d"
                    #     % (len(original_questions), len(parsed_styled_questions))
                    # )
                    return qa_sample_copy
                    # raise ValueError("Number of questions does not match: %d vs. %d" % (len(original_questions), len(parsed_styled_questions)))

                qa_sample_copy["new_questions"] = parsed_styled_questions

            except Exception as e:
                qa_sample["status"] = "failed"
                qa_sample["error"] = str(e)
                import pdb

                pdb.set_trace()

        qa_sample_copy["new_questions"] = maybe_lower_case(
            qa_sample_copy["new_questions"]
        )
        qa_sample_copy["new_questions"] = maybe_strip_question_mark(
            qa_sample_copy["new_questions"]
        )
        qa_sample_copy["status"] = "success"

        return qa_sample_copy


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        columns_to_keep = ["status", "n_answers"]
        new_stats = {
            k: response[k] if k in response else np.nan for k in columns_to_keep
        }
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "n_answers": "sum",
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == "success").sum()
        else:
            return len(self.stats)


processor = StyleQuestionsProcessor()
stats = GenerationStats()

binks_v3_repo_uuids = set()
if OUTPUT_FILE.exists():
    for line in OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        if response["status"] != "success":
            continue
        repo_uuid = response["repo_uuid"]
        binks_v3_repo_uuids.add(repo_uuid)
        stats.add_response(response)
    print(f"Loaded {len(binks_v3_repo_uuids)} already completed repos")

pbar = tqdm.tqdm(
    desc="Style questions",
)
pbar.update(stats.size())
if stats.size() > 0:
    pbar.set_postfix(stats.to_dict())

with OUTPUT_FILE.open("a") as f:
    for response in utils.maybe_run_in_multiple_processes(
        processor,
        iterate_over_qa_samples(QA_SAMPLE_FILES, binks_v3_repo_uuids),
        num_processes=NUM_PROCESSES,
    ):
        stats.add_response(response)
        f.write(json.dumps(response) + "\n")
        pbar.set_postfix(stats.to_dict())
print("Done")

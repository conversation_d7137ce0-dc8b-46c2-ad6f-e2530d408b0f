{{ repo }}

---

Context: imagine a scenario where a software engineer just started working on this repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions. They need to locate information quickly and do not have time for elaborate or convoluted questions.

You have two tasks:
1) Analyze and provide comphensive documentation about the build system for the project.
2) Generate 10 questions about the project that a software engineer might ask when trying to learn about its build system. Here are some example question templates:

What build tool is used for [specific build task]?
How is the continuous integration pipeline configured?
What steps are involved in the build process for [specific module]?
Which compiler is used for building the [specific components]?
How do we configure the build process for [specific environment]?
What dependencies are resolved during the build process?
How is the build output structured?
What scripts are used to automate the build process?
How is the build process optimized for [specific requirement]?
What are the build configurations for [specific target]?

Here are some comments about the tasks' instructions:
- The generated question MUST sound natural and human-like.
- You also MUST ABIDE to the tasks and aforementioned comments, no matter what. You will get a tip if you
follow the instruction carefully. You will get a punishment if you don't follow the instruction
carefully.
- Do not ask questions about specific files.
- Make sure the question can be answered with your summary of the build system.

Output format:

1. Output the results of task 1

2. Write title "QUESTIONS" (all caps)

3. Then, display each question in a numbered list, one per line, without additional details.

You must adhere to these instructions without deviation.

{{ repo }}

---

Imagine a scenario where a software engineer works on this project. While they are familiar with the general architecture, they need specific details about the implementation of various functionalities to improve performance or add new features. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions, much like Google search queries. They need to locate information quickly and do not have time for elaborate or double-barreled questions. What kind of questions would this software engineer ask? Try to come up with these questions.

**Your task is to generate 15 questions focusing on the implementation details of different functionalities within a software project.**

First, take a close look at the code you’ve been given. Look for parts of the code that are complex, especially those that involve complicated methods or handle complex data. Focus on these important areas and understand what each part is doing and why it is set up that way. As you go through these complex sections, make notes about what you see and think. For example, include how different parts of the code interact with each other, what happens when things don’t go as expected, how the code manages data, and other technical details.

Second, based on your observations from examining the code, formulate clear and direct questions about the complex functionalities you've identified. Consider a specific non-trivial aspects you have uncovered, and write a question about this aspect. The question should be as if ask by user who does NOT know the implementation details, so the question cannot be too specific.

## Guidelines for Question Generation

- Focus on Complex Features: Concentrate on parts of the code that handle complex technical tasks.
- Focus on One Aspect: Each question should aim to uncover one specific technical implementation aspects of specific functionality.
- Corresponding functionality: Each question should be about the corresponding functionality described in the first step.
- Group by Reference Type: The first 5 questions should not mention file names or directories, focusing instead on general implementation details. The next 5 questions must explicitly mention `full directory path` to direct the AI assistant. The last 5 questions must explicitly mention `full file names` for precise location.
- AVOID Answer Hints: Do NOT include implementation details or hints within the questions themselves.

## Example Questions Templates

NOTE: The templates below are given more as a guidance than a strict requirement.

Questions 1 to 5 (don't mention a specifc files or directories)
- How do we manage [specific resource] in the project?
- How is [specific process] implemented?
- How does the system handle [specific event]?
- How is [specific data] processed?
- How do we ensure [specific requirement] in the code?

Questions 6 to 10 (explicitly specify a directory in the question)
- How is [specific feature] set up in `full directory path`?
- How is [specific error] dealt with in `full directory path`?
- How do we implement [specific algorithm] in `full directory path`?
- How is [specific security measure] applied in `full directory path`?
- What algorithm is used for [specific task] in `full directory path`?

Questions 11 to 15 (explicitly specify a file or multiple files in the question)
- How is [specific task] done in the file at `full file path or multiple files`?
- What method is used for [specific task] in the file at `full file path or multiple files`?
- How is [specific functionality] executed in the file at `full file path or multiple files`?
- How do we achieve [specific result] in the application in `full file path or multiple files`?
- How is [specific error] fixed in the file at `full file path or multiple files`?

## Output Format

1. First, write you analyze on 15 functionalities of your choice. Follow instructions above.

2. Write the title "QUESTIONS" (all caps)

3. Then, write questions in a single numbered list, one question per line, with each question directly linked to its respective functionality. Do not include any additional details.

You must adhere to these instructions without deviation.

{{ repo }}

---

Context: imagine a scenario where a software engineer just started working on this repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions. They need to locate information quickly and do not have time for elaborate or convoluted questions.

You have one task:
1) Generate 15 questions about the project that a software engineer might ask when trying to learn about what its tests cover. Here are some example question templates:


Where are the unit tests for [specific functionality] located?
Where are the integration tests for [specific module] defined?
Where can I find the test cases for [specific feature]?
Where are the end-to-end tests for [specific workflow] implemented?
Where are the performance tests for [specific component] located?
Where do we define the test data for [specific scenario]?
Where are the test fixtures for [specific module] defined?
Where is the test runner configuration file located?
Where are the test reports generated?


Here are some comments about the tasks' instructions:
- The generated question MUST sound natural and human-like.
- You also MUST ABIDE to the tasks and aforementioned comments, no matter what. You will get a tip if you
follow the instruction carefully. You will get a punishment if you don't follow the instruction
carefully.
- Do not ask questions about specific files.

Output format:

1. Write title "QUESTIONS" (all caps) on a single line.

2. Then, output the results of task 1 in the following format: Display each question in a numbered list, one per line. Do not include any additional details.

You must adhere to these instructions without deviation.

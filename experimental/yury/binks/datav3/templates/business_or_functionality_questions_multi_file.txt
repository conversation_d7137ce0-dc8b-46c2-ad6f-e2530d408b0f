{{ repo }}

---

Imagine a scenario where a software engineer works on this project. While they are familiar with the project as a whole, they need help locating specific functionalities within the repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions, much like Google search queries. They need to locate information quickly and do not have time for elaborate or double-barreled questions. What kind of questions would this software engineer ask? Try to come up with these questions.

**Your task is to generate 15 questions about a software project.**

First, analyze the given code to identify and understand various functionalities and business logic implemented within the repository. Focus on specific functionalities such as adding a new user to the database, starting a new model training, saving a checkpoint during training, etc. Prioritize functionalities that span multiple files or directories. For each functionality, list the files and directories where these are implemented and provide a brief description of each functionality.

Second, formulate questions based on the identified functionalities and their corresponding files or directories.

## Guidelines for Question Generation

- Focus on One Aspect: Each question should target only one specific functionality or aspect of business logic.
- Encourage Search-Like Questions: Questions should help locate specific functionalities or data handling within the codebase, similar to performing a search task.
- AVOID General Questions: Never ask "What is the purpose" or "What is the role."
- AVOID Complex Questions: Do not include compound or double-barreled questions.
- Corresponding File: Each question should be about the corresponding file selected in the first step.
- Functionality Reference: The first 5 questions should be formulated without mentioning file names or directories. The next 5 questions must explicitly mention directories. The last 5 questions must explicitly mention full file names or directory paths.
- Prioritize Multi-File Functionalities: Focus on functionalities that are implemented across multiple files or directories.

## Example Questions Templates

Here are some examples of question templates

- Where is the code for [specific functionality] implemented?
- Where do we handle [specific task] in the repository?
- Where is the logic for [specific process] defined?
- Where can I find the code for [specific feature]?
- Where is the functionality for [specific operation] implemented?
- Where do we define [specific configuration] in the directory [full directory path]?
- Where is the entry point for [specific process] in the directory [full directory path]?
- Where is the [specific resource] managed in the directory [full directory path]?
- Where can I find the initialization code for [specific component] in the directory [full directory path]?
- Where is the implementation for [specific method] in the directory [full directory path]?
- Where is the functionality for [specific feature] in the file located at [full files path]?
- How is [specific business logic] implemented in the file at [full files path]?
- Where can I find the code for [specific operation] handling in the file at [full files path]?
- Where is the code for [specific task] in the document at [full files path]?
- Which file contains the logic for [specific process] at [full files path]?

## Output Format

1. Select 15 functionalities and write down their descriptions along with the paths of the files or directories where they are implemented: For each functionality, provide a brief description and list the relevant files and directories that contain the implementation of that functionality. Focus on specific business logic or functionalities like adding a new user to the database, starting a new model training, saving a checkpoint during training, etc. Prioritize functionalities that span multiple files or directories.

2. Write title "QUESTIONS" (all caps)

3. Then, display each question in a numbered list, one per line, with each question directly linked to its respective functionality, without additional details.

You must adhere to these instructions without deviation.

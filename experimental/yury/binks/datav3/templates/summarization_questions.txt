{{ repo }}

Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help understanding the purpose of different components within the repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions. They need to locate information quickly and do not have time for elaborate or convoluted questions. What kind of questions would this software engineer ask? Try to come up with these questions.

**Your task is to generate 15 questions asking to explain or summarize different components within this project.**

First, take a close look at the code you’ve been given, and recognize 15 individual complex components -- individual services, libraries, tools, etc. Focus on components that span multiple files or directories.

Second, write questions about direct questions asking to explain or summarize each component. Make sure user referens to the component using a natural language rather than explicit name.

## Example Questions Templates

In the templates below, X is a natural language description of one of the components.

NOTE: The templates below are given more as a guidance than a strict requirement.

- Explain what the X is for.
- Summarize the purpose of the service X.
- Give me the gist of service X.
- What does component X manage?
- How does tool X interact with other parts of the system?
- What role does library X play in the project?
- Can you describe the function of module X?
- How is service X utilized within the workflow?
- What is the primary function of script X?
- What operations does the X feature support?
- How does configuration X affect system performance?
- What dependencies does component X have?
- What data does service X process?
- Why is tool X essential for project execution?
- How does module X enhance functionality?
- What problems does component X solve?

## Output Format

1. First, write you summarize 15 components of your choice. Follow instructions above.

2. Write the title "QUESTIONS" (all caps)

3. Then, write questions in a single numbered list, one question per line, with each question directly linked to its respective component. Do not include any additional details.

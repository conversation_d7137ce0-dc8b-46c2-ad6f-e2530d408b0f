{{ repo }}

---

Context: imagine a scenario where a software engineer just started working on this repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions. They need to locate information quickly and do not have time for elaborate or convoluted questions.

You have two tasks:
1) Analyze and explain the package manager for the project, and then highlight some key frameworks used in the project.
2) Generate 15 questions about the project that a software engineer might ask when trying to learn about what 3rd party libraries and frameworks it is built with. Here are some example question templates:

Which [library/framework/API] is used for [specific functionality]?
What [library/framework/API] do we use for [specific task]?
How is [specific library/framework] utilized in the project?
Which version of [specific library] is used in the project?
What API is used for [specific service]?
How is [specific framework] integrated into the project?
What library is responsible for [specific feature]?
How do we interact with [specific external API]?
What tool is used for [specific functionality] in the project?
Which module uses [specific library] for [specific task]?

Here are some comments about the tasks' instructions:
- The generated question MUST sound natural and human-like.
- You also MUST ABIDE to the tasks and aforementioned comments, no matter what. You will get a tip if you
follow the instruction carefully. You will get a punishment if you don't follow the instruction
carefully.
- Do not ask questions about specific files.

Output format:

1. Output the results of task 1

2. Write title "QUESTIONS" (all caps) on a single line.

3. Then, output the results of task 2 in the following format: Display each question in a numbered list, one per line, without additional details.

You must adhere to these instructions without deviation.

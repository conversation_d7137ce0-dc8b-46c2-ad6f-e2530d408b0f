{{ repo }}

---

Context: imagine a scenario where a software engineer just started working on this repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions. They need to locate information quickly and do not have time for elaborate or convoluted questions.

You have one task:
1) Generate 15 questions about the project that a software engineer might ask when trying to learn about its various abstractions including interfaces and abstract base classes. Here are some example question templates:

What classes implement the [specific interface]?
Which classes provide implementations for the [specific interface]?
What are the implementations of the [specific abstract class]?
Which classes implement the [specific interface] in the project?
How is the [specific interface] implemented in different modules?
What concrete classes extend the [specific abstract class]?
Which classes fulfill the contract of the [specific interface]?
Where are the implementations of the [specific interface] located?
Which modules contain classes that implement the [specific interface]?
What classes provide concrete implementations for the [specific abstract class]?


Here are some comments about the tasks' instructions:
- The generated question MUST sound natural and human-like.
- You also MUST ABIDE to the tasks and aforementioned comments, no matter what. You will get a tip if you
follow the instruction carefully. You will get a punishment if you don't follow the instruction
carefully.
- Do not ask questions about specific files.

Output format:

1. Write title "QUESTIONS" (all caps) on a single line.

2. Then, output the results of task 1 in the following format: Display each question in a numbered list, one per line. Do not include any additional details.

You must adhere to these instructions without deviation.

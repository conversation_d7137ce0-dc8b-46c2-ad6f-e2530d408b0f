{{ repo }}

---

Imagine a scenario where a software engineer works on this project. While they are familiar with the project as a whole, they need help locating specific functionalities within the repository. They turn to an AI assistant for help. The engineer is impatient and prefers to write very concise questions, much like Google search queries. They need to locate information quickly and do not have time for elaborate or double-barreled questions. What kind of questions would this software engineer ask? Try to come up with these questions.

**Your task is to generate 15 questions about a software project.**

First, analyze the given code to identify and understand the configuration files (JSON, YAML, XML, Dockerfile, jsonnet, INI, TOML, etc.) or build files (B<PERSON>LD, TARGET, Makefile, cmake, etc.). Focus on files that seem more critical than others—for example, those used in many places or with content that is unique compared to other configuration files. Select 15 files and write down their paths along with a brief description of what each file does and which settings are being configured within.

Second, formulate questions based on the content of these files.

## Guidelines for Question Generation

- Focus on One Aspect: Each question should target only one feature, process, or setup aspect.
- Encourage Search-Like Questions: Questions should help locate specific functionalities or data handling within the codebase, similar to performing a search task.
- AVOID General Questions: Never ask "What is the purpose" or "What is the role."
- AVOID Complex Questions: Do not include compound or double-barreled questions.
- Corresponding File: Each question should be about the corresponding file selected in the first step.
- File Name Reference: The first 10 questions should be formulated without mentioning file names. The last 5 questions must explicitly mention file names using a natural language shorthand.

## Example Questions Templates

Here are some examples of question templates (where X and Y are some natural language placeholders)

- What are the settings for the [specific feature]?
- Where do we set [specific configuration option]?
- What is the default setting for [specific configuration option]?
- Where do we configure [specific feature]?
- Where can I find the configuration for [specific feature or device]?
- How do I adjust settings for [specific feature] based on user preferences?
- What parameters control [specific functionality] in our system?
- How are [specific resource] thresholds set in the system's configuration?
- What options are available for configuring [specific functionality]?
- How is [specific device functionality] managed within the system?
- How is [configuration parameter] adjusted for different [devices or environments] in the settings file located at [full file path]?
- What are the file paths for [resources or assets] used across different [devices or environments] in the document at [full file path]?
- Which [specific capabilities] are enabled in the [specific build configuration] found in the file at [full file path]?
- How do we specify [specific parameter] for [feature or device] in the configuration file located at [full file path]?
- What values are supported for [specific configuration option] in the file located at [full file path]?

## Output Format

1. Select 15 files and write down their paths, along with a brief description of what each file does: For each of the 15 files, provide the file path and a short description explaining its purpose or role within the project as well as which settings are being configured in the file. Focus on configuration files, build files, various Manifests, Dockerfiles, etc.

2. Write title "QUESTIONS" (all caps)

3. Then, display each question in a numbered list, one per line, without additional details.

You must adhere to these instructions without deviation.

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def parse_url(url: str) -> dict:\n", "    pattern = r'https://github.com/([^/]+)/([^/]+)/blob/([^/]+)/([^?#]+).*#L(\\d+)(-L(\\d+))?'\n", "    match = re.match(pattern, url)\n", "    if match:\n", "        repo_owner, repo_name, commit, path, lines_range_start, _, lines_range_end = match.groups()\n", "        if lines_range_end is None:\n", "            lines_range_end = lines_range_start\n", "        return {\n", "            \"repo_owner\": repo_owner,\n", "            \"repo_name\": repo_name,\n", "            \"repo_owner_name\": f\"{repo_owner}/{repo_name}\",\n", "            \"commit\": commit,\n", "            \"path\": path,\n", "            \"start\": int(lines_range_start),\n", "            \"end\": int(lines_range_end) if lines_range_end else int(lines_range_start)\n", "        }\n", "    else:\n", "        raise ValueError(\"Invalid GitHub URL: %s\" % url)\n", "\n", "print(parse_url(\"https://github.com/palantir/blueprint/blob/79010df15a799883b0729e3a07d03737b97177a8/packages/core/src/docs/classes.md?plain=1#L169C1-L175C24\"))\n", "print(parse_url(\"https://github.com/okuar/ExoClone/blob/46b4f86c7045441552f457295945d9343a1d4e4a/Assets/Scripts/Controllers/ResearchController.cs#L96-L143 \"))\n", "print(parse_url(\"https://github.com/okuar/ExoClone/blob/46b4f86c7045441552f457295945d9343a1d4e4a/Assets/Scripts/View/Cards/ResearchCard.cs#L24\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import csv\n", "import dataclasses\n", "import json\n", "\n", "EVAL_CRITERIA = {\"Blank Filling\", \"Keyword Matching\", \"Keywords Matching\"}\n", "\n", "\n", "PATHS = [\n", "    \"/mnt/efs/augment/user/yury/binks/evals/raw/turing1.csv\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/raw/turing2.csv\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/raw/alex1.csv\",\n", "]\n", "\n", "\n", "class Sample:\n", "    def __init__(self, category, question, references, answer, eval_criterion, eval_label):\n", "        self.category = category\n", "        self.question = question\n", "        self.answer = answer\n", "        self.eval_criterion = eval_criterion\n", "        self.eval_label = eval_label\n", "\n", "        if self.eval_criterion not in EVAL_CRITERIA:\n", "            raise ValueError(f\"Invalid eval_criterion: {self.eval_criterion} for question {self.question}\")\n", "\n", "        refereces_list = references.replace(\",\", \"\\n\").replace(\"\\s\", \"\\n\").split()\n", "        self.references = []\n", "        parsed_reference0 = parse_url(refereces_list[0])\n", "        self.repo_owner = parsed_reference0[\"repo_owner_name\"]\n", "        self.commit = parsed_reference0[\"commit\"]\n", "        for reference in refereces_list:\n", "            parsed_reference = parse_url(reference)\n", "            assert self.repo_owner == parsed_reference[\"repo_owner_name\"], reference\n", "            assert self.commit == parsed_reference[\"commit\"], reference\n", "            self.references.append(parsed_reference)\n", "\n", "        if self.commit == \"main\":\n", "            raise ValueError(\"Invalid commit: %s\" % self.commit)\n", "\n", "        self.repo_id = self.repo_owner.replace(\"/\", \"_\") + \"_\" + self.commit[:7]\n", "\n", "    def to_dict(self):\n", "        return {\n", "            \"category\": self.category,\n", "            \"question\": self.question,\n", "            \"references\": self.references,\n", "            \"answer\": self.answer,\n", "            \"eval_criterion\": self.eval_criterion,\n", "            \"eval_label\": self.eval_label,\n", "        }\n", "\n", "n_total_samples = 0\n", "samples_per_id = {}\n", "for path in PATHS:\n", "    with open(path, 'r') as csvfile:\n", "        reader = csv.Dict<PERSON><PERSON>er(csvfile)\n", "        for row in reader:\n", "            try:\n", "                sample = Sample(\n", "                    category=row.get(\"Category\", None),\n", "                    question=row[\"Question\"].strip(),\n", "                    references=row[\"References\"].strip(),\n", "                    answer=row[\"Answer\"].strip(),\n", "                    eval_criterion=row[\"Evaluation Criteria\"].strip(),\n", "                    eval_label=row[\"Evaluation Label\"].strip(),\n", "                )\n", "                n_total_samples += 1\n", "                if sample.repo_id not in samples_per_id:\n", "                    samples_per_id[sample.repo_id] = []\n", "                samples_per_id[sample.repo_id].append(sample)\n", "            except ValueError as e:\n", "                # print(row)\n", "                print(f\"Failed to parse sample: {e}\")\n", "\n", "print(\"Loaded %d samples and %d repos\" % (n_total_samples, len(samples_per_id)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import json\n", "import os\n", "import subprocess\n", "from pathlib import Path\n", "\n", "from research.retrieval.utils import convert_repository_to_documents\n", "\n", "\n", "IGNORE_EXTENSION = {\".meta\", \".svg\"}\n", "\n", "\n", "def get_extension(path):\n", "    return Path(path).suffix\n", "\n", "\n", "def setup_repo(samples):\n", "    sample = samples[0]\n", "    repo_url = f\"https://github.com/{sample.repo_owner}\"\n", "    repo_git_url = repo_url + \".git\"\n", "    repo_dir = f\"/mnt/efs/augment/user/yury/binks/evals/raw_repos/{sample.repo_id}\"\n", "\n", "    if not os.path.exists(repo_dir):\n", "        print(f\"Cloning repository {sample.repo_owner} from {repo_url}...\")\n", "        subprocess.run([\"git\", \"clone\", repo_git_url, repo_dir])\n", "\n", "    subprocess.run([\"git\", \"checkout\", sample.commit], cwd=repo_dir)\n", "\n", "    current_commit = subprocess.check_output([\"git\", \"rev-parse\", \"HEAD\"], cwd=repo_dir).decode(\"utf-8\").strip()\n", "    assert current_commit == sample.commit, f\"Current commit {current_commit} does not match expected commit {sample.commit}\"\n", "\n", "    repo_file_path = f\"/mnt/efs/augment/user/yury/binks/evals/repos/{sample.repo_id}.json\"\n", "\n", "    if not os.path.exists(repo_file_path):\n", "        print(f\"Generating repo file {repo_file_path}...\")\n", "        docs = convert_repository_to_documents(repo_dir)\n", "        docs = [d for d in docs if get_extension(d.path) not in IGNORE_EXTENSION]\n", "\n", "        docs_as_dict = {\n", "            \"repo_url\": \"repo_url\",\n", "            \"docs\": [dataclasses.asdict(d) for d in docs],\n", "            \"samples\": [sample.to_dict() for sample in samples],\n", "            \"repo_owner\": sample.repo_owner,\n", "            \"commit\": sample.commit,\n", "            \"repo_id\": sample.repo_id,\n", "        }\n", "        with open(repo_file_path, \"w\") as f:\n", "            json.dump(docs_as_dict, f)\n", "\n", "\n", "for sample in samples_per_id.values():\n", "    setup_repo(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "\n", "MODEL_NAME = \"chatanol1_11\"\n", "\n", "config = {\n", "    # \"scorer\": {\n", "    #     \"name\": \"dense_scorer_v2_fbwd\",\n", "    #     \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-2\",\n", "    # },\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-11\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "}\n", "\n", "retriever = create_retriever(config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import dataclasses\n", "import numpy as np\n", "from research.core.model_input import ModelInput\n", "import pathlib\n", "from research.core.types import Document\n", "import json\n", "\n", "\n", "def compute_coverage_stupid(sample, retrieved_chunks):\n", "    label_lines = set([\n", "        (reference[\"path\"], line)\n", "        for reference in sample[\"references\"]\n", "        for line in range(reference[\"start\"], reference[\"end\"] + 1)\n", "    ])\n", "    predicted_lines = set([\n", "        (chunk.path, line)\n", "        for chunk in retrieved_chunks\n", "        for line in chunk.line_range\n", "    ])\n", "\n", "    covered_lines = label_lines & predicted_lines\n", "    total_label_lines = len(label_lines)\n", "\n", "    coverage = float(len(covered_lines)) / total_label_lines\n", "    return coverage\n", "\n", "def evaluate_repo(repo, top_k=10):\n", "    retriever.remove_all_docs()\n", "    retriever.add_docs(\n", "        Document(**doc)\n", "        for doc in repo[\"docs\"]\n", "    )\n", "    result = []\n", "    for sample in repo[\"samples\"]:\n", "        new_sample = sample.copy()\n", "        chunks, scores = retriever.query(ModelInput(sample[\"question\"]), top_k=top_k)\n", "        new_sample[\"coverage\"] = compute_coverage_stupid(sample, chunks)\n", "        new_sample[\"chunks\"] = []\n", "        for chunk, score in zip(chunks, scores):\n", "            chunk_dict = dataclasses.asdict(chunk)\n", "            chunk_dict[\"score\"] = score\n", "            new_sample[\"chunks\"].append(chunk_dict)\n", "        result.append(new_sample)\n", "    return result\n", "\n", "\n", "PATHS = [\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/react-hook-form_documentation_d5ce267.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/react-hook-form_react-hook-form_4549afd.json\",\n", "    # \"/mnt/efs/augment/user/yury/binks/evals/repos/alibaba_fastjson2_f866b92.json\",\n", "    # \"/mnt/efs/augment/user/yury/binks/evals/repos/apache_kafka_81e6098.json\",\n", "    # \"/mnt/efs/augment/user/yury/binks/evals/repos/apache_kafka_b8c9638.json\",\n", "    # \"/mnt/efs/augment/user/yury/binks/evals/repos/apache_kafka_f9db4fa.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/chat2db_Chat2DB_2248343.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/chat2db_Chat2DB_521aa82.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/chat2db_Chat2DB_5f51a9b.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/epicmaxco_vuestic-ui_13064fa.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/epicmaxco_vuestic-ui_249a3ec.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/jestjs_jest_654dbd6.json\",\n", "    # \"/mnt/efs/augment/user/yury/binks/evals/repos/okuar_OVIVO_8670784.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/okuar_OVIVO_8da115c.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/palantir_blueprint_5721022.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/palantir_blueprint_79010df.json\",\n", "    \"/mnt/efs/augment/user/yury/binks/evals/repos/palantir_blueprint_e094518.json\",\n", "]\n", "\n", "all_results = []\n", "for path in PATHS:\n", "    path_dirname, path_filename = os.path.split(path)\n", "    result_path = os.path.join(path_dirname, \"results\", MODEL_NAME)\n", "    if not os.path.exists(result_path):\n", "        os.makedirs(result_path)\n", "    result_path = os.path.join(result_path, path_filename.replace(\".json\", \"_results.json\"))\n", "    if not os.path.exists(result_path):\n", "        with open(path) as f:\n", "            repo = json.load(f)\n", "        print(\"Loaded repo with %d files and %d samples from %s\" % (len(repo[\"docs\"]), len(repo[\"samples\"]), path))\n", "        result = evaluate_repo(repo)\n", "        with open(result_path, \"w\") as f:\n", "            json.dump(result, f, indent=2)\n", "    else:\n", "        with open(result_path) as f:\n", "            result = json.load(f)\n", "    all_results.extend(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame(all_results)\n", "df.mean(numeric_only=True)\n", "\n", "# chatanol1_11   -- 0.598006\n", "# chatanol1_14   -- 0.554579\n", "# chatanol1_16_2 -- 0.605972\n", "# chatanol1_16_3 -- 0.551229"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for result in all_results:\n", "    if result['coverage'] == 0.0:\n", "        print(result['question'])\n", "        references = \"\\n\".join([f\"+ {reference['path']}#L{reference['start']}-L{reference['end']}\" for reference in result['references']])\n", "        print(references)\n", "        print()\n", "        chunks = \"\\n\".join([f\"- {chunk['parent_doc']['path']}#L{chunk['line_offset']}-L{chunk['line_offset'] + chunk['length_in_lines']}\" for chunk in result['chunks']])\n", "        print(chunks)\n", "        print()\n", "        print('---')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
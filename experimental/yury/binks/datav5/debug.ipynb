{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "counter = 0\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs-sonnet/show_files.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        if data[\"status\"] != \"success\":\n", "            continue\n", "        counter += 1\n", "\n", "        print(data[\"repo_uuid\"])\n", "        print()\n", "        # print(data[\"text\"])\n", "        for sample in data[\"qa_samples\"]:\n", "            print(\"QUESTION:\", sample[\"question\"])\n", "            print(\"    PATH:\", sample[\"paths\"][0])\n", "            print(\"  ANSWER:\", sample[\"answer\"])\n", "            print()\n", "        print(\"\\n---\\n\")\n", "\n", "        if counter >= 10:\n", "            break\n", "\n", "# https://gist.github.com/urikz/62ff730a1e1878cf8ac03bb5f76384c8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "\n", "REPOS_PATTERN = \"/mnt/efs/augment/user/yury/binks/binks-v4/01_raw_repos/*.zstd.parquet\"\n", "\n", "for repo in ParquetRepoDataset(REPOS_PATTERN):\n", "    files = repo.get_files()\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import sentencepiece as spm\n", "\n", "GEMMA_TOKENIZER_PATH = \"/mnt/efs/augment/user/yury/gemma/tokenizer.model\"\n", "\n", "tokenizer = spm.SentencePieceProcessor()\n", "tokenizer.Load(GEMMA_TOKENIZER_PATH)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import os\n", "\n", "\n", "def compute_file_size(file):\n", "    return len(tokenizer.tokenize(file[\"content\"]))\n", "\n", "def build_file_tree(files):\n", "    file_tree = {}\n", "    for file in files:\n", "        file_path = pathlib.Path(file[\"max_stars_repo_path\"])\n", "        dir_path = file_path.parent\n", "        file_name = file_path.name\n", "        file_size = compute_file_size(file)  # custom function to compute file size\n", "        file_obj = {\n", "            \"name\": file_name,\n", "            \"size\": file_size,\n", "            \"file\": file  # store the file object itself\n", "        }\n", "        current_dir = file_tree\n", "        for dir_component in dir_path.parts:\n", "            if dir_component not in current_dir:\n", "                current_dir[dir_component] = {}\n", "            current_dir = current_dir[dir_component]\n", "        current_dir[file_name] = file_obj\n", "    return file_tree\n", "\n", "def compute_dir_sizes(file_tree, dir_path=\"\"):\n", "    dir_sizes = {}\n", "    total_size = 0\n", "    for file_name, file_subtree in file_tree.items():\n", "        if \"size\" in file_subtree:  # file size\n", "            total_size += file_subtree[\"size\"]\n", "        else:  # subdirectory\n", "            subdir_path = os.path.join(dir_path, file_name)\n", "            subdir_size, subdir_sizes = compute_dir_sizes(file_subtree, subdir_path)\n", "            total_size += subdir_size  # add the total size of the subdirectory\n", "            dir_sizes.update(subdir_sizes)\n", "    dir_sizes[dir_path] = total_size\n", "    return total_size, dir_sizes\n", "\n", "def get_all_files(file_tree, dir_path, files=None):\n", "    if files is None:\n", "        files = []\n", "    for file_name, file_obj in file_tree.items():\n", "        if \"file\" in file_obj:  # file object\n", "            files.append(file_obj[\"file\"])  # original file object\n", "        else:  # subdirectory\n", "            subdir_path = os.path.join(dir_path, file_name)\n", "            get_all_files(file_obj, subdir_path, files)\n", "    return files\n", "\n", "file_tree = build_file_tree(files)\n", "_, dir_sizes = compute_dir_sizes(file_tree)\n", "\n", "for dir_path, total_size in dir_sizes.items():\n", "    print(f\"Directory {dir_path}: {total_size} bytes\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_all_files(file_tree):\n", "    files = []\n", "    for file_obj in file_tree.values():\n", "        if \"file\" in file_obj:\n", "            files.append(file_obj[\"file\"])\n", "        else:\n", "            files.extend(get_all_files(file_obj))\n", "    return files\n", "\n", "def get_all_files_per_directory(file_tree, dir_path):\n", "    files = []\n", "    current_dir = file_tree\n", "    dir_components = dir_path.split(os.sep)\n", "    for component in dir_components:\n", "        if component in current_dir:\n", "            current_dir = current_dir[component]\n", "        else:\n", "            return []\n", "    return get_all_files(current_dir)\n", "\n", "get_all_files_per_directory(file_tree, 'zf-server/src/common/auth')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_all_path_parts(file_tree, current_path=\"\"):\n", "    path_parts = []\n", "    for file_name, file_obj in file_tree.items():\n", "        new_path = os.path.join(current_path, file_name)\n", "        if \"file\" not in file_obj:  # subdirectory\n", "            path_parts.append(new_path)\n", "            path_parts.extend(generate_all_path_parts(file_obj, new_path))\n", "    return path_parts\n", "\n", "def generate_all_path_parts_for_repo(file_tree):\n", "    path_parts_counter = Counter()\n", "    path_parts = generate_all_path_parts(file_tree)\n", "    for path in path_parts:\n", "        if path:  # skip empty path\n", "            components = path.split(\"/\")\n", "            for i in range(len(components)):\n", "                path_part = \"/\".join(components[i:])\n", "                path_parts_counter[path_part] += 1\n", "    return path_parts_counter\n", "\n", "generate_all_path_parts_for_repo(file_tree)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from collections import Counter\n", "# from pathlib import Path\n", "\n", "# def generate_all_path_parts( path) -> list:\n", "#     path = Path(path)\n", "#     components = path.parts\n", "#     path_parts = []\n", "#     for i in range(len(components)):\n", "#         for j in range(i, len(components)):\n", "#             path_part = Path(*components[i:j+1]).as_posix()\n", "#             path_parts.append(path_part)\n", "#     return path_parts\n", "\n", "# def generate_all_path_parts_for_repo( files):\n", "#     path_parts_counter = Counter()\n", "#     for file in files:\n", "#         path_parts_counter.update(\n", "#             generate_all_path_parts(file[\"max_stars_repo_path\"])\n", "#         )\n", "#     return path_parts_counter\n", "\n", "def _generate_all_suffixes_for_path( path) -> list:\n", "    path = Path(path)\n", "    components = path.parts\n", "    suffixes = []\n", "    for i in range(1, len(components) + 1):\n", "        suffix = Path(*components[-i:]).as_posix()\n", "        suffixes.append(suffix)\n", "    return suffixes\n", "\n", "def _generate_all_suffixes_for_repo( files):\n", "    suffixes_counter = Counter()\n", "    for file in files:\n", "        suffixes_counter.update(\n", "            _generate_all_suffixes_for_path(file[\"max_stars_repo_path\"])\n", "        )\n", "    return suffixes_counter\n", "\n", "_generate_all_suffixes_for_repo(files)\n", "# generate_all_path_parts_for_repo(files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "counter = 0\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/show_dirs_v2.jsonl\") as f:\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/show_files_v2.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        if data[\"status\"] != \"success\":\n", "            continue\n", "        counter += 1\n", "\n", "        print(data[\"repo_uuid\"])\n", "        print()\n", "        # print(data[\"text\"])\n", "        for sample in data[\"qa_samples\"]:\n", "            print(\"QUESTION:\", sample[\"question\"])\n", "            # print(\"    DIR:\", sample[\"dir_name\"])\n", "            print(\"    PATH:\", sample[\"paths\"])\n", "            print(\"  ANSWER:\", sample[\"answer\"])\n", "            print()\n", "        print(\"\\n---\\n\")\n", "\n", "        if counter >= 10:\n", "            break\n", "\n", "# https://gist.github.com/urikz/62ff730a1e1878cf8ac03bb5f76384c8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_total, n_with_ticks = 0, 0\n", "# with open(\"/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/show_dirs_v2.jsonl\") as f:\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/show_files_v2.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        for sample in data[\"qa_samples\"]:\n", "            n_total += 1\n", "            if \"`\" in sample[\"question\"]:\n", "                n_with_ticks += 1\n", "print(n_total, n_with_ticks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "\n", "REPO_UUIDS = {\n", "    \"75a66072-9951-400d-a526-71cbd8bc33ec\",\n", "    \"2f8980f0-6d0f-40af-8abb-a059657439bc\",\n", "    \"2f632536-abe4-46ae-9c40-1a0e48378004\",\n", "    \"b0e377d1-562e-45b8-a56c-260e85be1eec\",\n", "    \"f72b5dcb-833c-4112-a69e-7d382808979e\",\n", "}\n", "\n", "REPOS_PATTERN = \"/mnt/efs/augment/user/yury/binks/binks-v4/01_raw_repos/*.zstd.parquet\"\n", "\n", "for repo in ParquetRepoDataset(REPOS_PATTERN):\n", "    if repo.id == \"a4696af3-f03b-45dd-9b55-ddf9487502a3\":\n", "        break\n", "    # if repo.id not in REPO_UUIDS:\n", "    #     continue\n", "    # files = repo.get_files()\n", "    # for file in files:\n", "    #     assert \"max_stars_repo_path\" in file\n", "\n", "print(repo.id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "def build_file_tree(files, compute_file_size_fn):\n", "    file_tree = {}\n", "    for file in files:\n", "        file_path = Path(file[\"max_stars_repo_path\"])\n", "        dir_path = file_path.parent\n", "        file_name = file_path.name\n", "        file_size = compute_file_size_fn(file)  # custom function to compute file size\n", "        file_obj = {\n", "            \"name\": file_name,\n", "            \"size\": file_size,\n", "            \"file\": file,  # store the file object itself\n", "        }\n", "        current_dir = file_tree\n", "        for dir_component in dir_path.parts:\n", "            if dir_component not in current_dir:\n", "                current_dir[dir_component] = {}\n", "            current_dir = current_dir[dir_component]\n", "        current_dir[file_name] = file_obj\n", "    return file_tree\n", "\n", "def compute_file_size(file):\n", "    return 1\n", "\n", "file_tree = build_file_tree(repo.get_files(), compute_file_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "def is_node_leaf(node):\n", "    if set(node.keys()) != {'file', 'name', 'size'}:\n", "        return False\n", "    if 'content' not in node['file'] or not isinstance(node['file']['content'], str):\n", "        return False\n", "    if 'max_stars_repo_path' not in node['file'] or not isinstance(node['file']['max_stars_repo_path'], str):\n", "        return False\n", "    return True\n", "\n", "def get_all_files_from_a_subtree(file_subtree):\n", "    files = []\n", "    for file_obj in file_subtree.values():\n", "        if is_node_leaf(file_obj):\n", "            # Leaf node\n", "            files.append(file_obj[\"file\"])\n", "        else:\n", "            files.extend(get_all_files_from_a_subtree(file_obj))\n", "    return files\n", "\n", "\n", "def get_all_files_per_directory(file_tree, dir_path):\n", "    current_dir = file_tree\n", "    dir_components = dir_path.split(os.sep)\n", "    for component in dir_components:\n", "        if component in current_dir:\n", "            current_dir = current_dir[component]\n", "        else:\n", "            return []\n", "    return get_all_files_from_a_subtree(current_dir)\n", "\n", "d = \"sdk/storage/azure-storage-file-share/src/main/java/com/azure\"\n", "files_per_d = get_all_files_per_directory(file_tree, d)\n", "\n", "files_per_d\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "dir_path = \"sdk/storage/azure-storage-file-share/src/main/java/com/azure\"\n", "current_dir = file_tree\n", "dir_components = dir_path.split(os.sep)\n", "for component in dir_components:\n", "    if component in current_dir:\n", "        current_dir = current_dir[component]\n", "    else:\n", "        assert False\n", "\n", "set(current_dir.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_dir['storage']['file']['share']['options']['ShareFileRenameOptions.java']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_dir"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_per_d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import dataclasses\n", "import glob\n", "import json\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import tqdm\n", "import re\n", "from jinja2 import Environment\n", "\n", "import experimental.yury.binks.datav3.utils as utils\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BINKS_VERSION = \"binks-v5-files-n-dirs\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "REPOS_PATTERN = \"/mnt/efs/augment/user/yury/binks/binks-v4/01_raw_repos/*.zstd.parquet\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QA_SAMPLE_PATHS = [\n", "    OUTPUT_PATH / \"show_files_v2.jsonl\",\n", "    OUTPUT_PATH / \"show_dirs_v2.jsonl\",\n", "]\n", "\n", "n_total_qa_samples = 0\n", "qa_samples_per_repo = {}\n", "\n", "\n", "for path in QA_SAMPLE_PATHS:\n", "    with open(path) as f:\n", "        for line in f:\n", "            data = json.loads(line[:-1])\n", "            if data[\"status\"] != \"success\":\n", "                continue\n", "            repo_uuid = data[\"repo_uuid\"]\n", "            if repo_uuid not in qa_samples_per_repo:\n", "                qa_samples_per_repo[repo_uuid] = []\n", "            for qa_sample in data[\"qa_samples\"]:\n", "                assert isinstance(qa_sample[\"question\"], str)\n", "                assert isinstance(qa_sample[\"answer\"], str)\n", "                assert isinstance(qa_sample[\"paths\"], list)\n", "                qa_samples_per_repo[repo_uuid].append({\n", "                    \"question\": qa_sample[\"question\"],\n", "                    \"answer\": qa_sample[\"answer\"],\n", "                    \"paths\": qa_sample[\"paths\"],\n", "                })\n", "                n_total_qa_samples += 1\n", "\n", "print(len(qa_samples_per_repo), n_total_qa_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def extract_single_tick_strings(text):\n", "    pattern = r\"`([^`]*)`\"\n", "    matches = re.findall(pattern, text)\n", "    assert len(matches) <= 1, \"Expected at most one match, got {}\".format(len(matches))\n", "    if len(matches) == 0:\n", "        return None\n", "    return matches[0]\n", "\n", "# Example usage:\n", "# text = \"Hello `world` this is a `test` string\"\n", "# print(extract_single_tick_strings(text))  # Raises AssertionError\n", "\n", "text = \"Hello `world` string\"\n", "print(extract_single_tick_strings(text))  # Output: `world`\n", "\n", "text = \"Hello string\"\n", "print(extract_single_tick_strings(text))  # Output: None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "def generate_prefix_paths(path):\n", "    path_obj = pathlib.Path(path)\n", "    prefix_paths = []\n", "    for part in range(1, len(path_obj.parts) + 1):\n", "        prefix_paths.append(str(pathlib.Path(*path_obj.parts[:part])))\n", "    return prefix_paths\n", "\n", "path = \"a/b/c.txt\"\n", "print(generate_prefix_paths(path))  # Output: ['a', 'a/b', 'a/b/c.txt']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "n_total_qa_samples, n_total_repos = 0, 0\n", "n_ticks, n_ticks_kept, n_ticks_adjusted = 0, 0, 0\n", "\n", "with (OUTPUT_PATH / \"repos_with_qa.jsonl\").open(\"w\") as f:\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(REPOS_PATTERN)):\n", "        if repo.id not in qa_samples_per_repo:\n", "            continue\n", "        all_paths_and_prefixes = set()\n", "        for file in repo.get_files():\n", "            path = file[\"max_stars_repo_path\"]\n", "            all_paths_and_prefixes.update(generate_prefix_paths(path))\n", "\n", "        fixed_qa_samples = []\n", "        for qa_sample in qa_samples_per_repo[repo.id]:\n", "            path = extract_single_tick_strings(qa_sample[\"question\"])\n", "            if path is None:\n", "                fixed_qa_samples.append(qa_sample)\n", "                continue\n", "            n_ticks += 1\n", "            if path in all_paths_and_prefixes:\n", "                n_ticks_kept += 1\n", "                fixed_qa_samples.append(qa_sample)\n", "            else:\n", "                n_ticks_adjusted += 1\n", "                fixed_qa_samples.append({\n", "                    \"question\": qa_sample[\"question\"].replace(\"`\", \"\"),\n", "                    \"answer\": qa_sample[\"answer\"],\n", "                    \"paths\": qa_sample[\"paths\"],\n", "                })\n", "\n", "        repo_d = repo.to_jsonable_dict()\n", "        repo_d['documents_with_questions'] = fixed_qa_samples\n", "\n", "        n_total_repos += 1\n", "        n_total_qa_samples += len(fixed_qa_samples)\n", "\n", "        sample_json = json.dumps(repo_d)\n", "        f.write(sample_json + \"\\n\")\n", "\n", "        if n_total_repos >= len(qa_samples_per_repo):\n", "            break\n", "\n", "\n", "print(n_total_repos, n_total_qa_samples)\n", "print(n_ticks, n_ticks_kept, n_ticks_adjusted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ready_repos = []\n", "\n", "with (OUTPUT_PATH / \"repos_with_qa.jsonl\").open(\"r\") as f:\n", "    for line in f:\n", "        ready_repos.append(json.loads(line[:-1]))\n", "print(len(ready_repos))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "random_repo_index = np.random.randint(len(ready_repos))\n", "random_index = np.random.randint(len(ready_repos[random_repo_index][\"documents_with_questions\"]))\n", "sample = ready_repos[random_repo_index][\"documents_with_questions\"][random_index]\n", "\n", "print(\"QUESTION:\", sample[\"question\"])\n", "print(\"   PATHS:\", sample[\"paths\"])\n", "print(\"  ANSWER:\", sample[\"answer\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
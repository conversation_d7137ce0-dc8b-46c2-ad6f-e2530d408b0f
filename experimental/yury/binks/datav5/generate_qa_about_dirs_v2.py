"""Script to generate answers given a repo and a set of questions."""

from collections import Counter
import os
import random
import json
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm

import experimental.yury.binks.datav3.gemini_api as gemini_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset
import sentencepiece as spm

GEMMA_TOKENIZER_PATH = "/mnt/efs/augment/user/yury/gemma/tokenizer.model"


BINKS_VERSION = "binks-v5-files-n-dirs"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)


REPOS_PATTERN = "/mnt/efs/augment/user/yury/binks/binks-v4/01_raw_repos/*.zstd.parquet"

BINKS_V1_V2_REPO_UUIDS = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3/binks_v1_v2_repo_uuids.jsonl"
)
BINKS_V1_V2_REPO_NAMES = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3/binks_v1_v2_repo_names.jsonl"
)

BINKS_V3_V4_REPO_UUIDS = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3-evals/binks_v3_v4_repo_uuids.jsonl"
)
BINKS_V3_V4_REPO_NAMES = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3-evals/binks_v3_v4_repo_names.jsonl"
)

PROJECT_ID = "system-services-dev"
LOCATION = "us-central1"
TEMPERATURE = 0.6
MAX_OUTPUT_TOKENS = 8192
GEMINI_MODEL_NAME = gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME
# GEMINI_MODEL_NAME = gemini_api.GeminiModelName.GEMINI_PRO_MODEL_NAME

TEMPERATURE = 0.7
MAX_OUTPUT_TOKENS = 1024
NUM_PROCESSES = 10
N_QUESTIONS_PER_REPO = 5
N_REPOS = 6000

ANSWERS_OUTPUT_FILE = OUTPUT_PATH / "show_dirs_v2.jsonl"


def pd_median_list(s):
    if len(s) == 0:
        return 0
    return np.median(s)


def pd_median_safe(s):
    return s.median(skipna=True)


def pd_p90_safe(s):
    return np.nanpercentile(s, 90)


def iterate_over_repos(repos_pattern, binks_v5_repo_uuids):
    """Join questions with repos."""
    with BINKS_V1_V2_REPO_UUIDS.open("r") as f:
        binks_v1_v2_repo_uuids = set(json.load(f))
    with BINKS_V1_V2_REPO_NAMES.open("r") as f:
        binks_v1_v2_repo_names = set(json.load(f))
    print(
        "Loaded %d repo uuids and %d repo names from Binks-v1 and Binks-v2"
        % (len(binks_v1_v2_repo_uuids), len(binks_v1_v2_repo_names))
    )

    with BINKS_V3_V4_REPO_UUIDS.open("r") as f:
        binks_v3_v4_repo_uuids = set(json.load(f))
    with BINKS_V3_V4_REPO_NAMES.open("r") as f:
        binks_v3_v4_repo_names = set(json.load(f))
    print(
        "Loaded %d repo uuids and %d repo names from Binks-v3 and Binks-v4"
        % (len(binks_v3_v4_repo_uuids), len(binks_v3_v4_repo_names))
    )

    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id in binks_v5_repo_uuids:
            continue
        if repo.id in binks_v1_v2_repo_uuids:
            continue
        if repo.name in binks_v1_v2_repo_names:
            continue
        if repo.id in binks_v3_v4_repo_uuids:
            continue
        if repo.name in binks_v3_v4_repo_names:
            continue
        # print(repo.id, repo.name)
        yield repo


FILE_SYSTEM_PROMPT_TEMPLATE = """\
This is file `{path}`:

```
{content}
```

"""


QUESTIONS_TEMPLATES = [
    "Can you provide a summary of the contents in the directory `{path}`?",
    "Please describe the contents of `{path}`.",
    "Give me an overview of `{path}`.",
    "What's inside the directory `{path}`?",
    "Summarize the details of `{path}` for me.",
    "Can you tell me what `{path}` contains?",
    "Explain the contents of the directory `{path}`.",
    "What information is in `{path}`?",
    "Could you break down the contents of `{path}`?",
    "Please outline the key points of the directory `{path}`.",
    "Show the contents of `{path}`.",
    "What's in the directory `{path}`?",
    "Detail `{path}`.",
    "Summary of the directory `{path}`.",
    "Explain `{path}`.",
    "Describe `{path}`.",
    "Contents of `{path}`?",
    "Summarize `{path}`.",
    "Outline the directory `{path}`.",
    "Info on `{path}`.",
    "What does the directory `{path}` include?",
    "Brief me on `{path}`.",
    "Highlight the main points of `{path}`.",
    "Give a rundown of the directory `{path}`.",
    "What's the gist of `{path}`?",
    "Can you unpack the directory `{path}`?",
    "Break down `{path}` for me.",
    "What's the essence of the directory `{path}`?",
    "Provide details on `{path}`.",
    "What are the key elements of the directory `{path}`?",
    "What are the primary functions in `{path}`?",
    "List the main classes defined in the directory `{path}`.",
    "Can you interpret the code structure of `{path}`?",
    "Outline the modules and methods in the directory `{path}`.",
    "Describe the role of `{path}` in the project.",
    "What does the directory `{path}` implement?",
    "How do the files in the directory `{path}` interact with each other?",
    "Break down the algorithms in `{path}`.",
    "What is the purpose of `{path}`?",
    "Summarize the key variables and constants in the directory `{path}`.",
    "What are the external dependencies in the directory `{path}`?",
    "List the libraries imported in `{path}`.",
    "Summarize the comments and documentation in `{path}`.",
    "How does the directory `{path}` handle errors and exceptions?",
    "Detail the configuration settings in `{path}`.",
    "Describe the logic flow in `{path}`.",
    "What data structures are utilized in `{path}`?",
    "Are there any security concerns in `{path}`?",
    "Identify refactoring opportunities in the directory `{path}`.",
    "Does `{path}` reference any other files?",
    "Analyze the complexity of `{path}`.",
    "Does `{path}` interact with external APIs?",
]


NATURAL_LANGUAGE_HINT_TEMPLATE = """\
A software engineer asks the AI assistant about the directory `{path}` using natural language instead of the exact path. How might they reference this directory path using natural language?

Write only the reference without any additional details or thoughts. Do not start with a capital letter or end with a period.
"""


ADD_TYPOS_TEMPLATE = """\
A software engineer asks the AI assistant about the directory `{path}`, but they are in a rush and make one or a few typos in the path. How did they end up writing `{path}`?

Write only the path with typos, without any additional details or thoughts.
"""


def build_file_tree(files, compute_file_size_fn):
    file_tree = {}
    for file in files:
        file_path = Path(file["max_stars_repo_path"])
        dir_path = file_path.parent
        file_name = file_path.name
        file_size = compute_file_size_fn(file)  # custom function to compute file size
        file_obj = {
            "name": file_name,
            "size": file_size,
            "file": file,  # store the file object itself
        }
        current_dir = file_tree
        for dir_component in dir_path.parts:
            if dir_component not in current_dir:
                current_dir[dir_component] = {}
            current_dir = current_dir[dir_component]
        current_dir[file_name] = file_obj
    return file_tree


def is_node_leaf(node):
    if set(node.keys()) != {"file", "name", "size"}:
        return False
    if "content" not in node["file"] or not isinstance(node["file"]["content"], str):
        return False
    if "max_stars_repo_path" not in node["file"] or not isinstance(
        node["file"]["max_stars_repo_path"], str
    ):
        return False
    return True


def compute_dir_sizes(file_tree, dir_path=""):
    dir_sizes = {}
    total_size = 0
    for file_name, file_subtree in file_tree.items():
        if is_node_leaf(file_subtree):
            total_size += file_subtree["size"]
        else:  # subdirectory
            subdir_path = os.path.join(dir_path, file_name)
            subdir_size, subdir_sizes = compute_dir_sizes(file_subtree, subdir_path)
            total_size += subdir_size  # add the total size of the subdirectory
            dir_sizes.update(subdir_sizes)
    dir_sizes[dir_path] = total_size
    return total_size, dir_sizes


def get_all_files_from_a_subtree(file_subtree):
    files = []
    for file_obj in file_subtree.values():
        if is_node_leaf(file_obj):
            # Leaf node
            files.append(file_obj["file"])
        else:
            files.extend(get_all_files_from_a_subtree(file_obj))
    return files


def get_all_files_per_directory(file_tree, dir_path):
    current_dir = file_tree
    dir_components = dir_path.split(os.sep)
    for component in dir_components:
        if component in current_dir:
            current_dir = current_dir[component]
        else:
            return []
    return get_all_files_from_a_subtree(current_dir)


def generate_all_path_parts(file_tree, current_path=""):
    path_parts = []
    for file_name, file_obj in file_tree.items():
        new_path = os.path.join(current_path, file_name)
        if "file" not in file_obj:  # subdirectory
            path_parts.append(new_path)
            path_parts.extend(generate_all_path_parts(file_obj, new_path))
    return path_parts


def generate_all_path_parts_for_repo(file_tree):
    path_parts_counter = Counter()
    path_parts = generate_all_path_parts(file_tree)
    for path in path_parts:
        if path:  # skip empty path
            components = path.split("/")
            for i in range(len(components)):
                path_part = "/".join(components[i:])
                path_parts_counter[path_part] += 1
    return path_parts_counter


class GenerateQAProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, n_questions_per_repo):
        self.n_questions_per_repo = n_questions_per_repo

    def initialize(self):
        GenerateQAProcessor.model = gemini_api.GeminiAPI(
            PROJECT_ID,
            LOCATION,
            GEMINI_MODEL_NAME,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        GenerateQAProcessor.tokenizer = spm.SentencePieceProcessor()
        GenerateQAProcessor.tokenizer.Load(GEMMA_TOKENIZER_PATH)
        print("Initialized processor")

    def format_input(self, dir_name, files):
        prompt = (
            f"Below is the full content of the directory `{dir_name}`. "
            "You'll be asked a question that requires summarizing the content of this directory. "
            "Please read the content carefully."
        )

        for file in files:
            if "max_stars_repo_path" not in file:
                import pdb

                pdb.set_trace()
                raise ValueError(
                    "Missing max_stars_repo_path in file %s, %s, %s"
                    % (str(file), str(file.keys()), dir_name)
                )
            prompt += "\n\n" + FILE_SYSTEM_PROMPT_TEMPLATE.format(
                path=file["max_stars_repo_path"],
                content=file["content"],
            )

        question_template = np.random.choice(QUESTIONS_TEMPLATES)
        prompt += (
            "---\n\n"
            + question_template.format(path=dir_name)
            + "\n\nCite individual files in your answer using the FULL paths."
        )

        return prompt, question_template

    def replace_with_natural_language_hint(self, path):
        message = NATURAL_LANGUAGE_HINT_TEMPLATE.format(
            path=path,
        )
        response = GenerateQAProcessor.model.generate_response(message)
        if response["status"] == "success":
            return response["text"].strip()
        else:
            return path

    def add_typos(self, path):
        message = ADD_TYPOS_TEMPLATE.format(
            path=path,
        )
        response = GenerateQAProcessor.model.generate_response(message)
        if response["status"] == "success":
            return response["text"].strip()
        else:
            return path

    def replace_with_partial_path(self, path, suffixes_counter):
        coin_flip = random.random()

        if coin_flip < 0.7:
            # in 70% of cases we don't do anything
            return path

        components = Path(path).parts

        min_components_to_include = 1
        while min_components_to_include < len(components):
            suffix = Path(*components[-min_components_to_include:]).as_posix()
            if suffixes_counter[suffix] > 1:
                min_components_to_include += 1
                continue
            else:
                break

        # sanity check
        suffix = Path(*components[-min_components_to_include:]).as_posix()
        assert suffixes_counter[suffix] == 1, path

        if coin_flip < 0.9:
            # in another 20% we take the minimal suffix
            return suffix

        n_components_to_include = random.randint(
            min_components_to_include, len(components) + 1
        )
        # sanity check
        suffix = Path(*components[-n_components_to_include:]).as_posix()
        assert suffixes_counter[suffix] == 1
        return suffix

    def postprocess_path(self, path, suffixes_counter):
        path = self.replace_with_partial_path(path, suffixes_counter)
        coin_flip = random.random()
        if coin_flip < 0.25:
            # in 25% of cases we replace the path with natural language hint
            path = self.replace_with_natural_language_hint(path)
        elif coin_flip < 0.5:
            # in another 25% we add typos
            path = self.add_typos(path)
        return path

    def postprocess_question(self, question, force_remove_ticks):
        if force_remove_ticks or random.random() < 0.25:
            question = question.replace("`", "")
        if random.random() < 0.5:
            question = question.rstrip(".?")
        return question

    def generate_qa_sample(self, dir_name, files, suffixes_counter):
        prompt, question_template = self.format_input(dir_name, files)
        response = GenerateQAProcessor.model.generate_response(prompt)

        new_path = self.postprocess_path(dir_name, suffixes_counter)
        new_question = question_template.format(path=new_path)
        force_remove_ticks = new_path != dir_name
        new_question = self.postprocess_question(
            new_question,
            force_remove_ticks,
        )

        response["original_question"] = question_template.format(path=dir_name)
        response["dir_name"] = dir_name
        response["question"] = new_question
        response["paths"] = [file["max_stars_repo_path"] for file in files]
        return response

    def __call__(self, repo):
        # Sample self.n_questions_per_repo different files from the repo

        response = {
            "repo_uuid": repo.id,
            "cost": 0,
            "n_qa_samples": 0,
            "qa_samples": [],
            "n_prompt_tokens": [],
            "n_output_tokens": [],
        }

        def compute_file_size(file):
            return len(GenerateQAProcessor.tokenizer.tokenize(file["content"]))

        file_tree = build_file_tree(repo.get_files(), compute_file_size)
        _, dir_sizes = compute_dir_sizes(file_tree)
        suffixes_counter = generate_all_path_parts_for_repo(file_tree)

        candidate_dirs = [
            dir_path
            for dir_path, size in dir_sizes.items()
            if size < 64000 and suffixes_counter[dir_path] == 1
        ]

        sampled_dirs = random.sample(
            candidate_dirs, min(len(candidate_dirs), self.n_questions_per_repo)
        )

        for d in sampled_dirs:
            files_per_d = get_all_files_per_directory(file_tree, d)
            response_per_file = self.generate_qa_sample(
                d, files_per_d, suffixes_counter
            )
            if response_per_file["status"] == "success":
                response["qa_samples"].append(
                    {
                        "question": response_per_file["question"],
                        "answer": response_per_file["text"],
                        "paths": response_per_file["paths"],
                        "original_question": response_per_file["original_question"],
                        "dir_name": response_per_file["dir_name"],
                    }
                )
                response["cost"] += response_per_file["cost"]
                response["n_qa_samples"] += 1
                response["n_prompt_tokens"].append(response_per_file["n_prompt_tokens"])
                response["n_output_tokens"].append(response_per_file["n_output_tokens"])

        response["n_prompt_tokens"] = pd_median_list(response["n_prompt_tokens"])
        response["n_output_tokens"] = pd_median_list(response["n_output_tokens"])

        if len(response["qa_samples"]) == 0:
            response["status"] = "failed"
        else:
            response["status"] = "success"

        return response


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        columns = [
            "status",
            "n_qa_samples",
            "n_prompt_tokens",
            "n_output_tokens",
            "cost",
        ]
        new_stats = {k: response[k] or np.nan for k in columns if k in response}
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "cost": "sum",
            "n_qa_samples": "sum",
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        if "n_prompt_tokens" in self.stats.columns:
            d["n_prompt_tokens_p90"] = pd_p90_safe(self.stats["n_prompt_tokens"])
        if "n_output_tokens" in self.stats.columns:
            d["n_output_tokens_p90"] = pd_p90_safe(self.stats["n_output_tokens"])
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == status).sum()
        else:
            return len(self.stats)


processor = GenerateQAProcessor(N_QUESTIONS_PER_REPO)
stats = GenerationStats()

binks_v5_repo_uuids = set()
if ANSWERS_OUTPUT_FILE.exists():
    n_completed_repos = 0
    for line in ANSWERS_OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        if response["status"] != "success":
            continue
        repo_uuid = response["repo_uuid"]
        binks_v5_repo_uuids.add(repo_uuid)
        stats.add_response(response)
        n_completed_repos += 1
    print(f"Loaded {n_completed_repos} repos already completed")

pbar = tqdm.tqdm(
    total=N_REPOS,
    desc="Gen answers",
)
pbar.update(stats.size("success"))
if stats.size("success") > 0:
    pbar.set_postfix(stats.to_dict())

with ANSWERS_OUTPUT_FILE.open("a") as f:
    for response in utils.maybe_run_in_multiple_processes(
        processor,
        iterate_over_repos(REPOS_PATTERN, binks_v5_repo_uuids),
        num_processes=NUM_PROCESSES,
    ):
        stats.add_response(response)
        f.write(json.dumps(response) + "\n")
        f.flush()
        if response["status"] == "success":
            pbar.update()
        pbar.set_postfix(stats.to_dict())
        if stats.size("success") >= N_REPOS:
            break

print("Done")

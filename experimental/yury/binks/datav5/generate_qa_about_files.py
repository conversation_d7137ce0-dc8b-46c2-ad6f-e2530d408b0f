"""<PERSON><PERSON>t to generate answers given a repo and a set of questions."""

from collections import Counter
import dataclasses
import glob
import random
import json
from pathlib import Path

import numpy as np
import pandas as pd
import tqdm
import re
from jinja2 import Environment

import experimental.yury.binks.datav5.anthropic_api as anthropic_api
import experimental.yury.binks.datav3.utils as utils
from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset


BINKS_VERSION = "binks-v5-files-n-dirs"
BASE_PATH = Path("/mnt/efs/augment/user/yury/binks/")
OUTPUT_PATH = BASE_PATH / BINKS_VERSION
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)


REPOS_PATTERN = "/mnt/efs/augment/user/yury/binks/binks-v4/01_raw_repos/*.zstd.parquet"

BINKS_V1_V2_REPO_UUIDS = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3/binks_v1_v2_repo_uuids.jsonl"
)
BINKS_V1_V2_REPO_NAMES = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3/binks_v1_v2_repo_names.jsonl"
)

BINKS_V3_V4_REPO_UUIDS = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3-evals/binks_v3_v4_repo_uuids.jsonl"
)
BINKS_V3_V4_REPO_NAMES = Path(
    "/mnt/efs/augment/user/yury/binks/binks-v3-evals/binks_v3_v4_repo_names.jsonl"
)

ANTHROPIC_MODEL = "claude-3-5-sonnet-20240620"
TEMPERATURE = 0.5
MAX_OUTPUT_TOKENS = 1024
NUM_PROCESSES = 1
N_QUESTIONS_PER_REPO = 5
N_REPOS = 6000

ANSWERS_OUTPUT_FILE = OUTPUT_PATH / "show_files.jsonl"


def pd_median_list(s):
    if len(s) == 0:
        return 0
    return np.median(s)


def pd_median_safe(s):
    return s.median(skipna=True)


def pd_p90_safe(s):
    return np.nanpercentile(s, 90)


def iterate_over_repos(repos_pattern, binks_v5_repo_uuids):
    """Join questions with repos."""
    with BINKS_V1_V2_REPO_UUIDS.open("r") as f:
        binks_v1_v2_repo_uuids = set(json.load(f))
    with BINKS_V1_V2_REPO_NAMES.open("r") as f:
        binks_v1_v2_repo_names = set(json.load(f))
    print(
        "Loaded %d repo uuids and %d repo names from Binks-v1 and Binks-v2"
        % (len(binks_v1_v2_repo_uuids), len(binks_v1_v2_repo_names))
    )

    with BINKS_V3_V4_REPO_UUIDS.open("r") as f:
        binks_v3_v4_repo_uuids = set(json.load(f))
    with BINKS_V3_V4_REPO_NAMES.open("r") as f:
        binks_v3_v4_repo_names = set(json.load(f))
    print(
        "Loaded %d repo uuids and %d repo names from Binks-v3 and Binks-v4"
        % (len(binks_v3_v4_repo_uuids), len(binks_v3_v4_repo_names))
    )

    for repo in ParquetRepoDataset(repos_pattern):
        if repo.id in binks_v5_repo_uuids:
            continue
        if repo.id in binks_v1_v2_repo_uuids:
            continue
        if repo.name in binks_v1_v2_repo_names:
            continue
        if repo.id in binks_v3_v4_repo_uuids:
            continue
        if repo.name in binks_v3_v4_repo_names:
            continue
        # print(repo.id, repo.name)
        yield repo


FILE_SYSTEM_PROMPT_TEMPLATE = """\
This is file `{path}`:

```
{content}
```

"""


QUESTIONS_TEMPLATES = [
    "Can you provide a summary of `{path}`?",
    "Please describe the content of `{path}`.",
    "Give me an overview of `{path}`.",
    "What's inside `{path}`?",
    "Summarize the details of `{path}` for me.",
    "Can you tell me what `{path}` contains?",
    "Explain the content of `{path}`.",
    "What information is in `{path}`?",
    "Could you break down the contents of `{path}`?",
    "Please outline the key points of `{path}`.",
    "Show `{path}`.",
    "What's in `{path}`?",
    "Detail `{path}`.",
    "File summary: `{path}`.",
    "Explain `{path}`.",
    "Describe `{path}`.",
    "Contents of `{path}`?",
    "Summarize `{path}`.",
    "Outline `{path}`.",
    "Info on `{path}`.",
    "What does `{path}` include?",
    "Brief me on `{path}`.",
    "Highlight the main points of `{path}`.",
    "Give a rundown of `{path}`.",
    "What's the gist of `{path}`?",
    "Can you unpack `{path}`?",
    "Break down `{path}` for me.",
    "What's the essence of `{path}`?",
    "Provide details on `{path}`.",
    "What are the key elements of `{path}`?",
    "What are the primary functions in `{path}`?",
    "List the main classes defined in `{path}`.",
    "Can you interpret the code structure of `{path}`?",
    "Outline the modules and methods in `{path}`.",
    "Describe the role of `{path}` in the project.",
    "What does `{path}` implement?",
    "How does `{path}` interact with other files?",
    "Break down the algorithms in `{path}`.",
    "What is the purpose of `{path}`?",
    "Summarize the key variables and constants in `{path}`.",
    "What are the external dependencies in `{path}`?",
    "List the libraries imported in `{path}`.",
    "Summarize the comments and documentation in `{path}`.",
    "How does `{path}` handle errors and exceptions?",
    "Detail the configuration settings in `{path}`.",
    "What are the testing and debugging steps mentioned in `{path}`?",
    "Are there any performance considerations in `{path}`?",
    "Describe the logic flow in `{path}`.",
    "What data structures are utilized in `{path}`?",
    "Are there any security concerns in `{path}`?",
    "Identify refactoring opportunities in `{path}`.",
    "What are the recent changes made to `{path}`?",
    "Does `{path}` reference any other files?",
    "Is `{path}` compliant with coding standards?",
    "Analyze the complexity of `{path}`.",
    "Does `{path}` interact with external APIs?",
]


NATURAL_LANGUAGE_HINT_TEMPLATE = """\
A software engineer asks the AI assistant about the file `{path}` using natural language instead of the exact path. How might they reference this path using natural language?

Write only the reference without any additional details or thoughts.
"""


class GenerateQAProcessor(utils.AbstractProcessor):
    """Processor to generate answers given a repo and a set of questions."""

    def __init__(self, n_questions_per_repo):
        self.n_questions_per_repo = n_questions_per_repo

    def initialize(self):
        GenerateQAProcessor.model = anthropic_api.AntrophicAPI(
            ANTHROPIC_MODEL,
            TEMPERATURE,
            MAX_OUTPUT_TOKENS,
        )
        print("Initialized processor")

    def format_input(self, file):
        system_prompt = FILE_SYSTEM_PROMPT_TEMPLATE.format(
            path=file["max_stars_repo_path"],
            content=file["content"],
        )
        question_template = np.random.choice(QUESTIONS_TEMPLATES)
        question = question_template.format(path=file["max_stars_repo_path"])
        return system_prompt, question_template, question

    def replace_with_with_natural_language_hint(self, path):
        message = NATURAL_LANGUAGE_HINT_TEMPLATE.format(
            path=path,
        )
        response = GenerateQAProcessor.model.generate_response(None, message, 1.0)
        return response["text"]

    def replace_with_partial_path(self, path, suffixes_counter):
        coin_flip = random.random()

        if coin_flip < 0.3:
            # in 30% of cases we don't do anything
            return path

        components = Path(path).parts

        min_components_to_include = 1
        while min_components_to_include < len(components):
            suffix = Path(*components[-min_components_to_include:]).as_posix()
            if suffixes_counter[suffix] > 1:
                min_components_to_include += 1
                continue
            else:
                break

        # sanity check
        suffix = Path(*components[-min_components_to_include:]).as_posix()
        assert suffixes_counter[suffix] == 1, path

        if coin_flip < 0.6:
            # in another 30% we take the minimal suffix
            return suffix

        n_components_to_include = random.randint(
            min_components_to_include, len(components) + 1
        )
        # sanity check
        suffix = Path(*components[-n_components_to_include:]).as_posix()
        assert suffixes_counter[suffix] == 1
        return suffix

    def postprocess_path(self, path, suffixes_counter):
        path = self.replace_with_partial_path(path, suffixes_counter)
        if random.random() < 0.3:
            # in 30% of cases we replace the path with natural language hint
            path = self.replace_with_with_natural_language_hint(path)
        return path

    def postprocess_question(self, question):
        if random.random() < 0.5:
            question = question.replace("`", "")
        if random.random() < 0.5:
            question = question.rstrip(".?")
        if random.random() < 0.5:
            question = question.lower()
        return question

    def generate_qa_sample(self, file, suffixes_counter):
        system_prompt, question_template, question = self.format_input(file)
        response = GenerateQAProcessor.model.generate_response(system_prompt, question)

        new_path = self.postprocess_path(file["max_stars_repo_path"], suffixes_counter)
        new_question = question_template.format(path=new_path)
        new_question = self.postprocess_question(new_question)

        response["question"] = new_question
        response["paths"] = [file["max_stars_repo_path"]]
        return response

    def generate_all_suffixes_for_path(self, path) -> list:
        path = Path(path)
        components = path.parts
        suffixes = []
        for i in range(1, len(components) + 1):
            suffix = Path(*components[-i:]).as_posix()
            suffixes.append(suffix)
        return suffixes

    def generate_all_suffixes_for_repo(self, files):
        suffixes_counter = Counter()
        for file in files:
            suffixes_counter.update(
                self.generate_all_suffixes_for_path(file["max_stars_repo_path"])
            )
        return suffixes_counter

    def __call__(self, repo):
        # Sample self.n_questions_per_repo different files from the repo

        response = {
            "repo_uuid": repo.id,
            "cost": 0,
            "n_qa_samples": 0,
            "qa_samples": [],
            "n_input_tokens": [],
            "n_output_tokens": [],
        }

        sampled_files = random.sample(repo.get_files(), self.n_questions_per_repo)

        suffixes_counter = self.generate_all_suffixes_for_repo(repo.get_files())

        # Filter out samples with non-unique files paths
        sampled_files = [
            file
            for file in sampled_files
            if suffixes_counter[file["max_stars_repo_path"]] == 1
        ]

        for file in sampled_files:
            response_per_file = self.generate_qa_sample(file, suffixes_counter)
            if response_per_file["status"] == "success":
                response["qa_samples"].append(
                    {
                        "question": response_per_file["question"],
                        "answer": response_per_file["text"],
                        "paths": response_per_file["paths"],
                    }
                )
                response["cost"] += response_per_file["cost"]
                response["n_qa_samples"] += 1
                response["n_input_tokens"].append(response_per_file["n_input_tokens"])
                response["n_output_tokens"].append(response_per_file["n_output_tokens"])

        response["n_input_tokens"] = pd_median_list(response["n_input_tokens"])
        response["n_output_tokens"] = pd_median_list(response["n_output_tokens"])

        if len(response["qa_samples"]) == 0:
            response["status"] = "failed"
        else:
            response["status"] = "success"

        return response


class GenerationStats:
    def __init__(self):
        self.stats = pd.DataFrame()

    def add_response(self, response):
        columns = [
            "status",
            "n_qa_samples",
            "n_prompt_tokens",
            "n_output_tokens",
            "cost",
        ]
        new_stats = {k: response[k] or np.nan for k in columns if k in response}
        new_stats = pd.DataFrame([new_stats])
        self.stats = pd.concat([self.stats, new_stats])

    def to_dict(self):
        d = {}
        d.update(self.stats.groupby("status").size().to_dict())
        columns_agg = {
            "cost": "sum",
            "n_qa_samples": "sum",
        }
        columns_agg = {k: v for k, v in columns_agg.items() if k in self.stats.columns}
        if len(columns_agg) > 0:
            d.update(self.stats.agg(columns_agg).to_dict())
        if "n_prompt_tokens" in self.stats.columns:
            d["n_prompt_tokens_p90"] = pd_p90_safe(self.stats["n_prompt_tokens"])
        if "n_output_tokens" in self.stats.columns:
            d["n_output_tokens_p90"] = pd_p90_safe(self.stats["n_output_tokens"])
        return d

    def size(self, status=None):
        if len(self.stats) == 0:
            return 0
        if status is not None:
            return (self.stats["status"] == status).sum()
        else:
            return len(self.stats)


processor = GenerateQAProcessor(N_QUESTIONS_PER_REPO)
stats = GenerationStats()

binks_v5_repo_uuids = set()
if ANSWERS_OUTPUT_FILE.exists():
    n_completed_repos = 0
    for line in ANSWERS_OUTPUT_FILE.open("r"):
        response = json.loads(line[:-1])
        if response["status"] != "success":
            continue
        repo_uuid = response["repo_uuid"]
        binks_v5_repo_uuids.add(repo_uuid)
        stats.add_response(response)
        n_completed_repos += 1
    print(f"Loaded {n_completed_repos} repos already completed")

pbar = tqdm.tqdm(
    total=N_REPOS,
    desc="Gen answers",
)
pbar.update(stats.size("success"))
if stats.size("success") > 0:
    pbar.set_postfix(stats.to_dict())

with ANSWERS_OUTPUT_FILE.open("a") as f:
    for response in utils.maybe_run_in_multiple_processes(
        processor,
        iterate_over_repos(REPOS_PATTERN, binks_v5_repo_uuids),
        num_processes=NUM_PROCESSES,
    ):
        stats.add_response(response)
        f.write(json.dumps(response) + "\n")
        f.flush()
        if response["status"] == "success":
            pbar.update()
        pbar.set_postfix(stats.to_dict())
        if stats.size("success") >= N_REPOS:
            break

print("Done")

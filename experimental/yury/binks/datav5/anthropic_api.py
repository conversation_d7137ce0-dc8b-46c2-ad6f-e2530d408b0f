import os
import re

# !pip install anthropic
import anthropic

from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer


ANTHROPIC_MODELS_CONFIG = {
    "claude-3-5-sonnet-20240620": {
        "input_cost_per_1m_tokens": 3.0,
        "output_cost_per_1m_tokens": 15.0,
        "max_input_tokens": 190_000,
    }
}


class AntrophicAPI:
    """A class to interact with the Anthropic API for generating responses."""

    def __init__(self, model_name, temperature, max_output_tokens):
        """Initialize the Anthropic with project details and model parameters."""
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.initialize()

    def initialize(self):
        """Initialize the Anthropic API and set up the model for generating responses."""
        ANTHROPIC_API_KEY = os.environ.get("ANTHROPIC_API_KEY")
        if ANTHROPIC_API_KEY is None:
            raise ValueError("ANTHROPIC_API_KEY environment variable is not set")

        self.config = ANTHROPIC_MODELS_CONFIG[self.model_name]
        self.client = anthropic.Anthropic(
            api_key=ANTHROPIC_API_KEY,
        )
        self.tokenizer = DeepSeekCoderInstructTokenizer()

    def _compute_cost(self, n_input_tokens, n_output_tokens):
        return (
            self.config["input_cost_per_1m_tokens"] * n_input_tokens / 1000_000.0
            + self.config["output_cost_per_1m_tokens"] * n_output_tokens / 1000_000.0
        )

    def generate_response(self, system_prompt, message, temperature=None):
        """Generate a response based on the given message."""
        response = {}
        response["n_est_prompt_tokens"] = len(self.tokenizer.tokenize_safe(message))
        if system_prompt is not None:
            response["n_est_prompt_tokens"] += len(
                self.tokenizer.tokenize_safe(system_prompt)
            )
        if response["n_est_prompt_tokens"] > self.config["max_input_tokens"]:
            response.update(
                {
                    "text": None,
                    "status": "est_prompt_too_long",
                    "cost": 0,
                }
            )
            return response

        if system_prompt is None:
            system_prompt = anthropic.NOT_GIVEN
        if temperature is None:
            temperature = self.temperature

        try:
            api_response = self.client.messages.create(
                model=self.model_name,
                system=system_prompt,
                max_tokens=self.max_output_tokens,
                temperature=temperature,
                messages=[
                    {"role": "user", "content": message},
                ],
            )
            assert len(api_response.content) == 1

            response.update(
                {
                    "text": api_response.content[0].text,
                    "status": "success",
                    "n_input_tokens": api_response.usage.input_tokens,
                    "n_output_tokens": api_response.usage.output_tokens,
                    "cost": self._compute_cost(
                        api_response.usage.input_tokens,
                        api_response.usage.output_tokens,
                    ),
                }
            )
            return response
        except anthropic.BadRequestError as e:
            if "prompt is too long" in str(e):
                response.update(
                    {
                        "text": None,
                        "status": "prompt_too_long",
                        "cost": 0,
                    }
                )
                return response
            if "Output blocked by content filtering policy" in str(e):
                response.update(
                    {
                        "text": None,
                        "status": "safety_failed",
                        "cost": 0,
                    }
                )
                return response
            print(e, flush=True)
            import pdb

            pdb.set_trace()
            raise e
        except Exception as e:
            print(e, flush=True)
            import pdb

            pdb.set_trace()
            raise e

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    Prompt<PERSON><PERSON><PERSON>,\n", ")\n", "\n", "def parse_row(row: bigquery.Row) -> ChatPromptInput:\n", "    parsed_retrieved_chunks = []\n", "\n", "    for retrieved_chunk in row['request_json']['retrieved_chunks']:\n", "        if 'char_offset' in retrieved_chunk:\n", "            char_offset = retrieved_chunk['char_offset']\n", "        else:\n", "            char_offset = 0\n", "\n", "        if 'text' not in retrieved_chunk:\n", "            # Empty chunk\n", "            continue\n", "\n", "        parsed_retrieved_chunks.append(PromptChunk(\n", "            text=retrieved_chunk['text'],\n", "            path=retrieved_chunk['path'],\n", "            char_start=char_offset,\n", "            char_end=retrieved_chunk['char_end'],\n", "            blob_name=retrieved_chunk['blob_name'],\n", "            origin=retrieved_chunk['origin'],\n", "        ))\n", "\n", "    request_json = row['request_json']['request']\n", "\n", "    if 'prefix' in request_json:\n", "        prefix = request_json['prefix']\n", "    else:\n", "        prefix = ''\n", "\n", "    if 'prefix_begin' in request_json:\n", "        prefix_begin = request_json['prefix_begin']\n", "    else:\n", "        prefix_begin = 0\n", "\n", "    if 'suffix' in request_json:\n", "        suffix = request_json['suffix']\n", "    else:\n", "        suffix = ''\n", "\n", "    if 'suffix_end' in request_json:\n", "        suffix_end = request_json['suffix_end']\n", "    else:\n", "        suffix_end = 0\n", "\n", "\n", "    if 'selected_code' in request_json and len(request_json['selected_code']) > 0:\n", "        selected_code = request_json['selected_code']\n", "        if selected_code.endswith('\\n'):\n", "            selected_code = selected_code[:-1]\n", "    else:\n", "        selected_code = ''\n", "\n", "    return ChatPromptInput(\n", "        path=request_json['path'],\n", "        prefix=prefix,\n", "        selected_code=selected_code,\n", "        suffix=suffix,\n", "        message=request_json['message'],\n", "        # chat_history=request_json.get('chat_history', []),\n", "        chat_history=[],\n", "        prefix_begin=prefix_begin,\n", "        suffix_end=suffix_end,\n", "        retrieved_chunks=parsed_retrieved_chunks,\n", "    )\n", "\n", "# retrievals AS (\n", "#     SELECT request_id, raw_json AS retrieval_json FROM data WHERE event_type = 'retrieval_response'\n", "# ),\n", "# JOIN retrievals ON requests.request_id = retrievals.request_id\n", "\n", "# @utils.persistent_cache(\"/mnt/efs/augment/user/yury/binks/llama3/download_request.jsonl\")\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/binks/llama3/download_request_with_response.jsonl\")\n", "def download_request(request_id: str):\n", "    query = f\"\"\"WITH\n", "        data AS (\n", "            SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}'\n", "        ),\n", "        requests AS (\n", "            SELECT request_id, raw_json AS request_json FROM data WHERE event_type = 'chat_host_request'\n", "        ),\n", "        responses AS (\n", "            SELECT request_id, raw_json AS response_json FROM data WHERE event_type = 'chat_host_response'\n", "        )\n", "    SELECT requests.request_id, requests.request_json, responses.response_json\n", "    FROM requests\n", "    JOIN responses ON requests.request_id = responses.request_id\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(\n", "        project=tenant.project_id, credentials=gcp_creds\n", "    )\n", "\n", "    print('sending request to big query')\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    if len(rows) == 0:\n", "        return None\n", "    assert len(rows) == 1, len(rows)\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.request_json,\n", "        \"response_json\": row.response_json,\n", "    }\n", "    return row\n", "\n", "def get_request_inputs_and_response(request_id: str):\n", "    chat_input_raw = download_request(request_id)\n", "    if chat_input_raw is None:\n", "        return None, None\n", "    response = chat_input_raw[\"response_json\"][\"response\"][\"text\"]\n", "    return parse_row(chat_input_raw), response\n", "\n", "# chat_input, response = get_request_inputs_and_response(\"b66302dc-11a7-4f10-8e3e-5e2dfb76a765\")\n", "# chat_input, response = get_request_inputs_and_response(\"1d038dad-4308-4ebb-8e33-48a14fcd9e3c\") # Marcus\n", "# chat_input, response = get_request_inputs_and_response(\"17a68644-e44b-4279-8b3a-8b266507f20f\") # Luke 1\n", "# chat_input, response = get_request_inputs_and_response(\"cdee6acb-9c65-4420-a611-b5f5c7813c5c\") # Luke 2\n", "chat_input, response = get_request_inputs_and_response(\"c4a36fce-8cf7-4d14-9ce8-0205d189da5c\") # Pranay\n", "\n", "chat_input, response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base import tokenizers\n", "from base.prompt_format_chat.binks_llama3_prompt_formatter import (\n", "    BinksLlama3ChatPromptFormatter,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer\n", "\n", "\n", "# Same as in services/deploy/binks_llama3_70B_FP8_chat_ug_chatanol1-16-3_deploy.jsonnet\n", "token_apportionment_with_retrieval = ChatTokenApportionment(\n", "    prefix_len=1024,\n", "    suffix_len=1024,\n", "    path_len=256,\n", "    message_len=0,  # Not used by the binks_llama3 prompt formatter\n", "    selected_code_len=0,  # Not used by the binks_llama3 prompt formatter\n", "    chat_history_len=2048,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=8192 - 2048,  # the last 2048 reserved for output tokens\n", ")\n", "\n", "# max_context_length: 8192,  // Total length of prompt and generation\n", "# max_output_length: 2048,  // Max number of generated tokens\n", "\n", "tokenizer = Llama3InstructTokenizer()\n", "prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment_with_retrieval)\n", "\n", "prompt_output = prompter.format_prompt(chat_input)\n", "prompt = tokenizer.detokenize(prompt_output.tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "]\n", "\n", "PORTS = [\"8000\"]\n", "\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "EOD = tokenizer.special_tokens.eod_token\n", "\n", "class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.url = url\n", "\n", "    def generate(self, prompt: str):\n", "        payload = self.get_payload(prompt)\n", "        response_json = self.send_request(payload)\n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, text):\n", "        payload = {\n", "            \"text_input\": text,\n", "            \"max_tokens\": 2048,\n", "            \"end_id\": EOD,\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": 31415,\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json\n", "\n", "client = TritonClient(ALL_URLS[0])\n", "\n", "print(client.generate(prompt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
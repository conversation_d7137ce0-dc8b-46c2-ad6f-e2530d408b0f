{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    Prompt<PERSON><PERSON><PERSON>,\n", ")\n", "\n", "def parse_row(row: bigquery.Row) -> ChatPromptInput:\n", "    parsed_retrieved_chunks = []\n", "\n", "    for retrieved_chunk in row['request_json']['retrieved_chunks']:\n", "        if 'char_offset' in retrieved_chunk:\n", "            char_offset = retrieved_chunk['char_offset']\n", "        else:\n", "            char_offset = 0\n", "        \n", "        if 'text' not in retrieved_chunk:\n", "            # Empty chunk\n", "            continue\n", "\n", "        parsed_retrieved_chunks.append(PromptChunk(\n", "            text=retrieved_chunk['text'],\n", "            path=retrieved_chunk['path'],\n", "            char_start=char_offset,\n", "            char_end=retrieved_chunk['char_end'],\n", "            blob_name=retrieved_chunk['blob_name'],\n", "            origin=retrieved_chunk['origin'],\n", "        ))\n", "\n", "    request_json = row['request_json']['request']\n", "\n", "    if 'prefix' in request_json:\n", "        prefix = request_json['prefix']        \n", "    else:\n", "        prefix = ''\n", "    \n", "    if 'prefix_begin' in request_json:\n", "        prefix_begin = request_json['prefix_begin']\n", "    else:\n", "        prefix_begin = 0\n", "\n", "    if 'suffix' in request_json:\n", "        suffix = request_json['suffix']\n", "    else:\n", "        suffix = ''\n", "\n", "    if 'suffix_end' in request_json:\n", "        suffix_end = request_json['suffix_end']\n", "    else:\n", "        suffix_end = 0\n", "\n", "\n", "    if 'selected_code' in request_json and len(request_json['selected_code']) > 0:\n", "        selected_code = request_json['selected_code']\n", "        if selected_code.endswith('\\n'):\n", "            selected_code = selected_code[:-1]\n", "    else:\n", "        selected_code = ''\n", "\n", "    return ChatPromptInput(\n", "        path=request_json['path'],\n", "        prefix=prefix,\n", "        selected_code=selected_code,\n", "        suffix=suffix,\n", "        message=request_json['message'],\n", "        chat_history=request_json.get('chat_history', []),\n", "        prefix_begin=prefix_begin,\n", "        suffix_end=suffix_end,\n", "        retrieved_chunks=parsed_retrieved_chunks,\n", "    )\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/binks/llama3/download_request.jsonl\")\n", "def download_request(request_id: str):\n", "    query = f\"\"\"WITH\n", "        data AS (\n", "            SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}'\n", "        ),\n", "        requests AS (\n", "            SELECT request_id, raw_json AS request_json FROM data WHERE event_type = 'chat_host_request'\n", "        ),\n", "        retrievals AS (\n", "            SELECT request_id, raw_json AS retrieval_json FROM data WHERE event_type = 'retrieval_response'\n", "        )\n", "    SELECT requests.request_id, requests.request_json, retrievals.retrieval_json\n", "    FROM requests JOIN retrievals ON requests.request_id = retrievals.request_id\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(\n", "        project=tenant.project_id, credentials=gcp_creds\n", "    )\n", "\n", "    print('sending request to big query')\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.request_json,\n", "        \"retrieval_json\": row.retrieval_json,\n", "    }\n", "    return row\n", "\n", "def get_request_inputs(request_id: str):\n", "    return parse_row(download_request(request_id))\n", "\n", "CHAT_REQUEST_ID = \"bed7cbf6-9770-407a-94e8-d24e1c8053be\"  # Eric example with fll selected file\n", "CHAT_REQUEST_ID = \"48e2c7f5-f6f5-4cde-9076-093d3dfb6717\"  # My example with LOL => JS\n", "CHAT_REQUEST_ID = \"d4ec9a1c-1c1e-49bc-bc55-24eaa8c4b859\"  # My example for reverse comments where there's also a chat history\n", "CHAT_REQUEST_ID = \"7a782fda-60fc-4609-87ba-6db1d4d2e072\"  # Example where existing model didn't respect indentation\n", "\n", "CHAT_REQUEST_ID = \"59a539f4-9c89-4e65-ad44-5d5ab0d81192\"  # My example of misbalanced `` ticks\n", "\n", "# CHAT_REQUEST_ID = \"b66302dc-11a7-4f10-8e3e-5e2dfb76a765\"\n", "# CHAT_REQUEST_ID = \"b254d523-49fa-4cad-8c40-6a5dd0d7bc6c\"\n", "# CHAT_REQUEST_ID = \"f7b956e5-3545-4c0c-b041-cf583fcacd72\"\n", "# CHAT_REQUEST_ID = \"6686cceb-5541-4792-b5a9-5feb5942e206\"\n", "\n", "chat_input = get_request_inputs(CHAT_REQUEST_ID)\n", "chat_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment, Template\n", "\n", "from base.prompt_format.util import head_n, trailing_n\n", "from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer\n", "tokenizer = Llama3InstructTokenizer()\n", "\n", "TEMPLATE_FILE = \"/home/<USER>/augment/experimental/yury/binks/llama_prompt_formatter/llama3_with_retrieval_template.txt\"\n", "\n", "MAX_TOTAL_TOKENS = 6144\n", "MAX_PREFIX_SUFFIX_TOKENS = 1024\n", "\n", "with open(TEMPLATE_FILE) as f:\n", "    template = f.read()\n", "\n", "# It's possible to simply instanciate the Template via `Template(template)`.\n", "# However, jinja2 will strip the trailing newline, which is annoying.\n", "# The way to prevent that is by explicitly passing the keep_trailing_newline flag\n", "# into the Environment object.\n", "env = Environment(keep_trailing_newline=True)\n", "TEMPLATE = env.from_string(template)\n", "\n", "prefix_tokens = tokenizer.tokenize_unsafe(chat_input.prefix)\n", "if len(prefix_tokens) > MAX_PREFIX_SUFFIX_TOKENS:\n", "    prefix_tokens = trailing_n(prefix_tokens, MAX_PREFIX_SUFFIX_TOKENS)\n", "prefix = tokenizer.detokenize(prefix_tokens)    \n", " \n", "\n", "suffix_tokens = tokenizer.tokenize_unsafe(chat_input.suffix)\n", "if len(suffix_tokens) > MAX_PREFIX_SUFFIX_TOKENS:\n", "    suffix_tokens = head_n(suffix_tokens, MAX_PREFIX_SUFFIX_TOKENS)\n", "suffix = tokenizer.detokenize(suffix_tokens)\n", "\n", "\n", "prompt = TEMPLATE.render(\n", "    message=chat_input.message,\n", "    chat_history=chat_input.chat_history,\n", "    path=chat_input.path,\n", "    selected_code=chat_input.selected_code,\n", "    prefix=prefix,\n", "    suffix=suffix,\n", "    # retrieved_chunks=[],\n", "    retrieved_chunks=chat_input.retrieved_chunks,\n", ")\n", "\n", "\n", "print(prompt)\n", "assert len(tokenizer.tokenize_unsafe(prompt)) <= MAX_TOTAL_TOKENS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "ALL_IPS = [\n", "    # \"*************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "]\n", "\n", "PORTS = [\"8000\"]\n", "\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "EOD = tokenizer.special_tokens.eod_token\n", "\n", "class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.url = url\n", "\n", "    def generate(self, prompt: str):\n", "        payload = self.get_payload(prompt)\n", "        response_json = self.send_request(payload)\n", "        if \"text_output\" not in response_json:\n", "            print(response_json)            \n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, text):\n", "        payload = {\n", "            \"text_input\": text,\n", "            \"max_tokens\": 2048,\n", "            \"end_id\": EOD,\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": 31415,\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json\n", "\n", "client = TritonClient(ALL_URLS[0])\n", "\n", "print(client.generate(prompt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
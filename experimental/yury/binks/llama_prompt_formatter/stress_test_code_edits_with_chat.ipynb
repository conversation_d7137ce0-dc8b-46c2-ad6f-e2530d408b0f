{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    Prompt<PERSON><PERSON><PERSON>,\n", ")\n", "\n", "def parse_row(row: bigquery.Row) -> ChatPromptInput:\n", "    parsed_retrieved_chunks = []\n", "\n", "    for retrieved_chunk in row['request_json']['retrieved_chunks']:\n", "        if 'char_offset' in retrieved_chunk:\n", "            char_offset = retrieved_chunk['char_offset']\n", "        else:\n", "            char_offset = 0\n", "        \n", "        if 'text' not in retrieved_chunk:\n", "            # Empty chunk\n", "            continue\n", "\n", "        parsed_retrieved_chunks.append(PromptChunk(\n", "            text=retrieved_chunk['text'],\n", "            path=retrieved_chunk['path'],\n", "            char_start=char_offset,\n", "            char_end=retrieved_chunk['char_end'],\n", "            blob_name=retrieved_chunk['blob_name'],\n", "            origin=retrieved_chunk['origin'],\n", "        ))\n", "\n", "    request_json = row['request_json']['request']\n", "\n", "    if 'prefix' in request_json:\n", "        prefix = request_json['prefix']        \n", "    else:\n", "        prefix = ''\n", "    \n", "    if 'prefix_begin' in request_json:\n", "        prefix_begin = request_json['prefix_begin']\n", "    else:\n", "        prefix_begin = 0\n", "\n", "    if 'suffix' in request_json:\n", "        suffix = request_json['suffix']\n", "    else:\n", "        suffix = ''\n", "\n", "    if 'suffix_end' in request_json:\n", "        suffix_end = request_json['suffix_end']\n", "    else:\n", "        suffix_end = 0\n", "\n", "\n", "    if 'selected_code' in request_json and len(request_json['selected_code']) > 0:\n", "        selected_code = request_json['selected_code']\n", "        if selected_code.endswith('\\n'):\n", "            selected_code = selected_code[:-1]\n", "    else:\n", "        selected_code = ''\n", "\n", "    return ChatPromptInput(\n", "        path=request_json['path'],\n", "        prefix=prefix,\n", "        selected_code=selected_code,\n", "        suffix=suffix,\n", "        message=request_json['message'],\n", "        chat_history=request_json.get('chat_history', []),\n", "        prefix_begin=prefix_begin,\n", "        suffix_end=suffix_end,\n", "        retrieved_chunks=parsed_retrieved_chunks,\n", "    )\n", "\n", "# @utils.persistent_cache(\"/mnt/efs/augment/user/yury/binks/llama3/download_request.jsonl\")\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/binks/llama3/download_request_with_response.jsonl\")\n", "def download_request(request_id: str):\n", "    query = f\"\"\"WITH\n", "        data AS (\n", "            SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}'\n", "        ),\n", "        requests AS (\n", "            SELECT request_id, raw_json AS request_json FROM data WHERE event_type = 'chat_host_request'\n", "        ),\n", "        retrievals AS (\n", "            SELECT request_id, raw_json AS retrieval_json FROM data WHERE event_type = 'retrieval_response'\n", "        ),\n", "        responses AS (\n", "            SELECT request_id, raw_json AS response_json FROM data WHERE event_type = 'chat_host_response'\n", "        )        \n", "    SELECT requests.request_id, requests.request_json, retrievals.retrieval_json, responses.response_json\n", "    FROM requests\n", "    JOIN retrievals ON requests.request_id = retrievals.request_id\n", "    JOIN responses ON requests.request_id = responses.request_id\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(\n", "        project=tenant.project_id, credentials=gcp_creds\n", "    )\n", "\n", "    print('sending request to big query')\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    if len(rows) == 0:\n", "        return None\n", "    assert len(rows) == 1, len(rows)\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.request_json,\n", "        \"retrieval_json\": row.retrieval_json,\n", "        \"response_json\": row.response_json,\n", "    }\n", "    return row\n", "\n", "def get_request_inputs_and_response(request_id: str):\n", "    chat_input_raw = download_request(request_id)    \n", "    if chat_input_raw is None:\n", "        return None, None\n", "    response = chat_input_raw[\"response_json\"][\"response\"][\"text\"]\n", "    return parse_row(chat_input_raw), response\n", "\n", "chat_input, response = get_request_inputs_and_response(\"b66302dc-11a7-4f10-8e3e-5e2dfb76a765\")\n", "chat_input, response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment, Template\n", "\n", "from base.prompt_format.util import head_n, trailing_n\n", "from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer\n", "tokenizer = Llama3InstructTokenizer()\n", "\n", "TEMPLATE_FILE = \"/home/<USER>/augment/experimental/yury/binks/llama_prompt_formatter/llama3_with_retrieval_template.txt\"\n", "\n", "MAX_TOTAL_TOKENS = 6144\n", "MAX_PREFIX_SUFFIX_TOKENS = 512\n", "\n", "def format_prompt(chat_input: ChatPromptInput):\n", "\n", "    with open(TEMPLATE_FILE) as f:\n", "        template = f.read()\n", "\n", "    # It's possible to simply instanciate the Template via `Template(template)`.\n", "    # However, jinja2 will strip the trailing newline, which is annoying.\n", "    # The way to prevent that is by explicitly passing the keep_trailing_newline flag\n", "    # into the Environment object.\n", "    env = Environment(keep_trailing_newline=True)\n", "    TEMPLATE = env.from_string(template)\n", "\n", "    prefix_tokens = tokenizer.tokenize_unsafe(chat_input.prefix)\n", "    if len(prefix_tokens) > MAX_PREFIX_SUFFIX_TOKENS:\n", "        prefix_tokens = trailing_n(prefix_tokens, MAX_PREFIX_SUFFIX_TOKENS)\n", "    prefix = tokenizer.detokenize(prefix_tokens)    \n", "    \n", "\n", "    suffix_tokens = tokenizer.tokenize_unsafe(chat_input.suffix)\n", "    if len(suffix_tokens) > MAX_PREFIX_SUFFIX_TOKENS:\n", "        suffix_tokens = head_n(suffix_tokens, MAX_PREFIX_SUFFIX_TOKENS)\n", "    suffix = tokenizer.detokenize(suffix_tokens)\n", "\n", "\n", "    prompt = TEMPLATE.render(\n", "        message=chat_input.message,\n", "        chat_history=chat_input.chat_history,\n", "        path=chat_input.path,\n", "        selected_code=chat_input.selected_code,\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        # retrieved_chunks=[],\n", "        retrieved_chunks=chat_input.retrieved_chunks,\n", "    )\n", "    assert len(tokenizer.tokenize_unsafe(prompt)) <= MAX_TOTAL_TOKENS\n", "    return prompt\n", "\n", "# chat_input, _ = get_request_inputs_and_response(\"48e2c7f5-f6f5-4cde-9076-093d3dfb6717\")\n", "chat_input, _ = get_request_inputs_and_response(\"bed7cbf6-9770-407a-94e8-d24e1c8053be\")\n", "\n", "prompt = format_prompt(chat_input)\n", "print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "ALL_IPS = [\n", "    # \"*************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "    # \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "]\n", "\n", "PORTS = [\"8000\"]\n", "\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "EOD = tokenizer.special_tokens.eod_token\n", "\n", "class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.url = url\n", "\n", "    def generate(self, prompt: str):\n", "        payload = self.get_payload(prompt)\n", "        response_json = self.send_request(payload)\n", "        if \"text_output\" not in response_json:\n", "            print(response_json)            \n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, text):\n", "        payload = {\n", "            \"text_input\": text,\n", "            \"max_tokens\": 2048,\n", "            \"end_id\": EOD,\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": 31415,\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json\n", "\n", "client = TritonClient(ALL_URLS[0])\n", "\n", "print(client.generate(prompt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CHAT_REQUESTS_LIMIT100 = [\"79149e17-68ee-46b1-9ab9-484a4b2dd1bf\", \"b4361e31-40d9-43fb-90d2-447014651c76\", \"f1447e32-a794-45cc-a11c-1ef33db3515c\", \"b296c424-14e9-4f80-852c-3b4c427721df\", \"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"019b69b2-6557-4e9f-bbbb-f48702174600\", \"686474c4-fca0-42a4-a2e6-2ba77dadea9b\", \"9803b8ee-d3d0-4fee-ba01-8ae84b5aa166\", \"50856ff3-e488-4594-bf50-c24d57af8691\", \"f7e66514-6ef5-4daf-a877-df3012624f9b\", \"5543df85-915c-4b31-8c89-1044385f5502\", \"4839420e-07ec-41e7-a2d0-98e4e3241839\", \"bc338297-d9e0-46ba-bd14-df3c194c0c43\", \"7a1dc147-5712-4461-9498-c7f1c506066c\", \"b87b3f9f-9dc3-43bc-b1d2-7f48d465be22\", \"4ea43ea4-a2bf-4c64-975c-17a17ed89450\", \"d4f94f65-e7d7-4973-a59f-7df06c29959b\", \"359cf3b8-1bef-414c-bd95-0e0ab7b7799f\", \"3fd83dd8-f2a4-44b3-96e9-0b246dfe4039\", \"d57a407d-87b0-4f2a-a8cc-bb728f9cef4d\", \"b2278cee-b4a7-49d8-ad29-9b99e384821f\", \"0e49c07e-5db2-4b6b-90f4-6c404c5f0db5\", \"b3f43bbd-72ec-44e6-a4b8-c5531f94e6d1\", \"ffadae43-5637-4901-a080-dcef1f243853\", \"46a901af-e8cb-4e6e-a29f-75dd6eaf9ebe\", \"c031ce27-2f39-459e-9459-4afedb2a5c92\", \"6b406f39-eb8c-4a66-a134-37b9474aad51\", \"4acecdab-4a70-407b-af74-e2ef3d47ec81\", \"3dc54a23-b2e5-4cc6-a6fb-bbbb6e2cff8b\", \"85708c79-086c-442f-a6f2-7f711974d2cd\", \"00e12f18-77f2-4d5f-a607-3483cd8f2022\", \"cbb688b1-79a6-4260-a7fd-74d1bed31801\", \"1c9bb8bd-e09b-46b2-9c8b-b5c37e872195\", \"1b391084-df3e-435a-b631-7357a6c98202\", \"04d1b23d-26a9-4b6b-9bf2-fdd8b9db2cf4\", \"72526145-c459-4e09-b177-6dec27937fbf\", \"8bbf72c1-6c41-41b7-8d5f-762860d1ff26\", \"ccb2ea5e-0f54-4e32-bc74-7f9419ade483\", \"f21cad65-b018-40af-8d7d-03e900769025\", \"2ae1a4f6-8d81-4273-8647-e93401432082\", \"0edfddc1-b598-48ab-95e3-167e80986af7\", \"6a904f9a-4f8d-4518-96bf-cf1cc7af4da7\", \"112a70ea-f342-4f88-bd61-1607e6da431a\", \"47963ef2-9507-49a4-a82e-1f458a69301b\", \"419bf8a5-3453-4091-83f6-638e3e7d7c16\", \"ffdd9137-834c-4c56-963f-040a3449c355\", \"0a9c30f2-2e51-435d-bc76-f5e61664a8eb\", \"3cd69120-430c-4b27-bdc8-9abea4125325\", \"0c067cc4-3c7d-41ad-a733-1f968167550f\", \"80efa9f9-c9de-4b79-a70c-d4a52855b80a\", \"03408a5f-4093-404b-b72b-99375c9b39f3\", \"894f938e-b211-46cb-b8c7-fda837d65547\", \"1cf0fb64-9bdb-4fa8-b82f-19b2d58074e9\", \"84a7380e-a50f-40b1-9fa2-2108299b4293\", \"de4457f1-bacb-41bc-be4d-3df5e9934af6\", \"6d99f2bb-626a-4b63-80b4-0ba6c65a0694\", \"71fae28c-8ee4-47d1-81c4-d97048376181\", \"35d6a17c-e996-4247-bd6a-fba3bd0dcaea\", \"2d5d6dd5-d11c-4339-959e-1750fd807843\", \"d4ec9a1c-1c1e-49bc-bc55-24eaa8c4b859\", \"513558e1-28f6-416c-9f80-1322848d8462\", \"a8722f47-069a-42db-8337-1f5d8d911474\", \"12583cec-d655-4d94-9a1f-5fffffbbe19c\", \"e57850a9-868d-435b-8c64-b836b1cc79d1\", \"9ec3ad52-fa9a-4ea4-9e4d-c0a6c90280b4\", \"8d3b0829-ac87-4078-8d26-1e9f4d2119b0\", \"930cbf27-5588-4eb4-a23e-068b8cab79f1\", \"27b10af1-d8ec-4ab1-a3d3-a927dfc38b61\", \"ae593e23-67ac-4928-8218-546cea4f5ad2\", \"219c60fe-232c-4bc8-bd2a-37fd5897a51d\", \"eb7844cd-1d38-4e8f-90e4-493e289f1255\", \"db22ee0c-2c1f-4d0b-b05b-ba367ebb3ed9\", \"541c971d-f988-4792-ab56-48d4e26b9d71\", \"9580acdf-b779-4019-a502-9ec956d72441\", \"8f81c598-9faf-428c-86bd-20c715466122\", \"59a539f4-9c89-4e65-ad44-5d5ab0d81192\", \"dba3e608-fc78-4bf3-b921-338fb433fc8c\", \"d30df448-a765-43c4-8343-e2388dd1148d\", \"b10ca845-e1cb-49cf-b3ff-3b6f02399932\", \"dee205f6-e3db-403b-8a68-cef110cd392e\", \"4ec17a9e-9a78-4b85-9a41-3b8d44555293\", \"92c4115b-53b8-4192-b4e1-1d8181fc9826\", \"bb64ca24-0174-41ab-8893-f5ccf99d075a\", \"9e431513-eef9-449f-bcd8-b79095f6fed1\", \"0fdcf916-b820-4e3d-afb0-3e9fcb131568\", \"27433359-20be-495e-b7d0-cda48ebf3bee\", \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\", \"3a62972e-806e-4fa2-8173-53f28f96216a\", \"7e8524fa-6433-4a15-873f-db75dc081d9d\", \"bd1cc0e2-74c9-4182-848d-b3c5488700c9\", \"b0d37c90-67f3-4054-b795-bca9fa4e5ce8\", \"177a9038-ef62-4e74-9c0e-7e9fb62a8af8\", \"0045b6d2-39df-4bb4-a486-433694da024a\", \"5bf950fe-41d9-4128-844b-be56644e3e00\", \"75460499-c581-41be-8f2d-2964eecb232c\", \"a2510041-5451-4577-9d27-336111b94aec\", \"a9bf460e-6a77-4933-810d-2f8f5afa47de\", \"16157ede-3f24-45da-8f17-9e90d6e2ddb7\", \"0a92cae8-c100-4757-a7d3-c418eca15aa0\", \"9250833a-e509-44f0-9006-42fd9867bf03\"]\n", "# CHAT_REQUESTS_RAND100 = [\"63c55189-ce4b-4ebf-81ec-9a2eccb45f12\", \"84a990df-0ca7-4b0b-89c1-c3c4a2b3e505\", \"70418e8e-950f-4833-b803-97e386ca22e4\", \"86795e4a-3f6e-4e59-9b9f-28821a514416\", \"59b1400a-a566-4938-b167-33cf6c7c9f2e\", \"f8ae095e-20b0-4008-9b40-275d91e969a2\", \"34895545-32ad-41ac-a9ef-cd44ced4d94b\", \"1153ccf8-2a86-4db2-bbe5-6dc44b4f7d86\", \"24e66dfb-8e62-4331-bc73-3db987256c63\", \"ae593e23-67ac-4928-8218-546cea4f5ad2\", \"1df4a1f1-904a-4b0c-9dec-7fb4e1f13e11\", \"b052f35d-71d7-43db-a4dd-5d5f596079f9\", \"6b96be15-56f1-42cf-8ec9-fd755aa86061\", \"359cf3b8-1bef-414c-bd95-0e0ab7b7799f\", \"c285e40f-6185-45ea-96a4-2413b3105ec6\", \"445d2f7d-495f-4075-9612-f45f8de802a9\", \"60e1f5e3-aa49-4247-832d-65c9d6fb2278\", \"7320a4ab-302c-4494-86c1-4b4d43ba7243\", \"98e1f9b9-aede-43e7-aee4-9ce23455fffc\", \"e62e92ff-36ae-4a61-914e-7bedbad08745\", \"cbb688b1-79a6-4260-a7fd-74d1bed31801\", \"46742c0e-8db4-416a-a9cc-9f8501358d27\", \"8a71145e-ba1f-45b7-9ea8-b5667c4b4067\", \"2e2a9a91-8a89-439c-823e-bdb38ef70e85\", \"dd98340f-e996-4e27-a68b-37f0c139f313\", \"9a17b7eb-2c32-4939-a900-707cf1e14b93\", \"6818baf1-107b-41d8-97ed-adbf33305038\", \"039b9266-a692-4795-88ca-e4fc63b07620\", \"d42156dc-fc2d-4854-bf03-2267220a2a54\", \"175b2d06-f59d-4a15-ac4c-c5fadd01b72d\", \"b8e2209c-1b64-418a-9cfd-c036f272b731\", \"7786c2b5-7db1-4df0-a5d4-9793c066f6c0\", \"fb6ba6d1-fe83-4ca7-9f50-e1268e0ac5b9\", \"2db7347e-14b1-43c0-9468-77f95e269522\", \"dbc4c457-9857-457b-91ca-a46ea75b19a2\", \"e610f5f8-3689-4085-bb4a-912baadcbad4\", \"bc397519-7b45-4dae-a132-4fbe694da711\", \"3103e24e-02ac-48a3-a32c-fb6d1c51216a\", \"58954470-3d48-4e27-b2c1-ade336fb5fd8\", \"0742c0c5-97be-4f9b-976e-8de7ff33fa8c\", \"daa50138-bed0-49ff-ba7e-aef28329e33d\", \"671bde31-af76-408f-a9ac-dbc38909b48a\", \"20e9efc3-2112-4ed5-84d3-d3b866a2bcff\", \"2b3b9d1e-b695-4175-a0fe-cb4dcd02cd7c\", \"1c9bb8bd-e09b-46b2-9c8b-b5c37e872195\", \"833af964-c824-4eca-b8b4-ebfab2a1cc1c\", \"eb8fe5fc-24eb-4673-9010-4fc708a4643a\", \"608e4e66-cb7e-4fc4-852a-f735b876ec1f\", \"6733e026-58d6-4865-977a-a0ed8a88f286\", \"8c35dd6d-8681-456b-b339-920c989498a5\", \"0ae73c71-d915-433d-9426-e4533ec62df5\", \"9a806e3b-aa7f-4a6a-96e1-1f204a0b1499\", \"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"8c4ca260-1e6b-4360-964b-d24a9a2140d1\", \"a946f46c-083c-4590-adb0-19bd990a08f7\", \"bbab4b3d-9124-40ab-a496-a6a21e5f3a16\", \"af5c9455-5c55-4013-9020-635e269b2eac\", \"4fe0cda3-143a-48f1-865c-daf3493fa4f7\", \"cc50e5d7-2c30-44bb-bb67-53c83b328d14\", \"903d5a22-78d7-47bd-93bc-5d5105320865\", \"19e994b3-ac44-463c-9613-d393cb21b3dc\", \"588f4d93-fd7b-4295-88e9-972412b3d496\", \"644c74b2-3d77-40f9-9d0f-58c87508d7da\", \"f2231adb-9418-4072-abc7-527a39ceba05\", \"d4035e8c-f364-4911-bad3-8e5b3c367197\", \"c136a589-d332-4ec1-8d85-9b4dcd90b204\", \"0a9c30f2-2e51-435d-bc76-f5e61664a8eb\", \"a23463c6-402f-49c5-b8a0-6a3421df39ac\", \"178a3747-2d5c-409e-927f-7b6b1a47dcca\", \"cc410cfd-809a-4d9d-9e58-609f4295f523\", \"da16cb72-a3fe-432f-9df7-98b5975d3c2c\", \"94bfcfa8-9d6f-4b77-82d1-c87e3744b4ce\", \"2ce7e604-d7b4-49a0-97de-915cbc286950\", \"585e93d2-7b6b-483a-8de9-c5c22b366f1a\", \"4839420e-07ec-41e7-a2d0-98e4e3241839\", \"57affda2-4c03-4795-b049-0f8b59476ca0\", \"cafd817d-237f-4bc2-9759-e5c07463d2cc\", \"fb311726-27fa-4a42-9c77-d4dad8f2e8df\", \"98b9f1d6-3f8f-4b0f-9d83-da2d681a76dc\", \"2af140f0-488c-47f9-a7f9-7c926f5da783\", \"fbb644b7-61a0-476a-a102-3bd38d27530d\", \"98629789-3ba6-48be-993f-e1137ef5fa95\", \"8b953e02-2bdd-4e7e-b3b7-94907a890e40\", \"f1a1a9f4-8db1-4526-abe8-2530649a2442\", \"ce255b2e-e02c-4b9a-ad28-07ebbd0e2ae5\", \"aea7bf20-05d5-4464-9ff0-3a4c456e75b5\", \"0348f37c-56e3-485d-9992-e10fdd668b18\", \"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"59717724-8508-42b0-9612-6665895f50f9\", \"79149e17-68ee-46b1-9ab9-484a4b2dd1bf\", \"5863dbd3-fcb9-4a14-81b0-f1134f2bde55\", \"019b69b2-6557-4e9f-bbbb-f48702174600\", \"2ac8a0a3-743a-4128-bf60-4dcf0e636a2a\", \"801428cd-ee7e-4d1b-a23b-7803f9e24788\", \"ef3cb154-48dd-416e-9437-e362ca39591f\", \"669359e2-a16b-47d3-bd49-5959447f6e85\", \"787a62a9-6d47-4d64-ac91-33d99b43652f\", \"d31d31a4-40db-436c-8c6e-62a55d6ac1cd\", \"7f773c42-1572-4291-93b0-63a06b53a52f\", \"7c6858dc-33e5-417e-bfdc-47974e955729\"]\n", "\n", "CURRENT_MODEL_IS_RIGHT_AND_SELECTED_CODE_IS_IRRELEVANT = [\n", "    \"019b69b2-6557-4e9f-bbbb-f48702174600\",\n", "    \"686474c4-fca0-42a4-a2e6-2ba77dadea9b\",\n", "    \"f7e66514-6ef5-4daf-a877-df3012624f9b\",\n", "    \"1153ccf8-2a86-4db2-bbe5-6dc44b4f7d86\",\n", "    \"24e66dfb-8e62-4331-bc73-3db987256c63\",\n", "    \"9a806e3b-aa7f-4a6a-96e1-1f204a0b1499\",\n", "]\n", "\n", "# CHAT_REQUESTS = [\"63c55189-ce4b-4ebf-81ec-9a2eccb45f12\", \"84a990df-0ca7-4b0b-89c1-c3c4a2b3e505\", \"70418e8e-950f-4833-b803-97e386ca22e4\", \"86795e4a-3f6e-4e59-9b9f-28821a514416\", \"59b1400a-a566-4938-b167-33cf6c7c9f2e\", \"f8ae095e-20b0-4008-9b40-275d91e969a2\", \"34895545-32ad-41ac-a9ef-cd44ced4d94b\", \"1153ccf8-2a86-4db2-bbe5-6dc44b4f7d86\", \"24e66dfb-8e62-4331-bc73-3db987256c63\", \"ae593e23-67ac-4928-8218-546cea4f5ad2\", \"1df4a1f1-904a-4b0c-9dec-7fb4e1f13e11\", \"b052f35d-71d7-43db-a4dd-5d5f596079f9\", \"6b96be15-56f1-42cf-8ec9-fd755aa86061\", \"359cf3b8-1bef-414c-bd95-0e0ab7b7799f\", \"c285e40f-6185-45ea-96a4-2413b3105ec6\", \"445d2f7d-495f-4075-9612-f45f8de802a9\", \"60e1f5e3-aa49-4247-832d-65c9d6fb2278\", \"7320a4ab-302c-4494-86c1-4b4d43ba7243\", \"98e1f9b9-aede-43e7-aee4-9ce23455fffc\", \"e62e92ff-36ae-4a61-914e-7bedbad08745\", \"cbb688b1-79a6-4260-a7fd-74d1bed31801\", \"46742c0e-8db4-416a-a9cc-9f8501358d27\", \"8a71145e-ba1f-45b7-9ea8-b5667c4b4067\", \"2e2a9a91-8a89-439c-823e-bdb38ef70e85\", \"dd98340f-e996-4e27-a68b-37f0c139f313\", \"9a17b7eb-2c32-4939-a900-707cf1e14b93\", \"6818baf1-107b-41d8-97ed-adbf33305038\", \"039b9266-a692-4795-88ca-e4fc63b07620\", \"d42156dc-fc2d-4854-bf03-2267220a2a54\", \"175b2d06-f59d-4a15-ac4c-c5fadd01b72d\", \"b8e2209c-1b64-418a-9cfd-c036f272b731\", \"7786c2b5-7db1-4df0-a5d4-9793c066f6c0\", \"fb6ba6d1-fe83-4ca7-9f50-e1268e0ac5b9\", \"2db7347e-14b1-43c0-9468-77f95e269522\", \"dbc4c457-9857-457b-91ca-a46ea75b19a2\", \"e610f5f8-3689-4085-bb4a-912baadcbad4\", \"bc397519-7b45-4dae-a132-4fbe694da711\", \"3103e24e-02ac-48a3-a32c-fb6d1c51216a\", \"58954470-3d48-4e27-b2c1-ade336fb5fd8\", \"0742c0c5-97be-4f9b-976e-8de7ff33fa8c\", \"daa50138-bed0-49ff-ba7e-aef28329e33d\", \"671bde31-af76-408f-a9ac-dbc38909b48a\", \"20e9efc3-2112-4ed5-84d3-d3b866a2bcff\", \"2b3b9d1e-b695-4175-a0fe-cb4dcd02cd7c\", \"1c9bb8bd-e09b-46b2-9c8b-b5c37e872195\", \"833af964-c824-4eca-b8b4-ebfab2a1cc1c\", \"eb8fe5fc-24eb-4673-9010-4fc708a4643a\", \"608e4e66-cb7e-4fc4-852a-f735b876ec1f\", \"6733e026-58d6-4865-977a-a0ed8a88f286\", \"8c35dd6d-8681-456b-b339-920c989498a5\", \"0ae73c71-d915-433d-9426-e4533ec62df5\", \"9a806e3b-aa7f-4a6a-96e1-1f204a0b1499\", \"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"8c4ca260-1e6b-4360-964b-d24a9a2140d1\", \"a946f46c-083c-4590-adb0-19bd990a08f7\", \"bbab4b3d-9124-40ab-a496-a6a21e5f3a16\", \"af5c9455-5c55-4013-9020-635e269b2eac\", \"4fe0cda3-143a-48f1-865c-daf3493fa4f7\", \"cc50e5d7-2c30-44bb-bb67-53c83b328d14\", \"903d5a22-78d7-47bd-93bc-5d5105320865\", \"19e994b3-ac44-463c-9613-d393cb21b3dc\", \"588f4d93-fd7b-4295-88e9-972412b3d496\", \"644c74b2-3d77-40f9-9d0f-58c87508d7da\", \"f2231adb-9418-4072-abc7-527a39ceba05\", \"d4035e8c-f364-4911-bad3-8e5b3c367197\", \"c136a589-d332-4ec1-8d85-9b4dcd90b204\", \"0a9c30f2-2e51-435d-bc76-f5e61664a8eb\", \"a23463c6-402f-49c5-b8a0-6a3421df39ac\", \"178a3747-2d5c-409e-927f-7b6b1a47dcca\", \"cc410cfd-809a-4d9d-9e58-609f4295f523\", \"da16cb72-a3fe-432f-9df7-98b5975d3c2c\", \"94bfcfa8-9d6f-4b77-82d1-c87e3744b4ce\", \"2ce7e604-d7b4-49a0-97de-915cbc286950\", \"585e93d2-7b6b-483a-8de9-c5c22b366f1a\", \"4839420e-07ec-41e7-a2d0-98e4e3241839\", \"57affda2-4c03-4795-b049-0f8b59476ca0\", \"cafd817d-237f-4bc2-9759-e5c07463d2cc\", \"fb311726-27fa-4a42-9c77-d4dad8f2e8df\", \"98b9f1d6-3f8f-4b0f-9d83-da2d681a76dc\", \"2af140f0-488c-47f9-a7f9-7c926f5da783\", \"fbb644b7-61a0-476a-a102-3bd38d27530d\", \"98629789-3ba6-48be-993f-e1137ef5fa95\", \"8b953e02-2bdd-4e7e-b3b7-94907a890e40\", \"f1a1a9f4-8db1-4526-abe8-2530649a2442\", \"ce255b2e-e02c-4b9a-ad28-07ebbd0e2ae5\", \"aea7bf20-05d5-4464-9ff0-3a4c456e75b5\", \"0348f37c-56e3-485d-9992-e10fdd668b18\", \"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"59717724-8508-42b0-9612-6665895f50f9\", \"79149e17-68ee-46b1-9ab9-484a4b2dd1bf\", \"5863dbd3-fcb9-4a14-81b0-f1134f2bde55\", \"019b69b2-6557-4e9f-bbbb-f48702174600\", \"2ac8a0a3-743a-4128-bf60-4dcf0e636a2a\", \"801428cd-ee7e-4d1b-a23b-7803f9e24788\", \"ef3cb154-48dd-416e-9437-e362ca39591f\", \"669359e2-a16b-47d3-bd49-5959447f6e85\", \"787a62a9-6d47-4d64-ac91-33d99b43652f\", \"d31d31a4-40db-436c-8c6e-62a55d6ac1cd\", \"7f773c42-1572-4291-93b0-63a06b53a52f\", \"7c6858dc-33e5-417e-bfdc-47974e955729\"]\n", "# CHAT_REQUESTS = CURRENT_MODEL_IS_RIGHT_AND_SELECTED_CODE_IS_IRRELEVANT\n", "\n", "for request_id in CHAT_REQUESTS:\n", "    chat_input, response = get_request_inputs_and_response(request_id)\n", "    if chat_input is None:\n", "        print(\"No chat input for\", request_id)\n", "        continue\n", "    print(\"[REQUEST]\", request_id)\n", "    print(\"[MESSAGE]\", chat_input.message)\n", "    print(\"[SELECTED CODE]\")\n", "    print(chat_input.selected_code)\n", "    print(\"[RESPONSE]\")\n", "    print(response)\n", "    print(\"\\n\" + \"=\" * 30 + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = TritonClient(ALL_URLS[0])\n", "\n", "\n", "CURRENT_MODEL_IS_RIGHT = [\n", "    \"d4ec9a1c-1c1e-49bc-bc55-24eaa8c4b859\", # My example for reverse comments where there's also a chat history\n", "    \"b296c424-14e9-4f80-852c-3b4c427721df\",\n", "    \"f1447e32-a794-45cc-a11c-1ef33db3515c\",\n", "    \"b2278cee-b4a7-49d8-ad29-9b99e384821f\",\n", "    \"0e49c07e-5db2-4b6b-90f4-6c404c5f0db5\",\n", "    \"c031ce27-2f39-459e-9459-4afedb2a5c92\",\n", "    \"3a62972e-806e-4fa2-8173-53f28f96216a\",\n", "    \"9250833a-e509-44f0-9006-42fd9867bf03\",\n", "    \"0a92cae8-c100-4757-a7d3-c418eca15aa0\", # very nice hardcore example\n", "    \"b0d37c90-67f3-4054-b795-bca9fa4e5ce8\", # nice examp[les \n", "    \"ccb2ea5e-0f54-4e32-bc74-7f9419ade483\", # question about selected code\n", "    \"eb7844cd-1d38-4e8f-90e4-493e289f1255\", # question about selected code\n", "    \"63c55189-ce4b-4ebf-81ec-9a2eccb45f12\", # hardcore example where \n", "    \"84a990df-0ca7-4b0b-89c1-c3c4a2b3e505\", # explain code\n", "    \"6b96be15-56f1-42cf-8ec9-fd755aa86061\", # nice and small edit\n", "    \"608e4e66-cb7e-4fc4-852a-f735b876ec1f\", # nice case where no edit is expected, it's just a question about the selected code\n", "    \"4fe0cda3-143a-48f1-865c-daf3493fa4f7\", # simple case but a lot of lines to edit\n", "]\n", "\n", "CURRENT_MODEL_IS_WRONG_REQUESTS = [\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"bed7cbf6-9770-407a-94e8-d24e1c8053be\", # long example where prod model had weird [BEGIN SELECTED CODE] artifact    \n", "    \"48e2c7f5-f6f5-4cde-9076-093d3dfb6717\",    \n", "    \"d31d31a4-40db-436c-8c6e-62a55d6ac1cd\",\n", "    \"a23463c6-402f-49c5-b8a0-6a3421df39ac\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\", # almost correct just line \"T = TypeVar(\"T\")\" is missing      \n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",        \n", "    # \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"dbc4c457-9857-457b-91ca-a46ea75b19a2\", # not a bad edit, but output doesn't have parts of the selected code\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\", # almost correct, just lossed the comments marker\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\", # model might have just failed horribly\n", "    # \"0ae73c71-d915-433d-9426-e4533ec62df5\",    \n", "    \"cc50e5d7-2c30-44bb-bb67-53c83b328d14\", # seems alright, but deleted the original imports\n", "    # \"0a9c30f2-2e51-435d-bc76-f5e61664a8eb\", # some of the imports are missing    \n", "]\n", "\n", "for request_id in CURRENT_MODEL_IS_WRONG_REQUESTS:\n", "    print(\"[REQUEST]\", request_id)    \n", "    chat_input, old_response = get_request_inputs_and_response(request_id)\n", "    if chat_input is None:\n", "        print(\"No chat input for\", request_id)\n", "        continue\n", "\n", "    prompt = format_prompt(chat_input)\n", "    new_response = client.generate(prompt)\n", "    \n", "    print(\"[MESSAGE]\", chat_input.message)\n", "    print(\"[SELECTED CODE]\")\n", "    print(chat_input.selected_code)\n", "    print(\"[PROD RESPONSE]\")\n", "    print(old_response)\n", "    print(\"[NEW RESPONSE]\")\n", "    print(new_response)\n", "    print(\"\\n\" + \"=\" * 30 + \"\\n\")\n", "    break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
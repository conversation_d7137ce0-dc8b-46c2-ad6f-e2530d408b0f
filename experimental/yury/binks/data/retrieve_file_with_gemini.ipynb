{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# !pip install <PERSON>ai"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import vertexai\n", "from vertexai.generative_models import GenerativeModel, ChatSession\n", "project_id = \"gemini-pro-420822\"\n", "location = \"us-central1\"\n", "vertexai.init(project=project_id, location=location)\n", "# GOOGLE_MODEL = GenerativeModel(\"gemini-1.5-pro-preview-0409\")\n", "GOOGLE_MODEL = GenerativeModel(\"gemini-1.5-flash-preview-0514\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 5000 samples\n"]}], "source": ["data = []\n", "with open('/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl') as f:\n", "    for line in f:\n", "        data.append(json.loads(line[:-1]))\n", "print(\"Loaded %d samples\" % len(data))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 19\u001b[0m\n\u001b[1;32m     10\u001b[0m             full_repo\u001b[38;5;241m.\u001b[39mextend([\n\u001b[1;32m     11\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThis is file `\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m`\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m file[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmax_stars_repo_path\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m     12\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m```\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     13\u001b[0m                 file[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m     14\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m```\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     15\u001b[0m             ])\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(full_repo)\n\u001b[0;32m---> 19\u001b[0m repo_prompt \u001b[38;5;241m=\u001b[39m get_repo_prompt(\u001b[43mdata\u001b[49m[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfile_list\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28mprint\u001b[39m(repo_prompt[:\u001b[38;5;241m500\u001b[39m])\n", "\u001b[0;31mNameError\u001b[0m: name 'data' is not defined"]}], "source": ["def get_repo_prompt(repo):\n", "    full_repo = [\n", "        \"You are an AI code assistant integrated into the VSCode IDE. Your primary role is to help software developers by answering their questions related to code and software engineering.\"\n", "        \"Below are the files from the developer's project. Read them carefully and be ready to answer any questions.\\n\"\n", "        # \"For now, do NOT provide a lengthy reply -- only acknowledge you understand the instructions and wait for the question.\"\n", "    ]\n", "\n", "    for file in repo:\n", "        if file['langpart'] in RETRIEVAL_LANGUAGES:\n", "            full_repo.extend([\n", "                \"This is file `%s`\" % file['max_stars_repo_path'],\n", "                \"```\",\n", "                file['content'],\n", "                \"```\",\n", "            ])\n", "\n", "    return \"\\n\\n\".join(full_repo)\n", "\n", "repo_prompt = get_repo_prompt(data[0]['file_list'])\n", "\n", "print(repo_prompt[:500])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 17\u001b[0m\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m prefix \u001b[38;5;241m+\u001b[39m question \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m suffix\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# print(format_question(question))\u001b[39;00m\n\u001b[0;32m---> 17\u001b[0m repo \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m     18\u001b[0m sample \u001b[38;5;241m=\u001b[39m repo[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdocuments_with_questions\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m     20\u001b[0m path \u001b[38;5;241m=\u001b[39m sample[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpath\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[0;31mNameError\u001b[0m: name 'data' is not defined"]}], "source": ["def format_question(question):\n", "    prefix = (\n", "        \"\\n----------------------------\\n\\n\"\n", "        \"Now that you have read an entire project, answer the following question:\\n\\n\"\n", "    )\n", "    suffix = (\n", "        \"\\nNOTE: First, find the paths to ALL files with the relevant information. It's guranteed that the answer is located somewhere in the project.\"\n", "        \"Then, on a next line, provide the answer.\\n\"\n", "        \"For example, for the question \\\"Where do we perform X?\\\", you answer would be:\\n\"\n", "        \"`path/to/file1.ext`, `path/to/file2.ext`\\n\"\n", "        \"We do X in the Y method of the Z class in the file `path/to/file1.ext`, and in the method Y2 of the class Z2 in the file `path/to/file2.ext`.\"\n", "    )\n", "    return prefix + question + \"\\n\" + suffix\n", "\n", "# print(format_question(question))\n", "\n", "repo = data[0]\n", "sample = repo['documents_with_questions'][0]\n", "\n", "path = sample['path']\n", "question = sample['questions'][0]['question']\n", "print(format_question(question))"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"ename": "InvalidArgument", "evalue": "400 Unable to submit request because the input token count is 1023011 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m_InactiveRpcError\u001b[0m                         Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:76\u001b[0m, in \u001b[0;36m_wrap_unary_errors.<locals>.error_remapped_callable\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     75\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 76\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcallable_\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     77\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m grpc\u001b[38;5;241m.\u001b[39mRpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/grpc/_channel.py:1176\u001b[0m, in \u001b[0;36m_UnaryUnaryMultiCallable.__call__\u001b[0;34m(self, request, timeout, metadata, credentials, wait_for_ready, compression)\u001b[0m\n\u001b[1;32m   1170\u001b[0m (\n\u001b[1;32m   1171\u001b[0m     state,\n\u001b[1;32m   1172\u001b[0m     call,\n\u001b[1;32m   1173\u001b[0m ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_blocking(\n\u001b[1;32m   1174\u001b[0m     request, timeout, metadata, credentials, wait_for_ready, compression\n\u001b[1;32m   1175\u001b[0m )\n\u001b[0;32m-> 1176\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_end_unary_response_blocking\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcall\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>e\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/grpc/_channel.py:1005\u001b[0m, in \u001b[0;36m_end_unary_response_blocking\u001b[0;34m(state, call, with_call, deadline)\u001b[0m\n\u001b[1;32m   1004\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1005\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m _InactiveRpcError(state)\n", "\u001b[0;31m_InactiveRpcError\u001b[0m: <_InactiveRpcError of RPC that terminated with:\n\tstatus = StatusCode.INVALID_ARGUMENT\n\tdetails = \"Unable to submit request because the input token count is 1023011 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\"\n\tdebug_error_string = \"UNKNOWN:Error received from peer ipv4:***************:443 {created_time:\"2024-05-06T22:09:37.029509898+00:00\", grpc_status:3, grpc_message:\"Unable to submit request because the input token count is 1023011 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\"}\"\n>", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mInvalidArgument\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[106], line 20\u001b[0m\n\u001b[1;32m     17\u001b[0m message \u001b[38;5;241m=\u001b[39m get_repo_prompt(repo[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfile_list\u001b[39m\u001b[38;5;124m'\u001b[39m]) \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m format_question(question)\n\u001b[1;32m     19\u001b[0m chat \u001b[38;5;241m=\u001b[39m GOOGLE_MODEL\u001b[38;5;241m.\u001b[39mstart_chat()\n\u001b[0;32m---> 20\u001b[0m responses \u001b[38;5;241m=\u001b[39m \u001b[43mchat\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend_message\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m     22\u001b[0m evals\u001b[38;5;241m.\u001b[39mappend({\n\u001b[1;32m     23\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrepo_index\u001b[39m\u001b[38;5;124m\"\u001b[39m: repo_id,\n\u001b[1;32m     24\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquestion_type_index\u001b[39m\u001b[38;5;124m\"\u001b[39m: question_type_id,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     29\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgemini_response\u001b[39m\u001b[38;5;124m\"\u001b[39m: responses\u001b[38;5;241m.\u001b[39mtext,\n\u001b[1;32m     30\u001b[0m })\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/vertexai/generative_models/_generative_models.py:809\u001b[0m, in \u001b[0;36mChatSession.send_message\u001b[0;34m(self, content, generation_config, safety_settings, tools, stream)\u001b[0m\n\u001b[1;32m    802\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_message_streaming(\n\u001b[1;32m    803\u001b[0m         content\u001b[38;5;241m=\u001b[39mcontent,\n\u001b[1;32m    804\u001b[0m         generation_config\u001b[38;5;241m=\u001b[39mgeneration_config,\n\u001b[1;32m    805\u001b[0m         safety_settings\u001b[38;5;241m=\u001b[39msafety_settings,\n\u001b[1;32m    806\u001b[0m         tools\u001b[38;5;241m=\u001b[39mtools,\n\u001b[1;32m    807\u001b[0m     )\n\u001b[1;32m    808\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 809\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_message\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    810\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    811\u001b[0m \u001b[43m        \u001b[49m\u001b[43mgeneration_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgeneration_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    812\u001b[0m \u001b[43m        \u001b[49m\u001b[43msafety_settings\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msafety_settings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    813\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    814\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/vertexai/generative_models/_generative_models.py:905\u001b[0m, in \u001b[0;36mChatSession._send_message\u001b[0;34m(self, content, generation_config, safety_settings, tools)\u001b[0m\n\u001b[1;32m    903\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    904\u001b[0m     request_history \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_history \u001b[38;5;241m+\u001b[39m history_delta\n\u001b[0;32m--> 905\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_model\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_generate_content\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    906\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontents\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_history\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    907\u001b[0m \u001b[43m        \u001b[49m\u001b[43mgeneration_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgeneration_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    908\u001b[0m \u001b[43m        \u001b[49m\u001b[43msafety_settings\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msafety_settings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    909\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    910\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    911\u001b[0m     \u001b[38;5;66;03m# By default we're not adding incomplete interactions to history.\u001b[39;00m\n\u001b[1;32m    912\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response_validator \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/vertexai/generative_models/_generative_models.py:496\u001b[0m, in \u001b[0;36m_GenerativeModel._generate_content\u001b[0;34m(self, contents, generation_config, safety_settings, tools, tool_config)\u001b[0m\n\u001b[1;32m    471\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Generates content.\u001b[39;00m\n\u001b[1;32m    472\u001b[0m \n\u001b[1;32m    473\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    487\u001b[0m \u001b[38;5;124;03m    A single GenerationResponse object\u001b[39;00m\n\u001b[1;32m    488\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    489\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_request(\n\u001b[1;32m    490\u001b[0m     contents\u001b[38;5;241m=\u001b[39mcontents,\n\u001b[1;32m    491\u001b[0m     generation_config\u001b[38;5;241m=\u001b[39mgeneration_config,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    494\u001b[0m     tool_config\u001b[38;5;241m=\u001b[39mtool_config,\n\u001b[1;32m    495\u001b[0m )\n\u001b[0;32m--> 496\u001b[0m gapic_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_prediction_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_parse_response(gapic_response)\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/google/cloud/aiplatform_v1beta1/services/prediction_service/client.py:2102\u001b[0m, in \u001b[0;36mPredictionServiceClient.generate_content\u001b[0;34m(self, request, model, contents, retry, timeout, metadata)\u001b[0m\n\u001b[1;32m   2099\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_universe_domain()\n\u001b[1;32m   2101\u001b[0m \u001b[38;5;66;03m# Send the request.\u001b[39;00m\n\u001b[0;32m-> 2102\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrpc\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2103\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2104\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2105\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2106\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2107\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2109\u001b[0m \u001b[38;5;66;03m# Done; return the response.\u001b[39;00m\n\u001b[1;32m   2110\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py:131\u001b[0m, in \u001b[0;36m_GapicCallable.__call__\u001b[0;34m(self, timeout, retry, compression, *args, **kwargs)\u001b[0m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compression \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    129\u001b[0m     kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcompression\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m compression\n\u001b[0;32m--> 131\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped_func\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:78\u001b[0m, in \u001b[0;36m_wrap_unary_errors.<locals>.error_remapped_callable\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     76\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m callable_(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m     77\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m grpc\u001b[38;5;241m.\u001b[39mRpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m---> 78\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exceptions\u001b[38;5;241m.\u001b[39mfrom_grpc_error(exc) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mexc\u001b[39;00m\n", "\u001b[0;31mInvalidArgument\u001b[0m: 400 Unable to submit request because the input token count is 1023011 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models"]}], "source": ["np.random.seed(31415)\n", "NUM_SAMPLES = 20\n", "\n", "repo_ids = np.random.randint(len(data), size=(NUM_SAMPLES,))\n", "question_type_ids = np.random.randint(2, size=(NUM_SAMPLES,))\n", "question_ids = np.random.randint(5, size=(NUM_SAMPLES,))\n", "\n", "# evals = []\n", "\n", "for repo_id, question_type_id, question_id in zip(repo_ids, question_type_ids, question_ids):\n", "    repo = data[repo_id]\n", "    sample = repo['documents_with_questions'][question_type_id]\n", "    question = sample['questions'][question_id]['question']\n", "    path = sample['path']\n", "    answer = sample['questions'][question_id]['answers']['gpt_answer']\n", "    \n", "    message = get_repo_prompt(repo['file_list']) + \"\\n\\n\" + format_question(question)\n", "\n", "    chat = GOOGLE_MODEL.start_chat()\n", "    responses = chat.send_message(message, stream=False)\n", "\n", "    evals.append({\n", "        \"repo_index\": repo_id,\n", "        \"question_type_index\": question_type_id,\n", "        \"question_index\": question_id,\n", "        \"path\": path,\n", "        \"question\": question,\n", "        \"gpt_answer\": answer,\n", "        \"gemini_response\": responses.text,\n", "    })"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def parse_gemini_reponse(response):\n", "    lines = response.splitlines(True)\n", "    path = lines[0].strip()\n", "    if \"`\" in path:\n", "        path = re.sub(r\"^`([^`]+)`$\", r\"\\1\", path)\n", "    assert \"/\" in path\n", "    answer = \"\".join(lines[1:]).strip()\n", "    path, answer\n", "    return path, answer\n", "\n", "# for e in evals:\n", "#     e['gemini_path'], e['gemini_answer'] = parse_gemini_reponse(e['gemini_response'])"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/gemini_eval.jsonl', 'w') as f:\n", "    for e in evals:\n", "        e_jsonify = {}\n", "        for k, v in e.items():\n", "            if isinstance(v, np.int64):\n", "                e_jsonify[k] = int(v)\n", "            else:\n", "                e_jsonify[k] = v\n", "        f.write(json.dumps(e_jsonify) + '\\n')"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False: spec/chef/recipes/gitlab-shell_spec.rb and doc/settings/gitlab.yml.md\n", "True: cesiumjs4gwt-main/src/main/java/org/cesiumjs/cs/collections/EntityCollection.java and cesiumjs4gwt-main/src/main/java/org/cesiumjs/cs/collections/EntityCollection.java\n", "True: src/application/view/Timeline.java and src/application/view/Timeline.java\n", "True: include/DTL/Range/RectBaseWithValuePos.hpp and include/DTL/Range/RectBaseWithValuePos.hpp\n", "True: plugins/outsideOnly.cpp and plugins/outsideOnly.cpp\n", "True: Tools/Tester/Utilities/Convert.cpp and Tools/Tester/Utilities/Convert.cpp\n", "True: src/main/java/org/mastodon/graph/ref/ListenableGraphImp.java and src/main/java/org/mastodon/graph/ref/ListenableGraphImp.java\n", "True: windows/FaceRecognitionTest/HistoDistances.h and windows/FaceRecognitionTest/HistoDistances.h\n", "True: src/main/java/com/iot/mgt/util/MemberChecker.java and src/main/java/com/iot/mgt/util/MemberChecker.java\n", "True: src/mutex.h and src/mutex.h\n", "False: spec/chef/recipes/gitlab-shell_spec.rb and doc/settings/gitlab.yml.md\n", "True: cesiumjs4gwt-main/src/main/java/org/cesiumjs/cs/collections/ImageryLayerCollection.java and cesiumjs4gwt-main/src/main/java/org/cesiumjs/cs/collections/ImageryLayerCollection.java\n", "True: src/application/model/annotation/CustomAnnotation.java and src/application/model/annotation/CustomAnnotation.java\n", "False: include/DTL/Range/RectBaseMaze.hpp and Unity/Assets/DungeonTemplateLibrary/Scripts/Range/RectBaseWithValuePos.cs\n", "True: plugins/petcapRemover.cpp and plugins/petcapRemover.cpp\n", "True: Headers/SBScript.h and Headers/SBScript.h\n", "True: src/main/java/org/mastodon/graph/ref/ListenableGraphImp.java and src/main/java/org/mastodon/graph/ref/ListenableGraphImp.java\n", "True: src/DbReader.cpp and src/DbReader.cpp\n", "True: src/main/webapp/resources/dist/c3-0.7.20/src/tooltip.ts and src/main/webapp/resources/dist/c3-0.7.20/src/tooltip.ts\n", "False: src/vm.h and src/vm0.cpp\n"]}], "source": ["for e in evals:\n", "    valid_path = e['path'] == e['gemini_path']\n", "    print(f\"{valid_path}: {e['path']} and {e['gemini_path']}\")\n", "    # if not valid_path:\n", "    #     print(e['path'])\n", "    #     print(e['gemini_path'])\n", "    #     print(e['question'])\n", "    #     print(e['gpt_answer'])\n", "    #     print(e['gemini_answer'])\n", "    #     print()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 20 samples \n"]}], "source": ["import json\n", "import numpy as np\n", "import glob \n", "\n", "ALL_FILES = glob.glob(\"/mnt/efs/augment/user/colin/data/binks/searchqa-01/02_generated_examples/success/*.json\")\n", "NUM_SAMPLES = 20\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(ALL_FILES)\n", "    \n", "samples_per_repo = {}\n", "\n", "for path in ALL_FILES[:NUM_SAMPLES]:\n", "    sample = json.loads(open(path).read())\n", "    repo_uuid = sample['code_edit_data']['repo_uuid']\n", "    if repo_uuid in samples_per_repo:\n", "        print(\"Duplicate\", repo_uuid)\n", "        continue\n", "    samples_per_repo[repo_uuid] = sample\n", "\n", "print(\"Loaded %d samples \" % len(samples_per_repo))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["evals = {}\n", "\n", "with open('/mnt/efs/augment/user/yury/binks/colin_gemini_eval.jsonl') as f:\n", "    for line in f:\n", "        evals[json.loads(line)['repo_name']] = json.loads(line)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2629it [10:17,  4.27it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["503 The service is currently unavailable.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["3503it [11:46,  5.82it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 2838019 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["5651it [13:16, 11.38it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 3652879 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["8662it [17:43, 12.51it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1831946 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["8665it [18:25,  9.87it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1127700 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["10008it [28:44,  2.70it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["503 The service is currently unavailable.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["13309it [31:33,  7.00it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 4034973 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["19808it [33:20,  9.90it/s, n_samples=5, n_non_empty_samples=5]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1526219 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import glob\n", "import tqdm\n", "import os\n", "import pandas as pd\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "# evals = {}\n", "\n", "FILES = '/mnt/efs/augment/user/yury/binks/colin/*.zstd.parquet'\n", "pbar = tqdm.tqdm(ParquetRepoDataset(FILES))\n", "pbar.set_postfix({\"n_samples\": len(evals), \"n_non_empty_samples\": len(list(filter(lambda x: x is not None, evals.values())))})\n", "        \n", "for repo in pbar:\n", "    repo_uuid = repo['repo_uuid']    \n", "    repo_name = repo['max_stars_repo_name']\n", "    if repo_uuid not in samples_per_repo:\n", "        continue\n", "    if repo_name in evals:\n", "        continue\n", "\n", "    sample = samples_per_repo[repo_uuid]\n", "    question = sample['question']\n", "    answer = sample['answer']\n", "    path = sample['code_edit_data']['file_name']\n", "\n", "    message = get_repo_prompt(repo['file_list']) + \"\\n\\n\" + format_question(question)\n", "    chat = GOOGLE_MODEL.start_chat()\n", "    try:\n", "        responses = chat.send_message(message, stream=False)\n", "        evals[repo_name] = {\n", "            \"repo_name\": repo[\"max_stars_repo_name\"],\n", "            \"repo_uuid\": repo_uuid,\n", "            \"path\": path,\n", "            \"question\": question,\n", "            \"answer\": answer,\n", "            \"gemini_response\": responses.text,\n", "        }\n", "    except Exception as e:\n", "        print(e)\n", "        evals[repo_uuid] = None\n", "    \n", "    with open('/mnt/efs/augment/user/yury/binks/colin_gemini_eval_v2.jsonl', 'w') as f:\n", "        for k, e in evals.items():\n", "            if e is None:\n", "                continue\n", "            e_jsonify = {}\n", "            for k, v in e.items():\n", "                if isinstance(v, np.int64):\n", "                    e_jsonify[k] = int(v)\n", "                else:\n", "                    e_jsonify[k] = v\n", "            f.write(json.dumps(e_jsonify) + '\\n')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def parse_gemini_reponse_v2(response):\n", "    lines = response.splitlines(True)\n", "    path = lines[0].strip()\n", "    assert \"/\" in path\n", "    answer = \"\".join(lines[1:]).strip()\n", "    return path, answer\n", "\n", "for e in evals.values():\n", "    if e is not None:\n", "        e['gemini_path'], e['gemini_answer'] = parse_gemini_reponse_v2(e['gemini_response'])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/user/yury/binks/colin_gemini_eval_v2.jsonl', 'w') as f:\n", "    for k, e in evals.items():\n", "        if e is None:\n", "            continue\n", "        e_jsonify = {}\n", "        for k, v in e.items():\n", "            if isinstance(v, np.int64):\n", "                e_jsonify[k] = int(v)\n", "            else:\n", "                e_jsonify[k] = v\n", "        f.write(json.dumps(e_jsonify) + '\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, e in evals.items():\n", "    if e is None:\n", "        continue\n", "    print(\"REPO %s uuid=%s\\n\" % (e['repo_name'], k))\n", "    print(\"     QUESTION:\", e['question'].strip())\n", "    print()\n", "    print(\"        PATHS:\", e['path'].strip())\n", "    print(\" GEMINI PATHS:\", e['gemini_path'].strip().strip(\"`\"))\n", "    print()\n", "    print(\"       ANSWER:\", e[\"answer\"].strip())\n", "    print(\"GEMINI ANSWER:\", e[\"gemini_answer\"].strip())\n", "    print(\"\\n---\\n\")\n", "\n", "# https://gist.github.com/urikz/07bd61e2bcd8d2d42ae907de65bdf1ce"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 20 samples \n"]}], "source": ["import json\n", "import numpy as np\n", "import glob \n", "\n", "ALL_FILES = glob.glob(\"/mnt/efs/augment/user/colin/data/binks/searchqa-01/04_added_path_hints/*.json\")\n", "NUM_SAMPLES = 20\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(ALL_FILES)\n", "    \n", "samples_per_repo = {}\n", "\n", "for path in ALL_FILES[:NUM_SAMPLES]:\n", "    sample = json.loads(open(path).read())\n", "    repo_uuid = sample['chat_example']['repo_uuid']\n", "    if repo_uuid in samples_per_repo:\n", "        print(\"Duplicate\", repo_uuid)\n", "        continue\n", "    samples_per_repo[repo_uuid] = sample\n", "\n", "print(\"Loaded %d samples \" % len(samples_per_repo))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["55it [00:40,  1.46it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1440825 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["576it [02:33,  4.04it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["The model response did not completed successfully.\n", "Finish reason: 3.\n", "Finish message: .\n", "Safety ratings: [category: HARM_CATEGORY_HATE_SPEECH\n", "probability: NEGLIGIBLE\n", "probability_score: 0.3717066\n", "severity: HARM_SEVERITY_LOW\n", "severity_score: 0.2536761\n", ", category: HARM_CATEGORY_DANGEROUS_CONTENT\n", "probability: NEGLIGIBLE\n", "probability_score: 0.3523132\n", "severity: HARM_SEVERITY_LOW\n", "severity_score: 0.24508502\n", ", category: HARM_CATEGORY_HARASSMENT\n", "probability: MEDIUM\n", "blocked: true\n", "probability_score: 0.66290563\n", "severity: HARM_SEVERITY_MEDIUM\n", "severity_score: 0.41063806\n", ", category: HARM_CATEGORY_SEXUALLY_EXPLICIT\n", "probability: NEGLIGIBLE\n", "probability_score: 0.116965584\n", "severity: HARM_SEVERITY_LOW\n", "severity_score: 0.22270013\n", "].\n", "To protect the integrity of the chat session, the request and response were not added to chat history.\n", "To skip the response validation, specify `model.start_chat(response_validation=False)`.\n", "Note that letting blocked or otherwise incomplete responses into chat history might lead to future interactions being blocked by the service.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["3665it [05:12, 14.80it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 5070176 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["4927it [05:43, 18.97it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1115933 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["9513it [07:08, 45.64it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1282953 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["14047it [08:19, 104.64it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1504730 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["16803it [10:01, 24.46it/s, n_samples=0, n_non_empty_samples=0] "]}, {"name": "stdout", "output_type": "stream", "text": ["500 Internal error encountered.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["17305it [10:09, 30.31it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["400 Unable to submit request because the input token count is 1026391 but model only supports up to 1000000. Reduce the input token count and try again. You can also use the CountTokens API to calculate prompt token count and billable characters. Learn more: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models\n"]}, {"name": "stderr", "output_type": "stream", "text": ["18016it [20:16,  2.79it/s, n_samples=0, n_non_empty_samples=0]"]}, {"name": "stdout", "output_type": "stream", "text": ["503 The service is currently unavailable.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["19808it [20:23, 16.19it/s, n_samples=0, n_non_empty_samples=0]\n"]}], "source": ["import glob\n", "import tqdm\n", "import os\n", "import pandas as pd\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "evals = {}\n", "\n", "FILES = '/mnt/efs/augment/user/yury/binks/colin/*.zstd.parquet'\n", "pbar = tqdm.tqdm(ParquetRepoDataset(FILES))\n", "pbar.set_postfix({\"n_samples\": len(evals), \"n_non_empty_samples\": len(list(filter(lambda x: x is not None, evals.values())))})\n", "\n", "for repo in pbar:\n", "    repo_uuid = repo['repo_uuid']    \n", "    repo_name = repo['max_stars_repo_name']\n", "    if repo_uuid not in samples_per_repo:\n", "        continue\n", "    if repo_name in evals:\n", "        continue\n", "\n", "    sample = samples_per_repo[repo_uuid]\n", "    question = sample['new_question']\n", "    answer = sample['new_answer']\n", "    path = sample['chat_example']['filepath']\n", "\n", "    message = get_repo_prompt(repo['file_list']) + \"\\n\\n\" + format_question(question)\n", "    chat = GOOGLE_MODEL.start_chat()\n", "    try:\n", "        responses = chat.send_message(message, stream=False)\n", "        evals[repo_name] = {\n", "            \"repo_name\": repo[\"max_stars_repo_name\"],\n", "            \"repo_uuid\": repo_uuid,\n", "            \"path\": path,\n", "            \"question\": question,\n", "            \"answer\": answer,\n", "            \"gemini_response\": responses.text,\n", "        }\n", "    except Exception as e:\n", "        print(e)\n", "        evals[repo_uuid] = None\n", "    \n", "    with open('/mnt/efs/augment/user/yury/binks/colin_with_path_hints_gemini_eval_v2.jsonl', 'w') as f:\n", "        for k, e in evals.items():\n", "            if e is None:\n", "                continue\n", "            e_jsonify = {}\n", "            for k, v in e.items():\n", "                if isinstance(v, np.int64):\n", "                    e_jsonify[k] = int(v)\n", "                else:\n", "                    e_jsonify[k] = v\n", "            f.write(json.dumps(e_jsonify) + '\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for e in evals.values():\n", "    if e is not None:\n", "        e['gemini_path'], e['gemini_answer'] = parse_gemini_reponse_v2(e['gemini_response'])\n", "        \n", "for k, e in evals.items():\n", "    if e is None:\n", "        continue\n", "    print(\"REPO %s uuid=%s\\n\" % (e['repo_name'], k))\n", "    print(\"     QUESTION:\", e['question'].strip())\n", "    print()\n", "    print(\"        PATHS:\", e['path'].strip())\n", "    print(\" GEMINI PATHS:\", e['gemini_path'].strip().strip(\"`\"))\n", "    print()\n", "    print(\"       ANSWER:\", e[\"answer\"].strip())\n", "    print(\"GEMINI ANSWER:\", e[\"gemini_answer\"].strip())\n", "    print(\"\\n---\\n\")\n", "\n", "# https://gist.github.com/urikz/f021f111934a737c1262d9b94819b13c"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
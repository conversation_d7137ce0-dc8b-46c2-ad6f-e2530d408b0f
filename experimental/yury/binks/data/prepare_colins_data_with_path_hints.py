"""Script to generate answers given a repo and a set of questions."""

import dataclasses
import glob
import json

import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import tqdm

from experimental.yury.data.processing import CachingPool, PersistentJSONFileCache


class ParquetRepoDataset:
    """
    A class to iterate over repositories stored in Parquet files.

    Attributes:
        paths (list): A list of file paths to Parquet files.
        columns (list): A list of column names to be read from the Parquet files.
        chunksize (int): The number of rows to read at a time from each Parquet file.
    """

    def __init__(self, pattern, columns=None, chunksize=1000):
        """
        Initializes the class with the pattern for file paths, the columns to be read, and the chunk size.

        Args:
            pattern (str): A glob pattern to match Parquet file paths.
            columns (list): A list of column names to be read from the Parquet files.
            chunksize (int): The number of rows to read at a time from each Parquet file.
        """
        self.paths = glob.glob(str(pattern))
        assert len(self.paths) > 0, f"No files found for pattern {pattern}"
        self.columns = columns
        self.chunksize = chunksize

    def __iter__(self):
        """
        Iterates over the repositories in the Parquet files.

        Yields:
            repo (pandas.Series): A pandas Series representing a repository.
        """
        for path in self.paths:
            parquet_file = pq.ParquetFile(path)
            for batch in parquet_file.iter_batches(
                columns=self.columns, batch_size=self.chunksize
            ):
                df = batch.to_pandas()
                for _, repo in df.iterrows():
                    yield repo


class RepoQuestionCache(PersistentJSONFileCache):
    def _construct_key(self, args, kwargs):
        repo, questions = args
        return (repo["repo_uuid"], questions)


def load_questions(pattern):
    n_samples, samples_per_repo = 0, {}
    for path in glob.glob(pattern):
        sample = json.loads(open(path).read())
        n_samples += 1
        repo_uuid = sample['chat_example']['repo_uuid']
        if repo_uuid not in samples_per_repo:
            samples_per_repo[repo_uuid] = []
        samples_per_repo[repo_uuid].append(sample)
    print("Loaded %d samples for %d repos" % (n_samples, len(samples_per_repo)))
    return samples_per_repo


FILEDS_TO_COVERT_TO_LISTS = [
   'max_stars_repo_licenses',
   'max_issues_repo_licenses',
   'max_forks_repo_licenses',
]


def repo_to_dict(repo):
    repo_d = repo.to_dict()
    repo_d['file_list'] = repo_d['file_list'].tolist()
    for file in repo_d['file_list']:
        for k in FILEDS_TO_COVERT_TO_LISTS:
            if not isinstance(file[k], list):
                file[k] = file[k].tolist()
    return repo_d


def join_questions_with_repos(questions_per_repo, repos_pattern):
    for repo in ParquetRepoDataset(repos_pattern):
        if repo['repo_uuid'] not in questions_per_repo:
            continue
        yield (repo, questions_per_repo[repo['repo_uuid']])


@dataclasses.dataclass
class QASample:
    question: str
    paths: list[str]
    answer: str


questions_per_repo = load_questions("/mnt/efs/augment/user/colin/data/binks/searchqa-01/04_added_path_hints/*.json")
FILES = "/mnt/efs/augment/user/yury/binks/colin/*.zstd.parquet"
pbar = tqdm.tqdm(join_questions_with_repos(questions_per_repo, FILES))

np.random.seed(31415)

# with open("/mnt/efs/augment/user/yury/binks/binks-v2-colin/repos_with_qa_with_path_hints.jsonl", "w") as f:
with open("/mnt/efs/augment/user/yury/binks/binks-v2-colin/repos_with_qa_original.jsonl", "w") as f:
    for repo, questions in pbar:
        qa_samples = []
        for question in questions:
            # if question["added_path"]:
            question_text = question['new_question'].strip()
            strip_ticks = np.random.random() < 0.5
            if strip_ticks:
                question_text = question_text.replace("`", "")
            qa_sample = QASample(
                question=question_text,
                paths=[question['chat_example']['filepath']],
                answer=question['new_answer'].strip(),
            )
            qa_samples.append(qa_sample)
        if len(qa_samples) > 0:
            repo_d = repo_to_dict(repo)
            repo_d["documents_with_questions"] = [dataclasses.asdict(sample) for sample in qa_samples]
            sample_json = json.dumps(repo_d)
            f.write(sample_json + "\n")

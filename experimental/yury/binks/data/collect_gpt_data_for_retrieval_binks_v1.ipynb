{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 8457 questions\n"]}], "source": ["import json\n", "OUTPUT_PATH = '/mnt/efs/augment/user/yury/binks/retrieval-binks-v1/qa_data.v1.jsonl'\n", "PATHS = [\n", "    '/mnt/efs/augment/user/yury/binks/binks-v1/docs.jsonl',\n", "    '/mnt/efs/augment/user/yury/binks/binks-v2/docs.jsonl',\n", "]\n", "\n", "questions = []\n", "\n", "for path in PATHS:\n", "    with open(path) as f:\n", "        for line in f:\n", "            datum = json.loads(line[:-1])\n", "            for question in datum['questions']:\n", "                questions.append({\n", "                    'file_path': datum['path'],\n", "                    'file_content': datum['text'],\n", "                    'question': question['question'],\n", "                    'answer': question['answers']['gpt_answer'],\n", "                })\n", "\n", "print('Loaded %d questions' % len(questions))\n", "\n", "with open(OUTPUT_PATH, 'w') as f:\n", "    for question in questions:\n", "        f.write(json.dumps(question) + '\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
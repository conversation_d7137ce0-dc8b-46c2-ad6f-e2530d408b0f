from typing import Any
import argparse
import json
import multiprocessing
from base.caching.lru_cache_test import CONTENT
import experimental.yury.data.processing as utils
from pathlib import Path
import tqdm
from dataclasses import dataclass, field

ID_COLUMN = "repo_uuid"
CONTENT_COLUMN = "content"
GPT_ANSWER_KEY = "gpt_answer"
PATH_COLUMN = "max_stars_repo_path"

QA_SIMPLE_CACHE_FILENAME = "generate_qa_simple.jsonl"
HOWTO_CACHE_FILENAME = "generate_howto.jsonl"

from experimental.dxy.edits.api_lib import generate_response_via_chat


N_QUESTIONS = 5


QA_SIMPLE_PROMPT = """Your task is two fold.

First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, a software engineer who asks these questions does NOT see the actual source code.

Here are some examples of question templates (where X and Y are some placeholder)
- How do we handle X in the Y?
- What happens if we do X while calling Y?
- Where do we perform X?
- Does Y have the property X?
- How do I specify X in Y?
- Where in Y do we perform X?
- What X is for?
- Where Y does X?
- How does X is implemented in Y?

Notice the simple structure of these questions and their information seeking nature.

Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer.

Format the output in the following way.
First, write a section names "QUESTIONS ONLY" and list each question on a separate line.
Second, write a section name "QUESTION AND ANSWERS"
Third, for every section in the "QUESTIONS ONLY" section write "QUESTION:" followed by question itself, and, on the next line, "ANSWER:" followed by answer to this question.

Below is a code snippet from the file located at `{path}`

```
{file_content}
```
"""


def generate_qa_simple_with_gpt(repo_uuid_and_file):
    repo_uuid, file = repo_uuid_and_file
    qa_prompt = QA_SIMPLE_PROMPT.format(
        n_questions=N_QUESTIONS,
        path=file[PATH_COLUMN],
        file_content=file[CONTENT_COLUMN],
    )
    return (generate_response_via_chat(
        [qa_prompt],
        num_completion=1,
        temperature=0.8,
        model="gpt-4-1106-preview",
        max_tokens=4096,
    )[0], repo_uuid, file[PATH_COLUMN])



HOWTO_PROMPT = """Your task is two fold.

First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, this software engineer is trying to use some of the API from the file, but need assistance. However, they does NOT see the actual source code.

Here are some examples of question templates (where ,X, Y and Z are some placeholder)
- How do I do X?
- Which method / class shall I use to do X?
- Which functionality have we available in Y?
- Which argument shall I to function X to perform Z?
- How do I specify X in Y?

Notice the simple structure of these questions and their information seeking nature.

Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer. Additionally, include a concise code snippet with either relevant code excerpt or a practical example.

Format the output in the following way.
First, write a section names "QUESTIONS ONLY" and list each question on a separate line.
Second, write a section name "QUESTION AND ANSWERS"
Third, for every section in the "QUESTIONS ONLY" section write "QUESTION:" followed by question itself, and, on the next line, "ANSWER:" followed by answer to this question.

Below is a code snippet from the file located at `{path}`

```
{file_content}
```
"""

def generate_howto_with_gpt(repo_uuid_and_file):
    repo_uuid, file = repo_uuid_and_file
    qa_prompt = HOWTO_PROMPT.format(
        n_questions=N_QUESTIONS,
        path=file[PATH_COLUMN],
        file_content=file[CONTENT_COLUMN],
    )
    return (generate_response_via_chat(
        [qa_prompt],
        num_completion=1,
        temperature=0.8,
        model="gpt-4-1106-preview",
        max_tokens=4096,
    )[0], repo_uuid, file[PATH_COLUMN])



@dataclass
class Question:
    question: str
    retrievals: list[Any] = field(default_factory=list)
    answers: dict[str, str] = field(default_factory=dict)


@dataclass
class DocumentWithQuestions:
    repo_uuid: str
    qa_set_name: str
    path: str
    text: str
    questions: list[Question] = field(default_factory=list)


QUESTION_PREFIX = "QUESTION:"
ANSWER_PREFIX = "ANSWER:"


def parse_gpt_qa_output(text: str) -> list[Question]:
    data = []
    question_index = text.find(QUESTION_PREFIX)
    while question_index != -1:
        answer_index = text.find(ANSWER_PREFIX, question_index + 1)
        assert answer_index != -1
        next_question_index = text.find(QUESTION_PREFIX, answer_index + 1)
        end_of_the_current_question = next_question_index if next_question_index != -1 else len(text)
        question = text[question_index:answer_index][len(QUESTION_PREFIX):].strip()
        answer = text[answer_index:end_of_the_current_question][len(ANSWER_PREFIX):].strip()
        data.append(Question(
            question=question,
            answers={
                GPT_ANSWER_KEY: answer,
            }
        ))
        question_index = next_question_index
    return data


# def generate_qa_simple(repo_uuid: str, file):
#     qa_text = generate_qa_simple_with_gpt(file)
#     doc = DocumentWithQuestions(
#         repo_uuid=repo_uuid,
#         qa_set_name=file[PATH_COLUMN] + ' simple QA',
#         path=file[PATH_COLUMN],
#         text=file[CONTENT_COLUMN],
#         questions=parse_gpt_qa_output(qa_text),
#     )
#     return doc


# def generate_howto(repo_uuid: str, file):
#     qa_text = generate_howto_with_gpt(file)
#     doc = DocumentWithQuestions(
#         repo_uuid=repo_uuid,
#         qa_set_name=file[PATH_COLUMN] + ' How-To',
#         path=file[PATH_COLUMN],
#         text=file[CONTENT_COLUMN],
#         questions=parse_gpt_qa_output(qa_text),
#     )
#     return doc


def map_maybe_in_parallel(func, inputs, num_processes):
    assert num_processes > 0
    if num_processes == 1:
        for result in map(func, inputs):
            yield result
    else:
        with multiprocessing.Pool(processes=num_processes) as pool:
            for result in pool.imap(func, inputs):
                yield result


def run_pipeline(
    input_path: Path,
    output_path: Path,
    num_processes: int,
):
    qa_simple_cache = utils.PersistentJSONFileCache(output_path.parent / QA_SIMPLE_CACHE_FILENAME)
    howto_cache = utils.PersistentJSONFileCache(output_path.parent / HOWTO_CACHE_FILENAME)

    qa_simple_new_files = []
    howto_new_files = []
    with input_path.open() as f:
        for line in f:
            file = json.loads(line[:-1])
            repo_uuid = file[ID_COLUMN]
            qa_simple_file, howto_file = file["files"]

            if not qa_simple_cache.exists(repo_uuid, qa_simple_file[PATH_COLUMN]):
                qa_simple_new_files.append((repo_uuid, qa_simple_file))
            if not howto_cache.exists(repo_uuid, howto_file[PATH_COLUMN]):
                howto_new_files.append((repo_uuid, howto_file))

    print('Found unprocessed %d files for simple QA' % len(qa_simple_new_files))
    print('Found unprocessed %d files for How-To' % len(howto_new_files))

#     if num_processes <= 1:
#         for task in tqdm(tasks):
#             filter_sample(task)
#     else:
#         with Pool(num_processes) as pool:
#             _ = list(tqdm(pool.imap(filter_sample, tasks), total=len(tasks)))

    for response, repo_uuid, path in tqdm.tqdm(
        map_maybe_in_parallel(generate_howto_with_gpt, howto_new_files, num_processes),
        total=len(howto_new_files),
    ):
        howto_cache.add(response, repo_uuid, path)

    for response, repo_uuid, path in tqdm.tqdm(
        map_maybe_in_parallel(generate_qa_simple_with_gpt, qa_simple_new_files, num_processes),
        total=len(qa_simple_new_files),
    ):
        qa_simple_cache.add(response, repo_uuid, path)





def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="path to the input JSONL file")
    parser.add_argument("--output", type=str, help="path to the output directory")
    parser.add_argument(
        "--num_processes",
        type=int,
        default=1,
        help="number of processes to parallelize requests",
    )
    args = parser.parse_args()

    run_pipeline(
        Path(args.input),
        Path(args.output),
        args.num_processes,
    )


if __name__ == "__main__":
    main()

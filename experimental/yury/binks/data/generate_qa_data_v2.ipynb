{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "import tqdm\n", "import dataclasses\n", "from types import SimpleNamespace"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v2\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "QA_DATA_V1 = BASE_PATH / \"binks-v1.3-merged/repos_with_qa.jsonl\"\n", "EVOL_CODEALPACA = BASE_PATH / \"binks-v1.3-merged/evol_codealpaca_v1_5K.jsonl\"\n", "\n", "QA_DATA_V1_WITH_RETRIEVALS = OUTPUT_PATH / \"repos_with_qa_with_retrievals.jsonl\"\n", "EVOL_CODEALPACA_WITH_RETRIEVALS = OUTPUT_PATH / \"evol_codealpaca_v1_5K_with_retrievals.jsonl\"\n", "    \n", "config = SimpleNamespace(\n", "    **{\n", "        \"repo_languages\": REPO_LANGUAGES,\n", "        \"sample_languages\": SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": RETRIEVAL_LANGUAGES,\n", "        \"limit_repos_from_the_stack\": 500,\n", "        \"limit_repos\": 200,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"min_lines_per_file\": 200,\n", "        \"max_lines_per_file\": 1000,\n", "        \"file_max_size\": 10000,\n", "        \"n_questions_per_file\": 5,\n", "        \"random_seed\": 31415,    \n", "        \"n_retrievals\": 25,\n", "        \"p_local_context_dropout\": 0.05,\n", "        \"p_empty_selection\": 0.45,\n", "        \"max_lines_per_selected_code\": 50,\n", "        \"max_seq_length\": 16384 + 1,\n", "        \"num_validation_samples\": 200,\n", "    }\n", ")\n", "\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=2048,\n", "    prefix_len=1536,\n", "    selected_code_len=1536,\n", "    chat_history_len=1536,\n", "    suffix_len=1024,\n", "    max_prompt_len=16384 - 2048,  # 2048 represents the max output tokens\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"add_path\": <PERSON>als<PERSON>,\n", "        \"max_tokens\": 1023,        \n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    }\n", "}\n", "\n", "retrieval_database = create_retriever(retriever_config)\n", "retrieval_database.scorer.load()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5000it [6:11:15,  4.46s/it]\n", "68it [03:33, 11.85s/it]Token indices sequence length is longer than the specified maximum sequence length for this model (1482 > 1024). Running this sequence through the model will result in indexing errors\n", "5000it [4:36:33,  3.32s/it] \n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.retrieval.types import Document\n", "from research.core.types import Chunk\n", "\n", "import random\n", "random.seed(config.random_seed)\n", "\n", "def get_retrievals(repo, query, top_k: int):\n", "    global retrieval_database\n", "\n", "    retrieval_database.remove_all_docs()\n", "\n", "    for file in repo[FILE_LIST_COLUMN]:\n", "        if file[FILE_LANG_COLUMN] in config.retrieval_languages:\n", "            path = file[PATH_COLUMN]\n", "            document = Document(\n", "                id=path, text=file[CONTENT_COLUMN], path=path\n", "            )\n", "            retrieval_database.add_doc(document)\n", "\n", "    \n", "    retrieved_chunks, _ = retrieval_database.query(\n", "        model_input=ModelInput(\n", "            prefix=\"\",\n", "            suffix=\"\",\n", "            path=\"\",\n", "            extra={\n", "                \"message\": query,\n", "            }\n", "        ),\n", "        top_k=top_k,\n", "    )        \n", "    retrieved_chunks = [dataclasses.asdict(r) for r in retrieved_chunks]\n", "    return retrieved_chunks\n", "\n", "with QA_DATA_V1_WITH_RETRIEVALS.open('w') as f_out:\n", "    with QA_DATA_V1.open('r') as f_in:\n", "        for line in tqdm.tqdm(f_in):\n", "            sample = json.loads(line[:-1])\n", "\n", "            questions_set = random.choice(sample['documents_with_questions'])\n", "            question = random.choice(questions_set['questions'])\n", "            del questions_set['questions']\n", "            questions_set.update(question)\n", "            sample['sampled_documents_with_questions'] = questions_set\n", "            del sample['documents_with_questions']\n", "\n", "            query = sample['sampled_documents_with_questions']['question']            \n", "            retrieved_chunks = get_retrievals(sample, query, config.n_retrievals)\n", "            \n", "            sample['retrieved_chunks'] = retrieved_chunks\n", "            f_out.write(json.dumps(sample) + \"\\n\")\n", "\n", "with EVOL_CODEALPACA_WITH_RETRIEVALS.open('w') as f_out:\n", "    with EVOL_CODEALPACA.open('r') as f_in:\n", "        for line in tqdm.tqdm(f_in):\n", "            sample = json.loads(line[:-1])\n", "            query = sample['instructions']['instruction']\n", "            retrieved_chunks = get_retrievals(sample, query, config.n_retrievals)  \n", "            sample['retrieved_chunks'] = retrieved_chunks\n", "            f_out.write(json.dumps(sample) + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.binks_prompt_formatter import BinksChatPromptFormatter\n", "from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "prompt_formatter = BinksChatPromptFormatter(tokenizer, TOKEN_APPORTIONMENT)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["import random \n", "import numpy as np\n", "\n", "random.seed(config.random_seed)\n", "\n", "def can_generate_samples_from_file(file):\n", "    if file[FILE_LANG_COLUMN] not in config.sample_languages:\n", "        return False\n", "    if file[SIZE_COLUMN] > config.file_max_size:\n", "        return False\n", "    n_lines = len(file[CONTENT_COLUMN].splitlines())\n", "    return (\n", "        n_lines >= config.min_lines_per_file\n", "        and n_lines <= config.max_lines_per_file\n", "    )\n", "\n", "def pick_files_per_repo(repo, n_files):\n", "    candidates = [\n", "        file\n", "        for file in repo[FILE_LIST_COLUMN]\n", "        if can_generate_samples_from_file(file)\n", "    ]\n", "    if len(candidates) < n_files:\n", "        return None\n", "    files = random.sample(candidates, n_files)\n", "    files_json_friendly = []\n", "    for file in files:\n", "        file_json_friendly = {}\n", "        for k, v in file.items():\n", "            if isinstance(v, np.ndarray):\n", "                file_json_friendly[k] = v.tolist()\n", "            else:\n", "                file_json_friendly[k] = v\n", "        files_json_friendly.append(file_json_friendly)\n", "    return files_json_friendly"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'path': 'alchemy-core/src/test/java/io/rtr/alchemy/models/ExperimentsTest.java',\n", " 'prefix': 'package io.rtr.alchemy.models;\\n\\nimport com.google.common.collect.ImmutableMap;\\nimport com.google.common.collect.Sets;\\nimport io.rtr.alchemy.caching.CacheStrategy;\\nimport io.rtr.alchemy.db.ExperimentsCache;\\nimport io.rtr.alchemy.db.ExperimentsStore;\\nimport io.rtr.alchemy.db.ExperimentsStoreProvider;\\nimport io.rtr.alchemy.db.Filter;\\nimport io.rtr.alchemy.filtering.FilterExpression;\\nimport io.rtr.alchemy.identities.Attributes;\\nimport io.rtr.alchemy.identities.AttributesMap;\\nimport io.rtr.alchemy.identities.Identity;\\nimport org.junit.Before;\\nimport org.junit.Test;\\n\\nimport javax.validation.ValidationException;\\nimport java.util.Collections;\\nimport java.util.Set;\\n\\nimport static org.junit.Assert.assertNotNull;\\nimport static org.junit.Assert.assertNull;\\nimport static org.mockito.Matchers.any;\\nimport static org.mockito.Matchers.eq;\\nimport static org.mockito.Mockito.doReturn;\\nimport static org.mockito.Mockito.mock;\\nimport static org.mockito.Mockito.reset;\\nimport static org.mockito.Mockito.verify;\\nimport static org.mockito.Mockito.verifyZeroInteractions;\\n\\npublic class ExperimentsTest {\\n    private ExperimentsStore store;\\n    private ExperimentsCache cache;\\n    private Experiments experiments;\\n\\n    @Attributes({\"foo\", \"bar\"})\\n    private static class MyIdentity extends Identity {\\n        private final Set<String> attributes;\\n\\n        public MyIdentity(String ... attributes) {\\n            this.attributes = Sets.newHashSet(attributes);\\n        }\\n\\n        @Override\\n        public AttributesMap computeAttributes() {\\n            final AttributesMap.Builder builder = attributes();\\n\\n            for (String name : attributes) {\\n                builder.put(name, true);\\n            }\\n\\n            return builder.build();\\n        }\\n    }\\n\\n    @Before\\n    public void setUp() {\\n        final ExperimentsStoreProvider provider = mock(ExperimentsStoreProvider.class);\\n        store = mock(ExperimentsStore.class);\\n        cache = mock(ExperimentsCache.class);\\n        doReturn(store).when(provider).getStore();\\n        doReturn(cache).when(provider).getCache();\\n        experiments =\\n            Experiments\\n                .using(provider)\\n                .using(mock(CacheStrategy.class))\\n                .build();\\n\\n        // suppress initial invalidateAll() call\\n        reset(cache);\\n    }\\n\\n    @Test\\n    public void testGetActiveTreatment() {\\n        final Identity identity = mock(Identity.class);\\n        final Experiment experiment = mock(Experiment.class);\\n        doReturn(AttributesMap.empty()).when(identity).computeAttributes();\\n        doReturn(FilterExpression.alwaysTrue()).when(experiment).getFilter();\\n        doReturn(ImmutableMap.of(\"foo\", experiment))\\n            .when(cache)\\n            .getActiveExperiments();\\n        experiments.getActiveTreatment(\"foo\", identity);\\n        verifyZeroInteractions(store);\\n        verify(cache).getActiveExperiments();\\n    }\\n\\n    @Test\\n    public void testGetActiveTreatmentUnspecifiedAttributes() throws ValidationException {\\n        final MyIdentity identity1 = new MyIdentity(\"foo\", \"bar\", \"baz\");\\n        final MyIdentity identity2 = new MyIdentity(\"baz\");\\n        final MyIdentity identity3 = new MyIdentity(\"foo\");\\n\\n        final Experiment exp1 =\\n            experiments\\n                .create(\"exp1\")\\n                .addTreatment(\"control\")\\n                .allocate(\"control\", 100)\\n                .setFilter(FilterExpression.of(\"baz\"))\\n                .activate()\\n                .save();\\n\\n        final Experiment exp2 =\\n            experiments\\n                .create(\"exp2\")\\n                .addTreatment(\"control\")\\n                .allocate(\"control\", 100)\\n                .setFilter(FilterExpression.of(\"bar\"))\\n                .activate()\\n                .save();\\n\\n        doReturn(\\n            ImmutableMap.of(\\n                \"exp1\", exp1,\\n                \"exp2\", exp2\\n            )\\n        ).when(cache).getActiveExperiments();\\n\\n        // baz was not specified in @Attributes\\n        assertNull(experiments.getActiveTreatment(\"exp1\", identity1));\\n        assertNull(experiments.getActiveTreatment(\"exp1\", identity2));\\n\\n        // bar was specified in @Attributes\\n        assertNotNull(experiments.getActiveTreatment(\"exp2\", identity1));\\n        assertNull(experiments.getActiveTreatment(\"exp2\", identity2));\\n        assertNull(experiments.getActiveTreatment(\"exp2\", identity3));\\n    }\\n\\n    @Test\\n    public void testGetActiveTreatmentNoAttributes() throws ValidationException {\\n        final Identity identity = mock(Identity.class);\\n        doReturn(AttributesMap.empty()).when(identity).computeAttributes();\\n\\n        final Experiment exp =\\n            experiments\\n                .create(\"exp\")\\n                .addTreatment(\"control\")\\n                .allocate(\"control\", 100)\\n                .setFilter(FilterExpression.of(\"baz\"))\\n                .activate()\\n                .save();\\n\\n        doReturn(\\n            ImmutableMap.of(\\n                \"exp\", exp\\n            )\\n        ).when(cache).getActiveExperiments();\\n\\n        // identity does not have @Attributes\\n        assertNull(experiments.getActiveTreatment(\"exp\", identity));\\n    }\\n\\n    @Test\\n    public void testGetActiveTreatments() {\\n        final Experiment experiment = mock(Experiment.class);\\n        final Identity identity = mock(Identity.class);\\n        doReturn(FilterExpression.alwaysTrue()).when(experiment).getFilter();\\n        doReturn(AttributesMap.empty()).when(identity).computeAttributes();\\n        doReturn(ImmutableMap.of(\"foo\", experiment))\\n            .when(cache)\\n            .getActiveExperiments();\\n',\n", " 'selected_code': '        experiments.getActiveTreatments(identity);\\n        verifyZeroInteractions(store);\\n        verify(cache).getActiveExperiments();\\n    }\\n\\n    @Test\\n    public void testGetActiveExperiments() {\\n        experiments.getActiveExperiments();\\n',\n", " 'suffix': '        verifyZeroInteractions(store);\\n        verify(cache).getActiveExperiments();\\n    }\\n\\n    @Test\\n    public void testCreate() throws ValidationException {\\n        experiments.create(\"foo\");\\n        verifyZeroInteractions(store);\\n        verifyZeroInteractions(cache);\\n    }\\n\\n    @Test\\n    public void testSave() throws ValidationException {\\n        final Experiment experiment = experiments.create(\"foo\").save();\\n        verify(store).save(eq(experiment));\\n        verifyZeroInteractions(cache);\\n    }\\n\\n    @Test\\n    public void testFind() {\\n        experiments.find();\\n        verify(store).find(eq(Filter.NONE), any(Experiment.BuilderFactory.class));\\n        verifyZeroInteractions(cache);\\n    }\\n\\n    @Test\\n    public void testFindFiltered() {\\n        final Filter filter = Filter.criteria().filter(\"foo\").offset(1).limit(2).build();\\n        experiments.find(filter);\\n        verify(store).find(eq(filter), any(Experiment.BuilderFactory.class));\\n        verifyZeroInteractions(cache);\\n    }\\n\\n    @Test\\n    public void testDelete() {\\n        experiments.delete(\"foo\");\\n        verify(store).delete(eq(\"foo\"));\\n        verifyZeroInteractions(cache);\\n    }\\n\\n    @Test\\n    public void testValidateOnSave() {\\n        final Treatment treatment = mock(Treatment.class);\\n        final TreatmentOverride override = mock(TreatmentOverride.class);\\n        final Experiment experiment = mock(Experiment.class);\\n\\n        doReturn(Collections.singletonList(treatment)).when(experiment).getTreatments();\\n        doReturn(Collections.singletonList(override)).when(experiment).getOverrides();\\n\\n        experiments.save(experiment);\\n\\n        verify(experiment).validateName();\\n        verify(treatment).validateName();\\n        verify(override).validateName();\\n    }\\n}\\n'}"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "\n", "\n", "def sample_current_file(repo, p_local_context_dropout, p_empty_selection, max_lines_per_selected_code):\n", "    random_score = random.random()\n", "\n", "    if random_score < p_local_context_dropout:\n", "        # In 0.5% of cases we don't provide selected code\n", "        return {\n", "            \"path\": \"\",\n", "            \"prefix\": \"\",\n", "            \"selected_code\": \"\",\n", "            \"suffix\": \"\",\n", "        }\n", "\n", "    file = pick_files_per_repo(repo, 1)\n", "    if file is None:\n", "        # print(\"WARNING: Didn't find suitable file!\")\n", "        return {\n", "            \"path\": \"\",\n", "            \"prefix\": \"\",\n", "            \"selected_code\": \"\",\n", "            \"suffix\": \"\",\n", "        }\n", "\n", "    assert file is not None\n", "    file = file[0]\n", "    file_lines = file[CONTENT_COLUMN].splitlines(True)\n", "    assert len(file_lines) > 1\n", "    if len(file_lines) <= 1:\n", "        selected_code_start, selected_code_end = 0, 1\n", "    else:\n", "        selected_code_start, selected_code_end = random.sample(range(len(file_lines)), 2)\n", "    \n", "        if selected_code_end < selected_code_start:\n", "            selected_code_start, selected_code_end = selected_code_end, selected_code_start\n", "        selected_code_end = min(selected_code_end, selected_code_start + max_lines_per_selected_code)\n", "    \n", "\n", "    if random_score < p_empty_selection:        \n", "        # In half of the cases, we set selected code to be empty    \n", "        selected_code_end = selected_code_start\n", "\n", "    prefix = \"\".join(file_lines[:selected_code_start])\n", "    selected_code = \"\".join(file_lines[selected_code_start:selected_code_end])\n", "    suffix = \"\".join(file_lines[selected_code_end:])\n", "    return {\n", "        \"path\": file[PATH_COLUMN],\n", "        \"prefix\": prefix,\n", "        \"selected_code\": selected_code,\n", "        \"suffix\": suffix,\n", "    }\n", "\n", "sample_current_file(sample, config.p_local_context_dropout, config.p_empty_selection, config.max_lines_per_selected_code)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'repo_uuid': '8a33f4f0-00c4-4d5a-84b8-9683cf0df914',\n", " 'qa_set_name': 'How-To',\n", " 'path': 'cmd/cloudflared/tunnel/subcommands_test.go',\n", " 'text': 'package tunnel\\n\\nimport (\\n\\t\"encoding/base64\"\\n\\t\"encoding/json\"\\n\\t\"path/filepath\"\\n\\t\"testing\"\\n\\n\\t\"github.com/google/uuid\"\\n\\thomedir \"github.com/mitchellh/go-homedir\"\\n\\t\"github.com/stretchr/testify/assert\"\\n\\t\"github.com/stretchr/testify/require\"\\n\\n\\t\"github.com/cloudflare/cloudflared/cfapi\"\\n\\t\"github.com/cloudflare/cloudflared/connection\"\\n)\\n\\nfunc Test_fmtConnections(t *testing.T) {\\n\\ttype args struct {\\n\\t\\tconnections []cfapi.Connection\\n\\t}\\n\\ttests := []struct {\\n\\t\\tname string\\n\\t\\targs args\\n\\t\\twant string\\n\\t}{\\n\\t\\t{\\n\\t\\t\\tname: \"empty\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\tconnections: []cfapi.Connection{},\\n\\t\\t\\t},\\n\\t\\t\\twant: \"\",\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"trivial\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\tconnections: []cfapi.Connection{\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName: \"DFW\",\\n\\t\\t\\t\\t\\t\\tID:       uuid.MustParse(\"ea550130-57fd-4463-aab1-752822231ddd\"),\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t},\\n\\t\\t\\t},\\n\\t\\t\\twant: \"1xDFW\",\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"with a pending reconnect\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\tconnections: []cfapi.Connection{\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName:           \"DFW\",\\n\\t\\t\\t\\t\\t\\tID:                 uuid.MustParse(\"ea550130-57fd-4463-aab1-752822231ddd\"),\\n\\t\\t\\t\\t\\t\\tIsPendingReconnect: true,\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t},\\n\\t\\t\\t},\\n\\t\\t\\twant: \"\",\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"many colos\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\tconnections: []cfapi.Connection{\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName: \"YRV\",\\n\\t\\t\\t\\t\\t\\tID:       uuid.MustParse(\"ea550130-57fd-4463-aab1-752822231ddd\"),\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName: \"DFW\",\\n\\t\\t\\t\\t\\t\\tID:       uuid.MustParse(\"c13c0b3b-0fbf-453c-8169-a1990fced6d0\"),\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName: \"ATL\",\\n\\t\\t\\t\\t\\t\\tID:       uuid.MustParse(\"70c90639-e386-4e8d-9a4e-7f046d70e63f\"),\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tColoName: \"DFW\",\\n\\t\\t\\t\\t\\t\\tID:       uuid.MustParse(\"30ad6251-0305-4635-a670-d3994f474981\"),\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t},\\n\\t\\t\\t},\\n\\t\\t\\twant: \"1xATL, 2xDFW, 1xYRV\",\\n\\t\\t},\\n\\t}\\n\\tfor _, tt := range tests {\\n\\t\\tt.Run(tt.name, func(t *testing.T) {\\n\\t\\t\\tif got := fmtConnections(tt.args.connections, false); got != tt.want {\\n\\t\\t\\t\\tt.Errorf(\"fmtConnections() = %v, want %v\", got, tt.want)\\n\\t\\t\\t}\\n\\t\\t})\\n\\t}\\n}\\n\\nfunc TestTunnelfilePath(t *testing.T) {\\n\\ttunnelID, err := uuid.Parse(\"f48d8918-bc23-4647-9d48-082c5b76de65\")\\n\\tassert.NoError(t, err)\\n\\toriginCertDir := filepath.Dir(\"~/.cloudflared/cert.pem\")\\n\\tactual, err := tunnelFilePath(tunnelID, originCertDir)\\n\\tassert.NoError(t, err)\\n\\thomeDir, err := homedir.Dir()\\n\\tassert.NoError(t, err)\\n\\texpected := filepath.Join(homeDir, \".cloudflared\", tunnelID.String()+\".json\")\\n\\tassert.Equal(t, expected, actual)\\n}\\n\\nfunc TestValidateName(t *testing.T) {\\n\\ttests := []struct {\\n\\t\\tname string\\n\\t\\twant bool\\n\\t}{\\n\\t\\t{name: \"\", want: false},\\n\\t\\t{name: \"-\", want: false},\\n\\t\\t{name: \".\", want: false},\\n\\t\\t{name: \"a b\", want: false},\\n\\t\\t{name: \"a+b\", want: false},\\n\\t\\t{name: \"-ab\", want: false},\\n\\n\\t\\t{name: \"ab\", want: true},\\n\\t\\t{name: \"ab-c\", want: true},\\n\\t\\t{name: \"abc.def\", want: true},\\n\\t\\t{name: \"_ab_c.-d-ef\", want: true},\\n\\t}\\n\\tfor _, tt := range tests {\\n\\t\\tif got := validateName(tt.name, false); got != tt.want {\\n\\t\\t\\tt.Errorf(\"validateName() = %v, want %v\", got, tt.want)\\n\\t\\t}\\n\\t}\\n}\\n\\nfunc Test_validateHostname(t *testing.T) {\\n\\ttype args struct {\\n\\t\\ts                      string\\n\\t\\tallowWildcardSubdomain bool\\n\\t}\\n\\ttests := []struct {\\n\\t\\tname string\\n\\t\\targs args\\n\\t\\twant bool\\n\\t}{\\n\\t\\t{\\n\\t\\t\\tname: \"Normal\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\ts:                      \"example.com\",\\n\\t\\t\\t\\tallowWildcardSubdomain: true,\\n\\t\\t\\t},\\n\\t\\t\\twant: true,\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"wildcard subdomain for TUN-358\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\ts:                      \"*.ehrig.io\",\\n\\t\\t\\t\\tallowWildcardSubdomain: true,\\n\\t\\t\\t},\\n\\t\\t\\twant: true,\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"Misplaced wildcard\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\ts:                      \"subdomain.*.ehrig.io\",\\n\\t\\t\\t\\tallowWildcardSubdomain: true,\\n\\t\\t\\t},\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"Invalid domain\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\ts:                      \"..\",\\n\\t\\t\\t\\tallowWildcardSubdomain: true,\\n\\t\\t\\t},\\n\\t\\t},\\n\\t\\t{\\n\\t\\t\\tname: \"Invalid domain\",\\n\\t\\t\\targs: args{\\n\\t\\t\\t\\ts: \"..\",\\n\\t\\t\\t},\\n\\t\\t},\\n\\t}\\n\\tfor _, tt := range tests {\\n\\t\\tt.Run(tt.name, func(t *testing.T) {\\n\\t\\t\\tif got := validateHostname(tt.args.s, tt.args.allowWildcardSubdomain); got != tt.want {\\n\\t\\t\\t\\tt.Errorf(\"validateHostname() = %v, want %v\", got, tt.want)\\n\\t\\t\\t}\\n\\t\\t})\\n\\t}\\n}\\n\\nfunc Test_TunnelToken(t *testing.T) {\\n\\ttoken, err := ParseToken(\"aabc\")\\n\\trequire.Error(t, err)\\n\\trequire.Nil(t, token)\\n\\n\\texpectedToken := &connection.TunnelToken{\\n\\t\\tAccountTag:   \"abc\",\\n\\t\\tTunnelSecret: []byte(\"secret\"),\\n\\t\\tTunnelID:     uuid.New(),\\n\\t}\\n\\n\\ttokenJsonStr, err := json.Marshal(expectedToken)\\n\\trequire.NoError(t, err)\\n\\n\\ttoken64 := base64.StdEncoding.EncodeToString(tokenJsonStr)\\n\\n\\ttoken, err = ParseToken(token64)\\n\\trequire.NoError(t, err)\\n\\trequire.Equal(t, token, expectedToken)\\n}\\n',\n", " 'question': 'Which method should I call to validate a hostname and support wildcard subdomains?',\n", " 'answers': {'gpt_answer': 'The `validateHostname` method from the file `cmd/cloudflared/tunnel/subcommands_test.go` is what you should use to validate a hostname and determine if wildcard subdomains are supported. Below is an example of how to use it:\\n```go\\nhostname := \"*.example.com\" // Replace with your hostname\\nallowWildcardSubdomain := true\\nisValid := validateHostname(hostname, allowWildcardSubdomain)\\nfmt.Println(\"Is valid:\", isValid)\\n```'}}"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["sample['sampled_documents_with_questions']"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["NO_RELEVENT_RETRIEVAL_ANSWERS = [\n", "\t\"I couldn't find a relevant code snippet. Could you try rephrasing your question?\",\n", "\t\"No matching code found. Perhaps adjust your query for better results?\",\n", "\t\"Your question didn't match any snippets in the codebase. Could you specify further?\",\n", "\t\"I found no relevant code. Maybe a different question or more details could help?\",\n", "\t\"No code snippets match your query. Would you mind rewording it?\",\n", "\t\"Unable to locate relevant code. Can you reformulate your question?\",\n", "\t\"No answers found in the codebase. How about trying a different approach to your question?\",\n", "\t\"The search yielded no results. Could you provide more context or rephrase your question?\",\n", "\t\"Relevant code snippets are absent. Could you refine your question?\",\n", "\t\"I didn’t find any applicable code. Would reformulating the question help?\",\n", "]"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ChatPromptInput,\n", "    ExceedContextLength,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptChunk, TokenList\n", "\n", "\n", "def pad_prompt_in_place(tokens):\n", "    assert len(tokens) <= config.max_seq_length\n", "    tokens.extend([-tokenizer.special_tokens.eos] * (config.max_seq_length - len(tokens)))\n", "    assert len(tokens) == config.max_seq_length\n", "\n", "\n", "def create_hyperlinks(doc_path, answer):\n", "    return answer.replace(f\"`{doc_path}`\", f\"[{doc_path}]({doc_path})\")\n", "\n", "\n", "def build_example(sample, question, answer, correct_path):\n", "    retrievals = [\n", "        PromptChunk(\n", "            text=retrieval['text'],\n", "            path=retrieval['parent_doc']['path'],\n", "            unique_id=retrieval['id'],\n", "            char_start=retrieval['char_offset'],\n", "            char_end=retrieval['char_offset'] + retrieval['length'],        \n", "\n", "        )\n", "        for retrieval in sample['retrieved_chunks']\n", "    ]\n", "\n", "    features = {\n", "        \"message\": question,\n", "        \"chat_history\": [],\n", "        \"prefix_begin\": 0,\n", "        \"suffix_end\": 0,\n", "        \"retrieved_chunks\": retrievals,\n", "    }\n", "\n", "    try:\n", "        features.update(sample_current_file(\n", "            sample,\n", "            p_local_context_dropout=config.p_local_context_dropout,\n", "            p_empty_selection=config.p_empty_selection,\n", "            max_lines_per_selected_code=config.max_lines_per_selected_code,\n", "        ))    \n", "        chat_input = ChatPromptInput(**features)\n", "        chat_output = prompt_formatter.format_prompt(chat_input)\n", "    except ExceedContextLength:\n", "        try:\n", "            # Selected text is too long. Let's not worry and just make it empty.\n", "            features.update(sample_current_file(\n", "                sample,\n", "                p_local_context_dropout=0,\n", "                p_empty_selection=1,\n", "                max_lines_per_selected_code=config.max_lines_per_selected_code,\n", "            ))    \n", "            chat_input = ChatPromptInput(**features)\n", "            chat_output = prompt_formatter.format_prompt(chat_input)\n", "        except ExceedContextLength:\n", "            return None, {}\n", "\n", "\n", "    if correct_path is not None:\n", "        retrieved_from_the_correct_path = any([\n", "            actually_retrieved_chunk.path == correct_path\n", "            for actually_retrieved_chunk in chat_output.retrieved_chunks_in_prompt\n", "        ])    \n", "        \n", "        if retrieved_from_the_correct_path:\n", "            answer = create_hyperlinks(correct_path, answer)            \n", "        else:\n", "            answer = random.choice(NO_RELEVENT_RETRIEVAL_ANSWERS)\n", "    else:\n", "        retrieved_from_the_correct_path = False\n", "    retrieved_from_the_correct_path = int(retrieved_from_the_correct_path)\n", "    \n", "    label = tokenizer.tokenize_safe(answer + \"\\n\") + [tokenizer.special_tokens.eos]\n", "    tokens = [-1 * t for t in chat_output.tokens] + label\n", "    assert len(tokens) < config.max_seq_length, len(tokens)\n", "    pad_prompt_in_place(tokens)\n", "\n", "    stats = {\n", "        'prompt_length': len(chat_output.tokens),\n", "        'label_length': len(label),\n", "        'seq_length': len(tokens),\n", "        'n_retrievals': len(chat_output.retrieved_chunks_in_prompt),\n", "        'retrieved_from_the_correct_path': float(retrieved_from_the_correct_path),\n", "    }\n", "\n", "    return tokens, stats  "]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5000it [04:02, 20.64it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt_length</th>\n", "      <th>label_length</th>\n", "      <th>seq_length</th>\n", "      <th>n_retrievals</th>\n", "      <th>retrieved_from_the_correct_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>5000.000000</td>\n", "      <td>5000.00000</td>\n", "      <td>5000.0</td>\n", "      <td>5000.000000</td>\n", "      <td>5000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>9841.395800</td>\n", "      <td>135.15800</td>\n", "      <td>16385.0</td>\n", "      <td>24.482000</td>\n", "      <td>0.933800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2569.619261</td>\n", "      <td>49.23653</td>\n", "      <td>0.0</td>\n", "      <td>1.879144</td>\n", "      <td>0.248656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2176.000000</td>\n", "      <td>15.00000</td>\n", "      <td>16385.0</td>\n", "      <td>8.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>7970.500000</td>\n", "      <td>111.00000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>9647.000000</td>\n", "      <td>134.00000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>11720.750000</td>\n", "      <td>162.00000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>14425.000000</td>\n", "      <td>370.00000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       prompt_length  label_length  seq_length  n_retrievals  \\\n", "count    5000.000000    5000.00000      5000.0   5000.000000   \n", "mean     9841.395800     135.15800     16385.0     24.482000   \n", "std      2569.619261      49.23653         0.0      1.879144   \n", "min      2176.000000      15.00000     16385.0      8.000000   \n", "25%      7970.500000     111.00000     16385.0     25.000000   \n", "50%      9647.000000     134.00000     16385.0     25.000000   \n", "75%     11720.750000     162.00000     16385.0     25.000000   \n", "max     14425.000000     370.00000     16385.0     25.000000   \n", "\n", "       retrieved_from_the_correct_path  \n", "count                      5000.000000  \n", "mean                          0.933800  \n", "std                           0.248656  \n", "min                           0.000000  \n", "25%                           1.000000  \n", "50%                           1.000000  \n", "75%                           1.000000  \n", "max                           1.000000  "]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["qa_token_samples = []\n", "stats = []\n", "\n", "with QA_DATA_V1_WITH_RETRIEVALS.open('r') as f_in:\n", "    for line in tqdm.tqdm(f_in):\n", "        sample = json.loads(line[:-1])\n", "        tokens, stat = build_example(\n", "            sample,\n", "            sample['sampled_documents_with_questions']['question'], \n", "            sample['sampled_documents_with_questions']['answers']['gpt_answer'],\n", "            sample['sampled_documents_with_questions']['path'],\n", "        )\n", "        qa_token_samples.append(tokens)\n", "        stats.append(stat)\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5000it [04:09, 20.01it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt_length</th>\n", "      <th>label_length</th>\n", "      <th>seq_length</th>\n", "      <th>n_retrievals</th>\n", "      <th>retrieved_from_the_correct_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4950.000000</td>\n", "      <td>4950.000000</td>\n", "      <td>4950.0</td>\n", "      <td>4950.000000</td>\n", "      <td>4950.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>10532.527071</td>\n", "      <td>436.538586</td>\n", "      <td>16385.0</td>\n", "      <td>23.018586</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3126.432095</td>\n", "      <td>225.400179</td>\n", "      <td>0.0</td>\n", "      <td>3.912621</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1564.000000</td>\n", "      <td>19.000000</td>\n", "      <td>16385.0</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>8147.250000</td>\n", "      <td>287.000000</td>\n", "      <td>16385.0</td>\n", "      <td>24.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>10771.500000</td>\n", "      <td>406.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>13704.750000</td>\n", "      <td>550.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>14425.000000</td>\n", "      <td>2451.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       prompt_length  label_length  seq_length  n_retrievals  \\\n", "count    4950.000000   4950.000000      4950.0   4950.000000   \n", "mean    10532.527071    436.538586     16385.0     23.018586   \n", "std      3126.432095    225.400179         0.0      3.912621   \n", "min      1564.000000     19.000000     16385.0      2.000000   \n", "25%      8147.250000    287.000000     16385.0     24.000000   \n", "50%     10771.500000    406.000000     16385.0     25.000000   \n", "75%     13704.750000    550.000000     16385.0     25.000000   \n", "max     14425.000000   2451.000000     16385.0     25.000000   \n", "\n", "       retrieved_from_the_correct_path  \n", "count                           4950.0  \n", "mean                               0.0  \n", "std                                0.0  \n", "min                                0.0  \n", "25%                                0.0  \n", "50%                                0.0  \n", "75%                                0.0  \n", "max                                0.0  "]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["code_codealpaca_samples = []\n", "stats = []\n", "\n", "with EVOL_CODEALPACA_WITH_RETRIEVALS.open('r') as f_in:\n", "    for line in tqdm.tqdm(f_in):\n", "        sample = json.loads(line[:-1])\n", "        tokens, stat = build_example(\n", "            sample,\n", "            sample['instructions']['instruction'], \n", "            sample['instructions']['output'],\n", "            None,\n", "        )\n", "        if tokens is None:\n", "            continue\n", "        code_codealpaca_samples.append(tokens)\n", "        stats.append(stat)\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5000, 4950)"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["len(qa_token_samples), len(code_codealpaca_samples)"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "random.shuffle(qa_token_samples)\n", "random.shuffle(code_codealpaca_samples)\n", "\n", "validation_samples = qa_token_samples[:500] + code_codealpaca_samples[:500]\n", "train_samples = qa_token_samples[500:] + code_codealpaca_samples[500:]\n", "\n", "random.shuffle(train_samples)\n", "random.shuffle(validation_samples)"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "import torch\n", "\n", "\n", "def save_dataset(samples, path):\n", "    builder = MMapIndexedDatasetBuilder(path + \".bin\", dtype=np.int32)\n", "    for sample in samples:\n", "        builder.add_item(torch.Tensor(sample))\n", "        builder.end_document()\n", "    builder.finalize(path + \".idx\")\n", "\n", "save_dataset(train_samples, os.path.join(OUTPUT_PATH, \"train\"))\n", "save_dataset(validation_samples, os.path.join(OUTPUT_PATH, \"validation\"))"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"data": {"text/plain": ["PosixPath('/mnt/efs/augment/user/yury/binks/binks-v2')"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["OUTPUT_PATH\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
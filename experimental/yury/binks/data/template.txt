<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>{{ project_name }}</title>
<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    document.querySelectorAll('.toggle-button').forEach(button => {
        button.addEventListener('click', function() {
            const content = this.nextElementSibling;
            content.style.display = content.style.display === "none" ? "block" : "none";
        });
    });

    document.querySelectorAll('.markdown-content').forEach(element => {
        const markdownText = element.getAttribute('data-markdown');
        element.innerHTML = marked.parse(markdownText);
    });
});
</script>
<style>
  body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f4f4; }
  .container { padding-top: 20px; }
  .overview ul { padding-left: 0; list-style: none; }
  .overview li a { text-decoration: none; color: #007bff; display: block; margin-bottom: 0.5rem; }
  .card { box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2); transition: 0.3s; border: none; margin-bottom: 2rem; }
  .card-header { background: #007bff; color: white; padding: 1rem; display: flex; justify-content: space-between; align-items: center; }
  .card-header span { white-space: nowrap; }
  .nav-link { text-decoration: none; font-weight: normal; font-size: 16px; padding-left: 15px; display: inline-block; color: white; }
  .nav-link:hover { text-decoration: underline; }
  .nav-link.disabled { color: #a9a9a9; pointer-events: none; }
  .toggle-button { margin-top: 10px; font-size: 16px; }
  .toggle-content { display: none; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
  .same-file { color: green; font-weight: bold; }
  .different-file { color: red; font-weight: bold; }
  pre { border-radius: 5px; background-color: #f8f9fa; border: 1px solid #eee; padding: 10px; }
  .text-info { color: #17a2b8; font-weight: bold; }
  ul { padding: 0; }
  li { padding: 8px; border-radius: 5px; }
  table { margin-top: 10px; }
  th, td { text-align: left; padding: 8px; }
  thead { background-color: #eee; }
</style>
</head>
<body>
<div class="container" id="top">
  <div class="overview">
    <h2>{{ project_name }}</h2>
    <ul>
      {% for doc in documents %}
      <li><a href="#doc{{ loop.index }}">{{ doc.qa_set_name }}</a></li>
      {% endfor %}
    </ul>
  </div>

  {% for doc in documents %}
    <div id="doc{{ loop.index }}" class="card">
      <div class="card-header">
        <span>Path: {{ doc.qa_set_name }}</span>
        <span>
          {% if loop.first %}
            <a class="nav-link disabled">Previous</a>
          {% else %}
            <a href="#doc{{ loop.index - 1 }}" class="nav-link">Previous</a>
          {% endif %}
          <a href="#top" class="nav-link top">Top</a>
          {% if loop.last %}
            <a class="nav-link disabled">Next</a>
          {% else %}
            <a href="#doc{{ loop.index + 1 }}" class="nav-link">Next</a>
          {% endif %}
        </span>
      </div>
      <div class="card-body">
        <div class="toggle-button text-info">Toggle Document Text</div>
        <div class="toggle-content">
          <pre><code>{{ doc.text | e }}</code></pre>
        </div>
        <br>

        {% for question in doc.questions %}
          <div>
            <strong>Question:</strong> {{ question.question }}
            <div class="toggle-button text-info">Toggle Retrievals</div>
            <div class="toggle-content">
              <ul>
                {% for retrieval in question.retrievals %}
                <li>
                  <strong>ID:</strong> {{ retrieval.id }}
                  <span class="{% if retrieval.parent_doc.path == doc.path %}same-file{% else %}different-file{% endif %}">
                    {% if retrieval.parent_doc.path == doc.path %}
                    (Retrieved from the SAME file the question is about)
                    {% else %}
                    (Retrieved from a DIFFERENT file the question is about)
                    {% endif %}
                  </span>
                  <div class="toggle-button text-info">Toggle Retrieval Text</div>
                  <div class="toggle-content"><pre><code>{{ retrieval.text | e }}</code></pre></div>
                </li>
                {% endfor %}
              </ul>
            </div>
          </div>
          <div>
            <h6>Answers:</h6>
            <table class="table">
              <thead>
                <tr>
                  {% for key in question.answers.keys() %}
                  <th>{{ key }}</th>
                  {% endfor %}
                </tr>
              </thead>
              <tbody>
                <tr>
                  {% for value in question.answers.values() %}
                  <td class="markdown-content" data-markdown="{{ value | e }}"></td>
                  {% endfor %}
                </tr>
              </tbody>
            </table>
          </div>
          <hr>
        {% endfor %}
      </div>
    </div>
  {% endfor %}
</div>
</body>
</html>

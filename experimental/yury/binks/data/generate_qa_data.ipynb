{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "from dataclasses import dataclass, field\n", "import experimental.yury.data.processing as utils\n", "\n", "\n", "os.environ['OPENAI_API_KEY'] = \"***************************************************\"\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# PROJECT_NAME = Path('torch')\n", "# PROJECT_PATH = Path('/mnt/efs/augment/user/yury/binks/repos/torch_nn/')\n", "\n", "PROJECT_NAME = Path('MoDelS')\n", "PROJECT_PATH = Path('/mnt/efs/augment/user/yury/binks/repos/MoDelS')\n", "\n", "# PROJECT_NAME = Path('OVIVO_ios')\n", "# PROJECT_PATH = Path('/mnt/efs/augment/user/yury/binks/repos/OVIVO_ios')\n", "\n", "DOCS = []"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building CodeGenTokenizer tokenizer ...\n", " > padded vocab (size: 50328) with 872 dummy tokens (new size: 51200)\n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "> initializing torch distributed ...\n", "[2024-04-07 03:16:37,005] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2024-04-07 03:16:37,074] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=*************, master_port=6001\n", "[2024-04-07 03:16:37,074] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2024-04-07 03:16:37,076] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2024-04-07 03:16:37,113] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=25\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: _post_transformer_block\n", "    23: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    24: ContrastiveRetrievalHead\n", "  loss: contrastive_loss\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev2:2709510] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.query_key_value.weight requires_grad=True\n", "    2.attention.query_key_value.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.query_key_value.weight requires_grad=True\n", "    3.attention.query_key_value.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.query_key_value.weight requires_grad=True\n", "    4.attention.query_key_value.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.query_key_value.weight requires_grad=True\n", "    5.attention.query_key_value.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.query_key_value.weight requires_grad=True\n", "    6.attention.query_key_value.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.query_key_value.weight requires_grad=True\n", "    7.attention.query_key_value.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.query_key_value.weight requires_grad=True\n", "    8.attention.query_key_value.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.query_key_value.weight requires_grad=True\n", "    9.attention.query_key_value.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.query_key_value.weight requires_grad=True\n", "    10.attention.query_key_value.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.query_key_value.weight requires_grad=True\n", "    11.attention.query_key_value.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.query_key_value.weight requires_grad=True\n", "    12.attention.query_key_value.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.query_key_value.weight requires_grad=True\n", "    13.attention.query_key_value.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    14.input_layernorm.weight requires_grad=True\n", "    14.input_layernorm.bias requires_grad=True\n", "    14.attention.query_key_value.weight requires_grad=True\n", "    14.attention.query_key_value.bias requires_grad=True\n", "    14.attention.dense.weight requires_grad=True\n", "    14.attention.dense.bias requires_grad=True\n", "    14.mlp.dense_h_to_4h.weight requires_grad=True\n", "    14.mlp.dense_h_to_4h.bias requires_grad=True\n", "    14.mlp.dense_4h_to_h.weight requires_grad=True\n", "    14.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.input_layernorm.weight requires_grad=True\n", "    15.input_layernorm.bias requires_grad=True\n", "    15.attention.query_key_value.weight requires_grad=True\n", "    15.attention.query_key_value.bias requires_grad=True\n", "    15.attention.dense.weight requires_grad=True\n", "    15.attention.dense.bias requires_grad=True\n", "    15.mlp.dense_h_to_4h.weight requires_grad=True\n", "    15.mlp.dense_h_to_4h.bias requires_grad=True\n", "    15.mlp.dense_4h_to_h.weight requires_grad=True\n", "    15.mlp.dense_4h_to_h.bias requires_grad=True\n", "    16.input_layernorm.weight requires_grad=True\n", "    16.input_layernorm.bias requires_grad=True\n", "    16.attention.query_key_value.weight requires_grad=True\n", "    16.attention.query_key_value.bias requires_grad=True\n", "    16.attention.dense.weight requires_grad=True\n", "    16.attention.dense.bias requires_grad=True\n", "    16.mlp.dense_h_to_4h.weight requires_grad=True\n", "    16.mlp.dense_h_to_4h.bias requires_grad=True\n", "    16.mlp.dense_4h_to_h.weight requires_grad=True\n", "    16.mlp.dense_4h_to_h.bias requires_grad=True\n", "    17.input_layernorm.weight requires_grad=True\n", "    17.input_layernorm.bias requires_grad=True\n", "    17.attention.query_key_value.weight requires_grad=True\n", "    17.attention.query_key_value.bias requires_grad=True\n", "    17.attention.dense.weight requires_grad=True\n", "    17.attention.dense.bias requires_grad=True\n", "    17.mlp.dense_h_to_4h.weight requires_grad=True\n", "    17.mlp.dense_h_to_4h.bias requires_grad=True\n", "    17.mlp.dense_4h_to_h.weight requires_grad=True\n", "    17.mlp.dense_4h_to_h.bias requires_grad=True\n", "    18.input_layernorm.weight requires_grad=True\n", "    18.input_layernorm.bias requires_grad=True\n", "    18.attention.query_key_value.weight requires_grad=True\n", "    18.attention.query_key_value.bias requires_grad=True\n", "    18.attention.dense.weight requires_grad=True\n", "    18.attention.dense.bias requires_grad=True\n", "    18.mlp.dense_h_to_4h.weight requires_grad=True\n", "    18.mlp.dense_h_to_4h.bias requires_grad=True\n", "    18.mlp.dense_4h_to_h.weight requires_grad=True\n", "    18.mlp.dense_4h_to_h.bias requires_grad=True\n", "    19.input_layernorm.weight requires_grad=True\n", "    19.input_layernorm.bias requires_grad=True\n", "    19.attention.query_key_value.weight requires_grad=True\n", "    19.attention.query_key_value.bias requires_grad=True\n", "    19.attention.dense.weight requires_grad=True\n", "    19.attention.dense.bias requires_grad=True\n", "    19.mlp.dense_h_to_4h.weight requires_grad=True\n", "    19.mlp.dense_h_to_4h.bias requires_grad=True\n", "    19.mlp.dense_4h_to_h.weight requires_grad=True\n", "    19.mlp.dense_4h_to_h.bias requires_grad=True\n", "    20.input_layernorm.weight requires_grad=True\n", "    20.input_layernorm.bias requires_grad=True\n", "    20.attention.query_key_value.weight requires_grad=True\n", "    20.attention.query_key_value.bias requires_grad=True\n", "    20.attention.dense.weight requires_grad=True\n", "    20.attention.dense.bias requires_grad=True\n", "    20.mlp.dense_h_to_4h.weight requires_grad=True\n", "    20.mlp.dense_h_to_4h.bias requires_grad=True\n", "    20.mlp.dense_4h_to_h.weight requires_grad=True\n", "    20.mlp.dense_4h_to_h.bias requires_grad=True\n", "    21.input_layernorm.weight requires_grad=True\n", "    21.input_layernorm.bias requires_grad=True\n", "    21.attention.query_key_value.weight requires_grad=True\n", "    21.attention.query_key_value.bias requires_grad=True\n", "    21.attention.dense.weight requires_grad=True\n", "    21.attention.dense.bias requires_grad=True\n", "    21.mlp.dense_h_to_4h.weight requires_grad=True\n", "    21.mlp.dense_h_to_4h.bias requires_grad=True\n", "    21.mlp.dense_4h_to_h.weight requires_grad=True\n", "    21.mlp.dense_4h_to_h.bias requires_grad=True\n", "    23.norm.weight requires_grad=True\n", "    23.norm.bias requires_grad=True\n", "    24.log_logit_scale requires_grad=True\n", "DeepSpeed is enabled.\n", "[2024-04-07 03:16:37,265] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+41fe977, git-hash=41fe977, git-branch=HEAD\n", "[2024-04-07 03:16:37,265] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:761: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024-04-07 03:16:38,011] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   disable_allgather ............ False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4096, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4096\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 0.0003}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2024-04-07 03:16:38,012] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   train_batch_size ............. 12\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  12\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2024-04-07 03:16:38,013] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 12, \n", "    \"train_micro_batch_size_per_gpu\": 12, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 0.0003\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"initial_scale_power\": 12, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using /home/<USER>/.cache/torch_extensions/py39_cu121 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu121/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.19611549377441406 seconds\n", "[2024-04-07 03:16:38,691] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=12\n", "[2024-04-07 03:16:38,755] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=25 [0, 25) STAGE_PARAMS=304314370 (304.314M) TOTAL_PARAMS=304314370 (304.314M) UNIQUE_PARAMS=304314370 (304.314M)\n", " > number of parameters on model parallel rank 0: 304314370\n", "Warning: did not find final_linear layer, cannot calculate embedding params\n", " > total params: 304,314,370\n", " > embedding params: 0\n", "Loading: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1\n", "[2024-04-07 03:16:38,817] [INFO] [engine.py:1555:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", "[2024-04-07 03:16:39,066] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_00-model_00-model_states.pt\n", "[2024-04-07 03:16:39,128] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_02-model_00-model_states.pt\n", "[2024-04-07 03:16:39,170] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_03-model_00-model_states.pt\n", "[2024-04-07 03:16:39,218] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_04-model_00-model_states.pt\n", "[2024-04-07 03:16:39,272] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_05-model_00-model_states.pt\n", "[2024-04-07 03:16:39,312] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_06-model_00-model_states.pt\n", "[2024-04-07 03:16:39,362] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_07-model_00-model_states.pt\n", "[2024-04-07 03:16:39,404] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_08-model_00-model_states.pt\n", "[2024-04-07 03:16:39,441] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_09-model_00-model_states.pt\n", "[2024-04-07 03:16:39,496] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_10-model_00-model_states.pt\n", "[2024-04-07 03:16:39,553] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_11-model_00-model_states.pt\n", "[2024-04-07 03:16:39,588] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_12-model_00-model_states.pt\n", "[2024-04-07 03:16:39,614] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_13-model_00-model_states.pt\n", "[2024-04-07 03:16:39,660] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_14-model_00-model_states.pt\n", "[2024-04-07 03:16:39,979] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_15-model_00-model_states.pt\n", "[2024-04-07 03:16:40,061] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_16-model_00-model_states.pt\n", "[2024-04-07 03:16:40,216] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_17-model_00-model_states.pt\n", "[2024-04-07 03:16:40,331] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_18-model_00-model_states.pt\n", "[2024-04-07 03:16:40,480] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_19-model_00-model_states.pt\n", "[2024-04-07 03:16:40,654] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_20-model_00-model_states.pt\n", "[2024-04-07 03:16:40,726] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_21-model_00-model_states.pt\n", "[2024-04-07 03:16:40,754] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_23-model_00-model_states.pt\n", "[2024-04-07 03:16:40,782] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_24-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"add_path\": <PERSON>als<PERSON>,\n", "        \"max_tokens\": 1023,        \n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    }\n", "}\n", "\n", "retrieval_database = create_retriever(retriever_config)\n", "retrieval_database.scorer.load()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from research.retrieval.types import Document\n", "\n", "for root, dirs, files in os.walk(PROJECT_PATH):\n", "    for file in files:\n", "        if not file.endswith('.cs'):\n", "            continue\n", "        path = os.path.join(root, file)\n", "        relative_path = os.path.relpath(path, PROJECT_PATH)\n", "        try:\n", "            with open(path, 'rt') as f:\n", "                text = f.read()\n", "            document = Document(\n", "                id=relative_path, text=text, path=relative_path\n", "            )\n", "            retrieval_database.add_doc(document)\n", "        except UnicodeDecodeError:\n", "            # Ignore binary files\n", "            pass"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"\"\"\n", "    Collection of the enumerations defined in MoDelS, and used in the MoDO ontology.\n", "\"\"\"\n", "XMLNS = 'http://www.w3.org/2001/XMLSchema-instance'\n", "XSI = 'OpenScenario.xsd'\n", "\n", "from enum import Enum, auto\n", "# from .exceptions import OpenSCENARIOVersionError\n", "from os import error\n", "\n", "_MINOR_VERSION = 1\n", "\n", "\n", "class VersionBase:\n", "    \"\"\" base class for checking different versions of OpenSCENARIO \"\"\"\n", "    version_major = 1\n", "    version_minor = _MINOR_VERSION\n", "\n", "    def isVersion(self, major=1, minor=1):\n", "        return major == self.version_major and minor == self.version_minor\n", "\n", "\n", "class _MoDelSEnum(VersionBase):\n", "    \"\"\" custom \"enum\" class to be able to handle different versions of the enums in MoDelS\n", "\n", "        Parameters\n", "        ----------\n", "            name (str): enum name\n", "\n", "            class_name (str): name of the enum class (only used for printouts to help debugging)\n", "\n", "            min_minor_version (int): how the relative distance should be calculated\n", "                Default: 0\n", "\n", "            max_minor_version (int): the max \"minor version\" where the enum is valid\n", "                Default: Current Supported Version\n", "\n", "        Attributes\n", "        ----------\n", "            name (str): enum name\n", "\n", "            class_name (str): name of the enum class (only used for printouts to help debugging)\n", "\n", "            min_minor_version (int): how the relative distance should be calculated\n", "\n", "            max_minor_version (int): the max \"minor version\" where the enum is valid\n", "\n", "        Methods\n", "        -------\n", "            get_name()\n", "                Returns the correct string of the Enum, will take care of versions\n", "\n", "    \"\"\"\n", "\n", "    def __init__(self, class_name, name, pretty_name=\"\", description=\"\", min_minor_version=0, max_minor_version=_MINOR_VERSION):\n", "        \"\"\" initalize the _MoDelSEnum\n", "\n", "            Parameters\n", "            ----------\n", "                name (str): enum name\n", "\n", "                class_name (str): name of the enum class (only used for printouts to help debugging)\n", "\n", "                min_minor_version (int): how the relative distance should be calculated\n", "                    Default: 0\n", "\n", "                max_minor_version (int): the max \"minor version\" where the enum is valid\n", "                    Default: Current Supported Version\n", "        \"\"\"\n", "\n", "        self.name = name\n", "        self.class_name = class_name\n", "        self.pretty_name = pretty_name == \"\" if name else pretty_name\n", "        self.description = description\n", "        self.min_minor_version = min_minor_version\n", "        self.max_minor_version = max_minor_version\n", "\n", "    def __eq__(self, other):\n", "        if isinstance(other, _MoDelSEnum):\n", "            if self.name == other.name and \\\n", "                    self.class_name == other.class_name:\n", "                return True\n", "        return False\n", "\n", "    def get_name(self):\n", "        \"\"\" method that should be used when using the _MoDelSEnum to get the string, will check version of the enum to see if it is used correctly with the used version\n", "\n", "            Returns\n", "            -------\n", "            name (str)\n", "        \"\"\"\n", "\n", "        # if self.min_minor_version > self.version_minor:\n", "        #     raise OpenSCENARIOVersionError(self.class_name + '.' + self.name + ' is not part of OpenSCENARIO V' + str(\n", "        #         self.version_major) + '.' + str(self.version_minor) + ', was introduced in V' + str(\n", "        #         self.version_major) + '.' + str(self.min_minor_version))\n", "        # elif self.max_minor_version < self.version_minor:\n", "        #     raise OpenSCENARIOVersionError(self.class_name + '.' + self.name + ' is not part of OpenSCENARIO V' + str(\n", "        #         self.version_major) + '.' + str(self.version_minor) + ', was deprecated in V' + str(\n", "        #         self.version_major) + '.' + str(self.max_minor_version))\n", "        # return self.name\n", "\n", "    def __str__(self):\n", "        return self.name\n", "\n", "\n", "class e_Vehicle:\n", "    Bus = _MoDelSEnum(\"Vehicle\", \"Bus\")\n", "    Bike = _MoDelSEnum(\"Vehicle\", \"Bike\")\n", "    Car = _MoDelSEnum(\"Vehicle\", \"Car\")\n", "    Motorbike = _MoDelSEnum(\"Vehicle\", \"Motorbike\")\n", "    Train = _MoDelSEnum(\"Vehicle\", \"Train\")\n", "    Trailer = _MoDelSEnum(\"Vehicle\", \"Trailer\")\n", "    Van = _MoDelSEnum(\"Vehicle\", \"Van\")\n", "    Tram = _MoDelSEnum(\"Vehicle\", \"Tram\")\n", "    Semitrailer = _MoDelSEnum(\"Vehicle\", \"Semitrailer\")\n", "    Truck = _MoDelSEnum(\"Vehicle\", \"Truck\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"Vehicle\"\n", "\n", "\n", "class e_Pedestrian:\n", "    Animal = _MoDelSEnum(\"Pedestrian\", \"Animal\")\n", "    Wheelchair = _MoDelSEnum(\"Pedestrian\", \"Wheelchair\")\n", "    People = _MoDelSEnum(\"Pedestrian\", \"People\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"Pedestrian\"\n", "\n", "\n", "class e_EntityType:\n", "    Vehicle = e_Vehicle\n", "    Pedestrian = e_Pedestrian\n", "    Driver = _MoDelSEnum(\"EntityType\", \"Driver\")\n", "    Passenger = _MoDelSEnum(\"EntityType\", \"Passenger\")\n", "\n", "\n", "class e_Relation:\n", "    class e_connectedTo:\n", "        junctionToJunction = _MoDelSEnum(\"connectedTo\", \"junctionToJunction\")\n", "        sectionToJunction = _MoDelSEnum(\"connectedTo\", \"sectionToJunction\")\n", "        sectionToSection = _MoDelSEnum(\"connectedTo\", \"sectionToSection\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"connectedTo\"\n", "\n", "    class e_hasLane:\n", "        hasLeftLane = _MoDelSEnum(\"hasLane\", \"hasLeftLane\")\n", "        hasRightLane = _MoDelSEnum(\"hasLane\", \"hasRightLane\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"has<PERSON>ane\"\n", "\n", "    class e_temporalRelation:\n", "        before = _MoDelSEnum(\"temporalRelation\", \"before\")\n", "        equals = _MoDelSEnum(\"temporalRelation\", \"equals\")\n", "        overlaps = _MoDelSEnum(\"temporalRelation\", \"overlaps\")\n", "        meets = _MoDelSEnum(\"temporalRelation\", \"meets\")\n", "        during = _MoDelSEnum(\"temporalRelation\", \"during\")\n", "        starts = _MoDelSEnum(\"temporalRelation\", \"starts\")\n", "        finishes = _MoDelSEnum(\"temporalRelation\", \"finishes\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"temporalRelation\"\n", "\n", "    class e_hasPosition:\n", "        hasLanePosition = _MoDelSEnum(\"hasPosition\", \"hasLanePosition\")\n", "        hasRoadPosition = _MoDelSEnum(\"hasPosition\", \"hasRoadPosition\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"hasPosition\"\n", "\n", "    class e_impairs:\n", "        impairsDriver = _MoDelSEnum(\"impaires\", \"impairsDriver\")\n", "        impairsEntity = _MoDelSEnum(\"impaires\", \"impairsEntity\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"impairs\"\n", "\n", "    class e_isImpaired:\n", "        driverIsImpaired = _MoDelSEnum(\"isImpaired\", \"driverIsImpaired\")\n", "        entityIsImpaired = _MoDelSEnum(\"isImpaired\", \"entityIsImpaired\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"isImpaired\"\n", "\n", "    class e_isOn:\n", "        class e_isOnRoad:\n", "            isOnCrosswalk = _MoDelSEnum(\"isOnRoad\", \"isOnCrosswalk\")\n", "            isOnIntersection = _MoDelSEnum(\"isOnRoad\", \"isOnIntersection\")\n", "            isOnLane = _MoDelSEnum(\"isOnRoad\", \"isOnLane\")\n", "\n", "            @classmethod\n", "            def name(cls):\n", "                return \"isOnRoad\"\n", "\n", "        class e_isOnVehicle:\n", "            driverIsOnVehicle = _MoDelSEnum(\"isOnVehicle\", \"driverIsOnVehicle\")\n", "            passengerIsOnVehicle = _MoDelSEnum(\"isOnVehicle\", \"passengerIsOnVehicle\")\n", "\n", "            @classmethod\n", "            def name(cls):\n", "                return \"isOnVehicle\"\n", "\n", "        isOnRoad = e_isOnRoad\n", "        isOnVehicle = e_isOnVehicle\n", "\n", "    class e_objectOf:\n", "        objectOfAction = _MoDelSEnum(\"objectOf\", \"objectOfAction\")\n", "        objectOfEvent = _MoDelSEnum(\"objectOf\", \"objectOfEvent\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"objectOf\"\n", "\n", "    class e_partOf:\n", "        junctionPartOfRoad = _MoDelSEnum(\"partOf\", \"junctionPartOfRoad\")\n", "        lanePartOfSection = _MoDelSEnum(\"partOf\", \"lanePartOfSection\")\n", "        miscStaticObjectPartOfRoad = _MoDelSEnum(\"partOf\", \"miscStaticObjectPartOfRoad\")\n", "        sectionPartOfRoad = _MoDelSEnum(\"partOf\", \"sectionPartOfRoad\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"partOf\"\n", "\n", "    class e_performs:\n", "        actionIsPerformed = _MoDelSEnum(\"performs\", \"actionIsPerformed\")\n", "        causesEvent = _MoDelSEnum(\"performs\", \"causesEvent\")\n", "        eventIsPerformed = _MoDelSEnum(\"performs\", \"eventIsPerformed\")\n", "        notPerformsAction = _MoDelSEnum(\"performs\", \"notPerformsAction\")\n", "        performsAction = _MoDelSEnum(\"performs\", \"performsAction\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"performs\"\n", "\n", "    class e_triggers:\n", "        actionTriggersAction = _MoDelSEnum(\"triggers\", \"actionTriggersAction\")\n", "        actionTriggersEvent = _MoDelSEnum(\"triggers\", \"actionTriggersEvent\")\n", "        eventTriggersAction = _MoDelSEnum(\"triggers\", \"eventTriggersAction\")\n", "        eventTriggersEvent = _MoDelSEnum(\"triggers\", \"eventTriggersEvent\")\n", "\n", "        @classmethod\n", "        def name(cls):\n", "            return \"triggers\"\n", "\n", "    connectedTo = e_connectedTo\n", "    drivesWith = _MoDelSEnum(\"Relation\", \"drivesWith\")\n", "    hasDriver = _MoDelSEnum(\"Relation\", \"hasDriver\")\n", "    hasDrivingSkill = _MoDelSEnum(\"Relation\", \"hasDrivingSkill\")\n", "    hasLane = e_hasLane\n", "    hasPassenger = _MoDelSEnum(\"Relation\", \"hasPassenger\")\n", "    haPosition = e_hasPosition\n", "    hasSafetyEquipment = _MoDelSEnum(\"Relation\", \"hasSafetyEquipment\")\n", "    impairs = e_impairs\n", "    isBehindOf = _MoDelSEnum(\"Relation\", \"isBehindOf\")\n", "    isCausedBy = _MoDelSEnum(\"Relation\", \"isCausedBy\")\n", "    isCauseOf = _MoDelSEnum(\"Relation\", \"isCauseOf\")\n", "    isDrivingSkill = _MoDelSEnum(\"Relation\", \"isDrivingSkill\")\n", "    isFrontOf = _MoDelSEnum(\"Relation\", \"isFrontOf\")\n", "    isImpaired = e_isImpaired\n", "    isLeftOf = _MoDelSEnum(\"Relation\", \"isLeftOf\")\n", "    isOn = e_isOn\n", "    isRightOf = _MoDelSEnum(\"Relation\", \"isRightOf\")\n", "    isSafetyEquipment = _MoDelSEnum(\"Relation\", \"isSafetyEquipment\")\n", "    objectOf = e_objectOf\n", "    sendSignal = _MoDelSEnum(\"Relation\", \"sendSignal\")\n", "    partOf = e_partOf\n", "    performs = e_performs\n", "    temporalRelation = e_temporalRelation\n", "    triggers = e_triggers\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"Relation\"\n", "\n", "\n", "class e_ActionType:\n", "    AbsoluteSpeedAction = _MoDelSEnum(\"Action\", \"AbsoluteSpeedAction\")\n", "    RelativeSpeedAction = _MoDelSEnum(\"Action\", \"RelativeSpeedAction\")\n", "    LongitudinalDistanceAction = _MoDelSEnum(\"Action\", \"LongitudinalDistanceAction\")\n", "\n", "    # ---\n", "    BeingAction = _MoDelSEnum(\"Action\", \"BeingAction\")\n", "    Screaming = _MoDelSEnum(\"Action\", \"Screaming\")\n", "    Talking = _MoDelSEnum(\"Action\", \"Talking\")\n", "    GlobalAction = _MoDelSEnum(\"Action\", \"GlobalAction\")\n", "    PrivateAction = _MoDelSEnum(\"Action\", \"PrivateAction\")\n", "    ActivateControllerAction = _MoDelSEnum(\"Action\", \"ActivateControllerAction\")\n", "    ControllerAction = _MoDelSEnum(\"Action\", \"ControllerAction\")\n", "    LateralAction = _MoDelSEnum(\"Action\", \"LateralAction\")\n", "    LaneChangeAction = _MoDelSEnum(\"Action\", \"LaneChangeAction\")\n", "    LongDistanceLaneChangeAction = _MoDelSEnum(\"Action\", \"LongDistanceLaneChangeAction\")\n", "    LongTimeLaneChangeAction = _MoDelSEnum(\"Action\", \"LongTimeLaneChangeAction\")\n", "    ShortDistanceLaneChangeAction = _MoDelSEnum(\"Action\", \"ShortDistanceLaneChangeAction\")\n", "    ShortTimeLaneChangeAction = _MoDelSEnum(\"Action\", \"ShortTimeLaneChangeAction\")\n", "    LateralDistanceAction = _MoDelSEnum(\"Action\", \"LateralDistanceAction\")\n", "    IncreaseLateralDistanceAction = _MoDelSEnum(\"Action\", \"IncreaseLateralDistanceAction\")\n", "    ReduceLateralDistanceAction = _MoDelSEnum(\"Action\", \"ReduceLateralDistanceAction\")\n", "    SameLateralDistanceAction = _MoDelSEnum(\"Action\", \"SameLateralDistanceAction\")\n", "    LongitudinalAction = _MoDelSEnum(\"Action\", \"LongitudinalAction\")\n", "    IncreaseLongitudinalDistanceAction = _MoDelSEnum(\"Action\", \"IncreaseLongitudinalDistanceAction\")\n", "    ReduceLongitudinalDistanceAction = _MoDelSEnum(\"Action\", \"ReduceLongitudinalDistanceAction\")\n", "    SameLongitudinalDistanceAction = _MoDelSEnum(\"Action\", \"SameLongitudinalDistanceAction\")\n", "    SpeedAction = _MoDelSEnum(\"Action\", \"SpeedAction\")\n", "    IncreaseSpeedAction = _MoDelSEnum(\"Action\", \"IncreaseSpeedAction\")\n", "    OvertakeSpeedAction = _MoDelSEnum(\"Action\", \"OvertakeSpeedAction\")\n", "    ReduceSpeedAction = _MoDelSEnum(\"Action\", \"ReduceSpeedAction\")\n", "    SameSpeedAction = _MoDelSEnum(\"Action\", \"SameSpeedAction\")\n", "    ZeroSpeedAction = _MoDelSEnum(\"Action\", \"ZeroSpeedAction\")\n", "    RoutingAction = _MoDelSEnum(\"Action\", \"RoutingAction\")\n", "    SynchronizeAction = _MoDelSEnum(\"Action\", \"SynchronizeAction\")\n", "    TeleportAction = _MoDelSEnum(\"Action\", \"TeleportAction\")\n", "    VisibilityAction = _MoDelSEnum(\"Action\", \"VisibilityAction\")\n", "    UserDefinedAction = _MoDelSEnum(\"Action\", \"UserDefinedAction\")\n", "\n", "    # ***\n", "    # To delete ?\n", "    #\n", "    # Approaching = _MoDelSEnum(\"Action\", \"Approaching\")\n", "    # AvoidingCollision = _MoDelSEnum(\"Action\", \"AvoidingCollision\")\n", "    # Blocking = _MoDelSEnum(\"Action\", \"Blocking\")\n", "    # Colliding = _MoDelSEnum(\"Action\", \"Colliding\")\n", "    # Crossing = _MoDelSEnum(\"Action\", \"Crossing\")\n", "    # CuttingIn = _MoDelSEnum(\"Action\", \"CuttingIn\")\n", "    # CuttingOut = _MoDelSEnum(\"Action\", \"CuttingOut\")\n", "    # Decelerating = _MoDelSEnum(\"Action\", \"Decelerating\")\n", "    # DrivingInOppositeDirection = _MoDelSEnum(\"Action\", \"DrivingInOppositeDirection\")\n", "    # DrivingStraight = _MoDelSEnum(\"Action\", \"DrivingStraight\")\n", "    # FallingBehind = _MoDelSEnum(\"Action\", \"FallingBehind\")\n", "    # FallingDown = _MoDelSEnum(\"Action\", \"FallingDown\")\n", "    # Following = _MoDelSEnum(\"Action\", \"Following\")\n", "    # GlidingOnWheels = _MoDelSEnum(\"Action\", \"GlidingOnWheels\")\n", "    # LaneChanging = _MoDelSEnum(\"Action\", \"LaneChanging\")\n", "    # LaneChangingLeft = _MoDelSEnum(\"Action\", \"LaneChangingLeft\")\n", "    # LaneChangingRight = _MoDelSEnum(\"Action\", \"LaneChangingRight\")\n", "    # LoosingControl = _MoDelSEnum(\"Action\", \"LoosingControl\")\n", "    # Overtaking = _MoDelSEnum(\"Action\", \"Overtaking\")\n", "    # Parked = _MoDelSEnum(\"Action\", \"Parked\")\n", "    # Parking = _MoDelSEnum(\"Action\", \"Parking\")\n", "    # Passing = _MoDelSEnum(\"Action\", \"Passing\")\n", "    # Reversing = _MoDelSEnum(\"Action\", \"Reversing\")\n", "    # Rising = _MoDelSEnum(\"Action\", \"Rising\")\n", "    # Running = _MoDelSEnum(\"Action\", \"Running\")\n", "    # Sitting = _MoDelSEnum(\"Action\", \"Sitting\")\n", "    # Standing = _MoDelSEnum(\"Action\", \"Standing\")\n", "    # Standstill = _MoDelSEnum(\"Action\", \"Standstill\")\n", "    # Stopped = _MoDelSEnum(\"Action\", \"Stopped\")\n", "    # Stopping = _MoDelSEnum(\"Action\", \"Stopping\")\n", "    # Swerving = _MoDelSEnum(\"Action\", \"Swerving\")\n", "    # Turning = _MoDelSEnum(\"Action\", \"Turning\")\n", "    # TurningLeft = _MoDelSEnum(\"Action\", \"TurningLeft\")\n", "    # TurningRight = _MoDelSEnum(\"Action\", \"TurningRight\")\n", "    # uTurning = _MoDelSEnum(\"Action\", \"uTurning\")\n", "    # Waiting = _MoDelSEnum(\"Action\", \"Waiting\")\n", "    # Walking = _MoDelSEnum(\"Action\", \"Walking\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"Action\"\n", "\n", "\n", "class e_ExperienceFactor:\n", "    DrivingExperience = _MoDelSEnum(\"ExperienceFactor\", \"DrivingExperience\")\n", "    RoadAreaUnfamiliarity = _MoDelSEnum(\"ExperienceFactor\", \"RoadAreaUnfamiliarity\")\n", "    VehicleUnfamiliarity = _MoDelSEnum(\"ExperienceFactor\", \"VehicleUnfamiliarity\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"ExperienceFactor\"\n", "\n", "    @classmethod\n", "    def all(cls):\n", "        import inspect\n", "        attributes = inspect.getmembers(cls, lambda a: not(inspect.isroutine(a)))\n", "        return [a for a in attributes if not (a[0].startswith('__') and a[0].endswith('__'))]\n", "\n", "\n", "class e_MentalOrEmotionalFactor:\n", "    Distraction = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"Distraction\")\n", "    EmotionalUpset = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"EmotionalUpset\")\n", "    Frustration = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"Frustration\")\n", "    InHurry = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"InHurry\")\n", "    Panic = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"Panic\")\n", "    PressureOrStrain = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"PressureOrStrain\")\n", "    SelfConfidence = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"SelfConfidence\")\n", "    Uncertainty = _MoDelSEnum(\"MentalOrEmotionalFactor\", \"Uncertainty\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"ExperienceFactor\"\n", "\n", "    @classmethod\n", "    def all(cls):\n", "        import inspect\n", "        attributes = inspect.getmembers(cls, lambda a: not (inspect.isroutine(a)))\n", "        return [a for a in attributes if not (a[0].startswith('__') and a[0].endswith('__'))]\n", "\n", "\n", "class e_PhyOrPhyFactor:\n", "    AlcoholImpairment = _MoDelSEnum(\"PhyOrPhyFactor\", \"AlcoholImpairment\")\n", "    Deafness = _MoDelSEnum(\"PhyOrPhyFactor\", \"Deafness\")\n", "    Drowsy = _MoDelSEnum(\"PhyOrPhyFactor\", \"Drowsy\")\n", "    DrugImpairment = _MoDelSEnum(\"PhyOrPhyFactor\", \"DrugImpairment\")\n", "    Fatigued = _MoDelSEnum(\"PhyOrPhyFactor\", \"Fatigued\")\n", "    ReducedVision = _MoDelSEnum(\"PhyOrPhyFactor\", \"ReducedVision\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"ExperienceFactor\"\n", "\n", "    @classmethod\n", "    def all(cls):\n", "        import inspect\n", "        attributes = inspect.getmembers(cls, lambda a: not (inspect.isroutine(a)))\n", "        return [a for a in attributes if not (a[0].startswith('__') and a[0].endswith('__'))]\n", "\n", "\n", "class e_HumanFactor:\n", "    ExperienceFactor = e_ExperienceFactor\n", "    MentalOrEmotionalFactor = e_MentalOrEmotionalFactor\n", "    PhyOrPhyFactor = e_PhyOrPhyFactor\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"HumanFactor\"\n", "\n", "\n", "class e_VehicleFactor:\n", "    BrakeProblem = _MoDelSEnum(\"VehicleFactor\", \"BrakeProblem\")\n", "    EngineSystemFailure = _MoDelSEnum(\"VehicleFactor\", \"EngineSystemFailure\")\n", "    FunctionalFailure = _MoDelSEnum(\"VehicleFactor\", \"FunctionalFailure\")\n", "    LightingProblem = _MoDelSEnum(\"VehicleFactor\", \"LightingProblem\")\n", "    SensorFailure = _MoDelSEnum(\"VehicleFactor\", \"SensorFailure\")\n", "    SteeringProblem = _MoDelSEnum(\"VehicleFactor\", \"SteeringProblem\")\n", "    TireWheelProblem = _MoDelSEnum(\"VehicleFactor\", \"TireWheelProblem\")\n", "    VisionObscured = _MoDelSEnum(\"VehicleFactor\", \"VisionObscured\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"HumanFactor\"\n", "\n", "\n", "class e_CausalFactorType:\n", "\n", "    HumanFactor = e_HumanFactor\n", "    VehicleFactor = e_VehicleFactor\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"CausalFactor\"\n", "\n", "\n", "class e_DecisionError:\n", "    AggressiveDriving = _MoDelSEnum(\"DecisionError\", \"AggressiveDriving\")\n", "    AvoidingConflict = _MoDelSEnum(\"DecisionError\", \"AvoidingConflict\")\n", "    ImproperManeuver = _MoDelSEnum(\"DecisionError\", \"ImproperManeuver\")\n", "    ImproperStoppingOrDecelerating = _MoDelSEnum(\"DecisionError\", \"ImproperStoppingOrDecelerating\")\n", "    SpeedRelatedError = _MoDelSEnum(\"DecisionError\", \"SpeedRelatedError\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"DecisionError\"\n", "\n", "\n", "class e_RecognitionError:\n", "    _name = \"RecognitionError\"\n", "\n", "    DistractionError = _MoDelSEnum(_name, \"DistractionError\")\n", "    RecognitionFailure = _MoDelSEnum(_name, \"RecognitionFailure\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "\n", "class e_PerformanceError:\n", "    _name = \"PerformanceError\"\n", "\n", "    PoorLateralControl = _MoDelSEnum(_name, \"PoorLateralControl\")\n", "    PoorLongitudinalControl = _MoDelSEnum(_name, \"PoorLongitudinalControl\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "\n", "class e_Violation:\n", "    _name = \"Violation\"\n", "\n", "    IllegalManeuver = _MoDelSEnum(_name, \"IllegalManeuver\")\n", "    IntentionalIntersectionViolation = _MoDelSEnum(_name, \"IllegalManeuver\")\n", "    SpeedViolation = _MoDelSEnum(_name, \"IllegalManeuver\")\n", "    UnintentionalIntersectionViolation = _MoDelSEnum(_name, \"IllegalManeuver\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "\n", "class e_DrivingErrorType:\n", "    DecisionError = e_DecisionError\n", "    RecognitionError = e_RecognitionError\n", "    PerformanceError = e_PerformanceError\n", "    Violation = e_Violation\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return \"DrivingError\"\n", "\n", "\n", "class e_LineType:\n", "    _name = \"LineType\"\n", "\n", "    solid = _MoDelSEnum(_name, \"solid\")\n", "    dashed = _MoDelSEnum(_name, \"dashed\")\n", "    solid_dashed = _MoDelSEnum(_name, \"solid_dashed\")\n", "    dashed_solid = _MoDelSEnum(_name, \"dashed_solid\")\n", "    none = _MoDelSEnum(_name, \"none\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "\n", "class e_ReferenceContext:\n", "    _name = \"ReferenceContext\"\n", "\n", "    absolute = _MoDelSEnum(_name, \"absolute\")\n", "    relative = _MoDelSEnum(_name, \"relative\")\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "\n", "class e_DynamicsShapes:\n", "    \"\"\" Enum for DynamicsShapes\n", "    \"\"\"\n", "    _name = \"ReferenceContext\"\n", "\n", "    linear = _MoDelSEnum('DynamicsShapes', 'linear')\n", "    cubic = _MoDelSEnum('DynamicsShapes', 'cubic')\n", "    sinusoidal = _MoDelSEnum('DynamicsShapes', 'sinusoidal')\n", "    step = _MoDelSEnum('DynamicsShapes', 'step')\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "    @classmethod\n", "    def by_name(cls, name: str):\n", "        if hasattr(cls, name):\n", "            return _MoDelSEnum('DynamicsShapes', name)\n", "\n", "\n", "class e_DynamicsDimension:\n", "    \"\"\" Enum for DynamicsDimension\n", "    \"\"\"\n", "    _name = \"ReferenceContext\"\n", "\n", "    rate = _MoDelSEnum('DynamicsDimension', 'rate')\n", "    time = _MoDelSEnum('DynamicsDimension', 'time')\n", "    distance = _MoDelSEnum('DynamicsDimension', 'distance')\n", "\n", "    @classmethod\n", "    def name(cls):\n", "        return cls._name\n", "\n", "    @classmethod\n", "    def by_name(cls, name: str):\n", "        if hasattr(cls, name):\n", "            return _MoDelSEnum('DynamicsDimension', name)\n", "\n", "# ------------------------------------------------------------\n", "\n", "\n", "class CloudState:\n", "    \"\"\" Enum for CloudState\n", "    \"\"\"\n", "    skyOff = _MoDelSEnum('CloudState', 'skyOff')\n", "    free = _MoDelSEnum('CloudState', 'free')\n", "    cloudy = _MoDelSEnum('CloudState', 'cloudy')\n", "    overcast = _MoDelSEnum('CloudState', 'overcast')\n", "    rainy = _MoDelSEnum('CloudState', 'rainy')\n", "\n", "\n", "class ConditionEdge():\n", "    \"\"\" Enum for ConditionEdge\n", "    \"\"\"\n", "    rising = _MoDelSEnum('ConditionEdge', 'rising')\n", "    falling = _MoDelSEnum('ConditionEdge', 'falling')\n", "    risingOrFalling = _MoDelSEnum('ConditionEdge', 'risingOrFalling')\n", "    none = _MoDelSEnum('ConditionEdge', 'none')\n", "\n", "\n", "class FollowMode():\n", "    \"\"\" Enum for FollowMode\n", "    \"\"\"\n", "    position = _MoDelSEnum('FollowMode', 'position')\n", "    follow = _MoDelSEnum('FollowMode', 'follow')\n", "\n", "\n", "class MiscObjectCategory():\n", "    \"\"\" Enum for MiscObjectCategory\n", "    \"\"\"\n", "\n", "    none = _MoDelSEnum('MiscObjectCategory', 'none')\n", "    obstacle = _MoDelSEnum('MiscObjectCategory', 'obstacle')\n", "    pole = _MoDelSEnum('MiscObjectCategory', 'pole')\n", "    tree = _MoDelSEnum('MiscObjectCategory', 'tree')\n", "    vegetation = _MoDelSEnum('MiscObjectCategory', 'vegetation')\n", "    barrier = _MoDelSEnum('MiscObjectCategory', 'barrier')\n", "    building = _MoDelSEnum('MiscObjectCategory', 'building')\n", "    parkingSpace = _MoDelSEnum('MiscObjectCategory', 'parkingSpace')\n", "    patch = _MoDelSEnum('MiscObjectCategory', 'patch')\n", "    railing = _MoDelSEnum('MiscObjectCategory', 'railing')\n", "    grafficIsland = _MoDelSEnum('MiscObjectCategory', 'grafficIsland')\n", "    crosswalk = _MoDelSEnum('MiscObjectCategory', 'crosswalk')\n", "    streetLamp = _MoDelSEnum('MiscObjectCategory', 'streetLamp')\n", "    gantry = _MoDelSEnum('MiscObjectCategory', 'gantry')\n", "    soundBarrier = _MoDelSEnum('MiscObjectCategory', 'soundBarrier')\n", "    wind = _MoDelSEnum('MiscObjectCategory', 'wind', max_minor_version=0)\n", "    roadMark = _MoDelSEnum('MiscObjectCategory', 'roadMark')\n", "\n", "\n", "class ObjectType():\n", "    \"\"\" Enum for ObjectType\n", "    \"\"\"\n", "    pedestrian = _MoDelSEnum('ObjectType', 'pedestrian')\n", "    vehicle = _MoDelSEnum('ObjectType', 'cf_vehicle')\n", "    miscellaneous = _MoDelSEnum('ObjectType', 'miscellaneous')\n", "    external = _MoDelSEnum('ObjectType', 'external', min_minor_version=1)\n", "\n", "\n", "class ParameterType():\n", "    \"\"\" Enum for ParameterType\n", "    \"\"\"\n", "    integer = _MoDelSEnum('ParameterType', 'integer')\n", "    double = _MoDelSEnum('ParameterType', 'double')\n", "    string = _MoDelSEnum('ParameterType', 'string')\n", "    unsighedInt = _MoDelSEnum('ParameterType', 'unsighedInt')\n", "    unsighedShort = _MoDelSEnum('ParameterType', 'unsighedShort')\n", "    boolean = _MoDelSEnum('ParameterType', 'boolean')\n", "    dateTime = _MoDelSEnum('ParameterType', 'dateTime')\n", "\n", "\n", "class PedestrianCategory():\n", "    \"\"\" Enum for PedestrianCategory\n", "    \"\"\"\n", "    pedestrian = _MoDelSEnum('PedestrianCategory', 'pedestrian')\n", "    wheelchair = _Mo<PERSON>elSEnum('PedestrianCategory', 'wheelchair')\n", "    animal = _MoDelSEnum('PedestrianCategory', 'animal')\n", "\n", "\n", "class PrecipitationType():\n", "    \"\"\" Enum for PercipitationType\n", "    \"\"\"\n", "    dry = _MoDelSEnum('PrecipitationType', 'dry')\n", "    rain = _MoDelSEnum('PrecipitationType', 'rain')\n", "    snow = _MoDelSEnum('PrecipitationType', 'snow')\n", "\n", "\n", "class Priority():\n", "    \"\"\" Enum for Priority\n", "    \"\"\"\n", "    overwrite = _MoDelSEnum('Priority', 'overwrite')\n", "    skip = _MoDelSEnum('Priority', 'skip')\n", "    parallel = _MoDelSEnum('Priority', 'parallel')\n", "\n", "\n", "class ReferenceContext():\n", "    \"\"\" Enum for ReferenceContext\n", "    \"\"\"\n", "    relative = _MoDelSEnum('ReferenceContext', 'relative')\n", "    absolute = _MoDelSEnum('ReferenceContext', 'absolute')\n", "\n", "\n", "class RelativeDistanceType():\n", "    \"\"\" Enum for RelativeDistanceType\n", "    \"\"\"\n", "    longitudinal = _MoDelSEnum('RelativeDistanceType', 'longitudinal')\n", "    lateral = _MoDelSEnum('RelativeDistanceType', 'lateral')\n", "    cartesianDistance = _MoDelSEnum('RelativeDistanceType', 'cartesianDistance', max_minor_version=0)\n", "    euclidianDistance = _MoDelSEnum('RelativeDistanceType', 'euclidianDistance', min_minor_version=1)\n", "\n", "\n", "class RouteStrategy():\n", "    \"\"\" Enum for RouteStrategy\n", "    \"\"\"\n", "    fastest = _MoDelSEnum('RouteStrategy', 'fastest')\n", "    shortest = _MoDelSEnum('RouteStrategy', 'shortest')\n", "    leastIntersections = _MoDelSEnum('RouteStrategy', 'leastIntersections')\n", "    random = _MoDelSEnum('RouteStrategy', 'random')\n", "\n", "\n", "class Rule():\n", "    \"\"\" Enum for Rule\n", "    \"\"\"\n", "    greaterThan = _MoDelSEnum('Rule', 'greaterThan')\n", "    lessThan = _MoDelSEnum('Rule', 'lessThan')\n", "    equalTo = _MoDelSEnum('Rule', 'equalTo')\n", "    greaterOrEqual = _MoDelSEnum('Rule', 'greaterOrEqual', min_minor_version=1)\n", "    lessOrEqual = _MoDelSEnum('Rule', 'lessOrEqual', min_minor_version=1)\n", "    notEqualTo = _MoDelSEnum('Rule', 'notEqualTo', min_minor_version=1)\n", "\n", "\n", "class SpeedTargetValueType():\n", "    \"\"\" Enum for SpeedTargetValueType\n", "    \"\"\"\n", "    delta = _MoDelSEnum('SpeedTargetValueType', 'delta')\n", "    factor = _MoDelSEnum('SpeedTargetValueType', 'factor')\n", "\n", "\n", "class StoryboardElementState():\n", "    \"\"\" Enum for StoryboardElementState\n", "    \"\"\"\n", "    startTransition = _MoDelSEnum('StoryboardElementState', 'startTransition')\n", "    endTransition = _MoDelSEnum('StoryboardElementState', 'endTransition')\n", "    stopTransition = _MoDelSEnum('StoryboardElementState', 'stopTransition')\n", "    skipTransition = _MoDelSEnum('StoryboardElementState', 'skipTransition')\n", "    completeState = _MoDelSEnum('StoryboardElementState', 'completeState')\n", "    runningState = _MoDelSEnum('StoryboardElementState', 'runningState')\n", "    standbyState = _MoDelSEnum('StoryboardElementState', 'standbyState')\n", "\n", "\n", "class StoryboardElementType():\n", "    \"\"\" Enum for StoryboardElementType\n", "    \"\"\"\n", "    story = _MoDelSEnum('StoryboardElementType', 'story')\n", "    act = _MoDelSEnum('StoryboardElementType', 'act')\n", "    maneuver = _MoDelSEnum('StoryboardElementType', 'maneuver')\n", "    event = _MoDelSEnum('StoryboardElementType', 'event')\n", "    action = _MoDelSEnum('StoryboardElementType', 'action')\n", "    maneuverGroup = _MoDelSEnum('StoryboardElementType', 'maneuverGroup')\n", "\n", "\n", "class TriggeringEntitiesRule():\n", "    \"\"\" Enum for TriggeringEntitiesRule\n", "    \"\"\"\n", "    any = _MoDelSEnum('TriggeringEntitiesRule', 'any')\n", "    all = _MoDelSEnum('TriggeringEntitiesRule', 'all')\n", "\n", "\n", "class VehicleCategory():\n", "    \"\"\" Enum for VehicleCategory\n", "    \"\"\"\n", "    car = _MoDelSEnum('VehicleCategory', 'car')\n", "    van = _MoDelSEnum('VehicleCategory', 'van')\n", "    truck = _MoDelSEnum('VehicleCategory', 'truck')\n", "    trailer = _MoDelSEnum('VehicleCategory', 'trailer')\n", "    semitrailer = _MoDelSEnum('VehicleCategory', 'semitrailer')\n", "    bus = _MoDelSEnum('VehicleCategory', 'bus')\n", "    motorbike = _MoDelSEnum('VehicleCategory', 'motorbike')\n", "    bicycle = _MoDelSEnum('VehicleCategory', 'bicycle')\n", "    train = _MoDelSEnum('VehicleCategory', 'train')\n", "    tram = _MoDelSEnum('VehicleCategory', 'tram')\n", "\n", "\n", "class CoordinateSystem():\n", "    \"\"\" Enum for CoordinateSystem\n", "    \"\"\"\n", "    entity = _MoDelSEnum('CoordinateSystem', 'entity', min_minor_version=1)\n", "    lane = _MoDelSEnum('CoordinateSystem', 'lane', min_minor_version=1)\n", "    road = _MoDelSEnum('CoordinateSystem', 'road', min_minor_version=1)\n", "    trajectory = _MoDelSEnum('CoordinateSystem', 'trajectory', min_minor_version=1)\n", "\n", "\n", "class LateralDisplacement():\n", "    any = _MoDelSEnum('LateralDisplacement', 'any', min_minor_version=1)\n", "    leftToReferencedEntity = _MoDelSEnum('LateralDisplacement', 'leftToReferencedEntity', min_minor_version=1)\n", "    rightToReferencedEntity = _MoDelSEnum('LateralDisplacement', 'rightToReferencedEntity', min_minor_version=1)\n", "\n", "\n", "class LongitudinalDisplacement():\n", "    any = _MoDelSEnum('LongitudinalDisplacement', 'any', min_minor_version=1)\n", "    trailingReferencedEntity = _MoDelSEnum('LongitudinalDisplacement', 'trailingReferencedEntity', min_minor_version=1)\n", "    leadingReferencedEntity = _MoDelSEnum('LongitudinalDisplacement', 'leadingReferencedEntity', min_minor_version=1)\n", "\n", "\n"]}], "source": ["# Automatically generate 100 questions, 10 per file for torch_nn repo\n", "\n", "# RELATIVE_PATH = Path('modules/transformer.py')\n", "# RELATIVE_PATH = Path('parallel/data_parallel.py')\n", "# RELATIVE_PATH = Path('Assets/Scripts/Mobile/MobileMenu.cs')\n", "RELATIVE_PATH = Path('model/enumerations.py')\n", "\n", "path = PROJECT_PATH / RELATIVE_PATH\n", "\n", "with open(path.expanduser(), 'r') as f:\n", "    file_content = f.read()\n", "\n", "print(file_content)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QUESTIONS ONLY\n", "- What is the purpose of the `_MoDelSEnum` class in the `enumerations.py` file?\n", "- How does the `VersionBase` class in `enumerations.py` determine compatibility with a version?\n", "- Does the `get_name` method in `_MoDelSEnum` handle version checks for enum usage?\n", "- What is the significance of the `pretty_name` attribute in the `_MoDelSEnum` class?\n", "- How are different vehicle types represented in the `enumerations.py`?\n", "- Are there any methods to retrieve all enum members of a class like `e_HumanFactor`?\n", "- How is the `XMLNS` constant used in the `enumerations.py` file?\n", "- Can enum items in `enumerations.py` be compared using the equality operator?\n", "- Are there any commented-out sections in the `enumerations.py` source code that might be important?\n", "- How are different `CoordinateSystem` enumerations handled for different minor versions?\n", "\n", "QUESTION AND ANSWERS\n", "QUESTION: What is the purpose of the `_MoDelSEnum` class in the `enumerations.py` file?\n", "ANSWER: In the file located at `model/enumerations.py`, the `_MoDelSEnum` class serves as a custom enumeration class allowing for handling different versions of the enums in the MoDelS framework. It incorporates version checks, pretty name handling, and initialization with version-specific attributes for the defined enumerations.\n", "\n", "QUESTION: How does the `VersionBase` class in `enumerations.py` determine compatibility with a version?\n", "ANSWER: The `VersionBase` class in the `model/enumerations.py` file has a method `isVersion` that checks the compatibility of an object with a specific version by comparing the given major and minor version numbers with the object's version attributes.\n", "\n", "QUESTION: Does the `get_name` method in `_MoDelSEnum` handle version checks for enum usage?\n", "ANSWER: The `get_name` method in the `_MoDelSEnum` class, as found in the `model/enumerations.py` file, is intended to handle version checks for enum usage. However, the actual version check logic is commented out, suggesting that the method currently simply returns the enum's name without performing any checks.\n", "\n", "QUESTION: What is the significance of the `pretty_name` attribute in the `_MoDelSEnum` class?\n", "ANSWER: In `model/enumerations.py`, the `pretty_name` attribute of the `_MoDelSEnum` class is intended to provide a more user-friendly or presentable version of the enum's name. If `pretty_name` is not explicitly provided during initialization, it defaults to the value of the `name` attribute.\n", "\n", "QUESTION: How are different vehicle types represented in the `enumerations.py`?\n", "ANSWER: Different vehicle types in `model/enumerations.py` are represented as instances of the `_MoDelSEnum` class, organized within the `e_Vehicle` class. Each vehicle type like 'Car', 'Bus', 'Bike', etc., is an instance variable of the `e_Vehicle` class.\n", "\n", "QUESTION: Are there any methods to retrieve all enum members of a class like `e_HumanFactor`?\n", "ANSWER: Yes, in the `model/enumerations.py` file, classes like `e_HumanFactor` have a class method `all` that uses reflection (via the `inspect` module) to retrieve all member attributes of the class, which effectively returns all the enum instances defined within that class.\n", "\n", "QUESTION: How is the `XMLNS` constant used in the `enumerations.py` file?\n", "ANSWER: The `XMLNS` constant defined in `model/enumerations.py` is assigned a URL that appears to be a namespace identifier for XML Schema instances. The constant itself is not used within the `enumerations.py` file's code snippet, but it is likely intended for use elsewhere in the project to reference the XML Schema namespace.\n", "\n", "QUESTION: Can enum items in `enumerations.py` be compared using the equality operator?\n", "ANSWER: Yes, enum items in `model/enumerations.py` can be compared using the equality operator. The `_MoDelSEnum` class defines an `__eq__` method that allows for comparison of enum instances based on their name and class_name attributes.\n", "\n", "QUESTION: Are there any commented-out sections in the `enumerations.py` source code that might be important?\n", "ANSWER: In the `model/enumerations.py` file, there are commented-out sections within the `get_name` method of the `_MoDelSEnum` class. These sections contain code for raising errors when version checks fail, which could be important for enforcing correct version usage of enums in the project.\n", "\n", "QUESTION: How are different `CoordinateSystem` enumerations handled for different minor versions?\n", "ANSWER: In `model/enumerations.py`, different `CoordinateSystem` enumerations are handled by specifying the `min_minor_version` attribute during the instantiation of `_MoDelSEnum` class objects. This allows certain instances to only be valid for specific minor versions, enforcing version compatibility within the enumerations.\n"]}], "source": ["n_questions = max(10, len(file_content.splitlines()) // 100)\n", "\n", "qa_template = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, a software engineer who asks these questions does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "- How do we handle X in the Y?\n", "- What happens if we do X while calling <PERSON>?\n", "- Where do we perform X?\n", "- Does Y have the property X?\n", "- How do I specify X in Y?\n", "- Where in Y do we perform X?\n", "- What X is for?\n", "- Where Y does X?\n", "- How does X is implemented in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "qa_prompt = qa_template.format(\n", "    n_questions=n_questions,\n", "    path=RELATIVE_PATH, \n", "    file_content=file_content\n", ")\n", "qa_text = generate_response_via_chat(\n", "    [qa_prompt],\n", "    num_completion=1,\n", "    temperature=0.8,\n", "    model=\"gpt-4-1106-preview\",\n", "    max_tokens=4096,\n", ")[0]\n", "\n", "print(qa_text)\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QUESTIONS ONLY\n", "1. How do I check if an object is a specific version in the model?\n", "2. Which class should I use to represent different types of vehicles in the model?\n", "3. How can I retrieve the name of the enumeration class 'e_HumanFactor'?\n", "4. What method would I use to get the enum name for 'Driver' in 'e_EntityType'?\n", "5. How do I obtain all the available values within the 'e_ExperienceFactor' enum?\n", "6. Can I create a custom dynamic shape for modeling object motion, and if so, how?\n", "7. What attribute would I use to check if an enum has a maximum minor version limit?\n", "8. How can I determine the story element type for a maneuver in the model?\n", "9. Which enum class should I use for specifying road conditions, like wet or icy roads?\n", "10. How can I represent a change in weather, especially cloud states, using the model?\n", "\n", "QUESTION AND ANSWERS\n", "QUESTION: How do I check if an object is a specific version in the model?\n", "ANSWER: You can check if an object is of a specific version by using the `isVersion` method found in the `VersionBase` class in the `model/enumerations.py` file. For example:\n", "```python\n", "version_object = VersionBase()\n", "if version_object.isVersion(major=1, minor=2):\n", "    print(\"The object is at version 1.2\")\n", "```\n", "\n", "QUESTION: Which class should I use to represent different types of vehicles in the model?\n", "ANSWER: To represent different types of vehicles in the model, you should use the `e_Vehicle` class from the `model/enumerations.py` file. Here's how you can use it:\n", "```python\n", "from model.enumerations import e_Vehicle\n", "car = e_Vehicle.Car\n", "print(car.get_name())  # Output: Car\n", "```\n", "\n", "QUESTION: How can I retrieve the name of the enumeration class 'e_HumanFactor'?\n", "ANSWER: The name of the enumeration class 'e_HumanFactor' can be retrieved using the `name` class method, which is defined in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import e_HumanFactor\n", "print(e_HumanFactor.name())  # Output: HumanFactor\n", "```\n", "\n", "QUESTION: What method would I use to get the enum name for 'Driver' in 'e_EntityType'?\n", "ANSWER: To get the enum name for 'Driver' in `e_EntityType`, you would use the `get_name` method of the `_MoDelSEnum` instance associated with 'Driver'. The corresponding code is in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import e_EntityType\n", "print(e_EntityType.Driver.get_name())  # Output: Driver\n", "```\n", "\n", "QUESTION: How do I obtain all the available values within the 'e_ExperienceFactor' enum?\n", "ANSWER: You can obtain all the available values within the 'e_ExperienceFactor' enum using the `all` class method. This is found in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import e_ExperienceFactor\n", "for value in e_ExperienceFactor.all():\n", "    print(value)\n", "```\n", "\n", "QUESTION: Can I create a custom dynamic shape for modeling object motion, and if so, how?\n", "ANSWER: Custom dynamic shapes for modeling object motion can be created using the `e_DynamicsShapes` class and its `by_name` class method, which is in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import e_DynamicsShapes\n", "custom_shape = e_DynamicsShapes.by_name('linear')\n", "print(custom_shape.get_name())  # Output: linear\n", "```\n", "\n", "QUESTION: What attribute would I use to check if an enum has a maximum minor version limit?\n", "ANSWER: To check if an enum has a maximum minor version limit, you would look at the `max_minor_version` attribute. You can find examples of this in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import e_DynamicsShapes\n", "print(e_DynamicsShapes.step.max_minor_version)  # Output: 1\n", "```\n", "\n", "QUESTION: How can I determine the story element type for a maneuver in the model?\n", "ANSWER: You can determine the story element type for a maneuver using the `StoryboardElementType` class in the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import StoryboardElementType\n", "print(StoryboardElementType.maneuver.get_name())  # Output: maneuver\n", "```\n", "\n", "QUESTION: Which enum class should I use for specifying road conditions, like wet or icy roads?\n", "ANSWER: The source code you've provided does not explicitly define an enum for road conditions such as wet or icy roads. However, general environmental conditions might be represented by other enums such as `PrecipitationType` or `e_DynamicsShapes` for slippery dynamics, and it would be best to search for or create a suitable enum in the `model/enumerations.py` file as per your model's needs.\n", "\n", "QUESTION: How can I represent a change in weather, especially cloud states, using the model?\n", "ANSWER: To represent a change in weather, especially cloud states, use the `CloudState` class from the `model/enumerations.py` file:\n", "```python\n", "from model.enumerations import CloudState\n", "weather_change = CloudState.cloudy\n", "print(weather_change.get_name())  # Output: cloudy\n", "```\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 241\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 52\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 155\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 178\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 199\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 84\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 88\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 128\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 97\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 116\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 67\n", "[yury-dev2:2709553] tcp_peer_recv_connect_ack: invalid header type: 48\n"]}], "source": ["n_questions = max(10, len(file_content.splitlines()) // 100)\n", "\n", "qa_template = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, this software engineer is trying to use some of the API from the file, but need assistance. However, they does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where ,X, Y and Z are some placeholder)\n", "- How do I do X?\n", "- Which method / class shall I use to do X?\n", "- Which functionality have we available in Y?\n", "- Which argument shall I to function X to perform Z?\n", "- How do I specify X in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer. Additionally, include a concise code snippet with either relevant code excerpt or a practical example.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "qa_prompt = qa_template.format(\n", "    n_questions=n_questions,\n", "    path=RELATIVE_PATH, \n", "    file_content=file_content\n", ")\n", "qa_text = generate_response_via_chat(\n", "    [qa_prompt],\n", "    num_completion=1,\n", "    temperature=0.8,\n", "    model=\"gpt-4-1106-preview\",\n", "    max_tokens=4096,\n", ")[0]\n", "\n", "print(qa_text)\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "\n", "def search(question: str, top_k: int):\n", "    retrieved_chunks, _ = retrieval_database.query(\n", "        model_input=ModelInput(\n", "            prefix=\"\",\n", "            suffix=\"\",\n", "            path=\"\",\n", "            extra={\n", "                \"message\": question,\n", "            }\n", "        ),\n", "        top_k=top_k,\n", "    )\n", "    return retrieved_chunks\n", "\n", "\n", "@dataclass\n", "class Question:\n", "    question: str\n", "    retrievals: list[Chunk] = field(default_factory=list)\n", "    answers: dict[str, str] = field(default_factory=dict)\n", "\n", "    def add_retrieval(self, top_k: int):        \n", "        self.retrievals = search(self.question, top_k)\n", "\n", "\n", "@dataclass\n", "class DocumentWithQuestions:\n", "    path: str\n", "    text: str\n", "    questions: list[Question] = field(default_factory=list)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Question(question='What is the purpose of the `_MoDelSEnum` class in the `enumerations.py` file?', retrievals=[], answers={'gpt_answer': 'In the file located at `model/enumerations.py`, the `_MoDelSEnum` class serves as a custom enumeration class allowing for handling different versions of the enums in the MoDelS framework. It incorporates version checks, pretty name handling, and initialization with version-specific attributes for the defined enumerations.'}),\n", " Question(question='How does the `VersionBase` class in `enumerations.py` determine compatibility with a version?', retrievals=[], answers={'gpt_answer': \"The `VersionBase` class in the `model/enumerations.py` file has a method `isVersion` that checks the compatibility of an object with a specific version by comparing the given major and minor version numbers with the object's version attributes.\"}),\n", " Question(question='Does the `get_name` method in `_MoDelSEnum` handle version checks for enum usage?', retrievals=[], answers={'gpt_answer': \"The `get_name` method in the `_MoDelSEnum` class, as found in the `model/enumerations.py` file, is intended to handle version checks for enum usage. However, the actual version check logic is commented out, suggesting that the method currently simply returns the enum's name without performing any checks.\"}),\n", " Question(question='What is the significance of the `pretty_name` attribute in the `_MoDelSEnum` class?', retrievals=[], answers={'gpt_answer': \"In `model/enumerations.py`, the `pretty_name` attribute of the `_MoDelSEnum` class is intended to provide a more user-friendly or presentable version of the enum's name. If `pretty_name` is not explicitly provided during initialization, it defaults to the value of the `name` attribute.\"}),\n", " Question(question='How are different vehicle types represented in the `enumerations.py`?', retrievals=[], answers={'gpt_answer': \"Different vehicle types in `model/enumerations.py` are represented as instances of the `_MoDelSEnum` class, organized within the `e_Vehicle` class. Each vehicle type like 'Car', 'Bus', 'Bike', etc., is an instance variable of the `e_Vehicle` class.\"}),\n", " Question(question='Are there any methods to retrieve all enum members of a class like `e_HumanFactor`?', retrievals=[], answers={'gpt_answer': 'Yes, in the `model/enumerations.py` file, classes like `e_HumanFactor` have a class method `all` that uses reflection (via the `inspect` module) to retrieve all member attributes of the class, which effectively returns all the enum instances defined within that class.'}),\n", " Question(question='How is the `XMLNS` constant used in the `enumerations.py` file?', retrievals=[], answers={'gpt_answer': \"The `XMLNS` constant defined in `model/enumerations.py` is assigned a URL that appears to be a namespace identifier for XML Schema instances. The constant itself is not used within the `enumerations.py` file's code snippet, but it is likely intended for use elsewhere in the project to reference the XML Schema namespace.\"}),\n", " Question(question='Can enum items in `enumerations.py` be compared using the equality operator?', retrievals=[], answers={'gpt_answer': 'Yes, enum items in `model/enumerations.py` can be compared using the equality operator. The `_MoDelSEnum` class defines an `__eq__` method that allows for comparison of enum instances based on their name and class_name attributes.'}),\n", " Question(question='Are there any commented-out sections in the `enumerations.py` source code that might be important?', retrievals=[], answers={'gpt_answer': 'In the `model/enumerations.py` file, there are commented-out sections within the `get_name` method of the `_MoDelSEnum` class. These sections contain code for raising errors when version checks fail, which could be important for enforcing correct version usage of enums in the project.'}),\n", " Question(question='How are different `CoordinateSystem` enumerations handled for different minor versions?', retrievals=[], answers={'gpt_answer': 'In `model/enumerations.py`, different `CoordinateSystem` enumerations are handled by specifying the `min_minor_version` attribute during the instantiation of `_MoDelSEnum` class objects. This allows certain instances to only be valid for specific minor versions, enforcing version compatibility within the enumerations.'})]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["QUESTION_PREFIX = \"QUESTION:\"\n", "ANSWER_PREFIX = \"ANSWER:\"\n", "\n", "def parse_gpt_qa_output(text: str) -> list[Question]:\n", "    data = []\n", "    question_index = text.find(QUESTION_PREFIX)    \n", "    while question_index != -1:\n", "        answer_index = text.find(ANSWER_PREFIX, question_index + 1)        \n", "        assert answer_index != -1\n", "        next_question_index = text.find(QUESTION_PREFIX, answer_index + 1)\n", "        end_of_the_current_question = next_question_index if next_question_index != -1 else len(text)\n", "        question = text[question_index:answer_index][len(QUESTION_PREFIX):].strip()\n", "        answer = text[answer_index:end_of_the_current_question][len(ANSWER_PREFIX):].strip()\n", "        data.append(Question(\n", "            question=question, \n", "            answers={\n", "                'gpt_answer': answer,\n", "            }\n", "        ))\n", "        question_index = next_question_index\n", "    return data\n", "\n", "qa_data = parse_gpt_qa_output(qa_text)\n", "qa_data"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["for qa_item in qa_data:\n", "    qa_item.add_retrieval(top_k=10)\n", "\n", "doc = DocumentWithQuestions(\n", "    path=str(RELATIVE_PATH),\n", "    text=file_content,\n", "    questions=qa_data,\n", ")\n", "\n", "DOCS.append(doc)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment, Template\n", "\n", "with Path('/home/<USER>/augment/experimental/yury/binks/template.txt').open() as f:\n", "    template_str = f.read()\n", "\n", "# It's possible to simply instanciate the Template via `Template(template)`.\n", "# However, jinja2 will strip the trailing newline, which is annoying.\n", "# The way to prevent that is by explicitly passing the keep_trailing_newline flag\n", "# into the Environment object.\n", "env = Environment(keep_trailing_newline=True)\n", "template = env.from_string(template_str)\n", "\n", "rendered_html = template.render(documents=DOCS, project_name=PROJECT_NAME)\n", "\n", "# with Path('/mnt/efs/augment/public_html/yury/tmp/torch_nn.html').open('w') as f:\n", "with Path('/mnt/efs/augment/public_html/yury/tmp/angelocarbone_MoDelS.html').open('w') as f:\n", "# with Path('/mnt/efs/augment/public_html/yury/tmp/OVIVO_ios_v2.html').open('w') as f:\n", "    f.write(rendered_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "import tqdm\n", "import dataclasses\n", "from types import SimpleNamespace"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v1.3-merged\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "OUTPUT_FILE = OUTPUT_PATH / \"repos_with_qa.jsonl\"\n", "\n", "QA_DATA_V1 = [\n", "    (\n", "        BASE_PATH / \"binks-v1/01_raw_repos/part-00000-9990b718-de5b-4aed-97d4-968fd1302624-c000.zstd.parquet\",\n", "        BASE_PATH / \"binks-v1/docs.jsonl\",\n", "    ),\n", "    (\n", "        BASE_PATH / \"binks-v1.1/01_raw_repos/part-0000*-3ccecdd8-89f4-4520-b804-988dbe95a44d-c000.zstd.parquet\",\n", "        BASE_PATH / \"binks-v1.1/docs.jsonl\",\n", "    ),    \n", "    (\n", "        BASE_PATH / \"binks-v1.2/01_raw_repos/part-0000*-a63b384a-3dd5-45bd-aeb5-0dd345dbcad0-c000.zstd.parquet\",\n", "        BASE_PATH / \"binks-v1.2/docs.jsonl\",\n", "    ),    \n", "]\n", "\n", "# Keys are \"instruction\", \"output\"\n", "EVOL_INSTRUCTIONS = Path(\"/mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1/train.shuffled.jsonl\")\n", "N_FIRST_EVOL_INSTRUCTIONS_TO_KEEP = 5000\n", "EVOL_INSTRUCTIONS_OUTPUT_FILE = OUTPUT_PATH / \"evol_codealpaca_v1_5K.jsonl\"\n", "\n", "FILEDS_TO_COVERT_TO_LISTS = [\n", "   'max_stars_repo_licenses',\n", "   'max_issues_repo_licenses',\n", "   'max_forks_repo_licenses',\n", "]"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["from research.core.types import Chunk\n", "\n", "\n", "@dataclasses.dataclass\n", "class Question:\n", "    question: str\n", "    retrievals: list[Chunk] = dataclasses.field(default_factory=list)\n", "    answers: dict[str, str] = dataclasses.field(default_factory=dict)\n", "\n", "    @classmethod\n", "    def from_dict(cls, data):\n", "        return cls(**data)\n", "\n", "\n", "@dataclasses.dataclass\n", "class DocumentWithQuestions:\n", "    repo_uuid: str\n", "    qa_set_name: str\n", "    path: str\n", "    text: str\n", "    questions: list[Question] = dataclasses.field(default_factory=list)\n", "\n", "    @classmethod\n", "    def from_dict(cls, data):\n", "        data_copy = {k: v for k, v in data.items()}\n", "        data_copy[\"questions\"] = [Question.from_dict(q) for q in data_copy[\"questions\"]]\n", "        return cls(**data_copy)\n", "    \n", "def load_docs(path):\n", "    docs = [DocumentWithQuestions.from_dict(json.loads(line)) for line in open(path)]\n", "    print(f\"Loaded {len(docs)} docs\")\n", "    return docs\n"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 400 docs\n", "Found 200 repos in /mnt/efs/augment/user/yury/binks/binks-v1/docs.jsonl\n"]}, {"name": "stderr", "output_type": "stream", "text": ["500it [00:02, 220.54it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 4000 docs\n", "Found 2000 repos in /mnt/efs/augment/user/yury/binks/binks-v1.1/docs.jsonl\n"]}, {"name": "stderr", "output_type": "stream", "text": ["20000it [00:43, 457.62it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 5600 docs\n", "Found 2800 repos in /mnt/efs/augment/user/yury/binks/binks-v1.2/docs.jsonl\n"]}, {"name": "stderr", "output_type": "stream", "text": ["15000it [00:42, 355.08it/s]\n"]}], "source": ["import glob\n", "import os\n", "import pandas as pd\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "\n", "visited_repo_uuids = set()\n", "used_repo_uuids = set()\n", "languages_set_for_unused = {}\n", "output_file = OUTPUT_FILE.open('w')\n", "\n", "for repo_pattern, docs_path in QA_DATA_V1:\n", "    docs = load_docs(docs_path)\n", "\n", "    repo_uuid_to_doc = {}\n", "    for doc in docs:\n", "        if doc.repo_uuid not in repo_uuid_to_doc:\n", "            repo_uuid_to_doc[doc.repo_uuid] = []\n", "        repo_uuid_to_doc[doc.repo_uuid].append(doc)\n", "    print('Found %d repos in %s' % (len(repo_uuid_to_doc), docs_path))\n", "\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "        assert repo[ID_COLUMN] not in visited_repo_uuids\n", "        visited_repo_uuids.add(repo[ID_COLUMN])\n", "        if repo[ID_COLUMN] in repo_uuid_to_doc:\n", "            used_repo_uuids.add(repo[ID_COLUMN])\n", "            all_paths = {r['max_stars_repo_path'] for r in repo['file_list']}\n", "            repo_d = repo.to_dict()\n", "            repo_d['file_list'] = repo_d['file_list'].tolist()\n", "            for file in repo_d['file_list']:\n", "                for k in FILEDS_TO_COVERT_TO_LISTS:\n", "                    file[k] = file[k].tolist()\n", "\n", "            docs = repo_uuid_to_doc[repo_d[ID_COLUMN]]\n", "            docs = [dataclasses.asdict(doc) for doc in docs]        \n", "\n", "            for doc in docs:\n", "                assert doc['qa_set_name'].startswith(doc['path'] + ' ')\n", "                doc['qa_set_name'] = doc['qa_set_name'][len(doc['path']) + 1:]\n", "                assert doc['path'] in all_paths\n", "                for question in doc['questions']:\n", "                    del question['retrievals']\n", "            \n", "            repo_d['documents_with_questions'] = docs\n", "            output_file.write(json.dumps(repo_d) + \"\\n\")\n", "        else:\n", "            lang = repo[REPO_LANG_COLUMN][REPO_LANG_SUBCOL]\n", "            if lang not in languages_set_for_unused:\n", "                languages_set_for_unused[lang] = 0\n", "            languages_set_for_unused[lang] += 1\n", "\n", "output_file.close()"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total repos: 35500, used: 5000, unqied languages in unused repos: 11\n"]}], "source": ["print('Total repos: %d, used: %d, unqied languages in unused repos: %d' % (len(visited_repo_uuids), len(used_repo_uuids), len(languages_set_for_unused)))"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/plain": ["['typescript',\n", " 'c-sharp',\n", " 'c',\n", " 'javascript',\n", " 'python',\n", " 'java',\n", " 'c++',\n", " 'go',\n", " 'ruby',\n", " 'rust',\n", " 'shell']"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["languages_list_for_unused = list(languages_set_for_unused.keys())\n", "languages_list_for_unused"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 5000 samples data, found 11 languages\n"]}], "source": ["import re\n", "import random\n", "\n", "random.seed(31415)\n", "\n", "counter_per_language = {l: 0 for l in languages_list_for_unused}\n", "\n", "\n", "def guess_language_prompt_sample(sample):\n", "    global counter_per_language\n", "\n", "    m = re.findall(\"```[^\\s]+\\s\", sample['output'])\n", "    if len(m) == 0:\n", "        m = re.findall(\"```[^\\s]+\\s\", sample['instruction'])\n", "    if len(m) == 0:\n", "        language = None\n", "    else:\n", "        language = m[0].lower().lstrip('```').strip()\n", "        if language not in languages_set_for_unused:\n", "            language = None\n", "    \n", "    while language is None or counter_per_language[language] >= languages_set_for_unused[language]:\n", "        language = random.choice(languages_list_for_unused)\n", "    counter_per_language[language] += 1\n", "    return language\n", "\n", "counter = 0\n", "evol_instruct_samples_per_language = {}\n", "\n", "with EVOL_INSTRUCTIONS.open() as f:\n", "    for line in f:\n", "        sample = json.loads(line[:-1])\n", "        language = guess_language_prompt_sample(sample)\n", "        if language not in evol_instruct_samples_per_language:\n", "            evol_instruct_samples_per_language[language] = []\n", "        evol_instruct_samples_per_language[language].append(sample)\n", "        counter += 1\n", "        if counter >= N_FIRST_EVOL_INSTRUCTIONS_TO_KEEP:\n", "            break\n", "\n", "print('Loaded %d samples data, found %d languages' % (counter, len(evol_instruct_samples_per_language)))\n", "\n", "for lang in languages_list_for_unused:\n", "    assert counter_per_language[lang] <= languages_set_for_unused[lang], (counter_per_language[lang], languages_set_for_unused[lang])"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["500it [00:03, 159.44it/s]\n", "20000it [00:51, 384.81it/s] \n", "15000it [00:27, 541.10it/s]\n"]}], "source": ["output_file = EVOL_INSTRUCTIONS_OUTPUT_FILE.open('w')\n", "\n", "counter_per_language = {l: 0 for l in languages_list_for_unused}\n", "\n", "for repo_pattern, docs_path in QA_DATA_V1:\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "        repo_lang = repo[REPO_LANG_COLUMN][REPO_LANG_SUBCOL]\n", "        if repo[ID_COLUMN] not in used_repo_uuids and counter_per_language[repo_lang] < len(evol_instruct_samples_per_language[repo_lang]):\n", "            \n", "            all_paths = {r['max_stars_repo_path'] for r in repo['file_list']}\n", "            repo_d = repo.to_dict()\n", "            repo_d['file_list'] = repo_d['file_list'].tolist()\n", "            for file in repo_d['file_list']:\n", "                for k in FILEDS_TO_COVERT_TO_LISTS:\n", "                    file[k] = file[k].tolist()\n", "\n", "            repo_d['instructions'] = evol_instruct_samples_per_language[repo_lang][counter_per_language[repo_lang]]\n", "            counter_per_language[repo_lang] += 1            \n", "            output_file.write(json.dumps(repo_d) + \"\\n\")\n", "            \n", "output_file.close()"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["for lang in languages_list_for_unused:\n", "    assert counter_per_language[lang] <= len(evol_instruct_samples_per_language[lang])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
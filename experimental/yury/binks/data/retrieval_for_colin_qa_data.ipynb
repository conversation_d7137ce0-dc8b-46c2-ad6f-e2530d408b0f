{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import glob"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 6600 samples\n"]}], "source": ["samples = []\n", "for path in glob.glob(\"/mnt/efs/augment/user/colin/data/binks/searchqa-01/04_added_path_hints/*.json\"):\n", "    samples.append(json.loads(open(path).read()))\n", "\n", "print(\"Loaded %d samples\" % len(samples))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PATH HINT? True\n", " QUESTION: Is there a method for calculating the distance between two geo points in the GeoPoint class of the Apache Unomi API?\n", "\n", "   ANSWER: Yes, there is a method for calculating the distance between two geo points in the GeoPoint class of the Apache Unomi API. The method is called `distanceTo` and it calculates the distance using the <PERSON><PERSON><PERSON> formula.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a function that retrieves the status of whether all stores are selected in the JavaScript for the admin section?\n", "\n", "   ANSWER: Yes, there is a function named `getAllStoreStatus` that retrieves the status of whether all stores are selected in the JavaScript for the admin section. This function checks the selected options for a `data-allStores` attribute and returns 'on' if all stores are selected, otherwise 'off'.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a function in the Email app's setup options tests that checks for the presence of a push option for different email account types?\n", "\n", "   ANSWER: Yes, there is a function in the Email app's setup options tests that checks for the presence of a push option for different email account types. The function `frequencySpinnerHasValue` is used within the test methods `testPushOptionPOP`, `testPushOptionIMAP`, and `testPushOptionEAS` to determine whether the push option, represented by `Account.CHECK_INTERVAL_PUSH`, is available for POP3, IMAP, and EAS accounts respectively.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Is there a method to handle screen transitions in BaseActivity?\n", "\n", "   ANSWER: Yes, there are methods to handle screen transitions in BaseActivity, which is located at `app/src/main/java/jrkim/rcash/ui/activities/BaseActivity.java`. These methods are `startActivity`, `startActivityForResult`, and `finish`. They all override the corresponding methods from the `AppCompatActivity` class to provide custom animations for screen transitions.\n", "\n", "For example, the `startActivity` method is overridden as follows:\n", "\n", "```java\n", "@Override\n", "public void startActivity(Intent intent) {\n", "    super.startActivity(intent);\n", "    overridePendingTransition(R.anim.slide_in_from_bottom, R.anim.slide_out_to_top);\n", "}\n", "```\n", "\n", "This code snippet shows that when a new activity is started, the transition animation is set to slide in the new activity from the bottom and slide out the current activity to the top. Similar overrides are done for `startActivityForResult` and `finish` to maintain consistent transition animations throughout the app.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Are there functions to process MCTP interface control requests?\n", "\n", "   ANSWER: Yes, there is a function designed to process MCTP interface control requests. The function is named `mctp_interface_control_process_request` and its purpose is to handle incoming requests for the MCTP interface control. Here is the relevant function prototype:\n", "\n", "```c\n", "int mctp_interface_control_process_request (struct mctp_interface *intf,\n", "\tstruct cmd_interface_request *request, uint8_t source_addr);\n", "```\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Are there functions for measuring memory usage?\n", "\n", "   ANSWER: Yes, there are functions for measuring memory usage, such as the `memory_usage` function from the `memory_profiler` module. It is utilized to measure the memory usage of different implementations for creating cell objects. Here's how it is used:\n", "\n", "```python\n", "initial_use = memory_usage(proc=-1, interval=1)[0]\n", "# ... some operations ...\n", "current_use = memory_usage(proc=-1, interval=1)[0] - initial_use\n", "```\n", "\n", "This function is called before and after operations that potentially consume memory to determine the amount of memory used by each process.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a test case for the Binary Convolution Layer in the shared plugin tests?\n", "\n", "   ANSWER: Yes, the test case for the Binary Convolution Layer is defined in the shared plugin tests for single layers. Here is the relevant code excerpt:\n", "\n", "```cpp\n", "namespace LayerTestsDefinitions {\n", "\n", "TEST_P(BinaryConvolutionLayerTest, CompareWithRefs) {\n", "    Run();\n", "}\n", "\n", "}  // namespace LayerTestsDefinitions\n", "```\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Where is the generalList method defined in the Icoolle project's Java core controller?\n", "\n", "   ANSWER: The generalList method is defined in the IcoolleController class within the Java core controller of the Icoolle project.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a function to add a new maintenance category in the actions related to maintenance categories?\n", "\n", "   ANSWER: Yes, the `registerMaintenancecategory` function is responsible for adding a new maintenance category. It is defined in the maintenance category actions file within the actions directory.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a class that helps build URIs for Google Sheets Stream endpoints in the Apache Camel Google Sheets component?\n", "\n", "   ANSWER: Yes, there is a class designed to help build URIs for Google Sheets Stream endpoints, and it is defined in the Google Sheets Stream component of the Apache Camel project. The class is named `GoogleSheetsStreamEndpointUriFactory` and it extends `org.apache.camel.support.component.EndpointUriFactorySupport` while implementing the `EndpointUriFactory` interface.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a GUID for the LoadedImage PPI defined in the EDK compatibility package?\n", "\n", "   ANSWER: Yes, the GUID for the LoadedImage PPI is defined in the EDK compatibility package. The GUID is named `gEfiPeiLoadedImagePpiGuid` and is initialized with the value `EFI_PEI_LOADED_IMAGE_PPI_GUID`.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Is there a custom validation function for a form control?\n", "\n", "   ANSWER: Yes, there is a custom validation function defined in the file at `src/app/modules/imports/import-types-create-edit/content-variable-dialog/content-variable-dialog.component.ts`. The function is named `notNamedTimeAndNotEmpty` and it is a static method of the `ContentVariableDialogComponent` class. This function checks if the form control value is not empty and not equal to the string 'time'. Here's the relevant code excerpt:\n", "\n", "```typescript\n", "static notNamedTimeAndNotEmpty(control: AbstractControl): ValidationErrors | null {\n", "    if (control.value === '') {\n", "        const err = {notNamedTimeAndNotEmpty: 'Name required'};\n", "        control.setErrors(err);\n", "        return err;\n", "    }\n", "    if (control.value === 'time') {\n", "        const err = {notNamedTimeAndNotEmpty: 'Name time not allowed at this level'};\n", "        control.setErrors(err);\n", "        return err;\n", "    }\n", "    control.setErrors(null);\n", "    return null;\n", "}\n", "```\n", "\n", "This function is used in the `ngOnInit` method to set a validator on the 'name' form control if the `nameTimeAllowed` flag is false in the component's data.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a function in the status view curses source that handles resizing when the window dimensions change?\n", "\n", "   ANSWER: Yes, the `statusview_curses::window_change()` function handles resizing when the window dimensions change. It is defined in the status view curses source file.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Does the Java class within the Jane GUI views directory include a method to sort package names?\n", "\n", "   ANSWER: Yes, the PackageView class within the Jane GUI views directory includes the `assign_model_impl` method, which sorts the names of packages. After accumulating package names in a HashSet, the method converts this set to an array and applies `Arrays.sort(temp)` to alphabetically order the package names. These sorted names are then used to instantiate `PackageNode` objects that are added to the `root_` of the view.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Is there an input type for ordering the count of device extra fields?\n", "\n", "   ANSWER: Yes, there is an input type for ordering the count of device extra fields defined in the file located at `src/@generated/prisma-nestjs-graphql/device-extra/device-extra-count-order-by-aggregate.input.ts`. The input type is named `DeviceExtraCountOrderByAggregateInput` and it allows specifying the sort order for various fields of a device extra entity, such as `id`, `deviceId`, `name`, `description`, `location`, `exportLocation`, and `locale`.\n", "\n", "Here's a relevant excerpt from the file:\n", "\n", "```typescript\n", "@InputType()\n", "export class DeviceExtraCountOrderByAggregateInput {\n", "    // ... other fields ...\n", "\n", "    @Field(() => SortOrder, {nullable:true})\n", "    locale?: keyof typeof SortOrder;\n", "}\n", "```\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Are there functions that retrieve keys from a KeyPair object in the Mozilla DOM-Crypto implementation?\n", "\n", "   ANSWER: Yes, there are functions that retrieve keys from a KeyPair object in the Mozilla DOM-Crypto implementation. The `PublicKey` function returns a pointer to a `Key` object representing the public key, and the `PrivateKey` function returns a pointer to a `Key` object representing the private key.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a function that determines the active Telegraf plugin in the PluginConfigSwitcher component?\n", "\n", "   ANSWER: Yes, the function that determines the active Telegraf plugin is defined as a getter method named `activeTelegrafPlugin` within the `PluginConfigSwitcher` class in the PluginConfigSwitcher component.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Where are the controllers defined?\n", "\n", "   ANSWER: Controllers are defined in their respective files within the project structure. For instance, the AddProductController class is defined in the file located at `src/presentation/controllers/product/add-product-controller.ts`.\n", "\n", "---\n", "\n", "PATH HINT? False\n", " QUESTION: Is there a function that tests the querying of node endpoints in a Zigbee network?\n", "\n", "   ANSWER: Yes, there is a function that tests the querying of node endpoints in a Zigbee network in the file `node_endpoints_test.go`. The function `Test_QueryNodeEndpoints` specifically checks if the system returns a success when a query for node endpoints is made and the response for the requested network address is received. Here's the relevant part of the function:\n", "\n", "```go\n", "func Test_QueryNodeEndpoints(t *testing.T) {\n", "    // Test body\n", "}\n", "```\n", "\n", "This function is part of a test suite that verifies the behavior of the `QueryNodeEndpoints` method under certain conditions, such as receiving the expected response from the mocked Zigbee network interface.\n", "\n", "---\n", "\n", "PATH HINT? True\n", " QUESTION: Is there a test suite for a React component in the test directory's Label.spec.js file?\n", "\n", "   ANSWER: Yes, there is a test suite for the `Label` React component defined in the Label.spec.js file within the test directory. The test suite includes several test cases that check various aspects of the `Label` component's rendering and behavior, using `react-test-renderer/shallow` for shallow rendering and `expect` for assertions.\n", "\n", "---\n", "\n"]}], "source": ["import random\n", "\n", "\n", "sample_ids = random.sample(range(len(samples)), 20)\n", "\n", "for sample_id in sample_ids:\n", "    sample = samples[sample_id]\n", "    assert sample[\"success\"]\n", "    print(\"PATH HINT?\", sample[\"added_path\"])\n", "    print(\" QUESTION:\", sample[\"new_question\"])\n", "    print(\"   ANSWER:\", sample[\"new_answer\"])    \n", "    print(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'added_path': True,\n", " 'selected_code': \"\\nimport React from 'react'\\nimport Shallow from 'react-test-renderer/shallow'\\nimport expect from 'expect'\\nimport { Label, Base } from '../src'\\n\\nconst renderer = new Shallow()\\n\\ndescribe('Label', () => {\\n  let tree\\n\\n  beforeEach(() => {\\n    renderer.render(<Label />)\\n    tree = renderer.getRenderOutput()\\n  })\\n\\n  it('should render', () => {\\n    expect(tree.type).toEqual(Base)\\n  })\\n\\n  it('should set tagName', () => {\\n    expect(tree.props.tagName).toEqual('label')\\n  })\\n\\n  it('should have a className', () => {\\n    expect(tree.props.className).toEqual('Label')\\n  })\\n\\n  it('should not have accessible hide styles', () => {\\n    expect(tree.props.baseStyle.position).toNotExist()\\n    expect(tree.props.baseStyle.height).toNotExist()\\n    expect(tree.props.baseStyle.width).toNotExist()\\n    expect(tree.props.baseStyle.overflow).toNotExist()\\n    expect(tree.props.baseStyle.clip).toNotExist()\\n  })\\n\\n  context('when hide is true', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label hide />)\\n      tree = renderer.getRenderOutput()\\n    })\\n    it('should have accessible hide styles', () => {\\n      expect(tree.props.baseStyle.position).toEqual('absolute')\\n      expect(tree.props.baseStyle.height).toEqual(1)\\n      expect(tree.props.baseStyle.width).toEqual(1)\\n      expect(tree.props.baseStyle.overflow).toEqual('hidden')\\n      expect(tree.props.baseStyle.clip).toEqual('rect(1px, 1px, 1px, 1px)')\\n    })\\n  })\\n\\n  context('when custom styles are set', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label style={{ color: 'tomato' }} />)\\n      tree = renderer.getRenderOutput()\\n    })\\n\\n    it('should have a custom color', () => {\\n      expect(tree.props.style.color).toEqual('tomato')\\n    })\\n  })\\n})\\n\",\n", " 'old_question': 'Is there a test suite for a React component in the file test/Label.spec.js?\\n',\n", " 'new_question': \"Is there a test suite for a React component in the test directory's Label.spec.js file?\\n\",\n", " 'old_answer': \"Yes, there is a test suite for the `Label` React component defined in the file located at `test/Label.spec.js`. The test suite includes several test cases that check various aspects of the `Label` component's rendering and behavior. Here's an excerpt from the file showing the structure of the test suite:\\n\\n```javascript\\ndescribe('Label', () => {\\n  // ... beforeEach and individual test cases ...\\n})\\n```\\n\\nThis test suite uses `react-test-renderer/shallow` to render the component and `expect` for assertions.\",\n", " 'new_answer': \"Yes, there is a test suite for the `Label` React component defined in the Label.spec.js file within the test directory. The test suite includes several test cases that check various aspects of the `Label` component's rendering and behavior, using `react-test-renderer/shallow` for shallow rendering and `expect` for assertions.\",\n", " 'chat_example': {'question': 'Is there a test suite for a React component in the file test/Label.spec.js?\\n',\n", "  'answer': \"Yes, there is a test suite for the `Label` React component defined in the file located at `test/Label.spec.js`. The test suite includes several test cases that check various aspects of the `Label` component's rendering and behavior. Here's an excerpt from the file showing the structure of the test suite:\\n\\n```javascript\\ndescribe('Label', () => {\\n  // ... beforeEach and individual test cases ...\\n})\\n```\\n\\nThis test suite uses `react-test-renderer/shallow` to render the component and `expect` for assertions.\",\n", "  'filepath': 'test/Label.spec.js',\n", "  'repo_uuid': 'f57fa26f-b1ce-4f25-97ae-2a1e8caf41e6',\n", "  'selected_code': \"\\nimport React from 'react'\\nimport Shallow from 'react-test-renderer/shallow'\\nimport expect from 'expect'\\nimport { Label, Base } from '../src'\\n\\nconst renderer = new Shallow()\\n\\ndescribe('Label', () => {\\n  let tree\\n\\n  beforeEach(() => {\\n    renderer.render(<Label />)\\n    tree = renderer.getRenderOutput()\\n  })\\n\\n  it('should render', () => {\\n    expect(tree.type).toEqual(Base)\\n  })\\n\\n  it('should set tagName', () => {\\n    expect(tree.props.tagName).toEqual('label')\\n  })\\n\\n  it('should have a className', () => {\\n    expect(tree.props.className).toEqual('Label')\\n  })\\n\\n  it('should not have accessible hide styles', () => {\\n    expect(tree.props.baseStyle.position).toNotExist()\\n    expect(tree.props.baseStyle.height).toNotExist()\\n    expect(tree.props.baseStyle.width).toNotExist()\\n    expect(tree.props.baseStyle.overflow).toNotExist()\\n    expect(tree.props.baseStyle.clip).toNotExist()\\n  })\\n\\n  context('when hide is true', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label hide />)\\n      tree = renderer.getRenderOutput()\\n    })\\n    it('should have accessible hide styles', () => {\\n      expect(tree.props.baseStyle.position).toEqual('absolute')\\n      expect(tree.props.baseStyle.height).toEqual(1)\\n      expect(tree.props.baseStyle.width).toEqual(1)\\n      expect(tree.props.baseStyle.overflow).toEqual('hidden')\\n      expect(tree.props.baseStyle.clip).toEqual('rect(1px, 1px, 1px, 1px)')\\n    })\\n  })\\n\\n  context('when custom styles are set', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label style={{ color: 'tomato' }} />)\\n      tree = renderer.getRenderOutput()\\n    })\\n\\n    it('should have a custom color', () => {\\n      expect(tree.props.style.color).toEqual('tomato')\\n    })\\n  })\\n})\\n\"},\n", " 'response1': \"QUESTION: Is there a test suite for a React component in the test directory's Label.spec.js file?\\nANSWER: Yes, there is a test suite for the `Label` React component defined in the Label.spec.js file within the test directory. The test suite includes several test cases that check various aspects of the `Label` component's rendering and behavior, using `react-test-renderer/shallow` for shallow rendering and `expect` for assertions.\",\n", " 'prompt1': '\\nBelow is a code snippet from the file located at `test/Label.spec.js`\\n\\n```\\n\\nimport React from \\'react\\'\\nimport Shallow from \\'react-test-renderer/shallow\\'\\nimport expect from \\'expect\\'\\nimport { Label, Base } from \\'../src\\'\\n\\nconst renderer = new Shallow()\\n\\ndescribe(\\'Label\\', () => {\\n  let tree\\n\\n  beforeEach(() => {\\n    renderer.render(<Label />)\\n    tree = renderer.getRenderOutput()\\n  })\\n\\n  it(\\'should render\\', () => {\\n    expect(tree.type).toEqual(Base)\\n  })\\n\\n  it(\\'should set tagName\\', () => {\\n    expect(tree.props.tagName).toEqual(\\'label\\')\\n  })\\n\\n  it(\\'should have a className\\', () => {\\n    expect(tree.props.className).toEqual(\\'Label\\')\\n  })\\n\\n  it(\\'should not have accessible hide styles\\', () => {\\n    expect(tree.props.baseStyle.position).toNotExist()\\n    expect(tree.props.baseStyle.height).toNotExist()\\n    expect(tree.props.baseStyle.width).toNotExist()\\n    expect(tree.props.baseStyle.overflow).toNotExist()\\n    expect(tree.props.baseStyle.clip).toNotExist()\\n  })\\n\\n  context(\\'when hide is true\\', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label hide />)\\n      tree = renderer.getRenderOutput()\\n    })\\n    it(\\'should have accessible hide styles\\', () => {\\n      expect(tree.props.baseStyle.position).toEqual(\\'absolute\\')\\n      expect(tree.props.baseStyle.height).toEqual(1)\\n      expect(tree.props.baseStyle.width).toEqual(1)\\n      expect(tree.props.baseStyle.overflow).toEqual(\\'hidden\\')\\n      expect(tree.props.baseStyle.clip).toEqual(\\'rect(1px, 1px, 1px, 1px)\\')\\n    })\\n  })\\n\\n  context(\\'when custom styles are set\\', () => {\\n    beforeEach(() => {\\n      renderer.render(<Label style={{ color: \\'tomato\\' }} />)\\n      tree = renderer.getRenderOutput()\\n    })\\n\\n    it(\\'should have a custom color\\', () => {\\n      expect(tree.props.style.color).toEqual(\\'tomato\\')\\n    })\\n  })\\n})\\n\\n```\\n\\nHere is a question about the code snippet: `Is there a test suite for a React component in the file test/Label.spec.js?\\n`\\n\\nHere is an answer to the question: `Yes, there is a test suite for the `Label` React component defined in the file located at `test/Label.spec.js`. The test suite includes several test cases that check various aspects of the `Label` component\\'s rendering and behavior. Here\\'s an excerpt from the file showing the structure of the test suite:\\n\\n```javascript\\ndescribe(\\'Label\\', () => {\\n  // ... beforeEach and individual test cases ...\\n})\\n```\\n\\nThis test suite uses `react-test-renderer/shallow` to render the component and `expect` for assertions.`\\n\\nInstructions:\\n\\nPlease update the question to make a reference to one of the directories the source code file is contained in. \\nYou MUST use a natural language shorthand for the directory, instead of using the actual directory path.\\n\\n\\nHere are some additional comments about the instruction:\\n- If necessary, also update the answer accordingly. \\n- The question MUST be kept simple.\\n- The question MUST sound natural and human-like. \\n- Whatever you do, do NOT use the phrase \"in the code snippet\" in the question or answer.\\n- You also MUST ABIDE to the instruction and aformentioned comments, no matter what. You will get a tip if you\\nfollow the instruction carefully. You will get a punishment if you don\\'t follow the instruction\\ncarefully.\\n\\nFormat the output in style of the following example:\\n```\\nQUESTION: Where is Foo defined?\\nANSWER: Foo is defined in file /path/to/foo.rs.\\n```\\n'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sample"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3317 samples with path hints\n", "Loaded 3283 samples without path hints\n"]}], "source": ["samples_with_path_hints = [sample for sample in samples if sample[\"added_path\"]]\n", "samples_without_path_hints = [sample for sample in samples if not sample[\"added_path\"]]\n", "print(\"Loaded %d samples with path hints\" % len(samples_with_path_hints))\n", "print(\"Loaded %d samples without path hints\" % len(samples_without_path_hints))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 2279 repos\n"]}], "source": ["import dataclasses \n", "\n", "@dataclasses.dataclass\n", "class QASample:\n", "    question: str\n", "    paths: list[str]\n", "    answer: str\n", "\n", "samples_per_repo = {}\n", "\n", "for sample in samples_with_path_hints:\n", "    repo_uuid = sample['chat_example']['repo_uuid']\n", "    qa_sample = QASample(\n", "        question=sample['new_question'].strip().replace(\"`\", \"\"),\n", "        paths=[sample['chat_example']['filepath']],\n", "        answer=sample['new_answer'].strip(),\n", "    )\n", "    if repo_uuid in samples_per_repo:\n", "        samples_per_repo[repo_uuid].append(qa_sample)\n", "    else:\n", "        samples_per_repo[repo_uuid] = [qa_sample]\n", "\n", "print(\"Loaded %d repos\" % len(samples_per_repo))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["14724it [02:13, 547.57it/s]"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import glob\n", "import tqdm\n", "import os\n", "import pandas as pd\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "\n", "FILEDS_TO_COVERT_TO_LISTS = [\n", "   'max_stars_repo_licenses',\n", "   'max_issues_repo_licenses',\n", "   'max_forks_repo_licenses',\n", "]\n", "\n", "n_saved_repos, n_saved_samples, n_filted_repos, n_filtered_samples = 0, 0, 0, 0\n", "\n", "FILES = '/mnt/efs/augment/user/yury/binks/colin/*.zstd.parquet'\n", "pbar = tqdm.tqdm(ParquetRepoDataset(FILES))\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-colin-with-path-hints/repos_with_qa.jsonl\", \"w\") as f:\n", "    for repo in pbar:\n", "        repo_uuid = repo['repo_uuid']    \n", "        repo_name = repo['max_stars_repo_name']\n", "\n", "        if repo_uuid not in samples_per_repo:\n", "            continue\n", "\n", "        repo_d = repo.to_dict()\n", "        repo_d['file_list'] = repo_d['file_list'].tolist()\n", "        for file in repo_d['file_list']:\n", "            for k in FILEDS_TO_COVERT_TO_LISTS:\n", "                if not isinstance(file[k], list):\n", "                    file[k] = file[k].tolist()        \n", "\n", "        all_paths = {r['max_stars_repo_path'] for r in repo['file_list']}\n", "\n", "        for sample in samples_per_repo[repo_uuid]:\n", "            for path in sample.paths:\n", "                assert path in all_paths\n", "\n", "            repo_d['documents_with_questions'] = [dataclasses.asdict(sample) for sample in samples_per_repo[repo_uuid]]\n", "\n", "            sample_json = json.dumps(repo_d)\n", "            f.write(sample_json + \"\\n\")\n", "        \n", "        del repo_d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
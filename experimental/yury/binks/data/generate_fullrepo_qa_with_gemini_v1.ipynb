{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "import tqdm\n", "import dataclasses\n", "from types import SimpleNamespace"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v2-gemini\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "QA_DATA_V1 = BASE_PATH / \"binks-v1.3-merged/repos_with_qa.jsonl\"\n", "    \n", "config = SimpleNamespace(\n", "    **{\n", "        \"repo_languages\": REPO_LANGUAGES,\n", "        \"sample_languages\": SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": RETRIEVAL_LANGUAGES,\n", "        \"limit_repos_from_the_stack\": 500,\n", "        \"limit_repos\": 1000,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"min_lines_per_file\": 200,\n", "        \"max_lines_per_file\": 1000,\n", "        \"file_max_size\": 10000,\n", "        \"n_questions_per_file\": 5,\n", "        \"random_seed\": 31415,    \n", "        \"n_retrievals\": 25,\n", "        \"p_local_context_dropout\": 0.05,\n", "        \"p_empty_selection\": 0.45,\n", "        \"max_lines_per_selected_code\": 50,\n", "        \"max_seq_length\": 16384 + 1,\n", "        \"num_validation_samples\": 200,\n", "    }\n", ")\n", "\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=2048,\n", "    prefix_len=1536,\n", "    selected_code_len=1536,\n", "    chat_history_len=1536,\n", "    suffix_len=1024,\n", "    max_prompt_len=16384 - 2048,  # 2048 represents the max output tokens\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5000\n"]}], "source": ["already_used_repos = set()\n", "\n", "with open(QA_DATA_V1) as f:\n", "    for line in f:\n", "        repo = json.loads(line[:-1])\n", "        already_used_repos.add(repo[\"max_stars_repo_name\"])\n", "\n", "print(len(already_used_repos))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["500it [00:01, 310.54it/s]\n", "2699it [00:08, 309.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["1000 2200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import glob\n", "import os\n", "import pandas as pd\n", "\n", "QA_DATA_V1 = [\n", "    BASE_PATH / \"binks-v1/01_raw_repos/part-00000-9990b718-de5b-4aed-97d4-968fd1302624-c000.zstd.parquet\",\n", "    BASE_PATH / \"binks-v1.1/01_raw_repos/part-0000*-3ccecdd8-89f4-4520-b804-988dbe95a44d-c000.zstd.parquet\",\n", "    BASE_PATH / \"binks-v1.2/01_raw_repos/part-0000*-a63b384a-3dd5-45bd-aeb5-0dd345dbcad0-c000.zstd.parquet\",\n", "]\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "REPOS = {}\n", "n_already_used = 0\n", "\n", "for repo_pattern in QA_DATA_V1:\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "        if repo[\"max_stars_repo_name\"] in already_used_repos:\n", "            n_already_used += 1\n", "            continue\n", "        REPOS[repo[\"repo_uuid\"]] = repo\n", "        if len(REPOS) >= config.limit_repos:\n", "            break\n", "    if len(REPOS) >= config.limit_repos:\n", "        break\n", "\n", "print(len(REPOS), n_already_used)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["max_stars_repo_name                                    whokion/celeritas\n", "max_file_lang                     {'file_count': 404, 'langpart': 'c++'}\n", "max_size_lang                 {'total_size': 1642416, 'langpart': 'c++'}\n", "total_size                                                       1760559\n", "file_list              [{'hexsha': '7ddbacf168617abc8efb02e7b51a47759...\n", "repo_uuid                           d27c4e56-3462-41ef-977f-ed609ca9c163\n", "Name: 699, dtype: object"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["repo"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Below are the files from the whokion/celeritas project. Read them carefully.\n", "\n", "This is file `scripts/build/docker-setup.sh`\n", "\n", "```\n", "\n", "#!/bin/sh -e\n", "\n", "if [ -z \"${SOURCE_DIR}\" ]; then\n", "  test -n \"${BUILDSCRIPT_DIR}\"\n", "  SOURCE_DIR=\"$(cd \"${BUILDSCRIPT_DIR}\" && git rev-parse --show-toplevel)\"\n", "else\n", "  SOURCE_DIR=\"$(cd \"${SOURCE_DIR}\" && pwd)\"\n", "fi\n", "\n", "if [ -z \"${BUILD_DIR}\" ]; then\n", "  : ${BUILD_SUBDIR:=build}\n", "  BUILD_DIR=${SOURCE_DIR}/${BUILD_SUBDIR}\n", "fi\n", "\n", ": ${CTEST_ARGS:=--output-on-failure}\n", "if [ -z \"${PARALLEL_LEVEL\n"]}], "source": ["def get_repo_prompt(repo):\n", "    full_repo = [\n", "        f\"Below are the files from the {repo[REPO_COLUMN]} project. Read them carefully.\"\n", "    ]\n", "\n", "    for file in repo[FILE_LIST_COLUMN]:\n", "        if file['langpart'] in RETRIEVAL_LANGUAGES:\n", "            full_repo.extend([\n", "                \"This is file `%s`\" % file['max_stars_repo_path'],\n", "                \"```\",\n", "                file[CONTENT_COLUMN],\n", "                \"```\",\n", "            ])\n", "\n", "    return \"\\n\\n\".join(full_repo)\n", "\n", "repo_prompt = get_repo_prompt(repo)\n", "\n", "print(repo_prompt[:500])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import vertexai\n", "from vertexai.generative_models import GenerativeModel, ChatSession, GenerationConfig\n", "project_id = \"gemini-pro-420822\"\n", "location = \"us-central1\"\n", "vertexai.init(project=project_id, location=location)\n", "\n", "generation_config = GenerationConfig(\n", "    candidate_count=1,\n", "    temperature=0.7,\n", "    max_output_tokens=8192,\n", ")\n", "GOOGLE_MODEL = GenerativeModel(\n", "    model_name=\"gemini-1.5-pro-preview-0409\",\n", "    generation_config=generation_config,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["QUESTIONS_PROMPT = \"\"\"\n", "---\n", "\n", "Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help locating specific functionalities within the repository. They have AI assistant they are going to ask for help. However, the software engineer is impatient and writes very concise questions, almost like Google search queries. Indeed, this the software engineer needs to locate information quickly and they don't have time to write elaborate or double questions. What kind of questions would this software engineer ask? Try to come up with these questions.\n", "\n", "**Your task is to generate 20 questions about a software project.**\n", "\n", "## Guidelines for Question Generation\n", "\n", "- Focus on One Aspect: Each question should target only one feature, process, or setup aspect.\n", "- Encourage Search-Like Questions: Questions should help locate specific functionalities or data handling within the codebase, similar to performing a search task.\n", "- AVOID General Questions: Never ask \"What is the purpose\" or \"What is the role.\"\n", "- AVOID Complex Questions: Do not include compound or double-barreled questions.\n", "- Cover Different Areas: Question SHOULD NOT BE about the same files.\n", "- Relate to MULTIPLE Relevant Files: Each question should involve two to four relevant files, providing detailed insight.\n", "\n", "## Output Format\n", "\n", "Display each question in a numbered list, one per line, without additional details.\n", "\n", "## Example Questions Templates\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "\n", "- How do we configure X?\n", "- What data structure is used to store information about X?\n", "- Where is the logic for X defined?\n", "- How does X handle Y?\n", "- Which files define X?\n", "- Where can I find implementation of X?\n", "- Which X is used for data storage?\n", "- How X are handled?\n", "\n", "You also MUST ABIDE to the instruction, no matter what.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/gemini_quetions_cache.jsonl\")\n", "def generate_questions_with_gemini(repo_uuid):\n", "    global GOOGLE_MODEL\n", "    global REPOS\n", "    global QUESTIONS_PROMPT\n", "    repo = REPOS[repo_uuid]\n", "\n", "    repo_prompt = get_repo_prompt(repo)\n", "    message = repo_prompt + \"\\n\\n\" + QUESTIONS_PROMPT\n", "\n", "    chat = GOOGLE_MODEL.start_chat()\n", "    question_generation_config = GenerationConfig(\n", "        candidate_count=1,\n", "        temperature=0.8,\n", "        max_output_tokens=1024,\n", "    )\n", "    responses = chat.send_message(message, stream=False, generation_config=question_generation_config)\n", "\n", "    usage_metadata = {\n", "        \"candidates_token_count\": responses.usage_metadata.candidates_token_count,\n", "        \"prompt_token_count\": responses.usage_metadata.prompt_token_count,\n", "        \"total_token_count\": responses.usage_metadata.total_token_count,\n", "    }\n", "    return {\n", "        \"text\": responses.text,\n", "        \"usage_metadata\": usage_metadata,\n", "    }\n", "\n", "QUESTIONS = {}\n", "for repo_uuid in tqdm.tqdm(REPOS):\n", "    try:\n", "        QUESTIONS[repo_uuid] = generate_questions_with_gemini(repo_uuid)\n", "    except Exception as e:\n", "        print(f\"Failed to generate for {repo_uuid} due to {e}\")\n", "    if len(QUESTIONS) >= 1000:\n", "        break\n", "\n", "print(\"Generated %d sets ofquestions\" % len(QUESTIONS))\n", "\n", "# 100%|██████████| 1000/1000 [6:45:49<00:00, 24.35s/it]\n", "# Generated 969 sets ofquestions"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 969 questions\n"]}], "source": ["QUESTIONS_PATH = \"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/gemini_quetions_cache.jsonl\"\n", "\n", "QUESTIONS = {}\n", "for line in open(QUESTIONS_PATH):\n", "    data = json.loads(line[:-1])\n", "    QUESTIONS[data[\"key\"]] = data[\"value\"]\n", "\n", "print(\"Loaded %d questions\" % len(QUESTIONS))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Software Engineer's Questions:\n", "\n", "1. How is the `epoch` value updated? \n", "2. What data structure holds `SystemInfo` objects?\n", "3. How are `HostState` objects stored in the database? \n", "4. Where is the logic for handling Kafka messages defined? \n", "5. Which files define the `Worker` class and its functionalities?\n", "6. How are routine ping tasks executed and managed? \n", "7. Where can I find the implementation for handling HTTP requests? \n", "8. How are channel attributes updated and stored? \n", "9. How are unavail host states retrieved from the database? \n", "10. Which files are involved in generating routine files for workers?\n", "11. How are system mappings represented and stored in ZooKeeper? \n", "12. Where is the logic for worker registration in <PERSON><PERSON><PERSON><PERSON> defined?\n", "13. How are claim notices processed and used for host registration?\n", "14. Where can I find the implementation of the `SystemQueryService`?\n", "15. How are `RegistrationException` objects handled and stored? \n", "16. Which files define the structure and behavior of `WorkerRecorder`? \n", "17. How are system segments allocated to workers during remapping? \n", "18. Where is the logic for re-balancing worker load implemented? \n", "19. How are IP addresses validated and handled in channel updates? \n", "20. What is the process for generating and storing UUIDs for hosts? \n", "\n"]}], "source": ["# print(QUESTIONS[\"9100bac5-ce3f-4ebb-9a8e-9bebb929ecee\"]['text'])\n", "# print(QUESTIONS[\"6b17beb4-56a6-471c-b841-260660a2cfcc\"]['text'])\n", "# print(QUESTIONS[\"76faf84d-3f82-4813-bb23-bdaa8f24d15e\"]['text'])\n", "# print(QUESTIONS[\"0b9cd3f7-c757-448e-96ee-c8dc3d5cd1e8\"]['text'])\n", "# print(QUESTIONS[\"c3e68893-f862-4dd6-a308-84d03f11e16e\"]['text'])\n", "# print(QUESTIONS[\"6d0a0f5d-089d-46f5-8ba9-394be2e86c0d\"]['text'])\n", "# print(QUESTIONS[\"de1f1654-e1a3-4773-a23d-7d2174a97042\"]['text'])\n", "# print(QUESTIONS[\"750310a3-93d2-46d5-8a6a-39b609500197\"]['text'])\n", "print(QUESTIONS[\"e7643e35-ec11-4a48-adce-b716dc448130\"]['text'])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["ANSWERS_PROMPT = \"\"\"\n", "---\n", "\n", "Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help locating specific functionalities within the repository. You are an AI assistant they are going to ask for help -- answer their questions.\n", "\n", "**Your task is to answer <PERSON><PERSON> these questions about the software project above:**\n", "\n", "%s\n", "\n", "# Objective\n", "- List References: For EVERY question, provide a complete list of ALL files with necessary details for the question.\n", "- Provide Answers: Provide a focused yet complete answer for each question and include a relevant code excerpt from the project or a code example to demonstrate the API usage.\n", "\n", "# Guidelines for Reference\n", "- List all relevant files using full paths, formatted with single ticks (`).\n", "- Avoid using wildcard patterns ('*') or simply listing directories; be sure to specify individual files only.\n", "\n", "# Guidelines for Answer Generation\n", "- Incorporate file paths fluently into the answer, ensuring all paths are shown with full detail and formatted correctly.\n", "- Always include a code snippet in each answer to illustrate the practical implementation or functionality being discussed. This can either be a direct quote from one of the references or a short example demonstrating API usage.\n", "\n", "# Output Format:\n", "For EVERY question use Markdown headers to structure each response as follows.\n", "\n", "======================= SAMPLE OUTPUT IS BELOW =======================\n", "\n", "# Question 1\n", "How is user authentication handled in the project?\n", "\n", "## References\n", "- `ProjectRoot/Services/AuthService.cs`\n", "- `ProjectRoot/Config/UserConfig.cs`\n", "\n", "## Answer\n", "User authentication in the project is managed through a dedicated service defined in `ProjectRoot/Services/AuthService.cs`, with configuration settings outlined in `ProjectRoot/Config/UserConfig.cs`. Here are examples from both files demonstrating the authentication process:\n", "\n", "Snippet from `ProjectRoot/Services/AuthService.cs`:\n", "\n", "```\n", "public bool AuthenticateUser(string username, string password)\n", "{\n", "    // Authentication logic here\n", "    return CheckCredentials(username, password);\n", "}\n", "```\n", "\n", "Snippet from `ProjectRoot/Config/UserConfig.cs`:\n", "\n", "```\n", "public Dictionary<string, string> GetUserSettings()\n", "{\n", "    // Load user settings from configuration\n", "    return new Dictionary<string, string> { {\"maxAttempts\", \"5\"}, {\"timeout\", \"00:30:00\"} };\n", "}\n", "```\n", "\n", "These methods work together to ensure that user credentials are checked against security settings, managing authentication attempts and session timeouts as configured in `ProjectRoot/Config/UserConfig.cs`, ensuring a secure user authentication process.\n", "\n", "---\n", "\n", "... Questions from 2 to 19 ...\n", "\n", "---\n", "\n", "# Question 20\n", "How is network communication structured and secured within the project?\n", "\n", "## References\n", "- `ProjectRoot/Network/NetworkConfig.cs`\n", "- `ProjectRoot/Network/NetworkService.cs`\n", "\n", "## Answer\n", "Network communication within the project is orchestrated and secured through settings specified in `ProjectRoot/Network/NetworkConfig.cs`, with operational management by `ProjectRoot/Network/NetworkService.cs. Here's an example of how network settings are initialized and applied:\n", "\n", "\n", "```\n", "public void ConfigureNetwork() {\n", "    NetworkConfiguration netConfig = new NetworkConfiguration();\n", "    // Setup network parameters\n", "    netConfig.UseEncryption = true;\n", "    netConfig.EncryptionType = \"AES\";\n", "    netConfig.RequireAuthentication = true;\n", "\n", "    // Apply configuration\n", "    NetworkService.ApplyConfiguration(netConfig);\n", "}\n", "```\n", "\n", "This configuration snippet illustrates the foundational network setup, emphasizing security features like encryption and required authentication, showcasing the thoroughness of the project’s network management strategy.\n", "\n", "======================= END OF SAMPLE OUTPUT =======================\n", "\n", "ANSWER ALL QUESTIONS!! You MUST provide answer for ALL 20 questions! I will get FIRED and will NOT be able to afford rent if you don't answer ALL 20 questions!\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "def extract_questions(text):\n", "    index1 = text.find(\"1.\")\n", "    if index1 == -1:\n", "        return text\n", "    else:\n", "        return text[index1:]\n", "\n", "def count_questions(text):\n", "    for index in reversed(range(1, 21)):\n", "        s = \"Question %s\\n\" % index\n", "        if s in text:\n", "            return index\n", "    return 0\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/gemini_answers_cache.jsonl\")\n", "def generate_answers_with_gemini(repo_uuid):\n", "    global GOOGLE_MODEL\n", "    global REPOS\n", "    global ANSWERS_PROMPT\n", "    global QUESTIONS\n", "    repo = REPOS[repo_uuid]\n", "    questions = extract_questions(QUESTIONS[repo_uuid]['text'])\n", "\n", "    answer_prompt = ANSWERS_PROMPT % questions\n", "    repo_prompt = get_repo_prompt(repo)\n", "    message = repo_prompt + \"\\n\\n\" + answer_prompt\n", "\n", "    chat = GOOGLE_MODEL.start_chat()\n", "    answer_generation_config = GenerationConfig(\n", "        candidate_count=1,\n", "        temperature=1.0,\n", "        max_output_tokens=8192,\n", "    )\n", "    try:\n", "        responses = chat.send_message(message, stream=False, generation_config=answer_generation_config)\n", "    except Exception as e:\n", "        print(f\"Failed to generate for {repo_uuid} due to {e}\")\n", "        return {\"text\": None, \"usage_metadata\": {}}        \n", "\n", "    usage_metadata = {\n", "        \"candidates_token_count\": responses.usage_metadata.candidates_token_count,\n", "        \"prompt_token_count\": responses.usage_metadata.prompt_token_count,\n", "        \"total_token_count\": responses.usage_metadata.total_token_count,\n", "    }\n", "    return {\n", "        \"text\": responses.text,\n", "        \"usage_metadata\": usage_metadata,\n", "    }\n", "\n", "ANSWERS = {}\n", "\n", "n_total_questions = 0\n", "# pbar = tqdm.tqdm(['9100bac5-ce3f-4ebb-9a8e-9bebb929ecee', '6b17beb4-56a6-471c-b841-260660a2cfcc', 'c3e68893-f862-4dd6-a308-84d03f11e16e', '79272fd4-6922-4e07-b436-7ce040a64912', '305a0be8-e03e-4a5c-bc69-4758d23bac6b', '9ccc9ef5-267e-408c-9659-84090712a8cf', 'de1f1654-e1a3-4773-a23d-7d2174a97042', '750310a3-93d2-46d5-8a6a-39b609500197'])\n", "pbar = tqdm.tqdm(QUESTIONS) \n", "\n", "for repo_uuid in pbar:\n", "    ANSWERS[repo_uuid] = generate_answers_with_gemini(repo_uuid)\n", "    if ANSWERS[repo_uuid]['text'] is not None:\n", "        n_total_questions += count_questions(ANSWERS[repo_uuid]['text'])        \n", "    pbar.set_postfix({\"total_questions\": n_total_questions})\n", "        \n", "# 100%|██████████| 969/969 [17:47:38<00:00, 66.11s/it, total_questions=12768] "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded samples for 969 repos\n"]}], "source": ["ANSWERS = {}\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/gemini_answers_cache.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        ANSWERS[data[\"key\"]] = data[\"value\"]\n", "\n", "print(\"Loaded samples for %d repos\" % len(ANSWERS))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'text': \"## Question 1: How to configure DocumentClient? \\n\\n### References\\n- `src/node_modules/documentdb/lib/documentclient.js`\\n\\n### Answer\\nThe `DocumentClient` class in `src/node_modules/documentdb/lib/documentclient.js` is configured during its construction. Here's an example:\\n\\n```javascript\\nconst DocumentClient = require('./documentclient');\\n\\nconst client = new DocumentClient(\\n    'https://your-account.documents.azure.com:443/',\\n    { masterKey: 'your-master-key' },\\n    { EnableEndpointDiscovery: true },\\n    'Session'\\n);\\n```\\n\\nIn this example, we provide the DocumentDB service endpoint URL, an object containing the authorization master key, a connection policy object with endpoint discovery enabled, and a consistency level. \\n\\n## Question 2: Where is the logic for handling parallelized queries defined?\\n\\n### References\\n- `src/node_modules/documentdb/lib/queryExecutionContext/parallelQueryExecutionContext.js`\\n\\n### Answer\\nThe `ParallelQueryExecutionContext` class in `src/node_modules/documentdb/lib/queryExecutionContext/parallelQueryExecutionContext.js` encapsulates the logic for handling parallelized queries. It manages multiple `DocumentProducer` instances, each responsible for a specific partition key range, and aggregates their results. \\n\\n## Question 3: How does `DocumentProducer` handle buffering?\\n\\n### References\\n- `src/node_modules/documentdb/lib/queryExecutionContext/documentProducer.js`\\n\\n### Answer\\nThe `DocumentProducer` class in `src/node_modules/documentdb/lib/queryExecutionContext/documentProducer.js` uses an internal buffer (`itemsBuffer`) to store fetched documents. Methods like `peekBufferedItems` and `consumeBufferedItems` allow access and manipulation of this buffer. \\n\\n## Question 4: Which files define `PipelinedQueryExecutionContext`?\\n\\n### References\\n- `src/node_modules/documentdb/lib/queryExecutionContext/pipelinedQueryExecutionContext.js`\\n\\n### Answer\\nThe `PipelinedQueryExecutionContext` class is defined in `src/node_modules/documentdb/lib/queryExecutionContext/pipelinedQueryExecutionContext.js`. It handles the pipelining of top and orderby execution contexts when necessary.\\n\\n## Question 5: Where is the implementation of `RetryUtility`?\\n\\n### References\\n- `src/node_modules/documentdb/lib/retryUtility.js`\\n\\n### Answer\\nThe implementation of `RetryUtility` is located in `src/node_modules/documentdb/lib/retryUtility.js`. It provides methods for executing and applying retry policies for DocumentDB requests.\\n\\n## Question 6: How are `FeedOptions` used?\\n\\n### References\\n- `src/node_modules/documentdb/lib/documentclient.js`\\n\\n### Answer\\n`FeedOptions` are used to configure various aspects of feed operations, such as the maximum number of items to return and continuation tokens. They are passed as arguments to methods like `queryDocuments` in `src/node_modules/documentdb/lib/documentclient.js`:\\n\\n```javascript\\nconst queryIterator = client.queryDocuments(collectionLink, query, { maxItemCount: 10 });\\n```\\n\\n## Question 7: Where can I find the definition of `IndexingPolicy`?\\n\\n### References\\n- `src/node_modules/documentdb/lib/documentclient.js`\\n\\n### Answer\\nThe `IndexingPolicy` type definition is located in `src/node_modules/documentdb/lib/documentclient.js`. It specifies the indexing configuration for a collection, including automatic indexing, indexing mode, and included/excluded paths.\\n\\n## Question 8: Which files handle HTTP requests?\\n\\n### References\\n- `src/node_modules/documentdb/lib/request.js`\\n- `src/node_modules/documentdb/lib/documentclient.js`\\n\\n### Answer\\nThe `RequestHandler` in `src/node_modules/documentdb/lib/request.js` handles the low-level details of making HTTP requests. The `DocumentClient` in `src/node_modules/documentdb/lib/documentclient.js` uses `RequestHandler` to make requests to the DocumentDB service. \\n\\n## Question 9: How does `GlobalEndpointManager` update endpoint cache? \\n\\n### References\\n- `src/node_modules/documentdb/lib/globalEndpointManager.js`\\n\\n### Answer\\nThe `GlobalEndpointManager` in `src/node_modules/documentdb/lib/globalEndpointManager.js` updates the endpoint cache by retrieving the writable and readable locations from the database account and then selecting the appropriate endpoints based on preferred locations and endpoint discovery settings. \\n\\n## Question 10: Where is the logic for extracting partition keys? \\n\", 'usage_metadata': {'candidates_token_count': 1044, 'prompt_token_count': 47922, 'total_token_count': 48966}}\n"]}], "source": ["ANSWERS.keys()\n", "\n", "# print(ANSWERS[\"9100bac5-ce3f-4ebb-9a8e-9bebb929ecee\"]['text'])\n", "# print(ANSWERS[\"6b17beb4-56a6-471c-b841-260660a2cfcc\"]['text'])\n", "# print(ANSWERS[\"76faf84d-3f82-4813-bb23-bdaa8f24d15e\"]['text'])\n", "# print(ANSWERS[\"0b9cd3f7-c757-448e-96ee-c8dc3d5cd1e8\"]['text'])\n", "# print(ANSWERS[\"383756ed-4838-4484-86a0-7585dc80aa8f\"]['text'])\n", "\n", "import random\n", "\n", "repo_uuid = random.choice(list(ANSWERS.keys()))\n", "# print(repo_uuid)\n", "# print(ANSWERS[repo_uuid]['text'])\n", "print(ANSWERS[repo_uuid])\n", "\n", "# EXAMPLES\n", "\n", "# https://gist.github.com/urikz/9f15dd06cc7af7a9688fefc648be5578\n", "# https://gist.github.com/urikz/925a3463845793f39161bd3a888536a2\n", "# https://gist.github.com/urikz/932a4b5ee2463604e94f3d1bcf877b5e\n", "# https://gist.github.com/urikz/9c7b9918e2a40f826193526f31fcf1b0\n", "# https://gist.github.com/urikz/23e53ca1520f16ed679a4e4964e37fa3"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 13344 samples\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import tqdm\n", "import re\n", "import dataclasses\n", "\n", "\n", "def next_block_index(text, start_index, headers):\n", "    for header in headers:\n", "        index = text.find(header, start_index)\n", "        if index != -1:\n", "            return index\n", "    return None\n", "\n", "def next_line(text, start_index):\n", "    next_line_index = text.find(\"\\n\", start_index)\n", "    if next_line_index != -1:\n", "        return next_line_index + 1\n", "    return None\n", "\n", "def next_question_index(text, start_index):\n", "    question_headers = [\"# Question \", \"## Question \"]\n", "    return next_block_index(text, start_index, question_headers)\n", "\n", "def next_reference_index(text, start_index):\n", "    reference_headers = [\"## References\", \"# References\"]\n", "    return next_block_index(text, start_index, reference_headers)\n", "\n", "def next_answer_index(text, start_index):\n", "    answer_headers = [\"## Answer\", \"# Answer\"]\n", "    return next_block_index(text, start_index, answer_headers)\n", "\n", "def next_line_break(text, start_index):\n", "    header = [\"---\"]\n", "    return next_block_index(text, start_index, header)\n", "\n", "def parse_references(text):\n", "    pattern = r\"`([^`]+)`\"\n", "    return list(re.findall(pattern, text))\n", "\n", "def verify_references(text):\n", "    # TO BE IMPLEMENTED!\n", "    pass\n", "\n", "\n", "@dataclasses.dataclass\n", "class QASample:\n", "    question: str\n", "    paths: list[str]\n", "    answer: str\n", "\n", "n_hit_max_output_tokens_limit = 0\n", "\n", "def parse_gemini_answers(response):\n", "    global n_hit_max_output_tokens_limit\n", "    text = response['text']\n", "    start_index = 0\n", "\n", "    samples = []\n", "\n", "    while True:\n", "        question_start = next_question_index(text, start_index)\n", "        if question_start is None:\n", "            break\n", "\n", "        question_start = next_line(text, question_start)\n", "        if question_start is None:\n", "            break\n", "        question_end = next_reference_index(text, question_start)\n", "        if question_end is None:\n", "            break        \n", "\n", "        question = text[question_start:question_end].strip()\n", "\n", "        references_start = next_line(text, question_end)\n", "        if references_start is None:\n", "            break\n", "        references_end = next_answer_index(text, references_start)\n", "        if references_end is None:\n", "            break\n", "\n", "        reference_text = text[references_start:references_end].strip()\n", "\n", "        references = parse_references(reference_text)\n", "        # if len(references) == 0:\n", "            # assert reference_text.startswith(\"I am unable to answer this question\"), reference_text\n", "        # assert len(references) > 0, reference_text\n", "\n", "        answer_start = next_line(text, references_end)\n", "        answer_end = next_question_index(text, answer_start) or len(text)\n", "        answer = text[answer_start:answer_end].strip()\n", "        if len(references) > 0:\n", "            samples.append(QASample(\n", "                question=question,\n", "                paths=references,\n", "                answer=answer,\n", "            ))\n", "\n", "        if start_index >= answer_end:\n", "            print(text)\n", "        assert start_index < answer_end\n", "\n", "        start_index = answer_end\n", "\n", "    if response['usage_metadata']['candidates_token_count'] == 4096:\n", "        samples = samples[:-1]\n", "        n_hit_max_output_tokens_limit += 1\n", "\n", "    return samples\n", "\n", "\n", "\n", "samples = []\n", "# xxx = {k: v for k, v in ANSWERS.items() if k == \"fbce6744-3288-4d33-a606-************\"}\n", "for repo_uuid, answer in ANSWERS.items():\n", "    if answer is not None and answer['text'] is not None: \n", "        samples.extend(parse_gemini_answers(answer))\n", "\n", "print(\"Loaded %d samples\" % len(samples))\n", "n_hit_max_output_tokens_limit"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["871 52\n", "11655 1689\n"]}], "source": ["FILEDS_TO_COVERT_TO_LISTS = [\n", "   'max_stars_repo_licenses',\n", "   'max_issues_repo_licenses',\n", "   'max_forks_repo_licenses',\n", "]\n", "\n", "n_saved_repos, n_saved_samples, n_filted_repos, n_filtered_samples = 0, 0, 0, 0\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl\", \"w\") as f:\n", "    for repo_uuid, answer in ANSWERS.items():\n", "        if answer['text'] is not None: \n", "            repo = REPOS[repo_uuid]\n", "            samples = parse_gemini_answers(answer['text'])\n", "            all_paths = {r['max_stars_repo_path'] for r in repo['file_list']}\n", "            filtered_samples = []\n", "            for sample in samples:\n", "                filtered_paths = [path for path in sample.paths if path in all_paths]\n", "                if len(filtered_paths) == 0:\n", "                    n_filtered_samples += 1\n", "                    continue\n", "                filtered_sample = dataclasses.replace(sample, paths=filtered_paths)\n", "                filtered_samples.append(filtered_sample)\n", "                n_saved_samples += 1\n", "                    \n", "            if len(filtered_samples) == 0:\n", "                n_filted_repos += 1\n", "                continue\n", "\n", "            n_saved_repos += 1\n", "\n", "            repo_d = repo.to_dict()\n", "            repo_d['file_list'] = repo_d['file_list'].tolist()\n", "            for file in repo_d['file_list']:\n", "                for k in FILEDS_TO_COVERT_TO_LISTS:\n", "                    if not isinstance(file[k], list):\n", "                        file[k] = file[k].tolist()\n", "\n", "            repo_d['documents_with_questions'] = [dataclasses.asdict(sample) for sample in filtered_samples]\n", "\n", "            sample_json = json.dumps(repo_d)\n", "            f.write(sample_json + \"\\n\")\n", "\n", "print(n_saved_repos, n_filted_repos)\n", "print(n_saved_samples, n_filtered_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "import tqdm\n", "import dataclasses\n", "from types import SimpleNamespace"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v2-haiku-v2\"\n", "BASE_PATH = Path(\"/mnt/efs/augment/user/yury/binks/\")\n", "OUTPUT_PATH = BASE_PATH / BINKS_VERSION\n", "OUTPUT_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "OLD_QA_DATA = [    \n", "    BASE_PATH / \"binks-v1.3-merged/repos_with_qa.jsonl\",\n", "    BASE_PATH / \"binks-v2-gemini/repos_with_qa.jsonl\",\n", "    # BASE_PATH / \"binks-v2-haiku/repos_with_qa.jsonl\",\n", "]\n", "    \n", "config = SimpleNamespace(\n", "    **{\n", "        \"repo_languages\": REPO_LANGUAGES,\n", "        \"sample_languages\": SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": RETRIEVAL_LANGUAGES,\n", "        \"limit_repos_from_the_stack\": 500,\n", "        \"limit_repos\": 5000,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"min_lines_per_file\": 200,\n", "        \"max_lines_per_file\": 1000,\n", "        \"file_max_size\": 10000,\n", "        \"n_questions_per_file\": 5,\n", "        \"random_seed\": 31415,    \n", "        \"n_retrievals\": 25,\n", "        \"p_local_context_dropout\": 0.05,\n", "        \"p_empty_selection\": 0.45,\n", "        \"max_lines_per_selected_code\": 50,\n", "        \"max_seq_length\": 16384 + 1,\n", "        \"num_validation_samples\": 200,\n", "    }\n", ")\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5871\n"]}], "source": ["already_used_repos = set()\n", "\n", "for path in OLD_QA_DATA:\n", "    with open(path) as f:\n", "        for line in f:\n", "            repo = json.loads(line[:-1])\n", "            already_used_repos.add(repo[\"max_stars_repo_name\"])\n", "\n", "print(len(already_used_repos))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["500it [00:01, 305.08it/s]\n", "7597it [00:16, 462.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["5000 3098\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import glob\n", "import os\n", "import pandas as pd\n", "\n", "QA_DATA_V1 = [\n", "    BASE_PATH / \"binks-v1/01_raw_repos/part-00000-9990b718-de5b-4aed-97d4-968fd1302624-c000.zstd.parquet\",\n", "    BASE_PATH / \"binks-v1.1/01_raw_repos/part-0000*-3ccecdd8-89f4-4520-b804-988dbe95a44d-c000.zstd.parquet\",\n", "    BASE_PATH / \"binks-v1.2/01_raw_repos/part-0000*-a63b384a-3dd5-45bd-aeb5-0dd345dbcad0-c000.zstd.parquet\",\n", "]\n", "\n", "\n", "class ParquetRepoDataset:\n", "    def __init__(self, pattern):\n", "        self.paths = glob.glob(str(pattern))\n", "        assert len(self.paths) > 0, f\"No files found for pattern {pattern}\"\n", "    \n", "    def __iter__(self):\n", "        for path in self.paths:\n", "            for _, repo in pd.read_parquet(path, engine='pyarrow').iterrows():\n", "                yield repo\n", "\n", "REPOS = {}\n", "n_already_used = 0\n", "\n", "for repo_pattern in QA_DATA_V1:\n", "    for repo in tqdm.tqdm(ParquetRepoDataset(repo_pattern)):\n", "        if repo[\"max_stars_repo_name\"] in already_used_repos:\n", "            n_already_used += 1\n", "            continue\n", "        REPOS[repo[\"repo_uuid\"]] = repo\n", "        if len(REPOS) >= config.limit_repos:\n", "            break\n", "    if len(REPOS) >= config.limit_repos:\n", "        break\n", "\n", "print(len(REPOS), n_already_used)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_repo_prompt(repo):\n", "    full_repo = [\n", "        f\"Below are the files from the {repo[REPO_COLUMN]} project. Read them carefully.\"\n", "    ]\n", "\n", "    for file in repo[FILE_LIST_COLUMN]:\n", "        if file['langpart'] in RETRIEVAL_LANGUAGES:\n", "            full_repo.extend([\n", "                \"This is file `%s`\" % file['max_stars_repo_path'],\n", "                \"```\",\n", "                file[CONTENT_COLUMN],\n", "                \"```\",\n", "            ])\n", "\n", "    return \"\\n\\n\".join(full_repo)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 500/500 [18:46<00:00,  2.25s/it]  \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_characters</th>\n", "      <th>n_tokens</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>500.00</td>\n", "      <td>500.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1215893.65</td>\n", "      <td>454487.99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3866023.52</td>\n", "      <td>1500674.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>201387.00</td>\n", "      <td>54654.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>271290.00</td>\n", "      <td>92580.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>413651.00</td>\n", "      <td>139948.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>813551.50</td>\n", "      <td>271038.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>59282107.00</td>\n", "      <td>24383400.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       n_characters    n_tokens\n", "count        500.00      500.00\n", "mean     1215893.65   454487.99\n", "std      3866023.52  1500674.72\n", "min       201387.00    54654.00\n", "25%       271290.00    92580.50\n", "50%       413651.00   139948.50\n", "75%       813551.50   271038.00\n", "max     59282107.00 24383400.00"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["n_characters, n_tokens = [], []\n", "for repo in tqdm.tqdm(REPOS.values()):    \n", "    repo_prompt = get_repo_prompt(repo)\n", "    n_characters.append(len(repo_prompt))\n", "    n_tokens.append(len(tokenizer.tokenize_safe(repo_prompt)))\n", "\n", "df = pd.DataFrame({\n", "    \"n_characters\": n_characters,\n", "    \"n_tokens\": n_tokens,\n", "})\n", "pd.options.display.float_format = '{:.2f}'.format\n", "df.describe()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# !pip install anthropic\n", "import anthropic\n", "\n", "client = anthropic.Anthropic(\n", "    api_key=\"************************************************************************************************************\",\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["QUESTIONS_PROMPT = \"\"\"\n", "---\n", "\n", "Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help locating specific functionalities within the repository. They have AI assistant they are going to ask for help. However, the software engineer is impatient and writes very concise questions, almost like Google search queries. Indeed, this the software engineer needs to locate information quickly and they don't have time to write elaborate or double questions. What kind of questions would this software engineer ask? Try to come up with these questions.\n", "\n", "**Your task is to generate 10 questions about a software project.**\n", "\n", "## Guidelines for Question Generation\n", "\n", "- Focus on One Aspect: Each question should target only one feature, process, or setup aspect.\n", "- Encourage Search-Like Questions: Questions should help locate specific functionalities or data handling within the codebase, similar to performing a search task.\n", "- AVOID General Questions: Never ask \"What is the purpose\" or \"What is the role.\"\n", "- AVOID Complex Questions: Do not include compound or double-barreled questions.\n", "- Cover Different Areas: Question SHOULD NOT BE about the same files.\n", "\n", "## Output Format\n", "\n", "Write each question in a numbered list, one per line, without additional details.\n", "\n", "## Example Questions Templates\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "\n", "- How do we configure X?\n", "- What data structure is used to store information about X?\n", "- Where is the logic for X defined?\n", "- How does X handle Y?\n", "- Which files define X?\n", "- Where can I find implementation of X?\n", "- Which X is used for data storage?\n", "- How X are handled?\n", "- What is the default value of X?\n", "- What is the value Y in the configuration?\n", "- Where do we keep documentation for X component?\n", "\n", "You MUST abide by the instructions without exception.\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["1232"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["QUESTIONS = {}\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/haiku_quetions_cache.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        QUESTIONS[data[\"key\"]] = data[\"value\"]\n", "\n", "len(QUESTIONS)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 1615/5000 [18:36:17<38:59:42, 41.47s/it, n_questions=1231, n_skipped=673]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 53\u001b[0m\n\u001b[1;32m     50\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to generate for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrepo_uuid\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m due to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     51\u001b[0m     \u001b[38;5;66;03m# if len(QUESTIONS) >= 2:\u001b[39;00m\n\u001b[1;32m     52\u001b[0m     \u001b[38;5;66;03m#     break\u001b[39;00m\n\u001b[0;32m---> 53\u001b[0m     time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m1\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m60\u001b[39m)\n\u001b[1;32m     55\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGenerated \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m sets ofquestions\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m \u001b[38;5;28mlen\u001b[39m(QUESTIONS))\n\u001b[1;32m     57\u001b[0m \u001b[38;5;66;03m# 100%|██████████| 1000/1000 [6:45:49<00:00, 24.35s/it]\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;66;03m# Generated 969 sets ofquestions\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import tqdm\n", "import time\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/haiku_quetions_cache.jsonl\")\n", "def generate_questions_with_haiku(repo_uuid):\n", "    global GOOGLE_MODEL\n", "    global REPOS\n", "    global QUESTIONS_PROMPT\n", "    repo = REPOS[repo_uuid]\n", "\n", "    repo_prompt = get_repo_prompt(repo)\n", "\n", "    message = client.messages.create(\n", "        model=\"claude-3-haiku-20240307\",\n", "        system=repo_prompt,\n", "        max_tokens=1024,\n", "        temperature=0.7,\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": QUESTIONS_PROMPT},\n", "        ]\n", "    )\n", "    assert len(message.content) == 1\n", "    text = message.content[0].text\n", "\n", "    usage_metadata = {\n", "        \"candidates_token_count\": message.usage.output_tokens,\n", "        \"prompt_token_count\": message.usage.input_tokens,\n", "    }\n", "    return {\n", "        \"text\": text,\n", "        \"usage_metadata\": usage_metadata,\n", "    }\n", "\n", "n_skipped = 0\n", "pbar = tqdm.tqdm(REPOS)\n", "\n", "for repo_uuid in pbar:\n", "    pbar.set_postfix({\"n_questions\": len(QUESTIONS), \"n_skipped\": n_skipped})\n", "    repo = REPOS[repo_uuid]\n", "    repo_prompt = get_repo_prompt(repo)\n", "    n_tokens = len(tokenizer.tokenize_safe(repo_prompt))\n", "    if n_tokens > 180000:\n", "        n_skipped += 1\n", "        continue\n", "    try:\n", "        QUESTIONS[repo_uuid] = generate_questions_with_haiku(repo_uuid)\n", "    except Exception as e:\n", "        print(f\"Failed to generate for {repo_uuid} due to {e}\")\n", "    # if len(QUESTIONS) >= 2:\n", "    #     break\n", "    time.sleep(1 * 60)\n", "\n", "print(\"Generated %d sets ofquestions\" % len(QUESTIONS))\n", "\n", "# 100%|██████████| 1000/1000 [6:45:49<00:00, 24.35s/it]\n", "# Generated 969 sets ofquestions"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["ANSWERS_PROMPT = \"\"\"\n", "---\n", "\n", "Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help locating specific functionalities within the repository. You are an AI assistant they are going to ask for help -- answer their questions.\n", "\n", "**Your task is to answer <PERSON><PERSON> these questions about the software project above:**\n", "\n", "%s\n", "\n", "# Objective\n", "- List References: For EVERY question, provide a complete list of ALL files with necessary details for the question.\n", "- Provide Answers: Provide a comprehensive answer for each question and include a relevant code excerpt from the project or a code example to demonstrate the API usage.\n", "\n", "# Guidelines for Reference\n", "- List all relevant files using full paths, formatted with single ticks (`).\n", "- Avoid using wildcard patterns ('*') or simply listing directories; be sure to specify individual files only.\n", "\n", "# Guidelines for Answer Generation\n", "- Incorporate file paths fluently into the answer, ensuring all paths are shown with full detail and formatted correctly.\n", "- Always include a code snippet in each answer to illustrate the practical implementation or functionality being discussed. This can either be a direct quote from one of the references or a short example demonstrating API usage.\n", "\n", "# Output Format:\n", "For EVERY question use Markdown headers to structure each response as follows.\n", "\n", "======================= SAMPLE OUTPUT IS BELOW =======================\n", "\n", "# Question 1\n", "How is user authentication handled in the project?\n", "\n", "## References\n", "- `ProjectRoot/Services/AuthService.cs`\n", "- `ProjectRoot/Config/UserConfig.cs`\n", "\n", "## Answer\n", "User authentication in the project is managed through a dedicated service defined in `ProjectRoot/Services/AuthService.cs`, with configuration settings outlined in `ProjectRoot/Config/UserConfig.cs`. Here are examples from both files demonstrating the authentication process:\n", "\n", "Snippet from `ProjectRoot/Services/AuthService.cs`:\n", "\n", "```\n", "public bool AuthenticateUser(string username, string password)\n", "{\n", "    // Authentication logic here\n", "    return CheckCredentials(username, password);\n", "}\n", "```\n", "\n", "Snippet from `ProjectRoot/Config/UserConfig.cs`:\n", "\n", "```\n", "public Dictionary<string, string> GetUserSettings()\n", "{\n", "    // Load user settings from configuration\n", "    return new Dictionary<string, string> { {\"maxAttempts\", \"5\"}, {\"timeout\", \"00:30:00\"} };\n", "}\n", "```\n", "\n", "These methods work together to ensure that user credentials are checked against security settings, managing authentication attempts and session timeouts as configured in `ProjectRoot/Config/UserConfig.cs`, ensuring a secure user authentication process.\n", "\n", "---\n", "\n", "... Questions from 2 to 9 ...\n", "\n", "---\n", "\n", "# Question 10\n", "How is network communication structured and secured within the project?\n", "\n", "## References\n", "- `ProjectRoot/Network/NetworkConfig.cs`\n", "- `ProjectRoot/Network/NetworkService.cs`\n", "\n", "## Answer\n", "Network communication within the project is orchestrated and secured through settings specified in `ProjectRoot/Network/NetworkConfig.cs`, with operational management by `ProjectRoot/Network/NetworkService.cs. Here's an example of how network settings are initialized and applied:\n", "\n", "\n", "```\n", "public void ConfigureNetwork() {\n", "    NetworkConfiguration netConfig = new NetworkConfiguration();\n", "    // Setup network parameters\n", "    netConfig.UseEncryption = true;\n", "    netConfig.EncryptionType = \"AES\";\n", "    netConfig.RequireAuthentication = true;\n", "\n", "    // Apply configuration\n", "    NetworkService.ApplyConfiguration(netConfig);\n", "}\n", "```\n", "\n", "This configuration snippet illustrates the foundational network setup, emphasizing security features like encryption and required authentication, showcasing the thoroughness of the project’s network management strategy.\n", "\n", "======================= END OF SAMPLE OUTPUT =======================\n", "\n", "ANSWER ALL QUESTIONS!! You MUST provide answer for ALL 10 questions! I will get FIRED and will NOT be able to afford rent if you don't answer ALL 10 questions!\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1232 [00:00<?, ?it/s, total_questions=732]"]}, {"name": "stderr", "output_type": "stream", "text": [" 79%|███████▉  | 976/1232 [00:29<00:12, 20.67it/s, total_questions=6113]  "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to generate for f1cc39a5-b8d6-4ab4-8ba1-85f806ac8d99 due to Error code: 400 - {'type': 'error', 'error': {'type': 'invalid_request_error', 'message': 'Output blocked by content filtering policy'}}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1232/1232 [3:11:16<00:00,  9.32s/it, total_questions=8356]  \n"]}], "source": ["import tqdm\n", "from experimental.yury.data.processing import persistent_cache\n", "\n", "def extract_questions(text):\n", "    index1 = text.find(\"1.\")\n", "    if index1 == -1:\n", "        return text\n", "    else:\n", "        return text[index1:]\n", "\n", "def count_questions(text):\n", "    for index in reversed(range(1, 21)):\n", "        s = \"Question %s\\n\" % index\n", "        if s in text:\n", "            return index\n", "    return 0\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/yury/binks/binks-v2-haiku-v2/haiku_answers_cache.jsonl\")\n", "def generate_answers_with_haiku(repo_uuid):\n", "    global REPOS\n", "    global ANSWERS_PROMPT\n", "    global QUESTIONS\n", "    repo = REPOS[repo_uuid]\n", "    questions = extract_questions(QUESTIONS[repo_uuid]['text'])\n", "\n", "    answer_prompt = ANSWERS_PROMPT % questions\n", "    repo_prompt = get_repo_prompt(repo)\n", "\n", "    message = client.messages.create(\n", "        model=\"claude-3-haiku-20240307\",\n", "        system=repo_prompt,\n", "        max_tokens=4096,\n", "        temperature=0.7,\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": answer_prompt},\n", "        ]\n", "    )\n", "    assert len(message.content) == 1\n", "    text = message.content[0].text\n", "\n", "    usage_metadata = {\n", "        \"candidates_token_count\": message.usage.output_tokens,\n", "        \"prompt_token_count\": message.usage.input_tokens,\n", "    }\n", "    return {\n", "        \"text\": text,\n", "        \"usage_metadata\": usage_metadata,\n", "    }\n", "\n", "\n", "ANSWERS = {}\n", "\n", "n_total_questions = 0\n", "pbar = tqdm.tqdm(QUESTIONS) \n", "\n", "for repo_uuid in pbar:\n", "    if repo_uuid not in REPOS:\n", "        continue\n", "    if repo_uuid in ANSWERS:\n", "        continue\n", "    try:\n", "        ANSWERS[repo_uuid] = generate_answers_with_haiku(repo_uuid)\n", "        if ANSWERS[repo_uuid]['text'] is not None:\n", "            n_total_questions += count_questions(ANSWERS[repo_uuid]['text'])\n", "    except Exception as e:\n", "        print(f\"Failed to generate for {repo_uuid} due to {e}\")\n", "        ANSWERS[repo_uuid] = None\n", "    pbar.set_postfix({\"total_questions\": n_total_questions})    \n", "\n", "# 100%|██████████| 969/969 [17:47:38<00:00, 66.11s/it, total_questions=12768] "]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded samples for 310 repos\n"]}], "source": ["ANSWERS = {}\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/haiku_answers_cache.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        ANSWERS[data[\"key\"]] = data[\"value\"]\n", "\n", "print(\"Loaded samples for %d repos\" % len(ANSWERS))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'text': '# Question 1\\nHow do we define the layout and styling for the web page components?\\n\\n## References\\n- `web site/site inicialv0.01/site/img/assets/node_modules/varstream/README.md`\\n- `JavaScript/botstrape+js/script.js`\\n\\n## Answer\\nThe layout and styling for the web page components are defined primarily in the `JavaScript/botstrape+js/script.js` file, which utilizes the Bootstrap CSS framework.\\n\\nIn the `JavaScript/botstrape+js/script.js` file, the `formulario` class is responsible for generating the HTML structure of the various form elements, such as the Circulo, Triangulo, Quadrilátero, and Polígono regular. The HTML for these forms is constructed using Bootstrap classes and elements, as shown in this example:\\n\\n```javascript\\nthis.MontaCirculo = function(){\\n    var linha1 = \"<div class=\\'form-row\\'>\" +\\n        \"<div class=\\'form-group col-md-4\\'>\" +\\n        \"<label for=\\'txtDiametro\\'>Diametro :</label>\" +\\n        \"<input class=\\'form-control\\' id=\\'txtDiametro\\' type=\\'text\\' maxlength=\\'40\\' required=\\'required\\'>\" +\\n        \"</div></div>\";\\n    // ... additional HTML structure for the Circulo form ...\\n}\\n```\\n\\nThe use of Bootstrap classes, such as `form-row`, `form-group`, and `form-control`, ensures that the web page components are styled consistently and responsively across different devices and screen sizes.\\n\\nAdditionally, the `README.md` file for the `varstream` library provides some high-level information about the project, but it does not contain any details about the layout and styling of the web page components.\\n\\n# Question 2\\nWhat data structures are used to represent the game state in the Tic-Tac-Toe game?\\n\\n## References\\n- `JavaScript/Jogo da venha em Js/script.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha1.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha2.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha3.js`\\n\\n## Answer\\nThe game state of the Tic-Tac-Toe game is represented using a combination of HTML elements and JavaScript variables. Specifically:\\n\\n1. The game board is represented using a set of `<div>` elements with IDs `c1_1`, `c1_2`, `c1_3`, `c2_1`, `c2_2`, `c2_3`, `c3_1`, `c3_2`, and `c3_3`. These elements store the current state of the game board, with the `alt` attribute representing the player\\'s mark (`\"o\"` or `\"x\"`).\\n\\n2. The current player\\'s turn is tracked using the `vez` variable, which stores the string `\"Sua vez O\"` or `\"Sua vez X\"`.\\n\\n3. The `vezimg` element is used to display the current player\\'s mark (`\"o\"` or `\"x\"`).\\n\\n4. The game logic is implemented in the `script.js`, `robotvelha1.js`, `robotvelha2.js`, and `robotvelha3.js` files, which contain functions to handle user input, update the game board, and determine the winner.\\n\\nFor example, here\\'s an excerpt from `script.js` that demonstrates how the game board is updated:\\n\\n```javascript\\nfunction defini_celula(x){\\n    posicao=document.getElementById(x);\\n    if(verifica_ai()){\\n        switch(posicao.alt){\\n            case \"0\":\\n                if(vezimg.alt==\"o\"){\\n                    posicao.src=\"./img/o-icon.png\"\\n                    posicao.alt=\"o\"\\n                    vezimg.alt=\"x\"\\n                    vezimg.src=\"./img/x-icon.png\"\\n                    vez.value=\"Sua vez X\"\\n                }else{\\n                    posicao.alt=\"x\"\\n                    posicao.src=\"./img/x-icon.png\"\\n                    vezimg.alt=\"o\"\\n                    vezimg.src=\"./img/o-icon.png\"\\n                    vez.value=\"Sua vez O\"\\n                }\\n            default:\\n                // ...\\n        }\\n    }\\n    // ...\\n}\\n```\\n\\nThis function updates the game board based on the current player\\'s move, modifying the `alt` and `src` attributes of the corresponding HTML elements.\\n\\n# Question 3\\nWhere is the logic for determining the winner of a Tic-Tac-Toe game implemented?\\n\\n## References\\n- `JavaScript/Jogo da venha em Js/script.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha1.js`\\n\\n## Answer\\nThe logic for determining the winner of a Tic-Tac-Toe game is implemented in the `verifica_venha()` function, which is defined in the `JavaScript/Jogo da venha em Js/script.js` file.\\n\\nThe `verifica_venha()` function iterates through the game board, represented by an array of HTML elements, and checks for winning conditions. It does this by comparing the `alt` attribute of the game board elements to determine if a player has achieved a winning combination (three consecutive marks of the same type).\\n\\nHere\\'s an excerpt from the `verifica_venha()` function:\\n\\n```javascript\\nfunction verifica_venha(){\\n    for (i=0;i<9;i++){\\n        var arrayVelha = [   document.getElementById(\"c1_1\"),document.getElementById(\"c1_2\"),\\n                            document.getElementById(\"c1_3\"),document.getElementById(\"c2_1\"),\\n                            document.getElementById(\"c2_2\"),document.getElementById(\"c2_3\"),\\n                            document.getElementById(\"c3_1\"),document.getElementById(\"c3_2\"),\\n                            document.getElementById(\"c3_3\")   ]\\n        switch(arrayVelha[i].id){\\n            case\"c1_1\":\\n                verifica(arrayVelha[i].alt,arrayVelha[i+1].alt,arrayVelha[i+2].alt)//verifica linha\\n                verifica(arrayVelha[i].alt,arrayVelha[i+3].alt,arrayVelha[i+6].alt)//verificador coluna\\n                verifica(arrayVelha[i].alt,arrayVelha[i+4].alt,arrayVelha[i+8].alt)//verificador transversal\\n                break;\\n            // ... additional cases for other board positions ...\\n        }\\n    }\\n}\\n```\\n\\nThe `verifica()` function, also defined in `JavaScript/Jogo da venha em Js/script.js`, is responsible for checking if the three marks in a row, column, or diagonal match and determining the winner.\\n\\n# Question 4\\nHow does the AI player decide its next move in the Tic-Tac-Toe game?\\n\\n## References\\n- `JavaScript/Jogo da venha em Js/robotvelha1.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha2.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha3.js`\\n\\n## Answer\\nThe AI player\\'s decision-making process for its next move in the Tic-Tac-Toe game is implemented across the `robotvelha1.js`, `robotvelha2.js`, and `robotvelha3.js` files.\\n\\nThe AI player\\'s strategy is based on a series of checks and priorities, which differ depending on the chosen difficulty level (easy, intermediate, or difficult).\\n\\nIn the `facil()` function (easy difficulty) defined in `robotvelha3.js`, the AI player simply places its mark randomly on an available cell:\\n\\n```javascript\\nfunction facil(){\\n    var arrayVelha = [   document.getElementById(\"c1_1\"),document.getElementById(\"c1_2\"),\\n                        document.getElementById(\"c1_3\"),document.getElementById(\"c2_1\"),\\n                        document.getElementById(\"c2_2\"),document.getElementById(\"c2_3\"),\\n                        document.getElementById(\"c3_1\"),document.getElementById(\"c3_2\"),\\n                        document.getElementById(\"c3_3\")   ]\\n    colocaxAleatoriamente()\\n}\\n```\\n\\nThe `colocaxAleatoriamente()` function, defined in `robotvelha1.js`, is responsible for randomly selecting an available cell and placing the AI player\\'s mark.\\n\\nFor the `intermediario()` and `dificil()` functions (intermediate and difficult difficulties), the AI player\\'s decision-making is more sophisticated, involving checks for potential winning moves, blocking the opponent\\'s winning moves, and strategically placing marks to create winning opportunities. These functions are defined in `robotvelha2.js` and `robotvelha1.js`, respectively.\\n\\nThe AI player\\'s decision-making logic is encapsulated in functions like `dizCelulaParaVenha()`, `dizCelulaParaVelhaInimiga()`, and `colocaAdicionaXaoX()`, which analyze the current state of the game board and determine the best next move for the AI player.\\n\\n# Question 5\\nWhich files contain the implementation of the Circulo, Triangulo, Quadrilatero, and Poligono classes?\\n\\n## References\\n- `JavaScript/botstrape+js/script.js`\\n\\n## Answer\\nThe implementation of the Circulo, Triangulo, Quadrilatero, and Poligono classes is contained within the `JavaScript/botstrape+js/script.js` file.\\n\\nThe relevant classes are defined as follows:\\n\\n1. **Circulo**:\\n   - Defined in the `JavaScript/botstrape+js/script.js` file\\n   - Represents a circle and provides methods to calculate its area and perimeter\\n\\n2. **Triangulo**:\\n   - Defined in the `JavaScript/botstrape+js/script.js` file\\n   - Represents a triangle and provides methods to calculate its area, perimeter, and determine the type of triangle (equilateral, isosceles, or scalene)\\n\\n3. **Quadrilatero**:\\n   - Defined in the `JavaScript/botstrape+js/script.js` file\\n   - Represents a quadrilateral and provides methods to calculate its area and perimeter, as well as determine the type of quadrilateral (square or rectangle)\\n\\n4. **Poligono**:\\n   - Defined in the `JavaScript/botstrape+js/script.js` file\\n   - Represents a regular polygon and provides methods to calculate its area, perimeter, and apothem\\n\\nEach of these classes encapsulates the necessary properties and methods to handle the corresponding geometric shape and its calculations.\\n\\n# Question 6\\nWhere can I find the code that handles the user input and updates the game board?\\n\\n## References\\n- `JavaScript/Jogo da venha em Js/script.js`\\n\\n## Answer\\nThe code that handles the user input and updates the game board is located in the `JavaScript/Jogo da venha em Js/script.js` file.\\n\\nThe main function responsible for this is `defini_celula(x)`, which is called when the user clicks on a cell in the game board. This function updates the game board based on the current player\\'s move, modifying the `alt` and `src` attributes of the corresponding HTML elements.\\n\\nHere\\'s an excerpt from the `defini_celula(x)` function:\\n\\n```javascript\\nfunction defini_celula(x){\\n    posicao=document.getElementById(x);\\n    if(verifica_ai()){\\n        switch(posicao.alt){\\n            case \"0\":\\n                if(vezimg.alt==\"o\"){\\n                    posicao.src=\"./img/o-icon.png\"\\n                    posicao.alt=\"o\"\\n                    vezimg.alt=\"x\"\\n                    vezimg.src=\"./img/x-icon.png\"\\n                    vez.value=\"Sua vez X\"\\n                }else{\\n                    posicao.alt=\"x\"\\n                    posicao.src=\"./img/x-icon.png\"\\n                    vezimg.alt=\"o\"\\n                    vezimg.src=\"./img/o-icon.png\"\\n                    vez.value=\"Sua vez O\"\\n                }\\n            default:\\n                // ...\\n        }\\n    }\\n    // ...\\n}\\n```\\n\\nThis function checks the current state of the game board, determines the appropriate action based on the player\\'s move, and updates the board accordingly.\\n\\nAdditionally, the `verifica_venha()` function, also defined in `JavaScript/Jogo da venha em Js/script.js`, is responsible for checking the game board for winning conditions and determining the winner.\\n\\n# Question 7\\nWhich configuration settings control the difficulty levels of the AI player in the Tic-Tac-Toe game?\\n\\n## References\\n- `JavaScript/Jogo da venha em Js/robotvelha1.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha2.js`\\n- `JavaScript/Jogo da venha em Js/robotvelha3.js`\\n\\n## Answer\\nThe difficulty levels of the AI player in the Tic-Tac-Toe game are controlled by the implementation of the `facil()`, `intermediario()`, and `dificil()` functions, which are defined in the `JavaScript/Jogo da venha em Js/robotvelha3.js`, `JavaScript/Jogo da venha em Js/robotvelha2.js`, and `JavaScript/Jogo da venha em Js/robotvelha1.js` files, respectively.\\n\\nEach of these functions represents a different difficulty level for the AI player:\\n\\n1. `facil()` (easy difficulty):\\n   - Defined in `JavaScript/Jogo da venha em Js/robotvelha3.js`\\n   - The AI player simply places its mark randomly on an available cell.\\n\\n2. `intermediario()` (intermediate difficulty):\\n   - Defined in `JavaScript/Jogo da venha em Js/robotvelha2.js`\\n   - The AI player analyzes the game board and checks for potential winning moves, blocking the opponent\\'s winning moves, and strategically placing marks to create winning opportunities.\\n\\n3. `dificil()` (difficult difficulty):\\n   - Defined in `JavaScript/Jogo da venha em Js/robotvelha1.js`\\n   - The AI player\\'s decision-making is more sophisticated, involving a deeper analysis of the game board and a more complex strategy for placing marks.\\n\\nThe selection of the appropriate difficulty level is typically handled in the main game logic, where the user can choose the desired difficulty before starting the game.\\n\\n# Question 8\\nHow are the game results (area, perimeter, etc.) persisted and displayed in the web application?\\n\\n## References\\n- `JavaScript/botstrape+js/script.js`\\n\\n## Answer\\nThe game results (area, perimeter, etc.) are persisted and displayed in the web application using the following approach:\\n\\n1. Persistence:\\n   - The game results are stored in the `conteudoTabela` array, which is defined as a global variable in the `JavaScript/botstrape+js/script.js` file.\\n   - Whenever a new calculation is performed, the corresponding result is added to the `conteudoTabela` array using the `Main.Grava()` function.\\n   - The `conteudoTabela` array is then saved to the browser\\'s local storage using the `localStorage.setItem()` method.\\n\\n2. Display:\\n   - The `Main.criaTabela()` function is responsible for creating and populating the HTML table that displays the game results.\\n   - This function iterates through the `conteudoTabela` array and generates the table rows, with each row containing the figure, type, area, perimeter, and date/time information.\\n   - The generated table is then inserted into the `espaçotabela` element on the web page.\\n\\nHere\\'s an example of how the `Main.Grava()` and `Main.criaTabela()` functions are implemented:\\n\\n```javascript\\nthis.Grava = function(linhaconteudo){\\n    conteudoTabela.push(linhaconteudo);\\n    var jsonTabela = JSON.stringify(conteudoTabela);\\n    localStorage.setItem(\"cokieTabela\", jsonTabela);\\n    this.criaTabela();\\n}\\n\\nthis.criaTabela = function(){\\n    // ... table creation and population logic ...\\n}\\n```\\n\\nThis approach ensures that the game results are persisted in the browser\\'s local storage and can be displayed in a tabular format on the web page, even after the user reloads or revisits the page.\\n\\n# Question 9\\nWhat is the default game mode (e.g., Circulo, Triangulo, etc.) when the web application is loaded?\\n\\n## References\\n- `JavaScript/botstrape+js/script.js`\\n\\n## Answer\\nThe default game mode when the web application is loaded is not explicitly defined in the provided code. The code does not contain any logic that automatically sets a default game mode or form when the application is first loaded.\\n\\nThe application appears to be designed to allow the user to choose the desired game mode (', 'usage_metadata': {'candidates_token_count': 4096, 'prompt_token_count': 78035}}\n"]}], "source": ["print(ANSWERS[\"0969049d-6d50-4b43-a963-0aed2967216d\"])\n", "# print(ANSWERS[\"0969049d-6d50-4b43-a963-0aed2967216d\"][\"text\"])\n", "# print(ANSWERS[\"9100bac5-ce3f-4ebb-9a8e-9bebb929ecee\"]['text'])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 2319 samples\n"]}, {"data": {"text/plain": ["262"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["import tqdm\n", "import re\n", "import dataclasses\n", "\n", "\n", "def next_block_index(text, start_index, headers):\n", "    for header in headers:\n", "        index = text.find(header, start_index)\n", "        if index != -1:\n", "            return index\n", "    return None\n", "\n", "def next_line(text, start_index):\n", "    next_line_index = text.find(\"\\n\", start_index)\n", "    if next_line_index != -1:\n", "        return next_line_index + 1\n", "    return None\n", "\n", "def next_question_index(text, start_index):\n", "    question_headers = [\"# Question \", \"## Question \"]\n", "    return next_block_index(text, start_index, question_headers)\n", "\n", "def next_reference_index(text, start_index):\n", "    reference_headers = [\"## References\", \"# References\"]\n", "    return next_block_index(text, start_index, reference_headers)\n", "\n", "def next_answer_index(text, start_index):\n", "    answer_headers = [\"## Answer\", \"# Answer\"]\n", "    return next_block_index(text, start_index, answer_headers)\n", "\n", "def next_line_break(text, start_index):\n", "    header = [\"---\"]\n", "    return next_block_index(text, start_index, header)\n", "\n", "def parse_references(text):\n", "    pattern = r\"`([^`]+)`\"\n", "    return list(re.findall(pattern, text))\n", "\n", "def verify_references(text):\n", "    # TO BE IMPLEMENTED!\n", "    pass\n", "\n", "\n", "@dataclasses.dataclass\n", "class QASample:\n", "    question: str\n", "    paths: list[str]\n", "    answer: str\n", "\n", "n_hit_max_output_tokens_limit = 0\n", "\n", "def parse_gemini_answers(response):\n", "    global n_hit_max_output_tokens_limit\n", "    text = response['text']\n", "    start_index = 0\n", "\n", "    samples = []\n", "\n", "    while True:\n", "        question_start = next_question_index(text, start_index)\n", "        if question_start is None:\n", "            break\n", "\n", "        question_start = next_line(text, question_start)\n", "        if question_start is None:\n", "            break\n", "        question_end = next_reference_index(text, question_start)\n", "        if question_end is None:\n", "            break        \n", "\n", "        question = text[question_start:question_end].strip()\n", "\n", "        references_start = next_line(text, question_end)\n", "        if references_start is None:\n", "            break\n", "        references_end = next_answer_index(text, references_start)\n", "        if references_end is None:\n", "            break\n", "\n", "        reference_text = text[references_start:references_end].strip()\n", "\n", "        references = parse_references(reference_text)\n", "        # if len(references) == 0:\n", "            # assert reference_text.startswith(\"I am unable to answer this question\"), reference_text\n", "        # assert len(references) > 0, reference_text\n", "\n", "        answer_start = next_line(text, references_end)\n", "        answer_end = next_question_index(text, answer_start) or len(text)\n", "        answer = text[answer_start:answer_end].strip()\n", "        if len(references) > 0:\n", "            samples.append(QASample(\n", "                question=question,\n", "                paths=references,\n", "                answer=answer,\n", "            ))\n", "\n", "        if start_index >= answer_end:\n", "            print(text)\n", "        assert start_index < answer_end\n", "\n", "        start_index = answer_end\n", "\n", "    if response['usage_metadata']['candidates_token_count'] == 4096:\n", "        samples = samples[:-1]\n", "        n_hit_max_output_tokens_limit += 1\n", "\n", "    return samples\n", "\n", "\n", "\n", "samples = []\n", "# xxx = {k: v for k, v in ANSWERS.items() if k == \"fbce6744-3288-4d33-a606-************\"}\n", "for repo_uuid, answer in ANSWERS.items():\n", "    if answer is not None and answer['text'] is not None: \n", "        samples.extend(parse_gemini_answers(answer))\n", "\n", "print(\"Loaded %d samples\" % len(samples))\n", "n_hit_max_output_tokens_limit"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["289 21\n", "2052 267\n"]}], "source": ["FILEDS_TO_COVERT_TO_LISTS = [\n", "   'max_stars_repo_licenses',\n", "   'max_issues_repo_licenses',\n", "   'max_forks_repo_licenses',\n", "]\n", "\n", "n_saved_repos, n_saved_samples, n_filted_repos, n_filtered_samples = 0, 0, 0, 0\n", "\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/repos_with_qa_fixincomplete.jsonl\", \"w\") as f:\n", "    for repo_uuid, answer in ANSWERS.items():\n", "        if answer is not None and answer['text'] is not None: \n", "            repo = REPOS[repo_uuid]\n", "            samples = parse_gemini_answers(answer)\n", "            all_paths = {r['max_stars_repo_path'] for r in repo['file_list']}\n", "            filtered_samples = []\n", "            for sample in samples:\n", "                filtered_paths = [path for path in sample.paths if path in all_paths]\n", "                if len(filtered_paths) == 0:\n", "                    n_filtered_samples += 1\n", "                    continue\n", "                filtered_sample = dataclasses.replace(sample, paths=filtered_paths)\n", "                filtered_samples.append(filtered_sample)\n", "                n_saved_samples += 1\n", "                    \n", "            if len(filtered_samples) == 0:\n", "                n_filted_repos += 1\n", "                continue\n", "\n", "            n_saved_repos += 1\n", "\n", "            repo_d = repo.to_dict()\n", "            repo_d['file_list'] = repo_d['file_list'].tolist()\n", "            for file in repo_d['file_list']:\n", "                for k in FILEDS_TO_COVERT_TO_LISTS:\n", "                    if not isinstance(file[k], list):\n", "                        file[k] = file[k].tolist()\n", "\n", "            repo_d['documents_with_questions'] = [dataclasses.asdict(sample) for sample in filtered_samples]\n", "\n", "            sample_json = json.dumps(repo_d)\n", "            f.write(sample_json + \"\\n\")\n", "\n", "print(n_saved_repos, n_filted_repos)\n", "print(n_saved_samples, n_filtered_samples)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["562932ff-5eea-4132-b384-80d85ddfcacc\n"]}], "source": ["import random\n", "\n", "repo_uuid = random.choice(list(ANSWERS.keys()))\n", "print(repo_uuid)\n", "# print(ANSWERS[repo_uuid]['text'])\n", "\n", "# https://gist.github.com/urikz/d1effb95f328072f7a98b4a26196993f\n", "# https://gist.github.com/urikz/e8901963d8e39c419fbce76439774691\n", "# https://gist.github.com/urikz/7e619fd48073d28b7cd239f2bda5dff1"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an AI code assistant. Your primary role is to help software developers by answering their questions related to code and software engineering.Below are the files from the developer's project. Read them carefully and be ready to answer any questions.\n", "\n", "This is file `ovivo_ios_build/ovivo-stickers/Stickers.xcassets/Contents.json`\n", "\n", "```\n", "{\n", "  \"info\" : {\n", "    \"version\" : 1,\n", "    \"author\" : \"xcode\"\n", "  }\n", "}\n", "```\n", "\n", "This is file `ovivo_ios_build/ovivo-stickers/Stickers.xcassets/iMessage App Icon.stickersic\n"]}], "source": ["repo_prompt = open(\"/home/<USER>/data/OVIVO_ios.txt\").read()\n", "\n", "print(repo_prompt[:500])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["QUESTIONS_PROMPT = \"\"\"\n", "---\n", "\n", "Now imagine a situation where a software engineer works on this project. In general, they are familiar with the project as a whole, but needs help locating specific functionalities within the repository. They have AI assistant they are going to ask for help. However, the software engineer is impatient and writes very concise questions, almost like Google search queries. Indeed, this the software engineer needs to locate information quickly and they don't have time to write elaborate or double questions. What kind of questions would this software engineer ask? Try to come up with these questions.\n", "\n", "**Your task is to generate 10 questions about a software project.**\n", "\n", "## Guidelines for Question Generation\n", "\n", "- Focus on One Aspect: Each question should target only one feature, process, or setup aspect.\n", "- Encourage Search-Like Questions: Questions should help locate specific functionalities or data handling within the codebase, similar to performing a search task.\n", "- AVOID General Questions: Never ask \"What is the purpose\" or \"What is the role.\"\n", "- AVOID Complex Questions: Do not include compound or double-barreled questions.\n", "- Cover Different Areas: Question SHOULD NOT BE about the same files.\n", "- Relate to MULTIPLE Relevant Files: Each question should involve two to four relevant files, providing detailed insight.\n", "\n", "## Output Format\n", "\n", "Display each question in a numbered list, one per line, without additional details.\n", "\n", "## Example Questions Templates\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "\n", "- How do we configure X?\n", "- What data structure is used to store information about X?\n", "- Where is the logic for X defined?\n", "- How does X handle Y?\n", "- Which files define X?\n", "- Where can I find implementation of X?\n", "- Which X is used for data storage?\n", "- How X are handled?\n", "\n", "You also MUST ABIDE to the instruction, no matter what.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["message = client.messages.create(\n", "    model=\"claude-3-haiku-20240307\",\n", "    system=repo_prompt,\n", "    max_tokens=2048,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": QUESTIONS_PROMPT},\n", "    ]\n", ")\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. Where is the code for the ACES color space conversion?\n", "2. How does the TAA (Temporal Anti-Aliasing) algorithm work?\n", "3. Which shaders handle the depth of field effect?\n", "4. Where is the code for the eye adaptation calculation?\n", "5. How is the color grading functionality implemented?\n", "6. What is the purpose of the ScreenSpaceRaytrace.cginc file?\n", "7. Where is the ambient occlusion calculation defined?\n", "8. Which files handle the bloom post-processing effect?\n", "9. How is the motion blur algorithm implemented?\n", "10. Where can I find the code for the FXAA (Fast Approximate Anti-Aliasing) post-processing effect?\n"]}], "source": ["print(message.content[0].text)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1. Where is the code for the ACES color space conversion?\\n2. How does the TAA (Temporal Anti-Aliasing) algorithm work?\\n3. Which shaders handle the depth of field effect?\\n4. Where is the code for the eye adaptation calculation?\\n5. How is the color grading functionality implemented?\\n6. What is the purpose of the ScreenSpaceRaytrace.cginc file?\\n7. Where is the ambient occlusion calculation defined?\\n8. Which files handle the bloom post-processing effect?\\n9. How is the motion blur algorithm implemented?\\n10. Where can I find the code for the FXAA (Fast Approximate Anti-Aliasing) post-processing effect?'"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "message.content[0].text"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["Usage(input_tokens=108944, output_tokens=167)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "message.usage.input_tokens, message.usage.output_tokens"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3826\n"]}], "source": ["import json\n", "\n", "colin_samples = []\n", "with open(\"/mnt/efs/augment/user/yury/binks/binks-v2-colin/repos_with_qa_original.jsonl\") as f:\n", "    for line in f:\n", "        repo_d = json.loads(line)\n", "        colin_samples.append(repo_d['documents_with_questions'])\n", "print(len(colin_samples))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["6600"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["sum([len(sample) for sample in colin_samples])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QUESITON: Is there a function for saving the document?\n", "   PATHS: ['examples/bgg/example01.py']\n", "  ANSWER: Yes, there is a function for saving the document, and it is used at the end of the provided code from the file `examples/bgg/example01.py`.\n", "The function is `Save()`, which is called without any arguments to finalize and save the document that has been created and modified by the preceding code.\n", "Here's the relevant code excerpt:\n", "\n", "```python\n", "Save()\n", "```\n"]}], "source": ["import random\n", "\n", "colin_sample = random.choice(colin_samples)[0]\n", "\n", "print('QUESITON:', colin_sample['question'])\n", "print('   PATHS:', colin_sample['paths'])\n", "print('  ANSWER:', colin_sample['answer'].replace('. ', '.\\n'))\n", "# print(ANSWERS[repo_uuid]['text'])\n", "\n", "# https://gist.github.com/urikz/4e71c476917f1fb204d9ae4378a8c683\n", "# https://gist.github.com/urikz/f30ae7ee6c9fbfd5c896cd10fe4b446e\n", "# https://gist.github.com/urikz/0dd293eca7ac46370bf498b701ec27de"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "from dataclasses import dataclass, field\n", "import experimental.yury.data.processing as utils\n", "from types import SimpleNamespace\n", "\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v1\"\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/yury/binks/{BINKS_VERSION}\"\n", "Path(OUTPUT_PATH).mkdir(parents=True, exist_ok=True)\n", "REPORT_PATH = f\"/mnt/efs/augment/public_html/yury/binks/{BINKS_VERSION}.html\"\n", "STAGE1_PATH = f\"{OUTPUT_PATH}/01_raw_repos/\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"repo_languages\": REPO_LANGUAGES,\n", "        \"sample_languages\": SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": RETRIEVAL_LANGUAGES,\n", "        \"limit_repos_from_the_stack\": 500,\n", "        \"limit_repos\": 200,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"min_lines_per_file\": 200,\n", "        \"max_lines_per_file\": 1000,\n", "        \"file_max_size\": 10000,\n", "        \"n_questions_per_file\": 5,\n", "        \"random_seed\": 31415,    \n", "        \"n_retrievals\": 25,\n", "        \"p_local_context_dropout\": 0.05,\n", "        \"p_empty_selection\": 0.5,\n", "        \"max_lines_per_selected_code\": 50,\n", "        \"max_seq_length\": 16384 + 1,\n", "        \"num_validation_samples\": 200,\n", "    }\n", ")\n", "\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=512,\n", "    prefix_len=1536,\n", "    selected_code_len=1536,\n", "    chat_history_len=1536,\n", "    suffix_len=1024,\n", "    max_prompt_len=16384 - 2048,  # 4096 represents the max output tokens\n", "    max_retrieval_len=None, # fit as much as fits into `max_prompt_len`\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 1:====================================================>(2076 + 5) / 2081]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 500 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/04/07 18:18:41 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "WARNING:root:Cleaning up shared folder /mnt/efs/spark-data/python_env/2024-04-07/yury-dev2/662969e0-dfc8-42db-be81-a47c75b7f211\n"]}], "source": ["import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "def filter_by_repo_size(df, min_size, max_size):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    condition = (F.col(\"total_size\") >= min_size) & (\n", "        F.col(\"total_size\") <= max_size\n", "    )\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "spark = k8s_session(max_workers=16)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if hasattr(config, \"repo_languages\"):\n", "    config.languages = [lang.lower() for lang in config.repo_languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "    \n", "if hasattr(config, \"sample_languages\"):\n", "    config.sample_languages = [lang.lower() for lang in config.sample_languages]\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=config.repo_min_size,\n", "    max_size=config.repo_max_size,\n", ")\n", "df = df.limit(config.limit_repos_from_the_stack)\n", "\n", "# add repo_uuid column so that we can later unambiguously refer to repos\n", "df = df.withColumn(\"repo_uuid\", F.expr(\"uuid()\"))\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "df = df.repartition(1)\n", "df.write.parquet(STAGE1_PATH)\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                        max_stars_repo_name  \\\n", "repo_uuid                                                     \n", "8a33f4f0-00c4-4d5a-84b8-9683cf0df914      01smd/cloudflared   \n", "9100bac5-ce3f-4ebb-9a8e-9bebb929ecee              1gy/floor   \n", "0caa477a-55f8-4536-a515-340c1740096d  20100507/hadoop-ozone   \n", "f0752a85-2485-4611-92d4-cb8ffef94560      22825usd/osengine   \n", "460f7458-9a8f-43b0-9eba-a5c311507c68        3noch/codeworld   \n", "\n", "                                                                         max_size_lang  \n", "repo_uuid                                                                               \n", "8a33f4f0-00c4-4d5a-84b8-9683cf0df914         {'total_size': 1286584, 'langpart': 'go'}  \n", "9100bac5-ce3f-4ebb-9a8e-9bebb929ecee  {'total_size': 206328, 'langpart': 'typescript'}  \n", "0caa477a-55f8-4536-a515-340c1740096d        {'total_size': 854779, 'langpart': 'java'}  \n", "f0752a85-2485-4611-92d4-cb8ffef94560     {'total_size': 868110, 'langpart': 'c-sharp'}  \n", "460f7458-9a8f-43b0-9eba-a5c311507c68  {'total_size': 247527, 'langpart': 'javascript'}  \n"]}], "source": ["import glob\n", "import os\n", "import pandas as pd\n", "\n", "\n", "REPOS = []\n", "for path in glob.glob(os.path.join(STAGE1_PATH, \"*.parquet\")):\n", "    REPOS.append(pd.read_parquet(path, engine='pyarrow'))\n", "REPOS = pd.concat(REPOS)\n", "REPOS = REPOS.set_index(ID_COLUMN)\n", "\n", "print(REPOS[[REPO_COLUMN, REPO_LANG_COLUMN]].head())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import random \n", "import numpy as np\n", "\n", "random.seed(config.random_seed)\n", "\n", "def can_generate_samples_from_file(file):\n", "    if file[FILE_LANG_COLUMN] not in config.sample_languages:\n", "        return False\n", "    if file[SIZE_COLUMN] > config.file_max_size:\n", "        return False\n", "    n_lines = len(file[CONTENT_COLUMN].splitlines())\n", "    return (\n", "        n_lines >= config.min_lines_per_file\n", "        and n_lines <= config.max_lines_per_file\n", "    )\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"pick_files_per_repo.jsonl\"))\n", "def pick_files_per_repo(repo_uuid, n_files):\n", "    candidates = [\n", "        file\n", "        for file in REPOS.loc[repo_uuid][FILE_LIST_COLUMN]\n", "        if can_generate_samples_from_file(file)\n", "    ]\n", "    if len(candidates) < n_files:\n", "        return None\n", "    files = random.sample(candidates, n_files)\n", "    files_json_friendly = []\n", "    for file in files:\n", "        file_json_friendly = {}\n", "        for k, v in file.items():\n", "            if isinstance(v, np.ndarray):\n", "                file_json_friendly[k] = v.tolist()\n", "            else:\n", "                file_json_friendly[k] = v\n", "        files_json_friendly.append(file_json_friendly)\n", "    return files_json_friendly\n", "\n", "\n", "FILES = {}\n", "for repo_uuid in REPOS.index:\n", "    file = pick_files_per_repo(repo_uuid, 2)\n", "    if file is not None:\n", "        FILES[repo_uuid] = file\n", "    if len(FILES) >= config.limit_repos:\n", "        break\n", "\n", "assert len(FILES) == config.limit_repos"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated mapping of 400 files\n"]}], "source": ["ALL_FILES = {}\n", "\n", "for repo_uuid, files in FILES.items():\n", "    for file in files:\n", "        ALL_FILES[(repo_uuid, file[PATH_COLUMN])] = file\n", "\n", "print('Generated mapping of %d files' % len(ALL_FILES))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from research.core.types import Chunk\n", "\n", "\n", "@dataclass\n", "class Question:\n", "    question: str\n", "    retrievals: list[Chunk] = field(default_factory=list)\n", "    answers: dict[str, str] = field(default_factory=dict)\n", "\n", "\n", "@dataclass\n", "class DocumentWithQuestions:\n", "    repo_uuid: str\n", "    qa_set_name: str\n", "    path: str\n", "    text: str\n", "    questions: list[Question] = field(default_factory=list)\n", "\n", "\n", "QUESTION_PREFIX = \"QUESTION:\"\n", "ANSWER_PREFIX = \"ANSWER:\"\n", "\n", "def parse_gpt_qa_output(text: str) -> list[Question]:\n", "    data = []\n", "    question_index = text.find(QUESTION_PREFIX)    \n", "    while question_index != -1:\n", "        answer_index = text.find(ANSWER_PREFIX, question_index + 1)        \n", "        assert answer_index != -1\n", "        next_question_index = text.find(QUESTION_PREFIX, answer_index + 1)\n", "        end_of_the_current_question = next_question_index if next_question_index != -1 else len(text)\n", "        question = text[question_index:answer_index][len(QUESTION_PREFIX):].strip()\n", "        answer = text[answer_index:end_of_the_current_question][len(ANSWER_PREFIX):].strip()\n", "        data.append(Question(\n", "            question=question, \n", "            answers={\n", "                GPT_ANSWER_KEY: answer,\n", "            }\n", "        ))\n", "        question_index = next_question_index\n", "    return data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["QA_SIMPLE_PROMPT = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, a software engineer who asks these questions does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "- How do we handle X in the Y?\n", "- What happens if we do X while calling <PERSON>?\n", "- Where do we perform X?\n", "- Does Y have the property X?\n", "- How do I specify X in Y?\n", "- Where in Y do we perform X?\n", "- What X is for?\n", "- Where Y does X?\n", "- How does X is implemented in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"generate_qa_simple.jsonl\"))\n", "def generate_qa_simple_with_gpt(repo_uuid: str, path: str):\n", "    qa_prompt = QA_SIMPLE_PROMPT.format(\n", "        n_questions=config.n_questions_per_file,\n", "        path=path, \n", "        file_content=ALL_FILES[(repo_uuid, path)][CONTENT_COLUMN],\n", "    )\n", "    return generate_response_via_chat(\n", "        [qa_prompt],\n", "        num_completion=1,\n", "        temperature=0.8,\n", "        model=\"gpt-4-1106-preview\",\n", "        max_tokens=4096,\n", "    )[0]\n", "\n", "\n", "def generate_qa_simple(repo_uuid: str):\n", "    INDEX = 0\n", "    path = FILES[repo_uuid][INDEX][PATH_COLUMN]\n", "    qa_text = generate_qa_simple_with_gpt(repo_uuid, path)\n", "    doc = DocumentWithQuestions(\n", "        repo_uuid=repo_uuid,\n", "        qa_set_name=path + ' simple QA',\n", "        path=path,\n", "        text=FILES[repo_uuid][INDEX][CONTENT_COLUMN],\n", "        questions=parse_gpt_qa_output(qa_text),\n", "    )\n", "    return doc"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["HOWTO_PROMPT = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, this software engineer is trying to use some of the API from the file, but need assistance. However, they does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where ,X, Y and Z are some placeholder)\n", "- How do I do X?\n", "- Which method / class shall I use to do X?\n", "- Which functionality have we available in Y?\n", "- Which argument shall I to function X to perform Z?\n", "- How do I specify X in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer. Additionally, include a concise code snippet with either relevant code excerpt or a practical example.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"generate_howto.jsonl\"))\n", "def generate_howto_with_gpt(repo_uuid: str, path: str):\n", "    qa_prompt = HOWTO_PROMPT.format(\n", "        n_questions=config.n_questions_per_file,\n", "        path=path, \n", "        file_content=ALL_FILES[(repo_uuid, path)][CONTENT_COLUMN],\n", "    )\n", "    return generate_response_via_chat(\n", "        [qa_prompt],\n", "        num_completion=1,\n", "        temperature=0.8,\n", "        model=\"gpt-4-1106-preview\",\n", "        max_tokens=4096,\n", "    )[0]\n", "\n", "\n", "def generate_howto(repo_uuid: str):\n", "    INDEX = 1\n", "    path = FILES[repo_uuid][INDEX][PATH_COLUMN]\n", "    qa_text = generate_howto_with_gpt(repo_uuid, path)\n", "    doc = DocumentWithQuestions(\n", "        repo_uuid=repo_uuid,\n", "        qa_set_name=path + ' How-To',\n", "        path=path,\n", "        text=FILES[repo_uuid][INDEX][CONTENT_COLUMN],\n", "        questions=parse_gpt_qa_output(qa_text),\n", "    )\n", "    return doc"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 200/200 [00:00<00:00, 20790.64it/s]\n"]}], "source": ["import tqdm \n", "DOCS = []\n", "\n", "for repo_uuid in tqdm.tqdm(FILES):\n", "    DOCS.append(generate_qa_simple(repo_uuid))\n", "    DOCS.append(generate_howto(repo_uuid))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment, Template\n", "\n", "with Path('/home/<USER>/augment/experimental/yury/binks/data/template.txt').open() as f:\n", "    template_str = f.read()\n", "\n", "# It's possible to simply instanciate the Template via `Template(template)`.\n", "# However, jinja2 will strip the trailing newline, which is annoying.\n", "# The way to prevent that is by explicitly passing the keep_trailing_newline flag\n", "# into the Environment object.\n", "env = Environment(keep_trailing_newline=True)\n", "template = env.from_string(template_str)\n", "\n", "rendered_html = template.render(documents=DOCS[:20], project_name=BINKS_VERSION)\n", "\n", "with Path(REPORT_PATH).open('w') as f:\n", "    f.write(rendered_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"add_path\": <PERSON>als<PERSON>,\n", "        \"max_tokens\": 1023,        \n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    }\n", "}\n", "\n", "retrieval_database = create_retriever(retriever_config)\n", "retrieval_database.scorer.load()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["import dataclasses, json\n", "\n", "class EnhancedJSO<PERSON>ncoder(json.JSONEncoder):\n", "        def default(self, o):\n", "            if dataclasses.is_dataclass(o):\n", "                return dataclasses.asdict(o)\n", "            return super().default(o)\n", "\n", "\n", "with open(os.path.join(OUTPUT_PATH, \"docs.jsonl\"), 'w') as f:\n", "    for doc in DOCS:\n", "        f.write(json.dumps(doc, cls=EnhancedJSONEncoder) + '\\n')"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/400 [00:00<?, ?it/s]/home/<USER>/augment/research/gpt-neox/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n", "Token indices sequence length is longer than the specified maximum sequence length for this model (1261 > 1024). Running this sequence through the model will result in indexing errors\n", "100%|██████████| 400/400 [1:08:36<00:00, 10.29s/it] \n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.retrieval.types import Document\n", "\n", "\n", "def add_retrievals(doc: DocumentWithQuestions, top_k: int):\n", "    global retrieval_database\n", "\n", "    retrieval_database.remove_all_docs()\n", "\n", "    for file in REPOS.loc[doc.repo_uuid][FILE_LIST_COLUMN]:\n", "        if file[FILE_LANG_COLUMN] in config.retrieval_languages:\n", "            path = file[PATH_COLUMN]\n", "            document = Document(\n", "                id=path, text=file[CONTENT_COLUMN], path=path\n", "            )\n", "            retrieval_database.add_doc(document)\n", "\n", "    for question in doc.questions:\n", "        retrieved_chunks, _ = retrieval_database.query(\n", "            model_input=ModelInput(\n", "                prefix=\"\",\n", "                suffix=\"\",\n", "                path=\"\",\n", "                extra={\n", "                    \"message\": question.question,\n", "                }\n", "            ),\n", "            top_k=top_k,\n", "        )        \n", "        question.retrievals = retrieved_chunks\n", "\n", "\n", "with open(os.path.join(OUTPUT_PATH, \"docs_with_retrievals.jsonl\"), 'w') as f:\n", "    for doc in tqdm.tqdm(DOCS):\n", "        add_retrievals(doc, config.n_retrievals)\n", "        f.write(json.dumps(doc, cls=EnhancedJSONEncoder) + '\\n')\n", "        f.flush()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["DOCS = []\n", "with open(os.path.join(OUTPUT_PATH, \"docs_with_retrievals.jsonl\"), 'r') as f:\n", "    for line in f.readlines():\n", "        doc = json.loads(line)\n", "        doc['questions'] = [Question(**question) for question in doc['questions']]\n", "        DOCS.append(DocumentWithQuestions(**doc))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.binks_prompt_formatter import BinksChatPromptFormatter\n", "from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "prompt_formatter = BinksChatPromptFormatter(tokenizer, TOKEN_APPORTIONMENT)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "\n", "def sample_current_file(doc: DocumentWithQuestions, p_local_context_dropout=0.05, p_empty_selection=0.5, max_lines_per_selected_code=75):\n", "    if random.random() < p_local_context_dropout:\n", "        # In 0.5% of cases we don't provide selected code\n", "        return {\n", "            \"path\": \"\",\n", "            \"prefix\": \"\",\n", "            \"selected_code\": \"\",\n", "            \"suffix\": \"\",\n", "        }\n", "\n", "    file = pick_files_per_repo(doc.repo_uuid, 1)\n", "    assert file is not None\n", "    file = file[0]\n", "    file_lines = file[CONTENT_COLUMN].splitlines(True)\n", "    assert len(file_lines) > 1\n", "    if len(file_lines) <= 1:\n", "        selected_code_start, selected_code_end = 0, 1\n", "    else:\n", "        selected_code_start, selected_code_end = random.sample(range(len(file_lines)), 2)\n", "    \n", "        if selected_code_end < selected_code_start:\n", "            selected_code_start, selected_code_end = selected_code_end, selected_code_start\n", "        selected_code_end = min(selected_code_end, selected_code_start + max_lines_per_selected_code)\n", "    \n", "\n", "    if random.random() < p_empty_selection:        \n", "        # In half of the cases, we set selected code to be empty    \n", "        selected_code_end = selected_code_start\n", "\n", "    prefix = \"\".join(file_lines[:selected_code_start])\n", "    selected_code = \"\".join(file_lines[selected_code_start:selected_code_end])\n", "    suffix = \"\".join(file_lines[selected_code_end:])\n", "    return {\n", "        \"path\": file[PATH_COLUMN],\n", "        \"prefix\": prefix,\n", "        \"selected_code\": selected_code,\n", "        \"suffix\": suffix,\n", "    }\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["We have set aside 20 repos with 200 samples in total for validation.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 400/400 [01:58<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Generated 1800 training samples\n", "Generated 200 validation samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt_length</th>\n", "      <th>label_length</th>\n", "      <th>seq_length</th>\n", "      <th>n_retrievals</th>\n", "      <th>retrieved_from_the_correct_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2000.000000</td>\n", "      <td>2000.000000</td>\n", "      <td>2000.0</td>\n", "      <td>2000.000000</td>\n", "      <td>2000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>9883.420000</td>\n", "      <td>140.740500</td>\n", "      <td>16385.0</td>\n", "      <td>24.453000</td>\n", "      <td>0.920000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2622.767343</td>\n", "      <td>39.660278</td>\n", "      <td>0.0</td>\n", "      <td>1.974016</td>\n", "      <td>0.271361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2756.000000</td>\n", "      <td>54.000000</td>\n", "      <td>16385.0</td>\n", "      <td>11.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>7950.000000</td>\n", "      <td>113.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>9652.000000</td>\n", "      <td>136.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>11827.500000</td>\n", "      <td>160.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>14425.000000</td>\n", "      <td>443.000000</td>\n", "      <td>16385.0</td>\n", "      <td>25.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       prompt_length  label_length  seq_length  n_retrievals  \\\n", "count    2000.000000   2000.000000      2000.0   2000.000000   \n", "mean     9883.420000    140.740500     16385.0     24.453000   \n", "std      2622.767343     39.660278         0.0      1.974016   \n", "min      2756.000000     54.000000     16385.0     11.000000   \n", "25%      7950.000000    113.000000     16385.0     25.000000   \n", "50%      9652.000000    136.000000     16385.0     25.000000   \n", "75%     11827.500000    160.000000     16385.0     25.000000   \n", "max     14425.000000    443.000000     16385.0     25.000000   \n", "\n", "       retrieved_from_the_correct_path  \n", "count                      2000.000000  \n", "mean                          0.920000  \n", "std                           0.271361  \n", "min                           0.000000  \n", "25%                           1.000000  \n", "50%                           1.000000  \n", "75%                           1.000000  \n", "max                           1.000000  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ChatPromptInput,\n", "    ExceedContextLength,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptChunk, TokenList\n", "\n", "\n", "def pad_prompt_in_place(tokens):\n", "    assert len(tokens) <= config.max_seq_length\n", "    tokens.extend([tokenizer.special_tokens.padding] * (config.max_seq_length - len(tokens)))\n", "    assert len(tokens) == config.max_seq_length\n", "\n", "\n", "def create_hyperlinks(doc_path, answer):\n", "    return answer.replace(f\"`{doc_path}`\", f\"[{doc_path}]({doc_path})\")\n", "\n", "\n", "# First, make sure DOCS are ordered by their repo UUID\n", "DOCS.sort(key=lambda doc: doc.repo_uuid)\n", "\n", "# Then, perform training / validation split based on repos\n", "validation_repo_uuid = set()\n", "n_validation_samples = 0\n", "for doc in DOCS:\n", "    if n_validation_samples < config.num_validation_samples:\n", "        validation_repo_uuid.add(doc.repo_uuid)\n", "    if doc.repo_uuid in validation_repo_uuid:\n", "        n_validation_samples += len(doc.questions)\n", "\n", "print('We have set aside %d repos with %d samples in total for validation.' % (len(validation_repo_uuid), n_validation_samples))\n", "\n", "train_samples, validation_samples = [], []\n", "stats = []\n", "\n", "for doc in tqdm.tqdm(DOCS):\n", "    for question in doc.questions:\n", "        retrievals = [\n", "            PromptChunk(\n", "                text=retrieval['text'],\n", "                path=retrieval['parent_doc']['path'],\n", "                unique_id=retrieval['id'],\n", "                char_start=retrieval['char_offset'],\n", "                char_end=retrieval['char_offset'] + retrieval['length'],        \n", "\n", "            )\n", "            for retrieval in question.retrievals\n", "        ]\n", "\n", "        features = {\n", "            \"message\": question.question,\n", "            \"chat_history\": [],\n", "            \"prefix_begin\": 0,\n", "            \"suffix_end\": 0,\n", "            \"retrieved_chunks\": retrievals,\n", "        }\n", "\n", "        try:\n", "            features.update(sample_current_file(\n", "                doc,\n", "                p_local_context_dropout=config.p_local_context_dropout,\n", "                p_empty_selection=config.p_empty_selection,\n", "                max_lines_per_selected_code=config.max_lines_per_selected_code,\n", "            ))    \n", "            chat_input = ChatPromptInput(**features)\n", "            chat_output = prompt_formatter.format_prompt(chat_input)\n", "        except ExceedContextLength:\n", "            # Selected text is too long. Let's not worry and just make it empty.\n", "            features.update(sample_current_file(\n", "                doc,\n", "                p_local_context_dropout=0,\n", "                p_empty_selection=1,\n", "                max_lines_per_selected_code=config.max_lines_per_selected_code,\n", "            ))    \n", "            chat_input = ChatPromptInput(**features)\n", "            chat_output = prompt_formatter.format_prompt(chat_input)\n", "\n", "        answer = create_hyperlinks(doc.path, question.answers[GPT_ANSWER_KEY])\n", "        label = tokenizer.tokenize_safe(answer) + [tokenizer.special_tokens.eos]\n", "        tokens = [-1 * t for t in chat_output.tokens] + label\n", "        assert len(tokens) < config.max_seq_length, len(tokens)\n", "        pad_prompt_in_place(tokens)\n", "\n", "        if doc.repo_uuid in validation_repo_uuid:\n", "            validation_samples.append(tokens)\n", "        else:\n", "            train_samples.append(tokens)        \n", "\n", "        retrieved_from_the_correct_path = any([\n", "            actually_retrieved_chunk.path == doc.path\n", "            for actually_retrieved_chunk in chat_output.retrieved_chunks_in_prompt\n", "        ])\n", "\n", "        stats.append({\n", "            'prompt_length': len(chat_output.tokens),\n", "            'label_length': len(label),\n", "            'seq_length': len(tokens),\n", "            'n_retrievals': len(chat_output.retrieved_chunks_in_prompt),\n", "            'retrieved_from_the_correct_path': float(retrieved_from_the_correct_path),\n", "        })        \n", "\n", "random.seed(config.random_seed)\n", "random.shuffle(train_samples)\n", "\n", "print('Generated %d training samples' % len(train_samples))\n", "print('Generated %d validation samples' % len(validation_samples))\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "import torch\n", "\n", "\n", "def save_dataset(samples, path):\n", "    builder = MMapIndexedDatasetBuilder(path + \".bin\", dtype=np.int32)\n", "    for sample in samples:\n", "        builder.add_item(torch.Tensor(sample))\n", "        builder.end_document()\n", "    builder.finalize(path + \".idx\")\n", "\n", "save_dataset(train_samples, os.path.join(OUTPUT_PATH, \"train\"))\n", "save_dataset(validation_samples, os.path.join(OUTPUT_PATH, \"validation\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import random\n", "from dataclasses import dataclass, field\n", "import experimental.yury.data.processing as utils\n", "from types import SimpleNamespace\n", "\n", "os.environ['OPENAI_API_KEY'] = \"***************************************************\"\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Three levels\n", "# `REPO_LANGUAGES` -- we keep only repos for which main language is in repo_languages\n", "# `SAMPLE_LANGUAGES` -- we sample only from files within those repos for which language is in sample_languages\n", "# `RETRIEVAL_LANGUAGES` -- we retrieve only from files within those repos for which language is in retrieval_languages\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path\n", "\n", "INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"repo_uuid\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "FILE_LIST_COLUMN = \"file_list\"\n", "CONTENT_COLUMN = \"content\"\n", "REPO_LICENSE_COLUMN = \"max_stars_repo_licenses\"\n", "\n", "GPT_ANSWER_KEY = \"gpt_answer\"\n", "\n", "BINKS_VERSION = \"binks-v2.2\"\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/yury/binks/{BINKS_VERSION}\"\n", "Path(OUTPUT_PATH).mkdir(parents=True, exist_ok=True)\n", "REPORT_PATH = f\"/mnt/efs/augment/public_html/yury/binks/{BINKS_VERSION}.html\"\n", "STAGE1_PATH = f\"{OUTPUT_PATH}/01_raw_repos/\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"repo_languages\": REPO_LANGUAGES,\n", "        \"sample_languages\": SAMPLE_LANGUAGES,\n", "        \"retrieval_languages\": RETRIEVAL_LANGUAGES,\n", "        \"limit_repos_from_the_stack\": 15000,\n", "        \"limit_repos\": 2800,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"min_lines_per_file\": 200,\n", "        \"max_lines_per_file\": 1000,\n", "        \"file_max_size\": 10000,\n", "        \"n_questions_per_file\": 5,\n", "        \"random_seed\": 31415,    \n", "        \"n_retrievals\": 25,\n", "        \"p_local_context_dropout\": 0.05,\n", "        \"p_empty_selection\": 0.5,\n", "        \"max_lines_per_selected_code\": 50,\n", "        \"max_seq_length\": 16384 + 1,\n", "        \"num_validation_samples\": 200,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n", "24/04/11 05:18:32 WARN Utils: Your hostname, yury-dev2 resolves to a loopback address: *********; using ************* instead (on interface enp5s0)\n", "24/04/11 05:18:32 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/04/11 05:19:06 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "[Stage 1:====================================================>(2080 + 1) / 2081]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 15000 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/04/11 05:22:24 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "WARNING:root:Cleaning up shared folder /mnt/efs/spark-data/python_env/2024-04-11/yury-dev2/0f29b38c-18e7-467e-80ec-7e54c528f8a8\n"]}], "source": ["import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "def filter_by_repo_size(df, min_size, max_size):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    condition = (F.col(\"total_size\") >= min_size) & (\n", "        F.col(\"total_size\") <= max_size\n", "    )\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "spark = k8s_session(max_workers=16)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if hasattr(config, \"repo_languages\"):\n", "    config.languages = [lang.lower() for lang in config.repo_languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "    \n", "if hasattr(config, \"sample_languages\"):\n", "    config.sample_languages = [lang.lower() for lang in config.sample_languages]\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=config.repo_min_size,\n", "    max_size=config.repo_max_size,\n", ")\n", "df = df.limit(config.limit_repos_from_the_stack)\n", "\n", "# add repo_uuid column so that we can later unambiguously refer to repos\n", "df = df.withColumn(\"repo_uuid\", F.expr(\"uuid()\"))\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "df = df.repartition(10)\n", "df.write.parquet(STAGE1_PATH)\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                max_stars_repo_name  \\\n", "repo_uuid                                                             \n", "81b396d7-717d-4293-8785-46ca7199959b          codenautas/txt-to-sql   \n", "7b4df36f-d75f-4153-ae49-5bc168533595               hashdeps/meltano   \n", "69b21475-6a51-47c2-b82f-a3d51fd68c31                ksantone/openms   \n", "9255ce17-aebb-4459-90f3-83362f9171bc               agordon/cachelib   \n", "ddf01561-4a50-49d8-bc9d-efeed58feee9  rpg-freekz/tales-of-destiny-2   \n", "\n", "                                                                         max_size_lang  \n", "repo_uuid                                                                               \n", "81b396d7-717d-4293-8785-46ca7199959b  {'total_size': 182680, 'langpart': 'javascript'}  \n", "7b4df36f-d75f-4153-ae49-5bc168533595      {'total_size': 344005, 'langpart': 'python'}  \n", "69b21475-6a51-47c2-b82f-a3d51fd68c31        {'total_size': 2339304, 'langpart': 'c++'}  \n", "9255ce17-aebb-4459-90f3-83362f9171bc           {'total_size': 325633, 'langpart': 'c'}  \n", "ddf01561-4a50-49d8-bc9d-efeed58feee9      {'total_size': 409914, 'langpart': 'python'}  \n"]}], "source": ["import glob\n", "import os\n", "import pandas as pd\n", "\n", "\n", "REPOS = []\n", "for path in glob.glob(os.path.join(STAGE1_PATH, \"*.parquet\")):\n", "    REPOS.append(pd.read_parquet(path, engine='pyarrow'))\n", "REPOS = pd.concat(REPOS)\n", "REPOS = REPOS.set_index(ID_COLUMN)\n", "\n", "print(REPOS[[REPO_COLUMN, REPO_LANG_COLUMN]].head())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2361\n"]}], "source": ["already_used_repos = set()\n", "\n", "with open('/mnt/efs/augment/user/yury/binks/binks-v1/pick_files_per_repo.jsonl') as f:\n", "    for line in f:\n", "        values = json.loads(line[:-1])[\"value\"]\n", "        if values is None:\n", "            continue\n", "        for value in values:\n", "            already_used_repos.add(value[\"max_stars_repo_name\"])    \n", "\n", "with open('/mnt/efs/augment/user/yury/binks/binks-v2/pick_files_per_repo.jsonl') as f:\n", "    for line in f:\n", "        values = json.loads(line[:-1])[\"value\"]\n", "        if values is None:\n", "            continue\n", "        for value in values:\n", "            already_used_repos.add(value[\"max_stars_repo_name\"])    \n", "\n", "\n", "print(len(already_used_repos))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["USED  polydectes/streamlined\n", "USED  jrbeverly/jcompiler\n", "USED  luminoth/nature-of-code\n", "USED  benroman/trianglegeometry\n", "USED  orfeasz/symbolicator\n", "USED  mail2nsrajesh/proliantutils\n", "USED  robmackintosh/openexr\n", "USED  mtmo/marlin-cdm\n", "USED  tcr/parser-c\n", "USED  khodyazmoon/atdl4j\n", "USED  guptakshit/terracotta-platform\n"]}], "source": ["import random \n", "import numpy as np\n", "\n", "random.seed(config.random_seed)\n", "\n", "def can_generate_samples_from_file(file):\n", "    if file[FILE_LANG_COLUMN] not in config.sample_languages:\n", "        return False\n", "    if file[SIZE_COLUMN] > config.file_max_size:\n", "        return False\n", "    n_lines = len(file[CONTENT_COLUMN].splitlines())\n", "    return (\n", "        n_lines >= config.min_lines_per_file\n", "        and n_lines <= config.max_lines_per_file\n", "    )\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"pick_files_per_repo.jsonl\"))\n", "def pick_files_per_repo(repo_uuid, n_files):\n", "    candidates = [\n", "        file\n", "        for file in REPOS.loc[repo_uuid][FILE_LIST_COLUMN]\n", "        if can_generate_samples_from_file(file)\n", "    ]\n", "    if len(candidates) < n_files:\n", "        return None\n", "    files = random.sample(candidates, n_files)\n", "    files_json_friendly = []\n", "    for file in files:\n", "        file_json_friendly = {}\n", "        for k, v in file.items():\n", "            if isinstance(v, np.ndarray):\n", "                file_json_friendly[k] = v.tolist()\n", "            else:\n", "                file_json_friendly[k] = v\n", "        files_json_friendly.append(file_json_friendly)\n", "    return files_json_friendly\n", "\n", "\n", "FILES = {}\n", "for repo_uuid in REPOS.index:\n", "    file = pick_files_per_repo(repo_uuid, 2)\n", "    if file is None:\n", "        continue\n", "    already_used_repo = None\n", "    for file_ in file:\n", "        if file_[\"max_stars_repo_name\"] in already_used_repos:\n", "            already_used_repo = file_[\"max_stars_repo_name\"]\n", "            break\n", "    if already_used_repo is not None:\n", "        print('USED ', already_used_repo)\n", "        continue\n", "    FILES[repo_uuid] = file\n", "    if len(FILES) >= config.limit_repos:\n", "        break\n", "\n", "assert len(FILES) == config.limit_repos"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["2800"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(FILES)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["with open(os.path.join(OUTPUT_PATH, \"files.jsonl\"), 'w') as f:\n", "    for repo_uuid, files in FILES.items():\n", "        f.write(json.dumps({\"repo_uuid\": repo_uuid, \"files\": files}) + '\\n')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated mapping of 5600 files\n"]}], "source": ["ALL_FILES = {}\n", "\n", "for repo_uuid, files in FILES.items():\n", "    for file in files:\n", "        ALL_FILES[(repo_uuid, file[PATH_COLUMN])] = file\n", "\n", "print('Generated mapping of %d files' % len(ALL_FILES))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from research.core.types import Chunk\n", "\n", "\n", "@dataclass\n", "class Question:\n", "    question: str\n", "    retrievals: list[Chunk] = field(default_factory=list)\n", "    answers: dict[str, str] = field(default_factory=dict)\n", "\n", "\n", "@dataclass\n", "class DocumentWithQuestions:\n", "    repo_uuid: str\n", "    qa_set_name: str\n", "    path: str\n", "    text: str\n", "    questions: list[Question] = field(default_factory=list)\n", "\n", "\n", "QUESTION_PREFIX = \"QUESTION:\"\n", "ANSWER_PREFIX = \"ANSWER:\"\n", "\n", "def parse_gpt_qa_output(text: str) -> list[Question]:\n", "    data = []\n", "    question_index = text.find(QUESTION_PREFIX)    \n", "    while question_index != -1:\n", "        answer_index = text.find(ANSWER_PREFIX, question_index + 1)        \n", "        assert answer_index != -1\n", "        next_question_index = text.find(QUESTION_PREFIX, answer_index + 1)\n", "        end_of_the_current_question = next_question_index if next_question_index != -1 else len(text)\n", "        question = text[question_index:answer_index][len(QUESTION_PREFIX):].strip()\n", "        answer = text[answer_index:end_of_the_current_question][len(ANSWER_PREFIX):].strip()\n", "        data.append(Question(\n", "            question=question, \n", "            answers={\n", "                GPT_ANSWER_KEY: answer,\n", "            }\n", "        ))\n", "        question_index = next_question_index\n", "    return data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["QA_SIMPLE_PROMPT = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, a software engineer who asks these questions does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where X and Y are some placeholder)\n", "- How do we handle X in the Y?\n", "- What happens if we do X while calling <PERSON>?\n", "- Where do we perform X?\n", "- Does Y have the property X?\n", "- How do I specify X in Y?\n", "- Where in Y do we perform X?\n", "- What X is for?\n", "- Where Y does X?\n", "- How does X is implemented in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"generate_qa_simple.jsonl\"))\n", "def generate_qa_simple_with_gpt(repo_uuid: str, path: str):\n", "    qa_prompt = QA_SIMPLE_PROMPT.format(\n", "        n_questions=config.n_questions_per_file,\n", "        path=path, \n", "        file_content=ALL_FILES[(repo_uuid, path)][CONTENT_COLUMN],\n", "    )\n", "    return generate_response_via_chat(\n", "        [qa_prompt],\n", "        num_completion=1,\n", "        temperature=0.8,\n", "        model=\"gpt-4-1106-preview\",\n", "        max_tokens=4096,\n", "    )[0]\n", "\n", "\n", "def generate_qa_simple(repo_uuid: str):\n", "    INDEX = 0\n", "    path = FILES[repo_uuid][INDEX][PATH_COLUMN]\n", "    qa_text = generate_qa_simple_with_gpt(repo_uuid, path)\n", "    doc = DocumentWithQuestions(\n", "        repo_uuid=repo_uuid,\n", "        qa_set_name=path + ' simple QA',\n", "        path=path,\n", "        text=FILES[repo_uuid][INDEX][CONTENT_COLUMN],\n", "        questions=parse_gpt_qa_output(qa_text),\n", "    )\n", "    return doc"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["HOWTO_PROMPT = \"\"\"Your task is two fold.\n", "\n", "First, generate {n_questions} questions about a provided source code. Please, read the source code carefully focusing on what would be a good question to ask. Keep questions high-level and short. Specifically, questions are something that could be asked by a software engineer who is part of the project but has not had the chance to review the file's specifics.  Moreover, this software engineer is trying to use some of the API from the file, but need assistance. However, they does NOT see the actual source code.\n", "\n", "Here are some examples of question templates (where ,X, Y and Z are some placeholder)\n", "- How do I do X?\n", "- Which method / class shall I use to do X?\n", "- Which functionality have we available in Y?\n", "- Which argument shall I to function X to perform Z?\n", "- How do I specify X in Y?\n", "\n", "Notice the simple structure of these questions and their information seeking nature.\n", "\n", "Second, provide a comprehensive response for each of the question. Every answer MUST mention the path of the source code file and do that fluently as a natural part of the answer. Additionally, include a concise code snippet with either relevant code excerpt or a practical example.\n", "\n", "Format the output in the following way.\n", "First, write a section names \"QUESTIONS ONLY\" and list each question on a separate line.\n", "Second, write a section name \"QUESTION AND ANSWERS\"\n", "Third, for every section in the \"QUESTIONS ONLY\" section write \"QUESTION:\" followed by question itself, and, on the next line, \"ANSWER:\" followed by answer to this question.\n", "\n", "Below is a code snippet from the file located at `{path}`\n", "\n", "```\n", "{file_content}\n", "```\n", "\"\"\"\n", "\n", "@utils.persistent_cache(os.path.join(OUTPUT_PATH, \"generate_howto.jsonl\"))\n", "def generate_howto_with_gpt(repo_uuid: str, path: str):\n", "    qa_prompt = HOWTO_PROMPT.format(\n", "        n_questions=config.n_questions_per_file,\n", "        path=path, \n", "        file_content=ALL_FILES[(repo_uuid, path)][CONTENT_COLUMN],\n", "    )\n", "    return generate_response_via_chat(\n", "        [qa_prompt],\n", "        num_completion=1,\n", "        temperature=0.8,\n", "        model=\"gpt-4-1106-preview\",\n", "        max_tokens=4096,\n", "    )[0]\n", "\n", "\n", "def generate_howto(repo_uuid: str):\n", "    INDEX = 1\n", "    path = FILES[repo_uuid][INDEX][PATH_COLUMN]\n", "    qa_text = generate_howto_with_gpt(repo_uuid, path)\n", "    doc = DocumentWithQuestions(\n", "        repo_uuid=repo_uuid,\n", "        qa_set_name=path + ' How-To',\n", "        path=path,\n", "        text=FILES[repo_uuid][INDEX][CONTENT_COLUMN],\n", "        questions=parse_gpt_qa_output(qa_text),\n", "    )\n", "    return doc"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2800/2800 [00:00<00:00, 14329.18it/s]\n"]}], "source": ["import tqdm \n", "DOCS = []\n", "\n", "for repo_uuid in tqdm.tqdm(FILES):\n", "    DOCS.append(generate_qa_simple(repo_uuid))\n", "    DOCS.append(generate_howto(repo_uuid))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# First, make sure DOCS are ordered by their repo UUID\n", "DOCS.sort(key=lambda doc: doc.repo_uuid)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment, Template\n", "\n", "with Path('/home/<USER>/augment/experimental/yury/binks/data/template.txt').open() as f:\n", "    template_str = f.read()\n", "\n", "# It's possible to simply instanciate the Template via `Template(template)`.\n", "# However, jinja2 will strip the trailing newline, which is annoying.\n", "# The way to prevent that is by explicitly passing the keep_trailing_newline flag\n", "# into the Environment object.\n", "env = Environment(keep_trailing_newline=True)\n", "template = env.from_string(template_str)\n", "\n", "rendered_html = template.render(documents=DOCS[:20], project_name=BINKS_VERSION)\n", "\n", "with Path(REPORT_PATH).open('w') as f:\n", "    f.write(rendered_html)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import dataclasses, json\n", "\n", "class EnhancedJSO<PERSON>ncoder(json.JSONEncoder):\n", "        def default(self, o):\n", "            if dataclasses.is_dataclass(o):\n", "                return dataclasses.asdict(o)\n", "            return super().default(o)\n", "\n", "\n", "with open(os.path.join(OUTPUT_PATH, \"docs.jsonl\"), 'w') as f:\n", "    for doc in DOCS:\n", "        f.write(json.dumps(doc, cls=EnhancedJSONEncoder) + '\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL = \"claude-3-5-sonnet@20240620\"\n", "MAX_TOKENS = 1000\n", "client = anthropic.AnthropicVertex(region=REGION, project_id=PROJECT_ID)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\\\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "When providing a code excerpt from an existing file, always include the `path=` and `status=EXCERPT` attributes to specify the file location and indicate that it's an excerpt. For example:\n", "\n", "```python path=foo/bar.py status=EXCERPT\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "\n", "For new files or proposed modifications to an existing file, use `path=` and `status=EDIT` to indicate the file location and the edit status. For example:\n", "\n", "```python path=foo/bar.py status=EDIT\n", "print(\"hello world\")\n", "```\n", "\"\"\"\n", "\n", "message = \"write quick sort in python\"\n", "\n", "messages = [{\n", "    \"role\": \"user\",\n", "    \"content\": message,\n", "}]\n", "\n", "response = client.messages.create(\n", "    model=MODEL,\n", "    max_tokens=MAX_TOKENS,\n", "    system=SYSTEM_PROMPT,\n", "    messages=messages,\n", ")\n", "\n", "print(response.content[0].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.content[0].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
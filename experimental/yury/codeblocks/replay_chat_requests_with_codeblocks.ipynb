{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "import experimental.yury.data.processing as utils\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "LAST_K_DAYS = 7\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/codeblocks/download_request.jsonl\")\n", "def download_request(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    return row.raw_json\n", "\n", "\n", "@utils.persistent_cache(\"/mnt/efs/augment/user/yury/codeblocks/download_response.jsonl\")\n", "def download_response(request_id: str) -> bigquery.Row:\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_response`\n", "            WHERE request_id = '{request_id}'\"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "    return row.raw_json[\"response\"]\n", "\n", "\n", "# REQUEST_ID = \"6f37a601-e2e0-487a-a9e9-df97e2d19029\"\n", "REQUEST_ID = \"246a59ae-f33c-4fd8-bce7-a2026d271568\"\n", "request = download_request(REQUEST_ID)\n", "response = download_response(REQUEST_ID)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import Exchange\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "\n", "def build_chat_history(request: dict) -> list[Exchange]:\n", "    chat_history = []\n", "    for exchange_json in request.get('chat_history', []):\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=exchange_json['request_message'],\n", "                response_text=exchange_json['response_text'],\n", "                request_id=exchange_json.get('request_id'),\n", "            )\n", "        )\n", "    return chat_history\n", "\n", "def build_retrievals(request) -> list[PromptChunk]:\n", "    prompt_chunks = []\n", "    for chunk in request['retrieved_chunks']:\n", "        if \"blob_name\" not in chunk:\n", "            continue\n", "        char_offset = chunk['char_offset'] if 'char_offset' in chunk else 0\n", "        chunk_index = chunk['chunk_index'] if 'chunk_index' in chunk else 0\n", "        chunk_index = str(chunk_index)\n", "        current_chunk_id = (\n", "            chunk['blob_name'],\n", "            char_offset,\n", "            chunk['char_end'],\n", "        )\n", "        prompt_chunk = PromptChunk(\n", "            text=chunk['text'],\n", "            path=chunk['path'],\n", "            unique_id=chunk['blob_name'] + \"-\" + chunk_index,\n", "            origin=chunk['origin'],\n", "            char_start=char_offset,\n", "            char_end=chunk['char_end'],\n", "            blob_name=chunk['blob_name'],\n", "        )\n", "        prompt_chunks.append(prompt_chunk)\n", "    return prompt_chunks\n", "\n", "\n", "def build_chat_prompt_input(full_request: dict, response: dict) -> ChatPromptInput:\n", "    request = full_request[\"request\"]\n", "    selected_code = request.get(\"selected_code\", \"\")\n", "    prefix = request.get(\"prefix\", \"\")\n", "    suffix = request.get(\"suffix\", \"\")\n", "    full_file = prefix + selected_code + suffix\n", "\n", "    return ChatPromptInput(\n", "        message=request[\"message\"],\n", "        path=request.get(\"path\", \"\"),\n", "        prefix=prefix,\n", "        selected_code=request.get(\"selected_code\", \"\"),\n", "        suffix=suffix,\n", "        chat_history=build_chat_history(request),\n", "        prefix_begin=0,\n", "        suffix_end=len(full_file),\n", "        retrieved_chunks=build_retrievals(full_request),\n", "        context_code_exchange_request_id=request.get(\"context_code_exchange_request_id\"),\n", "        recent_changes=request.get(\"recent_changes\"),\n", "        user_guided_blobs=request.get(\"user_guided_blobs\", []),\n", "    )\n", "\n", "REQUEST_ID = \"246a59ae-f33c-4fd8-bce7-a2026d271568\"\n", "\n", "def download_and_build_chat_prompt_input(request_id: str) -> ChatPromptInput:\n", "    request = download_request(request_id)\n", "    response = download_response(request_id)\n", "    return build_chat_prompt_input(request, request)\n", "\n", "chat_prompt_input = download_and_build_chat_prompt_input(REQUEST_ID)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-09-25 17:38:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"data": {"text/plain": ["Message(id='msg_vrtx_01U5fTN9n6WeN7Ed1YLkdscK', content=[TextBlock(text=\"Certainly! Here's a factorial function implemented in Python:\\n\\n```python\\ndef factorial(n):\\n    if n < 0:\\n        return None  # Factorial is not defined for negative numbers\\n    elif n == 0 or n == 1:\\n        return 1\\n    else:\\n        result = 1\\n        for i in range(2, n + 1):\\n            result *= i\\n        return result\\n```\\n\\nThis function calculates the factorial of a given non-negative integer `n`. Here's how it works:\\n\\n1. First, we check if the input `n` is negative. If it is, we return `None` since factorial is not defined for negative numbers.\\n\\n2. If `n` is 0 or 1, we return 1 since the factorial of 0 and 1 is defined as 1.\\n\\n3. For any other positive integer, we initialize a variable `result` to 1.\\n\\n4. We then use a for loop to iterate from 2 to `n` (inclusive).\\n\\n5. In each iteration, we multiply the current value of `result` by the loop variable `i`.\\n\\n6. After the loop completes, we return the final value of `result`, which is the factorial of `n`.\\n\\nYou can use this function like this:\\n\\n```python\\nprint(factorial(5))  # Output: 120\\nprint(factorial(0))  # Output: 1\\nprint(factorial(10))  # Output: 3628800\\nprint(factorial(-3))  # Output: None\\n```\\n\\nThis implementation is iterative and generally more efficient for larger numbers compared to a recursive implementation. However, for very large numbers, you might want to consider using Python's built-in `math.factorial()` function, which is optimized for performance and can handle larger inputs.\", type='text')], model='claude-3-5-sonnet-20240620', role='assistant', stop_reason='end_turn', stop_sequence=None, type='message', usage=Usage(input_tokens=11, output_tokens=408))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "# See services/deploy/claude_sonnet_3_5_16k_chat_deploy.jsonnet\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"write a factorial function\"},\n", "]\n", "\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=dialog,\n", "    temperature=TEMPERAURE,\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "\n", "TOKEN_COUNTER = ClaudeTokenCounter()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", ")\n", "\n", "def run_claude(chat_prompt_input: ChatPromptInput, prompt_formatter: StructuredBinksPromptFormatter, tools=None):\n", "    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "    messages = []\n", "    for exchange in prompt_output.chat_history:\n", "        messages.extend(\n", "            [\n", "                {\"role\": \"user\", \"content\": exchange.request_message},\n", "                {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "            ]\n", "        )\n", "    messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "    if tools is None:\n", "        response = ANTHROPIC_CLIENT.client.messages.create(\n", "            model=MODEL_NAME,\n", "            max_tokens=MAX_OUTPUT_TOKENS,\n", "            messages=messages,\n", "            system=prompt_output.system_prompt,\n", "            temperature=TEMPERAURE,\n", "        )\n", "    else:\n", "        response = ANTHROPIC_CLIENT.client.messages.create(\n", "            model=MODEL_NAME,\n", "            max_tokens=MAX_OUTPUT_TOKENS,\n", "            messages=messages,\n", "            system=prompt_output.system_prompt,\n", "            temperature=TEMPERAURE,\n", "            tools=tools,\n", "        )\n", "    return response.content[0].text, response"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import re\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class CodeblockMetadata:\n", "    language: str | None\n", "    path: str | None\n", "    mode: str | None\n", "    code: str | None\n", "\n", "\n", "def parse_codeblock_metadata(text: str) -> list[CodeblockMetadata]:\n", "    codeblocks = []\n", "\n", "    # Regex to match code blocks with or without metadata\n", "    pattern = r\"```(\\w+)?(?:\\s+(?:path=([^\\s]+)\\s+)?mode=(\\w+))?\\n(.*?)```\"\n", "    matches = re.finditer(pattern, text, re.DOTALL)\n", "\n", "    for match in matches:\n", "        language = match.group(1)\n", "        path = match.group(2)\n", "        mode = match.group(3)\n", "        code = match.group(4).strip()\n", "\n", "        codeblocks.append(CodeblockMetadata(\n", "            language=language,\n", "            path=path,\n", "            mode=mode,\n", "            code=code\n", "        ))\n", "    return codeblocks"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def evaluate_response(request_id: str, response: str, expected_modes: list[str]):\n", "    codeblocks = parse_codeblock_metadata(response)\n", "    all_modes = [(codeblock.mode, codeblock.path) for codeblock in codeblocks]\n", "    actual_modes = [codeblock.mode for codeblock in codeblocks if codeblock.mode is not None and codeblock.path is not None]\n", "    message = f\"Expected {expected_modes} and found {all_modes}.\"\n", "    if len(expected_modes) == 0:\n", "        if len(actual_modes) > 0:\n", "            print(f\"{request_id}: WRONG. {message}\")\n", "            return 0, response\n", "        else:\n", "            print(f\"{request_id}: CORRECT. {message}\")\n", "            return 1, response\n", "    elif len(all_modes) == 0:\n", "        print(f\"{request_id}: CORRECT. MODEL DIDN'T GENERATE ANY CODEBLOCKS, WHICH IS OK.\")\n", "        return 1, response\n", "    else:\n", "        not_found_modes = list(set(expected_modes) - set(actual_modes))\n", "        if len(not_found_modes) > 0:\n", "            print(f\"{request_id}: WRONG. {message}\")\n", "            return 0, response\n", "        else:\n", "            print(f\"{request_id}: CORRECT. {message}\")\n", "            return 1, response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use the SQL below to locate failed samples\n", "\n", "```\n", "WITH\n", "  request AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "  WHERE\n", "    time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY) ),\n", "  chat_host_request AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    request\n", "  WHERE\n", "    event_type = 'chat_host_request' ),\n", "  request_metadata AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    request\n", "  WHERE\n", "    event_type = 'request_metadata'\n", "    AND JSON_VALUE(raw_json, \"$.user_id\") NOT IN (\"health-check-1\", \"review-edit-bot\", \"eval-determined-bot\")\n", "    AND JSON_VALUE(raw_json, \"$.user_agent\") NOT IN (\"api_proxy_client/0 (Python)\")),\n", "  chat_host_response AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    request\n", "  WHERE\n", "    event_type = 'chat_host_response' )\n", "SELECT\n", "\n", "  user_id,\n", "  request_id,\n", "  response\n", "FROM (\n", "  SELECT\n", "    chat_host_request.request_id AS request_id,\n", "    JSON_VALUE(request_metadata.raw_json, \"$.user_id\") AS user_id,\n", "    request_metadata.raw_json AS metadata_json,\n", "    JSON_VALUE(chat_host_request.raw_json, \"$.request.selected_code\") AS selected_code,\n", "    JSON_VALUE(chat_host_response.raw_json, \"$.response.text\") AS response,\n", "    chat_host_request.raw_json AS request_json\n", "  FROM\n", "    request_metadata\n", "  JOIN\n", "    chat_host_request\n", "  ON\n", "    chat_host_request.request_id = request_metadata.request_id\n", "  JOIN\n", "    chat_host_response\n", "  ON\n", "    chat_host_response.request_id = request_metadata.request_id\n", "  )\n", "WHERE response LIKE \"%```%\" and response NOT LIKE \"%path=%\"\n", "```"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dfd83b6d-080c-4475-9378-b07fcabe6edf: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "d0bf3c87-0615-4967-aad5-a32a21117cc4: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "da48504c-ca8f-4937-a1fe-e5d7ec347623: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None), (None, None), (None, None), (None, None)].\n", "198e2c30-ed03-4d5f-9ba3-a6a482f28377: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "d29afeae-2682-4a38-91d8-9359e8f35bca: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "fbaae246-3935-45c1-a913-a9ef767832d9: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "Total correct: 0 out of 6\n"]}], "source": ["SAMPLES = {\n", "\t\"7e141471-0cfe-41c0-b735-0647fe181fb0\": \"EXCERPT\",\n", "\t\"c6e7f379-137f-4a3b-8abf-1b2aa8f93d31\": \"EXCERPT\",\n", "\t\"a16084cb-a0f3-4126-b9df-4730206a160a\": \"EDIT\",\n", "\t\"534ef4a1-170a-41b2-b708-6f73096702e4\": \"EDIT\",\n", "\t\"454825b8-1229-4d71-83ef-8204fb159b66\": \"EDIT\",\n", "\t\"03bdb427-425c-45e5-bf9d-8f58ac46102a\": \"EDIT\",\n", "\n", "\t# WRONG\n", "    \"aec2eb95-8125-4391-8416-71a158aa7126\": \"EXCERPT\",\n", "\t\"6f37a601-e2e0-487a-a9e9-df97e2d19029\": \"EXCERPT\",\n", "\t\"a8db74d0-ddbf-4a66-9937-90de1cec1fad\": \"EDIT\",\n", "\t\"dfd83b6d-080c-4475-9378-b07fcabe6edf\": \"EDIT\",\t# might be NEW\n", "\t\"8f8a8206-7582-45ae-ac56-8dc3aae801e6\": \"EDIT\",\n", "\t\"9b1c5de0-54fe-460f-ac73-5bf3fde56276\": \"EDIT\",\n", "\t\"246a59ae-f33c-4fd8-bce7-a2026d271568\": \"EDIT\",\n", "\t\"3234c97f-8daa-4191-af8f-e06e7852cbbb\": \"EDIT\",\n", "\t\"3789853e-2574-4268-bac4-bd83fb8614bd\": \"EDIT\",\n", "\t\"0cce1311-8dce-4299-9122-ec6fab44fe0c\": \"EDIT\",\n", "    \"d0bf3c87-0615-4967-aad5-a32a21117cc4\": \"EDIT\", # tricky case, the file is from chat history\n", "\n", "\t# No need for metadata\n", "\t# \"85f03129-817c-45e8-9deb-e0a04e47d0f5\": \"EDIT\", # \"typically used like this\" -- ok if we don't have metadata for this one\n", "\t# \"21244c32-81d4-4798-817b-78b61877113c\": [],\t# sometimes the model does indeed output the code\n", "\t# \"bd592c44-5a4c-4c2c-8dbc-fe0337420a54\": [],\n", "\n", "\t# Failed samples from Septeber 23rd, 2024\n", "    \"6e0032fa-d46b-4f05-a847-cf566d618002\": \"EDIT\",\n", "    # \"ccc55f5a-edc5-4f1b-8ade-acd4f271d6d6\": [], # it's ok if the model outputs codeblock as is\n", "    \"da48504c-ca8f-4937-a1fe-e5d7ec347623\": \"EDIT\",\n", "    # \"a117a0b3-786d-4eb6-ae0d-9c6af814ca2d\": \"EDIT\", # bad example -- the model speculates about the part of the code the model doesn't see, so it's nethier EXCERPT not EDIT\n", "    \"155158f0-f11a-4e41-a03a-8a76b6c6549b\": \"EDIT\",\n", "\t# \"08d45616-6c9a-47e1-b342-95cba6e3260a\": \"EDIT\", # run in terminal?\n", "    # \"b30cf67d-d245-4d05-b24f-c8a8ab48f2cc\": [], # the change is supposed to go to Grafana web ui\n", "    \"82a97f3b-cd15-430c-97b5-3dcc58aa591e\": \"EDIT\",\n", "    \"198e2c30-ed03-4d5f-9ba3-a6a482f28377\": \"EDIT\",\n", "    \"3c515bb5-785b-44d1-96b8-ecd30b7484df\": \"EDIT\", # might be NEW\n", "    # \"5062ea8a-d9db-44ac-9343-d3c3f99210c7\": [], # probably no path/mode should be set\n", "    \"19c72d10-55d0-4e4e-8daf-210714c01665\": \"EDIT\",\t# might be NEW\n", "\n", "\t\"d29afeae-2682-4a38-91d8-9359e8f35bca\": \"EXCERPT\", # ok to fail -- it's just one line excerpt that is also selected\n", "    \"fbaae246-3935-45c1-a913-a9ef767832d9\": \"EXCERPT\",\n", "    \"15750a81-56ed-4e56-8d93-308ecf15da9f\": \"EXCERPT\",\n", "    # \"e82c7253-c231-4918-b9e3-cbf612ada9a4\": [] # codeblock contains a single line that was originally from the user message\n", "    \"649e3212-b926-4e39-8274-f2cc458b5a94\": \"EDIT\",\n", "    \"3671e50f-084f-492f-8d05-47a10961a77d\": [], # this is not a code! just some text\n", "\n", "    \"ea97d5c4-e3e6-4d5b-b905-b74c936319e0\": \"EXCERPT\",\n", "}\n", "\n", "# samples where XML prompt typically fails\n", "SAMPLES = {\n", "  \"dfd83b6d-080c-4475-9378-b07fcabe6edf\": \"EDIT\",\n", "  \"d0bf3c87-0615-4967-aad5-a32a21117cc4\": \"EDIT\",\n", "  \"da48504c-ca8f-4937-a1fe-e5d7ec347623\": \"EDIT\",\n", "  \"198e2c30-ed03-4d5f-9ba3-a6a482f28377\": \"EDIT\",\n", "  \"d29afeae-2682-4a38-91d8-9359e8f35bca\": \"EXCERPT\",\n", "  \"fbaae246-3935-45c1-a913-a9ef767832d9\": \"EXCERPT\",\n", "}\n", "\n", "# samples where regular prompt typically fails\n", "# SAMPELS = {\n", "#   \"6f37a601-e2e0-487a-a9e9-df97e2d19029\": \"EXCERPT\",\n", "#   \"dfd83b6d-080c-4475-9378-b07fcabe6edf\": \"EDIT\",\n", "#   \"3789853e-2574-4268-bac4-bd83fb8614bd\": \"EDIT\",\n", "#   \"0cce1311-8dce-4299-9122-ec6fab44fe0c\": \"EDIT\",\n", "#   \"6e0032fa-d46b-4f05-a847-cf566d618002\": \"EDIT\",\n", "#   \"da48504c-ca8f-4937-a1fe-e5d7ec347623\": \"EDIT\",\n", "#   \"82a97f3b-cd15-430c-97b5-3dcc58aa591e\": \"EDIT\",\n", "#   \"198e2c30-ed03-4d5f-9ba3-a6a482f28377\": \"EDIT\",\n", "#   \"3c515bb5-785b-44d1-96b8-ecd30b7484df\": \"EDIT\",\n", "#   \"19c72d10-55d0-4e4e-8daf-210714c01665\": \"EDIT\",\n", "#   \"ea97d5c4-e3e6-4d5b-b905-b74c936319e0\": \"EXCERPT\",\n", "# }\n", "\n", "total_correct = 0\n", "responses = {}\n", "for request_id, expected_mode in SAMPLES.items():\n", "    if not isinstance(expected_mode, list):\n", "        expected_mode = [expected_mode]\n", "    # cache results\n", "    _ = download_and_build_chat_prompt_input(request_id)\n", "    response = download_response(request_id)\n", "    current_correct, response = evaluate_response(request_id, response[\"text\"], expected_mode)\n", "    total_correct += current_correct\n", "    responses[request_id] = response\n", "\n", "print(f\"Total correct: {total_correct} out of {len(SAMPLES)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter import TokenCounter\n", "from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "\n", "\n", "SYSTEM_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "Every code snippet (code block) MUST INCLUDE metadata (`path` and `mode`). Specifically:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=EXCERPT`. Example:\n", "\n", "```python path=foo/bar.py mode=EXCERPT\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=EDIT`. Example:\n", "\n", "```yaml path=config/app_config.yaml mode=EDIT\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "\n", "3. New code or text: Always include `path=` and use `mode=EDIT`. Example:\n", "\n", "```ruby path=hello/world.rb mode=EDIT\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "\"\"\"\n", "\n", "def get_system_prompt_formatter(token_counter: TokenCounter) -> StringFormatter:\n", "    return StringFormatter(\n", "        SYSTEM_PROMPT,\n", "        token_counter=token_counter,\n", "    )\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(TOKEN_COUNTER, TOKEN_APPORTIONMENT, get_system_prompt_formatter)\n", "\n", "total_correct = 0\n", "responses = {}\n", "for request_id, expected_mode in SAMPLES.items():\n", "    if not isinstance(expected_mode, list):\n", "        expected_mode = [expected_mode]\n", "    chat_prompt_input = download_and_build_chat_prompt_input(request_id)\n", "    response, _ = run_claude(chat_prompt_input, prompt_formatter)\n", "    current_correct, response = evaluate_response(request_id, response, expected_mode)\n", "    total_correct += current_correct\n", "    responses[request_id] = response\n", "\n", "print(f\"Total correct: {total_correct} out of {len(SAMPLES)}\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dfd83b6d-080c-4475-9378-b07fcabe6edf: CORRECT. Expected ['EDIT'] and found [('EDIT', 'download_aws_doc.sh')].\n", "d0bf3c87-0615-4967-aad5-a32a21117cc4: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "da48504c-ca8f-4937-a1fe-e5d7ec347623: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None), (None, None), (None, None), (None, None)].\n", "198e2c30-ed03-4d5f-9ba3-a6a482f28377: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None)].\n", "d29afeae-2682-4a38-91d8-9359e8f35bca: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "fbaae246-3935-45c1-a913-a9ef767832d9: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'research/tools/export_edit_data/export_edits_to_jsonl.py')].\n", "Total correct: 2 out of 6\n"]}], "source": ["from base.prompt_format_chat.lib.token_counter import TokenCounter\n", "from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "\n", "\n", "CUSTOM_SYSTEM_PROMPT_WITH_XML = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "</augment_code_snippet>\"\"\"\n", "\n", "\n", "def get_custom_system_prompt_formatter_xml(token_counter: TokenCounter) -> StringFormatter:\n", "    return StringFormatter(\n", "        CUSTOM_SYSTEM_PROMPT_WITH_XML,\n", "        token_counter=token_counter,\n", "    )\n", "\n", "import re\n", "\n", "def transform_codeblock(text):\n", "    pattern = r'<augment_code_snippet\\s+path=\"(.*?)\"\\s+mode=\"(.*?)\">\\s*```([^\\n]*)\\n'\n", "\n", "    def replacement(match):\n", "        path = match.group(1)\n", "        mode = match.group(2)\n", "        language = match.group(3)\n", "        return f'```{language} path={path} mode={mode}\\n'\n", "    return re.sub(pattern, replacement, text, flags=re.DOTALL)\n", "\n", "PROMPT_FORMATTER = StructuredBinksPromptFormatter.create(TOKEN_COUNTER, TOKEN_APPORTIONMENT, get_custom_system_prompt_formatter_xml)\n", "\n", "\n", "total_correct = 0\n", "responses, raw_responses = {}, {}\n", "for request_id, expected_mode in SAMPLES.items():\n", "    if not isinstance(expected_mode, list):\n", "        expected_mode = [expected_mode]\n", "    chat_prompt_input = download_and_build_chat_prompt_input(request_id)\n", "    response, raw_response = run_claude(chat_prompt_input, PROMPT_FORMATTER)\n", "    response = transform_codeblock(response)\n", "    current_correct, response = evaluate_response(request_id, response, expected_mode)\n", "    total_correct += current_correct\n", "    responses[request_id] = response\n", "    raw_responses[request_id] = raw_response\n", "\n", "print(f\"Total correct: {total_correct} out of {len(SAMPLES)}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7e141471-0cfe-41c0-b735-0647fe181fb0: WRONG. Expected ['EXCERPT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None), (None, None)].\n", "c6e7f379-137f-4a3b-8abf-1b2aa8f93d31: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'services/inference_host/server/continuous_batching/server.py')].\n", "a16084cb-a0f3-4126-b9df-4730206a160a: CORRECT. Expected ['EDIT'] and found [('EDIT', 'services/inference_host/server/continuous_batching/server.py')].\n", "534ef4a1-170a-41b2-b708-6f73096702e4: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/main-panel/apps/chat-webview-app.ts')].\n", "454825b8-1229-4d71-83ef-8204fb159b66: CORRECT. Expected ['EDIT'] and found [('EDIT', 'src/main.rs'), ('EDIT', 'src/handlers.rs'), ('EDIT', 'src/routes.rs'), ('EDIT', 'Cargo.toml'), (None, None), (None, None)].\n", "03bdb427-425c-45e5-bf9d-8f58ac46102a: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/chat/fuzzy-fs-searcher.ts')].\n", "aec2eb95-8125-4391-8416-71a158aa7126: CORRECT. MODEL DIDN'T GENERATE ANY CODEBLOCKS, WHICH IS OK.\n", "6f37a601-e2e0-487a-a9e9-df97e2d19029: WRONG. Expected ['EXCERPT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None), (None, None)].\n", "a8db74d0-ddbf-4a66-9937-90de1cec1fad: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/common/webviews/src/apps/diff-view/DiffView.svelte')].\n", "dfd83b6d-080c-4475-9378-b07fcabe6edf: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "8f8a8206-7582-45ae-ac56-8dc3aae801e6: CORRECT. Expected ['EDIT'] and found [('EDIT', 'base/prompt_format_chat/prompt_formatter.py')].\n", "9b1c5de0-54fe-460f-ac73-5bf3fde56276: WRONG. Expected ['EDIT'] and found [(None, None), (None, None)].\n", "246a59ae-f33c-4fd8-bce7-a2026d271568: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "3234c97f-8daa-4191-af8f-e06e7852cbbb: CORRECT. Expected ['EDIT'] and found [('EDIT', 'base/diff_utils/str_diff.py'), (None, None)].\n", "3789853e-2574-4268-bac4-bd83fb8614bd: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "0cce1311-8dce-4299-9122-ec6fab44fe0c: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "d0bf3c87-0615-4967-aad5-a32a21117cc4: CORRECT. Expected ['EDIT'] and found [('EDIT', 'Cargo.toml')].\n", "6e0032fa-d46b-4f05-a847-cf566d618002: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None)].\n", "da48504c-ca8f-4937-a1fe-e5d7ec347623: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None)].\n", "155158f0-f11a-4e41-a03a-8a76b6c6549b: WRONG. Expected ['EDIT'] and found [(None, None), (None, None)].\n", "82a97f3b-cd15-430c-97b5-3dcc58aa591e: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None)].\n", "198e2c30-ed03-4d5f-9ba3-a6a482f28377: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None)].\n", "3c515bb5-785b-44d1-96b8-ecd30b7484df: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "19c72d10-55d0-4e4e-8daf-210714c01665: WRONG. Expected ['EDIT'] and found [(None, None), (None, None)].\n", "d29afeae-2682-4a38-91d8-9359e8f35bca: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "fbaae246-3935-45c1-a913-a9ef767832d9: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "15750a81-56ed-4e56-8d93-308ecf15da9f: WRONG. Expected ['EXCERPT'] and found [(None, None), (None, None), (None, None)].\n", "649e3212-b926-4e39-8274-f2cc458b5a94: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "3671e50f-084f-492f-8d05-47a10961a77d: CORRECT. Expected [] and found [(None, None)].\n", "ea97d5c4-e3e6-4d5b-b905-b74c936319e0: WRONG. Expected ['EXCERPT'] and found [(None, None), (None, None), (None, None), (None, None)].\n", "Total correct: 11 out of 30\n"]}], "source": ["from base.prompt_format_chat.lib.token_counter import TokenCounter\n", "from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "\n", "\n", "CUSTOM_SYSTEM_PROMPT_WITH_XML_AFTERWARDS = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "MUST ALWAYS include metadata information in `<augment_codeblock>` tags after EVERY code snippets (codeblocks).\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "<augment_codeblock path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "</augment_codeblock>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "<augment_codeblock path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "</augment_codeblock>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "<augment_codeblock path=\"hello/world.rb\" mode=\"EDIT\">\n", "</augment_codeblock>\"\"\"\n", "\n", "\n", "def get_custom_system_prompt_formatter_xml_after(token_counter: TokenCounter) -> StringFormatter:\n", "    return StringFormatter(\n", "        CUSTOM_SYSTEM_PROMPT_WITH_XML_AFTERWARDS,\n", "        token_counter=token_counter,\n", "    )\n", "\n", "import re\n", "\n", "def transform_codeblock(text):\n", "    # Pattern for <augment_codeblock> after the code snippet\n", "    pattern = r'```([^\\n]*)\\n(.*?)\\n```\\s*<augment_codeblock\\s+path=\"(.*?)\"\\s+mode=\"(.*?)\">\\s*(?:</augment_codeblock>)?'\n", "\n", "    def replacement(match):\n", "        language = match.group(1)\n", "        code = match.group(2)\n", "        path = match.group(3)\n", "        mode = match.group(4)\n", "        return f'```{language} path={path} mode={mode}\\n{code}\\n```'\n", "\n", "    # Apply the transformation\n", "    return re.sub(pattern, replacement, text, flags=re.DOTALL)\n", "\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(TOKEN_COUNTER, TOKEN_APPORTIONMENT, get_custom_system_prompt_formatter_xml_after)\n", "\n", "\n", "total_correct = 0\n", "responses, raw_responses = {}, {}\n", "for request_id, expected_mode in SAMPLES.items():\n", "    if not isinstance(expected_mode, list):\n", "        expected_mode = [expected_mode]\n", "    chat_prompt_input = download_and_build_chat_prompt_input(request_id)\n", "    response, raw_response = run_claude(chat_prompt_input, prompt_formatter)\n", "    response = transform_codeblock(response)\n", "    current_correct, response = evaluate_response(request_id, response, expected_mode)\n", "    total_correct += current_correct\n", "    responses[request_id] = response\n", "    raw_responses[request_id] = raw_response\n", "\n", "print(f\"Total correct: {total_correct} out of {len(SAMPLES)}\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7e141471-0cfe-41c0-b735-0647fe181fb0: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'experimental/yury/inference/benchmark_inference_server_old.py')].\n", "c6e7f379-137f-4a3b-8abf-1b2aa8f93d31: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'services/inference_host/server/continuous_batching/server.py')].\n", "a16084cb-a0f3-4126-b9df-4730206a160a: CORRECT. Expected ['EDIT'] and found [('EDIT', 'services/inference_host/server/continuous_batching/server.py')].\n", "534ef4a1-170a-41b2-b708-6f73096702e4: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/main-panel/apps/chat-webview-app.ts')].\n", "454825b8-1229-4d71-83ef-8204fb159b66: CORRECT. Expected ['EDIT'] and found [('EDIT', 'main.rs')].\n", "03bdb427-425c-45e5-bf9d-8f58ac46102a: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/chat/fuzzy-fs-searcher.ts')].\n", "aec2eb95-8125-4391-8416-71a158aa7126: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "6f37a601-e2e0-487a-a9e9-df97e2d19029: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'clients/vscode/src/completions/pending-completion.ts')].\n", "a8db74d0-ddbf-4a66-9937-90de1cec1fad: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/common/webviews/src/apps/diff-view/DiffView.svelte')].\n", "dfd83b6d-080c-4475-9378-b07fcabe6edf: WRONG. Expected ['EDIT'] and found [(None, None)].\n", "8f8a8206-7582-45ae-ac56-8dc3aae801e6: CORRECT. Expected ['EDIT'] and found [('EDIT', 'base/prompt_format_chat/prompt_formatter.py')].\n", "9b1c5de0-54fe-460f-ac73-5bf3fde56276: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/common/webviews/src/apps/chat/Chat.svelte')].\n", "246a59ae-f33c-4fd8-bce7-a2026d271568: CORRECT. Expected ['EDIT'] and found [('EDIT', 'haystack/logging.py')].\n", "3234c97f-8daa-4191-af8f-e06e7852cbbb: CORRECT. Expected ['EDIT'] and found [('EDIT', 'base/diff_utils/str_diff.py')].\n", "3789853e-2574-4268-bac4-bd83fb8614bd: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/chat/fuzzy-fs-searcher.ts')].\n", "0cce1311-8dce-4299-9122-ec6fab44fe0c: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/chat/fuzzy-fs-searcher.ts')].\n", "d0bf3c87-0615-4967-aad5-a32a21117cc4: CORRECT. MODEL DIDN'T GENERATE ANY CODEBLOCKS, WHICH IS OK.\n", "85f03129-817c-45e8-9deb-e0a04e47d0f5: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/common/webviews/src/apps/diff-view/models/diff-view-model.ts'), ('EDIT', 'clients/common/webviews/src/apps/diff-view/DiffView.svelte')].\n", "6e0032fa-d46b-4f05-a847-cf566d618002: CORRECT. Expected ['EDIT'] and found [('EDIT', 'tools/genie/frontend/src/App.tsx')].\n", "da48504c-ca8f-4937-a1fe-e5d7ec347623: WRONG. Expected ['EDIT'] and found [(None, None), (None, None), (None, None), (None, None), (None, None)].\n", "155158f0-f11a-4e41-a03a-8a76b6c6549b: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/utils/editor.ts')].\n", "82a97f3b-cd15-430c-97b5-3dcc58aa591e: CORRECT. Expected ['EDIT'] and found [('EDIT', 'services/api_proxy/server/src/handlers.rs')].\n", "198e2c30-ed03-4d5f-9ba3-a6a482f28377: CORRECT. MODEL DIDN'T GENERATE ANY CODEBLOCKS, WHICH IS OK.\n", "3c515bb5-785b-44d1-96b8-ecd30b7484df: CORRECT. Expected ['EDIT'] and found [('EDIT', 'experimental/andre/intuit-demo/quickbooks-python-sdk/tests/test_utils.py')].\n", "19c72d10-55d0-4e4e-8daf-210714c01665: CORRECT. Expected ['EDIT'] and found [('EDIT', 'arrow-flight/src/sql/client.rs')].\n", "d29afeae-2682-4a38-91d8-9359e8f35bca: WRONG. Expected ['EXCERPT'] and found [(None, None)].\n", "fbaae246-3935-45c1-a913-a9ef767832d9: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'services/request_insight/annotation_exporter/annotation_exporter.py')].\n", "15750a81-56ed-4e56-8d93-308ecf15da9f: CORRECT. Expected ['EXCERPT'] and found [('EXCERPT', 'research/fastbackward/train.py')].\n", "649e3212-b926-4e39-8274-f2cc458b5a94: CORRECT. Expected ['EDIT'] and found [('EDIT', 'clients/vscode/src/completions/inline-provider.ts')].\n", "3671e50f-084f-492f-8d05-47a10961a77d: WRONG. Expected [] and found [('EDIT', 'clients/intellij/README.md')].\n", "Total correct: 25 out of 30\n"]}], "source": ["from base.prompt_format_chat.lib.token_counter import TokenCounter\n", "from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "from anthropic.types.text_block import TextBlock\n", "from anthropic.types.tool_use_block import ToolUseBlock\n", "\n", "\n", "CUSTOM_SYSTEM_PROMPT_TOOL_USAGE = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "MUST USE `show_excerpts` and `suggest_edits` tools instead of writing codeblocks or code snippets.\n", "\n", "1. Excerpts: When showing an excerpt from an existing file, MUST USE `show_excerpts` tool.\n", "2. Modifications: For any proposed edits, MUST USE `suggest_edits` tool.\n", "3. New: For any new code or text, MUST USE `suggest_edits` tool.\n", "\n", "When you are about to write a codeblock or a code snippet like\n", "\n", "```python\n", "print(\"hello world\")\n", "```\n", "\n", "then STOP IMMEDIATELY AND USE THE TOOLS INSTEAD. Remember, writing codeblocks directly leads to terrible user experience.\n", "\"\"\"\n", "\n", "\n", "def get_custom_system_prompt_formatter_tool(token_counter: TokenCounter) -> StringFormatter:\n", "    return StringFormatter(\n", "        CUSTOM_SYSTEM_PROMPT_TOOL_USAGE,\n", "        token_counter=token_counter,\n", "    )\n", "\n", "TOOLNAME_TO_MODE = {\n", "    \"show_excerpt\": \"EXCERPT\",\n", "    \"suggest_edits\": \"EDIT\",\n", "}\n", "\n", "\n", "def transform_response_with_tools(response):\n", "    result = []\n", "    for block in response.content:\n", "        if isinstance(block, TextBlock):\n", "            result.append(block.text + \"\\n\")\n", "        else:\n", "            result.append(\n", "                f\"```{block.input['language']} \"\n", "                f\"path={block.input['path']} \"\n", "                f\"mode={TOOLNAME_TO_MODE[block.name]}\\n\"\n", "                f\"{block.input['content']}\\n```\"\n", "            )\n", "    return \"\\n\".join(result)\n", "\n", "\n", "TOOLS = [\n", "    {\n", "        \"name\": \"show_excerpt\",\n", "        \"description\": \"Show specific excerpts from an existing file.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"path\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The full path of the file from which to display excerpts.\",\n", "                },\n", "                \"content\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The actual content (code) to be displayed.\",\n", "                },\n", "                \"language\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The programming language of the code excerpt (used as a language hint for Markdown code blocks).\",\n", "                },\n", "            },\n", "            \"required\": [\n", "                \"path\",\n", "                \"content\",\n", "                \"language\",\n", "            ],\n", "        },\n", "    },\n", "    {\n", "        \"name\": \"suggest_edits\",\n", "        \"description\": \"Propose edits and modifications to a part of the existing code.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"path\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The full path of the file to suggest edits for.\",\n", "                },\n", "                \"content\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The proposed new code or text.\",\n", "                },\n", "                \"language\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The programming language of the code (used as a language hint for Markdown code blocks).\",\n", "                },\n", "            },\n", "            \"required\": [\n", "                \"path\",\n", "                \"content\",\n", "                \"language\",\n", "            ],\n", "        },\n", "    },\n", "]\n", "\n", "\n", "PROMPT_FORMATTER = StructuredBinksPromptFormatter.create(TOKEN_COUNTER, TOKEN_APPORTIONMENT, get_custom_system_prompt_formatter_tool)\n", "\n", "\n", "total_correct = 0\n", "responses, raw_responses = {}, {}\n", "for request_id, expected_mode in SAMPLES.items():\n", "    if not isinstance(expected_mode, list):\n", "        expected_mode = [expected_mode]\n", "    chat_prompt_input = download_and_build_chat_prompt_input(request_id)\n", "    response, raw_response = run_claude(chat_prompt_input, PROMPT_FORMATTER, TOOLS)\n", "    response = transform_response_with_tools(raw_response)\n", "    current_correct, response = evaluate_response(request_id, response, expected_mode)\n", "    total_correct += current_correct\n", "    responses[request_id] = response\n", "    raw_responses[request_id] = raw_response\n", "\n", "print(f\"Total correct: {total_correct} out of {len(SAMPLES)}\")"]}, {"cell_type": "code", "execution_count": 259, "metadata": {}, "outputs": [], "source": ["request_id = \"dfd83b6d-080c-4475-9378-b07fcabe6edf\"\n", "chat_prompt_input = download_and_build_chat_prompt_input(request_id)\n", "prompt_output = PROMPT_FORMATTER.format_prompt(chat_prompt_input)\n", "# print(prompt_output.system_prompt)\n", "# print(prompt_output.chat_history[0].request_message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
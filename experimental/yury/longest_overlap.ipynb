{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import warnings\n", "import time"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[8, 4]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["MAX_LENGTH = 50000\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(MAX_LENGTH), np.int64)\n", "\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(5,), (4, 2), (2, 3)]\n"]}], "source": ["class LongestOverlapLM:\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, k, beam_size):    \n", "        assert beam_size > 0\n", "        if len(self.tokens) < k:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return []\n", "            \n", "        searchable_tokens = self.tokens[:-1]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return []\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue                \n", "                current_prediction = tuple(self.tokens[target_position + 1: min(target_position + k + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= beam_size:\n", "                    break\n", "            if len(predictions) >= beam_size:\n", "                break                \n", "        return predictions\n", "\n", "\n", "predictor = LongestOverlapLM()\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], k=2, beam_size=3))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# from https://docs.google.com/presentation/d/1V1XQ0IxEEPSekTvWW10KPR3Bg5BDRultJQGotCQd3mg/edit#slide=id.g237b45739c5_0_21\n", "LATENCY_A100 = {\n", "    1: 24.2,\n", "    2: 24.8,\n", "    4: 24.9,\n", "    8: 24.7,\n", "    16: 25,\n", "    20: 26.1,\n", "    24: 25.9,\n", "    28: 26,\n", "    32: 26.1,\n", "    64: 27.6,\n", "    96: 29,\n", "    128: 30.1,\n", "    160: 40.5,\n", "    192: 42,\n", "    224: 43,\n", "    256: 44,\n", "    384: 62,\n", "    512: 81.6,\n", "}\n", "\n", "# from https://docs.google.com/spreadsheets/d/1VGjlBbbKFmBEO4hAPxss1vAR_DxUC4YDkGOwm4fdhcw/edit#gid=1581007424\n", "LATENCY_H100 = {\n", "    1: 14.66,\n", "    16: 14.66,\n", "    128: 15.08,\n", "    256: 18.7,\n", "    512: 31.53,\n", "    1024: 59.66,\n", "}\n", "\n", "LATENCY_A100_KEYS = np.array(sorted(list(LATENCY_A100.keys())))\n", "LATENCY_H100_KEYS = np.array(sorted(list(LATENCY_H100.keys())))\n", "\n", "def estimate_latency_a100(n):\n", "    assert n > 0 and n <= 512\n", "    if n in LATENCY_A100:\n", "        return LATENCY_A100[n]\n", "    n_lower_index = np.searchsorted(LATENCY_A100_KEYS, n)\n", "    n_lower = LATENCY_A100_KEYS[n_lower_index - 1]\n", "    n_upper = LATENCY_A100_KEYS[n_lower_index]\n", "    return LATENCY_A100[n_lower] * (n_upper - n) / (n_upper - n_lower) + LATENCY_A100[n_upper] * (n - n_lower) / (n_upper - n_lower)\n", "\n", "\n", "def estimate_latency_h100(n):\n", "    return LATENCY_H100[LATENCY_H100_KEYS[np.searchsorted(LATENCY_H100_KEYS, 6, side='left')]]\n", "\n", "# LATENCY = {n: estimate_latency_a100(n) for n in range(1, 512)}\n", "LATENCY = {n: estimate_latency_h100(n) for n in range(1, 512)}"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1000 samples (had to process 1000 samples)\n"]}], "source": ["from research.retrieval.types import Chunk, Document\n", "import json\n", "from typing import Any, List, Dict\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "from research.retrieval import utils as rutils\n", "\n", "\n", "def prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix):\n", "    metadata = {\n", "        'num_prefix_chars_post_truncation': len(model_input['prefix']),\n", "        'num_suffix_chars_post_truncation': len(model_input['suffix']),\n", "    }\n", "    context = []\n", "    retrieved_chunks = model_input['retrieved_chunks']\n", "    if n_retrievals is not None:\n", "        retrieved_chunks = retrieved_chunks[:n_retrievals]\n", "    for chunk in reversed(retrieved_chunks):\n", "        context.extend(tokenizer.tokenize(chunk.text) + [tokenizer.eod_id])\n", "    if not remove_prefix_and_suffix:\n", "        context.extend(tokenizer.tokenize(model_input['suffix']))    \n", "        context.extend(tokenizer.tokenize(model_input['prefix']))\n", "    return context, metadata\n", "\n", "\n", "def generate_prompt(\n", "    prefix: str,\n", "    middle: str,\n", "    suffix: str,\n", "    middle_char_start: int,\n", "    middle_char_end: int,\n", "    file_path: str,\n", "    retrieved_chunk_str: str,\n", "    max_target_tokens: int,\n", "    n_retrievals,\n", "    remove_prefix_and_suffix=False,\n", ") -> List[int]:\n", "    \"\"\"Construct a token prompt.\"\"\"\n", "\n", "    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)\n", "\n", "    # Remove chunks that overlap with middle\n", "    filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(middle_char_start, middle_char_end),\n", "        retrieved_chunks,\n", "    )\n", "\n", "    model_input = dict(\n", "        prefix=prefix,\n", "        middle=middle,\n", "        suffix=suffix,\n", "        retrieved_chunks=filtered_chunks,\n", "        path=file_path,\n", "    )\n", "\n", "    # TODO(michiel) Add option for sampling different prompt styles\n", "    _, metadata = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    # Remove chunks that overlap with prefix or suffix\n", "    new_filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(\n", "            middle_char_start - metadata[\"num_prefix_chars_post_truncation\"],\n", "            middle_char_end,\n", "        ),\n", "        filtered_chunks,\n", "    )\n", "    if metadata[\"num_suffix_chars_post_truncation\"] > 0:\n", "        new_filtered_chunks = rutils.filter_overlap_chunks(\n", "            file_path,\n", "            rutils.Span(\n", "                middle_char_end,\n", "                middle_char_end + metadata[\"num_suffix_chars_post_truncation\"],\n", "            ),\n", "            new_filtered_chunks,\n", "        )\n", "\n", "    model_input['retrieved_chunks'] = new_filtered_chunks\n", "    prompt_tokens, _ = prepare_prompt(model_input, n_retrievals, remove_prefix_and_suffix)\n", "    \n", "    target_tokens = tokenizer.tokenize(middle + \"<|endoftext|>\")    \n", "    target_tokens = target_tokens[:max_target_tokens]\n", "\n", "    return prompt_tokens, target_tokens\n", "\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for row_index, datum in df.iterrows():\n", "            n_read_samples += 1\n", "            context, label = generate_prompt(\n", "                prefix=datum['prefix'],\n", "                middle=datum['middle'],\n", "                suffix=datum['suffix'],\n", "                middle_char_start=datum['middle_char_start'],\n", "                middle_char_end=datum['middle_char_end'],\n", "                file_path=datum['file_path'],\n", "                retrieved_chunk_str=datum['retrieved_chunks'],\n", "                max_target_tokens=max_tokens_to_predict,\n", "                n_retrievals=n_retrievals,\n", "                remove_prefix_and_suffix=remove_prefix_and_suffix)        \n", "\n", "            context = np.array(context)\n", "            label = np.array(label)\n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'prefix_suffix_len': len(tokenizer.tokenize(datum['prefix'] + datum['suffix'])),\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 1000, 256, None)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'context_length': 4284, 'label_length': 100, 'n_rounds': 67, 'latency': 1072, 'prediction_latency': 0.5196296807491418, 'beam_size': 0.8484848484848485, 'n_unique_first_token': 0.8484848484848485, 'n_unique_two_tokens': 0.8484848484848485}\n"]}], "source": ["def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample, n_sd, n_parallel_sd):\n", "    context = sample['context'].tolist()\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    beam_sizes, prediction_latency, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = min(n_sd, len(label) - index - 1)\n", "        actual_tokens_predicted = 0\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            beam_of_predictions = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict,\n", "                beam_size=n_parallel_sd)\n", "            prediction_latency.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                actual_tokens_predicted += len(predictions) + 1\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            index = furthest_index\n", "\n", "        # Make prediction with the main model\n", "        index += 1\n", "        if actual_tokens_predicted == 0:\n", "            # no paralle SD is used at this step\n", "            latency += LATENCY[1]\n", "        else:\n", "            latency += LATENCY[actual_tokens_predicted]\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "        'prediction_latency': safe_mean(prediction_latency),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "lm = LongestOverlapLM()\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[1], n_parallel_sd=1, n_sd=12))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PARALLEL SPECULATIVE DECODING BS=1 N=0\n", "context_length          4906.74400\n", "label_length              64.98700\n", "n_rounds                  64.98700\n", "latency                  952.70942\n", "prediction_latency         0.00000\n", "beam_size                  0.00000\n", "n_unique_first_token       0.00000\n", "n_unique_two_tokens        0.00000\n", "latency_per_token         14.66000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.188000\n", "latency                  427.896080\n", "prediction_latency         0.544031\n", "beam_size                  0.816559\n", "n_unique_first_token       0.816559\n", "n_unique_two_tokens        0.816559\n", "latency_per_token          7.538900\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.007000\n", "latency                  366.602620\n", "prediction_latency         0.866386\n", "beam_size                  3.881310\n", "n_unique_first_token       2.433548\n", "n_unique_two_tokens        2.991534\n", "latency_per_token          6.647174\n", "dtype: float64\n"]}], "source": ["def measure_latency_per_data_parallel_sd(lm, data, n_sd, n_parallel_sd):\n", "    df = []\n", "    for d in data:\n", "        df.append(measure_latency_per_sample_parallel_sd(lm=lm, sample=d, n_sd=n_sd, n_parallel_sd=n_parallel_sd))        \n", "    df = pd.DataFrame(df)\n", "    df['latency_per_token'] = df['latency'] / df['label_length'] \n", "    print('PARALLEL SPECULATIVE DECODING BS=%d N=%d' % (n_parallel_sd, n_sd))\n", "    print(df.mean())\n", "    return df\n", "\n", "lm = LongestOverlapLM()\n", "_ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_parallel_sd=1, n_sd=0)\n", "_ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_parallel_sd=1, n_sd=12)\n", "_ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_parallel_sd=6, n_sd=12)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PARALLEL SPECULATIVE DECODING BS=1 N=0\n", "context_length          4906.7440\n", "label_length              64.9870\n", "n_rounds                  64.9870\n", "latency                 1572.6854\n", "prediction_latency         0.0000\n", "beam_size                  0.0000\n", "n_unique_first_token       0.0000\n", "n_unique_two_tokens        0.0000\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  32.666000\n", "latency                  808.109250\n", "prediction_latency         0.544986\n", "beam_size                  0.828009\n", "n_unique_first_token       0.828009\n", "n_unique_two_tokens        0.828009\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=2 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  30.598000\n", "latency                  755.078125\n", "prediction_latency         0.856602\n", "beam_size                  1.541852\n", "n_unique_first_token       1.289961\n", "n_unique_two_tokens        1.423802\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=3 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.666000\n", "latency                  735.469150\n", "prediction_latency         0.886791\n", "beam_size                  2.190210\n", "n_unique_first_token       1.682318\n", "n_unique_two_tokens        1.948107\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=4 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.094000\n", "latency                  740.111225\n", "prediction_latency         0.908718\n", "beam_size                  2.790905\n", "n_unique_first_token       2.032083\n", "n_unique_two_tokens        2.431286\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=5 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.759000\n", "latency                  729.356025\n", "prediction_latency         0.917656\n", "beam_size                  3.350529\n", "n_unique_first_token       2.343917\n", "n_unique_two_tokens        2.872579\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.504000\n", "latency                  724.669488\n", "prediction_latency         0.928160\n", "beam_size                  3.873576\n", "n_unique_first_token       2.623729\n", "n_unique_two_tokens        3.279414\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=7 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.290000\n", "latency                  721.818178\n", "prediction_latency         0.927750\n", "beam_size                  4.366045\n", "n_unique_first_token       2.880522\n", "n_unique_two_tokens        3.659479\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=8 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.123000\n", "latency                  720.488109\n", "prediction_latency         0.931636\n", "beam_size                  4.835262\n", "n_unique_first_token       3.128054\n", "n_unique_two_tokens        4.016944\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=9 N=4\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.985000\n", "latency                  719.845372\n", "prediction_latency         0.933888\n", "beam_size                  5.288573\n", "n_unique_first_token       3.353214\n", "n_unique_two_tokens        4.348255\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  30.927000\n", "latency                  762.638900\n", "prediction_latency         0.541341\n", "beam_size                  0.823063\n", "n_unique_first_token       0.823063\n", "n_unique_two_tokens        0.823063\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=2 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.910000\n", "latency                  715.857150\n", "prediction_latency         0.837209\n", "beam_size                  1.537823\n", "n_unique_first_token       1.265126\n", "n_unique_two_tokens        1.390511\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=3 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.920000\n", "latency                  710.159625\n", "prediction_latency         0.871442\n", "beam_size                  2.190227\n", "n_unique_first_token       1.631554\n", "n_unique_two_tokens        1.880390\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=4 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.361000\n", "latency                  695.739812\n", "prediction_latency         0.891955\n", "beam_size                  2.797698\n", "n_unique_first_token       1.964059\n", "n_unique_two_tokens        2.336229\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=5 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.004000\n", "latency                  690.141916\n", "prediction_latency         0.900214\n", "beam_size                  3.366231\n", "n_unique_first_token       2.258063\n", "n_unique_two_tokens        2.751933\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.729000\n", "latency                  687.473453\n", "prediction_latency         0.892275\n", "beam_size                  3.896714\n", "n_unique_first_token       2.527241\n", "n_unique_two_tokens        3.137046\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=7 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.506000\n", "latency                  685.906122\n", "prediction_latency         0.904498\n", "beam_size                  4.396275\n", "n_unique_first_token       2.768012\n", "n_unique_two_tokens        3.490254\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=8 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.333000\n", "latency                  685.244853\n", "prediction_latency         0.913520\n", "beam_size                  4.874777\n", "n_unique_first_token       2.997429\n", "n_unique_two_tokens        3.817969\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=9 N=6\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.207000\n", "latency                  685.692153\n", "prediction_latency         0.910151\n", "beam_size                  5.333232\n", "n_unique_first_token       3.205234\n", "n_unique_two_tokens        4.120968\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  30.053000\n", "latency                  740.716838\n", "prediction_latency         0.547095\n", "beam_size                  0.820379\n", "n_unique_first_token       0.820379\n", "n_unique_two_tokens        0.820379\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=2 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  28.039000\n", "latency                  704.698175\n", "prediction_latency         0.824075\n", "beam_size                  1.532313\n", "n_unique_first_token       1.253913\n", "n_unique_two_tokens        1.375034\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=3 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.057000\n", "latency                  687.950262\n", "prediction_latency         0.850446\n", "beam_size                  2.183182\n", "n_unique_first_token       1.613338\n", "n_unique_two_tokens        1.853896\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=4 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.507000\n", "latency                  678.501441\n", "prediction_latency         0.866707\n", "beam_size                  2.788390\n", "n_unique_first_token       1.932824\n", "n_unique_two_tokens        2.290691\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=5 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.137000\n", "latency                  674.826347\n", "prediction_latency         0.873825\n", "beam_size                  3.355338\n", "n_unique_first_token       2.214042\n", "n_unique_two_tokens        2.687218\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.861000\n", "latency                  673.054387\n", "prediction_latency         0.895720\n", "beam_size                  3.883798\n", "n_unique_first_token       2.477395\n", "n_unique_two_tokens        3.059395\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=7 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.633000\n", "latency                  672.192819\n", "prediction_latency         0.890716\n", "beam_size                  4.381236\n", "n_unique_first_token       2.703572\n", "n_unique_two_tokens        3.400027\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=8 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.468000\n", "latency                  672.320412\n", "prediction_latency         0.892789\n", "beam_size                  4.857622\n", "n_unique_first_token       2.921000\n", "n_unique_two_tokens        3.716056\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=9 N=8\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.338000\n", "latency                  673.163269\n", "prediction_latency         0.892341\n", "beam_size                  5.320550\n", "n_unique_first_token       3.132429\n", "n_unique_two_tokens        4.016585\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.520000\n", "latency                  728.983700\n", "prediction_latency         0.549087\n", "beam_size                  0.818243\n", "n_unique_first_token       0.818243\n", "n_unique_two_tokens        0.818243\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=2 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.493000\n", "latency                  698.626775\n", "prediction_latency         0.827215\n", "beam_size                  1.528727\n", "n_unique_first_token       1.245602\n", "n_unique_two_tokens        1.362940\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=3 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.545000\n", "latency                  677.934222\n", "prediction_latency         0.840231\n", "beam_size                  2.182148\n", "n_unique_first_token       1.596901\n", "n_unique_two_tokens        1.830556\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=4 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.986000\n", "latency                  670.884647\n", "prediction_latency         0.874494\n", "beam_size                  2.788403\n", "n_unique_first_token       1.913252\n", "n_unique_two_tokens        2.262166\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=5 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.610000\n", "latency                  668.020569\n", "prediction_latency         0.887474\n", "beam_size                  3.354724\n", "n_unique_first_token       2.193253\n", "n_unique_two_tokens        2.654347\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.323000\n", "latency                  666.773228\n", "prediction_latency         0.900820\n", "beam_size                  3.889916\n", "n_unique_first_token       2.451379\n", "n_unique_two_tokens        3.022199\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=7 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.100000\n", "latency                  666.522491\n", "prediction_latency         0.907019\n", "beam_size                  4.389897\n", "n_unique_first_token       2.672985\n", "n_unique_two_tokens        3.352237\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=8 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  24.928000\n", "latency                  667.161519\n", "prediction_latency         0.896366\n", "beam_size                  4.870141\n", "n_unique_first_token       2.886379\n", "n_unique_two_tokens        3.659408\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=9 N=10\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  24.795000\n", "latency                  668.410506\n", "prediction_latency         0.901470\n", "beam_size                  5.334610\n", "n_unique_first_token       3.093040\n", "n_unique_two_tokens        3.955520\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=1 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  29.188000\n", "latency                  722.106987\n", "prediction_latency         0.557853\n", "beam_size                  0.816559\n", "n_unique_first_token       0.816559\n", "n_unique_two_tokens        0.816559\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=2 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  27.165000\n", "latency                  689.954300\n", "prediction_latency         0.842934\n", "beam_size                  1.525594\n", "n_unique_first_token       1.239432\n", "n_unique_two_tokens        1.355291\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=3 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  26.218000\n", "latency                  673.350094\n", "prediction_latency         0.856922\n", "beam_size                  2.176895\n", "n_unique_first_token       1.588347\n", "n_unique_two_tokens        1.816668\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=4 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.672000\n", "latency                  667.772019\n", "prediction_latency         0.879145\n", "beam_size                  2.782544\n", "n_unique_first_token       1.901279\n", "n_unique_two_tokens        2.243449\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=5 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.287000\n", "latency                  665.581341\n", "prediction_latency         0.886585\n", "beam_size                  3.348626\n", "n_unique_first_token       2.178060\n", "n_unique_two_tokens        2.630440\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=6 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  25.007000\n", "latency                  665.050162\n", "prediction_latency         0.892916\n", "beam_size                  3.881310\n", "n_unique_first_token       2.433548\n", "n_unique_two_tokens        2.991534\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=7 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  24.788000\n", "latency                  665.669328\n", "prediction_latency         0.893458\n", "beam_size                  4.382281\n", "n_unique_first_token       2.657066\n", "n_unique_two_tokens        3.323097\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=8 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  24.609000\n", "latency                  666.203300\n", "prediction_latency         0.901783\n", "beam_size                  4.861549\n", "n_unique_first_token       2.871479\n", "n_unique_two_tokens        3.628808\n", "dtype: float64\n", "PARALLEL SPECULATIVE DECODING BS=9 N=12\n", "context_length          4906.744000\n", "label_length              64.987000\n", "n_rounds                  24.474000\n", "latency                  667.183291\n", "prediction_latency         0.893658\n", "beam_size                  5.323069\n", "n_unique_first_token       3.071157\n", "n_unique_two_tokens        3.917126\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM()\n", "_ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_parallel_sd=1, n_sd=0)\n", "\n", "for n_sd in [4, 6, 8, 10, 12]:\n", "    for n_parallel_sd in [1, 2, 3, 4, 5, 6, 7, 8, 9]:\n", "        _ = measure_latency_per_data_parallel_sd(lm, data_fim_retrieval, n_sd=n_sd, n_parallel_sd=n_parallel_sd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
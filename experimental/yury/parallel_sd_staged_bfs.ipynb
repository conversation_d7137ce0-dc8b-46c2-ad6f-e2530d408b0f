{"cells": [{"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["([((4, 2), 2), ((2, 3), 1)], 0)\n", "([(5,), (4, 2), (2, 3)], 0)\n", "([(5,), (4,), (2,)], 0)\n"]}], "source": ["import warnings\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(100000))\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])\n", "\n", "\n", "\n", "class LongestOverlapLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd, allow_predicting_less_than_k_tokens=False, n_predictions_per_overlap=None):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "        self.allow_predicting_less_than_k_tokens = allow_predicting_less_than_k_tokens\n", "        self.n_predictions_per_overlap = n_predictions_per_overlap\n", "        if self.n_predictions_per_overlap:\n", "            assert self.allow_predicting_less_than_k_tokens\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, return_overlap=False):        \n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        n_sd = min(n_sd, len(self.tokens) - 1)\n", "        \n", "        if n_sd == 0:\n", "            return (), 0\n", "        \n", "        assert self.n_parallel_sd > 0\n", "        if len(self.tokens) < n_sd:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return (), 0\n", "        \n", "        if self.allow_predicting_less_than_k_tokens:\n", "            searchable_tokens = self.tokens[:-1]\n", "        else:\n", "            searchable_tokens = self.tokens[:-n_sd]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return (), 0\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue           \n", "                if self.n_predictions_per_overlap is not None:     \n", "                    assert l > 0\n", "                    if l < len(self.n_predictions_per_overlap):\n", "                        current_n_sd = self.n_predictions_per_overlap[l]\n", "                    else:\n", "                        current_n_sd = self.n_predictions_per_overlap[0]\n", "                    current_n_sd = min(current_n_sd, n_sd)\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + current_n_sd + 1, len(self.tokens))])\n", "                else:\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + n_sd + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if not self.allow_predicting_less_than_k_tokens:\n", "                    assert len(current_prediction) == n_sd\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                if return_overlap:\n", "                    predictions.append((current_prediction, l))\n", "                else:\n", "                    predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= self.n_parallel_sd:\n", "                    break\n", "            if len(predictions) >= self.n_parallel_sd:\n", "                break                \n", "        return predictions, 0\n", "\n", "\n", "predictor = LongestOverlapLM(2, 3)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2, True))\n", "\n", "predictor = LongestOverlapLM(2, 3, True)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))\n", "\n", "predictor = LongestOverlapLM(2, 3, True, n_predictions_per_overlap=[1])\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BUDGET (2,) SELECTED ({()}, True)\n", "BUDGET (2,) SELECTED ({(1,)}, False)\n", "BUDGET (2,) SELECTED ({(2,), (1,)}, True)\n", "BUDGET (2,) SELECTED ({(2, 5), (1,)}, True)\n", "BUDGET (2,) SELECTED ({(2, 5), (1, 3)}, True)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5)}, True)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5)}, True)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5)}, True)\n"]}], "source": ["import numpy as np\n", "from typing import List, Optional, Union\n", "import heapq\n", "import scipy.special\n", "\n", "\n", "TRIE_NODE_EOS_TOKEN_ID = 0\n", "\n", "\n", "class TrieNode:\n", "\n", "    def __init__(self, token_id: Optional[int], logprob: float, parent):\n", "        self.token_id = token_id        \n", "        self.logprob = logprob\n", "        self.parent = parent\n", "        self.children = dict()\n", "        if self.parent is not None:\n", "            self.all_tokens = self.parent.all_tokens + (self.token_id,)\n", "            self.all_logprobs = self.parent.all_logprobs + (self.logprob,)\n", "            self.sumlogprob = np.sum(self.all_logprobs)\n", "        else:\n", "            self.all_tokens = tuple()\n", "            self.all_logprobs = tuple()\n", "            self.sumlogprob = 0\n", "    \n", "    def __repr__(self):\n", "        return str(self.all_tokens)\n", "\n", "    def has_finished(self):\n", "        return self.token_id == TRIE_NODE_EOS_TOKEN_ID\n", "    \n", "    def is_leaf(self):\n", "        return len(self.children) == 0\n", "    \n", "    def estimate_next_token_will_be_correct(self):\n", "        if self.has_finished():\n", "            next_token_logprob = 0.0\n", "        else:\n", "            next_token_logprob = self.logprob or 0.0\n", "        return np.exp(next_token_logprob + self.sumlogprob)\n", "    \n", "    def maybe_add_child(self, token_id: int, logprob: float):\n", "        if token_id in self.children:\n", "            if np.abs(self.children[token_id].logprob - logprob) > 1e-0:\n", "                print('MISMATCH', np.abs(self.children[token_id].logprob - logprob), 'when adding', token_id, 'to', self)\n", "                raise ValueError()\n", "        else:\n", "            new_node = TrieNode(token_id, logprob, parent=self)\n", "            self.children[token_id] = new_node\n", "        return self.children[token_id]\n", "    \n", "    def get_total_nodes(self):\n", "        return sum(child.get_total_nodes() for child in self.children.values()) + 1\n", "\n", "# root0 \n", "# |     \\\n", "# A1     B2\n", "# |  \\    |  \\\n", "# C3  D4  E5  F6\n", "# |\n", "# G7\n", "\n", "root = TrieNode(None, 0.0, None)\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "b = root.maybe_add_child(2, np.log(0.39))\n", "c = a.maybe_add_child(3, np.log(0.5001))\n", "d = a.maybe_add_child(4, np.log(0.4999))\n", "e = b.maybe_add_child(5, np.log(0.9))\n", "f = b.maybe_add_child(6, np.log(0.1))\n", "g = c.maybe_add_child(7, np.log(0.99))\n", "\n", "\n", "def get_nodes_under_budget(root, n_sd: int, tokens_budget: int, target_prob: float):\n", "    assert tokens_budget > 0\n", "    assert target_prob > 0\n", "    \n", "    tokens_selected = 0    \n", "    leaves_selected = set()\n", "    Q, next_Q = [(0, 0, root)], []\n", "    current_length = 0\n", "    current_prob = 0\n", "\n", "    while len(Q) > 0 and tokens_selected < tokens_budget:\n", "        minus_cumlogprob, _, node = heapq.heappop(Q)\n", "\n", "        if node.has_finished():\n", "            assert len(node.all_tokens) <= current_length\n", "        else:\n", "            assert len(node.all_tokens) == current_length, len(node.all_tokens)\n", "\n", "        tokens_selected += 1\n", "        current_prob += np.exp(-minus_cumlogprob)\n", "\n", "        # Remove parent\n", "        if node.parent in leaves_selected:\n", "            leaves_selected.remove(node.parent)        \n", "        leaves_selected.add(node)\n", "\n", "        # Expand children only if this is not the end of the generation.\n", "        if not node.has_finished():            \n", "            for child in node.children.values():\n", "                if len(child.all_tokens) <= n_sd:\n", "                    heapq.heappush(next_Q, (-child.sumlogprob, np.random.random(), child))\n", "        else:\n", "            heapq.heappush(next_Q, (-node.sumlogprob, np.random.random(), node))\n", "\n", "        if current_prob >= target_prob:\n", "            Q = next_Q\n", "            next_Q = []\n", "            current_length += 1\n", "            current_prob = 0\n", "\n", "    current_length -= 1\n", "\n", "    estimated_prob_after_round = 0\n", "    for node in leaves_selected:\n", "        if len(node.all_tokens) == current_length + 1:\n", "            estimated_prob_after_round += np.exp(node.sumlogprob)\n", "        elif len(node.all_tokens) == current_length:\n", "            estimated_prob_after_round += np.exp(node.estimate_next_token_will_be_correct())\n", "        else:\n", "            assert node.has_finished()\n", "            estimated_prob_after_round += np.exp(node.sumlogprob)\n", "\n", "    should_do_this_round = estimated_prob_after_round >= target_prob\n", "    return leaves_selected, should_do_this_round\n", "\n", "# BUDGET 1 SELECTED ({()}, 0)\n", "# BUDGET 2 SELECTED ({(1,)}, 0.4)\n", "# BUDGET 3 SELECTED ({(1,), (2,)}, 0.79)\n", "# BUDGET 4 SELECTED ({(1,), (2, 5)}, 1.141)\n", "# BUDGET 5 SELECTED ({(2, 5), (1, 3)}, 1.34104)\n", "# BUDGET 6 SELECTED ({(2, 5), (1, 3), (1, 4)}, 1.541)\n", "# BUDGET 7 SELECTED ({(1, 3, 7), (2, 5), (1, 4)}, 1.7390396)\n", "# BUDGET 8 SELECTED ({(1, 3, 7), (2, 6), (2, 5), (1, 4)}, 1.7780395999999998)\n", "for budget in [1, 2, 3, 4, 5, 6, 7, 8]:\n", "    print('BUDGET', b, 'SELECTED', get_nodes_under_budget(root, 3, budget, 0.5))"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.19605920400000004\n", "0.3159\n", "LIST OF PREDICTIONS\n", "(2, 5)\n", "(1, 3, 7)\n", "LIST OF PREDICTIONS EXPANDED\n", "(2, 5, 6, 7)\n", "(1, 3, 7, 9)\n"]}], "source": ["nodes, estimate_next_token_will_be_correct = get_nodes_under_budget(root, 3, 7, 0.5)\n", "\n", "# compute current estimate on the number of correct tokens\n", "for node in nodes:\n", "    print(node.estimate_next_token_will_be_correct())\n", "\n", "\n", "def nodes_to_candidates(nodes):\n", "    candidates = sorted([(-node.estimate_next_token_will_be_correct(), node.all_tokens) for node in nodes])\n", "    return [x[1] for x in candidates]\n", "\n", "\n", "# generate list of predictions and expand each with LongestOverlapLM\n", "candidates = nodes_to_candidates(nodes)\n", "print('LIST OF PREDICTIONS')\n", "for candidate in candidates:\n", "    print(candidate)\n", "\n", "\n", "def expand_with_draft_model_predictions(lm, context, candidates, n_sd, total_budget):\n", "    expanded_candidates = []\n", "    current_budget = total_budget - sum(len(c) for c in candidates)\n", "    for candidate in candidates:\n", "        if current_budget > 0:\n", "            extra_predictions, _ = lm.predict_next_k_tokens(context + list(candidate), min(current_budget, n_sd - len(candidate)))\n", "            if len(extra_predictions) == 1:\n", "                extra_predictions = extra_predictions[0]\n", "            else:\n", "                assert len(extra_predictions) == 0            \n", "            assert current_budget >= len(extra_predictions)\n", "            current_budget -= len(extra_predictions)\n", "            expanded_candidates.append(candidate + extra_predictions)\n", "        else:\n", "            expanded_candidates.append(candidate)\n", "    return expanded_candidates\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "lm.fit([1, 2, 5, 6, 7, 9])\n", "candidates = expand_with_draft_model_predictions(lm, [5, 6], candidates, 4, 1000)\n", "print('LIST OF PREDICTIONS EXPANDED')\n", "for candidate in candidates:\n", "    print(candidate)"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "Socket error: [Errno 98] Address already in use; Port 6001 is in use on 0.0.0.0. Checking 6002...\n", "Socket error: [Errno 98] Address already in use; Port 6002 is in use on 0.0.0.0. Checking 6003...\n", "torch distributed is already initialized, skipping initialization ...\n", "_initialize_distributed() model parallel is already initialized\n", "> setting random seeds to 1234 ...\n", "[2023-10-03 18:35:03,340] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n", " > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-03 18:35:03,609] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-10-03 18:35:03,785] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-03 18:35:03,786] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-03 18:35:03,825] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-03 18:35:03,825] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-03 18:35:03,826] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-03 18:35:03,826] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-03 18:35:03,827] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-10-03 18:35:03,828] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-03 18:35:03,828] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-03 18:35:03,829] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-03 18:35:03,830] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-03 18:35:03,830] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-03 18:35:03,831] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-03 18:35:03,831] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-03 18:35:03,832] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-03 18:35:03,833] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-03 18:35:03,833] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-03 18:35:03,834] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-03 18:35:03,835] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-03 18:35:03,836] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-03 18:35:03,836] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-03 18:35:03,837] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-03 18:35:03,838] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-03 18:35:03,838] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-03 18:35:03,839] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-03 18:35:03,839] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-03 18:35:03,840] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-03 18:35:03,840] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-03 18:35:03,841] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-03 18:35:03,841] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-03 18:35:03,842] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-03 18:35:03,842] [INFO] [config.py:763:print]   prescale_gradients ........... <PERSON>alse\n", "[2023-10-03 18:35:03,843] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-03 18:35:03,843] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-03 18:35:03,845] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-03 18:35:03,846] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-03 18:35:03,847] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-03 18:35:03,847] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-03 18:35:03,848] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-03 18:35:03,848] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-03 18:35:03,848] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-03 18:35:03,849] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-03 18:35:03,849] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-03 18:35:03,850] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-03 18:35:03,850] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-03 18:35:03,851] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-03 18:35:03,851] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-03 18:35:03,852] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-03 18:35:03,854] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n", "Time to load utils op: 0.00040221214294433594 seconds\n", "[2023-10-03 18:35:03,856] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-10-03 18:35:03,891] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-10-03 18:35:03,904] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "No modifications detected for re-loaded extension module utils, skipping build step...\n", "Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-03 18:35:04,074] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-10-03 18:35:04,139] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-10-03 18:35:04,194] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-10-03 18:35:04,249] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-10-03 18:35:04,296] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-10-03 18:35:04,351] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-10-03 18:35:04,403] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-10-03 18:35:04,454] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-10-03 18:35:04,503] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-10-03 18:35:04,577] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-10-03 18:35:04,638] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-10-03 18:35:04,711] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-10-03 18:35:04,773] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-10-03 18:35:04,828] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-10-03 18:35:04,886] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-10-03 18:35:04,938] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-10-03 18:35:04,990] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-10-03 18:35:05,033] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-10-03 18:35:05,075] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-10-03 18:35:05,129] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-10-03 18:35:05,194] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-10-03 18:35:05,267] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-10-03 18:35:05,333] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-10-03 18:35:05,422] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-10-03 18:35:05,499] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-10-03 18:35:05,500] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-10-03 18:35:05,617] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["# Based on this https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e\n", "model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7f0c9c7bbeb0>\n"]}], "source": ["# allow '<|endoftext|>'\n", "ALL_SPECIAL_TOKENS_IDS = [model.tokenizer.tokenize(token)[0] for token in model.tokenizer.all_special_tokens() if token != '<|endoftext|>']\n", "ALL_SPECIAL_TOKENS_IDS_SET = set(ALL_SPECIAL_TOKENS_IDS)\n", "\n", "\n", "EOS_TOKEN_ID = model.tokenizer.tokenize('<|endoftext|>')[0]\n", "EOS_TOKEN_ID\n", "\n", "print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 100 samples (had to process 100 samples)\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "import json\n", "from research.models.meta_model import GenerationOptions\n", "from typing import Any, List, Dict\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for _, datum in df.iterrows():\n", "            n_read_samples += 1\n", "\n", "            context = ModelInput(\n", "                prefix=datum['prefix'],\n", "                suffix=datum['suffix'],\n", "                middle='',\n", "                retrieved_chunks=deserialize_retrieved_chunks(datum['retrieved_chunks']),\n", "                path=datum['file_path'],\n", "                target=None,\n", "            )\n", "            context, _ = model.prompt_formatter.prepare_prompt(context)\n", "\n", "            label = model.prompt_formatter.tokenizer.tokenize(datum['middle'] + \"<|endoftext|>\")    \n", "            label = label[:max_tokens_to_predict]\n", "            for token in label:\n", "                assert token not in ALL_SPECIAL_TOKENS_IDS_SET, (token, model.tokenizer.detokenize([token]))\n", "            label = np.array(label)            \n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 100, 32, None)"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 2\n", "PREDICITONS\n", "=>  Transformer<|endoftext|>\n"]}], "source": ["import gc\n", "import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "\n", "\n", "class NeuralLM:\n", "    \n", "    def __init__(self, tokens_budget, target_prob, draft_model=None, draft_tokens_budget=None):\n", "        self.tokens_budget = tokens_budget\n", "        self.target_prob = target_prob\n", "        self.draft_model = draft_model        \n", "        if self.draft_model is not None:\n", "            assert draft_tokens_budget is not None\n", "            self.draft_tokens_budget = draft_tokens_budget\n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def _get_final_predictions(self, context, n_sd, root):\n", "        nodes, _ = get_nodes_under_budget(root, n_sd, self.tokens_budget, self.target_prob)\n", "        candidates = nodes_to_candidates(nodes)        \n", "        if self.draft_model is not None:            \n", "            candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.tokens_budget + self.draft_tokens_budget)\n", "        return candidates\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        root = TrieNode(None, None, None)\n", "        \n", "        n_rounds = 0\n", "\n", "        while True:\n", "            nodes, should_do_this_round = get_nodes_under_budget(root, n_sd - 1, self.tokens_budget, self.target_prob)\n", "\n", "            if not should_do_this_round:\n", "                # print('stop #0')\n", "                break\n", "\n", "            nodes = [node for node in nodes if not node.has_finished()]\n", "            if len(nodes) == 0:\n", "                # print('stop #1')\n", "                break\n", "\n", "            nodes = [node for node in nodes if node.is_leaf()]\n", "            if len(nodes) == 0:\n", "                # print('stop #2')\n", "                break\n", "\n", "            candidates = nodes_to_candidates(nodes)\n", "            if self.draft_model is not None:\n", "                candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.tokens_budget + self.draft_tokens_budget)\n", "\n", "            # print('CANDIDATES', n_rounds)\n", "            # for candidate in candidates:\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "            candidates = [list(context + list(x)) for x in candidates]            \n", "            n_rounds += 1            \n", "            for candidate in candidates:\n", "                with torch.no_grad():\n", "                    context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([candidate], dtype=torch.int64))                \n", "                    model_inputs = (\n", "                        context_tokens_tensor,  # input_ids\n", "                        position_ids,  # position_ids\n", "                        attention_mask,  # attention_mask\n", "                    )            \n", "                    \n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "                    logits = text_gen_utils.forward_model(\n", "                        model=model.neox_model,\n", "                        model_inputs=model_inputs,\n", "                        is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                        pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                        timers=None,\n", "                    )\n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "                    logits[:, :, ALL_SPECIAL_TOKENS_IDS] = -10000                                    \n", "                    assert logits.shape[0] == 1, logits.shape\n", "                    logits = logits[0]\n", "                    logprobs = F.log_softmax(logits, dim=-1)\n", "                    top_predictions = torch.topk(logprobs, self.tokens_budget, dim=-1, sorted=True)                \n", "\n", "                logprobs = logprobs.float().cpu().numpy()\n", "                top_prediction_values = top_predictions.values.float().cpu().numpy()\n", "                top_prediction_indices = top_predictions.indices.cpu().numpy()\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "                for index in range(len(context) - 1, len(candidate)): \n", "                    if index == len(context) - 1:\n", "                        node = root\n", "                    else:\n", "                        token_id = candidate[index]\n", "                        node = node.maybe_add_child(token_id, logprobs[index - 1, token_id])\n", "                    # add new token from the input\n", "                    for token_id, logprob in zip(top_prediction_indices[index], top_prediction_values[index]):\n", "                        # print('adding #2', token_id, 'to', node, 'with logprob', logprob)\n", "                        _ = node.maybe_add_child(token_id.item(), logprob)\n", "                \n", "            # print('ROUND', n_rounds)\n", "            # for candidate in self._get_final_predictions(context, n_sd, root):\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "        candidates = self._get_final_predictions(context, n_sd, root)\n", "        for candidate in candidates:\n", "            assert len(candidate) <= n_sd\n", "        return candidates, n_rounds\n", "\n", "\n", "sample = data_fim_retrieval[2]\n", "\n", "# lm = NeuralLM(tokens_budget=16, target_prob=0.8)\n", "# predictions, cost = lm.predict_next_k_tokens(sample['context'], 20)\n", "# print('COST', cost)\n", "# print('PREDICITONS')\n", "# for p in predictions:\n", "#     print('=>', tokenizer.detok<PERSON>ze(p))\n", "\n", "lm = NeuralLM(tokens_budget=20, target_prob=0.9, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "lm.fit(sample['context'])\n", "predictions, cost = lm.predict_next_k_tokens(sample['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITONS')\n", "for p in predictions:\n", "    print('=>', tokenizer.detok<PERSON>ze(p))"]}, {"cell_type": "code", "execution_count": 190, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "{'context_length': 3726, 'label_length': 12, 'n_rounds': 1, 'n_draft_rounds': 4, 'average_draft_rounds': 4.0, 'average_draft_tokens_accepted': 11.0, 'simulation_time': 3911.4537239074707, 'beam_size': 1.0, 'n_unique_first_token': 1.0, 'n_unique_two_tokens': 1.0}\n"]}], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample):\n", "    context = sample['context']\n", "    if not isinstance(context, list):\n", "        context = context.tolist()\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, n_draft_rounds = 0, 0\n", "    index = 0\n", "    beam_sizes, simulation_time, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    draft_tokens_accepted = []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 1\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            beam_of_predictions, current_draft_rounds = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict)\n", "            n_draft_rounds += current_draft_rounds\n", "\n", "            simulation_time.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "\n", "            if index == 0:\n", "                print(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            draft_tokens_accepted.append(furthest_index - index)\n", "            index = furthest_index\n", "        # Make prediction with the main model\n", "        index += 1\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'n_draft_rounds': n_draft_rounds,\n", "        'average_draft_rounds': n_draft_rounds / n_rounds,\n", "        'average_draft_tokens_accepted': safe_mean(draft_tokens_accepted),\n", "        'simulation_time': safe_mean(simulation_time),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "\n", "lm = NeuralLM(tokens_budget=16, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0]))"]}, {"cell_type": "code", "execution_count": 191, "metadata": {}, "outputs": [], "source": ["import os \n", "\n", "\n", "BASE_DIR = '/mnt/efs/augment/user/yury/parallel_sd_staged_32label_bfs_v2'\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data, main_round_cost, draft_round_cost, exp_name=None):\n", "    if exp_name is not None:\n", "        path = os.path.join(BASE_DIR, exp_name)\n", "        if os.path.isfile(path):\n", "            print('EXPEREIMENT', exp_name, 'CACHED')\n", "            df = pd.read_csv(path)\n", "            print(df.mean())\n", "            return df                    \n", "\n", "    pbar = tqdm(data, total=len(data))\n", "    df = None\n", "    for d in pbar:\n", "        datum = measure_latency_per_sample_parallel_sd(lm=lm, sample=d)\n", "        datum['latency'] = main_round_cost * datum['n_rounds'] + draft_round_cost * datum['n_draft_rounds']\n", "        datum['latency_per_token'] = datum['latency'] / datum['label_length']        \n", "        if df is None:\n", "            df = pd.DataFrame([datum])\n", "        else:\n", "            df = pd.concat([df, pd.DataFrame([datum])], ignore_index=True)\n", "        pbar.set_description('Latency per token: ' + ' '.join(['%s: %.2f' % (k, v) for k, v in df['latency_per_token'].aggregate([q50, q80, q90, q95, q99]).to_dict().items()]))\n", "\n", "    if exp_name is not None:\n", "        df.to_csv(path, index=False)\n", "        print(df.mean())\n", "    return df"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "75431ee1951642eeb5f8001f60f919ec", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Exception ignored in: <function tqdm.__del__ at 0x7f0f232f8d30>\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/tqdm/std.py\", line 1144, in __del__\n", "    def __del__(self):\n", "  File \"_pydevd_bundle/pydevd_cython.pyx\", line 1457, in _pydevd_bundle.pydevd_cython.SafeCallWrapper.__call__\n", "  File \"_pydevd_bundle/pydevd_cython.pyx\", line 1758, in _pydevd_bundle.pydevd_cython.ThreadTracer.__call__\n", "  File \"/opt/conda/lib/python3.9/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_is_thread_alive.py\", line 9, in is_thread_alive\n", "    def is_thread_alive(t):\n", "KeyboardInterrupt: \n"]}, {"name": "stdout", "output_type": "stream", "text": ["1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 11\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m lm \u001b[39m=\u001b[39m NeuralLM(tokens_budget\u001b[39m=\u001b[39m\u001b[39m16\u001b[39m, target_prob\u001b[39m=\u001b[39m\u001b[39m0.8\u001b[39m, draft_model\u001b[39m=\u001b[39mLongestOverlapLM(\u001b[39m12\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39mTrue\u001b[39;00m), draft_tokens_budget\u001b[39m=\u001b[39m\u001b[39m36\u001b[39m)\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m df \u001b[39m=\u001b[39m measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:\u001b[39m40\u001b[39;49m], \u001b[39m9.5\u001b[39;49m, \u001b[39m3.0\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mdf_parallel_staged_neural_1b_40_budget16_tp0.8_redo.csv\u001b[39;49m\u001b[39m'\u001b[39;49m)\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 11\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=32'>33</a>\u001b[0m df \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=33'>34</a>\u001b[0m \u001b[39mfor\u001b[39;00m d \u001b[39min\u001b[39;00m pbar:\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=34'>35</a>\u001b[0m     datum \u001b[39m=\u001b[39m measure_latency_per_sample_parallel_sd(lm\u001b[39m=\u001b[39;49mlm, sample\u001b[39m=\u001b[39;49md)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=35'>36</a>\u001b[0m     datum[\u001b[39m'\u001b[39m\u001b[39mlatency\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m=\u001b[39m main_round_cost \u001b[39m*\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mn_rounds\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m+\u001b[39m draft_round_cost \u001b[39m*\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mn_draft_rounds\u001b[39m\u001b[39m'\u001b[39m]\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=36'>37</a>\u001b[0m     datum[\u001b[39m'\u001b[39m\u001b[39mlatency_per_token\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m=\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mlatency\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m/\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mlabel_length\u001b[39m\u001b[39m'\u001b[39m]        \n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 11\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m lm\u001b[39m.\u001b[39mfit(context \u001b[39m+\u001b[39m label[:index])\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m start_time \u001b[39m=\u001b[39m time\u001b[39m.\u001b[39mtime()\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m beam_of_predictions, current_draft_rounds \u001b[39m=\u001b[39m lm\u001b[39m.\u001b[39;49mpredict_next_k_tokens(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     context \u001b[39m+\u001b[39;49m label[:index],\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m     max_tokens_to_predict)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m n_draft_rounds \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m current_draft_rounds\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m simulation_time\u001b[39m.\u001b[39mappend(\u001b[39m1000\u001b[39m \u001b[39m*\u001b[39m (time\u001b[39m.\u001b[39mtime() \u001b[39m-\u001b[39m start_time))\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 11\u001b[0m line \u001b[0;36m9\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=87'>88</a>\u001b[0m top_prediction_values \u001b[39m=\u001b[39m top_predictions\u001b[39m.\u001b[39mvalues\u001b[39m.\u001b[39mfloat()\u001b[39m.\u001b[39mcpu()\u001b[39m.\u001b[39mnumpy()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=88'>89</a>\u001b[0m top_prediction_indices \u001b[39m=\u001b[39m top_predictions\u001b[39m.\u001b[39mindices\u001b[39m.\u001b[39mcpu()\u001b[39m.\u001b[39mnumpy()\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=89'>90</a>\u001b[0m gc\u001b[39m.\u001b[39;49mcollect()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=90'>91</a>\u001b[0m torch\u001b[39m.\u001b[39mcuda\u001b[39m.\u001b[39mempty_cache()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#X65sdnNjb2RlLXJlbW90ZQ%3D%3D?line=92'>93</a>\u001b[0m \u001b[39mfor\u001b[39;00m index \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39mlen\u001b[39m(context) \u001b[39m-\u001b[39m \u001b[39m1\u001b[39m, \u001b[39mlen\u001b[39m(candidate)): \n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["lm = NeuralLM(tokens_budget=16, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:40], 9.5, 3.0, 'df_parallel_staged_neural_1b_40_budget16_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e97e729422814b10a0f424020fa496df", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length                    3681.350000\n", "label_length                        20.575000\n", "n_rounds                             3.375000\n", "n_draft_rounds                       3.275000\n", "average_draft_rounds                 0.978304\n", "average_draft_tokens_accepted        9.275392\n", "simulation_time                  11144.796136\n", "beam_size                            9.931020\n", "n_unique_first_token                 2.964050\n", "n_unique_two_tokens                  5.591006\n", "latency                             41.887500\n", "latency_per_token                    2.846868\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=32, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:40], 9.5, 3.0, 'df_parallel_staged_neural_1b_40_budget32_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "509664a0df7b424db7bbd286d064c570", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length                    3681.350000\n", "label_length                        20.575000\n", "n_rounds                             3.400000\n", "n_draft_rounds                       3.325000\n", "average_draft_rounds                 0.991250\n", "average_draft_tokens_accepted        8.973606\n", "simulation_time                  33830.203166\n", "beam_size                           21.408106\n", "n_unique_first_token                 3.598764\n", "n_unique_two_tokens                 10.329118\n", "latency                             42.275000\n", "latency_per_token                    2.862362\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=64, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:40], 9.5, 3.0, 'df_parallel_staged_neural_1b_40_budget64_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": 189, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9159a35f1e48429b9b6e3c90fb2f2bf6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 14\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m lm \u001b[39m=\u001b[39m NeuralLM(tokens_budget\u001b[39m=\u001b[39m\u001b[39m16\u001b[39m, target_prob\u001b[39m=\u001b[39m\u001b[39m0.8\u001b[39m, draft_model\u001b[39m=\u001b[39mLongestOverlapLM(\u001b[39m12\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39mTrue\u001b[39;00m), draft_tokens_budget\u001b[39m=\u001b[39m\u001b[39m36\u001b[39m)\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m df \u001b[39m=\u001b[39m measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:\u001b[39m100\u001b[39;49m], \u001b[39m9.5\u001b[39;49m, \u001b[39m3.0\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mdf_parallel_staged_neural_1b_100_budget16_tp0.8_redo.csv\u001b[39;49m\u001b[39m'\u001b[39;49m)\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 14\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=32'>33</a>\u001b[0m df \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=33'>34</a>\u001b[0m \u001b[39mfor\u001b[39;00m d \u001b[39min\u001b[39;00m pbar:\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=34'>35</a>\u001b[0m     datum \u001b[39m=\u001b[39m measure_latency_per_sample_parallel_sd(lm\u001b[39m=\u001b[39;49mlm, sample\u001b[39m=\u001b[39;49md)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=35'>36</a>\u001b[0m     datum[\u001b[39m'\u001b[39m\u001b[39mlatency\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m=\u001b[39m main_round_cost \u001b[39m*\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mn_rounds\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m+\u001b[39m draft_round_cost \u001b[39m*\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mn_draft_rounds\u001b[39m\u001b[39m'\u001b[39m]\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=36'>37</a>\u001b[0m     datum[\u001b[39m'\u001b[39m\u001b[39mlatency_per_token\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m=\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mlatency\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m/\u001b[39m datum[\u001b[39m'\u001b[39m\u001b[39mlabel_length\u001b[39m\u001b[39m'\u001b[39m]        \n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 14\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m lm\u001b[39m.\u001b[39mfit(context \u001b[39m+\u001b[39m label[:index])\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m start_time \u001b[39m=\u001b[39m time\u001b[39m.\u001b[39mtime()\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m beam_of_predictions, draft_cost \u001b[39m=\u001b[39m lm\u001b[39m.\u001b[39;49mpredict_next_k_tokens(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     context \u001b[39m+\u001b[39;49m label[:index],\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m     max_tokens_to_predict)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m n_draft_rounds \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m \u001b[39m1\u001b[39m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m simulation_time\u001b[39m.\u001b[39mappend(\u001b[39m1000\u001b[39m \u001b[39m*\u001b[39m (time\u001b[39m.\u001b[39mtime() \u001b[39m-\u001b[39m start_time))\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb Cell 14\u001b[0m line \u001b[0;36m9\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=87'>88</a>\u001b[0m top_prediction_values \u001b[39m=\u001b[39m top_predictions\u001b[39m.\u001b[39mvalues\u001b[39m.\u001b[39mfloat()\u001b[39m.\u001b[39mcpu()\u001b[39m.\u001b[39mnumpy()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=88'>89</a>\u001b[0m top_prediction_indices \u001b[39m=\u001b[39m top_predictions\u001b[39m.\u001b[39mindices\u001b[39m.\u001b[39mcpu()\u001b[39m.\u001b[39mnumpy()\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=89'>90</a>\u001b[0m gc\u001b[39m.\u001b[39;49mcollect()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=90'>91</a>\u001b[0m torch\u001b[39m.\u001b[39mcuda\u001b[39m.\u001b[39mempty_cache()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v2.ipynb#Y126sdnNjb2RlLXJlbW90ZQ%3D%3D?line=92'>93</a>\u001b[0m \u001b[39mfor\u001b[39;00m index \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39mlen\u001b[39m(context) \u001b[39m-\u001b[39m \u001b[39m1\u001b[39m, \u001b[39mlen\u001b[39m(candidate)): \n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["lm = NeuralLM(tokens_budget=16, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0, 'df_parallel_staged_neural_1b_100_budget16_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm = NeuralLM(tokens_budget=32, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0, 'df_parallel_staged_neural_1b_100_budget32_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm = NeuralLM(tokens_budget=64, target_prob=0.8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0, 'df_parallel_staged_neural_1b_100_budget64_tp0.8_redo.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from experimental.yury.instruction_tuning.types import Exchange, Chat\n", "import dataclasses\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloaded 53 chat sessions and 426 samples.\n"]}], "source": ["OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yury/instruction_tuning/chats\")\n", "\n", "n_samples = 0\n", "chats = []\n", "\n", "with (OUTPUT_DIR / \"mercor_Jul25_2weeks.jsonl\").open(\"r\") as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        chat = Chat.from_dict(data)\n", "        n_samples += len(chat)\n", "        chats.append(chat)\n", "\n", "print(f\"Downloaded {len(chats)} chat sessions and {n_samples} samples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 763 original samples\n", "Loaded 763 meta-conversations\n", "Loaded 37 final samples\n"]}], "source": ["import json\n", "\n", "ORIGINAL_PATH = \"/mnt/efs/augment/user/yuri/tmp/preference_samples_mercor_jul1_jul17.json\"\n", "\n", "with open(ORIGINAL_PATH, \"r\") as f:\n", "    original_samples = json.load(f)\n", "\n", "print(\"Loaded %d original samples\" % len(original_samples))\n", "\n", "# META_CONVERSATION_PATH = \"./meta_conversations_jul8_v12.json\"\n", "META_CONVERSATION_PATH = \"/mnt/efs/augment/user/yuri/tmp/meta_conversations_jul1_jul16_mercor_763.json\"\n", "\n", "with open(META_CONVERSATION_PATH, \"r\") as f:\n", "    meta_conversations = json.load(f)\n", "\n", "print(\"Loaded %d meta-conversations\" % len(meta_conversations))\n", "\n", "# FINAL_SAMPLES = \"./final_samples.jul10_v9.json\"\n", "FINAL_SAMPLES = \"/mnt/efs/augment/user/yuri/tmp/mercor_samples_for_viz_jul17_v2.json\"\n", "\n", "with open(FINAL_SAMPLES, \"r\") as f:\n", "    final_samples = json.load(f)\n", "\n", "print(\"Loaded %d final samples\" % len(final_samples))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11\n"]}], "source": ["from experimental.yury.instruction_tuning.types import Exchange, Chat\n", "\n", "\n", "def build_synthesized_chat(sample):\n", "    history = []\n", "    for turn in sample['history']:\n", "        history.append(\n", "            Exchange(\n", "                request_message=turn['user'],\n", "                response_text=turn['assistant'],\n", "            )\n", "        )\n", "    # Add the last message as a separate turn\n", "    selected_code = sample.get(\"selected_code\", None)\n", "    if selected_code is not None and len(selected_code) == 0:\n", "        selected_code = None\n", "    history.append(\n", "        Exchange(\n", "            request_message=final_samples[index]['message'],\n", "            response_text=final_samples[index]['response'],\n", "            selected_code=selected_code,\n", "        )\n", "    )\n", "    return Chat(\n", "        history=history,\n", "        extra=\"[PERSONA] \" + sample[\"extra\"][\"persona\"],\n", "    )\n", "\n", "def build_original_chat(sample):\n", "    history = []\n", "    for turn in sample['request'].get(\"chat_history\", []):\n", "        history.append(\n", "            Exchange(\n", "                request_message=turn[\"request_message\"],\n", "                response_text=turn[\"response_text\"],\n", "            )\n", "        )\n", "    # Add the last message as a separate turn\n", "    selected_code = sample['request'].get(\"selected_code\", None)\n", "    if selected_code is not None and len(selected_code) == 0:\n", "        selected_code = None\n", "    history.append(\n", "        Exchange(\n", "            request_message=sample['request']['message'],\n", "            response_text=sample['response']['text'],\n", "            selected_code=sample['request'].get(\"selected_code\", None),\n", "        )\n", "    )\n", "    return Chat(\n", "        history=history,\n", "        extra=f\"[REQUEST {sample['request_id']}]\",\n", "    )\n", "\n", "def build_meta_chat(sample):\n", "    history = []\n", "    for turn in sample['meta_conversation_turns']:\n", "        history.append(\n", "            Exchange(\n", "                request_message=turn['user'],\n", "                response_text=turn['assistant'],\n", "            )\n", "        )\n", "    if sample['is_selected_code']:\n", "        history[-1] = dataclasses.replace(\n", "            history[-1],\n", "            selected_code=\"[some code is selected]\",\n", "        )\n", "\n", "    return Chat(\n", "        history=history,\n", "    )\n", "\n", "data = []\n", "\n", "for index in range(len(final_samples)):\n", "\n", "    synthesized_chat = build_synthesized_chat(final_samples[index])\n", "    meta_conversation_idx = final_samples[index]['extra']['meta_conversation_idx']\n", "    # original_chat = build_original_chat(original_samples[meta_conversation_idx]['option_a'])\n", "    original_chat = build_original_chat(meta_conversations[meta_conversation_idx]['sample_option_a'])\n", "    meta_chat = build_meta_chat(meta_conversations[meta_conversation_idx])\n", "\n", "    if len(meta_chat) != len(synthesized_chat):\n", "        continue\n", "\n", "    original_chat_truncated_history = original_chat.history[-len(meta_chat):]\n", "    original_chat = dataclasses.replace(original_chat, history=original_chat_truncated_history)\n", "\n", "    # print(len(original_chat), len(meta_chat), len(synthesized_chat))\n", "    data.append({\n", "        \"AI Tutors conversation\": original_chat,\n", "        \"Meta-conversation\": meta_chat,\n", "        \"Synthesized conversation\": synthesized_chat,\n", "    })\n", "\n", "print(len(data))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from jinja2 import Environment\n", "from pathlib import Path\n", "\n", "TEMPLATE_PATH = \"visualize_chats_template.html\"\n", "OUTPUT_PATH = \"/mnt/efs/augment/public_html/yury/tmp/instruction_tuning.html\"\n", "\n", "env = Environment(keep_trailing_newline=True)\n", "env.globals.update(zip=zip)\n", "template = Path(TEMPLATE_PATH).read_text(\"utf-8\")\n", "template = env.from_string(template)\n", "\n", "rendered_html = template.render(data=data)\n", "with open(OUTPUT_PATH, \"w\") as f:\n", "    f.write(rendered_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Data structures for instruction tuning data generation."""

import json
import dataclasses
from markdown import markdown


@dataclasses.dataclass(frozen=True)
class Exchange:
    """Represents a single exchange between the user and the model."""

    request_message: str
    """The user's message"""

    response_text: str
    """The response message from the model"""

    request_id: str | None = None
    """The request id"""

    selected_code: str | None = None
    """The selected code as additional context"""

    path: str | None = None
    """The path of the file"""

    prefix: str | None = None
    """The prefix of the file"""

    suffix: str | None = None
    """The suffix of the file"""

    @property
    def request_message_html(self):
        return markdown(self.request_message)

    @property
    def response_text_html(self):
        return markdown(self.response_text)

    @property
    def selected_code_html(self):
        if self.selected_code is None:
            return None
        return markdown(self.selected_code)


@dataclasses.dataclass(frozen=True)
class Chat:
    """A chat between the user and the model."""

    history: list[Exchange]
    """The conversation history"""

    extra: str | None = None

    def __len__(self):
        return len(self.history)

    @classmethod
    def from_dict(cls, data: dict):
        return cls(
            history=[Exchange(**x) for x in data["history"]],
            extra=data.get("extra", None),
        )

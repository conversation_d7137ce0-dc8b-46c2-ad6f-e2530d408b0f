<!DOCTYPE html>
<html>
<head>
  <title>Chat Visualization</title>
  <style>
    /* Typography and Spacing */
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      margin: 20px;
      background-color: #f0f4f8;
      color: #333;
      line-height: 1.6;
    }

    .chat-table {
      border-collapse: separate;
      width: 100%;
      table-layout: fixed; /* Ensures columns are of equal width */
      margin-bottom: 20px;
      background-color: #fff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .chat-table thead {
      background-color: #0074d9;
      color: #fff;
      text-align: left;
      cursor: pointer;
      font-size: 16px;
    }

    .chat-table th {
      padding: 12px 15px;
      width: 20%; /* fixed width for each column */
    }

    .extra-row {
      background-color: #cceeff;
      font-weight: bold;
      text-align: center;
      border-top: 1px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;
    }

    .user-row {
      background-color: #e0f7ff;
      border-bottom: 1px solid #e0e0e0;
    }

    .assistant-row {
      background-color: #e6f2ff;
      border-bottom: 1px solid #e0e0e0;
    }

    .chat-table td {
      padding: 12px 15px;
      vertical-align: top;
      width: 20%; /* fixed width for each column */
    }

    .chat-table tr:last-child td {
      border-bottom: none;
    }

    .code-block {
      display: block;
      background-color: #f4f4f4;
      border: 1px solid #ddd;
      padding: 10px;
      margin-top: 10px;
      font-family: 'Courier New', Courier, monospace;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  {% for item in data %}
  <table class="chat-table">
    <thead onclick="toggleTableBody(this)">
      <tr>
        {% for key in item %}
        <th>{{ key }}</th>
        {% endfor %}
      </tr>
    </thead>
    <tbody class="table-body">
      <tr class="extra-row">
        {% for value in item.values() %}
        <td>{{ value.extra if value.extra else '' }}</td>
        {% endfor %}
      </tr>
      {% set max_len = item.values() | map(attribute='history') | map('length') | max %}
      {% for i in range(max_len) %}
      <tr class="user-row">
        {% for value in item.values() %}
        <td>
          {% if i < value.history|length %}
            {{ value.history[i].request_message_html | safe }}
            {% if value.history[i].selected_code_html %}
              <div class="code-block">{{ value.history[i].selected_code_html | safe }}</div>
            {% endif %}
          {% endif %}
        </td>
        {% endfor %}
      </tr>
      <tr class="assistant-row">
        {% for value in item.values() %}
        <td>
          {% if i < value.history|length %}
            {{ value.history[i].response_text_html | safe }}
          {% endif %}
        </td>
        {% endfor %}
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% endfor %}

  <script>
    function toggleTableBody(header) {
      const tbody = header.nextElementSibling;
      tbody.classList.toggle('hidden');
    }
  </script>
</body>
</html>

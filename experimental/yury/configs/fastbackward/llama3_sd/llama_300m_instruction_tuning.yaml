determined:
  description: "Instruction tuning of a small llama3 model for speculative decoding purposes."
  workspace: Dev
  project: yury

augment:
  project_group: pretraining
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/llama3_300m_mqa.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  learning_rate: 1.0e-5
  decay_lr: False
  warmup_iters: 0
  min_lr: 1.0e-5
  # Total batch size is 64
  batch_size: 2
  gradient_accumulation_steps: 4
  # 2'000 steps
  max_iters: 2_000
  eval_interval: 250
  block_size: 8192
  use_activation_checkpointing: False
  train_data_path: /mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b/training
  eval_data_path: dogfood@/mnt/efs/augment/user/yury/speculative_decoding/llama3/dogfood_jul2024;binks_v4_llama3_70b@/mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b/validation
  checkpoint_optimizer_state: True
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/78360
  checkpoint: 9a01a832-93fa-44aa-b47b-536dd3da9d9c
  restore_optimizer_state_from_checkpoint: False

  tokenizer_name: Llama3InstructTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: llama3_300m_instruction_tuning_v1
  wandb_project: yury-llama3-pretrain

determined:
  description: "Instruction tuning of a llama3-1B model for speculative decoding purposes."
  workspace: Dev
  project: yury

augment:
  project_group: pretraining
  podspec_path: "8xH100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/llama3_1b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  learning_rate: 0.00001
  # Total batch size is 64
  batch_size: 1
  gradient_accumulation_steps: 16
  warmup_iters: 0
  min_lr: 0.00001
  decay_lr: False
  max_iters: 2000
  eval_interval: 250
  block_size: 8192
  use_activation_checkpointing: True
  train_data_path: /mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b/training
  eval_data_path: dogfood@/mnt/efs/augment/user/yury/speculative_decoding/llama3/dogfood_jul2024;binks_v4_llama3_70b@/mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b/validation
  checkpoint_optimizer_state: True
  model_parallel_size: 2
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81916/overview
  checkpoint: 1c722c58-b56e-438e-9f0c-a86b520cb734
  restore_optimizer_state_from_checkpoint: False

  tokenizer_name: Llama3InstructTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: llama3_1b_instruction_tuning_v1
  wandb_project: yury-llama3-pretrain

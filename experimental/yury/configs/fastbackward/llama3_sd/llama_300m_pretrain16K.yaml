determined:
  description: "Pre-training of a small llama3 model for speculative decoding purposes."
  workspace: Dev
  project: yury

augment:
  project_group: pretraining
  podspec_path: "8xH100.yaml"
  gpu_count: 16

fastbackward_configs:
 - configs/llama3_300m_mqa.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  # We follow DeepSeekCoder paper for hyperparameters during 16K uptraining
  # See section 3.6 https://arxiv.org/pdf/2401.14196
  learning_rate: 5.3e-5
  decay_lr: False
  warmup_iters: 0
  min_lr: 5.3e-5
  # Total batch size is 1024
  batch_size: 1
  gradient_accumulation_steps: 64
  # 5'000 steps
  max_iters: 5_000
  eval_interval: 500
  block_size: 16384
  use_activation_checkpointing: False
  train_data_path: /mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/code_contest/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/devdocs/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gmane/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gwene/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/jupyter_notebook/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/stackexchange/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/usenet/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/wikimedia/training;/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-16k-no-fim/training
  eval_data_path: code_contest@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/code_contest/validation;devdocs@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/devdocs/validation;gmane@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gmane/validation;gwene@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gwene/validation;jupyter_notebooks@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/jupyter_notebook/validation;stackexchange@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/stackexchange/validation;usenet@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/usenet/validation;wikimedia@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/wikimedia/validation;code@/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-16k-no-fim/validation
  checkpoint_optimizer_state: True
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/78143
  checkpoint: 389fd7b2-a916-4374-a458-2a93bceb8f1a
  restore_optimizer_state_from_checkpoint: False

  tokenizer_name: Llama3InstructTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: llama3_300m_pretrain_16K_v1
  wandb_project: yury-llama3-pretrain

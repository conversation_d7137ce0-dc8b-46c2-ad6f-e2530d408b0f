determined:
  description: "Pre-training of a llama3-1B model for speculative decoding purposes."
  workspace: Dev
  project: yury

augment:
  project_group: pretraining
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/llama3_1b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  # We follow DeepSeekCoder paper for hyperparameters:
  # See Table 2 https://arxiv.org/pdf/2401.14196
  learning_rate: 5.3e-5
  # Total batch size is 1024
  batch_size: 1
  gradient_accumulation_steps: 64
  warmup_iters: 0
  # See https://arxiv.org/pdf/2401.02954
  min_lr: 5.3e-5
  decay_lr: False
  # 5'000 steps
  max_iters: 5_000
  eval_interval: 500
  block_size: 16384
  use_activation_checkpointing: True
  train_data_path: /mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/code_contest/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/devdocs/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gmane/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gwene/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/jupyter_notebook/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/stackexchange/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/usenet/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/wikimedia/training;/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-16k-no-fim/training
  eval_data_path: code_contest@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/code_contest/validation;devdocs@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/devdocs/validation;gmane@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gmane/validation;gwene@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/gwene/validation;jupyter_notebooks@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/jupyter_notebook/validation;stackexchange@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/stackexchange/validation;usenet@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/usenet/validation;wikimedia@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-16k/wikimedia/validation;code@/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-16k-no-fim/validation
  checkpoint_optimizer_state: True
  model_parallel_size: 2
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/80602
  checkpoint: 879b6594-22dc-445d-89db-6d71ecb9eff1
  restore_optimizer_state_from_checkpoint: False

  tokenizer_name: Llama3InstructTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: llama3_1b_pretrain_16k_v1
  wandb_project: yury-llama3-pretrain-1B-16K

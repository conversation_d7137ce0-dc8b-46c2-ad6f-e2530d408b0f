determined:
  name: "StarCoder2-7B roguesl training"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/starcoder2_ft
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 128
  batch_size: 1
  max_iters: 1000
  warmup_iters: 100
  lr_decay_iters: 1000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 50
  checkpoint_dir: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  model_vocab_size: 51200
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/validation_dataset
  checkpoint_optimizer_state: True
  run_name: starcoder2_7b_roguesl_eth64m_npref250_olap0_quant50_v1
  wandb_project: yury-ft

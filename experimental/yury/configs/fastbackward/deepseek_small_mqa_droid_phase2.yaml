determined:
  name: "Droid-Small-MQA-phase2"
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_small_mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain
  learning_rate: 2.5e-6
  min_lr: 1e-4
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 10
  eval_iters: 128
  checkpoint_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain/deepseek_small_mqa_droid_phase1_v1/checkpoint_llama_iteration_2102
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-48/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-48/validation
  model_vocab_size: 51200
  batch_size: 1
  block_size: 16384
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  wandb_run_name: deepseek_small_mqa_droid_phase2_v1

determined:
  name: "StarCoder2-3B roguesl training"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/starcoder2_3b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/starcoder2_ft
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 32
  batch_size: 4
  max_iters: 1000
  warmup_iters: 100
  lr_decay_iters: 1000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 50
  checkpoint_dir: /mnt/efs/augment/checkpoints/starcoder2-3b-fb
  model_vocab_size: 51200
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/validation_dataset
  checkpoint_optimizer_state: True
  run_name: starcoder2_3b_roguesl_eth64m_npref250_olap0_quant50_v1
  wandb_project: yury-ft

# for local testing cnosider
# torchrun --nproc_per_node=1 train.py configs/starcoder2_3b.py   --block_size=4096 --learning_rate=1.0e-5 --min_lr=1.0e-6 --decay_lr=True --max_iters=2 --lr_decay_iters=2 --eval_interval=2   --train_data_path=/mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/dataset   --eval_data_path=/mnt/efs/augment/data/processed/rag/dataset/starcoder2_roguesl_eth64m_npref250_olap0_quant50/validation_dataset   --model_vocab_size=51200 --batch_size=2 --gradient_accumulation_steps=1 --use_activation_checkpointing=False   --checkpoint_optimizer_state=False --wandb_log=False --checkpoint_dir=/mnt/efs/augment/checkpoints/starcoder2-3b-fb

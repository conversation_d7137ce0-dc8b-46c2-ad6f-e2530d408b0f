determined:
  name: "DeepRogueSL-33B farpref use_reentrant=True"
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_ft
  loss_mask_policy: fim
  fim_middle_token_id: 32017
  eot_token_id: 32014
  pad_token_id: 32014
  gradient_accumulation_steps: 128
  batch_size: 1
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 50
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-base
  # Enable if restoring a training
  # checkpoint_dir: /mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth61m_farpref_prefsufretnpref250_quant50_v1/checkpoint_llama_iteration_550
  # restore_training_state_from_checkpoint: True
  # DATA: deeproguesl_eth61m_farpref_prefsufretnpref250_quant50
  # train_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth61m_farpref_prefsufretnpref250_quant50/dataset
  # eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth61m_farpref_prefsufretnpref250_quant50/validation_dataset
  # wandb_run_name: deeproguesl_eth61m_farpref_prefsufretnpref250_quant50_v1
  # DATA: deeproguesl_eth64m_npref250_olap0_quant50
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth64m_npref250_olap0_quant50/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth64m_npref250_olap0_quant50/validation_dataset
  wandb_run_name: deeproguesl_eth64m_npref250_olap0_quant50_v1
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft

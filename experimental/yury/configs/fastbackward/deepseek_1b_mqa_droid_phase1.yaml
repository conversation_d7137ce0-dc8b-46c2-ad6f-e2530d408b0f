determined:
  name: "Droid-1B-MQA-phase1"
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_1b_mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain
  learning_rate: 5e-6
  min_lr: 1e-4
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 10
  eval_iters: 128
  checkpoint_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain/deepseek_1b_mqa_filename_match_v1/checkpoint_llama_iteration_237500
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-47/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-47/validation
  model_vocab_size: 51200
  batch_size: 1
  block_size: 16384
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  wandb_run_name: deepseek_1b_mqa_droid_phase1_v1

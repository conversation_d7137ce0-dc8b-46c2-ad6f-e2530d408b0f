determined:
  name: "EvolCodealpacaV1-DeepSeek-1B-MQ<PERSON>, bsz 32, epochs 1, lr 1e-5"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_1b_mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain
  learning_rate: 1e-5
  min_lr: 1e-4
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 100
  eval_items: 0
  #
  # checkpoint: TBD
  train_data_path: /mnt/efs/augment/user/yury/droid_sd/evol_codealpaca_v1/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/validation
  model_vocab_size: 32356
  batch_size: 2
  block_size: 16384
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  run_name: deepseek_1b_mqa_evol_codealpaca_v1_bsz32_epochs1_lr1e-5

determined:
  name: "Dr<PERSON>-166-phase1-1B-MHA-24677"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_coder_instruct_1.3b.py

fastbackward_args:
  learning_rate: 7.5e-6
  min_lr: 1e-4
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 10
  eval_items: 0
  # hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/
  # hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-base/
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/62199/overview
  # checkpoint: 212f321a-723c-4757-91d1-6efa95854094
  checkpoint: /mnt/efs/augment/checkpoints/yury/deepseek-1b-mqa-24677/global_step10000
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/validation
  model_vocab_size: 51200
  # model_vocab_size: 32256
  batch_size: 1
  block_size: 16384
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  run_name: deepseek_1b_mha_24677

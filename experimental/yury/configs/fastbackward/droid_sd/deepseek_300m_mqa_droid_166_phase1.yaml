determined:
  name: "Droid-166-phase1-300M-MQA"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_small_mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain
  learning_rate: 7.5e-6
  min_lr: 1e-4
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 10
  eval_items: 0
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/59131/checkpoints
  checkpoint: df6a7fa8-6b37-4fc0-af1d-4fc863405af0
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/validation
  model_vocab_size: 32356
  batch_size: 1
  block_size: 16384
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  run_name: deepseek_300m_mqa_droid_166_phase1_v1

determined:
  name: "Droid-166-phase1-DeepSeek-1B-MQA-62199"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_1b_mqa.py

fastbackward_args:
  learning_rate: 7.5e-6
  min_lr: 1e-4
  rope_scaling_factor: 4.0
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  max_iters: 0
  max_epochs: 1
  eval_interval: 100
  eval_items: 0
  # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/62199/overview
  checkpoint: fb3480ae-c842-4fb0-bc64-0a61eb98c1d0
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-67/validation
  model_vocab_size: 32356
  batch_size: 1
  block_size: 16384
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  run_name: deepseek_1b_mqa_62199_droid_166_phase1

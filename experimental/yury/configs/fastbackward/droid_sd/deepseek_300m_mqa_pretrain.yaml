determined:
  name: "deepseek-300M-MQA pretraining"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "4xH100.yaml"
  gpu_count: 64
  keep_last_n_checkpoints: 2

fastbackward_configs:
 - configs/deepseek_small_mqa.py

fastbackward_args:
  block_size: 16384
  n_kv_heads: 1
  learning_rate: 5.3e-4
  min_lr: 5.3e-5
  decay_lr: True
  max_iters: 135000
  lr_decay_iters: 135000
  eval_interval: 5000
  loss_mask_policy: ignore_list
  ignore_list: "32015,32016,32017"
  eot_token_id: 32014
  pad_token_id: 32014
  train_data_path:  /mnt/efs/spark-data/shared/aug-stack/indexed-dataset-16k-fixed-base/training
  eval_data_path:  /mnt/efs/spark-data/shared/aug-stack/indexed-dataset-16k-fixed-base/validation
  # eval_data_path `/mnt/efs/spark-data/shared/aug-stack/indexed-dataset-16k-fixed-base/validation`
  # only has 2498 samples, so it is fine to evaluate on all of them
  eval_items: 0
  model_vocab_size: 32256
  batch_size: 8
  gradient_accumulation_steps: 1
  use_activation_checkpointing: True
  checkpoint_optimizer_state: True
  wandb_project: yury-ft
  run_name:  deepseek_300m_mqa_aug_stack_16k
  visualize_logits_samples: 10
  tokenizer_name: deepseek_coder_base

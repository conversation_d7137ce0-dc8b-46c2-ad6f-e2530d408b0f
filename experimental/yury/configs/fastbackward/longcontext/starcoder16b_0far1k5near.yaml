determined:
  name: "LongContext StarCoder-16B 0far1k5near"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 16

fastbackward_configs:
 - configs/starcoder_16b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/longcontext
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  model_vocab_size: 51200
  gradient_accumulation_steps: 64
  batch_size: 2
  max_iters: 1000
  warmup_iters: 100
  lr_decay_iters: 1000
  block_size: 1791
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 200
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/starcoderbase
  train_data_path: /mnt/efs/augment/data/processed/rag/longcon/0far1k5near/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/longcon/0far1k5near/validation_dataset
  checkpoint_optimizer_state: True
  wandb_project: yury-ft-longcontext
  run_name: starcoder16b_0far1k5near_v1

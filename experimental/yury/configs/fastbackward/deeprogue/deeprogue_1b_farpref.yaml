determined:
  name: "DeepRogueSL-1B farpref, model_vocab_size 51200, with 5677"
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 8

fastbackward_configs:
 - configs/deepseek_coder_instruct_1.3b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_ft
  loss_mask_policy: fim
  fim_middle_token_id: 32017
  eot_token_id: 32014
  pad_token_id: 32014
  gradient_accumulation_steps: 32
  batch_size: 4
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 50
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-base
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth64m_npref250_olap0_quant50/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeproguesl_eth64m_npref250_olap0_quant50/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: True
  run_name: deeproguesl_1.3b_eth64m_npref250_olap0_quant50_mvs51200_with5677
  wandb_project: yury-ft

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_1b_mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_pretrain
  rope_scaling_factor: 1.0
  learning_rate: 1e-3
  min_lr: 1e-4
  decay_lr: True
  max_iters: 237_501
  eval_interval: 5000
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: yury-ft
  wandb_run_name: deepseek_1b_mqa_filename_match_v1

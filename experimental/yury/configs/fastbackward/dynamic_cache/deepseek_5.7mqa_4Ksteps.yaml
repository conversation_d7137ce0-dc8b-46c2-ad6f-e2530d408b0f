determined:
  name: "DeepRogue-Dync-5.7B"
  description: null
  workspace: Dev
  project: yury

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_base_5.7mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yury/logs/deepseek_ft
  loss_mask_policy: fim
  fim_middle_token_id: 32017
  eot_token_id: 32014
  pad_token_id: 32014
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 16384
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 100
  model_vocab_size: 32256
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-5.7bmqa-base
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeprogue_dync_eth6_4m_morelang_4k7seq_fretprefsuf_minc4K_maxc16K_rdrop005/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/deeprogue_dync_eth6_4m_morelang_4k7seq_fretprefsuf_minc4K_maxc16K_rdrop005/validation_dataset
  checkpoint_optimizer_state: True
  run_name: deeprogue_5.7bmqa_dync_v1
  wandb_project: yury-ft

# Config for evaluating Starcoder on the Hydra 2-3 lines task without retrieval.
#
# Run it locally
# $ cd augment/research/eval
# $ python3 eval.py --local --v2 /home/<USER>/augment/experimental/yury/configs/eval/humaneval/starcoder_norag.yml

systems:
  - name: basic_rag
    model:
      name: starcoderbase_1b
      prompt:
        max_prefix_tokens: 4096
        max_suffix_tokens: 0
        max_prompt_tokens: 4096
        max_retrieved_chunk_tokens: 0
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
    experimental:
      remove_suffix: True
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: null

tasks:
  - name: humaneval
    # dataset: repoeval_2-3lines
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: HumanEval, starcoder/no_retrieval
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

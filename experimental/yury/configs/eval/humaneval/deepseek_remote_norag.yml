# Config for evaluating DeepSeek on the HumanEval task without retrieval.
#
# First, launch llama.cpp server locally
# $ bash research/utils/launch-llama.cpp.sh --build
# $ bash research/utils/launch-llama.cpp.sh --deepseek1b_base
#
# Second, launch evaluation locally
# $ cd augment/research/eval
# $ python3 eval.py --local --v2 /home/<USER>/augment/experimental/yury/configs/eval/humaneval/deepseek_remote_norag.yml

systems:
  - name: basic_rag
    model:
      name: deepseek_base_remote
      url: "http://127.0.0.1:8080"
      prompt:
        max_prefix_tokens: 4096
        max_suffix_tokens: 0
        max_prompt_tokens: 4096
        preamble: ""
        always_fim_style: False
        include_filename_in_prompt: False
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
    experimental:
      remove_suffix: True
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: null

tasks:
  - name: humaneval

# Cannot be executed via Determined since requires launch llama.cpp server

# Config for evaluating DeepSeek on the Hydra 2-3 lines task without retrieval.
#
# Run it locally
# $ cd research/eval
# $ python3 eval.py --local --v2 /home/<USER>/src/augment/experimental/yury/configs/eval/23lines/deepseek_ft_rogue.yml

systems:
  - name: basic_rag
    model:
      name: deeprogue
      # new Rogue model -- see https://www.notion.so/How-to-fine-tune-DeepSeek-models-from-zero-to-hero-997183bf3f624e88811132390a0c9e59 -- gets 425
      checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deepseek-5.7bmqa_20231223_rogue_v1/checkpoint_llama_iteration_999
      # old Rogue model -- see https://www.notion.so/RAG-fine-tuning-DeepSeek-db25d8c2ae9d477db8d205e74554a5e4 -- gets 440-443
      # checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deepseek-5.7bmqa_20231121_bsz1024_1000steps_v2_singlenode/checkpoint_llama_iteration_1000
      fim_gen_mode: evaluation
      model_parallel_size: 1
      prompt:
        max_prefix_tokens: 1280
        max_suffix_tokens: 768
        max_retrieved_chunk_tokens: -1
        max_filename_tokens: 50
        only_truncate_true_prefix: True
        prepend_bos_token: True
        max_prompt_tokens: 3816
        always_use_suffix_token: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      chunker:
        max_lines_per_chunk: 30
        name: line_level
      document_formatter:
        add_path: true
        name: ethanol6_document
        max_tokens: 999
      query_formatter:
        add_path: true
        max_tokens: 1023
        name: ethanol6_query
      scorer:
        checkpoint_path: ethanol/ethanol6-04.1
        name: ethanol
    experimental:
      retriever_top_k: 25
      trim_on_dedent: False
      trim_on_max_lines: null
      remove_suffix: False

task:
  name: hydra
  dataset: repoeval_2-3lines

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 1xH100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - RepoEval 23lines, rogue/16b_rogue_ethanol51m_addcpp_fix, DeepSeek
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

import_modules: experimental.igor.systems.ethanol

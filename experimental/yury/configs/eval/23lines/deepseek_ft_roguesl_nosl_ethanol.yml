# Config for evaluating DeepRogueSL model on the Hydra 2-3 lines task.
# See https://www.notion.so/748e51866e02402287eb7564d879116f for the reference results.
#
# $ cd research/eval
# $ python3 eval.py --v2 /home/<USER>/src/augment/experimental/yury/configs/eval/23lines/deepseek_ft_rogue.yml
# or for local run
# $ python3 eval.py --local --v2 /home/<USER>/src/augment/experimental/yury/configs/eval/23lines/deepseek_ft_rogue.yml
#
# NOTE: when evaluating 33B model (or any other model with `model_parallel_size: 4`) some additional steps are required:
# 1. Must change `gpu_count` to 4 in `research/gpt-neox/jobs/templates/eval-exec-v2-metaconfig.yaml`
# 2. Must change `podspec` to `4xA100.yaml` or `4xH100.yaml`
# 3. Must comment out `assert train_batch == micro_batch * grad_acc * dp_world_size` in the `research/gpt-neox/megatron/neox_arguments/arguments.py`
# 4. Consider changing setting `cpu=4` and `max_containers_per_pod=4` in the `research/eval/hydra/driver.py`

systems:
  - name: basic_rag
    model:
      name: deepseek_coder_roguesl_ft
      # DeepSeek-5.7B model
      checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth61m_fixfim_plain_5.7b_20240115_v1/checkpoint_llama_iteration_999
      model_parallel_size: 1
      # DeepSeek-33B model
      # checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth61m_fixfim_plain_33b_20240115_v1/checkpoint_llama_iteration_999
      # model_parallel_size: 4
      fim_gen_mode: evaluation
      prompt:
        max_prefix_tokens: 1180
        max_suffix_tokens: 768
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 3816
        component_order:
        - retrieval
        - prefix
        - suffix
        context_quant_token_len: 0
        nearby_prefix_token_len: 0
        nearby_prefix_token_overlap: 0
        nearby_suffix_token_len: 0
        nearby_suffix_token_overlap: 0
        prepend_bos_token: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      chunker:
        max_lines_per_chunk: 30
        name: line_level
      document_formatter:
        add_path: true
        name: ethanol6_document
        max_tokens: 999
      query_formatter:
        add_path: true
        max_tokens: 1023
        name: ethanol6_query
      scorer:
        checkpoint_path: ethanol/ethanol6-04.1
        name: ethanol
    experimental:
      retriever_top_k: 25
      trim_on_dedent: False
      trim_on_max_lines: null
      remove_suffix: False

task:
  name: hydra
  dataset: repoeval_2-3lines

podspec: 1xA100.yaml
# Make sure to replace it to either `4xA100.yaml` or `4xH100.yaml` if `model_parallel_size: 4`
# podspec: 4xA100.yaml

determined:
  name: Hydra - RepoEval 23lines, deeproguesl/ethanol6-04.1, DeepSeek
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

import_modules: experimental.igor.systems.ethanol

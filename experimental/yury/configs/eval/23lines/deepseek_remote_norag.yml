# Config for evaluating DeepSeek on the Hydra 2-3 lines task without retrieval.
#
# First, launch llama.cpp server locally
# $ bash research/utils/launch-llama.cpp.sh --build
# $ bash research/utils/launch-llama.cpp.sh --deepseek1b_base
#
# Second, launch evaluation locally
# $ cd augment/research/eval
# $ python3 eval.py --local --v2 /home/<USER>/augment/experimental/yury/configs/eval/23lines/deepseek_remote_norag.yml

systems:
  - name: basic_rag
    model:
      name: deepseek_base_remote
      url: "http://127.0.0.1:8080"
      prompt:
        max_prefix_tokens: 1280
        max_suffix_tokens: 768
        max_prompt_tokens: 3816
        preamble: ""
        always_fim_style: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker:
        name: line_level
        max_lines_per_chunk: 40
    experimental:
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: 3
      remove_suffix: False
      trim_trailing_newline_on_prefix: True

task:
  name: hydra
  dataset: repoeval_2-3lines

# Cannot be executed via Determined since requires launch llama.cpp server

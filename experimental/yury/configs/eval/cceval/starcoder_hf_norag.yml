# Config for evaluating Starcoder (HF) on the Hydra 2-3 lines task without retrieval.
#
# Run it locally
# $ cd augment/research/eval
# $ python3 eval.py --local --v2 /home/<USER>/src/augment/experimental/yury/configs/eval/23lines/starcoder_hf_norag.yml

systems:
  - name: basic_rag
    model:
      # name: starcoderbase_3b_hf
      # name: starcoderbase_7b_hf
      name: starcoderbase
      # name: starcoder2base_3b_hf
      # name: starcoder2base_7b_hf
      # name: starcoder2base_15b_hf
      prompt:
        max_prefix_tokens: 1280
        max_suffix_tokens: 768
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: 0
        preamble: ""
        always_fim_style: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker:
        name: line_level
        max_lines_per_chunk: 40
    experimental:
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: 3
      remove_suffix: False
      trim_trailing_newline_on_prefix: True

task:
  name: cceval  

# Use A40 for smaller models and A100 for larger models.
# podspec: A40.yaml
podspec: 1xA100.yaml

determined:
  name: repoeval_2-cceval, starcoder-hf without retrieval
  workspace: Dev
  project: yury-eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

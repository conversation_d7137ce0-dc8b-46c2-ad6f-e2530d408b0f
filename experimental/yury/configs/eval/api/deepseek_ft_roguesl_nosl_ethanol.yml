# Config for evaluating Deep<PERSON>eek on the Hydra 2-3 lines task without retrieval.
#
# Run it locally
# $ cd research/eval
# $ python3 eval.py --local --v2 /home/<USER>/src/augment/experimental/yury/configs/eval/23lines/deepseek_ft_rogue.yml

systems:
  - name: basic_rag
    model:
      name: deepseek_coder_roguesl_ft
      checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deeproguesl_eth61m_fixfim_plain_33b_20240115_v1/checkpoint_llama_iteration_999
      model_parallel_size: 4
      fim_gen_mode: evaluation
      prompt:
        max_prefix_tokens: 1180
        max_suffix_tokens: 768
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 3816
        component_order:
        - retrieval
        - prefix
        - suffix
        context_quant_token_len: 0
        nearby_prefix_token_len: 0
        nearby_prefix_token_overlap: 0
        nearby_suffix_token_len: 0
        nearby_suffix_token_overlap: 0
        prepend_bos_token: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      chunker:
        max_lines_per_chunk: 30
        name: line_level
      document_formatter:
        add_path: true
        name: ethanol6_document
        max_tokens: 999
      query_formatter:
        add_path: true
        max_tokens: 1023
        name: ethanol6_query
      scorer:
        checkpoint_path: ethanol/ethanol6-04.1
        name: ethanol
    experimental:
      retriever_top_k: 25
      trim_on_dedent: False
      trim_on_max_lines: null
      remove_suffix: False

task:
  dataset: finegrained-python.large
  name: api

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 4xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - RepoEval 23lines, roguesl/16b_rogue_ethanol51m_addcpp_fix, DeepSeek
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

import_modules: experimental.igor.systems.ethanol

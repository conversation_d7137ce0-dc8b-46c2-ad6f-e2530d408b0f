#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

system:
  name: basic_rag
  model:
    name: deeprogue
    checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deepseek-33b_20231124_v2/checkpoint_llama_iteration_1000_singledevice  # pragma: allowlist secret
    enable_evaluation_mode: true
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
      preamble: <｜begin▁of▁sentence｜>
      always_use_suffix_token: True
      only_truncate_true_prefix: True
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      name: simple_document
    query_formatter:
      add_path: true
      max_tokens: 1023
      name: ethanol3_query
      retokenize: true
    scorer:
      checkpoint_path: ethanol/ethanol5-01
      name: ethanol
  experimental:
    remove_suffix: False
    retriever_top_k: 25
    trim_on_dedent: True
    trim_on_max_lines: null


# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: repoeval_functions
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 1xH100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - RepoEval function, DeepSeek 33b_ethanol51m_addcpp
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
import_modules:
  experimental.igor.systems.ethanol

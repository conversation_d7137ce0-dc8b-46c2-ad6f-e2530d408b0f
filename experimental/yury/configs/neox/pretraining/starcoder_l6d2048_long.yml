includes:
- augment_configs/starcoder/model/starcoder.yml
- augment_configs/starcoder/model/starcoder-l6d2048.yml

determined:
  name: &detname starcoder-l6d2048-exported_dataset_4096_fim0.5  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: yury
  labels: ["starcoder", "l6d2048", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: *detname
  wandb_group: *detname
  wandb_project: yury-pretrain

  load: null
  tokenizer_type: StarCoderTokenizer
  dataset_type: direct
  shuffle_direct_dataset: True

  # Autoregressive loss
  loss_mask_mode: "none"
  attention_precision: bfloat16
  seq-length: 4096

  train_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_fim0.5/dataset"]
  valid_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_fim0.5/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_fim0.5/validation_dataset"]
  data_impl: mmap

  gradient_clipping: 1.0
  hidden_dropout: 0.1

  # GPT-3-Medium (125M params) reference hyperparameters:
  # Batch size: 0.5M tokens.
  # LR: 6e-4 --> 6e-4 with cosine annealing.
  # Warmup: 2k iters (from StarCoder; GPT-3 was 0.1)

  # 512k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 16
  gradient_accumulation_steps: 1
  train_batch_size: 128

  # 30k steps @ 512k tokens
  lr_decay_iters: 1000000
  lr_decay_style: cosine
  train_iters: 1000000
  warmup_iters: 2000

  # Eval/save frequency
  eval_interval: 500
  save_interval: 5000
  log-interval: 500

  # Optimization
  min_lr: 6.0e-5
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 6.0e-4
    type: Adam

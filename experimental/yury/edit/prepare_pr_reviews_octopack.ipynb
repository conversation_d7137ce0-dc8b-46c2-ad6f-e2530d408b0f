{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import os\n", "import random"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import functools\n", "from pathlib import Path\n", "\n", "def persistent_cache(filename):\n", "    def decorator(func):\n", "        cache = {}\n", "        cache_path = Path(filename)\n", "\n", "        # Load existing cache if file exists\n", "        if cache_path.is_file():\n", "            with open(filename, 'r') as f:\n", "                for line in f:\n", "                    datum = json.loads(line[:-1])\n", "                    cache[datum[\"key\"]] = datum[\"value\"]\n", "\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            # handle special cases\n", "            if len(args) == 1 and len(kwargs) == 0:\n", "                key = args[0]\n", "            else:\n", "                key = json.dumps((args, kwargs), sort_keys=True)\n", "\n", "            if key not in cache:\n", "                cache[key] = func(*args, **kwargs)\n", "                # Save the updated cache to the file\n", "                with open(filename, 'a') as f:\n", "                    f.write(json.dumps({\"key\": key, \"value\": cache[key]}) + '\\n')\n", "\n", "            return cache[key]\n", "\n", "        return wrapper\n", "    return decorator\n", "\n", "# Example usage\n", "@persistent_cache('/tmp/my_cache.jsonl')\n", "def my_function(x):\n", "    print(\"EXECUTING FOR\", x)\n", "    return x ** 2\n", "\n", "my_function(3)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import json\n", "\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\"Authorization\": f\"token {TOKEN}\"}\n", "\n", "\n", "@persistent_cache('/home/<USER>/data/url_cache_octopack.jsonl')\n", "def download(url): \n", "    time.sleep(0.5)    \n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code != 200:\n", "        raise ValueError(f\"Failed to download {url}: {response.status_code}\")\n", "    return response.json()\n", "\n", "\n", "def download_review_comment(repo_owner, repo_name, review_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/comments/{review_id}\"\n", "    return download(url)\n", "\n", "\n", "def get_commits_for_pr(repo_owner, repo_name, pr_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_id}/commits\"\n", "    return download(url)\n", "\n", "\n", "def get_commit_diff(repo_owner, repo_name, from_commit, to_commit):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/compare/{from_commit}...{to_commit}\"\n", "    return download(url)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from unidiff import PatchSet, UnidiffParseError\n", "\n", "def parse_diff_hunks(patch_obj):\n", "    full_patch = f\"\"\"diff --git a/{patch_obj[\"filename\"]} b/{patch_obj[\"filename\"]}\n", "    --- a/{patch_obj[\"filename\"]}\n", "    +++ b/{patch_obj[\"filename\"]}\n", "    {patch_obj['patch']}\n", "    \"\"\"\n", "    parsed_patch_set = PatchSet(full_patch)\n", "    assert len(parsed_patch_set) == 1\n", "    return parsed_patch_set[0]\n", "\n", "def is_line_inside_hunk(hunk, original_line):\n", "    return original_line >= hunk.source_start and original_line < hunk.source_start + hunk.source_length\n", "\n", "\n", "def get_hunk_that_modifies_line(commit_diff_obj, target_file, target_line):\n", "    if target_file is None or target_line is None:\n", "        return None\n", "    target_patch = None\n", "    for patch in commit_diff_obj['files']:\n", "        if patch['filename'] == target_file:\n", "            target_patch = patch\n", "            break\n", "    if target_patch is None:\n", "        return None\n", "    \n", "    if 'patch' not in target_patch:\n", "        # print('WEIRD PATCH: ', target_patch)\n", "        return None \n", "    \n", "    try:\n", "        hunks = parse_diff_hunks(target_patch)\n", "    except UnidiffParseError:\n", "        # print('UnidiffParseError')\n", "        return None\n", "    \n", "    target_hunk = None\n", "    for hunk in hunks:\n", "        if is_line_inside_hunk(hunk, target_line):\n", "            target_hunk = hunk\n", "            break\n", "    if target_hunk is None:\n", "        return None\n", "    return str(target_hunk)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 21178 repos and 4374590 reviews (cannot parse PR ID in 43892 samples) from /home/<USER>/data/mined-comments-25stars-25prs-Python.json\n", "TOTAL Loaded 21178 repos and 4374590 reviews (cannot parse PR ID in 43892 samples)\n"]}], "source": ["# These files are located at '/mnt/efs/augment/user/yury/pr_data/\n", "PATHS = [\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Python.json',\n", "    # '/home/<USER>/data/mined-comments-25stars-25prs-Java.json',\n", "    # '/home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json',\n", "    # '/home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json',\n", "    # '/home/<USER>/data/mined-comments-25stars-25prs-Go.json',\n", "]\n", "\n", "loaded_reviews = []\n", "n_total_repos = 0\n", "n_total_skip_cannot_parse_pr_id = 0\n", "\n", "\n", "for path in PATHS:\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "\n", "    n_total_repos += len(data)\n", "    n_reviews = 0\n", "    n_skip_cannot_parse_pr_id = 0\n", "\n", "    for repo_owner_name, repo in data.items():\n", "        repo_owner, repo_name = repo_owner_name.split('/')        \n", "        for review in repo:\n", "            review_with_extra_info = {\n", "                \"original_review_file\": path,\n", "                \"repo_owner_name\": repo_owner_name,\n", "                \"repo_owner\": repo_owner,\n", "                \"repo_name\": repo_name,\n", "            }\n", "            re_extract_pr_from_html_url = f\"https://github.com/{repo_owner}/{repo_name}/pull/([0-9]+)#discussion_r{review['id']}\"\n", "            m = re.match(re_extract_pr_from_html_url, review[\"html_url\"])\n", "            if m is None:\n", "                # That usually means that there is a mismatch between repo_owner in the data and html_url\n", "                n_skip_cannot_parse_pr_id += 1\n", "                n_total_skip_cannot_parse_pr_id += 1 \n", "                continue\n", "            n_reviews += 1\n", "            review_with_extra_info[\"pr_id\"] = int(m.group(1))\n", "            review_with_extra_info.update(review)\n", "            loaded_reviews.append(review_with_extra_info)\n", "    \n", "    print('Loaded %d repos and %d reviews (cannot parse PR ID in %d samples) from %s' % (len(data), n_reviews, n_skip_cannot_parse_pr_id, path))\n", "\n", "print('TOTAL Loaded %d repos and %d reviews (cannot parse PR ID in %d samples)' % (n_total_repos, len(loaded_reviews), n_total_skip_cannot_parse_pr_id))\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 47325 reviews after Octopack-style filtering.\n"]}], "source": ["OCTOPACK_STARTING_WORDS = {\"abort\", \"accelerate\", \"access\", \"accumulate\", \"add\", \"address\", \"adjust\", \"advance\", \"align\", \"allot\", \"allow\", \"amplify\", \"annotate\", \"append\", \"apply\", \"archive\", \"arrange\", \"attach\", \"augment\", \"automate\", \"backup\", \"boost\", \"break\", \"bring\", \"brush up\", \"build\", \"bump\", \"call\", \"change\", \"check\", \"choose\", \"clarify\", \"clean\", \"clear\", \"clone\", \"comment\", \"complete\", \"compress\", \"concatenate\", \"configure\", \"connect\", \"consolidate\", \"convert\", \"copy\", \"correct\", \"cover\", \"create\", \"customize\", \"cut\", \"deal with\", \"debug\", \"decipher\", \"declare\", \"decommission\", \"decomplexify\", \"decompress\", \"decrease\", \"decrypt\", \"define\", \"delete\", \"deploy\", \"designate\", \"destroy\", \"detach\", \"determine\", \"develop\", \"diminish\", \"disable\", \"discard\", \"disentangle\", \"dismantle\", \"divide\", \"document\", \"downgrade\", \"drop\", \"duplicate\", \"edit\", \"embed\", \"emphasize\", \"enable\", \"encrypt\", \"enforce\", \"enhance\", \"enlarge\", \"enumerate\", \"eradicate\", \"escalate\", \"establish\", \"exclude\", \"exit\", \"expand\", \"expedite\", \"expire\", \"extend\", \"facilitate\", \"fix\", \"format\", \"gather\", \"generalize\", \"halt\", \"handle\", \"hasten\", \"hide\", \"implement\", \"improve\", \"include\", \"increase\", \"increment\", \"indent\", \"index\", \"inflate\", \"initialize\", \"insert\", \"install\", \"integrate\", \"interpolate\", \"interrupt\", \"introduce\", \"isolate\", \"join\", \"kill\", \"leverage\", \"load\", \"magnify\", \"maintain\", \"make\", \"manage\", \"mark\", \"mask\", \"mend\", \"merge\", \"migrate\", \"modify\", \"monitor\", \"move\", \"multiply\", \"normalize\", \"optimize\", \"orchestrate\", \"order\", \"package\", \"paraphrase\", \"paste\", \"patch\", \"plug, \", \"prepare\", \"prepend\", \"print\", \"provision\", \"purge\", \"put\", \"quit\", \"raise\", \"read\", \"reannotate\", \"rearrange\", \"rebase\", \"reboot\", \"rebuild\", \"recomment\", \"recompile\", \"reconfigure\", \"reconnect\", \"rectify\", \"redact\", \"redefine\", \"reduce\", \"refactor\", \"reformat\", \"refresh\", \"reimplement\", \"reinforce\", \"relocate\", \"remove\", \"rename\", \"reorder\", \"reorganize\", \"repackage\", \"repair\", \"rephrase\", \"replace\", \"reposition\", \"reschedule\", \"reset\", \"reshape\", \"resolve\", \"restructure\", \"return\", \"revert\", \"revise\", \"revoke\", \"reword\", \"rework\", \"rewrite\", \"rollback\", \"save\", \"scale\", \"scrub\", \"secure\", \"select\", \"send\", \"set\", \"settle\", \"simplify\", \"solve\", \"sort\", \"speed up\", \"split\", \"stabilize\", \"standardize\", \"stipulate\", \"stop\", \"store\", \"streamline\", \"strengthen\", \"structure\", \"substitute\", \"subtract\", \"support\", \"swap\", \"switch\", \"synchronize\", \"tackle\", \"tag\", \"terminate\", \"test\", \"throw\", \"tidy\", \"transform\", \"transpose\", \"trim\", \"troubleshoot\", \"truncate\", \"tweak\", \"unblock\", \"uncover\", \"undo\", \"unify\", \"uninstall\", \"unplug\", \"unpublish\", \"unravel\", \"unstage\", \"unsync\", \"untangle\", \"unwind\", \"update\", \"upgrade\", \"use\", \"validate\", \"verify\", \"watch\", \"watermark\", \"whitelist\", \"withdraw\", \"work\", \"write\"}\n", "\n", "def filter_octopack_style(review):\n", "    words = review.strip().split()\n", "    if len(words) == 0:\n", "        return False\n", "    return words[0] in OCTOPACK_STARTING_WORDS\n", "\n", "filtered_reviews = []\n", "\n", "for review in loaded_reviews:\n", "    if filter_octopack_style(review['body']):\n", "        filtered_reviews.append(review)\n", "\n", "print(f\"There are {len(filtered_reviews)} reviews after Octopack-style filtering.\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 47%|████▋     | 22285/47325 [2:49:09<3:10:04,  2.20it/s, mined_suggestions=5631]  \n"]}, {"ename": "ValueError", "evalue": "Failed to download https://api.github.com/repos/allenai/natural-instructions/compare/f4553a9de4e10ce940bc422b23c94f40ca72e3e8...13f5143e9ca1dc7005bbb8b07886256f5bbde6d9: 422", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb Cell 7\u001b[0m line \u001b[0;36m7\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=67'>68</a>\u001b[0m following_commit, commit_diff, hunk \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m, \u001b[39mNone\u001b[39;00m, \u001b[39mNone\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=68'>69</a>\u001b[0m \u001b[39mfor\u001b[39;00m following_commit \u001b[39min\u001b[39;00m following_commits:\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=69'>70</a>\u001b[0m     commit_diff \u001b[39m=\u001b[39m get_commit_diff(review[\u001b[39m'\u001b[39;49m\u001b[39mrepo_owner\u001b[39;49m\u001b[39m'\u001b[39;49m], review[\u001b[39m'\u001b[39;49m\u001b[39mrepo_name\u001b[39;49m\u001b[39m'\u001b[39;49m], original_commit[\u001b[39m'\u001b[39;49m\u001b[39msha\u001b[39;49m\u001b[39m'\u001b[39;49m], following_commit[\u001b[39m'\u001b[39;49m\u001b[39msha\u001b[39;49m\u001b[39m'\u001b[39;49m])\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=70'>71</a>\u001b[0m     hunk \u001b[39m=\u001b[39m get_hunk_that_modifies_line(commit_diff, review[\u001b[39m'\u001b[39m\u001b[39mpath\u001b[39m\u001b[39m'\u001b[39m], end_line)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=71'>72</a>\u001b[0m     \u001b[39mif\u001b[39;00m hunk \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb Cell 7\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mget_commit_diff\u001b[39m(repo_owner, repo_name, from_commit, to_commit):\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m     url \u001b[39m=\u001b[39m \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mhttps://api.github.com/repos/\u001b[39m\u001b[39m{\u001b[39;00mrepo_owner\u001b[39m}\u001b[39;00m\u001b[39m/\u001b[39m\u001b[39m{\u001b[39;00mrepo_name\u001b[39m}\u001b[39;00m\u001b[39m/compare/\u001b[39m\u001b[39m{\u001b[39;00mfrom_commit\u001b[39m}\u001b[39;00m\u001b[39m...\u001b[39m\u001b[39m{\u001b[39;00mto_commit\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=31'>32</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m download(url)\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb Cell 7\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m     key \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39mdumps((args, kwargs), sort_keys\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m \u001b[39mif\u001b[39;00m key \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m cache:\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m     cache[key] \u001b[39m=\u001b[39m func(\u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     \u001b[39m# Save the updated cache to the file\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m     \u001b[39mwith\u001b[39;00m \u001b[39mopen\u001b[39m(filename, \u001b[39m'\u001b[39m\u001b[39ma\u001b[39m\u001b[39m'\u001b[39m) \u001b[39mas\u001b[39;00m f:\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb Cell 7\u001b[0m line \u001b[0;36m1\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m response \u001b[39m=\u001b[39m requests\u001b[39m.\u001b[39mget(url, headers\u001b[39m=\u001b[39mheaders)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m \u001b[39mif\u001b[39;00m response\u001b[39m.\u001b[39mstatus_code \u001b[39m!=\u001b[39m \u001b[39m200\u001b[39m:\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mFailed to download \u001b[39m\u001b[39m{\u001b[39;00murl\u001b[39m}\u001b[39;00m\u001b[39m: \u001b[39m\u001b[39m{\u001b[39;00mresponse\u001b[39m.\u001b[39mstatus_code\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_reviews_octopack.ipynb#W6sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m \u001b[39mreturn\u001b[39;00m response\u001b[39m.\u001b[39mjson()\n", "\u001b[0;31mValueError\u001b[0m: Failed to download https://api.github.com/repos/allenai/natural-instructions/compare/f4553a9de4e10ce940bc422b23c94f40ca72e3e8...13f5143e9ca1dc7005bbb8b07886256f5bbde6d9: 422"]}], "source": ["import tqdm \n", "import time\n", "\n", "reviews = []\n", "\n", "n_repo_no_longer_exists = 0\n", "n_original_commit_is_missing = 0\n", "n_no_commits_after = 0\n", "n_end_line_is_missing = 0\n", "n_start_line_is_missing = 0\n", "n_cannot_find_corresponding_hunk = 0\n", "\n", "with tqdm.tqdm(total=len(filtered_reviews)) as pbar:    \n", "    for filtered_review in filtered_reviews:\n", "        pbar.update(1)        \n", "        review = {}\n", "        review.update(filtered_review)\n", "\n", "        try:\n", "            full_comment_info = download_review_comment(\n", "                filtered_review['repo_owner'],\n", "                filtered_review['repo_name'],\n", "                filtered_review['id'],\n", "            )\n", "            review[\"full_comment_info\"] = full_comment_info\n", "        except ValueError:\n", "            n_repo_no_longer_exists += 1\n", "            continue\n", "\n", "        end_line = full_comment_info['original_line']\n", "        if end_line is None:\n", "            n_end_line_is_missing += 1\n", "            continue\n", "\n", "        start_line = full_comment_info['line']\n", "        # if start_line is None:\n", "        #     n_start_line_is_missing += 1\n", "        #     continue\n", "\n", "        review.update({\n", "            'start_line': start_line,\n", "            'end_line': end_line,\n", "        })\n", "\n", "        original_commit_sha = full_comment_info['original_commit_id']\n", "\n", "        all_commits = get_commits_for_pr(review['repo_owner'], review['repo_name'], review['pr_id'])\n", "        review[\"all_commits_in_pr\"] = all_commits\n", "\n", "        original_commit_index = None\n", "        for commit_index, commit in enumerate(all_commits):\n", "            if commit['sha'] == original_commit_sha:\n", "                original_commit_index = commit_index\n", "\n", "        if original_commit_index is None:\n", "            n_original_commit_is_missing += 1 \n", "            continue\n", "\n", "        original_commit = all_commits[original_commit_index]\n", "        review[\"original_commit\"] = original_commit\n", "\n", "        following_commits = all_commits[original_commit_index + 1:]\n", "\n", "        if len(following_commits) == 0:\n", "            n_no_commits_after += 1\n", "            continue                \n", "\n", "        following_commit, commit_diff, hunk = None, None, None\n", "        for following_commit in following_commits:\n", "            commit_diff = get_commit_diff(review['repo_owner'], review['repo_name'], original_commit['sha'], following_commit['sha'])\n", "            hunk = get_hunk_that_modifies_line(commit_diff, review['path'], end_line)\n", "            if hunk is not None:\n", "                review.update({\n", "                    'following_commit': following_commit,\n", "                    'commit_diff': commit_diff,\n", "                    'hunk': hunk,\n", "                })\n", "                break\n", "\n", "        if hunk is None:\n", "            n_cannot_find_corresponding_hunk += 1\n", "            continue\n", "\n", "        reviews.append(review)\n", "        pbar.set_postfix({\"mined_suggestions\": len(reviews)})\n", "\n", "print('-- removed %d because cannot fetch comment info' % n_repo_no_longer_exists)\n", "print('-- removed %d because cannot determine end line' % n_end_line_is_missing)\n", "print('-- removed %d because cannot determine start line' % n_start_line_is_missing)\n", "print('-- removed %d because original commit is missing' % n_original_commit_is_missing)\n", "print('-- removed %d because no commits after original commit' % n_no_commits_after)\n", "print('-- removed %d because no commits modify code line the comment corresponded to' % n_cannot_find_corresponding_hunk)\n", "\n", "print('Final reviews: %d' % len(reviews))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'@@ -44,6 +70,10 @@ def bfs(self, start_vertex: int) -> Set[int]:\\n \\n \\n if __name__ == \"__main__\":\\n+    from doctest import testmod\\n+\\n+    testmod(verbose=True)\\n+\\n     g = Graph()\\n     g.add_edge(0, 1)\\n     g.add_edge(0, 2)\\n'"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["hunk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                   REVIEW COMMENT: add\n", "```py\n", "from doctest import testmod\n", "testmod()\n", "```\n", "                      REVIEW LINK: https://github.com/TheAlgorithms/Python/pull/3908#discussion_r527491074\n", "COMMIT WE THINK ADDRESSED COMMENT: https://github.com/TheAlgorithms/Python/commit/0b36986b9fd62f6617e376934b6bc45850bb7549\n", "    DIFF WITH THE ORIGINAL COMMIT: https://github.com/TheAlgorithms/Python/compare/51192870300b45076276dc285b94125a6d25175f...0b36986b9fd62f6617e376934b6bc45850bb7549\n", "@@ -44,6 +70,10 @@ def bfs(self, start_vertex: int) -> Set[int]:\n", " \n", " \n", " if __name__ == \"__main__\":\n", "+    from doctest import testmod\n", "+\n", "+    testmod(verbose=True)\n", "+\n", "     g = Graph()\n", "     g.add_edge(0, 1)\n", "     g.add_edge(0, 2)\n", "\n", "\n", "====================================================================================================\n", "                   REVIEW COMMENT: comment about what prepare_for_model does would be nice.\n", "\n", "                      REVIEW LINK: https://github.com/huggingface/transformers/pull/2973#discussion_r382962362\n", "COMMIT WE THINK ADDRESSED COMMENT: https://github.com/huggingface/transformers/commit/80e272d990efe637d0d9c889bb6c64afaefa94ef\n", "    DIFF WITH THE ORIGINAL COMMIT: https://github.com/huggingface/transformers/compare/c17c7411ba173f3a3e8ca65af3f2640c40acd66d...80e272d990efe637d0d9c889bb6c64afaefa94ef\n", "@@ -1114,6 +1147,9 @@ def total_sequence_length(input_pairs):\n", " \n", "         batch_outputs = {}\n", "         for first_ids, second_ids in input_ids:\n", "+            # Prepares a sequence of input id, or a pair of sequences of inputs ids so that it can be used by\n", "+            # the model. It adds special tokens, truncates sequences if overflowing while taking into account\n", "+            # the special tokens and manages a window stride for overflowing tokens\n", "             outputs = self.prepare_for_model(\n", "                 first_ids,\n", "                 pair_ids=second_ids,\n", "\n", "\n", "====================================================================================================\n"]}], "source": ["# for index in random.sample(range(len(reviews)), 10):\n", "for index in range(len(reviews)):\n", "    print('                   REVIEW COMMENT:', reviews[index]['body'])\n", "    print('                      REVIEW LINK:', reviews[index]['html_url'])    \n", "    print('COMMIT WE THINK ADDRESSED COMMENT:', reviews[index]['following_commit']['html_url'])\n", "    print('    DIFF WITH THE ORIGINAL COMMIT:', reviews[index]['commit_diff']['html_url'])\n", "    print(reviews[index]['hunk'])\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Hunk: @@ 44,6 70,10 @@ def bfs(self, start_vertex: int) -> Set[int]:>"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["commit_diff_obj = commit_diff\n", "target_file = review['path']\n", "target_line = end_line\n", "\n", "target_patch = None\n", "for patch in commit_diff_obj['files']:\n", "    if patch['filename'] == target_file:\n", "        target_patch = patch\n", "        break\n", "\n", "try:\n", "    hunks = parse_diff_hunks(target_patch)\n", "except UnidiffParseError:\n", "    print('UnidiffParseError')\n", "\n", "target_hunk = None\n", "for hunk in hunks:\n", "    if is_line_inside_hunk(hunk, target_line):\n", "        target_hunk = hunk\n", "        break\n", "\n", "target_hunk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'diff_line_no',\n", " 'is_added',\n", " 'is_context',\n", " 'is_removed',\n", " 'line_type',\n", " 'source_line_no',\n", " 'target_line_no',\n", " 'value']"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(target_hunk[4])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fetch PR info so we know the author\n", "# Fetch PR comments so we know which suggestions have only one response by non-author\n", "# Maybe also consider changes in the single file?\n", "# <PERSON><PERSON> commits for PR and see that a comment has something right before  "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
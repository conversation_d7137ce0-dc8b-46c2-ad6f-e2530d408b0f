{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import functools\n", "from pathlib import Path\n", "\n", "def persistent_cache(filename):\n", "    def decorator(func):\n", "        cache = {}\n", "        cache_path = Path(filename)\n", "\n", "        # Load existing cache if file exists\n", "        if cache_path.is_file():\n", "            with open(filename, 'r') as f:\n", "                for line in f:\n", "                    datum = json.loads(line[:-1])\n", "                    cache[datum[\"key\"]] = datum[\"value\"]\n", "\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            # handle special cases\n", "            if len(args) == 1 and len(kwargs) == 0:\n", "                key = args[0]\n", "            else:\n", "                key = json.dumps((args, kwargs), sort_keys=True)\n", "\n", "            if key not in cache:\n", "                cache[key] = func(*args, **kwargs)\n", "                # Save the updated cache to the file\n", "                with open(filename, 'a') as f:\n", "                    f.write(json.dumps({\"key\": key, \"value\": cache[key]}) + '\\n')\n", "\n", "            return cache[key]\n", "\n", "        return wrapper\n", "    return decorator\n", "\n", "# Example usage\n", "@persistent_cache('/tmp/my_cache.jsonl')\n", "def my_function(x):\n", "    print(\"EXECUTING FOR\", x)\n", "    return x ** 2\n", "\n", "my_function(3)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'databaseId': 553261406,\n", " 'bodyText': 'Given that each test has a description, could use sub-tests here;\\n  \\n    \\n      \\n        Suggested change\\n      \\n    \\n    \\n      \\n          \\n            \\n            \\tfor _, c := range cases {\\n          \\n          \\n            \\n            \\t\\tst, err := parseStat(c.input)\\n          \\n          \\n            \\n            \\t\\tt.Logf(\"case %q: error: %v, stat: %v\", c.desc, err, st)\\n          \\n          \\n            \\n            \\t\\tif err == nil {\\n          \\n          \\n            \\n            \\t\\t\\tt.Errorf(\"case %q, expected error, got nil\", c.desc)\\n          \\n          \\n            \\n            \\t\\t}\\n          \\n          \\n            \\n            \\tfor _, c := range cases {\\n          \\n          \\n            \\n            \\t\\tc := c\\n          \\n          \\n            \\n            \\t\\tt.Run(c.desc, func(t *testing.T) {\\n          \\n          \\n            \\n            \\t\\t\\tst, err := parseStat(c.input)\\n          \\n          \\n            \\n            \\t\\t\\tt.Logf(\"error: %v, stat: %v\", err, st)\\n          \\n          \\n            \\n            \\t\\t\\tif err == nil {\\n          \\n          \\n            \\n            \\t\\t\\t\\tt.Fatalf(\"expected error, got nil\", c.desc)\\n          \\n          \\n            \\n            \\t\\t\\t}\\n          \\n          \\n            \\n            \\t\\t})',\n", " 'bodyHTML': '<p dir=\"auto\">Given that each test has a description, could use sub-tests here;</p>\\n  <div class=\"my-2 border rounded-2 js-suggested-changes-blob diff-view js-check-bidi\" id=\"\">\\n    <div class=\"f6 p-2 lh-condensed border-bottom d-flex\">\\n      <div class=\"flex-auto flex-items-center color-fg-muted\">\\n        Suggested change\\n      </div>\\n    </div>\\n    <div itemprop=\"text\" class=\"blob-wrapper data file\" style=\"margin: 0; border: none; overflow-y: visible; overflow-x: auto;\">\\n      <table class=\"d-table tab-size mb-0 width-full\" data-paste-markdown-skip=\"\">\\n          <tbody><tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"114\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t<span class=\"pl-k\">for</span> <span class=\"pl-s1\">_</span>, <span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-k\">range</span> <span class=\"pl-s1\">cases</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"115\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-s1\">st</span>, <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-en\">parseStat</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">input</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"116\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Logf</span>(<span class=\"pl-s\">\"case %q: error: %v, stat: %v\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>, <span class=\"pl-s1\">err</span>, <span class=\"pl-s1\">st</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"117\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-k\">if</span> <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">==</span> <span class=\"pl-c1\">nil</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"118\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Errorf</span>(<span class=\"pl-s\">\"case %q, expected error, got nil\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"119\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t}</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"114\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t<span class=\"pl-k\">for</span> <span class=\"pl-s1\">_</span>, <span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-k\">range</span> <span class=\"pl-s1\">cases</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"115\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t<span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-s1\">c</span></td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"116\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-c1\">Run</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>, <span class=\"pl-k\">func</span>(<span class=\"pl-s1\">t</span> <span class=\"pl-c1\">*</span>testing.<span class=\"pl-smi\">T</span>) {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"117\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-s1\">st</span>, <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-en\">parseStat</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">input</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"118\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Logf</span>(<span class=\"pl-s\">\"error: %v, stat: %v\"</span>, <span class=\"pl-s1\">err</span>, <span class=\"pl-s1\">st</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"119\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-k\">if</span> <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">==</span> <span class=\"pl-c1\">nil</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"120\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Fatalf</span>(<span class=\"pl-s\">\"expected error, got nil\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"121\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t}</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"122\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t})</td>\\n          </tr>\\n      </tbody></table>\\n    </div>\\n    <div class=\"js-apply-changes\"></div>\\n  </div>\\n',\n", " 'body': 'Given that each test has a description, could use sub-tests here;\\r\\n\\r\\n```suggestion\\r\\n\\tfor _, c := range cases {\\r\\n\\t\\tc := c\\r\\n\\t\\tt.Run(c.desc, func(t *testing.T) {\\r\\n\\t\\t\\tst, err := parseStat(c.input)\\r\\n\\t\\t\\tt.Logf(\"error: %v, stat: %v\", err, st)\\r\\n\\t\\t\\tif err == nil {\\r\\n\\t\\t\\t\\tt.Fatalf(\"expected error, got nil\", c.desc)\\r\\n\\t\\t\\t}\\r\\n\\t\\t})\\r\\n```'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "import json\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\n", "    \"Authorization\": f\"token {TOKEN}\",\n", "    \"Content-Type\": \"application/json\",\n", "}\n", "\n", "@persistent_cache('/home/<USER>/data/url_graphql_cache_v1.jsonl')\n", "def query_graphql_comment(node_id):\n", "  # https://docs.github.com/en/graphql/overview/about-the-graphql-api\n", "  # Use https://docs.github.com/en/graphql/overview/explorer for experimenting with queries  \n", "  query = \"\"\"query {\n", "    node(id: \"%s\") {\n", "      ... on PullRequestReviewComment {\n", "        databaseId\n", "        bodyText\n", "        bodyHTML\n", "        body\n", "      }\n", "    }\n", "  }\"\"\" % node_id\n", "\n", "  response = requests.post(\"https://api.github.com/graphql\", headers=headers, data=json.dumps({\"query\": query}))\n", "  assert response.status_code == 200\n", "  return response.json()[\"data\"][\"node\"]\n", "\n", "\n", "query_graphql_comment(\"MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1MzI2MTQwNg==\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\tfor _, c := range cases {\n", "\t\tst, err := parseStat(c.input)\n", "\t\tt.Logf(\"case %q: error: %v, stat: %v\", c.desc, err, st)\n", "\t\tif err == nil {\n", "\t\t\t<PERSON><PERSON>(\"case %q, expected error, got nil\", c.desc)\n", "\t\t}\n", "\tfor _, c := range cases {\n", "\t\tc := c\n", "\t\tt.<PERSON>(c.desc, func(t *testing.T) {\n", "\t\t\tst, err := parseStat(c.input)\n", "\t\t\tt.Logf(\"error: %v, stat: %v\", err, st)\n", "\t\t\tif err == nil {\n", "\t\t\t\tt.<PERSON>(\"expected error, got nil\", c.desc)\n", "\t\t\t}\n", "\t\t})\n", "def test_collection_named_collections(\n", "@pytest.mark.parallel\n", "def test_collection_named_collections(\n"]}], "source": ["import re\n", "import html\n", "\n", "RE_TD_CLASS_OLD_CODE = r\"\\s*<td class=\\\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\\\">(.*)</td>\"\n", "RE_TD_CLASS_NEW_CODE = r\"\\s*<td class=\\\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\\\">(.*)</td>\"\n", "RE_SPAN_TAG = r\"<span class=[^>]+>([^<]+)</span>\"\n", "\n", "\n", "def strip_span_tags(text):\n", "    new_text = re.sub(RE_SPAN_TAG, r\"\\1\", text)\n", "    while new_text != text:\n", "        text = new_text\n", "        new_text = re.sub(RE_SPAN_TAG, r\"\\1\", text)\n", "    return new_text\n", "\n", "\n", "def extract_code(bodyHTML, re_td_pattern):\n", "    html_lines = bodyHTML.splitlines()\n", "    extracted_code_lines = []\n", "    for line in html_lines:\n", "        if m := re.match(re_td_pattern, line):\n", "            extracted_line = m.group(1)\n", "            cleaned_extracted_line = html.unescape(strip_span_tags(extracted_line))\n", "            extracted_code_lines.append(cleaned_extracted_line)\n", "    return \"\\n\".join(extracted_code_lines).rstrip(\"\\n\")\n", "\n", "\n", "def extract_old_code(bodyHTML):\n", "    return extract_code(bodyHTML, RE_TD_CLASS_OLD_CODE)\n", "\n", "\n", "def extract_new_code(bodyHTML):\n", "    return extract_code(bodyHTML, RE_TD_CLASS_NEW_CODE)\n", "\n", "\n", "def extract_old_new_code(bodyHTML):    \n", "    return extract_old_code(bodyHTML), extract_new_code(bodyHTML)\n", "\n", "\n", "sample_comment = query_graphql_comment(\"MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1MzI2MTQwNg==\")\n", "extract_old_new_code(sample_comment['bodyHTML'])\n", "print(extract_old_code(sample_comment['bodyHTML']))\n", "print(extract_new_code(sample_comment['bodyHTML']))\n", "\n", "# https://github.com/pulp/pulp_ansible/pull/1376#discussion_r1122782900\n", "# https://api.github.com/repos/pulp/pulp_ansible/pulls/comments/1122782900\n", "sample_comment = query_graphql_comment(\"PRRC_kwDOBr-zhM5C7E60\")\n", "extract_old_new_code(sample_comment['bodyHTML'])\n", "print(extract_old_code(sample_comment['bodyHTML']))\n", "print(extract_new_code(sample_comment['bodyHTML']))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 106341 v1 examples\n"]}], "source": ["import json\n", "\n", "with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v1.json\") as f:\n", "    data_v1 = json.load(f)\n", "\n", "print('Loaded %d v1 examples' % len(data_v1))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def slice_code(code, start_line, end_line, assert_borders):\n", "    code_lines = code.splitlines()\n", "    if assert_borders:\n", "        assert start_line < len(code_lines)\n", "        assert end_line <= len(code_lines)\n", "    return \"\\n\".join(code_lines[start_line: end_line])\n", "\n", "\n", "def recompute_old_code(start_line, end_line, old_file, new_code, graphql_old_code, graphql_new_code):    \n", "    if graphql_new_code != new_code:\n", "        # print('NEW CODE')\n", "        # print(new_code)\n", "        # print('NEW CODE ACCORDING TO GRAPHQL')\n", "        # print(graphql_new_code)\n", "        return None\n", "        \n", "    assert graphql_new_code == new_code\n", "    old_code = slice_code(old_file, start_line, end_line, False)\n", "\n", "    if old_code == graphql_old_code:\n", "        return start_line, end_line\n", "\n", "    if old_code.startswith(graphql_old_code):\n", "        actual_number_of_lines = len(graphql_old_code.splitlines())\n", "        new_end_line = start_line + actual_number_of_lines\n", "        new_old_code = slice_code(old_file, start_line, new_end_line, True)\n", "        return start_line, new_end_line\n", "    else:\n", "        # print('OLD CODE')\n", "        # print(old_code)\n", "        # print('OLD CODE ACCORDING TO GRAPHQL')\n", "        # print(graphql_old_code)\n", "        return None\n", "\n", "def get_data_v1_sample_given_id(sample_id):\n", "    samples = [sample for sample in data_v1 if sample[\"id\"] == sample_id]\n", "    assert len(samples) == 1\n", "    return samples[0]\n", "\n", "sample_553261406 = get_data_v1_sample_given_id(553261406)\n", "graphql_info_553261406 = query_graphql_comment(sample_553261406[\"full_comment_info\"][\"node_id\"])\n", "graphql_old_code_553261406, graphql_new_code_553261406 =  extract_old_new_code(graphql_info_553261406[\"bodyHTML\"])\n", "\n", "assert recompute_old_code(\n", "    start_line=sample_553261406['start_line'] - 1,\n", "    end_line=sample_553261406['end_line'],\n", "    old_file=sample_553261406['old_file'],\n", "    new_code=\"\\n\".join(sample_553261406['new_code'].splitlines()),\n", "    graphql_old_code=graphql_old_code_553261406,\n", "    graphql_new_code=graphql_new_code_553261406,\n", ") is not None\n", "\n", "sample_1122782900 = get_data_v1_sample_given_id(1122782900)\n", "graphql_info_1122782900 = query_graphql_comment(sample_1122782900[\"full_comment_info\"][\"node_id\"])\n", "graphql_old_code1122782900, graphql_new_code1122782900 =  extract_old_new_code(graphql_info_1122782900[\"bodyHTML\"])\n", "\n", "assert recompute_old_code(\n", "    start_line=sample_1122782900['start_line'] - 1,\n", "    end_line=sample_1122782900['end_line'],\n", "    old_file=sample_1122782900['old_file'],\n", "    new_code=\"\\n\".join(sample_1122782900['new_code'].splitlines()),\n", "    graphql_old_code=graphql_old_code1122782900,\n", "    graphql_new_code=graphql_new_code1122782900,\n", ") is None\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import requests\n", "import base64\n", "\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\"Authorization\": f\"token {TOKEN}\"}\n", "\n", "\n", "@persistent_cache('/home/<USER>/data/url_cache.jsonl')\n", "def download(url):    \n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    elif response.status_code == 404:\n", "        return None\n", "    else:\n", "        raise ValueError(f\"Failed to download {url}: {response.status_code}\")\n", "\n", "\n", "def download_file(repo_owner, repo_name, file_path, commit):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{file_path}?ref={commit}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")\n", "    try:\n", "        if result[\"encoding\"] != \"base64\":\n", "            raise ValueError(f\"Failed to download {url}: {result['encoding']} != base64\")    \n", "    except TypeError:\n", "        raise ValueError(f\"Failed to download {url}: problems with encoding in the result {result}\")\n", "    result[\"decoded_content\"] = base64.b64decode(result[\"content\"]).decode(\"utf8\")\n", "    return result\n", "\n", "\n", "def download_review_comment(repo_owner, repo_name, review_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/comments/{review_id}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")    \n", "    return result\n", "\n", "\n", "def download_commit_ids_for_pr(repo_owner, repo_name, pr_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_id}/commits\"\n", "    return download(url)\n", "\n", "\n", "def download_commit(repo_owner, repo_name, commit_sha):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/commits/{commit_sha}\"\n", "    return download(url)\n", "\n", "\n", "def download_pr_info(repo_owner, repo_name, pr_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_id}\"\n", "    return download(url)    \n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It might be best to check if the type is uuid.UUID instead of blindly casting to str\n", "\n", "```suggestion\n", "        if isinstance(field.data, uuid.UUID):\n", "            return\n", "\n", "        try:\n", "            uuid.UUID(field.data)\n", "        except ValueError:\n", "            raise ValidationError(message)\n", "```\n", "{'original_instruction': 'It might be best to check if the type is uuid.UUID instead of blindly casting to str', 'new_code': '        if isinstance(field.data, uuid.UUID):\\n            return\\n\\n        try:\\n            uuid.UUID(field.data)\\n        except ValueError:\\n            raise ValidationError(message)'}\n", "```suggestion\n", "        uuid.UUID(\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\"),\n", "```\n", "\n", "RNG in tests is bad!'\n", "{'original_instruction': \"RNG in tests is bad!'\", 'new_code': '        uuid.UUID(\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\"),'}\n"]}], "source": ["RE_SUGGESTION_PATTERN = r\"```suggestion\\r\\n([^`]*?)\\r\\n```\"\n", "\n", "\n", "def parse_single_suggestion(text: str):\n", "    comments, suggestions = [], []\n", "    while (m := re.search(RE_SUGGESTION_PATTERN, text)) is not None:\n", "        comment = text[:m.start()].strip()\n", "        if len(comment) > 0:\n", "            comments.append(comment)\n", "        text = text[m.end():].strip()\n", "        suggestions.append(m.group(1))\n", "        \n", "    text = text.strip()\n", "    if len(text) > 0:\n", "        comments.append(text)\n", "\n", "    if len(comments) == 1 and len(suggestions) == 1:\n", "        return {\n", "            \"original_instruction\": comments[0],\n", "            # We normalize new lines for the new_code to remove \\r symbols\n", "            \"new_code\": \"\\n\".join(suggestions[0].splitlines()),\n", "        }\n", "    else:\n", "        return None\n", "\n", "\n", "sample_1 = \"It might be best to check if the type is uuid.UUID instead of blindly casting to str\\r\\n\\r\\n```suggestion\\r\\n        if isinstance(field.data, uuid.UUID):\\r\\n            return\\r\\n\\r\\n        try:\\r\\n            uuid.UUID(field.data)\\r\\n        except ValueError:\\r\\n            raise ValidationError(message)\\r\\n```\"\n", "print(sample_1)\n", "print(parse_single_suggestion(sample_1))\n", "\n", "sample_2 = \"```suggestion\\r\\n        uuid.UUID(\\\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\\\"),\\r\\n```\\r\\n\\r\\nRNG in tests is bad!'\"\n", "print(sample_2)\n", "print(parse_single_suggestion(sample_2))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 21178 repos, 4418482 reviews and 78022 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Python.json\n", "Loaded 10244 repos, 2712741 reviews and 21619 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Java.json\n", "Loaded 28835 repos, 1136610 reviews and 16081 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json\n", "Loaded 16319 repos, 1759265 reviews and 37798 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json\n", "Loaded 8643 repos, 3141655 reviews and 46714 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Go.json\n", "TOTAL Loaded 85219 repos, 13168753 reviews and 200234 suggested code edits from\n", "2833\n"]}], "source": ["# These files are located at '/mnt/efs/augment/user/yury/pr_data/\n", "PATHS = [\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Python.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Java.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Go.json',\n", "]\n", "\n", "parsed_suggested_edits = []\n", "n_total_repos, n_total_reviews = 0, 0\n", "n_skip_cannot_parse_pr_id = 0\n", "\n", "for path in PATHS:\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "\n", "    n_reviews, n_suggestions = 0, 0\n", "\n", "    for repo_owner_name, repo in data.items():\n", "        repo_owner, repo_name = repo_owner_name.split('/')\n", "        for review in repo:\n", "            n_reviews += 1\n", "            parsed_suggested_edit = parse_single_suggestion(review['body'])\n", "            if parsed_suggested_edit is None:\n", "                continue\n", "            parsed_suggested_edit.update({\n", "                \"original_review_file\": path,\n", "                \"repo_owner_name\": repo_owner_name,\n", "                \"repo_owner\": repo_owner,\n", "                \"repo_name\": repo_name,\n", "            })            \n", "\n", "            re_extract_pr_from_html_url = f\"https://github.com/{repo_owner}/{repo_name}/pull/([0-9]+)#discussion_r{review['id']}\"\n", "            m = re.match(re_extract_pr_from_html_url, review[\"html_url\"])\n", "            if m is None:\n", "                # That usually means that there is a mismatch between repo_owner in the data and html_url\n", "                n_skip_cannot_parse_pr_id += 1\n", "                continue\n", "            parsed_suggested_edit[\"pr_id\"] = int(m.group(1))\n", "\n", "            n_suggestions += 1\n", "            parsed_suggested_edit.update(review)\n", "            parsed_suggested_edits.append(parsed_suggested_edit)            \n", "            \n", "    n_total_repos += len(data)\n", "    n_total_reviews += n_reviews\n", "    print('Loaded %d repos, %d reviews and %d suggested code edits from %s' % (len(data), n_reviews, n_suggestions, path))\n", "\n", "print('TOTAL Loaded %d repos, %d reviews and %d suggested code edits from' % (n_total_repos, n_total_reviews, len(parsed_suggested_edits)))\n", "print(n_skip_cannot_parse_pr_id)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["urls:  True\n"]}], "source": ["import re\n", "\n", "RE_URL = re.compile(\"https?:\\\\/\\\\/(?:www\\\\.)?[-a-zA-Z0-9@:%._\\\\+~#=]{1,256}\\\\.[a-zA-Z0-9()]{1,6}\\\\b(?:[-a-zA-Z0-9()@:%_\\\\+.~#?&\\\\/=]*)\")\n", "  \n", "def does_text_contain_url(text):\n", "    m = re.search(RE_URL, text)\n", "    return m is not None\n", " \n", " \n", "# Driver Code\n", "text = 'My Profile: https://auth.geeksforgeeks.org/user/Chinmoy%20Lenka/articles in the portal of https://www.geeksforgeeks.org/'\n", "print(\"urls: \", does_text_contain_url(text))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["import re\n", "from bs4 import BeautifulSoup\n", "\n", "\n", "@persistent_cache('/home/<USER>/data/zyte_download.jsonl')\n", "def download_raw_text(url):\n", "    api_response = requests.post(\n", "        \"https://api.zyte.com/v1/extract\",\n", "        auth=(\"ad87250664794d3b8de0edec79f09d98\", \"\"),\n", "        json={\n", "            \"url\": url,\n", "            \"browserHtml\": True,\n", "        },\n", "    )\n", "    browser_html: str = api_response.json()[\"browserHtml\"]\n", "    return browser_html\n", "\n", "\n", "def does_commit_exists_in_tree(repo_owner, repo_name, commit_sha):\n", "    url = f\"https://github.com/{repo_owner}/{repo_name}/commit/{commit_sha}\"    \n", "    webpage = download_raw_text(url)    \n", "    soup = BeautifulSoup(webpage)\n", "    warning_box = soup.find_all(\"div\", id=\"spoof-warning\")\n", "    assert len(warning_box) <= 1, url\n", "    if len(warning_box) == 0:\n", "        return False\n", "    warning_box = warning_box[0]\n", "    return 'hidden' in warning_box.attrs    \n", "\n", "\n", "# webpage_true = download_raw_text('https://github.com/public-apis/public-apis/commit/97cb773f4517dfa04fa3b49b373b39e9461388dd')    \n", "# webpage_false = download_raw_text('https://github.com/feast-dev/feast/commit/6a99cd9e07a98559f7db2d5cddaf64994c480cb4')    \n", "print(does_commit_exists_in_tree('public-apis', 'public-apis', '97cb773f4517dfa04fa3b49b373b39e9461388dd'))\n", "print(does_commit_exists_in_tree('feast-dev', 'feast', '6a99cd9e07a98559f7db2d5cddaf64994c480cb4'))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/200234 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["  4%|▍         | 7896/200234 [11:38<4:43:28, 11.31it/s, mined_suggestions=17, n_fixed_old_code=0, n_failed_to_fix_old_code_all=1]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb Cell 11\u001b[0m line \u001b[0;36m1\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=182'>183</a>\u001b[0m     \u001b[39mcontinue\u001b[39;00m\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=184'>185</a>\u001b[0m owner_who_has_commit \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[0;32m--> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=185'>186</a>\u001b[0m \u001b[39mif\u001b[39;00m does_commit_exists_in_tree(\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=186'>187</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39;49m\u001b[39mauthor\u001b[39;49m\u001b[39m'\u001b[39;49m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=187'>188</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39;49m\u001b[39mrepo_name\u001b[39;49m\u001b[39m'\u001b[39;49m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=188'>189</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39;49m\u001b[39moriginal_commit\u001b[39;49m\u001b[39m'\u001b[39;49m][\u001b[39m'\u001b[39;49m\u001b[39mcommit_sha\u001b[39;49m\u001b[39m'\u001b[39;49m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=189'>190</a>\u001b[0m ):\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=190'>191</a>\u001b[0m     owner_who_has_commit \u001b[39m=\u001b[39m suggested_edit[\u001b[39m'\u001b[39m\u001b[39mauthor\u001b[39m\u001b[39m'\u001b[39m]\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=191'>192</a>\u001b[0m \u001b[39melif\u001b[39;00m does_commit_exists_in_tree(\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=192'>193</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39m\u001b[39mrepo_owner\u001b[39m\u001b[39m'\u001b[39m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=193'>194</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39m\u001b[39mrepo_name\u001b[39m\u001b[39m'\u001b[39m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=194'>195</a>\u001b[0m     suggested_edit[\u001b[39m'\u001b[39m\u001b[39moriginal_commit\u001b[39m\u001b[39m'\u001b[39m][\u001b[39m'\u001b[39m\u001b[39mcommit_sha\u001b[39m\u001b[39m'\u001b[39m],\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=195'>196</a>\u001b[0m ):\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb Cell 11\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mdoes_commit_exists_in_tree\u001b[39m(repo_owner, repo_name, commit_sha):\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m     url \u001b[39m=\u001b[39m \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mhttps://github.com/\u001b[39m\u001b[39m{\u001b[39;00mrepo_owner\u001b[39m}\u001b[39;00m\u001b[39m/\u001b[39m\u001b[39m{\u001b[39;00mrepo_name\u001b[39m}\u001b[39;00m\u001b[39m/commit/\u001b[39m\u001b[39m{\u001b[39;00mcommit_sha\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m    \n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m     webpage \u001b[39m=\u001b[39m download_raw_text(url)    \n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m     soup \u001b[39m=\u001b[39m BeautifulSoup(webpage)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m     warning_box \u001b[39m=\u001b[39m soup\u001b[39m.\u001b[39mfind_all(\u001b[39m\"\u001b[39m\u001b[39mdiv\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39mid\u001b[39m\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mspoof-warning\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb Cell 11\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m     key \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39mdumps((args, kwargs), sort_keys\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m \u001b[39mif\u001b[39;00m key \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m cache:\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m     cache[key] \u001b[39m=\u001b[39m func(\u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     \u001b[39m# Save the updated cache to the file\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m     \u001b[39mwith\u001b[39;00m \u001b[39mopen\u001b[39m(filename, \u001b[39m'\u001b[39m\u001b[39ma\u001b[39m\u001b[39m'\u001b[39m) \u001b[39mas\u001b[39;00m f:\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb Cell 11\u001b[0m line \u001b[0;36m7\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39m@persistent_cache\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m/home/<USER>/data/zyte_download.jsonl\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mdownload_raw_text\u001b[39m(url):\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m     api_response \u001b[39m=\u001b[39m requests\u001b[39m.\u001b[39;49mpost(\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mhttps://api.zyte.com/v1/extract\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m         auth\u001b[39m=\u001b[39;49m(\u001b[39m\"\u001b[39;49m\u001b[39mad87250664794d3b8de0edec79f09d98\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39m\"\u001b[39;49m\u001b[39m\"\u001b[39;49m),\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m         json\u001b[39m=\u001b[39;49m{\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m             \u001b[39m\"\u001b[39;49m\u001b[39murl\u001b[39;49m\u001b[39m\"\u001b[39;49m: url,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m             \u001b[39m\"\u001b[39;49m\u001b[39mbrowserHtml\u001b[39;49m\u001b[39m\"\u001b[39;49m: \u001b[39mTrue\u001b[39;49;00m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m         },\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m     )\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m     browser_html: \u001b[39mstr\u001b[39m \u001b[39m=\u001b[39m api_response\u001b[39m.\u001b[39mjson()[\u001b[39m\"\u001b[39m\u001b[39mbrowserHtml\u001b[39m\u001b[39m\"\u001b[39m]\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_for_eval.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m browser_html\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[0;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mpost\u001b[39m(url, data\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, json\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[1;32m    105\u001b[0m \n\u001b[1;32m    106\u001b[0m \u001b[39m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[39m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 115\u001b[0m     \u001b[39mreturn\u001b[39;00m request(\u001b[39m\"\u001b[39;49m\u001b[39mpost\u001b[39;49m\u001b[39m\"\u001b[39;49m, url, data\u001b[39m=\u001b[39;49mdata, json\u001b[39m=\u001b[39;49mjson, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[39m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[39m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[39m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[39mwith\u001b[39;00m sessions\u001b[39m.\u001b[39mSession() \u001b[39mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[39mreturn\u001b[39;00m session\u001b[39m.\u001b[39;49mrequest(method\u001b[39m=\u001b[39;49mmethod, url\u001b[39m=\u001b[39;49murl, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[39m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mtimeout\u001b[39m\u001b[39m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mallow_redirects\u001b[39m\u001b[39m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[39m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49msend(prep, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49msend_kwargs)\n\u001b[1;32m    591\u001b[0m \u001b[39mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[39m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[39m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[39m=\u001b[39m adapter\u001b[39m.\u001b[39;49msend(request, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m    705\u001b[0m \u001b[39m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[39m=\u001b[39m preferred_clock() \u001b[39m-\u001b[39m start\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[39m=\u001b[39m TimeoutSauce(connect\u001b[39m=\u001b[39mtimeout, read\u001b[39m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39;49murlopen(\n\u001b[1;32m    487\u001b[0m         method\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mmethod,\n\u001b[1;32m    488\u001b[0m         url\u001b[39m=\u001b[39;49murl,\n\u001b[1;32m    489\u001b[0m         body\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mbody,\n\u001b[1;32m    490\u001b[0m         headers\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mheaders,\n\u001b[1;32m    491\u001b[0m         redirect\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    492\u001b[0m         assert_same_host\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    493\u001b[0m         preload_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    494\u001b[0m         decode_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    495\u001b[0m         retries\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mmax_retries,\n\u001b[1;32m    496\u001b[0m         timeout\u001b[39m=\u001b[39;49mtimeout,\n\u001b[1;32m    497\u001b[0m         chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[1;32m    498\u001b[0m     )\n\u001b[1;32m    500\u001b[0m \u001b[39mexcept\u001b[39;00m (ProtocolError, \u001b[39mOSError\u001b[39;00m) \u001b[39mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m(err, request\u001b[39m=\u001b[39mrequest)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:715\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    712\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    714\u001b[0m \u001b[39m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 715\u001b[0m httplib_response \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_make_request(\n\u001b[1;32m    716\u001b[0m     conn,\n\u001b[1;32m    717\u001b[0m     method,\n\u001b[1;32m    718\u001b[0m     url,\n\u001b[1;32m    719\u001b[0m     timeout\u001b[39m=\u001b[39;49mtimeout_obj,\n\u001b[1;32m    720\u001b[0m     body\u001b[39m=\u001b[39;49mbody,\n\u001b[1;32m    721\u001b[0m     headers\u001b[39m=\u001b[39;49mheaders,\n\u001b[1;32m    722\u001b[0m     chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[1;32m    723\u001b[0m )\n\u001b[1;32m    725\u001b[0m \u001b[39m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[39m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[39m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    728\u001b[0m \u001b[39m# mess.\u001b[39;00m\n\u001b[1;32m    729\u001b[0m response_conn \u001b[39m=\u001b[39m conn \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m release_conn \u001b[39melse\u001b[39;00m \u001b[39mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:467\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    462\u001b[0m             httplib_response \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39mgetresponse()\n\u001b[1;32m    463\u001b[0m         \u001b[39mexcept\u001b[39;00m \u001b[39mBaseException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    464\u001b[0m             \u001b[39m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    465\u001b[0m             \u001b[39m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    466\u001b[0m             \u001b[39m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[0;32m--> 467\u001b[0m             six\u001b[39m.\u001b[39;49mraise_from(e, \u001b[39mNone\u001b[39;49;00m)\n\u001b[1;32m    468\u001b[0m \u001b[39mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    469\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_raise_timeout(err\u001b[39m=\u001b[39me, url\u001b[39m=\u001b[39murl, timeout_value\u001b[39m=\u001b[39mread_timeout)\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:462\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    459\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mTypeError\u001b[39;00m:\n\u001b[1;32m    460\u001b[0m     \u001b[39m# Python 3\u001b[39;00m\n\u001b[1;32m    461\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 462\u001b[0m         httplib_response \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39;49mgetresponse()\n\u001b[1;32m    463\u001b[0m     \u001b[39mexcept\u001b[39;00m \u001b[39mBaseException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    464\u001b[0m         \u001b[39m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    465\u001b[0m         \u001b[39m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    466\u001b[0m         \u001b[39m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[1;32m    467\u001b[0m         six\u001b[39m.\u001b[39mraise_from(e, \u001b[39mNone\u001b[39;00m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:1377\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1375\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m   1376\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m-> 1377\u001b[0m         response\u001b[39m.\u001b[39;49mbegin()\n\u001b[1;32m   1378\u001b[0m     \u001b[39mexcept\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m:\n\u001b[1;32m   1379\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:320\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    318\u001b[0m \u001b[39m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    319\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[0;32m--> 320\u001b[0m     version, status, reason \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_read_status()\n\u001b[1;32m    321\u001b[0m     \u001b[39mif\u001b[39;00m status \u001b[39m!=\u001b[39m CONTINUE:\n\u001b[1;32m    322\u001b[0m         \u001b[39mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:281\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    280\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_read_status\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[0;32m--> 281\u001b[0m     line \u001b[39m=\u001b[39m \u001b[39mstr\u001b[39m(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mfp\u001b[39m.\u001b[39;49mreadline(_MAXLINE \u001b[39m+\u001b[39;49m \u001b[39m1\u001b[39;49m), \u001b[39m\"\u001b[39m\u001b[39miso-8859-1\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m    282\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mlen\u001b[39m(line) \u001b[39m>\u001b[39m _MAXLINE:\n\u001b[1;32m    283\u001b[0m         \u001b[39mraise\u001b[39;00m LineTooLong(\u001b[39m\"\u001b[39m\u001b[39mstatus line\u001b[39m\u001b[39m\"\u001b[39m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/socket.py:704\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    702\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[1;32m    703\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 704\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sock\u001b[39m.\u001b[39;49mrecv_into(b)\n\u001b[1;32m    705\u001b[0m     \u001b[39mexcept\u001b[39;00m timeout:\n\u001b[1;32m    706\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_timeout_occurred \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/ssl.py:1275\u001b[0m, in \u001b[0;36mSSLSocket.recv_into\u001b[0;34m(self, buffer, nbytes, flags)\u001b[0m\n\u001b[1;32m   1271\u001b[0m     \u001b[39mif\u001b[39;00m flags \u001b[39m!=\u001b[39m \u001b[39m0\u001b[39m:\n\u001b[1;32m   1272\u001b[0m         \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m   1273\u001b[0m           \u001b[39m\"\u001b[39m\u001b[39mnon-zero flags not allowed in calls to recv_into() on \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m\"\u001b[39m \u001b[39m%\u001b[39m\n\u001b[1;32m   1274\u001b[0m           \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__class__\u001b[39m)\n\u001b[0;32m-> 1275\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mread(nbytes, buffer)\n\u001b[1;32m   1276\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m   1277\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39msuper\u001b[39m()\u001b[39m.\u001b[39mrecv_into(buffer, nbytes, flags)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/ssl.py:1133\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1131\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m   1132\u001b[0m     \u001b[39mif\u001b[39;00m buffer \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[0;32m-> 1133\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sslobj\u001b[39m.\u001b[39;49mread(\u001b[39mlen\u001b[39;49m, buffer)\n\u001b[1;32m   1134\u001b[0m     \u001b[39melse\u001b[39;00m:\n\u001b[1;32m   1135\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_sslobj\u001b[39m.\u001b[39mread(\u001b[39mlen\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import tqdm \n", "import editdistance # pip install editdistance\n", "from urlextract import URLExtract # pip install urlextract\n", "\n", "MAX_WORDS_IN_COMMENT = 25\n", "MIN_EDIT_DISTANCE = 8\n", "\n", "filtered_suggested_edit = []\n", "\n", "extractor = URLExtract()\n", "\n", "\n", "n_too_many_words = 0\n", "n_original_line_is_missing = 0\n", "n_failed_download_commits = 0\n", "n_instruction_too_vague = 0\n", "n_cannot_download_pr_info_restapi = 0\n", "n_cannot_download_comment_info_restapi = 0\n", "n_failed_to_fix_old_code, n_fixed_old_code = 0, 0\n", "n_start_line_is_broken = 0\n", "n_failed_to_fix_old_code_again = 0\n", "n_too_small_edit_distance = 0\n", "n_no_relevant_urls = 0\n", "n_original_commit_not_in_commits = 0\n", "n_commits_in_pr_is_none = 0\n", "n_original_commit_not_in_tree = 0\n", "\n", "def download_file_info(repo_owner, repo_name, path, commit_sha, start_line, end_line):\n", "    try:\n", "        file = download_file(\n", "            repo_owner, repo_name, path, commit_sha)\n", "        return {\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,\n", "            \"commit_sha\": commit_sha,\n", "            \"file_info\": file,\n", "            \"file\": file['decoded_content'],\n", "        }   \n", "    except ValueError:\n", "        return None    \n", "    \n", "\n", "KEYS_TO_COPY = [\n", "\t\"original_instruction\",\n", "\t\"new_code\",\n", "\t\"original_review_file\",\n", "\t\"repo_owner_name\",\n", "\t\"repo_owner\",\n", "\t\"repo_name\",\n", "\t\"html_url\",\n", "\t\"path\",\n", "\t\"id\",\n", "    \"pr_id\",\n", "]\n", "\n", "with tqdm.tqdm(total=len(parsed_suggested_edits)) as pbar:    \n", "    for parsed_suggested_edit in parsed_suggested_edits:\n", "        pbar.update(1)\n", "\n", "        suggested_edit = {}\n", "        for k in KEYS_TO_COPY:\n", "            suggested_edit[k] = parsed_suggested_edit[k]\n", "\n", "        original_instruction = suggested_edit['original_instruction']\n", "        # if len(original_instruction.split()) > MAX_WORDS_IN_COMMENT:\n", "        #     n_too_many_words += 1\n", "        #     continue\n", "\n", "        urls = extractor.find_urls(original_instruction)\n", "        pull_url = f\"https://github.com/{suggested_edit['repo_owner']}/{suggested_edit['repo_name']}/pull\"\n", "        issues_url = f\"https://github.com/{suggested_edit['repo_owner']}/{suggested_edit['repo_name']}/issues\"\n", "        relevant_urls = [\n", "            url\n", "            for url in urls\n", "            if (\n", "                suggested_edit['repo_owner_name'] in url and\n", "                pull_url not in url and\n", "                issues_url not in url\n", "            )\n", "        ]\n", "\n", "        if len(relevant_urls) == 0:\n", "            n_no_relevant_urls += 1\n", "            continue\n", "\n", "        try:\n", "            full_pr_info = download_pr_info(\n", "                suggested_edit['repo_owner'],\n", "                suggested_edit['repo_name'],\n", "                suggested_edit['pr_id'],\n", "            )\n", "            if full_pr_info is None:\n", "                n_cannot_download_pr_info_restapi += 1\n", "                continue\n", "            suggested_edit.update({\n", "                \"full_pr_info\": full_pr_info,\n", "                \"author\": full_pr_info[\"user\"][\"login\"],                \n", "            })\n", "        except ValueError:\n", "            n_cannot_download_pr_info_restapi += 1\n", "            continue                \n", "\n", "        try:\n", "            full_comment_info = download_review_comment(\n", "                suggested_edit['repo_owner'],\n", "                suggested_edit['repo_name'],\n", "                suggested_edit['id'],\n", "            )\n", "            suggested_edit[\"full_comment_info\"] = full_comment_info\n", "        except ValueError:\n", "            n_cannot_download_comment_info_restapi += 1\n", "            continue        \n", "\n", "        end_line = full_comment_info['original_line']\n", "        if end_line is None:\n", "            n_original_line_is_missing += 1\n", "            continue\n", "        start_line = (full_comment_info['original_start_line'] or end_line) - 1\n", "        assert end_line is not None\n", "        assert start_line is not None\n", "\n", "        graphql_comment_info = query_graphql_comment(suggested_edit[\"full_comment_info\"][\"node_id\"])\n", "        suggested_edit[\"graphql_comment_info\"] = graphql_comment_info\n", "        graphql_old_code, graphql_new_code =  extract_old_new_code(graphql_comment_info[\"bodyHTML\"])\n", "\n", "        original_commit_sha = full_comment_info['original_commit_id']\n", "        \n", "        original_commit_info = download_file_info(\n", "            commit_sha=original_commit_sha,\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            repo_owner=suggested_edit['repo_owner'],\n", "            repo_name=suggested_edit['repo_name'],\n", "            path=suggested_edit['path'],\n", "        )     \n", "        if original_commit_info is None:\n", "            n_failed_download_commits += 1\n", "            continue\n", "\n", "        suggested_edit[\"original_commit\"] = original_commit_info        \n", "        old_file = suggested_edit['original_commit']['file']\n", "\n", "        adjusted_start_end_line = recompute_old_code(\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            old_file=old_file,\n", "            new_code=suggested_edit['new_code'],\n", "            graphql_old_code=graphql_old_code,\n", "            graphql_new_code=graphql_new_code,\n", "        )\n", "        if adjusted_start_end_line is None:\n", "            n_failed_to_fix_old_code += 1\n", "            continue\n", "\n", "        assert adjusted_start_end_line is not None\n", "        if adjusted_start_end_line[0] != start_line or adjusted_start_end_line[1] != end_line:\n", "            n_fixed_old_code += 1\n", "            start_line, end_line = adjusted_start_end_line\n", "\n", "        old_file_lines = old_file.splitlines()\n", "\n", "        if start_line >= len(old_file_lines):\n", "            n_start_line_is_broken += 1\n", "            continue\n", "            \n", "        end_line = min(end_line, len(old_file_lines))\n", "        prefix = '\\n'.join(old_file_lines[:start_line])\n", "        suffix = '\\n'.join(old_file_lines[end_line: ])        \n", "        # old_code = '\\n'.join(old_file_lines[start_line: end_line])        \n", "        old_code = slice_code(old_file, start_line, end_line, True)\n", "\n", "        if graphql_old_code != old_code:\n", "            n_failed_to_fix_old_code_again += 1\n", "            continue        \n", "\n", "        assert graphql_old_code == old_code\n", "        assert graphql_new_code == suggested_edit['new_code']\n", "\n", "        \n", "        old_new_code_edit_distance = editdistance.eval(suggested_edit['new_code'], old_code)\n", "        if old_new_code_edit_distance < MIN_EDIT_DISTANCE:\n", "            n_too_small_edit_distance += 1\n", "            continue\n", "\n", "        owner_who_has_commit = None\n", "        if does_commit_exists_in_tree(\n", "            suggested_edit['author'],\n", "            suggested_edit['repo_name'],\n", "            suggested_edit['original_commit']['commit_sha'],\n", "        ):\n", "            owner_who_has_commit = suggested_edit['author']\n", "        elif does_commit_exists_in_tree(\n", "            suggested_edit['repo_owner'],\n", "            suggested_edit['repo_name'],\n", "            suggested_edit['original_commit']['commit_sha'],\n", "        ):\n", "            owner_who_has_commit = suggested_edit['repo_owner']\n", "        else:                \n", "            n_original_commit_not_in_tree += 1\n", "            continue\n", "\n", "        # commits_in_pr = download_commit_ids_for_pr(\n", "        #     repo_owner=suggested_edit['repo_owner'],\n", "        #     repo_name=suggested_edit['repo_name'],            \n", "        #     pr_id=suggested_edit['pr_id'])\n", "        \n", "        # if commits_in_pr is None:\n", "        #     n_commits_in_pr_is_none += 1\n", "        #     continue\n", "        \n", "        # commits_sha = [c['sha'] for c in commits_in_pr]\n", "        # if suggested_edit['original_commit']['commit_sha'] not in commits_sha:\n", "        #     n_original_commit_not_in_commits += 1\n", "        #     continue\n", "\n", "        suggested_edit.update({\n", "            \"edit_distance\": old_new_code_edit_distance,\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,            \n", "            'old_file': old_file,\n", "            'old_code': slice_code(old_file, start_line, end_line, False),\n", "            'prefix': prefix,\n", "            'suffix': suffix,\n", "            # \"commit_html_url\": f\"https://github.com/{owner_who_has_commit}/{suggested_edit['repo_name']}/commit/{original_commit_sha}\",\n", "            # 'owner_who_has_commit': owner_who_has_commit,\n", "            # 'commits_in_pr': commits_in_pr,\n", "        })\n", "\n", "        filtered_suggested_edit.append(suggested_edit)\n", "        pbar.set_postfix({\"mined_suggestions\": len(filtered_suggested_edit), \"n_fixed_old_code\": n_fixed_old_code, \"n_failed_to_fix_old_code_all\": n_failed_to_fix_old_code + n_start_line_is_broken + n_failed_to_fix_old_code_again})\n", "\n", "print('-- removed %d because comment had too many words' % n_too_many_words)\n", "print('-- removed %d because comment does not containt relevant URLs' % n_no_relevant_urls)\n", "print('-- removed %d because cannot download PR info from REST API' % n_cannot_download_pr_info_restapi)\n", "print('-- removed %d because cannot download comment info from REST API' % n_cannot_download_comment_info_restapi)\n", "print('-- removed %d because lines are missing' % n_original_line_is_missing)\n", "print('-- removed %d because failed to download commits' % n_failed_download_commits)\n", "print('-- removed %d because failed to fix old code given GraphQL result' % n_failed_to_fix_old_code)\n", "print('-- removed %d because start_line is outside of the file' % n_start_line_is_broken)\n", "print('-- removed %d because failed to fix old code given GraphQL result (nevertheless)' % n_failed_to_fix_old_code_again)\n", "print('-- removed %d because edit distance between old and new code is too small' % n_too_small_edit_distance)\n", "print('-- removed %d because commits in PR is None' % n_commits_in_pr_is_none)\n", "print('-- removed %d because original commit is not amongst commits' % n_original_commit_not_in_commits)\n", "print('-- removed %d because original commit is in the git tree' % n_original_commit_not_in_tree)\n", "\n", "print('-- recovered %d corrupted files by comparing with GraphQL result' % n_fixed_old_code)\n", "\n", "print('Final suggestions: %d' % len(filtered_suggested_edit))"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'original_instruction': 'https://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339',\n", " 'new_code': '\\nimport math',\n", " 'original_review_file': '/home/<USER>/data/mined-comments-25stars-25prs-Python.json',\n", " 'repo_owner_name': 'TheAlgorithms/Python',\n", " 'repo_owner': 'TheAlgorithms',\n", " 'repo_name': '<PERSON>',\n", " 'html_url': 'https://github.com/TheAlgorithms/Python/pull/1845#discussion_r406234397',\n", " 'path': 'data_structures/Math/PowerfulArray.py',\n", " 'id': 406234397,\n", " 'pr_id': 1845,\n", " 'full_pr_info': {'url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845',\n", "  'id': 401394407,\n", "  'node_id': 'MDExOlB1bGxSZXF1ZXN0NDAxMzk0NDA3',\n", "  'html_url': 'https://github.com/TheAlgorithms/Python/pull/1845',\n", "  'diff_url': 'https://github.com/TheAlgorithms/Python/pull/1845.diff',\n", "  'patch_url': 'https://github.com/TheAlgorithms/Python/pull/1845.patch',\n", "  'issue_url': 'https://api.github.com/repos/TheAlgorithms/Python/issues/1845',\n", "  'number': 1845,\n", "  'state': 'closed',\n", "  'locked': <PERSON><PERSON><PERSON>,\n", "  'title': 'added data structure questions',\n", "  'user': {'login': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "   'id': 22793422,\n", "   'node_id': 'MDQ6VXNlcjIyNzkzNDIy',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/22793422?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/Shrutikabansal',\n", "   'html_url': 'https://github.com/Shrutikabansal',\n", "   'followers_url': 'https://api.github.com/users/Shrutikabansal/followers',\n", "   'following_url': 'https://api.github.com/users/Shrutikabansal/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/Shrutikabansal/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/Shrutikabansal/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/Shrutikabansal/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/Shrutikabansal/orgs',\n", "   'repos_url': 'https://api.github.com/users/Shrutikabansal/repos',\n", "   'events_url': 'https://api.github.com/users/Shrutikabansal/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/Shrutikabansal/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': '### **Describe your change:**\\r\\n\\r\\nAdded Algorithm related to DynamicPrograming and Backtracking\\r\\n\\r\\n* [x] Add an algorithm?\\r\\n* [ ] Fix a bug or typo in an existing algorithm?\\r\\n* [ ] Documentation change?\\r\\n\\r\\n### **Checklist:**\\r\\n* [ ] I have read [CONTRIBUTING.md](https://github.com/TheAlgorithms/Python/blob/master/CONTRIBUTING.md).\\r\\n* [ ] This pull request is all my own work -- I have not plagiarized.\\r\\n* [ ] I know that pull requests will not be merged if they fail the automated tests.\\r\\n* [ ] This PR only changes one algorithm file.  To ease review, please open separate PRs for separate algorithms.\\r\\n* [x] All new Python files are placed inside an existing directory.\\r\\n* [ ] All filenames are in all lowercase characters with no spaces or dashes.\\r\\n* [ ] All functions and variable names follow Python naming conventions.\\r\\n* [ ] All function parameters and return values are annotated with Python [type hints](https://docs.python.org/3/library/typing.html).\\r\\n* [ ] All functions have [doctests](https://docs.python.org/3/library/doctest.html) that pass the automated testing.\\r\\n* [ ] All new algorithms have a URL in its comments that points to Wikipedia or other similar explanation.\\r\\n* [ ] If this pull request resolves one or more open issues then the commit message contains `Fixes: #{$ISSUE_NO}`.\\r\\n',\n", "  'created_at': '2020-04-09T12:26:45Z',\n", "  'updated_at': '2020-04-20T12:26:24Z',\n", "  'closed_at': '2020-04-20T12:26:24Z',\n", "  'merged_at': None,\n", "  'merge_commit_sha': '274f2e1f45c71e647ff79e65a9d18de55f3521d1',\n", "  'assignee': None,\n", "  'assignees': [],\n", "  'requested_reviewers': [],\n", "  'requested_teams': [],\n", "  'labels': [],\n", "  'milestone': None,\n", "  'draft': <PERSON><PERSON><PERSON>,\n", "  'commits_url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845/commits',\n", "  'review_comments_url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845/comments',\n", "  'review_comment_url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/comments{/number}',\n", "  'comments_url': 'https://api.github.com/repos/TheAlgorithms/Python/issues/1845/comments',\n", "  'statuses_url': 'https://api.github.com/repos/TheAlgorithms/Python/statuses/1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "  'head': {'label': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:master',\n", "   'ref': 'master',\n", "   'sha': '1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "   'user': {'login': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "    'id': 22793422,\n", "    'node_id': 'MDQ6VXNlcjIyNzkzNDIy',\n", "    'avatar_url': 'https://avatars.githubusercontent.com/u/22793422?v=4',\n", "    'gravatar_id': '',\n", "    'url': 'https://api.github.com/users/Shrutikabansal',\n", "    'html_url': 'https://github.com/Shrutikabansal',\n", "    'followers_url': 'https://api.github.com/users/Shrutikabansal/followers',\n", "    'following_url': 'https://api.github.com/users/Shrutikabansal/following{/other_user}',\n", "    'gists_url': 'https://api.github.com/users/Shrutikabansal/gists{/gist_id}',\n", "    'starred_url': 'https://api.github.com/users/Shrutikabansal/starred{/owner}{/repo}',\n", "    'subscriptions_url': 'https://api.github.com/users/Shrutikabansal/subscriptions',\n", "    'organizations_url': 'https://api.github.com/users/Shrutikabansal/orgs',\n", "    'repos_url': 'https://api.github.com/users/Shrutikabansal/repos',\n", "    'events_url': 'https://api.github.com/users/Shrutikabansal/events{/privacy}',\n", "    'received_events_url': 'https://api.github.com/users/Shrutikabansal/received_events',\n", "    'type': 'User',\n", "    'site_admin': <PERSON><PERSON><PERSON>},\n", "   'repo': None},\n", "  'base': {'label': 'TheAlgorithms:master',\n", "   'ref': 'master',\n", "   'sha': '8f2c9932e09948a046f8d11b18f7c634ee4161fe',\n", "   'user': {'login': 'TheAlgorithms',\n", "    'id': 20487725,\n", "    'node_id': 'MDEyOk9yZ2FuaXphdGlvbjIwNDg3NzI1',\n", "    'avatar_url': 'https://avatars.githubusercontent.com/u/20487725?v=4',\n", "    'gravatar_id': '',\n", "    'url': 'https://api.github.com/users/TheAlgorithms',\n", "    'html_url': 'https://github.com/TheAlgorithms',\n", "    'followers_url': 'https://api.github.com/users/TheAlgorithms/followers',\n", "    'following_url': 'https://api.github.com/users/TheAlgorithms/following{/other_user}',\n", "    'gists_url': 'https://api.github.com/users/TheAlgorithms/gists{/gist_id}',\n", "    'starred_url': 'https://api.github.com/users/TheAlgorithms/starred{/owner}{/repo}',\n", "    'subscriptions_url': 'https://api.github.com/users/TheAlgorithms/subscriptions',\n", "    'organizations_url': 'https://api.github.com/users/TheAlgorithms/orgs',\n", "    'repos_url': 'https://api.github.com/users/TheAlgorithms/repos',\n", "    'events_url': 'https://api.github.com/users/TheAlgorithms/events{/privacy}',\n", "    'received_events_url': 'https://api.github.com/users/TheAlgorithms/received_events',\n", "    'type': 'Organization',\n", "    'site_admin': <PERSON><PERSON><PERSON>},\n", "   'repo': {'id': 63476337,\n", "    'node_id': 'MDEwOlJlcG9zaXRvcnk2MzQ3NjMzNw==',\n", "    'name': '<PERSON>',\n", "    'full_name': 'TheAlgorithms/Python',\n", "    'private': <PERSON><PERSON><PERSON>,\n", "    'owner': {'login': 'TheAlgorithms',\n", "     'id': 20487725,\n", "     'node_id': 'MDEyOk9yZ2FuaXphdGlvbjIwNDg3NzI1',\n", "     'avatar_url': 'https://avatars.githubusercontent.com/u/20487725?v=4',\n", "     'gravatar_id': '',\n", "     'url': 'https://api.github.com/users/TheAlgorithms',\n", "     'html_url': 'https://github.com/TheAlgorithms',\n", "     'followers_url': 'https://api.github.com/users/TheAlgorithms/followers',\n", "     'following_url': 'https://api.github.com/users/TheAlgorithms/following{/other_user}',\n", "     'gists_url': 'https://api.github.com/users/TheAlgorithms/gists{/gist_id}',\n", "     'starred_url': 'https://api.github.com/users/TheAlgorithms/starred{/owner}{/repo}',\n", "     'subscriptions_url': 'https://api.github.com/users/TheAlgorithms/subscriptions',\n", "     'organizations_url': 'https://api.github.com/users/TheAlgorithms/orgs',\n", "     'repos_url': 'https://api.github.com/users/TheAlgorithms/repos',\n", "     'events_url': 'https://api.github.com/users/TheAlgorithms/events{/privacy}',\n", "     'received_events_url': 'https://api.github.com/users/TheAlgorithms/received_events',\n", "     'type': 'Organization',\n", "     'site_admin': <PERSON><PERSON><PERSON>},\n", "    'html_url': 'https://github.com/TheAlgorithms/Python',\n", "    'description': 'All Algorithms implemented in Python',\n", "    'fork': <PERSON><PERSON><PERSON>,\n", "    'url': 'https://api.github.com/repos/TheAlgorithms/Python',\n", "    'forks_url': 'https://api.github.com/repos/TheAlgorithms/Python/forks',\n", "    'keys_url': 'https://api.github.com/repos/TheAlgorithms/Python/keys{/key_id}',\n", "    'collaborators_url': 'https://api.github.com/repos/TheAlgorithms/Python/collaborators{/collaborator}',\n", "    'teams_url': 'https://api.github.com/repos/TheAlgorithms/Python/teams',\n", "    'hooks_url': 'https://api.github.com/repos/TheAlgorithms/Python/hooks',\n", "    'issue_events_url': 'https://api.github.com/repos/TheAlgorithms/Python/issues/events{/number}',\n", "    'events_url': 'https://api.github.com/repos/TheAlgorithms/Python/events',\n", "    'assignees_url': 'https://api.github.com/repos/TheAlgorithms/Python/assignees{/user}',\n", "    'branches_url': 'https://api.github.com/repos/TheAlgorithms/Python/branches{/branch}',\n", "    'tags_url': 'https://api.github.com/repos/TheAlgorithms/Python/tags',\n", "    'blobs_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/blobs{/sha}',\n", "    'git_tags_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/tags{/sha}',\n", "    'git_refs_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/refs{/sha}',\n", "    'trees_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/trees{/sha}',\n", "    'statuses_url': 'https://api.github.com/repos/TheAlgorithms/Python/statuses/{sha}',\n", "    'languages_url': 'https://api.github.com/repos/TheAlgorithms/Python/languages',\n", "    'stargazers_url': 'https://api.github.com/repos/TheAlgorithms/Python/stargazers',\n", "    'contributors_url': 'https://api.github.com/repos/TheAlgorithms/Python/contributors',\n", "    'subscribers_url': 'https://api.github.com/repos/TheAlgorithms/Python/subscribers',\n", "    'subscription_url': 'https://api.github.com/repos/TheAlgorithms/Python/subscription',\n", "    'commits_url': 'https://api.github.com/repos/TheAlgorithms/Python/commits{/sha}',\n", "    'git_commits_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/commits{/sha}',\n", "    'comments_url': 'https://api.github.com/repos/TheAlgorithms/Python/comments{/number}',\n", "    'issue_comment_url': 'https://api.github.com/repos/TheAlgorithms/Python/issues/comments{/number}',\n", "    'contents_url': 'https://api.github.com/repos/TheAlgorithms/Python/contents/{+path}',\n", "    'compare_url': 'https://api.github.com/repos/TheAlgorithms/Python/compare/{base}...{head}',\n", "    'merges_url': 'https://api.github.com/repos/TheAlgorithms/Python/merges',\n", "    'archive_url': 'https://api.github.com/repos/TheAlgorithms/Python/{archive_format}{/ref}',\n", "    'downloads_url': 'https://api.github.com/repos/TheAlgorithms/Python/downloads',\n", "    'issues_url': 'https://api.github.com/repos/TheAlgorithms/Python/issues{/number}',\n", "    'pulls_url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls{/number}',\n", "    'milestones_url': 'https://api.github.com/repos/TheAlgorithms/Python/milestones{/number}',\n", "    'notifications_url': 'https://api.github.com/repos/TheAlgorithms/Python/notifications{?since,all,participating}',\n", "    'labels_url': 'https://api.github.com/repos/TheAlgorithms/Python/labels{/name}',\n", "    'releases_url': 'https://api.github.com/repos/TheAlgorithms/Python/releases{/id}',\n", "    'deployments_url': 'https://api.github.com/repos/TheAlgorithms/Python/deployments',\n", "    'created_at': '2016-07-16T09:44:01Z',\n", "    'updated_at': '2024-02-08T03:34:10Z',\n", "    'pushed_at': '2024-02-05T19:48:15Z',\n", "    'git_url': 'git://github.com/TheAlgorithms/Python.git',\n", "    'ssh_url': '**************:TheAlgorithms/Python.git',\n", "    'clone_url': 'https://github.com/TheAlgorithms/Python.git',\n", "    'svn_url': 'https://github.com/TheAlgorithms/Python',\n", "    'homepage': 'https://the-algorithms.com/',\n", "    'size': 14349,\n", "    'stargazers_count': 175552,\n", "    'watchers_count': 175552,\n", "    'language': 'Python',\n", "    'has_issues': True,\n", "    'has_projects': True,\n", "    'has_downloads': True,\n", "    'has_wiki': True,\n", "    'has_pages': <PERSON><PERSON><PERSON>,\n", "    'has_discussions': True,\n", "    'forks_count': 43845,\n", "    'mirror_url': None,\n", "    'archived': <PERSON><PERSON><PERSON>,\n", "    'disabled': <PERSON><PERSON><PERSON>,\n", "    'open_issues_count': 224,\n", "    'license': {'key': 'mit',\n", "     'name': 'MIT License',\n", "     'spdx_id': 'MIT',\n", "     'url': 'https://api.github.com/licenses/mit',\n", "     'node_id': 'MDc6TGljZW5zZTEz'},\n", "    'allow_forking': True,\n", "    'is_template': <PERSON><PERSON><PERSON>,\n", "    'web_commit_signoff_required': False,\n", "    'topics': ['algorithm',\n", "     'algorithm-competitions',\n", "     'algorithms-implemented',\n", "     'algos',\n", "     'community-driven',\n", "     'education',\n", "     'hacktoberfest',\n", "     'interview',\n", "     'learn',\n", "     'practice',\n", "     'python',\n", "     'searches',\n", "     'sorting-algorithms',\n", "     'sorts'],\n", "    'visibility': 'public',\n", "    'forks': 43845,\n", "    'open_issues': 224,\n", "    'watchers': 175552,\n", "    'default_branch': 'master'}},\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845'},\n", "   'html': {'href': 'https://github.com/TheAlgorithms/Python/pull/1845'},\n", "   'issue': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/issues/1845'},\n", "   'comments': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/issues/1845/comments'},\n", "   'review_comments': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845/comments'},\n", "   'review_comment': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/comments{/number}'},\n", "   'commits': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845/commits'},\n", "   'statuses': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/statuses/1f3eccd6787c643b461d17dae6825f8fa5ddec82'}},\n", "  'author_association': 'CONTRIBUTOR',\n", "  'auto_merge': None,\n", "  'active_lock_reason': None,\n", "  'merged': False,\n", "  'mergeable': None,\n", "  'rebaseable': None,\n", "  'mergeable_state': 'unknown',\n", "  'merged_by': None,\n", "  'comments': 7,\n", "  'review_comments': 2,\n", "  'maintainer_can_modify': False,\n", "  'commits': 1,\n", "  'additions': 1062,\n", "  'deletions': 0,\n", "  'changed_files': 23},\n", " 'author': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'full_comment_info': {'url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/comments/406234397',\n", "  'pull_request_review_id': 390830578,\n", "  'id': 406234397,\n", "  'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDQwNjIzNDM5Nw==',\n", "  'diff_hunk': \"@@ -0,0 +1,56 @@\\n+'''\\n+\\n+Problem Description\\n+Given two array of integers A, B of equal size N. Power of an array is defined as the product of all the elements of the array. If the power of array A >= power of array B return 1 else return 0.    \\n+\\n+\\n+Problem Constraints\\n+1 <= N <= 100000\\n+1 <= A[i], B[i] <= 109\\n+\\n+\\n+Input Format\\n+First argument is an array of integers A.\\n+Second argument is an array of integers B.\\n+\\n+\\n+Output Format\\n+Return 1 if power of A >= power of B else return 0.\\n+\\n+\\n+Example Input\\n+Input 1:\\n+A = [1, 2, 3, 4]\\n+B = [2, 4, 3, 2]\\n+   \\n+\\n+\\n+Example Output\\n+Output 1:\\n+0\\n+   \\n+\\n+\\n+Example Explanation\\n+Explanation 1:\\n+Power of A = 24 and Power of B = 48.\\n+So, the answer is 0.\\n+'''\\n+\",\n", "  'path': 'data_structures/Math/PowerfulArray.py',\n", "  'commit_id': '1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "  'original_commit_id': '1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "  'user': {'login': 'cclauss',\n", "   'id': 3709715,\n", "   'node_id': 'MDQ6VXNlcjM3MDk3MTU=',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/3709715?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/cclauss',\n", "   'html_url': 'https://github.com/cclauss',\n", "   'followers_url': 'https://api.github.com/users/cclauss/followers',\n", "   'following_url': 'https://api.github.com/users/cclauss/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/cclauss/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/cclauss/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/cclauss/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/cclauss/orgs',\n", "   'repos_url': 'https://api.github.com/users/cclauss/repos',\n", "   'events_url': 'https://api.github.com/users/cclauss/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/cclauss/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': '```suggestion\\r\\n\\r\\nimport math\\r\\n```\\r\\nhttps://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339',\n", "  'created_at': '2020-04-09T14:12:55Z',\n", "  'updated_at': '2020-04-09T14:12:56Z',\n", "  'html_url': 'https://github.com/TheAlgorithms/Python/pull/1845#discussion_r406234397',\n", "  'pull_request_url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845',\n", "  'author_association': 'MEMBER',\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/comments/406234397'},\n", "   'html': {'href': 'https://github.com/TheAlgorithms/Python/pull/1845#discussion_r406234397'},\n", "   'pull_request': {'href': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/1845'}},\n", "  'reactions': {'url': 'https://api.github.com/repos/TheAlgorithms/Python/pulls/comments/406234397/reactions',\n", "   'total_count': 0,\n", "   '+1': 0,\n", "   '-1': 0,\n", "   'laugh': 0,\n", "   'hooray': 0,\n", "   'confused': 0,\n", "   'heart': 0,\n", "   'rocket': 0,\n", "   'eyes': 0},\n", "  'start_line': None,\n", "  'original_start_line': None,\n", "  'start_side': None,\n", "  'line': 39,\n", "  'original_line': 39,\n", "  'side': 'RIGHT',\n", "  'original_position': 39,\n", "  'position': 39,\n", "  'subject_type': 'line'},\n", " 'graphql_comment_info': {'databaseId': 406234397,\n", "  'bodyText': 'Suggested change\\n      \\n    \\n    \\n      \\n          \\n            \\n            \\n          \\n          \\n            \\n            \\n          \\n          \\n            \\n            import math\\n          \\n      \\n    \\n    \\n  \\n\\nhttps://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339',\n", "  'bodyHTML': '  <div class=\"my-2 border rounded-2 js-suggested-changes-blob diff-view js-check-bidi\" id=\"\">\\n    <div class=\"f6 p-2 lh-condensed border-bottom d-flex\">\\n      <div class=\"flex-auto flex-items-center color-fg-muted\">\\n        Suggested change\\n      </div>\\n    </div>\\n    <div itemprop=\"text\" class=\"blob-wrapper data file\" style=\"margin: 0; border: none; overflow-y: visible; overflow-x: auto;\">\\n      <table class=\"d-table tab-size mb-0 width-full\" data-paste-markdown-skip=\"\">\\n          <tbody><tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"39\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\"></td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"39\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\"></td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"40\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\"><span class=\"pl-k\">import</span> <span class=\"pl-s1\">math</span></td>\\n          </tr>\\n      </tbody></table>\\n    </div>\\n    <div class=\"js-apply-changes\"></div>\\n  </div>\\n\\n<p dir=\"auto\"><a href=\"https://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339\" rel=\"nofollow\">https://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339</a></p>',\n", "  'body': '```suggestion\\r\\n\\r\\nimport math\\r\\n```\\r\\nhttps://travis-ci.com/github/TheAlgorithms/Python/builds/159594158#L339'},\n", " 'original_commit': {'start_line': 38,\n", "  'end_line': 39,\n", "  'commit_sha': '1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "  'file_info': {'name': 'PowerfulArray.py',\n", "   'path': 'data_structures/Math/PowerfulArray.py',\n", "   'sha': '9b9e29d60412e98b2cb96ddf7577d65613a1ac16',\n", "   'size': 1011,\n", "   'url': 'https://api.github.com/repos/TheAlgorithms/Python/contents/data_structures/Math/PowerfulArray.py?ref=1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "   'html_url': 'https://github.com/TheAlgorithms/Python/blob/1f3eccd6787c643b461d17dae6825f8fa5ddec82/data_structures/Math/PowerfulArray.py',\n", "   'git_url': 'https://api.github.com/repos/TheAlgorithms/Python/git/blobs/9b9e29d60412e98b2cb96ddf7577d65613a1ac16',\n", "   'download_url': 'https://raw.githubusercontent.com/TheAlgorithms/Python/1f3eccd6787c643b461d17dae6825f8fa5ddec82/data_structures/Math/PowerfulArray.py',\n", "   'type': 'file',\n", "   'content': 'JycnCgpQcm9ibGVtIERlc2NyaXB0aW9uCkdpdmVuIHR3byBhcnJheSBvZiBp\\nbnRlZ2VycyBBLCBCIG9mIGVxdWFsIHNpemUgTi4gUG93ZXIgb2YgYW4gYXJy\\nYXkgaXMgZGVmaW5lZCBhcyB0aGUgcHJvZHVjdCBvZiBhbGwgdGhlIGVsZW1l\\nbnRzIG9mIHRoZSBhcnJheS4gSWYgdGhlIHBvd2VyIG9mIGFycmF5IEEgPj0g\\ncG93ZXIgb2YgYXJyYXkgQiByZXR1cm4gMSBlbHNlIHJldHVybiAwLiAgICAK\\nCgpQcm9ibGVtIENvbnN0cmFpbnRzCjEgPD0gTiA8PSAxMDAwMDAKMSA8PSBB\\nW2ldLCBCW2ldIDw9IDEwOQoKCklucHV0IEZvcm1hdApGaXJzdCBhcmd1bWVu\\ndCBpcyBhbiBhcnJheSBvZiBpbnRlZ2VycyBBLgpTZWNvbmQgYXJndW1lbnQg\\naXMgYW4gYXJyYXkgb2YgaW50ZWdlcnMgQi4KCgpPdXRwdXQgRm9ybWF0ClJl\\ndHVybiAxIGlmIHBvd2VyIG9mIEEgPj0gcG93ZXIgb2YgQiBlbHNlIHJldHVy\\nbiAwLgoKCkV4YW1wbGUgSW5wdXQKSW5wdXQgMToKQSA9IFsxLCAyLCAzLCA0\\nXQpCID0gWzIsIDQsIDMsIDJdCiAgIAoKCkV4YW1wbGUgT3V0cHV0Ck91dHB1\\ndCAxOgowCiAgIAoKCkV4YW1wbGUgRXhwbGFuYXRpb24KRXhwbGFuYXRpb24g\\nMToKUG93ZXIgb2YgQSA9IDI0IGFuZCBQb3dlciBvZiBCID0gNDguClNvLCB0\\naGUgYW5zd2VyIGlzIDAuCicnJwoKCgpjbGFzcyBTb2x1dGlvbjoKICAgICMg\\nQHBhcmFtIEEgOiBsaXN0IG9mIGludGVnZXJzCiAgICAjIEBwYXJhbSBCIDog\\nbGlzdCBvZiBpbnRlZ2VycwogICAgIyBAcmV0dXJuIGFuIGludGVnZXIKICAg\\nIGRlZiBzb2x2ZShzZWxmLCBBLCBCKToKICAgICAgICBuPWxlbihBKQogICAg\\nICAgIGFuczE9MAogICAgICAgIGFuczI9MAogICAgCiAgICAgICAgZm9yIGkg\\naW4gcmFuZ2UgKDAsbik6CiAgICAgICAgICAgIGFuczE9YW5zMSttYXRoLmxv\\nZzEwKEFbaV0pCiAgICAgICAgICAgIGFuczI9YW5zMittYXRoLmxvZzEwKEJb\\naV0pCiAgICAgICAgaWYoYW5zMT49YW5zMik6CiAgICAgICAgICAgIHJldHVy\\nbiAxCiAgICAgICAgcmV0dXJuIDAK\\n',\n", "   'encoding': 'base64',\n", "   '_links': {'self': 'https://api.github.com/repos/TheAlgorithms/Python/contents/data_structures/Math/PowerfulArray.py?ref=1f3eccd6787c643b461d17dae6825f8fa5ddec82',\n", "    'git': 'https://api.github.com/repos/TheAlgorithms/Python/git/blobs/9b9e29d60412e98b2cb96ddf7577d65613a1ac16',\n", "    'html': 'https://github.com/TheAlgorithms/Python/blob/1f3eccd6787c643b461d17dae6825f8fa5ddec82/data_structures/Math/PowerfulArray.py'},\n", "   'decoded_content': \"'''\\n\\nProblem Description\\nGiven two array of integers A, B of equal size N. Power of an array is defined as the product of all the elements of the array. If the power of array A >= power of array B return 1 else return 0.    \\n\\n\\nProblem Constraints\\n1 <= N <= 100000\\n1 <= A[i], B[i] <= 109\\n\\n\\nInput Format\\nFirst argument is an array of integers A.\\nSecond argument is an array of integers B.\\n\\n\\nOutput Format\\nReturn 1 if power of A >= power of B else return 0.\\n\\n\\nExample Input\\nInput 1:\\nA = [1, 2, 3, 4]\\nB = [2, 4, 3, 2]\\n   \\n\\n\\nExample Output\\nOutput 1:\\n0\\n   \\n\\n\\nExample Explanation\\nExplanation 1:\\nPower of A = 24 and Power of B = 48.\\nSo, the answer is 0.\\n'''\\n\\n\\n\\nclass Solution:\\n    # @param A : list of integers\\n    # @param B : list of integers\\n    # @return an integer\\n    def solve(self, A, B):\\n        n=len(A)\\n        ans1=0\\n        ans2=0\\n    \\n        for i in range (0,n):\\n            ans1=ans1+math.log10(A[i])\\n            ans2=ans2+math.log10(B[i])\\n        if(ans1>=ans2):\\n            return 1\\n        return 0\\n\"},\n", "  'file': \"'''\\n\\nProblem Description\\nGiven two array of integers A, B of equal size N. Power of an array is defined as the product of all the elements of the array. If the power of array A >= power of array B return 1 else return 0.    \\n\\n\\nProblem Constraints\\n1 <= N <= 100000\\n1 <= A[i], B[i] <= 109\\n\\n\\nInput Format\\nFirst argument is an array of integers A.\\nSecond argument is an array of integers B.\\n\\n\\nOutput Format\\nReturn 1 if power of A >= power of B else return 0.\\n\\n\\nExample Input\\nInput 1:\\nA = [1, 2, 3, 4]\\nB = [2, 4, 3, 2]\\n   \\n\\n\\nExample Output\\nOutput 1:\\n0\\n   \\n\\n\\nExample Explanation\\nExplanation 1:\\nPower of A = 24 and Power of B = 48.\\nSo, the answer is 0.\\n'''\\n\\n\\n\\nclass Solution:\\n    # @param A : list of integers\\n    # @param B : list of integers\\n    # @return an integer\\n    def solve(self, A, B):\\n        n=len(A)\\n        ans1=0\\n        ans2=0\\n    \\n        for i in range (0,n):\\n            ans1=ans1+math.log10(A[i])\\n            ans2=ans2+math.log10(B[i])\\n        if(ans1>=ans2):\\n            return 1\\n        return 0\\n\"},\n", " 'edit_distance': 12,\n", " 'start_line': 38,\n", " 'end_line': 39,\n", " 'old_file': \"'''\\n\\nProblem Description\\nGiven two array of integers A, B of equal size N. Power of an array is defined as the product of all the elements of the array. If the power of array A >= power of array B return 1 else return 0.    \\n\\n\\nProblem Constraints\\n1 <= N <= 100000\\n1 <= A[i], B[i] <= 109\\n\\n\\nInput Format\\nFirst argument is an array of integers A.\\nSecond argument is an array of integers B.\\n\\n\\nOutput Format\\nReturn 1 if power of A >= power of B else return 0.\\n\\n\\nExample Input\\nInput 1:\\nA = [1, 2, 3, 4]\\nB = [2, 4, 3, 2]\\n   \\n\\n\\nExample Output\\nOutput 1:\\n0\\n   \\n\\n\\nExample Explanation\\nExplanation 1:\\nPower of A = 24 and Power of B = 48.\\nSo, the answer is 0.\\n'''\\n\\n\\n\\nclass Solution:\\n    # @param A : list of integers\\n    # @param B : list of integers\\n    # @return an integer\\n    def solve(self, A, B):\\n        n=len(A)\\n        ans1=0\\n        ans2=0\\n    \\n        for i in range (0,n):\\n            ans1=ans1+math.log10(A[i])\\n            ans2=ans2+math.log10(B[i])\\n        if(ans1>=ans2):\\n            return 1\\n        return 0\\n\",\n", " 'old_code': '',\n", " 'prefix': \"'''\\n\\nProblem Description\\nGiven two array of integers A, B of equal size N. Power of an array is defined as the product of all the elements of the array. If the power of array A >= power of array B return 1 else return 0.    \\n\\n\\nProblem Constraints\\n1 <= N <= 100000\\n1 <= A[i], B[i] <= 109\\n\\n\\nInput Format\\nFirst argument is an array of integers A.\\nSecond argument is an array of integers B.\\n\\n\\nOutput Format\\nReturn 1 if power of A >= power of B else return 0.\\n\\n\\nExample Input\\nInput 1:\\nA = [1, 2, 3, 4]\\nB = [2, 4, 3, 2]\\n   \\n\\n\\nExample Output\\nOutput 1:\\n0\\n   \\n\\n\\nExample Explanation\\nExplanation 1:\\nPower of A = 24 and Power of B = 48.\\nSo, the answer is 0.\\n'''\",\n", " 'suffix': '\\n\\nclass Solution:\\n    # @param A : list of integers\\n    # @param B : list of integers\\n    # @return an integer\\n    def solve(self, A, B):\\n        n=len(A)\\n        ans1=0\\n        ans2=0\\n    \\n        for i in range (0,n):\\n            ans1=ans1+math.log10(A[i])\\n            ans2=ans2+math.log10(B[i])\\n        if(ans1>=ans2):\\n            return 1\\n        return 0',\n", " 'commit_html_url': 'https://github.com/Shrutikabansal/Python/commit/1f3eccd6787c643b461d17dae6825f8fa5ddec82'}"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_suggested_edit[0]"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  REVIEW URL: https://github.com/saros-project/saros/pull/742#discussion_r342579610\n", "                 MAIN COMMIT: https://github.com/saros-project/saros/commit/ba521f5ff965d5e67bcd5e4c1a6fe0e61e4c4e65\n", "               AUTHOR COMMIT: https://github.com/tobous/saros/commit/ba521f5ff965d5e67bcd5e4c1a6fe0e61e4c4e65\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: As far as I can tell, this is the minimal setup. Furthermore, it requires additional setup in `SPathConverterTest`. The amended commits are available on https://github.com/saros-project/saros/commit/4567fa0d78d529318aa8562cf43067552362c7aa and https://github.com/saros-project/saros/commit/6c84b9b96b69b201024916569cc553de71f6d266.\n", "\n", "    xStream.allowTypeHierarchy(Collection.class);\n", "    xStream.allowTypeHierarchy(Map.class);\n", "    xStream.allowTypes(new Class[] {String.class});\n", "\n", "    // allow all saros classes\n", "-    xStream.allowTypesByWildcard(new String[] {\"saros.**\"});\n", "+    // allow classes needed for Saros network logic\n", "+    xStream.allowTypeHierarchy(IActivity.class);\n", "+    xStream.allowTypeHierarchy(SarosPacketExtension.class);\n", "+    xStream.allowTypeHierarchy(Operation.class);\n", "+\n", "+    Class[] allowedSarosNetworkClasses =\n", "+        new Class[] {\n", "+          FileList.class,\n", "+          JID.class,\n", "+          JupiterVectorTime.class,\n", "+          ProjectNegotiationData.class,\n", "+          SPath.class,\n", "+          User.class,\n", "+          UserListExtension.UserListEntry.class,\n", "+          VersionExchangeExtension.class,\n", "+          XStreamPacketExtension.class,\n", "+        };\n", "+    xStream.allowTypes(allowedSarosNetworkClasses);\n", "+\n", "+    String[] allowedPrivateSarosNetworkClassNames = {\n", "+      FileList.class.getCanonicalName() + \"$File\", FileList.class.getCanonicalName() + \"$MetaData\"\n", "+    };\n", "+    xStream.allowTypes(allowedPrivateSarosNetworkClassNames);\n", "+\n", "+    // allow classes needed for ColorIDSetStorage\n", "+    Class[] allowedColorIDSetStorageClasses = new Class[] {ColorIDSet.class, UserColorID.class};\n", "+    xStream.allowTypes(allowedColorIDSetStorageClasses);\n", "+\n", "+    // allow classed needed for XMPPAccountStore\n", "+    Class[] allowedXMPPAccountStoreClasses = new Class[] {XMPPAccount.class};\n", "+    xStream.allowTypes(allowedXMPPAccountStoreClasses);\n", "+\n", "+    String[] allowedPrivateXMPPAccountStoreClassName = {\n", "+      XMPPAccountStore.class.getCanonicalName() + \"$AccountStoreInformation\"\n", "+    };\n", "+    xStream.allowTypes(allowedPrivateXMPPAccountStoreClassName);\n", "  }\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/onflow/flow-go/pull/406#discussion_r580653158\n", "                 MAIN COMMIT: https://github.com/onflow/flow-go/commit/80ae3eb0e11e825d3cfc13e95da0bf03ef7b94f0\n", "               AUTHOR COMMIT: https://github.com/durkmurder/flow-go/commit/80ae3eb0e11e825d3cfc13e95da0bf03ef7b94f0\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This is a personal stylistic preference, just wanted to bring it up:\n", "* the business logic `processBlockProposal` might return some expected error (like `OutdatedInputError` or `InvalidInputError`). We could check for these errors directly after https://github.com/onflow/flow-go/blob/80ae3eb0e11e825d3cfc13e95da0bf03ef7b94f0/engine/consensus/compliance/core.go#L224\n", "* then, the only errors we are left with in the recursion-part of `processBlockAndDescendants` are unexpected (fatal) errors. We can escalate those without wrapping:\n", "   *  <PERSON> made an insightful comment on this error wrapping https://github.com/onflow/flow-go/blob/80ae3eb0e11e825d3cfc13e95da0bf03ef7b94f0/engine/consensus/compliance/core.go#L255 Specifically: \n", "      * Lets assume we go through 100 recursions, processing the child of the child of the chid ...\n", "      * at recursion 100, we hit some fatal error. This error would be wrapped 100 times: `internal error processing block A at height 1: internal error processing block B at height 2: ..... internal error processing block XYZ at height 100`\n", "      * But the blocks at height 1 to 99 were just fine. So the error message carries a lot of unrelated information and is very long. \n", "\n", "   * Instead, we could just bubble the error up without wrapping. \n", "\n", " #### suggestion (summary of the points above):\n", "\n", "func (c *Core) processBlockAndDescendants(proposal *messages.BlockProposal) error {\n", "\tblockID := proposal.Header.ID()\n", "\n", "\t// process block itself\n", "\terr := c.process<PERSON>lockProposal(proposal)\n", "-\tif err != nil {\n", "-\t\treturn fmt.<PERSON><PERSON><PERSON>(\"failed to process block %x: %w\", blockID, err)\n", "-\t}\n", "-\n", "-\t// process all children\n", "-\t// do not break on invalid or outdated blocks as they should not prevent us\n", "-\t// from processing other valid children\n", "-\tchildren, has := c.pending.ByParentID(blockID)\n", "-\tif !has {\n", "-\t\treturn nil\n", "-\t}\n", "-\tfor _, child := range children {\n", "-\t\tchildProposal := &messages.BlockProposal{\n", "-\t\t\tHeader:  child.<PERSON><PERSON>,\n", "-\t\t\tPayload: child.Payload,\n", "-\t\t}\n", "-\t\tcpr := c.processBlockAndDescendants(childProposal)\n", "-\t\tif cpr != nil {\n", "-\t\t\t// child is outdated by the time we started processing it\n", "-\t\t\t// => node was probably behind and is catching up. Log as warning\n", "-\t\t\tif engine.IsOutdatedInputError(cpr) {\n", "-\t\t\t\tc.log.Warn().Msg(\"dropped processing of abandoned fork; this might be an indicator that the node is slightly behind\")\n", "-\t\t\t\tcontinue\n", "-\t\t\t}\n", "-\t\t\t// the block is invalid; log as error as we desire honest participation\n", "-\t\t\t// ToDo: potential slashing\n", "-\t\t\tif engine.IsInvalidInputError(err) {\n", "-\t\t\t\tc.log.Error().<PERSON>rr(err).Msg(\"invalid block (potential slashing evidence?)\")\n", "-\t\t\t\tcontinue\n", "-\t\t\t}\n", "-\t\t\treturn fmt.<PERSON><PERSON>rf(\"internal error processing block %x at height %d: %w\", child.Header.ID(), child.Header.Height, err)\n", "+\t// child is outdated by the time we started processing it\n", "+\t// => node was probably behind and is catching up. Log as warning\n", "+\tif engine.IsOutdatedInputError(err) {\n", "+\t\tc.log.Info().Msg(\"dropped processing of abandoned fork; this might be an indicator that the node is slightly behind\")\n", "+\t\treturn nil\n", "+\t}\n", "+\t// the block is invalid; log as error as we desire honest participation\n", "+\t// ToDo: potential slashing\n", "+\tif engine.IsInvalidInputError(err) {\n", "+\t\tc.log.Warn().Err(err).Msg(\"received invalid block from other node (potential slashing evidence?)\")\n", "+\t\treturn nil\n", "+\t}\n", "+\tif err != nil {\n", "+\t\t// unexpected error: potentially corrupted internal state => abort processing and escalate error\n", "+\t\treturn fmt.<PERSON><PERSON><PERSON>(\"failed to process block %x: %w\", blockID, err)\n", "+\t}\n", "+\n", "+\t// process all children\n", "+\t// do not break on invalid or outdated blocks as they should not prevent us\n", "+\t// from processing other valid children\n", "+\tchildren, has := c.pending.ByParentID(blockID)\n", "+\tif !has {\n", "+\t\treturn nil\n", "+\t}\n", "+\tfor _, child := range children {\n", "+\t\tchildProposal := &messages.BlockProposal{\n", "+\t\t\tHeader:  child.<PERSON><PERSON>,\n", "+\t\t\tPayload: child.Payload,\n", "+\t\t}\n", "+\t\tcpr := c.processBlockAndDescendants(childProposal)\n", "+\t\tif cpr != nil {\n", "+\t\t\t// unexpected error: potentially corrupted internal state => abort processing and escalate error\n", "+\t\t\treturn cpr\n", "\t\t}\n", "\t}\n", "\n", "\t// drop all of the children that should have been processed now\n", "\tc.pending.DropForParent(blockID)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458364651\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator will also update the rest of the JavaDocs in this file to ensure everything matches the RPC Spec exactly\n", "\n", "import android.support.annotation.NonNull;\n", "\n", "import com.smartdevicelink.proxy.RPCStruct;\n", "\n", "import java.util.Hashtable;\n", "\n", "+/**\n", "+ * Describes the status of a window of a door/liftgate etc.\n", "+ *\n", "+ * <p><b>Parameter List</b></p>\n", "+ *\n", "+ * <table border=\"1\" rules=\"all\">\n", "+ *  <tr>\n", "+ *      <th>Param Name</th>\n", "+ *      <th>Type</th>\n", "+ *      <th>Description</th>\n", "+ *      <th>Required</th>\n", "+ *      <th>Version Available</th>\n", "+ *  </tr>\n", "+ *  <tr>\n", "+ *      <td>location</td>\n", "+ *      <td>Grid</td>\n", "+ *      <td></td>\n", "+ *      <td>Y</td>\n", "+ *      <td></td>\n", "+ *  </tr>\n", "+ *  <tr>\n", "+ *      <td>state</td>\n", "+ *      <td>WindowState</td>\n", "+ *      <td></td>\n", "+ *      <td>Y</td>\n", "+ *      <td></td>\n", "+ *  </tr>\n", "+ * </table>\n", "+ *\n", "+ * @since SmartDeviceLink 7.0.0\n", "+ */\n", "public class WindowStatus extends RPCStruct {\n", "\n", "    public static final String KEY_LOCATION = \"location\";\n", "    public static final String KEY_WINDOW_STATE = \"state\";\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458361534\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator will also update the rest of the JavaDocs in this file to ensure everything matches the RPC Spec exactly\n", "\n", "import android.support.annotation.NonNull;\n", "\n", "import com.smartdevicelink.proxy.RPCStruct;\n", "\n", "import java.util.Hashtable;\n", "\n", "+/**\n", "+ *\n", "+ * <p><b>Parameter List</b></p>\n", "+ *\n", "+ * <table border=\"1\" rules=\"all\">\n", "+ *  <tr>\n", "+ *      <th>Param Name</th>\n", "+ *      <th>Type</th>\n", "+ *      <th>Description</th>\n", "+ *      <th>Required</th>\n", "+ *      <th>Version Available</th>\n", "+ *  </tr>\n", "+ *  <tr>\n", "+ *      <td>approximatePosition</td>\n", "+ *      <td>Integer</td>\n", "+ *      <td>The approximate percentage that the window is open - 0 being fully closed, 100 being fullyopen</td>\n", "+ *      <td>Y</td>\n", "+ *      <td></td>\n", "+ *  </tr>\n", "+ *  <tr>\n", "+ *      <td>deviation</td>\n", "+ *      <td>Integer</td>\n", "+ *      <td>The percentage deviation of the approximatePosition. e.g. If the approximatePosition is 50and the deviation is 10, then the window's location is somewhere between 40 and 60.</td>\n", "+ *      <td>Y</td>\n", "+ *      <td></td>\n", "+ *  </tr>\n", "+ * </table>\n", "+ * @since SmartDeviceLink 7.0.0\n", "+ */\n", "public class WindowState extends RPCStruct {\n", "    public static final String KEY_APPROXIMATE_POSITION = \"approximatePosition\";\n", "    public static final String KEY_DEVIATION = \"deviation\";\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/planetdecred/godcr/pull/565#discussion_r691816088\n", "                 MAIN COMMIT: https://github.com/planetdecred/godcr/commit/3614da84df18121f1246e8fc6ee8a3abb00fea08\n", "               AUTHOR COMMIT: https://github.com/song50119/godcr/commit/3614da84df18121f1246e8fc6ee8a3abb00fea08\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I think using a switch case would be better.\n", "\n", "Also, there is a TimeAgo() in the component package... kindly make use of that. \n", "https://github.com/planetdecred/godcr/blob/269088483ecb35ae68550985dd6b39ae4df25591/ui/page/components/components.go#L280\n", "\n", "\n", "func nextTicketRemaining(allsecs int) string {\n", "\tif allsecs == 0 {\n", "\t\treturn \"imminent\"\n", "\t}\n", "-\tstr := \"\"\n", "-\tif allsecs > 604799 {\n", "-\t\tweeks := allsecs / 604800\n", "-\t\tallsecs %= 604800\n", "-\t\tstr += fmt.Sprintf(\"%dw \", weeks)\n", "-\t}\n", "-\tif allsecs > 86399 {\n", "-\t\tdays := allsecs / 86400\n", "-\t\tallsecs %= 86400\n", "-\t\tstr += fmt.Sprintf(\"%dd \", days)\n", "-\t}\n", "-\tif allsecs > 3599 {\n", "-\t\thours := allsecs / 3600\n", "-\t\tallsecs %= 3600\n", "-\t\tstr += fmt.Sprintf(\"%dh \", hours)\n", "-\t}\n", "-\tif allsecs > 59 {\n", "-\t\tmins := allsecs / 60\n", "-\t\tallsecs %= 60\n", "-\t\tstr += fmt.Sprintf(\"%dm \", mins)\n", "-\t}\n", "-\tif allsecs > 0 {\n", "-\t\tstr += fmt.Sprintf(\"%ds \", allsecs)\n", "-\t}\n", "-\treturn str\n", "-}\n", "+\tvar str string\n", "+\tif allsecs > 604799 {\n", "+\t\tweeks := allsecs / 604800\n", "+\t\tallsecs %= 604800\n", "+\t\tstr = fmt.Sprintf(\"%dw \", weeks)\n", "+\t}\n", "+\tif allsecs > 86399 {\n", "+\t\tdays := allsecs / 86400\n", "+\t\tallsecs %= 86400\n", "+\t\tstr = fmt.Sprintf(\"%dd \", days)\n", "+\t}\n", "+\tif allsecs > 3599 {\n", "+\t\thours := allsecs / 3600\n", "+\t\tallsecs %= 3600\n", "+\t\tstr = fmt.Sprintf(\"%dh \", hours)\n", "+\t}\n", "+\tif allsecs > 59 {\n", "+\t\tmins := allsecs / 60\n", "+\t\tallsecs %= 60\n", "+\t\tstr = fmt.Sprintf(\"%dm \", mins)\n", "+\t}\n", "+\tif allsecs > 0 {\n", "+\t\tstr = fmt.Sprintf(\"%ds \", allsecs)\n", "+\t}\n", "+\treturn str\n", "+}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/onflow/flow-go/pull/632#discussion_r628829075\n", "                 MAIN COMMIT: https://github.com/onflow/flow-go/commit/a552e78e9b45d21c0b436eb3c6d3439b403cf6ac\n", "               AUTHOR COMMIT: https://github.com/durkmurder/flow-go/commit/a552e78e9b45d21c0b436eb3c6d3439b403cf6ac\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I have two comments regarding the business logic of `GetOrCreateCollector`:\n", "\n", "1. We could move the object construction https://github.com/onflow/flow-go/blob/a552e78e9b45d21c0b436eb3c6d3439b403cf6ac/engine/consensus/approvals/assignment_collector_tree.go#L124-L132 outside of the write lock https://github.com/onflow/flow-go/blob/a552e78e9b45d21c0b436eb3c6d3439b403cf6ac/engine/consensus/approvals/assignment_collector_tree.go#L114 Reasoning: The constructor internally computes the ID of `result` (cryptographic hash), which is a comparatively expensive operation. No need to keep the lock during this time. \n", "2. Generally, we orphan entire forks. This means that if the parent is orphaned, _all_ descendants are orphaned as well. I would suggest to utilize this relation ship here\n", "    ```golang\n", "    parent, parentFound := t.forest.GetVertex(result.PreviousResultID)\n", "    if parentFound {\n", "      vertex.Orphan = parent.(*AssignmentCollectorVertex).Orphan\n", "    }\n", "    ``` \n", "    This also enables the optimization I suggest for `markOrphanFork`\n", "\n", "putting it together:\n", "\n", "\t// first let's check if we have a collector already\n", "\tcachedCollector := t.GetCollector(resultID)\n", "\tif cachedCollector != nil {\n", "\t\treturn cachedCollector.Collector, false, nil\n", "\t}\n", "-\t// fast check shows that there is no collector, need to create one\n", "-\tt.lock.Lock()\n", "-\tdefer t.lock.<PERSON><PERSON>()\n", "-\n", "-\t// we need to check again, since it's possible that after checking for existing collector but before taking a lock\n", "-\t// new collector was created by concurrent goroutine\n", "-\tvertex, found := t.forest.GetVertex(resultID)\n", "-\tif found {\n", "-\t\treturn vertex.(*AssignmentCollectorVertex).Collector, false, nil\n", "-\t}\n", "-\n", "-\tcollector, err := t.onCreate<PERSON>ollector(result)\n", "-\tif err != nil {\n", "-\t\treturn nil, false, fmt.<PERSON><PERSON><PERSON>(\"could not create assignment collector for %v: %w\", resultID, err)\n", "-\t}\n", "-\n", "-\tvertex = &AssignmentCollectorVertex{\n", "-\t\tCollector: collector,\n", "-\t\t<PERSON><PERSON><PERSON>:    false,\n", "-\t}\n", "+\tcollector, err := t.onC<PERSON>ollector(result)\n", "+\tif err != nil {\n", "+\t\treturn nil, false, fmt.<PERSON><PERSON><PERSON>(\"could not create assignment collector for %v: %w\", resultID, err)\n", "+\t}\n", "+\tvertex := &AssignmentCollectorVertex{\n", "+\t\tCollector: collector,\n", "+\t\t<PERSON><PERSON>n:    false,\n", "+\t}\n", "+\n", "+\t// fast check shows that there is no collector, need to create one\n", "+\tt.lock.Lock()\n", "+\tdefer t.lock.<PERSON><PERSON>()\n", "+\n", "+\t// we need to check again, since it's possible that after checking for existing collector but before taking a lock\n", "+\t// new collector was created by concurrent goroutine\n", "+\tv, found := t.forest.GetVertex(resultID)\n", "+\tif found {\n", "+\t\treturn v.(*AssignmentCollectorVertex).Collector, false, nil\n", "+\t}\n", "+\tparent, parentFound := t.forest.GetVertex(result.PreviousResultID)\n", "+\tif parentFound {\n", "+\t\tvertex.Orphan = parent.(*AssignmentCollectorVertex).Orphan\n", "+\t}\n", "\n", "\terr = t.forest.VerifyVertex(vertex)\n", "\tif err != nil {\n", "\t\treturn nil, false, fmt.<PERSON><PERSON><PERSON>(\"failed to store assignment collector into the tree: %w\", err)\n", "\t}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/optuna/optuna/pull/1202#discussion_r425464057\n", "                 MAIN COMMIT: https://github.com/optuna/optuna/commit/aab96e6297e3a28743f43dd3fe164e6a374bb4ac\n", "               AUTHOR COMMIT: https://github.com/sskarkhanis/optuna/commit/aab96e6297e3a28743f43dd3fe164e6a374bb4ac\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: How about using the same optimization settings as [`examples/pruning/xgboost_cv_integration.py` ](https://github.com/optuna/optuna/blob/master/examples/pruning/xgboost_cv_integration.py)?\n", "\n", "# FYI: Objective functions can take additional arguments\n", "# (https://optuna.readthedocs.io/en/stable/faq.html#objective-func-additional-args).\n", "def objective(trial):\n", "    (data, target) = sklearn.datasets.load_breast_cancer(return_X_y=True)\n", "    dtrain = xgb.DMatrix(data, label=target)\n", "-    param = {\n", "-        \"silent\": 1,\n", "-        \"objective\": \"binary:logistic\",\n", "-        \"eval_metric\": \"auc\",\n", "-        \"booster\": trial.suggest_categorical(\"booster\", [\"gbtree\"]),\n", "-        \"alpha\": trial.suggest_loguniform(\"alpha\", 1e-3, 1.0),\n", "-    }\n", "-\n", "-    if param[\"booster\"] == \"gbtree\" or param[\"booster\"] == \"dart\":\n", "-        param[\"max_depth\"] = trial.suggest_int(\"max_depth\", 1, 9)\n", "-        param[\"min_child_weight\"] = trial.suggest_int(\"min_child_weight\", 1, 9)\n", "-        param[\"eta\"] = trial.suggest_loguniform(\"eta\", 1e-3, 1.0)\n", "-        param[\"gamma\"] = trial.suggest_loguniform(\"gamma\", 1e-3, 1.0)\n", "-        param[\"subsample\"] = trial.suggest_loguniform(\"subsample\", 0.6, 1.0)\n", "-        param[\"colsample_bytree\"] = trial.suggest_loguniform(\"colsample_bytree\", 0.6, 1.0)\n", "-        param[\"grow_policy\"] = trial.suggest_categorical(\"grow_policy\", [\"depthwise\", \"lossguide\"])\n", "-\n", "-    if param[\"booster\"] == \"dart\":\n", "-        param[\"sample_type\"] = trial.suggest_categorical(\"sample_type\", [\"uniform\", \"weighted\"])\n", "-        param[\"normalize_type\"] = trial.suggest_categorical(\"normalize_type\", [\"tree\", \"forest\"])\n", "-        param[\"rate_drop\"] = trial.suggest_loguniform(\"rate_drop\", 1e-8, 1.0)\n", "-        param[\"skip_drop\"] = trial.suggest_loguniform(\"skip_drop\", 1e-8, 1.0)\n", "+    param = {\n", "+        \"silent\": 1,\n", "+        \"objective\": \"binary:logistic\",\n", "+        \"eval_metric\": \"auc\",\n", "+        \"booster\": trial.suggest_categorical(\"booster\", [\"gbtree\", \"gblinear\", \"dart\"]),\n", "+        \"lambda\": trial.suggest_loguniform(\"lambda\", 1e-8, 1.0),\n", "+        \"alpha\": trial.suggest_loguniform(\"alpha\", 1e-8, 1.0),\n", "+    }\n", "+\n", "+    if param[\"booster\"] == \"gbtree\" or param[\"booster\"] == \"dart\":\n", "+        param[\"max_depth\"] = trial.suggest_int(\"max_depth\", 1, 9)\n", "+        param[\"eta\"] = trial.suggest_loguniform(\"eta\", 1e-8, 1.0)\n", "+        param[\"gamma\"] = trial.suggest_loguniform(\"gamma\", 1e-8, 1.0)\n", "+        param[\"grow_policy\"] = trial.suggest_categorical(\"grow_policy\", [\"depthwise\", \"lossguide\"])\n", "+    if param[\"booster\"] == \"dart\":\n", "+        param[\"sample_type\"] = trial.suggest_categorical(\"sample_type\", [\"uniform\", \"weighted\"])\n", "+        param[\"normalize_type\"] = trial.suggest_categorical(\"normalize_type\", [\"tree\", \"forest\"])\n", "+        param[\"rate_drop\"] = trial.suggest_loguniform(\"rate_drop\", 1e-8, 1.0)\n", "+        param[\"skip_drop\"] = trial.suggest_loguniform(\"skip_drop\", 1e-8, 1.0)\n", "\n", "    xgb_cv_results = xgb.cv(\n", "        params=param,\n", "        dtrain=dtrain,\n", "        num_boost_round=10000,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/buildkite/go-buildkite/pull/87#discussion_r679638162\n", "                 MAIN COMMIT: https://github.com/buildkite/go-buildkite/commit/****************************************\n", "               AUTHOR COMMIT: https://github.com/alam0rt/go-buildkite/commit/****************************************\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: When I ran this, it returned an error:\n", "\n", "```\n", "➜  go-buildkite git:(alam0rt/master) ✗ go run test.go token=$JJ_BK_TOKEN \n", "EOF2021/07/30 14:13:06 EOF\n", "exit status 1\n", "```\n", "\n", "I think the issue is that the API is returning a 204 for [revoke-the-current-token](https://buildkite.com/docs/apis/rest-api/access-token#revoke-the-current-token) and the JSON decoder is complaining [here](https://github.com/buildkite/go-buildkite/blob/master/buildkite/buildkite.go#L273) (that maps to [here](https://github.com/docker/go/blob/master/canonical/json/stream.go#L57)).\n", "\n", "Since it's a Delete call, maybe it would be better to do something similar to [delete-an-agent](https://github.com/buildkite/go-buildkite/blob/master/buildkite/agents.go#L127)?\n", "\n", "I quickly played around and came up with something like:\n", "\n", "}\n", "\n", "// Revokes the current token which was used to authenticate the request\n", "//\n", "// buildkite API docs: https://buildkite.com/docs/rest-api/access-token\n", "-func (ats *AccessTokensService) Revoke() (*AccessToken, *Response, error) {\n", "+func (ats *AccessTokensService) Revoke() (*Response, error) {\n", "+\n", "+    var u string\n", "+\n", "+    u = fmt.Sprintf(\"v2/access-token\")\n", "+\n", "+    req, err := ats.client.NewRequest(\"DELETE\", u, nil)\n", "+    if err != nil {\n", "+        return nil, err\n", "+    }\n", "+\n", "+    resp, err := ats.client.Do(req, nil)\n", "+    if err != nil {\n", "+        return resp, err\n", "+    }\n", "+\n", "+    return resp, nil\n", "+}\n", "\n", "\tvar u string\n", "\n", "\tu = fmt.Sprintf(\"v2/access-token\")\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/openmrs/openmrs-core/pull/3266#discussion_r443720010\n", "                 MAIN COMMIT: https://github.com/openmrs/openmrs-core/commit/0a78aa1d08de9a2093509cb57f9ce09f5c96efd5\n", "               AUTHOR COMMIT: https://github.com/jwnasambu/openmrs-core/commit/0a78aa1d08de9a2093509cb57f9ce09f5c96efd5\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Here we have something similar. However, unlike the above, we don't just cleanly want option 1 or option 2. We primarily want what's in option 2, but we also want the fields (but not the annotations) defined in option 1. \n", "\n", "If you want to understand *why* you get this conflict, it's because the version of `master` you were working from had [this commit](https://github.com/openmrs/openmrs-core/commit/267f4df2ba3d683323e2938690f5bcef116318b6#diff-7325e970b68864df576bcb7c49e495c0), but it's [since been backed out](https://github.com/openmrs/openmrs-core/commit/4d53a02710df2fda97ca5579772b629022267677#diff-7325e970b68864df576bcb7c49e495c0); this is also why we want option 2 with some additions. While we don't want to add all the Hibernate annotations in, we do want the fields that used to be defined in `BaseChangeableOpenmrsMetadata` that are required to support the `Auditable` adn `Retireable` interfaces.\n", "\n", "So in this case, we want to end up with something like this:\n", "\n", "\t\n", "\tprivate String username;\n", "\t\n", "\tprivate String email;\n", "\t\n", "-<<<<<<< HEAD\n", "-\t@ManyToOne(optional = false)\n", "-\t@JoinC<PERSON>umn(name = \"creator\")\n", "-\tprivate User creator;\n", "-\t\n", "-\t@Column(name = \"date_created\", nullable = false)\n", "-\tprivate Date dateCreated;\n", "-\t\n", "-\t@ManyToOne\n", "-\t@JoinColumn(name = \"changed_by\")\n", "-\tprivate User changedBy;\n", "-\t\n", "-\t@Column(name = \"date_changed\")\n", "-\tprivate Date dateChanged;\n", "-\t\n", "-\t@Column(name = \"retired\", nullable = false)\n", "-\tprivate <PERSON> retired;\n", "-\t\n", "-\t@ManyToOne\n", "-\t@JoinColumn(name = \"retired_by\")\n", "-\tprivate User retired<PERSON><PERSON>;\n", "-\t\n", "-\t@Column(name = \"retire_reason\", length = 255)\n", "-\tprivate String retireReason;\n", "-\t\n", "-\t@Column(name = \"date_retired\")\n", "-\tprivate Date dateRetired;\n", "-\t\n", "-\t@SuppressWarnings(\"deprecation\")\n", "-\t@ManyToMany\n", "-\t@JoinTable(name = \"user_role\", joinColumns = @JoinColumn(name = \"user_id\"), inverseJoinColumns = @JoinColumn(name = \"role\"))\n", "-\t@LazyCollection(LazyCollectionOption.FALSE)\n", "-\t@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)\n", "-\t@Cascade({ CascadeType.SAVE_UPDATE, CascadeType.MERGE, CascadeType.EVICT })\n", "-\tprivate Set<Role> roles;\n", "-\t\n", "-\t@SuppressWarnings(\"deprecation\")\n", "-\t@ElementCollection\n", "-\t@CollectionTable(name = \"user_property\", joinColumns = @JoinColumn(name = \"user_id\", nullable = false))\n", "-\t@MapKeyColumn(name = \"property\", length = 100)\n", "-\t@Column(name = \"property_value\")\n", "-\t@Cascade({ CascadeType.SAVE_UPDATE, CascadeType.MERGE, CascadeType.EVICT })\n", "-=======\n", "-\tprivate Set<Role> roles;\n", "-\t\n", "->>>>>>> 7f1edc92d80496f5d1c86e9e0b968292c5ebf238\n", "+\tprivate User creator;\n", "+\t\n", "+\tprivate Date dateCreated;\n", "+\n", "+\tprivate User changedBy;\n", "+\t\n", "+\tprivate Date dateChanged;\n", "+\t\n", "+\tprivate String retired;\n", "+\t\n", "+\tprivate User retiredBy;\n", "+\t\n", "+\tprivate String retireReason;\n", "+\t\n", "+\tprivate Date dateRetired;\n", "+\n", "+\tprivate Set<Role> roles;\n", "\tprivate Map<String, String> userProperties;\n", "\t\n", "\tprivate List<Locale> proficientLocales = null;\n", "\t\n", "\tprivate String parsedProficientLocalesProperty = \"\";\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/apache/accumulo/pull/1715#discussion_r511176220\n", "                 MAIN COMMIT: https://github.com/apache/accumulo/commit/e0e9cbbc83a9bd5699d2a6bb782c93a2ef66e95b\n", "               AUTHOR COMMIT: https://github.com/dlmarion/accumulo/commit/e0e9cbbc83a9bd5699d2a6bb782c93a2ef66e95b\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The following are the rational behind the suggestion below.\n", " * Using a parameters interface makes evolving the SPI over time much simpler.  Its easy to add additional parameters in the future w/o breaking existing code that was written against the SPI.\n", " * Using the [Configuration](https://github.com/apache/accumulo/blob/353c611891a8b88b307dcba5e884467d4191b1f2/core/src/main/java/org/apache/accumulo/core/spi/common/ServiceEnvironment.java#L40) interface used by other SPI interface is consistent and offers more options.  For example this interface offers an isSet() method that allows checking if a user set a property.  There is no way to do this by analyzing the values because some props have default values.\n", "\n", " *\n", " * @since 2.1.0\n", " *\n", " */\n", "public interface ContextClassLoaderFactory {\n", "-  /**\n", "-   * Initialize the ClassLoaderFactory. Implementations may need a reference to the configuration so\n", "-   * that it can clean up contexts that are no longer being used.\n", "-   *\n", "-   * @param contextProperties\n", "-   *          Accumulo configuration properties\n", "-   * @throws Exception\n", "-   *           if error initializing ClassLoaderFactory\n", "-   */\n", "-  void initialize(Supplier<Map<String,String>> contextProperties) throws Exception;\n", "+  public interface InitParameters {\n", "+      /**\n", "+   * @return A view of Accumulo's system level configuration. This is backed by system level config\n", "+   *         in zookeeper, which falls back to site configuration, which falls back to the default\n", "+   *         configuration.\n", "+   */\n", "+    Configuration getConfiguration();\n", "+  }\n", "+\n", "+  /**\n", "+   * Initialize the ClassLoaderFactory. Implementations may need a reference to the configuration so\n", "+   * that it can clean up contexts that are no longer being used.\n", "+   *\n", "+   * @throws Exception\n", "+   *           if error initializing ClassLoaderFactory\n", "+   */\n", "+  void initialize(InitParameters initParam) throws Exception;\n", "\n", "  /**\n", "   *\n", "   * @param contextName\n", "   *          name of classloader context\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/saros-project/saros/pull/937#discussion_r413700794\n", "                 MAIN COMMIT: https://github.com/saros-project/saros/commit/2f9e1d5e762d9d2d5b05889c87e51aa23241582f\n", "               AUTHOR COMMIT: https://github.com/stefaus/saros/commit/2f9e1d5e762d9d2d5b05889c87e51aa23241582f\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: You could restructure the logic to make it easier to make the possible code paths easier to see and to avoid unnecessary writes of `null` to `smackTransfermanager`.\n", "\n", "I am not sure whether the case `currentManager != null && state == ConnectionState.CONNECTED` is actually possible – [probably not](https://github.com/saros-project/saros/blob/master/core/src/saros/net/ConnectionState.java#L18) – but I still wanted to cover it. If not, you could also move the call to `currentManager.removeFileTransferListener(smackTransferListener);` into the `else` block.\n", "\n", "        }\n", "      };\n", "\n", "  private final IConnectionListener connectionListener =\n", "      (connection, state) -> {\n", "-        FileTransferManager currentManager = smackTransferManager.getAndSet(null);\n", "-        if (currentManager != null) {\n", "-          currentManager.removeFileTransferListener(smackTransferListener);\n", "-        }\n", "-\n", "-        if (state == ConnectionState.CONNECTED) {\n", "-          FileTransferManager newManager = new FileTransferManager(connection);\n", "-          smackTransferManager.set(newManager);\n", "-          newManager.addFileTransferListener(smackTransferListener);\n", "-        }\n", "+        FileTransferManager currentManager = smackTransferManager.get();\n", "+\n", "+        if (currentManager == null && state != ConnectionState.CONNECTED) {\n", "+          return;\n", "+        }\n", "+\n", "+        if (currentManager != null) {\n", "+          currentManager.removeFileTransferListener(smackTransferListener);\n", "+        }\n", "+\n", "+        if (state == ConnectionState.CONNECTED) {\n", "+          FileTransferManager newManager = new FileTransferManager(connection);\n", "+          smackTransferManager.set(newManager);\n", "+          newManager.addFileTransferListener(smackTransferListener);\n", "+        } else {\n", "+          smackTransferManager.set(null);\n", "+        }\n", "      };\n", "\n", "  private final XMPPContactsService contactsService;\n", "\n", "  public XMPPFileTransferManager(\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/discordjs/discord.js/pull/7581#discussion_r871660644\n", "                 MAIN COMMIT: https://github.com/discordjs/discord.js/commit/1856ac28504aad1a690cd5553852601baeb939b4\n", "               AUTHOR COMMIT: https://github.com/legendhimself/discord.js/commit/1856ac28504aad1a690cd5553852601baeb939b4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Now u can change \n", "https://github.com/discordjs/discord.js/blob/1856ac28504aad1a690cd5553852601baeb939b4/src/client/websocket/WebSocketShard.js#L373...L379\n", "to \n", "```js\n", "this.emitClose(event);\n", "```\n", "\n", "     * @event WebSocketShard#close\n", "     * @param {CloseEvent} event The received event\n", "     */\n", "    this.emit(ShardEvents.CLOSE, event);\n", "  }\n", "-  /**\n", "-   * Manually emit close since the ws never received the close frame.\n", "-   */\n", "-  emitClose() {\n", "-    /**\n", "-     * Emitted when a shard's WebSocket closes.\n", "-     * @private\n", "-     * @event WebSocketShard#close\n", "-     * @param {CloseEvent} event The received event\n", "-     */\n", "-    this.emit(ShardEvents.CLOSE, {\n", "-      code: 1011,\n", "-      reason: WSCodes[1011],\n", "-      /**\n", "-       * - `wasClean` property is set to false when the WebSocket connection did not close via the close handshake.\n", "-       * i.e. by not receiving a valid Close frame from the server.\n", "-       */\n", "-      was<PERSON><PERSON>: false,\n", "-    });\n", "-  }\n", "+  /**\n", "+   * Manually emit close since the ws never received the close frame.\n", "+   * @param {CloseEvent} [event] Close event that was received\n", "+   */\n", "+  emitClose(event = {\n", "+      code: 1011,\n", "+      reason: WSCodes[1011],\n", "+      wasClean: false,\n", "+    }) {\n", "+    /**\n", "+     * Emitted when a shard's WebSocket closes.\n", "+     * @private\n", "+     * @event WebSocketShard#close\n", "+     * @param {CloseEvent} event The received event\n", "+     */\n", "+    this.emit(ShardEvents.CLOSE, event);\n", "+  }\n", "\n", "  /**\n", "   * Called whenever a packet is received.\n", "   * @param {Object} packet The received packet\n", "   * @private\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/directus/directus/pull/14737#discussion_r1141495604\n", "                 MAIN COMMIT: https://github.com/directus/directus/commit/c0054866aa5ff9d90e5d9556082e907fa99da3be\n", "               AUTHOR COMMIT: https://github.com/br41nslug/directus/commit/c0054866aa5ff9d90e5d9556082e907fa99da3be\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: <br>\n", "\n", "The ELSE block below uses `info.websocket` instead of `info.realtime`:\n", "\n", "https://github.com/directus/directus/blob/c0054866aa5ff9d90e5d9556082e907fa99da3be/api/src/services/server.ts#L116-L118\n", "\n", "so this change assumes `websocket` is the \"finalized\" name. 👍\n", "\n", "\t\t\t};\n", "\t\t}\n", "\n", "\t\tif (this.accountability?.user) {\n", "\t\t\tif (env.WEBSOCKETS_ENABLED) {\n", "-\t\t\t\tinfo.realtime = {};\n", "-\n", "-\t\t\t\tinfo.realtime.rest = env.WEBSOCKETS_REST_ENABLED\n", "-\t\t\t\t\t? {\n", "-\t\t\t\t\t\t\tauthentication: env.WEBSOCKETS_REST_AUTH,\n", "-\t\t\t\t\t\t\tpath: env.WEBSOCKETS_REST_PATH,\n", "-\t\t\t\t\t  }\n", "-\t\t\t\t\t: false;\n", "-\n", "-\t\t\t\tinfo.realtime.graphql = env.WEBSOCKETS_GRAPHQL_ENABLED\n", "-\t\t\t\t\t? {\n", "-\t\t\t\t\t\t\tauthentication: env.WEBSOCKETS_GRAPHQL_AUTH,\n", "-\t\t\t\t\t\t\tpath: env.WEBSOCKETS_GRAPHQL_PATH,\n", "-\t\t\t\t\t  }\n", "-\t\t\t\t\t: false;\n", "-\n", "-\t\t\t\tinfo.realtime.heartbeat = env.WEBSOCKETS_HEARTBEAT_ENABLED ? env.WEBSOCKETS_HEARTBEAT_FREQUENCY : false;\n", "+\t\t\t\tinfo.websocket = {};\n", "+\n", "+\t\t\t\tinfo.websocket.rest = env.WEBSOCKETS_REST_ENABLED\n", "+\t\t\t\t\t? {\n", "+\t\t\t\t\t\t\tauthentication: env.WEBSOCKETS_REST_AUTH,\n", "+\t\t\t\t\t\t\tpath: env.WEBSOCKETS_REST_PATH,\n", "+\t\t\t\t\t  }\n", "+\t\t\t\t\t: false;\n", "+\n", "+\t\t\t\tinfo.websocket.graphql = env.WEBSOCKETS_GRAPHQL_ENABLED\n", "+\t\t\t\t\t? {\n", "+\t\t\t\t\t\t\tauthentication: env.WEBSOCKETS_GRAPHQL_AUTH,\n", "+\t\t\t\t\t\t\tpath: env.WEBSOCKETS_GRAPHQL_PATH,\n", "+\t\t\t\t\t  }\n", "+\t\t\t\t\t: false;\n", "+\n", "+\t\t\t\tinfo.websocket.heartbeat = env.WEBSOCKETS_HEARTBEAT_ENABLED ? env.WEBSOCKETS_HEARTBEAT_FREQUENCY : false;\n", "\t\t\t} else {\n", "\t\t\t\tinfo.websocket = false;\n", "\t\t\t}\n", "\t\t}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/git-lfs/git-lfs/pull/4446#discussion_r672572871\n", "                 MAIN COMMIT: https://github.com/git-lfs/git-lfs/commit/81452ac17ce2861060943a7a81f3367885e1347d\n", "               AUTHOR COMMIT: https://github.com/bk2204/git-lfs/commit/81452ac17ce2861060943a7a81f3367885e1347d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: With my \"bad server\" thinking cap on again, should we guard against multiple or invalid (e.g., negative) `size` argument packet lines here?\n", "\n", "The proposed API spec does [say](https://github.com/git-lfs/git-lfs/blob/05f7e0c8b8ee7378ebbb87b8fb5d655a871cf2b3/docs/proposals/ssh_adapter.md#requests-to-transfer-objects) that \"unknown arguments should be ignored\".  But since the `\"size\"` argument is known and expected, maybe something like the following would cover cases of extra, missing, and invalid ones?  (I'm unsure if a zero size is acceptable; I allowed it here.)\n", "\n", "\t\tif buffer.Len() > 1024 {\n", "\t\t\tbuffer.Truncate(1024)\n", "\t\t}\n", "\t\treturn errors.NewRetriableError(fmt.<PERSON>rrorf(\"got status %d when fetching OID %s: %s\", status, t.Oid, buffer.String()))\n", "\t}\n", "-\tactualSize := int64(-1)\n", "-\tfor _, arg := range args {\n", "-\t\tentries := strings.SplitN(arg, \"=\", 2)\n", "-\t\tif entries[0] == \"size\" {\n", "-\t\t\tval, err := strconv.ParseInt(entries[1], 10, 64)\n", "-\t\t\tif err != nil {\n", "-\t\t\t\treturn fmt.<PERSON><PERSON><PERSON>(\"expected valid size, got %q\", entries[1])\n", "-\t\t\t}\n", "-\t\t\tactualSize = val\n", "-\t\t}\n", "-\t}\n", "+\tvar actualSize int64\n", "+\tseenSize := false\n", "+\tfor _, arg := range args {\n", "+\t\tif strings.HasPrefix(arg, \"size=\") {\n", "+\t\t\tif seenSize {\n", "+\t\t\t\treturn errors.NewProtocolError(\"unexpected size argument\", nil)\n", "+\t\t\t}\n", "+\t\t\tactualSize, err = strconv.ParseInt(arg[5:], 10, 64)\n", "+\t\t\tif err != nil || actualSize < 0 {\n", "+\t\t\t\treturn errors.NewProtocolError(fmt.Sprintf(\"expected valid size, got %q\", arg[5:]), err)\n", "+\t\t\t}\n", "+\t\t\tseenSize = true\n", "+\t\t}\n", "+\t}\n", "+\tif !seenSize {\n", "+\t\treturn errors.NewProtocolError(\"no size argument seen\", nil)\n", "+\t}\n", "\tif actualSize != t.Size {\n", "\t\treturn fmt.<PERSON><PERSON><PERSON>(\"expected size %d, got %d\", t.<PERSON>, actualSize)\n", "\t}\n", "\n", "\tdlfilename := f.Name()\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/digitalfabrik/integreat-cms/pull/2029#discussion_r1099393067\n", "                 MAIN COMMIT: https://github.com/digitalfabrik/integreat-cms/commit/1cd65aec5d76b682400699773e90f15a3016f2ce\n", "               AUTHOR COMMIT: https://github.com/ztefanie/integreat-cms/commit/1cd65aec5d76b682400699773e90f15a3016f2ce\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Since we already specify [python-dateutil](https://github.com/digitalfabrik/integreat-cms/blob/7393f06527f0e5f6538ea8eeb2d97ac90aa0c6e0/setup.cfg#L67) as dependency, we might as well use it to generate rrule strings (see [docs](https://dateutil.readthedocs.io/en/stable/rrule.html)).\n", "\n", "We might take this as an occasion to refactor our recurrence rule model in the future to match the class definition of `rrule`, but this doesn't need to happen in this PR.\n", "\n", "        https://icalendar.org/iCalendar-RFC-5545/3-8-5-3-recurrence-rule.html\n", "\n", "        :return: The ical rrule string for the recurrence rule\n", "        :rtype: str\n", "        \"\"\"\n", "-        rrule = (\n", "-            \"RRULE:\"\n", "-            + \"FREQ=\"\n", "-            + self.frequency\n", "-            + \";INTERVAL=\"\n", "-            + str(self.interval)\n", "-            + \";\"\n", "-            + \"WKST=MO;\"\n", "-        )\n", "-        if self.weekdays_for_weekly:\n", "-            on_weekdays = \"\"\n", "-            for weekday in self.weekdays_for_weekly:\n", "-                on_weekdays = on_weekdays + calendar.day_name[weekday][:2].upper() + \",\"\n", "-            rrule = rrule + \"BYDAY=\" + on_weekdays[:-1]\n", "-        if self.week_for_monthly:\n", "-            rrule = (\n", "-                r<PERSON><PERSON>\n", "-                + \"BYDAY=\"\n", "-                + str(self.week_for_monthly)\n", "-                + calendar.day_name[self.weekday_for_monthly][:2].upper()\n", "-            )\n", "-        if self.recurrence_end_date:\n", "-            until_date_as_ical = self.recurrence_end_date.strftime(\"%Y%m%dT\")\n", "-            rrule = rrule + \";UNTIL=\" + until_date_as_ical\n", "-        return rrule\n", "+        kwargs = {}\n", "+        if self.frequency == frequency.WEEKLY:\n", "+            kwargs[\"byweekday\"] = self.weekdays_for_weekly\n", "+        elif self.frequency == frequency.MONTHLY:\n", "+            kwargs[\"byweekday\"] = rrule.weekday(self.weekday_for_monthly, self.week_for_monthly)\n", "+        if self.recurrence_end_date:\n", "+            kwargs[\"until\"] = make_aware(\n", "+                datetime.combine(self.recurrence_end_date, time.max)\n", "+            )\n", "+        ical_rrule = rrule.rrule(\n", "+            getattr(rrule, self.frequency),\n", "+            dtstart=self.event.start,\n", "+            interval=self.interval,\n", "+            **kwargs,\n", "+        )\n", "+        return str(ical_rrule)\n", "\n", "    class Meta:\n", "        #: The verbose name of the model\n", "        verbose_name = _(\"recurrence rule\")\n", "        #: The plural verbose name of the model\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/kedacore/keda/pull/3559#discussion_r949648734\n", "                 MAIN COMMIT: https://github.com/kedacore/keda/commit/24a9f6f738df5e2278d50186fa1f659c033b5a9f\n", "               AUTHOR COMMIT: https://github.com/fk128/keda/commit/24a9f6f738df5e2278d50186fa1f659c033b5a9f\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: https://github.com/kedacore/keda/blob/708890964f00c169ba990fa5fd497b47893a4ded/pkg/scalers/aws_sqs_queue_scaler_test.go#L258\n", "\n", "Here (on line258) use `testSQSResolvedEnv` instead of `testAWSSQSAuthentication` , and similarly for the other test as well.\n", "\n", "\t\t\"awsRegion\":       \"eu-west-1\",\n", "\t\t\"scaleOnInFlight\": \"true\"},\n", "\t\ttestAWSSQSAuthentication,\n", "\t\tfalse,\n", "\t\t\"properly formed queue and region\"},\n", "-\t{map[string]string{\n", "-\t\t\"queueURLFromEnv\": \"QUEUE_URL\",\n", "-\t\t\"queueLength\":     \"1\",\n", "-\t\t\"awsRegion\":       \"eu-west-1\"},\n", "-\t\ttestSQSResolvedEnv,\n", "-\t\tfalse,\n", "-\t\t\"properly formed queue loaded from env\"},\n", "-\t{map[string]string{\n", "-\t\t\"queueLength\": \"1\",\n", "-\t\t\"awsRegion\":   \"eu-west-1\"},\n", "-\t\ttestSQSResolvedEnv,\n", "-\t\ttrue,\n", "-\t\t\"missing queue url from both queueURL and queueURLFromEnv\"},\n", "+\t{map[string]string{\n", "+\t\t\"queueURLFromEnv\": \"QUEUE_URL\",\n", "+\t\t\"queueLength\":     \"1\",\n", "+\t\t\"awsRegion\":       \"eu-west-1\"},\n", "+\t\ttestAWSSQSAuthentication,\n", "+\t\tfalse,\n", "+\t\t\"properly formed queue loaded from env\"},\n", "+\t\t\n", "+\t\t\n", "+\t\t\n", "+\t{map[string]string{\n", "+\t\t\"queueLength\": \"1\",\n", "+\t\t\"awsRegion\":   \"eu-west-1\"},\n", "+\t\ttestAWSSQSAuthentication ,\n", "+\t\ttrue,\n", "+\t\t\"missing queue url from both queueURL and queueURLFromEnv\"},\n", "}\n", "\n", "var awsSQSMetricIdentifiers = []awsSQSMetricIdentifier{\n", "\t{&testAWSSQSMetadata[1], 0, \"s0-aws-sqs-DeleteArtifactQ\"},\n", "\t{&testAWSSQSMetadata[1], 1, \"s1-aws-sqs-DeleteArtifactQ\"},\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/ent/contrib/pull/76#discussion_r635093781\n", "                 MAIN COMMIT: https://github.com/ent/contrib/commit/b95ed8a5accb359548e04ae75af467f914bb15b8\n", "               AUTHOR COMMIT: https://github.com/cliedeman/contrib/commit/b95ed8a5accb359548e04ae75af467f914bb15b8\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Can we move this logic to https://github.com/ent/contrib/blob/master/entgql/template.go?\n", "\n", "// limitations under the License.\n", "\n", "package entgql\n", "\n", "import \"entgo.io/ent/entc/gen\"\n", "-func filterNodes(nodes []*gen.Type) []*gen.Type {\n", "-\tvar filteredNodes []*gen.Type\n", "-\tfor _, n := range nodes {\n", "-\t\tann := EntgqlAnnotate(n.Annotations)\n", "-\t\tif ann == nil || !ann.<PERSON> {\n", "-\t\t\tfilteredNodes = append(filteredNodes, n)\n", "-\t\t}\n", "-\t}\n", "-\treturn filteredNodes\n", "-}\n", "+func filterNodes(nodes []*gen.Type) ([]*gen.Type, error) {\n", "+\tvar filteredNodes []*gen.Type\n", "+\tfor _, n := range nodes {\n", "+\t\tant := &Annotation{}\n", "+\t\tif n.Annotations != nil && n.Annotations[ant.Name()] != nil {\n", "+\t\t\tif err := ant.Decode(n.Annotations[ant.Name()]); err != nil {\n", "+\t\t\t\treturn nil, err\n", "+\t\t\t}\n", "+\t\t\tif ann.<PERSON> {\n", "+\t\t\t\tcontinue\n", "+\t\t\t}\n", "+\t\t}\n", "+\t\tfilteredNodes = append(filteredNodes, n)\n", "+\t}\n", "+\treturn filteredNodes\n", "+}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/nginx/agent/pull/207#discussion_r1114795780\n", "                 MAIN COMMIT: https://github.com/nginx/agent/commit/d739bb531bcbbca850fee83ba5367038c8dad5de\n", "               AUTHOR COMMIT: https://github.com/achawla2012/agent/commit/d739bb531bcbbca850fee83ba5367038c8dad5de\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: nit: golang has some specific rules and conventions around doc comments: https://tip.golang.org/doc/comment\n", "\n", "These doc comments get processed and then publicized online, e.g. https://pkg.go.dev/github.com/nginx/agent/v2@v2.23.0/src/core#EnvironmentType.DiskDevices\n", "\n", "The format isn't markdown, but it's close, and whitespace has significance. This comment will be rendered as a code block due to the leading spaces.\n", "\n", "To follow the conventions and match your intent I think we want something like:\n", "\n", "\t\tlog.Tracef(\"HostInfo created: %v\", hostInfoFacacde)\n", "\t\tenv.host = hostInfoFacacde\n", "\t}\n", "\treturn env.host\n", "}\n", "-/*  Returns string containing\n", "-\t   sysname\t Name of the operating system implementation.\n", "-\t   nodename\t Network name of this machine.\n", "-\t   release\t Release level of the operating\tsystem.\n", "-\t   version\t Version level of the operating\tsystem.\n", "-\t   machine\t Machine hardware platform.\n", "-\n", "-\tDifferent platforms have different Utsname struct definition.\n", "-\thttps://cs.opensource.google/search?q=utsname&ss=go%2Fx%2Fsys&start=11\n", "-\n", "-\tTODO :- Make this function platform agnotic to pull uname (uname -a).\n", "-*/\n", "-func (env *EnvironmentType) GetUnixName() string {\n", "+// GetUnixName returns details about this operating system formatted as \"sysname\n", "+// nodename release version machine\". Returns \"\" if unix name cannot be\n", "+// determined.\n", "+//\n", "+//   - sysname: Name of the operating system implementation.\n", "+//   - nodename: Network name of this machine.\n", "+//   - release: Release level of the operating system.\n", "+//   - version: Version level of the operating system.\n", "+//   - machine: Machine hardware platform.\n", "+//\n", "+// Different platforms have different [Utsname] struct definitions.\n", "+//\n", "+// TODO :- Make this function platform agnostic to pull uname (uname -a).\n", "+//\n", "+// [Utsname]: https://cs.opensource.google/search?q=utsname&ss=go%2Fx%2Fsys&start=11\n", "+func (env *EnvironmentType) GetUnixName() string {\n", "\tvar utsname unix.Utsname\n", "\terr := unix.Uname(&utsname)\n", "\tif(err != nil) {\n", "\t\tlog.Warnf(\"Unable to read Uname. Error: %v\", err)\n", "\t\treturn \"\"\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/optuna/optuna/pull/1196#discussion_r424170923\n", "                 MAIN COMMIT: https://github.com/optuna/optuna/commit/c8527b881f25101e5f0f3bf9a8f7a82daa18a433\n", "               AUTHOR COMMIT: https://github.com/HideakiImamura/optuna/commit/c8527b881f25101e5f0f3bf9a8f7a82daa18a433\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The type hinting for `sampler_cls` is `abc.ABCMeta`, but it is actually a kind of constructor of `BaseStorage`.\n", "We can see similar code in [`tests/storage_tests`](https://github.com/optuna/optuna/blob/master/tests/storages_tests/test_storages.py#L105-L115), and how about using the same approach here?\n", "\n", "The following code is just an example. If you use it, you also need to update `sampler_cls(**sampler_kwargs)` and `sample_cls.__name__`.\n", "\n", "\n", "def test_hyperband_max_resource_value_error() -> None:\n", "    with pytest.raises(ValueError):\n", "        _ = optuna.pruners.HyperbandPruner(max_resource=\"not_appropriate\")\n", "\n", "-@pytest.mark.parametrize(\n", "-    \"sampler_cls,sampler_kwargs\",\n", "-    [\n", "-        (optuna.samplers.RandomSampler, {}),\n", "-        (optuna.samplers.TPESampler, {\"n_startup_trials\": 1}),\n", "-        (\n", "-            optuna.samplers.GridSampler,\n", "-            {\"search_space\": {\"value\": numpy.linspace(0.0, 1.0, 10, endpoint=False).tolist()}},\n", "-        ),\n", "-        (optuna.samplers.CmaEsSampler, {\"n_startup_trials\": 1}),\n", "-    ],\n", "-)\n", "-def test_hyperband_filter_study(sampler_cls: abc.ABCMeta, sampler_kwargs: Dict[str, Any]) -> None:\n", "+@pytest.mark.parametrize(\n", "+    \"sampler_init_func\",\n", "+    [\n", "+        (optuna.samplers.RandomSampler),\n", "+        (lambda: optuna.samplers.TPESampler(n_startup_trials=1)),\n", "+        (\n", "+            lambda: optuna.samplers.GridSampler(\n", "+                search_space={\"value\": numpy.linspace(0.0, 1.0, 10, endpoint=False).tolist()}\n", "+            )\n", "+        ),\n", "+        (lambda: optuna.samplers.CmaEsSampler(n_startup_trials=1)),\n", "+    ],\n", "+)\n", "+def test_hyperband_filter_study(sampler_init_func: Callable[[], optuna.samplers.BaseSampler]) -> None:\n", "    def objective(trial: optuna.trial.Trial) -> float:\n", "        return trial.suggest_uniform(\"value\", 0.0, 1.0)\n", "\n", "    n_trials = 8\n", "    n_brackets = 4\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/googleapis/python-bigquery/pull/778#discussion_r755328186\n", "                 MAIN COMMIT: https://github.com/googleapis/python-bigquery/commit/072b785fce6c3d77252a65d8c69df7f12183a1d1\n", "               AUTHOR COMMIT: https://github.com/loferris/python-bigquery/commit/072b785fce6c3d77252a65d8c69df7f12183a1d1\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: We've found that some customers get confused when necessary variables are commented out. Since we want to use the value from the fixture, we'll need to save the original value and then set it back.\n", "\n", "See https://github.com/googleapis/python-bigquery/blob/21cd71022d60c32104f8f90ee2ca445fbb43f7f3/samples/geography/insert_geojson.py#L23-L30\n", "\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n", "\n", "\n", "def revoke_dataset_access(dataset_id, entity_id):\n", "-    # [START bigquery_revoke_dataset_access]\n", "-\n", "-    # TODO(developer): Set dataset_id to the ID of the dataset to fetch.\n", "-    # dataset_id = 'your-project.your_dataset'\n", "-\n", "-    # TOD<PERSON>(developer): Set entity_id to the ID of the email or group from whom you are revoking access.\n", "-    # entity_id = \"<EMAIL>\"\n", "+    original_dataset_id = dataset_id\n", "+    original_entity_id = entity_id\n", "+    \n", "+    # [START bigquery_revoke_dataset_access]\n", "+\n", "+    # TODO(developer): Set dataset_id to the ID of the dataset to fetch.\n", "+    dataset_id = \"your-project.your_dataset\"\n", "+\n", "+    # TOD<PERSON>(developer): Set entity_id to the ID of the email or group from whom you are revoking access.\n", "+    entity_id = \"<EMAIL>\"\n", "+     # [END bigquery_revoke_dataset_access]\n", "+     dataset_id = original_dataset_id\n", "+     entity_id = original_entity_id\n", "+     # [START bigquery_revoke_dataset_access]\n", "\n", "    from google.cloud import bigquery\n", "\n", "    # Construct a BigQuery client object.\n", "    client = bigquery.Client()\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/radiantearth/radiant-mlhub/pull/71#discussion_r816267694\n", "                 MAIN COMMIT: https://github.com/radiantearth/radiant-mlhub/commit/7fac6b4e9b92f5371ec9e20805d4c1b6d64fe148\n", "               AUTHOR COMMIT: https://github.com/guidorice/radiant-mlhub/commit/7fac6b4e9b92f5371ec9e20805d4c1b6d64fe148\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Looks like we don't need to ignore this error if we add type annotations to the `api_key` and `profile` parameters. I'm guessing this code was based on the [`radiant_mlhub.models.Collection.__init__` method](https://github.com/radiantearth/radiant-mlhub/blob/c179d7d65995a30f8d4d9237ab69a13cc994fb8c/radiant_mlhub/models.py#L31), which similarly includes a `# type: ignore` statement. I'm not sure if we needed this for an older version of PySTAC or if I was just being lazy 😊 , but we should probably fix it for the `MLModel` class at least. If you want to fix it in the `Collection` class by adding type annotations feel free, otherwise I can open a separate PR.\n", "\n", "    session_kwargs: Dict[str, Any] = {}\n", "\n", "    \"\"\"\n", "    Class inheriting from :class:`pystac.Item` that adds some convenience methods for listing and fetching from the Radiant MLHub API.\n", "    \"\"\"\n", "-    def __init__(  # type: ignore[no-untyped-def]\n", "-        self,\n", "-        id: str,\n", "-        geometry: Optional[Dict[str, Any]],\n", "-        bbox: Optional[List[float]],\n", "-        datetime: Optional[Datetime],\n", "-        properties: Dict[str, Any],\n", "-        stac_extensions: Optional[List[str]] = None,\n", "-        href: Optional[str] = None,\n", "-        collection: Optional[Union[str, Collection]] = None,\n", "-        extra_fields: Optional[Dict[str, Any]] = None,\n", "-        *,\n", "-        api_key=None,\n", "-        profile=None\n", "+    def __init__(\n", "+        self,\n", "+        id: str,\n", "+        geometry: Optional[Dict[str, Any]],\n", "+        bbox: Optional[List[float]],\n", "+        datetime: Optional[Datetime],\n", "+        properties: Dict[str, Any],\n", "+        stac_extensions: Optional[List[str]] = None,\n", "+        href: Optional[str] = None,\n", "+        collection: Optional[Union[str, Collection]] = None,\n", "+        extra_fields: Optional[Dict[str, Any]] = None,\n", "+        *,\n", "+        api_key: Optional[str] = None,\n", "+        profile: Optional[str] = None\n", "    ):\n", "        super().__init__(\n", "            id=id,\n", "            geometry=geometry,\n", "            bbox=bbox,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/twisted/twisted/pull/1551#discussion_r596455434\n", "                 MAIN COMMIT: https://github.com/twisted/twisted/commit/****************************************\n", "               AUTHOR COMMIT: https://github.com/twm/twisted/commit/****************************************\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Just a minor style suggestion.\n", "\n", "I think that this refactoring will also solve the missing branch coverage that is preventing this diff for being 100%\n", "\n", "In Codecov, yellow is missing branch coverage.\n", "\n", "https://app.codecov.io/gh/twisted/twisted/compare/1551/diff#D1L1893\n", "\n", "    def _dataReceived_BODY(self) -> bool:\n", "        if len(self._buffer) >= self.length:\n", "            chunk = memoryview(self._buffer)[: self.length].tobytes()\n", "            del self._buffer[: self.length]\n", "            self.state = \"CRLF\"\n", "-            self.dataCallback(chunk)\n", "-        elif self._buffer:\n", "-            chunk = bytes(self._buffer)\n", "-            self.length -= len(chunk)\n", "-            self._buffer = bytearray()\n", "-            self.dataCallback(chunk)\n", "-        return True\n", "+            self.dataCallback(chunk)\n", "+            return True\n", "+\n", "+        if not self._buffer:\n", "+             # No data received yet for the current chunk.\n", "+            return True\n", "+\n", "+        # We have data and we are not at the end of the chunk.\n", "+        chunk = bytes(self._buffer)\n", "+        self.length -= len(chunk)\n", "+        self._buffer = bytearray()\n", "+        self.dataCallback(chunk)\n", "+        return True\n", "\n", "    def _dataReceived_FINISHED(self) -> bool:\n", "        raise RuntimeError(\n", "            \"_ChunkedTransferDecoder.dataReceived called after last \"\n", "            \"chunk was processed\"\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/zenodo/zenodo/pull/2198#discussion_r705994060\n", "                 MAIN COMMIT: https://github.com/zenodo/zenodo/commit/bef96a87a08bc886e9031af0f3840c30b405b51d\n", "               AUTHOR COMMIT: https://github.com/mitsosf/zenodo/commit/bef96a87a08bc886e9031af0f3840c30b405b51d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: - I think you're right about the `RecordSearch` class, it probably messes things up. You can use the base ElasticSearch-DSL `Search` class as done [here](https://github.com/zenodo/zenodo/blob/9e044a035a4b66d75949c609f29794c44e2b739e/zenodo/modules/stats/exporters.py#L63-L65). Basically you have to pass the `using=<client>` parameter, and apply some index prefixing (i.e. to make `events-stats-*` -> `zenodo-events-stats-*`).\n", "- We also need to filter results in the specific time range (i.e. starting from 2021-01-01), but see the next point.\n", "- For some inspiration for using the `cardinality` metric aggregation over a specific time range see [this example](https://github.com/inveniosoftware/invenio-stats/blob/master/invenio_stats/utils.py#L49-L61).\n", "\n", "        return 1\n", "\n", "    @staticmethod\n", "    def get_visitors():\n", "        \"\"\"Get number of unique zenodo users.\"\"\"\n", "-        # Query: '\"aggs\":{\"visitors_count\":{\"cardinality\":{' \\\n", "-        #                '\"field\":\"visitor_id\",\"precision_threshold\":' \\\n", "-        #                '40000}}}'\n", "-        # search = RecordsSearch(index='events-stats-*') \\\n", "-        #     .aggs.bucket('visitors_count', 'value_count',\n", "-        #                  field='visitor_id')\n", "-        # return search.count()\n", "+        # Query: '\"aggs\":{\"visitors_count\":{\"cardinality\":{' \\\n", "+        #                '\"field\":\"visitor_id\",\"precision_threshold\":' \\\n", "+        #                '40000}}}'\n", "+        search = Search(\n", "+            using=current_search_client,\n", "+            index='events-stats-*'\n", "+        ).filter(\n", "+            'range', ...,\n", "+        ).aggs.metric(\n", "+            'visitors_count', 'cardinality', field='visitor_id'\n", "+        )\n", "+        result = search.execute()\n", "+        return result.aggregations.visitors_count.value\n", "        return 1\n", "\n", "    @staticmethod\n", "    def get_researchers():\n", "        \"\"\"Get number of unique zenodo users.\"\"\"\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/interledger/rafiki/pull/323#discussion_r861948149\n", "                 MAIN COMMIT: https://github.com/interledger/rafiki/commit/ae2a7aff5193b5fc5f53da331eedcecdb77ada6f\n", "               AUTHOR COMMIT: https://github.com/sabineschaller/rafiki/commit/ae2a7aff5193b5fc5f53da331eedcecdb77ada6f\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: You could make `receivedAmount` required by adding it and `receivedAmountValue` as [virtual attributes](https://github.com/interledger/rafiki/blob/b038caaa90063164d0a0f02515cdeb59b7170844/packages/backend/src/open_payments/payment/incoming/model.ts#L53-L54)\n", "\n", "\n", "  public processAt!: Date | null\n", "\n", "  public readonly assetId!: string\n", "  public asset!: Asset\n", "-  public receivedAmount?: Amount\n", "+  private receivedAmountValue?: bigint\n", "+\n", "+  public get receivedAmount(): Amount {\n", "+    return {\n", "+      value: this.receivedAmountValue || BigInt(0),\n", "+      assetCode: this.asset.code,\n", "+      assetScale: this.asset.scale\n", "+    }\n", "+  }\n", "+\n", "+  public set receivedAmount(amount: Amount) {\n", "+    this.receivedAmountValue = amount.value\n", "+  }\n", "\n", "  private incomingAmountValue?: bigint | null\n", "\n", "  public get incomingAmount(): Amount | undefined {\n", "    if (this.incomingAmountValue) {\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/transcom/mymove/pull/2386#discussion_r308813079\n", "                 MAIN COMMIT: https://github.com/transcom/mymove/commit/d2e87e109c5e06035c72936858609f174932eedc\n", "               AUTHOR COMMIT: https://github.com/lynzt/mymove/commit/d2e87e109c5e06035c72936858609f174932eedc\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: You need this code from:\n", "\n", "- https://github.com/transcom/mymove/blob/master/cmd/milmove/migrate.go#L52-L54\n", "- https://github.com/transcom/mymove/blob/master/cmd/milmove/gen_office_user_migration.go#L59-L65\n", "\n", "func InitAddDutyStationsFlags(flag *pflag.FlagSet) {\n", "\tflag.StringP(DutyStationsFilenameFlag, \"f\", \"\", \"File name of csv file containing the new office users\")\n", "}\n", "\n", "// CheckAddDutyStations validates add_office_users command line flags\n", "-func CheckAddDutyStations(v *viper.Viper) error {\n", "+func CheckAddDutyStations(v *viper.Viper) error {\n", "+\n", "+\tif err := cli.CheckDatabase(v, logger); err != nil {\n", "+\t\treturn err\n", "+\t}\n", "+\t\n", "+\tif err := cli.CheckMigration(v); err != nil {\n", "+\t\treturn err\n", "+\t}\n", "+\n", "+\tif err := cli.CheckMigrationFile(v); err != nil {\n", "+\t\treturn err\n", "+\t}\n", "\tDutyStationsFilename := v.GetString(DutyStationsFilenameFlag)\n", "\tif DutyStationsFilename == \"\" {\n", "\t\treturn fmt.Errorf(\"--office-users-filename is required\")\n", "\t}\n", "\treturn nil\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/skypilot-org/skypilot/pull/975#discussion_r1044071394\n", "                 MAIN COMMIT: https://github.com/skypilot-org/skypilot/commit/6b53f1765839b77fcb0b39ab36b965b5bb0fad72\n", "               AUTHOR COMMIT: https://github.com/WoosukKwon/skypilot/commit/6b53f1765839b77fcb0b39ab36b965b5bb0fad72\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: very small nit: When the zone is set, the region will always be filled already as shown below https://github.com/skypilot-org/skypilot/blob/6b53f1765839b77fcb0b39ab36b965b5bb0fad72/sky/resources.py#L272-L276\n", "\n", "Therefore, we can make the code faster by not going into the for loop if the region is None:\n", "\n", "            region_zones = gcp_region_zones\n", "\n", "        # If the region or zone is specified, filter out the other regions\n", "        # and zones.\n", "        filtered_region_zones = []\n", "-        for region, zones in region_zones:\n", "-            if self._region is not None and region.name != self._region:\n", "-                continue\n", "-            if self._zone is None:\n", "-                filtered_region_zones.append((region, zones))\n", "-            else:\n", "-                filtered_zones = [\n", "-                    zone for zone in zones if zone.name == self._zone\n", "-                ]\n", "-                if filtered_zones:\n", "-                    filtered_region_zones.append((region, filtered_zones))\n", "+        if self._region is not None:\n", "+            for region, zones in region_zones:\n", "+                if region.name != self._region:\n", "+                    continue\n", "+                if self._zone is None:\n", "+                    filtered_region_zones.append((region, zones))\n", "+                else:\n", "+                    filtered_zones = [\n", "+                        zone for zone in zones if zone.name == self._zone\n", "+                    ]\n", "+                    if filtered_zones:\n", "+                        filtered_region_zones.append((region, filtered_zones))\n", "        return filtered_region_zones\n", "\n", "    def _try_validate_instance_type(self) -> None:\n", "        if self.instance_type is None:\n", "            return\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/godaddy/tartufo/pull/272#discussion_r746148007\n", "                 MAIN COMMIT: https://github.com/godaddy/tartufo/commit/af061147e71fd20aa383a6e0b5a98adabfc1e297\n", "               AUTHOR COMMIT: https://github.com/rbailey-godaddy/tartufo/commit/af061147e71fd20aa383a6e0b5a98adabfc1e297\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: It's important that these are specifically deprecation warnings, or the user will likely not see them: https://github.com/godaddy/tartufo/blob/3b4acef809158676ebf7dfa8b93ae6f0a525b199/tartufo/cli.py#L277-L278\n", "\n", "        self._hex_entropy_score = entropy_score * 4.0\n", "        self._b64_entropy_score = entropy_score * 6.0\n", "\n", "        # For backwards compatibility, allow the caller to manipulate each of\n", "        # these representation-specific scores directly (but complain about it).\n", "-        if self.global_options.hex_entropy_score:\n", "-            warnings.warn(\n", "-                \"--hex-entropy-score is deprecated. Use --entropy-sensitivity instead.\"\n", "-            )\n", "-            self._hex_entropy_score = self.global_options.hex_entropy_score\n", "-\n", "-        if self.global_options.b64_entropy_score:\n", "-            warnings.warn(\n", "-                \"--b64-entropy-score is deprecated. Use --entropy-sensitivity instead.\"\n", "-            )\n", "+        if self.global_options.hex_entropy_score:\n", "+            warnings.warn(\n", "+                \"--hex-entropy-score is deprecated. Use --entropy-sensitivity instead.\",\n", "+                DeprecationWarning\n", "+            )\n", "+            self._hex_entropy_score = self.global_options.hex_entropy_score\n", "+\n", "+        if self.global_options.b64_entropy_score:\n", "+            warnings.warn(\n", "+                \"--b64-entropy-score is deprecated. Use --entropy-sensitivity instead.\",\n", "+                DeprecationWarning\n", "+            )\n", "            self._b64_entropy_score = self.global_options.b64_entropy_score\n", "\n", "    @property\n", "    def completed(self) -> bool:\n", "        \"\"\"Return True if scan has completed\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Go/pull/298#discussion_r706602184\n", "                 MAIN COMMIT: https://github.com/TheAlgorithms/Go/commit/3e6c6813d9a402a30c9575a999f3affcf69959ba\n", "               AUTHOR COMMIT: https://github.com/Zzocker/Go/commit/3e6c6813d9a402a30c9575a999f3affcf69959ba\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Could you look into our [this section](https://github.com/TheAlgorithms/Go/blob/master/CONTRIBUTING.md#typical-structure-of-a-program) in our contribution guideline.\n", "\n", "\n", "-// Package heap : max heap implemnation\n", "-// https://en.wikipedia.org/wiki/Heap_(data_structure)\n", "-package heap\n", "+// filename\n", "+// description: Add one line description here\n", "+// details:\n", "+// This is a multi-line\n", "+// description containing links, references,\n", "+// math equations, etc.\n", "+// author(s) [Name](https://github.com/handle), [Name](https://github.com/handle)\n", "+// see relatedfile.go, anotherfile.go, file_test.go\n", "+\n", "+// Package heap implements a max heap\n", "+// https://en.wikipedia.org/wiki/Heap_(data_structure)\n", "+package heap\n", "\n", "// Heap represents a max heap\n", "type Heap struct {\n", "\tbucket []int\n", "\tSize   int\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/openlayers/openlayers/pull/11722#discussion_r523470289\n", "                 MAIN COMMIT: https://github.com/openlayers/openlayers/commit/7cc45c967b2abd48e8f2d3dab404dee148c0a665\n", "               AUTHOR COMMIT: https://github.com/ahocevar/openlayers/commit/7cc45c967b2abd48e8f2d3dab404dee148c0a665\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Apparently it is  not possible to use 5-digit unicode escape codes in js ...\n", "\n", "The data I used to look it up is this:\n", "https://github.com/openlayers/openlayers/blob/e7a23d619adc41f8ebe7b57ac5e42fe528533c1c/unicode14.ods?raw=true\n", "sourced from: https://www.unicode.org/Public/14.0.0/ucd/UnicodeData-14.0.0d3.txt\n", "\n", "RTL characters are of type `AL` or `R` (orange in the spreadsheet),  LTR characters are of type `L` (blue in the spreadsheet), see: https://www.unicode.org/reports/tr9/#Table_Bidirectional_Character_Types\n", "\n", " * @return {BBox} Declutter bbox.\n", " */\n", "function getDeclutterBox(replayImageOrLabelArgs) {\n", "  return replayImageOrLabelArgs[3].declutterBox;\n", "}\n", "-const rtlRegEx = /[\\u0591-\\u07FF]/;\n", "+const rtlRegEx = new RegExp(\n", "+  /* eslint-disable prettier/prettier */\n", "+  '[' +\n", "+    String.fromCharCode(0x00591) + '-' + String.fromCharCode(0x008ff) +\n", "+    String.fromCharCode(0x0fb1d) + '-' + String.fromCharCode(0x0fdff) +\n", "+    String.fromCharCode(0x0fe70) + '-' + String.fromCharCode(0x0fefc) +\n", "+    String.fromCharCode(0x10800) + '-' + String.fromCharCode(0x10fff) +\n", "+    String.fromCharCode(0x1e800) + '-' + String.fromCharCode(0x1efff) +\n", "+  ']'\n", "+  /* eslint-enable prettier/prettier */\n", "+);\n", "\n", "/**\n", " * @param {string} text Text.\n", " * @param {string} align Alignment.\n", " * @return {number} Text alignment.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/mongodb/go-client-mongodb-atlas/pull/296#discussion_r882458842\n", "                 MAIN COMMIT: https://github.com/mongodb/go-client-mongodb-atlas/commit/ea3c0bd46f514aa84fd40517155df3c11369ffee\n", "               AUTHOR COMMIT: https://github.com/martinstibbe/go-client-mongodb-atlas/commit/ea3c0bd46f514aa84fd40517155df3c11369ffee\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Big change, we organize the client the same way we orgnaize the docs\n", "From [README](https://github.com/mongodb/go-client-mongodb-atlas#usage)\n", ">The services of a client divide the API into logical chunks and correspond to the structure of the Atlas API documentation at https://docs.atlas.mongodb.com/api/.\n", "\n", "This means this should be\n", "\n", "const federationSettingsDeleteBasePath = \"api/atlas/v1.0/federationSettings\"\n", "\n", "// FederatedSettingsService is an interface for working with the Federation Settings\n", "// endpoints of the MongoDB Atlas API.\n", "// See more: https://www.mongodb.com/docs/atlas/reference/api/federation-configuration/\n", "-type FederatedSettingsService interface {\n", "-\tGet(context.Context, string) (*FederatedSettings, *Response, error)\n", "-\tDelete(context.Context, string) (*Response, error)\n", "-}\n", "+type FederatedAuthenticationConfigurationService interface {\n", "+\tGet(context.Context, string) (*FederatedSettings, *Response, error)\n", "+\tListConnectedOrgs(...)\n", "+\tGetOneConnectedOrg(...)\n", "+\tUpdateConnectedOrg(...)\n", "+\tRemoveConnectedOrg(...)\n", "+\tListRoleMapping(...)\n", "+\tCreateRoleMapping(...)\n", "+\t// And the rest following the docs\n", "+\tDelete(context.Context, string) (*Response, error)\n", "+}\n", "\n", "// FederatedSettingsServiceOp handles communication with the FederatedSettings related methods of the\n", "// MongoDB Atlas API.\n", "type FederatedSettingsServiceOp service\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/openshift/windows-machine-config-operator/pull/618#discussion_r724338437\n", "                 MAIN COMMIT: https://github.com/openshift/windows-machine-config-operator/commit/07f9876d84b1390d7db401d2202e4eccb59ef7bb\n", "               AUTHOR COMMIT: https://github.com/saifshaikh48/windows-machine-config-operator/commit/07f9876d84b1390d7db401d2202e4eccb59ef7bb\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Won't the `defer func` always be called after `reconcileErr` is populated? We only want to call `MarkAsFree` depending on what `Reconcile` returns. This is the cleanest way I found to do that -- other options were: \n", "\n", "-  having to [call `<PERSON><PERSON><PERSON><PERSON>` everywhere we return with no error + no requeue](https://github.com/openshift/windows-machine-config-operator/commit/ea4e6b83360b441ae59a4ab01a57a451770495e5#diff-c5861f88c2edbbdc81d4ec5d1910af145b82e6acefe40f4c019b25ad112338dfL278-R298) in each controller\n", "- Making `Reconcile()` a wrapper, something like\n", "\n", "\t// Prevent WMCO upgrades while CSRs are being processed\n", "\tif err := condition.<PERSON><PERSON><PERSON><PERSON><PERSON>(r.client, r.watchNamespace, CSRController); err != nil {\n", "\t\treturn ctrl.Result{}, err\n", "\t}\n", "\tdefer func() {\n", "-\t\treconcileErr = tryToMarkAsFree(r.client, r.watchNamespace, CSRController, res.<PERSON>, reconcileErr)\n", "+func (r *Reconciler) Reconcile(ctx context.Context, request ctrl.Request) (ctrl.Result, error) {\n", "+\tresult, err := actualReconciler(ctx, request)\n", "+\tif result.Requeue || err != nil {\n", "+\t\treturn result, err\n", "+\t}\n", "+\treturn result, condition.<PERSON><PERSON><PERSON><PERSON>()\n", "+}\n", "+\n", "+func actualReconciler(ctx context.Context, request ctrl.Request) (ctrl.Result, error) {\n", "+\t// all the reconcile logic\n", "+}\n", "\t}()\n", "\n", "\tcertificateSigningRequest := &certificates.CertificateSigningRequest{}\n", "\tif err := r.client.Get(ctx, req.NamespacedName, certificateSigningRequest); err != nil {\n", "\t\tif k8sapierrors.IsNotFound(err) {\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Python/pull/2312#discussion_r471038377\n", "                 MAIN COMMIT: https://github.com/TheAlgorithms/Python/commit/f32d5c5386f27ac6b604a000dbdd67db6bedde56\n", "               AUTHOR COMMIT: https://github.com/AryanRaj315/Python/commit/f32d5c5386f27ac6b604a000dbdd67db6bedde56\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Adds a proper function name in snake_case with Python type hints and a doctest.  Please read [CONTRIBUTING.md](https://github.com/TheAlgorithms/Python/blob/master/CONTRIBUTING.md).\n", "\n", "    \"\"\"\n", "    This function is responsible for loading Qtable.txt if already present\n", "    \"\"\"\n", "    Q_table = pickle.load(f)\n", "\n", "-def Action(state, Q_table):\n", "-    \"\"\"\n", "-    This function returns the most suitable action value from the qtable.\n", "-    \"\"\"\n", "-    action = np.argmax(Q_table[state])\n", "-    if action is None:\n", "-        action = randomAction()\n", "-    return action\n", "-\n", "-\n", "-def randomAction():\n", "-    \"\"\"\n", "-    This function returns random action just in case the Qtable values return None.\n", "-    \"\"\"\n", "-    action = np.random.choice([0, 1, 2, 3])\n", "-    return action\n", "+def next_best_action(state: int, Q_table: np.ndarray) -> int:\n", "+    \"\"\"\n", "+    Return the most suitable action value from Q_table or a random action\n", "+    if there is no np.argmax(Q_table[state]).\n", "+    \n", "+    >>> next_best_action(1, []) in [0, 1, 2, 3]\n", "+    True\n", "+    \"\"\"\n", "+    action = np.argmax(Q_table[state])\n", "+    return action if action is not None else np.random.choice([0, 1, 2, 3])\n", "\n", "\n", "def StateActionReward(player, x_food, y_food):\n", "    \"\"\"\n", "    This function returns state, action and reward to update the Qtable.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/python-poetry/poetry/pull/5850#discussion_r936888423\n", "                 MAIN COMMIT: https://github.com/python-poetry/poetry/commit/9ee7f4056c2741b624f190ceb423373307bb90a0\n", "               AUTHOR COMMIT: https://github.com/spoorn/poetry/commit/9ee7f4056c2741b624f190ceb423373307bb90a0\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: `os.symlink` requires either administrative privileges or developer mode on Win10. We have a workaround to avoid symlinks in https://github.com/python-poetry/poetry/blob/83e6c318f59a77e83553c9805562490a4e5f8811/tests/helpers.py#L75-L92\n", "\n", "However, I don't think it makes sense to use this function here. Thus, maybe it's best to \"skip\" the test for windows if `os.symlink` fails.\n", "\n", "            dir=d4\n", "        ) as source_file, tempfile.NamedTemporaryFile(\n", "            dir=d3\n", "        ) as lock_file:\n", "            lock_file.close()\n", "-            os.symlink(Path(d3), symlink_path)\n", "+            try:\n", "+                os.symlink(Path(d3), symlink_path)\n", "+            except OSError:\n", "+                pass\n", "+                if sys.platform == \"win32\":\n", "+                    # os.symlink requires either administrative privileges or developer\n", "+                    # mode on Win10, throwing an OSError if neither is active.\n", "+                    # Test is not possible in that case.\n", "+                    return\n", "+                raise\n", "            locker = Locker(str(symlink_path) + os.sep + Path(lock_file.name).name, {})\n", "\n", "            package_local = Package(\n", "                \"local-package\",\n", "                \"1.2.3\",\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/optuna/optuna/pull/1202#discussion_r425468128\n", "                 MAIN COMMIT: https://github.com/optuna/optuna/commit/aab96e6297e3a28743f43dd3fe164e6a374bb4ac\n", "               AUTHOR COMMIT: https://github.com/sskarkhanis/optuna/commit/aab96e6297e3a28743f43dd3fe164e6a374bb4ac\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Some examples show the best trial, but I think it's kind for users to show the formatted output because this example has many hyperparameters.\n", "\n", "Example output:\n", "```console\n", "FrozenTrial(number=7, value=0.991965, datetime_start=datetime.datetime(2020, 5, 15, 7, 17, 45, 351583), datetime_complete=datetime.datetime(2020, 5, 15, 7, 17, 45, 657804), params={'booster': 'gbtree', 'alpha': 0.005432114697045233, 'max_depth': 6, 'min_child_weight': 4, 'eta': 0.25584322384906694, 'gamma': 0.01993575229218288, 'subsample': 0.7217599909772354, 'colsample_bytree': 0.8655936942560539, 'grow_policy': 'lossguide'}, distributions={'booster': CategoricalDistribution(choices=('gbtree',)), 'alpha': LogUniformDistribution(high=1.0, low=0.001), 'max_depth': IntUniformDistribution(high=9, low=1, step=1), 'min_child_weight': IntUniformDistribution(high=9, low=1, step=1), 'eta': LogUniformDistribution(high=1.0, low=0.001), 'gamma': LogUniformDistribution(high=1.0, low=0.001), 'subsample': LogUniformDistribution(high=1.0, low=0.6), 'colsample_bytree': LogUniformDistribution(high=1.0, low=0.6), 'grow_policy': CategoricalDistribution(choices=('depthwise', 'lossguide'))}, user_attrs={'n_estimators': 57}, system_attrs={}, intermediate_values={}, trial_id=7, state=TrialState.COMPLETE)\n", "```\n", "\n", "I suggest using a format similar to [examples/pruning/xgboost_cv_integration.py](https://github.com/optuna/optuna/blob/master/examples/pruning/xgboost_cv_integration.py).\n", "\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    study = optuna.create_study(direction=\"maximize\", sampler=sampler)\n", "    study.optimize(objective, n_trials=10)\n", "-    print()\n", "-    print(study.best_trial)\n", "+    print(\"Best trial:\")\n", "+    trial = study.best_trial\n", "+\n", "+    print(\"  Value: {}\".format(trial.value))\n", "+\n", "+    print(\"  Params: \")\n", "+    for key, value in trial.params.items():\n", "+        print(\"    {}: {}\".format(key, value))\n", "+\n", "+    print(\"  Number of estimators: {}\".format(trial.user_attrs[\"n_estimators\"]))\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/Netflix/dispatch/pull/2767#discussion_r1056634368\n", "                 MAIN COMMIT: https://github.com/Netflix/dispatch/commit/968dca66e8b593e106d2428d94460fc3295abcfb\n", "               AUTHOR COMMIT: https://github.com/kevgliss/dispatch/commit/968dca66e8b593e106d2428d94460fc3295abcfb\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Raises -> \n", "\n", "```bash\n", "ERROR:Error: create_instance() missing 1 required keyword-only argument: 'workflow':/Users/<USER>/Projects/dispatch/src/dispatch/plugins/dispatch_slack/bolt.py:app_error_handler:40\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/.pyenv/versions/3.9.7/envs/dispatch/lib/python3.9/site-packages/slack_bolt/listener/asyncio_runner.py\", line 65, in run\n", "    returned_value = await listener.run_ack_function(request=request, response=response)\n", "  File \"/Users/<USER>/.pyenv/versions/3.9.7/envs/dispatch/lib/python3.9/site-packages/slack_bolt/listener/async_listener.py\", line 119, in run_ack_function\n", "    return await self.ack_function(\n", "  File \"/Users/<USER>/Projects/dispatch/src/dispatch/plugins/dispatch_slack/workflow.py\", line 200, in handle_workflow_submission_event\n", "    instance = workflow_service.create_instance(\n", "TypeError: create_instance() missing 1 required keyword-only argument: 'workflow'\n", "```\n", "\n", "This seems like it's broken in main/pre-bolt as well:\n", "\n", "https://github.com/Netflix/dispatch/blob/0b75d5d00394f79bde54ab5c0c631cf87b975a29/src/dispatch/plugins/dispatch_slack/modals/workflow/handlers.py#L179-L188\n", "\n", "https://github.com/Netflix/dispatch/blob/0b75d5d00394f79bde54ab5c0c631cf87b975a29/tests/workflow/test_workflow_service.py#L57-L68\n", "\n", "            named_params.append({\"key\": key, \"value\": value})\n", "\n", "    creator = participant_service.get_by_incident_id_and_email(\n", "        db_session=db_session, incident_id=incident.id, email=user.email\n", "    )\n", "-    instance = workflow_service.create_instance(\n", "-        db_session=db_session,\n", "-        instance_in=WorkflowInstanceCreate(\n", "-            workflow=workflow,\n", "-            incident=incident,\n", "-            creator=creator,\n", "-            run_reason=form_data[RunWorkflowBlockIds.reason_input],\n", "-            parameters=named_params,\n", "-        ),\n", "-    )\n", "+    instance = workflow_service.create_instance(\n", "+        db_session=db_session,\n", "+        workflow=workflow,\n", "+        instance_in=WorkflowInstanceCreate(\n", "+            incident=incident,\n", "+            creator=creator,\n", "+            run_reason=form_data[RunWorkflowBlockIds.reason_input],\n", "+            parameters=named_params,\n", "+        ),\n", "+    )\n", "\n", "    for p in instance.parameters:\n", "        if p[\"value\"]:\n", "            params.update({p[\"key\"]: p[\"value\"]})\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/scikit-hep/awkward/pull/2258#discussion_r1114493336\n", "                 MAIN COMMIT: https://github.com/scikit-hep/awkward/commit/8ad6c6471bf49629ec74797648ffa7f3d34c3c92\n", "               AUTHOR COMMIT: https://github.com/ianna/awkward/commit/8ad6c6471bf49629ec74797648ffa7f3d34c3c92\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: because `highlevel` and `behavior` are generally last. Here's `ak.zip` as an example:\n", "\n", "https://github.com/scikit-hep/awkward/blob/3535fbb8b4bc3443c09ba595620610fee8be0b74/src/awkward/operations/ak_zip.py#L9-L19\n", "\n", "import awkward as ak\n", "from awkward._nplikes.numpylike import NumpyMetadata\n", "\n", "np = NumpyMetadata.instance()\n", "\n", "-def from_rdataframe(\n", "-    rdf,\n", "-    columns,\n", "-    *,\n", "-    keep_order=False,\n", "-    offsets_type=\"int64\",\n", "-    highlevel=True,\n", "-    behavior=None,\n", "-    with_name=None,\n", "-):\n", "+def from_rdataframe(\n", "+    rdf,\n", "+    columns,\n", "+    *,\n", "+    keep_order=False,\n", "+    offsets_type=\"int64\",\n", "+    with_name=None,\n", "+    highlevel=True,\n", "+    behavior=None,\n", "+):\n", "    \"\"\"\n", "    Args:\n", "        rdf (`ROOT.RDataFrame`): ROOT RDataFrame to convert into an\n", "            Awkward <PERSON>.\n", "        columns (str or iterable of str): A column or multiple columns to be\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/googleapis/python-aiplatform/pull/237#discussion_r579544210\n", "                 MAIN COMMIT: https://github.com/googleapis/python-aiplatform/commit/68d7c19812ed045e68dc6bcab87d9eac3c9ad0f9\n", "               AUTHOR COMMIT: https://github.com/ghost/python-aiplatform/commit/68d7c19812ed045e68dc6bcab87d9eac3c9ad0f9\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: See [here](https://github.com/googleapis/python-aiplatform/blob/e9d008daa9dff34769f5a232c7d215f1d2ea1ef4/google/cloud/aiplatform/jobs.py#L406-L416), the `bq://` prefix is new to BQ users so we allow them to pass their URI with or without the prefix.\n", "\n", "            \"pastHorizon\": past_horizon,\n", "            \"quantiles\": quantiles,\n", "            \"validationOptions\": validation_options,\n", "            \"optimizationObjective\": self._optimization_objective,\n", "        }\n", "-\n", "-        if export_evaluated_data_items:\n", "-            training_task_inputs_dict[\"exportEvaluatedDataItemsConfig\"] = {\n", "-                \"destinationBigqueryUri\": export_evaluated_data_items_bigquery_destination_uri,\n", "-                \"overrideExistingTable\": export_evaluated_data_items_override_destination,\n", "-            }\n", "+\n", "+        final_export_eval_bq_uri = export_evaluated_data_items_bigquery_destination_uri\n", "+        if final_export_eval_bq_uri and not final_export_eval_bq_uri.startswith(\"bq://\"):\n", "+            final_export_eval_bq_uri = f\"bq://{final_export_eval_bq_uri}\"\n", "+\n", "+        if export_evaluated_data_items:\n", "+            training_task_inputs_dict[\"exportEvaluatedDataItemsConfig\"] = {\n", "+                \"destinationBigqueryUri\": final_export_eval_bq_uri,\n", "+                \"overrideExistingTable\": export_evaluated_data_items_override_destination,\n", "+            }\n", "\n", "        if model_display_name is None:\n", "            model_display_name = self._display_name\n", "\n", "        model = gca_model.Model(display_name=model_display_name)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/digitalfabrik/integreat-cms/pull/1796#discussion_r1008103917\n", "                 MAIN COMMIT: https://github.com/digitalfabrik/integreat-cms/commit/3578fbeb66a6382cd9fdd43278fce3fcbc0b1846\n", "               AUTHOR COMMIT: https://github.com/charludo/integreat-cms/commit/3578fbeb66a6382cd9fdd43278fce3fcbc0b1846\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: 1. I wouldn't hardcode \"integreat.app\" and \"integreat-app.de\" here since we use the same code for whitelabeled versions like the malte app and the aschaffenburg app. Instead, we can rely on the existing settings for these domains. Also, this requires to move either this setting down or the `BASE_URL` up so it can be used here.\n", "2. To stay consistent with our other settings which contain lists (e.g. [`ALLOWED_HOSTS`](https://github.com/digitalfabrik/integreat-cms/blob/c87c51f80943dfc58cc249f698d85dfae1a1ad5b/integreat_cms/core/settings.py#L336-L346) or [`LANGUAGES`](https://github.com/digitalfabrik/integreat-cms/blob/c87c51f80943dfc58cc249f698d85dfae1a1ad5b/integreat_cms/core/settings.py#L649-L662)), I would split by newline characters instead of spaces, even if this makes the code a bit less readable. This makes it possible to write e.g.\n", "    ```\n", "    INTERNAL_URLS =\n", "\t    internal.host.org\n", "\t    my.app.de\n", "    ```\n", "into the config file. Speaking of which, could you add an example of this to [example-configs/integreat-cms.ini](https://github.com/digitalfabrik/integreat-cms/blob/develop/example-configs/integreat-cms.ini)?\n", "\n", "\n", "#: The default timeout in seconds for retrieving external APIs etc.\n", "DEFAULT_REQUEST_TIMEOUT = int(\n", "    os.environ.get(\"INTEGREAT_CMS_DEFAULT_REQUEST_TIMEOUT\", 10)\n", ")\n", "-#: The URLs which are treated as internal in TinyMCE custom link plugin\n", "-CUSTOM_INTERNAL_URLS = os.environ.get(\"INTEGREAT_INTERNAL_URLS\", \"\").split()\n", "-INTERNAL_URLS = \" \".join(\n", "-    url for url in [\"integreat.app\", \".integreat-app.de\"] + CUSTOM_INTERNAL_URLS if url\n", "-)\n", "+#: The URLs which are treated as internal in TinyMCE custom link plugin\n", "+INTERNAL_URLS = [BASE_URL, WEBAPP_URL, WEBSITE_URL] + list(\n", "+    filter(\n", "+        None,\n", "+        (\n", "+            x.strip()\n", "+            for x in os.environ.get(\"INTEGREAT_CMS_INTERNAL_URLS\", \"\").splitlines()\n", "+        ),\n", "+    )\n", "+)\n", "\n", "###############################\n", "# Firebase Push Notifications #\n", "###############################\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/grommet/grommet/pull/5275#discussion_r699655841\n", "                 MAIN COMMIT: https://github.com/grommet/grommet/commit/21f5c6930260a216f06eafae904795f6d2d0330c\n", "               AUTHOR COMMIT: https://github.com/g4rry420/grommet/commit/21f5c6930260a216f06eafae904795f6d2d0330c\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I believe `colorPropType` already covers the string and shape scenarios as it is [defined here](https://github.com/grommet/grommet/blob/e7d38edd69b344f021d80941453946b482df7a43/src/js/utils/general-prop-types.js#L5), so we can reduce this down to:\n", "\n", "\n", "let PropType = {};\n", "if (process.env.NODE_ENV !== 'production') {\n", "  PropType = {\n", "    a11yTitle: PropTypes.string,\n", "-    color: PropTypes.oneOfType([\n", "-      PropTypes.oneOfType([\n", "-        colorPropType,\n", "-        PropTypes.shape({\n", "-          dark: colorPropType.isRequired,\n", "-          light: colorPropType.isRequired,\n", "-        }),\n", "-      ]),\n", "-      PropTypes.arrayOf(\n", "-        PropTypes.shape({\n", "-          color: PropTypes.oneOfType([\n", "-            colorPropType.isRequired,\n", "-            PropTypes.shape({\n", "-              dark: colorPropType.isRequired,\n", "-              light: colorPropType.isRequired,\n", "-            }),\n", "-          ]),\n", "-          value: PropTypes.number.isRequired,\n", "-          opacity: PropTypes.number,\n", "-        }),\n", "-      ),\n", "-    ]),\n", "+    color: PropTypes.oneOfType([\n", "+      colorPropType,\n", "+      PropTypes.arrayOf(\n", "+        PropTypes.shape({\n", "+          color: colorPropType.isRequired,\n", "+          value: PropTypes.number.isRequired,\n", "+          opacity: PropTypes.number,\n", "+        }),\n", "+      ),\n", "+    ]),\n", "    id: PropTypes.string,\n", "    min: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n", "    max: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n", "    name: PropTypes.string,\n", "    onChange: PropTypes.func,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/google/site-kit-wp/pull/2571#discussion_r551447263\n", "                 MAIN COMMIT: https://github.com/google/site-kit-wp/commit/7ff27dbb4c9cd5add63bc2b4ae9b5aafd3ab8052\n", "               AUTHOR COMMIT: https://github.com/benbowler/site-kit-wp/commit/7ff27dbb4c9cd5add63bc2b4ae9b5aafd3ab8052\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This line is a bit long and would be better spread across a few. I thought we were spreading the date range dates into the args but it looks like we aren't doing that anymore.\n", "Let's do something like this https://github.com/google/site-kit-wp/blob/8dc8de99f9225f62972c2bb82f67f6e3d39651ad/assets/js/modules/adsense/components/dashboard/AdSensePerformanceWidget.js#L42-L47\n", "\n", "The args to `getDateRangeDates` should also be on separate lines though.\n", "\n", "A good rule of thumb here is 1-2 keys destructured or shorthand key/value is okay. More than that destructuring, or when using key/values without the shorthand should probably be on separate lines.\n", "\n", "\t\tconst store = select( STORE_NAME );\n", "\n", "\t\tconst accountID = store.getAccountID();\n", "\t\tconst profileID = store.getProfileID();\n", "\t\tconst internalWebPropertyID = store.getInternalWebPropertyID();\n", "-\t\tconst { compareStartDate, compareEndDate, startDate, endDate } = select( CORE_USER ).getDateRangeDates( { offsetDays: DATE_RANGE_OFFSET, compare: true, weekdayAlign: true } );\n", "+\t\tconst {\n", "+\t\t\tcompareStartDate,\n", "+\t\t\tcompareEndDate,\n", "+\t\t\tstartDate,\n", "+\t\t\tendDate\n", "+\t\t} = select( CORE_USER ).getDateRangeDates( {\n", "+\t\t\toffsetDays: DATE_RANGE_OFFSET,\n", "+\t\t\tcompare: true,\n", "+\t\t\tweekdayAlign: true,\n", "+\t\t} );\n", "\n", "\t\tconst args = {\n", "\t\t\tcompareStartDate,\n", "\t\t\tcompareEndDate,\n", "\t\t\tstartDate,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/interledger/rafiki/pull/178#discussion_r745036171\n", "                 MAIN COMMIT: https://github.com/interledger/rafiki/commit/7e23b57e69bcb14aa1202fd1d5c100452f20f1c2\n", "               AUTHOR COMMIT: https://github.com/sentientwaffle/rafiki/commit/7e23b57e69bcb14aa1202fd1d5c100452f20f1c2\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I tried this, and it looks like the invoice account isn't being deleted.\n", "Adding `.onDelete('CASCADE')` [here](https://github.com/interledger/rafiki/blob/main/packages/backend/migrations/20211022210203_create_invoices_table.js#L5) didn't fix it for me.\n", "\n", "      if (!invoiceAfter) throw new Error('invoice was deleted')\n", "      expect(invoiceAfter.active).toBe(false)\n", "    })\n", "\n", "    test('Deletes an expired invoice with no money', async (): Promise<void> => {\n", "-      const invoiceId = (\n", "-        await invoiceService.create({\n", "-          paymentPointerId,\n", "-          description: 'Test invoice',\n", "-          expiresAt: new Date(Date.now() - 40_000)\n", "-        })\n", "-      ).id\n", "-      await expect(invoiceService.deactivateNext()).resolves.toBe(invoiceId)\n", "-      expect(await invoiceService.get(invoiceId)).toBeUndefined()\n", "+      const { id: invoiceId, accountId } =\n", "+        await invoiceService.create({\n", "+          paymentPointerId,\n", "+          description: 'Test invoice',\n", "+          expiresAt: new Date(Date.now() - 40_000)\n", "+        })\n", "+      await expect(invoiceService.deactivateNext()).resolves.toBe(invoiceId)\n", "+      expect(await invoiceService.get(invoiceId)).toBeUndefined()\n", "+      const accountService = await deps.use('accountService')\n", "+      expect(await accountService.get(accountId)).toBeUndefined()\n", "    })\n", "  })\n", "\n", "  describe('Invoice pagination', (): void => {\n", "    let invoicesCreated: Invoice[]\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/coveo/ui-kit/pull/1584#discussion_r768779724\n", "                 MAIN COMMIT: https://github.com/coveo/ui-kit/commit/6be57da773a29733aed3d2c1823997a0e613654d\n", "               AUTHOR COMMIT: https://github.com/aelfaleh/ui-kit/commit/6be57da773a29733aed3d2c1823997a0e613654d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: We have [this expectation](https://github.com/coveo/ui-kit/blob/master/packages/quantic/cypress/integration/common-expectations.ts) that could be reused here.\n", "\n", "Your code would look like this:\n", "\n", "        .then((_target) => expect(_target).to.equal(target))\n", "        .logDetail(\n", "          `The result link should contain attribute target equal to \"${target}\"`\n", "        );\n", "    },\n", "-    logDocumentOpen: (resultTitle: string) => {\n", "-      cy.wait(InterceptAliases.UA.DocumentOpen)\n", "-        .then((interception) => {\n", "-          const analyticsBody = interception.request.body;\n", "-          expect(analyticsBody).to.have.property('actionCause', 'documentOpen');\n", "-          expect(analyticsBody).to.have.property('documentTitle', resultTitle);\n", "-          expect(analyticsBody).to.have.property('documentUri', urlResult);\n", "-        })\n", "-        .logDetail('should log the \"documentOpen\" UA event');\n", "-    },\n", "+    logDocumentOpen: (resultTitle: string) =>\n", "+      logUaEvent(\n", "+        InterceptAliases.UA.DocumentOpen,\n", "+        'documentOpen',\n", "+        {\n", "+          actionCause: 'documentOpen',\n", "+          documentTitle: resultTitle,\n", "+          documentUri, urlResult\n", "+        }\n", "+      ),\n", "  };\n", "}\n", "\n", "export const RecentResultsListExpectations = {\n", "  ...recentResultsListExpectations(RecentResultsListSelectors),\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/common-workflow-language/cwltool/pull/996#discussion_r590414358\n", "                 MAIN COMMIT: https://github.com/common-workflow-language/cwltool/commit/e07f96c183a53b9d053735337b614886369cc6aa\n", "               AUTHOR COMMIT: https://github.com/micha<PERSON>-kotliar/cwltool/commit/e07f96c183a53b9d053735337b614886369cc6aa\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This came up in the conformance tests: https://github.com/common-workflow-language/cwltool/runs/2066391703?check_suite_focus=true#step:5:939\n", "\n", "    # we're executing, so it's not safe to use a for loop here.\n", "    while processes_to_kill:\n", "        process = processes_to_kill.popleft()\n", "        cidfile = [str(arg).split(\"=\")[1] for arg in process.args if \"--cidfile\" in str(arg)]\n", "        if cidfile:\n", "-            with open(cidfile[0], \"r\") as inp_stream:\n", "-                p = subprocess.Popen([\"docker\", \"kill\", inp_stream.read()], shell=False)\n", "-                try:\n", "-                    p.wait(timeout=10)\n", "-                except subprocess.TimeoutExpired:\n", "-                    p.kill()\n", "+            try:\n", "+                with open(cidfile[0], \"r\") as inp_stream:\n", "+                    p = subprocess.Popen([\"docker\", \"kill\", inp_stream.read()], shell=False)\n", "+                    try:\n", "+                        p.wait(timeout=10)\n", "+                    except subprocess.TimeoutExpired:\n", "+                        p.kill()\n", "+            except FileNotFoundError:\n", "+                pass\n", "        process.kill()\n", "\n", "\n", "def _signal_handler(signum: int, _: Any) -> None:\n", "    \"\"\"Kill all spawned processes and exit.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/stylelint/stylelint/pull/6357#discussion_r978215954\n", "                 MAIN COMMIT: https://github.com/stylelint/stylelint/commit/166ff0306f4c935c88fb7b3d64a9976902827d6e\n", "               AUTHOR COMMIT: https://github.com/kaorun343/stylelint/commit/166ff0306f4c935c88fb7b3d64a9976902827d6e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: [suggest]\n", "\n", "- Let's use `\"` instead of `'` for consistency.\n", "  - I notice `'.stylelintcache'` should be `\".stylelintcache\"`. Can you also fix it in this PR?\n", "  - https://github.com/stylelint/stylelint/blob/005f39eea531283608afe5222e05a30c7d984d35/lib/cli.js#L166\n", "- Tabs are unintentionally mixed. Let's replace them with whitespaces.\n", "- Let's remove trailing whitespaces.\n", "- Typo: `change` -> `changes`\n", "\n", "        file will be created inside the specified folder, with a name derived\n", "        from a hash of the current working directory.\n", "\n", "        If the directory for the cache does not exist, make sure you add a trailing \"/\"\n", "        on *nix systems or \"\\\\\" on Windows. Otherwise the path will be assumed to be a file.\n", "-      --cache-strategy              [default: 'metadata']\n", "-\n", "-\t    Strategy for the cache to use for detecting changed files. Can be either\n", "-\t    \"metadata\" or \"content\".\n", "-\n", "-        The \"content\" strategy can be useful in cases where the modification time of\n", "-        your files change even if their contents have not. For example, this can happen\n", "-        during git operations like \"git clone\" because git does not track file modification\n", "-        time.  \n", "+      --cache-strategy              [default: \"metadata\"]\n", "+\n", "+        Strategy for the cache to use for detecting changed files. Can be either\n", "+        \"metadata\" or \"content\".\n", "+\n", "+        The \"content\" strategy can be useful in cases where the modification time of\n", "+        your files changes even if their contents have not. For example, this can happen\n", "+        during git operations like \"git clone\" because git does not track file modification\n", "+        time.\n", "\n", "      --formatter, -f               [default: \"string\"]\n", "\n", "        The output formatter: ${getFormatterOptionsText({ useOr: true })}.\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/lacework/go-sdk/pull/354#discussion_r603557439\n", "                 MAIN COMMIT: https://github.com/lacework/go-sdk/commit/8bf929dfc6e3d1a834689fc12a88ea7382dcf85d\n", "               AUTHOR COMMIT: https://github.com/hazedav/go-sdk/commit/8bf929dfc6e3d1a834689fc12a88ea7382dcf85d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: We try to keep these flag states inside a single struct per command, just like this https://github.com/lacework/go-sdk/blob/main/cli/cmd/vulnerability.go#L35\n", "\n", "\n", "\t\"github.com/lacework/go-sdk/api\"\n", ")\n", "\n", "var (\n", "-\tlqlEnd      string\n", "-\tlqlEnv      bool\n", "-\tlqlFile     string\n", "-\tlqlRepo     bool\n", "-\tlqlStart    string\n", "-\tlqlURL      string\n", "-\tlqlValidate bool\n", "+\tlqlCmdState = struct {\n", "+\t\tEnd      string\n", "+\t\tEnv      bool\n", "+\t\tFile     string\n", "+\t\t<PERSON><PERSON>     bool\n", "+\t\tStart    string\n", "+\t\tURL      string\n", "+\t\tValidate bool\n", "+\t}{}\n", "\n", "\t// lqlCmd represents the lql parent command\n", "\tlqlCmd = &cobra.Command{\n", "\t\tAliases: []string{\"lql\"},\n", "\t\tUse:     \"query\",\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/feast-dev/feast/pull/1483#discussion_r634257366\n", "                 MAIN COMMIT: https://github.com/feast-dev/feast/commit/6a99cd9e07a98559f7db2d5cddaf64994c480cb4\n", "               AUTHOR COMMIT: https://github.com/leonid133/feast/commit/6a99cd9e07a98559f7db2d5cddaf64994c480cb4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: perhaps adapt this pattern https://github.com/feast-dev/feast/blob/73ac3de613a729da41b7c832bb4a520132399339/sdk/python/feast/registry.py#L433-L440\n", "\n", "class S3RegistryStore(RegistryStore):\n", "    def __init__(self, uri: str):\n", "        self._uri = urlparse(uri)\n", "        self._bucket = self._uri.hostname\n", "        self._key = self._uri.path.lstrip(\"/\")\n", "-        return\n", "+        try:\n", "+            import boto3\n", "+            import botocore\n", "+        except ImportError as e:\n", "+            from feast.errors import FeastExtrasDependencyImportError\n", "+            raise FeastExtrasDependencyImportError(\"aws\", str(e))\n", "+        self.s3 = boto3.resource(\"s3\", endpoint_url=os.environ.get(\"FEAST_S3_ENDPOINT_URL\"))\n", "+        return\n", "\n", "    def get_registry_proto(self):\n", "        import boto3\n", "        import botocore\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/Instagram/LibCST/pull/426#discussion_r534623257\n", "                 MAIN COMMIT: https://github.com/Instagram/LibCST/commit/5ad75fe6671ce263cf3095999969281e8b15ed2e\n", "               AUTHOR COMMIT: https://github.com/Kronuz/LibCST/commit/5ad75fe6671ce263cf3095999969281e8b15ed2e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: We should also verify the operator is `+=` to be specific.\n", "There is a type error that can be fixed by the suggested change.\n", "https://app.circleci.com/pipelines/github/Instagram/LibCST/1469/workflows/8c3485d6-1123-436b-b0a2-f75110eda61b/jobs/9097\n", "\n", "        value = node.value\n", "        if value:\n", "            if self._handle_assign_target(node.target, value):\n", "                return True\n", "        return False\n", "-    def visit_AugAssign(self, node: cst.AugAssign) -> bool:\n", "-        target_name = get_full_name_for_node(node.target)\n", "-        if target_name == \"__all__\":\n", "-            if isinstance(node.value, (cst.<PERSON>, cst.<PERSON>, cst.Set)):\n", "-                self._is_assigned_export.add(node.value)\n", "-                return True\n", "-        return False\n", "+    def visit_AugAssign(self, node: cst.AugAssign) -> bool:\n", "+        target_name = get_full_name_for_node(node.target)\n", "+        if target_name == \"__all__\":\n", "+            value = node.value\n", "+            if isinstance(value, (cst.<PERSON>, cst.<PERSON>, cst.Set)):\n", "+                self._is_assigned_export.add(value)\n", "+                return True\n", "+        return False\n", "\n", "    def visit_Assign(self, node: cst.Assign) -> bool:\n", "        for target_node in node.targets:\n", "            if self._handle_assign_target(target_node.target, node.value):\n", "                return True\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/scikit-hep/awkward/pull/1677#discussion_r1002074283\n", "                 MAIN COMMIT: https://github.com/scikit-hep/awkward/commit/d4a69c4f72b3238244a1e395c40f4cad71f9237d\n", "               AUTHOR COMMIT: https://github.com/ianna/awkward/commit/d4a69c4f72b3238244a1e395c40f4cad71f9237d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Even though the Python API function ensures that the `char*` string ends with a null terminator, the string might still have null bytes within the string. For instance,\n", "\n", "```python\n", ">>> b\"arrow \\xe2\\x86\\x92 zero \\x00 not the end!\".decode(\"utf-8\")\n", "'arrow → zero \\x00 not the end!'\n", ">>> b\"arrow \\xe2\\x86\\x92 zero \\x00 not the end!\".decode(\"utf-8\").encode(\"utf-8\")\n", "b'arrow \\xe2\\x86\\x92 zero \\x00 not the end!'\n", "```\n", "\n", "Unless we explicitly ask for a length, we wouldn't be able to tell that the above string isn't `\"arrow → zero \"`. The ArrayBuilder has a assume-null-terminator method for convenience, but also a `char*`-and-length method for correctness:\n", "\n", "https://github.com/scikit-hep/awkward/blob/38243b2f75fbf559bf31f97dfaeb02c428645c59/include/awkward/builder/ArrayBuilder.h#L304-L313\n", "\n", "To use it, we also have to ask Python for the length. ([PythonAPI.string_as_string_and_size](https://github.com/numba/numba/blob/f4b6fe0124fa7734ba2bb28c7685db5b6306c486/numba/core/pythonapi.py#L1079-L1103), which is [PyUnicode_AsUTF8AndSize](https://docs.python.org/3/c-api/unicode.html#c.PyUnicode_AsUTF8AndSize), and that size is the number of _bytes_, not the number of code-points, so that's good.)\n", "\n", "    arraybuilderval, xval = args\n", "    proxyin = context.make_helper(builder, arraybuildertype, arraybuilderval)\n", "\n", "    pyapi = context.get_python_api(builder)\n", "    gil = pyapi.gil_ensure()\n", "-    out = pyapi.string_as_string(xval.value)\n", "-    call(\n", "-        context,\n", "-        builder,\n", "-        ak._libawkward.ArrayBuilder_string,\n", "-        (proxyin.rawptr, out),\n", "-    )\n", "+    is_ok, out, length = pyapi.string_as_string_and_size(xval.value)\n", "+    length = ak._connect.numba.layout.castint(context, builder, numba.ssize_t, numba.int64, length)\n", "+    call(\n", "+        context,\n", "+        builder,\n", "+        ak._libawkward.ArrayBuilder_string_length,\n", "+        (proxyin.rawptr, out, length),\n", "+    )\n", "    pyapi.gil_release(gil)\n", "\n", "    return context.get_dummy_value()\n", "\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/GenericMappingTools/pygmt/pull/1726#discussion_r825304718\n", "                 MAIN COMMIT: https://github.com/GenericMappingTools/pygmt/commit/56dc7c6e93f17089c05e02375e70f9a957360b0a\n", "               AUTHOR COMMIT: https://github.com/willschlitzer/pygmt/commit/56dc7c6e93f17089c05e02375e70f9a957360b0a\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Ideally, we would have nice column names like in `grdhisteq`:\n", "\n", "https://github.com/GenericMappingTools/pygmt/blob/d85bfdfc212ae5acf36921acdfd92c139288f30a/pygmt/src/grdhisteq.py#L301-L309\n", "\n", "But for the sake of time, and just finish this inline example, let's go with this:\n", "\n", "    >>> # and below the surface; set the minimum contour z-value to 200, the\n", "    >>> # maximum to 400, and the interval to 50.\n", "    >>> output_array = pygmt.grdvolume(\n", "    ...     grid=grid, contour=[200, 400, 50], output_type=\"numpy\"\n", "    ... )\n", "-    \"\"\"\n", "+    >>> print(output_dataframe)\n", "+             0             1             2           3\n", "+        0  200  2.144285e+12  7.972228e+14  371.789489\n", "+        1  250  2.104042e+12  6.908183e+14  328.329232\n", "+        2  300  2.014978e+12  5.877195e+14  291.675420\n", "+        3  350  1.892109e+12  4.897545e+14  258.840510\n", "+        4  400  1.744792e+12  3.988316e+14  228.584026\n", "+        \"\"\"\n", "    if output_type not in [\"numpy\", \"pandas\", \"file\"]:\n", "        raise GMTInvalidInput(\n", "            \"\"\"Must specify format as either numpy, pandas, or file.\"\"\"\n", "        )\n", "    if output_type == \"file\" and outfile is None:\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/talmolab/sleap/pull/1007#discussion_r1016041809\n", "                 MAIN COMMIT: https://github.com/talmolab/sleap/commit/0aa4acac3548cd4a41890d97dd9fd1148b4f7e4d\n", "               AUTHOR COMMIT: https://github.com/hsingchien/sleap/commit/0aa4acac3548cd4a41890d97dd9fd1148b4f7e4d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Move the docstring to the top of the function. Also, it might be faster to return just an empty list than filter the suggestions ([we just append whichever list is returned to the suggestions list](https://github.com/talmolab/sleap/blob/develop/sleap/gui/commands.py#L2428)).\n", "\n", "        videos: List[Video],\n", "        frame_from: int,\n", "        frame_to: int,\n", "        **kwargs,\n", "    ):\n", "-        # check the validity of inputs, frame_from <= frame_to\n", "-        if frame_from > frame_to:\n", "-            return VideoFrameSuggestions.filter_unique_suggestions(labels, videos, [])\n", "-\n", "-        \"\"\"Add consecutive frame chunk to label suggestion\"\"\"\n", "-        proposed_suggestions = []\n", "+        \"\"\"Add consecutive frame chunk to label suggestion\"\"\"\n", "+        \n", "+        proposed_suggestions = []\n", "+        \n", "+        # Check the validity of inputs\n", "+        if frame_from > frame_to:\n", "+            return proposed_suggestions\n", "+        \n", "        for video in videos:\n", "            # make sure when targeting all videos the from and to do not exceed frame number\n", "            if frame_from > video.num_frames:\n", "                continue\n", "            this_video_frame_to = min(frame_to, video.num_frames)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/unitaryfund/mitiq/pull/836#discussion_r682954290\n", "                 MAIN COMMIT: https://github.com/unitaryfund/mitiq/commit/9bf9e7b68698c850c0f130801286aa8b0e282bc5\n", "               AUTHOR COMMIT: https://github.com/rmlarose/mitiq/commit/9bf9e7b68698c850c0f130801286aa8b0e282bc5\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Minor suggestion to match how docstrings look in [here](https://github.com/unitaryfund/mitiq/blob/master/mitiq/interface/mitiq_qiskit/conversions.py).\n", "\n", "\n", "from pennylane import from_qasm as pennylane_from_qasm\n", "\n", "\n", "def from_pennylane(tape: <PERSON>Tape) -> Circuit:\n", "-    \"\"\"Returns a Cirq circuit equivalent to the input QuantumTape.\n", "-\n", "-    Args:\n", "-        tape: <PERSON><PERSON> QuantumTape to convert to a Cirq circuit.\n", "-    \"\"\"\n", "+    \"\"\"Returns a Mitiq circuit equivalent to the input QuantumTape.\n", "+\n", "+    Args:\n", "+        tape: <PERSON><PERSON> QuantumTape to convert to a Mitiq circuit.\n", "+\n", "+    Returns:\n", "+        Mitiq circuit representation equivalent to the input QuantumTape.\n", "+    \"\"\"\n", "    return cirq_from_qasm(tape.to_openqasm(rotations=True))\n", "\n", "\n", "def to_pennylane(circuit: Circuit) -> QuantumTape:\n", "    \"\"\"Returns a QuantumTape equivalent to the input Cirq circuit.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/ansible-collections/amazon.aws/pull/634#discussion_r790532500\n", "                 MAIN COMMIT: https://github.com/ansible-collections/amazon.aws/commit/0f2e93ce2660d04c8e934289600c7568ae5a7fa5\n", "               AUTHOR COMMIT: https://github.com/sebastien-rosset/amazon.aws/commit/0f2e93ce2660d04c8e934289600c7568ae5a7fa5\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: `C(...)` is used for quoting values, `I(...)` is used for quoting the names of module options.\n", "\n", "The `>` YAML block indicators aren't really needed here.\n", "\n", "Even better would be to define the suboptions here (see for example [elb_classic_lb](https://github.com/ansible-collections/amazon.aws/blob/main/plugins/modules/elb_classic_lb.py#L34)) however you then need to also define that in the [arg_spec](https://github.com/ansible-collections/amazon.aws/blob/main/plugins/modules/elb_classic_lb.py#L2096)\n", "\n", "    description:\n", "    - The ID of the route table to update or delete.\n", "    - Required when I(lookup=id).\n", "    type: str\n", "  routes:\n", "-    description:\n", "-        - >\n", "-          List of routes in the route table.\n", "-        - >\n", "-          Routes are specified as dicts containing the keys 'dest' and one of 'gateway_id',\n", "-          'instance_id', 'network_interface_id', or 'vpc_peering_connection_id'.\n", "-        - >\n", "-          The value of 'dest' is used for the destination match. It may be a IPv4 CIDR block\n", "-          or a IPv6 CIDR block.\n", "-        - >\n", "-          If 'gateway_id' is specified, you can refer to the VPC's IGW by using the value 'igw'.\n", "-        - >\n", "-          Routes are required for present states.\n", "+    description:\n", "+        - List of routes in the route table.\n", "+        - Routes are specified as dicts containing the keys C(dest) and one of C(gateway_id),\n", "+          C(instance_id), C(network_interface_id), or C(vpc_peering_connection_id).\n", "+        - The value of C(dest) is used for the destination match. It may be a IPv4 CIDR block\n", "+          or a IPv6 CIDR block.\n", "+        - If I(gateway_id) is specified, you can refer to the VPC's IGW by using the value C(igw).\n", "+        - Routes are required for present states.\n", "    type: list\n", "    elements: dict\n", "  state:\n", "    description: Create or destroy the VPC route table.\n", "    default: present\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/proteneer/timemachine/pull/323#discussion_r562278745\n", "                 MAIN COMMIT: https://github.com/proteneer/timemachine/commit/caa7a76311c17dafe22113543886486d1b284a8a\n", "               AUTHOR COMMIT: https://github.com/proteneer/timemachine/commit/caa7a76311c17dafe22113543886486d1b284a8a\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The [examples in `tests/test_parallel.py`](https://github.com/proteneer/timemachine/blob/caa7a76311c17dafe22113543886486d1b284a8a/tests/test_parallel.py#L27-L34) look a bit more like this:\n", "\n", "            A deferred object with a .result() method.\n", "\n", "        Usage:\n", "\n", "        client = ConcreteClient()\n", "-        futures = []\n", "-        for arg in args:\n", "-            fut = client.submit(fn, *args)\n", "-            futures.append(fut)\n", "-\n", "-        for fut in futures:\n", "-            res = fut.result()\n", "+        futures = []\n", "+        for arg in args:\n", "+            fut = client.submit(task_fn, arg)\n", "+            futures.append(fut)\n", "+            \n", "+        res = []\n", "+        for fut in futures:\n", "+            res.append(fut.result())\n", "\n", "        \"\"\"\n", "        raise NotImplementedError()\n", "\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/jellyfin/jellyfin-web/pull/2946#discussion_r706608732\n", "                 MAIN COMMIT: https://github.com/jellyfin/jellyfin-web/commit/de29dac896762599bfd3360f5b44e4f1a455c12e\n", "               AUTHOR COMMIT: https://github.com/thornbill/jellyfin-web/commit/de29dac896762599bfd3360f5b44e4f1a455c12e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: We can probably take the values directly because they are updated automatically.\n", "\n", "The only thing that needs to be fixed (in other PR?) is that here should be an arrow function: https://github.com/jellyfin/jellyfin-web/blob/9778f2db657a3be6ec44173e618768873d186ff6/src/components/syncPlay/core/timeSync/TimeSyncCore.js#L51\n", "\n", "_timeSyncCore fix from the above is included_\n", "\n", "        });\n", "    }\n", "\n", "    async initEditor() {\n", "        const { context } = this;\n", "-        context.querySelector('#txtExtraTimeOffset').value = toFloat(getSetting('extraTimeOffset'),\n", "-            SyncPlay.Manager.playbackCore.extraTimeOffset);\n", "-        context.querySelector('#chkSyncCorrection').checked = toBoolean(getSetting('enableSyncCorrection'),\n", "-            SyncPlay.Manager.playbackCore.enableSyncCorrection);\n", "-        context.querySelector('#txtMinDelaySpeedToSync').value = toFloat(getSetting('minDelaySpeedToSync'),\n", "-            SyncPlay.Manager.playbackCore.minDelaySpeedToSync);\n", "-        context.querySelector('#txtMaxDelaySpeedToSync').value = toFloat(getSetting('maxDelaySpeedToSync'),\n", "-            SyncPlay.Manager.playbackCore.maxDelaySpeedToSync);\n", "-        context.querySelector('#txtSpeedToSyncDuration').value = toFloat(getSetting('speedToSyncDuration'),\n", "-            SyncPlay.Manager.playbackCore.speedToSyncDuration);\n", "-        context.querySelector('#txtMinDelaySkipToSync').value = toFloat(getSetting('minDelaySkipToSync'),\n", "-            SyncPlay.Manager.playbackCore.minDelaySkipToSync);\n", "-        context.querySelector('#chkSpeedToSync').checked = toBoolean(getSetting('useSpeedToSync'),\n", "-            SyncPlay.Manager.playbackCore.useSpeedToSync);\n", "-        context.querySelector('#chkSkipToSync').checked = toBoolean(getSetting('useSkipToSync'),\n", "-            SyncPlay.Manager.playbackCore.useSkipToSync);\n", "+        context.querySelector('#txtExtraTimeOffset').value = SyncPlay.Manager.timeSyncCore.extraTimeOffset;\n", "+        context.querySelector('#chkSyncCorrection').checked = SyncPlay.Manager.playbackCore.enableSyncCorrection;\n", "+        context.querySelector('#txtMinDelaySpeedToSync').value = SyncPlay.Manager.playbackCore.minDelaySpeedToSync;\n", "+        context.querySelector('#txtMaxDelaySpeedToSync').value = SyncPlay.Manager.playbackCore.maxDelaySpeedToSync;\n", "+        context.querySelector('#txtSpeedToSyncDuration').value = SyncPlay.Manager.playbackCore.speedToSyncDuration;\n", "+        context.querySelector('#txtMinDelaySkipToSync').value = SyncPlay.Manager.playbackCore.minDelaySkipToSync;\n", "+        context.querySelector('#chkSpeedToSync').checked = SyncPlay.Manager.playbackCore.useSpeedToSync;\n", "+        context.querySelector('#chkSkipToSync').checked = SyncPlay.Manager.playbackCore.useSkipToSync;\n", "    }\n", "\n", "    onSubmit() {\n", "        this.save();\n", "        dialogHelper.close(this.context);\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/apollographql/apollo-client/pull/7902#discussion_r610898973\n", "                 MAIN COMMIT: https://github.com/apollographql/apollo-client/commit/cdc2d5140a75fe4f27fa61d80ab32caa705a4a56\n", "               AUTHOR COMMIT: https://github.com/jcreighton/apollo-client/commit/cdc2d5140a75fe4f27fa61d80ab32caa705a4a56\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: @jcre<PERSON>on As I just recently discovered while working with the old `MutationUpdateFn` type, the `T` type parameter here should probably be called `TData`, which is worth fixing because then it's a little easier to see there was previously/always something slightly wrong with `ApolloCache<T>`. Specifically, the [`ApolloCache`](https://github.com/apollographql/apollo-client/blob/2e2a80a8cb0ef8bbca0eea8d310e869d1a54a5c5/src/cache/core/cache.ts#L14) class is supposed to take a type parameter called `TSerialized`, which describes what you get from `cache.extract()`, and has nothing to do with the shape of individual results (which is what `TData` describes).\n", "\n", "Alas, I think this means we need yet another type parameter here: `TCache extends ApolloCache<any>`\n", "\n", ") => Record<string, any>;\n", "\n", "export type MutationQueryReducersMap<T = { [key: string]: any }> = {\n", "  [queryName: string]: MutationQueryReducer<T>;\n", "};\n", "-export type MutationUpdaterFunction<T, TVariables, TContext> = (\n", "-  cache: ApolloCache<T>,\n", "-  result: Omit<FetchResult<T>, 'context'>,\n", "+export type MutationUpdaterFunction<\n", "+  <PERSON><PERSON>,\n", "+  TVariables,\n", "+  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "+  TCache extends ApolloCache<any>,\n", "+> = (\n", "+  cache: TCache,\n", "+  result: Omit<FetchResult<TData>, 'context'>,\n", "  options: {\n", "    context?: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    variables?: TVariables,\n", "  },\n", ") => void;\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/rrweb-io/rrweb/pull/1105#discussion_r1098221952\n", "                 MAIN COMMIT: https://github.com/rrweb-io/rrweb/commit/fc5b77a81db4b1be2b1e340f6f10ab2ddfe43ca4\n", "               AUTHOR COMMIT: https://github.com/jlalmes/rrweb/commit/fc5b77a81db4b1be2b1e340f6f10ab2ddfe43ca4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: There's a warning from rollup https://github.com/rrweb-io/rrweb/actions/runs/4091331306/jobs/7055353848#step:5:113\n", "<img width=\"821\" alt=\"image\" src=\"https://user-images.githubusercontent.com/27533910/217164608-f04db9b1-3a83-499e-963f-3024a3c0eeed.png\">\n", "Change the code here can resolve the warning.\n", "\n", "  observer.observe({ entryTypes: ['navigation', 'resource'] });\n", "  return () => {\n", "    observer.disconnect();\n", "  };\n", "}\n", "-const getRequestPerformanceEntry = async (\n", "-  win: <PERSON><PERSON><PERSON><PERSON>,\n", "-  initiatorType: string,\n", "-  url: string,\n", "-  after?: number,\n", "-  before?: number,\n", "-  attempt = 0,\n", "-): Promise<PerformanceResourceTiming> => {\n", "+async function getRequestPerformanceEntry(\n", "+  win: <PERSON><PERSON><PERSON><PERSON>,\n", "+  initiatorType: string,\n", "+  url: string,\n", "+  after?: number,\n", "+  before?: number,\n", "+  attempt = 0,\n", "+): Promise<PerformanceResourceTiming> {\n", "  if (attempt > 10) {\n", "    throw new Error('Cannot find performance entry');\n", "  }\n", "  const urlPerformanceEntries = win.performance.getEntriesByName(\n", "    url,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/pagopa/io-app/pull/3944#discussion_r868925648\n", "                 MAIN COMMIT: https://github.com/pagopa/io-app/commit/6e2f2a5608303b4bb54eeec59a3287c320114593\n", "               AUTHOR COMMIT: https://github.com/fabriziofff/io-app/commit/6e2f2a5608303b4bb54eeec59a3287c320114593\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Locally I solved this problem with this, adding `testID={\"WalletCardsTestID\"}` [here](https://github.com/pagopa/io-app/blob/361054dbd30325e1f1195eed68779ea633556653/ts/screens/wallet/WalletHomeScreen.tsx#L365). Might it work for you?\n", "\n", "        .withTimeout(e2eWaitRenderTimeout);\n", "      await cgnBonusItem.tap();\n", "      await activateBonusSuccess();\n", "    });\n", "  });\n", "-  /*\n", "-  TODO: we should deactivate atm this test because we cannot scroll in the WalletHome, since is used native-base and we cannot have a testID binded to the scrollview.\n", "-\n", "-  describe(\"When the user want to start activation from card carousel\", () => {\n", "-    it(\"Should complete activation\", async () => {\n", "-      await element(by.text(I18n.t(\"global.navigator.wallet\"))).tap();\n", "-      await element(by.id(\"FeaturedCardCGNTestID\")).tap();\n", "-      await activateBonusSuccess();\n", "-    });\n", "-  });\n", "-   */\n", "+   describe(\"When the user want to start activation from card carousel\", () => {\n", "+    it(\"Should complete activation\", async () => {\n", "+      await element(by.text(I18n.t(\"global.navigator.wallet\"))).tap();\n", "+      await element(by.id(\"WalletCardsTestID\")).swipe(\"up\");\n", "+      await element(by.id(\"FeaturedCardCGNTestID\")).tap();\n", "+      await activateBonusSuccess();\n", "+    });\n", "+  });\n", "\n", "  describe(\"When the user want to start activation from service detail\", () => {\n", "    it(\"Should complete activation\", async () => {\n", "      await element(by.text(I18n.t(\"global.navigator.services\"))).tap();\n", "      const cgnServiceItem = element(by.id(CGN_TITLE));\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/onflow/flow-go/pull/1185#discussion_r694278757\n", "                 MAIN COMMIT: https://github.com/onflow/flow-go/commit/cefbeeb1a50d277b3f7378065fef858f15d458bf\n", "               AUTHOR COMMIT: https://github.com/kc1116/flow-go/commit/cefbeeb1a50d277b3f7378065fef858f15d458bf\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Can save a bit of space with https://github.com/onflow/flow-go/blob/3d3a19b423f9420ed3651501f2f803a116561306/state/protocol/convert.go#L150\n", "\n", "\tcurrentEpochFinalView, err := snap.Epochs().Current().FinalView()\n", "\tif err != nil {\n", "\t\treturn fmt.<PERSON><PERSON><PERSON>(\"could not update current epoch final view\")\n", "\t}\n", "\tstate.metrics.CurrentEpochFinalView(currentEpochFinalView)\n", "-\t// update dkg phase 1 final view\n", "-\tdkgPhase1FinalView, err := snap.Epochs().Current().DKGPhase1FinalView()\n", "-\tif err != nil {\n", "-\t\treturn fmt.<PERSON><PERSON><PERSON>(\"could not update current dkg phase 1 final view\")\n", "-\t}\n", "-\tstate.metrics.CurrentDKGPhase1FinalView(dkgPhase1FinalView)\n", "-\n", "-\t// update dkg phase 2 final view\n", "-\tdkgPhase2FinalView, err := snap.Epochs().Current().DKGPhase2FinalView()\n", "-\tif err != nil {\n", "-\t\treturn fmt.<PERSON><PERSON><PERSON>(\"could not update current dkg phase 2 final view\")\n", "-\t}\n", "-\tstate.metrics.CurrentDKGPhase2FinalView(dkgPhase2FinalView)\n", "-\n", "-\t// update dkg phase 3 final view\n", "-\tdkgPhase3FinalView, err := snap.Epochs().Current().DKGPhase3FinalView()\n", "-\tif err != nil {\n", "-\t\treturn fmt.<PERSON><PERSON><PERSON>(\"could not update current dkg phase 3 final view\")\n", "-\t}\n", "-\tstate.metrics.CurrentDKGPhase3FinalView(dkgPhase3FinalView)\n", "+\tdkgPhase1<PERSON><PERSON><PERSON>ie<PERSON>, dkgPhase2F<PERSON><PERSON>iew, dkgPhase3FinalView, err := protocol.DKGPhaseViews(currEpoch)\n", "+\tif err != nil {\n", "+\t\treturn fmt.<PERSON><PERSON><PERSON>(\"could not get dkg phase final view: %w\", err)\n", "+\t}\n", "+\n", "+\tstate.metrics.CurrentDKGPhase1FinalView(dkgPhase1FinalView)\n", "+\tstate.metrics.CurrentDKGPhase2FinalView(dkgPhase2FinalView)\n", "+\tstate.metrics.CurrentDKGPhase3FinalView(dkgPhase3FinalView)\n", "\n", "\treturn nil\n", "}\n", "\n", "// updateCommittedEpochFinalView updates the `committed_epoch_final_view` metric\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/ktr0731/go-fuzzyfinder/pull/130#discussion_r753770493\n", "                 MAIN COMMIT: https://github.com/ktr0731/go-fuzzyfinder/commit/859be21936caf8d95c8ba3ea1d34d8aa44354a51\n", "               AUTHOR COMMIT: https://github.com/r-darwish/go-fuzzyfinder/commit/859be21936caf8d95c8ba3ea1d34d8aa44354a51\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I think we should avoid adding an option which enables a specific layout. Instead, it's more extensible to add an option that can specify a layout from a list of layouts. For example, [`WithMode`](https://pkg.go.dev/github.com/ktr0731/go-fuzzyfinder#WithMode) option can specify one of the provided modes.\n", "\n", "What do you think about the following design? `CursorPositionBottom` is the default position (the same as the current implementation).\n", "\n", "\t\to.hot<PERSON><PERSON>ad = true\n", "\t}\n", "}\n", "\n", "// WithBeginAtTop makes the selection begin at the top of the list instead of the bottom\n", "-func WithBeginAtTop() Option {\n", "+type cursorPosition int\n", "+\n", "+const (\n", "+  CursorPositionBottom cursorPosition = iota\n", "+  CursorPositionTop\n", "+)\n", "+\n", "+func WithCursorPosition(position CursorPosition) Option {\n", "\treturn func(o *opt) {\n", "\t\to.beginAtTop = true\n", "\t}\n", "}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/fastly/cli/pull/717#discussion_r1034594108\n", "                 MAIN COMMIT: https://github.com/fastly/cli/commit/4d39cc86125b6cc12aaede2d4b84aeae46b6ac1d\n", "               AUTHOR COMMIT: https://github.com/awilliams-fastly/cli/commit/4d39cc86125b6cc12aaede2d4b84aeae46b6ac1d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Can we update the format of the commands to the following, so we're consistent with the rest of the code base ([example](https://github.com/fastly/cli/blob/main/pkg/commands/acl/create.go#L16-L21)):\n", "\n", "\tmaxSecretLen = maxSecretKiB * 1024\n", ")\n", "\n", "// NewCreateSecretCommand returns a usable command registered under the parent.\n", "func NewCreateSecretCommand(parent cmd.Registerer, globals *config.Data, data manifest.Data) *CreateSecretCommand {\n", "-\tvar c CreateSecretCommand\n", "-\tc.Globals = globals\n", "-\tc.manifest = data\n", "-\tc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = parent.Command(\"create\", \"Create secret\")\n", "+\tc := CreateSecretCommand{\n", "+\t\tBase: cmd.Base{\n", "+\t\t\tGlobals: globals,\n", "+\t\t},\n", "+\t\tmanifest: data,\n", "+\t}\n", "+\t\n", "+\tc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = parent.Command(\"create\", \"Create secret\")\n", "\tc.<PERSON>lag(storeIDFlag(&c.Input.ID))\n", "\tc.<PERSON>(secretNameFlag(&c.Input.Name))\n", "\tc.RegisterFlag(secretFileFlag(&c.secretFile))\n", "\tc.<PERSON>FlagBool(secretStdinFlag(&c.secretSTDIN))\n", "\tc.<PERSON>(c.json<PERSON>lag())\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/openshift/cluster-logging-operator/pull/1544#discussion_r924777803\n", "                 MAIN COMMIT: https://github.com/openshift/cluster-logging-operator/commit/f3212b0d892061827833e168149d427dd402723b\n", "               AUTHOR COMMIT: https://github.com/alanconway/cluster-logging-operator/commit/f3212b0d892061827833e168149d427dd402723b\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I think this could be simplified by generating the hostname based on the LokiStack name and ClusterLogging namespace, similar to the code in [`forwarding.go`](https://github.com/openshift/cluster-logging-operator/blob/b09b736e639b24cf1e28b7391111726e7745e46c/internal/k8shandler/forwarding.go#L384):\n", "\n", "\t\treturn kverrors.Wrap(err, \"Failed to create or update ClusterRoleBinding for LokiStack collector.\")\n", "\t}\n", "\n", "\treturn nil\n", "}\n", "-func findDefaultLokiHost(outputs []logging.OutputSpec) (string, error) {\n", "-\tfor _, o := range outputs {\n", "-\t\tif o.Name == logging.OutputNameDefault {\n", "-\t\t\tif o.Type != logging.OutputTypeLoki {\n", "-\t\t\t\treturn \"\", fmt.<PERSON>(\"expected loki output, got: %v\", o.Type)\n", "-\t\t\t}\n", "-\t\t\tu, err := url.Parse(o.URL)\n", "-\t\t\tif err != nil {\n", "-\t\t\t\treturn \"\", err\n", "-\t\t\t}\n", "-\t\t\treturn u.Host, nil\n", "-\t\t}\n", "-\t}\n", "-\treturn \"\", errors.New(\"no default output\")\n", "-}\n", "+func findDefaultLokiHost(clusterLogging *logging.ClusterLogging) (string, error) {\n", "+\tlogStore := clusterLogging.Spec.LogStore\n", "+\tif logStore == nil || logStore.Type != logging.LogStoreTypeLokiStack {\n", "+\t\treturn \"\", fmt.<PERSON>rrorf(\"logstore does not use LokiStack: %v\", logStore)\n", "+\t}\n", "+\n", "+\treturn fmt.Sprintf(\"%s-query-frontend-http.%s.svc\", logStore.LokiStack.Name, clusterLogging.Namespace), nil\n", "+}\n", "\n", "func (clusterRequest *ClusterLoggingRequest) createOrUpdateLoggingConsolePlugin() error {\n", "\tcluster := clusterRequest.Cluster\n", "\tcreatedBy := fmt.Sprintf(\"%v.%v.%v\", cluster.Kind, cluster.Namespace, cluster.Name)\n", "\tlokiService, err := findDefaultLokiHost(clusterRequest.ForwarderRequest.Spec.Outputs)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/integr8ly/integreatly-operator/pull/717#discussion_r428638164\n", "                 MAIN COMMIT: https://github.com/integr8ly/integreatly-operator/commit/b55e69ec9db1b640215ef127542b563d279252e5\n", "               AUTHOR COMMIT: https://github.com/brian<PERSON>agher/integreatly-operator/commit/b55e69ec9db1b640215ef127542b563d279252e5\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This might give a `kubeconfig refined` error due to https://github.com/operator-framework/operator-sdk/issues/2167 :thinking: \n", "\n", "If you just need a new http client, perhaps you could use this function instead of creating a new testing context :thinking: :\n", "https://github.com/integr8ly/integreatly-operator/blob/955d4997f97944aed60aef1c6184c5e9e4e8f0ef/test/common/shared_functions.go#L170\n", "\n", "\n", "}\n", "\n", "// Login as a developer and create a separate testing context. This will provide a separate http client, mimicking\n", "// What would happen in reality, i.e. 2 users using separate browsers.\n", "-func loginTo3ScaleAsDevloper(t *testing.T, user string, host string) error {\n", "-\tconfig, err := runtimeConfig.GetConfig()\n", "-\tif err != nil {\n", "-\t\tt.<PERSON>(\"Failed to get runtime config: %v\", err)\n", "-\t}\n", "-\tctx, err := NewTestingContext(config)\n", "-\n", "-\terr = loginToThreeScale(t, host, user, DefaultPassword, \"testing-idp\", ctx.HttpClient)\n", "+func loginTo3ScaleAsDevloper(t *testing.T, user string, host string, ctx *TestingContext) error {\n", "+\thttpClient, err := NewTestingHTTPClient(ctx.KubeConfig)\n", "+\t\n", "+\tif err != nil {\n", "+\t  t.<PERSON>(err)\n", "+\t}\n", "+\n", "+\terr = loginToThreeScale(t, host, user, DefaultPassword, \"testing-idp\", httpClient)\n", "\tif err != nil {\n", "\t\tt.<PERSON>(\"Failed to log into 3Scale: %v\", err)\n", "\t}\n", "\treturn nil\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/GenericMappingTools/pygmt/pull/537#discussion_r457294024\n", "                 MAIN COMMIT: https://github.com/GenericMappingTools/pygmt/commit/bb65874e9462d99b2883332afd1c85678eee4428\n", "               AUTHOR COMMIT: https://github.com/seisman/pygmt/commit/bb65874e9462d99b2883332afd1c85678eee4428\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Try using [`# doctest +NORMALIZE_WHITESPACE`](https://docs.python.org/3.8/library/doctest.html#doctest.NORMALIZE_WHITESPACE). Just used it recently for the datetime doctest https://github.com/GenericMappingTools/pygmt/blob/9a4c0a0177dac03e05d219c01105f272c9911655/pygmt/clib/conversion.py#L287-L289\n", "\n", "    R = bla J = meh\n", "    >>> my_module(R='bla', projection='meh')\n", "    R = bla J = meh\n", "    >>> my_module(region='bla', projection='meh')\n", "    R = bla J = meh\n", "-    >>> my_module(region='bla', projection='meh', J=\"bla\")\n", "-    <PERSON><PERSON> (most recent call last):\n", "-      ...\n", "-    pygmt.exceptions.GMTInvalidInput: Arguments in short-form (J) and long-form (projection) can't coexist\n", "+    >>> my_module(\n", "+    ...    region='bla', projection='meh', J=\"bla\"\n", "+    ... )  # doctest: +NORMALIZE_WHITESPACE\n", "+    <PERSON><PERSON> (most recent call last):\n", "+      ...\n", "+    pygmt.exceptions.GMTInvalidInput:\n", "+        Arguments in short-form (J) and long-form (projection) can't coexist\n", "    \"\"\"\n", "\n", "    def alias_decorator(module_func):\n", "        \"\"\"\n", "        Decorator that replaces the aliases for arguments.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/nextstrain/augur/pull/900#discussion_r856613569\n", "                 MAIN COMMIT: https://github.com/nextstrain/augur/commit/ec76046d4989df168a626aac7a26efcdcacdcd10\n", "               AUTHOR COMMIT: https://github.com/huddlej/augur/commit/ec76046d4989df168a626aac7a26efcdcacdcd10\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: It looks like the original intent was to catch and print with an additional line, something like:\n", "\n", "```\n", "ERROR: Problem reading in ...\n", "character set ... contains a non-string element: ...\n", "```\n", "\n", "Since `index_sequences() -> index_sequence()` raises custom `ValueErrors`:\n", "\n", "https://github.com/nextstrain/augur/blob/ec76046d4989df168a626aac7a26efcdcacdcd10/augur/index.py#L121-L132\n", "\n", "If `ValueError` isn't caught here, it will result in an uncaught exception.\n", "\n", "        if is_vcf(args.sequences):\n", "            num_of_seqs = index_vcf(args.sequences, args.output)\n", "            tot_length = None\n", "        else:\n", "            num_of_seqs, tot_length = index_sequences(args.sequences, args.output)\n", "-    except FileNotFoundError:\n", "-        print(f\"ERROR: Could not open sequences file '{args.sequences}'.\", file=sys.stderr)\n", "-        return 1\n", "+    except FileNotFoundError:\n", "+        print(f\"ERROR: Could not open sequences file '{args.sequences}'.\", file=sys.stderr)\n", "+        return 1\n", "+    except ValueError as error:\n", "+        print(f\"ERROR: Problem reading sequences file '{args.sequences}':\", file=sys.stderr)\n", "+        print(error, file=sys.stderr)\n", "+        return 1\n", "\n", "    if args.verbose:\n", "        if tot_length:\n", "            print(\"Analysed %i sequences with an average length of %i nucleotides.\" % (num_of_seqs, int(tot_length / num_of_seqs)))\n", "        else:\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/facebook/sapp/pull/50#discussion_r724381509\n", "                 MAIN COMMIT: https://github.com/facebook/sapp/commit/6ae4be651f5efba6d28763e96123f3223d29d3ba\n", "               AUTHOR COMMIT: https://github.com/esiebomaj/sapp/commit/6ae4be651f5efba6d28763e96123f3223d29d3ba\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Would we be able to use `Query.one_or_none()` instead, since we expect [`WarningMessage.code` to be unique](https://github.com/facebook/sapp/blob/main/sapp/models.py#L1248\n", ") in `warning_messages` table?\n", "\n", "\n", "\n", "class WarningMessageQueryType(graphene.ObjectType):\n", "    message = graphene.String()\n", "    code = graphene.Int()\n", "-def get_warning_message(\n", "-    session: Session, code: int,) -> List[WarningMessage]:\n", "-    return (\n", "-        session.query(WarningMessage)\n", "-        .filter(WarningMessage.code == code)\n", "-        .all()\n", "-    )\n", "+def get_warning_message(\n", "+    session: Session, code: int,) -> WarningMessage:\n", "+    return (\n", "+        session.query(WarningMessage)\n", "+        .filter(WarningMessage.code == code)\n", "+        .one_or_none()\n", "+    )\n", "\n", "# pyre-ignore[13]: unitialized class attribute\n", "class IssueQueryResultType(graphene.ObjectType):\n", "    concatenated_features: str\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/ioos/erddapy/pull/267#discussion_r978747849\n", "                 MAIN COMMIT: https://github.com/ioos/erddapy/commit/b84c858e5d083f71e1d6b92ff913e7a6b4e67659\n", "               AUTHOR COMMIT: https://github.com/vinisalazar/erddapy/commit/b84c858e5d083f71e1d6b92ff913e7a6b4e67659\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: If the connection doesn't include server info, then the connection can be simplified.\n", "\n", "We could also support initializing servers based on whats in the [`servers.server_list`](https://github.com/ioos/erddapy/blob/6057872825aa29423e31c849a3c921fe25474e21/erddapy/servers/servers.py#L10) as we currently do.\n", "\n", "    pass\n", "\n", "\n", "class ERDDAPServer:\n", "    \"\"\"Instance of an ERDDAP server, with support to ERDDAP's native functionalities.\"\"\"\n", "-    def __init__(self, connection: str | ERDDAPConnection):\n", "-        \"\"\"Initialize instance of ERDDAPServer.\"\"\"\n", "-        self._connection = ERDDAPConnection(ERDDAPConnection.to_string(connection))\n", "+    def __init__(self, url: str, connection: ERDDAPConnection | None):\n", "+        \"\"\"Initialize instance of ERDDAPServer.\"\"\"\n", "+        if \"http\" in url:\n", "+            self.url = url\n", "+        else:\n", "+            # get URL from dict of ERDDAP servers\n", "+        self._connection = connection or ERDDAPConnection()\n", "\n", "    @property\n", "    def connection(self) -> ERDDAPConnection:\n", "        \"\"\"Access private ._connection attribute.\"\"\"\n", "        return self._connection\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/Slimefun/Slimefun4/pull/3279#discussion_r714694721\n", "                 MAIN COMMIT: https://github.com/Slimefun/Slimefun4/commit/eec5d2d66b2185b60c8eb34b293a9f8d78b6078a\n", "               AUTHOR COMMIT: https://github.com/variananora/Slimefun4/commit/eec5d2d66b2185b60c8eb34b293a9f8d78b6078a\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Should this just always be cancelled? From what I can tell that isAllowed will be true still for the reported issue. Relevant code: https://github.com/Slimefun/Slimefun4/blob/eec5d2d66b2185b60c8eb34b293a9f8d78b6078a/src/main/java/io/github/thebusybiscuit/slimefun4/implementation/listeners/BackpackListener.java#L121-L127\n", "\n", "\n", "                        if (!isAllowed((SlimefunBackpack) backpack, hotbarItem)) {\n", "                            e.setCancelled(true);\n", "                        }\n", "                    }\n", "-                } else if (e.getClick() == ClickType.SWAP_OFFHAND) { // Fixes #3265\n", "-                    if (e.getClickedInventory().getType() != InventoryType.PLAYER) {\n", "-                        ItemStack offHandItem = e.getWhoClicked().getInventory().getItemInOffHand();\n", "-\n", "-                        if (!isAllowed((SlimefunBackpack) backpack, offHandItem)) {\n", "-                            e.setCancelled(true);\n", "-                        }\n", "-                    }\n", "+                } else if (e.getClick() == ClickType.SWAP_OFFHAND && e.getClickedInventory().getType() != InventoryType.PLAYER) {\n", "+                    // Fixes #3265\n", "+                    ItemStack offHandItem = e.getWhoClicked().getInventory().getItemInOffHand();\n", "+\n", "+                    if (!isAllowed((SlimefunBackpack) backpack, offHandItem)) {\n", "+                        e.setCancelled(true);\n", "+                    }\n", "                } else if (!isAllowed((SlimefunBackpack) backpack, e.getCurrentItem())) {\n", "                    e.setCancelled(true);\n", "                }\n", "            }\n", "        }\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/stylelint/stylelint/pull/6357#discussion_r977613484\n", "                 MAIN COMMIT: https://github.com/stylelint/stylelint/commit/711ce9dcb2292093c422b98e6f14d25f832b0740\n", "               AUTHOR COMMIT: https://github.com/kaorun343/stylelint/commit/711ce9dcb2292093c422b98e6f14d25f832b0740\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Let's adopt [ESLint's wording](https://eslint.org/docs/latest/user-guide/command-line-interface#--cache-strategy).\n", "\n", "Let's also add it to these docs:\n", "- https://github.com/stylelint/stylelint/blob/main/docs/user-guide/usage/cli.md\n", "- https://github.com/stylelint/stylelint/blob/main/docs/user-guide/usage/options.md\n", "\n", "\n", "        If the directory for the cache does not exist, make sure you add a trailing \"/\"\n", "        on *nix systems or \"\\\\\" on Windows. Otherwise the path will be assumed to be a file.\n", "\n", "      --cache-strategy              [default: 'metadata']\n", "-\t    // TODO\n", "+\t    Strategy for the cache to use for detecting changed files. Can be either\n", "+\t    \"metadata\" or \"content\".\n", "+\n", "+        The \"content\" strategy can be useful in cases where the modification time of\n", "+        your files change even if their contents have not. For example, this can happen\n", "+        during git operations like \"git clone\" because git does not track file modification\n", "+        time.  \n", "\n", "      --formatter, -f               [default: \"string\"]\n", "\n", "        The output formatter: ${getFormatterOptionsText({ useOr: true })}.\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/kubernetes-sigs/kubebuilder/pull/1355#discussion_r417411538\n", "                 MAIN COMMIT: https://github.com/kubernetes-sigs/kubebuilder/commit/f2fe2b18836b5bd4fd2d8a1f2f5aff45619d6c8f\n", "               AUTHOR COMMIT: https://github.com/camilamacedo86/kubebuilder/commit/f2fe2b18836b5bd4fd2d8a1f2f5aff45619d6c8f\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This function is a little weird as-is. The `bool` return value is never used, and returning no error for a non-existent config doesn't make sense. The following is more succinct, and should be used only once in [`pkg/cli/cli.initialize()`](https://github.com/kubernetes-sigs/kubebuilder/blob/master/pkg/cli/cli.go#L195):\n", "\n", "func (e saveError) Error() string {\n", "\treturn fmt.Sprintf(\"unable to save the configuration: %v\", e.err)\n", "}\n", "\n", "// IsProjectVersionSupported returns true if the project is already configured and it is v2\n", "-func IsProjectVersionSupported() (bool, error) {\n", "+func (c Config) CheckProjectVersionSupported() error {\n", "+\tif c.IsV2() || c.IsV3() {\n", "+\t\treturn nil\n", "+\t}\n", "+\treturn fmt.<PERSON><PERSON><PERSON>(NoticeColor, \"Project version \"+c.Version+\" is not supported. \"+\n", "+\t\t\"See how to upgrade your project to the latest version: https://book.kubebuilder.io/migration/guide.html\")\n", "+}\n", "\tcfg, err := Read()\n", "\tif os.IsNotExist(err) {\n", "\t\treturn true, nil\n", "\t}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/onflow/flow-go/pull/660#discussion_r640309653\n", "                 MAIN COMMIT: https://github.com/onflow/flow-go/commit/63052932c6c522989ab722ef931d2862001286ca\n", "               AUTHOR COMMIT: https://github.com/durkmurder/flow-go/commit/63052932c6c522989ab722ef931d2862001286ca\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: I think we need to add the latest sealed result, even if we have already sealed subsequent results. \n", "Would suggest to re-use part of the comment from the previous code version here: https://github.com/onflow/flow-go/blob/5433e1d3959767dc5e1f6b3ae28092502e8308b9/module/builder/consensus/builder.go#L442-L447\n", "\n", "\n", "\tsealedResult, err := b.resultsDB.ByID(latestSeal.ResultID)\n", "\tif err != nil {\n", "\t\treturn fmt.Errorf(\"could not retrieve sealed result (%x): %w\", latestSeal.ResultID, err)\n", "\t}\n", "-\t// handle latest sealed result manually\n", "-\t// this is needed to handle a case when we are building on top of genesis block\n", "+\t// At initialization, the execution tree is empty. However, during normal operations, we \n", "+\t// generally query the tree for \"all receipts, whose results are derived from the latest \n", "+\t// sealed and finalized result\". This requires the execution tree to know what the latest \n", "+\t// sealed and finalized result is, so we add it here.  \n", "+\t// Note: we only add the sealed and finalized result, without any Execution Receipts. This\n", "+\t// is sufficient to create a vertex in the tree. Thereby, we can subsequently traverse the \n", "+\t// tree to find derived results and their respective receipts.\t\n", "\terr = b.recPool.AddResult(sealedResult, latestSealed)\n", "\tif err != nil {\n", "\t\treturn fmt.Errorf(\"failed to add sealed result as vertex to ExecutionTree (%x): %w\", latestSeal.ResultID, err)\n", "\t}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/onflow/cadence/pull/1845#discussion_r939743555\n", "                 MAIN COMMIT: https://github.com/onflow/cadence/commit/c56291ae599cc97a7e4424f4996a930bec4334ce\n", "               AUTHOR COMMIT: https://github.com/dsainati1/cadence/commit/c56291ae599cc97a7e4424f4996a930bec4334ce\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: One possible solution is to use a temp address for array value, like the approach used by [`interpreterRuntime.newAccountContractsGetNamesFunction()`](https://github.com/onflow/cadence/blob/master/runtime/runtime.go#L2820-L2872).\n", "\n", "It calls `NewArrayValue` with `common.Address{}` (temp address).  Because slabs with temp address are not persisted, it avoids the problem of creating unreachable register.\n", "\n", "}\n", "\n", "func (interpreter *Interpreter) publicAccountPaths(addressValue AddressValue, getLocationRange func() LocationRange) *ArrayValue {\n", "\taddress := addressValue.ToAddress()\n", "\tvalues := interpreter.domainIterator(address, common.PathDomainPublic)\n", "-\treturn New<PERSON><PERSON>y<PERSON><PERSON><PERSON>(\n", "-\t\tinterpreter,\n", "-\t\tgetLocationRange,\n", "-\t\tNewVariableSizedStaticType(interpreter, PrimitiveStaticTypePublicPath),\n", "-\t\taddress,\n", "-\t\tvalues...,\n", "-\t)\n", "+\treturn NewArrayValue(\n", "+\t\tinterpreter,\n", "+\t\tgetLocationRange,\n", "+\t\tNewVariableSizedStaticType(interpreter, PrimitiveStaticTypePublicPath),\n", "+\t\tcommon.Address{},\n", "+\t\tvalues...,\n", "+\t)\n", "}\n", "\n", "func (interpreter *Interpreter) allAccountPaths(addressValue AddressValue, getLocationRange func() LocationRange) *ArrayValue {\n", "\taddress := addressValue.ToAddress()\n", "\tpublicValues := interpreter.domainIterator(address, common.PathDomainPublic)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/fastly/cli/pull/717#discussion_r1034591405\n", "                 MAIN COMMIT: https://github.com/fastly/cli/commit/4d39cc86125b6cc12aaede2d4b84aeae46b6ac1d\n", "               AUTHOR COMMIT: https://github.com/awilliams-fastly/cli/commit/4d39cc86125b6cc12aaede2d4b84aeae46b6ac1d\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Can we group together embedded types, and order fields for readability, so we're consistent with the rest of the code base ([example](https://github.com/fastly/cli/blob/main/pkg/commands/acl/create.go#L56-L64)):\n", "\n", "\treturn &c\n", "}\n", "\n", "// CreateSecretCommand calls the Fastly API to create a secret.\n", "type CreateSecretCommand struct {\n", "-\tcmd.Base\n", "-\tmanifest    manifest.Data\n", "-\tInput       fastly.CreateSecretInput\n", "-\tsecretFile  string\n", "-\tsecretSTDIN bool\n", "-\tjsonOutput\n", "+\tcmd.Base\n", "+\tjsonOutput\n", "+\t\n", "+\tInput       fastly.CreateSecretInput\n", "+\tmanifest    manifest.Data\n", "+\tsecretFile  string\n", "+\tsecretSTDIN bool\n", "}\n", "\n", "var errMultipleSecretValue = fsterr.RemediationError{\n", "\tInner:       fmt.<PERSON><PERSON><PERSON>(\"invalid flag combination, --secret-file and --secret-stdin\"),\n", "\tRemediation: \"Use one of --file or --stdin flag\",\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/algorand/indexer/pull/1415#discussion_r1084681790\n", "                 MAIN COMMIT: https://github.com/algorand/indexer/commit/f6e9bf036e78ebd5fb41d9c04976662f6f9f9817\n", "               AUTHOR COMMIT: https://github.com/AlgoStephenAkiki/indexer/commit/f6e9bf036e78ebd5fb41d9c04976662f6f9f9817\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The block processor skips round 0. We need to for the delta endpoint as well since it won't be able to fetch a delta for round 0 ever.\n", "https://github.com/algorand/indexer/blob/develop/conduit/plugins/processors/blockprocessor/block_processor.go#L129\n", "\n", "\t\t\treturn blk, err\n", "\t\t}\n", "\n", "\t\t// We aren't going to do anything with the new delta until we get everything\n", "\t\t// else converted over\n", "-\t\t_, err = sm.aclient.GetLedgerStateDelta(rnd).Do(sm.ctx)\n", "-\t\tif err != nil {\n", "-\t\t\treturn blk, err\n", "-\t\t}\n", "+\t\t// Round 0 has no delta associated with it\n", "+\t\tif rnd != 0 {\n", "+\t\t\t_, err = sm.aclient.GetLedgerStateDelta(rnd).Do(sm.ctx)\n", "+\t\t\tif err != nil {\n", "+\t\t\t\treturn blk, err\n", "+\t\t\t}\n", "+\t\t}\n", "\n", "\t\tblk = data.BlockData{\n", "\t\t\tBlockHeader: tmpBlk.Block.BlockHeader,\n", "\t\t\tPayset:      tmpBlk.Block.Payset,\n", "\t\t\tCertificate: &tmpBlk.Certificate,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/cuducos/chunk/pull/27#discussion_r1030956182\n", "                 MAIN COMMIT: https://github.com/cuducos/chunk/commit/5bc6c327932a27af76c41c568fab5b5b76a310d4\n", "               AUTHOR COMMIT: https://github.com/danielfireman/chunk/commit/5bc6c327932a27af76c41c568fab5b5b76a310d4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: [`c.start` is already `int64`](https://github.com/cuducos/chunk/blob/78be3922b59195aff90ddd5350e545cef6092db7/downloader.go#L90) and maybe we can use the return value of `f.WriteAt` to update `s.DownloadedFileBytes`:\n", "\n", "\t\t\tif err != nil {\n", "\t\t\t\ts.<PERSON>rror = err\n", "\t\t\t\tch <- s\n", "\t\t\t\treturn\n", "\t\t\t}\n", "-\t\t\t_, err = f.Write<PERSON>t(b, int64(c.start))\n", "-\t\t\tif err != nil {\n", "-\t\t\t\ts.Error = fmt.<PERSON><PERSON><PERSON>(\"error writing to %s: %w\", path, err)\n", "-\t\t\t\tch <- s\n", "-\t\t\t\treturn\n", "-\t\t\t}\n", "-\t\t\ts.DownloadedFileBytes += int64(len(b))\n", "+\t\t\ts, err = f.<PERSON>t(b, c.start)\n", "+\t\t\tif err != nil {\n", "+\t\t\t\ts.Error = fmt.<PERSON><PERSON><PERSON>(\"error writing to %s: %w\", path, err)\n", "+\t\t\t\tch <- s\n", "+\t\t\t\treturn\n", "+\t\t\t}\n", "+\t\t\ts.DownloadedFileBytes += s\n", "\t\t\tch <- s\n", "\t\t}(c)\n", "\t}\n", "}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/integr8ly/integreatly-operator/pull/1741#discussion_r621110281\n", "                 MAIN COMMIT: https://github.com/integr8ly/integreatly-operator/commit/3ae07fc47fd73ee86a7e18c86054b8d2f40c709b\n", "               AUTHOR COMMIT: https://github.com/makslion/integreatly-operator/commit/3ae07fc47fd73ee86a7e18c86054b8d2f40c709b\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: What do you think on just adding the `deployment[\"marin3rDeployment\"]` block from https://github.com/integr8ly/integreatly-operator/blob/3ae07fc47fd73ee86a7e18c86054b8d2f40c709b/test/common/deployment_types.go#L149-L155 to  here instead of just changing the replica value ?  I feel it would be slighly safer on the off chance someone in the future adds to this and messes up the array order causing the wrong struct replica value to be reassigned. Also this will make it a single place where it is added and not changed somewhere else in the code :thinking:\n", "\n", "\t\tif err != nil {\n", "\t\t\tif !k8sError.IsNotFound(err) {\n", "\t\t\t\tt.<PERSON>(\"Error obtaining ratelimit CR: %v\", err)\n", "\t\t\t}\n", "\t\t}\n", "-\t\tdeployment[\"marin3rDeployment\"].Products[1].ExpectedReplicas = *ratelimitCR.Spec.Replicas\n", "+\t\tdeployment[\"marin3rDeployment\"] = Namespace{\n", "+\t\t\tName: Marin3rProductNamespace,\n", "+\t\t\tProducts: []Product{\n", "+\t\t\t\t{Name: \"prom-statsd-exporter\", ExpectedReplicas: 1},\n", "+\t\t\t\t{Name: \"ratelimit\", ExpectedReplicas: *ratelimitCR.Spec.Replicas},\n", "+\t\t\t},\n", "+\t\t}\n", "\t}\n", "\n", "\treturn deployment[deploymentName]\n", "}\n", "\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Python/pull/2934#discussion_r502876700\n", "                 MAIN COMMIT: https://github.com/TheAlgorithms/Python/commit/7c53dda918d7df73fe5d2ee03b005467043fda74\n", "               AUTHOR COMMIT: https://github.com/sarthaka1310/Python/commit/7c53dda918d7df73fe5d2ee03b005467043fda74\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please provide `doctest` for testing the function and not the answer as it is already being tested by this [script](https://github.com/TheAlgorithms/Python/blob/master/project_euler/validate_solutions.py)\n", "\n", "It can be seen that n=6 produces a maximum n/φ(n) for n ≤ 10.\n", "\n", "Find the value of n ≤ 1,000,000 for which n/φ(n) is a maximum.\n", "\"\"\"\n", "\n", "-def solution() -> int:\n", "-    \"\"\"\n", "-    Returns solution to problem.\n", "-    Algorithm:\n", "-    Find n/φ(n)for all n ≤ 1,000,000 and return the n that attains maximum\n", "-\n", "-    >>> solution()\n", "-    999983\n", "-    \"\"\"\n", "-    n = 10 ** 6\n", "+def solution(n: int = 10 ** 6) -> int:\n", "+    \"\"\"\n", "+    Returns solution to problem.\n", "+    Algorithm:\n", "+    Find n/φ(n)for all n ≤ 1,000,000 and return the n that attains maximum\n", "+    \"\"\"\n", "\n", "    # Precompute phi using product formula (wikilink below)\n", "    # https://en.wikipedia.org/wiki/Euler%27s_totient_function#E<PERSON><PERSON>'s_product_formula\n", "\n", "    phi = list(range(0, n + 1))\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/optuna/optuna/pull/1892#discussion_r499411376\n", "                 MAIN COMMIT: https://github.com/optuna/optuna/commit/dab1ae2c86d65b7038fdbbd247bbe9a5bde0cbd2\n", "               AUTHOR COMMIT: https://github.com/norihitoishida/optuna/commit/dab1ae2c86d65b7038fdbbd247bbe9a5bde0cbd2\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Sorry for the confusion. According to the [wiki](https://github.com/optuna/optuna/wiki/Coding-Style-Conventions#write--versionadded--directive-to-the-doc-of-new-features), we should add `.. versionaddded::`. Could you fix as follows?\n", "\n", "\n", "from optuna.distributions import BaseDistribution\n", "from optuna.samplers import BaseSampler\n", "from optuna.study import Study\n", "from optuna.trial import FrozenTrial\n", "-\n", "-class PartialFixedSampler(BaseSampler):\n", "-    \"\"\"Sampler that can sample with some parameters fixed.\n", "-\n", "-        .. versionadded:: 2.2.0\n", "+\n", "+@experimental(\"2.3.0\")\n", "+class PartialFixedSampler(BaseSampler):\n", "+    \"\"\"Sampler that can sample with some parameters fixed.\n", "+    \n", "+        .. versionadded:: 2.3.0\n", "\n", "    Example:\n", "\n", "        After optimizing with :class:`~optuna.samplers.TPESampler`,\n", "        fix the value of ``y`` and optimize again with :class:`~optuna.samplers.CmaEsSampler`.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/feast-dev/feast/pull/1483#discussion_r634256800\n", "                 MAIN COMMIT: https://github.com/feast-dev/feast/commit/6a99cd9e07a98559f7db2d5cddaf64994c480cb4\n", "               AUTHOR COMMIT: https://github.com/leonid133/feast/commit/6a99cd9e07a98559f7db2d5cddaf64994c480cb4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: perhaps adapt this pattern https://github.com/feast-dev/feast/blob/73ac3de613a729da41b7c832bb4a520132399339/sdk/python/feast/infra/gcp.py#L29-L35\n", "\n", "from typing import Any, Callable, Dict, List, Optional, Sequence, Tuple, Union\n", "\n", "import boto3\n", "import mmh3\n", "import pandas\n", "-from botocore.exceptions import ClientError\n", "+try:\n", "+    import boto3\n", "+    from botocore.exceptions import ClientError\n", "+except ImportError as e:\n", "+    from feast.errors import FeastExtrasDependencyImportError\n", "+    raise FeastExtrasDependencyImportError(\"aws\", str(e))\n", "from tqdm import tqdm\n", "\n", "from feast import FeatureTable, utils\n", "from feast.entity import Entity\n", "from feast.feature_view import FeatureView\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/Project-MONAI/MONAI/pull/1886#discussion_r603160853\n", "                 MAIN COMMIT: https://github.com/Project-MONAI/MONAI/commit/888de1b7337dfafbf6070d3fcab7132d5ca5e490\n", "               AUTHOR COMMIT: https://github.com/yiheng-wang-nv/MONAI/commit/888de1b7337dfafbf6070d3fcab7132d5ca5e490\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: could follow `RandAxisFlip` as an example for the randomize() method: https://github.com/Project-MONAI/MONAI/blob/master/monai/transforms/spatial/array.py#L781-L809\n", "\n", "            coeff_mat[np_pts[:, 0], np_pts[:, 1], np_pts[:, 2]] = coeff\n", "            field = np.polynomial.legendre.leggrid3d(coords[0], coords[1], coords[2], coeff_mat)\n", "        else:\n", "            raise NotImplementedError(\"only supoprts 2D or 3D fields\")\n", "        return field\n", "-    def randomize(self, data: Optional[Any] = None) -> None:\n", "-        super().randomize(None)\n", "+    def randomize(self, spatial_dims: Optional[Any] = None) -> None:\n", "+        super().randomize(None)\n", "+        self._bias_fields = np.stack(\n", "+            [self._generate_random_field(spatial_dims, self.degree, self.coeff_range) for _ in range(num_channels)],\n", "+            axis=0,\n", "+        )\n", "\n", "    def __call__(self, img: np.n<PERSON><PERSON>):\n", "        \"\"\"\n", "        Apply the transform to `img`.\n", "        \"\"\"\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/aesara-devs/aesara/pull/541#discussion_r680249614\n", "                 MAIN COMMIT: https://github.com/aesara-devs/aesara/commit/3bae66fa14b4a2385c6220c12dc59f36b94d60a3\n", "               AUTHOR COMMIT: https://github.com/mjhajharia/aesara/commit/3bae66fa14b4a2385c6220c12dc59f36b94d60a3\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The `RandomVariable` interface requires other keyword arguments here (see [`RandomVariable.__call__`](https://github.com/aesara-devs/aesara/blob/main/aesara/tensor/random/op.py#L303)).\n", "\n", "    name = \"ARMA\"\n", "    ndim_supp = 1\n", "    ndims_params = [1, 1]\n", "    dtype = \"floatX\"\n", "    _print_name = (\"ARMA\", \"\\\\operatorname{ARMA}\")\n", "-    def __call__(\n", "-            self,\n", "-            loc: np.n<PERSON><PERSON>,\n", "-            scale: np.n<PERSON><PERSON>,\n", "-    ) -> RandomVariable:\n", "+    def __call__(\n", "+            self,\n", "+            loc: np.n<PERSON><PERSON>,\n", "+            scale: np.n<PERSON><PERSON>,\n", "+            **kwargs\n", "+    ) -> RandomVariable:\n", "        return super().__call__(loc, scale)\n", "\n", "    @classmethod\n", "    def rng_fn_scipy(\n", "            cls,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/tensorflow/data-validation/pull/20#discussion_r226913423\n", "                 MAIN COMMIT: https://github.com/tensorflow/data-validation/commit/ee3b0b946915cd26c75c1371dee287381c4e315a\n", "               AUTHOR COMMIT: https://github.com/terrytangyuan/data-validation/commit/ee3b0b946915cd26c75c1371dee287381c4e315a\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Use a matcher function to check the actual result similar to [CSV decoder](https://github.com/tensorflow/data-validation/blob/master/tensorflow_data_validation/coders/csv_decoder_test.py#L34).\n", "\n", "        'str_feature_2': np.array([b'string', b'list'], dtype=np.object),\n", "    }\n", "    example = tf.train.Example()\n", "    text_format.Merge(example_proto_text, example)\n", "    with beam.Pipeline() as p:\n", "-      result = (p\n", "+      result = (p\n", "+               | beam.Create([example.SerializeToString()])\n", "+               | tf_example_decoder.DecodeTFExample())\n", "+      util.assert_that(\n", "+          result,\n", "+          _make_example_dict_equal_fn(self, [expected_decoded]))\n", "        | beam.Create([example.SerializeToString()])\n", "        | tf_example_decoder.DecodeTFExample()\n", "        | beam.FlatMap(\n", "          lambda decoded_example: np.testing.assert_equal(\n", "            decoded_example, expected_decoded)))\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/OCA/server-tools/pull/2505#discussion_r1147392324\n", "                 MAIN COMMIT: https://github.com/OCA/server-tools/commit/6a6b0a81853d9cf125b4506421ee125e65cff8c8\n", "               AUTHOR COMMIT: https://github.com/AungKoKoLin1997/server-tools/commit/6a6b0a81853d9cf125b4506421ee125e65cff8c8\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: @AungKoKoLin1997 \n", "It seems that you took the module while it was still in migration to Odoo 15 (https://github.com/OCA/server-tools/pull/2329).\n", "Before it was fully done, there were some force-pushes that fixed some code. \n", "The forced commit: https://github.com/OCA/server-tools/pull/2329/commits/f94ba4fc5c3a15d979424cd043f49d2350e9df60#diff-db4160b3be01859e4c870cbbce8305e536fd4b5cad80ce3812b8e7a187aafccfR51-R56\n", "The compare between force-pushed and commit before: https://github.com/OCA/server-tools/compare/88258a039d89c9050311a65c78cd7ae679c1463d..5f43f4d41911488b92b84aaf71b968e0b2ab13dd\n", "Please also look what else changed.\n", "\n", "While migrating, @Mantux11 made an error that switched the lines around. That started generating bad file names. Then he force-pushed to switch them back. \n", "It looks like you took the code before it was fixed.\n", "\n", "TLDR:\n", "\n", "            )\n", "            excel = base64.decodebytes(excel)\n", "            if docids:\n", "                records = request.env[report.model].browse(docids)\n", "                if report.print_report_name and not len(records) > 1:\n", "-                    report_name = safe_eval(\n", "-                        report.print_report_name, {\"object\": records, \"time\": time}\n", "-                    )\n", "-                    # this is a bad idea, this sould only be .xlsx\n", "-                    extension = report_name.split(\".\")[-1:].pop()\n", "-                    report_name = f\"{report_name}.{extension}\"\n", "+                    # this is a bad idea, this should only be .xlsx\n", "+                    extension = report_name.split(\".\")[-1:].pop()\n", "+                    report_name = safe_eval(\n", "+                        report.print_report_name, {\"object\": records, \"time\": time}\n", "+                    )\n", "+                    report_name = f\"{report_name}.{extension}\"\n", "            excelhttpheaders = [\n", "                (\n", "                    \"Content-Type\",\n", "                    \"application/vnd.openxmlformats-\"\n", "                    \"officedocument.spreadsheetml.sheet\",\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/Qiskit/qiskit-experiments/pull/964#discussion_r1010302855\n", "                 MAIN COMMIT: https://github.com/Qiskit/qiskit-experiments/commit/7632c62c040728ef7dd6d04f36caae72c1d33f25\n", "               AUTHOR COMMIT: https://github.com/eggerdj/qiskit-experiments/commit/7632c62c040728ef7dd6d04f36caae72c1d33f25\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This code guarantees all fit results of sub fits exist, i.e. `fit_data.success = True` guarantees you have fit parameters in the dataset (but chi-sq can be large), otherwise fit parameter doesn't exist and code will crash when you try to pull that value from the `fit_dataset`. This is not affected by the fit quality that the experiment evaluates.\n", "https://github.com/Qiskit/qiskit-experiments/blob/0e6153d6f4d2fc61989ec85e9e241546f9d91855/qiskit_experiments/curve_analysis/utils.py#L142-L148\n", "\n", "        total_quality = self._evaluate_quality(fit_dataset)\n", "        if red_chi:\n", "            self.plotter.set_supplementary_data(fit_red_chi=red_chi)\n", "\n", "        # Create analysis results by combining all fit data\n", "-        primary_results = self._create_analysis_results(\n", "-            fit_data=fit_dataset, quality=total_quality, **self.options.extra.copy()\n", "-        )\n", "-        analysis_results.extend(primary_results)\n", "-        self.plotter.set_supplementary_data(primary_results=primary_results)\n", "+        for all(fit_data.success for fit_data in fit_dataset.values()):\n", "+            primary_results = self._create_analysis_results(\n", "+                fit_data=fit_dataset, quality=total_quality, **self.options.extra.copy()\n", "+            )\n", "+            analysis_results.extend(primary_results)\n", "+            self.plotter.set_supplementary_data(primary_results=primary_results)\n", "\n", "        if self.options.plot:\n", "            return analysis_results, [self.plotter.figure()]\n", "\n", "        return analysis_results, []\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/digitalfabrik/integreat-cms/pull/1976#discussion_r1051504599\n", "                 MAIN COMMIT: https://github.com/digitalfabrik/integreat-cms/commit/82a0ffddbe87cf4f7975f38c1e8e1d9a1e693f21\n", "               AUTHOR COMMIT: https://github.com/seluianova/integreat-cms/commit/82a0ffddbe87cf4f7975f38c1e8e1d9a1e693f21\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The problem with splitting the sentence is that the order of the words might be different in other languages and it makes it harder to grasp the context of the translations.\n", "Instead, I think this would be a good use case for my [translate_link()](https://github.com/digitalfabrik/integreat-cms/blob/45a836a6fb8426d0183a8c760b1895dba930c733/integreat_cms/cms/utils/translation_utils.py#L26-L48) utility I recently implemented.\n", "\n", "                    },\n", "                )\n", "\n", "                messages.info(\n", "                    request,\n", "-                    format_html(\n", "-                        \"{} <a href='{}' class='underline hover:no-underline'>{} {}</a> {}\",\n", "-                        _(\n", "-                            \"This is not the most recent public revision of this translation. Instead,\"\n", "+                    translate_link(\n", "+                        message,\n", "+                        attributes={\n", "+                            \"href\": public_translation_url,\n", "+                            \"class\": \"underline hover:no-underline\",\n", "+                        },\n", "                        ),\n", "                        public_translation_url,\n", "                        _(\"revision\"),\n", "                        public_translation.version,\n", "                        _(\"is shown in the apps\"),\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/ros2/launch_ros/pull/33#discussion_r290826440\n", "                 MAIN COMMIT: https://github.com/ros2/launch_ros/commit/4efbfe5e0933a3747954f78b1c765348711375e9\n", "               AUTHOR COMMIT: https://github.com/ivanpauno/launch_ros/commit/4efbfe5e0933a3747954f78b1c765348711375e9\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: That change will also raise an error in:\n", "https://github.com/ros2/launch_ros/blob/4efbfe5e0933a3747954f78b1c765348711375e9/test_launch_ros/test/test_launch_ros/test_normalize_parameters.py#L214-L239\n", "https://github.com/ros2/launch_ros/blob/4efbfe5e0933a3747954f78b1c765348711375e9/test_launch_ros/test/test_launch_ros/test_normalize_parameters.py#L283-L293\n", "\n", "I can add complicated logic for allowing interpreting some dissimilar arrays as arrays of strings, and some others not.\n", "But, what's that useful for?\n", "\n", "I think that in all the cases in `test_dictionary_with_dissimilar_array`, the user could write easily the parameters in other wat for getting an array of strings (if they want that). I wouldn't be so flexible as we are being now.\n", "\n", "If we want that change, I could later simplify `normalize_parameters` logic in a follow-up PR.\n", "\n", "                # All values in a list must have the same type.\n", "                # If they don't then assume it is a list of strings\n", "                yaml_evaluated_value: Union[List[str], List[int], List[float], List[bool]] = [\n", "                    yaml.safe_load(item) for item in evaluated_value\n", "                ]\n", "-                if check_sequence_type_is_allowed(yaml_evaluated_value):\n", "+                if not check_sequence_type_is_allowed(yaml_evaluated_value):\n", "+                    raise TypeError(\n", "+                        'Expected a non-empty sequence, with items of uniform type. '\n", "+                        'Allowed sequence item types are bool, int, float, str.'\n", "+                    )\n", "+                evaluated_value = tuple(yaml_evaluated_value)\n", "                    evaluated_value = tuple(yaml_evaluated_value)\n", "            else:\n", "                # Value is an array of the same type, so nothing to evaluate.\n", "                output_value = []\n", "                target_type = type(value[0])\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/E3SM-Project/e3sm_diags/pull/584#discussion_r863059676\n", "                 MAIN COMMIT: https://github.com/E3SM-Project/e3sm_diags/commit/b15a05feb08539e9c71ef0628720a90a415c4dc0\n", "               AUTHOR COMMIT: https://github.com/chengzhuzhang/e3sm_diags/commit/b15a05feb08539e9c71ef0628720a90a415c4dc0\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: `rmse()` and `corr()` both return float types:\n", "https://github.com/E3SM-Project/e3sm_diags/blob/a776ece46f9b0cb2154011fb39373445610a4590/e3sm_diags/metrics/__init__.py#L10-L19\n", "https://github.com/E3SM-Project/e3sm_diags/blob/a776ece46f9b0cb2154011fb39373445610a4590/e3sm_diags/metrics/__init__.py#L34-L40\n", "\n", "        \"min\": float(min_cdms(diff)) if diff is not None else missing_value,\n", "        \"max\": float(max_cdms(diff)) if diff is not None else missing_value,\n", "        \"mean\": float(mean(diff)) if diff is not None else missing_value,\n", "    }\n", "    metrics_dict[\"misc\"] = {\n", "-        \"rmse\": float(rmse(test_regrid, ref_regrid))\n", "-        if ref_regrid is not None\n", "-        else missing_value,\n", "-        \"corr\": float(corr(test_regrid, ref_regrid))\n", "-        if ref_regrid is not None\n", "-        else missing_value,\n", "+        \"rmse\": rmse(test_regrid, ref_regrid)\n", "+        if ref_regrid is not None\n", "+        else missing_value,\n", "+        \"corr\": corr(test_regrid, ref_regrid)\n", "+        if ref_regrid is not None\n", "+        else missing_value,\n", "    }\n", "    return metrics_dict\n", "\n", "\n", "def run_diag(parameter):  # noqa: C901\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458353422\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "    }\n", "\n", "    /**\n", "     * Sets a status value for WindowStatus.\n", "     * @param status a Boolean value\n", "-     */\n", "+    /**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public void setWindowStatus(Boolean status){\n", "        setParameters(KEY_WINDOW_STATUS, status);\n", "    }\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458353986\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "    /**\n", "     * Gets a Boolean value for WindowStatus.\n", "     * @return a Boolean object value or null.\n", "     * If true, means the WindowStatus data has been subscribed.\n", "-     */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return Bo<PERSON>an See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public Boolean getWindowStatus(){\n", "        return getBoolean(KEY_WINDOW_STATUS);\n", "    }\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458354952\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "    /**\n", "     * Gets a List<WindowStatus> value for WindowStatus.\n", "     * @return a list of WindowStatus object or null.\n", "     * If true, means the WindowStatus data has been subscribed.\n", "-     */\n", "+/**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return List<WindowStatus> See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    @SuppressWarnings(\"unchecked\")\n", "    public List<WindowStatus> getWindowStatus(){\n", "        return (List<WindowStatus>) getObject(WindowStatus.class, KEY_WINDOW_STATUS);\n", "    }\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458355326\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the[ RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "    }\n", "\n", "    /**\n", "     * Sets an array of statuses for WindowStatus.\n", "     * @param status a WindowStatus value\n", "-     */\n", "+/**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public void setWindowStatus(@NonNull List<WindowStatus> status){\n", "        setParameters(KEY_WINDOW_STATUS, status);\n", "    }\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458355913\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "    }\n", "\n", "    /**\n", "     * Sets an array of statuses for WindowStatus.\n", "     * @param status a WindowStatus value\n", "-     */\n", "+    /**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public void setWindowStatus(List<WindowStatus> status){\n", "        setParameters(KEY_WINDOW_STATUS, status);\n", "    }\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458356252\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "    /**\n", "     * Gets a List<WindowStatus> value for WindowStatus.\n", "     * @return a list of WindowStatus object or null.\n", "     * If true, means the WindowStatus data has been subscribed.\n", "-     */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return List<WindowStatus> See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    @SuppressWarnings(\"unchecked\")\n", "    public List<WindowStatus> getWindowStatus(){\n", "        return (List<WindowStatus>) getObject(WindowStatus.class, KEY_WINDOW_STATUS);\n", "    }\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458356982\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "\t}\n", "\n", "\t/**\n", "\t * Sets a status value for WindowStatus.\n", "\t * @param status a Boolean value\n", "-\t */\n", "+/**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "\tpublic void setWindowStatus(Boolean status){\n", "\t\tsetParameters(KEY_WINDOW_STATUS, status);\n", "\t}\n", "\n", "\t/**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458357454\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "\t/**\n", "\t * Gets a boolean value for WindowStatus.\n", "\t * @return a Boolean object value or null.\n", "\t * If true, means the WindowStatus data has been subscribed.\n", "-\t */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return Bo<PERSON>an See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "\tpublic Boolean getWindowStatus(){\n", "\t\treturn getBoolean(KEY_WINDOW_STATUS);\n", "\t}\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458359036\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "    }\n", "\n", "    /**\n", "     * Sets a vehicleDataResult value for WindowStatus.\n", "     * @param windowStatus a VehicleDataResult value\n", "-     */\n", "+/**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public void setWindowStatus(VehicleDataResult windowStatus){\n", "        setParameters(KEY_WINDOW_STATUS, windowStatus);\n", "    }\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458359217\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "    /**\n", "     * Gets a VehicleDataResult value for WindowStatus.\n", "     * @return a VehicleDataResult object value or null.\n", "     * If true, means the VehicleDataResult data has been subscribed.\n", "-     */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return Bo<PERSON>an See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public VehicleDataResult getWindowStatus(){\n", "        return (VehicleDataResult) getObject(VehicleDataResult.class, KEY_WINDOW_STATUS);\n", "    }\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458359514\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "\t}\n", "\n", "\t/**\n", "\t * Sets a status value for WindowStatus.\n", "\t * @param status a Boolean value\n", "-\t */\n", "+/**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "\tpublic void setWindowStatus(Boolean status){\n", "\t\tsetParameters(KEY_WINDOW_STATUS, status);\n", "\t}\n", "\n", "\t/**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458359699\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "\t/**\n", "\t * Gets a boolean value for WindowStatus.\n", "\t * @return a WindowStatus object value or null.\n", "\t * If true, means the WindowStatus data has been subscribed.\n", "-\t */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return Bo<PERSON>an See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "\tpublic Boolean getWindowStatus(){\n", "\t\treturn getBoolean(KEY_WINDOW_STATUS);\n", "\t}\n", "}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458360049\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly. The generator can also be used to update the XML comments at the top of this file\n", "\n", "    }\n", "\n", "    /**\n", "     * Sets a status value for WindowStatus.\n", "     * @param status a WindowStatus value\n", "-     */\n", "+/**\n", "+     * Sets the windowStatus.\n", "+     *\n", "+     * @param windowStatus See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public void setWindowStatus(VehicleDataResult status){\n", "        setParameters(KEY_WINDOW_STATUS, status);\n", "    }\n", "\n", "    /**\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/smartdevicelink/sdl_java_suite/pull/1396#discussion_r458360167\n", "                 MAIN COMMIT: https://github.com/smartdevicelink/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "               AUTHOR COMMIT: https://github.com/kboskin/sdl_java_suite/commit/972b6ef08d060bf92b11ca38e4795eaac2b9eb1e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please use the [RPC generator](https://github.com/smartdevicelink/sdl_java_suite/tree/master/generator) in the sdl_java_suite project to ensure the RPC matched the spec exactly.\n", "\n", "\n", "    /**\n", "     * Gets a VehicleDataResult value for WindowStatus.\n", "     * @return a WindowStatus object value or null.\n", "     * If true, means the WindowStatus data has been subscribed.\n", "-     */\n", "+    /**\n", "+     * Gets the windowStatus. If true, means the WindowStatus data has been subscribed.\n", "+     *\n", "+     * @return Bo<PERSON>an See WindowStatus\n", "+     * @since SmartDeviceLink 7.0.0\n", "+     */\n", "    public VehicleDataResult getWindowStatus(){\n", "        return (VehicleDataResult) getObject(VehicleDataResult.class, KEY_WINDOW_STATUS);\n", "    }\n", "}\n", "\n", "====================================================================================================\n"]}], "source": ["import random\n", "\n", "random.seed(31415)\n", "# samples = random.sample(range(len(filtered_suggested_edit)), 200)\n", "# samples = [filtered_suggested_edit[index] for index in samples]\n", "samples = sorted(filtered_suggested_edit, key=lambda x: -len(x['new_code'].splitlines()))\n", "\n", "for suggestion in samples[:100]:\n", "    commit_url = 'https://github.com/%s/commit/%s' % (suggestion['repo_owner_name'], suggestion['original_commit']['commit_sha'])\n", "    print('                  REVIEW URL:', suggestion['html_url'])    \n", "    print('                 MAIN COMMIT:', commit_url)    \n", "    print('               AUTHOR COMMIT:', suggestion['commit_html_url'])    \n", "    print('MANUAL<PERSON>Y REVISED INSTRUCTION: ')\n", "    print('        ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print()\n", "    prefix = suggestion['prefix'].splitlines()[-5:]\n", "    suffix = suggestion['suffix'].splitlines()[:5]\n", "    print('\\n'.join(prefix))\n", "    print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "    print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "    print('\\n'.join(suffix))\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/all_including_without_commits.json\", \"w\") as f:\n", "    json.dump(filtered_suggested_edit, f)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import json \n", "\n", "with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/all.json\") as f:\n", "    filtered_suggested_edit = json.load(f)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n", "5\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Python/pull/3177#discussion_r502871106\n", "                      COMMIT: ['https://github.com/Hyftar/Python/commit/8f9d8ea051233a50ed9ede41362011bb2f309025']\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please include `doctest` which tests the function and not the answer. The answer part is tested by this [script](https://github.com/TheAlgorithms/Python/blob/master/project_euler/validate_solutions.py)\n", "\n", "        if is_prime[i]:\n", "            primes.append(i)\n", "\n", "    return primes\n", "\n", "-def solution() -> int:\n", "-    \"\"\"\n", "-    Computes the solution to the problem\n", "-    >>> solution()\n", "-    1259187438574927161\n", "-    \"\"\"\n", "-    limit = 999966663333\n", "+def solution(limit: int = 999966663333) -> int:\n", "+    \"\"\"\n", "+    Computes the solution to the problem\n", "+    \"\"\"\n", "    primes_upper_bound = math.floor(math.sqrt(limit)) + 100\n", "    primes = sieve(primes_upper_bound)\n", "\n", "    matches_sum = 0\n", "    prime_index = 0\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Python/pull/2312#discussion_r471038377\n", "                      COMMIT: ['https://github.com/AryanRaj315/Python/commit/f32d5c5386f27ac6b604a000dbdd67db6bedde56']\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Adds a proper function name in snake_case with Python type hints and a doctest.  Please read [CONTRIBUTING.md](https://github.com/TheAlgorithms/Python/blob/master/CONTRIBUTING.md).\n", "\n", "    \"\"\"\n", "    This function is responsible for loading Qtable.txt if already present\n", "    \"\"\"\n", "    Q_table = pickle.load(f)\n", "\n", "-def Action(state, Q_table):\n", "-    \"\"\"\n", "-    This function returns the most suitable action value from the qtable.\n", "-    \"\"\"\n", "-    action = np.argmax(Q_table[state])\n", "-    if action is None:\n", "-        action = randomAction()\n", "-    return action\n", "-\n", "-\n", "-def randomAction():\n", "-    \"\"\"\n", "-    This function returns random action just in case the Qtable values return None.\n", "-    \"\"\"\n", "-    action = np.random.choice([0, 1, 2, 3])\n", "-    return action\n", "+def next_best_action(state: int, Q_table: np.ndarray) -> int:\n", "+    \"\"\"\n", "+    Return the most suitable action value from Q_table or a random action\n", "+    if there is no np.argmax(Q_table[state]).\n", "+    \n", "+    >>> next_best_action(1, []) in [0, 1, 2, 3]\n", "+    True\n", "+    \"\"\"\n", "+    action = np.argmax(Q_table[state])\n", "+    return action if action is not None else np.random.choice([0, 1, 2, 3])\n", "\n", "\n", "def StateActionReward(player, x_food, y_food):\n", "    \"\"\"\n", "    This function returns state, action and reward to update the Qtable.\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/openai/gym/pull/2374#discussion_r698008306\n", "                      COMMIT: ['https://github.com/FirefoxMetzger/gym/commit/f6cef57a00a5224ab7e46578000b23e8e7eb657d']\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: This doesn't have too much impact on the test, but this should be `dtype=np.int64` (the flattened space takes the dtype of `Discrete`, [which is `np.int64`](https://github.com/openai/gym/blob/150404104afc57ea618acca34d2d1f797d779869/gym/spaces/discrete.py#L17)). Also the bounds should be 0 and 1 ([according to the rules for flattening the space](https://github.com/openai/gym/blob/150404104afc57ea618acca34d2d1f797d779869/gym/spaces/utils.py#L200-L214), since flattening a `Tuple` of `Dsicrete` spaces leads to multi-hot vectors).\n", "\n", "\n", "    if env_id == \"Blackjack-v0\":\n", "        space = spaces.<PERSON><PERSON>(\n", "            (spaces.<PERSON><PERSON><PERSON>(32), spaces.<PERSON><PERSON><PERSON>(11), spaces.<PERSON><PERSON><PERSON>(2))\n", "        )\n", "-        wrapped_space = spaces.Box(-np.inf, np.inf, [32 + 11 + 2], dtype=np.float64)\n", "+        wrapped_space = spaces.Box(0, 1, [32 + 11 + 2], dtype=np.int64)\n", "    elif env_id == \"KellyCoinflip-v0\":\n", "        space = spaces.<PERSON><PERSON>(\n", "            (spaces.Box(0, 250.0, [1], dtype=np.float32), spaces.Discrete(300 + 1))\n", "        )\n", "        wrapped_space = spaces.Box(-np.inf, np.inf, [1 + (300 + 1)], dtype=np.float64)\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/TheAlgorithms/Python/pull/2934#discussion_r502876700\n", "                      COMMIT: ['https://github.com/sarthaka1310/Python/commit/7c53dda918d7df73fe5d2ee03b005467043fda74']\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Please provide `doctest` for testing the function and not the answer as it is already being tested by this [script](https://github.com/TheAlgorithms/Python/blob/master/project_euler/validate_solutions.py)\n", "\n", "It can be seen that n=6 produces a maximum n/φ(n) for n ≤ 10.\n", "\n", "Find the value of n ≤ 1,000,000 for which n/φ(n) is a maximum.\n", "\"\"\"\n", "\n", "-def solution() -> int:\n", "-    \"\"\"\n", "-    Returns solution to problem.\n", "-    Algorithm:\n", "-    Find n/φ(n)for all n ≤ 1,000,000 and return the n that attains maximum\n", "-\n", "-    >>> solution()\n", "-    999983\n", "-    \"\"\"\n", "-    n = 10 ** 6\n", "+def solution(n: int = 10 ** 6) -> int:\n", "+    \"\"\"\n", "+    Returns solution to problem.\n", "+    Algorithm:\n", "+    Find n/φ(n)for all n ≤ 1,000,000 and return the n that attains maximum\n", "+    \"\"\"\n", "\n", "    # Precompute phi using product formula (wikilink below)\n", "    # https://en.wikipedia.org/wiki/Euler%27s_totient_function#E<PERSON><PERSON>'s_product_formula\n", "\n", "    phi = list(range(0, n + 1))\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/python-poetry/poetry/pull/5850#discussion_r936888423\n", "                      COMMIT: ['https://github.com/spoorn/poetry/commit/9ee7f4056c2741b624f190ceb423373307bb90a0']\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: `os.symlink` requires either administrative privileges or developer mode on Win10. We have a workaround to avoid symlinks in https://github.com/python-poetry/poetry/blob/83e6c318f59a77e83553c9805562490a4e5f8811/tests/helpers.py#L75-L92\n", "\n", "However, I don't think it makes sense to use this function here. Thus, maybe it's best to \"skip\" the test for windows if `os.symlink` fails.\n", "\n", "            dir=d4\n", "        ) as source_file, tempfile.NamedTemporaryFile(\n", "            dir=d3\n", "        ) as lock_file:\n", "            lock_file.close()\n", "-            os.symlink(Path(d3), symlink_path)\n", "+            try:\n", "+                os.symlink(Path(d3), symlink_path)\n", "+            except OSError:\n", "+                pass\n", "+                if sys.platform == \"win32\":\n", "+                    # os.symlink requires either administrative privileges or developer\n", "+                    # mode on Win10, throwing an OSError if neither is active.\n", "+                    # Test is not possible in that case.\n", "+                    return\n", "+                raise\n", "            locker = Locker(str(symlink_path) + os.sep + Path(lock_file.name).name, {})\n", "\n", "            package_local = Package(\n", "                \"local-package\",\n", "                \"1.2.3\",\n", "\n", "====================================================================================================\n"]}], "source": ["import random\n", "import json\n", "\n", "filtered_suggested_edit_shuffled = filtered_suggested_edit\n", "# random.seed(31415)\n", "# random.shuffle(filtered_suggested_edit_shuffled)\n", "\n", "first_half_len = len(filtered_suggested_edit_shuffled) // 2\n", "\n", "filtered_suggested_edit_shuffled_1 = filtered_suggested_edit_shuffled[:first_half_len]\n", "filtered_suggested_edit_shuffled_2 = filtered_suggested_edit_shuffled[first_half_len:]\n", "\n", "print(len(filtered_suggested_edit_shuffled_1))\n", "print(len(filtered_suggested_edit_shuffled_2))\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/half1.json\", \"w\") as f:\n", "#     json.dump(filtered_suggested_edit_shuffled_1, f)\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/half2.json\", \"w\") as f:\n", "#     json.dump(filtered_suggested_edit_shuffled_2, f)    \n", "\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/half1.json\", \"r\") as f:\n", "#     filtered_suggested_edit_shuffled_1 = json.load(f)\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/half2.json\", \"r\") as f:\n", "#     filtered_suggested_edit_shuffled_2 = json.load(f)    \n", "\n", "\n", "for suggestion in filtered_suggested_edit_shuffled_2:\n", "    commit_url = 'https://github.com/%s/commit/%s' % (suggestion['repo_owner_name'], suggestion['original_commit']['commit_sha'])\n", "    print('                  REVIEW URL:', suggestion['html_url'])    \n", "    print('                      COMMIT:', suggestion['commit_html_url'])    \n", "    print('MANUAL<PERSON>Y REVISED INSTRUCTION: ')\n", "    print('        ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print()\n", "    prefix = suggestion['prefix'].splitlines()[-5:]\n", "    suffix = suggestion['suffix'].splitlines()[:5]\n", "    print('\\n'.join(prefix))\n", "    print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "    print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "    print('\\n'.join(suffix))\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
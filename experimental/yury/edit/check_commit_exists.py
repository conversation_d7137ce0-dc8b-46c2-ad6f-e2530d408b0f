import json
import tqdm
from requests_html import HTMLSession
from bs4 import BeautifulSoup


INPUT = '/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/all_including_without_commits.json'
OUTPUT = '/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2/all.json'


def download(url):
    session = HTMLSession()
    request = session.get(url)
    import pdb; pdb.set_trace()
    webpage = request.html.render(timeout=20)
    if request.status_code in [404]:
        return None
    return request.content


def does_commit_exists_in_tree(repo_owner, repo_name, commit_sha):
    url = f"https://github.com/{repo_owner}/{repo_name}/commit/{commit_sha}"
    webpage = download(url)
    import pdb; pdb.set_trace()
    if webpage is None:
        return False
    soup = BeautifulSoup(webpage)
    warning_box = soup.find_all("div", id="spoof-warning")
    assert len(warning_box) == 1, url
    warning_box = warning_box[0]
    return 'hidden' in warning_box.attrs



with open(INPUT) as f:
    data_including_without_commits = json.load(f)
print('Loaded %d samples from %s' % (len(data_including_without_commits), INPUT))


data = []


for sample in tqdm.tqdm(data_including_without_commits[:10]):
    original_commit_sha = sample['original_commit']['commit_sha']
    if does_commit_exists_in_tree(
        sample['author'],
        sample['repo_name'],
        original_commit_sha,
    ):
        owner_who_has_commit = sample['author']
    elif does_commit_exists_in_tree(
        sample['repo_owner'],
        sample['repo_name'],
        original_commit_sha,
    ):
        owner_who_has_commit = sample['repo_owner']
    else:
        continue

    sample["commit_html_url"] = f"https://github.com/{owner_who_has_commit}/{sample['repo_name']}/commit/{original_commit_sha}"
    data.append(sample)


with open(OUTPUT, 'w') as f:
    json.dump(data, f)
print('Saved %d samples to %s' % (len(data), INPUT))

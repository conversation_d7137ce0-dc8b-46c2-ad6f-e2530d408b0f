import json
import os
import requests 
import multiprocessing
import difflib
import time
import tqdm
from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer, DeepSeekLLMChatTokenizer


SERVERS = [
    "http://localhost:8080/completion",
    "http://localhost:8081/completion",
    "http://localhost:8082/completion",
    "http://localhost:8083/completion",
    "http://localhost:8084/completion",
    "http://localhost:8085/completion",
    "http://localhost:8086/completion",
    "http://localhost:8087/completion",
]
# PATH = "/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v2.json"
PATH = "/home/<USER>/data/pr_suggested_edits.raw.v2.json"
OUTPUT_PATH = "/home/<USER>/data/pr_suggested_edits.fixllm.v2.jsonl"

tokenizer = DeepSeekCoderInstructTokenizer()


UNCLEAR = "UNCLEAR"
GOOD = "GOOD"
UNRELATED_CHANGES = "UNRELATED CHANGES"
INVALID_LLM_RESPONSE = "INVALID LLM RESPONSE"

MANUAL_TRIPLE_LABELS = {    
    "https://github.com/Neuraxio/Neuraxle/pull/309#discussion_r424270695": (
        "put separator after *args and use constant for the default value",
        "separator after args with default constant",
        GOOD,
    ),
    "https://github.com/carloscuesta/gitmoji-cli/pull/582#discussion_r622434992": (
        "add return type",
        "add type",
        GOOD,
    ),    
    "https://github.com/pytorch/vision/pull/5264#discussion_r790429498": (
        "improve exception error message",
        "improve error message",
        GOOD,
    ),
    "https://github.com/webex/webex-js-sdk/pull/1809#discussion_r462650960": (
        "Keep consistent with other functions and split into 2 separate arguments",
        "split into 2 arguments",
        GOOD,
    ),
    "https://github.com/jenkinsci/jenkins-test-harness/pull/465#discussion_r935642731": (
        "use object's isEmpty method instead of StringUtils",
        "use object's isEmpty method",
        GOOD,
    ),
    "https://github.com/apache/flink-training/pull/31#discussion_r697212832": (
        "change the arguments type as in other comment",
        "fix arguments type",
        UNCLEAR,
    ),
    "https://github.com/apolloconfig/apollo/pull/4189#discussion_r777299171": (
        "no need for the StrictMapAppenderConstructor",
        "remove StrictMapAppenderConstructor",
        UNRELATED_CHANGES,
    ),
    "https://github.com/aws/aws-toolkit-vscode/pull/1764#discussion_r636532097": (
        "don't initialize cfnTemplatePath",
        "remove initialization",
        GOOD,
    ),
    "https://github.com/greenplum-db/pxf/pull/703#discussion_r728486742": (        
        "rewrite code in one line",
        "rewrite in a line",
        GOOD,
    ),
    # "https://github.com/DKU-STUDY/Algorithm/pull/337#discussion_r464035670": (        
    #     "여기도 마찬가지",
    #     "여기도 마찬가지",
    #     UNCLEAR,
    # ),
    "https://github.com/aleph-im/pyaleph/pull/248#discussion_r864943892": (
        "use single number with underscore as thousand separator",
        "use single number with underscores",
        GOOD,
    ),
    "https://github.com/awslabs/multi-model-server/pull/689#discussion_r235183954": (
        "use String.valueOf",
        "use String class",
        GOOD,
    ),
    "https://github.com/OSGeo/grass-addons/pull/222#discussion_r446030744": (
        "add UI module description",
        "add module description",
        GOOD,
    ),
    "https://github.com/bridgecrewio/checkov/pull/4427#discussion_r1099838181": (
        "use None instead of empty string as default",
        "use None as default",
        GOOD,
    ),
    "https://github.com/netbox-community/netbox/pull/9554#discussion_r905098669": (
        "update time using raw update method",
        "update time via update method",
        GOOD,
    ),
    "https://github.com/99designs/gqlgen/pull/2131#discussion_r859905936": (
        "add godoc",
        "add godoc",
        UNRELATED_CHANGES,
    ),
    "https://github.com/matrix-org/matrix-js-sdk/pull/3254#discussion_r1162645693": (
        "drop 'or response from' from the comment",
        "drop or response from",
        GOOD,
    ),
    "https://github.com/yearn/yearn-sdk/pull/266#discussion_r838997715": (
        "return result directly without saving it in the const",
        "return result directly",
        GOOD,
    ),
    "https://github.com/nipy/nipype/pull/2896#discussion_r263160511": (
        "raise error instead of warning if max_jobs is not provided",
        "raise error",
        GOOD,
    ),
    "https://github.com/status-im/status-go/pull/2180#discussion_r613961182": (
        "add condition that cursors is not nill",
        "check cursors not nill",
        GOOD,
    ),
    "https://github.com/optuna/optuna/pull/1537#discussion_r471318089": (
        "validate input shapes with atleast_td",
        "validate input shapes",
        GOOD,
    ),
    "https://github.com/exadel-inc/esl/pull/987#discussion_r876529103": (
        "remove default value for a11yLabelActive",
        "remove default value",
        GOOD,
    ),
    "https://github.com/aminalaee/sqladmin/pull/101#discussion_r832961082": (
        "don't follow Flask-Admin behaviour here",
        "don't follow Flask-Admin",
        UNCLEAR,
    ),
    "https://github.com/darkbot-reloaded/DarkBot/pull/11#discussion_r511660612": (
        "add 2 more comments here",
        "add more comments",
        GOOD,
    ),
    "https://github.com/ImagingDataCommons/dicom-microscopy-viewer/pull/43#discussion_r608180779": (
        "switch values of variables Value and Meaning",
        "switch values of variables",
        GOOD,
    ),
    "https://github.com/Akuli/porcupine/pull/279#discussion_r571111199": (
        "use if-else statement instead of ternary operator",
        "use if-else statement",
        GOOD,
    ),
    "https://github.com/larq/larq/pull/446#discussion_r388253790": (
        "expect a ValueError",
        "expect ValueError",
        GOOD,
    ),
    "https://github.com/glotzerlab/signac-flow/pull/464#discussion_r606855373": (
        "rename class to match the style of other projects",
        "improve class name",
        GOOD,
    ),
    "https://github.com/hashicorp/terraform-ls/pull/1024#discussion_r942285083": (
        "remove unnecessary server setup and registry client",
        "don't setup server and client",
        UNCLEAR,
    ),
    "https://github.com/dstl/Stone-Soup/pull/624#discussion_r867899979": (
        "break line into multiple lines to pass FLAKE8 tests",
        "break line into multiple",
        GOOD,
    ),    
}


with open(PATH) as f:
    filtered_suggested_edit = json.load(f)

print('Loaded %d samples from %s' % (len(filtered_suggested_edit), PATH))

filtered_suggested_edit_by_url = {sample['html_url']: sample for sample in filtered_suggested_edit}


def get_diff(sample, n_context=3):
    # prefix = sample['prefix'].splitlines()[-5:]
    # suffix = sample['suffix'].splitlines()[:5]
    prefix = sample['prefix'].splitlines()
    suffix = sample['suffix'].splitlines()
    old_code = prefix + sample['old_code'].splitlines() + suffix
    new_code = prefix + sample['new_code'].splitlines() + suffix

    diff = '\n'.join(difflib.unified_diff(old_code, new_code, fromfile=sample['path'], tofile=sample['path'], lineterm='', n=n_context))
    return diff


TEMPLATE = """<｜begin▁of▁sentence｜>You are an AI programming assistant. Your task is to transform code review comments into two versions of actionable instructions, considering both the original comment and the code change as sources of additional context. Then, classify the sample into one of three categories. Structure your response in three lines as follows:

First Line - Verbose Instruction: Develop a detailed version of the instruction, maintaining relevant content from the original comment but omitting any polite language, subjective opinions, and extraneous details. Use both the comment and code change as context.
Second Line - Concise Instruction: Create a brief version of the instruction, ideal for users who prefer minimal wording but still require clarity. Incorporate both the comment and the code change for guidance.
Third Line - Classification Label: Classify the sample into one of these categories:
UNCLEAR: If the instruction is vague, lacks essential information, or requires additional context not provided.
UNRELATED CHANGES: If the revised instruction is clear but the code change contains changes that are unrelated to the instruction.
GOOD: If the revised instruction is clear and effectively addresses the code change, demonstrating an understanding of the task's requirements and the context of the code, making it a valuable and instructive example.

Focus Solely on Instructions: Your output should strictly be the actionable instruction derived from the original comment, without providing code continuation or modification. Ensure to use both the original comment and the provided code change (in backticks `) as context to inform your instructions.
Follow Historical Examples: Refer to the examples from our conversation history as a guide for how to approach this task

Your objective is to generate two distinct instructions (verbose and concise) based on the combination of the original comment and the code change, followed by a classification label that accurately reflects the nature and complexity of the instruction.

"""

EXAMPLE_INPUT = """### Instruction:
INPUT
{original_instruction}
CODE CHANGE

```
{code_diff}
```

### Response:
"""

EXAMPLE_OUTPUT = """{verbose_instruction}
{short_instruction}
{label}
<|EOT|>
"""

for sample_id, (verbose_instruction, short_instruction, label) in MANUAL_TRIPLE_LABELS.items():
    sample = filtered_suggested_edit_by_url[sample_id]
    TEMPLATE = TEMPLATE + EXAMPLE_INPUT.format(
        original_instruction=sample['original_instruction'],
        code_diff=get_diff(sample))
    TEMPLATE = TEMPLATE + EXAMPLE_OUTPUT.format(
        verbose_instruction=verbose_instruction,
        short_instruction=short_instruction,
        label=label)    

def persistent_cache(filename):
    def decorator(func):
        cache = {}
        cache_path = Path(filename)

        # Load existing cache if file exists
        if cache_path.is_file():
            with open(filename, 'r') as f:
                for line in f:
                    datum = json.loads(line[:-1])
                    cache[datum["key"]] = datum["value"]

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # handle special cases
            if len(args) == 1 and len(kwargs) == 0:
                key = args[0]
            else:
                key = json.dumps((args, kwargs), sort_keys=True)

            if key not in cache:
                cache[key] = func(*args, **kwargs)
                # Save the updated cache to the file
                with open(filename, 'a') as f:
                    f.write(json.dumps({"key": key, "value": cache[key]}) + '\n')

            return cache[key]

        return wrapper
    return decorator


def query_remote_model(sample):    
    server_index = multiprocessing.current_process()._identity[0] - 1
    server_url = SERVERS[server_index]

    text = TEMPLATE + EXAMPLE_INPUT.format(
        original_instruction=sample['original_instruction'],
        code_diff=get_diff(sample))

    # prompt_tokens = [tokenizer.bos_id] + tokenizer.tokenize(text)
    prompt_tokens = tokenizer.tokenize(text)
    if len(prompt_tokens) > 10240:
        print('TOO LONG QUERY')
        fixed_sample = {
            'diff_hunk': get_diff(sample, n_context=5),
            'revision_verdict': INVALID_LLM_RESPONSE,
        }
        fixed_sample.update(sample)
        return fixed_sample

    max_generated_tokens = 64
    temperature = 0
    top_k = 0
    top_p = 1
    data = {
        "prompt": prompt_tokens,
        "n_predict": max_generated_tokens,
        "temperature": temperature,
        "top_k": top_k or 50,
        "top_p": top_p or 0.95,
    }

    response = requests.post(
                server_url,
                json=data,
                headers={"Content-Type": "application/json"},
                timeout=120.0,  # set a timeout as 2 minutes
            )
    decoded_response = response.json()
    content = decoded_response["content"].strip().splitlines()

    fixed_sample = {
        'diff_hunk': get_diff(sample, n_context=5),
    }
    fixed_sample.update(sample)

    if len(content) != 3:
        fixed_sample.update({
            'revision_verdict': INVALID_LLM_RESPONSE,
        })
        return fixed_sample
    
    verbose_instruction, short_instruction, label = content
    if len(verbose_instruction) < len(short_instruction):
        verbose_instruction, short_instruction = short_instruction, verbose_instruction

    if label not in {GOOD, UNCLEAR, UNRELATED_CHANGES}:
        label = INVALID_LLM_RESPONSE

    fixed_sample.update({
        'revised_verbose_instruction': verbose_instruction,
        'revised_short_instruction': short_instruction,
        'revision_verdict': label,
    })
    return fixed_sample

llmfixed_filtered_suggested_edit_by_url = {}
if os.path.exists(OUTPUT_PATH):
    with open(OUTPUT_PATH, "r") as f:
        for line in f:
            sample = json.loads(line[:-1])
            llmfixed_filtered_suggested_edit_by_url[sample["html_url"]] = sample
    print('Loaded ALREADY PROCESSED %d samples from %s' % (len(llmfixed_filtered_suggested_edit_by_url), OUTPUT_PATH))

TQDM_FORMAT = (
    '{l_bar}{bar}| '
    '{n_fmt}/{total_fmt} [{elapsed}<{remaining} {postfix}]'
)

pbar = tqdm.tqdm(
    total=len(filtered_suggested_edit),
    desc='Revising instructions',
    bar_format=TQDM_FORMAT,
)
stats = {
    INVALID_LLM_RESPONSE: 0,
    UNCLEAR: 0,
    GOOD: 0,
    UNRELATED_CHANGES: 0,
}

filtered_suggested_edit_without_already_processed = []
for sample in filtered_suggested_edit:
    if sample["html_url"] in llmfixed_filtered_suggested_edit_by_url:
        fixed_sample = llmfixed_filtered_suggested_edit_by_url[sample["html_url"]]
        stats[fixed_sample["revision_verdict"]] += 1
        pbar.set_postfix(stats)
        pbar.update()
    else:
        filtered_suggested_edit_without_already_processed.append(sample)

with open(OUTPUT_PATH, "a") as llmfixed_filtered_suggested_edit_f:
    with multiprocessing.Pool(len(SERVERS)) as pool:
        for fixed_sample in pool.imap_unordered(query_remote_model, filtered_suggested_edit_without_already_processed):        
            stats[fixed_sample['revision_verdict']] += 1
            llmfixed_filtered_suggested_edit_f.write(json.dumps(fixed_sample) + "\n")
            llmfixed_filtered_suggested_edit_f.flush()
            pbar.set_postfix(stats)
            pbar.update()
pbar.close()

print('Processed %d samples' % len(filtered_suggested_edit))
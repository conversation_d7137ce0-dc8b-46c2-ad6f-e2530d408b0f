{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy\n", "import pathlib\n", "import pandas \n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    print(<PERSON><PERSON><PERSON><PERSON>(n))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["PATH = '/mnt/efs/augment/user/yury/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with pathlib.Path(PATH).open() as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commit': '45fc612fdc5a354dbf0bacccd345b1aebcc73e59',\n", " 'old_file': 'tests/test_openweather.py',\n", " 'new_file': 'tests/test_openweather.py',\n", " 'old_contents': '# -*- coding: utf-8 -*-\\nimport bot_mock\\nfrom pyfibot.modules import module_openweather\\nfrom utils import check_re\\n\\n\\nbot = bot_mock.BotMock()\\n\\n\\ndef test_weather():\\n    regex = u\\'Lappeenranta, FI: Temperature: \\\\d+.\\\\d\\\\xb0C, feels like: \\\\d+.\\\\d\\\\xb0C, wind: \\\\d+.\\\\d m/s, humidity: \\\\d+%, pressure: \\\\d+ hPa, cloudiness: \\\\d+%\\'\\n    check_re(regex, module_openweather.command_weather(bot, None, \"#channel\", \\'lappeenranta\\')[1])\\n\\n\\ndef test_forecast():\\n    regex = u\\'Lappeenranta, Finland: tomorrow: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\), in 2 days: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\), in 3 days: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\)\\'\\n    check_re(regex, module_openweather.command_forecast(bot, None, \"#channel\", \\'lappeenranta\\')[1])\\n',\n", " 'new_contents': '# -*- coding: utf-8 -*-\\nimport bot_mock\\nfrom pyfibot.modules import module_openweather\\nfrom utils import check_re\\n\\n\\nbot = bot_mock.BotMock()\\n\\n\\ndef test_weather():\\n    regex = u\\'Lappeenranta, FI: Temperature: \\\\d+.\\\\d\\\\xb0C, feels like: \\\\d+.\\\\d\\\\xb0C, wind: \\\\d+.\\\\d m/s, humidity: \\\\d+%, pressure: \\\\d+ hPa, cloudiness: \\\\d+%\\'\\n    check_re(regex, module_openweather.command_weather(bot, None, \"#channel\", \\'lappeenranta\\')[1])\\n\\n\\ndef test_forecast():\\n    regex = u\\'Lappeenranta, FI: tomorrow: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\), in 2 days: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\), in 3 days: \\\\d+.\\\\d-\\\\d+.\\\\d \\\\xb0C \\\\(.*?\\\\)\\'\\n    check_re(regex, module_openweather.command_forecast(bot, None, \"#channel\", \\'lappeenranta\\')[1])\\n',\n", " 'subject': '<PERSON>ert \"Fix openweather unit tests\"',\n", " 'message': '<PERSON>ert \"Fix openweather unit tests\"\\n\\nThis reverts commit 36e100e649f0a337228a6d7375358d23afd544ff.\\n\\nOpen Weather Map has reverted back to their old api or something like that...\\n',\n", " 'lang': 'Python',\n", " 'license': 'bsd-3-clause',\n", " 'repos': 'r<PERSON><PERSON>/pyfibot,<PERSON><PERSON><PERSON>/pyfibot,aapa/pyfibot,aapa/pyfibot,le<PERSON><PERSON><PERSON>/pyfibot,rnyberg/pyfibot,le<PERSON><PERSON><PERSON>/pyfibot,huqa/pyfibot,huqa/pyfibot,<PERSON><PERSON><PERSON>/pyfibot'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data[1]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["try:\n", "    from setuptools import setup\n", "    from setuptools import find_packages\n", "    packages = find_packages()\n", "except ImportError:\n", "    from distutils.core import setup\n", "    import os\n", "    packages = [x.strip('./').replace('/','.') for x in os.popen('find -name \"__init__.py\" | xargs -n1 dirname').read().strip().split('\\n')]\n", "\n", "if bytes is str:\n", "    raise Exception(\"This module is designed for python 3 only. Please install an older version to use python 2.\")\n", "\n", "setup(\n", "    name='cle',\n", "    description='CLE Loads Everything (at least, many binary formats!) and provides a pythonic interface to analyze what they are and what they would look like in memory.',\n", "    version='*********',\n", "    python_requires='>=3.6',\n", "    packages=packages,\n", "    install_requires=[\n", "        'pyelftools>=0.25',\n", "        'cffi',\n", "        'pyvex==*********',\n", "        'pefile',\n", "        'sortedcontainers>=2.0',\n", "    ],\n", "    extras_require={\n", "        \"minidump\": [\"minidump==0.0.10\"],\n", "        \"xbe\": [\"pyxbe==0.0.2\"],\n", "        \"ar\": [\"arpy==1.1.1\"],\n", "    }\n", ")\n", "\n"]}], "source": ["index = 10\n", "print(data[index]['old_contents'])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["try:\n", "    from setuptools import setup\n", "    from setuptools import find_packages\n", "    packages = find_packages()\n", "except ImportError:\n", "    from distutils.core import setup\n", "    import os\n", "    packages = [x.strip('./').replace('/','.') for x in os.popen('find -name \"__init__.py\" | xargs -n1 dirname').read().strip().split('\\n')]\n", "\n", "if bytes is str:\n", "    raise Exception(\"This module is designed for python 3 only. Please install an older version to use python 2.\")\n", "\n", "setup(\n", "    name='cle',\n", "    description='CLE Loads Everything (at least, many binary formats!) and provides a pythonic interface to analyze what they are and what they would look like in memory.',\n", "    version='*********',\n", "    python_requires='>=3.6',\n", "    packages=packages,\n", "    install_requires=[\n", "        'pyelftools>=0.25',\n", "        'cffi',\n", "        'pyvex==*********',\n", "        'pefile',\n", "        'sortedcontainers>=2.0',\n", "    ],\n", "    extras_require={\n", "        \"minidump\": [\"minidump>=0.0.10\"],\n", "        \"xbe\": [\"pyxbe==0.0.2\"],\n", "        \"ar\": [\"arpy==1.1.1\"],\n", "    }\n", ")\n", "\n"]}], "source": ["print(data[index]['new_contents'])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["before:\n", "'''docstring'''\n", "import foo\n", "import bar\n", "\n", "after:\n", "'''docstring'''\n", "import bar\n", "\n", "hunk.target_start=1 hunk.target_length=0\n"]}], "source": ["import tempfile\n", "from pathlib import Path\n", "import subprocess\n", "from unidiff import PatchSet\n", "\n", "def git_diff(\n", "    before_text: str,\n", "    before_path: str,\n", "    after_text: str,\n", "    after_path: str,\n", "    git_options: str = \"\",\n", ") -> str:\n", "    \"\"\"Run git-diff on the two pieces of text and return the output.\n", "\n", "    Args:\n", "        before_text: The first piece of text\n", "        after_text: The second piece of text\n", "        git_options: Command-line options to pass to 'git diff'\n", "\n", "    Returns:\n", "        The git diff output.\n", "    \"\"\"\n", "    with tempfile.TemporaryDirectory() as tmp_dir:\n", "        temp_before_path = Path(tmp_dir) / \"before.txt\"\n", "        temp_after_path = Path(tmp_dir) / \"after.txt\"\n", "\n", "        with temp_before_path.open(\"w\", encoding=\"utf8\") as before_file:\n", "            before_file.write(before_text)\n", "\n", "        with temp_after_path.open(\"w\", encoding=\"utf8\") as after_file:\n", "            after_file.write(after_text)\n", "\n", "        proc = subprocess.run(\n", "            f\"git diff {git_options} {temp_before_path} {temp_after_path}\",\n", "            shell=True,\n", "            check=False,\n", "            capture_output=True,\n", "        )\n", "        stdout = proc.stdout.decode()\n", "        stderr = proc.stderr.decode()\n", "        if stderr:\n", "            raise ValueError(f\"git diff failed: {stderr}\")\n", "        output = stdout\n", "        if not before_path.startswith(\"/\"):\n", "            before_path = f\"/{before_path}\"\n", "        if not after_path.startswith(\"/\"):\n", "            after_path = f\"/{after_path}\"\n", "        output = output.replace(str(temp_before_path), before_path)\n", "        output = output.replace(str(temp_after_path), after_path)\n", "        return output\n", "\n", "path = \"hello.txt\"\n", "\n", "before_text = \"'''docstring'''\\nimport foo\\nimport bar\\n\"\n", "after_text = \"'''docstring'''\\nimport bar\\n\"\n", "\n", "diff_text: str = git_diff(\n", "    before_text=before_text,\n", "    before_path=path,\n", "    after_text=after_text,\n", "    after_path=path,\n", "    git_options=\"-U0\",\n", ")\n", "diff_patchset = PatchSet(diff_text)\n", "hunk = diff_patchset[0][0]\n", "\n", "print(f\"before:\\n{before_text}\")\n", "print(f\"after:\\n{after_text}\")\n", "# print(diff_text)\n", "print(f\"{hunk.target_start=} {hunk.target_length=}\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Hunk: @@ 2,1 1,0 @@ >"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["diff_patchset[0][0]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/instruct-del_00_00-per40.json') as f:\n", "    for line in f:\n", "        data = json.loads(line[:-1])\n", "        break"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'index': 0,\n", " 'edit_data': {'repo': 'orangex4/typst-sympy-calculator',\n", "  'commit_sha': '9976fa05339b7610dab04caaec5e8195dcc0aba8',\n", "  'file_path': '6a3f4bea003c765bde2e622c404b28aa7ffff2fd:DefaultTypstCalculator.py',\n", "  'lines': [{'content': '\\n', 'origin': ' '},\n", "   {'content': \"    expr = calculator.simplify('lim_(x -> oo) 1/x')\\n\",\n", "    'origin': ' '},\n", "   {'content': \"    assert expr == '0'\\n\", 'origin': ' '},\n", "   {'content': '\\n', 'origin': '+'},\n", "   {'content': \"    expr = calculator.simplify('sum_(k = 1)^n k')\\n\",\n", "    'origin': '+'},\n", "   {'content': \"    assert expr == '1/2 n (n + 1)'\\n\", 'origin': '+'},\n", "   {'content': '\\n', 'origin': '+'},\n", "   {'content': \"    expr = calculator.simplify('product_(k = 1)^3 k')\\n\",\n", "    'origin': '+'},\n", "   {'content': \"    assert expr == '6'\\n\", 'origin': '+'},\n", "   {'content': '\\n', 'origin': '+'},\n", "   {'content': \"    expr = calculator.simplify('integral_0^1 x^2 dif x')\\n\",\n", "    'origin': '+'},\n", "   {'content': \"    assert expr == '1/3'\\n\", 'origin': '+'},\n", "   {'content': '\\n', 'origin': '+'},\n", "   {'content': \"    expr = calculator.simplify('derivative(x^2, x)')\\n\",\n", "    'origin': '+'},\n", "   {'content': \"    assert expr == '2 x'\\n\", 'origin': '+'}],\n", "  'old_file_content': \"import sympy\\nfrom TypstCalculator import TypstCalculator\\n\\n\\ndef get_default_calculator(calculator: TypstCalculator = None, complex_number: bool = True):\\n    if calculator is None:\\n        calculator = TypstCalculator(return_text=False, enable_subs=True)\\n\\n    operator, relation_op, additive_op, mp_op, postfix_op, reduce_op, func, func_mat, constant = calculator.get_decorators()\\n\\n    # Accents\\n    accents = ['cancel', 'grave', 'acute', 'hat', 'tilde', 'macron', 'breve', 'hdot', 'hdot.double',\\n               'hdot.triple', 'hdot.quad', 'diaer', 'circle', 'acute.double', 'caron', 'harrow', 'harrow.l']\\n    for accent in accents:\\n        calculator.define_accent(accent)\\n\\n    # Styles\\n    styles = ['upright', 'italic', 'bold']\\n    for style in styles:\\n        calculator.define_accent(style)\\n\\n    # Variants\\n    variants = ['serif', 'sans', 'frak', 'mono', 'bb', 'cal']\\n    for variant in variants:\\n        calculator.define_accent(variant)\\n\\n    # Under/Over\\n    underover = ['underline', 'overline']\\n    for uo in underover:\\n        calculator.define_accent(uo)\\n\\n    # Symbols\\n    abc = 'abcdefghijklmnopqrstuvwxyz'\\n    for c in abc:\\n        calculator.define_symbol_base(c)\\n        calculator.define_symbol_base(c.upper())\\n\\n    greeks = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta', 'iota', 'kappa',\\n              'lambda', 'mu', 'nu', 'xi', 'omicron', 'pi', 'rho', 'sigma', 'tau', 'upsilon', 'phi',\\n              'chi', 'psi', 'omega']\\n    for greek in greeks:\\n        calculator.define_symbol_base(greek)\\n        calculator.define_symbol_base(greek[0].upper() + greek[1:])\\n\\n    greek_alts = ['beta.alt', 'epsilon.alt', 'kappa.alt',\\n                  'phi.alt', 'pi.alt', 'rho.alt', 'theta.alt']\\n    for greek_alt in greek_alts:\\n        calculator.define_symbol_base(greek_alt)\\n\\n    # Constants\\n    @constant()\\n    def convert_oo():\\n        return sympy.oo\\n\\n    calculator.set_variance('pi', sympy.pi)\\n    calculator.set_variance('e', sympy.E)\\n    calculator.set_variance('E', sympy.E)\\n\\n    # complex_number\\n    if complex_number:\\n        calculator.set_variance('i', sympy.I)\\n        calculator.set_variance('I', sympy.I)\\n        calculator.set_variance('j', sympy.I)\\n\\n    # Relation Operators\\n    @relation_op()\\n    def convert_eq(a, b):\\n        return sympy.Eq(a, b)\\n\\n    @relation_op()\\n    def convert_eq_dot_not(a, b):\\n        return sympy.Ne(a, b)\\n\\n    @relation_op()\\n    def convert_gt(a, b):\\n        return sympy.Gt(a, b)\\n\\n    @relation_op()\\n    def convert_lt(a, b):\\n        return sympy.Lt(a, b)\\n\\n    @relation_op()\\n    def convert_gt_dot_eq(a, b):\\n        return sympy.Ge(a, b)\\n\\n    @relation_op()\\n    def convert_lt_dot_eq(a, b):\\n        return sympy.Le(a, b)\\n\\n    # Additive Operators\\n    @additive_op()\\n    def convert_plus(a, b):\\n        return a + b\\n\\n    # Mp Operators\\n    @mp_op()\\n    def convert_times(a, b):\\n        return a * b\\n\\n    @mp_op()\\n    def convert_times_dot(a, b):\\n        return a * b\\n\\n    @mp_op()\\n    def convert_div(a, b):\\n        return a / b\\n\\n    # Postfix Operators\\n    @postfix_op()\\n    def convert_degree(expr):\\n        return expr / 180 * sympy.pi\\n\\n    # Matrix\\n    @func_mat()\\n    def convert_mat(mat):\\n        return sympy.Matrix(mat)\\n\\n    @func()\\n    def convert_vec(*vec):\\n        return sympy.Matrix(vec)\\n\\n    # Functions\\n    @func()\\n    def convert_binom(n, k):\\n        return sympy.binomial(n, k)\\n\\n    @func()\\n    def convert_frac(n, d):\\n        return sympy.Rational(n, d)\\n\\n    @func()\\n    def convert_lr(expr):\\n        return expr\\n\\n    @func()\\n    def convert_sqrt(expr):\\n        return sympy.sqrt(expr)\\n\\n    @func()\\n    def convert_root(n, expr):\\n        return sympy.root(expr, n)\\n\\n    @func()\\n    def convert_round(expr):\\n        return sympy.Number(round(expr.evalf(), 0))\\n\\n    @func()\\n    def convert_abs(expr):\\n        return sympy.Abs(expr)\\n\\n    @func()\\n    def convert_arccos(expr):\\n        return sympy.acos(expr)\\n\\n    @func()\\n    def convert_arcsin(expr):\\n        return sympy.asin(expr)\\n\\n    @func()\\n    def convert_arctan(expr):\\n        return sympy.atan(expr)\\n\\n    @func()\\n    def convert_arctan2(expr):\\n        return sympy.atan2(expr)\\n\\n    @func()\\n    def convert_ceil(expr):\\n        return sympy.ceiling(expr)\\n\\n    @func()\\n    def convert_cos(expr):\\n        return sympy.cos(expr)\\n\\n    @func()\\n    def convert_cosh(expr):\\n        return sympy.cosh(expr)\\n\\n    @func()\\n    def convert_fact(expr):\\n        return sympy.factorial(expr)\\n\\n    @func()\\n    def convert_floor(expr):\\n        return sympy.floor(expr)\\n\\n    @func()\\n    def convert_gcd(f, g):\\n        return sympy.gcd(f, g)\\n\\n    @func()\\n    def convert_lcm(f, g):\\n        return sympy.lcm(f, g)\\n\\n    @func()\\n    def convert_log(expr):\\n        return sympy.log(expr)\\n\\n    @func()\\n    def convert_max(*args):\\n        return sympy.Max(*args)\\n\\n    @func()\\n    def convert_min(*args):\\n        return sympy.Min(*args)\\n\\n    @func()\\n    def convert_pow(b, e):\\n        return sympy.Pow(b, e)\\n\\n    @func()\\n    def convert_quo(f, g):\\n        return sympy.quo(f, g)\\n\\n    @func()\\n    def convert_rem(f, g):\\n        return sympy.rem(f, g)\\n\\n    @func()\\n    def convert_sin(expr):\\n        return sympy.sin(expr)\\n\\n    @func()\\n    def convert_sinh(expr):\\n        return sympy.sinh(expr)\\n\\n    @func()\\n    def convert_tan(expr):\\n        return sympy.tan(expr)\\n\\n    @func()\\n    def convert_tanh(expr):\\n        return sympy.tanh(expr)\\n\\n    # Matrix Functions\\n    @func()\\n    def convert_rank(expr):\\n        return sympy.Matrix.rank(expr)\\n\\n    @func()\\n    def convert_rref(expr):\\n        return sympy.Matrix.rref(expr)\\n\\n    @func()\\n    def convert_det(expr):\\n        return sympy.det(expr)\\n\\n    @func()\\n    def convert_transpose(expr):\\n        return sympy.transpose(expr)\\n\\n    @func()\\n    def convert_inverse(expr):\\n        return expr ** -1\\n\\n    @func()\\n    def convert_trace(expr):\\n        return sympy.trace(expr)\\n\\n    return calculator\\n\\n\\nif __name__ == '__main__':\\n\\n    calculator = get_default_calculator(complex_number=True)\\n    calculator.return_text = True\\n\\n    operator, relation_op, additive_op, mp_op, postfix_op, \\\\\\n        reduce_op, func, func_mat, constant = calculator.get_decorators()\\n\\n    expr = calculator.simplify('1 + 1')\\n    assert expr == '2'\\n\\n    expr = calculator.evalf('1/2', n=3)\\n    assert expr == '0.500'\\n\\n    calculator.set_variance('a', '1/2')\\n    expr = calculator.simplify('a + 1')\\n    assert expr == '3/2'\\n\\n    calculator.unset_variance('a')\\n    expr = calculator.simplify('a + 1')\\n    assert expr == 'a + 1' or expr == '1 + a'\\n\\n    expr = calculator.evalf('pi', n=3)\\n    assert expr == '3.14'\\n\\n    expr = calculator.simplify('max(1, 2)')\\n    assert expr == '2'\\n\\n    calculator.define_function('f')\\n    expr = calculator.simplify('f(1) + f(1) - f(1)')\\n    assert expr == 'f(1)'\\n\\n    expr = calculator.simplify('lim_(x -> oo) 1/x')\\n    assert expr == '0'\\n\",\n", "  'new_file_content': \"import sympy\\nfrom TypstCalculator import TypstCalculator\\n\\n\\ndef get_default_calculator(calculator: TypstCalculator = None, complex_number: bool = True):\\n    if calculator is None:\\n        calculator = TypstCalculator(return_text=False, enable_subs=True)\\n\\n    operator, relation_op, additive_op, mp_op, postfix_op, reduce_op, func, func_mat, constant = calculator.get_decorators()\\n\\n    # Accents\\n    accents = ['cancel', 'grave', 'acute', 'hat', 'tilde', 'macron', 'breve', 'hdot', 'hdot.double',\\n               'hdot.triple', 'hdot.quad', 'diaer', 'circle', 'acute.double', 'caron', 'harrow', 'harrow.l']\\n    for accent in accents:\\n        calculator.define_accent(accent)\\n\\n    # Styles\\n    styles = ['upright', 'italic', 'bold']\\n    for style in styles:\\n        calculator.define_accent(style)\\n\\n    # Variants\\n    variants = ['serif', 'sans', 'frak', 'mono', 'bb', 'cal']\\n    for variant in variants:\\n        calculator.define_accent(variant)\\n\\n    # Under/Over\\n    underover = ['underline', 'overline']\\n    for uo in underover:\\n        calculator.define_accent(uo)\\n\\n    # Symbols\\n    abc = 'abcdefghijklmnopqrstuvwxyz'\\n    for c in abc:\\n        calculator.define_symbol_base(c)\\n        calculator.define_symbol_base(c.upper())\\n\\n    greeks = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta', 'iota', 'kappa',\\n              'lambda', 'mu', 'nu', 'xi', 'omicron', 'pi', 'rho', 'sigma', 'tau', 'upsilon', 'phi',\\n              'chi', 'psi', 'omega']\\n    for greek in greeks:\\n        calculator.define_symbol_base(greek)\\n        calculator.define_symbol_base(greek[0].upper() + greek[1:])\\n\\n    greek_alts = ['beta.alt', 'epsilon.alt', 'kappa.alt',\\n                  'phi.alt', 'pi.alt', 'rho.alt', 'theta.alt']\\n    for greek_alt in greek_alts:\\n        calculator.define_symbol_base(greek_alt)\\n\\n    # Constants\\n    @constant()\\n    def convert_oo():\\n        return sympy.oo\\n\\n    calculator.set_variance('pi', sympy.pi)\\n    calculator.set_variance('e', sympy.E)\\n    calculator.set_variance('E', sympy.E)\\n\\n    # complex_number\\n    if complex_number:\\n        calculator.set_variance('i', sympy.I)\\n        calculator.set_variance('I', sympy.I)\\n        calculator.set_variance('j', sympy.I)\\n\\n    # Relation Operators\\n    @relation_op()\\n    def convert_eq(a, b):\\n        return sympy.Eq(a, b)\\n\\n    @relation_op()\\n    def convert_eq_dot_not(a, b):\\n        return sympy.Ne(a, b)\\n\\n    @relation_op()\\n    def convert_gt(a, b):\\n        return sympy.Gt(a, b)\\n\\n    @relation_op()\\n    def convert_lt(a, b):\\n        return sympy.Lt(a, b)\\n\\n    @relation_op()\\n    def convert_gt_dot_eq(a, b):\\n        return sympy.Ge(a, b)\\n\\n    @relation_op()\\n    def convert_lt_dot_eq(a, b):\\n        return sympy.Le(a, b)\\n\\n    # Additive Operators\\n    @additive_op()\\n    def convert_plus(a, b):\\n        return a + b\\n\\n    # Mp Operators\\n    @mp_op()\\n    def convert_times(a, b):\\n        return a * b\\n\\n    @mp_op()\\n    def convert_times_dot(a, b):\\n        return a * b\\n\\n    @mp_op()\\n    def convert_div(a, b):\\n        return a / b\\n\\n    # Postfix Operators\\n    @postfix_op()\\n    def convert_degree(expr):\\n        return expr / 180 * sympy.pi\\n\\n    # Matrix\\n    @func_mat()\\n    def convert_mat(mat):\\n        return sympy.Matrix(mat)\\n\\n    @func()\\n    def convert_vec(*vec):\\n        return sympy.Matrix(vec)\\n    \\n    # Reduces\\n    @reduce_op()\\n    def convert_sum(expr, args):\\n        return sympy.Sum(expr, args)\\n\\n    @reduce_op()\\n    def convert_product(expr, args):\\n        return sympy.Product(expr, args)\\n\\n    # Functions\\n    @func()\\n    def convert_derivative(expr, var):\\n        return sympy.Derivative(expr, var)\\n\\n    @func()\\n    def convert_binom(n, k):\\n        return sympy.binomial(n, k)\\n\\n    @func()\\n    def convert_frac(n, d):\\n        return sympy.Rational(n, d)\\n\\n    @func()\\n    def convert_lr(expr):\\n        return expr\\n\\n    @func()\\n    def convert_sqrt(expr):\\n        return sympy.sqrt(expr)\\n\\n    @func()\\n    def convert_root(n, expr):\\n        return sympy.root(expr, n)\\n\\n    @func()\\n    def convert_round(expr):\\n        return sympy.Number(round(expr.evalf(), 0))\\n\\n    @func()\\n    def convert_abs(expr):\\n        return sympy.Abs(expr)\\n\\n    @func()\\n    def convert_arccos(expr):\\n        return sympy.acos(expr)\\n\\n    @func()\\n    def convert_arcsin(expr):\\n        return sympy.asin(expr)\\n\\n    @func()\\n    def convert_arctan(expr):\\n        return sympy.atan(expr)\\n\\n    @func()\\n    def convert_arctan2(expr):\\n        return sympy.atan2(expr)\\n\\n    @func()\\n    def convert_ceil(expr):\\n        return sympy.ceiling(expr)\\n\\n    @func()\\n    def convert_cos(expr):\\n        return sympy.cos(expr)\\n\\n    @func()\\n    def convert_cosh(expr):\\n        return sympy.cosh(expr)\\n\\n    @func()\\n    def convert_fact(expr):\\n        return sympy.factorial(expr)\\n\\n    @func()\\n    def convert_floor(expr):\\n        return sympy.floor(expr)\\n\\n    @func()\\n    def convert_gcd(f, g):\\n        return sympy.gcd(f, g)\\n\\n    @func()\\n    def convert_lcm(f, g):\\n        return sympy.lcm(f, g)\\n\\n    @func()\\n    def convert_log(expr):\\n        return sympy.log(expr)\\n\\n    @func()\\n    def convert_max(*args):\\n        return sympy.Max(*args)\\n\\n    @func()\\n    def convert_min(*args):\\n        return sympy.Min(*args)\\n\\n    @func()\\n    def convert_pow(b, e):\\n        return sympy.Pow(b, e)\\n\\n    @func()\\n    def convert_quo(f, g):\\n        return sympy.quo(f, g)\\n\\n    @func()\\n    def convert_rem(f, g):\\n        return sympy.rem(f, g)\\n\\n    @func()\\n    def convert_sin(expr):\\n        return sympy.sin(expr)\\n\\n    @func()\\n    def convert_sinh(expr):\\n        return sympy.sinh(expr)\\n\\n    @func()\\n    def convert_tan(expr):\\n        return sympy.tan(expr)\\n\\n    @func()\\n    def convert_tanh(expr):\\n        return sympy.tanh(expr)\\n\\n    # Matrix Functions\\n    @func()\\n    def convert_rank(expr):\\n        return sympy.Matrix.rank(expr)\\n\\n    @func()\\n    def convert_rref(expr):\\n        return sympy.Matrix.rref(expr)\\n\\n    @func()\\n    def convert_det(expr):\\n        return sympy.det(expr)\\n\\n    @func()\\n    def convert_transpose(expr):\\n        return sympy.transpose(expr)\\n\\n    @func()\\n    def convert_inverse(expr):\\n        return expr ** -1\\n\\n    @func()\\n    def convert_trace(expr):\\n        return sympy.trace(expr)\\n\\n    return calculator\\n\\n\\nif __name__ == '__main__':\\n\\n    calculator = get_default_calculator(complex_number=True)\\n    calculator.return_text = True\\n\\n    operator, relation_op, additive_op, mp_op, postfix_op, \\\\\\n        reduce_op, func, func_mat, constant = calculator.get_decorators()\\n\\n    expr = calculator.simplify('1 + 1')\\n    assert expr == '2'\\n\\n    expr = calculator.evalf('1/2', n=3)\\n    assert expr == '0.500'\\n\\n    calculator.set_variance('a', '1/2')\\n    expr = calculator.simplify('a + 1')\\n    assert expr == '3/2'\\n\\n    calculator.unset_variance('a')\\n    expr = calculator.simplify('a + 1')\\n    assert expr == 'a + 1' or expr == '1 + a'\\n\\n    expr = calculator.evalf('pi', n=3)\\n    assert expr == '3.14'\\n\\n    expr = calculator.simplify('max(1, 2)')\\n    assert expr == '2'\\n\\n    calculator.define_function('f')\\n    expr = calculator.simplify('f(1) + f(1) - f(1)')\\n    assert expr == 'f(1)'\\n\\n    expr = calculator.simplify('lim_(x -> oo) 1/x')\\n    assert expr == '0'\\n\\n    expr = calculator.simplify('sum_(k = 1)^n k')\\n    assert expr == '1/2 n (n + 1)'\\n\\n    expr = calculator.simplify('product_(k = 1)^3 k')\\n    assert expr == '6'\\n\\n    expr = calculator.simplify('integral_0^1 x^2 dif x')\\n    assert expr == '1/3'\\n\\n    expr = calculator.simplify('derivative(x^2, x)')\\n    assert expr == '2 x'\\n\",\n", "  'old_hunk_range': [294, 297],\n", "  'new_hunk_range': [307, 322]},\n", " 'instruction': 'Based on the provided information, the user\\'s instruction could be:\\n\\n```\\n\"Add some code to test the calculator\\'s ability to simplify summation, product, integral, and derivative expressions.\"\\n```',\n", " 'input_file': '/home/<USER>/datasets/edit/organized-basic-2023-10-13/manager-del_00_00-per40.jsonl.zst'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from augment.research.models.remote_models import (\n", "    GenerationOptions,\n", "    CodeLLaMA_LLaMACPP_Model,\n", "    DeepSeekInstruct_LLaMACPP_Model,\n", ")\n", "from augment.research.core.llama_prompt_formatters import WizardCoderChatFormatter\n", "from augment.research.core.model_input import ModelInput\n", "\n", "# WizardCoder model\n", "# model = CodeLLaMA_LLaMACPP_Model(url=\"http://127.0.0.1:8086\")\n", "# model.prompt_formatter = WizardCoderChatFormatter()\n", "\n", "model = DeepSeekInstruct_LLaMACPP_Model(url=\"http://127.0.0.1:8087\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["print(\"Hello, <PERSON>!\")\n", "print(\"Hello, <PERSON>!\")\n", "print(\"Hello, <PERSON>!\")\n", "print(\"Hello, <PERSON>!\")\n"]}], "source": ["import re\n", "\n", "def clean_markdown(text):\n", "    for markdown_occurence in re.finditer(r\"(```[^ ]*\\n)\", text):\n", "        text = text[markdown_occurence.end():]\n", "        break\n", "    for markdown_occurence in re.finditer(r\"(\\n```)\", text):\n", "        text = text[:markdown_occurence.start()]\n", "        break\n", "    return text\n", "\n", "print(clean_markdown(\"\"\"```python\n", "print(\"Hello, <PERSON>!\")\n", "```\"\"\"))\n", "\n", "print(clean_markdown(\"\"\"```c++\n", "print(\"Hello, <PERSON>!\")\n", "```\"\"\"))\n", "\n", "print(clean_markdown(\"\"\"```\n", "print(\"Hello, <PERSON>!\")\n", "```\"\"\"))\n", "\n", "print(clean_markdown(\"\"\"print(\"Hello, World!\")\"\"\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["print(\"Hello, <PERSON>!\")\n", "   load_data = torch.load(output_dir / args.source_doc_name)\n", "   doc_dicts, image_name = load_data['documents'], load_data['image_name']\n", "   repo_for_system: list[Document] = load_data['repo_for_system']\n", "   load_data = torch.load(output_dir / args.source_doc_name)\n", "   doc_dicts, image_name = load_data['documents'], load_data['image_name']\n", "   repo_for_system: list[Document] = load_data['repo_for_system']\n"]}], "source": [" def count_leading_spaces(original_line):\n", "    \"\"\"Counts the number of spaces at the beginning of a line\"\"\"\n", "    count = 0\n", "    for char in original_line:\n", "        if char == \" \":\n", "            count += 1\n", "        else:\n", "            break\n", "    return count\n", "\n", "def count_min_indentation(code):\n", "    # Adjust indentation\n", "    min_spaces = None\n", "    for original_line in code.split('\\n'):\n", "        current_indent = count_leading_spaces(original_line)\n", "        if min_spaces is None:\n", "            min_spaces = current_indent\n", "        else:\n", "            min_space = min(min_spaces, current_indent)\n", "    if min_spaces is None:\n", "        min_spaces = 0\n", "    return min_spaces\n", "\n", "def cleanup(original_code, modified_code):\n", "    # Remove Markdown formatting\n", "    modified_code = clean_markdown(modified_code)\n", "    extra_identation = count_min_indentation(original_code) - count_min_indentation(modified_code)\n", "    modified_lines = []\n", "    for modified_line in modified_code.split('\\n'):\n", "        modified_lines.append(' ' * extra_identation + modified_line)\n", "    modified_code = '\\n'.join(modified_lines)\n", "    modified_code = modified_code.rstrip()\n", "    return modified_code\n", "\n", "print(cleanup(\"\", \"\"\"```python\n", "print(\"Hello, <PERSON>!\")\n", "```\"\"\"))\n", "\n", "print(cleanup(\n", "    \"\"\"\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "    \"\"\".strip(\"\\n\"),\n", "    \"\"\"\n", "load_data = torch.load(output_dir / args.source_doc_name)\n", "doc_dicts, image_name = load_data['documents'], load_data['image_name']\n", "repo_for_system: list[Document] = load_data['repo_for_system']\n", "\"\"\".strip(\"\\n\"),\n", "))\n", "\n", "print(cleanup(\n", "    \"\"\"\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "    \"\"\".strip(\"\\n\"),\n", "    \"\"\"\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data['documents'], load_data['image_name']\n", "    repo_for_system: list[Document] = load_data['repo_for_system']\n", "\"\"\".strip(\"\\n\"),\n", "))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f'system-{idx}-of-{len(systems)}.repr').open('w') as xfile:\n", "            xfile.write(str(system) + '\\n')\n", "        with (real_output_dir / f'system-{idx}-of-{len(systems)}.json').open('w') as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f'Finish loading the system, output_dir={real_output_dir}')\n", "    logger.info(f'Add the repo with {len(repo_for_system)} documents.')\n"]}], "source": ["PROMPT = \"\"\"{instruction}\n", "\n", "Adhere strictly to these guidelines in your response:\n", "\n", "- Execute the provided instructions with precision and accuracy, ensuring no errors.\n", "- Your modifications should be based directly on the provided code.\n", "- Preserve the original code's indentation and trailing whitespace in your modifications.\n", "- Start your response directly with the modified code, without any additional text, headers, comments, suggestions, or formatting.\n", "\n", "Code\n", "```\n", "{selected_code}\n", "```\n", "\"\"\"\n", "\n", "def run(instruction, code):\n", "    code = code.strip(\"\\n\")\n", "    prompt = PROMPT.format(\n", "        instruction=instruction,\n", "        selected_code=code,\n", "    )\n", "    response = model.generate(\n", "        ModelInput(prefix=prompt),\n", "        options=GenerationOptions(max_generated_tokens=512),\n", "    )\n", "    response = response.rstrip()\n", "    response = cleanup(code, response)\n", "    print(response)\n", "\n", "run(\n", "    \"Use single quotes instead of double quotes\",\n", "    \"\"\"\n", "    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data['documents'], load_data['image_name']\n", "    repo_for_system: list[Document] = load_data['repo_for_system']\n"]}], "source": ["run(\n", "    \"Use single quotes instead of double quotes\",\n", "    \"\"\"\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    \n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\"w\") as xfile:\n", "            xfile.write(str(system) + \"\\n\")\n", "            \n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\"w\") as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    \n", "    for system in systems:\n", "        system.load()\n", "        \n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n"]}], "source": ["run(\n", "    \"Improve the code\",\n", "    \"\"\"\n", "    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]  # Get the list of systems from provided names\n", "    \n", "    # Iterate over each system and write its representation to a file\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\"w\") as xfile:  # Open the file for writing\n", "            xfile.write(str(system) + \"\\n\")  # Write the system representation to the file\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\"w\") as xfile:  # Open another file for writing\n", "            xfile.write(system.ui_to_json())  # Write the system's JSON representation to the file\n", "    \n", "    # Load each system, add documents from a repository, and log the process\n", "    for system in systems:\n", "        system.load()  # Load the system\n", "        system.add_docs(repo_for_system)  # Add documents from the repository to the system\n", "        \n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")  # Log the completion of system loading\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")  # Log the addition of documents to the system\n"]}], "source": ["run(\n", "    \"add more comments\",\n", "    \"\"\"\n", "    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import datetime\n", "print(datetime.date.today())\n"]}], "source": ["run(\n", "    \"Write a function to tell me what the date is today.\",\n", "    \"\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["from pathlib import Path\n", "import json\n", "\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(Path(PATH).expanduser(), 'r') as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n"]}], "source": ["run(\n", "    \"use pathlib library to open file\",\n", "    \"\"\"\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import json\n", "\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n"]}], "source": ["run(\n", "    \"add missing import\",\n", "    \"\"\"\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    temp_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = temp_data[\"documents\"], temp_data[\"image_name\"]\n", "    repo_for_system: list[Document] = temp_data[\"repo_for_system\"]\n"]}], "source": ["run(\n", "    \"rename load_data to temp_data\",\n", "    \"\"\"\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "    \"\"\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / \"system-{}-of-{}.repr\".format(idx, len(systems))).open(\"w\") as xfile:\n", "            xfile.write(str(system) + \"\\n\")\n", "        with (real_output_dir / \"system-{}-of-{}.json\".format(idx, len(systems))).open(\"w\") as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(\"Finish loading the system, output_dir={}\".format(real_output_dir))\n", "    logger.info(\"Add the repo with {} documents.\".format(len(repo_for_system)))\n"]}], "source": ["run(\n", "    \"use string format() instead of f-str\",\n", "    \"\"\"\n", "    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    min_spaces = None\n", "    for original_line in original_code.split(\"\\n\"):\n", "        current_indent = count_leading_spaces(original_line)\n", "        if min_spaces is None:\n", "            min_spaces = current_indent\n", "        else:\n", "            min_space = min(min_spaces, current_indent)\n", "    if min_spaces is None:\n", "        min_spaces = 0\n"]}], "source": ["run(\"fix bug\", \n", "    \"\"\"\n", "    min_spaces = None\n", "    for original_line in original_code.split(\"\\n\"):\n", "        current_indent = count_leading_spaces(original_line)\n", "        if min_spaces is None:\n", "            min_spaces = current_indent\n", "        else:\n", "            min_space = min(min_spaces, current_indent)\n", "    if min_spaces is None:\n", "        min_spaces = 0\n", "    \"\"\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["for i, sample in enumerate(samples):\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n"]}], "source": ["run(\n", "    \"In the following code, make the for loop enumerate\",\n", "    \"\"\"\n", "for sample in samples:\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
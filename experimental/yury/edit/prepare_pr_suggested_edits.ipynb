{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import os\n", "import tqdm\n", "import random\n", "import base64\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import functools\n", "from pathlib import Path\n", "\n", "def persistent_cache(filename):\n", "    def decorator(func):\n", "        cache = {}\n", "        cache_path = Path(filename)\n", "\n", "        # Load existing cache if file exists\n", "        if cache_path.is_file():\n", "            with open(filename, 'r') as f:\n", "                for line in f:\n", "                    datum = json.loads(line[:-1])\n", "                    cache[datum[\"key\"]] = datum[\"value\"]\n", "\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            # handle special cases\n", "            if len(args) == 1 and len(kwargs) == 0:\n", "                key = args[0]\n", "            else:\n", "                key = json.dumps((args, kwargs), sort_keys=True)\n", "\n", "            if key not in cache:\n", "                cache[key] = func(*args, **kwargs)\n", "                # Save the updated cache to the file\n", "                with open(filename, 'a') as f:\n", "                    f.write(json.dumps({\"key\": key, \"value\": cache[key]}) + '\\n')\n", "\n", "            return cache[key]\n", "\n", "        return wrapper\n", "    return decorator\n", "\n", "# Example usage\n", "@persistent_cache('/tmp/my_cache.jsonl')\n", "def my_function(x):\n", "    print(\"EXECUTING FOR\", x)\n", "    return x ** 2\n", "\n", "my_function(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\"Authorization\": f\"token {TOKEN}\"}\n", "\n", "\n", "@persistent_cache('/home/<USER>/data/url_cache.jsonl')\n", "def download(url):    \n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    elif response.status_code == 404:\n", "        return None\n", "    else:\n", "        raise ValueError(f\"Failed to download {url}: {response.status_code}\")\n", "\n", "\n", "def download_file(repo_owner, repo_name, file_path, commit):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{file_path}?ref={commit}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")\n", "    try:\n", "        if result[\"encoding\"] != \"base64\":\n", "            raise ValueError(f\"Failed to download {url}: {result['encoding']} != base64\")    \n", "    except TypeError:\n", "        raise ValueError(f\"Failed to download {url}: problems with encoding in the result {result}\")\n", "    result[\"decoded_content\"] = base64.b64decode(result[\"content\"]).decode(\"utf8\")\n", "    return result\n", "\n", "\n", "def download_review_comment(repo_owner, repo_name, review_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/comments/{review_id}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")    \n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer, DeepSeekLLMChatTokenizer\n", "\n", "# Use this tokenizer if you are using DeepSeek-Coder model\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "# Use this tokenizer if you are using DeepSeek-LLM-Chat\n", "# tokenizer = DeepSeekLLMChatTokenizer()\n", "\n", "\n", "TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>You are an AI programming assistant, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "To avoid potential side effects, you can just create a new tokenizer instead\n", "### Response:\n", "create new tokenizer\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "I think this test would be easier to read if `10` were an argument of the test itself.\n", "### Response:\n", "Make 10 an argument of the test itself.\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "There are other statuses in rq that this should be tracking, so I would suggest to make the condition:\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "I think you should be able to use run_in_pyodide to reduce the nesting\n", "### Response:\n", "run_in_pyodide to reduce the nesting\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "it's maybe simpler to understand when downloading a file rather than iterating over a downloaded archive:\n", "### Response:\n", "download a file instead of iterating\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "A nit, but the grammar checker found some typos.\n", "### Response:\n", "fix typos\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Same note about possible consolidation of print statements.\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "It should be like this:\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Mostly cosmetic suggestion, not a blocker:\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Am I correct saying this would also reproduce for a regular `client.request()`? If so, should we update this to…?\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Stylistic nitpick\n", "### Response:\n", "fix style\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "I think a comment here.\n", "### Response:\n", "add comment\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Does self.children need to be checked? If it's empty, the for loop will be skipped anyway.\n", "### Response:\n", "don't check if self.children is empty\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "It might be best to check if the type is uuid.UUID instead of blindly casting to str\n", "### Response:\n", "check if the type is uuid.UUID\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "sorry for the nit, but this actually was hard to parse at first\n", "### Response:\n", "make it easier to read\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "seems like test cases 2, 3, 4, 5 are redundant, let's reduce the number of test cases\n", "### Response:\n", "remove test cases 2, 3, 4, 5\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "Better to solve this TODO, so we can use any type of CSV\n", "### Response:\n", "address the TODO\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "won't this work?\n", "### Response:\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "Your role is to succinctly rewrite code review comments into short actionable instructions.\n", "Rewrite comments to be direct and clear.\n", "Remove any pleasantries, subjective opinions, and unnecessary details.\n", "For comments that are ambiguous or lack essential information, your response should be uniformly \"UNCLEAR\".\n", "Code review comment is below:\n", "{instruction}\n", "### Response:\n", "\"\"\"\n", "\n", "@persistent_cache('/home/<USER>/data/deepseek_33b_instruct_cache.jsonl')\n", "def query_remote_model(text):\n", "    text = TEMPLATE.format(instruction=text)\n", "    # prompt_tokens = [tokenizer.bos_id] + tokenizer.tokenize(text)\n", "    prompt_tokens = tokenizer.tokenize(text)\n", "    max_generated_tokens = 64\n", "    temperature = 0\n", "    top_k = 0\n", "    top_p = 1\n", "    data = {\n", "        \"prompt\": prompt_tokens,\n", "        \"n_predict\": max_generated_tokens,\n", "        \"temperature\": temperature,\n", "        \"top_k\": top_k or 50,\n", "        \"top_p\": top_p or 0.95,\n", "    }\n", "\n", "    response = requests.post(\n", "                \"http://localhost:8086/completion\",\n", "                json=data,\n", "                headers={\"Content-Type\": \"application/json\"},\n", "                timeout=120.0,  # set a timeout as 2 minutes\n", "            )\n", "    decoded_response = response.json()\n", "    content = decoded_response[\"content\"].strip()\n", "    if content == 'UNCLEAR':\n", "        return None\n", "    content = ' '.join(content.splitlines())\n", "    return content\n", "\n", "\n", "SAMPLES = [\n", "    \"💬 suggestion: ‏\\nmore precise test description\\navoid ts-ignore annotation\\ninline unneeded variable\",\n", "    \"From @89z:\",\n", "    \"I think you can optimize this into a single UPDATE.\",\n", "    'Perhaps:',\n", "    \"This is pretty inflexible. I'd recommend doing something like this:\",\n", "    \"reduce indentation by using setdefault method, use `append` instead of `extend` when you're adding a single element at a time:\",\n", "    \"Simplify error reporting as below, or skip this check and just let the `_html_search_regex()` calls raise the exception?\",\n", "    \"Prefer listing imports separately and with trailing comma -- makes it easier to see changes, and sorted:\",\n", "    \"If this is retained, use `url_or_none()` from `utils.py` to condition the values:\",\n", "    \"Add data to help `upload_date` extraction and a sorting key:\",\n", "    \"Make the regex less strict and the search non-fatal:\",\n", "    \"`#playlist2` has gone: use `#playlist1` instead:\",\n", "    \"won't this work?\",\n", "    \"I think a comment here.\",\n", "    \"I reckon we should pull some of this out of the f-string...\",\n", "    \"I think then following will introduce less copying, and should allow us to merge this less obtrusively.\",\n", "    \"I think this is equivalent but shorter & easier to follow?\",\n", "    \"We could also make the `try` bit even shorter, which I think further clarifies the flow:\",\n", "    \"Maybe test with a slightly longer phrase? That looks more natural to me (I'm nitpicking, I know)\",\n", "    \"Here I think it's important to include at least some context:\",\n", "    \"The API should support `Iterable` here, as for the other pipeline components:\",\n", "    \"Stylistic nitpick 😅\",\n", "    \"To avoid potential side effects, you can just create a new tokenizer instead:\",\n", "    \"Oh, I didn't see that this was in the format off block. Now with formatting:\",\n", "    \"Use compat fn.\",\n", "    \"Don't let this crash the extraction since it's only being used for optional metadata:\",\n", "    \"Optional metadata:\",\n", "    \"Or:\",\n", "    \"Or:\",\n", "    \"Apparently <PERSON>'s Illustrated is `cio`.\",\n", "    \"Project convention:\",\n", "    \"One more situation that we should include in our tests...  What happens when we call middle_element() on an __empty__ LinkedList?\",\n", "    \"Let's add a [doctest](https://docs.python.org/3/library/doctest.html) _inside_ the docstring of the function.  The first test should pass and the second should fail.\",\n", "    \"Updated\",\n", "    \"___OPTIONAL:___  For me `range(len())` is always a sign that perhaps I should consider [`enumerate()`](https://docs.python.org/3/library/functions.html#enumerate) instead.  Sometimes this helps understand-ability.\",\n", "    \"Doctests must be in the (first) docstring of the function.\",\n", "    \"Whenever you see `range(len())`, try to use https://docs.python.org/3/library/functions.html#enumerate instead.\",\n", "]\n", "for sample in SAMPLES:\n", "    print(\"  input>\", sample)\n", "    print(\"revised>\", query_remote_model(sample))\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RE_SUGGESTION_PATTERN = r\"```suggestion\\r\\n([^`]*?)\\r\\n```\"\n", "\n", "\n", "def parse_single_suggestion(text: str):\n", "    comments, suggestions = [], []\n", "    while (m := re.search(RE_SUGGESTION_PATTERN, text)) is not None:\n", "        comment = text[:m.start()].strip()\n", "        if len(comment) > 0:\n", "            comments.append(comment)\n", "        text = text[m.end():].strip()\n", "        suggestions.append(m.group(1))\n", "        \n", "    text = text.strip()\n", "    if len(text) > 0:\n", "        comments.append(text)\n", "\n", "    if len(comments) == 1 and len(suggestions) == 1:\n", "        return {\n", "            \"original_instruction\": comments[0],\n", "            \"new_code\": suggestions[0],\n", "        }\n", "    else:\n", "        return None\n", "\n", "\n", "sample_1 = \"It might be best to check if the type is uuid.UUID instead of blindly casting to str\\r\\n\\r\\n```suggestion\\r\\n        if isinstance(field.data, uuid.UUID):\\r\\n            return\\r\\n\\r\\n        try:\\r\\n            uuid.UUID(field.data)\\r\\n        except ValueError:\\r\\n            raise ValidationError(message)\\r\\n```\"\n", "print(sample_1)\n", "print(parse_single_suggestion(sample_1))\n", "\n", "sample_2 = \"```suggestion\\r\\n        uuid.UUID(\\\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\\\"),\\r\\n```\\r\\n\\r\\nRNG in tests is bad!'\"\n", "print(sample_2)\n", "print(parse_single_suggestion(sample_2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# These files are located at '/mnt/efs/augment/user/yury/pr_data/\n", "PATHS = [\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Python.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Java.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Go.json',\n", "]\n", "\n", "parsed_suggested_edits = []\n", "n_total_repos, n_total_reviews = 0, 0\n", "\n", "for path in PATHS:\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "\n", "    n_reviews, n_suggestions = 0, 0\n", "\n", "    for repo_owner_name, repo in data.items():\n", "        repo_owner, repo_name = repo_owner_name.split('/')\n", "        for review in repo:\n", "            n_reviews += 1\n", "            parsed_suggested_edit = parse_single_suggestion(review['body'])\n", "            if parsed_suggested_edit is None:\n", "                continue\n", "            parsed_suggested_edit.update({\n", "                \"original_review_file\": path,\n", "                \"repo_owner_name\": repo_owner_name,\n", "                \"repo_owner\": repo_owner,\n", "                \"repo_name\": repo_name,\n", "            })            \n", "            n_suggestions += 1\n", "            parsed_suggested_edit.update(review)\n", "            parsed_suggested_edits.append(parsed_suggested_edit)            \n", "            \n", "    n_total_repos += len(data)\n", "    n_total_reviews += n_reviews\n", "    print('Loaded %d repos, %d reviews and %d suggested code edits from %s' % (len(data), n_reviews, n_suggestions, path))\n", "\n", "print('TOTAL Loaded %d repos, %d reviews and %d suggested code edits from' % (n_total_repos, n_total_reviews, len(parsed_suggested_edits)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAX_WORDS_IN_COMMENT = 20\n", "\n", "filtered_suggested_edit = []\n", "\n", "n_too_many_words = 0\n", "n_multiple_suggestions_in_comment = 0\n", "n_original_line_is_missing = 0\n", "n_failed_download_commits = 0\n", "n_instruction_too_vague = 0\n", "n_repo_no_longer_exists = 0\n", "\n", "\n", "def download_file_info(repo_owner, repo_name, path, commit_sha, start_line, end_line):\n", "    try:\n", "        file = download_file(\n", "            repo_owner, repo_name, path, commit_sha)\n", "        return {\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,\n", "            \"commit_sha\": commit_sha,\n", "            \"file_info\": file,\n", "            \"file\": file['decoded_content'],\n", "        }   \n", "    except ValueError:\n", "        return None    \n", "\n", "with tqdm.tqdm(total=len(parsed_suggested_edits)) as pbar:    \n", "    for parsed_suggested_edit in parsed_suggested_edits:\n", "        pbar.update(1)\n", "\n", "        suggested_edit = {}\n", "        suggested_edit.update(parsed_suggested_edit)\n", "\n", "        original_instruction = suggested_edit['original_instruction']\n", "        if len(original_instruction.split()) > MAX_WORDS_IN_COMMENT:\n", "            n_too_many_words += 1\n", "            continue\n", "\n", "        revised_instruction = query_remote_model(original_instruction)\n", "        if revised_instruction is None:\n", "            n_instruction_too_vague += 1\n", "            continue\n", "        suggested_edit[\"instruction\"] = revised_instruction\n", "\n", "        try:\n", "            full_comment_info = download_review_comment(\n", "                suggested_edit['repo_owner'],\n", "                suggested_edit['repo_name'],\n", "                suggested_edit['id'],\n", "            )\n", "            suggested_edit[\"full_comment_info\"] = full_comment_info\n", "        except ValueError:\n", "            n_repo_no_longer_exists += 1\n", "            continue\n", "\n", "        download_file_info_kwargs = {\n", "            \"repo_owner\": suggested_edit['repo_owner'],\n", "            \"repo_name\": suggested_edit['repo_name'],\n", "            \"path\": suggested_edit['path'],\n", "        }\n", "\n", "        end_line = full_comment_info['original_line']\n", "        start_line = full_comment_info['original_start_line'] or end_line\n", "        if end_line is None:\n", "            n_original_line_is_missing += 1\n", "            continue    \n", "        \n", "        original_commit_info = download_file_info(\n", "            commit_sha=full_comment_info['original_commit_id'],\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            **download_file_info_kwargs,\n", "        )\n", "\n", "        if original_commit_info is not None:\n", "            suggested_edit[\"original_commit\"] = original_commit_info\n", "    \n", "        end_line = full_comment_info['line'] or full_comment_info['original_line']\n", "        start_line = full_comment_info['start_line'] or full_comment_info['original_start_line'] or end_line    \n", "        commit_info = download_file_info(\n", "            commit_sha=full_comment_info['commit_id'],\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            **download_file_info_kwargs,\n", "        )\n", "    \n", "        if commit_info is not None:\n", "            suggested_edit[\"commit\"] = commit_info\n", "    \n", "        if original_commit_info is None and commit_info is None:\n", "            n_failed_download_commits += 1\n", "            continue\n", "\n", "        commit_key = \"original_commit\" if original_commit_info is not None else \"commit\"\n", "        \n", "        start_line = suggested_edit[commit_key]['start_line']\n", "        end_line = suggested_edit[commit_key]['end_line']\n", "        old_file = suggested_edit[commit_key]['file']\n", "        old_file_lines = old_file.splitlines()\n", "        prefix = '\\n'.join(old_file_lines[: max(0, start_line - 1)])\n", "        suffix = '\\n'.join(old_file_lines[end_line: ])\n", "        old_code = '\\n'.join(old_file_lines[start_line - 1: end_line])\n", "\n", "        suggested_edit.update({\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,\n", "            \"old_file\": old_file,\n", "            \"old_code\": old_code,\n", "            \"prefix\": prefix,\n", "            \"suffix\": suffix,\n", "        })\n", "        filtered_suggested_edit.append(suggested_edit)\n", "        pbar.set_postfix({\"mined_suggestions\": len(filtered_suggested_edit)})\n", "\n", "print('-- removed %d because comment had too many words' % n_too_many_words)\n", "print('-- removed %d because repo no longer exists' % n_repo_no_longer_exists)\n", "print('-- removed %d because lines are missing' % n_original_line_is_missing)\n", "print('-- removed %d because failed to download commits' % n_failed_download_commits)\n", "\n", "print('Final suggestions: %d' % len(filtered_suggested_edit))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v1.json\", \"w\") as f:\n", "    json.dump(filtered_suggested_edit, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = [sample for sample in filtered_suggested_edit if sample[\"html_url\"] == \"https://github.com/opencontainers/runc/pull/2696#discussion_r553261406\"][0]\n", "\n", "sample.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample['full_comment_info']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample['original_commit']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample['original_commit']['file'].splitlines()[67 - 1: 114]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["suggestion = sample\n", "print('         INSTRUCTION:', suggestion['instruction'])\n", "print('ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "print('          REVIEW URL:', suggestion['html_url'])\n", "print()\n", "prefix = suggestion['prefix'].splitlines()[-5:]\n", "suffix = suggestion['suffix'].splitlines()[:5]\n", "print('\\n'.join(prefix))\n", "print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "print('\\n'.join(suffix))\n", "print()    \n", "print('=' * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index in random.sample(range(len(filtered_suggested_edit)), 200):\n", "    suggestion = filtered_suggested_edit[index]\n", "    print('         INSTRUCTION:', suggestion['instruction'])\n", "    print('ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print('          REVIEW URL:', suggestion['html_url'])\n", "    print()\n", "    prefix = suggestion['prefix'].splitlines()[-5:]\n", "    suffix = suggestion['suffix'].splitlines()[:5]\n", "    print('\\n'.join(prefix))\n", "    print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "    print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "    print('\\n'.join(suffix))\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_suggested_edit_2lines = []\n", "\n", "for sample in filtered_suggested_edit:\n", "    if len(sample['new_code'].splitlines()) > 1:\n", "        filtered_suggested_edit_2lines.append(sample)\n", "\n", "print(f\"Number of two lines added: {len(filtered_suggested_edit_2lines)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index in random.sample(range(len(filtered_suggested_edit_2lines)), 200):\n", "    suggestion = filtered_suggested_edit_2lines[index]\n", "    print('         INSTRUCTION:', suggestion['instruction'])\n", "    print('ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print('          REVIEW URL:', suggestion['html_url'])\n", "    print()\n", "    prefix = suggestion['prefix'].splitlines()[-5:]\n", "    suffix = suggestion['suffix'].splitlines()[:5]\n", "    print('\\n'.join(prefix))\n", "    print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "    print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "    print('\\n'.join(suffix))\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
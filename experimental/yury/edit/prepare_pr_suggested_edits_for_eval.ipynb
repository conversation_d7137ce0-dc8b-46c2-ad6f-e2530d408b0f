{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import functools\n", "from pathlib import Path\n", "\n", "def persistent_cache(filename):\n", "    def decorator(func):\n", "        cache = {}\n", "        cache_path = Path(filename)\n", "\n", "        # Load existing cache if file exists\n", "        if cache_path.is_file():\n", "            with open(filename, 'r') as f:\n", "                for line in f:\n", "                    datum = json.loads(line[:-1])\n", "                    cache[datum[\"key\"]] = datum[\"value\"]\n", "\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            # handle special cases\n", "            if len(args) == 1 and len(kwargs) == 0:\n", "                key = args[0]\n", "            else:\n", "                key = json.dumps((args, kwargs), sort_keys=True)\n", "\n", "            if key not in cache:\n", "                cache[key] = func(*args, **kwargs)\n", "                # Save the updated cache to the file\n", "                with open(filename, 'a') as f:\n", "                    f.write(json.dumps({\"key\": key, \"value\": cache[key]}) + '\\n')\n", "\n", "            return cache[key]\n", "\n", "        return wrapper\n", "    return decorator\n", "\n", "# Example usage\n", "@persistent_cache('/tmp/my_cache.jsonl')\n", "def my_function(x):\n", "    print(\"EXECUTING FOR\", x)\n", "    return x ** 2\n", "\n", "my_function(3)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'databaseId': 553261406,\n", " 'bodyText': 'Given that each test has a description, could use sub-tests here;\\n  \\n    \\n      \\n        Suggested change\\n      \\n    \\n    \\n      \\n          \\n            \\n            \\tfor _, c := range cases {\\n          \\n          \\n            \\n            \\t\\tst, err := parseStat(c.input)\\n          \\n          \\n            \\n            \\t\\tt.Logf(\"case %q: error: %v, stat: %v\", c.desc, err, st)\\n          \\n          \\n            \\n            \\t\\tif err == nil {\\n          \\n          \\n            \\n            \\t\\t\\tt.Errorf(\"case %q, expected error, got nil\", c.desc)\\n          \\n          \\n            \\n            \\t\\t}\\n          \\n          \\n            \\n            \\tfor _, c := range cases {\\n          \\n          \\n            \\n            \\t\\tc := c\\n          \\n          \\n            \\n            \\t\\tt.Run(c.desc, func(t *testing.T) {\\n          \\n          \\n            \\n            \\t\\t\\tst, err := parseStat(c.input)\\n          \\n          \\n            \\n            \\t\\t\\tt.Logf(\"error: %v, stat: %v\", err, st)\\n          \\n          \\n            \\n            \\t\\t\\tif err == nil {\\n          \\n          \\n            \\n            \\t\\t\\t\\tt.Fatalf(\"expected error, got nil\", c.desc)\\n          \\n          \\n            \\n            \\t\\t\\t}\\n          \\n          \\n            \\n            \\t\\t})',\n", " 'bodyHTML': '<p dir=\"auto\">Given that each test has a description, could use sub-tests here;</p>\\n  <div class=\"my-2 border rounded-2 js-suggested-changes-blob diff-view js-check-bidi\" id=\"\">\\n    <div class=\"f6 p-2 lh-condensed border-bottom d-flex\">\\n      <div class=\"flex-auto flex-items-center color-fg-muted\">\\n        Suggested change\\n      </div>\\n    </div>\\n    <div itemprop=\"text\" class=\"blob-wrapper data file\" style=\"margin: 0; border: none; overflow-y: visible; overflow-x: auto;\">\\n      <table class=\"d-table tab-size mb-0 width-full\" data-paste-markdown-skip=\"\">\\n          <tbody><tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"114\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t<span class=\"pl-k\">for</span> <span class=\"pl-s1\">_</span>, <span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-k\">range</span> <span class=\"pl-s1\">cases</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"115\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-s1\">st</span>, <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-en\">parseStat</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">input</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"116\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Logf</span>(<span class=\"pl-s\">\"case %q: error: %v, stat: %v\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>, <span class=\"pl-s1\">err</span>, <span class=\"pl-s1\">st</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"117\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t<span class=\"pl-k\">if</span> <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">==</span> <span class=\"pl-c1\">nil</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"118\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Errorf</span>(<span class=\"pl-s\">\"case %q, expected error, got nil\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-deletion text-right border-0 px-2 py-1 lh-default\" data-line-number=\"119\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\">\\t\\t}</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"114\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t<span class=\"pl-k\">for</span> <span class=\"pl-s1\">_</span>, <span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-k\">range</span> <span class=\"pl-s1\">cases</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"115\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t<span class=\"pl-s1\">c</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-s1\">c</span></td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"116\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-c1\">Run</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>, <span class=\"pl-k\">func</span>(<span class=\"pl-s1\">t</span> <span class=\"pl-c1\">*</span>testing.<span class=\"pl-smi\">T</span>) {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"117\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-s1\">st</span>, <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">:=</span> <span class=\"pl-en\">parseStat</span>(<span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">input</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"118\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Logf</span>(<span class=\"pl-s\">\"error: %v, stat: %v\"</span>, <span class=\"pl-s1\">err</span>, <span class=\"pl-s1\">st</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"119\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t<span class=\"pl-k\">if</span> <span class=\"pl-s1\">err</span> <span class=\"pl-c1\">==</span> <span class=\"pl-c1\">nil</span> {</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"120\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t\\t<span class=\"pl-s1\">t</span>.<span class=\"pl-en\">Fatalf</span>(<span class=\"pl-s\">\"expected error, got nil\"</span>, <span class=\"pl-s1\">c</span>.<span class=\"pl-c1\">desc</span>)</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"121\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t\\t}</td>\\n          </tr>\\n          <tr class=\"border-0\">\\n            <td class=\"blob-num blob-num-addition text-right border-0 px-2 py-1 lh-default\" data-line-number=\"122\"></td>\\n            <td class=\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\">\\t\\t})</td>\\n          </tr>\\n      </tbody></table>\\n    </div>\\n    <div class=\"js-apply-changes\"></div>\\n  </div>\\n',\n", " 'body': 'Given that each test has a description, could use sub-tests here;\\r\\n\\r\\n```suggestion\\r\\n\\tfor _, c := range cases {\\r\\n\\t\\tc := c\\r\\n\\t\\tt.Run(c.desc, func(t *testing.T) {\\r\\n\\t\\t\\tst, err := parseStat(c.input)\\r\\n\\t\\t\\tt.Logf(\"error: %v, stat: %v\", err, st)\\r\\n\\t\\t\\tif err == nil {\\r\\n\\t\\t\\t\\tt.Fatalf(\"expected error, got nil\", c.desc)\\r\\n\\t\\t\\t}\\r\\n\\t\\t})\\r\\n```'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "import json\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\n", "    \"Authorization\": f\"token {TOKEN}\",\n", "    \"Content-Type\": \"application/json\",\n", "}\n", "\n", "@persistent_cache('/home/<USER>/data/url_graphql_cache_v1.jsonl')\n", "def query_graphql_comment(node_id):\n", "  # https://docs.github.com/en/graphql/overview/about-the-graphql-api\n", "  # Use https://docs.github.com/en/graphql/overview/explorer for experimenting with queries  \n", "  query = \"\"\"query {\n", "    node(id: \"%s\") {\n", "      ... on PullRequestReviewComment {\n", "        databaseId\n", "        bodyText\n", "        bodyHTML\n", "        body\n", "      }\n", "    }\n", "  }\"\"\" % node_id\n", "\n", "  response = requests.post(\"https://api.github.com/graphql\", headers=headers, data=json.dumps({\"query\": query}))\n", "  assert response.status_code == 200\n", "  return response.json()[\"data\"][\"node\"]\n", "\n", "\n", "query_graphql_comment(\"MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1MzI2MTQwNg==\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\tfor _, c := range cases {\n", "\t\tst, err := parseStat(c.input)\n", "\t\tt.Logf(\"case %q: error: %v, stat: %v\", c.desc, err, st)\n", "\t\tif err == nil {\n", "\t\t\t<PERSON><PERSON>(\"case %q, expected error, got nil\", c.desc)\n", "\t\t}\n", "\tfor _, c := range cases {\n", "\t\tc := c\n", "\t\tt.<PERSON>(c.desc, func(t *testing.T) {\n", "\t\t\tst, err := parseStat(c.input)\n", "\t\t\tt.Logf(\"error: %v, stat: %v\", err, st)\n", "\t\t\tif err == nil {\n", "\t\t\t\tt.<PERSON>(\"expected error, got nil\", c.desc)\n", "\t\t\t}\n", "\t\t})\n", "def test_collection_named_collections(\n", "@pytest.mark.parallel\n", "def test_collection_named_collections(\n"]}], "source": ["import re\n", "import html\n", "\n", "RE_TD_CLASS_OLD_CODE = r\"\\s*<td class=\\\"border-0 px-2 py-1 blob-code-inner blob-code-deletion js-blob-code-deletion blob-code-marker-deletion\\\">(.*)</td>\"\n", "RE_TD_CLASS_NEW_CODE = r\"\\s*<td class=\\\"border-0 px-2 py-1 blob-code-inner blob-code-addition js-blob-code-addition blob-code-marker-addition\\\">(.*)</td>\"\n", "RE_SPAN_TAG = r\"<span class=[^>]+>([^<]+)</span>\"\n", "\n", "\n", "def strip_span_tags(text):\n", "    new_text = re.sub(RE_SPAN_TAG, r\"\\1\", text)\n", "    while new_text != text:\n", "        text = new_text\n", "        new_text = re.sub(RE_SPAN_TAG, r\"\\1\", text)\n", "    return new_text\n", "\n", "\n", "def extract_code(bodyHTML, re_td_pattern):\n", "    html_lines = bodyHTML.splitlines()\n", "    extracted_code_lines = []\n", "    for line in html_lines:\n", "        if m := re.match(re_td_pattern, line):\n", "            extracted_line = m.group(1)\n", "            cleaned_extracted_line = html.unescape(strip_span_tags(extracted_line))\n", "            extracted_code_lines.append(cleaned_extracted_line)\n", "    return \"\\n\".join(extracted_code_lines).rstrip(\"\\n\")\n", "\n", "\n", "def extract_old_code(bodyHTML):\n", "    return extract_code(bodyHTML, RE_TD_CLASS_OLD_CODE)\n", "\n", "\n", "def extract_new_code(bodyHTML):\n", "    return extract_code(bodyHTML, RE_TD_CLASS_NEW_CODE)\n", "\n", "\n", "def extract_old_new_code(bodyHTML):    \n", "    return extract_old_code(bodyHTML), extract_new_code(bodyHTML)\n", "\n", "\n", "sample_comment = query_graphql_comment(\"MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1MzI2MTQwNg==\")\n", "extract_old_new_code(sample_comment['bodyHTML'])\n", "print(extract_old_code(sample_comment['bodyHTML']))\n", "print(extract_new_code(sample_comment['bodyHTML']))\n", "\n", "# https://github.com/pulp/pulp_ansible/pull/1376#discussion_r1122782900\n", "# https://api.github.com/repos/pulp/pulp_ansible/pulls/comments/1122782900\n", "sample_comment = query_graphql_comment(\"PRRC_kwDOBr-zhM5C7E60\")\n", "extract_old_new_code(sample_comment['bodyHTML'])\n", "print(extract_old_code(sample_comment['bodyHTML']))\n", "print(extract_new_code(sample_comment['bodyHTML']))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 106341 v1 examples\n"]}], "source": ["import json\n", "\n", "with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v1.json\") as f:\n", "    data_v1 = json.load(f)\n", "\n", "print('Loaded %d v1 examples' % len(data_v1))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def slice_code(code, start_line, end_line, assert_borders):\n", "    code_lines = code.splitlines()\n", "    if assert_borders:\n", "        assert start_line < len(code_lines)\n", "        assert end_line <= len(code_lines)\n", "    return \"\\n\".join(code_lines[start_line: end_line])\n", "\n", "\n", "def recompute_old_code(start_line, end_line, old_file, new_code, graphql_old_code, graphql_new_code):    \n", "    if graphql_new_code != new_code:\n", "        # print('NEW CODE')\n", "        # print(new_code)\n", "        # print('NEW CODE ACCORDING TO GRAPHQL')\n", "        # print(graphql_new_code)\n", "        return None\n", "        \n", "    assert graphql_new_code == new_code\n", "    old_code = slice_code(old_file, start_line, end_line, False)\n", "\n", "    if old_code == graphql_old_code:\n", "        return start_line, end_line\n", "\n", "    if old_code.startswith(graphql_old_code):\n", "        actual_number_of_lines = len(graphql_old_code.splitlines())\n", "        new_end_line = start_line + actual_number_of_lines\n", "        new_old_code = slice_code(old_file, start_line, new_end_line, True)\n", "        return start_line, new_end_line\n", "    else:\n", "        # print('OLD CODE')\n", "        # print(old_code)\n", "        # print('OLD CODE ACCORDING TO GRAPHQL')\n", "        # print(graphql_old_code)\n", "        return None\n", "\n", "def get_data_v1_sample_given_id(sample_id):\n", "    samples = [sample for sample in data_v1 if sample[\"id\"] == sample_id]\n", "    assert len(samples) == 1\n", "    return samples[0]\n", "\n", "sample_553261406 = get_data_v1_sample_given_id(553261406)\n", "graphql_info_553261406 = query_graphql_comment(sample_553261406[\"full_comment_info\"][\"node_id\"])\n", "graphql_old_code_553261406, graphql_new_code_553261406 =  extract_old_new_code(graphql_info_553261406[\"bodyHTML\"])\n", "\n", "assert recompute_old_code(\n", "    start_line=sample_553261406['start_line'] - 1,\n", "    end_line=sample_553261406['end_line'],\n", "    old_file=sample_553261406['old_file'],\n", "    new_code=\"\\n\".join(sample_553261406['new_code'].splitlines()),\n", "    graphql_old_code=graphql_old_code_553261406,\n", "    graphql_new_code=graphql_new_code_553261406,\n", ") is not None\n", "\n", "sample_1122782900 = get_data_v1_sample_given_id(1122782900)\n", "graphql_info_1122782900 = query_graphql_comment(sample_1122782900[\"full_comment_info\"][\"node_id\"])\n", "graphql_old_code1122782900, graphql_new_code1122782900 =  extract_old_new_code(graphql_info_1122782900[\"bodyHTML\"])\n", "\n", "assert recompute_old_code(\n", "    start_line=sample_1122782900['start_line'] - 1,\n", "    end_line=sample_1122782900['end_line'],\n", "    old_file=sample_1122782900['old_file'],\n", "    new_code=\"\\n\".join(sample_1122782900['new_code'].splitlines()),\n", "    graphql_old_code=graphql_old_code1122782900,\n", "    graphql_new_code=graphql_new_code1122782900,\n", ") is None\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import requests\n", "import base64\n", "\n", "\n", "TOKEN = \"****************************************\"  # Replace with your GitHub personal access token\n", "# Headers for authentication\n", "headers = {\"Authorization\": f\"token {TOKEN}\"}\n", "\n", "\n", "@persistent_cache('/home/<USER>/data/url_cache.jsonl')\n", "def download(url):    \n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    elif response.status_code == 404:\n", "        return None\n", "    else:\n", "        raise ValueError(f\"Failed to download {url}: {response.status_code}\")\n", "\n", "\n", "def download_file(repo_owner, repo_name, file_path, commit):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{file_path}?ref={commit}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")\n", "    try:\n", "        if result[\"encoding\"] != \"base64\":\n", "            raise ValueError(f\"Failed to download {url}: {result['encoding']} != base64\")    \n", "    except TypeError:\n", "        raise ValueError(f\"Failed to download {url}: problems with encoding in the result {result}\")\n", "    result[\"decoded_content\"] = base64.b64decode(result[\"content\"]).decode(\"utf8\")\n", "    return result\n", "\n", "\n", "def download_review_comment(repo_owner, repo_name, review_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/comments/{review_id}\"\n", "    result = download(url)\n", "    if result is None:\n", "        raise ValueError(f\"Failed to download {url}: 404\")    \n", "    return result\n", "\n", "\n", "def download_commit_ids_for_pr(repo_owner, repo_name, pr_id):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_id}/commits\"\n", "    return download(url)\n", "\n", "\n", "def download_commit(repo_owner, repo_name, commit_sha):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/commits/{commit_sha}\"\n", "    return download(url)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It might be best to check if the type is uuid.UUID instead of blindly casting to str\n", "\n", "```suggestion\n", "        if isinstance(field.data, uuid.UUID):\n", "            return\n", "\n", "        try:\n", "            uuid.UUID(field.data)\n", "        except ValueError:\n", "            raise ValidationError(message)\n", "```\n", "{'original_instruction': 'It might be best to check if the type is uuid.UUID instead of blindly casting to str', 'new_code': '        if isinstance(field.data, uuid.UUID):\\n            return\\n\\n        try:\\n            uuid.UUID(field.data)\\n        except ValueError:\\n            raise ValidationError(message)'}\n", "```suggestion\n", "        uuid.UUID(\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\"),\n", "```\n", "\n", "RNG in tests is bad!'\n", "{'original_instruction': \"RNG in tests is bad!'\", 'new_code': '        uuid.UUID(\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\"),'}\n"]}], "source": ["RE_SUGGESTION_PATTERN = r\"```suggestion\\r\\n([^`]*?)\\r\\n```\"\n", "\n", "\n", "def parse_single_suggestion(text: str):\n", "    comments, suggestions = [], []\n", "    while (m := re.search(RE_SUGGESTION_PATTERN, text)) is not None:\n", "        comment = text[:m.start()].strip()\n", "        if len(comment) > 0:\n", "            comments.append(comment)\n", "        text = text[m.end():].strip()\n", "        suggestions.append(m.group(1))\n", "        \n", "    text = text.strip()\n", "    if len(text) > 0:\n", "        comments.append(text)\n", "\n", "    if len(comments) == 1 and len(suggestions) == 1:\n", "        return {\n", "            \"original_instruction\": comments[0],\n", "            # We normalize new lines for the new_code to remove \\r symbols\n", "            \"new_code\": \"\\n\".join(suggestions[0].splitlines()),\n", "        }\n", "    else:\n", "        return None\n", "\n", "\n", "sample_1 = \"It might be best to check if the type is uuid.UUID instead of blindly casting to str\\r\\n\\r\\n```suggestion\\r\\n        if isinstance(field.data, uuid.UUID):\\r\\n            return\\r\\n\\r\\n        try:\\r\\n            uuid.UUID(field.data)\\r\\n        except ValueError:\\r\\n            raise ValidationError(message)\\r\\n```\"\n", "print(sample_1)\n", "print(parse_single_suggestion(sample_1))\n", "\n", "sample_2 = \"```suggestion\\r\\n        uuid.UUID(\\\"2bc1c94f-0deb-43e9-92a1-4775189ec9f8\\\"),\\r\\n```\\r\\n\\r\\nRNG in tests is bad!'\"\n", "print(sample_2)\n", "print(parse_single_suggestion(sample_2))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 21178 repos, 4418482 reviews and 78022 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Python.json\n", "Loaded 10244 repos, 2712741 reviews and 21619 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Java.json\n", "Loaded 28835 repos, 1136610 reviews and 16081 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json\n", "Loaded 16319 repos, 1759265 reviews and 37798 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json\n", "Loaded 8643 repos, 3141655 reviews and 46714 suggested code edits from /home/<USER>/data/mined-comments-25stars-25prs-Go.json\n", "TOTAL Loaded 85219 repos, 13168753 reviews and 200234 suggested code edits from\n", "2833\n"]}], "source": ["# These files are located at '/mnt/efs/augment/user/yury/pr_data/\n", "PATHS = [\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Python.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Java.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-JavaScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-TypeScript.json',\n", "    '/home/<USER>/data/mined-comments-25stars-25prs-Go.json',\n", "]\n", "\n", "parsed_suggested_edits = []\n", "n_total_repos, n_total_reviews = 0, 0\n", "n_skip_cannot_parse_pr_id = 0\n", "\n", "for path in PATHS:\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "\n", "    n_reviews, n_suggestions = 0, 0\n", "\n", "    for repo_owner_name, repo in data.items():\n", "        repo_owner, repo_name = repo_owner_name.split('/')\n", "        for review in repo:\n", "            n_reviews += 1\n", "            parsed_suggested_edit = parse_single_suggestion(review['body'])\n", "            if parsed_suggested_edit is None:\n", "                continue\n", "            parsed_suggested_edit.update({\n", "                \"original_review_file\": path,\n", "                \"repo_owner_name\": repo_owner_name,\n", "                \"repo_owner\": repo_owner,\n", "                \"repo_name\": repo_name,\n", "            })            \n", "\n", "            re_extract_pr_from_html_url = f\"https://github.com/{repo_owner}/{repo_name}/pull/([0-9]+)#discussion_r{review['id']}\"\n", "            m = re.match(re_extract_pr_from_html_url, review[\"html_url\"])\n", "            if m is None:\n", "                # That usually means that there is a mismatch between repo_owner in the data and html_url\n", "                n_skip_cannot_parse_pr_id += 1\n", "                continue\n", "            parsed_suggested_edit[\"pr_id\"] = int(m.group(1))\n", "\n", "            n_suggestions += 1\n", "            parsed_suggested_edit.update(review)\n", "            parsed_suggested_edits.append(parsed_suggested_edit)            \n", "            \n", "    n_total_repos += len(data)\n", "    n_total_reviews += n_reviews\n", "    print('Loaded %d repos, %d reviews and %d suggested code edits from %s' % (len(data), n_reviews, n_suggestions, path))\n", "\n", "print('TOTAL Loaded %d repos, %d reviews and %d suggested code edits from' % (n_total_repos, n_total_reviews, len(parsed_suggested_edits)))\n", "print(n_skip_cannot_parse_pr_id)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["urls:  True\n"]}], "source": ["import re\n", "\n", "RE_URL = re.compile(\"https?:\\\\/\\\\/(?:www\\\\.)?[-a-zA-Z0-9@:%._\\\\+~#=]{1,256}\\\\.[a-zA-Z0-9()]{1,6}\\\\b(?:[-a-zA-Z0-9()@:%_\\\\+.~#?&\\\\/=]*)\")\n", "  \n", "def does_text_contain_url(text):\n", "    m = re.search(RE_URL, text)\n", "    return m is not None\n", " \n", " \n", "# Driver Code\n", "text = 'My Profile: https://auth.geeksforgeeks.org/user/Chinmoy%20Lenka/articles in the portal of https://www.geeksforgeeks.org/'\n", "print(\"urls: \", does_text_contain_url(text))"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "False\n", "False\n"]}], "source": ["import requests\n", "\n", "BAD_COMMIT_MESSAGE = \"This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository\"\n", "\n", "@persistent_cache('/home/<USER>/data/curl_github_webpage.jsonl')\n", "def download_raw_text(url):\n", "    response = requests.get(url)\n", "    if response.status_code == 200:\n", "        return response.content.decode('utf8')\n", "    raise ValueError(f\"Failed to download {url}: {response.status_code}\")\n", "    \n", "def does_commit_exists_in_tree(repo_owner, repo_name, commit_sha):\n", "    url = f\"https://github.com/{repo_owner}/{repo_name}/commit/{commit_sha}\"\n", "    webpage = download_raw_text(url)\n", "    return BAD_COMMIT_MESSAGE not in webpage\n", "\n", "print(does_commit_exists_in_tree('feast-dev', 'feast', '6a99cd9e07a98559f7db2d5cddaf64994c480cb4'))\n", "print(does_commit_exists_in_tree('adazzle', 'react-data-grid', '00f17e08b0552845d8915700935a26024336399d'))\n", "print(does_commit_exists_in_tree('matrix-org', 'complement', '2c71289492e6cf719c879492f28066043e0e46c1'))"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/200234 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 297/200234 [00:00<08:58, 371.48it/s]"]}], "source": ["import tqdm \n", "import editdistance # pip install editdistance\n", "from urlextract import URLExtract # pip install urlextract\n", "\n", "MAX_WORDS_IN_COMMENT = 25\n", "MIN_EDIT_DISTANCE = 8\n", "\n", "filtered_suggested_edit = []\n", "\n", "extractor = URLExtract()\n", "\n", "\n", "n_too_many_words = 0\n", "n_original_line_is_missing = 0\n", "n_failed_download_commits = 0\n", "n_instruction_too_vague = 0\n", "n_cannot_download_comment_info_restapi = 0\n", "n_failed_to_fix_old_code, n_fixed_old_code = 0, 0\n", "n_start_line_is_broken = 0\n", "n_failed_to_fix_old_code_again = 0\n", "n_too_small_edit_distance = 0\n", "n_no_relevant_urls = 0\n", "n_original_commit_not_in_commits = 0\n", "n_commits_in_pr_is_none = 0\n", "n_original_commit_not_in_tree = 0\n", "\n", "def download_file_info(repo_owner, repo_name, path, commit_sha, start_line, end_line):\n", "    try:\n", "        file = download_file(\n", "            repo_owner, repo_name, path, commit_sha)\n", "        return {\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,\n", "            \"commit_sha\": commit_sha,\n", "            \"file_info\": file,\n", "            \"file\": file['decoded_content'],\n", "        }   \n", "    except ValueError:\n", "        return None    \n", "    \n", "\n", "KEYS_TO_COPY = [\n", "\t\"original_instruction\",\n", "\t\"new_code\",\n", "\t\"original_review_file\",\n", "\t\"repo_owner_name\",\n", "\t\"repo_owner\",\n", "\t\"repo_name\",\n", "\t\"html_url\",\n", "\t\"path\",\n", "\t\"id\",\n", "    \"pr_id\",\n", "]\n", "\n", "with tqdm.tqdm(total=len(parsed_suggested_edits)) as pbar:    \n", "    for parsed_suggested_edit in parsed_suggested_edits:\n", "        pbar.update(1)\n", "\n", "        suggested_edit = {}\n", "        for k in KEYS_TO_COPY:\n", "            suggested_edit[k] = parsed_suggested_edit[k]\n", "\n", "        original_instruction = suggested_edit['original_instruction']\n", "        if len(original_instruction.split()) > MAX_WORDS_IN_COMMENT:\n", "            n_too_many_words += 1\n", "            continue\n", "\n", "        urls = extractor.find_urls(original_instruction)\n", "        relevant_urls = [url for url in urls if suggested_edit['repo_owner_name'] in url]\n", "\n", "        if len(relevant_urls) == 0:\n", "            n_no_relevant_urls += 1\n", "            continue\n", "\n", "        try:\n", "            full_comment_info = download_review_comment(\n", "                suggested_edit['repo_owner'],\n", "                suggested_edit['repo_name'],\n", "                suggested_edit['id'],\n", "            )\n", "            suggested_edit[\"full_comment_info\"] = full_comment_info\n", "        except ValueError:\n", "            n_cannot_download_comment_info_restapi += 1\n", "            continue        \n", "\n", "        end_line = full_comment_info['original_line']\n", "        if end_line is None:\n", "            n_original_line_is_missing += 1\n", "            continue\n", "        start_line = (full_comment_info['original_start_line'] or end_line) - 1\n", "        assert end_line is not None\n", "        assert start_line is not None\n", "\n", "        graphql_comment_info = query_graphql_comment(suggested_edit[\"full_comment_info\"][\"node_id\"])\n", "        suggested_edit[\"graphql_comment_info\"] = graphql_comment_info\n", "        graphql_old_code, graphql_new_code =  extract_old_new_code(graphql_comment_info[\"bodyHTML\"])\n", "        \n", "        original_commit_info = download_file_info(\n", "            commit_sha=full_comment_info['original_commit_id'],\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            repo_owner=suggested_edit['repo_owner'],\n", "            repo_name=suggested_edit['repo_name'],\n", "            path=suggested_edit['path'],\n", "        )     \n", "        if original_commit_info is None:\n", "            n_failed_download_commits += 1\n", "            continue\n", "\n", "        suggested_edit[\"original_commit\"] = original_commit_info        \n", "        old_file = suggested_edit['original_commit']['file']\n", "\n", "        adjusted_start_end_line = recompute_old_code(\n", "            start_line=start_line,\n", "            end_line=end_line,\n", "            old_file=old_file,\n", "            new_code=suggested_edit['new_code'],\n", "            graphql_old_code=graphql_old_code,\n", "            graphql_new_code=graphql_new_code,\n", "        )\n", "        if adjusted_start_end_line is None:\n", "            n_failed_to_fix_old_code += 1\n", "            continue\n", "\n", "        assert adjusted_start_end_line is not None\n", "        if adjusted_start_end_line[0] != start_line or adjusted_start_end_line[1] != end_line:\n", "            n_fixed_old_code += 1\n", "            start_line, end_line = adjusted_start_end_line\n", "\n", "        old_file_lines = old_file.splitlines()\n", "\n", "        if start_line >= len(old_file_lines):\n", "            n_start_line_is_broken += 1\n", "            continue\n", "            \n", "        end_line = min(end_line, len(old_file_lines))\n", "        prefix = '\\n'.join(old_file_lines[:start_line])\n", "        suffix = '\\n'.join(old_file_lines[end_line: ])        \n", "        # old_code = '\\n'.join(old_file_lines[start_line: end_line])        \n", "        old_code = slice_code(old_file, start_line, end_line, True)\n", "\n", "        if graphql_old_code != old_code:\n", "            n_failed_to_fix_old_code_again += 1\n", "            continue        \n", "\n", "        assert graphql_old_code == old_code\n", "        assert graphql_new_code == suggested_edit['new_code']\n", "\n", "        \n", "        old_new_code_edit_distance = editdistance.eval(suggested_edit['new_code'], old_code)\n", "        if old_new_code_edit_distance < MIN_EDIT_DISTANCE:\n", "            n_too_small_edit_distance += 1\n", "            continue\n", "\n", "        if not does_commit_exists_in_tree(\n", "            suggested_edit['repo_owner'],\n", "            suggested_edit['repo_name'],\n", "            suggested_edit['original_commit']['commit_sha'],\n", "        ):\n", "            n_original_commit_not_in_tree += 1\n", "            continue\n", "\n", "        # commits_in_pr = download_commit_ids_for_pr(\n", "        #     repo_owner=suggested_edit['repo_owner'],\n", "        #     repo_name=suggested_edit['repo_name'],            \n", "        #     pr_id=suggested_edit['pr_id'])\n", "        \n", "        # if commits_in_pr is None:\n", "        #     n_commits_in_pr_is_none += 1\n", "        #     continue\n", "        \n", "        # commits_sha = [c['sha'] for c in commits_in_pr]\n", "        # if suggested_edit['original_commit']['commit_sha'] not in commits_sha:\n", "        #     n_original_commit_not_in_commits += 1\n", "        #     continue\n", "\n", "        suggested_edit.update({\n", "            \"edit_distance\": old_new_code_edit_distance,\n", "            \"start_line\": start_line,\n", "            \"end_line\": end_line,            \n", "            'old_file': old_file,\n", "            'old_code': slice_code(old_file, start_line, end_line, False),\n", "            'prefix': prefix,\n", "            'suffix': suffix,\n", "            # 'commits_in_pr': commits_in_pr,\n", "        })\n", "\n", "        filtered_suggested_edit.append(suggested_edit)\n", "        pbar.set_postfix({\"mined_suggestions\": len(filtered_suggested_edit), \"n_fixed_old_code\": n_fixed_old_code, \"n_failed_to_fix_old_code_all\": n_failed_to_fix_old_code + n_start_line_is_broken + n_failed_to_fix_old_code_again})\n", "\n", "print('-- removed %d because comment had too many words' % n_too_many_words)\n", "print('-- removed %d because comment does not containt relevant URLs' % n_no_relevant_urls)\n", "print('-- removed %d because cannot download comment info from REST API' % n_cannot_download_comment_info_restapi)\n", "print('-- removed %d because lines are missing' % n_original_line_is_missing)\n", "print('-- removed %d because failed to download commits' % n_failed_download_commits)\n", "print('-- removed %d because failed to fix old code given GraphQL result' % n_failed_to_fix_old_code)\n", "print('-- removed %d because start_line is outside of the file' % n_start_line_is_broken)\n", "print('-- removed %d because failed to fix old code given GraphQL result (nevertheless)' % n_failed_to_fix_old_code_again)\n", "print('-- removed %d because edit distance between old and new code is too small' % n_too_small_edit_distance)\n", "print('-- removed %d because commits in PR is None' % n_commits_in_pr_is_none)\n", "print('-- removed %d because original commit is not amongst commits' % n_original_commit_not_in_commits)\n", "print('-- removed %d because original commit is in the git tree' % n_original_commit_not_in_tree)\n", "\n", "print('-- recovered %d corrupted files by comparing with GraphQL result' % n_fixed_old_code)\n", "\n", "print('Final suggestions: %d' % len(filtered_suggested_edit))"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_suggested_edit)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v2.json\", \"w\") as f:\n", "#     json.dump(filtered_suggested_edit, f)\n", "\n", "# with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v2.json\") as f:\n", "#     filtered_suggested_edit = json.load(f)\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  REVIEW URL: https://github.com/matrix-org/complement/pull/496#discussion_r996210417\n", "                      COMMIT: https://github.com/matrix-org/complement/commit/2c71289492e6cf719c879492f28066043e0e46c1\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: (same for `secondEventID`)\n", "\n", "Related to https://github.com/matrix-org/complement/pull/496#discussion_r996208469\n", "\n", "\tbob.<PERSON><PERSON>(t, roomID, nil)\n", "\n", "\ttoken := bob.MustSyncUntil(t, client.SyncReq{}, client.SyncJoinedTo(bob.UserID, roomID))\n", "\n", "\t// Send messages as alice and then check the highlight and notification counts from bob.\n", "-\tfirstEventID := alice.SendEventSynced(t, roomID, b.Event{\n", "+\tfirstHighlightForBobEventID := alice.SendEventSynced(t, roomID, b.Event{\n", "\t\tType: \"m.room.message\",\n", "\t\tContent: map[string]interface{}{\n", "\t\t\t\"msgtype\": \"m.text\",\n", "\t\t\t\"body\":    \"Hello world!\",\n", "\t\t},\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/project-koku/koku/pull/1845#discussion_r387901041\n", "                      COMMIT: https://github.com/project-koku/koku/commit/cdfe3c13f38e4a23f3d97be6eea0a26862cd182b\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Maybe we should be using this:\n", "https://github.com/project-koku/koku/blob/b3d4ff79d24df9182c2e2a752344e3d606a5dd12/koku/api/utils.py#L34\n", "\n", "        assembly_ids = db_accessor.get_last_seen_manifest_ids(month)\n", "        assembly_ids_to_exclude.extend(assembly_ids)\n", "    # now we want to loop through the files and clean up the ones that are not in the exclude list\n", "    deleted_files = []\n", "    retain_files = []\n", "-    now = datetime.now()\n", "+    datehelper = DateHelper()\n", "+    now = datehelper.now()\n", "    expiration_date = now - timedelta(seconds=Config.VOLUME_FILE_RETENTION)\n", "    for [root, _, filenames] in os.walk(Config.PVC_DIR):\n", "        for file in filenames:\n", "            match = False\n", "            for assembly_id in assembly_ids_to_exclude:\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/fluxcd/source-controller/pull/727#discussion_r882416395\n", "                      COMMIT: https://github.com/fluxcd/source-controller/commit/3d4d84cebb18d2340eef85b4fe24711ffc07d706\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Wonder whether we should lean on the existing type for transports:\n", "https://github.com/fluxcd/source-controller/blob/main/pkg/git/options.go#L57\n", "\n", "\tcc, err = checkoutTag.Checkout(ctx, tmpDir, repoURL, authOpts)\n", "\tg.<PERSON>pect(err).<PERSON><PERSON><PERSON>(HaveOccurred())\n", "\tg.Expect(cc.String()).To(Equal(\"tag-1\" + \"/\" + commit.Id().String()))\n", "\tg.Expect(git.IsConcreteCommit(*cc)).To(Equal(false))\n", "}\n", "-func getTransportOptionsURL(transport string) string {\n", "+func getTransportOptionsURL(transport git.TransportType) string {\n", "\tletterRunes := []rune(\"abcdefghijklmnopqrstuvwxyz1234567890\")\n", "\tb := make([]rune, 10)\n", "\tfor i := range b {\n", "\t\tb[i] = letterRunes[rand.Intn(len(letterRunes))]\n", "\t}\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/beetbox/beets/pull/3084#discussion_r237183122\n", "                      COMMIT: https://github.com/beetbox/beets/commit/196b581d0bf1b59fc4b36f2d428aea0f271386c4\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: [This line exceeds our line length limit of 79 characters](https://travis-ci.org/beetbox/beets/jobs/460820041#L1086), I'd suggest moving the comment onto the preceding line;\n", "\n", "            data_collector = tag_data\n", "\n", "        included_keys = []\n", "        for keys in opts.included_keys:\n", "            included_keys.extend(keys.split(','))\n", "-        included_keys = [k for k in included_keys if k != 'path'] # Drop path even if user provides it multiple times\n", "+        # Drop path even if user provides it multiple times\n", "+        included_keys = [k for k in included_keys if k != 'path']\n", "        key_filter = make_key_filter(included_keys)\n", "\n", "        first = True\n", "        summary = {}\n", "        for data_emitter in data_collector(lib, ui.decargs(args)):\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/thanos-community/promql-engine/pull/69#discussion_r989677501\n", "                      COMMIT: https://github.com/thanos-community/promql-engine/commit/d858406044f7be28042d7ab587c806c6e634f685\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Maybe this should be `unaryNegation` as per this [comment](https://github.com/thanos-community/promql-engine/pull/62#discussion_r989283683)?\n", "\n", "\tstepsBatch int\n", "\tworkers    worker.Group\n", "}\n", "\n", "func (u *unaryNegation) Explain() (me string, next []model.VectorOperator) {\n", "-\treturn \"[*unary]\", []model.VectorOperator{u.next}\n", "+\treturn \"[*unaryNegation]\", []model.VectorOperator{u.next}\n", "}\n", "\n", "func NewUnaryNegation(\n", "\tnext model.VectorOperator,\n", "\tstepsBatch int,\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/MobilityData/gtfs-validator/pull/1149#discussion_r883726666\n", "                      COMMIT: https://github.com/MobilityData/gtfs-validator/commit/1ecfcd24cb1376e3cd870cbdc9973e2930cdc74e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: My bad, there was an error in my previous suggestion. It makes the Task :aggregateJavadoc to fail [here](https://github.com/MobilityData/gtfs-validator/runs/6619597119?check_suite_focus=true) and [here](https://github.com/MobilityData/gtfs-validator/runs/6619597193?check_suite_focus=true).\n", "\n", " *\n", " * <p>Example.\n", " *\n", " * <pre>\n", " *   {@literal @}GtfsTable(value = \"feed_info.txt\", singleRow = true) {\n", "- *   @Recommended\n", "+ *   {@literal @}Recommended\n", " *   public interface GtfsFeedInfoSchema extends GtfsEntity {\n", " *     ...\n", " *   }\n", " * </pre>\n", " */\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/OpenZeppelin/nile/pull/207#discussion_r980238830\n", "                      COMMIT: https://github.com/OpenZeppelin/nile/commit/87c8ff96d170368603c947e4a417c8ddbabca281\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: See https://github.com/OpenZeppelin/nile/pull/207#discussion_r980258619\n", "\n", "from nile.common import ABIS_DIRECTORY, BUILD_DIRECTORY\n", "\n", "try:\n", "    from starkware.crypto.signature.fast_pedersen_hash import pedersen_hash\n", "    from starkware.starknet.business_logic.execution.objects import Event\n", "-    from starkware.starknet.compiler.compile import compile_starknet_files\n", "+    from starkware.starknet.services.api.contract_class import ContractClass\n", "    from starkware.starknet.core.os.class_hash import compute_class_hash\n", "    from starkware.starknet.public.abi import get_selector_from_name\n", "    from starkware.starkware_utils.error_handling import StarkException\n", "except BaseException:\n", "    pass\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/operator-framework/operator-sdk/pull/1689#discussion_r305100012\n", "                      COMMIT: https://github.com/operator-framework/operator-sdk/commit/089fe3c0154732d5de0ef4fc5db57d88100d292e\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Nit: Move Line 257 to 261\n", "https://github.com/operator-framework/operator-sdk/blob/089fe3c0154732d5de0ef4fc5db57d88100d292e/internal/pkg/scaffold/olm-catalog/csv_updaters.go#L257-L261\n", "\n", "\tfor _, ver := range versions {\n", "\t\tkind := crd.Spec.Names.Kind\n", "\t\tcrdDesc := olmapiv1alpha1.CRDDescription{\n", "\t\t\tName:    crd.ObjectMeta.Name,\n", "\t\t\tVersion: ver,\n", "-\t\t\tKind:    kind,\n", "+\t\t\tKind:    crd.Spec.Names.Kind,\n", "\t\t}\n", "\t\tstore.crds.crIDs[crdDescID(crdDesc)] = struct{}{}\n", "\t\tstore.crds.Owned = append(store.crds.Owned, crdDesc)\n", "\t}\n", "\treturn nil\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/open-mmlab/mmyolo/pull/544#discussion_r1104345218\n", "                      COMMIT: https://github.com/open-mmlab/mmyolo/commit/4eb657c57a0f20114b2848826fb9000d0b060477\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: The config of RTMDet was updated in https://github.com/open-mmlab/mmyolo/pull/531.\n", "\n", "        update_buffers=True,\n", "        strict_load=False,\n", "        priority=49),\n", "    dict(\n", "        type='mmdet.PipelineSwitchHook',\n", "-        switch_epoch=_base_.max_epochs - _base_.stage2_num_epochs,\n", "+        switch_epoch=_base_.max_epochs - _base_.num_epochs_stage2,\n", "        switch_pipeline=_base_.train_pipeline_stage2),\n", "    dict(type='mmrazor.DistillationLossDetachHook', detach_epoch=280)\n", "]\n", "\n", "====================================================================================================\n", "                  REVIEW URL: https://github.com/sematic-ai/sematic/pull/402#discussion_r1050451819\n", "                      COMMIT: https://github.com/sematic-ai/sematic/commit/b0fc21b00beeeb9f503c47c84f0bdd72f9e0d628\n", "MANUALLY REVISED INSTRUCTION: \n", "        ORIGINAL INSTRUCTION: Doesn't this require the treatment described [here](https://github.com/sematic-ai/sematic/pull/402#discussion_r1049861833)?\n", "\n", "    def assert_supported(type_):\n", "        if type_ is Any:\n", "            raise TypeError(\n", "                \"'Any' is not a Sematic-supported type. Use 'object' instead.\"\n", "            )\n", "-        if _is_type(type_):\n", "+        if _is_type(type_) and is_dataclass(type_)::\n", "            try:\n", "                # Sematic\n", "                from sematic.external_resource import ExternalResource\n", "\n", "                if issubclass(type_, ExternalResource):\n", "\n", "====================================================================================================\n"]}], "source": ["import random\n", "\n", "random.seed(31415)\n", "\n", "for index in random.sample(range(len(filtered_suggested_edit)), 10):\n", "    suggestion = filtered_suggested_edit[index]\n", "    commit_url = 'https://github.com/%s/commit/%s' % (suggestion['repo_owner_name'], suggestion['original_commit']['commit_sha'])\n", "    print('                  REVIEW URL:', suggestion['html_url'])    \n", "    print('                      COMMIT:', commit_url)    \n", "    print('MANUAL<PERSON>Y REVISED INSTRUCTION: ')\n", "    print('        ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print()\n", "    prefix = suggestion['prefix'].splitlines()[-5:]\n", "    suffix = suggestion['suffix'].splitlines()[:5]\n", "    print('\\n'.join(prefix))\n", "    print('\\n'.join(['-' + line for line in suggestion['old_code'].splitlines()]))\n", "    print('\\n'.join(['+' + line for line in suggestion['new_code'].splitlines()]))\n", "    print('\\n'.join(suffix))\n", "    print()    \n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["634"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_suggested_edit)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["filtered_suggested_edit_by_url = {sample['html_url']: sample for sample in filtered_suggested_edit}"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- <PERSON><PERSON><PERSON>/Compiler/Parsing.py\n", "+++ <PERSON><PERSON><PERSON>/Compiler/Parsing.py\n", "@@ -3405,12 +3405,13 @@\n", "             body = suite,\n", "             doc = doc,\n", "             modifiers = modifiers,\n", "             api = ctx.api,\n", "             overridable = ctx.overridable,\n", "-            is_const_method = is_const_method,\n", "-            return_type_annotation=return_type_annotation)\n", "+            is_const_method=is_const_method,\n", "+            return_type_annotation=return_type_annotation,\n", "+        )\n", "     else:\n", "         #if api:\n", "         #    s.error(\"'api' not allowed with variable declaration\")\n", "         if is_const_method:\n", "             declarator.is_const_method = is_const_method\n"]}], "source": ["import difflib\n", "\n", "def get_diff(sample, n_context=3):\n", "    # prefix = sample['prefix'].splitlines()[-5:]\n", "    # suffix = sample['suffix'].splitlines()[:5]\n", "    prefix = sample['prefix'].splitlines()\n", "    suffix = sample['suffix'].splitlines()\n", "    old_code = prefix + sample['old_code'].splitlines() + suffix\n", "    new_code = prefix + sample['new_code'].splitlines() + suffix\n", "\n", "    diff = '\\n'.join(difflib.unified_diff(old_code, new_code, fromfile=sample['path'], tofile=sample['path'], lineterm='', n=n_context))\n", "    return diff\n", "\n", "print(get_diff(filtered_suggested_edit_by_url['https://github.com/cython/cython/pull/4759#discussion_r934272129'], n_context=5))"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["# Iteration #1\n", "# https://gist.github.com/urikz/67ab79a25e1608aa7be9d01ecfd667b5\n", "\n", "# Iteration #2\n", "# with DeepSeek-Instruct https://gist.github.com/urikz/db652a3fe29fd4be85f21971dd37faae\n", "# with DeepSeek-Base https://gist.github.com/urikz/8fe86bc675a181b9c6d739fcce367d22\n", "\n", "UNCLEAR = \"UNCLEAR\"\n", "\n", "MANUAL_LABELS = {    \n", "    \"https://github.com/jenkinsci/jenkins-test-harness/pull/465#discussion_r935642731\": UNCLEAR,\n", "    \"https://github.com/Neuraxio/Neuraxle/pull/309#discussion_r424270695\": \"put separator after args and use constant\",\n", "    \"https://github.com/webex/webex-js-sdk/pull/1809#discussion_r462650960\": UNCLEAR,\n", "    \"https://github.com/carloscuesta/gitmoji-cli/pull/582#discussion_r622434992\": \"add type\",\n", "    \"https://github.com/DKU-STUDY/Algorithm/pull/337#discussion_r464035670\": UNCLEAR,\n", "    \"https://github.com/pytorch/vision/pull/5264#discussion_r790429498\": \"improve error message\",\n", "    \"https://github.com/aleph-im/pyaleph/pull/248#discussion_r864943892\": \"use single number with underscores\",\n", "    \"https://github.com/OSGeo/grass-addons/pull/222#discussion_r446030744\": \"add module descrption\",\n", "    \"https://github.com/elastic/apm-agent-nodejs/pull/2904#discussion_r960090284\": \"make it warning\",\n", "    \"https://github.com/reframe-hpc/reframe/pull/1925#discussion_r614224321\": UNCLEAR,\n", "    \"https://github.com/bridgecrewio/checkov/pull/4427#discussion_r1099838181\": \"use empty string as default\",\n", "    \"https://github.com/apache/flink-training/pull/31#discussion_r697212832\": UNCLEAR,\n", "    \"https://github.com/apolloconfig/apollo/pull/4189#discussion_r777299171\": UNCLEAR,\n", "    \"https://github.com/greenplum-db/pxf/pull/703#discussion_r728486742\": \"rewrite in one line\",\n", "    \"https://github.com/IntellectualSites/FastAsyncWorldEdit/pull/1947#discussion_r972105824\": UNCLEAR,\n", "    \"https://github.com/sveltejs/kit/pull/6936#discussion_r976603418\": UNCLEAR,\n", "    \"https://github.com/umijs/umi/pull/4247#discussion_r396461436\": UNCLEAR,\n", "    \"https://github.com/aws/aws-toolkit-vscode/pull/1764#discussion_r636532097\": \"don't initialize cfnTemplatePath\",\n", "    \"https://github.com/netbox-community/netbox/pull/9554#discussion_r905098669\": \"update time via update method\",\n", "    \"https://github.com/awslabs/multi-model-server/pull/689#discussion_r235183954\": \"use String.valueOf\",\n", "    \"https://github.com/uclapi/uclapi/pull/3052#discussion_r546840898\": UNCLEAR,    \n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Iteration #1\n", "# https://gist.github.com/urikz/67ab79a25e1608aa7be9d01ecfd667b5\n", "\n", "# Iteration #2\n", "# with DeepSeek-Instruct https://gist.github.com/urikz/db652a3fe29fd4be85f21971dd37faae\n", "# with DeepSeek-Base https://gist.github.com/urikz/8fe86bc675a181b9c6d739fcce367d22\n", "\n", "UNCLEAR = \"UNCLEAR\"\n", "GOOD = \"GOOD\"\n", "UNRELATED_CHANGES = \"UNRELATED CHANGES\"\n", "\n", "MANUAL_TRIPLE_LABELS = {    \n", "    \"https://github.com/Neuraxio/Neuraxle/pull/309#discussion_r424270695\": (\n", "        \"put separator after *args and use constant for the default value\",\n", "        \"separator after args with default constant\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/carloscuesta/gitmoji-cli/pull/582#discussion_r622434992\": (\n", "        \"add return type\",\n", "        \"add type\",\n", "        GOOD,\n", "    ),    \n", "    \"https://github.com/pytorch/vision/pull/5264#discussion_r790429498\": (\n", "        \"improve exception error message\",\n", "        \"improve error message\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/webex/webex-js-sdk/pull/1809#discussion_r462650960\": (\n", "        \"Keep consistent with other functions and split into 2 separate arguments\",\n", "        \"split into 2 arguments\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/jenkinsci/jenkins-test-harness/pull/465#discussion_r935642731\": (\n", "        \"use object's isEmpty method instead of StringUtils\",\n", "        \"use object's isEmpty method\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/apache/flink-training/pull/31#discussion_r697212832\": (\n", "        \"change the arguments type as in other comment\",\n", "        \"fix arguments type\",\n", "        UNCLEAR,\n", "    ),\n", "    \"https://github.com/apolloconfig/apollo/pull/4189#discussion_r777299171\": (\n", "        \"no need for the StrictMapAppenderConstructor\",\n", "        \"remove StrictMapAppenderConstructor\",\n", "        UNRELATED_CHANGES,\n", "    ),\n", "    \"https://github.com/aws/aws-toolkit-vscode/pull/1764#discussion_r636532097\": (\n", "        \"don't initialize cfnTemplatePath\",\n", "        \"remove initialization\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/greenplum-db/pxf/pull/703#discussion_r728486742\": (        \n", "        \"rewrite code in one line\",\n", "        \"rewrite in a line\",\n", "        GOOD,\n", "    ),\n", "    # \"https://github.com/DKU-STUDY/Algorithm/pull/337#discussion_r464035670\": (        \n", "    #     \"여기도 마찬가지\",\n", "    #     \"여기도 마찬가지\",\n", "    #     UNCLEAR,\n", "    # ),\n", "    \"https://github.com/aleph-im/pyaleph/pull/248#discussion_r864943892\": (\n", "        \"use single number with underscore as thousand separator\",\n", "        \"use single number with underscores\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/awslabs/multi-model-server/pull/689#discussion_r235183954\": (\n", "        \"use String.valueOf\",\n", "        \"use String class\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/OSGeo/grass-addons/pull/222#discussion_r446030744\": (\n", "        \"add UI module description\",\n", "        \"add module description\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/bridgecrewio/checkov/pull/4427#discussion_r1099838181\": (\n", "        \"use None instead of empty string as default\",\n", "        \"use None as default\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/netbox-community/netbox/pull/9554#discussion_r905098669\": (\n", "        \"update time using raw update method\",\n", "        \"update time via update method\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/99designs/gqlgen/pull/2131#discussion_r859905936\": (\n", "        \"add godoc\",\n", "        \"add godoc\",\n", "        UNRELATED_CHANGES,\n", "    ),\n", "    \"https://github.com/matrix-org/matrix-js-sdk/pull/3254#discussion_r1162645693\": (\n", "        \"drop 'or response from' from the comment\",\n", "        \"drop or response from\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/yearn/yearn-sdk/pull/266#discussion_r838997715\": (\n", "        \"return result directly without saving it in the const\",\n", "        \"return result directly\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/nipy/nipype/pull/2896#discussion_r263160511\": (\n", "        \"raise error instead of warning if max_jobs is not provided\",\n", "        \"raise error\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/status-im/status-go/pull/2180#discussion_r613961182\": (\n", "        \"add condition that cursors is not nill\",\n", "        \"check cursors not nill\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/optuna/optuna/pull/1537#discussion_r471318089\": (\n", "        \"validate input shapes with atleast_td\",\n", "        \"validate input shapes\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/exadel-inc/esl/pull/987#discussion_r876529103\": (\n", "        \"remove default value for a11yLabelActive\",\n", "        \"remove default value\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/aminalaee/sqladmin/pull/101#discussion_r832961082\": (\n", "        \"don't follow Flask-Admin behaviour here\",\n", "        \"don't follow <PERSON>las<PERSON>-<PERSON><PERSON>\",\n", "        UNCLEAR,\n", "    ),\n", "    \"https://github.com/darkbot-reloaded/DarkBot/pull/11#discussion_r511660612\": (\n", "        \"add 2 more comments here\",\n", "        \"add more comments\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/ImagingDataCommons/dicom-microscopy-viewer/pull/43#discussion_r608180779\": (\n", "        \"switch values of variables Value and Meaning\",\n", "        \"switch values of variables\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/Akuli/porcupine/pull/279#discussion_r571111199\": (\n", "        \"use if-else statement instead of ternary operator\",\n", "        \"use if-else statement\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/larq/larq/pull/446#discussion_r388253790\": (\n", "        \"expect a ValueError\",\n", "        \"expect ValueError\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/glotzerlab/signac-flow/pull/464#discussion_r606855373\": (\n", "        \"rename class to match the style of other projects\",\n", "        \"improve class name\",\n", "        GOOD,\n", "    ),\n", "    \"https://github.com/hashicorp/terraform-ls/pull/1024#discussion_r942285083\": (\n", "        \"remove unnecessary server setup and registry client\",\n", "        \"don't setup server and client\",\n", "        UNCLEAR,\n", "    ),\n", "    \"https://github.com/dstl/Stone-Soup/pull/624#discussion_r867899979\": (\n", "        \"break line into multiple lines to pass FLAKE8 tests\",\n", "        \"break line into multiple\",\n", "        GOOD,\n", "    ),    \n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<｜begin▁of▁sentence｜>You are an AI programming assistant. Your task is to transform code review comments into two versions of actionable instructions, considering both the original comment and the code change as sources of additional context. Then, classify the sample into one of three categories. Structure your response in three lines as follows:\n", "\n", "First Line - Verbose Instruction: Develop a detailed version of the instruction, maintaining relevant content from the original comment but omitting any polite language, subjective opinions, and extraneous details. Use both the comment and code change as context.\n", "Second Line - Concise Instruction: Create a brief version of the instruction, ideal for users who prefer minimal wording but still require clarity. Incorporate both the comment and the code change for guidance.\n", "Third Line - Classification Label: Classify the sample into one of these categories:\n", "UNCLEAR: If the instruction is vague, lacks essential information, or requires additional context not provided.\n", "UNRELATED CHANGES: If the revised instruction is clear but the code change contains changes that are unrelated to the instruction.\n", "GOOD: If the revised instruction is clear and effectively addresses the code change, demonstrating an understanding of the task's requirements and the context of the code, making it a valuable and instructive example.\n", "\n", "Focus Solely on Instructions: Your output should strictly be the actionable instruction derived from the original comment, without providing code continuation or modification. Ensure to use both the original comment and the provided code change (in backticks `) as context to inform your instructions.\n", "Follow Historical Examples: Refer to the examples from our conversation history as a guide for how to approach this task\n", "\n", "Your objective is to generate two distinct instructions (verbose and concise) based on the combination of the original comment and the code change, followed by a classification label that accurately reflects the nature and complexity of the instruction.\n", "\n", "### Instruction:\n", "INPUT\n", "Please put separator after `*args` (important), and also use the constant:\n", "CODE CHANGE\n", "\n", "```\n", "--- neuraxle/hyperparams/space.py\n", "+++ neuraxle/hyperparams/space.py\n", "@@ -78,7 +78,7 @@\n", "     HyperparameterSamples are often the result of calling ``.rvs()`` on an HyperparameterSpace.\n", "     \"\"\"\n", "     DEFAULT_SEPARATOR = '__'\n", "-    def __init__(self, separator=None, *args, **kwds):\n", "+    def __init__(self, *args, separator=DEFAULT_SEPARATOR, **kwds):\n", "         if len(args) == 1 and isinstance(args[0], RecursiveDict) and len(kwds) == 0:\n", "             super().__init__(args[0].items())\n", "             separator = args[0].separator\n", "```\n", "\n", "### Response:\n", "put separator after *args and use constant for the default value\n", "separator after args with default constant\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Add a type for the return\n", "CODE CHANGE\n", "\n", "```\n", "--- src/utils/buildFetchOptions.js\n", "+++ src/utils/buildFetchOptions.js\n", "@@ -5,7 +5,7 @@\n", " \n", " export const buildAgent = (proxy: string = defaultProxy) =>\n", "   proxy ? new ProxyAgent(proxy) : ''\n", "-export const buildFetchOptions = (options: { proxy?: string } = {}) => {\n", "+export const buildFetchOptions = (options: { proxy?: string } = {}): { agent: Object } => {\n", "   const agent = buildAgent(options.proxy)\n", "   return { agent }\n", " }\n", "```\n", "\n", "### Response:\n", "add return type\n", "add type\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Maybe more idiomatic is\n", "CODE CHANGE\n", "\n", "```\n", "--- torchvision/models/detection/backbone_utils.py\n", "+++ torchvision/models/detection/backbone_utils.py\n", "@@ -126,7 +126,7 @@\n", "     if returned_layers is None:\n", "         returned_layers = [1, 2, 3, 4]\n", "     if min(returned_layers) <= 0 or max(returned_layers) >= 5:\n", "-        raise ValueError(f\" `returned_layers` object should contain integers between [1,4], got {returned_layers} \")\n", "+        raise ValueError(f\"Each returned layer must be in the range [1,4]. Got {returned_layers}\")\n", "     return_layers = {f\"layer{k}\": str(v) for v, k in enumerate(returned_layers)}\n", " \n", "     in_channels_stage2 = backbone.inplanes // 8\n", "```\n", "\n", "### Response:\n", "improve exception error message\n", "improve error message\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Can you keep this consistent with other functions and have them be 2 separate arguments?\n", "CODE CHANGE\n", "\n", "```\n", "--- packages/node_modules/@webex/internal-plugin-conversation/src/conversation.js\n", "+++ packages/node_modules/@webex/internal-plugin-conversation/src/conversation.js\n", "@@ -1602,7 +1602,7 @@\n", "    *\n", "    * jumpToActivity - gets searched-for activity and surrounding activities\n", "    */\n", "-  listActivitiesThreadOrdered(options) {\n", "+  listActivitiesThreadOrdered(conversationUrl, activityId) {\n", "     const {\n", "       conversationUrl,\n", "       conversationId\n", "```\n", "\n", "### Response:\n", "Keep consistent with other functions and split into 2 separate arguments\n", "split into 2 arguments\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "?\n", "CODE CHANGE\n", "\n", "```\n", "--- src/main/java/org/jvnet/hudson/test/RealJenkinsRule.java\n", "+++ src/main/java/org/jvnet/hudson/test/RealJenkinsRule.java\n", "@@ -618,7 +618,7 @@\n", " \n", "     private static int readPort(File portFile) throws IOException {\n", "         String s = FileUtils.readFileToString(portFile, StandardCharsets.UTF_8);\n", "-        if (StringUtils.isEmpty(s)) {\n", "+        if (s.isEmpty() {\n", "             LOGGER.log(Level.WARNING, () -> String.format(\"PortFile: %s exists, but value is still not written\", portFile.getAbsolutePath()));\n", "             return 0;\n", "         }\n", "```\n", "\n", "### Response:\n", "use object's isEmpty method instead of StringUtils\n", "use object's isEmpty method\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "same here\n", "CODE CHANGE\n", "\n", "```\n", "--- rides-and-fares/src/main/java/org/apache/flink/training/exercises/ridesandfares/RidesAndFaresExercise.java\n", "+++ rides-and-fares/src/main/java/org/apache/flink/training/exercises/ridesandfares/RidesAndFaresExercise.java\n", "@@ -73,7 +73,7 @@\n", " \n", "         // A stream of taxi fare events, also keyed by rideId.\n", "         DataStream<TaxiFare> fares =\n", "-                env.addSource(fareSource).keyBy((TaxiFare fare) -> fare.rideId);\n", "+                env.addSource(fareSource).keyBy(fare -> fare.rideId);\n", " \n", "         // Create the pipeline.\n", "         rides.connect(fares)\n", "```\n", "\n", "### Response:\n", "change the arguments type as in other comment\n", "fix arguments type\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Now that LoaderOptions provide the `setAllowDuplicateKeys` option, we don't need the `StrictMapAppenderConstructor` anymore?\n", "CODE CHANGE\n", "\n", "```\n", "--- apollo-client/src/main/java/com/ctrip/framework/apollo/util/yaml/YamlParser.java\n", "+++ apollo-client/src/main/java/com/ctrip/framework/apollo/util/yaml/YamlParser.java\n", "@@ -65,16 +65,9 @@\n", "    * Create the {@link Yaml} instance to use.\n", "    */\n", "   private Yaml createYaml() {\n", "-    StrictMapAppenderConstructor constructor = new StrictMapAppenderConstructor();\n", "-    Representer representer = new Representer();\n", "-    DumperOptions dumperOptions = new DumperOptions();\n", "-    dumperOptions.setDefaultFlowStyle(representer.getDefaultFlowStyle());\n", "-    dumperOptions.setDefaultScalarStyle(representer.getDefaultScalarStyle());\n", "-    dumperOptions.setAllowReadOnlyProperties(representer.getPropertyUtils().isAllowReadOnlyProperties());\n", "-    dumperOptions.setTimeZone(representer.getTimeZone());\n", "     LoaderOptions loadingConfig = new LoaderOptions();\n", "     loadingConfig.setAllowDuplicateKeys(false);\n", "-    return new Yaml(constructor, representer, dumperOptions, loadingConfig);\n", "+    return new Yaml(new SafeConstructor(), new Representer(), new DumperOptions(), loadingConfig);\n", "   }\n", " \n", "   private boolean process(MatchCallback callback, Yaml yaml, String content) {\n", "```\n", "\n", "### Response:\n", "no need for the StrictMapAppenderConstructor\n", "remove StrictMapAppenderConstructor\n", "UNRELATED CHANGES\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "can   `file` and `cfnTemplatePath` be uninitialized? Looks like they are reassigned in the loop below.\n", "CODE CHANGE\n", "\n", "```\n", "--- src/lambda/commands/createNewSamApp.ts\n", "+++ src/lambda/commands/createNewSamApp.ts\n", "@@ -355,7 +355,7 @@\n", "     files: string[]\n", " ): Promise<vscode.Uri | undefined> {\n", "     let file: string = files[0]\n", "-    let cfnTemplatePath: string = path.resolve(config.location.fsPath, config.name, file)\n", "+    let cfnTemplatePath: string\n", "     for (let i = 0; i < files.length; i++) {\n", "          file = files[i];\n", "          cfnTemplatePath = path.resolve(config.location.fsPath, config.name, file)\n", "```\n", "\n", "### Response:\n", "don't initialize cfnTemplatePath\n", "remove initialization\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "this seems a bit redundant. I think you could just change it to one line\n", "CODE CHANGE\n", "\n", "```\n", "--- server/pxf-hdfs/src/main/java/org/greenplum/pxf/plugins/hdfs/AvroResolver.java\n", "+++ server/pxf-hdfs/src/main/java/org/greenplum/pxf/plugins/hdfs/AvroResolver.java\n", "@@ -221,12 +221,7 @@\n", "         Schema.Type fieldType = fieldSchema.getType();\n", " \n", "         int ret = 0;\n", "-        LogicalType logicalType = null;\n", "-        if(fieldSchema.getLogicalType() != null )\n", "-        {\n", "-             logicalType = fieldSchema.getLogicalType();\n", "-\n", "-        }\n", "+        LogicalType logicalType = fieldSchema.getLogicalType();\n", "         switch (fieldType) {\n", "             case ARRAY:\n", "                 if (fieldValue == null) {\n", "```\n", "\n", "### Response:\n", "rewrite code in one line\n", "rewrite in a line\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Would it not be more clear like this ?\n", "CODE CHANGE\n", "\n", "```\n", "--- src/aleph/config.py\n", "+++ src/aleph/config.py\n", "@@ -7,7 +7,7 @@\n", "     return {\n", "         \"logging\": {\n", "             \"level\": logging.WARNING,\n", "-            \"max_log_file_size\": 1 * 1024 * 1024 * 1024,\n", "+            \"max_log_file_size\": 1_000_000_000,  # 1 GB\n", "         },\n", "         \"aleph\": {\n", "             \"queue_topic\": \"ALEPH-QUEUE\",\n", "```\n", "\n", "### Response:\n", "use single number with underscore as thousand separator\n", "use single number with underscores\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "St<PERSON>\n", "CODE CHANGE\n", "\n", "```\n", "--- frontend/server/src/main/java/com/amazonaws/ml/mms/wlm/WorkerThread.java\n", "+++ frontend/server/src/main/java/com/amazonaws/ml/mms/wlm/WorkerThread.java\n", "@@ -110,7 +110,7 @@\n", "                                 + '-'\n", "                                 + model.getModelName()\n", "                                         .substring(0, <PERSON><PERSON>min(model.getModelName().length(), 25)),\n", "-                        <PERSON><PERSON>toS<PERSON>(System.currentTimeMillis()),\n", "+                        String.valueOf(System.currentTimeMillis()),\n", "                         \"ms\",\n", "                         ConfigManager.getInstance().getHostName(),\n", "                         new Dimension(\"Level\", \"Host\"));\n", "```\n", "\n", "### Response:\n", "use String.valueOf\n", "use String class\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Silence: \"WARNING: Bug in UI description. Missing module description\"\n", "\n", "--> might be extended\n", "CODE CHANGE\n", "\n", "```\n", "--- grass7/gui/wxpython/wx.metadata/g.gui.cswbrowser/g.gui.cswbrowser.py\n", "+++ grass7/gui/wxpython/wx.metadata/g.gui.cswbrowser/g.gui.cswbrowser.py\n", "@@ -17,6 +17,13 @@\n", " \n", " from mdlib.cswlib import CSWBrowserPanel, CSWConnectionPanel\n", " import wx\n", "+\n", "+#%module\n", "+#% description: Graphical CSW metadata browser.\n", "+#% keyword: general\n", "+#% keyword: GUI\n", "+#% keyword: metadata\n", "+#%end\n", " \n", " set_gui_path()\n", " \n", "```\n", "\n", "### Response:\n", "add UI module description\n", "add module description\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "let's avoid an edge case of a key with an empty string\n", "CODE CHANGE\n", "\n", "```\n", "--- checkov/terraform/graph_builder/foreach_handler.py\n", "+++ checkov/terraform/graph_builder/foreach_handler.py\n", "@@ -184,7 +184,7 @@\n", "         if new_key:\n", "             key_to_val_changes[EACH_KEY] = new_key\n", "         return key_to_val_changes\n", "-    def _create_new_resource(self, main_resource: TerraformBlock, new_value: int | str, new_key: str = ''):\n", "+    def _create_new_resource(self, main_resource: TerraformBlock, new_value: int | str, new_key: Optional[str] = None):\n", "         new_resource = deepcopy(main_resource)\n", "         block_type, block_name = new_resource.name.split('.')\n", "         if main_resource.attributes.get(COUNT_STRING):\n", "```\n", "\n", "### Response:\n", "use None instead of empty string as default\n", "use None as default\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "It might be a bit faster to make a raw `update()` query to update the time.\n", "CODE CHANGE\n", "\n", "```\n", "--- netbox/netbox/api/authentication.py\n", "+++ netbox/netbox/api/authentication.py\n", "@@ -30,8 +30,7 @@\n", "                 logger = logging.getLogger('netbox.auth.login')\n", "                 logger.warning(\"Maintenance mode enabled: disabling update of token's last used timestamp\")\n", "             else:\n", "-                token.last_used = timezone.now()\n", "-                token.save()\n", "+                Token.objects.filter(pk=token.pk).update(last_used=timezone.now())\n", " \n", "         # Enforce the Token's expiration time, if one has been set.\n", "         if token.is_expired:\n", "```\n", "\n", "### Response:\n", "update time using raw update method\n", "update time via update method\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Please, add god<PERSON>.\n", "\n", "Also, what do you think about returning an error instead of panic?\n", "CODE CHANGE\n", "\n", "```\n", "--- graphql/input.go\n", "+++ graphql/input.go\n", "@@ -7,7 +7,7 @@\n", " )\n", " \n", " const unmarshalInputCtx key = \"unmarshal_input_context\"\n", "-func BuildMap(unmarshaler ...interface{}) map[reflect.Type]reflect.Value {\n", "+func BuildUnmarshalerMap(unmarshaler ...interface{}) map[reflect.Type]reflect.Value {\n", " \tmaps := make(map[reflect.Type]reflect.Value)\n", " \tfor _, v := range unmarshaler {\n", " \t\tft := reflect.TypeOf(v)\n", "```\n", "\n", "### Response:\n", "add godoc\n", "add godoc\n", "UNRELATED CHANGES\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "\"or response from...\" ?\n", "CODE CHANGE\n", "\n", "```\n", "--- src/rust-crypto/rust-crypto.ts\n", "+++ src/rust-crypto/rust-crypto.ts\n", "@@ -235,7 +235,7 @@\n", "     /** called by the sync loop to process the notification that device lists have\n", "      * been changed.\n", "      *\n", "-     * @param deviceLists - device_lists field from /sync, or response from\n", "+     * @param deviceLists - device_lists field from /sync\n", "      */\n", "     public async processDeviceLists(deviceLists: IDeviceLists): Promise<void> {\n", "         const devices = new RustSdkCryptoJs.DeviceLists(deviceLists.changed, deviceLists.left);\n", "```\n", "\n", "### Response:\n", "drop 'or response from' from the comment\n", "drop or response from\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "nit: Are we saving it in the const so it's clearer what we are returning? If not, let's just\n", "CODE CHANGE\n", "\n", "```\n", "--- src/interfaces/vault.ts\n", "+++ src/interfaces/vault.ts\n", "@@ -408,8 +408,7 @@\n", " \n", "     const willZapToPickleJar = PickleJars.includes(vaultAddress);\n", "     const zapContract = willZapToPickleJar ? ContractAddressId.pickleZapIn : ContractAddressId.zapperZapIn;\n", "-    const zapContractAddress = await this.yearn.addressProvider.addressById(zapContract);\n", "-    return zapContractAdd<PERSON>;\n", "+    return this.yearn.addressProvider.addressById(zapContract);\n", "   }\n", " \n", "   private async getWithdrawSpenderAddress(vaultAddress: Address, tokenAddress: Address): Promise<Address> {\n", "```\n", "\n", "### Response:\n", "return result directly without saving it in the const\n", "return result directly\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Also, shouldn't passing `'max_jobs'` be an error, not a warning?\n", "CODE CHANGE\n", "\n", "```\n", "--- nipype/pipeline/plugins/legacymultiproc.py\n", "+++ nipype/pipeline/plugins/legacymultiproc.py\n", "@@ -185,7 +185,8 @@\n", "         maxtasks = self.plugin_args.get('maxtasksperchild', 10)\n", "         self.processors = self.plugin_args.get('n_procs', cpu_count())\n", "         if self.plugin_args.get('max_jobs', np.inf) < self.processors:\n", "-            logger.warning(\"To limit processes, use n_procs\")\n", "+            raise ValueError(\"Unused plugin argument 'max_jobs' - \"\n", "+                             \"to limit processes, use 'n_procs'\")\n", "         self.memory_gb = self.plugin_args.get(\n", "             'memory_gb',  # Allocate 90% of system memory\n", "             get_system_total_memory_gb() * 0.9)\n", "```\n", "\n", "### Response:\n", "raise error instead of warning if max_jobs is not provided\n", "raise error\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "The `cursors` slice maybe nil at this point, as there is no check on the slice's state.\n", "CODE CHANGE\n", "\n", "```\n", "--- protocol/message_persistence.go\n", "+++ protocol/message_persistence.go\n", "@@ -617,7 +617,7 @@\n", " \t}\n", " \n", " \tvar newCursor string\n", "-\tif len(result) > limit {\n", "+\tif len(result) > limit  && cursors != nil  {\n", " \t\tnewCursor = cursors[limit]\n", " \t\tresult = result[:limit]\n", " \t}\n", "```\n", "\n", "### Response:\n", "add condition that cursors is not nill\n", "check cursors not nill\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Should `s` not be 2d? Maybe it's overkill but we could also validate the input shapes.\n", "CODE CHANGE\n", "\n", "```\n", "--- tests/multi_objective_tests/hypervolume_tests/test_wfg.py\n", "+++ tests/multi_objective_tests/hypervolume_tests/test_wfg.py\n", "@@ -41,6 +41,6 @@\n", " \n", " def test_invalid_input() -> None:\n", "     r = np.ones(3)\n", "-    s = 2 * np.ones(3)\n", "+    s = np.atleast_td(2 * np.ones(3))\n", "     with pytest.raises(ValueError):\n", "         _ = optuna.multi_objective._hypervolume.WFG().compute(s, r)\n", "```\n", "\n", "### Response:\n", "validate input shapes with atleast_td\n", "validate input shapes\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Suggested example is tottaly equal even in terms of upcoming `@attr` update\n", "CODE CHANGE\n", "\n", "```\n", "--- src/modules/esl-trigger/core/esl-trigger.ts\n", "+++ src/modules/esl-trigger/core/esl-trigger.ts\n", "@@ -45,7 +45,7 @@\n", "   @attr({defaultValue: ''}) public a11yTarget: string;\n", " \n", "   /** Value of aria-label for active state */\n", "-  @attr({defaultValue: ''}) public a11yLabelActive: string;\n", "+  @attr() public a11yLabelActive: string;\n", "   /** Value of aria-label for inactive state */\n", "   @attr({defaultValue: ''}) public a11yLabelInactive: string;\n", " \n", "```\n", "\n", "### Response:\n", "remove default value for a11yLabelActive\n", "remove default value\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "What do you think if we don't follow the Flask-Admin behaviour here? I don't think it would be a problem here.\n", "CODE CHANGE\n", "\n", "```\n", "--- sqladmin/models.py\n", "+++ sqladmin/models.py\n", "@@ -331,10 +331,9 @@\n", "     \"\"\"A list of available export filetypes.\n", "     Currently only `csv` is supported.\n", "     \"\"\"\n", "-    export_max_rows: ClassVar[Optional[int]] = 0\n", "+    export_max_rows: ClassVar[int] = 0\n", "     \"\"\"Maximum number of rows allowed for export.\n", "-\n", "-    Unlimited by default. Uses `page_size` if set to `None`.\n", "+    Unlimited by default.\n", "     \"\"\"\n", " \n", "     # Form\n", "```\n", "\n", "### Response:\n", "don't follow Flask-Admin behaviour here\n", "don't follow <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "i'd just add the 2 little comments here and remove them below\n", "CODE CHANGE\n", "\n", "```\n", "--- src/main/java/com/github/manolo8/darkbot/gui/titlebar/TrayButton.java\n", "+++ src/main/java/com/github/manolo8/darkbot/gui/titlebar/TrayButton.java\n", "@@ -17,7 +17,9 @@\n", " public class TrayButton extends TitleBarButton<JFrame> {\n", " \n", "     private final TrayIcon icon;\n", "+    // Default <PERSON> for TrayIcon has no look & feel, forcing us to use a JPopupMenu\n", "     private final JPopupMenu popupMenu;\n", "+    // JPopupMenu requires a parent dialog to display, see https://stackoverflow.com/a/20079304\n", "     private final JDialog dialog;\n", " \n", "     private boolean shownMsg;\n", "```\n", "\n", "### Response:\n", "add 2 more comments here\n", "add more comments\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Value and Meaning are switched:\n", "CODE CHANGE\n", "\n", "```\n", "--- src/viewer.js\n", "+++ src/viewer.js\n", "@@ -602,8 +602,8 @@\n", "   const unitCodedConceptMeaning = unitSuffixToMeaningMap[unitSuffix];\n", " \n", "   if (area) {\n", "-    const nameCodedConceptValue = \"Area\";\n", "-    const nameCodedConceptMeaning = \"42798000\";\n", "+    const nameCodedConceptValue = \"42798000\";\n", "+    const nameCodedConceptMeaning = \"Area\";\n", "     measurement = getMeasurementContentItem(\n", "       area,\n", "       nameCodedConceptValue,\n", "```\n", "\n", "### Response:\n", "switch values of variables Value and Meaning\n", "switch values of variables\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "`a if b else c` is typically used when the resulting value is useful, not when the side-effects of evaluating `a` and `c` are useful:\n", "CODE CHANGE\n", "\n", "```\n", "--- porcupine/plugins/sidebar.py\n", "+++ porcupine/plugins/sidebar.py\n", "@@ -20,7 +20,10 @@\n", "         directories: List[pathlib.Path] = []\n", " \n", "         for p in sorted(self.path.iterdir()):\n", "-            directories.append(p) if p.is_dir() else files.append(p)\n", "+            if p.is_dir():\n", "+                directories.append(p)\n", "+            else:\n", "+                files.append(p)\n", " \n", "         for d in directories:\n", "             node = self.insert('', 'end', text=d.name, open=False)\n", "```\n", "\n", "### Response:\n", "use if-else statement instead of ternary operator\n", "use if-else statement\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Shouldn't this be?\n", "CODE CHANGE\n", "\n", "```\n", "--- larq/optimizers_test.py\n", "+++ larq/optimizers_test.py\n", "@@ -80,7 +80,7 @@\n", "             _test_optimizer(naughty_case_opt)\n", " \n", "     def test_missing_default(self):\n", "-        with pytest.warns(Warning):\n", "+        with pytest.raises(ValueError):\n", "             naughty_case_opt = lq.optimizers.CaseOptimizer(\n", "                 (lq.optimizers.Bop.is_binary_variable, lq.optimizers.Bop()),\n", "             )\n", "```\n", "\n", "### Response:\n", "expect a ValueError\n", "expect ValueError\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "This should not be changed, it matched the style of the other projects in our testing suite. However, it should be named like:\n", "CODE CHANGE\n", "\n", "```\n", "--- tests/define_test_aggregate_project.py\n", "+++ tests/define_test_aggregate_project.py\n", "@@ -1,6 +1,6 @@\n", " from flow import FlowProject, aggregator, cmd, with_job\n", " \n", "-class _TestAggregateProject(FlowProject):\n", "+class _AggregateTestProject(FlowProject):\n", "     pass\n", " \n", " \n", "```\n", "\n", "### Response:\n", "rename class to match the style of other projects\n", "improve class name\n", "GOOD\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "This doesn't seem necessary, given that we're testing a method which is unlikely to ever make request to the Registry?\n", "CODE CHANGE\n", "\n", "```\n", "--- internal/hooks/module_source_local_test.go\n", "+++ internal/hooks/module_source_local_test.go\n", "@@ -28,16 +28,8 @@\n", " \tif err != nil {\n", " \t\t<PERSON><PERSON>(err)\n", " \t}\n", "-\tregClient := registry.NewClient()\n", "-\tsrv := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {\n", "-\t\thttp.Error(w, fmt.Sprintf(\"unexpected request: %q\", r.RequestURI), 400)\n", "-\t}))\n", "-\tregClient.BaseURL = srv.URL\n", "-\tt.<PERSON>(srv.<PERSON>)\n", "-\n", " \th := &<PERSON><PERSON>{\n", " \t\tModStore:       <PERSON><PERSON>,\n", "-\t\tRegistryClient: regClient,\n", " \t}\n", " \n", " \tmodules := []string{\n", "```\n", "\n", "### Response:\n", "remove unnecessary server setup and registry client\n", "don't setup server and client\n", "UNCLEAR\n", "<|EOT|>\n", "### Instruction:\n", "INPUT\n", "Line too long to pass FLAKE8 tests\n", "CODE CHANGE\n", "\n", "```\n", "--- stonesoup/dataassociator/general.py\n", "+++ stonesoup/dataassociator/general.py\n", "@@ -45,8 +45,9 @@\n", "         track_1_dict: Dict[datetime, State] = \\\n", "             {state.timestamp: state for i, state in enumerate(reversed(track1.states))\n", "              if i < self.max_timestep_to_look_back}\n", "-        track_2_dict = {state.timestamp: state for i, state in enumerate(reversed(track2.states)) if\n", "-                        i < self.max_timestep_to_look_back}\n", "+        track_2_dict = {\n", "+        state.timestamp: state for i, state in enumerate(reversed(track2.states)) \n", "+        if i < self.max_timestep_to_look_back}\n", " \n", "         all_times = set(track_1_dict.keys()) | set(track_2_dict.keys())\n", " \n", "```\n", "\n", "### Response:\n", "break line into multiple lines to pass FLAKE8 tests\n", "break line into multiple\n", "GOOD\n", "<|EOT|>\n", "\n"]}], "source": ["from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer, DeepSeekLLMChatTokenizer\n", "\n", "# Use this tokenizer if you are using DeepSeek-Coder model\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "# Use this tokenizer if you are using DeepSeek-LLM-Chat\n", "# tokenizer = DeepSeekLLMChatTokenizer()\n", "\n", "TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>You are an AI programming assistant. Your task is to transform code review comments into two versions of actionable instructions, considering both the original comment and the code change as sources of additional context. Then, classify the sample into one of three categories. Structure your response in three lines as follows:\n", "\n", "First Line - Verbose Instruction: Develop a detailed version of the instruction, maintaining relevant content from the original comment but omitting any polite language, subjective opinions, and extraneous details. Use both the comment and code change as context.\n", "Second Line - Concise Instruction: Create a brief version of the instruction, ideal for users who prefer minimal wording but still require clarity. Incorporate both the comment and the code change for guidance.\n", "Third Line - Classification Label: Classify the sample into one of these categories:\n", "UNCLEAR: If the instruction is vague, lacks essential information, or requires additional context not provided.\n", "UNRELATED CHANGES: If the revised instruction is clear but the code change contains changes that are unrelated to the instruction.\n", "GOOD: If the revised instruction is clear and effectively addresses the code change, demonstrating an understanding of the task's requirements and the context of the code, making it a valuable and instructive example.\n", "\n", "Focus Solely on Instructions: Your output should strictly be the actionable instruction derived from the original comment, without providing code continuation or modification. Ensure to use both the original comment and the provided code change (in backticks `) as context to inform your instructions.\n", "Follow Historical Examples: Refer to the examples from our conversation history as a guide for how to approach this task\n", "\n", "Your objective is to generate two distinct instructions (verbose and concise) based on the combination of the original comment and the code change, followed by a classification label that accurately reflects the nature and complexity of the instruction.\n", "\n", "\"\"\"\n", "\n", "EXAMPLE_INPUT = \"\"\"### Instruction:\n", "INPUT\n", "{original_instruction}\n", "CODE CHANGE\n", "\n", "```\n", "{code_diff}\n", "```\n", "\n", "### Response:\n", "\"\"\"\n", "\n", "EXAMPLE_OUTPUT = \"\"\"{verbose_instruction}\n", "{short_instruction}\n", "{label}\n", "<|EOT|>\n", "\"\"\"\n", "\n", "for sample_id, (verbose_instruction, short_instruction, label) in MANUAL_TRIPLE_LABELS.items():\n", "    sample = filtered_suggested_edit_by_url[sample_id]\n", "    TEMPLATE = TEMPLATE + EXAMPLE_INPUT.format(\n", "        original_instruction=sample['original_instruction'],\n", "        code_diff=get_diff(sample))\n", "    TEMPLATE = TEMPLATE + EXAMPLE_OUTPUT.format(\n", "        verbose_instruction=verbose_instruction,\n", "        short_instruction=short_instruction,\n", "        label=label)\n", "\n", "print(TEMPLATE)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ORIGINAL INSTRUCTION: adding a `counter2` field to the schema will verify that only `BUG_1737656_METRIC_NAMES` are converted:\n", "\n", "--- ingestion-core/src/test/java/com/mozilla/telemetry/ingestion/core/transform/PubsubMessageToObjectNodeBeamTest.java\n", "+++ ingestion-core/src/test/java/com/mozilla/telemetry/ingestion/core/transform/PubsubMessageToObjectNodeBeamTest.java\n", "@@ -424,6 +424,8 @@\n", "         + \"}\\n\");\n", "     List<Field> bqFields = ImmutableList.of(Field.newBuilder(\"metrics\", LegacySQLTypeName.RECORD, //\n", "         Field.of(\"counter\", LegacySQLTypeName.RECORD, //\n", "+            Field.of(\"my_count\", LegacySQLTypeName.INTEGER)), //\n", "+        Field.of(\"counter2\", LegacySQLTypeName.RECORD, //\n", "             Field.of(\"my_count\", LegacySQLTypeName.INTEGER)), //\n", "         Field.newBuilder(\"url\", LegacySQLTypeName.RECORD, Field.of(\"key\", LegacySQLTypeName.STRING),\n", "             Field.of(\"value\", LegacySQLTypeName.STRING)).setMode(Mode.REPEATED).build(),\n", "\n", " REVISED INSTRUCTION: ['add counter2 field to schema', 'add counter2 field', 'GOOD']\n", "\n", "==================================================\n"]}], "source": ["import requests \n", "\n", "# @persistent_cache('/home/<USER>/data/deepseek_33b_instruct_cache.jsonl')\n", "def query_remote_model(sample):\n", "    text = TEMPLATE + EXAMPLE_INPUT.format(\n", "        original_instruction=sample['original_instruction'],\n", "        code_diff=get_diff(sample))\n", "\n", "    # prompt_tokens = [tokenizer.bos_id] + tokenizer.tokenize(text)\n", "    prompt_tokens = tokenizer.tokenize(text)\n", "    max_generated_tokens = 64\n", "    temperature = 0\n", "    top_k = 0\n", "    top_p = 1\n", "    data = {\n", "        \"prompt\": prompt_tokens,\n", "        \"n_predict\": max_generated_tokens,\n", "        \"temperature\": temperature,\n", "        \"top_k\": top_k or 50,\n", "        \"top_p\": top_p or 0.95,\n", "    }\n", "\n", "    response = requests.post(\n", "                \"http://localhost:8086/completion\",\n", "                json=data,\n", "                headers={\"Content-Type\": \"application/json\"},\n", "                timeout=120.0,  # set a timeout as 2 minutes\n", "            )\n", "    decoded_response = response.json()\n", "    content = decoded_response[\"content\"].strip().splitlines()\n", "    return content\n", "\n", "url = \"https://github.com/mozilla/gcp-ingestion/pull/1881#discussion_r745063169\"\n", "# url = \"https://github.com/techqueria/data-structures-and-algorithms/pull/16#discussion_r249550574\"\n", "print('ORIGINAL INSTRUCTION:', filtered_suggested_edit_by_url[url]['original_instruction'])\n", "print()\n", "print(get_diff(filtered_suggested_edit_by_url[url]))\n", "print()\n", "print(' REVISED INSTRUCTION:', query_remote_model(filtered_suggested_edit_by_url[url]))\n", "print()\n", "print('=' * 50)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                   REVIEW URL: https://github.com/spiffe/spire/pull/3132#discussion_r888254865\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: consolidate log fields into one\n", "  REVISED (SHORT) INSTRUCTION: consolidate log fields\n", "         ORIGINAL INSTRUCTION: nitpick: If there is just one field, this can be consolidated:\n", "\n", "--- pkg/server/datastore/sqlstore/sqlstore.go\n", "+++ pkg/server/datastore/sqlstore/sqlstore.go\n", "@@ -3825,12 +3825,10 @@\n", " \t// The issue allowing orphaned dns_name records was reported in #3126 and fixed in #3127\n", " \t// TODO: remove in 1.5.0 (see #3131)\n", " \tif res := tx.Exec(\"DELETE FROM dns_names WHERE registered_entry_id NOT IN (SELECT id FROM registered_entries)\"); res.Error != nil {\n", " \t\treturn sqlError.<PERSON><PERSON>(res.Error)\n", " \t} else if (res.<PERSON>sAffected > 0) {\n", "-\t\tlogger.With<PERSON><PERSON><PERSON>(logrus.Fields{\n", "-\t\t\ttelemetry.Count: res.<PERSON>sAffected,\n", "-\t\t}).Info(\"Pruned orphaned dns_names\")\n", "+\t\tlogger.<PERSON><PERSON><PERSON>(telemetry.Count, res.RowsAffected).Info(\"Pruned orphaned dns_names\")\n", " \t}\n", " \n", " \treturn nil\n", " }\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/grafana/metrictank/pull/1212#discussion_r269681885\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: use strings.Builder instead of creating a new one\n", "  REVISED (SHORT) INSTRUCTION: use existing Builder\n", "         ORIGINAL INSTRUCTION: this is the canonical way\n", "\n", "--- idx/metric_definition.go\n", "+++ idx/metric_definition.go\n", "@@ -51,11 +51,11 @@\n", " // using data interned in the object store\n", " func (mn *MetricName) String() string {\n", " \tif len(mn.nodes) == 0 {\n", " \t\treturn \"\"\n", " \t}\n", "-\tbld := strings.Builder{}\n", "+\tvar bld strings.Builder\n", " \treturn mn.string(&bld)\n", " }\n", " \n", " func (mn *MetricName) string(bld *strings.Builder) string {\n", " \t// get []int of the lengths of all of the mn.Nodes\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/discordjs/discord.js/pull/7490#discussion_r809436974\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: use strict equality check instead of loose\n", "  REVISED (SHORT) INSTRUCTION: use strict equality check\n", "         ORIGINAL INSTRUCTION: Why not do this instead?\n", "\n", "--- packages/discord.js/src/structures/Message.js\n", "+++ packages/discord.js/src/structures/Message.js\n", "@@ -299,12 +299,11 @@\n", "    * If mentions cannot be resolved to a name, the relevant mention in the message content will not be converted.\n", "    * @type {?string}\n", "    * @readonly\n", "    */\n", "   get cleanContent() {\n", "-    // eslint-disable-next-line eqeqeq\n", "-    return this.content != null ? Util.cleanContent(this.content, this.channel) : null;\n", "+    return typeof this.content === 'string' ? Util.cleanContent(this.content, this.channel) : null;\n", "   }\n", " \n", "   /**\n", "    * Whether the message is editable by the client user\n", "    * @type {boolean}\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/Graylog2/graylog2-server/pull/13947#discussion_r1021523673\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: break line into multiple lines to pass FLAKE8 tests\n", "  REVISED (SHORT) INSTRUCTION: break line into multiple\n", "         ORIGINAL INSTRUCTION: does the same, no?\n", "\n", "--- graylog2-server/src/main/java/org/graylog/plugins/pipelineprocessor/functions/json/JsonUtils.java\n", "+++ graylog2-server/src/main/java/org/graylog/plugins/pipelineprocessor/functions/json/JsonUtils.java\n", "@@ -64,11 +64,11 @@\n", "         for (Map.Entry<String, Object> mapEntry : json.entrySet()) {\n", "             for (Entry entry : parseValue(mapEntry.getKey(), mapEntry.getValue(), mapper, extractFlags)) {\n", "                 if (stringify) {\n", "                     resultRoot.put(entry.key(), entry.value().toString());\n", "                 } else {\n", "-                    putNodeWithType(resultRoot, entry.key(), entry.value());\n", "+                    resultRoot.putPOJO(entry.key(), entry.value());\n", "                 }\n", "             }\n", "         }\n", "         return resultRoot;\n", "     }\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/danforthcenter/plantcv/pull/777#discussion_r660178594\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: exclude first row from ecdf dataframe\n", "  REVISED (SHORT) INSTRUCTION: exclude first row\n", "         ORIGINAL INSTRUCTION: The eCDF starts with x=0 and y=-Inf, causing a warning in plotnine. If we exclude the first row then only valid values are plotted.\n", "\n", "--- plantcv/plantcv/visualize/obj_size_ecdf.py\n", "+++ plantcv/plantcv/visualize/obj_size_ecdf.py\n", "@@ -29,11 +29,11 @@\n", " \n", "     objects, hierarchy = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)[-2:]\n", "     areas = [cv2.contourArea(cnt) for cnt in objects]\n", " \n", "     ecdf = ECDF(areas, side='right')\n", "-    ecdf_df = pd.DataFrame({'object area': ecdf.x, 'cumulative probability': ecdf.y})\n", "+    ecdf_df = pd.DataFrame({'object area': ecdf.x[1:], 'cumulative probability': ecdf.y[1:]})\n", "     # create ecdf plot and apply log-scale for x-axis (areas)\n", "     fig_ecdf = (ggplot(data=ecdf_df, mapping=aes(x='object area', y='cumulative probability'))\n", "                 + geom_point(size=.1)\n", "                 + scale_x_log10())\n", "     if title is not None:\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/cri-o/cri-o/pull/5082#discussion_r674551062\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: remove unnecessary return statement\n", "  REVISED (SHORT) INSTRUCTION: remove redundant return\n", "         ORIGINAL INSTRUCTION: nit:\n", "\n", "--- internal/process/defunct_processes.go\n", "+++ internal/process/defunct_processes.go\n", "@@ -76,14 +76,13 @@\n", " \n", " \tparts := strings.SplitN(data[:i], \" (\", 2)\n", " \tif len(parts) != 2 {\n", " \t\treturn nil, errors.Errorf(\"invalid stat data (no comm): %q\", data)\n", " \t}\n", "-\tstat := &Stat{\n", "+\treturn &Stat{\n", " \t\t// The command name is field 2.\n", " \t\tComm: parts[1],\n", " \n", " \t\t// The state is field 3, which is the first two fields and a space after.\n", " \t\tState: string(data[i+2]),\n", "-\t}\n", "-\treturn stat, nil\n", "+\t}, nil\n", " }\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/realm/realm-js/pull/4299#discussion_r797649409\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: rename baseUrl to mongodbRealmBaseUrl\n", "  REVISED (SHORT) INSTRUCTION: rename variable name\n", "         ORIGINAL INSTRUCTION: for consistency maybe?\n", "\n", "--- integration-tests/tests/src/utils/import-app.ts\n", "+++ integration-tests/tests/src/utils/import-app.ts\n", "@@ -27,11 +27,11 @@\n", " function getUrls() {\n", "   // Try reading the app importer URL out of the environment, it might not be accessiable via localhost\n", "   const { appImporterUrl, mongodbRealmBaseUrl } = environment;\n", "   return {\n", "     appImporterUrl: typeof appImporterUrl === \"string\" ? appImporterUrl : \"http://localhost:8091\",\n", "-    baseUrl: typeof mongodbRealmBaseUrl === \"string\" ? mongodbRealmBaseUrl : \"http://localhost:9090\",\n", "+    mongodbRealmBaseUrl: typeof mongodbRealmBaseUrl === \"string\" ? mongodbRealmBaseUrl : \"http://localhost:9090\",\n", "   };\n", " }\n", " \n", " export async function importApp(name: string, replacements: TemplateReplacements = {}): Promise<App> {\n", "   const { appImporterUrl, baseUrl } = getUrls();\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/0xPolygon/polygon-edge/pull/94#discussion_r676827998\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: use t.Fatalf instead of assert\n", "  REVISED (SHORT) INSTRUCTION: use t.Fatalf\n", "         ORIGINAL INSTRUCTION: Might be better to do `t.<PERSON><PERSON><PERSON>` here, since you might want the test to stop if this happens\n", "\n", "--- e2e/websocket_test.go\n", "+++ e2e/websocket_test.go\n", "@@ -87,11 +87,13 @@\n", " \t\trequest, constructErr := constructWSRequest(\n", " \t\t\trequestID,\n", " \t\t\t\"eth_getBalance\",\n", " \t\t\t[]string{preminedAccounts[0].address.String(), \"latest\"},\n", " \t\t)\n", "-\t\tassert.Nilf(t, constructErr, fmt.Sprintf(\"Unable to construct request: %v\", constructErr))\n", "+\t\tif constructErr != nil {\n", "+\t\t\tt.<PERSON>(\"Unable to construct request: %v\", constructErr)\n", "+\t\t}\n", " \n", " \t\tres, _ := getWSResponse(t, ws, request)\n", " \n", " \t\tassert.Equalf(t, res.ID, float64(requestID), \"Invalid response ID\")\n", " \n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/awslabs/syne-tune/pull/24#discussion_r750159572\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: simplify the condition\n", "  REVISED (SHORT) INSTRUCTION: simplify condition\n", "         ORIGINAL INSTRUCTION: Would this be simpler?\n", "\n", "--- sagemaker_tune/optimizer/schedulers/pbt.py\n", "+++ sagemaker_tune/optimizer/schedulers/pbt.py\n", "@@ -324,14 +324,11 @@\n", " \n", "         for key, hp_range in self.config_space.items():\n", "             if isinstance(hp_range, Domain):\n", "                 # For `Categorical`, all values have the same distance from each\n", "                 # other, so we can always resample uniformly\n", "-                is_numerical = \\\n", "-                    isinstance(hp_range, Float) or isinstance(hp_range, Integer) \\\n", "-                    or isinstance(hp_range, FiniteRange)\n", "-                if (not is_numerical) or \\\n", "+                if isinstance(hp_range, Categorical) or \\\n", "                         self._random_state.rand() < self._resample_probability:\n", "                     new_config[key] = hp_range.sample(\n", "                         size=1, random_state=self._random_state)\n", "                 else:\n", "                     multiplier = 1.2 if self._random_state.rand() > 0.5 else 0.8\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/kubeshark/kubeshark/pull/683#discussion_r796807497\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: use errors.As for error handling\n", "  REVISED (SHORT) INSTRUCTION: handle error wrapping\n", "         ORIGINAL INSTRUCTION: This code doesn't handle error wrapping.\n", "`LogError(fmt.<PERSON><PERSON><PERSON>(\"%w\", errors.Wrap(fmt.<PERSON><PERSON><PERSON>(\"oh no\"), 0)))` doesn't print the call stack.\n", "Use `errors.As` instead of bare type checking.\n", "\n", "--- tap/tlstapper/tls_tapper.go\n", "+++ tap/tlstapper/tls_tapper.go\n", "@@ -160,14 +160,14 @@\n", " \t}\n", " \n", " \treturn nil\n", " }\n", " func LogError(err error) error {\n", "-\tswitch err := err.(type) {\n", "-\tcase *errors.Error:\n", "-\t\tlogger.<PERSON><PERSON><PERSON>(\"Error: %v\", err.<PERSON><PERSON><PERSON>())\n", "-\tdefault:\n", "+\tvar e *errors.Error\n", "+\tif errors.As(err, &e) {\n", "+\t\tlogger.Log<PERSON>(\"Error: %v\", e.<PERSON><PERSON><PERSON>())\n", "+\t} else {\n", " \t\tlogger.Log<PERSON>(\"Error: %v\", err)\n", " \t}\n", " \n", " \treturn err\n", " }\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/readthedocs/readthedocs.org/pull/7548#discussion_r547203429\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: improve docstring description\n", "  REVISED (SHORT) INSTRUCTION: improve docstring\n", "         ORIGINAL INSTRUCTION: I found the docstring a little ambiguous and I'm suggesting a change.\n", "\n", "--- readthedocs/builds/tasks.py\n", "+++ readthedocs/builds/tasks.py\n", "@@ -223,13 +223,13 @@\n", "     default_retry_delay=60,\n", "     queue='web'\n", " )\n", " def sync_versions_task(project_pk, tags_data, branches_data, **kwargs):\n", "     \"\"\"\n", "-    Sync the version data in the repo (on the build server).\n", "-\n", "-    Version data in the repo is synced with what we have in the database.\n", "+    Sync the version data in the repo (from build server) into our database.\n", "+\n", "+\tCreates new Version objects for those tags/branches that we don't have tracked in our database and deletes Version objects for tags/branches that don't exists anymore in the repository.\n", " \n", "     :param tags_data: List of dictionaries with ``verbose_name`` and ``identifier``.\n", "     :param branches_data: Same as ``tags_data`` but for branches.\n", "     :returns: the identifiers for the versions that have been deleted.\n", "     \"\"\"\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/codenotary/immudb/pull/1157#discussion_r845929150\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: set explicit values for featureState\n", "  REVISED (SHORT) INSTRUCTION: explicitly set values\n", "         ORIGINAL INSTRUCTION: Since this i persisted I think it's better to set explicit values here to avoid conincidental reordering / modifications of those values:\n", "\n", "--- pkg/server/db_options.go\n", "+++ pkg/server/db_options.go\n", "@@ -68,13 +68,13 @@\n", " }\n", " \n", " type featureState int\n", " \n", " const (\n", "-\tunspecifiedState featureState = iota\n", "-\tenabledState\n", "-\tdisabledState\n", "+\tunspecifiedState featureState = 0\n", "+\tenabledState featureState = 1\n", "+\tdisabledState featureState = 2\n", " )\n", " \n", " func (fs featureState) isEnabled() bool {\n", " \treturn fs == unspecifiedState || fs == enabledState\n", " }\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/badges/shields/pull/5262#discussion_r463982078\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: remove Joi.alternatives()\n", "  REVISED (SHORT) INSTRUCTION: remove alternatives\n", "         ORIGINAL INSTRUCTION: No need for `alternatives` now\n", "\n", "--- services/gitlab/gitlab-coverage.service.js\n", "+++ services/gitlab/gitlab-coverage.service.js\n", "@@ -4,13 +4,11 @@\n", " const { coveragePercentage } = require('../color-formatters')\n", " const { optionalUrl } = require('../validators')\n", " const { BaseSvgScrapingService, NotFound } = require('..')\n", " \n", " const badgeSchema = Joi.object({\n", "-  message: Jo<PERSON>.alternatives()\n", "-    .try(Joi.string().regex(/^([0-9]+\\.[0-9]+%)|unknown$/))\n", "-    .required(),\n", "+  message: Joi.string().regex(/^([0-9]+\\.[0-9]+%)|unknown$/).required(),\n", " }).required()\n", " \n", " const queryParamSchema = Joi.object({\n", "   gitlab_url: optionalUrl,\n", " }).required()\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/bottlerocket-os/bottlerocket-ecs-updater/pull/38#discussion_r623257692\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: remove instanceID parameter\n", "  REVISED (SHORT) INSTRUCTION: remove unused parameter\n", "         ORIGINAL INSTRUCTION: It doesn't look like the `instanceID` parameter is used\n", "\n", "--- updater/aws.go\n", "+++ updater/aws.go\n", "@@ -240,11 +240,11 @@\n", " \treturn false\n", " }\n", " \n", " // getActiveVersion unmarshals GetCommandInvocation output to determine the active version of a Bottlerocket instance.\n", " // Takes an SSM Command ID and EC2ID as parameters and returns the active version in use.\n", "-func getActiveVersion(commandOutput []byte, instanceID string) (string, error) {\n", "+func getActiveVersion(commandOutput []byte) (string, error) {\n", " \ttype version struct {\n", " \t\tVersion string `json:\"version\"`\n", " \t}\n", " \n", " \ttype image struct {\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/BabylonJS/Babylon.js/pull/11070#discussion_r710266162\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: remove new keyword\n", "  REVISED (SHORT) INSTRUCTION: remove new RegExp\n", "         ORIGINAL INSTRUCTION: no need to create a new RegExp if you are already using `/.../`\n", "\n", "--- src/Engines/WebGPU/webgpuShaderProcessorsGLSL.ts\n", "+++ src/Engines/WebGPU/webgpuShaderProcessorsGLSL.ts\n", "@@ -39,11 +39,11 @@\n", "         this._textureArrayProcessing.length = 0;\n", "     }\n", " \n", "     public varyingProcessor(varying: string, isFragment: boolean, preProcessors: { [key: string]: string }, processingContext: Nullable<ShaderProcessingContext>) {\n", "         this._preProcessors = preProcessors;\n", "-        const varyingRegex = new RegExp(/\\s*varying\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm);\n", "+        const varyingRegex = /\\s*varying\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm;\n", "         const match = varyingRegex.exec(varying);\n", "         if (match != null) {\n", "             const varyingType = match[1];\n", "             const name = match[2];\n", "             let location: number;\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/sopel-irc/sopel/pull/1479#discussion_r277108561\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: reference issue number in skip reason\n", "  REVISED (SHORT) INSTRUCTION: add issue reference\n", "         ORIGINAL INSTRUCTION: Makes sense to reference the issue number, since test output is for developers.\n", "\n", "--- test/test_bot.py\n", "+++ test/test_bot.py\n", "@@ -31,11 +31,11 @@\n", "     plugin.unregister(sopel)\n", "     # And now it must raises\n", "     with pytest.raises(plugins.exceptions.PluginNotRegistered):\n", "         sopel.remove_plugin(plugin, [], [], [], [])\n", " \n", "-@pytest.mark.skip(reason='Skip until JobScheduler can be stopped')\n", "+@pytest.mark.skip(reason='Skip until JobScheduler can be stopped (#1557)')\n", " def test_reload_plugin_unregistered_plugin(tmpconfig):\n", "     sopel = bot.Sopel(tmpconfig, daemon=False)\n", "     plugin = sopel._plugins.get('coretasks')\n", " \n", "     assert plugin is not None, 'coretasks should be always loaded'\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/AzureAD/microsoft-authentication-library-common-for-android/pull/1367#discussion_r630400650\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: fix the length of copied array\n", "  REVISED (SHORT) INSTRUCTION: fix the length of copied array\n", "         ORIGINAL INSTRUCTION: This is probably breaking your tests\n", "\n", "--- common4j/src/main/com/microsoft/identity/common/java/net/HttpRequest.java\n", "+++ common4j/src/main/com/microsoft/identity/common/java/net/HttpRequest.java\n", "@@ -49,11 +49,11 @@\n", "     public byte[] getRequestContent() {\n", "         if (mRequestContent == null) {\n", "             return null;\n", "         }\n", "         \n", "-        return Arrays.copyOf(mRequestContent, mRequestMethod.length());\n", "+        return Arrays.copyOf(mRequestContent, mRequestContent.length);\n", "     }\n", " \n", "     @Getter\n", "     @Accessors(prefix = \"m\")\n", "     private final String mRequestContentType;\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/django-ses/django-ses/pull/276#discussion_r1169206047\n", "             REVISION VERDICT: GOOD\n", "REVISED (VERBOSE) INSTRUCTION: change from_email to my_default_from\n", "  REVISED (SHORT) INSTRUCTION: use my_default_from\n", "         ORIGINAL INSTRUCTION: And this is the correct behaviour\n", "\n", "--- tests/test_backend.py\n", "+++ tests/test_backend.py\n", "@@ -334,13 +334,13 @@\n", "     def tearDown(self):\n", "         # Empty outbox everytime test finishes\n", "         FakeSESConnection.outbox = []\n", "     \n", "     def test_from_email(self):\n", "-        settings.AWS_SES_FROM_EMAIL = \"<EMAIL>\"\n", "-        send_mail('subject', 'body', '<EMAIL>', ['<EMAIL>'])\n", "-        self.assertEqual(self.outbox.pop()['Source'], '<EMAIL>')\n", "+        settings.AWS_SES_FROM_EMAIL = \"<EMAIL>\"\n", "+        send_mail('subject', 'body', '<EMAIL>', ['<EMAIL>'])\n", "+        self.assertEqual(self.outbox.pop()['Source'], '<EMAIL>')\n", "     \n", "     def test_return_path(self):\n", "         settings.USE_SES_V2 = True\n", "         settings.AWS_SES_RETURN_PATH = \"<EMAIL>\"\n", "         send_mail('subject', 'body', '<EMAIL>', ['<EMAIL>'])\n", "====================================================================================================\n", "                   REVIEW URL: https://github.com/ynput/OpenPype/pull/4195#discussion_r1070446246\n", "             REVISION VERDICT: UNCLEAR\n", "REVISED (VERBOSE) INSTRUCTION: add safe-guard for host_name\n", "  REVISED (SHORT) INSTRUCTION: safe-guard host_name\n", "         ORIGINAL INSTRUCTION: I feel like this helper method might be better of 'safe-guarding' a host maybe not having implement `imagio` settings yet and do: `project_settings.get(host_name, {}).get(\"imageio\", {})`\n", "\n", "--- openpype/pipeline/colorspace.py\n", "+++ openpype/pipeline/colorspace.py\n", "@@ -457,8 +457,8 @@\n", "     Returns:\n", "         tuple[dict, dict]: image io settings for global and host\n", "     \"\"\"\n", "     # get image io from global and host_name\n", "     imageio_global = project_settings[\"global\"][\"imageio\"]\n", "-    imageio_host = project_settings[host_name][\"imageio\"]\n", "+    imageio_host = project_settings.get(host_name, {}).get(\"imageio\", {})\n", " \n", "     return imageio_global, imageio_host\n", "====================================================================================================\n"]}], "source": ["import numpy as np\n", "\n", "llmfixed_filtered_suggested_edit = []\n", "n_failed_to_parse_llm = 0\n", "\n", "np.random.seed(31415)\n", "ids = np.random.permutation(len(filtered_suggested_edit))\n", "\n", "for index in ids[1000:1020]:\n", "    sample = filtered_suggested_edit[index]\n", "\n", "    llm_result = query_remote_model(sample)\n", "    if len(llm_result) != 3:\n", "        n_failed_to_parse_llm += 1\n", "        continue\n", "\n", "    verbose_instruction, short_instruction, label = llm_result\n", "    if len(verbose_instruction) < len(short_instruction):\n", "        verbose_instruction, short_instruction = short_instruction, verbose_instruction\n", "\n", "    # if label == UNCLEAR:\n", "    #     continue\n", "\n", "    print('                   REVIEW URL:', sample['html_url'])\n", "    print('             REVISION VERDICT:', label)\n", "    print('REVISED (VERBOSE) INSTRUCTION:', verbose_instruction)\n", "    print('  REVISED (SHORT) INSTRUCTION:', short_instruction)\n", "    print('         ORIGINAL INSTRUCTION:', sample['original_instruction'])   \n", "    print()\n", "    print(get_diff(sample, n_context=5))\n", "    print('=' * 100)            \n", "\n", "    fixed_sample = {\n", "        'revised_verbose_instruction': verbose_instruction,\n", "        'revised_short_instruction': short_instruction,\n", "        'revision_verdict': label,\n", "    }\n", "    fixed_sample.update(sample)\n", "    llmfixed_filtered_suggested_edit.append(fixed_sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "llmfixed_filtered_suggested_edit = []\n", "\n", "np.random.seed(31415)\n", "ids = np.random.permutation(len(filtered_suggested_edit))\n", "\n", "with tqdm.tqdm(total=len(filtered_suggested_edit)) as pbar:    \n", "    for index in ids:\n", "        sample = filtered_suggested_edit[index]\n", "        pbar.update(1)\n", "\n", "        fixed_instruction = query_remote_model(sample)\n", "        if fixed_instruction is None:\n", "            continue\n", "\n", "        fixed_sample = {'instruction': fixed_instruction}\n", "        fixed_sample.update(sample)\n", "        llmfixed_filtered_suggested_edit.append(fixed_sample)\n", "\n", "        pbar.set_postfix({\"mined_suggestions\": len(llmfixed_filtered_suggested_edit)})"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  ID: 482492569\n", "         INSTRUCTION: make sure labels is not None\n", "ORIGINAL INSTRUCTION: These are required fields in the GQL schema, so you can rely on them having expected types\n", "          REVIEW URL: https://github.com/PrefectHQ/server/pull/58#discussion_r482492569\n", "\n", "--- src/prefect_server/graphql/agents.py\n", "+++ src/prefect_server/graphql/agents.py\n", "@@ -13,7 +13,7 @@\n", " ) -> dict:\n", "     agent_id = await api.agents.register_agent_instance(\n", "         tenant_id=input.get(\"tenant_id\"),\n", "-        labels=input.get(\"labels\", []) or [],\n", "+        labels=input[\"labels\"],\n", "         agent_id=input.get(\"agent_id\"),\n", "         type=input[\"type\"],\n", "         name=input.get(\"name\"),\n", "====================================================================================================\n", "                  ID: 406747286\n", "         INSTRUCTION: append scenario\n", "ORIGINAL INSTRUCTION: this is basically appending new Scenario, right?\n", "IF so:\n", "          REVIEW URL: https://github.com/kyma-incubator/compass/pull/1152#discussion_r406747286\n", "\n", "--- components/director/internal/domain/scenarioassignment/engine.go\n", "+++ components/director/internal/domain/scenarioassignment/engine.go\n", "@@ -84,7 +84,7 @@\n", " \n", " \treturn str.<PERSON><PERSON><PERSON><PERSON>(set), nil\n", " }\n", "-func (e *engine) scenariosDiff(scenarios []interface{}, diffScenario string) ([]string, error) {\n", "+func (e *engine) appendScenario(scenarios []interface{}, scenario string) ([]string, error) {\n", " \tvar newScenarios []string\n", " \tfor _, scenario := range scenarios {\n", " \t\toutput, ok := scenario.(string)\n", "====================================================================================================\n", "                  ID: 326071004\n", "         INSTRUCTION: use Text throughout\n", "ORIGINAL INSTRUCTION: Just to be consistent between `Text` and `str` throughout the code.\n", "          REVIEW URL: https://github.com/RasaHQ/rasa/pull/4483#discussion_r326071004\n", "\n", "--- rasa/nlu/utils/spacy_utils.py\n", "+++ rasa/nlu/utils/spacy_utils.py\n", "@@ -143,7 +143,7 @@\n", "     @staticmethod\n", "     def filter_training_samples_by_content(\n", "         indexed_training_samples: List[Tuple[int, Text]]\n", "-    ) -> Tuple[List[Tuple[int, str]], List[Tuple[int, str]]]:\n", "+    ) -> Tuple[List[Tuple[int, Text]], List[Tuple[int, Text]]]:\n", "         \"\"\"Separates empty training samples from content bearing ones.\"\"\"\n", " \n", "         docs_to_pipe = list(\n", "====================================================================================================\n", "                  ID: 1140697215\n", "         INSTRUCTION: clarify the error message\n", "ORIGINAL INSTRUCTION: If I've misunderstood what was meant, clarification is welcome.\n", "          REVIEW URL: https://github.com/CommunitySolidServer/CommunitySolidServer/pull/1595#discussion_r1140697215\n", "\n", "--- src/util/errors/NotModifiedHttpError.ts\n", "+++ src/util/errors/NotModifiedHttpError.ts\n", "@@ -5,7 +5,7 @@\n", " const BaseHttpError = generateHttpErrorClass(304, 'NotModifiedHttpError');\n", " \n", " /**\n", "- * An error thrown when a request conflict with current state of the server.\n", "+ * An error is thrown when a request conflicts with the current state of the server.\n", "  */\n", " export class NotModifiedHttpError extends BaseHttpError {\n", "   public constructor(message?: string, options?: HttpErrorOptions) {\n", "====================================================================================================\n", "                  ID: 760288884\n", "         INSTRUCTION: remove extra parameter\n", "ORIGINAL INSTRUCTION: I think this extra parameter came from an intermediate version of the code, but doesn't look like it gets used here:\n", "          REVIEW URL: https://github.com/NVIDIA-Merlin/NVTabular/pull/1272#discussion_r760288884\n", "\n", "--- nvtabular/loader/tensorflow.py\n", "+++ nvtabular/loader/tensorflow.py\n", "@@ -40,7 +40,7 @@\n", " \n", " \n", " def _validate_dataset(\n", "-    paths_or_dataset, batch_size, buffer_size, engine, reader_kwargs, dataclass=None\n", "+    paths_or_dataset, batch_size, buffer_size, engine, reader_kwargs\n", " ):\n", "     # TODO: put this in parent class and allow\n", "     # torch dataset to leverage as well?\n", "====================================================================================================\n", "                  ID: 1148522839\n", "         INSTRUCTION: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n", "ORIGINAL INSTRUCTION: ![image](https://user-images.githubusercontent.com/77577746/227769210-63768f4f-4958-4689-b187-c39c94e152b8.png)\n", "`getConfig` returns a boolean, `localStorage.getItem` returns a string, which evaluates to `true` on the string `\"false\"`\n", "![image](https://user-images.githubusercontent.com/77577746/227769729-a4898da4-719f-4e96-8e68-db0995af7e26.png)\n", "          REVIEW URL: https://github.com/spicetify/spicetify-cli/pull/2217#discussion_r1148522839\n", "\n", "--- CustomApps/lyrics-plus/index.js\n", "+++ CustomApps/lyrics-plus/index.js\n", "@@ -48,7 +48,7 @@\n", " \t\t[\"font-size\"]: localStorage.getItem(\"lyrics-plus:visual:font-size\") || \"32\",\n", " \t\t[\"translation-mode:japanese\"]: localStorage.getItem(\"lyrics-plus:visual:translation-mode:japanese\") || \"furigana\",\n", " \t\t[\"translation-mode:chinese\"]: localStorage.getItem(\"lyrics-plus:visual:translation-mode:chinese\") || \"cn\",\n", "-\t\t[\"translate\"]: localStorage.getItem(\"lyrics-plus:visual:translate\") || false,\n", "+\t\t[\"translate\"]: getConfig(\"lyrics-plus:visual:translate\", false),\n", " \t\t[\"ja-detect-threshold\"]: localStorage.getItem(\"lyrics-plus:visual:ja-detect-threshold\") || \"40\",\n", " \t\t[\"hans-detect-threshold\"]: localStorage.getItem(\"lyrics-plus:visual:hans-detect-threshold\") || \"40\",\n", " \t\t[\"fade-blur\"]: getConfig(\"lyrics-plus:visual:fade-blur\"),\n", "====================================================================================================\n", "                  ID: 494252994\n", "         INSTRUCTION: use pathlib\n", "ORIGINAL INSTRUCTION: Since `component_path` is already a path type, it should be possible to do\n", "          REVIEW URL: https://github.com/projectsyn/commodore/pull/190#discussion_r494252994\n", "\n", "--- commodore/component/compile.py\n", "+++ commodore/component/compile.py\n", "@@ -92,7 +92,7 @@\n", " \n", "         # Fetch Jsonnet libs\n", "         fetch_jsonnet_libs(config, libs)\n", "-        if os.path.exists(os.path.join(component_path, 'jsonnetfile.json')):\n", "+        if (component_path / 'jsonnetfile.json').exists():\n", "             fetch_jsonnet_libraries(component_path)\n", " \n", "         # Compile component\n", "====================================================================================================\n", "                  ID: 363710108\n", "         INSTRUCTION: use str() to convert Path object\n", "ORIGINAL INSTRUCTION: We may handle `Path`s cleanly... not sure.\n", "          REVIEW URL: https://github.com/nipy/nipype/pull/3148#discussion_r363710108\n", "\n", "--- nipype/pipeline/plugins/tests/test_base.py\n", "+++ nipype/pipeline/plugins/tests/test_base.py\n", "@@ -67,7 +67,7 @@\n", "     wf.base_dir = tmpdir\n", "     wf.config['execution']['remove_node_directories'] = 'true'\n", "     wf.config['execution']['stop_on_first_crash'] = 'false'\n", "-    wf.config['logging']['crashdump_dir'] = tmpdir\n", "+    wf.config['logging']['crashdump_dir'] = str(tmp_path)\n", "     res = wf.run(plugin='MultiProc')\n", " \n", "     assert os.path.isdir(Path(tmpdir)/'test/functor') is False\n", "====================================================================================================\n", "                  ID: 476481976\n", "         INSTRUCTION: use consistent variable naming\n", "ORIGINAL INSTRUCTION: for consistency with the rest of the code\n", "          REVIEW URL: https://github.com/salto-io/salto/pull/1348#discussion_r476481976\n", "\n", "--- packages/salesforce-adapter/src/filters/custom_feed_filter.ts\n", "+++ packages/salesforce-adapter/src/filters/custom_feed_filter.ts\n", "@@ -53,7 +53,7 @@\n", " \n", " const filterCreator: FilterCreator = ({ client, config }) => ({\n", "   onFetch: async (elements: Element[]): Promise<ConfigChangeSuggestion[]> => {\n", "-    const customFeedFilterMetadataValue = findObjectType(\n", "+    const customFeedFilterType = findObjectType(\n", "       elements, CUSTOM_FEED_FILTER_METADATA_TYPE_ID\n", "     )\n", "     if (customFeedFilterMetadataValue === undefined) {\n", "====================================================================================================\n", "                  ID: 1053584951\n", "         INSTRUCTION: add comment\n", "ORIGINAL INSTRUCTION: I'm okay with merging as soon as we add a comment or two here, thanks!\n", "          REVIEW URL: https://github.com/celestiaorg/celestia-app/pull/1126#discussion_r1053584951\n", "\n", "--- x/blob/keeper/params.go\n", "+++ x/blob/keeper/params.go\n", "@@ -22,6 +22,8 @@\n", " \n", " // MinSquareSize returns the MinSquareSize param\n", " func (k Keeper) MinSquareSize(ctx sdk.Context) (res uint32) {\n", "+\t// use the default size for the first block so that we return a value on the\n", "+\t// first block in PrepareProposal\n", " \tif ctx.<PERSON>Header().Height < 1 {\n", " \t\treturn appconsts.DefaultMinSquareSize\n", " \t}\n", "====================================================================================================\n", "                  ID: 1123242701\n", "         INSTRUCTION: use f-string\n", "ORIGINAL INSTRUCTION: This hurt my brain a little 😄 how about\n", "          REVIEW URL: https://github.com/mystic-ai/pipeline/pull/215#discussion_r1123242701\n", "\n", "--- pipeline/console/environments.py\n", "+++ pipeline/console/environments.py\n", "@@ -21,9 +21,7 @@\n", " def _get_environment(name_or_id: str, by_name=False) -> EnvironmentGet:\n", "     remote_service = PipelineCloud(verbose=False)\n", "     remote_service.authenticate()\n", "-    url_config = [\"/v2/environments\", name_or_id]\n", "-    separator = \"/\" if not by_name else \"/by-name/\"\n", "-    url = separator.join(url_config)\n", "+    url = f\"/v2/environments/by-name/{name_or_id}\" if by_name else f\"/v2/environments/{name_or_id}\"\n", " \n", "     environment_information = EnvironmentGet.parse_obj(remote_service._get(url))\n", " \n", "====================================================================================================\n", "                  ID: 783200358\n", "         INSTRUCTION: use meaningful variable name\n", "ORIGINAL INSTRUCTION: Feel free to use other names, but let's try to use something meaningful\n", "          REVIEW URL: https://github.com/rsksmart/rskj/pull/1685#discussion_r783200358\n", "\n", "--- rskj-core/src/main/java/co/rsk/peg/BridgeSupport.java\n", "+++ rskj-core/src/main/java/co/rsk/peg/BridgeSupport.java\n", "@@ -2503,7 +2503,7 @@\n", "     protected void checktimeLockExpiration() {\n", "         if (activations.isActive(RSKIP264)) {\n", "             long height = provider.getNextUtxoExpirationCheckpointHeight().orElse(0L);\n", "-            long timestamp = provider.getNextUtxoExpirationCheckpointTimestamp().orElse(0L);\n", "+            long nextUtxoExpirationCheckpointTimestamp = provider.getNextUtxoExpirationCheckpointTimestamp().orElse(0L);\n", "             if ((rskExecutionBlock.getNumber() >= height) || (rskExecutionBlock.getTimestamp() >= timestamp)) {\n", "                 // TODO: Select Utxos, Build Transaction, Update CheckPoints\n", "             }\n", "====================================================================================================\n", "                  ID: 611226616\n", "         INSTRUCTION: update context description\n", "ORIGINAL INSTRUCTION: I think it sounds simpler this way\n", "          REVIEW URL: https://github.com/balancer/balancer-v2-monorepo/pull/464#discussion_r611226616\n", "\n", "--- test/pools/BasePool.test.ts\n", "+++ test/pools/BasePool.test.ts\n", "@@ -156,7 +156,7 @@\n", "               await expect(pool.connect(sender).setSwapFee(MAX_SWAP_FEE.add(1))).to.be.revertedWith('MAX_SWAP_FEE');\n", "             });\n", "           });\n", "-          context('when the new swap fee is not above the minimum', () => {\n", "+          context('when the new swap fee is below the minimum', () => {\n", "             it('reverts', async () => {\n", "               await expect(pool.connect(sender).setSwapFee(MIN_SWAP_FEE.sub(1))).to.be.revertedWith('MIN_SWAP_FEE');\n", "             });\n", "====================================================================================================\n", "                  ID: 936685834\n", "         INSTRUCTION: refactor calculation\n", "ORIGINAL INSTRUCTION: I found this hard to read (particularly splitting the calculation with a comment), would help to rewrite to something like the following:\n", "          REVIEW URL: https://github.com/yt-project/yt/pull/2998#discussion_r936685834\n", "\n", "--- yt/frontends/artio/data_structures.py\n", "+++ yt/frontends/artio/data_structures.py\n", "@@ -339,14 +339,11 @@\n", "         Mostly useful for cases where we have irregularly spaced or structured\n", "         grids.\n", "         \"\"\"\n", "-        dds = self.ds.domain_width[(axes,)] / (\n", "-            self.ds.domain_dimensions[\n", "-                axes,\n", "-            ]\n", "-            # This may need an additional refinement factor conversion for\n", "-            # non-refine-by-2 cases.\n", "-            * self.ds.refine_by ** ires[:, None]\n", "-        )\n", "+        active_dims = self.ds.domain_dimensions[axes,]\n", "+        # This may need an additional refinement factor conversion for\n", "+        # non-refine-by-2 cases.\n", "+        refine_factor = self.ds.refine_by ** ires[:, None]    \n", "+        dds = self.ds.domain_width[(axes,)] / (active_dims * refine_factor)\n", "         pos = (0.5 + icoords) * dds + self.ds.domain_left_edge[\n", "             axes,\n", "         ]\n", "====================================================================================================\n", "                  ID: 450671572\n", "         INSTRUCTION: simplify error handling\n", "ORIGINAL INSTRUCTION: NIT: you can probably simplify this a bit:\n", "          REVIEW URL: https://github.com/gruntwork-io/terratest/pull/569#discussion_r450671572\n", "\n", "--- modules/terraform/output.go\n", "+++ modules/terraform/output.go\n", "@@ -280,11 +280,7 @@\n", " \tif err != nil {\n", " \t\treturn err\n", " \t}\n", "-\tif err := json.Unmarshal([]byte(out), &v); err != nil {\n", "-\t\treturn err\n", "-\t}\n", "-\n", "-\treturn nil\n", "+\treturn json.Unmar<PERSON>l([]byte(out), &v)\n", " }\n", " \n", " // OutputForKeysE calls terraform output for the given key list and returns values as a map.\n", "====================================================================================================\n", "                  ID: 688609034\n", "         INSTRUCTION: run test with both numpy and arrow arrays\n", "ORIGINAL INSTRUCTION: This will execute the test twice, one with numpy arrays, once with arrow arrays.\n", "          REVIEW URL: https://github.com/vaexio/vaex/pull/1509#discussion_r688609034\n", "\n", "--- tests/dataframe_protocol_test.py\n", "+++ tests/dataframe_protocol_test.py\n", "@@ -2,8 +2,8 @@\n", " import numpy as np\n", " import pyarrow as pa\n", " from vaex.dataframe_protocol import from_dataframe_to_vaex, _DtypeKind\n", "-def test_float_only():\n", "-\tdf = vaex.from_arrays(x=np.array([1.5, 2.5, 3.5]), y=np.array([9.2, 10.5, 11.8]))\n", "+def test_float_only(df_facory):\n", "+\tdf = df_facory(x=[1.5, 2.5, 3.5], y=[9.2, 10.5, 11.8])\n", " \tdf2 = from_dataframe_to_vaex(df)\n", " \tassert  df2.x.tolist() == df.x.tolist()\n", " \tassert  df2.y.tolist() == df.y.tolist()\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: use nodecg-io\n", "ORIGINAL INSTRUCTION: I would just use `nodecg-io` as it is clearer to the user and there isn't another nodecg-io module that will send payloads.\n", "          REVIEW URL: https://github.com/codeoverflow-org/nodecg-io/pull/83#discussion_r*********\n", "\n", "--- nodecg-io-sacn-sender/extension/index.ts\n", "+++ nodecg-io-sacn-sender/extension/index.ts\n", "@@ -56,7 +56,7 @@\n", "             sendPayload(payload: Record<number, number>): Promise<void> {\n", "                 return sacn.send({\n", "                     payload: payload,\n", "-                    sourceName: \"nodecg-io-scan-sender\",\n", "+                    sourceName: \"nodecg-io\",\n", "                     priority: 100,\n", "                 });\n", "             },\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: set min and max values for multiple devices\n", "ORIGINAL INSTRUCTION: I assume these need to be set for it to be working with multiple devices\n", "          REVIEW URL: https://github.com/Lightning-AI/torchmetrics/pull/556#discussion_r*********\n", "\n", "--- torchmetrics/wrappers/minmax.py\n", "+++ torchmetrics/wrappers/minmax.py\n", "@@ -70,8 +70,8 @@\n", "     ):\n", "         super().__init__(dist_sync_on_step=dist_sync_on_step)\n", "         self._base_metric = base_metric\n", "-        self.add_state(\"min_val\", default=torch.tensor(min_bound_init))\n", "-        self.add_state(\"max_val\", default=torch.tensor(max_bound_init))\n", "+        self.add_state(\"min_val\", default=torch.tensor(min_bound_init), dist_reduce_fx='min')\n", "+        self.add_state(\"max_val\", default=torch.tensor(max_bound_init), dist_reduce_fx='max')\n", "         self.min_bound_init = min_bound_init\n", "         self.max_bound_init = max_bound_init\n", " \n", "====================================================================================================\n", "                  ID: 1103724846\n", "         INSTRUCTION: format the code for better readability\n", "ORIGINAL INSTRUCTION: format the code for better readability\n", "          REVIEW URL: https://github.com/berstend/puppeteer-extra/pull/615#discussion_r1103724846\n", "\n", "--- packages/puppeteer-extra-plugin-stealth/evasions/user-agent-override/index.js\n", "+++ packages/puppeteer-extra-plugin-stealth/evasions/user-agent-override/index.js\n", "@@ -128,7 +128,7 @@\n", " \n", "       return greasedBrandVersionList\n", "     }\n", "-\tconst _padPlatformVersion = (version) => version+((version.split('.').length<=2)?'.0':'')\n", "+    const _padPlatformVersion = (version) => version + ((version.split('.').length <= 2) ? '.0' : '')\n", " \n", "     // Return OS version\n", "     const _getPlatformVersion = () => {\n", "====================================================================================================\n", "                  ID: 268220494\n", "         INSTRUCTION: use truthy check\n", "ORIGINAL INSTRUCTION: Truthy value check should be sufficient\n", "          REVIEW URL: https://github.com/Wikidata/soweego/pull/239#discussion_r268220494\n", "\n", "--- soweego/linker/feature_extraction.py\n", "+++ soweego/linker/feature_extraction.py\n", "@@ -436,7 +436,7 @@\n", "                 LOGGER.debug(\n", "                     \"Can't compare occupations, the wikidata value is null. Pair: %s\", pair)\n", "                 return np.nan\n", "-            if t_item == set():\n", "+            if not t_item:\n", "                 LOGGER.debug(\n", "                     \"Can't compare occupations, the target value is null. Pair: %s\", pair)\n", "                 return np.nan\n", "====================================================================================================\n", "                  ID: 823334802\n", "         INSTRUCTION: delete mapToArray\n", "ORIGINAL INSTRUCTION: Can probably skip a couple steps here, in which case you can delete `mapToArray`\n", "          REVIEW URL: https://github.com/adobe/react-spectrum/pull/2883#discussion_r823334802\n", "\n", "--- packages/@react-stately/table/src/useTableColumnResizeState.ts\n", "+++ packages/@react-stately/table/src/useTableColumnResizeState.ts\n", "@@ -162,8 +162,7 @@\n", " \n", "     // when getting recalculated columns above, the column being resized is not considered \"recalculated\"\n", "     // so we need to add it to the list of affected columns\n", "-    let allAffectedColumns = new Map<Key, number>([[column.key, newWidth], ...recalculatedColumnWidths]);\n", "-    return mapToArray(allAffectedColumns);\n", "+    return [[column.key, newWidth], ...recalculatedColumnWidths].map([key, width] => {key, width});\n", "   }\n", " \n", "   function getColumnWidth(key: Key): number {\n", "====================================================================================================\n", "                  ID: 475277007\n", "         INSTRUCTION: use parseInt\n", "ORIGINAL INSTRUCTION: same as crafting\n", "          REVIEW URL: https://github.com/oldschoolgg/oldschoolbot/pull/513#discussion_r475277007\n", "\n", "--- src/lib/filterables.ts\n", "+++ src/lib/filterables.ts\n", "@@ -260,7 +260,7 @@\n", " ]);\n", " \n", " const fletchingItems = Fletching.Fletchables.flatMap(item =>\n", "-\tObject.keys(item.inputItems).map(key => parseInt(key))\n", "+\tObject.keys(item.inputItems).map(key => parseInt(key)).concat(item.id)\n", " );\n", " \n", " const fletchingItemsSet = [...new Set(fletchingItems)];\n", "====================================================================================================\n", "                  ID: 847794027\n", "         INSTRUCTION: rename `router` to `target-id`\n", "ORIGINAL INSTRUCTION: I think calling this option `router` might be confusing,\n", "          REVIEW URL: https://github.com/softlayer/softlayer-python/pull/1614#discussion_r847794027\n", "\n", "--- SoftLayer/CLI/globalip/assign.py\n", "+++ SoftLayer/CLI/globalip/assign.py\n", "@@ -12,7 +12,7 @@\n", " @click.option('--target',\n", "               help='See SLDN docs. '\n", "                    'E.g <PERSON>Layer_Network_Subnet_IpAddress, SoftLayer_Hardware_Server,SoftLayer_Virtual_Guest')\n", "-@click.option('--router', help='An appropriate identifier for the specified $type. Some types have multiple identifier')\n", "+@click.option('--target-id', help='The identifier for the destination resource to route this subnet to. ')\n", " @environment.pass_env\n", " def cli(env, identifier, target, router):\n", "     \"\"\"Assigns the global IP to a target.\"\"\"\n", "====================================================================================================\n", "                  ID: 996986058\n", "         INSTRUCTION: use map increment\n", "ORIGINAL INSTRUCTION: Nit. It is enough.\n", "          REVIEW URL: https://github.com/liqotech/liqo/pull/1460#discussion_r996986058\n", "\n", "--- pkg/telemetry/builder.go\n", "+++ pkg/telemetry/builder.go\n", "@@ -100,11 +100,7 @@\n", " \t\t\tklog.Warningf(\"Pod %s/%s has no node assigned\", pod.Namespace, pod.Name)\n", " \t\t\tcontinue\n", " \t\t}\n", "-\t\tif _, ok := nodePodBucket[pod.Spec.NodeName]; !ok {\n", "-\t\t\tnodePodBucket[pod.Spec.NodeName] = 1\n", "-\t\t} else {\n", "-\t\t\tnodePodBucket[pod.Spec.NodeName]++\n", "-\t\t}\n", "+\t\tnodePodBucket[pod.Spec.NodeName]++\n", " \t}\n", " \n", " \tvirtualNodes, err := liqogetters.ListVirtualNodes(ctx, c.Client)\n", "====================================================================================================\n", "                  ID: 382074092\n", "         INSTRUCTION: use webpack alias\n", "ORIGINAL INSTRUCTION: We have a webpack alias for this directory - `#data`\n", "          REVIEW URL: https://github.com/bbc/simorgh/pull/5622#discussion_r382074092\n", "\n", "--- src/app/routes/article/getInitialData/index.test.js\n", "+++ src/app/routes/article/getInitialData/index.test.js\n", "@@ -1,5 +1,5 @@\n", " import getInitialData from '.';\n", "-import articleJson from '../../../../../data/pidgin/articles/cwl08rd38l6o.json';\n", "+import articleJson from '#data/pidgin/articles/cwl08rd38l6o.json';\n", " \n", " fetch.mockResponse(JSON.stringify(articleJson));\n", " \n", "====================================================================================================\n", "                  ID: 789120357\n", "         INSTRUCTION: skip test in windows\n", "ORIGINAL INSTRUCTION: let's skip the test in windows instead\n", "          REVIEW URL: https://github.com/npm/cli/pull/4258#discussion_r789120357\n", "\n", "--- workspaces/arborist/test/shrinkwrap.js\n", "+++ workspaces/arborist/test/shrinkwrap.js\n", "@@ -1627,5 +1627,5 @@\n", "         fs.chmod<PERSON><PERSON>(dir, '777')\n", "       }\n", "     }\n", "-  })\n", "-})\n", "+  }, { skip: process.platform === 'win32' ? 'skip chmod in windows' : false)\n", "+})\n", "====================================================================================================\n", "                  ID: 228244414\n", "         INSTRUCTION: remove container prefix\n", "ORIGINAL INSTRUCTION: If we're going to be putting this in a package called \"container\" these container prefixes are superfluous\n", "          REVIEW URL: https://github.com/tilt-dev/tilt/pull/619#discussion_r228244414\n", "\n", "--- internal/container/container.go\n", "+++ internal/container/container.go\n", "@@ -5,7 +5,7 @@\n", " \n", " \t\"github.com/docker/distribution/reference\"\n", " )\n", "-type ContainerID string\n", "+type ID string\n", " type ContainerName string\n", " \n", " func (cID ContainerID) Empty() bool    { return cID.String() == \"\" }\n", "====================================================================================================\n", "                  ID: 586306335\n", "         INSTRUCTION: use `this.ownerDocument` to get the document\n", "ORIGINAL INSTRUCTION: Instead of using global `window`, we make it clear the relationship between the component DOM and the window object:\n", "          REVIEW URL: https://github.com/carbon-design-system/carbon-for-ibm-dotcom/pull/5326#discussion_r586306335\n", "\n", "--- packages/web-components/src/components/back-to-top/back-to-top.ts\n", "+++ packages/web-components/src/components/back-to-top/back-to-top.ts\n", "@@ -40,7 +40,7 @@\n", "    */\n", "   // eslint-disable-next-line class-methods-use-this\n", "   private _handleOnClick() {\n", "-    window.scrollTo({ top: 0, behavior: 'smooth' });\n", "+    this.ownerDocument!.defaultView!.scrollTo({ top: 0, behavior: 'smooth' });\n", "   }\n", " \n", "   /**\n", "====================================================================================================\n", "                  ID: 799209723\n", "         INSTRUCTION: remove logging the error\n", "ORIGINAL INSTRUCTION: logging the error is redundant as the returned error will be logged.\n", "          REVIEW URL: https://github.com/banzaicloud/koperator/pull/760#discussion_r799209723\n", "\n", "--- pkg/resources/cruisecontrol/configmap.go\n", "+++ pkg/resources/cruisecontrol/configmap.go\n", "@@ -169,7 +169,7 @@\n", " \tcapacityConfig.BrokerCapacities = append(capacityConfig.BrokerCapacities, BrokerCapacities...)\n", " \tresult, err := json.<PERSON>(capacityConfig, \"\", \"    \")\n", " \tif err != nil {\n", "-\t\tlog.<PERSON><PERSON>r(err, \"Could not marshal cruise control capacity config\")\n", "+\t\treturn \"\", err\n", " \t}\n", " \tlog.Info(fmt.Sprintf(\"Generated capacity config was successful with values: %s\", result))\n", " \treturn string(result), err\n", "====================================================================================================\n", "                  ID: 886973529\n", "         INSTRUCTION: reformat to a single line\n", "ORIGINAL INSTRUCTION: **nitpick:** Can be reformatted to a single line:\n", "          REVIEW URL: https://github.com/Tribler/tribler/pull/6889#discussion_r886973529\n", "\n", "--- src/tribler/core/components/ipv8/eva/transfer/base.py\n", "+++ src/tribler/core/components/ipv8/eva/transfer/base.py\n", "@@ -74,9 +74,7 @@\n", "     def _release(self):\n", "         self.logger.debug('Release')\n", "         self.finished = True\n", "-        self.protocol_task_group.add(\n", "-            self.task_group.cancel()\n", "-        )\n", "+        self.protocol_task_group.add(self.task_group.cancel())\n", " \n", "         if self.container:\n", "             self.container.pop(self.peer, None)\n", "====================================================================================================\n", "                  ID: 858335496\n", "         INSTRUCTION: remove eslint-disable\n", "ORIGINAL INSTRUCTION: I think we can get rid of this `eslint-disable` cause we dont really need the inferred type here, just to know if `Target extends []`\n", "          REVIEW URL: https://github.com/empathyco/x/pull/447#discussion_r858335496\n", "\n", "--- packages/x-adapter/src/schemas/schemas.types.ts\n", "+++ packages/x-adapter/src/schemas/schemas.types.ts\n", "@@ -227,8 +227,7 @@\n", "           ? Target extends (infer TargetArrayType)[]\n", "             ? Schema<SourceArrayType, TargetArrayType>\n", "             : never\n", "-          : // eslint-disable-next-line @typescript-eslint/no-unused-vars\n", "-          Target extends (infer TargetArrayType)[]\n", "+          Target extends []\n", "           ? never\n", "           : Schema<ExtractType<Source, Path>, Target>)\n", "       | '$self';\n", "====================================================================================================\n", "                  ID: 682381324\n", "         INSTRUCTION: use getType() instead of isCompleted()\n", "ORIGINAL INSTRUCTION: Careful, I think that getType() returns String, we can't expect comparison with int to provide expected values.\n", "          REVIEW URL: https://github.com/kiegroup/droolsjbpm-integration/pull/2560#discussion_r682381324\n", "\n", "--- kie-server-parent/kie-server-services/kie-server-services-jbpm-ui/src/main/java/org/kie/server/services/jbpm/ui/ImageServiceBase.java\n", "+++ kie-server-parent/kie-server-services/kie-server-services-jbpm-ui/src/main/java/org/kie/server/services/jbpm/ui/ImageServiceBase.java\n", "@@ -151,7 +151,7 @@\n", "             activeLogs.forEach(activeNode -> {\n", "                 populateSubProcessLink(containerId, activeNode, subProcessLinks);\n", "             });\n", "-            fullLogs.stream().filter(node -> ((org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).getType() != 0 && ((org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).getType() != 1)\n", "+            fullLogs.stream().filter(node -> ((org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).getType() != 0 && (!(org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).isCompleted())\n", "                     .forEach(node -> populateSubProcessLink(containerId, node, subProcessLinks));\n", " \n", "             fullLogs.stream().filter(node -> ((org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).getType() != 0 && ((org.jbpm.kie.services.impl.model.NodeInstanceDesc) node).getType() != 1)\n", "====================================================================================================\n", "                  ID: 983994894\n", "         INSTRUCTION: rename local variable\n", "ORIGINAL INSTRUCTION: Some renaming of local variables, also in combination with the `BoundField.componentName` renaming proposed above:\n", "          REVIEW URL: https://github.com/google/gson/pull/2201#discussion_r983994894\n", "\n", "--- gson/src/main/java/com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.java\n", "+++ gson/src/main/java/com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.java\n", "@@ -429,19 +429,20 @@\n", " \n", "     @Override\n", "     void readField(Object[] accumulator, <PERSON><PERSON><PERSON><PERSON><PERSON> in, <PERSON><PERSON><PERSON>ield field) throws IOException {\n", "-      Integer fieldIndex = componentIndices.get(field.componentName);\n", "-      if (fieldIndex == null) {\n", "+      // Obtain the component index from the name of the field backing it\n", "+      Integer componentIndex = componentIndices.get(field.fieldName);\n", "+      if (componentIndex == null) {\n", "         throw new IllegalStateException(\n", "             \"Could not find the index in the constructor \"\n", "                 + constructor\n", "                 + \" for field with name \"\n", "-                + field.name\n", "+                + field.fieldName\n", "                 + \", unable to determine which argument in the constructor the field corresponds\"\n", "                 + \" to. This is unexpected behaviour, as we expect the RecordComponents to have the\"\n", "                 + \" same names as the fields in the Java class, and that the order of the\"\n", "-                + \" RecordComponents is the same as the order of the canonical arguments.\");\n", "-      }\n", "-      field.readIntoArray(in, fieldIndex, accumulator);\n", "+                + \" RecordComponents is the same as the order of the canonical constructor arguments.\");\n", "+      }\n", "+      field.readIntoArray(in, componentIndex, accumulator);\n", "     }\n", " \n", "     @Override\n", "====================================================================================================\n", "                  ID: 744277703\n", "         INSTRUCTION: remove unnecessary import\n", "ORIGINAL INSTRUCTION: Lint :)\n", "          REVIEW URL: https://github.com/refined-github/refined-github/pull/5055#discussion_r744277703\n", "\n", "--- build/verify-features.ts\n", "+++ build/verify-features.ts\n", "@@ -1,5 +1,5 @@\n", "+import regexJoin from 'regex-join';\n", " import {existsSync, readdirSync, readFileSync} from 'node:fs';\n", "-import regexJoin from 'regex-join';\n", " \n", " import {getFeatures, getFeaturesMeta} from './readme-parser.js'; // Must import as `.js`\n", " \n", "====================================================================================================\n", "                  ID: 1148097407\n", "         INSTRUCTION: use kelvin units\n", "ORIGINAL INSTRUCTION: We can be a little more tight with units here:\n", "          REVIEW URL: https://github.com/Unidata/MetPy/pull/2952#discussion_r1148097407\n", "\n", "--- src/metpy/calc/thermo.py\n", "+++ src/metpy/calc/thermo.py\n", "@@ -1664,8 +1664,8 @@\n", "         wet-bulb potential temperature of the parcel\n", " \n", "     \"\"\"\n", "-    theta_e = equivalent_potential_temperature(pressure, temperature, dewpoint).magnitude\n", "-    x = theta_e / 273.15\n", "+    theta_e = equivalent_potential_temperature(pressure, temperature, dewpoint)\n", "+    x = theta_e.m_as('kelvin') / 273.15\n", "     a = 7.101574 - 20.68208 * x + 16.11182 * x ** 2 + 2.574631 * x ** 3 - 5.205688 * x ** 4\n", "     b = 1 - 3.552497 * x + 3.781782 * x ** 2 - 0.6899655 * x ** 3 - 0.5929340 * x ** 4\n", "     return units.Quantity(theta_e - np.exp((a) / (b)), 'kelvin')\n", "====================================================================================================\n", "                  ID: 861916894\n", "         INSTRUCTION: Replace `RequestHeaders` with `HeadersInit`\n", "ORIGINAL INSTRUCTION: I believe this is actually called `HeadersInit` in dom.d.ts?\n", "([ts playground link](https://www.typescriptlang.org/play?target=99#code/DYUwLgBAFiCGAmIBOBnAXBAEnRqCSAdgJZgDcAUOaJDAsigEwYBKIAjgK4gpjZ2oUgA))\n", "          REVIEW URL: https://github.com/developit/redaxios/pull/79#discussion_r861916894\n", "\n", "--- src/index.js\n", "+++ src/index.js\n", "@@ -16,7 +16,7 @@\n", "  * @typedef Options\n", "  * @property {string} [url] the URL to request\n", "  * @property {'get'|'post'|'put'|'patch'|'delete'|'options'|'head'|'GET'|'POST'|'PUT'|'PATCH'|'DELETE'|'OPTIONS'|'HEAD'} [method=\"get\"] HTTP method, case-insensitive\n", "- * @property {RequestHeaders} [headers] Request headers\n", "+ * @property {HeadersInit} [headers] Request headers\n", "  * @property {FormData|string|object} [body] a body, optionally encoded, to send\n", "  * @property {'text'|'json'|'stream'|'blob'|'arrayBuffer'|'formData'|'stream'} [responseType=\"json\"] An encoding to use for the response\n", "  * @property {Record<string,any>|URLSearchParams} [params] querystring parameters\n", "====================================================================================================\n", "                  ID: 942205920\n", "         INSTRUCTION: format the code\n", "ORIGINAL INSTRUCTION: Small formatting change to satisfy Python formatters\n", "          REVIEW URL: https://github.com/mpepping/solarman-mqtt/pull/32#discussion_r942205920\n", "\n", "--- solarman/__init__.py\n", "+++ solarman/__init__.py\n", "@@ -108,7 +108,9 @@\n", "             topic + \"/inverter/deviceState\",\n", "             inverter_data[\"deviceState\"],\n", "         )\n", "-        mqtt_connection.message(topic + \"/logger/deviceState\", logger_data[\"deviceState\"])\n", "+        mqtt_connection.message(\n", "+            topic + \"/logger/deviceState\", logger_data[\"deviceState\"]\n", "+        )\n", "         logging.info(\n", "             \"%s - Inverter DeviceState: %s\"\n", "             \"-> Only status MQTT publish (probably offline due to nighttime shutdown)\",\n", "====================================================================================================\n", "                  ID: 567984235\n", "         INSTRUCTION: initialize as an empty array\n", "ORIGINAL INSTRUCTION: So we always have an array in the state and don't need to check for that later.\n", "          REVIEW URL: https://github.com/chartjs/chartjs-plugin-annotation/pull/327#discussion_r567984235\n", "\n", "--- src/annotation.js\n", "+++ src/annotation.js\n", "@@ -55,6 +55,8 @@\n", "     } else if (isArray(state.options.annotations)) {\n", "       for (var i = 0; i < state.options.annotations.length; i++) {\n", "         state.options.annotations[i] = resolveAnnotationOptions(chart, state.options.annotations[i]);\n", "+      } else {\n", "+        state.options.annotations = [];\n", "       }\n", "     }\n", "   },\n", "====================================================================================================\n", "                  ID: 747479838\n", "         INSTRUCTION: swap the order of comparison\n", "ORIGINAL INSTRUCTION: I am not sure if other groups must have this attribute set, therefore I would compare them like this to avoid `NullPointerException`:\n", "          REVIEW URL: https://github.com/CESNET/perun/pull/3406#discussion_r747479838\n", "\n", "--- perun-core/src/main/java/cz/metacentrum/perun/core/impl/modules/attributes/urn_perun_group_attribute_def_def_adGroupName.java\n", "+++ perun-core/src/main/java/cz/metacentrum/perun/core/impl/modules/attributes/urn_perun_group_attribute_def_def_adGroupName.java\n", "@@ -78,7 +78,7 @@\n", " \t\t\t\tfor (Group subGroup : subGroups) {\n", " \t\t\t\t\tAttribute nameAttribute = sess.getPerunBl().getAttributesManagerBl().getAttribute(sess, subGroup, A_G_D_AD_GROUP_NAME);\n", " \t\t\t\t\tString name = nameAttribute.valueAsString();\n", "-\t\t\t\t\tif (name.equals(attribute.valueAsString())) {\n", "+\t\t\t\t\tif (attribute.valueAsString().equals(name)) {\n", " \t\t\t\t\t\treturn false;\n", " \t\t\t\t\t}\n", " \t\t\t\t}\n", "====================================================================================================\n", "                  ID: **********\n", "         INSTRUCTION: rename providerOrAccount to account\n", "ORIGINAL INSTRUCTION: should this also be renamed to account? Also in the comments above the `connect`\n", "          REVIEW URL: https://github.com/0xs34n/starknet.js/pull/410#discussion_r**********\n", "\n", "--- src/contract/contractFactory.ts\n", "+++ src/contract/contractFactory.ts\n", "@@ -61,7 +61,7 @@\n", "    *\n", "    * @param providerOrAccount - new Provider or Account to attach to\n", "    */\n", "-  connect(providerOrAccount: AccountInterface): ContractFactory {\n", "+  connect(account: AccountInterface): ContractFactory {\n", "     this.account = providerOrAccount;\n", "     return this;\n", "   }\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: ensure folder exists\n", "ORIGINAL INSTRUCTION: Same here:\n", "          REVIEW URL: https://github.com/microsoft/rushstack/pull/1413#discussion_r*********\n", "\n", "--- apps/rush-buildxl/src/api/BxlModule.ts\n", "+++ apps/rush-buildxl/src/api/BxlModule.ts\n", "@@ -78,7 +78,7 @@\n", " `;\n", " \n", "     FileSystem.ensureFolder(this._moduleDir);\n", "-    FileSystem.writeFile(this.moduleFilePath, contents);\n", "+    FileSystem.writeFile(this.moduleFilePath, contents, { ensureFolderExists: true });\n", "     return this._config.writeFile();\n", "   }\n", " }\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: avoid mutations and use actions\n", "ORIGINAL INSTRUCTION: Could we avoid mutations here and use actions to allow SwaggerUI to properly react to the data change?\n", "          REVIEW URL: https://github.com/swagger-api/swagger-ui/pull/7438#discussion_r*********\n", "\n", "--- test/e2e-cypress/tests/features/auth-code-flow-pkce-without-secret.js\n", "+++ test/e2e-cypress/tests/features/auth-code-flow-pkce-without-secret.js\n", "@@ -27,6 +27,10 @@\n", "       .then(win => {\n", "         // set auth config to not use PKCE\n", "         let authConfigs = win.ui.authSelectors.getConfigs()\n", "+        win.ui.authActions.configureAuth({\n", "+          ...authConfigs,\n", "+          usePkceWithAuthorizationCodeGrant: false,\n", "+        })\n", "         authConfigs.usePkceWithAuthorizationCodeGrant = false\n", "       })\n", "       .get(\"button.authorize\")\n", "====================================================================================================\n", "                  ID: 1017864039\n", "         INSTRUCTION: format variabel\n", "ORIGINAL INSTRUCTION: ini dibikin variabel dulu aja hug\n", "          REVIEW URL: https://github.com/MonAPI-xyz/MonAPI/pull/78#discussion_r1017864039\n", "\n", "--- alerts/management/commands/run_cron_alerts.py\n", "+++ alerts/management/commands/run_cron_alerts.py\n", "@@ -54,7 +54,10 @@\n", "         success_rate = 100\n", "         if success_count['total'] != 0:\n", "             success_rate = success_count['s'] / (success_count['total']) * 100\n", "-        return success_rate, start_time.strftime(\"%d %b %Y, %H:%M:%S\"), end_time.strftime(\"%d %b %Y, %H:%M:%S\")\n", "+        formatted_success_rate = round(float(success_rate), 2)\n", "+        formatted_start_time = start_time.strftime(\"%d %b %Y, %H:%M:%S\")\n", "+        formatted_end_time = end_time.strftime(\"%d %b %Y, %H:%M:%S\")\n", "+        return formatted_success_rate , formatted_start_time, formatted_end_time\n", "     \n", "     def get_monitor_id_from_queue(self, type):\n", "         while True:\n", "====================================================================================================\n", "                  ID: 443913355\n", "         INSTRUCTION: mention asymmetric key\n", "ORIGINAL INSTRUCTION: We should mention the key should be asymmetric.\n", "          REVIEW URL: https://github.com/stellar/go/pull/2692#discussion_r443913355\n", "\n", "--- exp/services/recoverysigner/cmd/serve.go\n", "+++ exp/services/recoverysigner/cmd/serve.go\n", "@@ -97,7 +97,7 @@\n", " \t\t},\n", " \t\t{\n", " \t\t\tName:        \"encryption-tink-keyset\",\n", "-\t\t\tUsage:       \"Tink keyset in JSON format used to encrypt/decrypt signing keys. The keyset is encrypted by the remote KMS key specified in encryption-kms-key-uri if present\",\n", "+\t\t\tUsage:       \"Tink keyset in JSON format used to encrypt/decrypt signing keys containing a single asymmetric key (the keyset must be encrypted by the remote KMS key specified in encryption-kms-key-uri if that option is present)\",\n", " \t\t\tOptType:     types.String,\n", " \t\t\tConfigKey:   &opts.EncryptionTinkKeysetJSON,\n", " \t\t\tFlagDefault: \"\",\n", "====================================================================================================\n", "                  ID: 460507265\n", "         INSTRUCTION: use os.path.expanduser\n", "ORIGINAL INSTRUCTION: Why not just\n", "          REVIEW URL: https://github.com/davidhalter/parso/pull/146#discussion_r460507265\n", "\n", "--- parso/cache.py\n", "+++ parso/cache.py\n", "@@ -65,7 +65,7 @@\n", " \n", " def _get_default_cache_path():\n", "     if platform.system().lower() == 'windows':\n", "-        dir_ = Path(os.getenv('LOCALAPPDATA') or os.path.expanduser('~'), 'Parso', 'Parso')\n", "+        dir_ = Path(os.getenv('LOCALAPPDATA') or '~', 'Parso', 'Parso')\n", "     elif platform.system().lower() == 'darwin':\n", "         dir_ = Path('~', 'Library', 'Caches', 'Parso')\n", "     else:\n", "====================================================================================================\n", "                  ID: 540326827\n", "         INSTRUCTION: use casting to string\n", "ORIGINAL INSTRUCTION: I think casting to a string will work here. Otherwise I'd prefer to use ts-expect-error.\n", "          REVIEW URL: https://github.com/NotWoods/maskable/pull/37#discussion_r540326827\n", "\n", "--- src/editor/main.js\n", "+++ src/editor/main.js\n", "@@ -187,7 +187,7 @@\n", " button('export', async () => {\n", "   const exportSizes = new FormData(document.forms['exportSizes']).getAll('sizes')\n", "     // @ts-ignore\n", "-    .map(item => parseInt(item, 10));\n", "+    .map(item => parseInt(item as string, 10));\n", " \n", "   exportSizes.forEach(async (size) => {\n", "     const url = await toUrl(controller.export(size), true);\n", "====================================================================================================\n", "                  ID: 789250362\n", "         INSTRUCTION: use Sequence instead of List\n", "ORIGINAL INSTRUCTION: Seems like it's sometimes a tuple at runtime, which makes mypy<PERSON> unhappy\n", "          REVIEW URL: https://github.com/psf/black/pull/2744#discussion_r789250362\n", "\n", "--- src/black/__init__.py\n", "+++ src/black/__init__.py\n", "@@ -411,7 +411,7 @@\n", "     fast: bool,\n", "     pyi: bool,\n", "     ipynb: bool,\n", "-    python_cell_magics: List[str],\n", "+    python_cell_magics: Sequence[str],\n", "     skip_string_normalization: bool,\n", "     skip_magic_trailing_comma: bool,\n", "     experimental_string_processing: bool,\n", "====================================================================================================\n", "                  ID: 435930427\n", "         INSTRUCTION: remove unnecessary condition\n", "ORIGINAL INSTRUCTION: If `node.rank > source.rank` then isn't `index > sourceIndex` already implied?\n", "          REVIEW URL: https://github.com/kedro-org/kedro-viz/pull/187#discussion_r435930427\n", "\n", "--- src/utils/random-data.js\n", "+++ src/utils/random-data.js\n", "@@ -189,7 +189,7 @@\n", "       const source = nodesByRank[sourceIndex];\n", " \n", "       const nextRankFirstIndex = nodesByRank.findIndex(\n", "-        (node, index) => index > sourceIndex && node.rank > source.rank\n", "+        node => node.rank > source.rank\n", "       );\n", " \n", "       if (nextRankFirstIndex === -1) continue;\n", "====================================================================================================\n", "                  ID: 814308473\n", "         INSTRUCTION: check version\n", "ORIGINAL INSTRUCTION: We should probably check that the version is greater than or equal to the auto-updated pyright version\n", "          REVIEW URL: https://github.com/RobertCraigie/pyright-python/pull/24#discussion_r814308473\n", "\n", "--- tests/test_main.py\n", "+++ tests/test_main.py\n", "@@ -50,7 +50,7 @@\n", "     output = proc.stdout.decode('utf-8')\n", "     match = VERSION_REGEX.match(output)\n", "     assert match is not None\n", "-    assert version.parse(match.group(1)) > version.parse('1.1.223')\n", "+    assert version.parse(match.group(1)) > version.parse(__pyright_version__)\n", " \n", " \n", " def test_entry_point() -> None:\n", "====================================================================================================\n", "                  ID: 363773591\n", "         INSTRUCTION: remove return type\n", "ORIGINAL INSTRUCTION: At the minute it looks like the parsed annotations would not correctly implement the`InputModelFieldAnnotations` interface. Should this return type be removed altogether?\n", "          REVIEW URL: https://github.com/aerogear/graphback/pull/603#discussion_r363773591\n", "\n", "--- packages/graphback-core/src/graphql/annotations.ts\n", "+++ packages/graphback-core/src/graphql/annotations.ts\n", "@@ -35,7 +35,7 @@\n", "     return fieldAnnotations;\n", " }\n", " \n", "-export const parseTypeAnnotations = (node: TypeDefinitionNode): InputModelFieldAnnotations => {\n", "+export const parseTypeAnnotations = (node: TypeDefinitionNode) => {\n", " \n", "     return node.description ? parseAnnotations('crud', String(node.description)) : undefined;\n", " }\n", "====================================================================================================\n"]}], "source": ["import random\n", "\n", "random.seed(31415)\n", "\n", "for index in range(50, 100):\n", "    suggestion = llmfixed_filtered_suggested_edit[index]\n", "    print('                  ID:', suggestion['id'])\n", "    print('         INSTRUCTION:', suggestion['instruction'])\n", "    print('ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print('          REVIEW URL:', suggestion['html_url'])\n", "    print()\n", "    print(get_diff(suggestion))\n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<｜begin▁of▁sentence｜>You are an AI programming assistant. Your task is to transform code review comments into clear, actionable instructions in the same language as the original comment, while ensuring they align with the associated code changes. Follow these guidelines:\n", "\n", "Language Consistency: The transformed instruction must be in the same language as the original code review comment. Do not translate or change the language.\n", "Focus on Instruction Transformation: Your output should strictly be a transformed, actionable instruction derived from the original comment. Place this after 'INPUT COMMENT'.\n", "Avoid Code Continuation: Do not provide any continuation or modification of the code. The response should only include the revised instruction.\n", "Clear and Direct Instructions: Convert each comment into a direct, actionable instruction. Eliminate polite language, subjective opinions, and extraneous details.\n", "Address Ambiguity: If a comment is unclear, lacks critical information, or seems to reference other parts of the file not provided, respond with 'UNCLEAR' after 'INPUT COMMENT'.\n", "Code Change Context: Provide the code change formatted with backticks (`) after 'CODE CHANGE' for context. This code modification is not part of the transformed output.\n", "Ensure your output includes only the revised instruction in the same language as the original comment, placed after 'INPUT COMMENT'\n", "\n", "INPUT COMMENT\n", "Also see this:\n", "https://stackoverflow.com/questions/21925671/how-to-convert-django-model-object-to-dict-with-its-fields-and-values\n", "In particular, the 6th option (using Django Rest Framework) may be the best option here.\n", "CODE CHANGE\n", "\n", "```\n", "--- backend/uclapi/workspaces/occupeye/api.py\n", "+++ backend/uclapi/workspaces/occupeye/api.py\n", "@@ -396,13 +396,8 @@\n", "         if survey is None:\n", "             return None\n", "         objs = Sensors.objects.filter(survey_id=survey_id)\n", "-        sensors = []\n", "-        for obj in objs:\n", "-            sensors.append({\"sensor_id\": obj.sensor_id, \"hardware_id\": obj.hardware_id,\n", "-                            \"survey_device_id\": obj.survey_device_id})\n", "-\n", "         return {\"survey_id\": survey.survey_id, \"name\": survey.name, \"start\": survey.start_datetime,\n", "-                \"end\": survey.end_datetime, \"active\": survey.active, \"sensors\": sensors,\n", "+                \"end\": survey.end_datetime, \"active\": survey.active, \"sensors\": list(objs),\n", "                 \"last_updated\": survey.last_updated}\n", " \n", "     def get_historical_list_surveys(self):\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "?\n", "CODE CHANGE\n", "\n", "```\n", "--- src/main/java/org/jvnet/hudson/test/RealJenkinsRule.java\n", "+++ src/main/java/org/jvnet/hudson/test/RealJenkinsRule.java\n", "@@ -618,7 +618,7 @@\n", " \n", "     private static int readPort(File portFile) throws IOException {\n", "         String s = FileUtils.readFileToString(portFile, StandardCharsets.UTF_8);\n", "-        if (StringUtils.isEmpty(s)) {\n", "+        if (s.isEmpty() {\n", "             LOGGER.log(Level.WARNING, () -> String.format(\"PortFile: %s exists, but value is still not written\", portFile.getAbsolutePath()));\n", "             return 0;\n", "         }\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "Please put separator after `*args` (important), and also use the constant:\n", "CODE CHANGE\n", "\n", "```\n", "--- neuraxle/hyperparams/space.py\n", "+++ neuraxle/hyperparams/space.py\n", "@@ -78,7 +78,7 @@\n", "     HyperparameterSamples are often the result of calling ``.rvs()`` on an HyperparameterSpace.\n", "     \"\"\"\n", "     DEFAULT_SEPARATOR = '__'\n", "-    def __init__(self, separator=None, *args, **kwds):\n", "+    def __init__(self, *args, separator=DEFAULT_SEPARATOR, **kwds):\n", "         if len(args) == 1 and isinstance(args[0], RecursiveDict) and len(kwds) == 0:\n", "             super().__init__(args[0].items())\n", "             separator = args[0].separator\n", "```\n", "\n", "REVISED COMMENT\n", "put separator after args and use constant\n", "INPUT COMMENT\n", "Can you keep this consistent with other functions and have them be 2 separate arguments?\n", "CODE CHANGE\n", "\n", "```\n", "--- packages/node_modules/@webex/internal-plugin-conversation/src/conversation.js\n", "+++ packages/node_modules/@webex/internal-plugin-conversation/src/conversation.js\n", "@@ -1602,7 +1602,7 @@\n", "    *\n", "    * jumpToActivity - gets searched-for activity and surrounding activities\n", "    */\n", "-  listActivitiesThreadOrdered(options) {\n", "+  listActivitiesThreadOrdered(conversationUrl, activityId) {\n", "     const {\n", "       conversationUrl,\n", "       conversationId\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "Add a type for the return\n", "CODE CHANGE\n", "\n", "```\n", "--- src/utils/buildFetchOptions.js\n", "+++ src/utils/buildFetchOptions.js\n", "@@ -5,7 +5,7 @@\n", " \n", " export const buildAgent = (proxy: string = defaultProxy) =>\n", "   proxy ? new ProxyAgent(proxy) : ''\n", "-export const buildFetchOptions = (options: { proxy?: string } = {}) => {\n", "+export const buildFetchOptions = (options: { proxy?: string } = {}): { agent: Object } => {\n", "   const agent = buildAgent(options.proxy)\n", "   return { agent }\n", " }\n", "```\n", "\n", "REVISED COMMENT\n", "add type\n", "INPUT COMMENT\n", "여기도 마찬가지!\n", "CODE CHANGE\n", "\n", "```\n", "--- programmers/난이도별/level03.정수삼각형/rockmiin.py\n", "+++ programmers/난이도별/level03.정수삼각형/rockmiin.py\n", "@@ -1,14 +1,16 @@\n", " def solution(triangle):\n", "-    dp=[[0]* i for i in range(1, len(triangle)+1)]\n", "-    for i in range(0, len(triangle)):\n", "+    triangleLen = len(triangle)\n", "+    dp=[[0]* i for i in range(1, triangleLen+1)]\n", "+    for i in range(0, triangleLen):\n", "         for j in range(i+1):\n", "+            v = triangle[i][j]\n", "             if j==0:\n", "-                dp[i][j]=dp[i-1][j]+triangle[i][j]\n", "+                dp[i][j] = dp[i-1][j] + v\n", "             elif j==i:\n", "-                dp[i][j]=dp[i-1][j-1]+triangle[i][j]\n", "+                dp[i][j] = dp[i-1][j-1] + v\n", "             elif j>=1:\n", "-                dp[i][j]=max(dp[i-1][j], dp[i-1][j-1])+triangle[i][j]\n", "-    return max(dp[len(triangle)-1])\n", "+                dp[i][j] = max(dp[i-1][j], dp[i-1][j-1]) + v\n", "+    return max(dp[-1])\n", " \n", " print(\n", "     solution([[7], [3, 8], [8, 1, 0], [2, 7, 4, 4], [4, 5, 2, 6, 5]])==30\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "Maybe more idiomatic is\n", "CODE CHANGE\n", "\n", "```\n", "--- torchvision/models/detection/backbone_utils.py\n", "+++ torchvision/models/detection/backbone_utils.py\n", "@@ -126,7 +126,7 @@\n", "     if returned_layers is None:\n", "         returned_layers = [1, 2, 3, 4]\n", "     if min(returned_layers) <= 0 or max(returned_layers) >= 5:\n", "-        raise ValueError(f\" `returned_layers` object should contain integers between [1,4], got {returned_layers} \")\n", "+        raise ValueError(f\"Each returned layer must be in the range [1,4]. Got {returned_layers}\")\n", "     return_layers = {f\"layer{k}\": str(v) for v, k in enumerate(returned_layers)}\n", " \n", "     in_channels_stage2 = backbone.inplanes // 8\n", "```\n", "\n", "REVISED COMMENT\n", "improve error message\n", "INPUT COMMENT\n", "Would it not be more clear like this ?\n", "CODE CHANGE\n", "\n", "```\n", "--- src/aleph/config.py\n", "+++ src/aleph/config.py\n", "@@ -7,7 +7,7 @@\n", "     return {\n", "         \"logging\": {\n", "             \"level\": logging.WARNING,\n", "-            \"max_log_file_size\": 1 * 1024 * 1024 * 1024,\n", "+            \"max_log_file_size\": 1_000_000_000,  # 1 GB\n", "         },\n", "         \"aleph\": {\n", "             \"queue_topic\": \"ALEPH-QUEUE\",\n", "```\n", "\n", "REVISED COMMENT\n", "use single number with underscores\n", "INPUT COMMENT\n", "Silence: \"WARNING: Bug in UI description. Missing module description\"\n", "\n", "--> might be extended\n", "CODE CHANGE\n", "\n", "```\n", "--- grass7/gui/wxpython/wx.metadata/g.gui.cswbrowser/g.gui.cswbrowser.py\n", "+++ grass7/gui/wxpython/wx.metadata/g.gui.cswbrowser/g.gui.cswbrowser.py\n", "@@ -17,6 +17,13 @@\n", " \n", " from mdlib.cswlib import CSWBrowserPanel, CSWConnectionPanel\n", " import wx\n", "+\n", "+#%module\n", "+#% description: Graphical CSW metadata browser.\n", "+#% keyword: general\n", "+#% keyword: GUI\n", "+#% keyword: metadata\n", "+#%end\n", " \n", " set_gui_path()\n", " \n", "```\n", "\n", "REVISED COMMENT\n", "add module descrption\n", "INPUT COMMENT\n", "What do you think about making this a warning? I don't think a user typically needs to act on this.\n", "CODE CHANGE\n", "\n", "```\n", "--- lib/cloud-metadata/callback-coordination.js\n", "+++ lib/cloud-metadata/callback-coordination.js\n", "@@ -40,7 +40,7 @@\n", "       this.timeout = setTimeout(() => {\n", "         if (!this.done) {\n", "           this.complete()\n", "-          this.logger.error('metadata requests timed out, using default values instead')\n", "+          this.logger.warn('cloud metadata requests timed out, using default values instead')\n", "           const error = new CallbackCoordinationError(\n", "             'callback coordination reached timeout',\n", "             this.errors\n", "```\n", "\n", "REVISED COMMENT\n", "make it warning\n", "INPUT COMMENT\n", "And I'm not sure if we can report `error`. I think yes, in which case you should create a separate entry afaics in the schema.\n", "CODE CHANGE\n", "\n", "```\n", "--- reframe/frontend/statistics.py\n", "+++ reframe/frontend/statistics.py\n", "@@ -227,7 +227,7 @@\n", "                     'time': str(tid['time_total']),\n", "                 }\n", "             )\n", "-            if not tid['result'] == 'success':\n", "+            if tid['result'] == 'failure':\n", "                 testcase_msg = ET.SubElement(\n", "                     testcase, 'failure', attrib={'message': tid['fail_phase']}\n", "                 )\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "let's avoid an edge case of a key with an empty string\n", "CODE CHANGE\n", "\n", "```\n", "--- checkov/terraform/graph_builder/foreach_handler.py\n", "+++ checkov/terraform/graph_builder/foreach_handler.py\n", "@@ -184,7 +184,7 @@\n", "         if new_key:\n", "             key_to_val_changes[EACH_KEY] = new_key\n", "         return key_to_val_changes\n", "-    def _create_new_resource(self, main_resource: TerraformBlock, new_value: int | str, new_key: str = ''):\n", "+    def _create_new_resource(self, main_resource: TerraformBlock, new_value: int | str, new_key: Optional[str] = None):\n", "         new_resource = deepcopy(main_resource)\n", "         block_type, block_name = new_resource.name.split('.')\n", "         if main_resource.attributes.get(COUNT_STRING):\n", "```\n", "\n", "REVISED COMMENT\n", "use empty string as default\n", "INPUT COMMENT\n", "same here\n", "CODE CHANGE\n", "\n", "```\n", "--- rides-and-fares/src/main/java/org/apache/flink/training/exercises/ridesandfares/RidesAndFaresExercise.java\n", "+++ rides-and-fares/src/main/java/org/apache/flink/training/exercises/ridesandfares/RidesAndFaresExercise.java\n", "@@ -73,7 +73,7 @@\n", " \n", "         // A stream of taxi fare events, also keyed by rideId.\n", "         DataStream<TaxiFare> fares =\n", "-                env.addSource(fareSource).keyBy((TaxiFare fare) -> fare.rideId);\n", "+                env.addSource(fareSource).keyBy(fare -> fare.rideId);\n", " \n", "         // Create the pipeline.\n", "         rides.connect(fares)\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "Now that LoaderOptions provide the `setAllowDuplicateKeys` option, we don't need the `StrictMapAppenderConstructor` anymore?\n", "CODE CHANGE\n", "\n", "```\n", "--- apollo-client/src/main/java/com/ctrip/framework/apollo/util/yaml/YamlParser.java\n", "+++ apollo-client/src/main/java/com/ctrip/framework/apollo/util/yaml/YamlParser.java\n", "@@ -65,16 +65,9 @@\n", "    * Create the {@link Yaml} instance to use.\n", "    */\n", "   private Yaml createYaml() {\n", "-    StrictMapAppenderConstructor constructor = new StrictMapAppenderConstructor();\n", "-    Representer representer = new Representer();\n", "-    DumperOptions dumperOptions = new DumperOptions();\n", "-    dumperOptions.setDefaultFlowStyle(representer.getDefaultFlowStyle());\n", "-    dumperOptions.setDefaultScalarStyle(representer.getDefaultScalarStyle());\n", "-    dumperOptions.setAllowReadOnlyProperties(representer.getPropertyUtils().isAllowReadOnlyProperties());\n", "-    dumperOptions.setTimeZone(representer.getTimeZone());\n", "     LoaderOptions loadingConfig = new LoaderOptions();\n", "     loadingConfig.setAllowDuplicateKeys(false);\n", "-    return new Yaml(constructor, representer, dumperOptions, loadingConfig);\n", "+    return new Yaml(new SafeConstructor(), new Representer(), new DumperOptions(), loadingConfig);\n", "   }\n", " \n", "   private boolean process(MatchCallback callback, Yaml yaml, String content) {\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "this seems a bit redundant. I think you could just change it to one line\n", "CODE CHANGE\n", "\n", "```\n", "--- server/pxf-hdfs/src/main/java/org/greenplum/pxf/plugins/hdfs/AvroResolver.java\n", "+++ server/pxf-hdfs/src/main/java/org/greenplum/pxf/plugins/hdfs/AvroResolver.java\n", "@@ -221,12 +221,7 @@\n", "         Schema.Type fieldType = fieldSchema.getType();\n", " \n", "         int ret = 0;\n", "-        LogicalType logicalType = null;\n", "-        if(fieldSchema.getLogicalType() != null )\n", "-        {\n", "-             logicalType = fieldSchema.getLogicalType();\n", "-\n", "-        }\n", "+        LogicalType logicalType = fieldSchema.getLogicalType();\n", "         switch (fieldType) {\n", "             case ARRAY:\n", "                 if (fieldValue == null) {\n", "```\n", "\n", "REVISED COMMENT\n", "rewrite in one line\n", "INPUT COMMENT\n", "That allows to also remove the Channel resources.\n", "CODE CHANGE\n", "\n", "```\n", "--- worldedit-core/src/main/java/com/fastasyncworldedit/core/util/TextureUtil.java\n", "+++ worldedit-core/src/main/java/com/fastasyncworldedit/core/util/TextureUtil.java\n", "@@ -383,7 +383,7 @@\n", "                          final ReadableByteChannel inChannel = Channels.newChannel(stream);\n", "                          final FileOutputStream out = new FileOutputStream(outPath);\n", "                          final FileChannel outChannel = out.getChannel()) {\n", "-                        outChannel.transferFrom(inChannel, 0, Long.MAX_VALUE);\n", "+                        stream.transferTo(out);\n", "                     }\n", "                     // Validate sha-1 hash\n", "                     try {\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "per https://github.com/sveltejs/kit/pull/6936/files#r976600055\n", "CODE CHANGE\n", "\n", "```\n", "--- packages/adapter-node/src/handler.js\n", "+++ packages/adapter-node/src/handler.js\n", "@@ -56,7 +56,7 @@\n", " \t\t});\n", " \t} catch (err) {\n", " \t\tres.statusCode = err.status || 400;\n", "-\t\tres.end(err.message || err.toString() || 'Invalid request body');\n", "+\t\tres.end('Invalid request body');\n", " \t\treturn;\n", " \t}\n", " \n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "不仅是 inspect，\n", "CODE CHANGE\n", "\n", "```\n", "--- packages/preset-built-in/src/plugins/commands/config/config.ts\n", "+++ packages/preset-built-in/src/plugins/commands/config/config.ts\n", "@@ -4,7 +4,7 @@\n", " export default (api: IApi) => {\n", "   api.registerCommand({\n", "     name: 'config',\n", "-    description: 'inspect umi config',\n", "+    description: 'umi config cli',\n", "     details: `\n", " # List configs\n", " $ umi config list\n", "```\n", "\n", "REVISED COMMENT\n", "UNCLEAR\n", "INPUT COMMENT\n", "can   `file` and `cfnTemplatePath` be uninitialized? Looks like they are reassigned in the loop below.\n", "CODE CHANGE\n", "\n", "```\n", "--- src/lambda/commands/createNewSamApp.ts\n", "+++ src/lambda/commands/createNewSamApp.ts\n", "@@ -355,7 +355,7 @@\n", "     files: string[]\n", " ): Promise<vscode.Uri | undefined> {\n", "     let file: string = files[0]\n", "-    let cfnTemplatePath: string = path.resolve(config.location.fsPath, config.name, file)\n", "+    let cfnTemplatePath: string\n", "     for (let i = 0; i < files.length; i++) {\n", "          file = files[i];\n", "          cfnTemplatePath = path.resolve(config.location.fsPath, config.name, file)\n", "```\n", "\n", "REVISED COMMENT\n", "don't initialize cfnTemplatePath\n", "INPUT COMMENT\n", "It might be a bit faster to make a raw `update()` query to update the time.\n", "CODE CHANGE\n", "\n", "```\n", "--- netbox/netbox/api/authentication.py\n", "+++ netbox/netbox/api/authentication.py\n", "@@ -30,8 +30,7 @@\n", "                 logger = logging.getLogger('netbox.auth.login')\n", "                 logger.warning(\"Maintenance mode enabled: disabling update of token's last used timestamp\")\n", "             else:\n", "-                token.last_used = timezone.now()\n", "-                token.save()\n", "+                Token.objects.filter(pk=token.pk).update(last_used=timezone.now())\n", " \n", "         # Enforce the Token's expiration time, if one has been set.\n", "         if token.is_expired:\n", "```\n", "\n", "REVISED COMMENT\n", "use raw update query\n", "\n"]}], "source": ["from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer, DeepSeekLLMChatTokenizer\n", "\n", "# Use this tokenizer if you are using DeepSeek-Coder model\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "\n", "# Use this tokenizer if you are using DeepSeek-LLM-Chat\n", "# tokenizer = DeepSeekLLMChatTokenizer()\n", "\n", "# https://gist.github.com/urikz/67ab79a25e1608aa7be9d01ecfd667b5\n", "\n", "UNCLEAR = \"UNCLEAR\"\n", "\n", "MANUAL_LABELS = {\n", "    \"https://github.com/uclapi/uclapi/pull/3052#discussion_r546840898\": UNCLEAR,\n", "    \"https://github.com/jenkinsci/jenkins-test-harness/pull/465#discussion_r935642731\": UNCLEAR,\n", "    \"https://github.com/Neuraxio/Neuraxle/pull/309#discussion_r424270695\": \"put separator after args and use constant\",\n", "    \"https://github.com/webex/webex-js-sdk/pull/1809#discussion_r462650960\": UNCLEAR,\n", "    \"https://github.com/carloscuesta/gitmoji-cli/pull/582#discussion_r622434992\": \"add type\",\n", "    \"https://github.com/DKU-STUDY/Algorithm/pull/337#discussion_r464035670\": UNCLEAR,\n", "    \"https://github.com/pytorch/vision/pull/5264#discussion_r790429498\": \"improve error message\",\n", "    \"https://github.com/aleph-im/pyaleph/pull/248#discussion_r864943892\": \"use single number with underscores\",\n", "    \"https://github.com/OSGeo/grass-addons/pull/222#discussion_r446030744\": \"add module descrption\",\n", "    \"https://github.com/elastic/apm-agent-nodejs/pull/2904#discussion_r960090284\": \"make it warning\",\n", "    \"https://github.com/reframe-hpc/reframe/pull/1925#discussion_r614224321\": UNCLEAR,\n", "    \"https://github.com/bridgecrewio/checkov/pull/4427#discussion_r1099838181\": \"use empty string as default\",\n", "    \"https://github.com/apache/flink-training/pull/31#discussion_r697212832\": UNCLEAR,\n", "    \"https://github.com/apolloconfig/apollo/pull/4189#discussion_r777299171\": UNCLEAR,\n", "    \"https://github.com/greenplum-db/pxf/pull/703#discussion_r728486742\": \"rewrite in one line\",\n", "    \"https://github.com/IntellectualSites/FastAsyncWorldEdit/pull/1947#discussion_r972105824\": UNCLEAR,\n", "    \"https://github.com/sveltejs/kit/pull/6936#discussion_r976603418\": UNCLEAR,\n", "    \"https://github.com/umijs/umi/pull/4247#discussion_r396461436\": UNCLEAR,\n", "    \"https://github.com/aws/aws-toolkit-vscode/pull/1764#discussion_r636532097\": \"don't initialize cfnTemplatePath\",\n", "    \"https://github.com/netbox-community/netbox/pull/9554#discussion_r905098669\": \"use raw update query\",\n", "}\n", "\n", "TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>You are an AI programming assistant. Your task is to transform code review comments into clear, actionable instructions in the same language as the original comment, while ensuring they align with the associated code changes. Follow these guidelines:\n", "\n", "Language Consistency: The transformed instruction must be in the same language as the original code review comment. Do not translate or change the language.\n", "Focus on Instruction Transformation: Your output should strictly be a transformed, actionable instruction derived from the original comment. Place this after 'INPUT COMMENT'.\n", "Avoid Code Continuation: Do not provide any continuation or modification of the code. The response should only include the revised instruction.\n", "Clear and Direct Instructions: Convert each comment into a direct, actionable instruction. Eliminate polite language, subjective opinions, and extraneous details.\n", "Address Ambiguity: If a comment is unclear, lacks critical information, or seems to reference other parts of the file not provided, respond with 'UNCLEAR' after 'INPUT COMMENT'.\n", "Code Change Context: Provide the code change formatted with backticks (`) after 'CODE CHANGE' for context. This code modification is not part of the transformed output.\n", "Ensure your output includes only the revised instruction in the same language as the original comment, placed after 'INPUT COMMENT'\n", "\n", "\"\"\"\n", "\n", "EXAMPLE = \"\"\"INPUT COMMENT\n", "{original_instruction}\n", "CODE CHANGE\n", "\n", "```\n", "{code_diff}\n", "```\n", "\n", "REVISED COMMENT\n", "{instruction}\n", "\"\"\"\n", "\n", "for sample_id, instruction in MANUAL_LABELS.items():\n", "    sample = filtered_suggested_edit_by_url[sample_id]\n", "    TEMPLATE = TEMPLATE + EXAMPLE.format(\n", "        original_instruction=sample['original_instruction'],\n", "        code_diff=get_diff(sample),\n", "        instruction=instruction)\n", "\n", "print(TEMPLATE)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ORIGINAL INSTRUCTION: What do you think if we don't follow the Flask-Admin behaviour here? I don't think it would be a problem here.\n", "\n", "--- sqladmin/models.py\n", "+++ sqladmin/models.py\n", "@@ -331,10 +331,9 @@\n", "     \"\"\"A list of available export filetypes.\n", "     Currently only `csv` is supported.\n", "     \"\"\"\n", "-    export_max_rows: ClassVar[Optional[int]] = 0\n", "+    export_max_rows: ClassVar[int] = 0\n", "     \"\"\"Maximum number of rows allowed for export.\n", "-\n", "-    Unlimited by default. Uses `page_size` if set to `None`.\n", "+    Unlimited by default.\n", "     \"\"\"\n", " \n", "     # Form\n", "\n", " REVISED INSTRUCTION: None\n", "\n", "==================================================\n"]}], "source": ["# @persistent_cache('/home/<USER>/data/deepseek_33b_instruct_cache.jsonl')\n", "def query_remote_model(sample):\n", "    text = TEMPLATE + \"\"\"INPUT COMMENT\n", "{original_instruction}\n", "CODE CHANGE\n", "\n", "```\n", "{code_diff}\n", "```\n", "\n", "REVISED COMMENT\n", "\"\"\".format(\n", "        original_instruction=sample['original_instruction'],\n", "        code_diff=get_diff(sample))\n", "\n", "    # prompt_tokens = [tokenizer.bos_id] + tokenizer.tokenize(text)\n", "    prompt_tokens = tokenizer.tokenize(text)\n", "    max_generated_tokens = 64\n", "    temperature = 0\n", "    top_k = 0\n", "    top_p = 1\n", "    data = {\n", "        \"prompt\": prompt_tokens,\n", "        \"n_predict\": max_generated_tokens,\n", "        \"temperature\": temperature,\n", "        \"top_k\": top_k or 50,\n", "        \"top_p\": top_p or 0.95,\n", "    }\n", "\n", "    response = requests.post(\n", "                \"http://localhost:8086/completion\",\n", "                json=data,\n", "                headers={\"Content-Type\": \"application/json\"},\n", "                timeout=120.0,  # set a timeout as 2 minutes\n", "            )\n", "    decoded_response = response.json()\n", "    content = decoded_response[\"content\"].strip()\n", "    if 'INPUT COMMENT' in content:\n", "        content = content[:content.find('INPUT COMMENT')].strip()\n", "    if content == 'UNCLEAR':\n", "        return None\n", "    content = ' '.join(content.splitlines())\n", "    return content\n", "\n", "url = \"https://github.com/aminalaee/sqladmin/pull/101#discussion_r832961082\"\n", "print('ORIGINAL INSTRUCTION:', filtered_suggested_edit_by_url[url]['original_instruction'])\n", "print()\n", "print(get_diff(filtered_suggested_edit_by_url[url]))\n", "print()\n", "print(' REVISED INSTRUCTION:', query_remote_model(filtered_suggested_edit_by_url[url]))\n", "print()\n", "print('=' * 50)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 52/102186 [04:24<144:08:20,  5.08s/it, mined_suggestions=33]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb Cell 21\u001b[0m line \u001b[0;36m1\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m sample \u001b[39m=\u001b[39m filtered_suggested_edit[index]\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m pbar\u001b[39m.\u001b[39mupdate(\u001b[39m1\u001b[39m)\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m fixed_instruction \u001b[39m=\u001b[39m query_remote_model(sample)\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m \u001b[39mif\u001b[39;00m fixed_instruction \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m     \u001b[39mcontinue\u001b[39;00m\n", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb Cell 21\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m top_p \u001b[39m=\u001b[39m \u001b[39m1\u001b[39m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m data \u001b[39m=\u001b[39m {\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mprompt\u001b[39m\u001b[39m\"\u001b[39m: prompt_tokens,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mn_predict\u001b[39m\u001b[39m\"\u001b[39m: max_generated_tokens,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mtop_p\u001b[39m\u001b[39m\"\u001b[39m: top_p \u001b[39mor\u001b[39;00m \u001b[39m0.95\u001b[39m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m }\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m response \u001b[39m=\u001b[39m requests\u001b[39m.\u001b[39;49mpost(\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m             \u001b[39m\"\u001b[39;49m\u001b[39mhttp://localhost:8086/completion\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=31'>32</a>\u001b[0m             json\u001b[39m=\u001b[39;49mdata,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=32'>33</a>\u001b[0m             headers\u001b[39m=\u001b[39;49m{\u001b[39m\"\u001b[39;49m\u001b[39mContent-Type\u001b[39;49m\u001b[39m\"\u001b[39;49m: \u001b[39m\"\u001b[39;49m\u001b[39mapplication/json\u001b[39;49m\u001b[39m\"\u001b[39;49m},\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=33'>34</a>\u001b[0m             timeout\u001b[39m=\u001b[39;49m\u001b[39m120.0\u001b[39;49m,  \u001b[39m# set a timeout as 2 minutes\u001b[39;49;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=34'>35</a>\u001b[0m         )\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=35'>36</a>\u001b[0m decoded_response \u001b[39m=\u001b[39m response\u001b[39m.\u001b[39mjson()\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X41sdnNjb2RlLXJlbW90ZQ%3D%3D?line=36'>37</a>\u001b[0m content \u001b[39m=\u001b[39m decoded_response[\u001b[39m\"\u001b[39m\u001b[39mcontent\u001b[39m\u001b[39m\"\u001b[39m]\u001b[39m.\u001b[39mstrip()\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[0;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mpost\u001b[39m(url, data\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, json\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[1;32m    105\u001b[0m \n\u001b[1;32m    106\u001b[0m \u001b[39m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[39m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 115\u001b[0m     \u001b[39mreturn\u001b[39;00m request(\u001b[39m\"\u001b[39;49m\u001b[39mpost\u001b[39;49m\u001b[39m\"\u001b[39;49m, url, data\u001b[39m=\u001b[39;49mdata, json\u001b[39m=\u001b[39;49mjson, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[39m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[39m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[39m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[39mwith\u001b[39;00m sessions\u001b[39m.\u001b[39mSession() \u001b[39mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[39mreturn\u001b[39;00m session\u001b[39m.\u001b[39;49mrequest(method\u001b[39m=\u001b[39;49mmethod, url\u001b[39m=\u001b[39;49murl, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[39m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mtimeout\u001b[39m\u001b[39m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mallow_redirects\u001b[39m\u001b[39m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[39m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49msend(prep, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49msend_kwargs)\n\u001b[1;32m    591\u001b[0m \u001b[39mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[39m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[39m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[39m=\u001b[39m adapter\u001b[39m.\u001b[39;49msend(request, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m    705\u001b[0m \u001b[39m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[39m=\u001b[39m preferred_clock() \u001b[39m-\u001b[39m start\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[39m=\u001b[39m TimeoutSauce(connect\u001b[39m=\u001b[39mtimeout, read\u001b[39m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39;49murlopen(\n\u001b[1;32m    487\u001b[0m         method\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mmethod,\n\u001b[1;32m    488\u001b[0m         url\u001b[39m=\u001b[39;49murl,\n\u001b[1;32m    489\u001b[0m         body\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mbody,\n\u001b[1;32m    490\u001b[0m         headers\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mheaders,\n\u001b[1;32m    491\u001b[0m         redirect\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    492\u001b[0m         assert_same_host\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    493\u001b[0m         preload_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    494\u001b[0m         decode_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[1;32m    495\u001b[0m         retries\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mmax_retries,\n\u001b[1;32m    496\u001b[0m         timeout\u001b[39m=\u001b[39;49mtimeout,\n\u001b[1;32m    497\u001b[0m         chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[1;32m    498\u001b[0m     )\n\u001b[1;32m    500\u001b[0m \u001b[39mexcept\u001b[39;00m (ProtocolError, \u001b[39mOSError\u001b[39;00m) \u001b[39mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m(err, request\u001b[39m=\u001b[39mrequest)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:714\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    711\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    713\u001b[0m \u001b[39m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 714\u001b[0m httplib_response \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_make_request(\n\u001b[1;32m    715\u001b[0m     conn,\n\u001b[1;32m    716\u001b[0m     method,\n\u001b[1;32m    717\u001b[0m     url,\n\u001b[1;32m    718\u001b[0m     timeout\u001b[39m=\u001b[39;49mtimeout_obj,\n\u001b[1;32m    719\u001b[0m     body\u001b[39m=\u001b[39;49mbody,\n\u001b[1;32m    720\u001b[0m     headers\u001b[39m=\u001b[39;49mheaders,\n\u001b[1;32m    721\u001b[0m     chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[1;32m    722\u001b[0m )\n\u001b[1;32m    724\u001b[0m \u001b[39m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    725\u001b[0m \u001b[39m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[39m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[39m# mess.\u001b[39;00m\n\u001b[1;32m    728\u001b[0m response_conn \u001b[39m=\u001b[39m conn \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m release_conn \u001b[39melse\u001b[39;00m \u001b[39mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:466\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    461\u001b[0m             httplib_response \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39mgetresponse()\n\u001b[1;32m    462\u001b[0m         \u001b[39mexcept\u001b[39;00m \u001b[39mBaseException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    463\u001b[0m             \u001b[39m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    464\u001b[0m             \u001b[39m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    465\u001b[0m             \u001b[39m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[0;32m--> 466\u001b[0m             six\u001b[39m.\u001b[39;49mraise_from(e, \u001b[39mNone\u001b[39;49;00m)\n\u001b[1;32m    467\u001b[0m \u001b[39mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    468\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_raise_timeout(err\u001b[39m=\u001b[39me, url\u001b[39m=\u001b[39murl, timeout_value\u001b[39m=\u001b[39mread_timeout)\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/urllib3/connectionpool.py:461\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    458\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mTypeError\u001b[39;00m:\n\u001b[1;32m    459\u001b[0m     \u001b[39m# Python 3\u001b[39;00m\n\u001b[1;32m    460\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 461\u001b[0m         httplib_response \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39;49mgetresponse()\n\u001b[1;32m    462\u001b[0m     \u001b[39mexcept\u001b[39;00m \u001b[39mBaseException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    463\u001b[0m         \u001b[39m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    464\u001b[0m         \u001b[39m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    465\u001b[0m         \u001b[39m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[1;32m    466\u001b[0m         six\u001b[39m.\u001b[39mraise_from(e, \u001b[39mNone\u001b[39;00m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:1377\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1375\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m   1376\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m-> 1377\u001b[0m         response\u001b[39m.\u001b[39;49mbegin()\n\u001b[1;32m   1378\u001b[0m     \u001b[39mexcept\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m:\n\u001b[1;32m   1379\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:320\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    318\u001b[0m \u001b[39m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    319\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[0;32m--> 320\u001b[0m     version, status, reason \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_read_status()\n\u001b[1;32m    321\u001b[0m     \u001b[39mif\u001b[39;00m status \u001b[39m!=\u001b[39m CONTINUE:\n\u001b[1;32m    322\u001b[0m         \u001b[39mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/http/client.py:281\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    280\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_read_status\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[0;32m--> 281\u001b[0m     line \u001b[39m=\u001b[39m \u001b[39mstr\u001b[39m(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mfp\u001b[39m.\u001b[39;49mreadline(_MAXLINE \u001b[39m+\u001b[39;49m \u001b[39m1\u001b[39;49m), \u001b[39m\"\u001b[39m\u001b[39miso-8859-1\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m    282\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mlen\u001b[39m(line) \u001b[39m>\u001b[39m _MAXLINE:\n\u001b[1;32m    283\u001b[0m         \u001b[39mraise\u001b[39;00m LineTooLong(\u001b[39m\"\u001b[39m\u001b[39mstatus line\u001b[39m\u001b[39m\"\u001b[39m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/socket.py:704\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    702\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[1;32m    703\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 704\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sock\u001b[39m.\u001b[39;49mrecv_into(b)\n\u001b[1;32m    705\u001b[0m     \u001b[39mexcept\u001b[39;00m timeout:\n\u001b[1;32m    706\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_timeout_occurred \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import numpy as np\n", "\n", "llmfixed_filtered_suggested_edit = []\n", "\n", "np.random.seed(31415)\n", "ids = np.random.permutation(len(filtered_suggested_edit))\n", "\n", "with tqdm.tqdm(total=len(filtered_suggested_edit)) as pbar:    \n", "    for index in ids:\n", "        sample = filtered_suggested_edit[index]\n", "        pbar.update(1)\n", "\n", "        fixed_instruction = query_remote_model(sample)\n", "        if fixed_instruction is None:\n", "            continue\n", "\n", "        fixed_sample = {'instruction': fixed_instruction}\n", "        fixed_sample.update(sample)\n", "        llmfixed_filtered_suggested_edit.append(fixed_sample)\n", "\n", "        pbar.set_postfix({\"mined_suggestions\": len(llmfixed_filtered_suggested_edit)})"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  ID: 893481999\n", "         INSTRUCTION: use find()\n", "ORIGINAL INSTRUCTION: To make it a little more precise and indicate that the button is inside the banner, what do you think about doing like this:\n", "          REVIEW URL: https://github.com/Codeminer42/cm42-central/pull/792#discussion_r893481999\n", "\n", "--- app/assets/javascripts/global_listeners.js\n", "+++ app/assets/javascripts/global_listeners.js\n", "@@ -7,7 +7,7 @@\n", " var $sidebarToggleIcon = $(\"#sidebar-toggle\").children('.mi');\n", " var $sidebarWrapper = $(\"#sidebar-wrapper\");\n", " var $cookiesBanner = $(\".cookies-banner\");\n", "-var $cookiesBannerBtn = $(\".cookies-banner__btn\");\n", "+var $cookiesBannerBtn = $cookiesBanner.find(\".cookies-banner__btn\");\n", " \n", " window.onload = function() {\n", "   var isCookiesAllowed = Cookies.get('allow_cookies') === 'allowed';\n", "====================================================================================================\n", "                  ID: 1108962431\n", "         INSTRUCTION: use else\n", "ORIGINAL INSTRUCTION: （こっちの方がきれいかも...？程度のsuggestionです）\n", "          REVIEW URL: https://github.com/VOICEVOX/voicevox_engine/pull/616#discussion_r1108962431\n", "\n", "--- voicevox_engine/downloadable_library.py\n", "+++ voicevox_engine/downloadable_library.py\n", "@@ -39,7 +39,7 @@\n", "             if downloadable_library.uuid == library_id:\n", "                 library_info = downloadable_library.dict()\n", "                 break\n", "-        if library_info is None:\n", "+        else:\n", "             raise HTTPException(status_code=404, detail=\"指定されたライブラリが見つかりません。\")\n", "         library_dir = self.root_dir / library_id\n", "         if not library_dir.exists():\n", "====================================================================================================\n", "                  ID: 386693749\n", "         INSTRUCTION: move rloc_status\n", "ORIGINAL INSTRUCTION: `rloc_status` was wrong location. moved.\n", "          REVIEW URL: https://github.com/CiscoTestAutomation/genieparser/pull/86#discussion_r386693749\n", "\n", "--- src/genie/libs/parser/iosxe/show_lisp.py\n", "+++ src/genie/libs/parser/iosxe/show_lisp.py\n", "@@ -2052,7 +2052,7 @@\n", "         # blue                  LISP0.102@    1      0      1   0.0%    0%  ITR-ETR\n", "         # blue                  LISP0.102*    1      0      1   0.0%    0%  ITR-ETR\n", "         p4_1 = re.compile(r'(?P<vrf>(\\S+)) +(?P<interface>(\\S+))\\.(?P<iid>(\\d+))'\n", "-                         ' +(?P<db_size>(\\d+))(?P<rloc_status>([@*]?)) +(?P<db_no_route>(\\d+))'\n", "+                         '(?P<rloc_status>[@\\*])? +(?P<db_size>(\\d+)) +(?P<db_no_route>(\\d+))'\n", "                          ' +(?P<cache_size>(\\d+)) +(?P<incomplete>(\\S+))'\n", "                          ' +(?P<cache_idle>(\\S+)) +(?P<role>(\\S+))$')\n", " \n", "====================================================================================================\n", "                  ID: 329308171\n", "         INSTRUCTION: use function\n", "ORIGINAL INSTRUCTION: wdyt?\n", "          REVIEW URL: https://github.com/kudobuilder/kudo/pull/850#discussion_r329308171\n", "\n", "--- pkg/util/health/ready.go\n", "+++ pkg/util/health/ready.go\n", "@@ -42,7 +42,7 @@\n", " \t\treturn fmt.Errorf(\"job \\\"%v\\\" still running or failed\", obj.Name)\n", " \tcase *kudov1alpha1.Instance:\n", " \t\tlog.Printf(\"HealthUtil: Instance %v is in state %v\", obj.Name, obj.Status.AggregatedStatus.Status)\n", "-\t\tif obj.Status.AggregatedStatus.Status == kudov1alpha1.ExecutionComplete {\n", "+\t\tif isFinished(obj.Status.AggregatedStatus.Status){\n", " \t\t\treturn nil\n", " \t\t}\n", " \t\treturn fmt.Errorf(\"instance's active plan is in state %v\", obj.Status.AggregatedStatus.Status)\n", "====================================================================================================\n", "                  ID: 903616770\n", "         INSTRUCTION: use Mi\n", "ORIGINAL INSTRUCTION: 1000 seems like not very much\n", "          REVIEW URL: https://github.com/iotaledger/wasp/pull/1086#discussion_r903616770\n", "\n", "--- tools/cluster/cluster.go\n", "+++ tools/cluster/cluster.go\n", "@@ -242,7 +242,7 @@\n", " \t}\n", " \tscParams := chainclient.\n", " \t\tNewPostRequestParams(scArgs.AsDict()).\n", "-\t\tWith<PERSON>otas(1000)\n", "+\t\tWithIotas(1*iscp.Mi)\n", " \tgovClient := chain.SCClient(governance.Contract.Hname(), chain.OriginatorKeyPair)\n", " \ttx, err := govClient.PostRequest(governance.FuncChangeAccessNodes.Name, *scParams)\n", " \tif err != nil {\n", "====================================================================================================\n", "                  ID: 299316536\n", "         INSTRUCTION: use lower case\n", "ORIGINAL INSTRUCTION: better to be more verbose, and more standard design, I've never seen an upper case argument before\n", "          REVIEW URL: https://github.com/jenkinsci/plugin-installation-manager-tool/pull/24#discussion_r299316536\n", "\n", "--- plugin-management-cli/src/main/java/io/jenkins/tools/pluginmanager/cli/CliOptions.java\n", "+++ plugin-management-cli/src/main/java/io/jenkins/tools/pluginmanager/cli/CliOptions.java\n", "@@ -35,7 +35,7 @@\n", "             handler = BooleanOptionHandler.class)\n", "     public boolean showAllWarnings;\n", " \n", "-    @Option(name = \"-JENKINS_UC\",\n", "+    @Option(name = \"-jenkins-update-center\",\n", "             usage = \"Sets main update center; will override environment variable. If not set via CLI option or \" +\n", "                     \"environment variable, will default to https://updates.jenkins.io\")\n", "     public String jenkinsUc;\n", "====================================================================================================\n", "                  ID: 674226465\n", "         INSTRUCTION: move to outer level\n", "ORIGINAL INSTRUCTION: nit: This is not important but I could suggest that the returned values sit on the outer level.\n", "          REVIEW URL: https://github.com/huawei-noah/SMARTS/pull/1008#discussion_r674226465\n", "\n", "--- smarts/core/smarts.py\n", "+++ smarts/core/smarts.py\n", "@@ -223,6 +223,8 @@\n", "             # so that all updates are ready before rendering happens per frame\n", "             with timeit(\"Running through render pipeline\", self._log):\n", "                 self._renderer.render()\n", "+        observations, rewards, scores, dones = None, None, None, None\n", "+        response_for_ego = None\n", "         with timeit(\"calculating observations and rewards\", self._log):\n", "             observations, rewards, scores, dones = self._agent_manager.observe(self)\n", "             response_for_ego = self._agent_manager.filter_response_for_ego(\n", "====================================================================================================\n", "                  ID: 1150726348\n", "         INSTRUCTION: use const\n", "ORIGINAL INSTRUCTION: We only all-caps variables that are compiled out at build time. I would do:\n", "          REVIEW URL: https://github.com/lit/lit/pull/3768#discussion_r1150726348\n", "\n", "--- packages/lit-html/src/directives/style-map.ts\n", "+++ packages/lit-html/src/directives/style-map.ts\n", "@@ -23,8 +23,11 @@\n", " export interface StyleInfo {\n", "   [name: string]: string | undefined | null;\n", " }\n", "-const IMPORTNAT_PRIORITY = 'important';\n", "-const IMPORTANT_DIRECTIVE = '!important';\n", "+const important = 'important';\n", "+// The leading space is important\n", "+const importantFlag = ' !' + important;\n", "+// How many characters to remove from a value, as a negative number\n", "+const flagTrim = 1 - importantFlag.length;\n", " \n", " class StyleMapDirective extends Directive {\n", "   _previousStyleProperties?: Set<string>;\n", "====================================================================================================\n", "                  ID: 249550574\n", "         INSTRUCTION: use guard clause\n", "ORIGINAL INSTRUCTION: @lh<PERSON><PERSON> Does also following follow the guard clause pattern ?\n", "          REVIEW URL: https://github.com/techqueria/data-structures-and-algorithms/pull/16#discussion_r249550574\n", "\n", "--- Python/chapter01/1.4 - PalinPerm/Nick4.py\n", "+++ Python/chapter01/1.4 - PalinPerm/Nick4.py\n", "@@ -17,7 +17,13 @@\n", "     table = {}\n", "     for letter in string.lower().replace(\" \", \"\"):\n", "         table[letter] = table.get(letter, 0) + 1\n", "-    count = 0\n", "+    odd_frequency_count = 0\n", "+    for frequency in letter_frequencies.values:\n", "+        if frequency % 2 == 1:\n", "+            odd_frequency_count += 1\n", "+            if odd_frequency_count > 1:\n", "+             return False\n", "+    return True\n", "     for key in table:\n", "         if table[key] % 2 == 1:\n", "             count += 1\n", "====================================================================================================\n", "                  ID: 583114740\n", "         INSTRUCTION: add return type\n", "ORIGINAL INSTRUCTION: since you only check the result for truthiness, should the result actually be a `boolean` instead? See below.\n", "          REVIEW URL: https://github.com/Agoric/agoric-sdk/pull/2486#discussion_r583114740\n", "\n", "--- packages/xsnap/src/avaAssertXS.js\n", "+++ packages/xsnap/src/avaAssertXS.js\n", "@@ -165,7 +165,7 @@\n", " /**\n", "  * @param {*} exc\n", "  * @param {Expectation} expectation\n", "- *\n", "+ * @returns {boolean}\n", "  * @typedef {{ instanceOf: Function } | { message: string | RegExp }=} Expectation\n", "  */\n", " function checkExpectation(exc, expectation) {\n", "====================================================================================================\n", "                  ID: 984537504\n", "         INSTRUCTION: use apoc.util.validatePredicate\n", "ORIGINAL INSTRUCTION: I'm sure you already have tabs on this, but this is so close! 😧\n", "          REVIEW URL: https://github.com/neo4j/graphql/pull/2105#discussion_r984537504\n", "\n", "--- packages/graphql/tests/tck/directives/auth/projection-connection.test.ts\n", "+++ packages/graphql/tests/tck/directives/auth/projection-connection.test.ts\n", "@@ -80,7 +80,7 @@\n", " \n", "         expect(formatCypher(result.cypher)).toMatchInlineSnapshot(`\n", "             \"MATCH (this:\\`User\\`)\n", "-            CALL apoc.util.validate(NOT ((this.id IS NOT NULL AND this.id = $thisauth_param0)), \\\\\"@neo4j/graphql/FORBIDDEN\\\\\", [0])\n", "+            WHERE apoc.util.validatePredicate(NOT ((this.id IS NOT NULL AND this.id = $thisauth_param0)), \\\\\"@neo4j/graphql/FORBIDDEN\\\\\", [0])\n", "             CALL {\n", "                 WITH this\n", "                 MATCH (this)-[this_connection_postsConnectionthis0:HAS_POST]->(this_Post:\\`Post\\`)\n", "====================================================================================================\n", "                  ID: **********\n", "         INSTRUCTION: use `write_text`\n", "ORIGINAL INSTRUCTION: Same note here for [`Path::write_text`](https://docs.python.org/3/library/pathlib.html#pathlib.Path.write_text):\n", "          REVIEW URL: https://github.com/WordPress/openverse-catalog/pull/790#discussion_r**********\n", "\n", "--- openverse_catalog/templates/create_provider_ingester.py\n", "+++ openverse_catalog/templates/create_provider_ingester.py\n", "@@ -56,12 +56,11 @@\n", "     media_types: list[str],\n", "     name: str,\n", " ):\n", "-    with target.open(\"w\", encoding=\"utf-8\") as target_file:\n", "-        filled_template = _get_filled_template(\n", "-            template_path, provider, endpoint, media_types\n", "-        )\n", "-        target_file.write(filled_template)\n", "-        print(f\"{name + ':':<18} {target.relative_to(PROJECT_PATH)}\")\n", "+    filled_template = _get_filled_template(\n", "+        template_path, provider, endpoint, media_types\n", "+    )\n", "+    target.write_text(filled_template)\n", "+    print(f\"{name + ':':<18} {target.relative_to(PROJECT_PATH)}\")\n", " \n", " \n", " def fill_template(provider, endpoint, media_types):\n", "====================================================================================================\n", "                  ID: **********\n", "         INSTRUCTION: fix typo\n", "ORIGINAL INSTRUCTION: typo?\n", "          REVIEW URL: https://github.com/denoland/denobyexample/pull/47#discussion_r**********\n", "\n", "--- data/tcp-connector.ts\n", "+++ data/tcp-connector.ts\n", "@@ -3,7 +3,7 @@\n", "  * @difficulty intermediate\n", "  * @tags cli\n", "  * @run --allow-net <url>\n", "- * @resource {https://deno.land/api?s=Deno.listen} Deno.listen: API\n", "+ * @resource {https://deno.land/api?s=Deno.connect} Deno.connect: API\n", "  * @resource {https://github.com/denoland/denobyexample/blob/main/data/tcp-listener.ts} Example TCP server that will accept connections\n", "  *\n", "  * An example of connecting to a TCP server on localhost and writing a 'ping' message to the server.\n", "====================================================================================================\n", "                  ID: 846744857\n", "         INSTRUCTION: fix typo\n", "ORIGINAL INSTRUCTION: \"UID not fond\" has a typo and it would also be nice when the `@ApiReponse`s are sorted on the `responseCode` in all the new operations.\n", "          REVIEW URL: https://github.com/openhab/openhab-core/pull/2821#discussion_r846744857\n", "\n", "--- bundles/org.openhab.core.io.rest.transform/src/main/java/org/openhab/core/io/rest/transform/internal/TransformationConfigurationResource.java\n", "+++ bundles/org.openhab.core.io.rest.transform/src/main/java/org/openhab/core/io/rest/transform/internal/TransformationConfigurationResource.java\n", "@@ -166,8 +166,8 @@\n", "     @Produces(MediaType.TEXT_PLAIN)\n", "     @Operation(operationId = \"deleteTransformationConfiguration\", summary = \"Get a single transformation configuration\", responses = {\n", "             @ApiResponse(responseCode = \"200\", description = \"OK\"),\n", "-            @ApiResponse(responseCode = \"405\", description = \"Configuration not editable\"),\n", "-            @ApiResponse(responseCode = \"404\", description = \"UID not fond\") })\n", "+            @ApiResponse(responseCode = \"404\", description = \"UID not found\"),\n", "+            @ApiResponse(responseCode = \"405\", description = \"Configuration not editable\") })\n", "     public Response deleteTransformationConfiguration(\n", "             @PathParam(\"uid\") @Parameter(description = \"Configuration UID\") String uid) {\n", "         logger.debug(\"Received HTTP DELETE request at '{}'\", uriInfo.getPath());\n", "====================================================================================================\n", "                  ID: 983691133\n", "         INSTRUCTION: add timeout\n", "ORIGINAL INSTRUCTION: Should the httpClient have a timeout?\n", "          REVIEW URL: https://github.com/status-im/status-go/pull/2884#discussion_r983691133\n", "\n", "--- protocol/discord/assets.go\n", "+++ protocol/discord/assets.go\n", "@@ -18,7 +18,7 @@\n", " }\n", " \n", " func DownloadAsset(url string) ([]byte, string, error) {\n", "-\tclient := http.Client{}\n", "+\tclient := http.Client{Timeout: time.Minute} // 1 min\n", " \tres, err := client.Get(url)\n", " \tif err != nil {\n", " \t\treturn nil, \"\", err\n", "====================================================================================================\n", "                  ID: 967399908\n", "         INSTRUCTION: drop exclamation mark\n", "ORIGINAL INSTRUCTION: Let's drop the exclamation mark here and make the success message a little more generic:\n", "          REVIEW URL: https://github.com/Shopify/cli/pull/405#discussion_r967399908\n", "\n", "--- packages/cli-hydrogen/src/cli/services/deploy.ts\n", "+++ packages/cli-hydrogen/src/cli/services/deploy.ts\n", "@@ -79,7 +79,7 @@\n", "         if (retryCount && !isUnitTest) await system.sleep(backoffPolicy[retryCount - 1]!)\n", " \n", "         await healthCheck(ctx.previewURL)\n", "-        task.title = '✅ Deployed and healthy!'\n", "+        task.title = '✅ Deployed successfully'\n", "       },\n", "       retry: backoffPolicy.length,\n", "       skip: (ctx) => !ctx.config.healthCheck,\n", "====================================================================================================\n", "                  ID: 728170392\n", "         INSTRUCTION: use optional chaining\n", "ORIGINAL INSTRUCTION: I suppose in this way it's more readable\n", "          REVIEW URL: https://github.com/pagopa/io-app/pull/3424#discussion_r728170392\n", "\n", "--- ts/utils/messages.ts\n", "+++ ts/utils/messages.ts\n", "@@ -214,11 +214,7 @@\n", " \n", " const hasMetadataTokenName = (\n", "   metadata?: ServicePublicService_metadata\n", "-): boolean =>\n", "-  fromNullable(metadata).fold(\n", "-    false,\n", "-    m => m !== undefined && m.token_name !== undefined\n", "-  );\n", "+): boolean => metadata?.token_name !== undefined;\n", " \n", " // a mapping between routes name (the key) and predicates (the value)\n", " // the predicate says if for that specific route the navigation is allowed\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: use to.BoolPtr\n", "ORIGINAL INSTRUCTION: The rest of the file uses `to.*`\n", "          REVIEW URL: https://github.com/kubernetes-sigs/cluster-api-provider-azure/pull/1478#discussion_r*********\n", "\n", "--- azure/services/virtualmachines/virtualmachines.go\n", "+++ azure/services/virtualmachines/virtualmachines.go\n", "@@ -203,7 +203,7 @@\n", " \t\tfor _, dataDisk := range vmSpec.DataDisks {\n", " \t\t\tif dataDisk.ManagedDisk != nil && dataDisk.ManagedDisk.StorageAccountType == UltraSSDStorageAccountType {\n", " \t\t\t\tvirtualMachine.VirtualMachineProperties.AdditionalCapabilities = &compute.AdditionalCapabilities{\n", "-\t\t\t\t\tUltraSSDEnabled: pointer.<PERSON><PERSON>(true),\n", "+\t\t\t\t\tUltraSSDEnabled: to.<PERSON>(true),\n", " \t\t\t\t}\n", " \t\t\t}\n", " \t\t}\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: add type\n", "ORIGINAL INSTRUCTION: maybe this? Feeling less sure about these Any values, but the str is for sure right.\n", "          REVIEW URL: https://github.com/NixOS/nixops/pull/1253#discussion_r*********\n", "\n", "--- nixops/resources/__init__.py\n", "+++ nixops/resources/__init__.py\n", "@@ -72,7 +72,7 @@\n", "         self.id = id\n", "         self.logger = depl.logger.get_logger_for(name)\n", "         self.logger.register_index(self.index)\n", "-    def _set_attrs(self, attrs) -> None:\n", "+    def _set_attrs(self, attrs: Dict[str, Any]) -> None:\n", "         \"\"\"Update machine attributes in the state file.\"\"\"\n", "         with self.depl._db:\n", "             c = self.depl._db.cursor()\n", "====================================================================================================\n", "                  ID: 513258349\n", "         INSTRUCTION: add null check\n", "ORIGINAL INSTRUCTION: It looks like we need a null check here, to avoid a `NullPointerException` when no graphics is selected and we're dragging around:\n", "          REVIEW URL: https://github.com/Esri/arcgis-maps-sdk-java-samples/pull/579#discussion_r513258349\n", "\n", "--- display_information/update-graphics/src/main/java/com/esri/samples/update_graphics/UpdateGraphicsSample.java\n", "+++ display_information/update-graphics/src/main/java/com/esri/samples/update_graphics/UpdateGraphicsSample.java\n", "@@ -195,7 +195,7 @@\n", "       });\n", " \n", "       mapView.setOnMouseDragged(e -> {\n", "-        if (selectedGraphic.isSelected()) {\n", "+        if (selectedGraphic != null && selectedGraphic.isSelected()) {\n", "           // set the cursor to the move\n", "           mapView.setCursor(Cursor.MOVE);\n", " \n", "====================================================================================================\n", "                  ID: 393773591\n", "         INSTRUCTION: add spaces\n", "ORIGINAL INSTRUCTION: And there should be spaces before and after `==`\n", "          REVIEW URL: https://github.com/bisq-network/bisq/pull/4068#discussion_r393773591\n", "\n", "--- desktop/src/main/java/bisq/desktop/main/offer/offerbook/OfferBookView.java\n", "+++ desktop/src/main/java/bisq/desktop/main/offer/offerbook/OfferBookView.java\n", "@@ -293,16 +293,18 @@\n", "         model.priceSortTypeProperty.addListener((observable, oldValue, newValue) -> priceColumn.setSortType(newValue));\n", "         priceColumn.setSortType(model.priceSortTypeProperty.get());\n", "         amountColumn.sortTypeProperty().addListener((observable, oldValue, newValue) -> {\n", "-            if (newValue==TableColumn.SortType.DESCENDING)\n", "+            if (newValue == TableColumn.SortType.DESCENDING) {\n", "                 amountColumn.setComparator(Comparator.comparing(o -> o.getOffer().getAmount(), Comparator.nullsFirst(Comparator.naturalOrder())));\n", "-            else\n", "+            } else {\n", "                 amountColumn.setComparator(Comparator.comparing(o -> o.getOffer().getMinAmount(), Comparator.nullsFirst(Comparator.naturalOrder())));\n", "+            }\n", "         });\n", "         volumeColumn.sortTypeProperty().addListener((observable, oldValue, newValue) -> {\n", "-            if (newValue==TableColumn.SortType.DESCENDING)\n", "+            if (newValue == TableColumn.SortType.DESCENDING) {\n", "                 volumeColumn.setComparator(Comparator.comparing(o -> o.getOffer().getVolume(), Comparator.nullsFirst(Comparator.naturalOrder())));\n", "-            else\n", "+            } else {\n", "                 volumeColumn.setComparator(Comparator.comparing(o -> o.getOffer().getMinVolume(), Comparator.nullsFirst(Comparator.naturalOrder())));\n", "+            }\n", "         });\n", " \n", "         paymentMethodComboBox.setConverter(new PaymentMethodStringConverter(paymentMethodComboBox));\n", "====================================================================================================\n", "                  ID: 446302262\n", "         INSTRUCTION: use callable\n", "ORIGINAL INSTRUCTION: Wouldn't that be better?\n", "          REVIEW URL: https://github.com/Lightning-AI/lightning/pull/2375#discussion_r446302262\n", "\n", "--- pytorch_lightning/trainer/data_loading.py\n", "+++ pytorch_lightning/trainer/data_loading.py\n", "@@ -285,10 +285,7 @@\n", "         # datasets could be none, 1 or 2+\n", "         if len(dataloaders) != 0:\n", "             for i, dataloader in enumerate(dataloaders):\n", "-                try:\n", "-                    num_batches = len(dataloader)\n", "-                except (TypeError, NotImplementedError):\n", "-                    num_batches = float('inf')\n", "+                num_batches = len(dataloader) if callable(getattr(dataloader, \"__len__\", None)) else float(\"inf\")\n", " \n", "                 self._worker_check(dataloader, f'{mode} dataloader {i}')\n", " \n", "====================================================================================================\n", "                  ID: 733915731\n", "         INSTRUCTION: grammar\n", "ORIGINAL INSTRUCTION: Small capitalization/grammar cleanup:\n", "          REVIEW URL: https://github.com/cisagov/gophish-tools/pull/51#discussion_r733915731\n", "\n", "--- src/tools/gophish_export.py\n", "+++ src/tools/gophish_export.py\n", "@@ -403,8 +403,8 @@\n", " \n", "     if not validate_assessment_id(args[\"ASSESSMENT_ID\"]):\n", "         logging.critical(\n", "-            '\"%s\" is an invalid assessment_id format. Assessment Identifiers begin with RV and followed by a 4 or 5 '\n", "-            \"digit numerical sequence. Example: RV1234\",\n", "+            '\"%s\" is an invalid assessment_id format. Assessment identifiers begin with RV and are followed by '\n", "+            \" a 4 or 5 digit numerical sequence. Examples: RV1234, RV12345\",\n", "             args[\"ASSESSMENT_ID\"],\n", "         )\n", "         sys.exit(1)\n", "====================================================================================================\n", "                  ID: 974458775\n", "         INSTRUCTION: use call_unmount\n", "ORIGINAL INSTRUCTION: Similar to comments above, to use a dictionary as an input each value must be a `Variant` with the right signature.\n", "          REVIEW URL: https://github.com/home-assistant/supervisor/pull/3848#discussion_r974458775\n", "\n", "--- supervisor/dbus/udisks2/filesystem.py\n", "+++ supervisor/dbus/udisks2/filesystem.py\n", "@@ -31,7 +31,7 @@\n", "     @dbus_connected\n", "     async def mount(self, options: dict[str, Any]):\n", "         \"\"\"Mount filesystem.\"\"\"\n", "-        await self.dbus.Filesystem.Mount((\"a{sv}\", options))\n", "+        await self.dbus.Filesystem.call_mount(options)\n", " \n", "     @dbus_connected\n", "     async def unmount(self, options: dict[str, Any]):\n", "====================================================================================================\n", "                  ID: *********\n", "         INSTRUCTION: use if\n", "ORIGINAL INSTRUCTION: IIUC `realGroupFields` can be empty\n", "          REVIEW URL: https://github.com/DIRACGrid/DIRAC/pull/5420#discussion_r*********\n", "\n", "--- src/DIRAC/AccountingSystem/DB/AccountingDB.py\n", "+++ src/DIRAC/AccountingSystem/DB/AccountingDB.py\n", "@@ -1158,7 +1158,8 @@\n", "         for testGroupFields in testGroupFieldsList:\n", "           if \"sum\" not in testGroupFields.lower():\n", "             realGroupFields += (testGroupFields.strip(),)\n", "-        cmd += \" GROUP BY \" + ','.join(realGroupFields)\n", "+        if realGroupFields:\n", "+          cmd += \" GROUP BY \" + ','.join(realGroupFields)\n", "       else:\n", "         cmd += \" GROUP BY %s\" % (groupFields[0] % tuple(groupFields[1]))\n", "     if orderFields:\n", "====================================================================================================\n", "                  ID: **********\n", "         INSTRUCTION: use index\n", "ORIGINAL INSTRUCTION: The `_` creeps up to the public API. This is a better way:\n", "          REVIEW URL: https://github.com/winglang/wing/pull/1045#discussion_r**********\n", "\n", "--- libs/wingsdk/src/std/array.ts\n", "+++ libs/wingsdk/src/std/array.ts\n", "@@ -14,8 +14,9 @@\n", "    * @param _index index of the value to get\n", "    * @returns the value at the given index\n", "    */\n", "-  public at(_index: number): any {\n", "-    return 0 as any;\n", "+  public at(index: number): any {\n", "+    index;\n", "+    throw new Error(\"abstract\");\n", "   }\n", " \n", "   /**\n", "====================================================================================================\n", "                  ID: 791254150\n", "         INSTRUCTION: use function\n", "ORIGINAL INSTRUCTION: and I think we replicate this bit of logic above in the original endpoint function\n", "          REVIEW URL: https://github.com/deso-protocol/backend/pull/276#discussion_r791254150\n", "\n", "--- routes/post.go\n", "+++ routes/post.go\n", "@@ -1281,31 +1281,11 @@\n", " \t// Figure out if the current poster is greylisted.  If the current poster is greylisted, we will add their\n", " \t// public key back to the filteredProfileMap so their profile will appear for the current post\n", " \t// and any parents, but we will remove any comments by this greylisted user.\n", "+        posterPKID := utxoView.GetPKIDForPublicKey(posterPublicKeyBytes)\n", " \tisCurrentPosterGreylisted := false\n", "-\tif _, ok := filteredProfilePubKeyMap[lib.MakePkMapKey(posterPublicKeyBytes)]; !ok {\n", "-\t\t// Get the userMetadata for the currentPosters\n", "-\t\tcurrentPosterUserMetadataKey := append([]byte{}, _GlobalStatePrefixPublicKeyToUserMetadata...)\n", "-\t\tcurrentPosterUserMetadataKey = append(currentPosterUserMetadataKey, posterPublicKeyBytes...)\n", "-\t\tvar currentPosterUserMetadataBytes []byte\n", "-\t\tcurrentPosterUserMetadataBytes, err = fes.GlobalState.Get(currentPosterUserMetadataKey)\n", "-\t\tif err != nil {\n", "-\t\t\treturn nil, err\n", "-\t\t}\n", "-\t\t// If the currentPoster's userMetadata doesn't exist, then they are no greylisted, so we can exit.\n", "-\t\tif currentPosterUserMetadataBytes != nil {\n", "-\t\t\t// Decode the currentPoster's userMetadata.\n", "-\t\t\tcurrentPosterUserMetadata := UserMetadata{}\n", "-\t\t\terr = gob.NewDecoder(bytes.NewReader(currentPosterUserMetadataBytes)).Decode(&currentPosterUserMetadata)\n", "-\t\t\tif err != nil {\n", "-\t\t\t\treturn nil, err\n", "-\t\t\t}\n", "-\t\t\t// If the currentPoster is not blacklisted (removed everywhere) and is greylisted (removed from leaderboard)\n", "-\t\t\t// add them back to the filteredProfilePubKeyMap and note that the currentPoster is greylisted.\n", "-\t\t\tif currentPosterUserMetadata.RemoveFromLeaderboard && !currentPosterUserMetadata.RemoveEverywhere {\n", "-\t\t\t\tisCurrentPosterGreylisted = true\n", "-\t\t\t\tfilteredProfilePubKeyMap[lib.MakePkMapKey(posterPublicKeyBytes)] = posterPublicKeyBytes\n", "-\t\t\t}\n", "-\t\t}\n", "+\tif fes.IsUserGraylisted(posterPKID.PKID) {\n", "+\t\tisCurrentPosterGreylisted = true\n", "+\t\tfilteredProfilePubKeyMap[lib.MakePkMapKey(posterPublicKeyBytes)] = posterPublicKeyBytes\n", " \t}\n", " \n", " \t// If the profile that posted this post is not in our filtered list, return with error.\n", "====================================================================================================\n", "                  ID: 800491134\n", "         INSTRUCTION: use sort.Reverse\n", "ORIGINAL INSTRUCTION: Yes you're right. Please use sort.Sort to conduct sort\n", "https://stackoverflow.com/questions/18343208/how-do-i-reverse-sort-a-slice-of-integer-go\n", "          REVIEW URL: https://github.com/seata/seata-go/pull/102#discussion_r800491134\n", "\n", "--- pkg/tc/session/global_session.go\n", "+++ pkg/tc/session/global_session.go\n", "@@ -170,7 +170,7 @@\n", " \n", " func (gs *GlobalSession) GetReverseSortedBranches() []*BranchSession {\n", " \tvar branchSessions = gs.GetSortedBranches()\n", "-\tsort.Reverse(BranchSessionSlice(branchSessions)) // TODO: 这里并没有倒序？\n", "+\tsort.Sort(sort.Reverse(BranchSessionSlice(branchSessions)))\n", " \treturn branchSessions\n", " }\n", " \n", "====================================================================================================\n", "                  ID: 812423175\n", "         INSTRUCTION: add whitespace\n", "ORIGINAL INSTRUCTION: Nit: Whitespace.\n", "          REVIEW URL: https://github.com/ActiveState/cli/pull/1716#discussion_r812423175\n", "\n", "--- pkg/cmdlets/auth/login.go\n", "+++ pkg/cmdlets/auth/login.go\n", "@@ -189,24 +189,31 @@\n", " \tif err != nil {\n", " \t\treturn locale.WrapError(err, \"err_auth_device\")\n", " \t}\n", "+\t\n", " \tif response.UserCode == nil || response.VerificationURIComplete == nil {\n", " \t\tlogging.Error(\"Platform API error: device authorization request's response has nil UserCode and/or VerificationURIComplete\")\n", " \t\treturn locale.NewError(\"err_auth_device\")\n", " \t}\n", "+\t\n", " \tout.Notice(locale.Tr(\"auth_device_verify_security_code\", *response.UserCode))\n", "+\t\n", " \terr = OpenURI(*response.VerificationURIComplete)\n", " \tif err != nil {\n", " \t\tlogging.Error(\"Could not open browser: %v\", err)\n", " \t\tout.Notice(locale.Tr(\"err_browser_open\", *response.VerificationURIComplete))\n", " \t}\n", "+\t\n", " \tauthorization, err := model.WaitForAuthorization(response)\n", " \tif err != nil {\n", " \t\treturn locale.WrapError(err, \"err_auth_device\")\n", " \t}\n", "+\t\n", " \terr = authentication.LegacyGet().AuthenticateWithDevice(authorization.AccessToken)\n", " \tif err != nil {\n", " \t\treturn locale.WrapError(err, \"err_auth_device\")\n", " \t}\n", "+\t\n", " \tout.Notice(locale.T(\"auth_device_success\"))\n", "-\treturn nil\n", "-}\n", "+\t\n", "+\treturn nil\n", "+}\n", "====================================================================================================\n", "                  ID: 1142915222\n", "         INSTRUCTION: use deprecated\n", "ORIGINAL INSTRUCTION: Goes with newly deprecated properties in the `AbstractBot`.\n", "          REVIEW URL: https://github.com/sopel-irc/sopel/pull/2341#discussion_r1142915222\n", "\n", "--- sopel/irc/__init__.py\n", "+++ sopel/irc/__init__.py\n", "@@ -47,6 +47,7 @@\n", "     TYPE_CHECKING,\n", " )\n", " from sopel import tools, trigger\n", "+from sopel.lifecycle import deprecated\n", " from sopel.tools import identifiers\n", " from .backends import AsyncioBackend, UninitializedBackend\n", " from .capabilities import Capabilities\n", "====================================================================================================\n", "                  ID: 870048052\n", "         INSTRUCTION: use raw update query\n", "ORIGINAL INSTRUCTION: Nit, this could be a bit easier to understand\n", "          REVIEW URL: https://github.com/gridsingularity/gsy-e/pull/1435#discussion_r870048052\n", "\n", "--- src/gsy_e/models/strategy/predefined_load.py\n", "+++ src/gsy_e/models/strategy/predefined_load.py\n", "@@ -64,9 +64,9 @@\n", " \n", "     def read_or_rotate_profiles(self, reconfigure=False):\n", "         \"\"\"Read power profiles or rotate them, from the DB or from JSON dicts.\"\"\"\n", "-        input_profile = (self._load_profile_input\n", "-                         if reconfigure or not self._load_profile_W\n", "-                         else self._load_profile_W)\n", "+        input_profile = (self._load_profile_W\n", "+                         if reconfigure and self._load_profile_W\n", "+                         else self. _load_profile_input)\n", " \n", "         if global_objects.profiles_handler.should_create_profile(\n", "                 self._load_profile_kWh) or reconfigure:\n", "====================================================================================================\n", "                  ID: 870120236\n", "         INSTRUCTION: use if instead of try\n", "ORIGINAL INSTRUCTION: @h-<PERSON><PERSON> can you change it this way??\n", "          REVIEW URL: https://github.com/SpikeInterface/spikeinterface/pull/596#discussion_r870120236\n", "\n", "--- spikeinterface/extractors/neoextractors/spikeglx.py\n", "+++ spikeinterface/extractors/neoextractors/spikeglx.py\n", "@@ -44,9 +44,9 @@\n", "             meta_filename = signals_info_dict[self.stream_id][\"meta_file\"]\n", "             # Load probe geometry if available\n", "             probe = pi.read_spikeglx(meta_filename)\n", "-            try:\n", "+            if probe.shank_ids is not None:\n", "                 self.set_probe(probe, in_place=True, group_mode=\"by_shank\")\n", "-            except:\n", "+            else:\n", "                 self.set_probe(probe, in_place=True)\n", "             self.set_probe(probe, in_place=True)\n", " \n", "====================================================================================================\n", "                  ID: 1005239522\n", "         INSTRUCTION: use early return\n", "ORIGINAL INSTRUCTION: we can do an early return here instead of indenting the full block.\n", "          REVIEW URL: https://github.com/JanDeDobbeleer/oh-my-posh/pull/2709#discussion_r1005239522\n", "\n", "--- src/segments/gitversion.go\n", "+++ src/segments/gitversion.go\n", "@@ -38,7 +38,9 @@\n", " \t}\n", " \tdir := n.env.Pwd()\n", " \tresult, err := getCacheValue(n, dir)\n", "-\tif result.FullSemVer == \"\" {\n", "+\tif len(result.FullSemVer) != 0 {\n", "+\t    return err == nil\n", "+\t}\n", " \t\tresponse, err := n.env.RunCommand(gitversion, \"-output json\")\n", " \t\tif err != nil {\n", " \t\t\treturn false\n", "====================================================================================================\n"]}, {"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb Cell 22\u001b[0m line \u001b[0;36m6\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X42sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m random\u001b[39m.\u001b[39mseed(\u001b[39m31415\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X42sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39mfor\u001b[39;00m index \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39m50\u001b[39m):\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X42sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m     suggestion \u001b[39m=\u001b[39m llmfixed_filtered_suggested_edit[index]\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X42sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m     \u001b[39mprint\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m                  ID:\u001b[39m\u001b[39m'\u001b[39m, suggestion[\u001b[39m'\u001b[39m\u001b[39mid\u001b[39m\u001b[39m'\u001b[39m])\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X42sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m     \u001b[39mprint\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m         INSTRUCTION:\u001b[39m\u001b[39m'\u001b[39m, suggestion[\u001b[39m'\u001b[39m\u001b[39minstruction\u001b[39m\u001b[39m'\u001b[39m])\n", "\u001b[0;31mIndexError\u001b[0m: list index out of range"]}], "source": ["import random\n", "\n", "random.seed(31415)\n", "\n", "for index in range(50):\n", "    suggestion = llmfixed_filtered_suggested_edit[index]\n", "    print('                  ID:', suggestion['id'])\n", "    print('         INSTRUCTION:', suggestion['instruction'])\n", "    print('ORIGINAL INSTRUCTION:', suggestion['original_instruction'])\n", "    print('          REVIEW URL:', suggestion['html_url'])\n", "    print()\n", "    print(get_diff(suggestion))\n", "    print('=' * 100)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/data/pr_suggested_edits.fixllm.v2.jsonl'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb Cell 27\u001b[0m line \u001b[0;36m4\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X35sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mjson\u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X35sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m data \u001b[39m=\u001b[39m []\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X35sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m \u001b[39mwith\u001b[39;00m \u001b[39mopen\u001b[39;49m(\u001b[39m'\u001b[39;49m\u001b[39m/home/<USER>/data/pr_suggested_edits.fixllm.v2.jsonl\u001b[39;49m\u001b[39m'\u001b[39;49m) \u001b[39mas\u001b[39;00m f:\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X35sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m     \u001b[39mfor\u001b[39;00m line \u001b[39min\u001b[39;00m f:\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byury-h100/home/<USER>/src/augment/experimental/yury/edit/prepare_pr_suggested_edits_v2.ipynb#X35sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m         data\u001b[39m.\u001b[39mappend(json\u001b[39m.\u001b[39mloads(line[:\u001b[39m-\u001b[39m\u001b[39m1\u001b[39m]))\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/IPython/core/interactiveshell.py:286\u001b[0m, in \u001b[0;36m_modified_open\u001b[0;34m(file, *args, **kwargs)\u001b[0m\n\u001b[1;32m    279\u001b[0m \u001b[39mif\u001b[39;00m file \u001b[39min\u001b[39;00m {\u001b[39m0\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m2\u001b[39m}:\n\u001b[1;32m    280\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m    281\u001b[0m         \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mIPython won\u001b[39m\u001b[39m'\u001b[39m\u001b[39mt let you open fd=\u001b[39m\u001b[39m{\u001b[39;00mfile\u001b[39m}\u001b[39;00m\u001b[39m by default \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    282\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    283\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39my<PERSON> can use builtins\u001b[39m\u001b[39m'\u001b[39m\u001b[39m open.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    284\u001b[0m     )\n\u001b[0;32m--> 286\u001b[0m \u001b[39mreturn\u001b[39;00m io_open(file, \u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/home/<USER>/data/pr_suggested_edits.fixllm.v2.jsonl'"]}], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 98581 samples\n"]}], "source": ["import json\n", "\n", "data = []\n", "with open('/home/<USER>/data/pr_suggested_edits.fixllm.v2.jsonl') as f:\n", "    for line in f:\n", "        data.append(json.loads(line[:-1]))\n", "print('Loaded %d samples' % len(data))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["86239\n"]}], "source": ["n_total = 0\n", "with open('/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.v2.jsonl', 'w') as f:\n", "    for datum in data:\n", "        if datum['revision_verdict'] == 'GOOD':\n", "            smaller_datum = {k: v for k, v in datum.items()}\n", "            del smaller_datum['full_comment_info']\n", "            del smaller_datum['graphql_comment_info']\n", "            del smaller_datum['original_commit']\n", "            f.write(json.dumps(smaller_datum) + \"\\n\")\n", "            n_total += 1\n", "\n", "print(n_total)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
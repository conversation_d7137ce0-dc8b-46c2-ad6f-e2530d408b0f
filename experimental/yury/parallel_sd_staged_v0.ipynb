{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["([((4, 2), 2), ((2, 3), 1)], 0)\n", "([(5,), (4, 2), (2, 3)], 0)\n", "([(5,), (4,), (2,)], 0)\n"]}], "source": ["import warnings\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(100000))\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])\n", "\n", "\n", "\n", "class LongestOverlapLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd, allow_predicting_less_than_k_tokens=False, n_predictions_per_overlap=None):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "        self.allow_predicting_less_than_k_tokens = allow_predicting_less_than_k_tokens\n", "        self.n_predictions_per_overlap = n_predictions_per_overlap\n", "        if self.n_predictions_per_overlap:\n", "            assert self.allow_predicting_less_than_k_tokens\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, return_overlap=False):        \n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        n_sd = min(n_sd, len(self.tokens) - 1)\n", "        \n", "        if n_sd == 0:\n", "            return (), 0\n", "        \n", "        assert self.n_parallel_sd > 0\n", "        if len(self.tokens) < n_sd:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return (), 0\n", "        \n", "        if self.allow_predicting_less_than_k_tokens:\n", "            searchable_tokens = self.tokens[:-1]\n", "        else:\n", "            searchable_tokens = self.tokens[:-n_sd]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return (), 0\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue           \n", "                if self.n_predictions_per_overlap is not None:     \n", "                    assert l > 0\n", "                    if l < len(self.n_predictions_per_overlap):\n", "                        current_n_sd = self.n_predictions_per_overlap[l]\n", "                    else:\n", "                        current_n_sd = self.n_predictions_per_overlap[0]\n", "                    current_n_sd = min(current_n_sd, n_sd)\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + current_n_sd + 1, len(self.tokens))])\n", "                else:\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + n_sd + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if not self.allow_predicting_less_than_k_tokens:\n", "                    assert len(current_prediction) == n_sd\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                if return_overlap:\n", "                    predictions.append((current_prediction, l))\n", "                else:\n", "                    predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= self.n_parallel_sd:\n", "                    break\n", "            if len(predictions) >= self.n_parallel_sd:\n", "                break                \n", "        return predictions, 0\n", "\n", "\n", "predictor = LongestOverlapLM(2, 3)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2, True))\n", "\n", "predictor = LongestOverlapLM(2, 3, True)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))\n", "\n", "predictor = LongestOverlapLM(2, 3, True, n_predictions_per_overlap=[1])\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BUDGET (2,) SELECTED ({()}, 1.0)\n", "BUDGET (2,) SELECTED ({(1,)}, 1.3999999980577529)\n", "BUDGET (2,) SELECTED ({(1,), (2,)}, 1.7899999970732914)\n", "BUDGET (2,) SELECTED ({(2, 5), (1,)}, 2.1409999953239938)\n", "BUDGET (2,) SELECTED ({(1, 3), (2, 5)}, 2.341039993430035)\n", "BUDGET (2,) SELECTED ({(1, 3), (1, 4), (2, 5)}, 2.5409999909719962)\n", "BUDGET (2,) SELECTED ({(1, 4), (1, 3, 7), (2, 5)}, 2.739039590243699)\n", "BUDGET (2,) SELECTED ({(1, 4), (1, 3, 7), (2, 5), (2, 6)}, 2.7780395922530894)\n"]}], "source": ["import numpy as np\n", "from typing import List, Optional, Union\n", "import heapq\n", "import scipy.special\n", "\n", "\n", "class TrieNode:\n", "\n", "    def __init__(self, token_id: Optional[int], logprob: Optional[float], parent):\n", "        self.token_id = token_id\n", "        self.logprob = logprob\n", "        self.parent = parent\n", "        self.children = dict()\n", "        if self.parent is not None:\n", "            self.all_tokens = self.parent.all_tokens + (self.token_id,)\n", "            self.all_logprobs = self.parent.all_logprobs + (self.logprob,)\n", "            self.sumlogprob = np.sum(self.all_logprobs)\n", "            self.meanprob = np.mean(np.exp(self.all_logprobs))\n", "        else:\n", "            self.all_tokens = tuple()\n", "            self.all_logprobs = tuple()\n", "            self.sumlogprob = 0\n", "            self.meanprob = 1.0\n", "    \n", "    def __repr__(self):\n", "        return str(self.all_tokens)\n", "    \n", "    def is_leaf(self):\n", "        return len(self.children) == 0\n", "    \n", "    def estimate_next_token_will_be_correct(self):\n", "        # return self.meanprob * np.exp(self.sumlogprob)\n", "        return np.exp(self.logprob or 0.0) * np.exp(self.sumlogprob)\n", "    \n", "    def maybe_add_child(self, token_id: int, logprob: float):\n", "        if token_id in self.children:\n", "            if np.abs(self.children[token_id].logprob - logprob) > 1e-0:\n", "                print('MISMATCH', np.abs(self.children[token_id].logprob - logprob), 'when adding', token_id, 'to', self)\n", "                raise ValueError()\n", "        else:\n", "            new_node = TrieNode(token_id, logprob, parent=self)\n", "            self.children[token_id] = new_node\n", "        return self.children[token_id]\n", "    \n", "    def get_total_nodes(self):\n", "        return sum(child.get_total_nodes() for child in self.children.values()) + 1\n", "\n", "# root0 \n", "# |     \\\n", "# A1     B2\n", "# |  \\    |  \\\n", "# C3  D4  E5  F6\n", "# |\n", "# G7\n", "\n", "root = TrieNode(None, None, None)\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "b = root.maybe_add_child(2, np.log(0.39))\n", "c = a.maybe_add_child(3, np.log(0.5001))\n", "d = a.maybe_add_child(4, np.log(0.4999))\n", "e = b.maybe_add_child(5, np.log(0.9))\n", "f = b.maybe_add_child(6, np.log(0.1))\n", "g = c.maybe_add_child(7, np.log(0.99))\n", "\n", "\n", "def get_nodes_under_budget(root, n_sd: int, tokens_budget: int, eos_token_id: int):\n", "    assert tokens_budget > 0\n", "    expected_number_of_correct_tokens = 0\n", "    tokens_selected = 0    \n", "    leafs_selected = set()\n", "    Q = [(0, root)]\n", "\n", "    while len(Q) > 0 and tokens_selected < tokens_budget:\n", "        minus_cumlogprob, node = heapq.heappop(Q)\n", "\n", "        tokens_selected += 1\n", "        expected_number_of_correct_tokens += np.exp(-minus_cumlogprob)\n", "\n", "        # Remove parent\n", "        if node.parent in leafs_selected:\n", "            leafs_selected.remove(node.parent)        \n", "        leafs_selected.add(node)\n", "\n", "        # Expand children only if this is not the end of the generation.\n", "        if node.token_id != eos_token_id:            \n", "            for child in node.children.values():\n", "                if len(child.all_tokens) <= n_sd:\n", "                    heapq.heappush(Q, (-child.sumlogprob + np.random.random() * 1e-8, child))\n", "    return leafs_selected, expected_number_of_correct_tokens\n", "\n", "# BUDGET 1 SELECTED ({()}, 0)\n", "# BUDGET 2 SELECTED ({(1,)}, 0.4)\n", "# BUDGET 3 SELECTED ({(1,), (2,)}, 0.79)\n", "# BUDGET 4 SELECTED ({(1,), (2, 5)}, 1.141)\n", "# BUDGET 5 SELECTED ({(2, 5), (1, 3)}, 1.34104)\n", "# BUDGET 6 SELECTED ({(2, 5), (1, 3), (1, 4)}, 1.541)\n", "# BUDGET 7 SELECTED ({(1, 3, 7), (2, 5), (1, 4)}, 1.7390396)\n", "# BUDGET 8 SELECTED ({(1, 3, 7), (2, 6), (2, 5), (1, 4)}, 1.7780395999999998)\n", "for budget in [1, 2, 3, 4, 5, 6, 7, 8]:\n", "    print('BUDGET', b, 'SELECTED', get_nodes_under_budget(root, 3, budget, 0))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.09996000400000002\n", "0.19605920400000001\n", "0.3159\n", "LIST OF PREDICTIONS\n", "(2, 5)\n", "(1, 3, 7)\n", "(1, 4)\n", "LIST OF PREDICTIONS EXPANDED\n", "(2, 5, 6, 7)\n", "(1, 3, 7, 9)\n", "(1, 4)\n"]}], "source": ["nodes, estimate_next_token_will_be_correct = get_nodes_under_budget(root, 3, 7, 0)\n", "\n", "# compute current estimate on the number of correct tokens\n", "for node in nodes:\n", "    print(node.estimate_next_token_will_be_correct())\n", "\n", "\n", "def nodes_to_candidates(nodes):\n", "    candidates = sorted([(-node.estimate_next_token_will_be_correct(), node.all_tokens) for node in nodes])\n", "    return [x[1] for x in candidates]\n", "\n", "\n", "# generate list of predictions and expand each with LongestOverlapLM\n", "candidates = nodes_to_candidates(nodes)\n", "print('LIST OF PREDICTIONS')\n", "for candidate in candidates:\n", "    print(candidate)\n", "\n", "\n", "def expand_with_draft_model_predictions(lm, context, candidates, n_sd, total_budget):\n", "    expanded_candidates = []\n", "    current_budget = total_budget - sum(len(c) for c in candidates)\n", "    for candidate in candidates:\n", "        if current_budget > 0:\n", "            extra_predictions, _ = lm.predict_next_k_tokens(context + list(candidate), min(current_budget, n_sd - len(candidate)))\n", "            if len(extra_predictions) == 1:\n", "                extra_predictions = extra_predictions[0]\n", "            else:\n", "                assert len(extra_predictions) == 0            \n", "            assert current_budget >= len(extra_predictions)\n", "            current_budget -= len(extra_predictions)\n", "            expanded_candidates.append(candidate + extra_predictions)\n", "        else:\n", "            expanded_candidates.append(candidate)\n", "    return expanded_candidates\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "lm.fit([1, 2, 5, 6, 7, 9])\n", "candidates = expand_with_draft_model_predictions(lm, [5, 6], candidates, 4, 1000)\n", "print('LIST OF PREDICTIONS EXPANDED')\n", "for candidate in candidates:\n", "    print(candidate)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> initializing torch distributed ...\n", "[2023-10-02 19:12:07,749] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-10-02 19:12:07,842] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=216.153.49.136, master_port=6000\n", "[2023-10-02 19:12:07,843] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-10-02 19:12:07,848] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev:00867] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-02 19:12:08,140] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-10-02 19:12:08,401] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-02 19:12:08,401] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-02 19:12:08,583] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-02 19:12:08,585] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-02 19:12:08,586] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-02 19:12:08,587] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-02 19:12:08,588] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-10-02 19:12:08,589] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-02 19:12:08,590] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-02 19:12:08,591] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-02 19:12:08,591] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-02 19:12:08,592] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-02 19:12:08,593] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-02 19:12:08,594] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-02 19:12:08,595] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-02 19:12:08,595] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-02 19:12:08,596] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-02 19:12:08,596] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-02 19:12:08,597] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-02 19:12:08,597] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-02 19:12:08,597] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-02 19:12:08,597] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-02 19:12:08,598] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-02 19:12:08,598] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-02 19:12:08,600] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-02 19:12:08,600] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-02 19:12:08,601] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-02 19:12:08,601] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-02 19:12:08,602] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-02 19:12:08,603] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-02 19:12:08,603] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-02 19:12:08,603] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-10-02 19:12:08,603] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-02 19:12:08,604] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-02 19:12:08,604] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-02 19:12:08,604] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-02 19:12:08,605] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-02 19:12:08,605] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-02 19:12:08,605] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-02 19:12:08,605] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-02 19:12:08,606] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-02 19:12:08,606] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-02 19:12:08,606] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-02 19:12:08,607] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-02 19:12:08,607] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-02 19:12:08,607] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-02 19:12:08,608] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-02 19:12:08,608] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-02 19:12:08,609] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Creating extension directory /home/<USER>/.cache/torch_extensions/py39_cu118/utils...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[1/2] c++ -<PERSON><PERSON> -<PERSON><PERSON> flatten_unflatten.o.d -DTORCH_EXTENSION_NAME=utils -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -isystem /opt/conda/lib/python3.9/site-packages/torch/include -isystem /opt/conda/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -isystem /opt/conda/lib/python3.9/site-packages/torch/include/TH -isystem /opt/conda/lib/python3.9/site-packages/torch/include/THC -isystem /opt/conda/include/python3.9 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++17 -c /usr/local/local_user_base/lib/python3.9/site-packages/deepspeed/ops/csrc/utils/flatten_unflatten.cpp -o flatten_unflatten.o \n", "[2/2] c++ flatten_unflatten.o -shared -L/opt/conda/lib/python3.9/site-packages/torch/lib -lc10 -ltorch_cpu -ltorch -ltorch_python -o utils.so\n", "Time to load utils op: 12.283860445022583 seconds\n", "[2023-10-02 19:12:21,714] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-10-02 19:12:21,740] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-10-02 19:12:21,795] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-02 19:12:22,994] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-10-02 19:12:23,403] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-10-02 19:12:23,810] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-10-02 19:12:24,283] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-10-02 19:12:24,723] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-10-02 19:12:25,200] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-10-02 19:12:25,589] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-10-02 19:12:25,970] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-10-02 19:12:26,359] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-10-02 19:12:26,753] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-10-02 19:12:27,122] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-10-02 19:12:27,502] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-10-02 19:12:27,951] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-10-02 19:12:28,267] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-10-02 19:12:28,495] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-10-02 19:12:28,988] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-10-02 19:12:29,388] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-10-02 19:12:29,877] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-10-02 19:12:30,571] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-10-02 19:12:30,927] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-10-02 19:12:31,412] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-10-02 19:12:31,803] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-10-02 19:12:32,111] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-10-02 19:12:32,427] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-10-02 19:12:32,792] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-10-02 19:12:32,796] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-10-02 19:12:33,470] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["# Based on this https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e\n", "model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7fc8534c2520>\n"]}], "source": ["# allow '<|endoftext|>'\n", "ALL_SPECIAL_TOKENS_IDS = [model.tokenizer.tokenize(token)[0] for token in model.tokenizer.all_special_tokens() if token != '<|endoftext|>']\n", "ALL_SPECIAL_TOKENS_IDS_SET = set(ALL_SPECIAL_TOKENS_IDS)\n", "\n", "\n", "EOS_TOKEN_ID = model.tokenizer.tokenize('<|endoftext|>')[0]\n", "EOS_TOKEN_ID\n", "\n", "print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 100 samples (had to process 100 samples)\n"]}], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "import json\n", "from research.models.meta_model import GenerationOptions\n", "from typing import Any, List, Dict\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def load_retrieval_data(paths, n_samples, max_tokens_to_predict, n_retrievals, remove_prefix_and_suffix=False):\n", "    n_read_samples, data = 0, []\n", "    for path in paths:\n", "        df = pd.read_parquet(path, engine='pyarrow')\n", "        for _, datum in df.iterrows():\n", "            n_read_samples += 1\n", "\n", "            context = ModelInput(\n", "                prefix=datum['prefix'],\n", "                suffix=datum['suffix'],\n", "                middle='',\n", "                retrieved_chunks=deserialize_retrieved_chunks(datum['retrieved_chunks']),\n", "                path=datum['file_path'],\n", "                target=None,\n", "            )\n", "            context, _ = model.prompt_formatter.prepare_prompt(context)\n", "\n", "            label = model.prompt_formatter.tokenizer.tokenize(datum['middle'] + \"<|endoftext|>\")    \n", "            label = label[:max_tokens_to_predict]\n", "            for token in label:\n", "                assert token not in ALL_SPECIAL_TOKENS_IDS_SET, (token, model.tokenizer.detokenize([token]))\n", "            label = np.array(label)            \n", "\n", "            data.append({\n", "                'context': context,\n", "                'label': label,\n", "                'pretokenized_file': datum['prefix'] + datum['middle'] + datum['suffix'],\n", "                'pretokenized_suffix': datum['suffix'],\n", "                'pretokenized_prefix': datum['prefix'],\n", "                'pretokenized_middle': datum['middle'],\n", "            })\n", "            if len(data) >= n_samples:\n", "                break\n", "        if len(data) >= n_samples:\n", "            break\n", "    print('Loaded %d samples (had to process %d samples)' % (len(data), n_read_samples))\n", "    return data\n", "\n", "import glob\n", "MICHIEL_BM25_RETRIEVAL_DATA_PATHS = sorted(glob.glob(\"/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/part-?????-0153bb65-91c2-4afb-9526-4bec0beb6656-c000.zstd.parquet\"))\n", "\n", "data_fim_retrieval = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 100, 32, None)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 12.0\n", "PREDICITONS\n", "=> = self.get_mention_prefix(mention)<|endoftext|>\n", "=> =self.get_abstractions(query, signs, 5\n", "=> = self.get_mention_prefix( mention\n", "=> = self.get_ mention\n", "=> = \"\n", "=> = self.get_mention_prefix(mention)\n", "           \n"]}], "source": ["import gc\n", "import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "\n", "\n", "class NeuralLM:\n", "    \n", "    def __init__(self, tokens_budget, main_model_cost=9.5, model_cost=3.0, draft_model=None, draft_tokens_budget=None, n_rounds=None):\n", "        self.tokens_budget = tokens_budget\n", "        self.main_model_cost = main_model_cost\n", "        self.model_cost = model_cost\n", "        self.draft_model = draft_model        \n", "        if self.draft_model is not None:\n", "            assert draft_tokens_budget is not None\n", "            self.draft_tokens_budget = draft_tokens_budget\n", "        self.n_rounds = n_rounds\n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def _get_final_predictions(self, context, n_sd, root):\n", "        nodes, _ = get_nodes_under_budget(root, n_sd, self.tokens_budget, EOS_TOKEN_ID)\n", "        candidates = nodes_to_candidates(nodes)        \n", "        if self.draft_model is not None:            \n", "            candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.tokens_budget + self.draft_tokens_budget)\n", "        return candidates\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        root = TrieNode(None, None, None)\n", "        \n", "        n_rounds, cost = 0, 0\n", "\n", "        while n_rounds < self.n_rounds:\n", "            n_rounds += 1            \n", "            nodes, expected_number_of_correct_tokens = get_nodes_under_budget(root, n_sd - 1, self.tokens_budget, EOS_TOKEN_ID)\n", "            nodes = [node for node in nodes if node.is_leaf()]\n", "            if len(nodes) == 0:\n", "                # print('stop #1')\n", "                break\n", "            next_token_estimated_prob = sum(node.estimate_next_token_will_be_correct() for node in nodes)\n", "\n", "            # More numerically precise version of the stopping condition:\n", "            # current_expected_latency_per_token = (cost + self.main_model_cost) / expected_number_of_correct_tokens    \n", "            # print('current_expected_latency_per_token', current_expected_latency_per_token)\n", "            # next_expected_latency_per_token = (cost + self.model_cost + self.main_model_cost) / (expected_number_of_correct_tokens + next_token_estimated_prob)\n", "            # print('next_expected_latency_per_token', next_expected_latency_per_token)\n", "            should_stop = (cost + self.model_cost + self.main_model_cost) * expected_number_of_correct_tokens > (cost + self.main_model_cost) * (expected_number_of_correct_tokens + next_token_estimated_prob)\n", "            not_first_round = cost > 0\n", "            if not_first_round and should_stop:\n", "                # print('stop #2')\n", "                break\n", "\n", "            candidates = nodes_to_candidates(nodes)\n", "            if self.draft_model is not None:\n", "                candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.tokens_budget + self.draft_tokens_budget)\n", "\n", "            # print('CANDIDATES', n_rounds)\n", "            # for candidate in candidates:\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "            candidates = [list(context + list(x)) for x in candidates]            \n", "            cost += self.model_cost        \n", "            for candidate in candidates:\n", "                with torch.no_grad():\n", "                    context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([candidate], dtype=torch.int64))                \n", "                    model_inputs = (\n", "                        context_tokens_tensor,  # input_ids\n", "                        position_ids,  # position_ids\n", "                        attention_mask,  # attention_mask\n", "                    )            \n", "                    \n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "                    logits = text_gen_utils.forward_model(\n", "                        model=model.neox_model,\n", "                        model_inputs=model_inputs,\n", "                        is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                        pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                        timers=None,\n", "                    )\n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "                    logits[:, :, ALL_SPECIAL_TOKENS_IDS] = -10000                                    \n", "                    assert logits.shape[0] == 1, logits.shape\n", "                    logits = logits[0]\n", "                    logprobs = F.log_softmax(logits, dim=-1)\n", "                    top_predictions = torch.topk(logprobs, self.tokens_budget, dim=-1, sorted=True)                \n", "\n", "                logprobs = logprobs.float().cpu().numpy()\n", "                top_prediction_values = top_predictions.values.float().cpu().numpy()\n", "                top_prediction_indices = top_predictions.indices.cpu().numpy()\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "                for index in range(len(context) - 1, len(candidate)): \n", "                    if index == len(context) - 1:\n", "                        node = root\n", "                    else:\n", "                        token_id = candidate[index]\n", "                        node = node.maybe_add_child(token_id, logprobs[index - 1, token_id])\n", "                    # add new token from the input\n", "                    for token_id, logprob in zip(top_prediction_indices[index], top_prediction_values[index]):\n", "                        # print('adding #2', token_id, 'to', node, 'with logprob', logprob)\n", "                        _ = node.maybe_add_child(token_id.item(), logprob)\n", "                \n", "            # print('ROUND', n_rounds)\n", "            # for candidate in self._get_final_predictions(context, n_sd, root):\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "        candidates = self._get_final_predictions(context, n_sd, root)\n", "        for candidate in candidates:\n", "            assert len(candidate) <= n_sd\n", "        return candidates, cost\n", "\n", "\n", "# lm = NeuralLM(tokens_budget=16, n_rounds=10)\n", "# predictions, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 10)\n", "# print('COST', cost)\n", "# print('PREDICITONS')\n", "# for p in predictions:\n", "#     print('=>', tokenizer.detok<PERSON>ze(p))\n", "\n", "lm = NeuralLM(tokens_budget=20, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "lm.fit(data_fim_retrieval[0]['context'])\n", "predictions, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITONS')\n", "for p in predictions:\n", "    print('=>', tokenizer.detok<PERSON>ze(p))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# from https://docs.google.com/presentation/d/1V1XQ0IxEEPSekTvWW10KPR3Bg5BDRultJQGotCQd3mg/edit#slide=id.g237b45739c5_0_21\n", "LATENCY_A100 = {\n", "    1: 24.2,\n", "    2: 24.8,\n", "    4: 24.9,\n", "    8: 24.7,\n", "    16: 25,\n", "    20: 26.1,\n", "    24: 25.9,\n", "    28: 26,\n", "    32: 26.1,\n", "    64: 27.6,\n", "    96: 29,\n", "    128: 30.1,\n", "    160: 40.5,\n", "    192: 42,\n", "    224: 43,\n", "    256: 44,\n", "    384: 62,\n", "    512: 81.6,\n", "}\n", "\n", "# from https://docs.google.com/spreadsheets/d/1VGjlBbbKFmBEO4hAPxss1vAR_DxUC4YDkGOwm4fdhcw/edit#gid=1581007424\n", "LATENCY_H100 = {\n", "    1: 14.66,\n", "    16: 14.66,\n", "    128: 15.08,\n", "    256: 18.7,\n", "    512: 31.53,\n", "    1024: 59.66,\n", "}\n", "\n", "LATENCY_A100_KEYS = np.array(sorted(list(LATENCY_A100.keys())))\n", "LATENCY_H100_KEYS = np.array(sorted(list(LATENCY_H100.keys())))\n", "\n", "def estimate_latency_a100(n):\n", "    assert n > 0 and n <= 512\n", "    if n in LATENCY_A100:\n", "        return LATENCY_A100[n]\n", "    n_lower_index = np.searchsorted(LATENCY_A100_KEYS, n)\n", "    n_lower = LATENCY_A100_KEYS[n_lower_index - 1]\n", "    n_upper = LATENCY_A100_KEYS[n_lower_index]\n", "    return LATENCY_A100[n_lower] * (n_upper - n) / (n_upper - n_lower) + LATENCY_A100[n_upper] * (n - n_lower) / (n_upper - n_lower)\n", "\n", "\n", "def estimate_latency_h100(n):\n", "    return LATENCY_H100[LATENCY_H100_KEYS[np.searchsorted(LATENCY_H100_KEYS, 6, side='left')]]\n", "\n", "# LATENCY = {n: estimate_latency_a100(n) for n in range(1, 512)}\n", "# LATENCY = {n: estimate_latency_h100(n) for n in range(1, 512)}\n", "# Latency for H100 with FP8\n", "LATENCY = {n: 9.5 for n in range(1, 256)}"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13\n", "6\n"]}], "source": ["def compute_predictions_batch_cost(predictions):\n", "    return sum(len(prediction) + 1 for prediction in predictions)\n", "\n", "\n", "def compute_predictions_tree_cost(predictions):\n", "    root = TrieNode(None, None, None)\n", "    for prediction in predictions:\n", "        node = root\n", "        for token in prediction:\n", "            node = node.maybe_add_child(token, 0)\n", "    return root.get_total_nodes()\n", "\n", "print(compute_predictions_batch_cost([[1, 2, 3], [1, 2, 3, 4], [1, 2, 4]]))\n", "print(compute_predictions_tree_cost([[1, 2, 3], [1, 2, 3, 4], [1, 2, 4]]))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'context_length': 3726, 'label_length': 12, 'n_rounds': 1, 'latency': 21.5, 'prediction_latency': 18388.017177581787, 'beam_size': 5.0, 'n_unique_first_token': 1.0, 'n_unique_two_tokens': 3.0}\n"]}], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample, use_tree_cost):\n", "    context = sample['context']\n", "    if not isinstance(context, list):\n", "        context = context.tolist()\n", "    label = sample['label'].tolist()\n", "\n", "    n_rounds, latency = 0, 0\n", "    index = 0\n", "    beam_sizes, prediction_latency, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 1\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            beam_of_predictions, draft_cost = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict)\n", "            latency += draft_cost            \n", "\n", "            prediction_latency.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            index = furthest_index\n", "\n", "            if use_tree_cost:\n", "                actual_tokens_predicted = compute_predictions_tree_cost(beam_of_predictions)\n", "            else:\n", "                actual_tokens_predicted = compute_predictions_batch_cost(beam_of_predictions)\n", "\n", "        # Make prediction with the main model\n", "        index += 1\n", "        latency += LATENCY[actual_tokens_predicted]\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'latency': latency,\n", "        'prediction_latency': safe_mean(prediction_latency),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "\n", "# lm = NeuralLM(16, n_rounds=3)\n", "# print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0]))\n", "\n", "# lm = NeuralLM(16, draft_model=LongestOverlapLM(12, 1, True), n_rounds=10)\n", "# print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0], True))\n", "\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[0], True))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data, use_tree_cost):\n", "    pbar = tqdm(data, total=len(data))\n", "    df = None\n", "    for d in pbar:\n", "        datum = measure_latency_per_sample_parallel_sd(lm=lm, sample=d, use_tree_cost=use_tree_cost)\n", "        datum['latency_per_token'] = datum['latency'] / datum['label_length']        \n", "        if df is None:\n", "            df = pd.DataFrame([datum])\n", "        else:\n", "            df = pd.concat([df, pd.DataFrame([datum])], ignore_index=True)\n", "        pbar.set_description('Latency per token: ' + ' '.join(['%s: %.2f' % (k, v) for k, v in df['latency_per_token'].aggregate([q50, q80, q95, q99]).to_dict().items()]))\n", "    return df"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import os \n", "\n", "BASE_DIR = '/mnt/efs/augment/user/yury/parallel_sd_staged_32label'\n", "\n", "\n", "def get_experiment_path(exp_name, mode):\n", "    path = os.path.join(BASE_DIR, exp_name)\n", "    if mode == 'w':\n", "        assert not os.path.isfile(path)\n", "    elif mode == 'r':\n", "        assert os.path.isfile(path), path\n", "    else:\n", "        raise ValueError\n", "    return path"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b6abfef20464eacb96155b6b0ce061a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3687.360000\n", "label_length              22.360000\n", "n_rounds                   4.240000\n", "latency                   61.340000\n", "prediction_latency      7288.552952\n", "beam_size                  8.653603\n", "n_unique_first_token       5.554145\n", "n_unique_two_tokens        6.904118\n", "latency_per_token          3.183930\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget16_estimate_via_last.csv', 'w')\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2af0d9703b624b40a09275dbc770faf8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3687.360000\n", "label_length              22.360000\n", "n_rounds                   4.480000\n", "latency                   65.300000\n", "prediction_latency      3518.727238\n", "beam_size                  3.612603\n", "n_unique_first_token       2.872176\n", "n_unique_two_tokens        3.295431\n", "latency_per_token          3.324013\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget8.csv', 'w')\n", "lm = NeuralLM(tokens_budget=8, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef9f5d03687d46c2b28a6a189f274435", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3687.360000\n", "label_length              22.360000\n", "n_rounds                   4.180000\n", "latency                   61.190000\n", "prediction_latency      7546.563587\n", "beam_size                  8.729518\n", "n_unique_first_token       5.603841\n", "n_unique_two_tokens        6.866070\n", "latency_per_token          3.172229\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget16.csv', 'w')\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Unnamed: 0                49.500000\n", "context_length          3687.360000\n", "label_length              22.360000\n", "n_rounds                   4.180000\n", "latency                   61.190000\n", "prediction_latency      7546.563587\n", "beam_size                  8.729518\n", "n_unique_first_token       5.603841\n", "n_unique_two_tokens        6.866070\n", "latency_per_token          3.172229\n", "dtype: float64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget16.csv', 'r')\n", "df_parallel_staged_neural_optimal_budget16 = pd.read_csv(path)\n", "df_parallel_staged_neural_optimal_budget16.mean()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb Cell 17\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m path \u001b[39m=\u001b[39m get_experiment_path(\u001b[39m'\u001b[39;49m\u001b[39mdf_parallel_staged_neural_1b_100_round10_budget32.csv\u001b[39;49m\u001b[39m'\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mw\u001b[39;49m\u001b[39m'\u001b[39;49m)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m lm \u001b[39m=\u001b[39m NeuralLM(tokens_budget\u001b[39m=\u001b[39m\u001b[39m32\u001b[39m, draft_model\u001b[39m=\u001b[39mLongestOverlapLM(\u001b[39m12\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39mTrue\u001b[39;00m), draft_tokens_budget\u001b[39m=\u001b[39m\u001b[39m36\u001b[39m, n_rounds\u001b[39m=\u001b[39m\u001b[39m10\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m df \u001b[39m=\u001b[39m measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:\u001b[39m100\u001b[39m], use_tree_cost\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb Cell 17\u001b[0m line \u001b[0;36m9\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m path \u001b[39m=\u001b[39m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39mjoin(BASE_DIR, exp_name)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m \u001b[39mif\u001b[39;00m mode \u001b[39m==\u001b[39m \u001b[39m'\u001b[39m\u001b[39mw\u001b[39m\u001b[39m'\u001b[39m:\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m     \u001b[39massert\u001b[39;00m \u001b[39mnot\u001b[39;00m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39misfile(path)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m \u001b[39melif\u001b[39;00m mode \u001b[39m==\u001b[39m \u001b[39m'\u001b[39m\u001b[39mr\u001b[39m\u001b[39m'\u001b[39m:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m     \u001b[39massert\u001b[39;00m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39misfile(path)\n", "\u001b[0;31mAssertionError\u001b[0m: "]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget32.csv', 'w')\n", "lm = NeuralLM(tokens_budget=32, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb Cell 18\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m path \u001b[39m=\u001b[39m get_experiment_path(\u001b[39m'\u001b[39;49m\u001b[39mdf_parallel_staged_neural_1b_100_round10_budget32_nrounds20.csv\u001b[39;49m\u001b[39m'\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mw\u001b[39;49m\u001b[39m'\u001b[39;49m)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m lm \u001b[39m=\u001b[39m NeuralLM(tokens_budget\u001b[39m=\u001b[39m\u001b[39m32\u001b[39m, draft_model\u001b[39m=\u001b[39mLongestOverlapLM(\u001b[39m12\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39mTrue\u001b[39;00m), draft_tokens_budget\u001b[39m=\u001b[39m\u001b[39m36\u001b[39m, n_rounds\u001b[39m=\u001b[39m\u001b[39m20\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m df \u001b[39m=\u001b[39m measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:\u001b[39m100\u001b[39m], use_tree_cost\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb Cell 18\u001b[0m line \u001b[0;36m9\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m path \u001b[39m=\u001b[39m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39mjoin(BASE_DIR, exp_name)\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m \u001b[39mif\u001b[39;00m mode \u001b[39m==\u001b[39m \u001b[39m'\u001b[39m\u001b[39mw\u001b[39m\u001b[39m'\u001b[39m:\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m     \u001b[39massert\u001b[39;00m \u001b[39mnot\u001b[39;00m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39misfile(path)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m \u001b[39melif\u001b[39;00m mode \u001b[39m==\u001b[39m \u001b[39m'\u001b[39m\u001b[39mr\u001b[39m\u001b[39m'\u001b[39m:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged.ipynb#X40sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m     \u001b[39massert\u001b[39;00m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39misfile(path), path\n", "\u001b[0;31mAssertionError\u001b[0m: "]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget32_nrounds20.csv', 'w')\n", "lm = NeuralLM(tokens_budget=32, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=20)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a79d82d963d40abb466ec04dcd38e29", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length           3687.360000\n", "label_length               22.360000\n", "n_rounds                    4.430000\n", "latency                    66.745000\n", "prediction_latency      32261.883889\n", "beam_size                  48.405072\n", "n_unique_first_token       21.324332\n", "n_unique_two_tokens        29.096256\n", "latency_per_token           3.402403\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget64.csv', 'w')\n", "lm = NeuralLM(tokens_budget=64, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b0b0150949f345b39d29ce03f88d4ee3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3685.000\n", "label_length              32.000\n", "n_rounds                  32.000\n", "latency                  304.000\n", "prediction_latency         0.006\n", "beam_size                  0.000\n", "n_unique_first_token       0.000\n", "n_unique_two_tokens        0.000\n", "latency_per_token          9.500\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "715960efeee0420482fb4c98f9683c63", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3685.000000\n", "label_length              32.000000\n", "n_rounds                   6.000000\n", "latency                   57.000000\n", "prediction_latency         0.528425\n", "beam_size                  0.739936\n", "n_unique_first_token       0.739936\n", "n_unique_two_tokens        0.739936\n", "latency_per_token          4.007812\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8567c4db51134eb99fb58df2341642df", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3685.000000\n", "label_length              32.000000\n", "n_rounds                   5.000000\n", "latency                   47.500000\n", "prediction_latency         0.518508\n", "beam_size                  3.150000\n", "n_unique_first_token       2.000000\n", "n_unique_two_tokens        2.354167\n", "latency_per_token          3.562500\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM(0, 1, True)\n", "df_baseline = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "print(df_baseline.median())\n", "\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "df_longestoverlap = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "print(df_longestoverlap.median())\n", "\n", "\n", "lm = LongestOverlapLM(12, 6, True)\n", "df_longestoverlap_parallel = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "print(df_longestoverlap_parallel.median())"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 14.0\n", "PREDICITON = self.get_mention_prefix(mention)<|endoftext|>\n"]}], "source": ["import copy\n", "import gc\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "\n", "\n", "class NeuralLMSingle:\n", "    \n", "    def __init__(self, round_cost=3.5, draft_model=None):\n", "        self.round_cost = round_cost\n", "        self.draft_model = draft_model        \n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def get_draft_model_predictions(self, context, n_sd):\n", "        if self.draft_model is not None:\n", "            draft_predictions, draft_predictions_cost = self.draft_model.predict_next_k_tokens(context, n_sd)\n", "            if len(draft_predictions) == 1:\n", "                draft_predictions = list(draft_predictions[0])\n", "            else:\n", "                # We don't support parallel SD yet                \n", "                assert len(draft_predictions) == 0\n", "        else:\n", "            draft_predictions = []\n", "            draft_predictions_cost = 0\n", "        return draft_predictions, draft_predictions_cost\n", "\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        predictions = []    \n", "        \n", "        n_rounds, cost = 0, 0\n", "        predictions_probs = []\n", "\n", "        while len(predictions) < n_sd:\n", "            n_rounds += 1\n", "\n", "            draft_predictions, draft_cost = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions) - 1)\n", "            cost += draft_cost\n", "\n", "            context_tokens = context + predictions + list(draft_predictions)\n", "            with torch.no_grad():\n", "                context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([context_tokens], dtype=torch.int64))\n", "                model_inputs = (\n", "                    context_tokens_tensor,  # input_ids\n", "                    position_ids,  # position_ids\n", "                    attention_mask,  # attention_mask\n", "                )            \n", "\n", "                model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "                eval_result = text_gen_utils.forward_model(\n", "                    model=model.neox_model,\n", "                    model_inputs=model_inputs,\n", "                    is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                    pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                    timers=None,\n", "                )                \n", "                model.neox_model.module.clear_cache()  # clear the k,v cache after\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "                cost += self.round_cost\n", "\n", "                # We only work with batch size 1 for now\n", "                assert eval_result.shape[0] == 1\n", "                eval_result = eval_result[0]\n", "\n", "                # cursed stuff\n", "                assert eval_result.shape[0] == len(context_tokens)\n", "\n", "                # Disallow special tokens\n", "                eval_result[:, ALL_SPECIAL_TOKENS_IDS] = -10000\n", "                logprobs = F.log_softmax(eval_result.float(), dim=-1)\n", "                            \n", "                for index in range(len(context + predictions) - 1, len(context_tokens)):\n", "                    token_id = logprobs[index].argmax().cpu()\n", "                    \n", "                    assert token_id not in ALL_SPECIAL_TOKENS_IDS_SET, (token_id, model.tokenizer.detokenize([token_id]))\n", "                    predictions.append(token_id)\n", "                    predictions_probs.append(torch.exp(logprobs[index, token_id]).cpu().item())\n", "\n", "                    if index < len(context_tokens) - 1 and token_id != context_tokens[index + 1]:\n", "                        break      \n", "\n", "                    if token_id == EOS_TOKEN_ID:\n", "                        break\n", "\n", "            # print('ROUND (AFTER PREDICTION)', n_rounds, '=>', tokenizer.detokenize(predictions), ' ||| ', predictions)\n", "\n", "            if (len(predictions) > 0 and predictions[-1] == EOS_TOKEN_ID):\n", "                break\n", "        \n", "            # We decide whether to stop at this point.                \n", "            expected_number_of_predicted_tokens = np.sum(np.cumprod(predictions_probs))            \n", "            next_token_estimated_prob = np.mean(predictions_probs) * np.prod(predictions_probs)\n", "\n", "            # More numerically precise version of the stopping condition:\n", "            # current_expected_latency_per_token = (cost + LATENCY[len(predictions) + 1]) / expected_number_of_predicted_tokens                    \n", "            # next_expected_latency_per_token = (cost + self.round_cost + LATENCY[len(predictions) + 2]) / (expected_number_of_predicted_tokens + next_token_estimated_prob)\n", "            # if next_expected_latency_per_token > current_expected_latency_per_token:\n", "            #     break\n", "            if (cost + self.round_cost + LATENCY[len(predictions) + 2]) * expected_number_of_predicted_tokens > (cost + LATENCY[len(predictions) + 1]) * (expected_number_of_predicted_tokens + next_token_estimated_prob):\n", "                break\n", "\n", "\n", "        # Do one final round of draft predictions since it's free\n", "        if len(predictions) < n_sd and not (len(predictions) > 0 and predictions[-1] == EOS_TOKEN_ID):            \n", "            draft_predictions, draft_cost = self.get_draft_model_predictions(context + predictions, n_sd - len(predictions))\n", "            cost += draft_cost\n", "            predictions.extend(draft_predictions)            \n", "\n", "        return [tuple(predictions)], cost\n", "\n", "\n", "# lm = NeuralLMSingle()\n", "# p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 20)\n", "# print('COST', cost)\n", "# print('PREDICITON', tokenizer.detokenize(p[0]))\n", "\n", "lm = NeuralLMSingle(draft_model=LongestOverlapLM(12, 1, True))\n", "lm.fit(data_fim_retrieval[0]['context'])\n", "p, cost = lm.predict_next_k_tokens(data_fim_retrieval[0]['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITON', tokenizer.detokenize(p[0]))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "16fca5bd14244bc090f2bc3a1e635aaa", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3687.360000\n", "label_length              22.360000\n", "n_rounds                   4.670000\n", "latency                   74.245000\n", "prediction_latency       499.014575\n", "beam_size                  1.000000\n", "n_unique_first_token       1.000000\n", "n_unique_two_tokens        1.000000\n", "latency_per_token          3.706209\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_single_staged_neural_1b_100.csv', 'w')\n", "lm = NeuralLMSingle(round_cost=3.0, draft_model=LongestOverlapLM(12, 1, True))\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "QUANTILES = [q50, q80, q90, q95, q99]\n", "QUANTILE_NAMES = []\n", "\n", "QUANTILES = {\n", "    # 'average': 'mean',\n", "    'q50': q50,\n", "    'q80': q80,\n", "    'q90': q90,\n", "    'q95': q95,\n", "    'q99': q99,\n", "}\n", "\n", "\n", "DFS = {\n", "    'no SD': df_baseline,\n", "    'Single LongestOverlapLM (in prod)': df_longestoverlap,\n", "    # 'Parallel LongestOverlapLM': df_longestoverlap_parallel,\n", "    'Single staged neural SD 1B': 'df_single_staged_neural_1b_100.csv',\n", "    # 'Parallel staged neural SD 1B (budget 8)': 'df_parallel_staged_neural_1b_100_round10_budget8.csv',\n", "    # 'Parallel staged neural SD 1B (budget 16)': 'df_parallel_staged_neural_1b_100_round10_budget16.csv',\n", "    'Parallel staged neural SD 1B (budget 16)': 'df_parallel_staged_neural_1b_100_round10_budget16_estimate_via_last.csv',    \n", "    # 'Parallel staged neural SD 1B (budget 32)': 'df_parallel_staged_neural_1b_100_round10_budget32.csv',\n", "    # 'Parallel staged neural SD 1B (budget 32, max rounds 20)': 'df_parallel_staged_neural_1b_100_round10_budget32_nrounds20.csv',    \n", "    # 'Parallel staged neural SD 1B (budget 64)': 'df_parallel_staged_neural_1b_100_round10_budget64.csv',\n", "}\n", "\n", "results = []\n", "for q_name, q_fn in QUANTILES.items():\n", "    features = {'latency_aggr': q_name}\n", "    for df_name, df in DFS.items():        \n", "        if isinstance(df, str):\n", "            # Need to load DF first\n", "            path = get_experiment_path(df, 'r')\n", "            df = pd.read_csv(path)\n", "        features[df_name] = df.aggregate({'latency_per_token': q_fn})['latency_per_token']\n", "    results.append(features)\n", "results = pd.DataFrame(results)\n", "\n", "ax = results.plot(\n", "    x='latency_aggr',\n", "    kind='bar',\n", "    stacked=False,\n", "    figsize=(12, 6),\n", "    rot=0,\n", "    yticks=np.arange(11),\n", "    edgecolor='white',\n", "    width=0.9,\n", "    linewidth=4)\n", "ax.legend(loc='lower right')\n", "ax.grid(axis='y')\n", "plt.title('Simulation of speculative decoding performance on 100 samples on H100 with FP8 (cost of main model is 9.5, draft model is 3.0). Generate up to 32 tokens.')\n", "for i in range(len(ax.containers)):\n", "    ax.bar_label(ax.containers[i], fmt='%.1f')\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 100 samples (had to process 100 samples)\n"]}], "source": ["data_fim_retrieval256 = load_retrieval_data(MICHIEL_BM25_RETRIEVAL_DATA_PATHS, 100, 256, None)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b415563589b4b7097419d085f70b978", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3685.000000\n", "label_length              32.000000\n", "n_rounds                  32.000000\n", "latency                  304.000000\n", "prediction_latency         0.006288\n", "beam_size                  0.000000\n", "n_unique_first_token       0.000000\n", "n_unique_two_tokens        0.000000\n", "latency_per_token          9.500000\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "83eb959178e14200b2105ac0a7341b18", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3685.000000\n", "label_length              32.000000\n", "n_rounds                   6.000000\n", "latency                   57.000000\n", "prediction_latency         0.536799\n", "beam_size                  0.739936\n", "n_unique_first_token       0.739936\n", "n_unique_two_tokens        0.739936\n", "latency_per_token          4.007812\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM(0, 1, True)\n", "df_baseline256 = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval256[:100], use_tree_cost=True)\n", "print(df_baseline.median())\n", "\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "df_longestoverlap256 = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval256[:100], use_tree_cost=True)\n", "print(df_longestoverlap.median())"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d652298fb8a04910bfd1d9e441f0cca6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["context_length          3687.360000\n", "label_length              70.400000\n", "n_rounds                  12.490000\n", "latency                  180.785000\n", "prediction_latency      7290.149200\n", "beam_size                  8.247100\n", "n_unique_first_token       5.225099\n", "n_unique_two_tokens        6.419171\n", "latency_per_token          2.929668\n", "dtype: float64\n"]}], "source": ["path = get_experiment_path('df_parallel_staged_neural_1b_100_round10_budget16_len256.csv', 'w')\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=10)\n", "df = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval256[:100], use_tree_cost=True)\n", "df.to_csv(path)\n", "print(df.mean())"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "QUANTILES = [q50, q80, q95, q99]\n", "QUANTILE_NAMES = []\n", "\n", "QUANTILES = {\n", "    # 'average': 'mean',\n", "    'q50': q50,\n", "    'q80': q80,\n", "    'q95': q95,\n", "    'q99': q99,\n", "}\n", "\n", "\n", "DFS = {\n", "    'no SD': df_baseline256,\n", "    'Single LongestOverlapLM (in prod)': df_longestoverlap256,\n", "    'Single staged neural SD 1B': 'df_single_staged_neural_1b_optimal_100_fp8_len256.csv',\n", "    'Parallel staged neural SD 1B (budget 16)': 'df_parallel_staged_neural_1b_100_round10_budget16_len256.csv',\n", "}\n", "\n", "results = []\n", "for q_name, q_fn in QUANTILES.items():\n", "    features = {'latency_aggr': q_name}\n", "    for df_name, df in DFS.items():        \n", "        if isinstance(df, str):\n", "            # Need to load DF first\n", "            path = get_experiment_path(df, 'r')\n", "            df = pd.read_csv(path)\n", "        features[df_name] = df.aggregate({'latency_per_token': q_fn})['latency_per_token']\n", "    results.append(features)\n", "results = pd.DataFrame(results)\n", "\n", "ax = results.plot(\n", "    x='latency_aggr',\n", "    kind='bar',\n", "    stacked=False,\n", "    figsize=(12, 6),\n", "    rot=0,\n", "    yticks=np.arange(11),\n", "    edgecolor='white',\n", "    width=0.9,\n", "    linewidth=4)\n", "ax.legend(loc='lower right')\n", "ax.grid(axis='y')\n", "plt.title('Simulation of speculative decoding performance on 100 samples on H100 with FP8 (cost of main model is 9.5, draft model is 3.0). Generate up to 256 tokens.')\n", "for i in range(len(ax.containers)):\n", "    ax.bar_label(ax.containers[i], fmt='%.1f')\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import glob\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import os\n", "\n", "import research.eval.harness.factories as factories"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["([((4, 2), 2), ((2, 3), 1)], 0)\n", "([(5,), (4, 2), (2, 3)], 0)\n", "([(5,), (4,), (2,)], 0)\n"]}], "source": ["import warnings\n", "\n", "P = 31\n", "P_POWER = np.power(31, np.arange(100000))\n", "\n", "def get_hash_l(l):\n", "    return np.cumsum(l * P_POWER[:len(l)])\n", "\n", "def find(l, sublist, p=31, hash_l=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> algorithm algorithm for pattern matching.\"\"\"\n", "    with warnings.catch_warnings():\n", "        warnings.simplefilter(\"ignore\")\n", "        if len(sublist) > len(l):\n", "            return []\n", "        \n", "        if hash_l is None:\n", "            hash_l = get_hash_l(l)\n", "        current_hash_l = hash_l[len(sublist) - 1:] - np.concatenate([[0], hash_l[:-len(sublist)]])\n", "\n", "        hash_sublist = np.sum(sublist * P_POWER[:len(sublist)])\n", "        current_hash_sublist = hash_sublist * P_POWER[:len(l) - len(sublist) + 1]\n", "\n", "        result = np.nonzero(current_hash_l == current_hash_sublist)[0] + len(sublist) - 1\n", "        result = list(reversed(result))\n", "        return result\n", "\n", "find([1, 2, 3, 1, 3, 4, 5, 1, 3], [1, 3])\n", "\n", "\n", "\n", "class LongestOverlapLM:\n", "\n", "    def __init__(self, n_sd, n_parallel_sd, allow_predicting_less_than_k_tokens=False, n_predictions_per_overlap=None):\n", "        self.n_sd = n_sd\n", "        self.n_parallel_sd = n_parallel_sd\n", "        self.allow_predicting_less_than_k_tokens = allow_predicting_less_than_k_tokens\n", "        self.n_predictions_per_overlap = n_predictions_per_overlap\n", "        if self.n_predictions_per_overlap:\n", "            assert self.allow_predicting_less_than_k_tokens\n", "\n", "    def fit(self, tokens):\n", "        self.tokens = np.array(tokens)\n", "\n", "    def predict_next_k_tokens(self, suffix, n_sd_overwrite, return_overlap=False):        \n", "        n_sd = min(n_sd_overwrite, self.n_sd)\n", "        n_sd = min(n_sd, len(self.tokens) - 1)\n", "\n", "        if n_sd == 0:\n", "            return (), 0\n", "        \n", "        assert self.n_parallel_sd > 0\n", "        if len(self.tokens) < n_sd:\n", "            print('Cannot predict %d tokens since the context length is only %d' % (k, len(self.tokens)))\n", "            return (), 0\n", "        \n", "        if self.allow_predicting_less_than_k_tokens:\n", "            searchable_tokens = self.tokens[:-1]\n", "        else:\n", "            searchable_tokens = self.tokens[:-n_sd]\n", "\n", "        hash_tokens = get_hash_l(searchable_tokens)\n", "        # the overlap length is within interval [min_length; max_length)\n", "        min_length, max_length = 0, min(len(searchable_tokens), len(suffix) + 1)\n", "        # binary search\n", "        while max_length - min_length > 1:\n", "            mid_length = int((min_length + max_length) / 2)\n", "            target_suffix = suffix[-mid_length:]            \n", "            target_pos = find(searchable_tokens, target_suffix, hash_l=hash_tokens)\n", "            if len(target_pos) == 0:\n", "                max_length = mid_length\n", "            else:\n", "                min_length = mid_length\n", "\n", "        if min_length == 0:                        \n", "            return (), 0\n", "        \n", "        predictions = []\n", "        positions_set, predictions_set = set(), set()\n", "        for l in reversed(range(1, min_length + 1)):\n", "            target_suffix = suffix[-l:]\n", "            target_positions = find(searchable_tokens, target_suffix)\n", "            for target_position in target_positions:\n", "                if target_position in positions_set:\n", "                    continue           \n", "                if self.n_predictions_per_overlap is not None:     \n", "                    assert l > 0\n", "                    if l < len(self.n_predictions_per_overlap):\n", "                        current_n_sd = self.n_predictions_per_overlap[l]\n", "                    else:\n", "                        current_n_sd = self.n_predictions_per_overlap[0]\n", "                    current_n_sd = min(current_n_sd, n_sd)\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + current_n_sd + 1, len(self.tokens))])\n", "                else:\n", "                    current_prediction = tuple(self.tokens[target_position + 1: min(target_position + n_sd + 1, len(self.tokens))])\n", "                assert len(current_prediction) >= 1\n", "                if not self.allow_predicting_less_than_k_tokens:\n", "                    assert len(current_prediction) == n_sd\n", "                if current_prediction in predictions_set:\n", "                    continue\n", "                if return_overlap:\n", "                    predictions.append((current_prediction, l))\n", "                else:\n", "                    predictions.append(current_prediction)\n", "                positions_set.add(target_position)\n", "                predictions_set.add(current_prediction)\n", "                if len(predictions) >= self.n_parallel_sd:\n", "                    break\n", "            if len(predictions) >= self.n_parallel_sd:\n", "                break        \n", "        return predictions, 0\n", "\n", "\n", "predictor = LongestOverlapLM(2, 3)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2, True))\n", "\n", "predictor = LongestOverlapLM(2, 3, True)\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))\n", "\n", "predictor = LongestOverlapLM(2, 3, True, n_predictions_per_overlap=[1])\n", "predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])\n", "print(predictor.predict_next_k_tokens([2, 3], 2))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BUDGET (2,) SELECTED ({(1,)}, 1.4)\n", "BUDGET (2,) SELECTED ({(1,), (2,)}, 1.79)\n", "BUDGET (2,) SELECTED ({(1,), (2, 5)}, 2.141)\n", "BUDGET (2,) SELECTED ({(2, 5), (1, 3)}, 2.34104)\n", "BUDGET (2,) SELECTED ({(2, 5), (1, 4), (1, 3)}, 2.541)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5), (1, 4)}, 2.7390396)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5), (1, 4), (2, 6)}, 2.7780396)\n", "BUDGET (2,) SELECTED ({(1, 3, 7), (2, 5), (1, 4), (2, 6)}, 2.7780396)\n"]}], "source": ["import numpy as np\n", "from typing import List, Optional, Union\n", "import heapq\n", "import scipy.special\n", "\n", "\n", "class TrieNode:\n", "\n", "    def __init__(self, token_id: Optional[int], logprob: Optional[float], parent):\n", "        self.token_id = token_id\n", "        self.logprob = logprob\n", "        self.parent = parent\n", "        self.children = dict()\n", "        if self.parent is not None:\n", "            self.all_tokens = self.parent.all_tokens + (self.token_id,)\n", "            self.all_logprobs = self.parent.all_logprobs + (self.logprob,)\n", "            self.sumlogprob = np.sum(self.all_logprobs)\n", "        else:\n", "            self.all_tokens = tuple()\n", "            self.all_logprobs = tuple()\n", "            self.sumlogprob = 0\n", "    \n", "    def __repr__(self):\n", "        return str(self.all_tokens)\n", "    \n", "    def is_leaf(self):\n", "        return len(self.children) == 0\n", "    \n", "    def estimate_next_token_will_be_correct(self):\n", "        # return self.meanprob * np.exp(self.sumlogprob)\n", "        next_token_prob = self.logprob or 0.0\n", "        return np.exp(next_token_prob + self.sumlogprob)\n", "\n", "    def estimate_next_token_sumlogprob(self):\n", "        # return self.meanprob * np.exp(self.sumlogprob)\n", "        next_token_prob = self.logprob or 0.0\n", "        return next_token_prob + self.sumlogprob\n", "    \n", "    def maybe_add_child(self, token_id: int, logprob: float):\n", "        if token_id in self.children:\n", "            if np.abs(self.children[token_id].logprob - logprob) > 1e-0:\n", "                print('MISMATCH', np.abs(self.children[token_id].logprob - logprob), 'when adding', token_id, 'to', self)\n", "                raise ValueError()\n", "        else:\n", "            new_node = TrieNode(token_id, logprob, parent=self)\n", "            self.children[token_id] = new_node\n", "        return self.children[token_id]\n", "    \n", "    def get_total_nodes(self):\n", "        return sum(child.get_total_nodes() for child in self.children.values()) + 1\n", "\n", "# root0 \n", "# |     \\\n", "# A1     B2\n", "# |  \\    |  \\\n", "# C3  D4  E5  F6\n", "# |\n", "# G7\n", "\n", "root = TrieNode(None, None, None)\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "a = root.maybe_add_child(1, np.log(0.4))\n", "b = root.maybe_add_child(2, np.log(0.39))\n", "c = a.maybe_add_child(3, np.log(0.5001))\n", "d = a.maybe_add_child(4, np.log(0.4999))\n", "e = b.maybe_add_child(5, np.log(0.9))\n", "f = b.maybe_add_child(6, np.log(0.1))\n", "g = c.maybe_add_child(7, np.log(0.99))\n", "\n", "\n", "def get_nodes_under_budget(root, n_sd: int, tokens_budget: int, eos_token_id: int, allow_hypothetical_next_tokens: bool):\n", "    assert tokens_budget > 0\n", "    expected_number_of_correct_tokens = 0\n", "    tokens_selected = 0    \n", "    leafs_selected = set()\n", "    Q = [(0, 0, root)]\n", "\n", "    while len(Q) > 0 and tokens_selected < tokens_budget:\n", "        minus_cumlogprob, _, node = heapq.heappop(Q)\n", "        tokens_selected += int(node is None or node.token_id is not None)\n", "        expected_number_of_correct_tokens += np.exp(-minus_cumlogprob)\n", "\n", "        if node is None:\n", "            assert allow_hypothetical_next_tokens\n", "            continue\n", "\n", "        # Remove parent\n", "        if node.parent in leafs_selected:\n", "            leafs_selected.remove(node.parent)        \n", "        leafs_selected.add(node)\n", "\n", "        # Expand children only if this is not the end of the generation.\n", "        if node.token_id != eos_token_id:            \n", "            if not node.is_leaf():\n", "                for child in node.children.values():\n", "                    if len(child.all_tokens) <= n_sd:\n", "                        heapq.heappush(Q, (-child.sumlogprob, np.random.random(), child))\n", "            else:\n", "                if allow_hypothetical_next_tokens and len(node.all_tokens) < n_sd:\n", "                    heapq.heappush(Q, (-node.estimate_next_token_sumlogprob(), np.random.random(), None))\n", "\n", "    return leafs_selected, expected_number_of_correct_tokens\n", "\n", "\n", "# we have tokens that \n", "# \n", "\n", "\n", "\n", "# BUDGET 1 SELECTED ({()}, 0)\n", "# BUDGET 2 SELECTED ({(1,)}, 0.4)\n", "# BUDGET 3 SELECTED ({(1,), (2,)}, 0.79)\n", "# BUDGET 4 SELECTED ({(1,), (2, 5)}, 1.141)\n", "# BUDGET 5 SELECTED ({(2, 5), (1, 3)}, 1.34104)\n", "# BUDGET 6 SELECTED ({(2, 5), (1, 3), (1, 4)}, 1.541)\n", "# BUDGET 7 SELECTED ({(1, 3, 7), (2, 5), (1, 4)}, 1.7390396)\n", "# BUDGET 8 SELECTED ({(1, 3, 7), (2, 6), (2, 5), (1, 4)}, 1.7780395999999998)\n", "for budget in [1, 2, 3, 4, 5, 6, 7, 8]:\n", "    print('BUDGET', b, 'SELECTED', get_nodes_under_budget(root, 4, budget, 0, False))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.19605920400000004\n", "0.3159\n", "0.09996000400000002\n", "0.0038999999999999994\n", "LIST OF PREDICTIONS\n", "(2, 5)\n", "(1, 3, 7)\n", "(1, 4)\n", "(2, 6)\n", "LIST OF PREDICTIONS EXPANDED\n", "(2, 5, 6, 7)\n", "(1, 3, 7, 9)\n", "(1, 4)\n", "(2, 6, 7, 9)\n"]}], "source": ["nodes, estimate_next_token_will_be_correct = get_nodes_under_budget(root, 3, 7, 0, False)\n", "\n", "# compute current estimate on the number of correct tokens\n", "for node in nodes:\n", "    print(node.estimate_next_token_will_be_correct())\n", "\n", "\n", "def nodes_to_candidates(nodes):\n", "    candidates = sorted([(-node.estimate_next_token_will_be_correct(), node.all_tokens) for node in nodes])\n", "    # print('nodes_to_candidates')\n", "    # for candidate in candidates[:5]:\n", "    #     print(candidate[0], tokenizer.detokenize(candidate[1]))\n", "    # print('....')\n", "    # print('------')\n", "\n", "    return [x[1] for x in candidates]\n", "\n", "\n", "# generate list of predictions and expand each with LongestOverlapLM\n", "candidates = nodes_to_candidates(nodes)\n", "print('LIST OF PREDICTIONS')\n", "for candidate in candidates:\n", "    print(candidate)\n", "\n", "\n", "def expand_with_draft_model_predictions(lm, context, candidates, n_sd, extra_budget):\n", "    # print('expand_with_draft_model_predictions')\n", "    expanded_candidates = []    \n", "    for candidate in candidates:\n", "        # print('TRYING TO EXPAND ', tokenizer.de<PERSON><PERSON><PERSON>(candidate), 'BUDGET', extra_budget)\n", "        if extra_budget > 0:\n", "            extra_predictions, _ = lm.predict_next_k_tokens(context + list(candidate), min(extra_budget, n_sd - len(candidate)))\n", "            if len(extra_predictions) == 1:\n", "                extra_predictions = extra_predictions[0]\n", "            else:\n", "                assert len(extra_predictions) == 0            \n", "            assert extra_budget >= len(extra_predictions)\n", "            extra_budget -= len(extra_predictions)\n", "            # print('extra_predictions', extra_predictions)\n", "            # print('EXPANDED ', tokenizer.detokenize(candidate), 'INTO', tokenizer.detokenize(candidate + extra_predictions))\n", "            expanded_candidates.append(candidate + extra_predictions)\n", "        else:\n", "            expanded_candidates.append(candidate)\n", "    # print('expand_with_draft_model_predictions END')\n", "    return expanded_candidates\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "lm.fit([1, 2, 5, 6, 7, 9])\n", "candidates = expand_with_draft_model_predictions(lm, [5, 6], candidates, 4, 1000)\n", "print('LIST OF PREDICTIONS EXPANDED')\n", "for candidate in candidates:\n", "    print(candidate)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue', 'checkpoint_path': '/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall', 'prompt': {'max_prefix_tokens': 1280, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3816}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> initializing torch distributed ...\n", "[2023-10-10 16:32:35,573] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-10-10 16:32:35,659] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=216.153.49.136, master_port=6000\n", "[2023-10-10 16:32:35,660] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-10-10 16:32:35,664] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[yury-dev:25959] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 49163) with 2037 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-10-10 16:32:35,947] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-10-10 16:32:36,242] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-10-10 16:32:36,243] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-10-10 16:32:36,411] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-10-10 16:32:36,414] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-10-10 16:32:36,414] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-10-10 16:32:36,415] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-10-10 16:32:36,415] [INFO] [config.py:763:print]   amp_enabled .................. <PERSON>alse\n", "[2023-10-10 16:32:36,416] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-10-10 16:32:36,417] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-10-10 16:32:36,417] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-10-10 16:32:36,418] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-10-10 16:32:36,418] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-10-10 16:32:36,419] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-10-10 16:32:36,419] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-10-10 16:32:36,420] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-10-10 16:32:36,420] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-10-10 16:32:36,421] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-10-10 16:32:36,422] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-10-10 16:32:36,422] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-10-10 16:32:36,423] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-10-10 16:32:36,424] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-10-10 16:32:36,424] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-10-10 16:32:36,425] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-10-10 16:32:36,426] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-10-10 16:32:36,427] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-10-10 16:32:36,428] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-10-10 16:32:36,429] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 1e-05}\n", "[2023-10-10 16:32:36,430] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-10-10 16:32:36,431] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-10-10 16:32:36,431] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-10-10 16:32:36,432] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-10-10 16:32:36,432] [INFO] [config.py:763:print]   prescale_gradients ........... <PERSON>alse\n", "[2023-10-10 16:32:36,433] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-10-10 16:32:36,434] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-10-10 16:32:36,434] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-10-10 16:32:36,435] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-10-10 16:32:36,435] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-10-10 16:32:36,436] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-10-10 16:32:36,437] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-10-10 16:32:36,437] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-10-10 16:32:36,438] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-10-10 16:32:36,439] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-10-10 16:32:36,440] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-10-10 16:32:36,440] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-10-10 16:32:36,441] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-10-10 16:32:36,442] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-10-10 16:32:36,442] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-10-10 16:32:36,443] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-10-10 16:32:36,444] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 1e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.17578601837158203 seconds\n", "[2023-10-10 16:32:37,249] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-10-10 16:32:37,283] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\n", "[2023-10-10 16:32:37,329] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "[2023-10-10 16:32:37,489] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-10-10 16:32:37,560] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-10-10 16:32:37,624] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-10-10 16:32:37,691] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-10-10 16:32:37,753] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-10-10 16:32:37,814] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-10-10 16:32:37,873] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-10-10 16:32:37,934] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-10-10 16:32:37,997] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-10-10 16:32:38,059] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-10-10 16:32:38,118] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-10-10 16:32:38,181] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-10-10 16:32:38,247] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-10-10 16:32:38,307] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-10-10 16:32:38,366] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-10-10 16:32:38,429] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-10-10 16:32:38,483] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-10-10 16:32:38,536] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-10-10 16:32:38,593] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-10-10 16:32:38,655] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-10-10 16:32:38,712] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-10-10 16:32:38,768] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-10-10 16:32:38,824] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-10-10 16:32:38,879] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-10-10 16:32:38,933] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-10-10 16:32:38,935] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-10-10 16:32:39,060] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["# Based on this https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e\n", "model = factories.create_model({\n", "    \"name\": \"rogue\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_v2downsmall\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3816,\n", "    },\n", "})\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IS LOADED True\n", "TOKENIZER <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7f4a35312d00>\n"]}], "source": ["# allow '<|endoftext|>'\n", "ALL_SPECIAL_TOKENS_IDS = [model.tokenizer.tokenize(token)[0] for token in model.tokenizer.all_special_tokens() if token != '<|endoftext|>']\n", "ALL_SPECIAL_TOKENS_IDS_SET = set(ALL_SPECIAL_TOKENS_IDS)\n", "\n", "\n", "EOS_TOKEN_ID = model.tokenizer.tokenize('<|endoftext|>')[0]\n", "EOS_TOKEN_ID\n", "\n", "print('IS LOADED', model.is_loaded)\n", "print('TOKENIZER', model.tokenizer)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "data_fim_retrieval = []\n", "\n", "with open('/mnt/efs/augment/user/yury/michiel_pythonmedium_bm25/data_32l.16B_generations.jsonl') as f:\n", "    for line in f:\n", "        datum = json.loads(line)\n", "        if EOS_TOKEN_ID in datum['predictions']:\n", "            datum['label'] = datum['predictions'][:datum['predictions'].index(EOS_TOKEN_ID) + 1]\n", "        data_fim_retrieval.append(datum)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COST 4\n", "PREDICITONS\n", "=> = self.get_mention_prefix(mention)<|endoftext|>\n", "=> = self.get_mention_prefix(mention)<|pause|>\n", "=> = self.get_mention_prefix( mention_back += [s for s in re.\n", "=> = self.get_ mention_back += [s for s in re.split('\n", "=> = \"http://twitter.com/\" + tweet.author.screen\n"]}], "source": ["import gc\n", "import copy\n", "import torch \n", "import torch.nn.functional as F\n", "import megatron\n", "import megatron.text_generation_utils as text_gen_utils\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "\n", "class NeuralLM:\n", "    \n", "    def __init__(self, tokens_budget, main_model_cost=9.5, model_cost=3.0, draft_model=None, draft_tokens_budget=None, n_rounds=None):\n", "        self.tokens_budget = tokens_budget\n", "        self.main_model_cost = main_model_cost\n", "        self.model_cost = model_cost\n", "        self.draft_model = draft_model        \n", "        if self.draft_model is not None:\n", "            assert draft_tokens_budget is not None\n", "            self.draft_tokens_budget = draft_tokens_budget\n", "        self.n_rounds = n_rounds\n", "\n", "    def fit(self, tokens):\n", "        if self.draft_model is not None:\n", "            self.draft_model.fit(tokens)\n", "\n", "    def _get_final_predictions(self, context, n_sd, root):\n", "        nodes, _ = get_nodes_under_budget(root, n_sd, self.tokens_budget, EOS_TOKEN_ID, False)\n", "        candidates = nodes_to_candidates(nodes)        \n", "        if self.draft_model is not None:            \n", "            candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.draft_tokens_budget)\n", "        return candidates\n", "\n", "    def predict_next_k_tokens(self, context, n_sd):\n", "        root = TrieNode(None, None, None)\n", "        \n", "        n_rounds = 0\n", "\n", "        while self.n_rounds is None or (self.n_rounds is not None and n_rounds < self.n_rounds):\n", "            nodes, expected_number_of_correct_tokens_before_round = get_nodes_under_budget(root, n_sd - 1, self.tokens_budget, EOS_TOKEN_ID, False)\n", "            # print('nodes', nodes)\n", "\n", "            if self.n_rounds is None:\n", "                _, expected_number_of_correct_tokens_after_round = get_nodes_under_budget(root, n_sd, self.tokens_budget, EOS_TOKEN_ID, True)\n", "                # cost_before_round = (n_rounds * self.model_cost + self.main_model_cost) / expected_number_of_correct_tokens_before_round\n", "                # cost_after_round = ((n_rounds + 1) * self.model_cost + self.main_model_cost) / expected_number_of_correct_tokens_after_round\n", "                # print('expected_number_of_correct_tokens_before_round', expected_number_of_correct_tokens_before_round)                \n", "                # print('expected_number_of_correct_tokens_after_round', expected_number_of_correct_tokens_after_round)\n", "                # print('cost_before_round', cost_before_round)                \n", "                # print('cost_after_round', cost_after_round)\n", "                if ((n_rounds + 1) * self.model_cost + self.main_model_cost) * expected_number_of_correct_tokens_before_round >= (n_rounds * self.model_cost + self.main_model_cost) * expected_number_of_correct_tokens_after_round:\n", "                    # print('A')\n", "                    break\n", "\n", "            nodes = [node for node in nodes if node.is_leaf()]                        \n", "            if len(nodes) == 0:\n", "                break\n", "\n", "            candidates = nodes_to_candidates(nodes)\n", "            if self.draft_model is not None:\n", "                candidates = expand_with_draft_model_predictions(self.draft_model, context, candidates, n_sd, self.draft_tokens_budget)\n", "\n", "            # print('CANDIDATES', n_rounds)\n", "            # for candidate in candidates:\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "            n_rounds += 1\n", "            candidates = [list(context + list(x)) for x in candidates]            \n", "            for candidate in candidates:\n", "                with torch.no_grad():\n", "                    context_tokens_tensor, attention_mask, position_ids = text_gen_utils.get_batch(model._neox_args, torch.tensor([candidate], dtype=torch.int64))                \n", "                    model_inputs = (\n", "                        context_tokens_tensor,  # input_ids\n", "                        position_ids,  # position_ids\n", "                        attention_mask,  # attention_mask\n", "                    )            \n", "                    \n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "                    logits = text_gen_utils.forward_model(\n", "                        model=model.neox_model,\n", "                        model_inputs=model_inputs,\n", "                        is_pipe_parallel=model._neox_args.is_pipe_parallel,\n", "                        pipe_parallel_size=model._neox_args.pipe_parallel_size,\n", "                        timers=None,\n", "                    )\n", "                    model.neox_model.module.clear_cache()  # clear the k,v cache after                    \n", "                    # logits[:, :, ALL_SPECIAL_TOKENS_IDS] = -10000                                    \n", "                    assert logits.shape[0] == 1, logits.shape\n", "                    logits = logits[0]\n", "                    logprobs = F.log_softmax(logits, dim=-1)\n", "                    top_predictions = torch.topk(logprobs, self.tokens_budget, dim=-1, sorted=True)                \n", "\n", "                logprobs = logprobs.float().cpu().numpy()\n", "                top_prediction_values = top_predictions.values.float().cpu().numpy()\n", "                top_prediction_indices = top_predictions.indices.cpu().numpy()\n", "                gc.collect()\n", "                torch.cuda.empty_cache()                \n", "\n", "                for index in range(len(context) - 1, len(candidate)): \n", "                    if index == len(context) - 1:\n", "                        node = root\n", "                    else:\n", "                        token_id = candidate[index]\n", "                        node = node.maybe_add_child(token_id, logprobs[index - 1, token_id])\n", "                    # add new token from the input\n", "                    for token_id, logprob in zip(top_prediction_indices[index], top_prediction_values[index]):\n", "                        # print('adding #2', token_id, 'to', node, 'with logprob', logprob)\n", "                        _ = node.maybe_add_child(token_id.item(), logprob)\n", "                \n", "            # print('ROUND', n_rounds)\n", "            # for candidate in self._get_final_predictions(context, n_sd, root):\n", "            #     print('=>', tokenizer.de<PERSON><PERSON><PERSON>(candidate))\n", "            # print('--')\n", "\n", "        candidates = self._get_final_predictions(context, n_sd, root)\n", "        for candidate in candidates:\n", "            assert len(candidate) <= n_sd\n", "        return candidates, n_rounds\n", "\n", "sample = data_fim_retrieval[0]\n", "\n", "# lm = NeuralLM(tokens_budget=16, n_rounds=10)\n", "# predictions, cost = lm.predict_next_k_tokens(sample['context'], 10)\n", "# print('COST', cost)\n", "# print('PREDICITONS')\n", "# for p in predictions:\n", "#     print('=>', tokenizer.detok<PERSON>ze(p))\n", "\n", "\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=None)\n", "lm.fit(sample['context'])\n", "predictions, cost = lm.predict_next_k_tokens(sample['context'], 20)\n", "print('COST', cost)\n", "print('PREDICITONS')\n", "for p in predictions:\n", "    print('=>', tokenizer.detok<PERSON>ze(p))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'context_length': 3668, 'label_length': 2, 'n_rounds': 1, 'n_draft_rounds': 1, 'average_draft_rounds': 1.0, 'average_draft_tokens_accepted': 1.0, 'simulation_time': 547.8098392486572, 'beam_size': 16.0, 'n_unique_first_token': 16.0, 'n_unique_two_tokens': 16.0}\n", "{'context_length': 3668, 'label_length': 2, 'n_rounds': 1, 'n_draft_rounds': 1, 'average_draft_rounds': 1.0, 'average_draft_tokens_accepted': 1.0, 'simulation_time': 541.5027141571045, 'beam_size': 256.0, 'n_unique_first_token': 256.0, 'n_unique_two_tokens': 256.0}\n", "{'context_length': 3668, 'label_length': 2, 'n_rounds': 1, 'n_draft_rounds': 1, 'average_draft_rounds': 1.0, 'average_draft_tokens_accepted': 1.0, 'simulation_time': 545.6271171569824, 'beam_size': 16.0, 'n_unique_first_token': 16.0, 'n_unique_two_tokens': 16.0}\n"]}], "source": ["import time \n", "\n", "def safe_mean(l):\n", "    if len(l) > 0:\n", "        return np.array(l).mean()\n", "    else:\n", "        return 0    \n", "\n", "def measure_latency_per_sample_parallel_sd(lm, sample):\n", "    context = sample['context']\n", "    if not isinstance(context, list):\n", "        context = context.tolist()\n", "    label = sample['label']\n", "    if not isinstance(label, list):\n", "        label = label.tolist()        \n", "\n", "    n_rounds, n_draft_rounds = 0, 0\n", "    index = 0\n", "    beam_sizes, simulation_time, n_unique_first_token, n_unique_two_tokens = [], [], [], []\n", "    draft_tokens_accepted = []\n", "    while index < len(label):\n", "        n_rounds += 1\n", "        max_tokens_to_predict = len(label) - index - 1\n", "        actual_tokens_predicted = 1\n", "        if max_tokens_to_predict > 0:\n", "            lm.fit(context + label[:index])\n", "            start_time = time.time()\n", "            # print('CURRENT GENERATION', tokenizer.detokenize(label[:index]))\n", "            beam_of_predictions, current_draft_rounds = lm.predict_next_k_tokens(\n", "                context + label[:index],\n", "                max_tokens_to_predict)\n", "\n", "            # print('PREDICITONS for max_tokens_to_predict=', max_tokens_to_predict)\n", "            # for p in beam_of_predictions:\n", "            #     print('=>', tokenizer.detok<PERSON>ze(p))\n", "            # print('------')                \n", "\n", "            n_draft_rounds += current_draft_rounds\n", "\n", "            simulation_time.append(1000 * (time.time() - start_time))\n", "            beam_sizes.append(len(beam_of_predictions))\n", "            n_unique_first_token.append(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            n_unique_two_tokens.append(len({predictions[:2] for predictions in beam_of_predictions}))\n", "\n", "            # if index == 0:\n", "            #     print(len({predictions[:1] for predictions in beam_of_predictions}))\n", "            \n", "            furthest_index = index\n", "            for predictions in beam_of_predictions:\n", "                current_index = index\n", "                for prediction_index in range(len(predictions)):\n", "                    if predictions[prediction_index] == label[current_index]:\n", "                        current_index += 1\n", "                    else:\n", "                        break\n", "                furthest_index = max(furthest_index, current_index)\n", "            draft_tokens_accepted.append(furthest_index - index)\n", "            index = furthest_index\n", "        # print(furthest_index)\n", "        # Make prediction with the main model\n", "        index += 1\n", "\n", "    return {\n", "        'context_length': len(context),\n", "        'label_length': len(label),\n", "        'n_rounds': n_rounds,\n", "        'n_draft_rounds': n_draft_rounds,\n", "        'average_draft_rounds': n_draft_rounds / n_rounds,\n", "        'average_draft_tokens_accepted': safe_mean(draft_tokens_accepted),\n", "        'simulation_time': safe_mean(simulation_time),\n", "        'beam_size': safe_mean(beam_sizes),\n", "        'n_unique_first_token': safe_mean(n_unique_first_token),\n", "        'n_unique_two_tokens': safe_mean(n_unique_two_tokens),\n", "    }\n", "\n", "\n", "sample_index = 2\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[sample_index]))\n", "\n", "lm = NeuralLM(tokens_budget=256, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[sample_index]))\n", "\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=None)\n", "print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[sample_index]))\n", "\n", "# lm = NeuralLM(tokens_budget=64, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=None)\n", "# print(measure_latency_per_sample_parallel_sd(lm, data_fim_retrieval[sample_index]))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13eb9f26dcd543fd89ebcc1ce7b73349", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os \n", "\n", "\n", "BASE_DIR = '/mnt/efs/augment/user/yury/parallel_sd_staged_32label_v2'\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "\n", "def measure_latency_per_data_parallel_sd_verbose(lm, data, main_round_cost, draft_round_cost, exp_name=None):\n", "    if exp_name is not None:\n", "        path = os.path.join(BASE_DIR, exp_name)\n", "        if os.path.isfile(path):\n", "            print('EXPEREIMENT', exp_name, 'CACHED')\n", "            df = pd.read_csv(path)\n", "            print(df.mean())\n", "            return df                    \n", "\n", "    pbar = tqdm(data, total=len(data))\n", "    df = None\n", "    index = 0\n", "    for d in pbar:\n", "        datum = measure_latency_per_sample_parallel_sd(lm=lm, sample=d)\n", "        datum['latency'] = main_round_cost * datum['n_rounds'] + draft_round_cost * datum['n_draft_rounds']\n", "        datum['latency_per_token'] = datum['latency'] / datum['label_length']        \n", "        if df is None:\n", "            df = pd.DataFrame([datum])\n", "        else:\n", "            df = pd.concat([df, pd.DataFrame([datum])], ignore_index=True)\n", "        if datum['latency_per_token'] > 9.4:\n", "            print('No speed ups on sample', index)\n", "        index += 1\n", "        pbar.set_description('Latency per token: ' + ' '.join(['%s: %.2f' % (k, v) for k, v in df['latency_per_token'].aggregate([q50, q80, q90, q95, q99]).to_dict().items()]))\n", "\n", "    if exp_name is not None:\n", "        df.to_csv(path, index=False)\n", "        print(df.mean())\n", "    return df\n", "\n", "lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)\n", "_ = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:2], 9.5, 3.0)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_parallel_staged_neural_1b_100_round1_budget16_cost2.csv CACHED\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            5.170000\n", "n_draft_rounds                      4.920000\n", "average_draft_rounds                0.938897\n", "average_draft_tokens_accepted       4.452717\n", "simulation_time                   709.594552\n", "beam_size                          13.685462\n", "n_unique_first_token               11.863390\n", "n_unique_two_tokens                12.597136\n", "latency                            58.955000\n", "latency_per_token                   3.333867\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)\n", "df16_round1 = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 2.0, 'df_parallel_staged_neural_1b_100_round1_budget16_cost2.csv')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_parallel_staged_neural_1b_100_round1_budget64_cost2.csv CACHED\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            5.050000\n", "n_draft_rounds                      4.810000\n", "average_draft_rounds                0.939415\n", "average_draft_tokens_accepted       4.498005\n", "simulation_time                   787.714930\n", "beam_size                          61.350596\n", "n_unique_first_token               47.546083\n", "n_unique_two_tokens                51.884757\n", "latency                            57.595000\n", "latency_per_token                   3.288398\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=64, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)\n", "df64_round1 = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 2.0, 'df_parallel_staged_neural_1b_100_round1_budget64_cost2.csv')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_parallel_staged_neural_1b_100_roundNone_budget16_cost2.csv CACHED\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            3.970000\n", "n_draft_rounds                      6.950000\n", "average_draft_rounds                2.196078\n", "average_draft_tokens_accepted       7.035094\n", "simulation_time                  9821.993257\n", "beam_size                           9.523441\n", "n_unique_first_token                5.955945\n", "n_unique_two_tokens                 7.701466\n", "latency                            51.615000\n", "latency_per_token                   2.848764\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=None)\n", "df16_none = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 2.0, 'df_parallel_staged_neural_1b_100_roundNone_budget16_cost2.csv')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXPEREIMENT df_parallel_staged_neural_1b_100_roundNone_budget64_cost2.csv CACHED\n", "context_length                    3687.360000\n", "label_length                        21.500000\n", "n_rounds                             3.690000\n", "n_draft_rounds                       6.700000\n", "average_draft_rounds                 2.340706\n", "average_draft_tokens_accepted        8.239828\n", "simulation_time                  47579.089255\n", "beam_size                           46.442287\n", "n_unique_first_token                22.278211\n", "n_unique_two_tokens                 30.484687\n", "latency                             48.455000\n", "latency_per_token                    2.746967\n", "dtype: float64\n"]}], "source": ["lm = NeuralLM(tokens_budget=64, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=None)\n", "df64_none = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 2.0, 'df_parallel_staged_neural_1b_100_roundNone_budget64_cost2.csv')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "36845def1d1a4b219967576611fda60e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 0\n", "No speed ups on sample 1\n", "No speed ups on sample 2\n", "No speed ups on sample 3\n", "No speed ups on sample 4\n", "No speed ups on sample 5\n", "No speed ups on sample 6\n", "No speed ups on sample 7\n", "No speed ups on sample 8\n", "No speed ups on sample 9\n", "No speed ups on sample 10\n", "No speed ups on sample 11\n", "No speed ups on sample 12\n", "No speed ups on sample 13\n", "No speed ups on sample 14\n", "No speed ups on sample 15\n", "No speed ups on sample 16\n", "No speed ups on sample 17\n", "No speed ups on sample 18\n", "No speed ups on sample 19\n", "No speed ups on sample 20\n", "No speed ups on sample 21\n", "No speed ups on sample 22\n", "No speed ups on sample 23\n", "No speed ups on sample 24\n", "No speed ups on sample 25\n", "No speed ups on sample 26\n", "No speed ups on sample 27\n", "No speed ups on sample 28\n", "No speed ups on sample 29\n", "No speed ups on sample 30\n", "No speed ups on sample 31\n", "No speed ups on sample 32\n", "No speed ups on sample 33\n", "No speed ups on sample 34\n", "No speed ups on sample 35\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 39\n", "No speed ups on sample 40\n", "No speed ups on sample 41\n", "No speed ups on sample 42\n", "No speed ups on sample 43\n", "No speed ups on sample 44\n", "No speed ups on sample 45\n", "No speed ups on sample 46\n", "No speed ups on sample 47\n", "No speed ups on sample 48\n", "No speed ups on sample 49\n", "No speed ups on sample 50\n", "No speed ups on sample 51\n", "No speed ups on sample 52\n", "No speed ups on sample 53\n", "No speed ups on sample 54\n", "No speed ups on sample 55\n", "No speed ups on sample 56\n", "No speed ups on sample 57\n", "No speed ups on sample 58\n", "No speed ups on sample 59\n", "No speed ups on sample 60\n", "No speed ups on sample 61\n", "No speed ups on sample 62\n", "No speed ups on sample 63\n", "No speed ups on sample 64\n", "No speed ups on sample 65\n", "No speed ups on sample 66\n", "No speed ups on sample 67\n", "No speed ups on sample 68\n", "No speed ups on sample 69\n", "No speed ups on sample 70\n", "No speed ups on sample 71\n", "No speed ups on sample 72\n", "No speed ups on sample 73\n", "No speed ups on sample 74\n", "No speed ups on sample 75\n", "No speed ups on sample 76\n", "No speed ups on sample 77\n", "No speed ups on sample 78\n", "No speed ups on sample 79\n", "No speed ups on sample 80\n", "No speed ups on sample 81\n", "No speed ups on sample 82\n", "No speed ups on sample 83\n", "No speed ups on sample 84\n", "No speed ups on sample 85\n", "No speed ups on sample 86\n", "No speed ups on sample 87\n", "No speed ups on sample 88\n", "No speed ups on sample 89\n", "No speed ups on sample 90\n", "No speed ups on sample 91\n", "No speed ups on sample 92\n", "No speed ups on sample 93\n", "No speed ups on sample 94\n", "No speed ups on sample 95\n", "No speed ups on sample 96\n", "No speed ups on sample 97\n", "No speed ups on sample 98\n", "No speed ups on sample 99\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                           21.500000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       0.000000\n", "simulation_time                     0.006368\n", "beam_size                           0.000000\n", "n_unique_first_token                0.000000\n", "n_unique_two_tokens                 0.000000\n", "latency                           204.250000\n", "latency_per_token                   9.500000\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a7a8a89a0714876826ac342ce62b9af", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 2\n", "No speed ups on sample 4\n", "No speed ups on sample 12\n", "No speed ups on sample 16\n", "No speed ups on sample 22\n", "No speed ups on sample 24\n", "No speed ups on sample 26\n", "No speed ups on sample 32\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 41\n", "No speed ups on sample 62\n", "No speed ups on sample 75\n", "No speed ups on sample 78\n", "No speed ups on sample 84\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            9.400000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       1.904132\n", "simulation_time                     0.615224\n", "beam_size                           0.633645\n", "n_unique_first_token                0.633645\n", "n_unique_two_tokens                 0.633645\n", "latency                            89.300000\n", "latency_per_token                   5.018798\n", "dtype: float64\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5044c6b2c61d4eb9b7b427d7068d686f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["No speed ups on sample 2\n", "No speed ups on sample 4\n", "No speed ups on sample 12\n", "No speed ups on sample 16\n", "No speed ups on sample 22\n", "No speed ups on sample 24\n", "No speed ups on sample 26\n", "No speed ups on sample 36\n", "No speed ups on sample 37\n", "No speed ups on sample 38\n", "No speed ups on sample 41\n", "No speed ups on sample 62\n", "No speed ups on sample 78\n", "No speed ups on sample 84\n", "context_length                   3687.360000\n", "label_length                       21.500000\n", "n_rounds                            8.290000\n", "n_draft_rounds                      0.000000\n", "average_draft_rounds                0.000000\n", "average_draft_tokens_accepted       2.395242\n", "simulation_time                     0.645203\n", "beam_size                           2.860082\n", "n_unique_first_token                1.823724\n", "n_unique_two_tokens                 2.174692\n", "latency                            78.755000\n", "latency_per_token                   4.519333\n", "dtype: float64\n"]}], "source": ["lm = LongestOverlapLM(0, 1, True)\n", "df_baseline = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_baseline.mean())\n", "\n", "\n", "lm = LongestOverlapLM(12, 1, True)\n", "df_longestoverlap = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_longestoverlap.mean())\n", "\n", "\n", "lm = LongestOverlapLM(12, 6, True)\n", "df_longestoverlap_parallel = measure_latency_per_data_parallel_sd_verbose(lm, data_fim_retrieval[:100], 9.5, 3.0)\n", "print(df_longestoverlap_parallel.mean())"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df_baseline' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb Cell 16\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m QUANTILE_NAMES \u001b[39m=\u001b[39m []\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m QUANTILES \u001b[39m=\u001b[39m {\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m     \u001b[39m# 'average': 'mean',\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39m50 percentile (median)\u001b[39m\u001b[39m'\u001b[39m: q50,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m     \u001b[39m# '99 percentile': q99,\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m }\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=34'>35</a>\u001b[0m DFS \u001b[39m=\u001b[39m {\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=35'>36</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mno speculative decoding (SD)\u001b[39m\u001b[39m'\u001b[39m: df_baseline,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=36'>37</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mLongestOverlapLM (in prod)\u001b[39m\u001b[39m'\u001b[39m: df_longestoverlap,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=37'>38</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mNeural SD (1B cost = 3.0ms)\u001b[39m\u001b[39m'\u001b[39m: \u001b[39m'\u001b[39m\u001b[39m/mnt/efs/augment/user/yury/sd_staged_v3/df_staged_neural_1b_optimal_100.csv\u001b[39m\u001b[39m'\u001b[39m,    \n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=38'>39</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mNeural SD (1B cost = 2.0ms)\u001b[39m\u001b[39m'\u001b[39m: \u001b[39m'\u001b[39m\u001b[39m/mnt/efs/augment/user/yury/sd_staged_v3/df_staged_neural_1b_optimal_100_cost2.csv\u001b[39m\u001b[39m'\u001b[39m,    \n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=39'>40</a>\u001b[0m     \u001b[39m# 'Parallel neural SD (1B cost = 2.0ms) -- 1 round (budget 16)': df16_round1,\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=40'>41</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mParallel neural SD (1B cost = 2.0ms) -- 1 round (budget 64)\u001b[39m\u001b[39m'\u001b[39m: df64_round1,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=41'>42</a>\u001b[0m     \u001b[39m# 'Parallel neural SD (1B cost = 2.0ms) -- adaptive (budget 16)': df16_none,\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=42'>43</a>\u001b[0m     \u001b[39m'\u001b[39m\u001b[39mParallel neural SD (1B cost = 2.0ms) -- adaptive (budget 64)\u001b[39m\u001b[39m'\u001b[39m: df64_none,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=43'>44</a>\u001b[0m }\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=45'>46</a>\u001b[0m results \u001b[39m=\u001b[39m []\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d797572792d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f646576227d7d/home/<USER>/augment/experimental/yury/parallel_sd_staged_v1.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=46'>47</a>\u001b[0m \u001b[39mfor\u001b[39;00m q_name, q_fn \u001b[39min\u001b[39;00m QUANTILES\u001b[39m.\u001b[39mitems():\n", "\u001b[0;31mNameError\u001b[0m: name 'df_baseline' is not defined"]}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "\n", "def q50(x):\n", "    return x.quantile(0.5)\n", "\n", "def q80(x):\n", "    return x.quantile(0.8)\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "def q95(x):\n", "    return x.quantile(0.95)\n", "\n", "def q99(x):\n", "    return x.quantile(0.99)\n", "\n", "\n", "QUANTILES = [q50, q80, q90, q95, q99]\n", "QUANTILE_NAMES = []\n", "\n", "QUANTILES = {\n", "    # 'average': 'mean',\n", "    '50 percentile (median)': q50,\n", "    '80 percentile': q80,\n", "    '90 percentile': q90,\n", "    '95 percentile': q95,\n", "    # '99 percentile': q99,\n", "}\n", "\n", "\n", "\n", "DFS = {\n", "    'no speculative decoding (SD)': df_baseline,\n", "    'LongestOverlapLM (in prod)': df_longestoverlap,\n", "    'Neural SD (1B cost = 3.0ms)': '/mnt/efs/augment/user/yury/sd_staged_v3/df_staged_neural_1b_optimal_100.csv',    \n", "    'Neural SD (1B cost = 2.0ms)': '/mnt/efs/augment/user/yury/sd_staged_v3/df_staged_neural_1b_optimal_100_cost2.csv',    \n", "    # 'Parallel neural SD (1B cost = 2.0ms) -- 1 round (budget 16)': df16_round1,\n", "    'Parallel neural SD (1B cost = 2.0ms) -- 1 round (budget 64)': df64_round1,\n", "    # 'Parallel neural SD (1B cost = 2.0ms) -- adaptive (budget 16)': df16_none,\n", "    'Parallel neural SD (1B cost = 2.0ms) -- adaptive (budget 64)': df64_none,\n", "}\n", "\n", "results = []\n", "for q_name, q_fn in QUANTILES.items():\n", "    features = {'latency_aggr': q_name}\n", "    for df_name, df in DFS.items():        \n", "        if isinstance(df, str):\n", "            if df.startswith('/'):\n", "                path = df\n", "            else:            \n", "                path = get_experiment_path(df, 'r')\n", "            df = pd.read_csv(path)\n", "            \n", "        features[df_name] = df.aggregate({'latency_per_token': q_fn})['latency_per_token']\n", "    results.append(features)\n", "results = pd.DataFrame(results)\n", "\n", "ax = results.plot(\n", "    x='latency_aggr',\n", "    kind='bar',\n", "    stacked=False,\n", "    figsize=(16, 6),\n", "    rot=0,\n", "    yticks=np.arange(11),\n", "    edgecolor='white',\n", "    width=0.9,\n", "    linewidth=4)\n", "ax.legend(loc='lower right', fontsize=14)\n", "ax.grid(axis='y')\n", "plt.title('Simulation of speculative decoding performance on 100 samples on H100 with FP8 (cost of main model is 9.5, draft model is 3.0). Generate up to 32 tokens.')\n", "ax.set_ylabel('Latency per token (ms)', fontsize=14)\n", "plt.xticks(fontsize=12)\n", "ax.set_xlabel('', fontsize=14)\n", "for i in range(len(ax.containers)):\n", "    ax.bar_label(ax.containers[i], fmt='%.1f')\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
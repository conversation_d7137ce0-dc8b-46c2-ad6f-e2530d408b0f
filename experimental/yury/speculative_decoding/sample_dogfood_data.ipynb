{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded 411 samples.\n"]}], "source": ["from base.datasets.tenants import DOGFOOD\n", "from google.cloud import bigquery\n", "\n", "\n", "DATASET = DOGFOOD\n", "LAST_N_DAYS = 14\n", "N_REQUESTS_PER_USER = 10\n", "\n", "\n", "QUERY = f\"\"\"\\\n", "WITH DATA AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    `{DATASET.project_id}.{DATASET.dataset_name}.request_event`\n", "  WHERE\n", "    time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {LAST_N_DAYS} DAY) ),\n", "  requests AS (\n", "  SELECT\n", "    request_id,\n", "    raw_json AS request_json\n", "  FROM\n", "    DATA\n", "  WHERE\n", "    event_type = 'chat_host_request' ),\n", "  requests_metadata AS (\n", "  SELECT\n", "    request_id,\n", "    raw_json AS metadata_json\n", "  FROM\n", "    DATA\n", "  WHERE\n", "    event_type = 'request_metadata' ),\n", "  responses AS (\n", "  SELECT\n", "    request_id,\n", "    raw_json AS response_json\n", "  FROM\n", "    DATA\n", "  WHERE\n", "    event_type = 'chat_host_response' )\n", "SELECT\n", "  user_id,\n", "  request_id,\n", "  prompt_tokens,\n", "  output_tokens\n", "FROM (\n", "  SELECT\n", "    user_id,\n", "    request_id,\n", "    JSON_EXTRACT(request_json, \"$.tokenization.token_ids\") AS prompt_tokens,\n", "    JSON_EXTRACT(response_json, \"$.tokenization.token_ids\") AS output_tokens,\n", "    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY RAND()) AS row_num\n", "  FROM (\n", "    SELECT\n", "      requests.request_id AS request_id,\n", "      JSON_VALUE(requests_metadata.metadata_json, \"$.user_id\") AS user_id,\n", "      requests_metadata.metadata_json AS metadata_json,\n", "      requests.request_json AS request_json,\n", "      responses.response_json AS response_json\n", "    FROM\n", "      requests_metadata\n", "    JOIN\n", "      requests\n", "    ON\n", "      requests.request_id = requests_metadata.request_id\n", "    JOIN\n", "      responses\n", "    ON\n", "      requests_metadata.request_id = responses.request_id )\n", "  WHERE\n", "    user_id NOT IN (\"intern-chat-proto\",\n", "      \"eval-determined-bot\",\n", "      \"health-check-1\") )\n", "WHERE\n", "  row_num <= {N_REQUESTS_PER_USER}\n", "ORDER BY\n", "  RAND()\n", "\"\"\"\n", "\n", "\n", "client = bigquery.Client(project=DATASET.project_id)\n", "data = []\n", "for row in client.query_and_wait(QUERY):\n", "    data.append({\n", "        \"prompt_tokens\": row[2],\n", "        \"output_tokens\": row[3],\n", "    })\n", "\n", "print(f\"Downloaded {len(data)} samples.\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "409 409\n", "2\n"]}], "source": ["from pathlib import Path\n", "import json\n", "\n", "import torch\n", "from megatron.data import indexed_dataset\n", "from megatron.tokenizer.tokenizer import LLama3InstructTokenizer\n", "\n", "MAX_LENGTH = 8192 + 1\n", "output_dir = Path(\"/mnt/efs/augment/user/yury/speculative_decoding/llama3/\")\n", "\n", "\n", "tokenizer = LLama3InstructTokenizer()\n", "\n", "builder = indexed_dataset.make_builder(\n", "    output_dir / \"dogfood_jul2024.bin\",\n", "    impl=\"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")\n", "\n", "n_too_long_samples = 0\n", "\n", "filtered_samples = []\n", "\n", "for datum in data:\n", "    tensor = [-x for x in datum[\"prompt_tokens\"]] + datum[\"output_tokens\"] + [tokenizer.eod_id]\n", "    if len(tensor) < MAX_LENGTH:\n", "        tensor.extend([-tokenizer.eod_id] * (MAX_LENGTH - len(tensor)))\n", "    else:\n", "        n_too_long_samples += 1\n", "        continue\n", "    assert len(tensor) == MAX_LENGTH, (len(tensor), MAX_LENGTH)\n", "    builder.add_item(torch.tensor(tensor))\n", "    filtered_samples.append(\n", "        {\n", "            \"context\": datum[\"prompt_tokens\"],\n", "            \"label\": datum[\"output_tokens\"],\n", "        }\n", "    )\n", "\n", "builder.finalize(output_dir / \"dogfood_jul2024.idx\")\n", "\n", "ds = indexed_dataset.make_dataset(\n", "    str(output_dir / \"dogfood_jul2024\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "print(len(ds), len(filtered_samples))\n", "print(n_too_long_samples)\n", "\n", "with (output_dir / \"dogfood_jul2024.json\").open(\"w\") as f:\n", "    json.dump(filtered_samples, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
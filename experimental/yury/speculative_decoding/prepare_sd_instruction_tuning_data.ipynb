{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 90923 prompts.\n", "Loaded 90923 outputs for prompts.\n"]}], "source": ["import json\n", "from pathlib import Path\n", "\n", "SAMPLES_PROMPT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/binks_v4_for_infer/tokenized_samples.json\")\n", "\n", "with SAMPLES_PROMPT_PATH.open(\"r\") as f:\n", "    tokenized_prompts = json.load(f)\n", "\n", "print(f\"Loaded {len(tokenized_prompts)} prompts.\")\n", "\n", "SAMPLES_OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/binks_v4_for_infer/llama_inference_results.json\")\n", "\n", "with SAMPLES_OUTPUT_PATH.open(\"r\") as f:\n", "    outputs = json.load(f)\n", "\n", "print(f\"Loaded {len(tokenized_prompts)} outputs for prompts.\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from megatron.tokenizer.tokenizer import LLama3InstructTokenizer\n", "\n", "MAX_LENGTH = 8192 + 1\n", "\n", "tokenizer = LLama3InstructTokenizer()\n", "\n", "samples = []\n", "\n", "for tokenized_prompt, output in zip(tokenized_prompts, outputs):\n", "    tokenized_output = tokenizer.tokenize(output) + [tokenizer.eod_id]\n", "    tokens = [-x for x in tokenized_prompt] + tokenized_output\n", "    assert len(tokens) < MAX_LENGTH\n", "    tokens.extend([-tokenizer.eod_id] * (MAX_LENGTH - len(tokens)))\n", "    assert len(tokens) == MAX_LENGTH\n", "    samples.append(tokens)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "random.shuffle(samples)\n", "\n", "training_samples = samples[500:]\n", "validation_samples = samples[:500]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "traininig 90423\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "validation 500\n"]}], "source": ["import torch\n", "from megatron.data import indexed_dataset\n", "\n", "output_dir = Path(\"/mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "\n", "def generate(data, name):\n", "    path = str(output_dir / name)\n", "    builder = indexed_dataset.make_builder(\n", "        path + \".bin\",\n", "        impl=\"mmap\",\n", "        vocab_size=tokenizer.vocab_size,\n", "    )\n", "\n", "    for sample in data:\n", "        builder.add_item(torch.tensor(sample))\n", "\n", "    builder.finalize(path + \".idx\")\n", "\n", "    ds = indexed_dataset.make_dataset(\n", "        path, impl=\"mmap\", skip_warmup=True\n", "    )\n", "    print(name, len(ds))\n", "\n", "\n", "generate(training_samples, \"traininig\")\n", "generate(validation_samples, \"validation\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"data": {"text/plain": ["128009"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from megatron.tokenizer.tokenizer import LLama3InstructTokenizer\n", "\n", "MAX_LENGTH = 8192 + 1\n", "\n", "tokenizer = LLama3InstructTokenizer()\n", "\n", "tokenizer.eod_id"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from megatron.tokenizer.tokenizer import LLama3InstructTokenizer\n", "\n", "MAX_LENGTH = 8192\n", "\n", "tokenizer = LLama3InstructTokenizer()\n", "\n", "samples = []\n", "\n", "for tokenized_prompt, output in zip(tokenized_prompts, outputs):\n", "    tokenized_output = tokenizer.tokenize(output) + [tokenizer.eod_id]\n", "    tokens = [-x for x in tokenized_prompt] + tokenized_output\n", "    assert len(tokens) < MAX_LENGTH\n", "    tokens.extend([-tokenizer.eod_id] * (MAX_LENGTH - len(tokens)))\n", "    assert len(tokens) == MAX_LENGTH\n", "    samples.append(tokens)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "random.shuffle(samples)\n", "\n", "calibration_samples = samples[:2000]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "calibration 2000\n"]}], "source": ["import torch\n", "from megatron.data import indexed_dataset\n", "\n", "output_dir = Path(\"/mnt/efs/augment/user/yury/speculative_decoding/llama3/binks_v4_llama3_70b_L8192\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "\n", "def generate(data, name):\n", "    path = str(output_dir / name)\n", "    builder = indexed_dataset.make_builder(\n", "        path + \".bin\",\n", "        impl=\"mmap\",\n", "        vocab_size=tokenizer.vocab_size,\n", "    )\n", "\n", "    for sample in data:\n", "        builder.add_item(torch.tensor(sample))\n", "\n", "    builder.finalize(path + \".idx\")\n", "\n", "    ds = indexed_dataset.make_dataset(\n", "        path, impl=\"mmap\", skip_warmup=True\n", "    )\n", "    print(name, len(ds))\n", "\n", "\n", "generate(calibration_samples, \"calibration\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
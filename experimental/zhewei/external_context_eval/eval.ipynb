{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "from base.augment_client.client import AugmentClient\n", "from experimental.zhewei.external_context_eval.data import get_eval_dataset\n", "from experimental.zhewei.external_context_eval.utils import get_api_token\n", "from research.infra.cfg.clusters import Clusters\n", "\n", "# Evaluation settings\n", "AUGMENT_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "AUGMENT_TOKEN = get_api_token(namespace=Clusters.load_current().main_namespace)\n", "\n", "CHAT_MODEL_NAME = \"binks-v11\"\n", "EVAL_MODEL_NAME = \"gemini-1-5-pro-001-chat\"\n", "\n", "EVAL_IDS = []  # empty list means to evaluate all\n", "VERBOSE = False\n", "\n", "\n", "client = AugmentClient(\n", "    url=AUGMENT_URL,\n", "    token=AUGMENT_TOKEN,\n", "    user_agent=\"Augment-EvalHarness/external-context\",\n", ")\n", "\n", "chat_model = client.client_for_model(CHAT_MODEL_NAME)\n", "eval_model = client.client_for_model(EVAL_MODEL_NAME)\n", "\n", "print(\"Available models:\")\n", "model_names = sorted([m.name for m in client.get_models().models])\n", "for name in model_names:\n", "    print(f\"  - {name}\")\n", "print(\"=\" * 50)\n", "\n", "print(\"Available external source types:\")\n", "for src in client.list_external_source_types():\n", "    print(f\"  - {src}\")\n", "print(\"=\" * 50)\n", "\n", "eval_dataset = get_eval_dataset()\n", "total_count = len(eval_dataset)\n", "print(f\"There are {total_count} eval data points.\")\n", "\n", "counter = Counter([\"|\".join(dp.docsets) for dp in eval_dataset])\n", "for k, v in counter.most_common():\n", "    print(f\"  - {k}: {v} ({v/total_count*100}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from base.augment_client.client import ChatResponse, ExternalSource, UploadContent\n", "import experimental.zhewei.external_context_eval.utils as utils\n", "\n", "if EVAL_IDS:\n", "    ds = [dp for dp in eval_dataset if dp.id in EVAL_IDS]\n", "else:\n", "    ds = eval_dataset\n", "\n", "print(f\"Chat model = {CHAT_MODEL_NAME}\")\n", "print(f\"Eval model = {EVAL_MODEL_NAME}\")\n", "\n", "# each result is a [model_resp, eval_resp] sequence\n", "results_wo: List[List[ChatResponse]] = []\n", "results_w: List[List[ChatResponse]] = []\n", "scores_compare: List[int] = []\n", "\n", "for dp in ds:\n", "    if VERBOSE:\n", "        print(\"=\" * 80)\n", "        print(f\"[ID {dp.id}] Question: {dp.message}\")\n", "        print(\"-\" * 80)\n", "        print(f\"Suggested answer: {dp.answer}\")\n", "        print(\"-\" * 80)\n", "    \n", "    blob_name, blobs, user_guided_blobs = \"\", None, None\n", "    if dp.content != \"\":\n", "        assert dp.path != \"\"\n", "        upload_content = UploadContent(\n", "            content=dp.content,\n", "            path_name=dp.path,\n", "        )\n", "        blob_names = client.batch_upload(blobs=[upload_content])\n", "        assert len(blob_names) == 1\n", "        blobs = {\n", "            \"added_blobs\": [blob_names[0]],\n", "            \"deleted_blobs\": [],\n", "            \"checkpoint_id\": None,\n", "        }\n", "        user_guided_blobs = [blob_names[0]]\n", "    \n", "    model_resp_wo, eval_resp_wo = utils.evaluate_data_point(\n", "        chat_model, eval_model, dp, blobs, user_guided_blobs, external_srcs=[])\n", "    results_wo.append([model_resp_wo, eval_resp_wo])\n", "\n", "    external_srcs: List[ExternalSource] = []\n", "    for docset in dp.docsets:\n", "        external_srcs += client.search_external_sources(\n", "            query=docset,\n", "            source_types=[\"DOCUMENTATION_SET\"],\n", "        )\n", "    \n", "    model_resp_w, eval_resp_w = utils.evaluate_data_point(\n", "        chat_model, eval_model, dp, blobs, user_guided_blobs, external_srcs=external_srcs)\n", "    results_w.append([model_resp_w, eval_resp_w])\n", "    \n", "    comp_msg = utils.get_comp_resp_msg(dp.answer, dp.keywords, model_resp_wo.text, model_resp_w.text)\n", "    eval_resp_comp = eval_model.chat(selected_code=\"\", message=comp_msg, prefix=\"\", suffix=\"\", path=\"\")\n", "    scores_compare.append(utils.get_score_from_resp(eval_resp_comp))\n", "    \n", "    score_wo = utils.get_score_from_resp(results_wo[-1][1])\n", "    score_w = utils.get_score_from_resp(results_w[-1][1])\n", "    print(f\"{dp.id}: w/o={score_wo} | w/={score_w} | compare={scores_compare[-1]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# format for easy copyig to spreadsheet\n", "for id in range(len(results_wo)):\n", "    s1 = utils.get_score_from_resp(results_wo[id][1])\n", "    s2 = utils.get_score_from_resp(results_w[id][1])\n", "    s3 = scores_compare[id]\n", "    print(f\"{s1},{s2},{s3}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for id in range(len(results_wo)):\n", "    docset = eval_dataset[id].docsets[0]\n", "    print(f\"id={id+1} | docset={docset} | compare={scores_compare[id]}\")\n", "    \n", "    rid = results_wo[id][0].request_id\n", "    score = utils.get_score_from_resp(results_wo[id][1])\n", "    print(f\"w/o={score} | request_id={rid}\")\n", "    \n", "    rid = results_w[id][0].request_id\n", "    score = utils.get_score_from_resp(results_w[id][1])\n", "    print(f\"w/ ={score} | request_id={rid}\")\n", "    print(\"=\" * 80)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
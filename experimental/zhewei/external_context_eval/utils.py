"""Utilities for external context evaluation."""

import os
from typing import List

from base.augment_client.client import AugmentModelClient, ChatResponse, ExternalSource
from experimental.zhewei.external_context_eval.data import ExternalContextEvalData
from research.core.augment_secrets import get_k8s_secret


def get_api_token(
    secret_name: str = "eval-external-context-bot-token",
    namespace: str = "tenant-augment-eng",
):
    config_path = ""
    for path in os.environ.get("KUBECONFIG", "").split(":"):
        if os.path.exists(path):
            config_path = path
            break

    return get_k8s_secret(
        name=secret_name,
        field="api-token",
        namespace=namespace,
        config_path=config_path,
    )


def get_eval_resp_msg(
    suggested_answer: str,
    keywords: List[str],
    model_resp: str,
) -> str:
    description = "Evaluate the Response against the provided Answer and Keywords, focusing on accuracy, completeness, and attention to detail. Assign a score from 0 to 10, with 10 being perfect."
    resp_format = "Begin with the score followed by a period. Then provide a concise explanation of the reasoning behind the score."
    print_keywords = "\n".join(keywords)
    message = f"{description} {resp_format}\n\n* Answer:\n{suggested_answer}\n\n* Keywords:\n{print_keywords}\n\n* Response:\n{model_resp}\n"
    return message


def get_comp_resp_msg(
    suggested_answer: str,
    keywords: List[str],
    model_resp_a: str,
    model_resp_b: str,
) -> str:
    description = "Compare the two Responses against the provided Answer and Keywords, focusing on accuracy, completeness, and attention to detail. Assign a score between -5 and 5, with negative scores favoring Response A and positive scores favoring Response B. The absolute value of the score reflects the strength of the preference, with 0 indicating no difference."
    resp_format = "Begin with the score followed by a period. Then provide a concise explanation of the reasoning behind the score."
    print_keywords = "\n".join(keywords)
    message = f"{description} {resp_format}\n\n* Answer:\n{suggested_answer}\n\n* Keywords:\n{print_keywords}\n\n* Response A:\n{model_resp_a}\n\n* Response B:\n{model_resp_b}\n"
    return message


def evaluate_data_point(
    chat_model: AugmentModelClient,
    eval_model: AugmentModelClient,
    dp: ExternalContextEvalData,
    blobs,
    user_guided_blobs,
    external_srcs: List[ExternalSource],
    verbose: bool = False,
) -> List[ChatResponse]:
    model_resp = chat_model.chat(
        selected_code=dp.selected_code,
        message=dp.message,
        prefix=dp.content,
        suffix="",
        path=dp.path,
        blobs=blobs,
        user_guided_blobs=user_guided_blobs,
        context_code_exchange_request_id="new",
        external_source_ids=[src.id for src in external_srcs],
    )

    if verbose:
        if external_srcs:
            print("WITH external sources:")
            for src in external_srcs:
                print(src.id, "|", src.name, "|", src.title, "|", src.source_type)
            print("-" * 80)
        else:
            print("WITHOUT external sources:")
        print(model_resp.text)
        print("-" * 80)
        print(f"^ RI: {model_resp.request_id}")
        print("-" * 80)

    eval_resp_msg = get_eval_resp_msg(
        dp.answer,
        dp.keywords,
        model_resp.text,
    )
    eval_resp = eval_model.chat(
        selected_code="",
        message=eval_resp_msg,
        prefix="",
        suffix="",
        path="",
    )

    return [model_resp, eval_resp]


def get_score_from_resp(resp: ChatResponse) -> int:
    return int(resp.text.split(sep=".", maxsplit=2)[0].strip(" `^#*"))

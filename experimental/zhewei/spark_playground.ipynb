{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import random\n", "import string\n", "\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import get_local_session, k8s_session\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "MODE = \"k8s_cpu\"\n", "ACCESS_GCP = True\n", "\n", "if MODE == \"local\":\n", "    spark = get_local_session(\n", "        access_gcp=ACCESS_GCP,\n", "    )\n", "elif <PERSON> == \"k8s_cpu\":\n", "    spark = k8s_session(\n", "        max_workers=4,\n", "        access_gcp=ACCESS_GCP,\n", "    )\n", "elif <PERSON> == \"k8s_big_cpu\":\n", "    spark = k8s_session(\n", "        max_workers=32,\n", "        conf={\n", "            \"spark.executor.cores\": \"8\",\n", "            \"spark.executor.memory\": \"64G\",\n", "        },\n", "        access_gcp=ACCESS_GCP,\n", "    )\n", "elif <PERSON> == \"k8s_gpu\":\n", "    spark = k8s_session(\n", "        max_workers=2,\n", "        gpu_type=\"A100_NVLINK_80GB\",\n", "        # gpu_type=\"nvidia-h100-80gb\",\n", "        gpu_count=1,\n", "        access_gcp=ACCESS_GCP,\n", "    )\n", "else:\n", "    raise ValueError(f\"Unknown mode {MODE}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Test1: read parquet data from shared drive\\n\")\n", "\n", "data = spark.read.parquet(\"/mnt/efs/spark-data/example.parquet\")\n", "data.show(5)\n", "\n", "#=====#\n", "\n", "print(\"Test2: read data and run udf with augment dependency\\n\")\n", "\n", "@F.udf(\"array<string>\")\n", "def split_str_udf(text):\n", "    from research.core.constants import AUGMENT_ROOT\n", "    return [str(AUGMENT_ROOT)] + text.split(\"/\")\n", "\n", "data = spark.read.json(\"/mnt/efs/spark-data/shared/gh_pr_issues/query_output_fixed\")\n", "data.select(split_str_udf(<PERSON>.col(\"repo_name\"))).show(5)\n", "\n", "#=====#\n", "\n", "print(\"Test3: independently generate random data and run udf\\n\")\n", "\n", "num_rows = 100\n", "num_cols = 3\n", "\n", "df_int = spark.range(num_rows).withColumn(\"int_col\", (F.rand() * 100).cast(\"int\"))\n", "df_float = df_int.withColumn(\"float_col\", <PERSON><PERSON>rand())\n", "\n", "def random_string(length):\n", "    letters = string.ascii_lowercase\n", "    return ''.join(random.choice(letters) for _ in range(length))\n", "\n", "random_strings = [random_string(5) for _ in range(num_rows)]\n", "df = df_float.withColumn(\"string_col\", <PERSON><PERSON>concat(F.lit(\"str_\"), F.col(\"id\").cast(\"string\")))\n", "\n", "df.show(5)\n", "\n", "#=====#\n", "\n", "print(\"Test4: read data from s3\\n\")\n", "\n", "data = spark.read.parquet(\"s3a://starcoder/raw/lang=python/\")\n", "data.show(5)\n", "\n", "#=====#\n", "\n", "print(\"Test5: read data from GCS\\n\")\n", "\n", "data = spark.read.parquet(\"gs://bigquery-data-staging/starcoder/lang=python\")\n", "data.show(5)\n", "\n", "#=====#\n", "\n", "print(\"Test6: read data from BigQuery\\n\")\n", "\n", "data = (\n", "\tspark.read\n", "\t.format(\"bigquery\")\n", "\t.option(\"table\", \"augment-387916.aug_stack.lang_ids\")\n", "\t.load()\n", ")\n", "data.show(5)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
#!/bin/bash
set -e

# set CWD to augment root
cd "$(dirname "$0")"/../..

# Get current date in YYYY-MM-DD format
CURRENT_DATE=$(date +"%Y-%m-%d")

function generate_report() {
    local channel="$1"
    local notify_channel="$2"
    local output_dir="/mnt/efs/augment/public_html/feedback-reports/$channel"
    local report_filename="${CURRENT_DATE}.html"
    local report_url="${BASE_URL}/${channel}/${report_filename}"
    echo "Generating report for channel $channel: $report_filename"
    python3 experimental/slack_feedback_summarizer/slack_feedback_summarizer.py \
        --channel $channel \
        --output $output_dir/$report_filename
    # NOTE(arun): our slack token doesn't currently allow sending slack messages, so
    # disabling this for now.
        # --notify-channel $notify_channel
    ln -s $output_dir/latest.html $output_dir/$report_filename
}

generate_report "feedback-agents-extension" "team-agents"
generate_report "feedback-agents" "team-agents"
generate_report "feedback-chat-extension" "team-chat"
generate_report "feedback-chat-extension" "team-chat"
generate_report "feedback-remote-agent" "team-remote-agent"

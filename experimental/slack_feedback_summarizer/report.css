/*
 * Slack Feedback Summarizer Report Styles
 * Custom CSS to enhance the Bootstrap styling
 */

:root {
  --slack-purple: #4A154B;
  --slack-blue: #36C5F0;
  --slack-green: #2EB67D;
  --slack-yellow: #ECB22E;
  --slack-red: #E01E5A;

  --category-ui-ux-issue: #f0ad4e;
  --category-model-quality-issue: #5bc0de;
  --category-stability-issue: #d9534f;
  --category-other: #777777;
}

body {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header styling */
h1 {
  color: var(--slack-purple);
  border-bottom: 3px solid var(--slack-blue);
  padding-bottom: 10px;
  margin-bottom: 30px;
}

h2 {
  color: #333;
  margin-top: 30px;
  margin-bottom: 20px;
}

h3 {
  color: #444;
}

/* Card styling */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  border: none;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  background-color: #f5f5f5;
  padding: 15px;
}

.card-body {
  padding: 20px;
}

/* Thread card styling */
.thread-card {
  transition: all 0.3s ease;
}

.thread-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Category background colors */
.bg-category-ui-ux-issue {
  background-color: var(--category-ui-ux-issue);
  color: white;
}

.bg-category-model-quality-issue {
  background-color: var(--category-model-quality-issue);
  color: white;
}

.bg-category-stability-issue {
  background-color: var(--category-stability-issue);
  color: white;
}

.bg-category-other {
  background-color: var(--category-other);
  color: white;
}

/* Badge styling */
.badge {
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 12px;
}

/* Button styling */
.btn-link {
  color: #333;
  text-decoration: none;
  width: 100%;
  text-align: left;
  padding: 0;
}

.btn-link:hover, .btn-link:focus {
  color: var(--slack-purple);
  text-decoration: none;
}

.btn-outline-secondary {
  border-color: #ddd;
  color: #666;
}

.btn-outline-secondary:hover {
  background-color: #f5f5f5;
  color: #333;
  border-color: #ccc;
}

/* Table of contents styling */
.list-group-item {
  border-radius: 8px !important;
  margin-bottom: 5px;
  border: 1px solid #eee;
  transition: all 0.2s ease;
}

.list-group-item:hover {
  background-color: #f5f5f5;
  transform: translateX(5px);
}

.list-group-item-action {
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .card-header {
    padding: 10px;
  }

  .card-body {
    padding: 15px;
  }
}

/* Print styles */
@media print {
  body {
    background-color: white;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .collapse {
    display: block !important;
    height: auto !important;
  }

  .btn-outline-secondary {
    display: none;
  }
}

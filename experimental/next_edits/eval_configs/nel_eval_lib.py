"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

import copy
import os
import subprocess
import tempfile
from datetime import date
from pathlib import Path

import yaml

from research.core.constants import AUGMENT_ROOT
from research.fastbackward.utils import combine_dict, unflatten_dict

CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

template: dict = yaml.safe_load("""
system:
  name: next_edit_location
  group_by_path: False
  filter_input_ranges: True
  retriever:
    scorer:
      name: dense_scorer_v2_fbwd
      checkpoint_path: FILL-ME
      tokenizer_name: starcoder
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: "next_edit_location_query"
      tokenizer: starcoder
    document_formatter:
      name: base:ethanol6-embedding-with-path-key
      tokenizer: starcoder
      max_tokens: 999

task:
  name: next_edit_location
  limit_examples: 1000

podspec: A40.yaml
determined:
  name: NextEditLocation
  workspace: Dev
  project: arun
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
""")


def run_eval(name: str, config: dict, local: bool = False):
    """Run the evaluation."""

    name = name.replace(" ", "-")
    config["determined"]["name"] = "NextEditEvaluation " + name

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        print(f"Running {name}: ")
        print(yaml.dump(config))

        yaml.dump(config, f)
        f.flush()
        f.close()

        proc = subprocess.run(
            "python3 research/eval/eval.py "
            f"{'--local' if local else ''} "
            f"--job_root /mnt/efs/augment/eval/next_edits/{name} "
            f"{f.name}",
            shell=True,
            check=False,
            capture_output=True,
            cwd=AUGMENT_ROOT,
        )
        output = proc.stdout.decode("utf-8").strip()
        print(output)
        url = output.splitlines()[-1].strip()
        assert url.startswith("https://"), url
        # Delete the temp file.
        os.unlink(f.name)

    return url


def get_config(base_config: dict = template, *deltas: dict) -> dict:
    """Get the config."""
    ret = copy.deepcopy(base_config)
    for config_delta in deltas:
        config_delta = unflatten_dict(config_delta)
        ret = combine_dict(ret, config_delta)
    return ret


MODELS = (
    {
        "starethanol_gbp": {
            "system.group_by_path": True,
            "system.retriever.scorer": {
                "name": "dense_scorer_v2_fbwd_neox",
                "checkpoint_path": str(
                    CHECKPOINT_ROOT
                    / "star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000"
                ),
            },
            "system.retriever.query_formatter": {
                "name": "base:ethanol6.16.1-query-embedding",
                "max_tokens": 1024,
                "tokenizer": "starcoder",
            },
        },
        "starethanol": {
            "system.retriever.scorer": {
                "name": "dense_scorer_v2_fbwd_neox",
                "checkpoint_path": str(
                    CHECKPOINT_ROOT
                    / "star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000"
                ),
            },
            "system.retriever.query_formatter": {
                "name": "base:ethanol6.16.1-query-embedding",
                "max_tokens": 1024,
                "tokenizer": "starcoder",
            },
        },
        # Archived because they are pre-bug fix.
        # "raven_v5": {
        #     "system.retriever.scorer.checkpoint_path": str(
        #         RAVEN_ROOT / "archive/raven1b.v5-bs4x4x16-lr2e-05-iters2000-K128-onepos"
        #     )
        # },
        # "raven_v7_tied": {
        #     "system.retriever.scorer.checkpoint_path": str(
        #         RAVEN_ROOT / "archive/raven1b.v7-onepos-tied-bs1x16x16-lr2e-05-iters2000-K128"
        #     )
        # },
        # "raven_v7_dual": {
        #     "system.retriever.scorer.checkpoint_path": str(
        #         RAVEN_ROOT / "archive/raven1b.v7-onepos-dual-bs1x16x16-lr2e-05-iters2000-K128"
        #     )
        # },
        "raven_v6_query": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v6-query-1pos-bs8x2x16-lr2e-05-iters2000-K128"
            )
        },
        "raven_v6_tied": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v6-tied-1pos-bs1x2x16-lr2e-05-iters2000-K128"
            )
        },
        "raven_v7_query": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v7-query-1pos-bs8x2x16-lr2e-05-iters2000-K128"
            )
        },
        "raven_v9f_query": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v9f-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            )
        },
        "raven_v9f_tied": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v9f-tied-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            )
        },
        "raven_v11_query_nofir": {
            "system.filter_input_ranges": False,
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v11-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            ),
        },
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_query": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / f"raven1b.v{i}-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            )
        }
        for i in range(10, 15 + 1)
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_tied-step600": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT
                / f"raven1b.v{i}-tied-1pos-bs1x8x16-lr2e-05-iters2000-K128-step600"
            )
        }
        for i in range(14, 15 + 1)
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_tied": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / f"raven1b.v{i}-tied-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            )
        }
        for i in range(14, 15 + 1)
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_query": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT
                / f"raven1b.v{i}-query-1pos-doc1792-bs1x8x16-lr2e-05-iters2000-K128"
            )
        }
        for i in range(16, 18 + 1)
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_tied-step600": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT
                / f"raven1b.v{i}-tied-1pos-doc1792-bs1x8x16-lr2e-05-iters2000-K128-step600"
            )
        }
        for i in range(16, 18 + 1)
    }
    | {
        # These are all forumulaic.
        f"raven_v{i}_tied": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT
                / f"raven1b.v{i}-tied-1pos-doc1792-bs1x8x16-lr2e-05-iters2000-K128"
            )
        }
        for i in range(16, 18 + 1)
    }
    | {
        key: {"system.retriever.scorer.checkpoint_path": str(RAVEN_ROOT / key)}
        for key in [
            "raven1b.query.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
            "raven1b.query.S1.2_prs_2k.keepmost.filter.empty10,R1.1_uniform-128,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
            "raven1b.tied.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
        ]
    }
)

DATASETS = {
    "prs.v7": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst"
    },
    "manual.v2": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/next_edits/manual.v2.jsonl.zst"
    },
    "manual.v3": {
        "task.diffs_path": "/mnt/efs/augment/data/eval/next_edits/manual.v3.diffs.jsonl.zst",
        "task.files_path": "/mnt/efs/augment/data/eval/next_edits/manual.v3.files.jsonl.zst",
    },
    "manual.v3.no_instructions": {
        "task.diffs_path": "/mnt/efs/augment/data/eval/next_edits/manual.v3.diffs.jsonl.zst",
        "task.files_path": "/mnt/efs/augment/data/eval/next_edits/manual.v3.files.jsonl.zst",
        "task.drop_instructions": True,
    },
}


def run_sweep(configs: dict[str, list[dict]]):
    urls = {}
    for name, config in configs.items():
        config = get_config(template, *config)
        url = run_eval(name, config)
        urls[name] = url

    print("Name URL")
    for name, url in urls.items():
        print(f"{name} {url}")


if __name__ == "__main__":
    # This is an example.
    datasets = ["manual.v2"]
    models = ["raven_v16_query"]
    run_sweep(
        {
            f"{dataset_name}-{model_name}-{date.today()}": [
                DATASETS[dataset_name],
                MODELS[model_name],
            ]
            for dataset_name in datasets
            for model_name in models
        }
    )

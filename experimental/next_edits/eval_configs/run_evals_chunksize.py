"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from datetime import date

from experimental.next_edits.eval_configs.nel_eval_lib import (
    DATASETS,
    MODELS,
    run_sweep,
)

QUERY_CONFIG = {
    "diff5": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 5,
        }
    },
    "diff10": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 10,
        }
    },
    "diff15": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 15,
        }
    },
}
CHUNKING_CONFIG = {
    "lines30-0": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 30,
            "overlap_lines": 0,
        }
    },
    "lines30-5": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 30,
            "overlap_lines": 5,
        }
    },
    "lines60-10": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 60,
            "overlap_lines": 10,
        }
    },
    "lines90-15": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 90,
            "overlap_lines": 15,
        }
    },
}


if __name__ == "__main__":
    # This is an example.
    datasets = [
        "manual.v2",
        "prs.v7",
    ]
    models = [
        # "raven_v13_query",
        "raven_v14_query",
        "raven_v15_query",
        "raven_v16_query",
    ]
    run_sweep(
        {
            f"{dataset_name}-{model_name}-{diff_name}-{chunking_name}-{date.today()}": [
                DATASETS[dataset_name],
                MODELS[model_name],
                QUERY_CONFIG[diff_name],
                chunking_config,
            ]
            for dataset_name in datasets
            for model_name in models
            if (diff_name := "diff15") in QUERY_CONFIG
            for chunking_name, chunking_config in CHUNKING_CONFIG.items()
        },
    )

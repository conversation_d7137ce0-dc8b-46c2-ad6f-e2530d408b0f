"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from datetime import date

from experimental.next_edits.eval_configs.nel_eval_lib import (
    DATASETS,
    MODELS,
    run_sweep,
)

QUERY_CONFIG = {
    "diff5": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 5,
        }
    },
    "diff10": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 10,
        }
    },
    "diff15": {
        "system.retriever.query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 15,
        }
    },
}


if __name__ == "__main__":
    # This is an example.
    datasets = [
        "manual.v2",
        "prs.v7",
    ]
    models = [
        # "raven_v13_query",
        # "raven_v14_query",
        # "raven_v15_query",
        "raven_v16_query",
    ]
    run_sweep(
        {
            f"{dataset_name}-{model_name}-{diff_name}-{date.today()}": [
                DATASETS[dataset_name],
                MODELS[model_name],
                diff_config,
            ]
            for dataset_name in datasets
            for model_name in models
            for diff_name, diff_config in QUERY_CONFIG.items()
        },
    )

"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from datetime import date
from pathlib import Path

from experimental.next_edits.eval_configs.nel_eval_lib import (
    DATASETS,
    MODELS,
    RAVEN_ROOT,
    run_sweep,
)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-c",
        "--checkpoint-paths",
        type=Path,
        nargs="*",
        help="Checkpoints to evaluate",
        default=[],
    )

    args = parser.parse_args()
    checkpoint_paths: list[Path] = [
        path if path.is_absolute() else RAVEN_ROOT / path
        for path in args.checkpoint_paths
    ]

    datasets = [
        "manual.v3",
        # "manual.v3.no_instructions",
        # "manual.v2",
        "prs.v7",
    ]
    models = [
        (model_name, MODELS[model_name])
        for model_name in [
            "starethanol",
            # "raven_v11_query",
            # "raven_v13_query",
            # "raven1b.query.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
            # "raven1b.query.S1.2_prs_2k.keepmost.filter.empty10,R1.1_uniform-128,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
            # "raven1b.tied.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,T1.1_5-15lines.downsample10.instructions50,indexed_dataset-bs1x8x32-lr2e-05-iters2000-K128",
        ]
    ] + [
        (
            checkpoint_path.name,
            {"system.retriever.scorer.checkpoint_path": str(checkpoint_path)},
        )
        for checkpoint_path in checkpoint_paths
    ]
    extra_config = {
        "system.retriever.chunker~": {
            "name": "smart_line_level",
            "max_chunk_chars": 1280,
        },
    }

    run_sweep(
        {
            f"{dataset_name}-{model_name}-sc-{date.today()}": [
                DATASETS[dataset_name],
                model_config,
                extra_config,
            ]
            for dataset_name in datasets
            for model_name, model_config in models
        }
    )

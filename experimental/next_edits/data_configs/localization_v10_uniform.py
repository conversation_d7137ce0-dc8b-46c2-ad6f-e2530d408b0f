from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    DEFAULT_OUTPUT_ROOT,
    PR_TRAIN_DATA_PATH,
    TMP_OUTPUT_ROOT,
    PRToRepoChangeConfig,
    RepoChangeToProblemConfig,
    run_create_indexed_dataset,
    run_create_retrieval_problems,
    run_format_as_tokens,
    run_pr_to_problems,
)
from research.next_edits.edit_localization_stages import (
    CreateRetrievalProblemsConfig,
    FormatAsTokensConfig,
)


def main():
    # Takes about 20-25 minutes with 32 workers.
    problems_path, spark = run_pr_to_problems(
        pr_source_path=PR_TRAIN_DATA_PATH,
        output_root=DEFAULT_OUTPUT_ROOT,
        # short_name="prs_2k.keepmost.filter",
        short_name="prs_2k.keepmost.filter",
        # NOTE(arun): Not explicitly skipping some repos because we filter them with the
        # max repo size.
        pr_config=PRToRepoChangeConfig(),
        config=RepoChangeToProblemConfig(
            max_problems_per_commit=10,
            max_problems_per_repo=1000,
            max_repo_size_files=2_000,
            wip_sampling_strategy="keep_most",
            # remove_unsupported_languages=True,
        ),
        max_workers=32,
    )
    # Takes about 16 minutes with 32 workers.
    retrieval_problems_path, spark = run_create_retrieval_problems(
        problems_source_path=problems_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        short_name="uniform-128",
        config=CreateRetrievalProblemsConfig(
            retriever_config={
                "scorer": {},
                "chunker": {
                    "name": "line_level",
                    "max_lines_per_chunk": 30,
                },
                "query_formatter": {},
                "document_formatter": {},
            },
            num_retrieved_chunks=128,
            retrieval_strategy="uniform",
            ignore_whitespace_changes=True,
        ),
        max_workers=32,
        spark=spark,
    )
    # Takes ~20 minutes
    formatted_tokens_path, spark = run_format_as_tokens(
        input_path=retrieval_problems_path,
        # The output parquet files are 100% equivalent to the indexed dataset, so they
        # can be saved in a temporary location.
        output_root=TMP_OUTPUT_ROOT,
        config=FormatAsTokensConfig(
            query_formatter_config={
                "diff_context_lines": 5,
                "max_prompt_tokens": 4096,
                "max_instruction_tokens": 512,
            },
            document_formatter_config={
                "max_content_length": 1024,
                "add_path": True,
            },
            # downsample_commits_hunk_threshold=10,
        ),
        # short_name="5lines.downsample10",
        short_name="5lines",
        max_workers=32,
        spark=spark,
    )
    indexed_dataset_output_path = run_create_indexed_dataset(
        input_path=formatted_tokens_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        tokenizer_name="starcoder",
    )
    print(f"Indexed dataset saved to: {indexed_dataset_output_path}")


if __name__ == "__main__":
    main()

from pathlib import Path

from pyspark.sql import SparkSession

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalQueryFormatterConfig,
)
from research.data.spark.pipelines.stages.next_edit_gen_retrieval_pipelines import (
    DEFAULT_OUTPUT_ROOT,
    PR_TRAIN_DATA_PATH,
    TMP_OUTPUT_ROOT,
    ScoreChunksConfig,
    run_format_as_tokens,
    run_score_chunks,
)
from research.data.spark.pipelines.utils.next_edit_common import (
    run_create_indexed_dataset,
)
from research.next_edits.edit_gen_retrieval_stages import FormatAsTokensConfig

RAG_EDIT_PROBLEMS_PATH = Path(
    "/mnt/efs/spark-data/shared/next-edit/stage2/gh_pr_train_repartitioned/S1.13.1_6000p_2000f,R1.3_edit_ethanol_synth_instruct_k128"
)
CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")

SCORE_CHUNKS_CONFIGS = {
    "smart_chunks_1280": ScoreChunksConfig(
        checkpoint_path=(
            "/mnt/efs/augment/checkpoints/"
            "next-edit-gen/S1.13.1-R1.3_edit_ethanol_synth_instruct-P1.10.1_context12-gh_pr_train_repartitioned-starcoder2_7b-ffwd"
        ),
        formatter_config=EditGenFormatterConfig(
            diff_context_lines=12,
            max_prompt_tokens=1750,
            section_budgets={
                "prefix_tks": 100,
                "suffix_tks": 100,
                "filename_tks": 50,
                "instruction_tks": 100,
                "diff_tks": 300,
                "retrieval_tks": 1000,
            },
        ),
        downsample_retrieval_rate=0.0,
        drop_instruction_rate=0.5,
        downsample_no_change_rate=0.0,
    )
}

FORMAT_AS_TOKENS_CONFIGS = {
    "diff1k_sc1": FormatAsTokensConfig(
        query_formatter_config=EditGenRetrievalQueryFormatterConfig(
            diff_context_lines=12,
            max_prompt_tokens=4096,
            section_budgets={
                "prefix_tks": 500,
                "selected_tks": 250,
                "suffix_tks": 500,
                "filename_tks": 50,
                "instruction_tks": 200,
                "diff_tks": 1000,
            },
        ),
        document_formatter_config={
            "max_content_length": 1024,
            "add_path": True,
        },
        tokenizer_name="starcoder",
    )
}


def run_pipeline(
    score_chunks_name: str,
    format_as_tokens_name: str,
    spark: SparkSession | None = None,
):
    # Takes about 16 minutes with 32 workers.
    score_chunks_config = SCORE_CHUNKS_CONFIGS[score_chunks_name]

    scored_problems_path, spark = run_score_chunks(
        input_path=RAG_EDIT_PROBLEMS_PATH,
        output_root=DEFAULT_OUTPUT_ROOT,
        short_name=score_chunks_name,
        config=score_chunks_config,
        max_workers=128,
        spark=spark,
    )

    if spark:
        spark.stop()
        spark = None

    formatted_tokens_path, spark = run_format_as_tokens(
        input_path=scored_problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name=format_as_tokens_name,
        config=FORMAT_AS_TOKENS_CONFIGS[format_as_tokens_name],
        max_workers=128,
        spark=spark,
        overwrite=True,
    )
    if spark:
        spark.stop()
        spark = None

    indexed_dataset_output_path = run_create_indexed_dataset(
        input_path=formatted_tokens_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        tokenizer_name="starcoder",
        overwrite=True,
    )

    print(f"Output saved to: {indexed_dataset_output_path}")
    return spark


def main():
    spark = None
    # spark = run_pipeline(
    #     pr_to_problems_name="prs_2k.keepmost.filter.empty10",
    #     create_retrieval_problems_name="uniform-128",
    #     format_as_tokens_name="5-15lines.downsample10.instructions50",
    # )
    spark = run_pipeline(
        score_chunks_name="smart_chunks_1280",
        format_as_tokens_name="diff1k_sc1",
        spark=spark,
    )


if __name__ == "__main__":
    main()

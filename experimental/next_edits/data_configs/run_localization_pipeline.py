from pathlib import Path

from pyspark.sql import SparkSession

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    DEFAULT_OUTPUT_ROOT,
    PR_TRAIN_DATA_PATH,
    PR_VALIDATION_DATA_PATH,
    TMP_OUTPUT_ROOT,
    PRToRepoChangeConfig,
    RepoChangeToProblemConfig,
    run_create_indexed_dataset,
    run_create_retrieval_problems,
    run_format_as_tokens,
    run_pr_to_problems,
    run_score_chunks,
)
from research.next_edits.edit_localization_stages import (
    CreateRetrievalProblemsConfig,
    EditModelScorerConfig,
    FormatAsTokensConfig,
    ScoreChunksConfig,
)

CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

PR_TO_PROBLEM_CONFIGS = {
    "prs_2k.keepmost.filter": RepoChangeToProblemConfig(
        max_problems_per_commit=10,
        max_problems_per_repo=1000,
        max_repo_size_files=2_000,
        wip_sampling_strategy="keep_most",
        remove_unsupported_languages=True,
    ),
    "prs_2k.keepmost.filter.empty10": RepoChangeToProblemConfig(
        max_problems_per_commit=10,
        max_problems_per_repo=1000,
        max_repo_size_files=2_000,
        wip_sampling_strategy="keep_most",
        remove_unsupported_languages=True,
        empty_change_chance=0.1,
    ),
}

CREATE_RETRIEVAL_PROBLEMS_CONFIGS = {
    "uniform-128": CreateRetrievalProblemsConfig(
        retriever_config={
            "scorer": {},
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {},
            "document_formatter": {},
        },
        num_retrieved_chunks=128,
        retrieval_strategy="uniform",
        ignore_whitespace_changes=True,
    ),
    "uniform-128.30-90lines": CreateRetrievalProblemsConfig(
        retriever_config={
            "scorer": {},
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {},
            "document_formatter": {},
        },
        num_retrieved_chunks=128,
        retrieval_strategy="uniform",
        ignore_whitespace_changes=True,
        chunker_configs=[
            {
                "name": "line_level",
                "max_lines_per_chunk": 30,
                "overlap_lines": 5,
            },
            {
                "name": "line_level",
                "max_lines_per_chunk": 60,
                "overlap_lines": 10,
            },
            {
                "name": "line_level",
                "max_lines_per_chunk": 90,
                "overlap_lines": 15,
            },
        ],
    ),
    "v13-128.30lines": CreateRetrievalProblemsConfig(
        retriever_config={
            "scorer": {
                "name": "dense_scorer_v2_fbwd",
                "checkpoint_path": str(
                    RAVEN_ROOT
                    / "raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
                ),
                "tokenizer_name": "starcoder",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_instruction_tokens": 0,
            },
            "document_formatter": {
                "name": "ethanol6_document",
                "max_tokens": 999,
                "add_path": True,
                "tokenizer_name": "StarCoderTokenizer",
            },
        },
        num_retrieved_chunks=128,
        # Cap how much time it takes to process a single repo.
        timeout_per_repo_s=10 * 60,
        ignore_whitespace_changes=True,
        retrieval_strategy="retrieved",
    ),
    "v13-128.smart2000": CreateRetrievalProblemsConfig(
        retriever_config={
            "scorer": {
                "name": "dense_scorer_v2_fbwd",
                "checkpoint_path": str(
                    RAVEN_ROOT
                    / "raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
                ),
                "tokenizer_name": "starcoder",
            },
            "chunker": {
                "name": "smart_line_level",
                "max_chunk_chars": 2_000,
            },
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_instruction_tokens": 0,
            },
            "document_formatter": {
                "name": "ethanol6_document",
                "max_tokens": 999,
                "add_path": True,
                "tokenizer_name": "StarCoderTokenizer",
            },
        },
        num_retrieved_chunks=128,
        # Cap how much time it takes to process a single repo.
        timeout_per_repo_s=10 * 60,
        ignore_whitespace_changes=True,
        retrieval_strategy="retrieved",
    ),
}

SCORE_CHUNKS_CONFIGS = {
    "path_distance": ScoreChunksConfig(
        scoring_strategies=("path_distance",),
    ),
    "raven_edit_S24-R4-P18": ScoreChunksConfig(
        scoring_strategies=("path_distance", "edit_model_score"),
        edit_model_scorer_config=EditModelScorerConfig(
            checkpoint_path=str(
                CHECKPOINT_ROOT
                / "next-edit-gen/S24-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_3b-ffwd"
            ),
            tokenizer_name="starcoder2",
            formatter_config=EditGenFormatterConfig(
                # Prefer a very short context for retrieval scoring.
                diff_context_lines=9,
                max_prompt_tokens=1750,
                section_budgets={
                    "prefix_tks": 1000,
                    "suffix_tks": 100,
                    "filename_tks": 50,
                    "instruction_tks": 100,
                    "diff_tks": 300,
                    "retrieval_tks": 0,
                },
            ),
        ),
    ),
}

FORMAT_AS_TOKENS_CONFIGS = {
    "5lines.downsample10": FormatAsTokensConfig(
        query_formatter_config={
            "diff_context_lines": 5,
            "max_prompt_tokens": 4096,
            "max_instruction_tokens": 512,
        },
        document_formatter_config={
            # Longer documents to cover to 95% chunks with 90 lines
            "max_content_length": 1792,
            "add_path": True,
        },
        downsample_commits_hunk_threshold=10,
        min_diff_context_lines=5,
        max_diff_context_lines=5,
        instructions_path=None,
    ),
    "5lines.downsample10.instructions50": FormatAsTokensConfig(
        query_formatter_config={
            "diff_context_lines": 5,
            "max_prompt_tokens": 4096,
            "max_instruction_tokens": 512,
        },
        document_formatter_config={
            # Longer documents to cover to 95% chunks with 90 lines
            "max_content_length": 1792,
            "add_path": True,
        },
        downsample_commits_hunk_threshold=10,
        min_diff_context_lines=5,
        max_diff_context_lines=5,
    ),
    "5-15lines.downsample10.instructions50": FormatAsTokensConfig(
        query_formatter_config={
            "diff_context_lines": 5,
            "max_prompt_tokens": 4096,
            "max_instruction_tokens": 512,
        },
        document_formatter_config={
            # Longer documents to cover to 95% chunks with 90 lines
            "max_content_length": 1792,
            "add_path": True,
        },
        downsample_commits_hunk_threshold=10,
        min_diff_context_lines=5,
        max_diff_context_lines=15,
    ),
}


def run_pipeline(
    pr_to_problems_name: str,
    create_retrieval_problems_name: str,
    score_chunks_name: str | None,
    format_as_tokens_name: str,
    spark: SparkSession | None = None,
    validation: bool = True,
):
    # Tracks if the current spark session uses a GPU.
    spark_uses_gpu = False
    # Takes about 20-25 minutes with 32 workers.
    pr_source_path = PR_VALIDATION_DATA_PATH if validation else PR_TRAIN_DATA_PATH
    problems_path, spark = run_pr_to_problems(
        pr_source_path=pr_source_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        short_name=pr_to_problems_name + (".val" if validation else ""),
        pr_config=PRToRepoChangeConfig(),
        config=PR_TO_PROBLEM_CONFIGS[pr_to_problems_name],
        max_workers=128,
        spark=spark,
    )
    # Takes about 16 minutes with 32 workers.
    retrieval_config = CREATE_RETRIEVAL_PROBLEMS_CONFIGS[create_retrieval_problems_name]

    spark_needs_gpu = retrieval_config.retrieval_strategy == "retrieved"
    if spark and (spark_uses_gpu is not spark_needs_gpu):
        # We can't reuse the spark session if we're using the retrieval stage, because
        # we need GPUs.
        spark.stop()
        spark = None
    spark_uses_gpu = spark_needs_gpu
    retrieval_problems_path, spark = run_create_retrieval_problems(
        problems_source_path=problems_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        short_name=create_retrieval_problems_name,
        config=retrieval_config,
        max_workers=128,
        spark=spark,
    )

    # Should take ~20 minutes at most.
    if score_chunks_name:
        spark_needs_gpu = (
            "edit_model_score"
            in SCORE_CHUNKS_CONFIGS[score_chunks_name].scoring_strategies
        )
        if spark and (spark_uses_gpu is not spark_needs_gpu):
            spark.stop()
            spark = None
        spark_uses_gpu = spark_needs_gpu

        scored_problems_path, spark = run_score_chunks(
            input_path=retrieval_problems_path,
            output_root=DEFAULT_OUTPUT_ROOT,
            short_name=score_chunks_name,
            config=SCORE_CHUNKS_CONFIGS[score_chunks_name],
            max_workers=128,
            spark=spark,
            overwrite=False,
        )
    else:
        scored_problems_path = retrieval_problems_path

    # Takes ~20 minutes
    spark_needs_gpu = False
    if spark and (spark_uses_gpu is not spark_needs_gpu):
        spark.stop()
        spark = None
    spark_uses_gpu = spark_needs_gpu
    formatted_tokens_path, spark = run_format_as_tokens(
        input_path=scored_problems_path,
        # The output parquet files are 100% equivalent to the indexed dataset, so they
        # can be saved in a temporary location.
        output_root=TMP_OUTPUT_ROOT,
        short_name=format_as_tokens_name,
        config=FORMAT_AS_TOKENS_CONFIGS[format_as_tokens_name],
        max_workers=128,
        spark=spark,
        overwrite=False,
    )
    indexed_dataset_output_path = run_create_indexed_dataset(
        input_path=formatted_tokens_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        tokenizer_name="starcoder",
    )
    print(f"Indexed dataset saved to: {indexed_dataset_output_path}")
    return spark


def main():
    spark = None
    for validation in [True]:  # [False, True]:
        spark = run_pipeline(
            pr_to_problems_name="prs_2k.keepmost.filter.empty10",
            create_retrieval_problems_name="v13-128.smart2000",
            score_chunks_name="raven_edit_S24-R4-P18",
            format_as_tokens_name="5-15lines.downsample10.instructions50",
            spark=spark,
            validation=validation,
        )


if __name__ == "__main__":
    main()

"""<PERSON><PERSON><PERSON> to generate a configuration and a launch model training job."""

import logging
import typing
from collections.abc import Sequence
from pathlib import Path
from typing import Any, Literal

import yaml

from research.core.constants import AUGMENT_ROOT
from research.environments.providers import ClusterName
from research.fastbackward.determined.launch import launch_fb_job
from research.fastbackward.utils import (
    combine_dict,
    parse_key_value_args,
    unflatten_dict,
)

logger = logging.getLogger(__name__)

TEMPLATE = """
determined:
  description: null
  workspace: Dev
  project: next-edit-retrieval

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: {gpus}
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: ravenr1b.{model_type}.{data_name}-bs{{train_options.batch_size}}x{{train_options.gradient_accumulation_steps}}x{gpus}-lr{{train_options.optimizer.learning_rate:.0e}}-iters{{train_options.max_iters}}-K{{train_data.documents_per_batch}}
  wandb_project: next-edit-retrieval

  components:
    model:
      $component_name: create_dual_encoder_with_tokenizer
      tokenizer: tokenizer
    loss_fn:
      $component_name: PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 1000.0
        logits_scale: 1.0
        learnable_logits_scale: True

  eval_interval: 10
  checkpoint_interval: 100

  max_epochs: -1
  train_options:
    # Total batch size should be 256 = 8 x 32
    batch_size: 1
    gradient_accumulation_steps: {grad_accumulation_steps}
    max_iters: 1000
    log_interval: 1
    grad_clip: 1.0

    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
  eval_batch_size: 8

  train_data:
    path: {train_data_path}/train
    tokenizer_name: starcoder
    documents_per_batch: 128
    max_query_tokens: 4096
    max_document_tokens: 1792

  eval_data:
    path: {eval_data_path}/dev
    tokenizer_name: starcoder
    limit: 1024
    documents_per_batch: 128
"""

ModelType = Literal["query", "tied"]

CONFIGS = {
    "query": {
        "fastbackward_args.components": {
            "model": {
                "query_model": "$query_model",
                "doc_model": "$doc_model",
                "freeze_document_model": True,
            },
            "query_model": {
                "$component_name": "neox.load_starethanol_checkpoint",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
            },
            "doc_model": {
                "$component_name": "neox.load_starethanol_checkpoint",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
            },
        }
    },
    "tied": {
        "fastbackward_args.components": {
            "model": {
                "query_model": "$query_model",
                "doc_model": "$query_model",
                "freeze_document_model": False,
            },
            "query_model": {
                "$component_name": "neox.load_starethanol_checkpoint",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
            },
        }
    },
    "8targets": {
        "fastbackward_args.train_data.max_positive_count": 8,
        "fastbackward_args.components.loss_fn.config.gold_temperature": 1,
    },
    "8targets.rel": {
        "fastbackward_args.train_data.max_positive_count": 8,
        "fastbackward_args.train_data.positive_threshold": -1,
        "fastbackward_args.components.loss_fn.config.gold_temperature": 1,
    },
}


def main(
    model_configs: Sequence[str],
    data_train_path: Path,
    data_val_path: Path | None,
    cluster: ClusterName,
    nodes: int,
    extra_args: dict[str, Any],
    script_path: Path = AUGMENT_ROOT / "research/fastbackward/train_retriever.py",
):
    """Main function."""
    if data_val_path is None:
        data_val_path = data_train_path

    config = yaml.safe_load(
        TEMPLATE.format(
            gpus=8 * nodes,
            model_type=".".join(model_configs),
            data_name=data_train_path.name,
            train_data_path=data_train_path,
            eval_data_path=data_val_path,
            grad_accumulation_steps=256 // (8 * nodes),
        )
    )
    for option in model_configs:
        config = combine_dict(config, unflatten_dict(CONFIGS[option]))

    logger.info("Running config:\n%s", yaml.dump(config))
    launch_fb_job(config, extra_args, script_path, cluster=cluster)


if __name__ == "__main__":
    import argparse

    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-m",
        "--model-configs",
        nargs="+",
        choices=list(CONFIGS.keys()),
        required=True,
        help="The model configuration options to use.",
    )
    parser.add_argument(
        "-dt", "--data-train-path", type=Path, required=True, help="Training data path."
    )
    parser.add_argument(
        "-dv",
        "--data-val-path",
        type=Path,
        default=None,
        help="Validation data path.",
    )
    parser.add_argument(
        "-c",
        "--cloud",
        choices=typing.get_args(ClusterName),
        default="CW",
        help="Cloud provider.",
    )
    parser.add_argument("-n", "--nodes", type=int, default=1, help="Number of nodes.")

    args, extra_values = parser.parse_known_args()
    extra_args = parse_key_value_args(extra_values)

    main(
        args.model_configs,
        args.data_train_path,
        args.data_val_path,
        args.cloud,
        args.nodes,
        extra_args,
    )

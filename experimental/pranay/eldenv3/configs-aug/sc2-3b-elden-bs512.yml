#
# Step 1: Training
# python research/fastbackward/determined/launch.py experimental/pranay/eldenv3/configs-aug/sc2-3b-elden-bs512.yml
#
# Step 2: Download the checkpoint from Determined (check the determined UI)
# bash research/utils/download_checkpoint.sh b3ebc9cd-058a-460e-a6ba-ebbe470350af pranay/sc2-3b-mix-bs512-s3k
#
# Step 3: Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --model-name="starcoder2-3b" \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw"
#
#
# Step 4: Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-3b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-elden-sc2/dataset"
#
# Step 5: Deploy it
#
determined:
  description: null
  workspace: Dev
  project: pranay
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 16

fastbackward_configs:
 - configs/starcoder2_3b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 3000
  warmup_iters: 200
  lr_decay_iters: 3000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-3b-fb
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-elden-sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-elden-sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-3B-MIX-BS512-S3K
  wandb_project: pranay

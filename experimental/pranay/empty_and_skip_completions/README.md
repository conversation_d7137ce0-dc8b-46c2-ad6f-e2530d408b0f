# Empty and Skip Completions

## License Filtering
For license filtering on empty and skip completions, as they may not be in the user table and therefore not exposed to the license filtering pipeline, do the following (hacky):
1. Run first part of `pranay/empty_and_skip_completions/license_filter_step1.ipynb`. This will read in JSON data of request IDs and then convert it to parquet format with tenant information from vanguard i0, and write to local {output_dir}/request_ids.
2. Edit `get_request_ids_path` in `research/vanguard/license_filter/utils.py` and comment out step 1 in `research/vanguard/license_filter/pipeline.py`.
3. Run some form of `python research/vanguard/license_filter/pipeline.py --event_type completion_host_request --env prod --date_from 2024-11-01 --date_to 2025-01-14`, which will read in the data from this parquet file and put the results in the `tmp/` bucket of `augment-research-gsc`
4. Run the second half of `pranay/empty_and_skip_completions/license_filter_step1.ipynb`. This will take in data from tmp/request_permits and write to {output_dir}/permissive_rids/ and {output_dir}/non_permissive_rids.


Link to bucket:
https://console.cloud.google.com/storage/browser/gcp-us1-spark-data/shared/vanguard/license_filter/tmp?pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&authuser=0&invt=AbtqVQ&project=augment-research-gsc

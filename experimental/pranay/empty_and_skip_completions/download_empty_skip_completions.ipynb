{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logger = logging.getLogger(__name__)\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 208 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 208 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets.tenants import get_tenant\n", "from pathlib import Path\n", "from research.core.data_paths import canonicalize_path\n", "from experimental.pranay.empty_and_skip_completions.download_empty_skip_completions import (\n", "    get_gcs_completions,\n", "    categorize_and_save_completions,\n", ")\n", "\n", "dogfood_tenant = get_tenant(\"dogfood-shard\")\n", "i0_vanguard0_tenant = get_tenant(\"i0-vanguard0\")\n", "\n", "\n", "BASE_DIR = Path(canonicalize_path(\"user/pranay/hindsight/\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dogfood_completions = get_gcs_completions(\n", "    dogfood_tenant,\n", "    model_name=\"qweldenv1-14b\",\n", "    start_time=\"2024-11-01T00:00:00Z\",\n", "    end_time=\"2024-11-14T23:59:59Z\",\n", "    not_filtered_only=True,\n", "    limit=500,\n", "    offset=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vanguard_completions = get_gcs_completions(\n", "    i0_vanguard0_tenant,\n", "    model_name=\"qweldenv1-1-14b\",\n", "    start_time=\"2024-11-01T00:00:00Z\",\n", "    end_time=\"2025-01-14T23:59:59Z\",\n", "    not_filtered_only=True,\n", "    limit=500,\n", "    offset=00000,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["categorize_and_save_completions(\n", "    completions=vanguard_completions,\n", "    start_time=\"2024-11-01T00:00:00Z\",\n", "    end_time=\"2025-01-14T23:59:59Z\",\n", "    tenant_name=\"i0-vanguard0\",\n", "    limit=50000,\n", "    offset=50000,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from base.tokenizers import create_tokenizer_by_name\n", "from typing import cast\n", "\n", "# Initialize tokenizer\n", "tokenizer: Qwen25CoderTokenizer = cast(\n", "    Qwen25CoderTokenizer, create_tokenizer_by_name(\"qwen25coder\")\n", ")\n", "eos_id = tokenizer.special_tokens.eos\n", "skip_id = tokenizer.special_tokens.skip\n", "\n", "# Categorize completions\n", "empty_completion = []\n", "skip_completion = []\n", "neither_empty_nor_skip = []\n", "\n", "# There are some completions that have skip_id in token_ids but empty skipped_suffix and suffix_replacement_text.\n", "# We will not consider these as sample-able skip completions, but will include them in the total skip count.\n", "all_skip_completions_count = 0\n", "\n", "# Keep only the completions with at least 50 blob names\n", "completions = [\n", "    completion\n", "    for completion in vanguard_completions\n", "    if len(completion.request.blob_names) >= 50\n", "]\n", "\n", "for completion in completions:\n", "    # Check for empty completions\n", "    if len(completion.response.token_ids) == 0:\n", "        assert completion.inference_response is not None\n", "        if (\n", "            len(completion.inference_response.token_ids) != 1\n", "            or completion.inference_response.token_ids[0] != eos_id\n", "        ):\n", "            logger.error(\n", "                f\"Empty completion with non-eos token_ids: {completion.request_id}\"\n", "            )\n", "            continue\n", "        empty_completion.append(completion)\n", "    # Check for skip completions\n", "    elif skip_id in completion.response.token_ids:\n", "        if (\n", "            len(completion.response.skipped_suffix) > 0\n", "            or len(completion.response.suffix_replacement_text) > 0\n", "        ):\n", "            skip_completion.append(completion)\n", "        all_skip_completions_count += 1\n", "    else:\n", "        neither_empty_nor_skip.append(completion)\n", "\n", "print(\n", "    f\"Empty completion: {len(empty_completion)}, Skip completion: {all_skip_completions_count}, Neither: {len(neither_empty_nor_skip)}\"\n", ")\n", "\n", "# Print percentage of each\n", "total = len(empty_completion) + all_skip_completions_count + len(neither_empty_nor_skip)\n", "if total > 0:\n", "    print(\n", "        f\"Empty: {len(empty_completion)/total*100:.2f}%, Skip: {all_skip_completions_count/total*100:.2f}%, Neither: {len(neither_empty_nor_skip)/total*100:.2f}%\"\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
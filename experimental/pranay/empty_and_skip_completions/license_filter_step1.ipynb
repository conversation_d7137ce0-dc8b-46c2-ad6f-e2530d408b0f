{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "SKIP = \"skip\"\n", "EMPTY = \"empty\"\n", "\n", "USING = EMPTY\n", "\n", "BASE_PATH = \"/mnt/efs/augment/user/pranay/hindsight/\"\n", "json_path = os.path.join(\n", "    BASE_PATH,\n", "    f\"datasets/vanguard_{USING}_2024-11-01_2025-01-14/i0-vanguard0/request_ids.json\",\n", ")\n", "output_dir = os.path.join(BASE_PATH, f\"{USING}_2024-11-01_2025-01-14\")\n", "\n", "print(f\"JSON Path: {json_path}\")\n", "print(f\"Output Directory: {output_dir}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import lit\n", "import json\n", "from pyspark.sql.types import StructType, StructField, StringType\n", "\n", "# Create a Spark session\n", "spark = SparkSession.builder.appName(\"ConvertJsonToParquet\").getOrCreate()\n", "\n", "# Load the JSON file which contains an array of request IDs\n", "with open(json_path, \"r\") as f:\n", "    request_ids = json.load(f)\n", "\n", "# Define the schema\n", "schema = StructType(\n", "    [\n", "        StructField(\"tenant_id\", StringType(), False),\n", "        StructField(\"tenant\", StringType(), False),\n", "        StructField(\"request_id\", StringType(), False),\n", "    ]\n", ")\n", "\n", "# Create rows with tenant information\n", "data = [\n", "    (\n", "        \"789b1a18a6970fc4de4cf3aa89a35827\",  # tenant_id\n", "        \"i0-vanguard0\",  # tenant\n", "        request_id,  # request_id\n", "    )\n", "    for request_id in request_ids\n", "]\n", "\n", "# Create DataFrame\n", "df = spark.createDataFrame(data, schema)\n", "\n", "print(f\"Number of request IDs: {df.count()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parquet_path = os.path.join(output_dir, \"request_ids\")\n", "df = df.coalesce(1)\n", "df.write.mode(\"overwrite\").option(\"compression\", \"zstd\").parquet(parquet_path)\n", "\n", "print(f\"Successfully converted JSON to Parquet at {parquet_path}\")\n", "print(f\"Converted {len(request_ids)} request IDs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stop the Spark session\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read request_permits and save permissive list and non-permissive list"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "import pyspark.sql.functions as F\n", "from research.vanguard.license_filter.utils import (\n", "    get_request_permits_path,\n", "    read_parquets,\n", ")\n", "\n", "spark = k8s_session(max_workers=8)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Subdirectory under root for temporary/intermediate data\n", "TMP_SUBDIR = \"tmp\"\n", "# Non-temporary folders\n", "REQUEST_PERMITS_DIR = \"request_permits\"\n", "PERMISSIVE_RIDS_DIR = \"permissive_rids\"\n", "NON_PERMISSIVE_RIDS_DIR = \"non_permissive_rids\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["gcs_path = f\"{get_request_permits_path(is_tmp=True, use_efs=False)}\"\n", "print(f\"Reading from {gcs_path}\")\n", "request_permits = read_parquets(spark, gcs_path)\n", "if request_permits is None:\n", "    raise ValueError(f\"No data found at {gcs_path}\")\n", "\n", "request_permits = request_permits.persist()\n", "print(f\"Total Count: {request_permits.count()}\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["permissive_path = os.path.join(output_dir, PERMISSIVE_RIDS_DIR)\n", "(\n", "    request_permits.filter(<PERSON>.col(\"is_permissive\"))\n", "    .select(F.col(\"request_id\"))\n", "    .distinct()\n", "    .repartition(1)\n", "    .write.mode(\"overwrite\")\n", "    .parquet(permissive_path)\n", ")\n", "permissive_count = spark.read.parquet(permissive_path).count()\n", "print(f\"Permissive Count: {permissive_count}\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["non_permissive_path = os.path.join(output_dir, NON_PERMISSIVE_RIDS_DIR)\n", "(\n", "    request_permits.filter(~F.col(\"is_permissive\"))\n", "    .select(F.col(\"request_id\"))\n", "    .distinct()\n", "    .repartition(1)\n", "    .write.mode(\"overwrite\")\n", "    .parquet(non_permissive_path)\n", ")\n", "non_permissive_count = spark.read.parquet(non_permissive_path).count()\n", "print(f\"Non-Permissive Count: {non_permissive_count}\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["print(f\"Sum: {permissive_count + non_permissive_count}\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Stop the Spark session\n", "spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
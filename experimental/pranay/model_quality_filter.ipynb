{"cells": [{"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.eval.harness.factories import create_model\n", "from research.models.fastbackward_models import FastBackwardLLM\n", "\n", "from base.fastforward.starcoder import model_specs\n", "from base.fastforward.starcoder import fwd_starcoder2\n", "from research.models.meta_model import GenerativeLanguageModel\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.tokenizers.tokenizer import Tokenizer\n", "from dataclasses import dataclass\n", "from typing import Any\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "import torch\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "tokenizer = create_tokenizer_by_name(\"qwen25coder\")\n", "assert isinstance(tokenizer, Qwen25CoderTokenizer)\n", "\n", "eos_token_id = tokenizer.special_tokens.eos\n", "pause_token_id = tokenizer.special_tokens.pause"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'fastbackward', 'checkpoint_path': '/mnt/efs/augment/checkpoints/pranay/sc2-eldenv4-15b-8k-bs2ks5k-quality-mps1', 'override_prompt_formatter': {'name': 'ender'}, 'override_tokenizer': 'starcoder2', 'model_parallel_size': 1, 'seq_length': 7939, 'prompt': {'max_prefix_tokens': 1024, 'max_suffix_tokens': 512, 'max_signature_tokens': 1024, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 6144, 'component_order': ['prefix', 'retrieval', 'signature', 'nearby_prefix', 'suffix'], 'context_quant_token_len': 64, 'nearby_prefix_token_len': 512, 'nearby_prefix_token_overlap': 0, 'nearby_suffix_token_len': 0, 'nearby_suffix_token_overlap': 0}}\n"]}], "source": ["model_location = (\n", "    \"/mnt/efs/augment/checkpoints/pranay/sc2-eldenv4-15b-8k-bs2ks5k-quality-mps1\"\n", ")\n", "\n", "config_elden_quality_fbw = {\n", "    \"name\": \"fastbackward\",\n", "    \"checkpoint_path\": model_location,\n", "    \"override_prompt_formatter\": {\n", "        \"name\": \"ender\",\n", "    },\n", "    \"override_tokenizer\": \"starcoder2\",\n", "    \"model_parallel_size\": 1,\n", "    \"seq_length\": 7939,\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1024,\n", "        \"max_suffix_tokens\": 512,\n", "        \"max_signature_tokens\": 1024,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 6144,\n", "        \"component_order\": [\n", "            \"prefix\",\n", "            \"retrieval\",\n", "            \"signature\",\n", "            \"nearby_prefix\",\n", "            \"suffix\",\n", "        ],\n", "        \"context_quant_token_len\": 64,\n", "        \"nearby_prefix_token_len\": 512,\n", "        \"nearby_prefix_token_overlap\": 0,\n", "        \"nearby_suffix_token_len\": 0,\n", "        \"nearby_suffix_token_overlap\": 0,\n", "    },\n", "}\n", "model = create_model(config_elden_quality_fbw)\n", "assert isinstance(model, FastBackwardLLM)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/augment/research/fastbackward/inference.py:40: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  state_dict = torch.load(checkpoint_file, map_location=\"cpu\")\n"]}], "source": ["# load the model\n", "model.load()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "\n", "fp = \"/mnt/efs/augment/data/prism/completion/qwelden/2024-11-20_2024-11-30_qwelden/data_dogfood-shard.jsonl.zst\"\n", "data = read_jsonl_zst(fp)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2672\n"]}], "source": ["from datetime import datetime\n", "from collections import defaultdict\n", "\n", "by_day = defaultdict(list)\n", "for d in data:\n", "    timestamp = d[\"request\"][\"timestamp\"]\n", "    date = datetime.fromtimestamp(timestamp).date()\n", "    by_day[str(date)].append(d)\n", "\n", "eval_dataset = by_day[\"2024-11-22\"]\n", "print(len(eval_dataset))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9693 2152\n"]}], "source": ["yes_token = tokenizer.tokenize_safe(\"yes\")\n", "no_token = tokenizer.tokenize_safe(\"no\")\n", "\n", "assert len(yes_token) == 1\n", "assert len(no_token) == 1\n", "\n", "yes_token = yes_token[0]\n", "no_token = no_token[0]\n", "\n", "print(yes_token, no_token)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Name,\\n  }\\n<|fim_middle|>'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.detokenize(eval_dataset[40][\"response\"][\"prompt_token_ids\"][-5:])"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["'KeybindingDecor(suggestionLineRange.start,<|fim_middle|>this._bottomBackgroundGap, this._bottomBlueDecoration'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.detok<PERSON>ze(\n", "    eval_dataset[1][\"response\"][\"prompt_token_ids\"][-10:]\n", "    + eval_dataset[1][\"response\"][\"token_ids\"]\n", ")"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["6030"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["len(eval_dataset[0][\"response\"][\"prompt_token_ids\"])"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [32,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [33,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [34,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [35,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [36,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [37,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [38,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [39,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [40,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [41,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [42,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [43,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [44,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [45,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [46,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [47,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [48,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [49,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [50,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [51,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [52,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [53,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [54,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [55,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [56,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [57,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [58,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [59,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [60,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [61,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [62,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [63,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [96,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [97,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [98,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [99,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [100,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [101,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [102,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [103,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [104,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [105,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [106,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [107,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [108,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [109,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [110,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [111,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [112,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [113,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [114,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [115,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [116,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [117,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [118,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [119,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [120,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [121,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [122,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [123,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [124,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [125,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [126,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [1017,0,0], thread: [127,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [64,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [65,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [66,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [67,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [68,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [69,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [70,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [71,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [72,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [73,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [74,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [75,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [76,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [77,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [78,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [79,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [80,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [81,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [82,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [83,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [84,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [85,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [86,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [87,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [88,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [89,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [90,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [91,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [92,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [93,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [94,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [95,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [64,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [65,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [66,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [67,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [68,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [69,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [70,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [71,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [72,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [73,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [74,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [75,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [76,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [77,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [78,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [79,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [80,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [81,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [82,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [83,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [84,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [85,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [86,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [87,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [88,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [89,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [90,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [91,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [92,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [93,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [94,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [857,0,0], thread: [95,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [96,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [97,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [98,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [99,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [100,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [101,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [102,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [103,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [104,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [105,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [106,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [107,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [108,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [109,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [110,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [111,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [112,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [113,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [114,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [115,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [116,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [117,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [118,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [119,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [120,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [121,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [122,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [123,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [124,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [125,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [126,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [477,0,0], thread: [127,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [64,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [65,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [66,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [67,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [68,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [69,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [70,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [71,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [72,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [73,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [74,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [75,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [76,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [77,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [78,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [79,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [80,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [81,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [82,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [83,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [84,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [85,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [86,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [87,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [88,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [89,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [90,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [91,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [92,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [93,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [94,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "../aten/src/ATen/native/cuda/Indexing.cu:1308: indexSelectLargeIndex: block: [476,0,0], thread: [95,0,0] Assertion `srcIndex < srcSelectDimSize` failed.\n", "Process SpawnProcess-2:\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.11/multiprocessing/process.py\", line 314, in _bootstrap\n", "    self.run()\n", "  File \"/opt/conda/lib/python3.11/multiprocessing/process.py\", line 108, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"/home/<USER>/augment/research/fastbackward/inference.py\", line 80, in model_parallel_generate_func\n", "    raise e\n", "  File \"/home/<USER>/augment/research/fastbackward/inference.py\", line 76, in model_parallel_generate_func\n", "    result = model.generate(x, start_pos)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/fastbackward/model.py\", line 1168, in generate\n", "    h = layer(h, rotary_freqs, start_pos=start_pos)\n", "        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/modules/module.py\", line 1736, in _wrapped_call_impl\n", "    return self._call_impl(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/modules/module.py\", line 1747, in _call_impl\n", "    return forward_call(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/fastbackward/model.py\", line 993, in forward\n", "    attention_norm = self.attention_norm(x)\n", "                     ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/modules/module.py\", line 1736, in _wrapped_call_impl\n", "    return self._call_impl(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/modules/module.py\", line 1747, in _call_impl\n", "    return forward_call(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/modules/normalization.py\", line 217, in forward\n", "    return F.layer_norm(\n", "           ^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/nn/functional.py\", line 2900, in layer_norm\n", "    return torch.layer_norm(\n", "           ^^^^^^^^^^^^^^^^^\n", "RuntimeError: CUDA error: device-side assert triggered\n", "CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.\n", "For debugging consider passing CUDA_LAUNCH_BLOCKING=1\n", "Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.\n", "\n"]}, {"ename": "RuntimeError", "evalue": "CUDA error: device-side assert triggered\nCUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.\nFor debugging consider passing CUDA_LAUNCH_BLOCKING=1\nCompile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[49], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m sample \u001b[38;5;241m=\u001b[39m eval_dataset[idx]\n\u001b[1;32m      3\u001b[0m tokens \u001b[38;5;241m=\u001b[39m sample[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprompt_token_ids\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m+\u001b[39m sample[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtoken_ids\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m----> 4\u001b[0m logits \u001b[38;5;241m=\u001b[39m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mforward_pass_single_logits\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtensor\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtokens\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(logits\u001b[38;5;241m.\u001b[39mshape)\n\u001b[1;32m      6\u001b[0m last_token \u001b[38;5;241m=\u001b[39m logits[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[0;32m~/augment/research/models/fastbackward_models.py:228\u001b[0m, in \u001b[0;36mFastBackwardLLM.forward_pass_single_logits\u001b[0;34m(self, input_tokens)\u001b[0m\n\u001b[1;32m    222\u001b[0m     \u001b[38;5;28;01<PERSON>raise\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\n\u001b[1;32m    223\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mModel input too long: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(input_tokens)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m > \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mseq_length\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    224\u001b[0m     )\n\u001b[1;32m    226\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch\u001b[38;5;241m.\u001b[39minference_mode():\n\u001b[1;32m    227\u001b[0m     \u001b[38;5;66;03m# `model.generate` expects a (dummy) batch dimension\u001b[39;00m\n\u001b[0;32m--> 228\u001b[0m     logits \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_runner\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_tokens\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43munsqueeze\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_pos\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    229\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m logits\u001b[38;5;241m.\u001b[39msqueeze(\u001b[38;5;241m0\u001b[39m)\n", "File \u001b[0;32m~/augment/research/fastbackward/inference.py:152\u001b[0m, in \u001b[0;36mParallelTransformerRunner.generate\u001b[0;34m(self, x, start_pos)\u001b[0m\n\u001b[1;32m    150\u001b[0m     this_result \u001b[38;5;241m=\u001b[39m q\u001b[38;5;241m.\u001b[39mget()\n\u001b[1;32m    151\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(this_result, \u001b[38;5;167;01mException\u001b[39;00m):\n\u001b[0;32m--> 152\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m this_result\n\u001b[1;32m    153\u001b[0m     results\u001b[38;5;241m.\u001b[39mappend(this_result)\n\u001b[1;32m    154\u001b[0m \u001b[38;5;66;03m# Each results is the same, so copy the 0th one\u001b[39;00m\n", "\u001b[0;31mRuntimeError\u001b[0m: CUDA error: device-side assert triggered\nCUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.\nFor debugging consider passing CUDA_LAUNCH_BLOCKING=1\nCompile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[rank0]:[W123 01:10:01.720968561 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())\n"]}], "source": ["for idx in range(len(eval_dataset)):\n", "    sample = eval_dataset[idx]\n", "    tokens = sample[\"response\"][\"prompt_token_ids\"] + sample[\"response\"][\"token_ids\"]\n", "    logits = model.forward_pass_single_logits(input_tokens=torch.tensor(tokens))\n", "    print(logits.shape)\n", "    last_token = logits[-1]\n", "    print(last_token.shape)\n", "\n", "    yes_token_probs = torch.softmax(last_token, dim=-1)[yes_token].item()\n", "    no_token_probs = torch.softmax(last_token, dim=-1)[no_token].item()\n", "\n", "    print(yes_token_probs, no_token_probs)\n", "\n", "    break"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id a466fe9d-7023-4501-9597-5b2548975082.\n"]}], "source": ["# model_location = \"/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw\"\n", "# sha = \"3acca7923656f656c397dfefdba13deb286033d31a17033a83f01f98618591a1\"\n", "\n", "# model_spec = model_specs.get_starcoder2_model_spec(\n", "#     \"starcoder2-3b\", checkpoint_path=model_location, checkpoint_sha256=sha\n", "# )\n", "# step_fn = fwd_starcoder2.generate_step_fn(ms=model_spec)\n", "# attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(ms=model_spec)\n", "# attn = attn_factory(8192)  # some reason max seqlen\n", "\n", "# tokens = [1, 2, 3]  # or whatever\n", "# result = step_fn(tokens, attn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
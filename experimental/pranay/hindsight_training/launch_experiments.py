#!/usr/bin/env python3
import os
import yaml
from typing import List, Optional
import subprocess
import re
from colorama import Fore, Style
from copy import deepcopy

from research.fastbackward.determined.launch import __file__ as fb_launch_file

BASE_CONFIG = {
    "determined": {
        "description": "Qwelden-b14 model on hindsight data with {dataset_name}. Train is vanguard, eval is dogfood.",
        "workspace": "Dev",
        "project": "pranay-hindsight-training",
    },
    "augment": {"podspec_path": "8xH100.yaml", "gpu_count": 64},
    "fastbackward_configs": ["configs/qwen25coder_14b.py"],
    "fastbackward_args": {
        "loss_mask_policy": "fim",
        "batch_size": 1,
        "gradient_accumulation_steps": 8,
        "warmup_iters": 32,
        "learning_rate": 1e-5,
        "min_lr": 1e-6,
        "decay_lr": True,
        "max_epochs": 1,
        "eval_interval": 50,
        "use_activation_checkpointing": True,
        "checkpoint_optimizer_state": False,
        "loss_function": "sequence_chunked_cross_entropy",
        "checkpoint": "/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_fb",
        "tokenizer_name": "qwen25coder",
        "use_research_tokenizer": False,
        "visualize_logits_samples": 32,
        "model_parallel_size": 2,
        "use_sequence_parallel": True,
        "wandb_project": "pranay-hindsight-training",
    },
}

VANGUARD_BASE = (
    "/mnt/efs/augment/user/pranay/hindsight/vanguard_2024-11-01_2025-01-14/datasets"
)
DOGFOOD_BASE = (
    "/mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets"
)
CONFIG_BASE = "experimental/pranay/hindsight_training/configs"


def parse_block_size_from_name(dataset_name: str) -> int:
    """Extract block size from dataset name (e.g., '5824b_1024' -> 5822)."""
    match = re.match(r"(\d+)b_", dataset_name)
    if match:
        return int(match.group(1)) - 2  # Subtract 2 as per requirement
    raise ValueError(f"Could not parse block size from dataset name: {dataset_name}")


def read_block_size_from_sample(dataset_path: str) -> int:
    """Read the first sample from the dataset to determine block size."""
    import numpy as np

    sample_path = os.path.join(dataset_path, "dataset", "0.npy")
    if not os.path.exists(sample_path):
        raise FileNotFoundError(f"Could not find sample file: {sample_path}")

    sample = np.load(sample_path)
    return len(sample) - 2  # Subtract 2 as per requirement


def get_block_size(dataset_name: str, dataset_path: str, method: str = "parse") -> int:
    """Get block size using specified method."""
    if method == "parse":
        return parse_block_size_from_name(dataset_name)
    elif method == "read":
        return read_block_size_from_sample(dataset_path)
    else:
        raise ValueError(f"Unknown method: {method}")


def get_dataset_names(base_path: str) -> List[str]:
    """Get all dataset directory names from the base path."""
    return os.listdir(base_path)


def launch_experiment(
    dataset_name: str,
    version: str,
    block_size_method: str = "parse",
    dry_run: bool = False,
) -> None:
    """Launch a single experiment for the given dataset name and version."""
    config = deepcopy(BASE_CONFIG)

    # Update paths
    train_data_path = f"{VANGUARD_BASE}/{dataset_name}/dataset"
    eval_data_path = f"{DOGFOOD_BASE}/{dataset_name}/dataset"

    # Get block size
    try:
        block_size = get_block_size(
            dataset_name, VANGUARD_BASE + "/" + dataset_name, block_size_method
        )
    except Exception as e:
        raise ValueError(f"Error determining block size: {e}")

    # Update config
    config["determined"]["description"] = config["determined"]["description"].format(
        dataset_name=dataset_name
    )
    config["fastbackward_args"]["train_data_path"] = train_data_path
    config["fastbackward_args"]["eval_data_path"] = eval_data_path
    config["fastbackward_args"]["block_size"] = block_size
    config["fastbackward_args"]["run_name"] = (
        f"hindsight_{dataset_name}_qwen14b_{version}"
    )

    # Create version directory and config file
    version_dir = os.path.join(CONFIG_BASE, f"v{version}")
    os.makedirs(version_dir, exist_ok=True)

    config_path = os.path.join(version_dir, f"qwelden_{dataset_name}.yml")

    # if config file already exists, check if content is the same. If not, raise an error
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            existing_config = yaml.safe_load(f)
        if existing_config != config:
            print(Fore.RED + f"Existing config: {existing_config}" + Style.RESET_ALL)
            print(Fore.GREEN + f"New config: {config}" + Style.RESET_ALL)
            raise ValueError(
                f"Config file {config_path} already exists and has different content. Please resolve the conflict manually."
            )
        print(
            f"Config file already exists. No conflict detected in config: {config_path}"
        )
    else:
        print(f"Writing new config to: {config_path}")
        with open(config_path, "w") as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)

    # Launch experiment
    launch_cmd = [
        "python3",
        fb_launch_file,  # This is already defined at the top of the file
        "--config_file",
        config_path,
    ]
    if dry_run:
        print(f"Config file location: {config_path}")
        print(f"Would run: {' '.join(launch_cmd)}")
    else:
        print(f"Launching experiment for {dataset_name} with config: {config_path}")
        print(
            Fore.GREEN + f"Executing command: {' '.join(launch_cmd)}" + Style.RESET_ALL
        )
        subprocess.run(launch_cmd, check=True)


def main(
    dataset_names: Optional[List[str]] = None,
    version: str = "1",
    block_size_method: str = "parse",
    dry_run: bool = False,
):
    """
    Launch experiments for all specified datasets or discover them automatically.

    Args:
        dataset_names: List of dataset names to process. If None, discovers all datasets.
        version: Version string to append to experiment names.
        block_size_method: Method to determine block size ("parse" or "read")
        dry_run: If True, only print what would be done without actually launching.
    """
    all_dataset_names = sorted(
        set(get_dataset_names(VANGUARD_BASE)).union(get_dataset_names(DOGFOOD_BASE))
    )

    if dataset_names is None:
        dataset_names = all_dataset_names
    else:
        # use prefix matching to find datasets
        new_dataset_names: List[str] = []
        for prefix in dataset_names:
            matches = [name for name in all_dataset_names if name.startswith(prefix)]
            if len(matches) != 1:
                raise ValueError(
                    f"Prefix {prefix} matched {len(matches)} datasets: {matches}. Please specify exactly one dataset."
                )
            new_dataset_names.append(matches[0])
        dataset_names = new_dataset_names

    print(f"Launching experiments for {len(dataset_names)} datasets: {dataset_names}")
    print(f"Using version: v{version}")
    print(f"Block size determination method: {block_size_method}")

    for dataset_name in dataset_names:
        print(f"\nProcessing dataset: {dataset_name}")
        launch_experiment(dataset_name, version, block_size_method, dry_run)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--datasets",
        nargs="*",
        help="Specific dataset names to process. If not provided, will discover all datasets.",
    )
    parser.add_argument(
        "--version", default="1", help="Version string for the experiments"
    )
    parser.add_argument(
        "--block-size-method",
        choices=["parse", "read"],
        default="parse",
        help="Method to determine block size (parse from name or read from sample)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Print what would be done without actually launching",
    )
    args = parser.parse_args()

    main(args.datasets, args.version, args.block_size_method, args.dry_run)

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "from pathlib import Path\n", "from base.datasets.completion_dataset_gcs import CompletionDataset, Filters\n", "from base.datasets import tenants\n", "from base.datasets.completion import CompletionDatum\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "from research.core.utils_for_file import write_jsonl_zst\n", "import zstandard as zstd\n", "from colorama import Fore, Style\n", "\n", "from datetime import datetime\n", "\n", "\n", "def convert_datetime(obj):\n", "    if isinstance(obj, datetime):\n", "        return int(obj.timestamp())\n", "    return obj\n", "\n", "\n", "BASE_DIR = Path(\"/mnt/efs/augment/user/pranay/requests/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Original request datums in memory and converting to fake hindsight data (empty ground truth) for prod remote eval on opposing models."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["v1_1_jsonl = BASE_DIR / \"v1_1_requests.json\"\n", "v3_jsonl = BASE_DIR / \"v3_requests.json\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_request_ids(jsonl_file):\n", "    with open(jsonl_file, \"r\") as f:\n", "        requests = [json.loads(line) for line in f]\n", "    return [request[\"request_id\"] for request in requests]\n", "\n", "\n", "def download_completion_data(request_ids):\n", "    dataset = CompletionDataset.create_data_from_gcs(\n", "        tenant=tenants.DOGFOOD_SHARD,\n", "        filters=Filters(request_ids=request_ids),\n", "    )\n", "    dataset = list(dataset)\n", "    request_to_completion = {datum.request_id: datum for datum in dataset}\n", "    return request_to_completion\n", "\n", "\n", "def calculate_acceptance_rate(datums: list[CompletionDatum]):\n", "    accepted = 0\n", "    for datum in datums:\n", "        assert datum.resolution, f\"Missing resolution for {datum.request_id}\"\n", "        accepted += datum.resolution.accepted\n", "    total = len(datums)\n", "    print(f\"Accepted: {accepted}, total: {total}\")\n", "    return accepted / total\n", "\n", "\n", "def _convert_datetime_nested(obj):\n", "    \"\"\"Convert datetime objects to timestamps, handling nested structures.\"\"\"\n", "    if isinstance(obj, dict):\n", "        return {k: _convert_datetime_nested(v) for k, v in obj.items()}\n", "    elif isinstance(obj, list):\n", "        return [_convert_datetime_nested(item) for item in obj]\n", "    elif isinstance(obj, tuple):\n", "        return tuple(_convert_datetime_nested(item) for item in obj)\n", "    elif isinstance(obj, datetime):\n", "        return int(obj.timestamp())\n", "    return obj\n", "\n", "\n", "def write_fake_hindsight_data(datums: list[CompletionDatum], output_file: Path):\n", "    fake_hindsight_datums = [\n", "        HindsightCompletionDatum(completion=datum, ground_truth=\"\") for datum in datums\n", "    ]\n", "    fake_hindsight_dicts = [datum.to_dict() for datum in fake_hindsight_datums]\n", "    with zstd.open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "        for datum in fake_hindsight_dicts:\n", "            json.dump(_convert_datetime_nested(datum), f)\n", "            f.write(\"\\n\")\n", "\n", "\n", "class CompletionData:\n", "    def __init__(\n", "        self, request_to_completion: dict[str, CompletionDatum], model_name: str\n", "    ):\n", "        self.request_to_completion = request_to_completion\n", "        self.datums = list(request_to_completion.values())\n", "        self.model_name = model_name\n", "\n", "    def get_completion_for_request(self, request_id: str) -> CompletionDatum:\n", "        assert request_id in self.request_to_completion, f\"Missing request {request_id}\"\n", "        return self.request_to_completion[request_id]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["v3_request_to_completion: dict[str, CompletionDatum] = download_completion_data(\n", "    get_request_ids(v3_jsonl)\n", ")\n", "v3_completion_data = CompletionData(v3_request_to_completion, \"v3\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["v1_1_request_to_completion = download_completion_data(get_request_ids(v1_1_jsonl))\n", "v1_1_completion_data = CompletionData(v1_1_request_to_completion, \"v1.1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# write_fake_hindsight_data(v3_completion_data.datums, BASE_DIR / \"v3_fake_hindsight/dogfood-shard/data.jsonl.zst\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# write_fake_hindsight_data(v1_1_completion_data.datums, BASE_DIR / \"v1_1_fake_hindsight/dogfood-shard/data.jsonl.zst\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analysis replayed v3 data on v1.1 model and v1.1 data on v3 model, and construct two comparison dataset"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.core.utils_for_file import read_jsonl_zst"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class ModelComparison:\n", "    \"\"\"Data structure for comparing model performance across different datasets\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        original_request_id: str,\n", "        replayed_request_id: str,\n", "        original_generation: str,\n", "        replayed_generation: str,\n", "        original_model: str,\n", "        replayed_model: str,\n", "        original_accepted: bool,\n", "    ):\n", "        self.original_request_id = original_request_id\n", "        self.replayed_request_id = replayed_request_id\n", "        self.original_generation = original_generation\n", "        self.replayed_generation = replayed_generation\n", "        self.original_model = original_model\n", "        self.replayed_model = replayed_model\n", "        self.original_accepted = original_accepted\n", "\n", "    def is_original_filtered_out(self):\n", "        \"\"\"If the original generation is empty, it was filtered out by either the model or the post processing filters.\"\"\"\n", "        return len(self.original_generation) == 0\n", "\n", "    def is_replay_filtered_out(self):\n", "        \"\"\"If the replayed generation is empty, it was filtered out by either the model or the post processing filters.\"\"\"\n", "        return len(self.replayed_generation) == 0\n", "\n", "    def __repr__(self):\n", "        return f\"ModelComparison(original_request_id={self.original_request_id}, replayed_request_id={self.replayed_request_id}, original_generation={self.original_generation}, replayed_generation={self.replayed_generation}, original_model={self.original_model}, replayed_model={self.replayed_model}, original_accepted={self.original_accepted})\"\n", "\n", "\n", "def construct_model_comparison(\n", "    filepath: str | Path, original_completion_data: CompletionData, replayed_model: str\n", ") -> list[ModelComparison]:\n", "    results = read_jsonl_zst(filepath)\n", "    model_comparisons = []\n", "    for row in results:\n", "        assert len(row[\"artifacts\"]) == 1\n", "        assert (\n", "            len(row[\"artifacts\"][0][\"request_ids\"]) == 1\n", "            and len(row[\"artifacts\"][0][\"generated_text_per_step\"]) == 1\n", "        )\n", "        original_request_id = row[\"request_id\"]\n", "        replayed_request_id = row[\"artifacts\"][0][\"request_ids\"][0]\n", "        original_generation = original_completion_data.get_completion_for_request(\n", "            original_request_id\n", "        ).response.text\n", "        replayed_generation = row[\"generation\"]\n", "        original_resolution = original_completion_data.get_completion_for_request(\n", "            original_request_id\n", "        ).resolution\n", "        assert original_resolution, f\"Missing resolution for {original_request_id}\"\n", "        model_comparison = ModelComparison(\n", "            original_request_id=original_request_id,\n", "            replayed_request_id=replayed_request_id,\n", "            original_generation=original_generation,\n", "            replayed_generation=replayed_generation,\n", "            original_model=original_completion_data.model_name,\n", "            replayed_model=replayed_model,\n", "            original_accepted=original_resolution.accepted,\n", "        )\n", "        model_comparisons.append(model_comparison)\n", "    return model_comparisons"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["replay_v3_data_on_v1_1_model_fp = \"/mnt/efs/augment/eval/jobs/LvYMg2dh/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "replay_v3_data_on_v1_1_model_results = construct_model_comparison(\n", "    replay_v3_data_on_v1_1_model_fp,\n", "    original_completion_data=v3_completion_data,\n", "    replayed_model=\"v1.1\",\n", ")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["replay_v1_1_data_on_v3_model_fp = \"/mnt/efs/augment/eval/jobs/MH8EtgGE/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "replay_v1_1_data_on_v3_model_results = construct_model_comparison(\n", "    replay_v1_1_data_on_v3_model_fp,\n", "    original_completion_data=v1_1_completion_data,\n", "    replayed_model=\"v3\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ASIDE\n", "def empty_original_generations(model_comparisons: list[ModelComparison]):\n", "    count = 0\n", "    accepted = 0\n", "    for model_comparison in model_comparisons:\n", "        if model_comparison.is_original_filtered_out():\n", "            count += 1\n", "            accepted += model_comparison.original_accepted\n", "\n", "    print(f\"Number of empty original generations: {count}, accepted: {accepted}\")\n", "\n", "\n", "empty_original_generations(replay_v3_data_on_v1_1_model_results)\n", "empty_original_generations(replay_v1_1_data_on_v3_model_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["def calculate_rates_after_replay_filter(model_comparisons: list[ModelComparison]):\n", "    stats = {\n", "        \"num_accepted_before_filter\": 0,  # Total accepted completions before filtering\n", "        \"num_total_before_filter\": len(\n", "            model_comparisons\n", "        ),  # Total completions before filtering\n", "        \"num_accepted_after_filter\": 0,  # Accepted completions that passed the filter\n", "        \"num_total_after_filter\": 0,  # Completions that passed the filter\n", "        \"num_filtered_out\": 0,  # Completions removed by filter\n", "        \"filter_bc_replay\": 0,  # Filtered out by replay model\n", "        \"filter_bc_original\": 0,  # Filtered out by original model\n", "    }\n", "\n", "    for comparison in model_comparisons:\n", "        stats[\"num_accepted_before_filter\"] += comparison.original_accepted\n", "\n", "        if comparison.is_replay_filtered_out():\n", "            stats[\"filter_bc_replay\"] += 1\n", "        if comparison.is_original_filtered_out():\n", "            stats[\"filter_bc_original\"] += 1\n", "\n", "        if comparison.is_replay_filtered_out() or comparison.is_original_filtered_out():\n", "            stats[\"num_filtered_out\"] += 1\n", "        else:\n", "            stats[\"num_total_after_filter\"] += 1\n", "            stats[\"num_accepted_after_filter\"] += comparison.original_accepted\n", "\n", "    results = {\n", "        \"acceptance_rate_before_filter\": round(\n", "            stats[\"num_accepted_before_filter\"] / stats[\"num_total_before_filter\"], 2\n", "        ),\n", "        \"acceptance_rate_after_filter\": round(\n", "            stats[\"num_accepted_after_filter\"] / stats[\"num_total_after_filter\"], 2\n", "        ),\n", "        \"filtered_out_rate\": round(\n", "            stats[\"num_filtered_out\"] / stats[\"num_total_before_filter\"], 2\n", "        ),\n", "    }\n", "\n", "    return stats, results"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["v3_data_on_v1_1_stats, v3_data_on_v1_1_results = calculate_rates_after_replay_filter(\n", "    replay_v3_data_on_v1_1_model_results\n", ")\n", "v1_1_data_on_v3_stats, v1_1_data_on_v3_results = calculate_rates_after_replay_filter(\n", "    replay_v1_1_data_on_v3_model_results\n", ")\n", "print(f\"V3 data replayed on V1.1 model: {v3_data_on_v1_1_results}\")\n", "print(f\"V1.1 data replayed on V3 model: {v1_1_data_on_v3_results}\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["v3_data_on_v1_1_stats"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["v1_1_data_on_v3_stats"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Dict, Tu<PERSON>, List\n", "import pandas as pd\n", "\n", "\n", "@dataclass\n", "class BinConfig:\n", "    bin_size: int = 10\n", "    max_length: int = 80  # Adjust based on your data\n", "\n", "\n", "def create_length_bins(config: BinConfig) -> Dict[str, Tuple[int, int]]:\n", "    \"\"\"Create bins for length ranges, including an 'all' bin.\"\"\"\n", "    bins = {\"all\": (0, 999999)}\n", "\n", "    for start in range(0, config.max_length, config.bin_size):\n", "        end = start + config.bin_size - 1\n", "        bins[f\"{start}-{end}\"] = (start, end)\n", "\n", "    print(f\"Created {len(bins)} bins: {list(bins.keys())}\")\n", "\n", "    return bins\n", "\n", "\n", "def calculate_rates_by_length(\n", "    model_comparisons: list[ModelComparison], config: BinConfig = BinConfig()\n", "):\n", "    \"\"\"Calculate acceptance rates for different completion length bins.\"\"\"\n", "    bins = create_length_bins(config)\n", "    stats_by_bin = {}\n", "\n", "    # Initialize stats for each bin\n", "    for bin_name in bins:\n", "        stats_by_bin[bin_name] = {\n", "            \"num_accepted_before_filter\": 0,\n", "            \"num_total_before_filter\": 0,\n", "            \"num_accepted_after_filter\": 0,\n", "            \"num_total_after_filter\": 0,\n", "            \"num_filtered_out\": 0,\n", "            \"filter_bc_replay\": 0,\n", "            \"filter_bc_original\": 0,\n", "        }\n", "\n", "    # Process each comparison\n", "    for bin_name, (start, end) in bins.items():\n", "        for comparison in model_comparisons:\n", "            completion_length = len(comparison.original_generation)\n", "\n", "            # Find which bin this completion belongs to\n", "            if start <= completion_length <= end or bin_name == \"all\":\n", "                bin_stats = stats_by_bin[bin_name]\n", "\n", "                # Update stats for relevant bin\n", "                bin_stats[\"num_total_before_filter\"] += 1\n", "                bin_stats[\"num_accepted_before_filter\"] += comparison.original_accepted\n", "\n", "                if comparison.is_replay_filtered_out():\n", "                    bin_stats[\"filter_bc_replay\"] += 1\n", "                if comparison.is_original_filtered_out():\n", "                    bin_stats[\"filter_bc_original\"] += 1\n", "\n", "                if (\n", "                    comparison.is_replay_filtered_out()\n", "                    or comparison.is_original_filtered_out()\n", "                ):\n", "                    bin_stats[\"num_filtered_out\"] += 1\n", "                else:\n", "                    bin_stats[\"num_total_after_filter\"] += 1\n", "                    bin_stats[\"num_accepted_after_filter\"] += (\n", "                        comparison.original_accepted\n", "                    )\n", "\n", "    # Calculate results for each bin\n", "    results = []\n", "    for bin_name, stats in stats_by_bin.items():\n", "        if stats[\"num_total_before_filter\"] == 0:  # Skip empty bins\n", "            continue\n", "\n", "        results.append(\n", "            {\n", "                \"bin_name\": bin_name,\n", "                \"acceptance_rate_before_filter\": round(\n", "                    stats[\"num_accepted_before_filter\"]\n", "                    / stats[\"num_total_before_filter\"],\n", "                    2,\n", "                ),\n", "                \"acceptance_rate_after_filter\": round(\n", "                    stats[\"num_accepted_after_filter\"]\n", "                    / max(stats[\"num_total_after_filter\"], 1),\n", "                    2,\n", "                ),\n", "                \"filtered_out_rate\": round(\n", "                    stats[\"num_filtered_out\"] / stats[\"num_total_before_filter\"], 2\n", "                ),\n", "                \"total_samples\": stats[\"num_total_before_filter\"],\n", "            }\n", "        )\n", "\n", "    results_df = pd.DataFrame(results)\n", "\n", "    return stats_by_bin, results_df\n", "\n", "\n", "def print_binned_results(results_df, title):\n", "    print(f\"\\n{title}\")\n", "    print(results_df.to_string(index=False))"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Example usage:\n", "v3_stats, v3_results = calculate_rates_by_length(replay_v3_data_on_v1_1_model_results)\n", "v1_1_stats, v1_1_results = calculate_rates_by_length(\n", "    replay_v1_1_data_on_v3_model_results\n", ")\n", "\n", "\n", "print_binned_results(v3_results, \"V3 data on V1.1 model\")\n", "print_binned_results(v1_1_results, \"V1.1 data on V3 model\")"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# I want results on how bin compares between the two models...\n", "def calculate_delta_stats(v3_results, v1_1_results):\n", "    delta_stats = []\n", "    for _, v3_row in v3_results.iterrows():\n", "        v1_1_row = v1_1_results[v1_1_results[\"bin_name\"] == v3_row[\"bin_name\"]].iloc[0]\n", "        delta_stats.append(\n", "            {\n", "                \"bin_name\": v3_row[\"bin_name\"],\n", "                \"v3_total_samples\": v3_row[\"total_samples\"],\n", "                \"v1_1_total_samples\": v1_1_row[\"total_samples\"],\n", "                \"v3_delta_acceptance_rate_before_filter\": v3_row[\n", "                    \"acceptance_rate_before_filter\"\n", "                ]\n", "                - v1_1_row[\"acceptance_rate_before_filter\"],\n", "                \"v3_delta_acceptance_rate_after_filter\": v3_row[\n", "                    \"acceptance_rate_after_filter\"\n", "                ]\n", "                - v1_1_row[\"acceptance_rate_after_filter\"],\n", "            }\n", "        )\n", "    return pd.DataFrame(delta_stats)\n", "\n", "\n", "delta_stats = calculate_delta_stats(v3_results, v1_1_results)\n", "print_binned_results(delta_stats, \"Delta stats\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run the actual model and replay instead of using remote eval results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from research.models.meta_model import GenerationOptions\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from research.models.meta_model import RawGenerateOutput\n", "from typing import cast\n", "\n", "import contextlib\n", "import io\n", "import sys\n", "import torch\n", "import gc\n", "\n", "tokenizer: Qwen25CoderTokenizer = cast(\n", "    Qwen25CoderTokenizer, create_tokenizer_by_name(\"qwen25coder\")\n", ")\n", "st = tokenizer.special_tokens\n", "interactive_stop_tokens = [st.pause, st.eos, st.skip]\n", "\n", "checkpoint = \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_empty_skip_hindsight_ffwd\"\n", "sha_fp16 = \"c475239c06148a30504b4dc3588140908e0945100129ed671ed601a00ff79fd9\"\n", "sha_fp8 = \"a347fb6db3022f983325ce636cb28dfee1e3721e9aa3999d39e0970fae8116f1\"\n", "\n", "\n", "class ExecuteModel:\n", "    _instances = []\n", "\n", "    @contextlib.contextmanager\n", "    def _suppress_output(self):\n", "        \"\"\"Context manager to suppress stdout temporarily while keeping stderr.\"\"\"\n", "        stdout = sys.stdout\n", "        output = io.StringIO()\n", "        try:\n", "            sys.stdout = output\n", "            yield\n", "        finally:\n", "            sys.stdout = stdout\n", "\n", "    def __init__(\n", "        self,\n", "        checkpoint: str,\n", "        sha: str,\n", "        tokenizer: Qwen25CoderTokenizer,\n", "        use_fp8: bool = False,\n", "    ):\n", "        with self._suppress_output():\n", "            self.tokenizer = tokenizer\n", "            self.config = {\n", "                \"name\": \"fastforward_qwen25coder_14b\",\n", "                \"checkpoint_path\": checkpoint,\n", "                \"checkpoint_sha256\": sha,\n", "                \"sequence_length\": 8648,  # 6600,\n", "                \"use_fp8\": use_fp8,\n", "            }\n", "            from research.eval.harness.factories import create_model\n", "\n", "            self.model = None\n", "            self._model_creator = lambda: cast(\n", "                LLAMA_FastForwardModel, create_model(self.config)\n", "            )\n", "\n", "            self.options = GenerationOptions(\n", "                max_generated_tokens=256, stop_tokens=[st.eos]\n", "            )\n", "\n", "            ExecuteModel._instances.append(self)\n", "\n", "    def ensure_loaded(self):\n", "        \"\"\"Ensure this model is loaded and others are unloaded.\"\"\"\n", "        with self._suppress_output():\n", "            # Unload all other models\n", "            for instance in ExecuteModel._instances:\n", "                if instance is not self and instance.model is not None:\n", "                    instance.deep_unload()\n", "\n", "            # Load this model if needed\n", "            if self.model is None:\n", "                self.model = self._model_creator()\n", "                self.model.load()\n", "\n", "    def __call__(\n", "        self, prompt_tokens: list[int], keep_pause_tokens: bool = False\n", "    ) -> list[int]:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            generated_tokens = self.model.raw_generate_tokens(\n", "                prompt_tokens, options=self.options\n", "            ).tokens\n", "\n", "            original_length = len(generated_tokens)\n", "            generated_tokens, _ = self.extract_generation_before_stop_tokens(\n", "                generated_tokens, [st.eos]\n", "            )\n", "            generation_stopped_at_max_length = original_length == len(generated_tokens)\n", "            if generation_stopped_at_max_length and st.pause is not None:\n", "                for index in reversed(range(len(generated_tokens))):\n", "                    if generated_tokens[index] == st.pause:\n", "                        generated_tokens = generated_tokens[:index]\n", "                        break\n", "\n", "            if keep_pause_tokens:\n", "                return generated_tokens\n", "\n", "            generated_tokens = [\n", "                token for token in generated_tokens if token != st.pause\n", "            ]\n", "            return generated_tokens\n", "\n", "    def simple_call(\n", "        self, prompt_tokens: list[int], max_tokens: int = 256\n", "    ) -> RawGenerateOutput:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            options = GenerationOptions(\n", "                max_generated_tokens=max_tokens, stop_tokens=[st.eos, st.pause]\n", "            )\n", "            return self.model.raw_generate_tokens(prompt_tokens, options=options)\n", "\n", "    def forward_pass_for_logits(self, full_prompt: torch.Tensor) -> torch.Tensor:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            return self.model.forward_pass_single_logits(full_prompt)\n", "\n", "    def deep_unload(self):\n", "        with self._suppress_output():\n", "            if self.model is not None:\n", "                self.model.unload()\n", "                self.model = None\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "    def extract_generation_before_stop_tokens(\n", "        self, generated: list[int], stop_token_ids: list[int | None]\n", "    ) -> tuple[list[int], int | None]:\n", "        stop_tokens_ids_set = {\n", "            token_id for token_id in stop_token_ids if token_id is not None\n", "        }\n", "        fim_stop_token_id = None\n", "        for index in range(len(generated)):\n", "            if generated[index] in stop_tokens_ids_set:\n", "                fim_stop_token_id = generated[index]\n", "                generated = generated[:index]\n", "                break\n", "        return generated, fim_stop_token_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["v3_1_model = ExecuteModel(checkpoint, sha_fp16, tokenizer, use_fp8=False)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "replay_v3_data_on_v3_1_model_fp = \"/mnt/efs/augment/eval/jobs/LvYMg2dh/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "replay_v3_data_on_v3_1_model_results = construct_model_comparison(\n", "    replay_v3_data_on_v3_1_model_fp,\n", "    original_completion_data=v3_completion_data,\n", "    replayed_model=\"v3.1\",\n", ")\n", "\n", "results = read_jsonl_zst(replay_v3_data_on_v3_1_model_fp)\n", "for idx, row in tqdm(enumerate(results)):\n", "    completion_datum = v3_completion_data.get_completion_for_request(row[\"request_id\"])\n", "    prompt_token_ids = completion_datum.response.prompt_token_ids\n", "    assert prompt_token_ids is not None\n", "    raw_generate_output = v3_1_model.simple_call(prompt_token_ids, max_tokens=2)\n", "    generated_response_token_ids, _ = v3_1_model.extract_generation_before_stop_tokens(\n", "        raw_generate_output.tokens, [st.eos, st.pause]\n", "    )\n", "    generation_text = tokenizer.detokenize(generated_response_token_ids)\n", "    replay_v3_data_on_v3_1_model_results[idx].replayed_generation = generation_text\n", "    # print(generation_text)\n", "    # print()\n", "# replay_v3_data_on_v3_1_model_results[2]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# save v3.1 replay responses to file\n", "generated_text = []\n", "for model_comparison in replay_v3_data_on_v3_1_model_results:\n", "    generated_text.append(model_comparison.replayed_generation)\n", "with open(BASE_DIR / \"replay_v3_data_on_v3_1_model_results.txt\", \"w\") as f:\n", "    for text in generated_text:\n", "        f.write(text + \"\\n\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["v3_data_on_v3_1_stats, v3_data_on_v3_1_results = calculate_rates_after_replay_filter(\n", "    replay_v3_data_on_v3_1_model_results\n", ")\n", "print(f\"V3 data replayed on V3.1 model: {v3_data_on_v3_1_results}\")\n", "v3_data_on_v3_1_stats"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["v3_1_stats, v3_1_results = calculate_rates_by_length(\n", "    replay_v3_data_on_v3_1_model_results\n", ")\n", "print_binned_results(v3_1_results, \"V3 data on V3.1 model\")"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["print_binned_results(v3_results, \"V3 data on V1.1 model\")\n", "print_binned_results(v1_1_results, \"V1.1 data on V3 model\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
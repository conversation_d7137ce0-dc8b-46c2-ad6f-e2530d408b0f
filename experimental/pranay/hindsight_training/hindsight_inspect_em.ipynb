{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Process Data (Required for all runs)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.data.rag import hindsight_common\n", "from research.data.rag import common\n", "from base.static_analysis.parsing import SrcSpan\n", "import pickle\n", "import glob\n", "from collections import defaultdict\n", "from research.models.meta_model import RawGenerateOutput\n", "import os\n", "import pandas as pd\n", "from pathlib import Path\n", "from base.datasets.completion import RecencyInfo\n", "\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from base.prompt_format_completion.overlap import modified_chunks_filter\n", "from base.prompt_format.chunk_origin import ChunkOrigin, ChunkOriginValues\n", "\n", "\n", "from research.models.meta_model import GenerationOptions\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.tokenizers.tokenizer import Tokenizer\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "import numpy as np\n", "import gc\n", "from colorama import Fore, Style\n", "\n", "import dataclasses\n", "from typing import cast\n", "\n", "from dataclasses import dataclass\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_prompt_chunks\n", "\n", "from base.prompt_format_completion.ender_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EnderPromptFormatterConfig,\n", "    TokenApportionmentConfig,\n", "    StatelessCachingConfig,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from research.core.utils_for_file import read_jsonl, write_jsonl\n", "\n", "from base.tokenizers import tokenizer as prod_tokenizer\n", "from collections import Counter\n", "\n", "import torch\n", "from base.tokenizers import create_tokenizer_by_name\n", "from pandas import DataFrame\n", "\n", "BASE_DATA_DIR = Path(\"/mnt/efs/augment/user/pranay/hindsight/\")\n", "\n", "tokenizer: Qwen25CoderTokenizer = cast(\n", "    Qwen25CoderTokenizer, create_tokenizer_by_name(\"qwen25coder\")\n", ")\n", "st = tokenizer.special_tokens\n", "\n", "interactive_stop_tokens = [st.pause, st.eos, st.skip]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def prefix_up_to_any_delimiter(\n", "    lst: list[int], delimiter: list[int]\n", ") -> tuple[list[int], int | None]:\n", "    \"\"\"Returns the prefix up to the first occurrence of any delimiter and the delimiter.\"\"\"\n", "    for i, token in enumerate(lst):\n", "        if token in delimiter:\n", "            return lst[:i], token\n", "    return lst, None\n", "\n", "\n", "def read_parquet_files(parquet_pattern: str):\n", "    parquet_files = glob.glob(parquet_pattern)\n", "    dfs = []\n", "    sizes = {}\n", "    for file in parquet_files:\n", "        df = pd.read_parquet(file)\n", "        sizes[os.path.basename(file).split(\"-\")[1]] = len(df)\n", "        dfs.append(df)\n", "    parquet_df = pd.concat(dfs, ignore_index=True)\n", "    print(f\"Loaded {len(parquet_df)} records from Parquet files\")\n", "    # sort sizes by value\n", "    sizes = dict(sorted(sizes.items(), key=lambda item: item[1], reverse=True))\n", "    return parquet_df, sizes"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df_skip, sizes_old = read_parquet_files(\n", "    str(\n", "        BASE_DATA_DIR\n", "        / \"datasets/vanguard_skip_2024-11-01_2025-01-14/i0-vanguard0/retrieval/part-*.zstd.parquet\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df_hindsight, sizes_new = read_parquet_files(\n", "    str(\n", "        BASE_DATA_DIR\n", "        / \"datasets/vanguard_hindsight_2024-11-01_2025-01-14/i0-vanguard0/target/part-*.zstd.parquet\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mix, sizes_mix = read_parquet_files(\n", "    str(\n", "        BASE_DATA_DIR\n", "        / \"datasets/vanguard_mix_2024-11-01_2025-01-14/i0-vanguard0/mixdata/part-*.zstd.parquet\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# from research.core.utils_for_file import read_jsonl_zst\n", "# tenant = 'i1-vanguard0'\n", "# paths = [BASE_DATA_DIR / '2024-12-15_2024-12-31' / tenant / 'data.jsonl.zst', BASE_DATA_DIR / '2025-01-01_2025-01-14' / tenant / 'data.jsonl.zst']\n", "# all_data = []\n", "# for path in paths:\n", "#     results = read_jsonl_zst(path)\n", "#     print(f\"Loaded {len(results)} records from {path}\")\n", "#     all_data.extend(results)\n", "# print(f\"Loaded {len(all_data)} records in total\")\n", "\n", "# # 154502 -> 552 = 280 reduction\n", "\n", "# #6200 completions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# from research.core.utils_for_file import read_jsonl_zst\n", "# tenant = 'i0-vanguard0'\n", "# paths = [BASE_DATA_DIR / '2024-12-15_2024-12-31' / tenant / 'data.jsonl.zst', BASE_DATA_DIR / '2025-01-01_2025-01-14' / tenant / 'data.jsonl.zst']\n", "\n", "# all_data = []\n", "# for path in paths:\n", "#     results = read_jsonl_zst(path)\n", "#     print(f\"Loaded {len(results)} records from {path}\")\n", "#     all_data.extend(results)\n", "# print(f\"Loaded {len(all_data)} records in total\")\n", "\n", "# # 723178 -> 12751 -> 60 reduction"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class EnderDataPromptFormattingConfig:\n", "    \"\"\"The configuration for the <PERSON><PERSON> prompt formatter.\"\"\"\n", "\n", "    max_content_len: int\n", "    input_fraction: float\n", "    prefix_fraction: float\n", "    max_path_tokens: int\n", "\n", "    max_dense_signature_tokens: int\n", "    max_recency_retriever_tokens: int\n", "    max_diff_retriever_tokens: int\n", "    include_diff_retriever: bool\n", "\n", "    max_target_tokens: int\n", "\n", "    always_filter_chunks_by_recency: bool = True\n", "\n", "    def as_dict(self) -> dict:\n", "        \"\"\"Return configuration as a dictionary.\"\"\"\n", "        return dataclasses.asdict(self)\n", "\n", "\n", "def calculate_line_tokens(\n", "    max_content_len: int,\n", "    input_fraction: float,\n", "    signature_tokens: int,\n", "    recency_tokens: int,\n", "    diff_tokens: int,\n", "    target_tokens: int = 256,\n", ") -> int:\n", "    return round(\n", "        (max_content_len - target_tokens) * (1 - input_fraction)\n", "        - (signature_tokens + recency_tokens + diff_tokens)\n", "    )\n", "\n", "\n", "def config_name(\n", "    content_len: int,\n", "    signature_tokens: int,\n", "    recency_tokens: int,\n", "    diff_tokens: int,\n", "    line_tokens: int,\n", ") -> str:\n", "    return f\"{content_len}b_{signature_tokens}s_{recency_tokens}r_{diff_tokens}d_{line_tokens}l\"\n", "\n", "\n", "def generate_configs():\n", "    content_lens = [6304, 7328, 8352]\n", "    input_fractions = {6304: 4 / 12, 7328: 4 / 14, 8352: 4 / 16}\n", "    signature_tokens = [512, 1024]\n", "    recency_tokens = [1024, 1536]\n", "    diff_tokens = [0, 1024, 1536]\n", "\n", "    configs = {}\n", "\n", "    for content_len in content_lens:\n", "        for sig in signature_tokens:\n", "            for rec in recency_tokens:\n", "                for diff in diff_tokens:\n", "                    input_frac = input_fractions[content_len]\n", "                    line_tokens = calculate_line_tokens(\n", "                        content_len, input_frac, sig, rec, diff\n", "                    )\n", "\n", "                    name = config_name(content_len, sig, rec, diff, line_tokens)\n", "                    if line_tokens < 200:  # Skip invalid configurations\n", "                        print(f\"Skipping {name} config\")\n", "                        continue\n", "\n", "                    if diff == 0 and content_len == max(content_lens):\n", "                        print(f\"Skipping {name} config\")\n", "                        continue\n", "\n", "                    configs[name] = EnderDataPromptFormattingConfig(\n", "                        max_content_len=content_len,\n", "                        input_fraction=input_frac,\n", "                        prefix_fraction=3 / 4,\n", "                        max_path_tokens=50,\n", "                        max_dense_signature_tokens=sig,\n", "                        max_recency_retriever_tokens=rec,\n", "                        max_diff_retriever_tokens=diff,\n", "                        include_diff_retriever=diff > 0,\n", "                        max_target_tokens=256,\n", "                    )\n", "\n", "    specific_configs = [\n", "        {\"max_content_len\": 7328, \"signature\": 1364, \"recency\": 1364, \"diff\": 0},\n", "        {\"max_content_len\": 8352, \"signature\": 1024, \"recency\": 1024, \"diff\": 2048},\n", "    ]\n", "    for config in specific_configs:\n", "        content_len = config[\"max_content_len\"]\n", "        sig = config[\"signature\"]\n", "        rec = config[\"recency\"]\n", "        diff = config[\"diff\"]\n", "        input_frac = input_fractions[content_len]\n", "        line_tokens = calculate_line_tokens(content_len, input_frac, sig, rec, diff)\n", "\n", "        name = config_name(content_len, sig, rec, diff, line_tokens)\n", "\n", "        configs[name] = EnderDataPromptFormattingConfig(\n", "            max_content_len=config[\"max_content_len\"],\n", "            input_fraction=input_fractions[config[\"max_content_len\"]],\n", "            prefix_fraction=3 / 4,\n", "            max_path_tokens=50,\n", "            max_dense_signature_tokens=config[\"signature\"],\n", "            max_recency_retriever_tokens=config[\"recency\"],\n", "            max_diff_retriever_tokens=config[\"diff\"],\n", "            include_diff_retriever=config[\"diff\"] > 0,\n", "            max_target_tokens=256,\n", "        )\n", "\n", "    print(f\"Generated {len(configs)} configs\")\n", "\n", "    return configs\n", "\n", "\n", "configs = generate_configs()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["def generate_ender_prompt_fn(\n", "    tokenizer: prod_tokenizer.Tokenizer,\n", "    config: EnderDataPromptFormattingConfig,\n", "):\n", "    \"\"\"Return a row-wise function to generate <PERSON><PERSON> prompts.\"\"\"\n", "\n", "    def _generate_ender_promptprefix(\n", "        prefix: str,\n", "        prefix_begin: int,\n", "        suffix: str,\n", "        middle_spans: bytes,\n", "        file_path: str,\n", "        retrieved_chunks: str,\n", "        request_id: str,\n", "        recency_info: str | None,\n", "    ) -> dict[str, list[int] | str]:\n", "        \"\"\"Generate End<PERSON> prompts.\"\"\"\n", "\n", "        assert (\n", "            prefix is not None\n", "            and suffix is not None\n", "            and middle_spans is not None\n", "            and file_path is not None\n", "            and request_id is not None\n", "            and retrieved_chunks is not None\n", "        ), \"These fields must be non-None when generating prompts.\"\n", "\n", "        deserialized_retrieved_chunks = deserialize_retrieved_prompt_chunks(\n", "            retrieved_chunks\n", "        )\n", "        # Filter retrieved chunks based on recency info if we are using recency retriever in the prompt\n", "        if (\n", "            config.max_recency_retriever_tokens > 0\n", "            or config.always_filter_chunks_by_recency\n", "        ):\n", "            recency_info_obj = (\n", "                RecencyInfo.from_json(recency_info) if recency_info else None\n", "            )\n", "            if recency_info_obj is not None:\n", "                deserialized_retrieved_chunks = list(\n", "                    modified_chunks_filter(\n", "                        deserialized_retrieved_chunks,\n", "                        convert_from_datasets_recency_info(recency_info_obj),\n", "                        origins=Chunk<PERSON><PERSON><PERSON>,\n", "                        skip_origins=[\n", "                            ChunkOrigin.RECENCY_RETRIEVER.value,\n", "                            ChunkOrigin.DIFF_RETRIEVER.value,\n", "                        ],\n", "                    )\n", "                )\n", "\n", "        apportionment_config = TokenApportionmentConfig(\n", "            max_content_len=config.max_content_len,\n", "            input_fraction=config.input_fraction,\n", "            prefix_fraction=config.prefix_fraction,\n", "            max_path_tokens=config.max_path_tokens,\n", "            per_retriever_max_tokens={\n", "                \"dense_signature\": config.max_dense_signature_tokens,\n", "                \"recency_retriever\": config.max_recency_retriever_tokens,\n", "                \"diff_retriever\": config.max_diff_retriever_tokens,\n", "            },\n", "        )\n", "\n", "        if config.include_diff_retriever:\n", "            component_order = (\n", "                \"path\",\n", "                \"prefix\",\n", "                \"retrieval\",\n", "                \"signature\",\n", "                \"diff\",\n", "                \"nearby_prefix\",\n", "                \"suffix\",\n", "            )\n", "        else:\n", "            component_order = (\n", "                \"path\",\n", "                \"prefix\",\n", "                \"retrieval\",\n", "                \"signature\",\n", "                \"nearby_prefix\",\n", "                \"suffix\",\n", "            )\n", "\n", "        ender_prompt_formatter_config = EnderPromptFormatterConfig(\n", "            stateless_caching_config=StatelessCachingConfig(\n", "                nearby_prefix_token_len=512,\n", "                quantize_token_len=64,\n", "                quantize_char_len=250,\n", "            ),\n", "            component_order=component_order,\n", "            filter_visible_chunks_by_content=True,\n", "            signature_chunk_origin=\"dense_signature\",\n", "        )\n", "\n", "        prompt_formatter = EnderPromptFormatter(\n", "            apportionment_config=apportionment_config,\n", "            prompt_formatter_config=ender_prompt_formatter_config,\n", "            tokenizer=tokenizer,\n", "        )\n", "\n", "        prompt_input = PromptInput(\n", "            prefix=prefix,\n", "            suffix=suffix,\n", "            prefix_begin=prefix_begin,\n", "            path=file_path,\n", "            retrieved_chunks=deserialized_retrieved_chunks,\n", "            lang=None,\n", "        )\n", "\n", "        formatter_output = prompt_formatter.format_prompt(\n", "            prompt_input, config.max_target_tokens\n", "        )\n", "\n", "        deserialized_middle_spans: list[\n", "            hindsight_common.OutputWithBoundary[SrcSpan]\n", "        ] = pickle.loads(middle_spans)\n", "        special_tokens = tokenizer.special_tokens\n", "        assert (\n", "            len(deserialized_middle_spans) == 1\n", "        ), \"Not implemented for multiple middle spans.\"\n", "        assert isinstance(\n", "            special_tokens,\n", "            (prod_tokenizer.RagSpecialTokens),\n", "        )\n", "        deserialized_middle_span = deserialized_middle_spans[0]\n", "        target_tokens = tokenizer.tokenize_safe(deserialized_middle_span.content.code)\n", "        if deserialized_middle_span.pause_at_end:\n", "            target_tokens.append(special_tokens.pause)\n", "        if deserialized_middle_span.stop_at_end:\n", "            target_tokens.append(special_tokens.eos)\n", "        target_tokens = target_tokens[: config.max_target_tokens]\n", "\n", "        prompt_tokens = formatter_output.tokens() + target_tokens\n", "        # Prompt consists of both prompt and target tokens.\n", "        return {\n", "            \"prompt_tokens\": prompt_tokens,\n", "            \"target_tokens\": target_tokens,\n", "            \"request_id\": request_id,\n", "        }\n", "\n", "    return _generate_ender_promptprefix\n", "\n", "\n", "config_to_test = configs[\"?\"]\n", "generate_ender_prompt = generate_ender_prompt_fn(tokenizer, config_to_test)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Paths to data files\n", "inner_dir = \"dogfood_2024-11-15_2024-12-14\"\n", "# inner_dir = \"dogfood_2024-12-01_2024-12-14\"\n", "parquet_partition_pattern = str(\n", "    BASE_DATA_DIR / inner_dir / \"partition/part-*.zstd.parquet\"\n", ")\n", "parquet_retrieval_pattern = str(\n", "    BASE_DATA_DIR / inner_dir / \"retrieval/part-*.zstd.parquet\"\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["parquet_initial_df, _ = read_parquet_files(parquet_partition_pattern)\n", "\n", "# Surface certain fields\n", "parquet_initial_df[\"request_id\"] = parquet_initial_df[\"hindsight_problem\"].apply(\n", "    lambda x: x[\"completion\"][\"request_id\"]\n", ")\n", "parquet_initial_df[\"prod_prompt_token_ids\"] = parquet_initial_df[\n", "    \"hindsight_problem\"\n", "].apply(lambda x: x[\"completion\"][\"response\"][\"prompt_token_ids\"])\n", "\n", "parquet_initial_df[\"prod_completion_token_ids\"] = parquet_initial_df[\n", "    \"hindsight_problem\"\n", "].apply(lambda x: x[\"completion\"][\"inference_response\"][\"token_ids\"])\n", "\n", "\n", "parquet_initial_df[\"prod_completion_token_log_probs\"] = parquet_initial_df[\n", "    \"hindsight_problem\"\n", "].apply(lambda x: x[\"completion\"][\"inference_response\"][\"token_log_probs\"])\n", "\n", "parquet_initial_df[\"prod_retrieved_chunks\"] = parquet_initial_df[\n", "    \"hindsight_problem\"\n", "].apply(lambda x: x[\"completion\"][\"response\"][\"retrieved_chunks\"])\n", "\n", "merged_df = parquet_initial_df\n", "\n", "parquet_initial_df.head()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["parquet_retrieval_df, _ = read_parquet_files(parquet_retrieval_pattern)\n", "\n", "parquet_retrieval_df[\"research_prompt_token_ids\"] = parquet_retrieval_df.apply(\n", "    lambda row: generate_ender_prompt(\n", "        prefix=row[\"prefix\"],\n", "        prefix_begin=row[\"prefix_begin\"],\n", "        middle_spans=row[\"middle_spans\"],\n", "        suffix=row[\"suffix\"],\n", "        file_path=row[\"file_path\"],\n", "        retrieved_chunks=row[\"retrieved_chunks\"],\n", "        recency_info=row[\"recency_info\"],\n", "        request_id=row[\"request_id\"],\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "parquet_retrieval_df.drop(\n", "    columns=[\n", "        \"prefix\",\n", "        \"prefix_begin\",\n", "        \"suffix\",\n", "        \"file_path\",\n", "        \"retrieved_scores\",\n", "        \"recency_info\",\n", "    ],\n", "    inplace=True,\n", ")\n", "\n", "parquet_retrieval_df.rename(\n", "    columns={\"retrieved_chunks\": \"research_retrieved_chunks\"}, inplace=True\n", ")\n", "\n", "print(parquet_retrieval_df.shape)\n", "parquet_retrieval_df.head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# merge parquet_initial_df and parquet_retrieval_df on request_id\n", "merged_df = parquet_initial_df.merge(parquet_retrieval_df, on=\"request_id\", how=\"inner\")\n", "\n", "print(merged_df.shape)\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# # TEST: remove block after\n", "# parquet_prompt_pattern = str(BASE_DATA_DIR / inner_dir / \"prompts/recency_only/part-*.zstd.parquet\")\n", "# parquet_prompt_df = read_parquet_files(parquet_prompt_pattern)\n", "\n", "# parquet_prompt_df.head()\n", "\n", "# # from research.data.rag.common import create_unpack_unpad_tokens_fn\n", "\n", "# # for idx in range(8566):\n", "# #     tokens = parquet_prompt_df.iloc[idx][\"prompt_tokens\"]\n", "# #     prompt_new = list(create_unpack_unpad_tokens_fn(tokenizer)(tokens))[0]\n", "# #     assert isinstance(prompt_new, dict)\n", "# #     final_tokens: list[int] = prompt_new[\"prompt_tokens\"]\n", "# #     # get up to fim_middle\n", "# #     final_tokens = final_tokens[: final_tokens.index(st.fim_middle) + 1]\n", "\n", "# #     existing_prompt = merged_df.iloc[idx][\"research_prompt_token_ids\"]\n", "\n", "# #     assert final_tokens == existing_prompt, f\"{len(final_tokens)} vs {len(existing_prompt)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load model and run generations"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["base_fp16_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd\",\n", "    \"7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9\",\n", ")\n", "base_fp8_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd_fp8\",\n", "    \"651be22236ee5aa104c4c190eb4e6c9464527f3e825a6afd11ab9d24046dbc15\",\n", ")\n", "# no_context_train_checkpoint = (\"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_no_context_hindsight_ffwd\", \"e0f24f37d6225f53eb75236c324b632945d87af15c880ae218c57c4ed347a7ae\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import contextlib\n", "import io\n", "import sys\n", "\n", "\n", "class ExecuteModel:\n", "    _instances = []\n", "\n", "    @contextlib.contextmanager\n", "    def _suppress_output(self):\n", "        \"\"\"Context manager to suppress stdout temporarily while keeping stderr.\"\"\"\n", "        stdout = sys.stdout\n", "        output = io.StringIO()\n", "        try:\n", "            sys.stdout = output\n", "            yield\n", "        finally:\n", "            sys.stdout = stdout\n", "\n", "    def __init__(\n", "        self,\n", "        checkpoint: str,\n", "        sha: str,\n", "        tokenizer: Qwen25CoderTokenizer,\n", "        use_fp8: bool = False,\n", "    ):\n", "        with self._suppress_output():\n", "            self.tokenizer = tokenizer\n", "            self.config = {\n", "                \"name\": \"fastforward_qwen25coder_14b\",\n", "                \"checkpoint_path\": checkpoint,\n", "                \"checkpoint_sha256\": sha,\n", "                \"sequence_length\": 6600,\n", "                \"use_fp8\": use_fp8,\n", "            }\n", "            from research.eval.harness.factories import create_model\n", "\n", "            self.model = None\n", "            self._model_creator = lambda: cast(\n", "                LLAMA_FastForwardModel, create_model(self.config)\n", "            )\n", "\n", "            self.options = GenerationOptions(\n", "                max_generated_tokens=256, stop_tokens=[st.eos]\n", "            )\n", "\n", "            ExecuteModel._instances.append(self)\n", "\n", "    def ensure_loaded(self):\n", "        \"\"\"Ensure this model is loaded and others are unloaded.\"\"\"\n", "        with self._suppress_output():\n", "            # Unload all other models\n", "            for instance in ExecuteModel._instances:\n", "                if instance is not self and instance.model is not None:\n", "                    instance.deep_unload()\n", "\n", "            # Load this model if needed\n", "            if self.model is None:\n", "                self.model = self._model_creator()\n", "                self.model.load()\n", "\n", "    def __call__(\n", "        self, prompt_tokens: list[int], keep_pause_tokens: bool = False\n", "    ) -> list[int]:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            generated_tokens = self.model.raw_generate_tokens(\n", "                prompt_tokens, options=self.options\n", "            ).tokens\n", "\n", "            original_length = len(generated_tokens)\n", "            generated_tokens, _ = self.extract_generation_before_stop_tokens(\n", "                generated_tokens, [st.eos]\n", "            )\n", "            generation_stopped_at_max_length = original_length == len(generated_tokens)\n", "            if generation_stopped_at_max_length and st.pause is not None:\n", "                for index in reversed(range(len(generated_tokens))):\n", "                    if generated_tokens[index] == st.pause:\n", "                        generated_tokens = generated_tokens[:index]\n", "                        break\n", "\n", "            if keep_pause_tokens:\n", "                return generated_tokens\n", "\n", "            generated_tokens = [\n", "                token for token in generated_tokens if token != st.pause\n", "            ]\n", "            return generated_tokens\n", "\n", "    def simple_call(self, prompt_tokens: list[int]) -> RawGenerateOutput:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            options = GenerationOptions(\n", "                max_generated_tokens=256, stop_tokens=[st.eos, st.pause]\n", "            )\n", "            return self.model.raw_generate_tokens(prompt_tokens, options=options)\n", "\n", "    def forward_pass_for_logits(self, full_prompt: torch.Tensor) -> torch.Tensor:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            return self.model.forward_pass_single_logits(full_prompt)\n", "\n", "    def deep_unload(self):\n", "        with self._suppress_output():\n", "            if self.model is not None:\n", "                self.model.unload()\n", "                self.model = None\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "    def extract_generation_before_stop_tokens(\n", "        self, generated: list[int], stop_token_ids: list[int | None]\n", "    ) -> tuple[list[int], int | None]:\n", "        stop_tokens_ids_set = {\n", "            token_id for token_id in stop_token_ids if token_id is not None\n", "        }\n", "        fim_stop_token_id = None\n", "        for index in range(len(generated)):\n", "            if generated[index] in stop_tokens_ids_set:\n", "                fim_stop_token_id = generated[index]\n", "                generated = generated[:index]\n", "                break\n", "        return generated, fim_stop_token_id\n", "\n", "\n", "base_fp16_model = ExecuteModel(\n", "    base_fp16_checkpoint[0],\n", "    base_fp16_checkpoint[1],\n", "    tokenizer,\n", ")\n", "\n", "base_fp8_model = ExecuteModel(\n", "    base_fp8_checkpoint[0],\n", "    base_fp8_checkpoint[1],\n", "    tokenizer,\n", "    use_fp8=True,\n", ")\n", "\n", "# no_context_train_model = ExecuteModel(\n", "#     no_context_train_checkpoint[0],\n", "#     no_context_train_checkpoint[1],\n", "#     tokenizer,\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Model and Get Statistics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save log probability data of model"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from pathlib import Path\n", "from typing import Dict, List, Optional\n", "import torch\n", "from tqdm import tqdm\n", "\n", "\n", "def load_results_file(jsonl_path: str | Path):\n", "    results = []\n", "    if os.path.exists(jsonl_path):\n", "        results = read_jsonl(jsonl_path)\n", "        print(f\"Processed {len(results)} samples\")\n", "    return results\n", "\n", "\n", "@dataclass\n", "class ModelConfig:\n", "    name: str  # e.g. \"fp16\", \"fp8\"\n", "    model: object  # The actual model object\n", "    column_prefix: str  # Column prefix in the dataframe\n", "\n", "\n", "class TokenAnalyzer:\n", "    def __init__(self, tokenizer):\n", "        self.tokenizer = tokenizer\n", "\n", "    def get_top_tokens_results(\n", "        self,\n", "        logits_list,\n", "        prod_completion_token_ids: List[int],\n", "        prod_completion_token_log_probs: List[float],\n", "        k: int = 3,\n", "    ):\n", "        \"\"\"\n", "        Analyze the top tokens for each position in the completion.\n", "        \"\"\"\n", "\n", "        assert (\n", "            len(logits_list)\n", "            == len(prod_completion_token_ids)\n", "            == len(prod_completion_token_log_probs)\n", "        ), \"Lengths of logits_list, prod_completion_token_ids, and prod_completion_token_log_probs must match\"\n", "\n", "        all_results = []\n", "        for i, logit in enumerate(logits_list):\n", "            probs = torch.softmax(logit, dim=-1)\n", "            top_probs, top_indices = torch.topk(probs, k=k, dim=-1)\n", "\n", "            top_results = [\n", "                {\n", "                    \"token\": self.tokenizer.detokenize([idx]),\n", "                    \"id\": idx,\n", "                    \"prob\": round(prob, 3),\n", "                }\n", "                for prob, idx in zip(top_probs.tolist(), top_indices.tolist())\n", "            ]\n", "\n", "            token_id = prod_completion_token_ids[i]\n", "            token = self.tokenizer.detokenize([token_id])\n", "\n", "            prod_token = {\n", "                \"token\": token,\n", "                \"id\": token_id,\n", "                \"prob\": round(np.exp(prod_completion_token_log_probs[i]), 3),\n", "            }\n", "\n", "            prod_token_replay = {\n", "                \"token\": token,\n", "                \"id\": token_id,\n", "                \"prob\": round(probs[token_id].item(), 3),\n", "            }\n", "\n", "            # Check if production token matches top token in replay\n", "            is_match = token_id == top_indices[0].item()\n", "\n", "            all_results.append(\n", "                {\n", "                    \"position\": i,\n", "                    \"prod_token\": prod_token,\n", "                    \"prod_token_replay\": prod_token_replay,\n", "                    \"top_results_in_replay\": top_results,\n", "                    \"is_match\": is_match,\n", "                }\n", "            )\n", "\n", "        return all_results\n", "\n", "    def analyze_for_model(self, model, row):\n", "        prod_prompt_token_ids = row[\"prod_prompt_token_ids\"].tolist()\n", "        prod_completion_token_ids = row[\"prod_completion_token_ids\"].tolist()\n", "        prod_completion_token_log_probs = row[\n", "            \"prod_completion_token_log_probs\"\n", "        ].tolist()\n", "\n", "        full_prompt = torch.tensor(prod_prompt_token_ids + prod_completion_token_ids)\n", "        logits = model.forward_pass_for_logits(full_prompt)[:-1]\n", "        completion_logits = logits[len(prod_prompt_token_ids) - 1 :]\n", "\n", "        return self.get_top_tokens_results(\n", "            completion_logits,\n", "            prod_completion_token_ids=prod_completion_token_ids,\n", "            prod_completion_token_log_probs=prod_completion_token_log_probs,\n", "        )\n", "\n", "\n", "def process_single_model(\n", "    df,\n", "    model_config: ModelConfig,\n", "    results: List[Dict],\n", "    analyzer: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    save_frequency: int,\n", ") -> List[Dict]:\n", "    \"\"\"Process all rows for a single model.\"\"\"\n", "    print(f\"Processing model: {model_config.name}\")\n", "\n", "    request_id_to_idx = {\n", "        result[\"request_id\"]: idx for idx, result in enumerate(results)\n", "    }\n", "\n", "    # Find which rows need processing for this model\n", "    rows_to_process = []\n", "    for idx, (_, row) in enumerate(df.iterrows()):\n", "        request_id = row[\"request_id\"]\n", "        if (\n", "            request_id not in request_id_to_idx\n", "            or model_config.name\n", "            not in results[request_id_to_idx[request_id]][\"analyses\"]\n", "        ):\n", "            rows_to_process.append((idx, row))\n", "\n", "    if not rows_to_process:\n", "        print(f\"All rows already processed for model {model_config.name}\")\n", "        return results\n", "\n", "    print(f\"Processing {len(rows_to_process)} rows for {model_config.name}\")\n", "\n", "    # Process needed rows\n", "    for count, (_, row) in enumerate(tqdm(rows_to_process), 1):\n", "        request_id = row.request_id\n", "        if request_id not in request_id_to_idx:\n", "            current_row = {\n", "                \"request_id\": request_id,\n", "                \"analyses\": {},\n", "            }\n", "            results.append(current_row)\n", "            request_id_to_idx[request_id] = len(results) - 1\n", "        else:\n", "            current_row = results[request_id_to_idx[request_id]]\n", "\n", "        try:\n", "            analysis_results = analyzer.analyze_for_model(model_config.model, row)\n", "            current_row[\"analyses\"][model_config.name] = analysis_results\n", "\n", "        except Exception as e:\n", "            print(\n", "                f\"Error analyzing request {request_id} with model {model_config.name}: {e}\"\n", "            )\n", "            continue\n", "\n", "        if count % save_frequency == 0 or count == len(rows_to_process):\n", "            write_jsonl(output_path, results)\n", "            print(f\"Saved after processing {count} rows\")\n", "\n", "    return results\n", "\n", "\n", "def run_analysis(\n", "    df, models: List[ModelConfig], output_path: Path, save_frequency: int = 200\n", "):\n", "    \"\"\"Run analysis for multiple models sequentially.\"\"\"\n", "    analyzer = TokenAnalyzer(tokenizer)  # Assuming tokenizer is globally available\n", "\n", "    # Load existing results if any\n", "    results = load_results_file(output_path)\n", "\n", "    # Process each model completely before moving to the next\n", "    for model_config in models:\n", "        results = process_single_model(\n", "            df, model_config, results, analyzer, save_frequency\n", "        )\n", "\n", "    return results\n", "\n", "\n", "# Usage example:\n", "file_name = \"logits_analysis.jsonl\"\n", "output_path = BASE_DATA_DIR / inner_dir / file_name\n", "\n", "models = [\n", "    ModelConfig(name=\"fp16\", model=base_fp16_model, column_prefix=\"prod_replay_fp16\"),\n", "    ModelConfig(name=\"fp8\", model=base_fp8_model, column_prefix=\"prod_replay_fp8\"),\n", "]\n", "\n", "results = run_analysis(merged_df, models, output_path)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Dict, List, Any\n", "\n", "\n", "@dataclass\n", "class ModelMetrics:\n", "    \"\"\"Container for model metrics.\"\"\"\n", "\n", "    avg_prob_diff: float\n", "    top_token_mismatch_rate: float\n", "    extreme_diff_rate: float\n", "    accuracy: float\n", "    total_tokens: int\n", "\n", "\n", "@dataclass\n", "class MetricsCollector:\n", "    \"\"\"Collects metrics during analysis.\"\"\"\n", "\n", "    avg_prob_diff: List[float]\n", "    top_token_mismatches: int\n", "    extreme_diffs: int\n", "    total_tokens: int\n", "    accuracy: int\n", "\n", "    @classmethod\n", "    def create(cls) -> \"MetricsCollector\":\n", "        \"\"\"Create a new metrics collector with zero values.\"\"\"\n", "        return cls(\n", "            avg_prob_diff=[],\n", "            top_token_mismatches=0,\n", "            extreme_diffs=0,\n", "            total_tokens=0,\n", "            accuracy=0,\n", "        )\n", "\n", "    def compute_metrics(self) -> ModelMetrics:\n", "        \"\"\"Compute final metrics from collected data.\"\"\"\n", "        if self.total_tokens == 0:\n", "            raise ValueError(\"No tokens processed\")\n", "\n", "        return ModelMetrics(\n", "            avg_prob_diff=float(np.mean(self.avg_prob_diff)),\n", "            top_token_mismatch_rate=self.top_token_mismatches / self.total_tokens,\n", "            extreme_diff_rate=self.extreme_diffs / self.total_tokens,\n", "            accuracy=self.accuracy / self.total_tokens,\n", "            total_tokens=self.total_tokens,\n", "        )\n", "\n", "\n", "class ResultsAnalyzer:\n", "    \"\"\"Analyzes model results and computes metrics.\"\"\"\n", "\n", "    def __init__(self, model_types: List[str], extreme_diff_threshold: float = 0.2):\n", "        self.model_types = model_types\n", "        self.extreme_diff_threshold = extreme_diff_threshold\n", "\n", "    def analyze_position(self, pos_result: Dict[str, Any], metrics: MetricsCollector):\n", "        \"\"\"Analyze metrics for a single position.\"\"\"\n", "        prod_prob = pos_result[\"prod_token\"][\"prob\"]\n", "        replay_prob = pos_result[\"prod_token_replay\"][\"prob\"]\n", "\n", "        # 1. Probability difference\n", "        prob_diff = abs(prod_prob - replay_prob)\n", "        metrics.avg_prob_diff.append(prob_diff)\n", "\n", "        # 2. Top token mismatch\n", "        if not pos_result[\"is_match\"]:\n", "            metrics.top_token_mismatches += 1\n", "\n", "        # 3. Extreme differences\n", "        if prob_diff > self.extreme_diff_threshold:\n", "            metrics.extreme_diffs += 1\n", "\n", "        metrics.total_tokens += 1\n", "\n", "        # 4. Perfect accuracy (matching token and very close probability)\n", "        if pos_result[\"is_match\"] and prob_diff < 1e-5:\n", "            metrics.accuracy += 1\n", "\n", "    def analyze_results(self, results_path: Path) -> Dict[str, ModelMetrics]:\n", "        \"\"\"Analyze the saved results file for all model types.\"\"\"\n", "        results = load_results_file(results_path)\n", "\n", "        # Initialize metrics collectors for each model type\n", "        metrics_collectors = {\n", "            model_type: MetricsCollector.create() for model_type in self.model_types\n", "        }\n", "\n", "        # Process all results\n", "        for result in results:\n", "            analyses = result[\"analyses\"]\n", "            for model_type in self.model_types:\n", "                if model_type not in analyses:\n", "                    continue\n", "\n", "                for pos_result in analyses[model_type]:\n", "                    self.analyze_position(pos_result, metrics_collectors[model_type])\n", "\n", "        # Compute final metrics for each model\n", "        return {\n", "            model_type: collector.compute_metrics()\n", "            for model_type, collector in metrics_collectors.items()\n", "            if collector.total_tokens > 0\n", "        }\n", "\n", "\n", "def print_metrics(metrics: Dict[str, ModelMetrics]):\n", "    \"\"\"Print metrics in a formatted way.\"\"\"\n", "    for model_type, model_metrics in metrics.items():\n", "        print(f\"\\n{model_type.upper()} Model Metrics:\")\n", "        print(f\"Average Probability Difference: {model_metrics.avg_prob_diff:.4f}\")\n", "        print(f\"Top Token Mismatch Rate: {model_metrics.top_token_mismatch_rate:.4f}\")\n", "        print(f\"Extreme Difference Rate: {model_metrics.extreme_diff_rate:.4f}\")\n", "        print(f\"Accuracy: {model_metrics.accuracy:.4f}\")\n", "        print(f\"Total Tokens Analyzed: {model_metrics.total_tokens}\")\n", "\n", "\n", "# Usage:\n", "def main():\n", "    results_path = BASE_DATA_DIR / inner_dir / \"logits_analysis.jsonl\"\n", "\n", "    # Define model types to analyze\n", "    model_types = [\"fp16\", \"fp8\"]\n", "\n", "    # Create analyzer and process results\n", "    analyzer = ResultsAnalyzer(model_types)\n", "    metrics = analyzer.analyze_results(results_path)\n", "\n", "    # Print results\n", "    print_metrics(metrics)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze Results"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def compare_sample(result: dict, ground_truth: str, tokens_column: str):\n", "    response_tokens = result[tokens_column]\n", "    response_interactive_tokens, delimiter = prefix_up_to_any_delimiter(\n", "        response_tokens, [st.pause, st.eos, st.skip]\n", "    )\n", "    response_interactive_str = tokenizer.detokenize(response_interactive_tokens)\n", "\n", "    response_no_pause_str = tokenizer.detokenize(\n", "        [t for t in response_tokens if t != st.pause]\n", "    )\n", "\n", "    no_pause_match = response_no_pause_str == ground_truth\n", "    interactive_match = response_interactive_str == ground_truth\n", "\n", "    return {\n", "        \"no_pause_match\": no_pause_match,  # not sure if this is useful or correct due to\n", "        \"interactive_match\": interactive_match,\n", "    }\n", "\n", "\n", "def compute_stats(results: list[dict], columns: list[str]):\n", "    \"\"\"Compute statistics for multiple columns with the same metrics.\n", "\n", "    Args:\n", "        results: List of result dictionaries\n", "        columns: List of column names to compute stats for. If None, will detect from first result.\n", "    \"\"\"\n", "\n", "    stats = defaultdict(int)\n", "\n", "    for result in results:\n", "        # make sure all columns are keys in the result, otherwise skip this result\n", "        if not all(col in result for col in columns):\n", "            continue\n", "\n", "        ground_truth = result[\"ground_truth\"]\n", "        stats[\"total_samples\"] += 1\n", "\n", "        for col in columns:\n", "            comparison = compare_sample(result, ground_truth, col)\n", "            if comparison[\"interactive_match\"]:\n", "                stats[f\"{col}_interactive_match\"] += 1\n", "\n", "    return dict(stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_name = \"hindsight_inspect.jsonl\"\n", "save_data = True\n", "\n", "results = []\n", "jsonl_path = BASE_DATA_DIR / inner_dir / file_name\n", "if os.path.exists(jsonl_path):\n", "    results = read_jsonl(jsonl_path)\n", "    print(f\"Processed {len(results)} samples\")\n", "\n", "request_id_to_idx = {result[\"request_id\"]: idx for idx, result in enumerate(results)}\n", "\n", "for count, (_, row) in enumerate(merged_df.iterrows(), start=1):\n", "    ground_truth = row[\"ground_truth\"]\n", "    request_id = row[\"request_id\"]\n", "\n", "    # Retrieve the existing result if it exists\n", "    current_row = {\"request_id\": request_id, \"ground_truth\": ground_truth}\n", "    if request_id in request_id_to_idx:\n", "        result_idx = request_id_to_idx[request_id]\n", "        current_row = results[result_idx]\n", "\n", "    # This is from production. This is the official results.\n", "    if \"prod_completion_token_ids\" not in current_row:\n", "        prod_prompt_token_ids = row[\"prod_prompt_token_ids\"].tolist()\n", "        prod_completion_token_ids = row[\"prod_completion_token_ids\"].tolist()\n", "        prod_completion_token_probs = np.exp(\n", "            row[\"prod_completion_token_log_probs\"]\n", "        ).tolist()\n", "        current_row[\"prod_completion_token_ids\"] = prod_completion_token_ids\n", "        current_row[\"prod_completion_token_probs\"] = prod_completion_token_probs\n", "\n", "    # This is the response generated from the expected prompt.\n", "    # Theoretically this should be the same as the original response, but it may not be due to quantization.\n", "    if \"prod_replay_fp16_completion_token_ids\" not in current_row:\n", "        prod_prompt_token_ids = row[\"prod_prompt_token_ids\"].tolist()\n", "        prod_replay_fp16_raw_generated_output = base_fp16_model.simple_call(\n", "            prod_prompt_token_ids\n", "        )\n", "        prod_replay_fp16_completion_token_ids = (\n", "            prod_replay_fp16_raw_generated_output.tokens\n", "        )\n", "        prod_replay_fp16_completion_token_probs = (\n", "            prod_replay_fp16_raw_generated_output.token_probs()\n", "        )\n", "        current_row[\"prod_replay_fp16_completion_token_ids\"] = (\n", "            prod_replay_fp16_completion_token_ids\n", "        )\n", "        current_row[\"prod_replay_fp16_completion_token_probs\"] = (\n", "            prod_replay_fp16_completion_token_probs\n", "        )\n", "\n", "    # This is the response generated from the re-generated prompt using retrieval.\n", "    if \"prod_replay_fp8_completion_token_ids\" not in current_row:\n", "        prod_prompt_token_ids = row[\"prod_prompt_token_ids\"].tolist()\n", "        prod_replay_fp8_raw_generated_output = base_fp8_model.simple_call(\n", "            prod_prompt_token_ids\n", "        )\n", "        prod_replay_fp8_completion_token_ids = (\n", "            prod_replay_fp8_raw_generated_output.tokens\n", "        )\n", "        prod_replay_fp8_completion_token_probs = (\n", "            prod_replay_fp8_raw_generated_output.token_probs()\n", "        )\n", "        current_row[\"prod_replay_fp8_completion_token_ids\"] = (\n", "            prod_replay_fp8_completion_token_ids\n", "        )\n", "        current_row[\"prod_replay_fp8_completion_token_probs\"] = (\n", "            prod_replay_fp8_completion_token_probs\n", "        )\n", "    # This is the one we are interested in, and the prompt itself is slightly different due to different retrieval results.\n", "    # actual_prompt_tokens = row[\"actual_prompt_tokens\"]\n", "    # actual_response_tokens = base_model(actual_prompt_tokens, keep_pause_tokens=True)\n", "\n", "    if request_id in request_id_to_idx:\n", "        result_idx = request_id_to_idx[request_id]\n", "        results[result_idx] = current_row\n", "    else:\n", "        results.append(current_row)\n", "\n", "    if count % 200 == 1 or count == len(merged_df):\n", "        print(\n", "            f\"Processed {count} samples, stats={compute_stats(results, ['prod_completion_token_ids', 'prod_replay_fp16_completion_token_ids', 'prod_replay_fp8_completion_token_ids'])}\"\n", "        )\n", "        if save_data:\n", "            write_jsonl(jsonl_path, results)\n", "\n", "# 4296/8566 prod completion token ids interactive match\n", "# Processed 6401 samples, stats={'total_samples': 6401, 'prod_completion_token_ids_interactive_match': 3204, 'prod_replay_fp8_completion_token_ids_interactive_match': 3122, 'prod_replay_fp16_completion_token_ids_interactive_match': 3084}"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["results = []\n", "count = 4324\n", "\n", "jsonl_path = BASE_DATA_DIR / f\"hindsight_inspect_exp1_results_{count}.jsonl\"\n", "if os.path.exists(jsonl_path):\n", "    results = read_jsonl(jsonl_path)\n", "    print(f\"Processed {len(results)} samples\")\n", "\n", "print(len(results))\n", "assert count == len(\n", "    results\n", "), f\"Number of results does not match count. Expected {count}, got {len(results)}\"\n", "\n", "stats = compute_stats(results)\n", "\n", "# print stats in a well formatted way\n", "for k, v in stats.items():\n", "    print(f\"{k}: {v}/{count} ({100*v/count:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze Interesting Cases"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["# understand the difference in interactive key metrics in results\n", "import csv\n", "\n", "link = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}#tab=Summary\"\n", "\n", "csv_file = \"hindsight_inspect_results.csv\"\n", "rows = []\n", "for idx, result in enumerate(results):\n", "    original_response_tokens: list[int] = result[\"original_response_tokens\"]\n", "    expected_response_tokens: list[int] = result[\"expected_response_tokens\"]\n", "    actual_response_tokens: list[int] = result[\"actual_response_tokens\"]\n", "\n", "    original_response_interactive_tokens, original_delimiter = (\n", "        prefix_up_to_any_delimiter(\n", "            original_response_tokens, [st.pause, st.eos, st.skip]\n", "        )\n", "    )\n", "    expected_response_interactive_tokens, expected_delimiter = (\n", "        prefix_up_to_any_delimiter(\n", "            expected_response_tokens, [st.pause, st.eos, st.skip]\n", "        )\n", "    )\n", "    # actual_response_interactive_tokens, actual_delimiter = prefix_up_to_any_delimiter(actual_response_tokens, [pause, eos, skip])\n", "\n", "    original_response_interactive_str = tokenizer.detokenize(\n", "        original_response_interactive_tokens\n", "    )\n", "    expected_response_interactive_str = tokenizer.detokenize(\n", "        expected_response_interactive_tokens\n", "    )\n", "    # actual_response_interactive_str = tokenizer.detokenize(actual_response_interactive_tokens)\n", "\n", "    if (\n", "        original_response_interactive_str != expected_response_interactive_str\n", "        and original_response_interactive_str == result[\"expected_ground_truth\"]\n", "    ):\n", "        print(f\"Sample {idx}\")\n", "        request_id = merged_df.iloc[idx][\"request_id\"]\n", "        # print(Fore.YELLOW + \"GROUND TRUTH:\\n\" + result[\"expected_ground_truth\"] + Style.RESET_ALL)\n", "        # print()\n", "        print(\n", "            Fore.RED\n", "            + \"ORIGINAL RESPONSE:\\n\"\n", "            + original_response_interactive_str\n", "            + Style.RESET_ALL\n", "        )\n", "        print(request_id)\n", "        print(\n", "            Fore.GREEN\n", "            + \"SAME PROMPT RESPONSE:\\n\"\n", "            + expected_response_interactive_str\n", "            + Style.RESET_ALL\n", "        )\n", "        # print()\n", "        # print(Fore.YELLOW + \"SAME PROMPT TOKENS:\\n\" + tokenizer.detokenize(base_model.call2(merged_df.iloc[idx][\"expected_prompt_tokens\"])) + Style.RESET_ALL)\n", "\n", "        rid_link = link.format(request_id=request_id)\n", "        rows.append(\n", "            {\n", "                \"idx\": idx,\n", "                \"request_id\": f'= HYPERLINK(\"{rid_link}\", \"{request_id}\")',\n", "                \"original_response_interactive_str\": original_response_interactive_str,\n", "                \"expected_response_interactive_str\": expected_response_interactive_str,\n", "            }\n", "        )\n", "        print(\"=\" * 100)\n", "\n", "df = pd.DataFrame(rows)\n", "# df.to_csv(csv_file, index=False, quoting=csv.QUOTE_MINIMAL, escapechar=\"\\\\\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate / Update Log Probability Statistics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Log Probability Stuff"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Get Interesting Cases\n", "\n", "count = 4324\n", "results = []\n", "jsonl_path = BASE_DATA_DIR / f\"hindsight_inspect_exp1_results_{count}.jsonl\"\n", "if os.path.exists(jsonl_path):\n", "    results = read_jsonl(jsonl_path)\n", "    print(f\"Processed {len(results)} samples\")\n", "\n", "# stats = compute_stats(results, columns=[\"original_response_tokens\", \"expected_response_tokens\", \"actual_response_tokens\"])\n", "\n", "interesting_cases = []\n", "for idx, result in enumerate(results):\n", "    original_response_tokens: list[int] = result[\"original_response_tokens\"]\n", "    expected_response_tokens: list[int] = result[\"expected_response_tokens\"]\n", "    actual_response_tokens: list[int] = result[\"actual_response_tokens\"]\n", "\n", "    prod_completion_interactive_token_ids, original_delimiter = (\n", "        prefix_up_to_any_delimiter(\n", "            original_response_tokens, [st.pause, st.eos, st.skip]\n", "        )\n", "    )\n", "    expected_response_interactive_tokens, expected_delimiter = (\n", "        prefix_up_to_any_delimiter(\n", "            expected_response_tokens, [st.pause, st.eos, st.skip]\n", "        )\n", "    )\n", "    # actual_response_interactive_tokens, actual_delimiter = prefix_up_to_any_delimiter(actual_response_tokens, [pause, eos, skip])\n", "\n", "    prod_completion_interactive_str = tokenizer.detokenize(\n", "        prod_completion_interactive_token_ids\n", "    )\n", "    expected_response_interactive_str = tokenizer.detokenize(\n", "        expected_response_interactive_tokens\n", "    )\n", "    # actual_response_interactive_str = tokenizer.detokenize(actual_response_interactive_tokens)\n", "\n", "    if (\n", "        prod_completion_interactive_str != expected_response_interactive_str\n", "        and prod_completion_interactive_str == result[\"expected_ground_truth\"]\n", "    ):\n", "        interesting_cases.append(idx)\n", "\n", "interesting_df_cases = merged_df.iloc[interesting_cases]\n", "print(interesting_df_cases.shape)\n", "interesting_df_cases.head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def print_results_formatted(results):\n", "    for result in results:\n", "        position = result[\"position\"]\n", "        print(f\"\\n=== Position {position} ===\")\n", "\n", "        # Print production token info\n", "        if result[\"prod_token\"]:\n", "            prod = result[\"prod_token\"]\n", "            print(\"\\nProduction Token:\")\n", "            print(f\"  '{prod['token']}' (id={prod['id']}, prob={prod['prob']})\")\n", "\n", "        # Print replay token info\n", "        if result[\"prod_token_replay\"]:\n", "            replay = result[\"prod_token_replay\"]\n", "            print(\"\\nReplay Token:\")\n", "            print(\n", "                f\"  '{replay['token']}' (id={replay['id']}, prob={replay['prob']:.3f})\"\n", "            )\n", "\n", "        # Print top k alternatives\n", "        print(f\"\\nTop {len(result['top_results_in_replay'])} Alternatives:\")\n", "        for i, alt in enumerate(result[\"top_results_in_replay\"], 1):\n", "            print(f\"  {i}. '{alt['token']}' (id={alt['id']}, prob={alt['prob']:.3f})\")\n", "\n", "        print(\"\\n\" + \"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Util functions for analyzing Prompts"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["def split_list(lst: list[int], delimiter: list[int]) -> list[list[int]]:\n", "    result = []\n", "    current_subsequence = []\n", "\n", "    idx = 0\n", "    while idx < len(lst):\n", "        if lst[idx : idx + len(delimiter)] == delimiter:\n", "            if current_subsequence:\n", "                result.append(current_subsequence)\n", "            idx += len(delimiter)\n", "            current_subsequence = []\n", "        else:\n", "            current_subsequence.append(lst[idx])\n", "            idx += 1\n", "    if current_subsequence:  # Append the last subsequence if it's not empty\n", "        result.append(current_subsequence)\n", "    return result\n", "\n", "\n", "def extract_tokens(tokens: list[int], start_token: int, end_token: int):\n", "    \"\"\"Extracts tokens between start and end tokens.\"\"\"\n", "    tokens = list(tokens)\n", "    start = tokens.index(start_token) + 1\n", "    end = tokens.index(end_token) if end_token in tokens else len(tokens)\n", "    assert start <= end, f\"start: {start}, end: {end}\"\n", "    return tokens[start:end]\n", "\n", "\n", "def extract_text(tokens: list[int], start_token: int, end_token: int):\n", "    \"\"\"Extracts text between start and end tokens.\"\"\"\n", "    extracted_tokens = extract_tokens(tokens, start_token, end_token)\n", "    return tokenizer.detokenize(extracted_tokens)\n", "\n", "\n", "def longestCommonSubstr(s1, s2):\n", "    m = len(s1)\n", "    n = len(s2)\n", "\n", "    # Create a 1D array to store the previous row's results\n", "    prev = [0] * (n + 1)\n", "\n", "    res = 0\n", "    for i in range(1, m + 1):\n", "        # Create a temporary array to store the current row\n", "        curr = [0] * (n + 1)\n", "        for j in range(1, n + 1):\n", "            if s1[i - 1] == s2[j - 1]:\n", "                curr[j] = prev[j - 1] + 1\n", "                res = max(res, curr[j])\n", "            else:\n", "                curr[j] = 0\n", "\n", "        # Move the current row's data to the previous row\n", "        prev = curr\n", "\n", "    return res, res / max(m, n)\n", "\n", "\n", "def print_list(items, color: str = Fore.RED):\n", "    for item in items:\n", "        print(color + str(item) + Style.RESET_ALL)\n", "        print(\"-\" * 50)\n", "    print(\"=\" * 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prompt Similarity Metrics"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["def count_shared_tokens(list1: list[int], list2: list[int]) -> int:\n", "    \"\"\"Count shared tokens between two lists, respecting duplicates.\n", "\n", "    Example:\n", "        list1 = [1, 2, 2, 3]\n", "        list2 = [1, 2, 2, 2, 4]\n", "        count_common_tokens(list1, list2) -> 3  # (one 1, two 2s), 3/4 = 0.75\n", "    \"\"\"\n", "    # Count frequencies in both lists\n", "    counter1 = Counter(list1)\n", "    counter2 = Counter(list2)\n", "\n", "    # Sum up the minimum frequency for each token that appears in both lists\n", "    result = sum(\n", "        min(counter1[token], counter2[token]) for token in set(counter1) & set(counter2)\n", "    )\n", "    return result\n", "\n", "\n", "def compute_prompt_similarity_metrics(row):\n", "    prod_prompt_token_ids = row[\"prod_prompt_token_ids\"]\n", "    research_prompt_token_ids = row[\"research_prompt_token_ids\"]\n", "\n", "    sections_to_check = [\n", "        (\"start_to_retrieval\", st.filename, st.retrieval_section),\n", "        (\"prefix\", st.fim_prefix, st.fim_suffix),\n", "        (\"suffix\", st.fim_suffix, st.fim_middle),\n", "    ]\n", "\n", "    for section_name, start_token, end_token in sections_to_check:\n", "        prod_section = extract_text(prod_prompt_token_ids, start_token, end_token)\n", "        research_section = extract_text(\n", "            research_prompt_token_ids, start_token, end_token\n", "        )\n", "        assert (\n", "            prod_section == research_section\n", "        ), f\"{section_name} mismatch: prod '{prod_section}', research '{research_section}'\"\n", "\n", "    shared_token_count = count_shared_tokens(\n", "        prod_prompt_token_ids, research_prompt_token_ids\n", "    )\n", "\n", "    return {\n", "        \"prod_sequence_length\": len(prod_prompt_token_ids),\n", "        \"research_sequence_length\": len(research_prompt_token_ids),\n", "        \"shared_token_count\": shared_token_count,\n", "        \"sequences_identical\": list(prod_prompt_token_ids)\n", "        == list(research_prompt_token_ids),\n", "        \"sequences_share_all_tokens\": shared_token_count\n", "        == len(prod_prompt_token_ids)\n", "        == len(research_prompt_token_ids),\n", "        \"production_token_coverage\": shared_token_count / len(prod_prompt_token_ids),\n", "    }"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["all_results = []\n", "for idx, row in merged_df.iterrows():\n", "    row_data = compute_prompt_similarity_metrics(row)\n", "    all_results.append(row_data)\n", "\n", "df = pd.DataFrame(all_results)\n", "\n", "df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing Signature Chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_closest_signature_chunk(s1: list[int], candidate_chunks: list[list[int]]):\n", "    if st.newline in s1:\n", "        first_newline_idx = s1.index(st.newline)\n", "    else:\n", "        first_newline_idx = len(s1)\n", "\n", "    filtered_candidate_chunks = []\n", "    for candidate_chunk in candidate_chunks:\n", "        # print(tokenizer.detokenize(s1[:first_newline_idx]), tokenizer.detokenize(candidate_chunk[:first_newline_idx]))\n", "        if s1[:first_newline_idx] == candidate_chunk[:first_newline_idx]:\n", "            filtered_candidate_chunks.append(candidate_chunk)\n", "\n", "    best_similarity_percentage = 0\n", "    best_candidate_chunk = None\n", "    for candidate_chunk in filtered_candidate_chunks:\n", "        _, similarity_percentage = longestCommonSubstr(\n", "            s1[first_newline_idx:], candidate_chunk[first_newline_idx:]\n", "        )\n", "        if similarity_percentage > best_similarity_percentage:\n", "            best_similarity_percentage = similarity_percentage\n", "            best_candidate_chunk = candidate_chunk\n", "\n", "    return best_candidate_chunk, best_similarity_percentage, first_newline_idx"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [], "source": ["def analyze_signature_mismatches(merged_df, compare_research_to_prod=True, drop_n=2):\n", "    \"\"\"\n", "    Analyze signature mismatches between research and prod prompts.\n", "\n", "    Args:\n", "        merged_df: DataFrame containing the prompt data\n", "        compare_research_to_prod: If True, finds research chunks missing in prod\n", "                                If False, finds prod chunks missing in research\n", "        drop_n: Number of chunks to ignore from the end\n", "    \"\"\"\n", "    count = 0\n", "    results = []\n", "\n", "    source_label = \"research\" if compare_research_to_prod else \"prod\"\n", "    target_label = \"prod\" if compare_research_to_prod else \"research\"\n", "\n", "    for idx, row in merged_df.iterrows():\n", "        source_tokens = row[f\"{source_label}_prompt_tokens\"]\n", "        target_tokens = row[f\"{target_label}_prompt_tokens\"]\n", "\n", "        source_signature_tokens = extract_tokens(\n", "            source_tokens, st.sig_begin, st.sig_end\n", "        )[1:-1]\n", "        target_signature_tokens = extract_tokens(\n", "            target_tokens, st.sig_begin, st.sig_end\n", "        )[1:-1]\n", "\n", "        source_chunks: list[list[int]] = split_list(\n", "            source_signature_tokens, [st.newline, st.newline]\n", "        )\n", "        target_chunks: list[list[int]] = split_list(\n", "            target_signature_tokens, [st.newline, st.newline]\n", "        )\n", "\n", "        source_chunks.reverse()\n", "        target_chunks.reverse()\n", "\n", "        is_problem = False\n", "        for source_chunk in source_chunks[:-drop_n]:\n", "            if source_chunk not in target_chunks:\n", "                is_problem = True\n", "\n", "        if is_problem:\n", "            result = {\n", "                \"idx\": idx,\n", "                \"request_id\": row[\"request_id\"],\n", "                \"file_path\": row[\"file_path\"],\n", "                f\"num_{source_label}_chunks\": len(source_chunks),\n", "                f\"num_{target_label}_chunks\": len(target_chunks),\n", "                \"max_similarity\": 0,\n", "                \"mismatches\": [],\n", "            }\n", "\n", "            for chunk_rank, source_chunk in enumerate(source_chunks[:-drop_n]):\n", "                if source_chunk not in target_chunks:\n", "                    closest_chunk, similarity_percentage, first_newline_idx = (\n", "                        find_closest_signature_chunk(source_chunk, target_chunks)\n", "                    )\n", "                    result[\"max_similarity\"] = max(\n", "                        result[\"max_similarity\"], similarity_percentage\n", "                    )\n", "\n", "                    mismatch = {\n", "                        f\"{source_label}_chunk\": tokenizer.detokenize(source_chunk),\n", "                        \"similarity_percentage\": similarity_percentage,\n", "                        \"first_newline_idx\": first_newline_idx,\n", "                        \"chunk_rank\": chunk_rank,\n", "                        f\"{source_label}_chunk_preview\": tokenizer.detokenize(\n", "                            source_chunk[: first_newline_idx + 20]\n", "                        ),\n", "                        \"closest_chunk\": tokenizer.detokenize(closest_chunk)\n", "                        if closest_chunk\n", "                        else None,\n", "                    }\n", "\n", "                    result[\"mismatches\"].append(mismatch)\n", "\n", "            assert result[\n", "                \"mismatches\"\n", "            ], f\"Row {idx}: no mismatches found, but is_problem is True\"\n", "            results.append(result)\n", "            count += 1\n", "\n", "    # Sort results by highest similarity percentage\n", "    results.sort(\n", "        key=lambda x: max(\n", "            (m[\"similarity_percentage\"] for m in x[\"mismatches\"]), default=0\n", "        ),\n", "        reverse=True,\n", "    )\n", "\n", "    # Print results\n", "    for idx, result in enumerate(results):\n", "        max_similarity = result[\"max_similarity\"]\n", "\n", "        print(\n", "            f\"Row {result['idx']}: mismatched signature chunks. Count={idx} || Max similarity={max_similarity}\"\n", "        )\n", "        print(f\"Rid={result['request_id']} || Path={result['file_path']}\")\n", "\n", "        for midx, mismatch in enumerate(result[\"mismatches\"]):\n", "            if midx != 0:\n", "                print(\"-\" * 50)\n", "\n", "            print(\n", "                f\"Chunk rank: {mismatch['chunk_rank']}/{result[f'num_{source_label}_chunks']}. \"\n", "                f\"{target_label.capitalize()} chunks count={result[f'num_{target_label}_chunks']}\"\n", "            )\n", "\n", "            if mismatch[\"closest_chunk\"] is not None:\n", "                print(\n", "                    Fore.RED\n", "                    + f\"{source_label.capitalize()} chunk:\\n{mismatch[f'{source_label}_chunk']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.GREEN\n", "                    + f\"Closest {target_label} chunk with same path:\\n{mismatch['closest_chunk']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.YELLOW\n", "                    + f\"Similarity percentage: {mismatch['similarity_percentage']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "            else:\n", "                print(\n", "                    Fore.RED\n", "                    + f\"{source_label.capitalize()} chunk:\\n{mismatch[f'{source_label}_chunk_preview']}\"\n", "                    + Fore.WHITE\n", "                    + \"...\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.GREEN\n", "                    + f\"Closest {target_label} chunk with same path: None\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.YELLOW\n", "                    + f\"Similarity percentage: {mismatch['similarity_percentage']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "\n", "        print(\"=\" * 100)\n", "\n", "    print(\n", "        f\"Found {count} rows with mismatched signature chunks out of {len(merged_df)} rows\"\n", "    )\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare actual chunks missing from expected\n", "results_actual = analyze_signature_mismatches(\n", "    merged_df, compare_research_to_prod=True, drop_n=4\n", ")\n", "\n", "# Compare expected chunks missing from actual\n", "results_expected = analyze_signature_mismatches(\n", "    merged_df, compare_research_to_prod=False, drop_n=4\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing Line Chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SmallPromptChunk:\n", "    def __init__(self, origin: str, path: str, text: str, idx: int | None = None):\n", "        self.origin = origin\n", "        self.path = path\n", "        self.text = text\n", "        self.idx = idx\n", "\n", "    def __eq__(self, other):\n", "        return self.path == other.path and self.text == other.text\n", "\n", "    def __hash__(self):\n", "        return hash((self.origin, self.path, self.text))\n", "\n", "    def __repr__(self):\n", "        return f\"SmallPromptChunk(origin={self.origin}, path={self.path}, idx={self.idx}, text={self.text[:200]})\"\n", "\n", "\n", "class DenseChunk:\n", "    def __init__(\n", "        self,\n", "        tokens: list[int],\n", "        path: list[int],\n", "        text: list[int],\n", "        path_str: str,\n", "        text_str: str,\n", "    ):\n", "        self.tokens = tokens\n", "        self.path = path\n", "        self.text = text\n", "        self.path_str = path_str\n", "        self.text_str = text_str\n", "\n", "    def __eq__(self, other):\n", "        return self.path == other.path and self.text == other.text\n", "\n", "    def __hash__(self):\n", "        return hash((tuple(self.path), tuple(self.text)))\n", "\n", "    def __repr__(self):\n", "        return f\"DenseChunk(path={self.path_str}, text={self.text_str[:20]})\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_closest_line_chunk(s1: <PERSON>se<PERSON>hun<PERSON>, candidate_chunks: list[DenseChunk]):\n", "    filtered_candidate_chunks = [\n", "        chunk for chunk in candidate_chunks if s1.path == chunk.path\n", "    ]\n", "\n", "    best_similarity_percentage = 0\n", "    best_candidate_chunk = None\n", "    for candidate_chunk in filtered_candidate_chunks:\n", "        _, similarity_percentage = longestCommonSubstr(s1.text, candidate_chunk.text)\n", "        if similarity_percentage > best_similarity_percentage:\n", "            best_similarity_percentage = similarity_percentage\n", "            best_candidate_chunk = candidate_chunk\n", "\n", "    return best_candidate_chunk, best_similarity_percentage\n", "\n", "\n", "def line_or_recency_chunk_tokens_to_dense_chunk(tokens: list[int]):\n", "    path = extract_tokens(tokens, st.filename, st.ret_body)\n", "    text = extract_tokens(tokens, st.ret_body, st.ret_start)\n", "\n", "    path_str = tokenizer.detokenize(path)\n", "    text_str = tokenizer.detokenize(text)\n", "    return DenseChunk(tokens, path, text, path_str, text_str)\n", "\n", "\n", "def separate_dense_chunks(\n", "    dense_chunks: list[DenseChunk], retrieval_chunks: list[SmallPromptChunk]\n", ") -> tuple[list[<PERSON><PERSON><PERSON><PERSON><PERSON>], list[<PERSON>se<PERSON><PERSON><PERSON>]]:\n", "    \"\"\"Separates dense chunks into line chunks and recency chunks based on their origin.\n", "\n", "    Args:\n", "        dense_chunks: List of DenseChunk objects to be separated\n", "        retrieval_chunks: List of SmallPromptChunk objects containing origin information\n", "\n", "    Returns:\n", "        tuple containing:\n", "            - list of line chunks (from dense retriever)\n", "            - list of recency chunks (from recency retriever)\n", "\n", "    Raises:\n", "        AssertionError: If a chunk is not found in retrieval_chunks or if recency chunks\n", "                       appear after line chunks\n", "    \"\"\"\n", "    line_chunks: list[DenseChunk] = []\n", "    recency_chunks: list[DenseChunk] = []\n", "\n", "    for dense_chunk in dense_chunks:\n", "        candidate_chunks = list(\n", "            set(\n", "                [\n", "                    ret\n", "                    for ret in retrieval_chunks\n", "                    if (\n", "                        dense_chunk.path_str in ret.path\n", "                        or ret.path in dense_chunk.path_str\n", "                    )\n", "                    and dense_chunk.text_str == ret.text\n", "                ]\n", "            )\n", "        )\n", "\n", "        matching_origins = defaultdict(list)\n", "        for candidate_chunk in candidate_chunks:\n", "            matching_origins[candidate_chunk.origin].append(candidate_chunk)\n", "\n", "        # if line chunks is empty - prefer recency if possible\n", "        if len(line_chunks) == 0:\n", "            if ChunkOrigin.RECENCY_RETRIEVER.value in matching_origins:\n", "                recency_chunks.append(dense_chunk)\n", "            elif <PERSON>.DENSE_RETRIEVER.value in matching_origins:\n", "                line_chunks.append(dense_chunk)\n", "            else:\n", "                raise AssertionError(\n", "                    f\"Chunk not found in retrieval chunks when no line chunks:\\n\"\n", "                    f\"Path: {dense_chunk.path_str}\\n\"\n", "                    f\"Text: {dense_chunk.text_str}\\n\"\n", "                )\n", "        # if line chunks is not empty - have to choose a line chunk\n", "        else:\n", "            if ChunkOrigin.DENSE_RETRIEVER.value in matching_origins:\n", "                line_chunks.append(dense_chunk)\n", "            else:\n", "                raise AssertionError(\n", "                    f\"Chunk not found in retrieval chunks when line chunks exist:\\n\"\n", "                    f\"Path: {dense_chunk.path_str}\\n\"\n", "                    f\"Text: {dense_chunk.text_str}\\n\"\n", "                )\n", "\n", "    return line_chunks, recency_chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_line_mismatches(\n", "    merged_df, compare_research_to_prod=True, drop_n=2, filter_idx=None\n", "):\n", "    \"\"\"\n", "    Analyze (recency) + line mismatches between research and prod prompts.\n", "\n", "    Args:\n", "        merged_df: DataFrame containing the prompt data\n", "        compare_research_to_prod: If True, finds research chunks missing from prod\n", "                                If False, finds prod chunks missing from research\n", "        drop_n: Number of chunks to ignore from the end\n", "    \"\"\"\n", "    count = 0\n", "    results = []\n", "\n", "    source_label = \"research\" if compare_research_to_prod else \"prod\"\n", "    target_label = \"prod\" if compare_research_to_prod else \"research\"\n", "\n", "    df = merged_df\n", "    if filter_idx:\n", "        df = merged_df[filter_idx : filter_idx + 1]\n", "\n", "    for idx, row in df.iterrows():\n", "        source_tokens = row[f\"{source_label}_prompt_tokens\"]\n", "        target_tokens = row[f\"{target_label}_prompt_tokens\"]\n", "\n", "        source_line_tokens = extract_tokens(\n", "            source_tokens, st.retrieval_section, st.sig_begin\n", "        )\n", "        target_line_tokens = extract_tokens(\n", "            target_tokens, st.retrieval_section, st.sig_begin\n", "        )\n", "\n", "        source_chunks_list: list[list[int]] = split_list(\n", "            source_line_tokens, [st.ret_start]\n", "        )\n", "        target_chunks_list: list[list[int]] = split_list(\n", "            target_line_tokens, [st.ret_start]\n", "        )\n", "\n", "        # Find retrieved chunks in research and prod so we can assign each chunk an origin\n", "        research_retrieved_chunks = deserialize_retrieved_prompt_chunks(\n", "            row[\"research_retrieved_chunks\"]\n", "        )\n", "        research_small_retrieved_chunks = [\n", "            SmallPromptChunk(chunk.origin, chunk.path, chunk.text, chunk_idx)\n", "            for chunk_idx, chunk in enumerate(research_retrieved_chunks)\n", "        ]\n", "\n", "        prod_retrieval_chunks = row[\"prod_retrieved_chunks\"]\n", "        prod_small_retrieved_chunks = [\n", "            SmallPromptChunk(chunk[\"origin\"], chunk[\"path\"], chunk[\"text\"], chunk_idx)\n", "            for chunk_idx, chunk in enumerate(prod_retrieval_chunks)\n", "        ]\n", "\n", "        source_retrieval_chunks: list[SmallPromptChunk] = (\n", "            research_small_retrieved_chunks\n", "            if compare_research_to_prod\n", "            else prod_small_retrieved_chunks\n", "        )\n", "        target_retrieval_chunks: list[SmallPromptChunk] = (\n", "            prod_small_retrieved_chunks\n", "            if compare_research_to_prod\n", "            else research_small_retrieved_chunks\n", "        )\n", "\n", "        source_retrieval_chunks = [\n", "            chunk\n", "            for chunk in source_retrieval_chunks\n", "            if chunk.origin == ChunkOrigin.RECENCY_RETRIEVER.value\n", "            or chunk.origin == ChunkOrigin.DENSE_RETRIEVER.value\n", "        ]\n", "        target_retrieval_chunks = [\n", "            chunk\n", "            for chunk in target_retrieval_chunks\n", "            if chunk.origin == ChunkOrigin.RECENCY_RETRIEVER.value\n", "            or chunk.origin == ChunkOrigin.DENSE_RETRIEVER.value\n", "        ]\n", "\n", "        source_chunks: list[DenseChunk] = [\n", "            line_or_recency_chunk_tokens_to_dense_chunk(chunk)\n", "            for chunk in source_chunks_list\n", "        ]\n", "        target_chunks: list[DenseChunk] = [\n", "            line_or_recency_chunk_tokens_to_dense_chunk(chunk)\n", "            for chunk in target_chunks_list\n", "        ]\n", "\n", "        line_source_chunks, recency_source_chunks = separate_dense_chunks(\n", "            source_chunks, source_retrieval_chunks\n", "        )\n", "        line_target_chunks, recency_target_chunks = separate_dense_chunks(\n", "            target_chunks, target_retrieval_chunks\n", "        )\n", "\n", "        if len(recency_source_chunks) != len(recency_target_chunks):\n", "            print(\n", "                f\"Skipping row {idx} ({row['request_id']}) due to - Line chunks mismatch: {len(recency_target_chunks)} != {len(recency_source_chunks)}\"\n", "            )\n", "            continue\n", "\n", "        source_chunks = line_source_chunks\n", "        target_chunks = line_target_chunks\n", "\n", "        source_chunks.reverse()\n", "        target_chunks.reverse()\n", "\n", "        is_problem = False\n", "        for source_chunk in source_chunks[:-drop_n]:\n", "            if source_chunk not in target_chunks:\n", "                is_problem = True\n", "\n", "        if is_problem:\n", "            result = {\n", "                \"idx\": idx,\n", "                \"request_id\": row[\"request_id\"],\n", "                \"file_path\": row[\"file_path\"],\n", "                f\"num_{source_label}_chunks\": len(source_chunks),\n", "                f\"num_{target_label}_chunks\": len(target_chunks),\n", "                \"max_similarity\": 0,\n", "                \"mismatches\": [],\n", "            }\n", "\n", "            for chunk_rank, source_chunk in enumerate(source_chunks[:-drop_n]):\n", "                if source_chunk not in target_chunks:\n", "                    closest_chunk, similarity_percentage = find_closest_line_chunk(\n", "                        source_chunk, target_chunks\n", "                    )\n", "                    result[\"max_similarity\"] = max(\n", "                        result[\"max_similarity\"], similarity_percentage\n", "                    )\n", "\n", "                    mismatch = {\n", "                        f\"{source_label}_chunk\": tokenizer.detokenize(\n", "                            source_chunk.tokens\n", "                        ),\n", "                        \"similarity_percentage\": similarity_percentage,\n", "                        \"chunk_rank\": chunk_rank,\n", "                        f\"{source_label}_chunk_preview\": tokenizer.detokenize(\n", "                            source_chunk.path + source_chunk.text[:20]\n", "                        ),\n", "                        \"closest_chunk\": tokenizer.detokenize(closest_chunk.tokens)\n", "                        if closest_chunk\n", "                        else None,\n", "                    }\n", "\n", "                    result[\"mismatches\"].append(mismatch)\n", "\n", "                    break\n", "\n", "            assert result[\n", "                \"mismatches\"\n", "            ], f\"Row {idx}: no mismatches found, but is_problem is True\"\n", "            results.append(result)\n", "            count += 1\n", "\n", "    # Sort results by highest similarity percentage\n", "    results.sort(\n", "        key=lambda x: max(\n", "            (m[\"similarity_percentage\"] for m in x[\"mismatches\"]), default=0\n", "        ),\n", "        reverse=True,\n", "    )\n", "\n", "    # Print results\n", "    for idx, result in enumerate(results):\n", "        max_similarity = result[\"max_similarity\"]\n", "\n", "        print(\n", "            f\"Row {result['idx']}: mismatched line/recency chunks. Count={idx} || Max similarity={max_similarity}\"\n", "        )\n", "        print(f\"Rid={result['request_id']} || Path={result['file_path']}\")\n", "\n", "        for midx, mismatch in enumerate(result[\"mismatches\"]):\n", "            if midx != 0:\n", "                print(\"-\" * 50)\n", "\n", "            print(\n", "                f\"Chunk rank: {mismatch['chunk_rank']}/{result[f'num_{source_label}_chunks']}. \"\n", "                f\"{target_label.capitalize()} chunks count={result[f'num_{target_label}_chunks']}\"\n", "            )\n", "\n", "            if mismatch[\"closest_chunk\"] is not None:\n", "                print(\n", "                    Fore.RED\n", "                    + f\"{source_label.capitalize()} chunk:\\n{mismatch[f'{source_label}_chunk']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.GREEN\n", "                    + f\"Closest {target_label} chunk with same path:\\n{mismatch['closest_chunk']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.YELLOW\n", "                    + f\"Similarity percentage: {mismatch['similarity_percentage']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "            else:\n", "                print(\n", "                    Fore.RED\n", "                    + f\"{source_label.capitalize()} chunk:\\n{mismatch[f'{source_label}_chunk_preview']}\"\n", "                    + Fore.WHITE\n", "                    + \"...\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.GREEN\n", "                    + f\"Closest {target_label} chunk with same path: None\"\n", "                    + Style.RESET_ALL\n", "                )\n", "                print(\n", "                    Fore.YELLOW\n", "                    + f\"Similarity percentage: {mismatch['similarity_percentage']}\"\n", "                    + Style.RESET_ALL\n", "                )\n", "\n", "        print(\"=\" * 100)\n", "\n", "    print(\n", "        f\"Found {count} rows with mismatched line/recency chunks out of {len(merged_df)} rows\"\n", "    )\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare actual chunks missing from expected\n", "results_actual = analyze_line_mismatches(\n", "    merged_df, compare_research_to_prod=True, drop_n=4\n", ")\n", "\n", "results_actual = analyze_line_mismatches(\n", "    merged_df, compare_research_to_prod=False, drop_n=4\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GCS Random Checking Code"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import get_tenant\n", "from base.datasets.gcs_blob_cache import GCSBlobCache\n", "\n", "from google.cloud import storage\n", "\n", "gcp_creds, _ = get_gcp_creds()\n", "tenant = get_tenant(\"dogfood-shard\")\n", "client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "bucket = client.bucket(tenant.blob_bucket_name)\n", "cache = GCSBlobCache(\n", "    bucket=bucket,\n", "    bucket_prefix=tenant.blob_bucket_prefix,\n", "    max_size_bytes=1_000_000_000,  # 1GB cache size\n", "    num_threads=10,\n", ")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from services.embeddings_indexer.chunker_factory import (\n", "    create_transform_smart_line_level_chunker,\n", ")\n", "\n", "config = {\n", "    \"max_chunk_chars\": 768,\n", "    \"max_headers\": 3,\n", "}\n", "service_chunker = create_transform_smart_line_level_chunker(config)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from research.core.types import Document\n", "from models.retrieval.chunking import chunking\n", "\n", "blob_name = \"9912a01d979ae279ca21da85ef7f25c1e906b5183fd8dc3b4fb3b5cae8644d53\"\n", "blob_content = list(cache.get([blob_name]))[0]\n", "assert blob_content is not None\n", "document = Document.new(\n", "    text=blob_content.content,\n", "    path=str(blob_content.path),\n", ")\n", "service_document = chunking.Document(\n", "    blob_name=blob_name, text=blob_content.content, path=str(blob_content.path)\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["chunks = service_chunker.split_into_chunks(service_document)\n", "\n", "for chunk in chunks:\n", "    print(chunk.text)\n", "    print(Fore.RED + \"==================\" + Fore.RESET)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze task info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "base_path = \"/mnt/efs/augment/user/pranay/hindsight/vanguard_2024-11-01_2025-01-14/task_info/hindsight-retrieval/2025-01-27-09-52-06-1931ab455bdd42fd969421812675e7e7/\"\n", "\n", "# Get all part files\n", "part_files = glob.glob(os.path.join(base_path, \"part-*\"))\n", "part_files.sort()\n", "\n", "for part_file in part_files:\n", "    with open(part_file, \"r\") as f:\n", "        content = f.read()\n", "        # This is equivalent to the 'back' alias: interpret escape sequences\n", "        interpreted_content = content.encode().decode(\"unicode_escape\")\n", "        # Print lines containing \"rows\"\n", "        for line in interpreted_content.split(\"\\n\"):\n", "            if \"rows\" in line.lower():\n", "                print(f\"{os.path.basename(part_file)}: {line.strip()}\")\n", "\n", "        print(\"=\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import get_tenant\n", "from base.datasets.gcs_blob_cache import GCSBlobCache\n", "\n", "from google.cloud import storage\n", "\n", "gcp_creds, _ = get_gcp_creds()\n", "tenant = get_tenant(\"i0-vanguard0\")\n", "client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "bucket = client.bucket(tenant.blob_bucket_name)\n", "cache = GCSBlobCache(\n", "    bucket=bucket,\n", "    bucket_prefix=tenant.blob_bucket_prefix,\n", "    max_size_bytes=1_000_000_000,  # 1GB cache size\n", "    num_threads=10,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blobs = [\n", "    \"008951ed1aa097214770eae250e08b9f09c975b27c91621d2c8fe4c285adfc23\",\n", "    \"017f7273326013eb817690e2cbdc4a68e771338e6dd7ae14244306ecf3e359c1\",\n", "    \"01d2c427e66ce11d685148891e1efc7e6dd178732792877b4bc447256c0425fb\",\n", "    \"01daea0c6215a7dfb45fc3f9d219893776b99648d574dc1c785ea26fae8f63e2\",\n", "    \"029cbe43b9ee505116f8c5a70572ea659032a8cff6506fb5b54a691113c1e497\",\n", "    \"02e0fb556a25bb1f58dc3fbd756be4c3f93c2c8e0327a74ca25dba3b3e1e0464\",\n", "    \"02ef8e4281d96185d67bf9f26f09935abbc9e788f53ddad3a3df51d8b3c71b5f\",\n", "    \"03050e2e97429c6da30e29a5b7f2754c1d0a2014f5aed0dbb0744c227a937c23\",\n", "    \"032e1588cea362b142ad40e7601b43295b7533fabc099eb06dc3885a563d70bb\",\n", "    \"033482c2c31a5c06765868b4c4477fa7313d9d79b5cdbe2bf78c65dc557fa3eb\",\n", "    \"03354b0990902c2aa675418604161e5a0bcab0f638e5a9126ae81cf8a6e0abfb\",\n", "    \"036775dcd9c871b4fb42f373f200f2fdee31c1be32384fde63192c0af1ce3d7d\",\n", "    \"03c92317b976726ba90566277a82d1a31f5c70dd37e4f0326b6d291ae5a4b883\",\n", "    \"03d8702cd25b0f1a12983d852035b43a12294e251038c8c9b21f6c3e5405f4b2\",\n", "    \"0421f101dca0a53272acf07d29baff045cbadc64cc176dc19c6c4f9e5f4eb98f\",\n", "    \"048c267814732ad28c4f8125f7d586c857caf53730c713734b140a718866ebaf\",\n", "    \"050db2bc4f160d4dbe535bb7e2c1a427c289743436c5bf9b9b058a19a327ca79\",\n", "    \"06faf9fd28bb194551c9dddbc679ee552a68bc7258499aae49d711cbe92ba974\",\n", "    \"074f716120d8c29f189eda7e938a3f34901ab90cb9239817a8c76ffd14cb2526\",\n", "    \"077cd06f582e7b378cbf791773a92dfdb44632d9118722af27dfef4c1b5fcd7f\",\n", "    \"08bb42e0b23f5f1cd46b31e2d6027aaa28284c8bc005928bb7f49337a618bccc\",\n", "    \"08f80ec0553d3a946ff334394ff03b4ec6a3fdc0e5b86985915f7d6d4fee95c0\",\n", "    \"091d011a9b93160fd71c5ce9dd344894e28aed5bdcf2b4b61c3ad0fc464ae589\",\n", "    \"0a531ce41444d4684b567973938f3256c6f79b16b9ad953b48f6b1ca00f9952e\",\n", "    \"0acaea86391c44dac9ac968aa5ce520b84cd519d88a30540a2677246f8ffca60\",\n", "    \"0acaeb6844ef0f73426026e81d43242c208f73fdddf669a3bdfff2333e5562c6\",\n", "    \"0ca3e066007e102f1d4936ab1bc0fa01178084ca899d63aff64c83f5ed45e5ce\",\n", "    \"0cd8629814c48d61d7117527c35d5387492c6c65bef222deb9ca38ec28731698\",\n", "    \"0d1c472e89e162faf5c647ff136e2327a1c8cae9a19b2020a64427b9bfa2dbfb\",\n", "    \"0d9226f66ec0f6c02471a5698d974db07ec04a09157237e7979804a5d7323bbf\",\n", "    \"0da3fd68356271e54c49c1c1cc2783cc9e50ae5416002c157d8122b7460bd152\",\n", "    \"0daf3584dfa7e474ae0e68a393497ed0f7382f5ecf176153cd690c965079bdc8\",\n", "    \"0dc9fb211ac992bf9033e6f0901963cc0c8de4189531293c60b3a9406c7db1f3\",\n", "    \"0dcb7bc132f76abef4bff5e4f4a34e16a3901973b6c0da494ded052258178017\",\n", "    \"0ec0ccd14b56c68147a319b54171a597ba8251fca7cbdf73047adfb1780b98c0\",\n", "    \"0ec6ee339e4bc35b6018631d17aaeccaa3b797c3150bcca220bab4ade9988af4\",\n", "    \"0f177dfde1b3ff10ac92263a293a1b88681c8bcee2f12fe12eaa095a139f5871\",\n", "    \"10cc2f4de9b9bba71ab771181e8f3de570ea15791ad881dfd31d56d8e17cc1b5\",\n", "    \"1132163c895f5b0ec7b814ba1ec7c9e1f2b4e8bf331c6aab79f0c11efac3384f\",\n", "    \"130bfd3404e40ce9799399ad5c4a8d57a6f0e86f055612ad4b4d551dd89898a0\",\n", "    \"130c13c7ba02fd00cf161d5aa493767b5e4fc9e77ca251566d77ad9bdfc77e96\",\n", "    \"13ca27ff3034e02be603f861ca3ddbfbd9229de5a15786d4bae8d9bc43109ae3\",\n", "    \"14012fc5d0eb7d794144904cfa20eac808e0b6b6b2000a57699ec615660645c4\",\n", "    \"141d471363cf2d14a393ae9f096daf525858f640ed1e77559fb6a7af0dff1e96\",\n", "    \"14ed623818cd9bc3f9fd50ca3a32e6c6f1cc22836f355795fe6f79425f824ded\",\n", "    \"1597efdfe5bfa7fab5d56bb84efa6ef86b7a80138d831fb30e0a5ca6d02efd46\",\n", "    \"16b9640acef131d535f4aebe870a7eb94adb77306545277662a442e004d5acba\",\n", "    \"1731392c80b8ddca8310c6523220d74132f675ceed29ce65f8fb6a8346f5321f\",\n", "    \"183f5bcc8ebf2fc4d15fc12f4123d5dbff48106225e93a183418b9c774ddceb7\",\n", "    \"1875939bc428b85f88e3dbc2f7ecf2854454d77bbdf07d8b7209354879598b63\",\n", "    \"18bad9c111b3ab979815a7ce950228243109418c2e969214a8284d10e243c1c1\",\n", "    \"18e8200f7a633bb11cfc70c8f9c16eb1476720264492350770abeb9e35fa0f8d\",\n", "    \"192c966457d6ca7b9c26d6d9f38ab44e4e7920f7512c5c4ab15bba7ed9225549\",\n", "    \"1986aff5a8561c5d9cd66f97f58a829f5d17e16aa6572a4b057fb4d0a5262338\",\n", "    \"19b453347f6d89d4e350c7c892ac3ede1d5f374352fdba5b1cdb045027e01668\",\n", "    \"19f6adf0bbbbc1ff5d40f4354d47099efed01353ddaf3c268910634053c3f0d8\",\n", "    \"1aed4ebf87fde1103750fc0a4b6e03aa4edb8f611b805a4f860e43ac8bfeb414\",\n", "    \"1b5acd94f4abb5d9d44c416313fab750754bbcebd875879f28f82f7abcc47bee\",\n", "    \"1bd32c35320cee87cbf583e8819ae04cfe7bba4c60850742584dc66c85f07ef6\",\n", "    \"1c03defa54ad93deab5a711d10681cad6ad255ab92717d1add5979ed1ea185cc\",\n", "    \"1c38b037dcef98b29ae7966d4e7630fd651484384f18d609d837ea419aa78181\",\n", "    \"1c397619e0e282fb235881b2cb4c7a69f89af71cdb48b969fd673994f5ce3533\",\n", "    \"1c43d38098a75abfd1d2a68515d05ab53758deaaea87934c775b7c39be48259d\",\n", "    \"1ca33287bdf7ed20d799fcd57ebfb30f32074188430e5ee4579a55a7cd801e34\",\n", "    \"1d555bd50e461363e02328feba9a27c49e2404ee5c719236aea97e2ae34fbbb5\",\n", "    \"1d68a44a3192bdb2d895080fd2a4eac5dd7e99c8a20956b2f781eeff85be4b9a\",\n", "    \"1d7a15be2cda281eda869644d3281e0686533e69e4fb290125723d730a0cf7e1\",\n", "    \"1e3ef9c7da2d6359e2826d71a72b284e1db4daa84248e361f1bfaf9052ba4e31\",\n", "    \"1e5359ca826548491e0ae36687a7f07ad3b5c99bbc2f512fb3f1ade9625538a7\",\n", "    \"1f80b5257b1665c6306b255c60f9dbdfb25977fadba20dc58ea78e6d080cefd8\",\n", "    \"1fd49e4e6c614cb5a2882d7ac6258db3e4d6800a46c588601bdccee7ba022ea2\",\n", "    \"206cf6a582cf5f962c6abb3200782ceb12585ef205ef60c07054773b3668defc\",\n", "    \"20718a7b42b804a7d0f5f8472e64e83852fea48185a596e5f3ccab79d40d7f08\",\n", "    \"221c9852d6e6241cead38ffe0b7ceb18e2be0c691d9e2bc1c4ca064d0cd57fd7\",\n", "    \"221fe021bf9d31ebf1d025f02d2dd6e1a45eb477546ef6ebd876089f81f5399d\",\n", "    \"223cda5931ca3dc8a2f291cdca7ae2955f58a005e69adad87c3b918e1ed73308\",\n", "    \"2248f0a4fb380b5b0c1f1c1160a15f3d957c27a9cc2bc78e536d3c2650c619f7\",\n", "    \"227bf440dbe1098a50ef97f7786583eb5743879bc8ca9d7df82a399707566a21\",\n", "    \"2292a73b763c02c99abac40d3a67c70fb810382c00e231962446b8cdd5ee3382\",\n", "    \"24041b7c392fb8500db6c1b4afa527ab268510a44fcbc2d1fe487c9afbdb8b08\",\n", "    \"24bef66e5d759480baa105912b00f9f9b8731d41e3c13c1e5357528f9cdca292\",\n", "    \"24c8d48d3b029935675b76d5ceb222c13399dc71a385600bd772b115ed4b23ff\",\n", "    \"25017a0c538de4110f6fc2afcda1e34ad672a9893a2818b00305fe272fa77602\",\n", "    \"2522a491c68676599138d5b64663cfdcf630fd55ecea6fa52b531ac242be3930\",\n", "    \"2527ea8cec7ebf97430ae7b072dae967848a051e0efdacd343b223865b24879e\",\n", "    \"25aa8233f5501eda156fba6fe3cdcc3809ecf3f92eaa211b033d399e10819589\",\n", "    \"25fbeaee42363e7c601ee5882af068944b292873f6282bc9fa4ff34a2cead04c\",\n", "    \"277d21074727e24a4f6dc4a814fac9f1438fa0e84efca9e5d8fd247931778335\",\n", "    \"27fb6911aada5a7613ddab294d33bbeee36167b21777cb702378c67209226524\",\n", "    \"287e1e4069d5966f5bb7e7612409f6dbf6cadc450729f525f02e58d8b6fb7b8f\",\n", "    \"2a0e5976012f29339481cbef5254e0ddad02e8214fbf21cbaf6b70643980e389\",\n", "    \"2a3ff0ed37f5686f2c188505039ddb1d6f266c0dac14122386b38e2cd466c62d\",\n", "    \"2a76e6a8cfcf517ac9b51f17ad243e48d0a78f730e90bdb0b4aac5f86bbd52a8\",\n", "    \"2b5108109993b9e2b0e263f5bd25be230582ea5d28da203f9505930a5fd4c658\",\n", "    \"2c4598c7b81f69d4dd46214770f631dea1275a1c8049024ee3f1e24a3298366b\",\n", "    \"2c95472ea941561d5aa45614d979968e2f8a48e809df7739f8c3c9c7569c1966\",\n", "    \"2ce683e18377fd7660b3dc3581a97b55f5298c11f6d865bdceb77c40ef0a1307\",\n", "    \"2cf79c2d6a30e2123800c9bcd6a5a60a74433b3e913800159114ebc1f7a8f7c8\",\n", "    \"2d23ceb60bc336f37b5a4d5832fc3e144aaaab5285025b6b08fd3113ee8e31ce\",\n", "    \"2d4ea2f96ec6eb2b7d27475b20fdf3f1337d547f9c3a3a6d75f3b57be3289c9b\",\n", "    \"2f2c382eb871d5f3bef66b9df55a6109becbc912c0d47b81650a09d5d50eda04\",\n", "    \"2f42e54d41f3542009fc0accd6ee43ac70cb0f2a9a6ccd94cdfa5b10202f0f00\",\n", "    \"2f54a5f732e34f17ae971db9b331f3424af5d4ac54d08d05d95550014dae600c\",\n", "    \"2f723be11945e4b9e60fcdb4481e54c46eca05a6d950fdf1a6badb02e2cd2d25\",\n", "    \"317b73326ca0e0d7cf888132749bcd30cd784db6f43c3b99a6a2523687ef2171\",\n", "    \"3204032446f01fd5348ef576dbb8855c25bee50d3fefc0525bc4d2781810a6e0\",\n", "    \"325f146688802bd25bedd847e688fdc8147dc981d70662666062d7140d354bb9\",\n", "    \"33303bcbab92cfd8f494bccc4d0e67f6d51325f346eb05df48b81ad6f4b4b53d\",\n", "    \"33f1694123b9231f4402c8f93e54d7bfabc71eaf746446507f817d8c1eb8e51d\",\n", "    \"3415276f6ae89f912b26fe89db403fbe77a96ec60d7d55402502173488e4ade3\",\n", "    \"344dc0ae11ae5611440919d29db1e44fe345ca841646c673093cdb5e02193ec7\",\n", "    \"34a5b1449267cfd9cb47436c287223d91e38764933c56f2578317e6de4aadf4c\",\n", "    \"34b6719d6cf3980a8d7d6b04e1e02487b9eba28860fe03a06e1d056b39a972f3\",\n", "    \"35b367fb96fc58d612edd93eb7d9bf284fa3b6f583d5467291af68140ab084a0\",\n", "    \"36219b5b60ceb5989b35070402395fe247e2e42de86d046a6e6235d90ac64c2e\",\n", "    \"363cc8c0fab13168a7916d682394ffb6b8c87a8897b68c424eef4e4cf84e15ba\",\n", "    \"370901003a047801e2c053a52b2707a9e28e23ec7b5ce33dcd84209a611809ca\",\n", "    \"37b41af1f57bdae5600a67ec531cb7cc4443fa37cce4cb7abf636eef2ac69586\",\n", "    \"3803d949bc8f56bf5111f69e1724528b2cdc52ed4326258bfc217f0f960891f0\",\n", "    \"3838f4ba821480f37f16919b43b8717b59527143e7dbcfc9d23e8d1546cfc450\",\n", "    \"384bb04292208e9f52310eea95318bced6824fc59e2452124d4400ca4c019bfb\",\n", "    \"38d816f79ab914e1e88831d6644e4a92eb504af4a61a904f6640aabb88f01fa6\",\n", "    \"391149df0cb334dd2b4b83acb5fa6c464ef8554ea650e55a5411559268ed36d1\",\n", "    \"394227f1c68defd4f53051e301b99fe06bbf46ca7f5bbbd384bf8316ffd90213\",\n", "    \"39705abe7c59a75f56fcfc2c5c89bdd6f9bd8231c1c427d021d6903fb7664fbb\",\n", "    \"39bbf1c7dce050b70ca60ded3d4616699d70d88d4586c4669b39fe4c7e37cad5\",\n", "    \"3a487ecd7748b3c4a33697dda2e1731f27a74d484c81d4c94a7aa3a4023c0a7b\",\n", "    \"3acce3485a1af0b9a4a13a9a6288cb7632b2522d6c226f13b440854eeb4389cd\",\n", "    \"3b1b2c170c74bd4a3ce98a817ba75c6c316c40d0f4540ec1f03e89f809c10d62\",\n", "    \"3b45f0178f8d0dd1bb0c3c56c9bd6b13b69e0bceaba44119e3f7af11e3bed8d4\",\n", "    \"3b816d3937e9f21c3be1045674eb73c830ef2cec3302f13f02f6e30f0433b2e6\",\n", "    \"3cb7bcec3d4979282d1da07da5b8ca029bcf49e9d0a2d5a186f5afadc0a125eb\",\n", "    \"3d1edee96ecc94870238ae653bc9b6465aab138cc1cf1609a35f6549f9578add\",\n", "    \"3d909502199ade9d06d17836c79f46ff4cbc2d860655c1dc072e01fe7c0e649a\",\n", "    \"3d9ce23a46684301d53edb91d68393cde8060a78df4d061ed1ba99ed99ccbfb8\",\n", "    \"3db4c42d2fd710cd387be307721bdb1c32cdef1962a392707a75fef4e6ad114a\",\n", "    \"3defd7a7bac051f5aac326bf6a9a89711ed46dd257601ae0a4b6772b69d4174c\",\n", "    \"3e18cf1436282eb1e351416313a4ed226359b8e9dd64ad68ecb8e2f2b502318b\",\n", "    \"3e4864802260b93cce5e56a4d9eb7ffebf4b595bb52554cf5d2a71b4a6193070\",\n", "    \"3e92a7b05c0999502fd1e456fa102f9f878b9e965c6ff932e752f11c5084765f\",\n", "    \"3f521b57a7267d3a48a9ef8c562a5ead3280e5757d97506677f1cab8c039cafd\",\n", "    \"3f72fba141b3c33b9d57443240fcea5610405427a59b4ca859daaba1cf9ae334\",\n", "    \"4005c1f0125fc68d306c8ebb7e2f8b065762ff7fa5119bbd22dc281b97133da1\",\n", "    \"423dd03ddd1a4a8b705a80580b759747666c935d47ffb71b0a78c33996079d06\",\n", "    \"423e85d09efaaac9539224890713fabb3db681f17e5ef5b1110998b4207577cc\",\n", "    \"438a8cb79289aa20071ffc9cac7fc33a9507def065c6760fb8a1054f64e55f58\",\n", "    \"43db51ed3925a508b46d626144c94369c67e7a9dd365bd872a33f71dd198292e\",\n", "    \"448e02b7d3f53250ce184d6d55d25094280f28f197638cd3bdccbf736830403d\",\n", "    \"449467fcf647184044b224da8ac0feae3ffe51c8c28f05d7b1fd152243aaccd8\",\n", "    \"462b9e9fe86660c79b1882f4982f23467ffdb578dc8eefb1988a1ab12552cf0e\",\n", "    \"4655b700935478ae8d558e8d946033178bbaf582193180004995351e3e5ede59\",\n", "    \"471a6c4228161ab60215d3f9fa22a096188292c27e36e503b50c0a5c4f3e18c6\",\n", "    \"47271c4ab3a9c6ffa08b7b0cb5eb43adffa2ed44e802d803b00fe80c3a72ce71\",\n", "    \"4751acb5e993c52d7cf352cbe415dfd3c920ca27140228196cab03a89e9dca05\",\n", "    \"477fc7c2ee8539947c50bb0deebdfdc68ec869ce835688660bf5f232ada3c9e2\",\n", "    \"478e25ce2684ede421e732193b30757448cb9210f55e2913c2e8cad10add6986\",\n", "    \"47f2f50100ba9ba98b9b2473fd57bf784d57db11c5c5a246ec2078bdf0116d8c\",\n", "    \"486a8a0ec8a07c64c827f162110b935cc3e903131a8d6147f3dc39eefd57c657\",\n", "    \"489bab873db8d28066ffb017cdbe19d124958aab48f67b51f5fad3bcf656e6b0\",\n", "    \"49cbc174d33ed373694a99557b0a3ded3bb8f027fc7884fb9cf320da4b1bde1f\",\n", "    \"4a5f204243ece8d176e6d2ed95efb934d60a4eb397567d250eaae38f9b089efe\",\n", "    \"4a77487d71b60f9136fe6507db8dc133e4d8bbcdd80b11eb8b52218c2d2120e3\",\n", "    \"4ad2df1bb655acb4ad9ebd40c7c8f73848b33edd1a3516d9b715ccc10bd00bb8\",\n", "    \"4b614cdb0bc780a6fbd0402764b4db74c192e59119e771467372f565bfdeb299\",\n", "    \"4c2adab09179282547f02c4cd46e8a30709af5c5be19ec5623a5f64b2c5cca0f\",\n", "    \"4d077508cd370243a0ef726651168e01fb0b9ed476944ae1119ed8b97e53710f\",\n", "    \"4d2a3ad7d5e91d42a7351f7ef4cff7e58c260a638220fa9bedffcd5c3c06507d\",\n", "    \"4d4ae7f05ac6c8ca934c6fe24c279f26902dc4cd9ee4870b77795447a23ab1bc\",\n", "    \"4d90392fb496bec637cc0bcb34d075a675fe13380d163bd1f121c6ffb08c1f67\",\n", "    \"4db1dd9c00aedbaa5b0b8bec4cd8b2564528790e3f68c85ff9e7385747a6904d\",\n", "    \"4dfc3d959bbf8bbb437e5f5270279e820ad195164a4a3d9b0154a108d1293f6b\",\n", "    \"4e8b4b5d37d272db2f4bea160c167bf7258d652bdc7694c03d8dc1f823dc783a\",\n", "    \"4f2781e9319b24dd1ec01e8e519b5159354d99fdb71455caf984c7e25ad4c9b8\",\n", "    \"4fff4f1408d14052e35dcc1750e91db09fdb76d53688b1b06ecce7e8a7d540cf\",\n", "    \"502cb1a3ce1e769a5bab7abd6330c75e3e639c13c9ebf1502e2b35e885b823ff\",\n", "    \"5148083bddff79fe19e0b7f846d36e54ed4725fa9fa1ac0ade25211d3c315cfa\",\n", "    \"51651f43151331d0463f10c25f586f295348c88d82d8b8ac5965d6856c97d2e5\",\n", "    \"5198e4c459f90d06ff4e2bdb86bb93e670918a9c255b04c17cff4b4215848bbc\",\n", "    \"51c525b9298913f8f7c34cc240056367ddee00656acaa09107ddbf7e4d5ae5a7\",\n", "    \"529b7c1de0c1888fa25fee72ddf0c76c3b59f3516bc6c5481230014c58ba92e0\",\n", "    \"52f562a9b5134fd29eabe359360d5669405111ddcd167ad52e75e3fb4958b418\",\n", "    \"5398a275a6be80be547b163bc95147a1a68034fe2f30cc36de1a40d6d5b3a295\",\n", "    \"53e9996c1dab7a9067deeafbf6d390d4b6c2b524296b1c3a1eca809cc3c525c0\",\n", "    \"551220fb9fe7843db0b63343f46c7e9766d17e2e440db549217132a383681cca\",\n", "    \"559f5834dcc3f01353be459957966e65a545929bf0967eb03170072a61ad7fed\",\n", "    \"560b974139271b2c714689316749c7f71e50237d0648e3a4dd9273aa690a87f6\",\n", "    \"5708f342036d17380b7002c990bbadebcafdb2200a8cf1f6accf12f3c887224e\",\n", "    \"578d0978ef1da661e14e0f29e575aefc0c6effa923e6af41b164c7ae2c721b75\",\n", "    \"57e56e3ea0bc221d4d56f96b77c1debdd71d2457475555abca98671946bde3cb\",\n", "    \"57fdad8e2ac02ee3b95b9d7f34f0fb62f93f3766b38646076b19fc189c32fca2\",\n", "    \"58775d52f5517c5235a957df1a43d8a39ed7e395ebd427136adc62ea73a3f159\",\n", "    \"589dd63b92e761a731789e8b3809f7c672eb7d0ceabf410044b84e8b0365a140\",\n", "    \"58e5d6752363a535d5a4cc9b4e1a1c570e713f238e887a3deacf899673c539c5\",\n", "    \"58fd0901f7e71edae1ec8978e377b55b6824bae22cde92ddf07911837749867c\",\n", "    \"59d3ce77f6477600591d170e8bb53130a7b2aaa1829f2ce21defa8ea9f9691e6\",\n", "    \"5a2c7a6ea7d9711609251f41b96af6add49236c507fcde1c32f1b115e0e7ed08\",\n", "    \"5ae237a92851862706a355d4be346c9288ec903a37059bc831dafdf0172b96f6\",\n", "    \"5ba473bd69532d26da64a406a41e2008d812cca6a7fc116719e520b5dabd5194\",\n", "    \"5c4c58341f7bdeae64581dd82a126014fed609fad9d559f000de1800a715db9b\",\n", "    \"5db371d35075b804601c37d48fe28714115c2534549d4e6294dc8969ff126e6c\",\n", "    \"5dfdf2e34464c855e016db9a21e49a5caa676fbbed8825f3d95719d9a281d309\",\n", "    \"5e4cc5bd1d2b4166fbf1f3ac7467bb45ee0f6da2153c03aa64dac99ad9035a29\",\n", "    \"5f3baef57e7cd39ca10aef5ed3e4b8d313fd98d34ca137ae4edf80262d43df8c\",\n", "    \"5f4bef28ccbf67f03f8dca8937235b02b8ef234a0c165330c51699c2066c4ba6\",\n", "    \"5f5f6d04f4afa3f46beb30e3d2df7675363721c9d4ab971b46ca3e65503d36a6\",\n", "    \"6026ed360f9ec66815595c14313fbe1a455416eafcdc5aa4397082f31f05c98b\",\n", "    \"6056df293be16edeabccac29a1e94a7105d0c1fd03447527cd074849d760cdb9\",\n", "    \"60a60f6cf1a2463ae050abe96465fd443863e40cecc25df311f04f07af17bf86\",\n", "    \"645de77073d04fad99ebba2fa401f1bf31fc78192ef4acdeb2566a905c276ac5\",\n", "    \"64bf2672d7988c9093a1f0b3ea5a3237a70e58684b93ab33012d79bbc4ec107e\",\n", "    \"653c4dfa2f5a0c3b8efee32809680d352775299b568387b9442509031c2e5abf\",\n", "    \"653e2e83d377d8ab3fe5ae15ce2863406ab5d34354bf57707b37ffd2ef6adcfb\",\n", "    \"6556c22696613294669410547471546e4ae15b623aea276744d6fbdb64c2c7a5\",\n", "    \"656a57ff07f597db6ef82a5d7cea17f43f958ed7331a37303b614ec7c6de5267\",\n", "    \"65960fdb95d40f86eba1309e915cc076e9f839e807f28f8ab2bec9d4a3b3e3ac\",\n", "    \"65bdd54aea4745ee03724e57dcfbd56de4c876c53e163430fad65663a00071d5\",\n", "    \"6621640ac6be4dc91ffadad7bb1988cfe3645911e34510b68676d3f86a8ad926\",\n", "    \"687e573c1274892aa980475a7d80d7e2d915d7e019d585774e045d698528654b\",\n", "    \"68e52f6ac4c17c87e257cf73c236dbe8661bd878e0eee2dfd85e07732cbe583e\",\n", "    \"694cff832afff548f286890bf8a40f3a302b0380b0263bead671b507226a9e39\",\n", "    \"696ad85cec50b4f869a684c9b8eb1876febdea4f02c143162a9a1824a30a0eac\",\n", "    \"69d362fcbfc91eaca95a39d635a775830c85b792da689225a317bb33f0eed1ff\",\n", "    \"69eab36a4e4a3ec60d19b1910a81683755dc78ccd0b814dbbbf0f5e157bc17f6\",\n", "    \"6a3809b581fa8b545e4cf218c4ba523fb1c8df22daccbeadc5d3f49a6372c6b8\",\n", "    \"6a7b4615b99114e0868670b553aeb80a29f9b0f954ed4783b3e4d129db04e7b3\",\n", "    \"6b9292766c968561ad912f83d247cd0b611ae684ba68042b4dd2ca2df1b7c02d\",\n", "    \"6bc2d95c2be0fd9118a126abe092c054eba3dc8dcf28ea5ab9552f4bc4f69279\",\n", "    \"6c2b600cfc64391b2b2b104b2ee94fe73522efe88e6be5c9faee3b68e894ae3b\",\n", "    \"6c92d28746a866cff4eac20b81dc7ddafbf3b30681b8e8f621fb077616b23977\",\n", "    \"6ca7c88ed7d9b3fa5aed19d116a030d018a5ca1be5c8cdb35f4994496fc48df7\",\n", "    \"6cbe75bf0a4e4b6e20612727a5bc7b87ec33677163cf73f8782ca02ca80bc245\",\n", "    \"6db031e93235102e06076ff2729b02fa513400d572113c2a1076ff2f4532ec96\",\n", "    \"6dd13fdf58ffd6dd8e593e77033402b5b4e11161b255aca515ab4ed64d4130f6\",\n", "    \"6ded019c89f026b54a38ed151c9dcfdc5778f00c6980027f6ec212a98151f555\",\n", "    \"6e2ff6f293c3e14ab4683d2184f975d09a9ccbbbf82455f496aa6c630a28e3d1\",\n", "    \"6e85bf9f9ea1bc11bbedb14def7b0214c8d90baf292a683b0623b44dab2f40e7\",\n", "    \"6f1d937dcf940995634fe9722d664ec4d325de9df7864426caf467a4e988bb4d\",\n", "    \"6f21579eb5a862c1896c4ebb675f306a3c0cdb5660e43f7e4defc2465e877c29\",\n", "    \"6f54393669386f2cba701b4ad7665ac257c03ce15ecdd9f1ccfcc1c3de313091\",\n", "    \"6f54ecfdc39238de32059a74d5115f96aca2148874445b20ae2d38f3cbefb1bd\",\n", "    \"70ee34da3c7992c1fc785c8165786c4c24dd0e2f88bcdecbb3da96fd5f9707cc\",\n", "    \"71713dd5cc04c14b74873f857c762c9383cce1328cff83e98e30db0b0b8cb2fa\",\n", "    \"727ffbd286924c129899d11f997b4d336858e058364c5b331436891faae401ec\",\n", "    \"72bb68f5fc5f85f91149797be388581ccf11dd908b438a5004b1bc9f38e74bb2\",\n", "    \"72f0948cc98a91d937ba9050e84abe68973f4966f6c834222ec4d156a4c68c40\",\n", "    \"7367452547c6f6515c875d69daae09e041e28c83dda55c3078cf3d691965c402\",\n", "    \"74bb7e04bb4d4bcbc79a6160640270ec5b9d39c665f7f2b893e9f4c6e10d1376\",\n", "    \"74c70e708795fba1a7fe9de7768b8c9a082fcd77184225924d883b8ca30afad1\",\n", "    \"74db0645b84081ffc53df6bfac7f7f65a2f94009012418b3c7751a6910a7bfde\",\n", "    \"7550a145d9d15bbe0c7c0e71f1d7517d6cc9ccf3fe30e63e87925afee2df4479\",\n", "    \"75bf4315a89299e94011c80139b000ec20b34e9be7116b48932489f3d3f8c31d\",\n", "    \"75cfc20ed4f9d958032b532c1cb633242f1cad07b21f1be5537c2bbfc578ffb3\",\n", "    \"7677b595bce919cb504f49b0969f37a7b602be7869c55c05c942e4b96f0b1189\",\n", "    \"7684f00f05c167b676f1c428fc09bee65c007832a2a63fe6f7477e76801b0300\",\n", "    \"76cdee7591767b2c4258c46b3e6cc294766bcda09262fdd56b793648f9a3c17c\",\n", "    \"76d38e02e440fbdd86dc39fc9a4ee60a721d708a9b34f188055d1b708301f561\",\n", "    \"773c662e90f92c8d535730c42475b41462605cbc8f212c1068c36e9735a44471\",\n", "    \"77725f6d7d7cef2307dcd030c514be7e7b6836645eb687a6d9fd26c3fe2631be\",\n", "    \"78568ed7b9d3251b608d3c6ce980cdff960c74d0962c818a4b2dbb772e97e58a\",\n", "    \"7882f19d21b623a941399e4b9dad3247c61e4f77a259bb284cd2665b165cb90b\",\n", "    \"790a9c8850eb7633ba364e083d60153c8e93fc5bbf2f2d2797b749ada3f3a3ca\",\n", "    \"798dd33ee8c3060e93fd58a7f01bde4fda8d1c40126507dba9203b7db05e5cae\",\n", "    \"7a1afa4ebc6b4ade15d771f977e88ed650bb16e061ce4846386811fb762298a1\",\n", "    \"7ae1d911a070d069bc157ce7237da01c9b0d8e9f151b6b556679daf305bd5bfc\",\n", "    \"7b2f2e2406fda5cb63ab502c2ee45cd8fa29c345836a172b78b748b46c0c38ed\",\n", "    \"7b48adb756fea117195f42fca5d5981d852e3ddf1c72e157537f66792e3ba806\",\n", "    \"7b9dc392e976aa35726ce14d02d6237ab860d5913b7c1030c5fd7913053d8102\",\n", "    \"7bc48ca581357f4472f3e14c4566df93e998a4e75655bfc660588331307bc383\",\n", "    \"7bd65ea21666f56636b048a1e8f8498958ed283b6ee7dcbe4dfd14f98ffd4430\",\n", "    \"7c01a5e569171c2eddabcf8d8352d953cb72fd066336abd911f06b9a48817f0e\",\n", "    \"7c43a56e4ebff03ac7f55c5a48ea0b2921fce2f692a44e69dbeca44b6a8b4bb2\",\n", "    \"7c6263232052523b057c10252afbab71a771ee3e27f9ce0a5d19765e7aae5681\",\n", "    \"7c7fed383843c44f3340c24d7b7721bfbdc729b4eb76f512317be713c30366cd\",\n", "    \"7c9ddd4175766da9cd39eca48f19dd146c5d4d43cb1f544acd384f50b5874fbc\",\n", "    \"7ce3720ef59ea8e13b18250fde10968db9a1d72b8c6a14f153561b458f906d56\",\n", "    \"7ce9a19976f78e84bea04b5bdbf17ad3a67309e8f83dc0c207021f0fd89487c8\",\n", "    \"7e79b0e1fadb624c1f5ba8d29c40e73284bfeafe97439b50d7df0a74841897e5\",\n", "    \"7e98e1519765d25811919efc826141e7ac9dacb1c75bddf76198df2f30531101\",\n", "    \"7ed05a0917ea9833ff6596af0450be4c54eea2c7b2b3518e1869f1540b3804dd\",\n", "    \"7f8c44f624e90fa11db9426797b6c81a697b3607e1dbe264eb72beef2e78cf14\",\n", "    \"7fcd3170004a7e659146f8605bb864da2099bdc54a6b247f178cdd1e71c7a6e9\",\n", "    \"80aa7addfb3f46b2b04add8e81e033c731920e13bb02d0c66fe887dfb1f82e6b\",\n", "    \"817537f6d78b8f77c9f6ed12725707cae0cc8915d34468eea6b44bbcc759f686\",\n", "    \"817ef4477fd7b647f5eb2cf61e05175231a6bbe18894139c07ce41f273c7858f\",\n", "    \"8183172a09f2825214c35fbab70b053b1270c7bd600a2618816fb0787015bd63\",\n", "    \"819be0b5e7bcdde79ac710f5fc1e467ff30cd489db9434f0ea17e8db715018fa\",\n", "    \"81a3ff63321f3620deb5443f24e0a1f0601bf174778df17e7a795159c400b535\",\n", "    \"82e10f089896c5ebfaf3148f010d365ea126bdc27eab73db129c863da3f62748\",\n", "    \"837b708274ac893b6f7edf3691576fc1aa31bdf3f0311d24ddc7cd3e55c62c15\",\n", "    \"8409bcb6258ff52b56771ef3b5cbded013c1bfd08d7e8cf5e436b566b694f64b\",\n", "    \"842c5d8a043d8a942d9dafcd7a74d4369c739fca0b0ef471bba0fc71976fbfc0\",\n", "    \"8432d419657e578dc09b411bc537cfbd477f18a531d843443fd75db7be5f6e26\",\n", "    \"8491167187025f409ba69d549ebab8a6ca2ec22f216cbeb1c17851b47a1754bf\",\n", "    \"84afa881972877d60a52a3ad1664eb26e002b4ce1024a4a2351e95920cc3c31e\",\n", "    \"85c659afb5fcb0d972ee60171527145a1219d4ea52776cbf3014f811dc7f8929\",\n", "    \"85fbd8b1de5317abc03119be4194c74d3daef31860445f0f2cba8aca5d8e316a\",\n", "    \"8645364290b0aa4d27cdd93405971fac49b9251fd9a13957c1264df494daa8b7\",\n", "    \"8648a69f7b4df5f21bec32f71fd0b61161c5e0c48f1b36a2020fc64c7f3fe40b\",\n", "    \"86f382dc3b10ab4184e194d9d8023fec7614965f07c9f1d4b57a1f63daa643bb\",\n", "    \"882401f1a1c79673a2db5ad742bc70cd5178c6ee8b531c300b63d385ea58dd02\",\n", "    \"8852fcc5443fc8505634407d9fe0db111f74ef953c546a62a80b1b0d1134db68\",\n", "    \"89b250ef4a7b7ecdb27f4927679c5774926d894e21d3ce8270d01ec533e8a1d3\",\n", "    \"89b289545f988c63a17b8954206e9c75a96f5c13b7e27631590f8529d26a3639\",\n", "    \"8b4024c3abca1ea1884bcb1b982aa43269de30583612c6cf30da9e2ec07e113b\",\n", "    \"8b69c54b83f5686984d9c98ef91c954a64b645761cd2396f76051218f566f0d3\",\n", "    \"8be1eb56e07cf9642555b7690628ed267dccd76ad75df1b66d3cca16f84cbf81\",\n", "    \"8c903b02d294839fcf3e558b3ad7148f7ffb4d48a63eb1169f7a3226522a222d\",\n", "    \"8ca8802535ced62e2113404a51004484e10c19faedb27b1cd383d8ba5313c2f2\",\n", "    \"8df4768cd39aae578658b7521c270fc3551d1da611d3078d4bca1cbb1cbdd428\",\n", "    \"8e17a2ea069aa8dd50741a6d45766c885c18fc7159f13de59e75fb1cb74a9e8d\",\n", "    \"8e815f00e8efe14582a9ebadb001deb7944663c4f382cfe4199fa0958344ee16\",\n", "    \"8f284bccecd05e168666112637ef6569dd7ef5acace8adcfd4de7a027969d6c4\",\n", "    \"8f3cac1a275d779a9953db2a9c3d0d069f8846fc7fb055f4bf30a3b401d09fbc\",\n", "    \"8f6efe8b95006ddfd2bb69ab838c2bff8cceba199c1752434cf2c20de0bb9bcc\",\n", "    \"8f7c2ef89d033d99f6b16bbb9f9057a75a7a3daddbcca4e9fffd6d8a53fd28f5\",\n", "    \"8fad6cd39c0554f0c23014dd61f7f0117136d88c221883f3e9f041e6e03d3b06\",\n", "    \"900adf248c451ff56afa14df046f67ddf34a71ab91a9630269c66069ca6baf9d\",\n", "    \"905065e43ee4008cbf2fab32ab4aa708c1ca619968cab1a075a39a4384e9cb9c\",\n", "    \"905ec9c9bf920947486b70e07b8ec5511d4f05906dd7e8896ac3ed04e7cd585e\",\n", "    \"90a31dd239ab46580b7f784935e81ebdffbc95e2ec753ebb1c2e18cbdf9e42be\",\n", "    \"91d95340d1f94368fb0a38621f3182e1f154dc0e473badee397f530559045256\",\n", "    \"93415bed7890c99cd75cb51e3bc946f894f1a5c262ed31c2b8a68b104b1999db\",\n", "    \"938eea76c2ef724d9a1c750549e92b48a50528b868386c83bfb777b4cfc02742\",\n", "    \"93f079f7ea7cd23b4d452c08eb9ddd0e768b9165b81e68a1985942ed78afacc0\",\n", "    \"93f79be18a361f923d5d05e238c4a9305a74a07462d16c199f0dcafcca071ebe\",\n", "    \"941fcea24338b9b0468680acb0692c9c4f3db099be61fb012d61d26b5ce2c093\",\n", "    \"95456023702b9d0f8a3bd835e9892906423dab8d2bab72e1f55a87af5b451495\",\n", "    \"9631f06c01df8d3c05e04c722140a55641913b54cea5b9c3400eb5e394a6fffb\",\n", "    \"96b3b2e8ad98cb21db0db098f37c03d91bc9d7b7c0720685f73c2a35c5951c33\",\n", "    \"9741cf2560335d8d09fa097b3347ab808f88da928a8617adf2a6428e00df3d26\",\n", "    \"9750d5a55763d4143c0343761104e825d553aa2919ce5e43d3c0ff2c907f44c7\",\n", "    \"977275ecb04edd329cc4a1581ae3563035e8bf53809f3a733ace3125c60e3520\",\n", "    \"985582a0cc881e5c80956ef5fc2c41e5ca16b9af32a2c1c834542400034f2836\",\n", "    \"98a6308e31c3dd2877efd9e29f0641b52ad536a147244d51617813af9f0779d3\",\n", "    \"98b2dabab1cdcee663df09d7aafc1bb7052d8b4ad5b35280e90bec1463d5d40e\",\n", "    \"98dafd9ca852471636f1b54953289bb1813cf852bf38dd93b5adf29d5ac589da\",\n", "    \"99332cf153439672f30c076c0c6c125cf78d02b40ac416c42fcf2841d3ef2de9\",\n", "    \"9aeb0f5f21d77734c9f6ae415b9451a1ad14c08693769e87668bf6ce6d03ddc0\",\n", "    \"9b09c8ef44bc9f943788da8647270fe79494fdefd6f4d9cd0d07cdd7079d9cf7\",\n", "    \"9bf8c798e9041b6b89277fdf6076d9100cde854618783b5f67f024bd070042bd\",\n", "    \"9bfdb1111877a87528898b765b913d215ac0967a6343ea213b9532c6773c351f\",\n", "    \"9d43475706e76af10774ed8a19a5d3cdbc10497cc5e23b3b6f38c958aec251de\",\n", "    \"9d56cb2c347ee17427107ba7c8d3482f385b008232cb901cf59ef872e8124244\",\n", "    \"9d701c5fd455596f695b808f10c3237b185378e7e20c270e663fdc9c18d1ecb1\",\n", "    \"9d83e31a6d6417fbbc35913f28f59054937de3359d596fe434bee90c78dbcb39\",\n", "    \"9dd9946b046e53d092686703642a51960bd67049f2c0c69847fd30cf2fe2198d\",\n", "    \"9fbe3fc572ad44ed5cfea6fcf25613d98ffbe2b5a6ccf2d0d9947d089afff538\",\n", "    \"a0e4c4334d29e8fd32fd0524bdf903adf357ec923904b9f90e48e52131195254\",\n", "    \"a162cc56c0c5336769198f8ea2787446fa546351498f2862985bf9c7a37d45f6\",\n", "    \"a26b4b67713d4320212d154674d11f6b15229e47861c17f796bab1fdf84bfdd7\",\n", "    \"a28f7302e6ee612f7408f7aeed91c791de67047656ea24cca66c9fcdc4b0c4ee\",\n", "    \"a2ca6f985104435add6a5b70718fc676faadd7904d6dc00f6fc5d6ff2ea89fbe\",\n", "    \"a3053f828b3d1fd011fda66887a04909c1c6bab68f67af874bf123cf9a9a52a5\",\n", "    \"a3534f910fa30d60cf8b9f9abdffe6916866129035bb4aa681c58b0e7f714366\",\n", "    \"a3cc31af6215e9aaeb32fde96159807073a6142f8753982f2d083aa3eebcb503\",\n", "    \"a40491b304296cf9d6af26eea5e8e9058f1a6de6181a93c8891a547901ad50b0\",\n", "    \"a43b4f35abcb813ecdbd3b62eb703f7f6985183e248079213b4a598c372dc280\",\n", "    \"a4bea5d40e80b6942754cd4149741f2473ed820e18c42439834d6273cbe58cee\",\n", "    \"a5705b021acc048445fe2b92a69082e23ea0d8d81a8900365111b14d2179b12b\",\n", "    \"a5a4d24ae64691bbc856dd064a822778c9c43eb94c36c1f87d58477ea28bd15f\",\n", "    \"a75dac92546f909257b21e9bdceee29202e4faa4993246a5bb912bc6b8e94a55\",\n", "    \"a8f4fb58aca145adc073e6fc3673ca9e9b9c60f3df194fa70ae0b48ae2a07bc8\",\n", "    \"a92ccd5aa7f863e7fbeda6ee1f9546aa26fe9c73879f5092f2d74a78a98881a2\",\n", "    \"aa91a851095bbf70bea3543b9e262468477858e2897d1db160e56e8694fa3216\",\n", "    \"aae4622999e18b2fcdb2371b8962829caeaa3bcc25fadd258045078fed140c56\",\n", "    \"ab141bafa54bf8f22e771cc38fa3a3c371c55edf9a814e7defa359c42531164a\",\n", "    \"ab38daae78425cd03c287bec299d66cbd59bafced9b0a46c9d4f8c6690f2dabf\",\n", "    \"abb2fd308494578ed660ac223b267ea258639009cdee5a23258a20963ad5ad04\",\n", "    \"abbf813f936f5255c397bee5efd9a9fdd1b3ef4d3ea1527da95b490753c86b92\",\n", "    \"ad04c98ceeee3492ca7ca8391847edc469516446896c533dd62816d5d59316c3\",\n", "    \"adc4e4a8fb7e7005856d4a861468e5b62d63bbf3c2a99fc2d6a13d6df1773d5f\",\n", "    \"ae4161775b4fae844ecf756c925fce1b53256773ecc4c31a48a8475f18862808\",\n", "    \"ae72408ebc4410475aba36c8cefc53d7952b91f2c064502dda560fbac4d3316e\",\n", "    \"ae97fbdd18aaa2b116e4c294682c57e6d00bdab5aeed7d4d2ea999bcfe50b9f7\",\n", "    \"af8d83aaf574bda5dd4c15aec51ff5ac1089ce64e4965166a60ec2a514fc53ba\",\n", "    \"af9b7bbeb19398c1029232036755efd34e1b5bf08228646d95c5cf39a51e7306\",\n", "    \"afebdc701929b95bbecf87e33925403a4ebe871a9647c56c64ec248a1011caca\",\n", "    \"b02ee509411eda4179e981bd9d5f3c20ac8ce1ab5ef63e390597e0214a1bf178\",\n", "    \"b066933b1961999e0a1927069a28af3d1f4bbd6a855e36940bd5a1fa9e3f9736\",\n", "    \"b09aabbd13b4a371161503c4840ea061650f3f4b7496397843785cff50e2e142\",\n", "    \"b12182b58e7a2b0e766002091674d1c1beee9ebe9e3c46f8331e5150f6d20df2\",\n", "    \"b25b8fec5eb16516f5ad32e1176d82aeb553708813ff95b19f6156056774736b\",\n", "    \"b30f8437f875a5d0a00513225b0ede05fe952ed1a0b85a719fcbebbb64e2dde7\",\n", "    \"b390a2ebf2f3bd8d1ff0b15b11124c7c156aceb157a8f157e05ec0d0865e98a7\",\n", "    \"b458dff9878187d7e9ac96f8102ca5509359d0e53f88722b6c20c9e312b23bb2\",\n", "    \"b4910955841a3c77d1d482b35e35192296650a3a49cc3d2cfab0e24b14073508\",\n", "    \"b4c8b657a9977b02fe25cc7f39d5c70ba50df0eb36c58e09beb0479608d76e3e\",\n", "    \"b5bc8cb2017c6644db8b158d8c41769ae7153d289269481bd2f15786b00435de\",\n", "    \"b5c04829cbb53e6da22e104b8d28ba299a0982b6dbf8161a5ae383e9515f7707\",\n", "    \"b60be8101b8d2af56b1d5b5acef8b1882e6918f09e0aa7d5a3d80120cf1da452\",\n", "    \"b69c0c1d58837b4f86ca49b4dda38262888d53c07bf764d61de78a564a2d1424\",\n", "    \"b7016abd837359c27fbbf779a1b9ebdff32dcf953d9b6865aa2842b8e41cfdeb\",\n", "    \"b7699a4f1ef44f6faa876f1d95354b000d44ed163450c87ae6a2fff0b10e6db5\",\n", "    \"b8257e539a54b090036dd39b07bbc7d5def9fa7ef1e3617398e3281b6c4ee7b9\",\n", "    \"b8461249a2829fb9c1c9228eeef8c2e9e9aacae142c11211dd3ec1f5005878ce\",\n", "    \"b8d32875a70376a6b0659714847577acad27b14d8db542f81cbf17b25b5e01e7\",\n", "    \"b91a7ddac6c34c88fd6b9c3179fbedb4a742cab27c8d4df4a0047e69406094d2\",\n", "    \"b9a3d5526b49629a1bf2371d58084eca32fe239367b466730c62b0078e3d11ff\",\n", "    \"bae809ed5c7c6cdf4ddda898e10544b2d0af0ff12500e2e9bf08c83bbf25d8a5\",\n", "    \"bb5d266f8fa318d0cbcb1c33174fae29bd3b9dacd19115ecc57d13307f0c8c2f\",\n", "    \"bb8c35ed1e3b749db951887112454b7713e0570a87f4fbc74afe63e8babcf589\",\n", "    \"bbcdcfad7635dc32d6e621dd9be0c8ef4e16c2bcf2f18d0498ff0f1c54c51c9d\",\n", "    \"bc65b63110f1ddaabdc83771e63f32dcd4192e38ba5f22d6e4613018681d2e25\",\n", "    \"bcca71ea6be5abfbd33afc2bd326b54da1dabbf2775ef1cb492fa11b1fcdb37e\",\n", "    \"bdcd97520f14310c67cfd961366d09abcdb052da4ad8e80bd6f2f572a5d36008\",\n", "    \"bdda6199d5cfe11bdd6a5c128ed6e392b5b1c9b51f32c3366f0fa01ea036e0a1\",\n", "    \"bf06d718758f9632d7ae060a9f660ffd7e746086fbc58157003510c92e2e19ed\",\n", "    \"bfc1f26e487e6a92566c7a9ab5d954fad3b97a16d4c951f2ea36ae80b6156005\",\n", "    \"bffaa0c20597d0e7bf54a9b634906956be3113652212c2423281c162c072e153\",\n", "    \"c0c1f9410041ceda6aa66150886d6f1723c2ef0600b5d6586391186d6a228a8b\",\n", "    \"c11b22eeed3acf73ad296c8e64877143d455a601b0bb4899c28efc3d0d4e1487\",\n", "    \"c183a908025b7fb5677bcbc6ff85cb08bf2e7d9cfcf8d00a5eb21ddf36427404\",\n", "    \"c1ea5d422edc80467fa2e9228fd01732bf40d520c7ca4748c8682b3ab0084e31\",\n", "    \"c1ec985e6bbc8d1b16de4c1f35019edfcd534a2de8277b28e44e56a0ed8e9cce\",\n", "    \"c21451b17e297a6f5e6c67e5b0266d23d4189d5ad0bb912ae9a7c5fe1382fab4\",\n", "    \"c2c8e5b7ffdb881d4a3b59c5137a867d7ba36c2d792d3644c29a29eec1071dec\",\n", "    \"c2e8567b44628733246cca840d9c982424bc25a8b7321c86756ed797b9e8263b\",\n", "    \"c3012438ea9df4eb7a94d21d329b0e2dbf0a884743e9eff04eba8c42715c8570\",\n", "    \"c331bf254ea2e8c1147c39e0a948fd294657d9082937a2a7ee1891015a38c2bd\",\n", "    \"c36d64c2c5b9c904f532cdf3252a268dd231d6817cb40a782dc0cc85de510447\",\n", "    \"c496344b259d5d100f4f9b09fb1c05f5f3113c6706fa385ef438002e6cc4bf75\",\n", "    \"c683b2b14209c0fdc6bdf174d9dd9e05e7b43f20701d70b0c1e2a88e6ce097d7\",\n", "    \"c6fc071e79e32d4831883f4ca6c065fb57f19199483ed6106fa72827cd9d1ce8\",\n", "    \"c7048915819fee1bbd2b210eb5e3224453c530091cb6b2a8d5b70007cbc16a49\",\n", "    \"c73355952af1da779f9827b0281e0fb1772ea4e40a55443b7a766f8d2bea7f57\",\n", "    \"c7ee6baca2443c05a756662ffe0810b7c54b6496bb7834e151eb698a4381a2b0\",\n", "    \"c87de66528fd034f65fc449668e8fd2b22db5cb9791ad0ae9897cd4271f361e0\",\n", "    \"c9fe95f7cba665a475767bcad98cd3a1dcb936089eba33b2a2a25bad59ff061d\",\n", "    \"ca878eb494278314e7f635448f8236b33ed0a9c4ac54880988771cd929a301a3\",\n", "    \"cb25082b2d430abdab5700d73864522c8309a319918c8b3b6e12249a3d0e452e\",\n", "    \"cb7b9b24ccf470fc06739b97961875ea1bb5fe33e4ba3a3708104ec072cee6e5\",\n", "    \"cb8bdfe025bb22ab36cc33eb056c50c01129b770c0c49436f921bdff088e496a\",\n", "    \"cbda50abcccb3265f13421a3c78f80264f3f50abab963bbd763d1ddc00fcf93f\",\n", "    \"cca31c898c6a79d080ebdb274ac401ec448ee532cec6b3716aee300123b7aa89\",\n", "    \"ccd3d8d9e88293000bb9c20ab1e758952940ef7efbee903a1d8ec56ec52dbfda\",\n", "    \"d0dff21944ca4478a5f5b65a01d8f1e7a25ea912dc2a2f41adc4402e81652863\",\n", "    \"d12c3313708125f68376e68de7c576376c28aa80303633e25788e2a5d171187f\",\n", "    \"d18557569edacc3d9255e5deb5dca03a719185cdbb549533f35c7f96d3636340\",\n", "    \"d1dabf4bbb883c4ba3f70e6a12ec175bdd4a4f96e43b6e8dc236594225d4ebe1\",\n", "    \"d23ae8928a04615c051c8ba4b92f362a9eefb922a43bbe2090d5339bbfe1227e\",\n", "    \"d245e56f344105e76c6d216c26767988b4e381c1e1d09bd0f55f88b2fa20b758\",\n", "    \"d2e963c8791b6fdc04a979c234853f6ecc9aeae2474ae07fd536434bc4f7a9e7\",\n", "    \"d47cd815f16047ba28c6af27801c923682242a60f36f47fa9c0c38e0c5832d3c\",\n", "    \"d48404b17093747244085e4eb15b75e079446e391a5d81665cf632c1eeb7d1f9\",\n", "    \"d4c2b8262f10c4baff4f27d0d7dcb22ebe609ab70aee9eb82f5c44abaeef4c95\",\n", "    \"d4cf3777611dc5ff70868b51bb163a97630149468e18c6512d0dc21a5e8b1ff1\",\n", "    \"d532ba221e29a6db2d128d2f660f8aac37cf168217aeb8d0f775d1c9ded8c25d\",\n", "    \"d749191fc0454f6ce0fcde4ab59982c67784b867efbdf7a4e2a88f3188edf963\",\n", "    \"d78fa3b5303ff2b549f19fc0cf4116b3f4401cbe3bad20bb9d572b498e3d998a\",\n", "    \"d7adbbbf161f55aec03b191e98f980abe362a7fed3e230e044278c9f99896dc8\",\n", "    \"d7b79623b36d1c6ab844427f0e1644987caa8cfb1c6d5986651a171ed5f6a00e\",\n", "    \"d8adc4c9ccc5fe5d1eb85a9d3b2a8d6ac1fcb209434b53b000a73fadc0bf0269\",\n", "    \"d8b03f193d2603c8ec94b79dc2ade0d46538f5aa7c646d8e35bee7897dfe851d\",\n", "    \"dac77090f216a9ff8ee88ae10ee8f461430aeea0ab1b77bb487a3d629efc998b\",\n", "    \"daf163695b3ec15994a4bde3590b9668b459e878747952de9c78825c66e614be\",\n", "    \"db79dfc813fb17408fd03481ef880c5fce6a88b93aed8e866797b43eebf2df0b\",\n", "    \"db8e0f9dbdae50c2e4d710e2a8372c9a0bd7a8febcf3984cbcfbd47e6b8623f6\",\n", "    \"dba9392c4e2f9c95184600940876c870d1e43141b77af870c0f9b94aa03813ca\",\n", "    \"dddac9a7ce7e1dda1b73566de07e10005420567df09de443a08c738f910021af\",\n", "    \"dde858d26d6bdcc467067b2af3ccad8a068ee4860d90c14818ef5f6ed8b91826\",\n", "    \"de1a864238f17bf536c45c81a06e8342c75313178cc26b4156fc19254eb0ab3b\",\n", "    \"e0a97d39912703f2f3b1aa61410f13c097191a8a5add2a97c4d6a17a828e0599\",\n", "    \"e0cfaa31e1195a400f386a8a78274f9338e99ce21a2148d9d3b99d0c417f489b\",\n", "    \"e12df953ca9518e2d021b303e1c6406b8b6bb96ed6fd089ca9d5f2c1c2863e97\",\n", "    \"e1c6ac94d8b348bb0e92c8894cae3e8d0be10323f2bf4d7da663c8cc2c528307\",\n", "    \"e1cca4c3b58c7676f65decc3728ec7fabfe622d2d93b93d09e9565ade0fcc07d\",\n", "    \"e1d7f71e3d15594eb85d5487d53d9bdc1e7d31c619b88be3b96050b87e3869ef\",\n", "    \"e20b127dc86f6411a6b7ad40d3a05692e0f6510c34198a43b11bbd259534d32c\",\n", "    \"e26201d078d57c94103004c6f322c43a53e3c7e418792cab7e3c7735f1022714\",\n", "    \"e27f919196e07247f4fbf79b1b5331c3777abb6b3b5226c0ec79f7f6dde7f424\",\n", "    \"e2cac4ae80794b175f6378a6c4abec8504699cdc535908933d938c12df5ef950\",\n", "    \"e2db78b09e69df00182e3116419bf8dd8d9b2af301bffd9fa35f4196e38d07ae\",\n", "    \"e38d65c25dc91aa15c3cf894793ad949740baf2446b46ef1f0e9f4599207ec18\",\n", "    \"e3d2d5f34dbe8ea359173ac38a04bef5cdb20f435ffe77ca0e8d933f466b4bfd\",\n", "    \"e464df5816d67064f9e6fb00229b5f17ce79b7780fffab8958ced1f7a1b3fc16\",\n", "    \"e55522130e329e24de84dd5272af338294010c1e731e627d9deb4dbf76e4889d\",\n", "    \"e58797c6b02ece5235ce9c88fbf276ae5dc942807854c14b3244e2fabac6d271\",\n", "    \"e5f82e95cadb741e95041205a4cae26d72686a275d4000ab8284e2717019a4b1\",\n", "    \"e610fa8239e0bc1e1754f1fb80167dba2c073a7018e7020511034f8c49a209c6\",\n", "    \"e632bc300fbc2ff537efc0926dd2cf24f868bdc26bdee93c1be30ed0252b910f\",\n", "    \"e6779c0c65c5fc7fe3ffb236a2761b1ef27f2085e7fa5ff8ac57a1af93e3b9d4\",\n", "    \"e6a3d1c0ee5c71f3d6b2274d07d8878f500f6ad6f8778c60108508b094e289be\",\n", "    \"e6ec511e5fa085ca1fb3da02558425cd6598223ddc64263c830434c9e9d64c13\",\n", "    \"e720c6694189fc0cd49d84ec95f8da86ddd2536eb2036b2fc348e60801472582\",\n", "    \"e7c851788c2007671b0ccd68191ba847ac24984c2720c2dbedf0cd24ba27df41\",\n", "    \"e7e34e65d43b69d536edee1742548b2fc3c10c577273169fdf043867a2e4cbf7\",\n", "    \"e7ff8434f96bbd6e99c523447b03d743e2185feacab9bdfe87e300171b615afa\",\n", "    \"e8e6c7572d35a6ac1387851cf7506810fd0ffc9d9e68ab3cb2d8ade24fd2d094\",\n", "    \"e9591a6933b5c1c2a91be38f7ce8a17c2b58a806be216ca735d3221b19e9c3c5\",\n", "    \"e9839f235de91ebe455209e7a1078c8600f312535e59333a22608980722115c9\",\n", "    \"ea0295c91f1d457931c6e8a5e7bfdb5951619389feb9766a7436d057e1d162ee\",\n", "    \"ec6c04b5b297750df7878ef38c2c08f2529c06139320959fb2164d74c85da83b\",\n", "    \"ec7a8d971cc0ac366e7f65b950beb24e639043ef2dfd19a4ec6e61cde81652ca\",\n", "    \"eccb06384db84f62d2bb73a1e8f431296c3d3d8d4fe5585b75fa21c669638ad6\",\n", "    \"ecee80abe4dac8a48c78e6cf658d1d2fb1a92ea3b182e149cb7856a3552d4cf1\",\n", "    \"ed34269a833fc4a9eb97bc13c04b4e6ba1698bda276b2c754371423eca22ee01\",\n", "    \"ed7865f7d80e2195f62d57e4e05364c4e55dd751baae95214f5bda0179ce6224\",\n", "    \"ed966695fb2edd4b287f64ff8515912ef95eab2ab553fc4309ab44a13d3bf2d8\",\n", "    \"eda27cf95d5088562588c90917fa9c60f1c625b013c3b3fb2bb03a0ae16fb8c3\",\n", "    \"ee17c33d4dadf77d72124d8ed4f795750dd4bfcf961e2b5f9fea7470a54b8993\",\n", "    \"ee60f34b9e9996cd009d1d775854d83112c76daa23c8bed2e53a399dd9b0f16c\",\n", "    \"ee8101946e59cac924885ca151af0fe07c2409f10a43e971d2788bb16a6ded2c\",\n", "    \"ef7b436dbfa960ffb91d30c3df58b683246c15d52bc6aceb31fc1d19877ff1e8\",\n", "    \"efc3278db1c6f245eb648c03cca9df9e30c9526f5a71236ce6bc7e5629d700ce\",\n", "    \"f00cddf75831f7e9e67d36c74247b98e1c76f4ef537d81d492085699e0beb2c3\",\n", "    \"f0b44dbf9f3725ecab590494ba4a053e869632bd42811b45deb6e9d739e73995\",\n", "    \"f16f7b346082574234f9942c0b115bd0a77743ce41977f3afdd828b6d3743137\",\n", "    \"f1bf5ecd391cc7f46f79a79bbd1f2e3dd21f389596ef373cca09b8f9c9e7b745\",\n", "    \"f1ee67a3a2c0f8eb23ccd5f69b78a68a5f41cd36cbb932ad574a3d59f198a04d\",\n", "    \"f249c135b453de5d3243c71c38d08fcae809092d39c67578bc6f9dce35a4321e\",\n", "    \"f2c226f00bf51cb9bed330ab0143d96b373be9f7f362e7c0a6b77089dcfcd1e4\",\n", "    \"f2fda3146e60d18cdf5e667110c4e5190580ac8fa21429d6d7e8665a8ec5ec5b\",\n", "    \"f328085201aa9928085bb7b297fa95623f5e80ea989d11d0b9dd486cf15fc208\",\n", "    \"f38808ec35d96406a3e6ea9fe868ed0298c4861856447d24ca655ac870957ac1\",\n", "    \"f3a5bedddddd2c5297e9e7fed87fc0ad83af5cc62e5bb56265355757a937e627\",\n", "    \"f3df2a1d9422f26d5b72f1829920a4a60ebfc6e77140761dbcc8370180716f3a\",\n", "    \"f41433c343bd0370737a3d996a2b644dcad5860aa9b2d4e4ec4d161dcfffe14a\",\n", "    \"f4447406c6af1eecda49ba35dbd414f6b5dc7f70cea342778ec1875109d98b03\",\n", "    \"f5903e5aec123353df853997d8f10419342069ee12d34671e2aee003b9769c19\",\n", "    \"f5c7fe83f8da860c2e10249780b8475db96c0f8bb8af9a89cdadcc52f0ddbbdd\",\n", "    \"f6773bd6a38d368ddfbe7f6e0555994db0dadf92a7bef8fd2e92c3da917c4212\",\n", "    \"f67a6b503b032761de3d0a8bbfec63c1bbd0bb4dd9330df52ef7af6a984300ef\",\n", "    \"f68d191684d5f440de28656672ad599392262c3d8db07f4af09657fa45c59381\",\n", "    \"f6edd9dfff2c1634d42f1daf2c3de5269748be9b63bbb8ea6c7dd3d0363df259\",\n", "    \"f75d3e12721a4b4770d4f75adbd34eaa0b4e32bf71eed3a4d741135133158141\",\n", "    \"f7c62a7bf60eab1365af3f23bed9c59cc784b159645cd22f34db2921db24e57c\",\n", "    \"f8581c676491281d5cfa73ebc0ddfb88b2fbcde3bfb71ed40fcef5af5824eae6\",\n", "    \"f8b7af4c1aaf3514693a7c948d501995db5fe48f0fb2a802638f55ec2e687a5f\",\n", "    \"f8e45f920d529b874c43830de70b029a5d0fb30184a9ae4b54f2b05ff71ec133\",\n", "    \"f90b05b7cbeb5e01ffa0d9c92fda2e315069334b2097d6934109205615922bef\",\n", "    \"fa6ec842799e0e01386fc35706d22d26a1171a428a5e7edbcc2e8ac1d77f86a4\",\n", "    \"fae5d8f59a96d914236e1f42055def1bed45ff0978465099ba62fc33da4de26e\",\n", "    \"fb21c00bb0dd254f74e056e4c13797e1ac11e67c2030d4b3f02617272290b784\",\n", "    \"fc0e8ba5b15d2b8bb4f1df4fe54b3ecd1d608b393643738ee84177319cb7f859\",\n", "    \"fc431dacd357eea2c4faf767e4884da7ce656a122cdaaa1085dcc2c06a87b223\",\n", "    \"fc5c9b02b8f1fcf2444364bfdf5768e931348608ee6ef8f81f0645c752ea7b0a\",\n", "    \"fc7a15504c5d31340657439987a90ad5df99ad98decc4506d5932ebb074d0523\",\n", "    \"fd4e77922ce798755d82f3f00393559ea2fb61163b34733df87d22d4aeac2a69\",\n", "    \"fd7a72fa5c6fcb607eca165cfdc5018d4546cedc9ec06b719bf32a4fc0b575ed\",\n", "    \"fde38b9e4abeec95b5565a4a168056f61ded2b11f46d56cf0fc9456a840553a2\",\n", "    \"fef55a6ca28afe8dd5c8e6b30f040851db8b604a8829eaa8a7eb261c06ce3926\",\n", "    \"ff1fb492ed71849868e45d269f6f45533431b6b54ade7173a1980e02edb6e533\",\n", "    \"ff2aac965a84c2b090e66e66002e88ed6f564abfc98e6957893da35c812c6c9d\",\n", "    \"ff43fe4ca053dc58fd639dd97481326678e66138fed25ae8ff9954a7358df9a8\",\n", "    \"ff6987695ee5b38c7ca79a28d54e22bba333d65e7dea7bb04f2fb9d3920b1c87\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blob_contents = list(cache.get(blobs))\n", "paths = set()\n", "for blob_content in blob_contents:\n", "    if blob_content is not None:\n", "        if blob_content.path in paths:\n", "            print(f\"Duplicate path: {blob_content.path}\")\n", "        paths.add(blob_content.path)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}
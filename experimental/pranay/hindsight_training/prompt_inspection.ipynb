{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "import glob\n", "from collections import defaultdict\n", "from research.models.meta_model import RawGenerateOutput\n", "import os\n", "import pandas as pd\n", "from pathlib import Path\n", "from base.datasets.completion import RecencyInfo\n", "\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from base.prompt_format_completion.overlap import modified_chunks_filter\n", "from base.prompt_format.chunk_origin import ChunkOrigin, ChunkOriginValues\n", "\n", "from research.models.meta_model import GenerationOptions\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.tokenizers.tokenizer import Tokenizer\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "import numpy as np\n", "import gc\n", "from colorama import Fore, Style\n", "from base.prompt_format_completion import PromptChunk\n", "\n", "import dataclasses\n", "from typing import cast\n", "\n", "from dataclasses import dataclass\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_prompt_chunks\n", "\n", "from base.prompt_format_completion.ender_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EnderPromptFormatterConfig,\n", "    TokenApportionmentConfig,\n", "    StatelessCachingConfig,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from research.core.utils_for_file import read_jsonl, write_jsonl\n", "\n", "from base.tokenizers import tokenizer as prod_tokenizer\n", "from collections import Counter\n", "\n", "import torch\n", "from base.tokenizers import create_tokenizer_by_name\n", "from pandas import DataFrame\n", "\n", "BASE_DATA_DIR = Path(\"/mnt/efs/augment/user/pranay/hindsight/\")\n", "\n", "\n", "tokenizer: Qwen25CoderTokenizer = cast(\n", "    Qwen25CoderTokenizer, create_tokenizer_by_name(\"qwen25coder\")\n", ")\n", "st = tokenizer.special_tokens\n", "\n", "interactive_stop_tokens = [st.pause, st.eos, st.skip]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_prompt_to_middle(sample):\n", "    sample_list = sample.tolist()\n", "    try:\n", "        middle_idx = sample_list.index(st.fim_middle) + 1\n", "        return sample_list[:middle_idx]\n", "    except ValueError:\n", "        print(\"fim_middle token not found in sample\")\n", "        return None\n", "\n", "\n", "def split_list(lst: list[int], delimiter: list[int]) -> list[list[int]]:\n", "    result = []\n", "    current_subsequence = []\n", "\n", "    idx = 0\n", "    while idx < len(lst):\n", "        if lst[idx : idx + len(delimiter)] == delimiter:\n", "            if current_subsequence:\n", "                result.append(current_subsequence)\n", "            idx += len(delimiter)\n", "            current_subsequence = []\n", "        else:\n", "            current_subsequence.append(lst[idx])\n", "            idx += 1\n", "    if current_subsequence:  # Append the last subsequence if it's not empty\n", "        result.append(current_subsequence)\n", "    return result\n", "\n", "\n", "def extract_tokens(tokens: list[int], start_token: int, end_token: int):\n", "    \"\"\"Extracts tokens between start and end tokens.\"\"\"\n", "    tokens = list(tokens)\n", "    start = tokens.index(start_token) + 1\n", "    end = tokens.index(end_token) if end_token in tokens else len(tokens)\n", "    assert start <= end, f\"start: {start}, end: {end}\"\n", "    return tokens[start:end]\n", "\n", "\n", "def read_parquet_files(parquet_pattern: str):\n", "    parquet_files = sorted(glob.glob(parquet_pattern))\n", "    dfs = []\n", "    for file in parquet_files:\n", "        df = pd.read_parquet(file)\n", "        dfs.append(df)\n", "    parquet_df = pd.concat(dfs, ignore_index=True)\n", "    return parquet_df\n", "\n", "\n", "class DenseChunk:\n", "    def __init__(\n", "        self,\n", "        tokens: list[int],\n", "        path: list[int],\n", "        text: list[int],\n", "        path_str: str,\n", "        text_str: str,\n", "    ):\n", "        self.tokens = tokens\n", "        self.path = path\n", "        self.text = text\n", "        self.path_str = path_str\n", "        self.text_str = text_str\n", "\n", "    def __eq__(self, other):\n", "        return self.path == other.path and self.text == other.text\n", "\n", "    def __hash__(self):\n", "        return hash((tuple(self.path), tuple(self.text)))\n", "\n", "    def __repr__(self):\n", "        return f\"DenseChunk(path={self.path_str}, text={self.text_str[:20]})\"\n", "\n", "\n", "def retrieval_section_tokens_to_dense_chunks(tokens: list[int]) -> list[DenseChunk]:\n", "    \"\"\"Converts retrieval section tokens to a list of DenseChunk objects.\n", "\n", "    Args:\n", "        tokens: List of tokens representing the retrieval section\n", "\n", "    Returns:\n", "        List of DenseChunk objects extracted from the retrieval section tokens\n", "    \"\"\"\n", "    chunks_list: list[list[int]] = split_list(tokens, [st.ret_start])\n", "    chunks: list[DenseChunk] = []\n", "    for chunk in chunks_list:\n", "        path = extract_tokens(chunk, st.filename, st.ret_body)\n", "        text = extract_tokens(chunk, st.ret_body, st.ret_start)\n", "        path_str = tokenizer.detokenize(path)\n", "        text_str = tokenizer.detokenize(text)\n", "        chunks.append(DenseChunk(chunk, path, text, path_str, text_str))\n", "    return chunks\n", "\n", "\n", "def separate_retrieval_section_chunks(\n", "    dense_chunks: list[DenseChunk], retrieval_chunks: list[PromptChunk]\n", ") -> tuple[list[<PERSON><PERSON><PERSON><PERSON><PERSON>], list[<PERSON>se<PERSON><PERSON><PERSON>]]:\n", "    \"\"\"Separates dense chunks into line chunks and recency chunks based on their origin.\n", "\n", "    Args:\n", "        dense_chunks: List of DenseChunk objects to be separated\n", "        retrieval_chunks: List of SmallPromptChunk objects containing origin information\n", "\n", "    Returns:\n", "        tuple containing:\n", "            - list of line chunks (from dense retriever)\n", "            - list of recency chunks (from recency retriever)\n", "\n", "    Raises:\n", "        AssertionError: If a chunk is not found in retrieval_chunks or if recency chunks\n", "                       appear after line chunks\n", "    \"\"\"\n", "    line_chunks: list[DenseChunk] = []\n", "    recency_chunks: list[DenseChunk] = []\n", "\n", "    for dense_chunk in dense_chunks:\n", "        candidate_chunks = list(\n", "            set(\n", "                [\n", "                    ret\n", "                    for ret in retrieval_chunks\n", "                    if (\n", "                        dense_chunk.path_str in ret.path\n", "                        or ret.path in dense_chunk.path_str\n", "                    )\n", "                    and dense_chunk.text_str == ret.text\n", "                ]\n", "            )\n", "        )\n", "\n", "        matching_origins = defaultdict(list)\n", "        for candidate_chunk in candidate_chunks:\n", "            matching_origins[candidate_chunk.origin].append(candidate_chunk)\n", "\n", "        # if line chunks is empty - prefer recency if possible\n", "        if len(line_chunks) == 0:\n", "            if ChunkOrigin.RECENCY_RETRIEVER.value in matching_origins:\n", "                recency_chunks.append(dense_chunk)\n", "            elif <PERSON>.DENSE_RETRIEVER.value in matching_origins:\n", "                line_chunks.append(dense_chunk)\n", "            else:\n", "                raise AssertionError(\n", "                    f\"Chunk not found in retrieval chunks when no line chunks:\\n\"\n", "                    f\"Path: {dense_chunk.path_str}\\n\"\n", "                    f\"Text: {dense_chunk.text_str}\\n\"\n", "                )\n", "        # if line chunks is not empty - have to choose a line chunk\n", "        else:\n", "            if ChunkOrigin.DENSE_RETRIEVER.value in matching_origins:\n", "                line_chunks.append(dense_chunk)\n", "            else:\n", "                raise AssertionError(\n", "                    f\"Chunk not found in retrieval chunks when line chunks exist:\\n\"\n", "                    f\"Path: {dense_chunk.path_str}\\n\"\n", "                    f\"Text: {dense_chunk.text_str}\\n\"\n", "                )\n", "\n", "    return line_chunks, recency_chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retrieval_base = \"/mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/retrieval/part-*.zstd.parquet\"\n", "retrieval_df = read_parquet_files(retrieval_base)\n", "\n", "retrieval_df[\"retrieved_chunks\"] = retrieval_df[\"retrieved_chunks\"].apply(\n", "    deserialize_retrieved_prompt_chunks\n", ")\n", "\n", "# only keep retrieved chunks that are from dense or recency retriever\n", "retrieval_df[\"retrieved_chunks\"] = retrieval_df[\"retrieved_chunks\"].apply(\n", "    lambda chunks: [\n", "        chunk\n", "        for chunk in chunks\n", "        if chunk.origin == ChunkOrigin.RECENCY_RETRIEVER.value\n", "        or chunk.origin == ChunkOrigin.DENSE_RETRIEVER.value\n", "    ]\n", ")\n", "\n", "# request_id to retrieval chunks\n", "retrieval_dict = retrieval_df.set_index(\"request_id\")[\"retrieved_chunks\"].to_dict()\n", "\n", "print(f\"Found {len(retrieval_dict)} request_ids in retrieval data\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# load dataset.bin and .idx\n", "from research.data.rag.common import create_unpack_unpad_tokens_fn\n", "\n", "\n", "prompt_base = (\n", "    \"/mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/prompts/\"\n", ")\n", "directories = sorted(glob.glob(f\"{prompt_base}*\"))\n", "prompt_names = [os.path.basename(d) for d in directories]\n", "datasets = {name: [] for name in prompt_names}\n", "\n", "print(prompt_names)\n", "\n", "for prompt_name in prompt_names:\n", "    path = os.path.join(prompt_base, prompt_name, \"part-*.zstd.parquet\")\n", "    prompt_df = read_parquet_files(path)\n", "    print(f\"Loaded {len(prompt_df)} samples from {prompt_name}\")\n", "    for idx in range(len(prompt_df)):\n", "        information = {}\n", "        tokens = prompt_df.iloc[idx][\"prompt_tokens\"]\n", "        request_id = prompt_df.iloc[idx][\"request_id\"]\n", "        prompt_new = list(create_unpack_unpad_tokens_fn(tokenizer)(tokens))[0]\n", "        assert isinstance(prompt_new, dict)\n", "        final_tokens: list[int] = prompt_new[\"prompt_tokens\"]\n", "        final_tokens = final_tokens[: final_tokens.index(st.fim_middle) + 1]\n", "        information[\"prompt_tokens\"] = final_tokens\n", "        information[\"request_id\"] = request_id\n", "\n", "        datasets[prompt_name].append(information)\n", "\n", "print(\"Found datasets:\", list(datasets.keys()))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["for name, dataset in datasets.items():\n", "    print(f\"{name} has {len(dataset)} samples\")\n", "    print(f\"Sample format: {dataset[0].keys()}\")\n", "    break\n", "\n", "assert all(len(datasets[name]) == len(datasets[prompt_names[0]]) for name in datasets)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["prompts_retrieval_lens = {name: [] for name in datasets}\n", "for name, prompt_dict in datasets.items():\n", "    # if name != \"recency_and_edits\":\n", "    #     continue\n", "    print(f\"{name} has {len(prompt_dict)} prompts\")\n", "    for prompt_data in prompt_dict:\n", "        prompt = prompt_data[\"prompt_tokens\"]\n", "        request_id = prompt_data[\"request_id\"]\n", "        retrieval_chunks = retrieval_dict[request_id]\n", "        information = {}\n", "\n", "        information[\"prompt_tokens\"] = len(prompt)\n", "\n", "        sig_retrieval_tokens = extract_tokens(prompt, st.sig_begin, st.sig_end)\n", "        information[\"sig_retrieval_tokens\"] = len(sig_retrieval_tokens)\n", "\n", "        if st.diff_section not in prompt:\n", "            diff_retrieval_tokens = [0]\n", "        else:\n", "            diff_retrieval_tokens = extract_tokens(\n", "                prompt, st.diff_section, st.fim_prefix\n", "            )\n", "        information[\"diff_retrieval_tokens\"] = len(diff_retrieval_tokens)\n", "\n", "        recency_and_line_tokens = extract_tokens(\n", "            prompt, st.retrieval_section, st.sig_begin\n", "        )\n", "        recency_and_line_chunks = retrieval_section_tokens_to_dense_chunks(\n", "            recency_and_line_tokens\n", "        )\n", "        line_chunks, recency_chunks = separate_retrieval_section_chunks(\n", "            recency_and_line_chunks, retrieval_chunks\n", "        )\n", "        line_retrieval_tokens = sum([len(chunk.tokens) for chunk in line_chunks])\n", "        information[\"line_retrieval_tokens\"] = line_retrieval_tokens\n", "        information[\"line_retrieval_num\"] = len(line_chunks)\n", "        recency_retrieval_tokens = sum([len(chunk.tokens) for chunk in recency_chunks])\n", "        information[\"recency_retrieval_tokens\"] = recency_retrieval_tokens\n", "        information[\"recency_retrieval_num\"] = len(recency_chunks)\n", "\n", "        dropped_tokens = (\n", "            len(recency_and_line_tokens)\n", "            - line_retrieval_tokens\n", "            - recency_retrieval_tokens\n", "        )\n", "        assert dropped_tokens <= 100, f\"Too many dropped tokens: {dropped_tokens}\"\n", "\n", "        prompts_retrieval_lens[name].append(information)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["# with this information create a dataframe with the following structure:\n", "\"\"\"\n", "Each row is the type of dataset\n", "The columns: the name of the dataset, the average prompt len, the average recency retrieval len, the average diff retrieval len, the average line chunk len (and avg # chunks), the average signature chunk len (and avg # chunks)\n", "\"\"\"\n", "\n", "\n", "def df_col_mean_and_round(df, column, round_digits=2):\n", "    return round(df[column].median(), round_digits)\n", "\n", "\n", "def calculate_dataset_stats(df: pd.DataFrame) -> dict:\n", "    \"\"\"Calculate average statistics for a dataset.\"\"\"\n", "    return {\n", "        \"prompt_len\": df_col_mean_and_round(df, \"prompt_tokens\"),\n", "        \"signature_tokens\": df_col_mean_and_round(df, \"sig_retrieval_tokens\"),\n", "        \"diff_tokens\": df_col_mean_and_round(df, \"diff_retrieval_tokens\"),\n", "        \"recency_tokens\": df_col_mean_and_round(df, \"recency_retrieval_tokens\"),\n", "        \"line_tokens\": df_col_mean_and_round(df, \"line_retrieval_tokens\"),\n", "        \"recency_chunks\": df_col_mean_and_round(df, \"recency_retrieval_num\"),\n", "        \"line_chunks\": df_col_mean_and_round(df, \"line_retrieval_num\"),\n", "    }"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Do not use diff retriever but use recency retriever.\n", "@dataclass\n", "class EnderDataPromptFormattingConfig:\n", "    \"\"\"The configuration for the <PERSON><PERSON> prompt formatter.\"\"\"\n", "\n", "    max_content_len: int\n", "    input_fraction: float\n", "    prefix_fraction: float\n", "    max_path_tokens: int\n", "\n", "    max_dense_signature_tokens: int\n", "    max_recency_retriever_tokens: int\n", "    max_diff_retriever_tokens: int\n", "    include_diff_retriever: bool\n", "\n", "    max_target_tokens: int\n", "\n", "    always_filter_chunks_by_recency: bool = True\n", "\n", "    def as_dict(self) -> dict:\n", "        \"\"\"Return configuration as a dictionary.\"\"\"\n", "        return dataclasses.asdict(self)\n", "\n", "\n", "prompt_recency_only_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304,  # 6144 (max content len for services with 96 max target tokens) + (256 - 96)\n", "    input_fraction=4 / 12,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_diff_retriever_tokens=0,\n", "    include_diff_retriever=False,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Do not use diff retriever or recency retriever.\n", "prompt_no_context_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304,  # 6144 + (256 - 96)\n", "    input_fraction=4 / 14,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=0,\n", "    max_diff_retriever_tokens=0,\n", "    include_diff_retriever=False,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Use diff retriever but not recency retriever.\n", "prompt_edits_only_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304,  # 6144 + (256 - 96)\n", "    input_fraction=4 / 12,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=0,\n", "    max_diff_retriever_tokens=1024,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Use both diff retriever and recency retriever.\n", "# Increased max_content_len to 7328 to account for the additional 1024 tokens from having both diff and recency retriever, and decreased input_fraction proportionally.\n", "prompt_full_context_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=7328,  # 6144 + 1024 + (256 - 96)\n", "    input_fraction=4 / 14,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_diff_retriever_tokens=1024,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Use both diff retriever and recency retriever. 512 extra budget for edits retriever.\n", "prompt_512_extra_budget_full_context_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304 + 512,\n", "    input_fraction=4 / 13,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_diff_retriever_tokens=512,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Use both diff retriever and recency retriever. 2048 extra budget for edits retriever.\n", "prompt_2048_extra_budget_full_context_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304 + 2048,\n", "    input_fraction=4 / 16,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_diff_retriever_tokens=2048,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# Use both diff retriever and recency retriever. Reduce line and signature budget to 512.\n", "prompt_reduced_line_sig_full_context_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=6304,\n", "    input_fraction=4 / 12,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=512,\n", "    max_recency_retriever_tokens=1024,\n", "    max_diff_retriever_tokens=1024,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "prompt_recency_extra_only_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=7328,  # 6144 + 1024 + (256 - 96)\n", "    input_fraction=4 / 14,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=2048,\n", "    max_diff_retriever_tokens=0,\n", "    include_diff_retriever=False,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# This prompt config is used to test the effect of extra budget when it is evenly distributed between all (line, signature, recency) retrievers.\n", "prompt_even_split_extra_budget_no_edits_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=7328,  # 6144 + 1024 + (256 - 96)\n", "    input_fraction=4 / 14,\n", "    prefix_fraction=3 / 4,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024 + 340,\n", "    max_recency_retriever_tokens=1024 + 340,\n", "    max_diff_retriever_tokens=0,\n", "    include_diff_retriever=False,\n", "    max_target_tokens=256,\n", ")\n", "\n", "# This prompt config is used to test the effect of normal budget when it is evenly distributed between all (line, signature, recency, edits) retrievers.\n", "prompt_even_split_normal_budget_recency_and_edits_config = (\n", "    EnderDataPromptFormattingConfig(\n", "        max_content_len=6304,  # 6144 + 1024 + (256 - 96)\n", "        input_fraction=4 / 12,\n", "        prefix_fraction=3 / 4,\n", "        max_path_tokens=50,\n", "        max_dense_signature_tokens=1024 - 256,\n", "        max_recency_retriever_tokens=1024 - 256,\n", "        max_diff_retriever_tokens=1024 - 256,\n", "        include_diff_retriever=True,\n", "        max_target_tokens=256,\n", "    )\n", ")\n", "\n", "# This prompt config is used to test the effect of extra budget split up unevenly\n", "prompt_uneven_split_normal_budget_recency_and_edits_config = (\n", "    EnderDataPromptFormattingConfig(\n", "        max_content_len=6304,  # 6144 + 1024 + (256 - 96)\n", "        input_fraction=4 / 12,\n", "        prefix_fraction=3 / 4,\n", "        max_path_tokens=50,\n", "        max_dense_signature_tokens=1024 - 340,\n", "        max_recency_retriever_tokens=1024 - 340,\n", "        max_diff_retriever_tokens=1024,\n", "        include_diff_retriever=True,\n", "        max_target_tokens=256,\n", "    )\n", ")\n", "\n", "STRATEGY_CONFIGS = {\n", "    # \"no_recency_no_edits\": prompt_no_context_config,\n", "    # \"recency_only\": prompt_recency_only_config,\n", "    # \"edits_only\": prompt_edits_only_config,\n", "    # \"recency_and_edits\": prompt_full_context_config,\n", "    # \"recency_extra_only\": prompt_recency_extra_only_config,\n", "    \"even_split_extra_budget_no_edits\": prompt_even_split_extra_budget_no_edits_config,\n", "    # \"even_split_normal_budget_recency_and_edits\": prompt_even_split_normal_budget_recency_and_edits_config,\n", "    # \"uneven_split_normal_budget_recency_and_edits\": prompt_uneven_split_normal_budget_recency_and_edits_config,\n", "    # \"512_extra_budget_full_context\": prompt_512_extra_budget_full_context_config,\n", "    # \"2048_extra_budget_full_context\": prompt_2048_extra_budget_full_context_config,\n", "    # \"reduced_line_sig_full_context\": prompt_reduced_line_sig_full_context_config,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_summary_data = []\n", "for name, config in STRATEGY_CONFIGS.items():\n", "    max_prompt_len = config.max_content_len - config.max_target_tokens\n", "    diff_budget = config.max_diff_retriever_tokens\n", "    recency_budget = config.max_recency_retriever_tokens\n", "    signature_budget = config.max_dense_signature_tokens\n", "    line_budget = round(\n", "        max_prompt_len * (1 - config.input_fraction)\n", "        - diff_budget\n", "        - recency_budget\n", "        - signature_budget\n", "    )\n", "    # print(f\"{name} has {diff_budget} diff budget, {recency_budget} recency budget, {signature_budget} signature budget, {line_budget} line budget\")\n", "    config_summary_data.append(\n", "        {\n", "            \"dataset_name\": name,\n", "            \"prompt_budget\": max_prompt_len,\n", "            \"diff_budget\": diff_budget,\n", "            \"recency_budget\": recency_budget,\n", "            \"signature_budget\": signature_budget,\n", "            \"line_budget (calculated)\": line_budget,\n", "        }\n", "    )\n", "config_summary_df = pd.DataFrame(config_summary_data)\n", "print(\"\\nPrompt Configuration Summary:\")\n", "config_summary_df.round(2)  # Round to 2 decimal places"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["summary_data = [\n", "    {\"dataset_name\": name, **calculate_dataset_stats(pd.DataFrame(data))}\n", "    for name, data in prompts_retrieval_lens.items()\n", "    if data  # Skip empty datasets\n", "]\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "print(\"\\nDataset Statistics:\")\n", "summary_df.round(2)  # Round to 2 decimal places"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["merged_df = pd.merge(config_summary_df, summary_df, on=\"dataset_name\", how=\"outer\")\n", "merged_df.round(2)  # Round to 2 decimal places"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["def format_utilization(used: float, budget: float, chunks: int | None = None) -> str:\n", "    if budget == 0:\n", "        percentage = \"N/A\"\n", "    else:\n", "        percentage = f\"{round((used / budget) * 100, 2)}%\"\n", "    if chunks is not None:\n", "        return f\"{used:.0f}/{budget:.0f} ({percentage}), {chunks:.0f}\"\n", "    return f\"{used:.0f}/{budget:.0f} ({percentage})\"\n", "\n", "\n", "summary_df = pd.DataFrame(\n", "    {\n", "        \"dataset_name\": merged_df[\"dataset_name\"],\n", "        \"prompt (used/total)\": merged_df.apply(\n", "            lambda x: format_utilization(x[\"prompt_len\"], x[\"prompt_budget\"]), axis=1\n", "        ),\n", "        \"signature (used/total)\": merged_df.apply(\n", "            lambda x: format_utilization(x[\"signature_tokens\"], x[\"signature_budget\"]),\n", "            axis=1,\n", "        ),\n", "        \"diff (used/total)\": merged_df.apply(\n", "            lambda x: format_utilization(x[\"diff_tokens\"], x[\"diff_budget\"]), axis=1\n", "        ),\n", "        \"recency (used/total), # chunks\": merged_df.apply(\n", "            lambda x: format_utilization(\n", "                x[\"recency_tokens\"], x[\"recency_budget\"], x[\"recency_chunks\"]\n", "            ),\n", "            axis=1,\n", "        ),\n", "        \"line (used/total), # chunks\": merged_df.apply(\n", "            lambda x: format_utilization(\n", "                x[\"line_tokens\"], x[\"line_budget (calculated)\"], x[\"line_chunks\"]\n", "            ),\n", "            axis=1,\n", "        ),\n", "        # 'chunks (recency/line)': merged_df.apply(lambda x: f\"{x['recency_chunks']:.0f}/{x['line_chunks']:.0f}\", axis=1)\n", "    }\n", ")\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.width\", None)\n", "summary_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
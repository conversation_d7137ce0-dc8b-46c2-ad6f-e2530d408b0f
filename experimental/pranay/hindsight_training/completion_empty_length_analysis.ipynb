{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Analyze the LLM empty rate and response length on various models trained with different proportions of empty data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Query types to get 1000 requests from BigQuery\n", "* Query that helps analyze the empty rate of the LLM.\n", "* Query that helps analyze the length distribution of the LLM responses."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def get_empty_query(model, client, end_date):\n", "    query = f\"\"\"\n", "  WITH request_data AS (\n", "    SELECT \n", "      m.request_id as request_id,\n", "      m.model_name,\n", "      SPLIT(rm.user_agent, '/')[OFFSET(0)] as user_agent,\n", "    FROM \n", "      `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata` rm\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.model` m\n", "    ON \n", "      rm.request_id = m.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.completion_response` c_resp\n", "    ON \n", "      rm.request_id = c_resp.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.completion_post_process` cpp\n", "    ON \n", "      rm.request_id = cpp.request_id\n", "    WHERE \n", "      rm.tenant = \"dogfood-shard\"\n", "      AND (m.model_name = \"{model}\")\n", "      AND SPLIT(rm.user_agent, '/')[OFFSET(0)] = '{client}'\n", "      AND m.time < TIMESTAMP(\"{end_date}\")\n", "  )\n", "\n", "  SELECT\n", "    request_id,\n", "  FROM \n", "    request_data\n", "  ORDER BY\n", "  request_id\n", "  LIMIT 1000\"\"\"\n", "    return query"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["vscode_qwelden_v1_1_query = get_empty_query(\n", "    \"qweldenv1-1-14b\", \"Augment.vscode-augment\", \"2025-03-01\"\n", ")\n", "vscode_qwelden_v3_1_query = get_empty_query(\n", "    \"qweldenv3-1-14b\", \"Augment.vscode-augment\", \"2025-04-20\"\n", ")\n", "vscode_qwelden_v3_2_query = get_empty_query(\n", "    \"qweldenv3-2-14b\", \"Augment.vscode-augment\", \"2025-04-20\"\n", ")\n", "intellij_qwelden_v1_1_query = get_empty_query(\n", "    \"qweldenv1-1-14b\", \"augment.intellij\", \"2025-03-01\"\n", ")\n", "intellij_qwelden_v3_1_query = get_empty_query(\n", "    \"qweldenv3-1-14b\", \"augment.intellij\", \"2025-04-20\"\n", ")\n", "intellij_qwelden_v3_2_query = get_empty_query(\n", "    \"qweldenv3-2-14b\", \"augment.intellij\", \"2025-04-20\"\n", ")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def get_length_query(model, client, end_date):\n", "    query = f\"\"\"\n", "  WITH request_data AS (\n", "  SELECT\n", "      m.request_id as request_id,\n", "      m.model_name,\n", "      SPLIT(rm.user_agent, '/')[OFFSET(0)] as user_agent,\n", "      c_resp.character_count,\n", "      CAST(cr.accepted AS INT64) as accepted,\n", "    FROM \n", "      `system-services-prod.us_staging_request_insight_analytics_dataset.completion_resolution` cr\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.model` m\n", "    ON \n", "      cr.request_id = m.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata` rm\n", "    ON \n", "      cr.request_id = rm.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.completion_response` c_resp\n", "    ON \n", "      cr.request_id = c_resp.request_id\n", "    WHERE \n", "      cr.tenant = \"dogfood-shard\"\n", "      AND (m.model_name = \"{model}\")\n", "      AND SPLIT(rm.user_agent, '/')[OFFSET(0)] = '{client}'\n", "      AND m.time < TIMESTAMP(\"{end_date}\")\n", "    )\n", "    SELECT\n", "      request_id,\n", "    FROM \n", "      request_data\n", "    ORDER BY\n", "    request_id\n", "    LIMIT 1000\"\"\"\n", "    return query"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["vscode_qwelden_v1_1_length_query = get_length_query(\n", "    \"qweldenv1-1-14b\", \"Augment.vscode-augment\", \"2025-04-20\"\n", ")\n", "vscode_qwelden_v3_1_length_query = get_length_query(\n", "    \"qweldenv3-1-14b\", \"Augment.vscode-augment\", \"2025-04-20\"\n", ")\n", "vscode_qwelden_v3_2_length_query = get_length_query(\n", "    \"qweldenv3-2-14b\", \"Augment.vscode-augment\", \"2025-04-20\"\n", ")\n", "\n", "intellij_qwelden_v1_1_length_query = get_length_query(\n", "    \"qweldenv1-1-14b\", \"augment.intellij\", \"2025-04-20\"\n", ")\n", "intellij_qwelden_v3_1_length_query = get_length_query(\n", "    \"qweldenv3-1-14b\", \"augment.intellij\", \"2025-04-20\"\n", ")\n", "intellij_qwelden_v3_2_length_query = get_length_query(\n", "    \"qweldenv3-2-14b\", \"augment.intellij\", \"2025-04-20\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What to actually query and download?"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["query = vscode_qwelden_v3_2_length_query"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.completion_dataset_gcs import CompletionDataset, Filters\n", "from base.datasets import tenants\n", "from base.datasets.completion import CompletionDatum\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "# Set up BigQuery client\n", "project_id = \"system-services-prod\"\n", "gcp_creds, _ = get_gcp_creds()\n", "bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)\n", "\n", "rows = bigquery_client.query_and_wait(query)\n", "request_ids = [row.request_id for row in rows]\n", "print(f\"Found {len(request_ids)} request IDs\")\n", "\n", "\n", "dataset = CompletionDataset.create_data_from_gcs(\n", "    tenant=tenants.DOGFOOD_SHARD,\n", "    filters=Filters(request_ids=request_ids),\n", ")\n", "dataset = list(dataset)\n", "request_to_completion: dict[str, CompletionDatum] = {\n", "    datum.request_id: datum for datum in dataset\n", "}\n", "print(f\"Downloaded {len(request_to_completion)} completions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Models"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import contextlib\n", "import io\n", "import sys\n", "import torch\n", "import gc\n", "from typing import cast\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from research.models.meta_model import GenerationOptions, RawGenerateOutput\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "tokenizer: Qwen25CoderTokenizer = cast(\n", "    Qwen25CoderTokenizer, create_tokenizer_by_name(\"qwen25coder\")\n", ")\n", "st = tokenizer.special_tokens\n", "\n", "\n", "class ExecuteModel:\n", "    _instances = []\n", "\n", "    @contextlib.contextmanager\n", "    def _suppress_output(self):\n", "        \"\"\"Context manager to suppress stdout temporarily while keeping stderr.\"\"\"\n", "        stdout = sys.stdout\n", "        output = io.StringIO()\n", "        try:\n", "            sys.stdout = output\n", "            yield\n", "        finally:\n", "            sys.stdout = stdout\n", "\n", "    def __init__(\n", "        self,\n", "        checkpoint: str,\n", "        sha: str,\n", "        tokenizer: Qwen25CoderTokenizer,\n", "        use_fp8: bool = False,\n", "    ):\n", "        with self._suppress_output():\n", "            self.tokenizer = tokenizer\n", "            self.config = {\n", "                \"name\": \"fastforward_qwen25coder_14b\",\n", "                \"checkpoint_path\": checkpoint,\n", "                \"checkpoint_sha256\": sha,\n", "                \"sequence_length\": 6600,\n", "                \"use_fp8\": use_fp8,\n", "            }\n", "            from research.eval.harness.factories import create_model\n", "\n", "            self.model = None\n", "            self._model_creator = lambda: cast(\n", "                LLAMA_FastForwardModel, create_model(self.config)\n", "            )\n", "\n", "            self.options = GenerationOptions(\n", "                max_generated_tokens=256, stop_tokens=[st.eos]\n", "            )\n", "\n", "            ExecuteModel._instances.append(self)\n", "\n", "    def ensure_loaded(self):\n", "        \"\"\"Ensure this model is loaded and others are unloaded.\"\"\"\n", "        with self._suppress_output():\n", "            # Unload all other models\n", "            for instance in ExecuteModel._instances:\n", "                if instance is not self and instance.model is not None:\n", "                    instance.deep_unload()\n", "\n", "            # Load this model if needed\n", "            if self.model is None:\n", "                self.model = self._model_creator()\n", "                self.model.load()\n", "\n", "    def __call__(\n", "        self, prompt_tokens: list[int], keep_pause_tokens: bool = False\n", "    ) -> list[int]:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            generated_tokens = self.model.raw_generate_tokens(\n", "                prompt_tokens, options=self.options\n", "            ).tokens\n", "\n", "            original_length = len(generated_tokens)\n", "            generated_tokens, _ = self.extract_generation_before_stop_tokens(\n", "                generated_tokens, [st.eos]\n", "            )\n", "            generation_stopped_at_max_length = original_length == len(generated_tokens)\n", "            if generation_stopped_at_max_length and st.pause is not None:\n", "                for index in reversed(range(len(generated_tokens))):\n", "                    if generated_tokens[index] == st.pause:\n", "                        generated_tokens = generated_tokens[:index]\n", "                        break\n", "\n", "            if keep_pause_tokens:\n", "                return generated_tokens\n", "\n", "            generated_tokens = [\n", "                token for token in generated_tokens if token != st.pause\n", "            ]\n", "            return generated_tokens\n", "\n", "    def simple_call(\n", "        self, prompt_tokens: list[int], max_tokens: int = 256\n", "    ) -> RawGenerateOutput:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            options = GenerationOptions(\n", "                max_generated_tokens=max_tokens, stop_tokens=[st.eos, st.pause]\n", "            )\n", "            return self.model.raw_generate_tokens(prompt_tokens, options=options)\n", "\n", "    def forward_pass_for_logits(self, full_prompt: torch.Tensor) -> torch.Tensor:\n", "        with self._suppress_output():\n", "            self.ensure_loaded()\n", "            assert self.model is not None\n", "            return self.model.forward_pass_single_logits(full_prompt)\n", "\n", "    def deep_unload(self):\n", "        with self._suppress_output():\n", "            if self.model is not None:\n", "                self.model.unload()\n", "                self.model = None\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "\n", "    def extract_generation_before_stop_tokens(\n", "        self, generated: list[int], stop_token_ids: list[int | None]\n", "    ) -> tuple[list[int], int | None]:\n", "        stop_tokens_ids_set = {\n", "            token_id for token_id in stop_token_ids if token_id is not None\n", "        }\n", "        fim_stop_token_id = None\n", "        for index in range(len(generated)):\n", "            if generated[index] in stop_tokens_ids_set:\n", "                fim_stop_token_id = generated[index]\n", "                generated = generated[:index]\n", "                break\n", "        return generated, fim_stop_token_id"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_generation_before_stop_tokens(\n", "    generated: list[int], stop_token_ids: list[int | None]\n", ") -> tuple[list[int], int | None]:\n", "    stop_tokens_ids_set = {\n", "        token_id for token_id in stop_token_ids if token_id is not None\n", "    }\n", "    fim_stop_token_id = None\n", "    for index in range(len(generated)):\n", "        if generated[index] in stop_tokens_ids_set:\n", "            fim_stop_token_id = generated[index]\n", "            generated = generated[:index]\n", "            break\n", "    return generated, fim_stop_token_id"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["e10_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_e10_hindsight_ffwd\",\n", "    \"67e0ffc42f473c974fa8c043999a58db981d207f54f63e424b0bea18557ccdd6\",\n", ")\n", "\n", "# V3_2\n", "e15_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_e15_hindsight_ffwd\",\n", "    \"36ad1f40bb3015d54d10308c335c08122a70bd79f105184544434df0843af43c\",\n", ")\n", "\n", "e20_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_e20_hindsight_ffwd\",\n", "    \"9f13658809283639d28f98a44162b84e74a18a6c642a9a98ad39b94afdfe01ac\",\n", ")\n", "\n", "e25_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_e25_hindsight_ffwd\",\n", "    \"d555ac430534e1ea80ef82e22f4386968f1935e719bb09696e5116ceebf1ff4a\",\n", ")\n", "\n", "e30_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_e30_hindsight_ffwd\",\n", "    \"4157fd9e552647b714b7f64def5e6d24db09164c826c7b1405820b07028cc4e4\",\n", ")\n", "\n", "# V3_1\n", "e34_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_empty_skip_hindsight_ffwd/\",\n", "    \"c475239c06148a30504b4dc3588140908e0945100129ed671ed601a00ff79fd9\",\n", ")\n", "\n", "v1_1_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd/\",\n", "    \"7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9\",\n", ")\n", "\n", "\n", "def construct_model(checkpoint: tuple[str, str], tokenizer: Qwen25CoderTokenizer):\n", "    return ExecuteModel(checkpoint[0], checkpoint[1], tokenizer)\n", "\n", "\n", "models = {\n", "    # \"e10\": construct_model(e10_checkpoint, tokenizer),\n", "    \"v3_2\": construct_model(e15_checkpoint, tokenizer),\n", "    # \"e20\": construct_model(e20_checkpoint, tokenizer),\n", "    # \"e25\": construct_model(e25_checkpoint, tokenizer),\n", "    # \"e30\": construct_model(e30_checkpoint, tokenizer),\n", "    # \"v3_1\": construct_model(e34_checkpoint, tokenizer),\n", "    \"v1_1\": construct_model(v1_1_checkpoint, tokenizer),\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compute Stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Empty Stats"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def is_empty_output(output_token_ids: list[int]):\n", "    if len(output_token_ids) == 1:\n", "        token = output_token_ids[0]\n", "        if token == st.eos:\n", "            return True\n", "        else:\n", "            print(f\"Empty output with non-eos token: {tokenizer.detokenize([token])}\")\n", "            return False\n", "    else:\n", "        return False"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["model_stats = {model_name: {\"empty\": 0, \"non-empty\": 0} for model_name in models}\n", "print(model_stats)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["for model_name, model in models.items():\n", "    print(f\"Loading {model_name}...\")\n", "    for request_id, datum in request_to_completion.items():\n", "        prompt_token_ids = datum.response.prompt_token_ids\n", "        assert prompt_token_ids is not None\n", "        output_token_ids = model.simple_call(prompt_token_ids, max_tokens=2).tokens\n", "        is_empty = is_empty_output(output_token_ids)\n", "        if is_empty:\n", "            model_stats[model_name][\"empty\"] += 1\n", "        else:\n", "            model_stats[model_name][\"non-empty\"] += 1\n", "\n", "    print(model_stats)\n", "    for model_name, stats in model_stats.items():\n", "        if stats[\"empty\"] + stats[\"non-empty\"] == 0:\n", "            continue\n", "        empty_pct = stats[\"empty\"] / (stats[\"empty\"] + stats[\"non-empty\"])\n", "        print(f\"{model_name}: {empty_pct:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Length Stats"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["model_token_data = {model_name: {} for model_name in models}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "for model_name, model in models.items():\n", "    print(f\"Loading {model_name}...\")\n", "    for request_id, datum in request_to_completion.items():\n", "        prompt_token_ids = datum.response.prompt_token_ids\n", "        assert prompt_token_ids is not None\n", "        output_token_ids = model.simple_call(prompt_token_ids, max_tokens=96).tokens\n", "        model_token_data[model_name][request_id] = output_token_ids\n", "\n", "    with open(\"vscode_qwelden_v3_2_query_dat.json\", \"w\") as f:\n", "        json.dump(model_token_data, f)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "model_token_data = json.load(open(\"intellij_qwelden_v1_1_query_data.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["averages_by_model = {model_name: [] for model_name in model_token_data}\n", "for model_name in model_token_data:\n", "    for idx, (request_id, output_token_ids) in enumerate(\n", "        model_token_data[model_name].items()\n", "    ):\n", "        generated, _ = extract_generation_before_stop_tokens(\n", "            output_token_ids, [st.eos, st.pause, st.skip]\n", "        )\n", "        text = tokenizer.detokenize(generated)\n", "        averages_by_model[model_name].append(len(text))\n", "\n", "for model_name, stats in averages_by_model.items():\n", "    print(f\"{model_name}: {sum(stats) / len(stats)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
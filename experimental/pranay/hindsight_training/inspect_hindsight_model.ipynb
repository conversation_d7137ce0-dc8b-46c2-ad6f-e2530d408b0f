{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.eval.harness.factories import create_model\n", "from research.models.fastbackward_models import FastBackwardLLM\n", "from base.fastforward.starcoder import model_specs\n", "from base.fastforward.starcoder import fwd_starcoder2\n", "from research.models.meta_model import GenerationOptions\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.tokenizers.tokenizer import Tokenizer\n", "from research.data.dataset.indexed_dataset import MMapIndexedDataset\n", "import os\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "import numpy as np\n", "import gc\n", "import random\n", "import pandas as pd\n", "import json\n", "from pathlib import Path\n", "from colorama import Fore, Style\n", "\n", "\n", "import torch\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "tokenizer = create_tokenizer_by_name(\"qwen25coder\")\n", "assert isinstance(tokenizer, Qwen25CoderTokenizer)\n", "\n", "file_sep = tokenizer.special_tokens.filename\n", "newline = tokenizer.special_tokens.newline\n", "skip_token = tokenizer.special_tokens.skip\n", "fim_prefix = tokenizer.special_tokens.fim_prefix\n", "fim_suffix = tokenizer.special_tokens.fim_suffix\n", "fim_middle = tokenizer.special_tokens.fim_middle\n", "padding = tokenizer.special_tokens.padding\n", "pause = tokenizer.special_tokens.pause\n", "eos = tokenizer.special_tokens.eos\n", "\n", "base_checkpoint = \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd\"\n", "trained_checkpoint = (\n", "    \"/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_hindsight_ffwd\"\n", ")\n", "\n", "BASE_DATA_DIR = Path(\"/mnt/efs/augment/user/pranay/hindsight/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run models to generate completion (done)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_prompt(tokens) -> list[int]:\n", "    # Find first occurrence of fim_middle\n", "    idx = np.where(tokens == fim_middle)[0]\n", "    if len(idx) > 0:\n", "        return list(tokens[: idx[0] + 1])\n", "    else:\n", "        raise ValueError(\"fim_middle not found in tokens\")\n", "\n", "\n", "def extract_target(tokens) -> list[int]:\n", "    # Find fim_middle and padding indices\n", "    middle_idx = np.where(tokens == fim_middle)[0]\n", "    pad_idx = np.where(tokens == padding)[0]\n", "    pad_idx = pad_idx[0] if len(pad_idx) > 0 else len(tokens)\n", "\n", "    if len(middle_idx) > 0:\n", "        start = middle_idx[0] + 1\n", "        return list(tokens[start:pad_idx])\n", "    else:\n", "        raise ValueError(\"fim_middle not found in tokens\")\n", "\n", "\n", "def tokens_to_str(tokens) -> str:\n", "    return tokenizer.de<PERSON><PERSON>ze(tokens)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_datasets(output_dataset_url):\n", "    # Load the main dataset\n", "    return MMapIndexedDataset(\n", "        os.path.join(output_dataset_url, \"dataset\"), skip_warmup=True\n", "    )\n", "\n", "\n", "output_dataset_url = (\n", "    BASE_DATA_DIR\n", "    / \"datasets/vanguard_mix_2024-11-01_2025-01-14/i0-vanguard0/datasets/8352b_1024s_1024r_2048d_1976l/\"\n", ")\n", "main_dataset = load_datasets(output_dataset_url)\n", "\n", "print(f\"Main dataset size: {len(main_dataset)}\")\n", "print(\"First item in main dataset:\", main_dataset[0])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class ExecuteModel:\n", "    def __init__(self, checkpoint: str, sha: str, tokenizer: Tokenizer):\n", "        self.checkpoint = checkpoint\n", "        self.tokenizer = tokenizer\n", "        self.config = {\n", "            \"name\": \"fastforward_qwen25coder_14b\",\n", "            \"checkpoint_path\": checkpoint,\n", "            \"checkpoint_sha256\": sha,\n", "            \"sequence_length\": 6600,\n", "        }\n", "        self.options = GenerationOptions(max_generated_tokens=256, stop_tokens=[eos])\n", "        from research.eval.harness.factories import create_model\n", "\n", "        self.model = create_model(self.config)\n", "        assert isinstance(self.model, LLAMA_FastForwardModel)\n", "        self.model.load()\n", "\n", "    def extract_generation_before_stop_tokens(\n", "        self, generated: list[int], stop_token_ids: list[int | None]\n", "    ) -> tuple[list[int], int | None]:\n", "        \"\"\"Extract generation before stop tokens.\"\"\"\n", "        stop_tokens_ids_set = {\n", "            token_id for token_id in stop_token_ids if token_id is not None\n", "        }\n", "        fim_stop_token_id = None\n", "        for index in range(len(generated)):\n", "            if generated[index] in stop_tokens_ids_set:\n", "                fim_stop_token_id = generated[index]\n", "                generated = generated[:index]\n", "                break\n", "        return generated, fim_stop_token_id\n", "\n", "    def __call__(self, prompt_tokens: list[int]) -> list[int]:\n", "        assert isinstance(self.model, LLAMA_FastForwardModel)\n", "        generated_tokens = self.model.raw_generate_tokens(\n", "            prompt_tokens, options=self.options\n", "        ).tokens\n", "\n", "        original_length = len(generated_tokens)\n", "        generated_tokens, _ = self.extract_generation_before_stop_tokens(\n", "            generated_tokens, [eos]\n", "        )\n", "        generation_stopped_at_max_length = original_length == len(generated_tokens)\n", "        if generation_stopped_at_max_length and pause is not None:\n", "            # If generation stopped at max length, truncate at the last pause token to\n", "            # potentially recover from non-runnable code.\n", "            for index in reversed(range(len(generated_tokens))):\n", "                if generated_tokens[index] == pause:\n", "                    generated_tokens = generated_tokens[:index]\n", "                    break\n", "        # During evaluation, remove all pause tokens starting from the prompt.\n", "        generated_tokens = [token for token in generated_tokens if token != pause]\n", "\n", "        return generated_tokens\n", "\n", "    def deep_unload(self):\n", "        self.model.unload()\n", "        gc.collect()\n", "        torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# base_model = ExecuteModel(\n", "#     base_checkpoint,\n", "#     \"7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9\",\n", "#     tokenizer,\n", "# )"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# train_model = ExecuteModel(\n", "#     trained_checkpoint,\n", "#     \"7025e4a01e79530e965070bd6dd542a951064221e4ffb73bef2320473af1cd19\",\n", "#     tokenizer,\n", "# )"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["random.seed(42)\n", "\n", "random_indices = random.sample(range(len(main_dataset)), 500)\n", "result_data = []\n", "\n", "base_model = ExecuteModel(\n", "    base_checkpoint,\n", "    \"7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9\",\n", "    tokenizer,\n", ")\n", "for i, idx in enumerate(random_indices):\n", "    prompt_tokens = extract_prompt(main_dataset[idx])\n", "    target_tokens = extract_target(main_dataset[idx])\n", "    base_output_tokens = base_model(prompt_tokens)\n", "    prompt_str = tokens_to_str(prompt_tokens)\n", "    if i % 50 == 0:\n", "        print(f\"Processed {i}/{len(random_indices)} samples for base model\")\n", "    result_data.append(\n", "        {\n", "            \"idx\": idx,\n", "            \"prompt_tokens\": prompt_tokens,\n", "            \"target_tokens\": target_tokens,\n", "            \"prompt\": prompt_str,\n", "            \"target\": tokens_to_str(target_tokens),\n", "            \"base_output_tokens\": base_output_tokens,\n", "            \"base_output\": tokens_to_str(base_output_tokens),\n", "        }\n", "    )\n", "base_model.deep_unload()\n", "\n", "train_model = ExecuteModel(\n", "    trained_checkpoint,\n", "    \"7025e4a01e79530e965070bd6dd542a951064221e4ffb73bef2320473af1cd19\",\n", "    tokenizer,\n", ")\n", "for i, idx in enumerate(random_indices):\n", "    prompt_tokens = extract_prompt(main_dataset[idx])\n", "    train_output_tokens = train_model(prompt_tokens)\n", "    result_data[i][\"train_output_tokens\"] = train_output_tokens\n", "    result_data[i][\"train_output\"] = tokens_to_str(train_output_tokens)\n", "    if i % 50 == 0:\n", "        print(f\"Processed {i}/{len(random_indices)} samples for train model\")\n", "train_model.deep_unload()\n", "\n", "df = pd.DataFrame(result_data)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def convert_numpy_types(obj):\n", "    \"\"\"Convert numpy types to native Python types for JSON serialization.\"\"\"\n", "    if isinstance(obj, dict):\n", "        return {k: convert_numpy_types(v) for k, v in obj.items()}\n", "    elif isinstance(obj, list):\n", "        return [convert_numpy_types(item) for item in obj]\n", "    elif isinstance(obj, tuple):\n", "        return tuple(convert_numpy_types(item) for item in obj)\n", "    elif isinstance(obj, np.integer):\n", "        return int(obj)\n", "    elif isinstance(obj, np.floating):\n", "        return float(obj)\n", "    elif isinstance(obj, np.n<PERSON>ray):\n", "        return obj.tolist()\n", "    elif isinstance(obj, np.bool_):\n", "        return bool(obj)\n", "    return obj\n", "\n", "\n", "# Convert DataFrame to records and save\n", "with open(BASE_DATA_DIR / \"exp/237/hindsight_eval.jsonl\", \"w\") as f:\n", "    for _, row in df.iterrows():\n", "        # Convert row to dict and handle numpy types\n", "        row_dict = row.to_dict()\n", "        converted_row = convert_numpy_types(row_dict)\n", "        f.write(json.dumps(converted_row) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect Samples"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["hindsight_eval_jsonl = BASE_DATA_DIR / \"exp/237/hindsight_eval.jsonl\"\n", "df = pd.read_json(hindsight_eval_jsonl, lines=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["class DataSample:\n", "    def __init__(self, row):\n", "        self.prompt_tokens = row[\"prompt_tokens\"]\n", "        self.target_tokens = row[\"target_tokens\"]\n", "\n", "        self.path_str = self.extract_text(self.prompt_tokens, file_sep, newline)\n", "        self.prefix_str = self.extract_text(self.prompt_tokens, fim_prefix, fim_suffix)\n", "        self.suffix_str = self.extract_text(self.prompt_tokens, fim_suffix, fim_middle)\n", "        self.middle_str = self.tokens_to_text(\n", "            self.target_tokens, remove_eos_or_pause=True\n", "        )\n", "\n", "        # EDIT - no longer true: These fields have pause/eos at the end of the string\n", "        # self.ground_truth_generation_str = self.tokens_to_text(\n", "        #     self.target_tokens, remove_eos_or_pause=False\n", "        # )\n", "        self.base_generation_str = row[\"base_output\"]\n", "        self.train_generation_str = row[\"train_output\"]\n", "\n", "        self.contains_skip_token = (\n", "            skip_token in row[\"base_output_tokens\"]\n", "            or skip_token in row[\"train_output_tokens\"]\n", "        )\n", "\n", "    def tokens_to_text(self, tokens: list[int], remove_eos_or_pause: bool = True):\n", "        if remove_eos_or_pause:\n", "            tokens = [t for t in tokens if t not in [eos, pause]]\n", "        return tokenizer.de<PERSON><PERSON>ze(tokens)\n", "\n", "    def extract_text(self, tokens: list[int], start_token: int, end_token: int):\n", "        \"\"\"Extracts text between start and end tokens.\"\"\"\n", "        start = tokens.index(start_token) + 1\n", "        end = tokens.index(end_token)\n", "        assert start <= end, f\"start: {start}, end: {end}\"\n", "        text_tokens = tokens[start:end]\n", "        return tokenizer.detokenize(text_tokens)\n", "\n", "\n", "samples = [DataSample(row) for _, row in df.iterrows()]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def print_sample(prefix, suffix, generation):\n", "    prefix_string = Fore.BLUE + prefix + Style.RESET_ALL\n", "    generation_string = Fore.YELLOW + generation + Style.RESET_ALL\n", "    suffix_string = Fore.BLUE + suffix + Style.RESET_ALL\n", "    print(prefix_string + generation_string + suffix_string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 0\n", "count_skipping_bc_skip_token = 0\n", "base_agree_count = 0\n", "train_agree_count = 0\n", "for sample_idx, sample in enumerate(samples):\n", "    if sample.base_generation_str != sample.train_generation_str and (\n", "        sample.base_generation_str == sample.middle_str\n", "        or sample.train_generation_str == sample.middle_str\n", "    ):\n", "        count += 1\n", "        if sample.contains_skip_token:\n", "            count_skipping_bc_skip_token += 1\n", "            continue\n", "        print(Fore.WHITE + f\"REQUEST {count}\" + Style.RESET_ALL)\n", "        print(sample.path_str)\n", "        print_sample(\n", "            sample.prefix_str[-800:],\n", "            sample.suffix_str[:300],\n", "            sample.middle_str,\n", "        )\n", "        print(Fore.YELLOW + \"GROUND TRUTH\" + Style.RESET_ALL)\n", "        print(sample.middle_str)\n", "\n", "        base_agrees = sample.base_generation_str == sample.middle_str\n", "        base_agree_count += base_agrees\n", "        base_color = Fore.GREEN if base_agrees else Fore.RED\n", "        print(base_color + \"BASE\" + Style.RESET_ALL)\n", "        print(sample.base_generation_str)\n", "        train_agrees = sample.train_generation_str == sample.middle_str\n", "        train_agree_count += train_agrees\n", "        train_color = Fore.GREEN if train_agrees else Fore.RED\n", "        print(train_color + \"TRAIN\" + Style.RESET_ALL)\n", "        print(sample.train_generation_str)\n", "        print(\"=\" * 80)\n", "\n", "print(\n", "    f\"Found {count}/{len(samples)} samples where base and train differ but one matches ground truth. Base agrees {base_agree_count} times, train agrees {train_agree_count} times, skipping {count_skipping_bc_skip_token} samples because they contained a skip token.\"\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from typing import Dict, Union\n", "\n", "\n", "def compare_metrics(\n", "    base_metrics: Dict[str, float], trained_metrics: Dict[str, float]\n", ") -> Dict:\n", "    keys = set(base_metrics.keys()).union(set(trained_metrics.keys()))\n", "    results = {}\n", "    for key in keys:\n", "        if \"exact_match\" in key:\n", "            base_value = base_metrics.get(key, 0)\n", "            trained_value = trained_metrics.get(key, 0)\n", "            results[key] = [base_value, trained_value]\n", "    return results\n", "\n", "\n", "train_metrics = {\n", "    \"exact_match.caddyserver/caddy\": 0.5625,\n", "    \"exact_match.ALL\": 0.5774,\n", "    \"exact_match.ethereum/go-ethereum\": 0.5426,\n", "    \"exact_match.google/mobly\": 0.6766,\n", "    \"exact_match.jaegertracing/jaeger\": 0.5294,\n", "    \"exact_match.moment/luxon\": 0.6471,\n", "    \"exact_match.mrdoob/three.js\": 0.8431,\n", "    \"exact_match.pallets/flask\": 0.5267,\n", "    \"exact_match.pydantic/pydantic\": 0.5043,\n", "    \"exact_match.seata/seata\": 0.807,\n", "    \"exact_match.spulec/freezegun\": 0.2903,\n", "    \"edit_similarity.caddyserver/caddy\": 0.8152,\n", "    \"edit_similarity.ALL\": 0.8059,\n", "    \"edit_similarity.ethereum/go-ethereum\": 0.7875,\n", "    \"edit_similarity.google/mobly\": 0.8638,\n", "    \"edit_similarity.jaegertracing/jaeger\": 0.797,\n", "    \"edit_similarity.moment/luxon\": 0.8231,\n", "    \"edit_similarity.mrdoob/three.js\": 0.9308,\n", "    \"edit_similarity.pallets/flask\": 0.7706,\n", "    \"edit_similarity.pydantic/pydantic\": 0.762,\n", "    \"edit_similarity.seata/seata\": 0.8952,\n", "    \"edit_similarity.spulec/freezegun\": 0.7215,\n", "    \"counts.caddyserver/caddy\": 48,\n", "    \"counts.ALL\": 1221,\n", "    \"counts.ethereum/go-ethereum\": 94,\n", "    \"counts.google/mobly\": 235,\n", "    \"counts.jaegertracing/jaeger\": 153,\n", "    \"counts.moment/luxon\": 51,\n", "    \"counts.mrdoob/three.js\": 51,\n", "    \"counts.pallets/flask\": 150,\n", "    \"counts.pydantic/pydantic\": 351,\n", "    \"counts.seata/seata\": 57,\n", "    \"counts.spulec/freezegun\": 31,\n", "    \"avg_ground_truth_log_likelihoods.caddyserver/caddy\": -0.1354,\n", "    \"avg_ground_truth_log_likelihoods.ALL\": -0.1837,\n", "    \"avg_ground_truth_log_likelihoods.ethereum/go-ethereum\": -0.1631,\n", "    \"avg_ground_truth_log_likelihoods.google/mobly\": -0.1171,\n", "    \"avg_ground_truth_log_likelihoods.jaegertracing/jaeger\": -0.2123,\n", "    \"avg_ground_truth_log_likelihoods.moment/luxon\": -0.131,\n", "    \"avg_ground_truth_log_likelihoods.mrdoob/three.js\": -0.0481,\n", "    \"avg_ground_truth_log_likelihoods.pallets/flask\": -0.2106,\n", "    \"avg_ground_truth_log_likelihoods.pydantic/pydantic\": -0.2457,\n", "    \"avg_ground_truth_log_likelihoods.seata/seata\": -0.1246,\n", "    \"avg_ground_truth_log_likelihoods.spulec/freezegun\": -0.2723,\n", "    \"avg_ground_truth_token_accuracy.caddyserver/caddy\": 0.9624,\n", "    \"avg_ground_truth_token_accuracy.ALL\": 0.9442,\n", "    \"avg_ground_truth_token_accuracy.ethereum/go-ethereum\": 0.9467,\n", "    \"avg_ground_truth_token_accuracy.google/mobly\": 0.9672,\n", "    \"avg_ground_truth_token_accuracy.jaegertracing/jaeger\": 0.9354,\n", "    \"avg_ground_truth_token_accuracy.moment/luxon\": 0.9473,\n", "    \"avg_ground_truth_token_accuracy.mrdoob/three.js\": 0.9737,\n", "    \"avg_ground_truth_token_accuracy.pallets/flask\": 0.9298,\n", "    \"avg_ground_truth_token_accuracy.pydantic/pydantic\": 0.9295,\n", "    \"avg_ground_truth_token_accuracy.seata/seata\": 0.9704,\n", "    \"avg_ground_truth_token_accuracy.spulec/freezegun\": 0.9125,\n", "    \"avg_ground_truth_probabilities.caddyserver/caddy\": 0.8882,\n", "    \"avg_ground_truth_probabilities.ALL\": 0.8565,\n", "    \"avg_ground_truth_probabilities.ethereum/go-ethereum\": 0.8724,\n", "    \"avg_ground_truth_probabilities.google/mobly\": 0.8935,\n", "    \"avg_ground_truth_probabilities.jaegertracing/jaeger\": 0.8436,\n", "    \"avg_ground_truth_probabilities.moment/luxon\": 0.8923,\n", "    \"avg_ground_truth_probabilities.mrdoob/three.js\": 0.9548,\n", "    \"avg_ground_truth_probabilities.pallets/flask\": 0.8313,\n", "    \"avg_ground_truth_probabilities.pydantic/pydantic\": 0.8179,\n", "    \"avg_ground_truth_probabilities.seata/seata\": 0.9106,\n", "    \"avg_ground_truth_probabilities.spulec/freezegun\": 0.7831,\n", "    \"p10_ground_truth_log_likelihoods.caddyserver/caddy\": -0.323,\n", "    \"p10_ground_truth_log_likelihoods.ALL\": -0.4655,\n", "    \"p10_ground_truth_log_likelihoods.ethereum/go-ethereum\": -0.441,\n", "    \"p10_ground_truth_log_likelihoods.google/mobly\": -0.2348,\n", "    \"p10_ground_truth_log_likelihoods.jaegertracing/jaeger\": -0.5497,\n", "    \"p10_ground_truth_log_likelihoods.moment/luxon\": -0.2481,\n", "    \"p10_ground_truth_log_likelihoods.mrdoob/three.js\": -0.1221,\n", "    \"p10_ground_truth_log_likelihoods.pallets/flask\": -0.564,\n", "    \"p10_ground_truth_log_likelihoods.pydantic/pydantic\": -0.6441,\n", "    \"p10_ground_truth_log_likelihoods.seata/seata\": -0.255,\n", "    \"p10_ground_truth_log_likelihoods.spulec/freezegun\": -0.6015,\n", "    \"total_metrics.COUNT\": 1221,\n", "    \"total_metrics.EM\": 0.5774,\n", "    \"total_metrics.ES\": 0.8059,\n", "    \"total_metrics.AVERAGE_LOG_LIKELIHOOD\": -0.1837,\n", "    \"total_metrics.AVERAGE_TOKEN_ACCURACY\": 0.9442,\n", "    \"total_metrics.PASSED\": 940,\n", "    \"exec_fail_counts.caddyserver/caddy\": 9,\n", "    \"exec_fail_counts.TOTAL\": 281,\n", "    \"exec_fail_counts.ethereum/go-ethereum\": 26,\n", "    \"exec_fail_counts.google/mobly\": 38,\n", "    \"exec_fail_counts.jaegertracing/jaeger\": 32,\n", "    \"exec_fail_counts.moment/luxon\": 14,\n", "    \"exec_fail_counts.mrdoob/three.js\": 4,\n", "    \"exec_fail_counts.pallets/flask\": 37,\n", "    \"exec_fail_counts.pydantic/pydantic\": 106,\n", "    \"exec_fail_counts.seata/seata\": 2,\n", "    \"exec_fail_counts.spulec/freezegun\": 13,\n", "    \"exec_pass_counts.caddyserver/caddy\": 39,\n", "    \"exec_pass_counts.TOTAL\": 940,\n", "    \"exec_pass_counts.ethereum/go-ethereum\": 68,\n", "    \"exec_pass_counts.google/mobly\": 197,\n", "    \"exec_pass_counts.jaegertracing/jaeger\": 121,\n", "    \"exec_pass_counts.moment/luxon\": 37,\n", "    \"exec_pass_counts.mrdoob/three.js\": 47,\n", "    \"exec_pass_counts.pallets/flask\": 113,\n", "    \"exec_pass_counts.pydantic/pydantic\": 245,\n", "    \"exec_pass_counts.seata/seata\": 55,\n", "    \"exec_pass_counts.spulec/freezegun\": 18,\n", "}\n", "base_metrics = {\n", "    \"exact_match.caddyserver/caddy\": 0.5625,\n", "    \"exact_match.ALL\": 0.6667,\n", "    \"exact_match.ethereum/go-ethereum\": 0.5851,\n", "    \"exact_match.google/mobly\": 0.9745,\n", "    \"exact_match.jaegertracing/jaeger\": 0.5556,\n", "    \"exact_match.moment/luxon\": 0.6471,\n", "    \"exact_match.mrdoob/three.js\": 0.9608,\n", "    \"exact_match.pallets/flask\": 0.5267,\n", "    \"exact_match.pydantic/pydantic\": 0.5641,\n", "    \"exact_match.seata/seata\": 0.8421,\n", "    \"exact_match.spulec/freezegun\": 0.3548,\n", "    \"edit_similarity.caddyserver/caddy\": 0.8011,\n", "    \"edit_similarity.ALL\": 0.8423,\n", "    \"edit_similarity.ethereum/go-ethereum\": 0.8088,\n", "    \"edit_similarity.google/mobly\": 0.9839,\n", "    \"edit_similarity.jaegertracing/jaeger\": 0.8224,\n", "    \"edit_similarity.moment/luxon\": 0.8444,\n", "    \"edit_similarity.mrdoob/three.js\": 0.9737,\n", "    \"edit_similarity.pallets/flask\": 0.7769,\n", "    \"edit_similarity.pydantic/pydantic\": 0.7782,\n", "    \"edit_similarity.seata/seata\": 0.922,\n", "    \"edit_similarity.spulec/freezegun\": 0.709,\n", "    \"counts.caddyserver/caddy\": 48,\n", "    \"counts.ALL\": 1221,\n", "    \"counts.ethereum/go-ethereum\": 94,\n", "    \"counts.google/mobly\": 235,\n", "    \"counts.jaegertracing/jaeger\": 153,\n", "    \"counts.moment/luxon\": 51,\n", "    \"counts.mrdoob/three.js\": 51,\n", "    \"counts.pallets/flask\": 150,\n", "    \"counts.pydantic/pydantic\": 351,\n", "    \"counts.seata/seata\": 57,\n", "    \"counts.spulec/freezegun\": 31,\n", "    \"avg_ground_truth_log_likelihoods.caddyserver/caddy\": -0.1455,\n", "    \"avg_ground_truth_log_likelihoods.ALL\": -0.1876,\n", "    \"avg_ground_truth_log_likelihoods.ethereum/go-ethereum\": -0.1548,\n", "    \"avg_ground_truth_log_likelihoods.google/mobly\": -0.1055,\n", "    \"avg_ground_truth_log_likelihoods.jaegertracing/jaeger\": -0.1963,\n", "    \"avg_ground_truth_log_likelihoods.moment/luxon\": -0.1383,\n", "    \"avg_ground_truth_log_likelihoods.mrdoob/three.js\": -0.1251,\n", "    \"avg_ground_truth_log_likelihoods.pallets/flask\": -0.2131,\n", "    \"avg_ground_truth_log_likelihoods.pydantic/pydantic\": -0.2579,\n", "    \"avg_ground_truth_log_likelihoods.seata/seata\": -0.1421,\n", "    \"avg_ground_truth_log_likelihoods.spulec/freezegun\": -0.2806,\n", "    \"avg_ground_truth_token_accuracy.caddyserver/caddy\": 0.955,\n", "    \"avg_ground_truth_token_accuracy.ALL\": 0.9461,\n", "    \"avg_ground_truth_token_accuracy.ethereum/go-ethereum\": 0.9527,\n", "    \"avg_ground_truth_token_accuracy.google/mobly\": 0.9777,\n", "    \"avg_ground_truth_token_accuracy.jaegertracing/jaeger\": 0.9387,\n", "    \"avg_ground_truth_token_accuracy.moment/luxon\": 0.9489,\n", "    \"avg_ground_truth_token_accuracy.mrdoob/three.js\": 0.9817,\n", "    \"avg_ground_truth_token_accuracy.pallets/flask\": 0.9297,\n", "    \"avg_ground_truth_token_accuracy.pydantic/pydantic\": 0.9276,\n", "    \"avg_ground_truth_token_accuracy.seata/seata\": 0.9579,\n", "    \"avg_ground_truth_token_accuracy.spulec/freezegun\": 0.9133,\n", "    \"avg_ground_truth_probabilities.caddyserver/caddy\": 0.8791,\n", "    \"avg_ground_truth_probabilities.ALL\": 0.8557,\n", "    \"avg_ground_truth_probabilities.ethereum/go-ethereum\": 0.8789,\n", "    \"avg_ground_truth_probabilities.google/mobly\": 0.91,\n", "    \"avg_ground_truth_probabilities.jaegertracing/jaeger\": 0.8542,\n", "    \"avg_ground_truth_probabilities.moment/luxon\": 0.8829,\n", "    \"avg_ground_truth_probabilities.mrdoob/three.js\": 0.9008,\n", "    \"avg_ground_truth_probabilities.pallets/flask\": 0.8302,\n", "    \"avg_ground_truth_probabilities.pydantic/pydantic\": 0.8106,\n", "    \"avg_ground_truth_probabilities.seata/seata\": 0.8993,\n", "    \"avg_ground_truth_probabilities.spulec/freezegun\": 0.781,\n", "    \"p10_ground_truth_log_likelihoods.caddyserver/caddy\": -0.3383,\n", "    \"p10_ground_truth_log_likelihoods.ALL\": -0.4949,\n", "    \"p10_ground_truth_log_likelihoods.ethereum/go-ethereum\": -0.4662,\n", "    \"p10_ground_truth_log_likelihoods.google/mobly\": -0.329,\n", "    \"p10_ground_truth_log_likelihoods.jaegertracing/jaeger\": -0.4983,\n", "    \"p10_ground_truth_log_likelihoods.moment/luxon\": -0.3972,\n", "    \"p10_ground_truth_log_likelihoods.mrdoob/three.js\": -0.4277,\n", "    \"p10_ground_truth_log_likelihoods.pallets/flask\": -0.5779,\n", "    \"p10_ground_truth_log_likelihoods.pydantic/pydantic\": -0.6724,\n", "    \"p10_ground_truth_log_likelihoods.seata/seata\": -0.3205,\n", "    \"p10_ground_truth_log_likelihoods.spulec/freezegun\": -0.6189,\n", "    \"total_metrics.COUNT\": 1221,\n", "    \"total_metrics.EM\": 0.6667,\n", "    \"total_metrics.ES\": 0.8423,\n", "    \"total_metrics.AVERAGE_LOG_LIKELIHOOD\": -0.1876,\n", "    \"total_metrics.AVERAGE_TOKEN_ACCURACY\": 0.9461,\n", "    \"total_metrics.PASSED\": 1018,\n", "    \"exec_fail_counts.caddyserver/caddy\": 8,\n", "    \"exec_fail_counts.TOTAL\": 203,\n", "    \"exec_fail_counts.ethereum/go-ethereum\": 19,\n", "    \"exec_fail_counts.google/mobly\": 3,\n", "    \"exec_fail_counts.jaegertracing/jaeger\": 29,\n", "    \"exec_fail_counts.moment/luxon\": 13,\n", "    \"exec_fail_counts.mrdoob/three.js\": 1,\n", "    \"exec_fail_counts.pallets/flask\": 30,\n", "    \"exec_fail_counts.pydantic/pydantic\": 87,\n", "    \"exec_fail_counts.seata/seata\": 3,\n", "    \"exec_fail_counts.spulec/freezegun\": 10,\n", "    \"exec_pass_counts.caddyserver/caddy\": 40,\n", "    \"exec_pass_counts.TOTAL\": 1018,\n", "    \"exec_pass_counts.ethereum/go-ethereum\": 75,\n", "    \"exec_pass_counts.google/mobly\": 232,\n", "    \"exec_pass_counts.jaegertracing/jaeger\": 124,\n", "    \"exec_pass_counts.moment/luxon\": 38,\n", "    \"exec_pass_counts.mrdoob/three.js\": 50,\n", "    \"exec_pass_counts.pallets/flask\": 120,\n", "    \"exec_pass_counts.pydantic/pydantic\": 264,\n", "    \"exec_pass_counts.seata/seata\": 54,\n", "    \"exec_pass_counts.spulec/freezegun\": 21,\n", "}\n", "compare_metrics(base_metrics, train_metrics)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Samples Quantitatively"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["hindsight_eval_jsonl = BASE_DATA_DIR / \"exp/237/hindsight_eval.jsonl\"\n", "df = pd.read_json(hindsight_eval_jsonl, lines=True)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "stats = defaultdict(int)\n", "stats_array = defaultdict(list)\n", "for idx, row in df.iterrows():\n", "    stats[\"total\"] += 1\n", "    base_str = row[\"base_output\"]\n", "    train_str = row[\"train_output\"]\n", "    target_str = row[\"target\"]\n", "    if base_str == target_str:\n", "        stats[\"based_model_exact_match_target\"] += 1\n", "    if train_str == target_str:\n", "        stats[\"trained_model_exact_match_target\"] += 1\n", "    if base_str == train_str:\n", "        stats[\"based_model_exact_match_trained_model\"] += 1\n", "\n", "    stats[\"based_model_endswith_pause\"] += base_str.endswith(\"<|pause|>\")\n", "    stats[\"based_model_endswith_eos\"] += base_str.endswith(\"<|endoftext|>\")\n", "    stats[\"trained_model_endswith_pause\"] += train_str.endswith(\"<|pause|>\")\n", "    stats[\"trained_model_endswith_eos\"] += train_str.endswith(\"<|endoftext|>\")\n", "    stats[\"target_endswith_pause\"] += target_str.endswith(\"<|pause|>\")\n", "    stats[\"target_endswith_eos\"] += target_str.endswith(\"<|endoftext|>\")\n", "\n", "    base_str_len = len(base_str)\n", "    if base_str.endswith(\"<|endoftext|>\"):\n", "        base_str_len -= len(\"<|endoftext|>\")\n", "    elif base_str.endswith(\"<|pause|>\"):\n", "        base_str_len -= len(\"<|pause|>\")\n", "    stats_array[\"based_model_text_length\"].append(base_str_len)\n", "\n", "    train_str_len = len(train_str)\n", "    if train_str.endswith(\"<|endoftext|>\"):\n", "        train_str_len -= len(\"<|endoftext|>\")\n", "    elif train_str.endswith(\"<|pause|>\"):\n", "        train_str_len -= len(\"<|pause|>\")\n", "    stats_array[\"trained_model_text_length\"].append(train_str_len)\n", "\n", "    target_str_len = len(target_str)\n", "    if target_str.endswith(\"<|endoftext|>\"):\n", "        target_str_len -= len(\"<|endoftext|>\")\n", "    elif target_str.endswith(\"<|pause|>\"):\n", "        target_str_len -= len(\"<|pause|>\")\n", "    stats_array[\"target_text_length\"].append(target_str_len)\n", "\n", "print(dict(stats))\n", "print({u: sum(v) / len(v) for u, v in dict(stats_array).items()})"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "\n", "# Create figure with multiple subplots\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Distribution plot comparing all three\n", "sns.kdeplot(data=stats_array[\"based_model_text_length\"], label=\"Base Model\", ax=ax1)\n", "sns.kdeplot(\n", "    data=stats_array[\"trained_model_text_length\"], label=\"Trained Model\", ax=ax1\n", ")\n", "sns.kdeplot(data=stats_array[\"target_text_length\"], label=\"Target\", ax=ax1)\n", "ax1.set_title(\"Distribution of Text Lengths\")\n", "ax1.set_xlabel(\"Text Length\")\n", "ax1.legend()\n", "\n", "# 2. Scatter plot: Base vs Target\n", "sns.scatterplot(\n", "    x=stats_array[\"target_text_length\"],\n", "    y=stats_array[\"based_model_text_length\"],\n", "    alpha=0.5,\n", "    ax=ax2,\n", "    label=\"Base Model\",\n", ")\n", "sns.scatterplot(\n", "    x=stats_array[\"target_text_length\"],\n", "    y=stats_array[\"trained_model_text_length\"],\n", "    alpha=0.5,\n", "    ax=ax2,\n", "    label=\"Trained Model\",\n", ")\n", "ax2.plot(\n", "    [0, max(stats_array[\"target_text_length\"])],\n", "    [0, max(stats_array[\"target_text_length\"])],\n", "    \"r--\",\n", "    label=\"Perfect Match\",\n", ")\n", "ax2.set_title(\"Model Output Length vs Target Length\")\n", "ax2.set_xlabel(\"Target Length\")\n", "ax2.set_ylabel(\"Model Output Length\")\n", "ax2.legend()\n", "\n", "# 3. Box plot\n", "data_for_box = {\n", "    \"Base Model\": stats_array[\"based_model_text_length\"],\n", "    \"Trained Model\": stats_array[\"trained_model_text_length\"],\n", "    \"Target\": stats_array[\"target_text_length\"],\n", "}\n", "sns.boxplot(data=data_for_box, ax=ax3)\n", "ax3.set_title(\"Length Distribution Comparison\")\n", "ax3.set_ylabel(\"Text Length\")\n", "\n", "# 4. Length difference distribution\n", "base_diff = np.array(stats_array[\"based_model_text_length\"]) - np.array(\n", "    stats_array[\"target_text_length\"]\n", ")\n", "trained_diff = np.array(stats_array[\"trained_model_text_length\"]) - np.array(\n", "    stats_array[\"target_text_length\"]\n", ")\n", "sns.histplot(data=base_diff, label=\"Base Model\", alpha=0.5, ax=ax4)\n", "sns.histplot(data=trained_diff, label=\"Trained Model\", alpha=0.5, ax=ax4)\n", "ax4.set_title(\"Length Difference Distribution (Model - Target)\")\n", "ax4.set_xlabel(\"Length Difference\")\n", "ax4.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate and print statistical measures\n", "print(\"\\nStatistical Measures:\")\n", "print(\"\\nMean Lengths:\")\n", "print(f\"Base Model: {np.mean(stats_array['based_model_text_length']):.2f}\")\n", "print(f\"Trained Model: {np.mean(stats_array['trained_model_text_length']):.2f}\")\n", "print(f\"Target: {np.mean(stats_array['target_text_length']):.2f}\")\n", "\n", "print(\"\\nMedian Lengths:\")\n", "print(f\"Base Model: {np.median(stats_array['based_model_text_length']):.2f}\")\n", "print(f\"Trained Model: {np.median(stats_array['trained_model_text_length']):.2f}\")\n", "print(f\"Target: {np.median(stats_array['target_text_length']):.2f}\")\n", "\n", "print(\"\\nCorrelation with Target:\")\n", "print(\n", "    f\"Base Model: {np.corrcoef(stats_array['based_model_text_length'], stats_array['target_text_length'])[0,1]:.3f}\"\n", ")\n", "print(\n", "    f\"Trained Model: {np.corrcoef(stats_array['trained_model_text_length'], stats_array['target_text_length'])[0,1]:.3f}\"\n", ")\n", "\n", "print(\"\\nMean Absolute Error:\")\n", "print(f\"Base Model: {np.mean(np.abs(base_diff)):.2f}\")\n", "print(f\"Trained Model: {np.mean(np.abs(trained_diff)):.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Eval Multilang"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from colorama import Fore, Style"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["trained_path = \"/mnt/efs/augment/eval/jobs/2drVp5bk/000_EldenSystem_hydra_completed_patches.jsonl.zst\"\n", "base_path = \"/mnt/efs/augment/eval/jobs/nkNViv5D/000_EldenSystem_hydra_completed_patches.jsonl.zst\"\n", "\n", "trained_results = read_jsonl_zst(trained_path)\n", "base_results = read_jsonl_zst(base_path)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["char_start = base_results[0][\"patch\"][\"char_start\"]\n", "char_end = base_results[0][\"patch\"][\"char_end\"]\n", "print(base_results[0][\"patch\"][\"file_content\"][char_start:char_end])"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["def get_gt(result):\n", "    char_start = result[\"patch\"][\"char_start\"]\n", "    char_end = result[\"patch\"][\"char_end\"]\n", "    return result[\"patch\"][\"file_content\"][char_start:char_end]\n", "\n", "\n", "def print_sample(prefix, suffix, generation):\n", "    prefix_string = Fore.BLUE + prefix + Style.RESET_ALL\n", "    generation_string = Fore.YELLOW + generation + Style.RESET_ALL\n", "    suffix_string = Fore.BLUE + suffix + Style.RESET_ALL\n", "    print(prefix_string + generation_string + suffix_string)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["count = 0\n", "base_agree_count = 0\n", "trained_agree_count = 0\n", "for base_result, trained_result in zip(base_results, trained_results):\n", "    base_gt = get_gt(base_result)\n", "    trained_gt = get_gt(trained_result)\n", "    assert base_gt == trained_gt, \"ground truth is not the same\"\n", "    ground_truth = base_gt\n", "\n", "    if base_result[\"generation\"] != trained_result[\"generation\"] and (\n", "        base_result[\"generation\"] == ground_truth\n", "        or trained_result[\"generation\"] == ground_truth\n", "    ):\n", "        assert (\n", "            base_result[\"prefix\"] == trained_result[\"prefix\"]\n", "        ), \"prefixes are not the same\"\n", "        assert (\n", "            base_result[\"suffix\"] == trained_result[\"suffix\"]\n", "        ), \"suffixes are not the same\"\n", "        count += 1\n", "        print(Fore.WHITE + f\"Next Sample: {count}\" + Style.RESET_ALL)\n", "\n", "        print_sample(\n", "            prefix=base_result[\"prefix\"][-100:],\n", "            suffix=base_result[\"suffix\"][:100],\n", "            generation=base_gt,\n", "        )\n", "\n", "        base_agrees = base_result[\"generation\"] == ground_truth\n", "        base_agree_count += base_agrees\n", "        base_color = Fore.GREEN if base_agrees else Fore.RED\n", "        print(base_color + \"BASE\" + Style.RESET_ALL)\n", "        print(base_result[\"generation\"])\n", "\n", "        trained_agrees = trained_result[\"generation\"] == ground_truth\n", "        trained_agree_count += trained_agrees\n", "        trained_color = Fore.GREEN if trained_agrees else Fore.RED\n", "        print(trained_color + \"TRAINED\" + Style.RESET_ALL)\n", "        print(trained_result[\"generation\"])\n", "        print(\"=\" * 80)\n", "\n", "print(\n", "    f\"Found {count}/{len(base_results)} samples where base and train differ but one matches ground truth. Base agrees {base_agree_count} times, train agrees {train_agree_count} times\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### FBW (Old stuff)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["model_location = (\n", "    \"/mnt/efs/augment/checkpoints/pranay/sc2-eldenv4-15b-8k-bs2ks5k-quality-mps1\"\n", ")\n", "\n", "config_elden_quality_fbw = {\n", "    \"name\": \"fastbackward\",\n", "    \"checkpoint_path\": model_location,\n", "    \"override_prompt_formatter\": {\n", "        \"name\": \"ender\",\n", "    },\n", "    \"override_tokenizer\": \"starcoder2\",\n", "    \"model_parallel_size\": 1,\n", "    \"seq_length\": 7939,\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1024,\n", "        \"max_suffix_tokens\": 512,\n", "        \"max_signature_tokens\": 1024,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 6144,\n", "        \"component_order\": [\n", "            \"prefix\",\n", "            \"retrieval\",\n", "            \"signature\",\n", "            \"nearby_prefix\",\n", "            \"suffix\",\n", "        ],\n", "        \"context_quant_token_len\": 64,\n", "        \"nearby_prefix_token_len\": 512,\n", "        \"nearby_prefix_token_overlap\": 0,\n", "        \"nearby_suffix_token_len\": 0,\n", "        \"nearby_suffix_token_overlap\": 0,\n", "    },\n", "}\n", "model = create_model(config_elden_quality_fbw)\n", "assert isinstance(model, FastBackwardLLM)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load the model\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["model_location = \"/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw\"\n", "sha = \"3acca7923656f656c397dfefdba13deb286033d31a17033a83f01f98618591a1\"\n", "\n", "model_spec = model_specs.get_starcoder2_model_spec(\n", "    \"starcoder2-3b\", checkpoint_path=model_location, checkpoint_sha256=sha\n", ")\n", "step_fn = fwd_starcoder2.generate_step_fn(ms=model_spec)\n", "attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(ms=model_spec)\n", "attn = attn_factory(8192)  # some reason max seqlen\n", "\n", "tokens = [1, 2, 3]  # or whatever\n", "result = step_fn(tokens, attn)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from base.fastforward.checkpoints import save_load\n", "\n", "ffw_weights = save_load.load_weights(model_location, target_sha256=sha)\n", "bfw_weights = torch.load(\n", "    \"/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k/consolidated.00.pth\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
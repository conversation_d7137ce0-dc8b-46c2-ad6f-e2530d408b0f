# Trains on vanguard hindsight data with recency and edit event chunks
# Evals on dogfood hindsight data with recency and edit event chunks
# Objective: Does training on both recency and edit events help?
# Success Metric: The model does not perform worse as we train.
determined:
  description: "Training qwelden-b14 model on vanguard hindsight with recency and edit events, eval on dogfood qweldenv1 hindsight with recency and edit events."
  workspace: Dev
  project: pranay-hindsight

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/qwen25coder_14b.py

fastbackward_args:
  loss_mask_policy: fim
  batch_size: 1
  gradient_accumulation_steps: 8
  warmup_iters: 32
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  eval_interval: 50
  block_size: 7326 # (seq-1)
  use_activation_checkpointing: True

  train_data_path: /mnt/efs/augment/user/pranay/hindsight/vanguard_2024-11-01_2025-01-14/datasets/recency_and_edits/dataset
  eval_data_path: /mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets/recency_and_edits/dataset

  checkpoint_optimizer_state: False
  loss_function: sequence_chunked_cross_entropy
  checkpoint: "/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_fb"

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 2
  use_sequence_parallel: True

  run_name: hindsight_recency_and_edits_qwen14b_v2
  wandb_project: pranay-hindsight

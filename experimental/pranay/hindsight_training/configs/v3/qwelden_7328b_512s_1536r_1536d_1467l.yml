determined:
  description: Qwelden-b14 model on hindsight data with 7328b_512s_1536r_1536d_1467l.
    Train is vanguard, eval is dogfood.
  workspace: Dev
  project: pranay-hindsight
augment:
  podspec_path: 8xH100.yaml
  gpu_count: 64
fastbackward_configs:
- configs/qwen25coder_14b.py
fastbackward_args:
  loss_mask_policy: fim
  batch_size: 1
  gradient_accumulation_steps: 8
  warmup_iters: 32
  learning_rate: 1.0e-05
  min_lr: 1.0e-06
  decay_lr: true
  max_epochs: 1
  eval_interval: 50
  use_activation_checkpointing: true
  checkpoint_optimizer_state: false
  loss_function: sequence_chunked_cross_entropy
  checkpoint: /mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_fb
  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 2
  use_sequence_parallel: true
  wandb_project: pranay-hindsight
  train_data_path: /mnt/efs/augment/user/pranay/hindsight/vanguard_2024-11-01_2025-01-14/datasets/7328b_512s_1536r_1536d_1467l/dataset
  eval_data_path: /mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets/7328b_512s_1536r_1536d_1467l/dataset
  block_size: 7326
  run_name: hindsight_7328b_512s_1536r_1536d_1467l_qwen14b_3

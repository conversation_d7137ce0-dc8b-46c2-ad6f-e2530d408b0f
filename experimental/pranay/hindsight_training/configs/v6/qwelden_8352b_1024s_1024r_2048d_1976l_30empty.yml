# python3 /home/<USER>/augment/research/fastbackward/determined/launch.py --config_file experimental/pranay/hindsight_training/configs/v6/qwelden_8352b_1024s_1024r_2048d_1976l_30empty.yml
determined:
  description: Qwelden-b14 model on hindsight data with 8352b_1024s_1024r_2048d_1976l.
    Train is PERMISSIVE vanguard data and 30% empty, eval is dogfood. Do not trust eval too much.
  workspace: Dev
  project: pranay-hindsight-training
augment:
  podspec_path: 8xH100.yaml
  gpu_count: 64
fastbackward_configs:
- configs/qwen25coder_14b.py
fastbackward_args:
  loss_mask_policy: fim
  batch_size: 1
  gradient_accumulation_steps: 8
  warmup_iters: 32
  learning_rate: 1.0e-05
  min_lr: 1.0e-06
  decay_lr: true
  max_epochs: 1
  eval_interval: 50
  use_activation_checkpointing: true
  checkpoint_optimizer_state: false
  loss_function: sequence_chunked_cross_entropy
  checkpoint: /mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_fb
  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 2
  use_sequence_parallel: true
  wandb_project: pranay-hindsight-training
  train_data_path: /mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_mix_empty30_2024-11-01_2025-01-14/i0-vanguard0/datasets/8352b_1024s_1024r_2048d_1976l/dataset
  eval_data_path: /mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets/8352b_1024s_1024r_2048d_1976l/dataset
  block_size: 8350
  run_name: hindsight_8352b_1024s_1024r_2048d_1976l_qwen14b_permissive_empty30_6

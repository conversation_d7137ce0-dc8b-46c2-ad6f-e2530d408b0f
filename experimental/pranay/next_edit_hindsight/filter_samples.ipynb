{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Notebook that determines a heuristic for filtering out samples where the recent changes do not correlate with the next edit."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from pathlib import Path\n", "import pandas as pd\n", "import glob\n", "import pickle\n", "import numpy as np\n", "from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "from research.fastbackward.loss_masking import unmask_token_np\n", "from colorama import Fore, Style\n", "from IPython.display import display, HTML\n", "import html\n", "from base.prompt_format_next_edit.gen_prompt_formatter import decode_model_diff\n", "from experimental.pranay.next_edit_hindsight.diff_to_jupyter import display_diff\n", "\n", "from research.core.tokenizers import StarCoder2Tokenizer\n", "from base.tokenizers.tokenizer import NextEditGenSpecialTokens\n", "\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "DIFF_SECTION_TOKEN = tokenizer.diff_section_token\n", "PR_BASE_TOKEN = tokenizer.retrieval_section_token\n", "FILE_SEP_TOKEN = tokenizer.filename_token\n", "FIM_PREFIX_TOKEN = tokenizer.fim_prefix_token\n", "EOS_TOKEN = tokenizer.eod_token\n", "HAS_CHANGE_ID = tokenizer.has_change_id\n", "HAS_CHANGE_TOKEN = \"<|has_change|>\"\n", "BEGIN_SELECTION = \"[[To Replace]]\"\n", "BEGIN_REPLACEMENT = \"[[Begin Replacement]]\"\n", "\n", "\n", "def get_probability(score):\n", "    return np.round(np.exp(score), 3)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_results(parquet_pattern, num_files=2):\n", "    parquet_files = glob.glob(parquet_pattern)[:num_files]\n", "    df = pd.concat([pd.read_parquet(parquet_file) for parquet_file in parquet_files])\n", "    results = []\n", "    for row in df[\"pickled_results\"]:\n", "        results.extend(pickle.loads(row))\n", "    return results"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.next_edits.next_edits_dataset import HindsightEditGenProblem\n", "\n", "scored_1_parquet_pattern = \"/mnt/efs/spark-data/shared/next-edit/stage1/dogfood-v8_2025_02_22_42days/spark_input/*.parquet\"\n", "hindsight_edit_gen_problems: list[HindsightEditGenProblem] = get_results(\n", "    scored_1_parquet_pattern\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_stages import FormattedEditGenProblem\n", "\n", "scored_parquet_pattern = \"/mnt/efs/spark-data/shared/next-edit/stage3/dogfood-v8_2025_02_22_42days/R5_ethanol-K50-diff_1_2,P22_star2_seq12k_pause500_out600.scored/*.parquet\"\n", "formatted_edit_gen_problems: list[FormattedEditGenProblem] = get_results(\n", "    scored_parquet_pattern\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def merge_on_request_id(hindsight_edit_gen_problems, formatted_edit_gen_problems):\n", "    hindsight_edit_gen_problems_dict = {\n", "        p.origin.request_id: p for p in hindsight_edit_gen_problems\n", "    }\n", "    formatted_edit_gen_problems_dict = {\n", "        p.hindsight_origin.request_id: p for p in formatted_edit_gen_problems\n", "    }\n", "\n", "    hindsight_request_ids = set(hindsight_edit_gen_problems_dict.keys())\n", "    formatted_request_ids = set(formatted_edit_gen_problems_dict.keys())\n", "    print(f\"Number of hindsight request ids: {len(hindsight_request_ids)}\")\n", "    print(f\"Number of formatted request ids: {len(formatted_request_ids)}\")\n", "\n", "    common_request_ids = hindsight_request_ids.intersection(formatted_request_ids)\n", "    print(f\"Number of common request ids: {len(common_request_ids)}\")\n", "    common_request_ids = sorted(list(common_request_ids))\n", "\n", "    results = []\n", "    for request_id in common_request_ids:\n", "        hindsight_problem = hindsight_edit_gen_problems_dict[request_id]\n", "        formatted_problem = formatted_edit_gen_problems_dict[request_id]\n", "        results.append(\n", "            {\n", "                \"hindsight_problem\": hindsight_problem,\n", "                \"formatted_problem\": formatted_problem,\n", "            }\n", "        )\n", "    return results"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["results = merge_on_request_id(hindsight_edit_gen_problems, formatted_edit_gen_problems)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def filter_result_v1(result):\n", "    \"\"\"First filtering rule set - focuses on presence of changes and high probabilities.\"\"\"\n", "    formatted_problem: FormattedEditGenProblem = result[\"formatted_problem\"]\n", "    # origin_probability = get_probability(\n", "    #     formatted_problem.debug_info[\"origin_score\"][0]\n", "    # )\n", "    # no_prdiff_probability = get_probability(\n", "    #     formatted_problem.debug_info[\"no_prdiff_score\"][0]\n", "    # )\n", "    token_list = unmask_token_np(formatted_problem.tokens).tolist()\n", "    has_change = HAS_CHANGE_ID in token_list\n", "    # prompt_text = tokenizer.detokenize(token_list)\n", "    return has_change\n", "\n", "\n", "def filter_result_v2(result):\n", "    \"\"\"Second filtering rule set - focuses on the difference between probabilities.\"\"\"\n", "    formatted_problem: FormattedEditGenProblem = result[\"formatted_problem\"]\n", "    origin_probability = get_probability(\n", "        formatted_problem.debug_info[\"origin_score\"][0]\n", "    )\n", "    no_prdiff_probability = get_probability(\n", "        formatted_problem.debug_info[\"no_prdiff_score\"][0]\n", "    )\n", "\n", "    token_list = unmask_token_np(formatted_problem.tokens).tolist()\n", "    # prompt_text = tokenizer.detokenize(token_list)\n", "    has_change = HAS_CHANGE_ID in token_list\n", "\n", "    return has_change and origin_probability > no_prdiff_probability + 0.15"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def apply_filters_and_compare(results):\n", "    \"\"\"Apply both filter sets and compare the results, assuming v2 is a subset of v1.\"\"\"\n", "    # Apply both filters\n", "    filtered_v1 = [result for result in results if filter_result_v1(result)]\n", "    filtered_v2 = [result for result in results if filter_result_v2(result)]\n", "\n", "    # # Get request IDs for each filtered set\n", "    request_ids_v1 = {\n", "        result[\"hindsight_problem\"].origin.request_id for result in filtered_v1\n", "    }\n", "    request_ids_v2 = {\n", "        result[\"hindsight_problem\"].origin.request_id for result in filtered_v2\n", "    }\n", "\n", "    # # Find samples in v1 but not in v2 (dropped by the stricter filter)\n", "    dropped_ids = request_ids_v1 - request_ids_v2\n", "\n", "    # # Print summary statistics\n", "    print(f\"Total samples: {len(results)}\")\n", "    print(\n", "        f\"Samples passing filter v1 (broader filter): {len(filtered_v1)} ({len(filtered_v1)/len(results):.2%})\"\n", "    )\n", "    print(\n", "        f\"Samples passing filter v2 (stricter filter): {len(filtered_v2)} ({len(filtered_v2)/len(results):.2%})\"\n", "    )\n", "    print(\n", "        f\"Samples dropped by stricter filter: {len(dropped_ids)} ({len(dropped_ids)/len(filtered_v1):.2%} of v1)\"\n", "    )\n", "\n", "    # Verify if v2 is actually a subset of v1\n", "    unexpected_ids = request_ids_v2 - request_ids_v1\n", "    if unexpected_ids:\n", "        print(\n", "            f\"WARNING: Found {len(unexpected_ids)} samples in v2 that are not in v1. V2 is not a strict subset of V1.\"\n", "        )\n", "\n", "    dropped_examples = [\n", "        r\n", "        for r in filtered_v1\n", "        if r[\"hindsight_problem\"].origin.request_id in dropped_ids\n", "    ]\n", "\n", "    return {\n", "        \"filtered_v1\": filtered_v1,\n", "        \"filtered_v2\": filtered_v2,\n", "        \"dropped_examples\": dropped_examples,\n", "    }"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Apply filters and get comparison results\n", "filter_comparison = apply_filters_and_compare(results)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# results = filter_comparison[\"filtered_v1\"]\n", "# avg_origin_prob = []\n", "# avg_no_prdiff_prob = []\n", "# for result in results:\n", "#     formatted_problem: FormattedEditGenProblem = result[\"formatted_problem\"]\n", "#     origin_probability = get_probability(\n", "#         formatted_problem.debug_info[\"origin_score\"][0]\n", "#     )\n", "#     no_prdiff_probability = get_probability(\n", "#         formatted_problem.debug_info[\"no_prdiff_score\"][0]\n", "#     )\n", "#     avg_origin_prob.append(origin_probability)\n", "#     avg_no_prdiff_prob.append(no_prdiff_probability)\n", "\n", "# print(f\"Average median origin probability: {np.median(avg_origin_prob):.3f}\")\n", "# print(f\"Average median no prdiff probability: {np.median(avg_no_prdiff_prob):.3f}\")\n", "# # compare average before vs after"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def text_to_int_lines(text) -> dict[int, str]:\n", "    line_number_to_line = {}\n", "    all_lines = text.split(\"\\n\")\n", "    for line in all_lines:\n", "        for i, char in enumerate(line):\n", "            if char in \"|+-\":\n", "                line_number = line[:i].strip()\n", "                if line_number.isdigit():\n", "                    line_number = int(line_number)\n", "                    line_number_to_line[line_number] = line\n", "                break\n", "\n", "    return line_number_to_line\n", "\n", "\n", "def minimize_change_text(change_text, min_line, max_line, tolerance=8):\n", "    line_number_to_line = text_to_int_lines(change_text)\n", "    lines = []\n", "    for line_number in range(min_line - tolerance, max_line + tolerance + 1):\n", "        if line_number in line_number_to_line:\n", "            lines.append(line_number_to_line[line_number])\n", "    return \"\\n\".join(lines)\n", "\n", "\n", "def print_info(formatted_problem: FormattedEditGenProblem):\n", "    token_list = unmask_token_np(formatted_problem.tokens).tolist()\n", "    prompt_text = tokenizer.detokenize(token_list)\n", "\n", "    try:\n", "        diff_section_text = prompt_text[\n", "            prompt_text.index(DIFF_SECTION_TOKEN)\n", "            + len(DIFF_SECTION_TOKEN)\n", "            + 1 : prompt_text.index(PR_BASE_TOKEN) + len(PR_BASE_TOKEN)\n", "        ]\n", "        file_text = prompt_text[\n", "            prompt_text.index(FILE_SEP_TOKEN) + len(FILE_SEP_TOKEN) : prompt_text.index(\n", "                FIM_PREFIX_TOKEN\n", "            )\n", "            - 1\n", "        ]\n", "        edit_location_text = prompt_text[\n", "            prompt_text.index(BEGIN_SELECTION)\n", "            + len(BEGIN_SELECTION)\n", "            + 1 : prompt_text.index(BEGIN_REPLACEMENT) - 2\n", "        ]\n", "        change_text = prompt_text[\n", "            prompt_text.index(HAS_CHANGE_TOKEN)\n", "            + len(HAS_CHANGE_TOKEN) : prompt_text.index(EOS_TOKEN)\n", "        ]\n", "\n", "        lines_in_change_text = text_to_int_lines(change_text).keys()\n", "        min_line, max_line = min(lines_in_change_text), max(lines_in_change_text)\n", "\n", "        edit_location_lines = text_to_int_lines(edit_location_text).keys()\n", "        max_edit_line = max(edit_location_lines)\n", "        edit_location_text_2 = minimize_change_text(\n", "            edit_location_text, min_line, max_line\n", "        )\n", "\n", "        decoded_change = decode_model_diff(edit_location_text, change_text)\n", "\n", "        threshold = 8\n", "        start_idx = max(0, min_line - threshold)\n", "        relative_from_end_idx = max_edit_line - (max_line + threshold)\n", "        decoded_change_before = decoded_change.before.split(\"\\n\")\n", "        decoded_change_before = \"\\n\".join(\n", "            decoded_change_before[\n", "                start_idx : len(decoded_change_before) - relative_from_end_idx\n", "            ]\n", "        )\n", "        decoded_change_after = decoded_change.after.split(\"\\n\")\n", "        decoded_change_after = \"\\n\".join(\n", "            decoded_change_after[\n", "                start_idx : len(decoded_change_after) - relative_from_end_idx\n", "            ]\n", "        )\n", "\n", "        change_diff_str = display_diff(\n", "            decoded_change_before, decoded_change_after, diff_type=\"precise_line\"\n", "        )\n", "\n", "        return {\n", "            \"diff_section_text\": html.escape(diff_section_text),\n", "            \"file_text\": html.escape(file_text),\n", "            \"edit_location_text\": html.escape(edit_location_text),\n", "            \"edit_location_text_2\": html.escape(edit_location_text_2),\n", "            \"change_text\": html.escape(change_text),\n", "            \"change_diff_str\": change_diff_str,\n", "        }\n", "    except Exception as e:\n", "        print(\n", "            highlight_special_tokens(\n", "                tokenizer.detokenize(token_list), tokenizer.all_special_tokens()\n", "            )\n", "        )\n", "        print(tokenizer.detokenize(formatted_problem.debug_info[\"output_tokens\"]))\n", "        raise e\n", "\n", "\n", "# Function to display examples of samples dropped by the stricter filter\n", "def display_dropped_examples(results, offset=0, num_examples=5):\n", "    \"\"\"Display examples.\"\"\"\n", "    # Display up to num_examples\n", "    for i, result in enumerate(results[offset : offset + num_examples]):\n", "        hindsight_problem = result[\"hindsight_problem\"]\n", "        formatted_problem = result[\"formatted_problem\"]\n", "\n", "        request_id = hindsight_problem.origin.request_id\n", "        # Get probabilities for analysis\n", "        origin_probability = get_probability(\n", "            formatted_problem.debug_info[\"origin_score\"][0]\n", "        )\n", "        no_prdiff_probability = get_probability(\n", "            formatted_problem.debug_info[\"no_prdiff_score\"][0]\n", "        )\n", "        diff = round(origin_probability - no_prdiff_probability, 2)\n", "\n", "        def get_color(value, green_threshold=0.7, orange_threshold=0.4):\n", "            if value > green_threshold:\n", "                return \"green\"\n", "            elif value > orange_threshold:\n", "                return \"orange\"\n", "            else:\n", "                return \"red\"\n", "\n", "        origin_color = get_color(origin_probability)\n", "        no_prdiff_color = get_color(no_prdiff_probability)\n", "        diff_color = get_color(diff, green_threshold=0.1, orange_threshold=0)\n", "\n", "        origin_probability = (\n", "            f\"<span style='color: {origin_color}'>{origin_probability:.3g}</span>\"\n", "        )\n", "        no_prdiff_probability = (\n", "            f\"<span style='color: {no_prdiff_color}'>{no_prdiff_probability:.3g}</span>\"\n", "        )\n", "        diff = f\"<span style='color: {diff_color}'>{diff:.3g}</span>\"\n", "\n", "        data = print_info(formatted_problem)\n", "        output = f\"\"\"\n", "<span style=\"color: cyan;\">{data['diff_section_text']}</span>\n", "<span style=\"color: magenta;\">{data['file_text']}</span> \n", "<span style=\"color: magenta;\">{data['edit_location_text_2']}</span>\n", "<span style=\"color: green;\">{data['change_text']}</span>\n", "<span>{data['change_diff_str']}</span>\n", "\"\"\"\n", "\n", "        html = f\"\"\"\n", "        <details>\n", "            <summary><b>Example {i+1} - Request ID: {request_id}\n", "            <br>(Origin: {origin_probability}, No PRDiff: {no_prdiff_probability}, Diff: {diff})</b></summary>\n", "            <div style=\"margin-left: 20px; border-left: 2px solid #ccc; padding-left: 10px;\">\n", "                <pre style=\"white-space: pre-wrap;\">{output}</pre>\n", "            </div>\n", "        </details>\n", "        \"\"\"\n", "\n", "        display(HTML(html))\n", "        print(\"\\n\" + \"-\" * 50 + \"\\n\")\n", "\n", "\n", "display_dropped_examples(filter_comparison[\"filtered_v2\"], offset=0, num_examples=20)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# render_hindsight_edit_gen_problem(hindsight_problem, _UIParams(2000, 1000, 1000))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON> completion\n", "\n", "* Export hindsight data you want to eval \n", "* add model to pieces.yml\n", "* combinations.yml in configs/evals/ - to generate combinations of stuff\n", "* commit the code\n", "* Look at scratch.sh for useful commands, see what it seelcts: v3.1 data on v1.1 model |  v1.1 data on v3.1 model | v1.1 data on v1.1 model | v3.1 data on v3.1 model. test new model on all the old data, and the new data on all the models\n", "\n", "v3.1 vs 1.1\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
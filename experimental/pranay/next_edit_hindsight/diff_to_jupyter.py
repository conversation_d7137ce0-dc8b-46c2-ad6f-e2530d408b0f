#!/usr/bin/env python3
"""
Module to convert string diffs to HTML for display in Jupyter notebooks.
"""

import html
from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    ModSpan,
    NoopSpan,
    StrDiff,
    precise_char_diff,
    precise_line_diff,
    line_diff,
)


def diff_to_html(diff: StrDiff) -> str:
    """
    Convert a StrDiff to HTML with the same colors as print_diff().

    Args:
        diff: The StrDiff object

    Returns:
        HTML string with the formatted diff
    """

    def highlight_spaces(text: str) -> str:
        """Replace spaces and newlines with visible characters."""
        return text.replace(" ", "·").replace("\n", "↩\n")

    def add_style(text: str) -> str:
        """Style for added text (bright blue)."""
        text = html.escape(highlight_spaces(text))
        return f'<span style="color: #3498db; font-weight: bold;">{text}</span>'

    def del_style(text: str) -> str:
        """Style for deleted text (dim red)."""
        text = html.escape(highlight_spaces(text))
        return f'<span style="color: #e74c3c; opacity: 0.7;">{text}</span>'

    def normal_style(text: str) -> str:
        """Style for unchanged text."""
        text = html.escape(highlight_spaces(text))
        return f"<span>{text}</span>"

    def mod_style(old: str, new: str) -> str:
        """Style for modified text (deleted in yellow, added in cyan)."""
        deleted = html.escape(highlight_spaces(old))
        added = html.escape(highlight_spaces(new))
        deleted_html = f'<span style="color: #f39c12; opacity: 0.7;">{deleted}</span>'
        added_html = f'<span style="color: #1abc9c; font-weight: bold;">{added}</span>'
        return f"{deleted_html}{added_html}"

    html_parts = []
    for op in diff.spans:
        if isinstance(op, AddedSpan):
            html_parts.append(add_style(op.inserted))
        elif isinstance(op, DeletedSpan):
            html_parts.append(del_style(op.deleted))
        elif isinstance(op, ModSpan):
            html_parts.append(mod_style(op.before, op.after))
        elif isinstance(op, NoopSpan):
            html_parts.append(normal_style(op.text))

    # Wrap in a pre tag to preserve formatting
    return f'<pre style="font-family: monospace; background-color: #2d2d2d; color: #e0e0e0; padding: 10px; border-radius: 5px;">{"".join(html_parts)}</pre>'


def display_diff(before: str, after: str, diff_type: str = "char") -> str:
    """
    Generate HTML that highlights the differences between two strings.
    Suitable for use with Jupyter's display(HTML(html)).

    Args:
        before: The original string
        after: The modified string
        diff_type: The type of diff to use ('char', 'line', or 'precise_line')

    Returns:
        HTML string with the formatted diff
    """

    if diff_type == "char":
        diff = precise_char_diff(before, after)
    elif diff_type == "line":
        diff = line_diff(before, after)
    elif diff_type == "precise_line":
        diff = precise_line_diff(before, after)
    else:
        diff = precise_char_diff(before, after)

    html_content = diff_to_html(diff)

    # Add a legend above the diff
    legend_html = """
    <div style="display: flex; gap: 20px; margin-bottom: 10px; background-color: #2d2d2d; color: #e0e0e0; padding: 10px; border-radius: 5px;">
        <div style="display: flex; align-items: center;">
            <div style="width: 20px; height: 20px; background-color: #3498db; margin-right: 5px; border-radius: 3px;"></div>
            <span>Added</span>
        </div>
        <div style="display: flex; align-items: center;">
            <div style="width: 20px; height: 20px; background-color: #e74c3c; margin-right: 5px; border-radius: 3px;"></div>
            <span>Deleted</span>
        </div>
        <div style="display: flex; align-items: center;">
            <div style="width: 20px; height: 20px; background-color: #f39c12; margin-right: 5px; border-radius: 3px;"></div>
            <span>Modified (old)</span>
        </div>
        <div style="display: flex; align-items: center;">
            <div style="width: 20px; height: 20px; background-color: #1abc9c; margin-right: 5px; border-radius: 3px;"></div>
            <span>Modified (new)</span>
        </div>
    </div>
    """
    legend_html = "<div></div>"

    return legend_html + html_content

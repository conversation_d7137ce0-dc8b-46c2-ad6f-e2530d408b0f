{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets import tenants\n", "import base.tokenizers\n", "import re\n", "\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from base.datasets.completion import CompletionDatum, CompletionResolution\n", "\n", "from research.prism import train\n", "from research.prism.train import main as train_main\n", "from research.prism.train import create_features\n", "import matplotlib.pyplot as plt\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    extract_completion_filter_features,\n", ")\n", "from collections import defaultdict\n", "\n", "from research.core.data_paths import canonicalize_path\n", "from pathlib import Path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from sklearn.metrics import confusion_matrix\n", "from sklearn.metrics import ConfusionMatrixDisplay\n", "import numpy as np\n", "from research.prism.run_data_generation import transform_completion_datum\n", "\n", "TENANT_NAME = \"dogfood\"\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "tokenizer = base.tokenizers.create_tokenizer_by_name(\"starcoder2\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from IPython.core.interactiveshell import InteractiveShell\n", "\n", "InteractiveShell.ast_node_interactivity = \"all\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def print_dict_multiline(dictionary, keys=None):\n", "    if keys is not None:\n", "        dictionary = {k: dictionary[k] for k in keys if k in dictionary}\n", "\n", "    for key, value in dictionary.items():\n", "        if isinstance(value, str):\n", "            print(f'\"{key}\":')\n", "            for line in value.split(\"\\n\"):\n", "                print(f\"    {line}\")\n", "        else:\n", "            print(f'\"{key}\": {value}')\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hindsight Dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["psuedocode:\n", "1. load `data` from `mnt/efs/augment/eval/jobs/<JOB_ID>/000_null_HindsightCompletionTask_completed_patches.jsonl.zst`\n", "2. create list of request_id from `data[i][\"artifacts\"][0][\"request_ids\"][0]`.\n", "3. Query BigQuery with list of request_id and save the CompletionDatum. This data has everything (token log probs, generated text, etc.) except for the ground truth and resolution.accepted\n", "4. Retrieve the ground truth from `data[i][\"ground_truth\"]` and create a list\n", "5. Compare the ground truth with the generated text via heuristics to see if the true ground truth label is accepted or rejected\n", "6. (Optional) Compute interesting stats between resolution.accepted and heuristic-based ground truth label accepted or rejected. To get resolution.accepted, this is in data from `/mnt/efs/augment/data/eval/hindsight/2024-05-01-v1.0/dogfood/data.jsonl.zst` on the original requests (data[i].completion.resolution.accepted)\n", "7. Train a model with this data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_hindsight_and_model_data(\n", "    job_row, hindsight_row, missing_info=defaultdict(int), get_prefix: bool = False\n", "):\n", "    assert job_row[\"request_id\"] == hindsight_row[\"completion\"][\"request_id\"]\n", "    assert job_row[\"ground_truth\"] == hindsight_row[\"ground_truth\"]\n", "\n", "    model_request_id = job_row[\"artifacts\"][0][\"request_ids\"][-1]\n", "    model_generated_text = job_row[\"generation\"]\n", "\n", "    original_request_id = hindsight_row[\"completion\"][\"request_id\"]\n", "    original_model = hindsight_row[\"completion\"][\"response\"][\"model\"]\n", "    original_generated_text = hindsight_row[\"completion\"][\"response\"][\"text\"]\n", "\n", "    ground_truth = hindsight_row[\"ground_truth\"]\n", "    try:\n", "        resolution_accepted = hindsight_row[\"completion\"][\"resolution\"][\"accepted\"]\n", "    except Exception:\n", "        missing_info[\"missing_resolution\"] += 1\n", "        return None, None\n", "\n", "    info = {\n", "        \"original_request_id\": original_request_id,\n", "        \"model_request_id\": model_request_id,\n", "        \"original_generated_text\": original_generated_text,\n", "        \"model_generated_text\": model_generated_text,\n", "        \"ground_truth\": ground_truth,\n", "        \"original_model\": original_model,\n", "        \"resolution_accepted\": resolution_accepted,\n", "    }\n", "\n", "    if get_prefix:\n", "        prefix = hindsight_row[\"completion\"][\"request\"][\"prefix\"]\n", "        info[\"prefix\"] = prefix\n", "        if not prefix:\n", "            missing_info[\"missing_prefix\"] += 1\n", "            return None, None\n", "\n", "    assert model_request_id\n", "    assert model_generated_text is not None\n", "    assert original_request_id\n", "    assert original_model\n", "    assert original_generated_text is not None\n", "    assert ground_truth\n", "\n", "    return model_request_id, info\n", "\n", "\n", "def get_model_request_ids(get_prefix: bool = False):\n", "    HINDSIGHT_BASE_DIR = Path(canonicalize_path(\"data/eval/hindsight/\"))\n", "    MODEL_BASE_DIR = Path(canonicalize_path(\"eval/jobs/\"))\n", "\n", "    # Comes from https://www.notion.so/2024-07-10-Hindsight-1-0-Results-cdc10573512f4e4e95f9755cf80d3cc4,\n", "    # which is the hindsight data from May and June ran through eldenv3-15b.\n", "    DATASET_TO_MODEL_JOBS = {\n", "        \"2024-05-01-v1.0/dogfood\": \"7Vnn8paP\",\n", "        \"2024-06-01-v1.0/dogfood\": \"hs5qkbrK\",\n", "    }\n", "\n", "    model_request_ids = {}\n", "    missing_info = defaultdict(int)\n", "\n", "    for dataset_name, job_id in DATASET_TO_MODEL_JOBS.items():\n", "        job_dir = (\n", "            MODEL_BASE_DIR\n", "            / job_id\n", "            / \"000_null_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "        )\n", "        hindsight_dir = HINDSIGHT_BASE_DIR / dataset_name / \"data.jsonl.zst\"\n", "        if not job_dir.exists() or not hindsight_dir.exists():\n", "            raise ValueError(f\"Missing {job_dir} or {hindsight_dir}\")\n", "        job_data = read_jsonl_zst(job_dir)\n", "        hindsight_data = read_jsonl_zst(hindsight_dir)\n", "        for job_row, hindsight_row in zip(job_data, hindsight_data):\n", "            model_request_id, info = get_hindsight_and_model_data(\n", "                job_row, hindsight_row, missing_info, get_prefix=get_prefix\n", "            )\n", "            if info:\n", "                if model_request_id in model_request_ids:\n", "                    raise ValueError(f\"Duplicate model request id {model_request_id}\")\n", "                model_request_ids[model_request_id] = info\n", "\n", "    eldenv3_requests = list(model_request_ids.keys())\n", "\n", "    print(f\"{dict(missing_info)}\")\n", "    print(f\"{len(model_request_ids)} model request ids\")\n", "\n", "    return model_request_ids, eldenv3_requests"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def num_accepts_rejects(model_request_ids):\n", "    num_accepts, num_rejects = 0, 0\n", "    heuristic_num_accepts, heuristic_num_rejects = 0, 0\n", "\n", "    if isinstance(model_request_ids, dict):\n", "        for idx, (model_request_id, info) in enumerate(model_request_ids.items()):\n", "            if info[\"resolution_accepted\"] is None:\n", "                pass\n", "            elif info[\"resolution_accepted\"]:\n", "                num_accepts += 1\n", "            else:\n", "                num_rejects += 1\n", "\n", "            if \"accepted_heuristic\" in info:\n", "                if info[\"accepted_heuristic\"] is None:\n", "                    pass\n", "                if info[\"accepted_heuristic\"]:\n", "                    heuristic_num_accepts += 1\n", "                else:\n", "                    heuristic_num_rejects += 1\n", "\n", "        return num_accepts, num_rejects, heuristic_num_accepts, heuristic_num_rejects\n", "    elif isinstance(model_request_ids, list):\n", "        for idx, model_request_id in enumerate(model_request_ids):\n", "            try:\n", "                if model_request_id.resolution.accepted:\n", "                    num_accepts += 1\n", "                else:\n", "                    num_rejects += 1\n", "            except Exception:\n", "                pass\n", "        return num_accepts, num_rejects, None, None\n", "    else:\n", "        raise ValueError(f\"Unknown model request ids type {type(model_request_ids)}\")\n", "\n", "\n", "# num_accepts, num_rejects, heuristic_num_accepts, heuristic_num_rejects = num_accepts_rejects(model_request_ids)\n", "# print(f\"Num accepts: {num_accepts}, num rejects: {num_rejects}\")\n", "# print(f\"Num heuristic accepted: {heuristic_num_accepts}, num heuristic rejected: {heuristic_num_rejects}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def longestCommonSubsequence(generated_text: str, ground_truth: str):\n", "    n = len(generated_text)\n", "\n", "    # We only want to compare the first n characters of the ground truth, where n is the length of the generated text + some padding\n", "    m = min(len(ground_truth), max(n + 10, int(n * 1.5)))\n", "\n", "    dpGrid = [[0] * (m + 1) for _ in range(n + 1)]\n", "\n", "    for row in range(n - 1, -1, -1):\n", "        for col in range(m - 1, -1, -1):\n", "            if generated_text[row] == ground_truth[col]:\n", "                dpGrid[row][col] = 1 + dpGrid[row + 1][col + 1]\n", "            else:\n", "                dpGrid[row][col] = max(dpGrid[row + 1][col], dpGrid[row][col + 1])\n", "    lcs_len = dpGrid[0][0]\n", "    percent = lcs_len / len(generated_text)\n", "\n", "    return lcs_len, percent"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def remove_empty_last_line(text):\n", "    return re.sub(r\"\\n\\s*$\", \"\", \"\" + text)\n", "\n", "\n", "def remove_whitespaces(text):\n", "    return (\"\" + text).replace(\" \", \"\")\n", "\n", "\n", "def resolution_accepted_heuristic(\n", "    model_request_id: dict,\n", "    use_model_generated_text: bool = True,\n", "    remove_whitespace: bool = False,\n", "):\n", "    \"\"\"\n", "    returns True if the original completion is a prefix of the ground truth or vice versa\n", "    \"\"\"\n", "    original_generated_text = model_request_id[\"original_generated_text\"]\n", "    model_generated_text = model_request_id[\"model_generated_text\"]\n", "\n", "    ground_truth = model_request_id[\"ground_truth\"]\n", "    # resolution_accepted = model_request_id[\"resolution_accepted\"]\n", "\n", "    # remove empty \\n line at the end of the string if it exists\n", "    original_generated_text = remove_empty_last_line(original_generated_text)\n", "    model_generated_text = remove_empty_last_line(model_generated_text)\n", "    ground_truth = remove_empty_last_line(ground_truth)\n", "\n", "    if remove_whitespace:\n", "        original_generated_text = remove_whitespaces(original_generated_text)\n", "        model_generated_text = remove_whitespaces(model_generated_text)\n", "        ground_truth = remove_whitespaces(ground_truth)\n", "\n", "    if use_model_generated_text:\n", "        generated_text = model_generated_text\n", "    else:\n", "        generated_text = original_generated_text\n", "\n", "    confident_correct = None\n", "    reasoning = \"N/A\"\n", "\n", "    if len(generated_text) == 0:\n", "        confident_correct = None\n", "        reasoning = \"generated text is empty\"\n", "        model_request_id[\"accepted_heuristic\"] = confident_correct\n", "        model_request_id[\"accepted_heuristic_reasoning\"] = reasoning\n", "        return model_request_id, reasoning\n", "\n", "    ground_truth_percent_of_completion = len(ground_truth) / len(generated_text)\n", "\n", "    _, percent_of_completion_matches = longestCommonSubsequence(\n", "        generated_text, ground_truth\n", "    )\n", "\n", "    model_request_id[\"percent_of_completion_matches\"] = percent_of_completion_matches\n", "\n", "    if ground_truth_percent_of_completion < 0.1 and len(ground_truth.strip()) <= 3:\n", "        confident_correct = None\n", "        reasoning = \"ground truth is too short to tell\"\n", "        # del model_request_id[\"percent_of_completion_matches\"]\n", "    elif percent_of_completion_matches > 0.8:\n", "        confident_correct = True\n", "        reasoning = \"most of generated text is in the ground truth\"\n", "    else:\n", "        confident_correct = False\n", "        reasoning = \"most of generated text is not in the ground truth\"\n", "\n", "    # if ground_truth_percent_of_completion < 0.1 and len(ground_truth.strip()) <= 3:\n", "    #     confident_correct = None\n", "    #     reasoning = \"ground truth is too short to tell\"\n", "    #     # del model_request_id[\"percent_of_completion_matches\"]\n", "    # elif percent_of_completion_matches >= 1:\n", "    #     confident_correct = True\n", "    #     reasoning = \"most of generated text is in the ground truth\"\n", "    # else:\n", "    #     confident_correct = resolution_accepted\n", "    # reasoning = \"using resolution_accepted as completion match is not close to exact\"\n", "\n", "    model_request_id[\"accepted_heuristic\"] = confident_correct\n", "    model_request_id[\"accepted_heuristic_reasoning\"] = reasoning\n", "\n", "    return model_request_id, reasoning"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def compute_accepted_heuristic(\n", "    model_request_ids,\n", "    use_model_generated_text: bool = True,\n", "    remove_whitespace=True,\n", "    debug=True,\n", "):\n", "    all_stats = defaultdict(list)\n", "    disagreements_stats = defaultdict(list)\n", "    disagreements = 0\n", "    old_is_accepted = []\n", "    new_is_accepted = []\n", "\n", "    for idx, (model_request_id, info) in enumerate(model_request_ids.items()):\n", "        result, reasoning = resolution_accepted_heuristic(\n", "            info,\n", "            use_model_generated_text=use_model_generated_text,\n", "            remove_whitespace=remove_whitespace,\n", "        )\n", "\n", "        if (\n", "            result[\"accepted_heuristic\"] is not None\n", "            and result[\"resolution_accepted\"] is not None\n", "        ):\n", "            if result[\"accepted_heuristic\"]:\n", "                new_is_accepted.append(1)\n", "            else:\n", "                new_is_accepted.append(0)\n", "            if result[\"resolution_accepted\"]:\n", "                old_is_accepted.append(1)\n", "            else:\n", "                old_is_accepted.append(0)\n", "\n", "        if (\n", "            result[\"resolution_accepted\"] != result[\"accepted_heuristic\"]\n", "            and result[\"resolution_accepted\"] is not None\n", "        ):\n", "            disagreements += 1\n", "            disagreements_stats[reasoning].append(result)\n", "        all_stats[reasoning].append(result)\n", "\n", "    if debug:\n", "        print(f\"Total completions: {len(model_request_ids)}\")\n", "        print(\n", "            f\"Old accepts: {sum(old_is_accepted)}, old rejects: {len(old_is_accepted) - sum(old_is_accepted)}\"\n", "        )\n", "        print(f\"Total Label changed: {disagreements}\")\n", "        print(\n", "            f\"New accepts: {sum(new_is_accepted)}, new rejects: {len(new_is_accepted) - sum(new_is_accepted)}\"\n", "        )\n", "\n", "        print()\n", "\n", "        for reasoning, results in all_stats.items():\n", "            print(\n", "                f\"{reasoning}: (total={len(results)}, labels_changed={len(disagreements_stats[reasoning])})\"\n", "            )\n", "        print()\n", "\n", "        for reasoning, results in disagreements_stats.items():\n", "            print(\"************----------------************\\n\")\n", "            print(f\"{reasoning}: labels_changed={len(results)}\")\n", "\n", "            to_print = 10\n", "            printed = 0\n", "            for i in range(len(results)):\n", "                # No reason to print completions where the LCS is 100% ()\n", "                # if \"percent_of_completion_matches\" in results[i] and results[i][\"percent_of_completion_matches\"] == 1:\n", "                #     continue\n", "                print_dict_multiline(\n", "                    results[i],\n", "                    keys=[\n", "                        \"original_request_id\",\n", "                        \"original_generated_text\",\n", "                        \"model_generated_text\",\n", "                        \"ground_truth\",\n", "                        \"resolution_accepted\",\n", "                        \"percent_of_completion_matches\",\n", "                        \"accepted_heuristic\",\n", "                        \"accepted_heuristic_reasoning\",\n", "                    ],\n", "                )\n", "                print(\"----------------\\n\")\n", "\n", "                printed += 1\n", "                if printed >= to_print:\n", "                    break\n", "\n", "    return old_is_accepted, new_is_accepted"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["model_request_ids, eldenv3_requests = get_model_request_ids()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["old_is_accepted, new_is_accepted = compute_accepted_heuristic(\n", "    model_request_ids,\n", "    use_model_generated_text=False,\n", "    remove_whitespace=True,\n", "    debug=True,\n", ")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def get_data_from_loc(loc: str = \"2024-06-15_2024-07-03_elden_20k\"):\n", "    train_data = f\"/mnt/efs/augment/data/prism/{loc}/train.jsonl\"\n", "    test_data = f\"/mnt/efs/augment/data/prism/{loc}/test.jsonl\"\n", "    train_datum: list[CompletionDatum] = train.load_completion_datum(train_data)\n", "    test_datum: list[CompletionDatum] = train.load_completion_datum(test_data)\n", "    print(f\"train: {len(train_datum)}\")\n", "    print(f\"test: {len(test_datum)}\")\n", "    return train_datum, test_datum\n", "\n", "\n", "train_data, test_data = get_data_from_loc(\n", "    \"2024-05-01_2024-06-30_eldenv3-15b_15k_original\"\n", ")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Combines the hindsight dataset with the completion datum that has no accepted labels as it is a replay\n", "# We can choose between using the heuristic label or the original resolution_accepted label\n", "USE_HEURISTIC = True  # use heuristic or resolution_accepted when creating the dataset\n", "\n", "label = \"accepted_heuristic\" if USE_HEURISTIC else \"resolution_accepted\"\n", "train_data_combined, test_data_combined = [], []\n", "for datum in train_data:\n", "    request_id = datum.request_id\n", "    info = model_request_ids[request_id]\n", "    accepted = info[label]\n", "    if accepted is not None:\n", "        datum.resolution = CompletionResolution(\n", "            accepted=accepted, timestamp=datum.response.timestamp\n", "        )\n", "        train_data_combined.append(datum)\n", "\n", "for datum in test_data:\n", "    request_id = datum.request_id\n", "    info = model_request_ids[request_id]\n", "    accepted = info[label]\n", "    if accepted is not None:\n", "        datum.resolution = CompletionResolution(\n", "            accepted=accepted, timestamp=datum.response.timestamp\n", "        )\n", "        test_data_combined.append(datum)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["na, nr, _, _ = num_accepts_rejects(train_data_combined)\n", "print(f\"Num accepts: {na}, num rejects: {nr}\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["OUTPUT_DIR = Path(canonicalize_path(\"data/prism/\"))\n", "output_dir = OUTPUT_DIR / \"2024-05-01_2024-06-30_all_models_15k_lcs100\"\n", "output_dir.mkdir(parents=True, exist_ok=False)\n", "output_dir"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def save_data(output_dir, train_data_combined, test_data_combined):\n", "    with (output_dir / \"train.jsonl\").open(\"w\") as f:\n", "        for datum in train_data_combined:\n", "            f.write(datum.to_json())  # type: ignore\n", "            f.write(\"\\n\")\n", "\n", "    with (output_dir / \"test.jsonl\").open(\"w\") as f:\n", "        for datum in test_data_combined:\n", "            f.write(datum.to_json())  # type: ignore\n", "            f.write(\"\\n\")\n", "\n", "\n", "save_data(output_dir, train_data_combined, test_data_combined)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze data - prefix, completion, ground truth, precision LCS\n", "\n", "Look at difference between model request IDs where the accepted label is computed from text data with and without white space.\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [], "source": ["model_request_ids_keep_ws, _ = get_model_request_ids(get_prefix=True)\n", "for idx, (model_request_id, info) in enumerate(model_request_ids_keep_ws.items()):\n", "    result, reasoning = resolution_accepted_heuristic(\n", "        info, use_model_generated_text=False, remove_whitespace=False\n", "    )\n", "\n", "na, nr, ha, hr = num_accepts_rejects(model_request_ids_keep_ws)\n", "print(\n", "    f\"Num accepts: {na}, num rejects: {nr}, heuristic accepts: {ha}, heuristic rejects: {hr}\"\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["model_request_ids_rem_ws, _ = get_model_request_ids(get_prefix=True)\n", "for idx, (model_request_id, info) in enumerate(model_request_ids_rem_ws.items()):\n", "    result, reasoning = resolution_accepted_heuristic(\n", "        info, use_model_generated_text=False, remove_whitespace=True\n", "    )\n", "\n", "na, nr, ha, hr = num_accepts_rejects(model_request_ids_rem_ws)\n", "print(\n", "    f\"Num accepts: {na}, num rejects: {nr}, heuristic accepts: {ha}, heuristic rejects: {hr}\"\n", ")"]}, {"cell_type": "code", "execution_count": 172, "metadata": {}, "outputs": [], "source": ["model_request_ids_keep_ws[\"fb869335-0404-45ce-a752-0a228d00a383\"]"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [], "source": ["model_request_ids_rem_ws[\"fb869335-0404-45ce-a752-0a228d00a383\"]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Get examples of accepted and rejected completions with various match scores\n", "MAX_EXAMPLES = 10\n", "\n", "\n", "def last_n_lines(text, n):\n", "    lines = text.splitlines(keepends=True)\n", "    max_lines = min(len(lines), n)\n", "    return \"\".join(lines[-max_lines:])\n", "\n", "\n", "curr_example = 0\n", "examples = defaultdict(list)\n", "\n", "for idx, (model_request_id, info) in enumerate(model_request_ids_rem_ws.items()):\n", "    prefix = info[\"prefix\"]\n", "    info[\"prefix_last_n_lines\"] = (\n", "        last_n_lines(prefix, 10) + \"<completion>??</completion>\"\n", "    )\n", "    completion = info[\"original_generated_text\"]\n", "    model_generated_text = info[\"model_generated_text\"]\n", "    ground_truth = info[\"ground_truth\"]\n", "    resolution_accepted = info[\"resolution_accepted\"]\n", "    accepted_heuristic = info[\"accepted_heuristic\"]\n", "    accepted_heuristic_reasoning = info[\"accepted_heuristic_reasoning\"]\n", "\n", "    if \"percent_of_completion_matches\" not in info or resolution_accepted is None:\n", "        continue\n", "\n", "    percent_of_completion_matches = info[\"percent_of_completion_matches\"]\n", "\n", "    idx = int(percent_of_completion_matches * 10)\n", "    key = f\"{idx}_{resolution_accepted}\"\n", "    if len(examples[key]) >= MAX_EXAMPLES:\n", "        continue\n", "    examples[key].append(info)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import csv\n", "\n", "with open(\n", "    \"/home/<USER>/augment/experimental/pranay/low_quality_completion_filter/output.csv\",\n", "    \"w\",\n", "    newline=\"\",\n", ") as csvfile:\n", "    writer = csv.writer(csvfile)\n", "    writer.writerow(\n", "        [\n", "            \"Key\",\n", "            \"Original Request ID\",\n", "            \"EldenV3 Request ID\",\n", "            \"Prefix\",\n", "            \"Original Generated Text\",\n", "            \"Model Generated Text\",\n", "            \"Ground Truth\",\n", "            \"Resolution Accepted\",\n", "            \"Accepted Heuristic (Original)\",\n", "            \"% of Completion Matches (Original)\",\n", "        ]\n", "    )  # header row\n", "    for i in range(11):\n", "        key_true = f\"{i}_True\"\n", "        key_false = f\"{i}_False\"\n", "        for example in examples[key_true]:\n", "            writer.writerow(\n", "                [\n", "                    key_true,\n", "                    example[\"original_request_id\"],\n", "                    example[\"model_request_id\"],\n", "                    example[\"prefix_last_n_lines\"],\n", "                    example[\"original_generated_text\"],\n", "                    example[\"model_generated_text\"],\n", "                    example[\"ground_truth\"],\n", "                    example[\"resolution_accepted\"],\n", "                    example[\"accepted_heuristic\"],\n", "                    example[\"percent_of_completion_matches\"],\n", "                ]\n", "            )\n", "        for example in examples[key_false]:\n", "            writer.writerow(\n", "                [\n", "                    key_false,\n", "                    example[\"original_request_id\"],\n", "                    example[\"model_request_id\"],\n", "                    example[\"prefix_last_n_lines\"],\n", "                    example[\"original_generated_text\"],\n", "                    example[\"model_generated_text\"],\n", "                    example[\"ground_truth\"],\n", "                    example[\"resolution_accepted\"],\n", "                    example[\"accepted_heuristic\"],\n", "                    example[\"percent_of_completion_matches\"],\n", "                ]\n", "            )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get completion data"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["def hindsight_to_completion():\n", "    from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "    from tqdm import tqdm\n", "\n", "    HINDSIGHT_BASE_DIR = Path(canonicalize_path(\"data/eval/hindsight/\"))\n", "\n", "    # Comes from https://www.notion.so/2024-07-10-Hindsight-1-0-Results-cdc10573512f4e4e95f9755cf80d3cc4,\n", "    # which is the hindsight data from May and June ran through eldenv3-15b.\n", "    DATASET_TO_MODEL_JOBS = {\n", "        \"2024-05-01-v1.0/dogfood\": \"7Vnn8paP\",\n", "        \"2024-06-01-v1.0/dogfood\": \"hs5qkbrK\",\n", "    }\n", "\n", "    completions = []\n", "    for dataset_name, job_id in DATASET_TO_MODEL_JOBS.items():\n", "        hindsight_dir = HINDSIGHT_BASE_DIR / dataset_name / \"data.jsonl.zst\"\n", "        hindsight_data = read_jsonl_zst(hindsight_dir)\n", "        for hindsight_row in tqdm(hindsight_data):\n", "            datum = HindsightCompletionDatum.from_dict(hindsight_row).completion\n", "            try:\n", "                _ = datum.resolution.accepted\n", "                completions.append(datum)\n", "            except Exception:\n", "                pass\n", "    return completions"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["def get_completions_data(requests: list[str]):\n", "    completion_filters = CompletionDataset.Filters(request_ids=requests)\n", "    completions = CompletionDataset.create(\n", "        tenant=tenants.get_tenant(TENANT_NAME),\n", "        filters=completion_filters,\n", "        page_size=8192,\n", "    )\n", "    completions = completions.get_completions()\n", "    completions = [transform_completion_datum(completion) for completion in completions]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Stats"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["cnt = 0\n", "for idx, (model_request_id, info) in enumerate(model_request_ids.items()):\n", "    if info[\"resolution_accepted\"] and not info[\"accepted_heuristic\"]:\n", "        print_dict_multiline(\n", "            info,\n", "            keys=[\n", "                \"original_generated_text\",\n", "                \"model_generated_text\",\n", "                \"ground_truth\",\n", "                \"percent_of_completion_matches\",\n", "            ],\n", "        )\n", "        print(\"----------------\\n\")\n", "        cnt += 1\n", "        if cnt >= 10:\n", "            break"]}, {"cell_type": "code", "execution_count": 223, "metadata": {}, "outputs": [], "source": ["conf_mat = confusion_matrix(old_is_accepted, new_is_accepted)\n", "disp = ConfusionMatrixDisplay(\n", "    confusion_matrix=conf_mat, display_labels=[\"rejected\", \"accepted\"]\n", ")\n", "disp.plot()\n", "disp.ax_.set_title(\"Confusion Matrix (Old vs New accepted labels)\")\n", "disp.ax_.set_ylabel(\"Old Acceptance Label\")\n", "disp.ax_.set_xlabel(\"New Acceptance Label\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 224, "metadata": {}, "outputs": [], "source": ["results = [\n", "    r\n", "    for r in model_request_ids.values()\n", "    if r[\"resolution_accepted\"] is not None and \"percent_of_completion_matches\" in r\n", "]\n", "print(len(results))\n", "\n", "# Create buckets of 0.05\n", "buckets = np.arange(0, 1.01, 0.05)\n", "\n", "# Initialize lists to store the results\n", "bucket_counts = [0] * len(buckets)\n", "bucket_accepted = [0] * len(buckets)\n", "\n", "# print(len(buckets))\n", "\n", "# Iterate over the results and count the number of accepted and total for each bucket\n", "for r in results:\n", "    bucket_idx = int(r[\"percent_of_completion_matches\"] / 0.05)\n", "    bucket_counts[bucket_idx] += 1\n", "    if r[\"resolution_accepted\"]:\n", "        bucket_accepted[bucket_idx] += 1\n", "\n", "# Calculate the percentage of accepted for each bucket\n", "bucket_percentages = [\n", "    a / c if c > 0 else 0 for a, c in zip(bucket_accepted, bucket_counts)\n", "]\n", "\n", "# Plot the results\n", "plt.plot(buckets, bucket_percentages)\n", "plt.xlabel(\"LCS Ratio: len(LCS)/len(generated_text)\")\n", "plt.ylabel(\"Percentage of Resolution Accepted\")\n", "plt.title(\"Resolution Accepted vs Longest Common Subsequence Generation Ratio\")\n", "plt.grid(axis=\"x\", alpha=0.75)\n", "plt.grid(axis=\"y\", alpha=0.75)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# Assuming you have your results list as before\n", "results = [\n", "    r\n", "    for r in model_request_ids.values()\n", "    if r[\"resolution_accepted\"] and \"percent_of_completion_matches\" in r\n", "]\n", "\n", "# Sort the results by percent_of_completion_matches\n", "sorted_results = sorted(results, key=lambda x: x[\"percent_of_completion_matches\"])\n", "\n", "# Calculate cumulative percentages\n", "total = len(sorted_results)\n", "cumulative_percentages = np.arange(1, total + 1) / total\n", "\n", "# Extract LCS ratios\n", "lcs_ratios = [r[\"percent_of_completion_matches\"] for r in sorted_results]\n", "\n", "# Create the cumulative line plot\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(lcs_ratios, cumulative_percentages, \"-\")\n", "\n", "plt.xlabel(\"LCS Ratio: len(LCS)/len(generated_text)\")\n", "plt.ylabel(\"Cumulative Percentage of Accepted Completions\")\n", "plt.title(\"Cumulative Distribution of LCS Ratios for Accepted Completions\")\n", "plt.grid(True, linestyle=\"--\", alpha=0.7)\n", "\n", "# Set y-axis to percentage format\n", "plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: \"{:.0%}\".format(y)))\n", "\n", "# Ensure the y-axis goes from 0 to 100%\n", "plt.ylim(0, 1)\n", "\n", "# Add a horizontal line at y=1 (100%)\n", "plt.axhline(y=1, color=\"r\", linestyle=\"--\", alpha=0.5)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [], "source": ["og_request_ids = [\n", "    \"027369bb-78c5-4e95-bc1c-1d4427b82df5\",\n", "    \"0392c034-c699-4d3d-98eb-f4a996c9f66a\",\n", "    \"03b95755-cb07-43d0-80ef-6fb4564d45da\",\n", "    \"04da7865-9656-41d2-b529-5047c5109c37\",\n", "    \"062eff88-f051-4f87-aee0-ca6981d5f862\",\n", "    \"192cc3cf-45f9-48eb-9a16-395619786758\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results\n", "\n", "### Insight 1\n", "Out of 15000 completions, 7732 completions are a prefix of the ground truth, but 2919 of these had the rejected label.\n", "\n", "completion is prefix of ground truth: (total=7732, labels_changed=2919).\n", "\n", "\n", "\n", "### Insight 2\n", "When using a heuristic based on the Longest Common Subsequence, we get that 9274 completions are accepted, and 5217 are rejected. Of that, 3728/9274 had the rejected label but were accepted by the heuristic. Additionally, 989/5217 had the accepted label but were rejected by the heuristic. We accept the completion if and only if at least 80% of the generated text is in the LCS: `len(LCS) / len(generated_text) > 0.8`. Note that we limit the ground truth to the first n characters of the generated text, where n is the length of the generated text + padding (10 characters, or 1.5 times the length of the generated text)\n", "\n", "most of generated text is in the ground truth: (total=9274, labels_changed=3728)\n", "most of generated text is not in the ground truth: (total=5217, labels_changed=989)\n", "\n", "\n", "### Insight 3\n", "Roughly half of the rejected completions are mislabeled as rejected, and 1/6 of the accepted completions are mislabeled as accepted. Essentially, a user is much more likely to reject a completion instead of accept it (could be due to completion resolution bug as well).\n", "\n", "### Insight 4\n", "This LCS ratio seems like a good metric of accepted/rejected from the graph. The cutoff I am not too sure about as it is currently set to 0.8.\n", "\n", "### Insight 5\n", "There is a list of request ids that are interesting to look at above\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
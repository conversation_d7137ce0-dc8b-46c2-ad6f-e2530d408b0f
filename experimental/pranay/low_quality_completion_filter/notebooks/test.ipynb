{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets import completion\n", "from base.datasets.completion import CompletionDatum\n", "from base.datasets import tenants\n", "from services.request_insight import request_insight_pb2\n", "\n", "TENANT_DOGFOOD = \"dogfood\"\n", "TENANT_DOGFOOD_SHARD = \"dogfood-shard\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from base.datasets.gcp_creds import get_gcp_creds\n", "from google.cloud import bigquery\n", "import os\n", "from google.cloud import storage\n", "from google.oauth2 import service_account\n", "from google.protobuf.json_format import MessageToDict\n", "from typing import List, Dict, Any\n", "\n", "dogfood_project_id = tenants.get_tenant(TENANT_DOGFOOD_SHARD).project_id\n", "dogfood_shard_project_id = tenants.get_tenant(TENANT_DOGFOOD_SHARD).project_id\n", "assert dogfood_project_id == dogfood_shard_project_id\n", "project_id = dogfood_project_id\n", "\n", "gcp_creds, _ = get_gcp_creds()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["class GCPRequestInsightFetcher:\n", "    def __init__(self, credentials):\n", "        self.client = storage.Client(\n", "            credentials=credentials, project=\"system-services-prod\"\n", "        )\n", "        self.bucket = self.client.bucket(\n", "            \"us-staging-request-insight-events-confidential\"\n", "        )\n", "\n", "    def fetch_request_events(\n", "        self, tenant_id: str, request_id: str, event_type: str\n", "    ) -> List[Dict[str, Any]]:\n", "        prefix = f\"{tenant_id}/request/{request_id}/\"\n", "\n", "        if event_type:\n", "            prefix += f\"{event_type}/\"\n", "\n", "        blobs = self.bucket.list_blobs(prefix=prefix)\n", "        events = []\n", "\n", "        for blob in blobs:\n", "            event_data = blob.download_as_bytes()\n", "            request_event = request_insight_pb2.RequestEvent()\n", "            request_event.ParseFromString(event_data)\n", "            events.append(MessageToDict(request_event))\n", "\n", "        return events"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["gcp_fetcher = GCPRequestInsightFetcher(gcp_creds)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'time': '2024-09-23T12:50:16.734760Z',\n", "  'completionHostRequest': {'eosTokenId': [0, 49154],\n", "   'outputLen': 4,\n", "   'model': 'eldenv4-15b',\n", "   'prefix': 'def test_health_check():\\n    ',\n", "   'path': 'augment/health_check/test.py',\n", "   'blobs': {'added': ['UY05XvcdCCDF1okOX+sn1U3R6JK+0pK7bAtB+zrX5Cs=']},\n", "   'recencyInfo': {},\n", "   'tokenization': {'tokenIds': [6,\n", "     34650,\n", "     52,\n", "     9864,\n", "     100,\n", "     1571,\n", "     52,\n", "     881,\n", "     51,\n", "     997,\n", "     222,\n", "     49168,\n", "     21,\n", "     49161,\n", "     222,\n", "     402,\n", "     822,\n", "     63,\n", "     28410,\n", "     52,\n", "     9864,\n", "     100,\n", "     1571,\n", "     52,\n", "     881,\n", "     51,\n", "     997,\n", "     222,\n", "     9840,\n", "     63,\n", "     913,\n", "     100,\n", "     9864,\n", "     100,\n", "     1571,\n", "     222,\n", "     49162,\n", "     1,\n", "     610,\n", "     913,\n", "     100,\n", "     9864,\n", "     100,\n", "     1571,\n", "     2284,\n", "     294,\n", "     3,\n", "     2],\n", "    'offsets': [0,\n", "     10,\n", "     17,\n", "     18,\n", "     24,\n", "     25,\n", "     30,\n", "     31,\n", "     35,\n", "     36,\n", "     38,\n", "     39,\n", "     53,\n", "     62,\n", "     75,\n", "     76,\n", "     78,\n", "     83,\n", "     84,\n", "     92,\n", "     93,\n", "     99,\n", "     100,\n", "     105,\n", "     106,\n", "     110,\n", "     111,\n", "     113,\n", "     114,\n", "     122,\n", "     123,\n", "     128,\n", "     129,\n", "     135,\n", "     136,\n", "     141,\n", "     142,\n", "     153,\n", "     165,\n", "     168,\n", "     173,\n", "     174,\n", "     180,\n", "     181,\n", "     186,\n", "     189,\n", "     194,\n", "     206],\n", "    'text': '<file_sep>augment/health_check/test.py\\n<|far_prefix|><pr_base><|sig-begin|>\\nIn file: augment/health_check/test.py\\nFUNCTION: test_health_check\\n<|sig-end|><fim_prefix>def test_health_check():\\n    <fim_suffix><fim_middle>'}},\n", "  'tenantInfo': {'tenantId': '07e5a0d66265b8624c14b819fb3598c0',\n", "   'tenantName': 'staging-shard-0'},\n", "  'eventId': '1eae58fa-108c-4d3a-8670-9e0f4917b2d6'}]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["tenant_id = \"07e5a0d66265b8624c14b819fb3598c0\"\n", "request_id = \"********-1207-4256-8eb2-ebcba0e821a9\"\n", "event_type = \"completion_host_request\"\n", "\n", "gcp_fetcher.fetch_request_events(tenant_id, request_id, event_type)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.cloud import storage\n", "# from google.oauth2 import service_account\n", "# from google.protobuf.json_format import MessageToDict\n", "# from typing import List, Dict, Any\n", "\n", "# # Import the proto definition (you'll need to generate this from your .proto file)\n", "# # from your_proto_package import RequestEvent\n", "\n", "# class GCPRequestInsightFetcher:\n", "#     def __init__(self, credentials_path: str):\n", "#         self.credentials = service_account.Credentials.from_service_account_file(\n", "#             credentials_path,\n", "#             scopes=[\"https://www.googleapis.com/auth/cloud-platform\"]\n", "#         )\n", "#         self.client = storage.Client(credentials=self.credentials, project=\"system-services-prod\")\n", "#         self.bucket = self.client.bucket(\"us-staging-request-insight-events-confidential\")\n", "\n", "#     def fetch_request_events(self, tenant_id: str, request_id: str, event_type: str) -> List[Dict[str, Any]]:\n", "#         prefix = f\"{tenant_id}/request/{request_id}/\"\n", "\n", "#         if event_type:\n", "#             prefix += f\"{event_type}/\"\n", "\n", "#         blobs = self.bucket.list_blobs(prefix=prefix)\n", "#         events = []\n", "\n", "#         for blob in blobs:\n", "#             event_data = blob.download_as_bytes()\n", "#             event = RequestEvent()\n", "#             event.ParseFromString(event_data)\n", "#             events.append(MessageToDict(event))\n", "\n", "#         return events\n", "\n", "# def main():\n", "#     # Set your credentials file path\n", "#     credentials_path = \"/path/to/your/service-account-key.json\"\n", "\n", "#     # Create the fetcher\n", "#     fetcher = GCPRequestInsightFetcher(credentials_path)\n", "\n", "#     # Example usage\n", "#     tenant_id = \"example_tenant\"\n", "#     request_id = \"example_request_id\"\n", "#     event_type = \"chat_host_request\"  # Optional\n", "\n", "#     events = fetcher.fetch_request_events(tenant_id, request_id, event_type)\n", "\n", "#     for event in events:\n", "#         print(event)\n", "\n", "# if __name__ == \"__main__\":\n", "#     main()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import xgboost as xgb\n", "from xgboost import plot_tree\n", "from base.datasets.completion import CompletionDatum\n", "from research.prism import train\n", "from research.prism.train import create_features, datum_to_dmatrix\n", "import base.tokenizers\n", "import csv\n", "from pathlib import Path\n", "from sklearn.metrics import roc_auc_score, roc_curve, auc\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "from research.core.data_paths import canonicalize_path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "\n", "\n", "tokenizer = base.tokenizers.create_tokenizer_by_name(\"starcoder2\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Models\n", "\n", "We have a model trained on a general dataset from completions, and another model trained on data from hindsight. The general dataset does not have heuristic based labels and the hindsight dataset does. \n", "\n", "We want to check if the hindsight model is better than the general model. We shown that the AUCROC when trained and evaluated on the hindsight dataset is higher than the AUCROC when trained and evaluated on the general dataset (.88 vs .70). Additionally, we perform only slightly worse when training on the hindsight data and evaluating on the general dataset. We now want to test the filter score directly on examples from the general dataset:\n", "\n", "1. Retrieve dataset from a general dataset\n", "2. Load two different models: general, hindsight\n", "3. Compute filter score for both\n", "4. Look at examples where filter score is wildly different"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_hindsight_and_model_data(\n", "    job_row, hindsight_row, missing_info=defaultdict(int), get_prefix: bool = False\n", "):\n", "    assert job_row[\"request_id\"] == hindsight_row[\"completion\"][\"request_id\"]\n", "    assert job_row[\"ground_truth\"] == hindsight_row[\"ground_truth\"]\n", "\n", "    model_request_id = job_row[\"artifacts\"][0][\"request_ids\"][-1]\n", "    model_generated_text = job_row[\"generation\"]\n", "\n", "    original_request_id = hindsight_row[\"completion\"][\"request_id\"]\n", "    original_model = hindsight_row[\"completion\"][\"response\"][\"model\"]\n", "    original_generated_text = hindsight_row[\"completion\"][\"response\"][\"text\"]\n", "\n", "    ground_truth = hindsight_row[\"ground_truth\"]\n", "    try:\n", "        resolution_accepted = hindsight_row[\"completion\"][\"resolution\"][\"accepted\"]\n", "    except Exception:\n", "        missing_info[\"missing_resolution\"] += 1\n", "        return None, None\n", "\n", "    info = {\n", "        \"original_request_id\": original_request_id,\n", "        \"model_request_id\": model_request_id,\n", "        \"original_generated_text\": original_generated_text,\n", "        \"model_generated_text\": model_generated_text,\n", "        \"ground_truth\": ground_truth,\n", "        \"original_model\": original_model,\n", "        \"resolution_accepted\": resolution_accepted,\n", "    }\n", "\n", "    if get_prefix:\n", "        prefix = hindsight_row[\"completion\"][\"request\"][\"prefix\"]\n", "        info[\"prefix\"] = prefix\n", "        if not prefix:\n", "            missing_info[\"missing_prefix\"] += 1\n", "            return None, None\n", "\n", "    assert model_request_id\n", "    assert model_generated_text is not None\n", "    assert original_request_id\n", "    assert original_model\n", "    assert original_generated_text is not None\n", "    assert ground_truth\n", "\n", "    return model_request_id, info\n", "\n", "\n", "def get_model_request_ids(get_prefix: bool = False):\n", "    HINDSIGHT_BASE_DIR = Path(canonicalize_path(\"data/eval/hindsight/\"))\n", "    MODEL_BASE_DIR = Path(canonicalize_path(\"eval/jobs/\"))\n", "\n", "    # Comes from https://www.notion.so/2024-07-10-Hindsight-1-0-Results-cdc10573512f4e4e95f9755cf80d3cc4,\n", "    # which is the hindsight data from May and June ran through eldenv3-15b.\n", "    DATASET_TO_MODEL_JOBS = {\n", "        \"2024-05-01-v1.0/dogfood\": \"7Vnn8paP\",\n", "        \"2024-06-01-v1.0/dogfood\": \"hs5qkbrK\",\n", "    }\n", "\n", "    model_request_ids = {}\n", "    missing_info = defaultdict(int)\n", "\n", "    for dataset_name, job_id in DATASET_TO_MODEL_JOBS.items():\n", "        job_dir = (\n", "            MODEL_BASE_DIR\n", "            / job_id\n", "            / \"000_null_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "        )\n", "        hindsight_dir = HINDSIGHT_BASE_DIR / dataset_name / \"data.jsonl.zst\"\n", "        if not job_dir.exists() or not hindsight_dir.exists():\n", "            raise ValueError(f\"Missing {job_dir} or {hindsight_dir}\")\n", "        job_data = read_jsonl_zst(job_dir)\n", "        hindsight_data = read_jsonl_zst(hindsight_dir)\n", "        for job_row, hindsight_row in zip(job_data, hindsight_data):\n", "            model_request_id, info = get_hindsight_and_model_data(\n", "                job_row, hindsight_row, missing_info, get_prefix=get_prefix\n", "            )\n", "            if info:\n", "                if model_request_id in model_request_ids:\n", "                    raise ValueError(f\"Duplicate model request id {model_request_id}\")\n", "                model_request_ids[model_request_id] = info\n", "\n", "    eldenv3_requests = list(model_request_ids.keys())\n", "\n", "    print(f\"{dict(missing_info)}\")\n", "    print(f\"{len(model_request_ids)} model request ids\")\n", "\n", "    return model_request_ids, eldenv3_requests\n", "\n", "\n", "model_request_ids, _ = get_model_request_ids()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def get_data_from_loc(loc: str = \"2024-06-15_2024-07-03_elden_20k\", verbose=True):\n", "    train_data = f\"/mnt/efs/augment/data/prism/{loc}/train.jsonl\"\n", "    test_data = f\"/mnt/efs/augment/data/prism/{loc}/test.jsonl\"\n", "    train_datum: list[CompletionDatum] = train.load_completion_datum(train_data)\n", "    test_datum: list[CompletionDatum] = train.load_completion_datum(test_data)\n", "    if verbose:\n", "        print(f\"train: {len(train_datum)}, test: {len(test_datum)}\")\n", "    return train_datum, test_datum\n", "\n", "\n", "def get_predict_score(\n", "    model: xgb.<PERSON><PERSON>,\n", "    datums: list[CompletionDatum],\n", "    tokenizer: base.tokenizers.Tokenizer,\n", "    features_to_use=[],\n", "):\n", "    \"\"\"Returns the predict score for a list of datums.\"\"\"\n", "    dmatrix = datum_to_dmatrix(datums, tokenizer, features_to_use)\n", "    return dmatrix.get_label(), model.predict(dmatrix)\n", "\n", "\n", "def last_n_lines(text, n):\n", "    lines = text.splitlines(keepends=True)\n", "    max_lines = min(len(lines), n)\n", "    return \"\".join(lines[-max_lines:])\n", "\n", "\n", "def del_extension(filename):\n", "    return filename[: filename.rfind(\".\")]\n", "\n", "\n", "base_ckpt_path = Path(\n", "    \"/home/<USER>/augment/services/completion_host/single_model_server/prism_models/\"\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def plot_roc_curve(\n", "    fpr1,\n", "    tpr1,\n", "    thresholds1,\n", "    num_examples1,\n", "    fpr2,\n", "    tpr2,\n", "    thresholds2,\n", "    num_examples2,\n", "    title,\n", "    filename,\n", "):\n", "    plt.figure(figsize=(10, 8))\n", "\n", "    # Plot first ROC curve\n", "    plt.plot(\n", "        fpr1,\n", "        tpr1,\n", "        color=\"green\",\n", "        lw=2,\n", "        label=f\"ROC curve Original Model (area = {auc(fpr1, tpr1):.2f}, N = {num_examples1})\",\n", "    )\n", "\n", "    # Plot second ROC curve\n", "    plt.plot(\n", "        fpr2,\n", "        tpr2,\n", "        color=\"red\",\n", "        lw=2,\n", "        label=f\"ROC curve Hindsight Model (area = {auc(fpr2, tpr2):.2f}, N = {num_examples2})\",\n", "    )\n", "\n", "    plt.plot([0, 1], [0, 1], color=\"navy\", lw=2, linestyle=\"--\")\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel(\"False Positive Rate\")\n", "    plt.ylabel(\"True Positive Rate\")\n", "    plt.title(f\"ROC: {title}\")\n", "    plt.legend(loc=\"lower right\")\n", "    plt.grid(True, linestyle=\"--\", alpha=0.7)\n", "\n", "    key_thresholds = [0.9, 0.8, 0.7, 0.6, 0.5]\n", "\n", "    # Annotate key thresholds for first curve\n", "    for key_threshold in key_thresholds:\n", "        idx = np.argmin(np.abs(thresholds1 - key_threshold))\n", "        plt.annotate(\n", "            f\"({fpr1[idx]:.2f}, {tpr1[idx]:.2f}, {thresholds1[idx]:.2f})\",\n", "            (fpr1[idx], tpr1[idx]),\n", "            textcoords=\"offset points\",\n", "            xytext=(50, 10),\n", "            ha=\"center\",\n", "            color=\"green\",\n", "        )\n", "        plt.plot(fpr1[idx], tpr1[idx], \"go\")\n", "\n", "    # Annotate key thresholds for second curve\n", "    for key_threshold in key_thresholds:\n", "        idx = np.argmin(np.abs(thresholds2 - key_threshold))\n", "        plt.annotate(\n", "            f\"({fpr2[idx]:.2f}, {tpr2[idx]:.2f}, {thresholds2[idx]:.2f})\",\n", "            (fpr2[idx], tpr2[idx]),\n", "            textcoords=\"offset points\",\n", "            xytext=(-50, -10),\n", "            ha=\"center\",\n", "            color=\"red\",\n", "        )\n", "        plt.plot(fpr2[idx], tpr2[idx], \"ro\")\n", "\n", "    path = \"/home/<USER>/augment/services/completion_host/single_model_server/prism_models\"\n", "    full_path = f\"{path}/{filename}.png\"\n", "    plt.savefig(full_path)\n", "    print(f\"Saved plot to {full_path}\")\n", "    plt.close()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class CompareModels:\n", "    def __init__(\n", "        self,\n", "        test_data_location,\n", "        general_model_ckpt_path,\n", "        hindsight_model_ckpt_path,\n", "        title,\n", "        filename,\n", "        request_id_to_ground_truth=None,\n", "    ):\n", "        self.test_data_location = test_data_location\n", "        self.general_model_ckpt_path = general_model_ckpt_path\n", "        self.hindsight_model_ckpt_path = hindsight_model_ckpt_path\n", "        self.title = title\n", "        self.filename = filename\n", "\n", "        _, test_data = get_data_from_loc(test_data_location, verbose=False)\n", "\n", "        self.test_data = []\n", "        self.ground_truth = []\n", "        for datum in test_data:\n", "            if datum.resolution and datum.resolution.accepted is not None:\n", "                if (\n", "                    datum.user_agent is None\n", "                    or \"vscode\" in datum.user_agent\n", "                    or \"Eval\" in datum.user_agent\n", "                ):\n", "                    self.test_data.append(datum)\n", "                    if request_id_to_ground_truth:\n", "                        self.ground_truth.append(\n", "                            request_id_to_ground_truth[datum.request_id][\"ground_truth\"]\n", "                        )\n", "\n", "        print(\n", "            f\"Test data Label -- Accepted/Total: {sum(d.resolution.accepted for d in self.test_data)}/{len(self.test_data)}\"\n", "        )\n", "\n", "        self.general_model = xgb.<PERSON><PERSON>(\n", "            model_file=base_ckpt_path / general_model_ckpt_path\n", "        )\n", "        self.hindsight_model = xgb.<PERSON><PERSON><PERSON>(\n", "            model_file=base_ckpt_path / hindsight_model_ckpt_path\n", "        )\n", "\n", "        data_labels1, self.scores_on_general_data = get_predict_score(\n", "            self.general_model,\n", "            self.test_data,\n", "            tokenizer,\n", "            self.general_model.feature_names,\n", "        )\n", "        data_labels2, self.scores_on_hindsight_data = get_predict_score(\n", "            self.hindsight_model,\n", "            self.test_data,\n", "            tokenizer,\n", "            self.hindsight_model.feature_names,\n", "        )\n", "        self.data_labels = data_labels1\n", "\n", "        self.mse_general_model = np.mean(\n", "            (self.scores_on_general_data - self.data_labels) ** 2, axis=0\n", "        )\n", "        self.mse_hindsight_model = np.mean(\n", "            (self.scores_on_hindsight_data - self.data_labels) ** 2, axis=0\n", "        )\n", "\n", "        self.results = self.generate_results()\n", "\n", "    def generate_results(self):\n", "        results = {}\n", "        stats = {\n", "            \"hindsight_better_on_accepted\": 0,\n", "            \"hindsight_worse_on_accepted\": 0,\n", "            \"hindsight_better_on_rejected\": 0,\n", "            \"hindsight_worse_on_rejected\": 0,\n", "        }\n", "        for idx, (td, general_score, hindsight_score) in enumerate(\n", "            zip(\n", "                self.test_data,\n", "                self.scores_on_general_data,\n", "                self.scores_on_hindsight_data,\n", "            )\n", "        ):\n", "            difference = general_score - hindsight_score\n", "            abs_difference = abs(difference)\n", "            # if accepted and lower +1 or not accepted and higher +1\n", "            is_accepted = td.resolution.accepted\n", "\n", "            if is_accepted is None:\n", "                continue\n", "            elif is_accepted:\n", "                if hindsight_score < general_score:\n", "                    stats[\"hindsight_better_on_accepted\"] += 1\n", "                else:\n", "                    stats[\"hindsight_worse_on_accepted\"] += 1\n", "            elif not is_accepted:\n", "                if hindsight_score > general_score:\n", "                    stats[\"hindsight_better_on_rejected\"] += 1\n", "                else:\n", "                    stats[\"hindsight_worse_on_rejected\"] += 1\n", "                    # print(f\"{difference=}\\n{self.ground_truth[idx]=}\\n{td.response.text=}\\n\\n\")\n", "\n", "            if abs_difference >= 0.05:\n", "                results[idx] = {\n", "                    \"general_score\": general_score,\n", "                    \"hindsight_score\": hindsight_score,\n", "                    \"datum\": td,\n", "                    \"hindsight_more_reject\": difference < 0,\n", "                    \"difference_abs\": abs_difference,\n", "                }\n", "                if self.ground_truth:\n", "                    results[idx][\"ground_truth\"] = self.ground_truth[idx]\n", "\n", "        # sort based on resolution accepted and then general score\n", "        # results = {k: v for k, v in sorted(results.items(), key=lambda item: (item[1][\"datum\"].resolution.accepted, item[1][\"general_score\"]), reverse=True)}\n", "\n", "        # sort based on difference diretion and then abs difference\n", "        results = {\n", "            k: v\n", "            for k, v in sorted(\n", "                results.items(),\n", "                key=lambda item: (\n", "                    item[1][\"hindsight_more_reject\"],\n", "                    item[1][\"difference_abs\"],\n", "                ),\n", "                reverse=True,\n", "            )\n", "        }\n", "        self.stats = stats\n", "        return results\n", "\n", "    def save_plot(self):\n", "        fpr_general, tpr_general, thresholds_general = roc_curve(\n", "            self.data_labels, self.scores_on_general_data\n", "        )\n", "        fpr_hindsight, tpr_hindsight, thresholds_hindsight = roc_curve(\n", "            self.data_labels, self.scores_on_hindsight_data\n", "        )\n", "        plot_roc_curve(\n", "            fpr_general,\n", "            tpr_general,\n", "            thresholds_general,\n", "            len(self.test_data),\n", "            fpr_hindsight,\n", "            tpr_hindsight,\n", "            thresholds_hindsight,\n", "            len(self.test_data),\n", "            self.title,\n", "            self.filename,\n", "        )\n", "\n", "    def save_csv(self):\n", "        general_model_name = del_extension(self.general_model_ckpt_path)\n", "        hindsight_model_name = del_extension(self.hindsight_model_ckpt_path)\n", "        results_path = f\"/home/<USER>/augment/services/completion_host/single_model_server/prism_models/{self.filename}.csv\"\n", "        with open(results_path, \"w\", newline=\"\") as csvfile:\n", "            writer = csv.writer(csvfile)\n", "            writer.writerow(\n", "                [\n", "                    \"Request ID\",\n", "                    \"Prefix\",\n", "                    \"Model Generated Text\",\n", "                    \"Resolution Accepted\",\n", "                    f\"{general_model_name} Score\",\n", "                    f\"{hindsight_model_name} Score\",\n", "                ]\n", "                + ([\"Ground Truth\"] if self.ground_truth else [])\n", "            )  # header row\n", "            for idx, result in self.results.items():\n", "                datum = result[\"datum\"]\n", "                short_prefix = (\n", "                    last_n_lines(datum.request.prefix, 10)\n", "                    + \"<completion>??</completion>\"\n", "                )\n", "                writer.writerow(\n", "                    [\n", "                        datum.request_id,\n", "                        short_prefix,\n", "                        datum.response.text,\n", "                        datum.resolution.accepted,\n", "                        result[\"general_score\"],\n", "                        result[\"hindsight_score\"],\n", "                    ]\n", "                    + ([result[\"ground_truth\"]] if self.ground_truth else [])\n", "                )"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["original_trained_ckpt_path = \"prism_eldenv3.json\"\n", "general_trained_ckpt_path = \"prism_eldenv3_general.json\"\n", "hindsight_trained_ckpt_path = \"prism_eldenv3_lcs80.json\"\n", "hindsight_trained_ckpt_path_100 = \"prism_eldenv3_lcs100.json\""]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["original_on_general_data = CompareModels(\n", "    \"2024-07-03_2024-07-11_elden_20k\",\n", "    original_trained_ckpt_path,\n", "    hindsight_trained_ckpt_path,\n", "    \"Original Model vs Hindsight Model on General Data\",\n", "    \"original_model_vs_hindsight_model_on_general_data\",\n", ")\n", "original_on_general_data.save_plot()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["original_on_hindsight_data = CompareModels(\n", "    \"2024-05-01_2024-06-30_eldenv3-15b_15k_lcs80\",\n", "    original_trained_ckpt_path,\n", "    hindsight_trained_ckpt_path,\n", "    \"Original Model vs Hindsight Model on Hindsight Data\",\n", "    \"original_model_vs_hindsight_model_on_hindsight_data\",\n", ")\n", "original_on_hindsight_data.save_plot()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["models_eval_on_general_data = CompareModels(\n", "    \"2024-07-03_2024-07-11_elden_20k\",\n", "    general_trained_ckpt_path,\n", "    hindsight_trained_ckpt_path,\n", "    \"General Model vs Hindsight Model on General Data\",\n", "    \"general_model_vs_hindsight_model_on_general_data\",\n", ")\n", "\n", "# models_eval_on_general_data = CompareModels(\n", "#     \"2024-07-03_2024-07-11_elden_20k\", general_trained_ckpt_path, hindsight_trained_ckpt_path_100,\n", "#     \"General Model vs Hindsight Model (LCS=100) on Hindsight Data\", \"general_model_vs_hindsight_model_lcs100_on_hindsight_data\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["models_eval_on_hindsight_data = CompareModels(\n", "    \"2024-05-01_2024-06-30_eldenv3-15b_15k_lcs80\",\n", "    general_trained_ckpt_path,\n", "    hindsight_trained_ckpt_path,\n", "    \"General Model vs Hindsight Model on Hindsight Data\",\n", "    \"general_model_vs_hindsight_model_on_hindsight_data\",\n", "    model_request_ids,\n", ")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["models_eval_on_hindsight_data_lcs100 = CompareModels(\n", "    \"2024-05-01_2024-06-30_all_models_15k_lcs100\",\n", "    general_trained_ckpt_path,\n", "    hindsight_trained_ckpt_path_100,\n", "    \"General Model vs Hindsight Model on Hindsight Data LCS100\",\n", "    \"general_model_vs_hindsight_model_on_hindsight_data_lcs100\",\n", "    model_request_ids,\n", ")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["# When comparing the hindsight model to the general model, see for accepted and rejected labels if the hindsight model pushes scores higher or lower.\n", "\n", "print(\"On general data\", models_eval_on_general_data.stats)\n", "\n", "print(\"On hindsight data\", models_eval_on_hindsight_data.stats)\n", "\n", "print(\n", "    \"On hindsight data LCS100\", models_eval_on_hindsight_data_lcs100.stats\n", ")  # note if LCS!=100, then we take resolution.accepted label"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["models_eval_on_general_data.save_plot()\n", "models_eval_on_hindsight_data.save_plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["models_eval_on_general_data.save_csv()\n", "models_eval_on_hindsight_data.save_csv()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"General Model on general data MSE: {models_eval_on_general_data.mse_general_model:.5f}\"\n", ")\n", "print(\n", "    f\"Hindsight Model on general data MSE: {models_eval_on_general_data.mse_hindsight_model:.5f}\"\n", ")\n", "print()\n", "print(\n", "    f\"General Model on hindsight data MSE: {models_eval_on_hindsight_data.mse_general_model:.5f}\"\n", ")\n", "print(\n", "    f\"Hindsight Model on hindsight data MSE: {models_eval_on_hindsight_data.mse_hindsight_model:.5f}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n", "Base directory: /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets import tenants, completion\n", "from base.datasets.completion import CompletionDatum\n", "import base.tokenizers\n", "\n", "from research.prism.run_data_generation import main as run_data_generation\n", "from research.prism.run_data_generation import (\n", "    get_train_eval_data,\n", "    get_train_eval_data_helper,\n", "    create_output_dir,\n", "    transform_completion_datum,\n", ")\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from datetime import datetime\n", "from research.prism import train\n", "from research.prism.train import main as train_main\n", "from research.prism.train import create_features\n", "from collections import defaultdict\n", "import matplotlib.pyplot as plt\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    extract_completion_filter_features,\n", ")\n", "import random\n", "\n", "from pathlib import Path\n", "import pandas as pd\n", "from research.core.data_paths import canonicalize_path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "\n", "SEED = 42\n", "random.seed(SEED)\n", "\n", "TENANT_NAME = \"dogfood\"\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "tokenizer = base.tokenizers.create_tokenizer_by_name(\"starcoder2\")\n", "\n", "BASE_DIR = (\n", "    Path(canonicalize_path(\"data/prism/\"))\n", "    / f\"{datetime(2024, 7, 1).date()}_{datetime(2024, 8, 30).date()}_elden\"\n", ")\n", "print(f\"Base directory: {BASE_DIR}\")\n", "# output_dir.mkdir(parents=True, exist_ok=False)\n", "\n", "ckpt_path = \"/home/<USER>/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\""]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["from IPython.core.interactiveshell import InteractiveShell\n", "\n", "InteractiveShell.ast_node_interactivity = \"all\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Elden data from jsonl.zst files and extract features for xgboost"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden/data_dogfood.jsonl.zst\n", "Found 44988 completions in data_dogfood.\n", "Reading /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden/data_dogfood_shard_part1.jsonl.zst\n", "Found 46546 completions in data_dogfood_shard_part1.\n", "Reading /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden/data_dogfood_shard_part2.jsonl.zst\n", "Found 46545 completions in data_dogfood_shard_part2.\n", "Reading /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden/data_dogfood_shard_part3.jsonl.zst\n", "Found 43674 completions in data_dogfood_shard_part3.\n", "Reading /mnt/efs/augment/data/prism/2024-07-01_2024-08-30_elden/data_dogfood_shard_part4.jsonl.zst\n", "Found 53411 completions in data_dogfood_shard_part4.\n", "Found 235162 elden completions.\n"]}], "source": ["# the .jsonl.zst files to read from\n", "files = [\n", "    \"data_dogfood\",\n", "    \"data_dogfood_shard_part1\",\n", "    \"data_dogfood_shard_part2\",\n", "    \"data_dogfood_shard_part3\",\n", "    \"data_dogfood_shard_part4\",\n", "]\n", "\n", "elden_json_data = []\n", "for file in files:\n", "    json_file = f\"{BASE_DIR / file}.jsonl.zst\"\n", "    print(f\"Reading {json_file}\")\n", "    json_data = read_jsonl_zst(Path(json_file))\n", "    print(f\"Found {len(json_data)} completions in {file}.\")\n", "    elden_json_data.extend(json_data)\n", "\n", "elden_json_data = [d for d in elden_json_data if d[\"resolution\"] is not None]\n", "assert all([\"elden\" in d[\"response\"][\"model\"] for d in elden_json_data])\n", "assert len(elden_json_data) == len(set([d[\"request_id\"] for d in elden_json_data]))\n", "\n", "for data in elden_json_data:\n", "    data[\"request\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"request\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data[\"response\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"response\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data[\"resolution\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"resolution\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "elden_json_data = sorted(elden_json_data, key=lambda x: x[\"request\"][\"timestamp\"])\n", "\n", "print(f\"Found {len(elden_json_data)} elden completions.\")"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["class XGBoostFeatureDataSet:\n", "    def __init__(self, data: list[dict]):\n", "        self.data = data\n", "        self.features = []\n", "        self.metadata = []\n", "        for d in self.data:\n", "            features, metadata = self.extract_features(d)\n", "            self.features.append(features)\n", "            self.metadata.append(metadata)\n", "        self.feature_names = list(self.features[0].keys())\n", "\n", "        # Combine original data and extracted features\n", "        combined_data = []\n", "        for original, features, metadata in zip(\n", "            self.data, self.features, self.metadata\n", "        ):\n", "            combined_row = original.copy()  # Start with the original data\n", "            combined_row.update(features)  # Add the extracted features\n", "            combined_row[\"metadata\"] = metadata\n", "            combined_data.append(combined_row)\n", "\n", "        # Create DataFrame with combined data\n", "        self.df = pd.DataFrame(combined_data)\n", "\n", "    def extract_features(self, c: dict):\n", "        if type(c) == dict:\n", "            return extract_completion_filter_features(\n", "                c[\"response\"][\"token_ids\"],\n", "                c[\"response\"][\"token_log_probs\"],\n", "                tokenizer,\n", "                c[\"request\"][\"prefix\"],\n", "                c[\"request\"][\"path\"],\n", "                return_metadata=True,\n", "            )\n", "        else:\n", "            raise ValueError(f\"Unknown type: {type(c)}\")"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["xgboost_dataset = XGBoostFeatureDataSet(elden_json_data)"]}, {"cell_type": "code", "execution_count": 237, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9233 / 28999 = 0.3183902893203214\n"]}], "source": ["filtered_df = xgboost_dataset.df[xgboost_dataset.df[\"is_ipynb\"] == 1]\n", "res = [filtered_df.iloc[i][\"resolution\"][\"accepted\"] for i in range(len(filtered_df))]\n", "print(f\"{sum(res)} / {len(res)} = {sum(res) / len(res)}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class RecencyFeatureDataSet:\n", "    def __init__(\n", "        self, completion_data_csv_path: str = \"completion_ids.csv\", n_lags: int = 5\n", "    ):\n", "        self.completion_data_csv_path = completion_data_csv_path\n", "        self.n_lags = n_lags\n", "\n", "        completion_data = pd.read_csv(BASE_DIR / completion_data_csv_path)\n", "        completion_data[\"user_id\"] = completion_data[\"user_id\"].apply(\n", "            lambda x: x.split(\"@\")[0]\n", "        )\n", "        completion_data = completion_data.sort_values(by=[\"time\"])\n", "        completion_data[\"filtered\"] = completion_data[\"accepted\"].apply(\n", "            lambda x: 0 if x else 1\n", "        )\n", "\n", "        self.feature_names = [f\"lag_{i}\" for i in range(1, self.n_lags + 1)]\n", "\n", "        self.completion_data = completion_data\n", "\n", "        self.users = [\n", "            \"aswin\",\n", "            \"colin\",\n", "            \"costa\",\n", "            \"des\",\n", "            \"devang\",\n", "            \"dirk\",\n", "            \"edvin\",\n", "            \"eric\",\n", "            \"guy\",\n", "            \"igoros\",\n", "            \"jacqueline\",\n", "            \"jeff\",\n", "            \"joel\",\n", "            \"justin\",\n", "            \"kanav\",\n", "            \"liam\",\n", "            \"lior.neumann\",\n", "            \"luke\",\n", "            \"marcmac\",\n", "            \"markus\",\n", "            \"moogi\",\n", "            \"mpauly\",\n", "            \"msdejong\",\n", "            \"navtej\",\n", "            \"nikita\",\n", "            \"pranay\",\n", "        ]\n", "\n", "        self.user_to_df = self.extract_features()\n", "        self.df = pd.concat(self.user_to_df.values())\n", "\n", "    def extract_features(self):\n", "        user_features = {}\n", "        for user in self.users:\n", "            user_completions = self.completion_data[\n", "                self.completion_data[\"user_id\"] == user\n", "            ]\n", "            # sort by time column\n", "            user_completions = user_completions.sort_values(by=[\"time\"])\n", "            user_completions = user_completions.reset_index(drop=True)\n", "            for i in range(1, self.n_lags + 1):\n", "                user_completions[f\"lag_{i}\"] = user_completions[\"filtered\"].shift(i)\n", "            user_completions = user_completions.dropna()\n", "            user_features[user] = user_completions\n", "\n", "        return user_features"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["recency_dataset = RecencyFeatureDataSet()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(191288, 42)\n", "(144009, 12)\n", "(119116, 53)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>user_id_x</th>\n", "      <th>request</th>\n", "      <th>response</th>\n", "      <th>resolution</th>\n", "      <th>feedback</th>\n", "      <th>user_agent_x</th>\n", "      <th>token_prob_min</th>\n", "      <th>token_prob_median</th>\n", "      <th>token_prob_max</th>\n", "      <th>...</th>\n", "      <th>time</th>\n", "      <th>user_id_y</th>\n", "      <th>user_agent_y</th>\n", "      <th>accepted</th>\n", "      <th>filtered</th>\n", "      <th>lag_1</th>\n", "      <th>lag_2</th>\n", "      <th>lag_3</th>\n", "      <th>lag_4</th>\n", "      <th>lag_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>dbd4dca1-d56b-4d6d-9183-d72e54387746</td>\n", "      <td>joel</td>\n", "      <td>{'prefix': 'import * as vscode from \"vscode\";\n", "...</td>\n", "      <td>{'text': 'public get currentGeneration(): Next...</td>\n", "      <td>{'accepted': False, 'timestamp': 1721147141.321}</td>\n", "      <td>None</td>\n", "      <td>Augment.vscode-augment/0.166.0 (linux; x64; 6....</td>\n", "      <td>0.648176</td>\n", "      <td>0.998327</td>\n", "      <td>0.999990</td>\n", "      <td>...</td>\n", "      <td>2024-07-16 16:25:39.651467 UTC</td>\n", "      <td>joel</td>\n", "      <td>Augment.vscode-augment/0.166.0 (linux; x64; 6....</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>f79c6990-d595-4522-a566-a1fb25c575eb</td>\n", "      <td>joel</td>\n", "      <td>{'prefix': 'import * as vscode from \"vscode\";\n", "...</td>\n", "      <td>{'text': ';', 'model': 'eldenv3-15b', 'skipped...</td>\n", "      <td>{'accepted': True, 'timestamp': 1721317082.002}</td>\n", "      <td>None</td>\n", "      <td>Augment.vscode-augment/0.168.0 (linux; x64; 6....</td>\n", "      <td>0.941479</td>\n", "      <td>0.941479</td>\n", "      <td>0.941479</td>\n", "      <td>...</td>\n", "      <td>2024-07-18 15:38:01.650199 UTC</td>\n", "      <td>joel</td>\n", "      <td>Augment.vscode-augment/0.168.0 (linux; x64; 6....</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23e7963a-2fd9-4349-97a1-d8e0dc911c95</td>\n", "      <td>msdejong</td>\n", "      <td>{'prefix': '\"\"\"Configuration for the Methanol ...</td>\n", "      <td>{'text': '\n", "', 'model': 'eldenv3-15b', 'skipped...</td>\n", "      <td>{'accepted': False, 'timestamp': 1721088275.075}</td>\n", "      <td>None</td>\n", "      <td>Augment.vscode-augment/0.166.0 (linux; x64; 5....</td>\n", "      <td>0.981736</td>\n", "      <td>0.981736</td>\n", "      <td>0.981736</td>\n", "      <td>...</td>\n", "      <td>2024-07-16 00:04:27.362705 UTC</td>\n", "      <td>msdejong</td>\n", "      <td>Augment.vscode-augment/0.166.0 (linux; x64; 5....</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6a61ddf1-c5a2-477f-a4f3-e146fe6782f0</td>\n", "      <td>msdejong</td>\n", "      <td>{'prefix': '\"\"\"A code edit system based on in-...</td>\n", "      <td>{'text': '_ids = model_input.doc_ids', 'model'...</td>\n", "      <td>{'accepted': False, 'timestamp': 1720730958.733}</td>\n", "      <td>None</td>\n", "      <td>Augment.vscode-augment/0.164.0 (linux; x64; 5....</td>\n", "      <td>0.937580</td>\n", "      <td>0.993969</td>\n", "      <td>0.999986</td>\n", "      <td>...</td>\n", "      <td>2024-07-11 20:49:13.995424 UTC</td>\n", "      <td>msdejong</td>\n", "      <td>Augment.vscode-augment/0.164.0 (linux; x64; 5....</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>a3eec6f1-d0fa-4e35-9951-362d68ca8c90</td>\n", "      <td>joel</td>\n", "      <td>{'prefix': 'de.TextDocument) {\n", "        const e...</td>\n", "      <td>{'text': 'i', 'model': 'eldenv3-15b', 'skipped...</td>\n", "      <td>{'accepted': False, 'timestamp': 1721425434.483}</td>\n", "      <td>None</td>\n", "      <td>Augment.vscode-augment/0.170.0 (linux; x64; 6....</td>\n", "      <td>0.928469</td>\n", "      <td>0.928469</td>\n", "      <td>0.928469</td>\n", "      <td>...</td>\n", "      <td>2024-07-19 21:43:52.294285 UTC</td>\n", "      <td>joel</td>\n", "      <td>Augment.vscode-augment/0.170.0 (linux; x64; 6....</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 53 columns</p>\n", "</div>"], "text/plain": ["                             request_id user_id_x  \\\n", "0  dbd4dca1-d56b-4d6d-9183-d72e54387746      joel   \n", "1  f79c6990-d595-4522-a566-a1fb25c575eb      joel   \n", "2  23e7963a-2fd9-4349-97a1-d8e0dc911c95  msdejong   \n", "3  6a61ddf1-c5a2-477f-a4f3-e146fe6782f0  msdejong   \n", "4  a3eec6f1-d0fa-4e35-9951-362d68ca8c90      joel   \n", "\n", "                                             request  \\\n", "0  {'prefix': 'import * as vscode from \"vscode\";\n", "...   \n", "1  {'prefix': 'import * as vscode from \"vscode\";\n", "...   \n", "2  {'prefix': '\"\"\"Configuration for the Methanol ...   \n", "3  {'prefix': '\"\"\"A code edit system based on in-...   \n", "4  {'prefix': 'de.TextDocument) {\n", "        const e...   \n", "\n", "                                            response  \\\n", "0  {'text': 'public get currentGeneration(): Next...   \n", "1  {'text': ';', 'model': 'eldenv3-15b', 'skipped...   \n", "2  {'text': '\n", "', 'model': 'eldenv3-15b', 'skipped...   \n", "3  {'text': '_ids = model_input.doc_ids', 'model'...   \n", "4  {'text': 'i', 'model': 'eldenv3-15b', 'skipped...   \n", "\n", "                                         resolution feedback  \\\n", "0  {'accepted': False, 'timestamp': 1721147141.321}     None   \n", "1   {'accepted': True, 'timestamp': 1721317082.002}     None   \n", "2  {'accepted': False, 'timestamp': 1721088275.075}     None   \n", "3  {'accepted': False, 'timestamp': 1720730958.733}     None   \n", "4  {'accepted': False, 'timestamp': 1721425434.483}     None   \n", "\n", "                                        user_agent_x  token_prob_min  \\\n", "0  Augment.vscode-augment/0.166.0 (linux; x64; 6....        0.648176   \n", "1  Augment.vscode-augment/0.168.0 (linux; x64; 6....        0.941479   \n", "2  Augment.vscode-augment/0.166.0 (linux; x64; 5....        0.981736   \n", "3  Augment.vscode-augment/0.164.0 (linux; x64; 5....        0.937580   \n", "4  Augment.vscode-augment/0.170.0 (linux; x64; 6....        0.928469   \n", "\n", "   token_prob_median  token_prob_max  ...                            time  \\\n", "0           0.998327        0.999990  ...  2024-07-16 16:25:39.651467 UTC   \n", "1           0.941479        0.941479  ...  2024-07-18 15:38:01.650199 UTC   \n", "2           0.981736        0.981736  ...  2024-07-16 00:04:27.362705 UTC   \n", "3           0.993969        0.999986  ...  2024-07-11 20:49:13.995424 UTC   \n", "4           0.928469        0.928469  ...  2024-07-19 21:43:52.294285 UTC   \n", "\n", "   user_id_y                                       user_agent_y  accepted  \\\n", "0       joel  Augment.vscode-augment/0.166.0 (linux; x64; 6....     False   \n", "1       joel  Augment.vscode-augment/0.168.0 (linux; x64; 6....      True   \n", "2   msdejong  Augment.vscode-augment/0.166.0 (linux; x64; 5....     False   \n", "3   msdejong  Augment.vscode-augment/0.164.0 (linux; x64; 5....     False   \n", "4       joel  Augment.vscode-augment/0.170.0 (linux; x64; 6....     False   \n", "\n", "   filtered  lag_1  lag_2  lag_3  lag_4  lag_5  \n", "0         1    1.0    1.0    0.0    0.0    0.0  \n", "1         0    1.0    0.0    0.0    0.0    1.0  \n", "2         1    1.0    1.0    0.0    0.0    0.0  \n", "3         1    1.0    1.0    1.0    1.0    1.0  \n", "4         1    1.0    1.0    1.0    0.0    1.0  \n", "\n", "[5 rows x 53 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["print(xgboost_dataset.df.shape)\n", "print(recency_dataset.df.shape)\n", "# Merge two DF on request_id\n", "\n", "merged_df = pd.merge(xgboost_dataset.df, recency_dataset.df, on=\"request_id\")\n", "print(merged_df.shape)\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered rate 0.630763289566473\n"]}], "source": ["# get accepted\n", "res = []\n", "for i in range(len(merged_df[\"resolution\"])):\n", "    if merged_df[\"resolution\"].iloc[i][\"accepted\"]:\n", "        res.append(0)\n", "    else:\n", "        res.append(1)\n", "\n", "print(\"Filtered rate\", sum(res) / len(res))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Models"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [], "source": ["import xgboost as xgb\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report\n", "from sklearn.metrics import roc_auc_score"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [], "source": ["class TrainModels:\n", "    def __init__(self, df, xgboost_features, recency_features, n_lags=5):\n", "        self.df = df\n", "        self.xgboost_features = xgboost_features\n", "        self.recency_features = recency_features\n", "        self.n_lags = n_lags\n", "\n", "        # split into train and val set\n", "        self.train_df, self.val_df = train_test_split(\n", "            self.df, test_size=0.1, random_state=42\n", "        )\n", "        self.train_df = self.train_df.reset_index(drop=True)\n", "        self.val_df = self.val_df.reset_index(drop=True)\n", "\n", "        print(f\"train: {self.train_df.shape}\")\n", "        print(f\"val: {self.val_df.shape}\")\n", "\n", "    def train_recency_model(self):\n", "        X_train = self.train_df[[f\"lag_{i}\" for i in range(1, self.n_lags + 1)]]\n", "        y_train = self.train_df[\"filtered\"]\n", "\n", "        X_val = self.val_df[[f\"lag_{i}\" for i in range(1, self.n_lags + 1)]]\n", "        y_val = self.val_df[\"filtered\"]\n", "\n", "        model = LogisticRegression()\n", "        model.fit(X_train, y_train)\n", "\n", "        y_pred = model.predict(X_val)\n", "\n", "        y_pred_proba = model.predict_proba(X_val)[::, 1]\n", "        # fpr, tpr, _ = roc_curve(y_val, y_pred_proba)\n", "\n", "        # Evaluate the model\n", "        print(\"Train Recency Model:\")\n", "        print(\"Accuracy:\", accuracy_score(y_val, y_pred))\n", "        print(f\"Eval AUCROC: {roc_auc_score(y_val, y_pred_proba)}\")\n", "        print(\"\\nClassification Report:\")\n", "        print(classification_report(y_val, y_pred))\n", "\n", "    def to_dmatrix(\n", "        self, df_X: pd.DataFrame, df_y: pd.Series, features: list\n", "    ) -> xgb.DMatrix:\n", "        \"\"\"Converts a DataFrame and Series into an XGBoost DMatrix.\"\"\"\n", "        assert len(df_X) == len(df_y)\n", "\n", "        # Ensure we only use the specified features\n", "        X = []\n", "        y = []\n", "        for d, label in zip(df_X.iterrows(), df_y):\n", "            cache = []\n", "            for key in features:\n", "                cache.append(d[1][key])\n", "            X.append(cache)\n", "            y.append(label)\n", "\n", "        return xgb.DMatrix(\n", "            X,\n", "            label=y,\n", "            feature_names=features,\n", "        )\n", "\n", "    def train_xgboost_model(self, ckpt_path=None):\n", "        X_train = self.train_df[self.xgboost_features]\n", "        y_train = self.train_df[\"filtered\"]\n", "\n", "        X_val = self.val_df[self.xgboost_features]\n", "        y_val = self.val_df[\"filtered\"]\n", "\n", "        model = None\n", "        if ckpt_path:\n", "            # load xgboost model from checkpoint\n", "            print(f\"Loading model from checkpoint: {ckpt_path}\")\n", "            model = xgb.Booster(model_file=ckpt_path)\n", "            features = model.feature_names\n", "            assert features\n", "            deval = self.to_dmatrix(X_val, y_val, list(features))\n", "        else:\n", "            print(\"Training model...\")\n", "            dtrain = self.to_dmatrix(X_train, y_train, self.xgboost_features)\n", "            deval = self.to_dmatrix(X_val, y_val, self.xgboost_features)\n", "            eval_set = [(deval, \"eval\")]\n", "            model = xgb.train(\n", "                dict(\n", "                    max_depth=4,\n", "                    eta=0.005,\n", "                    objective=\"binary:logistic\",\n", "                    random_state=0,\n", "                    alpha=2,\n", "                    gamma=0.1,  # Minimum loss reduction for spli\n", "                ),\n", "                dtrain,\n", "                early_stopping_rounds=50,\n", "                num_boost_round=1000,\n", "                evals=eval_set,\n", "                verbose_eval=False,\n", "            )\n", "\n", "        labels = deval.get_label()\n", "        bst_predict = model.predict(deval)\n", "        # fpr, tpr, thresholds = roc_curve(labels, bst_predict)\n", "        bst_predict_binary = (bst_predict >= 0.5).astype(int)\n", "\n", "        # get accuracy\n", "        print(\"XGBoost Model:\")\n", "        print(f\"Accuracy: {accuracy_score(labels, bst_predict_binary)}\")\n", "        print(\"Eval AUCROC\", roc_auc_score(labels, bst_predict))\n", "        print(\"Classification Report:\")\n", "        print(classification_report(labels, bst_predict_binary))"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train: (107204, 53)\n", "val: (11912, 53)\n"]}], "source": ["train_models = TrainModels(\n", "    merged_df, xgboost_dataset.feature_names, recency_dataset.feature_names\n", ")"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training model...\n", "XGBoost Model:\n", "Accuracy: 0.7010577568838147\n", "Eval AUCROC 0.7334736782881041\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.63      0.46      0.53      4402\n", "         1.0       0.73      0.84      0.78      7510\n", "\n", "    accuracy                           0.70     11912\n", "   macro avg       0.68      0.65      0.66     11912\n", "weighted avg       0.69      0.70      0.69     11912\n", "\n"]}], "source": ["train_models.train_xgboost_model()"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading model from checkpoint: /home/<USER>/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\n", "XGBoost Model:\n", "Accuracy: 0.6353257219610476\n", "Eval AUCROC 0.6837505921228155\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.51      0.66      0.57      4402\n", "         1.0       0.76      0.62      0.68      7510\n", "\n", "    accuracy                           0.64     11912\n", "   macro avg       0.63      0.64      0.63     11912\n", "weighted avg       0.66      0.64      0.64     11912\n", "\n"]}], "source": ["train_models.train_xgboost_model(ckpt_path=ckpt_path)"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train Recency Model:\n", "Accuracy: 0.6841840161182001\n", "Eval AUCROC: 0.6825210789672531\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.61      0.41      0.49      4402\n", "           1       0.71      0.84      0.77      7510\n", "\n", "    accuracy                           0.68     11912\n", "   macro avg       0.66      0.63      0.63     11912\n", "weighted avg       0.67      0.68      0.67     11912\n", "\n"]}], "source": ["train_models.train_recency_model()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
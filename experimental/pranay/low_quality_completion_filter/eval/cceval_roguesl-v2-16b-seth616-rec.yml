# Usages:
# python research/eval/eval.py --v2 experimental/vzhao/20240214_confidence_filter/eval/cceval_roguesl-v2-16b-seth616-rec.yml

system:
    name: remote_completion

    # The model name is optional, will select default if not present
    model_name: roguesl-v2-16b-seth616-rec

    retriever:
        wait_indexing_retry_count: 60
        disable_wait_indexing: true

    completion:
        warn_on_unknown_blobs: true

    client:
        # Set a url to run the remote system.
        # url: https://dev-<USER>.us-central.api.augmentcode.com
        url: https://dev-vzhao.us-central.api.augmentcode.com

        # If not running on determined, the client searchs for the API token
        # in $AUGMENT_TOKEN or ~/.config/augment/api_token
        # To generate your own API token, see
        # https://www.notion.so/Runbook-How-to-generate-API-tokens-a7ede88059604149867f03c2cf6f434b
        # api_token_env_var: AUGMENT_TOKEN
        # api_token_path: ~/.config/augment/api_token

        # These control global retry settings for the client.
        # Likely something is wrong if you need to change these, but if your pods
        # are restarting often, you could try increasing retry_sleep or retry_count.
        # timeout: 60
        # retry_count: 2
        # retry_sleep: 0.1

task:
    name: cceval
    limit: 1000

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: CCEval, remote model, roguesl-v2-16b-seth616-rec, 1000
  workspace: Dev
  project: vzhao-eval

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import datetime\n", "import argparse\n", "import logging\n", "from google.cloud import bigquery  # type: ignore\n", "from google.api_core.retry import Retry\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher  # type: ignore\n", "from typing import Iterable\n", "from typing import Optional\n", "from base.datasets.gcs_client import Request\n", "from base.datasets.tenants import DatasetTenant, get_tenant\n", "from tools.load_test import load_test_pb2\n", "from typing import List\n", "from google.protobuf import json_format\n", "from services.api_proxy import public_api_pb2\n", "from services.next_edit_host import next_edit_pb2\n", "from services.request_insight import request_insight_pb2\n", "from google.protobuf.json_format import MessageToJson\n", "import json\n", "from google.cloud import storage\n", "from dateutil import parser\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.interactiveshell import InteractiveShell\n", "\n", "InteractiveShell.ast_node_interactivity = \"all\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_request_ids_by_user(\n", "    request_type: str,\n", "    start: datetime.datetime,\n", "    end: datetime.datetime,\n", "    user_limit: int,\n", "    requests_per_user_limit: int,\n", "    tenant: DatasetTenant,\n", ") -> Iterable[bigquery.Row]:\n", "    \"\"\"\n", "    Get user request IDs and times from BigQuery, sorted by time from oldest to newest.\n", "\n", "    Args:\n", "        start_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of first request.\n", "        end_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of last request.\n", "        user_limit: The maximum number of users to return.\n", "        requests_per_user_limit: The maximum number of requests per user to return.\n", "        tenant_id: The tenant ID to filter by.\n", "        project_name: The name of the project where the BigQuery table is located.\n", "\n", "    Returns:\n", "        An iterable of rows, each containing a user_id and an array of structs with request_id and time, sorted from oldest to newest.\n", "    \"\"\"\n", "\n", "    QUERY = \"\"\"\n", "    SELECT\n", "        user_id,\n", "        ARRAY_AGG(STRUCT(request_id, time) ORDER BY time ASC LIMIT @requests_per_user_limit) AS requests\n", "    FROM\n", "        `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata`\n", "    WHERE\n", "        request_type = @request_type\n", "        AND\n", "        tenant_id = @tenant_id\n", "        AND\n", "        (TIMESTAMP(time) BETWEEN TIMESTAMP(@start_iso) AND TIMESTAMP(@end_iso))\n", "    GROUP BY user_id\n", "    LIMIT @user_limit\n", "    \"\"\"\n", "\n", "    PARAMS = [\n", "        bigquery.ScalarQueryParameter(\"tenant_id\", \"STRING\", tenant.tenant_id),\n", "        bigquery.ScalarQueryParameter(\"request_type\", \"STRING\", request_type),\n", "        bigquery.ScalarQueryParameter(\"start_iso\", \"DATETIME\", start.isoformat()),\n", "        bigquery.ScalarQueryParameter(\"end_iso\", \"DATETIME\", end.isoformat()),\n", "        bigquery.ScalarQueryParameter(\"user_limit\", \"INT64\", user_limit),\n", "        bigquery.ScalarQueryParameter(\n", "            \"requests_per_user_limit\", \"INT64\", requests_per_user_limit\n", "        ),\n", "    ]\n", "\n", "    client = bigquery.Client(project=tenant.project_id)\n", "\n", "    query_retry = Retry(\n", "        initial=10,  # 10 seconds per page before retrying\n", "        maximum=60,  # 60 seconds max per page\n", "        multiplier=1.5,  # Backoff\n", "        timeout=60 * 5,  # 5 minutes before giving up\n", "    )\n", "\n", "    for row in client.query(\n", "        QUERY,\n", "        retry=query_retry,\n", "        job_config=bigquery.QueryJobConfig(\n", "            use_legacy_sql=False,\n", "            query_parameters=PARAMS,\n", "        ),\n", "    ).result():\n", "        yield row"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_request_events_from_ids(\n", "    request_ids: List[str],\n", "    tenant: DatasetTenant,\n", "    request_events: Optional[frozenset[str]] = None,\n", ") -> Iterable[List[request_insight_pb2.RequestEvent]]:\n", "    \"\"\"\n", "    Get events for a user's requests.\n", "\n", "    Args:\n", "        request_ids: A list of request IDs for which to get events.\n", "        project_name: The name of the project where the request events are stored.\n", "        tenant_id: The tenant ID to filter by.\n", "        request_events: A list of request event names to filter by.\n", "\n", "    Returns:\n", "        An iterable of lists of events, each list containing the events for a single request.\n", "    \"\"\"\n", "\n", "    if not request_ids:\n", "        return []\n", "\n", "    results = GCSRequestInsightFetcher.from_tenant(tenant).get_requests(\n", "        request_ids=request_ids,\n", "        request_event_names=request_events,\n", "    )\n", "    for request in results:\n", "        if isinstance(request, Exception):\n", "            continue\n", "        yield request.events"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def convert_next_edit_host_request_to_public_api_request(\n", "    next_edit_host_request: next_edit_pb2.NextEditRequest,\n", ") -> public_api_pb2.NextEditRequest:\n", "    \"\"\"\n", "    Convert a NextEditRequest from the NextEditHost to a NextEditRequest for the API.\n", "\n", "    Args:\n", "        next_edit_host_request: The NextEditRequest from the NextEditHost.\n", "\n", "    Returns:\n", "        A NextEditRequest for the API.\n", "    \"\"\"\n", "\n", "    change_type_mapping = {\n", "        next_edit_pb2.ChangeType.ADDED: public_api_pb2.ChangeType.ADDED,\n", "        next_edit_pb2.ChangeType.DELETED: public_api_pb2.ChangeType.DELETED,\n", "        next_edit_pb2.ChangeType.MODIFIED: public_api_pb2.ChangeType.MODIFIED,\n", "        next_edit_pb2.ChangeType.RENAMED: public_api_pb2.ChangeType.RENAMED,\n", "    }\n", "\n", "    diagnostic_severity_mapping = {\n", "        next_edit_pb2.DiagnosticSeverity.ERROR: public_api_pb2.DiagnosticSeverity.ERROR,\n", "        next_edit_pb2.DiagnosticSeverity.WARNING: public_api_pb2.DiagnosticSeverity.WARNING,\n", "        next_edit_pb2.DiagnosticSeverity.INFORMATION: public_api_pb2.DiagnosticSeverity.INFORMATION,\n", "        next_edit_pb2.DiagnosticSeverity.HINT: public_api_pb2.DiagnosticSeverity.HINT,\n", "    }\n", "\n", "    next_edit_mode_mapping = {\n", "        next_edit_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE: public_api_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,\n", "        next_edit_pb2.NextEditMode.BACKGROUND: public_api_pb2.NextEditMode.BACKGROUND,\n", "        next_edit_pb2.NextEditMode.FOREGROUND: public_api_pb2.NextEditMode.FOREGROUND,\n", "        next_edit_pb2.NextEditMode.FORCED: public_api_pb2.NextEditMode.FORCED,\n", "    }\n", "\n", "    next_edit_scope_mapping = {\n", "        next_edit_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE: public_api_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,\n", "        next_edit_pb2.NextEditScope.CURSOR: public_api_pb2.NextEditScope.CURSOR,\n", "        next_edit_pb2.NextEditScope.FILE: public_api_pb2.NextEditScope.FILE,\n", "        next_edit_pb2.NextEditScope.WORKSPACE: public_api_pb2.NextEditScope.WORKSPACE,\n", "    }\n", "\n", "    public_api_request = public_api_pb2.NextEditRequest(\n", "        model=next_edit_host_request.model_name,\n", "        sequence_id=next_edit_host_request.sequence_id,\n", "        lang=next_edit_host_request.lang,\n", "        instruction=next_edit_host_request.instruction,\n", "        blobs=public_api_pb2.Blobs(\n", "            checkpoint_id=next_edit_host_request.blobs.baseline_checkpoint_id,\n", "            added_blobs=[bytes.hex() for bytes in next_edit_host_request.blobs.added],\n", "            deleted_blobs=[\n", "                bytes.hex() for bytes in next_edit_host_request.blobs.deleted\n", "            ],\n", "        ),\n", "        recent_changes=[\n", "            public_api_pb2.ReplacementText(\n", "                blob_name=recent_change.blob_name,\n", "                path=recent_change.path,\n", "                char_start=recent_change.char_start,\n", "                char_end=recent_change.char_end,\n", "                replacement_text=recent_change.replacement_text,\n", "                present_in_blob=recent_change.present_in_blob,\n", "            )\n", "            for recent_change in next_edit_host_request.recent_changes\n", "        ],\n", "        vcs_change=public_api_pb2.VCSChange(\n", "            working_directory_changes=[\n", "                public_api_pb2.WorkingDirectoryChange(\n", "                    before_path=wdc.before_path,\n", "                    after_path=wdc.after_path,\n", "                    change_type=change_type_mapping[wdc.change_type],\n", "                    head_blob_name=wdc.head_blob_name,\n", "                    indexed_blob_name=wdc.indexed_blob_name,\n", "                    current_blob_name=wdc.current_blob_name,\n", "                )\n", "                for wdc in next_edit_host_request.vcs_change.working_directory_changes\n", "            ]\n", "        ),\n", "        path=next_edit_host_request.path,\n", "        blob_name=next_edit_host_request.blob_name,\n", "        selection_begin_char=next_edit_host_request.selection_begin_char,\n", "        selection_end_char=next_edit_host_request.selection_end_char,\n", "        prefix=next_edit_host_request.prefix,\n", "        selected_text=next_edit_host_request.selected_text,\n", "        suffix=next_edit_host_request.suffix,\n", "        diagnostics=[\n", "            public_api_pb2.Diagnostic(\n", "                location=public_api_pb2.FileLocation(\n", "                    path=diagnostic.location.path,\n", "                    line_start=diagnostic.location.line_start,\n", "                    line_end=diagnostic.location.line_end,\n", "                ),\n", "                message=diagnostic.message,\n", "                severity=diagnostic_severity_mapping[diagnostic.severity],\n", "            )\n", "            for diagnostic in next_edit_host_request.diagnostics\n", "        ],\n", "        mode=next_edit_mode_mapping[next_edit_host_request.mode],\n", "        scope=next_edit_scope_mapping[next_edit_host_request.scope],\n", "        edit_events=[\n", "            public_api_pb2.FileEditEvent(\n", "                path=granular_edit_event.path,\n", "                before_blob_name=granular_edit_event.before_blob_name,\n", "                after_blob_name=granular_edit_event.after_blob_name,\n", "                edits=[\n", "                    public_api_pb2.FileEdit(\n", "                        before_start=single_edit.before_start,\n", "                        after_start=single_edit.after_start,\n", "                        before_text=single_edit.before_text,\n", "                        after_text=single_edit.after_text,\n", "                    )\n", "                    for single_edit in granular_edit_event.edits\n", "                ],\n", "            )\n", "            for granular_edit_event in next_edit_host_request.edit_events\n", "        ],\n", "        blocked_locations=[\n", "            public_api_pb2.FileRegion(\n", "                path=blocked_location.path,\n", "                char_start=blocked_location.char_start,\n", "                char_end=blocked_location.char_end,\n", "            )\n", "            for blocked_location in next_edit_host_request.blocked_locations\n", "        ],\n", "    )\n", "\n", "    return public_api_request"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def skip_row(row: bigquery.Row) -> bool:\n", "    \"\"\"\n", "    Determine if a row should be skipped based on its contents.\n", "\n", "    Args:\n", "        row: The row to check.\n", "\n", "    Returns:\n", "        True if the row should be skipped, False otherwise.\n", "    \"\"\"\n", "\n", "    if row.requests is None:\n", "        return True\n", "\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DEFAULT_TENANT = get_tenant(\"dogfood-shard\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_ids_by_user = get_request_ids_by_user(\n", "    request_type=\"NEXT_EDIT\",\n", "    start=datetime.datetime.fromisoformat(\"2024-10-08\"),\n", "    end=datetime.datetime.fromisoformat(\"2024-10-28\"),\n", "    user_limit=50000,\n", "    requests_per_user_limit=10000,\n", "    tenant=DEFAULT_TENANT,\n", ")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["user_request_ids = []\n", "\n", "for row in request_ids_by_user:\n", "    if skip_row(row):\n", "        continue\n", "    user_request_ids.extend([request[\"request_id\"] for request in row.requests])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["331712\n", "331709\n"]}], "source": ["print(len(user_request_ids))\n", "print(len(set(user_request_ids)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No events with names in {'next_edit_host_request'} for request ID: a54934a0-9371-4a4e-9ad9-f430cbbd7138. Found event names are: ['api_http_response', 'request_metadata']\n"]}], "source": ["results = []\n", "for request_events in get_request_events_from_ids(\n", "    user_request_ids[10000:30000],\n", "    tenant=DEFAULT_TENANT,\n", "    request_events=frozenset({\"next_edit_host_request\"}),\n", "):\n", "    for event in request_events:\n", "        next_edit_host_request = event.next_edit_host_request.request\n", "\n", "        # convert the next_edit_host_request to a public_api_request\n", "        public_api_next_edit_request = (\n", "            convert_next_edit_host_request_to_public_api_request(next_edit_host_request)\n", "        )\n", "        results.append(public_api_next_edit_request)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["19999"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["len(results)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["def get_json_size_full(repeated_field):\n", "    json_list = []\n", "    for item in repeated_field:\n", "        json_list.append(\n", "            {\n", "                \"path\": item.path,\n", "                \"before_blob_name\": item.before_blob_name,\n", "                \"after_blob_name\": item.after_blob_name,\n", "                \"edits\": [\n", "                    {\n", "                        \"before_start\": edit.before_start,\n", "                        \"before_text\": edit.before_text,\n", "                        \"after_start\": edit.after_start,\n", "                        \"after_text\": edit.after_text,\n", "                    }\n", "                    for edit in item.edits\n", "                ],\n", "            }\n", "        )\n", "    json_string = json.dumps(json_list)\n", "    return len(json_string.encode(\"utf-8\"))\n", "\n", "\n", "def get_json_size(repeated_field):\n", "    length = 0\n", "    for item in repeated_field:\n", "        for edit in item.edits:\n", "            length += len(edit.before_text) + len(edit.after_text)\n", "    return length"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["306 | 30759 | 100\n", "[333, 337, 337, 346, 347, 347, 348, 356, 358, 358]\n", "avg: 14.411870593529677\n", "max_multi: 100, cnt: 7122\n", "max_after: 89933\n"]}], "source": ["avg_multi = []\n", "max_after = 0\n", "cnt = 0\n", "max_multi = 0\n", "\n", "for res in results:\n", "    after_before_text_size = get_json_size(res.edit_events)\n", "    json_size_full = get_json_size_full(res.edit_events)\n", "    # print(f\"{after_before_text_size} => {json_size_full}\")\n", "    if after_before_text_size == 0:\n", "        print(f\"full={res.edit_events}\")\n", "        continue\n", "    multi = json_size_full // after_before_text_size\n", "\n", "    avg_multi.append(multi)\n", "    if json_size_full >= 20000:\n", "        max_multi = max(max_multi, multi)\n", "        if multi == 100:\n", "            print(f\"{after_before_text_size} | {json_size_full} | {multi}\")\n", "        #     print(res.edit_events)\n", "        #     break\n", "        cnt += 1\n", "\n", "    max_after = max(max_after, json_size_full)\n", "\n", "avg_multi.sort()\n", "\n", "print(avg_multi[-10:])\n", "print(f\"avg: {sum(avg_multi) / len(avg_multi)}\")\n", "print(f\"max_multi: {max_multi}, cnt: {cnt}\")\n", "print(f\"max_after: {max_after}\")"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"data": {"text/plain": ["[path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"3e84cbbe2cc24e2b8d52c0b4afccaa552d7e7dba9d72ddd452466594c958085e\"\n", "after_blob_name: \"91af30cdea7f5b4b5a72301bd14cfd7f8ac3541b63119854020f5683a477560b\"\n", "edits {\n", "  before_start: 4376\n", "  before_text: \"return vscode.window.showInputBox({\\n            prompt: \\\"Enter the test command to run (e.g. `pytest`)\\\",\\n            placeHolder: \\\"Test command like `pytest research/core/diff_utils_test.py`\\\",\\n        })\"\n", "  after_start: 4376\n", "  after_text: \"const context = this._extension.context;\\n        const historyKey = \\'testCommandHistory\\';\\n        let history: string[] = context.globalState.get(historyKey, []);\\n\\n        const result = await vscode.window.showInputBox({\\n            prompt: \\\"Enter the test command\\\",\\n            placeHolder: \\\"npm test\\\",\\n            ignoreFocusOut: true,\\n            value: history[0] || \\'\\',\\n            valueSelection: [0, history[0]?.length || 0],\\n        });\\n\\n        if (result) {\\n            history = [result, ...history.filter(item => item !== result)].slice(0, 10);\\n            await context.globalState.update(historyKey, history);\\n        }\\n\\n        return result\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"91af30cdea7f5b4b5a72301bd14cfd7f8ac3541b63119854020f5683a477560b\"\n", "after_blob_name: \"2000f2c1b6321407ee01bc6b0292b1fda1316d563eeff94144fc791c66ef546d\"\n", "edits {\n", "  before_start: 4641\n", "  after_start: 4641\n", "  after_text: \" to run\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"2000f2c1b6321407ee01bc6b0292b1fda1316d563eeff94144fc791c66ef546d\"\n", "after_blob_name: \"181825ad528a717459cebd8d9a6e79bbe52ba749ce8310d92e329f13a53bf90c\"\n", "edits {\n", "  before_start: 4537\n", "  after_start: 4537\n", "  after_text: \" as string[]\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"181825ad528a717459cebd8d9a6e79bbe52ba749ce8310d92e329f13a53bf90c\"\n", "after_blob_name: \"b78d989ca941cc3e9c1690ceda0f849b27d94af4617c361d394fac66ecac2583\"\n", "edits {\n", "  before_start: 4385\n", "  before_text: \"text = this._extension.context\"\n", "  after_start: 4385\n", "  after_text: \"fig = vscode.workspace.getConfiguration(\\'augment\\')\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"b78d989ca941cc3e9c1690ceda0f849b27d94af4617c361d394fac66ecac2583\"\n", "after_blob_name: \"58657cbb4e766247d1d680354bcd2368e7242f6d08689e64e6c37e3c98359763\"\n", "edits {\n", "  before_start: 4521\n", "  before_text: \"text.globalState.get(historyKey, []) as string[]\"\n", "  after_start: 4521\n", "  after_text: \"fig.get(historyKey, [])\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"58657cbb4e766247d1d680354bcd2368e7242f6d08689e64e6c37e3c98359763\"\n", "after_blob_name: \"ad4708e56552ca8ea0963cc90c741690ceef1420f4be459f67cfcef70a8951bd\"\n", "edits {\n", "  before_start: 4969\n", "  before_text: \"text.globalState.update(historyKey, history\"\n", "  after_start: 4969\n", "  after_text: \"fig.update(historyKey, history, vscode.ConfigurationTarget.Workspace\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"ad4708e56552ca8ea0963cc90c741690ceef1420f4be459f67cfcef70a8951bd\"\n", "after_blob_name: \"5024c7c6dfaffcf25351fbee76dd7ba7d4e35b74a82ba4f0a8335a8ea2fa738b\"\n", "edits {\n", "  before_start: 4465\n", "  after_start: 4465\n", "  after_text: \"autofix.\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"5024c7c6dfaffcf25351fbee76dd7ba7d4e35b74a82ba4f0a8335a8ea2fa738b\"\n", "after_blob_name: \"0a974462f9e35a55e075c3ea2000b18e04705a18ef120bfd8b144e8bedf06ad9\"\n", "edits {\n", "  before_start: 1705\n", "  after_start: 1705\n", "  after_text: \" private static testCommandHistory: string[] = [];\"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"0a974462f9e35a55e075c3ea2000b18e04705a18ef120bfd8b144e8bedf06ad9\"\n", "after_blob_name: \"766561fbb9c0b27e2b7409bf22c1ca0981f95e0f063c6db2bfd174116ff3ccce\"\n", "edits {\n", "  before_start: 1706\n", "  after_start: 1706\n", "  after_text: \"   \"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"766561fbb9c0b27e2b7409bf22c1ca0981f95e0f063c6db2bfd174116ff3ccce\"\n", "after_blob_name: \"b466d85b7ba57087f953bccac83f6f2919645ee41f86d18fd0aa43bfddb3a4a6\"\n", "edits {\n", "  before_start: 1758\n", "  after_start: 1758\n", "  after_text: \"\\n    \"\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"b466d85b7ba57087f953bccac83f6f2919645ee41f86d18fd0aa43bfddb3a4a6\"\n", "after_blob_name: \"2be22284b9ec1b95e79703086700ff473d382ae0456403853a71a0e5ac9678b5\"\n", "edits {\n", "  before_start: 1709\n", "  before_text: \"private static testCommandHistory: string[] = [];\"\n", "  after_start: 1709\n", "}\n", "edits {\n", "  before_start: 1759\n", "  before_text: \"    \"\n", "  after_start: 1710\n", "}\n", ", path: \"clients/vscode/src/commands/autofix.ts\"\n", "before_blob_name: \"2be22284b9ec1b95e79703086700ff473d382ae0456403853a71a0e5ac9678b5\"\n", "after_blob_name: \"6b65c0a81342a14c60351b9dacabe0f71f06c94449975177de2923d7a2da34cd\"\n", "edits {\n", "  before_start: 1598\n", "  after_start: 1598\n", "  after_text: \"    private static testCommandHistory: string[] = [];\"\n", "}\n", "]"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["results[10].edit_events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Launch evals for the Elden system."""

import argparse
import json
import pathlib

from experimental.pranay.configs.eval.execute_eval import execute_eval
from experimental.pranay.configs.eval.model_configs import model_config_dict
from experimental.pranay.configs.eval.retriever_configs import retriever_config_dict


def main(args):
    model_config = model_config_dict[args.model]

    if "checkpoint" in model_config:
        checkpoint_name = model_config["checkpoint"]
        determined_checkpoint_id = model_config["checkpoint"]
    elif "checkpoint_path" in model_config:
        checkpoint_name = model_config["checkpoint_path"]
    elif "model_path" in model_config:
        checkpoint_name = model_config["model_path"]
    else:
        raise ValueError("No checkpoint path found.")

    if args.model.endswith("ffw"):
        # Checkout SHA365
        info_path = pathlib.Path(
            f"/mnt/efs/augment/checkpoints/{checkpoint_name}/info.json"
        )
        assert info_path.exists()
        # Run cat .../info.json | jq '.manifestSha256'
        with open(info_path, "r") as file:
            xdata = json.load(file)
            manifest_sha256 = xdata.get("manifestSha256")
            assert manifest_sha256 is not None, f"Check {info_path}"
        model_config["checkpoint_sha256"] = manifest_sha256

    dense_retriever_config = retriever_config_dict[args.dense_retriever]
    sig_retriever_config = retriever_config_dict[args.sig_retriever]

    system_config = {
        "name": "elden",
        "model": model_config,
        "generation_options": {"max_generated_tokens": 280},
        "dense_retriever": dense_retriever_config,
        "signature_retriever": sig_retriever_config,
        "experimental": {
            "remove_suffix": False,
            "trim_on_dedent": False,
            "retriever_top_k": 32,
            "signature_retriever_top_k": 32,
        },
        "fim_gen_mode": "evaluation",
    }

    num_gpus = 1
    if "model_parallel_size" in model_config:
        num_gpus = model_config["model_parallel_size"]

    tasks = {
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "hindsight": {
            "name": "hindsight",
            "dataset": "2024-04-25-v0.7",
            "tenant_name": "dogfood",
            "service_account_file": "/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json",
        },
        "cceval": {"name": "cceval", "score_interval": 1000},
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "23lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
    }

    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue

        if num_gpus > 1:
            pod_spec = f"{num_gpus}xH100.yaml"
        else:
            pod_spec = "1xH100.yaml"

        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        task_name = (
            task_config["dataset"] if "dataset" in task_config else task_config["name"]
        )
        determined_checkpoint_id = None

        retriever_name = args.dense_retriever
        exp_name = args.exp_name

        determined_name = (
            f"{task_name}, {checkpoint_name}, {retriever_name}, {exp_name}"
        )

        augment_args = {
            "gpu_count": num_gpus,
            "dai_gcp_service_accounts": [
                {
                    "secret": "aug-prod-cw-ri-importer",  # pragma: allowlist secret
                    "mountpoint": "/mnt/augment/secrets/cw-ri-importer",
                }
            ],
        }
        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            determined_checkpoint_id=determined_checkpoint_id,
            additional_overrides={"augment": augment_args},
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        "-m",
        type=str,
        help="The key in the model dict.",
    )
    parser.add_argument(
        "--dense-retriever",
        "-d",
        type=str,
        help="Dense key in the retriever dict.",
    )
    parser.add_argument(
        "--sig-retriever",
        "-s",
        type=str,
        help="Sig key in the retriever dict.",
    )
    parser.add_argument(
        "--exp_name",
        "-e",
        type=str,
        default="",
        help="The experiment name.",
    )
    parser.add_argument(
        "--tasks",
        "-t",
        nargs="+",
        help="List of specific task names to run.",
        default=["multilang", "cceval", "functions", "23lines", "api", "hindsight"],
    )

    parsed_args = parser.parse_args()

    main(parsed_args)

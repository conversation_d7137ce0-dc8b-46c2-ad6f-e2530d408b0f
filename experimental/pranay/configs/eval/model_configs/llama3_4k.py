"""Configuration for DeepRogue with Stateless Caching system."""

config = {
    "name": "llama3_roguesl",
    "checkpoint_path": "llama3_rogue/8b_4k_4ksteps",
    "fim_gen_mode": "interactive",
    "prompt": {
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "component_order": [
            "prefix",
            "suffix",
            "retrieval",
            "nearby_prefix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
        "prepend_bos_token": True,
    },
}

"""Configuration for roguesl system with 2k context, using nearby prefix."""

config = {
    "name": "rogue2_statelesscache",
    "checkpoint_path": "roguesl/star2_roguesl_eth6_4m_morelang3_fprefret_npref250suf_quant50_rdrop015",
    "model_parallel_size": 2,
    "prompt": {
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "component_order": [
            "prefix",
            "suffix",
            "retrieval",
            "nearby_prefix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
    },
}

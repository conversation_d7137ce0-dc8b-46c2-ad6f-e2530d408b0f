"""Configuration for roguesl system with 2k context, using nearby prefix."""

config = {
    "name": "rogue_statelesscache",
    "checkpoint_path": "roguesl/16b_roguesl_eth6_4m_morelang2_4k7seq_fprefsufret_npref250_nsuf250_quant50_rdrop015",
    "prompt": {
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 512,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "component_order": [
            "prefix",
            "suffix",
            "retrieval",
            "nearby_prefix",
            "nearby_suffix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 250,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
        "use_far_suffix_token": True,
    },
}

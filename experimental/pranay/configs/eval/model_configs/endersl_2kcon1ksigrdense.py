"""Configuration for ender system with 2k context, 1k signatures and rest dense."""

config = {
    "model": {
        "name": "ender_fastforward",
        "model_path": "ender/16b_ender_multieth64m_prefsufsigretnpref250_quant50_daug05_ddrop03_sdrop03",
        "prompt": {
            "max_prefix_tokens": 1030,
            "max_suffix_tokens": 768,
            "max_signature_tokens": 1024,
            "max_prompt_tokens": 5120,
            "max_retrieved_chunk_tokens": -1,
            "nearby_prefix_token_len": 250,
            "context_quant_token_len": 50,
            "component_order": [
                "prefix",
                "suffix",
                "signature",
                "retrieval",
                "nearby_prefix",
            ],
        },
    },
    "signature_index": {
        "est_prefix_chars": 3840,
        "est_suffix_chars": 2304,
        "max_ctx_signature_chars": 3000,
        "sig_printer": {
            "show_full_method_signatures": True,
        },
    },
    "sig_prompt_formatter": {
        "max_middle_tks": 1024,
    },
}

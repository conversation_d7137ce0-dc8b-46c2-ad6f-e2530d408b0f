"""Configuration for roguesl system with 2k context, using nearby prefix."""

config = {
    "name": "rogue2_statelesscache",
    "checkpoint_path": "star2/star2_eth6_4m_morelang3_fpref1kretnpref0k5suf0k5_aug05rdrop03_fb",
    "model_parallel_size": 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "component_order": [
            "prefix",
            "retrieval",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
    },
}

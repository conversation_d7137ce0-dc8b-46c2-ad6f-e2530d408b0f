"""Configuration for elden system with 6k context, using nearby prefix."""

config = {
    "name": "elden_qwen_fb",
    "checkpoint_path": "qwencompletion/14b_elden_no_context_hindsight_fb",
    "model_parallel_size": 2,
    "seq_length": 6302,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

"""Configuration for roguesl plain system with 2k context, using nearby prefix."""

config = {
    "name": "rogue_statelesscache",
    "checkpoint_path": "roguesl/16b_eth61m_plain_scopepath",
    "prompt": {
        "max_prefix_tokens": 1280,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "max_scope_path_tokens": 100,
        "component_order": [
            "retrieval",
            "prefix",
            "suffix",
        ],
        "context_quant_token_len": 0,
        "nearby_prefix_token_len": 0,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": False,
    },
}

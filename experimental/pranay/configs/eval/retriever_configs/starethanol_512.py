"""Configuration for the starethanol6 version 16 retriever."""

config = {
    "scorer": {
        "name": "starcoder_1b",
        "checkpoint_path": "star_ethanol/starethanol6_17.1_linear_doc_proj_512_1875",
        "additional_yaml_files": [
            "/home/<USER>/augment/experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml"
        ],
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 30,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "max_tokens": 1023,
        "add_path": True,
        "add_suffix": True,
        "prefix_ratio": 0.9,
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "max_tokens": 999,
        "add_path": True,
    },
}

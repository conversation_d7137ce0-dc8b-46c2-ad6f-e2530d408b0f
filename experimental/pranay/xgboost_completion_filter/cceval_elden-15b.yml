# Usages:
# python research/eval/eval.py experimental/pranay/xgboost_completion_filter/cceval_elden-15b.yml

system:
    name: remote_completion

    # The model name is optional, will select default if not present
    model_name: eldenv4-3-15b

    retriever:
        wait_indexing_retry_count: 256
    #     disable_wait_indexing: true

    # completion:
    #     warn_on_unknown_blobs: true

    client:
        # Set a url to run the remote system.
        # url: https://dev-<USER>.us-central.api.augmentcode.com
        url: https://dev-pranay.us-central.api.augmentcode.com

        # If not running on determined, the client searchs for the API token
        # in $AUGMENT_TOKEN or ~/.config/augment/api_token
        # To generate your own API token, see
        # https://www.notion.so/Runbook-How-to-generate-API-tokens-a7ede88059604149867f03c2cf6f434b
        # api_token_env_var: AUGMENT_TOKEN
        # api_token_path: ~/.config/augment/api_token

        # These control global retry settings for the client.
        # Likely something is wrong if you need to change these, but if your pods
        # are restarting often, you could try increasing retry_sleep or retry_count.
        # timeout: 60
        # retry_count: 2
        # retry_sleep: 0.1

task:
    name: cceval
    limit: 4000

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: CCEval, remote model, eldenv4-3-15b xgboost filter v2, 4000
  workspace: Dev
  project: pranay

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Notebook to prepare data for xgboost filter. \n", "* Step 2 takes a long time (few hours) to run"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from base.datasets import completion\n", "from base.datasets.completion import CompletionDatum\n", "from base.datasets import tenants\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from pathlib import Path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.core.data_paths import canonicalize_path\n", "from datetime import datetime\n", "from collections.abc import Iterable\n", "import zstandard as zstd\n", "import json\n", "\n", "TENANT_DOGFOOD = \"dogfood\"\n", "TENANT_DOGFOOD_SHARD = \"dogfood-shard\"\n", "\n", "START_DATE = datetime(2024, 7, 1).date()\n", "END_DATE = datetime(2024, 8, 30).date()\n", "BASE_DIR = Path(canonicalize_path(\"data/prism/\")) / f\"{START_DATE}_{END_DATE}_elden\"\n", "print(f\"Base Output Directory: {BASE_DIR}\")\n", "if not BASE_DIR.exists():\n", "    BASE_DIR.mkdir(parents=True, exist_ok=False)\n", "REQUEST_ID_CSV_PATH = BASE_DIR / \"completion_ids.csv\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from IPython.core.interactiveshell import InteractiveShell\n", "\n", "InteractiveShell.ast_node_interactivity = \"all\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Get completion ids from BigQuery and save to a file:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from base.datasets.gcp_creds import get_gcp_creds\n", "from google.cloud import bigquery\n", "import csv\n", "\n", "dogfood_project_id = tenants.get_tenant(TENANT_DOGFOOD_SHARD).project_id\n", "dogfood_shard_project_id = tenants.get_tenant(TENANT_DOGFOOD_SHARD).project_id\n", "assert dogfood_project_id == dogfood_shard_project_id\n", "project_id = dogfood_project_id\n", "\n", "gcp_creds, _ = get_gcp_creds()\n", "bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def get_query(start_date: str, end_date: str):\n", "    SQL_query = f\"\"\"\n", "    WITH \n", "    request AS (\n", "        SELECT \n", "            request_id, \n", "            tenant,\n", "            time,\n", "            JSON_VALUE(sanitized_json, \"$.model\") AS model\n", "        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_host_request`\n", "        WHERE time >= \"{start_date}\"\n", "        AND time <= \"{end_date}\"\n", "        AND (tenant = \"dogfood-shard\" OR tenant = \"dogfood\")\n", "    ),\n", "    response AS (\n", "        SELECT\n", "            request_id,\n", "            character_count,\n", "        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_host_response`\n", "        WHERE time >= \"{start_date}\"\n", "        AND time <= \"{end_date}\"\n", "        AND character_count >= 1\n", "    ),\n", "    metadata AS (\n", "        SELECT\n", "            request_id,\n", "            user_id,\n", "            user_agent\n", "        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata`\n", "        WHERE time >= \"{start_date}\"\n", "        AND time <= \"{end_date}\"\n", "    ),\n", "    resolution AS (\n", "        SELECT\n", "            request_id,\n", "            accepted\n", "        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_resolution`\n", "        WHERE time >= \"{start_date}\"\n", "        AND time <= \"{end_date}\"\n", "    )\n", "    SELECT \n", "        request.request_id,\n", "        MAX(request.tenant) AS tenant,\n", "        MAX(request.model) AS model,\n", "        MAX(request.time) AS time,\n", "        MAX(metadata.user_id) AS user_id,\n", "        MAX(metadata.user_agent) AS user_agent,\n", "        MAX(resolution.accepted) AS accepted\n", "    FROM request\n", "    JOIN response USING (request_id)\n", "    JOIN metadata USING (request_id)\n", "    JOIN resolution USING (request_id)\n", "    WHERE request.model LIKE '%elden%'\n", "    GROUP BY request.request_id\n", "    \"\"\"\n", "    return SQL_query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query_and_save_request_ids(\n", "    start_date: str, end_date: str, request_id_csv_path: Path\n", "):\n", "    # Get completion ids from BigQuery and save to a file:\n", "    query = get_query(start_date, end_date)\n", "    rows = bigquery_client.query_and_wait(query)\n", "    print(f\"Found {rows.total_rows or -1} rows for {start_date} to {end_date}\")\n", "\n", "    # Write to csv\n", "    schema = [field.name for field in rows.schema]\n", "    with open(request_id_csv_path, \"w\", newline=\"\") as csvfile:\n", "        writer = csv.writer(csvfile)\n", "        writer.writerow(schema)\n", "        for row in rows:\n", "            writer.writerow(row.values())"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["query_and_save_request_ids(\n", "    START_DATE.isoformat(), END_DATE.isoformat(), REQUEST_ID_CSV_PATH\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: \n", "* Retrieve completions from full export bigquery data for request IDs from Step 1\n", "* Save the data in .jsonl.zst format"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["completion_data = pd.read_csv(REQUEST_ID_CSV_PATH)\n", "completion_data[\"user_id\"] = completion_data[\"user_id\"].apply(lambda x: x.split(\"@\")[0])\n", "completion_data = completion_data.sort_values(by=[\"time\"])\n", "\n", "request_ids_all = completion_data[\"request_id\"].tolist()\n", "assert len(request_ids_all) == len(set(request_ids_all))\n", "print(f\"Found {len(request_ids_all)} request ids\")\n", "\n", "completion_data_dogfood = completion_data[completion_data[\"tenant\"] == \"dogfood\"]\n", "completion_data_dogfood_request_ids = completion_data_dogfood[\"request_id\"].tolist()\n", "\n", "completion_data_dogfood_shard = completion_data[\n", "    completion_data[\"tenant\"] == \"dogfood-shard\"\n", "]\n", "completion_data_dogfood_shard_request_ids = completion_data_dogfood_shard[\n", "    \"request_id\"\n", "].tolist()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve completions and save to .jsonl.zst file(s)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["def transform_completion_datum(\n", "    row: completion.CompletionDatum,\n", ") -> completion.CompletionDatum:\n", "    \"\"\"Transforms a completion datum to only include the necessary fields. Used to save space and time loading dataset.\"\"\"\n", "    return completion.CompletionDatum(\n", "        request_id=row.request_id,\n", "        user_id=row.user_id,\n", "        user_agent=row.user_agent,\n", "        request=completion.CompletionRequest(\n", "            prefix=row.request.prefix,\n", "            suffix=row.request.suffix,\n", "            path=row.request.path,\n", "            timestamp=row.request.timestamp,\n", "            position=row.request.position,\n", "            # The following is not used.\n", "            blob_names=[],\n", "            output_len=-1,\n", "        ),\n", "        response=completion.CompletionResponse(\n", "            text=row.response.text,\n", "            model=row.response.model,\n", "            timestamp=row.response.timestamp,\n", "            token_ids=row.response.token_ids,\n", "            token_log_probs=row.response.token_log_probs,\n", "            prompt_token_ids=row.response.prompt_token_ids,\n", "            tokens=row.response.tokens,\n", "            # The following is not used.\n", "            skipped_suffix=\"\",\n", "            suffix_replacement_text=\"\",\n", "            unknown_blob_names=[],\n", "            retrieved_chunks=[],\n", "            prompt_tokens=[],\n", "        ),\n", "        resolution=row.resolution,\n", "        feedback=None,\n", "    )"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def get_completions_data(\n", "    tenant: str, request_ids: list[str]\n", ") -> Iterable[CompletionDatum]:\n", "    completion_filters = CompletionDataset.Filters(\n", "        request_ids=request_ids,\n", "    )\n", "    completions = CompletionDataset.create(\n", "        tenant=tenants.get_tenant(tenant),\n", "        filters=completion_filters,\n", "        page_size=8192,\n", "    )\n", "    completions = completions.get_completions()\n", "    return completions"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def process_and_write_data(completions: Iterable[CompletionDatum], file_name: str):\n", "    with zstd.open(BASE_DIR / file_name, \"w\", encoding=\"utf-8\") as f:\n", "        for completion in completions:\n", "            # We convert to json and then dict so that datetime objects are serialized.\n", "            datum_json = transform_completion_datum(completion).to_json()\n", "            datum_dict = json.loads(datum_json)\n", "            json.dump(datum_dict, f)\n", "            f.write(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get dogfood completions from bigquery\n", "dogfood_completions = get_completions_data(\n", "    TENANT_DOGFOOD, completion_data_dogfood_request_ids\n", ")\n", "\n", "# Process and write dogfood data to file\n", "process_and_write_data(dogfood_completions, \"data_dogfood.jsonl.zst\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read dogfood-shard data from bigquery, process data and write to file. Split into 4 parts to reduce memory usage.\n", "dogfood_shard_completions = get_completions_data(\n", "    TENANT_DOGFOOD_SHARD, completion_data_dogfood_shard_request_ids\n", ")\n", "\n", "# Process and write dogfood-shard data to file\n", "process_and_write_data(dogfood_shard_completions, \"data_dogfood_shard.jsonl.zst\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Verify data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import itertools\n", "\n", "# the .jsonl.zst files to read from\n", "files = [\n", "    \"data_dogfood\",\n", "    \"data_dogfood_shard\",\n", "]\n", "\n", "JULY_BASE_DIR = (\n", "    Path(canonicalize_path(\"data/prism/\"))\n", "    / f\"{datetime(2024, 7, 1).date()}_{datetime(2024, 7, 31).date()}_elden\"\n", ")\n", "AUGUST_BASE_DIR = (\n", "    Path(canonicalize_path(\"data/prism/\"))\n", "    / f\"{datetime(2024, 8, 1).date()}_{datetime(2024, 8, 31).date()}_elden\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["results = []\n", "for base_dir, file in itertools.product([JULY_BASE_DIR, AUGUST_BASE_DIR], files):\n", "    json_file = f\"{base_dir / file}.jsonl.zst\"\n", "    print(f\"Reading {json_file}\")\n", "    json_data = read_jsonl_zst(Path(json_file))\n", "    print(f\"Found {len(json_data)} elden completions in {file}.\")\n", "    results.extend(json_data)\n", "\n", "print(f\"Found {len(results)} total elden completions.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unique_ids = set(r[\"request_id\"] for r in results)\n", "assert len(unique_ids) == len(results)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from pandas import Series\n", "from pathlib import Path\n", "import xgboost as xgb\n", "from datetime import datetime\n", "from collections import defaultdict\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "from sklearn.model_selection import BaseCrossValidator\n", "import random\n", "import itertools\n", "import json\n", "import shutil\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    FeatureExtractorFactory,\n", ")\n", "from research.core.data_paths import canonicalize_path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "\n", "from tabulate import tabulate\n", "\n", "\n", "SEED = 42\n", "random.seed(SEED)\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "\n", "JULY_BASE_DIR = (\n", "    Path(canonicalize_path(\"data/prism/completion\"))\n", "    / f\"{datetime(2024, 7, 1).date()}_{datetime(2024, 7, 31).date()}_elden\"\n", ")\n", "print(f\"July base directory: {JULY_BASE_DIR}\")\n", "assert JULY_BASE_DIR.exists()\n", "\n", "AUGUST_BASE_DIR = (\n", "    Path(canonicalize_path(\"data/prism/completion\"))\n", "    / f\"{datetime(2024, 8, 1).date()}_{datetime(2024, 8, 31).date()}_elden\"\n", ")\n", "print(f\"August base directory: {AUGUST_BASE_DIR}\")\n", "assert AUGUST_BASE_DIR.exists()\n", "\n", "# Define the base directory for the data\n", "BASE_DIR = [JULY_BASE_DIR, AUGUST_BASE_DIR]\n", "\n", "# A previous checkpoint to use as a baseline for comparison.\n", "PREV_CKPT_PATH = \"/home/<USER>/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\"\n", "PREV_XGBOOST_MODEL = xgb.Booster(model_file=PREV_CKPT_PATH)\n", "PREV_XGBOOST_THRESHOLD = 0.8\n", "\n", "# the .jsonl.zst files to read from\n", "FILES = [\n", "    \"data_dogfood\",\n", "    \"data_dogfood_shard\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load data and create XGBoostFeatureDataSet"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["elden_json_data = []\n", "for base_dir, file in itertools.product(BASE_DIR, FILES):\n", "    json_file = f\"{base_dir / file}.jsonl.zst\"\n", "    print(f\"Reading {json_file}\")\n", "    json_data = read_jsonl_zst(Path(json_file))\n", "    print(f\"Found {len(json_data)} completions in {file}.\")\n", "    elden_json_data.extend(json_data)\n", "\n", "elden_json_data = [d for d in elden_json_data if d[\"resolution\"] is not None]\n", "assert all([\"elden\" in d[\"response\"][\"model\"] for d in elden_json_data])\n", "assert len(elden_json_data) == len(set([d[\"request_id\"] for d in elden_json_data]))\n", "\n", "for data in elden_json_data:\n", "    data[\"request\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"request\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data[\"response\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"response\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data[\"resolution\"][\"timestamp\"] = datetime.fromtimestamp(\n", "        data[\"resolution\"][\"timestamp\"]\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "elden_json_data = sorted(elden_json_data, key=lambda x: x[\"request\"][\"timestamp\"])\n", "\n", "print(f\"Found {len(elden_json_data)} elden completions.\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class XGBoostFeatureDataSet:\n", "    def __init__(self, data: list[dict], version: str = \"feature_extractor_v2\"):\n", "        \"\"\"\n", "        data: list of dicts, each dict is a completion datum\n", "        \"\"\"\n", "        self.data = data\n", "        self.feature_extractor = FeatureExtractorFactory.create(version)\n", "        self.features = []\n", "        self.metadata = []\n", "        for d in self.data:\n", "            features, metadata = self.extract_features(d), {}\n", "            self.features.append(features)\n", "            self.metadata.append(metadata)\n", "        self.feature_names = list(self.features[0].keys())\n", "\n", "        # Combine original data and extracted features\n", "        combined_data = []\n", "        for original, features, metadata in zip(\n", "            self.data, self.features, self.metadata\n", "        ):\n", "            combined_row = original.copy()  # Start with the original data\n", "            combined_row.update(features)  # Add the extracted features\n", "            combined_row[\"metadata\"] = metadata\n", "            combined_data.append(combined_row)\n", "\n", "        # Create DataFrame with combined data\n", "        self.df = pd.DataFrame(combined_data)\n", "\n", "        self.combined_data_dict = self.df.to_dict(orient=\"records\")\n", "\n", "    def extract_features(self, c: dict):\n", "        if type(c) == dict:\n", "            return self.feature_extractor.extract_completion_filter_features(\n", "                c[\"response\"][\"token_ids\"],\n", "                c[\"response\"][\"token_log_probs\"],\n", "                TOKENIZER,\n", "                c[\"request\"][\"prefix\"],\n", "                c[\"request\"][\"path\"],\n", "            )\n", "        else:\n", "            raise ValueError(f\"Unknown type: {type(c)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["xgboost_dataset = XGBoostFeatureDataSet(elden_json_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train model"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class MultiDateBasedSplit(BaseCrossValidator):\n", "    def __init__(self, test_ranges, new_model_per_eval=False):\n", "        \"\"\"\n", "        test_ranges: list of tuples, each tuple contains (start_date, end_date) for a test set\n", "        new_model_per_eval: bool, if True, train a new model for each eval\n", "        \"\"\"\n", "        self.test_ranges = [\n", "            (datetime.fromisoformat(start), datetime.fromisoformat(end))\n", "            for start, end in test_ranges\n", "        ]\n", "        self.new_model_per_eval = new_model_per_eval\n", "\n", "    def split(self, X, y=None, groups=None):\n", "        dates: pd.Series = X[\"request\"].apply(  # type: ignore\n", "            lambda x: datetime.fromisoformat(x[\"timestamp\"])\n", "        )\n", "\n", "        if self.new_model_per_eval:\n", "            for start, end in self.test_ranges:\n", "                test_mask = (dates >= start) & (dates <= end)\n", "                test_indices = np.where(test_mask)[0]\n", "                train_indices = np.where(~test_mask)[0]\n", "                yield train_indices, test_indices\n", "        else:\n", "            no_train_mask = Series(np.zeros(len(dates), dtype=bool))\n", "            for start, end in self.test_ranges:\n", "                no_train_mask = no_train_mask | ((dates >= start) & (dates <= end))\n", "\n", "            for start, end in self.test_ranges:\n", "                test_mask = (dates >= start) & (dates <= end)\n", "                test_indices = np.where(test_mask)[0]\n", "                train_indices = np.where(~no_train_mask)[0]\n", "                yield train_indices, test_indices\n", "\n", "    def get_n_splits(self, X=None, y=None, groups=None):\n", "        return len(self.test_ranges)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["class CompletionDatasetManager:\n", "    def __init__(self, train_data: list[dict], test_data: list[dict], downsample=False):\n", "        self.train_data: list[dict] = train_data\n", "        self.test_data: list[dict] = test_data\n", "\n", "        # Assert that all test_data request ids are not in train_data\n", "        train_data_request_ids = set([d[\"request_id\"] for d in self.train_data])\n", "        for d in self.test_data:\n", "            assert d[\"request_id\"] not in train_data_request_ids\n", "\n", "        self.downsample: bool = downsample\n", "\n", "        self.recognized_categories = [\n", "            \"any_completion_line_matches_last_prefix_line\",\n", "            \"first_long_non_print_line_matches_recent_prefix\",\n", "            \"is_comment\",\n", "            \"is_py\",\n", "            \"is_ipynb\",\n", "            \"is_ts\",\n", "            \"is_tsx\",\n", "            \"is_go\",\n", "            \"is_rs\",\n", "            \"is_proto\",\n", "            \"is_yaml\",\n", "            \"is_json\",\n", "            \"is_java\",\n", "            \"is_misc\",\n", "            \"completion_geq_1_duplicate_line\",\n", "            \"completion_geq_4_duplicate_line\",\n", "        ]\n", "\n", "    def filter_train_data(self):\n", "        self.filtered_train_data = defaultdict(list)\n", "        self.filtered_train_data[\"all\"] = []\n", "\n", "        for d in self.train_data:\n", "            categories = self.get_categories(d)\n", "            keep_sample = self.keep_sample(categories)\n", "            if not self.downsample or keep_sample:\n", "                for category in categories:\n", "                    self.filtered_train_data[category].append(d)\n", "                self.filtered_train_data[\"all\"].append(d)\n", "\n", "        self.filtered_train_data_size = sum(\n", "            map(len, self.filtered_train_data.values())\n", "        ) - len(self.filtered_train_data[\"all\"])\n", "        self.filtered_train_data_sizes_by_type = {\n", "            k: len(v) for k, v in self.filtered_train_data.items()\n", "        }\n", "        self.filtered_train_data_sizes_by_type = dict(\n", "            sorted(\n", "                self.filtered_train_data_sizes_by_type.items(),\n", "                key=lambda item: item[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "\n", "        # print(f\"train data original->filtered size: {len(self.train_data)}->{self.filtered_train_data_size}\")\n", "        print(f\"train data size by types: {self.filtered_train_data_sizes_by_type}\")\n", "\n", "    def keep_sample(self, category):\n", "        if category == \"is_comment\":\n", "            return random.random() < 0.1\n", "        if category == \"is_py\":\n", "            return random.random() < 0.1\n", "\n", "        return True\n", "\n", "    def to_dmatrix(self, data: list[dict], Y: list, features) -> xgb.DMatrix:\n", "        \"\"\"Converts a list of dicts into an XGBoost DMatrix.\"\"\"\n", "        assert len(data) == len(Y)\n", "        X = []\n", "        for d in data:\n", "            cache = []\n", "            for key in features:\n", "                cache.append(d[key])\n", "            X.append(cache)\n", "\n", "        return xgb.DMatrix(\n", "            X,\n", "            label=Y,\n", "            feature_names=features,\n", "        )\n", "\n", "    def filter_test_data(self):\n", "        self.filtered_test_data = defaultdict(list)\n", "\n", "        for d in self.test_data:\n", "            self.filtered_test_data[\"all\"].append(d)\n", "            categories = self.get_categories(d)\n", "\n", "            for category in categories:\n", "                self.filtered_test_data[category].append(d)\n", "\n", "        self.filtered_test_data_size = sum(\n", "            map(len, self.filtered_test_data.values())\n", "        ) - len(self.filtered_test_data[\"all\"])\n", "        self.filtered_test_data_sizes_by_type = {\n", "            k: len(v) for k, v in self.filtered_test_data.items()\n", "        }\n", "        self.filtered_test_data_sizes_by_type = dict(\n", "            sorted(\n", "                self.filtered_test_data_sizes_by_type.items(),\n", "                key=lambda item: item[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "\n", "        # print(f\"test data original->filtered size: {len(self.test_data)}->{self.filtered_test_data_size}\")\n", "        print(f\"test data size by types: {self.filtered_test_data_sizes_by_type}\")\n", "\n", "    def get_categories(self, d: dict):\n", "        categories = []\n", "\n", "        for category in self.recognized_categories:\n", "            if d[category]:\n", "                categories.append(category)\n", "\n", "        return categories\n", "\n", "    def train_model(self, features):\n", "        X_train = self.filtered_train_data[\"all\"]\n", "        y_train = [0 if d[\"resolution\"][\"accepted\"] else 1 for d in X_train]\n", "        dtrain = self.to_dmatrix(X_train, y_train, features)\n", "        model = xgb.train(\n", "            dict(\n", "                max_depth=8,\n", "                eta=0.005,\n", "                objective=\"binary:logistic\",\n", "                random_state=0,\n", "                alpha=2,\n", "                gamma=0.1,  # Minimum loss reduction for split\n", "            ),\n", "            dtrain,\n", "            num_boost_round=1000,\n", "            verbose_eval=False,\n", "        )\n", "        self.model = model\n", "        return model\n", "\n", "    def eval_model(\n", "        self,\n", "        model: xgb.<PERSON><PERSON>,\n", "        verbose=False,\n", "        save_to_self=False,\n", "        target_threshold=None,\n", "    ):\n", "        feature_names = model.feature_names\n", "        assert feature_names\n", "\n", "        eval_results: dict[str, tuple[float | None, int]] = {\n", "            k: (None, 0) for k in self.recognized_categories\n", "        }\n", "\n", "        for type, data in self.filtered_test_data.items():\n", "            if verbose:\n", "                print(f\"Evaluating {type} with {len(data)} data...\")\n", "            X_val = data\n", "            y_val = [0 if d[\"resolution\"][\"accepted\"] else 1 for d in data]\n", "            deval = self.to_dmatrix(X_val, y_val, list(feature_names))\n", "            labels = deval.get_label()\n", "            bst_predict = model.predict(deval)\n", "            try:\n", "                score = float(roc_auc_score(labels, bst_predict))\n", "                eval_results[type] = (score, len(data))\n", "                if verbose:\n", "                    print(\"Eval AUCROC\", score)\n", "            except Exception:\n", "                eval_results[type] = (None, len(data))\n", "\n", "            if type == \"all\":\n", "                fpr, tpr, thresholds = roc_curve(labels, bst_predict)\n", "                if save_to_self:\n", "                    self.fpr, self.tpr, self.thresholds = fpr, tpr, thresholds\n", "                if target_threshold is not None:\n", "\n", "                    def get_fpr_tpr_at_threshold(target_threshold):\n", "                        idx = np.argmin(np.abs(thresholds - target_threshold))\n", "                        if target_threshold <= thresholds[idx]:\n", "                            idx += 1\n", "                        return idx\n", "\n", "                    idx = get_fpr_tpr_at_threshold(target_threshold)\n", "                    fpr_p, tpr_p, thresholds_p = fpr[idx], tpr[idx], thresholds[idx]\n", "                    fpr_n, tpr_n, thresholds_n = (\n", "                        fpr[idx - 1],\n", "                        tpr[idx - 1],\n", "                        thresholds[idx - 1],\n", "                    )\n", "                    if verbose:\n", "                        print(f\"{thresholds_p:.4f} -> TPR={tpr_p:.4f}, FPR={fpr_p:.4f}\")\n", "                        print(f\"{thresholds_n:.4f} -> TPR={tpr_n:.4f}, FPR={fpr_n:.4f}\")\n", "                        print()\n", "\n", "        return eval_results"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def train_and_eval_models(\n", "    xgboost_dataset: XGBoostFeatureDataSet,\n", "    cv: MultiDateBasedSplit,\n", "    verbose: int = 0,\n", "    downsample=False,\n", "    new_model_threshold=0.86,\n", "    features_to_use=[],\n", "):\n", "    \"\"\"\n", "    xgboost_dataset: XGBoostFeatureDataSet - dataset to use\n", "    cv: BaseCrossValidator - cross validator to use\n", "    verbose: int - whether to print out the results, 0-2 (2 is the most verbose)\n", "    downsample: bool - whether to downsample the data\n", "    features_to_use: list[str] - list of features to use. If empty, use all features defined in xgboost_dataset\n", "\n", "    Returns:\n", "        all_results: list[tuple[dict[str, tuple[float | None, int]], dict[str, tuple[float | None, int]]]]\n", "        completion_dataset_managers: CompletionDatasetManager - list of CompletionDatasetManager objects\n", "    \"\"\"\n", "    all_results = []\n", "    completion_dataset_managers = []\n", "    xgboost_combined_data_dict = xgboost_dataset.combined_data_dict\n", "\n", "    if not features_to_use:\n", "        features_to_use = xgboost_dataset.feature_names\n", "    print(f\"Using features: {features_to_use}\\n\")\n", "\n", "    model = None\n", "\n", "    if cv.new_model_per_eval:\n", "        print(\"Training new model for each eval\")\n", "    else:\n", "        print(\"Training one model for all evals\")\n", "\n", "    for train_index, test_index in cv.split(xgboost_dataset.df):\n", "        train_data = [xgboost_combined_data_dict[i] for i in train_index]\n", "        test_data = [xgboost_combined_data_dict[i] for i in test_index]\n", "\n", "        completion_dataset = CompletionDatasetManager(\n", "            train_data, test_data, downsample=downsample\n", "        )\n", "        completion_dataset.filter_train_data()\n", "        completion_dataset.filter_test_data()\n", "\n", "        if cv.new_model_per_eval:\n", "            model = completion_dataset.train_model(features_to_use)\n", "        else:\n", "            if model is None:\n", "                model = completion_dataset.train_model(features_to_use)\n", "            assert completion_dataset.train_data == train_data\n", "\n", "        if verbose:\n", "            print(\"\\nEvaluating on previous model:\")\n", "        old_model_eval_results = completion_dataset.eval_model(\n", "            PREV_XGBOOST_MODEL,\n", "            verbose == 2,\n", "            save_to_self=False,\n", "            target_threshold=PREV_XGBOOST_THRESHOLD,\n", "        )\n", "\n", "        if verbose:\n", "            print(\"\\nEvaluating on new model:\")\n", "        new_model_eval_results = completion_dataset.eval_model(\n", "            model, verbose == 2, save_to_self=True, target_threshold=new_model_threshold\n", "        )\n", "\n", "        keys = set(new_model_eval_results.keys()).intersection(\n", "            set(old_model_eval_results.keys())\n", "        )\n", "        old_model_eval_results = {\n", "            k: v for k, v in old_model_eval_results.items() if k in keys\n", "        }\n", "        new_model_eval_results = {\n", "            k: v for k, v in new_model_eval_results.items() if k in keys\n", "        }\n", "\n", "        all_results.append((old_model_eval_results, new_model_eval_results))\n", "        completion_dataset_managers.append(completion_dataset)\n", "\n", "        print()\n", "\n", "    return all_results, completion_dataset_managers"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# multi_date_cv = MultiDateBasedSplit(\n", "#     [\n", "#         (\"2024-07-01\", \"2024-07-06\"),\n", "#         (\"2024-08-01\", \"2024-08-06\"),\n", "#         (\"2024-08-25\", \"2024-08-31\"),\n", "#     ],\n", "#     new_model_per_eval=True,\n", "# )"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["multi_date_cv = MultiDateBasedSplit(\n", "    [\n", "        (\"2024-07-01\", \"2024-07-02\"),\n", "        (\"2024-08-01\", \"2024-08-02\"),\n", "        (\"2024-08-30\", \"2024-08-31\"),\n", "    ],\n", "    new_model_per_eval=False,\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# base_features = [\"token_prob_min\", \"token_prob_median\", \"token_prob_max\", \"leading_average_1\", \"leading_average_3\", \"leading_average_5\", \"moving_average_min\", \"moving_average_median\", \"moving_average_max\", \"mean\", \"num_lines\", \"token_diversity\"]\n", "\n", "eval_results, completion_dataset_managers = train_and_eval_models(\n", "    xgboost_dataset,\n", "    multi_date_cv,\n", "    verbose=1,\n", "    new_model_threshold=0.875,\n", "    downsample=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Output Results"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class EvalTypeInfo:\n", "    def __init__(self, category, new_score, old_score, size, test_range):\n", "        self.category = category\n", "        self.new_score = None\n", "        self.old_score = None\n", "        self.improvement = None\n", "        if new_score:\n", "            self.new_score = round(new_score, 4)\n", "        if old_score:\n", "            self.old_score = round(old_score, 4)\n", "        if new_score and old_score:\n", "            self.improvement = round(new_score - old_score, 4)\n", "        self.size = size\n", "        self.start_date = test_range[0].date()\n", "        self.end_date = test_range[1].date()\n", "\n", "\n", "class ExtractedEvalResults:\n", "    def __init__(self, all_results, completion_dataset_managers, test_ranges):\n", "        self.all_results = all_results\n", "        self.completion_dataset_managers = completion_dataset_managers\n", "        self.test_ranges = test_ranges\n", "        self.eval_type_info = defaultdict(list)\n", "\n", "        for (old_model_eval_results, new_model_eval_results), test_range in zip(\n", "            all_results, test_ranges\n", "        ):\n", "            categories = list(new_model_eval_results.keys())\n", "            for category in categories:\n", "                new_score, new_size = new_model_eval_results[category]\n", "                old_score, old_size = old_model_eval_results[category]\n", "                assert new_size == old_size\n", "                eval_type_info = EvalTypeInfo(\n", "                    category, new_score, old_score, new_size, test_range\n", "                )\n", "                self.eval_type_info[category].append(eval_type_info)\n", "\n", "        self.categories = list(self.eval_type_info.keys())\n", "        self.categories.sort()\n", "\n", "        self.matrix_overall = self.create_matrix(\n", "            \"Overall AUC\", self.create_model_auc_rows\n", "        )\n", "        self.matrix_category = self.create_matrix(\n", "            \"Filter Category AUC Improvement (Old->New Model)\",\n", "            self.create_category_rows,\n", "        )\n", "\n", "    def create_matrix(self, header_name: str, create_rows_fn):\n", "        header = self.create_header(header_name)\n", "        data = create_rows_fn()\n", "        return tabulate(data, headers=header, tablefmt=\"grid\")\n", "\n", "    def create_header(self, header_name: str):\n", "        header = [header_name]\n", "        for start_date, end_date in self.test_ranges:\n", "            header.append(f\"{start_date.date()} - {end_date.date()}\")\n", "        header.append(\"Average\")\n", "        return header\n", "\n", "    def create_model_auc_rows(self):\n", "        return [\n", "            self.create_row(\"New Model AUC\", \"all\", lambda info: info.new_score),\n", "            self.create_row(\"Old Model AUC\", \"all\", lambda info: info.old_score),\n", "            self.create_row(\"AUC Improvement\", \"all\", lambda info: info.improvement),\n", "        ]\n", "\n", "    def create_category_rows(self):\n", "        return [\n", "            self.create_row(category, category, lambda info: info.improvement)\n", "            for category in self.categories\n", "            if category != \"all\"\n", "        ]\n", "\n", "    def create_row(self, row_name, data_key, value_getter):\n", "        row = [row_name]\n", "        sum_value, sum_size, count = 0, 0, 0\n", "\n", "        for eval_info in self.eval_type_info[data_key]:\n", "            value = value_getter(eval_info)\n", "            if value is not None:\n", "                row.append(f\"{value:.4f} ({eval_info.size})\")\n", "                sum_value += value\n", "                sum_size += eval_info.size\n", "                count += 1\n", "            else:\n", "                row.append(\"N/A\")\n", "\n", "        row.append(self.calculate_average(sum_value, sum_size, count))\n", "        return row\n", "\n", "    @staticmethod\n", "    def calculate_average(sum_value, sum_size, count):\n", "        if count > 0:\n", "            avg_value = sum_value / count\n", "            avg_size = sum_size / count\n", "            return f\"{avg_value:.4f} ({avg_size:.0f})\"\n", "        return \"N/A\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["extracted_eval_results = ExtractedEvalResults(\n", "    eval_results, completion_dataset_managers, multi_date_cv.test_ranges\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["print(extracted_eval_results.matrix_overall)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["print(extracted_eval_results.matrix_category)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def create_model_info_file(\n", "    ckpt_dir: Path, info_dir: Path, extracted_eval_results, completion_dataset_managers\n", "):\n", "    model: xgb.Booster = completion_dataset_managers[0].model\n", "    model_features = model.feature_names\n", "    assert model_features\n", "\n", "    # save model\n", "    model_file = ckpt_dir / \"model.json\"\n", "    model.save_model(model_file)\n", "\n", "    info_file = ckpt_dir / \"model_info.txt\"\n", "    with info_file.open(\"w\", encoding=\"utf-8\") as f:\n", "        # General Information\n", "        f.write(\"Model Information\\n\")\n", "        f.write(\"=================\\n\\n\")\n", "\n", "        # Data Source Information\n", "        f.write(\"Data Source Information\\n\")\n", "        f.write(\"------------------------\\n\")\n", "        f.write(\"Base directories:\\n\")\n", "        for base_dir in BASE_DIR:\n", "            f.write(f\"  - {str(base_dir)}\\n\")\n", "        f.write(\"Data files:\\n\")\n", "        for file in FILES:\n", "            f.write(f\"  - {file}.jsonl.zst\\n\")\n", "        f.write(\"\\n\")\n", "\n", "        # Features\n", "        f.write(\"Model Features\\n\")\n", "        f.write(\"--------------\\n\")\n", "        f.write(f\"Features: {', '.join(model_features)}\\n\\n\")\n", "\n", "        # Train and Eval Data Information\n", "        f.write(\"Data Information\\n\")\n", "        f.write(\"-----------------\\n\")\n", "        for i, (completion_dataset_manager, test_range) in enumerate(\n", "            zip(completion_dataset_managers, extracted_eval_results.test_ranges)\n", "        ):\n", "            f.write(f\"Eval Set {i+1}:\\n\")\n", "            f.write(\n", "                f\"  Train Data: {len(completion_dataset_manager.train_data)} samples\\n\"\n", "            )\n", "            f.write(\n", "                f\"  Test Data: {len(completion_dataset_manager.test_data)} samples\\n\"\n", "            )\n", "            f.write(\n", "                f\"  Eval Date Range (Inclusive): {test_range[0].date()} to {test_range[1].date()}\\n\\n\"\n", "            )\n", "\n", "        # Matrix Table\n", "        f.write(\"Evaluation Results\\n\")\n", "        f.write(\"-------------------------\\n\")\n", "        f.write(f\"New model checkpoint: {model_file}\\n\")\n", "        f.write(f\"Old model checkpoint: {PREV_CKPT_PATH}\\n\\n\")\n", "        f.write('Format of each cell is \"EVAL_AUC (EVAL_SIZE)\"\\n')\n", "        f.write(extracted_eval_results.matrix_overall)\n", "        f.write(\"\\n\\n\")\n", "        f.write(\n", "            \"Each category below represents a subset of the evaluation data where a specific feature is present.\\n\"\n", "        )\n", "        f.write(\n", "            \"This analysis helps identify the new model's performance on samples with particular characteristics.\\n\"\n", "        )\n", "        f.write(extracted_eval_results.matrix_category)\n", "        f.write(\"\\n\\n\")\n", "\n", "        # TP/FP/Threshold Tables\n", "        f.write(\"True Positive / False Positive / Threshold Tables\\n\")\n", "        f.write(\"------------------------------------------------\\n\")\n", "        for i, completion_dataset_manager in enumerate(completion_dataset_managers):\n", "            f.write(f\"Eval Set {i+1}:\\n\")\n", "            tpr, fpr, thresholds = (\n", "                completion_dataset_manager.tpr,\n", "                completion_dataset_manager.fpr,\n", "                completion_dataset_manager.thresholds,\n", "            )\n", "\n", "            table_data = []\n", "            prev = -1\n", "            for curr_tpr, curr_fpr, curr_threshold in zip(tpr, fpr, thresholds):\n", "                if (\n", "                    curr_tpr - prev > 0.01 and curr_fpr < 0.1\n", "                ) or curr_tpr - prev > 0.03:\n", "                    prev = curr_tpr\n", "                    table_data.append(\n", "                        [f\"{curr_tpr:.3f}\", f\"{curr_fpr:.3f}\", f\"{curr_threshold:.3f}\"]\n", "                    )\n", "\n", "            headers = [\"True Positive\", \"False Positive\", \"Threshold\"]\n", "            table = tabulate(table_data, headers=headers, tablefmt=\"grid\")\n", "            f.write(table)\n", "            f.write(\"\\n\\n\")\n", "\n", "        # Feature Importance\n", "        f.write(\"Feature Importance\\n\")\n", "        f.write(\"------------------\\n\")\n", "        importance_scores = model.get_score(importance_type=\"gain\")\n", "        importance_table = sorted(\n", "            importance_scores.items(), key=lambda x: x[1], reverse=True\n", "        )\n", "        headers = [\"Feature\", \"Importance Score\"]\n", "        f.write(tabulate(importance_table, headers=headers, tablefmt=\"grid\"))\n", "        f.write(\"\\n\\n\")\n", "\n", "        # Hyperparameters\n", "        f.write(\"Model Hyperparameters\\n\")\n", "        f.write(\"---------------------\\n\")\n", "        params = json.loads(model.save_config())\n", "        for param, value in params.items():\n", "            f.write(f\"{param}: {value}\\n\")\n", "        f.write(\"\\n\")\n", "\n", "        # Model File Location\n", "        f.write(\"Model File\\n\")\n", "        f.write(\"----------\\n\")\n", "        f.write(f\"Model saved at: {model_file}\\n\")\n", "        f.write(\"\\n\")\n", "\n", "        # Threshold Information\n", "        f.write(\"Threshold Information\\n\")\n", "        f.write(\"---------------------\\n\")\n", "        f.write(\"Threshold Value: TBD\\n\")\n", "\n", "    shutil.copy(info_file, info_dir)\n", "\n", "\n", "model_name = \"prism_eldenv3v4\"\n", "ckpt_dir = Path(canonicalize_path(\"checkpoints/prism/completion\")) / model_name\n", "info_dir = (\n", "    Path(\n", "        \"/home/<USER>/augment/services/completion_host/single_model_server/prism_models/\"\n", "    )\n", "    / model_name\n", ")\n", "\n", "print(f\"ckpt_dir: {ckpt_dir}\")\n", "print(f\"info_dir: {info_dir}\")\n", "\n", "ckpt_dir.mkdir(parents=True, exist_ok=True)\n", "info_dir.mkdir(parents=True, exist_ok=True)\n", "create_model_info_file(\n", "    ckpt_dir, info_dir, extracted_eval_results, completion_dataset_managers\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Random functions to use to understand the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def acceptance_rate_of_column(df, column):\n", "    filtered_df = df[df[column] == 1]\n", "    result = [\n", "        filtered_df.iloc[i][\"resolution\"][\"accepted\"] for i in range(len(filtered_df))\n", "    ]\n", "    print(\n", "        f\"Acceptance Rate: {sum(result)} / {len(result)} = {sum(result) / len(result)}\"\n", "    )"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["def acceptance_rate_of_dict(dict):\n", "    result = [dict[i][\"resolution\"][\"accepted\"] for i in range(len(dict))]\n", "    print(\n", "        f\"Acceptance Rate: {sum(result)} / {len(result)} = {sum(result) / len(result)}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_dict_multiline(dictionary, keys=None):\n", "    if keys is not None:\n", "        dictionary = {k: dictionary[k] for k in keys if k in dictionary}\n", "\n", "    for key, value in dictionary.items():\n", "        if isinstance(value, str):\n", "            print(f'\"{key}\":')\n", "            for line in value.split(\"\\n\"):\n", "                print(f\"    {line}\")\n", "        else:\n", "            print(f'\"{key}\": {value}')\n", "        print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
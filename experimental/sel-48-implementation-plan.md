# Implementation Plan for SEL-48: Handling Stripe SetupIntent Flow

## Overview
The task requires implementing the SetupIntent flow for Stripe integration with Orb, specifically:
1. Handling the `customer.created` Stripe webhook event
2. Completing the SetupIntent flow
3. Listening to the `setup_intent.succeeded` event to confirm it worked

## Current System Analysis
The current system has:
1. A Stripe webhook handler (`services/auth/stripe_webhook/handler.go`) that:
   - Receives webhook events from Stripe
   - Verifies the webhook signature
   - Parses events into structured `StripeEvent` protobuf messages
   - Publishes events to a PubSub topic

2. A Stripe event processor (`services/auth/central/server/stripe_event_processor.go`) that:
   - Subscribes to the PubSub topic
   - Processes events asynchronously
   - Currently handles subscription-related events (`customer.subscription.*`)

## Required Changes

### 1. Update the Stripe Event Proto Definition
We need to add a new event type for SetupIntent events in `services/auth/stripe_webhook/stripe_event.proto`:
- Add a new message type `SetupIntentEvent` to hold setup intent-specific data
- Add this new event type to the `oneof event` field in the `StripeEvent` message

### 2. Update the Webhook Handler
In `services/auth/stripe_webhook/handler.go`, we need to:
- Add handling for `setup_intent.succeeded` events in the `parseStripeEvent` method
- Parse the SetupIntent data from the Stripe event
- Populate the new `SetupIntentEvent` message

### 3. Update the Event Processor
In `services/auth/central/server/stripe_event_processor.go`, we need to:
- Add handling for SetupIntent events in the `processStripeEvent` method
- Create a new method `handleSetupIntentEvents` to process these events
- Implement specific handling for `setup_intent.succeeded` events

### 4. Implement Customer Payment Method Update
When a `setup_intent.succeeded` event is received:
- Find the user associated with the Stripe customer ID
- Update the user's payment method information
- Mark the user as having a valid payment method

## Detailed Implementation Steps

### 1. Update Stripe Event Proto Definition

Add a new message type for SetupIntent events in `services/auth/stripe_webhook/stripe_event.proto`:

```protobuf
// SetupIntentEvent contains setup-intent-specific data
// For events: setup_intent.succeeded, setup_intent.canceled, setup_intent.setup_failed
message SetupIntentEvent {
  string setup_intent_id = 1;
  string payment_method_id = 2;
  string status = 3;
  string customer_id = 4;
  bool livemode = 5;
  string usage = 6;  // "on_session" or "off_session"
}
```

Update the `StripeEvent` message to include the new event type:

```protobuf
message StripeEvent {
  // ... existing fields

  // Event-specific data
  oneof event {
    SubscriptionEvent subscription_event = 6;
    CustomerEvent customer_event = 7;
    InvoiceEvent invoice_event = 8;
    CheckoutEvent checkout_event = 9;
    SetupIntentEvent setup_intent_event = 10;  // New field
  }
}
```

### 2. Update the Webhook Handler

In `services/auth/stripe_webhook/handler.go`, update the `parseStripeEvent` method to handle SetupIntent events:

```go
// In the parseStripeEvent method, add a new case for setup_intent events
case "setup_intent.succeeded", "setup_intent.canceled", "setup_intent.setup_failed":
    var setupIntent stripe.SetupIntent
    if err := json.Unmarshal(event.Data.Raw, &setupIntent); err != nil {
        return nil, fmt.Errorf("error unmarshalling setup intent data: %w", err)
    }

    // Extract the customer ID
    if setupIntent.Customer != nil {
        stripeCustomerId := setupIntent.Customer.ID
        stripeEvent.StripeCustomerId = &stripeCustomerId
    }

    setupIntentEvent := &stripe_event.SetupIntentEvent{
        SetupIntentId:   setupIntent.ID,
        Status:          string(setupIntent.Status),
        Usage:           string(setupIntent.Usage),
        Livemode:        setupIntent.Livemode,
    }

    // Add payment method ID if available
    if setupIntent.PaymentMethod != nil {
        setupIntentEvent.PaymentMethodId = setupIntent.PaymentMethod.ID
    }

    // Add customer ID if available
    if setupIntent.Customer != nil {
        setupIntentEvent.CustomerId = setupIntent.Customer.ID
    }

    stripeEvent.Event = &stripe_event.StripeEvent_SetupIntentEvent{
        SetupIntentEvent: setupIntentEvent,
    }
```

### 3. Update the Event Processor

In `services/auth/central/server/stripe_event_processor.go`, update the `processStripeEvent` method to handle SetupIntent events:

```go
// In the processStripeEvent method, add a new case for setup_intent events
func (p *StripeEventProcessor) processStripeEvent(ctx context.Context, event *stripe_event.StripeEvent) error {
    log.Info().
        Str("event_type", event.EventType).
        Str("event_id", event.EventId).
        Msg("Processing Stripe event")

    // Handle different event types
    switch {
    case strings.HasPrefix(event.EventType, "customer.subscription."):
        return p.handleSubscriptionEvents(ctx, event)
    case strings.HasPrefix(event.EventType, "setup_intent."):
        return p.handleSetupIntentEvents(ctx, event)
    case strings.HasPrefix(event.EventType, "customer."):
        return p.handleCustomerEvents(ctx, event)
    default:
        log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled event type")
        return nil
    }
}
```

Add a new method to handle SetupIntent events:

```go
// handleSetupIntentEvents handles all setup-intent-related events
func (p *StripeEventProcessor) handleSetupIntentEvents(ctx context.Context, event *stripe_event.StripeEvent) error {
    setupIntentEvent := event.GetSetupIntentEvent()
    if setupIntentEvent == nil {
        return fmt.Errorf("setup intent event is nil")
    }

    if event.StripeCustomerId == nil {
        return fmt.Errorf("customer ID is nil")
    }

    log.Info().
        Str("event_type", event.EventType).
        Str("setup_intent_id", setupIntentEvent.SetupIntentId).
        Str("customer_id", *event.StripeCustomerId).
        Str("status", setupIntentEvent.Status).
        Msg("Processing setup intent event")

    switch event.EventType {
    case "setup_intent.succeeded":
        return p.handleSetupIntentSucceeded(ctx, setupIntentEvent, *event.StripeCustomerId)
    case "setup_intent.canceled":
        // Handle canceled setup intents if needed
        log.Info().Str("setup_intent_id", setupIntentEvent.SetupIntentId).Msg("Setup intent was canceled")
        return nil
    case "setup_intent.setup_failed":
        // Handle failed setup intents if needed
        log.Info().Str("setup_intent_id", setupIntentEvent.SetupIntentId).Msg("Setup intent failed")
        return nil
    default:
        log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled setup intent event type")
        return nil
    }
}
```

Implement the handler for `setup_intent.succeeded` events:

```go
// handleSetupIntentSucceeded handles setup_intent.succeeded events
func (p *StripeEventProcessor) handleSetupIntentSucceeded(ctx context.Context, setupIntentEvent *stripe_event.SetupIntentEvent, stripeCustomerID string) error {
    // Find the user with the matching Stripe customer ID
    userDAO := p.daoFactory.GetUserDAO()
    users, err := userDAO.FindAll(ctx)
    if err != nil {
        return fmt.Errorf("failed to find users: %v", err)
    }

    // Find the user with matching Stripe customer ID
    var user *auth_entities.User
    for _, u := range users {
        if u.StripeCustomerId == stripeCustomerID {
            user = u
            break
        }
    }

    if user == nil {
        log.Warn().Str("stripe_customer_id", stripeCustomerID).Msg("No user found with matching Stripe customer ID")
        return nil
    }

    // Update the user to indicate they have a valid payment method
    // This could be a new field in the User entity, or we could use an existing field
    // For now, we'll use TryUpdate to update the user
    _, err = userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
        // Update user fields as needed to indicate they have a valid payment method
        // This might involve setting a has_payment_method field or similar
        // For now, we'll just log that we would update the user
        log.Info().
            Str("user_id", user.Id).
            Str("setup_intent_id", setupIntentEvent.SetupIntentId).
            Str("payment_method_id", setupIntentEvent.PaymentMethodId).
            Msg("User payment method successfully set up")
        return true
    }, DefaultRetry)

    if err != nil {
        return fmt.Errorf("failed to update user: %v", err)
    }

    return nil
}
```

### 4. Add Customer Event Handling

We also need to implement handling for `customer.created` events:

```go
// Add a new method to handle customer events
func (p *StripeEventProcessor) handleCustomerEvents(ctx context.Context, event *stripe_event.StripeEvent) error {
    customerEvent := event.GetCustomerEvent()
    if customerEvent == nil {
        return fmt.Errorf("customer event is nil")
    }

    if event.StripeCustomerId == nil {
        return fmt.Errorf("customer ID is nil")
    }

    log.Info().
        Str("event_type", event.EventType).
        Str("customer_id", *event.StripeCustomerId).
        Msg("Processing customer event")

    switch event.EventType {
    case "customer.created":
        return p.handleCustomerCreated(ctx, customerEvent, *event.StripeCustomerId)
    case "customer.updated":
        // Handle customer updates if needed
        return nil
    case "customer.deleted":
        // Handle customer deletion if needed
        return nil
    default:
        log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled customer event type")
        return nil
    }
}

// handleCustomerCreated handles customer.created events
func (p *StripeEventProcessor) handleCustomerCreated(ctx context.Context, customerEvent *stripe_event.CustomerEvent, stripeCustomerID string) error {
    // Find the user with the matching Stripe customer ID
    userDAO := p.daoFactory.GetUserDAO()
    users, err := userDAO.FindAll(ctx)
    if err != nil {
        return fmt.Errorf("failed to find users: %v", err)
    }

    // Find the user with matching Stripe customer ID
    var user *auth_entities.User
    for _, u := range users {
        if u.StripeCustomerId == stripeCustomerID {
            user = u
            break
        }
    }

    if user == nil {
        log.Warn().Str("stripe_customer_id", stripeCustomerID).Msg("No user found with matching Stripe customer ID")
        return nil
    }

    // Log that we received a customer.created event for this user
    log.Info().
        Str("user_id", user.Id).
        Str("stripe_customer_id", stripeCustomerID).
        Msg("Received customer.created event for user")

    return nil
}
```

## Testing Plan

1. **Unit Tests**:
   - Add unit tests for the new event handling methods
   - Test parsing of SetupIntent events
   - Test the event processor's handling of SetupIntent events

2. **Integration Tests**:
   - Test the full flow from webhook receipt to event processing
   - Verify that user records are properly updated when SetupIntent events are received

3. **Manual Testing**:
   - Use the Stripe CLI to trigger test events
   - Verify that the events are properly processed
   - Check that user records are updated correctly

## Verification Steps

To verify the implementation works correctly:

1. Create a test customer in Stripe
2. Create a SetupIntent for that customer
3. Confirm the SetupIntent
4. Verify that the `setup_intent.succeeded` event is received and processed
5. Check that the user's payment method information is updated correctly

## Conclusion

This implementation plan addresses the requirements of SEL-48 by:
1. Adding support for handling `customer.created` Stripe webhook events
2. Implementing the SetupIntent flow
3. Adding handling for `setup_intent.succeeded` events to confirm the flow worked

The changes are focused on extending the existing Stripe webhook and event processing infrastructure to handle these new event types, while maintaining the current architecture and patterns.

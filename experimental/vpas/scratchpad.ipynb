{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = {\n", "    \"file_path\": \"experimental/lior/python_interpreter_agent/builtins.py\",\n", "    \"edit_summary\": \"Remove test function that was accidentally included in builtins.py\",\n", "    \"detailed_edit_description\": 'Remove the test function that was accidentally included at the end of the file:\\n\\n```\\ndef test_expert_agent_invalid_ref(git_repo):\\n    \"\"\"Test that ExpertAgent handles invalid git refs gracefully.\"\"\"\\n    with pytest.raises(Exception) as exc_info:\\n        ExpertAgent(ref=\"nonexistent_branch_12345\", workspace_root=git_repo)\\n    assert \"did not match any file(s) known to git\" in str(exc_info.value)\\n```\\n\\nThe file should end with the request_edit function.',\n", "}\n", "\n", "print(inputs[\"detailed_edit_description\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for line in inputs[\"detailed_edit_description\"].split(\"\\n\"):\n", "    if line and line[-1] == \" \":\n", "        print(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    '\\n\\nclass ContextRequestException(Exception):\\n    \"\"\"Raised when more context is needed to execute a command.\"\"\"\\n    pass\\n\\n\\ndef run_command(cmd: str) -> str:\\n    \"\"\"\\n    Run a shell command and return its output.\\n\\n    Args:\\n        cmd: The command to run\\n\\n    Returns:\\n        The command\\'s output as a string\\n    \"\"\"\\n    try:\\n        result = subprocess.run(\\n            cmd,\\n            shell=True,\\n            check=True,\\n            stdout=subprocess.PIPE,\\n            stderr=subprocess.PIPE,\\n            text=True,\\n            timeout=30,\\n        )\\n        return result.stdout\\n    except subprocess.CalledProcessError as e:\\n        error_output = e.stdout or e.stderr or str(e)\\n        return f\"Error: Command failed: {error_output}\"\\n    except subprocess.TimeoutExpired:\\n        return \"Error: Command timed out after 30 seconds\"\\n    except Exception as e:\\n        return f\"Error: Command failed: {str(e)}\"\\n\\n\\nclass ReadFileTool(Tool):\\n    \"\"\"Tool for reading files from the workspace.\"\"\"\\n    \\n    def __init__(self, workspace_manager, tool_logger):\\n        \"\"\"Initialize the tool.\\n        \\n        Args:\\n            workspace_manager: Workspace manager instance\\n            tool_logger: Tool logger instance\\n        \"\"\"\\n        super().__init__(tool_logger)\\n        self.workspace_manager = workspace_manager\\n\\n    def run_impl(self, tool_input: Dict[str, Any]) -> ToolImplOutput:\\n        \"\"\"Run the tool implementation.\\n        \\n        Args:\\n            tool_input: Tool input containing file path\\n            \\n        Returns:\\n            Tool output containing file contents\\n        \"\"\"\\n        file_path = tool_input[\"file_path\"]\\n        try:\\n            content = self.workspace_manager.read_file(file_path)\\n            return ToolImplOutput(tool_output=content)\\n        except Exception as e:\\n            return ToolImplOutput(tool_output=f\"Error reading file: {str(e)}\")\\n\\n\\nclass ExpertAgent:\\n    \"\"\"Agent that analyzes code at a specific git reference.\"\"\"\\n\\n    def __init__(self, ref: str):\\n        \"\"\"Initialize the agent with a git reference.\\n        \\n        Args:\\n            ref: Git reference (commit hash, branch name, PR number)\\n        \"\"\"\\n        # Create temp directory for workspace\\n        self.temp_dir = Path(tempfile.mkdtemp())\\n        \\n        # Clone repo and checkout ref\\n        subprocess.run(\\n            [\"git\", \"clone\", \".\", str(self.temp_dir)],\\n            check=True,\\n        )\\n        subprocess.run(\\n            [\"git\", \"checkout\", ref],\\n            cwd=str(self.temp_dir),\\n            check=True,\\n        )\\n\\n        # Setup workspace manager and tools\\n        self.tool_logger = ToolCallLogger()\\n        self.workspace_manager = WorkspaceManagerImpl(\\n            augment_client=AugmentPrototypingClient(),\\n            root=self.temp_dir,\\n        )\\n        \\n        # Initialize tools\\n        self.codebase_tool = CodebaseRetrievalTool(\\n            tool_logger=self.tool_logger,\\n            augment_client=AugmentPrototypingClient(),\\n            workspace_manager=self.workspace_manager,\\n            max_tool_chars=50000,\\n            max_retrieval_chunks=1000,\\n            max_result_chunks=20,\\n        )\\n        \\n        self.read_file_tool = ExternalReadFileTool(\\n            tool_logger=self.tool_logger,\\n            root=self.temp_dir,\\n        )\\n\\n        # Initialize LLM client\\n        self.client = get_client(\\n            \"anthropic-direct\", \\n            model_name=\"claude-3-5-sonnet-20241022\",\\n            max_retries=50\\n        )\\n\\n    def answer_question(self, question: str) -> str:\\n        \"\"\"Answer a question about the codebase.\\n        \\n        Args:\\n            question: The question to answer\\n            \\n        Returns:\\n            The answer from the expert\\n        \"\"\"\\n        # First retrieve relevant code\\n        retrieval_result = self.codebase_tool.run_impl(\\n            tool_input={\"code_section_requests\": [{\"description\": question}]}\\n        )\\n\\n        # Format messages for LLM\\n        messages = [\\n            [\\n                cast(GeneralContentBlock, TextPrompt(text=question)),\\n                cast(GeneralContentBlock, TextPrompt(text=f\"\\\\nRelevant code:\\\\n{retrieval_result.tool_output}\")),\\n            ]\\n        ]\\n\\n        # Define tools\\n        request_context_tool = ToolParam(\\n            name=\"request_context\",\\n            description=\"Request additional context from the user when you need more information to answer a question.\",\\n            input_schema={\\n                \"type\": \"object\",\\n                \"properties\": {\\n                    \"context_request\": {\\n                        \"type\": \"string\",\\n                        \"description\": \"Detailed and technical request for additional context or information.\",\\n                    },\\n                },\\n                \"required\": [\"context_request\"],\\n            },\\n        )\\n\\n        read_file_tool = ToolParam(\\n            name=\"read_file\",\\n            description=\"Read a file from the workspace.\",\\n            input_schema={\\n                \"type\": \"object\",\\n                \"properties\": {\\n                    \"file_path\": {\\n                        \"type\": \"string\",\\n                        \"description\": \"Path to the file to read.\",\\n                    },\\n                },\\n                \"required\": [\"file_path\"],\\n            },\\n        )\\n\\n        codebase_tool_param = ToolParam(\\n            name=\"search_code\",\\n            description=\"Search the codebase for relevant code sections.\",\\n            input_schema={\\n                \"type\": \"object\",\\n                \"properties\": {\\n                    \"description\": {\\n                        \"type\": \"string\",\\n                        \"description\": \"Description of the code sections to search for.\",\\n                    },\\n                },\\n                \"required\": [\"description\"],\\n            },\\n        )\\n\\n        # Generate response with tools available\\n        response, _ = self.client.generate(\\n            messages=messages,\\n            max_tokens=8192,\\n            system_prompt=\"\"\"\\n            You are an expert software engineer, helping with wide variety of queries.\\n            If you need more context to answer a question properly, use the request_context tool\\n            to ask for additional context.\\n            You can read specific files using the read_file tool.\\n            You can search for relevant code sections using the search_code tool.\\n            Provide a technical and detailed answer to the question given the context.\\n            \"\"\",\\n            tools=[request_context_tool, read_file_tool, codebase_tool_param],\\n        )\\n\\n        # Handle tool calls\\n        for block in response:\\n            if isinstance(block, ToolCall):\\n                if block.tool_name == request_context_tool.name:\\n                    context_request = block.tool_input.get(\\n                        \"context_request\", \"Need more information to proceed.\"\\n                    )\\n                    formatted_request = (\\n                        f\"I need more context to help you effectively:\\\\n\\\\n{context_request}\"\\n                    )\\n                    raise ContextRequestException(formatted_request)\\n                elif block.tool_name == read_file_tool.name:\\n                    file_result = self.read_file_tool.run_impl(block.tool_input)\\n                    # Add file content to messages and generate new response\\n                    messages[0].append(\\n                        cast(GeneralContentBlock, TextPrompt(text=f\"\\\\nFile contents:\\\\n{file_result.tool_output}\"))\\n                    )\\n                elif block.tool_name == codebase_tool_param.name:\\n                    search_result = self.codebase_tool.run_impl({\\n                        \"code_section_requests\": [{\\n                            \"description\": block.tool_input[\"description\"]\\n                        }]\\n                    })\\n                    # Add search results to messages\\n                    messages[0].append(\\n                        cast(GeneralContentBlock, TextPrompt(text=f\"\\\\nRelevant code:\\\\n{search_result.tool_output}\"))\\n                    )\\n                \\n                # Generate new response with updated context\\n                response, _ = self.client.generate(\\n                    messages=messages,\\n                    max_tokens=8192,\\n                    system_prompt=\"\"\"\\n                    You are an expert software engineer, helping with wide variety of queries.\\n                    If you need more context to answer a question properly, use the request_context tool\\n                    to ask for additional context.\\n                    You can read specific files using the read_file tool.\\n                    You can search for relevant code sections using the search_code tool.\\n                    Provide a technical and detailed answer to the question given the context.\\n                    \"\"\",\\n                    tools=[request_context_tool, read_file_tool, codebase_tool_param],\\n                )\\n\\n        # Return final text response\\n        if not isinstance(response[0], TextResult):\\n            raise ValueError(\"Expected text response from LLM\")\\n\\n        return response[0].text.strip()\\n\\n\\ndef ask_expert(question: str) -> str:\\n    \"\"\"\\n    Ask an expert question and get the response.\\n\\n    Args:\\n        question: The question to ask\\n\\n    Returns:\\n        Claude\\'s response as a string\\n    \"\"\"\\n    # Get current branch/ref\\n    current_ref = subprocess.check_output(\\n        [\"git\", \"rev-parse\", \"--abbrev-ref\", \"HEAD\"],\\n        text=True,\\n    ).strip()\\n\\n    # Create expert agent for current ref\\n    expert = ExpertAgent(current_ref)\\n    \\n    try:\\n        return expert.answer_question(question)\\n    finally:\\n        # Cleanup temp directory\\n        subprocess.run([\"rm\", \"-rf\", str(expert.temp_dir)], check=True)\\n'\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
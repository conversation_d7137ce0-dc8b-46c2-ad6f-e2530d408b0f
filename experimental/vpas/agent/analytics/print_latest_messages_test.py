#!/usr/bin/env python3
"""
Tests for print_latest_messages.py
"""

import pytest
import tempfile
import os
from unittest.mock import MagicMock, patch

from experimental.vpas.agent.analytics.print_latest_messages import (
    read_request_ids_from_file,
    get_latest_request_id_from_history,
)


class TestReadRequestIdsFromFile:
    """Test the read_request_ids_from_file function."""

    def test_read_valid_file(self):
        """Test reading a valid file with request IDs."""
        # Create a temporary file with request IDs
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            f.write("request-id-1\n")
            f.write("request-id-2\n")
            f.write("request-id-3\n")
            f.write("\n")  # Empty line should be skipped
            f.write("request-id-4\n")
            temp_file_path = f.name

        try:
            request_ids = read_request_ids_from_file(temp_file_path)
            assert len(request_ids) == 4
            assert request_ids == [
                "request-id-1",
                "request-id-2",
                "request-id-3",
                "request-id-4",
            ]
        finally:
            os.unlink(temp_file_path)

    def test_read_empty_file(self):
        """Test reading an empty file."""
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            temp_file_path = f.name

        try:
            request_ids = read_request_ids_from_file(temp_file_path)
            assert len(request_ids) == 0
        finally:
            os.unlink(temp_file_path)

    def test_read_nonexistent_file(self):
        """Test reading a file that doesn't exist."""
        request_ids = read_request_ids_from_file("/nonexistent/file.txt")
        assert len(request_ids) == 0

    def test_read_file_with_whitespace(self):
        """Test reading a file with whitespace around request IDs."""
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            f.write("  request-id-1  \n")
            f.write("\trequest-id-2\t\n")
            f.write("request-id-3\n")
            f.write("   \n")  # Line with only whitespace
            temp_file_path = f.name

        try:
            request_ids = read_request_ids_from_file(temp_file_path)
            assert len(request_ids) == 3
            assert request_ids == ["request-id-1", "request-id-2", "request-id-3"]
        finally:
            os.unlink(temp_file_path)


class TestGetLatestRequestIdFromHistory:
    """Test the get_latest_request_id_from_history function."""

    def test_get_request_id_with_chat_history(self):
        """Test getting request ID when chat_request has chat history."""
        mock_chat_request = MagicMock()

        # Create mock chat history with request_id
        mock_exchange = MagicMock()
        mock_exchange.request_id = "latest-request-id"
        mock_chat_request.chat_history = [mock_exchange]

        mock_get_func = MagicMock(return_value=mock_chat_request)

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result == "latest-request-id"

    def test_get_request_id_multiple_exchanges(self):
        """Test getting request ID from the last exchange when multiple exist."""
        mock_chat_request = MagicMock()

        # Create mock chat history with multiple exchanges
        mock_exchange1 = MagicMock()
        mock_exchange1.request_id = "first-request-id"
        mock_exchange2 = MagicMock()
        mock_exchange2.request_id = "second-request-id"
        mock_exchange3 = MagicMock()
        mock_exchange3.request_id = "latest-request-id"

        mock_chat_request.chat_history = [
            mock_exchange1,
            mock_exchange2,
            mock_exchange3,
        ]

        mock_get_func = MagicMock(return_value=mock_chat_request)

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result == "latest-request-id"

    def test_get_request_id_no_chat_history(self):
        """Test getting request ID when no chat history exists."""
        mock_chat_request = MagicMock()
        mock_chat_request.chat_history = []

        mock_get_func = MagicMock(return_value=mock_chat_request)

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result == "test-id"  # Should return the original request_id

    def test_get_request_id_no_request_found(self):
        """Test handling when no chat request is found."""
        mock_get_func = MagicMock(return_value=None)

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result.startswith("[ERROR: Could not find chat request")

    def test_get_request_id_exception_handling(self):
        """Test handling when an exception occurs."""
        mock_get_func = MagicMock(side_effect=Exception("Test exception"))

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result.startswith("[ERROR: Test exception")

    def test_get_request_id_no_request_id_in_exchange(self):
        """Test handling when exchange has no request_id."""
        mock_chat_request = MagicMock()

        # Create mock chat history with exchange that has no request_id
        mock_exchange = MagicMock()
        mock_exchange.request_id = None
        mock_chat_request.chat_history = [mock_exchange]

        mock_get_func = MagicMock(return_value=mock_chat_request)

        result = get_latest_request_id_from_history(
            "test-id", mock_get_func, "test-tenant"
        )
        assert result == "test-id"  # Should return the original request_id


if __name__ == "__main__":
    pytest.main([__file__])

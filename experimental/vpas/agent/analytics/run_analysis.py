#!/usr/bin/env python3

import argparse
from collections import Counter, defaultdict
from concurrent.futures import ThreadPoolExecutor
import datetime
import logging
import pandas as pd
from tqdm import tqdm

from base.datasets.tenants import DatasetTenant, get_tenant

from experimental.vpas.agent.analytics.big_query_utils import (
    get_agent_conv_last_request_ids,
)
from experimental.vpas.agent.analytics.conversation import Conversation
from experimental.vpas.agent.analytics.html_report.html_report_generator import (
    gen_html_report,
)
from experimental.vpas.utils.ri_utils import (
    get_chat_host_request_factory,
    get_chat_host_response_factory,
)
from experimental.vpas.agent.analytics.str_replace_editor_tool_analysis import (
    add_analysis,
    gen_summary,
    print_summary,
)
from experimental.vpas.agent.analytics.conversation_analysis import (
    make_tool_calls_df,
    print_results,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="Analyze agent conversations")
    date_group = parser.add_mutually_exclusive_group()
    date_group.add_argument(
        "--from-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        default=None,
        help="Start date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    date_group.add_argument(
        "-lh",
        "--last-hours",
        type=int,
        default=None,
        help="Process data from the last N hours",
    )
    date_group.add_argument(
        "-ld",
        "--last-days",
        type=int,
        default=None,
        help="Process data from the last N days",
    )
    parser.add_argument(
        "--to-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        default=None,
        help="End date in ISO format (YYYY-MM-DDTHH:MM:SS). Default: now",
    )
    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help=f"Tenant name to filter by. Default: {get_tenant('dogfood-shard').name}",
    )
    parser.add_argument(
        "--thread-count",
        type=int,
        default=20,
        help="Number of threads to use for parallel processing. Default: 20",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of conversations to process. Default: None (process all)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    parser.add_argument(
        "--model",
        type=str,
        default="claude",
        choices=["claude", "gemini"],
        help="Filter conversations by model name (claude or gemini). Default: claude",
    )
    return parser.parse_args()


def process_request_id(
    request_id, _get_chat_host_request, _get_chat_host_response, model=None
):
    """Process a single request ID to get the conversation."""
    try:
        start_time = datetime.datetime.now()
        logger.debug(f"Processing request ID: {request_id}")

        # Get the chat host request and response
        chat_request = _get_chat_host_request(request_id=request_id)
        chat_response = _get_chat_host_response(request_id=request_id)

        if not chat_request or not chat_response:
            logger.warning(f"Could not find chat request or response for {request_id}")
            return None

        # Filter by model name if provided
        if model:
            if not hasattr(chat_request, "model_name") or not isinstance(
                chat_request.model_name, str
            ):
                logger.debug(
                    f"Skipping request ID {request_id} because model_name attribute is missing or not a string."
                )
                return None
            if model.lower() not in chat_request.model_name.lower():
                logger.debug(
                    f"Skipping request ID {request_id} because model_name '{chat_request.model_name}' does not match filter '{model}'."
                )
                return None

        # Convert to conversation
        conversation = Conversation.from_chat_request(
            request_id=request_id,
            chat_request=chat_request,
            chat_response=chat_response,
            get_chat_host_request_func=_get_chat_host_request,
        )

        # Add str-replace-editor tool call analysis
        add_analysis(conversation)

        processing_time = datetime.datetime.now() - start_time
        logger.debug(
            f"Processed request ID {request_id} in {processing_time.total_seconds():.2f} seconds"
        )

        return conversation
    except Exception as e:
        logger.error(f"Error processing request ID {request_id}: {e}", exc_info=True)
        return None


def fetch_conversations(
    from_date, to_date, tenant_name, thread_count, limit=None, model=None
):
    """Fetch conversations in the given time range."""

    logger.info(
        f"Analyzing conversations from {from_date} to {to_date} for tenant {tenant_name}"
    )

    # Get the last request ID for each conversation
    logger.info("Fetching last request IDs for conversations...")
    request_ids = get_agent_conv_last_request_ids(
        tenant=get_tenant(tenant_name),
        from_datetime=from_date,
        to_datetime=to_date,
    )

    if limit:
        request_ids = request_ids[:limit]

    logger.info(f"Found {len(request_ids)} conversations")

    # Process each request ID in parallel
    conversations = []
    total_processed = 0
    error_count = 0

    _get_chat_host_request = get_chat_host_request_factory(tenant_name)
    _get_chat_host_response = get_chat_host_response_factory(tenant_name)

    with ThreadPoolExecutor(max_workers=thread_count) as executor:
        futures = [
            executor.submit(
                process_request_id,
                request_id,
                _get_chat_host_request,
                _get_chat_host_response,
                model,  # Pass model here
            )
            for request_id in request_ids
        ]

        # Use tqdm to show progress
        for future in tqdm(
            futures, desc="Processing conversations", total=len(futures)
        ):
            total_processed += 1
            conversation = future.result()
            if conversation:
                conversations.append(conversation)
            else:
                error_count += 1

    # Calculate error rate
    error_rate = (error_count / total_processed * 100) if total_processed > 0 else 0

    logger.info(f"Successfully processed {len(conversations)} conversations")
    logger.info(
        f"Processing errors: {error_count}/{total_processed} ({error_rate:.2f}%)"
    )
    return conversations, total_processed, error_count


def analyze_conversations(
    from_date, to_date, tenant_name, thread_count, limit=None, model=None
):
    """Analyze agent conversations in the given time range."""

    conversations, total_processed, error_count = fetch_conversations(
        from_date, to_date, tenant_name, thread_count, limit, model
    )

    # Analyze the conversations
    analyze_results(conversations, total_processed, error_count, from_date, to_date)

    return conversations


def analyze_results(
    conversations: list[Conversation],
    total_processed=0,
    error_count=0,
    from_date=None,
    to_date=None,
):
    """Analyze the conversations and print statistics."""
    if not conversations:
        logger.warning("No conversations to analyze")
        return

    # Basic statistics
    df = make_tool_calls_df(conversations)
    print_results(df, total_processed, error_count)

    # str-replace-editor specific analysis
    str_replace_summary = gen_summary(
        conversations,
        from_date=from_date,
        to_date=to_date,
    )
    print_summary(str_replace_summary)
    gen_html_report(str_replace_summary, df)


def main():
    args = parse_args()

    # Set root log level
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    # Get current time
    now = datetime.datetime.now(datetime.timezone.utc)

    # Parse dates
    to_date = args.to_date if args.to_date is not None else now

    # Determine from_date based on arguments
    if args.last_hours is not None:
        from_date = now - datetime.timedelta(hours=args.last_hours)
        logger.info(f"Processing data from the last {args.last_hours} hours")
    elif args.last_days is not None:
        from_date = now - datetime.timedelta(days=args.last_days)
        logger.info(f"Processing data from the last {args.last_days} days")
    elif args.from_date is not None:
        from_date = args.from_date
    else:
        # Default: process the last 1 hour
        from_date = now - datetime.timedelta(hours=1)
        logger.info("No time range specified, defaulting to the last 1 hour")

    logger.info(f"Analyzing conversations from {from_date} to {to_date}")

    # Analyze conversations
    analyze_conversations(
        from_date=from_date,
        to_date=to_date,
        tenant_name=args.tenant_name,
        thread_count=args.thread_count,
        limit=args.limit,
        model=args.model,  # Pass the model argument
    )


if __name__ == "__main__":
    main()

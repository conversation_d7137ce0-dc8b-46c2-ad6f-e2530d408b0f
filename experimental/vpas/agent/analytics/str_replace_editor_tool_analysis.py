from dataclasses import dataclass, field
import json
import datetime

from experimental.vpas.agent.analytics.conversation import (
    Conversation,
    StrReplaceEditorToolCallAnalysis,
    TokenStats,
)
from experimental.vpas.agent.replay_eval.str_replace_editor_utils import (
    extract_str_replace_entries_any_schema,
)
from experimental.vpas.agent.cost_analysis.token_cost_calculator import (
    calculate_claude_cost,
    format_cost,
)
import re
from typing import Dict, List, Optional
from experimental.vpas.agent.str_replace_editor.utils import (
    parse_str_replace_tool_result,
)
from experimental.vpas.agent.analytics.save_no_match_sample import save_no_match_sample

# -1 means infinity
# if view_range is missing meaning viewing the whole file, we will treat it as infinity
VIEW_RANGE_LEN_BUCKETS = [10, 20, 50, 100, 200, 500, 1000, 2000, -1]

# Buckets for the number of entries in str_replace_entries
STR_REPLACE_ENTRIES_BUCKETS = [1, 2, 3, 5, 10, 20, -1]

# Error classification patterns
ERROR_PATTERNS = {
    # File not found errors
    "cannot_read_file": [r"Cannot read file"],
    "failed_to_read_file": [r"Failed to read file"],
    "error_while_reading": [r"Ran into .* while trying to read"],
    "no_such_file": [r"No such file or directory"],
    # Path parameter errors
    "missing_path_param": [r"Missing required parameter `path`"],
    "invalid_path_param": [r"Invalid parameter `path`"],
    "empty_path": [r"path must not be empty"],
    # Command errors
    "unknown_command": [r"Unknown command"],
    "invalid_command": [r"Invalid command"],
    # View range errors
    "invalid_view_range": [r"Invalid view range"],
    "invalid_view_range_param": [r"Invalid view_range"],
    # String replacement match errors
    "str_replace_no_verbatim_match": [
        r"No replacement was performed, oldStr did not appear verbatim"
    ],
    "str_replace_no_match_at_line": [
        r"No match found close to the provided line numbers"
    ],
    # String replacement content errors
    "str_replace_empty_old_str": [
        r"old_str is empty which is only allowed when the file is empty"
    ],
    "str_replace_multiple_matches": [r"Multiple occurrences of oldStr .* found"],
    "str_replace_overlapping_entries": [
        r"old_str line numbers range overlaps with another entry"
    ],
    # String replacement parameter errors
    "str_replace_missing_entries_param": [
        r"Missing required parameter `str_replace_entries`"
    ],
    "str_replace_invalid_entries_param": [r"Invalid parameter `str_replace_entries`"],
    "str_replace_empty_entries": [r"Empty required parameter `str_replace_entries`"],
    "str_replace_invalid_start_line": [
        r"Invalid parameter `old_str_start_line_number`"
    ],
    "str_replace_invalid_end_line": [r"Invalid parameter `old_str_end_line_number`"],
    "str_replace_missing_old_str": [r"Missing required parameter `old_str`"],
    "str_replace_missing_new_str": [r"Missing required parameter `new_str`"],
    "str_replace_old_new_identical": [r"The old_str and new_str are identical"],
    "str_replace_old_new_not_string": [r".*includes is not a function"],
    # Insert parameter errors
    "insert_missing_entries_param": [
        r"Missing required parameter `insert_line_entries`"
    ],
    "insert_invalid_entries_param": [r"Invalid parameter `insert_line_entries`"],
    "insert_empty_entries": [r"Empty required parameter `insert_line_entries`"],
    "insert_invalid_line_param": [r"Invalid `insert_line` parameter"],
    "insert_invalid_line": [r"Invalid parameter `insert_line`"],
    # Other errors
    "internal_error": [r"Internal error"],
}


def classify_error_message(error_message: Optional[str]) -> Optional[str]:
    """
    Classify an error message from the str-replace-editor tool into predefined categories.

    Args:
        error_message: The error message to classify

    Returns:
        The error type as a string, or None if no match is found or error_message is None/empty
    """
    if not error_message:
        return None

    cur_error_type = None
    for error_type, patterns in ERROR_PATTERNS.items():
        for pattern in patterns:
            if re.search(pattern, error_message, re.IGNORECASE):
                cur_error_type = error_type
                break
        if cur_error_type:
            break

    return cur_error_type or "other"


def are_paths_equivalent(path1: str, path2: str) -> bool:
    """
    Check if two paths are considered equivalent.
    Paths are equivalent if they have a common suffix that includes the full filename
    and at least one directory.

    Examples:
    - 'clients/a/t.txt' and 'a/t.txt' are equivalent
    - 'clients/f.txt' and 'server/f.txt' are not equivalent
    """
    # Handle empty paths
    if not path1 or not path2:
        return False

    if path1 == path2:
        # If paths are identical, they're equivalent only if they contain at least one directory
        return "/" in path1

    # Split paths into components
    path1_parts = path1.split("/")
    path2_parts = path2.split("/")

    # Both paths must have at least 2 components (directory and filename)
    if len(path1_parts) < 2 or len(path2_parts) < 2:
        return False

    # Find the common suffix
    common_suffix_length = 0
    for i in range(1, min(len(path1_parts), len(path2_parts)) + 1):
        if path1_parts[-i] == path2_parts[-i]:
            common_suffix_length = i
        else:
            break

    # Common suffix must include filename and at least one directory
    return common_suffix_length >= 2


class PathSet:
    """
    A set-like collection for paths that uses the are_paths_equivalent function
    for membership tests instead of exact string matching.
    """

    def __init__(self):
        self.paths = []

    def add(self, path: str) -> None:
        """
        Add a path to the set if it's not already present (or equivalent to an existing path).
        """
        if path not in self:
            self.paths.append(path)

    def update(self, paths) -> None:
        """
        Add multiple paths to the set.
        """
        if isinstance(paths, PathSet):
            # If we're updating from another PathSet, use its internal paths list
            for path in paths.paths:
                self.add(path)
        else:
            # Otherwise, assume it's an iterable of paths
            for path in paths:
                self.add(path)

    def __contains__(self, path: str) -> bool:
        """
        Check if the path or an equivalent path is in the set.
        """
        return any(
            are_paths_equivalent(path, existing_path) for existing_path in self.paths
        )

    def __iter__(self):
        """
        Make the PathSet iterable by returning an iterator over its paths.
        """
        return iter(self.paths)


@dataclass
class StrReplaceEditorAnalysisSummary:
    total_calls: int
    success_rate: float
    command_counts: dict[str, int]
    view_range_len_hist: list[int]
    # Histogram of number of entries in str_replace_entries
    str_replace_entries_hist: list[int]
    first_view_count: int
    # Count of views where we should not have used view range
    should_not_use_view_range_count: int
    # Percentage of views where we should not have used view range
    should_not_use_view_range_percentage: float
    # List of request_ids for views where we should not have used view range
    should_not_use_view_range_request_ids: list[str]
    # Count of views that are first in a sequence of consecutive view calls
    first_in_seq_of_views_count: int
    # List of request_ids for views that are first in a sequence of consecutive view calls
    first_in_seq_of_views_request_ids: list[str]
    # Count of views that are last in a sequence of consecutive view calls
    last_in_seq_of_views_count: int
    # List of request_ids for views that are last in a sequence of consecutive view calls
    last_in_seq_of_views_request_ids: list[str]
    # List of tuples (first_request_id, last_request_id) for each sequence
    view_sequence_pairs: list[tuple[str, str]]
    # Count of views of files that were previously retrieved
    previously_retrieved_view_count: int = 0
    # List of request_ids for views of files that were previously retrieved
    previously_retrieved_view_request_ids: list[str] = field(default_factory=list)
    # List of request_ids for str_replace calls with more than 5 entries
    large_str_replace_request_ids: list[str] = field(default_factory=list)
    # Count of views where the view range was expanded
    view_expanded_count: int = 0
    # Percentage of views where the view range was expanded
    view_expanded_percentage: float = 0.0
    # List of request_ids for views where the view range was expanded
    view_expanded_request_ids: list[str] = field(default_factory=list)
    # List of request_ids for the last request in conversations where there was at least one view call that was expanded
    conversations_with_expanded_view_last_request_ids: list[str] = field(
        default_factory=list
    )
    # Count of views where the current view_range is a subrange of previous expanded view range
    subrange_of_expanded_view_count: int = 0
    # List of request_ids for views where the current view_range is a subrange of previous expanded view range
    subrange_of_expanded_view_request_ids: list[tuple[str, str]] = field(
        default_factory=list
    )
    # Error statistics
    error_counts: Dict[str, int] = field(default_factory=dict)
    # List of tool use request_ids for each error type
    error_request_ids: Dict[str, List[str]] = field(default_factory=dict)
    # List of tool response request_ids for each error type
    error_response_request_ids: Dict[str, List[str]] = field(default_factory=dict)
    # List of (tool_use_request_id, tool_response_request_id, error_message) tuples for each error type
    error_request_id_pairs: Dict[str, List[tuple[str, str, str]]] = field(
        default_factory=dict
    )
    # Count of last turns in a conversation round where the message is empty and has no tool use
    empty_last_turn_count: int = 0
    # List of request_ids for last turns in a conversation round where the message is empty and has no tool use
    empty_last_turn_request_ids: list[str] = field(default_factory=list)
    # Count of turns where the response contains "[/AUGMENT]"
    augment_tag_count: int = 0
    # List of request_ids for turns where the response contains "[/AUGMENT]"
    augment_tag_request_ids: list[str] = field(default_factory=list)

    # Token statistics
    # Total tokens in all conversations
    total_tokens: int = 0
    # Token statistics for each tool
    tool_token_stats: Dict[str, Dict[str, int]] = field(default_factory=dict)
    # Token statistics for text messages
    text_token_stats: Dict[str, int] = field(default_factory=dict)
    # Command-specific token statistics for str-replace-editor
    str_replace_editor_command_stats: Dict[str, Dict[str, int]] = field(
        default_factory=dict
    )
    # Cost information
    total_cost: float = 0.0
    tool_costs: Dict[str, float] = field(default_factory=dict)
    str_replace_editor_command_costs: Dict[str, float] = field(default_factory=dict)

    tool_to_token_stats: Dict[str, TokenStats] = field(default_factory=dict)
    sum_token_stats: TokenStats = field(default_factory=TokenStats)

    # Date range for the analysis
    from_date: Optional[datetime.datetime] = None
    to_date: Optional[datetime.datetime] = None


def add_analysis(conversation: Conversation):
    # Track which paths have been viewed in this conversation
    viewed_paths = PathSet()
    # Track which paths have been mentioned in retrieval tool output
    retrieved_paths = PathSet()
    # Track which files have been created
    created_files = PathSet()
    # Track which files have been mentioned with line numbers
    files_with_line_numbers = PathSet()
    # Track which files have been mentioned in diffs
    files_in_diffs = PathSet()

    # For tracking consecutive view calls
    consecutive_view_calls = []
    current_view_sequence = []

    # Define path patterns once
    path_patterns = [
        r"Path:\s*([\w\-./]+\.[\w]+)",  # Path: path/to/file.py
    ]

    # Define line number patterns
    line_number_patterns = [
        r"([\w\-./]+\.[\w]+):(\d+)",  # file.py:123
        r"([\w\-./]+\.[\w]+) line (\d+)",  # file.py line 123
        r"line (\d+) of ([\w\-./]+\.[\w]+)",  # line 123 of file.py
    ]

    # Define diff patterns
    diff_patterns = [
        r"diff --git a/([\w\-./]+\.[\w]+) b/",  # diff --git a/file.py b/
        r"--- a/([\w\-./]+\.[\w]+)",  # --- a/file.py
        r"\+\+\+ b/([\w\-./]+\.[\w]+)",  # +++ b/file.py
    ]

    # Helper function to extract paths from retrieval response
    def extract_paths_from_response(response_content):
        paths = PathSet()
        if not response_content:
            return paths

        for pattern in path_patterns:
            for match in re.finditer(pattern, response_content):
                path = match.group(1)
                paths.add(path)
        return paths

    # Helper function to extract files with line numbers from content
    def extract_files_with_line_numbers(content):
        files = PathSet()
        if not content:
            return files

        for pattern in line_number_patterns:
            for match in re.finditer(pattern, content):
                file_path = None
                # Different patterns have file path in different groups
                if pattern == line_number_patterns[0]:  # file.py:123
                    file_path = match.group(1)
                elif pattern == line_number_patterns[1]:  # file.py line 123
                    file_path = match.group(1)
                elif pattern == line_number_patterns[2]:  # line 123 of file.py
                    file_path = match.group(2)

                if file_path is not None:
                    files.add(file_path)
        return files

    # Helper function to extract files from diff content
    def extract_files_from_diff(content):
        files = PathSet()
        if not content:
            return files

        for pattern in diff_patterns:
            for match in re.finditer(pattern, content):
                file_path = match.group(1)
                files.add(file_path)
        return files

    # Process all turns in chronological order
    for round in conversation.agent_rounds:
        prev_turn = None
        for turn in round.agent_turns:
            if turn.tool_call is None or turn.tool_call.tool_use is None:
                continue

            # If this is a retrieval tool call, extract paths and add to retrieved_paths
            if (
                turn.tool_call.tool_use.name == "codebase-retrieval"
                and turn.tool_call.tool_result is not None
            ):
                response_content = turn.tool_call.tool_result.content
                new_paths = extract_paths_from_response(response_content)
                retrieved_paths.update(new_paths)

            elif (
                turn.tool_call.tool_use.name == "save-file"
                and turn.tool_call.tool_result is not None
            ):
                tool_inputs = turn.tool_call.tool_use.input
                path = tool_inputs.get("file_path")
                if path is not None:
                    created_files.add(path)

            # If this is a str-replace-editor tool call, analyze it
            elif (
                turn.tool_call.tool_use.name == "str-replace-editor"
                or turn.tool_call.tool_use.name == "view"
            ):
                tool_inputs = turn.tool_call.tool_use.input
                if turn.tool_call.tool_use.name == "view":
                    command = "view"
                else:
                    command = tool_inputs.get("command")

                path = tool_inputs.get("path")
                view_range = tool_inputs.get("view_range")

                # Check if the tool call resulted in an error
                is_error = False
                error_message = None
                if turn.tool_call.tool_result and turn.tool_call.tool_result.is_error:
                    is_error = True
                    error_message = turn.tool_call.tool_result.content

                # Classify error if present
                error_type = None
                if is_error and error_message:
                    error_type = classify_error_message(error_message)

                # Check if view range was expanded and extract expanded range if available
                was_view_expanded = False
                expanded_view_range = None
                if (
                    command == "view"
                    and turn.tool_call.tool_result
                    and not turn.tool_call.tool_result.is_error
                    and turn.tool_call.tool_result.content
                    and "View range expanded to meet minimum size of"
                    in turn.tool_call.tool_result.content
                ):
                    was_view_expanded = True
                    content = turn.tool_call.tool_result.content

                    # Extract the expanded range from the message
                    # Try to match "New range: [start, end]" pattern
                    new_range_match = re.search(r"New range: \[(\d+), (\d+)\]", content)
                    if new_range_match:
                        start_line = int(new_range_match.group(1))
                        end_line = int(new_range_match.group(2))
                        expanded_view_range = [start_line, end_line]
                    else:
                        # Try to match "End line adjusted to last line of file (end)" pattern
                        end_line_match = re.search(
                            r"End line adjusted to last line of file \((\d+)\)", content
                        )
                        if end_line_match:
                            # For this pattern, we need to get the start line from the original view range
                            end_line = int(end_line_match.group(1))
                            # Use the original start line if available, otherwise default to 1
                            start_line = (
                                view_range[0]
                                if view_range and len(view_range) >= 1
                                else 1
                            )
                            expanded_view_range = [start_line, end_line]

                analysis = StrReplaceEditorToolCallAnalysis(
                    command=command,
                    path=path,
                    view_range=view_range,
                    expanded_view_range=expanded_view_range,
                    error_type=error_type,
                    error_message=error_message if is_error else None,
                    was_view_expanded=was_view_expanded,
                )
                turn.tool_call.analysis = analysis

                # Track consecutive view calls for sequence detection
                if command == "view" and path is not None and view_range is not None:
                    # If this is a view call with a view range, add it to the current sequence
                    if not current_view_sequence or (
                        current_view_sequence[-1][0]
                        == path  # Same file as previous view
                    ):
                        # Continue or start a new sequence
                        current_view_sequence.append((path, turn.tool_call))
                    else:
                        # Different file, end the current sequence and start a new one
                        if len(current_view_sequence) >= 3:
                            # We found a sequence of 3+ consecutive view calls on the same file
                            consecutive_view_calls.append(current_view_sequence)
                        current_view_sequence = [(path, turn.tool_call)]
                elif (
                    current_view_sequence
                ):  # Not a view call but we have a sequence in progress
                    # End the current sequence
                    if len(current_view_sequence) >= 3:
                        # We found a sequence of 3+ consecutive view calls on the same file
                        consecutive_view_calls.append(current_view_sequence)
                    current_view_sequence = []

                if command == "view" and path is not None:
                    analysis.was_viewed = path in viewed_paths
                    viewed_paths.add(path)

                    analysis.was_path_retrieved = path in retrieved_paths
                    analysis.was_file_created = path in created_files
                    analysis.had_line_number_info = path in files_with_line_numbers
                    analysis.had_diff_info = path in files_in_diffs

                    # Determine if we should not use view range but did use it
                    # We should use full file view if we don't have any prior information about the file
                    had_prior_info = (
                        analysis.was_viewed
                        or analysis.was_path_retrieved
                        or analysis.was_file_created
                        or analysis.had_line_number_info
                        or analysis.had_diff_info
                    )
                    analysis.should_not_use_view_range = (
                        not had_prior_info and view_range is not None
                    )

                    # Check if this view is a subrange of previous expanded view
                    # Conditions:
                    # 1. Current command is view with view_range
                    # 2. Previous command was also view with view_range on the same file
                    # 3. Previous view was expanded
                    # 4. Current view_range is a subrange of previous expanded range
                    analysis.is_subrange_of_expanded_view = False

                    # Check if the previous turn was a view command on the same file
                    if (
                        prev_turn is not None
                        and prev_turn.tool_call is not None
                        and prev_turn.tool_call.tool_use is not None
                        and prev_turn.tool_call.tool_use.name == "str-replace-editor"
                        and prev_turn.tool_call.analysis is not None
                        and prev_turn.tool_call.analysis.command == "view"
                        and prev_turn.tool_call.analysis.path == path
                        and prev_turn.tool_call.analysis.was_view_expanded
                        and prev_turn.tool_call.analysis.expanded_view_range is not None
                        and view_range is not None
                        and len(view_range) == 2
                    ):
                        # Check if current range is a subrange of previous expanded range
                        curr_start, curr_end = view_range
                        prev_start, prev_end = (
                            prev_turn.tool_call.analysis.expanded_view_range
                        )

                        # Handle -1 in ranges (meaning end of file)
                        if curr_end == -1:
                            curr_end = float("inf")
                        if prev_end == -1:
                            prev_end = float("inf")

                        if curr_start >= prev_start and curr_end <= prev_end:
                            analysis.is_subrange_of_expanded_view = True

                if (
                    command == "str_replace"
                    and turn.tool_call.tool_result
                    and turn.tool_call.tool_result.is_error
                    and turn.tool_call.tool_result.content
                ):
                    analysis.str_replace_tool_result = parse_str_replace_tool_result(
                        turn.tool_call.tool_result.content
                    )
                    # uncomment to save samples to
                    # save_no_match_sample(turn.tool_call)

            else:
                # some other tool
                if turn.tool_call.tool_result is None:
                    continue
                response_content = turn.tool_call.tool_result.content
                files_with_line_info = extract_files_with_line_numbers(response_content)
                files_with_line_numbers.update(files_with_line_info)

                files_in_diff = extract_files_from_diff(response_content)
                files_in_diffs.update(files_in_diff)
            prev_turn = turn

    # Process any remaining sequence
    if current_view_sequence and len(current_view_sequence) >= 3:
        consecutive_view_calls.append(current_view_sequence)

    # Mark the first and last calls in each sequence
    for sequence in consecutive_view_calls:
        # Get the first and last tool calls in the sequence
        first_tool_call = sequence[0][1]
        last_tool_call = sequence[-1][1]

        if first_tool_call.analysis is not None:
            first_tool_call.analysis.first_in_seq_of_views = True

        if last_tool_call.analysis is not None:
            last_tool_call.analysis.last_in_seq_of_views = True


def gen_summary(
    conversations: list[Conversation],
    from_date: Optional[datetime.datetime] = None,
    to_date: Optional[datetime.datetime] = None,
) -> StrReplaceEditorAnalysisSummary:
    total_calls = 0
    successful_calls = 0
    command_counts = {}
    view_range_len_hist = [0] * len(VIEW_RANGE_LEN_BUCKETS)
    str_replace_entries_hist = [0] * len(STR_REPLACE_ENTRIES_BUCKETS)
    first_view_count = 0
    should_not_use_view_range_count = 0
    should_not_use_view_range_request_ids = []
    first_in_seq_of_views_count = 0
    first_in_seq_of_views_request_ids = []
    last_in_seq_of_views_count = 0
    view_expanded_count = 0
    view_expanded_request_ids = []
    conversations_with_expanded_view_last_request_ids = []
    empty_last_turn_count = 0
    empty_last_turn_request_ids = []
    augment_tag_count = 0
    augment_tag_request_ids = []

    # Token statistics
    total_tokens = 0
    tool_token_stats = {}
    text_token_stats = {
        "user_messages_tokens": 0,
        "agent_messages_tokens": 0,
        "user_messages_tokens_pct": 0.0,
        "agent_messages_tokens_pct": 0.0,
    }
    str_replace_editor_command_stats = {
        "view": {
            "prompt_tokens": 0,  # Tokens sent TO the model (tool outputs)
            "completion_tokens": 0,  # Tokens generated BY the model (tool inputs)
            "call_count": 0,
            "prompt_tokens_pct": 0.0,
            "completion_tokens_pct": 0.0,
            "cost_pct": 0.0,
        },
        "str_replace": {
            "prompt_tokens": 0,  # Tokens sent TO the model (tool outputs)
            "completion_tokens": 0,  # Tokens generated BY the model (tool inputs)
            "call_count": 0,
            "prompt_tokens_pct": 0.0,
            "completion_tokens_pct": 0.0,
            "cost_pct": 0.0,
        },
        "insert": {
            "prompt_tokens": 0,  # Tokens sent TO the model (tool outputs)
            "completion_tokens": 0,  # Tokens generated BY the model (tool inputs)
            "call_count": 0,
            "prompt_tokens_pct": 0.0,
            "completion_tokens_pct": 0.0,
            "cost_pct": 0.0,
        },
    }

    # New token stats
    tool_to_token_stats = {}
    sum_token_stats = TokenStats()

    # Cost information
    calculated_total_cost = 0.0
    tool_costs = {}
    str_replace_editor_command_costs = {}
    last_in_seq_of_views_request_ids = []
    view_sequence_pairs = []
    large_str_replace_request_ids = []
    previously_retrieved_view_count = 0
    previously_retrieved_view_request_ids = []

    # Error statistics
    error_counts = {}
    error_request_ids = {}
    error_response_request_ids = {}
    error_request_id_pairs = {}

    # Track conversations with expanded view calls
    conversations_with_expanded_view = set()
    conversations_with_expanded_view_last_request_ids = []

    # Track views that are subranges of previous expanded views
    subrange_of_expanded_view_count = 0
    subrange_of_expanded_view_request_ids = []

    for conversation in conversations:
        for round in conversation.agent_rounds:
            # Check if the last turn in this round has an empty message and no tool use
            if round.agent_turns and len(round.agent_turns) > 0:
                last_turn = round.agent_turns[-1]
                if last_turn.message == "" and (
                    last_turn.tool_call is None or last_turn.tool_call.tool_use is None
                ):
                    empty_last_turn_count += 1
                    empty_last_turn_request_ids.append(last_turn.request_id)

            # Check for turns where the response contains "[/AUGMENT]"
            for turn in round.agent_turns:
                if turn.message and "[/AUGMENT]" in turn.message:
                    augment_tag_count += 1
                    augment_tag_request_ids.append(turn.request_id)

            for turn in round.agent_turns:
                if turn.tool_call is None or turn.tool_call.tool_use is None:
                    continue

                # Update new token stats
                tool_name = turn.tool_call.tool_use.name
                if tool_name == "str-replace-editor":
                    command = turn.tool_call.tool_use.input.get("command")
                    if command and command in ["view", "str_replace", "insert"]:
                        tool_name = f"str-replace-editor-{command}"
                if tool_name not in tool_to_token_stats:
                    tool_to_token_stats[tool_name] = TokenStats()
                tool_to_token_stats[tool_name].add(turn.tool_call.token_stats)
                sum_token_stats.add(turn.tool_call.token_stats)

                if (
                    turn.tool_call.tool_use.name not in ["view", "str-replace-editor"]
                    or turn.tool_call.analysis is None
                ):
                    continue

                # Check if this is a view call with expanded range
                if (
                    turn.tool_call.analysis.command == "view"
                    and turn.tool_call.analysis.was_view_expanded
                ):
                    # Add this conversation to the set of conversations with expanded view calls
                    conversations_with_expanded_view.add(conversation.last_request_id)

                total_calls += 1

                # Check if the call was successful
                if (
                    turn.tool_call.tool_result
                    and not turn.tool_call.tool_result.is_error
                ):
                    successful_calls += 1
                elif turn.tool_call.analysis.error_type:
                    # Track error statistics
                    error_type = turn.tool_call.analysis.error_type
                    error_counts[error_type] = error_counts.get(error_type, 0) + 1

                    # Track request IDs for each error type
                    if error_type not in error_request_ids:
                        error_request_ids[error_type] = []
                        error_response_request_ids[error_type] = []
                        error_request_id_pairs[error_type] = []

                    # Add tool use request ID
                    tool_use_request_id = turn.tool_call.tool_use_request_id
                    if tool_use_request_id:
                        error_request_ids[error_type].append(tool_use_request_id)

                    # Add tool response request ID
                    tool_response_request_id = None
                    if turn.tool_call.tool_result:
                        tool_response_request_id = turn.tool_call.tool_result.request_id
                        if tool_response_request_id:
                            error_response_request_ids[error_type].append(
                                tool_response_request_id
                            )

                    # Add request ID pair and error message if both IDs are available
                    if tool_use_request_id and tool_response_request_id:
                        error_message = ""
                        if (
                            turn.tool_call.tool_result
                            and turn.tool_call.tool_result.content
                        ):
                            error_message = turn.tool_call.tool_result.content

                        error_request_id_pairs[error_type].append(
                            (
                                tool_use_request_id,
                                tool_response_request_id,
                                error_message,
                            )
                        )

                # Count commands
                command = turn.tool_call.analysis.command
                if command:
                    command_counts[command] = command_counts.get(command, 0) + 1

                # Add to view range length histogram
                if command == "view":
                    # Count first views
                    if not turn.tool_call.analysis.was_viewed:
                        first_view_count += 1

                    view_range = turn.tool_call.analysis.view_range
                    range_len = (
                        None  # Initialize range_len to avoid unbound variable issues
                    )
                    if not view_range:
                        view_range = [0, -1]
                    if len(view_range) == 2:
                        # Calculate the range length
                        start, end = view_range
                        if end == -1:  # -1 means infinity
                            range_len = float("inf")
                            # If this is a first view and it's viewing the whole file, don't count it as partial
                        else:
                            range_len = end - start + 1

                    # Add to view range length histogram if we have a valid range_len
                    if range_len is not None:
                        # Find the appropriate bucket
                        for i, bucket in enumerate(VIEW_RANGE_LEN_BUCKETS):
                            if (
                                range_len <= bucket or bucket == -1
                            ):  # -1 bucket catches infinity
                                view_range_len_hist[i] += 1
                                break

                    # Check if we should not have used view range
                    if turn.tool_call.analysis.should_not_use_view_range:
                        should_not_use_view_range_count += 1
                        request_id = turn.tool_call.tool_use_request_id or "unknown"
                        should_not_use_view_range_request_ids.append(request_id)

                    # Check if view range was expanded
                    if turn.tool_call.analysis.was_view_expanded:
                        view_expanded_count += 1
                        request_id = turn.tool_call.tool_use_request_id or "unknown"
                        view_expanded_request_ids.append(request_id)

                    # Check if this view is a subrange of previous expanded view
                    if turn.tool_call.analysis.is_subrange_of_expanded_view:
                        subrange_of_expanded_view_count += 1
                        request_id = turn.tool_call.tool_use_request_id or "unknown"
                        subrange_of_expanded_view_request_ids.append(
                            [request_id, conversation.last_request_id]
                        )

                # Add to str_replace_entries histogram
                elif (
                    command == "str_replace"
                    and turn.tool_call.tool_use
                    and turn.tool_call.tool_use.input
                ):
                    tool_inputs = turn.tool_call.tool_use.input
                    str_replace_entries = extract_str_replace_entries_any_schema(
                        tool_inputs
                    )
                    if str_replace_entries and isinstance(str_replace_entries, list):
                        num_entries = len(str_replace_entries)
                        # Find the appropriate bucket
                        for i, bucket in enumerate(STR_REPLACE_ENTRIES_BUCKETS):
                            if (
                                num_entries <= bucket or bucket == -1
                            ):  # -1 bucket catches infinity
                                str_replace_entries_hist[i] += 1
                                break

                        # Track request IDs for str_replace calls with more than 5 entries
                        if num_entries > 5:
                            request_id = turn.tool_call.tool_use_request_id or "unknown"
                            large_str_replace_request_ids.append(request_id)

                    # Check if this is a view of a file that was previously retrieved
                    if command == "view" and turn.tool_call.analysis.was_path_retrieved:
                        previously_retrieved_view_count += 1
                        request_id = turn.tool_call.tool_use_request_id or "unknown"
                        previously_retrieved_view_request_ids.append(request_id)

                    # Check if this is the first in a sequence of view calls
                    if turn.tool_call.analysis.first_in_seq_of_views:
                        first_in_seq_of_views_count += 1
                        first_request_id = (
                            turn.tool_call.tool_use_request_id or "unknown"
                        )
                        first_in_seq_of_views_request_ids.append(first_request_id)

                        # Find the corresponding last call in the same conversation
                        for last_round in conversation.agent_rounds:
                            for last_turn in last_round.agent_turns:
                                if (
                                    last_turn.tool_call
                                    and last_turn.tool_call.tool_use
                                    and last_turn.tool_call.tool_use.name
                                    == "str-replace-editor"
                                    and last_turn.tool_call.analysis
                                    and last_turn.tool_call.analysis.command == "view"
                                    and last_turn.tool_call.analysis.last_in_seq_of_views
                                    and last_turn.tool_call.analysis.path
                                    == turn.tool_call.analysis.path
                                ):
                                    # Found the corresponding last call
                                    last_request_id = (
                                        last_turn.tool_call.tool_use_request_id
                                        or "unknown"
                                    )
                                    if (
                                        last_request_id
                                        not in last_in_seq_of_views_request_ids
                                    ):
                                        last_in_seq_of_views_count += 1
                                        last_in_seq_of_views_request_ids.append(
                                            last_request_id
                                        )
                                        view_sequence_pairs.append(
                                            (first_request_id, last_request_id)
                                        )
                                    break

    # Calculate success rate
    success_rate = successful_calls / total_calls if total_calls > 0 else 0.0

    # Calculate should not use view range percentage
    view_command_count = command_counts.get("view", 0)
    should_not_use_view_range_percentage = (
        should_not_use_view_range_count / view_command_count * 100
        if view_command_count > 0
        else 0.0
    )

    # Calculate view expanded percentage
    view_expanded_percentage = (
        view_expanded_count / view_command_count * 100
        if view_command_count > 0
        else 0.0
    )

    # Collect the last request IDs for conversations with expanded view calls
    for conversation in conversations:
        if conversation.last_request_id in conversations_with_expanded_view:
            # Get the last request ID in the conversation
            last_request_id = conversation.last_request_id
            conversations_with_expanded_view_last_request_ids.append(last_request_id)

    # Collect token statistics from all conversations
    for conversation in conversations:
        # Get token statistics for this conversation
        conv_stats = conversation.get_token_statistics()

        # Skip if conv_stats is not a dictionary
        if not isinstance(conv_stats, dict):
            continue

        # Update total tokens
        total_tokens_value = conv_stats.get("total_tokens")
        if isinstance(total_tokens_value, int):
            total_tokens += total_tokens_value

        # Update text token stats
        text_stats = conv_stats.get("text", {})
        if isinstance(text_stats, dict):
            text_token_stats["user_messages_tokens"] += text_stats.get(
                "user_messages_tokens", 0
            )
            text_token_stats["agent_messages_tokens"] += text_stats.get(
                "agent_messages_tokens", 0
            )

        # Update tool token stats
        tools_data = conv_stats.get("tools", {})
        if isinstance(tools_data, dict):
            for tool_name, tool_stats in tools_data.items():
                if tool_name not in tool_token_stats:
                    tool_token_stats[tool_name] = {
                        "prompt_tokens": 0,  # Tokens sent TO the model (tool outputs)
                        "completion_tokens": 0,  # Tokens generated BY the model (tool inputs)
                        "call_count": 0,
                    }

                if isinstance(tool_stats, dict):
                    prompt_tokens = tool_stats.get("prompt_tokens", 0)
                    completion_tokens = tool_stats.get("completion_tokens", 0)
                    call_count = tool_stats.get("call_count", 0)

                    tool_token_stats[tool_name]["prompt_tokens"] += prompt_tokens
                    tool_token_stats[tool_name]["completion_tokens"] += (
                        completion_tokens
                    )
                    tool_token_stats[tool_name]["call_count"] += call_count

                # Extract command-specific stats for str-replace-editor
                if tool_name == "str-replace-editor":
                    # Check if tool_stats is a dictionary and has a 'commands' key
                    if isinstance(tool_stats, dict) and "commands" in tool_stats:
                        command_stats = tool_stats.get("commands", {})

                        # Update command-specific stats
                        for cmd in ["view", "str_replace", "insert"]:
                            if cmd in command_stats and isinstance(
                                command_stats[cmd], dict
                            ):
                                cmd_stats = command_stats[cmd]
                                prompt_tokens = (
                                    cmd_stats.get("prompt_tokens", 0)
                                    if isinstance(cmd_stats, dict)
                                    else 0
                                )
                                completion_tokens = (
                                    cmd_stats.get("completion_tokens", 0)
                                    if isinstance(cmd_stats, dict)
                                    else 0
                                )
                                call_count = (
                                    cmd_stats.get("call_count", 0)
                                    if isinstance(cmd_stats, dict)
                                    else 0
                                )

                                str_replace_editor_command_stats[cmd][
                                    "prompt_tokens"
                                ] += prompt_tokens
                                str_replace_editor_command_stats[cmd][
                                    "completion_tokens"
                                ] += completion_tokens
                                str_replace_editor_command_stats[cmd]["call_count"] += (
                                    call_count
                                )

    # Calculate percentages for text tokens
    if total_tokens > 0:
        text_token_stats["user_messages_tokens_pct"] = (
            text_token_stats["user_messages_tokens"] / total_tokens
        ) * 100
        text_token_stats["agent_messages_tokens_pct"] = (
            text_token_stats["agent_messages_tokens"] / total_tokens
        ) * 100

        # Calculate percentages for tool tokens
        for tool_name in tool_token_stats:
            tool_token_stats[tool_name]["prompt_tokens_pct"] = (
                tool_token_stats[tool_name]["prompt_tokens"] / total_tokens
            ) * 100
            tool_token_stats[tool_name]["completion_tokens_pct"] = (
                tool_token_stats[tool_name]["completion_tokens"] / total_tokens
            ) * 100

        # Calculate percentages for str-replace-editor command-specific tokens
        for cmd in str_replace_editor_command_stats:
            str_replace_editor_command_stats[cmd]["prompt_tokens_pct"] = (
                (str_replace_editor_command_stats[cmd]["prompt_tokens"] / total_tokens)
                * 100
                if total_tokens > 0
                else 0.0
            )
            str_replace_editor_command_stats[cmd]["completion_tokens_pct"] = (
                (
                    str_replace_editor_command_stats[cmd]["completion_tokens"]
                    / total_tokens
                )
                * 100
                if total_tokens > 0
                else 0.0
            )

    # Calculate costs
    # Calculate total cost
    # Note: prompt_tokens are tokens sent TO the model (user messages + tool outputs)
    # completion_tokens are tokens generated BY the model (agent messages + tool inputs)
    calculated_total_cost = calculate_claude_cost(
        # Prompt tokens: user messages + tool outputs (tool.prompt_tokens)
        text_token_stats["user_messages_tokens"]
        + sum(tool_stats["prompt_tokens"] for tool_stats in tool_token_stats.values()),
        # Completion tokens: agent messages + tool inputs (tool.completion_tokens)
        text_token_stats["agent_messages_tokens"]
        + sum(
            tool_stats["completion_tokens"] for tool_stats in tool_token_stats.values()
        ),
    )

    # Calculate cost for each tool
    for tool_name, stats in tool_token_stats.items():
        tool_costs[tool_name] = calculate_claude_cost(
            stats["prompt_tokens"],  # Tokens sent TO the model (tool outputs)
            stats["completion_tokens"],  # Tokens generated BY the model (tool inputs)
        )

    # Calculate cost for each str-replace-editor command
    for cmd, stats in str_replace_editor_command_stats.items():
        str_replace_editor_command_costs[cmd] = calculate_claude_cost(
            stats["prompt_tokens"],  # Tokens sent TO the model (tool outputs)
            stats["completion_tokens"],  # Tokens generated BY the model (tool inputs)
        )

    # Calculate cost percentages
    if calculated_total_cost > 0:
        # Calculate cost percentage for each tool
        for tool_name, cost in tool_costs.items():
            tool_token_stats[tool_name]["cost_pct"] = (
                cost / calculated_total_cost
            ) * 100

        # Calculate cost percentage for each str-replace-editor command
        for cmd, cost in str_replace_editor_command_costs.items():
            str_replace_editor_command_stats[cmd]["cost_pct"] = (
                cost / calculated_total_cost
            ) * 100

    return StrReplaceEditorAnalysisSummary(
        total_calls=total_calls,
        success_rate=success_rate,
        command_counts=command_counts,
        view_range_len_hist=view_range_len_hist,
        str_replace_entries_hist=str_replace_entries_hist,
        first_view_count=first_view_count,
        should_not_use_view_range_count=should_not_use_view_range_count,
        should_not_use_view_range_percentage=should_not_use_view_range_percentage,
        should_not_use_view_range_request_ids=should_not_use_view_range_request_ids,
        first_in_seq_of_views_count=first_in_seq_of_views_count,
        first_in_seq_of_views_request_ids=first_in_seq_of_views_request_ids,
        last_in_seq_of_views_count=last_in_seq_of_views_count,
        last_in_seq_of_views_request_ids=last_in_seq_of_views_request_ids,
        view_sequence_pairs=view_sequence_pairs,
        previously_retrieved_view_count=previously_retrieved_view_count,
        previously_retrieved_view_request_ids=previously_retrieved_view_request_ids,
        large_str_replace_request_ids=large_str_replace_request_ids,
        view_expanded_count=view_expanded_count,
        view_expanded_percentage=view_expanded_percentage,
        view_expanded_request_ids=view_expanded_request_ids,
        conversations_with_expanded_view_last_request_ids=conversations_with_expanded_view_last_request_ids,
        subrange_of_expanded_view_count=subrange_of_expanded_view_count,
        subrange_of_expanded_view_request_ids=subrange_of_expanded_view_request_ids,
        error_counts=error_counts,
        error_request_ids=error_request_ids,
        error_response_request_ids=error_response_request_ids,
        error_request_id_pairs=error_request_id_pairs,
        empty_last_turn_count=empty_last_turn_count,
        empty_last_turn_request_ids=empty_last_turn_request_ids,
        augment_tag_count=augment_tag_count,
        augment_tag_request_ids=augment_tag_request_ids,
        # Token statistics
        total_tokens=total_tokens,
        tool_token_stats=tool_token_stats,
        text_token_stats=text_token_stats,
        str_replace_editor_command_stats=str_replace_editor_command_stats,
        # New token stats
        tool_to_token_stats=tool_to_token_stats,
        sum_token_stats=sum_token_stats,
        # Cost information
        total_cost=calculated_total_cost,
        tool_costs=tool_costs,
        str_replace_editor_command_costs=str_replace_editor_command_costs,
        from_date=from_date,
        to_date=to_date,
    )


def print_summary(summary: StrReplaceEditorAnalysisSummary):
    print("\n===== str-replace-editor Tool Analysis =====\n")

    # Print date range if available
    if summary.from_date and summary.to_date:
        print(
            f"Date range: {summary.from_date.strftime('%Y-%m-%d %H:%M:%S')} to {summary.to_date.strftime('%Y-%m-%d %H:%M:%S')}"
        )
    elif summary.from_date:
        print(f"From date: {summary.from_date.strftime('%Y-%m-%d %H:%M:%S')}")
    elif summary.to_date:
        print(f"To date: {summary.to_date.strftime('%Y-%m-%d %H:%M:%S')}")

    print(f"Total calls: {summary.total_calls}")
    print(f"Success rate: {summary.success_rate:.2%}")
    print(f"First view count: {summary.first_view_count}")
    print(f"Should not use view range count: {summary.should_not_use_view_range_count}")
    print(
        f"Should not use view range percentage: {summary.should_not_use_view_range_percentage:.2f}%"
    )
    print(f"View expanded count: {summary.view_expanded_count}")
    print(f"View expanded percentage: {summary.view_expanded_percentage:.2f}%")
    print(f"Subrange of expanded view count: {summary.subrange_of_expanded_view_count}")
    print(f"First in sequence of views count: {summary.first_in_seq_of_views_count}")
    print(f"Last in sequence of views count: {summary.last_in_seq_of_views_count}")
    print(f"View sequence pairs count: {len(summary.view_sequence_pairs)}")
    print(
        f"Views of previously retrieved files count: {summary.previously_retrieved_view_count}"
    )

    # Print token statistics
    print("\nToken Statistics:")
    print(f"Total tokens: {summary.total_tokens:,}")
    print(f"Total cost: {format_cost(summary.total_cost)}")

    # Print text token statistics
    print("\nText Token Statistics:")
    print(
        f"  User messages tokens: {summary.text_token_stats.get('user_messages_tokens', 0):,} ({summary.text_token_stats.get('user_messages_tokens_pct', 0):.1f}%)"
    )
    print(
        f"  Agent messages tokens: {summary.text_token_stats.get('agent_messages_tokens', 0):,} ({summary.text_token_stats.get('agent_messages_tokens_pct', 0):.1f}%)"
    )

    # Print tool token statistics
    print("\nTool Token Statistics:")
    for tool_name, stats in sorted(
        summary.tool_token_stats.items(),
        key=lambda x: x[1].get("prompt_tokens", 0) + x[1].get("completion_tokens", 0),
        reverse=True,
    ):
        prompt_tokens = stats.get(
            "prompt_tokens", 0
        )  # Tokens sent TO the model (tool outputs)
        completion_tokens = stats.get(
            "completion_tokens", 0
        )  # Tokens generated BY the model (tool inputs)
        prompt_pct = stats.get("prompt_tokens_pct", 0)
        completion_pct = stats.get("completion_tokens_pct", 0)
        call_count = stats.get("call_count", 0)
        tool_cost = summary.tool_costs.get(tool_name, 0.0)

        print(f"  {tool_name}:")
        print(
            f"    Prompt tokens (TO model - tool outputs): {prompt_tokens:,} ({prompt_pct:.1f}%)"
        )
        print(
            f"    Completion tokens (FROM model - tool inputs): {completion_tokens:,} ({completion_pct:.1f}%)"
        )
        print(
            f"    Total tokens: {prompt_tokens + completion_tokens:,} ({prompt_pct + completion_pct:.1f}%)"
        )
        cost_pct = stats.get("cost_pct", 0.0)
        print(f"    Cost: {format_cost(tool_cost)} ({cost_pct:.1f}% of total)")
        print(f"    Call count: {call_count}")
        if call_count > 0:
            print(f"    Avg prompt tokens per call: {prompt_tokens / call_count:.1f}")
            print(
                f"    Avg completion tokens per call: {completion_tokens / call_count:.1f}"
            )
            print(f"    Avg cost per call: {format_cost(tool_cost / call_count)}")

        # Print command-specific token statistics for str-replace-editor
        if tool_name == "str-replace-editor":
            print("\n    Command-specific Token Statistics:")
            for cmd, cmd_stats in sorted(
                summary.str_replace_editor_command_stats.items(),
                key=lambda x: x[1].get("prompt_tokens", 0)
                + x[1].get("completion_tokens", 0),
                reverse=True,
            ):
                cmd_prompt_tokens = cmd_stats.get(
                    "prompt_tokens", 0
                )  # Tokens sent TO the model (tool outputs)
                cmd_completion_tokens = cmd_stats.get(
                    "completion_tokens", 0
                )  # Tokens generated BY the model (tool inputs)
                cmd_prompt_pct = cmd_stats.get("prompt_tokens_pct", 0)
                cmd_completion_pct = cmd_stats.get("completion_tokens_pct", 0)
                cmd_call_count = cmd_stats.get("call_count", 0)
                cmd_cost = summary.str_replace_editor_command_costs.get(cmd, 0.0)

                print(f"      {cmd}:")
                print(
                    f"        Prompt tokens (TO model - tool outputs): {cmd_prompt_tokens:,} ({cmd_prompt_pct:.1f}%)"
                )
                print(
                    f"        Completion tokens (FROM model - tool inputs): {cmd_completion_tokens:,} ({cmd_completion_pct:.1f}%)"
                )
                print(
                    f"        Total tokens: {cmd_prompt_tokens + cmd_completion_tokens:,} ({cmd_prompt_pct + cmd_completion_pct:.1f}%)"
                )
                cmd_cost_pct = cmd_stats.get("cost_pct", 0.0)
                print(
                    f"        Cost: {format_cost(cmd_cost)} ({cmd_cost_pct:.1f}% of total)"
                )
                print(f"        Call count: {cmd_call_count}")
                if cmd_call_count > 0:
                    print(
                        f"        Avg prompt tokens per call: {cmd_prompt_tokens / cmd_call_count:.1f}"
                    )
                    print(
                        f"        Avg completion tokens per call: {cmd_completion_tokens / cmd_call_count:.1f}"
                    )
                    print(
                        f"        Avg cost per call: {format_cost(cmd_cost / cmd_call_count)}"
                    )

    # Print error statistics
    if summary.error_counts:
        print("\nError statistics:")
        total_errors = sum(summary.error_counts.values())
        for error_type, count in sorted(
            summary.error_counts.items(), key=lambda x: x[1], reverse=True
        ):
            percentage = count / total_errors * 100 if total_errors > 0 else 0
            print(f"  {error_type}: {count} ({percentage:.1f}%)")

        # Print error request ID pairs
        print("\nError request ID pairs:")
        for error_type, pairs in summary.error_request_id_pairs.items():
            if pairs:
                print(f"  {error_type}: {len(pairs)} pairs")
                for pair in pairs[
                    :3
                ]:  # Show only the first 3 pairs to avoid cluttering the console
                    error_msg = pair[2][:100] + "..." if len(pair[2]) > 100 else pair[2]
                    print(f"    Tool use: {pair[0]}, Tool response: {pair[1]}")
                    if error_msg:
                        print(f"    Error: {error_msg}")
                if len(pairs) > 3:
                    print(f"    ... and {len(pairs) - 3} more")

    if summary.should_not_use_view_range_request_ids:
        print("\nViews where we should not have used view range request IDs:")
        print(json.dumps(summary.should_not_use_view_range_request_ids, indent=2))

    if summary.first_in_seq_of_views_request_ids:
        print("\nViews that are first in a sequence of consecutive view calls:")
        print(json.dumps(summary.first_in_seq_of_views_request_ids, indent=2))

    if summary.last_in_seq_of_views_request_ids:
        print("\nViews that are last in a sequence of consecutive view calls:")
        print(json.dumps(summary.last_in_seq_of_views_request_ids, indent=2))

    if summary.view_sequence_pairs:
        print("\nView sequence pairs (first_request_id, last_request_id):")
        print(json.dumps(summary.view_sequence_pairs, indent=2))

    if summary.large_str_replace_request_ids:
        print("\nStr replace calls with more than 5 entries:")
        print(json.dumps(summary.large_str_replace_request_ids, indent=2))

    if summary.previously_retrieved_view_request_ids:
        print("\nViews of previously retrieved files request IDs:")
        print(json.dumps(summary.previously_retrieved_view_request_ids, indent=2))

    if summary.view_expanded_request_ids:
        print("\nViews where the view range was expanded request IDs:")
        print(json.dumps(summary.view_expanded_request_ids, indent=2))

    if summary.subrange_of_expanded_view_request_ids:
        print(
            "\nViews where the current view_range is a subrange of previous expanded view range:"
        )
        print(json.dumps(summary.subrange_of_expanded_view_request_ids, indent=2))

    if summary.conversations_with_expanded_view_last_request_ids:
        print(
            "\nLast request IDs for conversations with at least one expanded view call:"
        )
        print(
            json.dumps(
                summary.conversations_with_expanded_view_last_request_ids, indent=2
            )
        )

    if summary.empty_last_turn_request_ids:
        print(
            "\nRequest IDs for last turns in a conversation round where the message is empty and has no tool use:"
        )
        print(f"Total count: {summary.empty_last_turn_count}")
        print(json.dumps(summary.empty_last_turn_request_ids, indent=2))

    if summary.augment_tag_request_ids:
        print('\nRequest IDs for turns where the response contains "[/AUGMENT]":')
        print(f"Total count: {summary.augment_tag_count}")
        print(json.dumps(summary.augment_tag_request_ids, indent=2))

    print("\nCommand distribution:")
    for command, count in sorted(
        summary.command_counts.items(), key=lambda x: x[1], reverse=True
    ):
        percentage = count / summary.total_calls * 100 if summary.total_calls > 0 else 0
        print(f"  {command}: {count} ({percentage:.1f}%)")

    print("\nView range length histogram:")
    for i, bucket in enumerate(VIEW_RANGE_LEN_BUCKETS):
        count = summary.view_range_len_hist[i]
        if i == len(VIEW_RANGE_LEN_BUCKETS) - 1 or bucket == -1:
            print(f"  > {VIEW_RANGE_LEN_BUCKETS[i-1]:4} lines: {count:4}")
        elif i == 0:
            print(f"  <= {bucket:4} lines: {count:4}")
        else:
            print(f"  {VIEW_RANGE_LEN_BUCKETS[i-1]:4} < lines <= {bucket:4}: {count:4}")

    print("\nStr replace entries histogram:")
    for i, bucket in enumerate(STR_REPLACE_ENTRIES_BUCKETS):
        count = summary.str_replace_entries_hist[i]
        if i == len(STR_REPLACE_ENTRIES_BUCKETS) - 1 or bucket == -1:
            print(f"  > {STR_REPLACE_ENTRIES_BUCKETS[i-1]:4} entries: {count:4}")
        elif i == 0:
            print(f"  <= {bucket:4} entries: {count:4}")
        else:
            print(
                f"  {STR_REPLACE_ENTRIES_BUCKETS[i-1]:4} < entries <= {bucket:4}: {count:4}"
            )

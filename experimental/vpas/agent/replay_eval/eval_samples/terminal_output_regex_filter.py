from experimental.vpas.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls
from typing import <PERSON><PERSON>
from base.third_party_clients.third_party_model_client import Tool<PERSON><PERSON><PERSON>, ToolChoiceType


samples = [
    "d04e433b-2323-4c7e-b563-c626c04f29b7",
    "8c0aba55-4e71-4bb2-bdb7-848744d1d2f3",
    "dad3236b-821f-4031-a75b-5f3ea5b5afdb",
    "a5b0a04b-9cc7-48e7-8946-9658fc59e9e0",
    "76b7223c-9990-4b9c-82f5-205875a8645a",
    "99e0cafa-ffd6-4d23-abc9-f2ab1b8a410f",
    "a8478b00-2db7-49d0-b2c6-75c868a557a8",
    "0a0c16a9-677f-4fd6-ba17-7902e3516a37",
    "818601cf-4ec0-4810-8696-3cf11813833c",
    "165f51e9-15da-475a-87c3-c6fe36124c89",
    "75dcaa51-de87-47dd-8df8-347939e63c94",
    "1533c16e-f622-43d1-82e8-f49b74564d1d",
    "ec804957-feff-4a79-9bfd-d1d9738ae4a6",
    "6b744b54-c889-455b-8f2e-034eb3ca621a",
    "986de913-dec3-4eae-966b-a49e851efa25",
    "824cedda-7f0e-4afc-bdfb-cff51e3bd42d",
    "2ab75e70-735f-456e-ae0e-039fa724b5b4",
    "fe83de76-20ab-4509-9c66-b1e4f3c29582",
    "7b3671d0-ccf2-4b37-a037-98b60d2d4df4",
    "6bb64247-129a-431d-b4bb-4b62754b2bde",
    "d6012ded-51c6-4a15-9ab8-767fcc3f75c4",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    for tool_call in tool_calls:
        if tool_call.name == "launch-process":
            if tool_call.input.get("output_regex_filter") is not None:
                return True, ""
    return False, "No launch-process tool calls with output_regex_filter found"


samples = [
    EvalSample(
        request_id=sample,
        name=f"terminal_output_regex_filter_{index}",
        category="terminal_output_regex_filter",
        assistant_message_prefill_from_response=True,
        tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="launch-process"),
        eval_response_func=eval_func,
    )
    for index, sample in enumerate(samples)
]

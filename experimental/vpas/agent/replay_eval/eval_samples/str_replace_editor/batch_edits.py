# str_replace calls with more than 5 entries in original request
import math
from typing import <PERSON><PERSON>
from base.prompt_format.common import ToolDefinition
from base.third_party_clients.third_party_model_client import Tool<PERSON><PERSON><PERSON>, ToolChoiceType
from experimental.vpas.agent.replay_eval.eval_functions import (
    calls_tool_with_valid_input,
)
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.str_replace_editor_utils import (
    call_str_replace_editor_any_schema,
    extract_str_replace_entries_any_schema,
)
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls
from experimental.vpas.agent.str_replace_editor.extract_testcase_from_request import (
    extract_tool_input_and_content,
)


samples = [
    "0c561a59-6848-482e-a6a4-4711115bfff6",
    "3ba39b2f-2027-41d2-bcba-e54bbdbaa2bb",
    "8a2ef1e8-ee3a-4f20-afd6-c84a2184805e",
    "9a7dd848-6d3a-4cd1-aa0b-e96536081ee3",
    "aaf4e56c-04e6-4a45-892d-da470adaf4ed",
    "3367ca1c-185e-47ea-90d7-af5b4ebc9f18",
    "d1e48765-aea8-40aa-87c9-6d765e858b37",
    "9a91f3d2-5ec6-4f54-a2e2-47f7d8317b50",
    "8a088c01-8d98-43ad-95aa-9ee6f552ac1f",
    "2ccab35b-3bff-4af0-a5f5-c21d7c72a7fa",
    "7c307654-088b-49e7-b02a-30d3c56f8720",
    "881baad1-ee34-4a82-b7bc-12ad4dd69d8e",
    "3b2f40fd-52f0-426c-a37d-47b02ccd1e73",
    "1a77cce5-ca71-457b-b84a-9915a49912f9",
]


def count_lines_in_entry(entry: dict) -> int:
    """Count the total number of lines in both old_str and new_str of an entry."""
    old_str_lines = (
        entry.get("old_str", "").count("\n") + 1 if entry.get("old_str") else 0
    )
    new_str_lines = (
        entry.get("new_str", "").count("\n") + 1 if entry.get("new_str") else 0
    )
    return old_str_lines + new_str_lines


def count_total_lines(entries: list[dict]) -> int:
    """Count the total number of lines across all entries."""
    return sum(count_lines_in_entry(entry) for entry in entries)


def eval_response_func(
    response: ModelResponse,
    tool_definitions: list[ToolDefinition],
    request_id: str,
) -> Tuple[bool, str]:
    res, message = calls_tool_with_valid_input("str-replace-editor")(
        response, tool_definitions
    )
    if not res:
        return res, message

    tool_input = extract_tool_calls(response)[0].input
    if tool_input.get("command") != "str_replace":
        return False, "Response does not use the 'str_replace' command"
    str_replace_entries = extract_str_replace_entries_any_schema(tool_input)
    if str_replace_entries is None:
        return False, "No str_replace entries found in tool input"

    original_tool_input, _ = extract_tool_input_and_content(request_id)
    assert original_tool_input is not None
    original_str_replace_entries = extract_str_replace_entries_any_schema(
        original_tool_input
    )
    assert original_str_replace_entries is not None
    assert len(original_str_replace_entries) > 0

    # Count total lines in original and response entries
    original_total_lines = count_total_lines(original_str_replace_entries)
    response_total_lines = count_total_lines(str_replace_entries)

    # Require at least 70% of the original total lines
    min_expected_lines = math.ceil(original_total_lines * 0.7)

    if response_total_lines < min_expected_lines:
        return (
            False,
            f"Only found {response_total_lines} total lines across all entries (original had {original_total_lines} lines)",
        )

    # success, output = call_str_replace_editor_any_schema(request_id, tool_input)
    # if not success:
    #     return False, output

    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"str_replace_editor_batch_edits_{index}",
        eval_response_func=eval_response_func,
        assistant_message_prefill_from_response=True,
        tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="str-replace-editor"),
        category="batch_edits",
    )
    for index, sample in enumerate(samples)
]

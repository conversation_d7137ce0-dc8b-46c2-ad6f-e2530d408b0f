from experimental.vpas.agent.replay_eval.eval_functions import (
    calls_tool_with_valid_input,
    contains_one_of_tool_calls,
    eval_chain,
    num_output_tokens_less_than,
)
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample

samples = [
    (
        "67dd3d51-1a4c-449f-b932-2e9099e99abf",
        """
        """.strip(),
        "https://linear.app/augmentcode/issue/AU-8049/running-out-of-output-tokens-when-calling-str-replace-editor",
    ),
]

samples = [
    EvalSample(
        request_id=sample[0],
        name=f"str_replace_editor_output_tokens_limit_small_{index}",
        eval_response_func=eval_chain(
            contains_one_of_tool_calls(["str-replace-editor"]),
            num_output_tokens_less_than(250),
        ),
        assistant_message_prefill=sample[1],
        linear_url=sample[2],
    )
    for index, sample in enumerate(samples)
]

import experimental.vpas.agent.replay_eval.eval_samples.augment_closing_tag as augment_closing_tags
import experimental.vpas.agent.replay_eval.eval_samples.empty_response as empty_responses
import experimental.vpas.agent.replay_eval.eval_samples.repeating_message as repeating_message
import experimental.vpas.agent.replay_eval.eval_samples.str_replace_editor.output_tokens_limit_all as output_tokens_limit_all

samples = (
    output_tokens_limit_all.samples
    + augment_closing_tags.samples
    + empty_responses.samples
    + repeating_message.samples
)

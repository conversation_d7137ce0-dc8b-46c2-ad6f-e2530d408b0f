from typing import <PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON><PERSON>
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls

samples = [
    "8a5d4a31-2b09-4c12-a2ed-7b117a2c4fa5",
]


def eval_response_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    if (
        len(tool_calls) == 1
        and tool_calls[0] is not None
        and tool_calls[0].name == "codebase-retrieval"
    ):
        return False, "Response incorrectly uses codebase-retrieval tool"
    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"should_not_use_retrieval_{index}",
        eval_response_func=eval_response_func,
        assistant_message_prefill_from_response=True,
    )
    for index, sample in enumerate(samples)
]

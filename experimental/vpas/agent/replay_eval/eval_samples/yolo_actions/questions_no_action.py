from typing import <PERSON><PERSON>

from experimental.vpas.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON>ample
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls

samples = [
    (
        "97ea79bb-c487-491f-85f6-4c2ad9a40ab5",
        "",
        "https://linear.app/augmentcode/issue/AU-8093/agent-should-not-take-actions-when-asked-a-question",
    ),
]


def eval_response_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    for tool_call in tool_calls:
        if tool_call.name == "str-replace-editor":
            if tool_call.input.get("command") == "str_replace":
                return (
                    False,
                    "Response shouldn't be editing files in response to question",
                )
    return True, ""


samples = [
    EvalSample(
        request_id=sample[0],
        name=f"questions_no_action_{index}",
        eval_response_func=eval_response_func,
        assistant_message_prefill=sample[1],
        linear_url=sample[2],
    )
    for index, sample in enumerate(samples)
]

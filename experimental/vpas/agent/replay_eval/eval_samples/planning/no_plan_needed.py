from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.eval_functions import not_contain_keywords

samples = [
    (
        "cf90cbea-48e0-4af9-8385-d166e420e6a3",
        "",
        "https://linear.app/augmentcode/issue/AU-7986/agent-repeats-user-given-plan-and-asks-for-approval",
    ),
    (
        "6859308a-1a9f-4095-a449-585cca6c91ed",
        "",
        "https://linear.app/augmentcode/issue/AU-7870/agent-overly-eager-to-plan-instead-of-executing-tasks-directly",
    ),
]


samples = [
    EvalSample(
        request_id=sample[0],
        name=f"no_plan_needed_{index}",
        eval_response_func=not_contain_keywords(["plan"]),
        assistant_message_prefill=sample[1],
        linear_url=sample[2],
    )
    for index, sample in enumerate(samples)
]

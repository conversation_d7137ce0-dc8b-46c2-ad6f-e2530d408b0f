from experimental.vpas.agent.replay_eval.eval_sample import EvalS<PERSON>
from experimental.vpas.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)

samples = [
    # "e92ddde8-9dd9-4d09-96dd-b0e868467fac",
    "8450ed60-4410-416c-b2aa-21cd0beb2329",
    "17197109-a892-4451-9d04-9cb85b169466",
    "af7c2dbe-abbe-4f59-81d5-cf84fc784c82",
]

samples = [
    EvalSample(
        request_id=sample,
        name=f"should_use_find_definition_{index}",
        category="should_use_find_definition",
        eval_response_func=contains_one_of_tool_calls(["find-definition"]),
    )
    for index, sample in enumerate(samples)
]

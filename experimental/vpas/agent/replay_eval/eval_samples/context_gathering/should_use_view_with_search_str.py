from typing import <PERSON><PERSON>

from experimental.vpas.agent.replay_eval.eval_sample import Eva<PERSON><PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_functions import (
    extract_tool_calls,
    ModelResponse,
)

samples = [
    "8450ed60-4410-416c-b2aa-21cd0beb2329",
    "17197109-a892-4451-9d04-9cb85b169466",
    "af7c2dbe-abbe-4f59-81d5-cf84fc784c82",
    "0920065a-7cdf-41ca-b989-be36633da331",
    "b58b2dd5-24e7-483b-ae33-1e08d7765cb8",
    "1d7629e4-5f15-487e-bcc7-9bc61354b660",
    "ef5be4d1-f596-4fd2-ba29-bc9c1d77f8f3",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    for tool_call in tool_calls:
        if tool_call.name == "view":
            if tool_call.input.get("search_str") is not None:
                return True, ""
    return False, "No view tool calls with search_str found"


samples = [
    EvalSample(
        request_id=sample,
        name=f"should_use_view_with_search_str_{index}",
        category="should_use_view_with_search_str",
        eval_response_func=eval_func,
    )
    for index, sample in enumerate(samples)
]

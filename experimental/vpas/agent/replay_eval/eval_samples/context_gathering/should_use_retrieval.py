from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)

samples = [
    "8412f544-27e3-4053-a761-ec40cef7b745",
    "ff8ba669-3fbe-429e-89e8-0aae04b147fe",
    "99d6969c-63c0-4103-a6d5-1088f1d58112",
    "f641bf16-da38-4473-a2c7-71e467b77d58",
]

samples = [
    EvalSample(
        request_id=sample,
        name=f"should_use_retrieval_{index}",
        category="should_use_retrieval",
        eval_response_func=contains_one_of_tool_calls(["codebase-retrieval"]),
    )
    for index, sample in enumerate(samples)
]

from experimental.vpas.agent.replay_eval.eval_sample import Eval<PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)

samples = [
    "0920065a-7cdf-41ca-b989-be36633da331",
    "b58b2dd5-24e7-483b-ae33-1e08d7765cb8",
    "1d7629e4-5f15-487e-bcc7-9bc61354b660",
    "ef5be4d1-f596-4fd2-ba29-bc9c1d77f8f3",
]

samples = [
    EvalSample(
        request_id=sample,
        name=f"should_use_grep_{index}",
        category="should_use_grep",
        eval_response_func=contains_one_of_tool_calls(["grep-search"]),
    )
    for index, sample in enumerate(samples)
]

from experimental.vpas.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)

samples = [
    "48582d8f-a10d-4a7b-81e8-842b8f83044f",
    "2a003ff4-37be-4438-9f25-8a48f68e57ed",
    "9d9fcc2e-8ac2-4d7e-b134-3a44ea61fc1e",
    "392ff02b-fb6f-4480-8cf6-07c9fac67749",
    "1a98447e-11d7-4490-a219-37c796b9886f",
    "51b03a6e-2d9c-476f-b0b8-17df50a35061",
]

samples = [
    EvalSample(
        request_id=sample,
        name=f"should_use_view_{index}",
        category="should_use_view",
        eval_response_func=contains_one_of_tool_calls(["view"]),
    )
    for index, sample in enumerate(samples)
]

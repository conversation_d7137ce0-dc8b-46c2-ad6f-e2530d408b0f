from typing import Iterable, <PERSON><PERSON>
from base.prompt_format.common import ChatResultNode, ChatResultNodeType, ToolDefinition
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls

samples = [
    "524e3718-3610-4208-b38a-23561825537a",
    "2217b268-16be-445f-811f-0290b541285f",
    "55229bcf-b3af-4f57-83d3-a4b3970a42e8",
    "aae0e78f-bc25-4ce0-80ef-dfe90b9cf2fc",
    "ffb31577-1d72-4ed7-a08c-cf59976b95f9",
    "6f79ae6f-db2d-4efd-a085-bd2c23e1ce10",
    "b913f26d-886b-4b5b-b166-bbb1a20f75a8",
    "b97c0d56-78a3-4332-86dd-511309aa01f4",
    "78e51e32-dc89-4460-bf18-23c865806b19",
    "997c6b3b-7004-4371-a4f5-117dc0e688c2",
    "36bbe947-82e0-42bb-b416-97d30fe15e0c",
    "4bae7b27-bdc7-4a3b-93fe-1330756a2ff0",
    "cbb733b0-13ba-4ed0-aa3f-80fc1841bd73",
    "d08b4913-9410-45b3-aad9-ec1899a1ff0b",
    "241abd86-62ee-4e63-a681-7656c2b4e2d1",
    "0cf46603-ec04-422d-b504-d516795d42d7",
    "6c970b23-fa65-4709-a001-f3f07cc34325",
    "a1da47ad-f28d-47ff-9da5-0d145b911446",
    "c6b94b5f-5eff-413e-902b-9b6a7920747e",
    "de031c43-7b3f-438a-ba25-ec726404bbcd",
    "eb07f971-f969-40c8-be97-3ae5bc5976c5",
    "34f52711-e52c-40e1-bad3-c03fc58469f4",
    "d6d52c69-8059-4430-ade2-a717e3593514",
    "6f7285bb-9d26-4d8a-8027-351e11c1bf3b",
    "086b740d-6811-4e48-903b-b3682c4a45e6",
    "fec423b8-97ae-412e-aafd-f23e57b7cd97",
    "a9764167-fe54-46d9-8db8-4dd68e20789a",
    "57c3197a-c13f-42cd-b901-a85b06ac002c",
    "3c409c7d-902b-45f0-ba6a-9ef4cb13676d",
    "003c6ef7-a3e7-4b7c-89c5-c47003b765dc",
    "e671ccae-6a27-4c1f-af0b-fbf260c28775",
    "493bb5f0-02ce-4753-a7de-350324c70fcb",
    "e40763a8-c59a-4b33-9c94-8e845405f3e5",
    "d9bec7d8-418a-43cb-a50f-98179072155b",
    "6e2c6d35-0a71-4d0e-863e-4b63a97c89db",
    "2839aeaa-5044-4acd-abb2-3e5e01ea28cb",
    "d49ff389-c238-42a4-9977-a2192524112a",
    "af0c213b-6572-4f68-adf6-79c6265d3260",
    "46b97daa-9f59-4636-ab10-7a45758cd336",
    "05d0f780-fd9f-496c-927a-1322a7e99f6e",
    "9279bc52-0742-4076-9bf4-7ba1d1afde6a",
    "bfbf7cea-4a6b-459b-ae81-4ebdc6d57e3c",
    "84b567dd-e559-491d-b81c-182905248161",
    "821f48ac-7dfe-48d6-8182-55719b4fa4b7",
    "965fbda1-6cd1-4446-8967-ace76155cd6b",
    "3ac0b9a0-038c-4fcd-9e1a-1f741af8d5d5",
    "14d634ca-60e8-402c-807d-327ef58cf190",
    "b807b974-4a51-477f-9eea-82f026400fab",
    "3a3b0398-3761-4109-bb69-b6964535271b",
    "6d32d021-ef1d-4bde-a99f-39f247020af2",
    "7c1ad0f2-d886-4e6b-b53c-6b3bae848ba6",
]

# small subset of consistent empty responses
# samples = [
#     "997c6b3b-7004-4371-a4f5-117dc0e688c2",
#     "241abd86-62ee-4e63-a681-7656c2b4e2d1",
#     "0cf46603-ec04-422d-b504-d516795d42d7",
#     "6c970b23-fa65-4709-a001-f3f07cc34325",
#     "eb07f971-f969-40c8-be97-3ae5bc5976c5",
# ]

# empty responses for April 17-18
samples = [
    "400593b8-e133-40ce-9e5b-1eed2cdb2b3b",
    "a25d9c0e-bd79-443d-9679-11d94099057e",
    "66a989f2-63f0-4e99-b5f2-7986a4262bce",
    "fc234cb7-c32b-49ab-bb66-277a4265caf9",
    "b1541ed5-7fa9-499c-ba55-19c477257181",
    "f9d975b0-762b-45d8-8ac4-bed605ae8c0d",
    "11199cd0-bd7f-46ed-818c-adef5d40742e",
    "d871563f-e029-49ae-99ee-f91e3b28be3f",
    "7c66ebd9-2ccf-495a-89cd-30bacd0eb93a",
    "dec808ab-c60b-4737-a992-3f34cee5051c",
    "3b1d22e3-4800-4b22-9bc1-d9064f98b98f",
    "fb54565e-4a74-42ea-b683-b2c19957fdf4",
]


def extract_text_from_response(chat_result_nodes: Iterable[ChatResultNode]) -> str:
    text = ""
    for node in chat_result_nodes:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            text += node.content
    return text


def eval_func(
    response: ModelResponse,
    tool_definitions: list[ToolDefinition],
    request_id: str,
) -> Tuple[bool, str]:
    cur_response_text = extract_text_from_response(response).strip()
    tool_calls = extract_tool_calls(response)
    if len(tool_calls) == 0 and not cur_response_text:
        return False, "Empty response"

    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"empty_response_{index}",
        eval_response_func=eval_func,
        category="empty_response",
        linear_url="https://augment-wic8570.slack.com/archives/C088KC6GXFB/p1744744309073369",
    )
    for index, sample in enumerate(samples)
]

# cases where model generates [/AUGMENT]

from typing import <PERSON><PERSON>
from experimental.vpas.agent.replay_eval.eval_sample import Eval<PERSON>ample
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_text

samples = [
    "7e8106df-4ab5-46af-bd03-d5442607322b",
    "f3d7c487-8cf2-4f76-9aa9-bd97afc7061a",
    "970d78aa-3e62-41bb-9348-90f32076a414",
    "777bae50-15ef-46ba-a827-2664d335d2b2",
    "0d52bca6-10e3-404d-8b9a-7ace81d2a892",
    "033d3625-771c-430e-8a4e-820831588a11",
    # "95838953-c891-4332-90b7-4cb62c2ecdcb",
    # "41ab56e2-cc88-437f-85df-dac2a9377046",
    # "035b8844-d4e0-4a96-abdc-b476c517bd0f",
    # "682718bb-0c74-4e63-9b66-87aa4329ffb7",
    # "1231f15e-6cf0-40d6-ae21-bbce85037816",
    # "162ab410-0c1d-4f9a-acdc-14bee8f59e17",
    # "c6afe43d-ec51-47a2-955b-819e76e45804",
    # "55391c61-925d-4104-8000-548de4bfcbcc",
    # "abcd7ef5-ce9e-4c85-a4bf-688bbe0e8c8f",
    # "b0e926ab-fda6-40e3-9506-6b423e97b7c6",
    # "2d5d8d70-818c-4538-9bf5-a22d8e6d60d6",
    # "e143a82d-2885-45d0-b086-8e5769062cda",
    # "a9d2b5e8-aaca-4b4c-97dc-61a3f7133aef",
    # "4c9cdfb5-4518-47b0-b256-60bb45abd252",
    # "74c41cce-7348-4a12-b4f3-4b1dc23d21a3",
    # "b3d41e36-27e2-4357-a030-cb64a5031024",
    # "094c741e-5ed0-4bbf-b44b-cf7425ab6c01",
    # "c1690426-c01a-4ca9-972f-0a121be061f7",
    # "a1fe1823-d416-4e99-b367-e6d93afd2265",
    # "49d05b1d-e96f-4f4f-bca3-d3c5790199e1",
    # "b1ca3bde-c86f-4eb0-b619-42d98ba0b5db",
    # "06f15866-7ba5-4ae5-8d25-422ed3681d80",
    # "a953108c-e12a-4c8a-b01f-5ecfc1e2992d",
    # "b4b0f2e5-60a3-42e5-b7ef-a6d7721a44a7",
    # "875bc3b9-fd0a-45ec-9acc-152cdb860527",
    # "70821ec4-9b81-4207-b2f9-449bec7e0c22",
    # "4a593cf5-f341-4f7e-a337-fb019a6a9e57",
    # "6218b3ad-1492-4959-bfbb-7b8da7aea836",
    # "dcf3cca8-a49a-49d4-9df6-3197b1c590ec",
    # "1edc73d2-b173-4b3f-a63a-405cb8653b48",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    response_text = extract_text(response)
    if "[/AUGMENT]" in response_text:
        return False, "Response contains [/AUGMENT]"
    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"augment_closing_tag_{index}",
        eval_response_func=eval_func,
        category="augment_closing_tag",
        linear_url="https://linear.app/augmentcode/issue/AU-9038/[augment]-gets-inserted-into-a-response",
    )
    for index, sample in enumerate(samples)
]

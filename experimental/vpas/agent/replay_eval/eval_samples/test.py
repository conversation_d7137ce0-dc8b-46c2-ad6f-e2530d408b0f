# eval samples with small chat history for testing
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample

samples = [
    EvalSample(
        request_id="e673405a-16bb-4377-b0fb-32590f89c90d",
        name="test_sample_1",
        eval_response_func=lambda _: (True, ""),
    ),
    EvalSample(
        request_id="f97d20ad-13f5-4899-833d-f54e59013edb",
        name="test_sample_2",
        eval_response_func=lambda _: (True, ""),
    ),
]

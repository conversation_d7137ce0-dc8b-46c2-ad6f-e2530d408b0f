import inspect
from dataclasses import dataclass, field
from functools import cached_property
from typing import Any, Callable, Dict, <PERSON><PERSON>

from dataclasses_json import DataClassJsonMixin, config

from base.datasets.tenants import DatasetTenant, get_tenant
from base.third_party_clients.third_party_model_client import Tool<PERSON>ho<PERSON>, ToolDefinition
from experimental.vpas.agent.replay_eval.model_config import ModelConfig, ModelResponse
from experimental.vpas.utils.ri_utils import get_chat_host_response

EvalResponseFunc = (
    Callable[[ModelResponse], Tuple[bool, str]]
    | Callable[[ModelResponse, list[ToolDefinition]], Tuple[bool, str]]
    | Callable[[ModelResponse, list[ToolDefinition], str], Tuple[bool, str]]
)


@dataclass
class EvalSample(DataClassJsonMixin):
    request_id: str
    name: str
    eval_response_func: EvalResponseFunc | None = field(
        default=None, metadata=config(encoder=lambda x: None, exclude=lambda x: True)
    )
    assistant_message_prefill: str = ""
    assistant_message_prefill_from_response: bool = False
    linear_url: str | None = None
    tool_choice: ToolChoice | None = None
    category: str | None = None
    gen_stats_func: (
        Callable[[ModelResponse, list[ToolDefinition], str], Dict[str, int | float]]
        | None
    ) = field(
        default=None, metadata=config(encoder=lambda x: None, exclude=lambda x: True)
    )
    tenant_name: str = "dogfood-shard"

    def __post_init__(self):
        if self.assistant_message_prefill_from_response:
            response = get_chat_host_response(self.request_id, self.tenant_name)
            assert response, f"Could not find chat host response for {self.request_id}"
            # remove input too large message
            response_text = response.text.replace(
                "I'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?",
                "",
            )
            self.assistant_message_prefill = (
                response_text.strip() + "\n" + self.assistant_message_prefill
            )

    @cached_property
    def tenant(self) -> DatasetTenant:
        return get_tenant(self.tenant_name)

    def eval_response(
        self,
        response: ModelResponse | None,
        tool_definitions: list[ToolDefinition],
    ) -> Tuple[bool, str]:
        """Evaluate if the response is correct and provide an explanation if not.

        Args:
            response: The model response to evaluate

        Returns:
            A tuple containing:
                - is_correct: Whether the response is correct
                - message: An explanation message (empty string if correct)
        """
        if response is None:
            return False, "Response is None"

        if len(response) == 0:
            return False, "Response is empty"

        # Use inspect to determine the number of parameters in the callable
        sig = inspect.signature(self.eval_response_func)
        param_count = len(sig.parameters)

        # If the callable takes only one parameter, it's the first variant
        if param_count == 1:
            return self.eval_response_func(response)  # type: ignore
        # If it takes two parameters, it's the second variant
        elif param_count == 2:
            return self.eval_response_func(response, tool_definitions)  # type: ignore
        elif param_count == 3:
            return self.eval_response_func(
                response,
                tool_definitions,  # type: ignore
                self.request_id,  # type: ignore
            )
        else:
            raise ValueError(
                f"Unexpected number of parameters in eval_response_func: {param_count}"
            )

    def get_gen_stats(
        self, response: ModelResponse | None, tool_definitions: list[ToolDefinition]
    ) -> Dict[str, int | float]:
        if self.gen_stats_func is None or response is None:
            return {}
        return self.gen_stats_func(response, tool_definitions, self.request_id)

    def get_unique_name(self) -> str:
        return self.name

    def get_assistant_message_prefill(self) -> str:
        return self.assistant_message_prefill

    def get_linear_url(self) -> str | None:
        return self.linear_url

    def get_tool_choice(self) -> ToolChoice | None:
        return self.tool_choice

    # def __getstate__(self) -> Dict:
    #     """Get the serializable state of the sample, excluding the eval_response_func.

    #     This prevents the callable function from being pickled, which can cause issues
    #     with serialization.
    #     """
    #     state = self.__dict__.copy()
    #     # Remove the eval_response_func from the state
    #     state.pop("eval_response_func", None)
    #     return state

    # def __setstate__(self, state: Dict) -> None:
    #     """Restore the state of the sample.

    #     Note: This will not restore the eval_response_func, which must be set
    #     separately after unpickling if needed.
    #     """
    #     self.__dict__.update(state)
    #     # Set a placeholder function that will raise an error if called
    #     self.eval_response_func = lambda response: (
    #         False,
    #         "eval_response_func was not restored after unpickling",
    #     )

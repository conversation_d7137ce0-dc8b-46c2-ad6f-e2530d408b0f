from typing import <PERSON><PERSON>
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls


def str_replace_new_string_check(
    new_str_should_contain: str | None = None,
    new_str_should_not_contain: str | None = None,
):
    def _str_replace_new_string_check(response: ModelResponse) -> Tuple[bool, str]:
        tool_calls = extract_tool_calls(response)
        for tool_call in tool_calls:
            if tool_call.name != "str-replace-editor":
                continue
            tool_input = tool_call.input
            if tool_input.get("command") != "str_replace":
                continue
            str_replace_entries = tool_input.get("str_replace_entries")
            if str_replace_entries is None:
                return False, "str_replace_entries is None"
            for entry in str_replace_entries:
                new_str = entry.get("new_str")

                if new_str is None:
                    return False, "new_str is None"

                if (
                    new_str_should_contain is not None
                    and new_str_should_contain not in entry.get("new_str")
                ):
                    return False, f"new_str should contain: {new_str_should_contain}"
                if (
                    new_str_should_not_contain is not None
                    and new_str_should_not_contain in entry.get("new_str")
                ):
                    return (
                        False,
                        f"new_str should not contain: {new_str_should_not_contain}",
                    )

        return True, ""

    return _str_replace_new_string_check

from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "terminal_output_regex_filter_filter"
model_config.override_tool_map["launch-process"] = dict(
    description="""\
Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).

If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
`max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
while another is running, the tool will return an error.

If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

## Output filtering
Output filtering is strongly recommended to reduce the amount of tokens that need to be processed.
For example if you are running unit tests you only need the summary and the information about failing tests.
So you can use `show_last_n_lines` to only get the summary and `output_regex_filter` to filter out the failing tests.
This is very important to save on latency and const.
Use information from previous `launch-process` tool calls with similar commands to understand what information you need from the output.
Then use `output_filtering_reasoning` to explain your decision on how to filter the output.
If you decide to filter the output, use these fields:
- `output_regex_filter` to provide a regex pattern to filter the output.
- `output_regex_filter_context_lines` to provide the number of lines of context to include before and after each matching line.
- `show_last_n_lines` to provide the number of lines to show from the end of the output. This is useful to only get the summary since it is often printed last. This last n lines would be shown regardless of the output_regex_filter.
If you decide that you need the full output you MUST set `show_full_output` to true.

Notes:
- Use `wait=true` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use `wait=false` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is linux. The shell is 'bash'.
""",
    schema={
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "description": "The shell command to execute.",
            },
            "wait": {
                "type": "boolean",
                "description": "Whether to wait for the command to complete.",
            },
            "max_wait_seconds": {
                "type": "number",
                "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.",
            },
            "cwd": {
                "type": "string",
                "description": "Working directory for the command. If not supplied, uses the current working directory.",
            },
            "output_filtering_reasoning": {
                "type": "string",
                "description": "Reasoning for what information you need from the output. If you need full output or it can be filtered to reduce the token usage. Strongly consider filtering the output.",
            },
            "output_regex_filter": {
                "type": "string",
                "description": "Optional regex pattern to filter the output. Only lines matching the pattern will be returned. If not provided, all output will be returned.",
            },
            "output_regex_filter_context_lines": {
                "type": "number",
                "description": "Number of lines of context to include before and after each matching line when using output_regex_filter. Default is 0 (no context).",
            },
            "show_last_n_lines": {
                "type": "number",
                "description": "Number of lines to show from the end of the output. This is useful to only get the summary since it is often printed last. This last n lines would be shown regardless of the output_regex_filter.",
            },
            "show_full_output": {
                "type": "boolean",
                "description": "Whether to show the full output. This is mutually exclusive with output_regex_filter and show_last_n_lines. This is false by default.",
            },
        },
        "required": [
            "command",
            "wait",
            "max_wait_seconds",
            "output_filtering_reasoning",
        ],
    },
)

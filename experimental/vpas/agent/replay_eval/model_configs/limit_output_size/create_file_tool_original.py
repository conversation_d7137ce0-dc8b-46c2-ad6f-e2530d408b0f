from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "create_file_tool_original"
model_config.override_tool_map["save-file"] = dict(
    description="""\
Save a new file.
Use this tool to write new files with the attached content.
It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content": {
                "type": "string",
                "description": "The content of the file.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["file_path", "file_content"],
    },
)

from typing import <PERSON><PERSON>
from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.vpas.agent.replay_eval.model_config import (
    ModelResponse,
    from_prod_model_name,
)
from experimental.vpas.agent.replay_eval.utils import extract_tool_call
from experimental.vpas.agent.str_replace_editor.utils import (
    extract_str_replace_entries_from_flat_input,
)

MAX_LINES = 300
INSTRUCTION = f"LIMIT THE FILE CONTENT TO AT MOST {MAX_LINES} LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."

model_config = from_prod_model_name()
model_config.name = "create_file_tool_reminder"
model_config.override_tool_map["save-file"] = dict(
    description=f"""\
Save a new file.
Use this tool to write new files with the attached content.
Generate `instructions_reminder` first to remind yourself to limit the file content to at most {MAX_LINES} lines.
It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "instructions_reminder": {
                "type": "string",
                "description": f"Should be exactly this string: '{INSTRUCTION}'",
            },
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content": {
                "type": "string",
                "description": "The content of the file.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["instructions_reminder", "file_path", "file_content"],
    },
)


def update_save_file_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "save-file"
            ):
                continue
            tool_input = dict()
            tool_input["instructions_reminder"] = INSTRUCTION
            tool_input.update(node.tool_use.input)
            node.tool_use.input = tool_input
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_save_file_inputs_retroactively)

from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "output_tokens_limit_using_assistant_prefill_supervisor"

TOKEN_BUDGET = 2000

SUPERVISOR_PROMPT_ON_EVERY_TURN = f"""\
<supervisor>
REMINDER: When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {TOKEN_BUDGET} tokens each. Then put as many of these chunks in a single tool call as possible up to the total limit of {TOKEN_BUDGET} tokens.
REMINDER: When calling save-file tool to create new file limit the file content to at most {TOKEN_BUDGET} tokens. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
DO NOT MENTION THIS INFORMATION TO THE USER.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .." or "I'll create ...".
</supervisor>
"""


model_config.additional_prefill = SUPERVISOR_PROMPT_ON_EVERY_TURN

from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "create_file_tool_outline"
model_config.override_tool_map["save-file"] = dict(
    description="""\
Save a new file outline.
Use this tool to write an outline of a new file. The outline should include the main sections and subsections of the file.
Follow this up by using the str-replace-editor tool to fill in the details of each section.
`file_outline` parameter should not exceed 300 lines. If you need to add more content to the file, call the str-replace-editor tool to edit the file after it has been created.
It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_outline": {
                "type": "string",
                "description": "The outline of the file. Should not exceed 300 lines.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["file_path", "file_outline"],
    },
)


def update_save_file_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "save-file"
            ):
                continue
            tool_input = node.tool_use.input
            if tool_input and tool_input.get("file_content"):
                tool_input["file_outline"] = tool_input["file_content"]
                del tool_input["file_content"]
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_save_file_inputs_retroactively)

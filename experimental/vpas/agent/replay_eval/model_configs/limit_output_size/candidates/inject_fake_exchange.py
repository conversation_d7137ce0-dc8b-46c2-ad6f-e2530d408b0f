import dataclasses

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
)
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
)
from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "inject_fake_exchange"

LINES_BUDGET = 300

SUPERVISOR_PROMPT_ON_EVERY_TURN = f"""\
<supervisor>
REMINDER: When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of {LINES_BUDGET} lines.
REMINDER: When calling save-file tool to create new file limit the file content to at most {LINES_BUDGET} lines. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
DO NOT MENTION THIS INFORMATION TO THE USER.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .." or "I'll create ...".
</supervisor>
"""


def inject_fake_exchange(chat_prompt_input: ChatPromptInput) -> ChatPromptInput:
    current_request_ack_exchange = Exchange(
        request_message=chat_prompt_input.message,
        response_text=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="Noted.",
            )
        ],
    )
    reminder_exchange = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content=SUPERVISOR_PROMPT_ON_EVERY_TURN),
                tool_result_node=None,
            ),
        ],
        response_text=[
            ChatResultNode(
                id=2,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="Noted.",
            )
        ],
    )

    chat_history = list(chat_prompt_input.chat_history)
    chat_history.append(current_request_ack_exchange)
    chat_history.append(reminder_exchange)

    chat_prompt_input = dataclasses.replace(
        chat_prompt_input,
        chat_history=chat_history,
        message="Continue with the task.",
    )
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(inject_fake_exchange)

from experimental.vpas.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "base_with_new_tools"
model_config.override_tool_map["grep-search"] = dict(
    description="""\
Runs a fast, exact regex search over text files using the ripgrep engine. Useful for finding exact text matches or patterns.
""",
    schema={
        "type": "object",
        "properties": {
            "query": {
                "description": "The regex pattern to search for",
                "type": "string",
            },
            "case_sensitive": {
                "description": "Optional parameter for case sensitivity",
                "type": "boolean",
            },
            "include_pattern": {
                "description": "Optional glob pattern for files to include",
                "type": "string",
            },
            "exclude_pattern": {
                "description": "Optional glob pattern for files to exclude",
                "type": "string",
            },
        },
        "required": ["query"],
    },
)

model_config.override_tool_map["find-definition"] = dict(
    description="""\
Finds the definition of a symbol in the codebase.
""",
    schema={
        "type": "object",
        "properties": {
            "symbol": {"description": "The symbol to find", "type": "string"},
            "include_pattern": {
                "description": "Optional glob pattern for files to include",
                "type": "string",
            },
            "exclude_pattern": {
                "description": "Optional glob pattern for files to exclude",
                "type": "string",
            },
        },
        "required": ["symbol"],
    },
)

"""React report generator for HTML reports.

This module provides functions for generating JSON data for the React app,
building the React app, and copying it to the output directory.
"""

from dataclasses import dataclass
import os
import shutil
import subprocess
from pathlib import Path
from typing import List, Optional

from dataclasses_json import DataClassJsonMixin


from base.prompt_format.common import ChatResultNodeType
from experimental.vpas.agent.replay_eval.eval_output import (
    ComparisonSummary,
    EvalSummary,
)

# Constants
CURRENT_DIR = Path(__file__).parent
REACT_DIR = CURRENT_DIR / "react"


@dataclass
class ReportData(DataClassJsonMixin):
    comparison_summary: ComparisonSummary
    ref_model_name: str
    model_names: List[str]
    dataset_path: str


def generate_comparison_json_str(
    comparison_summary: ComparisonSummary,
) -> str:
    # Add a few additional fields that might be useful for the React app
    ref_model_name = comparison_summary.ref_summary.model_config.name
    model_names = [ref_model_name] + list(comparison_summary.new_model_summaries.keys())
    dataset_path = comparison_summary.ref_summary.dataset_path

    # Hack to make Anthropic_NOT_GIVEN JSON serializable
    def prepare_summary(summary: EvalSummary):
        for result in summary.eval_results.values():
            for attempt in result.attempts:
                if attempt.response is not None:
                    for node in attempt.response:
                        if node.type != ChatResultNodeType.FINAL_PARAMETERS:
                            continue
                        assert node.final_parameters is not None
                        for name, value in node.final_parameters.items():
                            if value.__class__.__name__ == "NotGiven":
                                node.final_parameters[name] = None

    prepare_summary(comparison_summary.ref_summary)
    for summary in comparison_summary.new_model_summaries.values():
        prepare_summary(summary)

    data = ReportData(
        comparison_summary=comparison_summary,
        # comparison_summary=None,
        ref_model_name=ref_model_name,
        model_names=model_names,
        dataset_path=dataset_path,
    )
    return data.to_json()


def build_react_app(data_path: Path) -> bool:
    """Build the React app with the provided data.

    Args:
        data_path: Path to the JSON data file

    Returns:
        True if the build was successful, False otherwise
    """
    try:
        print(f"Building React app with data from {data_path}")
        # Change to the React directory
        os.chdir(REACT_DIR)

        # Install dependencies
        subprocess.run(["npm", "install", "--legacy-peer-deps"], check=True)

        # Build the React app
        subprocess.run(["npm", "run", "build"], check=True)

        # Copy the data file to the dist directory
        shutil.copy(data_path, REACT_DIR / "dist" / "data.json")

        return True
    except subprocess.CalledProcessError as e:
        print(f"Error building React app: {e}")
        return False
    finally:
        # Change back to the original directory
        os.chdir(CURRENT_DIR)


def save_react_comparison_html_report(
    comparison_summary: ComparisonSummary, output_dir: Path
) -> Optional[str]:
    """Generate a React comparison HTML report and save it to the output directory.

    Args:
        comparison_summary: The comparison summary to generate a report for
        output_dir: The directory where the report should be saved

    Returns:
        The path to the HTML report file, or None if the build failed
    """
    print(f"Generating React report in {output_dir}")
    # Create the output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate the JSON data
    json_str = generate_comparison_json_str(comparison_summary)

    # Save the JSON data to a temporary file
    temp_data_path = CURRENT_DIR / "temp_data.json"
    with open(temp_data_path, "w") as f:
        f.write(json_str)

    # Build the React app
    build_success = build_react_app(temp_data_path)

    # Clean up the temporary file
    if temp_data_path.exists():
        temp_data_path.unlink()

    if not build_success:
        print("Failed to build React app")
        return None

    # Copy the built React app to the output directory
    react_build_dir = REACT_DIR / "dist"

    # Copy all files from the build directory to the output directory
    for item in react_build_dir.glob("*"):
        if item.is_file():
            shutil.copy(item, output_dir / item.name)
        elif item.is_dir():
            shutil.copytree(item, output_dir / item.name, dirs_exist_ok=True)

    # Return the path to the index.html file
    index_path = output_dir / "index.html"
    if index_path.exists():
        return str(index_path)
    else:
        print("index.html not found in the build output")
        return None

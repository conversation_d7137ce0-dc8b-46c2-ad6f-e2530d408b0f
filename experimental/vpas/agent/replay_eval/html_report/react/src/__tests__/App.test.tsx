import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import App from '../App'

// Mock the fetch function
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock the components used in App
vi.mock('../components/PassLogic/PassLogicSelector', () => ({
  default: () => <div data-testid="pass-logic-selector">PassLogicSelector</div>
}))

vi.mock('../components/Statistics/ComparisonSummary', () => ({
  default: ({ refModelName, modelNames, datasetPath }: any) => (
    <div data-testid="comparison-summary">
      ComparisonSummary: {refModelName}, {modelNames.join(', ')}, {datasetPath}
    </div>
  )
}))

vi.mock('../components/SystemPrompt/SystemPromptDiff', () => ({
  default: () => <div data-testid="system-prompt-diff">SystemPromptDiff</div>
}))

vi.mock('../components/SupervisorMessage/SupervisorMessageDiff', () => ({
  default: () => <div data-testid="supervisor-message-diff">SupervisorMessageDiff</div>
}))

vi.mock('../components/EvalResults/EvalResultsList', () => ({
  default: () => <div data-testid="eval-results-list">EvalResultsList</div>
}))

describe('App', () => {
  beforeEach(() => {
    mockFetch.mockReset()
  })

  it('shows loading state initially', () => {
    // Mock fetch to never resolve
    mockFetch.mockImplementation(() => new Promise(() => {}))

    render(<App />)

    expect(screen.getByText('Loading report data...')).toBeInTheDocument()
  })

  it('shows error state when fetch fails', async () => {
    // Mock fetch to reject
    mockFetch.mockRejectedValue(new Error('Failed to fetch'))

    render(<App />)

    await waitFor(() => {
      expect(screen.getByText('Error: Failed to load report data')).toBeInTheDocument()
    })
  })

  it('renders the app with data when fetch succeeds', async () => {
    // Mock successful fetch with sample data
    mockFetch.mockResolvedValue({
      json: () => Promise.resolve({
        title: 'Test Report',
        ref_model_name: 'Reference Model',
        model_names: ['Reference Model', 'Model A'],
        dataset_path: '/path/to/dataset',
        comparison_summary: {
          ref_summary: {
            model_config: {
              name: 'Reference Model',
              anthropic_model: 'sonnet3.7',
              system_prompt: 'System prompt',
              supervisor_message: 'Supervisor message'
            },
            dataset_path: '/path/to/dataset',
            eval_results: {},
            eval_stats: {}
          },
          new_model_summaries: {
            'Model A': {
              model_config: {
                name: 'Model A',
                anthropic_model: 'sonnet3.7',
                system_prompt: 'System prompt A',
                supervisor_message: 'Supervisor message A'
              },
              dataset_path: '/path/to/dataset',
              eval_results: {},
              eval_stats: {}
            }
          },
          ref_system_prompt: 'System prompt',
          model_system_prompts: { 'Model A': 'System prompt A' },
          ref_supervisor_message: 'Supervisor message',
          model_supervisor_messages: { 'Model A': 'Supervisor message A' }
        }
      })
    })

    render(<App />)

    await waitFor(() => {
      expect(screen.getByText('Evaluation Report')).toBeInTheDocument()
    })

    expect(screen.getByTestId('pass-logic-selector')).toBeInTheDocument()
    expect(screen.getByTestId('comparison-summary')).toBeInTheDocument()
    expect(screen.getByTestId('system-prompt-diff')).toBeInTheDocument()
    expect(screen.getByTestId('supervisor-message-diff')).toBeInTheDocument()
    expect(screen.getByTestId('eval-results-list')).toBeInTheDocument()
  })

  it('shows no data available when fetch returns null', async () => {
    // Mock fetch to return null
    mockFetch.mockResolvedValue({
      json: () => Promise.resolve(null)
    })

    render(<App />)

    await waitFor(() => {
      expect(screen.getByText('No report data available')).toBeInTheDocument()
    })
  })
})

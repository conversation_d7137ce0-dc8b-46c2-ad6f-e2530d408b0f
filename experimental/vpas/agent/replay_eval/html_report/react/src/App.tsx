import { useState, useEffect } from 'react'
import { PassLogicProvider } from './contexts/PassLogicContext'
import PassLogicSelector from './components/PassLogic/PassLogicSelector'
import ComparisonSummary from './components/Statistics/ComparisonSummary'
import EvalResultsList from './components/EvalResults/EvalResultsList'
import { ReportData } from './types'
import ReactJson from 'react-json-view';
import MessageDiff from './components/common/MessageDiff'

function App() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Load data from the JSON file
    const loadData = async () => {
      try {
        const response = await fetch('./data.json');
        const data = await response.json();
        setReportData(data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load report data');
        setLoading(false);
        console.error(err);
      }
    };

    loadData();
  }, [])

  if (loading) {
    return <div>Loading report data...</div>
  }

  if (error) {
    return <div>Error: {error}</div>
  }

  if (!reportData) {
    return <div>No report data available</div>
  }

  // Extract system prompts from the data structure
  const systemPrompts: Record<string, { content: string }> = {};

  // Add reference model system prompt
  systemPrompts[reportData.ref_model_name] = {
    content: reportData.comparison_summary.ref_system_prompt || ''
  };

  // Add comparison models system prompts
  Object.entries(reportData.comparison_summary.model_system_prompts).forEach(([modelName, prompt]) => {
    systemPrompts[modelName] = {
      content: prompt || ''
    };
  });

  // Extract supervisor messages from the data structure
  const supervisorMessages: Record<string, { content: string }> = {};

  // Add reference model supervisor message
  supervisorMessages[reportData.ref_model_name] = {
    content: reportData.comparison_summary.ref_supervisor_message || ''
  };

  // Add comparison models supervisor messages
  Object.entries(reportData.comparison_summary.model_supervisor_messages).forEach(([modelName, message]) => {
    supervisorMessages[modelName] = {
      content: message || ''
    };
  });

  return (
    <PassLogicProvider>
      <div className="report-container">
        <h1>Evaluation Report</h1>

        <PassLogicSelector />

        <ComparisonSummary
          comparisonSummary={reportData.comparison_summary}
          refModelName={reportData.ref_model_name}
          modelNames={reportData.model_names}
          datasetPath={reportData.dataset_path}
        />

        <div data-testid="system-prompt-diff">
          <MessageDiff
            messages={systemPrompts}
            title="System Prompts"
            className="system-prompt"
            refModelName={reportData.ref_model_name}
          />
        </div>

        <div data-testid="supervisor-message-diff">
          <MessageDiff
            messages={supervisorMessages}
            title="Supervisor Messages"
            className="supervisor-message"
            refModelName={reportData.ref_model_name}
          />
        </div>

        <EvalResultsList
          comparisonSummary={reportData.comparison_summary}
          refModelName={reportData.ref_model_name}
        />

        <ReactJson src={reportData} theme="monokai" collapsed={true} />
      </div>
    </PassLogicProvider>
  )
}

export default App

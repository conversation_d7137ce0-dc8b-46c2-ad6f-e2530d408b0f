import { useState, useMemo } from 'react';
import { EvalResult, PassLogicType } from '../../types/index';
import { doesSamplePass } from '../../utils/passLogic';
import AttemptView from './AttemptView';
import TabNav, { Tab } from './TabNav';
import './TabbedEvalResult.css';

interface TabbedEvalResultProps {
  refEvalResult: EvalResult;
  modelEvalResults: Record<string, EvalResult>;
  passLogic: PassLogicType;
  refModelName: string;
}

function TabbedEvalResult({ refEvalResult, modelEvalResults, passLogic, refModelName }: TabbedEvalResultProps) {
  // Get all model names including reference
  const modelNames = useMemo(() => {
    return [refModelName, ...Object.keys(modelEvalResults)];
  }, [refModelName, modelEvalResults]);

  // Set the first model as active by default
  const [activeModelName, setActiveModelName] = useState(modelNames[0]);

  // Get the active eval result
  const activeEvalResult = useMemo(() => {
    return activeModelName === refModelName
      ? refEvalResult
      : modelEvalResults[activeModelName];
  }, [activeModelName, refEvalResult, modelEvalResults, refModelName]);

  // Calculate stats for all models
  const modelStats = useMemo(() => {
    const stats: Record<string, { correctAttempts: number, totalAttempts: number, passes: boolean }> = {};

    // Calculate reference model stats
    let refCorrectAttempts = 0;
    const refAttempts = refEvalResult.attempts || [];
    const refTotalAttempts = refAttempts.length;

    refAttempts.forEach(attempt => {
      if (attempt && attempt.is_correct) refCorrectAttempts++;
    });

    const refPasses = doesSamplePass(refCorrectAttempts, refTotalAttempts, passLogic);

    stats[refModelName] = {
      correctAttempts: refCorrectAttempts,
      totalAttempts: refTotalAttempts,
      passes: refPasses
    };

    // Calculate stats for each comparison model
    Object.entries(modelEvalResults).forEach(([modelName, evalResult]) => {
      if (!evalResult) return;

      let correctAttempts = 0;
      const attempts = evalResult.attempts || [];
      const totalAttempts = attempts.length;

      attempts.forEach(attempt => {
        if (attempt && attempt.is_correct) correctAttempts++;
      });

      const passes = doesSamplePass(correctAttempts, totalAttempts, passLogic);

      stats[modelName] = {
        correctAttempts,
        totalAttempts,
        passes
      };
    });

    return stats;
  }, [refEvalResult, modelEvalResults, passLogic, refModelName]);

  // Create tabs with status indicators
  const tabs = useMemo(() => {
    return modelNames.map(modelName => {
      const isReference = modelName === refModelName;
      let status: Tab['status'] = 'neutral';

      if (isReference) {
        status = 'reference';
      } else {
        // Compare with reference model
        const modelPasses = modelStats[modelName].passes;
        const refPasses = modelStats[refModelName].passes;

        if (modelPasses && !refPasses) {
          status = 'improvement';
        } else if (!modelPasses && refPasses) {
          status = 'regression';
        } else {
          status = 'neutral';
        }
      }

      return {
        id: modelName,
        label: isReference ? `Reference: ${modelName}` : modelName,
        status
      };
    });
  }, [modelNames, modelStats, refModelName]);

  // Calculate if the active eval result passes
  const resultStats = useMemo(() => {
    return modelStats[activeModelName];
  }, [modelStats, activeModelName]);

  if (!activeEvalResult) {
    return <div>No evaluation result available</div>;
  }

  const ri_url = `https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/${activeEvalResult.sample.request_id}`;

  return (
    <div className="tabbed-eval-result" id={refEvalResult.sample.name} data-category={refEvalResult.sample.category}>
      <div className="eval-result-header">
        <h4>{refEvalResult.sample.name}</h4>
        <span className={resultStats.passes ? 'correct' : 'incorrect'}>
          {resultStats.passes ? 'PASS' : 'FAIL'}
        </span>
      </div>

      <TabNav
        tabs={tabs}
        activeTabId={activeModelName}
        onTabChange={setActiveModelName}
      />

      <div className="model-details">
        <div className="model-info">
          <div><strong>Model:</strong> {activeModelName}</div>
          <div><strong>Request ID:</strong> <a href={ri_url}>{activeEvalResult.sample.request_id}</a></div>
          <div><strong>Correct Attempts:</strong> {resultStats.correctAttempts}/{resultStats.totalAttempts}</div>
        </div>

        <details className="sample-details">
          <summary>Sample Details</summary>
          <div className="sample-details-content">
            <p><strong>Category:</strong> {activeEvalResult.sample.category || 'Uncategorized'}</p>

            {activeEvalResult.sample.assistant_message_prefill && (
              <div>
                <p><strong>Assistant Message Prefill:</strong></p>
                <pre>{activeEvalResult.sample.assistant_message_prefill}</pre>
              </div>
            )}

            {activeEvalResult.sample.assistant_message_prefill_from_response !== undefined && (
              <p><strong>Prefill From Response:</strong> {activeEvalResult.sample.assistant_message_prefill_from_response ? 'Yes' : 'No'}</p>
            )}

            {activeEvalResult.sample.linear_url && (
              <p><strong>Linear URL:</strong> <a href={activeEvalResult.sample.linear_url} target="_blank" rel="noopener noreferrer">{activeEvalResult.sample.linear_url}</a></p>
            )}

            {activeEvalResult.sample.tool_choice && (
              <div>
                <p><strong>Tool Choice:</strong></p>
                {typeof activeEvalResult.sample.tool_choice === 'string' ? (
                  <p>{activeEvalResult.sample.tool_choice}</p>
                ) : (
                  <pre>{JSON.stringify(activeEvalResult.sample.tool_choice, null, 2)}</pre>
                )}
              </div>
            )}
          </div>
        </details>
      </div>

      <div className="eval-result-attempts">
        <h5>Attempts:</h5>
        {activeEvalResult && activeEvalResult.attempts && Array.isArray(activeEvalResult.attempts) && activeEvalResult.attempts.length > 0 ? (
          activeEvalResult.attempts.map((attempt, index) => {
            // Attempt data is available
            if (!attempt) {
              return <div key={index}>Attempt {index + 1} data is missing</div>;
            }
            return (
              <AttemptView
                key={index}
                attempt={attempt}
                index={index}
              />
            );
          })
        ) : (
          <div>No attempts available</div>
        )}
      </div>
    </div>
  );
}

export default TabbedEvalResult;

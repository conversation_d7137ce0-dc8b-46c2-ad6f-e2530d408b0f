.button {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  cursor: pointer;
}

.button:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.button:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Variants */
.button-primary {
  color: #fff;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.button-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.button-secondary {
  color: #fff;
  background-color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.button-success {
  color: #fff;
  background-color: var(--color-success);
  border-color: var(--color-success);
}

.button-danger {
  color: #fff;
  background-color: var(--color-danger);
  border-color: var(--color-danger);
}

.button-warning {
  color: #212529;
  background-color: var(--color-warning);
  border-color: var(--color-warning);
}

.button-info {
  color: #fff;
  background-color: var(--color-info);
  border-color: var(--color-info);
}

/* Sizes */
.button-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.button-md {
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
}

.button-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

/* Active state */
.button.active {
  background-color: var(--color-success);
  color: white;
  border-color: var(--color-success);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

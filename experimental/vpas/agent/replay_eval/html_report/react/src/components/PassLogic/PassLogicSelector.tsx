import { usePassLogic } from '../../contexts/PassLogicContext';
import Button from '../common/Button';
import './PassLogicSelector.css';

function PassLogicSelector() {
  const { passLogic, setPassLogic, passLogicDescription } = usePassLogic();

  const handlePassLogicChange = (newPassLogic: 'at_least_one' | 'majority' | 'all' | 'aggregate') => {
    setPassLogic(newPassLogic);
  };

  return (
    <div className="pass-logic-selector">
      <h3>Pass Logic:</h3>
      <div className="pass-logic-btn-group">
        <Button
          id="pass-logic-at_least_one"
          className="pass-logic-btn"
          isActive={passLogic === 'at_least_one'}
          onClick={() => handlePassLogicChange('at_least_one')}
        >
          At Least One Attempt
        </Button>
        <Button
          id="pass-logic-majority"
          className="pass-logic-btn"
          isActive={passLogic === 'majority'}
          onClick={() => handlePassLogicChange('majority')}
        >
          Majority of Attempts
        </Button>
        <Button
          id="pass-logic-all"
          className="pass-logic-btn"
          isActive={passLogic === 'all'}
          onClick={() => handlePassLogicChange('all')}
        >
          All Attempts
        </Button>
        <Button
          id="pass-logic-aggregate"
          className="pass-logic-btn"
          isActive={passLogic === 'aggregate'}
          onClick={() => handlePassLogicChange('aggregate')}
        >
          Aggregate All Attempts
        </Button>
      </div>
      <div className="pass-logic-description" dangerouslySetInnerHTML={{ __html: passLogicDescription }} />
    </div>
  );
}

export default PassLogicSelector;

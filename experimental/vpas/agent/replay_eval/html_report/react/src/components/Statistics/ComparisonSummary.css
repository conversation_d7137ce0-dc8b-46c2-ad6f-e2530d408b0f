.comparison-summary {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.summary-content {
  margin-top: 10px;
}

.overall-stats, .category-stats {
  margin-top: 20px;
}

.overall-stats h3, .category-stats h3 {
  margin-bottom: 20px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
}

.category-stats .category-table {
  margin-bottom: 30px;
}

.category-stats .category-table h4 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.comparison-summary table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.comparison-summary th, .comparison-summary td {
  padding: 8px;
  text-align: left;
  border: 1px solid #ddd;
}

.comparison-summary th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.average-stats-section {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.average-stats-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1rem;
  color: #555;
}

.average-stats-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.average-stat-item {
  background-color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #eee;
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.average-stat-name {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 5px;
}

.average-stat-value {
  font-size: 1.1rem;
  font-weight: bold;
}

.model-average-stats {
  margin-bottom: 15px;
}

.model-average-stats h6 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #444;
}

.comparison-summary tr.reference {
  background-color: #f0f0f0;
}

.comparison-summary tr.improvement {
  background-color: var(--color-success-light);
}

.comparison-summary tr.regression {
  background-color: var(--color-danger-light);
}

.comparison-summary tr.neutral {
  background-color: var(--color-gray-light);
}

/* Comparison indicators */
.comparison-summary td.better {
  color: green;
  font-weight: bold;
}

.comparison-summary td.worse {
  color: red;
  font-weight: bold;
}

.comparison-summary td.same {
  color: gray;
}

/* Improvements and regressions */
.comparison-summary tr:not(.reference) td:nth-child(5) {
  color: green;
  font-weight: bold;
}

.comparison-summary tr:not(.reference) td:nth-child(6) {
  color: red;
  font-weight: bold;
}

import React from 'react';

interface ModelStatsRowProps {
  modelName: string;
  successRate: string;
  correct: number;
  total: number;
  improvements?: number | null;
  regressions?: number | null;
  isReference?: boolean;
}

const ModelStatsRow: React.FC<ModelStatsRowProps> = ({
  modelName,
  successRate,
  correct,
  total,
  improvements = null,
  regressions = null,
  isReference = false
}) => {
  const modelId = modelName.replace(/ /g, '_').toLowerCase();

  return (
    <tr className={isReference ? 'reference' : ''} data-model={modelName}>
      <td>{modelName}</td>
      <td id={isReference ? 'overall-success-rate' : `overall-success-rate-${modelId}`}>{successRate}</td>
      <td>{correct}</td>
      <td>{total}</td>
      <td className="better">{isReference ? '-' : (improvements && improvements > 0 ? improvements : '-')}</td>
      <td className="worse">{isReference ? '-' : (regressions && regressions > 0 ? regressions : '-')}</td>
    </tr>
  );
};

export default ModelStatsRow;

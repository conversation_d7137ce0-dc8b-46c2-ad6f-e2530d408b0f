import React from 'react';
import <PERSON>act<PERSON><PERSON> from 'react-json-view';
import { ChatResultNode } from '../../types';

interface ToolUseNodeProps {
  node: ChatResultNode;
}

const ToolUseNode: React.FC<ToolUseNodeProps> = ({ node }) => {
  if (!node.tool_use) return null;

  return (
    <div className="node tool-use">
      <div className="tool-use-header">
        <strong>Tool Use:</strong> {node.tool_use.name}
      </div>
      <div className="tool-use-input">
        <ReactJson
          src={node.tool_use.input}
          theme="monokai"
          name={false}
          displayDataTypes={false}
          collapsed={1}
          enableClipboard={false}
        />
      </div>
    </div>
  );
};

export default ToolUseNode;

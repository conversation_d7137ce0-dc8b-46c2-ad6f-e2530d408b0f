import { useState, useCallback } from 'react';
import Tabs from './Tabs';
import CodeBlock from './CodeBlock';
import Details from './Details';
// @ts-ignore
import * as diffLib from 'diff';
import './MessageDiff.css';

// Generic interface for messages with content
export interface Message {
  content: string;
}

interface MessageDiffProps<T extends Message> {
  messages: Record<string, T>;
  title: string;
  className?: string;
  refModelName?: string;
}

function MessageDiff<T extends Message>({
  messages,
  title,
  className = 'message',
  refModelName
}: MessageDiffProps<T>) {
  // State to track which model's full content is being shown
  const [showingFullContent, setShowingFullContent] = useState<string | null>(null);

  // Function to toggle showing full content for a model
  const toggleFullContent = useCallback((modelName: string) => {
    setShowingFullContent(current => current === modelName ? null : modelName);
  }, []);
  const [isOpen] = useState(false);

  // If there are no messages, don't render anything
  if (!messages || Object.keys(messages).length === 0) {
    return null;
  }

  const modelNames = Object.keys(messages);

  // If refModelName is provided, we'll show diffs for other models
  const refMessage = refModelName ? messages[refModelName]?.content : null;

  const tabs = modelNames.map(modelName => {
    const modelId = modelName.replace(/ /g, '_').toLowerCase();
    const isReference = modelName === refModelName;
    const currentMessage = messages[modelName].content;

    // If this is the reference model or no reference is provided, show the full content
    // Otherwise, show the diff between this model and the reference
    let displayContent = currentMessage;

    if (refMessage && !isReference) {
      // Generate a diff between the reference and current message
      const diffResult = diffLib.diffLines(refMessage, currentMessage);

      // Format the diff for display
      displayContent = diffResult.map((part: any) => {
        // Only show added or removed parts
        if (!part.added && !part.removed) {
          return ''; // Skip unchanged parts
        }

        const prefix = part.added ? '+ ' : part.removed ? '- ' : '';
        return prefix + part.value;
      }).join('');

      // If there are no differences, show a message
      if (!displayContent.trim()) {
        displayContent = 'No differences from reference';
      }
    }

    return {
      id: modelId,
      label: modelName + (isReference ? ' (Reference)' : ''),
      content: (
        <div className={`${className}-content ${isReference ? 'reference' : ''}`}>
          {isReference ? (
            <CodeBlock
              code={currentMessage}
              language="markdown"
              showLineNumbers={true}
            />
          ) : (
            <div className="diff-container">
              {refMessage && (
                <div className="diff-header">
                  <span>Showing only differences from reference model</span>
                  <button
                    className="show-full-button"
                    onClick={() => toggleFullContent(modelName)}
                  >
                    {showingFullContent === modelName ? 'Show Only Differences' : 'Show Full Content'}
                  </button>
                </div>
              )}
              {showingFullContent === modelName ? (
                <CodeBlock
                  code={currentMessage}
                  language="markdown"
                  showLineNumbers={true}
                />
              ) : (
                <CodeBlock
                  code={displayContent}
                  language="diff"
                  showLineNumbers={false}
                />
              )}
            </div>
          )}
        </div>
      )
    };
  });

  return (
    <div className={`${className}-section`}>
      <Details
        summary={<h2 className="collapsible-header">{title}</h2>}
        defaultOpen={isOpen}
        className={`${className}-details`}
      >
        <Tabs tabs={tabs} defaultActiveTab={tabs[0]?.id} />
      </Details>
    </div>
  );
}

export default MessageDiff;

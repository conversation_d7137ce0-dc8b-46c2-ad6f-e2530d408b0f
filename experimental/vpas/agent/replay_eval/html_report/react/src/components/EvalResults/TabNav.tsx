import React from 'react';
import './TabNav.css';

export interface Tab {
  id: string;
  label: string;
  status?: 'improvement' | 'regression' | 'neutral' | 'reference';
}

interface TabNavProps {
  tabs: Tab[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
}

const TabNav: React.FC<TabNavProps> = ({ tabs, activeTabId, onTabChange }) => {
  return (
    <div className="tab-nav">
      {tabs.map(tab => (
        <button
          key={tab.id}
          className={`tab-button ${tab.status || ''} ${activeTabId === tab.id ? 'active' : ''}`}
          onClick={() => onTabChange(tab.id)}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export default TabNav;

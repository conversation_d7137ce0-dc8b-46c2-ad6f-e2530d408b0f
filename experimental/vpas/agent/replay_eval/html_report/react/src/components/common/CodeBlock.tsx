import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import './CodeBlock.css';

interface CodeBlockProps {
  code: string;
  language?: string;
  showLineNumbers?: boolean;
  className?: string;
}

function CodeBlock({
  code,
  language = 'javascript',
  showLineNumbers = true,
  className = '',
}: CodeBlockProps) {
  return (
    <div className={`code-block ${className}`}>
      <SyntaxHighlighter
        language={language}
        style={vscDarkPlus}
        showLineNumbers={showLineNumbers}
        wrapLines={true}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
}

export default CodeBlock;

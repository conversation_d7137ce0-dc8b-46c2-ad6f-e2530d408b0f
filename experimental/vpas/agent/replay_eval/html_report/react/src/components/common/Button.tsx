import { ReactNode, ButtonHTMLAttributes } from 'react';
import './Button.css';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  isActive?: boolean;
}

function Button({
  children,
  variant = 'primary',
  size = 'md',
  isActive = false,
  className = '',
  ...props
}: ButtonProps) {
  const buttonClasses = [
    'button',
    `button-${variant}`,
    `button-${size}`,
    isActive ? 'active' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button className={buttonClasses} {...props}>
      {children}
    </button>
  );
}

export default Button;

.tab-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.tab-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  background-color: var(--color-gray-light);
  color: var(--color-text);
}

.tab-button:hover {
  background-color: var(--color-gray);
}

.tab-button.active {
  background-color: var(--color-primary);
  color: white;
}

/* Status-based styling */
.tab-button.improvement {
  background-color: rgba(0, 128, 0, 0.1);
  border-left: 3px solid green;
}

.tab-button.improvement.active {
  background-color: rgba(0, 128, 0, 0.7);
  color: white;
}

.tab-button.regression {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 3px solid red;
}

.tab-button.regression.active {
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
}

.tab-button.reference {
  background-color: rgba(0, 0, 0, 0.1);
  border-left: 3px solid #333;
}

.tab-button.reference.active {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
}

.tab-button.neutral {
  background-color: rgba(128, 128, 128, 0.1);
  border-left: 3px solid gray;
}

.tab-button.neutral.active {
  background-color: rgba(128, 128, 128, 0.7);
  color: white;
}

import React from 'react';
import { formatNumber } from './statsUtils';

interface AllStatsProps {
  stats: Record<string, number>;
}

/**
 * Component to display all statistics
 */
const AllStats: React.FC<AllStatsProps> = ({ stats }) => {
  if (!stats || Object.keys(stats).length === 0) {
    return <p>No statistics available.</p>;
  }

  return (
    <>
      {Object.entries(stats).map(([key, value]) => {
        // Format the key for display (convert to title case)
        const displayKey = key
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

        return (
          <div key={key} className="stat-item">
            <div className="stat-name">{displayKey}</div>
            <div className="stat-value">{formatNumber(value)}</div>
          </div>
        );
      })}
    </>
  );
};

export default AllStats;

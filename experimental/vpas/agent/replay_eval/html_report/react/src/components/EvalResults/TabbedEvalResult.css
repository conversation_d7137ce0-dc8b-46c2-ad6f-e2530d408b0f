.tabbed-eval-result {
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.eval-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.eval-result-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--color-text);
}

.eval-result-header .correct {
  color: green;
  font-weight: bold;
}

.eval-result-header .incorrect {
  color: red;
  font-weight: bold;
}

.model-details {
  margin: 12px 0;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.model-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.sample-details {
  margin-top: 8px;
}

.sample-details summary {
  cursor: pointer;
  font-weight: 500;
  color: var(--color-primary);
}

.sample-details-content {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.sample-details-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
  font-size: 0.9rem;
  margin: 8px 0;
}

.sample-details-content div {
  margin-bottom: 12px;
}

.eval-result-attempts {
  margin-top: 16px;
}

.eval-result-attempts h5 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1rem;
  color: var(--color-text);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .model-info {
    flex-direction: column;
    gap: 8px;
  }
}

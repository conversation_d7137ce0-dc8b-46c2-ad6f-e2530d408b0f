import { usePassLogic } from '../../contexts/PassLogicContext';
import { ComparisonSummary as ComparisonSummaryType } from '../../types';
import OverallStatsSection from './OverallStatsSection';
import CategoryStatsSection from './CategoryStatsSection';
import './ComparisonSummary.css';

interface ComparisonSummaryProps {
  comparisonSummary: ComparisonSummaryType;
  refModelName: string;
  modelNames: string[];
  datasetPath: string;
}

function ComparisonSummary({ comparisonSummary, refModelName, modelNames, datasetPath }: ComparisonSummaryProps) {
  // Get the current pass logic
  const { passLogic } = usePassLogic();

  // Filter out the reference model from the model names if it's included
  const comparisonModelNames = modelNames.filter(name => name !== refModelName);

  return (
    <div className="comparison-summary">
      <h2>Summary</h2>
      <div className="summary-content">
        <p><strong>Reference Model:</strong> {refModelName}</p>
        <p><strong>Comparison Models:</strong> {comparisonModelNames.join(', ')}</p>
        <p><strong>Dataset:</strong> {datasetPath}</p>

        <OverallStatsSection
          comparisonSummary={comparisonSummary}
          refModelName={refModelName}
          comparisonModelNames={comparisonModelNames}
          passLogic={passLogic}
        />

        <div className="category-stats">
          <CategoryStatsSection
            comparisonSummary={comparisonSummary}
            refModelName={refModelName}
            comparisonModelNames={comparisonModelNames}
            passLogic={passLogic}
          />
        </div>
      </div>
    </div>
  );
}

export default ComparisonSummary;

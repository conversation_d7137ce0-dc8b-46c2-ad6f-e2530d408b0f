.pass-logic-selector {
  background-color: #e9ecef;
  padding: 10px 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.pass-logic-selector h3 {
  margin: 0 15px 0 0;
  font-size: 1em;
}

.pass-logic-btn-group {
  display: flex;
  flex-wrap: wrap;
}

.pass-logic-btn {
  margin: 5px;
  font-size: 0.9em;
}

/* Override the active state for pass logic buttons */
.pass-logic-btn.active {
  background-color: var(--color-success);
  border-color: var(--color-success);
  color: white;
  font-weight: 500;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.pass-logic-description {
  flex-basis: 100%;
  margin-top: 8px;
  font-size: 0.85em;
  color: #6c757d;
  padding-left: 5px;
}

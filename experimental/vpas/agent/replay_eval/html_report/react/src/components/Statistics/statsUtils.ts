import { EvalResult, PassLogicType, EvalSummary } from '../../types';
import { calculateSampleStatistics, calculateSuccessRate, formatSuccessRate } from '../../utils/statistics';

export interface ModelStats {
  modelName: string;
  stats: {
    total: number;
    correct: number;
    totalAttempts: number;
    totalCorrectAttempts: number;
  };
  successRate: number;
  formattedSuccessRate: string;
  improvements: number;
  regressions: number;
}

/**
 * Calculate statistics for a model in a specific category
 */
export function calculateModelCategoryStats(
  modelName: string,
  modelEvalResults: EvalResult[],
  refEvalResults: EvalResult[],
  passLogic: PassLogicType,
  category?: string
): ModelStats {
  // Filter to just this category if specified
  const filteredModelResults = category
    ? modelEvalResults.filter(result => (result.sample.category || 'Uncategorized') === category)
    : modelEvalResults;

  const filteredRefResults = category
    ? refEvalResults.filter(result => (result.sample.category || 'Uncategorized') === category)
    : refEvalResults;

  // Calculate statistics for this model
  const modelSampleStats = filteredModelResults.map(evalResult =>
    calculateSampleStatistics(evalResult, passLogic)
  );

  // Calculate overall statistics for this model
  const modelStats = {
    total: modelSampleStats.length,
    correct: modelSampleStats.filter(stats => stats.passes).length,
    totalAttempts: modelSampleStats.reduce((sum, stats) => sum + stats.totalAttempts, 0),
    totalCorrectAttempts: modelSampleStats.reduce((sum, stats) => sum + stats.correctAttempts, 0)
  };

  // Calculate success rate for this model
  const successRate = calculateSuccessRate({
    totalSamples: modelStats.total,
    correctSamples: modelStats.correct,
    totalAttempts: modelStats.totalAttempts,
    totalCorrectAttempts: modelStats.totalCorrectAttempts
  }, passLogic);

  const formattedSuccessRate = formatSuccessRate(successRate);

  // Calculate improvements and regressions
  let improvements = 0;
  let regressions = 0;

  filteredModelResults.forEach(modelEvalResult => {
    // Find the corresponding sample in the reference model
    const refEvalResult = filteredRefResults.find(result =>
      result.sample.name === modelEvalResult.sample.name
    );

    if (refEvalResult) {
      // Calculate if both samples pass or fail
      const refStats = calculateSampleStatistics(refEvalResult, passLogic);
      const modelStats = calculateSampleStatistics(modelEvalResult, passLogic);

      if (modelStats.passes && !refStats.passes) {
        improvements++;
      } else if (!modelStats.passes && refStats.passes) {
        regressions++;
      }
    }
  });

  return {
    modelName,
    stats: modelStats,
    successRate,
    formattedSuccessRate,
    improvements,
    regressions
  };
}

/**
 * Group eval results by category
 */
export function groupResultsByCategory(evalResults: EvalResult[]): Record<string, EvalResult[]> {
  const resultsByCategory: Record<string, EvalResult[]> = {};

  evalResults.forEach(evalResult => {
    const category = evalResult.sample.category || 'Uncategorized';
    if (!resultsByCategory[category]) {
      resultsByCategory[category] = [];
    }
    resultsByCategory[category].push(evalResult);
  });

  return resultsByCategory;
}

/**
 * Extract all statistics from EvalSummary
 * @param summary The EvalSummary object containing stats
 * @returns An object with all statistics
 */
export function extractAllStats(summary: EvalSummary): Record<string, number> {
  if (!summary.stats) return {};

  // Include all stats
  const allStats: Record<string, number> = {};

  Object.entries(summary.stats).forEach(([key, value]) => {
    allStats[key] = typeof value === 'number' ? value : 0;
  });

  return allStats;
}

/**
 * Format a number for display
 * @param value The number to format
 * @returns Formatted string
 */
export function formatNumber(value: number): string {
  // Format with 2 decimal places if it's a float
  return Number.isInteger(value) ? value.toString() : value.toFixed(2);
}

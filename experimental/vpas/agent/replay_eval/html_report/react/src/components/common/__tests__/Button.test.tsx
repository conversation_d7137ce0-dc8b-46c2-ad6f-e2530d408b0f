import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Button from '../Button'

describe('Button', () => {
  it('renders with default props', () => {
    render(<Button>Click me</Button>)

    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('button')
    expect(button).toHaveClass('button-primary')
    expect(button).toHaveClass('button-md')
  })

  it('renders with custom variant and size', () => {
    render(<Button variant="success" size="lg">Success</Button>)

    const button = screen.getByRole('button', { name: /success/i })
    expect(button).toHaveClass('button-success')
    expect(button).toHaveClass('button-lg')
  })

  it('renders as active', () => {
    render(<Button isActive>Active</Button>)

    const button = screen.getByRole('button', { name: /active/i })
    expect(button).toHaveClass('active')
  })

  it('applies additional className', () => {
    render(<Button className="custom-class">Custom</Button>)

    const button = screen.getByRole('button', { name: /custom/i })
    expect(button).toHaveClass('custom-class')
  })

  it('handles click events', async () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)

    const button = screen.getByRole('button', { name: /click me/i })
    await userEvent.click(button)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('can be disabled', () => {
    render(<Button disabled>Disabled</Button>)

    const button = screen.getByRole('button', { name: /disabled/i })
    expect(button).toBeDisabled()
  })
})

import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import AllStats from '../AllStats';

describe('AllStats', () => {
  it('renders a message when no stats are provided', () => {
    render(<AllStats stats={{}} />);
    expect(screen.getByText('No statistics available.')).toBeInTheDocument();
  });

  it('renders stats correctly', () => {
    const stats = {
      'test_metric': 42,
      'another_metric': 3.14159
    };

    render(<AllStats stats={stats} />);

    // Check if the formatted names are displayed
    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    expect(screen.getByText('Another Metric')).toBeInTheDocument();

    // Check if the values are displayed correctly
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('3.14')).toBeInTheDocument();
  });
});

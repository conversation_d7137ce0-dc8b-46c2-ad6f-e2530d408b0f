import React, { useMemo } from 'react';
import { ComparisonSummary, PassLogicType } from '../../types';
import StatsTable from './StatsTable';
import ModelStatsRow from './ModelStatsRow';
import { useReferenceStats, useComparisonModelStats } from './StatsUtils';
import { groupResultsByCategory } from './statsUtils';

interface CategoryStatsSectionProps {
  comparisonSummary: ComparisonSummary;
  refModelName: string;
  comparisonModelNames: string[];
  passLogic: PassLogicType;
}

const CategoryStatsSection: React.FC<CategoryStatsSectionProps> = ({
  comparisonSummary,
  refModelName,
  comparisonModelNames,
  passLogic
}) => {
  // Get the eval results from the reference summary
  const evalResults = useMemo(() =>
    Object.values(comparisonSummary.ref_summary.eval_results),
    [comparisonSummary]
  );

  // Group eval results by category
  const resultsByCategory = useMemo(() =>
    groupResultsByCategory(evalResults),
    [evalResults]
  );

  // Sort categories alphabetically
  const sortedCategories = useMemo(() =>
    Object.keys(resultsByCategory).sort(),
    [resultsByCategory]
  );

  if (sortedCategories.length === 0) {
    return <p>No category statistics available.</p>;
  }

  return (
    <>
      <h3>Category Success Rates</h3>
      {sortedCategories.map(category => {
        // Get the eval results for this category
        const categoryEvalResults = resultsByCategory[category];

        // Calculate reference model statistics for this category
        const { stats: refCategoryStats, formattedSuccessRate: refCategoryFormattedSuccessRate } =
          useReferenceStats(categoryEvalResults, passLogic);

        // Calculate comparison model statistics for this category
        const modelCategoryStats = useComparisonModelStats(
          comparisonSummary,
          comparisonModelNames,
          categoryEvalResults,
          passLogic,
          category
        );

        // We don't need to extract stats here as we're using StatsTableNew directly

        return (
          <StatsTable
            key={category}
            title={category}
            passLogic={passLogic}
            averageStats={undefined}
          >
              {/* Reference model row */}
              <ModelStatsRow
                modelName={refModelName}
                successRate={refCategoryFormattedSuccessRate}
                correct={passLogic === 'aggregate' ? refCategoryStats.totalCorrectAttempts : refCategoryStats.correctSamples}
                total={passLogic === 'aggregate' ? refCategoryStats.totalAttempts : refCategoryStats.totalSamples}
                isReference={true}
              />

              {/* Comparison models rows */}
              {modelCategoryStats.map(modelStat => (
                <ModelStatsRow
                  key={modelStat.modelName}
                  modelName={modelStat.modelName}
                  successRate={modelStat.formattedSuccessRate}
                  correct={passLogic === 'aggregate' ? modelStat.stats.totalCorrectAttempts : modelStat.stats.correct}
                  total={passLogic === 'aggregate' ? modelStat.stats.totalAttempts : modelStat.stats.total}
                  improvements={modelStat.improvements}
                  regressions={modelStat.regressions}
                />
              ))}
            </StatsTable>
        );
      })}
    </>
  );
};

export default CategoryStatsSection;

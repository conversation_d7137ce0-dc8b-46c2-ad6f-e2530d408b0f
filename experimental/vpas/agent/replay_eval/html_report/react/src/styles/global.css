/* Global styles based on the original report_styles.css */

:root {
  --color-primary: #007bff;
  --color-primary-light: #e2e6ea;
  --color-primary-dark: #0056b3;
  --color-secondary: #6c757d;
  --color-success: #28a745;
  --color-success-light: #d4edda;
  --color-success-dark: #155724;
  --color-danger: #dc3545;
  --color-danger-light: #f8d7da;
  --color-danger-dark: #721c24;
  --color-warning: #ffc107;
  --color-info: #17a2b8;
  --color-light: #f8f9fa;
  --color-dark: #343a40;
  --color-white: #fff;
  --color-gray: #6c757d;
  --color-gray-dark: #343a40;
  --color-gray-light: #f8f9fa;

  --font-family-sans-serif: Arial, sans-serif;
  --font-family-monospace: monospace;

  --border-radius: 5px;
  --spacing-xs: 5px;
  --spacing-sm: 10px;
  --spacing-md: 15px;
  --spacing-lg: 20px;
  --spacing-xl: 30px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans-serif);
  margin: var(--spacing-lg);
  line-height: 1.5;
  color: var(--color-dark);
}

h1, h2, h3, h4 {
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
}

.report-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Utility classes */
.correct {
  color: var(--color-success);
}

.incorrect {
  color: var(--color-danger);
}

.improvement {
  background-color: var(--color-success-light);
  border-left: 4px solid var(--color-success);
}

.regression {
  background-color: var(--color-danger-light);
  border-left: 4px solid var(--color-danger);
}

.neutral {
  background-color: var(--color-gray-light);
  border-left: 4px solid var(--color-gray);
}

.reference {
  background-color: #f0f0f0;
  border-left: 4px solid var(--color-gray-dark);
}

/* Pre and code formatting */
pre {
  background-color: var(--color-gray-light);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: var(--font-family-monospace);
}

/* Tables */
table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: var(--spacing-md);
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

th {
  background-color: #f2f2f2;
}

/* Diff display */
.diff-display {
  margin: 0;
  overflow-x: auto;
}

.diff-container {
  font-family: var(--font-family-monospace);
  white-space: pre;
  overflow-x: auto;
}

.diff-line {
  padding: 2px 5px;
}

.diff-added {
  background-color: #e6ffed;
  color: #22863a;
}

.diff-removed {
  background-color: #ffeef0;
  color: #cb2431;
}

.diff-unchanged {
  color: #24292e;
}

.diff-equal {
  padding: 10px;
  background-color: #f6f8fa;
  border-radius: 5px;
}

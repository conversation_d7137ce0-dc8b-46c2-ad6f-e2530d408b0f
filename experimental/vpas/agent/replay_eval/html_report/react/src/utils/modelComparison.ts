import { EvalResult, PassLogicType, ComparisonCounts } from '../types';
import { doesSamplePass } from './passLogic';

/**
 * Compare results between reference and model content.
 *
 * @param refCorrectAttempts - Number of correct attempts for reference model
 * @param refTotalAttempts - Total number of attempts for reference model
 * @param modelCorrectAttempts - Number of correct attempts for comparison model
 * @param modelTotalAttempts - Total number of attempts for comparison model
 * @param passLogic - The pass logic to use
 * @returns 'improvement', 'regression', or 'neutral'
 */
export function compareModelResults(
  refCorrectAttempts: number,
  refTotalAttempts: number,
  modelCorrectAttempts: number,
  modelTotalAttempts: number,
  passLogic: PassLogicType
): 'improvement' | 'regression' | 'neutral' {
  // For aggregate mode, compare the success rates directly
  if (passLogic === 'aggregate') {
    const refSuccessRate = refTotalAttempts > 0 ? refCorrectAttempts / refTotalAttempts : 0;
    const modelSuccessRate = modelTotalAttempts > 0 ? modelCorrectAttempts / modelTotalAttempts : 0;

    if (modelSuccessRate > refSuccessRate) {
      return 'improvement';
    } else if (modelSuccessRate < refSuccessRate) {
      return 'regression';
    } else {
      return 'neutral';
    }
  } else {
    // For other modes, use the pass/fail logic
    const refPasses = doesSamplePass(refCorrectAttempts, refTotalAttempts, passLogic);
    const modelPasses = doesSamplePass(modelCorrectAttempts, modelTotalAttempts, passLogic);

    // Determine comparison result
    if (modelPasses && !refPasses) {
      return 'improvement';
    } else if (!modelPasses && refPasses) {
      return 'regression';
    } else {
      return 'neutral';
    }
  }
}

/**
 * Count improvements and regressions for a model compared to reference.
 *
 * @param sampleResults - List of sample results
 * @param modelName - Name of the model to compare
 * @param refModelName - Name of the reference model
 * @param passLogic - The pass logic to use
 * @returns Object with improvements and regressions counts
 */
export function countImprovementsAndRegressions(
  _evalResults: EvalResult[],
  _modelName: string,
  _refModelName: string,
  _passLogic: PassLogicType
): ComparisonCounts {
  // For now, we'll just return a placeholder since we don't have multiple models
  return { improvements: 0, regressions: 0 };

  /* This is the original implementation that would work with multiple models
  let improvements = 0;
  let regressions = 0;

  sampleResults.forEach(sampleResult => {
    // In a real implementation, you would have model results for different models
    // For now, we'll just return a placeholder
  });

  return { improvements, regressions };
  */
}

/**
 * Count improvements and regressions for a model in a specific category.
 *
 * @param sampleResults - List of sample results
 * @param modelName - Name of the model to compare
 * @param refModelName - Name of the reference model
 * @param category - The category to filter by
 * @param passLogic - The pass logic to use
 * @returns Object with improvements and regressions counts
 */
export function countCategoryImprovementsAndRegressions(
  evalResults: EvalResult[],
  modelName: string,
  refModelName: string,
  category: string,
  passLogic: PassLogicType
): ComparisonCounts {
  // Filter samples by category
  const categoryEvalResults = evalResults.filter(evalResult =>
    evalResult.sample.category === category
  );

  // Count improvements and regressions for the filtered samples
  return countImprovementsAndRegressions(
    categoryEvalResults,
    modelName,
    refModelName,
    passLogic
  );
}

import { describe, it, expect } from 'vitest'
import {
  calculateSampleStatistics,
  calculateSuccessRate,
  formatSuccessRate
} from '../statistics'
import { EvalResult, ChatResultNodeType, CategoryStatistics, OverallStatistics } from '../../types'

describe('calculateSampleStatistics', () => {
  it('should calculate statistics for a sample with correct and incorrect attempts', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: 'Test Category'
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: [
        {
          is_correct: true,
          explanation: '',
          response: [{ id: 1, type: ChatResultNodeType.RAW_RESPONSE, content: 'Correct attempt' }],
          stats: {},
        },
        {
          is_correct: false,
          explanation: '',
          response: [{ id: 2, type: ChatResultNodeType.RAW_RESPONSE, content: 'Incorrect attempt' }],
          stats: {},
        },
        {
          is_correct: true,
          explanation: '',
          response: [{ id: 3, type: ChatResultNodeType.RAW_RESPONSE, content: 'Another correct attempt' }],
          stats: {},
        }
      ]
    }

    const stats = calculateSampleStatistics(evalResult, 'at_least_one')

    expect(stats.category).toBe('Test Category')
    expect(stats.correctAttempts).toBe(2)
    expect(stats.totalAttempts).toBe(3)
    expect(stats.passes).toBe(true)
  })

  it('should handle samples with no category', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: ''
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: [
        {
          is_correct: true,
          explanation: '',
          response: [{ id: 4, type: ChatResultNodeType.RAW_RESPONSE, content: 'Correct attempt' }],
          stats: {},
        }
      ]
    }

    const stats = calculateSampleStatistics(evalResult, 'at_least_one')

    expect(stats.category).toBe('Uncategorized')
  })

  it('should handle samples with no attempts', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: 'Test Category'
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: []
    }

    const stats = calculateSampleStatistics(evalResult, 'at_least_one')

    expect(stats.correctAttempts).toBe(0)
    expect(stats.totalAttempts).toBe(0)
    expect(stats.passes).toBe(false)
  })
})

describe('calculateSuccessRate', () => {
  it('should calculate success rate for aggregate pass logic', () => {
    const stats: OverallStatistics = {
      totalSamples: 10,
      correctSamples: 5,
      totalAttempts: 20,
      totalCorrectAttempts: 10
    }

    const rate = calculateSuccessRate(stats, 'aggregate')
    expect(rate).toBe(0.5) // 10/20 = 0.5
  })

  it('should calculate success rate for CategoryStatistics', () => {
    const stats: CategoryStatistics = {
      total: 10,
      correct: 5,
      totalAttempts: 20,
      totalCorrectAttempts: 10
    }

    const rate = calculateSuccessRate(stats, 'at_least_one')
    expect(rate).toBe(0.5) // 5/10 = 0.5
  })

  it('should calculate success rate for OverallStatistics', () => {
    const stats: OverallStatistics = {
      totalSamples: 10,
      correctSamples: 5,
      totalAttempts: 20,
      totalCorrectAttempts: 10
    }

    const rate = calculateSuccessRate(stats, 'at_least_one')
    expect(rate).toBe(0.5) // 5/10 = 0.5
  })

  it('should handle zero division', () => {
    const statsCategory: CategoryStatistics = {
      total: 0,
      correct: 0,
      totalAttempts: 0,
      totalCorrectAttempts: 0
    }

    const statsOverall: OverallStatistics = {
      totalSamples: 0,
      correctSamples: 0,
      totalAttempts: 0,
      totalCorrectAttempts: 0
    }

    expect(calculateSuccessRate(statsCategory, 'at_least_one')).toBe(0)
    expect(calculateSuccessRate(statsOverall, 'at_least_one')).toBe(0)
    expect(calculateSuccessRate(statsCategory, 'aggregate')).toBe(0)
  })
})

describe('formatSuccessRate', () => {
  it('should format success rate as percentage', () => {
    expect(formatSuccessRate(0.5)).toBe('50.00%')
    expect(formatSuccessRate(0.75)).toBe('75.00%')
    expect(formatSuccessRate(0)).toBe('0.00%')
    expect(formatSuccessRate(1)).toBe('100.00%')
  })
})

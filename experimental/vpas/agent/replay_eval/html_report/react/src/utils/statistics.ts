import {
  EvalResult,
  Attempt,
  PassLogicType,
  CategoryStatistics,
  OverallStatistics,
  ModelStatistics
} from '../types';
import { doesSamplePass } from './passLogic';

/**
 * Calculate statistics for a single sample.
 *
 * @param sampleResult - The sample result
 * @param passLogic - The pass logic to use
 * @returns Sample statistics
 */
export function calculateSampleStatistics(evalResult: EvalResult, passLogic: PassLogicType) {
  const category = evalResult.sample.category || 'Uncategorized';
  const attempts = evalResult.attempts || [];
  let correctAttempts = 0;

  attempts.forEach((attempt: Attempt) => {
    if (attempt.is_correct) correctAttempts++;
  });

  // Determine if sample passes based on selected logic
  const samplePasses = doesSamplePass(correctAttempts, attempts.length, passLogic);

  return {
    category,
    correctAttempts,
    totalAttempts: attempts.length,
    passes: samplePasses
  };
}



/**
 * Calculate category statistics from sample statistics.
 *
 * @param sampleStats - Array of sample statistics
 * @param passLogic - The pass logic to use
 * @returns Category statistics
 */
export function calculateCategoryStatistics(
  sampleStats: ReturnType<typeof calculateSampleStatistics>[],
  passLogic: PassLogicType
): Record<string, CategoryStatistics> {
  const categoryStats: Record<string, CategoryStatistics> = {};

  sampleStats.forEach(stats => {
    const { category, correctAttempts, totalAttempts, passes } = stats;

    // Initialize category stats if needed
    if (!categoryStats[category]) {
      categoryStats[category] = {
        total: 0,
        correct: 0,
        totalAttempts: 0,
        totalCorrectAttempts: 0
      };
    }

    categoryStats[category].total++;
    categoryStats[category].totalAttempts += totalAttempts;
    categoryStats[category].totalCorrectAttempts += correctAttempts;

    // For non-aggregate modes, count samples that pass
    if (passLogic !== 'aggregate') {
      if (passes) {
        categoryStats[category].correct++;
      }
    }
    // For aggregate mode, we'll use totalCorrectAttempts/totalAttempts later
  });

  return categoryStats;
}

/**
 * Calculate overall statistics from sample statistics.
 *
 * @param sampleStats - Array of sample statistics
 * @param passLogic - The pass logic to use
 * @returns Overall statistics
 */
export function calculateOverallStatistics(
  sampleStats: ReturnType<typeof calculateSampleStatistics>[],
  passLogic: PassLogicType
): OverallStatistics {
  let totalSamples = sampleStats.length;
  let correctSamples = 0;
  let totalAttempts = 0;
  let totalCorrectAttempts = 0;

  sampleStats.forEach(stats => {
    const { correctAttempts, totalAttempts: attempts, passes } = stats;

    totalAttempts += attempts;
    totalCorrectAttempts += correctAttempts;

    // For non-aggregate modes, count samples that pass
    if (passes && passLogic !== 'aggregate') {
      correctSamples++;
    }
  });

  return {
    totalSamples,
    correctSamples,
    totalAttempts,
    totalCorrectAttempts
  };
}

/**
 * Calculate success rate based on statistics and pass logic.
 *
 * @param stats - The statistics object
 * @param passLogic - The pass logic to use
 * @returns Success rate as a decimal (0-1)
 */
export function calculateSuccessRate(
  stats: OverallStatistics | CategoryStatistics,
  passLogic: PassLogicType
): number {
  if (passLogic === 'aggregate') {
    return stats.totalAttempts > 0 ? stats.totalCorrectAttempts / stats.totalAttempts : 0;
  } else {
    // Check if we're dealing with CategoryStatistics (which has total and correct)
    // or OverallStatistics (which has totalSamples and correctSamples)
    if ('total' in stats && stats.total !== undefined && 'correct' in stats && stats.correct !== undefined) {
      return stats.total > 0 ? stats.correct / stats.total : 0;
    } else if ('totalSamples' in stats && 'correctSamples' in stats) {
      return stats.totalSamples > 0 ? stats.correctSamples / stats.totalSamples : 0;
    } else {
      return 0; // Default case
    }
  }
}

/**
 * Format success rate as a string with percentage.
 *
 * @param successRate - The success rate as a decimal (0-1)
 * @returns Formatted success rate string
 */
export function formatSuccessRate(successRate: number): string {
  return `${(successRate * 100).toFixed(2)}%`;
}

/**
 * Calculate statistics for all models.
 *
 * @param sampleResults - List of sample results
 * @param modelNames - Array of model names
 * @param passLogic - The pass logic to use
 * @returns Model statistics
 */
export function calculateModelStatistics(
  evalResults: EvalResult[],
  modelNames: string[],
  passLogic: PassLogicType
): Record<string, ModelStatistics> {
  const modelStats: Record<string, ModelStatistics> = {};

  // Initialize model stats
  modelNames.forEach(modelName => {
    modelStats[modelName] = {
      totalSamples: 0,
      correctSamples: 0,
      totalAttempts: 0,
      totalCorrectAttempts: 0,
      categoryStats: {}
    };
  });

  // Process each sample
  evalResults.forEach(evalResult => {
    const category = evalResult.sample.category || 'Uncategorized';
    const modelName = modelNames[0]; // For now, just use the first model

    // Initialize category stats if needed
    if (!modelStats[modelName].categoryStats[category]) {
      modelStats[modelName].categoryStats[category] = {
        total: 0,
        correct: 0,
        totalAttempts: 0,
        totalCorrectAttempts: 0
      };
    }

    // Increment total samples
    modelStats[modelName].totalSamples++;
    modelStats[modelName].categoryStats[category].total++;

    // Count correct attempts
    let correctAttempts = 0;
    const attempts = evalResult.attempts || [];

    attempts.forEach((attempt: Attempt) => {
      if (attempt.is_correct) correctAttempts++;
    });

    // Determine if sample passes based on selected logic
    const samplePasses = doesSamplePass(correctAttempts, attempts.length, passLogic);

    // Update stats
    if (samplePasses) {
      modelStats[modelName].correctSamples++;
      modelStats[modelName].categoryStats[category].correct++;
    }

    // Store attempt counts for aggregate calculation
    modelStats[modelName].totalAttempts += attempts.length;
    modelStats[modelName].totalCorrectAttempts += correctAttempts;
    modelStats[modelName].categoryStats[category].totalAttempts += attempts.length;
    modelStats[modelName].categoryStats[category].totalCorrectAttempts += correctAttempts;
  });

  return modelStats;
}

import { describe, it, expect } from 'vitest'
import { doesSamplePass, countCorrectAttempts } from '../passLogic'
import { EvalResult, ChatResultNodeType } from '../../types'

describe('doesSamplePass', () => {
  it('should return true for at_least_one when there is at least one correct attempt', () => {
    expect(doesSamplePass(1, 3, 'at_least_one')).toBe(true)
    expect(doesSamplePass(0, 3, 'at_least_one')).toBe(false)
  })

  it('should return true for majority when more than half of attempts are correct', () => {
    expect(doesSamplePass(2, 3, 'majority')).toBe(true)
    expect(doesSamplePass(1, 3, 'majority')).toBe(false)
    expect(doesSamplePass(1, 2, 'majority')).toBe(false)
    expect(doesSamplePass(2, 2, 'majority')).toBe(true)
  })

  it('should return true for all when all attempts are correct', () => {
    expect(doesSamplePass(3, 3, 'all')).toBe(true)
    expect(doesSamplePass(2, 3, 'all')).toBe(false)
  })

  it('should return true for aggregate when there is at least one correct attempt', () => {
    expect(doesSamplePass(1, 3, 'aggregate')).toBe(true)
    expect(doesSamplePass(0, 3, 'aggregate')).toBe(false)
  })
})

describe('countCorrectAttempts', () => {
  it('should count correct attempts in a sample result', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: 'Test Category'
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: [
        {
          is_correct: true,
          explanation: '',
          response: [{ id: 1, type: ChatResultNodeType.RAW_RESPONSE, content: 'Correct attempt' }],
          stats: {},
        },
        {
          is_correct: false,
          explanation: '',
          response: [{ id: 2, type: ChatResultNodeType.RAW_RESPONSE, content: 'Incorrect attempt' }],
          stats: {},
        },
        {
          is_correct: true,
          explanation: '',
          response: [{ id: 3, type: ChatResultNodeType.RAW_RESPONSE, content: 'Another correct attempt' }],
          stats: {},
        }
      ]
    }

    expect(countCorrectAttempts(evalResult)).toBe(2)
  })

  it('should return 0 when there are no correct attempts', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: 'Test Category'
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: [
        {
          is_correct: false,
          explanation: '',
          response: [{ id: 4, type: ChatResultNodeType.RAW_RESPONSE, content: 'Incorrect attempt' }],
          stats: {},
        },
        {
          is_correct: false,
          explanation: '',
          response: [{ id: 5, type: ChatResultNodeType.RAW_RESPONSE, content: 'Another incorrect attempt' }],
          stats: {},
        }
      ]
    }

    expect(countCorrectAttempts(evalResult)).toBe(0)
  })

  it('should return 0 when there are no attempts', () => {
    const evalResult: EvalResult = {
      sample: {
        request_id: 'sample1',
        name: 'Test Sample',
        category: 'Test Category'
      },
      sample_tool_definitions: [],
      model_tool_definitions: [],
      attempts: []
    }

    expect(countCorrectAttempts(evalResult)).toBe(0)
  })
})

from pathlib import Path
import tempfile
from typing import Any

from experimental.guy.agent_qa.file_edit.file_edit_utils import get_augment_token
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool import (
    ExtendedToolImplOutput,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_utils import (
    extract_str_replace_entries,
)
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManagerImpl,
)
from experimental.vpas.agent.str_replace_editor.extract_testcase_from_request import (
    extract_tool_input_and_content,
)
from research.agents.tools import ToolCallLogger
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_with_multiedit import (
    StrReplaceEditorToolWithMultiEdit,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_with_multiedit_flat import (
    StrReplaceEditorToolWithMultiEditFlat,
)
from base.prompt_format.common import ChatR<PERSON>ultNodeType
from base.prompt_format_chat.prompt_formatter import Chat<PERSON>romptInput


def call_str_replace_editor(
    request_id: str,
    str_replace_editor_tool_cls: Any,
    tool_input: dict[str, Any],
) -> tuple[bool, str]:
    _, original_content = extract_tool_input_and_content(request_id)
    if not original_content:
        return False, "Failed to extract original content"

    tool_call_logger = ToolCallLogger()

    token = get_augment_token()
    augment_prototyping_client = AugmentPrototypingClient(
        get_staging_api_proxy_url(), token
    )
    temp_dir = Path(tempfile.mkdtemp(prefix="call_str_replace_editor_"))
    workspace_manager = WorkspaceManagerImpl(augment_prototyping_client, temp_dir)

    # Create temporary file with original content
    file_path = Path(tool_input["path"])
    abs_path = workspace_manager.root / file_path
    abs_path.parent.mkdir(parents=True, exist_ok=True)
    abs_path.write_text(original_content)
    workspace_manager.update()

    tool = str_replace_editor_tool_cls(tool_call_logger, workspace_manager)
    tool_result = tool.run_impl(tool_input)
    assert type(tool_result) == ExtendedToolImplOutput
    return bool(tool_result.success), tool_result.tool_output[:200]


def extract_str_replace_entries_any_schema(
    tool_input: dict[str, Any],
) -> list[dict[str, Any]] | None:
    # Check if using nested schema
    if "str_replace_entries" in tool_input:
        return tool_input["str_replace_entries"]
    # Check if using flat schema
    elif "old_str_1" in tool_input or "old_str" in tool_input:
        return extract_str_replace_entries(tool_input)

    return None


def call_str_replace_editor_any_schema(
    request_id: str,
    tool_input: dict[str, Any],
) -> tuple[bool, str]:
    tool_cls = None
    if "old_str_1" in tool_input:
        tool_cls = StrReplaceEditorToolWithMultiEditFlat
    elif "str_replace_entries" in tool_input:
        tool_cls = StrReplaceEditorToolWithMultiEdit
    else:
        return False, "Failed to determine str_replace_editor tool class"

    return call_str_replace_editor(request_id, tool_cls, tool_input)


def convert_history_to_flat_schema(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "str-replace-editor"
            ):
                continue
            tool_input = node.tool_use.input
            if tool_input.get("command") == "str_replace":
                str_replace_entries = tool_input.get("str_replace_entries", [])
                for i, entry in enumerate(str_replace_entries):
                    suffix = f"_{i + 1}"
                    tool_input[f"old_str{suffix}"] = entry["old_str"]
                    tool_input[f"new_str{suffix}"] = entry["new_str"]
                    tool_input[f"old_str_start_line_number{suffix}"] = entry[
                        "old_str_start_line_number"
                    ]
                    tool_input[f"old_str_end_line_number{suffix}"] = entry[
                        "old_str_end_line_number"
                    ]
                tool_input.pop("str_replace_entries", None)
            if tool_input.get("command") == "insert":
                insert_line_entries = tool_input.get("insert_line_entries", [])
                for i, entry in enumerate(insert_line_entries):
                    suffix = f"_{i + 1}"
                    tool_input[f"insert_line{suffix}"] = entry["insert_line"]
                    tool_input[f"new_str{suffix}"] = entry["new_str"]
                tool_input.pop("insert_line_entries", None)

    return chat_prompt_input

from abc import ABC, abstractmethod
import random
from typing import <PERSON><PERSON>, Any
import numpy as np

from research.llm_apis.llm_client import (
    LL<PERSON>lient,
    AssistantContentBlock,
    LLMMessages,
    ToolParam,
    AnthropicDirectClient,
)

# Anthropic model names for the default random router
ANTHROPIC_SONNET_3_7_MODEL = "claude-3-7-sonnet-20250219"
ANTHROPIC_HAIKU_3_5_MODEL = (
    "claude-3-5-haiku-20241022"  # Based on available Haiku 3.5 identifiers
)


class ModelTurnRouter(LLMClient, ABC):
    """
    Abstract base class for routers that select an LLMClient on a per-turn basis.
    It implements the LLMClient interface itself, acting as a proxy.
    """

    @abstractmethod
    def _get_client_for_turn(self) -> LLMClient:
        """
        Subclasses must implement this to select and return an LLMClient
        instance for the current generation turn.
        """
        pass

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,  # Included to match LLMClient interface
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """
        Selects an LLM client for the current turn using _get_client_for_turn()
        and then delegates the generate call to that client.
        """

        client_for_this_turn = self._get_client_for_turn()

        print(
            f"ModelTurnRouter: Delegating to client {client_for_this_turn.__class__.__name__} with model_name: {getattr(client_for_this_turn, 'model_name', 'N/A')}"
        )

        return client_for_this_turn.generate(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            thinking_tokens=thinking_tokens,  # Pass through
        )


class DefaultAnthropicSonnetHaikuRouter(ModelTurnRouter):
    """
    A default model router that randomly selects between two hardcoded
    Anthropic models (Sonnet 3.7 and Haiku 3.5) using AnthropicDirectClient.
    Relies on AnthropicDirectClient's default API key handling.
    """

    def __init__(
        self,
        max_retries: int,
        use_low_qos_server: bool = False,
        thinking_tokens: int = 0,
        haiku_rate: float = 0.0,
    ):
        self.client_sonnet = AnthropicDirectClient(
            model_name=ANTHROPIC_SONNET_3_7_MODEL,
            max_retries=max_retries,
            use_low_qos_server=use_low_qos_server,
            thinking_tokens=thinking_tokens,
        )
        self.client_haiku = AnthropicDirectClient(
            model_name=ANTHROPIC_HAIKU_3_5_MODEL,
            max_retries=max_retries,
            use_low_qos_server=use_low_qos_server,
            thinking_tokens=thinking_tokens,
        )
        self.clients = [self.client_sonnet, self.client_haiku]
        self.weights = np.array([1.0 - haiku_rate, haiku_rate])
        self._random_state = np.random.RandomState(42)
        print(
            f"DefaultAnthropicSonnetHaikuRouter initialized with models: {ANTHROPIC_SONNET_3_7_MODEL} and {ANTHROPIC_HAIKU_3_5_MODEL}"
        )

    def _get_client_for_turn(self) -> LLMClient:
        selected_client = self._random_state.choice(self.clients, p=self.weights)
        # print(f"DefaultAnthropicSonnetHaikuRouter: Selected model {getattr(selected_client, 'model_name', 'N/A')} for this turn.")
        return selected_client

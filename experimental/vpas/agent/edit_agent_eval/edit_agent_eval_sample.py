import json
from pathlib import Path
import re
from typing import List
from dataclasses import dataclass, field
import hashlib
import copy
from base.languages.language_guesser import guess_language
from dataclasses_json import DataClassJsonMixin  # type: ignore

from experimental.vpas.agent.edit_agent_eval.fetch_edit_request import (
    get_edit_request,
    get_edit_response,
)
from research.agents.tools import LoggedToolCall


@dataclass
class LoggedToolCallWithMetadata(LoggedToolCall, DataClassJsonMixin):
    # path to the original log file
    log_file_path: str = field(default_factory=str)
    # index of the tool call in the original list of tool calls in the log file
    log_file_index: int = field(default_factory=int)


@dataclass
class EditAgentEvalSample(DataClassJsonMixin):
    edit_agent_call: LoggedToolCallWithMetadata | None = None
    edit_file_calls: List[LoggedToolCallWithMetadata] = field(default_factory=list)

    uuid: str = field(init=True, default="uuid_not_set")
    file_path: str = field(init=True, default="file_path_not_set")
    short_edit_description: str = field(
        init=True, default="short_edit_description_not_set"
    )
    failed_after_max_retries: bool = field(default=False)
    edit_request_id: str | None = field(default=None)
    original_file_content: str = field(
        init=True, default="original_file_content_not_set"
    )
    expected_modified_file_content_variants: List[str] = field(default_factory=list)
    expected_exact_match: bool = field(default=False)
    category: str = field(default_factory=str)
    is_empty_edit: bool = field(default=False)

    def has_label(self):
        expected = [
            e
            for e in self.expected_modified_file_content_variants
            if e.strip() != self.original_file_content.strip()
        ]
        return len(expected) > 0

    def __post_init__(self):
        if self.edit_agent_call is not None:
            self._extract_fields()

    def _extract_edit_request_id(self):
        if len(self.edit_file_calls) == 0:
            print("Failed to extract edit_request_id - no edit_file_calls found")
            raise ValueError("Failed to extract edit request ID from tool output")

        tool_output = self.edit_file_calls[0].tool_output
        if tool_output is None:
            print("Failed to extract edit_request_id - tool_output is None")
            raise ValueError("Failed to extract edit request ID from tool output")

        match = re.search(r"request_id=([0-9a-f-]+)", tool_output)
        if not match:
            print(
                f"Failed to extract edit_request_id - no matching pattern found in: {tool_output}"
            )
            raise ValueError("Failed to extract edit request ID from tool output")

        self.edit_request_id = match.group(1)

    def _extract_original_file_content(self):
        try:
            assert self.edit_request_id
            edit_request = get_edit_request(self.edit_request_id)
            self.original_file_content = edit_request.target_file_content
        except Exception as e:
            print(f"Failed to get original file content from request: {e}")
            # print stacktrace
            import traceback

            traceback.print_exc()
            if self.edit_agent_call:
                print(f"log_file_path: {self.edit_agent_call.log_file_path}")
            raise e

    def _set_is_empty_edit(self):
        assert self.edit_request_id
        replace_text = get_edit_response(self.edit_request_id).replace_text[0]
        self.is_empty_edit = (
            replace_text.text == "" and replace_text.start_line == replace_text.end_line
        )

    def _extract_fields(self):
        assert self.edit_agent_call
        self._extract_edit_request_id()
        self.file_path = self.edit_agent_call.tool_input["file_path"]
        self.short_edit_description = self.edit_agent_call.tool_input[
            "short_edit_description"
        ]

        assert self.edit_agent_call.tool_output is not None
        self.failed_after_max_retries = (
            self.edit_agent_call.tool_output.strip()
            == "Edit agent did not complete after max turns"
        )
        self._gen_uuid()

    def _gen_uuid(self):
        """Generate a UUID for this sample."""
        # Hash the edit request ID and file path to get a stable UUID
        hasher = hashlib.sha256()
        assert self.edit_request_id
        hasher.update(self.edit_request_id.encode())
        hasher.update(self.file_path.encode())
        self.uuid = hasher.hexdigest()[:32]

    def _get_user_from_log_path(self):
        # e.g. /mnt/efs/augment/user/guy/agent_logs/<user>/agent_log_20250202_031823.pickle
        assert self.edit_agent_call
        user_match = re.search(
            r"/agent_logs/([^/]+)/", self.edit_agent_call.log_file_path
        )
        return user_match.group(1) if user_match else "unknown"

    def to_dict(self):
        """Convert to a serializable dictionary, clearing file contents."""
        # Create a deep copy of self
        copy_self = copy.deepcopy(self)

        # Clear file content fields
        copy_self.original_file_content = ""
        copy_self.expected_modified_file_content_variants = []

        # Clear tool definitions
        for call in [copy_self.edit_agent_call] + copy_self.edit_file_calls:
            if call is not None:
                call.tool = None

        return DataClassJsonMixin.to_dict(copy_self)

    def save_to_dataset(self, dataset_dir: Path):
        """Save this sample to the dataset directory."""
        sample_dir = dataset_dir / self.uuid
        sample_dir.mkdir(parents=True, exist_ok=True)

        # Save sample.json
        sample_file = sample_dir / "sample.json"
        with open(sample_file, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

        short_edit_description_file = sample_dir / "short_edit_description.md"
        with open(short_edit_description_file, "w") as f:
            f.write(self.short_edit_description)

        filename = self.file_path.split("/")[-1]
        # Save original_file_content.txt
        original_file = sample_dir / f"a_original_{filename}"
        with open(original_file, "w") as f:
            f.write(self.original_file_content)

        # Save expected file content variants
        if self.expected_modified_file_content_variants:
            for i, variant in enumerate(self.expected_modified_file_content_variants):
                expected_file = sample_dir / f"b_expected_{i}_{filename}"
                with open(expected_file, "w") as f:
                    f.write(variant)
        else:
            # Create an empty expected file if no variants exist
            expected_file = sample_dir / f"b_expected_0_{filename}"
            if not expected_file.exists():
                with open(expected_file, "w") as f:
                    f.write(self.original_file_content)

    @classmethod
    def load_from_dir(cls, sample_dir: Path):
        """Load a sample from a directory."""
        sample_file = sample_dir / "sample.json"

        if sample_file.exists():
            # Load the base sample data from sample.json
            with open(sample_file) as f:
                sample_data = json.load(f)

            if sample_data:
                # Create instance from JSON
                sample = cls.from_dict(sample_data)
            else:
                sample = cls()
            sample.uuid = sample_dir.name
        else:
            raise ValueError(f"Failed to load sample from {sample_dir}")

        read_from_md_fields = ["short_edit_description"]
        for md_field in read_from_md_fields:
            md_file = sample_dir / f"{md_field}.md"
            if md_file.exists():
                with open(md_file) as f:
                    setattr(sample, md_field, f.read().strip())

        # Find and load the original file content
        original_files = list(sample_dir.glob("a_original_*"))
        assert len(original_files) == 1
        original_file = original_files[0]
        with open(original_file) as f:
            sample.original_file_content = f.read()
            sample.file_path = original_file.name[len("a_original_") :]
            if sample.file_path.endswith(".eval"):
                sample.file_path = sample.file_path[: -len(".eval")]

        # Find and load all expected file content variants
        expected_files = sorted(sample_dir.glob("b_expected_*"))
        sample.expected_modified_file_content_variants = []
        for expected_file in expected_files:
            with open(expected_file) as f:
                sample.expected_modified_file_content_variants.append(f.read())

        return sample

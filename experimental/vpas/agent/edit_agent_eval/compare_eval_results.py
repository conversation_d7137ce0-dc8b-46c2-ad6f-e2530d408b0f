"""Script to compare eval results generated by run_eval.py."""

import argparse
import json
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional

from experimental.vpas.agent.edit_agent_eval.run_eval import EditAgentEvalOutput


@dataclass
class EvalResultComparison:
    """Comparison of two eval results."""

    # Results from both runs
    results1: List[EditAgentEvalOutput]
    results2: List[EditAgentEvalOutput]

    # Stats for each run
    stats1: Dict[str, float]
    stats2: Dict[str, float]

    # Comparison stats
    common_samples: int
    only_in_1: int
    only_in_2: int
    both_correct: int
    both_incorrect: int
    only_1_correct: int
    only_2_correct: int


def compute_stats(results: List[EditAgentEvalOutput]) -> Dict[str, float]:
    """Compute statistics for a single eval run."""
    total = len(results)
    if total == 0:
        return {
            "total": 0,
            "correct": 0,
            "accuracy": 0.0,
        }

    correct = sum(1 for r in results if r.correct)
    return {
        "total": total,
        "correct": correct,
        "accuracy": correct / total,
    }


def compare_results(
    results1: List[EditAgentEvalOutput],
    results2: List[EditAgentEvalOutput],
) -> EvalResultComparison:
    """Compare two sets of eval results."""
    # Group results by sample UUID
    samples1 = {r.sample.uuid: r for r in results1}
    samples2 = {r.sample.uuid: r for r in results2}

    # Find common and unique samples
    common_uuids = set(samples1.keys()) & set(samples2.keys())
    only_in_1 = len(samples1) - len(common_uuids)
    only_in_2 = len(samples2) - len(common_uuids)

    # Compare correctness for common samples
    both_correct = 0
    both_incorrect = 0
    only_1_correct = 0
    only_2_correct = 0

    for uuid in common_uuids:
        r1 = samples1[uuid]
        r2 = samples2[uuid]
        if r1.correct and r2.correct:
            both_correct += 1
        elif not r1.correct and not r2.correct:
            both_incorrect += 1
        elif r1.correct:
            only_1_correct += 1
        else:
            only_2_correct += 1

    return EvalResultComparison(
        results1=results1,
        results2=results2,
        stats1=compute_stats(results1),
        stats2=compute_stats(results2),
        common_samples=len(common_uuids),
        only_in_1=only_in_1,
        only_in_2=only_in_2,
        both_correct=both_correct,
        both_incorrect=both_incorrect,
        only_1_correct=only_1_correct,
        only_2_correct=only_2_correct,
    )


def load_results(path: Path) -> List[EditAgentEvalOutput]:
    """Load eval results from a JSONL file."""
    results = []
    with open(path) as f:
        for line in f:
            data = json.loads(line)
            results.append(EditAgentEvalOutput.from_dict(data))
    return results


def main():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "results1",
        type=Path,
        help="First results file",
    )
    parser.add_argument(
        "results2",
        type=Path,
        help="Second results file",
    )
    args = parser.parse_args()

    results1 = load_results(args.results1)
    results2 = load_results(args.results2)
    comparison = compare_results(results1, results2)

    # Print comparison stats
    print(f"Results 1: {comparison.stats1}")
    print(f"Results 2: {comparison.stats2}")
    print()
    print(f"Common samples: {comparison.common_samples}")
    print(f"Only in results 1: {comparison.only_in_1}")
    print(f"Only in results 2: {comparison.only_in_2}")
    print()
    print(f"Both correct: {comparison.both_correct}")
    print(f"Both incorrect: {comparison.both_incorrect}")
    print(f"Only results 1 correct: {comparison.only_1_correct}")
    print(f"Only results 2 correct: {comparison.only_2_correct}")


if __name__ == "__main__":
    main()

import argparse
from pathlib import Path

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
)
from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample


def convert_chat_to_agent_eval_sample(
    chat_eval_sample: EvalSample,
) -> EditAgentEvalSample:
    assert chat_eval_sample.prompt_input

    short_edit_description = f"""
Edit the file according to this code block:
```
{chat_eval_sample.prompt_input.code_block}
```
"""

    return EditAgentEvalSample(
        uuid=chat_eval_sample.uuid,
        file_path=chat_eval_sample.prompt_input.target_path,
        short_edit_description=short_edit_description,
        original_file_content=chat_eval_sample.prompt_input.target_file_content,
        expected_modified_file_content_variants=chat_eval_sample.expected_outputs,
        category="chat_eval",
    )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--output_path", type=Path, required=True)
    args = parser.parse_args()

    print("Reading dogfood samples...")
    import experimental.yury.smart_paste_eval.data.dogfood as dogfood_eval_data

    print("Reading hard samples...")
    import experimental.yury.smart_paste_eval.data.hard as hard_eval_data

    chat_eval_samples = dogfood_eval_data.DATA + hard_eval_data.DATA

    chat_eval_samples = chat_eval_samples
    agent_eval_samples = [
        convert_chat_to_agent_eval_sample(chat_eval_sample)
        for chat_eval_sample in chat_eval_samples
    ]

    for agent_eval_sample in agent_eval_samples:
        agent_eval_sample.save_to_dataset(args.output_path)

    print(f"Saved {len(agent_eval_samples)} samples to {args.output_path}")


if __name__ == "__main__":
    main()

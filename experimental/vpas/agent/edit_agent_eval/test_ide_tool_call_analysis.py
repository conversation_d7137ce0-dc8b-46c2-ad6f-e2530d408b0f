import pytest
from ide_tool_call_analysis import get_next_request_ids, DOGFOOD_SHARD


def test_get_next_request_ids_real_data():
    """Test get_next_request_ids with real data from BigQuery."""
    # Given
    request_id = "db7d67af-6b7c-4a06-a8d1-dac46bac0983"
    expected_next_id = "e6876df3-90f9-45ab-b124-f906a6877ce9"

    # When
    result = get_next_request_ids([request_id], tenant_name=DOGFOOD_SHARD)
    print(f"\nResult: {result}")  # Debug print

    # Then
    assert len(result) == 1, f"Expected 1 result, got {len(result)}"
    assert request_id in result, f"Request ID {request_id} not found in results"
    assert (
        result[request_id] == expected_next_id
    ), f"Expected next ID {expected_next_id}, got {result[request_id]}"

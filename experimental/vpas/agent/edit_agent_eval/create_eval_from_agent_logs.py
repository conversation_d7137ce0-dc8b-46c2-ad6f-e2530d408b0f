"""<PERSON><PERSON><PERSON> to create eval dataset from agent logs."""

from collections import defaultdict
import random
import pickle
import json
import argparse
import glob
import re
import sys
from pathlib import Path
from typing import Any, List, Callable, Optional
from dataclasses import dataclass, field
import hashlib
from dataclasses_json import DataClassJsonMixin  # type: ignore

from research.agents.tools import LoggedToolCall
from experimental.vpas.utils.json_utils import jsonify

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
    LoggedToolCallWithMetadata,
)

EDIT_FILE_TOOL_NAMES = ["edit_file", "edit_file_v2"]
READ_FILE_TOOL_NAMES = ["read_file", "read_file_outline"]


@dataclass
class LoggedToolCallNode(DataClassJsonMixin):
    tool_call: LoggedToolCallWithMetadata
    children: List["LoggedToolCallNode"] = field(default_factory=list)
    interrupted: bool = False
    parent: Optional["LoggedToolCallNode"] = field(default=None, compare=False)

    @property
    def prev_sibling(self) -> Optional["LoggedToolCallNode"]:
        """Returns the previous sibling of this node, if any."""
        if not self.parent:
            return None
        idx = self.parent.children.index(self)
        return self.parent.children[idx - 1] if idx > 0 else None

    @property
    def next_sibling(self) -> Optional["LoggedToolCallNode"]:
        """Returns the next sibling of this node, if any."""
        if not self.parent:
            return None
        idx = self.parent.children.index(self)
        return (
            self.parent.children[idx + 1]
            if idx < len(self.parent.children) - 1
            else None
        )


def load_pickle_file(file_path: str | Path) -> Any:
    with open(file_path, "rb") as f:
        return pickle.load(f)


def extract_tool_calls(
    data: Any, file_path: str | Path
) -> List[LoggedToolCallWithMetadata]:
    """Extract information about all tool calls from the loaded data."""
    if isinstance(file_path, Path):
        file_path = str(file_path)
    tool_calls = []
    if isinstance(data, list):
        for idx, item in enumerate(data):
            if isinstance(item, LoggedToolCall):
                # Convert LoggedToolCall to LoggedToolCallWithMetadata
                metadata_call = LoggedToolCallWithMetadata(
                    started=item.started if hasattr(item, "started") else None,
                    tool=item.tool if hasattr(item, "tool") else None,
                    tool_input=item.tool_input if hasattr(item, "tool_input") else None,
                    tool_output=item.tool_output
                    if hasattr(item, "tool_output")
                    else None,
                    tool_message=item.tool_message
                    if hasattr(item, "tool_message")
                    else None,
                    auxiliary_data=item.auxiliary_data
                    if hasattr(item, "auxiliary_data")
                    else None,
                    timestamp=item.timestamp if hasattr(item, "timestamp") else None,
                    log_file_path=file_path,
                    log_file_index=idx,
                )
                tool_calls.append(metadata_call)
    return tool_calls


def extract_edit_agent_eval_samples_from_list(
    tool_calls: List[LoggedToolCallWithMetadata],
    filter_fn: Callable[[LoggedToolCallNode, List[LoggedToolCall]], str],
) -> List[EditAgentEvalSample]:
    # First convert the flat list into a list of trees
    root_nodes = convert_to_list_of_trees(tool_calls)

    # Then extract samples from the tree structures
    samples = []
    for root_node in root_nodes:
        samples.extend(extract_edit_agent_eval_samples(root_node, filter_fn))
    return samples


def extract_edit_agent_eval_samples(
    tool_call_node: LoggedToolCallNode,
    filter_fn: Callable[[LoggedToolCallNode, List[LoggedToolCall]], str],
) -> List[EditAgentEvalSample]:
    samples = []

    def process_node(node: LoggedToolCallNode):
        # Check if this is an edit agent call
        if node.tool_call.tool.name == "edit_file_agent":
            if node.interrupted:
                return

            if (
                "edit_summary" not in node.tool_call.tool_input
                or "detailed_edit_description" not in node.tool_call.tool_input
            ):
                return

            # Handle older logs that use edit_summary and detailed_edit_description
            tool_input = node.tool_call.tool_input
            if (
                "short_edit_description" not in tool_input
                and "edit_summary" in tool_input
            ):
                tool_input["short_edit_description"] = (
                    f"{tool_input.get('edit_summary', '')}\n\n"
                    f"{tool_input.get('detailed_edit_description', '')}"
                ).strip()

            # Find all edit_file calls that are direct children of this edit_agent call
            # Only include non-interrupted edit_file calls
            edit_file_calls = []

            # First collect all edit_file calls that have both start and end calls
            for child in node.children:
                if (
                    child.tool_call.tool.name in EDIT_FILE_TOOL_NAMES
                    and not child.interrupted
                ):
                    edit_file_calls.append(child.tool_call)

            # Apply the filter function to determine the match category
            match_category = filter_fn(node, edit_file_calls)
            # match_category = "empty_edit"
            if match_category:  # If category is not empty/None
                try:
                    sample = EditAgentEvalSample(
                        edit_agent_call=node.tool_call,
                        edit_file_calls=edit_file_calls,
                        category=match_category,
                    )
                    sample._set_is_empty_edit()
                    sample._extract_original_file_content()
                    samples.append(sample)
                except Exception as e:
                    import traceback

                    print(f"Failed to create sample from edit agent call: {e}")
                    traceback.print_exc()

        # Recursively process children
        for child in node.children:
            process_node(child)

    process_node(tool_call_node)
    return samples


def get_node_key(call: LoggedToolCall) -> str:
    """Generate a unique key for a tool call based on its name and input."""
    key = call.tool.name
    if call.tool_input:
        # Sort input keys to ensure consistent order
        sorted_inputs = sorted(call.tool_input.items())
        # Create a hash of the inputs to make the key shorter
        inputs_str = "_".join(f"{k}={v}" for k, v in sorted_inputs)
        hasher = hashlib.sha256()
        hasher.update(inputs_str.encode())
        key += "_" + hasher.hexdigest()[:8]
    return key


def convert_to_list_of_trees(
    tool_calls: List[LoggedToolCallWithMetadata],
) -> List[LoggedToolCallNode]:
    """Convert a flat list of tool calls into a list of trees.

    Each tree represents a sequence of nested tool calls. The root of each tree is a
    tool call that has no parent.

    Args:
        tool_calls: A list of tool calls in chronological order.

    Returns:
        A list of root nodes, each representing a tree of tool calls.
    """
    if not tool_calls:
        return []

    # Stack to keep track of currently open calls
    stack = []
    # List to store root nodes
    roots = []

    for i, call in enumerate(tool_calls):
        if call.started:
            # Create a new node for this start call
            node = LoggedToolCallNode(tool_call=call)

            # If there's a parent call on the stack, add this as its child
            if stack:
                stack[-1].children.append(node)
                node.parent = stack[-1]
            else:
                # No parent means this is a root node
                roots.append(node)

            # Push this call onto the stack
            stack.append(node)
        else:
            # This is an end call
            if not stack:
                continue

            # Pop calls from the stack until we find a matching one
            while stack:
                current = stack[-1]
                if (
                    current.tool_call.tool.name == call.tool.name
                    and current.tool_call.tool_input == call.tool_input
                ):
                    # Found matching start call
                    # Update the existing node with the end call's data
                    current.tool_call = call
                    stack.pop()
                    break
                else:
                    # End call doesn't match current start call - mark as interrupted and pop
                    current.interrupted = True
                    if stack[:-1]:  # Has parent
                        parent = stack[-2]
                        parent.interrupted = True
                    stack.pop()

    # Mark any remaining calls in the stack as interrupted
    while stack:
        node = stack.pop()
        node.interrupted = True

    return roots


def parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "logs_globs",
        nargs="*",
        help="Glob patterns for log pickle files (e.g., /dir/*/*.pickle). If not specified, uses latest log for current user",
    )
    parser.add_argument(
        "--json-only",
        action="store_true",
        help="Only convert pickle files to JSON and skip dataset creation",
    )
    parser.add_argument(
        "--output-dir",
        "-o",
        type=Path,
        help="Output directory for the dataset (default: ./dataset)",
        default=Path(__file__).parent / "dataset",
    )
    parser.add_argument(
        "--filter",
        action="store_true",
        help="Enable filtering",
    )
    parser.add_argument(
        "--stats-only",
        action="store_true",
        help="Only calculate statistics during filtering without creating samples",
    )
    parser.add_argument(
        "--limit-samples",
        type=int,
        default=None,
        help="Maximum number of samples to extract. If more samples are found, a random subset will be selected",
    )
    return parser.parse_args()


def get_pickle_files(logs_globs: List[str]) -> List[Path]:
    pickle_files = []
    if logs_globs:
        for pattern in logs_globs:
            matched_files = glob.glob(pattern, recursive=True)
            if not matched_files:
                print(f"Warning: No files found matching pattern: {pattern}")
            pickle_files.extend(matched_files)
    else:
        from tools.user_info import get_build_user

        user = get_build_user()
        logs_dir = Path(f"/mnt/efs/augment/user/guy/agent_logs/{user}")
        if not logs_dir.exists():
            print(f"Error: Log directory not found: {logs_dir}")
            sys.exit(1)

        pickle_files = list(logs_dir.glob("*.pickle"))
        if not pickle_files:
            print(f"Error: No pickle files found in {logs_dir}")
            sys.exit(1)

        latest_pickle = max(pickle_files, key=lambda x: x.stat().st_mtime)
        pickle_files = [latest_pickle]
        print(f"Using latest log file: {latest_pickle}")

    if not pickle_files:
        print("Error: No files found matching any of the provided patterns")
        sys.exit(1)

    return pickle_files


def has_followup_edit(edit_agent_call_node: LoggedToolCallNode) -> bool:
    """Check if the edit agent call has a follow-up edit on the same file."""
    # Check if the next two siblings are read_file and edit_file calls
    next_sibling = edit_agent_call_node.next_sibling
    if next_sibling and next_sibling.tool_call.tool.name in READ_FILE_TOOL_NAMES:
        next_next_sibling = next_sibling.next_sibling
        if (
            next_next_sibling
            and next_next_sibling.tool_call.tool.name == "edit_file_agent"
        ):
            # Get file path from original edit
            original_file_path = edit_agent_call_node.tool_call.tool_input.get(
                "file_path"
            )
            read_file_path = next_sibling.tool_call.tool_input.get("file_path")
            # Get file path from follow-up edit
            followup_file_path = next_next_sibling.tool_call.tool_input.get("file_path")

            # Check if both paths exist and are the same
            if original_file_path and read_file_path and followup_file_path:
                return original_file_path == read_file_path == followup_file_path
    return False


def remove_codeblocks(text: str) -> str:
    """Remove codeblocks from the text.

    Handles both complete codeblocks (```...```) and unclosed codeblocks (text after ```)
    """
    # First remove complete codeblocks
    text = re.sub(r"```[\s\S]*?```", "", text)

    # Then remove any remaining text after an unclosed codeblock
    if "```" in text:
        text = text.split("```")[0]

    return text


def create_filter_function(
    enable_filter: bool, stats_only: bool
) -> tuple[Callable, dict]:
    # Hardcoded filter parameters
    MIN_EDIT_CALLS = 2
    KEYWORDS = ["remove", "delete", "move"]

    # Stats dictionary to track matches
    filter_stats = {
        "total_samples": 0,
        "matched_any_condition": 0,
        "num_edits_histogram": defaultdict(int),
        "min_edits_matches": 0,
        "keyword_matches": 0,
        "followup_edit_matches": 0,
        "keyword_breakdown": {keyword: 0 for keyword in KEYWORDS},
    }

    def filter_fn(
        edit_agent_call_node: LoggedToolCallNode, edit_file_calls: List[LoggedToolCall]
    ) -> str:
        if not enable_filter:
            return "unfiltered"

        filter_stats["total_samples"] += 1

        if not edit_file_calls:
            return ""

        num_edits = len(edit_file_calls)
        filter_stats["num_edits_histogram"][num_edits] += 1

        has_min_edits = num_edits >= MIN_EDIT_CALLS
        if has_min_edits:
            filter_stats["min_edits_matches"] += 1

        edit_description = edit_agent_call_node.tool_call.tool_input[
            "short_edit_description"
        ].lower()
        edit_description = remove_codeblocks(edit_description)

        # Track individual keyword matches
        matching_keywords = sorted(
            [
                keyword
                for keyword in KEYWORDS
                if re.search(rf"\b{keyword}\b", edit_description)
            ]
        )
        has_keywords = bool(matching_keywords)
        if has_keywords:
            filter_stats["keyword_matches"] += 1
            # Update keyword-specific stats
            for keyword in matching_keywords:
                filter_stats["keyword_breakdown"][keyword] += 1

        has_followup = has_followup_edit(edit_agent_call_node)
        if has_followup:
            filter_stats["followup_edit_matches"] += 1

        # Determine match category
        if stats_only:
            return ""  # Don't create samples, just update stats

        # Return the most specific match category
        if has_keywords:
            filter_stats["matched_any_condition"] += 1
            return f"keyword_match:{','.join(matching_keywords)}"
        elif has_min_edits:
            filter_stats["matched_any_condition"] += 1
            return f"multi_edit:{num_edits}"
        elif has_followup:
            filter_stats["matched_any_condition"] += 1
            return "followup_edit"

        return ""  # No match

    return filter_fn, filter_stats


def process_pickle_file(
    file_path: Path, filter_fn, json_only: bool
) -> List[EditAgentEvalSample]:
    print(f"Processing {file_path}")
    try:
        data = load_pickle_file(file_path)
        if json_only:
            json_path = Path(file_path).with_suffix(".json")
            print(f"Converting {file_path} to {json_path}")
            try:
                with open(json_path, "w") as f:
                    json.dump(jsonify(data), f, indent=2)
                print(f"Successfully converted to {json_path}")
            except Exception as e:
                print(f"Failed to convert {file_path}: {e}")
            return []

        tool_calls = extract_tool_calls(data=data, file_path=file_path)
        print(f"Found {len(tool_calls)} tool calls in {file_path}")

        file_samples = extract_edit_agent_eval_samples_from_list(
            tool_calls, filter_fn=filter_fn
        )
        print(f"Extracted {len(file_samples)} edit agent eval samples from {file_path}")
        return file_samples

    except Exception as e:
        import traceback

        traceback.print_exc()
        print(f"Error processing {file_path}: {e}")
        return []


def save_samples(samples: List[EditAgentEvalSample], dataset_dir: Path):
    for i, sample in enumerate(samples):
        try:
            sample.save_to_dataset(dataset_dir)
        except Exception as e:
            import traceback

            print(f"Failed to save sample {sample.uuid}: {e}")
            if sample.edit_agent_call:
                print(f"Sample log file path: {sample.edit_agent_call.log_file_path}")
            traceback.print_exc()
            continue
        print(f"Saved sample {i+1}/{len(samples)}: {sample.uuid}")


def main():
    args = parse_arguments()

    dataset_dir = None
    if not args.stats_only:
        dataset_dir = args.output_dir
        dataset_dir.mkdir(exist_ok=True, parents=True)
        print(f"Creating dataset in: {dataset_dir}")

    if args.filter:
        print("Filtering enabled")
    if args.stats_only:
        print("Stats only mode - no samples will be created")

    pickle_files = get_pickle_files(args.logs_globs)
    print(f"Loading tool calls from {len(pickle_files)} pickle files...")
    files_str = "\n".join(str(f) for f in pickle_files)
    print(f"Files:\n{files_str[:1000]}\n...")

    filter_fn, filter_stats = create_filter_function(
        enable_filter=args.filter, stats_only=args.stats_only
    )
    all_samples = []
    for file_path in pickle_files:
        samples = process_pickle_file(file_path, filter_fn, args.json_only)
        all_samples.extend(samples)

    if args.json_only:
        return

    print(f"\nTotal samples extracted across all files: {len(all_samples)}")

    if args.limit_samples is not None and len(all_samples) > args.limit_samples:
        random.seed(74912)
        all_samples = random.sample(all_samples, args.limit_samples)
        print(f"Randomly selected {args.limit_samples} samples")

    if not args.stats_only and dataset_dir is not None:
        save_samples(all_samples, dataset_dir)

    # Print filter statistics if filtering was enabled
    if args.filter:
        total = filter_stats["total_samples"]
        print("\nFilter Statistics:")
        print(f"Total samples processed: {total}")
        print(
            f"Samples passing any condition: {filter_stats['matched_any_condition']} ({filter_stats['matched_any_condition']/total:.1%})"
        )
        print("\nNumber of edits distribution:")
        for num_edits, count in sorted(filter_stats["num_edits_histogram"].items()):
            print(f"  {num_edits} edits: {count} ({count/total:.1%})")
        print(
            f"Samples with keywords: {filter_stats['keyword_matches']} ({filter_stats['keyword_matches']/total:.1%})"
        )
        print(
            f"Samples with followup edits: {filter_stats['followup_edit_matches']} ({filter_stats['followup_edit_matches']/total:.1%})"
        )
        print(
            f"Samples with at least 2 edits: {filter_stats['min_edits_matches']} ({filter_stats['min_edits_matches']/total:.1%})"
        )
        print("\nKeyword breakdown:")
        for keyword, count in filter_stats["keyword_breakdown"].items():
            print(f"  {keyword}: {count} ({count/total:.1%})")


if __name__ == "__main__":
    main()

"""Tests for run_eval.py."""

import pytest
from pathlib import Path
from unittest.mock import MagicMock, patch
from datetime import datetime

from research.agents.tools import (
    ToolCallLogger,
    ToolParam,
    DialogMessages,
    ToolCall,
)
from research.llm_apis.llm_client import LL<PERSON>lient, TextResult
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.file_edit.udiff_edit_file_agent import (
    EditFileWithUDiffAgent,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
    LoggedToolCallWithMetadata,
)
from experimental.vpas.agent.edit_agent_eval.run_eval import (
    EditAgentEvalOutput,
    compute_diffs,
    run_eval_on_sample,
)
from experimental.vpas.utils.json_utils import setup_dataclasses_json


# Call setup_dataclasses_json to handle datetime serialization
setup_dataclasses_json()


def test_compute_diffs():
    original = "line1\nline2\nline3\n"
    modified = "line1\nmodified\nline3\n"
    expected = "line1\nexpected\nline3\n"

    diff_original, diff_expected = compute_diffs(original, modified, expected)

    assert "line2" in diff_original
    assert "modified" in diff_original
    assert "modified" in diff_expected
    assert "expected" in diff_expected


@pytest.fixture
def mock_llm_client():
    client = MagicMock(spec=LLMClient)
    client.generate.return_value = (
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="apply_udiff",
                tool_input={
                    "file_path": "test.py",
                    "udiff": """--- test.py
+++ test.py
@@ -1,2 +1,2 @@
 def test():
-    return 1
+    return 2
""",
                },
            )
        ],
        {},
    )
    return client


@pytest.fixture
def mock_workspace(tmp_path):
    workspace = WorkspaceManagerImpl(
        augment_client=MagicMock(),
        root=tmp_path,
    )
    return workspace


@pytest.fixture
def edit_agent(mock_llm_client, mock_workspace):
    # Create a real edit agent with mock client
    tool_call_logger = ToolCallLogger()
    return EditFileWithUDiffAgent(
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        workspace_manager=mock_workspace,
        max_output_tokens_per_turn=1000,
        max_turns=3,
        review_stage=False,  # Disable review stage for testing
    )


def create_mock_sample() -> EditAgentEvalSample:
    """Create a mock EditAgentEvalSample."""
    tool = ToolParam(
        name="edit_file_agent",
        description="Edit a file",
        input_schema={
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "short_edit_description": {"type": "string"},
            },
            "required": ["file_path", "short_edit_description"],
        },
    )

    edit_agent_call = LoggedToolCallWithMetadata(
        started=False,
        tool=tool,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Change return 1 to return 2",
        },
        tool_output="Edit completed successfully",
        tool_message="Editing file test.py",
        auxiliary_data={},
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=0,
    )

    edit_file_call = LoggedToolCallWithMetadata(
        started=False,
        tool=tool,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Change return 1 to return 2",
        },
        tool_output="Edit completed (request_id=123e4567-e89b-12d3-a456-426614174000)",
        tool_message="Editing file test.py",
        auxiliary_data={},
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=1,
    )

    sample = EditAgentEvalSample(
        edit_agent_call=edit_agent_call,
        edit_file_calls=[edit_file_call],
    )
    sample.original_file_content = "def test():\n    return 1\n"
    sample.expected_modified_file_content_variants = ["def test():\n    return 2\n"]
    return sample


def test_run_eval_on_sample(edit_agent, mock_workspace):
    tool_call_logger = ToolCallLogger()

    # Create sample and run eval
    sample = create_mock_sample()

    # Write original content to file
    file_path = Path(sample.file_path)
    abs_path = mock_workspace.root / file_path
    abs_path.write_text(sample.original_file_content)

    # Create dialog messages with tool call
    dialog_messages = DialogMessages()
    dialog_messages.add_user_prompt("Edit file test.py")
    dialog_messages.add_model_response(
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="edit_file_agent",
                tool_input={
                    "file_path": "test.py",
                    "short_edit_description": "Change return 1 to return 2",
                },
            )
        ]
    )

    # Run eval
    result = run_eval_on_sample(
        sample=sample,
        file_edit_agent=edit_agent,
        workspace_manager=mock_workspace,
        tool_call_logger=tool_call_logger,
        dialog_messages=dialog_messages,
    )

    # Check result
    assert isinstance(result, EditAgentEvalOutput)
    assert result.sample == sample
    assert result.correct is True
    assert "return 1" in result.diff_against_original
    assert "return 2" in result.diff_against_original
    assert result.diff_against_expected == ""  # No diff since content matches


def test_run_eval_on_sample_incorrect(edit_agent, mock_workspace, mock_llm_client):
    tool_call_logger = ToolCallLogger()

    # Mock LLM to return wrong content
    mock_llm_client.generate.return_value = (
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="apply_udiff",
                tool_input={
                    "file_path": "test.py",
                    "udiff": """--- test.py
+++ test.py
@@ -1,2 +1,2 @@
 def test():
-    return 1
+    return 3
""",
                },
            )
        ],
        {},
    )

    # Create sample and run eval
    sample = create_mock_sample()

    # Write original content to file
    file_path = Path(sample.file_path)
    abs_path = mock_workspace.root / file_path
    abs_path.write_text(sample.original_file_content)

    # Create dialog messages with tool call
    dialog_messages = DialogMessages()
    dialog_messages.add_user_prompt("Edit file test.py")
    dialog_messages.add_model_response(
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="edit_file_agent",
                tool_input={
                    "file_path": "test.py",
                    "short_edit_description": "Change return 1 to return 2",
                },
            )
        ]
    )

    # Run eval
    result = run_eval_on_sample(
        sample=sample,
        file_edit_agent=edit_agent,
        workspace_manager=mock_workspace,
        tool_call_logger=tool_call_logger,
        dialog_messages=dialog_messages,
    )

    # Check result
    assert isinstance(result, EditAgentEvalOutput)
    assert result.sample == sample
    assert result.correct is False
    assert "return 1" in result.diff_against_original
    assert "return 3" in result.diff_against_original
    assert "return 2" in result.diff_against_expected
    assert "return 3" in result.diff_against_expected


def test_run_eval_on_sample_multiple_variants(
    edit_agent, mock_workspace, mock_llm_client
):
    tool_call_logger = ToolCallLogger()

    # Mock LLM to return content matching second variant
    mock_llm_client.generate.return_value = (
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="apply_udiff",
                tool_input={
                    "file_path": "test.py",
                    "udiff": """--- test.py
+++ test.py
@@ -1,2 +1,2 @@
 def test():
-    return 1
+    return 3
""",
                },
            )
        ],
        {},
    )
    # Create sample with multiple variants
    sample = create_mock_sample()
    sample.expected_modified_file_content_variants = [
        "def test():\n    return 2\n",
        "def test():\n    return 3\n",  # This one matches
    ]

    # Write original content to file
    file_path = Path(sample.file_path)
    abs_path = mock_workspace.root / file_path
    abs_path.write_text(sample.original_file_content)

    # Create dialog messages with tool call
    dialog_messages = DialogMessages()
    dialog_messages.add_user_prompt("Edit file test.py")
    dialog_messages.add_model_response(
        [
            ToolCall(
                tool_call_id="test_id",
                tool_name="edit_file_agent",
                tool_input={
                    "file_path": "test.py",
                    "short_edit_description": "Change return 1 to return 2",
                },
            )
        ]
    )

    # Run eval
    result = run_eval_on_sample(
        sample=sample,
        file_edit_agent=edit_agent,
        workspace_manager=mock_workspace,
        tool_call_logger=tool_call_logger,
        dialog_messages=dialog_messages,
    )

    # Check result
    assert isinstance(result, EditAgentEvalOutput)
    assert result.sample == sample
    assert result.correct is True  # Should be True since it matches second variant
    assert "return 1" in result.diff_against_original
    assert "return 3" in result.diff_against_original
    assert "return 2" in result.diff_against_expected  # Diff against first variant

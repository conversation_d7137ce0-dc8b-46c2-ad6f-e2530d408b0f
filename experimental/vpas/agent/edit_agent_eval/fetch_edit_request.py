from base.datasets.gcs_client import GCSRequestInsightFetcher
import argparse


def get_edit_request_info(edit_request_id: str, request_event_name: str):
    fetcher = GCSRequestInsightFetcher.from_tenant_id(
        project="system-services-dev",
        tenant_id="e7fa6a426cbdb3d9da946d29db5fe73e",
        bucket_name="dev-guy-request-insight-events",
        max_pool_connections=100,
    )
    result = fetcher.get_request(
        request_id=edit_request_id,
        request_event_names=frozenset({request_event_name}),
    )
    for event in result.events:  # type: ignore
        if event.HasField(request_event_name):  # type: ignore
            full_request = getattr(event, request_event_name)
            return full_request
    raise AssertionError(f"No {request_event_name} found in events")


def get_edit_response(edit_request_id: str):
    return get_edit_request_info(edit_request_id, "instruction_host_response").response


def get_edit_request(edit_request_id: str):
    return get_edit_request_info(edit_request_id, "instruction_host_request").request


def main():
    parser = argparse.ArgumentParser(description="Fetch edit request details")
    parser.add_argument(
        "--request-id",
        type=str,
        required=True,
        help="UUID of the edit request to fetch",
    )
    args = parser.parse_args()

    request = get_edit_request(args.request_id)
    print(request)


if __name__ == "__main__":
    main()

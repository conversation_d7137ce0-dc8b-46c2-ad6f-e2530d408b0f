from typing import Iterator
from more_itertools import flatten
from research.llm_apis.llm_client import GeneralContentBlock, TextPrompt, TextResult
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON>ounter


def calc_theoretical_latency(num_requests, num_input_tokens, num_output_tokens):
    """Calculate the theoretical latency for a given number of requests, input tokens, and output tokens."""
    requests_per_sec = 2
    input_tokens_per_sec = 1000
    output_tokens_per_sec = 60

    request_latency = num_requests / requests_per_sec
    input_latency = num_input_tokens / input_tokens_per_sec
    output_latency = num_output_tokens / output_tokens_per_sec

    return request_latency + input_latency + output_latency


def count_tokens(messages: Iterator[GeneralContentBlock]):
    messages_concat = []
    for message in messages:
        if isinstance(message, TextResult) or isinstance(message, TextPrompt):
            messages_concat.append(message.text)
        else:
            messages_concat.append(message.to_json())

    messages_concat = "".join(messages_concat)
    token_counter = ClaudeTokenCounter()
    return token_counter.count_tokens(messages_concat)


def calc_dialog_stats(messages: list[list[GeneralContentBlock]]):
    num_requests = len(messages) // 2
    user_messages = flatten([msgs for idx, msgs in enumerate(messages) if idx % 2 == 0])
    assistant_messages = flatten(
        [msgs for idx, msgs in enumerate(messages) if idx % 2 == 1]
    )
    num_input_tokens = count_tokens(user_messages)
    num_output_tokens = count_tokens(assistant_messages)

    return {
        "num_requests": num_requests,
        "num_input_tokens": num_input_tokens,
        "num_output_tokens": num_output_tokens,
        "theoretical_latency": calc_theoretical_latency(
            num_requests, num_input_tokens, num_output_tokens
        ),
    }

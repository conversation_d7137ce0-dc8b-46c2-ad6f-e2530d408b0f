import pytest
from pathlib import Path
from unittest.mock import MagicMock, patch
from datetime import datetime

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
    LoggedToolCallWithMetadata,
)
from research.agents.tools import ToolParam
from experimental.vpas.utils.json_utils import setup_dataclasses_json

# Setup dataclasses_json to handle datetime serialization
setup_dataclasses_json()


@pytest.fixture
def mock_edit_request():
    with patch(
        "experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample.get_edit_request"
    ) as mock:
        mock.return_value = MagicMock(target_file_content="original file content")
        yield mock


@pytest.fixture
def sample_tool_param():
    return ToolParam(
        name="edit_file_agent",
        description="Edit a file",
        input_schema={
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "short_edit_description": {"type": "string"},
            },
        },
    )


@pytest.fixture
def sample_edit_agent_call(sample_tool_param):
    # Use a fixed datetime for reproducible tests
    fixed_time = datetime(2025, 2, 2, 3, 18, 23)
    return LoggedToolCallWithMetadata(
        started=False,
        tool=sample_tool_param,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Add a print statement",
        },
        tool_output="Edit completed successfully",
        tool_message="Edit completed successfully",
        timestamp=fixed_time,
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=0,
    )


@pytest.fixture
def sample_edit_file_call(sample_tool_param):
    # Use a fixed datetime for reproducible tests
    fixed_time = datetime(2025, 2, 2, 3, 18, 24)
    return LoggedToolCallWithMetadata(
        started=False,
        tool=sample_tool_param,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Add a print statement",
        },
        tool_output="Edit completed (request_id=123e4567-e89b-12d3-a456-426614174000)",
        tool_message="Edit completed",
        timestamp=fixed_time,
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=1,
    )


@pytest.fixture
def sample(sample_edit_agent_call, sample_edit_file_call, mock_edit_request):
    return EditAgentEvalSample(
        edit_agent_call=sample_edit_agent_call,
        edit_file_calls=[sample_edit_file_call],
    )


def test_save_to_dataset(tmp_path: Path, sample: EditAgentEvalSample):
    # Add an expected variant
    sample.expected_modified_file_content_variants = ["modified content"]

    # Save the sample
    sample.save_to_dataset(tmp_path)

    # Check that all expected files were created
    sample_dir = tmp_path / "testuser" / sample.uuid
    assert sample_dir.exists()

    # Check sample.json
    sample_json = sample_dir / "sample.json"
    assert sample_json.exists()

    # Check edit_description.md
    edit_desc = sample_dir / "edit_description.md"
    assert edit_desc.exists()
    content = edit_desc.read_text()
    assert "test.py" in content
    assert "Add a print statement" in content

    # Check original file
    original_file = sample_dir / "a_original_test.py.eval"
    assert original_file.exists()
    assert original_file.read_text() == "original file content"

    # Check expected file
    expected_file = sample_dir / "b_expected_0_test.py.eval"
    assert expected_file.exists()
    assert expected_file.read_text() == "modified content"


def test_save_to_dataset_no_variants(tmp_path: Path, sample: EditAgentEvalSample):
    # Save the sample without any variants
    sample.save_to_dataset(tmp_path)

    # Check that the expected file contains the original content
    sample_dir = tmp_path / "testuser" / sample.uuid
    expected_file = sample_dir / "b_expected_0_test.py.eval"
    assert expected_file.exists()
    assert expected_file.read_text() == "original file content"


def test_load_from_dir(tmp_path: Path, sample: EditAgentEvalSample):
    # First save the sample
    sample.expected_modified_file_content_variants = ["modified content"]
    sample.save_to_dataset(tmp_path)

    # Load the sample back
    sample_dir = tmp_path / "testuser" / sample.uuid
    loaded_sample = EditAgentEvalSample.load_from_dir(sample_dir)

    # Check that all fields match
    assert loaded_sample.uuid == sample.uuid
    assert loaded_sample.file_path == sample.file_path
    assert loaded_sample.short_edit_description == sample.short_edit_description
    assert loaded_sample.failed_after_max_retries == sample.failed_after_max_retries
    assert loaded_sample.edit_request_id == sample.edit_request_id
    assert loaded_sample.original_file_content == "original file content"
    assert loaded_sample.expected_modified_file_content_variants == ["modified content"]


def test_load_from_dir_multiple_variants(tmp_path: Path, sample: EditAgentEvalSample):
    # Save sample with multiple variants
    sample.expected_modified_file_content_variants = ["variant 1", "variant 2"]
    sample.save_to_dataset(tmp_path)

    # Load the sample back
    sample_dir = tmp_path / "testuser" / sample.uuid
    loaded_sample = EditAgentEvalSample.load_from_dir(sample_dir)

    # Check variants
    assert len(loaded_sample.expected_modified_file_content_variants) == 2
    assert loaded_sample.expected_modified_file_content_variants[0] == "variant 1"
    assert loaded_sample.expected_modified_file_content_variants[1] == "variant 2"

"""<PERSON>ript to generate a Python file with specific print patterns.

Example usage:

python generate_hello_world_prints.py output_file.py

This generates a Python file with 10 "Hello World" prints interspersed with 50 "Something different" prints.
"""

import argparse
from pathlib import Path


def main():
    parser = argparse.ArgumentParser(
        description="Generate a Python file with specific print patterns"
    )
    parser.add_argument("output_file", type=Path, help="Path to the output Python file")
    args = parser.parse_args()
    output_file: Path = args.output_file

    # Create parent directories if they don't exist
    output_file.parent.mkdir(parents=True, exist_ok=True)

    with output_file.open(mode="w", encoding="utf-8") as f:
        print(f"Writing to {f.name}")

        # Write a simple header
        f.write('"""Generated file with print patterns."""\n\n')

        # Generate the print statements with the specified pattern
        for i in range(10):
            # Write "Hello Wolld {index}"
            f.write(f'print("Hello Wolld {i}")\n')

            # Write 50 "Something different {index}" prints after each Hello World
            # (except after the last Hello World)
            if i < 9:
                for j in range(50):
                    f.write(f'print("Something different {i}_{j}")\n')

        # Add a main block at the end
        f.write('\nif __name__ == "__main__":\n')
        f.write('    print("Script completed")\n')


if __name__ == "__main__":
    main()

#!/usr/bin/env python3

import argparse
import glob
import logging
import os
import shutil
from pathlib import Path


def setup_logging():
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )


def find_sample_dirs(uuid: str) -> list[Path]:
    """Find all directories containing the given UUID."""
    base_path = Path("/mnt/efs/augment/user/vpas/edit_agent_eval/datasets")
    pattern = f"**/{uuid}"

    sample_dirs = list(base_path.glob(pattern))
    logging.info(f"Found {len(sample_dirs)} directories matching UUID {uuid}")
    for dir_path in sample_dirs:
        logging.info(f"Found: {dir_path}")

    return sample_dirs


def copy_to_manual_dataset(source_dirs: list[Path], uuid: str):
    """Copy the sample directories to the manual dataset location."""
    target_base = Path("/mnt/efs/augment/user/vpas/edit_agent_eval/datasets/manual")
    target_dir = target_base / uuid

    if not target_base.exists():
        target_base.mkdir(parents=True, exist_ok=True)
        logging.info(f"Created manual dataset directory: {target_base}")

    for source_dir in source_dirs:
        if target_dir.exists():
            logging.warning(f"Target directory already exists: {target_dir}")
            continue

        shutil.copytree(source_dir, target_dir)
        logging.info(f"Copied {source_dir} to {target_dir}")


def main():
    parser = argparse.ArgumentParser(
        description="Copy edit agent eval sample to manual dataset"
    )
    parser.add_argument("uuid", help="UUID of the sample to copy")
    args = parser.parse_args()

    setup_logging()

    sample_dirs = find_sample_dirs(args.uuid)
    if not sample_dirs:
        logging.error(f"No directories found matching UUID: {args.uuid}")
        return 1

    copy_to_manual_dataset(sample_dirs, args.uuid)
    return 0


if __name__ == "__main__":
    exit(main())

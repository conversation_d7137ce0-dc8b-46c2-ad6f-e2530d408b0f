"""Common utilities for HTML report generation."""

from pathlib import Path
import html
import re
from pygments import highlight
from pygments.formatters import HtmlFormatter
from pygments.lexers import (
    get_lexer_by_name,
    guess_lexer,
    <PERSON><PERSON><PERSON><PERSON>,
    Javas<PERSON><PERSON><PERSON><PERSON>,
    TextLexer,
)
from pygments.util import ClassNotFound


from markdown2 import markdown as _markdown2
from markdown import markdown as _markdown1
from copy import deepcopy


def markdown2(text):
    return _markdown2(
        text, extras=["fenced-code-blocks", "code-friendly"], safe_mode="escape"
    )


def markdown1(text):
    return _markdown1(text, extensions=["fenced_code", "codehilite"])


# Common CSS styles used across reports
COMMON_CSS = """
body {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
.container {
    margin: 0 auto;
    padding: 20px;
}
.section {
    margin: 20px 0;
    padding: 15px;
    background-color: #2d2d2d;
    border-radius: 5px;
}
.section h2 {
    margin-top: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    user-select: none;
}
.section h2:hover {
    opacity: 0.8;
}
.section h2::before {
    content: "▶";
    display: inline-block;
    margin-right: 8px;
    font-size: 0.8em;
    transition: transform 0.2s;
}
.section.expanded h2::before {
    transform: rotate(90deg);
}
.section-content {
    display: none;
    margin-top: 10px;
    opacity: 0;
    transition: opacity 0.3s;
}
.section.expanded .section-content {
    display: block;
    opacity: 1;
}
.status {
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
    font-weight: bold;
}
.status-correct {
    background-color: #2d4a3e;
    color: #73c991;
}
.status-incorrect {
    background-color: #4a2d2d;
    color: #c97373;
}
pre {
    margin: 0;
    padding: 10px;
    background-color: #1e1e1e;
    border-radius: 3px;
    overflow-x: auto;
}
.diff-info {
    color: #569cd6;
}
.diff-added {
    background-color: #2d4a3e;
    color: #73c991;
}
.diff-removed {
    background-color: #4a2d2d;
    color: #c97373;
}
.diff-context {
    color: #d4d4d4;
}
.whitespace-space:before {
    content: "·";
    color: #6e7681;
    opacity: 0.5;
}
.whitespace-tab:before {
    content: "→";
    color: #6e7681;
    opacity: 0.5;
}
/* Table styles - applied to all tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    table-layout: fixed;
    background-color: #1e1e1e;
}
th {
    width: 200px;
    max-width: 200px;
    padding: 4px 8px;
    text-align: right;
    background-color: #2d2d2d;
    border-right: 1px solid #444;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
td {
    padding: 4px 8px;
    text-align: left;
    border-bottom: 1px solid #444;
}
tr:hover {
    background-color: #333;
}
/* Specific table styles */
.aux-data-value-true {
    color: #73c991;
    font-weight: bold;
}
.aux-data-value-false {
    color: #c97373;
    font-weight: bold;
}
"""

# Common JavaScript for collapsible sections
COMMON_JS = """
function toggleSection(id) {
    const section = document.getElementById(id).closest('.section');
    section.classList.toggle('expanded');
}

// Expand the first section by default when page loads
document.addEventListener('DOMContentLoaded', function() {
    const firstSection = document.querySelector('.section');
    if (firstSection) {
        firstSection.classList.add('expanded');
    }
});
"""


def _normalize_language_name(name: str) -> str:
    """Normalize language name to a standard format.

    Args:
        name: The language name to normalize

    Returns:
        Normalized language name
    """
    name = name.lower()
    if name in ("py", "python", "python3"):
        return "Python"
    if name in ("js", "javascript"):
        return "JavaScript"
    return "Text"


def detect_language(file_path: str | None, content: str) -> str:
    """Detect the programming language of the code.

    Args:
        file_path: Optional path to the file, used for extension-based detection
        content: The code content to analyze

    Returns:
        The detected language name
    """
    if not content:
        if not file_path:
            return "Text"
        try:
            ext = Path(file_path).suffix[1:].lower()
            return _normalize_language_name(ext)
        except (IndexError, AttributeError):
            return "Text"

    # Try to detect from content first
    try:
        if "def " in content and ":" in content:
            return "Python"
        if "function " in content and "{" in content:
            return "JavaScript"
        lexer = guess_lexer(content)
        name = lexer.name.lower()
        if "python" in name:
            return "Python"
        if "javascript" in name:
            return "JavaScript"
        return "Text"
    except ClassNotFound:
        if file_path:
            try:
                ext = Path(file_path).suffix[1:].lower()
                return _normalize_language_name(ext)
            except (IndexError, AttributeError):
                pass
        return "Text"


def format_code(code: str, language: str | None = None) -> str:
    """Format code with syntax highlighting.

    Args:
        code: The code to format
        language: The programming language name

    Returns:
        HTML string with syntax highlighted code
    """
    if not language:
        language = detect_language(None, code)

    # Break long lines into multiple lines
    max_line_length = 150  # Standard line length limit
    lines = []
    for line in code.splitlines(keepends=True):
        # Preserve indentation
        indent_match = re.match(r"^(\s*)", line)
        indentation = indent_match.group(1) if indent_match else ""
        content = line[len(indentation) :]

        # If line is within limit, keep it as is
        if len(line) <= max_line_length:
            lines.append(line)
            continue

        # For long lines, break at logical points
        current_pos = 0
        line_content = content.rstrip("\n\r")
        while current_pos < len(line_content):
            # Try to find break points
            next_pos = current_pos + max_line_length - len(indentation)
            if next_pos >= len(line_content):
                # Add remaining content
                lines.append(indentation + line_content[current_pos:])
                if content.endswith("\n"):
                    lines[-1] += "\n"
                break

            # Look for good break points: comma, space, operator
            break_pos = -1
            for char in (",", " ", "+", "-", "*", "/", "&", "|", "="):
                pos = line_content.rfind(char, current_pos, next_pos)
                if pos > break_pos:
                    break_pos = pos + 1  # Break after the character

            if break_pos <= current_pos:
                # No good break point found, force break at max length
                break_pos = next_pos

            # Add the line segment with proper indentation
            if current_pos == 0:
                lines.append(indentation + line_content[current_pos:break_pos] + "\n")
            else:
                # Add extra indentation for continuation lines
                lines.append(
                    indentation + "    " + line_content[current_pos:break_pos] + "\n"
                )
            current_pos = break_pos

    code = "".join(lines)

    try:
        language = _normalize_language_name(language)
        if language == "Python":
            lexer = PythonLexer()
        elif language == "JavaScript":
            lexer = JavascriptLexer()
        else:
            return f'<div class="highlight"><pre>{html.escape(code)}</pre></div>'
    except ClassNotFound:
        return f'<div class="highlight"><pre>{html.escape(code)}</pre></div>'

    formatter = HtmlFormatter(style="monokai")
    return highlight(code, lexer, formatter)


def format_diff(diff_text: str | None) -> str:
    """Format a diff with syntax highlighting.

    Args:
        diff_text: The diff text to format

    Returns:
        HTML string with formatted diff
    """
    if not diff_text:
        return ""

    formatted_lines = []
    for line in diff_text.splitlines():
        if line.startswith("@@"):
            formatted_line = f'<span class="diff-info">{line}</span>'
        elif line.startswith("+"):
            formatted_line = (
                f'<span class="diff-added">{_format_whitespace(line[1:])}</span>'
            )
        elif line.startswith("-"):
            formatted_line = (
                f'<span class="diff-removed">{_format_whitespace(line[1:])}</span>'
            )
        else:
            formatted_line = f'<span class="diff-context">{_format_whitespace(line[1:] if line.startswith(" ") else line)}</span>'
        formatted_lines.append(formatted_line)

    return "<br>".join(formatted_lines)


def _format_whitespace(text: str) -> str:
    """Format whitespace characters in text for display.

    Args:
        text: The text to format

    Returns:
        Text with whitespace characters wrapped in spans
    """
    result = ""
    for char in text:
        if char == " ":
            result += '<span class="whitespace-space"></span>'
        elif char == "\t":
            result += '<span class="whitespace-tab"></span>'
        else:
            result += html.escape(char)
    return result

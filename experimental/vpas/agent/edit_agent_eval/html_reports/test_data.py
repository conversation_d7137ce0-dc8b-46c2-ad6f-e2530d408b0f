"""Test data for HTML report generation."""

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from unittest.mock import patch
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalOutput,
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
    LoggedToolCallWithMetadata,
)
from experimental.vpas.codeblocks.udiff import (
    UdiffApplyResult,
    HunkApplyResult,
    HunkLineNumbers,
)
from research.agents.tools import ToolImplOutput, ToolParam


def create_test_data():
    # Create a sample UdiffApplyResult
    udiff_result = UdiffApplyResult(
        udiff="@@ -1,3 +1,4 @@\n def test():\n-    print('hello')\n+    print('hello world')\n     return True\n",
        modified_text="def test():\n    print('hello world')\n    return True\n",
        hunk_apply_results=[
            HunkApplyResult(
                hunk="@@ -1,3 +1,4 @@\n def test():\n-    print('hello')\n+    print('hello world')\n     return True\n",
                modified_text="def test():\n    print('hello world')\n    return True\n",
                error_messages=[],
                parsed_line_numbers=HunkLineNumbers(
                    old_start=1,
                    old_count=3,
                    new_start=1,
                    new_count=4,
                ),
                matched_line_numbers=HunkLineNumbers(
                    old_start=1,
                    old_count=3,
                    new_start=1,
                    new_count=4,
                ),
                found_start=True,
                apply_success=True,
                pure_addition=False,
                pure_addition_to_non_empty_file=False,
                num_old_lines_matched=3,
                num_old_lines_total=3,
            )
        ],
        error_messages=[],
    )

    # Create sample tool calls
    tool_param = ToolParam(
        name="edit_file_agent",
        description="Edit a file",
        input_schema={
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "short_edit_description": {"type": "string"},
            },
            "required": ["file_path", "short_edit_description"],
        },
    )

    edit_agent_call = LoggedToolCallWithMetadata(
        started=True,
        tool=tool_param,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Update print statement",
        },
        tool_output="Edit completed (request_id=123e4567-e89b-12d3-a456-426614174000)",
        tool_message="Success",
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=0,
    )

    edit_file_call = LoggedToolCallWithMetadata(
        started=True,
        tool=tool_param,
        tool_input={
            "file_path": "test.py",
            "short_edit_description": "Update print statement",
        },
        tool_output="Edit completed (request_id=123e4567-e89b-12d3-a456-426614174000)",
        tool_message="Success",
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=0,
    )

    apply_udiff_call = LoggedToolCallWithMetadata(
        started=True,
        tool=ToolParam(
            name="apply_udiff",
            description="Apply a unified diff (udiff) to a file",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to modify",
                    },
                    "udiff": {
                        "type": "string",
                        "description": "The unified diff to apply",
                    },
                },
                "required": ["file_path", "udiff"],
            },
        ),
        tool_input={
            "file_path": "test.py",
            "udiff": "@@ -1,3 +1,4 @@\n def test():\n-    print('hello')\n+    print('hello world')\n     return True\n",
        },
        tool_output="Successfully applied udiff to test.py",
        tool_message="File test.py modified",
        log_file_path="/agent_logs/testuser/agent_log_20250202_031823.pickle",
        log_file_index=1,
    )

    # Create a sample EditAgentEvalSample
    with patch(
        "experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample.get_edit_request"
    ) as mock_get_request:
        # Create a mock edit request
        @dataclass
        class MockEditRequest:
            target_file_content: str = (
                "def test():\n    print('hello')\n    return True\n"
            )
            target_file_path: str = "test.py"
            short_edit_description: str = "Update print statement"
            expected_modified_file_content_variants: List[str] = field(
                default_factory=lambda: [
                    "def test():\n    print('hello world')\n    return True\n"
                ]
            )

        mock_get_request.return_value = MockEditRequest()

        sample = EditAgentEvalSample(
            edit_agent_call=edit_agent_call,
            edit_file_calls=[edit_file_call],
        )
        # Set fields that are normally computed after initialization
        sample.file_path = "test.py"
        sample.short_edit_description = "Update print statement"
        sample.original_file_content = (
            "def test():\n    print('hello')\n    return True\n"
        )
        sample.expected_modified_file_content_variants = [
            "def test():\n    print('hello world')\n    return True\n"
        ]

    # Create a sample ToolImplOutput
    tool_output = ToolImplOutput(
        tool_output="Tool output text",
        tool_result_message="Success",
        auxiliary_data={
            "udiffs_extracted_successfully": True,
            "udiff_results": [udiff_result],
            "response": "Model response text",
        },
    )

    # Create a sample EditAgentEvalOutput
    eval_output = EditAgentEvalOutput(
        sample=sample,
        tool_impl_output=tool_output,
        modified_file_content="def test():\n    print('hello world')\n    return True\n",
        diff_against_original="@@ -1,3 +1,4 @@\n def test():\n-    print('hello')\n+    print('hello world')\n     return True\n",
        diff_against_expected="",
        tool_call_logs=[edit_agent_call, apply_udiff_call],
        correct=True,
        success=True,
    )

    # Create a sample EditAgentEvalSummary
    eval_summary = EditAgentEvalSummary(
        dataset_name="test_dataset",
        anthropic_model="claude-3-5-sonnet-v2@20241022",
        agent_name="test_agent",
        outputs={"test-uuid": eval_output},
        num_samples=1,
        num_samples_with_label=1,
        num_correct=1,
        num_errors=0,
        accuracy=1.0,
        aux_data={
            "total": 1,
            "success": 1,
            "failed": 0,
            "udiffs_extracted": 1,
            "pure_additions": 0,
            "pure_additions_to_non_empty": 0,
            "found_start": 1,
            "total_errors": 0,
            "success_rate": 100.0,
            "udiff_extraction_rate": 100.0,
            "pure_addition_rate": 0.0,
            "found_start_rate": 100.0,
            "avg_errors_per_udiff": 0.0,
            "pure_addition_to_non_empty_rate": 0.0,
            "apply_success_rate": 100.0,
            "avg_lines_matched": 3.0,
            "line_match_rate": 100.0,
            "total_lines_to_match": 3,
        },
    )

    return eval_output, eval_summary

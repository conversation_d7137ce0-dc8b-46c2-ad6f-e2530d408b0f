import pytest
from research.llm_apis.llm_client import (
    Text<PERSON>rom<PERSON>,
    TextR<PERSON>ult,
    <PERSON><PERSON><PERSON>all,
    ToolFormattedResult,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.dialog import (
    generate_dialog_html,
)


def test_generate_dialog_html_simple():
    dialog = [
        [TextPrompt(text="Hello")],  # User message
        [TextResult(text="Hi there!")],  # Assistant message
    ]
    html = generate_dialog_html(dialog)
    assert "Hello" in html
    assert "Hi there!" in html
    assert "user" in html.lower()
    assert "assistant" in html.lower()


def test_generate_dialog_html_with_tools():
    dialog = [
        [TextPrompt(text="What's 2+2?")],  # User message
        [
            ToolCall(
                tool_name="calculator", tool_input={"a": 2, "b": 2}, tool_call_id="1"
            )
        ],  # Assistant tool call
        [
            ToolFormattedResult(
                tool_name="calculator", tool_output="4", tool_call_id="1"
            )
        ],  # Tool result
        [TextResult(text="The answer is 4")],  # Assistant final answer
    ]
    html = generate_dialog_html(dialog)
    assert "What's 2+2?" in html
    assert "calculator" in html
    assert "The answer is 4" in html
    assert "tool-call" in html.lower()
    assert "tool-result" in html.lower()


def test_generate_dialog_html_multiple_blocks():
    dialog = [
        [  # User turn with multiple blocks
            TextPrompt(text="First message"),
            ToolFormattedResult(
                tool_name="tool1", tool_output="result1", tool_call_id="1"
            ),
        ],
        [  # Assistant turn with multiple blocks
            TextResult(text="First response"),
            ToolCall(tool_name="tool2", tool_input={"x": 1}, tool_call_id="2"),
        ],
    ]
    html = generate_dialog_html(dialog)
    assert "First message" in html
    assert "result1" in html
    assert "First response" in html
    assert "tool2" in html
    assert "tool-call" in html.lower()
    assert "tool-result" in html.lower()

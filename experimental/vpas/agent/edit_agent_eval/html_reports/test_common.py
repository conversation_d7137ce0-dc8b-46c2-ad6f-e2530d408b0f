"""Tests for common.py."""

import pytest
from experimental.vpas.agent.edit_agent_eval.html_reports.common import (
    detect_language,
    format_code,
    format_diff,
    _format_whitespace,
)


def test_detect_language_from_file_path():
    """Test language detection from file path."""
    assert detect_language("test.py", "") == "Python"
    assert detect_language("test.js", "") == "JavaScript"
    assert detect_language("test.txt", "") == "Text"
    assert detect_language("test.unknown", "") == "Text"


def test_detect_language_from_content():
    """Test language detection from content."""
    python_code = "def hello():\n    print('Hello!')"
    js_code = "function hello() { console.log('Hello!'); }"

    assert detect_language(None, python_code) == "Python"
    assert detect_language(None, js_code) == "JavaScript"
    assert detect_language(None, "plain text") == "Text"


def test_detect_language_content_precedence():
    """Test that content detection takes precedence over file extension."""
    python_code = "def hello():\n    print('Hello!')"
    js_code = "function hello() { console.log('Hello!'); }"

    assert detect_language("test.js", python_code) == "Python"
    assert detect_language("test.py", js_code) == "JavaScript"


def test_format_code():
    """Test code formatting with syntax highlighting."""
    code = "def test():\n    print('hello')"
    formatted = format_code(code, "Python")

    assert 'class="highlight"' in formatted
    assert '<span class="k">def</span>' in formatted  # keyword
    assert '<span class="nf">test</span>' in formatted  # function name
    assert '<span class="s1">&#39;hello&#39;</span>' in formatted  # string


def test_format_code_unknown_language():
    """Test code formatting with unknown language."""
    code = "Some random text"
    formatted = format_code(code, "UnknownLanguage")

    assert 'class="highlight"' in formatted
    assert code in formatted


def test_format_diff():
    """Test diff formatting."""
    diff = (
        "@@ -1,3 +1,3 @@\n"
        " def test():\n"
        "-    print('old')\n"
        "+    print('new')\n"
        " def other():\n"
    )
    formatted = format_diff(diff)

    assert 'class="diff-info"' in formatted
    assert 'class="diff-added"' in formatted
    assert 'class="diff-removed"' in formatted
    assert 'class="diff-context"' in formatted


def test_format_diff_empty():
    """Test formatting empty diff."""
    assert format_diff("") == ""
    assert format_diff(None) == ""


def test_format_whitespace():
    """Test whitespace formatting."""
    text = "  \ttext"  # 2 spaces, 1 tab, then "text"
    formatted = _format_whitespace(text)

    assert 'class="whitespace-space"' in formatted
    assert 'class="whitespace-tab"' in formatted
    assert "text" in formatted
    assert formatted.count("whitespace-space") == 2
    assert formatted.count("whitespace-tab") == 1

"""Utilities for handling evaluation statistics."""

from typing import Dict, Any, <PERSON>, Tuple, NamedTuple, Optional


class StatField(NamedTuple):
    """Represents a statistic field with its metadata."""

    id: str
    name: str
    is_percentage: bool = False
    group: str = "Other"


def generate_histogram_rows(histogram: Optional[Dict[int, int]] = None) -> str:
    if not histogram:
        return '<tr><td colspan="2">No histogram data available</td></tr>'

    total = sum(histogram.values())
    rows = []

    for num, count in sorted(histogram.items()):
        percentage = (count / total) * 100 if total > 0 else 0
        description = f"{num}"

        row = f"""
            <tr>
                <th>
                    <div>
                        {description}
                    </div>
                </th>
                <td>{count} ({percentage:.1f}%)</td>
            </tr>
        """
        rows.append(row)

    # Add total row
    rows.append(f"""
        <tr>
            <th>
                <div>
                    Total Results
                </div>
            </th>
            <td>{total}</td>
        </tr>
    """)
    rows.append("""
        <tr>
            <th></th>
            <td></td>
        </tr>
    """)

    return "\n".join(rows)


def detect_available_stats(aux_data: List[Tuple[str, Any]]) -> List[StatField]:
    # Detect which fields are present in aux_data
    available_fields = []
    for key, _ in aux_data:
        available_fields.append(
            StatField(
                id=key,
                name=key,
                is_percentage=(
                    key.endswith("_rate")
                    or key.endswith("_percentage")
                    or key.endswith("accuracy")
                    or key.endswith("precision")
                    or key.endswith("recall")
                ),
                group="Other Statistics",
            )
        )

    return available_fields


def format_stat_value(
    value: Any, is_percentage: bool = False, is_histogram: bool = False
) -> str:
    """Format a statistic value for display.

    Args:
        value: The value to format
        is_percentage: Whether the value is a percentage

    Returns:
        Formatted string representation of the value
    """
    if value is None:
        return "N/A"

    if isinstance(value, bool):
        return str(value)

    if isinstance(value, (int, float)):
        if is_percentage:
            if value <= 1:
                value = value * 100
            return f"{float(value):.1f}%"
        if isinstance(value, int):
            return str(value)
        return f"{float(value):.2f}"

    if isinstance(value, dict) and is_histogram:
        return generate_histogram_rows(value)

    if isinstance(value, list) and all(isinstance(item, (int, str)) for item in value):
        return ", ".join(str(item) for item in sorted(value))

    s = str(value)
    if len(s) > 100:
        s = s[:100] + "..."
    return s


def group_stats_by_category(stats: List[StatField]) -> Dict[str, List[StatField]]:
    """Group statistics by their category.

    Args:
        stats: List of StatField objects

    Returns:
        Dictionary mapping category names to lists of StatField objects
    """
    grouped = {}
    for stat in stats:
        if stat.group not in grouped:
            grouped[stat.group] = []
        grouped[stat.group].append(stat)
    return grouped

"""HTML templates for evaluation reports."""

import json
import html
from typing import List, Dict, Any, Union
from pathlib import Path
from .stats_utils import StatField, format_stat_value


def generate_stats_table(
    title: str, stats: List[StatField], aux_data: Dict[str, Any]
) -> str:
    """Generate an HTML table for a group of statistics.

    Args:
        title: Title for the statistics section
        stats: List of StatField objects to include
        aux_data: Dictionary containing the actual values

    Returns:
        HTML string for the statistics table
    """
    rows = []
    for stat in stats:
        value = aux_data.get(stat.id)
        is_histogram = isinstance(value, dict) and stat.id.endswith("_histogram")
        formatted_value = format_stat_value(value, stat.is_percentage, is_histogram)

        rows.append(f"""
            <tr>
                <th>
                    <div>
                        {stat.name}
                    </div>
                </th>
                <td>{formatted_value}</td>
            </tr>
        """)

    return f"""
    <h3>{title}</h3>
    <table class="stats-table">
        {''.join(rows)}
    </table>
    """


def generate_filter_section(available_stats: List[StatField]) -> str:
    """Generate the HTML for the filter section.

    Args:
        available_stats: List of available statistics fields

    Returns:
        HTML string for the filter section
    """
    # Convert stats to JavaScript-friendly format
    stats_js = json.dumps(
        [
            {
                "id": stat.id,
                "name": stat.name,
                "is_percentage": stat.is_percentage,
                "group": stat.group,
            }
            for stat in available_stats
        ]
    )

    return f"""
    <div class="filter-container">
        <h2>Filter Results</h2>
        <div class="filter-group">
            <div id="filter-count"></div>
        </div>

        <div id="filter-rows"></div>

        <div class="filter-actions">
            <button class="action-button" onclick="addFilter()">Add Filter</button>
            <button class="action-button" onclick="resetFilters()">Reset Filters</button>
        </div>
    </div>
    <script>
        window.availableFields = {stats_js};
    </script>
    """


def generate_result_data_attributes(aux_data: List[tuple[str, Any]]) -> tuple[str, str]:
    """Generate data attributes for a result div.

    Args:
        aux_data: List of tuples containing (key, value) pairs

    Returns:
        String of HTML data attributes
    """
    # Generate visible table with attributes
    rows = []

    # Sort and filter attributes for display
    display_attrs = []
    for key, value in aux_data:
        # Skip internal attributes since we show it separately
        if key.startswith("_"):
            continue

        if value is not None:
            # Format the value for display
            display_value = value
            if isinstance(value, bool):
                display_value = "✓ Yes" if value else "✗ No"
            elif isinstance(value, float):
                display_value = f"{value:.2f}"

            rows.append(f"""
                <tr>
                    <th>{key}</th>
                    <td>{display_value}</td>
                </tr>
            """)
            display_attrs.append((key, value))

    def escape_attr_value(val: Any) -> str:
        """Escape and format value for data attribute."""
        if isinstance(val, bool):
            return str(val).lower()
        elif isinstance(val, (int, float)):
            return str(val)
        return html.escape(str(val), quote=True)

    # Generate data attributes for filtering with proper escaping
    data_attrs = []
    for key, value in display_attrs:
        escaped_value = escape_attr_value(value)
        data_attrs.append(f'data-{key}="{escaped_value}"')

    # Return both the visible table and the data attributes for filtering
    return (f'<table class="stats-table">{"".join(rows)}</table>', " ".join(data_attrs))


def read_static_file(filename: str) -> str:
    """Read a static file from the static directory.

    Args:
        filename: Name of the file to read

    Returns:
        Content of the file
    """
    static_dir = Path(__file__).parent / "static"
    file_path = static_dir / filename
    return file_path.read_text()

"""Generate sample HTML reports for testing."""

import uuid
from pathlib import Path
from test_data import create_test_data
from eval_output import save_eval_output_html
from eval_summary import save_eval_summary_html, WEB_SERVER_DIR, URL_TEMPLATE


def main():
    # Create test data
    eval_output, eval_summary = create_test_data()

    # Create output paths
    id = uuid.uuid4().hex[:4]
    rel_path = Path(f"vpas/edit_agent_eval/test_dataset_test_agent/summary_{id}.html")
    output_dir = WEB_SERVER_DIR / rel_path.parent
    output_path = WEB_SERVER_DIR / rel_path

    # Create directories
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save individual result
    result_path = output_dir / f"result_{eval_output.sample.uuid}.html"
    save_eval_output_html(eval_output, result_path)

    # Save summary
    save_eval_summary_html(eval_summary, output_path, output_dir)

    # Print URL
    url = URL_TEMPLATE.format(str(rel_path))
    print(f"Report generated at: {url}")


if __name__ == "__main__":
    main()

"""Generate HTML reports for individual EditAgentEvalOutput objects with updated UdiffApplyResult fields."""

import html
from pathlib import Path
from typing import List, Dict, Any, Union
from pygments.formatters import HtmlFormatter

from experimental.vpas.agent.edit_agent_eval.html_reports.common import (
    COMMON_CSS,
    COMMON_JS,
    detect_language,
    format_code,
    format_diff,
)
from experimental.guy.agent_qa.file_edit.udiff import UdiffApplyResult
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalOutput,
)
from research.agents.tools import LoggedToolCall


def generate_eval_output_html(eval_output: EditAgentEvalOutput) -> str:
    """Generate HTML view for an EditAgentEvalOutput object.

    Args:
        eval_output: The EditAgentEvalOutput object

    Returns:
        HTML string containing the report

    Raises:
        AttributeError: If required attributes are missing from eval_output
        ValueError: If there are issues with data formatting
    """
    try:
        # Get CSS for syntax highlighting and our custom styles
        css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

        # Generate edit agent call sections HTML if available
        edit_agent_old_call_sections = ""
        if eval_output.sample.edit_agent_call is not None:
            edit_agent_old_call_sections = f"""
                <div class="section">
                    <h2 onclick="toggleSection('edit-agent-call')">Edit Agent Call</h2>
                    <div id="edit-agent-call" class="section-content">
                        <h3>Tool Input</h3>
                        <pre>{html.escape(str(eval_output.sample.edit_agent_call.tool_input))}</pre>

                        <h3>Tool Output</h3>
                        <pre>{html.escape(str(eval_output.sample.edit_agent_call.tool_output))}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('edit-file-calls')">Edit File Calls</h2>
                    <div id="edit-file-calls" class="section-content">
                        {_generate_edit_file_calls_html(eval_output.sample.edit_file_calls)}
                    </div>
                </div>
            """

        # Generate HTML content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Edit Agent Eval Output - {eval_output.sample.edit_request_id}</title>
            <style>
                {css}
                {COMMON_CSS}
                .aux-data-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                    table-layout: fixed;
                }}
                .aux-data-table th {{
                    background-color: #2d2d2d;
                    text-align: left;
                    padding: 6px 12px;
                    width: 200px;
                    max-width: 200px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-weight: 500;
                }}
                .aux-data-table td {{
                    padding: 6px 12px;
                    border-bottom: 1px solid #444;
                    text-align: left;
                    width: calc(100% - 200px);
                }}
                .aux-data-value-true {{
                    color: #73c991;
                    font-weight: 500;
                }}
                .aux-data-value-false {{
                    color: #c97373;
                    font-weight: 500;
                }}
            </style>
            <script>
                {COMMON_JS}
            </script>
        </head>
        <body>
            <div class="container">
                <h1>Edit Agent Eval Output</h1>
                <div class="status {'status-correct' if eval_output.correct else 'status-incorrect'}">
                    {'✓ Correct' if eval_output.correct else '✗ Incorrect'}
                </div>
                <p>Request ID: {eval_output.sample.edit_request_id}</p>

                <div class="section">
                    <h2 onclick="toggleSection('sample')">Sample Information</h2>
                    <div id="sample" class="section-content">
                        <h3>File Path</h3>
                        <pre>{html.escape(eval_output.sample.file_path)}</pre>

                        <h3>Edit Description</h3>
                        <pre>{html.escape(eval_output.sample.short_edit_description)}</pre>

                        <h3>Original File Content</h3>
                        <pre class="highlight">{format_code(eval_output.sample.original_file_content, detect_language(eval_output.sample.file_path, eval_output.sample.original_file_content))}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('tool-output')">Tool Output</h2>
                    <div id="tool-output" class="section-content">
                        <h3>Tool Implementation Output</h3>
                        <pre>{html.escape(eval_output.tool_impl_output.tool_output)}</pre>

                        <h3>Tool Result Message</h3>
                        <pre>{html.escape(eval_output.tool_impl_output.tool_result_message)}</pre>

                        <h3>Auxiliary Data</h3>
                        <h4>UDiff Extraction</h4>
                        <table class="aux-data-table">
                            <tr>
                                <th>UDiffs Extracted Successfully</th>
                                <td class="aux-data-value-{'true' if eval_output.tool_impl_output.auxiliary_data.get('udiffs_extracted_successfully') else 'false'}">
                                    {str(eval_output.tool_impl_output.auxiliary_data.get('udiffs_extracted_successfully', False))}
                                </td>
                            </tr>
                        </table>

                        <h4>UDiff Results</h4>
                        {_generate_udiff_results_html(eval_output.tool_impl_output.auxiliary_data.get('udiff_results', []))}

                        <h4>Model Response</h4>
                        <pre>{html.escape(eval_output.tool_impl_output.auxiliary_data.get('response', ''))}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('modified-file')">Modified File</h2>
                    <div id="modified-file" class="section-content">
                        <pre class="highlight">{format_code(eval_output.modified_file_content, detect_language(eval_output.sample.file_path, eval_output.modified_file_content))}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2 onclick="toggleSection('diffs')">Diffs</h2>
                    <div id="diffs" class="section-content">
                        <h3>Against Original</h3>
                        <pre>{format_diff(eval_output.diff_against_original)}</pre>

                        <h3>Against Expected</h3>
                        <pre>{format_diff(eval_output.diff_against_expected)}</pre>
                    </div>
                </div>

                {edit_agent_old_call_sections}
            </div>
        </body>
        </html>
        """

        return html_content
    except AttributeError as e:
        raise AttributeError(f"Missing required attribute in eval_output: {str(e)}")
    except Exception as e:
        raise ValueError(f"Error generating HTML content: {str(e)}")


def _generate_udiff_results_html(
    udiff_results: List[Union[Dict[str, Any], UdiffApplyResult]],
) -> str:
    """Generate HTML for UDiff results.

    Args:
        udiff_results: List of UdiffApplyResult objects or their dict representations

    Returns:
        HTML string for UDiff results

    Raises:
        ValueError: If there are issues processing the UDiff results
    """
    if not udiff_results:
        return "<p>No UDiff results</p>"

    try:
        html_parts = []
        for i, result in enumerate(udiff_results, 1):
            if isinstance(result, dict):
                result = UdiffApplyResult.from_dict(result)

            # Generate HTML for each hunk result
            hunk_results_html = []
            for j, hunk_result in enumerate(result.hunk_apply_results, 1):
                hunk_results_html.append(f"""
                    <h6>Hunk {j}</h6>
                    <pre>{format_diff(hunk_result.hunk)}</pre>
                    <table class="aux-data-table">
                        <tr>
                            <th>Apply Success</th>
                            <td class="aux-data-value-{str(hunk_result.apply_success).lower()}">
                                {hunk_result.apply_success}
                            </td>
                        </tr>
                        <tr>
                            <th>Found Start</th>
                            <td class="aux-data-value-{str(hunk_result.found_start).lower()}">
                                {hunk_result.found_start}
                            </td>
                        </tr>
                        <tr>
                            <th>Pure Addition</th>
                            <td class="aux-data-value-{str(hunk_result.pure_addition).lower()}">
                                {hunk_result.pure_addition}
                            </td>
                        </tr>
                        <tr>
                            <th>Pure Addition to Non-Empty File</th>
                            <td class="aux-data-value-{str(hunk_result.pure_addition_to_non_empty_file).lower()}">
                                {hunk_result.pure_addition_to_non_empty_file}
                            </td>
                        </tr>
                        <tr>
                            <th>Old Lines Matched</th>
                            <td>{hunk_result.num_old_lines_matched} / {hunk_result.num_old_lines_total}</td>
                        </tr>
                        <tr>
                            <th>Line Numbers</th>
                            <td>
                                Parsed: {hunk_result.parsed_line_numbers and f"old: {hunk_result.parsed_line_numbers.old_start},{hunk_result.parsed_line_numbers.old_count} new: {hunk_result.parsed_line_numbers.new_start},{hunk_result.parsed_line_numbers.new_count}" or "None"}<br>
                                Matched: {hunk_result.matched_line_numbers and f"old: {hunk_result.matched_line_numbers.old_start},{hunk_result.matched_line_numbers.old_count} new: {hunk_result.matched_line_numbers.new_start},{hunk_result.matched_line_numbers.new_count}" or "None"}
                            </td>
                        </tr>
                        <tr>
                            <th>Error Messages</th>
                            <td>{'<br>'.join(html.escape(msg) for msg in hunk_result.error_messages)}</td>
                        </tr>
                    </table>
                """)

            html_parts.append(f"""
                <h5>UDiff {i}</h5>
                <pre>{format_diff(result.udiff)}</pre>
                <div class="hunk-results">
                    {''.join(hunk_results_html)}
                </div>
                <table class="aux-data-table">
                    <tr>
                        <th>Error Messages</th>
                        <td>{'<br>'.join(html.escape(msg) for msg in result.error_messages)}</td>
                    </tr>
                </table>
            """)

        return "\n".join(html_parts)
    except Exception as e:
        raise ValueError(f"Error generating UDiff results HTML: {str(e)}")


def _generate_error_messages_html(error_messages: List[str]) -> str:
    """Generate HTML for error messages.

    Args:
        error_messages: List of error messages

    Returns:
        HTML string for error messages
    """
    if not error_messages:
        return "<em>No errors</em>"

    return "<br>".join(html.escape(msg) for msg in error_messages)


def _generate_edit_file_calls_html(edit_file_calls: List[LoggedToolCall]) -> str:
    """Generate HTML for edit file calls section.

    Args:
        edit_file_calls: List of edit file calls

    Returns:
        HTML string for edit file calls section

    Raises:
        ValueError: If there are issues processing the edit file calls
    """
    if not edit_file_calls:
        return "<p>No edit file calls</p>"

    try:
        html_parts = []
        for i, call in enumerate(edit_file_calls, 1):
            html_parts.append(f"""
                <h3>Call {i}</h3>
                <h4>Tool Input</h4>
                <pre class="highlight">{format_code(str(call.tool_input), "json")}</pre>
                <h4>Tool Output</h4>
                <pre>{html.escape(str(call.tool_output))}</pre>
            """)

        return "\n".join(html_parts)
    except Exception as e:
        raise ValueError(f"Error generating edit file calls HTML: {str(e)}")


def save_eval_output_html(
    eval_output: EditAgentEvalOutput, output_path: Union[str, Path]
) -> None:
    """Save EditAgentEvalOutput as HTML to the specified path.

    Args:
        eval_output: The EditAgentEvalOutput object
        output_path: Path where to save the HTML file

    Raises:
        OSError: If there are issues creating directories or writing the file
        ValueError: If there are issues generating the HTML content
    """
    try:
        html_content = generate_eval_output_html(eval_output)
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        output_path.write_text(html_content)
    except OSError as e:
        raise OSError(f"Error saving HTML file: {str(e)}")
    except Exception as e:
        import traceback

        traceback.print_exc()
        raise ValueError(f"Error generating or saving HTML content: {str(e)}")

let filterCount = 1;

function addFilter() {
    const filtersContainer = document.getElementById('filter-rows');
    const newRow = document.createElement('div');
    newRow.className = 'filter-row';
    newRow.id = `filter-row-${filterCount}`;

    // Get available fields from data attributes
    const fields = window.availableFields || [];
    const fieldOptions = fields.map(field =>
        `<option value="${field.id}">${field.name}</option>`
    ).join('\n');

    newRow.innerHTML = `
        <select class="filter-select filter-field" onchange="filterResults()">
            <option value="status">Status</option>
            ${fieldOptions}
        </select>
        <select class="filter-select filter-type" onchange="filterResults()">
            <option value="contains">Contains</option>
            <option value="equals">Equals</option>
            <option value="true">Is True</option>
            <option value="false">Is False</option>
            <option value="greater">Greater Than</option>
            <option value="less">Less Than</option>
        </select>
        <input type="text" class="filter-input filter-value" placeholder="Enter filter value..." onkeyup="filterResults()">
        <button class="action-button" onclick="removeFilter(${filterCount})">Remove</button>
    `;

    filtersContainer.appendChild(newRow);
    filterCount++;
    filterResults();
}

function removeFilter(id) {
    const row = document.getElementById(`filter-row-${id}`);
    row.remove();
    filterResults();
}

function filterResults() {
    const results = document.getElementsByClassName('result-item');
    const filterRows = document.getElementsByClassName('filter-row');

    for (let result of results) {
        let showResult = true;

        for (let filterRow of filterRows) {
            const filterField = filterRow.querySelector('.filter-field').value;
            const filterType = filterRow.querySelector('.filter-type').value;
            const filterValue = filterRow.querySelector('.filter-value').value.toLowerCase();

            let fieldValue;
            if (filterField === 'status') {
                fieldValue = result.getAttribute('data-status');
            } else {
                fieldValue = result.getAttribute('data-' + filterField);
            }

            if (fieldValue !== null) {
                const value = fieldValue.toLowerCase();
                let matches = false;

                switch (filterType) {
                    case 'contains':
                        matches = value.includes(filterValue);
                        break;
                    case 'equals':
                        matches = value === filterValue;
                        break;
                    case 'true':
                        matches = value === 'true';
                        break;
                    case 'false':
                        matches = value === 'false';
                        break;
                    case 'greater':
                        matches = !isNaN(value) && !isNaN(filterValue) && parseFloat(value) > parseFloat(filterValue);
                        break;
                    case 'less':
                        matches = !isNaN(value) && !isNaN(filterValue) && parseFloat(value) < parseFloat(filterValue);
                        break;
                }

                if (!matches) {
                    showResult = false;
                    break;
                }
            }
        }

        result.classList.toggle('hidden', !showResult);
    }

    updateResultCounts();
}

function resetFilters() {
    const filtersContainer = document.getElementById('filter-rows');
    filtersContainer.innerHTML = '';
    addFilter();
}

function updateResultCounts() {
    const allResults = document.getElementsByClassName('result-item');
    const visibleResults = Array.from(allResults).filter(result => !result.classList.contains('hidden')).length;

    // Update the count in the header or another suitable location
    const resultsCountDiv = document.getElementById('filter-count');
    resultsCountDiv.textContent = `Results (${visibleResults} cases)`;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    addFilter();
});

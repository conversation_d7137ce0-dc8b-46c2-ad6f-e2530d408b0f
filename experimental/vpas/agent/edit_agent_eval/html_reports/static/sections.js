// Initialize all sections when the page loads
document.addEventListener('DOMContentLoaded', function () {
    // Initialize all section contents
    document.querySelectorAll('.section-content').forEach(section => {
        // Keep the first few sections expanded by default
        if (section.closest('.section').classList.contains('expanded')) {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    });
});

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;

    // Get computed style to handle initial state correctly
    const isExpanded = window.getComputedStyle(section).display !== 'none';

    // Toggle display
    section.style.display = isExpanded ? 'none' : 'block';

    // Toggle expanded class on parent section
    const parentSection = section.closest('.section');
    if (parentSection) {
        parentSection.classList.toggle('expanded', !isExpanded);
    }
}

function expandAllSections() {
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('expanded');
        const content = section.querySelector('.section-content');
        if (content) {
            content.style.display = 'block';
        }
    });
}

function collapseAllSections() {
    document.querySelectorAll('.section').forEach(section => {
        if (!section.querySelector('#dialog-header')) {
            section.classList.remove('expanded');
            const content = section.querySelector('.section-content');
            if (content) {
                content.style.display = 'none';
            }
        }
    });
}

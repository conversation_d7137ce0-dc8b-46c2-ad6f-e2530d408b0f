.filter-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    border-radius: 6px;
}

.filter-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.filter-input {
    flex: 1;
    padding: 8px;
    background-color: #2d2d2d;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    border-radius: 4px;
}

.filter-select {
    padding: 8px;
    background-color: #2d2d2d;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    border-radius: 4px;
}

.filter-actions {
    margin-top: 10px;
}

.hidden {
    display: none !important;
}

.action-button {
    background-color: #2d2d2d;
    color: #d4d4d4;
    border: 1px solid #3c3c3c;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: #3c3c3c;
    border-color: #569cd6;
}

.action-button:active {
    background-color: #4c4c4c;
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.stats-table th,
.stats-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #444;
}

.stats-table th {
    width: 30%;
    background-color: #2d2d2d;
}

.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted #666;
    cursor: help;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 300px;
    background-color: #2d2d2d;
    color: #d4d4d4;
    text-align: left;
    padding: 8px;
    border-radius: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -150px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

.tool-call-logs {
    margin: 10px 0;
}

.tool-call {
    background-color: #2d2d2d;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

.tool-call h4 {
    margin-top: 0;
    color: #0e639c;
}

.status {
    padding: 5px 10px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 10px;
}

.status-correct {
    background: #2ea043;
    color: white;
}

.status-incorrect {
    background: #f85149;
    color: white;
}

body {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.section {
    margin: 20px 0;
    border: 1px solid #3c3c3c;
    border-radius: 6px;
    overflow: hidden;
}

.section h2 {
    margin: 0;
    padding: 15px;
    background-color: #2d2d2d;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.section h2::after {
    content: '▼';
    position: absolute;
    right: 15px;
    transition: transform 0.3s ease;
}

.section:not(.expanded) h2::after {
    transform: rotate(-90deg);
}

.section-content {
    padding: 15px;
    border-top: 1px solid #3c3c3c;
}


.dialog {
    margin: 20px 0;
}

.message {
    margin: 15px 0;
    padding: 15px;
    background-color: #2d2d2d;
    border: 1px solid #3c3c3c;
    border-radius: 6px;
}

.role {
    font-weight: bold;
    color: #569cd6;
    margin-bottom: 10px;
}

.user-message {
    border-left: 4px solid #569cd6;
}

.assistant-message {
    border-left: 4px solid #4ec9b0;
}

.tool-call {
    margin: 10px 0;
    padding: 10px;
    background-color: #1e1e1e;
    border-radius: 4px;
}

.tool-result {
    margin: 10px 0;
    padding: 10px;
    background-color: #1e1e1e;
    border-radius: 4px;
}

.dict-container {
    margin: 10px 0;
}

.dict-item {
    margin: 5px 0;
}

.dict-key {
    color: #9cdcfe;
    font-weight: bold;
}

.dict-value {
    margin-left: 20px;
}

.markdown-content {
    line-height: 1.5;
}

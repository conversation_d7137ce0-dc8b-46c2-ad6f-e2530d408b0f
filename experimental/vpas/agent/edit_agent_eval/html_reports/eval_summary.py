"""Generate HTML reports for EditAgentEvalSummary objects with updated UdiffApplyResult fields."""

import argparse
import html
import pickle
from pathlib import Path
from typing import Optional, List, Any, Union, Dict, Tuple
from pygments import highlight
from pygments.lexers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pygments.formatters import HtmlFormatter

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.dialog import (
    generate_and_save_dialog_html_with_link,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.eval_output import (
    save_eval_output_html,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.stats_utils import (
    detect_available_stats,
    group_stats_by_category,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.templates import (
    generate_stats_table,
    generate_filter_section,
    generate_result_data_attributes,
    read_static_file,
)
from research.agents.tools import Logged<PERSON><PERSON><PERSON>all, ToolCallLogger

# Constants for web server configuration
WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"


def _format_percentage(value: float) -> str:
    """Format a float as a percentage string.

    Args:
        value: The float value to format

    Returns:
        Formatted percentage string
    """
    return f"{value * 100:.1f}%"


def _generate_tool_call_logs_html(tool_calls: List[LoggedToolCall]) -> str:
    """Generate HTML for tool call logs.

    Args:
        tool_calls: List of ToolCall objects

    Returns:
        HTML string showing tool call logs
    """
    if not tool_calls:
        return "<p>No tool calls logged</p>"

    logs_html = []
    for i, call in enumerate(tool_calls, 1):
        logs_html.append(f"""
            <div class="tool-call">
                <h4>Tool Call #{i}</h4>
                <p><strong>Tool:</strong> {html.escape(call.tool.name)}</p>
                <p><strong>Input:</strong></p>
                <pre>{html.escape(str(call.tool_input))}</pre>
            </div>
        """)
    return "\n".join(logs_html)


def _generate_result_link_html(result: Any, output_dir: Path) -> str:
    """Generate HTML for a link to the individual result page.

    Args:
        result: The evaluation result
        output_dir: The directory containing individual result pages

    Returns:
        HTML string with link to result page
    """
    result_path = output_dir / f"result_{result.sample.uuid}.html"
    save_eval_output_html(result, result_path)

    if result_path.exists():
        return f'<p><a href="{result_path.name}">View detailed report</a></p>'
    return ""


def _generate_tool_call_logs_link(result: Any, output_dir: Path) -> str:
    """Generate HTML link to tool call logs page and save the page.

    Args:
        result: The evaluation result
        output_dir: The directory to save tool call logs pages

    Returns:
        HTML string with link to tool call logs page
    """
    if not output_dir or not result.tool_call_logs:
        return ""

    tool_call_logger = ToolCallLogger()
    tool_call_logger.logged_calls = result.tool_call_logs

    # Generate and save tool call logs page
    logs_path = output_dir / f"tool_calls_{result.sample.uuid}.html"
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Tool Call Logs - {result.sample.uuid}</title>
        <style>
            {read_static_file('styles.css')}
            body {{
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: 'Courier New', monospace;
                margin: 20px;
            }}
        </style>
    </head>
    <body>
        <h1>Tool Call Logs - {result.sample.uuid}</h1>
        {tool_call_logger.get_html_representation()}
    </body>
    </html>
    """
    logs_path.write_text(html_content)

    return f'<p><a href="{logs_path.name}">View detailed tool call logs</a></p>'


def _generate_udiff_calls_html(result: Any) -> str:
    """Generate HTML for UDiff tool calls.

    Args:
        result: The evaluation result containing tool call logs

    Returns:
        HTML string showing UDiff tool calls
    """
    # Extract UDiff tool calls
    udiff_calls = []
    if result.tool_call_logs:
        for call in result.tool_call_logs:
            if call.tool.name == "apply_udiff" and not call.started:
                udiff_calls.append(call)

    # Generate UDiff calls HTML
    if not udiff_calls:
        return ""

    udiff_calls_html = """
    <h3>UDiff Tool Calls</h3>
    <div class="tool-call-logs">
    """
    for i, call in enumerate(udiff_calls, 1):
        udiff_calls_html += f"""
        <div class="tool-call">
            <h4>UDiff Call #{i}</h4>
            <p><strong>File Path:</strong> {html.escape(call.tool_input.get('file_path', 'NO FILE PATH'))}</p>
            <div class="udiff-content">
                <pre class="diff-display">{highlight(
                    call.tool_input.get('udiff', 'NO UDIFF'),
                    DiffLexer(),
                    HtmlFormatter(style='monokai')
                )}</pre>
            </div>
            <p><strong>Tool Output:</strong></p>
            <pre>{html.escape(call.tool_output)}</pre>
            <p><strong>Tool Message:</strong></p>
            <pre>{html.escape(call.tool_message)}</pre>
        </div>
        """
    udiff_calls_html += "</div>"

    return udiff_calls_html


def _generate_links_section(result: Any, output_dir: Optional[Path]) -> str:
    """Generate HTML for the links section of a result.

    Args:
        result: The evaluation result
        output_dir: The directory to save individual pages

    Returns:
        HTML string with links to related pages
    """
    if not output_dir:
        return """
        <h3>Links</h3>
        <div class="links">
        </div>
        """

    links_html = """
    <h3>Links</h3>
    <div class="links">
    """

    # Add tool call logs link if available
    tool_call_logs_link = _generate_tool_call_logs_link(result, output_dir)
    if tool_call_logs_link:
        links_html += tool_call_logs_link

    # Add dialog link if available
    dialog_link = generate_and_save_dialog_html_with_link(result, output_dir)
    if dialog_link:
        links_html += dialog_link

    # Add result link if available
    result_link = _generate_result_link_html(result, output_dir)
    if result_link:
        links_html += result_link

    links_html += "</div>"

    return links_html


def _generate_diff_section(result: Any) -> str:
    """Generate HTML for the diff sections of a result.

    Args:
        result: The evaluation result

    Returns:
        HTML string showing diffs
    """
    diff_html = f"""
    <h3>Diff Against Original</h3>
    <div class="diff-content">
        <pre class="diff-display">{highlight(
            result.diff_against_original,
            DiffLexer(),
            HtmlFormatter(style='monokai')
        )}</pre>
    </div>
    <h3>Diff Against Expected</h3>
    <div class="diff-content">
        <pre class="diff-display">{highlight(
            result.diff_against_expected,
            DiffLexer(),
            HtmlFormatter(style='monokai')
        )}</pre>
    </div>
    """

    return diff_html


def _generate_result_content(
    result: Any, output_dir: Optional[Path]
) -> Tuple[str, str]:
    """Generate HTML content for a single result.

    Args:
        result: The evaluation result
        output_dir: The directory to save individual pages

    Returns:
        Tuple of (HTML content, result metrics data attributes)
    """
    # Generate metrics table and data attributes
    result_metrics = result.get_all_metrics()
    result_metrics_table_html, result_metrics_data = generate_result_data_attributes(
        result_metrics
    )

    # Generate status indicator
    status_html = f"""
    <div class="status {'status-correct' if result.correct else 'status-incorrect'}">
        {'✓ Correct' if result.correct else '✗ Incorrect'}
    </div>
    """

    # Generate result data section
    result_data_html = f"""
    <div class="result-data">
        {result_metrics_table_html}
    </div>
    """

    # Generate links section
    links_html = _generate_links_section(result, output_dir)

    # Generate file path and edit request sections
    file_info_html = f"""
    <h3>File Path</h3>
    <pre>{html.escape(result.sample.file_path)}</pre>
    <h3>Edit Request</h3>
    <pre>{html.escape(result.sample.short_edit_description)}</pre>
    """

    # Generate tool output and result message sections
    tool_output_html = f"""
    <h3>Tool Output</h3>
    <pre>{html.escape(result.tool_impl_output.tool_output)}</pre>
    <h3>Result Message</h3>
    <pre>{html.escape(result.tool_impl_output.tool_result_message)}</pre>
    """

    # Generate UDiff calls section
    udiff_calls_html = _generate_udiff_calls_html(result)

    # Generate diff sections
    diff_html = _generate_diff_section(result)

    # Combine all sections
    result_content = f"""
    {status_html}
    {result_data_html}
    {links_html}
    {file_info_html}
    {tool_output_html}
    {udiff_calls_html}
    {diff_html}
    """

    return result_content, result_metrics_data


def _generate_result_section(result: Any, uuid: str, output_dir: Optional[Path]) -> str:
    """Generate HTML for a complete result section.

    Args:
        result: The evaluation result
        uuid: The result UUID
        output_dir: The directory to save individual pages

    Returns:
        HTML string for the complete result section
    """
    result_id = f"result-{uuid}"

    # Generate result content
    result_content, result_metrics_data = _generate_result_content(result, output_dir)

    # Create collapsible section
    result_section = f"""
    <div class="section result-item" {result_metrics_data}>
        <h2 onclick="toggleSection('{result_id}')">
            {'✓' if result.correct else '✗'} {html.escape(uuid)}
        </h2>
        <div id="{result_id}" class="section-content">
            {result_content}
        </div>
    </div>
    """

    return result_section


def _generate_model_info_section(eval_summary: EditAgentEvalSummary) -> str:
    """Generate HTML for the model information section.

    Args:
        eval_summary: The EditAgentEvalSummary object

    Returns:
        HTML string for the model info section
    """
    model_info_content = f"""
    <table class="stats-table">
        <tr>
            <th>Dataset</th>
            <td>{eval_summary.dataset_name}</td>
        </tr>
        <tr>
            <th>Anthropic Model</th>
            <td>{eval_summary.anthropic_model}</td>
        </tr>
        <tr>
            <th>Agent Name</th>
            <td>{eval_summary.agent_name}</td>
        </tr>
    </table>
    """

    model_info_section = f"""
    <div class="section expanded">
        <h2 onclick="toggleSection('model-info')">Model Information</h2>
        <div id="model-info" class="section-content">
            {model_info_content}
        </div>
    </div>
    """

    return model_info_section


def _generate_filters_section(available_metrics: Dict) -> str:
    """Generate HTML for the filters section.

    Args:
        available_metrics: Dictionary of available metrics

    Returns:
        HTML string for the filters section
    """
    filters_section = f"""
    <div class="section expanded">
        <h2 onclick="toggleSection('filters')">Filters</h2>
        <div id="filters" class="section-content">
            {generate_filter_section(available_metrics)}
        </div>
    </div>
    """

    return filters_section


def _generate_statistics_section(grouped_stats: Dict, metrics: Dict) -> str:
    """Generate HTML for the statistics section.

    Args:
        grouped_stats: Dictionary of grouped statistics
        metrics: Dictionary of metrics values

    Returns:
        HTML string for the statistics section
    """
    # Generate stats tables
    stats_tables = []
    for group_name, stats in grouped_stats.items():
        stats_tables.append(generate_stats_table(group_name, stats, metrics))

    statistics_section = f"""
    <div class="section expanded">
        <h2 onclick="toggleSection('statistics')">Statistics</h2>
        <div id="statistics" class="section-content">
            {''.join(stats_tables)}
        </div>
    </div>
    """

    return statistics_section


def _generate_results_sections(
    eval_summary: EditAgentEvalSummary, output_dir: Optional[Path]
) -> str:
    """Generate HTML for all result sections.

    Args:
        eval_summary: The EditAgentEvalSummary object
        output_dir: The directory to save individual pages

    Returns:
        HTML string for all result sections
    """
    results_sections = []

    for uuid, result in eval_summary.outputs.items():
        result_section = _generate_result_section(result, uuid, output_dir)
        results_sections.append(result_section)

    return "".join(results_sections)


def _generate_html_head(eval_summary: EditAgentEvalSummary) -> str:
    """Generate HTML for the document head section.

    Args:
        eval_summary: The EditAgentEvalSummary object

    Returns:
        HTML string for the head section
    """
    # Get CSS for syntax highlighting and custom styles
    css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

    head_html = f"""
    <head>
        <meta charset="UTF-8">
        <title>{eval_summary.agent_name}</title>
        <style>
            {css}
            {read_static_file('styles.css')}
        </style>
        <script>{read_static_file('filters.js')}</script>
        <script>{read_static_file('sections.js')}</script>
    </head>
    """

    return head_html


def generate_eval_summary_html(
    eval_summary: EditAgentEvalSummary,
    output_dir: Optional[Path] = None,
) -> str:
    """Generate HTML report for EditAgentEvalSummary.

    Args:
        eval_summary: The EditAgentEvalSummary object
        output_dir: Optional output directory where individual eval outputs are saved

    Returns:
        Complete HTML content as a string
    """
    # Detect available metrics
    available_metrics = detect_available_stats(
        next(iter(eval_summary.outputs.values())).get_all_metrics()
    )

    # Get summary stats
    summary_stats = detect_available_stats(eval_summary.get_all_metrics())
    grouped_stats = group_stats_by_category(summary_stats)
    metrics = dict(eval_summary.get_all_metrics())

    # Generate HTML head
    head_html = _generate_html_head(eval_summary)

    # Generate main sections
    model_info_section = _generate_model_info_section(eval_summary)
    filters_section = _generate_filters_section(available_metrics)
    statistics_section = _generate_statistics_section(grouped_stats, metrics)
    results_sections = _generate_results_sections(eval_summary, output_dir)

    # Combine all sections into complete HTML
    html_content = f"""
    <!DOCTYPE html>
    <html>
    {head_html}
    <body>
        <div class="container">
            {model_info_section}
            {filters_section}
            {statistics_section}
            {results_sections}
        </div>
    </body>
    </html>
    """

    return html_content


def save_eval_summary_html(
    eval_summary: EditAgentEvalSummary,
    output_path: Union[str, Path],
    output_dir: Optional[Path] = None,
) -> None:
    """Save EditAgentEvalSummary as HTML to the specified path.

    Args:
        eval_summary: The EditAgentEvalSummary object
        output_path: Path where to save the HTML file
        output_dir: Optional output directory where individual eval outputs are saved
    """
    html_content = generate_eval_summary_html(eval_summary, output_dir)
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    output_path.write_text(html_content)

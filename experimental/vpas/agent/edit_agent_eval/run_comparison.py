"""<PERSON><PERSON>t to run evaluation comparison between two agents."""

import argparse
from pathlib import Path
from typing import Optional

from experimental.guy.agent_qa.file_edit.edit_file_agent_type import (
    EditFileAgentType,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.run_eval import (
    run_eval,
    get_agent_name,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.comparison import (
    save_comparison_html,
)


def get_default_output_dir() -> Path:
    """Get default output directory for evaluation results."""
    return Path("/mnt/efs/augment/user/vpas/edit_agent_eval")


def main():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--dataset-dir",
        type=Path,
        default=Path(__file__).parent / "dataset",
        help="Directory containing evaluation dataset",
    )
    parser.add_argument(
        "--limit-samples",
        type=int,
        help="Limit the number of samples to process",
    )
    parser.add_argument(
        "--uuid",
        type=str,
        help="UUID of a specific sample to evaluate",
    )
    parser.add_argument(
        "--category",
        type=str,
        help="Category of samples to evaluate",
    )
    parser.add_argument(
        "--anthropic-model",
        type=str,
        default="claude-3-5-sonnet-v2@20241022",
        help="Anthropic model to use in EditFileAgent",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Run evaluation even if results already exist",
    )
    parser.add_argument(
        "--agent-old",
        type=str,
        choices=[agent_type.name for agent_type in EditFileAgentType],
        required=True,
        help="Base agent type to compare",
    )
    parser.add_argument(
        "--agent-new",
        type=str,
        choices=[agent_type.name for agent_type in EditFileAgentType],
        required=True,
        help="New agent type to compare against base",
    )
    parser.add_argument(
        "--max-turns",
        type=int,
        default=5,
        help="Maximum number of turns for the agent",
    )
    args = parser.parse_args()

    # Set up paths and names
    WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
    JSON_DIR = get_default_output_dir()
    dataset_name = args.dataset_dir.name

    # Add limit, uuid, and category to dataset name if specified
    if args.uuid:
        dataset_name = f"uuid_{args.uuid}"
    else:
        if args.category:
            dataset_name = f"{dataset_name}_{args.category}"
        if args.limit_samples:
            dataset_name = f"{dataset_name}_limit_{args.limit_samples}"

    # Run evaluations for both agents
    summaries = {}
    for agent_type_name in [args.agent_old, args.agent_new]:
        agent_type = EditFileAgentType[agent_type_name]
        print(f"\nRunning evaluation for agent type: {agent_type_name}")

        # Define output paths for this agent
        eval_dir_name = f"{dataset_name}_{args.anthropic_model}_{get_agent_name(agent_type)}_mt{args.max_turns}"
        summary_path = JSON_DIR / eval_dir_name / "summary.json"
        summary_path.parent.mkdir(parents=True, exist_ok=True)

        # Check cache or run evaluation
        if summary_path.exists() and not args.no_cache:
            print(f"Found existing results at {summary_path}")
            with open(summary_path) as f:
                summary = EditAgentEvalSummary.from_json(f.read())
        else:
            # Run evaluation and save results
            summary = run_eval(
                dataset_dir=args.dataset_dir,
                output_path=summary_path,
                limit_samples=args.limit_samples,
                specific_uuid=args.uuid,
                anthropic_model=args.anthropic_model,
                agent_type=agent_type,
                max_turns=args.max_turns,
                category=args.category,
            )

        summaries[agent_type_name] = summary

    # Generate comparison report
    comparison_dir_name = f"comparison_{dataset_name}_{args.agent_old}_vs_{args.agent_new}_mt{args.max_turns}"
    rel_path = f"vpas/edit_agent_eval/{comparison_dir_name}"
    comparison_dir = WEB_SERVER_DIR / rel_path
    comparison_dir.mkdir(parents=True, exist_ok=True)
    comparison_path = comparison_dir / "comparison.html"

    # Save comparison HTML
    save_comparison_html(
        base_summary=summaries[args.agent_old],
        new_summary=summaries[args.agent_new],
        output_path=comparison_path,
        output_dir=comparison_dir,
    )

    # Print URL
    URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
    url = URL_TEMPLATE.format(f"{rel_path}/comparison.html")
    print(f"\nComparison report generated at: {url}")


if __name__ == "__main__":
    main()

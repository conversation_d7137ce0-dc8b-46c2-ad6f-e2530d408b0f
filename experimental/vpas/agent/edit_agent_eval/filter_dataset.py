"""
<PERSON><PERSON><PERSON> to filter an edit agent evaluation dataset based on evaluation results.

This script reads an EditAgentEvalSummary from a JSON file, extracts sample UUIDs
based on a filter function that accepts EditAgentEvalOutput objects, and copies
matching samples from the source dataset to an output directory.
"""

import argparse
import json
import random
import shutil
from pathlib import Path
from typing import Callable, List, Set

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalOutput,
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
)


def filter_successful_samples(output: dict) -> bool:
    """Filter function that selects samples where success is True."""
    return output.get("success") is True


def filter_correct_samples(output: dict) -> bool:
    """Filter function that selects samples where correct is True."""
    return output.get("correct") is True


def filter_failed_samples(output: dict) -> bool:
    """Filter function that selects samples where success is False."""
    return output.get("success") is False


def filter_incorrect_samples(output: dict) -> bool:
    """Filter function that selects samples where correct is False."""
    return output.get("correct") is False


def extract_sample_uuids(
    eval_summary_data: dict,
    filter_fn: Callable[[dict], bool],
) -> Set[str]:
    """
    Extract sample UUIDs based on a filter function.

    Args:
        eval_summary_data: The raw evaluation summary dictionary containing outputs
        filter_fn: A function that takes an EditAgentEvalOutput and returns a boolean

    Returns:
        A set of UUIDs for samples that match the filter criteria
    """
    matching_uuids = set()

    for uuid, output_data in eval_summary_data.get("outputs", {}).items():
        if filter_fn(output_data):
            matching_uuids.add(uuid)

    return matching_uuids


def copy_samples(
    source_dataset_path: Path,
    output_path: Path,
    sample_uuids: Set[str],
) -> int:
    """
    Copy samples from source dataset to output path.

    Args:
        source_dataset_path: Path to the source dataset
        output_path: Path to the output directory
        sample_uuids: Set of UUIDs to copy

    Returns:
        Number of samples copied
    """
    output_path.mkdir(parents=True, exist_ok=True)

    copied_count = 0
    for uuid in sample_uuids:
        source_sample_dir = source_dataset_path / uuid
        if not source_sample_dir.exists():
            print(f"Warning: Sample directory not found: {source_sample_dir}")
            continue

        target_sample_dir = output_path / uuid
        if target_sample_dir.exists():
            shutil.rmtree(target_sample_dir)

        shutil.copytree(source_sample_dir, target_sample_dir)
        copied_count += 1

    return copied_count


def main():
    parser = argparse.ArgumentParser(description="Filter edit agent evaluation dataset")
    parser.add_argument(
        "eval_summary_path",
        type=Path,
        help="Path to the evaluation summary JSON file",
    )
    parser.add_argument(
        "dataset_path",
        type=Path,
        help="Path to the source dataset directory",
    )
    parser.add_argument(
        "output_path",
        type=Path,
        help="Path to the output directory for filtered samples",
    )
    parser.add_argument(
        "--filter",
        choices=["success", "correct", "failed", "incorrect", "all"],
        default="all",
        help="Filter type to apply (default: all)",
    )
    parser.add_argument(
        "--random-samples",
        type=int,
        help="Randomly select N samples from the filtered set",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=42,
        help="Random seed for sample selection (default: 42)",
    )

    args = parser.parse_args()

    # Validate paths
    if not args.eval_summary_path.exists():
        raise FileNotFoundError(
            f"Evaluation summary file not found: {args.eval_summary_path}"
        )

    if not args.dataset_path.exists() or not args.dataset_path.is_dir():
        raise FileNotFoundError(f"Dataset directory not found: {args.dataset_path}")

    # Load evaluation summary
    with open(args.eval_summary_path, "r") as f:
        eval_summary_data = json.load(f)

    # Select filter function
    filter_functions = {
        "success": filter_successful_samples,
        "correct": filter_correct_samples,
        "failed": filter_failed_samples,
        "incorrect": filter_incorrect_samples,
        "all": lambda _: True,
    }

    filter_fn = filter_functions[args.filter]

    # Extract matching sample UUIDs
    matching_uuids = extract_sample_uuids(eval_summary_data, filter_fn)
    print(f"Found {len(matching_uuids)} samples matching the '{args.filter}' filter")

    # Apply random sampling if requested
    if args.random_samples is not None and args.random_samples > 0:
        if args.random_samples >= len(matching_uuids):
            print(
                f"Requested {args.random_samples} samples, but only {len(matching_uuids)} available"
            )
        else:
            random.seed(args.seed)
            matching_uuids_list = list(matching_uuids)
            random.shuffle(matching_uuids_list)
            matching_uuids = set(matching_uuids_list[: args.random_samples])
            print(
                f"Randomly selected {len(matching_uuids)} samples (seed: {args.seed})"
            )

    # Copy samples to output directory
    copied_count = copy_samples(args.dataset_path, args.output_path, matching_uuids)
    print(f"Copied {copied_count} samples to {args.output_path}")

    # Save filter info
    filter_info = {
        "filter_type": args.filter,
        "total_samples": len(eval_summary_data.get("outputs", {})),
        "matching_samples_before_random": len(
            extract_sample_uuids(eval_summary_data, filter_fn)
        ),
        "matching_samples_after_random": len(matching_uuids),
        "copied_samples": copied_count,
        "source_dataset": str(args.dataset_path),
        "eval_summary": str(args.eval_summary_path),
    }

    if args.random_samples is not None:
        filter_info["random_samples_requested"] = args.random_samples
        filter_info["random_seed"] = args.seed

    with open(args.output_path / "filter_info.json", "w") as f:
        json.dump(filter_info, f, indent=2)


if __name__ == "__main__":
    main()

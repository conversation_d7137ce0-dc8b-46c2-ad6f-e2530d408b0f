"""Tests for create_eval_from_agent_logs.py."""

import pytest
import tempfile
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import patch, MagicMock
from dataclasses import dataclass

from research.agents.tools import Logged<PERSON><PERSON><PERSON><PERSON>, Tool
from experimental.vpas.agent.edit_agent_eval.create_eval_from_agent_logs import (
    convert_to_list_of_trees,
    LoggedToolCallNode,
    EditAgentEvalSample,
    main,
)
from experimental.vpas.agent.edit_agent_eval.fetch_edit_request import (
    GCSRequestInsightFetcher,
    get_edit_request,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    LoggedToolCallWithMetadata,
)


@dataclass
class MockTool:
    name: str
    description: str = "Test tool"
    input_schema: Dict[str, Any] = None

    def __post_init__(self):
        if self.input_schema is None:
            self.input_schema = {"type": "object", "properties": {}}


@dataclass
class MockInstructionHostRequest:
    target_file_content: str
    request: Any = None

    def __post_init__(self):
        if self.request is None:
            self.request = self


class MockEvent:
    def __init__(self, request=None):
        self._request = request
        self._fields = {"instruction_host_request": True} if request else {}
        # Add instruction_host_request attribute that returns a MagicMock with request
        self.instruction_host_request = MagicMock(request=self._request)

    def HasField(self, field_name: str) -> bool:
        # Always return True for instruction_host_request to match the expected behavior
        if field_name == "instruction_host_request":
            return True
        return field_name in self._fields

    @property
    def instructionHostRequest(self):
        return MagicMock(request=self._request)


class MockResult:
    def __init__(self, events=None):
        self.events = events or []


class MockGCSRequestInsightFetcher:
    def __init__(self, mock_request=None):
        self.mock_request = mock_request

    @classmethod
    def from_tenant_id(cls, project, tenant_id, bucket_name, max_pool_connections):
        return cls()

    def get_request(self, request_id, request_event_names):
        if self.mock_request is None:
            return MockResult()
        event = MockEvent(request=self.mock_request)
        return MockResult(events=[event])


def create_tool_call(
    name: str, started: bool, tool_input=None, tool_output=None
) -> LoggedToolCall:
    tool = type(
        "Tool",
        (),
        {
            "name": name,
            "description": f"Test tool {name}",
            "input_schema": {"type": "object", "properties": {}},
        },
    )()
    return LoggedToolCall(
        tool=tool,
        started=started,
        tool_input=tool_input or {},
        tool_output=tool_output,
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
    )


def test_convert_to_tree_simple():
    # Simple case: one parent with no children
    tool_calls = [create_tool_call("parent", True), create_tool_call("parent", False)]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert isinstance(root, LoggedToolCallNode)
    assert root.tool_call.tool.name == "parent"
    assert len(root.children) == 0
    assert not root.interrupted


def test_convert_to_tree_with_children():
    # Parent with one child
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child", True),
        create_tool_call("child", False),
        create_tool_call("parent", False),
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.tool_call.tool.name == "parent"
    assert len(root.children) == 1
    assert root.children[0].tool_call.tool.name == "child"
    assert len(root.children[0].children) == 0
    assert not root.interrupted
    assert not root.children[0].interrupted


def test_convert_to_tree_nested():
    # Nested structure with multiple levels
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child1", True),
        create_tool_call("grandchild", True),
        create_tool_call("grandchild", False),
        create_tool_call("child1", False),
        create_tool_call("child2", True),
        create_tool_call("child2", False),
        create_tool_call("parent", False),
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.tool_call.tool.name == "parent"
    assert len(root.children) == 2
    assert root.children[0].tool_call.tool.name == "child1"
    assert root.children[1].tool_call.tool.name == "child2"
    assert len(root.children[0].children) == 1
    assert root.children[0].children[0].tool_call.tool.name == "grandchild"
    assert not root.interrupted
    assert not root.children[0].interrupted
    assert not root.children[1].interrupted
    assert not root.children[0].children[0].interrupted


def test_convert_to_tree_empty():
    with pytest.raises(ValueError, match="Tool calls list is empty"):
        convert_to_list_of_trees([])


def test_convert_to_tree_with_interrupted_calls():
    # Test case with interrupted calls (missing end calls)
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child1", True),
        create_tool_call("child1", False),
        create_tool_call("child2", True),
        # Missing end calls for child2 and parent
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.tool_call.tool.name == "parent"
    assert root.interrupted  # Parent should be marked as interrupted
    assert len(root.children) == 2
    assert not root.children[0].interrupted  # child1 is complete
    assert root.children[1].interrupted  # child2 is interrupted


def test_convert_to_tree_with_mismatched_end_calls():
    # Test case with mismatched end calls
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child", True),
        create_tool_call("parent", False),  # Mismatched end call (should be child)
        create_tool_call("child", False),
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.tool_call.tool.name == "parent"
    assert root.interrupted  # Parent should be marked as interrupted
    assert len(root.children) == 1
    assert root.children[0].interrupted  # Child should be marked as interrupted


def test_convert_to_tree_with_nested_interruption():
    # Test case with nested interruption
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child1", True),
        create_tool_call("grandchild", True),
        # Missing end calls for all levels
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.interrupted
    assert len(root.children) == 1
    child = root.children[0]
    assert child.interrupted
    assert len(child.children) == 1
    grandchild = child.children[0]
    assert grandchild.interrupted


def test_convert_to_tree_multiple_roots():
    # Test case with multiple independent root calls
    tool_calls = [
        create_tool_call("root1", True),
        create_tool_call("root1", False),
        create_tool_call("root2", True),
        create_tool_call("root2", False),
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 2
    assert roots[0].tool_call.tool.name == "root1"
    assert roots[1].tool_call.tool.name == "root2"
    assert not roots[0].interrupted
    assert not roots[1].interrupted


def test_convert_to_tree_parallel_calls():
    # Test case with sequential tool calls that appear parallel
    tool_calls = [
        create_tool_call("parent", True),
        create_tool_call("child1", True),
        create_tool_call("child2", True),
        create_tool_call("child1", False),
        create_tool_call("child2", False),
        create_tool_call("parent", False),
    ]

    roots = convert_to_list_of_trees(tool_calls)
    assert len(roots) == 1
    root = roots[0]
    assert root.tool_call.tool.name == "parent"
    assert len(root.children) == 1
    child1 = root.children[0]
    assert child1.tool_call.tool.name == "child1"
    assert len(child1.children) == 1
    child2 = child1.children[0]
    assert child2.tool_call.tool.name == "child2"
    # All nodes should be marked as interrupted since we can't match end calls correctly
    assert root.interrupted
    assert child1.interrupted
    assert child2.interrupted


@pytest.fixture
def mock_sample():
    # Create mock tools
    edit_agent_tool = MockTool(name="edit_file_agent")  # Updated tool name
    edit_file_tool = MockTool(name="edit_file")

    # Create mock tool calls with proper tool attributes
    edit_agent_call_start = LoggedToolCallWithMetadata(
        tool=edit_agent_tool,
        started=True,
        tool_input={
            "file_path": "test/path/example.py.eval",
            "short_edit_description": "Test edit\n\nDetailed test edit description",  # Updated field name
        },
        tool_output=None,
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=0,
    )

    edit_file_call_start1 = LoggedToolCallWithMetadata(
        tool=edit_file_tool,
        started=True,
        tool_input={},
        tool_output=None,
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=1,
    )

    edit_file_call_end1 = LoggedToolCallWithMetadata(
        tool=edit_file_tool,
        started=False,
        tool_input={},
        tool_output="request_id=12345678-1234-5678-1234-************",
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=2,
    )

    edit_file_call_start2 = LoggedToolCallWithMetadata(
        tool=edit_file_tool,
        started=True,
        tool_input={},
        tool_output=None,
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=3,
    )

    edit_file_call_end2 = LoggedToolCallWithMetadata(
        tool=edit_file_tool,
        started=False,
        tool_input={},
        tool_output="request_id=12345678-1234-5678-1234-************",  # Use same request ID as first call
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=4,
    )

    edit_agent_call_end = LoggedToolCallWithMetadata(
        tool=edit_agent_tool,
        started=False,
        tool_input={
            "file_path": "test/path/example.py.eval",
            "short_edit_description": "Test edit\n\nDetailed test edit description",  # Updated field name
        },
        tool_output="Edit agent did not complete after max turns",
        tool_message=None,
        auxiliary_data=None,
        timestamp=None,
        log_file_path="test_log.pickle",
        log_file_index=5,
    )

    # Create a mock request with the target file content
    mock_request = MockInstructionHostRequest(
        target_file_content="def test():\n    pass\n"
    )

    # Initialize the sample with required fields
    with patch(
        "experimental.vpas.agent.edit_agent_eval.fetch_edit_request.GCSRequestInsightFetcher.from_tenant_id",  # Updated patch path
        return_value=MockGCSRequestInsightFetcher(mock_request),
    ), patch(
        "experimental.vpas.agent.edit_agent_eval.fetch_edit_request.get_edit_request",  # Updated patch path
        return_value=mock_request,
    ):
        sample = EditAgentEvalSample(
            edit_agent_call_end, [edit_file_call_end1, edit_file_call_end2]
        )
        sample.expected_modified_file_content_variants = ["def test():\n    pass\n"]
        sample.original_file_content = "def test():\n    pass\n"

    # Return both the sample and the tool calls in the correct order
    tool_calls = [
        edit_agent_call_start,  # Parent start
        edit_file_call_start1,  # First child start
        edit_file_call_end1,  # First child end
        edit_file_call_start2,  # Second child start
        edit_file_call_end2,  # Second child end
        edit_agent_call_end,  # Parent end
    ]
    return sample, tool_calls


@pytest.fixture
def temp_dataset_dir():
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


def test_dataset_creation(mock_sample, temp_dataset_dir, monkeypatch):
    # Mock the script directory to use our temp directory
    monkeypatch.setattr(
        "experimental.vpas.agent.edit_agent_eval.create_eval_from_agent_logs.__file__",
        str(temp_dataset_dir / "script.py.eval"),
    )

    # Create a pickle file with our mock data
    pickle_path = temp_dataset_dir / "test_data.pickle"
    sample, tool_calls = mock_sample
    with open(pickle_path, "wb") as f:
        import pickle

        pickle.dump(tool_calls, f)

    # Create a mock request with the target file content
    mock_request = MockInstructionHostRequest(
        target_file_content="def test():\n    pass\n"
    )

    # Create a mock fetcher instance that will return our mock request
    mock_fetcher_instance = MockGCSRequestInsightFetcher(mock_request)

    # Patch both the GCS client and get_edit_request
    with patch(
        "experimental.vpas.agent.edit_agent_eval.fetch_edit_request.GCSRequestInsightFetcher.from_tenant_id",  # Updated patch path
        return_value=mock_fetcher_instance,
    ), patch(
        "experimental.vpas.agent.edit_agent_eval.fetch_edit_request.get_edit_request",  # Updated patch path
        return_value=mock_request,
    ):
        # Run main with our test pickle file
        with patch("sys.argv", ["script.py.eval", str(pickle_path)]):
            main()

    # Check dataset directory was created
    dataset_dir = temp_dataset_dir / "dataset"
    assert dataset_dir.exists()

    # Get the sample directory (should be only one)
    sample_dirs = list((dataset_dir / "unknown").iterdir())
    assert len(sample_dirs) == 1
    sample_dir = sample_dirs[0]

    # Check sample.json
    sample_file = sample_dir / "sample.json"
    assert sample_file.exists()
    with open(sample_file) as f:
        metadata = json.load(f)

        # Check top-level fields
        assert metadata["uuid"] == sample.uuid
        assert metadata["file_path"] == "test/path/example.py.eval"
        assert (
            metadata["short_edit_description"]
            == "Test edit\n\nDetailed test edit description"
        )  # Updated field name
        assert metadata["failed_after_max_retries"] is True
        assert metadata["edit_request_id"] == "12345678-1234-5678-1234-************"

        # Check edit_agent_call fields
        assert (
            metadata["edit_agent_call"]["tool"] is None
        )  # Tool field is cleared in to_dict
        assert metadata["edit_agent_call"]["started"] is False
        assert (
            metadata["edit_agent_call"]["tool_input"]
            == {
                "file_path": "test/path/example.py.eval",
                "short_edit_description": "Test edit\n\nDetailed test edit description",  # Updated field name
            }
        )
        assert (
            metadata["edit_agent_call"]["tool_output"]
            == "Edit agent did not complete after max turns"
        )
        assert metadata["edit_agent_call"]["tool_message"] is None
        assert metadata["edit_agent_call"]["auxiliary_data"] is None
        assert metadata["edit_agent_call"]["timestamp"] is None

        # Check edit_file_calls fields
        assert (
            len(metadata["edit_file_calls"]) == 2
        )  # Updated to expect both edit file calls
        edit_file_call1 = metadata["edit_file_calls"][0]
        assert edit_file_call1["tool"] is None  # Tool field is cleared in to_dict
        assert edit_file_call1["started"] is False
        assert edit_file_call1["tool_input"] == {}
        assert (
            edit_file_call1["tool_output"]
            == "request_id=12345678-1234-5678-1234-************"
        )
        assert edit_file_call1["tool_message"] is None
        assert edit_file_call1["auxiliary_data"] is None
        assert edit_file_call1["timestamp"] is None

        edit_file_call2 = metadata["edit_file_calls"][1]
        assert edit_file_call2["tool"] is None  # Tool field is cleared in to_dict
        assert edit_file_call2["started"] is False
        assert edit_file_call2["tool_input"] == {}
        assert (
            edit_file_call2["tool_output"]
            == "request_id=12345678-1234-5678-1234-************"  # Same request ID as first call
        )
        assert edit_file_call2["tool_message"] is None
        assert edit_file_call2["auxiliary_data"] is None
        assert edit_file_call2["timestamp"] is None

    # Check original file
    original_file = sample_dir / "a_original_example.py.eval"
    assert original_file.exists()
    with open(original_file) as f:
        assert f.read() == "def test():\n    pass\n"

    # Check expected file
    expected_file = sample_dir / "b_expected_0_example.py.eval"
    assert expected_file.exists()
    with open(expected_file) as f:
        assert f.read() == "def test():\n    pass\n"

#!/usr/bin/env python3

import os
import shutil
import subprocess
import argparse
from pathlib import Path
from tools.user_info import get_build_user

# Get current user
user = get_build_user()

# Define directories
logs_dir = Path(f"/mnt/efs/augment/user/guy/agent_logs/{user}")
web_dir = Path(f"/mnt/efs/augment/public_html/{user}/agent_logs")
url_base = f"https://webserver.gcp-us1.r.augmentcode.com/{user}/agent_logs"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "pickle_file", nargs="?", type=Path, help="Path to pickle file to convert"
    )
    args = parser.parse_args()

    # Create web directory if it doesn't exist
    web_dir.mkdir(parents=True, exist_ok=True)

    if args.pickle_file:
        if not args.pickle_file.exists():
            print(f"Pickle file not found: {args.pickle_file}")
            return 1
        pickle_file = args.pickle_file
    else:
        # Find latest pickle file
        pickle_files = list(logs_dir.glob("*.pickle"))
        if not pickle_files:
            print(f"No pickle files found in {logs_dir}")
            return 1

        pickle_file = max(pickle_files, key=lambda x: x.stat().st_mtime)

    # Convert to HTML
    subprocess.run(
        [
            "python",
            "experimental/guy/agent_qa/convert_agent_log_to_html.py",
            "--pickle_log_file",
            str(pickle_file),
        ],
        check=True,
    )

    # Get HTML file path (same as pickle but with .html extension)
    html_file = pickle_file.with_suffix(".html")
    html_filename = html_file.name

    # Copy to web directory
    shutil.copy2(html_file, web_dir / html_filename)

    # Print URL
    print(f"{url_base}/{html_filename}")
    return 0


if __name__ == "__main__":
    exit(main())

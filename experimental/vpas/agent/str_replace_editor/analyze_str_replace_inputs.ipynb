{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.third_party_clients.token_counter.token_counter import (\n", "    TokenizerBasedTokenCounter,\n", ")\n", "from difflib import unified_diff\n", "\n", "claude_token_counter = ClaudeTokenCounter()\n", "counter = claude_token_counter\n", "\n", "\n", "def print_token_count(text, name):\n", "    token_count = counter.count_tokens(text)\n", "    print(f\"{name} token count: {token_count}\")\n", "    print(f\"{name} theoretical latency: {token_count // 60}\")\n", "\n", "\n", "def print_analysis(inputs):\n", "    print(\"*\" * 80)\n", "    print_token_count(inputs[\"old_str\"], \"old_str\")\n", "    print_token_count(inputs[\"new_str\"], \"new_str\")\n", "    print(f\"old_str_start_line_number: {inputs['old_str_start_line_number']}\")\n", "    print(f\"old_str_end_line_number: {inputs['old_str_end_line_number']}\")\n", "\n", "    # print diff between old and new str\n", "    print(\"Diff:\")\n", "    diff = unified_diff(\n", "        inputs[\"old_str\"].splitlines(),\n", "        inputs[\"new_str\"].splitlines(),\n", "        fromfile=\"old_str\",\n", "        tofile=\"new_str\",\n", "        lineterm=\"\",\n", "        n=5,\n", "    )\n", "    print(\"\\n\".join(diff))\n", "    print(\"*\" * 80)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of entries: 1\n", "********************************************************************************\n", "old_str token count: 435\n", "old_str theoretical latency: 7\n", "new_str token count: 442\n", "new_str theoretical latency: 7\n", "old_str_start_line_number: 0\n", "old_str_end_line_number: 0\n", "Diff:\n", "--- old_str\n", "+++ new_str\n", "@@ -34,6 +34,7 @@\n", "             is_accepted=True,\n", "             annotated_text=\"annotated text\",\n", "             annotated_instruction=\"annotated instruction\",\n", "             timestamp=datetime(2024, 1, 1, 0, 0, 2),\n", "         ),\n", "+        session_id=None,\n", "     )\n", "********************************************************************************\n", "    expected = EditDatum(\n", "        request_id=\"test-request\",\n", "        user_id=\"test-user\",\n", "        user_agent=\"test-agent\",\n", "        request=EditRequest(\n", "            prefix=simulated_file.full_prefix,\n", "            suffix=simulated_file.full_suffix,\n", "            selected_text=\"test-selected-text\",\n", "            instruction=\"test-instruction\",\n", "            path=\"test-path\",\n", "            lang=\"test-lang\",\n", "            blob_names=[a.hex() for a in added_blob_names],\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 0),\n", "            position=EditPosition(\n", "                prefix_begin=simulated_file.prefix_begin,\n", "                suffix_end=simulated_file.suffix_end,\n", "                blob_name=\"my-blob-name\",\n", "                original_prefix_length=(\n", "                    simulated_file.cursor_position - simulated_file.prefix_begin\n", "                ),\n", "                original_suffix_length=(\n", "                    simulated_file.suffix_end - simulated_file.cursor_position\n", "                ),\n", "            ),\n", "        ),\n", "        response=EditResponse(\n", "            text=\"test-generated-text\",\n", "            model_name=\"test-model\",\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 1),\n", "            unknown_blob_names=[\"unknown-blob-name\"],\n", "            checkpoint_not_found=False,\n", "        ),\n", "        resolution=EditResolution(\n", "            is_accepted=True,\n", "            annotated_text=\"annotated text\",\n", "            annotated_instruction=\"annotated instruction\",\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 2),\n", "        ),\n", "    )\n", "--------------------------------------------------------------------------------\n", "    expected = EditDatum(\n", "        request_id=\"test-request\",\n", "        user_id=\"test-user\",\n", "        user_agent=\"test-agent\",\n", "        request=EditRequest(\n", "            prefix=simulated_file.full_prefix,\n", "            suffix=simulated_file.full_suffix,\n", "            selected_text=\"test-selected-text\",\n", "            instruction=\"test-instruction\",\n", "            path=\"test-path\",\n", "            lang=\"test-lang\",\n", "            blob_names=[a.hex() for a in added_blob_names],\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 0),\n", "            position=EditPosition(\n", "                prefix_begin=simulated_file.prefix_begin,\n", "                suffix_end=simulated_file.suffix_end,\n", "                blob_name=\"my-blob-name\",\n", "                original_prefix_length=(\n", "                    simulated_file.cursor_position - simulated_file.prefix_begin\n", "                ),\n", "                original_suffix_length=(\n", "                    simulated_file.suffix_end - simulated_file.cursor_position\n", "                ),\n", "            ),\n", "        ),\n", "        response=EditResponse(\n", "            text=\"test-generated-text\",\n", "            model_name=\"test-model\",\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 1),\n", "            unknown_blob_names=[\"unknown-blob-name\"],\n", "            checkpoint_not_found=False,\n", "        ),\n", "        resolution=EditResolution(\n", "            is_accepted=True,\n", "            annotated_text=\"annotated text\",\n", "            annotated_instruction=\"annotated instruction\",\n", "            timestamp=datetime(2024, 1, 1, 0, 0, 2),\n", "        ),\n", "        session_id=None,\n", "    )\n"]}], "source": ["entries = [\n", "    {\n", "        \"old_str\": '    expected = EditDatum(\\n        request_id=\"test-request\",\\n        user_id=\"test-user\",\\n        user_agent=\"test-agent\",\\n        request=EditRequest(\\n            prefix=simulated_file.full_prefix,\\n            suffix=simulated_file.full_suffix,\\n            selected_text=\"test-selected-text\",\\n            instruction=\"test-instruction\",\\n            path=\"test-path\",\\n            lang=\"test-lang\",\\n            blob_names=[a.hex() for a in added_blob_names],\\n            timestamp=datetime(2024, 1, 1, 0, 0, 0),\\n            position=EditPosition(\\n                prefix_begin=simulated_file.prefix_begin,\\n                suffix_end=simulated_file.suffix_end,\\n                blob_name=\"my-blob-name\",\\n                original_prefix_length=(\\n                    simulated_file.cursor_position - simulated_file.prefix_begin\\n                ),\\n                original_suffix_length=(\\n                    simulated_file.suffix_end - simulated_file.cursor_position\\n                ),\\n            ),\\n        ),\\n        response=EditResponse(\\n            text=\"test-generated-text\",\\n            model_name=\"test-model\",\\n            timestamp=datetime(2024, 1, 1, 0, 0, 1),\\n            unknown_blob_names=[\"unknown-blob-name\"],\\n            checkpoint_not_found=False,\\n        ),\\n        resolution=EditResolution(\\n            is_accepted=True,\\n            annotated_text=\"annotated text\",\\n            annotated_instruction=\"annotated instruction\",\\n            timestamp=datetime(2024, 1, 1, 0, 0, 2),\\n        ),\\n    )',\n", "        \"new_str\": '    expected = EditDatum(\\n        request_id=\"test-request\",\\n        user_id=\"test-user\",\\n        user_agent=\"test-agent\",\\n        request=EditRequest(\\n            prefix=simulated_file.full_prefix,\\n            suffix=simulated_file.full_suffix,\\n            selected_text=\"test-selected-text\",\\n            instruction=\"test-instruction\",\\n            path=\"test-path\",\\n            lang=\"test-lang\",\\n            blob_names=[a.hex() for a in added_blob_names],\\n            timestamp=datetime(2024, 1, 1, 0, 0, 0),\\n            position=EditPosition(\\n                prefix_begin=simulated_file.prefix_begin,\\n                suffix_end=simulated_file.suffix_end,\\n                blob_name=\"my-blob-name\",\\n                original_prefix_length=(\\n                    simulated_file.cursor_position - simulated_file.prefix_begin\\n                ),\\n                original_suffix_length=(\\n                    simulated_file.suffix_end - simulated_file.cursor_position\\n                ),\\n            ),\\n        ),\\n        response=EditResponse(\\n            text=\"test-generated-text\",\\n            model_name=\"test-model\",\\n            timestamp=datetime(2024, 1, 1, 0, 0, 1),\\n            unknown_blob_names=[\"unknown-blob-name\"],\\n            checkpoint_not_found=False,\\n        ),\\n        resolution=EditResolution(\\n            is_accepted=True,\\n            annotated_text=\"annotated text\",\\n            annotated_instruction=\"annotated instruction\",\\n            timestamp=datetime(2024, 1, 1, 0, 0, 2),\\n        ),\\n        session_id=None,\\n    )',\n", "        \"old_str_start_line_number\": 0,\n", "        \"old_str_end_line_number\": 0,\n", "    }\n", "]\n", "print(f\"Number of entries: {len(entries)}\")\n", "str_replace_entry = entries[0]\n", "print_analysis(str_replace_entry)\n", "print(str_replace_entry[\"old_str\"])\n", "print(\"-\" * 80)\n", "print(str_replace_entry[\"new_str\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
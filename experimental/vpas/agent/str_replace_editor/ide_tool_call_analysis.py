from base.datasets.gcs_client import GCSRequestInsightFetcher, group_by_event_name
from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds
import datetime


DOGFOOD_SHARD = "dogfood-shard"


def get_tool_use_data(request_id: str, tenant_name: str = DOGFOOD_SHARD):
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({"tool_use_data"}),
    )
    return (request_id, result.events[0].tool_use_data)


def get_all_tool_calls_request_ids(
    tool_name: str,
    tenant_name: str = DOGFOOD_SHARD,
    limit: int = -1,
    from_datetime: datetime.datetime | None = None,
    to_datetime: datetime.datetime | None = None,
    is_error: bool | None = None,
):
    """Get request IDs for tool calls from BigQuery with optional error filtering.

    Args:
        tool_name: The name of the tool to query for (e.g. 'str-replace-editor')
        tenant_name: The tenant name to query for (defaults to dogfood-shard)
        limit: The maximum number of rows to return (defaults to -1, which means no limit)
        from_datetime: Optional start datetime to filter results
        to_datetime: Optional end datetime to filter results
        is_error: Optional filter for error status (True for errors, False for successes, None for all)

    Returns:
        List of request IDs matching the criteria
    """
    PROJECT_ID = "system-services-prod"
    DATASET = "us_staging_request_insight_analytics_dataset"
    TABLE = "tool_use_data"

    error_filter = ""
    if is_error is not None:
        error_value = "true" if is_error else "false"
        error_filter = f"AND JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_output_is_error') = '{error_value}'"

    query = f"""
        SELECT request_id
        FROM `{PROJECT_ID}.{DATASET}.{TABLE}`
        WHERE JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') = @tool_name
        {error_filter}
        AND tenant = @tenant_name
        {"AND time >= @from_datetime" if from_datetime else ""}
        {"AND time <= @to_datetime" if to_datetime else ""}
        LIMIT @limit
    """

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)

    query_parameters = [
        bigquery.ScalarQueryParameter("tool_name", "STRING", tool_name),
        bigquery.ScalarQueryParameter("tenant_name", "STRING", tenant_name),
        bigquery.ScalarQueryParameter("limit", "INT64", limit),
    ]

    if from_datetime:
        query_parameters.append(
            bigquery.ScalarQueryParameter("from_datetime", "TIMESTAMP", from_datetime)
        )
    if to_datetime:
        query_parameters.append(
            bigquery.ScalarQueryParameter("to_datetime", "TIMESTAMP", to_datetime)
        )

    job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)

    rows = bigquery_client.query_and_wait(query, job_config=job_config)
    return [row.request_id for row in rows]


def count_total_tool_calls(
    tool_name: str,
    tenant_name: str = DOGFOOD_SHARD,
    from_datetime: datetime.datetime | None = None,
    to_datetime: datetime.datetime | None = None,
):
    """Count total tool calls from BigQuery.

    Args:
        tool_name: The name of the tool to query for (e.g. 'str-replace-editor')
        tenant_name: The tenant name to query for (defaults to dogfood-shard)
        from_datetime: Optional start datetime to filter results
        to_datetime: Optional end datetime to filter results

    Returns:
        Total count of tool calls matching the criteria
    """
    PROJECT_ID = "system-services-prod"
    DATASET = "us_staging_request_insight_analytics_dataset"
    TABLE = "tool_use_data"

    query = f"""
        SELECT COUNT(*) as total_count
        FROM `{PROJECT_ID}.{DATASET}.{TABLE}`
        WHERE JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') = @tool_name
        AND tenant = @tenant_name
        {"AND time >= @from_datetime" if from_datetime else ""}
        {"AND time <= @to_datetime" if to_datetime else ""}
    """

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)

    query_parameters = [
        bigquery.ScalarQueryParameter("tool_name", "STRING", tool_name),
        bigquery.ScalarQueryParameter("tenant_name", "STRING", tenant_name),
    ]

    if from_datetime:
        query_parameters.append(
            bigquery.ScalarQueryParameter("from_datetime", "TIMESTAMP", from_datetime)
        )
    if to_datetime:
        query_parameters.append(
            bigquery.ScalarQueryParameter("to_datetime", "TIMESTAMP", to_datetime)
        )

    job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)

    rows = list(bigquery_client.query_and_wait(query, job_config=job_config))
    return rows[0].total_count


def get_failed_tool_calls(
    tool_name: str,
    tenant_name: str = DOGFOOD_SHARD,
    limit: int = -1,
    from_datetime: datetime.datetime | None = None,
    to_datetime: datetime.datetime | None = None,
):
    """Get failed tool calls data from BigQuery.

    Args:
        tool_name: The name of the tool to query for (e.g. 'str-replace-editor')
        tenant_name: The tenant name to query for (defaults to dogfood-shard)
        limit: The maximum number of rows to return (defaults to -1, which means no limit)
        from_datetime: Optional start datetime to filter results
        to_datetime: Optional end datetime to filter results

    Returns:
        Generator yielding tool use data for each failed call
    """
    request_ids = get_all_tool_calls_request_ids(
        tool_name=tool_name,
        tenant_name=tenant_name,
        limit=limit,
        from_datetime=from_datetime,
        to_datetime=to_datetime,
        is_error=True,
    )
    for request_id in request_ids:
        yield get_tool_use_data(request_id, tenant_name)


def get_next_request_ids(
    request_ids: list[str], tenant_name: str = DOGFOOD_SHARD
) -> dict[str, str | None]:
    """Get the next request ID for each input request ID from BigQuery.
    The next request ID is defined as the request that happened within 10 seconds after
    the current request in the same session.

    Args:
        request_ids: List of request IDs to get next request IDs for
        tenant_name: The tenant name to query for (defaults to dogfood-shard)

    Returns:
        Dictionary mapping input request IDs to their next request IDs (or None if no next request exists)
    """
    PROJECT_ID = "system-services-prod"
    DATASET = "us_staging_request_insight_analytics_dataset"
    TABLE = "chat_host_request"

    query = f"""
    WITH current_messages AS (
      SELECT
        request_id as current_request_id,
        time as current_time,
        JSON_VALUE(sanitized_json, '$.request_source') as request_source,
        tenant
      FROM `{PROJECT_ID}.{DATASET}.{TABLE}`
      WHERE request_id IN UNNEST(@request_ids)
      AND tenant = @tenant_name
    ),
    potential_next_messages AS (
      SELECT
        curr.current_request_id,
        next_msg.request_id as next_request_id,
        TIMESTAMP_DIFF(next_msg.time, curr.current_time, SECOND) as time_diff_seconds
      FROM current_messages curr
      LEFT JOIN `{PROJECT_ID}.{DATASET}.{TABLE}` next_msg
        ON JSON_VALUE(next_msg.sanitized_json, '$.request_source') = curr.request_source
        AND next_msg.time > curr.current_time
        AND TIMESTAMP_DIFF(next_msg.time, curr.current_time, SECOND) <= 100
        AND next_msg.tenant = curr.tenant
        -- Check that the current message's request_id appears in the next message's chat history
        AND REGEXP_CONTAINS(TO_JSON_STRING(JSON_EXTRACT(next_msg.sanitized_json, '$.chat_history')), curr.current_request_id)
    )
    SELECT
      current_request_id,
      ARRAY_AGG(
        STRUCT(
          next_request_id,
          time_diff_seconds
        )
        ORDER BY time_diff_seconds ASC
        LIMIT 1
      )[OFFSET(0)].next_request_id as next_request_id
    FROM potential_next_messages
    GROUP BY current_request_id
    """

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)

    query_parameters = [
        bigquery.ArrayQueryParameter("request_ids", "STRING", request_ids),
        bigquery.ScalarQueryParameter("tenant_name", "STRING", tenant_name),
    ]

    job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)

    rows = list(bigquery_client.query_and_wait(query, job_config=job_config))
    print("Query results:")
    for row in rows:
        print(
            f"  Current: {row.current_request_id} at {row.current_time} ({row.request_source})"
        )
        print(f"  Next: {row.next_request_id} at {row.next_time}")
        print(f"  Time diff: {row.time_diff_seconds} seconds")
        print(f"  Chat history: {row.chat_history}")
        print()

    # Group by current_request_id and take the first next_request_id for each
    result = {}
    for row in rows:
        if row.current_request_id not in result:
            result[row.current_request_id] = row.next_request_id
    return result

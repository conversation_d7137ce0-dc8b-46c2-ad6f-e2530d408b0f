import pytest
from unittest.mock import MagicMock, patch
from experimental.vpas.agent.edit_agent_eval.ide_tool_call_analysis import (
    get_failed_tool_calls_request_ids,
)


class MockBigQueryRow:
    def __init__(self, request_id):
        self.request_id = request_id


@pytest.fixture
def mock_bigquery_client():
    with patch("google.cloud.bigquery.Client") as mock_client:
        yield mock_client


@pytest.fixture
def mock_gcp_creds():
    with patch("base.datasets.gcp_creds.get_gcp_creds") as mock_creds:
        mock_creds.return_value = (MagicMock(), None)
        yield mock_creds


def test_get_failed_tool_calls_request_ids(mock_bigquery_client, mock_gcp_creds):
    # Setup mock response
    mock_rows = [
        MockBigQueryRow("00001234-abcd-efgh-ijkl-mnopqrstuvwx"),
        MockBigQueryRow("00005678-abcd-efgh-ijkl-mnopqrstuvwx"),
    ]
    mock_client_instance = mock_bigquery_client.return_value
    mock_client_instance.query_and_wait.return_value = mock_rows

    # Call function
    result = get_failed_tool_calls_request_ids("str-replace-editor")

    # Verify results
    assert len(result) == 2
    assert result[0] == "00001234-abcd-efgh-ijkl-mnopqrstuvwx"
    assert result[1] == "00005678-abcd-efgh-ijkl-mnopqrstuvwx"

    # Verify query parameters
    mock_client_instance.query_and_wait.assert_called_once()
    call_args = mock_client_instance.query_and_wait.call_args
    query = call_args[0][0]
    job_config = call_args[1]["job_config"]

    assert "JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') = @tool_name" in query
    assert "JSON_EXTRACT_SCALAR(sanitized_json, '$.success') = 'false'" in query
    assert "tenant = @tenant_name" in query

    params = job_config.query_parameters
    assert len(params) == 2
    assert params[0].name == "tool_name"
    assert params[0].value == "str-replace-editor"
    assert params[1].name == "tenant_name"
    assert params[1].value == "dogfood-shard"  # default value

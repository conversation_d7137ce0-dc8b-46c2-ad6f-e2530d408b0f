import unittest
from unittest.mock import patch, MagicMock

from extract_testcase_from_request import (
    parse_original_content,
    extract_tool_input_and_content,
)


def test_parse_original_content_empty():
    """Test parsing empty content."""
    assert parse_original_content("") == ""
    assert parse_original_content(None) == ""


def test_parse_original_content_simple():
    """Test parsing a simple file with line numbers."""
    view_response = """Here's the result of running `cat -n` on test.py:
     1	def hello():
     2	    print("Hello, world!")
     3
     4	if __name__ == "__main__":
     5	    hello()
Total lines in file: 5
"""
    expected = """def hello():
    print("Hello, world!")

if __name__ == "__main__":
    hello()"""

    assert parse_original_content(view_response) == expected


def test_parse_original_content_with_empty_lines():
    """Test parsing content with empty lines."""
    view_response = """Here's the result of running `cat -n` on test.py:
     1
     2	def hello():
     3	    print("Hello, world!")
     4
     5	if __name__ == "__main__":
     6	    hello()
     7
Total lines in file: 7
"""
    expected = """
def hello():
    print("Hello, world!")

if __name__ == "__main__":
    hello()
"""

    assert parse_original_content(view_response) == expected


def test_parse_original_content_with_line_numbers_in_content():
    """Test parsing content that has numbers in the actual content."""
    view_response = """Here's the result of running `cat -n` on test.py:
     1	def count():
     2	    for i in range(10):
     3	        print(f"Line {i+1}")
     4
     5	count()
Total lines in file: 5
"""
    expected = """def count():
    for i in range(10):
        print(f"Line {i+1}")

count()"""

    assert parse_original_content(view_response) == expected


def test_parse_original_content_specific_input():
    """Test with the specific input provided in the request."""
    view_input = """Here's the result of running `cat -n` on clients/common/webviews/src/apps/settings/Settings.svelte:
     1	<script lang="ts">
     2	  import { host } from "$common-webviews/src/common/hosts/host";
     3	  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
     4	  import { ToolConfigModel, type ConfigBlock } from "./models/settings-model";
     5	  import ToolCategory from "./components/ToolCategory.svelte";
     6	  import { onDestroy } from "svelte";
     7	  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
     8	  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
     9	  import { RemoteToolId } from "@augment-internal/sidecar-libs/src/tools/tool-types";
    10
    11	  const toolConfigModel = new ToolConfigModel(host);
    12	  const msgBroker = new MessageBroker(host);
    13	  msgBroker.registerConsumer(toolConfigModel);
    14
    15	  // Get all configs for reference
    16	  const configs = toolConfigModel.getConfigs();
    17
    18	  // Get filtered, sorted, and deduplicated tools
    19	  const displayableTools = toolConfigModel.getDisplayableTools();
    20
    21	  function handleSave(config: ConfigBlock) {
    22	    toolConfigModel.saveConfig(config);
    23	  }
    24
    25	  function handleAuthenticate(url: string) {
    26	    // Use the tool-call message type to call the openBrowser tool
    27	    host.postMessage({
    28	      type: WebViewMessageType.toolConfigStartOAuth,
    29	      data: {
    30	        authUrl: url,
    31	      },
    32	    });
    33
    34	    // Accelerate polling after authentication
    35	    toolConfigModel.acceleratePolling();
    36	  }
    37
    38	  function handleRevokeAccess(config: ConfigBlock) {
    39	    host.postMessage({
    40	      type: WebViewMessageType.toolConfigRevokeAccess,
    41	      data: {
    42	        toolId: config.toolId ?? RemoteToolId.Unknown,
    43	      },
    44	    });
    45
    46	    // Accelerate polling after revoking access
    47	    toolConfigModel.acceleratePolling();
    48	  }
    49
    50	  // Clean up resources when the component is destroyed
    51	  onDestroy(() => {
    52	    toolConfigModel.dispose();
    53	  });
    54
    55	  toolConfigModel.notifyLoaded();
    56	</script>
    57
    58	<svelte:window on:message={msgBroker.onMessageFromExtension} />
    59
    60	<main>
    61	  {#if $configs}
    62	    <div class="section-heading">
    63	      <TextAugment size={5} weight="medium">Tools</TextAugment>
    64	    </div>
    65
    66	    <ToolCategory
    67	      title="Web"
    68	      tools={$displayableTools}
    69	      onSave={handleSave}
    70	      onAuthenticate={handleAuthenticate}
    71	      onRevokeAccess={handleRevokeAccess}
    72	    />
    73	  {/if}
    74	</main>
    75
    76	<style>
    77	  main {
    78	    padding: var(--ds-spacing-4);
    79	  }
    80
    81	  .section-heading {
    82	    color: var(--ds-text);
    83	    opacity: 0.5;
    84	    margin: 0 0 var(--ds-spacing-2) 0;
    85	    border-bottom: 1px solid currentColor;
    86	    padding-bottom: var(--ds-spacing-2);
    87	  }
    88	</style>
    89
Total lines in file: 89
"""

    expected = """<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ToolConfigModel, type ConfigBlock } from "./models/settings-model";
  import ToolCategory from "./components/ToolCategory.svelte";
  import { onDestroy } from "svelte";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { RemoteToolId } from "@augment-internal/sidecar-libs/src/tools/tool-types";

  const toolConfigModel = new ToolConfigModel(host);
  const msgBroker = new MessageBroker(host);
  msgBroker.registerConsumer(toolConfigModel);

  // Get all configs for reference
  const configs = toolConfigModel.getConfigs();

  // Get filtered, sorted, and deduplicated tools
  const displayableTools = toolConfigModel.getDisplayableTools();

  function handleSave(config: ConfigBlock) {
    toolConfigModel.saveConfig(config);
  }

  function handleAuthenticate(url: string) {
    // Use the tool-call message type to call the openBrowser tool
    host.postMessage({
      type: WebViewMessageType.toolConfigStartOAuth,
      data: {
        authUrl: url,
      },
    });

    // Accelerate polling after authentication
    toolConfigModel.acceleratePolling();
  }

  function handleRevokeAccess(config: ConfigBlock) {
    host.postMessage({
      type: WebViewMessageType.toolConfigRevokeAccess,
      data: {
        toolId: config.toolId ?? RemoteToolId.Unknown,
      },
    });

    // Accelerate polling after revoking access
    toolConfigModel.acceleratePolling();
  }

  // Clean up resources when the component is destroyed
  onDestroy(() => {
    toolConfigModel.dispose();
  });

  toolConfigModel.notifyLoaded();
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />

<main>
  {#if $configs}
    <div class="section-heading">
      <TextAugment size={5} weight="medium">Tools</TextAugment>
    </div>

    <ToolCategory
      title="Web"
      tools={$displayableTools}
      onSave={handleSave}
      onAuthenticate={handleAuthenticate}
      onRevokeAccess={handleRevokeAccess}
    />
  {/if}
</main>

<style>
  main {
    padding: var(--ds-spacing-4);
  }

  .section-heading {
    color: var(--ds-text);
    opacity: 0.5;
    margin: 0 0 var(--ds-spacing-2) 0;
    border-bottom: 1px solid currentColor;
    padding-bottom: var(--ds-spacing-2);
  }
</style>
"""

    result = parse_original_content(view_input)
    assert result == expected


class TestExtractToolInputAndContent(unittest.TestCase):
    @patch("extract_testcase_from_request.get_request_events")
    @patch("extract_testcase_from_request.parse_tool_inputs")
    @patch("extract_testcase_from_request.find_latest_view_response")
    @patch("extract_testcase_from_request.parse_original_content")
    def test_extract_tool_input_and_content_success(
        self, mock_parse_content, mock_find_view, mock_parse_inputs, mock_get_events
    ):
        # Setup mocks
        mock_chat_request = MagicMock()
        mock_chat_response = MagicMock()
        mock_result = MagicMock()
        mock_get_events.return_value = (
            mock_chat_request,
            mock_chat_response,
            mock_result,
        )

        # Mock tool inputs
        tool_input = {
            "command": "str_replace",
            "path": "test/file.py",
            "str_replace_entries": [],
        }
        mock_parse_inputs.return_value = [{"tool_use_id": "123", "input": tool_input}]

        # Mock view response and content
        mock_find_view.return_value = "view response"
        mock_parse_content.return_value = "original content"

        # Call the function
        result_input, result_content = extract_tool_input_and_content(
            "request123", "test-tenant"
        )

        # Verify results
        self.assertEqual(result_input, tool_input)
        self.assertEqual(result_content, "original content")

        # Verify calls
        mock_get_events.assert_called_once_with("request123", "test-tenant")
        mock_parse_inputs.assert_called_once_with(mock_chat_response, mock_result)
        mock_find_view.assert_called_once_with(mock_chat_request, "test/file.py")
        mock_parse_content.assert_called_once_with("view response")

    @patch("extract_testcase_from_request.get_request_events")
    @patch("extract_testcase_from_request.parse_tool_inputs")
    def test_extract_tool_input_and_content_no_inputs(
        self, mock_parse_inputs, mock_get_events
    ):
        # Setup mocks
        mock_chat_request = MagicMock()
        mock_chat_response = MagicMock()
        mock_result = MagicMock()
        mock_get_events.return_value = (
            mock_chat_request,
            mock_chat_response,
            mock_result,
        )

        # Mock empty tool inputs
        mock_parse_inputs.return_value = []

        # Call the function
        result_input, result_content = extract_tool_input_and_content("request123")

        # Verify results
        self.assertIsNone(result_input)
        self.assertIsNone(result_content)

    @patch("extract_testcase_from_request.get_request_events")
    def test_extract_tool_input_and_content_exception(self, mock_get_events):
        # Setup mock to raise exception
        mock_get_events.side_effect = Exception("Test error")

        # Call the function
        result_input, result_content = extract_tool_input_and_content("request123")

        # Verify results
        self.assertIsNone(result_input)
        self.assertIsNone(result_content)

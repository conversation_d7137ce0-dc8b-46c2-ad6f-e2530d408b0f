{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from experimental.vpas.agent.edit_agent_eval.ide_tool_call_analysis import (\n", "    get_failed_tool_calls,\n", "    count_total_tool_calls,\n", ")\n", "\n", "from_datetime = datetime.datetime(2025, 3, 3, 0, 0, 0, tzinfo=datetime.timezone.utc)\n", "to_datetime = datetime.datetime(2025, 3, 10, 0, 0, 0, tzinfo=datetime.timezone.utc)\n", "\n", "total = count_total_tool_calls(\"str-replace-editor\", from_datetime=from_datetime)\n", "print(f\"Total tool calls: {total}\")\n", "\n", "failed_calls = list(\n", "    get_failed_tool_calls(\"str-replace-editor\", limit=1000, from_datetime=from_datetime)\n", ")\n", "print(f\"Failed tool calls: {len(list(failed_calls))}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failed_calls[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import json\n", "\n", "command_counter = Counter()\n", "for failed_call in failed_calls:\n", "    tool_input = json.loads(failed_call[1].tool_input)\n", "    command_counter[tool_input[\"command\"]] += 1\n", "\n", "print(command_counter.most_common(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vpas.agent.edit_agent_eval.ide_tool_call_analysis import (\n", "    get_next_request_ids,\n", ")\n", "\n", "get_next_request_ids([\"db7d67af-6b7c-4a06-a8d1-dac46bac0983\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
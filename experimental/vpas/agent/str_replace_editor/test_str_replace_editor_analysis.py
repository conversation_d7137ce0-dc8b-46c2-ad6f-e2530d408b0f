"""
Tests for str_replace_editor_analysis.py using pytest
"""

import pytest
from unittest.mock import patch, MagicMock, mock_open
import json
import datetime
from dataclasses import dataclass
from typing import Optional, List, Dict, Any

from experimental.vpas.agent.str_replace_editor.str_replace_editor_analysis import (
    parse_tool_input,
    extract_error_message,
    analyze_tool_call,
    collect_tool_call_stats,
    calculate_aggregate_stats,
    generate_html_report,
    ToolCallStats,
)


@dataclass
class MockToolUseData:
    """Mock class for tool use data."""

    tool_name: str
    tool_use_id: str
    tool_output_is_error: bool
    tool_run_duration_ms: int
    tool_input: str
    tool_output: Optional[str] = None


def test_parse_tool_input_valid_json():
    """Test parsing valid JSON tool input."""
    input_str = '{"command": "view", "path": "test.py"}'
    result = parse_tool_input(input_str)
    assert result == {"command": "view", "path": "test.py"}


def test_parse_tool_input_invalid_json():
    """Test parsing invalid JSON tool input."""
    input_str = "{invalid json}"
    result = parse_tool_input(input_str)
    assert result == {}


def test_extract_error_message_with_error():
    """Test extracting error message from tool output with error."""
    output = "Error: File not found\nSome other text"
    result = extract_error_message(output)
    assert result == "File not found"


def test_extract_error_message_with_failed_edit():
    """Test extracting error message from failed edit output."""
    output = "Failed to edit the file test.py.\nInvalid parameter: path\n\nFix failed entries accordingly and try again."
    result = extract_error_message(output)
    assert result == "Invalid parameter: path"


def test_extract_error_message_no_error():
    """Test extracting error message from output with no error."""
    output = "Successfully edited the file test.py."
    result = extract_error_message(output)
    assert result is None


def test_analyze_tool_call_view_command():
    """Test analyzing a tool call with view command."""
    request_id = "12345"
    tool_use_data = MockToolUseData(
        tool_name="str-replace-editor",
        tool_use_id="tool-123",
        tool_output_is_error=False,
        tool_run_duration_ms=150,
        tool_input='{"command": "view", "path": "test.py", "view_range": [1, 10]}',
    )

    result = analyze_tool_call(request_id, tool_use_data)

    assert result.request_id == "12345"
    assert result.tool_name == "str-replace-editor"
    assert result.command == "view"
    assert result.path == "test.py"
    assert result.view_range == [1, 10]
    assert not result.is_error
    assert result.duration_ms == 150


def test_analyze_tool_call_str_replace_command():
    """Test analyzing a tool call with str_replace command."""
    request_id = "12345"
    tool_use_data = MockToolUseData(
        tool_name="str-replace-editor",
        tool_use_id="tool-123",
        tool_output_is_error=False,
        tool_run_duration_ms=250,
        tool_input='{"command": "str_replace", "path": "test.py", "str_replace_entries": [{"old_str": "foo", "new_str": "bar", "old_str_start_line_number": 1, "old_str_end_line_number": 1}]}',
    )

    result = analyze_tool_call(request_id, tool_use_data)

    assert result.command == "str_replace"
    assert result.path == "test.py"
    assert result.str_replace_entries_count == 1


def test_analyze_tool_call_insert_command():
    """Test analyzing a tool call with insert command."""
    request_id = "12345"
    tool_use_data = MockToolUseData(
        tool_name="str-replace-editor",
        tool_use_id="tool-123",
        tool_output_is_error=False,
        tool_run_duration_ms=200,
        tool_input='{"command": "insert", "path": "test.py", "insert_line_entries": [{"insert_line": 5, "new_str": "new line"}]}',
    )

    result = analyze_tool_call(request_id, tool_use_data)

    assert result.command == "insert"
    assert result.path == "test.py"
    assert result.insert_line_entries_count == 1


def test_analyze_tool_call_with_error():
    """Test analyzing a tool call with error."""
    request_id = "12345"
    tool_use_data = MockToolUseData(
        tool_name="str-replace-editor",
        tool_use_id="tool-123",
        tool_output_is_error=True,
        tool_run_duration_ms=100,
        tool_input='{"command": "view", "path": "test.py"}',
        tool_output="Error: File not found",
    )

    result = analyze_tool_call(request_id, tool_use_data)

    assert result.is_error
    assert result.error_message == "File not found"


def test_collect_tool_call_stats():
    """Test collecting statistics for multiple tool calls."""
    tool_calls = [
        (
            "12345",
            MockToolUseData(
                tool_name="str-replace-editor",
                tool_use_id="tool-123",
                tool_output_is_error=False,
                tool_run_duration_ms=150,
                tool_input='{"command": "view", "path": "test.py"}',
            ),
        ),
        (
            "67890",
            MockToolUseData(
                tool_name="str-replace-editor",
                tool_use_id="tool-456",
                tool_output_is_error=True,
                tool_run_duration_ms=200,
                tool_input='{"command": "str_replace", "path": "test.py"}',
                tool_output="Error: Invalid parameters",
            ),
        ),
    ]

    results = collect_tool_call_stats(tool_calls)

    assert len(results) == 2
    assert results[0].request_id == "12345"
    assert results[0].command == "view"
    assert results[1].request_id == "67890"
    assert results[1].command == "str_replace"
    assert results[1].is_error


def test_calculate_aggregate_stats_empty():
    """Test calculating aggregate statistics with empty list."""
    stats_list = []
    result = calculate_aggregate_stats(stats_list)
    assert result == {}


def test_calculate_aggregate_stats():
    """Test calculating aggregate statistics."""
    stats_list = [
        ToolCallStats(
            request_id="12345",
            tool_name="str-replace-editor",
            tool_use_id="tool-123",
            is_error=False,
            duration_ms=150,
            command="view",
            path="test.py",
            view_range=[1, 10],
        ),
        ToolCallStats(
            request_id="67890",
            tool_name="str-replace-editor",
            tool_use_id="tool-456",
            is_error=True,
            duration_ms=200,
            command="str_replace",
            path="test.py",
            str_replace_entries_count=2,
            error_message="Invalid parameters",
        ),
        ToolCallStats(
            request_id="24680",
            tool_name="str-replace-editor",
            tool_use_id="tool-789",
            is_error=False,
            duration_ms=250,
            command="insert",
            path="other.py",
            insert_line_entries_count=3,
        ),
    ]

    result = calculate_aggregate_stats(stats_list)

    assert result["total_calls"] == 3
    assert result["success_calls"] == 2
    assert result["error_calls"] == 1
    assert result["success_rate"] == pytest.approx(2 / 3)
    assert result["command_counts"] == {"view": 1, "str_replace": 1, "insert": 1}
    assert result["avg_duration_ms"] == 200
    assert result["max_duration_ms"] == 250
    assert result["min_duration_ms"] == 150
    assert result["top_errors"][0] == ("Invalid parameters", 1)
    assert result["view_range_usage"] == 1
    assert result["avg_str_replace_entries"] == 2
    assert result["max_str_replace_entries"] == 2
    assert result["avg_insert_line_entries"] == 3
    assert result["max_insert_line_entries"] == 3


@patch("builtins.open", new_callable=mock_open)
def test_generate_html_report(mock_file):
    """Test generating HTML report."""
    stats = {
        "total_calls": 100,
        "success_calls": 80,
        "error_calls": 20,
        "success_rate": 0.8,
        "command_counts": {"view": 50, "str_replace": 30, "insert": 20},
        "top_paths": [("test.py", 30), ("other.py", 20)],
        "avg_duration_ms": 200,
        "max_duration_ms": 500,
        "min_duration_ms": 50,
        "top_errors": [("File not found", 10), ("Invalid parameters", 5)],
        "view_range_usage": 25,
        "avg_str_replace_entries": 2.5,
        "max_str_replace_entries": 5,
        "avg_insert_line_entries": 1.5,
        "max_insert_line_entries": 3,
    }

    output_path = "test_report.html"
    result = generate_html_report(stats, output_path)

    assert result == output_path
    mock_file.assert_called_once_with(output_path, "w")
    # Check that write was called (content is too long to check exactly)
    assert mock_file().write.called

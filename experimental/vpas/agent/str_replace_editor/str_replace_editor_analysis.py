"""
<PERSON><PERSON>t to analyze str-replace-editor tool calls and generate an HTML report with statistics.
"""

import argparse
import datetime
import json
import uuid
import difflib
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from collections import Counter
import re
from dataclasses import dataclass
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from ide_tool_call_analysis import (
    get_all_tool_calls_request_ids,
    get_tool_use_data,
    DOGFOOD_SHARD,
)

# Constants for web server configuration
WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"


@dataclass
class ToolCallStats:
    """Statistics for a single tool call."""

    request_id: str
    tool_name: str
    tool_use_id: str
    is_error: bool
    duration_ms: int
    command: Optional[str] = None
    path: Optional[str] = None
    view_range: Optional[List[int]] = None
    str_replace_entries_count: Optional[int] = None
    insert_line_entries_count: Optional[int] = None
    error_message: Optional[str] = None
    # New statistics
    view_range_size: Optional[int] = None
    str_replace_old_str_tokens: Optional[List[int]] = None
    str_replace_new_str_tokens: Optional[List[int]] = None
    str_replace_token_ratios: Optional[List[float]] = None
    insert_new_str_tokens: Optional[List[int]] = None
    # Schema conversion tracking
    old_format: bool = False


def parse_tool_input(tool_input_str: str) -> Dict[str, Any]:
    """Parse the tool input JSON string and convert old format to new format if needed.

    Args:
        tool_input_str: JSON string of the tool input

    Returns:
        Parsed tool input as a dictionary with converted format if needed
    """
    try:
        tool_input = json.loads(tool_input_str)

        # Check if we need to convert from old format to new format
        converted = False
        if (
            tool_input.get("command") == "str_replace"
            and "old_str" in tool_input
            and "str_replace_entries" not in tool_input
        ):
            # Convert old str_replace format to new format
            old_str = tool_input.get("old_str", "")
            new_str = tool_input.get("new_str", "")

            # Create a str_replace_entries array with a single entry
            tool_input["str_replace_entries"] = [
                {
                    "old_str": old_str,
                    "new_str": new_str,
                    # We don't have line numbers in the old format, but the analysis doesn't
                    # strictly need them as long as we're just counting entries
                    "old_str_start_line_number": 1,  # Placeholder
                    "old_str_end_line_number": 1 + old_str.count("\n"),  # Approximate
                }
            ]

            # Remove the old format fields
            tool_input.pop("old_str", None)
            tool_input.pop("new_str", None)
            converted = True

        elif (
            tool_input.get("command") == "insert"
            and "insert_line" in tool_input
            and "insert_line_entries" not in tool_input
        ):
            # Convert old insert format to new format
            insert_line = tool_input.get("insert_line", 0)
            new_str = tool_input.get("new_str", "")

            # Create an insert_line_entries array with a single entry
            tool_input["insert_line_entries"] = [
                {"insert_line": insert_line, "new_str": new_str}
            ]

            # Remove the old format fields
            tool_input.pop("insert_line", None)
            tool_input.pop("new_str", None)
            converted = True
            print("Converted old insert format to new format")

        # Add a flag to indicate if this was converted from old format
        if converted:
            tool_input["_converted_from_old_format"] = True

        return tool_input
    except json.JSONDecodeError:
        return {}


def extract_error_message(tool_output: str) -> Optional[str]:
    """Extract error message from tool output.

    Args:
        tool_output: The tool output string

    Returns:
        Extracted error message or None if no error found
    """
    if not tool_output:
        return None

    # Common error patterns
    error_patterns = [
        r"Error: (.*?)(?:\n|$)",
        r"Failed to edit the file.*?\n(.*?)(?:\n\n|$)",
        r"Invalid parameter.*?: (.*?)(?:\n|$)",
    ]

    for pattern in error_patterns:
        match = re.search(pattern, tool_output, re.DOTALL)
        if match:
            return match.group(1).strip()

    return None


def analyze_tool_call(request_id: str, tool_use_data: Any) -> ToolCallStats:
    """Analyze a single tool call and extract statistics.

    Args:
        request_id: The request ID
        tool_use_data: The tool use data object

    Returns:
        ToolCallStats object with extracted statistics
    """
    tool_input = parse_tool_input(tool_use_data.tool_input)
    token_counter = ClaudeTokenCounter()

    # Check if this was converted from old format
    old_format = tool_input.pop("_converted_from_old_format", False)

    stats = ToolCallStats(
        request_id=request_id,
        tool_name=tool_use_data.tool_name,
        tool_use_id=tool_use_data.tool_use_id,
        is_error=bool(tool_use_data.tool_output_is_error),
        duration_ms=tool_use_data.tool_run_duration_ms,
        command=tool_input.get("command"),
        path=tool_input.get("path"),
    )

    # Store if this was converted from old format
    stats.old_format = old_format

    # Extract command-specific details
    if stats.command == "view":
        stats.view_range = tool_input.get("view_range")
        # Calculate view range size if view_range is present
        if stats.view_range and len(stats.view_range) == 2:
            # If second value is -1, it means until the end of file, so we can't calculate size
            if stats.view_range[1] != -1 and stats.view_range[0] <= stats.view_range[1]:
                stats.view_range_size = stats.view_range[1] - stats.view_range[0] + 1

    elif stats.command == "str_replace":
        str_replace_entries = tool_input.get("str_replace_entries", [])
        if not isinstance(str_replace_entries, list):
            str_replace_entries = []

        stats.str_replace_entries_count = len(str_replace_entries)

        # Calculate token statistics for str_replace
        if str_replace_entries:
            old_str_tokens = []
            new_str_tokens = []
            token_ratios = []

            for entry in str_replace_entries:
                old_str = entry.get("old_str", "")
                new_str = entry.get("new_str", "")

                old_tokens = token_counter.count_tokens(old_str)
                new_tokens = token_counter.count_tokens(new_str)

                old_str_tokens.append(old_tokens)
                new_str_tokens.append(new_tokens)

                # Calculate ratio using unified diff to identify actual changes
                old_lines = old_str.splitlines() or [""]
                new_lines = new_str.splitlines() or [""]

                # Generate unified diff
                diff = list(difflib.unified_diff(old_lines, new_lines, n=0))

                # Extract changed lines (lines starting with '+' excluding the header lines)
                changed_lines = [
                    line[1:]
                    for line in diff
                    if line.startswith("+") and not line.startswith("+++")
                ]

                # Count tokens in changed lines
                changed_tokens = sum(
                    token_counter.count_tokens(line) for line in changed_lines
                )

                # Calculate ratio: tokens in changed lines / tokens in new_str
                if new_tokens > 0:
                    ratio = changed_tokens / new_tokens
                    token_ratios.append(ratio)
                else:
                    # If new_str is empty, the ratio is 0 (nothing new)
                    token_ratios.append(0.0)

            stats.str_replace_old_str_tokens = old_str_tokens
            stats.str_replace_new_str_tokens = new_str_tokens
            stats.str_replace_token_ratios = token_ratios

    elif stats.command == "insert":
        insert_line_entries = tool_input.get("insert_line_entries", [])
        if not isinstance(insert_line_entries, list):
            insert_line_entries = []

        stats.insert_line_entries_count = len(insert_line_entries)

        # Calculate token statistics for insert
        if insert_line_entries:
            new_str_tokens = []

            for entry in insert_line_entries:
                new_str = entry.get("new_str", "")
                new_tokens = token_counter.count_tokens(new_str)
                new_str_tokens.append(new_tokens)

            stats.insert_new_str_tokens = new_str_tokens

    # Extract error message if present
    if stats.is_error and hasattr(tool_use_data, "tool_output"):
        stats.error_message = extract_error_message(tool_use_data.tool_output)

    return stats


def collect_tool_call_stats(tool_calls: List[Tuple[str, Any]]) -> List[ToolCallStats]:
    """Collect statistics for multiple tool calls.

    Args:
        tool_calls: List of (request_id, tool_use_data) tuples

    Returns:
        List of ToolCallStats objects
    """
    stats_list = []
    for request_id, tool_use_data in tqdm(
        tool_calls, desc="Analyzing tool calls", unit="call"
    ):
        try:
            stats = analyze_tool_call(request_id, tool_use_data)
            stats_list.append(stats)
        except Exception as e:
            print(f"\nError analyzing tool call {request_id}: {e}")

    return stats_list


def calculate_aggregate_stats(stats_list: List[ToolCallStats]) -> Dict[str, Any]:
    """Calculate aggregate statistics from a list of tool call stats.

    Args:
        stats_list: List of ToolCallStats objects

    Returns:
        Dictionary of aggregate statistics
    """
    if not stats_list:
        return {}

    # Track schema conversion statistics
    old_schema_count = sum(
        1 for s in stats_list if hasattr(s, "old_format") and s.old_format
    )

    total_calls = len(stats_list)
    error_calls = sum(1 for s in stats_list if s.is_error)
    success_calls = total_calls - error_calls

    # Command distribution
    command_counts = Counter(s.command for s in stats_list if s.command)

    # We're no longer using path statistics in the report

    # Duration statistics
    durations = [s.duration_ms for s in stats_list]
    avg_duration = sum(durations) / len(durations) if durations else 0
    max_duration = max(durations) if durations else 0
    min_duration = min(durations) if durations else 0

    # Error analysis
    error_messages = Counter(
        s.error_message for s in stats_list if s.is_error and s.error_message
    )
    top_errors = error_messages.most_common(10)

    # Command-specific stats
    # View command stats
    view_range_usage = sum(
        1 for s in stats_list if s.command == "view" and s.view_range
    )

    view_range_sizes = [
        s.view_range_size
        for s in stats_list
        if s.command == "view" and s.view_range_size is not None
    ]
    avg_view_range_size = (
        sum(view_range_sizes) / len(view_range_sizes) if view_range_sizes else 0
    )
    max_view_range_size = max(view_range_sizes) if view_range_sizes else 0

    # Str_replace command stats
    str_replace_entries_counts = [
        s.str_replace_entries_count
        for s in stats_list
        if s.command == "str_replace" and s.str_replace_entries_count is not None
    ]
    avg_str_replace_entries = (
        sum(str_replace_entries_counts) / len(str_replace_entries_counts)
        if str_replace_entries_counts
        else 0
    )
    max_str_replace_entries = (
        max(str_replace_entries_counts) if str_replace_entries_counts else 0
    )

    # Token statistics for str_replace
    all_old_str_tokens = []
    all_new_str_tokens = []
    all_token_ratios = []

    for s in stats_list:
        if s.command == "str_replace":
            if s.str_replace_old_str_tokens:
                all_old_str_tokens.extend(s.str_replace_old_str_tokens)
            if s.str_replace_new_str_tokens:
                all_new_str_tokens.extend(s.str_replace_new_str_tokens)
            if s.str_replace_token_ratios:
                all_token_ratios.extend(s.str_replace_token_ratios)

    avg_old_str_tokens = (
        sum(all_old_str_tokens) / len(all_old_str_tokens) if all_old_str_tokens else 0
    )
    avg_new_str_tokens = (
        sum(all_new_str_tokens) / len(all_new_str_tokens) if all_new_str_tokens else 0
    )
    avg_token_ratio = (
        sum(all_token_ratios) / len(all_token_ratios) if all_token_ratios else 0
    )

    # Insert command stats
    insert_line_entries_counts = [
        s.insert_line_entries_count
        for s in stats_list
        if s.command == "insert" and s.insert_line_entries_count is not None
    ]
    avg_insert_line_entries = (
        sum(insert_line_entries_counts) / len(insert_line_entries_counts)
        if insert_line_entries_counts
        else 0
    )
    max_insert_line_entries = (
        max(insert_line_entries_counts) if insert_line_entries_counts else 0
    )

    # Token statistics for insert
    all_insert_tokens = []

    for s in stats_list:
        if s.command == "insert" and s.insert_new_str_tokens:
            all_insert_tokens.extend(s.insert_new_str_tokens)

    avg_insert_tokens = (
        sum(all_insert_tokens) / len(all_insert_tokens) if all_insert_tokens else 0
    )

    return {
        "total_calls": total_calls,
        "success_calls": success_calls,
        "error_calls": error_calls,
        "success_rate": (success_calls / total_calls) if total_calls else 0,
        "command_counts": dict(command_counts),
        "avg_duration_ms": avg_duration,
        "max_duration_ms": max_duration,
        "min_duration_ms": min_duration,
        "top_errors": top_errors,
        # Schema conversion stats
        "old_schema_count": old_schema_count,
        "old_schema_percentage": (old_schema_count / total_calls) if total_calls else 0,
        # View command stats
        "view_range_usage": view_range_usage,
        "avg_view_range_size": avg_view_range_size,
        "max_view_range_size": max_view_range_size,
        # Str_replace command stats
        "avg_str_replace_entries": avg_str_replace_entries,
        "max_str_replace_entries": max_str_replace_entries,
        "avg_old_str_tokens": avg_old_str_tokens,
        "avg_new_str_tokens": avg_new_str_tokens,
        "avg_token_ratio": avg_token_ratio,
        # Insert command stats
        "avg_insert_line_entries": avg_insert_line_entries,
        "max_insert_line_entries": max_insert_line_entries,
        "avg_insert_tokens": avg_insert_tokens,
    }


def generate_html_report(
    stats: Dict[str, Any],
    output_path: str = "str_replace_editor_report.html",
    tool_name: str = "str-replace-editor",
    tenant_name: str = DOGFOOD_SHARD,
    limit: int = 1000,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> str:
    """Generate an HTML report from the aggregate statistics.

    Args:
        stats: Dictionary of aggregate statistics
        output_path: Path to save the HTML report
        tool_name: Name of the tool being analyzed
        tenant_name: Name of the tenant being queried
        limit: Maximum number of tool calls analyzed
        from_datetime: Start datetime for the analysis period
        to_datetime: End datetime for the analysis period

    Returns:
        Path to the generated HTML report
    """
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{tool_name} Tool Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #333; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .stat-box {{ background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
            .success {{ color: green; }}
            .error {{ color: red; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>{tool_name} Tool Analysis Report</h1>
            <p>Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

            <div class="stat-box">
                <h2>Analysis Parameters</h2>
                <p>Tool: <strong>{tool_name}</strong></p>
                <p>Tenant: <strong>{tenant_name}</strong></p>
                <p>Limit: <strong>{limit}</strong></p>
                <p>From Date: <strong>{from_datetime.strftime('%Y-%m-%d') if from_datetime else 'N/A'}</strong></p>
                <p>To Date: <strong>{to_datetime.strftime('%Y-%m-%d') if to_datetime else 'Now'}</strong></p>
            </div>

            <div class="stat-box">
                <h2>Overview</h2>
                <p>Total Calls: <strong>{stats.get('total_calls', 0)}</strong></p>
                <p>Success Rate: <strong class="success">{stats.get('success_rate', 0):.2%}</strong></p>
                <p>Successful Calls: <strong class="success">{stats.get('success_calls', 0)}</strong></p>
                <p>Failed Calls: <strong class="error">{stats.get('error_calls', 0)}</strong></p>
                <p>Average Duration: <strong>{stats.get('avg_duration_ms', 0):.2f} ms</strong></p>
                <p>Min Duration: <strong>{stats.get('min_duration_ms', 0)} ms</strong></p>
                <p>Max Duration: <strong>{stats.get('max_duration_ms', 0)} ms</strong></p>
                <p>Old Schema Format: <strong>{stats.get('old_schema_count', 0)} calls ({stats.get('old_schema_percentage', 0):.2%})</strong></p>
            </div>

            <div class="stat-box">
                <h2>Command Distribution</h2>
                <table>
                    <tr>
                        <th>Command</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
    """

    command_counts = stats.get("command_counts", {})
    total_commands = sum(command_counts.values())

    for command, count in command_counts.items():
        percentage = (count / total_commands) if total_commands else 0
        html_content += f"""
                    <tr>
                        <td>{command}</td>
                        <td>{count}</td>
                        <td>{percentage:.2%}</td>
                    </tr>
        """

    html_content += """
                </table>
            </div>



            <div class="stat-box">
                <h2>Command-Specific Statistics</h2>

                <h3>View Command</h3>
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>View Range Usage</td>
                        <td><strong>{view_range_usage}</strong></td>
                    </tr>
                    <tr>
                        <td>Average View Range Size</td>
                        <td><strong>{avg_view_range_size:.2f}</strong> lines</td>
                    </tr>
                    <tr>
                        <td>Max View Range Size</td>
                        <td><strong>{max_view_range_size}</strong> lines</td>
                    </tr>
                </table>

                <h3>Str Replace Command</h3>
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Average Entries per Call</td>
                        <td><strong>{avg_str_replace_entries:.2f}</strong></td>
                    </tr>
                    <tr>
                        <td>Max Entries in a Single Call</td>
                        <td><strong>{max_str_replace_entries}</strong></td>
                    </tr>
                    <tr>
                        <td>Average Tokens in Old String</td>
                        <td><strong>{avg_old_str_tokens:.2f}</strong></td>
                    </tr>
                    <tr>
                        <td>Average Tokens in New String</td>
                        <td><strong>{avg_new_str_tokens:.2f}</strong></td>
                    </tr>
                    <tr>
                        <td>Average New Code Ratio</td>
                        <td><strong>{avg_token_ratio:.2%}</strong></td>
                    </tr>
                </table>
                <p><em>Note: New Code Ratio = tokens in changed lines / tokens in new_str</em></p>

                <h3>Insert Command</h3>
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Average Entries per Call</td>
                        <td><strong>{avg_insert_line_entries:.2f}</strong></td>
                    </tr>
                    <tr>
                        <td>Max Entries in a Single Call</td>
                        <td><strong>{max_insert_line_entries}</strong></td>
                    </tr>
                    <tr>
                        <td>Average Tokens per Insert</td>
                        <td><strong>{avg_insert_tokens:.2f}</strong></td>
                    </tr>
                </table>
            </div>
    """.format(
        view_range_usage=stats.get("view_range_usage", 0),
        avg_view_range_size=stats.get("avg_view_range_size", 0),
        max_view_range_size=stats.get("max_view_range_size", 0),
        avg_str_replace_entries=stats.get("avg_str_replace_entries", 0),
        max_str_replace_entries=stats.get("max_str_replace_entries", 0),
        avg_old_str_tokens=stats.get("avg_old_str_tokens", 0),
        avg_new_str_tokens=stats.get("avg_new_str_tokens", 0),
        avg_token_ratio=stats.get("avg_token_ratio", 0),
        avg_insert_line_entries=stats.get("avg_insert_line_entries", 0),
        max_insert_line_entries=stats.get("max_insert_line_entries", 0),
        avg_insert_tokens=stats.get("avg_insert_tokens", 0),
    )

    html_content += """
            <div class="stat-box">
                <h2>Top Error Messages</h2>
                <table>
                    <tr>
                        <th>Error Message</th>
                        <th>Count</th>
                    </tr>
    """

    for error_msg, count in stats.get("top_errors", []):
        html_content += f"""
                    <tr>
                        <td>{error_msg}</td>
                        <td>{count}</td>
                    </tr>
        """

    html_content += """
                </table>
            </div>

            <script>

            </script>
        </div>
    </body>
    </html>
    """

    with open(output_path, "w") as f:
        f.write(html_content)

    return output_path


def fetch_and_analyze_tool_calls(
    tool_name: str = "str-replace-editor",
    tenant_name: str = DOGFOOD_SHARD,
    limit: int = 1000,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
    output_path: Optional[str] = None,
    use_web_server: bool = True,
    num_parallel_fetches: int = 20,
) -> str:
    """Fetch tool calls, analyze them, and generate an HTML report.

    Args:
        tool_name: The name of the tool to analyze
        tenant_name: The tenant name to query for
        limit: Maximum number of tool calls to analyze
        from_datetime: Optional start datetime to filter results
        to_datetime: Optional end datetime to filter results
        output_path: Path to save the HTML report (if use_web_server is False)
        use_web_server: Whether to save the report to the web server directory (default: True)
        num_parallel_fetches: Number of parallel requests to make when fetching tool use data (default: 20)

    Returns:
        If use_web_server is True, returns the URL to the report
        If use_web_server is False, returns the local path to the report
    """
    # Fetch all tool call request IDs
    print(f"Fetching request IDs for {tool_name}...")
    with tqdm(total=1, desc="Running BigQuery", unit="query") as pbar:
        request_ids = get_all_tool_calls_request_ids(
            tool_name=tool_name,
            tenant_name=tenant_name,
            limit=limit,
            from_datetime=from_datetime,
            to_datetime=to_datetime,
        )
        pbar.update(1)

    print(f"Found {len(request_ids)} request IDs")

    # Fetch tool use data for each request ID in parallel
    print("Fetching tool use data...")
    tool_calls = []

    # Function to fetch a single request's tool use data
    def fetch_tool_use_data(request_id):
        try:
            return get_tool_use_data(request_id, tenant_name)
        except Exception as e:
            print(f"\nError fetching tool use data for request ID {request_id}: {e}")
            return None

    # Use ThreadPoolExecutor for parallel fetching

    with ThreadPoolExecutor(max_workers=num_parallel_fetches) as executor:
        # Submit all tasks and create a map of future to request_id for error reporting
        futures = list(
            tqdm(
                executor.map(fetch_tool_use_data, request_ids),
                total=len(request_ids),
                desc="Fetching tool use data",
                unit="request",
            )
        )

        # Filter out None results (failed fetches)
        tool_calls = [result for result in futures if result is not None]

    print(f"Successfully fetched data for {len(tool_calls)} tool calls")

    # Analyze tool calls
    stats_list = collect_tool_call_stats(tool_calls)

    # Calculate aggregate statistics
    print("Calculating aggregate statistics...")
    with tqdm(total=1, desc="Calculating statistics", unit="stats") as pbar:
        aggregate_stats = calculate_aggregate_stats(stats_list)
        pbar.update(1)

    # Generate HTML report
    print("Generating HTML report...")
    with tqdm(total=1, desc="Generating HTML report", unit="report") as pbar:
        if use_web_server:
            # Create a unique directory name based on timestamp and tool name
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            random_suffix = str(uuid.uuid4())[:8]
            rel_path = f"vpas/str_replace_editor_analysis/{tool_name}_{timestamp}_{random_suffix}"

            # Create directory in web server
            web_dir = WEB_SERVER_DIR / rel_path
            web_dir.mkdir(parents=True, exist_ok=True)

            # Set output path to web server directory
            web_output_path = web_dir / "report.html"
            report_path = generate_html_report(
                stats=aggregate_stats,
                output_path=str(web_output_path),
                tool_name=tool_name,
                tenant_name=tenant_name,
                limit=limit,
                from_datetime=from_datetime,
                to_datetime=to_datetime,
            )

            # Generate URL
            url = URL_TEMPLATE.format(f"{rel_path}/report.html")
            pbar.update(1)
            print(f"Report generated at: {url}")
            return url
        else:
            # Use provided output path or default
            if output_path is None:
                output_path = "str_replace_editor_report.html"
            report_path = generate_html_report(
                stats=aggregate_stats,
                output_path=output_path,
                tool_name=tool_name,
                tenant_name=tenant_name,
                limit=limit,
                from_datetime=from_datetime,
                to_datetime=to_datetime,
            )
            pbar.update(1)
            print(f"Report generated at: {report_path}")
            return report_path


def parse_date(date_str):
    """Parse date string in YYYY-MM-DD format."""
    try:
        return datetime.datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise argparse.ArgumentTypeError(
            f"Invalid date format: {date_str}. Use YYYY-MM-DD"
        )


def main():
    """Main function to run the str-replace-editor tool analysis."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Analyze str-replace-editor tool calls and generate an HTML report",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""Examples:
  # Analyze last 7 days of data with default settings
  python str_replace_editor_analysis.py

  # Analyze data from a specific date range with a limit
  python str_replace_editor_analysis.py --from-date 2023-01-01 --to-date 2023-01-31 --limit 500

  # Analyze data for a specific tenant
  python str_replace_editor_analysis.py --tenant custom-tenant-name

  # Speed up data fetching with more parallel requests
  python str_replace_editor_analysis.py --num-parallel-fetches 50

The HTML report will be saved to the web server and a URL will be provided.""",
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=1000,
        help="Maximum number of tool calls to analyze (default: 1000)",
    )

    parser.add_argument(
        "--from-date",
        type=parse_date,
        default=(datetime.datetime.now() - datetime.timedelta(days=7)),
        help="Start date for analysis in YYYY-MM-DD format (default: 7 days ago)",
    )

    parser.add_argument(
        "--to-date",
        type=parse_date,
        default=None,
        help="End date for analysis in YYYY-MM-DD format (default: now)",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default=DOGFOOD_SHARD,
        help=f"Tenant name to query for (default: {DOGFOOD_SHARD})",
    )

    parser.add_argument(
        "--tool",
        type=str,
        default="str-replace-editor",
        help="Tool name to analyze (default: str-replace-editor)",
    )

    parser.add_argument(
        "--num-parallel-fetches",
        type=int,
        default=20,
        help="Number of parallel requests to make when fetching tool use data (default: 20)",
    )

    args = parser.parse_args()

    print("Starting analysis with the following parameters:")
    print(f"  Tool: {args.tool}")
    print(f"  Limit: {args.limit}")
    print(f"  From date: {args.from_date}")
    print(f"  To date: {args.to_date or 'now'}")
    print(f"  Tenant: {args.tenant}")
    print(f"  Parallel fetches: {args.num_parallel_fetches}")

    url = fetch_and_analyze_tool_calls(
        tool_name=args.tool,
        tenant_name=args.tenant,
        limit=args.limit,
        from_datetime=args.from_date,
        to_datetime=args.to_date,
        use_web_server=True,  # Always use web server
        num_parallel_fetches=args.num_parallel_fetches,
    )

    print(f"\nAnalysis complete! Report available at: {url}")


if __name__ == "__main__":
    main()

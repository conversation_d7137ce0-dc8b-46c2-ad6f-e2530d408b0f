#!/usr/bin/env python3
"""
<PERSON><PERSON>t to run the str-replace-editor tool analysis and generate an HTML report.
"""

import argparse
import datetime
import os
from pathlib import Path
from str_replace_editor_analysis import fetch_and_analyze_tool_calls


def parse_date(date_str):
    """Parse date string in YYYY-MM-DD format."""
    try:
        return datetime.datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise argparse.ArgumentTypeError(
            f"Invalid date format: {date_str}. Use YYYY-MM-DD"
        )


def main():
    parser = argparse.ArgumentParser(
        description="Analyze str-replace-editor tool calls and generate an HTML report"
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=1000,
        help="Maximum number of tool calls to analyze (default: 1000)",
    )

    parser.add_argument(
        "--from-date",
        type=parse_date,
        default=(datetime.datetime.now() - datetime.timedelta(days=7)),
        help="Start date for analysis in YYYY-MM-DD format (default: 7 days ago)",
    )

    parser.add_argument(
        "--to-date",
        type=parse_date,
        default=None,
        help="End date for analysis in YYYY-MM-DD format (default: now)",
    )

    parser.add_argument(
        "--output",
        type=str,
        default=None,
        help="Output path for the HTML report (default: auto-generated in web server directory)",
    )

    parser.add_argument(
        "--local",
        action="store_true",
        help="Save report locally instead of to the web server",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default="dogfood-shard",
        help="Tenant name to query for (default: dogfood-shard)",
    )

    args = parser.parse_args()

    print("Starting analysis with the following parameters:")
    print(f"  Limit: {args.limit}")
    print(f"  From date: {args.from_date}")
    print(f"  To date: {args.to_date or 'now'}")
    print(f"  Output mode: {'Local file' if args.local else 'Web server'}")
    if args.local:
        print(f"  Output path: {args.output or 'str_replace_editor_report.html'}")
    print(f"  Tenant: {args.tenant}")

    result = fetch_and_analyze_tool_calls(
        tool_name="str-replace-editor",
        tenant_name=args.tenant,
        limit=args.limit,
        from_datetime=args.from_date,
        to_datetime=args.to_date,
        output_path=args.output,
        use_web_server=not args.local,
    )

    print(f"\nAnalysis complete! Report available at: {result}")


if __name__ == "__main__":
    main()

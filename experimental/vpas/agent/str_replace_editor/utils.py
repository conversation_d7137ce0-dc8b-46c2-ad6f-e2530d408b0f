import re
from dataclasses import dataclass
from typing import Any
from experimental.vpas.utils.dict_utils import (
    extract_indexed_entries,
    IndexedEntriesExtractionResult,
)


DEFAULT_STR_REPLACE_PARAMS = [
    "old_str",
    "new_str",
    "old_str_start_line_number",
    "old_str_end_line_number",
]

LINE_BUDGET_PARAMS = [
    "old_str_num_lines",
    "new_str_num_lines",
    "old_str_num_lines_plus_new_str_num_lines",
    "edit_lines_budget_remaining",
    "can_generate_next_entry",
]

TOKEN_BUDGET_PARAMS = [
    "old_str_num_tokens",
    "new_str_num_tokens",
    "old_str_num_tokens_plus_new_str_num_tokens",
    "edit_tokens_budget_remaining",
    "can_generate_next_entry",
]


def extract_str_replace_entries_from_flat_input(
    tool_input: dict[str, Any], additional_params: list[str] = []
) -> IndexedEntriesExtractionResult:
    all_params = DEFAULT_STR_REPLACE_PARAMS + additional_params
    assert tool_input["command"] == "str_replace"
    assert "str_replace_entries" not in tool_input
    entries_dict = dict(tool_input)
    entries_dict.pop("command", None)
    entries_dict.pop("path", None)
    entries_dict.pop("edit_lines_budget_initial", None)
    entries_dict.pop("edit_tokens_budget_initial", None)
    return extract_indexed_entries(entries_dict, all_params)


@dataclass
class StrReplaceEntryResult:
    success: bool
    parsing_success: bool
    original_snippet_lines: list[str]


@dataclass
class StrReplaceToolResult:
    all_success: bool
    results: list[StrReplaceEntryResult]


def parse_str_replace_tool_result(tool_result: str) -> StrReplaceToolResult:
    """
    Parse the result of a str_replace tool call to determine success/failure and extract information.

    Args:
        tool_result: The string output from the str_replace tool call

    Returns:
        StrReplaceToolResult object containing success status and results for each entry
    """
    # Check if the operation was successful overall
    all_success = "Successfully edited the file" in tool_result

    # Extract results for individual entries
    results = []

    # Split the tool result by entry
    entry_results = []
    if "Result for str_replace_entries[" in tool_result:
        # Handle nested schema format
        pattern = r"Result for str_replace_entries\[(\d+)\]:(.*?)(?=Result for str_replace_entries\[\d+\]:|Fix failed str_replace_entries|$)"
        matches = list(re.finditer(pattern, tool_result, re.DOTALL))
        for match in matches:
            entry_results.append(match.group(2).strip())
    elif "Result for str_replace for entry with index" in tool_result:
        # Handle flat schema format
        pattern = r"Result for str_replace for entry with index \[(\d+)\]:(.*?)(?=Result for str_replace for entry with index|\Z|Fix failed str_replace)"
        matches = list(re.finditer(pattern, tool_result, re.DOTALL))
        for match in matches:
            entry_results.append(match.group(2).strip())
    else:
        # Handle single entry format
        entry_results = [tool_result]

    for entry_result in entry_results:
        success = (
            "Successfully" in entry_result
            and "No replacement was performed" not in entry_result
        )

        parsing_success = True

        # Extract the original snippet lines if available
        original_snippet_lines = []
        snippet_match = re.search(
            r"The content in the specified region is:(.*?)(?=Diff between oldStr|Fix failed|$)",
            entry_result,
            re.DOTALL,
        )

        if snippet_match:
            # Extract and clean up the snippet lines
            snippet_text = snippet_match.group(1).strip()
            for line in snippet_text.split("\n"):
                # Remove line numbers (assuming format like "     1\t{{")
                line_match = re.match(r"\s*\d+\t(.*)", line)
                if line_match:
                    original_snippet_lines.append(line_match.group(1))
                else:
                    parsing_success = False
                    break
        else:
            parsing_success = False

        results.append(
            StrReplaceEntryResult(
                success=success,
                parsing_success=parsing_success,
                original_snippet_lines=original_snippet_lines,
            )
        )

    return StrReplaceToolResult(all_success=all_success, results=results)

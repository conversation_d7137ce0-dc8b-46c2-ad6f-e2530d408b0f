import unittest
from experimental.vpas.agent.str_replace_editor.utils import (
    parse_str_replace_tool_result,
)


class TestParseStrReplaceToolResult(unittest.TestCase):
    def test_parse_failure_result(self):
        # Test with the example provided in the request
        tool_result = """Failed to edit the file models/final/product_events.sql. See below for details.
Result for str_replace_entries[0]:
No replacement was performed, oldStr did not appear verbatim in models/final/product_events.sql.
The content in the specified region is:
     1	{{
     2	  config(
     3	    materialized='table'
     4	  )
     5	}}}

Diff between oldStr and the specified region is:
===================================================================
--- oldStr
+++ regionContent
@@ -1,1 +1,1 @@
-{{{
+{{


Fix failed str_replace_entries accordingly and try again.
"""
        result = parse_str_replace_tool_result(tool_result)

        # Verify the overall success status
        self.assertFalse(result.all_success)

        # Verify the individual entry results
        self.assertEqual(len(result.results), 1)
        self.assertFalse(result.results[0].success)

        # Verify the extracted original snippet lines
        expected_lines = ["{{", "  config(", "    materialized='table'", "  )", "}}}"]
        self.assertEqual(result.results[0].original_snippet_lines, expected_lines)

    def test_parse_success_result(self):
        # Test with a successful result
        tool_result = """Successfully edited the file models/final/product_events.sql.
Result for str_replace_entries[0]:
Successfully replaced oldStr with newStr.
Edited section after IDE auto-formatting was applied:
     1	{{
     2	  config(
     3	    materialized='table'
     4	  )
     5	}}

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
"""
        result = parse_str_replace_tool_result(tool_result)

        # Verify the overall success status
        self.assertTrue(result.all_success)

        # Verify the individual entry results
        self.assertEqual(len(result.results), 1)
        self.assertTrue(result.results[0].success)

        # No original snippet lines should be extracted in this case
        self.assertEqual(result.results[0].original_snippet_lines, [])

    def test_parse_multiple_entries(self):
        # Test with multiple entries
        tool_result = """Partially edited the file models/final/product_events.sql. See below for details.
Result for str_replace for entry with index [0]:
Successfully replaced oldStr with newStr.
Edited section after IDE auto-formatting was applied:
     1	{{
     2	  config(
     3	    materialized='table'
     4	  )
     5	}}

Result for str_replace for entry with index [1]:
No replacement was performed, oldStr did not appear verbatim in models/final/product_events.sql.
The content in the specified region is:
    10	SELECT
    11	  id,
    12	  event_type
    13	FROM events

Diff between oldStr and the specified region is:
===================================================================
--- oldStr
+++ regionContent
@@ -1,3 +1,4 @@
 SELECT
   id,
-  type
+  event_type
+FROM events

Fix failed str_replace entries accordingly and try again.
"""
        result = parse_str_replace_tool_result(tool_result)

        # Verify the overall success status
        self.assertFalse(result.all_success)

        # Verify the individual entry results
        self.assertEqual(len(result.results), 2)
        self.assertTrue(result.results[0].success)
        self.assertFalse(result.results[1].success)

        # Verify the extracted original snippet lines for the second entry
        expected_lines = ["SELECT", "  id,", "  event_type", "FROM events"]
        self.assertEqual(result.results[1].original_snippet_lines, expected_lines)

    def test_parse_nested_schema(self):
        # Test with nested schema format
        tool_result = """Failed to edit the file models/final/product_events.sql. See below for details.
Result for str_replace_entries[0]:
No replacement was performed, oldStr did not appear verbatim in models/final/product_events.sql.
The content in the specified region is:
     1	{{
     2	  config(
     3	    materialized='table'
     4	  )
     5	}}}

Result for str_replace_entries[1]:
No replacement was performed, oldStr did not appear verbatim in models/final/product_events.sql.
The content in the specified region is:
    10	SELECT *
    11	FROM users

Fix failed str_replace_entries accordingly and try again.
"""
        result = parse_str_replace_tool_result(tool_result)

        # Verify the overall success status
        self.assertFalse(result.all_success)

        # Verify the individual entry results
        self.assertEqual(len(result.results), 2)
        self.assertFalse(result.results[0].success)
        self.assertFalse(result.results[1].success)

        # Verify the extracted original snippet lines
        expected_lines_1 = ["{{", "  config(", "    materialized='table'", "  )", "}}}"]
        expected_lines_2 = ["SELECT *", "FROM users"]
        self.assertEqual(result.results[0].original_snippet_lines, expected_lines_1)
        self.assertEqual(result.results[1].original_snippet_lines, expected_lines_2)


if __name__ == "__main__":
    unittest.main()

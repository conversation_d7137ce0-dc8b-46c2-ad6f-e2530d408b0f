{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.third_party_clients.token_counter.token_counter import (\n", "    TokenizerBasedTokenCounter,\n", ")\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "qwen_token_counter = TokenizerBasedTokenCounter(Qwen25CoderTokenizer())\n", "claude_token_counter = ClaudeTokenCounter()\n", "counter = claude_token_counter\n", "\n", "inputs = {\n", "    \"command\": \"str_replace\",\n", "    \"path\": \"clients/common/webviews/src/apps/chat/components/conversation/blocks/tools/headers/ToolUseHeader.svelte\",\n", "    \"old_str\": '<script lang=\"ts\">\\n  import TextAugment from \"$common-webviews/src/design-system/components/TextAugment.svelte\";\\n  import CaretSort from \"$common-webviews/src/design-system/icons/caret-sort.svelte\";\\n  import Collapse from \"$common-webviews/src/design-system/icons/augment/collapse.svelte\";\\n  import TextTooltipAugment from \"$common-webviews/src/design-system/components/TextTooltipAugment.svelte\";\\n  import { TooltipTriggerOn } from \"$common-webviews/src/design-system/_primitives/TooltipAugment/types\";\\n  import { getHoverContext } from \"$common-webviews/src/common/actions/onHoverAction\";\\n  import { getCollapsibleContext } from \"$common-webviews/src/design-system/components/CollapsibleAugment/context\";\\n\\n  export let toolName: string | undefined = undefined;\\n  export let toolUseInput: Record<string, any>;\\n\\n  function formatValue(value: any): string {\\n    if (value === undefined) {\\n      return \"\";\\n    }\\n    if (typeof value === \"object\") {\\n      return Object.values(value).join(\" \");\\n    }\\n\\n    return value;\\n  }\\n  let referenceClientRect: HTMLElement | undefined = undefined;\\n\\n  function formatInputArgs(records: Record<string, any>): string {\\n    let result: string[] = [];\\n    for (const value of Object.values(records)) {\\n      if (Array.isArray(value)) {\\n        result = result.concat(value.map((v) => formatValue(v)));\\n      } else {\\n        result.push(formatValue(value));\\n      }\\n    }\\n    return result.join(\" \");\\n  }\\n  const hover = getHoverContext();\\n  const collapsed = getCollapsibleContext().collapsed;',\n", "    \"new_str\": '<script lang=\"ts\">\\n  import TextAugment from \"$common-webviews/src/design-system/components/TextAugment.svelte\";\\n  import CaretSort from \"$common-webviews/src/design-system/icons/caret-sort.svelte\";\\n  import Collapse from \"$common-webviews/src/design-system/icons/augment/collapse.svelte\";\\n  import TextTooltipAugment from \"$common-webviews/src/design-system/components/TextTooltipAugment.svelte\";\\n  import { TooltipTriggerOn } from \"$common-webviews/src/design-system/_primitives/TooltipAugment/types\";\\n  import { getHoverContext } from \"$common-webviews/src/common/actions/onHoverAction\";\\n  import { getCollapsibleContext } from \"$common-webviews/src/design-system/components/CollapsibleAugment/context\";\\n\\n  export let toolName: string | undefined = undefined;\\n  export let toolUseInput: Record<string, any>;\\n  export let showOutput: boolean = false;\\n  export let toolOutput: string | undefined = undefined;\\n\\n  function formatValue(value: any): string {\\n    if (value === undefined) {\\n      return \"\";\\n    }\\n    if (typeof value === \"object\") {\\n      return Object.values(value).join(\" \");\\n    }\\n\\n    return value;\\n  }\\n  let referenceClientRect: HTMLElement | undefined = undefined;\\n\\n  function formatInputArgs(records: Record<string, any>): string {\\n    let result: string[] = [];\\n    for (const value of Object.values(records)) {\\n      if (Array.isArray(value)) {\\n        result = result.concat(value.map((v) => formatValue(v)));\\n      } else {\\n        result.push(formatValue(value));\\n      }\\n    }\\n    return result.join(\" \");\\n  }\\n  const hover = getHoverContext();\\n  const collapsed = getCollapsibleContext().collapsed;',\n", "}\n", "\n", "text = inputs[\"old_str\"]\n", "token_count = counter.count_tokens(text)\n", "print(f\"Char count: {len(text)}\")\n", "print(f\"Token count: {token_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
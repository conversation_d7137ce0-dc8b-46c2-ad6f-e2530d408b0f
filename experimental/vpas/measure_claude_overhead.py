#!/usr/bin/env python3

import time
import statistics
from dataclasses import dataclass
from research.llm_apis.llm_client import AnthropicDirectClient, TextPrompt, TextResult


@dataclass
class LatencyMetrics:
    time_to_first_token: float
    input_tokens: int
    output_tokens: int
    total_time: float

    @property
    def input_tokens_per_second(self) -> float:
        return (
            self.input_tokens / self.time_to_first_token
            if self.time_to_first_token > 0
            else 0
        )

    @property
    def output_tokens_per_second(self) -> float:
        generation_time = self.total_time - self.time_to_first_token
        return self.output_tokens / generation_time if generation_time > 0 else 0


def measure_latency(
    client: AnthropicDirectClient,
    system_prompt: str,
    messages: list[dict],
    output_tokens: int = 100,
) -> LatencyMetrics:
    """Measure various latency metrics."""
    start_time = time.time()

    # Convert messages to format expected by AnthropicDirectClient
    augment_messages = []
    for msg in messages:
        if msg["role"] == "user":
            augment_messages.append([TextPrompt(text=msg["content"])])
        elif msg["role"] == "assistant":
            augment_messages.append([TextResult(text=msg["content"])])

    # First get time to first token with a minimal response
    response, metadata = client.generate(
        messages=augment_messages,
        max_tokens=1,  # We only need first token for initial latency
        system_prompt=system_prompt,
        temperature=0.0,
    )
    time_to_first = time.time() - start_time

    # Now get full response to measure generation speed
    start_time = time.time()
    response, metadata = client.generate(
        messages=augment_messages,
        max_tokens=output_tokens,
        system_prompt=system_prompt,
        temperature=0.0,
    )
    total_time = time.time() - start_time

    return LatencyMetrics(
        time_to_first_token=time_to_first,
        input_tokens=metadata["input_tokens"],
        output_tokens=metadata["output_tokens"],
        total_time=total_time,
    )


def main():
    client = AnthropicDirectClient(
        model_name="claude-3-5-sonnet-20241022",
        max_retries=2,
    )

    # Simple system prompt
    system = "You are a helpful AI assistant."

    # Test with different message lengths to see impact on latency
    message_sets = [
        # Short conversation
        [{"role": "user", "content": "Hi"}],
        # Medium conversation
        [
            {"role": "user", "content": "Hi"},
            {"role": "assistant", "content": "Hello! How can I help you today?"},
            {"role": "user", "content": "What's the weather?"},
        ],
        # Longer conversation with more complex query
        [
            {"role": "user", "content": "Hi"},
            {"role": "assistant", "content": "Hello! How can I help you today?"},
            {"role": "user", "content": "What's the weather?"},
            {
                "role": "assistant",
                "content": "I don't have access to current weather information. I can help you find weather services though!",
            },
            {
                "role": "user",
                "content": "Write a detailed explanation of how quicksort algorithm works, with examples.",
            },
        ],
    ]

    print("Measuring Claude 3 performance metrics...")
    print("\nTesting different conversation lengths:")

    for i, messages in enumerate(message_sets):
        metrics_list = []
        num_trials = 5

        print(f"\nConversation length {i+1} ({len(messages)} messages):")
        for trial in range(num_trials):
            metrics = measure_latency(client, system, messages)
            metrics_list.append(metrics)
            print(f"Trial {trial+1}:")
            print(f"  Time to first token: {metrics.time_to_first_token:.3f}s")
            print(
                f"  Input processing speed: {metrics.input_tokens_per_second:.1f} tokens/sec"
            )
            print(
                f"  Output generation speed: {metrics.output_tokens_per_second:.1f} tokens/sec"
            )
            print(f"  Input tokens: {metrics.input_tokens}")
            print(f"  Output tokens: {metrics.output_tokens}")

        # Calculate averages
        avg_first_token = statistics.mean(m.time_to_first_token for m in metrics_list)
        avg_input_speed = statistics.mean(
            m.input_tokens_per_second for m in metrics_list
        )
        avg_output_speed = statistics.mean(
            m.output_tokens_per_second for m in metrics_list
        )
        std_first_token = statistics.stdev(m.time_to_first_token for m in metrics_list)
        std_input_speed = statistics.stdev(
            m.input_tokens_per_second for m in metrics_list
        )
        std_output_speed = statistics.stdev(
            m.output_tokens_per_second for m in metrics_list
        )

        print("\nAverages:")
        print(
            f"  Time to first token: {avg_first_token:.3f}s (±{std_first_token:.3f}s)"
        )
        print(
            f"  Input processing speed: {avg_input_speed:.1f} tokens/sec (±{std_input_speed:.1f})"
        )
        print(
            f"  Output generation speed: {avg_output_speed:.1f} tokens/sec (±{std_output_speed:.1f})"
        )


if __name__ == "__main__":
    main()

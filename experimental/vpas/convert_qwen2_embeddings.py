#!/usr/bin/env python3

import argparse
import torch
from transformers import Qwen2ForCausalLM


def convert_checkpoint(input_path: str, output_path: str):
    """Convert a Qwen2 checkpoint to have separate input and output embeddings."""
    print(f"Loading model from {input_path}")
    model = Qwen2ForCausalLM.from_pretrained(input_path)

    # Get the current tied embeddings
    tied_embeddings = model.get_input_embeddings().weight.data.clone()

    # Create a new output embedding layer with the same weights
    vocab_size, embedding_dim = tied_embeddings.shape
    model.lm_head = torch.nn.Linear(embedding_dim, vocab_size, bias=False)
    model.lm_head.weight.data = tied_embeddings.clone()

    # Set config to indicate embeddings are not tied
    model.config.tie_word_embeddings = False

    print(f"Saving converted model to {output_path}")
    model.save_pretrained(output_path)
    print("Conversion complete!")


def main():
    parser = argparse.ArgumentParser(
        description="Convert Qwen2 checkpoint to use separate embeddings"
    )
    parser.add_argument("input_path", help="Path to input Qwen2 checkpoint")
    parser.add_argument("output_path", help="Path to save converted checkpoint")

    args = parser.parse_args()
    convert_checkpoint(args.input_path, args.output_path)


if __name__ == "__main__":
    main()

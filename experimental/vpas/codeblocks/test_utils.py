from experimental.vpas.codeblocks.utils import (
    convert_augment_to_udiff,
    replace_all_augment_code_snippets,
)


def test_convert_augment_to_udiff_basic():
    """Test basic conversion of augment code snippet to udiff format."""
    input_text = """<augment_code_snippet path="foo/bar.py" mode="EDIT">
```python
def hello():
    print("world")
```
</augment_code_snippet>"""

    expected = """```udiff
file: foo/bar.py
+def hello():
+    print("world")
```"""

    assert convert_augment_to_udiff(input_text) == expected


def test_convert_augment_to_udiff_empty():
    """Test conversion with invalid input."""
    assert convert_augment_to_udiff("") == ""
    assert convert_augment_to_udiff("<invalid>code</invalid>") == ""


def test_convert_augment_to_udiff_multiline():
    """Test conversion with multiple lines and whitespace."""
    input_text = """<augment_code_snippet path="test/file.py" mode="EDIT">
```python

class Test:
    def method(self):
        return True

```
</augment_code_snippet>"""

    expected = """```udiff
file: test/file.py
+class Test:
+    def method(self):
+        return True
```"""

    assert convert_augment_to_udiff(input_text) == expected


def test_convert_augment_to_udiff_different_language():
    """Test conversion with different language marker."""
    input_text = """<augment_code_snippet path="test.js" mode="EDIT">
```javascript
function test() {
    return 42;
}
```
</augment_code_snippet>"""

    expected = """```udiff
file: test.js
+function test() {
+    return 42;
+}
```"""

    assert convert_augment_to_udiff(input_text) == expected


def test_replace_single_snippet():
    input_text = """Here is some code:
<augment_code_snippet path="foo/bar.py" mode="EDIT">
```python
def hello():
    print("world")
```
</augment_code_snippet>
Some text after"""

    expected = """Here is some code:
```udiff
file: foo/bar.py
+def hello():
+    print("world")
```
Some text after"""

    result = replace_all_augment_code_snippets(input_text)
    assert result == expected


def test_replace_multiple_snippets():
    input_text = """First snippet:
<augment_code_snippet path="foo/bar.py" mode="EDIT">
```python
def hello():
    print("world")
```
</augment_code_snippet>
Middle text
<augment_code_snippet path="foo/baz.js" mode="EDIT">
```javascript
console.log('hi');
```
</augment_code_snippet>"""

    expected = """First snippet:
```udiff
file: foo/bar.py
+def hello():
+    print("world")
```
Middle text
```udiff
file: foo/baz.js
+console.log('hi');
```"""

    result = replace_all_augment_code_snippets(input_text)
    assert result == expected


def test_no_snippets():
    input_text = "Just some regular text without any code snippets"
    result = replace_all_augment_code_snippets(input_text)
    assert result == input_text


def test_invalid_snippet():
    input_text = """<augment_code_snippet>
    Invalid snippet without path
    </augment_code_snippet>"""
    result = replace_all_augment_code_snippets(input_text)
    assert result == input_text

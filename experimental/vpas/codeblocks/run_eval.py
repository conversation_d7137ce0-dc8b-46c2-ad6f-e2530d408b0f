from textwrap import dedent
from google.protobuf.json_format import MessageToDict
from pathlib import Path
from base.diff_utils.diff_utils import File
from typing import List
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.vpas.codeblocks.process_chat_response import (
    extract_udiffs_from_chat_response,
)
from experimental.vpas.codeblocks.third_party_models import (
    CLAUDE_UDIFF_MODEL,
    ThirdPartyModel,
)
from experimental.vpas.codeblocks.udiff import apply_udiff
from experimental.vpas.codeblocks.utils import convert_blocks_in_chat_history_to_udiff
from experimental.yury.smart_paste_eval.data.chat_utils import (
    build_chat_prompt_input,
    download_and_build_chat_prompt_input,
)
from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample
from experimental.yury.smart_paste_eval.html_viewer import save_model_eval_html
from experimental.yury.smart_paste_eval.request_insight_api_utils import (
    get_chat_host_request,
)
from experimental.yury.smart_paste_eval.utils import (
    ModelEvaluationResults,
    SmartPasteEvalOutput,
    _compute_diffs_and_correctness,
    calculate_per_category_accuracy,
)
from tqdm import tqdm
import argparse
import json
from research.utils.dataclass_utils import fromdict, asdict
import traceback
import re
from typing import Tuple


IGNORED_SAMPLES = {
    "a2c4084a-4737-44ff-a1be-a35ca0fa989b-2",
    "e380e9d6-bd79-407a-aa60-0f0d00f97a63",
    "ed4653be-3e81-4a0a-97bc-d066cc6f4dfd",
    "0ae73c71-d915-433d-9426-e4533ec62df5-0",
}


def evaluate_model_on_sample(
    model: ThirdPartyModel,
    sample: EvalSample,
) -> SmartPasteEvalOutput:
    output = SmartPasteEvalOutput(
        prompt_input=sample.prompt_input,
        eval_sample=sample,
    )

    try:
        chat_request = get_chat_host_request(
            sample.chat_request_id, sample.tenant_name, full=True
        )
        chat_request = MessageToDict(
            chat_request,
            preserving_proto_field_name=True,
        )
        if chat_request != {}:
            chat_prompt_input = build_chat_prompt_input(
                full_request=chat_request,
                add_retrievals=True,
            )
        else:
            chat_prompt_input = download_and_build_chat_prompt_input(
                sample.chat_request_id
            )

        modified_chat_history = convert_blocks_in_chat_history_to_udiff(
            chat_prompt_input.chat_history
        )
        modified_chat_history = []
        chat_prompt_input = ChatPromptInput(
            **{
                **chat_prompt_input.__dict__,
                "chat_history": modified_chat_history,
            }
        )

        output.chat_model_response = model.gen_response(chat_prompt_input)
        output.chat_model_response_success = True
        print("Response:")
        print(output.chat_model_response)
        print("*" * 80)

        # Parse response using extract_udiffs_from_chat_response()
        udiffs = extract_udiffs_from_chat_response(output.chat_model_response)
        if len(udiffs) > 0:
            output.diff_found = True
    except Exception as e:
        error_details = dedent(f"""
            Exception details:
            Type: {type(e).__name__}
            Message: {str(e)}
            Stack trace:
            {traceback.format_exc()}
        """)
        output.error_messages.append(error_details)
        output.error_messages.append(f"response: {output.chat_model_response}")
        udiffs = []
        raise e

    # Apply udiffs to target file
    target_file_content = sample.prompt_input.target_file_content
    modified_content = target_file_content
    for path, udiff in udiffs:
        if path == sample.prompt_input.target_path:
            try:
                output.diff_with_target_path_found = True
                modified_content = apply_udiff(
                    udiff=udiff,
                    original_text=target_file_content,
                    output=output,
                )
                output.diff_apply_success = True
            except Exception as e:
                output.error_messages.append(f"Failed to apply udiff: {e}")
                output.error_messages.append(f"udiff: {udiff}")
                output.error_messages.append(f"original_text: {target_file_content}")
                output.error_messages.append(f"response: {output.chat_model_response}")

    # Create a File object for the target file
    target_file = File(
        path=sample.prompt_input.target_path, contents=target_file_content
    )

    # Compare with expected output and update fields
    diffs_and_correctness = _compute_diffs_and_correctness(
        modified_content, target_file, sample.expected_outputs
    )

    output.response = modified_content
    output.diff_against_original = diffs_and_correctness.get("diff_against_original")
    output.diff_against_expected = diffs_and_correctness.get("diff_against_expected")
    output.correct = diffs_and_correctness.get("correct")
    output.chat_prompt_input = chat_prompt_input

    return output


def evaluate_model_on_dataset(
    model: ThirdPartyModel,
    dataset: List[EvalSample],
) -> ModelEvaluationResults:
    results: dict[str, SmartPasteEvalOutput] = {}
    n_correct = 0
    n_processed = 0
    n_skipped = 0

    for sample in tqdm(dataset, desc="Evaluating samples"):
        print(f"Sample: {sample.uuid}")

        output = evaluate_model_on_sample(
            model,
            sample,
        )
        output.eval_sample = sample
        results[sample.uuid] = output
        n_processed += 1

        if output.correct:
            print("Correct")
            n_correct += 1
        else:
            print("Incorrect")
            print(output.diff_against_expected)

    print(f"Overall accuracy: {n_correct} out of {n_processed} samples")
    print(f"Skipped samples: {n_skipped}")

    category_stats = calculate_per_category_accuracy(dataset, results)
    print("\nAccuracy by category:")
    for category, stats in category_stats.items():
        correct = stats["correct"]
        total = stats["total"]
        accuracy = (correct / total * 100) if total > 0 else 0
        print(f"{category}: {correct}/{total} ({accuracy:.1f}%)")

    return ModelEvaluationResults(
        sample_results=results,
        category_stats=category_stats,
        total_correct=n_correct,
        total_processed=n_processed,
        total_skipped=n_skipped,
        model_name=model.model_name,
        url="NO_URL",
    )


# Example usage
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="""
Evaluates code block generation using the Claude model on a dataset of samples.

This script can:
1. Run evaluations on the full dataset or a subset
2. Evaluate a single sample by UUID
3. Regenerate HTML reports from cached results
4. Cache evaluation results for future use

The results are saved as HTML reports and cached as JSON for later analysis.
""",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    parser.add_argument(
        "--regenerate-html-only",
        action="store_true",
        help="Skip evaluation and only regenerate HTML from cached results",
    )
    parser.add_argument(
        "--cache-dir",
        type=str,
        default="/mnt/efs/augment/user/vpas/codeblocks/udiff/eval_results_cache",
        help="Directory to store cached evaluation results",
    )
    parser.add_argument(
        "--num-samples",
        type=int,
        default=None,
        help="Number of samples to process. If not set, process all samples",
    )
    parser.add_argument(
        "--uuid",
        type=str,
        default=None,
        help="UUID of a specific sample to evaluate. If set, only this sample will be processed",
    )
    args = parser.parse_args()

    DATASET_NAME = "dogfood"
    if args.uuid:
        DATASET_NAME += f"_sample_{args.uuid}"
    elif args.num_samples:
        DATASET_NAME += f"_{args.num_samples}"
    MODEL_NAME = CLAUDE_UDIFF_MODEL.model_name
    CACHE_PATH = Path(args.cache_dir) / f"{DATASET_NAME}_{MODEL_NAME}_results.json"
    WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
    REL_PATH = f"vpas/smart_paste_eval/test/{DATASET_NAME}_{MODEL_NAME}/results.html"

    if not args.regenerate_html_only:
        from experimental.yury.smart_paste_eval.data.dogfood import DATA

        # Filter dataset based on arguments
        if args.uuid:
            dataset = [sample for sample in DATA if sample.uuid == args.uuid]
            if not dataset:
                raise ValueError(f"No sample found with UUID: {args.uuid}")
        else:
            dataset = [sample for sample in DATA if sample.uuid not in IGNORED_SAMPLES]
            dataset = dataset[: args.num_samples] if args.num_samples else dataset

        # Run evaluation and cache results
        results = evaluate_model_on_dataset(
            model=CLAUDE_UDIFF_MODEL,
            dataset=dataset,
        )

        # Create cache directory if it doesn't exist
        CACHE_PATH.parent.mkdir(parents=True, exist_ok=True)

        # Cache the results using asdict for proper dataclass serialization
        with open(CACHE_PATH, "w") as f:
            json.dump(asdict(results), f, indent=2)
    else:
        # Load cached results
        if not CACHE_PATH.exists():
            raise FileNotFoundError(f"No cached results found at {CACHE_PATH}")

        with open(CACHE_PATH) as f:
            data = json.load(f)
            # Recursively reconstruct all nested dataclasses
            results = fromdict(ModelEvaluationResults, **data)

    # Generate HTML
    path = WEB_SERVER_DIR / REL_PATH
    save_model_eval_html(results, path)

    URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
    url = URL_TEMPLATE.format(REL_PATH)
    print(f"Results saved to: {path}")
    print(f"HTML URL: {url}")

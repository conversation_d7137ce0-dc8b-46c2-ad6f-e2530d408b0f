import re
from typing import Iterable, List

from base.prompt_format.common import Exchange


def convert_augment_to_udiff(text: str) -> str:
    """Convert augment code snippet format to udiff format.

    Args:
        text: String containing augment code snippet format

    Returns:
        String in udiff format with + prefixes
    """
    # Extract path and code content using regex
    pattern = r'<augment_code_snippet\s+path="([^"]+)"[^>]*>\s*```[a-zA-Z]*\s*(.*?)\s*```\s*</augment_code_snippet>'
    match = re.search(pattern, text, re.DOTALL)

    if not match:
        return ""

    file_path, code_content = match.groups()

    # Split into lines and add + prefix
    code_lines = code_content.strip().split("\n")
    prefixed_lines = [f"+{line}" for line in code_lines]

    # Format as udiff
    result = ["```udiff", f"file: {file_path}", *prefixed_lines, "```"]

    return "\n".join(result)


def replace_all_augment_code_snippets(text: str) -> str:
    """Replace all augment code snippets in text with udiff format.

    Args:
        text: String containing augment code snippets

    Returns:
        String with all augment code snippets replaced with udiff format
    """
    pattern = r'<augment_code_snippet\s+path="([^"]+)"[^>]*>\s*```[a-zA-Z]*\s*(.*?)\s*```\s*</augment_code_snippet>'
    return re.sub(
        pattern, lambda m: convert_augment_to_udiff(m.group(0)), text, flags=re.DOTALL
    )


def convert_augment_legacy_to_udiff(text: str) -> str:
    # Extract path and code content using regex
    pattern = r"````\w+\s+path=([^\s]+)\s+mode=\w+\s+(.*?)````"
    match = re.search(pattern, text, re.DOTALL)

    if not match:
        return ""

    file_path, code_content = match.groups()

    # Split into lines and add + prefix
    code_lines = code_content.strip().split("\n")
    prefixed_lines = [f"+{line}" for line in code_lines]

    # Format as udiff
    result = ["```udiff", f"file: {file_path}", *prefixed_lines, "```"]

    return "\n".join(result)


def replace_all_augment_legacy_code_snippets(text: str) -> str:
    """Replace all augment code snippets in text with udiff format.

    Args:
        text: String containing augment code snippets

    Returns:
        String with all augment code snippets replaced with udiff format
    """
    pattern = r"````\w+\s+path=([^\s]+)\s+mode=\w+\s+(.*?)````"
    return re.sub(
        pattern,
        lambda m: convert_augment_legacy_to_udiff(m.group(0)),
        text,
        flags=re.DOTALL,
    )


def convert_blocks_in_chat_history_to_udiff(
    chat_history: Iterable[Exchange],
) -> List[Exchange]:
    """Convert augment code snippets in chat history to udiff format.

    Args:
        chat_history: Iterable of Exchange objects

    Returns:
        Iterable of Exchange objects with augment code snippets converted to udiff format
    """
    new_chat_history = []
    for exchange in chat_history:
        response_text = exchange.response_text
        udiff_text = replace_all_augment_code_snippets(response_text)  # type: ignore
        udiff_text = replace_all_augment_legacy_code_snippets(udiff_text)  # type: ignore
        if udiff_text:
            exchange = Exchange(
                request_message=exchange.request_message,
                response_text=udiff_text,
                request_id=exchange.request_id,
            )
        new_chat_history.append(exchange)
    return new_chat_history

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.lib.system_prompts import String<PERSON><PERSON>atter
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.token_counter.token_counter_claude import (
    ClaudeTokenCounter,
)
from experimental.vpas.codeblocks.system_prompts import (
    BINKS_CODEBLOCKS_XML_SYSTEM_PROMPT_WITH_UDIFF,
)

token_apportionment = ChatTokenApportionment(
    prefix_len=1024 * 2,  # 2k tokens
    suffix_len=1024 * 2,  # 2k tokens
    path_len=256,
    message_len=-1,  # Deprecated field
    selected_code_len=-1,  # Deprecated field
    chat_history_len=1024 * 4,  # 4k tokens
    retrieval_len_per_each_user_guided_file=3000,
    retrieval_len_for_user_guided=8000,
    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals
    max_prompt_len=1024 * 12,  # 12k tokens for prompt
    inject_current_file_into_retrievals=True,
)


class ThirdPartyModel:
    def __init__(self, model_name: str, system_prompt: str):
        self.model_name = model_name
        token_counter = ClaudeTokenCounter()
        system_prompt_formatter = StringFormatter(
            system_prompt, token_counter=token_counter
        )
        self.formatter = StructuredBinksPromptFormatter.create(
            token_counter=token_counter,
            token_apportionment=token_apportionment,
            system_prompt_factory=lambda _: system_prompt_formatter,
        )

    def gen_response(self, chat_prompt_input: ChatPromptInput) -> str:
        # Construct message to Claude using StructuredBinksPromptFormatter
        chat_prompt_output = self.formatter.format_prompt(chat_prompt_input)

        messages = []
        for exchange in chat_prompt_output.chat_history:
            messages.extend(
                [
                    {"role": "user", "content": exchange.request_message},
                    {"role": "assistant", "content": exchange.response_text},
                ]
            )
        messages.append({"role": "user", "content": chat_prompt_output.message})

        return self._gen_response(chat_prompt_output.system_prompt, messages)

    def _gen_response(self, system_prompt: str, messages: list[dict[str, str]]) -> str:
        raise NotImplementedError()


class ClaudeModel(ThirdPartyModel):
    def __init__(self, model_name: str, claude_model_name: str, system_prompt: str):
        super().__init__(model_name, system_prompt)
        self.claude_model_name = claude_model_name
        self._client = None

    @property
    def client(self):
        if self._client is None:
            self._client = AnthropicVertexAiClient(
                project_id="augment-387916",
                region="us-east5",
                model_name=self.claude_model_name,
                temperature=0,
                max_output_tokens=1024 * 8,
            )
        return self._client

    def _gen_response(self, system_prompt: str, messages: list[dict[str, str]]) -> str:
        response = self.client.client.messages.create(
            model=self.claude_model_name,
            max_tokens=1024 * 8,
            messages=messages,
            system=system_prompt,
            temperature=0,
        )
        return response.content[0].text


CLAUDE_UDIFF_MODEL = ClaudeModel(
    model_name="claude-udiff",
    claude_model_name="claude-3-5-sonnet-v2@20241022",
    system_prompt=BINKS_CODEBLOCKS_XML_SYSTEM_PROMPT_WITH_UDIFF,
)

import pytest
import textwrap
from udiff import (
    apply_udiff,
    fuzzy_compare,
    get_new_old_lines,
    find_hunk_start,
    parse_hunks,
    strip_empty_lines,
    apply_hunk,
    Hunk,
    HunkLineNumbers,
)


def test_fuzzy_compare_simple():
    assert fuzzy_compare("foo", "foo", None, None, None, None) == (True, 1, 1)
    assert fuzzy_compare("foo", "bar", None, None, None, None) == (False, 1, 1)
    assert fuzzy_compare("foo_bar", "foo bar", None, None, None, None) == (True, 1, 1)
    assert fuzzy_compare("FOO", "foo", None, None, None, None) == (True, 1, 1)
    assert fuzzy_compare("foo,", "foo", None, None, None, None) == (True, 1, 1)


def test_fuzzy_compare_multiline():
    lines1 = ["foo", "bar", "baz"]
    lines2 = ["foo bar", "baz"]
    # Test multiline matching
    assert fuzzy_compare("foo bar", "foo\nbar", lines2, 0, lines1, 0) == (True, 1, 1)
    # Test with missing line lists
    assert fuzzy_compare("foo bar", "foo\nbar", None, None, None, None) == (True, 1, 1)
    # Test with out of bounds indices
    assert fuzzy_compare("foo", "bar", lines1, 10, lines2, 10) == (False, 1, 1)


def test_get_new_old_lines_basic():
    hunk_lines = [
        " context",
        "-removed",
        "+added",
        " more context",
    ]
    old_lines, new_lines = get_new_old_lines(hunk_lines)
    assert old_lines == [" context", "-removed", " more context"]
    assert new_lines == [" context", "+added", " more context"]


def test_find_hunk_start_exact_match():
    lines = ["foo", "bar", "baz"]
    old_lines = ["-foo", " bar"]
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 0
    assert result.num_lines_matched == 2
    assert not error_messages


def test_find_hunk_start_no_match():
    lines = ["foo", "bar", "baz"]
    old_lines = ["-different", " content"]
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 0
    assert result.num_lines_matched == 0
    assert len(error_messages) > 0  # Should contain debug info


def test_find_hunk_start_empty_old_lines():
    lines = ["foo", "bar", "baz"]
    old_lines = []
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 0
    assert result.num_lines_matched == 0
    assert not error_messages


def test_find_hunk_start_partial_match():
    lines = ["foo", "bar", "baz", "qux"]
    old_lines = [" foo", " bar", " different"]
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 0
    assert result.num_lines_matched == 2
    assert len(error_messages) > 0  # Should contain debug info


def test_find_hunk_start_empty_lines():
    lines = ["", "foo", "bar"]
    old_lines = [" ", " foo"]
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 1
    assert result.num_lines_matched == 1  # Only matches foo line


def test_find_hunk_start_real_sample():
    original = textwrap.dedent("""
            self.logged_calls: list[LoggedToolCall | LoggedLanguageModelCall] = []
            self.verbose = verbose
            self.verbose_llm_calls = verbose_llm_calls
            self.use_tool_supplied_messages = use_tool_supplied_messages
            self.show_call_durations = show_call_durations
            self.log_file = log_file
            self.pickle_log_file = pickle_log_file

        @staticmethod
        def from_pickle_file(pickle_log_file: Path) -> "ToolCallLogger":
            logged_calls = pickle.load(pickle_log_file.open("rb"))
            logger = ToolCallLogger()
            logger.logged_calls = logged_calls
            return logger

        def tool_call_started(
            self,
            called_tool: LLMTool,
            tool_input: ToolInputSchema,
            tool_started_message: str,
            auxiliary_data: Optional[dict[str, Any]] = None,
        ):
            tool_info = ToolParam(
                name=called_tool.name,
                description=called_tool.description,
                input_schema=called_tool.input_schema,
            )
    """)
    lines = original.splitlines()
    old_lines = [
        "         logger = ToolCallLogger()",
        "         logger.logged_calls = logged_calls",
        "         return logger",
        " ",
        "     def tool_call_started(",
        "         self,",
    ]
    error_messages = []
    result = find_hunk_start(lines, old_lines, error_messages)
    assert result.start_line == 12
    assert result.num_lines_matched == len(old_lines)
    assert not error_messages


def test_parse_hunks_empty():
    assert parse_hunks("") == []


def test_parse_hunks_single():
    udiff = textwrap.dedent("""
        @@ -1,3 +1,3 @@
         context
        -old
        +new
    """).strip()
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].line_numbers.old_start == 1
    assert hunks[0].line_numbers.old_count == 3
    assert hunks[0].line_numbers.new_start == 1
    assert hunks[0].line_numbers.new_count == 3
    assert hunks[0].old_lines == [" context", "-old"]
    assert hunks[0].new_lines == [" context", "+new"]
    assert hunks[0].all_lines == [" context", "-old", "+new"]


def test_parse_hunks_invalid_header():
    udiff = textwrap.dedent("""
        @@ invalid header @@
         context
        -old
        +new
    """).strip()
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].line_numbers is None


def test_parse_hunks_with_file_headers():
    udiff = textwrap.dedent("""
        --- a/file.txt
        +++ b/file.txt
        @@ -1,3 +1,3 @@
         context
        -old
        +new
    """).strip()
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].line_numbers.old_start == 1
    assert hunks[0].line_numbers.old_count == 3


def test_parse_hunks_invalid_format():
    udiff = textwrap.dedent("""
        @@ -1,x +1,3 @@
         context
        -old
        +new
    """).strip()
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].line_numbers is None


def test_parse_hunks_missing_count():
    udiff = textwrap.dedent("""
        @@ -1 +1 @@
         context
        -old
        +new
    """).strip()
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].line_numbers.old_count == 1
    assert hunks[0].line_numbers.new_count == 1


def test_parse_hunks_missing_header():
    udiff = textwrap.dedent("""
         context
        -old
        +new
    """)
    hunks = parse_hunks(udiff)
    assert len(hunks) == 1
    assert hunks[0].old_lines == [" context", "-old"]
    assert hunks[0].new_lines == [" context", "+new"]
    assert hunks[0].all_lines == [" context", "-old", "+new"]


def test_strip_empty_lines():
    text = "\n\nfoo\nbar\n\n"
    assert strip_empty_lines(text) == "foo\nbar"
    assert strip_empty_lines("") == ""
    assert strip_empty_lines("\n\n") == ""
    assert strip_empty_lines("foo") == "foo"


def test_apply_hunk_pure_addition():
    hunk = Hunk(
        hunk_raw="+new line",
        hunk_header=None,
        line_numbers=None,
        all_lines=["+new line"],
        old_lines=[],
        new_lines=["+new line"],
    )
    result = apply_hunk(hunk, "")
    assert result.modified_text == "new line"
    assert result.pure_addition
    assert not result.pure_addition_to_non_empty_file


def test_apply_hunk_pure_addition_to_non_empty():
    hunk = Hunk(
        hunk_raw="+new line",
        hunk_header=None,
        line_numbers=None,
        all_lines=["+new line"],
        old_lines=[],
        new_lines=["+new line"],
    )
    result = apply_hunk(hunk, "existing")
    assert result.modified_text == "existing\nnew line"
    assert result.pure_addition
    assert result.pure_addition_to_non_empty_file


def test_apply_hunk_with_line_numbers():
    hunk = Hunk(
        hunk_raw=" context\n+new",
        hunk_header="@@ -1,1 +1,2 @@",
        line_numbers=HunkLineNumbers(
            old_start=1, old_count=1, new_start=1, new_count=2
        ),
        all_lines=[" context", "+new"],
        old_lines=[" context"],
        new_lines=[" context", "+new"],
    )
    result = apply_hunk(hunk, "context")
    assert result.modified_text == "context\nnew"
    assert result.matched_line_numbers is not None
    assert result.matched_line_numbers.old_start == 1
    assert result.matched_line_numbers.old_count == 1


def test_apply_hunk_mismatch():
    hunk = Hunk(
        hunk_raw=" context\n+new",
        hunk_header=None,
        line_numbers=None,
        all_lines=[" context", "+new"],
        old_lines=[" context"],
        new_lines=[" context", "+new"],
    )
    result = apply_hunk(hunk, "different\ncontent")
    assert not result.apply_success
    assert len(result.error_messages) > 0
    assert "Could not find matching hunk in original text" in result.error_messages
    # Original text should be returned unchanged
    assert result.modified_text == "different\ncontent"


def test_apply_hunk_empty_lines():
    hunk = Hunk(
        hunk_raw=" context\n+new",
        hunk_header=None,
        line_numbers=None,
        all_lines=[" context", "+new"],
        old_lines=[" context"],
        new_lines=[" context", "+new"],
    )
    result = apply_hunk(hunk, "context\n")
    assert result.modified_text == "context\nnew"


def test_apply_hunk_with_empty_lines():
    hunk = Hunk(
        hunk_raw="+new",
        hunk_header=None,
        line_numbers=None,
        all_lines=["+new"],
        old_lines=[],
        new_lines=["+new"],
    )
    result = apply_hunk(hunk, "\n")
    assert result.modified_text == "\nnew"


def test_apply_hunk_with_empty_context():
    hunk = Hunk(
        hunk_raw=" \n+new",
        hunk_header=None,
        line_numbers=None,
        all_lines=[" ", "+new"],
        old_lines=[" "],
        new_lines=[" ", "+new"],
    )
    result = apply_hunk(hunk, "\n")
    assert result.modified_text == "\nnew"


def test_apply_hunk_real_sample():
    original_text = textwrap.dedent("""
            # Run the agent
            result = edit_agent.run_impl(
                tool_input={
                    "file_path": "test.py",
                    "short_edit_description": "Change hello to hello world",
                },
                dialog_messages=dialog_messages,
            )

            # Verify the result
            assert result.tool_result_message == "File test.py edited."
            expected_content = "def hello():\n    print('hello world')"
            assert test_file.read_text() == expected_content

        def test_edit_file_invalid_udiff(self, edit_agent, mock_workspace, mock_llm_client):
            # Setup test file
            test_file = mock_workspace.root / "test.py"
            test_file.write_text("def hello():\n    print('hello')\n")
    """)

    hunk_raw = textwrap.dedent("""
        @@ -71,7 +71,7 @@
                 # Verify the result
                 assert result.tool_result_message == "File test.py edited."
                 expected_content = "def hello():\n    print('hello world')"
        -        assert test_file.read_text() == expected_content
        +        assert test_file.read_text().rstrip() == expected_content

             def test_edit_file_invalid_udiff(self, edit_agent, mock_workspace, mock_llm_client):
                 # Setup test file
    """)
    hunks = parse_hunks(hunk_raw)
    assert len(hunks) == 1
    hunk = hunks[0]
    result = apply_hunk(hunk, original_text)

    expected_text = textwrap.dedent("""
            # Run the agent
            result = edit_agent.run_impl(
                tool_input={
                    "file_path": "test.py",
                    "short_edit_description": "Change hello to hello world",
                },
                dialog_messages=dialog_messages,
            )

            # Verify the result
            assert result.tool_result_message == "File test.py edited."
            expected_content = "def hello():\n    print('hello world')"
            assert test_file.read_text().rstrip() == expected_content

        def test_edit_file_invalid_udiff(self, edit_agent, mock_workspace, mock_llm_client):
            # Setup test file
            test_file = mock_workspace.root / "test.py"
            test_file.write_text("def hello():\n    print('hello')\n")
    """)
    assert result.modified_text == expected_text.strip()


def test_apply_udiff_simple():
    original_text = textwrap.dedent("""
        def main():
            print("Hello!")
    """).strip()

    udiff = textwrap.dedent("""
         def main():
        -    print("Hello!")
        +    print("Goodbye!")
    """).strip()

    expected_result = textwrap.dedent("""
        def main():
            print("Goodbye!")
    """).strip()

    result = apply_udiff(udiff, original_text)
    assert result.modified_text == expected_result


def test_apply_udiff_partial_change():
    original_text = textwrap.dedent("""
        def greet():
            print("Hello!")
            return
    """).strip()

    udiff = textwrap.dedent("""
         def greet():
        -    print("Hello!")
        +    print("Hi!")
             return
    """).strip()

    expected_result = textwrap.dedent("""
        def greet():
            print("Hi!")
            return
    """).strip()

    result = apply_udiff(udiff, original_text)
    assert result.modified_text == expected_result


def test_apply_udiff_multiple_hunks():
    original_text = textwrap.dedent("""\
        def add(a, b):
            return a + b

        def multiply(a, b):
            return a * b""")

    udiff = textwrap.dedent("""
         def add(a, b):
        -    return a + b
        +    return sum([a, b])

         def multiply(a, b):
        -    return a * b
        +    return a * b  # Multiplication operation""")

    expected_result = textwrap.dedent("""\
        def add(a, b):
            return sum([a, b])

        def multiply(a, b):
            return a * b  # Multiplication operation""")

    result = apply_udiff(udiff, original_text)
    assert result.modified_text.strip() == expected_result.strip()


def test_apply_udiff_no_changes():
    original_text = textwrap.dedent("""\
        def hello():
            print("Hello, World!")""")

    udiff = """
 def hello():
     print("Hello, World!")"""

    result = apply_udiff(udiff, original_text)
    assert result.modified_text.strip() == original_text.strip()


def test_apply_udiff_empty_original():
    original_text = ""
    udiff = textwrap.dedent("""
        +def new_function():
        +    print("This is a new function")""")

    expected_result = textwrap.dedent("""\
        def new_function():
            print("This is a new function")""")

    result = apply_udiff(udiff, original_text)
    assert result.modified_text.strip() == expected_result.strip()


def test_apply_udiff_empty_diff():
    original_text = "content"
    result = apply_udiff("", original_text)
    assert result.modified_text == original_text
    assert not result.error_messages


def test_apply_udiff_invalid_hunk():
    original_text = "content"
    udiff = textwrap.dedent("""
        @@ invalid header @@
        -wrong
        +content
    """)
    result = apply_udiff(udiff, original_text)
    assert len(result.error_messages) > 0


def test_apply_udiff_with_errors():
    original_text = "content"
    udiff = textwrap.dedent("""
        @@ -1,1 +1,1 @@
        -wrong
        +content
    """).strip()
    result = apply_udiff(udiff, original_text)
    assert len(result.error_messages) > 0
    assert result.modified_text == original_text
    assert result.hunk_apply_results[0].num_old_lines_matched == 0
    assert not result.hunk_apply_results[0].apply_succes


def test_apply_udiff_with_empty_lines():
    original_text = "content"
    udiff = textwrap.dedent("""
        @@ -1,1 +1,1 @@
        -content
        +new content
    """).strip()
    result = apply_udiff(udiff, original_text)
    assert result.modified_text == "new content"
    assert result.hunk_apply_results[0].found_start
    assert result.hunk_apply_results[0].apply_succes
    assert result.hunk_apply_results[0].num_old_lines_matched == 1
    assert result.hunk_apply_results[0].num_old_lines_total == 1
    assert not result.error_messages


if __name__ == "__main__":
    pytest.main([__file__])

import pytest
from process_chat_response import extract_udiffs_from_chat_response


def test_extract_udiffs_from_chat_response():
    sample_response = """
Here's the unified diff for the changes:

```udiff
file: example.py
@@ -1,5 +1,5 @@
 def greet(name):
-    print(f"Hello, {name}!")
+    print(f"Hello, {name.capitalize()}!")

 greet("world")
```

And another change:

```udiff
file: test.py
@@ -1,3 +1,3 @@
 def test_greet():
-    assert greet("world") == "Hello, world!"
+    assert greet("world") == "Hello, World!"
```

And a udiff without a file name:

```udiff
@@ -1,3 +1,3 @@
 def another_function():
-    return "old"
+    return "new"
```
"""

    result = extract_udiffs_from_chat_response(sample_response)

    assert len(result) == 3
    assert result[0] == (
        "example.py",
        '@@ -1,5 +1,5 @@\n def greet(name):\n-    print(f"Hello, {name}!")\n+    print(f"Hello, {name.capitalize()}!")\n \n greet("world")',
    )
    assert result[1] == (
        "test.py",
        '@@ -1,3 +1,3 @@\n def test_greet():\n-    assert greet("world") == "Hello, world!"\n+    assert greet("world") == "Hello, World!"',
    )
    assert result[2] == (
        "",
        '@@ -1,3 +1,3 @@\n def another_function():\n-    return "old"\n+    return "new"',
    )


def test_extract_udiffs_from_chat_response_no_udiffs():
    sample_response = "This is a response without any udiffs."
    result = extract_udiffs_from_chat_response(sample_response)
    assert len(result) == 0


def test_extract_udiffs_from_chat_response_malformed_udiff():
    sample_response = """
Here's a malformed udiff:

```udiff
This is not a proper udiff
```
"""
    result = extract_udiffs_from_chat_response(sample_response)
    assert len(result) == 1
    assert result[0] == ("", "This is not a proper udiff")


if __name__ == "__main__":
    pytest.main([__file__])

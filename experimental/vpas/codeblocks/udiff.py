"""Minimal version of udiff module for testing."""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class HunkLineNumbers:
    """Line numbers for a hunk."""

    old_start: int
    old_count: int
    new_start: int
    new_count: int


@dataclass
class HunkApplyResult:
    """Result of applying a hunk."""

    hunk: str
    modified_text: str
    error_messages: List[str]
    parsed_line_numbers: HunkLineNumbers
    matched_line_numbers: HunkLineNumbers
    found_start: bool
    apply_success: bool
    pure_addition: bool
    pure_addition_to_non_empty_file: bool
    num_old_lines_matched: int
    num_old_lines_total: int


@dataclass
class UdiffApplyResult:
    """Result of applying a unified diff."""

    udiff: str
    modified_text: str
    hunk_apply_results: List[HunkApplyResult]
    error_messages: List[str]

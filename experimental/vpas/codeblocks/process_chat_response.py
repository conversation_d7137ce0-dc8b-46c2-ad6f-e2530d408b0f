import re
from typing import List, <PERSON><PERSON>


def extract_udiffs_from_chat_response(response: str) -> List[Tuple[str, str]]:
    udiff_pattern = r"```udiff\s*(?:file:\s*([^\n]+))?([\s\S]*?)```"
    matches = re.finditer(udiff_pattern, response, re.MULTILINE)

    udiffs = []
    for match in matches:
        file_path = match.group(1) or ""
        udiff_content = match.group(2)
        udiffs.append((file_path, udiff_content))

    return udiffs


# Example usage
if __name__ == "__main__":
    sample_response = """
Here's the unified diff for the changes:

```udiff
file: example.py
@@ -1,5 +1,5 @@
 def greet(name):
-    print(f"Hello, {name}!")
+    print(f"Hello, {name.capitalize()}!")

 greet("world")
```

And another change:

```udiff
file: test.py
@@ -1,3 +1,3 @@
 def test_greet():
-    assert greet("world") == "Hello, world!"
+    assert greet("world") == "Hello, World!"
```
"""

    result = extract_udiffs_from_chat_response(sample_response)
    for file_path, udiff in result:
        print(f"File: {file_path}")
        print(udiff)
        print()

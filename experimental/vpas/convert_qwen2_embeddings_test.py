import pytest
import torch
from transformers import Qwen2ForCausalLM
from convert_qwen2_embeddings import convert_checkpoint
import tempfile
import os


@pytest.fixture
def temp_dirs():
    with tempfile.TemporaryDirectory() as input_dir, tempfile.TemporaryDirectory() as output_dir:
        yield input_dir, output_dir


def test_conversion(temp_dirs):
    input_dir, output_dir = temp_dirs

    # Create a small test model and save it
    model = Qwen2ForCausalLM.from_pretrained(
        "Qwen/Qwen2-7B-beta", torch_dtype=torch.float32
    )
    model.save_pretrained(input_dir)

    # Convert the model
    convert_checkpoint(input_dir, output_dir)

    # Load the converted model
    converted_model = Qwen2ForCausalLM.from_pretrained(output_dir)

    # Check that embeddings are not tied
    assert not converted_model.config.tie_word_embeddings

    # Check that input and output embeddings are separate but equal
    input_embeddings = converted_model.get_input_embeddings().weight.data
    output_embeddings = converted_model.lm_head.weight.data

    assert torch.allclose(input_embeddings, output_embeddings)
    assert input_embeddings is not output_embeddings  # Different objects in memory

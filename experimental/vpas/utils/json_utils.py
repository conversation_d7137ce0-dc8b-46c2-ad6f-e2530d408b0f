import dataclasses
from datetime import datetime

import dataclasses_json
import functools
from marshmallow import fields as mm_fields


def jsonify(x):
    if x is None or isinstance(x, (str, int, float, bool)):
        return x
    elif isinstance(x, (list, tuple)):
        return [jsonify(item) for item in x]
    elif isinstance(x, dict):
        return {str(k): jsonify(v) for k, v in x.items()}
    elif dataclasses.is_dataclass(x):
        return {f.name: jsonify(getattr(x, f.name)) for f in dataclasses.fields(x)}
    elif hasattr(x, "__dict__"):
        return jsonify(x.__dict__)
    else:
        return str(x)


@functools.cache
def setup_dataclasses_json():
    """Setup dataclasses_json to handle commonly used types.

    This function should be run before any dataclass is converted to JSON.
    """
    # Add global datetime support
    dataclasses_json.global_config.encoders[datetime] = (
        lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None
    )  # type: ignore
    dataclasses_json.global_config.decoders[datetime] = (
        lambda s: datetime.strptime(s, "%Y-%m-%d %H:%M:%S") if s else None
    )  # type: ignore
    dataclasses_json.global_config.mm_fields[datetime] = mm_fields.DateTime(
        format="%Y-%m-%d %H:%M:%S", allow_none=True
    )  # type: ignore

    # Support for Optional[datetime]
    dataclasses_json.global_config.encoders[datetime | None] = (
        lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None
    )  # type: ignore
    dataclasses_json.global_config.decoders[datetime | None] = (
        lambda s: datetime.strptime(s, "%Y-%m-%d %H:%M:%S") if s else None
    )  # type: ignore
    dataclasses_json.global_config.mm_fields[datetime | None] = mm_fields.DateTime(
        format="%Y-%m-%d %H:%M:%S", allow_none=True
    )  # type: ignore


setup_dataclasses_json()

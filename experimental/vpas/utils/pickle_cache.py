import functools
import hashlib
import logging
import os
import pickle
from pathlib import Path
from typing import Callable, Optional, TypeVar

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


CACHE_DIRECTORY = "/mnt/efs/augment/user/vpas/pickle_cache"


class PersistentPickleCache:
    """A simple pickle-based file cache system for persistently caching results."""

    def __init__(
        self,
        path: str,
        func_name: Optional[str] = None,
        size_limit_bytes: int = int(1.5e11),
    ):
        self.cache_files = {}
        self.cache_path = Path(path)
        self.size_limit_bytes = size_limit_bytes
        self.func_name = func_name
        self.cache_path.mkdir(parents=True, exist_ok=True)
        assert self.cache_path.is_dir()

        self.total_size = 0
        for file in self.cache_path.glob("*.pkl"):
            self.cache_files[file.stem] = {
                "size": file.stat().st_size,
                "path": file,
                "last_accessed": file.stat().st_atime,
            }
            self.total_size += file.stat().st_size

        if self.total_size > self.size_limit_bytes:
            logger.warning(
                f"Cache size {self.total_size / 1e9:.2f}GiB exceeds limit "
                f"{self.size_limit_bytes / 1e9:.2f}GiB, consider shrinking the cache."
            )

    def _construct_key(self, args, kwargs):
        # Convert args and kwargs to a string representation
        key_parts = []

        # Add function name to key parts if available
        if self.func_name:
            key_parts.append(f"func={self.func_name}")

        # Add args to key parts
        for arg in args:
            if isinstance(arg, str):
                key_parts.append(arg)
            else:
                key_parts.append(str(arg))

        # Add kwargs to key parts
        for k, v in sorted(kwargs.items()):
            if isinstance(v, str):
                key_parts.append(f"{k}={v}")
            else:
                key_parts.append(f"{k}={str(v)}")

        # Join key parts and hash
        key_str = "_".join(key_parts)
        key = hashlib.md5(key_str.encode("utf-8")).hexdigest()
        return key

    def exists(self, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        return key in self.cache_files

    def get(self, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        assert key in self.cache_files
        with (self.cache_path / f"{key}.pkl").open("rb") as f:
            return pickle.load(f)

    def add(self, value, *args, **kwargs):
        key = self._construct_key(args, kwargs)
        cache_file = self.cache_path / f"{key}.pkl"
        with cache_file.open("wb") as f:
            pickle.dump(value, f)

        # Update cache metadata
        self.cache_files[key] = {
            "size": os.path.getsize(cache_file),
            "path": cache_file,
            "last_accessed": os.path.getatime(cache_file),
        }
        self.total_size += self.cache_files[key]["size"]

    def shrink(self):
        if self.total_size <= self.size_limit_bytes:
            return

        # Sort files by last access time
        sorted_files = sorted(
            self.cache_files.items(), key=lambda x: x[1]["last_accessed"]
        )

        # Remove oldest files until we're under the limit
        for key, file_info in sorted_files:
            if self.total_size <= self.size_limit_bytes:
                break

            self.total_size -= file_info["size"]
            file_info["path"].unlink(missing_ok=True)
            del self.cache_files[key]


T = TypeVar("T")


def pickle_cache(path: str) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """Decorator for caching function results using pickle."""

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        cache = PersistentPickleCache(path, func_name=func.__name__)

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            if cache.exists(*args, **kwargs):
                return cache.get(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
                if result is not None:  # Only cache non-None results
                    try:
                        cache.add(result, *args, **kwargs)
                    except Exception as e:
                        logger.warning(f"Failed to cache result: {e}")
                return result

        return wrapper

    return decorator

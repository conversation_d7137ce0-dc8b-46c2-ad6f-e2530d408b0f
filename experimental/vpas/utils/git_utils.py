import subprocess


def get_git_username() -> str:
    """Get username from git config email.

    Extracts the username portion from the user's git email configuration.
    For example, from "<EMAIL>" returns "user".

    Returns:
        str: The extracted username, or "unknown" if git config is not available.
    """
    try:
        email = subprocess.check_output(
            ["git", "config", "user.email"],
            universal_newlines=True,
            stderr=subprocess.DEVNULL,
        ).strip()
        return email.split("@")[0]
    except subprocess.CalledProcessError:
        return "unknown"

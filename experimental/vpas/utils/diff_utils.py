from pathlib import Path

from base.static_analysis.common import is_parsing_supported_lang
from base.static_analysis.parsing import (
    TsParsedFile,
    compare_asts,
    ParsingFailedError,
    find_syntax_errors,
)
from research.fim.import_noising import find_all_imports


def rstrip_lines(text: str):
    """RStrip all lines in a multi-line string."""
    return "\n".join([line.rstrip() for line in text.splitlines()])


def remove_empty_lines(text: str):
    """Remove all empty lines in a multi-line string."""
    return "\n".join([line for line in text.splitlines() if line.strip()])


def normalize_for_diff(text: str):
    """Normalize text for diff comparison."""
    text = rstrip_lines(text)
    text = remove_empty_lines(text)
    text = text.replace("\t", "    ")
    return text


def normalize_imports_and_compare_asts(
    code1: str, code2: str, lang: str, path: Path, ignore_comments: bool = False
) -> bool | None:
    """Compare two source codes by constructing their ASTs, normalizing imports first.

    Returns True if the ASTs match after normalizing imports, False if they don't,
    and None if parsing failed or language not supported."""
    if not is_parsing_supported_lang(lang):
        return None

    try:
        # First try parsing to get imports
        pfile1 = TsParsedFile.parse(path, lang, code1)
        pfile2 = TsParsedFile.parse(path, lang, code2)

        # Get imports from both files
        imports1 = list(find_all_imports(pfile1.ts_tree.root_node, lang))
        imports2 = list(find_all_imports(pfile2.ts_tree.root_node, lang))

        # Convert imports to normalized strings for comparison
        import_strs1 = sorted(
            normalize_for_diff(str(imp.stmt.text, "utf8")) for imp in imports1
        )
        import_strs2 = sorted(
            normalize_for_diff(str(imp.stmt.text, "utf8")) for imp in imports2
        )

        # Compare normalized imports
        if import_strs1 != import_strs2:
            return False

        # Get non-import code
        non_import_code1 = code1
        non_import_code2 = code2
        for imp in imports1:
            non_import_code1 = non_import_code1.replace(str(imp.stmt.text, "utf8"), "")
        for imp in imports2:
            non_import_code2 = non_import_code2.replace(str(imp.stmt.text, "utf8"), "")

        # Compare the non-import code using compare_asts
        return compare_asts(
            non_import_code1, non_import_code2, lang, path, ignore_comments
        )
    except ParsingFailedError:
        # If parsing fails, let compare_asts handle it
        return compare_asts(code1, code2, lang, path, ignore_comments)
    except Exception:
        return None

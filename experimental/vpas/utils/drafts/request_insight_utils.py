from typing import List, Dict
import os
import argparse
import json
import datetime
import pydantic
import grpc
import requests
import http.cookiejar
from base.augment_client.client import AugmentClient
from experimental.dirk.iap import make_iap_request
from services.token_exchange.client.client import GrpcTokenExchangeClient
from services.token_exchange import token_exchange_pb2

AUGMENT_TOKEN: str = os.environ.get("AUGMENT_TOKEN")
assert AUGMENT_TOKEN


def auth_metadata_plugin(context, callback):
    """Add authorization metadata to gRPC requests."""
    callback([("authorization", f"Bearer {AUGMENT_TOKEN}")], None)


def get_service_token(tenant_name: str, namespace: str) -> str:
    """Get a service token using IAP token."""
    # For dev environments, use dev token exchange endpoint
    if namespace.startswith("dev-"):
        token_exchange_endpoint = (
            f"token-exchange-central-svc.{namespace}.svc.cluster.local:443"
        )
    else:
        token_exchange_endpoint = (
            "token-exchange-central-svc.staging-shard-0.svc.cluster.local:443"
        )

    # Initialize token exchange client with SSL credentials and auth metadata
    ssl_creds = grpc.ssl_channel_credentials()
    auth_creds = grpc.metadata_call_credentials(auth_metadata_plugin)
    composite_creds = grpc.composite_channel_credentials(ssl_creds, auth_creds)

    token_exchange_client = GrpcTokenExchangeClient.create(
        endpoint=token_exchange_endpoint,
        namespace=namespace,
        central_client_credentials=composite_creds,
    )

    # Get service token using IAP token
    service_token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_name,
        scopes=[
            token_exchange_pb2.REQUEST_R,  # Read request access
            token_exchange_pb2.REQUEST_RW,  # Write request access
        ],
    )

    return service_token.get_secret_value()


def fetch_request(request_id: str, tenant_name: str, namespace: str):
    """Fetch a request from the request insight API.

    Args:
        request_id: The ID of the request to fetch
        tenant_name: The name of the tenant
        namespace: The namespace (e.g. staging-shard-0, dev-*)

    Returns:
        Dict containing the request data
    """
    # Handle special cases for tenant/namespace mapping
    if namespace.startswith("dev-"):
        tenant_name = "augment"
        # For dev environments, use .dev.augmentcode.com
        base_url = f"https://support.{namespace}.t.us-central1.dev.augmentcode.com"
    elif tenant_name == "dogfood-shard":
        namespace = "staging-shard-0"
        base_url = f"https://support.{namespace}.t.us-central1.prod.augmentcode.com"
    else:
        base_url = f"https://support.{namespace}.t.us-central1.prod.augmentcode.com"

    # Get service token
    # service_token = get_service_token(tenant_name, namespace)

    # # Initialize AugmentClient with service token
    # client = AugmentClient(
    #     url=base_url, token=service_token, user_agent="request_insight_utils/0"
    # )

    # # Make the API request
    # try:
    #     response, _ = client._post(
    #         f"api/tenant/{tenant_name}/request/{request_id}",
    #         json={},
    #     )

    #     if not response.ok:
    #         raise ValueError(
    #             f"Failed to fetch request {request_id}: {response.status_code} {response.text}"
    #         )

    #     return response.json()
    # except Exception as e:
    #     raise ValueError(f"Error fetching request {request_id}: {str(e)}")

    # Make the API request
    client_id = (
        "1035750215372-069tveaouqi1k0oral031seb0som99u6.apps.googleusercontent.com"
    )

    try:
        url = f"{base_url}/api/tenant/{tenant_name}/request/{request_id}"
        response = make_iap_request(
            url=url,
            client_id=client_id,
            method="GET",
            json={},
        )
        return response
    except Exception as e:
        raise ValueError(f"Error fetching request {request_id}: {str(e)}")


def fetch_edit_request(request_id: str, tenant_name: str, namespace: str):
    """Fetch an edit request from the request insight API."""
    full_request = fetch_request(request_id, tenant_name, namespace)
    if "instructionHostRequest" in full_request:
        return full_request["instructionHostRequest"]["request"]
    raise ValueError(f"Request {request_id} does not have an edit host request")


def main():
    """Main function to fetch request details."""
    parser = argparse.ArgumentParser(
        description="Fetch request details from request insight API"
    )
    parser.add_argument("--request-id", required=True, help="Request ID to fetch")
    parser.add_argument(
        "--tenant-name", default="augment", help="Tenant name (default: augment)"
    )
    parser.add_argument(
        "--namespace", default="dev-vpas", help="Namespace (default: dev-vpas)"
    )
    parser.add_argument(
        "--edit-only", action="store_true", help="Only fetch edit request data"
    )
    parser.add_argument(
        "--pretty", action="store_true", help="Pretty print the JSON output"
    )

    args = parser.parse_args()

    try:
        print(f"Fetching request {args.request_id} from {args.namespace}...")
        if args.edit_only:
            result = fetch_edit_request(
                args.request_id, args.tenant_name, args.namespace
            )
        else:
            result = fetch_request(args.request_id, args.tenant_name, args.namespace)
        print("Request fetched successfully.")
        if args.pretty:
            print(json.dumps(result, indent=2))
        else:
            print(result)
    except ValueError as e:
        print(f"Error: {e}")
        exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to retrieve the content of a file at the time a specific chat request was made.
"""

import argparse
from pathlib import Path
from typing import Optional, Tuple

from base.datasets.tenants import get_tenant, DOGFOOD_SHARD
from base.datasets.gcs_client import GCSRequestInsightFetcher, Request
from research.tools.chat_replay.replay_utils import BlobGetter


def get_file_content_at_request_time(
    request_id: str, file_path: str, tenant_name: str = "dogfood-shard"
) -> Tuple[Optional[str], Optional[str]]:
    """
    Retrieve the content of a file at the time a specific chat request was made.

    Args:
        request_id: The ID of the chat request
        file_path: The path of the file to retrieve
        tenant_name: The tenant name to query for (default: dogfood-shard)

    Returns:
        A tuple of (file_content, error_message)
        If successful, file_content will contain the file content and error_message will be None
        If unsuccessful, file_content will be None and error_message will contain the error
    """
    try:
        # Get the tenant information
        tenant = get_tenant(tenant_name)

        # Create a request fetcher to get the request events
        request_fetcher = GCSRequestInsightFetcher.from_tenant(tenant)
        request_info = request_fetcher.get_request(request_id=request_id)

        if not request_info or not request_info.events:
            return None, f"No request found with ID {request_id}"

        # Create a blob getter to retrieve blob content
        blob_getter = BlobGetter(tenant)

        # Try to get the chat request directly using the BlobGetter
        chat_request = blob_getter.get_chat_request(request_id)

        if chat_request and hasattr(chat_request, "doc_ids") and chat_request.doc_ids:
            # Get all documents (blobs) for this request
            documents = blob_getter.get_blobs(chat_request.doc_ids)

            # Find the document that matches the file path
            for doc in documents:
                if doc.path == file_path:
                    return doc.content, None

            return None, f"File {file_path} not found in the request's blobs"

        # Fallback: Try to extract the chat_host_request event
        chat_host_request = None
        for event in request_info.events:
            if event.HasField("chat_host_request"):
                chat_host_request = event.chat_host_request
                break

        if not chat_host_request:
            return None, "No chat_host_request found for this request_id"

        # Get the blob names from the checkpoint in the request
        doc_ids = blob_getter.get_blob_names_from_chat_request(chat_host_request)

        if not doc_ids:
            return None, "No document IDs found in the chat request"

        # Get all documents (blobs) for this request
        documents = blob_getter.get_blobs(doc_ids)

        # Find the document that matches the file path
        for doc in documents:
            if doc.path == file_path:
                return doc.content, None

        return None, f"File {file_path} not found in the request's blobs"

    except Exception as e:
        return None, f"Error retrieving file content: {str(e)}"


def main():
    """Main function to run the file content retrieval."""
    parser = argparse.ArgumentParser(
        description="Retrieve the content of a file at the time a specific chat request was made",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""Examples:
  # Retrieve file content for a specific request ID
  python get_file_at_request_time.py --request-id b91c2a24-074b-4961-8c78-24350118c6f3 --file-path experimental/vpas/agent/edit_agent_eval/str_replace_editor_analysis.py

  # Retrieve file content for a specific request ID with a different tenant
  python get_file_at_request_time.py --request-id b91c2a24-074b-4961-8c78-24350118c6f3 --file-path experimental/vpas/agent/edit_agent_eval/str_replace_editor_analysis.py --tenant custom-tenant-name
""",
    )

    parser.add_argument(
        "--request-id",
        type=str,
        required=True,
        help="The ID of the chat request",
    )

    parser.add_argument(
        "--file-path",
        type=str,
        required=True,
        help="The path of the file to retrieve",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default=DOGFOOD_SHARD,
        help=f"Tenant name to query for (default: {DOGFOOD_SHARD})",
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Path to save the retrieved file content (optional)",
    )

    args = parser.parse_args()

    print(f"Retrieving file content for request ID: {args.request_id}")
    print(f"File path: {args.file_path}")
    print(f"Tenant: {args.tenant}")

    content, error = get_file_content_at_request_time(
        request_id=args.request_id,
        file_path=args.file_path,
        tenant_name=args.tenant,
    )

    if error:
        print(f"Error: {error}")
        return 1

    if content:
        print(f"Successfully retrieved file content ({len(content)} characters)")

        if args.output:
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            output_path.write_text(content)
            print(f"File content saved to: {args.output}")
        else:
            print("\n--- File Content ---\n")
            print(content)
            print("\n--- End of File Content ---")

        return 0
    else:
        print("No content retrieved")
        return 1


if __name__ == "__main__":
    main()

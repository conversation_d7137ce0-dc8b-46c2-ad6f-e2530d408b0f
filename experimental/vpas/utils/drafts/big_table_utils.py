from services.lib.request_context.request_context import RequestContext
from services.request_insight.bigtable_proxy_reader import BigtableProxyReader
from services.bigtable_proxy.client.client import BigtableProxyClient
import services.request_insight.request_insight_pb2 as request_insight_pb2
import uuid
import argparse
import logging
from base.logging.console_logging import setup_console_logging
import sys


def read_request_from_bigtable(
    request_id: str,
) -> request_insight_pb2.RequestEvent | None:
    """Read a request from BigTable for dev-guy namespace and augment tenant.

    Args:
        request_id: The UUID of the request to fetch

    Returns:
        RequestEvent proto if found, None otherwise
    """
    # Create request context for dev namespace
    request_context = RequestContext(namespace="dev-vpas")

    # Initialize BigTable proxy client and reader
    proxy_client = BigtableProxyClient()
    reader = BigtableProxyReader(proxy_client)

    # Convert string request_id to UUID
    request_uuid = uuid.UUID(request_id)

    # Read request info using tenant_id="augment" for augment tenant
    events = reader.get_request_info(
        request_context=request_context, tenant_id="augment", request_id=request_uuid
    )

    if events is None:
        return None

    # Return first event if found
    try:
        return next(events)
    except StopIteration:
        return None


def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--request-id",
        type=str,
        required=True,
        help="UUID of the request to fetch from BigTable",
    )

    args = parser.parse_args()

    try:
        event = read_request_from_bigtable(args.request_id)
        if event:
            logging.info("Found request event: %s", event)
        else:
            logging.error("No event found for request ID: %s", args.request_id)
            sys.exit(1)
    except ValueError as e:
        logging.error("Invalid request ID format: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

from dataclasses import dataclass
from typing import Any


@dataclass
class IndexedEntriesExtractionResult:
    entries: list[dict[str, Any]]
    missing_keys: list[str]
    extra_keys: list[str]


def extract_indexed_entries(
    d: dict[str, Any], keys: list[str]
) -> IndexedEntriesExtractionResult:
    """Extract indexed entries from a dict.

    Args:
        d: The dict to extract from.
        keys: The keys to extract.

    example inputs:
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
        "key_a_2": "value_a_2",
        "extra_key": "extra_value",
    }
    keys = ["key_a", "key_b"]

    example output:
    IndexedEntriesExtractionResult(
        entries=[
            {"key_a": "value_a_1", "key_b": "value_b_1"},
            {"key_a": "value_a_2", "key_b": None},
        ],
        missing_keys=["key_b_2"],
        extra_keys=["extra_key"],
    )

    Index should start from 1.
    """
    entries = []
    missing_keys = []
    extra_keys = []
    max_index = 0

    for key, value in d.items():
        parts = key.rsplit("_", 1)
        if len(parts) == 2 and parts[0] in keys:
            base_key, index = parts
            try:
                index = int(index)
                max_index = max(max_index, index)
                while len(entries) < index:
                    entries.append({k: None for k in keys})
                entries[index - 1][base_key] = value
            except ValueError:
                extra_keys.append(key)
        else:
            extra_keys.append(key)

    for i in range(1, max_index + 1):
        for key in keys:
            if f"{key}_{i}" not in d:
                missing_keys.append(f"{key}_{i}")

    return IndexedEntriesExtractionResult(entries, missing_keys, extra_keys)

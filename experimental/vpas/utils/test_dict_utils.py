from experimental.vpas.utils.dict_utils import (
    extract_indexed_entries,
    IndexedEntriesExtractionResult,
)


def test_extract_indexed_entries_basic():
    """Test the basic functionality with the example from the docstring."""
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
        "key_a_2": "value_a_2",
        "extra_key": "extra_value",
    }
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 2
    assert result.entries[0] == {"key_a": "value_a_1", "key_b": "value_b_1"}
    assert result.entries[1] == {"key_a": "value_a_2", "key_b": None}
    assert result.missing_keys == ["key_b_2"]
    assert result.extra_keys == ["extra_key"]


def test_extract_indexed_entries_empty_dict():
    """Test with an empty dictionary."""
    d = {}
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 0
    assert result.missing_keys == []
    assert result.extra_keys == []


def test_extract_indexed_entries_no_matching_keys():
    """Test with a dictionary that has no matching keys."""
    d = {
        "unrelated_1": "value1",
        "another_key": "value2",
    }
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 0
    assert result.missing_keys == []
    assert result.extra_keys == ["unrelated_1", "another_key"]


def test_extract_indexed_entries_non_integer_indices():
    """Test with keys that have non-integer indices."""
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
        "key_a_abc": "invalid_index",
        "key_b_xyz": "another_invalid_index",
    }
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 1
    assert result.entries[0] == {"key_a": "value_a_1", "key_b": "value_b_1"}
    assert result.missing_keys == []
    assert result.extra_keys == ["key_a_abc", "key_b_xyz"]


def test_extract_indexed_entries_gaps_in_indices():
    """Test with gaps in the indices."""
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
        "key_a_3": "value_a_3",
        "key_b_3": "value_b_3",
    }
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 3
    assert result.entries[0] == {"key_a": "value_a_1", "key_b": "value_b_1"}
    assert result.entries[1] == {"key_a": None, "key_b": None}
    assert result.entries[2] == {"key_a": "value_a_3", "key_b": "value_b_3"}
    assert sorted(result.missing_keys) == ["key_a_2", "key_b_2"]
    assert result.extra_keys == []


def test_extract_indexed_entries_partial_keys():
    """Test with only some of the requested keys present."""
    d = {
        "key_a_1": "value_a_1",
        "key_c_1": "value_c_1",
        "key_a_2": "value_a_2",
    }
    keys = ["key_a", "key_b", "key_c"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 2
    assert result.entries[0] == {
        "key_a": "value_a_1",
        "key_b": None,
        "key_c": "value_c_1",
    }
    assert result.entries[1] == {"key_a": "value_a_2", "key_b": None, "key_c": None}
    assert sorted(result.missing_keys) == ["key_b_1", "key_b_2", "key_c_2"]
    assert result.extra_keys == []


def test_extract_indexed_entries_large_indices():
    """Test with very large indices."""
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
        "key_a_1000": "value_a_1000",
    }
    keys = ["key_a", "key_b"]

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 1000
    assert result.entries[0] == {"key_a": "value_a_1", "key_b": "value_b_1"}
    assert result.entries[999] == {"key_a": "value_a_1000", "key_b": None}

    # Check that all entries between index 1 and 999 have None values
    for i in range(1, 999):
        assert result.entries[i] == {"key_a": None, "key_b": None}

    # Check missing keys - there should be missing keys for both key_a and key_b for indices 2-999,
    # plus key_b_1000, so 2 * 998 + 1 = 1997 missing keys
    assert len(result.missing_keys) == 1997
    assert "key_b_1000" in result.missing_keys
    assert "key_a_500" in result.missing_keys
    assert "key_b_500" in result.missing_keys
    assert result.extra_keys == []


def test_extract_indexed_entries_empty_keys_list():
    """Test with an empty keys list."""
    d = {
        "key_a_1": "value_a_1",
        "key_b_1": "value_b_1",
    }
    keys = []

    result = extract_indexed_entries(d, keys)

    assert isinstance(result, IndexedEntriesExtractionResult)
    assert len(result.entries) == 0
    assert result.missing_keys == []
    assert sorted(result.extra_keys) == ["key_a_1", "key_b_1"]

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tree_sitter as ts\n", "from tree_sitter_languages import get_language\n", "\n", "lang = get_language(\"python\")\n", "\n", "parser = ts.<PERSON>()\n", "parser.set_language(lang)\n", "\n", "tree = parser.parse(bytes(\"\"\"\n", "def foo():\n", "    if bar:\n", "        baz()\n", "\"\"\", \"utf8\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tree.root_node.children[0]."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
"""An interactive autonomous agent that provides a command-line interface for interacting with an AI assistant.

This module implements a system that allows users to interact with an AI assistant that has access
to a codebase through various tools. The agent can:
- Read and write files in the workspace
- Execute commands
- Search through codebase and documentation
- Store and retrieve knowledge about the codebase
- Track recent changes
- Handle interruptions gracefully

The system uses the Anthropic Claude API for language model capabilities and provides various tools
for codebase interaction through a command-line interface.

Key components:
- WorkspaceManager: Manages file operations and workspace state
- Agent: Core agent implementation that handles user instructions
- Various tools: File operations, command execution, codebase search, etc.
- Command-line interface with history and tab completion

"""

import argparse
import asyncio
import hashlib
import http.server
import json
import logging
import os
import pickle
import platform
import queue
import random
import re
import readline
import signal
import string
import subprocess
import sys
import threading
import time
from dataclasses import dataclass
from io import StringIO
from pathlib import Path
from typing import IO, Any, Callable, List, Optional
from urllib.parse import urlencode

import aiohttp
import yaml
from google.protobuf.json_format import MessageTo<PERSON>son
from gql import Client
from gql.transport.requests import RequestsHTTPTransport
from termcolor import colored

from base.augment_client.client import (
    AugmentClient,
    RemoteToolId,
    ToolAvailabilityStatus,
    ToolSafety,
)
from experimental.guy.agent_qa.agent_coordinator_interface import AgentCoordinator
from experimental.guy.agent_qa.agent_mode import AgentMode, ConstantModeProvider
from experimental.guy.agent_qa.agent_modes import (
    WHEN_TO_USE_ARCHITECT,
    WHEN_TO_USE_IMPLEMENTOR,
    WHEN_TO_USE_RESEARCHER,
    create_architect_mode,
    create_implementor_mode,
    create_researcher_mode,
)
from experimental.guy.agent_qa.agent_name import generate_agent_name
from experimental.guy.agent_qa.agents import DEFAULT_TOOLS, Agent, PromptedLLMAgent
from experimental.guy.agent_qa.augment_llm_client import AugmentLLMClient
from experimental.guy.agent_qa.bench_cli.auth_token import TokenManager
from experimental.guy.agent_qa.bench_cli.config import Config, Environment
from experimental.guy.agent_qa.bench_interface import BenchInterface
from experimental.guy.agent_qa.builtin_tools import (
    BackendCodebaseRetrievalTool,
    BackendEditFileAgent,
    ClarifyTool,
    CodebaseKnowledgeTool,
    CompleteTool,
    EditFileAgent,
    EditFileTool,
    FileEditClient,
    LLMTool,
    QueryOnlyDocumentIndex,
    ReadFileTool,
    SaveFileTool,
)
from experimental.guy.agent_qa.changes import (
    RecentChangesMode,
    RecentChangesProvider,
    RecentChangesTool,
)
from experimental.guy.agent_qa.command_approval import CommandApprovalManager
from experimental.guy.agent_qa.file_edit.edit_file_agent_type import (
    EditFileAgentType,
    create_edit_agent,
)
from experimental.guy.agent_qa.edit.edit_multi_file_agent import (
    EditMultiFileAgent,
    build_system_prompt_for_edit_multi_file_agent,
)
from experimental.guy.agent_qa.file_edit.forger_udiff_with_desc_edit_file_agent import (
    ForgerUdiffWithDescEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.str_replace_edit_file_agent import (
    StrReplaceEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool import (
    StrReplaceEditorTool,
)
from experimental.guy.agent_qa.integration_warnings import IntegrationWarnings
from experimental.guy.agent_qa.memories import (
    Memories,
    RememberTool,
    handle_remember_command,
    maybe_create_memory_from_instruction,
)
from experimental.guy.agent_qa.notion_tools import NotionPageTool
from experimental.guy.agent_qa.process_tools_v2 import create_process_tools
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_dev_deployment_api_proxy_url,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.slack_agent_coordinator import SlackAgentCoordinator
from experimental.guy.agent_qa.state_manager import StateManager
from experimental.guy.agent_qa.tool_loader import ToolLoader
from experimental.guy.agent_qa.tools.agent_coordinator_tool import AgentCoordinatorTool
from experimental.guy.agent_qa.tools.bash_tool import (
    BashTool,
    create_bash_tool,
    create_docker_bash_tool,
)
from experimental.guy.agent_qa.tools.code_clipboard_tool import CodeClipboardTool
from experimental.guy.agent_qa.tools.linear_tool import LinearTool
from experimental.guy.agent_qa.tools.mode_switch_tool import AgentModeSwitcher
from experimental.guy.agent_qa.tools.read_file_outline_tool import ReadFileOutlineTool
from experimental.guy.agent_qa.tools.remote_tool import (
    RemoteTool,
    create_remote_tools,
    list_remote_tools,
)
from experimental.guy.agent_qa.tools.scratchpad_tool import ScratchpadTool
from experimental.guy.agent_qa.tools.sequential_thinking_tool import (
    SequentialThinkingTool,
)
from experimental.guy.agent_qa.tools.slack_notification_tool import (
    SlackNotificationTool,
)
from experimental.guy.agent_qa.tools.slack_search_tool import SlackSearchTool
from experimental.guy.agent_qa.tools.web_page_fetcher_tool import WebPageFetcherTool
from experimental.guy.agent_qa.tools.web_search import (
    WebSearchError,
    get_search_credentials,
)
from experimental.guy.agent_qa.tools.web_search_tool import WebSearchTool
from experimental.guy.agent_qa.user_confirmation import (
    InteractiveUserConfirmationProvider,
)
from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManagerImpl,
    WorkspaceManagerListener,
    WorkspaceUpdateListener,
)

try:
    # FIXME: Avoid requiring deepseek
    from experimental.michiel.research.agentqa.tools_new import (
        HighLevelCodebaseMultiRetrievalAgent,
        RouterTool,
    )
except ImportError:
    HighLevelCodebaseMultiRetrievalAgent = None
    RouterTool = (None,)
from experimental.guy.agent_qa.chat_history import get_chat_history
from experimental.guy.agent_qa.state_manager_tool_call_logger import (
    StateManagerToolCallLoggerListener,
    load_tool_call_logger_state,
)
from research.agents.changed_file import ChangedFile
from research.agents.tools import (
    LoggingLLMClient,
    ToolCallLogger,
)
from research.core.constants import AUGMENT_EFS_ROOT
from research.llm_apis.llm_client import (
    AnthropicDirectClient,
    AnthropicVertexClient,
    FireworksClient,
    LLMClient,
)
from research.llm_apis.tool_support_wrapper import ToolSupportWrapper
from services.api_proxy.public_api_pb2 import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
    GetRemoteAgentChatHistoryResponse,
    RemoteAgentExchange,
)

logger = logging.getLogger(__name__)

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))


def validate_slack_email(email: str) -> str:
    """Validate that the provided string is a valid email address.

    Args:
        email: Email address to validate

    Returns:
        The email address if valid

    Raises:
        argparse.ArgumentTypeError if email is invalid
    """
    # Basic email regex pattern
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    if not re.match(pattern, email):
        raise argparse.ArgumentTypeError(f"Invalid email address: {email}")
    return email


class EditsUpdateListener(WorkspaceUpdateListener):
    """Saves diffs of agent edits to a file.

    This listener tracks changes made by the agent to the workspace and saves
    the diffs to a specified output file. This is useful for auditing and
    reviewing the agent's changes.
    """

    def __init__(self, output_path: Path):
        """Initialize the listener.

        Args:
            output_path: Path to save diffs to
        """
        self.output_path = output_path
        self.workspace_manager = None

    def set_workspace_manager(self, workspace_manager: WorkspaceManagerImpl):
        """Set the workspace manager after it's created.

        Args:
            workspace_manager: The workspace manager to get diffs from
        """
        self.workspace_manager = workspace_manager

    def on_update(self, workspace_root: Path):
        """Save diff between current state and most recent snapshot.

        Args:
            workspace_root: Root directory of the workspace
        """
        if not self.workspace_manager:
            return

        # Skip if there are no snapshots yet
        if not self.workspace_manager._snapshots:
            return

        # Get diff between most recent snapshot and current state
        diff = self.workspace_manager.diff_with_current(-1)

        # Convert PatchSet to string and append
        diff_str = str(diff)
        if diff_str.strip():  # Only write if there are actual changes
            with self.output_path.open("a") as f:
                f.write(diff_str)


def get_git_username() -> str:
    """Get username from git config email.

    Extracts the username portion from the user's git email configuration.
    For example, from "<EMAIL>" returns "user".

    Returns:
        str: The extracted username, or "unknown" if git config is not available.
    """
    try:
        email = subprocess.check_output(
            ["git", "config", "user.email"],
            universal_newlines=True,
            stderr=subprocess.DEVNULL,
        ).strip()
        return email.split("@")[0]
    except subprocess.CalledProcessError:
        return "unknown"


def get_git_repo_root(path: Path = Path(".")) -> Optional[Path]:
    """Get the root directory of the git repository.

    Args:
        path: Starting path to search from (default: current directory)

    Returns:
        Path to the git repository root, or None if not in a git repository
    """
    try:
        repo_root = subprocess.check_output(
            ["git", "rev-parse", "--show-toplevel"],
            cwd=path,
            universal_newlines=True,
            stderr=subprocess.DEVNULL,
        ).strip()
        return Path(repo_root)
    except subprocess.CalledProcessError:
        return None


def get_linear_api_key(cache_dir: Path) -> Optional[str]:
    """Get Linear API key from cache directory.

    Args:
        cache_dir: Agent cache directory

    Returns:
        API key if found, None otherwise
    """
    token_path = cache_dir / "linear_api_token"
    if token_path.exists():
        return token_path.read_text().strip()
    return None


def get_slack_bot_token(cache_dir: Path) -> Optional[str]:
    """Get Slack bot token (xoxb-) from cache directory.

    Args:
        cache_dir: Agent cache directory

    Returns:
        Bot token if found, None otherwise
    """
    token_path = cache_dir / "slack_bot_token"
    if token_path.exists():
        return token_path.read_text().strip()
    return None


def get_slack_user_token(cache_dir: Path) -> Optional[str]:
    """Get the Slack user token from the cache directory.

    The user token (xoxp-) is required for search operations as the search.messages
    API endpoint only works with user tokens, regardless of bot token scopes.
    """
    token_file = cache_dir / "slack_user_token"
    if not token_file.exists():
        return None
    return token_file.read_text().strip()


def get_git_branch_name(path: Path = Path(".")) -> Optional[str]:
    """Get the current git branch name.

    Args:
        path: Path in the git repository (default: current directory)

    Returns:
        The current branch name, or None if not in a git repository
    """
    try:
        branch = subprocess.check_output(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"],
            cwd=path,
            universal_newlines=True,
            stderr=subprocess.DEVNULL,
        ).strip()
        return branch
    except subprocess.CalledProcessError:
        return None


class PromptAutocomplete:
    """Provides path autocompletion functionality for the command-line interface.

    This class implements tab completion for file paths in the workspace,
    making it easier for users to specify files when interacting with the agent.
    """

    def __init__(self, workspace_manager: WorkspaceManagerImpl):
        self.workspace_manager = workspace_manager

    def get_workspace_paths(self) -> list[str]:
        """Get paths relative to workspace root."""
        try:
            return list(
                sorted(map(str, self.workspace_manager.get_paths(relative=True)))
            )
        except Exception:
            return []

    def path_completer(self, text: str, state: int) -> str | None:
        """Completer function for readline that completes paths."""
        if state == 0:
            # Get all available paths
            paths = self.get_workspace_paths()

            # Filter paths that match our text
            if text:
                self.matches = [p for p in paths if p.startswith(text)]
            else:
                self.matches = paths

        try:
            return self.matches[state]
        except IndexError:
            return None

    def setup_readline(self):
        """Setup readline with path completion."""
        # Set the completer function
        readline.set_completer(self.path_completer)

        # Set the completer delimiters - we want to keep / for paths
        readline.set_completer_delims(" \t\n`@#$%^&*()=+[{]}\\|;:'\",<>?")

        # Use platform-specific key bindings
        if sys.platform == "darwin":  # macOS
            readline.parse_and_bind("bind ^I rl_complete")
        else:  # Linux and others
            readline.parse_and_bind("tab: complete")


def get_workspace_hash(workspace_path: Path) -> str:
    """Generate MD5 hash of the workspace path."""
    return hashlib.md5(str(workspace_path).encode()).hexdigest()


def setup_history(cache_dir: Path, workspace_root: Path):
    """Set up command history with readline.

    Configures readline to:
    - Save command history to a workspace-specific file
    - Load previous history on startup
    - Enable arrow key navigation
    - Limit history size

    Args:
        cache_dir: Directory for agent cache files
        workspace_root: Root directory of the workspace
    """
    # Create cache directory if it doesn't exist
    cache_dir.mkdir(parents=True, exist_ok=True)
    (cache_dir / "instruction_histories").mkdir(parents=True, exist_ok=True)

    workspace_hash = hashlib.md5(str(workspace_root).encode()).hexdigest()
    history_file = (
        cache_dir / "instruction_histories" / f"instruction_history_{workspace_hash}"
    )
    try:
        # Create history file if it doesn't exist
        history_file.touch()
        # Read the history file
        readline.read_history_file(str(history_file))
        # Set maximum number of items to save
        readline.set_history_length(1000)
        # Enable arrow key navigation
        readline.parse_and_bind('"\e[A": history-search-backward')  # Up arrow
        readline.parse_and_bind('"\e[B": history-search-forward')  # Down arrow
        # Make sure history is saved on program close
        import atexit

        atexit.register(readline.write_history_file, str(history_file))
    except Exception as e:
        print(colored(f"Warning: Could not set up history: {e}", "yellow"))


class PrintingSyncListener(WorkspaceManagerListener, WorkspaceUpdateListener):
    """A simple listener that prints sync status messages for both workspace and Notion."""

    def __init__(self, notion_root: Path):
        """Initialize the listener.

        Args:
            notion_root: Root path of the Notion exports
        """
        self._sync_start_time = None
        self._sync_type = None
        self._notion_root = notion_root

    def sync_started(self, workspace_root: Path):
        """Called when workspace sync starts."""
        self._sync_type = "workspace"
        print(colored("[syncing workspace]", "blue"), file=sys.stderr)
        self._sync_start_time = time.time()

    def sync_finished(self, workspace_root: Path):
        """Called when workspace sync finishes."""
        if self._sync_start_time is not None:
            duration = time.time() - self._sync_start_time
            if duration >= 0.1:  # Only show duration if it's significant
                print(
                    colored(f"[syncing complete in {duration:.1f}s]", "blue"),
                    file=sys.stderr,
                )
            self._sync_start_time = None

    def on_update(self, workspace_root: Path):
        """Called when a workspace is updated (for both workspace and Notion)."""
        if workspace_root == self._notion_root:
            print(colored("[syncing notion]", "blue"))


def load_tools_from_yaml(
    yaml_files: list[Path],
    logging_client: LoggingLLMClient,
    tool_call_logger: ToolCallLogger,
    available_tools: list[LLMTool],
    max_output_tokens_per_turn: int = 8192,
    max_turns: int = 10,
    workspace_manager: Optional[WorkspaceManagerImpl] = None,
    system_prompt: str = "",
) -> list[PromptedLLMAgent]:
    """Load tools from YAML configuration files.

    Loads and instantiates tool configurations from YAML files. Each tool is initialized
    with the provided LLM client, logger, and other necessary components. Handles errors
    gracefully and reports any issues loading tools. Properly handles dependencies between
    tools defined in YAML files.

    Args:
        yaml_files: List of YAML files containing tool configurations
        logging_client: The LLM client to use
        tool_call_logger: The tool call logger to use
        available_tools: List of already instantiated tools that can be used by new tools
        max_output_tokens_per_turn: Maximum tokens per turn
        max_turns: Maximum number of turns
        workspace_manager: Optional workspace manager instance
        system_prompt: Optional system prompt prefix to pass to tools

    Returns:
        list[PromptedLLMAgent]: List of successfully loaded tools

    Raises:
        ValueError: If there are circular dependencies between tools
    """
    loader = ToolLoader(
        yaml_files=yaml_files,
        logging_client=logging_client,
        tool_call_logger=tool_call_logger,
        builtin_tools=available_tools,
        max_output_tokens_per_turn=max_output_tokens_per_turn,
        max_turns=max_turns,
        workspace_manager=workspace_manager,
        system_prompt=system_prompt,
    )

    try:
        # This will handle dependency resolution and instantiation
        tools = loader.load_all_tools()

        # Return only the newly created tools, not the builtin ones
        return [t for t in tools if t not in available_tools]
    except Exception as e:
        print(colored(f"Error loading tools: {e}", "red"))
        raise


def call_tool(tool_name: str, tool_input: str, tools: list[PromptedLLMAgent]) -> str:
    """Call a specific tool with the given input.

    Finds a tool by name and executes it with the provided JSON input. Used for
    direct tool invocation without going through the agent interface.

    Args:
        tool_name: Name of the tool to call
        tool_input: JSON string containing the tool input
        tools: List of available tools

    Returns:
        str: The tool's output

    Raises:
        ValueError: If tool not found or input is invalid JSON
    """
    # Find the tool
    tool = next((t for t in tools if t.name == tool_name), None)
    if not tool:
        raise ValueError(
            f"Tool '{tool_name}' not found. Available tools: {', '.join(t.name for t in tools)}"
        )

    try:
        # Parse the JSON input
        input_dict = json.loads(tool_input)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON input: {e}")

    # Call the tool
    try:
        return tool.run(input_dict)
    except Exception as e:
        raise ValueError(f"Error calling tool: {e}")


def get_workspace_prompt_builder(
    workspace_root: Path,
    knowledge_tool: CodebaseKnowledgeTool,
    recent_changes_provider: Optional[RecentChangesProvider] = None,
    pr_changes: bool = False,
    memories: Optional[Memories] = None,
) -> Callable[[], str]:
    def build_workspace_prompt() -> str:
        """Build the system prompt suffix that contains workspace-specific information.

        Args:
            workspace_root: The root directory of the workspace
            knowledge_tool: Tool for accessing codebase knowledge
            recent_changes_provider: Optional provider for recent changes
            pr_changes: Whether to include PR changes in the prompt
            memories: Optional memories to include

        Returns:
            The constructed system prompt suffix
        """
        workspace_prompt = f"""\
{memories.get_memories() if memories else ""}

Current working directory: {workspace_root}
Current operating system: {platform.system()}
"""

        if knowledge_tool.has_knowledge():
            workspace_prompt += f"""\

# Here are useful knowledge snippets about this codebase:

<augment-knowledge>
{knowledge_tool.get_knowledge()}
</augment-knowledge>
"""

        if pr_changes and recent_changes_provider:
            workspace_prompt += f"""\

# Here are the changes made so far in this branch or PR:

<augment-pr-changes>
{recent_changes_provider.get_recent_changes(RecentChangesMode.SINCE_BRANCH_POINT)}
</augment-pr-changes>
"""

        return workspace_prompt

    return build_workspace_prompt


def build_system_prompt(
    workspace_root: Path,
    knowledge_tool: CodebaseKnowledgeTool,
    recent_changes_provider: Optional[RecentChangesProvider] = None,
    pr_changes: bool = False,
    memories: Optional[Memories] = None,
    swebench_mode: bool = False,
    swebench_sparse_system_prompt: bool = False,
    edit_multi_file_agent: bool = False,
) -> str:
    """Build the system prompt for the agent.

    Args:
        workspace_root: The root directory of the workspace
        knowledge_tool: Tool for accessing codebase knowledge
        recent_changes_provider: Optional provider for recent changes
        pr_changes: Whether to include PR changes in the prompt
        memories: Optional memories to include
        swebench_mode: Whether to use the swebench system prompt
        swebench_sparse_system_prompt: Whether to use the sparse swebench system prompt
        edit_multi_file_agent: Whether to use the edit multi file agent

    Returns:
        The constructed system prompt
    """
    workspace_prompt_builder_fn = get_workspace_prompt_builder(
        workspace_root, knowledge_tool, recent_changes_provider, pr_changes, memories
    )
    workspace_prompt = workspace_prompt_builder_fn()

    if swebench_mode:
        if swebench_sparse_system_prompt:
            system_prompt = f"""\
You are an AI assistant helping a software engineer implement pull requests,
and you have access to tools to interact with the engineer's codebase.

Working directory: {workspace_root}
Operating system: {platform.system()}

"""
        else:
            if edit_multi_file_agent:
                system_prompt = f"""\
    You are an AI assistant helping a software engineer implement pull requests,
    and you have access to tools to interact with the engineer's codebase.

    Working directory: {workspace_root}
    Operating system: {platform.system()}

    Guidelines:
    - You are working in a codebase with other engineers and many different components. Be careful that changes you make in one component don't break other components.
    - When designing changes, implement them as a senior software engineer would. This means following best practices such as separating concerns and avoiding leaky interfaces.
    - When possible, choose the simpler solution.
    - Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
    - You should run relevant tests to verify that your changes work.
    - Delegate actually editing files to the {EditMultiFileAgent.name} tool.
    - {EditMultiFileAgent.name} tool can handle multi-file edits and perform retrieval, but cannot run tests.
    - Your role is to plan changes and verify {EditMultiFileAgent.name} solution by running tests (if tests are available)
    """
            else:
                system_prompt = f"""\
    You are an AI assistant helping a software engineer implement pull requests,
    and you have access to tools to interact with the engineer's codebase.

    Working directory: {workspace_root}
    Operating system: {platform.system()}

    Guidelines:
    - You are working in a codebase with other engineers and many different components. Be careful that changes you make in one component don't break other components.
    - When designing changes, implement them as a senior software engineer would. This means following best practices such as separating concerns and avoiding leaky interfaces.
    - When possible, choose the simpler solution.
    - Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
    - You should run relevant tests to verify that your changes work.

    """
    else:
        if edit_multi_file_agent:
            system_prompt = f"""\
    # Role
    You are an AI assistant, with access to the developer's codebase.
    You can read from and write to the codebase using the provided tools.
    Your role is to gather information, plan high-level designs, and execute commands, while delegating all code editing to the {EditMultiFileAgent.name} tool.

    # Preliminary tasks
    Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
    Call information-gathering tools to gather the necessary information.
    If you need information about the current state of the codebase, use the request_codebase_information tool.

    # Planning
    Once you have performed preliminary rounds of information-gathering, come up with a high-level plan for the actions you want to take.
    The plan doesn't needs to include low-level implementation details, which will be handled by the {EditMultiFileAgent.name} tool.
    Feel free to think about in a chain of thought first.
    If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.
    Once you have a plan, outline this plan to the user.
    Then, stop! Do not proceed with the changes! Wait for the user to agree to the plan before continuing.

    # Making edits via the {EditMultiFileAgent.name} tool
    Delegate all file editing to the {EditMultiFileAgent.name} tool. Do NOT make any edits yourself.
    {EditMultiFileAgent.name} tool can handle multi-file edits and perform retrieval, but cannot run tests.
    Your role is to plan changes and verify {EditMultiFileAgent.name} solution by running tests (if tests are available)

    # Following instructions
    Focus on doing what the user asks you to do.
    Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.
    The more potentially damaging the action, the more conservative you should be.
    For example, do NOT perform any of these actions without explicit permission from the user:
    - Committing or pushing code
    - Changing the status of a ticket
    - Merging a branch
    - Installing dependencies
    - Deploying code

    # Testing
    You are very good at writing unit tests and making them work. If you write
    code, suggest to the user to test the code by writing tests and running them.
    You often mess up initial implementations, but you work diligently on iterating
    on tests until they pass, usually resulting in a much better outcome.
    Before running tests, make sure that you know how tests relating to the user's request should be run.

    # Recovering from difficulties
    If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, ask the user for help.

    # Final
    After executing all the steps in the plan, reason out loud whether there are any futher changes that need to be made.
    If so, please repeat the planning process.
    If you have made code edits, suggest writing or updating tests and executing those tests to make sure the changes are correct.

    {workspace_prompt}

    # Summary of most important instructions
    - Search for information to carry out the user request
    - Always make a high-level plan before taking any action and let edit_agent handle the low-level editing tasks
    - Always ask the user if they agree with the plan before taking any action
    - Focus on following user instructions and ask before carrying out any actions beyond the user's instructions
    - If you find yourself repeatedly calling tools without making progress, ask the user for help
    """
        else:
            system_prompt = f"""\
    # Role
    You are an AI assistant, with access to the developer's codebase.
    You can read from and write to the codebase using the provided tools.

    # Preliminary tasks
    Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
    Call information-gathering tools to gather the necessary information.
    If you need information about the current state of the codebase, use the request_codebase_information tool.

    # Planning
    Once you have performed preliminary rounds of information-gathering, come up with a low-level, extremely detailed plan for the actions you want to take.
    Provide a bulleted list of each file you think you need to change.
    Be sure to be careful and exhaustive.
    Feel free to think about in a chain of thought first.
    If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.
    Once you have a plan, outline this plan to the user.
    Then, stop! Do not proceed with the changes! Wait for the user to agree to the plan before continuing.

    # Making edits
    When making edits, use the provided edit tool - do NOT just write a new file.
    Before calling the edit tool, ALWAYS first call the request_codebase_information tool
    asking for highly detailed information about the code you want to edit.
    Ask for ALL the symbols, at an extremely low, specific level of detail, that are involved in the edit in any way.
    Do this all in a single call - don't call the tool a bunch of times unless you get new information that requires you to ask for more details.
    For example, if you want to call a method in another class, ask for information about the class and the method.
    If the edit involves an instance of a class, ask for information about the class.
    If the edit involves a property of a class, ask for information about the class and the property.
    If several of the above apply, ask for all of them in a single call.
    When in any doubt, include the symbol or object.
    When making changes, be very conservative and respect the codebase.

    # Following instructions
    Focus on doing what the user asks you to do.
    Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.
    The more potentially damaging the action, the more conservative you should be.
    For example, do NOT perform any of these actions without explicit permission from the user:
    - Committing or pushing code
    - Changing the status of a ticket
    - Merging a branch
    - Installing dependencies
    - Deploying code

    # Testing
    You are very good at writing unit tests and making them work. If you write
    code, suggest to the user to test the code by writing tests and running them.
    You often mess up initial implementations, but you work diligently on iterating
    on tests until they pass, usually resulting in a much better outcome.
    Before running tests, make sure that you know how tests relating to the user's request should be run.

    # Recovering from difficulties
    If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, ask the user for help.

    # Final
    After executing all the steps in the plan, reason out loud whether there are any futher changes that need to be made.
    If so, please repeat the planning process.
    If you have made code edits, suggest writing or updating tests and executing those tests to make sure the changes are correct.

    {workspace_prompt}

    # Summary of most important instructions
    - Search for information to carry out the user request
    - Always make a detailed plan before taking any action
    - Always ask the user if they agree with the plan before taking any action
    - Make sure you have all the information before making edits
    - Focus on following user instructions and ask before carrying out any actions beyond the user's instructions
    - If you find yourself repeatedly calling tools without making progress, ask the user for help
    """

    return system_prompt


def show_in_pager(text: str, no_pager: bool = False):
    """Show text in a pager or print directly to console.

    Args:
        text: The text to show
        no_pager: If True, print directly to console instead of using a pager
    """
    if no_pager:
        # Print directly to console
        print(text)
        return

    # Use less with:
    # -R: show ANSI colors
    # -F: quit if content fits on one screen
    # -X: don't clear screen on exit
    pager = subprocess.Popen(
        ["less", "-RFX"],
        stdin=subprocess.PIPE,
        text=True,
    )
    try:
        pager.communicate(input=text)
    except BrokenPipeError:
        # User quit the pager
        pass


class CLIInterface:
    """Command-line interface for interacting with the agent."""

    def __init__(
        self,
        agent: Agent,
        workspace_manager: WorkspaceManagerImpl,
        knowledge_tool: CodebaseKnowledgeTool,
        recent_changes_provider: RecentChangesProvider,
        tool_call_logger: ToolCallLogger,
        tools: List[LLMTool],
        cli_instructions: List[str],
        orientation_instruction: str | None,
        autocomplete: PromptAutocomplete,
        args,
        memories: Memories,
        router_tool=None,
    ):
        """Initialize the CLI interface.

        Args:
            agent: The agent instance
            workspace_manager: The workspace manager
            knowledge_tool: Tool for accessing codebase knowledge
            recent_changes_provider: Provider for recent changes
            tool_call_logger: Logger for tool calls
            tools: List of available tools
            cli_instructions: List of instructions to run
            autocomplete: Autocomplete handler
            args: Command line arguments
        """
        self.agent = agent
        self.workspace_manager = workspace_manager
        self.knowledge_tool = knowledge_tool
        self.recent_changes_provider = recent_changes_provider
        self.tool_call_logger = tool_call_logger
        self.tools = tools
        self.cli_instructions = cli_instructions
        self.orientation_instruction = orientation_instruction
        self.autocomplete = autocomplete
        self.args = args
        self.memories = memories
        self.router_tool = router_tool

        # Set up command history
        setup_history(args.agent_cache_dir, workspace_manager.root)

    def get_next_instruction(self) -> Optional[str]:
        """Get the next instruction from CLI input or command line arguments."""
        if self.cli_instructions:
            return self.cli_instructions.pop(0)
        else:
            if self.args.quit:
                return None
            while True:
                # Get workspace info for prompt
                workspace_root = str(self.workspace_manager.root)
                branch_name = get_git_branch_name(self.workspace_manager.root)

                # Format the path in blue
                full_path = workspace_root

                prompt_parts = [full_path]
                if branch_name:
                    prompt_parts.append(f" [git:{branch_name}]")
                prompt_parts.append(" >>> ")

                if self.agent.interrupted:
                    prompt_parts.insert(-1, "(interrupted) ")

                prompt = "".join(prompt_parts)

                try:
                    user_input = input(prompt).strip()
                    if user_input:
                        return user_input
                    # else: continue without printing an empty line
                except EOFError:
                    return None

    def reset_diff_output(self):
        """Reset the diff output file."""
        self.args.edits_diff_output.parent.mkdir(parents=True, exist_ok=True)
        self.args.edits_diff_output.write_text("", encoding="utf8")

    def handle_command(self, command: str) -> bool:
        """Handle CLI commands that start with /.

        Args:
            command: The command to handle (without the leading /)

        Returns:
            True if should continue, False if should exit
        """

        # Split command into parts
        parts = command.split()
        cmd = parts[0]
        args = parts[1:] if len(parts) > 1 else []

        if cmd == "tools":
            print("\nAvailable tools\n")
            for tool in self.tools:
                print(f"- {colored(tool.name, 'blue')}")
            print("\nTool descriptions\n")
            for tool in self.tools:
                print(f"{colored(tool.name, 'blue')}:\n{tool.description.strip()}\n")
        elif cmd == "mode":
            # Find the mode switch tool
            mode_tool = next((t for t in self.tools if t.name == "mode_switch"), None)
            if not mode_tool:
                print(colored("Error: mode_switch tool not found", "red"))
                return True

            if not args:
                # Show current mode and available modes
                current_mode = self.agent.current_mode
                # TODO(guy) don't hard-code the modes
                print(f"\nCurrent agent mode: {colored(current_mode.name, 'magenta')}")
                print(
                    f"Available modes: {colored('architect', 'magenta')}, {colored('implementor', 'magenta')}, {colored('researcher', 'magenta')}"
                )
            else:
                # Switch to specified mode
                mode = args[0].lower()
                try:
                    mode_tool.run_impl(
                        {
                            "mode": mode,
                            "reason": "Manually switched by user",
                        }
                    )
                except ValueError as e:
                    print(colored(f"Error: {e}", "red"))
        elif cmd == "log":
            print("\nLogger state:")
            print(
                self.tool_call_logger.get_string_representation(
                    use_tool_supplied_messages=True
                )
            )
        elif cmd == "system_prompt":
            if args:
                subagent_name = args[0]
                subagent = [
                    tool for tool in self.agent.tools if tool.name == subagent_name
                ]
                if subagent and isinstance(subagent[0], PromptedLLMAgent):
                    print(f"\nSystem prompt for {subagent_name}:")
                    print(colored(subagent[0].system_prompt, "green"))
                else:
                    print(f"Subagent {subagent_name} not found.")
            else:
                print("\nSystem prompt:")
                print(colored(self.agent.system_prompt, "green"))
        elif cmd == "dialog":
            print("\nDialog history:")
            print(self.agent.dialog.get_summary())
        elif cmd == "clear":
            self.agent.clear()
        elif cmd == "revert":
            self.workspace_manager.revert_to_initial_state()
        elif cmd == "approve-command-execution":
            # Find process tools and update their ask_user_permission
            for tool in self.tools:
                if hasattr(tool, "ask_user_permission"):
                    # TODO(guy) this is a hack
                    tool.ask_user_permission = False  # type: ignore
        elif cmd == "quit":
            return False
        elif cmd == "help":
            print("Available commands:")
            print("    /help: print this help")
            print("    /tools: show the available tools")
            print("    /mode: show current mode and available modes")
            print(
                "    /mode MODE: switch to specified mode (architect/implementor/researcher)"
            )
            print("    /log: print the tool call log")
            print(
                "    /system_prompt: print the system prompt, can accept an arg for subagents"
            )
            print("    /dialog: print the dialog history")
            print("    /clear: clear the agent's state and chat history")
            print("    /revert: revert the workspace to the initial state,")
            print("        undoing the agent's changes")
            print("    /approve-command-execution: auto-approve all executed commands")
            print("    /diff: show changes since last turn")
            print(
                "    /chat-history-proto [file_path]: show the chat history in proto format"
            )
            print(
                "        If file_path is provided, save the output to that file instead of displaying it"
            )
            print("    /quit: quit the program")
            print("")
            print("Note: You can interrupt the agent at any time with Ctrl+C.")
            print(
                "      After interruption, you can provide a new instruction to resume."
            )
            print("      The agent will maintain its context and knowledge from before")
            print("      the interruption.")
        elif cmd == "diff":
            diff = self.workspace_manager.get_last_turn_diff()
            if not diff:
                print(colored("No changes in the last turn.", "yellow"))
                return True
            # Convert diff to string and show in pager
            show_in_pager(str(diff), self.args.no_pager)
        elif cmd == "chat-history-proto":
            # The command might be in the format "chat-history-proto /path/to/file.json"
            # or just "chat-history-proto"
            # Use the args variable for the file path parameter
            if args:
                file_path = " ".join(args)
                # Make sure the file has a .json extension
                if not file_path.endswith(".json"):
                    file_path += ".json"
            else:
                # If no file path is provided, we'll print to stdout instead of saving to a file
                file_path = None

            # Use the dialog-based implementation
            if self.agent.dialog is None:
                print(
                    colored(
                        "Error: No dialog available. Cannot generate chat history.",
                        "red",
                    )
                )
                return False

            # Note: We should properly track changes when they happen instead of scanning here
            # The proper fix would be to ensure that changes are tracked when files are created/modified
            # by the save_file tool or other tools that modify the workspace

            history = get_chat_history(self.agent.dialog)
            formatted_json = json.dumps(history, indent=2)

            # If a file path is provided, save the JSON to the file
            if file_path:
                try:
                    # Convert relative path to absolute path if needed
                    if not os.path.isabs(file_path):
                        file_path = os.path.join(
                            str(self.workspace_manager.root), file_path
                        )

                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                    # Write the JSON to the file
                    with open(file_path, "w") as f:
                        f.write(formatted_json)
                    print(colored(f"Chat history saved to {file_path}", "green"))
                except Exception as e:
                    print(
                        colored(f"Error saving chat history to {file_path}: {e}", "red")
                    )
            else:
                # Display the JSON in the pager if no file path is provided
                show_in_pager(formatted_json, self.args.no_pager)

        elif command.startswith("remember_button"):
            handle_remember_command(
                command,
                self.agent.dialog,
                self.agent.client,
                self.memories,
                self.tool_call_logger,
            )
        else:
            print(colored(f"Unknown command: /{command}", "red"))
            print("Use /help to see available commands")
        return True

    def handle_shell_command(self, command: str) -> None:
        """Handle shell commands that start with !."""
        if not command:
            print(colored("Error: Empty command", "red"))
            return

        if command.strip().lower().startswith("cd"):
            print(
                colored(
                    "Error: 'cd' commands are not supported. All commands are executed in the workspace root.",
                    "red",
                )
            )
            return

        # Execute other commands directly from workspace root
        try:
            result = subprocess.run(
                command,
                shell=True,
                text=True,
                capture_output=True,
                cwd=self.workspace_manager.root,
            )
            if result.stdout:
                print(result.stdout, end="")
            if result.stderr:
                print(result.stderr, end="", file=sys.stderr)
        except Exception as e:
            print(colored(f"Error executing command: {e}", "red"))

    def run_agent(
        self,
        instruction: str,
        orientation_instruction: str | None = None,
        retry_on_failure: bool = False,
    ) -> str:
        """Wrap agent.run_agent().


        Returns:
            The answer from the agent.
        """
        self.reset_diff_output()
        answer = self.agent.run_agent(
            instruction,
            resume=True,
            orientation_instruction=orientation_instruction,
            retry_on_failure=retry_on_failure,
        )

        # We try to auto-add the memory (based on the instruction)
        # if the agent did not call the `remember` tool on its own
        if RememberTool.name in [t.name for t in self.tools]:
            maybe_create_memory_from_instruction(
                instruction,
                self.agent.dialog,
                (
                    # No need for excessive logging here
                    self.agent.client.client
                    if isinstance(self.agent.client, LoggingLLMClient)
                    else self.agent.client
                ),
                self.memories,
                self.tool_call_logger,
                self.args.verbose_memories,
            )

        return answer

    def find_slack_notification_tool(self) -> Optional[SlackNotificationTool]:
        """Find the SlackNotificationTool in the list of tools if it exists."""
        for tool in self.tools:
            if isinstance(tool, SlackNotificationTool):
                return tool
        return None

    def run_router_tool(self, instruction: str) -> tuple[str, dict]:
        """Run the RouterTool on the user instruction.

        Args:
            instruction: The user instruction to analyze

        Returns:
            A tuple of (modified_instruction, annotations)
            where annotations is a dictionary of router annotations
        """

        assert self.router_tool is not None

        try:
            # Run the router tool using run_impl to get the ToolImplOutput with auxiliary_data
            # Pass None for dialog_messages since we're only analyzing the current instruction
            router_result = self.router_tool.run_impl(
                {"message": instruction}, dialog_messages=None
            )

            updated_instruction = router_result.tool_output
            return updated_instruction, router_result.auxiliary_data

        except Exception as e:
            print(colored(f"\nError running router: {e}", "red"))
            return instruction, {}

    def run(self) -> None:
        """Run the CLI interface loop."""
        self.reset_diff_output()
        first_instruction = True
        while True:
            instruction = self.get_next_instruction()
            orientation_instruction = (
                self.orientation_instruction if first_instruction else None
            )

            if instruction is None:
                print("Quitting because no more instructions were provided...")
                break

            elif instruction.startswith("/"):
                # Strip the slash and any leading/trailing whitespace
                command = instruction[1:].strip()
                # Pass the full command to handle_command
                if not self.handle_command(command):
                    print("Quitting because self.handle_command returned False...")
                    break

            elif instruction.startswith("!"):
                # Strip the exclamation mark and any leading/trailing whitespace
                command = instruction[1:].strip()
                self.handle_shell_command(command)

            else:
                # Process the instruction with the router tool if enabled
                if self.args.use_router:
                    instruction, _ = self.run_router_tool(instruction)

                answer = self.run_agent(
                    instruction,
                    orientation_instruction=orientation_instruction,
                    retry_on_failure=self.args.retry_on_failure,
                )
                print("\nAnswer:")
                print(colored(answer, "magenta"))
                os.system("tput bel")  # Add notification sound after showing answer

                # Print diff if debug flag is enabled
                if self.args.debug_show_diff:
                    diff = self.workspace_manager.get_last_turn_diff()
                    if diff:
                        print("\nChanges in this turn:")
                        print(str(diff))
                    else:
                        print("\nNo changes in this turn.")

                # Send answer via Slack if notifications are enabled
                notification_tool = self.find_slack_notification_tool()
                if notification_tool:
                    try:
                        notification_tool.run_impl(
                            {
                                "message": f"{answer}\n\n_Agent's response to: {instruction}_",
                            }
                        )
                    except Exception as e:
                        print(
                            colored(f"\nFailed to send answer via Slack: {e}", "yellow")
                        )

    def listen(self, port: int = 0) -> None:
        """Run the CLI interface as a server.

        When initial instructions have been provided, run through them first.
        """

        ## queue+lock a la go unbuffered channel.

        chat_queue = queue.Queue(maxsize=1)
        chat_lock = threading.Lock()

        ## HTTP handler, executes in the server thread, below.

        class _handler(http.server.BaseHTTPRequestHandler):
            _cli: CLIInterface = self

            def do_GET(self):
                return self._do("GET")

            def do_POST(self):
                return self._do("POST")

            def _do(self, method: str):
                # Default response Code, Message, and Body
                code, msg, body = 200, None, None

                # Broad try/except to return 5xx on any errors.
                try:
                    # Route the PATH+METHOD pair.
                    route = (self.path, method)

                    if route == ("/health", "GET"):
                        body = {
                            "status": "OK",
                            "agent_working": chat_lock.locked(),
                        }

                    elif route == ("/chat-history", "GET"):
                        body = self._cli.chat_history()

                    elif route == ("/quit", "POST"):
                        print("/quit called, sending SIGTERM to self.")
                        os.kill(os.getpid(), signal.SIGTERM)

                    elif route == ("/interrupt", "POST"):
                        body = self._cli._chat_interrupt(lock=chat_lock)

                    elif route == ("/chat", "POST"):
                        raw = self.rfile.read(int(self.headers["content-length"]))
                        req = json.loads(raw)
                        body = self._cli._chat_try_request(
                            q=chat_queue, lock=chat_lock, req=req
                        )

                    else:
                        code = 404

                    if body is not None:
                        body = (json.dumps(body) + "\n").encode("utf-8")

                except NotImplementedError as e:
                    code = 501
                    msg = str(e)
                    body = None
                except Exception as e:
                    code = 500
                    msg = str(e)
                    body = None

                self.send_response(code, message=msg)
                if body is not None:
                    self.send_header("Content-Type", "application/json")
                self.end_headers()
                if body is not None:
                    self.wfile.write(body)

        ## Run HTTP server in background thread.

        print(f"Listening on port {port}.")
        httpd = http.server.HTTPServer(("", port), _handler)
        threading.Thread(daemon=True, target=httpd.serve_forever).start()

        ## Load initial instructions in background thread.

        def _load_cli_instructions():
            while self.cli_instructions:
                instruction = self.cli_instructions.pop(0)
                self._chat_try_request(q=chat_queue, lock=chat_lock, req=instruction)

        threading.Thread(daemon=False, target=_load_cli_instructions).start()

        ## Main blocking loop calls run_agent() on chat requests.

        self._chat_main_loop(q=chat_queue, lock=chat_lock)

    def chat_history(self) -> dict:
        """Build a GetRemoteAgentChatHistoryResponse proto from the agent's dialog.

        This function converts the dialog messages to a GetRemoteAgentChatHistoryResponse
        proto, which contains a list of exchanges between the user and the agent.

        It uses get_chat_history to extract the history directly from the dialog.

        Returns:
            A dictionary representation of the GetRemoteAgentChatHistoryResponse proto.
        """
        return get_chat_history(self.agent.dialog)

    def _chat_interrupt(self, lock: threading.Lock) -> None:
        """Interrupt an ongoing agent run."""
        if lock.locked():
            print("/interrupt called, and chat is in progress.")
            os.killpg(os.getpgrp(), signal.SIGINT)
        else:
            print("/interrupt called, but no chat in progress.")

    def _chat_main_loop(self, q: queue.Queue, lock: threading.Lock) -> None:
        while True:
            try:
                got_text = False
                text = q.get()
                got_text = True
                self.run_agent(text)
            except KeyboardInterrupt as e:
                print(f"run_agent() -> caught KeyboardInterrupt {e}.")
            except Exception as e:
                print(f"run_agent() -> caught exception {e}.")
            finally:
                if got_text:
                    q.task_done()
                    lock.release()

    def _chat_try_request(
        self, q: queue.Queue, lock: threading.Lock, req: str | dict
    ) -> None:
        """Initiate a chat request.

        The request is kicked of asynchronously. Status is checked separately
        with `chat_history()`. An error is raised if a chat request is already
        in progress; requests are not queued.

        **NOTE**: Only a single request node of type TEXT is supported.

        Args:
            req: A str|dict. As a dict, a representation of the RemoteAgentChatRequest proto.

        Returns:
            None. RPC/HTTP status code is sufficient.
        """
        # Raises ValueError
        text = req if isinstance(req, str) else self._parse_chat_request(req)
        if not lock.acquire(blocking=False):
            raise Exception("It's not your turn, an existing request is in progress.")
        else:
            q.put(text, block=False)

    def _parse_chat_request(self, req: dict) -> str:
        """Parses a chat request from the RemoteAgentChatRequest proto.

        Args:
            req: A dict representation of the RemoteAgentChatRequest proto.

        Returns:
            The text of the request.

        Raises:
            ValueError: If the request doesn't contain a single text node.
        """
        if req.get("user_guidelines"):
            raise ValueError("user_guidelines not supported")
        if req.get("workspace_guidelines"):
            raise ValueError("workspace_guidelines not supported")
        if req.get("agent_memories"):
            raise ValueError("agent_memories not supported")

        request_nodes = req.get("request_details", {}).get("request_nodes", [])
        if len(request_nodes) != 1:
            raise ValueError("Only a single request node is supported")

        if text := request_nodes[0].get("text_node", {}).get("content", ""):
            return text
        else:
            raise ValueError("Request node does not contain text")


def create_fireworks_client(
    model_name: str,
    max_retries: int,
    api_key_file: Path,
) -> FireworksClient:
    """Create a Fireworks AI client.

    Args:
        model_name: The model name to use
        max_retries: Maximum number of retries for API calls
        api_key_file: Path to file containing the API key

    Returns:
        A FireworksClient instance
    """
    # Load API key from file
    if not api_key_file.exists():
        raise ValueError(f"Fireworks API key file not found: {api_key_file}")

    api_key = api_key_file.read_text().strip()
    if not api_key:
        raise ValueError(f"Fireworks API key file is empty: {api_key_file}")

    return FireworksClient(
        model_name=model_name,
        max_retries=max_retries,
        api_key=api_key,
    )


def create_anthropic_client(
    use_anthropic_direct: bool,
    direct_model_name: str,
    vertex_model_name: str,
    max_retries: int,
    use_low_qos_server: bool = False,
    thinking_tokens: int = 0,
) -> AnthropicVertexClient:
    if use_anthropic_direct:
        return AnthropicDirectClient(
            model_name=direct_model_name,
            max_retries=max_retries,
            use_low_qos_server=use_low_qos_server,
            thinking_tokens=thinking_tokens,
        )
    assert not use_low_qos_server
    assert thinking_tokens == 0
    return AnthropicVertexClient(
        model_name=vertex_model_name,
        max_retries=max_retries,
    )


def main(argv=None, llm_client: Optional[LLMClient] = None):
    """Main entry point for the interactive agent.

    Args:
        argv: Optional list of command line arguments. If None, sys.argv[1:] is used.
        llm_client: Optional LLM client to use. If None, creates a default client.

    Sets up the agent environment including:
    - Command line argument parsing
    - Workspace and file management
    - Tool initialization
    - LLM client setup
    - Command history and autocompletion
    - Interactive command loop

    The function handles both direct tool calls (--call-tool) and interactive mode.
    In interactive mode, it processes both command-line instructions (-i, -if) and
    interactive user input.
    """

    # Use AUGMENT_EFS_ROOT for the log directory
    log_dir = AUGMENT_EFS_ROOT / "user/guy/agent_logs" / get_git_username()

    if not log_dir.exists():
        log_dir.mkdir(parents=True, exist_ok=True)

    log_filename = time.strftime("%Y%m%d_%H%M%S")
    parser = argparse.ArgumentParser(
        description="Interactive Agent for Code Operations"
    )
    parser.add_argument(
        "-w",
        "--workspace-root",
        "--workspace_root",
        type=Path,
        help="Root directory for the workspace. If not supplied and in a git repo, uses the git repo root",
    )
    parser.add_argument(
        "--direct-model",
        # default="claude-3-5-sonnet-20241022",
        default="claude-3-7-sonnet-20250219",
        help="Language model to use for direct API (default: some sonnet version)",
    )
    parser.add_argument(
        "--vertex-model",
        # default="claude-3-5-sonnet-v2@20241022",
        default="claude-3-7-sonnet@20250219",
        help="Language model to use for vertex API (default: some sonnet version)",
    )
    parser.add_argument(
        "--knowledge-path",
        "--knowledge_path",
        type=Path,
        default=Path("/mnt/efs/augment/user/guy/bob/knowledge"),
        help="Directory where the agent's knowledge about the codebase is stored",
    )
    parser.add_argument(
        "--agent-cache-dir",
        type=Path,
        default=Path.home() / ".augment/agent",
        help="Directory for agent cache and history files",
    )
    parser.add_argument(
        "--auth-token-file",
        type=Path,
        default=Path.home() / ".augment/token",
        help="The file containing the API token",
    )
    parser.add_argument(
        "--log-file",
        type=Path,
        default=log_dir / f"agent_log_{log_filename}.txt",
        help="Tool calls log file",
    )
    parser.add_argument(
        "--pickle-log-file",
        type=Path,
        default=log_dir / f"agent_log_{log_filename}.pickle",
        help="Tool calls log file",
    )
    parser.add_argument(
        "--prompt-budgeting-log-file",
        type=Path,
        default=log_dir / f"prompt_budgeting_stats_{log_filename}.txt",
        help="Prompt budgeting stats file",
    )
    parser.add_argument(
        "--html-log-file",
        type=Path,
        default=None,
        help="Tool calls log file, only written at end of session on successful termination",
    )
    parser.add_argument(
        "--json-log-file",
        type=Path,
        default=None,
        help="Tool calls log file in JSON format",
    )
    # Get the max output tokens from the command line arguments
    # TODO(guy) 32k is currently broken
    # For context: https://augment-wic8570.slack.com/archives/C0864AXLZ7X/p1743013506189009?thread_ts=1743009047.843459&cid=C0864AXLZ7X
    # PR meant to fix this, after which we can switch to 32k: https://github.com/augmentcode/augment/pull/22177
    parser.add_argument(
        "--max-output-tokens",
        type=int,
        default=8192,
        help="Maximum number of tokens to generate per turn.",
    )
    parser.add_argument(
        "-v",
        "--verbose-llm-calls",
        action="store_true",
        help="Print LLM call details",
    )
    parser.add_argument(
        "--no-integration-warnings",
        action="store_true",
        help="Disable warnings about missing integrations like web search, Linear, and Notion",
    )
    parser.add_argument(
        "-y",
        "--approve-command-execution",
        action="store_true",
        help="Automatically approve command execution",
    )
    parser.add_argument(
        "--allow-recursive-calls",
        action="store_true",
        help="Allow the agent to call itself to accomplish subtasks",
    )
    parser.add_argument(
        "-i",
        "--instruction",
        type=str,
        action="append",
        help="Instructions for the agent. Can specify multiple --instruction arguments.",
    )
    parser.add_argument(
        "--orientation-instruction-file",
        type=str,
        help="Orientation_instruction for the agent.",
    )

    parser.add_argument(
        "-if",
        "--instruction-file",
        type=Path,
        help="A file containing instructions for the agent. Will be used before -i instructions.",
    )
    parser.add_argument(
        "--pr-changes",
        action="store_true",
        help="Show the agent the changes made so far in this branch or PR",
    )
    parser.add_argument(
        "-q",
        "--quit",
        action="store_true",
        help="Quit after the canned instructions are done, instead of waiting for user input.",
    )
    parser.add_argument(
        "--notion-export-root",
        type=Path,
        default="/mnt/efs/augment/agent/notion_export",
        help="Root directory for Notion exports",
    )
    parser.add_argument(
        "--notion-writable-pages",
        type=str,
        nargs="+",
        help="List of Notion page IDs or URLs that the agent can modify. Can be either page IDs (e.g., '7c71008b-816d-4ed1-8cbb-c735653e0d82') or Notion URLs (e.g., 'https://www.notion.so/page-title-7c71008b816d4ed18cbbc735653e0d82')",
    )
    parser.add_argument(
        "--notion-allow-create-pages",
        action="store_true",
        default=False,
        help="Allow the agent to create new Notion pages",
    )
    parser.add_argument(
        "--add-tool",
        type=Path,
        action="append",
        help="Define a tool with a YAML file. Can specify multiple files.",
    )
    parser.add_argument(
        "--agent",  # Renamed from --call-tool
        type=str,
        help="Name of a specific tool to call directly, or path to a YAML file defining the tool",
    )
    parser.add_argument(
        "--agent-input",  # Renamed from --tool-input
        type=str,
        help="JSON string containing the input for the tool being called",
    )
    parser.add_argument(
        "--list-tools",
        action="store_true",
        help="Print out tool names, descriptions, and input schemas for all registered tools",
    )
    parser.add_argument(
        "--list-remote-tools",
        action="store_true",
        help="List available remote tools from the Augment API and exit",
    )
    parser.add_argument(
        "--enable-remote-tools",
        action="store_true",
        help="Enable remote tools from the Augment API",
    )
    parser.add_argument(
        "--remote-tool",
        type=str,
        action="append",
        help="Specific remote tool to enable (can be specified multiple times). If not specified, all available remote tools will be enabled.",
    )
    parser.add_argument(
        "--edits-diff-output",
        type=Path,
        default=Path("/tmp/augment_agent_edits.diff"),
        help="Path to save the diff of agent edits",
    )
    parser.add_argument(
        "--max-turns",
        type=int,
        default=200,
        help="Maximum number of turns for the agent",
    )
    parser.add_argument(
        "--use-agents-host",
        action="store_true",
        help="Use the agents host for LLM calls/retrieval/edit as implemented",
    )
    parser.add_argument(
        "--enable-slack-notifications",
        type=validate_slack_email,
        help="Enable Slack notifications to this email address",
        metavar="EMAIL",
    )
    parser.add_argument(
        "--output-char-budget",
        type=int,
        default=60000,
        help="Output character budget for read files and command execution",
    )
    parser.add_argument(
        "--slack-user-token",
        type=str,
        help="Slack user token (xoxp-) for search functionality. If not provided, will try to load from ~/.augment/agent/slack_user_token",
    )
    parser.add_argument(
        "--debug-show-diff",
        action="store_true",
        help="Show diff after each turn (for debugging/testing)",
    )
    parser.add_argument(
        "--use-router",
        action="store_true",
        help="Use the RouterTool to analyze user messages and add context",
    )
    parser.add_argument(
        "--bench-server",
        action="store_true",
        help="Run as a bench server tool",
    )
    parser.add_argument(
        "--bench-env",
        choices=["local", "production"],
        default="production",
        help="Environment to connect to when running as bench server",
    )
    parser.add_argument(
        "--bench-custom-url",
        help="Custom URL to connect to when running as bench server",
    )
    parser.add_argument(
        "--edit-agent-type",
        choices=[agent_type.name for agent_type in EditFileAgentType],
        default=EditFileAgentType.STR_REPLACE.name,
        help="Which agent to use for editing files. Options: "
        + ", ".join([agent_type.name for agent_type in EditFileAgentType]),
    )
    parser.add_argument(
        "--use-direct-str-replace-tool",
        action="store_true",
        help="(DEPRECATED) This is now the default, and use-edit-file-agent is the option. "
        "Use the direct string replacement tool instead of the agent-based one",
    )
    parser.add_argument(
        "--use-edit-file-agent",
        action="store_true",
        help="Use the edit file agent for editing files",
    )
    parser.add_argument(
        "--use-edit-multi-file-agent",
        action="store_true",
        help="Use the edit agent for editing subtasks",
    )
    parser.add_argument(
        "--agent-coordination",
        type=str,
        metavar="CHANNEL",
        help="Enable agent coordination using the specified Slack channel (without the #)",
    )
    parser.add_argument(
        "--modal",
        action="store_true",
        help="Use modal system with architect/implementor/researcher modes",
    )
    parser.add_argument(
        "--use-anthropic-direct",
        action="store_true",
        help="Use Anthropic directly instead of through the agents host",
        default=False,
    )
    # Fireworks model is specified with --fireworks-model
    parser.add_argument(
        "--fireworks-model",
        default=None,
        help=f"Fireworks AI model to use. Available models: {FireworksClient.MODEL_DEEPSEEK_R1} (DeepSeek R1), "
        f"{FireworksClient.MODEL_DEEPSEEK_V3} (DeepSeek V3), "
        f"{FireworksClient.MODEL_DEEPSEEK_V3_0324} (DeepSeek V3 0324 update), "
        f"{FireworksClient.MODEL_QWEN2_5_CODER_32B} (Qwen2.5 Coder 32B), "
        f"{FireworksClient.MODEL_QWQ_32B} (QwQ 32B). "
        f"Note: Use --tool-support-adapter to enable tool support for models that don't natively support tools.",
    )
    parser.add_argument(
        "--fireworks-api-key-file",
        type=Path,
        default=Path.home() / ".augment/fireworks_api_token",
        help="File containing the Fireworks API key. Default: ~/.augment/fireworks_api_token",
    )
    parser.add_argument(
        "--tool-support-adapter",
        action="store_true",
        help="Use the ToolSupportWrapper to enable tool support for models that don't natively support tools",
        default=False,
    )
    parser.add_argument(
        "--use-low-qos-server",
        action="store_true",
        help="Use the low QoS server for Anthropic direct calls",
        default=False,
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=100,
        help="Maximum number of retries for API calls",
    )

    parser.add_argument(
        "--specify-tool",
        type=str,
        action="append",
        help="Name of a tool to specify to the agent. If set, then we only incldue these tools. Can specify multiple --specify-tool arguments.",
    )

    parser.add_argument(
        "--remove-tool",
        type=str,
        action="append",
        help="Name of a tool to remove from the agent's toolset. Can specify multiple --remove-tool arguments.",
    )

    parser.add_argument(
        "--remove-env-var",
        type=str,
        action="append",
        help="Name of an environment variable to remove from the agent's command environment. Can specify multiple --remove-env-var arguments.",
    )

    parser.add_argument(
        "--use-prompt-budgeting",
        action="store_true",
        help="Use prompt budgeting to limit the number of tokens used in each prompt",
        default=True,
    )

    parser.add_argument(
        "--verbose-memories",
        action="store_true",
        help="Print verbose memories information",
    )
    parser.add_argument(
        "--swebench-mode",
        action="store_true",
        help="Run in SWE bench mode",
    )
    parser.add_argument(
        "--swebench-sparse-system-prompt",
        action="store_true",
        help="Use a sparse system prompt in SWE bench mode",
    )
    parser.add_argument(
        "--enable-bash-tool",
        action="store_true",
        help="Enable the bash tool for direct bash command execution",
    )
    parser.add_argument(
        "--enable-regression-filtering",
        action="store_true",
        help="Enable the regression filtering tool",
    )
    parser.add_argument(
        "--add-scratchpad",
        action="store_true",
        help="Add the scratchpad tool",
    )
    parser.add_argument(
        "--add-sequential-thinking",
        action="store_true",
        help="Add the sequential thinking tool",
    )
    parser.add_argument(
        "--thinking-tokens",
        type=int,
        default=0,
        help="Number of tokens to use for the sequential thinking tool",
    )
    parser.add_argument(
        "--retrieve-in-edit",
        action="store_true",
        help="Retrieve information before each edit",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=0,
        help="Listening port for server mode.",
    )
    parser.add_argument(
        "--state-file",
        type=Path,
        help="Save the agent state to this file. If the file already exists, resume the agent from it.",
    )
    parser.add_argument(
        "--docker-container-id",
        type=str,
        help="Docker container ID to use for remote bash tool",
    )
    parser.add_argument(
        "--no-pager",
        action="store_true",
        help="Disable the pager for command output and print directly to console",
    )
    parser.add_argument(
        "--no-complete-tool",
        action="store_true",
        help="Disable the 'complete' tool that the agent uses to signal task completion",
    )
    parser.add_argument(
        "--disable-builtin-web-search",
        action="store_true",
        help="Disable the built-in web search tool (use remote web-search tool instead)",
    )
    parser.add_argument(
        "--use-container-workspace",
        type=Path,
        default=None,
        help="Use a specific directory in the container as the workspace",
    )
    parser.add_argument(
        "--use-dev-deployment",
        type=str,
        default=None,
        help="Use a given user's dev deployment for the API proxy (default: use staging)",
    )
    parser.add_argument(
        "--retry-on-failure",
        action="store_true",
        default=False,
        help="Retry the agent if it fails (e.g., due to rate limiting)",
    )

    args = parser.parse_args(argv)

    # Check that --use-router and --use-agents-host are not used together
    if args.use_router and args.use_agents_host:
        parser.error("--use-router and --use-agents-host cannot be used together")

    if args.workspace_root is None:
        git_root = get_git_repo_root()
        if git_root is None:
            print(
                colored(
                    "Error: No workspace root specified and not in a git repository",
                    "red",
                )
            )
            return 1
        args.workspace_root = git_root

    # Get token in priority order:
    # 1. --auth-token-file if provided and not empty
    # 2. AUGMENT_API_TOKEN env var if exists
    token = None

    # Try auth-token-file first
    if args.auth_token_file.exists():
        token = args.auth_token_file.read_text("utf8").strip()
        if token:
            print(f"Using token from {args.auth_token_file}")

    # Try environment variable if no token yet
    if not token and "AUGMENT_API_TOKEN" in os.environ:
        token = os.environ["AUGMENT_API_TOKEN"].strip()
        if token:
            print("Using token from AUGMENT_API_TOKEN environment variable")

    if not token:
        print(
            colored(
                f"Error: No valid auth token found. Tried:\n"
                f"- Token file: {args.auth_token_file}\n"
                f"- Environment variable: AUGMENT_API_TOKEN",
                "red",
            )
        )
        return 1

    if args.use_dev_deployment is not None:
        print(f"Using dev deployment for {args.use_dev_deployment}")
        api_proxy_url = get_dev_deployment_api_proxy_url(
            user_name=args.use_dev_deployment
        )
    else:
        print("Using staging deployment")
        api_proxy_url = get_staging_api_proxy_url(None)
    augment_client = AugmentPrototypingClient(
        api_proxy_url, auth_token=token, timeout=120
    )
    augment_public_api_client = AugmentClient(api_proxy_url, token=token)

    integration_warnings = IntegrationWarnings(not args.no_integration_warnings)

    # Check Linear availability
    linear_api_key = get_linear_api_key(args.agent_cache_dir)
    linear_available = False
    if linear_api_key:
        try:
            transport = RequestsHTTPTransport(
                url="https://api.linear.app/graphql",
                headers={"Authorization": linear_api_key},
            )
            Client(transport=transport)  # Test the connection
            linear_available = True
        except Exception:
            pass

    if not linear_available and integration_warnings:
        integration_warnings.warn_if_missing("linear", fail=False)

    # JSON log file path is handled directly in the ToolCallLogger

    # Create a StateManager if state_file is provided
    state_manager = None
    if args.state_file:
        state_manager = StateManager(args.state_file)

    # Create tool call logger
    tool_call_logger = ToolCallLogger(
        verbose=True,
        verbose_llm_calls=args.verbose_llm_calls,
        use_tool_supplied_messages=True,
        log_file=Path(args.log_file),
        pickle_log_file=Path(args.pickle_log_file),
        html_log_file=Path(args.html_log_file) if args.html_log_file else None,
        json_log_file=Path(args.json_log_file) if args.json_log_file else None,
    )

    # If state_manager is available, add a listener to save state
    if state_manager:
        # Load state from state manager if available
        load_tool_call_logger_state(tool_call_logger, state_manager)

        # Add a listener to save state when the logger is updated
        listener = StateManagerToolCallLoggerListener(state_manager)
        tool_call_logger.add_update_listener(listener)

    # Use provided LLM client or create default one
    if llm_client is not None:
        base_llm_client = llm_client
        assert (
            args.thinking_tokens == 0
        ), "Cannot specify thinking tokens with custom LLM client"
    else:
        if args.use_agents_host:
            base_llm_client = AugmentLLMClient(
                augment_public_api_client, max_retries=50
            )
            assert (
                args.thinking_tokens == 0
            ), "Cannot specify thinking tokens with agents host"
        elif args.fireworks_model:
            base_llm_client = create_fireworks_client(
                model_name=args.fireworks_model,
                max_retries=args.max_retries,
                api_key_file=args.fireworks_api_key_file,
            )
            assert (
                args.thinking_tokens == 0
            ), "Cannot specify thinking tokens with agents host"
        else:
            base_llm_client = create_anthropic_client(
                args.use_anthropic_direct,
                direct_model_name=args.direct_model,
                vertex_model_name=args.vertex_model,
                max_retries=args.max_retries,
                use_low_qos_server=args.use_low_qos_server,
                thinking_tokens=args.thinking_tokens,
            )

    # Wrap the base client with ToolSupportWrapper if requested
    if args.tool_support_adapter:
        print(
            f"Using ToolSupportWrapper to enable tool support for {base_llm_client.__class__.__name__}"
        )
        base_llm_client = ToolSupportWrapper(base_llm_client)

    # Create logging client wrapper
    logging_client = LoggingLLMClient(
        client=base_llm_client, tool_call_logger=tool_call_logger
    )

    # Create listeners
    sync_listener = PrintingSyncListener(args.notion_export_root)
    edits_listener = EditsUpdateListener(args.edits_diff_output)

    workspace_manager = WorkspaceManagerImpl(
        augment_client,
        args.workspace_root,
        max_file_size_bytes=1024 * 1024,
        listener=sync_listener,
        update_listener=edits_listener,
        confirmation_provider=InteractiveUserConfirmationProvider(),
        container_workspace=args.use_container_workspace,
    )
    edits_listener.set_workspace_manager(workspace_manager)
    router_tool = None

    if args.swebench_mode:
        codebase_retrieval_tool = None
    elif args.use_agents_host:
        codebase_retrieval_tool = BackendCodebaseRetrievalTool(
            client=augment_public_api_client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
        )
    else:
        # Create separate client for codebase retrieval
        if args.fireworks_model:
            codebase_retrieval_client = create_fireworks_client(
                model_name=args.fireworks_model,
                max_retries=args.max_retries,
                api_key_file=args.fireworks_api_key_file,
            )
        else:
            codebase_retrieval_client = create_anthropic_client(
                args.use_anthropic_direct,
                direct_model_name=args.direct_model,
                vertex_model_name=args.vertex_model,
                max_retries=args.max_retries,
                use_low_qos_server=args.use_low_qos_server,
            )
        logging_codebase_retrieval_client = LoggingLLMClient(
            client=codebase_retrieval_client, tool_call_logger=tool_call_logger
        )
        # Create query index
        query_index = QueryOnlyDocumentIndex(
            augment_client=augment_client,
            workspace_manager=workspace_manager,
            max_retrieval_chunks=1024,  # Not actually used
        )
        codebase_retrieval_tool = HighLevelCodebaseMultiRetrievalAgent(
            tool_call_logger=tool_call_logger,
            retriever=query_index,
            llm_client=logging_codebase_retrieval_client,
            max_tool_chars=40000,
        )
        # Create router tool if the flag is set
        if args.use_router:
            router_tool = RouterTool(
                tool_call_logger=tool_call_logger,
                retriever=query_index,
                llm_client=logging_codebase_retrieval_client,
            )

    knowledge_tool = CodebaseKnowledgeTool(
        tool_call_logger,
        workspace_manager,
        knowledge_path=args.knowledge_path,
    )

    recent_changes_provider = RecentChangesProvider(workspace_manager)

    # Create command approval manager
    command_approval_manager = CommandApprovalManager(args.agent_cache_dir)

    # Managing memories
    memories = Memories(workspace_manager.root)

    default_system_prompt = build_system_prompt(
        workspace_manager.root
        if args.use_container_workspace is None
        else args.use_container_workspace,
        knowledge_tool,
        recent_changes_provider,
        pr_changes=args.pr_changes,
        memories=memories,
        swebench_mode=args.swebench_mode,
        swebench_sparse_system_prompt=args.swebench_sparse_system_prompt,
        edit_multi_file_agent=args.use_edit_multi_file_agent,
    )

    # Initialize built-in tools
    if args.use_edit_file_agent:
        max_turns = 3
        if args.edit_agent_type == EditFileAgentType.STR_REPLACE.name:
            # StrReplaceEditFileAgent often needs more turns
            max_turns = 20
        edit_file_agent = create_edit_agent(
            agent_type=EditFileAgentType[args.edit_agent_type],
            llm_client=logging_client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            max_output_tokens_per_turn=args.max_output_tokens,
            max_turns=max_turns,
            review_stage=False,
            retriever_tool=codebase_retrieval_tool if args.retrieve_in_edit else None,
        )
    elif args.use_edit_multi_file_agent:
        edit_agent_tools = [
            SaveFileTool(tool_call_logger, workspace_manager),
            ReadFileTool(
                tool_call_logger,
                workspace_manager.root,
                char_budget=args.output_char_budget,
            ),
            ReadFileOutlineTool(
                tool_call_logger,
                workspace_manager.root,
            ),
            CodeClipboardTool(tool_call_logger, workspace_manager.root),
            codebase_retrieval_tool,
            NotionPageTool(
                tool_call_logger,
                args.agent_cache_dir,
                allowed_pages_to_modify=args.notion_writable_pages or [],
                allow_create_pages=args.notion_allow_create_pages,
            ),
            RecentChangesTool(tool_call_logger, workspace_manager),
            WebPageFetcherTool(tool_call_logger),
        ]

        edit_multi_file_system_prompt = build_system_prompt_for_edit_multi_file_agent(
            workspace_manager.root
            if args.use_container_workspace is None
            else args.use_container_workspace,
            knowledge_tool,
            recent_changes_provider,
            pr_changes=args.pr_changes,
            memories=memories,
            swebench_mode=args.swebench_mode,
            swebench_sparse_system_prompt=args.swebench_sparse_system_prompt,
        )

        edit_file_agent = EditMultiFileAgent(
            client=logging_client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            tools=edit_agent_tools,
            system_prompt=edit_multi_file_system_prompt,
            max_output_tokens_per_turn=args.max_output_tokens,
            max_turns=40,  # EditMultiFileAgent needs way more turns
        )
    else:
        edit_file_agent = StrReplaceEditorTool(
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
        )

    # Initialize Slack tokens
    slack_bot_token = get_slack_bot_token(args.agent_cache_dir)
    slack_user_token = args.slack_user_token or get_slack_user_token(
        args.agent_cache_dir
    )

    tools = [
        SaveFileTool(tool_call_logger, workspace_manager),
        edit_file_agent,
        ReadFileTool(
            tool_call_logger,
            workspace_manager.root,
            char_budget=args.output_char_budget,
        ),
        ReadFileOutlineTool(
            tool_call_logger,
            workspace_manager.root,
        ),
        CodeClipboardTool(tool_call_logger, workspace_manager.root),
        *create_process_tools(
            workspace_manager=workspace_manager,
            command_approval_manager=command_approval_manager,
            tool_call_logger=tool_call_logger,
            ask_user_permission=not args.approve_command_execution,
            char_budget=args.output_char_budget,
            cwd=workspace_manager.root,
        ),
        NotionPageTool(
            tool_call_logger,
            args.agent_cache_dir,
            allowed_pages_to_modify=args.notion_writable_pages or [],
            allow_create_pages=args.notion_allow_create_pages,
        ),
        RecentChangesTool(tool_call_logger, workspace_manager),
        WebPageFetcherTool(tool_call_logger),
        RememberTool(base_llm_client, tool_call_logger, memories),
    ]

    if not args.swebench_mode:
        tools.append(codebase_retrieval_tool)

    if (args.specify_tool and "scratchpad" in args.specify_tool) or args.add_scratchpad:
        logger.info("Using scratchpad tool")
        tools.append(
            ScratchpadTool(
                tool_call_logger, args.agent_cache_dir, workspace_manager.root
            )
        )
    if (
        args.specify_tool and "sequential_thinking" in args.specify_tool
    ) or args.add_sequential_thinking:
        logger.info("Using sequential thinking tool")
        tools.append(SequentialThinkingTool(tool_call_logger))

    # Add built-in web search tool if not disabled
    if not args.disable_builtin_web_search:
        try:
            tools.append(WebSearchTool(tool_call_logger, integration_warnings))
        except WebSearchError:
            if integration_warnings:
                integration_warnings.warn_if_missing("web_search", fail=False)
    else:
        print(
            colored(
                "Built-in web search tool disabled. Use remote web-search tool instead.",
                "blue",
            )
        )

    tools.append(ClarifyTool(base_llm_client, memories, tool_call_logger))

    # Add Linear tool if available
    if linear_available and linear_api_key:
        tools.append(
            LinearTool(
                api_key=linear_api_key,
                llm_client=logging_client,
                tool_call_logger=tool_call_logger,
                # integration_warnings=integration_warnings,
            )
        )

    # Add remote tools if enabled
    if args.enable_remote_tools:
        print(colored("Enabling remote tools from Augment API", "blue"))
        # Disable built-in web search by default when remote tools are enabled
        if not args.disable_builtin_web_search:
            print(
                colored(
                    "Disabling built-in web search tool since remote tools are enabled",
                    "blue",
                )
            )
            args.disable_builtin_web_search = True
        remote_tools = create_remote_tools(
            tool_call_logger=tool_call_logger,
            augment_client=augment_public_api_client,
            include_tools=args.remote_tool,  # None means include all
        )

        if remote_tools:
            # Check for duplicate tools and remove them
            existing_tool_names = {tool.name for tool in tools}
            unique_remote_tools = []

            for remote_tool in remote_tools:
                if remote_tool.name in existing_tool_names:
                    print(
                        colored(
                            f"Skipping duplicate remote tool: {remote_tool.name}",
                            "yellow",
                        )
                    )
                else:
                    unique_remote_tools.append(remote_tool)
                    existing_tool_names.add(remote_tool.name)

            if unique_remote_tools:
                print(
                    colored(
                        f"Added {len(unique_remote_tools)} remote tools: {[t.name for t in unique_remote_tools]}",
                        "blue",
                    )
                )
                tools.extend(unique_remote_tools)
            else:
                print(colored("No unique remote tools were found or enabled", "yellow"))
        else:
            print(colored("No remote tools were found or enabled", "yellow"))

    # Add Slack tools if available
    if slack_user_token:
        tools.append(
            SlackSearchTool(
                tool_call_logger=tool_call_logger,
                user_token=slack_user_token,
            )
        )

    if slack_bot_token and args.enable_slack_notifications:
        print(f"Enabling Slack notifications for {args.enable_slack_notifications}")
        tools.append(
            SlackNotificationTool(
                tool_call_logger=tool_call_logger,
                bot_token=slack_bot_token,
                user_email=args.enable_slack_notifications,
            )
        )

    # Add coordination if enabled
    agent_name = None
    coordinator: Optional[AgentCoordinator] = None
    if args.agent_coordination:
        if not slack_bot_token:
            print(
                colored(
                    "Error: Slack bot token not found. Required for agent coordination.",
                    "red",
                )
            )
            return 1

        # Create coordinator
        coordinator = SlackAgentCoordinator(
            bot_token=slack_bot_token,
            channel_name=args.agent_coordination,
        )

        # Generate random name if using coordination
        agent_name = generate_agent_name()
        print(colored(f"Agent name: {agent_name}", "blue"))

        # Add coordinator tool
        tools.append(
            AgentCoordinatorTool(
                tool_call_logger=tool_call_logger,
                coordinator=coordinator,
                agent_name=agent_name,
            )
        )

    # print(f"Default system prompt:\n{default_system_prompt}")

    # Load all YAML files from the agents/ directory
    agents_yaml_dir = Path(SCRIPT_DIR, "agents")
    agent_yaml_files = list(agents_yaml_dir.glob("*.yaml")) + list(
        agents_yaml_dir.glob("*.yml")
    )
    yaml_tools = load_tools_from_yaml(
        agent_yaml_files,
        logging_client,
        tool_call_logger,
        available_tools=tools,
        max_output_tokens_per_turn=args.max_output_tokens,
        max_turns=args.max_turns,
        workspace_manager=workspace_manager,
        system_prompt=default_system_prompt,
    )
    tools.extend(yaml_tools)

    # Then load additional tools specified by args.add_tool
    if args.add_tool:
        additional_yaml_tools = load_tools_from_yaml(
            args.add_tool,
            logging_client,
            tool_call_logger,
            available_tools=tools,
            max_output_tokens_per_turn=args.max_output_tokens,
            max_turns=args.max_turns,
            workspace_manager=workspace_manager,
            system_prompt=default_system_prompt,
        )
        tools.extend(additional_yaml_tools)

    # Handle --list-tools
    if args.list_tools:
        print("\nAvailable Tools:\n")
        for tool in tools:
            print(f"\n{colored(tool.name, 'blue')}")
            print(f"Description: {tool.description.strip()}")
            print("\nInput Schema:")
            print(json.dumps(tool.input_schema, indent=2))
        return 0

    # Handle --list-remote-tools
    if args.list_remote_tools:
        print("\nQuerying available remote tools from Augment API...\n")
        try:
            remote_tools = list_remote_tools(augment_public_api_client)

            # Convert the remote tools to a list for processing
            tools_data = []
            for tool in remote_tools:
                # Create a dictionary with the same structure as the raw response
                tool_data = {
                    "tool_definition": tool.tool_definition,
                    "remote_tool_id": tool.remote_tool_id,
                    "availability_status": tool.availability_status,
                    "tool_safety": tool.tool_safety,
                }
                tools_data.append(tool_data)

            if not tools_data:
                print(colored("No remote tools available.", "yellow"))
            else:
                print(f"Found {len(tools_data)} remote tools:\n")

                for tool_data in tools_data:
                    tool_definition = tool_data.get("tool_definition", {})
                    tool_name = tool_definition.get("name", "Unknown")
                    tool_description = tool_definition.get(
                        "description", "No description available"
                    )

                    # Try to get the tool ID if available
                    tool_id = tool_data.get("remote_tool_id", None)
                    tool_id_str = ""
                    if tool_id is not None:
                        # Try to get the enum name
                        for name, value in vars(RemoteToolId).items():
                            if isinstance(value, int) and value == tool_id:
                                tool_id_str = f" (ID: {name})"
                                break

                    # Try to get availability status if available
                    availability_status = tool_data.get("availability_status", None)
                    availability = "Available"
                    if (
                        availability_status
                        == ToolAvailabilityStatus.USER_CONFIG_REQUIRED
                    ):
                        availability = "Requires Configuration"
                    elif availability_status == ToolAvailabilityStatus.UNKNOWN_STATUS:
                        availability = "Unknown Status"

                    # Try to get safety status if available
                    tool_safety = tool_data.get("tool_safety", None)
                    safety = "Unsafe"
                    if tool_safety == ToolSafety.TOOL_SAFE:
                        safety = "Safe"
                    elif tool_safety == ToolSafety.TOOL_CHECK:
                        safety = "Requires Checking"

                    print(f"\n{colored(tool_name, 'blue')}{tool_id_str}")

                    # Only show status information if it's available
                    if availability_status is not None:
                        print(
                            f"Status: {colored(availability, 'green' if availability_status == ToolAvailabilityStatus.AVAILABLE else 'yellow')}"
                        )

                    if tool_safety is not None:
                        print(
                            f"Safety: {colored(safety, 'green' if tool_safety == ToolSafety.TOOL_SAFE else 'yellow')}"
                        )

                    print(f"Description: {tool_description}")

                    # Try to parse and display the input schema
                    print("\nInput Schema:")
                    try:
                        schema_json = tool_definition.get("input_schema_json", "{}")
                        schema = json.loads(schema_json)
                        print(json.dumps(schema, indent=2))
                    except (json.JSONDecodeError, KeyError):
                        print(colored("Schema not available", "red"))
        except Exception as e:
            print(colored(f"Error retrieving remote tools: {e}", "red"))
            import traceback

            print(colored(traceback.format_exc(), "red"))
        return 0

    if args.remove_tool:
        # Remove specified tools from the list of available tools
        for tool in args.remove_tool:
            if tool not in [t.name for t in tools]:
                print(
                    f"Warning: Tool '{tool}' not found in the list of available tools."
                )
        print(f"Removing tools: {args.remove_tool}")
        tools = [t for t in tools if t.name not in args.remove_tool]
        print(f"Remaining tools: {[t.name for t in tools]}")
    elif args.specify_tool:
        # Remove all tools except the specified ones
        for tool in args.specify_tool:
            if tool not in [t.name for t in tools]:
                print(
                    f"Warning: Tool '{tool}' not found in the list of available tools."
                )
        for tool in tools:
            if tool.name not in args.specify_tool:
                print(f"Removing tool {tool.name}")
        tools = [t for t in tools if t.name in args.specify_tool]

    # Add bash_tool if enabled
    bash_tool = None
    if args.enable_bash_tool:
        print(colored("Enabling bash_tool for direct bash command execution", "blue"))
        if args.docker_container_id:
            print(
                colored(
                    f"Enabling docker bash tool with container {args.docker_container_id}",
                    "blue",
                )
            )
            tools.append(
                create_docker_bash_tool(
                    command_approval_manager=command_approval_manager,
                    tool_call_logger=tool_call_logger,
                    container=args.docker_container_id,
                    ask_user_permission=not args.approve_command_execution,
                    cwd=workspace_manager.root,
                )
            )
        else:
            print(colored("Enabling local bash tool", "blue"))
            tools.append(
                BashTool(
                    tool_call_logger=tool_call_logger,
                    workspace_root=workspace_manager.root,
                    require_confirmation=not args.approve_command_execution,
                )
            )
        bash_tool = tools[-1]
    print(f"Remaining tools: {[t.name for t in tools]}")

    # Initialize cli_instructions
    cli_instructions: list[str] = args.instruction or []
    if args.instruction_file:
        cli_instructions.append(args.instruction_file.read_text())

    orientation_instruction = None
    if args.orientation_instruction_file:
        orientation_instruction = Path(args.orientation_instruction_file).read_text()

    # Create mode provider
    if args.modal:
        # If resuming, use the reconstructed dialog for the mode switch tool
        if args.state_file:
            raise ValueError("--state-file not supported with --modal")

        # Create tool lists for each mode
        editing_tools = [
            "save_file",
            "edit_file_agent",
            "codebase_edit",
            "launch_process",
            "kill_process",
            "read_process",
            "write_process",
            "list_processes",
        ]

        # Create base tools list without editing tools
        base_tools = [tool for tool in tools if tool.name not in editing_tools]

        # Create coder tools list with all tools
        coder_tools = tools.copy()

        # Create modes with appropriate tool lists
        architect_mode = create_architect_mode(base_tools)
        implementor_mode = create_implementor_mode(coder_tools)
        researcher_mode = create_researcher_mode(base_tools)

        # Create mode switch tool
        mode_switch_tool = AgentModeSwitcher(
            llm_client=logging_client,
            tool_call_logger=tool_call_logger,
            architect_mode=architect_mode,
            implementor_mode=implementor_mode,
            researcher_mode=researcher_mode,
            build_workspace_prompt_fn=get_workspace_prompt_builder(
                args.workspace_root,
                knowledge_tool,
                recent_changes_provider,
                args.pr_changes,
                memories,
            ),
        )

        # Add mode switch tool to tools list
        tools.append(mode_switch_tool)

        # Use mode switch tool as provider
        mode_provider = mode_switch_tool

        # Add initial instruction to dialog if provided and not resuming
        if cli_instructions:
            mode_switch_tool.dialog.add_user_prompt(cli_instructions[0])
    else:
        # Use constant mode provider with system prompt
        mode = AgentMode(
            name="default", system_prompt=default_system_prompt, tools=tools
        )
        mode_provider = ConstantModeProvider(mode)

    # get orientation tools
    orientation_tools = [
        ReadFileTool(
            tool_call_logger,
            workspace_manager.root,
            char_budget=args.output_char_budget,
        ),
        # codebase_retrieval_tool, # TODO (c-flaherty): add back once we fix the dev deploy issue
        SequentialThinkingTool(tool_call_logger),
    ]
    if bash_tool is not None:
        orientation_tools.append(bash_tool)

    # StateManager is already created above

    # Initialize the agent with the appropriate state management
    agent = Agent(
        client=logging_client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        max_output_tokens_per_turn=args.max_output_tokens,
        max_turns=args.max_turns,
        allow_recursive_calls=args.allow_recursive_calls,
        workspace_manager=workspace_manager,
        agent_name=agent_name,
        coordinator=coordinator if args.agent_coordination else None,
        prompt_analytics_logs=Path(args.prompt_budgeting_log_file),
        use_prompt_budgeting=args.use_prompt_budgeting,
        print_mode_switches=True,
        orientation_tools=orientation_tools,
        use_complete_tool=not args.no_complete_tool,
        state_manager=state_manager,
        enable_regression_filtering=args.enable_regression_filtering,
    )

    # The dialog is now loaded directly in the Agent constructor from the StateManager
    # No need to manually set the dialog here

    autocomplete = PromptAutocomplete(workspace_manager)
    autocomplete.setup_readline()
    setup_history(args.agent_cache_dir, workspace_manager.root)

    # Take snapshot at the beginning
    workspace_manager.snapshot_workspace()

    # If calling a specific tool
    if args.agent:
        if not args.agent_input:
            parser.error("--agent-input is required when using --agent")

        # Check if args.agent is a path to a YAML file
        agent_path = Path(args.agent)
        agent_name = args.agent  # Default to the provided name
        if agent_path.exists() and agent_path.suffix in [".yml", ".yaml"]:
            print(f"Loading agent from YAML file: {agent_path}")
            # Load the agent from YAML
            try:
                with open(agent_path) as f:
                    tool_config = yaml.safe_load(f)
                print(f"Loaded YAML config: {tool_config}")
                agent_name = tool_config["name"]  # Use the name from YAML

                # Filter out 'complete' from tools list - it's handled by Agent
                if "tools" in tool_config:
                    tool_config["tools"] = [
                        t for t in tool_config["tools"] if t != "complete"
                    ]
                    print(f"Filtered tools: {tool_config['tools']}")

                # Create a StateManager if state_file is provided
                state_manager = None
                if args.state_file:
                    state_manager = StateManager(args.state_file)

                agent = PromptedLLMAgent.from_yaml(
                    yaml_dict=tool_config,
                    client=logging_client,
                    tool_call_logger=tool_call_logger,
                    available_tools=tools,
                    max_output_tokens_per_turn=args.max_output_tokens,
                    max_turns=args.max_turns,
                    workspace_manager=workspace_manager,
                    system_prompt_prefix=default_system_prompt,
                    state_manager=state_manager,
                )
                tools = [agent]  # Only use this agent
                print(f"Available tools after loading: {[t.name for t in tools]}")
            except Exception as e:
                print(colored(f"Error loading agent from {agent_path}: {e}", "red"))
                return 1

        try:
            result = call_tool(
                agent_name,  # Use the agent name
                args.agent_input,
                tools,
            )
            print(colored(result, "magenta"))
        except ValueError as e:
            print(colored(f"Error: {e}", "red"))
            return 1
        return 0
    elif args.bench_server:
        # Set up bench environment
        env = (
            Environment.CUSTOM
            if args.bench_custom_url
            else getattr(Environment, args.bench_env.upper())
        )
        config = Config.new(env, args.bench_custom_url)
        logger.info("Starting up in %s environment", env)
        logger.debug("Using configuration: %s", config)

        # Set up token
        token_manager = TokenManager()
        if token := token_manager.load_token():
            logger.info("Using existing authentication token")
        else:
            token = token_manager.generate_token()
            token_manager.save_token(token)
            logger.info("Generated new authentication token")

        auth_url = config.auth_url(token)
        print(f"Please visit {auth_url} to authenticate your session")

        # Create bench interface
        bench = BenchInterface(
            agent=agent,
            workspace_manager=workspace_manager,
            knowledge_tool=knowledge_tool,
        )

        try:
            asyncio.run(bench.run(config, token))
        except KeyboardInterrupt:
            logger.info("Shutting down")
            tool_call_logger.finish()
            sys.exit(0)
    else:
        # Create CLI interface
        cli = CLIInterface(
            agent=agent,
            workspace_manager=workspace_manager,
            knowledge_tool=knowledge_tool,
            recent_changes_provider=recent_changes_provider,
            tool_call_logger=tool_call_logger,
            tools=tools,
            cli_instructions=cli_instructions,
            orientation_instruction=orientation_instruction,
            autocomplete=autocomplete,
            args=args,
            memories=memories,
            router_tool=router_tool,
        )

        if args.port:
            cli.listen(args.port)
        else:
            cli.run()

        tool_call_logger.finish()


if __name__ == "__main__":
    main()

from pathlib import Path
from collections import defaultdict
from experimental.guy.agent_qa.file_edit.file_edit_utils import is_path_in_directory

from experimental.guy.agent_qa.file_edit.str_replace_editor_tool import (
    SNIPPET_LINES,
    ExtendedToolImplOutput,
    Too<PERSON><PERSON><PERSON>r,
    maybe_truncate,
    run_sync,
    Command,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
)

from typing import Any, Optional, get_args
import logging

logger = logging.getLogger(__name__)


class StrReplaceEditorToolWithMultiEdit(LLMTool):
    name = "str_replace_editor"

    description = """\
Custom editing tool for viewing, creating and editing files
* `path` is a file path relative to the workspace root
* command `view` displays the result of applying `cat -n`.
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.


Notes for using the `str_replace` command:
* Use the `str_replace_entries` parameter with an array of objects
* Each object should have `old_str`, `new_str`, `old_str_start_line_number` and `old_str_end_line_number` properties
* The `old_str_start_line_number` and `old_str_end_line_number` parameters are 1-based line numbers
* Both `old_str_start_line_number` and `old_str_end_line_number` are INCLUSIVE
* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number` and `old_str_end_line_number` to disambiguate between multiple occurrences of `old_str` in the file
* Make sure that `old_str_start_line_number` and `old_str_end_line_number` do not overlap with other entries in `str_replace_entries`
* The `new_str` parameter should contain the edited lines that should replace the `old_str`. Can be an empty string to delete content

Notes for using the `insert` command:
* Use the `insert_line_entries` parameter with an array of objects
* Each object should have `insert_line` and `new_str` properties
* The `insert_line` parameter specifies the line number after which to insert the new string
* The `insert_line` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line: 0`

Notes for using the `view` command:
* Strongly prefer to use larger ranges of at least 1000 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges
* Prefer to use grep instead of view when looking for a specific symbol in the file

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use view command to read the file before editing it.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["view", "create", "str_replace", "insert", "undo_edit"],
                "description": "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.",
            },
            "file_text": {
                "description": "Required parameter of `create` command, with the content of the file to be created.",
                "type": "string",
            },
            "insert_line_entries": {
                "description": "Required parameter of `insert` command. A list of entries to insert. Each entry is a dictionary with keys `insert_line` and `new_str`.",
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "insert_line": {
                            "description": "The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",
                            "type": "integer",
                        },
                        "new_str": {
                            "description": "The string to insert.",
                            "type": "string",
                        },
                    },
                    "required": ["insert_line", "new_str"],
                },
            },
            "str_replace_entries": {
                "description": "Required parameter of `str_replace` command. A list of entries to replace. Each entry is a dictionary with keys `old_str`, `old_str_start_line_number`, `old_str_end_line_number` and `new_str`. `old_str` from different entries should not overlap.",
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "old_str": {
                            "description": "The string in `path` to replace.",
                            "type": "string",
                        },
                        "old_str_start_line_number": {
                            "description": "The line number of the first line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",
                            "type": "integer",
                        },
                        "old_str_end_line_number": {
                            "description": "The line number of the last line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",
                            "type": "integer",
                        },
                        "new_str": {
                            "description": "The string to replace `old_str` with.",
                            "type": "string",
                        },
                    },
                    "required": [
                        "old_str",
                        "new_str",
                        "old_str_start_line_number",
                        "old_str_end_line_number",
                    ],
                },
            },
            "path": {
                "description": "Path to file or directory.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "items": {"type": "integer"},
                "type": "array",
            },
        },
        "required": ["command", "path"],
    }

    # Track file edit history for undo operations
    _file_history = defaultdict(list)

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        ignore_indentation_for_str_replace: bool = False,
        expand_tabs: bool = False,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.ignore_indentation_for_str_replace = ignore_indentation_for_str_replace
        self.expand_tabs = expand_tabs
        self._file_history = defaultdict(list)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ExtendedToolImplOutput:
        command = tool_input["command"]
        path = tool_input["path"]
        file_text = tool_input.get("file_text")
        view_range = tool_input.get("view_range")
        str_replace_entries = tool_input.get("str_replace_entries")
        insert_line_entries = tool_input.get("insert_line_entries")

        try:
            _path = Path(path)
            if not _path.is_absolute():
                _path = self.workspace_manager.root / _path
            self.validate_path(command, _path)

            workspace_root = self.workspace_manager.root
            if not is_path_in_directory(workspace_root, _path):
                return ExtendedToolImplOutput(
                    f"Path {_path} is outside the workspace root directory: {workspace_root}. You can only access files within the workspace root directory.",
                    f"Path {_path} is outside the workspace root directory: {workspace_root}. You can only access files within the workspace root directory.",
                    {"success": False},
                )
            if command == "view":
                return self.view(_path, view_range)
            elif command == "create":
                if file_text is None:
                    raise ToolError(
                        "Parameter `file_text` is required for command: create"
                    )
                self.write_file(_path, file_text)
                self._file_history[_path].append(file_text)
                return ExtendedToolImplOutput(
                    f"File created successfully at: {_path}",
                    f"File created successfully at: {_path}",
                    {"success": True},
                )
            elif command == "str_replace":
                if str_replace_entries:
                    # Validate entries
                    for i, entry in enumerate(str_replace_entries):
                        if "old_str" not in entry:
                            raise ToolError(
                                f"Missing required field 'old_str' in str_replace_entries at index {i}"
                            )
                        if "new_str" not in entry:
                            raise ToolError(
                                f"Missing required field 'new_str' in str_replace_entries at index {i}"
                            )

                    # Sort entries by line number so that replacements don't affect later replacements
                    str_replace_entries = sorted(
                        str_replace_entries,
                        key=lambda x: x.get("old_str_start_line_number", 0),
                    )
                    content = self.read_file(_path)
                    self._file_history[_path].append(content)

                    outputs = []
                    for entry in reversed(str_replace_entries):
                        try:
                            outputs.append(
                                self.str_replace(
                                    _path,
                                    entry["old_str"],
                                    entry["new_str"],
                                    entry.get("old_str_start_line_number"),
                                    entry.get("old_str_end_line_number"),
                                )
                            )
                        except ToolError as e:
                            outputs.append(
                                ExtendedToolImplOutput(
                                    str(e),
                                    str(e),
                                    {"success": False},
                                )
                            )

                    # Combine all outputs into one
                    combined_output = ExtendedToolImplOutput(
                        "\n".join(output.tool_output for output in outputs),
                        "\n".join(output.tool_result_message for output in outputs),
                        {
                            "success": all(output.success for output in outputs),
                            "num_str_replace_entries": len(str_replace_entries),
                        },
                    )

                    return combined_output
                else:
                    raise ToolError(
                        "Parameter `str_replace_entries` is required for command: str_replace"
                    )
            elif command == "insert":
                if insert_line_entries:
                    # Validate entries
                    for entry in insert_line_entries:
                        if "insert_line" not in entry:
                            raise ToolError(
                                "Missing required field 'insert_line' in insert_line_entries"
                            )
                        if "new_str" not in entry:
                            raise ToolError(
                                "Missing required field 'new_str' in insert_line_entries"
                            )

                    # Sort entries by line number
                    entries = sorted(
                        insert_line_entries, key=lambda x: x["insert_line"]
                    )
                    content = self.read_file(_path)
                    self._file_history[_path].append(content)
                    lines = content.splitlines()

                    # Apply inserts in reverse order
                    for entry in reversed(entries):
                        line_num = entry["insert_line"]
                        if line_num < 0 or line_num > len(lines):
                            raise ToolError(f"Invalid line number: {line_num}")
                        lines.insert(line_num, entry["new_str"])

                    _path.write_text("\n".join(lines))
                    return ExtendedToolImplOutput(
                        f"Multiple inserts applied to {_path}",
                        f"Multiple inserts applied to {_path}",
                        {"success": True},
                    )
                else:
                    raise ToolError(
                        "Parameter `insert_line_entries` is required for command: insert"
                    )
            elif command == "undo_edit":
                return self.undo_edit(_path)
            raise ToolError(
                f'Unrecognized command {command}. The allowed commands for the {self.name} tool are: {", ".join(get_args(Command))}'
            )
        except ToolError as e:
            return ExtendedToolImplOutput(
                e.message,
                e.message,
                {"success": False},
            )

    def validate_path(self, command: str, path: Path):
        """
        Check that the path/command combination is valid.
        """
        # Check if path exists
        if not path.exists() and command != "create":
            raise ToolError(
                f"The path {path} does not exist. Please provide a valid path."
            )
        if path.exists() and command == "create":
            content = self.read_file(path)
            if content.strip():
                raise ToolError(
                    f"File already exists and is not empty at: {path}. Cannot overwrite non empty files using command `create`."
                )
        # Check if the path points to a directory
        if path.is_dir():
            if command != "view":
                raise ToolError(
                    f"The path {path} is a directory and only the `view` command can be used on directories"
                )

    def view(
        self, path: Path, view_range: Optional[list[int]] = None
    ) -> ExtendedToolImplOutput:
        if path.is_dir():
            if view_range:
                raise ToolError(
                    "The `view_range` parameter is not allowed when `path` points to a directory."
                )

            _, stdout, stderr = run_sync(rf"find {path} -maxdepth 2 -not -path '*/\.*'")
            if not stderr:
                output = f"Here's the files and directories up to 2 levels deep in {path}, excluding hidden items:\n{stdout}\n"
            else:
                output = f"stderr: {stderr}\nstdout: {stdout}\n"
            return ExtendedToolImplOutput(
                output, "Listed directory contents", {"success": not stderr}
            )

        file_content = self.read_file(path)
        file_lines = file_content.split(
            "\n"
        )  # Split into lines early for total line count
        init_line = 1
        if view_range:
            if len(view_range) != 2 or not all(isinstance(i, int) for i in view_range):
                raise ToolError(
                    "Invalid `view_range`. It should be a list of two integers."
                )
            n_lines_file = len(file_lines)
            init_line, final_line = view_range
            if init_line < 1 or init_line > n_lines_file:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its first element `{init_line}` should be within the range of lines of the file: {[1, n_lines_file]}"
                )
            if final_line > n_lines_file:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its second element `{final_line}` should be smaller than the number of lines in the file: `{n_lines_file}`"
                )
            if final_line != -1 and final_line < init_line:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its second element `{final_line}` should be larger or equal than its first `{init_line}`"
                )

            if final_line == -1:
                file_content = "\n".join(file_lines[init_line - 1 :])
            else:
                file_content = "\n".join(file_lines[init_line - 1 : final_line])

        output = self._make_output(
            file_content=file_content,
            file_descriptor=str(path),
            total_lines=len(
                file_lines
            ),  # Use total lines in file, not just the viewed range
            init_line=init_line,
        )
        return ExtendedToolImplOutput(
            output, "Displayed file content", {"success": True}
        )

    def str_replace(
        self,
        path: Path,
        old_str: str,
        new_str: str | None,
        old_str_start_line_number: int = None,
        old_str_end_line_number: int = None,
    ) -> ExtendedToolImplOutput:
        if new_str is None:
            new_str = ""

        content = self.read_file(path)
        if self.expand_tabs:
            content = content.expandtabs()
            old_str = old_str.expandtabs()
            new_str = new_str.expandtabs()

        if not old_str.strip():
            if content.strip():
                raise ToolError(
                    f"No replacement was performed, old_str is empty which is only allowed when the file is empty. The file {path} is not empty."
                )
            else:
                # replace the whole file with new_str
                new_content = new_str
                path.write_text(new_content)
                # Prepare the success message
                success_msg = f"The file {path} has been edited. "
                success_msg += self._make_output(
                    file_content=new_content,
                    file_descriptor=f"{path}",
                    total_lines=len(new_content.split("\n")),
                )
                success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

                return ExtendedToolImplOutput(
                    success_msg,
                    f"The file {path} has been edited.",
                    {"success": True},
                )

        # Find all occurrences of old_str in the content
        occurrences = []
        start_idx = 0

        while True:
            idx = content.find(old_str, start_idx)
            if idx == -1:
                break

            # Calculate line numbers for this occurrence
            prefix = content[:idx]
            start_line = prefix.count("\n") + 1
            end_line = start_line + old_str.count("\n")

            occurrences.append(
                {"index": idx, "start_line": start_line, "end_line": end_line}
            )

            start_idx = idx + len(old_str)

        if not occurrences:
            raise ToolError(
                f"No replacement was performed, old_str \n ```\n{old_str}\n```\n did not appear verbatim in {path}."
            )

        # If only one occurrence, use it directly
        if len(occurrences) == 1:
            occurrence = occurrences[0]
            warning_msg = ""

            # Add warning if line numbers were provided but don't match
            if (
                old_str_start_line_number is not None
                or old_str_end_line_number is not None
            ):
                actual_start = occurrence["start_line"]
                actual_end = occurrence["end_line"]

                if (
                    old_str_start_line_number is not None
                    and old_str_start_line_number != actual_start
                ):
                    warning_msg += f"Warning: Provided start line number {old_str_start_line_number} doesn't match actual start line {actual_start}.\n"

                if (
                    old_str_end_line_number is not None
                    and old_str_end_line_number != actual_end
                ):
                    warning_msg += f"Warning: Provided end line number {old_str_end_line_number} doesn't match actual end line {actual_end}.\n"

        # If multiple occurrences, use line numbers to disambiguate
        else:
            if old_str_start_line_number is None and old_str_end_line_number is None:
                lines_info = ", ".join(
                    [f"lines {o['start_line']}-{o['end_line']}" for o in occurrences]
                )
                raise ToolError(
                    f"No replacement was performed. Multiple occurrences of old_str \n ```\n{old_str}\n```\n at {lines_info}. "
                    f"Please provide line numbers to disambiguate using old_str_start_line_number and/or old_str_end_line_number."
                )

            # Find the best match based on line numbers
            best_match = None
            min_diff = float("inf")
            warning_msg = ""

            for occurrence in occurrences:
                start_diff = (
                    abs(occurrence["start_line"] - old_str_start_line_number)
                    if old_str_start_line_number is not None
                    else 0
                )
                end_diff = (
                    abs(occurrence["end_line"] - old_str_end_line_number)
                    if old_str_end_line_number is not None
                    else 0
                )
                total_diff = start_diff + end_diff

                if total_diff < min_diff:
                    min_diff = total_diff
                    best_match = occurrence

            # Define a strict threshold for line number matching
            MAX_DIFF_THRESHOLD = 5  # Maximum difference allowed for line numbers

            # For each occurrence, check if the provided line numbers are within threshold
            valid_occurrences = []
            for occurrence in occurrences:
                is_valid = True

                if old_str_start_line_number is not None:
                    start_diff = abs(
                        old_str_start_line_number - occurrence["start_line"]
                    )
                    if start_diff > MAX_DIFF_THRESHOLD:
                        is_valid = False

                if old_str_end_line_number is not None:
                    end_diff = abs(old_str_end_line_number - occurrence["end_line"])
                    if end_diff > MAX_DIFF_THRESHOLD:
                        is_valid = False

                if is_valid:
                    valid_occurrences.append(occurrence)

            # If no valid occurrences found, raise an error
            if not valid_occurrences:
                lines_info = ", ".join(
                    [f"lines {o['start_line']}-{o['end_line']}" for o in occurrences]
                )
                raise ToolError(
                    f"No replacement was performed. No occurrence of old_str matches the provided line numbers within threshold. "
                    f"Found occurrences at {lines_info}."
                )

            # Use the best match among valid occurrences
            best_match = min(
                valid_occurrences,
                key=lambda o: (
                    abs(o["start_line"] - (old_str_start_line_number or 0))
                    + abs(o["end_line"] - (old_str_end_line_number or 0))
                ),
            )

            occurrence = best_match

            # Add warning if line numbers don't match exactly
            warning_msg = ""
            actual_start = occurrence["start_line"]
            actual_end = occurrence["end_line"]

            if (
                old_str_start_line_number is not None
                and old_str_start_line_number != actual_start
            ):
                warning_msg += f"Warning: Provided start line number {old_str_start_line_number} doesn't match actual start line {actual_start}.\n"

            if (
                old_str_end_line_number is not None
                and old_str_end_line_number != actual_end
            ):
                warning_msg += f"Warning: Provided end line number {old_str_end_line_number} doesn't match actual end line {actual_end}.\n"

        # Perform the replacement
        new_content = (
            content[: occurrence["index"]]
            + new_str
            + content[occurrence["index"] + len(old_str) :]
        )
        path.write_text(new_content)

        # Create a snippet of the edited section
        start_line = max(0, occurrence["start_line"] - SNIPPET_LINES - 1)
        end_line = occurrence["start_line"] + SNIPPET_LINES + new_str.count("\n")
        snippet = "\n".join(new_content.split("\n")[start_line : end_line + 1])

        # Prepare the success message
        success_msg = f"The file {path} has been edited.\n"
        if warning_msg:
            success_msg += warning_msg
        success_msg += self._make_output(
            file_content=snippet,
            file_descriptor=f"a snippet of {path}",
            total_lines=len(new_content.split("\n")),
            init_line=start_line + 1,
        )
        success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

        return ExtendedToolImplOutput(
            success_msg,
            f"The file {path} has been edited.",
            {"success": True},
        )

    def insert(
        self, path: Path, insert_line: int, new_str: str
    ) -> ExtendedToolImplOutput:
        """Implement the insert command, which inserts new_str at the specified line in the file content."""
        file_text = self.read_file(path)
        if self.expand_tabs:
            file_text = file_text.expandtabs()
            new_str = new_str.expandtabs()
        file_text_lines = file_text.split("\n")
        n_lines_file = len(file_text_lines)

        if insert_line < 0 or insert_line > n_lines_file:
            raise ToolError(
                f"Invalid `insert_line` parameter: {insert_line}. It should be within the range of lines of the file: {[0, n_lines_file]}"
            )

        new_str_lines = new_str.split("\n")
        new_file_text_lines = (
            file_text_lines[:insert_line]
            + new_str_lines
            + file_text_lines[insert_line:]
        )
        snippet_lines = (
            file_text_lines[max(0, insert_line - SNIPPET_LINES) : insert_line]
            + new_str_lines
            + file_text_lines[insert_line : insert_line + SNIPPET_LINES]
        )

        new_file_text = "\n".join(new_file_text_lines)
        snippet = "\n".join(snippet_lines)

        self.write_file(path, new_file_text)

        success_msg = f"The file {path} has been edited.\n"
        success_msg += self._make_output(
            file_content=snippet,
            file_descriptor="a snippet of the edited file",
            total_lines=len(new_file_text_lines),
            init_line=max(1, insert_line - SNIPPET_LINES + 1),
        )
        success_msg += "Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc). Edit the file again if necessary."

        return ExtendedToolImplOutput(
            success_msg,
            "Insert successful",
            {"success": True},
        )

    def undo_edit(self, path: Path) -> ExtendedToolImplOutput:
        """Implement the undo_edit command."""
        if not self._file_history[path]:
            raise ToolError(f"No edit history found for {path}.")

        old_text = self._file_history[path].pop()
        self.write_file(path, old_text)

        formatted_file = self._make_output(
            file_content=old_text,
            file_descriptor=str(path),
            total_lines=len(old_text.split("\n")),
        )
        output = f"Last edit to {path} undone successfully.\n{formatted_file}"

        return ExtendedToolImplOutput(
            output,
            "Undo successful",
            {"success": True},
        )

    def read_file(self, path: Path):
        """Read the content of a file from a given path; raise a ToolError if an error occurs."""
        try:
            return path.read_text()
        except Exception as e:
            raise ToolError(f"Ran into {e} while trying to read {path}") from None

    def write_file(self, path: Path, file: str):
        """Write the content of a file to a given path; raise a ToolError if an error occurs."""
        try:
            path.write_text(file)
        except Exception as e:
            raise ToolError(f"Ran into {e} while trying to write to {path}") from None

    def _make_output(
        self,
        file_content: str,
        file_descriptor: str,
        total_lines: int,
        init_line: int = 1,
    ):
        """Generate output for the CLI based on the content of a file."""
        file_content = maybe_truncate(file_content)
        if self.expand_tabs:
            file_content = file_content.expandtabs()
        file_content = "\n".join(
            [
                f"{i + init_line:6}\t{line}"
                for i, line in enumerate(file_content.split("\n"))
            ]
        )
        return (
            f"Here's the result of running `cat -n` on {file_descriptor}:\n"
            + file_content
            + "\n"
            + f"Total lines in file: {total_lines}\n"
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['path']}"

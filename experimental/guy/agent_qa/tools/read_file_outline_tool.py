"""Tool for generating code outlines using tree-sitter."""

import os
from pathlib import Path
from typing import Any, Dict, Optional, List

from tree_sitter import Node
from tree_sitter_languages import get_language, get_parser

from research.agents.tools import Too<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolImplOutput
from experimental.guy.agent_qa.builtin_tools import LLMTool
from experimental.guy.agent_qa.tools.code_node_utils import (
    LANGUAGE_MAP,
    get_node_name,
    get_block_content,
    get_block_content_from_node,
    get_signature,
)


def get_outline_nodes(
    node: Node,
    source_bytes: bytes,
    expand_sections: list[str] = None,
    parent_expanded: bool = False,
    debug: bool = False,
    indent: str = "",
    depth: int = 0,
    max_depth: int = 5,
) -> List[str]:
    """Extract outline-worthy nodes from the syntax tree."""
    outline = []
    expand_sections = expand_sections or []

    # Check depth limit
    if depth >= max_depth:
        return [indent + "... (max depth reached)"]

    # Skip nodes that are too small (like single tokens)
    if node.start_byte == node.end_byte:
        return outline

    # Get node text
    node_text = source_bytes[node.start_byte : node.end_byte].decode("utf-8").strip()

    def should_expand(node_type: str, name: str) -> bool:
        """Check if this section should be expanded."""
        label = f"{node_type}:{name}"
        if debug:
            print(f"{indent}Checking expansion for {label} (in {expand_sections})")
        return label in expand_sections

    # Handle different node types based on the type field
    if node.type == "module":  # Python module
        if debug:
            print(f"{indent}Processing Python module")
        # Process all children to get imports and module-level nodes
        for child in node.children:
            outline.extend(
                get_outline_nodes(
                    child,
                    source_bytes,
                    expand_sections,
                    parent_expanded,
                    debug,
                    indent,  # Don't add indentation for module-level nodes
                    depth,  # Don't increment depth for module
                    max_depth,
                )
            )

    elif node.type == "source_file":  # Go source file
        if debug:
            print(f"{indent}Processing Go source file")
        # Process all children to get package, imports, and top-level nodes
        for child in node.children:
            outline.extend(
                get_outline_nodes(
                    child,
                    source_bytes,
                    expand_sections,
                    parent_expanded,
                    debug,
                    indent + "  ",
                    depth + 1,
                    max_depth,
                )
            )

    elif node.type in (
        "import_statement",
        "import_from_statement",
        "import_declaration",
    ):
        if debug:
            print(f"{indent}Found import: {node_text}")
        outline.append(node_text)

    elif node.type == "package_clause":
        if debug:
            print(f"{indent}Found package: {node_text}")
        outline.append(node_text)

    elif node.type == "const_declaration":
        if debug:
            print(f"{indent}Found const: {node_text}")
        outline.append(node_text)

    elif node.type in ("function_declaration", "method_declaration"):
        name = get_node_name(node, "function")
        is_expanded = should_expand("function", name) or parent_expanded

        if debug:
            print(f"{indent}Found function {name} (expanded: {is_expanded})")

        sig = get_signature(node, source_bytes)
        if is_expanded:
            outline.append(sig + " {")
            block = get_block_content_from_node(node, source_bytes)
            if block:
                outline.append(block)
            outline.append("}")
        else:
            outline.append(sig + " {")
            outline.append("    ...")
            outline.append("}")

    elif node.type == "type_declaration":
        name = get_node_name(node, "type")
        is_expanded = should_expand("type", name)

        if debug:
            print(f"{indent}Found type {name} (expanded: {is_expanded})")

        sig = get_signature(node, source_bytes)
        outline.append(sig + " {")

        if is_expanded:
            # For expanded types, include full definition
            block = get_block_content_from_node(node, source_bytes)
            if block:
                outline.append(block)
            else:
                # Try to find struct fields directly
                for child in node.children:
                    if child.type == "type_spec":
                        for subchild in child.children:
                            if subchild.type == "struct_type":
                                for field in subchild.children:
                                    if field.type == "field_declaration_list":
                                        for field_decl in field.children:
                                            if field_decl.type == "field_declaration":
                                                field_text = (
                                                    source_bytes[
                                                        field_decl.start_byte : field_decl.end_byte
                                                    ]
                                                    .decode("utf-8")
                                                    .strip()
                                                )
                                                outline.append("    " + field_text)
        else:
            outline.append("    ...")
        outline.append("}")

    elif node.type in ("class_definition", "function_definition", "method_definition"):
        node_type = node.type.split("_")[0]  # 'class' or 'function'
        name = get_node_name(node, node_type)
        is_expanded = should_expand(node_type, name) or parent_expanded

        if debug:
            print(f"{indent}Found {node_type} {name} (expanded: {is_expanded})")

        sig = get_signature(node, source_bytes)
        outline.append(indent + sig + ":")

        if is_expanded:
            # For expanded functions, include full definition
            if node_type != "class":
                block = get_block_content_from_node(node, source_bytes)
                if block:
                    outline.append(indent + "    " + block)
            # For expanded classes, show method signatures and nested classes
            else:
                for child in node.children:
                    if child.type == "block":
                        for item in child.children:
                            if item.type in (
                                "function_definition",
                                "method_definition",
                            ):
                                # Don't expand methods unless specifically requested
                                method_name = get_node_name(item, "function")
                                method_expanded = should_expand("function", method_name)
                                if method_expanded:
                                    # For expanded methods, include full definition
                                    method_sig = get_signature(item, source_bytes)
                                    outline.append(indent + "    " + method_sig + ":")
                                    method_block = get_block_content_from_node(
                                        item, source_bytes
                                    )
                                    if method_block:
                                        outline.append(
                                            indent + "        " + method_block
                                        )
                                else:
                                    # For collapsed methods, just show signature
                                    method_sig = get_signature(item, source_bytes)
                                    outline.append(indent + "    " + method_sig + ":")
                                    outline.append(indent + "        ...")
                            elif item.type == "class_definition":
                                # Recursively handle nested classes
                                nested_outline = get_outline_nodes(
                                    item,
                                    source_bytes,
                                    expand_sections,
                                    is_expanded,
                                    debug,
                                    indent + "    ",
                                    depth + 1,
                                    max_depth,
                                )
                                outline.extend(nested_outline)
        else:
            outline.append(indent + "    ...")

    elif node.type == "expression_statement":
        # For module-level constants (all caps assignments)
        if "=" in node_text:
            name = node_text.split("=")[0].strip()
            if name.isupper():
                if debug:
                    print(f"{indent}Found constant: {node_text}")
                outline.append(node_text)

    # For other nodes, only recurse if we haven't output anything
    # This ensures we don't miss nested definitions
    elif not outline:
        for child in node.children:
            child_outline = get_outline_nodes(
                child,
                source_bytes,
                expand_sections,
                parent_expanded,
                debug,
                indent + "  ",
                depth + 1,
                max_depth,
            )
            if child_outline:
                outline.extend(child_outline)

    return outline


class ReadFileOutlineTool(LLMTool):
    """Tool for reading a file and generating its outline."""

    def __init__(self, tool_call_logger: ToolCallLogger, root: Path):
        """Initialize the tool.

        Args:
            tool_call_logger: Logger for tool calls
            root: Root directory for resolving file paths
        """
        super().__init__(tool_call_logger)
        self.root = root
        self.name = "read_file_outline"
        self.description = (
            "Read a source code file and return its outline, showing imports, "
            "class/function signatures, and constants, but omitting implementations."
        )
        self.input_schema = {
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Path to the file to read",
                },
                "expand_sections": {
                    "type": "array",
                    "items": {
                        "type": "string",
                    },
                    "description": "List of sections to expand, in format 'type:name' (e.g., 'function:MyFunction', 'class:MyClass')",
                },
                "debug": {
                    "type": "boolean",
                    "description": "Whether to show debug output",
                },
                "max_depth": {
                    "type": "integer",
                    "description": "Maximum recursion depth for nested structures (default: 5)",
                    "minimum": 1,
                },
            },
            "required": ["file_path"],
        }

        # Initialize parser
        self.parser = get_parser("python")  # Default to Python

        # Load languages
        self.languages = {}
        for ext, lang_name in LANGUAGE_MAP.items():
            try:
                self.languages[ext] = get_language(lang_name)
            except Exception as e:
                print(f"Warning: Failed to load {lang_name} parser: {e}")

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[Any] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation."""
        path = self.root / tool_input["file_path"]
        if not path.exists():
            return ToolImplOutput("File does not exist", f"File {path} does not exist")

        # Get file extension and corresponding language
        ext = path.suffix.lower()
        if ext not in self.languages:
            return ToolImplOutput(
                f"Unsupported file type: {ext}",
                f"Cannot generate outline for {ext} files",
            )

        try:
            # Read file content
            content = path.read_bytes()

            # Parse the file
            self.parser.set_language(self.languages[ext])
            tree = self.parser.parse(content)

            # Get parameters from input
            expand_sections = tool_input.get("expand_sections", [])
            debug = tool_input.get("debug", False)
            max_depth = tool_input.get("max_depth", 5)

            # Generate outline
            outline = get_outline_nodes(
                tree.root_node,
                content,
                expand_sections,
                debug=debug,
                max_depth=max_depth,
            )

            # Format output
            output = "\n".join(outline)
            return ToolImplOutput(
                output,
                f"Generated outline for {path} with expanded sections: {expand_sections}",
            )

        except Exception as e:
            return ToolImplOutput(
                f"Error generating outline: {str(e)}",
                f"Failed to generate outline for {path}: {str(e)}",
            )

    def get_tool_start_message(self, tool_input: Dict[str, Any]) -> str:
        return f"Generating outline for {tool_input['file_path']}"


def test_python_outline_debug(outline_tool):
    """Test with debug output to see node types."""
    result = outline_tool.run_impl({"file_path": "test.py", "debug": True})
    print("\nDebug output:")
    print(result.tool_output)

#!/usr/bin/env python3
"""Test that ChangedFile objects have correct old_contents and new_contents."""

import os
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import MagicMock, patch

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl

# We don't need CLIInterface for this test
from research.agents.changed_file import ChangedFile


class TestChangedFileContents(unittest.TestCase):
    """Test that ChangedFile objects have correct old_contents and new_contents."""

    def setUp(self):
        """Set up a temporary workspace."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_root = Path(self.temp_dir)
        self.cache_dir = tempfile.mkdtemp()

        # Create a test file
        self.test_file_path = self.workspace_root / "test_file.txt"
        self.test_file_content = "This is the original content."
        self.test_file_path.write_text(self.test_file_content)

        # Create a mock AugmentPrototypingClient
        self.mock_client = MagicMock()

        # Create the WorkspaceManagerImpl
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.workspace_root,
            cache_root=Path(self.cache_dir),
        )

        # Take an initial snapshot
        self.initial_snapshot = self.workspace_manager.snapshot_workspace()

    def tearDown(self):
        """Clean up the temporary directories."""
        shutil.rmtree(self.temp_dir)
        shutil.rmtree(self.cache_dir)

    def test_changed_file_contents(self):
        """Test that ChangedFile objects have correct old_contents and new_contents."""
        # Modify the test file
        new_content = "This is the modified content."
        self.test_file_path.write_text(new_content)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Get the diff
        diff = self.workspace_manager.get_last_turn_diff()
        self.assertIsNotNone(diff, "Diff should not be None")

        # We don't need a CLIInterface for this test

        # Create ChangedFile objects from the diff
        changed_files = []
        for file_patch in diff:
            path = file_patch.path

            # Determine change type and paths
            if file_patch.is_added_file:
                changed_files.append(
                    ChangedFile.added(
                        new_path=path,
                        new_contents=file_patch.target_file or "",
                    )
                )
            elif file_patch.is_removed_file:
                changed_files.append(
                    ChangedFile.deleted(
                        old_path=path,
                        old_contents=file_patch.source_file or "",
                    )
                )
            elif file_patch.is_rename:
                changed_files.append(
                    ChangedFile.renamed(
                        old_path=file_patch.source_file or "",
                        new_path=path,
                        old_contents=file_patch.source_file or "",
                        new_contents=file_patch.target_file or "",
                    )
                )
            else:  # Modified
                changed_files.append(
                    ChangedFile.modified(
                        path=path,
                        old_contents=file_patch.source_file or "",
                        new_contents=file_patch.target_file or "",
                    )
                )

        # Verify that we have one changed file
        self.assertEqual(len(changed_files), 1, "Should have one changed file")

        # Verify that the changed file is a modified file
        self.assertEqual(
            changed_files[0].change_type, "MODIFIED", "File should be modified"
        )

        # This test demonstrates the issue with using file_patch.source_file and file_patch.target_file
        # as the content of the files. These properties actually contain the file paths, not the file contents.
        # The fix in interactive_agent.py reads the actual file contents from the workspace instead.

        # Verify that old_contents and new_contents are incorrect (contain paths, not content)
        self.assertIn(
            "a/",
            changed_files[0].old_contents,
            f"old_contents should contain 'a/' prefix, got: {changed_files[0].old_contents}",
        )
        self.assertIn(
            "b/",
            changed_files[0].new_contents,
            f"new_contents should contain 'b/' prefix, got: {changed_files[0].new_contents}",
        )

        # Verify that old_contents and new_contents do not contain the actual file contents
        self.assertNotIn(
            self.test_file_content,
            changed_files[0].old_contents,
            "old_contents should not contain the actual file content",
        )
        self.assertNotIn(
            new_content,
            changed_files[0].new_contents,
            "new_contents should not contain the actual file content",
        )


if __name__ == "__main__":
    unittest.main()

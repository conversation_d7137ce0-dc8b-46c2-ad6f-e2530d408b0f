# includes is an ordered list of gpt-neox config files to be loaded
includes:
- augment_configs/codegen-H/model/codegen-350M.stage1.yml
- augment_configs/codegen-H/train/big_batch.f.yml
- augment_configs/codegen-H/train/train_memory.yml
- augment_configs/codegen-H/lr/3e-4.yml
- augment_configs/codegen-H/mem/prelayerL16.yml
- augment_configs/codegen-H/mem/ignore_eod.yml
- augment_configs/codegen-H/mem/memory_train_on_gpu.yml
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: null
  description: null
  workspace: Dev
  project: Default
  max_restarts: 1
  #environment:
    #image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/dai_base:cuda-11.7-py-3.9-pytorch-2.0.1-gpt-neox-deepspeed-gpu-0.19.12
  perform_initial_validation: True  # Do a validation at iteration 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "gpu-small.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  # podspec_path: "8xH100.yaml"
  gpu_count: 1  # How many GPUs to ask for
  save_trial_best: 0  # How many of the best checkpoints to save
  # Optional for debugging - log every N tensors on rank 0
  # data_log_interval: 100
  # service_account_name: spark-sa

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"
  # source_checkpoint: 8fe74a82-5b7c-4067-82d2-9ecacfe0f0e4

  # Experiment args (comment out for eval)
  checkpoint_handling:
    action: gc
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"  # For training

  # Evaluation args (comment out for experiment)
  # eval_tasks: gitrepo_poly_C_small  # Space separated list of eval tasks
  # eval_results_prefix: /mnt/efs/augment/user/username/eval_results # Path and prefix for evaluation results
  # entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/eval.sh"  # For eval

# overrides is a dictionary of gpt-neox args to override the values in the above list of included config files
overrides:
  # WandB options. WANDB_API_KEY will come from the environment, or ~/.netrc if you are logged in.
  wandb_name: my_name
  wandb_project: my_project   # This probably needs to already exist in your wandb dashboard
  wandb_group: my_group
  # wandb_team: my_team

  # save: is ignored by determined; checkpoints are saved in s3
  # save: /mnt/efs/augment/checkpoints/pref_signal/test1_ft_beta0.95_s2048_lr1.6e-5_const

  data_path: /mnt/efs/augment/data/processed/github/github_small_text_document
  #train_data_paths: [/mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document]
  #valid_data_paths: []
  #test_data_paths: []

  # load: s3://dev-training-dai/3318bbaf-305e-4d64-bf4f-b44ca0bd4909
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi  # For GCP

  train_batch_size: 288
  train_micro_batch_size_per_gpu: 12
  gradient_accumulation_steps: 24
  # train_batch_size: 576 # for 64 GPU
  # train_micro_batch_size_per_gpu: 3 # for 64 GPU
  # gradient_accumulation_steps: 3 # for 64 GPU

  eval_interval: 20
  early_stopping: False
  early_stopping_threshold: 0.005

  train_iters: 40
  lr_decay_iters: 40  # If not set, defaults to train_iters
  warmup: 0.
  lr_decay_style: constant

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: Adam

  save_interval: 10
  keep_last_n_checkpoints: 2
  # to keep all checkpoints, also disable determined.enable_checkpoint_gc
  #keep_last_n_checkpoints: null
  no_save_optim: False

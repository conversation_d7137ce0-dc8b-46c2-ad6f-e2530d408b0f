---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-cw-auth-token
  namespace: tenant-augment-eng
spec:
  encryptedData:
    cw_auth_token: AgBRp9zMTt/jY8SN+cwUiiIkiC16Deh2R6LCCndKLvYtWC911Sxf3F8lfvE0NOFxHNC7jYRtgnfmtLEv8S9a6Wo2e/k8uBjCCA1p5JXIYVGXWRDDsf/JBl+AyaZ3dZcdsserOs7r7B0uLTgBs4OK5aeLj5s9AEB6praFtq5R2paNMWwML//fU0xZ1sGGXKlHltZOHv3k8xMAVwHQD/Z5eKl3iyf8jRGSjd3t+uC1lArDgCzrhYQDzAlNGslpYbJ5AfMLorNX8/UqXMFdNWm+GlrRimch91StHfMQxcMrbt4cauAHWbjfJGC61i4AdHIypQN+MaLxkuHU/v7Q0nPeRNA0EcbQIriMxP+hE+e+D7xjjRvyXEUKnSsUs3afS9Evd63dXhGW44ZR1psjdtsZWGBGjjyD8+8c3A8k3GHsdFkPMXKhAorjUDLjkA28bhCN5f2rweYq56eB9NcacGhDFjptc6ZhL2vkIxzYmB9HicbCCu94YI2GJAaxqnueQ2N0yiBGsUIGSr2kmQ0qQiUZ2ff/nYq4vfpYnXe2i41K3slvs2zIH706R8aNZvdFbdVr1FsvTVWokJkfjd2L4lKcqASH20esTMI/cKkIHT6Vt2EwTRACp1qcS6/H82qkhANGPK7DGdDPaHB26sRDfcrYpujjFhQu2uzy5xlVcc6uAGGVOxCMitQE927YJkjmhy/Fz1cAKsx8Bq7S4srA4lqgj8Dv3PYKItP92kicOh4peD0tUzo4qq3MKEJg
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-cw-auth-token
      namespace: tenant-augment-eng
---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-cw-determined-password
  namespace: tenant-augment-eng
spec:
  encryptedData:
    cw_determined_password: AgA1kc6JrbFDHw4j289VR+rrdSX5Jv4OgAYDLtZjnz2RA2bb4T6hEjFCOxULo/wRFQ07meMdbv9sJpwOZuDzA/n77lxaRfdz3QihffDYBKtsRsE8/GY+Kkiu6B8mgFzeh09+mjW8VD/z8wRHzgeSLY4cj+XEGVIyJ76M/Z3OwTLGxOLwNClWWmSvMKZ5piCdDL49yKcmeoP13b+WNQ0QbaukbVzP/S9U55ZcNWMqxH1bHd3UvNv2l9KOLxMUkYL0sgNodUSo9nCDETcjw1MlfRbtWiYHPZ4sd85swqwtPhouv352RGT5NsjA+oXNlUmTMXfd/CLT1kSgclnSoKGoRkTWZnV4L2Rcx0cdw0m//INi/AsEo9qldEIkOc8E/5VVeqdE3MPcS7/wXDNTrb/iqRkMBkAG/C6dcy1vQIdE4KmaU3ROFEANWi6qhX0iokj1ZjaK8yX9ImR9kIqAgrd68euIFDBzmEhjZeRvWqG2kB45mHfWrYKUhyqlkktQf79ZR6/YDYJBZp51jcEwnqzMxPKatCRtBHUu8zUpcxalFAOhESfCPS/dd6HDlyRWO5bypcBKGZtodxUM+EMOaZ/vtIf2KPcrBb0uu/SGzOGOegZy+ygJWh9zGw6peOHdBrDwjBM9mngmYfcmzb4SY5Xb1TVSlN3pGMYR2bbQ+UplruyEihMAZDTBWVyheJdksUZppWzlQ5zmszbB  # pragma: allowlist secret
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-cw-determined-password
      namespace: tenant-augment-eng
---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-cw-kubeconfig-b64
  namespace: tenant-augment-eng
spec:
  encryptedData:
    cw_kubeconfig_b64: 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
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-cw-kubeconfig-b64
      namespace: tenant-augment-eng
---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-gcp-determined-password
  namespace: tenant-augment-eng
spec:
  encryptedData:
    gcp_determined_password: AgABBqbgnBtOuvFzbnh98wOB/Z7oFHkGGX/9WzvVLqyWkP2cBsDNFIOaas4w9c00/WyF158d9OG/zcVbPHbkhSJAgFqJrqEj9H6y14atkTNo3b365sm/I8ZXhPlRSVmLKVwfDiYRZ63nBXbHpMH1OuL/V0rdKaIj975l+cyokJUgO0xHMWZPI276vY8sOgMyJ7C7Edt9uQNlgJXzVHifYKb3nD5tJQwtSgrnfuIEjRnyv9uVNDezYD81Ho3aOfeB1zQV1+NP6lWB6jXdxE6AaczjFk+XUl97n050O97Py6DJSNyVyyTruZPfMhpjDbMyaP+KoODmiGUB6EULrbK0UpcN1wHfWkFk6TYowzitpLFFBe0Uy59p78T9545Ozplee7ZLncajPzltX7oHrs98oN/qTYM4qrktayCCmGwYNAwDPpesM2NzFKbQJyj1A7Lr67sEY2DaT9VbovIsKFGcU5+hkItyOVgYdYzI8QnxRgnCBTkhllI/toi66uaVv+zqi17Hh6GzPm80kfO90A046dx9H+DTZpqZs2QYyvJO9/OI9zsxQS2Uuxgdq5sRPCoMCzSc04GJpo1CG6QIbHnTADfjYP2DV408FagqoVN+LXEGDKzsFTXn3g3gSD1VJYm1Li3G+C/dVYIyMlVBPSwtYjmi0kS0CzyjvWyoI1vBYchen/p/FPmj8KSdLW72U/ybWOvADQeB898d  # pragma: allowlist secret
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-gcp-determined-password
      namespace: tenant-augment-eng
---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-gsa-key
  namespace: tenant-augment-eng
spec:
  encryptedData:
    gsa_key.json: 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
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-gsa-key
      namespace: tenant-augment-eng
---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: cw-bot-slack-bot-token
  namespace: tenant-augment-eng
spec:
  encryptedData:
    slack_bot_token: AgBHIH4DEsbRZxdacP23x1KNf0BFpVjJ5onhJm0JOFv8NvnAyCqivR+NSwQTuigqA98R9CPvF5js7vvGtQVbg9GnxrLtufeqCqpn3YA9Jnr+8EI9YRpWUZ8gz6AUh/po/GL6vG1Haz2kkGCkX2Kb+PdBMZv2QeqfvZE/EUtjpcaYKDvmNbIZBZ0+thOjCa3gt2mUfw4BVMB8kBGnNe4fAhWO2hfVRbxSybxKDj/hfhOgUXzmr+BwiSOdxTe/3j5DIkN8Hd08Hi1hNsnrad8DYPHF/PXQlUYMa2qt4KKI6TiDN7VUHqnl70bRmRvxcHKRx/XKEK/2zFTPlQ8SgTOS7L0tOtolX2Xzc+WBNOGfpj9ZNAdpj6iY2Wyd56jcn10MGwbf1KK3f9Gh3cReRjovquXm/c/f1TIb8DAFOjD7nqUcBXCzdBV0rpAB1efYQ8Sk907NC4ldsGrKWf5m61q7tYnVK21PgF+q2AOBDB8tAplB3VKuwRDR7oiO+lAIeZOIqNgAai6iEW14zXLK3W3S+B0ymo6NQNOFwZZ9l4rmZN9Q7tGQFq5r+fk4Xd29tFa4bm7CvcCIvkkhB2JMVnDDxmKgtPyes0lQJokzpqeinILFb6/h7oR6t3Tem8Tig2yCid1BpowbtsQKR0E/0G2kV8ueidXdaSQNYnMr6KaJ7YSqMHWxtPnnnQSJ0ahGWBGCeLoWgGrgY0LrvEW3hUzrbBSBGY+hGjXZ343M7sraxg1jNgxg5/cpItKSBEm+2EOc6tnLko5bYifY1E0=
  template:
    metadata:
      creationTimestamp: null
      name: cw-bot-slack-bot-token
      namespace: tenant-augment-eng
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-metrics
  labels:
    app: cw-metrics
spec:
  schedule: "*/2 * * * *"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: cw-metrics
        spec:
          serviceAccountName: spark-sa
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: topology.kubernetes.io/region
                    operator: In
                    values:
                      - LAS1
          containers:
          - name: cw-ci-gc
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/cw-bot-image:**********
            imagePullPolicy: IfNotPresent
            command:
              - python
              - cw_metrics.py
              - --no-cache
              - --html-output
              - /mnt/efs/augment/public_html/cw-bot/index.html
              - --cloud
              - GCP
              - CW
            env:
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: /secrets/gsa_key.json
            - name: CW_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: cw-bot-cw-auth-token
                  key: cw_auth_token
            - name: CW_DETERMINED_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cw-bot-cw-determined-password
                  key: cw_determined_password
            - name: GCP_DETERMINED_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cw-bot-gcp-determined-password
                  key: gcp_determined_password
            - name: CW_KUBECONFIG_B64
              valueFrom:
                secretKeyRef:
                  name: cw-bot-cw-kubeconfig-b64
                  key: cw_kubeconfig_b64
            volumeMounts:
            - mountPath: /secrets/
              name: gsa-key
            - mountPath: /mnt/efs/augment/public_html/cw-bot/
              name: aug-cw-las1
              subPath: public_html/cw-bot
          volumes:
          - name: aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
          - name: cw-bot-cw-auth-token
            secret:
              secretName: cw-bot-cw-auth-token  #  pragma: allowlist secret
          - name: cw-bot-cw-determined-password
            secret:
              secretName: cw-bot-cw-determined-password  #  pragma: allowlist secret
          - name: cw-bot-gcp-determined-password
            secret:
              secretName: cw-bot-gcp-determined-password  #  pragma: allowlist secret
          - name: cw-bot-cw-kubeconfig-b64
            secret:
              secretName: cw-bot-cw-kubeconfig-b64  #  pragma: allowlist secret
          - name: gsa-key
            secret:
              secretName: cw-bot-gsa-key  #  pragma: allowlist secret
          restartPolicy: Never
      backoffLimit: 1

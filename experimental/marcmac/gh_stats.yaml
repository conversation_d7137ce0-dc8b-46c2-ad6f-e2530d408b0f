apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: gh-pr-stats
  labels:
    app: gh-pr-stats
spec:
  schedule: "*/10 * * * *"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: gh-pr-stats
        spec:
          serviceAccountName: spark-sa
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: topology.kubernetes.io/region
                    operator: In
                    values:
                      - LAS1
          containers:
          - name: gh-pr-stats
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/cw-bot-image:********-02
            imagePullPolicy: IfNotPresent
            command:
              - python
              - /mnt/efs/augment/user/marcmac/gh_stats/gh_pr_stats.py
              - --since
              - db
              - --update-db
              - --write-html
              - /mnt/efs/augment/public_html/cw-bot/gh-pr-stats
            env:
            - name: GH_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: gh-runner-monitor-token
                  key: token
            - name: METRICS_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: metrics-db-password
                  key: password
            volumeMounts:
            - mountPath: /mnt/efs/augment/public_html/cw-bot/
              name: aug-cw-las1
              subPath: public_html/cw-bot
            - mountPath: /mnt/efs/augment/user/marcmac/gh_stats/
              name: aug-cw-las1
              subPath: user/marcmac/gh_stats/
          volumes:
          - name: aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
          restartPolicy: Never
      backoffLimit: 1

#!/usr/bin/env python3

from datetime import datetime, timedelta
import argparse
import json
import os
import psycopg2
import requests
import matplotlib.pyplot as plt
import numpy
import pandas as pd

from pathlib import Path

url = "https://api.github.com/graphql"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {os.environ.get('GH_AUTH_TOKEN')}",
}
METRICS_DB_PASSWORD = os.environ.get("METRICS_DB_PASSWORD")
assert (
    METRICS_DB_PASSWORD is not None
), "Set the METRICS_DB_PASSWORD environment variable"

"""
create table pull_requests (
    pr_num integer,
    author varchar(30),
    state varchar(20),
    title varchar,
    created_at timestamp,
    updated_at timestamp,
    closed_at timestamp,
    merged_at timestamp
);

create table pull_request_events (
    pr_num integer,
    event_type varchar(30),
    actor varchar(30),
    state varchar(20),
    ts timestamp
);
"""
query_template = """
{
  search(
    first: %(batchsize)d, %(afterclause)s
    query: "repo:augmentcode/augment is:pr %(dateclause)s sort:updated-asc",
    type: ISSUE) {
      issueCount
      edges {
        cursor
        node {
          ... on PullRequest {
            author { login }
            number
            title
            createdAt
            updatedAt
            state
            timelineItems(
              first: 100,
              itemTypes:[
                REVIEW_REQUESTED_EVENT,
                REOPENED_EVENT,
                MERGED_EVENT,
                CLOSED_EVENT,
                READY_FOR_REVIEW_EVENT,
                PULL_REQUEST_REVIEW,
                ISSUE_COMMENT
              ]
            ) {
              nodes {
                __typename
                ... on ReviewRequestedEvent {
                  createdAt
                  actor { login }
                  requestedReviewer {
                    ... on User {
                      login
                    }
                  }
                }
                ... on PullRequestReview {
                  createdAt
                  author {
                    login
                  }
                  state
                }
                ... on MergedEvent {
                  createdAt
                  actor { login }
                }
                ... on ClosedEvent {
                  createdAt
                  actor { login }
                }
                ... on ReadyForReviewEvent {
                  createdAt
                  actor { login }
                }
                ... on IssueComment {
                  createdAt
                  author {
                    login
                  }
                }
              }
            }
          }
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
}
"""

# Return data looks something like this:
"""
{
  "data": {
    "repository": {
      "pullRequests": {
        "totalCount": 834,
        "edges": [
					{
						"cursor": "Y3Vyc29yOnYyOpK5MjAyMi0wNy0wN1QxNzoxMDozNi0wNzowMM47Ec9R",
						"node": {
							"number": 1,
							"title": "Convert the gpt-neox directory to a subtree tracking of the upstream GPT-NeoX codebase",
							"createdAt": "2022-07-08T00:10:36Z",
							"timelineItems": {
								"nodes": [
									{
										"__typename": "MergedEvent",
										"createdAt": "2022-07-08T00:26:06Z"
									},
									{
										"__typename": "ClosedEvent",
										"createdAt": "2022-07-08T00:26:06Z"
									}
								]
							}
						}
					},
"""


def get_connection() -> psycopg2.extensions.connection:
    conn = psycopg2.connect(
        dbname="metrics",
        user="augment",
        password=os.environ.get("METRICS_DB_PASSWORD"),
        host="devex-metrics-postgresql-hl",
    )
    return conn


def convert_sql_time_graphql_time(sql_time: datetime) -> str:
    """Convert a SQL timestamp to a GraphQL timestamp."""
    return sql_time.strftime("%Y-%m-%dT%H:%M:%SZ")


def get_last_update_time() -> str:
    """Get the last update time from the DB."""
    query = """
    select max(updated_at) from pull_requests
    """
    conn = get_connection()
    with conn, conn.cursor() as cur:
        cur.execute(query)
        last = cur.fetchone()
        if last[0] is None:
            last = datetime.now() - timedelta(days=365 * 10)
        else:
            last = last[0]
        return convert_sql_time_graphql_time(last)


def iso_timestamp(ts):
    """Fix a timestamp iso format for UTC timezone"""
    try:
        return datetime.fromisoformat(ts["createdAt"].replace("Z", "+00:00"))
    except:  # noqa: E722
        return datetime.fromisoformat(ts.replace("Z", "+00:00"))


def format_timedelta(td):
    """How in god's name is this not a built-in yet?

    td -> HH:MM:SS
    """
    hr, r = divmod(td.total_seconds(), 3600)
    min, sec = divmod(r, 60)
    return f"{int(hr):02}:{int(min):02}:{int(sec):02}"


def td_mean(a):
    if a:
        return format_timedelta(timedelta(seconds=numpy.mean(a)))
    return format_timedelta(timedelta(seconds=0))


def td_std(a):
    if a:
        return format_timedelta(timedelta(seconds=numpy.std(a)))
    return format_timedelta(timedelta(seconds=0))


class PullRequestEvent:
    def __init__(self, pr_number, event):
        self._init_from_gh(pr_number, event)

    def _init_from_gh(self, pr_number, event):
        self.pr = pr_number
        self.type_name = event["__typename"]
        self.ts = iso_timestamp(event["createdAt"])
        self.actor = (
            event["actor"]["login"] if "actor" in event else event["author"]["login"]
        )
        self.state = event["state"] if "state" in event else None

    def __str__(self) -> str:
        return f"PR #{self.pr} {self.type_name} {self.actor} {self.ts}"


class PullRequest:
    def __init__(self, pr):
        self._init_from_gh(pr)

    def _init_from_gh(self, pr):
        # print(pr)
        self.number = pr["number"]
        self.title = pr["title"]
        self.state = pr["state"]
        self.creation_ts = iso_timestamp(pr["createdAt"])
        self.update_ts = iso_timestamp(pr["updatedAt"])
        self.author = pr["author"]["login"]
        try:
            self.events = [
                PullRequestEvent(self.number, event)
                for event in pr["timelineItems"]["nodes"]
            ]
        except:  # noqa: E722
            self.events = []

    def __str__(self):
        return f"PR #{self.number} {self.state}"

    @property
    def merged_ts(self):
        for event in self.events:
            if event.type_name == "MergedEvent":
                return event.ts

    @property
    def closed_ts(self):
        for event in self.events:
            if event.type_name == "ClosedEvent":
                return event.ts

    @property
    def time_to_merge(self):
        try:
            return self.merged_ts - self.creation_ts  # type: ignore
        except:  # noqa: E722
            return timedelta(0)

    @property
    def time_to_close(self):
        try:
            return self.closed_ts - self.creation_ts
        except:  # noqa: E722
            return timedelta(0)

    @property
    def reviews(self):
        return [e for e in self.events if e["__typename"] == "PullRequestReview"]

    @property
    def review_requests(self):
        return [e for e in self.events if e["__typename"] == "ReviewRequestedEvent"]

    @property
    def approvals(self):
        return [e for e in self.reviews if e["state"] == "APPROVED"]

    @property
    def time_to_first_review(self):
        try:
            return iso_timestamp(self.reviews[0]) - self.creation_ts
        except:  # noqa: E722
            return timedelta(0)

    @property
    def time_to_first_requested_review(self):
        try:
            return iso_timestamp(self.reviews[0]) - iso_timestamp(
                self.review_requests[0]
            )
        except:  # noqa: E722
            return timedelta(0)

    @property
    def time_to_first_approval(self):
        try:
            return iso_timestamp(self.approvals[0]) - self.creation_ts
        except:  # noqa: E722
            return timedelta(0)


def get_next_batch(since, batchsize=10, last_cursor=None):
    afterclause = ""

    if last_cursor is not None:
        afterclause = f'after: "{last_cursor}",'

    query = query_template % {
        "batchsize": batchsize,
        "afterclause": afterclause,
        "dateclause": f"updated:>={since}",
    }
    # print(query)
    response = requests.request(
        "POST",
        url,
        data=json.dumps({"query": query}),
        headers=headers,
    )
    response.raise_for_status()
    # print(json.dumps(response.json(), indent=2))

    data = response.json()["data"]["search"]
    page_info = data["pageInfo"]
    total_count = data["issueCount"]
    prs = data["edges"]

    last_cursor = prs[-1]["cursor"]
    has_next_page = page_info["hasNextPage"]

    return prs, has_next_page, last_cursor, total_count


def fetch_all(max_fetches, batch_size, since, show_progress=False):
    last_cursor = None
    has_next_page = True
    fetch_count = 0
    pull_requests = []

    while has_next_page and fetch_count < max_fetches:
        prs, has_next_page, last_cursor, total_count = get_next_batch(
            since, batch_size, last_cursor
        )
        pull_requests.extend([PullRequest(pr["node"]) for pr in prs])  # type: ignore
        fetch_count = len(pull_requests)
        if show_progress:
            print(f"-- Fetched {fetch_count}/{total_count}")  # type: ignore
    return pull_requests


def summarize(pull_requests):
    print("num state   -   ttm    -   ttc    -   ttfr   -   ttfrr  -  ttfa")
    for pr in pull_requests:
        print(
            f"{pr.number:04} {pr.state:6} - {format_timedelta(pr.time_to_merge)} - {format_timedelta(pr.time_to_close)} - {format_timedelta(pr.time_to_first_review)} - {format_timedelta(pr.time_to_first_requested_review)} - {format_timedelta(pr.time_to_first_approval)}"
        )

    nz_ttm = [
        pr.time_to_merge.total_seconds()
        for pr in pull_requests
        if pr.time_to_merge.total_seconds() != 0
    ]
    nz_ttc = [
        pr.time_to_close.total_seconds()
        for pr in pull_requests
        if pr.time_to_close.total_seconds() != 0
    ]
    nz_ttfr = [
        pr.time_to_first_review.total_seconds()
        for pr in pull_requests
        if pr.time_to_first_review.total_seconds() != 0
    ]
    nz_ttfrr = [
        pr.time_to_first_requested_review.total_seconds()
        for pr in pull_requests
        if pr.time_to_first_requested_review.total_seconds() != 0
    ]
    nz_ttfa = [
        pr.time_to_first_approval.total_seconds()
        for pr in pull_requests
        if pr.time_to_first_approval.total_seconds() != 0
    ]
    print("=" * 66)
    print("            -   ttm    -   ttc    -   ttfr   -   ttfrr  -  ttfa")
    print(
        f"mean        - {td_mean(nz_ttm)} - {td_mean(nz_ttc)} - {td_mean(nz_ttfr)} - {td_mean(nz_ttfrr)} - {td_mean(nz_ttfa)}"
    )
    print(
        f"std       - {td_std(nz_ttm)} - {td_std(nz_ttc)} - {td_std(nz_ttfr)} - {td_std(nz_ttfrr)} - {td_std(nz_ttfa)}"
    )


def update_db(pull_requests):
    """Format data for inserting into the db.

    For every updated PR, we'll delete any existing events so we don't have duplicates.
    """
    pr_insert = """
        INSERT INTO pull_requests (
            pr_num,
            author,
            state,
            title,
            created_at,
            updated_at,
            merged_at,
            closed_at
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    event_insert = """
        INSERT INTO pull_request_events (
            pr_num,
            event_type,
            actor,
            state,
            ts
        )
        VALUES (%s, %s, %s, %s, %s)
    """

    conn = get_connection()
    print(f"-- Updating {len(pull_requests)} PRs")
    with conn, conn.cursor() as cur:
        for pr in pull_requests:
            cur.execute(f"DELETE FROM pull_request_events WHERE pr_num = {pr.number};")
            cur.execute(f"DELETE FROM pull_requests WHERE pr_num = {pr.number};")
            cur.execute(
                pr_insert,
                (
                    pr.number,
                    pr.author,
                    pr.state,
                    pr.title,
                    pr.creation_ts,
                    pr.update_ts,
                    pr.merged_ts,
                    pr.closed_ts,
                ),
            )
            for event in pr.events:
                cur.execute(
                    event_insert,
                    (
                        pr.number,
                        event.type_name,
                        event.actor,
                        event.state,
                        event.ts,
                    ),
                )


def plot_median_tta(dirname):
    # median time to first approval for prs by week over the last year
    query = """
    select date_trunc('week', created_at) as week,
        percentile_cont(0.5) within group
        (order by extract (epoch from(first_approval_ts - created_at)))
        as time_to_first_review_50,
        percentile_cont(0.9) within group
        (order by extract (epoch from(first_approval_ts - created_at)))
        as time_to_first_review_90
        from pull_requests, (
            select pr_num, min(ts) as first_approval_ts
            from pull_request_events
            where event_type = 'PullRequestReview' and state = 'APPROVED' group by pr_num
        ) as first_approvals
        where pull_requests.pr_num = first_approvals.pr_num
        and first_approval_ts is not null and
        created_at > now() - interval '.5 year' group by week order by week asc;
    """

    conn = get_connection()
    with conn, conn.cursor() as cur:
        cur.execute(query)
        rows = cur.fetchall()
        df = pd.DataFrame(
            rows,
            columns=["week", "time_to_first_approval_50", "time_to_first_approval_90"],
        )

    df["Median time to first approval"] = df["time_to_first_approval_50"].astype(
        float
    ) / (60 * 60)
    # df["time_to_first_approval_90_pct"] = df["time_to_first_approval_90"].astype(float) / (60*60)
    # print(df.dtypes)
    df.plot(
        x="week",
        y=[
            "Median time to first approval",
            # "time_to_first_approval_90_pct"
        ],
        # logy=True,
        xlabel="Week",
        ylabel="Time to first approval (hours)",
        title="Time to first approval by week",
    )
    plt.savefig(dirname / "median_tta.png")


def plot_median_ttr(dirname):
    # median time to first approval for prs by week over the last year
    query = """
    select date_trunc('week', created_at) as week,
        percentile_cont(0.5) within group
        (order by extract (epoch from(first_review_ts - created_at)))
        as time_to_first_review_50,
        percentile_cont(0.9) within group
        (order by extract (epoch from(first_review_ts - created_at)))
        as time_to_first_review_90
        from pull_requests, (
            select pr_num, min(ts) as first_review_ts
            from pull_request_events
            where event_type = 'PullRequestReview' group by pr_num
        ) as first_reviews
        where pull_requests.pr_num = first_reviews.pr_num
        and first_review_ts is not null and
        created_at > now() - interval '.5 year' group by week order by week asc;
    """

    conn = get_connection()
    with conn, conn.cursor() as cur:
        cur.execute(query)
        rows = cur.fetchall()
        df = pd.DataFrame(
            rows, columns=["week", "time_to_first_review_50", "time_to_first_review_90"]
        )

    df["Median time to first review"] = df["time_to_first_review_50"].astype(float) / (
        60 * 60
    )
    # df["time_to_first_review_90_pct"] = df["time_to_first_review_90"].astype(float) / (60*60)
    df.plot(
        x="week",
        y=[
            "Median time to first review",
            # "time_to_first_review_90_pct"
        ],
        # logy=True,
        xlabel="Week",
        ylabel="Time to first Review (hours)",
        title="Time to first Review by week",
    )
    plt.savefig(dirname / "median_ttr.png")


def write_html(dirname):
    if not dirname.exists():
        dirname.mkdir(parents=True)
    plot_median_tta(dirname)
    plot_median_ttr(dirname)
    filename = dirname / "index.html"
    with open(filename, "w") as f:
        f.write("<html><body>\n")
        f.write(f"<h1>Pull Requests {datetime.now()}</h1>\n")
        f.write("<img src='median_tta.png'/><br/>\n")
        f.write("<img src='median_ttr.png'/><br/>\n")
        f.write("</body></html>\n")


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--max-fetches",
        type=int,
        default=1000,
        help="Maximum number of fetches to make",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Batch size for fetches",
    )
    parser.add_argument(
        "--summarize",
        action="store_true",
        help="Summarize the fetched pull requests",
    )
    parser.add_argument(
        "--since",
        type=str,
        default="2010-01-01T00:00:00Z",
        help="Timestamp to start fetching from (format: YYYY-MM-DDTHH:MM:SSZ)",
    )
    parser.add_argument(
        "--update-db",
        action="store_true",
        help="update the db",
    )

    parser.add_argument(
        "--write-html",
        type=Path,
        default=None,
        help="Write html to the specified directory",
    )

    return parser.parse_args()


def main():
    args = parse_args()

    if args.since == "db":
        since = get_last_update_time()
    else:
        since = args.since
    if args.update_db:
        pull_requests = fetch_all(
            args.max_fetches, args.batch_size, since, show_progress=True
        )
        if args.summarize:
            summarize(pull_requests)
        update_db(pull_requests)
    if args.write_html:
        write_html(args.write_html)


if __name__ == "__main__":
    main()

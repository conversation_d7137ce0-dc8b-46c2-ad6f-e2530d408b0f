apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-hung-experiment
spec:
  schedule: "@daily"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: spark-sa
          containers:
          - name: cw-hung-experiment
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_determined_gc:latest
            env:
            - name: DET_MASTER
              value: "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud"
            - name: DET_USER
              value: "github"
            - name: DET_PASS
              valueFrom:
                secretKeyRef:
                  name: gh-determined-password
                  key: password
            - name: SLACK_WEBHOOK_URL
              valueFrom:
                secretKeyRef:
                  name: infra-alerts-webhook-url
                  key: url
            - name: CW_KUBECONFIG_B64
              valueFrom:
                secretKeyRef:
                  name: cw-bot-cw-kubeconfig-b64
                  key: cw_kubeconfig_b64
            command:
              - /opt/conda/bin/python
              - /mnt/efs/augment/user/marcmac/cw_ci_gc/determined_hung_experiment.py
            volumeMounts:
            - mountPath: /mnt/efs/augment
              name: aug-cw-las1
          volumes:
          - name: aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
          - name: cw-bot-cw-kubeconfig-b64
            secret:
              secretName: cw-bot-cw-kubeconfig-b64  #  pragma: allowlist secret
          restartPolicy: Never
      backoffLimit: 1

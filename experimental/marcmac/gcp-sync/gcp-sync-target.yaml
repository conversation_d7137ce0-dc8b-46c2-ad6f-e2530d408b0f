apiVersion: v1
data:
  authorized_keys: |
    ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAGegor5kvIsPyZ3TgCbAQcIATGsf8pwJcxpi36106jN augment@gcp-sync-target
    ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIKJK0cfr/87C9eP7+BMYm41YEfqPhcD1vDT9HDWt2moG augment@gcp-sync-target
  startup_script: |
    mkdir -p /home/<USER>/.ssh
    cat /startup_script/authorized_keys > /home/<USER>/.ssh/authorized_keys
    chown -R augment:augment /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    chmod 600 /home/<USER>/.ssh/authorized_keys
    dpkg-reconfigure openssh-server
    service ssh start
    trap : TERM INT
    sleep infinity & wait
kind: ConfigMap
metadata:
  name: gcp-sync-config-startup-script
  namespace: gcp-us1
---
apiVersion: v1
kind: Service
metadata:
  labels:
    augmentDevPod: "true"
    podName: gcp-sync-target
  name: gcp-sync-target
  namespace: gcp-us1
spec:
  ports:
  - port: 22
    protocol: TCP
    targetPort: 22
  selector:
    augmentDevPod: "true"
    app: gcp-sync-target
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: gcp-sync-target
  name: gcp-sync-target
  namespace: gcp-us1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gcp-sync-target
  template:
    metadata:
      labels:
        app: gcp-sync-target
        augmentDevPod: "true"
    spec:
      tolerations:
      - effect: NoSchedule
        key: r.augmentcode.com/pool-type
        value: svc
      affinity:
        podAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: augmentDevPod
                  operator: Exists
              topologyKey: kubernetes.io/hostname
            weight: 100
      containers:
      - name: cw-dev
        command:
        - /usr/bin/dumb-init
        - --verbose
        - --
        - /bin/bash
        - /startup_script/startup_script
        image: us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment_devpod_cpu:ubuntu22.04-py-3.11.7-spark-3.4.3-21-devpod21
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: "4"
            memory: 32Gi
          requests:
            cpu: "4"
            memory: 32Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /mnt/efs/augment/checkpoints
          name: aug-checkpoints
        - mountPath: /mnt/efs/augment/configs
          name: aug-configs
        - mountPath: /mnt/efs/augment/data
          name: aug-data
        - mountPath: /mnt/efs/augment/eval
          name: aug-eval
        - mountPath: /mnt/efs/augment/external_models
          name: aug-external-models
        - mountPath: /mnt/efs/augment/ftm_checkpoints
          name: aug-ftm-checkpoints
        - mountPath: /mnt/efs/augment/lib
          name: aug-lib
        - mountPath: /mnt/efs/augment/mem
          name: aug-mem
        - mountPath: /mnt/efs/augment/python_env
          name: aug-python-env
        - mountPath: /mnt/efs/augment/user
          name: aug-user
        - mountPath: /mnt/efs/spark-data
          name: aug-spark-data
        - mountPath: /run/determined/secrets/cw-bot-token
          name: cw-bot-token
          readOnly: true
        - mountPath: /run/determined/secrets/determined-sa-secret
          name: determined-sa-secret
          readOnly: true
        - mountPath: /dev/shm
          name: dshm
          readOnly: false
        - mountPath: /startup_script
          name: startup-script
          readOnly: true
      securityContext:
        fsGroup: 1000
      serviceAccount: determined-service-account
      serviceAccountName: determined-service-account
      volumes:
      - name: aug-checkpoints
        persistentVolumeClaim:
          claimName: aug-checkpoints
      - name: aug-configs
        persistentVolumeClaim:
          claimName: aug-configs
      - name: aug-data
        persistentVolumeClaim:
          claimName: aug-data
      - name: aug-eval
        persistentVolumeClaim:
          claimName: aug-eval
      - name: aug-external-models
        persistentVolumeClaim:
          claimName: aug-external-models
      - name: aug-ftm-checkpoints
        persistentVolumeClaim:
          claimName: aug-ftm-checkpoints
      - name: aug-lib
        persistentVolumeClaim:
          claimName: aug-lib
      - name: aug-mem
        persistentVolumeClaim:
          claimName: aug-mem
      - name: aug-python-env
        persistentVolumeClaim:
          claimName: aug-python-env
      - name: aug-user
        persistentVolumeClaim:
          claimName: aug-user
      - name: aug-spark-data
        persistentVolumeClaim:
          claimName: aug-spark-data
      - name: cw-bot-token
        secret:
          defaultMode: 420
          secretName: cw-bot-token # pragma: allowlist secret
      - name: determined-sa-secret
        secret:
          defaultMode: 420
          secretName: gh-determined-password # pragma: allowlist secret
      - emptyDir:
          medium: Memory
        name: dshm
      - name: startup-script
        configMap:
          name: gcp-sync-config-startup-script
          items:
          - key: startup_script
            path: startup_script
          - key: authorized_keys
            path: authorized_keys

-- Pr numbers in the last 2 weeks
select pr_num from pull_requests where updated_at > now() - interval '2 weeks';

-- Pr numbers in the last 2 weeks, with a merge time
select pr_num from pull_requests where updated_at > now() - interval '2 weeks' and merged_at is not null;

-- Count of PRs merged per week over the last 90 days
select date_trunc('week', merged_at) as week, count(*) from pull_requests where merged_at is not null and merged_at > now() - interval '90 days' group by week order by week;

-- Prs merged in the last week with time from open to merge
select pr_num, merged_at - created_at as time_to_merge from pull_requests where merged_at is not null and merged_at > now() - interval '1 week' order by time_to_merge;

-- Average time to merge for prs merged in the last week
select avg(extract (epoch from(merged_at - created_at)))
    as time_to_merge from pull_requests
    where merged_at is not null and merged_at > now() - interval '1 week';

-- average time to merge for prs by week over the last year
select date_trunc('week', merged_at) as week,
    avg(extract (epoch from(merged_at - created_at)))
    as time_to_merge from pull_requests
    where merged_at is not null
    and merged_at > now() - interval '1 year'
    group by week order by week;

-- First review timestamp for a PR
select pr_num, min(ts) as first_review_ts from pull_request_events where event_type = 'PullRequestReview' group by pr_num;

-- average time to first review for prs by week over the last year
select date_trunc('week', created_at) as week,
    avg(extract (epoch from(first_review_ts - created_at))) as time_to_first_review
    from pull_requests, (
        select pr_num, min(ts) as first_review_ts
        from pull_request_events
        where event_type = 'PullRequestReview' group by pr_num
    ) as first_reviews
    where pull_requests.pr_num = first_reviews.pr_num
    and first_review_ts is not null and
    created_at > now() - interval '1 year' group by week order by week asc;

-- average time to first approval for prs by week over the last year
select date_trunc('week', created_at) as week,
    avg(extract (epoch from(first_approval_ts - created_at))) as time_to_first_approval
    from pull_requests, (
        select pr_num, min(ts) as first_approval_ts
        from pull_request_events
        where event_type = 'PullRequestReview' and state = 'APPROVED' group by pr_num
    ) as first_approvals
    where pull_requests.pr_num = first_approvals.pr_num
    and first_approval_ts is not null and
    created_at > now() - interval '1 year' group by week order by week asc;

-- average time to first requested review for prs by week over the last year
select date_trunc('week', created_at) as week,
    avg(extract (epoch from(first_approval_ts - first_requested_review_ts)))
    as time_to_first_requested_review
    from pull_requests, (
        select pr_num, min(ts) as first_approval_ts
        from pull_request_events
        where event_type = 'PullRequestReview' group by pr_num
    ) as first_approvals, (
        select pr_num, min(ts) as first_requested_review_ts
        from pull_request_events
        where event_type = 'ReviewRequestedEvent' group by pr_num
    ) as first_requested_review
    where pull_requests.pr_num = first_requested_review.pr_num
    and pull_requests.pr_num = first_approvals.pr_num
    and first_requested_review_ts is not null and
    created_at > now() - interval '1 year' group by week order by week asc;

-- average time to first approval by pr author over the last year
select author,
    round(avg(extract (epoch from(first_approval_ts - first_requested_review_ts))) / (60*60)::numeric, 2)
    as time_to_first_approval
    from pull_requests, (
        select pr_num, min(ts) as first_approval_ts
        from pull_request_events
        where event_type = 'PullRequestReview' and state = 'APPROVED' group by pr_num
    ) as first_approvals, (
        select pr_num, min(ts) as first_requested_review_ts
        from pull_request_events
        where event_type = 'ReviewRequestedEvent' group by pr_num
    ) as first_requested_review
    where pull_requests.pr_num = first_approvals.pr_num
    and pull_requests.pr_num = first_requested_review.pr_num
    and first_approval_ts is not null and
    created_at > now() - interval '1 year' group by author
    order by time_to_first_approval;

-- average time to review by reviewer over the last year
select actor,
    round(avg(extract (epoch from(ts - created_at))) / (60*60)::numeric, 2)
    as time_to_review
    from pull_requests, pull_request_events
    where pull_requests.pr_num = pull_request_events.pr_num
    and event_type = 'PullRequestReview' and
    created_at > now() - interval '6 months' group by actor
    order by time_to_review;

-- average time to merge by pr author over the last month
select author,
    avg(extract (epoch from(merged_at - created_at))) as time_to_merge
    from pull_requests where merged_at is not null and
    merged_at > now() - interval '1 month' group by author
    order by time_to_merge;

-- time to give review - time to receive approval : negative is better
select a.author, time_to_give_review, time_to_receive_first_approval,
    time_to_give_review - time_to_receive_first_approval as review_delta
from
    (select author,
        round(avg(extract (epoch from(first_approval_ts - first_requested_review_ts))) / (60*60)::numeric, 2)
        as time_to_receive_first_approval
        from pull_requests, (
            select pr_num, min(ts) as first_approval_ts
            from pull_request_events
            where event_type = 'PullRequestReview' and state = 'APPROVED' group by pr_num
        ) as first_approvals, (
            select pr_num, min(ts) as first_requested_review_ts
            from pull_request_events
            where event_type = 'ReviewRequestedEvent' group by pr_num
        ) as first_requested_review
        where pull_requests.pr_num = first_approvals.pr_num
        and pull_requests.pr_num = first_requested_review.pr_num
        and first_approval_ts is not null and
        created_at > now() - interval '1 year' group by author
    ) as a,
    (
        select actor as author,
            round(avg(extract (epoch from(ts - created_at))) / (60*60)::numeric, 2)
            as time_to_give_review
            from pull_requests, pull_request_events
            where pull_requests.pr_num = pull_request_events.pr_num
            and event_type = 'PullRequestReview' and
            created_at > now() - interval '1 year' group by actor
    ) as b
    where a.author = b.author
    order by review_delta;


-- most prolific reviewers

apiVersion: v1
kind: ServiceAccount
metadata:
  name: determined-master-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
     app: determined-master-{{ .Release.Name }}
     release: {{ .Release.Name }}


---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: determined-master-{{ .Release.Name }}
  labels:
     app: determined-master-{{ .Release.Name }}
     release: {{ .Release.Name }}
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/status", "pods/log", "configmaps"]
    verbs: ["create", "get", "list", "delete"]
  - apiGroups: [""]
    resources: ["services", "resourcequotas"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["watch", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["list", "watch"]
  - apiGroups: ["scheduling.k8s.io"]
    resources: ["priorityclasses"]
    verbs: ["create", "get", "list", "delete"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["list", "watch", "patch"]


---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: determined-master-{{ .Release.Name }}
  labels:
     app: determined-master-{{ .Release.Name }}
     release: {{ .Release.Name }}
subjects:
  - kind: ServiceAccount
    name: determined-master-{{ .Release.Name }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: determined-master-{{ .Release.Name }}
  apiGroup: rbac.authorization.k8s.io

{{- if .Values.createNonNamespacedObjects }}
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: determined-system-priority
value: 1000000
preemptionPolicy: Never
globalDefault: false
description: "This priority class should be used for Determined system pods only."
{{ end }}
---
{{- if .Values.createNonNamespacedObjects }}
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: determined-medium-priority
value: 50
preemptionPolicy: Never
globalDefault: false
description: "This priority class should be used for medium priority Determined jobs."
{{ end }}

apiVersion: apps/v1
kind: Deployment
metadata:
  name: determined-master-deployment-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-master-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: determined-master-{{ .Release.Name }}
  template:
    metadata:
      labels:
        app: determined-master-{{ .Release.Name }}
        determined-system: master
      annotations:
        # This is added so that the master deployment restarts when an upgrade occurs that
        # changes the master-config.yaml.
        checksum/config: {{ include (print $.Template.BasePath "/master-config.yaml") . | sha256sum }}
    spec:
      priorityClassName: determined-system-priority
      serviceAccount: determined-master-{{ .Release.Name }}
      containers:
      - name: determined-master-{{ .Release.Name }}
        {{ $image := "determined-master" }}
        {{- if .Values.enterpriseEdition -}}
          {{ $image = "hpe-mlde-master" }}
        {{- end -}}
        {{ $tag := (required "A valid Chart.AppVersion entry required!" .Chart.AppVersion) }}
        {{- /* detVersion is used for CI to override the appVersion. */ -}}
        {{- if .Values.detVersion -}}
          {{ $tag = .Values.detVersion }}
        {{- end -}}
        image: {{ .Values.imageRegistry }}/{{ $image }}:{{ $tag }}
        imagePullPolicy: "Always"
        {{- if .Values.enterpriseEdition }}
        {{- if .Values.oidc }}
        env:
          - name: DET_OIDC_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ required "A valid client secret name is required!" .Values.oidc.clientSecretName }}
                key: {{ required "A valid client secret filename is required!" .Values.oidc.clientSecretKey }}
                optional: false
        {{- end }}
        {{- end }}
        volumeMounts:
          - name: master-config
            mountPath: /etc/determined/
            readOnly: true
          {{- if and (.Values.checkpointStorage.mountToServer) (eq .Values.checkpointStorage.type "shared_fs") }}
          - name: checkpoint-storage
            mountPath: /determined_shared_fs
          {{ end }}
          {{- if .Values.tlsSecret }}
          - name: tls-secret
            mountPath: {{ include "determined.secretPath" . }}
            readOnly: true
          {{ end }}
          {{- if .Values.db.certResourceName }}
          - name: database-cert
            mountPath: {{ include "determined.secretPath" . }}
            readOnly: true
          {{- end }}
          # Additional volume mount for ca.crt or boundle to perform the ca cert injection
          {{- if .Values.externalCaCertSecretName }}
          - name: etc-ssl-certs
            mountPath: /etc/ssl/certs
            readOnly: true
          {{- end }}
          # end - Additional volume mount
        resources:
          requests:
            {{- if .Values.masterCpuRequest }}
            cpu: {{ .Values.masterCpuRequest  | quote }}
            {{- end }}
            {{- if .Values.masterMemRequest }}
            memory: {{ .Values.masterMemRequest  | quote }}
            {{- end}}

          {{- if or .Values.masterCpuLimit .Values.masterMemLimit }}
          limits:
            {{- if .Values.masterCpuLimit }}
            cpu: {{ .Values.masterCpuLimit  | quote }}
            {{- end }}
            {{- if .Values.masterMemLimit }}
            memory: {{ .Values.masterMemLimit  | quote }}
            {{- end}}
          {{- end}}
      # Init container to update ca.crt or ca bundle into the master image
      {{- if .Values.externalCaCertSecretName }}
      initContainers:
      - name: update-ca-certificates
        command:
          - sh
          - -c
          - update-ca-certificates --fresh
        image: {{ .Values.imageRegistry }}/{{ $image }}:{{ $tag }}
        imagePullPolicy: "Always"
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /usr/local/share/ca-certificates/
            name: usr-local-share-ca-certificates
          - mountPath: /etc/ssl/certs
            name: etc-ssl-certs
      {{- end }}
      # end - Init container
      {{- if .Values.imagePullSecretName}}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecretName }}
      {{- end}}
      volumes:
        - name: master-config
          secret:
            secretName: determined-master-config-{{ .Release.Name }}
        {{- if and (.Values.checkpointStorage.mountToServer) ( eq .Values.checkpointStorage.type "shared_fs") }}
        - name: checkpoint-storage
          hostPath:
            path: {{ .Values.checkpointStorage.hostPath }}
            type: Directory
        {{ end }}
        {{- if .Values.tlsSecret }}
        - name: tls-secret
          secret:
            secretName: {{ .Values.tlsSecret }}
        {{- end }}
        {{- if .Values.db.sslMode }}
        - name: database-cert
          {{- $resourceType := (required "A valid .Values.db.resourceType entry required!" .Values.db.resourceType | trim)}}
          {{- if eq $resourceType "configMap"}}
          configMap:
            name: {{ required  "A valid Values.db.certResourceName entry is required!" .Values.db.certResourceName }}
          {{- else }}
          secret:
            secretName: {{ required  "A valid Values.db.certResourceName entry is required!" .Values.db.certResourceName }}
          {{- end }}
        {{- end }}
        # Additional volumes for ca.crt or ca boundle injection
        {{- if .Values.externalCaCertSecretName }}
        - name: usr-local-share-ca-certificates
          secret:
            defaultMode: 420
            secretName: {{ .Values.externalCaCertSecretName }}
        - name: etc-ssl-certs
          emptyDir: {}
        {{- end }}
        # end - Additional volumes

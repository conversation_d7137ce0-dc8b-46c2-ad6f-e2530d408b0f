For the testing install:

helm upgrade determined-27-testing  --install -n determined --create-namespace -f values-augment-testing.yaml .

Create the PVCs:

kubectl apply -f pvc_config.yaml

Create the managed cert

kubectl apply -f managed_cert.yaml

Create the ingress

kubectl apply -f ingress_config.yaml

Update the service healthcheck path to include .../det/ so the LB shows the system as up.
Update the service-account to have permissions to pull from the registry

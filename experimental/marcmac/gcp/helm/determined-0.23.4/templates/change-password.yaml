{{- if .Values.defaultPassword }}
apiVersion: batch/v1
kind: Job
metadata:
  name: determined-pw-change-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-pw-change
    release: {{ .Release.Name }}
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-delete-policy": hook-succeeded, hook-failed
spec:
  template:
    metadata:
      name: determined-pw-change
      labels:
        app: determined-pw-change
        release: {{ .Release.Name }}
    spec:
      serviceAccount: determined-master-{{ .Release.Name }}
      restartPolicy: OnFailure

      containers:
      - name: change-password
        image: {{ .Values.defaultImages.cpuImage | quote }}
        imagePullPolicy: "Always"
        command: ["/bin/bash"]
        args:
          - "-c"
          - >-
            echo -e {{ .Files.Get "scripts/k8s-password-change.py" | quote }} > /tmp/change-pw.py &&
            KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token) &&
            python3 /tmp/change-pw.py
            {{ .Release.Namespace | quote }} \
            {{ .Release.Name | quote }} \
            {{ .Values.masterPort | quote }} \
            {{ .Values.useNodePortForMaster | quote }} \
            $KUBERNETES_SERVICE_HOST \
            $KUBERNETES_PORT_443_TCP_PORT \
            $KUBE_TOKEN \
            {{ .Values.defaultPassword | default ""}}
{{- end }}

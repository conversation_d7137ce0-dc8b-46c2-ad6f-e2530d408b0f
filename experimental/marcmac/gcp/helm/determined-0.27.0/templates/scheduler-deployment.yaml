{{- if .Values.defaultScheduler}}
{{- $schedulerType := .Values.defaultScheduler | trim}}
{{- if or (eq $schedulerType "coscheduler") (eq $schedulerType "preemption")}}
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    component: scheduler
    tier: control-plane
    release: {{ $schedulerType }}-{{ .Release.Name }}
  name: {{ $schedulerType }}
  namespace: kube-system
spec:
  selector:
    matchLabels:
      component: scheduler
      tier: control-plane
  replicas: 1
  template:
    metadata:
      labels:
        component: scheduler
        tier: control-plane
    spec:
      serviceAccountName: {{ $schedulerType }}
      containers:
      - command:
        - kube-scheduler
        - -v=3
        - --leader-elect=false
        - --scheduler-name=coscheduler
        - --config=/etc/config/config.yaml
        name: {{ $schedulerType }}
        {{- if eq $schedulerType "preemption"}}
        image: "{{ .Values.defaultImages.kubeSchedulerPreemption }}"
        {{- else }}
        image: "{{ .Values.defaultImages.kubeScheduler }}"
        {{- end}}
        imagePullPolicy: "Always"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 10251
          initialDelaySeconds: 15
        readinessProbe:
          httpGet:
            path: /healthz
            port: 10251
        resources:
          requests:
            cpu: '0.1'
        securityContext:
          privileged: false
        volumeMounts:
          - name: scheduler-config-volume
            mountPath: /etc/config
      hostNetwork: false
      hostPID: false
      volumes:
        - name: scheduler-config-volume
          configMap:
            name: scheduler-config
{{- end }}
{{- end }}

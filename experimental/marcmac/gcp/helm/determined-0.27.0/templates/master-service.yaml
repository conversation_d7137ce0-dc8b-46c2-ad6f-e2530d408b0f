apiVersion: v1
kind: Service
metadata:
  name: determined-master-service-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-master-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  ports:
  - port: {{ required "A valid Values.masterPort entry required!" .Values.masterPort }}
    targetPort: {{- include "determined.masterPort" . | indent 1 }}
    protocol: TCP
  type: {{ if (.Values.useNodePortForMaster | default false) }}NodePort{{ else }}LoadBalancer{{ end }}
  selector:
    app: determined-master-{{ .Release.Name }}

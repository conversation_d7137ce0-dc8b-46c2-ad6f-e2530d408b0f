# The image registry to use. Defaults to the determinedai repository in DockerHub.
imageRegistry: determinedai

# Install Determined enterprise edition.
enterpriseEdition: false

# Should be configured if using the master image in the Determined enterprise edition
# or private registry.
imagePullSecretName:

# masterPort configures the port at which the Determined master listens for connections on.
masterPort: 8080

# When useNodePortForMaster is set to false (default), a LoadBalancer service is deployed to make
# the Determined master reachable from outside the cluster. When useNodePortForMaster is set to
# true, the master will instead be exposed behind a NodePort service. When using a NodePort service
# users will typically have to configure an Ingress to make the Determined master reachable from
# outside the cluster. NodePort service is recommended when configuring TLS termination in a
# load-balancer.
useNodePortForMaster: true

# Enable route support for Openshift by setting enabled to true. Configure tls termination (i.e edge) if needed.
# openshiftRoute:
  # enabled:
  # host:
  # termination:

# tlsSecret enables TLS encryption for all communication made to the Determined master (TLS
# termination is performed in the Determined master). This includes communication between the
# Determined master and the task containers it launches, but does not include communication between
# the task containers (distributed training). The specified Secret of type tls must already exist in
# the same namespace in which Determined is being installed.
# tlsSecret:

# security:
  # defaultTask sets the user and group that tasks will run as. For convenience, the default Determined
  # environments contain an unprivileged user named det-nobody, which does have a writable HOME
  # directory. The det-nobody user is a suitable default user when using the default Determined
  # environment images and when running containers as root is not desired.
  # defaultTask:
  #   user: det-nobody
  #   uid: 65533
  #   group: det-nobody
  #   gid: 65533
  # authz option (EE-only) sets the authorization mode.
  # authz:
  #   type: rbac

# oidc (EE-only) enables OpenID Connect Integration, which is only available if enterpriseEdition
# is true. It allows users to use single sign-on with their organization’s identity provider.
# clientSecretKey is the key of the secret contained in the secret.
# oidc:
#   enabled:
#   provider:
#   idpRecipientUrl:
#   idpSsoUrl:
#   clientId:
#   clientSecretKey:
#   clientSecretName:
#   authenticationClaim:
#   scimAuthenticationAttribute:

# scim (EE-only) enables System for Cross-domain Identity Management (SCIM) integration, which is
# only available if enterpriseEdition is true. It allows administrators to easily and securely
# provision users and groups through their standard identity provider (IdP).
# scim:
#   enabled: true
#   auth:
#     type: basic
#     username: determined
#     password: password

# db sets the configurations for the database.
db:
  # To deploy your own Postgres DB, provide a hostAddress. If hostAddress is provided, Determined
  # will skip deploying a Postgres DB.
  # hostAddress:

  # Required parameters, whether you are using your own DB or a Determined DB.
  name: determined
  user: postgres
  password: postgres
  port: 5432

  # Only used for Determined DB deployment. Configures the size of the PersistentVolumeClaim for the
  # Determined deployed database, as well as the CPU and memory requirements. Should be adjusted for
  # scale.
  storageSize: 300Gi
  cpuRequest: 8
  memRequest: 32Gi

  # useNodePortForDB configures whether ClusterIP or NodePort service type is used for the
  # Determined deployed DB. By default ClusterIP is used.
  useNodePortForDB: false

  # storageClassName configures the StorageClass used by the PersistentVolumeClaim for the
  # Determined deployed database. This can be left blank if a default storage class is specified in
  # the cluster. If dynamic provisioning of PersistentVolumes is disabled, users must manually
  # create a PersistentVolume that will match the PersistentVolumeClaim.
  # storageClassName:

  # ssl_mode and ssl_root_cert configure the TLS connection to the database. Users must first
  # create a kubernetes secret or configMap containing their certificate and specify its name in
  # certResourceName. For sslRootCert, specify the name of the file only (not path).
  # sslMode: verify-ca
  # sslRootCert: <cert_name>
  # resourceType: <secret/configMap>
  # certResourceName: <secret/configMap name>


# checkpointStorage controls where checkpoints are stored. Supported types include `shared_fs`,
# `gcs`, and `s3`.
checkpointStorage:
  # Applicable to all checkpointStorage types.
  saveExperimentBest: 0
  saveTrialBest: 1
  saveTrialLatest: 1


  # Comment out if not using `shared_fs`. Users are strongly discouraged from using `shared_fs` for
  # storage beyond initial testing as most Kubernetes cluster nodes do not have a shared file
  # system.
  # type: shared_fs
  # hostPath: /checkpoints

  # For storing in GCS.
  type: gcs
  bucket: determined-checkpoint-storage
  # prefix: <prefix>

  # For storing in S3.
  # type: s3
  # bucket: <bucket_name>
  # accessKey: <access_key>
  # secretKey: <secret_key>
  # endpointUrl: <endpoint_url>
  # prefix: <prefix>

  # For storing in Azure Blob Storage with a connection string.
  # Do NOT use if already using Azure Blob Storage with account URL
  # type: azure
  # container: <container_name>
  # connection_string: <connection_string>

  # For storing in Azure Blob Storage with an account URL.
  # Do NOT use if already using Azure Blob Storage with connection string.
  # The `credential` field is optional.
  # type: azure
  # container: <container_name>
  # account_url: <account_url>
  # credential: <credential>

# This is the number of GPUs there are per machine. Determined uses this information when scheduling
# multi-GPU tasks. Each multi-GPU (distributed training) task will be scheduled as a set of
# `slotsPerTask / maxSlotsPerPod` separate pods, with each pod assigned up to `maxSlotsPerPod` GPUs.
# Distributed tasks with sizes that are not divisible by `maxSlotsPerPod` are never scheduled. If
# you have a cluster of different size nodes (e.g., 4 and 8 GPUs per node), set `maxSlotsPerPod` to
# the greatest common divisor of all the sizes (4, in that case).
maxSlotsPerPod: 8

## For CPU-only clusters, use `slotType: cpu`, and make sure to set `slotResourceRequest` below.
# slotType: cpu
# slotResourceRequests:
  ## Number of cpu units requested for compute slots. Note: since kubernetes may schedule some
  ## system tasks on the nodes which take up some resources, 8-core node may not always fit
  ## a `cpu: 8` task container.
  # cpu: 7

# Memory and CPU requirements for the master instance. Should be adjusted for scale.
masterCpuRequest: 8
masterMemRequest: 64Gi

## Configure the task container defaults. Tasks include trials, commands, TensorBoards, notebooks,
## and shells. For all task containers, shm_size_bytes and network_mode are configurable. For
## trials, the network interface used by distributed (multi-machine) training is configurable.
taskContainerDefaults:
  networkMode: bridge
  # dtrainNetworkInterface: <network interface name>
  # forcePullImage: <true or false>

  # Configure a default pod spec for all GPU tasks (experiments, notebooks, commands) and CPU tasks
  # (CPU notebooks, TensorBoards, zero-slot commands). If a pod spec is defined for an individual
  # task, that pod spec will replace the default one that is defined here. See
  # https://docs:determined.ai/latest/topic-guides/custom-pod-specs.html for more details.
  cpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #       - matchExpressions:
      #         - key: topology.kubernetes.io/region
      #           operator: In
      #           values:
      #           - LAS1
      securityContext:
        fsGroup: 1000
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: '32'
            memory: 64Gi
          requests:
            cpu: '32'
            memory: 64Gi
        volumeMounts:
        - mountPath: /mnt/efs/augment/checkpoints
          name: aug-checkpoints
        - mountPath: /mnt/efs/augment/configs
          name: aug-configs
        - mountPath: /mnt/efs/augment/data
          name: aug-data
        - mountPath: /mnt/efs/augment/eval
          name: aug-eval
        - mountPath: /mnt/efs/augment/external_models
          name: aug-external-models
        - mountPath: /mnt/efs/augment/ftm_checkpoints
          name: aug-ftm-checkpoints
        - mountPath: /mnt/efs/augment/lib
          name: aug-lib
        - mountPath: /mnt/efs/augment/mem
          name: aug-mem
        - mountPath: /mnt/efs/augment/python_env
          name: aug-python-env
        - mountPath: /mnt/efs/augment/user
          name: aug-user
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      volumes:
      - name: aug-checkpoints
        persistentVolumeClaim:
          claimName: aug-checkpoints
      - name: aug-configs
        persistentVolumeClaim:
          claimName: aug-configs
      - name: aug-data
        persistentVolumeClaim:
          claimName: aug-data
      - name: aug-eval
        persistentVolumeClaim:
          claimName: aug-eval
      - name: aug-external-models
        persistentVolumeClaim:
          claimName: aug-external-models
      - name: aug-ftm-checkpoints
        persistentVolumeClaim:
          claimName: aug-ftm-checkpoints
      - name: aug-lib
        persistentVolumeClaim:
          claimName: aug-lib
      - name: aug-mem
        persistentVolumeClaim:
          claimName: aug-mem
      - name: aug-python-env
        persistentVolumeClaim:
          claimName: aug-python-env
      - name: aug-user
        persistentVolumeClaim:
          claimName: aug-user
    status: {}
  gpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #       - matchExpressions:
      #         - key: topology.kubernetes.io/region
      #           operator: In
      #           values:
      #           - LAS1
      #         - key: gpu.nvidia.com/class
      #           operator: In
      #           values:
      #           - RTX_A5000
      securityContext:
        fsGroup: 1000
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: '4'
            memory: 18Gi
          requests:
            cpu: '4'
            memory: 18Gi
        volumeMounts:
        - mountPath: /mnt/efs/augment/checkpoints
          name: aug-checkpoints
        - mountPath: /mnt/efs/augment/configs
          name: aug-configs
        - mountPath: /mnt/efs/augment/data
          name: aug-data
        - mountPath: /mnt/efs/augment/eval
          name: aug-eval
        - mountPath: /mnt/efs/augment/external_models
          name: aug-external-models
        - mountPath: /mnt/efs/augment/ftm_checkpoints
          name: aug-ftm-checkpoints
        - mountPath: /mnt/efs/augment/lib
          name: aug-lib
        - mountPath: /mnt/efs/augment/mem
          name: aug-mem
        - mountPath: /mnt/efs/augment/python_env
          name: aug-python-env
        - mountPath: /mnt/efs/augment/user
          name: aug-user
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      volumes:
      - name: aug-checkpoints
        persistentVolumeClaim:
          claimName: aug-checkpoints
      - name: aug-configs
        persistentVolumeClaim:
          claimName: aug-configs
      - name: aug-data
        persistentVolumeClaim:
          claimName: aug-data
      - name: aug-eval
        persistentVolumeClaim:
          claimName: aug-eval
      - name: aug-external-models
        persistentVolumeClaim:
          claimName: aug-external-models
      - name: aug-ftm-checkpoints
        persistentVolumeClaim:
          claimName: aug-ftm-checkpoints
      - name: aug-lib
        persistentVolumeClaim:
          claimName: aug-lib
      - name: aug-mem
        persistentVolumeClaim:
          claimName: aug-mem
      - name: aug-python-env
        persistentVolumeClaim:
          claimName: aug-python-env
      - name: aug-user
        persistentVolumeClaim:
          claimName: aug-user
    status: {}

  # Configure default Docker images for all GPU tasks (experiments, notebooks, commands) and
  # CPU tasks (CPU notebooks, TensorBoards, zero-slot commands). If a Docker image is defined
  # for an individual task, that image will replace the default one that is defined here.
  # If specifying a default image, both GPU and CPU default images must be defined.
  cpuImage: determinedai/environments:py-3.8-pytorch-1.10-lightning-1.5-tf-2.8-cpu-3e933ea
  gpuImage: determinedai/environments:cuda-11.3-pytorch-1.10-lightning-1.5-tf-2.8-gpu-3e933ea

## Configure whether we collect anonymous information about the usage of Determined.
telemetry:
  enabled: true

## Configure Prometheus endpoints for monitoring.
# observability:
#   enable_prometheus: true

## A user-friendly name to identify this cluster by.
# clusterName: Dev

## Specifies the duration in seconds before idle
# TensorBoard instances are automatically terminated.
# A TensorBoard instance is considered to be idle if
# it does not receive any HTTP traffic. The default timeout is 300 seconds (5 minutes).
# tensorboardTimeout: 300

# defaultPassword sets the password for the admin and determined user accounts.
defaultPassword: AugmentC0de!

## Configure how trial logs are stored.
# logging:
  ## The backend to use. Can be `default` to send logs to the master to store in the PostgreSQL
  ## database or `elastic` to store logs in an Elasticsearch cluster (without going through the
  ## master).
  # type: default

  ## The remaining options should be provided only for the `elastic` backend.

  ## The host and port to use to connect to the Elasticsearch cluster.
  # host: <host>
  # port: <port>

  ## Authentication and TLS options for making the connection to Elasticsearch.
  # security:
    # username: <username>
    # password: <password>
    # tls:
      # enabled: true
      # skipVerify: false

      ## The name to use when verifying the certificate, if different from the name used to connect.
      # certificateName: <name>

      ## This value must contain the contents of the certificate file, not a path. It may be set
      ## directly or using `helm install --set-file logging.security.tls.certificate=<path>`.
      # certificate: <certificate contents>

## Configure the default Determined scheduler
## Currently supports "coscheduler" for gang scheduling and "preemption" for priority based
## scheduling with preemption
# defaultScheduler: preemption

## Configure settings about how Determined launches the Fluent Bit sidecar.
# fluent:
#   image: fluent/fluent-bit:1.6
#   uid: 0
#   gid: 0

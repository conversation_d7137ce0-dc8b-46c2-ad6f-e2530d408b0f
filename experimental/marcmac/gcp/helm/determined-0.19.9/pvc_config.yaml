#
# GKE storage classes
# standard-rwx  1-64 TiB 	HDD
# premium-rwx   2.5-64 TiB SSD
# enterprise-rwx   1-10 TiB SSD
# enterprise-multishare-rwx 1-10 TiB SSD
#
# We create a separate share for every top-level item in /mnt/efs/augment
#  checkpoints
#  configs
#  data
#  eval
#  eval_results     -- skipping for now, nearly empty
#  external_models
#  ftm_checkpoints
#  jobs             -- skipping for now, nearly empty
#  lib
#  mem
#  python_env
#  user

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-checkpoints
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 4000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-configs
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-data
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 10000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-eval
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-external-models
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-ftm-checkpoints
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-lib
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-mem
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-python-env
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: enterprise-rwx
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-user
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 4000Gi
  storageClassName: enterprise-rwx
---

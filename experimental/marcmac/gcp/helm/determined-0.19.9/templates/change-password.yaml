{{- if .Values.defaultPassword }}
apiVersion: batch/v1
kind: Job
metadata:
  name: determined-pw-change-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-pw-change
    release: {{ .Release.Name }}
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-delete-policy": hook-succeeded, hook-failed
spec:
  template:
    metadata:
      name: determined-pw-change
      labels:
        app: determined-pw-change
        release: {{ .Release.Name }}
    spec:
      serviceAccount: determined-master-{{ .Release.Name }}
      restartPolicy: OnFailure
      containers:
      - name: change-password
        image: "{{ .Values.imageRegistry }}/utility:py-3.7-pw-changer"
        imagePullPolicy: "Always"
        command: ["/bin/bash"]
        args:
          - "-c"
          - >-
            echo -e {{ .Files.Get "scripts/k8s-password-change.py" | quote }} > /tmp/change-pw.py &&
            python3 /tmp/change-pw.py
            {{ .Release.Namespace | quote }} \
            {{ .Release.Name | quote }} \
            {{ .Values.masterPort | quote }} \
            {{ .Values.useNodePortForMaster | quote }} \
            {{ .Values.defaultPassword | default ""}}
{{- end }}

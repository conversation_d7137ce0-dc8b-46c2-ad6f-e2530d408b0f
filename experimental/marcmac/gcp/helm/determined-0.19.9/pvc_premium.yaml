
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-user
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 4000Gi
  storageClassName: premium-rwx
---


apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-data
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 10000Gi
  storageClassName: premium-rwx
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-checkpoints
  namespace: determined
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 4000Gi
  storageClassName: premium-rwx

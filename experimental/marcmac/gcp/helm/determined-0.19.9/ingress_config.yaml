apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: determined-cert
spec:
  domains:
    - determined-gcp.eng.augmentcode.com
---
# This ingress config will automagically create the load balancer required in GKE
# https://cloud.google.com/kubernetes-engine/docs/concepts/ingress
#
#
# TODO health check fix
# if present in the serving Pod's spec:
# containers[].readinessProbe.httpGet.path
#
# Also, manually updated the LB to send all traffic to this backend.
#
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: determined-ingress
  namespace: determined
  annotations:
    # If the class annotation is not specified it defaults to "gce".
    kubernetes.io/ingress.class: "gce"
    # **************
    kubernetes.io/ingress.global-static-ip-name: "determined-master-address"
    networking.gke.io/managed-certificates: "determined-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  rules:
  - host: "determined-gcp.eng.augmentcode.com"
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: determined-master-service-determined
            port:
              number: 8080

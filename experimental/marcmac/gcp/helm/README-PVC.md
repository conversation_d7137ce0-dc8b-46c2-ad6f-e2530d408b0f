# DEPLOY
Creating the PVC
    jsonnet -y --ext-str cluster=<cluster> pvc_config.jsonnet | kubectl apply -f -

Create the Ingress:
  jsonnet --ext-str cluster=<cluster> \
    ingress_config.jsonnet | kubectl apply -f -

Update the determined-sa:
   kubecfg --context=gcp-us1 show determined-sa.jsonnet --tla-str cluster=gcp-us1
   kubecfg --context=gcp-us1 update determined-sa.jsonnet --tla-str cluster=gcp-us1

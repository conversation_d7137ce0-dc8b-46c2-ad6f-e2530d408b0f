apiVersion: v1
kind: Service
metadata:
  name: determined-master-service-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-master-{{ .Release.Name }}
    determined.ai/master-service: "true"
    release: {{ .Release.Name }}
  {{- if (.Values.masterService).annotations }}
  annotations: {{- toYaml .Values.masterService.annotations | nindent 4 }}
  {{- end }}
spec:
  ports:
  - port: {{ required "A valid Values.masterPort entry required!" .Values.masterPort }}
    targetPort: {{- include "determined.masterPort" . | indent 1 }}
    protocol: TCP
  {{- if .Values.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}

{{- if ((.Values.openshiftRoute).enabled | default false) }}
  type: ClusterIP
{{- else }}
  type: {{ if (.Values.useNodePortForMaster | default false) }}NodePort{{ else }}LoadBalancer{{ end }}
{{- end }}
  selector:
    app: determined-master-{{ .Release.Name }}

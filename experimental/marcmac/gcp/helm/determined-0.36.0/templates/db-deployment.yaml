{{- if .Values.db.hostAddress }}
{{ else }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: determined-db-deployment-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-db-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: determined-db-{{ .Release.Name }}
  template:
    metadata:
      labels:
        app: determined-db-{{ .Release.Name }}
        determined-system: db
    spec:
      priorityClassName: determined-system-priority
      containers:
      - name: determined-db-{{ .Release.Name }}
        image: {{ .Values.defaultImages.postgreSQL | quote }}
        imagePullPolicy: "Always"
        resources:
          requests:
            {{- if .Values.db.cpuRequest }}
            cpu: {{ .Values.db.cpuRequest | quote }}
            {{- end }}
            {{- if .Values.db.memRequest }}
            memory: {{ .Values.db.memRequest | quote }}
            {{- end}}

          {{- if or .Values.db.cpuLimit .Values.db.memLimit }}
          limits:
            {{- if .Values.db.cpuLimit }}
            cpu: {{ .Values.db.cpuLimit | quote }}
            {{- end }}
            {{- if .Values.db.memLimit }}
            memory: {{ .Values.db.memLimit | quote }}
            {{- end}}
          {{- end}}
        env:
          - name: POSTGRES_DB
            value: {{ .Values.db.name | quote }}
          - name: POSTGRES_USER
            value: {{ .Values.db.user | quote }}
          - name: POSTGRES_PASSWORD
            value: {{ .Values.db.password | quote }}
          - name: PGPORT
            value: {{ .Values.db.port | quote }}
          - name: PGDATA
            value: /var/lib/postgresql/data/pgdata
        args: ["--max_connections=96", "--shared_buffers=512MB"]
      {{- if not .Values.db.disablePVC }}
        volumeMounts:
          - mountPath: "/var/lib/postgresql/data"
            name: determined-db-storage
      volumes:
        - name: determined-db-storage
          persistentVolumeClaim:
            claimName: determined-db-pvc-{{ .Release.Name }}
      {{ end }}
{{ end}}

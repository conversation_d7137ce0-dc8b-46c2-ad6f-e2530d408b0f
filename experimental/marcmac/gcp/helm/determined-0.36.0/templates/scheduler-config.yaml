{{- if .Values.defaultScheduler}}
{{- if eq (.Values.defaultScheduler | trim ) "coscheduler"}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: scheduler-config
  namespace: kube-system
data:
  config.yaml: |
    apiVersion: kubescheduler.config.k8s.io/v1alpha2
    kind: KubeSchedulerConfiguration
    leaderElection:
      leaderElect: false
    profiles:
    - schedulerName: {{ .Values.defaultScheduler }}
      plugins:
        queueSort:
          enabled:
            - name: Coscheduling
          disabled:
            - name: "*"
        permit:
          enabled:
            - name: Coscheduling
        unreserve:
          enabled:
            - name: Coscheduling
        score:
          enabled:
            - name: Coscheduling
    # optional plugin configs
      pluginConfig:
      - name: Coscheduling
        args:
          permitWaitingTimeSeconds: 1
          podGroupGCIntervalSeconds: 10
          podGroupExpirationTimeSeconds: 30
{{- end }}
{{- end }}

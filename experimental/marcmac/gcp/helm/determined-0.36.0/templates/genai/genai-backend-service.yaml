{{- if .Values.genai }}
{{- if .Values.genai.version }}
apiVersion: v1
kind: Service
metadata:
  name: genai-backend-service-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: genai-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  ports:
  - port: {{ required "A valid Values.genai.port entry required!" .Values.genai.port }}
    targetPort: {{ .Values.genai.port }}
    protocol: TCP
  type: ClusterIP
  selector:
    app: genai-{{ .Release.Name }}
{{ end }}
{{ end }}

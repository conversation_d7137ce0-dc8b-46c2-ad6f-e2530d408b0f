{{- if .Values.genai }}
{{- if .Values.genai.version }}
apiVersion: v1
kind: Service
metadata:
  name: genai-queue-service-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: genai-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  ports:
  - port: {{ required "A valid Values.genai.messageQueuePort entry required!" .Values.genai.messageQueuePort }}
    targetPort: {{ .Values.genai.messageQueuePort }}
    protocol: TCP
  type: ClusterIP
  selector:
    app: genai-{{ .Release.Name }}
{{ end }}
{{ end }}

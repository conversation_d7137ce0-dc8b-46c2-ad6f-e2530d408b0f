{{- if .Values.genai }}
{{- if .Values.genai.version }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: genai-deployment-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: genai-{{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: genai-{{ .Release.Name }}
  template:
    metadata:
      labels:
        app: genai-{{ .Release.Name }}
        determined-system: master
    spec:
      priorityClassName: determined-system-priority
      serviceAccount: genai-{{ .Release.Name }}
      {{- if .Values.genai.agentGroupID }}
      {{ $gid := .Values.genai.agentGroupID }}
      securityContext:
        runAsUser: {{ $gid }}
        runAsGroup: {{ $gid }}
        fsGroup: {{ $gid }}
        fsGroupChangePolicy: "OnRootMismatch"
      {{- end }}
      containers:
      - name: genai-{{ .Release.Name }}
        {{ $tag := (required "A valid .Values.genai.version entry required!" .Values.genai.version) }}
        {{- /* genai.version is used for CI to override the appVersion. */ -}}
        image: {{ .Values.imageRegistry }}/genai:{{ $tag }}
        imagePullPolicy: "Always"
        env:
          - name: DET_MASTER
            value: {{ include "genai.detMasterScheme" . }}://determined-master-service-{{ .Release.Name }}.{{ .Release.Namespace }}:{{ .Values.masterPort }}
          - name: DB_NAME
            value: lore
          - name: DB_USER
            value: {{ .Values.db.user | quote }}
          - name: DB_PASSWORD
            value: {{ .Values.db.password | quote }}
          - name: DB_PORT
            value: {{ .Values.db.port | quote }}
          - name: DB_HOST
            value: {{ include "determined.dbHost" . }}
          {{- if .Values.db.sslMode }}
          - name: DB_SSL_MODE
            value: {{ .Values.db.sslMode }}
          {{- $rootCert := (required "A valid .Values.db.sslRootCert entry required!" .Values.db.sslRootCert )}}
          - name: DB_SSL_ROOT_CERT
            value: {{ include "determined.secretPath" . }}{{ $rootCert }}
          {{- end }}
          - name: LORE_DOCKER_TAG_SUFFIX
            value: {{ $tag | quote }}
          {{- if .Values.genai.sharedFSHostPath }}
          - name: K8S_SHARED_HOST_PATH
            value: {{ .Values.genai.sharedFSHostPath }}
          {{- else }}
          - name: K8S_SHARED_PVC_NAME
            value: {{ include "genai.PVCName" . }}
          {{- end }}
          - name: SHARED_FS_ROOT_PATH
            value: {{ include "genai.sharedFSMountPath" . }}
          - name: RESOURCE_POOL_EXTRA_METADATA_PATH
            value: /run/determined/workdir/rp_config/resource_pool_metadata.yaml
          - name: MESSAGE_QUEUE_HOST
            value: genai-queue-service-{{ .Release.Name }}.{{ .Release.Namespace }}
          - name: MESSAGE_QUEUE_PORT
            value: {{ .Values.genai.messageQueuePort | quote }}
          - name: GENAI_HOST
            value: genai-backend-service-{{ .Release.Name }}.{{ .Release.Namespace }}
          - name: GENAI_HOST_PORT
            value: {{ .Values.genai.port | quote }}
          {{- if .Values.genai.imagePullSecretName}}
          - name: GENAI_IMAGE_PULL_SECRET_NAME
            value: {{ .Values.genai.imagePullSecretName }}
          {{- end}}
        volumeMounts:
          {{- if .Values.genai.sharedFSHostPath }}
          - name: genai-shared-host-path
            mountPath: {{ include "genai.sharedFSMountPath" . }}
          {{- else }}
          - name: genai-pvc-storage
            mountPath: {{ include "genai.sharedFSMountPath" . }}
            readOnly: false
          {{- end }}
          - name: genai-resource-pool-metadata
            mountPath: /run/determined/workdir/rp_config
            readOnly: true
          {{- include "determined.dbCertVolumeMount" . | nindent 10 }}
        resources:
          requests:
            {{- if .Values.genai.cpuRequest }}
            cpu: {{ .Values.genai.cpuRequest  | quote }}
            {{- end }}
            {{- if .Values.genai.memRequest }}
            memory: {{ .Values.genai.memRequest  | quote }}
            {{- end}}
          {{- if or .Values.genai.cpuLimit .Values.genai.memLimit }}
          limits:
            {{- if .Values.genai.cpuLimit }}
            cpu: {{ .Values.genai.cpuLimit  | quote }}
            {{- end }}
            {{- if .Values.genai.memLimit }}
            memory: {{ .Values.genai.memLimit  | quote }}
            {{- end}}
          {{- end}}
      {{- if .Values.genai.imagePullSecretName}}
      imagePullSecrets:
        - name: {{ .Values.genai.imagePullSecretName }}
      {{- end}}
      volumes:
        {{- if .Values.genai.sharedFSHostPath }}
        - name: genai-shared-host-path
          hostPath:
            path: {{ .Values.genai.sharedFSHostPath }}
            type: Directory
        {{- else }}
        - name: genai-pvc-storage
          persistentVolumeClaim:
            claimName: {{ include "genai.PVCName" . }}
        {{- end }}
        - name: genai-resource-pool-metadata
          configMap:
            name: genai-resource-pool-metadata-{{ .Release.Name }}
        {{- include "determined.dbCertVolume" . | nindent 8 }}
{{ end }}
{{ end }}

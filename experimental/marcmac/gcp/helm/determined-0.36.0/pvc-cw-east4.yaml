apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-checkpoints
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 2500Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-ci-testing
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 2500Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-configs
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-data
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 2500Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-eval
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-external-models
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-ftm-checkpoints
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-lib
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1000Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-spark-data
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 2500Gi
  storageClassName: shared-vast
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aug-user
  namespace: cw-east4
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 2500Gi
  storageClassName: shared-vast
---

checkpointStorage:
  bucket: determined-checkpoint-storage
  saveExperimentBest: 0
  saveTrialBest: 1
  saveTrialLatest: 1
  type: gcs
db:
  cpuRequest: 8
  memRequest: 32Gi
  name: determined
  password: postgres
  port: 5432
  storageSize: 300Gi
  useNodePortForDB: false
  user: postgres
defaultImages:
  cpuImage: determinedai/environments:py-3.9-pytorch-1.12-tf-2.11-cpu-622d512
  gpuImage: determinedai/environments:cuda-11.3-pytorch-1.12-tf-2.11-gpu-622d512
  kubeScheduler: k8s.gcr.io/scheduler-plugins/kube-scheduler:v0.18.9
  kubeSchedulerPreemption: determinedai/kube-scheduler:0.17.0
  postgreSQL: postgres:10.14
enterpriseEdition: false
imagePullSecretName: null
imageRegistry: determinedai
logColor: true
logLevel: info
masterCpuRequest: 16
masterMemRequest: 128Gi
masterPort: 8080
maxSlotsPerPod: 8
resourcePools:
- pool_name: default
taskContainerDefaults:
  cpuImage: determinedai/environments:py-3.9-pytorch-1.12-tf-2.11-cpu-622d512
  cpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: "32"
            memory: 64Gi
          requests:
            cpu: "32"
            memory: 64Gi
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      securityContext:
        fsGroup: 1000
    status: {}
  gpuImage: determinedai/environments:cuda-11.3-pytorch-1.12-tf-2.11-gpu-622d512
  gpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: "4"
            memory: 18Gi
          requests:
            cpu: "4"
            memory: 18Gi
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      securityContext:
        fsGroup: 1000
    status: {}
  networkMode: host
telemetry:
  enabled: true
useNodePortForMaster: true

checkpointStorage:
  bucket: determined-checkpoint-storage
  saveExperimentBest: 0
  saveTrialBest: 1
  saveTrialLatest: 1
  type: gcs
db:
  cpuRequest: 8
  memRequest: 32Gi
  name: determined
  password: postgres  # pragma: allowlist secret
  port: 5432
  storageSize: 300Gi
  useNodePortForDB: false
  user: postgres
defaultPassword:
enterpriseEdition: false
imagePullSecretName: null
imageRegistry: determinedai
masterCpuRequest: 8
masterMemRequest: 64Gi
masterPort: 8080
maxSlotsPerPod: 8
taskContainerDefaults:
  cpuImage: determinedai/environments:py-3.8-pytorch-1.10-lightning-1.5-tf-2.8-cpu-3e933ea
  cpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: "32"
            memory: 64Gi
          requests:
            cpu: "32"
            memory: 64Gi
        volumeMounts:
        - mountPath: /mnt/efs/augment/checkpoints
          name: aug-checkpoints
        - mountPath: /mnt/efs/augment/configs
          name: aug-configs
        - mountPath: /mnt/efs/augment/data
          name: aug-data
        - mountPath: /mnt/efs/augment/eval
          name: aug-eval
        - mountPath: /mnt/efs/augment/external_models
          name: aug-external-models
        - mountPath: /mnt/efs/augment/ftm_checkpoints
          name: aug-ftm-checkpoints
        - mountPath: /mnt/efs/augment/lib
          name: aug-lib
        - mountPath: /mnt/efs/augment/mem
          name: aug-mem
        - mountPath: /mnt/efs/augment/python_env
          name: aug-python-env
        - mountPath: /mnt/efs/augment/user
          name: aug-user
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      securityContext:
        fsGroup: 1000
      volumes:
      - name: aug-checkpoints
        persistentVolumeClaim:
          claimName: aug-checkpoints
      - name: aug-configs
        persistentVolumeClaim:
          claimName: aug-configs
      - name: aug-data
        persistentVolumeClaim:
          claimName: aug-data
      - name: aug-eval
        persistentVolumeClaim:
          claimName: aug-eval
      - name: aug-external-models
        persistentVolumeClaim:
          claimName: aug-external-models
      - name: aug-ftm-checkpoints
        persistentVolumeClaim:
          claimName: aug-ftm-checkpoints
      - name: aug-lib
        persistentVolumeClaim:
          claimName: aug-lib
      - name: aug-mem
        persistentVolumeClaim:
          claimName: aug-mem
      - name: aug-python-env
        persistentVolumeClaim:
          claimName: aug-python-env
      - name: aug-user
        persistentVolumeClaim:
          claimName: aug-user
    status: {}
  gpuImage: determinedai/environments:cuda-11.3-pytorch-1.10-lightning-1.5-tf-2.8-gpu-3e933ea
  gpuPodSpec:
    metadata:
      creationTimestamp: null
    spec:
      containers:
      - name: determined-container
        resources:
          limits:
            cpu: "4"
            memory: 18Gi
          requests:
            cpu: "4"
            memory: 18Gi
        volumeMounts:
        - mountPath: /mnt/efs/augment/checkpoints
          name: aug-checkpoints
        - mountPath: /mnt/efs/augment/configs
          name: aug-configs
        - mountPath: /mnt/efs/augment/data
          name: aug-data
        - mountPath: /mnt/efs/augment/eval
          name: aug-eval
        - mountPath: /mnt/efs/augment/external_models
          name: aug-external-models
        - mountPath: /mnt/efs/augment/ftm_checkpoints
          name: aug-ftm-checkpoints
        - mountPath: /mnt/efs/augment/lib
          name: aug-lib
        - mountPath: /mnt/efs/augment/mem
          name: aug-mem
        - mountPath: /mnt/efs/augment/python_env
          name: aug-python-env
        - mountPath: /mnt/efs/augment/user
          name: aug-user
      enableServiceLinks: false
      priorityClassName: determined-system-priority
      securityContext:
        fsGroup: 1000
      volumes:
      - name: aug-checkpoints
        persistentVolumeClaim:
          claimName: aug-checkpoints
      - name: aug-configs
        persistentVolumeClaim:
          claimName: aug-configs
      - name: aug-data
        persistentVolumeClaim:
          claimName: aug-data
      - name: aug-eval
        persistentVolumeClaim:
          claimName: aug-eval
      - name: aug-external-models
        persistentVolumeClaim:
          claimName: aug-external-models
      - name: aug-ftm-checkpoints
        persistentVolumeClaim:
          claimName: aug-ftm-checkpoints
      - name: aug-lib
        persistentVolumeClaim:
          claimName: aug-lib
      - name: aug-mem
        persistentVolumeClaim:
          claimName: aug-mem
      - name: aug-python-env
        persistentVolumeClaim:
          claimName: aug-python-env
      - name: aug-user
        persistentVolumeClaim:
          claimName: aug-user
    status: {}
  networkMode: bridge
telemetry:
  enabled: true
useNodePortForMaster: true

#
# GKE storage classes
# standard-rwx  1-64 TiB 	HDD
# premium-rwx   2.5-64 TiB SSD
# enterprise-rwx   1-10 TiB SSD
# enterprise-multishare-rwx 1-10 TiB SSD
#
# BECAUSE WE ARE USING VPC, we need custom storage classes
# https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/filestore-csi-driver#storage-class
#
# We create a separate share for every top-level item in /mnt/efs/augment
#  checkpoints
#  configs
#  data
#  eval
#  eval_results     -- skipping for now, nearly empty
#  external_models
#  ftm_checkpoints
#  jobs             -- skipping for now, nearly empty
#  lib
#  mem
#  python_env
#  user

allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  annotations:
    components.gke.io/component-name: filestorecsi
    components.gke.io/component-version: 0.10.18
    components.gke.io/layer: addon
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    k8s-app: gcp-filestore-csi-driver
  name: standard-rwx-vpc0
parameters:
  tier: standard
  network: det-sing-vpc0
provisioner: filestore.csi.storage.gke.io
reclaimPolicy: Delete
volumeBindingMode: Immediate  # WaitForFirstConsumer doesn't seem to work
---

allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  annotations:
    components.gke.io/component-name: filestorecsi
    components.gke.io/component-version: 0.10.18
    components.gke.io/layer: addon
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    k8s-app: gcp-filestore-csi-driver
  name: premium-rwx-vpc0
parameters:
  tier: premium
  network: det-sing-vpc0
provisioner: filestore.csi.storage.gke.io
reclaimPolicy: Delete
volumeBindingMode: Immediate  # WaitForFirstConsumer doesn't seem to work
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  annotations:
    components.gke.io/component-name: filestorecsi
    components.gke.io/component-version: 0.10.18
    components.gke.io/layer: addon
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    k8s-app: gcp-filestore-csi-driver
  name: enterprise-rwx-vpc0
parameters:
  tier: premium
  network: det-sing-vpc0
provisioner: filestore.csi.storage.gke.io
reclaimPolicy: Delete
volumeBindingMode: Immediate  # WaitForFirstConsumer doesn't seem to work
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  annotations:
    components.gke.io/component-name: filestorecsi
    components.gke.io/component-version: 0.10.18
    components.gke.io/layer: addon
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    k8s-app: gcp-filestore-csi-driver
  name: enterprise-multishare-rwx-vpc0
parameters:
  tier: enterprise
  multishare: "true"
  max-volume-size: "128Gi"
  network: det-sing-vpc0
provisioner: filestore.csi.storage.gke.io
reclaimPolicy: Delete
volumeBindingMode: Immediate  # WaitForFirstConsumer doesn't seem to work
---

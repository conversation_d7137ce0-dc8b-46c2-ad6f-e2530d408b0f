# This ingress config will automagically create the load balancer required in GKE
# https://cloud.google.com/kubernetes-engine/docs/concepts/ingress
#
#
# TODO health check fix
# if present in the serving Pod's spec:
# containers[].readinessProbe.httpGet.path
#
# Also, manually updated the LB to send all traffic to this backend.
#

local cluster = std.extVar('cluster');

local ingress = function(
  cluster
) {
  apiVersion: "networking.k8s.io/v1",
  kind: "Ingress",
  metadata: {
    annotations: {
      "cert-manager.io/cluster-issuer": "letsencrypt-prod",
      "nginx.ingress.kubernetes.io/proxy-body-size": "200m"
      },
    name: "determined-master",
    namespace: cluster
  },
  spec: {
    ingressClassName: "nginx",
    rules: [
      {
        host: "determined."+cluster+".r.augmentcode.com",
        http: {
          paths: [
            {
              backend: {
                service: {
                  name: "determined-master-service-determined",
                  port: {number: 8080}
                }
              },
              path: "/",
              pathType: "Prefix"
            }
          ]
        }
      }
    ],
    tls:[
      {
        hosts:["determined."+cluster+".r.augmentcode.com"],
        secretName: "determined-master-tls"  # pragma: allowlist secret
      }
    ]
  }
};

ingress(cluster)

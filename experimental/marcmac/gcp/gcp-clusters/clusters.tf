resource "google_service_account" "determined" {
  account_id   = "determined-service-account"
  display_name = "Determined service account"
}

locals {
  # map of cluster key to subnet names to
  # accomodate legacy naming in singapore
  cluster_name_map = {
    "det-sing" = "determined-sing-migration"
    "gcp-us1" = "gcp-us1-cluster"
  }
  checkpoint_storage_defaults = {
    name = "determined-checkpoint-storage"
    uniform_bucket_level_access = true
    public_access_prevention    = "enforced"
  }
  compute_networks = {
    networks = {
      auto_create_subnetworks = false
      mtu                     = 8244   # Magic MTU for optimal bandwidth
    }
  }

  # The only guaranteed unique keys here are map key and cluster_name
  clusters = {
    # Singapore has a lot of legacy naming; we keep it so we don't have to
    # recreate resources
    "det-sing" = {
      h100_name_prefix = "det-sing-"  # TODO(marcmac) remove in next maint window
      location = "asia-southeast1"
      cluster_name = "determined-sing-migration"
      checkpoint_storage_name = "determined-checkpoint-storage"
      default_node_locations = ["asia-southeast1-b"]
      node_locations = {
        "a100" = ["asia-southeast1-c"]
      }
      node_count = {
        "a100" = 8
        "h100" = 10
      }
      node_autoscaling = {
        "default_gpu" = {
          min_node_count = 0
          max_node_count = 10
        }
        "default_cpu" = {
          min_node_count = 3
          max_node_count = 10
        }
        "mid_cpu" = {
          min_node_count = 1
          max_node_count = 50
        }
        "mid_cpu_no_storage" = {
          min_node_count = 1
          max_node_count = 20
        }
      }
    }
    "gcp-us1" = {
      h100_name_prefix = ""  # TODO(marcmac) remove in next maint window
      location = "us-central1"
      cluster_name = "determined-us1-migration"
      checkpoint_storage_name = "determined-checkpoint-storage-gcp-us1"
      default_node_locations = ["us-central1-a"]
      node_locations = {
      }
      node_count = {
        "a100" = 0
        "h100" = 0
      }
      node_autoscaling = {
        "default_gpu" = {
          min_node_count = 0
          max_node_count = 10
        }
        "default_cpu" = {
          min_node_count = 3
          max_node_count = 10
        }
        "mid_cpu" = {
          min_node_count = 1
          max_node_count = 50
        }
        "mid_cpu_no_storage" = {
          min_node_count = 1
          max_node_count = 20
        }
      }
    }
  }
}

resource "google_storage_bucket" "checkpoint_storage" {
  for_each = local.clusters
  name = each.value.checkpoint_storage_name
  location = each.value.location
  uniform_bucket_level_access = local.checkpoint_storage_defaults.uniform_bucket_level_access
  public_access_prevention    = local.checkpoint_storage_defaults.public_access_prevention
}

moved {
  from = google_storage_bucket.determined_checkpoint_storage
  to   = google_storage_bucket.checkpoint_storage["det-sing"]
}

# When using a foreach, the terraform name will be:
#    resource_name[$key]

# VPCs for multi-NIC pods for maximal bandwidth.
# We need 5 VPCs each with 1 subnet (for node networks)
#   and 1 secondary range (for pod networks)
# We use the first VPC as the default VPC for the cluster
# resource "google_compute_network" "det_sing_vpc0" {
#   name                    = "det-sing-vpc0"
#   auto_create_subnetworks = false
#   mtu                     = 8244   # Magic MTU for optimal bandwidth
# }

# We cannot use count and for_each in the same block so we have to
# create 5 network and subnet blocks and for_each through the clusters
resource "google_compute_network" "vpc_networks0" {
  for_each = local.clusters
  name                    = "${each.key}-vpc0"
  auto_create_subnetworks = local.compute_networks.networks.auto_create_subnetworks
  mtu                     = local.compute_networks.networks.mtu
}
resource "google_compute_network" "vpc_networks1" {
  for_each = local.clusters
  name                    = "${each.key}-vpc1"
  auto_create_subnetworks = local.compute_networks.networks.auto_create_subnetworks
  mtu                     = local.compute_networks.networks.mtu
}
resource "google_compute_network" "vpc_networks2" {
  for_each = local.clusters
  name                    = "${each.key}-vpc2"
  auto_create_subnetworks = local.compute_networks.networks.auto_create_subnetworks
  mtu                     = local.compute_networks.networks.mtu
}
resource "google_compute_network" "vpc_networks3" {
  for_each = local.clusters
  name                    = "${each.key}-vpc3"
  auto_create_subnetworks = local.compute_networks.networks.auto_create_subnetworks
  mtu                     = local.compute_networks.networks.mtu
}
resource "google_compute_network" "vpc_networks4" {
  for_each = local.clusters
  name                    = "${each.key}-vpc4"
  auto_create_subnetworks = local.compute_networks.networks.auto_create_subnetworks
  mtu                     = local.compute_networks.networks.mtu
}

moved {
  from = google_compute_network.det_sing_vpc0
  to   = google_compute_network.vpc_networks0["det-sing"]
}
moved {
  from = google_compute_network.det_sing_vpc1
  to   = google_compute_network.vpc_networks1["det-sing"]
}
moved {
  from = google_compute_network.det_sing_vpc2
  to   = google_compute_network.vpc_networks2["det-sing"]
}
moved {
  from = google_compute_network.det_sing_vpc3
  to   = google_compute_network.vpc_networks3["det-sing"]
}
moved {
  from = google_compute_network.det_sing_vpc4
  to   = google_compute_network.vpc_networks4["det-sing"]
}

# Subnets map to network names as 10.1{N}0.0.0/16 where N is vpc_networks{N}
# Secondary ranges are 10.1{N}{R}.0.0/16 where R is the range number.
# Subnet names must be unique to the cluster, not just within the VPC.
resource "google_compute_subnetwork" "vpc0_subnet0" {
  for_each = local.clusters
  name          = "vpc0-subnet0"
  ip_cidr_range = "**********/16"
  region        = each.value.location
  network       = google_compute_network.vpc_networks0[each.key].id

  # two IP ranges for the main network's subnet
  secondary_ip_range = [
    {
      range_name    = "subnet-00-1"
      ip_cidr_range = "**********/16"
    },
    {
      range_name    = "subnet-00-2"
      ip_cidr_range = "**********/16"
    },
  ]
}

resource "google_compute_subnetwork" "vpc1_subnet0" {
  for_each = local.clusters
  name          = "vpc1-subnet0"
  ip_cidr_range = "**********/16"
  region        = each.value.location
  network       = google_compute_network.vpc_networks1[each.key].id

  secondary_ip_range = [
    {
      range_name    = "subnet-10-1"
      ip_cidr_range = "**********/16"
    },
  ]
}
resource "google_compute_subnetwork" "vpc2_subnet0" {
  for_each = local.clusters
  name          = "vpc2-subnet0"
  ip_cidr_range = "**********/16"
  region        = each.value.location
  network       = google_compute_network.vpc_networks2[each.key].id

  secondary_ip_range = [
    {
      range_name    = "subnet-20-1"
      ip_cidr_range = "**********/16"
    },
  ]
}
resource "google_compute_subnetwork" "vpc3_subnet0" {
  for_each = local.clusters
  name          = "vpc3-subnet0"
  ip_cidr_range = "**********/16"
  region        = each.value.location
  network       = google_compute_network.vpc_networks3[each.key].id

  secondary_ip_range = [
    {
      range_name    = "subnet-30-1"
      ip_cidr_range = "**********/16"
    },
  ]
}
resource "google_compute_subnetwork" "vpc4_subnet0" {
  for_each = local.clusters
  name          = "vpc4-subnet0"
  ip_cidr_range = "**********/16"
  region        = each.value.location
  network       = google_compute_network.vpc_networks4[each.key].id

  secondary_ip_range = [
    {
      range_name    = "subnet-40-1"
      ip_cidr_range = "**********/16"
    },
  ]
}
moved {
  from = google_compute_subnetwork.det_sing_vpc0_subnet0
  to   = google_compute_subnetwork.vpc0_subnet0["det-sing"]
}
moved {
  from = google_compute_subnetwork.det_sing_vpc1_subnet0
  to   = google_compute_subnetwork.vpc1_subnet0["det-sing"]
}
moved {
  from = google_compute_subnetwork.det_sing_vpc2_subnet0
  to   = google_compute_subnetwork.vpc2_subnet0["det-sing"]
}
moved {
  from = google_compute_subnetwork.det_sing_vpc3_subnet0
  to   = google_compute_subnetwork.vpc3_subnet0["det-sing"]
}
moved {
  from = google_compute_subnetwork.det_sing_vpc4_subnet0
  to   = google_compute_subnetwork.vpc4_subnet0["det-sing"]
}

resource "google_container_cluster" "clusters" {
  for_each = local.clusters
  name     = local.cluster_name_map[each.key]
  location = each.value.location

  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  remove_default_node_pool = true
  initial_node_count       = 1

  network = google_compute_network.vpc_networks0[each.key].id
  subnetwork = google_compute_subnetwork.vpc0_subnet0[each.key].id
  ip_allocation_policy {
    cluster_secondary_range_name = "subnet-00-1"
    services_secondary_range_name = "subnet-00-2"
  }
  enable_multi_networking = true
  networking_mode = "VPC_NATIVE"
  datapath_provider = "ADVANCED_DATAPATH"

  addons_config {
    gcp_filestore_csi_driver_config {
      enabled = true
    }
  }

  authenticator_groups_config {
    security_group = "<EMAIL>"
  }

  deletion_protection = true
}

moved {
  from = google_container_cluster.determined_migration
  to   = google_container_cluster.clusters["det-sing"]
}

## Node pools
#
# TODO - possibly configure locals to indicate which pools for each type
resource "google_container_node_pool" "default_gpu_nodes" {
  for_each = local.clusters
  name     = "default-gpu"
  location = each.value.location
  node_locations = lookup(each.value.node_locations, "default_gpu", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name

  autoscaling {
    min_node_count = each.value.node_autoscaling["default_gpu"].min_node_count
    max_node_count = each.value.node_autoscaling["default_gpu"].max_node_count
  }

  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name
  }
  node_config {
    preemptible  = false
    image_type   = "cos_containerd"
    machine_type = "g2-standard-96" # 96 CPU 384G
    disk_size_gb = "3000"
    disk_type    = "pd-ssd"

    guest_accelerator {
      count = 8
      type = "nvidia-l4"
      gpu_driver_installation_config {
        gpu_driver_version = "LATEST"
      }
    }

    gvnic {
      enabled = true # Required for c3 machine types
    }

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

resource "google_container_node_pool" "default_cpu_nodes" {
  for_each = local.clusters
  name     = "default-cpu"
  location = each.value.location
  provider = google-beta
  node_locations = lookup(each.value.node_locations, "default_cpu", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name
  max_pods_per_node = 110  # Inclusive of the system pods.
  autoscaling {
    min_node_count = each.value.node_autoscaling["default_cpu"].min_node_count
    max_node_count = each.value.node_autoscaling["default_cpu"].max_node_count
  }
  upgrade_settings {
    max_surge = 0
    max_unavailable = 2
  }
  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name
  }
  node_config {
    preemptible  = false
    machine_type = "e2-standard-16" # 16 core 64g
    disk_size_gb = "2048"

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

resource "google_container_node_pool" "mid_cpu_nodes" {
  for_each = local.clusters
  name     = "mid-cpu"
  location = each.value.location
  node_locations = lookup(each.value.node_locations, "mid_cpu", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name

  autoscaling {
    min_node_count = each.value.node_autoscaling["mid_cpu"].min_node_count
    max_node_count = each.value.node_autoscaling["mid_cpu"].max_node_count
  }
  upgrade_settings {
    max_surge = 0
    max_unavailable = 2
  }

  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name
  }

  node_config {
    preemptible  = false
    machine_type = "c3-standard-176-lssd" # 176 CPU 704G Ram with local SSD
    disk_size_gb = "4096"  # That's the BOOT disk size

    gvnic {
      enabled = true # Required for c3 machine types
    }

    # Local SSDs.  375G each.  Possibly not supported, automatically 32 count?
    ephemeral_storage_local_ssd_config {
      local_ssd_count = 32
    }

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
    taint {
      key    = "augment.com/local-storage"
      value  = "true"
      effect = "NO_SCHEDULE"
    }
  }
}

resource "google_container_node_pool" "mid_cpu_no_storage_nodes" {
  for_each = local.clusters
  name     = "mid-cpu-no-storage"
  location = each.value.location
  node_locations = lookup(each.value.node_locations, "mid_cpu_no_storage", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name

  autoscaling {
    min_node_count = each.value.node_autoscaling["mid_cpu_no_storage"].min_node_count
    max_node_count = each.value.node_autoscaling["mid_cpu_no_storage"].max_node_count
  }
  upgrade_settings {
    max_surge = 0
    max_unavailable = 2
  }

  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name
  }

  node_config {
    preemptible  = false
    machine_type = "c3-standard-176" # 176 CPU 704G Ram withOUT local SSD
    disk_size_gb = "4096"  # That's the BOOT disk size

    gvnic {
      enabled = true # Required for c3 machine types
    }

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

resource "google_container_node_pool" "a100_nodes" {
  for_each = local.clusters
  name     = "a100"
  location = each.value.location
  node_locations = lookup(each.value.node_locations, "a100", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name
  node_count = each.value.node_count["a100"]

  upgrade_settings {
    max_surge = 0
    max_unavailable = 4
  }

  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name
  }

  node_config {
    preemptible  = false
    image_type   = "cos_containerd"
    machine_type = "a2-ultragpu-8g" # 96 CPU 1360G
    disk_type    = "pd-ssd"
    disk_size_gb = "3000"
    fast_socket {
      enabled = true
    }

    local_nvme_ssd_block_config {
      local_ssd_count = 8
    }

    # TODO ephemeral_storage_config
    # gcfs_config?
    # image_type?
    # taints?

    guest_accelerator {
      count = 8
      type  = "nvidia-a100-80gb"
      gpu_driver_installation_config {
        gpu_driver_version = "LATEST"
      }
    }

    gvnic {
      enabled = true # Required for c3 machine types
    }

    reservation_affinity {
      # "SPECIFIC_RESERVATION" does not work with our reservation
      consume_reservation_type = "ANY_RESERVATION"
      # key = "compute.googleapis.com/reservation-name"
      # values = [
      #   "singapore-a2-ultra-reservation"
      # ]
    }

    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

# H100 node pool in singapore will be replaced by this because
# it's a name format change.
resource "google_container_node_pool" "h100_nodes" {
  for_each = local.clusters
  # transitional hack to allow me to deploy this without rebuilding the h100
  # pool in singapore, which requires a maintenance window.
  # TODO(marcamc) remove this in the next maintenance window
  name     = "${each.value.h100_name_prefix}h100"
  location = each.value.location
  provider = google-beta
  node_locations = lookup(each.value.node_locations, "h100", each.value.default_node_locations)
  cluster = google_container_cluster.clusters[each.key].name
  max_pods_per_node = 32  # Inclusive of the system pods.
  node_count = each.value.node_count["h100"]

  upgrade_settings {
    max_surge = 0
    max_unavailable = 2
  }

  network_config {
    # Don't create a new subnet for pods here
    create_pod_range = false
    # Use this subnet in vpc0 for pods in the node pool.
    pod_range = google_compute_subnetwork.vpc0_subnet0[each.key].secondary_ip_range[0].range_name

    # We always get the default VPC (vpc0), so don't list that here.
    additional_node_network_configs {
      network = google_compute_network.vpc_networks1[each.key].name
      subnetwork = google_compute_subnetwork.vpc1_subnet0[each.key].name
    }
    additional_pod_network_configs {
      subnetwork = google_compute_subnetwork.vpc1_subnet0[each.key].name
      secondary_pod_range = google_compute_subnetwork.vpc1_subnet0[each.key].secondary_ip_range[0].range_name
      max_pods_per_node = 16
    }

    additional_node_network_configs {
      network = google_compute_network.vpc_networks2[each.key].name
      subnetwork = google_compute_subnetwork.vpc2_subnet0[each.key].name
    }
    additional_pod_network_configs {
      subnetwork = google_compute_subnetwork.vpc2_subnet0[each.key].name
      secondary_pod_range = google_compute_subnetwork.vpc2_subnet0[each.key].secondary_ip_range[0].range_name
      max_pods_per_node = 16
    }

    additional_node_network_configs {
      network = google_compute_network.vpc_networks3[each.key].name
      subnetwork = google_compute_subnetwork.vpc3_subnet0[each.key].name
    }
    additional_pod_network_configs {
      subnetwork = google_compute_subnetwork.vpc3_subnet0[each.key].name
      secondary_pod_range = google_compute_subnetwork.vpc3_subnet0[each.key].secondary_ip_range[0].range_name
      max_pods_per_node = 16
    }

    additional_node_network_configs {
      network = google_compute_network.vpc_networks4[each.key].name
      subnetwork = google_compute_subnetwork.vpc4_subnet0[each.key].name
    }
    additional_pod_network_configs {
      subnetwork = google_compute_subnetwork.vpc4_subnet0[each.key].name
      secondary_pod_range = google_compute_subnetwork.vpc4_subnet0[each.key].secondary_ip_range[0].range_name
      max_pods_per_node = 16
    }

  }

  node_config {
    preemptible  = false
    image_type   = "cos_containerd"
    machine_type = "a3-highgpu-8g"
    disk_type    = "pd-ssd"
    disk_size_gb = "3000"
    fast_socket {
      enabled = true
    }

    local_nvme_ssd_block_config {
      local_ssd_count = 16
    }

    # TODO ephemeral_storage_config
    # gcfs_config?
    # image_type?
    # taints?

    guest_accelerator {
      count = 8
      type  = "nvidia-h100-80gb"
      gpu_driver_installation_config {
        gpu_driver_version = "LATEST"
      }
    }

    gvnic {
      enabled = true # Required for c3 machine types
    }

    reservation_affinity {
      # "SPECIFIC_RESERVATION" does not work with our reservation
      consume_reservation_type = "ANY_RESERVATION"
      # key = "compute.googleapis.com/reservation-name"
      # values = [
      #   "singapore-a2-ultra-reservation"
      # ]
    }

    service_account = google_service_account.determined.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}


moved {
  from = google_container_node_pool.det_sing_a100_nodes
  to   = google_container_node_pool.a100_nodes["det-sing"]
}
moved {
  from = google_container_node_pool.det_sing_default_cpu_nodes
  to   = google_container_node_pool.default_cpu_nodes["det-sing"]
}
moved {
  from = google_container_node_pool.det_sing_default_gpu_nodes
  to   = google_container_node_pool.default_gpu_nodes["det-sing"]
}
moved {
  from = google_container_node_pool.det_sing_h100_nodes
  to   = google_container_node_pool.h100_nodes["det-sing"]
}
moved {
  from = google_container_node_pool.det_sing_mid_cpu
  to   = google_container_node_pool.mid_cpu_nodes["det-sing"]
}
moved {
  from = google_container_node_pool.det_sing_mid_cpu_no_storage
  to   = google_container_node_pool.mid_cpu_no_storage_nodes["det-sing"]
}

#
# TODO figure out how this works
#
# resource "google_service_account_iam_binding" "determined-account-iam" {
#   service_account_id = google_service_account.determined.name
#   role               = "roles/iam.serviceAccountUser"

#   members = [
#     "group:<EMAIL>",
#   ]
# }

# resource "google_service_account_iam_member" "determined-account-member" {
#   service_account_id = google_service_account.determined.name
#   role               = "roles/iam.serviceAccountUser"
#   member             = "group:<EMAIL>"
# }

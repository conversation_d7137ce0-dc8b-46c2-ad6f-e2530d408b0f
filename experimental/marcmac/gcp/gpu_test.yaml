apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: null
  name: imagetest
spec:
  nodeSelector:
    cloud.google.com/gke-accelerator: nvidia-l4
    #cloud.google.com/gke-accelerator: nvidia-a100-80gb
  containers:
  - command: [ "/bin/bash", "-c", "--" ]
    args: [ "while true; do sleep 30; done;" ]
    image: asia-southeast1-docker.pkg.dev/augment-387916/au-docker-singapore/augment_gpu:cuda-11.8-py-3.9-pytorch-2.0.1-gpt-neox-deepspeed-gpu-0.19.12-1
    name: imagetest
    resources:
      limits:
        nvidia.com/gpu: 2
  dnsPolicy: ClusterFirst
  restartPolicy: Always
  tolerations:
  - key: nvidia.com/gpu
    operator: Exists
status: {}

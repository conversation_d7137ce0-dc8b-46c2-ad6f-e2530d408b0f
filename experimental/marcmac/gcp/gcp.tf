terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
    }
  }
}

provider "google" {
  credentials = file("~/augment-gcp-terraform.json")

  project = "augment-387916"
  region  = "asia-southeast1"
  zone    = "asia-southeast1-c"
}

provider "google-beta" {
  credentials = file("~/augment-gcp-terraform.json")

  project = "augment-387916"
  region  = "asia-southeast1"
  zone    = "asia-southeast1-c"
}

provider "kubernetes" {
  config_path    = "~/.kube/config"
  config_context = "gcp-sing"
}

module "dev-instance" {
  source = "./dev-instance"
}

module "gcp-clusters" {
  source = "./gcp-clusters"
}

module "rbac" {
  source = "./rbac"
}

resource "kubernetes_role" "gcp_users" {
  metadata {
    name = "gcp-users"
    namespace = "determined"
  }

  # Allow creation of pods to create dev pods
  rule {
    api_groups     = [""]
    resources      = ["pods", "configmaps"]
    verbs          = ["get", "list", "watch", "create", "delete", "update"]
  }
  # Allow exec in dev pods
  rule {
    api_groups     = [""]
    resources      = ["pods/exec"]
    verbs          = ["create"]
  }

  # Allow creation of services to expose dev pods
  rule {
    api_groups     = [""]
    resources      = ["services"]
    verbs          = ["get", "list", "watch", "create", "delete", "update"]
  }

  # Read everything else
  rule {
    api_groups     = ["*"]
    resources      = ["*"]
    verbs          = ["get", "list", "watch"]
  }

}

resource "kubernetes_role_binding" "gcp_users_binding" {
  metadata {
    name      = "gcp-users-binding"
    namespace = "determined"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.gcp_users.metadata.0.name
  }
  subject {
    kind      = "Group"
    name      = "<EMAIL>"
    api_group = "rbac.authorization.k8s.io"
    namespace = "determined"
  }
}

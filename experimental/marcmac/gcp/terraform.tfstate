{"version": 4, "terraform_version": "1.9.3", "serial": 378, "lineage": "a467b5f9-b2c8-35c8-cdec-277c85597c35", "outputs": {}, "resources": [{"module": "module.dev-instance", "mode": "managed", "type": "google_compute_instance", "name": "augment-base-cpu", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": false, "device_name": "augment-base-cpu", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/images/dev-vm-cpu-001", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 200, "storage_pool": "", "type": "pd-balanced"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/disks/augment-base-cpu"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Unknown CPU Platform", "current_status": "TERMINATED", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"goog-ec-src": "vm_add-tf"}, "enable_display": false, "guest_accelerator": [], "hostname": "", "id": "projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-cpu", "instance_id": "5594147545564307126", "label_fingerprint": "u__a2NSF3Cs=", "labels": {"goog-ec-src": "vm_add-tf"}, "machine_type": "e2-standard-16", "metadata": {"ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "metadata_fingerprint": "J0mW-UHziLw=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "augment-base-cpu", "network_interface": [{"access_config": [{"nat_ip": "", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/default", "network_ip": "***********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/default", "subnetwork_project": "augment-387916"}], "network_performance_config": [], "params": [], "project": "augment-387916", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "MIGRATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-cpu", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": [], "tags_fingerprint": "42WmSpB8rSM=", "terraform_labels": {"goog-ec-src": "vm_add-tf"}, "timeouts": null, "zone": "asia-southeast1-b"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}]], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.dev-instance", "mode": "managed", "type": "google_compute_instance", "name": "augment-base-gpu", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": false, "device_name": "augment-base-gpu", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/images/dev-vm-cpu-001", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 200, "storage_pool": "", "type": "pd-balanced"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/disks/augment-base-gpu"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Unknown CPU Platform", "current_status": "TERMINATED", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"goog-ec-src": "vm_add-tf"}, "enable_display": false, "guest_accelerator": [{"count": 1, "type": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/acceleratorTypes/nvidia-l4"}], "hostname": "", "id": "projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-gpu", "instance_id": "4108977197824073400", "label_fingerprint": "u__a2NSF3Cs=", "labels": {"goog-ec-src": "vm_add-tf"}, "machine_type": "g2-standard-16", "metadata": {"ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "metadata_fingerprint": "J0mW-UHziLw=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "augment-base-gpu", "network_interface": [{"access_config": [{"nat_ip": "", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/default", "network_ip": "***********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/default", "subnetwork_project": "augment-387916"}], "network_performance_config": [], "params": [], "project": "augment-387916", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "TERMINATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-gpu", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": [], "tags_fingerprint": "42WmSpB8rSM=", "terraform_labels": {"goog-ec-src": "vm_add-tf"}, "timeouts": null, "zone": "asia-southeast1-b"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}]], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.dev-instance", "mode": "managed", "type": "google_compute_instance", "name": "augment-base-gpu-dev", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": false, "device_name": "augment-base-gpu-dev", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/images/dev-vm-gpu-001", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 200, "storage_pool": "", "type": "pd-balanced"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/disks/augment-base-gpu-dev"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Unknown CPU Platform", "current_status": "TERMINATED", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"goog-ec-src": "vm_add-tf"}, "enable_display": false, "guest_accelerator": [{"count": 1, "type": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/acceleratorTypes/nvidia-l4"}], "hostname": "", "id": "projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-gpu-dev", "instance_id": "8514279970275319597", "label_fingerprint": "u__a2NSF3Cs=", "labels": {"goog-ec-src": "vm_add-tf"}, "machine_type": "g2-standard-16", "metadata": {"ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "metadata_fingerprint": "J0mW-UHziLw=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "augment-base-gpu-dev", "network_interface": [{"access_config": [{"nat_ip": "", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/default", "network_ip": "***********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/default", "subnetwork_project": "augment-387916"}], "network_performance_config": [], "params": [], "project": "augment-387916", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "TERMINATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instances/augment-base-gpu-dev", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": [], "tags_fingerprint": "42WmSpB8rSM=", "terraform_labels": {"goog-ec-src": "vm_add-tf"}, "timeouts": null, "zone": "asia-southeast1-b"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}]], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_network", "name": "vpc_networks0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/det-sing-vpc0", "internal_ipv6_range": "", "mtu": 8244, "name": "det-sing-vpc0", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "6009629526310112872", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc0", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/gcp-us1-vpc0", "internal_ipv6_range": "", "mtu": 8244, "name": "gcp-us1-vpc0", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "4496795453079610169", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc0", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_network", "name": "vpc_networks1", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/det-sing-vpc1", "internal_ipv6_range": "", "mtu": 8244, "name": "det-sing-vpc1", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "3344638736169740739", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc1", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/gcp-us1-vpc1", "internal_ipv6_range": "", "mtu": 8244, "name": "gcp-us1-vpc1", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "4350389844844143416", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc1", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_network", "name": "vpc_networks2", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/det-sing-vpc2", "internal_ipv6_range": "", "mtu": 8244, "name": "det-sing-vpc2", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "6286748603023665603", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc2", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/gcp-us1-vpc2", "internal_ipv6_range": "", "mtu": 8244, "name": "gcp-us1-vpc2", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "3034130377488130872", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc2", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_network", "name": "vpc_networks3", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/det-sing-vpc3", "internal_ipv6_range": "", "mtu": 8244, "name": "det-sing-vpc3", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "133271146121963971", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc3", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/gcp-us1-vpc3", "internal_ipv6_range": "", "mtu": 8244, "name": "gcp-us1-vpc3", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "4854930613861802808", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc3", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_network", "name": "vpc_networks4", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/det-sing-vpc4", "internal_ipv6_range": "", "mtu": 8244, "name": "det-sing-vpc4", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "352507949314779587", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc4", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/augment-387916/global/networks/gcp-us1-vpc4", "internal_ipv6_range": "", "mtu": 8244, "name": "gcp-us1-vpc4", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "8061684322406949688", "project": "augment-387916", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc4", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_subnetwork", "name": "vpc0_subnet0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"creation_timestamp": "2024-02-08T10:39:26.390-08:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc0-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc0-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc0", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "asia-southeast1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-00-1"}, {"ip_cidr_range": "**********/16", "range_name": "subnet-00-2"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/vpc0-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0"]}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"creation_timestamp": "2024-08-25T09:38:04.464-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/us-central1/subnetworks/vpc0-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc0-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc0", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-00-1"}, {"ip_cidr_range": "**********/16", "range_name": "subnet-00-2"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/us-central1/subnetworks/vpc0-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_subnetwork", "name": "vpc1_subnet0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"creation_timestamp": "2024-02-08T10:21:40.696-08:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc1-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc1-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc1", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "asia-southeast1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-10-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/vpc1-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks1"]}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"creation_timestamp": "2024-08-25T09:38:04.768-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/us-central1/subnetworks/vpc1-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc1-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc1", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-10-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/us-central1/subnetworks/vpc1-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks1"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_subnetwork", "name": "vpc2_subnet0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"creation_timestamp": "2024-02-08T10:26:02.207-08:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc2-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc2-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc2", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "asia-southeast1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-20-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/vpc2-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks2"]}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"creation_timestamp": "2024-08-25T09:37:54.460-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/us-central1/subnetworks/vpc2-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc2-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc2", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-20-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/us-central1/subnetworks/vpc2-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks2"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_subnetwork", "name": "vpc3_subnet0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"creation_timestamp": "2024-02-08T10:21:41.603-08:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc3-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc3-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc3", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "asia-southeast1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-30-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/vpc3-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks3"]}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"creation_timestamp": "2024-08-25T09:38:04.815-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/us-central1/subnetworks/vpc3-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc3-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc3", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-30-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/us-central1/subnetworks/vpc3-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks3"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_compute_subnetwork", "name": "vpc4_subnet0", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 0, "attributes": {"creation_timestamp": "2024-02-08T10:40:10.511-08:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc4-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc4-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/det-sing-vpc4", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "asia-southeast1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-40-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/asia-southeast1/subnetworks/vpc4-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks4"]}, {"index_key": "gcp-us1", "schema_version": 0, "attributes": {"creation_timestamp": "2024-08-25T09:38:04.796-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "**********", "id": "projects/augment-387916/regions/us-central1/subnetworks/vpc4-subnet0", "internal_ipv6_prefix": "", "ip_cidr_range": "**********/16", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "vpc4-subnet0", "network": "https://www.googleapis.com/compute/v1/projects/augment-387916/global/networks/gcp-us1-vpc4", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "augment-387916", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "**********/16", "range_name": "subnet-40-1"}], "self_link": "https://www.googleapis.com/compute/v1/projects/augment-387916/regions/us-central1/subnetworks/vpc4-subnet0", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks4"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_cluster", "name": "clusters", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 2, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [{"enabled": true}], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": true}], "ray_operator_config": [], "stateful_ha_config": []}], "allow_net_admin": null, "authenticator_groups_config": [{"security_group": "<EMAIL>"}], "binary_authorization": [{"enabled": false, "evaluation_mode": ""}], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "auto_provisioning_locations": [], "autoscaling_profile": "BALANCED", "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "**********/16", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "ADVANCED_DATAPATH", "default_max_pods_per_node": 110, "default_snat_status": [{"disabled": false}], "deletion_protection": true, "description": "", "dns_config": [], "enable_autopilot": false, "enable_cilium_clusterwide_network_policy": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_multi_networking": true, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "**************", "fleet": [], "gateway_api_config": [], "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration", "identity_service_config": [], "initial_node_count": 1, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "**********/16", "cluster_secondary_range_name": "subnet-00-1", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "**********/16", "services_secondary_range_name": "subnet-00-2", "stack_type": "IPV4"}], "label_fingerprint": "a9dc16a7", "location": "asia-southeast1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.30.3-gke.1225000", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "enable_relay": false, "relay_mode": "DISABLED"}], "enable_components": ["SYSTEM_COMPONENTS"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "determined-sing-migration", "network": "projects/augment-387916/global/networks/det-sing-vpc0", "network_policy": [{"enabled": false, "provider": "PROVIDER_UNSPECIFIED"}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 2048, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_locations": ["asia-southeast1-a", "asia-southeast1-b", "asia-southeast1-c"], "node_pool": [{"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 3, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 2, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migra-default-cpu-671a2da4-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migra-default-cpu-671a2da4-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 2048, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 3, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, {"autoscaling": [], "initial_node_count": 10, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-mig-det-sing-h100-a3d78493-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-mig-det-sing-h100-a3d78493-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 32, "name": "det-sing-h100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [{"network": "det-sing-vpc1", "subnetwork": "vpc1-subnet0"}, {"network": "det-sing-vpc2", "subnetwork": "vpc2-subnet0"}, {"network": "det-sing-vpc3", "subnetwork": "vpc3-subnet0"}, {"network": "det-sing-vpc4", "subnetwork": "vpc4-subnet0"}], "additional_pod_network_configs": [{"max_pods_per_node": 16, "secondary_pod_range": "subnet-10-1", "subnetwork": "vpc1-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-20-1", "subnetwork": "vpc2-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-30-1", "subnetwork": "vpc3-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-40-1", "subnetwork": "vpc4-subnet0"}], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-h100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 16}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a3-highgpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 10, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, {"autoscaling": [], "initial_node_count": 8, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-determined-sing-migration-a100-00abcdd4-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-determined-sing-migration-a100-00abcdd4-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "a100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-a100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 8}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a2-ultragpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 8, "node_locations": ["asia-southeast1-c"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 4, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 0, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migra-default-gpu-837a8d75-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migra-default-gpu-837a8d75-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 50, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migration-mid-cpu-534f4645-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migration-mid-cpu-534f4645-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [{"local_ssd_count": 32}], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176-lssd", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 20, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing--mid-cpu-no-stora-ea3a333f-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing--mid-cpu-no-stora-ea3a333f-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu-no-storage", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}], "node_pool_auto_config": [], "node_pool_defaults": [{"node_config_defaults": [{"containerd_config": [], "logging_variant": "DEFAULT"}]}], "node_version": "1.30.3-gke.1225000", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "***********", "private_endpoint_subnetwork": "", "public_endpoint": "**************"}], "private_ipv6_google_access": "", "project": "augment-387916", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": true, "resource_labels": {}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "**********/16", "subnetwork": "projects/augment-387916/regions/asia-southeast1/subnetworks/vpc0-subnet0", "timeouts": null, "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "master_auth"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "client_key"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0"]}, {"index_key": "gcp-us1", "schema_version": 2, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [{"enabled": true}], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": true}], "ray_operator_config": [], "stateful_ha_config": []}], "allow_net_admin": null, "authenticator_groups_config": [{"security_group": "<EMAIL>"}], "binary_authorization": [{"enabled": false, "evaluation_mode": ""}], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "auto_provisioning_locations": [], "autoscaling_profile": "BALANCED", "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "**********/16", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "ADVANCED_DATAPATH", "default_max_pods_per_node": 110, "default_snat_status": [{"disabled": false}], "deletion_protection": true, "description": "", "dns_config": [], "enable_autopilot": false, "enable_cilium_clusterwide_network_policy": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_multi_networking": true, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "************", "fleet": [], "gateway_api_config": [], "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster", "identity_service_config": [], "initial_node_count": 1, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "**********/16", "cluster_secondary_range_name": "subnet-00-1", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "**********/16", "services_secondary_range_name": "subnet-00-2", "stack_type": "IPV4"}], "label_fingerprint": "a9dc16a7", "location": "us-central1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.30.3-gke.1225000", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "enable_relay": false, "relay_mode": "DISABLED"}], "enable_components": ["SYSTEM_COMPONENTS", "STORAGE", "HPA", "POD", "DAEMONSET", "DEPLOYMENT", "STATEFULSET", "CADVISOR", "KUBELET"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "gcp-us1-cluster", "network": "projects/augment-387916/global/networks/gcp-us1-vpc0", "network_policy": [{"enabled": false, "provider": "PROVIDER_UNSPECIFIED"}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_locations": ["us-central1-b", "us-central1-c", "us-central1-f"], "node_pool": [{"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 0, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-default-gpu-19c47137-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-default-gpu-19c47137-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, {"autoscaling": [], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-a100-09f77506-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-a100-09f77506-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "a100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-a100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 8}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a2-ultragpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 4, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 3, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-default-cpu-1b3eb662-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-default-cpu-1b3eb662-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 2048, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 2, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 20, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-mid-cpu-no-storag-917f3ae5-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-mid-cpu-no-storag-917f3ae5-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu-no-storage", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 50, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-mid-cpu-50bae30c-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-mid-cpu-50bae30c-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [{"local_ssd_count": 32}], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176-lssd", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, {"autoscaling": [], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-det-sing-h100-5eb5f399-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-det-sing-h100-5eb5f399-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 32, "name": "det-sing-h100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [{"network": "gcp-us1-vpc1", "subnetwork": "vpc1-subnet0"}, {"network": "gcp-us1-vpc2", "subnetwork": "vpc2-subnet0"}, {"network": "gcp-us1-vpc3", "subnetwork": "vpc3-subnet0"}, {"network": "gcp-us1-vpc4", "subnetwork": "vpc4-subnet0"}], "additional_pod_network_configs": [{"max_pods_per_node": 16, "secondary_pod_range": "subnet-10-1", "subnetwork": "vpc1-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-20-1", "subnetwork": "vpc2-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-30-1", "subnetwork": "vpc3-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-40-1", "subnetwork": "vpc4-subnet0"}], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-h100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 16}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a3-highgpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}], "node_pool_auto_config": [], "node_pool_defaults": [{"node_config_defaults": [{"containerd_config": [], "logging_variant": "DEFAULT"}]}], "node_version": "1.30.2-gke.1587003", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "**********", "private_endpoint_subnetwork": "", "public_endpoint": "************"}], "private_ipv6_google_access": "", "project": "augment-387916", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": true, "resource_labels": {}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "**********/16", "subnetwork": "projects/augment-387916/regions/us-central1/subnetworks/vpc0-subnet0", "timeouts": null, "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "master_auth"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "client_key"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "a100_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/a100", "initial_node_count": 8, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-determined-sing-migration-a100-00abcdd4-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-determined-sing-migration-a100-00abcdd4-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "a100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-a100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 8}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a2-ultragpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 8, "node_locations": ["asia-southeast1-c"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 4, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/a100", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-a100-09f77506-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-a100-09f77506-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "a100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-a100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 8}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a2-ultragpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 4, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "default_cpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 3, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/default-cpu", "initial_node_count": 2, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migra-default-cpu-671a2da4-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/beta/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migra-default-cpu-671a2da4-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 2048, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_config": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "sandbox_config": [], "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 3, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 3, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/default-cpu", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-default-cpu-1b3eb662-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/beta/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-default-cpu-1b3eb662-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 2048, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_config": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "sandbox_config": [], "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 2, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "default_gpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 0, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/default-gpu", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migra-default-gpu-837a8d75-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migra-default-gpu-837a8d75-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 0, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/default-gpu", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-default-gpu-19c47137-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-default-gpu-19c47137-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "h100_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/det-sing-h100", "initial_node_count": 10, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-mig-det-sing-h100-a3d78493-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/beta/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-mig-det-sing-h100-a3d78493-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 32, "name": "det-sing-h100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [{"network": "det-sing-vpc1", "subnetwork": "vpc1-subnet0"}, {"network": "det-sing-vpc2", "subnetwork": "vpc2-subnet0"}, {"network": "det-sing-vpc3", "subnetwork": "vpc3-subnet0"}, {"network": "det-sing-vpc4", "subnetwork": "vpc4-subnet0"}], "additional_pod_network_configs": [{"max_pods_per_node": 16, "secondary_pod_range": "subnet-10-1", "subnetwork": "vpc1-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-20-1", "subnetwork": "vpc2-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-30-1", "subnetwork": "vpc3-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-40-1", "subnetwork": "vpc4-subnet0"}], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_config": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-h100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 16}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a3-highgpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": []}], "resource_labels": {}, "resource_manager_tags": {}, "sandbox_config": [], "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 10, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_network.vpc_networks1", "module.gcp-clusters.google_compute_network.vpc_networks2", "module.gcp-clusters.google_compute_network.vpc_networks3", "module.gcp-clusters.google_compute_network.vpc_networks4", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc1_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc2_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc3_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc4_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/h100", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-h100-b60af478-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/beta/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-h100-b60af478-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 32, "name": "h100", "name_prefix": "", "network_config": [{"additional_node_network_configs": [{"network": "gcp-us1-vpc1", "subnetwork": "vpc1-subnet0"}, {"network": "gcp-us1-vpc2", "subnetwork": "vpc2-subnet0"}, {"network": "gcp-us1-vpc3", "subnetwork": "vpc3-subnet0"}, {"network": "gcp-us1-vpc4", "subnetwork": "vpc4-subnet0"}], "additional_pod_network_configs": [{"max_pods_per_node": 16, "secondary_pod_range": "subnet-10-1", "subnetwork": "vpc1-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-20-1", "subnetwork": "vpc2-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-30-1", "subnetwork": "vpc3-subnet0"}, {"max_pods_per_node": 16, "secondary_pod_range": "subnet-40-1", "subnetwork": "vpc4-subnet0"}], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "nvidia.com/gpu", "value": "present"}], "enable_confidential_storage": false, "ephemeral_storage_config": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [{"enabled": true}], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": null, "type": "nvidia-h100-80gb"}], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [{"local_ssd_count": 16}], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "a3-highgpu-8g", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [{"consume_reservation_type": "ANY_RESERVATION", "key": "", "values": null}], "resource_labels": null, "resource_manager_tags": null, "sandbox_config": [], "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": null, "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_network.vpc_networks1", "module.gcp-clusters.google_compute_network.vpc_networks2", "module.gcp-clusters.google_compute_network.vpc_networks3", "module.gcp-clusters.google_compute_network.vpc_networks4", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc1_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc2_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc3_subnet0", "module.gcp-clusters.google_compute_subnetwork.vpc4_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "mid_cpu_no_storage_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 20, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/mid-cpu-no-storage", "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing--mid-cpu-no-stora-ea3a333f-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing--mid-cpu-no-stora-ea3a333f-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu-no-storage", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 20, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/mid-cpu-no-storage", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-mid-cpu-no-storag-917f3ae5-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-mid-cpu-no-storag-917f3ae5-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu-no-storage", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_container_node_pool", "name": "mid_cpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 50, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "determined-sing-migration", "id": "projects/augment-387916/locations/asia-southeast1/clusters/determined-sing-migration/nodePools/mid-cpu", "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-determined-sing-migration-mid-cpu-534f4645-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-determined-sing-migration-mid-cpu-534f4645-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [{"local_ssd_count": 32}], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176-lssd", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.3-gke.1225000"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 50, "min_node_count": 1, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "gcp-us1-cluster", "id": "projects/augment-387916/locations/us-central1/clusters/gcp-us1-cluster/nodePools/mid-cpu", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroupManagers/gke-gcp-us1-cluster-mid-cpu-50bae30c-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/us-central1-a/instanceGroups/gke-gcp-us1-cluster-mid-cpu-50bae30c-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "**********/16", "pod_range": "subnet-00-1"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 4096, "disk_type": "pd-balanced", "effective_taints": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [{"local_ssd_count": 32}], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-176-lssd", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [{"effect": "NO_SCHEDULE", "key": "augment.com/local-storage", "value": "true"}], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["us-central1-a"], "operation": null, "placement_policy": [], "project": "augment-387916", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 0, "max_unavailable": 2, "strategy": "SURGE"}], "version": "1.30.2-gke.1587003"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.gcp-clusters.google_compute_network.vpc_networks0", "module.gcp-clusters.google_compute_subnetwork.vpc0_subnet0", "module.gcp-clusters.google_container_cluster.clusters", "module.gcp-clusters.google_service_account.determined"]}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_service_account", "name": "determined", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "determined-service-account", "create_ignore_already_exists": null, "description": "", "disabled": false, "display_name": "Determined service account", "email": "<EMAIL>", "id": "projects/augment-387916/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/augment-387916/serviceAccounts/<EMAIL>", "project": "augment-387916", "timeouts": null, "unique_id": "104863729224588781072"}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"module": "module.gcp-clusters", "mode": "managed", "type": "google_storage_bucket", "name": "checkpoint_storage", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "det-sing", "schema_version": 1, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {}, "enable_object_retention": false, "encryption": [], "force_destroy": false, "id": "determined-checkpoint-storage", "labels": {}, "lifecycle_rule": [], "location": "ASIA-SOUTHEAST1", "logging": [], "name": "determined-checkpoint-storage", "project": "augment-387916", "project_number": ************, "public_access_prevention": "enforced", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/determined-checkpoint-storage", "soft_delete_policy": [{"effective_time": "2024-03-01T08:00:00.000Z", "retention_duration_seconds": 604800}], "storage_class": "STANDARD", "terraform_labels": {}, "timeouts": null, "uniform_bucket_level_access": true, "url": "gs://determined-checkpoint-storage", "versioning": [], "website": []}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************"}, {"index_key": "gcp-us1", "schema_version": 1, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {}, "enable_object_retention": false, "encryption": [], "force_destroy": false, "id": "determined-checkpoint-storage-gcp-us1", "labels": {}, "lifecycle_rule": [], "location": "US-CENTRAL1", "logging": [], "name": "determined-checkpoint-storage-gcp-us1", "project": "augment-387916", "project_number": ************, "public_access_prevention": "enforced", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/determined-checkpoint-storage-gcp-us1", "soft_delete_policy": [{"effective_time": "2024-08-25T16:37:43.287Z", "retention_duration_seconds": 604800}], "storage_class": "STANDARD", "terraform_labels": {}, "timeouts": null, "uniform_bucket_level_access": true, "url": "gs://determined-checkpoint-storage-gcp-us1", "versioning": [], "website": []}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************"}]}, {"module": "module.rbac", "mode": "managed", "type": "kubernetes_role", "name": "gcp_users", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "determined/gcp-users", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "gcp-users", "namespace": "determined", "resource_version": "16378360", "uid": "256be645-3bfe-4019-9134-d9d7fc59a6e2"}], "rule": [{"api_groups": [""], "resource_names": [], "resources": ["configmaps", "pods"], "verbs": ["create", "delete", "get", "list", "update", "watch"]}, {"api_groups": [""], "resource_names": [], "resources": ["pods/exec"], "verbs": ["create"]}, {"api_groups": [""], "resource_names": [], "resources": ["services"], "verbs": ["create", "delete", "get", "list", "update", "watch"]}, {"api_groups": ["*"], "resource_names": [], "resources": ["*"], "verbs": ["get", "list", "watch"]}]}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.rbac", "mode": "managed", "type": "kubernetes_role_binding", "name": "gcp_users_binding", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "determined/gcp-users-binding", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "gcp-users-binding", "namespace": "determined", "resource_version": "16378418", "uid": "0914bb91-b6ce-4eab-9e83-3072073820b5"}], "role_ref": [{"api_group": "rbac.authorization.k8s.io", "kind": "Role", "name": "gcp-users"}], "subject": [{"api_group": "rbac.authorization.k8s.io", "kind": "Group", "name": "<EMAIL>", "namespace": "determined"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.rbac.kubernetes_role.gcp_users"]}]}], "check_results": null}
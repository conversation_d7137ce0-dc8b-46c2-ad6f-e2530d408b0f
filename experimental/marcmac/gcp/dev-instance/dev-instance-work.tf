# This code is compatible with Terraform 4.25.0 and versions that are backwards compatible to 4.25.0.
# For information about validating this Terraform code, see https://developer.hashicorp.com/terraform/tutorials/gcp-get-started/google-cloud-platform-build#format-and-validate-the-configuration

resource "google_compute_instance" "augment-base-gpu-dev" {
  boot_disk {
    auto_delete = false
    device_name = "augment-base-gpu-dev"

    # initialize_params {
    #   image = "projects/augment-387916/global/images/dev-vm-gpu-002"
    #   size  = 200
    #   type  = "pd-balanced"
    # }

    mode = "READ_WRITE"
  }

  can_ip_forward      = false
  deletion_protection = false
  enable_display      = false

  guest_accelerator {
    count = 1
    type  = "projects/augment-387916/zones/asia-southeast1-b/acceleratorTypes/nvidia-l4"
  }

  labels = {
    goog-ec-src = "vm_add-tf"
  }

  machine_type = "g2-standard-16"

  metadata = {
    ssh-keys = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
  }

  name = "augment-base-gpu-dev"

  network_interface {
    access_config {
      network_tier = "PREMIUM"
    }

    subnetwork = "projects/augment-387916/regions/asia-southeast1/subnetworks/default"
  }

  scheduling {
    automatic_restart   = true
    on_host_maintenance = "TERMINATE"
    preemptible         = false
    provisioning_model  = "STANDARD"
  }

  service_account {
    email  = "<EMAIL>"
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }

  shielded_instance_config {
    enable_integrity_monitoring = true
    enable_secure_boot          = false
    enable_vtpm                 = true
  }

  zone = "asia-southeast1-b"
}

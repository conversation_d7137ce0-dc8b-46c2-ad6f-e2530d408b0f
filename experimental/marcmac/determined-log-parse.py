#!/usr/bin/env python3

import argparse
import json
import hashlib
import sys
import urllib.parse

from determined.common.api.logs import trial_logs
from determined.common.api import authentication, bindings
from determined.common.api._util import canonicalize_master_url
from pathlib import Path
from typing import Iterable, Generator


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--filename",
        "-f",
        type=str,
        default=None,
        required=False,
        help="Path to the log file (downloaded from determined with --json)",
    )
    parser.add_argument(
        "--url",
        "-u",
        type=str,
        default=None,
        required=False,
        help="URL to the experiment",
    )
    parser.add_argument(
        "--write-traces",
        "-w",
        action="store_true",
        help="Write traces to files by hash",
    )
    parser.add_argument(
        "--include-unranked",
        action="store_true",
        help="Include unranked stack traces, you probably do not want this.",
    )
    return parser.parse_args()


# TODO(marcmac) this is kind of terrible, we should probably just pull the log from determined.
def json_reader(fh: Iterable) -> Generator[dict, None, None]:
    text = ""
    error = None
    e = None
    for line in fh:
        text += line
        if text.endswith("}\n"):  # We know how this is structured, still terrible
            try:
                yield json.loads(text)
                text = ""
                e = None
            except Exception as e:  # pylint: disable=broad-except
                error = e
    if error is not None:
        raise error


def main(
    fh: Iterable, write_traces: bool = False, include_unranked: bool = False
) -> None:
    stack_traces = {}
    # We only need to track traces by rankId
    for record in fh:
        rank = record["rankId"]
        if rank is None and not include_unranked:
            continue
        if "Traceback (most recent call last):" in record["log"]:
            # start a new stacktrace
            stack_traces.setdefault(rank, [])
            stack_traces[rank].append([record])
        elif rank in stack_traces and (
            record["log"].startswith(f"[rank{rank}]:  ")
            or "Exception" in record["log"]
            or "^^^^^^^^^^" in record["log"]
        ):
            stack_traces[rank][-1].append(record)

    unique_traces = {}
    for rank, st in stack_traces.items():
        for trace in st:
            trace_str = "".join([r["log"] for r in trace])
            md5_lines = [
                line.replace(f"[rank{rank}]:", "")
                for line in trace_str.splitlines()[:-1]
            ]
            md5_str = "\n".join(md5_lines[:-1])
            trace_md5 = hashlib.md5(md5_str.encode("utf-8")).hexdigest()
            unique_traces.setdefault(
                trace_md5,
                {
                    "trace": md5_str,
                    "full": trace_str,
                    "ranks": [],
                },
            )
            unique_traces[trace_md5]["ranks"].append(
                (trace[0]["timestamp"], rank, trace[0]["containerId"])
            )

    print(f"Found {len(unique_traces)} unique traces")
    for md5, trace in unique_traces.items():
        trace["ranks"].sort()

    sorted_traces = [(t["ranks"][0][0], k) for k, t in unique_traces.items()]
    sorted_traces.sort()
    for _, md5 in sorted_traces:
        trace = unique_traces[md5]
        if write_traces:
            with Path(md5 + ".out").open("w") as f:
                f.write(trace["trace"])
        print(f"Trace {md5} occurred {len(trace['ranks'])} times")
        for timestamp, rank, container in sorted(trace["ranks"], key=lambda x: x[1]):
            print(f"  [rank{rank}] {timestamp} {container}")
        print(trace["full"])


if __name__ == "__main__":
    args = parse_args()
    assert args.filename or args.url, "Must specify --filename or --url"
    if args.filename == "-":
        fh = json_reader(sys.stdin)
    elif args.filename is not None:
        fh = json_reader(Path(args.filename).open())
    elif args.url is not None:
        url = urllib.parse.urlparse(args.url)
        exp_id = url.path.split("/")[3]  # Should work for experiments or searches

        murl = canonicalize_master_url(f"https://{url.netloc}")
        session = authentication.login_with_cache(murl)
        trials = bindings.get_GetExperimentTrials(session, experimentId=exp_id)
        trial_id = trials.trials[0].id
        tl = bindings.get_TrialLogs(session, trialId=trial_id)

        def logs():
            for log in tl:
                yield log.to_json()

        fh = logs()
    else:
        raise ValueError("Must specify --filename or --url")

    main(fh, args.write_traces, args.include_unranked)

# ~/.bashrc: executed by bash(1) for non-login shells.
# see /usr/share/doc/bash/examples/startup-files (in the package bash-doc)
# for examples

# If not running interactively, don't do anything
case $- in
    *i*) ;;
      *) return;;
esac

# don't put duplicate lines or lines starting with space in the history.
# See bash(1) for more options
HISTCONTROL=ignoreboth

# append to the history file, don't overwrite it
shopt -s histappend

# for setting history length see HISTSIZE and HISTFILESIZE in bash(1)
HISTSIZE=1000
HISTFILESIZE=2000

# check the window size after each command and, if necessary,
# update the values of LINES and COLUMNS.
shopt -s checkwinsize

# If set, the pattern "**" used in a pathname expansion context will
# match all files and zero or more directories and subdirectories.
#shopt -s globstar

# make less more friendly for non-text input files, see lesspipe(1)
[ -x /usr/bin/lesspipe ] && eval "$(SHELL=/bin/sh lesspipe)"

# set variable identifying the chroot you work in (used in the prompt below)
if [ -z "${debian_chroot:-}" ] && [ -r /etc/debian_chroot ]; then
    debian_chroot=$(cat /etc/debian_chroot)
fi

# set a fancy prompt (non-color, unless we know we "want" color)
case "$TERM" in
    xterm-color|*-256color) color_prompt=yes;;
esac

# uncomment for a colored prompt, if the terminal has the capability; turned
# off by default to not distract the user: the focus in a terminal window
# should be on the output of commands, not on the prompt
#force_color_prompt=yes

if [ -n "$force_color_prompt" ]; then
    if [ -x /usr/bin/tput ] && tput setaf 1 >&/dev/null; then
	# We have color support; assume it's compliant with Ecma-48
	# (ISO/IEC-6429). (Lack of such support is extremely rare, and such
	# a case would tend to support setf rather than setaf.)
	color_prompt=yes
    else
	color_prompt=
    fi
fi

# enable color support of ls and also add handy aliases
if [ -x /usr/bin/dircolors ]; then
    test -r ~/.dircolors && eval "$(dircolors -b ~/.dircolors)" || eval "$(dircolors -b)"
    alias ls='ls --color=auto'
    alias tf='terraform'
    #alias dir='dir --color=auto'
    #alias vdir='vdir --color=auto'

    alias grep='grep --color=auto'
    alias fgrep='fgrep --color=auto'
    alias egrep='egrep --color=auto'
fi

# colored GCC warnings and errors
#export GCC_COLORS='error=01;31:warning=01;35:note=01;36:caret=01;32:locus=01:quote=01'

# some more ls aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'

# Add an "alert" alias for long running commands.  Use like so:
#   sleep 10; alert
alias alert='notify-send --urgency=low -i "$([ $? = 0 ] && echo terminal || echo error)" "$(history|tail -n1|sed -e '\''s/^\s*[0-9]\+\s*//;s/[;&|]\s*alert$//'\'')"'

# Alias definitions.
# You may want to put all your additions into a separate file like
# ~/.bash_aliases, instead of adding them here directly.
# See /usr/share/doc/bash-doc/examples in the bash-doc package.

if [ -f ~/.bash_aliases ]; then
    . ~/.bash_aliases
fi

# enable programmable completion features (you don't need to enable
# this, if it's already enabled in /etc/bash.bashrc and /etc/profile
# sources /etc/bash.bashrc).
if ! shopt -oq posix; then
  if [ -f /usr/share/bash-completion/bash_completion ]; then
    . /usr/share/bash-completion/bash_completion
  elif [ -f /etc/bash_completion ]; then
    . /etc/bash_completion
  fi
fi

export EDITOR=vim
export DET_MASTER=https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud
alias h='history 20'
alias j='jobs'

fixssh() {
        eval $(tmux show-env -s |grep '^SSH_')
}

#### Section managed by deploy/dev_container/launch.sh
if [[ -f "/activate_env.sh" ]]; then
  source /activate_env.sh
fi
#### END section managed by deploy/dev_container/launch.sh

dlogin () {
        echo "Configuring gcloud auth for asia-southeast1-docker.pkg.dev..."
        gcloud auth configure-docker asia-southeast1-docker.pkg.dev
        echo "Logging into au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud... "
        local PW=$(kubectl --context coreweave get --namespace tenant-augment-eng secret au-docker-reg-docker-registry-secret -o=go-template='{{ printf "%s\n" (.data.password | base64decode) }}')
        echo $PW | docker login au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud -u augment --password-stdin
        echo "Logging into 829650725646.dkr.ecr.us-west-2.amazonaws.com... "
        aws ecr get-login-password | docker login --username AWS --password-stdin 829650725646.dkr.ecr.us-west-2.amazonaws.com
}

connectsh() {
  docker exec -it myaugment-$USER-$HOSTNAME  /bin/bash
}

gtop() {
  tl=$(git rev-parse --show-toplevel)
  if [[ -n $tl ]]; then
    cd $tl
  fi
}

metastore() {
  local POSTGRES_PASSWORD=$(kubectl get secret --namespace tenant-augment-eng metastore-postgresql -o jsonpath="{.data.password}" | base64 -d)
  kubectl run metastore-postgresql-client --rm --tty -i \
    --restart='Never' --namespace tenant-augment-eng \
    --image docker.io/bitnami/postgresql:15.3.0-debian-11-r7 \
    --env="PGPASSWORD=$POSTGRES_PASSWORD" \
    --command -- psql --host metastore-postgresql \
    -U augment -d metastore -p 5432
}

#conda activate augment_env

complete -C /usr/bin/terraform terraform

source $HOME/.prompt_config
source $HOME/.auth_tokens

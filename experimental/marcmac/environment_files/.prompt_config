source $HOME/.git_prompt

# detect the cloud provider we're in
AUGMENT_DEV_CLOUD_ZONE=$(curl -sH "Metadata-Flavor:Google" metadata.google.internal/computeMetadata/v1/instance/zone | awk -F/ '{print $NF}')
if [[ -n "$AUGMENT_DEV_CLOUD_ZONE" ]]; then
    AUGMENT_DEV_CLOUD_PROVIDER="GCP"
else
    # We're assuming coreweave for now
    AUGMENT_DEV_CLOUD_PROVIDER="CW"
fi

PROMPT_COMMAND='
pwd2=$(sed "s:\([^/]\{1,2\}\)[^/]*/:\1/:g" <<<$PWD |rev|cut -d/ -f-3|rev);
h2=$(hostname | cut -b-3);
h3=$(hostname | rev | cut -b-3 | rev);
u2=$(id -un | cut -b-3);
kctxt=$(kubectl config current-context)
'

# Set various prompt colors
fg2=192
if [ -f /.dockerenv ]; then
  fg2=214
fi
case $AUGMENT_DEV_CLOUD_PROVIDER in
  "GCP")
    fg1=39
    ;;
  "CW")
    fg1=46
    ;;
  *)
    fg1=255
    fg2=255
    ;;
esac
if [ "$color_prompt" = yes ]; then
    PS1='\[\e[1m\e[38;5;${fg1}m\]${kctxt} $u2@$h2.$h3\[\e[0m\]:\[\e[1m\e[38;5;${fg2}m\]$pwd2\[\e[0m\]$(__git_ps1)\$ '
else
    PS1='${debian_chroot:+($debian_chroot)}\u@\h:\w\$ '
fi
unset color_prompt force_color_prompt

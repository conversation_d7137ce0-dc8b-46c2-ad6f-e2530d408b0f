resource "google_service_account" "determined-testing" {
  account_id   = "dt-service-account"
  display_name = "Determined service account"
}

resource "google_storage_bucket" "dt_checkpoint_test" {
  name                        = "dt-checkpoint-test"
  location                    = "asia-southeast1"
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"
}

#
# TODO figure out how this works
#
# resource "google_service_account_iam_binding" "determined-account-iam" {
#   service_account_id = google_service_account.determined-testing.name
#   role               = "roles/iam.serviceAccountUser"

#   members = [
#     "group:<EMAIL>",
#   ]
# }

# resource "google_service_account_iam_member" "determined-account-member" {
#   service_account_id = google_service_account.determined.name
#   role               = "roles/iam.serviceAccountUser"
#   member             = "group:<EMAIL>"
# }

resource "google_container_cluster" "determined-testing" {
  name     = "dt-sing"
  location = "asia-southeast1"

  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  remove_default_node_pool = true
  initial_node_count       = 1
}

# GCP node types: https://cloud.google.com/compute/docs/machine-resource
#
# For (most) GPU accelerated machines, the machine_type is determined by the
# guest_accelerator type and count.
#

resource "google_container_node_pool" "dt_default_cpu_nodes" {
  name     = "default-cpu-testing"
  location = "asia-southeast1"
  node_locations = [
    "asia-southeast1-c"
  ]
  cluster = google_container_cluster.determined-testing.name

  autoscaling {
    min_node_count = 2
    max_node_count = 10

  }
  node_config {
    preemptible  = false
    machine_type = "e2-standard-16" # 16 core 64g

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined-testing.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

resource "google_container_node_pool" "dt_mid_cpu_nodes" {
  name     = "mid-cpu"
  location = "asia-southeast1"
  node_locations = [
    "asia-southeast1-c"
  ]
  cluster = google_container_cluster.determined-testing.name

  autoscaling {
    min_node_count = 2
    max_node_count = 10

  }
  node_config {
    preemptible  = false
    machine_type = "c3-standard-88" # 88 CPU 352G

    gvnic {
      enabled = true # Required for c3 machine types
    }

    # TODO
    #
    # C3 VMs require an updated driver for the gVNIC network interface.
    # Before migrating to C3 or creating new C3 VMs, make sure that the
    # operating system image that you use has full support (includes the updated drivers).
    # These images include the updated gVNIC driver, even if the guest OS
    # shows the gve driver version as 1.0.0.
    #
    # If your C3 VM is using an operating system with limited support
    # (includes an older version of the gVNIC driver), the network bandwidth
    # for C3 VMs is limited to 100 Gbps for Tier_1 networking on the largest VM sizes.


    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined-testing.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

resource "google_container_node_pool" "dt_default_gpu_nodes" {
  name     = "default-gpu"
  location = "asia-southeast1"
  node_locations = [
    "asia-southeast1-b"
  ]
  cluster = google_container_cluster.determined-testing.name

  node_count = 2

  node_config {
    preemptible  = false
    image_type   = "cos_containerd"
    machine_type = "g2-standard-96" # 96 CPU 384G
    disk_size_gb = "3000"
    disk_type    = "pd-ssd"

    guest_accelerator {
      count = 8
      # type  = "projects/augment-387916/zones/asia-southeast1-c/acceleratorTypes/nvidia-l4"
      type = "nvidia-l4"
      gpu_driver_installation_config {
        gpu_driver_version = "LATEST"
      }
    }

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = google_service_account.determined-testing.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    metadata = {
      disable-legacy-endpoints = "true"
      ssh-keys                 = "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"
    }
  }
}

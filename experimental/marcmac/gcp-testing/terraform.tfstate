{"version": 4, "terraform_version": "1.5.5", "serial": 9, "lineage": "dac78d5f-9648-2984-f557-ed31e2ec5da2", "outputs": {}, "resources": [{"module": "module.determined-testing", "mode": "managed", "type": "google_container_cluster", "name": "determined-testing", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 2, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": true}]}], "allow_net_admin": null, "authenticator_groups_config": [], "binary_authorization": [{"enabled": false, "evaluation_mode": ""}], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "autoscaling_profile": "BALANCED", "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "*********/14", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "", "default_max_pods_per_node": 110, "default_snat_status": [{"disabled": false}], "deletion_protection": true, "description": "", "dns_config": [], "enable_autopilot": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "*************", "fleet": [], "gateway_api_config": [], "id": "projects/augment-387916/locations/asia-southeast1/clusters/dt-sing", "identity_service_config": [], "initial_node_count": 1, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "*********/14", "cluster_secondary_range_name": "gke-dt-sing-pods-fad4fc0e", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "**********/20", "services_secondary_range_name": "gke-dt-sing-services-fad4fc0e", "stack_type": "IPV4"}], "label_fingerprint": "a9dc16a7", "location": "asia-southeast1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.27.3-gke.100", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "relay_mode": ""}], "enable_components": ["SYSTEM_COMPONENTS"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "dt-sing", "network": "projects/augment-387916/global/networks/default", "network_policy": [{"enabled": false, "provider": "PROVIDER_UNSPECIFIED"}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-88", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_locations": ["asia-southeast1-a", "asia-southeast1-b", "asia-southeast1-c"], "node_pool": [{"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 2, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-dt-sing-mid-cpu-9606b8a0-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-dt-sing-mid-cpu-9606b8a0-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-88", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["asia-southeast1-c"], "placement_policy": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}, {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 2, "total_max_node_count": 0, "total_min_node_count": 0}], "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-dt-sing-default-cpu-testing-85bec8b5-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-dt-sing-default-cpu-testing-85bec8b5-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu-testing", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-c"], "placement_policy": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}, {"autoscaling": [], "initial_node_count": 2, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-dt-sing-default-gpu-2f15c2e2-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-dt-sing-default-gpu-2f15c2e2-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 2, "node_locations": ["asia-southeast1-b"], "placement_policy": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}], "node_pool_auto_config": [], "node_pool_defaults": [{"node_config_defaults": [{"logging_variant": "DEFAULT"}]}], "node_version": "1.27.3-gke.100", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "***********", "private_endpoint_subnetwork": "", "public_endpoint": "*************"}], "private_ipv6_google_access": "", "project": "augment-387916", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": true, "resource_labels": {}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/augment-387916/locations/asia-southeast1/clusters/dt-sing", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "**********/20", "subnetwork": "projects/augment-387916/regions/asia-southeast1/subnetworks/default", "timeouts": null, "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNDAwMDAwMDAwMDAwLCJkZWxldGUiOjI0MDAwMDAwMDAwMDAsInJlYWQiOjI0MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMiJ9"}]}, {"module": "module.determined-testing", "mode": "managed", "type": "google_container_node_pool", "name": "dt_default_cpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 2, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "dt-sing", "id": "projects/augment-387916/locations/asia-southeast1/clusters/dt-sing/nodePools/default-cpu-testing", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-dt-sing-default-cpu-testing-85bec8b5-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-dt-sing-default-cpu-testing-85bec8b5-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-cpu-testing", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-16", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 1, "node_locations": ["asia-southeast1-c"], "operation": null, "placement_policy": [], "project": "augment-387916", "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.determined-testing.google_container_cluster.determined-testing", "module.determined-testing.google_service_account.determined-testing"]}]}, {"module": "module.determined-testing", "mode": "managed", "type": "google_container_node_pool", "name": "dt_default_gpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoscaling": [], "cluster": "dt-sing", "id": "projects/augment-387916/locations/asia-southeast1/clusters/dt-sing/nodePools/default-gpu", "initial_node_count": 2, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroupManagers/gke-dt-sing-default-gpu-2f15c2e2-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-b/instanceGroups/gke-dt-sing-default-gpu-2f15c2e2-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "default-gpu", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 3000, "disk_type": "pd-ssd", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [{"count": 8, "gpu_driver_installation_config": [{"gpu_driver_version": "LATEST"}], "gpu_partition_size": "", "gpu_sharing_config": [], "type": "nvidia-l4"}], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "g2-standard-96", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 2, "node_locations": ["asia-southeast1-b"], "operation": null, "placement_policy": [], "project": "augment-387916", "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.determined-testing.google_container_cluster.determined-testing", "module.determined-testing.google_service_account.determined-testing"]}]}, {"module": "module.determined-testing", "mode": "managed", "type": "google_container_node_pool", "name": "dt_mid_cpu_nodes", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoscaling": [{"location_policy": "BALANCED", "max_node_count": 10, "min_node_count": 2, "total_max_node_count": 0, "total_min_node_count": 0}], "cluster": "dt-sing", "id": "projects/augment-387916/locations/asia-southeast1/clusters/dt-sing/nodePools/mid-cpu", "initial_node_count": 0, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroupManagers/gke-dt-sing-mid-cpu-9606b8a0-grp"], "location": "asia-southeast1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/augment-387916/zones/asia-southeast1-c/instanceGroups/gke-dt-sing-mid-cpu-9606b8a0-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "mid-cpu", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-dt-sing-pods-fad4fc0e"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [{"enabled": true}], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "c3-standard-88", "metadata": {"disable-legacy-endpoints": "true", "ssh-keys": "augment:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC3nm53fgKnGpkyoFfh2XRwPIU8Jcg4Z5CUFTIYpp7ptAWu+Si4QESUSCqWA82F0qC1kQuhOk+a62jwRx6X4VtU61qScIe6epsRU2Amgmmi+hf8u11gvccGkaq8d1sL3VSN/ykn4PJsJql/JOvdB0X5fwlzrhxLIXqCcjcMN81B9DmzSpAHNrbYw/i8gkQCem62tVjgC1OvhJvTJs5NO7Vho3SnDGB8GW3TvQXXH7RomXB+8wKcoAQgwrC+lk4tow27ZCugD9MdnQiEBspISg3mdjFEG02zbe7PtEHBzxc0SRUpkxrSelMax7CaX8qId0nvBd8blNAVJ1HZ8dLn8csbL1qTKqA6Y1Wy+TW40nA0H6rjFGn0/uAaGFxW+PjleFi8f1DbpEb56hhWG5H9vlvm9FZBTMXqV0QNiaSjH0XzJe6jFDqhbdb+uKLxJfjpo4IW2MP25Hizrcb2ge6khmSklf8+XhobF/M+cZ5rY6dFXCq9pWQaHLyjdYtbqvVUbi0= augment@marcmac-dev-2004"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": false, "reservation_affinity": [], "resource_labels": {}, "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": []}], "node_count": 0, "node_locations": ["asia-southeast1-c"], "operation": null, "placement_policy": [], "project": "augment-387916", "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.27.3-gke.100"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.determined-testing.google_container_cluster.determined-testing", "module.determined-testing.google_service_account.determined-testing"]}]}, {"module": "module.determined-testing", "mode": "managed", "type": "google_service_account", "name": "determined-testing", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "dt-service-account", "description": "", "disabled": false, "display_name": "Determined service account", "email": "<EMAIL>", "id": "projects/augment-387916/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/augment-387916/serviceAccounts/<EMAIL>", "project": "augment-387916", "timeouts": null, "unique_id": "112137356109625782891"}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"module": "module.determined-testing", "mode": "managed", "type": "google_storage_bucket", "name": "dt_checkpoint_test", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {}, "enable_object_retention": false, "encryption": [], "force_destroy": false, "id": "dt-checkpoint-test", "labels": null, "lifecycle_rule": [], "location": "ASIA-SOUTHEAST1", "logging": [], "name": "dt-checkpoint-test", "project": "augment-387916", "public_access_prevention": "enforced", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/dt-checkpoint-test", "storage_class": "STANDARD", "terraform_labels": {}, "timeouts": null, "uniform_bucket_level_access": true, "url": "gs://dt-checkpoint-test", "versioning": [], "website": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsInJlYWQiOjI0MDAwMDAwMDAwMCwidXBkYXRlIjoyNDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets import completion, tenants\n", "from google.cloud import bigquery, storage  # type: ignore\n", "from base.datasets.gcs_blob_cache import GCSBlobCache\n", "\n", "\n", "TENANT_NAME = \"dogfood\"\n", "\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "\n", "gcp_creds, _ = get_gcp_creds(None)\n", "storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "blob_bucket = storage_client.bucket(tenant.blob_bucket_name)\n", "blob_cache = GCSBlobCache(\n", "    blob_bucket,\n", "    tenant.blob_bucket_prefix,\n", "    2**30,\n", "    num_threads=32,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "NUM_BLOBS = 30_000\n", "blob_names = []\n", "\n", "for blob in tqdm.tqdm(blob_bucket.list_blobs(prefix=tenant.blob_bucket_prefix)):\n", "    blob_names.append(blob.name.lstrip(tenant.blob_bucket_prefix + \"/\"))\n", "    if len(blob_names) >= NUM_BLOBS:\n", "        break"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["1875it [06:24,  4.88it/s]\n"]}], "source": ["import tqdm\n", "import more_itertools\n", "from base.augment_client.client import AugmentClient, UploadContent\n", "\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "    # url=\"https://dogfood.api.augmentcode.com\",\n", "    token=\"B41CEAD9-B173-4A42-9AD4-1B5DDD486489\",\n", ")\n", "\n", "\n", "for batch in tqdm.tqdm(more_itertools.chunked(blob_names, 16)):\n", "    rst = blob_cache.get(batch)\n", "    client.batch_upload(\n", "        [\n", "            UploadContent(content=c.content, path_name=str(c.path))\n", "            for c in rst\n", "            if c is not None\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Play with Retrieval Database and Dense Retriever"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads A Retriever"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from augment.research.eval.harness.factories import create_retriever\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["retriever_config = {\n", "    \"scorer\": {\n", "        # Change checkpoint to change models.\n", "        \"name\": \"diff_boykin\",\n", "        'checkpoint': 'ethanol_plus/6pos_32total_v0.1',\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 40,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"simple_query\",\n", "        \"max_lines\": 20,\n", "    },\n", "}\n", "retrieval_database = create_retriever(retriever_config)\n", "dense_scorer = retrieval_database.scorer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dense_scorer.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dense_scorer.unload()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1024, 1)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["emb, toks = dense_scorer._query_embedding(ModelInput('hello'))\n", "emb.shape"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import download_hydra\n", "from research.retrieval import chunking_functions\n", "import tqdm\n", "import numpy as np\n", "\n", "def add_files_to_scorer(scorer):\n", "    repo_id = ('deepmind', 'tracr')\n", "\n", "    (\n", "        _,\n", "        _,\n", "        files_for_retrieval_by_repo,\n", "    ) = download_hydra(\n", "        \"deepmind\", \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines\"\n", "    )\n", "\n", "    chunker = chunking_functions.LineLevelChunker(max_lines_per_chunk=20)\n", "    id2chunks = {}\n", "    id2docs = {}\n", "    chunks = {}\n", "    for doc in files_for_retrieval_by_repo[repo_id]:\n", "        id2docs[doc.id] = doc\n", "        if chunks_ls := chunker.split_into_chunks(doc):\n", "            chunks[doc.id] = chunks_ls\n", "        if chunks_ls:\n", "            for c in chunks_ls:\n", "                id2chunks[c.id] = c\n", "\n", "    for c in tqdm.tqdm(chunks.values()):\n", "        scorer.add_doc(c)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 73/73 [00:13<00:00,  5.40it/s]\n"]}], "source": ["add_files_to_scorer(dense_scorer)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["_, doc_embs = dense_scorer._get_view_of_embeddings()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2239, 1024)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["doc_embs.shape"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18480.0\n", "150.2\n", "143.5\n", "0.8574\n"]}], "source": ["id1 = random.randint(0, 2239)\n", "id2 = random.randint(0, 2239)\n", "print(np.dot(doc_embs[id1, :], doc_embs[id2, :]))\n", "\n", "print(np.linalg.norm(doc_embs[id1, :]))\n", "print(np.linalg.norm(doc_embs[id2, :]))\n", "print(np.dot(doc_embs[id1, :]/np.linalg.norm(doc_embs[id1, :]), doc_embs[id2, :]/np.linalg.norm(doc_embs[id2, :])))\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["147.4"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(doc_embs[id1, :])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1024,)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["doc_embs[id1, :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.dot"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["1672"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["id2"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from termcolor import colored\n", "from research.models.model_server import add_files_to_index\n", "from research.models.codegen_models import (\n", "    CodeGen_350M_Multi,\n", "    CodeGen_2B_Multi,\n", "    CodeGen_16B_Multi,\n", "    CodeGen_16B_Indiana,\n", ")\n", "from research.models.starcoder_models import (\n", "    StarCoder,\n", ")\n", "from research.models import GenerationOptions\n", "\n", "from research.retrieval.types import Document, Chunk\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import (\n", "    ScopeAwareChunker,\n", "    LineLevelChunker,\n", ")\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "from research.retrieval.chunking_functions import (\n", "    LineLevelChunker,\n", "    ScopeAwareChunker,\n", ")\n", "from research.retrieval.file_filterer import basic_file_filterer\n", "from research.retrieval.scorers.dense_scorer import (\n", "    Contrieve_350M_16k_Scorer,\n", ")\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "\n", "\n", "print(\"Loading the model...\")\n", "model = CodeGen_350M_Multi()\n", "\n", "tokenizer = CodeGenTokenizer()\n", "scorer = Contrieve_350M_16k_Scorer(max_query_tokens=5)\n", "scorer.load()\n", "scorer_bm25 = GoodEnoughBM25Scorer(max_query_tokens=5)\n", "chunker = LineLevelChunker(10)\n", "file_filterer = basic_file_filterer\n", "\n", "doc_index = RetrievalDatabase(chunker, scorer, file_filterer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the model\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "from research.models import StopCriteria\n", "\n", "result = model.generate(\n", "    ModelInput(\"def hello_world():\\n\"),\n", "    GenerationOptions(\n", "        temperature=0.0,\n", "        max_generated_tokens=120,\n", "        stop_criteria=StopCriteria(stop_texts=[\"\\n\"], check_stopping_condition_every=4),\n", "    ),\n", ")\n", "print(colored(result, \"blue\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Generation and retrieval using a supplied corpus"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "\n", "docs = [Document(id=\"1\", text=\"magic_number = 1231\", path=\"a_document.py\")]\n", "doc_index.add_docs(docs)\n", "\n", "# Create a prompt\n", "prompt = dedent(\n", "    \"\"\"\n", "    def print_constant():\n", "        '''Print the magic number defined above.'''\"\"\"\n", ")\n", "\n", "print(\"====== Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "# Retrieve\n", "print(\"\\n====== Retrieved chunks:\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "for chunk in chunks:\n", "    print(chunk)\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=chunks),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=128),\n", ")\n", "print(\"\\n======= Generated text:\")\n", "print(colored(generated_text, \"green\"))\n", "\n", "doc_index.remove_docs([\"1\"])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Generation and examples with retrieval from our repository"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "print(\"Clearing and repopulating the dense retrieval index...\")\n", "doc_index.remove_all_docs()\n", "\n", "repo_root = os.path.join(os.environ[\"HOME\"], \"src\", \"augment\")\n", "print(f'The repo root: {repo_root}')\n", "\n", "add_files_to_index(doc_index, path=repo_root, extensions=[\".py\"])\n", "\n", "prompt = \"def hello_world():\"\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print(\"\\n======= Generating without retrieval:\")\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=128),\n", ")\n", "print(colored(generated_text, \"green\"))\n", "\n", "print(\"\\n======= Retrieving and generating:\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=chunks),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=128),\n", ")\n", "\n", "print(colored(generated_text, \"green\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Compute cross-entropy loss and logits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "\n", "input_text = \"The quick brown fox jumps over the lazy dog.\"\n", "loss, logits = model.log_likelihood(text=input_text)\n", "\n", "print(\"Cross-entropy loss:\", loss)\n", "print(\"Logits shape:\", logits.shape)\n", "print(\"Logits:\\n\", logits)\n", "\n", "# Make sure the logits and targets align\n", "inv_vocab = {v: k for k, v in model.tokenizer.vocab.items()}\n", "tokens = np.array(model.tokenizer.tokenize(input_text))\n", "pred_tokens = torch.argmax(torch.tensor(logits, dtype=torch.int64), dim=-1)\n", "\n", "print(\"Token alignment:\")\n", "print(tokens[1:])\n", "print(pred_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Clearing and repopulating the dense retrieval index...\")\n", "doc_index.remove_all_docs()\n", "\n", "docs = [\n", "    Document(\n", "        text=\"\"\"def forward(turtle):\n", "    turtle.x += turtle.dx\n", "    turtle.y += turtle.dy\n", "\n", "\n", "def backward(turtle):\n", "    turtle.x -= turtle.dx\n", "    turtle.y -= turtle.dy\n", "\n", "\n", "@dataclass\n", "class Turtle:\n", "    x: float = 0.0\n", "    y: float = 0.0\n", "    dx: float = 1.0\n", "    dy: float = 0.0\n", "\n", "\n", "def turn_right(turtle):\n", "    turtle.dx, turtle.dy = turtle.dy, -turtle.dx\n", "\n", "\n", "def turn_left(turtle):\n", "    turtle.dx, turtle.dy = -turtle.dy, turtle.dx\n", "\n", "\n", "def draw_square(turtle):\n", "    for i in range(4):\n", "        forward(turtle)\n", "        turn_right(turtle)\n", "\"\"\",\n", "        id=\"file_uuid\",\n", "        path=\"a_document.py\",\n", "    )\n", "]\n", "doc_index.add_docs(docs)\n", "\n", "# Create a prompt\n", "prompt = dedent(\n", "    \"\"\"\n", "    def test_turn_right():\n", "        '''Test that the turn_right method for a Turtle object works correctly.'''\n", "\"\"\"\n", ")\n", "\n", "print(\"====== Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "# Retrieve\n", "print(\"\\n====== Retrieved chunks:\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=6)\n", "for i, chunk in enumerate(chunks):\n", "    print(f\"--- CHUNK {i} with score {scores[i]} ---\")\n", "    print(chunk.text)\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=chunks),\n", "    GenerationOptions(temperature=0, max_generated_tokens=200),\n", ")\n", "print(\"\\n======= Generated text with retrieval:\")\n", "print(prompt, colored(generated_text, \"green\"))\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(temperature=0, max_generated_tokens=200),\n", ")\n", "print(\"\\n======= Generated text WITHOUT retrieval:\")\n", "print(prompt, colored(generated_text, \"green\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Clearing and repopulating the dense retrieval index...\")\n", "doc_index.remove_all_docs()\n", "\n", "docs = [\n", "    Document(\n", "        text=\"\"\"\n", "def forward(turtle):\n", "    turtle.x += turtle.dx\n", "    turtle.y += turtle.dy\n", "\n", "\n", "def backward(turtle):\n", "    turtle.x -= turtle.dx\n", "    turtle.y -= turtle.dy\n", "\n", "\n", "def turn_right(turtle):\n", "    turtle.dx, turtle.dy = turtle.dy, -turtle.dx\n", "\n", "\n", "def turn_left(turtle):\n", "    turtle.dx, turtle.dy = -turtle.dy, turtle.dx\n", "\n", "\n", "def draw_square(turtle):\n", "    for i in range(4):\n", "        forward(turtle)\n", "        turn_right(turtle)\n", "\"\"\",\n", "        id=\"file_uuid\",\n", "        path=\"a_document.py\",\n", "    )\n", "]\n", "doc_index.add_docs(docs)\n", "\n", "# Create a prompt\n", "prompt = dedent(\n", "    \"\"\"\n", "@dataclass\n", "class Turtle:\n", "    x: float = 0.0\n", "    y: float = 0.0\n", "    dx: float = 1.0\n", "    dy: float = 0.0\n", "\n", "    \n", "def test_turn_right():\n", "    '''Test that the turn_right function for a Turtle object works correctly.'''\n", "\"\"\"\n", ")\n", "\n", "print(\"====== Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "# Retrieve\n", "print(\"\\n====== Retrieved chunks:\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=5)\n", "for i, chunk in enumerate(chunks):\n", "    print(f\"--- CHUNK {i} ---\")\n", "    print(chunk.text)\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=chunks[:3]),\n", "    GenerationOptions(temperature=0, max_generated_tokens=100),\n", ")\n", "print(\"\\n======= Generated text with retrieval:\")\n", "print(prompt, colored(generated_text, \"green\"))\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(temperature=0, max_generated_tokens=100),\n", ")\n", "print(\"\\n======= Generated text WITHOUT retrieval:\")\n", "print(prompt, colored(generated_text, \"green\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ResearchSignatureIndex"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.eval.harness.systems import research_signature_index\n", "\n", "database = research_signature_index.ResearchSignatureIndex()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import download_hydra\n", "from research.retrieval.libraries import chunking_functions\n", "import tqdm\n", "import numpy as np\n", "\n", "repo_id = ('deepmind', 'tracr')\n", "(\n", "    _,\n", "    _,\n", "    files_for_retrieval_by_repo,\n", ") = download_hydra(\n", "    \"deepmind\", \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines\"\n", ")\n", "\n", "_ = database.add_docs(files_for_retrieval_by_repo[repo_id])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["add_docs: 100%|██████████| 73/73 [00:01<00:00, 64.63it/s]\n"]}, {"data": {"text/plain": ["[ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Install script.\"\"\"\\n\\nimport setuptools\\n\\nsetuptools.setup(\\n    name=\"tracr\",\\n    version=\"1.0.0\",\\n    url=\"https://github.com/deepmind/tracr\",\\n    author=\"DeepMind LMI team\",\\n    author_email=\"<EMAIL>\",\\n    description=\"Compiler from RASP to transformer weights\",\\n    packages=setuptools.find_packages(),\\n    install_requires=[\\n        \"chex\",\\n        \"einops\",\\n        \"dm-haiku\",\\n        \"jax\",\\n        \"networkx\",\\n        \"numpy\",\\n        \"typing_extensions\",\\n    ],\\n)\\n', path=PosixPath('setup.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21bf6a30f0>, scope_tree=SrcScope(name='setup.py', kind='file', range=0:1183, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1183:1183, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21bf6a3630>, scope_tree=SrcScope(name='tracr/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Instrumented attention layer (forked from the Haiku library implementation).\\n\"\"\"\\n\\nfrom typing import Optional\\nimport warnings\\n\\nimport chex\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\n\\n\\<EMAIL>\\nclass AttentionOutput:\\n  out: jax.Array  # [..., T\\', D\\']\\n  logits: jax.Array  # [..., H, T\\', T]\\n\\n\\nclass MultiHeadAttention(hk.Module):\\n  \"\"\"Multi-headed attention (MHA) module.\\n\\n  This module is intended for attending over sequences of vectors.\\n\\n  Rough sketch:\\n  - Compute keys (K), queries (Q), and values (V) as projections of inputs.\\n  - Attention weights are computed as W = softmax(QK^T / sqrt(key_size)).\\n  - Output is another projection of WV^T.\\n\\n  For more detail, see the original Transformer paper:\\n    \"Attention is all you need\" https://arxiv.org/abs/1706.03762.\\n\\n  Glossary of shapes:\\n  - T: Sequence length.\\n  - D: Vector (embedding) size.\\n  - H: Number of attention heads.\\n  \"\"\"\\n\\n  def __init__(\\n      self,\\n      num_heads: int,\\n      key_size: int,\\n      # TODO(b/240019186): Remove `w_init_scale`.\\n      w_init_scale: Optional[float] = None,\\n      *,\\n      w_init: Optional[hk.initializers.Initializer] = None,\\n      value_size: Optional[int] = None,\\n      model_size: Optional[int] = None,\\n      name: Optional[str] = None,\\n  ):\\n    \"\"\"Initialises the module.\\n\\n    Args:\\n      num_heads: Number of independent attention heads (H).\\n      key_size: The size of keys (K) and queries used for attention.\\n      w_init_scale: DEPRECATED. Please use w_init instead.\\n      w_init: Initialiser for weights in the linear map.\\n      value_size: Optional size of the value projection (V). If None, defaults\\n        to the key size (K).\\n      model_size: Optional size of the output embedding (D\\'). If None, defaults\\n        to the key size multiplied by the number of heads (K * H).\\n      name: Optional name for this module.\\n    \"\"\"\\n    super().__init__(name=name)\\n    self.num_heads = num_heads\\n    self.key_size = key_size\\n    self.value_size = value_size or key_size\\n    self.model_size = model_size or key_size * num_heads\\n\\n    # Backwards-compatibility for w_init_scale.\\n    if w_init_scale is not None:\\n      warnings.warn(\\n          \"w_init_scale is deprecated; please pass an explicit weight \"\\n          \"initialiser instead.\", DeprecationWarning)\\n    if w_init and w_init_scale:\\n      raise ValueError(\"Please provide only `w_init`, not `w_init_scale`.\")\\n    if w_init is None and w_init_scale is None:\\n      raise ValueError(\"Please provide a weight initializer: `w_init`.\")\\n    if w_init is None:\\n      w_init = hk.initializers.VarianceScaling(w_init_scale)\\n    self.w_init = w_init\\n\\n  def __call__(\\n      self,\\n      query: jnp.ndarray,\\n      key: jnp.ndarray,\\n      value: jnp.ndarray,\\n      mask: Optional[jnp.ndarray] = None,\\n  ) -> AttentionOutput:\\n    \"\"\"Computes (optionally masked) MHA with queries, keys & values.\\n\\n    This module broadcasts over zero or more \\'batch-like\\' leading dimensions.\\n\\n    Args:\\n      query: Embeddings sequence used to compute queries; shape [..., T\\', D_q].\\n      key: Embeddings sequence used to compute keys; shape [..., T, D_k].\\n      value: Embeddings sequence used to compute values; shape [..., T, D_v].\\n      mask: Optional mask applied to attention weights; shape [..., H=1, T\\', T].\\n\\n    Returns:\\n      A new sequence of embeddings, consisting of a projection of the\\n        attention-weighted value projections; shape [..., T\\', D\\'].\\n    \"\"\"\\n\\n    # In shape hints below, we suppress the leading dims [...] for brevity.\\n    # Hence e.g. [A, B] should be read in every case as [..., A, B].\\n    *leading_dims, sequence_length, _ = query.shape\\n    projection = self._linear_projection\\n\\n    # Compute key/query/values (overload K/Q/V to denote the respective sizes).\\n    query_heads = projection(query, self.key_size, \"query\")  # [T\\', H, Q=K]\\n    key_heads = projection(key, self.key_size, \"key\")  # [T, H, K]\\n    value_heads = projection(value, self.value_size, \"value\")  # [T, H, V]\\n\\n    # Compute attention weights.\\n    attn_logits = jnp.einsum(\"...thd,...Thd->...htT\", query_heads, key_heads)\\n    attn_logits = attn_logits / np.sqrt(self.key_size).astype(key.dtype)\\n    if mask is not None:\\n      if mask.ndim != attn_logits.ndim:\\n        raise ValueError(\\n            f\"Mask dimensionality {mask.ndim} must match logits dimensionality \"\\n            f\"{attn_logits.ndim}.\")\\n      attn_logits = jnp.where(mask, attn_logits, -1e30)\\n    attn_weights = jax.nn.softmax(attn_logits)  # [H, T\\', T]\\n\\n    # Weight the values by the attention and flatten the head vectors.\\n    attn = jnp.einsum(\"...htT,...Thd->...thd\", attn_weights, value_heads)\\n    attn = jnp.reshape(attn, (*leading_dims, sequence_length, -1))  # [T\\', H*V]\\n\\n    # Apply another projection to get the final embeddings.\\n    final_projection = hk.Linear(self.model_size, w_init=self.w_init)\\n    return AttentionOutput(\\n        out=final_projection(attn),\\n        logits=attn_logits,\\n    )\\n\\n  @hk.transparent\\n  def _linear_projection(\\n      self,\\n      x: jnp.ndarray,\\n      head_size: int,\\n      name: Optional[str] = None,\\n  ) -> jnp.ndarray:\\n    y = hk.Linear(self.num_heads * head_size, w_init=self.w_init, name=name)(x)\\n    *leading_dims, _ = x.shape\\n    return y.reshape((*leading_dims, self.num_heads, head_size))\\n', path=PosixPath('tracr/transformer/attention.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21bf6a39f0>, scope_tree=SrcScope(name='tracr/transformer/attention.py', kind='file', range=0:5968, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5968:5968, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/transformer/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8957390>, scope_tree=SrcScope(name='tracr/transformer/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Modified transformer to learn a linear compression of the residual stream.\\n\\nCompressedTransformer adds three arguments compared to Transformer:\\n- embedding_size: the size of the compressed residual stream.\\n- unembed_at_every_layer: whether to apply the unembedding before applying\\n    attention and MLP layers\\n- return_activations: whether to return all model activations rather than just\\n    the outputs\\n\"\"\"\\n\\nimport collections\\nimport dataclasses\\nfrom typing import Optional\\n\\nimport haiku as hk\\nimport jax\\nimport numpy as np\\n\\nfrom tracr.transformer import attention\\nfrom tracr.transformer import model\\n\\n\\<EMAIL>\\nclass CompressedTransformer(hk.Module):\\n  \"\"\"A transformer stack with linearly compressed residual stream.\"\"\"\\n\\n  config: model.TransformerConfig\\n  name: Optional[str] = None\\n\\n  def __call__(\\n      self,\\n      embeddings: jax.Array,  # [B, T, D]\\n      mask: jax.Array,  # [B, T]\\n      *,\\n      use_dropout: bool = True,\\n      embedding_size: Optional[int] = None,\\n      unembed_at_every_layer: bool = False,\\n  ) -> model.TransformerOutput:  # [B, T, D]\\n    \"\"\"Transforms input embedding sequences to output embedding sequences.\\n\\n    Args:\\n      embeddings: Input embeddings to pass through the model.\\n      mask: Boolean mask to restrict the inputs the model uses.\\n      use_dropout: Turns dropout on/off.\\n      embedding_size: Dimension to compress the residual stream to.\\n      unembed_at_every_layer: Whether to unembed the residual stream when\\n        reading the input for every layer (keeping the layer input sizes) or to\\n        only unembed before the model output (compressing the layer inputs).\\n\\n    Returns:\\n      The outputs of the forward pass through the transformer.\\n    \"\"\"\\n\\n    def layer_norm(x: jax.Array) -> jax.Array:\\n      \"\"\"Applies a unique LayerNorm to x with default settings.\"\"\"\\n      if self.config.layer_norm:\\n        return hk.LayerNorm(axis=-1, create_scale=True, create_offset=True)(x)\\n      return x\\n\\n    initializer = hk.initializers.VarianceScaling(2 / self.config.num_layers)\\n    dropout_rate = self.config.dropout_rate if use_dropout else 0.\\n    _, seq_len, model_size = embeddings.shape\\n\\n    # To compress the model, we multiply with a matrix W when reading from\\n    # the residual stream, and with W^T when writing to the residual stream.\\n    if embedding_size is not None:\\n      # [to_size, from_size]\\n      w_emb = hk.get_parameter(\\n          \"w_emb\", (embedding_size, model_size),\\n          init=hk.initializers.RandomNormal())\\n\\n      write_to_residual = lambda x: x @ w_emb.T\\n      read_from_residual = lambda x: x @ w_emb\\n\\n      if not unembed_at_every_layer:\\n        model_size = embedding_size\\n    else:\\n      write_to_residual = lambda x: x\\n      read_from_residual = lambda x: x\\n\\n    # Compute causal mask for autoregressive sequence modelling.\\n    mask = mask[:, None, None, :]  # [B, H=1, T\\'=1, T]\\n    mask = mask.repeat(seq_len, axis=2)  # [B, H=1, T, T]\\n\\n    if self.config.causal:\\n      causal_mask = np.ones((1, 1, seq_len, seq_len))  # [B=1, H=1, T, T]\\n      causal_mask = np.tril(causal_mask)\\n      mask = mask * causal_mask  # [B, H=1, T, T]\\n\\n    # Set up activation collection.\\n    collected = collections.defaultdict(list)\\n\\n    def collect(**kwargs):\\n      for k, v in kwargs.items():\\n        collected[k].append(v)\\n\\n    residual = write_to_residual(embeddings)\\n\\n    for layer in range(self.config.num_layers):\\n      with hk.experimental.name_scope(f\"layer_{layer}\"):\\n        # First the attention block.\\n        attn_block = attention.MultiHeadAttention(\\n            num_heads=self.config.num_heads,\\n            key_size=self.config.key_size,\\n            model_size=model_size,\\n            w_init=initializer,\\n            name=\"attn\")\\n\\n        attn_in = residual\\n        if unembed_at_every_layer:\\n          attn_in = read_from_residual(attn_in)\\n        attn_in = layer_norm(attn_in)\\n        attn_out = attn_block(attn_in, attn_in, attn_in, mask=mask)\\n        attn_out, attn_logits = attn_out.out, attn_out.logits\\n        if dropout_rate > 0:\\n          attn_out = hk.dropout(hk.next_rng_key(), dropout_rate, attn_out)\\n\\n        if unembed_at_every_layer:\\n          collect(layer_outputs=attn_out, attn_logits=attn_logits)\\n        else:\\n          collect(\\n              layer_outputs=read_from_residual(attn_out),\\n              attn_logits=attn_logits,\\n          )\\n\\n        if unembed_at_every_layer:\\n          attn_out = write_to_residual(attn_out)\\n        residual = residual + attn_out\\n\\n        collect(residuals=residual)\\n\\n        # Then the dense block.\\n        with hk.experimental.name_scope(\"mlp\"):\\n          dense_block = hk.Sequential([\\n              hk.Linear(\\n                  self.config.mlp_hidden_size,\\n                  w_init=initializer,\\n                  name=\"linear_1\"),\\n              self.config.activation_function,\\n              hk.Linear(model_size, w_init=initializer, name=\"linear_2\"),\\n          ])\\n\\n        dense_in = residual\\n        if unembed_at_every_layer:\\n          dense_in = read_from_residual(dense_in)\\n        dense_in = layer_norm(dense_in)\\n        dense_out = dense_block(dense_in)\\n        if dropout_rate > 0:\\n          dense_out = hk.dropout(hk.next_rng_key(), dropout_rate, dense_out)\\n\\n        if unembed_at_every_layer:\\n          collect(layer_outputs=dense_out)\\n        else:\\n          collect(layer_outputs=read_from_residual(dense_out))\\n\\n        if unembed_at_every_layer:\\n          dense_out = write_to_residual(dense_out)\\n        residual = residual + dense_out\\n\\n        collect(residuals=residual)\\n\\n    output = read_from_residual(residual)\\n    output = layer_norm(output)\\n\\n    return model.TransformerOutput(\\n        layer_outputs=collected[\"layer_outputs\"],\\n        residuals=collected[\"residuals\"],\\n        attn_logits=collected[\"attn_logits\"],\\n        output=output,\\n        input_embeddings=embeddings,\\n    )\\n', path=PosixPath('tracr/transformer/compressed_model.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b89575d0>, scope_tree=SrcScope(name='tracr/transformer/compressed_model.py', kind='file', range=0:6548, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6548:6548, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for transformer.encoder.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.craft import bases\\nfrom tracr.transformer import encoder\\n\\n_BOS_TOKEN = \"bos_encoder_test\"\\n_PAD_TOKEN = \"pad_encoder_test\"\\n\\n\\nclass CategoricalEncoderTest(parameterized.TestCase):\\n\\n  def test_encode_raises_value_error_if_input_doesnt_start_with_bos(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", {1, 2, 3, _BOS_TOKEN})\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN)\\n    with self.assertRaisesRegex(ValueError,\\n                                r\"^.*First input token must be BOS token.*$\"):\\n      basic_encoder.encode([1, 1, 1])\\n\\n  def test_encode_raises_value_error_if_input_not_in_vocab(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", {1, 2, 3, _BOS_TOKEN})\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN)\\n    with self.assertRaisesRegex(ValueError,\\n                                r\"^.*Inputs .* not found in encoding.*$\"):\\n      basic_encoder.encode([_BOS_TOKEN, 1, 2, 3, 4])\\n\\n  def test_decode_raises_value_error_if_id_outside_of_vocab_size(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", {1, 2, _BOS_TOKEN})\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN)\\n    with self.assertRaisesRegex(ValueError,\\n                                r\"^.*Inputs .* not found in decoding map.*$\"):\\n      basic_encoder.decode([0, 1, 2, 3])\\n\\n  def test_encoder_raises_value_error_if_bos_not_in_basis(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", {1, 2, 3})\\n    with self.assertRaisesRegex(ValueError,\\n                                r\"^.*BOS token missing in encoding.*$\"):\\n      unused_basic_encoder = encoder.CategoricalEncoder(\\n          vs.basis, bos_token=_BOS_TOKEN)\\n\\n  def test_encoder_raises_value_error_if_pad_not_in_basis(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", {1, 2, 3})\\n    with self.assertRaisesRegex(ValueError,\\n                                r\"^.*PAD token missing in encoding.*$\"):\\n      unused_basic_encoder = encoder.CategoricalEncoder(\\n          vs.basis, pad_token=_PAD_TOKEN)\\n\\n  def test_encoder_encodes_bos_and_pad_tokens_as_expected(self):\\n    vs = bases.VectorSpaceWithBasis.from_values(\\n        \"input\", {1, 2, 3, _BOS_TOKEN, _PAD_TOKEN})\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, bos_token=_BOS_TOKEN, pad_token=_PAD_TOKEN)\\n    self.assertEqual(\\n        basic_encoder.encode([_BOS_TOKEN, _PAD_TOKEN]),\\n        [basic_encoder.bos_encoding, basic_encoder.pad_encoding])\\n\\n  @parameterized.parameters([\\n      dict(\\n          vocab={1, 2, 3, _BOS_TOKEN},  # lexicographic order\\n          inputs=[_BOS_TOKEN, 3, 2, 1],\\n          expected=[3, 2, 1, 0]),\\n      dict(\\n          vocab={\"a\", \"b\", _BOS_TOKEN, \"c\"},  # lexicographic order\\n          inputs=[_BOS_TOKEN, \"b\", \"b\", \"c\"],\\n          expected=[2, 1, 1, 3]),\\n  ])\\n  def test_tokens_are_encoded_in_lexicographic_order(self, vocab, inputs,\\n                                                     expected):\\n    # Expect encodings to be assigned to ids according to a lexicographic\\n    # ordering of the vocab\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN)\\n    encodings = basic_encoder.encode(inputs)\\n    self.assertEqual(encodings, expected)\\n\\n  @parameterized.parameters([\\n      dict(vocab={_BOS_TOKEN, _PAD_TOKEN, 1, 2, 3}, expected=5),\\n      dict(vocab={_BOS_TOKEN, _PAD_TOKEN, \"a\", \"b\"}, expected=4),\\n  ])\\n  def test_vocab_size_has_expected_value(self, vocab, expected):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN, pad_token=_PAD_TOKEN)\\n    self.assertEqual(basic_encoder.vocab_size, expected)\\n\\n  @parameterized.parameters([\\n      dict(\\n          vocab={_BOS_TOKEN, _PAD_TOKEN, 1, 2, 3}, inputs=[_BOS_TOKEN, 3, 2,\\n                                                           1]),\\n      dict(\\n          vocab={_BOS_TOKEN, _PAD_TOKEN, \"a\", \"b\", \"c\"},\\n          inputs=[_BOS_TOKEN, \"b\", \"b\", \"c\"]),\\n  ])\\n  def test_decode_inverts_encode(self, vocab, inputs):\\n    vs = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n    basic_encoder = encoder.CategoricalEncoder(\\n        vs.basis, enforce_bos=True, bos_token=_BOS_TOKEN, pad_token=_PAD_TOKEN)\\n    encodings = basic_encoder.encode(inputs)\\n    recovered = basic_encoder.decode(encodings)\\n    self.assertEqual(recovered, inputs)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/transformer/encoder_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b88f9130>, scope_tree=SrcScope(name='tracr/transformer/encoder_test.py', kind='file', range=0:5439, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5439:5439, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for transformer.model.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\nfrom tracr.transformer import compressed_model\\nfrom tracr.transformer import model\\n\\n\\nclass CompressedTransformerTest(parameterized.TestCase):\\n\\n  def _check_layer_naming(self, params):\\n    # Modules should be named for example\\n    # For MLPs: \"compressed_transformer/layer_{i}/mlp/linear_1\"\\n    # For Attention: \"compressed_transformer/layer_{i}/attn/key\"\\n    # For Layer Norm: \"compressed_transformer/layer_{i}/layer_norm\"\\n    for key in params.keys():\\n      levels = key.split(\"/\")\\n      self.assertEqual(levels[0], \"compressed_transformer\")\\n      if len(levels) == 1:\\n        self.assertEqual(list(params[key].keys()), [\"w_emb\"])\\n        continue\\n      if levels[1].startswith(\"layer_norm\"):\\n        continue  # output layer norm\\n      self.assertStartsWith(levels[1], \"layer\")\\n      if levels[2] == \"mlp\":\\n        self.assertIn(levels[3], {\"linear_1\", \"linear_2\"})\\n      elif levels[2] == \"attn\":\\n        self.assertIn(levels[3], {\"key\", \"query\", \"value\", \"linear\"})\\n      else:\\n        self.assertStartsWith(levels[2], \"layer_norm\")\\n\\n  def _zero_mlps(self, params):\\n    for module in params:\\n      if \"mlp\" in module:\\n        for param in params[module]:\\n          params[module][param] = jnp.zeros_like(params[module][param])\\n    return params\\n\\n  @parameterized.parameters(dict(layer_norm=True), dict(layer_norm=False))\\n  def test_layer_norm(self, layer_norm):\\n    # input = [1, 1, 1, 1]\\n    # If layer norm is used, this should give all-0 output for a freshly\\n    # initialized model because LN will subtract the mean after each layer.\\n    # Else we expect non-zero outputs.\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = compressed_model.CompressedTransformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              layer_norm=layer_norm))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = jnp.ones((1, seq_len, 1))\\n    mask = jnp.ones((1, seq_len))\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    out = forward.apply(params, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    if layer_norm:\\n      np.testing.assert_allclose(out, 0)\\n    else:\\n      self.assertFalse(np.allclose(out, 0))\\n\\n  @parameterized.parameters(dict(causal=True), dict(causal=False))\\n  def test_causal_attention(self, causal):\\n    # input = [0, random, random, random]\\n    # mask = [1, 0, 1, 1]\\n    # For causal attention the second token can only attend to the first one, so\\n    # it should be the same. For non-causal attention all tokens should change.\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = compressed_model.CompressedTransformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              layer_norm=False,\\n              causal=causal))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, 1))\\n    emb[:, 0, :] = 0\\n    mask = np.array([[1, 0, 1, 1]])\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    params = self._zero_mlps(params)\\n    out = forward.apply(params, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    if causal:\\n      self.assertEqual(0, out[0, 0, 0])\\n      self.assertEqual(emb[0, 1, 0], out[0, 1, 0])\\n    else:\\n      self.assertNotEqual(0, out[0, 0, 0])\\n      self.assertNotEqual(emb[0, 1, 0], out[0, 1, 0])\\n    self.assertNotEqual(emb[0, 2, 0], out[0, 2, 0])\\n    self.assertNotEqual(emb[0, 3, 0], out[0, 3, 0])\\n\\n  def test_setting_activation_function_to_zero(self):\\n    # An activation function that always returns zeros should result in the\\n    # same model output as setting all MLP weights to zero.\\n\\n    @hk.transform\\n    def forward_zero(emb, mask):\\n      transformer = compressed_model.CompressedTransformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jnp.zeros_like))\\n      return transformer(emb, mask).output\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = compressed_model.CompressedTransformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jax.nn.gelu))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, 1))\\n    mask = np.ones((1, seq_len))\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    params_no_mlps = self._zero_mlps(params)\\n\\n    out_zero_activation = forward_zero.apply(params, next(rng), emb, mask)\\n    out_no_mlps = forward.apply(params_no_mlps, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    np.testing.assert_allclose(out_zero_activation, out_no_mlps)\\n    self.assertFalse(np.allclose(out_zero_activation, 0))\\n\\n  def test_not_setting_embedding_size_produces_same_output_as_default_model(\\n      self):\\n    config = model.TransformerConfig(\\n        num_heads=2,\\n        num_layers=2,\\n        key_size=5,\\n        mlp_hidden_size=64,\\n        dropout_rate=0.,\\n        causal=False,\\n        layer_norm=False)\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def forward_model(emb, mask):\\n      return model.Transformer(config)(emb, mask).output\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def forward_superposition(emb, mask):\\n      return compressed_model.CompressedTransformer(config)(emb, mask).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, 1))\\n    mask = np.ones((1, seq_len))\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward_model.init(next(rng), emb, mask)\\n    params_superposition = {\\n        k.replace(\"transformer\", \"compressed_transformer\"): v\\n        for k, v in params.items()\\n    }\\n\\n    out_model = forward_model.apply(params, emb, mask)\\n    out_superposition = forward_superposition.apply(params_superposition, emb,\\n                                                    mask)\\n\\n    self._check_layer_naming(params_superposition)\\n    np.testing.assert_allclose(out_model, out_superposition)\\n\\n  @parameterized.parameters(\\n      dict(embedding_size=2, unembed_at_every_layer=True),\\n      dict(embedding_size=2, unembed_at_every_layer=False),\\n      dict(embedding_size=6, unembed_at_every_layer=True),\\n      dict(embedding_size=6, unembed_at_every_layer=False))\\n  def test_embbeding_size_produces_correct_shape_of_residuals_and_layer_outputs(\\n      self, embedding_size, unembed_at_every_layer):\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = compressed_model.CompressedTransformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False))\\n      return transformer(\\n          emb,\\n          mask,\\n          embedding_size=embedding_size,\\n          unembed_at_every_layer=unembed_at_every_layer,\\n      )\\n\\n    seq_len = 4\\n    model_size = 16\\n\\n    emb = np.random.random((1, seq_len, model_size))\\n    mask = np.ones((1, seq_len))\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    activations = forward.apply(params, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n\\n    for residual in activations.residuals:\\n      self.assertEqual(residual.shape, (1, seq_len, embedding_size))\\n\\n    for layer_output in activations.layer_outputs:\\n      self.assertEqual(layer_output.shape, (1, seq_len, model_size))\\n\\n  @parameterized.parameters(\\n      dict(model_size=2, unembed_at_every_layer=True),\\n      dict(model_size=2, unembed_at_every_layer=False),\\n      dict(model_size=6, unembed_at_every_layer=True),\\n      dict(model_size=6, unembed_at_every_layer=False))\\n  def test_identity_embedding_produces_same_output_as_standard_model(\\n      self, model_size, unembed_at_every_layer):\\n\\n    config = model.TransformerConfig(\\n        num_heads=2,\\n        num_layers=2,\\n        key_size=5,\\n        mlp_hidden_size=64,\\n        dropout_rate=0.,\\n        causal=False,\\n        layer_norm=False)\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def forward_model(emb, mask):\\n      return model.Transformer(config)(emb, mask).output\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def forward_superposition(emb, mask):\\n      return compressed_model.CompressedTransformer(config)(\\n          emb,\\n          mask,\\n          embedding_size=model_size,\\n          unembed_at_every_layer=unembed_at_every_layer).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, model_size))\\n    mask = np.ones((1, seq_len))\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward_model.init(next(rng), emb, mask)\\n    params_superposition = {\\n        k.replace(\"transformer\", \"compressed_transformer\"): v\\n        for k, v in params.items()\\n    }\\n    params_superposition[\"compressed_transformer\"] = {\\n        \"w_emb\": jnp.identity(model_size)\\n    }\\n\\n    out_model = forward_model.apply(params, emb, mask)\\n    out_superposition = forward_superposition.apply(params_superposition, emb,\\n                                                    mask)\\n\\n    self._check_layer_naming(params_superposition)\\n    np.testing.assert_allclose(out_model, out_superposition)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/transformer/compressed_model_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8923f90>, scope_tree=SrcScope(name='tracr/transformer/compressed_model_test.py', kind='file', range=0:10839, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=10839:10839, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Basic encoder for inputs with a fixed vocabulary.\"\"\"\\n\\nimport abc\\nfrom typing import Any, List, Optional, Sequence\\n\\nfrom tracr.craft import bases\\n\\n\\nclass Encoder(abc.ABC):\\n  \"\"\"Encodes a list of tokens into a list of inputs for a transformer model.\\n\\n  The abstract class does not make assumptions on the input and output types,\\n  and we have different encoders for different input types.\\n  \"\"\"\\n\\n  @abc.abstractmethod\\n  def encode(self, inputs: List[Any]) -> List[Any]:\\n    return list()\\n\\n  @abc.abstractmethod\\n  def decode(self, encodings: List[Any]) -> List[Any]:\\n    return list()\\n\\n  @property\\n  def pad_token(self) -> Optional[str]:\\n    return None\\n\\n  @property\\n  def bos_token(self) -> Optional[str]:\\n    return None\\n\\n  @property\\n  def pad_encoding(self) -> Optional[int]:\\n    return None\\n\\n  @property\\n  def bos_encoding(self) -> Optional[int]:\\n    return None\\n\\n\\nclass NumericalEncoder(Encoder):\\n  \"\"\"Encodes numerical variables (simply using the identity mapping).\"\"\"\\n\\n  def encode(self, inputs: List[float]) -> List[float]:\\n    return inputs\\n\\n  def decode(self, encodings: List[float]) -> List[float]:\\n    return encodings\\n\\n\\nclass CategoricalEncoder(Encoder):\\n  \"\"\"Encodes categorical variables with a fixed vocabulary.\"\"\"\\n\\n  def __init__(\\n      self,\\n      basis: Sequence[bases.BasisDirection],\\n      enforce_bos: bool = False,\\n      bos_token: Optional[str] = None,\\n      pad_token: Optional[str] = None,\\n      max_seq_len: Optional[int] = None,\\n  ):\\n    \"\"\"Initialises. If enforce_bos is set, ensures inputs start with it.\"\"\"\\n    if enforce_bos and not bos_token:\\n      raise ValueError(\"BOS token must be specified if enforcing BOS.\")\\n\\n    self.encoding_map = {}\\n    for i, direction in enumerate(basis):\\n      val = direction.value\\n      self.encoding_map[val] = i\\n\\n    if bos_token and bos_token not in self.encoding_map:\\n      raise ValueError(\"BOS token missing in encoding.\")\\n\\n    if pad_token and pad_token not in self.encoding_map:\\n      raise ValueError(\"PAD token missing in encoding.\")\\n\\n    self.enforce_bos = enforce_bos\\n    self._bos_token = bos_token\\n    self._pad_token = pad_token\\n    self._max_seq_len = max_seq_len\\n\\n  def encode(self, inputs: List[bases.Value]) -> List[int]:\\n    if self.enforce_bos and inputs[0] != self.bos_token:\\n      raise ValueError(\"First input token must be BOS token. \"\\n                       f\"Should be \\'{self.bos_token}\\', but was \\'{inputs[0]}\\'.\")\\n    if missing := set(inputs) - set(self.encoding_map.keys()):\\n      raise ValueError(f\"Inputs {missing} not found in encoding \",\\n                       self.encoding_map.keys())\\n    if self._max_seq_len is not None and len(inputs) > self._max_seq_len:\\n      raise ValueError(f\"inputs={inputs} are longer than the maximum \"\\n                       f\"sequence length {self._max_seq_len}\")\\n\\n    return [self.encoding_map[x] for x in inputs]\\n\\n  def decode(self, encodings: List[int]) -> List[bases.Value]:\\n    \"\"\"Recover the tokens that corresponds to `ids`. Inverse of __call__.\"\"\"\\n    decoding_map = {val: key for key, val in self.encoding_map.items()}\\n    if missing := set(encodings) - set(decoding_map.keys()):\\n      raise ValueError(f\"Inputs {missing} not found in decoding map \",\\n                       decoding_map.keys())\\n    return [decoding_map[x] for x in encodings]\\n\\n  @property\\n  def vocab_size(self) -> int:\\n    return len(self.encoding_map)\\n\\n  @property\\n  def bos_token(self) -> Optional[str]:\\n    return self._bos_token\\n\\n  @property\\n  def pad_token(self) -> Optional[str]:\\n    return self._pad_token\\n\\n  @property\\n  def bos_encoding(self) -> Optional[int]:\\n    return None if self.bos_token is None else self.encoding_map[self.bos_token]\\n\\n  @property\\n  def pad_encoding(self) -> Optional[int]:\\n    return None if self.pad_token is None else self.encoding_map[self.pad_token]\\n', path=PosixPath('tracr/transformer/encoder.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b88d68d0>, scope_tree=SrcScope(name='tracr/transformer/encoder.py', kind='file', range=0:4489, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=4489:4489, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Didactic example of an autoregressive Transformer-based language model.\\n\\nGlossary of shapes:\\n- B: Batch size.\\n- T: Sequence length.\\n- D: Model embedding size.\\n- H: Number of attention heads.\\n- V: Vocabulary size.\\n\\nForked from: haiku.examples.transformer.model\\n\"\"\"\\n\\nimport collections\\nimport dataclasses\\nfrom typing import Callable, List, Optional\\n\\nimport chex\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\nfrom tracr.transformer import attention\\n\\n# hk.Modules are not always callable: github.com/deepmind/dm-haiku/issues/52\\n# Ideally, we\\'d want a type:\\n# CallableHaikuModule = Intersection[Callable[..., jax.Array], hk.Module]\\n# But Intersection does not exist (yet): github.com/python/typing/issues/213\\nCallableHaikuModule = Callable[..., jax.Array]\\n\\n\\<EMAIL>\\nclass TransformerOutput:\\n  layer_outputs: List[jax.Array]  # [B, T, D]\\n  residuals: List[jax.Array]  # [B, T, D]\\n  attn_logits: List[jax.Array]  # [B, H, T, T]\\n  output: jax.Array  # [B, T, D]\\n  input_embeddings: jax.Array  # [B, T, D]\\n\\n\\<EMAIL>\\nclass TransformerConfig:\\n  num_heads: int\\n  num_layers: int\\n  key_size: int\\n  mlp_hidden_size: int\\n  dropout_rate: float\\n  activation_function: Callable[[jax.Array], jax.Array] = jax.nn.gelu\\n  layer_norm: bool = True\\n  causal: bool = False\\n\\n\\<EMAIL>\\nclass Transformer(hk.Module):\\n  \"\"\"A transformer stack.\"\"\"\\n\\n  config: TransformerConfig\\n  name: Optional[str] = None\\n\\n  def __call__(\\n      self,\\n      embeddings: jax.Array,  # [B, T, D]\\n      mask: jax.Array,  # [B, T]\\n      *,\\n      use_dropout: bool = True,\\n  ) -> TransformerOutput:\\n    \"\"\"Transforms input embedding sequences to output embedding sequences.\"\"\"\\n\\n    def layer_norm(x: jax.Array) -> jax.Array:\\n      \"\"\"Applies a unique LayerNorm to x with default settings.\"\"\"\\n      if self.config.layer_norm:\\n        return hk.LayerNorm(axis=-1, create_scale=True, create_offset=True)(x)\\n      return x\\n\\n    initializer = hk.initializers.VarianceScaling(2 / self.config.num_layers)\\n    dropout_rate = self.config.dropout_rate if use_dropout else 0.\\n    _, seq_len, model_size = embeddings.shape\\n\\n    # Compute causal mask for autoregressive sequence modelling.\\n    mask = mask[:, None, None, :]  # [B, H=1, T\\'=1, T]\\n    mask = mask.repeat(seq_len, axis=2)  # [B, H=1, T, T]\\n\\n    if self.config.causal:\\n      causal_mask = np.ones((1, 1, seq_len, seq_len))  # [B=1, H=1, T, T]\\n      causal_mask = np.tril(causal_mask)\\n      mask = mask * causal_mask  # [B, H=1, T, T]\\n\\n    # Set up activation collection.\\n    collected = collections.defaultdict(list)\\n\\n    def collect(**kwargs):\\n      for k, v in kwargs.items():\\n        collected[k].append(v)\\n\\n    residual = embeddings\\n    for layer in range(self.config.num_layers):\\n      with hk.experimental.name_scope(f\"layer_{layer}\"):\\n        # First the attention block.\\n        attn_block = attention.MultiHeadAttention(\\n            num_heads=self.config.num_heads,\\n            key_size=self.config.key_size,\\n            model_size=model_size,\\n            w_init=initializer,\\n            name=\"attn\")\\n        attn_in = layer_norm(residual)\\n        attn_out = attn_block(attn_in, attn_in, attn_in, mask=mask)\\n        attn_out, attn_logits = attn_out.out, attn_out.logits\\n        if dropout_rate > 0:\\n          attn_out = hk.dropout(hk.next_rng_key(), dropout_rate, attn_out)\\n        residual = residual + attn_out\\n\\n        collect(\\n            residuals=residual, layer_outputs=attn_out, attn_logits=attn_logits)\\n\\n        # Then the dense block.\\n        with hk.experimental.name_scope(\"mlp\"):\\n          dense_block = hk.Sequential([\\n              hk.Linear(\\n                  self.config.mlp_hidden_size,\\n                  w_init=initializer,\\n                  name=\"linear_1\"),\\n              self.config.activation_function,\\n              hk.Linear(model_size, w_init=initializer, name=\"linear_2\"),\\n          ])\\n        dense_in = layer_norm(residual)\\n        dense_out = dense_block(dense_in)\\n        if dropout_rate > 0:\\n          dense_out = hk.dropout(hk.next_rng_key(), dropout_rate, dense_out)\\n        residual = residual + dense_out\\n\\n        collect(residuals=residual, layer_outputs=dense_out)\\n\\n    return TransformerOutput(\\n        residuals=collected[\"residuals\"],\\n        layer_outputs=collected[\"layer_outputs\"],\\n        attn_logits=collected[\"attn_logits\"],\\n        output=layer_norm(residual),\\n        input_embeddings=embeddings,\\n    )\\n\\n\\<EMAIL>\\nclass CompiledTransformerModelOutput:\\n  transformer_output: TransformerOutput\\n  unembedded_output: jax.Array  # [B, T]\\n\\n\\<EMAIL>\\nclass CompiledTransformerModel(hk.Module):\\n  \"\"\"A transformer model with one-hot embeddings.\"\"\"\\n  transformer: Transformer\\n  token_embed: CallableHaikuModule\\n  position_embed: CallableHaikuModule\\n  unembed: CallableHaikuModule\\n  use_unembed_argmax: bool\\n  pad_token: Optional[int] = None\\n\\n  def embed(self, tokens: jax.Array) -> jax.Array:\\n    token_embeddings = self.token_embed(tokens)\\n    positional_embeddings = self.position_embed(jnp.indices(tokens.shape)[-1])\\n    return token_embeddings + positional_embeddings  # [B, T, D]\\n\\n  def __call__(\\n      self,\\n      tokens: jax.Array,\\n      use_dropout: bool = True,\\n  ) -> CompiledTransformerModelOutput:\\n    \"\"\"Embed tokens, pass through model, and unembed output.\"\"\"\\n    if self.pad_token is None:\\n      input_mask = jnp.ones_like(tokens)\\n    else:\\n      input_mask = (tokens != self.pad_token)\\n    input_embeddings = self.embed(tokens)\\n\\n    transformer_output = self.transformer(\\n        input_embeddings,\\n        input_mask,\\n        use_dropout=use_dropout,\\n    )\\n    return CompiledTransformerModelOutput(\\n        transformer_output=transformer_output,\\n        unembedded_output=self.unembed(\\n            transformer_output.output,\\n            use_unembed_argmax=self.use_unembed_argmax,\\n        ),\\n    )\\n', path=PosixPath('tracr/transformer/model.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b884e9f0>, scope_tree=SrcScope(name='tracr/transformer/model.py', kind='file', range=0:6544, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6544:6544, code=''), len(children)=6)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for transformer.model.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\nfrom tracr.transformer import model\\n\\n\\nclass TransformerTest(parameterized.TestCase):\\n\\n  def _check_layer_naming(self, params):\\n    # Modules should be named for example\\n    # For MLPs: \"transformer/layer_{i}/mlp/linear_1\"\\n    # For Attention: \"transformer/layer_{i}/attn/key\"\\n    # For Layer Norm: \"transformer/layer_{i}/layer_norm\"\\n    for key in params.keys():\\n      levels = key.split(\"/\")\\n      self.assertEqual(levels[0], \"transformer\")\\n      if levels[1].startswith(\"layer_norm\"):\\n        continue  # output layer norm\\n      self.assertStartsWith(levels[1], \"layer\")\\n      if levels[2] == \"mlp\":\\n        self.assertIn(levels[3], {\"linear_1\", \"linear_2\"})\\n      elif levels[2] == \"attn\":\\n        self.assertIn(levels[3], {\"key\", \"query\", \"value\", \"linear\"})\\n      else:\\n        self.assertStartsWith(levels[2], \"layer_norm\")\\n\\n  def _zero_mlps(self, params):\\n    for module in params:\\n      if \"mlp\" in module:\\n        for param in params[module]:\\n          params[module][param] = jnp.zeros_like(params[module][param])\\n    return params\\n\\n  @parameterized.parameters(dict(layer_norm=True), dict(layer_norm=False))\\n  def test_layer_norm(self, layer_norm):\\n    # input = [1, 1, 1, 1]\\n    # If layer norm is used, this should give all-0 output for a freshly\\n    # initialized model because LN will subtract the mean after each layer.\\n    # Else we expect non-zero outputs.\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              layer_norm=layer_norm))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = jnp.ones((1, seq_len, 1))\\n    mask = jnp.ones((1, seq_len))\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    out = forward.apply(params, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    if layer_norm:\\n      np.testing.assert_allclose(out, 0)\\n    else:\\n      self.assertFalse(np.allclose(out, 0))\\n\\n  @parameterized.parameters(dict(causal=True), dict(causal=False))\\n  def test_causal_attention(self, causal):\\n    # input = [0, random, random, random]\\n    # mask = [1, 0, 1, 1]\\n    # For causal attention the second token can only attend to the first one, so\\n    # it should be the same. For non-causal attention all tokens should change.\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              layer_norm=False,\\n              causal=causal))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, 1))\\n    emb[:, 0, :] = 0\\n    mask = np.array([[1, 0, 1, 1]])\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    params = self._zero_mlps(params)\\n    out = forward.apply(params, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    if causal:\\n      self.assertEqual(0, out[0, 0, 0])\\n      self.assertEqual(emb[0, 1, 0], out[0, 1, 0])\\n    else:\\n      self.assertNotEqual(0, out[0, 0, 0])\\n      self.assertNotEqual(emb[0, 1, 0], out[0, 1, 0])\\n    self.assertNotEqual(emb[0, 2, 0], out[0, 2, 0])\\n    self.assertNotEqual(emb[0, 3, 0], out[0, 3, 0])\\n\\n  def test_setting_activation_function_to_zero(self):\\n    # An activation function that always returns zeros should result in the\\n    # same model output as setting all MLP weights to zero.\\n\\n    @hk.transform\\n    def forward_zero(emb, mask):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jnp.zeros_like))\\n      return transformer(emb, mask).output\\n\\n    @hk.transform\\n    def forward(emb, mask):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jax.nn.gelu))\\n      return transformer(emb, mask).output\\n\\n    seq_len = 4\\n    emb = np.random.random((1, seq_len, 1))\\n    mask = np.ones((1, seq_len))\\n    emb, mask = jnp.array(emb), jnp.array(mask)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = forward.init(next(rng), emb, mask)\\n    params_no_mlps = self._zero_mlps(params)\\n\\n    out_zero_activation = forward_zero.apply(params, next(rng), emb, mask)\\n    out_no_mlps = forward.apply(params_no_mlps, next(rng), emb, mask)\\n\\n    self._check_layer_naming(params)\\n    np.testing.assert_allclose(out_zero_activation, out_no_mlps)\\n    self.assertFalse(np.allclose(out_zero_activation, 0))\\n\\n\\nclass CompiledTransformerModelTest(parameterized.TestCase):\\n\\n  def _get_one_hot_embed_unembed(self, vocab_size, max_seq_len):\\n    # Embeds tokens as one-hot into the first `vocab_size` dimensions\\n    token_embed = hk.Embed(\\n        embedding_matrix=jnp.block(\\n            [jnp.eye(vocab_size),\\n             jnp.zeros((vocab_size, max_seq_len))]))\\n\\n    # Embeds positions as one-hot into the last `max_seq_len` dimensions\\n    position_embed = hk.Embed(\\n        embedding_matrix=jnp.block(\\n            [jnp.zeros((max_seq_len, vocab_size)),\\n             jnp.eye(max_seq_len)]))\\n\\n    class Unembed(hk.Module):\\n\\n      def __call__(self, embeddings):\\n        return jnp.argmax(embeddings[:, :, :vocab_size], axis=-1)\\n\\n    return token_embed, position_embed, Unembed()\\n\\n  def test_embedding_gives_desired_result(self):\\n    tokens = jnp.array([[1, 2, 3]])\\n    vocab_size, max_seq_len, pad_token = 5, 5, 0\\n\\n    expected_embeddings = jnp.array([[[0, 1, 0, 0, 0, 1, 0, 0, 0, 0],\\n                                      [0, 0, 1, 0, 0, 0, 1, 0, 0, 0],\\n                                      [0, 0, 0, 1, 0, 0, 0, 1, 0, 0]]])\\n\\n    @hk.transform\\n    def embed(tokens):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jax.nn.gelu))\\n      token_embed, position_embed, unembed = self._get_one_hot_embed_unembed(\\n          vocab_size, max_seq_len)\\n      compiled_model = model.CompiledTransformerModel(\\n          transformer=transformer,\\n          token_embed=token_embed,\\n          position_embed=position_embed,\\n          unembed=unembed,\\n          use_unembed_argmax=True,\\n          pad_token=pad_token)\\n      return compiled_model.embed(tokens)\\n\\n    rng = hk.PRNGSequence(1)\\n    params = embed.init(next(rng), tokens)\\n    embeddings = embed.apply(params, next(rng), tokens)\\n\\n    np.testing.assert_allclose(embeddings, expected_embeddings)\\n\\n  def test_embedding_then_unembedding_gives_same_tokens(self):\\n    tokens = jnp.array([[1, 2, 3], [4, 5, 6], [3, 2, 4]])\\n    vocab_size, max_seq_len, pad_token = 10, 5, 0\\n\\n    @hk.transform\\n    def embed_unembed(tokens):\\n      transformer = model.Transformer(\\n          model.TransformerConfig(\\n              num_heads=2,\\n              num_layers=2,\\n              key_size=5,\\n              mlp_hidden_size=64,\\n              dropout_rate=0.,\\n              causal=False,\\n              layer_norm=False,\\n              activation_function=jax.nn.gelu))\\n      token_embed, position_embed, unembed = self._get_one_hot_embed_unembed(\\n          vocab_size, max_seq_len)\\n      compiled_model = model.CompiledTransformerModel(\\n          transformer=transformer,\\n          token_embed=token_embed,\\n          position_embed=position_embed,\\n          unembed=unembed,\\n          use_unembed_argmax=True,\\n          pad_token=pad_token)\\n      embeddings = compiled_model.embed(tokens)\\n      unembeddings = compiled_model.unembed(embeddings)\\n      return embeddings, unembeddings\\n\\n    rng = hk.PRNGSequence(1)\\n    params = embed_unembed.init(next(rng), tokens)\\n    embeddings, unembeddings = embed_unembed.apply(params, next(rng), tokens)\\n\\n    self.assertEqual(\\n        embeddings.shape,\\n        (tokens.shape[0], tokens.shape[1], vocab_size + max_seq_len))\\n\\n    np.testing.assert_allclose(unembeddings, tokens)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/transformer/model_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b87fa370>, scope_tree=SrcScope(name='tracr/transformer/model_test.py', kind='file', range=0:9513, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=9513:9513, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/examples/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b87fadd0>, scope_tree=SrcScope(name='tracr/examples/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='{\\n  \"cells\": [\\n    {\\n      \"cell_type\": \"markdown\",\\n      \"metadata\": {\\n        \"id\": \"99FBiGH7bsfn\"\\n      },\\n      \"source\": [\\n        \"# Compiling \\\\u0026 Visualizing Tracr Models\\\\n\",\\n        \"\\\\n\",\\n        \"This notebook demonstrates how to compile a tracr model and provides some tools visualize the model\\'s residual stream or layer outputs for a given input sequence.\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"qm-PM1PEawCx\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Imports\\\\n\",\\n        \"import jax\\\\n\",\\n        \"import numpy as np\\\\n\",\\n        \"import matplotlib.pyplot as plt\\\\n\",\\n        \"\\\\n\",\\n        \"# The default of float16 can lead to discrepancies between outputs of\\\\n\",\\n        \"# the compiled model and the RASP program.\\\\n\",\\n        \"jax.config.update(\\'jax_default_matmul_precision\\', \\'float32\\')\\\\n\",\\n        \"\\\\n\",\\n        \"from tracr.compiler import compiling\\\\n\",\\n        \"from tracr.compiler import lib\\\\n\",\\n        \"from tracr.rasp import rasp\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"HtOAc_yWawFR\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Plotting functions\\\\n\",\\n        \"def tidy_label(label, value_width=5):\\\\n\",\\n        \"  if \\':\\' in label:\\\\n\",\\n        \"    label, value = label.split(\\':\\')\\\\n\",\\n        \"  else:\\\\n\",\\n        \"    value = \\'\\'\\\\n\",\\n        \"  return label + f\\\\\":{value:\\\\u003e{value_width}}\\\\\"\\\\n\",\\n        \"\\\\n\",\\n        \"\\\\n\",\\n        \"def add_residual_ticks(model, value_width=5, x=False, y=True):\\\\n\",\\n        \"  if y:\\\\n\",\\n        \"    plt.yticks(\\\\n\",\\n        \"            np.arange(len(model.residual_labels))+0.5, \\\\n\",\\n        \"            [tidy_label(l, value_width=value_width)\\\\n\",\\n        \"              for l in model.residual_labels], \\\\n\",\\n        \"            family=\\'monospace\\',\\\\n\",\\n        \"            fontsize=20,\\\\n\",\\n        \"    )\\\\n\",\\n        \"  if x:\\\\n\",\\n        \"    plt.xticks(\\\\n\",\\n        \"            np.arange(len(model.residual_labels))+0.5, \\\\n\",\\n        \"            [tidy_label(l, value_width=value_width)\\\\n\",\\n        \"              for l in model.residual_labels], \\\\n\",\\n        \"            family=\\'monospace\\',\\\\n\",\\n        \"            rotation=90,\\\\n\",\\n        \"            fontsize=20,\\\\n\",\\n        \"    )\\\\n\",\\n        \"\\\\n\",\\n        \"\\\\n\",\\n        \"def plot_computation_trace(model,\\\\n\",\\n        \"                           input_labels,\\\\n\",\\n        \"                           residuals_or_outputs,\\\\n\",\\n        \"                           add_input_layer=False,\\\\n\",\\n        \"                           figsize=(12, 9)):\\\\n\",\\n        \"  fig, axes = plt.subplots(nrows=1, ncols=len(residuals_or_outputs), figsize=figsize, sharey=True)\\\\n\",\\n        \"  value_width = max(map(len, map(str, input_labels))) + 1\\\\n\",\\n        \"\\\\n\",\\n        \"  for i, (layer, ax) in enumerate(zip(residuals_or_outputs, axes)):\\\\n\",\\n        \"    plt.sca(ax)\\\\n\",\\n        \"    plt.pcolormesh(layer[0].T, vmin=0, vmax=1)\\\\n\",\\n        \"    if i == 0:\\\\n\",\\n        \"      add_residual_ticks(model, value_width=value_width)\\\\n\",\\n        \"    plt.xticks(\\\\n\",\\n        \"        np.arange(len(input_labels))+0.5,\\\\n\",\\n        \"        input_labels,\\\\n\",\\n        \"        rotation=90,\\\\n\",\\n        \"        fontsize=20,\\\\n\",\\n        \"    )\\\\n\",\\n        \"    if add_input_layer and i == 0:\\\\n\",\\n        \"      title = \\'Input\\'\\\\n\",\\n        \"    else:\\\\n\",\\n        \"      layer_no = i - 1 if add_input_layer else i\\\\n\",\\n        \"      layer_type = \\'Attn\\' if layer_no % 2 == 0 else \\'MLP\\'\\\\n\",\\n        \"      title = f\\'{layer_type} {layer_no // 2 + 1}\\'\\\\n\",\\n        \"    plt.title(title, fontsize=20)\\\\n\",\\n        \"\\\\n\",\\n        \"\\\\n\",\\n        \"def plot_residuals_and_input(model, inputs, figsize=(12, 9)):\\\\n\",\\n        \"  \\\\\"\\\\\"\\\\\"Applies model to inputs, and plots the residual stream at each layer.\\\\\"\\\\\"\\\\\"\\\\n\",\\n        \"  model_out = assembled_model.apply(inputs)\\\\n\",\\n        \"  residuals = np.concatenate([model_out.input_embeddings[None, ...],\\\\n\",\\n        \"                              model_out.residuals], axis=0)\\\\n\",\\n        \"  plot_computation_trace(\\\\n\",\\n        \"      model=model,\\\\n\",\\n        \"      input_labels=inputs,\\\\n\",\\n        \"      residuals_or_outputs=residuals,\\\\n\",\\n        \"      add_input_layer=True,\\\\n\",\\n        \"      figsize=figsize)\\\\n\",\\n        \"\\\\n\",\\n        \"\\\\n\",\\n        \"def plot_layer_outputs(model, inputs, figsize=(12, 9)):\\\\n\",\\n        \"  \\\\\"\\\\\"\\\\\"Applies model to inputs, and plots the outputs of each layer.\\\\\"\\\\\"\\\\\"\\\\n\",\\n        \"  model_out = assembled_model.apply(inputs)\\\\n\",\\n        \"  plot_computation_trace(\\\\n\",\\n        \"      model=model,\\\\n\",\\n        \"      input_labels=inputs,\\\\n\",\\n        \"      residuals_or_outputs=model_out.layer_outputs,\\\\n\",\\n        \"      add_input_layer=False,\\\\n\",\\n        \"      figsize=figsize)\\\\n\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"cellView\": \"form\",\\n        \"id\": \"8hV0nv_ISmhM\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Define RASP programs\\\\n\",\\n        \"def get_program(program_name, max_seq_len):\\\\n\",\\n        \"  \\\\\"\\\\\"\\\\\"Returns RASP program and corresponding token vocabulary.\\\\\"\\\\\"\\\\\"\\\\n\",\\n        \"  if program_name == \\\\\"length\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"a\\\\\", \\\\\"b\\\\\", \\\\\"c\\\\\", \\\\\"d\\\\\"}\\\\n\",\\n        \"    program = lib.make_length()\\\\n\",\\n        \"  elif program_name == \\\\\"frac_prevs\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"a\\\\\", \\\\\"b\\\\\", \\\\\"c\\\\\", \\\\\"x\\\\\"}\\\\n\",\\n        \"    program = lib.make_frac_prevs((rasp.tokens == \\\\\"x\\\\\").named(\\\\\"is_x\\\\\"))\\\\n\",\\n        \"  elif program_name == \\\\\"dyck-2\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"(\\\\\", \\\\\")\\\\\", \\\\\"{\\\\\", \\\\\"}\\\\\"}\\\\n\",\\n        \"    program = lib.make_shuffle_dyck(pairs=[\\\\\"()\\\\\", \\\\\"{}\\\\\"])\\\\n\",\\n        \"  elif program_name == \\\\\"dyck-3\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"(\\\\\", \\\\\")\\\\\", \\\\\"{\\\\\", \\\\\"}\\\\\", \\\\\"[\\\\\", \\\\\"]\\\\\"}\\\\n\",\\n        \"    program = lib.make_shuffle_dyck(pairs=[\\\\\"()\\\\\", \\\\\"{}\\\\\", \\\\\"[]\\\\\"])\\\\n\",\\n        \"  elif program_name == \\\\\"sort\\\\\":\\\\n\",\\n        \"    vocab = {1, 2, 3, 4, 5}\\\\n\",\\n        \"    program = lib.make_sort(\\\\n\",\\n        \"        rasp.tokens, rasp.tokens, max_seq_len=max_seq_len, min_key=1)\\\\n\",\\n        \"  elif program_name == \\\\\"sort_unique\\\\\":\\\\n\",\\n        \"    vocab = {1, 2, 3, 4, 5}\\\\n\",\\n        \"    program = lib.make_sort_unique(rasp.tokens, rasp.tokens)\\\\n\",\\n        \"  elif program_name == \\\\\"hist\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"a\\\\\", \\\\\"b\\\\\", \\\\\"c\\\\\", \\\\\"d\\\\\"}\\\\n\",\\n        \"    program = lib.make_hist()\\\\n\",\\n        \"  elif program_name == \\\\\"sort_freq\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"a\\\\\", \\\\\"b\\\\\", \\\\\"c\\\\\", \\\\\"d\\\\\"}\\\\n\",\\n        \"    program = lib.make_sort_freq(max_seq_len=max_seq_len)\\\\n\",\\n        \"  elif program_name == \\\\\"pair_balance\\\\\":\\\\n\",\\n        \"    vocab = {\\\\\"(\\\\\", \\\\\")\\\\\"}\\\\n\",\\n        \"    program = lib.make_pair_balance(\\\\n\",\\n        \"        sop=rasp.tokens, open_token=\\\\\"(\\\\\", close_token=\\\\\")\\\\\")\\\\n\",\\n        \"  else:\\\\n\",\\n        \"    raise NotImplementedError(f\\\\\"Program {program_name} not implemented.\\\\\")\\\\n\",\\n        \"  return program, vocab\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"L_m_ufaua9ri\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title: Assemble model\\\\n\",\\n        \"program_name = \\\\\"sort_unique\\\\\"  #@param [\\\\\"length\\\\\", \\\\\"frac_prevs\\\\\", \\\\\"dyck-2\\\\\", \\\\\"dyck-3\\\\\", \\\\\"sort\\\\\", \\\\\"sort_unique\\\\\", \\\\\"hist\\\\\", \\\\\"sort_freq\\\\\", \\\\\"pair_balance\\\\\"]\\\\n\",\\n        \"max_seq_len = 5  #@param {label: \\\\\"Test\\\\\", type: \\\\\"integer\\\\\"}\\\\n\",\\n        \"\\\\n\",\\n        \"program, vocab = get_program(program_name=program_name,\\\\n\",\\n        \"                             max_seq_len=max_seq_len)\\\\n\",\\n        \"\\\\n\",\\n        \"print(f\\\\\"Compiling...\\\\\")\\\\n\",\\n        \"print(f\\\\\"   Program: {program_name}\\\\\")\\\\n\",\\n        \"print(f\\\\\"   Input vocabulary: {vocab}\\\\\")\\\\n\",\\n        \"print(f\\\\\"   Context size: {max_seq_len}\\\\\")\\\\n\",\\n        \"\\\\n\",\\n        \"assembled_model = compiling.compile_rasp_to_model(\\\\n\",\\n        \"      program=program,\\\\n\",\\n        \"      vocab=vocab,\\\\n\",\\n        \"      max_seq_len=max_seq_len,\\\\n\",\\n        \"      causal=False,\\\\n\",\\n        \"      compiler_bos=\\\\\"bos\\\\\",\\\\n\",\\n        \"      compiler_pad=\\\\\"pad\\\\\",\\\\n\",\\n        \"      mlp_exactness=100)\\\\n\",\\n        \"\\\\n\",\\n        \"print(\\\\\"Done.\\\\\")\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"wtwiE-JiXF3F\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Forward pass\\\\n\",\\n        \"assembled_model.apply([\\\\\"bos\\\\\", 3, 4, 1]).decoded\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"RkEkVcEHa2gf\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Plot residual stream\\\\n\",\\n        \"plot_residuals_and_input(\\\\n\",\\n        \"  model=assembled_model,\\\\n\",\\n        \"  inputs=[\\\\\"bos\\\\\", 3, 4, 1],\\\\n\",\\n        \"  figsize=(10, 9)\\\\n\",\\n        \")\"\\n      ]\\n    },\\n    {\\n      \"cell_type\": \"code\",\\n      \"execution_count\": null,\\n      \"metadata\": {\\n        \"id\": \"8c4LakWHa4ey\"\\n      },\\n      \"outputs\": [],\\n      \"source\": [\\n        \"#@title Plot layer outputs\\\\n\",\\n        \"plot_layer_outputs(\\\\n\",\\n        \"  model=assembled_model,\\\\n\",\\n        \"  inputs = [\\\\\"bos\\\\\", 3, 4, 1],\\\\n\",\\n        \"  figsize=(8, 9)\\\\n\",\\n        \")\"\\n      ]\\n    }\\n  ],\\n  \"metadata\": {\\n    \"colab\": {\\n      \"private_outputs\": true\\n    },\\n    \"kernelspec\": {\\n      \"display_name\": \"Python 3\",\\n      \"name\": \"python3\"\\n    },\\n    \"language_info\": {\\n      \"name\": \"python\"\\n    }\\n  },\\n  \"nbformat\": 4,\\n  \"nbformat_minor\": 0\\n}\\n', path=PosixPath('tracr/examples/Visualize_Tracr_Models.ipynb'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b886ced0>, scope_tree=SrcScope(name='tracr/examples/Visualize_Tracr_Models.ipynb', kind='file', range=0:9491, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=9491:9491, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/rasp/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b87ff130>, scope_tree=SrcScope(name='tracr/rasp/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for rasp.rasp.\"\"\"\\n\\nimport itertools\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.rasp import rasp\\n\\n# Note that the example text labels must match their default names.\\n\\n_SOP_PRIMITIVE_EXAMPLES = lambda: [  # pylint: disable=g-long-lambda\\n    (\"tokens\", rasp.tokens),\\n    (\"length\", rasp.length),\\n    (\"indices\", rasp.indices),\\n]\\n\\n_NONPRIMITIVE_SOP_EXAMPLES = lambda: [  # pylint: disable=g-long-lambda\\n    (\"map\", rasp.Map(lambda x: x, rasp.tokens)),\\n    (\\n        \"sequence_map\",\\n        rasp.SequenceMap(lambda x, y: x + y, rasp.tokens, rasp.tokens),\\n    ),\\n    (\\n        \"linear_sequence_map\",\\n        rasp.LinearSequenceMap(rasp.tokens, rasp.tokens, 0.1, 0.2),\\n    ),\\n    (\\n        \"aggregate\",\\n        rasp.Aggregate(\\n            rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n            rasp.tokens,\\n        ),\\n    ),\\n    (\\n        \"selector_width\",\\n        rasp.SelectorWidth(\\n            rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ)),\\n    ),\\n]\\n\\n_SOP_EXAMPLES = lambda: _SOP_PRIMITIVE_EXAMPLES() + _NONPRIMITIVE_SOP_EXAMPLES()\\n\\n_SELECTOR_EXAMPLES = lambda: [  # pylint: disable=g-long-lambda\\n    (\"select\", rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ)),\\n    (\"selector_and\",\\n     rasp.SelectorAnd(\\n         rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n         rasp.Select(rasp.indices, rasp.tokens, rasp.Comparison.LEQ),\\n     )),\\n    (\"selector_or\",\\n     rasp.SelectorOr(\\n         rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n         rasp.Select(rasp.indices, rasp.tokens, rasp.Comparison.LEQ),\\n     )),\\n    (\"selector_not\",\\n     rasp.SelectorNot(\\n         rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),)),\\n]\\n\\n_ALL_EXAMPLES = lambda: _SOP_EXAMPLES() + _SELECTOR_EXAMPLES()\\n\\n\\nclass LabelTest(parameterized.TestCase):\\n\\n  def test_primitive_labels(self):\\n    self.assertEqual(rasp.tokens.label, \"tokens\")\\n    self.assertEqual(rasp.indices.label, \"indices\")\\n    self.assertEqual(rasp.length.label, \"length\")\\n\\n  @parameterized.parameters(*_ALL_EXAMPLES())\\n  def test_default_names(self, default_name: str, expr: rasp.RASPExpr):\\n    self.assertEqual(expr.name, default_name)\\n\\n\\nclass SOpTest(parameterized.TestCase):\\n  \"\"\"Tests for S-Ops.\"\"\"\\n\\n  @parameterized.parameters(\\n      (\"hello\", [\"h\", \"e\", \"l\", \"l\", \"o\"]),\\n      (\"h\", [\"h\"]),\\n      ([\"h\", \"e\", \"l\", \"l\", \"o\"], [\"h\", \"e\", \"l\", \"l\", \"o\"]),\\n      ([\"h\"], [\"h\"]),\\n      ([1, 2], [1, 2]),\\n      ([0.1, 0.2], [0.1, 0.2]),\\n  )\\n  def test_tokens(self, input_sequence, expected):\\n    self.assertEqual(rasp.tokens(input_sequence), expected)\\n\\n  @parameterized.parameters(\\n      (\"hello\", [0, 1, 2, 3, 4]),\\n      (\"h\", [0]),\\n      ([\"h\", \"e\", \"l\", \"l\", \"o\"], [0, 1, 2, 3, 4]),\\n      ([\"h\"], [0]),\\n      ([1, 2], [0, 1]),\\n      ([0.1, 0.2], [0, 1]),\\n  )\\n  def test_indices(self, input_sequence, expected):\\n    self.assertEqual(rasp.indices(input_sequence), expected)\\n\\n  @parameterized.parameters(\\n      (\"hello\", [5, 5, 5, 5, 5]),\\n      (\"h\", [1]),\\n      ([\"h\", \"e\", \"l\", \"l\", \"o\"], [5, 5, 5, 5, 5]),\\n      ([\"h\"], [1]),\\n      ([1, 2], [2, 2]),\\n      ([0.1, 0.2], [2, 2]),\\n  )\\n  def test_length(self, input_sequence, expected):\\n    self.assertEqual(rasp.length(input_sequence), expected)\\n\\n  def test_prims_are_sops(self):\\n    self.assertIsInstance(rasp.tokens, rasp.SOp)\\n    self.assertIsInstance(rasp.indices, rasp.SOp)\\n    self.assertIsInstance(rasp.length, rasp.SOp)\\n\\n  def test_prims_are_raspexprs(self):\\n    self.assertIsInstance(rasp.tokens, rasp.RASPExpr)\\n    self.assertIsInstance(rasp.indices, rasp.RASPExpr)\\n    self.assertIsInstance(rasp.length, rasp.RASPExpr)\\n\\n  @parameterized.parameters(\\n      (lambda x: x + \"a\", \"hello\", [\"ha\", \"ea\", \"la\", \"la\", \"oa\"]),\\n      (lambda x: x + \"t\", \"h\", [\"ht\"]),\\n      (lambda x: x + 1, [1, 2], [2, 3]),\\n      (lambda x: x / 2, [0.1, 0.2], [0.05, 0.1]),\\n  )\\n  def test_map(self, f, input_sequence, expected):\\n    self.assertEqual(rasp.Map(f, rasp.tokens)(input_sequence), expected)\\n\\n  def test_nested_elementwise_ops_results_in_only_one_map_object(self):\\n    map_sop = ((rasp.tokens * 2) + 2) / 2\\n    self.assertEqual(map_sop.inner, rasp.tokens)\\n    self.assertEqual(map_sop([1]), [2])\\n\\n  @parameterized.parameters(\\n      (lambda x, y: x + y, \"hello\", [\"hh\", \"ee\", \"ll\", \"ll\", \"oo\"]),\\n      (lambda x, y: x + y, \"h\", [\"hh\"]),\\n      (lambda x, y: x + y, [1, 2], [2, 4]),\\n      (lambda x, y: x * y, [1, 2], [1, 4]),\\n  )\\n  def test_sequence_map(self, f, input_sequence, expected):\\n    self.assertEqual(\\n        rasp.SequenceMap(f, rasp.tokens, rasp.tokens)(input_sequence), expected)\\n\\n  def test_sequence_map_with_same_inputs_logs_warning(self):\\n    with self.assertLogs(level=\"WARNING\"):\\n      rasp.SequenceMap(lambda x, y: x + y, rasp.tokens, rasp.tokens)\\n\\n  @parameterized.parameters(\\n      (1, 1, [1, 2], [2, 4]),\\n      (1, -1, [1, 2], [0, 0]),\\n      (1, -2, [1, 2], [-1, -2]),\\n  )\\n  def test_linear_sequence_map(self, fst_fac, snd_fac, input_sequence,\\n                               expected):\\n    self.assertEqual(\\n        rasp.LinearSequenceMap(rasp.tokens, rasp.tokens, fst_fac,\\n                               snd_fac)(input_sequence), expected)\\n\\n  @parameterized.parameters(\\n      ([5, 5, 5, 5, 5], \"hello\", [5, 5, 5, 5, 5]),\\n      ([\"e\"], \"h\", [\"e\"]),\\n      ([1, 2, 3, 4, 5], [\"h\", \"e\", \"l\", \"l\", \"o\"], [1, 2, 3, 4, 5]),\\n      ([2, 2], [1, 2], [2, 2]),\\n  )\\n  def test_constant(self, const, input_sequence, expected):\\n    self.assertEqual(rasp.ConstantSOp(const)(input_sequence), expected)\\n\\n  def test_constant_complains_if_sizes_dont_match(self):\\n    with self.assertRaisesRegex(\\n        ValueError,\\n        r\"^.*Constant len .* doesn\\'t match input len .*$\",):\\n      rasp.ConstantSOp([1, 2, 3])(\"longer string\")\\n\\n  def test_can_turn_off_constant_complaints(self):\\n    rasp.ConstantSOp([1, 2, 3], check_length=False)(\"longer string\")\\n\\n  def test_numeric_dunders(self):\\n    # We don\\'t check all the cases here -- only a few representative ones.\\n    self.assertEqual(\\n        (rasp.tokens > 1)([0, 1, 2]),\\n        [0, 0, 1],\\n    )\\n    self.assertEqual(\\n        (1 < rasp.tokens)([0, 1, 2]),\\n        [0, 0, 1],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens < 1)([0, 1, 2]),\\n        [1, 0, 0],\\n    )\\n    self.assertEqual(\\n        (1 > rasp.tokens)([0, 1, 2]),\\n        [1, 0, 0],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens == 1)([0, 1, 2]),\\n        [0, 1, 0],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens + 1)([0, 1, 2]),\\n        [1, 2, 3],\\n    )\\n    self.assertEqual(\\n        (1 + rasp.tokens)([0, 1, 2]),\\n        [1, 2, 3],\\n    )\\n\\n  def test_dunders_with_sop(self):\\n    self.assertEqual(\\n        (rasp.tokens + rasp.indices)([0, 1, 2]),\\n        [0, 2, 4],\\n    )\\n    self.assertEqual(\\n        (rasp.length - 1 - rasp.indices)([0, 1, 2]),\\n        [2, 1, 0],\\n    )\\n    self.assertEqual(\\n        (rasp.length * rasp.length)([0, 1, 2]),\\n        [9, 9, 9],\\n    )\\n\\n  def test_logical_dunders(self):\\n    self.assertEqual(\\n        (rasp.tokens & True)([True, False]),\\n        [True, False],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens & False)([True, False]),\\n        [False, False],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens | True)([True, False]),\\n        [True, True],\\n    )\\n    self.assertEqual(\\n        (rasp.tokens | False)([True, False]),\\n        [True, False],\\n    )\\n    self.assertEqual(\\n        (True & rasp.tokens)([True, False]),\\n        [True, False],\\n    )\\n    self.assertEqual(\\n        (False & rasp.tokens)([True, False]),\\n        [False, False],\\n    )\\n    self.assertEqual(\\n        (True | rasp.tokens)([True, False]),\\n        [True, True],\\n    )\\n    self.assertEqual(\\n        (False | rasp.tokens)([True, False]),\\n        [True, False],\\n    )\\n\\n    self.assertEqual(\\n        (~rasp.tokens)([True, False]),\\n        [False, True],\\n    )\\n\\n    self.assertEqual(\\n        (rasp.ConstantSOp([True, True, False, False])\\n         & rasp.ConstantSOp([True, False, True, False]))([1, 1, 1, 1]),\\n        [True, False, False, False],\\n    )\\n\\n    self.assertEqual(\\n        (rasp.ConstantSOp([True, True, False, False])\\n         | rasp.ConstantSOp([True, False, True, False]))([1, 1, 1, 1]),\\n        [True, True, True, False],\\n    )\\n\\n\\nclass EncodingTest(parameterized.TestCase):\\n  \"\"\"Tests for SOp encodings.\"\"\"\\n\\n  @parameterized.named_parameters(*_SOP_EXAMPLES())\\n  def test_all_sops_are_categorical_by_default(self, sop: rasp.SOp):\\n    self.assertTrue(rasp.is_categorical(sop))\\n\\n  @parameterized.named_parameters(*_SOP_EXAMPLES())\\n  def test_is_numerical(self, sop: rasp.SOp):\\n    self.assertTrue(rasp.is_numerical(rasp.numerical(sop)))\\n    self.assertFalse(rasp.is_numerical(rasp.categorical(sop)))\\n\\n  @parameterized.named_parameters(*_SOP_EXAMPLES())\\n  def test_is_categorical(self, sop: rasp.SOp):\\n    self.assertTrue(rasp.is_categorical(rasp.categorical(sop)))\\n    self.assertFalse(rasp.is_categorical(rasp.numerical(sop)))\\n\\n  @parameterized.named_parameters(*_SOP_EXAMPLES())\\n  def test_double_encoding_annotations_overwrites_encoding(self, sop: rasp.SOp):\\n    num_sop = rasp.numerical(sop)\\n    cat_num_sop = rasp.categorical(num_sop)\\n    self.assertTrue(rasp.is_numerical(num_sop))\\n    self.assertTrue(rasp.is_categorical(cat_num_sop))\\n\\n\\nclass SelectorTest(parameterized.TestCase):\\n  \"\"\"Tests for Selectors.\"\"\"\\n\\n  def test_select_eq_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.EQ)\\n    self.assertEqual(\\n        selector(\"hey\"), [\\n            [True, False, False],\\n            [False, True, False],\\n            [False, False, True],\\n        ])\\n\\n  def test_select_lt_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.LT)\\n    self.assertEqual(selector([0, 1]), [\\n        [False, False],\\n        [True, False],\\n    ])\\n\\n  def test_select_leq_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.LEQ)\\n    self.assertEqual(selector([0, 1]), [\\n        [True, False],\\n        [True, True],\\n    ])\\n\\n  def test_select_gt_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.GT)\\n    self.assertEqual(selector([0, 1]), [\\n        [False, True],\\n        [False, False],\\n    ])\\n\\n  def test_select_geq_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.GEQ)\\n    self.assertEqual(selector([0, 1]), [\\n        [True, True],\\n        [False, True],\\n    ])\\n\\n  def test_select_neq_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.NEQ)\\n    self.assertEqual(selector([0, 1]), [\\n        [False, True],\\n        [True, False],\\n    ])\\n\\n  def test_select_true_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.TRUE)\\n    self.assertEqual(selector([0, 1]), [\\n        [True, True],\\n        [True, True],\\n    ])\\n\\n  def test_select_false_has_correct_value(self):\\n    selector = rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.FALSE)\\n    self.assertEqual(selector([0, 1]), [\\n        [False, False],\\n        [False, False],\\n    ])\\n\\n  def test_selector_and_gets_simplified_when_keys_and_queries_match(self):\\n    selector = rasp.selector_and(\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.GEQ),\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.LEQ),\\n    )\\n    self.assertIsInstance(selector, rasp.Select)\\n    self.assertIs(selector.keys, rasp.tokens)\\n    self.assertIs(selector.queries, rasp.indices)\\n\\n  def test_selector_and_doesnt_get_simplified_when_keys_queries_different(self):\\n    selector = rasp.selector_and(\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.GEQ),\\n        rasp.Select(rasp.indices, rasp.tokens, rasp.Comparison.LEQ),\\n    )\\n    self.assertIsInstance(selector, rasp.SelectorAnd)\\n\\n  def test_selector_and_gets_simplified_when_keys_are_full(self):\\n    selector = rasp.selector_and(\\n        rasp.Select(rasp.Full(1), rasp.indices, rasp.Comparison.GEQ),\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.LEQ),\\n    )\\n    self.assertIsInstance(selector, rasp.Select)\\n    self.assertIs(selector.keys, rasp.tokens)\\n    self.assertIs(selector.queries, rasp.indices)\\n\\n  def test_selector_and_gets_simplified_when_queries_are_full(self):\\n    selector = rasp.selector_and(\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.GEQ),\\n        rasp.Select(rasp.tokens, rasp.Full(1), rasp.Comparison.LEQ),\\n    )\\n    self.assertIsInstance(selector, rasp.Select)\\n    self.assertIs(selector.keys, rasp.tokens)\\n    self.assertIs(selector.queries, rasp.indices)\\n\\n  @parameterized.parameters(\\n      itertools.product(\\n          (rasp.tokens, rasp.indices, rasp.Full(1)),\\n          (rasp.tokens, rasp.indices, rasp.Full(1)),\\n          list(rasp.Comparison),\\n          (rasp.tokens, rasp.indices, rasp.Full(1)),\\n          (rasp.tokens, rasp.indices, rasp.Full(1)),\\n          list(rasp.Comparison),\\n      ))\\n  def test_simplified_selector_and_works_the_same_way_as_not(\\n      self, fst_k, fst_q, fst_p, snd_k, snd_q, snd_p):\\n    fst = rasp.Select(fst_k, fst_q, fst_p)\\n    snd = rasp.Select(snd_k, snd_q, snd_p)\\n\\n    simplified = rasp.selector_and(fst, snd)([0, 1, 2, 3])\\n    not_simplified = rasp.selector_and(fst, snd, simplify=False)([0, 1, 2, 3])\\n\\n    np.testing.assert_array_equal(\\n        np.array(simplified),\\n        np.array(not_simplified),\\n    )\\n\\n  def test_select_is_selector(self):\\n    self.assertIsInstance(\\n        rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.EQ),\\n        rasp.Selector,\\n    )\\n\\n  def test_select_is_raspexpr(self):\\n    self.assertIsInstance(\\n        rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.EQ),\\n        rasp.RASPExpr,\\n    )\\n\\n  def test_constant_selector(self):\\n    self.assertEqual(\\n        rasp.ConstantSelector([[True, True], [False, False]])([1, 2]),\\n        [[True, True], [False, False]],\\n    )\\n\\n\\nclass CopyTest(parameterized.TestCase):\\n\\n  @parameterized.named_parameters(*_ALL_EXAMPLES())\\n  def test_copy_preserves_name(self, expr: rasp.RASPExpr):\\n    expr = expr.named(\"foo\")\\n    self.assertEqual(expr.copy().name, expr.name)\\n\\n  @parameterized.named_parameters(*_ALL_EXAMPLES())\\n  def test_renaming_copy_doesnt_rename_original(self, expr: rasp.RASPExpr):\\n    expr = expr.named(\"foo\")\\n    expr.copy().named(\"bar\")\\n    self.assertEqual(expr.name, \"foo\")\\n\\n  @parameterized.named_parameters(*_ALL_EXAMPLES())\\n  def test_renaming_original_doesnt_rename_copy(self, expr: rasp.RASPExpr):\\n    expr = expr.named(\"foo\")\\n    copy = expr.copy()\\n    expr.named(\"bar\")\\n    self.assertEqual(copy.name, \"foo\")\\n\\n  @parameterized.named_parameters(*_ALL_EXAMPLES())\\n  def test_copy_changes_id(self, expr: rasp.RASPExpr):\\n    self.assertNotEqual(expr.copy().unique_id, expr.unique_id)\\n\\n  @parameterized.named_parameters(*_ALL_EXAMPLES())\\n  def test_copy_preserves_child_ids(self, expr: rasp.RASPExpr):\\n    copy_child_ids = [c.unique_id for c in expr.copy().children]\\n    child_ids = [c.unique_id for c in expr.children]\\n    for child_id, copy_child_id in zip(child_ids, copy_child_ids):\\n      self.assertEqual(child_id, copy_child_id)\\n\\n\\nclass AggregateTest(parameterized.TestCase):\\n  \"\"\"Tests for Aggregate.\"\"\"\\n\\n  @parameterized.parameters(\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [True, False],\\n              [False, True],\\n          ]),\\n          sop=rasp.ConstantSOp([\"h\", \"e\"]),\\n          default=None,\\n          expected_value=[\"h\", \"e\"],\\n      ),\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [False, True],\\n              [False, False],\\n          ]),\\n          sop=rasp.ConstantSOp([\"h\", \"e\"]),\\n          default=None,\\n          expected_value=[\"e\", None],\\n      ),\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [True, False],\\n              [False, False],\\n          ]),\\n          sop=rasp.ConstantSOp([\"h\", \"e\"]),\\n          default=None,\\n          expected_value=[\"h\", None],\\n      ),\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [True, True],\\n              [False, True],\\n          ]),\\n          sop=rasp.ConstantSOp([0, 1]),\\n          default=0,\\n          expected_value=[0.5, 1],\\n      ),\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [False, False],\\n              [True, True],\\n          ]),\\n          sop=rasp.ConstantSOp([0, 1]),\\n          default=0,\\n          expected_value=[0, 0.5],\\n      ),\\n      dict(\\n          selector=rasp.ConstantSelector([\\n              [False, False],\\n              [True, True],\\n          ]),\\n          sop=rasp.ConstantSOp([0, 1]),\\n          default=None,\\n          expected_value=[None, 0.5],\\n      ),\\n  )\\n  def test_aggregate_on_size_2_inputs(self, selector, sop, default,\\n                                      expected_value):\\n    # The 0, 0 input is ignored as it\\'s overridden by the constant SOps.\\n    self.assertEqual(\\n        rasp.Aggregate(selector, sop, default)([0, 0]),\\n        expected_value,\\n    )\\n\\n\\nclass RaspProgramTest(parameterized.TestCase):\\n  \"\"\"Each testcase implements and tests a RASP program.\"\"\"\\n\\n  def test_has_prev(self):\\n\\n    def has_prev(seq: rasp.SOp) -> rasp.SOp:\\n      prev_copy = rasp.SelectorAnd(\\n          rasp.Select(seq, seq, rasp.Comparison.EQ),\\n          rasp.Select(rasp.indices, rasp.indices, rasp.Comparison.LT),\\n      )\\n      return rasp.Aggregate(prev_copy, rasp.Full(1), default=0) > 0\\n\\n    self.assertEqual(\\n        has_prev(rasp.tokens)(\"hello\"),\\n        [0, 0, 0, 1, 0],\\n    )\\n\\n    self.assertEqual(\\n        has_prev(rasp.tokens)(\"helllo\"),\\n        [0, 0, 0, 1, 1, 0],\\n    )\\n\\n    self.assertEqual(\\n        has_prev(rasp.tokens)([0, 2, 3, 2, 1, 0, 2]),\\n        [0, 0, 0, 1, 0, 1, 1],\\n    )\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/rasp/rasp_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8852ed0>, scope_tree=SrcScope(name='tracr/rasp/rasp_test.py', kind='file', range=0:18399, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=18399:18399, code=''), len(children)=9)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for causal_eval.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\n\\nfrom tracr.rasp import causal_eval\\nfrom tracr.rasp import rasp\\n\\n\\nclass CausalEvalTest(parameterized.TestCase):\\n\\n  @parameterized.named_parameters(\\n      dict(\\n          testcase_name=\"constant_selector_3x3_1\",\\n          program=rasp.ConstantSelector([\\n              [True, True, True],\\n              [True, True, True],\\n              [True, True, True],\\n          ]),\\n          input_sequence=[True, True, True],\\n          expected_output=[\\n              [True, False, False],\\n              [True, True, False],\\n              [True, True, True],\\n          ]),\\n      dict(\\n          testcase_name=\"constant_selector_3x3_2\",\\n          program=rasp.ConstantSelector([\\n              [True, True, True],\\n              [False, True, True],\\n              [True, False, True],\\n          ]),\\n          input_sequence=[True, True, True],\\n          expected_output=[\\n              [True, False, False],\\n              [False, True, False],\\n              [True, False, True],\\n          ]))\\n  def test_evaluations(self, program, input_sequence, expected_output):\\n    self.assertListEqual(\\n        causal_eval.evaluate(program, input_sequence),\\n        expected_output,\\n    )\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/rasp/causal_eval_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8722890>, scope_tree=SrcScope(name='tracr/rasp/causal_eval_test.py', kind='file', range=0:2008, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=2008:2008, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"RASP program objects.\\n\\nEvery object in the RASP language is a function.\\n\\nThe most important type is S-Op, which is a function List[Value] -> List[Value].\\n\\nAn S-Op represents a state inside the residual stream of the transformer.\\nTherefore, any RASP program that represents a transformer computation must\\ndefine a final S-Op that represents the state of the residual stream at the\\nend of the computation. In particular, given an S-Op `x`,\\n`x([1, 2, 3])` represents something like the state of the residual stream\\nat location `x` when the transformer is fed [1, 2, 3] as input.\\n\\nA secondary (but still important) type is Selector, which is a function\\nList[Value] -> List[List[bool]]. Given a Selector `sel`, sel([1, 2, 3])\\nrepresents something like an attention matrix in the transformer.\\n\\nFor a full reference on RASP, see https://arxiv.org/abs/2106.06981.\\n\"\"\"\\n\\nimport abc\\nimport collections.abc\\nimport copy\\nimport enum\\nimport functools\\nimport itertools\\nfrom typing import (Any, Callable, Dict, Generic, List, Mapping, Optional,\\n                    Sequence, TypeVar, Union)\\n\\nfrom absl import logging\\nimport numpy as np\\nfrom typing_extensions import Protocol\\n\\nSelectorValue = List[List[bool]]\\nNumericValue = Union[int, float]\\nValue = Union[None, int, float, str, bool]\\nVT = TypeVar(\"VT\", bound=Value)\\nRASPExprT = TypeVar(\"RASPExprT\", bound=\"RASPExpr\")\\nSOpT = TypeVar(\"SOpT\", bound=\"SOp\")\\nT = TypeVar(\"T\")\\n\\n_NAME_KEY = \"name\"\\n_ENCODING_KEY = \"encoding\"\\n\\n# These are run on every expression when it\\'s initialised.\\n# Add your own annotators to this dict to add custom default annotations.\\n#\\n# For example, DEFAULT_ANNOTATORS[\\'foo\\'] will provide the default value for\\n# expr.annotations[\\'foo]. The annotator will get called lazily the first time\\n# that key is accessed.\\n#\\n# See the `default_name` annotator for a full example.\\nDEFAULT_ANNOTATORS: Dict[str, \"Annotator\"] = {}\\n\\n\\nclass Annotator(Protocol):\\n\\n  def __call__(self, expr: \"RASPExpr\") -> Any:\\n    \"\"\"What annotation to add to `expr`.\"\"\"\\n\\n\\nclass _Annotations(collections.abc.Mapping):\\n  \"\"\"Holds the expression\\'s annotations.\\n\\n  It\\'s immutable to the user, but will attempt to generate default values\\n  lazily when missing keys are requested.\\n  \"\"\"\\n\\n  def __init__(self, expr, **kwargs: Any):\\n    self._expr = expr\\n    self._inner_dict: Dict[str, Any] = {**kwargs}\\n\\n  def __getitem__(self, key: str) -> Any:\\n    if key not in self._inner_dict:\\n      if key not in DEFAULT_ANNOTATORS:\\n        raise KeyError(\\n            f\"No annotation exists for key \\'{key}\\'. \"\\n            f\"Available keys: {list(*self.keys(), *DEFAULT_ANNOTATORS.keys())}\")\\n      self._inner_dict[key] = DEFAULT_ANNOTATORS[key](self._expr)\\n\\n    return self._inner_dict[key]\\n\\n  def __iter__(self):\\n    return iter(self._inner_dict)\\n\\n  def __len__(self):\\n    return len(self._inner_dict)\\n\\n\\nclass RASPExpr(abc.ABC):\\n  \"\"\"A class distinguishing RASP expressions from other objects.\"\"\"\\n  _ids = itertools.count(1)\\n\\n  def __init__(self):\\n    self._annotations: Mapping[str, Any] = _Annotations(self)\\n\\n  @abc.abstractmethod\\n  def __call__(self,\\n               xs: Sequence[Value]) -> Union[Sequence[Value], SelectorValue]:\\n    \"\"\"Evaluates the RASPExpr using the standard evaluator.\"\"\"\\n\\n  @property\\n  def annotations(self) -> Mapping[str, Any]:\\n    \"\"\"The annotations of this expression instance.\"\"\"\\n    return self._annotations\\n\\n  @annotations.setter\\n  def annotations(self, annotations: Mapping[str, Any]):\\n    self._annotations = _Annotations(self, **annotations)\\n\\n  @property\\n  def name(self) -> str:\\n    \"\"\"The name of this expression.\"\"\"\\n    return self.annotations[_NAME_KEY]\\n\\n  @property\\n  @abc.abstractmethod\\n  def children(self) -> Sequence[\"RASPExpr\"]:\\n    \"\"\"Direct dependencies of this expression.\"\"\"\\n\\n  @functools.cached_property\\n  def unique_id(self):\\n    \"\"\"A unique id for every expression instance.\"\"\"\\n    return next(self._ids)\\n\\n  def copy(self: RASPExprT) -> RASPExprT:\\n    \"\"\"Returns a shallow copy of this RASPExpr with a new ID.\"\"\"\\n    return copy.copy(self)\\n\\n  @property\\n  def label(self) -> str:\\n    return f\"{self.name}_{self.unique_id}\"\\n\\n  def named(self: RASPExprT, name: str) -> RASPExprT:\\n    \"\"\"Convenience method for adding a name.\"\"\"\\n    return annotate(self, name=name)\\n\\n  def annotated(self: RASPExprT, **annotations) -> RASPExprT:\\n    \"\"\"Convenience method for adding annotations.\"\"\"\\n    return annotate(self, **annotations)\\n\\n\\ndef annotate(expr: RASPExprT, **annotations) -> RASPExprT:\\n  \"\"\"Creates a new expr with added annotations.\"\"\"\\n  new = expr.copy()\\n  # Note that new annotations will overwrite existing ones with matching keys.\\n  new.annotations = {**expr.annotations, **annotations}\\n  return new\\n\\n\\n### S-Ops.\\n\\n\\nclass SOp(RASPExpr):\\n  \"\"\"A Sequence Operation.\"\"\"\\n\\n  def __call__(self, xs: Sequence[Value]) -> Sequence[Value]:\\n    return evaluate(self, xs)  # pytype: disable=bad-return-type\\n\\n  # Allow construction of SOps using numeric operators with constant values.\\n  # Note: if inheriting SOp by a dataclass, make sure to disable eq and order,\\n  # as they will override these.\\n\\n  def __lt__(self, other: Value) -> \"SOp\":\\n    \"\"\"self < other.\"\"\"\\n    return Map(lambda x: x < other, self)\\n\\n  def __le__(self, other: Value) -> \"SOp\":\\n    \"\"\"self <= other.\"\"\"\\n    return Map(lambda x: x <= other, self)\\n\\n  def __eq__(self, other: Value) -> \"SOp\":\\n    \"\"\"self == other.\"\"\"\\n    return Map(lambda x: x == other, self)\\n\\n  def __ne__(self, other: Value) -> \"SOp\":\\n    \"\"\"self != other.\"\"\"\\n    return Map(lambda x: x != other, self)\\n\\n  def __gt__(self, other: Value) -> \"SOp\":\\n    \"\"\"self > other.\"\"\"\\n    return Map(lambda x: x > other, self)\\n\\n  def __ge__(self, other: Value) -> \"SOp\":\\n    \"\"\"self >= other.\"\"\"\\n    return Map(lambda x: x >= other, self)\\n\\n  def __add__(self, other: Union[\"SOp\", Value]) -> \"SOp\":\\n    \"\"\"self + other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x + y, self, other)\\n    return Map(lambda x: x + other, self)\\n\\n  def __radd__(self, other: Union[\"SOp\", Value]) -> \"SOp\":\\n    \"\"\"other + self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x + y, other, self)\\n    return Map(lambda x: other + x, self)\\n\\n  def __sub__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"self - other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x - y, self, other)\\n    return Map(lambda x: x - other, self)\\n\\n  def __rsub__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"other - self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x - y, other, self)\\n    return Map(lambda x: other - x, self)\\n\\n  def __mul__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"self * other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x * y, self, other)\\n    return Map(lambda x: x * other, self)\\n\\n  def __rmul__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"other * self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x * y, other, self)\\n    return Map(lambda x: other * x, self)\\n\\n  def __truediv__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"self / other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x / y, self, other)\\n    return Map(lambda x: x / other, self)\\n\\n  def __rtruediv__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"other / self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x / y, other, self)\\n    return Map(lambda x: other / x, self)\\n\\n  def __invert__(self) -> \"SOp\":\\n    return Map(lambda x: not x, self)\\n\\n  def __and__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"self & other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x and y, self, other)\\n    return Map(lambda x: x and other, self)\\n\\n  def __or__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"self | other.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x or y, self, other)\\n    return Map(lambda x: x or other, self)\\n\\n  def __rand__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"other & self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x and y, other, self)\\n    return Map(lambda x: other and x, self)\\n\\n  def __ror__(self, other: Union[\"SOp\", NumericValue]) -> \"SOp\":\\n    \"\"\"other | self.\"\"\"\\n    if isinstance(other, SOp):\\n      return SequenceMap(lambda x, y: x or y, other, self)\\n    return Map(lambda x: x or other, self)\\n\\n\\nclass TokensType(SOp):\\n  \"\"\"Primitive SOp returning the original input tokens.\"\"\"\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n  @property\\n  def label(self) -> str:\\n    return \"tokens\"\\n\\n  def __repr__(self):\\n    return \"tokens\"\\n\\n\\nclass IndicesType(SOp):\\n  \"\"\"Primitive SOp returning the position index at each token.\"\"\"\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n  @property\\n  def label(self) -> str:\\n    return \"indices\"\\n\\n  def __repr__(self):\\n    return \"indices\"\\n\\n\\nclass LengthType(SOp):\\n  \"\"\"Primitive SOp returning the total length of the input.\"\"\"\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n  @property\\n  def label(self) -> str:\\n    return \"length\"\\n\\n  def __repr__(self):\\n    return \"length\"\\n\\n\\ntokens = TokensType()\\nindices = IndicesType()\\nlength = LengthType()\\n\\n\\nclass Map(SOp):\\n  \"\"\"SOp that evaluates the function elementwise on the input SOp.\\n\\n  Map(lambda x: x + 1, tokens).eval([1, 2, 3]) == [2, 3, 4]\\n  \"\"\"\\n\\n  def __init__(self, f: Callable[[Value], Value], inner: SOp):\\n    super().__init__()\\n    self.f = f\\n    self.inner = inner\\n\\n    assert isinstance(self.inner, SOp)\\n    assert callable(self.f) and not isinstance(self.f, RASPExpr)\\n\\n    if isinstance(self.inner, Map):\\n      # Combine the functions into just one.\\n      inner_f = self.inner.f\\n      self.f = lambda t: f(inner_f(t))\\n      self.inner = self.inner.inner\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.inner]\\n\\n\\nclass SequenceMap(SOp):\\n  \"\"\"SOp that evaluates the function elementwise on the two given SOp\\'s.\\n\\n  SequenceMap(lambda x, y: x - y, length, tokens).eval([1, 2, 3]) == [2, 1, 0]\\n  \"\"\"\\n\\n  def __init__(self, f: Callable[[Value, Value], Value], fst: SOp, snd: SOp):\\n    super().__init__()\\n\\n    if fst == snd:\\n      logging.warning(\"Creating a SequenceMap with both inputs being the same \"\\n                      \"SOp is discouraged. You should use a Map instead.\")\\n\\n    self.f = f\\n    self.fst = fst\\n    self.snd = snd\\n    assert isinstance(self.fst, SOp)\\n    assert isinstance(self.snd, SOp)\\n    assert callable(self.f) and not isinstance(self.f, RASPExpr)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.fst, self.snd]\\n\\n\\nclass LinearSequenceMap(SequenceMap):\\n  \"\"\"SOp that evaluates a linear function elementwise on the two given SOp\\'s.\"\"\"\\n\\n  def __init__(self, fst: SOp, snd: SOp, fst_fac: float, snd_fac: float):\\n    super().__init__(fst=fst, snd=snd, f=lambda x, y: fst_fac * x + snd_fac * y)\\n    self.fst_fac = fst_fac\\n    self.snd_fac = snd_fac\\n\\n\\nclass Full(SOp):\\n  \"\"\"A SOp evaluating to [fill]*len(input_values).\"\"\"\\n\\n  def __init__(self, fill: Value):\\n    super().__init__()\\n    self.fill = fill\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n\\ndef sop_not(sop: SOp) -> SOp:\\n  return Map(lambda t: not t, sop)\\n\\n\\nclass ConstantSOp(SOp, Generic[VT]):\\n  \"\"\"A constant S-Op for testing purposes.\"\"\"\\n\\n  def __init__(self, value: Sequence[VT], check_length: bool = True):\\n    super().__init__()\\n    self.value = value\\n    self.check_length = check_length\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n\\n### Selectors.\\n\\n\\nclass Predicate(Protocol):\\n\\n  def __call__(self, key: Value, query: Value) -> bool:\\n    \"\"\"Applies the predicate.\"\"\"\\n\\n\\nclass Comparison(enum.Enum):\\n  \"\"\"A two-place boolean comparison predicate for use in Select.\"\"\"\\n  EQ = \"==\"\\n  LT = \"<\"\\n  LEQ = \"<=\"\\n  GT = \">\"\\n  GEQ = \">=\"\\n  NEQ = \"!=\"\\n  TRUE = \"True\"\\n  FALSE = \"False\"\\n\\n  def __call__(self, key: Value, query: Value) -> bool:\\n    if key is None:\\n      raise ValueError(\"key is None!\")\\n    if query is None:\\n      raise ValueError(\"query is None!\")\\n    return _comparison_table[self](key, query)\\n\\n\\n_comparison_table = {\\n    Comparison.EQ: lambda key, query: key == query,\\n    Comparison.LT: lambda key, query: key < query,\\n    Comparison.LEQ: lambda key, query: key <= query,\\n    Comparison.GT: lambda key, query: key > query,\\n    Comparison.GEQ: lambda key, query: key >= query,\\n    Comparison.NEQ: lambda key, query: key != query,\\n    Comparison.TRUE: lambda key, query: True,\\n    Comparison.FALSE: lambda key, query: False,\\n}\\n\\n\\nclass Selector(RASPExpr):\\n  \"\"\"RASP Selector. Represents something like an attention head\\'s weights.\"\"\"\\n\\n  def __call__(self, xs: Sequence[Value]) -> SelectorValue:\\n    return evaluate(self, xs)  # pytype: disable=bad-return-type\\n\\n  # Allow construction of Selector combinations using Python logical operators.\\n  def __and__(self, other: \"Selector\") -> \"Selector\":\\n    \"\"\"self & other.\"\"\"\\n    return selector_and(self, other)\\n\\n  def __rand__(self, other: \"Selector\") -> \"Selector\":\\n    \"\"\"other & self.\"\"\"\\n    return selector_and(other, self)\\n\\n  def __or__(self, other: \"Selector\") -> \"Selector\":\\n    \"\"\"self | other.\"\"\"\\n    return selector_or(self, other)\\n\\n  def __ror__(self, other: \"Selector\") -> \"Selector\":\\n    \"\"\"other | self.\"\"\"\\n    return selector_or(other, self)\\n\\n  def __invert__(self) -> \"Selector\":\\n    \"\"\"~self.\"\"\"\\n    return selector_not(self)\\n\\n\\nclass Select(Selector):\\n  \"\"\"Primitive that creates a Selector.\"\"\"\\n\\n  def __init__(self, keys: SOp, queries: SOp, predicate: Predicate):\\n    super().__init__()\\n    self.keys = keys\\n    self.queries = queries\\n    self.predicate = predicate\\n    assert isinstance(self.keys, SOp)\\n    assert isinstance(self.queries, SOp)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.keys, self.queries]\\n\\n\\nclass ConstantSelector(Selector):\\n  \"\"\"A constant selector for testing purposes.\"\"\"\\n\\n  def __init__(self, value: SelectorValue, check_length: bool = True):\\n    super().__init__()\\n    self.value = value\\n    self.check_length = check_length\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return []\\n\\n\\nclass SelectorWidth(SOp):\\n  \"\"\"SelectorWidth primitive.\"\"\"\\n\\n  def __init__(self, selector: Selector):\\n    super().__init__()\\n    self.selector = selector\\n    assert isinstance(self.selector, Selector)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.selector]\\n\\n\\nclass SelectorAnd(Selector):\\n  \"\"\"Implements elementwise `and` between selectors.\"\"\"\\n\\n  def __init__(self, fst: Selector, snd: Selector):\\n    super().__init__()\\n    self.fst = fst\\n    self.snd = snd\\n    assert isinstance(self.fst, Selector)\\n    assert isinstance(self.snd, Selector)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.fst, self.snd]\\n\\n\\nclass SelectorOr(Selector):\\n  \"\"\"Implements elementwise `or` between selectors.\"\"\"\\n\\n  def __init__(self, fst: Selector, snd: Selector):\\n    super().__init__()\\n    self.fst = fst\\n    self.snd = snd\\n    assert isinstance(self.fst, Selector)\\n    assert isinstance(self.snd, Selector)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.fst, self.snd]\\n\\n\\nclass SelectorNot(Selector):\\n  \"\"\"Implements elementwise `not` on a selector.\"\"\"\\n\\n  def __init__(self, inner: Selector):\\n    self.inner = inner\\n    super().__init__()\\n    assert isinstance(self.inner, Selector)\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.inner]\\n\\n\\ndef selector_not(\\n    inner: Selector,\\n    simplify: bool = True,\\n) -> Selector:\\n  \"\"\"Returns a SelectorNot, or a Select if simplifying is possible.\"\"\"\\n  if simplify and isinstance(inner, Select):\\n    predicate = lambda k, q: not inner.predicate(k, q)\\n    return Select(inner.keys, inner.queries, predicate=predicate)\\n\\n  return SelectorNot(inner)\\n\\n\\ndef selector_and(\\n    fst: Selector,\\n    snd: Selector,\\n    simplify: bool = True,\\n) -> Selector:\\n  \"\"\"Returns a SelectorAnd, or a Select if simplifying is possible.\"\"\"\\n  if simplify and isinstance(fst, Select) and isinstance(snd, Select):\\n    simplified = _attempt_simplify(fst, snd, lambda l, r: l and r)\\n    if simplified:\\n      return simplified\\n\\n  return SelectorAnd(fst, snd)\\n\\n\\ndef selector_or(\\n    fst: Selector,\\n    snd: Selector,\\n    simplify: bool = True,\\n) -> Selector:\\n  \"\"\"Returns a SelectorOr, or a Select if simplifying is possible.\"\"\"\\n  if simplify and isinstance(fst, Select) and isinstance(snd, Select):\\n    simplified = _attempt_simplify(fst, snd, lambda l, r: l or r)\\n    if simplified:\\n      return simplified\\n\\n  return SelectorOr(fst, snd)\\n\\n\\ndef _attempt_simplify(\\n    fst: Select,\\n    snd: Select,\\n    combine: Callable[[bool, bool], bool],\\n) -> Optional[Select]:\\n  \"\"\"Simplifies two Selects if possible.\\n\\n  If two Selects in a compound Selector have matching keys and queries, they can\\n  be simplified into one Select with a compound predicate:\\n\\n  lambda k,q: combine(fst.predicate(k,q), snd.predicate(k,q))\\n\\n  This function returns a Select with this predicate if possible,\\n  and None otherwise.\\n\\n  A Full SOp in a key or query position is a special case that always matches\\n  any SOp in the corresponding position in the other selector. In that case,\\n  we bake in the fill value into the corresponding Select\\'s predicate before\\n  combining. This allows us to use the other SOp as the input to the simplified\\n  Select.\\n\\n  Args:\\n    fst: the first Select.\\n    snd: the second Select.\\n    combine: how to combine the outputs of the individual predicates.\\n\\n  Returns:\\n    A combined Select, if possible.\\n  \"\"\"\\n  fst_predicate = fst.predicate\\n  snd_predicate = snd.predicate\\n  common_keys = None\\n  common_queries = None\\n\\n  if isinstance(fst.keys, Full):\\n    common_keys = snd.keys\\n    # We pass the predicate in as a default arg to avoid unintended recursion.\\n    fst_predicate = lambda key, query, p=fst_predicate: p(fst.keys.fill, query)\\n  if isinstance(snd.keys, Full):\\n    common_keys = fst.keys\\n    snd_predicate = lambda key, query, p=snd_predicate: p(snd.keys.fill, query)\\n  if isinstance(fst.queries, Full):\\n    common_queries = snd.queries\\n    fst_predicate = lambda key, query, p=fst_predicate: p(key, fst.queries.fill)\\n  if isinstance(snd.queries, Full):\\n    common_queries = fst.queries\\n    snd_predicate = lambda key, query, p=snd_predicate: p(key, snd.queries.fill)\\n  if fst.keys is snd.keys:\\n    common_keys = fst.keys\\n  if fst.queries is snd.queries:\\n    common_queries = fst.queries\\n\\n  if not common_keys or not common_queries:\\n    return None\\n\\n  def predicate(key, query):\\n    return combine(fst_predicate(key, query), snd_predicate(key, query))\\n\\n  return Select(common_keys, common_queries, predicate=predicate)\\n\\n\\nclass Aggregate(SOp, Generic[VT]):\\n  \"\"\"Aggregate primitive.\"\"\"\\n\\n  def __init__(self,\\n               selector: Selector,\\n               sop: SOp,\\n               default: Optional[VT] = None):\\n    \"\"\"Initialises. The default is used where nothing is selected.\"\"\"\\n    super().__init__()\\n    self.selector = selector\\n    self.sop = sop\\n    self.default = default\\n    assert isinstance(self.selector, Selector)\\n    assert isinstance(self.sop, SOp)\\n    assert (self.default is None or isinstance(self.default,\\n                                               (str, float, bool, int)))\\n\\n  @property\\n  def children(self) -> Sequence[RASPExpr]:\\n    return [self.selector, self.sop]\\n\\n\\n### SOp encodings.\\n\\n\\nclass Encoding(enum.Enum):\\n  \"\"\"The encoding used by a SOp. Only number-valued SOps support numerical.\"\"\"\\n  CATEGORICAL = \"categorical\"\\n  NUMERICAL = \"numerical\"\\n\\n\\ndef numerical(sop: SOpT) -> SOpT:\\n  return annotate(sop, encoding=Encoding.NUMERICAL)\\n\\n\\ndef categorical(sop: SOpT) -> SOpT:\\n  return annotate(sop, encoding=Encoding.CATEGORICAL)\\n\\n\\ndef get_encoding(sop: SOp) -> Encoding:\\n  return sop.annotations[\"encoding\"]\\n\\n\\ndef is_numerical(sop: SOp) -> bool:\\n  \"\"\"Check if the SOp is numerically encoded.\"\"\"\\n  return get_encoding(sop) == Encoding.NUMERICAL\\n\\n\\ndef is_categorical(sop: SOp) -> bool:\\n  \"\"\"Check if the SOp is categorically encoded.\"\"\"\\n  return get_encoding(sop) == Encoding.CATEGORICAL\\n\\n\\ndef default_encoding(expr: RASPExpr) -> Optional[Encoding]:\\n  \"\"\"Adds an \\'encoding\\' annotation, default is Categorical.\"\"\"\\n  if not isinstance(expr, SOp):\\n    raise TypeError(f\"expr {expr} is not a SOp.\")\\n\\n  return Encoding.CATEGORICAL\\n\\n\\nDEFAULT_ANNOTATORS[_ENCODING_KEY] = default_encoding\\n\\n### naming.\\n\\n# Subclasses must appear here before superclasses in order for\\n# the most specific entry to be used.\\n\\n_default_name_by_class = {\\n    # Primitives\\n    TokensType: \"tokens\",\\n    IndicesType: \"indices\",\\n    LengthType: \"length\",\\n    # SOps\\n    LinearSequenceMap: \"linear_sequence_map\",\\n    SequenceMap: \"sequence_map\",\\n    Map: \"map\",\\n    Full: \"full\",\\n    ConstantSOp: \"constant_sop\",\\n    SelectorWidth: \"selector_width\",\\n    Aggregate: \"aggregate\",\\n    SOp: \"sop\",\\n    # Selectors\\n    Select: \"select\",\\n    SelectorAnd: \"selector_and\",\\n    SelectorOr: \"selector_or\",\\n    SelectorNot: \"selector_not\",\\n    ConstantSelector: \"constant_selector\",\\n    Selector: \"selector\",\\n}\\n\\n\\ndef default_name(expr: RASPExpr) -> Dict[str, str]:\\n  for cls, name in _default_name_by_class.items():\\n    if isinstance(expr, cls):\\n      return name\\n\\n  raise NotImplementedError(f\"{expr} was not given a default name!\")\\n\\n\\nDEFAULT_ANNOTATORS[_NAME_KEY] = default_name\\n\\n### evaluation.\\n\\n\\nclass RASPEvaluator(abc.ABC):\\n  \"\"\"ABC for RASP evaluators.\"\"\"\\n\\n  @abc.abstractmethod\\n  def evaluate(self, expr: RASPExpr,\\n               xs: Sequence[Value]) -> Union[Sequence[Value], SelectorValue]:\\n    \"\"\"Evaluates the RASP expression on input `xs`.\"\"\"\\n\\n\\nclass DefaultRASPEvaluator(abc.ABC):\\n  \"\"\"Default evaluator for RASP.\"\"\"\\n\\n  def evaluate(self, expr: RASPExpr,\\n               xs: Sequence[Value]) -> Union[Sequence[Value], SelectorValue]:\\n    \"\"\"Evaluates the RASP expression on input `xs`.\"\"\"\\n    return self._eval_fn_by_expr_type[type(expr)](expr, xs)\\n\\n  def __init__(self):\\n    self._eval_fn_by_expr_type = {\\n        # Primitives\\n        TokensType: self.eval_tokens,\\n        IndicesType: self.eval_indices,\\n        LengthType: self.eval_length,\\n        # SOps\\n        LinearSequenceMap: self.eval_sequence_map,\\n        SequenceMap: self.eval_sequence_map,\\n        Map: self.eval_map,\\n        Full: self.eval_full,\\n        ConstantSOp: self.eval_constant_sop,\\n        SelectorWidth: self.eval_selector_width,\\n        Aggregate: self.eval_aggregate,\\n        SOp: _raise_not_implemented,\\n        # Selectors\\n        Select: self.eval_select,\\n        SelectorAnd: self.eval_selector_and,\\n        SelectorOr: self.eval_selector_or,\\n        SelectorNot: self.eval_selector_not,\\n        ConstantSelector: self.eval_constant_selector,\\n        Selector: _raise_not_implemented,\\n    }\\n\\n  def eval_tokens(self, sop: TokensType,\\n                  xs: Sequence[Value]) -> Sequence[Value]:\\n    del sop\\n    return list(xs)\\n\\n  def eval_indices(self, sop: IndicesType,\\n                   xs: Sequence[Value]) -> Sequence[Value]:\\n    del sop\\n    return list(range(len(xs)))\\n\\n  def eval_length(self, sop: LengthType, xs: Sequence[Value]) -> Sequence[int]:\\n    del sop\\n    return [len(xs)] * len(xs)\\n\\n  def eval_sequence_map(self, sop: SequenceMap,\\n                        xs: Sequence[Value]) -> Sequence[Value]:\\n    fst_values = self.evaluate(sop.fst, xs)\\n    snd_values = self.evaluate(sop.snd, xs)\\n    return [\\n        sop.f(x, y) if None not in [x, y] else None\\n        for x, y in zip(fst_values, snd_values)\\n    ]\\n\\n  def eval_map(self, sop: Map, xs: Sequence[Value]) -> Sequence[Value]:\\n    return [\\n        sop.f(x) if x is not None else None\\n        for x in self.evaluate(sop.inner, xs)\\n    ]\\n\\n  def eval_full(self, sop: Full, xs: Sequence[Value]) -> Sequence[Value]:\\n    return [sop.fill] * len(xs)\\n\\n  def eval_constant_sop(self, sop: ConstantSOp,\\n                        xs: Sequence[Value]) -> Sequence[Value]:\\n    if sop.check_length and (len(xs) != len(sop.value)):\\n      raise ValueError(\\n          f\"Constant len {len(sop.value)} doesn\\'t match input len {len(xs)}.\")\\n    return sop.value\\n\\n  def eval_selector_width(self, sop: SelectorWidth,\\n                          xs: Sequence[Value]) -> Sequence[Value]:\\n    selector_values = self.evaluate(sop.selector, xs)\\n    return [sum(row) for row in selector_values]\\n\\n  def eval_aggregate(self, sop: Aggregate,\\n                     xs: Sequence[Value]) -> Sequence[Value]:\\n    selector_value = self.evaluate(sop.selector, xs)\\n    values = self.evaluate(sop.sop, xs)\\n    default = sop.default\\n\\n    return [\\n        _mean(_get_selected(row, values), default) for row in selector_value\\n    ]\\n\\n  def eval_select(self, sel: Select, xs: Sequence[Value]) -> SelectorValue:\\n    \"\"\"Evaluates a Select on `xs`.\"\"\"\\n    key_values = self.evaluate(sel.keys, xs)\\n    query_values = self.evaluate(sel.queries, xs)\\n\\n    key_len = len(key_values)\\n    query_len = len(query_values)\\n    out = np.zeros((query_len, key_len), dtype=bool).tolist()\\n    for row, query in enumerate(query_values):\\n      for col, key in enumerate(key_values):\\n        out[row][col] = bool(sel.predicate(key, query))\\n    return out\\n\\n  def eval_constant_selector(self, sel: ConstantSelector,\\n                             xs: Sequence[Value]) -> SelectorValue:\\n    if sel.check_length and (len(xs) != len(sel.value)):\\n      raise ValueError(\\n          f\"Constant len {len(xs)} doesn\\'t match input len {len(sel.value)}.\")\\n    return sel.value\\n\\n  def eval_selector_and(self, sel: SelectorAnd,\\n                        xs: Sequence[Value]) -> SelectorValue:\\n    fst_values = self.evaluate(sel.fst, xs)\\n    snd_values = self.evaluate(sel.snd, xs)\\n    return np.logical_and(np.array(fst_values), np.array(snd_values)).tolist()\\n\\n  def eval_selector_or(self, sel: SelectorOr,\\n                       xs: Sequence[Value]) -> SelectorValue:\\n    fst_values = self.evaluate(sel.fst, xs)\\n    snd_values = self.evaluate(sel.snd, xs)\\n    return np.logical_or(np.array(fst_values), np.array(snd_values)).tolist()\\n\\n  def eval_selector_not(self, sel: SelectorNot,\\n                        xs: Sequence[Value]) -> SelectorValue:\\n    values = self.evaluate(sel.inner, xs)\\n    return np.logical_not(np.array(values)).tolist()\\n\\n\\ndef _get_selected(\\n    selector_row: List[bool],\\n    values: Sequence[VT],\\n) -> Sequence[VT]:\\n  \"\"\"Helper for aggregate. [T T F], [a b c] -> [a b].\"\"\"\\n  return [v for s, v in zip(selector_row, values) if s]\\n\\n\\ndef _mean(xs: Sequence[VT], default: VT) -> VT:\\n  \"\"\"Takes the mean for numbers and concats for strings.\"\"\"\\n  if not xs:\\n    return default\\n  exemplar = xs[0]\\n  if isinstance(exemplar, (int, bool)):\\n    return sum(xs) / len(xs)\\n  elif len(xs) == 1:\\n    return exemplar\\n  else:\\n    raise ValueError(f\"Unsupported type for aggregation: {xs}\")\\n\\n\\ndef _raise_not_implemented(expr: RASPExpr, xs: Sequence[Value]):\\n  raise NotImplementedError(f\"Evaluation of {expr} is not defined.\")\\n\\n\\nevaluate = DefaultRASPEvaluator().evaluate\\n', path=PosixPath('tracr/rasp/rasp.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8660e30>, scope_tree=SrcScope(name='tracr/rasp/rasp.py', kind='file', range=0:27644, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=27644:27644, code=''), len(children)=50)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"RASP Evaluator which applies causal masks to selectors.\"\"\"\\n\\nfrom typing import Sequence, Union\\n\\nimport numpy as np\\nfrom tracr.rasp import rasp\\n\\n\\nclass CausalEvaluator(rasp.DefaultRASPEvaluator):\\n  \"\"\"Evaluates RASP with causal masking.\"\"\"\\n\\n  def evaluate(\\n      self, expr: rasp.RASPExpr, xs: Sequence[rasp.Value]\\n  ) -> Union[Sequence[rasp.Value], rasp.SelectorValue]:\\n    out = super().evaluate(expr, xs)\\n\\n    if not isinstance(expr, rasp.Selector):\\n      return out\\n\\n    out = np.array(out)\\n    causal_mask = np.tril(np.full(out.shape, 1))\\n    return np.logical_and(causal_mask, out).tolist()\\n\\n\\nevaluate = CausalEvaluator().evaluate\\n', path=PosixPath('tracr/rasp/causal_eval.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8614f30>, scope_tree=SrcScope(name='tracr/rasp/causal_eval.py', kind='file', range=0:1335, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1335:1335, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/craft/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8460490>, scope_tree=SrcScope(name='tracr/craft/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Vectors and bases.\"\"\"\\n\\nimport dataclasses\\nfrom typing import Sequence, Union, Optional, Iterable\\n\\nimport numpy as np\\n\\nName = Union[int, str]\\nValue = Union[int, float, bool, str, tuple]\\n\\n\\<EMAIL>(frozen=True)\\nclass BasisDirection:\\n  \"\"\"Represents a basis direction (no magnitude) in a vector space.\\n\\n  Attributes:\\n    name: a unique name for this direction.\\n    value: used to hold a value one-hot-encoded by this direction. e.g.,\\n      [BasisDirection(\"vs_1\", True), BasisDirection(\"vs_1\", False)] would be\\n      basis directions of a subspace called \"vs_1\" which one-hot-encodes the\\n      values True and False. If provided, considered part of the name for the\\n      purpose of disambiguating directions.\\n  \"\"\"\\n  name: Name\\n  value: Optional[Value] = None\\n\\n  def __str__(self):\\n    if self.value is None:\\n      return str(self.name)\\n    return f\"{self.name}:{self.value}\"\\n\\n  def __lt__(self, other: \"BasisDirection\") -> bool:\\n    try:\\n      return (self.name, self.value) < (other.name, other.value)\\n    except TypeError:\\n      return str(self) < str(other)\\n\\n\\<EMAIL>\\nclass VectorInBasis:\\n  \"\"\"A vector (or array of vectors) in a given basis.\\n\\n  When magnitudes are 1-d, this is a vector.\\n  When magnitudes are (n+1)-d, this is an array of vectors,\\n  where the -1th dimension is the basis dimension.\\n  \"\"\"\\n  basis_directions: Sequence[BasisDirection]\\n  magnitudes: np.ndarray\\n\\n  def __post_init__(self):\\n    \"\"\"Sort basis directions.\"\"\"\\n    if len(self.basis_directions) != self.magnitudes.shape[-1]:\\n      raise ValueError(\\n          \"Last dimension of magnitudes must be the same as number \"\\n          f\"of basis directions. Was {len(self.basis_directions)} \"\\n          f\"and {self.magnitudes.shape[-1]}.\")\\n\\n    sort_idx = np.argsort(self.basis_directions)\\n    self.basis_directions = [self.basis_directions[i] for i in sort_idx]\\n    self.magnitudes = np.take(self.magnitudes, sort_idx, -1)\\n\\n  def __add__(self, other: \"VectorInBasis\") -> \"VectorInBasis\":\\n    if self.basis_directions != other.basis_directions:\\n      raise TypeError(f\"Adding incompatible bases: {self} + {other}\")\\n    magnitudes = self.magnitudes + other.magnitudes\\n    return VectorInBasis(self.basis_directions, magnitudes)\\n\\n  def __radd__(self, other: \"VectorInBasis\") -> \"VectorInBasis\":\\n    if self.basis_directions != other.basis_directions:\\n      raise TypeError(f\"Adding incompatible bases: {other} + {self}\")\\n    return self + other\\n\\n  def __sub__(self, other: \"VectorInBasis\") -> \"VectorInBasis\":\\n    if self.basis_directions != other.basis_directions:\\n      raise TypeError(f\"Subtracting incompatible bases: {self} - {other}\")\\n    magnitudes = self.magnitudes - other.magnitudes\\n    return VectorInBasis(self.basis_directions, magnitudes)\\n\\n  def __rsub__(self, other: \"VectorInBasis\") -> \"VectorInBasis\":\\n    if self.basis_directions != other.basis_directions:\\n      raise TypeError(f\"Subtracting incompatible bases: {other} - {self}\")\\n    magnitudes = other.magnitudes - self.magnitudes\\n    return VectorInBasis(self.basis_directions, magnitudes)\\n\\n  def __mul__(self, scalar: float) -> \"VectorInBasis\":\\n    return VectorInBasis(self.basis_directions, self.magnitudes * scalar)\\n\\n  def __rmul__(self, scalar: float) -> \"VectorInBasis\":\\n    return self * scalar\\n\\n  def __truediv__(self, scalar: float) -> \"VectorInBasis\":\\n    return VectorInBasis(self.basis_directions, self.magnitudes / scalar)\\n\\n  def __neg__(self) -> \"VectorInBasis\":\\n    return (-1) * self\\n\\n  def __eq__(self, other: \"VectorInBasis\") -> bool:\\n    return ((self.basis_directions == other.basis_directions) and\\n            (self.magnitudes.shape == other.magnitudes.shape) and\\n            (np.all(self.magnitudes == other.magnitudes)))\\n\\n  @classmethod\\n  def sum(cls, vectors: Sequence[\"VectorInBasis\"]) -> \"VectorInBasis\":\\n    return cls(vectors[0].basis_directions,\\n               np.sum([x.magnitudes for x in vectors], axis=0))\\n\\n  @classmethod\\n  def stack(cls,\\n            vectors: Sequence[\"VectorInBasis\"],\\n            axis: int = 0) -> \"VectorInBasis\":\\n    for v in vectors[1:]:\\n      if v.basis_directions != vectors[0].basis_directions:\\n        raise TypeError(f\"Stacking incompatible bases: {vectors[0]} + {v}\")\\n    return cls(vectors[0].basis_directions,\\n               np.stack([v.magnitudes for v in vectors], axis=axis))\\n\\n  def project(\\n      self, basis: Union[\"VectorSpaceWithBasis\", Sequence[BasisDirection]]\\n  ) -> \"VectorInBasis\":\\n    \"\"\"Projects to the basis.\"\"\"\\n    if isinstance(basis, VectorSpaceWithBasis):\\n      basis = basis.basis\\n    components = []\\n    for direction in basis:\\n      if direction in self.basis_directions:\\n        components.append(\\n            self.magnitudes[..., self.basis_directions.index(direction)])\\n      else:\\n        components.append(np.zeros_like(self.magnitudes[..., 0]))\\n    return VectorInBasis(list(basis), np.stack(components, axis=-1))\\n\\n\\<EMAIL>\\nclass VectorSpaceWithBasis:\\n  \"\"\"A vector subspace in a given basis.\"\"\"\\n  basis: Sequence[BasisDirection]\\n\\n  def __post_init__(self):\\n    \"\"\"Keep basis directions sorted.\"\"\"\\n    self.basis = sorted(self.basis)\\n\\n  @property\\n  def num_dims(self) -> int:\\n    return len(self.basis)\\n\\n  def __contains__(self, item: Union[VectorInBasis, BasisDirection]) -> bool:\\n    if isinstance(item, BasisDirection):\\n      return item in self.basis\\n\\n    return set(self.basis) == set(item.basis_directions)\\n\\n  def issubspace(self, other: \"VectorSpaceWithBasis\") -> bool:\\n    return set(self.basis).issubset(set(other.basis))\\n\\n  def basis_vectors(self) -> Sequence[VectorInBasis]:\\n    basis_vector_magnitudes = list(np.eye(self.num_dims))\\n    return [VectorInBasis(self.basis, m) for m in basis_vector_magnitudes]\\n\\n  def vector_from_basis_direction(\\n      self, basis_direction: BasisDirection) -> VectorInBasis:\\n    i = self.basis.index(basis_direction)\\n    return VectorInBasis(self.basis, np.eye(self.num_dims)[i])\\n\\n  def null_vector(self) -> VectorInBasis:\\n    return VectorInBasis(self.basis, np.zeros(self.num_dims))\\n\\n  @classmethod\\n  def from_names(cls, names: Sequence[Name]) -> \"VectorSpaceWithBasis\":\\n    \"\"\"Creates a VectorSpace from a list of names for its basis directions.\"\"\"\\n    return cls([BasisDirection(n) for n in names])\\n\\n  @classmethod\\n  def from_values(\\n      cls,\\n      name: Name,\\n      values: Iterable[Value],\\n  ) -> \"VectorSpaceWithBasis\":\\n    \"\"\"Creates a VectorSpace from a list of values for its basis directions.\"\"\"\\n    return cls([BasisDirection(name, v) for v in values])\\n\\n\\ndef direct_sum(*vs: VectorSpaceWithBasis) -> VectorSpaceWithBasis:\\n  \"\"\"Create a direct sum of the vector spaces.\\n\\n  Assumes the basis elements of all input vector spaces are\\n  orthogonal to each other. Maintains the order of the bases.\\n\\n  Args:\\n    *vs: the vector spaces to sum.\\n\\n  Returns:\\n    the combined vector space.\\n\\n  Raises:\\n    Value error in case of overlapping bases.\\n  \"\"\"\\n  # Take the union of all the bases:\\n  total_basis = sum([v.basis for v in vs], [])\\n\\n  if len(total_basis) != len(set(total_basis)):\\n    raise ValueError(\"Overlapping bases!\")\\n\\n  return VectorSpaceWithBasis(total_basis)\\n\\n\\ndef join_vector_spaces(*vs: VectorSpaceWithBasis) -> VectorSpaceWithBasis:\\n  \"\"\"Joins a set of vector spaces allowing them to overlap.\\n\\n  Assumes the basis elements of all input vector spaces are\\n  orthogonal to each other. Does not maintain the order of the bases but\\n  sorts them.\\n\\n  Args:\\n    *vs: the vector spaces to sum.\\n\\n  Returns:\\n    the combined vector space.\\n  \"\"\"\\n  # Take the union of all the bases:\\n  total_basis = list(set().union(*[set(v.basis) for v in vs]))\\n  total_basis = sorted(total_basis)\\n  return VectorSpaceWithBasis(total_basis)\\n\\n\\ndef ensure_dims(\\n    vs: VectorSpaceWithBasis,\\n    num_dims: int,\\n    name: str = \"vector space\",\\n) -> None:\\n  \"\"\"Raises ValueError if vs has the wrong number of dimensions.\"\"\"\\n  if vs.num_dims != num_dims:\\n    raise ValueError(f\"{name} must have num_dims={num_dims}, \"\\n                     f\"but got {vs.num_dims}: {vs.basis}\")\\n', path=PosixPath('tracr/craft/bases.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8460710>, scope_tree=SrcScope(name='tracr/craft/bases.py', kind='file', range=0:8682, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=8682:8682, code=''), len(children)=7)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Pieces for making transformers.\"\"\"\\n\\nimport abc\\nimport dataclasses\\nfrom typing import Iterable, List, Optional, Sequence, Union\\n\\nimport numpy as np\\n\\nfrom tracr.craft import bases\\nfrom tracr.craft import vectorspace_fns\\n\\nproject = vectorspace_fns.project\\n\\n\\ndef _np_softmax(x, axis=-1):\\n  x_max = np.max(x, axis=axis, keepdims=True)\\n  return np.exp(x - x_max) / np.sum(np.exp(x - x_max), axis=axis, keepdims=True)\\n\\n\\ndef _np_relu(x):\\n  return np.where(x > 0, x, 0)\\n\\n\\ndef relu(x: bases.VectorInBasis) -> bases.VectorInBasis:\\n  return bases.VectorInBasis(x.basis_directions, _np_relu(x.magnitudes))\\n\\n\\nclass Block(abc.ABC):\\n  \"\"\"Transformer block, acting on a sequence of vector space elements.\\n\\n  Attributes:\\n    residual_space: Vector space that contains all subspaces the Block interacts\\n      with. This can be either the full residual space of a model or a subspace.\\n  \"\"\"\\n  residual_space: bases.VectorSpaceWithBasis\\n\\n  @abc.abstractmethod\\n  def apply(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    \"\"\"Applies self to an input.\"\"\"\\n\\n\\<EMAIL>\\nclass AttentionHead(Block):\\n  \"\"\"A transformer attention head.\"\"\"\\n  w_qk: vectorspace_fns.ScalarBilinear\\n  w_ov: vectorspace_fns.Linear\\n  residual_space: Optional[bases.VectorSpaceWithBasis] = None\\n  causal: bool = False\\n\\n  def __post_init__(self):\\n    \"\"\"Infer residual stream and typecheck subspaces.\"\"\"\\n    if self.residual_space is None:\\n      self.residual_space = bases.join_vector_spaces(self.w_qk.left_space,\\n                                                     self.w_qk.right_space,\\n                                                     self.w_ov.input_space,\\n                                                     self.w_ov.output_space)\\n\\n    assert self.w_qk.left_space.issubspace(self.residual_space)\\n    assert self.w_qk.right_space.issubspace(self.residual_space)\\n    assert self.w_ov.input_space.issubspace(self.residual_space)\\n    assert self.w_ov.output_space.issubspace(self.residual_space)\\n\\n  def apply(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    assert x in self.residual_space\\n    # seq_len x query_space\\n    queries = x.project(self.w_qk.left_space)\\n    # seq_len x key_space\\n    keys = x.project(self.w_qk.right_space)\\n\\n    attn_matrix = queries.magnitudes @ self.w_qk.matrix @ keys.magnitudes.T\\n\\n    if self.causal:\\n      # The 1 gives us the matrix above the diagonal.\\n      mask = np.triu(np.full_like(attn_matrix, -np.inf), 1)\\n      attn_matrix = attn_matrix + mask\\n\\n    attn_weights = _np_softmax(attn_matrix)  # seq_len_from, seq_len_to\\n    values = self.w_ov_residual(x).magnitudes  # seq_len_to, d_model\\n\\n    magnitudes = attn_weights @ values  # seq_len_from, d_model\\n    return bases.VectorInBasis(sorted(self.residual_space.basis), magnitudes)\\n\\n  def w_ov_residual(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    \"\"\"Wov but acting on the residual space.\"\"\"\\n    x = project(self.residual_space, self.w_ov.input_space)(x)\\n    out = self.w_ov(x)\\n    return project(self.w_ov.output_space, self.residual_space)(out)\\n\\n  @property\\n  def num_heads(self) -> int:\\n    return 1\\n\\n  def as_multi(self) -> \"MultiAttentionHead\":\\n    return MultiAttentionHead([self])\\n\\n\\<EMAIL>\\nclass MultiAttentionHead(Block):\\n  \"\"\"Applies attention heads in parallel.\"\"\"\\n  sub_blocks: List[Union[AttentionHead, \"MultiAttentionHead\"]]\\n\\n  def __post_init__(self):\\n    spaces = [block.residual_space for block in self.sub_blocks]\\n    self.residual_space, *others = spaces\\n    assert all(s == self.residual_space for s in others)\\n\\n  def apply(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    # each element is seq_len x embedding\\n    outs = [block.apply(x) for block in self.sub_blocks]\\n    return bases.VectorInBasis.sum(outs)  # seq_len x embedding\\n\\n  @property\\n  def num_heads(self) -> int:\\n    return sum(sub_block.num_heads for sub_block in self.sub_blocks)\\n\\n  def heads(self) -> Iterable[AttentionHead]:\\n    for sub_block in self.sub_blocks:\\n      if isinstance(sub_block, AttentionHead):\\n        yield sub_block\\n      elif isinstance(sub_block, MultiAttentionHead):\\n        yield from sub_block.heads()\\n      else:\\n        raise NotImplementedError()\\n\\n  def as_multi(self) -> \"MultiAttentionHead\":\\n    return self\\n\\n\\<EMAIL>\\nclass MLP(Block):\\n  \"\"\"A transformer MLP block.\"\"\"\\n  fst: vectorspace_fns.Linear\\n  snd: vectorspace_fns.Linear\\n  residual_space: Optional[bases.VectorSpaceWithBasis] = None\\n\\n  def __post_init__(self):\\n    \"\"\"Typecheck subspaces.\"\"\"\\n    if self.residual_space is None:\\n      self.residual_space = bases.join_vector_spaces(self.fst.input_space,\\n                                                     self.snd.output_space)\\n\\n    assert self.fst.output_space == self.snd.input_space\\n    assert self.fst.input_space.issubspace(self.residual_space)\\n    assert self.snd.output_space.issubspace(self.residual_space)\\n\\n  def apply(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    assert x in self.residual_space\\n\\n    x = project(self.residual_space, self.fst.input_space)(x)\\n    hidden = self.fst(x)\\n    hidden = relu(hidden)\\n    out = self.snd(hidden)\\n    return project(self.snd.output_space, self.residual_space)(out)\\n\\n  @classmethod\\n  def combine_in_parallel(cls, mlps: Sequence[\"MLP\"]) -> \"MLP\":\\n    fst = vectorspace_fns.Linear.combine_in_parallel(\\n        [block.fst for block in mlps])\\n    snd = vectorspace_fns.Linear.combine_in_parallel(\\n        [block.snd for block in mlps])\\n    return cls(fst=fst, snd=snd, residual_space=None)\\n\\n\\n# Block that fits into a half-layer, without residual connections.\\nHalfLayerBlock = Union[MLP, AttentionHead, MultiAttentionHead]\\n\\n\\<EMAIL>\\nclass SeriesWithResiduals(Block):\\n  \"\"\"A series of blocks with residual connections.\"\"\"\\n  blocks: List[HalfLayerBlock]\\n\\n  def __post_init__(self):\\n    spaces = [block.residual_space for block in self.blocks]\\n    self.residual_space = bases.join_vector_spaces(*spaces)\\n\\n  def apply(self, x: bases.VectorInBasis) -> bases.VectorInBasis:\\n    x = x.project(self.residual_space)\\n    for block in self.blocks:\\n      x_in = x.project(block.residual_space)\\n      x_out = block.apply(x_in).project(self.residual_space)\\n      x = x + x_out\\n    return x\\n', path=PosixPath('tracr/craft/transformers.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b84053b0>, scope_tree=SrcScope(name='tracr/craft/transformers.py', kind='file', range=0:6895, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6895:6895, code=''), len(children)=10)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for transformers.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns as vs_fns\\n\\n# This makes it easier to use comments to annotate dimensions in arrays\\n# pylint: disable=g-no-space-after-comment\\n\\n\\nclass AttentionHeadTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.parameters([\\n      dict(with_residual_stream=False),\\n      dict(with_residual_stream=True),\\n  ])\\n  def test_attention_head(self, with_residual_stream):\\n    i = bases.VectorSpaceWithBasis.from_values(\"i\", [1, 2])\\n    o = bases.VectorSpaceWithBasis.from_values(\"o\", [1, 2])\\n    q = bases.VectorSpaceWithBasis.from_values(\"q\", [1, 2])\\n    k = bases.VectorSpaceWithBasis.from_values(\"p\", [1, 2])\\n    rs = bases.direct_sum(i, o, q, k)\\n\\n    seq = bases.VectorInBasis(\\n        rs.basis,\\n        np.array([\\n            #i1 i2 o1 o2 q1 q2 p1 p2\\n            [1, 0, 0, 0, 1, 0, 1, 0],\\n            [0, 1, 0, 0, 0, 1, 0, 1],\\n        ]))\\n\\n    head = transformers.AttentionHead(\\n        w_qk=vs_fns.ScalarBilinear(q, k,\\n                                   np.eye(2) * 100),\\n        w_ov=vs_fns.Linear(i, o, np.eye(2)),\\n        residual_space=rs if with_residual_stream else None,\\n        causal=False,\\n    )\\n\\n    self.assertVectorAllClose(\\n        head.apply(seq),\\n        bases.VectorInBasis(\\n            rs.basis,\\n            np.array([\\n                #i1 i2 o1 o2 q1 q2 p1 p2\\n                [0, 0, 1, 0, 0, 0, 0, 0],\\n                [0, 0, 0, 1, 0, 0, 0, 0],\\n            ])),\\n    )\\n\\n\\nclass MLPTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.parameters([\\n      dict(with_residual_stream=False, same_in_out=False),\\n      dict(with_residual_stream=False, same_in_out=True),\\n      dict(with_residual_stream=True, same_in_out=False),\\n      dict(with_residual_stream=True, same_in_out=True),\\n  ])\\n  def test_mlp(self, with_residual_stream, same_in_out):\\n    i = bases.VectorSpaceWithBasis.from_values(\"i\", [1, 2])\\n    if same_in_out:\\n      o, rs = i, i\\n      expected_result = np.array([\\n          #o1 o2\\n          [1, 0],\\n          [0, 1],\\n      ])\\n    else:\\n      o = bases.VectorSpaceWithBasis.from_values(\"o\", [1, 2])\\n      rs = bases.direct_sum(i, o)\\n      expected_result = np.array([\\n          #i1 i2 o1 o2\\n          [0, 0, 1, 0],\\n          [0, 0, 0, 1],\\n      ])\\n    h = bases.VectorSpaceWithBasis.from_values(\"p\", [1, 2])\\n\\n    seq = bases.VectorInBasis(\\n        i.basis,\\n        np.array([\\n            #i1  i2\\n            [1, -1],\\n            [-1, 1],\\n        ])).project(rs)\\n\\n    mlp = transformers.MLP(\\n        fst=vs_fns.Linear(i, h, np.eye(2)),\\n        snd=vs_fns.Linear(h, o, np.eye(2)),\\n        residual_space=rs if with_residual_stream else None,\\n    )\\n\\n    self.assertEqual(\\n        mlp.apply(seq),\\n        bases.VectorInBasis(rs.basis, expected_result),\\n    )\\n\\n  def test_combining_mlps(self):\\n    in12 = bases.VectorSpaceWithBasis.from_values(\"in\", [1, 2])\\n    in34 = bases.VectorSpaceWithBasis.from_values(\"in\", [3, 4])\\n    out12 = bases.VectorSpaceWithBasis.from_values(\"out\", [1, 2])\\n    residual_space = bases.join_vector_spaces(in12, in34, out12)\\n\\n    h1 = bases.VectorSpaceWithBasis.from_values(\"h\", [1])\\n    h2 = bases.VectorSpaceWithBasis.from_values(\"h\", [2])\\n\\n    # MLP1 maps in2 -> h1 -> out1\\n    mlp1 = transformers.MLP(\\n        fst=vs_fns.Linear(in12, h1, np.array([[0], [1]])),\\n        snd=vs_fns.Linear(h1, out12, np.array([[1, 0]])))\\n\\n    # MLP2 maps in3 -> h2 -> out2\\n    mlp2 = transformers.MLP(\\n        fst=vs_fns.Linear(in34, h2, np.array([[1], [0]])),\\n        snd=vs_fns.Linear(h2, out12, np.array([[0, 1]])))\\n\\n    mlp = transformers.MLP.combine_in_parallel([mlp1, mlp2])\\n\\n    seq = bases.VectorInBasis(\\n        bases.direct_sum(in12, in34).basis,\\n        np.array([\\n            #i1 i2 i3 i4\\n            [1, 2, 0, 0],\\n            [0, 2, 3, 4],\\n        ])).project(residual_space)\\n\\n    expected_result = bases.VectorInBasis(\\n        out12.basis,\\n        np.array([\\n            #o1 o2\\n            [2, 0],\\n            [2, 3],\\n        ]))\\n\\n    self.assertEqual(\\n        mlp.apply(seq).project(out12),\\n        expected_result,\\n    )\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/transformers_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b83c8190>, scope_tree=SrcScope(name='tracr/craft/transformers_test.py', kind='file', range=0:4970, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=4970:4970, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for bases.\"\"\"\\n\\nfrom absl.testing import absltest\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\n\\n\\nclass VectorInBasisTest(tests_common.VectorFnTestCase):\\n\\n  def test_shape_mismatch_raises_value_error(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    regex = (r\"^.*Last dimension of magnitudes must be the same as number of \"\\n             r\"basis directions.*$\")\\n    with self.assertRaisesRegex(ValueError, regex):\\n      bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    with self.assertRaisesRegex(ValueError, regex):\\n      bases.VectorInBasis(vs1.basis, np.array([[0, 1, 2, 3], [1, 2, 3, 4]]))\\n\\n  def test_equal(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    v1 = bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    v2 = bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    self.assertEqual(v1, v2)\\n    self.assertEqual(v2, v1)\\n    v3 = bases.VectorInBasis(vs1.basis, np.array([[0, 1, 2, 3], [1, 2, 3, 4]]))\\n    v4 = bases.VectorInBasis(vs1.basis, np.array([[0, 1, 2, 3], [1, 2, 3, 4]]))\\n    self.assertEqual(v3, v4)\\n    self.assertEqual(v4, v3)\\n    v5 = bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    v6 = bases.VectorInBasis(vs1.basis, np.array([1, 1, 1, 1]))\\n    self.assertNotEqual(v5, v6)\\n    self.assertNotEqual(v6, v5)\\n    v7 = bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    v8 = bases.VectorInBasis(vs1.basis, np.array([[1, 2, 3, 4], [1, 1, 1, 1]]))\\n    self.assertNotEqual(v7, v8)\\n    self.assertNotEqual(v8, v7)\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"e\", \"f\", \"g\", \"h\"])\\n    v9 = bases.VectorInBasis(vs1.basis, np.array([1, 2, 3, 4]))\\n    v10 = bases.VectorInBasis(vs2.basis, np.array([1, 2, 3, 4]))\\n    self.assertNotEqual(v9, v10)\\n    self.assertNotEqual(v10, v9)\\n\\n  def test_dunders(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\"])\\n    v = bases.VectorInBasis(vs1.basis, np.array([0, 1, 2]))\\n    three = bases.VectorInBasis(vs1.basis, np.array([3, 3, 3]))\\n    five = bases.VectorInBasis(vs1.basis, np.array([5, 5, 5]))\\n    v_times_5 = bases.VectorInBasis(vs1.basis, np.array([0, 5, 10]))\\n    self.assertEqual(5 * v, v_times_5)\\n    self.assertEqual(v * 5, v_times_5)\\n    self.assertEqual(5.0 * v, v_times_5)\\n    self.assertEqual(v * 5.0, v_times_5)\\n    v_by_2 = bases.VectorInBasis(vs1.basis, np.array([0, 0.5, 1]))\\n    self.assertEqual(v / 2, v_by_2)\\n    self.assertEqual(v / 2.0, v_by_2)\\n    self.assertEqual(1 / 2 * v, v_by_2)\\n    v_plus_3 = bases.VectorInBasis(vs1.basis, np.array([3, 4, 5]))\\n    self.assertEqual(v + three, v_plus_3)\\n    self.assertEqual(three + v, v_plus_3)\\n    v_minus_5 = bases.VectorInBasis(vs1.basis, np.array([-5, -4, -3]))\\n    self.assertEqual(v - five, v_minus_5)\\n    minus_v = bases.VectorInBasis(vs1.basis, np.array([0, -1, -2]))\\n    self.assertEqual(-v, minus_v)\\n\\n\\nclass ProjectionTest(tests_common.VectorFnTestCase):\\n\\n  def test_direct_sum_produces_expected_result(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"d\", \"c\"])\\n    vs3 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"d\", \"c\"])\\n    self.assertEqual(bases.direct_sum(vs1, vs2), vs3)\\n\\n  def test_join_vector_spaces_produces_expected_result(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"d\", \"c\"])\\n    vs3 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    self.assertEqual(bases.join_vector_spaces(vs1, vs2), vs3)\\n\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"b\", \"d\", \"c\"])\\n    vs3 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    self.assertEqual(bases.join_vector_spaces(vs1, vs2), vs3)\\n\\n  def test_compare_vectors_with_differently_ordered_basis_vectors(self):\\n    basis1 = [\"a\", \"b\", \"c\", \"d\"]\\n    basis1 = [bases.BasisDirection(x) for x in basis1]\\n    basis2 = [\"b\", \"d\", \"a\", \"c\"]\\n    basis2 = [bases.BasisDirection(x) for x in basis2]\\n    vs1 = bases.VectorSpaceWithBasis(basis1)\\n    vs2 = bases.VectorSpaceWithBasis(basis2)\\n    v1 = bases.VectorInBasis(basis1, np.array([1, 2, 3, 4]))\\n    v2 = bases.VectorInBasis(basis2, np.array([2, 4, 1, 3]))\\n    self.assertEqual(v1, v2)\\n    self.assertEqual(v1 - v2, vs1.null_vector())\\n    self.assertEqual(v1 - v2, vs2.null_vector())\\n    self.assertEqual(v1 + v2, 2 * v2)\\n    self.assertIn(v1, vs1)\\n    self.assertIn(v1, vs2)\\n    self.assertIn(v2, vs1)\\n    self.assertIn(v2, vs2)\\n\\n  def test_compare_vector_arrays_with_differently_ordered_basis_vectors(self):\\n    basis1 = [\"a\", \"b\", \"c\", \"d\"]\\n    basis1 = [bases.BasisDirection(x) for x in basis1]\\n    basis2 = [\"b\", \"d\", \"a\", \"c\"]\\n    basis2 = [bases.BasisDirection(x) for x in basis2]\\n    vs1 = bases.VectorSpaceWithBasis(basis1)\\n    vs2 = bases.VectorSpaceWithBasis(basis2)\\n    v1 = bases.VectorInBasis(basis1, np.array([[1, 2, 3, 4], [5, 6, 7, 8]]))\\n    v2 = bases.VectorInBasis(basis2, np.array([[2, 4, 1, 3], [6, 8, 5, 7]]))\\n    null_vec = bases.VectorInBasis.stack([vs1.null_vector(), vs2.null_vector()])\\n    self.assertEqual(v1, v2)\\n    self.assertEqual(v1 - v2, null_vec)\\n    self.assertEqual(v1 + v2, 2 * v2)\\n    self.assertIn(v1, vs1)\\n    self.assertIn(v1, vs2)\\n    self.assertIn(v2, vs1)\\n    self.assertIn(v2, vs2)\\n\\n  def test_projection_to_larger_space(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    a1, b1 = vs1.basis_vectors()\\n    a2, b2, _, _ = vs2.basis_vectors()\\n\\n    self.assertEqual(a1.project(vs2), a2)\\n    self.assertEqual(b1.project(vs2), b2)\\n\\n  def test_projection_to_smaller_space(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a1, b1, c1, d1 = vs1.basis_vectors()\\n    a2, b2 = vs2.basis_vectors()\\n\\n    self.assertEqual(a1.project(vs2), a2)\\n    self.assertEqual(b1.project(vs2), b2)\\n    self.assertEqual(c1.project(vs2), vs2.null_vector())\\n    self.assertEqual(d1.project(vs2), vs2.null_vector())\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/bases_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b83e9b90>, scope_tree=SrcScope(name='tracr/craft/bases_test.py', kind='file', range=0:6880, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6880:6880, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Functions on vector spaces.\"\"\"\\n\\nimport abc\\nimport dataclasses\\nfrom typing import Callable, Sequence\\n\\nimport numpy as np\\n\\nfrom tracr.craft import bases\\n\\nVectorSpaceWithBasis = bases.VectorSpaceWithBasis\\nVectorInBasis = bases.VectorInBasis\\nBasisDirection = bases.BasisDirection\\n\\n\\nclass VectorFunction(abc.ABC):\\n  \"\"\"A function that acts on vectors.\"\"\"\\n\\n  input_space: VectorSpaceWithBasis\\n  output_space: VectorSpaceWithBasis\\n\\n  @abc.abstractmethod\\n  def __call__(self, x: VectorInBasis) -> VectorInBasis:\\n    \"\"\"Evaluates the function.\"\"\"\\n\\n\\nclass Linear(VectorFunction):\\n  \"\"\"A linear function.\"\"\"\\n\\n  def __init__(\\n      self,\\n      input_space: VectorSpaceWithBasis,\\n      output_space: VectorSpaceWithBasis,\\n      matrix: np.ndarray,\\n  ):\\n    \"\"\"Initialises.\\n\\n    Args:\\n      input_space: The input vector space.\\n      output_space: The output vector space.\\n      matrix: a [input, output] matrix acting in a (sorted) basis.\\n    \"\"\"\\n    self.input_space = input_space\\n    self.output_space = output_space\\n    self.matrix = matrix\\n\\n  def __post_init__(self) -> None:\\n    output_size, input_size = self.matrix.shape\\n    assert input_size == self.input_space.num_dims\\n    assert output_size == self.output_space.num_dims\\n\\n  def __call__(self, x: VectorInBasis) -> VectorInBasis:\\n    if x not in self.input_space:\\n      raise TypeError(f\"x={x} not in self.input_space={self.input_space}.\")\\n    return VectorInBasis(\\n        basis_directions=sorted(self.output_space.basis),\\n        magnitudes=x.magnitudes @ self.matrix,\\n    )\\n\\n  @classmethod\\n  def from_action(\\n      cls,\\n      input_space: VectorSpaceWithBasis,\\n      output_space: VectorSpaceWithBasis,\\n      action: Callable[[BasisDirection], VectorInBasis],\\n  ) -> \"Linear\":\\n    \"\"\"from_action(i, o)(action) creates a Linear.\"\"\"\\n\\n    matrix = np.zeros((input_space.num_dims, output_space.num_dims))\\n    for i, direction in enumerate(input_space.basis):\\n      out_vector = action(direction)\\n      if out_vector not in output_space:\\n        raise TypeError(f\"image of {direction} from input_space={input_space} \"\\n                        f\"is not in output_space={output_space}\")\\n      matrix[i, :] = out_vector.magnitudes\\n\\n    return Linear(input_space, output_space, matrix)\\n\\n  @classmethod\\n  def combine_in_parallel(cls, fns: Sequence[\"Linear\"]) -> \"Linear\":\\n    \"\"\"Combines multiple parallel linear functions into a single one.\"\"\"\\n    joint_input_space = bases.join_vector_spaces(\\n        *[fn.input_space for fn in fns])\\n    joint_output_space = bases.join_vector_spaces(\\n        *[fn.output_space for fn in fns])\\n\\n    def action(x: bases.BasisDirection) -> bases.VectorInBasis:\\n      out = joint_output_space.null_vector()\\n      for fn in fns:\\n        if x in fn.input_space:\\n          x_vec = fn.input_space.vector_from_basis_direction(x)\\n          out += fn(x_vec).project(joint_output_space)\\n      return out\\n\\n    return cls.from_action(joint_input_space, joint_output_space, action)\\n\\n\\ndef project(\\n    from_space: VectorSpaceWithBasis,\\n    to_space: VectorSpaceWithBasis,\\n) -> Linear:\\n  \"\"\"Creates a projection.\"\"\"\\n\\n  def action(direction: bases.BasisDirection) -> VectorInBasis:\\n    if direction in to_space:\\n      return to_space.vector_from_basis_direction(direction)\\n    else:\\n      return to_space.null_vector()\\n\\n  return Linear.from_action(from_space, to_space, action=action)\\n\\n\\<EMAIL>\\nclass ScalarBilinear:\\n  \"\"\"A scalar-valued bilinear operator.\"\"\"\\n  left_space: VectorSpaceWithBasis\\n  right_space: VectorSpaceWithBasis\\n  matrix: np.ndarray\\n\\n  def __post_init__(self):\\n    \"\"\"Ensure matrix acts in sorted bases and typecheck sizes.\"\"\"\\n    left_size, right_size = self.matrix.shape\\n    assert left_size == self.left_space.num_dims\\n    assert right_size == self.right_space.num_dims\\n\\n  def __call__(self, x: VectorInBasis, y: VectorInBasis) -> float:\\n    \"\"\"Describes the action of the operator on vectors.\"\"\"\\n    if x not in self.left_space:\\n      raise TypeError(f\"x={x} not in self.left_space={self.left_space}.\")\\n    if y not in self.right_space:\\n      raise TypeError(f\"y={y} not in self.right_space={self.right_space}.\")\\n    return (x.magnitudes.T @ self.matrix @ y.magnitudes).item()\\n\\n  @classmethod\\n  def from_action(\\n      cls,\\n      left_space: VectorSpaceWithBasis,\\n      right_space: VectorSpaceWithBasis,\\n      action: Callable[[BasisDirection, BasisDirection], float],\\n  ) -> \"ScalarBilinear\":\\n    \"\"\"from_action(l, r)(action) creates a ScalarBilinear.\"\"\"\\n\\n    matrix = np.zeros((left_space.num_dims, right_space.num_dims))\\n    for i, left_direction in enumerate(left_space.basis):\\n      for j, right_direction in enumerate(right_space.basis):\\n        matrix[i, j] = action(left_direction, right_direction)\\n\\n    return ScalarBilinear(left_space, right_space, matrix)\\n', path=PosixPath('tracr/craft/vectorspace_fns.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b83990f0>, scope_tree=SrcScope(name='tracr/craft/vectorspace_fns.py', kind='file', range=0:5461, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5461:5461, code=''), len(children)=5)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Helper functions for tests.\"\"\"\\n\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.craft import bases\\n\\n\\ndef strip_bos_token(vector: bases.VectorInBasis) -> bases.VectorInBasis:\\n  \"\"\"Removes BOS token of a vector.\"\"\"\\n  return bases.VectorInBasis(vector.basis_directions, vector.magnitudes[1:])\\n\\n\\nclass VectorFnTestCase(parameterized.TestCase):\\n  \"\"\"Asserts for vectors.\"\"\"\\n\\n  def assertVectorAllClose(self, v1: bases.VectorInBasis,\\n                           v2: bases.VectorInBasis):\\n    self.assertEqual(v1.basis_directions, v2.basis_directions)\\n    np.testing.assert_allclose(v1.magnitudes, v2.magnitudes, atol=1e-7)\\n', path=PosixPath('tracr/craft/tests_common.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b82fef30>, scope_tree=SrcScope(name='tracr/craft/tests_common.py', kind='file', range=0:1336, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1336:1336, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for vectorspace_fns.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft import vectorspace_fns as vs_fns\\n\\n\\nclass LinearTest(tests_common.VectorFnTestCase):\\n\\n  def test_identity_from_matrix(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\"])\\n    f = vs_fns.Linear(vs, vs, np.eye(3))\\n    for v in vs.basis_vectors():\\n      self.assertEqual(f(v), v)\\n\\n  def test_identity_from_action(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\"])\\n    f = vs_fns.Linear.from_action(vs, vs, vs.vector_from_basis_direction)\\n    for v in vs.basis_vectors():\\n      self.assertEqual(f(v), v)\\n\\n  def test_nonidentiy(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a = vs.vector_from_basis_direction(bases.BasisDirection(\"a\"))\\n    b = vs.vector_from_basis_direction(bases.BasisDirection(\"b\"))\\n\\n    f = vs_fns.Linear(vs, vs, np.array([[0.3, 0.7], [0.2, 0.1]]))\\n\\n    self.assertEqual(\\n        f(a), bases.VectorInBasis(vs.basis, np.array([0.3, 0.7])))\\n    self.assertEqual(\\n        f(b), bases.VectorInBasis(vs.basis, np.array([0.2, 0.1])))\\n\\n  def test_different_vector_spaces(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"c\", \"d\"])\\n    a, b = vs1.basis_vectors()\\n    c, d = vs2.basis_vectors()\\n\\n    f = vs_fns.Linear(vs1, vs2, np.eye(2))\\n\\n    self.assertEqual(f(a), c)\\n    self.assertEqual(f(b), d)\\n\\n  def test_combining_linear_functions_with_different_input(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"c\", \"d\"])\\n    vs = bases.direct_sum(vs1, vs2)\\n    a = vs.vector_from_basis_direction(bases.BasisDirection(\"a\"))\\n    b = vs.vector_from_basis_direction(bases.BasisDirection(\"b\"))\\n    c = vs.vector_from_basis_direction(bases.BasisDirection(\"c\"))\\n    d = vs.vector_from_basis_direction(bases.BasisDirection(\"d\"))\\n\\n    f1 = vs_fns.Linear(vs1, vs1, np.array([[0, 1], [1, 0]]))\\n    f2 = vs_fns.Linear(vs2, vs2, np.array([[1, 0], [0, 0]]))\\n    f3 = vs_fns.Linear.combine_in_parallel([f1, f2])\\n\\n    self.assertEqual(\\n        f3(a), bases.VectorInBasis(vs.basis, np.array([0, 1, 0, 0])))\\n    self.assertEqual(\\n        f3(b), bases.VectorInBasis(vs.basis, np.array([1, 0, 0, 0])))\\n    self.assertEqual(\\n        f3(c), bases.VectorInBasis(vs.basis, np.array([0, 0, 1, 0])))\\n    self.assertEqual(\\n        f3(d), bases.VectorInBasis(vs.basis, np.array([0, 0, 0, 0])))\\n\\n  def test_combining_linear_functions_with_same_input(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a = vs.vector_from_basis_direction(bases.BasisDirection(\"a\"))\\n    b = vs.vector_from_basis_direction(bases.BasisDirection(\"b\"))\\n\\n    f1 = vs_fns.Linear(vs, vs, np.array([[0, 1], [1, 0]]))\\n    f2 = vs_fns.Linear(vs, vs, np.array([[1, 0], [0, 0]]))\\n    f3 = vs_fns.Linear.combine_in_parallel([f1, f2])\\n\\n    self.assertEqual(\\n        f3(a), bases.VectorInBasis(vs.basis, np.array([1, 1])))\\n    self.assertEqual(\\n        f3(b), bases.VectorInBasis(vs.basis, np.array([1, 0])))\\n    self.assertEqual(f3(a), f1(a) + f2(a))\\n    self.assertEqual(f3(b), f1(b) + f2(b))\\n\\n\\nclass ProjectionTest(tests_common.VectorFnTestCase):\\n\\n  def test_projection_to_larger_space(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    a1, b1 = vs1.basis_vectors()\\n    a2, b2, _, _ = vs2.basis_vectors()\\n\\n    f = vs_fns.project(vs1, vs2)\\n\\n    self.assertEqual(f(a1), a2)\\n    self.assertEqual(f(b1), b2)\\n\\n  def test_projection_to_smaller_space(self):\\n    vs1 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\", \"c\", \"d\"])\\n    vs2 = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a1, b1, c1, d1 = vs1.basis_vectors()\\n    a2, b2 = vs2.basis_vectors()\\n\\n    f = vs_fns.project(vs1, vs2)\\n\\n    self.assertEqual(f(a1), a2)\\n    self.assertEqual(f(b1), b2)\\n    self.assertEqual(f(c1), vs2.null_vector())\\n    self.assertEqual(f(d1), vs2.null_vector())\\n\\n\\nclass ScalarBilinearTest(parameterized.TestCase):\\n\\n  def test_identity_matrix(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a, b = vs.basis_vectors()\\n\\n    f = vs_fns.ScalarBilinear(vs, vs, np.eye(2))\\n\\n    self.assertEqual(f(a, a), 1)\\n    self.assertEqual(f(a, b), 0)\\n    self.assertEqual(f(b, a), 0)\\n    self.assertEqual(f(b, b), 1)\\n\\n  def test_identity_from_action(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a, b = vs.basis_vectors()\\n\\n    f = vs_fns.ScalarBilinear.from_action(vs, vs, lambda x, y: int(x == y))\\n\\n    self.assertEqual(f(a, a), 1)\\n    self.assertEqual(f(a, b), 0)\\n    self.assertEqual(f(b, a), 0)\\n    self.assertEqual(f(b, b), 1)\\n\\n  def test_non_identity(self):\\n    vs = bases.VectorSpaceWithBasis.from_names([\"a\", \"b\"])\\n    a, b = vs.basis_vectors()\\n\\n    f = vs_fns.ScalarBilinear.from_action(vs, vs,\\n                                          lambda x, y: int(x.name == \"a\"))\\n\\n    self.assertEqual(f(a, a), 1)\\n    self.assertEqual(f(a, b), 1)\\n    self.assertEqual(f(b, a), 0)\\n    self.assertEqual(f(b, b), 0)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/vectorspace_fns_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8315e50>, scope_tree=SrcScope(name='tracr/craft/vectorspace_fns_test.py', kind='file', range=0:5927, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5927:5927, code=''), len(children)=5)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"MLPs to compute arbitrary numerical functions by discretising.\"\"\"\\n\\nimport dataclasses\\n\\nfrom typing import Callable, Iterable, List\\n\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns\\nfrom tracr.utils import errors\\n\\n\\<EMAIL>\\nclass DiscretisingLayerMaterials:\\n  \"\"\"Provides components for a hidden layer that discretises the input.\\n\\n  Attributes:\\n    action: Function acting on basis directions that defines the computation.\\n    hidden_space: Vector space of the hidden representation of the layer.\\n    output_values: Set of output values that correspond to the discretisation.\\n  \"\"\"\\n  action: Callable[[bases.BasisDirection], bases.VectorInBasis]\\n  hidden_space: bases.VectorSpaceWithBasis\\n  output_values: List[float]\\n\\n\\ndef _get_discretising_layer(input_value_set: Iterable[float],\\n                            f: Callable[[float],\\n                                        float], hidden_name: bases.Name,\\n                            one_direction: bases.BasisDirection,\\n                            large_number: float) -> DiscretisingLayerMaterials:\\n  \"\"\"Creates a hidden layer that discretises the input of f(x) into a value set.\\n\\n  The input is split up into a distinct region around each value in\\n  `input_value_set`:\\n\\n  elements of value set:  v0   |  v1  |  v2  |  v3  |  v4  | ...\\n  thresholds:                  t0     t1     t2     t3     t4\\n\\n  The hidden layer has two activations per threshold:\\n    hidden_k_1 = ReLU(L * (x - threshold[k]) + 1)\\n    hidden_k_2 = ReLU(L * (x - threshold[k]))\\n\\n  Note that hidden_k_1 - hidden_k_2 is:\\n    1                 if x >= threshold[k] + 1/L\\n    0                 if x <= threshold[k]\\n    between 0 and 1   if threshold[k] < x < threshold[k] + 1/L\\n\\n  So as long as we choose L a big enough number, we have\\n    hidden_k_1 - hidden_k_2 = 1 if x >= threshold[k].\\n  i.e. we know in which region the input value is.\\n\\n  Args:\\n    input_value_set: Set of discrete input values.\\n    f: Function to approximate.\\n    hidden_name: Name for hidden dimensions.\\n    one_direction: Auxiliary dimension that must contain 1 in the input.\\n    large_number: Large number L that determines accuracy of the computation.\\n\\n  Returns:\\n    DiscretisingLayerMaterials containing all components for the layer.\\n  \"\"\"\\n  output_values, sorted_values = [], []\\n  for x in sorted(input_value_set):\\n    res = errors.ignoring_arithmetic_errors(f)(x)\\n    if res is not None:\\n      output_values.append(res)\\n      sorted_values.append(x)\\n\\n  num_vals = len(sorted_values)\\n  value_thresholds = [\\n      (sorted_values[i] + sorted_values[i + 1]) / 2 for i in range(num_vals - 1)\\n  ]\\n\\n  hidden_directions = [bases.BasisDirection(f\"{hidden_name}start\")]\\n  for k in range(1, num_vals):\\n    dir0 = bases.BasisDirection(hidden_name, (k, 0))\\n    dir1 = bases.BasisDirection(hidden_name, (k, 1))\\n    hidden_directions.extend([dir0, dir1])\\n  hidden_space = bases.VectorSpaceWithBasis(hidden_directions)\\n\\n  def action(direction: bases.BasisDirection) -> bases.VectorInBasis:\\n    # hidden_k_0 = ReLU(L * (x - threshold[k]) + 1)\\n    # hidden_k_1 = ReLU(L * (x - threshold[k]))\\n    if direction == one_direction:\\n      hidden = hidden_space.vector_from_basis_direction(\\n          bases.BasisDirection(f\"{hidden_name}start\"))\\n    else:\\n      hidden = hidden_space.null_vector()\\n    for k in range(1, num_vals):\\n      vec0 = hidden_space.vector_from_basis_direction(\\n          bases.BasisDirection(hidden_name, (k, 0)))\\n      vec1 = hidden_space.vector_from_basis_direction(\\n          bases.BasisDirection(hidden_name, (k, 1)))\\n      if direction == one_direction:\\n        hidden += (1 - large_number * value_thresholds[k - 1]) * vec0\\n        hidden -= large_number * value_thresholds[k - 1] * vec1\\n      else:\\n        hidden += large_number * vec0 + large_number * vec1\\n    return hidden\\n\\n  return DiscretisingLayerMaterials(\\n      action=action, hidden_space=hidden_space, output_values=output_values)\\n\\n\\ndef map_numerical_mlp(\\n    f: Callable[[float], float],\\n    input_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    input_value_set: Iterable[float],\\n    one_space: bases.VectorSpaceWithBasis,\\n    large_number: float = 100,\\n    hidden_name: bases.Name = \"__hidden__\",\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP that encodes any function of a single variable f(x).\\n\\n  This is implemented by discretising the input according to input_value_set\\n  and defining thresholds that determine which part of the input range will\\n  is allocated to which value in input_value_set.\\n\\n  elements of value set:  v0   |  v1  |  v2  |  v3  |  v4  | ...\\n  thresholds:                  t0     t1     t2     t3     t4\\n\\n  The MLP computes two hidden activations per threshold:\\n    hidden_k_0 = ReLU(L * (x - threshold[k]) + 1)\\n    hidden_k_1 = ReLU(L * (x - threshold[k]))\\n\\n  Note that hidden_k_1 - hidden_k_2 is:\\n    1                 if x >= threshold[k] + 1/L\\n    0                 if x <= threshold[k]\\n    between 0 and 1   if threshold[k] < x < threshold[k] + 1/L\\n\\n  So as long as we choose L a big enough number, we have\\n    hidden_k_0 - hidden_k_1 = 1 if x >= threshold[k].\\n\\n  The MLP then computes the output as:\\n    output = f(input[0]) +\\n      sum((hidden_k_0 - hidden_k_1) * (f(input[k]) - f(input[k-1]))\\n        for all k=0,1,...)\\n\\n  This sum will be (by a telescoping sums argument)\\n    f(input[0])      if x <= threshold[0]\\n    f(input[k])      if threshold[k-1] < x <= threshold[k] for some other k\\n    f(input[-1])     if x > threshold[-1]\\n  which approximates f() up to an accuracy given by input_value_set and L.\\n\\n  Args:\\n    f: Function to approximate.\\n    input_space: 1-d vector space that encodes the input x.\\n    output_space: 1-d vector space to write the output to.\\n    input_value_set: Set of values the input can take.\\n    one_space: Auxiliary 1-d vector space that must contain 1 in the input.\\n    large_number: Large number L that determines accuracy of the computation.\\n      Note that too large values of L can lead to numerical issues, particularly\\n      during inference on GPU/TPU.\\n    hidden_name: Name for hidden dimensions.\\n  \"\"\"\\n  bases.ensure_dims(input_space, num_dims=1, name=\"input_space\")\\n  bases.ensure_dims(output_space, num_dims=1, name=\"output_space\")\\n  bases.ensure_dims(one_space, num_dims=1, name=\"one_space\")\\n\\n  input_space = bases.join_vector_spaces(input_space, one_space)\\n  out_vec = output_space.vector_from_basis_direction(output_space.basis[0])\\n\\n  discretising_layer = _get_discretising_layer(\\n      input_value_set=input_value_set,\\n      f=f,\\n      hidden_name=hidden_name,\\n      one_direction=one_space.basis[0],\\n      large_number=large_number)\\n  first_layer = vectorspace_fns.Linear.from_action(\\n      input_space, discretising_layer.hidden_space, discretising_layer.action)\\n\\n  def second_layer_action(\\n      direction: bases.BasisDirection) -> bases.VectorInBasis:\\n    # output = sum(\\n    #     (hidden_k_0 - hidden_k_1) * (f(input[k]) - f(input[k-1]))\\n    #   for all k)\\n    if direction.name == f\"{hidden_name}start\":\\n      return discretising_layer.output_values[0] * out_vec\\n    k, i = direction.value\\n    # add hidden_k_0 and subtract hidden_k_1\\n    sign = {0: 1, 1: -1}[i]\\n    return sign * (discretising_layer.output_values[k] -\\n                   discretising_layer.output_values[k - 1]) * out_vec\\n\\n  second_layer = vectorspace_fns.Linear.from_action(\\n      discretising_layer.hidden_space, output_space, second_layer_action)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n\\n\\ndef map_numerical_to_categorical_mlp(\\n    f: Callable[[float], float],\\n    input_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    input_value_set: Iterable[float],\\n    one_space: bases.VectorSpaceWithBasis,\\n    large_number: float = 100,\\n    hidden_name: bases.Name = \"__hidden__\",\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP to compute f(x) from a numerical to a categorical variable.\\n\\n  Uses a set of possible output values, and rounds f(x) to the closest value\\n  in this set to create a categorical output variable.\\n\\n  The output is discretised the same way as in `map_numerical_mlp`.\\n\\n  Args:\\n    f: Function to approximate.\\n    input_space: 1-d vector space that encodes the input x.\\n    output_space: n-d vector space to write categorical output to. The output\\n      directions need to encode the possible output values.\\n    input_value_set: Set of values the input can take.\\n    one_space: Auxiliary 1-d space that must contain 1 in the input.\\n    large_number: Large number L that determines accuracy of the computation.\\n    hidden_name: Name for hidden dimensions.\\n  \"\"\"\\n  bases.ensure_dims(input_space, num_dims=1, name=\"input_space\")\\n  bases.ensure_dims(one_space, num_dims=1, name=\"one_space\")\\n\\n  input_space = bases.join_vector_spaces(input_space, one_space)\\n\\n  vec_by_out_val = dict()\\n  for d in output_space.basis:\\n    # TODO(b/255937603): Do a similar assert in other places where we expect\\n    # categorical basis directions to encode values.\\n    assert d.value is not None, (\"output directions need to encode \"\\n                                 \"possible output values\")\\n    vec_by_out_val[d.value] = output_space.vector_from_basis_direction(d)\\n\\n  discretising_layer = _get_discretising_layer(\\n      input_value_set=input_value_set,\\n      f=f,\\n      hidden_name=hidden_name,\\n      one_direction=one_space.basis[0],\\n      large_number=large_number)\\n\\n  assert set(discretising_layer.output_values).issubset(\\n      set(vec_by_out_val.keys()))\\n\\n  first_layer = vectorspace_fns.Linear.from_action(\\n      input_space, discretising_layer.hidden_space, discretising_layer.action)\\n\\n  def second_layer_action(\\n      direction: bases.BasisDirection) -> bases.VectorInBasis:\\n    \"\"\"Computes output value and returns corresponding output direction.\"\"\"\\n    if direction.name == f\"{hidden_name}start\":\\n      return vec_by_out_val[discretising_layer.output_values[0]]\\n    else:\\n      k, i = direction.value\\n      # add hidden_k_0 and subtract hidden_k_1\\n      sign = {0: 1, 1: -1}[i]\\n      out_k = discretising_layer.output_values[k]\\n      out_k_m_1 = discretising_layer.output_values[k - 1]\\n      return sign * (vec_by_out_val[out_k] - vec_by_out_val[out_k_m_1])\\n\\n  second_layer = vectorspace_fns.Linear.from_action(\\n      discretising_layer.hidden_space, output_space, second_layer_action)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n\\n\\ndef linear_sequence_map_numerical_mlp(\\n    input1_basis_direction: bases.BasisDirection,\\n    input2_basis_direction: bases.BasisDirection,\\n    output_basis_direction: bases.BasisDirection,\\n    input1_factor: float,\\n    input2_factor: float,\\n    hidden_name: bases.Name = \"__hidden__\",\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP that encodes a linear function f(x, y) = a*x + b*y.\\n\\n  Args:\\n    input1_basis_direction: Basis direction that encodes the input x.\\n    input2_basis_direction: Basis direction that encodes the input y.\\n    output_basis_direction: Basis direction to write the output to.\\n    input1_factor: Linear factor a for input x.\\n    input2_factor: Linear factor a for input y.\\n    hidden_name: Name for hidden dimensions.\\n  \"\"\"\\n  input_space = bases.VectorSpaceWithBasis(\\n      [input1_basis_direction, input2_basis_direction])\\n  output_space = bases.VectorSpaceWithBasis([output_basis_direction])\\n  out_vec = output_space.vector_from_basis_direction(output_basis_direction)\\n\\n  hidden_directions = [\\n      bases.BasisDirection(f\"{hidden_name}x\", 1),\\n      bases.BasisDirection(f\"{hidden_name}x\", -1),\\n      bases.BasisDirection(f\"{hidden_name}y\", 1),\\n      bases.BasisDirection(f\"{hidden_name}y\", -1)\\n  ]\\n  hidden_space = bases.VectorSpaceWithBasis(hidden_directions)\\n  x_pos_vec, x_neg_vec, y_pos_vec, y_neg_vec = (\\n      hidden_space.vector_from_basis_direction(d) for d in hidden_directions)\\n\\n  def first_layer_action(\\n      direction: bases.BasisDirection) -> bases.VectorInBasis:\\n    output = hidden_space.null_vector()\\n    if direction == input1_basis_direction:\\n      output += x_pos_vec - x_neg_vec\\n    if direction == input2_basis_direction:\\n      output += y_pos_vec - y_neg_vec\\n    return output\\n\\n  first_layer = vectorspace_fns.Linear.from_action(input_space, hidden_space,\\n                                                   first_layer_action)\\n\\n  def second_layer_action(\\n      direction: bases.BasisDirection) -> bases.VectorInBasis:\\n    if direction.name == f\"{hidden_name}x\":\\n      return input1_factor * direction.value * out_vec\\n    if direction.name == f\"{hidden_name}y\":\\n      return input2_factor * direction.value * out_vec\\n    return output_space.null_vector()\\n\\n  second_layer = vectorspace_fns.Linear.from_action(hidden_space, output_space,\\n                                                    second_layer_action)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n', path=PosixPath('tracr/craft/chamber/numerical_mlp.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b83c8bd0>, scope_tree=SrcScope(name='tracr/craft/chamber/numerical_mlp.py', kind='file', range=0:13524, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=13524:13524, code=''), len(children)=6)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/craft/chamber/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8287930>, scope_tree=SrcScope(name='tracr/craft/chamber/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for chamber.numerical_mlp.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft.chamber import numerical_mlp\\nfrom tracr.utils import errors\\n\\n\\nclass NumericalMlpTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.parameters([\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=2,\\n          function=lambda x: x,\\n          result=2),\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=2,\\n          function=lambda x: x**2,\\n          result=4),\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=2,\\n          function=lambda x: x**3,\\n          result=8),\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=-2,\\n          function=lambda x: x,\\n          result=-2),\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=-2,\\n          function=lambda x: x**2,\\n          result=4),\\n      dict(\\n          in_value_set={-2, -2, -1, 0, 1, 2, 3},\\n          x=-2,\\n          function=lambda x: x**3,\\n          result=-8),\\n  ])\\n  def test_map_numerical_mlp_produces_expected_outcome(self, in_value_set, x,\\n                                                       function, result):\\n\\n    input_dir = bases.BasisDirection(\"input\")\\n    output_dir = bases.BasisDirection(\"output\")\\n    one_dir = bases.BasisDirection(\"one\")\\n    input_space = bases.VectorSpaceWithBasis([input_dir])\\n    output_space = bases.VectorSpaceWithBasis([output_dir])\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    mlp = numerical_mlp.map_numerical_mlp(\\n        f=function,\\n        input_space=input_space,\\n        output_space=output_space,\\n        one_space=one_space,\\n        input_value_set=in_value_set,\\n    )\\n\\n    test_inputs = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir, one_dir],\\n        magnitudes=np.array([x, 0, 1]))\\n\\n    expected_results = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir, one_dir],\\n        magnitudes=np.array([0, result, 0]))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  @parameterized.parameters([\\n      dict(in_value_set={0, 1, 2, 3}, x=1, function=lambda x: 1 / x, result=1),\\n      dict(\\n          in_value_set={0, 1, 2, 3}, x=2, function=lambda x: 1 / x, result=0.5),\\n      dict(\\n          in_value_set={0, 1, 2, 3},\\n          x=3,\\n          function=lambda x: 1 / x,\\n          result=1 / 3),\\n  ])\\n  def test_map_numerical_mlp_logs_warning_and_produces_expected_outcome(\\n      self, in_value_set, x, function, result):\\n\\n    input_dir = bases.BasisDirection(\"input\")\\n    output_dir = bases.BasisDirection(\"output\")\\n    one_dir = bases.BasisDirection(\"one\")\\n    input_space = bases.VectorSpaceWithBasis([input_dir])\\n    output_space = bases.VectorSpaceWithBasis([output_dir])\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      mlp = numerical_mlp.map_numerical_mlp(\\n          f=function,\\n          input_space=input_space,\\n          output_space=output_space,\\n          one_space=one_space,\\n          input_value_set=in_value_set,\\n      )\\n\\n    test_inputs = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir, one_dir],\\n        magnitudes=np.array([x, 0, 1]))\\n\\n    expected_results = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir, one_dir],\\n        magnitudes=np.array([0, result, 0]))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  @parameterized.parameters([\\n      dict(in_value_set={0, 1, 2, 3}, x=1, function=lambda x: 1 / x, result=1),\\n      dict(\\n          in_value_set={0, 1, 2, 3}, x=2, function=lambda x: 1 / x, result=0.5),\\n      dict(\\n          in_value_set={0, 1, 2, 3},\\n          x=3,\\n          function=lambda x: 1 / x,\\n          result=1 / 3),\\n  ])\\n  def test_map_numerical_to_categorical_mlp_logs_warning_and_produces_expected_outcome(\\n      self, in_value_set, x, function, result):\\n\\n    f_ign = errors.ignoring_arithmetic_errors(function)\\n    out_value_set = {f_ign(x) for x in in_value_set if f_ign(x) is not None}\\n\\n    in_space = bases.VectorSpaceWithBasis.from_names([\"input\"])\\n    out_space = bases.VectorSpaceWithBasis.from_values(\"output\", out_value_set)\\n    one_space = bases.VectorSpaceWithBasis.from_names([\"one\"])\\n\\n    residual_space = bases.join_vector_spaces(in_space, one_space, out_space)\\n    in_vec = residual_space.vector_from_basis_direction(in_space.basis[0])\\n    one_vec = residual_space.vector_from_basis_direction(one_space.basis[0])\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      mlp = numerical_mlp.map_numerical_to_categorical_mlp(\\n          f=function,\\n          input_space=in_space,\\n          output_space=out_space,\\n          input_value_set=in_value_set,\\n          one_space=one_space,\\n      )\\n\\n    test_inputs = x * in_vec + one_vec\\n    expected_results = out_space.vector_from_basis_direction(\\n        bases.BasisDirection(\"output\", result))\\n    test_outputs = mlp.apply(test_inputs).project(out_space)\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  @parameterized.parameters([\\n      dict(x_factor=1, y_factor=2, x=1, y=1, result=3),\\n      dict(x_factor=1, y_factor=2, x=1, y=-1, result=-1),\\n      dict(x_factor=1, y_factor=-1, x=1, y=1, result=0),\\n      dict(x_factor=1, y_factor=1, x=3, y=5, result=8),\\n      dict(x_factor=-2, y_factor=-0.5, x=4, y=1, result=-8.5),\\n  ])\\n  def test_linear_sequence_map_produces_expected_result(self, x_factor,\\n                                                        y_factor, x, y, result):\\n\\n    input1_dir = bases.BasisDirection(\"input1\")\\n    input2_dir = bases.BasisDirection(\"input2\")\\n    output_dir = bases.BasisDirection(\"output\")\\n\\n    mlp = numerical_mlp.linear_sequence_map_numerical_mlp(\\n        input1_basis_direction=input1_dir,\\n        input2_basis_direction=input2_dir,\\n        output_basis_direction=output_dir,\\n        input1_factor=x_factor,\\n        input2_factor=y_factor)\\n\\n    test_inputs = bases.VectorInBasis(\\n        basis_directions=[input1_dir, input2_dir, output_dir],\\n        magnitudes=np.array([x, y, 0]))\\n\\n    expected_results = bases.VectorInBasis(\\n        basis_directions=[input1_dir, input2_dir, output_dir],\\n        magnitudes=np.array([0, 0, result]))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  @parameterized.parameters([\\n      dict(x_factor=1, y_factor=2, x=1, result=3),\\n      dict(x_factor=1, y_factor=-1, x=1, result=0),\\n  ])\\n  def test_linear_sequence_map_produces_expected_result_with_same_inputs(\\n      self, x_factor, y_factor, x, result):\\n\\n    input_dir = bases.BasisDirection(\"input\")\\n    output_dir = bases.BasisDirection(\"output\")\\n\\n    mlp = numerical_mlp.linear_sequence_map_numerical_mlp(\\n        input1_basis_direction=input_dir,\\n        input2_basis_direction=input_dir,\\n        output_basis_direction=output_dir,\\n        input1_factor=x_factor,\\n        input2_factor=y_factor)\\n\\n    test_inputs = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir], magnitudes=np.array([x, 0]))\\n\\n    expected_results = bases.VectorInBasis(\\n        basis_directions=[input_dir, output_dir],\\n        magnitudes=np.array([0, result]))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/chamber/numerical_mlp_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8287c50>, scope_tree=SrcScope(name='tracr/craft/chamber/numerical_mlp_test.py', kind='file', range=0:8214, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=8214:8214, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for chamber.categorical_attn.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft.chamber import categorical_attn\\n\\n\\nclass CategoricalAttnTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.parameters([\\n      dict(causal=False, input_seq=[1, 2, 3, 4, 5], result_seq=[3, 3, 3, 3, 3]),\\n      dict(\\n          causal=True,\\n          input_seq=[1, 2, 3, 4, 5],\\n          result_seq=[1, 1.5, 2, 2.5, 3]),\\n      dict(causal=False, input_seq=[10], result_seq=[10]),\\n      dict(causal=True, input_seq=[10], result_seq=[10]),\\n      dict(causal=False, input_seq=[-1, 0, 1], result_seq=[0, 0, 0]),\\n      dict(causal=True, input_seq=[-1, 0, 1], result_seq=[-1, -0.5, 0]),\\n  ])\\n  def test_categorical_attn_can_implement_select_all(self, causal, input_seq,\\n                                                     result_seq):\\n    vocab = range(-20, 20)\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n\\n    output_dir = bases.BasisDirection(\"output\")\\n    output_space = bases.VectorSpaceWithBasis([output_dir])\\n    output_vec = output_space.vector_from_basis_direction(output_dir)\\n\\n    bos_dir = bases.BasisDirection(\"bos_dimension\")\\n    bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n\\n    one_dir = bases.BasisDirection(\"one\")\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    value_dir = bases.BasisDirection(\"value\")\\n    value_space = bases.VectorSpaceWithBasis([value_dir])\\n\\n    input_space = bases.join_vector_spaces(input_space, bos_space, one_space)\\n    value_space = bases.join_vector_spaces(value_space, bos_space)\\n    residual_space = bases.join_vector_spaces(input_space, value_space,\\n                                              output_space)\\n    one_vec = residual_space.vector_from_basis_direction(one_dir)\\n    bos_vec = residual_space.vector_from_basis_direction(bos_dir)\\n    value_vec = residual_space.vector_from_basis_direction(value_dir)\\n\\n    attn = categorical_attn.categorical_attn(\\n        key_space=input_space,\\n        query_space=input_space,\\n        value_space=value_space,\\n        output_space=output_space,\\n        bos_space=bos_space,\\n        one_space=one_space,\\n        attn_fn=lambda x, y: True,\\n        causal=causal)\\n\\n    test_inputs = [bos_vec + one_vec]\\n    for x in input_seq:\\n      test_inputs.append(\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(\"input\", x)) + x * value_vec)\\n    test_inputs = bases.VectorInBasis.stack(test_inputs)\\n\\n    # Expect the average of all (previous) tokens\\n    expected_results = [x * output_vec for x in result_seq]\\n    expected_results = bases.VectorInBasis.stack(expected_results)\\n\\n    test_outputs = attn.apply(test_inputs).project(output_space)\\n\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_outputs), expected_results)\\n\\n  @parameterized.parameters([\\n      dict(causal=False, input_seq=[1, 2, 3, 4, 5], default=0),\\n      dict(causal=True, input_seq=[1, 2, 3, 4, 5], default=1),\\n      dict(causal=False, input_seq=[10], default=2),\\n      dict(causal=True, input_seq=[10], default=-3),\\n      dict(causal=False, input_seq=[-1, 0, 1], default=-2),\\n      dict(causal=True, input_seq=[-1, 0, 1], default=-1),\\n  ])\\n  def test_categorical_attn_can_implement_select_none(self, causal, input_seq,\\n                                                      default):\\n    vocab = range(-20, 20)\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n\\n    output_dir = bases.BasisDirection(\"output\")\\n    output_space = bases.VectorSpaceWithBasis([output_dir])\\n    default_vec = default * output_space.vector_from_basis_direction(output_dir)\\n\\n    bos_dir = bases.BasisDirection(\"bos_dimension\")\\n    bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n\\n    one_dir = bases.BasisDirection(\"one\")\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    value_dir = bases.BasisDirection(\"value\")\\n    value_space = bases.VectorSpaceWithBasis([value_dir])\\n\\n    input_space = bases.join_vector_spaces(input_space, bos_space, one_space)\\n    value_space = bases.join_vector_spaces(value_space, bos_space)\\n    residual_space = bases.join_vector_spaces(input_space, value_space,\\n                                              output_space)\\n    value_vec = residual_space.vector_from_basis_direction(value_dir)\\n    bos_vec = residual_space.vector_from_basis_direction(bos_dir)\\n    one_vec = residual_space.vector_from_basis_direction(one_dir)\\n\\n    attn = categorical_attn.categorical_attn(\\n        key_space=input_space,\\n        query_space=input_space,\\n        value_space=value_space,\\n        output_space=output_space,\\n        bos_space=bos_space,\\n        one_space=one_space,\\n        attn_fn=lambda x, y: False,\\n        default_output=default_vec,\\n        causal=causal,\\n        always_attend_to_bos=False,\\n        use_bos_for_default_output=True)\\n\\n    def make_input(x):\\n      return (one_vec + x * value_vec +\\n              residual_space.vector_from_basis_direction(\\n                  bases.BasisDirection(\"input\", x)))\\n\\n    test_inputs = bases.VectorInBasis.stack([bos_vec + one_vec] +\\n                                            [make_input(x) for x in input_seq])\\n\\n    # Expect the default value\\n    expected_results = [default_vec for x in input_seq]\\n    expected_results = bases.VectorInBasis.stack(expected_results)\\n\\n    test_outputs = attn.apply(test_inputs).project(output_space)\\n\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_outputs), expected_results)\\n\\n  @parameterized.parameters([\\n      dict(num_counts=5, input_seq=[1, 4, 3, 2], n=1, result=[4, 3, 2, 1]),\\n      dict(num_counts=10, input_seq=[5, 8, 9, 2], n=3, result=[2, 5, 8, 9])\\n  ])\\n  def test_categorical_attn_can_implement_shift_by_n(self, num_counts,\\n                                                     input_seq, n, result):\\n    query_prefix = \"prefix1\"\\n    key_prefix = \"prefix2\"\\n    agg_input_prefix = \"prefix3\"\\n    output_prefix = \"prefix4\"\\n\\n    bos_direction = bases.BasisDirection(\"bos\")\\n    one_direction = bases.BasisDirection(\"one\")\\n    query_space = bases.VectorSpaceWithBasis.from_values(\\n        query_prefix, range(num_counts))\\n    key_space = bases.VectorSpaceWithBasis.from_values(key_prefix,\\n                                                       range(num_counts))\\n    bos_space = bases.VectorSpaceWithBasis([bos_direction])\\n    one_space = bases.VectorSpaceWithBasis([one_direction])\\n    key_space = bases.join_vector_spaces(key_space, bos_space)\\n\\n    agg_input_space = bases.VectorSpaceWithBasis.from_values(\\n        agg_input_prefix, range(num_counts))\\n    agg_input_space = bases.join_vector_spaces(agg_input_space, bos_space)\\n    output_space = bases.VectorSpaceWithBasis.from_values(\\n        output_prefix, range(num_counts))\\n\\n    attn = categorical_attn.categorical_attn(\\n        query_space=query_space,\\n        key_space=key_space,\\n        value_space=agg_input_space,\\n        output_space=output_space,\\n        bos_space=bos_space,\\n        one_space=one_space,\\n        attn_fn=lambda q, k: q.value == k.value,\\n        default_output=None,\\n        always_attend_to_bos=False,\\n        use_bos_for_default_output=True,\\n        causal=False)\\n\\n    residual_space = bases.join_vector_spaces(key_space, query_space,\\n                                              agg_input_space, output_space,\\n                                              one_space)\\n\\n    seq_len = len(input_seq)\\n    query_seq = np.arange(n, seq_len + n) % seq_len\\n    key_seq = np.arange(seq_len)\\n\\n    bos_vec = residual_space.vector_from_basis_direction(bos_direction)\\n    one_vec = residual_space.vector_from_basis_direction(one_direction)\\n\\n    test_inputs = [bos_vec + one_vec]\\n    expected_results = []\\n    for i in range(seq_len):\\n      test_inputs.append(\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(query_prefix, query_seq[i])) +\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(key_prefix, key_seq[i])) +\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(agg_input_prefix, input_seq[i])))\\n      expected_results.append(\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(output_prefix, result[i])))\\n\\n    test_inputs = bases.VectorInBasis.stack(test_inputs)\\n    expected_results = bases.VectorInBasis.stack(expected_results)\\n\\n    test_outputs = attn.apply(test_inputs)\\n\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_outputs), expected_results)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/chamber/categorical_attn_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b82871b0>, scope_tree=SrcScope(name='tracr/craft/chamber/categorical_attn_test.py', kind='file', range=0:9365, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=9365:9365, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for selector_width.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft.chamber import selector_width\\n\\n\\nclass SelectorWidthTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.product(\\n      causal=[False, True],\\n      categorical_output=[False, True],\\n      input_seq=[[1, 2, 3, 4, 5], [-1, 0, 1], [10]])\\n  def test_selector_width_of_select_all_is_length(self, causal,\\n                                                  categorical_output,\\n                                                  input_seq):\\n    vocab = range(-20, 20)\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n\\n    if categorical_output:\\n      output_space = bases.VectorSpaceWithBasis.from_values(\"output\", range(10))\\n    else:\\n      output_space = bases.VectorSpaceWithBasis(\\n          [bases.BasisDirection(\"output\")])\\n\\n    bos_dir = bases.BasisDirection(\"bos_dimension\")\\n    bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n\\n    one_dir = bases.BasisDirection(\"one_dimension\")\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    input_space = bases.join_vector_spaces(input_space, bos_space, one_space)\\n    residual_space = bases.join_vector_spaces(input_space, output_space)\\n    bos_vec = residual_space.vector_from_basis_direction(bos_dir)\\n    one_vec = residual_space.vector_from_basis_direction(one_dir)\\n\\n    block = selector_width.selector_width(\\n        query_space=input_space,\\n        key_space=input_space,\\n        output_space=output_space,\\n        bos_space=bos_space,\\n        one_space=one_space,\\n        attn_fn=lambda x, y: True,\\n        out_value_set=set(range(len(input_seq) + 1)),\\n        categorical_output=categorical_output,\\n        causal=causal,\\n        label=\"select_all\")\\n\\n    test_inputs = [bos_vec + one_vec]\\n    for x in input_seq:\\n      test_inputs.append(\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(\"input\", x)) + one_vec)\\n    test_inputs = bases.VectorInBasis.stack(test_inputs)\\n\\n    # Expect length of the input sequence\\n    if causal:\\n      expected_results = list(range(1, len(input_seq) + 1))\\n    else:\\n      expected_results = [len(input_seq) for _ in input_seq]\\n\\n    if categorical_output:\\n      expected_results = [\\n          output_space.vector_from_basis_direction(\\n              bases.BasisDirection(\"output\", x)) for x in expected_results\\n      ]\\n    else:\\n      output_vec = output_space.vector_from_basis_direction(\\n          bases.BasisDirection(\"output\"))\\n      expected_results = [x * output_vec for x in expected_results]\\n\\n    expected_results = bases.VectorInBasis.stack(expected_results)\\n\\n    test_outputs = block.apply(test_inputs).project(output_space)\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_outputs), expected_results)\\n\\n  @parameterized.product(\\n      causal=[False, True],\\n      categorical_output=[False, True],\\n      input_seq=[[1, 2, 3, 4, 5], [-1, 0, 1], [10]])\\n  def test_selector_width_of_select_none_is_zero(self, causal,\\n                                                 categorical_output, input_seq):\\n    vocab = range(-20, 20)\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"input\", vocab)\\n\\n    if categorical_output:\\n      output_space = bases.VectorSpaceWithBasis.from_values(\"output\", range(10))\\n    else:\\n      output_space = bases.VectorSpaceWithBasis(\\n          [bases.BasisDirection(\"output\")])\\n\\n    bos_dir = bases.BasisDirection(\"bos_dimension\")\\n    bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n\\n    one_dir = bases.BasisDirection(\"one_dimension\")\\n    one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n    input_space = bases.join_vector_spaces(input_space, bos_space, one_space)\\n    residual_space = bases.join_vector_spaces(input_space, output_space)\\n    bos_vec = residual_space.vector_from_basis_direction(bos_dir)\\n    one_vec = residual_space.vector_from_basis_direction(one_dir)\\n\\n    block = selector_width.selector_width(\\n        query_space=input_space,\\n        key_space=input_space,\\n        output_space=output_space,\\n        bos_space=bos_space,\\n        one_space=one_space,\\n        attn_fn=lambda x, y: False,\\n        out_value_set=set(range(len(input_seq) + 1)),\\n        categorical_output=categorical_output,\\n        causal=causal,\\n        label=\"select_all\")\\n\\n    test_inputs = [bos_vec + one_vec]\\n    for x in input_seq:\\n      test_inputs.append(\\n          residual_space.vector_from_basis_direction(\\n              bases.BasisDirection(\"input\", x)) + one_vec)\\n    test_inputs = bases.VectorInBasis.stack(test_inputs)\\n\\n    # Expect zero output\\n    if categorical_output:\\n      expected_results = [\\n          output_space.vector_from_basis_direction(\\n              bases.BasisDirection(\"output\", 0)) for _ in input_seq\\n      ]\\n    else:\\n      expected_results = [output_space.null_vector() for _ in input_seq]\\n    expected_results = bases.VectorInBasis.stack(expected_results)\\n\\n    test_outputs = block.apply(test_inputs).project(output_space)\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_outputs), expected_results)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/chamber/selector_width_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b820c2b0>, scope_tree=SrcScope(name='tracr/craft/chamber/selector_width_test.py', kind='file', range=0:5894, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5894:5894, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"MLP to compute basic linear functions of one-hot encoded integers.\"\"\"\\n\\nfrom typing import Callable\\n\\nimport numpy as np\\n\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns\\n\\n_ONE_SPACE = bases.VectorSpaceWithBasis.from_names([\"one\"])\\n\\n\\ndef map_categorical_mlp(\\n    input_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    operation: Callable[[bases.BasisDirection], bases.BasisDirection],\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP that encodes any categorical function of a single variable f(x).\\n\\n  The hidden layer is the identity and output combines this with a lookup table\\n    output_k = sum(f(i)*input_i for all i in input space)\\n\\n  Args:\\n    input_space: space containing the input x.\\n    output_space: space containing possible outputs.\\n    operation: A function operating on basis directions.\\n  \"\"\"\\n\\n  def operation_fn(direction):\\n    if direction in input_space:\\n      output_direction = operation(direction)\\n      if output_direction in output_space:\\n        return output_space.vector_from_basis_direction(output_direction)\\n    return output_space.null_vector()\\n\\n  first_layer = vectorspace_fns.Linear.from_action(input_space, output_space,\\n                                                   operation_fn)\\n\\n  second_layer = vectorspace_fns.project(output_space, output_space)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n\\n\\ndef map_categorical_to_numerical_mlp(\\n    input_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    operation: Callable[[bases.Value], float],\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP to compute f(x) from a categorical to a numerical variable.\\n\\n  The hidden layer is the identity and output combines this with a lookup table\\n    output = sum(f(i)*input_i for all i in input space)\\n\\n  Args:\\n    input_space: Vector space containing the input x.\\n    output_space: Vector space to write the numerical output to.\\n    operation: A function operating on basis directions.\\n  \"\"\"\\n  bases.ensure_dims(output_space, num_dims=1, name=\"output_space\")\\n  out_vec = output_space.vector_from_basis_direction(output_space.basis[0])\\n\\n  def operation_fn(direction):\\n    if direction in input_space:\\n      return operation(direction.value) * out_vec\\n    return output_space.null_vector()\\n\\n  first_layer = vectorspace_fns.Linear.from_action(input_space, output_space,\\n                                                   operation_fn)\\n\\n  second_layer = vectorspace_fns.project(output_space, output_space)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n\\n\\ndef sequence_map_categorical_mlp(\\n    input1_space: bases.VectorSpaceWithBasis,\\n    input2_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    operation: Callable[[bases.BasisDirection, bases.BasisDirection],\\n                        bases.BasisDirection],\\n    one_space: bases.VectorSpaceWithBasis = _ONE_SPACE,\\n    hidden_name: bases.Name = \"__hidden__\",\\n) -> transformers.MLP:\\n  \"\"\"Returns an MLP that encodes a categorical function of two variables f(x, y).\\n\\n  The hidden layer of the MLP computes the logical and of all input directions\\n    hidden_i_j = ReLU(x_i+x_j-1)\\n\\n  And the output combines this with a lookup table\\n    output_k = sum(f(i, j)*hidden_i_j for all i,j in input space)\\n\\n  Args:\\n    input1_space: Vector space containing the input x.\\n    input2_space: Vector space containing the input y.\\n    output_space: Vector space to write outputs to.\\n    operation: A function operating on basis directions.\\n    one_space: a reserved 1-d space that always contains a 1.\\n    hidden_name: Name for hidden dimensions.\\n  \"\"\"\\n  bases.ensure_dims(one_space, num_dims=1, name=\"one_space\")\\n\\n  if not set(input1_space.basis).isdisjoint(input2_space.basis):\\n    raise ValueError(\"Input spaces to a SequenceMap must be disjoint. \"\\n                     \"If input spaces are the same, use Map instead!\")\\n\\n  input_space = bases.direct_sum(input1_space, input2_space, one_space)\\n\\n  def to_hidden(x, y):\\n    return bases.BasisDirection(hidden_name, (x.name, x.value, y.name, y.value))\\n\\n  def from_hidden(h):\\n    x_name, x_value, y_name, y_value = h.value\\n    x_dir = bases.BasisDirection(x_name, x_value)\\n    y_dir = bases.BasisDirection(y_name, y_value)\\n    return x_dir, y_dir\\n\\n  hidden_dir = []\\n  for dir1 in input1_space.basis:\\n    for dir2 in input2_space.basis:\\n      hidden_dir.append(to_hidden(dir1, dir2))\\n  hidden_space = bases.VectorSpaceWithBasis(hidden_dir)\\n\\n  def logical_and(direction):\\n    if direction in one_space:\\n      out = bases.VectorInBasis(hidden_space.basis,\\n                                -np.ones(hidden_space.num_dims))\\n    elif direction in input1_space:\\n      dir1 = direction\\n      out = hidden_space.null_vector()\\n      for dir2 in input2_space.basis:\\n        out += hidden_space.vector_from_basis_direction(to_hidden(dir1, dir2))\\n    else:\\n      dir2 = direction\\n      out = hidden_space.null_vector()\\n      for dir1 in input1_space.basis:\\n        out += hidden_space.vector_from_basis_direction(to_hidden(dir1, dir2))\\n    return out\\n\\n  first_layer = vectorspace_fns.Linear.from_action(input_space, hidden_space,\\n                                                   logical_and)\\n\\n  def operation_fn(direction):\\n    dir1, dir2 = from_hidden(direction)\\n    output_direction = operation(dir1, dir2)\\n    if output_direction in output_space:\\n      return output_space.vector_from_basis_direction(output_direction)\\n    else:\\n      return output_space.null_vector()\\n\\n  second_layer = vectorspace_fns.Linear.from_action(hidden_space, output_space,\\n                                                    operation_fn)\\n\\n  return transformers.MLP(first_layer, second_layer)\\n', path=PosixPath('tracr/craft/chamber/categorical_mlp.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b81da730>, scope_tree=SrcScope(name='tracr/craft/chamber/categorical_mlp.py', kind='file', range=0:6426, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6426:6426, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for chamber.categorical_mlp.\"\"\"\\n\\nimport math\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\n\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.craft.chamber import categorical_mlp\\n\\n\\nclass CategoricalInputMlpTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.parameters([\\n      dict(num_counts=4, x=1, y=2, fun=lambda x, y: x + y, result=3),\\n      dict(num_counts=4, x=1, y=0, fun=lambda x, y: x + y + 1, result=2),\\n      dict(num_counts=5, x=2, y=1, fun=math.pow, result=2),\\n      dict(num_counts=5, x=2, y=2, fun=math.pow, result=4),\\n  ])\\n  def test_seq_map_categorical_mlp_produces_expected_outcome(\\n      self, num_counts, x, y, fun, result):\\n    input1_name = \"in1\"\\n    input2_name = \"in2\"\\n    output_name = \"out\"\\n    one_name = \"one_dimension\"\\n\\n    in1_space = bases.VectorSpaceWithBasis.from_values(input1_name,\\n                                                       range(num_counts + 1))\\n    in2_space = bases.VectorSpaceWithBasis.from_values(input2_name,\\n                                                       range(num_counts + 1))\\n    out_space = bases.VectorSpaceWithBasis.from_values(output_name,\\n                                                       range(num_counts + 1))\\n\\n    def operation(in1, in2):\\n      out_val = fun(int(in1.value), int(in2.value))\\n      return bases.BasisDirection(output_name, out_val)\\n\\n    mlp = categorical_mlp.sequence_map_categorical_mlp(\\n        input1_space=in1_space,\\n        input2_space=in2_space,\\n        output_space=out_space,\\n        operation=operation,\\n        one_space=bases.VectorSpaceWithBasis.from_names([one_name]))\\n\\n    test_inputs = (\\n        mlp.residual_space.vector_from_basis_direction(\\n            bases.BasisDirection(one_name)) +\\n        mlp.residual_space.vector_from_basis_direction(\\n            bases.BasisDirection(input1_name, x)) +\\n        mlp.residual_space.vector_from_basis_direction(\\n            bases.BasisDirection(input2_name, y)))\\n\\n    expected_results = mlp.residual_space.vector_from_basis_direction(\\n        bases.BasisDirection(output_name, result))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  def test_seq_map_categorical_mlp_raises_error_with_overlapping_inputs(self):\\n    input_name = \"in\"\\n    output_name = \"out\"\\n    one_name = \"one_dimension\"\\n\\n    in1_space = bases.VectorSpaceWithBasis.from_values(input_name, range(5))\\n    in2_space = bases.VectorSpaceWithBasis.from_values(input_name, range(3, 10))\\n    out_space = bases.VectorSpaceWithBasis.from_values(output_name, range(5))\\n\\n    with self.assertRaisesRegex(\\n        ValueError, r\".*Input spaces to a SequenceMap must be disjoint.*\"):\\n      categorical_mlp.sequence_map_categorical_mlp(\\n          input1_space=in1_space,\\n          input2_space=in1_space,\\n          output_space=out_space,\\n          operation=lambda x, y: bases.BasisDirection(output_name, 0),\\n          one_space=bases.VectorSpaceWithBasis.from_names([one_name]))\\n\\n    with self.assertRaisesRegex(\\n        ValueError, r\".*Input spaces to a SequenceMap must be disjoint.*\"):\\n      categorical_mlp.sequence_map_categorical_mlp(\\n          input1_space=in1_space,\\n          input2_space=in2_space,\\n          output_space=out_space,\\n          operation=lambda x, y: bases.BasisDirection(output_name, 0),\\n          one_space=bases.VectorSpaceWithBasis.from_names([one_name]))\\n\\n  @parameterized.parameters([\\n      dict(num_counts=5, x=2, fun=lambda x: x, result=2),\\n      dict(num_counts=5, x=2, fun=lambda x: math.pow(x, int(2)), result=4),\\n      dict(num_counts=5, x=-2, fun=lambda x: math.pow(x, int(2)), result=4),\\n      dict(num_counts=5, x=-1, fun=lambda x: math.pow(x, int(3)), result=-1),\\n  ])\\n  def test_map_categorical_mlp_produces_expected_outcome_computing_powers(\\n      self, num_counts, x, fun, result):\\n    input_name = \"in\"\\n    output_name = \"out\"\\n\\n    in_space = bases.VectorSpaceWithBasis.from_values(\\n        input_name, range(-num_counts, num_counts + 1))\\n    out_space = bases.VectorSpaceWithBasis.from_values(\\n        output_name, range(-num_counts, num_counts + 1))\\n\\n    def operation(direction):\\n      out_val = fun(int(direction.value))\\n      return bases.BasisDirection(output_name, out_val)\\n\\n    mlp = categorical_mlp.map_categorical_mlp(\\n        input_space=in_space, output_space=out_space, operation=operation)\\n\\n    test_inputs = mlp.residual_space.vector_from_basis_direction(\\n        bases.BasisDirection(input_name, x))\\n\\n    expected_results = mlp.residual_space.vector_from_basis_direction(\\n        bases.BasisDirection(output_name, result))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n  @parameterized.parameters([\\n      dict(x=2, fun=lambda x: x, result=2),\\n      dict(x=2, fun=lambda x: math.pow(x, int(2)), result=4),\\n      dict(x=1, fun=lambda x: 1 / (x + 1), result=0.5),\\n      dict(x=3, fun=lambda x: 1 / (x + 1), result=0.25),\\n  ])\\n  def test_map_categorical_to_numerical_mlp_produces_expected_outcome(\\n      self, x, fun, result):\\n\\n    in_space = bases.VectorSpaceWithBasis.from_values(\"in\", range(6))\\n    out_space = bases.VectorSpaceWithBasis.from_names([\"out\"])\\n\\n    mlp = categorical_mlp.map_categorical_to_numerical_mlp(\\n        input_space=in_space,\\n        output_space=out_space,\\n        operation=fun,\\n    )\\n\\n    test_inputs = mlp.residual_space.vector_from_basis_direction(\\n        bases.BasisDirection(\"in\", x))\\n\\n    expected_results = result * mlp.residual_space.vector_from_basis_direction(\\n        bases.BasisDirection(\"out\"))\\n\\n    test_outputs = mlp.apply(test_inputs)\\n\\n    self.assertVectorAllClose(test_outputs, expected_results)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/craft/chamber/categorical_mlp_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8206630>, scope_tree=SrcScope(name='tracr/craft/chamber/categorical_mlp_test.py', kind='file', range=0:6436, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6436:6436, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Attention head for categorical inputs.\"\"\"\\n\\nfrom typing import Optional\\n\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns\\nfrom typing_extensions import Protocol\\n\\n\\nclass QueryKeyToAttnLogit(Protocol):\\n\\n  def __call__(self, query: bases.BasisDirection,\\n               key: bases.BasisDirection) -> bool:\\n    pass\\n\\n\\ndef categorical_attn(\\n    query_space: bases.VectorSpaceWithBasis,\\n    key_space: bases.VectorSpaceWithBasis,\\n    value_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    bos_space: bases.VectorSpaceWithBasis,\\n    one_space: bases.VectorSpaceWithBasis,\\n    attn_fn: QueryKeyToAttnLogit,\\n    default_output: Optional[bases.VectorInBasis] = None,\\n    causal: bool = False,\\n    always_attend_to_bos: bool = False,\\n    use_bos_for_default_output: bool = True,\\n    softmax_coldness: float = 100.,\\n) -> transformers.AttentionHead:\\n  \"\"\"Returns an attention head for categorical inputs.\\n\\n  Assumes the existence of a beginning of sequence token and attends to it\\n  always with strength 0.5*softmax_coldness. This allows to implement an\\n  arbitrary default value for rows in the attention pattern that are all-zero.\\n\\n  Attends to the BOS token if all other key-query pairs have zero attention.\\n  Hence, the first value in the value sequence will be the default output for\\n  such cases.\\n\\n  Args:\\n    query_space: Vector space containing (categorical) query input.\\n    key_space: Vector space containing (categorical) key input.\\n    value_space: Vector space containing (numerical) value input.\\n    output_space: Vector space which will contain (numerical) output.\\n    bos_space: 1-d space used to identify the beginning of sequence token.\\n    one_space: 1-d space which contains 1 at every position.\\n    attn_fn: A selector function f(query, key) operating on the query/key basis\\n      directions that defines the attention pattern.\\n    default_output: Output to return if attention pattern is all zero.\\n    causal: If True, use masked attention.\\n    always_attend_to_bos: If True, always attend to the BOS token. If False,\\n      only attend to BOS when attending to nothing else.\\n    use_bos_for_default_output: If True, assume BOS is not in the value space\\n      and output a default value when attending to BOS. If False, assume BOS is\\n      in the value space, and map it to the output space like any other token.\\n    softmax_coldness: The inverse temperature of the softmax. Default value is\\n      high which makes the attention close to a hard maximum.\\n  \"\"\"\\n  bases.ensure_dims(bos_space, num_dims=1, name=\"bos_space\")\\n  bases.ensure_dims(one_space, num_dims=1, name=\"one_space\")\\n  bos_direction = bos_space.basis[0]\\n  one_direction = one_space.basis[0]\\n\\n  # Add bos direction to query, key, and value spaces in case it is missing\\n  query_space = bases.join_vector_spaces(query_space, bos_space, one_space)\\n  key_space = bases.join_vector_spaces(key_space, bos_space)\\n  value_space = bases.join_vector_spaces(value_space, bos_space)\\n\\n  if always_attend_to_bos:\\n    value_basis = value_space.basis\\n  else:\\n    value_basis = [v for v in value_space.basis if v != bos_direction]\\n  assert len(value_basis) == output_space.num_dims\\n  value_to_output = dict(zip(value_basis, output_space.basis))\\n\\n  if default_output is None:\\n    default_output = output_space.null_vector()\\n  assert default_output in output_space\\n\\n  def qk_fun(query: bases.BasisDirection, key: bases.BasisDirection) -> float:\\n\\n    # We want to enforce the following property on our attention patterns:\\n    # - if nothing else is attended to, attend to the BOS token.\\n    # - otherwise, don\\'t attend to the BOS token.\\n    #\\n    # We assume that the BOS position always only contains the vector bos + one,\\n    # and that any other position has bos coefficient 0.\\n    #\\n    # We do this as follows:\\n    # Let Q and K be subspaces of V containing the query and key vectors,\\n    # both disjoint with the BOS space {bos} or the one space {one}.\\n    # Suppose we have an attn_fn which defines a bilinear W_QK: V x V -> ℝ,\\n    # s.t. W_QK(q, k) = 0 whenever either q or k are bos or one.\\n    #\\n    # Then define W_new: V x V -> ℝ st:\\n    # W_new(one, bos) = 0.5, otherwise 0.\\n    #\\n    # Now set W_QK\\' = W_QK + W_new.\\n    #\\n    # To evaluate the attention to the BOS position:\\n    # W_QK\\'(q, bos + one)\\n    # = W_QK\\'(q, bos) + W_QK\\'(q, one)\\n    # = W_QK(q, bos) + W_QK(q, one) + W_new(q, bos) + W_new(q, one)\\n    # = 0            + 0            + W_new(q, bos) + W_new(q, one)\\n    # = W_new(q, bos) + W_new(q, one)\\n    # = W_new(q\\' + one, bos) + W_new(q\\' + one, one)  where q = one + q\\'\\n    # = W_new(q\\', bos) + W_new(one, bos) + W_new(q\\', one) + W_new(one, one)\\n    # = 0              + 0.5             + 0              + 0\\n    # = 0.5\\n    #\\n    # To evaluate the attention to a non-BOS position:\\n    # W_QK\\'(0 * bos + q, 0 * bos + k)  # s.t. q ∈ Q+{one}, k ∈ K+{one}\\n    # = 0*W_QK\\'(bos, 0*bos + k) + W_QK\\'(q, 0*bos + k)\\n    # = W_QK\\'(q, 0*bos + k)\\n    # = 0*W_QK\\'(q, bos) + W_QK\\'(q, k)\\n    # = W_QK\\'(q, k)\\n    # = W_QK(q, k)    since W_QK\\' = W_QK on inputs not containing bos.\\n    # = W_QK(q\\', k\\')  since W_QK(x, y) = 0 whenever x or y are one.\\n    #\\n    # Since W_QK(q, k) takes values in 0, 1, a sufficiently high softmax\\n    # coldness will give us the desired property.                            QED\\n    #\\n    # The following implements this idea.\\n    # By replacing 0.5 with 1, we can instead enforce a different property: that\\n    # the BOS token is always attended to in addition to whatever else.\\n\\n    if key == bos_direction and query == one_direction:\\n      c = 1. if always_attend_to_bos else 0.5\\n      return c * softmax_coldness\\n    elif {key, query}.intersection({one_direction, bos_direction}):\\n      return 0\\n\\n    return softmax_coldness * attn_fn(query, key)\\n\\n  w_qk = vectorspace_fns.ScalarBilinear.from_action(\\n      query_space,\\n      key_space,\\n      qk_fun,\\n  )\\n\\n  def ov_fun(input_dir: bases.BasisDirection) -> bases.VectorInBasis:\\n    if use_bos_for_default_output and input_dir == bos_direction:\\n      return default_output\\n    return output_space.vector_from_basis_direction(value_to_output[input_dir])\\n\\n  w_ov = vectorspace_fns.Linear.from_action(\\n      value_space,\\n      output_space,\\n      ov_fun,\\n  )\\n\\n  return transformers.AttentionHead(w_qk, w_ov, causal=causal)\\n', path=PosixPath('tracr/craft/chamber/categorical_attn.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b818fe70>, scope_tree=SrcScope(name='tracr/craft/chamber/categorical_attn.py', kind='file', range=0:7055, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=7055:7055, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"SelectorWidth component consisting of an attention head and an MLP.\"\"\"\\n\\nfrom typing import Iterable\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns\\nfrom tracr.craft.chamber import categorical_attn\\nfrom tracr.craft.chamber import numerical_mlp\\n\\n\\ndef selector_width(\\n    query_space: bases.VectorSpaceWithBasis,\\n    key_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    bos_space: bases.VectorSpaceWithBasis,\\n    one_space: bases.VectorSpaceWithBasis,\\n    attn_fn: categorical_attn.QueryKeyToAttnLogit,\\n    out_value_set: Iterable[float],\\n    categorical_output: bool,\\n    causal: bool = False,\\n    softmax_coldness: float = 100.,\\n    mlp_large_number: float = 100.,\\n    label: str = \"\",\\n) -> transformers.SeriesWithResiduals:\\n  \"\"\"Returns a craft block implementing RASP\\'s SelectorWidth primitive.\\n\\n  The block consists of one attention head and one MLP.\\n\\n  The attention head implements the attention pattern (attn_fn or key=bos) and\\n  aggregates the bos dimension over this pattern. The output of this will be\\n  1/(d+1) in every position, where d is the \"width\" of the attention pattern,\\n  i.e. the number of 1s in a row.\\n\\n  The MLP then computes d from the previous output in all positions except for\\n  the first BOS position. In the BOS position the MLP removes the output of the\\n  attention head, to ensure it only contains the encoding of the BOS token\\n  which is expected by all other model components.\\n\\n  Args:\\n    query_space: Vector space containing (categorical) query input.\\n    key_space: Vector space containing (categorical) key input.\\n    output_space: Vector space which will contain (numerical or categorical)\\n      output.\\n    bos_space: 1-d space used to identify the beginning of sequence token.\\n    one_space: Auxiliary 1-d vector space that must contain 1 in the input.\\n    attn_fn: A selector function f(query, key) operating on the query/key basis\\n      directions that defines the attention pattern to compute the width of.\\n    out_value_set: Set of possible output values of this SelectorWidth.\\n    categorical_output: If True, encode the output as a categorical variable.\\n    causal: If True, use masked attention.\\n    softmax_coldness: The inverse temperature of the softmax. Default value is\\n      high which makes the attention close to a hard maximum.\\n    mlp_large_number: A larger number makes the MLP more accurate.\\n    label: A name for this block, used to label auxiliary dimensions.\\n  \"\"\"\\n  assert output_space.num_dims == 1 or categorical_output\\n\\n  attn_out_dir = bases.BasisDirection(f\"{label}_selector_width_attn_output\")\\n  attn_out_space = bases.VectorSpaceWithBasis([attn_out_dir])\\n  attn_out_vec = attn_out_space.vector_from_basis_direction(attn_out_dir)\\n\\n  attn = categorical_attn.categorical_attn(\\n      query_space=query_space,\\n      key_space=key_space,\\n      value_space=bos_space,\\n      output_space=attn_out_space,\\n      bos_space=bos_space,\\n      one_space=one_space,\\n      attn_fn=attn_fn,\\n      default_output=attn_out_space.null_vector(),\\n      causal=causal,\\n      always_attend_to_bos=True,\\n      use_bos_for_default_output=False,\\n      softmax_coldness=softmax_coldness)\\n\\n  fun = lambda x: (1 / x) - 1\\n  in_value_set = {1 / (x + 1) for x in out_value_set}\\n  if categorical_output:\\n    mlp = numerical_mlp.map_numerical_to_categorical_mlp(\\n        f=fun,\\n        input_space=attn_out_space,\\n        output_space=output_space,\\n        input_value_set=in_value_set,\\n        one_space=one_space,\\n        hidden_name=f\"_hidden_{label}_\",\\n        large_number=mlp_large_number)\\n  else:\\n    mlp = numerical_mlp.map_numerical_mlp(\\n        f=fun,\\n        input_space=attn_out_space,\\n        output_space=output_space,\\n        input_value_set=in_value_set,\\n        one_space=one_space,\\n        hidden_name=f\"_hidden_{label}_\",\\n        large_number=mlp_large_number)\\n\\n  # This implementation of selector width writes at each position including\\n  # the BOS. To ensure that the BOS token position does not contain\\n  # additional values, we add an mlp to subtract the output of both layers.\\n  clean_bos_out_space = bases.join_vector_spaces(attn_out_space, output_space)\\n  vec_to_subtract_from_bos = attn_out_vec.project(clean_bos_out_space)\\n\\n  if categorical_output:\\n    # Add the one-hot encoding of the zero value to the vector\\n    # which will get scrubbed from the BOS position.\\n    zero_dir = [d for d in output_space.basis if d.value == 0][0]\\n    zero_vec = clean_bos_out_space.vector_from_basis_direction(zero_dir)\\n    vec_to_subtract_from_bos += zero_vec\\n\\n  # Construct an MLP that subtracts vec_to_subtract_from_bos * bos\\n  # from the residual stream which is vec_to_subtract_from_bos in the\\n  # bos position and 0 else. vec_to_subtract_from_bos contains what the\\n  # attention head writes to the bos position.\\n\\n  hidden_dir = bases.BasisDirection(\"_hidden_clean_bos_\")\\n  hidden_space = bases.VectorSpaceWithBasis([hidden_dir])\\n  hidden_vec = hidden_space.vector_from_basis_direction(hidden_dir)\\n\\n  # It\\'s okay to use the local variables because they are only used within\\n  # the same loop iteration to create the MLP.\\n  # pylint: disable=cell-var-from-loop\\n  first_layer = vectorspace_fns.Linear.from_action(bos_space, hidden_space,\\n                                                   lambda x: hidden_vec)\\n  second_layer = vectorspace_fns.Linear.from_action(\\n      hidden_space, clean_bos_out_space, lambda x: -vec_to_subtract_from_bos)\\n  # pylint: enable=cell-var-from-loop\\n  clean_bos_mlp = transformers.MLP(first_layer, second_layer)\\n\\n  mlp = transformers.MLP.combine_in_parallel([mlp, clean_bos_mlp])\\n  return transformers.SeriesWithResiduals([attn, mlp])\\n', path=PosixPath('tracr/craft/chamber/selector_width.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b815bc10>, scope_tree=SrcScope(name='tracr/craft/chamber/selector_width.py', kind='file', range=0:6406, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6406:6406, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Create a craft model from a computational graph.\"\"\"\\n\\nimport collections\\nfrom typing import Dict, List, Sequence\\n\\nimport networkx as nx\\nfrom tracr.compiler import nodes\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.rasp import rasp\\n\\nNode = nodes.Node\\nNodeID = nodes.NodeID\\n\\n\\ndef _get_longest_path_length_to_node(graph: nx.DiGraph, sources: Sequence[Node],\\n                                     node: Node) -> int:\\n  \"\"\"Returns the lengths of the longest path from sources to node.\\n\\n  Only SOps count towards the length of a path.\\n\\n  Args:\\n    graph: DAG to compute longest path in.\\n    sources: List of starting nodes, longest path will be a maximum over all.\\n    node: Target node.\\n\\n  Returns:\\n    Number of steps needed for the longest path from the source to the node, or\\n    -1 if there is no path from any of the sources to the target node.\\n  \"\"\"\\n  if node in sources:\\n    return 0\\n\\n  def num_sops(path: Sequence[NodeID]) -> int:\\n    num = 0\\n    for node_id in path:\\n      if isinstance(graph.nodes[node_id][nodes.EXPR], rasp.SOp):\\n        num += 1\\n    return num\\n\\n  result = -1\\n  for source in sources:\\n    all_paths = nx.all_simple_paths(graph, source[nodes.ID], node[nodes.ID])\\n    longest_path_len = max(map(num_sops, all_paths), default=-1) - 1\\n    if longest_path_len > result:\\n      result = longest_path_len\\n  return result\\n\\n\\ndef _node_is_attn(node: Node) -> bool:\\n  \"\"\"Returns True if node is an attention layer.\"\"\"\\n  return nodes.MODEL_BLOCK in node and isinstance(\\n      node[nodes.MODEL_BLOCK],\\n      (transformers.AttentionHead, transformers.MultiAttentionHead))\\n\\n\\ndef _node_is_mlp(node: Node) -> bool:\\n  \"\"\"Returns True if node is an MLP layer.\"\"\"\\n  return nodes.MODEL_BLOCK in node and isinstance(node[nodes.MODEL_BLOCK],\\n                                                  transformers.MLP)\\n\\n\\ndef _node_is_residual_block(node: Node) -> bool:\\n  \"\"\"Returns True if node is a valid residual block (Attn followed by MLP).\"\"\"\\n  block = node[nodes.MODEL_BLOCK] if nodes.MODEL_BLOCK in node else None\\n  if block and isinstance(block, transformers.SeriesWithResiduals):\\n    if len(block.blocks) == 2:\\n      attn, mlp = block.blocks\\n      if (isinstance(\\n          attn,\\n          (transformers.AttentionHead, transformers.MultiAttentionHead)) and\\n          isinstance(mlp, transformers.MLP)):\\n        return True\\n  return False\\n\\n\\ndef _all_attn_nodes(node_list: Sequence[Node]) -> bool:\\n  \"\"\"Returns True iff all nodes are attention layers (or nodes is empty).\"\"\"\\n  for node in node_list:\\n    if not _node_is_attn(node):\\n      return False\\n  return True\\n\\n\\ndef _all_mlp_nodes(node_list: Sequence[Node]) -> bool:\\n  \"\"\"Returns True iff all nodes are MLP layers (or nodes is empty).\"\"\"\\n  for node in node_list:\\n    if not _node_is_mlp(node):\\n      return False\\n  return True\\n\\n\\ndef _allocate_modules_to_layers(graph: nx.DiGraph,\\n                                sources: Sequence[Node]) -> Dict[int, int]:\\n  \"\"\"Allocate all nodes in compute graph to layers.\\n\\n  First, computes the longest path from the input to each node that is a model\\n  component (not input and output nodes). The longest path to a model component\\n  (its \"depth\") determines a layer in which we can place it while ensuring that\\n  all necessary previous computations have already happened.\\n\\n  This assumes layers are arranged as [Attention, MLP, Attention, MLP, ...]\\n\\n  In the special case where there are only Attention layers at one depth level\\n  and only MLP layers in the next depth layer, they are treated as if there\\n  are at the same depth because attention layers always come before MLP layers\\n  for the same depth.\\n\\n  Args:\\n    graph: RASP graph with craft blocks.\\n    sources: List of input nodes\\n\\n  Returns:\\n    A dict mapping from node ids to layer indices, where 0, 1, 2, 3, ...\\n    are in the order attention, mlp, attention, mlp, ...\\n  \"\"\"\\n  layer_allocation: Dict[int, int] = collections.defaultdict(lambda: -1)\\n  depth_by_node_id: Dict[int, int] = dict()\\n  nodes_by_depth: Dict[int, List[Node]] = collections.defaultdict(list)\\n\\n  # Compute depth of all model components (longest path from source to node)\\n  for node_id, node in graph.nodes.items():\\n    if (_node_is_mlp(node) or _node_is_attn(node)\\n        or _node_is_residual_block(node)):\\n      # Node is a model component\\n      longest_path_len = _get_longest_path_length_to_node(graph, sources, node)\\n      depth_by_node_id[node_id] = longest_path_len\\n      nodes_by_depth[longest_path_len].append(node)\\n\\n  # If at level `depth` there are only attention heads and at level `depths + 1`\\n  # there are only MLPs, we can condense them into one level\\n  # TODO(b/255936816): Think about improving this heuristic. The heuristic is\\n  # not optimal, and only catches very basic opportunities for optimization. It\\n  # is easy to come up with opportunities for optimization that it does not\\n  # catch.\\n  min_depth, max_depth = min(nodes_by_depth.keys()), max(nodes_by_depth.keys())\\n  depth = min_depth\\n  while depth < max_depth:\\n    if _all_attn_nodes(nodes_by_depth[depth]) and _all_mlp_nodes(\\n        nodes_by_depth[depth + 1]):\\n      # Condense by decrementing the depth of all nodes starting from depth+1\\n      for update_depth in range(depth + 1, max_depth + 1):\\n        for node in nodes_by_depth[update_depth]:\\n          node_id = node[nodes.ID]\\n          depth_by_node_id[node_id] = update_depth - 1\\n        nodes_by_depth[update_depth - 1].extend(nodes_by_depth[update_depth])\\n        nodes_by_depth[update_depth] = []\\n      max_depth -= 1\\n    depth += 1\\n\\n  # Allocate nodes to layers by depth, ensuring attn -> mlp -> attn -> mlp ...\\n  current_layer = 0\\n  current_depth = 1\\n  for node_id, depth in sorted(depth_by_node_id.items(), key=lambda x: x[1]):\\n    while depth > current_depth:\\n      current_depth += 1\\n      current_layer += 2\\n    if depth == current_depth:\\n      if _node_is_residual_block(graph.nodes[node_id]):\\n        layer_allocation[node_id] = current_layer\\n      else:\\n        is_mlp = _node_is_mlp(graph.nodes[node_id])\\n        layer_allocation[node_id] = current_layer + int(is_mlp)\\n\\n  return layer_allocation\\n\\n\\ndef craft_graph_to_model(\\n    graph: nx.DiGraph,\\n    sources: Sequence[Node]) -> transformers.SeriesWithResiduals:\\n  \"\"\"Translates a RASP graph with craft blocks into a full craft model.\\n\\n  1. Allocate modules to layers, assuming layers in the order\\n  2. Creates subspaces for all inputs and outputs, and builds residual stream.\\n  3. Assembles everything into a craft model and returns it.\\n\\n  Args:\\n    graph: RASP graph with craft blocks.\\n    sources: List of input nodes\\n\\n  Returns:\\n    A craft model that can be compiled to model weights.\\n\\n  Raises:\\n    ValueError: On invalid input (if the craft_graph does not have craft blocks\\n      already specified)\\n  \"\"\"\\n  layer_allocation = _allocate_modules_to_layers(graph, sources)\\n  blocks_by_layer = collections.defaultdict(list)\\n  model_blocks = []\\n\\n  residual_space = bases.VectorSpaceWithBasis([])\\n\\n  for node_id, layer_no in layer_allocation.items():\\n    node = graph.nodes[node_id]\\n    block = node[nodes.MODEL_BLOCK] if nodes.MODEL_BLOCK in node else None\\n\\n    if _node_is_residual_block(node):\\n      assert isinstance(block, transformers.SeriesWithResiduals)\\n      assert len(block.blocks) == 2\\n      residual_space = bases.join_vector_spaces(residual_space,\\n                                                block.blocks[0].residual_space,\\n                                                block.blocks[1].residual_space)\\n      blocks_by_layer[layer_no].append(block.blocks[0])\\n      blocks_by_layer[layer_no + 1].append(block.blocks[1])\\n    elif block:\\n      residual_space = bases.join_vector_spaces(\\n          residual_space, node[nodes.MODEL_BLOCK].residual_space)\\n      blocks_by_layer[layer_no].append(block)\\n\\n  for layer_no, layer_blocks in sorted(\\n      blocks_by_layer.items(), key=lambda x: x[0]):\\n    for block in layer_blocks:\\n      block.residual_space = residual_space\\n\\n    if layer_blocks:\\n      if layer_no % 2 == 0:  # Attention Layer\\n        multi_head_attn = transformers.MultiAttentionHead(layer_blocks)\\n        model_blocks.append(multi_head_attn)\\n      else:  # MLP Layer\\n        parallel_mlp = transformers.MLP.combine_in_parallel(layer_blocks)\\n        model_blocks.append(parallel_mlp)\\n\\n  return transformers.SeriesWithResiduals(model_blocks)\\n', path=PosixPath('tracr/compiler/craft_graph_to_model.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b80fa110>, scope_tree=SrcScope(name='tracr/compiler/craft_graph_to_model.py', kind='file', range=0:9023, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=9023:9023, code=''), len(children)=9)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Integration tests for the full RASP -> transformer compilation.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport jax\\nimport numpy as np\\n\\nfrom tracr.compiler import compiling\\nfrom tracr.compiler import lib\\nfrom tracr.compiler import test_cases\\nfrom tracr.craft import tests_common\\nfrom tracr.rasp import rasp\\n\\n_COMPILER_BOS = \"rasp_to_transformer_integration_test_BOS\"\\n_COMPILER_PAD = \"rasp_to_transformer_integration_test_PAD\"\\n\\n# Force float32 precision on TPU, which otherwise defaults to float16.\\njax.config.update(\"jax_default_matmul_precision\", \"float32\")\\n\\n\\nclass CompilerIntegrationTest(tests_common.VectorFnTestCase):\\n\\n  def assertSequenceEqualWhenExpectedIsNotNone(self, actual_seq, expected_seq):\\n    for actual, expected in zip(actual_seq, expected_seq):\\n      if expected is not None and actual != expected:\\n        self.fail(f\"{actual_seq} does not match (ignoring Nones) \"\\n                  f\"expected_seq={expected_seq}\")\\n\\n  @parameterized.named_parameters(\\n      dict(\\n          testcase_name=\"map\",\\n          program=rasp.Map(lambda x: x + 1, rasp.tokens)),\\n      dict(\\n          testcase_name=\"sequence_map\",\\n          program=rasp.SequenceMap(lambda x, y: x + y, rasp.tokens,\\n                                   rasp.indices)),\\n      dict(\\n          testcase_name=\"sequence_map_with_same_input\",\\n          program=rasp.SequenceMap(lambda x, y: x + y, rasp.tokens,\\n                                   rasp.indices)),\\n      dict(\\n          testcase_name=\"select_aggregate\",\\n          program=rasp.Aggregate(\\n              rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.EQ),\\n              rasp.Map(lambda x: 1, rasp.tokens))))\\n  def test_rasp_program_and_transformer_produce_same_output(self, program):\\n    vocab = {0, 1, 2}\\n    max_seq_len = 3\\n    assembled_model = compiling.compile_rasp_to_model(\\n        program, vocab, max_seq_len, compiler_bos=_COMPILER_BOS)\\n\\n    test_outputs = {}\\n    rasp_outputs = {}\\n    for val in vocab:\\n      test_outputs[val] = assembled_model.apply([_COMPILER_BOS, val]).decoded[1]\\n      rasp_outputs[val] = program([val])[0]\\n\\n    with self.subTest(val=0):\\n      self.assertEqual(test_outputs[0], rasp_outputs[0])\\n    with self.subTest(val=1):\\n      self.assertEqual(test_outputs[1], rasp_outputs[1])\\n    with self.subTest(val=2):\\n      self.assertEqual(test_outputs[2], rasp_outputs[2])\\n\\n  @parameterized.named_parameters(*test_cases.TEST_CASES)\\n  def test_compiled_models_produce_expected_output(self, program, vocab,\\n                                                   test_input, expected_output,\\n                                                   max_seq_len, **kwargs):\\n    del kwargs\\n    assembled_model = compiling.compile_rasp_to_model(\\n        program, vocab, max_seq_len, compiler_bos=_COMPILER_BOS)\\n    test_output = assembled_model.apply([_COMPILER_BOS] + test_input)\\n\\n    if isinstance(expected_output[0], (int, float)):\\n      np.testing.assert_allclose(\\n          test_output.decoded[1:], expected_output, atol=1e-7, rtol=0.005)\\n    else:\\n      self.assertSequenceEqualWhenExpectedIsNotNone(test_output.decoded[1:],\\n                                                    expected_output)\\n\\n  @parameterized.named_parameters(*test_cases.CAUSAL_TEST_CASES)\\n  def test_compiled_causal_models_produce_expected_output(\\n      self, program, vocab, test_input, expected_output, max_seq_len, **kwargs):\\n    del kwargs\\n    assembled_model = compiling.compile_rasp_to_model(\\n        program,\\n        vocab,\\n        max_seq_len,\\n        causal=True,\\n        compiler_bos=_COMPILER_BOS,\\n        compiler_pad=_COMPILER_PAD)\\n    test_output = assembled_model.apply([_COMPILER_BOS] + test_input)\\n\\n    if isinstance(expected_output[0], (int, float)):\\n      np.testing.assert_allclose(\\n          test_output.decoded[1:], expected_output, atol=1e-7, rtol=0.005)\\n    else:\\n      self.assertSequenceEqualWhenExpectedIsNotNone(test_output.decoded[1:],\\n                                                    expected_output)\\n\\n  @parameterized.named_parameters(\\n      dict(\\n          testcase_name=\"reverse_1\",\\n          program=lib.make_reverse(rasp.tokens),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"abcd\"),\\n          expected_output=list(\"dcba\"),\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"reverse_2\",\\n          program=lib.make_reverse(rasp.tokens),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"abc\"),\\n          expected_output=list(\"cba\"),\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"reverse_3\",\\n          program=lib.make_reverse(rasp.tokens),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"ad\"),\\n          expected_output=list(\"da\"),\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"reverse_4\",\\n          program=lib.make_reverse(rasp.tokens),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=[\"c\"],\\n          expected_output=[\"c\"],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_categorical_1\",\\n          program=rasp.categorical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"abc\"),\\n          expected_output=[3, 3, 3],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_categorical_2\",\\n          program=rasp.categorical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"ad\"),\\n          expected_output=[2, 2],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_categorical_3\",\\n          program=rasp.categorical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=[\"c\"],\\n          expected_output=[1],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_numerical_1\",\\n          program=rasp.numerical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"abc\"),\\n          expected_output=[3, 3, 3],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_numerical_2\",\\n          program=rasp.numerical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=list(\"ad\"),\\n          expected_output=[2, 2],\\n          max_seq_len=5),\\n      dict(\\n          testcase_name=\"length_numerical_3\",\\n          program=rasp.numerical(lib.make_length()),\\n          vocab={\"a\", \"b\", \"c\", \"d\"},\\n          test_input=[\"c\"],\\n          expected_output=[1],\\n          max_seq_len=5),\\n  )\\n  def test_compiled_models_produce_expected_output_with_padding(\\n      self, program, vocab, test_input, expected_output, max_seq_len, **kwargs):\\n    del kwargs\\n    assembled_model = compiling.compile_rasp_to_model(\\n        program,\\n        vocab,\\n        max_seq_len,\\n        compiler_bos=_COMPILER_BOS,\\n        compiler_pad=_COMPILER_PAD)\\n\\n    pad_len = (max_seq_len - len(test_input))\\n    test_input = test_input + [_COMPILER_PAD] * pad_len\\n    test_input = [_COMPILER_BOS] + test_input\\n    test_output = assembled_model.apply(test_input)\\n    output = test_output.decoded\\n    output_len = len(output)\\n    output_stripped = test_output.decoded[1:output_len - pad_len]\\n\\n    self.assertEqual(output[0], _COMPILER_BOS)\\n    if isinstance(expected_output[0], (int, float)):\\n      np.testing.assert_allclose(\\n          output_stripped, expected_output, atol=1e-7, rtol=0.005)\\n    else:\\n      self.assertEqual(output_stripped, expected_output)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/rasp_to_transformer_integration_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b81203f0>, scope_tree=SrcScope(name='tracr/compiler/rasp_to_transformer_integration_test.py', kind='file', range=0:8104, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=8104:8104, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for compiler.basis_inference.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.compiler import basis_inference\\nfrom tracr.compiler import nodes\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.rasp import rasp\\n\\n\\nclass InferBasesTest(parameterized.TestCase):\\n\\n  def test_arithmetic_error_logs_warning(self):\\n    program = rasp.numerical(rasp.Map(lambda x: 1 / x, rasp.tokens))\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n    vocab = {0, 1, 2}\\n    with self.assertLogs(level=\"WARNING\"):\\n      basis_inference.infer_bases(\\n          extracted.graph,\\n          extracted.sink,\\n          vocab,\\n          max_seq_len=1,\\n      )\\n\\n  @parameterized.parameters(({1, 2, 3}, {2, 3, 4}), ({0, 5}, {1, 6}))\\n  def test_one_edge(self, vocab, expected_value_set):\\n    program = rasp.categorical(rasp.Map(lambda x: x + 1, rasp.tokens))\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=1,\\n    )\\n\\n    self.assertSetEqual(\\n        extracted.graph.nodes[program.label][nodes.VALUE_SET],\\n        expected_value_set,\\n    )\\n\\n  def test_primitive_close_to_tip(self):\\n    intermediate = rasp.categorical(rasp.tokens + 1)\\n    intermediate = rasp.categorical(intermediate + intermediate)\\n    program = rasp.categorical(intermediate + rasp.indices)\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        {0, 1},\\n        max_seq_len=2,\\n    )\\n\\n    self.assertSetEqual(\\n        extracted.graph.nodes[program.label][nodes.VALUE_SET],\\n        {2, 3, 4, 5},\\n    )\\n    self.assertSetEqual(\\n        extracted.graph.nodes[intermediate.label][nodes.VALUE_SET],\\n        {2, 3, 4},\\n    )\\n\\n  def test_categorical_aggregate(self):\\n    program = rasp.categorical(\\n        rasp.Aggregate(\\n            rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n            rasp.indices,\\n        ))\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        {0, 1},\\n        max_seq_len=3,\\n    )\\n\\n    self.assertSetEqual(\\n        extracted.graph.nodes[program.label][nodes.VALUE_SET],\\n        {0, 1, 2},\\n    )\\n\\n  def test_numerical_aggregate(self):\\n    program = rasp.numerical(\\n        rasp.Aggregate(\\n            rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n            rasp.indices,\\n        ))\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        {0, 1},\\n        max_seq_len=2,\\n    )\\n\\n    self.assertSetEqual(\\n        extracted.graph.nodes[program.label][nodes.VALUE_SET],\\n        {0, 1, 1 / 2},\\n    )\\n\\n  def test_selector_width(self):\\n    program = rasp.SelectorWidth(\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ))\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        {0, 1},\\n        max_seq_len=2,\\n    )\\n\\n    self.assertSetEqual(\\n        extracted.graph.nodes[program.label][nodes.VALUE_SET],\\n        {0, 1, 2},\\n    )\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/basis_inference_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b80ccaf0>, scope_tree=SrcScope(name='tracr/compiler/basis_inference_test.py', kind='file', range=0:4027, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=4027:4027, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Combines all steps of compiling a RASP program.\"\"\"\\n\\nfrom typing import Set\\n\\nfrom tracr.compiler import assemble\\nfrom tracr.compiler import basis_inference\\nfrom tracr.compiler import craft_graph_to_model\\nfrom tracr.compiler import craft_model_to_transformer\\nfrom tracr.compiler import expr_to_craft_graph\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.craft import bases\\nfrom tracr.rasp import rasp\\n\\nCOMPILER_BOS = \"compiler_bos\"\\nCOMPILER_PAD = \"compiler_pad\"\\n\\n\\ndef compile_rasp_to_model(\\n    program: rasp.SOp,\\n    vocab: Set[rasp.Value],\\n    max_seq_len: int,\\n    causal: bool = False,\\n    compiler_bos: str = COMPILER_BOS,\\n    compiler_pad: str = COMPILER_PAD,\\n    mlp_exactness: int = 100) -> assemble.AssembledTransformerModel:\\n  \"\"\"Compile a RASP program to transformer weights.\\n\\n  Args:\\n    program: the RASP program to compile.\\n    vocab: the set of vocab tokens expected by RASP.\\n    max_seq_len: the maximum sequence length for the compiled model.\\n    causal: if True, outputs a model with causal masking.\\n    compiler_bos: the name of the special BOS token that will be added by the\\n      compiler. Must not be present in the vocab.\\n    compiler_pad: the name of the special PAD token that will be added by the\\n      compiler. Must not be present in the vocab.\\n    mlp_exactness: Controls the approximation of the MLP layers. In theory,\\n      larger values yield a better approximation. But too large values can cause\\n      numerical issues due to large parameter norms. Reasonable values are\\n      between 1 and 100.\\n\\n  Returns:\\n    The compiled model.\\n  \"\"\"\\n\\n  if compiler_bos in vocab:\\n    raise ValueError(\"Compiler BOS token must not be present in the vocab. \"\\n                     f\"Found \\'{compiler_bos}\\' in {vocab}\")\\n\\n  if compiler_pad in vocab:\\n    raise ValueError(\"Compiler PAD token must not be present in the vocab. \"\\n                     f\"Found \\'{compiler_pad}\\' in {vocab}\")\\n\\n  extracted = rasp_to_graph.extract_rasp_graph(program)\\n  graph, sources, sink = extracted.graph, extracted.sources, extracted.sink\\n\\n  basis_inference.infer_bases(\\n      graph,\\n      sink,\\n      vocab,\\n      max_seq_len,\\n  )\\n\\n  expr_to_craft_graph.add_craft_components_to_rasp_graph(\\n      graph,\\n      bos_dir=bases.BasisDirection(rasp.tokens.label, compiler_bos),\\n      mlp_exactness=mlp_exactness,\\n  )\\n\\n  craft_model = craft_graph_to_model.craft_graph_to_model(graph, sources)\\n\\n  return craft_model_to_transformer.craft_model_to_transformer(\\n      craft_model=craft_model,\\n      graph=graph,\\n      sink=sink,\\n      max_seq_len=max_seq_len,\\n      causal=causal,\\n      compiler_bos=compiler_bos,\\n      compiler_pad=compiler_pad,\\n  )\\n', path=PosixPath('tracr/compiler/compiling.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8086a90>, scope_tree=SrcScope(name='tracr/compiler/compiling.py', kind='file', range=0:3339, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=3339:3339, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Provides the main compiler function as a public import.\"\"\"\\n\\nfrom tracr.compiler.compiling import compile_rasp_to_model\\n\\n__all__ = [\"compile_rasp_to_model\"]\\n', path=PosixPath('tracr/compiler/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b80a6430>, scope_tree=SrcScope(name='tracr/compiler/__init__.py', kind='file', range=0:855, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=855:855, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Add craft model blocks to graph of RASPExpr.\"\"\"\\n\\nfrom typing import Any, Callable, Optional\\n\\nimport networkx as nx\\nfrom tracr.compiler import nodes\\nfrom tracr.craft import bases\\nfrom tracr.craft.chamber import categorical_attn\\nfrom tracr.craft.chamber import categorical_mlp\\nfrom tracr.craft.chamber import numerical_mlp\\nfrom tracr.craft.chamber import selector_width\\nfrom tracr.rasp import rasp\\n\\n\\ndef _transform_fun_to_basis_fun(\\n    fun: Callable[..., Any],\\n    output_direction_name: Optional[str] = None) -> Callable[..., Any]:\\n  \"\"\"Transforms a function acting on values into one acting on directions.\"\"\"\\n\\n  def bases_fun(*args):\\n    values = [d.value for d in args]\\n    result = fun(*values)\\n    if output_direction_name:\\n      return bases.BasisDirection(output_direction_name, result)\\n    return result\\n\\n  return bases_fun\\n\\n\\ndef _check_selector_expression(expr, graph):\\n  \"\"\"Check graph structure and encodings for an aggregate or selector width.\"\"\"\\n  sel_expr = expr.selector\\n\\n  # Check graph structure\\n  assert sel_expr.label in graph.predecessors(expr.label)\\n  assert sel_expr.keys.label in graph.predecessors(sel_expr.label)\\n  assert sel_expr.queries.label in graph.predecessors(sel_expr.label)\\n\\n  if (not rasp.is_categorical(sel_expr.queries) or\\n      not rasp.is_categorical(sel_expr.keys)):\\n    raise ValueError(\"Selector keys and queries must be categorical.\")\\n\\n\\ndef add_craft_components_to_rasp_graph(\\n    graph: nx.DiGraph,\\n    bos_dir: bases.BasisDirection = bases.BasisDirection(\"tokens\", \"bos\"),\\n    one_dir: bases.BasisDirection = bases.BasisDirection(\"one\"),\\n    causal: bool = False,\\n    mlp_exactness: float = 100,\\n) -> None:\\n  \"\"\"Translates expressions to craft blocks and attaches them to the graph.\\n\\n  Sets the `MODEL_BLOCK` attribute for all nodes in `graph`.\\n\\n  Args:\\n    graph: RASP graph with  `VALUE_SET` but not `MODEL_BLOCK` attributes.\\n    bos_dir: Basis direction representing beginning of sequence (bos) token.\\n    one_dir: Auxiliary basis direction that must contain 1.\\n    causal: If True, marks attention blocks as causal.\\n    mlp_exactness: Controls the approximation of the MLP layers.\\n\\n  Raises:\\n    ValueError: On invalid input (if `MODEL_BLOCK` is set already, or\\n      `VALUE_SET` is not set already)\\n    NotImplementedError: If the graph contains an unsupported expression.\\n  \"\"\"\\n  one_space = bases.VectorSpaceWithBasis([one_dir])\\n\\n  for node_id, node in graph.nodes.items():\\n    expr = node[nodes.EXPR]\\n\\n    if not isinstance(expr, rasp.SOp):\\n      continue\\n\\n    if nodes.MODEL_BLOCK in node and node[nodes.MODEL_BLOCK]:\\n      raise ValueError(\"Input graph cannot have model blocks set already.\")\\n    if nodes.VALUE_SET not in node:\\n      raise ValueError(\\n          \"Craft components can only be added after basis inference.\")\\n\\n    if expr is rasp.tokens or expr is rasp.indices:\\n      block = None\\n    elif isinstance(expr, rasp.Map):\\n      inner_expr, inner_node = expr.inner, graph.nodes[expr.inner.label]\\n      assert inner_expr.label in graph.predecessors(node_id)\\n      input_space = bases.VectorSpaceWithBasis(inner_node[nodes.OUTPUT_BASIS])\\n      output_space = bases.VectorSpaceWithBasis(node[nodes.OUTPUT_BASIS])\\n\\n      if rasp.is_categorical(inner_expr) and rasp.is_categorical(expr):\\n        basis_fun = _transform_fun_to_basis_fun(expr.f, expr.label)\\n        block = categorical_mlp.map_categorical_mlp(\\n            input_space=input_space,\\n            output_space=output_space,\\n            operation=basis_fun)\\n      elif rasp.is_categorical(inner_expr) and rasp.is_numerical(expr):\\n        block = categorical_mlp.map_categorical_to_numerical_mlp(\\n            input_space=input_space,\\n            output_space=output_space,\\n            operation=expr.f,\\n        )\\n      elif rasp.is_numerical(inner_expr) and rasp.is_categorical(expr):\\n        block = numerical_mlp.map_numerical_to_categorical_mlp(\\n            f=expr.f,\\n            input_space=input_space,\\n            output_space=output_space,\\n            input_value_set=inner_node[nodes.VALUE_SET],\\n            one_space=one_space,\\n            hidden_name=f\"_hidden_{expr.label}_\",\\n            large_number=mlp_exactness)\\n      elif rasp.is_numerical(inner_expr) and rasp.is_numerical(expr):\\n        block = numerical_mlp.map_numerical_mlp(\\n            f=expr.f,\\n            input_space=input_space,\\n            output_space=output_space,\\n            input_value_set=inner_node[nodes.VALUE_SET],\\n            one_space=one_space,\\n            hidden_name=f\"_hidden_{expr.label}_\",\\n            large_number=mlp_exactness)\\n      else:\\n        raise NotImplementedError(\"Map does no support \"\\n                                  f\"in_type \\'{inner_expr.type}\\' and\"\\n                                  f\" out_type \\'{expr.type}\\'!\")\\n\\n    elif isinstance(expr, rasp.SequenceMap):\\n      fst_expr, fst_node = expr.fst, graph.nodes[expr.fst.label]\\n      snd_expr, snd_node = expr.snd, graph.nodes[expr.snd.label]\\n\\n      # Check graph structure\\n      assert fst_expr.label in graph.predecessors(node_id)\\n      assert snd_expr.label in graph.predecessors(node_id)\\n\\n      fst_space = bases.VectorSpaceWithBasis(fst_node[nodes.OUTPUT_BASIS])\\n      snd_space = bases.VectorSpaceWithBasis(snd_node[nodes.OUTPUT_BASIS])\\n      out_space = bases.VectorSpaceWithBasis(node[nodes.OUTPUT_BASIS])\\n\\n      if (isinstance(expr, rasp.LinearSequenceMap) and\\n          not all(rasp.is_numerical(x) for x in (fst_expr, snd_expr, expr))):\\n        raise NotImplementedError(\"Linear SequenceMap only supports numerical \"\\n                                  \"inputs/outputs.\")\\n      elif (\\n          not isinstance(expr, rasp.LinearSequenceMap) and\\n          not all(rasp.is_categorical(x) for x in (fst_expr, snd_expr, expr))):\\n        raise NotImplementedError(\"(Non-linear) SequenceMap only supports \"\\n                                  \"categorical inputs/outputs.\")\\n\\n      if isinstance(expr, rasp.LinearSequenceMap):\\n        assert len(fst_space.basis) == 1\\n        assert len(snd_space.basis) == 1\\n        assert len(out_space.basis) == 1\\n        block = numerical_mlp.linear_sequence_map_numerical_mlp(\\n            input1_basis_direction=fst_space.basis[0],\\n            input2_basis_direction=snd_space.basis[0],\\n            output_basis_direction=out_space.basis[0],\\n            input1_factor=expr.fst_fac,\\n            input2_factor=expr.snd_fac,\\n            hidden_name=f\"_hidden_{expr.label}_\")\\n      elif fst_space == snd_space:\\n        # It\\'s okay to use the local variable expr.f because it is\\n        # only used within the same loop iteration to create the MLP.\\n        # pylint: disable=cell-var-from-loop\\n        basis_fun = _transform_fun_to_basis_fun(lambda x: expr.f(x, x),\\n                                                expr.label)\\n        block = categorical_mlp.map_categorical_mlp(\\n            input_space=fst_space, output_space=out_space, operation=basis_fun)\\n      else:\\n        basis_fun = _transform_fun_to_basis_fun(expr.f, expr.label)\\n        block = categorical_mlp.sequence_map_categorical_mlp(\\n            input1_space=fst_space,\\n            input2_space=snd_space,\\n            output_space=out_space,\\n            operation=basis_fun,\\n            one_space=one_space,\\n            hidden_name=f\"_hidden_{expr.label}_\")\\n    elif isinstance(expr, rasp.Aggregate):\\n      sel_expr: rasp.Select = expr.selector\\n      agg_expr: rasp.Aggregate = expr\\n\\n      if not isinstance(sel_expr, rasp.Select):\\n        raise TypeError(\"Compiling composite Selectors is not supported. \"\\n                        f\"Got a {sel_expr}.\")\\n\\n      queries = graph.nodes[sel_expr.queries.label]\\n      keys = graph.nodes[sel_expr.keys.label]\\n      sop = graph.nodes[agg_expr.sop.label]\\n\\n      _check_selector_expression(expr, graph)\\n      assert agg_expr.sop.label in graph.predecessors(node_id)\\n      if rasp.get_encoding(agg_expr.sop) != rasp.get_encoding(agg_expr):\\n        raise ValueError(\\n            \"sop encoding must match output encoding of the aggregate.\")\\n      if rasp.is_categorical(agg_expr) and agg_expr.default is not None:\\n        raise ValueError(\"Default for a categorical aggregate must be None. \"\\n                         f\"Got {agg_expr.default}\")\\n      if rasp.is_numerical(agg_expr) and agg_expr.default != 0:\\n        raise ValueError(\"Default for a numerical aggregate must be 0. \"\\n                         f\"Got {agg_expr.default}\")\\n\\n      bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n      one_space = bases.VectorSpaceWithBasis([one_dir])\\n      query_space = bases.VectorSpaceWithBasis(queries[nodes.OUTPUT_BASIS])\\n      key_space = bases.VectorSpaceWithBasis(keys[nodes.OUTPUT_BASIS])\\n      value_space = bases.VectorSpaceWithBasis(sop[nodes.OUTPUT_BASIS])\\n      output_space = bases.VectorSpaceWithBasis(node[nodes.OUTPUT_BASIS])\\n\\n      # Argument order is different in craft / transformers than RASP selectors\\n      def attn_basis_fn(query: bases.BasisDirection,\\n                        key: bases.BasisDirection) -> bool:\\n        # It\\'s okay to use the local variable sel_expr because this function is\\n        # only used within the same loop iteration to create an attention head.\\n        # pylint: disable=cell-var-from-loop\\n        selector_basis_fn = _transform_fun_to_basis_fun(sel_expr.predicate)\\n        return selector_basis_fn(key, query)\\n\\n      block = categorical_attn.categorical_attn(\\n          query_space=query_space,\\n          key_space=key_space,\\n          value_space=value_space,\\n          output_space=output_space,\\n          bos_space=bos_space,\\n          one_space=one_space,\\n          attn_fn=attn_basis_fn,\\n          default_output=output_space.null_vector(),\\n          causal=causal,\\n          always_attend_to_bos=False,\\n          use_bos_for_default_output=True,\\n          softmax_coldness=100)\\n    elif isinstance(expr, rasp.SelectorWidth):\\n      sel_expr = expr.selector\\n      queries = graph.nodes[sel_expr.queries.label]\\n      keys = graph.nodes[sel_expr.keys.label]\\n      _check_selector_expression(expr, graph)\\n\\n      bos_space = bases.VectorSpaceWithBasis([bos_dir])\\n      query_space = bases.VectorSpaceWithBasis(queries[nodes.OUTPUT_BASIS])\\n      key_space = bases.VectorSpaceWithBasis(keys[nodes.OUTPUT_BASIS])\\n      output_space = bases.VectorSpaceWithBasis(node[nodes.OUTPUT_BASIS])\\n\\n      # Argument order is different in craft / transformers than RASP selectors\\n      def attn_basis_fn(query: bases.BasisDirection,\\n                        key: bases.BasisDirection) -> bool:\\n        # It\\'s okay to use the local variable sel_expr because this function is\\n        # only used within the same loop iteration to create an attention head.\\n        selector_basis_fn = _transform_fun_to_basis_fun(sel_expr.predicate)  # pylint: disable=cell-var-from-loop\\n        return selector_basis_fn(key, query)\\n\\n      block = selector_width.selector_width(\\n          query_space=query_space,\\n          key_space=key_space,\\n          output_space=output_space,\\n          bos_space=bos_space,\\n          one_space=one_space,\\n          attn_fn=attn_basis_fn,\\n          out_value_set=node[nodes.VALUE_SET],\\n          categorical_output=rasp.is_categorical(expr),\\n          causal=False,\\n          softmax_coldness=100,\\n          mlp_large_number=mlp_exactness,\\n          label=expr.label)\\n    else:\\n      raise NotImplementedError(f\"Expression {expr} cannot be translated to \"\\n                                \"a model component.\")\\n\\n    graph.nodes[node_id][nodes.MODEL_BLOCK] = block\\n', path=PosixPath('tracr/compiler/expr_to_craft_graph.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b80a6a70>, scope_tree=SrcScope(name='tracr/compiler/expr_to_craft_graph.py', kind='file', range=0:12125, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=12125:12125, code=''), len(children)=4)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Converting a RaspExpr to a graph.\"\"\"\\n\\nimport dataclasses\\nimport queue\\nfrom typing import List\\n\\nimport networkx as nx\\nfrom tracr.compiler import nodes\\nfrom tracr.rasp import rasp\\n\\nNode = nodes.Node\\nNodeID = nodes.NodeID\\n\\n\\<EMAIL>\\nclass ExtractRaspGraphOutput:\\n  graph: nx.DiGraph\\n  sink: Node  # the program\\'s output.\\n  sources: List[Node]  # the primitive S-Ops.\\n\\n\\ndef extract_rasp_graph(tip: rasp.SOp) -> ExtractRaspGraphOutput:\\n  \"\"\"Converts a RASP program into a graph representation.\"\"\"\\n  expr_queue = queue.Queue()\\n  graph = nx.DiGraph()\\n  sources: List[NodeID] = []\\n\\n  def ensure_node(expr: rasp.RASPExpr) -> NodeID:\\n    \"\"\"Finds or creates a graph node corresponding to expr; returns its ID.\"\"\"\\n    node_id = expr.label\\n    if node_id not in graph:\\n      graph.add_node(node_id, **{nodes.ID: node_id, nodes.EXPR: expr})\\n\\n    return node_id\\n\\n  # Breadth-first search over the RASP expression graph.\\n\\n  def visit_raspexpr(expr: rasp.RASPExpr):\\n    parent_id = ensure_node(expr)\\n\\n    for child_expr in expr.children:\\n      expr_queue.put(child_expr)\\n      child_id = ensure_node(child_expr)\\n      graph.add_edge(child_id, parent_id)\\n\\n    if not expr.children:\\n      sources.append(graph.nodes[parent_id])\\n\\n  expr_queue.put(tip)\\n  sink = graph.nodes[ensure_node(tip)]\\n  while not expr_queue.empty():\\n    visit_raspexpr(expr_queue.get())\\n\\n  return ExtractRaspGraphOutput(graph=graph, sink=sink, sources=sources)\\n', path=PosixPath('tracr/compiler/rasp_to_graph.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b8053df0>, scope_tree=SrcScope(name='tracr/compiler/rasp_to_graph.py', kind='file', range=0:2128, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=2128:2128, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Convert craft model into transformer with the correct input/output spaces.\"\"\"\\n\\nimport networkx as nx\\nfrom tracr.compiler import assemble\\nfrom tracr.compiler import nodes\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.rasp import rasp\\nfrom tracr.transformer import encoder\\n\\n\\ndef craft_model_to_transformer(\\n    craft_model: transformers.SeriesWithResiduals,\\n    graph: nx.DiGraph,\\n    sink: nodes.Node,\\n    max_seq_len: int,\\n    compiler_bos: str,\\n    compiler_pad: str,\\n    causal: bool = False,\\n) -> assemble.AssembledTransformerModel:\\n  \"\"\"Turn a craft model into a transformer model.\"\"\"\\n\\n  # Add the compiler BOS token.\\n  tokens_value_set = (\\n      graph.nodes[rasp.tokens.label][nodes.VALUE_SET].union(\\n          {compiler_bos, compiler_pad}))\\n  tokens_space = bases.VectorSpaceWithBasis.from_values(rasp.tokens.label,\\n                                                        tokens_value_set)\\n\\n  indices_space = bases.VectorSpaceWithBasis.from_values(\\n      rasp.indices.label, range(max_seq_len))\\n\\n  categorical_output = rasp.is_categorical(sink[nodes.EXPR])\\n  output_space = bases.VectorSpaceWithBasis(sink[nodes.OUTPUT_BASIS])\\n\\n  assembled_model = assemble.assemble_craft_model(\\n      craft_model=craft_model,\\n      tokens_space=tokens_space,\\n      indices_space=indices_space,\\n      output_space=output_space,\\n      categorical_output=categorical_output,\\n      causal=causal,\\n  )\\n\\n  assembled_model.input_encoder = encoder.CategoricalEncoder(\\n      basis=tokens_space.basis,\\n      enforce_bos=compiler_bos is not None,\\n      bos_token=compiler_bos,\\n      pad_token=compiler_pad,\\n      max_seq_len=max_seq_len + 1 if compiler_bos is not None else max_seq_len,\\n  )\\n\\n  if categorical_output:\\n    assembled_model.output_encoder = encoder.CategoricalEncoder(\\n        basis=output_space.basis,\\n        enforce_bos=False,\\n        bos_token=None,\\n        pad_token=None)\\n  else:\\n    assembled_model.output_encoder = encoder.NumericalEncoder()\\n\\n  return assembled_model\\n', path=PosixPath('tracr/compiler/craft_model_to_transformer.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3fedd90>, scope_tree=SrcScope(name='tracr/compiler/craft_model_to_transformer.py', kind='file', range=0:2699, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=2699:2699, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Documents the data stored in nodes after each compiler pass.\"\"\"\\n\\nfrom typing import Any, Dict\\n\\nNode = Dict[str, Any]\\nNodeID = str\\n\\n# RASP -> Graph\\nID = \"ID\"  # unique ID of the node\\nEXPR = \"EXPR\"  # the RASPExpr of the node\\n\\n# Basis inference\\n# Note that only S-Op expressions will have these keys set.\\nVALUE_SET = \"VALUE_SET\"  # possible values taken on by this SOp.\\nOUTPUT_BASIS = \"OUTPUT_BASIS\"  # the corresponding named basis.\\n\\n# RASP Graph -> Craft Graph\\nMODEL_BLOCK = \"MODEL_BLOCK\"  # craft block representing a RASPExpr\\n', path=PosixPath('tracr/compiler/nodes.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3ff9dd0>, scope_tree=SrcScope(name='tracr/compiler/nodes.py', kind='file', range=0:1227, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1227:1227, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for compiler.lib.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.compiler import test_cases\\nfrom tracr.rasp import causal_eval\\nfrom tracr.rasp import rasp\\n\\n\\nclass LibTest(parameterized.TestCase):\\n\\n  @parameterized.named_parameters(*test_cases.TEST_CASES)\\n  def test_program_produces_expected_output(self, program, test_input,\\n                                            expected_output, **kwargs):\\n    del kwargs\\n    self.assertEqual(rasp.evaluate(program, test_input), expected_output)\\n\\n  @parameterized.named_parameters(*test_cases.CAUSAL_TEST_CASES)\\n  def test_causal_program_produces_expected_output(self, program, test_input,\\n                                                   expected_output, **kwargs):\\n    del kwargs\\n    self.assertEqual(causal_eval.evaluate(program, test_input), expected_output)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/lib_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f87890>, scope_tree=SrcScope(name='tracr/compiler/lib_test.py', kind='file', range=0:1599, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1599:1599, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for compiler.rasp_to_graph.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.compiler import nodes\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.rasp import rasp\\n\\n\\nclass ExtractRaspGraphTest(parameterized.TestCase):\\n\\n  def test_primitives_have_no_edges(self):\\n    tokens_graph = rasp_to_graph.extract_rasp_graph(rasp.tokens).graph\\n    self.assertEmpty(tokens_graph.edges)\\n\\n    indices_graph = rasp_to_graph.extract_rasp_graph(rasp.indices).graph\\n    self.assertEmpty(indices_graph.edges)\\n\\n    full_graph = rasp_to_graph.extract_rasp_graph(rasp.Full(1)).graph\\n    self.assertEmpty(full_graph.edges)\\n\\n  def test_one_edge(self):\\n    program = rasp.Map(lambda x: x + 1, rasp.tokens)\\n\\n    graph = rasp_to_graph.extract_rasp_graph(program).graph\\n\\n    self.assertLen(graph.edges, 1)\\n    (u, v), = graph.edges\\n    self.assertEqual(graph.nodes[u][nodes.EXPR], rasp.tokens)\\n    self.assertEqual(graph.nodes[v][nodes.EXPR], program)\\n\\n  def test_aggregate(self):\\n    program = rasp.Aggregate(\\n        rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n        rasp.indices,\\n    )\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    # Expected graph:\\n    #\\n    # indices \\\\ --------\\n    #          \\\\         \\\\\\n    #           select -- program\\n    # tokens  /\\n\\n    self.assertLen(extracted.graph.edges, 4)\\n    self.assertEqual(extracted.sink[nodes.EXPR], program)\\n    for source in extracted.sources:\\n      self.assertIn(\\n          source[nodes.EXPR],\\n          [rasp.tokens, rasp.indices],\\n      )\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/rasp_to_graph_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f8f8d0>, scope_tree=SrcScope(name='tracr/compiler/rasp_to_graph_test.py', kind='file', range=0:2303, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=2303:2303, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"A set of RASP programs and input/output pairs used in integration tests.\"\"\"\\n\\nfrom tracr.compiler import lib\\nfrom tracr.rasp import rasp\\n\\nUNIVERSAL_TEST_CASES = [\\n    dict(\\n        testcase_name=\"frac_prevs_1\",\\n        program=lib.make_frac_prevs(rasp.tokens == \"l\"),\\n        vocab={\"h\", \"e\", \"l\", \"o\"},\\n        test_input=list(\"hello\"),\\n        expected_output=[0.0, 0.0, 1 / 3, 1 / 2, 2 / 5],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"frac_prevs_2\",\\n        program=lib.make_frac_prevs(rasp.tokens == \"(\"),\\n        vocab={\"a\", \"b\", \"c\", \"(\", \")\"},\\n        test_input=list(\"a()b(c))\"),\\n        expected_output=[0.0, 1 / 2, 1 / 3, 1 / 4, 2 / 5, 2 / 6, 2 / 7, 2 / 8],\\n        max_seq_len=10),\\n    dict(\\n        testcase_name=\"frac_prevs_3\",\\n        program=lib.make_frac_prevs(rasp.tokens == \")\"),\\n        vocab={\"a\", \"b\", \"c\", \"(\", \")\"},\\n        test_input=list(\"a()b(c))\"),\\n        expected_output=[0.0, 0.0, 1 / 3, 1 / 4, 1 / 5, 1 / 6, 2 / 7, 3 / 8],\\n        max_seq_len=10,\\n    ),\\n    dict(\\n        testcase_name=\"shift_by_one\",\\n        program=lib.shift_by(1, rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=[None, \"a\", \"b\", \"c\"],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"shift_by_two\",\\n        program=lib.shift_by(2, rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=[None, None, \"a\", \"b\"],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"detect_pattern_a\",\\n        program=lib.detect_pattern(rasp.tokens, \"a\"),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"bacd\"),\\n        expected_output=[False, True, False, False],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"detect_pattern_ab\",\\n        program=lib.detect_pattern(rasp.tokens, \"ab\"),\\n        vocab={\"a\", \"b\"},\\n        test_input=list(\"aaba\"),\\n        expected_output=[None, False, True, False],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"detect_pattern_ab_2\",\\n        program=lib.detect_pattern(rasp.tokens, \"ab\"),\\n        vocab={\"a\", \"b\"},\\n        test_input=list(\"abaa\"),\\n        expected_output=[None, True, False, False],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"detect_pattern_ab_3\",\\n        program=lib.detect_pattern(rasp.tokens, \"ab\"),\\n        vocab={\"a\", \"b\"},\\n        test_input=list(\"aaaa\"),\\n        expected_output=[None, False, False, False],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"detect_pattern_abc\",\\n        program=lib.detect_pattern(rasp.tokens, \"abc\"),\\n        vocab={\"a\", \"b\", \"c\"},\\n        test_input=list(\"abcabc\"),\\n        expected_output=[None, None, True, False, False, True],\\n        max_seq_len=6,\\n    ),\\n]\\n\\nTEST_CASES = UNIVERSAL_TEST_CASES + [\\n    dict(\\n        testcase_name=\"reverse_1\",\\n        program=lib.make_reverse(rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=list(\"dcba\"),\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"reverse_2\",\\n        program=lib.make_reverse(rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abc\"),\\n        expected_output=list(\"cba\"),\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"reverse_3\",\\n        program=lib.make_reverse(rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"ad\"),\\n        expected_output=list(\"da\"),\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"reverse_4\",\\n        program=lib.make_reverse(rasp.tokens),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"c\"],\\n        expected_output=[\"c\"],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"length_categorical_1\",\\n        program=rasp.categorical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abc\"),\\n        expected_output=[3, 3, 3],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"length_categorical_2\",\\n        program=rasp.categorical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"ad\"),\\n        expected_output=[2, 2],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"length_categorical_3\",\\n        program=rasp.categorical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"c\"],\\n        expected_output=[1],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"length_numerical_1\",\\n        program=rasp.numerical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abc\"),\\n        expected_output=[3, 3, 3],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"length_numerical_2\",\\n        program=rasp.numerical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"ad\"),\\n        expected_output=[2, 2],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"length_numerical_3\",\\n        program=rasp.numerical(lib.make_length()),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"c\"],\\n        expected_output=[1],\\n        max_seq_len=3),\\n    dict(\\n        testcase_name=\"pair_balance_1\",\\n        program=lib.make_pair_balance(rasp.tokens, \"(\", \")\"),\\n        vocab={\"a\", \"b\", \"c\", \"(\", \")\"},\\n        test_input=list(\"a()b(c))\"),\\n        expected_output=[0.0, 1 / 2, 0.0, 0.0, 1 / 5, 1 / 6, 0.0, -1 / 8],\\n        max_seq_len=10),\\n    dict(\\n        testcase_name=\"shuffle_dyck2_1\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\"},\\n        test_input=list(\"({)}\"),\\n        expected_output=[1, 1, 1, 1],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"shuffle_dyck2_2\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\"},\\n        test_input=list(\"(){)}\"),\\n        expected_output=[0, 0, 0, 0, 0],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"shuffle_dyck2_3\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\"},\\n        test_input=list(\"{}(\"),\\n        expected_output=[0, 0, 0],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"shuffle_dyck3_1\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\", \"[]\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\", \"[\", \"]\"},\\n        test_input=list(\"({)[}]\"),\\n        expected_output=[1, 1, 1, 1, 1, 1],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"shuffle_dyck3_2\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\", \"[]\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\", \"[\", \"]\"},\\n        test_input=list(\"(){)}\"),\\n        expected_output=[0, 0, 0, 0, 0],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"shuffle_dyck3_3\",\\n        program=lib.make_shuffle_dyck(pairs=[\"()\", \"{}\", \"[]\"]),\\n        vocab={\"(\", \")\", \"{\", \"}\", \"[\", \"]\"},\\n        test_input=list(\"{}[(]\"),\\n        expected_output=[0, 0, 0, 0, 0],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"hist\",\\n        program=lib.make_hist(),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abac\"),\\n        expected_output=[2, 1, 2, 1],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"sort_unique_1\",\\n        program=lib.make_sort_unique(vals=rasp.tokens, keys=rasp.tokens),\\n        vocab={1, 2, 3, 4},\\n        test_input=[2, 4, 3, 1],\\n        expected_output=[1, 2, 3, 4],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"sort_unique_2\",\\n        program=lib.make_sort_unique(vals=rasp.tokens, keys=1 - rasp.indices),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=[\"d\", \"c\", \"b\", \"a\"],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"sort_1\",\\n        program=lib.make_sort(\\n            vals=rasp.tokens, keys=rasp.tokens, max_seq_len=5, min_key=1),\\n        vocab={1, 2, 3, 4},\\n        test_input=[2, 4, 3, 1],\\n        expected_output=[1, 2, 3, 4],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"sort_2\",\\n        program=lib.make_sort(\\n            vals=rasp.tokens, keys=1 - rasp.indices, max_seq_len=5, min_key=1),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=[\"d\", \"c\", \"b\", \"a\"],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"sort_3\",\\n        program=lib.make_sort(\\n            vals=rasp.tokens, keys=rasp.tokens, max_seq_len=5, min_key=1),\\n        vocab={1, 2, 3, 4},\\n        test_input=[2, 4, 1, 2],\\n        expected_output=[1, 2, 2, 4],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"sort_freq\",\\n        program=lib.make_sort_freq(max_seq_len=5),\\n        vocab={1, 2, 3, 4},\\n        test_input=[2, 4, 2, 1],\\n        expected_output=[2, 2, 4, 1],\\n        max_seq_len=5),\\n    dict(\\n        testcase_name=\"make_count_less_freq_categorical_1\",\\n        program=lib.make_count_less_freq(n=2),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"a\", \"a\", \"a\", \"b\", \"b\", \"c\"],\\n        expected_output=[3, 3, 3, 3, 3, 3],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"make_count_less_freq_categorical_2\",\\n        program=lib.make_count_less_freq(n=2),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"a\", \"a\", \"c\", \"b\", \"b\", \"c\"],\\n        expected_output=[6, 6, 6, 6, 6, 6],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"make_count_less_freq_numerical_1\",\\n        program=rasp.numerical(lib.make_count_less_freq(n=2)),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"a\", \"a\", \"a\", \"b\", \"b\", \"c\"],\\n        expected_output=[3, 3, 3, 3, 3, 3],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"make_count_less_freq_numerical_2\",\\n        program=rasp.numerical(lib.make_count_less_freq(n=2)),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=[\"a\", \"a\", \"c\", \"b\", \"b\", \"c\"],\\n        expected_output=[6, 6, 6, 6, 6, 6],\\n        max_seq_len=6),\\n    dict(\\n        testcase_name=\"make_count_1\",\\n        program=lib.make_count(rasp.tokens, \"a\"),\\n        vocab={\"a\", \"b\", \"c\"},\\n        test_input=[\"a\", \"a\", \"a\", \"b\", \"b\", \"c\"],\\n        expected_output=[3, 3, 3, 3, 3, 3],\\n        max_seq_len=8,\\n    ),\\n    dict(\\n        testcase_name=\"make_count_2\",\\n        program=lib.make_count(rasp.tokens, \"a\"),\\n        vocab={\"a\", \"b\", \"c\"},\\n        test_input=[\"c\", \"a\", \"b\", \"c\"],\\n        expected_output=[1, 1, 1, 1],\\n        max_seq_len=8,\\n    ),\\n    dict(\\n        testcase_name=\"make_count_3\",\\n        program=lib.make_count(rasp.tokens, \"a\"),\\n        vocab={\"a\", \"b\", \"c\"},\\n        test_input=[\"b\", \"b\", \"c\"],\\n        expected_output=[0, 0, 0],\\n        max_seq_len=8,\\n    ),\\n    dict(\\n        testcase_name=\"make_nary_sequencemap_1\",\\n        program=lib.make_nary_sequencemap(\\n            lambda x, y, z: x + y - z, rasp.tokens, rasp.tokens, rasp.indices),\\n        vocab={1, 2, 3},\\n        test_input=[1, 2, 3],\\n        expected_output=[2, 3, 4],\\n        max_seq_len=5,\\n    ),\\n    dict(\\n        testcase_name=\"make_nary_sequencemap_2\",\\n        program=lib.make_nary_sequencemap(\\n            lambda x, y, z: x * y / z, rasp.indices, rasp.indices, rasp.tokens),\\n        vocab={1, 2, 3},\\n        test_input=[1, 2, 3],\\n        expected_output=[0, 1 / 2, 4 / 3],\\n        max_seq_len=3,\\n    )\\n]\\n\\n# make_nary_sequencemap(f, *sops)\\n\\nCAUSAL_TEST_CASES = UNIVERSAL_TEST_CASES + [\\n    dict(\\n        testcase_name=\"selector_width\",\\n        program=rasp.SelectorWidth(\\n            rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.TRUE)),\\n        vocab={\"a\", \"b\", \"c\", \"d\"},\\n        test_input=list(\"abcd\"),\\n        expected_output=[1, 2, 3, 4],\\n        max_seq_len=5),\\n]\\n', path=PosixPath('tracr/compiler/test_cases.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f96a10>, scope_tree=SrcScope(name='tracr/compiler/test_cases.py', kind='file', range=0:12145, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=12145:12145, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for compiler.expr_to_craft_graph.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.compiler import basis_inference\\nfrom tracr.compiler import expr_to_craft_graph\\nfrom tracr.compiler import lib\\nfrom tracr.compiler import nodes\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.rasp import rasp\\n\\n\\nclass ExprToCraftGraphTest(parameterized.TestCase):\\n\\n  def _check_block_types_are_correct(self, graph):\\n    for _, node in graph.nodes.items():\\n      expr = node[nodes.EXPR]\\n      if isinstance(expr, rasp.SOp):\\n        block = node[nodes.MODEL_BLOCK]\\n        if isinstance(expr, (rasp.Map, rasp.SequenceMap)):\\n          self.assertIsInstance(block, transformers.MLP)\\n        elif isinstance(expr, rasp.Aggregate):\\n          self.assertIsInstance(block, transformers.AttentionHead)\\n\\n  def _get_input_space_from_node(self, node):\\n    block = node[nodes.MODEL_BLOCK]\\n    if isinstance(block, transformers.MLP):\\n      return block.fst.input_space\\n    elif isinstance(block, transformers.AttentionHead):\\n      return bases.join_vector_spaces(block.w_qk.left_space,\\n                                      block.w_qk.right_space,\\n                                      block.w_ov.input_space)\\n    else:\\n      return None\\n\\n  def _check_spaces_are_consistent(self, graph):\\n    \"\"\"Check that for each edge the output is a subspace of the input.\"\"\"\\n    for u, v in graph.edges:\\n      u_node, v_node = graph.nodes[u], graph.nodes[v]\\n      if isinstance(u_node[nodes.EXPR], rasp.SOp) and isinstance(\\n          v_node[nodes.EXPR], rasp.SOp):\\n        u_out_basis = u_node[nodes.OUTPUT_BASIS]\\n        u_out_space = bases.VectorSpaceWithBasis(u_out_basis)\\n        v_in_space = self._get_input_space_from_node(v_node)\\n        self.assertTrue(u_out_space.issubspace(v_in_space))\\n\\n  @parameterized.named_parameters(\\n      dict(\\n          testcase_name=\"single_map\",\\n          program=rasp.Map(lambda x: x + 1, rasp.tokens)),\\n      dict(\\n          testcase_name=\"single_sequence_map\",\\n          program=rasp.SequenceMap(lambda x, y: x + y, rasp.tokens,\\n                                   rasp.indices)),\\n      dict(\\n          testcase_name=\"single_select_aggregate\",\\n          program=rasp.Aggregate(\\n              rasp.Select(rasp.tokens, rasp.indices, rasp.Comparison.EQ),\\n              rasp.tokens,\\n          )),\\n      dict(testcase_name=\"reverse\", program=lib.make_reverse(rasp.tokens)),\\n      dict(testcase_name=\"length\", program=lib.make_length()))\\n  def test_compiling_rasp_programs(self, program):\\n    vocab = {0, 1, 2}\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=3,\\n    )\\n    expr_to_craft_graph.add_craft_components_to_rasp_graph(extracted.graph)\\n    self._check_block_types_are_correct(extracted.graph)\\n    self._check_spaces_are_consistent(extracted.graph)\\n\\n  def test_add_craft_components_raises_value_error_if_called_before_basis_inference(\\n      self):\\n    program = rasp.categorical(rasp.Map(lambda x: x + 1, rasp.tokens))\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    with self.assertRaisesRegex(\\n        ValueError,\\n        r\"^.*Craft components can only be added after basis inference.*$\"):\\n      expr_to_craft_graph.add_craft_components_to_rasp_graph(extracted.graph)\\n\\n  def test_add_craft_components_raises_value_error_if_called_twice(self):\\n    vocab = {0, 1, 2}\\n    program = rasp.categorical(rasp.Map(lambda x: x + 1, rasp.tokens))\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=1,\\n    )\\n\\n    expr_to_craft_graph.add_craft_components_to_rasp_graph(extracted.graph)\\n    with self.assertRaisesRegex(\\n        ValueError, r\"^.*Input graph cannot have model blocks set already.*$\"):\\n      expr_to_craft_graph.add_craft_components_to_rasp_graph(extracted.graph)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/expr_to_craft_graph_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f935f0>, scope_tree=SrcScope(name='tracr/compiler/expr_to_craft_graph_test.py', kind='file', range=0:4789, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=4789:4789, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Inferring the vector spaces taken on by certain operations.\"\"\"\\n\\nimport dataclasses\\nimport itertools\\nfrom typing import Set\\n\\nimport networkx as nx\\nfrom tracr.compiler import nodes\\nfrom tracr.craft import bases\\nfrom tracr.rasp import rasp\\nfrom tracr.utils import errors\\n\\nNode = nodes.Node\\n\\n\\<EMAIL>\\nclass InferBasesOutput:\\n  graph: nx.DiGraph\\n\\n\\ndef infer_bases(\\n    graph: nx.DiGraph,\\n    sink: Node,\\n    vocab: Set[rasp.Value],\\n    max_seq_len: int,\\n) -> None:\\n  \"\"\"Infers in-place the possible output values and vector bases of the SOps.\"\"\"\\n\\n  def compute_value_set(sop: rasp.SOp) -> Set[rasp.Value]:\\n    \"\"\"Computes value set using already-computed predecessor value sets.\"\"\"\\n    if sop is rasp.tokens:\\n      return vocab\\n    elif sop is rasp.indices:\\n      return set(range(max_seq_len))\\n    elif isinstance(sop, rasp.SelectorWidth):\\n      return set(range(0, max_seq_len + 1))\\n    elif isinstance(sop, rasp.Full):\\n      return {sop.fill}\\n    elif isinstance(sop, rasp.Map):\\n      inner_value_set = graph.nodes[sop.inner.label][nodes.VALUE_SET]\\n      out = set()\\n      for x in inner_value_set:\\n        res = errors.ignoring_arithmetic_errors(sop.f)(x)\\n        if res is not None:\\n          out.add(res)\\n      return out\\n    elif isinstance(sop, rasp.SequenceMap):\\n      f_ignore_error = errors.ignoring_arithmetic_errors(sop.f)\\n      fst_value_set = graph.nodes[sop.fst.label][nodes.VALUE_SET]\\n      snd_value_set = graph.nodes[sop.snd.label][nodes.VALUE_SET]\\n      out = set()\\n      for l, r in itertools.product(fst_value_set, snd_value_set):\\n        res = f_ignore_error(l, r)\\n        if res is not None:\\n          out.add(res)\\n      return out\\n    elif isinstance(sop, rasp.Aggregate):\\n      if rasp.is_categorical(sop):\\n        # Simply pass on the value set of the underlying S-Op.\\n        return graph.nodes[sop.sop.label][nodes.VALUE_SET]\\n      elif rasp.is_numerical(sop):\\n        # TODO(b/255936408): This doesn\\'t work if we average arbitrary values.\\n        # But most examples only average binary variables.\\n        sop_value_set = graph.nodes[sop.sop.label][nodes.VALUE_SET]\\n        if {int(x) for x in sop_value_set} != {0, 1}:\\n          raise NotImplementedError(\\n              \"Attention patterns can currently only \"\\n              \"average binary variables. Not:\", sop_value_set)\\n\\n        value_set = set()\\n        for value in sop_value_set:\\n          for length in range(1, max_seq_len + 1):\\n            value_set.add(value / length)\\n        return value_set\\n    raise ValueError(f\"Unsupported S-Op: {sop}\")\\n\\n  for node_id in nx.dfs_postorder_nodes(graph.reverse(), sink[nodes.ID]):\\n    expr = graph.nodes[node_id][nodes.EXPR]\\n\\n    if not isinstance(expr, rasp.SOp):\\n      # Only S-Ops have output vector spaces.\\n      continue\\n\\n    value_set = compute_value_set(expr)\\n    graph.nodes[node_id][nodes.VALUE_SET] = value_set\\n\\n    if rasp.is_categorical(expr):\\n      out_space = bases.VectorSpaceWithBasis.from_values(expr.label, value_set)\\n    elif rasp.is_numerical(expr):\\n      out_space = bases.VectorSpaceWithBasis.from_names([expr.label])\\n    else:\\n      raise ValueError(f\"Unsupported S-Op type: {expr.type}\")\\n    graph.nodes[node_id][nodes.OUTPUT_BASIS] = out_space.basis\\n', path=PosixPath('tracr/compiler/basis_inference.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3fb23d0>, scope_tree=SrcScope(name='tracr/compiler/basis_inference.py', kind='file', range=0:3913, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=3913:3913, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for transformer.assemble.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\nfrom tracr.compiler import assemble\\nfrom tracr.craft import bases\\n\\n\\nclass AssembleTest(parameterized.TestCase):\\n\\n  def test_token_embedding_produces_correct_embedding(self):\\n    # Token embeddings should be one-hot embeddings of the input integers\\n    # into the token subspace of residual_space\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"0inp\", range(2))\\n    indices_space = bases.VectorSpaceWithBasis.from_values(\"1ind\", range(3))\\n    output_space = bases.VectorSpaceWithBasis.from_values(\"2out\", range(2))\\n    residual_space = bases.join_vector_spaces(input_space, indices_space,\\n                                              output_space)\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def token_pos_embed(tokens):\\n      embed_modules = assemble._make_embedding_modules(\\n          residual_space=residual_space,\\n          tokens_space=input_space,\\n          indices_space=indices_space,\\n          output_space=output_space)\\n      return embed_modules.token_embed(tokens)\\n\\n    tokens = jnp.array([0, 0, 1])\\n    expected_token_embeddings = jnp.array([[1, 0, 0, 0, 0, 0, 0],\\n                                           [1, 0, 0, 0, 0, 0, 0],\\n                                           [0, 1, 0, 0, 0, 0, 0]])\\n\\n    params = token_pos_embed.init(jax.random.PRNGKey(0), tokens)\\n    embeddings = token_pos_embed.apply(params, tokens)\\n    np.testing.assert_allclose(embeddings, expected_token_embeddings)\\n\\n  def test_position_embedding_produces_correct_embedding(self):\\n    # Position embeddings should be one-hot embeddings of the input integers\\n    # (representing indices) into the indices subspace of residual_space\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"0inp\", range(2))\\n    indices_space = bases.VectorSpaceWithBasis.from_values(\"1ind\", range(3))\\n    output_space = bases.VectorSpaceWithBasis.from_values(\"2out\", range(2))\\n    residual_space = bases.join_vector_spaces(input_space, indices_space,\\n                                              output_space)\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def token_pos_embed(tokens):\\n      embed_modules = assemble._make_embedding_modules(\\n          residual_space=residual_space,\\n          tokens_space=input_space,\\n          indices_space=indices_space,\\n          output_space=output_space)\\n      return embed_modules.pos_embed(jnp.indices(tokens.shape)[-1])\\n\\n    tokens = jnp.array([3, 0, 0, 1])\\n    expected_pos_embeddings = jnp.array([[0, 0, 0, 0, 0, 0, 0],\\n                                         [0, 0, 1, 0, 0, 0, 0],\\n                                         [0, 0, 0, 1, 0, 0, 0],\\n                                         [0, 0, 0, 0, 1, 0, 0]])\\n\\n    params = token_pos_embed.init(jax.random.PRNGKey(0), tokens)\\n    embeddings = token_pos_embed.apply(params, tokens)\\n    np.testing.assert_allclose(embeddings, expected_pos_embeddings)\\n\\n  def test_unembedding(self):\\n    # Prepend numbers to preserve basis order [input, index, output]\\n    input_space = bases.VectorSpaceWithBasis.from_values(\"0inp\", range(2))\\n    indices_space = bases.VectorSpaceWithBasis.from_values(\"1ind\", range(3))\\n    output_space = bases.VectorSpaceWithBasis.from_values(\"2out\", range(2))\\n    residual_space = bases.join_vector_spaces(input_space, indices_space,\\n                                              output_space)\\n\\n    @hk.without_apply_rng\\n    @hk.transform\\n    def unembed(embeddings):\\n      embed_modules = assemble._make_embedding_modules(\\n          residual_space=residual_space,\\n          tokens_space=input_space,\\n          indices_space=indices_space,\\n          output_space=output_space)\\n      return embed_modules.unembed(embeddings, use_unembed_argmax=True)\\n\\n    embeddings = jnp.array([\\n        # pylint: disable=g-no-space-after-comment\\n        #inp| indices| out | < spaces\\n        #0  1  0  1  2  0  1  < values in spaces\\n        [0, 0, 0, 0, 0, 0, 1],\\n        [0, 0, 0, 0, 0, 1, 0],\\n        [0, 0, 0, 0, 0, 0, 1]\\n    ])\\n    expected_tokens = jnp.array([1, 0, 1])\\n\\n    params = unembed.init(jax.random.PRNGKey(0), embeddings)\\n    tokens = unembed.apply(params, embeddings)\\n    np.testing.assert_allclose(tokens, expected_tokens)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/assemble_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f5b630>, scope_tree=SrcScope(name='tracr/compiler/assemble_test.py', kind='file', range=0:5053, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=5053:5053, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Integration tests for the RASP -> craft stages of the compiler.\"\"\"\\n\\nimport unittest\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport numpy as np\\nfrom tracr.compiler import basis_inference\\nfrom tracr.compiler import craft_graph_to_model\\nfrom tracr.compiler import expr_to_craft_graph\\nfrom tracr.compiler import nodes\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.compiler import test_cases\\nfrom tracr.craft import bases\\nfrom tracr.craft import tests_common\\nfrom tracr.rasp import rasp\\n\\n_BOS_DIRECTION = \"rasp_to_transformer_integration_test_BOS\"\\n_ONE_DIRECTION = \"rasp_to_craft_integration_test_ONE\"\\n\\n\\ndef _make_input_space(vocab, max_seq_len):\\n  tokens_space = bases.VectorSpaceWithBasis.from_values(\"tokens\", vocab)\\n  indices_space = bases.VectorSpaceWithBasis.from_values(\\n      \"indices\", range(max_seq_len))\\n  one_space = bases.VectorSpaceWithBasis.from_names([_ONE_DIRECTION])\\n  bos_space = bases.VectorSpaceWithBasis.from_names([_BOS_DIRECTION])\\n  input_space = bases.join_vector_spaces(tokens_space, indices_space, one_space,\\n                                         bos_space)\\n\\n  return input_space\\n\\n\\ndef _embed_input(input_seq, input_space):\\n  bos_vec = input_space.vector_from_basis_direction(\\n      bases.BasisDirection(_BOS_DIRECTION))\\n  one_vec = input_space.vector_from_basis_direction(\\n      bases.BasisDirection(_ONE_DIRECTION))\\n  embedded_input = [bos_vec + one_vec]\\n  for i, val in enumerate(input_seq):\\n    i_vec = input_space.vector_from_basis_direction(\\n        bases.BasisDirection(\"indices\", i))\\n    val_vec = input_space.vector_from_basis_direction(\\n        bases.BasisDirection(\"tokens\", val))\\n    embedded_input.append(i_vec + val_vec + one_vec)\\n  return bases.VectorInBasis.stack(embedded_input)\\n\\n\\ndef _embed_output(output_seq, output_space, categorical_output):\\n  embedded_output = []\\n  output_label = output_space.basis[0].name\\n  for x in output_seq:\\n    if x is None:\\n      out_vec = output_space.null_vector()\\n    elif categorical_output:\\n      out_vec = output_space.vector_from_basis_direction(\\n          bases.BasisDirection(output_label, x))\\n    else:\\n      out_vec = x * output_space.vector_from_basis_direction(\\n          output_space.basis[0])\\n    embedded_output.append(out_vec)\\n  return bases.VectorInBasis.stack(embedded_output)\\n\\n\\nclass CompilerIntegrationTest(tests_common.VectorFnTestCase):\\n\\n  @parameterized.named_parameters(\\n      dict(\\n          testcase_name=\"map\",\\n          program=rasp.categorical(rasp.Map(lambda x: x + 1, rasp.tokens))),\\n      dict(\\n          testcase_name=\"sequence_map\",\\n          program=rasp.categorical(\\n              rasp.SequenceMap(lambda x, y: x + y, rasp.tokens, rasp.indices))),\\n      dict(\\n          testcase_name=\"sequence_map_with_same_input\",\\n          program=rasp.categorical(\\n              rasp.SequenceMap(lambda x, y: x + y, rasp.tokens, rasp.tokens))),\\n      dict(\\n          testcase_name=\"select_aggregate\",\\n          program=rasp.Aggregate(\\n              rasp.Select(rasp.tokens, rasp.tokens, rasp.Comparison.EQ),\\n              rasp.Map(lambda x: 1, rasp.tokens))))\\n  def test_rasp_program_and_craft_model_produce_same_output(self, program):\\n    vocab = {0, 1, 2}\\n    max_seq_len = 3\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=max_seq_len,\\n    )\\n    expr_to_craft_graph.add_craft_components_to_rasp_graph(\\n        extracted.graph,\\n        bos_dir=bases.BasisDirection(_BOS_DIRECTION),\\n        one_dir=bases.BasisDirection(_ONE_DIRECTION),\\n    )\\n    model = craft_graph_to_model.craft_graph_to_model(extracted.graph,\\n                                                      extracted.sources)\\n    input_space = _make_input_space(vocab, max_seq_len)\\n    output_space = bases.VectorSpaceWithBasis(\\n        extracted.sink[nodes.OUTPUT_BASIS])\\n\\n    for val in vocab:\\n      test_input = _embed_input([val], input_space)\\n      rasp_output = program([val])\\n      expected_output = _embed_output(\\n          output_seq=rasp_output,\\n          output_space=output_space,\\n          categorical_output=True)\\n      test_output = model.apply(test_input).project(output_space)\\n      self.assertVectorAllClose(\\n          tests_common.strip_bos_token(test_output), expected_output)\\n\\n  @parameterized.named_parameters(*test_cases.TEST_CASES)\\n  def test_compiled_models_produce_expected_output(self, program, vocab,\\n                                                   test_input, expected_output,\\n                                                   max_seq_len, **kwargs):\\n    del kwargs\\n    categorical_output = rasp.is_categorical(program)\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=max_seq_len,\\n    )\\n    expr_to_craft_graph.add_craft_components_to_rasp_graph(\\n        extracted.graph,\\n        bos_dir=bases.BasisDirection(_BOS_DIRECTION),\\n        one_dir=bases.BasisDirection(_ONE_DIRECTION),\\n    )\\n    model = craft_graph_to_model.craft_graph_to_model(extracted.graph,\\n                                                      extracted.sources)\\n    input_space = _make_input_space(vocab, max_seq_len)\\n    output_space = bases.VectorSpaceWithBasis(\\n        extracted.sink[nodes.OUTPUT_BASIS])\\n    if not categorical_output:\\n      self.assertLen(output_space.basis, 1)\\n\\n    test_input_vector = _embed_input(test_input, input_space)\\n    expected_output_vector = _embed_output(\\n        output_seq=expected_output,\\n        output_space=output_space,\\n        categorical_output=categorical_output)\\n    test_output = model.apply(test_input_vector).project(output_space)\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(test_output), expected_output_vector)\\n\\n  @unittest.expectedFailure\\n  def test_setting_default_values_can_lead_to_wrong_outputs_in_compiled_model(\\n      self, program):\\n    # This is an example program in which setting a default value for aggregate\\n    # writes a value to the bos token position, which interfers with a later\\n    # aggregate operation causing the compiled model to have the wrong output.\\n\\n    vocab = {\"a\", \"b\"}\\n    test_input = [\"a\"]\\n    max_seq_len = 2\\n\\n    # RASP: [False, True]\\n    # compiled: [False, False, True]\\n    not_a = rasp.Map(lambda x: x != \"a\", rasp.tokens)\\n\\n    # RASP:\\n    # [[True, False],\\n    #  [False, False]]\\n    # compiled:\\n    # [[False,True, False],\\n    #  [True, False, False]]\\n    sel1 = rasp.Select(rasp.tokens, rasp.tokens,\\n                       lambda k, q: k == \"a\" and q == \"a\")\\n\\n    # RASP: [False, True]\\n    # compiled: [True, False, True]\\n    agg1 = rasp.Aggregate(sel1, not_a, default=True)\\n\\n    # RASP:\\n    # [[False, True]\\n    #  [True, True]]\\n    # compiled:\\n    # [[True, False, False]\\n    #  [True, False, False]]\\n    # because pre-softmax we get\\n    # [[1.5, 1, 1]\\n    #  [1.5, 1, 1]]\\n    # instead of\\n    # [[0.5, 1, 1]\\n    #  [0.5, 1, 1]]\\n    # Because agg1 = True is stored on the BOS token position\\n    sel2 = rasp.Select(agg1, agg1, lambda k, q: k or q)\\n\\n    # RASP: [1, 0.5]\\n    # compiled\\n    # [1, 1, 1]\\n    program = rasp.numerical(\\n        rasp.Aggregate(sel2, rasp.numerical(not_a), default=1))\\n    expected_output = [1, 0.5]\\n\\n    # RASP program gives the correct output\\n    program_output = program(test_input)\\n    np.testing.assert_allclose(program_output, expected_output)\\n\\n    extracted = rasp_to_graph.extract_rasp_graph(program)\\n    basis_inference.infer_bases(\\n        extracted.graph,\\n        extracted.sink,\\n        vocab,\\n        max_seq_len=max_seq_len,\\n    )\\n    expr_to_craft_graph.add_craft_components_to_rasp_graph(\\n        extracted.graph,\\n        bos_dir=bases.BasisDirection(_BOS_DIRECTION),\\n        one_dir=bases.BasisDirection(_ONE_DIRECTION),\\n    )\\n    model = craft_graph_to_model.craft_graph_to_model(extracted.graph,\\n                                                      extracted.sources)\\n\\n    input_space = _make_input_space(vocab, max_seq_len)\\n    output_space = bases.VectorSpaceWithBasis(\\n        extracted.sink[nodes.OUTPUT_BASIS])\\n\\n    test_input_vector = _embed_input(test_input, input_space)\\n    expected_output_vector = _embed_output(\\n        output_seq=expected_output,\\n        output_space=output_space,\\n        categorical_output=True)\\n    compiled_model_output = model.apply(test_input_vector).project(output_space)\\n\\n    # Compiled craft model gives correct output\\n    self.assertVectorAllClose(\\n        tests_common.strip_bos_token(compiled_model_output),\\n        expected_output_vector)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/rasp_to_craft_integration_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3f34e30>, scope_tree=SrcScope(name='tracr/compiler/rasp_to_craft_integration_test.py', kind='file', range=0:9346, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=9346:9346, code=''), len(children)=6)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for compiler.craft_graph_to_model.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nimport networkx as nx\\nfrom tracr.compiler import craft_graph_to_model\\nfrom tracr.compiler import nodes\\nfrom tracr.compiler import rasp_to_graph\\nfrom tracr.craft import bases\\nfrom tracr.craft.chamber import categorical_attn\\nfrom tracr.craft.chamber import categorical_mlp\\nfrom tracr.rasp import rasp\\n\\n\\nclass CraftAllocateModulesToLayersTest(parameterized.TestCase):\\n\\n  def _get_dummy_block(self, block_type):\\n    if block_type == \"ATTN\":\\n      return categorical_attn.categorical_attn(\\n          query_space=bases.VectorSpaceWithBasis.from_names([\"query\"]),\\n          key_space=bases.VectorSpaceWithBasis.from_names([\"bos\", \"key\"]),\\n          value_space=bases.VectorSpaceWithBasis.from_names([\"bos\", \"value\"]),\\n          output_space=bases.VectorSpaceWithBasis.from_names([\"output\"]),\\n          bos_space=bases.VectorSpaceWithBasis.from_names([\"bos\"]),\\n          one_space=bases.VectorSpaceWithBasis.from_names([\"one\"]),\\n          attn_fn=lambda x, y: True,\\n      )\\n    elif block_type == \"MLP\":\\n      return categorical_mlp.map_categorical_mlp(\\n          input_space=bases.VectorSpaceWithBasis.from_names([\"input\"]),\\n          output_space=bases.VectorSpaceWithBasis.from_names([\"output\"]),\\n          operation=lambda x: x,\\n      )\\n    else:\\n      return None\\n\\n  def test_get_longest_path_length_to_node_returns_expected_result(self):\\n    \"\"\"Creates a graph and checks the longest path for each node.\"\"\"\\n\\n    # Node IDs:\\n    # 0 -- 1 -- 2 -- 3 ------------  4\\n    #               /              /\\n    # 5 -- 6 ---------- 7 <USER> <GROUP> -- 9\\n    #\\n    # 10\\n    # Expected return values:\\n    # 0 -- 1 -- 2 -- 3 ------------  5\\n    #               /              /\\n    # 0 -- 1 ---------- 2 <USER> <GROUP> -- 4\\n    #\\n    # -1\\n\\n    graph = nx.DiGraph()\\n    node_ids = list(range(11))\\n    expected_results = [0, 1, 2, 3, 5, 0, 1, 2, 3, 4, -1]\\n    for node_id, res in zip(node_ids, expected_results):\\n      graph.add_node(\\n          node_id, **{\\n              nodes.ID: node_id,\\n              nodes.EXPR: rasp.ConstantSOp(1),\\n              \"expected_result\": res\\n          })\\n    graph.add_edge(0, 1)\\n    graph.add_edge(1, 2)\\n    graph.add_edge(2, 3)\\n    graph.add_edge(3, 4)\\n    graph.add_edge(5, 6)\\n    graph.add_edge(6, 7)\\n    graph.add_edge(7, 8)\\n    graph.add_edge(8, 9)\\n    graph.add_edge(6, 3)\\n    graph.add_edge(9, 4)\\n    sources = [graph.nodes[0], graph.nodes[5]]\\n\\n    for node_id, node in graph.nodes.items():\\n      result = craft_graph_to_model._get_longest_path_length_to_node(\\n          graph, sources, node)\\n      self.assertEqual(result, node[\"expected_result\"])\\n\\n  def test_allocate_modules_to_layers_returns_expected_result(self):\\n    \"\"\"Creates a graph and checks if the correct layer assignment is returned.\"\"\"\\n\\n    # Computation Graph:\\n    # INPUT -- ATTN -- MLP -- ATTN ------ MLP -- OUTPUT\\n    #           /           /          /\\n    # INPUT -- MLP --- MLP          ATTN\\n    #                      \\\\      /\\n    #                        ATTN\\n    # Node IDs:\\n    # 0 -- 1 -- 2 -- 3 -- 4 -- 5\\n    #         /     /     /\\n    # 6 -- 7 ---- 8      9\\n    #               \\\\   /\\n    #                10\\n    # Expected layer allocation:\\n    # -1 -- 0 -- 3 -- 4 -- 7 -- -1\\n    #         /     /     /\\n    # -1 -- 1 --- 3      6\\n    #               \\\\   /\\n    #                 4\\n\\n    graph = nx.DiGraph()\\n    node_ids = list(range(11))\\n    types = [\\n        \"INPUT\", \"ATTN\", \"MLP\", \"ATTN\", \"MLP\", \"OUTPUT\", \"INPUT\", \"MLP\", \"MLP\",\\n        \"ATTN\", \"ATTN\"\\n    ]\\n    expected_results = [-1, 0, 3, 4, 7, -1, -1, 1, 3, 6, 4]\\n    for node_id, node_type, res in zip(node_ids, types, expected_results):\\n      graph.add_node(\\n          node_id, **{\\n              nodes.ID: node_id,\\n              nodes.EXPR: rasp.ConstantSOp(1),\\n              nodes.MODEL_BLOCK: self._get_dummy_block(node_type),\\n              \"expected_result\": res\\n          })\\n\\n    graph.add_edge(0, 1)\\n    graph.add_edge(1, 2)\\n    graph.add_edge(2, 3)\\n    graph.add_edge(3, 4)\\n    graph.add_edge(4, 5)\\n    graph.add_edge(6, 7)\\n    graph.add_edge(7, 2)\\n    graph.add_edge(7, 8)\\n    graph.add_edge(8, 3)\\n    graph.add_edge(8, 10)\\n    graph.add_edge(9, 4)\\n    graph.add_edge(10, 9)\\n\\n    craft_graph = rasp_to_graph.ExtractRaspGraphOutput(\\n        graph=graph,\\n        sink=graph.nodes[10],\\n        sources=[graph.nodes[0], graph.nodes[6]])\\n\\n    layer_allocation = craft_graph_to_model._allocate_modules_to_layers(\\n        craft_graph.graph, craft_graph.sources)\\n    for node_id, node in graph.nodes.items():\\n      self.assertEqual(layer_allocation[node_id], node[\"expected_result\"])\\n\\n  def test_allocate_modules_to_layers_returns_expected_result_for_chain(self):\\n    \"\"\"Tests a chain of alternating attention layers and MLPs.\"\"\"\\n\\n    # Computation Graph:\\n    # INPUT -- ATTN -- MLP -- ATTN -- MLP -- OUTPUT\\n    # Node IDs:\\n    # 0 -- 1 -- 2 -- 3 -- 4 -- 5\\n    # Expected layer allocation:\\n    # -1 -- 0 -- 1 -- 2 -- 3 -- -1\\n\\n    graph = nx.DiGraph()\\n    node_ids = list(range(11))\\n    types = [\"INPUT\", \"ATTN\", \"MLP\", \"ATTN\", \"MLP\", \"OUTPUT\"]\\n    expected_results = [-1, 0, 1, 2, 3, -1]\\n    for node_id, node_type, res in zip(node_ids, types, expected_results):\\n      graph.add_node(\\n          node_id, **{\\n              nodes.ID: node_id,\\n              nodes.EXPR: rasp.ConstantSOp(1),\\n              nodes.MODEL_BLOCK: self._get_dummy_block(node_type),\\n              \"expected_result\": res\\n          })\\n\\n    graph.add_edge(0, 1)\\n    graph.add_edge(1, 2)\\n    graph.add_edge(2, 3)\\n    graph.add_edge(3, 4)\\n    graph.add_edge(4, 5)\\n\\n    craft_graph = rasp_to_graph.ExtractRaspGraphOutput(\\n        graph=graph, sink=graph.nodes[5], sources=[graph.nodes[0]])\\n\\n    layer_allocation = craft_graph_to_model._allocate_modules_to_layers(\\n        craft_graph.graph, craft_graph.sources)\\n    for node_id, node in graph.nodes.items():\\n      self.assertEqual(layer_allocation[node_id], node[\"expected_result\"])\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/compiler/craft_graph_to_model_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3fb1f70>, scope_tree=SrcScope(name='tracr/compiler/craft_graph_to_model_test.py', kind='file', range=0:6708, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=6708:6708, code=''), len(children)=3)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Assemble weights of a transformer model from a craft residual stack.\"\"\"\\n\\nimport dataclasses\\nfrom typing import Any, Callable, Optional, List, Tuple\\n\\nimport chex\\nimport einops\\nimport haiku as hk\\nimport jax\\nimport jax.numpy as jnp\\nimport numpy as np\\nfrom tracr.craft import bases\\nfrom tracr.craft import transformers\\nfrom tracr.craft import vectorspace_fns\\nfrom tracr.transformer import encoder\\nfrom tracr.transformer import model\\nfrom typing_extensions import Protocol\\n\\n\\<EMAIL>\\nclass AssembledTransformerModelOutput:\\n  decoded: List[Any]  # length T.\\n  unembedded: jax.Array  # [B, T]     B = 1 always.\\n  layer_outputs: List[jax.Array]  # [B, T, D]\\n  residuals: List[jax.Array]  # [B, T, D]\\n  attn_logits: List[jax.Array]  # [B, T, T, H]\\n  transformer_output: jax.Array  # [B, T, D]\\n  input_embeddings: jax.Array\\n\\n\\nclass ModelForward(Protocol):\\n\\n  def __call__(\\n      self,\\n      params: hk.Params,\\n      emb: jax.Array,\\n  ) -> model.CompiledTransformerModelOutput:\\n    \"\"\"A hk-transformed forward pass through the compiled model.\"\"\"\\n\\n\\<EMAIL>\\nclass AssembledTransformerModel:\\n  \"\"\"Model architecture and parameters from assembling a model.\"\"\"\\n  forward: ModelForward\\n  get_compiled_model: Callable[[], model.CompiledTransformerModel]\\n  params: hk.Params\\n  model_config: model.TransformerConfig\\n  residual_labels: List[str]\\n  input_encoder: Optional[encoder.Encoder] = None\\n  output_encoder: Optional[encoder.Encoder] = None\\n\\n  def apply(self, tokens: List[bases.Value]) -> AssembledTransformerModelOutput:\\n    \"\"\"Returns output from running the model on a set of input tokens.\"\"\"\\n    if self.input_encoder:\\n      tokens = self.input_encoder.encode(tokens)\\n    tokens = jnp.array([tokens])\\n    output = self.forward(self.params, tokens)\\n    decoded = output.unembedded_output[0].tolist()\\n    if self.output_encoder:\\n      decoded = self.output_encoder.decode(decoded)\\n\\n    if self.input_encoder.bos_token:\\n      # Special case for decoding the bos token position, for which the output\\n      # decoder might have unspecified behavior.\\n      decoded = [self.input_encoder.bos_token] + decoded[1:]\\n\\n    return AssembledTransformerModelOutput(\\n        decoded=decoded,\\n        unembedded=output.unembedded_output,\\n        layer_outputs=output.transformer_output.layer_outputs,\\n        residuals=output.transformer_output.residuals,\\n        attn_logits=output.transformer_output.attn_logits,\\n        transformer_output=output.transformer_output.output,\\n        input_embeddings=output.transformer_output.input_embeddings)\\n\\n\\<EMAIL>\\nclass EmbeddingModules:\\n  \"\"\"Modules for embedding and tokens and positions and unembedding results.\"\"\"\\n  token_embed: model.CallableHaikuModule\\n  pos_embed: model.CallableHaikuModule\\n  unembed: model.CallableHaikuModule\\n\\n\\ndef _get_model_config_and_module_names(\\n    craft_model: transformers.SeriesWithResiduals\\n) -> Tuple[model.TransformerConfig, List[str]]:\\n  \"\"\"Returns model config and locations (in params) for halflayers.\"\"\"\\n\\n  multi_attn_heads: List[List[transformers.AttentionHead]] = []\\n  mlps: List[transformers.MLP] = []\\n  module_names: List[str] = []\\n\\n  candidate_module_names = []\\n  for layer in range(len(craft_model.blocks)):\\n    candidate_module_names.append(f\"transformer/layer_{layer}/attn\")\\n    candidate_module_names.append(f\"transformer/layer_{layer}/mlp\")\\n  candidate_module_names = iter(candidate_module_names)\\n\\n  for module in craft_model.blocks:\\n    if isinstance(module, transformers.MLP):\\n      mlps.append(module)\\n      layer_type = \"mlp\"\\n    else:\\n      multi_attn_heads.append(list(module.as_multi().heads()))\\n      layer_type = \"attn\"\\n    # Find next layer with the necessary type. Modules in-between, that are not\\n    # added to module_names will be disabled later by setting all weights to 0.\\n    module_name = next(candidate_module_names)\\n    while layer_type not in module_name:\\n      module_name = next(candidate_module_names)\\n    module_names.append(module_name)\\n\\n  num_layers = int(module_names[-1].split(\"_\")[1].split(\"/\")[0]) + 1\\n  heads = sum(multi_attn_heads, [])\\n\\n  if multi_attn_heads:\\n    num_heads = max(len(heads) for heads in multi_attn_heads)\\n    key_size = max(max(head.w_qk.matrix.shape) for head in heads)\\n  else:\\n    num_heads, key_size = 1, 1\\n\\n  if mlps:\\n    mlp_hidden_size = max(mlp.fst.output_space.num_dims for mlp in mlps)\\n  else:\\n    mlp_hidden_size = 1\\n\\n  model_config = model.TransformerConfig(\\n      num_heads=num_heads,\\n      num_layers=num_layers,\\n      key_size=key_size,\\n      mlp_hidden_size=mlp_hidden_size,\\n      dropout_rate=0.,\\n      activation_function=jax.nn.relu,\\n      layer_norm=False,\\n      causal=False,\\n  )\\n\\n  return model_config, module_names\\n\\n\\ndef _make_embedding_modules(\\n    residual_space: bases.VectorSpaceWithBasis,\\n    tokens_space: bases.VectorSpaceWithBasis,\\n    indices_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis) -> EmbeddingModules:\\n  \"\"\"Creates embedding and unembedding modules from vector spaces.\\n\\n  Args:\\n    residual_space: Full residual space of the model.\\n    tokens_space: Subspace to embed tokens to.\\n    indices_space: Subspace to embed indices/position embeddings to.\\n    output_space: Subspace to unembed outputs from.\\n\\n  Returns:\\n    EmbeddingModules containing modules for token embeddings, position\\n      embeddings and unembeddings.\\n  \"\"\"\\n  tokens_to_res = vectorspace_fns.project(tokens_space, residual_space)\\n\\n  # If we use the \\'one\\' direction, make sure all inputs have a 1 here\\n  one_dir = bases.BasisDirection(\"one\")\\n  if one_dir in residual_space:\\n    one_to_res = vectorspace_fns.Linear.from_action(\\n        tokens_space, residual_space,\\n        lambda x: residual_space.vector_from_basis_direction(one_dir))\\n    tokens_to_res = vectorspace_fns.Linear.combine_in_parallel(\\n        [tokens_to_res, one_to_res])\\n\\n  # Token embeddings.\\n  res_to_out = vectorspace_fns.project(residual_space, output_space)\\n  token_embed = hk.Embed(\\n      embedding_matrix=tokens_to_res.matrix, name=\"token_embed\")\\n\\n  # Positional embeddings.\\n  index_to_res = vectorspace_fns.project(indices_space, residual_space)\\n  # The zeroth position should not have any positional embeddings,\\n  # so we add one line of padding at the zeroth position.\\n  pos_matrix = np.concatenate(\\n      [np.zeros((1, residual_space.num_dims)), index_to_res.matrix], axis=0)\\n  pos_embed = hk.Embed(embedding_matrix=pos_matrix, name=\"pos_embed\")\\n\\n  def unembed(x, use_unembed_argmax):\\n    out = x @ res_to_out.matrix\\n    if use_unembed_argmax:\\n      return jnp.argmax(out, axis=-1)\\n    elif out.shape[-1] == 1:\\n      return out.squeeze(-1)\\n    return out\\n\\n  unembed_mod = hk.to_module(unembed)()\\n  return EmbeddingModules(\\n      token_embed=token_embed, pos_embed=pos_embed, unembed=unembed_mod)\\n\\n\\ndef assemble_craft_model(\\n    craft_model: transformers.SeriesWithResiduals,\\n    tokens_space: bases.VectorSpaceWithBasis,\\n    indices_space: bases.VectorSpaceWithBasis,\\n    output_space: bases.VectorSpaceWithBasis,\\n    categorical_output: bool,\\n    causal: bool = False,\\n) -> AssembledTransformerModel:\\n  \"\"\"Assembles the given components into a Haiku model with parameters.\\n\\n  Args:\\n    craft_model: Model to assemble weights for.\\n    tokens_space: Vectorspace to embed the input tokens to.\\n    indices_space: Vectorspace to embed the indices to (position encodings).\\n    output_space: Vectorspace that the model will write outputs to that should\\n      be unembedded.\\n    categorical_output: Whether the output is categorical. If True, we take an\\n      argmax when unembedding.\\n    causal: Whether to output a causally-masked model.\\n\\n  Returns:\\n    An AssembledTransformerModel that contains the model and parameters of the\\n      assembled transformer.\\n  \"\"\"\\n  # TODO(b/255936413): Make embeddings only retain the tokens and indices that\\n  #   are actually used.\\n  # TODO(b/255936496): Think about enabling layer norm and reversing it somehow\\n\\n  model_config, module_names = _get_model_config_and_module_names(craft_model)\\n  model_config.causal = causal\\n\\n  residual_space = bases.join_vector_spaces(craft_model.residual_space,\\n                                            tokens_space, indices_space,\\n                                            output_space)\\n  residual_labels = [str(basis_dir) for basis_dir in residual_space.basis]\\n\\n  # Build model with embedding and unembedding layers\\n  def get_compiled_model():\\n    transformer = model.Transformer(model_config)\\n    embed_modules = _make_embedding_modules(\\n        residual_space=residual_space,\\n        tokens_space=tokens_space,\\n        indices_space=indices_space,\\n        output_space=output_space)\\n    return model.CompiledTransformerModel(\\n        transformer=transformer,\\n        token_embed=embed_modules.token_embed,\\n        position_embed=embed_modules.pos_embed,\\n        unembed=embed_modules.unembed,\\n        use_unembed_argmax=categorical_output)\\n\\n  @hk.without_apply_rng\\n  @hk.transform\\n  def forward(emb):\\n    compiled_model = get_compiled_model()\\n    return compiled_model(emb, use_dropout=False)\\n\\n  params = forward.init(jax.random.PRNGKey(0), jnp.array([[1, 2, 3]]))\\n\\n  for key in params:\\n    if \"transformer\" in key:\\n      for par in params[key]:\\n        params[key][par] = np.zeros_like(params[key][par])\\n\\n  # Assemble attention and MLP weights.\\n  project = lambda space: vectorspace_fns.project(residual_space, space).matrix\\n\\n  for module_name, module in zip(module_names, craft_model.blocks):\\n    if isinstance(module, transformers.MLP):\\n      hidden_size = module.fst.output_space.num_dims\\n      residual_to_fst_input = project(module.fst.input_space)\\n      snd_output_to_residual = project(module.snd.output_space).T\\n      params[f\"{module_name}/linear_1\"][\"w\"][:, :hidden_size] = (\\n          residual_to_fst_input @ module.fst.matrix)\\n      params[f\"{module_name}/linear_2\"][\"w\"][:hidden_size, :] = (\\n          module.snd.matrix @ snd_output_to_residual)\\n    else:  # Attention module\\n      query, key, value, linear = [], [], [], []\\n      for head in module.as_multi().heads():\\n        key_size = head.w_qk.matrix.shape[1]\\n        query_mat = np.zeros((residual_space.num_dims, model_config.key_size))\\n        residual_to_query = project(head.w_qk.left_space)\\n        query_mat[:, :key_size] = residual_to_query @ head.w_qk.matrix\\n        query.append(query_mat)\\n\\n        key_mat = np.zeros((residual_space.num_dims, model_config.key_size))\\n        key_mat[:, :key_size] = project(head.w_qk.right_space)\\n        key.append(key_mat)\\n\\n        value_size = head.w_ov.matrix.shape[1]\\n        value_mat = np.zeros((residual_space.num_dims, model_config.key_size))\\n        residual_to_ov_input = project(head.w_ov.input_space)\\n        value_mat[:, :value_size] = residual_to_ov_input @ head.w_ov.matrix\\n        value.append(value_mat)\\n\\n        linear_mat = np.zeros((model_config.key_size, residual_space.num_dims))\\n        linear_mat[:value_size, :] = project(head.w_ov.output_space).T\\n        linear.append(linear_mat)\\n\\n      # Fill up heads that are not used with zero weights\\n      for _ in range(model_config.num_heads - module.as_multi().num_heads):\\n        query.append(np.zeros_like(query[0]))\\n        key.append(np.zeros_like(key[0]))\\n        value.append(np.zeros_like(value[0]))\\n        linear.append(np.zeros_like(linear[0]))\\n\\n      query = einops.rearrange(query,\\n                               \"heads input output -> input (heads output)\")\\n      key = einops.rearrange(key, \"heads input output -> input (heads output)\")\\n      value = einops.rearrange(value,\\n                               \"heads input output -> input (heads output)\")\\n      linear = einops.rearrange(linear,\\n                                \"heads input output -> (heads input) output\")\\n\\n      params[f\"{module_name}/query\"][\"w\"][:, :] = query\\n      params[f\"{module_name}/key\"][\"w\"][:, :] = key\\n      params[f\"{module_name}/value\"][\"w\"][:, :] = value\\n      params[f\"{module_name}/linear\"][\"w\"][:, :] = linear\\n\\n  params = jax.tree_util.tree_map(jnp.array, params)\\n  return AssembledTransformerModel(\\n      forward=forward.apply,\\n      get_compiled_model=get_compiled_model,\\n      params=params,\\n      model_config=model_config,\\n      residual_labels=residual_labels,\\n  )\\n', path=PosixPath('tracr/compiler/assemble.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3eadbf0>, scope_tree=SrcScope(name='tracr/compiler/assemble.py', kind='file', range=0:12893, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=12893:12893, code=''), len(children)=8)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"RASP programs only using the subset of RASP supported by the compiler.\"\"\"\\n\\nfrom typing import List, Sequence\\n\\nfrom tracr.rasp import rasp\\n\\n### Programs that work only under non-causal evaluation.\\n\\n\\ndef make_length() -> rasp.SOp:\\n  \"\"\"Creates the `length` SOp using selector width primitive.\\n\\n  Example usage:\\n    length = make_length()\\n    length(\"abcdefg\")\\n    >> [7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0]\\n\\n  Returns:\\n    length: SOp mapping an input to a sequence, where every element\\n      is the length of that sequence.\\n  \"\"\"\\n  all_true_selector = rasp.Select(\\n      rasp.tokens, rasp.tokens, rasp.Comparison.TRUE).named(\"all_true_selector\")\\n  return rasp.SelectorWidth(all_true_selector).named(\"length\")\\n\\n\\nlength = make_length()\\n\\n\\ndef make_reverse(sop: rasp.SOp) -> rasp.SOp:\\n  \"\"\"Create an SOp that reverses a sequence, using length primitive.\\n\\n  Example usage:\\n    reverse = make_reverse(rasp.tokens)\\n    reverse(\"Hello\")\\n    >> [\\'o\\', \\'l\\', \\'l\\', \\'e\\', \\'H\\']\\n\\n  Args:\\n    sop: an SOp\\n\\n  Returns:\\n    reverse : SOp that reverses the input sequence.\\n  \"\"\"\\n  opp_idx = (length - rasp.indices).named(\"opp_idx\")\\n  opp_idx = (opp_idx - 1).named(\"opp_idx-1\")\\n  reverse_selector = rasp.Select(rasp.indices, opp_idx,\\n                                 rasp.Comparison.EQ).named(\"reverse_selector\")\\n  return rasp.Aggregate(reverse_selector, sop).named(\"reverse\")\\n\\n\\ndef make_pair_balance(sop: rasp.SOp, open_token: str,\\n                      close_token: str) -> rasp.SOp:\\n  \"\"\"Return fraction of previous open tokens minus the fraction of close tokens.\\n\\n   (As implemented in the RASP paper.)\\n\\n  If the outputs are always non-negative and end in 0, that implies the input\\n  has balanced parentheses.\\n\\n  Example usage:\\n    num_l = make_pair_balance(rasp.tokens, \"(\", \")\")\\n    num_l(\"a()b(c))\")\\n    >> [0, 1/2, 0, 0, 1/5, 1/6, 0, -1/8]\\n\\n  Args:\\n    sop: Input SOp.\\n    open_token: Token that counts positive.\\n    close_token: Token that counts negative.\\n\\n  Returns:\\n    pair_balance: SOp mapping an input to a sequence, where every element\\n      is the fraction of previous open tokens minus previous close tokens.\\n  \"\"\"\\n  bools_open = rasp.numerical(sop == open_token).named(\"bools_open\")\\n  opens = rasp.numerical(make_frac_prevs(bools_open)).named(\"opens\")\\n\\n  bools_close = rasp.numerical(sop == close_token).named(\"bools_close\")\\n  closes = rasp.numerical(make_frac_prevs(bools_close)).named(\"closes\")\\n\\n  pair_balance = rasp.numerical(rasp.LinearSequenceMap(opens, closes, 1, -1))\\n  return pair_balance.named(\"pair_balance\")\\n\\n\\ndef make_shuffle_dyck(pairs: List[str]) -> rasp.SOp:\\n  \"\"\"Returns 1 if a set of parentheses are balanced, 0 else.\\n\\n   (As implemented in the RASP paper.)\\n\\n  Example usage:\\n    shuffle_dyck2 = make_shuffle_dyck(pairs=[\"()\", \"{}\"])\\n    shuffle_dyck2(\"({)}\")\\n    >> [1, 1, 1, 1]\\n    shuffle_dyck2(\"(){)}\")\\n    >> [0, 0, 0, 0, 0]\\n\\n  Args:\\n    pairs: List of pairs of open and close tokens that each should be balanced.\\n  \"\"\"\\n  assert len(pairs) >= 1\\n\\n  # Compute running balance of each type of parenthesis\\n  balances = []\\n  for pair in pairs:\\n    assert len(pair) == 2\\n    open_token, close_token = pair\\n    balance = make_pair_balance(\\n        rasp.tokens, open_token=open_token,\\n        close_token=close_token).named(f\"balance_{pair}\")\\n    balances.append(balance)\\n\\n  # Check if balances where negative anywhere -> parentheses not balanced\\n  any_negative = balances[0] < 0\\n  for balance in balances[1:]:\\n    any_negative = any_negative | (balance < 0)\\n\\n  # Convert to numerical SOp\\n  any_negative = rasp.numerical(rasp.Map(lambda x: x,\\n                                         any_negative)).named(\"any_negative\")\\n\\n  select_all = rasp.Select(rasp.indices, rasp.indices,\\n                           rasp.Comparison.TRUE).named(\"select_all\")\\n  has_neg = rasp.numerical(rasp.Aggregate(select_all, any_negative,\\n                                          default=0)).named(\"has_neg\")\\n\\n  # Check if all balances are 0 at the end -> closed all parentheses\\n  all_zero = balances[0] == 0\\n  for balance in balances[1:]:\\n    all_zero = all_zero & (balance == 0)\\n\\n  select_last = rasp.Select(rasp.indices, length - 1,\\n                            rasp.Comparison.EQ).named(\"select_last\")\\n  last_zero = rasp.Aggregate(select_last, all_zero).named(\"last_zero\")\\n\\n  not_has_neg = (~has_neg).named(\"not_has_neg\")\\n  return (last_zero & not_has_neg).named(\"shuffle_dyck\")\\n\\n\\ndef make_shuffle_dyck2() -> rasp.SOp:\\n  return make_shuffle_dyck(pairs=[\"()\", \"{}\"]).named(\"shuffle_dyck2\")\\n\\n\\ndef make_hist() -> rasp.SOp:\\n  \"\"\"Returns the number of times each token occurs in the input.\\n\\n   (As implemented in the RASP paper.)\\n\\n  Example usage:\\n    hist = make_hist()\\n    hist(\"abac\")\\n    >> [2, 1, 2, 1]\\n  \"\"\"\\n  same_tok = rasp.Select(rasp.tokens, rasp.tokens,\\n                         rasp.Comparison.EQ).named(\"same_tok\")\\n  return rasp.SelectorWidth(same_tok).named(\"hist\")\\n\\n\\ndef make_sort_unique(vals: rasp.SOp, keys: rasp.SOp) -> rasp.SOp:\\n  \"\"\"Returns vals sorted by < relation on keys.\\n\\n  Only supports unique keys.\\n\\n  Example usage:\\n    sort = make_sort(rasp.tokens, rasp.tokens)\\n    sort([2, 4, 3, 1])\\n    >> [1, 2, 3, 4]\\n\\n  Args:\\n    vals: Values to sort.\\n    keys: Keys for sorting.\\n  \"\"\"\\n  smaller = rasp.Select(keys, keys, rasp.Comparison.LT).named(\"smaller\")\\n  target_pos = rasp.SelectorWidth(smaller).named(\"target_pos\")\\n  sel_new = rasp.Select(target_pos, rasp.indices, rasp.Comparison.EQ)\\n  return rasp.Aggregate(sel_new, vals).named(\"sort\")\\n\\n\\ndef make_sort(vals: rasp.SOp, keys: rasp.SOp, *, max_seq_len: int,\\n              min_key: float) -> rasp.SOp:\\n  \"\"\"Returns vals sorted by < relation on keys, which don\\'t need to be unique.\\n\\n  The implementation differs from the RASP paper, as it avoids using\\n  compositions of selectors to break ties. Instead, it uses the arguments\\n  max_seq_len and min_key to ensure the keys are unique.\\n\\n  Note that this approach only works for numerical keys.\\n\\n  Example usage:\\n    sort = make_sort(rasp.tokens, rasp.tokens, 5, 1)\\n    sort([2, 4, 3, 1])\\n    >> [1, 2, 3, 4]\\n    sort([2, 4, 1, 2])\\n    >> [1, 2, 2, 4]\\n\\n  Args:\\n    vals: Values to sort.\\n    keys: Keys for sorting.\\n    max_seq_len: Maximum sequence length (used to ensure keys are unique)\\n    min_key: Minimum key value (used to ensure keys are unique)\\n\\n  Returns:\\n    Output SOp of sort program.\\n  \"\"\"\\n  keys = rasp.SequenceMap(lambda x, i: x + min_key * i / max_seq_len, keys,\\n                          rasp.indices)\\n  return make_sort_unique(vals, keys)\\n\\n\\ndef make_sort_freq(max_seq_len: int) -> rasp.SOp:\\n  \"\"\"Returns tokens sorted by the frequency they appear in the input.\\n\\n  Tokens the appear the same amount of times are output in the same order as in\\n  the input.\\n\\n  Example usage:\\n    sort = make_sort_freq(rasp.tokens, rasp.tokens, 5)\\n    sort([2, 4, 2, 1])\\n    >> [2, 2, 4, 1]\\n\\n  Args:\\n    max_seq_len: Maximum sequence length (used to ensure keys are unique)\\n  \"\"\"\\n  hist = -1 * make_hist().named(\"hist\")\\n  return make_sort(\\n      rasp.tokens, hist, max_seq_len=max_seq_len, min_key=1).named(\"sort_freq\")\\n\\n\\n### Programs that work under both causal and regular evaluation.\\n\\n\\ndef make_frac_prevs(bools: rasp.SOp) -> rasp.SOp:\\n  \"\"\"Count the fraction of previous tokens where a specific condition was True.\\n\\n   (As implemented in the RASP paper.)\\n\\n  Example usage:\\n    num_l = make_frac_prevs(rasp.tokens==\"l\")\\n    num_l(\"hello\")\\n    >> [0, 0, 1/3, 1/2, 2/5]\\n\\n  Args:\\n    bools: SOp mapping a sequence to a sequence of booleans.\\n\\n  Returns:\\n    frac_prevs: SOp mapping an input to a sequence, where every element\\n      is the fraction of previous \"True\" tokens.\\n  \"\"\"\\n  bools = rasp.numerical(bools)\\n  prevs = rasp.Select(rasp.indices, rasp.indices, rasp.Comparison.LEQ)\\n  return rasp.numerical(rasp.Aggregate(prevs, bools,\\n                                       default=0)).named(\"frac_prevs\")\\n\\n\\ndef shift_by(offset: int, /, sop: rasp.SOp) -> rasp.SOp:\\n  \"\"\"Returns the sop, shifted by `offset`, None-padded.\"\"\"\\n  select_off_by_offset = rasp.Select(rasp.indices, rasp.indices,\\n                                     lambda k, q: q == k + offset)\\n  out = rasp.Aggregate(select_off_by_offset, sop, default=None)\\n  return out.named(f\"shift_by({offset})\")\\n\\n\\ndef detect_pattern(sop: rasp.SOp, pattern: Sequence[rasp.Value]) -> rasp.SOp:\\n  \"\"\"Returns an SOp which is True at the final element of the pattern.\\n\\n  The first len(pattern) - 1 elements of the output SOp are None-padded.\\n\\n  detect_pattern(tokens, \"abc\")(\"abcabc\") == [None, None, T, F, F, T]\\n\\n  Args:\\n    sop: the SOp in which to look for patterns.\\n    pattern: a sequence of values to look for.\\n\\n  Returns:\\n    a sop which detects the pattern.\\n  \"\"\"\\n\\n  if len(pattern) < 1:\\n    raise ValueError(f\"Length of `pattern` must be at least 1. Got {pattern}\")\\n\\n  # detectors[i] will be a boolean-valued SOp which is true at position j iff\\n  # the i\\'th (from the end) element of the pattern was detected at position j-i.\\n  detectors = []\\n  for i, element in enumerate(reversed(pattern)):\\n    detector = sop == element\\n    if i != 0:\\n      detector = shift_by(i, detector)\\n    detectors.append(detector)\\n\\n  # All that\\'s left is to take the AND over all detectors.\\n  pattern_detected = detectors.pop()\\n  while detectors:\\n    pattern_detected = pattern_detected & detectors.pop()\\n\\n  return pattern_detected.named(f\"detect_pattern({pattern})\")\\n\\n\\ndef make_count_less_freq(n: int) -> rasp.SOp:\\n  \"\"\"Returns how many tokens appear fewer than n times in the input.\\n\\n  The output sequence contains this count in each position.\\n\\n  Example usage:\\n    count_less_freq = make_count_less_freq(2)\\n    count_less_freq([\"a\", \"a\", \"a\", \"b\", \"b\", \"c\"])\\n    >> [3, 3, 3, 3, 3, 3]\\n    count_less_freq([\"a\", \"a\", \"c\", \"b\", \"b\", \"c\"])\\n    >> [6, 6, 6, 6, 6, 6]\\n\\n  Args:\\n    n: Integer to compare token frequences to.\\n  \"\"\"\\n  hist = make_hist().named(\"hist\")\\n  select_less = rasp.Select(hist, hist,\\n                            lambda x, y: x <= n).named(\"select_less\")\\n  return rasp.SelectorWidth(select_less).named(\"count_less_freq\")\\n\\n\\ndef make_count(sop, token):\\n  \"\"\"Returns the count of `token` in `sop`.\\n\\n  The output sequence contains this count in each position.\\n\\n  Example usage:\\n    count = make_count(tokens, \"a\")\\n    count([\"a\", \"a\", \"a\", \"b\", \"b\", \"c\"])\\n    >> [3, 3, 3, 3, 3, 3]\\n    count([\"c\", \"a\", \"b\", \"c\"])\\n    >> [1, 1, 1, 1]\\n\\n  Args:\\n    sop: Sop to count tokens in.\\n    token: Token to count.\\n  \"\"\"\\n  return rasp.SelectorWidth(rasp.Select(\\n      sop, sop, lambda k, q: k == token)).named(f\"count_{token}\")\\n\\n\\ndef make_nary_sequencemap(f, *sops):\\n  \"\"\"Returns an SOp that simulates an n-ary SequenceMap.\\n\\n  Uses multiple binary SequenceMaps to convert n SOps x_1, x_2, ..., x_n\\n  into a single SOp arguments that takes n-tuples as value. The n-ary sequence\\n  map implementing f is then a Map on this resulting SOp.\\n\\n  Note that the intermediate variables representing tuples of varying length\\n  will be encoded categorically, and can become very high-dimensional. So,\\n  using this function might lead to very large compiled models.\\n\\n  Args:\\n    f: Function with n arguments.\\n    *sops: Sequence of SOps, one for each argument of f.\\n  \"\"\"\\n  values, *sops = sops\\n  for sop in sops:\\n    # x is a single entry in the first iteration but a tuple in later iterations\\n    values = rasp.SequenceMap(\\n        lambda x, y: (*x, y) if isinstance(x, tuple) else (x, y), values, sop)\\n  return rasp.Map(lambda args: f(*args), values)\\n', path=PosixPath('tracr/compiler/lib.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3e0d790>, scope_tree=SrcScope(name='tracr/compiler/lib.py', kind='file', range=0:12068, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=12068:12068, code=''), len(children)=18)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Useful helpers for model debugging.\"\"\"\\n\\n\\ndef print_arrays(arrays, labels=None, colwidth=12):\\n  \"\"\"Pretty-prints a list of [1, T, D] arrays.\"\"\"\\n  if labels is not None:\\n    print(\" |\".join(labels))\\n    widths = [len(l) for l in labels]\\n  else:\\n    widths = [colwidth] * len(arrays[0].shape[-1])\\n  for layer in arrays:\\n    print(\"=\" * (colwidth + 1) * layer.shape[1])\\n    for row in layer[0]:\\n      print(\" |\".join([f\"{x:<{width}.2f}\" for x, width in zip(row, widths)]))\\n', path=PosixPath('tracr/utils/debugging.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3d9ec90>, scope_tree=SrcScope(name='tracr/utils/debugging.py', kind='file', range=0:1168, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1168:1168, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n', path=PosixPath('tracr/utils/__init__.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3daba30>, scope_tree=SrcScope(name='tracr/utils/__init__.py', kind='file', range=0:696, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=696:696, code=''), len(children)=1)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Helpers for handling errors in user-provided functions.\"\"\"\\n\\nimport functools\\nimport logging\\nfrom typing import Any, Callable\\n\\n\\ndef ignoring_arithmetic_errors(fun: Callable[..., Any]) -> Callable[..., Any]:\\n  \"\"\"Makes fun return None instead of raising ArithmeticError.\"\"\"\\n\\n  @functools.wraps(fun)\\n  def fun_wrapped(*args):\\n    try:\\n      return fun(*args)\\n    except ArithmeticError:\\n      logging.warning(\\n          \"Encountered arithmetic error in function: for value %s. \"\\n          \"Assuming this input will never occur.\", str(args))\\n      return None\\n\\n  return fun_wrapped\\n', path=PosixPath('tracr/utils/errors.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3daf350>, scope_tree=SrcScope(name='tracr/utils/errors.py', kind='file', range=0:1277, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1277:1277, code=''), len(children)=2)),\n", " ScopeParsedFile(code='# Copyright 2022 DeepMind Technologies Limited. All Rights Reserved.\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n# ==============================================================================\\n\"\"\"Tests for rasp.helper.\"\"\"\\n\\nfrom absl.testing import absltest\\nfrom absl.testing import parameterized\\nfrom tracr.utils import errors\\n\\n\\nclass FunIgnoreArithmeticErrorsTest(parameterized.TestCase):\\n\\n  def test_ignoring_arithmetic_errors(self):\\n    fun = lambda x: 1 / x\\n    fun_ignore = errors.ignoring_arithmetic_errors(fun)\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      res = fun_ignore(0)\\n    self.assertIs(res, None)\\n\\n    self.assertEqual(fun_ignore(1), 1)\\n    self.assertEqual(fun_ignore(2), 0.5)\\n    self.assertEqual(fun_ignore(-2), -0.5)\\n\\n  def test_ignoring_arithmetic_errors_two_arguments(self):\\n    fun = lambda x, y: 1 / x + 1 / y\\n    fun_ignore = errors.ignoring_arithmetic_errors(fun)\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      res = fun_ignore(0, 1)\\n    self.assertIs(res, None)\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      res = fun_ignore(0, 0)\\n    self.assertIs(res, None)\\n\\n    with self.assertLogs(level=\"WARNING\"):\\n      res = fun_ignore(1, 0)\\n    self.assertIs(res, None)\\n\\n    self.assertEqual(fun_ignore(1, 1), 2)\\n    self.assertEqual(fun_ignore(1, 2), 1.5)\\n    self.assertEqual(fun_ignore(-2, 2), 0)\\n\\n\\nif __name__ == \"__main__\":\\n  absltest.main()\\n', path=PosixPath('tracr/utils/errors_test.py'), lang='python', ts_tree=<tree_sitter.Tree object at 0x7f21b3db65b0>, scope_tree=SrcScope(name='tracr/utils/errors_test.py', kind='file', range=0:1880, prefix=SrcSpan(range=0:0, code=''), doc_str=SrcSpan(range=0:0, code=''), suffix=SrcSpan(range=1880:1880, code=''), len(children)=3))]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
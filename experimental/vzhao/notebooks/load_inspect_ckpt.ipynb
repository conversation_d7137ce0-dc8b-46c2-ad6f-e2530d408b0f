{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["path = \"/mnt/efs/augment/checkpoints/star_ethanol/ethanol6_17.1_mean_1stsp_codegen_2000/\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base.fastforward.starcoder import fwd_starcoder"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import torch\n", "\n", "sd = fwd_starcoder._get_state_dict_from_pipeline_files(\n", "    load_dir=pathlib.Path(\n", "        # \"/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_1024_2000/global_step2000\"\n", "        \"/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint\"\n", "    ),\n", "    num_transformer_layers=24,\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['attention.dense.weight', 'attention.dense.bias', 'attention.query.weight', 'attention.query.bias', 'attention.key_value.weight', 'attention.key_value.bias', 'mlp.dense_h_to_4h.bias', 'mlp.dense_h_to_4h.weight', 'mlp.dense_4h_to_h.bias', 'mlp.dense_4h_to_h.weight', 'input_layernorm.bias', 'input_layernorm.weight', 'post_attention_layernorm.bias', 'post_attention_layernorm.weight'])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["foo = torch.load('/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_1024_2000/global_step2000/layer_00-model_00-model_states.pt')\n", "# foo = torch.load('/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint/layer_25-model_00-model_states.pt')\n", "foo.keys()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['dummy', 'word_embeddings.weight'])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
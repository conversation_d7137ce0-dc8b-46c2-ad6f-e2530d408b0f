{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports and Utilities"]}, {"cell_type": "code", "execution_count": 415, "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'experimental.vzhao.notebooks.utils' from '/home/<USER>/augment/experimental/vzhao/notebooks/utils.py'>"]}, "execution_count": 415, "metadata": {}, "output_type": "execute_result"}], "source": ["import importlib\n", "import pandas as pd\n", "from IPython.display import HTML, clear_output, display\n", "import ipywidgets as widgets\n", "\n", "from experimental.vzhao.notebooks import utils as vz_utils \n", "\n", "importlib.reload(vz_utils)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# RepoCode function completion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Register Experiments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Function"]}, {"cell_type": "code", "execution_count": 286, "metadata": {}, "outputs": [], "source": ["# Multiline\n", "EXPERIMENTS = {}\n", "\n", "## Baselines\n", "EXPERIMENTS.update(\n", "    {\n", "        \"EmptyCompletion\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/RxkjHdTh\",\n", "            description=\"Sanity Check with empty completion. All test cases should fail.\",\n", "            weight=128.0,\n", "        ),\n", "        \"GoldCompletion\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/mnnvX3tZ\",\n", "            description=\"Sanity Check with gold completion. All test cases should pass.\",\n", "            weight=128.0,\n", "        ),\n", "        \"StarCoderBase+NoRetrieval\": vz_utils.Experiment(\n", "            poc=\"colin\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/DU6XaJTk\",\n", "            description=\"StarCoder prompted with prefix. No RAG.\",\n", "            weight=64.0,\n", "        ),\n", "        \"RagFIMAV17B+NoRetrieval\": vz_utils.Experiment(\n", "            poc=\"colin\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/XAPHwL3r\",\n", "            description=\"StarCoder prompted with prefix. No RAG.\",\n", "            weight=32.0,\n", "        ),\n", "        # 'RagFIMAV17B+NoRetrieval+NoIndent': vz_utils.Experiment(\n", "        #     poc='colin',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/SguwrbfF',\n", "        #     description='StarCoder prompted with prefix. No RAG.',\n", "        #     weight=32.0,\n", "        # ),\n", "        # 'FIMAv2+No': vz_utils.Experiment(\n", "        #     poc='colin',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/Eh9VPVR9',\n", "        #     description='StarCoder prompted with prefix. No RAG.',\n", "        #     weight=32.0,\n", "        # ),\n", "        # 'FIMAv2+No+NoIndent': vz_utils.Experiment(\n", "        #     poc='colin',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/DjmwjA2Y',\n", "        #     description='StarCoder prompted with prefix. No RAG.',\n", "        #     weight=32.0,\n", "        # ),\n", "    }\n", ")\n", "\n", "# # Boy<PERSON><PERSON> Retrieval, no oracle\n", "# # p<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "# EXPERIMENTS.update(\n", "#     {\n", "#     'BoykinTop1': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/84q7HC3X',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'BoykinTop2': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/fBBE8qEy',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'BoykinTop10': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/bCnaf45T',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "\n", "\n", "#     'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/L9kTWMQj',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/GWkQiefu',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/f2d3poDc',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     }\n", "# )\n", "\n", "# pydiff7b, <PERSON><PERSON><PERSON>25\n", "EXPERIMENTS.update(\n", "    {\n", "        \"RagFIMAV17B+ScopeBM25+Stub+Top1\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/STZRQa6B\",\n", "            description=\"Rag finetuned + ScopeChunk + StubQuery/Key + Top1\",\n", "            weight=1.0,\n", "        ),\n", "        \"RagFIMAV17B+ScopeBM25+Stub+Top2\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/CLRFsK2z\",\n", "            description=\"Rag finetuned + ScopeChunk + StubQuery/Key + Top1\",\n", "            weight=1.0,\n", "        ),\n", "        \"RagFIMAV17B+ScopeBM25+Stub+Top10\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/n5tU3KGJ\",\n", "            description=\"Rag finetuned + ScopeChunk + StubQuery/Key + Top1\",\n", "            weight=1.0,\n", "        ),\n", "    }\n", ")\n", "\n", "# pydiff7b, ScopeBodyBM25\n", "EXPERIMENTS.update(\n", "    {\n", "        \"RagFIMAV17B+ScopeBM25+Body+Top1\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/Fr86SNwT\",\n", "            description=\"Rag finetuned + ScopeChunk + StubQuery/Key + Top1\",\n", "            weight=1.0,\n", "        ),\n", "        \"RagFIMAV17B+ScopeBM25+Body+Top1+BUG\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/mmhfoBtR\",\n", "            description=\"Rag finetuned + ScopeChunk + StubQuery/Key + Top1\",\n", "            weight=1.0,\n", "        ),\n", "        # 'RagFIMAV17B+ScopeBM25+Body+Top2': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/ewmbW5nd',\n", "        #     description='Rag finetuned + <PERSON>opeChunk + StubQuery/Key + Top1',\n", "        #     weight=1.0,\n", "        # ),\n", "        # 'RagFIMAV17B+ScopeBM25+Body+Top10': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/MJomMsc9',\n", "        #     description='Rag finetuned + <PERSON>opeChunk + StubQuery/Key + Top1',\n", "        #     weight=1.0,\n", "        # ),\n", "    }\n", ")\n", "\n", "# pydiff7b, <PERSON>\n", "EXPERIMENTS.update(\n", "    {\n", "        \"RagFIMAV17B+OracleTop1\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/MVS875TU\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=4.0,\n", "        ),\n", "        \"RagFIMAV17B+ScopeOracleTop1\": vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/MVyR5Z29\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=4.0,\n", "        ),\n", "        # 'RagFIMAV17B+OracleTop2': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/BvjDubA9',\n", "        #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix. Rerank top 1000.',\n", "        #     weight=4.0,\n", "        # ),\n", "        # 'RagFIMAV17B+OracleTop10': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/NXeCus8a',\n", "        #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        #     weight=4.0,\n", "        # ),\n", "        ## Oracle Greedy\n", "        # 'GreedyOracleTop1Rank100': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/9oY3XT2V',\n", "        #     description='Greedy oracle max number chunk = 1',\n", "        #     weight=1.2,\n", "        # ),\n", "        ## Oracle (non Greedy)\n", "        # 'OracleTop2Rank100': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/bPNZL9X4',\n", "        #     description='Oracle max number chunk = 2',\n", "        #     weight=2,\n", "        # ),\n", "        # 'OracleTop5Rank100': vz_utils.Experiment(\n", "        #     poc='vzhao',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/gDpSJ7Tm',\n", "        #     description='Oracle max number chunk = 5',\n", "        #     weight=1,\n", "        # ),\n", "        # 'DiffRe1000+PPL1000': vz_utils.Experiment(\n", "        #     poc='igor',\n", "        #     eval_dir='/mnt/efs/augment/eval/jobs/oY8zbris',\n", "        #     description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "        #     weight=10.0,\n", "        # )\n", "    }\n", ")\n", "\n", "\n", "\n", "\n", "\n", "path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_small.json\"\n", "# path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_retrieval_wins.json\"\n", "import json\n", "\n", "ALLOW_DENY_IDS = json.load(open(path_to_json, \"r\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multiline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Multiline\n", "\n", "EXPERIMENTS = {}\n", "\n", "# EXPERIMENTS.update({\n", "#     ## <PERSON>ines\n", "#     'EmptyCompletion': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/RxkjHdTh',\n", "#         description='Sanity Check with empty completion. All test cases should fail.',\n", "#         weight=128.0,\n", "#     ),\n", "#     'GoldCompletion': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/mnnvX3tZ',\n", "#         description='Sanity Check with gold completion. All test cases should pass.',\n", "#         weight=128.0,\n", "#     ),\n", "#     # 'StarCoderBase+NoRetrieval': vz_utils.Experiment(\n", "#     #     poc='colin',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/DU6XaJTk',\n", "#     #     description='StarCoder prompted with prefix. No RAG.',\n", "#     #     weight=64.0,\n", "#     # ),\n", "#     'RagFIMAV17B+NoRetrieval': vz_utils.Experiment(\n", "#         poc='colin',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/XAPHwL3r',\n", "#         description='StarCoder prompted with prefix. No RAG.',\n", "#         weight=64.0,\n", "#     ),\n", "#     # 'RagFIMAV17B+NoRetrieval+NoIndent': vz_utils.Experiment(\n", "#     #     poc='colin',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/SguwrbfF',\n", "#     #     description='StarCoder prompted with prefix. No RAG.',\n", "#     #     weight=32.0,\n", "#     # ),\n", "#     # 'FIMAv2+No': vz_utils.Experiment(\n", "#     #     poc='colin',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/Eh9VPVR9',\n", "#     #     description='StarCoder prompted with prefix. No RAG.',\n", "#     #     weight=32.0,\n", "#     # ),\n", "#     # 'FIMAv2+No+NoIndent': vz_utils.Experiment(\n", "#     #     poc='colin',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/DjmwjA2Y',\n", "#     #     description='StarCoder prompted with prefix. No RAG.',\n", "#     #     weight=32.0,\n", "#     # ),\n", "# })\n", "\n", "\n", "# Boy<PERSON>in Retrieval, no oracle\n", "# EXPERIMENTS.update({\n", "#     # 'BoykinTop1': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/84q7HC3X',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#     #     weight=1.0,\n", "#     # ),\n", "#     # 'BoykinTop2': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/fBBE8qEy',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#     #     weight=1.0,\n", "#     # ),\n", "#     # 'BoykinTop10': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/bCnaf45T',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#     #     weight=1.0,\n", "#     # ),\n", "\n", "#     # p<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "#     'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/L9kTWMQj',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/GWkQiefu',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "#     'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/f2d3poDc',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=1.0,\n", "#     ),\n", "# })\n", "\n", "# pydiff7b + ScopeBM25\n", "# EXPERIMENTS.update({\n", "#     # 'RagFIMAV17B+ScopeBM25+Stub+Top1': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/STZRQa6B',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "#     # 'RagFIMAV17B+ScopeBM25+Stub+Top2': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/CLRFsK2z',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "#     # 'RagFIMAV17B+ScopeBM25+Stub+Top10': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/n5tU3KGJ',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "\n", "#     # pyd<PERSON>7b, ScopeBodyBM25\n", "#     'RagFIMAV17B+ScopeBM25+Body+Top1': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/Fr86SNwT',\n", "#         description='Rag finetuned + <PERSON>opeChunk + StubQuery/Key + Top1',\n", "#         weight=1.0,\n", "#     ),\n", "#     # 'RagFIMAV17B+ScopeBM25+Body+Top1+BUG': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/mmhfoBtR',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "    \n", "#     # 'RagFIMAV17B+ScopeBM25+Body+Top2': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/ewmbW5nd',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "#     # 'RagFIMAV17B+ScopeBM25+Body+Top10': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/MJomMsc9',\n", "#     #     description='Rag finetuned + ScopeChunk + StubQuery/Key + Top1',\n", "#     #     weight=1.0,\n", "#     # ),\n", "# })\n", "\n", "# # pydiff7b + Oracle\n", "# EXPERIMENTS.update({\n", "#     'RagFIMAV17B+OracleTop1': vz_utils.Experiment(\n", "#         poc='vzhao',\n", "#         eval_dir='/mnt/efs/augment/eval/jobs/MVS875TU',\n", "#         description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#         weight=4.0,\n", "#     ),\n", "#     # 'RagFIMAV17B+ScopeOracleTop1': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/MVyR5Z29',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#     #     weight=4.0,\n", "#     # ),\n", "#     # 'RagFIMAV17B+OracleTop2': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/BvjDubA9',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix. Rerank top 1000.',\n", "#     #     weight=4.0,\n", "#     # ),\n", "#     # 'RagFIMAV17B+OracleTop10': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/NXeCus8a',\n", "#     #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "#     #     weight=4.0,\n", "#     # ),\n", "\n", "\n", "#     ## Oracle Greedy\n", "#     # 'GreedyOracleTop1Rank100': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/9oY3XT2V',\n", "#     #     description='Greedy oracle max number chunk = 1',\n", "#     #     weight=1.2,\n", "#     # ),\n", "\n", "\n", "\n", "#     ## Oracle (non Greedy)\n", "#     # 'OracleTop2Rank100': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/bPNZL9X4',\n", "#     #     description='Oracle max number chunk = 2',\n", "#     #     weight=2,\n", "#     # ),\n", "#     # 'OracleTop5Rank100': vz_utils.Experiment(\n", "#     #     poc='vzhao',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/gDpSJ7Tm',\n", "#     #     description='Oracle max number chunk = 5',\n", "#     #     weight=1,\n", "#     # ),\n", "\n", "#     # 'DiffRe1000+PPL1000': vz_utils.Experiment(\n", "#     #     poc='igor',\n", "#     #     eval_dir='/mnt/efs/augment/eval/jobs/oY8zbris',\n", "#     #     description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "#     #     weight=10.0,\n", "#     # )\n", "    \n", "# })\n", "\n", "\n", "# BM25 + Usage Hybrid\n", "# EXPERIMENTS.update(\n", "#     {\n", "#         \"pydiff2m_7b+BM25Body+DefLookupALL+Top5-5\": vz_utils.Experiment(\n", "#             poc=\"vzhao\",\n", "#             eval_dir=\"/mnt/efs/augment/eval/jobs/NeKyM6G4\",\n", "#             description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "#             weight=1.0,\n", "#         ),\n", "#         \"pydiff2m_7b+BM25Body+DefLookupALL+Top0-10\": vz_utils.Experiment(\n", "#             poc=\"vzhao\",\n", "#             eval_dir=\"/mnt/efs/augment/eval/jobs/ByVDFwSn\",\n", "#             description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "#             weight=4.0,\n", "#         ),\n", "#     }\n", "# )\n", "\n", "\n", "# Ethonal\n", "EXPERIMENTS.update(\n", "    {\n", "        'diffb1m_16b+DiffBoyKin+Top64+OracleRerank': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/HHftiD96\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+DiffBoyKin+Top64+EthanolRerank': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/dN9T8CWV\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+DiffBoyKin+Top128+EthanolRerank': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/RSJxoYZZ\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+DiffBoyKin+Top256+EthanolRerank': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/RQ9P8SNA\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+DiffBoyKin+Top512+EthanolRerank': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/isK4q6uz\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+Ethanol+Top32': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/f5mYqeb6\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "        'diffb1m_16b+DiffBoyKin+Top32': vz_utils.Experiment(\n", "            poc=\"vzhao\",\n", "            eval_dir=\"/mnt/efs/augment/eval/jobs/edk47iS7\",\n", "            description=\"StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.\",\n", "            weight=1.0,\n", "        ),\n", "    }\n", ")\n", "\n", "path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_small.json\"\n", "# path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_retrieval_wins.json\"\n", "import json\n", "# ALLOW_DENY_IDS = json.load(open(path_to_json, 'r'))\n", "ALLOW_DENY_IDS = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### AllLang"]}, {"cell_type": "code", "execution_count": 364, "metadata": {}, "outputs": [], "source": ["EXPERIMENTS = {\n", "    # Baseline\n", "    'diffb1m7B+NoRetrieval': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/M6RotFAv',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    'diffb1m7B+NoRetrieval2': vz_utils.Experiment(\n", "        poc='colin',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/EgPSQ924',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    # # Dense Retrieval\n", "    # 'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/MBTPu5wQ',\n", "    #     description='Sanity Check with empty completion. All test cases should fail.',\n", "    #     weight=128.\n", "    # ),\n", "    # 'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/dfJSHNvz',\n", "    #     description='Sanity Check with empty completion. All test cases should fail.',\n", "    #     weight=128.\n", "    # ),\n", "    # 'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/HuobqgP4',\n", "    #     description='Sanity Check with empty completion. All test cases should fail.',\n", "    #     weight=128.\n", "    # ),\n", "\n", "    # Oracle\n", "    'diffb1m7B+Oracle4k+1BRerank250': vz_utils.Experiment(\n", "        poc='colin',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/Cywx97aP',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    \n", "}\n", "\n", "ALLOW_DENY_IDS = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistics"]}, {"cell_type": "code", "execution_count": 417, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">patch_id</th>\n", "      <th colspan=\"2\" halign=\"left\">num_chunks_in_prompt</th>\n", "      <th colspan=\"2\" halign=\"left\">line_prompt_minus_line_suffix</th>\n", "      <th colspan=\"2\" halign=\"left\">sequence_length</th>\n", "    </tr>\n", "    <tr>\n", "      <th>pass/fail</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "    </tr>\n", "    <tr>\n", "      <th>experiment</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>EmptyCompletion</th>\n", "      <td>279.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+NoRetrieval</th>\n", "      <td>124.0</td>\n", "      <td>155.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>9116.64</td>\n", "      <td>9377.05</td>\n", "      <td>2583.94</td>\n", "      <td>2602.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>pydiff2m_7b+BM25Body+DefLookupALL+Top0-10</th>\n", "      <td>110.0</td>\n", "      <td>169.0</td>\n", "      <td>6.13</td>\n", "      <td>6.43</td>\n", "      <td>14826.45</td>\n", "      <td>14507.54</td>\n", "      <td>4132.24</td>\n", "      <td>3976.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top1</th>\n", "      <td>104.0</td>\n", "      <td>175.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>10088.22</td>\n", "      <td>10773.53</td>\n", "      <td>2843.85</td>\n", "      <td>3019.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+ScopeBM25+Body+Top1</th>\n", "      <td>100.0</td>\n", "      <td>179.0</td>\n", "      <td>1.62</td>\n", "      <td>1.65</td>\n", "      <td>10245.61</td>\n", "      <td>10947.39</td>\n", "      <td>2880.77</td>\n", "      <td>3052.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top2</th>\n", "      <td>97.0</td>\n", "      <td>182.0</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>11607.63</td>\n", "      <td>11932.88</td>\n", "      <td>3295.40</td>\n", "      <td>3342.54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>pydiff2m_7b+BM25Body+DefLookupALL+Top5-5</th>\n", "      <td>93.0</td>\n", "      <td>186.0</td>\n", "      <td>4.95</td>\n", "      <td>5.40</td>\n", "      <td>16276.91</td>\n", "      <td>16445.40</td>\n", "      <td>4578.97</td>\n", "      <td>4595.47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top10</th>\n", "      <td>92.0</td>\n", "      <td>187.0</td>\n", "      <td>9.78</td>\n", "      <td>9.80</td>\n", "      <td>21697.22</td>\n", "      <td>22347.43</td>\n", "      <td>6151.83</td>\n", "      <td>6310.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+OracleTop1</th>\n", "      <td>83.0</td>\n", "      <td>196.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>10301.69</td>\n", "      <td>10686.84</td>\n", "      <td>2888.80</td>\n", "      <td>3010.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GoldCompletion</th>\n", "      <td>0.0</td>\n", "      <td>279.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          patch_id          \n", "pass/fail                                   FAILED PASSED   \n", "experiment                                                  \n", "EmptyCompletion                              279.0    0.0  \\\n", "RagFIMAV17B+NoRetrieval                      124.0  155.0   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top0-10    110.0  169.0   \n", "RagFIMAV17B+Top1                             104.0  175.0   \n", "RagFIMAV17B+ScopeBM25+Body+Top1              100.0  179.0   \n", "RagFIMAV17B+Top2                              97.0  182.0   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top5-5      93.0  186.0   \n", "RagFIMAV17B+Top10                             92.0  187.0   \n", "RagFIMAV17B+OracleTop1                        83.0  196.0   \n", "GoldCompletion                                 0.0  279.0   \n", "\n", "                                          num_chunks_in_prompt          \n", "pass/fail                                               FAILED PASSED   \n", "experiment                                                              \n", "EmptyCompletion                                           0.00   0.00  \\\n", "RagFIMAV17B+NoRetrieval                                   0.00   0.00   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top0-10                 6.13   6.43   \n", "RagFIMAV17B+Top1                                          1.00   1.00   \n", "RagFIMAV17B+ScopeBM25+Body+Top1                           1.62   1.65   \n", "RagFIMAV17B+Top2                                          2.00   2.00   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top5-5                  4.95   5.40   \n", "RagFIMAV17B+Top10                                         9.78   9.80   \n", "RagFIMAV17B+OracleTop1                                    1.00   1.00   \n", "GoldCompletion                                            0.00   0.00   \n", "\n", "                                          line_prompt_minus_line_suffix   \n", "pass/fail                                                        FAILED   \n", "experiment                                                                \n", "EmptyCompletion                                                    0.00  \\\n", "RagFIMAV17B+NoRetrieval                                         9116.64   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top0-10                      14826.45   \n", "RagFIMAV17B+Top1                                               10088.22   \n", "RagFIMAV17B+ScopeBM25+Body+Top1                                10245.61   \n", "RagFIMAV17B+Top2                                               11607.63   \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top5-5                       16276.91   \n", "RagFIMAV17B+Top10                                              21697.22   \n", "RagFIMAV17B+OracleTop1                                         10301.69   \n", "GoldCompletion                                                     0.00   \n", "\n", "                                                    sequence_length           \n", "pass/fail                                    PASSED          FAILED   PASSED  \n", "experiment                                                                    \n", "EmptyCompletion                                0.00            0.00     0.00  \n", "RagFIMAV17B+NoRetrieval                     9377.05         2583.94  2602.03  \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top0-10  14507.54         4132.24  3976.40  \n", "RagFIMAV17B+Top1                           10773.53         2843.85  3019.56  \n", "RagFIMAV17B+ScopeBM25+Body+Top1            10947.39         2880.77  3052.04  \n", "RagFIMAV17B+Top2                           11932.88         3295.40  3342.54  \n", "pydiff2m_7b+BM25Body+DefLookupALL+Top5-5   16445.40         4578.97  4595.47  \n", "RagFIMAV17B+Top10                          22347.43         6151.83  6310.80  \n", "RagFIMAV17B+OracleTop1                     10686.84         2888.80  3010.83  \n", "GoldCompletion                                 0.00            0.00     0.00  "]}, "execution_count": 417, "metadata": {}, "output_type": "execute_result"}], "source": ["from multiprocessing.pool import ThreadPool, Pool\n", "import re\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "tokenizer = StarCoderTokenizer()\n", "import pandas as pd\n", "pd.set_option('display.precision', 2)\n", "\n", "def helper(key, exp):\n", "    results = vz_utils.load(exp)\n", "    output = []\n", "    for pid in results:\n", "        # This works for 'comment2' style prompt.\n", "        num_chunks_in_prompt = sum(\n", "            [\n", "                \"# the below code fragment can be found in:\" == line\n", "                for line in results[pid][\"prompt\"].splitlines()\n", "            ]\n", "        )\n", "        # This works for <PERSON> prompt.\n", "        num_chunks_in_prompt = max( num_chunks_in_prompt, len(re.findall('<filename>', results[pid][\"prompt\"])))\n", "\n", "        output.append(\n", "            \n", "            {\n", "                \"experiment\": key,\n", "                \"patch_id\": pid,\n", "                \"pass/fail\": results[pid][\"result\"],\n", "                \"num_chunks_in_prompt\": num_chunks_in_prompt,\n", "                \"line_prompt_minus_line_suffix\": len(\n", "                    results[pid][\"prompt\"].split(\"<fim_middle>\")[0]\n", "                ),\n", "                \"sequence_length\": len(tokenizer.tokenize(results[pid][\"prompt\"])),\n", "                \"weight\": exp.weight,\n", "            }\n", "        )\n", "    return output\n", "\n", "\n", "data = []\n", "for key, exp in EXPERIMENTS.items():\n", "    data.append(helper(key, exp))\n", "\n", "# pool = ThreadPool(4)\n", "# pool = Pool(16)\n", "# data = pool.starmap(helper, EXPERIMENTS.items())\n", "\n", "df = []\n", "for d in data:\n", "    df.extend(d)\n", "\n", "df = pd.DataFrame(df)\n", "\n", "# Optional: Filter IDS.\n", "if ALLOW_DENY_IDS and ALLOW_DENY_IDS[\"allowed\"]:\n", "    df = df.loc[df[\"patch_id\"].apply(lambda x: x in ALLOW_DENY_IDS[\"allowed\"]), :]\n", "# df = df.loc[df[\"patch_id\"].apply(lambda x: x in tmp_list), :]\n", "\n", "clear_output(wait=True)\n", "df.groupby([\"experiment\", \"pass/fail\"]).agg(\n", "    {\n", "        \"patch_id\": \"count\",\n", "        \"num_chunks_in_prompt\": \"mean\",\n", "        \"line_prompt_minus_line_suffix\": \"mean\",\n", "        \"sequence_length\": \"mean\",\n", "    }\n", ").reset_index().pivot(columns=[\"pass/fail\"], index=\"experiment\").fillna(0).sort_values(\n", "    [(\"patch_id\", \"PASSED\")]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.set_index(\"experiment\").loc[\"RagFIMAV17B+ScopeBM25+Body+Top1\", :].sort_values(\n", "    \"patch_id\", key=lambda x: [i.lower() for i in x]\n", ")[[\"patch_id\", \"pass/fail\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pass/Fail HeatMap"]}, {"cell_type": "code", "execution_count": 418, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 279)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "value_mapping = {\n", "    \"PASSED\": 1,\n", "    \"FAILED\": -1,\n", "    \"OTHER\": 0,\n", "    \"TIMEOUT\": 0,\n", "}\n", "foo = (\n", "    df.set_index(\"experiment\")\n", "    .loc[list(EXPERIMENTS.keys()), :]\n", "    .reset_index()\n", "    .pivot(index=\"experiment\", columns=\"patch_id\", values=\"pass/fail\")\n", "    .replace(value_mapping)\n", ")\n", "\n", "weights = (\n", "    df.set_index(\"experiment\")\n", "    .loc[list(EXPERIMENTS.keys()), :]\n", "    .reset_index()\n", "    .pivot(index=\"experiment\", columns=\"patch_id\", values=\"weight\")\n", ")\n", "weighted = foo * weights\n", "# Sort columns.\n", "foo = foo[weighted.sum(axis=0).sort_values(ascending=False).index]\n", "# Sort rows\n", "foo = foo.loc[foo.sum(axis=1).sort_values().index]\n", "\n", "cmap = sns.color_palette(\"Spectral\", 16)\n", "\n", "\n", "# One figure for all.\n", "plt.figure(figsize=(20, len(foo.index) * 0.3))\n", "s = sns.heatmap(\n", "    foo,\n", "    cmap=[cmap[5], cmap[0], cmap[-1]],\n", "    xticklabels=False,\n", ")\n", "print(foo.shape)\n", "\n", "# # Detailed plot.\n", "# exps_per_fig = 80\n", "# for i in range(4):\n", "#     plt.figure(figsize=(20, len(foo.index) * 0.3))\n", "#     sns.heatmap(\n", "#         foo.iloc[:, i * exps_per_fig : (i + 1) * exps_per_fig],\n", "#         cmap=[cmap[5], cmap[0], cmap[-1]],\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.set_index(['experiment']).loc['RagFIMAV17B+ScopeBM25+Stub+Top1', :].sort_values('patch_id')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Case SxS"]}, {"cell_type": "code", "execution_count": 439, "metadata": {}, "outputs": [], "source": ["# Utilities.\n", "def print_prefix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\n", "        \"\\n\".join(left_rst[\"patch_rst\"][\"prefix\"].splitlines()[-num_line:]), flush=True\n", "    )\n", "\n", "\n", "def generation_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"completion\"],\n", "                right_rst[\"completion\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        ),\n", "    )\n", "\n", "\n", "def print_suffix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\n", "        \"\\n\".join(left_rst[\"patch_rst\"][\"suffix\"].splitlines()[:num_line]), flush=True\n", "    )\n", "\n", "\n", "def prompt_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"prompt\"],\n", "                right_rst[\"prompt\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def generation_vs_ground_truth(pid: str, results):\n", "    rst = results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                rst[\"ground_truth\"],\n", "                rst[\"generation\"],\n", "                fromdesc=\"Ground Truth\",\n", "                todesc=f\"{select_right_experiment.value} ({rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def best_matched_sxs(pid: str, feature=\"ground_truth\"):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display_num_lines = 5\n", "\n", "    left_best_matched, left_score = vz_utils.get_best_match(\n", "        query=left_rst[feature],\n", "        corpus=left_rst[\"prompt\"],\n", "    )\n", "    splits = left_rst[\"prompt\"].split(left_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    lpre, lsuf = splits\n", "    lpre = \"\\n\".join(lpre.splitlines()[-display_num_lines:])\n", "    lsuf = \"\\n\".join(lsuf.splitlines()[:display_num_lines])\n", "\n", "    right_best_matched, right_score = vz_utils.get_best_match(\n", "        query=right_rst[feature],\n", "        corpus=right_rst[\"prompt\"],\n", "    )\n", "    splits = right_rst[\"prompt\"].split(right_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    rpre, rsuf = splits\n", "    rpre = \"\\n\".join(rpre.splitlines()[-display_num_lines:])\n", "    rsuf = \"\\n\".join(rsuf.splitlines()[:display_num_lines])\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lpre,\n", "                rpre,\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_best_matched,\n", "                right_best_matched,\n", "                fromdesc=f\"match score: {left_score}\",\n", "                todesc=f\"match score: {right_score}\",\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lsuf,\n", "                rsuf,\n", "            )\n", "        )\n", "    )\n", "\n", "    return left_best_matched, right_best_matched"]}, {"cell_type": "code", "execution_count": 420, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "adcaeeeb8fa54553800bf6d086ae2280", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Left Patch ID:', options=('EmptyCompletion', 'GoldCompletion', 'RagFIMAV17B+NoRetrieval'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "269871147d3d4c7aa7ef0c63d4dc0479", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Right Patch ID:', options=('EmptyCompletion', 'GoldCompletion', 'RagFIMAV17B+NoRetrieval…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Step 1: Select what you want to compare.\n", "select_left_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Left Patch ID:\",\n", "    disabled=False,\n", ")\n", "select_right_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Right Patch ID:\",\n", "    disabled=False,\n", ")\n", "clear_output(wait=True)\n", "display(select_left_experiment, select_right_experiment)"]}, {"cell_type": "code", "execution_count": 430, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "1\n", "left GoldCompletion right RagFIMAV17B+OracleTop1\n"]}], "source": ["# Step 2: Load results for left and right.\n", "import json\n", "from research.eval.patch_lib import Patch\n", "\n", "left_id = select_left_experiment.value\n", "left_results = vz_utils.load(EXPERIMENTS[select_left_experiment.value])\n", "for pid in left_results:\n", "    # Normalize to patch object.\n", "    cache = left_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        left_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "right_id = select_right_experiment.value\n", "right_results = vz_utils.load(EXPERIMENTS[select_right_experiment.value])\n", "for pid in right_results:\n", "    # Normalize to patch object.\n", "    cache = right_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        right_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)"]}, {"cell_type": "code", "execution_count": 431, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["79\n"]}], "source": ["want_pids = {\n", "    # 'EmptyCompletion': 'FAILED',\n", "    # 'StarCoderBase+NoRetrieval': 'FAILED',\n", "    'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    # 'RagFIMAV17B+Top1': 'PASSED',\n", "    # 'RagFIMAV17B+ScopeBM25+Stub+Top1': 'FAILED',\n", "    # 'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    'RagFIMAV17B+OracleTop1': 'FAILED',\n", "    # 'RagFIMAV17B+OracleTop2': 'PASSED',\n", "    # 'RagFIMAV17B+OracleTop10': 'PASSED',\n", "}\n", "\n", "\n", "pid_list = set(df['patch_id'])\n", "for exp, pf in want_pids.items():\n", "    _ids = set(df.loc[(df['experiment'] == exp) & (df['pass/fail'] == pf)]['patch_id'])\n", "    pid_list = pid_list & _ids\n", "pid_list = list(sorted(pid_list))\n", "print(len(pid_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gold_patch = {}\n", "for pid in pid_list:\n", "    chunk = results[pid]['patch_rst']['filtered_chunks'][0]\n", "    gold_patch[pid] = chunk['id']\n", "print(len(gold_patch))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = vz_utils.load(EXPERIMENTS['RagFIMAV17B+Top1'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["foo = results[pid_list[0]]['patch_rst']['filtered_chunks']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = 0\n", "for pid in pid_list:\n", "    for idx, c in enumerate(results[pid]['patch_rst']['filtered_chunks'][:20]):\n", "        if c['id'] == gold_patch[pid]:\n", "            break\n", "    score += 1.0 / (idx+1)\n", "score /= len(pid_list)\n", "score\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(foo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Option 1: Select one pid and display."]}, {"cell_type": "code", "execution_count": 407, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5685d64276354b6cbf6fbd6f711b4dfa", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Left Patch ID:', options=('amazon-science/patchcore-inspection/3d7c20bcb10c9362', 'amazo…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["select_patch_id = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description='Left Patch ID:',\n", "    disabled=False,\n", ")\n", "display(select_patch_id)"]}, {"cell_type": "code", "execution_count": 414, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import numpy as np\n", "import torch.utils.data\n", "from torchvision import models\n", "\n", "import patchcore\n", "from patchcore import patchcore as patchcore_model\n", "\n", "\n", "def _dummy_features(number_of_examples, shape_of_examples):\n", "    return torch.Tensor(\n", "        np.stack(number_of_examples * [np.ones(shape_of_examples)], axis=0)\n", "    )\n", "\n", "\n", "def _dummy_constant_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_various_features(number_of_examples, shape_of_examples):\n", "    images = torch.ones((number_of_examples, *shape_of_examples))\n", "    multiplier = torch.arange(number_of_examples) / float(number_of_examples)\n", "    for _ in range(images.ndim - 1):\n", "        multiplier = multiplier.unsqueeze(-1)\n", "    return multiplier * images\n", "\n", "\n", "def _dummy_various_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_various_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_images(number_of_examples, image_shape):\n", "    torch.manual_seed(0)\n", "    return torch.rand([number_of_examples, *image_shape])\n", "\n", "\n", "def _dummy_image_random_dataloader(number_of_examples, image_shape):\n", "    images = _dummy_images(number_of_examples, image_shape)\n", "    return torch.utils.data.DataLoader(images, batch_size=4)\n", "\n", "\n", "def _standard_patchcore(image_dimension):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    backbone = models.wide_resnet50_2(pretrained=False)\n", "    backbone.name, backbone.seed = \"wideresnet50\", 0\n", "    patchcore_instance.load(\n", "        backbone=backbone,\n", "        layers_to_extract_from=[\"layer2\", \"layer3\"],\n", "        device=torch.device(\"cpu\"),\n", "        input_shape=[3, image_dimension, image_dimension],\n", "        pretrain_embed_dimension=1024,\n", "        target_embed_dimension=1024,\n", "        patchsize=3,\n", "        patchstride=1,\n", "        spade_nn=2,\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _load_patchcore_from_path(load_path):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    patchcore_instance.load_from_path(\n", "        load_path=load_path,\n", "        device=torch.device(\"cpu\"),\n", "        prepend=\"temp_patchcore\",\n", "        nn_method=patchcore.common.FaissNN(False, 4),\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _approximate_greedycoreset_sampler_with_reduction(\n", "    sampling_percentage, joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim\n", "):\n", "    return patchcore.sampler.ApproximateGreedyCoresetSampler(\n", "        percentage=sampling_percentage,\n", "        device=torch.device(\"cpu\"),\n", "        number_of_starting_points=10,\n", "        dimension_to_project_features_to=joh<PERSON><PERSON><PERSON><PERSON>uss_dim,\n", "    )\n", "\n", "\n", "def test_dummy_patchcore():\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    print(model.featuresampler)\n", "    model.fit(training_dataloader)\n", "\n", "    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\n", "    scores, masks = model.predict(test_features)\n", "\n", "    assert all([score > 0 for score in scores])\n", "    for mask in masks:\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_on_dataloader():\n", "    \"\"\"Test PatchCore on dataloader and assure training scores are zero.\"\"\"\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    scores, masks, labels_gt, masks_gt = model.predict(training_dataloader)\n", "\n", "    assert all([score < 1e-3 for score in scores])\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_load_and_saveing(tmpdir):\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    model.save_to_path(tmpdir, \"temp_patchcore\")\n", "\n", "    test_features = torch.Tensor(\n", "        1.234 * np.ones([2, 3, image_dimension, image_dimension])\n", "    )\n", "    scores, masks = model.predict(test_features)\n", "    other_scores, other_masks = model.predict(test_features)\n", "\n", "    assert np.all(scores == other_scores)\n", "    for mask, other_mask in zip(masks, other_masks):\n", "        assert np.all(mask == other_mask)\n", "\n", "\n", "def test_patchcore_real_data():\n", "    image_dimension = 112\n", "    sampling_percentage = 0.1\n", "    model = _standard_patchcore(image_dimension)\n", "    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\n", "        sampling_percentage=sampling_percentage,\n", "        joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim=64,\n", "    )\n", "\n", "    num_dummy_train_images = 50\n", "    training_dataloader = _dummy_various_dataloader(\n", "        num_dummy_train_images, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "\n", "    num_dummy_test_images = 5\n", "    test_dataloader = _dummy_various_dataloader(\n", "        num_dummy_test_images, [3, image_dimension, image_dimension]\n", "    )\n", "    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\n", "\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "    assert len(scores) == 5\n", "\n"]}], "source": ["rst = left_results[pid]\n", "print(rst['patch_rst']['patch'].file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 410, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["amazon-science/patchcore-inspection/3d7c20bcb10c9362\n", "File Path: test/test_patchcore.py\n", "Retrieved File Path: test/test_sampler.py\n", "left pydiff2m_7b+BM25Body+DefLookupALL+Top0-10 right RagFIMAV17B+OracleTop1\n", "PREFIX\n", "        patchstride=1,\n", "        spade_nn=2,\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _load_patchcore_from_path(load_path):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    patchcore_instance.load_from_path(\n", "        load_path=load_path,\n", "        device=torch.device(\"cpu\"),\n", "        prepend=\"temp_patchcore\",\n", "        nn_method=patchcore.common.FaissNN(False, 4),\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _approximate_greedycoreset_sampler_with_reduction(\n", "    sampling_percentage, joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim\n", "):\n", "Generation Side by Side\n"]}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to120__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">pydiff2m_7b+BM25Body+DefLookupALL+Top0-10 (FAILED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">RagFIMAV17B+OracleTop1 (PASSED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to120__0\"><a href=\"#difflib_chg_to120__0\">f</a></td><td class=\"diff_header\" id=\"from120_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore.sampler.ApproximateGreedyCoresetSampler(</td><td class=\"diff_next\"><a href=\"#difflib_chg_to120__0\">f</a></td><td class=\"diff_header\" id=\"to120_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore.sampler.ApproximateGreedyCoresetSampler(</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to120__top\">t</a></td><td class=\"diff_header\" id=\"from120_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_sub\">sampling_</span>percentage=sampling_percentage,</td><td class=\"diff_next\"><a href=\"#difflib_chg_to120__top\">t</a></td><td class=\"diff_header\" id=\"to120_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;percentage=sampling_percentage,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to120_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</span></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to121__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">Ground Truth</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">RagFIMAV17B+OracleTop1 (PASSED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to121__top\">t</a></td><td class=\"diff_header\" id=\"from121_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore.sampler.ApproximateGreedyCoresetSampler(</td><td class=\"diff_next\"><a href=\"#difflib_chg_to121__top\">t</a></td><td class=\"diff_header\" id=\"to121_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore.sampler.ApproximateGreedyCoresetSampler(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from121_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;percentage=sampling_percentage,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to121_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;percentage=sampling_percentage,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from121_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to121_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["SUFFIX\n", "\n", "        number_of_starting_points=10,\n", "        dimension_to_project_features_to=joh<PERSON><PERSON><PERSON><PERSON>uss_dim,\n", "    )\n", "\n", "\n", "def test_dummy_patchcore():\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    print(model.featuresampler)\n", "    model.fit(training_dataloader)\n", "\n", "    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\n", "    scores, masks = model.predict(test_features)\n", "\n", "    assert all([score > 0 for score in scores])\n", "    for mask in masks:\n", "Prompt Side by Side\n"]}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to122__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">pydiff2m_7b+BM25Body+DefLookupALL+Top0-10 (FAILED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">RagFIMAV17B+OracleTop1 (PASSED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to122__0\"><a href=\"#difflib_chg_to122__1\">n</a></td><td class=\"diff_header\" id=\"from122_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|retrieval_section|&gt;&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/common.py&lt;|ret-body|&gt;class&nbsp;NearestNeighbourScorer(object):</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to122__1\">n</a></td><td class=\"diff_header\" id=\"to122_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&lt;filename&gt;test/test_sampler.py</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;load(self,&nbsp;load_folder:&nbsp;str,&nbsp;prepend:&nbsp;str&nbsp;=&nbsp;\"\")&nbsp;-&gt;&nbsp;None:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.nn_method.load(self._index_file(load_folder,&nbsp;prepend))</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;os.path.exists(self._detection_file(load_folder,&nbsp;prepend)):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.detection_features&nbsp;=&nbsp;self._load(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self._detection_file(load_folder,&nbsp;prepend)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/patchcore.py&lt;|ret-body|&gt;\"\"\"PatchCore&nbsp;and&nbsp;PatchCore&nbsp;detection&nbsp;methods.\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">class&nbsp;PatchCore(torch.nn.Module):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to122__1\"></td><td class=\"diff_header\" id=\"from122_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;predict(self,&nbsp;data):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;isinstance(data,&nbsp;torch.utils.data.DataLoader):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;self._predict_dataloader(data)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;self._predict(data)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_2\">2</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to122__2\">n</a></td><td class=\"diff_header\" id=\"from122_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/patchcore.py&lt;|ret-body|&gt;\"\"\"PatchCore&nbsp;and&nbsp;PatchCore&nbsp;detection&nbsp;methods.\"\"\"</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to122__2\">n</a></td><td class=\"diff_header\" id=\"to122_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage&nbsp;=&nbsp;0.1</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">class&nbsp;PatchCore(torch.nn.Module):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;dimension_to_project_features_to&nbsp;=&nbsp;64</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;load(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_18\">18</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;backbone,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;layers_to_extract_from,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_shape,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pretrain_embed_dimension,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;target_embed_dimension,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchsize=3,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchstride=1,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;anomaly_score_num_nn=1,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;featuresampler=patchcore.sampler.IdentitySampler(),</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nn_method=patchcore.common.FaissNN(False,&nbsp;4),</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**kwargs,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to122__2\"></td><td class=\"diff_header\" id=\"from122_31\">31</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.backbone&nbsp;=&nbsp;backbone.to(device)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.layers_to_extract_from&nbsp;=&nbsp;layers_to_extract_from</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.input_shape&nbsp;=&nbsp;input_shape</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_35\">35</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to122__3\">n</a></td><td class=\"diff_header\" id=\"from122_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.device&nbsp;=&nbsp;device</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to122__3\">n</a></td><td class=\"diff_header\" id=\"to122_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;sampler.ApproximateGreedyCoresetSampler(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.patch_maker&nbsp;=&nbsp;PatchMaker(patchsize,&nbsp;stride=patchstride)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;percentage=sampling_percentage,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to122__3\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;number_of_starting_points=10,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dimension_to_project_features_to=dimension_to_project_features_to,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;subsampled_features&nbsp;=&nbsp;model.run(init_features)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_38\">38</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_13\">13</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to122__top\">t</a></td><td class=\"diff_header\" id=\"from122_39\">39</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.forward_modules&nbsp;=&nbsp;torch.nn.ModuleDict({})</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to122__top\">t</a></td><td class=\"diff_header\" id=\"to122_14\">14</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;subsampled_features.shape[-1]&nbsp;==&nbsp;feature_dimension</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_40\">40</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&lt;fim_prefix&gt;import&nbsp;numpy&nbsp;as&nbsp;np</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;feature_aggregator&nbsp;=&nbsp;patchcore.common.NetworkFeatureAggregator(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_42\">42</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.backbone,&nbsp;self.layers_to_extract_from,&nbsp;self.device</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_43\">43</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_44\">44</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;feature_dimensions&nbsp;=&nbsp;feature_aggregator.feature_dimensions(input_shape)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_45\">45</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.forward_modules[\"feature_aggregator\"]&nbsp;=&nbsp;feature_aggregator</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_46\">46</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_47\">47</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;preprocessing&nbsp;=&nbsp;patchcore.common.Preprocessing(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_48\">48</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;feature_dimensions,&nbsp;pretrain_embed_dimension</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_49\">49</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_50\">50</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.forward_modules[\"preprocessing\"]&nbsp;=&nbsp;preprocessing</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_51\">51</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_52\">52</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.target_embed_dimension&nbsp;=&nbsp;target_embed_dimension</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_53\">53</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;preadapt_aggregator&nbsp;=&nbsp;patchcore.common.Aggregator(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_54\">54</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;target_dim=target_embed_dimension</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_55\">55</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_56\">56</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_57\">57</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;_&nbsp;=&nbsp;preadapt_aggregator.to(self.device)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_58\">58</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_59\">59</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.forward_modules[\"preadapt_aggregator\"]&nbsp;=&nbsp;preadapt_aggregator</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_60\">60</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_61\">61</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.anomaly_scorer&nbsp;=&nbsp;patchcore.common.NearestNeighbourScorer(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_62\">62</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;n_nearest_neighbours=anomaly_score_num_nn,&nbsp;nn_method=nn_method</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_63\">63</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_64\">64</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_65\">65</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.anomaly_segmentor&nbsp;=&nbsp;patchcore.common.RescaleSegmentor(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_66\">66</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=self.device,&nbsp;target_size=input_shape[-2:]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_67\">67</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_68\">68</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_69\">69</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.featuresampler&nbsp;=&nbsp;featuresampler</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_70\">70</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_71\">71</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/common.py&lt;|ret-body|&gt;class&nbsp;NearestNeighbourScorer(object):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_72\">72</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;predict(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_73\">73</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,&nbsp;query_features:&nbsp;List[np.ndarray]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_74\">74</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;-&gt;&nbsp;Union[np.ndarray,&nbsp;np.ndarray,&nbsp;np.ndarray]:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_75\">75</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"Predicts&nbsp;anomaly&nbsp;score.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_76\">76</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_77\">77</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches&nbsp;for&nbsp;nearest&nbsp;neighbours&nbsp;of&nbsp;test&nbsp;images&nbsp;in&nbsp;all</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_78\">78</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;support&nbsp;training&nbsp;images.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_79\">79</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_80\">80</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Args:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_81\">81</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;detection_query_features:&nbsp;[dict&nbsp;of&nbsp;np.arrays]&nbsp;List&nbsp;of&nbsp;np.arrays</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_82\">82</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;corresponding&nbsp;to&nbsp;the&nbsp;test&nbsp;features&nbsp;generated&nbsp;by</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_83\">83</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;some&nbsp;backbone&nbsp;network.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_84\">84</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_85\">85</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;query_features&nbsp;=&nbsp;self.feature_merger.merge(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_86\">86</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;query_features,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_87\">87</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_88\">88</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;query_distances,&nbsp;query_nns&nbsp;=&nbsp;self.imagelevel_nn(query_features)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_89\">89</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;anomaly_scores&nbsp;=&nbsp;np.mean(query_distances,&nbsp;axis=-1)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_90\">90</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;anomaly_scores,&nbsp;query_distances,&nbsp;query_nns</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_91\">91</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_92\">92</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/patchcore.py&lt;|ret-body|&gt;\"\"\"PatchCore&nbsp;and&nbsp;PatchCore&nbsp;detection&nbsp;methods.\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_93\">93</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">class&nbsp;PatchCore(torch.nn.Module):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_94\">94</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;fit(self,&nbsp;training_data):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_95\">95</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"PatchCore&nbsp;training.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_96\">96</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_97\">97</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This&nbsp;function&nbsp;computes&nbsp;the&nbsp;embeddings&nbsp;of&nbsp;the&nbsp;training&nbsp;data&nbsp;and&nbsp;fills&nbsp;the</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_98\">98</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;memory&nbsp;bank&nbsp;of&nbsp;SPADE.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_99\">99</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_100\">100</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self._fill_memory_bank(training_data)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_101\">101</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_102\">102</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/common.py&lt;|ret-body|&gt;class&nbsp;FaissNN(object):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_103\">103</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;fit(self,&nbsp;features:&nbsp;np.ndarray)&nbsp;-&gt;&nbsp;None:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_104\">104</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_105\">105</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds&nbsp;features&nbsp;to&nbsp;the&nbsp;FAISS&nbsp;search&nbsp;index.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_106\">106</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_107\">107</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Args:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_108\">108</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;features:&nbsp;Array&nbsp;of&nbsp;size&nbsp;NxD.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_109\">109</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_110\">110</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.search_index:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_111\">111</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.reset_index()</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_112\">112</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.search_index&nbsp;=&nbsp;self._create_index(features.shape[-1])</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_113\">113</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self._train(self.search_index,&nbsp;features)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_114\">114</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.search_index.add(features)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_115\">115</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_116\">116</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_117\">117</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_118\">118</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/common.py&lt;|ret-body|&gt;class&nbsp;NearestNeighbourScorer(object):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_119\">119</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;fit(self,&nbsp;detection_features:&nbsp;List[np.ndarray])&nbsp;-&gt;&nbsp;None:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_120\">120</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"Calls&nbsp;the&nbsp;fit&nbsp;function&nbsp;of&nbsp;the&nbsp;nearest&nbsp;neighbour&nbsp;method.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_121\">121</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_122\">122</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Args:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_123\">123</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;detection_features:&nbsp;[list&nbsp;of&nbsp;np.arrays]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_124\">124</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[[bs&nbsp;x&nbsp;d_i]&nbsp;for&nbsp;i&nbsp;in&nbsp;n]&nbsp;Contains&nbsp;a&nbsp;list&nbsp;of</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_125\">125</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;np.arrays&nbsp;for&nbsp;all&nbsp;training&nbsp;images&nbsp;corresponding&nbsp;to&nbsp;respective</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_126\">126</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;features&nbsp;VECTORS&nbsp;(or&nbsp;maps,&nbsp;but&nbsp;will&nbsp;be&nbsp;resized)&nbsp;produced&nbsp;by</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_127\">127</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;some&nbsp;backbone&nbsp;network&nbsp;which&nbsp;should&nbsp;be&nbsp;used&nbsp;for&nbsp;image-level</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_128\">128</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;anomaly&nbsp;detection.</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_129\">129</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_130\">130</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.detection_features&nbsp;=&nbsp;self.feature_merger.merge(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_131\">131</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;detection_features,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_132\">132</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_133\">133</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.nn_method.fit(self.detection_features)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_134\">134</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_135\">135</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;|ret-start|&gt;&lt;filename&gt;src/patchcore/patchcore.py&lt;|ret-body|&gt;\"\"\"PatchCore&nbsp;and&nbsp;PatchCore&nbsp;detection&nbsp;methods.\"\"\"</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_136\">136</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">class&nbsp;PatchCore(torch.nn.Module):</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_137\">137</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;load_from_path(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_138\">138</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_139\">139</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load_path:&nbsp;str,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_140\">140</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device:&nbsp;torch.device,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_141\">141</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nn_method:&nbsp;patchcore.common.FaissNN(False,&nbsp;4),</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_142\">142</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;prepend:&nbsp;str&nbsp;=&nbsp;\"\",</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_143\">143</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;-&gt;&nbsp;None:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_144\">144</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;LOGGER.info(\"Loading&nbsp;and&nbsp;initializing&nbsp;PatchCore.\")</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_145\">145</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;with&nbsp;open(self._params_file(load_path,&nbsp;prepend),&nbsp;\"rb\")&nbsp;as&nbsp;load_file:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_146\">146</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchcore_params&nbsp;=&nbsp;pickle.load(load_file)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_147\">147</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchcore_params[\"backbone\"]&nbsp;=&nbsp;patchcore.backbones.load(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_148\">148</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchcore_params[\"backbone.name\"]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_149\">149</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_150\">150</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchcore_params[\"backbone\"].name&nbsp;=&nbsp;patchcore_params[\"backbone.name\"]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_151\">151</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;del&nbsp;patchcore_params[\"backbone.name\"]</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_152\">152</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.load(**patchcore_params,&nbsp;device=device,&nbsp;nn_method=nn_method)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_153\">153</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_154\">154</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.anomaly_scorer.load(load_path,&nbsp;prepend)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_155\">155</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&lt;fim_prefix&gt;&lt;filename&gt;test/test_patchcore.py&lt;|prefix-body|&gt;import&nbsp;numpy&nbsp;as&nbsp;np</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_156\">156</td><td nowrap=\"nowrap\">import&nbsp;torch.utils.data</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_16\">16</td><td nowrap=\"nowrap\">import&nbsp;torch.utils.data</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_157\">157</td><td nowrap=\"nowrap\">from&nbsp;torchvision&nbsp;import&nbsp;models</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_17\">17</td><td nowrap=\"nowrap\">from&nbsp;torchvision&nbsp;import&nbsp;models</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_158\">158</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_18\">18</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_159\">159</td><td nowrap=\"nowrap\">import&nbsp;patchcore</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_19\">19</td><td nowrap=\"nowrap\">import&nbsp;patchcore</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_160\">160</td><td nowrap=\"nowrap\">from&nbsp;patchcore&nbsp;import&nbsp;patchcore&nbsp;as&nbsp;patchcore_model</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_20\">20</td><td nowrap=\"nowrap\">from&nbsp;patchcore&nbsp;import&nbsp;patchcore&nbsp;as&nbsp;patchcore_model</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_161\">161</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_21\">21</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_162\">162</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_163\">163</td><td nowrap=\"nowrap\">def&nbsp;_dummy_features(number_of_examples,&nbsp;shape_of_examples):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_23\">23</td><td nowrap=\"nowrap\">def&nbsp;_dummy_features(number_of_examples,&nbsp;shape_of_examples):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_164\">164</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.Tensor(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.Tensor(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_165\">165</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;np.stack(number_of_examples&nbsp;*&nbsp;[np.ones(shape_of_examples)],&nbsp;axis=0)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;np.stack(number_of_examples&nbsp;*&nbsp;[np.ones(shape_of_examples)],&nbsp;axis=0)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_166\">166</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_167\">167</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_27\">27</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_168\">168</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_28\">28</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_169\">169</td><td nowrap=\"nowrap\">def&nbsp;_dummy_constant_dataloader(number_of_examples,&nbsp;shape_of_examples):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_29\">29</td><td nowrap=\"nowrap\">def&nbsp;_dummy_constant_dataloader(number_of_examples,&nbsp;shape_of_examples):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_170\">170</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;features&nbsp;=&nbsp;_dummy_features(number_of_examples,&nbsp;shape_of_examples)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;features&nbsp;=&nbsp;_dummy_features(number_of_examples,&nbsp;shape_of_examples)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_171\">171</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(features,&nbsp;batch_size=1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(features,&nbsp;batch_size=1)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_172\">172</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_32\">32</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_173\">173</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_33\">33</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_174\">174</td><td nowrap=\"nowrap\">def&nbsp;_dummy_various_features(number_of_examples,&nbsp;shape_of_examples):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_34\">34</td><td nowrap=\"nowrap\">def&nbsp;_dummy_various_features(number_of_examples,&nbsp;shape_of_examples):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_175\">175</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;images&nbsp;=&nbsp;torch.ones((number_of_examples,&nbsp;*shape_of_examples))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;images&nbsp;=&nbsp;torch.ones((number_of_examples,&nbsp;*shape_of_examples))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_176\">176</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;multiplier&nbsp;=&nbsp;torch.arange(number_of_examples)&nbsp;/&nbsp;float(number_of_examples)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_36\">36</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;multiplier&nbsp;=&nbsp;torch.arange(number_of_examples)&nbsp;/&nbsp;float(number_of_examples)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_177\">177</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;_&nbsp;in&nbsp;range(images.ndim&nbsp;-&nbsp;1):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;_&nbsp;in&nbsp;range(images.ndim&nbsp;-&nbsp;1):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_178\">178</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;multiplier&nbsp;=&nbsp;multiplier.unsqueeze(-1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;multiplier&nbsp;=&nbsp;multiplier.unsqueeze(-1)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_179\">179</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;multiplier&nbsp;*&nbsp;images</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;multiplier&nbsp;*&nbsp;images</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_180\">180</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_40\">40</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_181\">181</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_41\">41</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_182\">182</td><td nowrap=\"nowrap\">def&nbsp;_dummy_various_dataloader(number_of_examples,&nbsp;shape_of_examples):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_42\">42</td><td nowrap=\"nowrap\">def&nbsp;_dummy_various_dataloader(number_of_examples,&nbsp;shape_of_examples):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_183\">183</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;features&nbsp;=&nbsp;_dummy_various_features(number_of_examples,&nbsp;shape_of_examples)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;features&nbsp;=&nbsp;_dummy_various_features(number_of_examples,&nbsp;shape_of_examples)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_184\">184</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(features,&nbsp;batch_size=1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(features,&nbsp;batch_size=1)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_185\">185</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_45\">45</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_186\">186</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_46\">46</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_187\">187</td><td nowrap=\"nowrap\">def&nbsp;_dummy_images(number_of_examples,&nbsp;image_shape):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_47\">47</td><td nowrap=\"nowrap\">def&nbsp;_dummy_images(number_of_examples,&nbsp;image_shape):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_188\">188</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;torch.manual_seed(0)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;torch.manual_seed(0)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_189\">189</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.rand([number_of_examples,&nbsp;*image_shape])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.rand([number_of_examples,&nbsp;*image_shape])</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_190\">190</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_50\">50</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_191\">191</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_51\">51</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_192\">192</td><td nowrap=\"nowrap\">def&nbsp;_dummy_image_random_dataloader(number_of_examples,&nbsp;image_shape):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_52\">52</td><td nowrap=\"nowrap\">def&nbsp;_dummy_image_random_dataloader(number_of_examples,&nbsp;image_shape):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_193\">193</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;images&nbsp;=&nbsp;_dummy_images(number_of_examples,&nbsp;image_shape)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_53\">53</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;images&nbsp;=&nbsp;_dummy_images(number_of_examples,&nbsp;image_shape)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_194\">194</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(images,&nbsp;batch_size=4)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_54\">54</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;torch.utils.data.DataLoader(images,&nbsp;batch_size=4)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_195\">195</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_55\">55</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_196\">196</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_56\">56</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_197\">197</td><td nowrap=\"nowrap\">def&nbsp;_standard_patchcore(image_dimension):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_57\">57</td><td nowrap=\"nowrap\">def&nbsp;_standard_patchcore(image_dimension):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_198\">198</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance&nbsp;=&nbsp;patchcore_model.PatchCore(torch.device(\"cpu\"))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_58\">58</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance&nbsp;=&nbsp;patchcore_model.PatchCore(torch.device(\"cpu\"))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_199\">199</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;backbone&nbsp;=&nbsp;models.wide_resnet50_2(pretrained=False)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_59\">59</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;backbone&nbsp;=&nbsp;models.wide_resnet50_2(pretrained=False)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_200\">200</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;backbone.name,&nbsp;backbone.seed&nbsp;=&nbsp;\"wideresnet50\",&nbsp;0</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_60\">60</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;backbone.name,&nbsp;backbone.seed&nbsp;=&nbsp;\"wideresnet50\",&nbsp;0</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_201\">201</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance.load(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_61\">61</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance.load(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_202\">202</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;backbone=backbone,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_62\">62</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;backbone=backbone,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_203\">203</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;layers_to_extract_from=[\"layer2\",&nbsp;\"layer3\"],</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_63\">63</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;layers_to_extract_from=[\"layer2\",&nbsp;\"layer3\"],</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_204\">204</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_64\">64</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_205\">205</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_shape=[3,&nbsp;image_dimension,&nbsp;image_dimension],</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_65\">65</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_shape=[3,&nbsp;image_dimension,&nbsp;image_dimension],</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_206\">206</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pretrain_embed_dimension=1024,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_66\">66</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pretrain_embed_dimension=1024,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_207\">207</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;target_embed_dimension=1024,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_67\">67</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;target_embed_dimension=1024,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_208\">208</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchsize=3,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_68\">68</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchsize=3,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_209\">209</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchstride=1,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_69\">69</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;patchstride=1,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_210\">210</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;spade_nn=2,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_70\">70</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;spade_nn=2,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_211\">211</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_71\">71</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_212\">212</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore_instance</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_72\">72</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore_instance</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_213\">213</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_73\">73</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_214\">214</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_74\">74</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_215\">215</td><td nowrap=\"nowrap\">def&nbsp;_load_patchcore_from_path(load_path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_75\">75</td><td nowrap=\"nowrap\">def&nbsp;_load_patchcore_from_path(load_path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_216\">216</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance&nbsp;=&nbsp;patchcore_model.PatchCore(torch.device(\"cpu\"))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_76\">76</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance&nbsp;=&nbsp;patchcore_model.PatchCore(torch.device(\"cpu\"))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_217\">217</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance.load_from_path(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_77\">77</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;patchcore_instance.load_from_path(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_218\">218</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load_path=load_path,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_78\">78</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load_path=load_path,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_219\">219</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_79\">79</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;device=torch.device(\"cpu\"),</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_220\">220</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;prepend=\"temp_patchcore\",</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_80\">80</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;prepend=\"temp_patchcore\",</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_221\">221</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nn_method=patchcore.common.FaissNN(False,&nbsp;4),</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_81\">81</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nn_method=patchcore.common.FaissNN(False,&nbsp;4),</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_222\">222</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_82\">82</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_223\">223</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore_instance</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_83\">83</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;patchcore_instance</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_224\">224</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_84\">84</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_225\">225</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_85\">85</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_226\">226</td><td nowrap=\"nowrap\">def&nbsp;_approximate_greedycoreset_sampler_with_reduction(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_86\">86</td><td nowrap=\"nowrap\">def&nbsp;_approximate_greedycoreset_sampler_with_reduction(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_227\">227</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage,&nbsp;joh<PERSON><PERSON><PERSON>trauss_dim</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_87\">87</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage,&nbsp;johnsonlindenstrauss_dim</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_228\">228</td><td nowrap=\"nowrap\">):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_88\">88</td><td nowrap=\"nowrap\">):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_229\">229</td><td nowrap=\"nowrap\">&lt;fim_suffix&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_89\">89</td><td nowrap=\"nowrap\">&lt;fim_suffix&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_230\">230</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;number_of_starting_points=10,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_90\">90</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;number_of_starting_points=10,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_231\">231</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dimension_to_project_features_to=johnsonlindenstrauss_dim,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_91\">91</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dimension_to_project_features_to=johnsonlindenstrauss_dim,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_232\">232</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_92\">92</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_233\">233</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_93\">93</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_234\">234</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_94\">94</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_235\">235</td><td nowrap=\"nowrap\">def&nbsp;test_dummy_patchcore():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_95\">95</td><td nowrap=\"nowrap\">def&nbsp;test_dummy_patchcore():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_236\">236</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_96\">96</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_237\">237</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_97\">97</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_238\">238</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_98\">98</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_239\">239</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_99\">99</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_240\">240</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_100\">100</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_241\">241</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(model.featuresampler)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_101\">101</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(model.featuresampler)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_242\">242</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_102\">102</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_243\">243</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_103\">103</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_244\">244</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_features&nbsp;=&nbsp;torch.Tensor(2&nbsp;*&nbsp;np.ones([2,&nbsp;3,&nbsp;image_dimension,&nbsp;image_dimension]))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_104\">104</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_features&nbsp;=&nbsp;torch.Tensor(2&nbsp;*&nbsp;np.ones([2,&nbsp;3,&nbsp;image_dimension,&nbsp;image_dimension]))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_245\">245</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks&nbsp;=&nbsp;model.predict(test_features)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_105\">105</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks&nbsp;=&nbsp;model.predict(test_features)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_246\">246</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_106\">106</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_247\">247</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;all([score&nbsp;&gt;&nbsp;0&nbsp;for&nbsp;score&nbsp;in&nbsp;scores])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_107\">107</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;all([score&nbsp;&gt;&nbsp;0&nbsp;for&nbsp;score&nbsp;in&nbsp;scores])</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_248\">248</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask&nbsp;in&nbsp;masks:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_108\">108</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask&nbsp;in&nbsp;masks:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_249\">249</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_109\">109</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_250\">250</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_110\">110</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_251\">251</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_111\">111</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_252\">252</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_on_dataloader():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_112\">112</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_on_dataloader():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_253\">253</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"Test&nbsp;PatchCore&nbsp;on&nbsp;dataloader&nbsp;and&nbsp;assure&nbsp;training&nbsp;scores&nbsp;are&nbsp;zero.\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_113\">113</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"Test&nbsp;PatchCore&nbsp;on&nbsp;dataloader&nbsp;and&nbsp;assure&nbsp;training&nbsp;scores&nbsp;are&nbsp;zero.\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_254\">254</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_114\">114</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_255\">255</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_115\">115</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_256\">256</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_116\">116</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_257\">257</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_117\">117</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_258\">258</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_118\">118</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_259\">259</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_119\">119</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_260\">260</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_120\">120</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_261\">261</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks,&nbsp;labels_gt,&nbsp;masks_gt&nbsp;=&nbsp;model.predict(training_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_121\">121</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks,&nbsp;labels_gt,&nbsp;masks_gt&nbsp;=&nbsp;model.predict(training_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_262\">262</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_122\">122</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_263\">263</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;all([score&nbsp;&lt;&nbsp;1e-3&nbsp;for&nbsp;score&nbsp;in&nbsp;scores])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_123\">123</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;all([score&nbsp;&lt;&nbsp;1e-3&nbsp;for&nbsp;score&nbsp;in&nbsp;scores])</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_264\">264</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;mask_gt&nbsp;in&nbsp;zip(masks,&nbsp;masks_gt):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_124\">124</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;mask_gt&nbsp;in&nbsp;zip(masks,&nbsp;masks_gt):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_265\">265</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_125\">125</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_266\">266</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask_gt.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_126\">126</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask_gt.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_267\">267</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_127\">127</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_268\">268</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_128\">128</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_269\">269</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_load_and_saveing(tmpdir):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_129\">129</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_load_and_saveing(tmpdir):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_270\">270</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_130\">130</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_271\">271</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_131\">131</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_272\">272</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_132\">132</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_273\">273</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_133\">133</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_constant_dataloader(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_274\">274</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_134\">134</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_275\">275</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_135\">135</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_276\">276</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_136\">136</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_277\">277</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.save_to_path(tmpdir,&nbsp;\"temp_patchcore\")</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_137\">137</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.save_to_path(tmpdir,&nbsp;\"temp_patchcore\")</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_278\">278</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_138\">138</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_279\">279</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_features&nbsp;=&nbsp;torch.Tensor(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_139\">139</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_features&nbsp;=&nbsp;torch.Tensor(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_280\">280</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.234&nbsp;*&nbsp;np.ones([2,&nbsp;3,&nbsp;image_dimension,&nbsp;image_dimension])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_140\">140</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.234&nbsp;*&nbsp;np.ones([2,&nbsp;3,&nbsp;image_dimension,&nbsp;image_dimension])</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_281\">281</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_141\">141</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_282\">282</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks&nbsp;=&nbsp;model.predict(test_features)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_142\">142</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks&nbsp;=&nbsp;model.predict(test_features)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_283\">283</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;other_scores,&nbsp;other_masks&nbsp;=&nbsp;model.predict(test_features)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_143\">143</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;other_scores,&nbsp;other_masks&nbsp;=&nbsp;model.predict(test_features)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_284\">284</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_144\">144</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_285\">285</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(scores&nbsp;==&nbsp;other_scores)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_145\">145</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(scores&nbsp;==&nbsp;other_scores)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_286\">286</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;other_mask&nbsp;in&nbsp;zip(masks,&nbsp;other_masks):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_146\">146</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;other_mask&nbsp;in&nbsp;zip(masks,&nbsp;other_masks):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_287\">287</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask&nbsp;==&nbsp;other_mask)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_147\">147</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask&nbsp;==&nbsp;other_mask)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_288\">288</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_148\">148</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_289\">289</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_149\">149</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_290\">290</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_real_data():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_150\">150</td><td nowrap=\"nowrap\">def&nbsp;test_patchcore_real_data():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_291\">291</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_151\">151</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;image_dimension&nbsp;=&nbsp;112</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_292\">292</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage&nbsp;=&nbsp;0.1</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_152\">152</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage&nbsp;=&nbsp;0.1</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_293\">293</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_153\">153</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model&nbsp;=&nbsp;_standard_patchcore(image_dimension)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_294\">294</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.sampler&nbsp;=&nbsp;_approximate_greedycoreset_sampler_with_reduction(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_154\">154</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.sampler&nbsp;=&nbsp;_approximate_greedycoreset_sampler_with_reduction(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_295\">295</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage=sampling_percentage,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_155\">155</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sampling_percentage=sampling_percentage,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_296\">296</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;johnsonlindenstrauss_dim=64,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_156\">156</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;johnsonlindenstrauss_dim=64,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_297\">297</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_157\">157</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_298\">298</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_158\">158</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_299\">299</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_train_images&nbsp;=&nbsp;50</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_159\">159</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_train_images&nbsp;=&nbsp;50</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_300\">300</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_various_dataloader(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_160\">160</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;training_dataloader&nbsp;=&nbsp;_dummy_various_dataloader(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_301\">301</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_train_images,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_161\">161</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_train_images,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_302\">302</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_162\">162</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_303\">303</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_163\">163</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;model.fit(training_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_304\">304</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_164\">164</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_305\">305</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_test_images&nbsp;=&nbsp;5</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_165\">165</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_test_images&nbsp;=&nbsp;5</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_306\">306</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_dataloader&nbsp;=&nbsp;_dummy_various_dataloader(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_166\">166</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;test_dataloader&nbsp;=&nbsp;_dummy_various_dataloader(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_307\">307</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_test_images,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_167\">167</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_dummy_test_images,&nbsp;[3,&nbsp;image_dimension,&nbsp;image_dimension]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_308\">308</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_168\">168</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_309\">309</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks,&nbsp;labels_gt,&nbsp;masks_gt&nbsp;=&nbsp;model.predict(test_dataloader)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_169\">169</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;scores,&nbsp;masks,&nbsp;labels_gt,&nbsp;masks_gt&nbsp;=&nbsp;model.predict(test_dataloader)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_310\">310</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_170\">170</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_311\">311</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;mask_gt&nbsp;in&nbsp;zip(masks,&nbsp;masks_gt):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_171\">171</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;mask,&nbsp;mask_gt&nbsp;in&nbsp;zip(masks,&nbsp;masks_gt):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_312\">312</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_172\">172</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_313\">313</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask_gt.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_173\">173</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;np.all(mask_gt.shape&nbsp;==&nbsp;(image_dimension,&nbsp;image_dimension))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_314\">314</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_174\">174</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_315\">315</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;len(scores)&nbsp;==&nbsp;5</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_175\">175</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;assert&nbsp;len(scores)&nbsp;==&nbsp;5</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from122_316\">316</td><td nowrap=\"nowrap\">&lt;fim_middle&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to122_176\">176</td><td nowrap=\"nowrap\">&lt;fim_middle&gt;</td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Best matched in the prompt\n"]}], "source": ["pid = select_patch_id.value\n", "\n", "clear_output(wait=True)\n", "print(pid)\n", "print(f\"File Path: {right_results[pid]['patch_rst']['patch'].file_name}\")\n", "print(f\"Retrieved File Path: {right_results[pid]['patch_rst']['filtered_chunks'][0]['parent_doc']['path']}\")\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "\n", "print('PREFIX')\n", "print_prefix_lines(pid, 20)\n", "\n", "print('Generation Side by Side')\n", "generation_sxs(pid)\n", "generation_vs_ground_truth(pid, right_results)\n", "\n", "print('SUFFIX')\n", "print_suffix_lines(pid, num_line=20)\n", "\n", "print('Prompt Side by Side')\n", "prompt_sxs(pid)\n", "print('Best matched in the prompt')\n", "# best_matched_sxs(pid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Option 2: loop over all pids and display."]}, {"cell_type": "code", "execution_count": 432, "metadata": {}, "outputs": [], "source": ["annotations = {}"]}, {"cell_type": "code", "execution_count": 433, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 433, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([{'patch_id':key, 'annotation': value} for key, value in annotations.items()])"]}, {"cell_type": "code", "execution_count": 442, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19 out of 79\n", "PATCH ID: facebookresearch/omnivore/313261f7a88579f0\n", "File Path: omnivision/optim/optimizer.py\n", "left GoldCompletion right RagFIMAV17B+OracleTop1\n", "Generation Side by Side\n", "# Copyright (c) Meta Platforms, Inc. and affiliates.\n", "# All rights reserved.\n", "\n", "# This source code is licensed under the license found in the\n", "# LICENSE file in the root directory of this source tree.\n", "\n", "# pyre-ignore-all-errors\n", "\n", "import fnmatch\n", "import itertools\n", "import logging\n", "from dataclasses import dataclass\n", "from typing import Any, Dict, Iterable, List, Optional, Set, Tuple, Union\n", "\n", "import hydra\n", "import torch\n", "import torch.nn as nn\n", "from omegaconf import DictConfig, MISSING\n", "\n", "from . import LARS, OmniOptimizer\n", "\n", "\n", "def create_lars_optimizer(params, opt, **lars_params):\n", "    optim = hydra.utils.instantiate(opt, params=params)\n", "    return LARS(optim, **lars_params)\n", "\n", "\n", "def validate_param_group_params(param_groups, model):\n", "    parameters = [set(param_group[\"params\"]) for param_group in param_groups]\n", "    model_parameters = {parameter for _, parameter in model.named_parameters()}\n", "    for p1, p2 in itertools.permutations(parameters, 2):\n", "        assert p1.isdisjoint(p2), \"Scheduler generated param_groups should be disjoint\"\n", "    assert (\n", "        set.union(*parameters) == model_parameters\n", "    ), \"Scheduler generated param_groups include all parameters of the model\"\n", "\n", "\n", "def unix_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig, model: nn.<PERSON><PERSON><PERSON>\n", ") -> Union[None, Set[str]]:\n", "    if \"param_names\" not in scheduler_cfg and \"module_cls_names\" not in scheduler_cfg:\n", "        return None\n", "    return unix_param_pattern_to_parameter_names(scheduler_cfg, model).union(\n", "        unix_module_cls_pattern_to_parameter_names(scheduler_cfg, model)\n", "    )\n", "\n", "\n", "def get_full_parameter_name(module_name, param_name):\n", "    if module_name == \"\":\n", "        return param_name\n", "    return f\"{module_name}.{param_name}\"\n", "\n", "\n", "def unix_module_cls_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig,\n", "    model: nn.<PERSON><PERSON>,\n", ") -> Union[None, Set[str]]:\n", "    if \"module_cls_names\" not in scheduler_cfg:\n", "        return set()\n", "    module_cls_to_params = {}\n", "    for module_name, module in model.named_modules():\n", "        module_cls = type(module)\n", "        module_cls_to_params.setdefault(module_cls, set())\n", "        module_cls_to_params[module_cls] |= set(\n", "            get_full_parameter_name(module_name, param_name)\n", "            for param_name, _ in module.named_parameters()\n", "        )\n", "    parameter_names = []\n", "    for module_cls_name in scheduler_cfg.module_cls_names:\n", "        module_cls = hydra.utils.get_class(module_cls_name)\n", "        matching_parameters = module_cls_to_params.get(module_cls, set())\n", "        assert len(matching_parameters) > 0, (\n", "            f\"Optimizer option for {scheduler_cfg.option} module_cls_name\"\n", "            f\" {module_cls_name} does not match any classes in the model\"\n", "        )\n", "        logging.info(\n", "            f\"Matches for module_cls_name [{module_cls_name}]: {matching_parameters} \"\n", "        )\n", "        parameter_names.append(matching_parameters)\n", "    return set.union(*parameter_names)\n", "\n", "\n", "def unix_param_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig,\n", "    model: nn.<PERSON><PERSON>,\n", ") -> Union[None, Set[str]]:\n", "    if \"param_names\" not in scheduler_cfg:\n", "        return set()\n", "    all_parameter_names = {name for name, _ in model.named_parameters()}\n", "    parameter_names = []\n", "    for param_name in scheduler_cfg.param_names:\n", "        matching_parameters = set(fnmatch.filter(all_parameter_names, param_name))\n", "        assert len(matching_parameters) >= 1, (\n", "            f\"Optimizer option for {scheduler_cfg.option} param_names {param_name} \"\n", "            \"does not match any parameters in the model\"\n", "        )\n", "        logging.info(f\"Matches for param_name [{param_name}]: {matching_parameters}\")\n", "        parameter_names.append(matching_parameters)\n", "    return set.union(*parameter_names)\n", "\n", "\n", "def set_default_parameters(\n", "    scheduler_cfgs: List[DictConfig], all_parameter_names: Set[str]\n", ") -> None:\n", "    constraints = [\n", "        scheduler_cfg.parameter_names\n", "        for scheduler_cfg in scheduler_cfgs\n", "        if scheduler_cfg.parameter_names is not None\n", "    ]\n", "    if len(constraints) == 0:\n", "        default_params = set(all_parameter_names)\n", "    else:\n", "\n", "        default_params = all_parameter_names - set.union(*constraints)\n", "    default_count = 0\n", "    for scheduler_cfg in scheduler_cfgs:\n", "        if scheduler_cfg.parameter_names is None:\n", "            scheduler_cfg.parameter_names = default_params\n", "            default_count += 1\n", "    assert default_count <= 1, \"Only one scheduler per option can be default\"\n", "    if default_count == 0:  # Add defaults without options\n", "        scheduler_cfgs.append({\"parameter_names\": default_params})\n", "\n", "\n", "def name_constraints_to_parameters(\n", "    param_constraints: List[Set[str]], model: torch.nn.Module\n", ") -> List[torch.nn.Parameter]:\n", "    matching_names = set.intersection(*param_constraints)\n", "    return [value for name, value in model.named_parameters() if name in matching_names]\n", "\n", "\n", "def map_scheduler_cfgs_to_param_groups(\n", "    scheduler_cfgs_per_param_group: Iterable[List[Dict]], model: torch.nn.Module\n", ") -> Tuple[List[Dict[Any, Any]], List[Dict[str, List[torch.nn.Parameter]]]]:\n", "    schedulers = []\n", "    param_groups = []\n", "    for scheduler_cfgs in scheduler_cfgs_per_param_group:\n", "        param_constraints = [\n", "            scheduler_cfg[\"parameter_names\"] for scheduler_cfg in scheduler_cfgs\n", "        ]\n", "        matching_parameters = name_constraints_to_parameters(param_constraints, model)\n", "        if len(matching_parameters) == 0:  # If no overlap of parameters, skip\n"]}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to187__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">GoldCompletion (PASSED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">RagFIMAV17B+OracleTop1 (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to187__0\"><a href=\"#difflib_chg_to187__0\">f</a></td><td class=\"diff_header\" id=\"from187_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;continue</td><td class=\"diff_next\"><a href=\"#difflib_chg_to187__0\">f</a></td><td class=\"diff_header\" id=\"to187_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;continue</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from187_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;schedulers_for_group&nbsp;=&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to187_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;schedulers_for_group&nbsp;=&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to187__top\">t</a></td><td class=\"diff_header\" id=\"from187_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"option\"]:&nbsp;scheduler_cfg[\"scheduler\"]</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to187__top\">t</a></td><td class=\"diff_header\" id=\"to187_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"option\"]:&nbsp;hydra.utils.instantiate(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to187_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"scheduler\"],&nbsp;params=matching_parameters</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to187_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to188__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">Ground Truth</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">RagFIMAV17B+OracleTop1 (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to188__0\"><a href=\"#difflib_chg_to188__0\">f</a></td><td class=\"diff_header\" id=\"from188_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;continue</td><td class=\"diff_next\"><a href=\"#difflib_chg_to188__0\">f</a></td><td class=\"diff_header\" id=\"to188_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;continue</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from188_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;schedulers_for_group&nbsp;=&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to188_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;schedulers_for_group&nbsp;=&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to188__top\">t</a></td><td class=\"diff_header\" id=\"from188_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"option\"]:&nbsp;scheduler_cfg[\"scheduler\"]</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to188__top\">t</a></td><td class=\"diff_header\" id=\"to188_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"option\"]:&nbsp;hydra.utils.instantiate(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to188_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;scheduler_cfg[\"scheduler\"],&nbsp;params=matching_parameters</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to188_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "            for scheduler_cfg in scheduler_cfgs\n", "            if \"option\" in scheduler_cfg\n", "        }\n", "        schedulers.append(schedulers_for_group)\n", "        param_groups.append({\"params\": matching_parameters})\n", "    return schedulers, param_groups\n", "\n", "\n", "def construct_optimizer(\n", "    model: <PERSON>.nn.<PERSON>,\n", "    optimizer_conf,\n", "    options_conf=None,\n", "    param_group_modifiers_conf=None,\n", ") -> OmniOptimizer:  # noqa\n", "    \"\"\"\n", "    Constructs a stochastic gradient descent or ADAM (or ADAMw) optimizer\n", "    with momentum. i.e, constructs a torch.optim.Optimizer with zero-weight decay\n", "    Batchnorm and/or no-update 1-D parameters support, based on the config.\n", "\n", "    Supports wrapping the optimizer with Layer-wise Adaptive Rate Scaling\n", "    (LARS): https://arxiv.org/abs/1708.03888\n", "\n", "    Args:\n", "        model (nn.<PERSON><PERSON><PERSON>): model to perform stochastic gradient descent\n", "            optimization or ADAM optimization.\n", "        cfg (OptimizerConf): Hydra/Omega conf object consisting hyper-parameters\n", "            of SGD or ADAM, includes base learning rate,  momentum, weight_decay,\n", "            dampening and etc. The supported config schema is `OptimizerConf`.\n", "    \"\"\"\n", "    if not options_conf:\n", "        optimizer = hydra.utils.instantiate(optimizer_conf, params=model.parameters())\n", "        return OmniOptimizer(optimizer)\n", "\n", "    scheduler_cfgs_per_option = hydra.utils.instantiate(options_conf)\n", "    all_parameter_names = {name for name, _ in model.named_parameters()}\n", "    flattened_scheduler_cfgs = []\n", "    for option, scheduler_cfgs in scheduler_cfgs_per_option.items():\n", "        for config in scheduler_cfgs:\n", "            config.option = option\n", "            config.parameter_names = unix_pattern_to_parameter_names(config, model)\n", "        set_default_parameters(scheduler_cfgs, all_parameter_names)\n", "        flattened_scheduler_cfgs.append(scheduler_cfgs)\n", "\n", "    if param_group_modifiers_conf:\n", "        for custom_param_modifier in param_group_modifiers_conf:\n", "            custom_param_modifier = hydra.utils.instantiate(custom_param_modifier)\n", "            flattened_scheduler_cfgs = custom_param_modifier(\n", "                scheduler_cfgs=flattened_scheduler_cfgs, model=model\n", "            )\n", "    schedulers, param_groups = map_scheduler_cfgs_to_param_groups(\n", "        itertools.product(*flattened_scheduler_cfgs), model\n", "    )\n", "    validate_param_group_params(param_groups, model)\n", "    optimizer = hydra.utils.instantiate(optimizer_conf, param_groups)\n", "    return OmniOptimizer(optimizer, schedulers)\n"]}], "source": ["for pid in pid_list:\n", "    if pid in annotations:\n", "        continue\n", "    clear_output(wait=True)\n", "    print(f\"{len(annotations)} out of {len(pid_list)}\")\n", "    print(f\"PATCH ID: {pid}\")\n", "    print(f\"File Path: {right_results[pid]['patch_rst']['patch'].file_name}\")\n", "    print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "    print(\"Generation Side by Side\")\n", "    print_prefix_lines(pid, 200)\n", "    generation_sxs(pid)\n", "    # print(\"Ground True\")\n", "    generation_vs_ground_truth(pid, right_results)\n", "    print_suffix_lines(pid, 200)\n", "    # print('---')\n", "    # print(\"Prompt Side by Side\")\n", "    # prompt_sxs(pid)\n", "    # print('---')\n", "    # print(\"Best matched ground truth in the prompt\")\n", "    # left_best_matched, right_best_matched = best_matched_sxs(pid, feature ='ground_truth')\n", "    # print(\"Best matched generation in the prompt\")\n", "    # best_matched_sxs(pid, feature ='generation')\n", "    annotation = input(\"Continue\")\n", "    if annotation == \"c\":\n", "        break\n", "    annotations[pid] = annotation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Yaml config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "import json\n", "\n", "EXPERIMENTS.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["left_experiment = \"StarCoder+No\"\n", "right_experimet = \"DiffRe1000+PPL1000\"\n", "\n", "left_config = vz_utils.load_experiment_config(EXPERIMENTS[left_experiment])\n", "right_config = vz_utils.load_experiment_config(EXPERIMENTS[right_experimet])\n", "\n", "display(\n", "    HTML(\n", "        vz_utils.get_diff_html(\n", "            json.dumps(left_config, indent=2),\n", "            json.dumps(right_config, indent=2),\n", "            fromdesc=left_experiment,\n", "            todesc=right_experimet,\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect Logs"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "eval_dir = '/mnt/efs/augment/eval/jobs/HemkWSQ9'\n", "log_file = os.path.join(eval_dir, '000_RAGSystem_hydra.jsonl')\n", "\n", "logs = {}\n", "with open(log_file) as f:\n", "    for line in f:\n", "        d = json.loads(line)\n", "        logs[d['patch_id']] = d\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['tests', 'command', 'mode', 'result', 'run_output'])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["pid = 'lucidrains/imagen-pytorch/c307eff8e6d75e03' # pragma: allowlist secret\n", "# pid = 'deepmind/tracr/fb69f788a231b892' # pragma: allowlist secret\n", "logs[pid]['_extra'].keys()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxrwxrwx 3 <USER> <GROUP> 101 Oct 12 21:57 .\n", "drwxr-xr-x 1 <USER> <GROUP>  57 Oct 12 21:57 ..\n", "drwxr-xr-x 2 <USER> <GROUP>  48 Oct 12 21:57 ..2023_10_12_21_57_16.522105423\n", "lrwxrwxrwx 1 root root  31 Oct 12 21:57 ..data -> ..2023_10_12_21_57_16.522105423\n", "lrwxrwxrwx 1 root root  17 Oct 12 21:57 patch.json -> ..data/patch.json\n", "lrwxrwxrwx 1 root root  23 Oct 12 21:57 patch_and_run.py -> ..data/patch_and_run.py\n", "repo_name=lucidrains\n", "file_name=imagen_pytorch/imagen_pytorch.py\n", "Running test harness...\n", "Starting now...\n", "model name\t: AMD EPYC 7413 24-Core Processor\n", "============================= test session starts ==============================\n", "platform linux -- Python 3.9.17, pytest-7.4.0, pluggy-1.2.0\n", "rootdir: /code\n", "plugins: cov-4.1.0, xdist-3.3.1\n", "collected 2 items\n", "\n", "imagen_pytorch/test/test_trainer.py Running command: pytest -x imagen_pytorch/test/test_trainer.py::test_trainer_step imagen_pytorch/test/test_trainer.py::test_trainer_instantiation\n", "lucidrains/imagen-pytorch/c307eff8e6d75e03                                      \u001b[36mTIMEOUT\u001b[0m    wall_time=600.05    test_time=600.05    \n", "\n"]}], "source": ["print(logs[pid]['_extra']['run_output'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Draft"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines_noindent/CarperAI/trlx_patches.jsonl', 'r') as f:\n", "    for l in f:\n", "        d = json.loads(l)\n", "        # char_start = d['char_start']\n", "        # char_end = d['char_end']\n", "        # while d['file_content'][char_start].isspace() and char_start <= char_end:\n", "        #     char_start += 1\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import shutil\n", "\n", "folder = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines\"\n", "new_folder = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines_noindent\"\n", "for repo_name in os.listdir(folder):\n", "    new_repo_dir = os.path.join(new_folder, repo_name)\n", "    patchfile = glob.glob(os.path.join(folder, repo_name, \"*_patches.jsonl\"))\n", "    assert len(patchfile) == 1\n", "    patchfile = patchfile[0]\n", "    with open(patchfile, \"r\") as fread:\n", "        os.makedirs(new_repo_dir, exist_ok=True)\n", "        with open(\n", "            os.path.join(new_repo_dir, os.path.basename(patchfile)), \"w\"\n", "        ) as fwrite:\n", "            for line in fread:\n", "                data = json.loads(line)\n", "                char_start = data[\"char_start\"]\n", "                char_end = data[\"char_end\"]\n", "                while (\n", "                    data[\"file_content\"][char_start].isspace()\n", "                    and char_start <= char_end\n", "                ):\n", "                    char_start += 1\n", "                data[\"char_start\"] = char_start\n", "                fwrite.write(json.dumps(data) + \"\\n\")\n", "    # Copy db file\n", "    dbfile = glob.glob(os.path.join(folder, repo_name, \"*_db.jsonl\"))\n", "    assert len(dbfile) == 1\n", "    dbfile = dbfile[0]\n", "    shutil.copyfile(\n", "        dbfile, os.path.join(new_repo_dir, os.path.basename(dbfile))\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
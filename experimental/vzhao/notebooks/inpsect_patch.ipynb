{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports and Utilities"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Imports and Utilities\n", "import difflib\n", "import html\n", "import json\n", "import pandas as pd\n", "import pickle\n", "import ipywidgets as widgets\n", "import os\n", "\n", "from research.eval.harness import utils\n", "from research.eval.patch_lib import Patch\n", "from IPython.display import display, HTML, clear_output\n", "from collections import defaultdict\n", "from pathlib import Path\n", "from types import MethodType\n", "\n", "\n", "def read_results(dir: str):\n", "    matching_files = list(Path(dir).glob(\"*_hydra.jsonl\"))\n", "    print(len(matching_files))\n", "    if len(matching_files) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 Hydra jsonl file under {dir}, found {len(matching_files)}\"\n", "        )\n", "    hydra_results_path = matching_files[0]\n", "    with hydra_results_path.open(\"r\") as f:\n", "        return [json.loads(x) for x in f]\n", "\n", "\n", "def read_jsonl_zst(dir: str):\n", "    matched = list(Path(dir).glob(\"*_completed_patches.jsonl.zst\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    return utils.read_jsonl_zst(path)\n", "\n", "\n", "def read_pkl(dir: str):\n", "    matched = list(Path(dir).glob(\"*_completed_patches.pkl\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    with open(path, \"rb\") as f:\n", "        return pickle.load(f)\n", "\n", "\n", "def load(eval_dir: str):\n", "    \"\"\"Returns dict[patch_id, results].\"\"\"\n", "    results = read_results(eval_dir)\n", "    patch_results = read_jsonl_zst(eval_dir) or read_pkl(eval_dir)\n", "    if len(patch_results) != len(results):\n", "        raise ValueError(\"Inconsistent records!\")\n", "    metadata = {}\n", "    # return patch_results, results\n", "    for pr, r in zip(patch_results, results):\n", "        assert pr[\"completion\"] == r[\"patch_content\"]\n", "        patch_obj = pr[\"patch\"]\n", "        run_metadata = {\n", "            \"prompt\": pr[\"prompt\"],\n", "            \"generation\": pr[\"generation\"],\n", "            \"completion\": pr[\"completion\"],\n", "            \"ground_truth\": r[\"file_content\"][r[\"char_start\"] : r[\"char_end\"]],\n", "            \"filename\": patch_obj.file_name\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_name\"],\n", "            \"file_content\": patch_obj.file_content\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_content\"],\n", "            \"result\": r[\"_extra\"][\"result\"],\n", "            \"run_output\": r[\"_extra\"][\"run_output\"],\n", "            \"patch_rst\": pr,\n", "            \"json_rst\": r,\n", "        }\n", "\n", "        if (\n", "            \"retrieval_metadata\" in pr\n", "            and \"retriever_prompt\" in pr[\"retrieval_metadata\"]\n", "        ):\n", "            run_metadata[\"retriever_prompt\"] = pr[\"retrieval_metadata\"][\n", "                \"retriever_prompt\"\n", "            ]\n", "        else:\n", "            run_metadata[\"retriever_prompt\"] = \"\"\n", "        metadata[r[\"patch_id\"]] = run_metadata\n", "    return metadata\n", "\n", "\n", "def get_diff_html(left: str, right: str, fromdesc=\"\", todesc=\"\") -> str:\n", "    \"\"\"Returns HTML to compare `left` and `right`.\"\"\"\n", "    diff_obj = difflib.HtmlDiff()\n", "    # diff_obj._legend = \"\"\n", "    return diff_obj.make_table(\n", "        left.splitlines(),\n", "        right.splitlines(),\n", "        fromdesc=fromdesc,\n", "        todesc=todesc,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inspect a Hydra patch"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["exp_id = \"StarCoder+No\"\n", "eval_dir = \"/mnt/efs/augment/eval/jobs/MuAhbB4X\"\n", "results = load(eval_dir)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["google/lightweight_mmm\n", "lightweight_mmm/core/transformations/lagging.py\n"]}], "source": ["pid = \"google_lightweight_mmm/24\"\n", "\n", "result = results[pid]\n", "patch = result['patch_rst']['patch']\n", "\n", "print(patch.repository)\n", "print(patch.file_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Editing Prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [".\n", "      Default is 0.5.\n", "    peak_effect_delay: Delay of the peak effect in the carryover function.\n", "      De<PERSON>ult is 1.\n", "    number_lags: Number of lags to include in the carryover calculation. Default\n", "      is 13.\n", "\n", "  Returns:\n", "    The carryover values for the given data with the given parameters.\n", "  \"\"\"\n", "  lags_arange = jnp.expand_dims(\n", "      jnp.arange(number_lags, dtype=jnp.float32), axis=-1)\n", "  convolve_func = _carryover_convolve\n", "  if data.ndim == 3:\n", "    # Since _carryover_convolve is already vmaped in the decorator we only need\n", "    # to vmap it once here to handle the geo level data. We keep the windows bi\n", "    # dimensional also for three dims data and vmap over only the extra data\n", "    # dimension.\n", "    convolve_func = jax.vmap(\n", "        fun=_carryover_convolve, in_axes=(2, None, None), out_axes=2)\n", "  weights = ad_effect_retention_rate**((lags_arange - peak_effect_delay)**2)\n", "  return convolve_func(data, weights, number_lags)\n", "\n", "\n", "def carryover(\n", "    data: jnp.n<PERSON><PERSON>,\n", "    custom_priors: Mapping[str, dist.Distribution],\n", "    *,\n", "    number_lags: int = 13,\n", "    prefix: str = \"\",\n", ") -> jnp.n<PERSON><PERSON>:\n", "  \"\"\"Transforms the input data with the carryover function.\n", "\n", "  Args:\n", "    data: Media data to be transformed. It is expected to have 2 dims for\n", "      national models and 3 for geo models.\n", "    custom_priors: The custom priors we want the model to take instead of the\n", "      default ones.\n", "    number_lags: Number of lags for the carryover function.\n", "    prefix: Prefix to use in the variable name for Numpyro.\n", "\n", "  Returns:\n", "    The transformed media data.\n", "  \"\"\"\n", "  default_priors = priors.get_default_priors()\n", "  with numpyro.plate(\n", "      name=f\"{prefix}{priors.AD_EFFECT_RETENTION_RATE}_plate\",\n", "      size=data.shape[1]):\n", "    ad_effect_retention_rate = numpyro.sample(\n", "        name=f\"{prefix}{priors.AD_EFFECT_RETENTION_RATE}\",\n", "        fn=custom_priors.get(priors.AD_EFFECT_RETENTION_RATE,\n", "                             default_priors[priors.AD_EFFECT_RETENTION_RATE]))\n", "\n", "  with numpyro.plate(\n", "      name=f\"{prefix}{priors.PEAK_EFFECT_DELAY}_plate\", size=data.shape[1]):\n", "    peak_effect_delay = numpyro.sample(\n", "        name=f\"{prefix}{priors.PEAK_EFFECT_DELAY}\",\n", "        fn=custom_priors.get(priors.PEAK_EFFECT_DELAY,\n", "                             default_priors[priors.PEAK_EFFECT_DELAY]))\n", "\n", "  return _carryover(\n", "      data=data,\n", "      ad_effect_retention_rate=ad_effect_retention_rate,\n", "      peak_effect_delay=peak_effect_delay,\n", "      number_lags=number_lags)\n", "\n", "\n", "@jax.jit\n", "def _adstock(\n", "    data: jnp.n<PERSON><PERSON>,\n", "    lag_weight: Union[float, jnp.ndarray] =.9,\n", "    normalise: bool = True,\n", ") -> jnp.n<PERSON><PERSON>:\n", "  \"\"\"Calculates the adstock value of a given array.\n", "\n", "  To learn more about advertising lag:\n", "  https://en.wikipedia.org/wiki/Advertising_adstock\n", "\n", "  Args:\n", "    data: Input array.\n", "    lag_weight: lag_weight effect of the adstock function. Default is 0.9.\n", "    normalise: Whether to normalise the output value. This normalization will\n", "      divide the output values by (1 / (1 - lag_weight)).\n", "\n", "  Returns:\n", "    The adstock output of the input array.\n", "  \"\"\""]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Load Code Mode"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CodeGen"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from augment.research.models.codegen_models import (\n", "    CodeGen_350M_Multi,\n", "    CodeGen_2B_Multi,\n", "    CodeGen_16B_Multi,\n", "    CodeGen_16B_Indiana,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## StarCoder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.models.starcoder_models import StarCoderBase16B"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.models.all_models import get_model, list_models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_models()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"starcoderbase_7b\")\n", "model.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Remote Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remote_model = get_model(\"remote\", url=\"http://216.153.48.243:5000\")\n", "remote_model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "from augment.research.core.model_input import ModelInput\n", "from augment.research.models import StopCriteria, GenerationOptions\n", "\n", "result = remote_model.generate(\n", "    ModelInput(\"def hello_world():\\n\"),\n", "    GenerationOptions(\n", "        temperature=0.0,\n", "        max_generated_tokens=120,\n", "        stop_criteria=StopCriteria(stop_texts=[\"\\n\"], check_stopping_condition_every=4),\n", "    ),\n", ")\n", "print(colored(result, \"blue\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Launch Code Model Server (for remote model)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["```shell\n", "# Step 1\n", "deploy/dev/reserved_nodes/launch_pod.py --pod_name vzhao-a40 --gpu_type A40 --cpu_count 1\n", "\n", "# Step 2\n", "ssh vzhao-a40\n", "\n", "# Step 3\n", "<NAME_EMAIL>:(yourname)/augment.git\n", "\n", "# Step 4\n", "pip install -e augment/research/model_server\n", "\n", "# Step 5\n", "python3 augment/research/model_server/launch_model_server.py --model starcoderbase_16b\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Launch Hydra Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "from augment.research.eval import hydra\n", "\n", "HYDRA_DRIVER_NAME = f\"{getpass.getuser()}-dev-hydra-notebook-example\"\n", "HYDRA_LOCAL_TIMEOUT_SECS = 30\n", "HYDRA_GLOBAL_TIMEOUT_SECS = 180\n", "HYDRA_NUM_PATCHES_LIMIT = 5\n", "\n", "driver = hydra.Driver(\n", "    driver_name=HYDRA_DRIVER_NAME,\n", "    local_timeout_secs=HYDRA_LOCAL_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_GLOBAL_TIMEOUT_SECS,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.eval import patch_lib\n", "import difflib\n", "import html\n", "import json\n", "import pandas as pd\n", "import pickle\n", "import ipywidgets as widgets\n", "import os\n", "\n", "from lm_eval.tasks.repo_coder.utils import trim_completion\n", "from augment.research.eval.harness import utils\n", "from augment.research.eval.patch_lib import Patch\n", "from IPython.display import display, HTML, clear_output\n", "from collections import defaultdict\n", "from pathlib import Path\n", "from types import MethodType\n", "\n", "\n", "def read_results(dir: str):\n", "    matching_files = list(Path(dir).glob(\"*_hydra.jsonl\"))\n", "    print(len(matching_files))\n", "    if len(matching_files) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 Hydra jsonl file under {dir}, found {len(matching_files)}\"\n", "        )\n", "    hydra_results_path = matching_files[0]\n", "    with hydra_results_path.open(\"r\") as f:\n", "        return [json.loads(x) for x in f]\n", "\n", "\n", "def read_jsonl_zst(dir: str):\n", "    matched = list(Path(dir).glob(\"*_completed_patches.jsonl.zst\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    return utils.read_jsonl_zst(path)\n", "\n", "\n", "def read_pkl(dir: str):\n", "    matched = list(Path(dir).glob(\"*_completed_patches.pkl\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    with open(path, \"rb\") as f:\n", "        return pickle.load(f)\n", "\n", "\n", "def load(eval_dir: str):\n", "    \"\"\"Returns dict[patch_id, results].\"\"\"\n", "    results = read_results(eval_dir)\n", "    patch_results = read_jsonl_zst(eval_dir) or read_pkl(eval_dir)\n", "    if len(patch_results) != len(results):\n", "        raise ValueError(\"Inconsistent records!\")\n", "    metadata = {}\n", "    # return patch_results, results\n", "    for pr, r in zip(patch_results, results):\n", "        assert pr[\"completion\"] == r[\"patch_content\"]\n", "        patch_obj = pr[\"patch\"]\n", "        run_metadata = {\n", "            \"prompt\": pr[\"prompt\"],\n", "            \"generation\": pr[\"generation\"],\n", "            \"completion\": pr[\"completion\"],\n", "            \"ground_truth\": r[\"file_content\"][r[\"char_start\"] : r[\"char_end\"]],\n", "            \"filename\": patch_obj.file_name\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_name\"],\n", "            \"file_content\": patch_obj.file_content\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_content\"],\n", "            \"result\": r[\"_extra\"][\"result\"],\n", "            \"run_output\": r[\"_extra\"][\"run_output\"],\n", "            \"patch_rst\": pr,\n", "            \"json_rst\": r,\n", "        }\n", "\n", "        if (\n", "            \"retrieval_metadata\" in pr\n", "            and \"retriever_prompt\" in pr[\"retrieval_metadata\"]\n", "        ):\n", "            run_metadata[\"retriever_prompt\"] = pr[\"retrieval_metadata\"][\n", "                \"retriever_prompt\"\n", "            ]\n", "        else:\n", "            run_metadata[\"retriever_prompt\"] = \"\"\n", "        metadata[r[\"patch_id\"]] = run_metadata\n", "    return metadata\n", "\n", "\n", "def get_diff_html(left: str, right: str, fromdesc=\"\", todesc=\"\") -> str:\n", "    \"\"\"Returns HTML to compare `left` and `right`.\"\"\"\n", "    diff_obj = difflib.HtmlDiff(tabsize=2)\n", "    diff_obj._legend = \"\"\n", "    # markers = [\n", "    #     '+' if res == \"PASSED\"\n", "    #     else (\n", "    #         '-' if res == \"FAILED\" else '^'\n", "    #     )\n", "    #     for res in ('FAILED', ['PASSED'])\n", "    # ]\n", "    # # Adjust colors\n", "    # orig_convert_flags = diff_obj._convert_flags\n", "    # def _convert_flags(self,fromlist,tolist,flaglist,context,numlines):\n", "    #     def _swap_parity(items):\n", "    #         return [\n", "    #             item.replace('\\0+', '\\0*').replace('\\0-', f'\\0{markers[0]}').replace('\\0*', f'\\0{markers[1]}')\n", "    #             for item in items\n", "    #         ]\n", "    #     fromlist = _swap_parity(fromlist)\n", "    #     tolist = _swap_parity(tolist)\n", "    #     return orig_convert_flags(fromlist, tolist, flaglist, context, numlines)\n", "\n", "    # diff_obj._convert_flags = MethodType(_convert_flags, diff_obj)\n", "\n", "    # This CSS thing is important.\n", "    display(HTML(f\"\"\"\n", "        <style type=\"text/css\">\n", "            {difflib.HtmlDiff()._styles}\n", "            td {{ text-align: left; }}\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "        </style>\n", "        \"\"\"))\n", "\n", "\n", "    return diff_obj.make_table(\n", "        left.splitlines(),\n", "        right.splitlines(),\n", "        fromdesc=fromdesc,\n", "        todesc=todesc,\n", "    )\n", "\n", "\n", "results = load('/mnt/efs/augment/eval/jobs/TGsikzze')\n", "for pid in results:\n", "    cache = results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid = 'maxhumber_redframes/4'\n", "\n", "new_content = '''\n", "    _check_type(columns, {list, str})\n", "    _check_type(descending, bool)\n", "    _check_keys(columns, df.columns)\n", "\n", "    return df.sort_values(by=columns, ascending=not descending).reset_index(drop=True)\n", "    # return df.sort_values(columns, ascending=not descending)\n", "'''\n", "\n", "_ = driver.dispatch(results[pid]['patch_rst']['patch'].with_patch_content(new_content))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(_['run_output'])"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Save df to Sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pygsheets\n", "def write_to_gsheet(service_file_path, spreadsheet_id, sheet_name, data_df):\n", "    \"\"\"\n", "    this function takes data_df and writes it under spreadsheet_id\n", "    and sheet_name using your credentials under service_file_path\n", "    \"\"\"\n", "    gc = pygsheets.authorize(service_file=service_file_path)\n", "    sh = gc.open_by_key(spreadsheet_id)\n", "    try:\n", "        sh.add_worksheet(sheet_name)\n", "    except:\n", "        pass\n", "    wks_write = sh.worksheet_by_title(sheet_name)\n", "    wks_write.clear('A1',None,'*')\n", "    wks_write.set_dataframe(data_df, (1,1), encoding='utf-8', fit=True)\n", "    wks_write.frozen_rows = 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Color Diff View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "from IPython.display import display, HTML, clear_output\n", "from types import MethodType\n", "\n", "def get_diff_html(left: str, right: str, fromdesc=\"\", todesc=\"\") -> str:\n", "    \"\"\"Returns HTML to compare `left` and `right`.\"\"\"\n", "    diff_obj = difflib.HtmlDiff(tabsize=2)\n", "    diff_obj._legend = \"\"\n", "    # # Adjust colors\n", "    # markers = [\n", "    #     '+' if res == \"PASSED\"\n", "    #     else (\n", "    #         '-' if res == \"FAILED\" else '^'\n", "    #     )\n", "    #     for res in ('FAILED', ['PASSED'])\n", "    # ]\n", "    # orig_convert_flags = diff_obj._convert_flags\n", "    # def _convert_flags(self,fromlist,tolist,flaglist,context,numlines):\n", "    #     def _swap_parity(items):\n", "    #         return [\n", "    #             item.replace('\\0+', '\\0*').replace('\\0-', f'\\0{markers[0]}').replace('\\0*', f'\\0{markers[1]}')\n", "    #             for item in items\n", "    #         ]\n", "    #     fromlist = _swap_parity(fromlist)\n", "    #     tolist = _swap_parity(tolist)\n", "    #     return orig_convert_flags(fromlist, tolist, flaglist, context, numlines)\n", "\n", "    # diff_obj._convert_flags = MethodType(_convert_flags, diff_obj)\n", "\n", "    # This CSS thing is important.\n", "    display(HTML(f\"\"\"\n", "        <style type=\"text/css\">\n", "            {difflib.HtmlDiff()._styles}\n", "            td {{ text-align: left; }}\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "        </style>\n", "        \"\"\"))\n", "\n", "\n", "    return diff_obj.make_table(\n", "        left.splitlines(),\n", "        right.splitlines(),\n", "        fromdesc=fromdesc,\n", "        todesc=todesc,\n", "    )\n", "\n", "\n", "display(\n", "    HTML(\n", "        get_diff_html(\n", "            \"\"\"abc\n", "dfdsfdsfds\n", "dfsdfsfdf\"\"\",\n", "            \"db\",\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tokenizer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Tokenizer"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer import get_tokenizer\n", "\n", "tokenizer = get_tokenizer('StarCoderTokenizer')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prod Tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# research/core/prod_adapters/tokenizer_wrapper.py\n", "from research.core.prod_adapters import tokenizer_wrapper\n", "\n", "self._prod_tokenizer = ProdTokenizerWrapper(\n", "            self.tokenizer,\n", "            self._special_tokens_factory,\n", "            unsafe_override=True,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chunker"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval import chunking_functions\n", "from augment.research.core.types import Document"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["document = Document(\n", "    id=str(hash(\"hello\")),\n", "    path=\"foo.py\",\n", "    text=\"\"\"from logging import Logger\n", "from .logger_tensorboard import TensorBoardLogger\n", "from .logger_wandb import WandBLogger\n", "from .logger_base import get_logger, LoggerBase\n", "\n", "\n", "logger_mapping = {\n", "    \"tensorboard\": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"wandb\": <PERSON><PERSON><PERSON>,\n", "    \"none\": <PERSON><PERSON><PERSON><PERSON>,\n", "}\n", "\n", "\n", "def type_check(logger_type):\n", "    assert logger_type in logger_mapping\n", "\n", "    if logger_type == \"wandb\":\n", "        try:\n", "            import wandb\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] WandB is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "    elif logger_type == \"tensorboard\":\n", "        try:\n", "            from torch.utils.tensorboard import SummaryWriter\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] Tensorboard is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "\n", "    return logger_type\n", "\n", "\n", "def logger(logger_type=\"none\"):\n", "    logger_type = type_check(logger_type)\n", "    return logger_mapping[logger_type]()\"\"\",\n", ")\n", "\n", "chunker = chunking_functions.ScopeAwareChunker(max_lines_per_chunk=10)\n", "chunks = chunker.split_into_chunks(document)\n", "chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chunks[1].text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LineChunker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["document = Document(\n", "    id=str(hash(\"hello\")),\n", "    path=\"foo.py\",\n", "    text=\"\"\"from logging import Logger\n", "from .logger_tensorboard import TensorBoardLogger\n", "from .logger_wandb import WandBLogger\n", "from .logger_base import get_logger, LoggerBase\n", "\n", "\n", "logger_mapping = {\n", "    \"tensorboard\": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"wandb\": <PERSON><PERSON><PERSON>,\n", "    \"none\": <PERSON><PERSON><PERSON><PERSON>,\n", "}\n", "\n", "\n", "def type_check(logger_type):\n", "    assert logger_type in logger_mapping\n", "\n", "    if logger_type == \"wandb\":\n", "        try:\n", "            import wandb\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] WandB is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "    elif logger_type == \"tensorboard\":\n", "        try:\n", "            from torch.utils.tensorboard import SummaryWriter\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] Tensorboard is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "\n", "    return logger_type\n", "\n", "\n", "def logger(logger_type=\"none\"):\n", "    logger_type = type_check(logger_type)\n", "    return logger_mapping[logger_type]()\"\"\",\n", ")\n", "\n", "chunker = chunking_functions.LineLevelChunker(max_lines_per_chunk=20)\n", "chunks = chunker.split_into_chunks(document)\n", "chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chunks[0].text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## SignatureChunk"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["document = Document(\n", "    id=str(hash(\"hello\")),\n", "    path=\"foo.py\",\n", "    text=\"\"\"from logging import Logger\n", "from .logger_tensorboard import TensorBoardLogger\n", "from .logger_wandb import WandBLogger\n", "from .logger_base import get_logger, LoggerBase\n", "\n", "\n", "logger_mapping = {\n", "    \"tensorboard\": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"wandb\": <PERSON><PERSON><PERSON>,\n", "    \"none\": <PERSON><PERSON><PERSON><PERSON>,\n", "}\n", "\n", "\n", "def type_check(logger_type):\n", "    assert logger_type in logger_mapping\n", "\n", "    if logger_type == \"wandb\":\n", "        try:\n", "            import wandb\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] WandB is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "    elif logger_type == \"tensorboard\":\n", "        try:\n", "            from torch.utils.tensorboard import SummaryWriter\n", "        except ImportError:\n", "            get_logger().warning(\n", "                \"[!] Tensorboard is not installed. The default logger will be instead used.\"\n", "            )\n", "            logger_type = \"none\"\n", "\n", "    return logger_type\n", "\n", "\n", "def logger(logger_type=\"none\"):\n", "    logger_type = type_check(logger_type)\n", "    return logger_mapping[logger_type]()\"\"\",\n", ")\n", "\n", "chunker = chunking_functions.SignatureChunker()\n", "chunks = chunker.split_into_chunks(document)\n", "chunks"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["print(chunks[0].text)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["print(chunks[1].text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scorer"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval.scorers import dense_scorer\n", "from augment.research.retrieval import prompt_formatters"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["scorer = dense_scorer.Contrastive_350M_Scorer(\n", "    query_formatter=prompt_formatters.SimpleQueryFormatter(\n", "        max_lines=20,\n", "        max_tokens=None,\n", "    ),\n", "    checkpoint=\"diff-retriever-boykin\",\n", ")\n", "# scorer.load()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["has<PERSON>r(scorer,'rerank')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorer.add_doc(chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorer.score('dfdfd')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorer.unload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval import prompt_formatters\n", "from augment.research.retrieval.scorers import dense_scorer\n", "from augment.research.retrieval import chunking_functions\n", "from augment.research.core.types import Document\n", "from augment.research.retrieval import retrieval_database\n", "\n", "from augment.research.retrieval import file_filterer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retriever = retrieval_database.RetrievalDatabase(\n", "    chunker=chunking_functions.LineLevelChunker(max_lines_per_chunk=10),\n", "    scorer=dense_scorer.Contrastive_350M_Scorer(\n", "        query_formatter=prompt_formatters.SimpleQueryFormatter(\n", "            max_lines=20,\n", "            max_tokens=None,\n", "        ),\n", "        checkpoint=\"diff-retriever-boykin\",\n", "    ),\n", "    file_filterer=file_filterer.basic_file_filterer,\n", ")\n", "\n", "retriever.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### StarEthanol 1b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["/home/<USER>/augment/research/gpt-neox/augment_configs/starcoder/model/starcoder-1b.yml"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/home/<USER>/augment/research/gpt-neox/augment_configs/starcoder/model/starcoder-1b.yml'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "\n", "canonicalize_path('research/gpt-neox/augment_configs/starcoder/model/starcoder-1b.yml', new_path=AUGMENT_ROOT)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval.scorers import dense_scorer\n", "from augment.research.retrieval import prompt_formatters\n", "\n", "scorer = dense_scorer.Starcoder_1B_Scorer(\n", "    checkpoint_path=\"starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "    additional_yaml_files=['/home/<USER>/augment/experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml']\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# System"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic RAG system."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from augment.research.eval.harness.factories import create_model, create_retriever\n", "from augment.research.eval.harness.systems.basic_RAG_system import (\n", "    RAGSystem,\n", "    MiscRAGSystemConfig,\n", ")\n", "from augment.research.eval.harness.tasks.hydra_task import HydraTask\n", "from augment.research.models.meta_model import (\n", "    GenerationOptions,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["model = create_model({\"name\": \"codegen-350m-multi\"})\n", "sys = RAGSystem(\n", "    model,\n", "    retriever=None,\n", "    generation_options=GenerationOptions(max_generated_tokens=64),\n", "    experimental_config=MiscRAGSystemConfig(),\n", ")\n", "sys.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Kubernetes\n", "\n", "**Show all pods**\n", "\n", "`kubectl get pods`\n", "\n", "**Find the pod that runs a determined exp**\n", "\n", "`kubectl get gods | <exp_id>`\n", "\n", "**SSH into the contain on the pod**\n", "\n", "`kubectl exec -it exp-16183-trial-16204-0-16183.1a9f2b78-3a20-45a4-8df6-e90dfb09d3c5.1-keen-mite -c determined-container -- /bin/bash`\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Run precommit\n", "\n", "`pre-commit run --from-ref main --to-ref HEAD`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Pipeline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "import os \n", "import json"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## List parquet files."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["spark = get_session()\n", "files = map_parquet.list_files(\n", "    spark,\n", "    # \"s3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/\",\n", "    \"s3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/\",\n", "    suffix=\"parquet\",\n", "    include_path=False,\n", ")\n", "spark.stop()\n", "print(len(files))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["!s3cmd ls s3://igor-dev-bucket/perplexity_distill4.01/04_subsampled/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading parquet files"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "import os \n", "import json\n", "\n", "spark = get_session()\n", "\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_6/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_6_label/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_6_label/\"\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1022_6pos_32total_posppl_1.0/stage_7_token_long_filepath/'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1022_6pos_32total_posppl_1.0/stage_7_token_long_filepath_preamble/'\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_6_label_1023'\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["df['retrieval_rank'][0]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["common.deserialize_retrieved_chunks(df['retrieved_chunks'][0])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["len(json.loads())"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "\n", "tokenizer = CodeGenTokenizer()\n", "\n", "print(spark_common.unpack_and_detokenize(tokenizer, df['prompt_tokens'][0][1]))"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["plt.hist(df['kendalltau'], bins=20)"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["import time\n", "import pandas as pd\n", "from scipy.stats import rankdata, kendalltau\n", "from IPython.display import display, HTML, clear_output\n", "\n", "for idx in range(20):\n", "    clear_output(wait=True)\n", "    foo = pd.DataFrame(\n", "        {\n", "            \"ppg\": json.loads(df[\"ppg\"][idx]),\n", "            \"ppg_raw\": json.loads(df[\"ppg_raw\"][idx]),\n", "            \"ppl\": json.loads(df[\"ppl\"][idx]),\n", "            \"ret_rank\": json.loads(df[\"retrieval_rank\"][idx]),\n", "        }\n", "    )\n", "    print(df[\"kendalltau\"][idx])\n", "    print(\n", "        kendalltau(\n", "            rankdata(foo[\"ret_rank\"], method=\"min\"),\n", "            rankdata(-foo[\"ppg\"], method=\"min\"),\n", "            variant=\"b\",\n", "        )\n", "    )\n", "    display(foo)\n", "    time.sleep(0.1)\n", "    if input() == \"c\":\n", "        break\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data size"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "import os \n", "import json\n", "\n", "# spark = get_session()\n", "spark = k8s_session(max_workers=8)\n", "path = 's3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/'\n", "# path = 's3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_6/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_5_ppg/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_6_label/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_6_label/\"\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1022_6pos_32total_posppl_1.0/stage_7_token_long_filepath_preamble'\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_6_label_1023'\n", "\n", "# files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "# df = spark.read.parquet(os.path.join(path, files[0]))\n", "df = spark.read.parquet(path)\n", "print(f'Examples: {df.count()}')\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Remove Parquet files."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "# WARNING: This will delete the data.\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_7b_alphal_fixtoken_128_128'\n", "\n", "foo = os.path.join(path.replace('s3a://', 's3://'), '*')\n", "!s3cmd rm {foo}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Download checkpoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* \\\n", "    s3://dev-training-dai/0b32edc6-35a6-4534-87e9-d4df36697c26 \\\n", "    /mnt/efs/augment/checkpoints/ethanol_rag"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python3 experimental/guy/utils/download_checkpoint.py \\\n", "    --checkpoint 5f2573a3-329c-49af-ac3e-6cb1f968d08f \\\n", "    --path /mnt/efs/augment/checkpoints/ethanol_rag/ethanol_rag_7b_1003"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["./download_checkpoint.sh <checkpoint-uuid> user/model_name [gcp]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Spark"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kill a Running Spark Job"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark= SparkSession.builder.appName('vzhao-dev').getOrCreate()\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DETERMINED API"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from determined.experimental import client"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["exp = client.get_experiment(29241)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["type(exp)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["exp.config['name']"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["t = exp.get_trials()[0]\n", "t.select_checkpoint(latest=True).uuid"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["exp.get_config()['name']"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "ckpt_root = pathlib.Path(\"/mnt/efs/augment/checkpoints/\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["(ckpt_root / 'ethanol/ethanol6-17.1').exists()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["ckpt_root.exists()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ckpt_path = ckpt_root / "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Draft"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Right"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["<fim_prefix># Here are some relevant code fragments from other files of the repo:\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/patchcore.py\n", "# --------------------------------------------------\n", "#             return x.numpy()\n", "#         return x\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/load_and_evaluate_patchcore.py\n", "# --------------------------------------------------\n", "# \n", "#                 def image_transform(image):\n", "#                     in_std = np.array(\n", "#                         dataloaders[\"testing\"].dataset.transform_std\n", "#                     ).reshape(-1, 1, 1)\n", "#                     in_mean = np.array(\n", "#                         dataloaders[\"testing\"].dataset.transform_mean\n", "#                     ).reshape(-1, 1, 1)\n", "#                     image = dataloaders[\"testing\"].dataset.transform_img(image)\n", "#                     return np.clip(\n", "#                         (image.numpy() * in_std + in_mean) * 255, 0, 255\n", "#                     ).astype(np.uint8)\n", "# \n", "#                 def mask_transform(mask):\n", "#                     return dataloaders[\"testing\"].dataset.transform_mask(mask).numpy()\n", "# \n", "#                 patchcore.utils.plot_segmentation_images(\n", "#                     results_path,\n", "#                     image_paths,\n", "#                     segmentations,\n", "#                     scores,\n", "#                     mask_paths,\n", "#                     image_transform=image_transform,\n", "#                     mask_transform=mask_transform,\n", "#                 )\n", "# \n", "#             LOGGER.info(\"Computing evaluation metrics.\")\n", "#             # Compute Image-level AUROC scores for all images.\n", "#             auroc = patchcore.metrics.compute_imagewise_retrieval_metrics(\n", "#                 scores, anomaly_labels\n", "#             )[\"auroc\"]\n", "# \n", "#             # Compute PRO score & PW Auroc for all images\n", "#             pixel_scores = patchcore.metrics.compute_pixelwise_retrieval_metrics(\n", "#                 segmentations, masks_gt\n", "#             )\n", "#             full_pixel_auroc = pixel_scores[\"auroc\"]\n", "# \n", "#             # Compute PRO score & PW Auroc only for images with anomalies\n", "#             sel_idxs = []\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/load_and_evaluate_patchcore.py\n", "# --------------------------------------------------\n", "#     device_context = (\n", "#         torch.cuda.device(\"cuda:{}\".format(device.index))\n", "#         if \"cuda\" in device.type.lower()\n", "#         else contextlib.suppress()\n", "#     )\n", "# \n", "#     result_collect = []\n", "# \n", "#     dataloader_iter, n_dataloaders = methods[\"get_dataloaders_iter\"]\n", "#     dataloader_iter = dataloader_iter(seed)\n", "#     patchcore_iter, n_patchcores = methods[\"get_patchcore_iter\"]\n", "#     patchcore_iter = patchcore_iter(device)\n", "#     if not (n_dataloaders == n_patchcores or n_patchcores == 1):\n", "#         raise ValueError(\n", "#             \"Please ensure that #PatchCores == #Datasets or #PatchCores == 1!\"\n", "#         )\n", "# \n", "#     for dataloader_count, dataloaders in enumerate(dataloader_iter):\n", "#         LOGGER.info(\n", "#             \"Evaluating dataset [{}] ({}/{})...\".format(\n", "#                 dataloaders[\"testing\"].name, dataloader_count + 1, n_dataloaders\n", "#             )\n", "#         )\n", "# \n", "#         patchcore.utils.fix_seeds(seed, device)\n", "# \n", "#         dataset_name = dataloaders[\"testing\"].name\n", "# \n", "#         with device_context:\n", "# \n", "#             torch.cuda.empty_cache()\n", "#             if dataloader_count < n_patchcores:\n", "#                 PatchCore_list = next(patchcore_iter)\n", "# \n", "#             aggregator = {\"scores\": [], \"segmentations\": []}\n", "#             for i, PatchCore in enumerate(PatchCore_list):\n", "#                 torch.cuda.empty_cache()\n", "#                 LOGGER.info(\n", "#                     \"Embedding test data with models ({}/{})\".format(\n", "#                         i + 1, len(PatchCore_list)\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_sampler.py\n", "# --------------------------------------------------\n", "# \n", "#     sampling_percentage = 0.1\n", "#     dimension_to_project_features_to = 64\n", "# \n", "#     model = sampler.ApproximateGreedyCoresetSampler(\n", "#         percentage=sampling_percentage,\n", "#         device=torch.device(\"cpu\"),\n", "#         number_of_starting_points=10,\n", "#         dimension_to_project_features_to=dimension_to_project_features_to,\n", "#     )\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     assert subsampled_features.shape[-1] == feature_dimension\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/run_patchcore.py\n", "# --------------------------------------------------\n", "#     log_project,\n", "#     save_segmentation_images,\n", "#     save_patchcore_model,\n", "# ):\n", "#     methods = {key: item for (key, item) in methods}\n", "# \n", "#     run_save_path = patchcore.utils.create_storage_folder(\n", "#         results_path, log_project, log_group, mode=\"iterate\"\n", "#     )\n", "# \n", "#     list_of_dataloaders = methods[\"get_dataloaders\"](seed)\n", "# \n", "#     device = patchcore.utils.set_torch_device(gpu)\n", "#     # Device context here is specifically set and used later\n", "#     # because there was GPU memory-bleeding which I could only fix with\n", "#     # context managers.\n", "#     device_context = (\n", "#         torch.cuda.device(\"cuda:{}\".format(device.index))\n", "#         if \"cuda\" in device.type.lower()\n", "#         else contextlib.suppress()\n", "#     )\n", "# \n", "#     result_collect = []\n", "# \n", "#     for dataloader_count, dataloaders in enumerate(list_of_dataloaders):\n", "#         LOGGER.info(\n", "#             \"Evaluating dataset [{}] ({}/{})...\".format(\n", "#                 dataloaders[\"training\"].name,\n", "#                 dataloader_count + 1,\n", "#                 len(list_of_dataloaders),\n", "#             )\n", "#         )\n", "# \n", "#         patchcore.utils.fix_seeds(seed, device)\n", "# \n", "#         dataset_name = dataloaders[\"training\"].name\n", "# \n", "#         with device_context:\n", "#             torch.cuda.empty_cache()\n", "#             imagesize = dataloaders[\"training\"].dataset.imagesize\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/datasets/mvtec.py\n", "# --------------------------------------------------\n", "#     def __init__(\n", "#         self,\n", "#         source,\n", "#         classname,\n", "#         resize=256,\n", "#         imagesize=224,\n", "#         split=DatasetSplit.TRAIN,\n", "#         train_val_split=1.0,\n", "#         **kwargs,\n", "#     ):\n", "#         \"\"\"\n", "#         Args:\n", "#             source: [str]. Path to the MVTec data folder.\n", "#             classname: [str or None]. Name of MVTec class that should be\n", "#                        provided in this dataset. If None, the datasets\n", "#                        iterates over all available images.\n", "#             resize: [int]. (Square) Size the loaded image initially gets\n", "#                     resized to.\n", "#             imagesize: [int]. (Square) Size the resized loaded image gets\n", "#                        (center-)cropped to.\n", "#             split: [enum-option]. Indicates if training or test split of the\n", "#                    data should be used. Has to be an option taken from\n", "#                    DatasetSplit, e.g. mvtec.DatasetSplit.TRAIN. Note that\n", "#                    mvtec.DatasetSplit.TEST will also load mask data.\n", "#         \"\"\"\n", "#         super().__init__()\n", "#         self.source = source\n", "#         self.split = split\n", "#         self.classnames_to_use = [classname] if classname is not None else _CLASSNAMES\n", "#         self.train_val_split = train_val_split\n", "# \n", "#         self.imgpaths_per_class, self.data_to_iterate = self.get_image_data()\n", "# \n", "#         self.transform_img = [\n", "#             transforms.Resize(resize),\n", "#             transforms.CenterCrop(imagesize),\n", "#             transforms.To<PERSON><PERSON>or(),\n", "#             transforms.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD),\n", "#         ]\n", "#         self.transform_img = transforms.Compose(self.transform_img)\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/utils.py\n", "# --------------------------------------------------\n", "#             header = [\"Row Names\"] + header\n", "# \n", "#         csv_writer.writerow(header)\n", "#         for i, result_list in enumerate(results):\n", "#             csv_row = result_list\n", "#             if row_names is not None:\n", "#                 csv_row = [row_names[i]] + result_list\n", "#             csv_writer.writerow(csv_row)\n", "#         mean_scores = list(mean_metrics.values())\n", "#         if row_names is not None:\n", "#             mean_scores = [\"Mean\"] + mean_scores\n", "#         csv_writer.writerow(mean_scores)\n", "# \n", "#     mean_metrics = {\"mean_{0}\".format(key): item for key, item in mean_metrics.items()}\n", "#     return mean_metrics\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/backbones.py\n", "# --------------------------------------------------\n", "#     \"efficientnet_b5\": 'timm.create_model(\"tf_efficientnet_b5\", pretrained=True)',\n", "#     \"efficientnet_b3\": 'timm.create_model(\"tf_efficientnet_b3\", pretrained=True)',\n", "#     \"efficientnet_b1\": 'timm.create_model(\"tf_efficientnet_b1\", pretrained=True)',\n", "#     \"efficientnetv2_m\": 'timm.create_model(\"tf_efficientnetv2_m\", pretrained=True)',\n", "#     \"efficientnetv2_l\": 'timm.create_model(\"tf_efficientnetv2_l\", pretrained=True)',\n", "#     \"efficientnet_b3a\": 'timm.create_model(\"efficientnet_b3a\", pretrained=True)',\n", "# }\n", "# \n", "# \n", "# def load(name):\n", "#     return eval(_BACKBONES[name])\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# setup.py\n", "# --------------------------------------------------\n", "# from pathlib import Path\n", "# \n", "# from setuptools import find_packages\n", "# from setuptools import setup\n", "# \n", "# package_path = Path(__file__).parent\n", "# \n", "# version_path = Path(__file__).parent.joinpath(f\"src/patchcore/VERSION\")\n", "# version = version_path.read_text().strip()\n", "# install_requires = (\n", "#     Path(__file__).parent.joinpath(\"requirements.txt\").read_text().splitlines()\n", "# )\n", "# data_files = [\n", "#     str(version_path.relative_to(package_path)),\n", "# ]\n", "# \n", "# setup(\n", "#     name=\"patchcore\",\n", "#     version=version,\n", "#     install_requires=install_requires,\n", "#     package_dir={\"\": \"src\"},\n", "#     packages=find_packages(\"src\"),\n", "#     include_package_data=True,\n", "#     data_files=data_files,\n", "# )\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/datasets/mvtec.py\n", "# --------------------------------------------------\n", "#                     if self.split == DatasetSplit.TEST and anomaly!= \"good\":\n", "#                         data_tuple.append(maskpaths_per_class[classname][anomaly][i])\n", "#                     else:\n", "#                         data_tuple.append(None)\n", "#                     data_to_iterate.append(data_tuple)\n", "# \n", "#         return imgpaths_per_class, data_to_iterate\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/utils.py\n", "# --------------------------------------------------\n", "#     if with_cuda:\n", "#         torch.cuda.manual_seed(seed)\n", "#         torch.cuda.manual_seed_all(seed)\n", "#         torch.backends.cudnn.deterministic = True\n", "# \n", "# \n", "# def compute_and_store_final_results(\n", "#     results_path,\n", "#     results,\n", "#     row_names=None,\n", "#     column_names=[\n", "#         \"Instance AUROC\",\n", "#         \"Full Pixel AUROC\",\n", "#         \"Full PRO\",\n", "#         \"Anomaly Pixel AUROC\",\n", "#         \"Anomaly PRO\",\n", "#     ],\n", "# ):\n", "#     \"\"\"Store computed results as CSV file.\n", "# \n", "#     Args:\n", "#         results_path: [str] Where to store result csv.\n", "#         results: [List[List]] List of lists containing results per dataset,\n", "#                  with results[i][0] == 'dataset_name' and results[i][1:6] =\n", "#                  [instance_auroc, full_pixelwisew_auroc, full_pro,\n", "#                  anomaly-only_pw_auroc, anomaly-only_pro]\n", "#     \"\"\"\n", "#     if row_names is not None:\n", "#         assert len(row_names) == len(results), \"#Rownames!= #Result-rows.\"\n", "# \n", "#     mean_metrics = {}\n", "#     for i, result_key in enumerate(column_names):\n", "#         mean_metrics[result_key] = np.mean([x[i] for x in results])\n", "#         LOGGER.info(\"{0}: {1:3.3f}\".format(result_key, mean_metrics[result_key]))\n", "# \n", "#     savename = os.path.join(results_path, \"results.csv\")\n", "#     with open(savename, \"w\") as csv_file:\n", "#         csv_writer = csv.writer(csv_file, delimiter=\",\")\n", "#         header = column_names\n", "#         if row_names is not None:\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/load_and_evaluate_patchcore.py\n", "# --------------------------------------------------\n", "# # NN on GPU.\n", "# @click.option(\"--faiss_on_gpu\", is_flag=True)\n", "# @click.option(\"--faiss_num_workers\", type=int, default=8)\n", "# def patch_core_loader(patch_core_paths, faiss_on_gpu, faiss_num_workers):\n", "#     def get_patchcore_iter(device):\n", "#         for patch_core_path in patch_core_paths:\n", "#             loaded_patchcores = []\n", "#             gc.collect()\n", "#             n_patchcores = len(\n", "#                 [x for x in os.listdir(patch_core_path) if \".faiss\" in x]\n", "#             )\n", "#             if n_patchcores == 1:\n", "#                 nn_method = patchcore.common.FaissNN(faiss_on_gpu, faiss_num_workers)\n", "#                 patchcore_instance = patchcore.patchcore.PatchCore(device)\n", "#                 patchcore_instance.load_from_path(\n", "#                     load_path=patch_core_path, device=device, nn_method=nn_method\n", "#                 )\n", "#                 loaded_patchcores.append(patchcore_instance)\n", "#             else:\n", "#                 for i in range(n_patchcores):\n", "#                     nn_method = patchcore.common.FaissNN(\n", "#                         faiss_on_gpu, faiss_num_workers\n", "#                     )\n", "#                     patchcore_instance = patchcore.patchcore.PatchCore(device)\n", "#                     patchcore_instance.load_from_path(\n", "#                         load_path=patch_core_path,\n", "#                         device=device,\n", "#                         nn_method=nn_method,\n", "#                         prepend=\"Ensemble-{}-{}_\".format(i + 1, n_patchcores),\n", "#                     )\n", "#                     loaded_patchcores.append(patchcore_instance)\n", "# \n", "#             yield loaded_patchcores\n", "# \n", "#     return (\"get_patchcore_iter\", [get_patchcore_iter, len(patch_core_paths)])\n", "# \n", "# \n", "# @main.command(\"dataset\")\n", "# @click.argument(\"name\", type=str)\n", "# @click.argument(\"data_path\", type=click.Path(exists=True, file_okay=False))\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_patchcore.py\n", "# --------------------------------------------------\n", "#         assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "# \n", "#     assert len(scores) == 5\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/datasets/mvtec.py\n", "# --------------------------------------------------\n", "#             maskpath = os.path.join(self.source, classname, \"ground_truth\")\n", "#             anomaly_types = os.listdir(classpath)\n", "# \n", "#             imgpaths_per_class[classname] = {}\n", "#             maskpaths_per_class[classname] = {}\n", "# \n", "#             for anomaly in anomaly_types:\n", "#                 anomaly_path = os.path.join(classpath, anomaly)\n", "#                 anomaly_files = sorted(os.listdir(anomaly_path))\n", "#                 imgpaths_per_class[classname][anomaly] = [\n", "#                     os.path.join(anomaly_path, x) for x in anomaly_files\n", "#                 ]\n", "# \n", "#                 if self.train_val_split < 1.0:\n", "#                     n_images = len(imgpaths_per_class[classname][anomaly])\n", "#                     train_val_split_idx = int(n_images * self.train_val_split)\n", "#                     if self.split == DatasetSplit.TRAIN:\n", "#                         imgpaths_per_class[classname][anomaly] = imgpaths_per_class[\n", "#                             classname\n", "#                         ][anomaly][:train_val_split_idx]\n", "#                     elif self.split == DatasetSplit.VAL:\n", "#                         imgpaths_per_class[classname][anomaly] = imgpaths_per_class[\n", "#                             classname\n", "#                         ][anomaly][train_val_split_idx:]\n", "# \n", "#                 if self.split == DatasetSplit.TEST and anomaly!= \"good\":\n", "#                     anomaly_mask_path = os.path.join(maskpath, anomaly)\n", "#                     anomaly_mask_files = sorted(os.listdir(anomaly_mask_path))\n", "#                     maskpaths_per_class[classname][anomaly] = [\n", "#                         os.path.join(anomaly_mask_path, x) for x in anomaly_mask_files\n", "#                     ]\n", "#                 else:\n", "#                     maskpaths_per_class[classname][\"good\"] = None\n", "# \n", "#         # Unrolls the data dictionary to an easy-to-iterate list.\n", "#         data_to_iterate = []\n", "#         for classname in sorted(imgpaths_per_class.keys()):\n", "#             for anomaly in sorted(imgpaths_per_class[classname].keys()):\n", "#                 for i, image_path in enumerate(imgpaths_per_class[classname][anomaly]):\n", "#                     data_tuple = [classname, anomaly, image_path]\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/common.py\n", "# --------------------------------------------------\n", "#                 [[bs x d_i] for i in n] Contains a list of\n", "#                 np.arrays for all training images corresponding to respective\n", "#                 features VECTORS (or maps, but will be resized) produced by\n", "#                 some backbone network which should be used for image-level\n", "#                 anomaly detection.\n", "#         \"\"\"\n", "#         self.detection_features = self.feature_merger.merge(\n", "#             detection_features,\n", "#         )\n", "#         self.nn_method.fit(self.detection_features)\n", "# \n", "#     def predict(\n", "#         self, query_features: List[np.ndarray]\n", "#     ) -> Union[np.ndarray, np.ndarray, np.ndarray]:\n", "#         \"\"\"Predicts anomaly score.\n", "# \n", "#         Searches for nearest neighbours of test images in all\n", "#         support training images.\n", "# \n", "#         Args:\n", "#              detection_query_features: [dict of np.arrays] List of np.arrays\n", "#                  corresponding to the test features generated by\n", "#                  some backbone network.\n", "#         \"\"\"\n", "#         query_features = self.feature_merger.merge(\n", "#             query_features,\n", "#         )\n", "#         query_distances, query_nns = self.imagelevel_nn(query_features)\n", "#         anomaly_scores = np.mean(query_distances, axis=-1)\n", "#         return anomaly_scores, query_distances, query_nns\n", "# \n", "#     @staticmethod\n", "#     def _detection_file(folder, prepend=\"\"):\n", "#         return os.path.join(folder, prepend + \"nnscorer_features.pkl\")\n", "# \n", "#     @staticmethod\n", "#     def _index_file(folder, prepend=\"\"):\n", "#         return os.path.join(folder, prepend + \"nnscorer_search_index.faiss\")\n", "# \n", "#     @staticmethod\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/load_and_evaluate_patchcore.py\n", "# --------------------------------------------------\n", "# \n", "# \n", "# if __name__ == \"__main__\":\n", "#     logging.basicConfig(level=logging.INFO)\n", "#     LOGGER.info(\"Command line arguments: {}\".format(\" \".join(sys.argv)))\n", "#     main()\n", "# --------------------------------------------------\n", "\n", "\n", "# Current source file:\n", "# src/patchcore/common.py\n", "# --------------------------------------------------\n", "\n", "import copy\n", "import os\n", "import pickle\n", "from typing import List\n", "from typing import Union\n", "\n", "import faiss\n", "import numpy as np\n", "import scipy.ndimage as ndimage\n", "import torch\n", "import torch.nn.functional as F\n", "\n", "\n", "class FaissNN(object):\n", "    def __init__(self, on_gpu: bool = False, num_workers: int = 4) -> None:\n", "        \"\"\"FAISS Nearest neighbourhood search.\n", "\n", "        Args:\n", "            on_gpu: If set true, nearest neighbour searches are done on GPU.\n", "            num_workers: Number of workers to use with FAISS for similarity search.\n", "        \"\"\"\n", "        faiss.omp_set_num_threads(num_workers)\n", "        self.on_gpu = on_gpu\n", "        self.search_index = None\n", "\n", "    def _gpu_cloner_options(self):\n", "        return faiss.GpuClonerOptions()\n", "\n", "    def _index_to_gpu(self, index):\n", "        if self.on_gpu:\n", "            # For the non-gpu faiss python package, there is no GpuClonerOptions\n", "            # so we can not make a default in the function header.\n", "            return faiss.index_cpu_to_gpu(\n", "                faiss.StandardGpuResources(), 0, index, self._gpu_cloner_options()\n", "            )\n", "        return index\n", "\n", "    def _index_to_cpu(self, index):\n", "        if self.on_gpu:\n", "            return faiss.index_gpu_to_cpu(index)\n", "        return index\n", "\n", "    def _create_index(self, dimension):\n", "        if self.on_gpu:\n", "            return faiss.GpuIndexFlatL2(\n", "                faiss.StandardGpuResources(), dimension, faiss.GpuIndexFlatConfig()\n", "            )\n", "        return faiss.IndexFlatL2(dimension)\n", "\n", "    def fit(self, features: np.n<PERSON>ray) -> None:\n", "        \"\"\"\n", "        Adds features to the FAISS search index.\n", "\n", "        Args:\n", "            features: Array of size NxD.\n", "        \"\"\"\n", "        if self.search_index:\n", "            self.reset_index()\n", "        self.search_index = self._create_index(features.shape[-1])\n", "        self._train(self.search_index, features)\n", "        self.search_index.add(features)\n", "\n", "    def _train(self, _index, _features):\n", "        pass\n", "\n", "    def run(\n", "        self,\n", "        n_nearest_neighbours,\n", "        query_features: np.n<PERSON>ray,\n", "        index_features: np.ndarray = None,\n", "    ) -> Union[np.ndarray, np.ndarray, np.ndarray]:\n", "        \"\"\"\n", "        Returns distances and indices of nearest neighbour search.\n", "\n", "        Args:\n", "            query_features: Features to retrieve.\n", "            index_features: [optional] Index features to search in.\n", "        \"\"\"\n", "        if index_features is None:\n", "            return self.search_index.search(query_features, n_nearest_neighbours)\n", "\n", "        # Build a search index just for this search.\n", "        search_index = self._create_index(index_features.shape[-1])\n", "        self._train(search_index, index_features)\n", "        search_index.add(index_features)\n", "        return search_index.search(query_features, n_nearest_neighbours)\n", "\n", "    def save(self, filename: str) -> None:\n", "        faiss.write_index(self._index_to_cpu(self.search_index), filename)\n", "\n", "    def load(self, filename: str) -> None:\n", "        self.search_index = self._index_to_gpu(faiss.read_index(filename))\n", "\n", "    def reset_index(self):\n", "        if self.search_index:\n", "            self.search_index.reset()\n", "            self.search_index = None\n", "\n", "\n", "class ApproximateFaissNN(FaissNN):\n", "    def _train(self, index, features):\n", "        index.train(features)\n", "\n", "    def _gpu_cloner_options(self):\n", "        cloner = faiss.GpuClonerOptions()\n", "        cloner.useFloat16 = True\n", "        return cloner\n", "\n", "    def _create_index(self, dimension):\n", "<fim_suffix>\n", "\n", "class _BaseMerger:\n", "    def __init__(self):\n", "        \"\"\"Merges feature embedding by name.\"\"\"\n", "\n", "    def merge(self, features: list):\n", "        features = [self._reduce(feature) for feature in features]\n", "        return np.concatenate(features, axis=1)\n", "\n", "\n", "class AverageMerger(_BaseMerger):\n", "    @staticmethod\n", "    def _reduce(features):\n", "        # NxCxWxH -> NxC\n", "        return features.reshape([features.shape[0], features.shape[1], -1]).mean(\n", "            axis=-1\n", "        )\n", "\n", "\n", "class ConcatMerger(_BaseMerger):\n", "    @staticmethod\n", "    def _reduce(features):\n", "        # NxCxWxH -> NxCWH\n", "        return features.reshape(len(features), -1)\n", "\n", "\n", "class Preprocessing(torch.nn.Module):\n", "    def __init__(self, input_dims, output_dim):\n", "        super(Preprocessing, self).__init__()\n", "        self.input_dims = input_dims\n", "        self.output_dim = output_dim\n", "\n", "        self.preprocessing_modules = torch.nn.ModuleList()\n", "        for input_dim in input_dims:\n", "            module = MeanMapper(output_dim)\n", "            self.preprocessing_modules.append(module)\n", "\n", "    def forward(self, features):\n", "        _features = []\n", "        for module, feature in zip(self.preprocessing_modules, features):\n", "            _features.append(module(feature))\n", "        return torch.stack(_features, dim=1)\n", "\n", "\n", "class MeanMapper(torch.nn.Module):\n", "    def __init__(self, preprocessing_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.preprocessing_dim = preprocessing_dim\n", "\n", "    def forward(self, features):\n", "        features = features.reshape(len(features), 1, -1)\n", "        return F.adaptive_avg_pool1d(features, self.preprocessing_dim).squeeze(1)\n", "\n", "\n", "class Aggregator(torch.nn.Module):\n", "    def __init__(self, target_dim):\n", "        super(Aggregator, self).__init__()\n", "        self.target_dim = target_dim\n", "\n", "    def forward(self, features):\n", "        \"\"\"Returns reshaped and average pooled features.\"\"\"\n", "        # batchsize x number_of_layers x input_dim -> batchsize x target_dim\n", "        features = features.reshape(len(features), 1, -1)\n", "        features = F.adaptive_avg_pool1d(features, self.target_dim)\n", "        return features.reshape(len(features), -1)\n", "\n", "\n", "class RescaleSegmentor:\n", "    def __init__(self, device, target_size=224):\n", "        self.device = device\n", "        self.target_size = target_size\n", "        self.smoothing = 4\n", "\n", "    def convert_to_segmentation(self, patch_scores):\n", "\n", "        with torch.no_grad():\n", "            if isinstance(patch_scores, np.ndarray):\n", "                patch_scores = torch.from_numpy(patch_scores)\n", "            _scores = patch_scores.to(self.device)\n", "            _scores = _scores.unsqueeze(1)\n", "            _scores = F.interpolate(\n", "                _scores, size=self.target_size, mode=\"bilinear\", align_corners=False\n", "            )\n", "            _scores = _scores.squeeze(1)\n", "            patch_scores = _scores.cpu().numpy()\n", "\n", "        return [\n", "            ndimage.gaussian_filter(patch_score, sigma=self.smoothing)\n", "            for patch_score in patch_scores\n", "        ]\n", "\n", "\n", "class NetworkFeatureAggregator(torch.nn.Module):\n", "    \"\"\"Efficient extraction of network features.\"\"\"\n", "\n", "    def __init__(self, backbone, layers_to_extract_from, device):\n", "        super(NetworkFeatureAggregator, self).__init__()\n", "        \"\"\"Extraction of network features.\n", "\n", "        Runs a network only to the last layer of the list of layers where\n", "        network features should be extracted from.\n", "\n", "        Args:\n", "            backbone: torchvision.model\n", "            layers_to_extract_from: [list of str]\n", "        \"\"\"\n", "        self.layers_to_extract_from = layers_to_extract_from\n", "        self.backbone = backbone\n", "        self.device = device\n", "        if not hasattr(backbone, \"hook_handles\"):\n", "            self.backbone.hook_handles = []\n", "        for handle in self.backbone.hook_handles:\n", "            handle.remove()\n", "        self.outputs = {}\n", "\n", "        for extract_layer in layers_to_extract_from:\n", "            forward_hook = ForwardHook(\n", "                self.outputs, extract_layer, layers_to_extract_from[-1]\n", "            )\n", "            if \".\" in extract_layer:\n", "                extract_block, extract_idx = extract_layer.split(\".\")\n", "                network_layer = backbone.__dict__[\"_modules\"][extract_block]\n", "                if<fim_middle>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Left"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["<fim_prefix># Current source file:\n", "# src/patchcore/common.py\n", "# --------------------------------------------------\n", "\n", "# Here are some relevant code fragments from other files of the repo:\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/sampler.py\n", "# --------------------------------------------------\n", "#                     features, features[select_idx : select_idx + 1]  # noqa: E203\n", "#                 )\n", "#                 approximate_coreset_anchor_distances = torch.cat(\n", "#                     [approximate_coreset_anchor_distances, coreset_select_distance],\n", "#                     dim=-1,\n", "#                 )\n", "#                 approximate_coreset_anchor_distances = torch.min(\n", "#                     approximate_coreset_anchor_distances, dim=1\n", "#                 ).values.reshape(-1, 1)\n", "# \n", "#         return np.array(coreset_indices)\n", "# \n", "# \n", "# class RandomSampler(BaseSampler):\n", "#     def __init__(self, percentage: float):\n", "#         super().__init__(percentage)\n", "# \n", "#     def run(\n", "#         self, features: Union[torch.Tensor, np.n<PERSON><PERSON>]\n", "#     ) -> Union[torch.Tensor, np.n<PERSON>ray]:\n", "#         \"\"\"Randomly samples input feature collection.\n", "# \n", "#         Args:\n", "#             features: [N x D]\n", "#         \"\"\"\n", "#         num_random_samples = int(len(features) * self.percentage)\n", "#         subset_indices = np.random.choice(\n", "#             len(features), num_random_samples, replace=False\n", "#         )\n", "#         subset_indices = np.array(subset_indices)\n", "#         return features[subset_indices]\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/sampler.py\n", "# --------------------------------------------------\n", "#     def _compute_batchwise_differences(\n", "#         matrix_a: torch.<PERSON><PERSON>, matrix_b: torch.Tensor\n", "#     ) -> torch.Tensor:\n", "#         \"\"\"Computes batchwise Euclidean distances using PyTorch.\"\"\"\n", "#         a_times_a = matrix_a.unsqueeze(1).bmm(matrix_a.unsqueeze(2)).reshape(-1, 1)\n", "#         b_times_b = matrix_b.unsqueeze(1).bmm(matrix_b.unsqueeze(2)).reshape(1, -1)\n", "#         a_times_b = matrix_a.mm(matrix_b.T)\n", "# \n", "#         return (-2 * a_times_b + a_times_a + b_times_b).clamp(0, None).sqrt()\n", "# \n", "#     def _compute_greedy_coreset_indices(self, features: torch.Tensor) -> np.ndarray:\n", "#         \"\"\"Runs iterative greedy coreset selection.\n", "# \n", "#         Args:\n", "#             features: [NxD] input feature bank to sample.\n", "#         \"\"\"\n", "#         distance_matrix = self._compute_batchwise_differences(features, features)\n", "#         coreset_anchor_distances = torch.norm(distance_matrix, dim=1)\n", "# \n", "#         coreset_indices = []\n", "#         num_coreset_samples = int(len(features) * self.percentage)\n", "# \n", "#         for _ in range(num_coreset_samples):\n", "#             select_idx = torch.argmax(coreset_anchor_distances).item()\n", "#             coreset_indices.append(select_idx)\n", "# \n", "#             coreset_select_distance = distance_matrix[\n", "#                 :, select_idx : select_idx + 1  # noqa E203\n", "#             ]\n", "#             coreset_anchor_distances = torch.cat(\n", "#                 [coreset_anchor_distances.unsqueeze(-1), coreset_select_distance], dim=1\n", "#             )\n", "#             coreset_anchor_distances = torch.min(coreset_anchor_distances, dim=1).values\n", "# \n", "#         return np.array(coreset_indices)\n", "# \n", "# \n", "# class ApproximateGreedyCoresetSampler(GreedyCoresetSampler):\n", "#     def __init__(\n", "#         self,\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/sampler.py\n", "# --------------------------------------------------\n", "#         self,\n", "#         percentage: float,\n", "#         device: torch.device,\n", "#         dimension_to_project_features_to=128,\n", "#     ):\n", "#         \"\"\"Greedy Coreset sampling base class.\"\"\"\n", "#         super().__init__(percentage)\n", "# \n", "#         self.device = device\n", "#         self.dimension_to_project_features_to = dimension_to_project_features_to\n", "# \n", "#     def _reduce_features(self, features):\n", "#         if features.shape[1] == self.dimension_to_project_features_to:\n", "#             return features\n", "#         mapper = torch.nn.Linear(\n", "#             features.shape[1], self.dimension_to_project_features_to, bias=False\n", "#         )\n", "#         _ = mapper.to(self.device)\n", "#         features = features.to(self.device)\n", "#         return mapper(features)\n", "# \n", "#     def run(\n", "#         self, features: Union[torch.Tensor, np.n<PERSON><PERSON>]\n", "#     ) -> Union[torch.Tensor, np.n<PERSON>ray]:\n", "#         \"\"\"Subsamples features using Greedy Coreset.\n", "# \n", "#         Args:\n", "#             features: [N x D]\n", "#         \"\"\"\n", "#         if self.percentage == 1:\n", "#             return features\n", "#         self._store_type(features)\n", "#         if isinstance(features, np.ndarray):\n", "#             features = torch.from_numpy(features)\n", "#         reduced_features = self._reduce_features(features)\n", "#         sample_indices = self._compute_greedy_coreset_indices(reduced_features)\n", "#         features = features[sample_indices]\n", "#         return self._restore_type(features)\n", "# \n", "#     @staticmethod\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/sampler.py\n", "# --------------------------------------------------\n", "# import abc\n", "# from typing import Union\n", "# \n", "# import numpy as np\n", "# import torch\n", "# import tqdm\n", "# \n", "# \n", "# class IdentitySampler:\n", "#     def run(\n", "#         self, features: Union[torch.Tensor, np.n<PERSON><PERSON>]\n", "#     ) -> Union[torch.Tensor, np.n<PERSON>ray]:\n", "#         return features\n", "# \n", "# \n", "# class BaseSampler(abc.ABC):\n", "#     def __init__(self, percentage: float):\n", "#         if not 0 < percentage < 1:\n", "#             raise ValueError(\"Percentage value not in (0, 1).\")\n", "#         self.percentage = percentage\n", "# \n", "#     @abc.abstractmethod\n", "#     def run(\n", "#         self, features: Union[torch.Tensor, np.n<PERSON><PERSON>]\n", "#     ) -> Union[torch.Tensor, np.n<PERSON>ray]:\n", "#         pass\n", "# \n", "#     def _store_type(self, features: Union[torch.Tensor, np.ndarray]) -> None:\n", "#         self.features_is_numpy = isinstance(features, np.ndarray)\n", "#         if not self.features_is_numpy:\n", "#             self.features_device = features.device\n", "# \n", "#     def _restore_type(self, features: torch.Tensor) -> Union[torch.Tensor, np.ndarray]:\n", "#         if self.features_is_numpy:\n", "#             return features.cpu().numpy()\n", "#         return features.to(self.features_device)\n", "# \n", "# \n", "# class GreedyCoresetSampler(BaseSampler):\n", "#     def __init__(\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# src/patchcore/common.py\n", "# --------------------------------------------------\n", "#     def _save(filename, features):\n", "#         if features is None:\n", "#             return\n", "#         with open(filename, \"wb\") as save_file:\n", "#             pickle.dump(features, save_file, pickle.HIGHEST_PROTOCOL)\n", "# \n", "#     @staticmethod\n", "#     def _load(filename: str):\n", "#         with open(filename, \"rb\") as load_file:\n", "#             return pickle.load(load_file)\n", "# \n", "#     def save(\n", "#         self,\n", "#         save_folder: str,\n", "#         save_features_separately: bool = False,\n", "#         prepend: str = \"\",\n", "#     ) -> None:\n", "#         self.nn_method.save(self._index_file(save_folder, prepend))\n", "#         if save_features_separately:\n", "#             self._save(\n", "#                 self._detection_file(save_folder, prepend), self.detection_features\n", "#             )\n", "# \n", "#     def save_and_reset(self, save_folder: str) -> None:\n", "#         self.save(save_folder)\n", "#         self.nn_method.reset_index()\n", "# \n", "#     def load(self, load_folder: str, prepend: str = \"\") -> None:\n", "#         self.nn_method.load(self._index_file(load_folder, prepend))\n", "#         if os.path.exists(self._detection_file(load_folder, prepend)):\n", "#             self.detection_features = self._load(\n", "#                 self._detection_file(load_folder, prepend)\n", "#             )\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_sampler.py\n", "# --------------------------------------------------\n", "# @pytest.mark.skipif(not torch.cuda.is_available(), reason=\"Fails for non-GPU machine.\")\n", "# def test_approximate_greedy_coreset_sampling():\n", "#     feature_dimension = 2\n", "#     init_features = _dummy_features(feature_dimension)\n", "# \n", "#     sampling_percentage = 0.1\n", "#     model = sampler.ApproximateGreedyCoresetSampler(\n", "#         percentage=sampling_percentage,\n", "#         device=torch.device(\"cpu\"),\n", "#         number_of_starting_points=10,\n", "#         dimension_to_project_features_to=feature_dimension,\n", "#     )\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     target_num_subsampled_features = int(len(init_features) * sampling_percentage)\n", "#     assert len(subsampled_features) == target_num_subsampled_features\n", "#     assert (\n", "#         len(torch.unique(subsampled_features, dim=0)) == target_num_subsampled_features\n", "#     )\n", "# \n", "# \n", "# @pytest.mark.skipif(not torch.cuda.is_available(), reason=\"Fails for non-GPU machine.\")\n", "# def test_coreset_sampling_on_same_samples():\n", "#     feature_dimension = 2\n", "#     init_features = _dummy_constant_features(5000, feature_dimension)\n", "# \n", "#     sampling_percentage = 0.1\n", "#     model = sampler.ApproximateGreedyCoresetSampler(\n", "#         percentage=sampling_percentage,\n", "#         device=torch.device(\"cpu\"),\n", "#         number_of_starting_points=10,\n", "#         dimension_to_project_features_to=feature_dimension,\n", "#     )\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     target_num_subsampled_features = int(len(init_features) * sampling_percentage)\n", "#     assert len(subsampled_features) == target_num_subsampled_features\n", "#     assert len(torch.unique(subsampled_features, dim=0)) == 1\n", "# \n", "# \n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_common.py\n", "# --------------------------------------------------\n", "# def test_average_merger_output():\n", "#     input_features = [np.ones([2, 3, 4, 5])]\n", "# \n", "#     merger = common.AverageMerger()\n", "#     output_features = merger.merge(input_features)\n", "#     assert np.all(output_features == 1.0)\n", "# \n", "# \n", "# def test_concat_merger_shape():\n", "#     input_features = []\n", "#     input_features.append(np.arange(2 * 3 * 4 * 5).reshape([2, 3, 4, 5]))\n", "#     input_features.append(2 * np.arange(2 * 3 * 4 * 5).reshape([2, 4, 3, 5]))\n", "# \n", "#     merger = common.ConcatMerger()\n", "#     output_features = merger.merge([input_features[0]])\n", "#     assert np.all(output_features.shape == (2, 3 * 4 * 5))\n", "# \n", "#     merger = common.ConcatMerger()\n", "#     output_features = merger.merge(input_features)\n", "#     assert np.all(output_features.shape == (2, 3 * 4 * 5 + 4 * 3 * 5))\n", "# \n", "# \n", "# def test_concat_merger_output():\n", "#     input_features = []\n", "#     input_features.append(np.ones([2, 3, 4, 5]))\n", "#     input_features.append(2 * np.ones([2, 3, 4, 5]))\n", "# \n", "#     merger = common.ConcatMerger()\n", "#     output_features = merger.merge([input_features[0]])\n", "#     assert np.all(output_features == 1.0)\n", "# \n", "#     merger = common.ConcatMerger()\n", "#     output_features = merger.merge(input_features)\n", "#     assert np.all(output_features[:, : 3 * 4 * 5] == 1.0)\n", "#     assert np.all(output_features[:, 3 * 4 * 5 :] == 2.0)\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_sampler.py\n", "# --------------------------------------------------\n", "# \n", "#     sampling_percentage = 0.1\n", "#     dimension_to_project_features_to = 64\n", "# \n", "#     model = sampler.ApproximateGreedyCoresetSampler(\n", "#         percentage=sampling_percentage,\n", "#         device=torch.device(\"cpu\"),\n", "#         number_of_starting_points=10,\n", "#         dimension_to_project_features_to=dimension_to_project_features_to,\n", "#     )\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     assert subsampled_features.shape[-1] == feature_dimension\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_sampler.py\n", "# --------------------------------------------------\n", "# @pytest.mark.skipif(not torch.cuda.is_available(), reason=\"Fails for non-GPU machine.\")\n", "# def test_random_sampling():\n", "#     init_features = _dummy_features(feature_dimension=2)\n", "# \n", "#     sampling_percentage = 0.1\n", "#     model = sampler.RandomSampler(percentage=sampling_percentage)\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     target_num_subsampled_features = int(len(init_features) * sampling_percentage)\n", "#     assert len(subsampled_features) == target_num_subsampled_features\n", "#     assert (\n", "#         len(torch.unique(subsampled_features, dim=0)) == target_num_subsampled_features\n", "#     )\n", "# \n", "# \n", "# @pytest.mark.skipif(not torch.cuda.is_available(), reason=\"Fails for non-GPU machine.\")\n", "# def test_type_retention():\n", "#     feature_dimension = 2\n", "#     init_features = _dummy_features(feature_dimension)\n", "# \n", "#     sampling_percentage = 0.1\n", "#     model = sampler.ApproximateGreedyCoresetSampler(\n", "#         percentage=sampling_percentage,\n", "#         device=torch.device(\"cpu\"),\n", "#         number_of_starting_points=10,\n", "#         dimension_to_project_features_to=feature_dimension,\n", "#     )\n", "#     subsampled_features = model.run(init_features)\n", "# \n", "#     assert type(subsampled_features) == type(init_features)\n", "#     assert subsampled_features.device == init_features.device\n", "# \n", "#     subsampled_features_numpy = model.run(init_features.numpy())\n", "#     assert isinstance(subsampled_features_numpy, type(init_features.numpy()))\n", "# \n", "# \n", "# @pytest.mark.skipif(not torch.cuda.is_available(), reason=\"Fails for non-GPU machine.\")\n", "# def test_johnsonlindenstrauss_reduction():\n", "#     feature_dimension = 256\n", "#     init_features = _dummy_features(feature_dimension)\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# bin/load_and_evaluate_patchcore.py\n", "# --------------------------------------------------\n", "# # NN on GPU.\n", "# @click.option(\"--faiss_on_gpu\", is_flag=True)\n", "# @click.option(\"--faiss_num_workers\", type=int, default=8)\n", "# def patch_core_loader(patch_core_paths, faiss_on_gpu, faiss_num_workers):\n", "#     def get_patchcore_iter(device):\n", "#         for patch_core_path in patch_core_paths:\n", "#             loaded_patchcores = []\n", "#             gc.collect()\n", "#             n_patchcores = len(\n", "#                 [x for x in os.listdir(patch_core_path) if \".faiss\" in x]\n", "#             )\n", "#             if n_patchcores == 1:\n", "#                 nn_method = patchcore.common.FaissNN(faiss_on_gpu, faiss_num_workers)\n", "#                 patchcore_instance = patchcore.patchcore.PatchCore(device)\n", "#                 patchcore_instance.load_from_path(\n", "#                     load_path=patch_core_path, device=device, nn_method=nn_method\n", "#                 )\n", "#                 loaded_patchcores.append(patchcore_instance)\n", "#             else:\n", "#                 for i in range(n_patchcores):\n", "#                     nn_method = patchcore.common.FaissNN(\n", "#                         faiss_on_gpu, faiss_num_workers\n", "#                     )\n", "#                     patchcore_instance = patchcore.patchcore.PatchCore(device)\n", "#                     patchcore_instance.load_from_path(\n", "#                         load_path=patch_core_path,\n", "#                         device=device,\n", "#                         nn_method=nn_method,\n", "#                         prepend=\"Ensemble-{}-{}_\".format(i + 1, n_patchcores),\n", "#                     )\n", "#                     loaded_patchcores.append(patchcore_instance)\n", "# \n", "#             yield loaded_patchcores\n", "# \n", "#     return (\"get_patchcore_iter\", [get_patchcore_iter, len(patch_core_paths)])\n", "# \n", "# \n", "# @main.command(\"dataset\")\n", "# @click.argument(\"name\", type=str)\n", "# @click.argument(\"data_path\", type=click.Path(exists=True, file_okay=False))\n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_common.py\n", "# --------------------------------------------------\n", "#     nn_search = common.FaissNN(on_gpu=False, num_workers=4)\n", "#     with pytest.raises(AttributeError):\n", "#         nn_search.run(2, features)\n", "#     assert nn_search.run(2, features, features) is not None\n", "# \n", "# \n", "# def test_read_write_index(tmpdir):\n", "#     index_filename = (tmpdir / \"index\").strpath\n", "#     nn_model = common.FaissNN()\n", "#     features = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)\n", "#     nn_model.fit(features)\n", "#     nn_model.save(index_filename)\n", "# \n", "#     loaded_nn_model = common.FaissNN()\n", "#     loaded_nn_model.load(index_filename)\n", "# \n", "#     query_features = np.arange(10 * 6, dtype=np.float32).reshape(10, 6)\n", "#     assert loaded_nn_model.run(2, query_features) is not None\n", "#     assert np.all(\n", "#         loaded_nn_model.run(2, query_features)[0] == nn_model.run(2, query_features)[0]\n", "#     )\n", "#     assert np.all(\n", "#         loaded_nn_model.run(2, query_features)[1] == nn_model.run(2, query_features)[1]\n", "#     )\n", "# \n", "# \n", "# def test_average_merger_shape():\n", "#     input_features = []\n", "#     input_features.append(np.arange(2 * 3 * 4 * 5).reshape([2, 3, 4, 5]))\n", "#     input_features.append(2 * np.arange(2 * 3 * 4 * 5).reshape([2, 4, 3, 5]))\n", "# \n", "#     merger = common.AverageMerger()\n", "#     output_features = merger.merge([input_features[0]])\n", "#     assert np.all(output_features.shape == (2, 3))\n", "# \n", "#     merger = common.AverageMerger()\n", "#     output_features = merger.merge(input_features)\n", "#     assert np.all(output_features.shape == (2, 7))\n", "# \n", "# \n", "# --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# test/test_common.py\n", "# --------------------------------------------------\n", "# import numpy as np\n", "# import pytest\n", "# \n", "# from patchcore import common\n", "# \n", "# \n", "# def test_calling_without_setting_index():\n", "#     query = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)\n", "#     index = 2 * query\n", "# \n", "#     nn_search = common.FaissNN()\n", "# \n", "#     distances_before_set_index, nn_indices_before_set_index = nn_search.run(\n", "#         2, query, index\n", "#     )\n", "#     nn_search.fit(index)\n", "#     distances_after_set_index, nn_indices_after_set_index = nn_search.run(2, query)\n", "# \n", "#     assert np.all(distances_before_set_index == distances_after_set_index)\n", "#     assert np.all(nn_indices_before_set_index == nn_indices_after_set_index)\n", "# \n", "# \n", "# def test_approximate_faiss():\n", "#     query = np.ones([768, 128], dtype=np.float32)\n", "#     index = 2 * query\n", "# \n", "#     nn_search = common.ApproximateFaissNN()\n", "# \n", "#     distances_before_set_index, nn_indices_before_set_index = nn_search.run(\n", "#         2, query, index\n", "#     )\n", "#     nn_search.fit(index)\n", "#     distances_after_set_index, nn_indices_after_set_index = nn_search.run(2, query)\n", "# \n", "#     assert np.all(distances_before_set_index == distances_after_set_index)\n", "#     assert np.all(nn_indices_before_set_index == nn_indices_after_set_index)\n", "# \n", "# \n", "# def test_search_without_index_raises_exception():\n", "#     features = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)\n", "# --------------------------------------------------\n", "\n", "\n", "# Current source file:\n", "# src/patchcore/common.py\n", "# --------------------------------------------------\n", "\n", "import copy\n", "import os\n", "import pickle\n", "from typing import List\n", "from typing import Union\n", "\n", "import faiss\n", "import numpy as np\n", "import scipy.ndimage as ndimage\n", "import torch\n", "import torch.nn.functional as F\n", "\n", "\n", "class FaissNN(object):\n", "    def __init__(self, on_gpu: bool = False, num_workers: int = 4) -> None:\n", "        \"\"\"FAISS Nearest neighbourhood search.\n", "\n", "        Args:\n", "            on_gpu: If set true, nearest neighbour searches are done on GPU.\n", "            num_workers: Number of workers to use with FAISS for similarity search.\n", "        \"\"\"\n", "        faiss.omp_set_num_threads(num_workers)\n", "        self.on_gpu = on_gpu\n", "        self.search_index = None\n", "\n", "    def _gpu_cloner_options(self):\n", "        return faiss.GpuClonerOptions()\n", "\n", "    def _index_to_gpu(self, index):\n", "        if self.on_gpu:\n", "            # For the non-gpu faiss python package, there is no GpuClonerOptions\n", "            # so we can not make a default in the function header.\n", "            return faiss.index_cpu_to_gpu(\n", "                faiss.StandardGpuResources(), 0, index, self._gpu_cloner_options()\n", "            )\n", "        return index\n", "\n", "    def _index_to_cpu(self, index):\n", "        if self.on_gpu:\n", "            return faiss.index_gpu_to_cpu(index)\n", "        return index\n", "\n", "    def _create_index(self, dimension):\n", "        if self.on_gpu:\n", "            return faiss.GpuIndexFlatL2(\n", "                faiss.StandardGpuResources(), dimension, faiss.GpuIndexFlatConfig()\n", "            )\n", "        return faiss.IndexFlatL2(dimension)\n", "\n", "    def fit(self, features: np.n<PERSON>ray) -> None:\n", "        \"\"\"\n", "        Adds features to the FAISS search index.\n", "\n", "        Args:\n", "            features: Array of size NxD.\n", "        \"\"\"\n", "        if self.search_index:\n", "            self.reset_index()\n", "        self.search_index = self._create_index(features.shape[-1])\n", "        self._train(self.search_index, features)\n", "        self.search_index.add(features)\n", "\n", "    def _train(self, _index, _features):\n", "        pass\n", "\n", "    def run(\n", "        self,\n", "        n_nearest_neighbours,\n", "        query_features: np.n<PERSON>ray,\n", "        index_features: np.ndarray = None,\n", "    ) -> Union[np.ndarray, np.ndarray, np.ndarray]:\n", "        \"\"\"\n", "        Returns distances and indices of nearest neighbour search.\n", "\n", "        Args:\n", "            query_features: Features to retrieve.\n", "            index_features: [optional] Index features to search in.\n", "        \"\"\"\n", "        if index_features is None:\n", "            return self.search_index.search(query_features, n_nearest_neighbours)\n", "\n", "        # Build a search index just for this search.\n", "        search_index = self._create_index(index_features.shape[-1])\n", "        self._train(search_index, index_features)\n", "        search_index.add(index_features)\n", "        return search_index.search(query_features, n_nearest_neighbours)\n", "\n", "    def save(self, filename: str) -> None:\n", "        faiss.write_index(self._index_to_cpu(self.search_index), filename)\n", "\n", "    def load(self, filename: str) -> None:\n", "        self.search_index = self._index_to_gpu(faiss.read_index(filename))\n", "\n", "    def reset_index(self):\n", "        if self.search_index:\n", "            self.search_index.reset()\n", "            self.search_index = None\n", "\n", "\n", "class ApproximateFaissNN(FaissNN):\n", "    def _train(self, index, features):\n", "        index.train(features)\n", "\n", "    def _gpu_cloner_options(self):\n", "        cloner = faiss.GpuClonerOptions()\n", "        cloner.useFloat16 = True\n", "        return cloner\n", "\n", "    def _create_index(self, dimension):\n", "<fim_suffix>\n", "\n", "class _BaseMerger:\n", "    def __init__(self):\n", "        \"\"\"Merges feature embedding by name.\"\"\"\n", "\n", "    def merge(self, features: list):\n", "        features = [self._reduce(feature) for feature in features]\n", "        return np.concatenate(features, axis=1)\n", "\n", "\n", "class AverageMerger(_BaseMerger):\n", "    @staticmethod\n", "    def _reduce(features):\n", "        # NxCxWxH -> NxC\n", "        return features.reshape([features.shape[0], features.shape[1], -1]).mean(\n", "            axis=-1\n", "        )\n", "\n", "\n", "class ConcatMerger(_BaseMerger):\n", "    @staticmethod\n", "    def _reduce(features):\n", "        # NxCxWxH -> NxCWH\n", "        return features.reshape(len(features), -1)\n", "\n", "\n", "class Preprocessing(torch.nn.Module):\n", "    def __init__(self, input_dims, output_dim):\n", "        super(Preprocessing, self).__init__()\n", "        self.input_dims = input_dims\n", "        self.output_dim = output_dim\n", "\n", "        self.preprocessing_modules = torch.nn.ModuleList()\n", "        for input_dim in input_dims:\n", "            module = MeanMapper(output_dim)\n", "            self.preprocessing_modules.append(module)\n", "\n", "    def forward(self, features):\n", "        _features = []\n", "        for module, feature in zip(self.preprocessing_modules, features):\n", "            _features.append(module(feature))\n", "        return torch.stack(_features, dim=1)\n", "\n", "\n", "class MeanMapper(torch.nn.Module):\n", "    def __init__(self, preprocessing_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.preprocessing_dim = preprocessing_dim\n", "\n", "    def forward(self, features):\n", "        features = features.reshape(len(features), 1, -1)\n", "        return F.adaptive_avg_pool1d(features, self.preprocessing_dim).squeeze(1)\n", "\n", "\n", "class Aggregator(torch.nn.Module):\n", "    def __init__(self, target_dim):\n", "        super(Aggregator, self).__init__()\n", "        self.target_dim = target_dim\n", "\n", "    def forward(self, features):\n", "        \"\"\"Returns reshaped and average pooled features.\"\"\"\n", "        # batchsize x number_of_layers x input_dim -> batchsize x target_dim\n", "        features = features.reshape(len(features), 1, -1)\n", "        features = F.adaptive_avg_pool1d(features, self.target_dim)\n", "        return features.reshape(len(features), -1)\n", "\n", "\n", "class RescaleSegmentor:\n", "    def __init__(self, device, target_size=224):\n", "        self.device = device\n", "        self.target_size = target_size\n", "        self.smoothing = 4\n", "\n", "    def convert_to_segmentation(self, patch_scores):\n", "\n", "        with torch.no_grad():\n", "            if isinstance(patch_scores, np.ndarray):\n", "                patch_scores = torch.from_numpy(patch_scores)\n", "            _scores = patch_scores.to(self.device)\n", "            _scores = _scores.unsqueeze(1)\n", "            _scores = F.interpolate(\n", "                _scores, size=self.target_size, mode=\"bilinear\", align_corners=False\n", "            )\n", "            _scores = _scores.squeeze(1)\n", "            patch_scores = _scores.cpu().numpy()\n", "\n", "        return [\n", "            ndimage.gaussian_filter(patch_score, sigma=self.smoothing)\n", "            for patch_score in patch_scores\n", "        ]\n", "\n", "\n", "class NetworkFeatureAggregator(torch.nn.Module):\n", "    \"\"\"Efficient extraction of network features.\"\"\"\n", "\n", "    def __init__(self, backbone, layers_to_extract_from, device):\n", "        super(NetworkFeatureAggregator, self).__init__()\n", "        \"\"\"Extraction of network features.\n", "\n", "        Runs a network only to the last layer of the list of layers where\n", "        network features should be extracted from.\n", "\n", "        Args:\n", "            backbone: torchvision.model\n", "            layers_to_extract_from: [list of str]\n", "        \"\"\"\n", "        self.layers_to_extract_from = layers_to_extract_from\n", "        self.backbone = backbone\n", "        self.device = device\n", "        if not hasattr(backbone, \"hook_handles\"):\n", "            self.backbone.hook_handles = []\n", "        for handle in self.backbone.hook_handles:\n", "            handle.remove()\n", "        self.outputs = {}\n", "\n", "        for extract_layer in layers_to_extract_from:\n", "            forward_hook = ForwardHook(\n", "                self.outputs, extract_layer, layers_to_extract_from[-1]\n", "            )\n", "            if \".\" in extract_layer:\n", "                extract_block, extract_idx = extract_layer.split(\".\")\n", "                network_layer = backbone.__dict__[\"_modules\"][extract_block]\n", "                if<fim_middle>"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, display\n", "\n", "display(\n", "    HTML(\n", "        \"<table><tr>{}</tr></table>\".format(\n", "            \"</tr><tr>\".join(\n", "                \"<td>{}</td>\".format(\"</td><td>\".join(str(_) for _ in row))\n", "                for row in [\"foo\", \"bar\"]\n", "            )\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import megatron.text_generation_utils as text_gen_utils\n", "import torch"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["text_gen_utils.filter_logits(torch.tensor([[-0.1, -0.1]]), top_p=0.51)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["torch.tensor([1, 2, 3])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
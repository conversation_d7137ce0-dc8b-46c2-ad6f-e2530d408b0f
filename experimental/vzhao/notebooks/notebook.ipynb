{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports and Utilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from IPython.display import HTML, clear_output, display\n", "\n", "import ipywidgets as widgets\n", "from experimental.vzhao.notebooks import utils as vz_utils \n", "import html\n", "from collections import defaultdict\n", "from lm_eval.tasks.repo_coder.utils import trim_completion\n", "import pandas as pd\n", "from research.eval.patch_lib import Patch\n", "from types import MethodType\n", "\n", "importlib.reload(vz_utils)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(HTML(vz_utils.get_diff_html('a', 'b')))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creates a Hydra Driver."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "from research.eval import hydra\n", "\n", "HYDRA_DRIVER_NAME = f\"{getpass.getuser()}-hydra-on-notebook\"\n", "# HYDRA_LOCAL_TIMEOUT_SECS = 30\n", "HYDRA_LOCAL_TIMEOUT_SECS = 360\n", "HYDRA_GLOBAL_TIMEOUT_SECS = 360\n", "HYDRA_NUM_PATCHES_LIMIT = 5\n", "\n", "driver = hydra.Driver(\n", "    driver_name=HYDRA_DRIVER_NAME,\n", "    local_timeout_secs=HYDRA_LOCAL_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_GLOBAL_TIMEOUT_SECS,\n", ")\n", "\n", "import threading\n", "\n", "from research.eval.hydra.driver import Driver\n", "\n", "def run_hydra_parallel(driver: Driver, patches: list[Patch]):\n", "    dispatch_threads = []\n", "    for p in patches:\n", "        thread = threading.Thread(target=driver.dispatch, args=(p,))\n", "        thread.start()\n", "        dispatch_threads.append(thread)\n", "    for thread in dispatch_threads:\n", "        thread.join()\n", "    # driver.collect_result()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loads Code Model (code generating model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.all_models import get_model, list_models\n", "from research.core.model_input import ModelInput\n", "from research.models import StopCriteria, GenerationOptions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 16B Remote Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # starcoder_16b = get_model(\"remote\", url=\"http://216.153.48.243:5000\")\n", "# starcoder_16b = get_model(\"remote\", url=\"http://10.145.211.24:5000\")\n", "# starcoder_16b.load()\n", "\n", "# starcoder_16b.generate(\n", "#     ModelInput(\"hello\"),\n", "#     GenerationOptions(\n", "#         temperature=0.0,\n", "#         max_generated_tokens=120,\n", "#         stop_criteria=StopCriteria(\n", "#             stop_texts=[\"tat\"], check_stopping_condition_every=4\n", "#         ),\n", "#     ),\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7B Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# starcoder_7b = get_model(\"starcoderbase_7b\")\n", "# starcoder_7b.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 16B Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["starcoder_16b = get_model(\"starcoderbase\")\n", "starcoder_16b.load()\n", "\n", "starcoder_16b.generate(\n", "    ModelInput(\"hello\"),\n", "    GenerationOptions(\n", "        temperature=0.0,\n", "        max_generated_tokens=120,\n", "        stop_criteria=StopCriteria(\n", "            stop_texts=[\"tat\"], check_stopping_condition_every=4\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Register Experiments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENTS = {\n", "    # 'StarCoder+Oracle250': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/PyHPwc9b',\n", "    #     description='BM25 250 reranked by oracle reranker.'\n", "    # ),\n", "    'EmptyCompletion': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/Dr9tY9zu',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=10.0,\n", "    ),\n", "    'GoldCompletion': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/TGsikzze',\n", "        description='Sanity Check with gold completion. All test cases should pass.',\n", "        weight=10.0,\n", "    ),\n", "    # 'NoRe': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/MuAhbB4X',\n", "    #     description='StarCoder prompted with prefix. No RAG.',\n", "    #     weight=1.0,\n", "    # ),\n", "    'NoReLong': vz_utils.Experiment(\n", "        poc='colin',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/RXxWdbqR',\n", "        description='StarCoder prompted with prefix. No RAG.',\n", "        weight=1.5,\n", "    ),\n", "    # 'DiffRe': vz_utils.Experiment(\n", "    #     poc='igor',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/8aynUvdJ',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    'DiffRe1000+PPL1000': vz_utils.Experiment(\n", "        poc='igor',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/kXRjLYLi',\n", "        description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "        weight=1.0,\n", "    ),\n", "    'GreedyHydraChunk1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/bcuooErM',\n", "        description='Greedy oracle max number chunk = 1',\n", "        weight=1.2,\n", "    ),\n", "    'GreedyHydraChunk2': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/Pb9DJ4NF',\n", "        description='Greedy oracle max number chunk = 2',\n", "        weight=1.1,\n", "    ),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MultiLine\n", "EXPERIMENTS = {\n", "    'EmptyCompletion': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/RxkjHdTh',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=10.0,\n", "    ),\n", "    'GoldCompletion': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/g6V3D2Vb',\n", "        description='Sanity Check with gold completion. All test cases should pass.',\n", "        weight=10.0,\n", "    ),\n", "    'StarCoder+No': vz_utils.Experiment(\n", "        poc='colin',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/TQdPXgCR',\n", "        description='StarCoder prompted with prefix. No RAG.',\n", "        weight=2.0,\n", "    ),\n", "    # 'DiffRe': vz_utils.Experiment(\n", "    #     poc='igor',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/8aynUvdJ',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    'DiffRe1000+PPL1000': vz_utils.Experiment(\n", "        poc='igor',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/oY8zbris',\n", "        description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "        weight=1.0,\n", "    )\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# IGNORE Bad Test Case"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PATCH_ID_DENYLIST = set()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Empty string should fail.\n", "for pid, value in vz_utils.load(EXPERIMENTS['EmptyCompletion']).items():\n", "    if value['result'] == 'PASSED':\n", "        PATCH_ID_DENYLIST.add(pid)\n", "len(PATCH_ID_DENYLIST)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Single Experiment Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["select_single_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description='Patch ID:',\n", "    disabled=False,\n", ")\n", "clear_output(wait=True)\n", "display(select_single_experiment)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "exp_id = select_single_experiment.value\n", "eval_dir = EXPERIMENTS[exp_id].eval_dir\n", "print(exp_id)\n", "\n", "results = load(eval_dir)\n", "patch_ids = list(results.keys())\n", "\n", "counter = collections.Counter([results[pid]['result'] for pid in results])\n", "\n", "pid_passed, pid_failed = [], []\n", "for pid in results:\n", "    # Gets patch_id for passes and failes\n", "    if results[pid]['result'] == 'PASSED':\n", "        pid_passed.append(pid)\n", "    else:\n", "        pid_failed.append(pid)\n", "    # Normalize to patch object.\n", "    cache = results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "    \n", "\n", "\n", "# print(results[\"CarperAI_trlx/0\"].keys())\n", "# print(results[\"CarperAI_trlx/0\"][\"patch_rst\"].keys())\n", "\n", "print(counter)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Per-Repo Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(\n", "    [\n", "        {\n", "            \"patch_id\": pid,\n", "            \"repo\": results[pid][\"patch_rst\"][\"patch\"].repository,\n", "            \"result\": results[pid][\"result\"],\n", "            \"target_patch_lines\": len(\n", "                results[pid][\"patch_rst\"][\"patch\"].original_patch_content.split(\"\\n\")\n", "            ),\n", "            \"file_context_lines\": len(\n", "                results[pid][\"patch_rst\"][\"patch\"].file_content.split(\"\\n\")\n", "            ),\n", "        }\n", "        for pid in results\n", "    ]\n", ")\n", "\n", "foo = df[[\"repo\", \"result\"]].groupby([\"repo\"]).count().reset_index()\n", "foo.columns = [\"repo\", \"total\"]\n", "\n", "# bar = df.groupby([\"repo\", \"result\"]).count()\n", "bar = df.groupby([\"repo\", \"result\"]).agg(\n", "    {\n", "        \"patch_id\": [\"count\"],\n", "        # \"target_patch_lines\": ['min', \"mean\", 'max'],\n", "        # \"file_context_lines\": ['min', \"mean\", 'max'],\n", "    }\n", ")\n", "bar.columns = [\n", "    \"patch_id\",\n", "    # \"target_patch_lines_min\",\n", "    # \"target_patch_lines_mean\",\n", "    # \"target_patch_lines_max\",\n", "    # \"file_context_lines_min\",\n", "    # \"file_context_lines_mean\",\n", "    # \"file_context_lines_max\",\n", "]\n", "\n", "foo = pd.merge(bar.reset_index(), foo, left_on=\"repo\", right_on=\"repo\").set_index(\n", "    [\"repo\", \"result\"]\n", ")\n", "foo[\"rate\"] = foo[\"patch_id\"] / foo[\"total\"]\n", "foo = foo[[\n", "    \"patch_id\",\n", "    \"rate\",\n", "    # \"target_patch_lines_min\",\n", "    # \"target_patch_lines_mean\",\n", "    # \"target_patch_lines_max\",\n", "    # \"file_context_lines_min\",\n", "    # \"file_context_lines_mean\",\n", "    # \"file_context_lines_max\",\n", "]]\n", "foo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = driver.dispatch(results['CarperAI_trlx/13']['patch_rst']['patch'].with_patch_content(''))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Per-File Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows', None)\n", "\n", "df = pd.DataFrame(\n", "    [\n", "        {\n", "            \"patch_id\": pid,\n", "            \"repo\": results[pid][\"patch_rst\"][\"patch\"].repository,\n", "            \"file_name\": results[pid][\"patch_rst\"][\"patch\"].file_name,\n", "            'result': results[pid]['result'],\n", "        }\n", "        for pid in results\n", "    ]\n", ")\n", "\n", "foo = df[['repo', 'file_name', \"result\"]].groupby(['repo', 'file_name']).count().reset_index()\n", "foo.columns = ['repo', 'file_name', \"total\"]\n", "\n", "bar = df.groupby(['repo', 'file_name', 'result']).count().reset_index()\n", "bar = bar.set_index(['repo', 'file_name', 'result'])\n", "baz = []\n", "for _, (repo, file_name) in bar.reset_index()[['repo', 'file_name']].drop_duplicates().iterrows():\n", "    baz.append({'repo': repo, 'file_name': file_name, 'result': 'FAILED', 'patch_id': 0})\n", "    baz.append({'repo': repo, 'file_name': file_name, 'result': 'PASSED', 'patch_id': 0})\n", "baz = pd.DataFrame(baz)\n", "\n", "\n", "# for row in bar.reset_index()[['repo', 'file_name']].drop_duplicates().iterrows():\n", "#     repo, file_name = row[1]\n", "#     if (repo, file_name, 'PASSED') not in bar['patch_id']:\n", "#         bar['patch_id'][repo, file_name, 'PASSED'] = 0\n", "#     if (repo, file_name, 'FAILED') not in bar['patch_id']:\n", "#         bar['patch_id'][repo, file_name, 'FAILED'] = 0\n", "# bar = bar.reset_index()\n", "\n", "\n", "# output = pd.merge(bar, foo, left_on=[\"repo\", 'file_name'], right_on=[\"repo\", 'file_name']).set_index(\n", "#     [\"repo\", 'file_name', \"result\"]\n", "# )\n", "# output['patch_id'] = output['patch_id'] / output['total']\n", "# display(output)\n", "\n", "\n", "# df.head()\n", "\n", "pd.set_option('display.max_rows', 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bar + baz.set_index(['repo', 'file_name', 'result'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["baz.set_index(['repo', 'file_name', 'result'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bar['patch_id']['maxhumber/redframes', 'trlx/data/configs.py', 'PASSED'] = 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bar"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid_list = pid_passed\n", "\n", "\n", "select_pid = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description='Patch ID:',\n", "    disabled=False,\n", ")\n", "\n", "prompt_widget = widgets.Textarea(\n", "    value=results[select_pid.value][\"prompt\"],\n", "    placeholder=\"Code completion prompt.\",\n", "    description=\"Prompt:\",\n", "    disabled=False,\n", "    layout=widgets.Layout(height=\"400px\", width=\"auto\"),\n", ")\n", "generated_completion = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["hide_code"]}, "outputs": [], "source": ["refresh_button = widgets.Button(description=\"Refresh\", button_style=\"success\")\n", "generate_button = widgets.Button(description=\"Generate\", button_style=\"success\")\n", "hydra_button = widgets.Button(description=\"Run Hydra\", button_style=\"success\")\n", "\n", "# Creates notebook widgets\n", "def display_origin():\n", "    rst = results[select_pid.value]\n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                rst[\"ground_truth\"],\n", "                rst[\"generation\"],\n", "                fromdesc=f\"ground truth\",\n", "                todesc=f\"origin ({rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def display_default():\n", "    display(select_pid)\n", "    print(select_pid.value)\n", "    display(refresh_button, prompt_widget)\n", "    display_origin()\n", "    display(generate_button)\n", "\n", "\n", "def refresh(b):\n", "    clear_output(wait=True)\n", "    for key in generated_completion.keys():\n", "        generated_completion[key] = None\n", "    prompt_widget.value = results[select_pid.value][\"prompt\"]\n", "    display_default()\n", "\n", "refresh_button.on_click(refresh)\n", "\n", "\n", "def generate_button_clicked(b):\n", "    rst = results[select_pid.value]\n", "\n", "    print(\"Generating ...\")\n", "    new_completion = starcoder_16b.generate(\n", "        ModelInput(prompt_widget.value),\n", "        GenerationOptions(\n", "            temperature=0.0,\n", "            max_generated_tokens=240,\n", "            stop_criteria=StopCriteria(\n", "                stop_texts=[\"tat\"], check_stopping_condition_every=4\n", "            ),\n", "        ),\n", "    )\n", "    new_completion = trim_completion(new_completion)\n", "    generated_completion['prompt'] = prompt_widget.value\n", "    generated_completion['generation'] = new_completion\n", "    \n", "\n", "    html = get_diff_html(\n", "        rst[\"ground_truth\"],\n", "        generated_completion['generation'],\n", "        fromdesc=f\"ground truth\",\n", "        todesc=\"new\",\n", "    )\n", "    generated_completion['diff_html'] = html\n", "\n", "    clear_output(wait=True)\n", "    # print(new_completion)\n", "    display_default()\n", "    display(HTML(html))\n", "    display(hydra_button)\n", "\n", "\n", "generate_button.on_click(generate_button_clicked)\n", "\n", "\n", "def hydra_button_clicked(b):\n", "    rst = results[select_pid.value]\n", "    patch = rst[\"patch_rst\"][\"patch\"]\n", "    print(\"Running Hydra ...\")\n", "    new_patch = patch.with_patch_content(generated_completion['generation'])\n", "    _ = driver.dispatch(new_patch)\n", "\n", "hydra_button.on_click(hydra_button_clicked)\n", "\n", "\n", "# Main\n", "display_default()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[select_pid.value].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results[select_pid.value]['run_output'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[select_pid.value]['patch_rst']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Two Expeirments Side by Side"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c638f9e9f6146bc8a36ed1dc76d5427", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Left Patch ID:', options=('EmptyCompletion', 'GoldCompletion', 'NoReLong', 'DiffRe1000+P…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03f64c8b02354352a730335873e48752", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Right Patch ID:', options=('EmptyCompletion', 'GoldCompletion', 'NoReLong', 'DiffRe1000+…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["select_left_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description='Left Patch ID:',\n", "    disabled=False,\n", ")\n", "select_right_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description='Right Patch ID:',\n", "    disabled=False,\n", ")\n", "clear_output(wait=True)\n", "display(select_left_experiment, select_right_experiment)\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["left GreedyHydraChunk1 right GreedyHydraChunk2\n", "1\n", "1\n", "dict_keys(['prompt', 'generation', 'completion', 'ground_truth', 'filename', 'file_content', 'result', 'run_output', 'patch_rst', 'json_rst', 'retriever_prompt'])\n", "dict_keys(['ground_truth_log_likelihood', 'patch', 'prefix', 'suffix', 'prompt', 'filtered_chunks', 'generation', 'completion', 'artifacts'])\n"]}], "source": ["import json\n", "\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "\n", "left_id = select_left_experiment.value\n", "left_results = vz_utils.load(EXPERIMENTS[select_left_experiment.value])\n", "for pid in left_results:\n", "    # Normalize to patch object.\n", "    cache = left_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        left_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "right_id = select_right_experiment.value\n", "right_results = vz_utils.load(EXPERIMENTS[select_right_experiment.value])\n", "for pid in right_results:\n", "    # Normalize to patch object.\n", "    cache = right_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        right_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "print(left_results[\"CarperAI_trlx/0\"].keys())\n", "print(left_results[\"CarperAI_trlx/0\"][\"patch_rst\"].keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistics"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["def to_dataframe(results):\n", "    df = pd.DataFrame(\n", "        [\n", "            {\n", "                \"patch_id\": pid,\n", "                \"repo\": results[pid][\"patch_rst\"][\"patch\"].repository,\n", "                \"filename\": results[pid]['filename'],\n", "                \"result\": results[pid][\"result\"],\n", "            }\n", "            for pid in results\n", "        ]\n", "    )\n", "    return df"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>GreedyHydraChunk1</th>\n", "      <th>GreedyHydraChunk2</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>192</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     count\n", "GreedyHydraChunk1 GreedyHydraChunk2       \n", "FAILED            FAILED               222\n", "                  PASSED                26\n", "PASSED            FAILED                15\n", "                  PASSED               192"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["left_df = to_dataframe(left_results)[[\"patch_id\", \"result\"]]\n", "left_df.columns = [\"patch_id\", \"left_result\"]\n", "\n", "right_df = to_dataframe(right_results)[[\"patch_id\", \"result\"]]\n", "right_df.columns = [\"patch_id\", \"right_result\"]\n", "\n", "# Show statistics.\n", "foo = (\n", "    pd.merge(\n", "        left_df, right_df, left_on=[\"patch_id\"], right_on=[\"patch_id\"]\n", "    )\n", "    .groupby([\"left_result\", \"right_result\"])\n", "    .count()\n", "    .reset_index()\n", ")\n", "foo.columns = [left_id, right_id, \"count\"]\n", "foo.set_index([left_id, right_id])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Per-Repo Statistics"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>repo</th>\n", "      <th>GreedyHydraChunk1</th>\n", "      <th>GreedyHydraChunk2</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">CarperAI/trlx</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">amazon-science/patchcore-inspection</th>\n", "      <th>FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">deepmind/tracr</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">facebookresearch/omnivore</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <th>PASSED</th>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">google/lightweight_mmm</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">leopard-ai/betty</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">lucidrains/imagen-pytorch</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">maxhumber/redframes</th>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                         count\n", "repo                                GreedyHydraChunk1 GreedyHydraChunk2       \n", "CarperAI/trlx                       FAILED            FAILED                 2\n", "                                                      PASSED                 2\n", "                                    PASSED            FAILED                 2\n", "                                                      PASSED                40\n", "amazon-science/patchcore-inspection FAILED            FAILED                18\n", "                                    PASSED            FAILED                 1\n", "                                                      PASSED                13\n", "deepmind/tracr                      FAILED            FAILED                78\n", "                                                      PASSED                 9\n", "                                    PASSED            FAILED                 4\n", "                                                      PASSED                55\n", "facebookresearch/omnivore           FAILED            FAILED                14\n", "                                                      PASSED                 1\n", "                                    PASSED            PASSED                 7\n", "google/lightweight_mmm              FAILED            FAILED                40\n", "                                                      PASSED                 7\n", "                                    PASSED            FAILED                 3\n", "                                                      PASSED                14\n", "leopard-ai/betty                    FAILED            FAILED                16\n", "                                                      PASSED                 1\n", "                                    PASSED            FAILED                 1\n", "                                                      PASSED                18\n", "lucidrains/imagen-pytorch           FAILED            FAILED                22\n", "                                                      PASSED                 5\n", "                                    PASSED            FAILED                 2\n", "                                                      PASSED                38\n", "maxhumber/redframes                 FAILED            FAILED                32\n", "                                                      PASSED                 1\n", "                                    PASSED            FAILED                 2\n", "                                                      PASSED                 7"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["left_df = to_dataframe(left_results)[[\"patch_id\", 'repo', \"result\"]]\n", "left_df.columns = [\"patch_id\", 'repo', \"left_result\"]\n", "\n", "right_df = to_dataframe(right_results)[[\"patch_id\",'repo', \"result\"]]\n", "right_df.columns = [\"patch_id\", 'repo',\"right_result\"]\n", "\n", "# Show statistics.\n", "foo = (\n", "    pd.merge(\n", "        left_df, right_df, left_on=[\"patch_id\", 'repo'], right_on=[\"patch_id\", 'repo']\n", "    )\n", "    .groupby(['repo', \"left_result\", \"right_result\"])\n", "    .count()\n", "    .reset_index()\n", ")\n", "foo.columns = ['repo', left_id, right_id, \"count\"]\n", "foo.set_index(['repo', left_id, right_id])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Side-by-Side"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>patch_id</th>\n", "    </tr>\n", "    <tr>\n", "      <th>GreedyHydraChunk1</th>\n", "      <th>GreedyHydraChunk2</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">FAILED</th>\n", "      <th>FAILED</th>\n", "      <td>CarperAI_trlx/20,CarperAI_trlx/33,maxhumber_re...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>CarperAI_trlx/3,CarperAI_trlx/25,maxhumber_red...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PASSED</th>\n", "      <th>FAILED</th>\n", "      <td>CarperAI_trlx/30,CarperAI_trlx/34,maxhumber_re...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PASSED</th>\n", "      <td>CarperAI_trlx/0,CarperAI_trlx/1,CarperAI_trlx/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                              patch_id\n", "GreedyHydraChunk1 GreedyHydraChunk2                                                   \n", "FAILED            FAILED             CarperAI_trlx/20,CarperAI_trlx/33,maxhumber_re...\n", "                  PASSED             CarperAI_trlx/3,CarperAI_trlx/25,maxhumber_red...\n", "PASSED            FAILED             CarperAI_trlx/30,CarperAI_trlx/34,maxhumber_re...\n", "                  PASSED             CarperAI_trlx/0,CarperAI_trlx/1,CarperAI_trlx/..."]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get list of patch ids for each class.\n", "left_df = to_dataframe(left_results)[[\"patch_id\", \"result\"]]\n", "left_df.columns = [\"patch_id\", \"left_result\"]\n", "\n", "right_df = to_dataframe(right_results)[[\"patch_id\", \"result\"]]\n", "right_df.columns = [\"patch_id\", \"right_result\"]\n", "\n", "foo = (\n", "    pd.merge(\n", "        left_df, right_df, left_on=[\"patch_id\"], right_on=[\"patch_id\"]\n", "    )\n", "    .groupby([\"left_result\", \"right_result\"])['patch_id']\n", "    .apply(','.join)\n", "    .reset_index()\n", ")\n", "foo.columns = [left_id, right_id, \"patch_id\"]\n", "patch_id_classified = foo.set_index([left_id, right_id])\n", "patch_id_classified"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Playground (prompt engineering)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["import re\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "def maybe_split_fim_prompt(prompt: str) -> ModelInput:\n", "    splits = re.split(\"<fim_prefix>|<fim_middle>|<fim_suffix>\", prompt)\n", "    if len(splits) == 4:\n", "        # This is a FIM prompt.\n", "        return ModelInput(\n", "            prefix=splits[1],\n", "            suffix=splits[2],\n", "        )\n", "    else:\n", "        return ModelInput(prompt)\n", "    \n", "def create_new_cell(contents):\n", "    from IPython.core.getipython import get_ipython\n", "\n", "    shell = get_ipython()\n", "\n", "    payload = dict(\n", "        source=\"set_next_input\",\n", "        text=contents,\n", "        replace=False,\n", "    )\n", "    shell.payload_manager.write_payload(payload, single=False)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["# Main\n", "\n", "pid_list = patch_id_classified[\"patch_id\"][\"PASSED\", \"FAILED\"].split(\",\")\n", "pid_list = [pid for pid in pid_list if pid not in PATCH_ID_DENYLIST]\n", "# pid_list = [pid for pid in pid_list if df_best_match.set_index('patch_id').loc[pid]['score'] < 0.6 ]\n", "len(pid_list)\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["select_pid = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description=\"Patch ID:\",\n", "    disabled=False,\n", ")\n", "\n", "prompt_widget = widgets.Textarea(\n", "    value=right_results[select_pid.value][\"prompt\"],\n", "    placeholder=\"Code completion prompt.\",\n", "    description=\"Prompt:\",\n", "    disabled=False,\n", "    layout=widgets.Layout(height=\"400px\", width=\"auto\"),\n", ")\n", "\n", "generated_completion = {}"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "31b5ee4313734c81bde9394ef6bcaac4", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Patch ID:', options=('CarperAI_trlx/30', 'CarperAI_trlx/34', 'maxhumber_redframes/16', '…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/30\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e45eefcae981415aa3c05cca01a21a59", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='success', description='Refresh', style=ButtonStyle())"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b924aa51a4bd4d68aafb6da04174b820", "version_major": 2, "version_minor": 0}, "text/plain": ["Textarea(value='        lm_logits = self.lm_head(sequence_output)\\n\\n        if not return_dict:\\n            …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to1__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (PASSED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to1__0\">f</a></td><td class=\"diff_header\" id=\"from1_1\">1</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to1__0\">f</a></td><td class=\"diff_header\" id=\"to1_1\">1</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to1__0\"></td><td class=\"diff_header\" id=\"from1_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to1__top\">t</a></td><td class=\"diff_header\" id=\"from1_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to1__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_11\">11</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_10\">10</td><td nowrap=\"nowrap\"></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to2__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">ground truth</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to2__1\"><a href=\"#difflib_chg_to2__1\">n</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__1\">n</a></td><td class=\"diff_header\" id=\"to2_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to2__2\"><a href=\"#difflib_chg_to2__2\">n</a></td><td class=\"diff_header\" id=\"from2_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;config&nbsp;=&nbsp;self.base_model.config</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__2\">n</a></td><td class=\"diff_header\" id=\"to2_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;hf_get_branch_class(config)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to2__top\">t</a></td><td class=\"diff_header\" id=\"from2_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;).eval()</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__top\">t</a></td><td class=\"diff_header\" id=\"to2_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "70833931e6b5405ebe259e79aa6535f8", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='success', description='Generate', style=ButtonStyle())"]}, "metadata": {}, "output_type": "display_data"}], "source": ["refresh_button = widgets.Button(description=\"Refresh\", button_style=\"success\")\n", "generate_button = widgets.Button(description=\"Generate\", button_style=\"success\")\n", "hydra_button = widgets.Button(description=\"Run Hydra\", button_style=\"success\")\n", "\n", "# Creates notebook widgets\n", "def display_sxs():\n", "    left_rst = left_results[select_pid.value]\n", "    right_rst = right_results[select_pid.value]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"generation\"],\n", "                right_rst[\"generation\"],\n", "                fromdesc=f\"origin ({left_rst['result']})\",\n", "                todesc=f\"origin ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "def display_rightxgold():\n", "    right_rst = right_results[select_pid.value]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                right_results[select_pid.value]['ground_truth'],\n", "                right_rst[\"generation\"],\n", "                fromdesc='ground truth',\n", "                todesc=f\"origin ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def display_default():\n", "    display(select_pid)\n", "    print(select_pid.value)\n", "    # By default, we use prompt from right side.\n", "    prompt_widget.value = right_results[select_pid.value][\"prompt\"]\n", "    display(refresh_button, prompt_widget)\n", "    display_sxs()\n", "    display_rightxgold()\n", "    display(generate_button)\n", "\n", "\n", "def refresh(b):\n", "    clear_output(wait=True)\n", "    for key in generated_completion.keys():\n", "        generated_completion[key] = None\n", "    prompt_widget.value = right_results[select_pid.value][\"prompt\"]\n", "    display_default()\n", "\n", "refresh_button.on_click(refresh)\n", "\n", "\n", "def generate_button_clicked(b):\n", "    rst = right_results[select_pid.value]\n", "\n", "    print(\"Generating ...\")\n", "    new_completion = starcoder_16b.generate(\n", "        maybe_split_fim_prompt(prompt_widget.value),\n", "        GenerationOptions(\n", "            temperature=0.0,\n", "            max_generated_tokens=560,\n", "            stop_criteria=StopCriteria(\n", "                stop_texts=[], check_stopping_condition_every=4\n", "            ),\n", "        ),\n", "    )\n", "    new_completion = trim_completion(new_completion)\n", "    generated_completion['prompt'] = prompt_widget.value\n", "    generated_completion['generation'] = new_completion\n", "    \n", "\n", "    html = vz_utils.get_diff_html(\n", "        rst[\"ground_truth\"],\n", "        generated_completion['generation'],\n", "        fromdesc=f\"ground truth\",\n", "        todesc=\"new\",\n", "    )\n", "    generated_completion['diff_html'] = html\n", "\n", "    clear_output(wait=True)\n", "    display_default()\n", "    display(HTML(html))\n", "    display(hydra_button)\n", "\n", "\n", "generate_button.on_click(generate_button_clicked)\n", "\n", "def hydra_button_clicked(b):\n", "    rst = right_results[select_pid.value]\n", "    patch = rst[\"patch_rst\"][\"patch\"]\n", "    print(\"Running Hydra ...\")\n", "    new_patch = patch.with_patch_content(generated_completion['generation'])\n", "    _ = driver.dispatch(new_patch)\n", "\n", "hydra_button.on_click(hydra_button_clicked)\n", "\n", "display_default()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(right_results[select_pid.value]['file_content'])"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.4124601483345032"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["right_results[select_pid.value]['patch_rst']['ground_truth_log_likelihood']"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.5075879693031311"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["left_results[select_pid.value]['patch_rst']['ground_truth_log_likelihood']"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['prompt', 'generation', 'completion', 'ground_truth', 'filename', 'file_content', 'result', 'run_output', 'patch_rst', 'json_rst', 'retriever_prompt'])"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["foo.keys()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['file_content', 'char_start', 'char_end', 'patch_content', 'patch_id', 'repository', 'commit_sha', 'file_name', '_extra'])"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["left_results[select_pid.value]['json_rst'].keys()"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ground_truth_log_likelihood' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[55], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m foo[\u001b[39m'\u001b[39m\u001b[39mpatch_rst\u001b[39m\u001b[39m'\u001b[39m][ground_truth_log_likelihood]\n", "\u001b[0;31mNameError\u001b[0m: name 'ground_truth_log_likelihood' is not defined"]}], "source": ["foo"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['ground_truth_log_likelihood', 'patch', 'prefix', 'suffix', 'prompt', 'filtered_chunks', 'generation', 'completion', 'artifacts'])"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["foo['patch_rst'].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_new_cell(right_results[select_pid.value]['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_new_cell(left_results[select_pid.value]['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_new_cell(left_results[select_pid.value]['generation'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!nvidia-smi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "    \"\"\"Sorts the dataframe by the given columns.\n", "\n", "    Args:\n", "        df: The dataframe to sort.\n", "        columns: The columns to sort by.\n", "        descending: Whether to sort in descending order.\n", "\n", "    Returns:\n", "        The sorted dataframe.\n", "    \"\"\"\n", "    _check_type(\"df\", df, PandasDataFrame)\n", "    _check_type(\"columns\", columns, LazyColumns)\n", "    _check_keys(\"columns\", columns, df.columns)\n", "\n", "    return df.sort_values(columns, ascending=not descending)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_new_cell(right_results[select_pid.value]['generation'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    _check_type(columns, {list, str})\n", "    _check_type(descending, bool)\n", "    _check_keys(columns, df.columns)\n", "    df = df.sort_values(by=columns, ascending=not descending)\n", "    df = df.reset_index(drop=True)\n", "    return df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compare Prompts"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["import re\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "def maybe_split_fim_prompt(prompt: str) -> ModelInput:\n", "    splits = re.split(\"<fim_prefix>|<fim_middle>|<fim_suffix>\", prompt)\n", "    if len(splits) == 4:\n", "        # This is a FIM prompt.\n", "        return ModelInput(\n", "            prefix=splits[1],\n", "            suffix=splits[2],\n", "        )\n", "    else:\n", "        return ModelInput(prompt)\n", "    \n", "def create_new_cell(contents):\n", "    from IPython.core.getipython import get_ipython\n", "\n", "    shell = get_ipython()\n", "\n", "    payload = dict(\n", "        source=\"set_next_input\",\n", "        text=contents,\n", "        replace=False,\n", "    )\n", "    shell.payload_manager.write_payload(payload, single=False)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["# Main\n", "\n", "pid_list = patch_id_classified[\"patch_id\"][\"PASSED\", \"FAILED\"].split(\",\")\n", "pid_list = [pid for pid in pid_list if pid not in PATCH_ID_DENYLIST]\n", "# pid_list = [pid for pid in pid_list if df_best_match.set_index('patch_id').loc[pid]['score'] < 0.6 ]\n", "len(pid_list)\n", "\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["select_pid = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description=\"Patch ID:\",\n", "    disabled=False,\n", ")\n", "\n", "prompt_widget = widgets.Textarea(\n", "    value=right_results[select_pid.value][\"prompt\"],\n", "    placeholder=\"Code completion prompt.\",\n", "    description=\"Prompt:\",\n", "    disabled=False,\n", "    layout=widgets.Layout(height=\"400px\", width=\"auto\"),\n", ")\n", "\n", "generated_completion = {}"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "528d286b41c04f32935ec052138fa42b", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Patch ID:', options=('CarperAI_trlx/30', 'CarperAI_trlx/34', 'maxhumber_redframes/16', '…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/30\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dbec8d919e294633abfd6878eeec053e", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='success', description='Refresh', style=ButtonStyle())"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1dccbea7251a49c38aeddc41037e31ad", "version_major": 2, "version_minor": 0}, "text/plain": ["Textarea(value='        lm_logits = self.lm_head(sequence_output)\\n\\n        if not return_dict:\\n            …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to3__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (PASSED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to3__0\"><a href=\"#difflib_chg_to3__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to3__top\">t</a></td><td class=\"diff_header\" id=\"to3_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lm_logits&nbsp;=&nbsp;self.lm_head(sequence_output)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;return_dict:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;(lm_logits,)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Seq2SeqLMOutputWithValue(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;logits=lm_logits,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;decoder_hidden_states=all_hidden_states,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;decoder_attentions=all_attentions,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_add\">#&nbsp;Branch&nbsp;class&nbsp;utils</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_14\">14</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_add\">def&nbsp;hf_get_branch_class(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;config:&nbsp;transformers.PretrainedConfig,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_18\">18</td><td nowrap=\"nowrap\"><span class=\"diff_add\">)&nbsp;-&gt;&nbsp;\"ModelBranch\":</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"Returns&nbsp;the&nbsp;model&nbsp;branch&nbsp;class&nbsp;for&nbsp;the&nbsp;given&nbsp;config.\"\"\"</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;gpt_branch_supported_archs&nbsp;=&nbsp;[</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"GPTJForCausalLM\",</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"GPT2LMHeadModel\",</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"GPTNeoForCausalLM\",</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"GPTNeoXForCausalLM\",</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;]</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;opt_branch_supported_archs&nbsp;=&nbsp;[\"OPTForCausalLM\"]</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;bloom_branch_supported_archs&nbsp;=&nbsp;[\"BloomModel\",&nbsp;\"BloomForCausalLM\"]</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;arch&nbsp;=&nbsp;config.architectures[0]</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;arch&nbsp;in&nbsp;gpt_branch_supported_archs:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;GPTModelBranch</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_31\">31</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;arch&nbsp;in&nbsp;opt_branch_supported_archs:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;OPTModelBranch</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;arch&nbsp;in&nbsp;bloom_branch_supported_archs:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;BloomModelBranch</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_35\">35</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;else:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;all_supported_archs&nbsp;=&nbsp;sum(</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gpt_branch_supported_archs,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_39\">39</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;opt_branch_supported_archs,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_40\">40</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bloom_branch_supported_archs,</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_1\">1</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;model.&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_42\">42</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;model.&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict&nbsp;=&nbsp;self.base_model.state_dict(*args,&nbsp;**kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict&nbsp;=&nbsp;self.base_model.state_dict(*args,&nbsp;**kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v_head_state_dict&nbsp;=&nbsp;self.v_head.state_dict(*args,&nbsp;**kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v_head_state_dict&nbsp;=&nbsp;self.v_head.state_dict(*args,&nbsp;**kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k,&nbsp;v&nbsp;in&nbsp;v_head_state_dict.items():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k,&nbsp;v&nbsp;in&nbsp;v_head_state_dict.items():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict[f\"v_head.{k}\"]&nbsp;=&nbsp;v</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict[f\"v_head.{k}\"]&nbsp;=&nbsp;v</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;base_model_state_dict</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;base_model_state_dict</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_50\">50</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_10\">10</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;post_init(self,&nbsp;state_dict):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_51\">51</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;post_init(self,&nbsp;state_dict):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_11\">11</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_52\">52</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_12\">12</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_53\">53</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_13\">13</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.&nbsp;This&nbsp;function&nbsp;removes&nbsp;the&nbsp;`v_head.`&nbsp;prefix&nbsp;from&nbsp;the</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_54\">54</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.&nbsp;This&nbsp;function&nbsp;removes&nbsp;the&nbsp;`v_head.`&nbsp;prefix&nbsp;from&nbsp;the</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keys&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;state&nbsp;dictionary.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_55\">55</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keys&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;state&nbsp;dictionary.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_56\">56</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k&nbsp;in&nbsp;list(state_dict.keys()):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_57\">57</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k&nbsp;in&nbsp;list(state_dict.keys()):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_17\">17</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;\"v_head.\"&nbsp;in&nbsp;k:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_58\">58</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;\"v_head.\"&nbsp;in&nbsp;k:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;state_dict[k.replace(\"v_head.\",&nbsp;\"\")]&nbsp;=&nbsp;state_dict.pop(k)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_59\">59</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;state_dict[k.replace(\"v_head.\",&nbsp;\"\")]&nbsp;=&nbsp;state_dict.pop(k)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head.load_state_dict(state_dict,&nbsp;strict=False)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_60\">60</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head.load_state_dict(state_dict,&nbsp;strict=False)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;del&nbsp;state_dict</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_61\">61</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;del&nbsp;state_dict</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gc.collect()&nbsp;&nbsp;#&nbsp;noqa:&nbsp;E702</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_62\">62</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gc.collect()&nbsp;&nbsp;#&nbsp;noqa:&nbsp;E702</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_63\">63</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_23\">23</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_64\">64</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_24\">24</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForSeq2SeqLMWithHydraValueHead(AutoModelForSeq2SeqLMWithValueHead):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_65\">65</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForSeq2SeqLMWithHydraValueHead(AutoModelForSeq2SeqLMWithValueHead):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\",&nbsp;\"frozen_head\"]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_66\">66</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\",&nbsp;\"frozen_head\"]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[\"num_layers_unfrozen\"]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_67\">67</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[\"num_layers_unfrozen\"]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_27\">27</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_68\">68</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_69\">69</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_70\">70</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_71\">71</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_72\">72</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen:&nbsp;int&nbsp;=&nbsp;-1,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_73\">73</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen:&nbsp;int&nbsp;=&nbsp;-1,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_74\">74</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_75\">75</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_76\">76</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_36\">36</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_77\">77</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_78\">78</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_79\">79</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_80\">80</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_81\">81</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_41\">41</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_82\">82</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_42\">42</td><td nowrap=\"nowrap\">_states:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_83\">83</td><td nowrap=\"nowrap\">_states:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;attentions:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_84\">84</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;attentions:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;cross_attentions:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_85\">85</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;cross_attentions:&nbsp;Optional[Tuple[torch.FloatTensor]]&nbsp;=&nbsp;None</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;value:&nbsp;Optional[torch.FloatTensor]&nbsp;=&nbsp;None</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_86\">86</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;value:&nbsp;Optional[torch.FloatTensor]&nbsp;=&nbsp;None</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_46\">46</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_87\">87</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_47\">47</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_88\">88</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_48\">48</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForCausalLMWithValueHead(PreTrainedModelWrapper):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_89\">89</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForCausalLMWithValueHead(PreTrainedModelWrapper):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"An&nbsp;`AutoModel`&nbsp;class&nbsp;wrapper&nbsp;for&nbsp;`transformers`&nbsp;causal&nbsp;models&nbsp;that&nbsp;have&nbsp;a</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_90\">90</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"An&nbsp;`AutoModel`&nbsp;class&nbsp;wrapper&nbsp;for&nbsp;`transformers`&nbsp;causal&nbsp;models&nbsp;that&nbsp;have&nbsp;a</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_50\">50</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;language&nbsp;modeling&nbsp;head&nbsp;and&nbsp;a&nbsp;value&nbsp;head</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_91\">91</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;language&nbsp;modeling&nbsp;head&nbsp;and&nbsp;a&nbsp;value&nbsp;head</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_51\">51</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_92\">92</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_52\">52</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_93\">93</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_53\">53</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_auto_model_parent_class&nbsp;=&nbsp;transformers.AutoModelForCausalLM</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_94\">94</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_auto_model_parent_class&nbsp;=&nbsp;transformers.AutoModelForCausalLM</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_54\">54</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\"]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_95\">95</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\"]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_55\">55</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_96\">96</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_56\">56</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_97\">97</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_57\">57</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_98\">98</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_58\">58</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_99\">99</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_59\">59</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_100\">100</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_60\">60</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_101\">101</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_61\">61</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_102\">102</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_62\">62</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head&nbsp;=&nbsp;make_head(hf_get_hidden_size(self.base_model.config),&nbsp;1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_103\">103</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head&nbsp;=&nbsp;make_head(hf_get_hidden_size(self.base_model.config),&nbsp;1)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_63\">63</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_104\">104</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_64\">64</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;forward(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_105\">105</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;forward(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_65\">65</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_106\">106</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_66\">66</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_ids:&nbsp;torch.LongTensor&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_107\">107</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_ids:&nbsp;torch.LongTensor&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_67\">67</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;attention_mask:&nbsp;Optional[torch.Tensor]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_108\">108</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;attention_mask:&nbsp;Optional[torch.Tensor]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_68\">68</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;past_key_values:&nbsp;Optional[List[torch.FloatTensor]]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_109\">109</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;past_key_values:&nbsp;Optional[List[torch.FloatTensor]]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_69\">69</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position_ids:&nbsp;Optional[List[torch.FloatTensor]]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_110\">110</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position_ids:&nbsp;Optional[List[torch.FloatTensor]]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_70\">70</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;head_mask:&nbsp;Optional[torch.Tensor]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_111\">111</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;head_mask:&nbsp;Optional[torch.Tensor]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_71\">71</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputs_embeds:&nbsp;Optional[torch.FloatTensor]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_112\">112</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputs_embeds:&nbsp;Optional[torch.FloatTensor]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_72\">72</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;use_cache:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_113\">113</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;use_cache:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_73\">73</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_attentions:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_114\">114</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_attentions:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_74\">74</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_hidden_states:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_115\">115</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_hidden_states:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_75\">75</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return_dict:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_116\">116</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return_dict:&nbsp;Optional[bool]&nbsp;=&nbsp;None,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_76\">76</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;-&gt;&nbsp;Union[Tuple,&nbsp;CausalLMOutputWithValue]:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_117\">117</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;-&gt;&nbsp;Union[Tuple,&nbsp;CausalLMOutputWithValue]:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_77\">77</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs&nbsp;=&nbsp;self.get_compatible_forward_kwargs(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_118\">118</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs&nbsp;=&nbsp;self.get_compatible_forward_kwargs(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_78\">78</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_ids=input_ids,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_119\">119</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;input_ids=input_ids,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_79\">79</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;attention_mask=attention_mask,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_120\">120</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;attention_mask=attention_mask,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_80\">80</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position_ids=position_ids,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_121\">121</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position_ids=position_ids,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_81\">81</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;past_key_values=past_key_values,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_122\">122</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;past_key_values=past_key_values,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_82\">82</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;head_mask=head_mask,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_123\">123</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;head_mask=head_mask,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_83\">83</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputs_embeds=inputs_embeds,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_124\">124</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputs_embeds=inputs_embeds,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_84\">84</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;use_cache=use_cache,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_125\">125</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;use_cache=use_cache,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_85\">85</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_attentions=output_attentions,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_126\">126</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_attentions=output_attentions,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_86\">86</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_hidden_states=output_hidden_states,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_127\">127</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output_hidden_states=output_hidden_states,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_87\">87</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return_dict=return_dict,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_128\">128</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return_dict=return_dict,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_88\">88</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_129\">129</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_89\">89</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs[\"output_hidden_states\"]&nbsp;=&nbsp;True</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_130\">130</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs[\"output_hidden_states\"]&nbsp;=&nbsp;True</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_90\">90</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs[\"return_dict\"]&nbsp;=&nbsp;True</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_131\">131</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;forward_kwargs[\"return_dict\"]&nbsp;=&nbsp;True</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_91\">91</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_132\">132</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_92\">92</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;outputs&nbsp;=&nbsp;self.base_model(**forward_kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_133\">133</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;outputs&nbsp;=&nbsp;self.base_model(**forward_kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_93\">93</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;=&nbsp;self.v_head(outputs.hidden_states[-1]).squeeze(-1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_134\">134</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;=&nbsp;self.v_head(outputs.hidden_states[-1]).squeeze(-1)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_94\">94</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_135\">135</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_95\">95</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;return_dict:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_136\">136</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;return_dict:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_96\">96</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;outputs&nbsp;=&nbsp;(outputs.logits,)&nbsp;+&nbsp;outputs[1:]&nbsp;+&nbsp;(value,)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_137\">137</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;outputs&nbsp;=&nbsp;(outputs.logits,)&nbsp;+&nbsp;outputs[1:]&nbsp;+&nbsp;(value,)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_97\">97</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;outputs</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_138\">138</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;outputs</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_98\">98</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_139\">139</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_99\">99</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;CausalLMOutputWithValue(**outputs,&nbsp;value=value)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_140\">140</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;CausalLMOutputWithValue(**outputs,&nbsp;value=value)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_100\">100</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_141\">141</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_101\">101</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate(self,&nbsp;*args,&nbsp;**kwargs)&nbsp;-&gt;&nbsp;Union[ModelOutput,&nbsp;torch.LongTensor]:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_142\">142</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate(self,&nbsp;*args,&nbsp;**kwargs)&nbsp;-&gt;&nbsp;Union[ModelOutput,&nbsp;torch.LongTensor]:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_102\">102</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;self.base_model.generate(*args,&nbsp;**kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_143\">143</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;self.base_model.generate(*args,&nbsp;**kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_103\">103</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_144\">144</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_104\">104</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;state_dict(self,&nbsp;*args,&nbsp;**kwargs):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_145\">145</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;state_dict(self,&nbsp;*args,&nbsp;**kwargs):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_105\">105</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_146\">146</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_106\">106</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;model.&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_147\">147</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;model.&nbsp;We&nbsp;add&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_107\">107</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_148\">148</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_108\">108</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_149\">149</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_109\">109</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict&nbsp;=&nbsp;self.base_model.state_dict(*args,&nbsp;**kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_150\">150</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict&nbsp;=&nbsp;self.base_model.state_dict(*args,&nbsp;**kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_110\">110</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v_head_state_dict&nbsp;=&nbsp;self.v_head.state_dict(*args,&nbsp;**kwargs)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_151\">151</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v_head_state_dict&nbsp;=&nbsp;self.v_head.state_dict(*args,&nbsp;**kwargs)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_111\">111</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k,&nbsp;v&nbsp;in&nbsp;v_head_state_dict.items():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_152\">152</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k,&nbsp;v&nbsp;in&nbsp;v_head_state_dict.items():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_112\">112</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict[f\"v_head.{k}\"]&nbsp;=&nbsp;v</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_153\">153</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model_state_dict[f\"v_head.{k}\"]&nbsp;=&nbsp;v</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_113\">113</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;base_model_state_dict</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_154\">154</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;base_model_state_dict</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_114\">114</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_155\">155</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_115\">115</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;post_init(self,&nbsp;state_dict):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_156\">156</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;post_init(self,&nbsp;state_dict):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_116\">116</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_157\">157</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_117\">117</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_158\">158</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;to&nbsp;the&nbsp;state&nbsp;dictionary&nbsp;of&nbsp;the&nbsp;wrapped&nbsp;model</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_118\">118</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.&nbsp;This&nbsp;function&nbsp;removes&nbsp;the&nbsp;`v_head.`&nbsp;prefix&nbsp;from&nbsp;the</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_159\">159</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;by&nbsp;prepending&nbsp;the&nbsp;key&nbsp;with&nbsp;`v_head.`.&nbsp;This&nbsp;function&nbsp;removes&nbsp;the&nbsp;`v_head.`&nbsp;prefix&nbsp;from&nbsp;the</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_119\">119</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keys&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;state&nbsp;dictionary.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_160\">160</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keys&nbsp;of&nbsp;the&nbsp;value&nbsp;head&nbsp;state&nbsp;dictionary.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_120\">120</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_161\">161</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\"\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_121\">121</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k&nbsp;in&nbsp;list(state_dict.keys()):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_162\">162</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;k&nbsp;in&nbsp;list(state_dict.keys()):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_122\">122</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;\"v_head.\"&nbsp;in&nbsp;k:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_163\">163</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;\"v_head.\"&nbsp;in&nbsp;k:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_123\">123</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;state_dict[k.replace(\"v_head.\",&nbsp;\"\")]&nbsp;=&nbsp;state_dict.pop(k)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_164\">164</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;state_dict[k.replace(\"v_head.\",&nbsp;\"\")]&nbsp;=&nbsp;state_dict.pop(k)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_124\">124</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head.load_state_dict(state_dict,&nbsp;strict=False)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_165\">165</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.v_head.load_state_dict(state_dict,&nbsp;strict=False)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_125\">125</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;del&nbsp;state_dict</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_166\">166</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;del&nbsp;state_dict</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_126\">126</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gc.collect()&nbsp;&nbsp;#&nbsp;noqa:&nbsp;E702</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_167\">167</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gc.collect()&nbsp;&nbsp;#&nbsp;noqa:&nbsp;E702</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_127\">127</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_168\">168</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_128\">128</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_169\">169</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_129\">129</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForCausalLMWithHydraValueHead(AutoModelForCausalLMWithValueHead):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_170\">170</td><td nowrap=\"nowrap\">class&nbsp;AutoModelForCausalLMWithHydraValueHead(AutoModelForCausalLMWithValueHead):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_130\">130</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\",&nbsp;\"frozen_head\"]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_171\">171</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_modules&nbsp;=&nbsp;[\"v_head\",&nbsp;\"frozen_head\"]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_131\">131</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[\"num_layers_unfrozen\"]</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_172\">172</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;_supported_args&nbsp;=&nbsp;[\"num_layers_unfrozen\"]</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_132\">132</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_173\">173</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_133\">133</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_174\">174</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;__init__(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_134\">134</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_175\">175</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_135\">135</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_176\">176</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base_model:&nbsp;transformers.PreTrainedModel,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_136\">136</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_177\">177</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_137\">137</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen:&nbsp;int&nbsp;=&nbsp;-1,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_178\">178</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen:&nbsp;int&nbsp;=&nbsp;-1,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from3_138\">138</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to3_179\">179</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;):</td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "        <style type=\"text/css\">\n", "            \n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "            td { text-align: left; }\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td { text-align: left; }\n", "        </style>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <table class=\"diff\" id=\"difflib_chg_to4__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <thead><tr><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (PASSED)</th><th class=\"diff_next\"><br /></th><th colspan=\"2\" class=\"diff_header\">origin (FAILED)</th></tr></thead>\n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to4__0\">f</a></td><td class=\"diff_header\" id=\"from4_1\">1</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to4__0\">f</a></td><td class=\"diff_header\" id=\"to4_1\">1</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_2\">2</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;super().__init__(base_model)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_3\">3</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.num_layers_unfrozen&nbsp;=&nbsp;num_layers_unfrozen</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to4__0\"></td><td class=\"diff_header\" id=\"from4_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_4\">4</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;self.num_layers_unfrozen&nbsp;&gt;&nbsp;0:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;branch_class&nbsp;=&nbsp;T5Branch&nbsp;&nbsp;#&nbsp;TODO:&nbsp;Add&nbsp;support&nbsp;for&nbsp;other&nbsp;model&nbsp;branches</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.frozen_head&nbsp;=&nbsp;branch_class(</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.base_model,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;num_layers_unfrozen=self.num_layers_unfrozen,</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to4__top\">t</a></td><td class=\"diff_header\" id=\"from4_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to4__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from4_11\">11</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to4_10\">10</td><td nowrap=\"nowrap\"></td></tr>\n", "        </tbody>\n", "    </table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["refresh_button = widgets.Button(description=\"Refresh\", button_style=\"success\")\n", "generate_button = widgets.Button(description=\"Generate\", button_style=\"success\")\n", "hydra_button = widgets.Button(description=\"Run Hydra\", button_style=\"success\")\n", "\n", "# Creates notebook widgets\n", "def display_sxs():\n", "    left_rst = left_results[select_pid.value]\n", "    right_rst = right_results[select_pid.value]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"prompt\"],\n", "                right_rst[\"prompt\"],\n", "                fromdesc=f\"origin ({left_rst['result']})\",\n", "                todesc=f\"origin ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"generation\"],\n", "                right_rst[\"generation\"],\n", "                fromdesc=f\"origin ({left_rst['result']})\",\n", "                todesc=f\"origin ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "def display_rightxgold():\n", "    right_rst = right_results[select_pid.value]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                right_results[select_pid.value]['ground_truth'],\n", "                right_rst[\"generation\"],\n", "                fromdesc='ground truth',\n", "                todesc=f\"origin ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def display_default():\n", "    display(select_pid)\n", "    print(select_pid.value)\n", "    # By default, we use prompt from right side.\n", "    prompt_widget.value = right_results[select_pid.value][\"prompt\"]\n", "    display(refresh_button, prompt_widget)\n", "    display_sxs()\n", "    # display_rightxgold()\n", "    # display(generate_button)\n", "\n", "\n", "def refresh(b):\n", "    clear_output(wait=True)\n", "    for key in generated_completion.keys():\n", "        generated_completion[key] = None\n", "    prompt_widget.value = right_results[select_pid.value][\"prompt\"]\n", "    display_default()\n", "\n", "refresh_button.on_click(refresh)\n", "\n", "\n", "def generate_button_clicked(b):\n", "    rst = right_results[select_pid.value]\n", "\n", "    print(\"Generating ...\")\n", "    new_completion = starcoder_16b.generate(\n", "        maybe_split_fim_prompt(prompt_widget.value),\n", "        GenerationOptions(\n", "            temperature=0.0,\n", "            max_generated_tokens=560,\n", "            stop_criteria=StopCriteria(\n", "                stop_texts=[], check_stopping_condition_every=4\n", "            ),\n", "        ),\n", "    )\n", "    new_completion = trim_completion(new_completion)\n", "    generated_completion['prompt'] = prompt_widget.value\n", "    generated_completion['generation'] = new_completion\n", "    \n", "\n", "    html = vz_utils.get_diff_html(\n", "        rst[\"ground_truth\"],\n", "        generated_completion['generation'],\n", "        fromdesc=f\"ground truth\",\n", "        todesc=\"new\",\n", "    )\n", "    generated_completion['diff_html'] = html\n", "\n", "    clear_output(wait=True)\n", "    display_default()\n", "    display(HTML(html))\n", "    display(hydra_button)\n", "\n", "\n", "generate_button.on_click(generate_button_clicked)\n", "\n", "def hydra_button_clicked(b):\n", "    rst = right_results[select_pid.value]\n", "    patch = rst[\"patch_rst\"][\"patch\"]\n", "    print(\"Running Hydra ...\")\n", "    new_patch = patch.with_patch_content(generated_completion['generation'])\n", "    _ = driver.dispatch(new_patch)\n", "\n", "hydra_button.on_click(hydra_button_clicked)\n", "\n", "display_default()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Why Retrieval Works"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hypothesis One: Retrieve exact or near-exact implementation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from difflib import SequenceMatcher\n", "\n", "def get_best_match(query, corpus, step=4, flex=3, case_sensitive=False, verbose=False):\n", "    \"\"\"Return best matching substring of corpus.\n", "\n", "    Parameters\n", "    ----------\n", "    query : str\n", "    corpus : str\n", "    step : int\n", "        Step size of first match-value scan through corpus. Can be thought of\n", "        as a sort of \"scan resolution\". Should not exceed length of query.\n", "    flex : int\n", "        Max. left/right substring position adjustment value. Should not\n", "        exceed length of query / 2.\n", "\n", "    Outputs\n", "    -------\n", "    output0 : str\n", "        Best matching substring.\n", "    output1 : float\n", "        Match ratio of best matching substring. 1 is perfect match.\n", "    \"\"\"\n", "\n", "    def _match(a, b):\n", "        \"\"\"Compact alias for <PERSON><PERSON><PERSON><PERSON><PERSON>.\"\"\"\n", "        return SequenceMatcher(None, a, b).ratio()\n", "\n", "    def scan_corpus(step):\n", "        \"\"\"Return list of match values from corpus-wide scan.\"\"\"\n", "        match_values = []\n", "\n", "        m = 0\n", "        while m + qlen - step <= len(corpus):\n", "            match_values.append(_match(query, corpus[m : m-1+qlen]))\n", "            if verbose:\n", "                print(query, \"-\", corpus[m: m + qlen], _match(query, corpus[m: m + qlen]))\n", "            m += step\n", "\n", "        return match_values\n", "\n", "    def index_max(v):\n", "        \"\"\"Return index of max value.\"\"\"\n", "        return max(range(len(v)), key=v.__getitem__)\n", "\n", "    def adjust_left_right_positions():\n", "        \"\"\"Return left/right positions for best string match.\"\"\"\n", "        # bp_* is synonym for 'Best Position Left/Right' and are adjusted \n", "        # to optimize bmv_*\n", "        p_l, bp_l = [pos] * 2\n", "        p_r, bp_r = [pos + qlen] * 2\n", "\n", "        # bmv_* are declared here in case they are untouched in optimization\n", "        bmv_l = match_values[p_l // step]\n", "        bmv_r = match_values[p_l // step]\n", "\n", "        for f in range(flex):\n", "            ll = _match(query, corpus[p_l - f: p_r])\n", "            if ll > bmv_l:\n", "                bmv_l = ll\n", "                bp_l = p_l - f\n", "\n", "            lr = _match(query, corpus[p_l + f: p_r])\n", "            if lr > bmv_l:\n", "                bmv_l = lr\n", "                bp_l = p_l + f\n", "\n", "            rl = _match(query, corpus[p_l: p_r - f])\n", "            if rl > bmv_r:\n", "                bmv_r = rl\n", "                bp_r = p_r - f\n", "\n", "            rr = _match(query, corpus[p_l: p_r + f])\n", "            if rr > bmv_r:\n", "                bmv_r = rr\n", "                bp_r = p_r + f\n", "\n", "            if verbose:\n", "                print(\"\\n\" + str(f))\n", "                print(\"ll: -- value: %f -- snippet: %s\" % (ll, corpus[p_l - f: p_r]))\n", "                print(\"lr: -- value: %f -- snippet: %s\" % (lr, corpus[p_l + f: p_r]))\n", "                print(\"rl: -- value: %f -- snippet: %s\" % (rl, corpus[p_l: p_r - f]))\n", "                print(\"rr: -- value: %f -- snippet: %s\" % (rl, corpus[p_l: p_r + f]))\n", "\n", "        return bp_l, bp_r, _match(query, corpus[bp_l : bp_r])\n", "\n", "    if not case_sensitive:\n", "        query = query.lower()\n", "        corpus = corpus.lower()\n", "\n", "    qlen = len(query)\n", "\n", "    if flex >= qlen/2:\n", "        print(\"Warning: flex exceeds length of query / 2. Setting to default.\")\n", "        flex = 3\n", "\n", "    match_values = scan_corpus(step)\n", "    pos = index_max(match_values) * step\n", "\n", "    pos_left, pos_right, match_value = adjust_left_right_positions()\n", "\n", "    return corpus[pos_left: pos_right].strip(), match_value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "\n", "left_results = load(experiments[select_left_experiment.value].eval_dir)\n", "for pid in left_results:\n", "    # Normalize to patch object.\n", "    cache = left_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        left_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "\n", "right_results = load(experiments[select_right_experiment.value].eval_dir)\n", "for pid in right_results:\n", "    # Normalize to patch object.\n", "    cache = right_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        right_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "# ========================================================================\n", "\n", "left_df = to_dataframe(left_results)[[\"patch_id\", 'repo', \"result\"]]\n", "left_df.columns = [\"patch_id\", 'repo', \"left_result\"]\n", "\n", "right_df = to_dataframe(right_results)[[\"patch_id\",'repo', \"result\"]]\n", "right_df.columns = [\"patch_id\", 'repo',\"right_result\"]\n", "\n", "# Show statistics.\n", "foo = (\n", "    pd.merge(\n", "        left_df, right_df, left_on=[\"patch_id\", 'repo'], right_on=[\"patch_id\", 'repo']\n", "    )\n", "    .groupby(['repo', \"left_result\", \"right_result\"])\n", "    .count()\n", "    .reset_index()\n", ")\n", "foo.columns = ['repo', left_id, right_id, \"count\"]\n", "display(foo.set_index(['repo', left_id, right_id]))\n", "\n", "# Get list of patch ids for each class.\n", "left_df = to_dataframe(left_results)[[\"patch_id\", \"result\"]]\n", "left_df.columns = [\"patch_id\", \"left_result\"]\n", "\n", "right_df = to_dataframe(right_results)[[\"patch_id\", \"result\"]]\n", "right_df.columns = [\"patch_id\", \"right_result\"]\n", "\n", "foo = (\n", "    pd.merge(\n", "        left_df, right_df, left_on=[\"patch_id\"], right_on=[\"patch_id\"]\n", "    )\n", "    .groupby([\"left_result\", \"right_result\"])['patch_id']\n", "    .apply(','.join)\n", "    .reset_index()\n", ")\n", "foo.columns = [left_id, right_id, \"patch_id\"]\n", "patch_id_classified = foo.set_index([left_id, right_id])\n", "patch_id_classified"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def maximum_matched_str(patch_content: str, chunks: list):\n", "    query = patch_content\n", "    matched, max_score, c, rank = None, 0, None, -1\n", "    for rank, chunk in enumerate(chunks):\n", "        try:\n", "            m, score = get_best_match(query, chunk[\"text\"], step=2, flex=4)\n", "        except:\n", "            continue\n", "        if score > max_score:\n", "            matched, max_score, c, rank = m, score, chunk, rank\n", "\n", "    return {\n", "        \"patch_content\": patch_content,\n", "        \"matched\": matched,\n", "        \"score\": max_score,\n", "        \"chunk\": c,\n", "        'rank': rank,\n", "        \"chunk_path\": c[\"parent_doc\"][\"path\"] if c else None,\n", "    }\n", "\n", "\n", "def max_matched_full_prompt(patch_content: str, result: dict):\n", "    chunks = []\n", "    chunks += result[\"patch_rst\"][\"filtered_chunks\"]\n", "    chunks.append(\n", "        {\n", "            \"text\": result[\"patch_rst\"][\"suffix\"],\n", "            'parent_doc': {'path': result['filename']},\n", "        }\n", "    )\n", "    return maximum_matched_str(patch_content, chunks)\n", "\n", "\n", "# _ = maximum_matched_str(\n", "#     right_results[\"CarperAI_trlx/0\"][\"ground_truth\"],\n", "#     right_results[\"CarperAI_trlx/0\"][\"patch_rst\"][\"filtered_chunks\"],\n", "# )\n", "# _[\"score\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Run Maximum matched str"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from multiprocessing.pool import ThreadPool, Pool\n", "import tqdm\n", "import os\n", "\n", "import os\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "\n", "def helper(pid: str):\n", "    # cache = maximum_matched_str(\n", "    #     right_results[pid][\"ground_truth\"],\n", "    #     right_results[pid][\"patch_rst\"][\"filtered_chunks\"],\n", "    # )\n", "    cache = max_matched_full_prompt(\n", "        patch_content=right_results[pid][\"ground_truth\"],\n", "        # patch_content=right_results[pid][\"generation\"],\n", "        result=right_results[pid],\n", "    )\n", "    cache['patch_id'] = pid\n", "    return cache\n", "\n", "keys = patch_id_classified.loc[\"FAILED\", \"PASSED\"][\"patch_id\"].split(\",\")\n", "\n", "# Use multiprocess as this is compute intense.\n", "with Pool(16) as pool:\n", "    data = list(tqdm.tqdm(pool.imap(helper, keys[:5])))\n", "\n", "data[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_best_match = pd.DataFrame(data)[['patch_id', 'score', 'patch_content', 'matched', 'rank']]\n", "df_best_match"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_best_match['score'].hist(bins=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_best_match.loc[df_best_match['score']>0.6].count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_best_match"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_best_match.sort_values('score')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _, row in df_best_match.sort_values(\"score\").iterrows():\n", "    if row[\"patch_id\"] in PATCH_ID_DENYLIST:\n", "        continue\n", "\n", "    # if row[\"score\"] < 0.6:\n", "    #     continue\n", "\n", "    clear_output(wait=True)\n", "    pid = row[\"patch_id\"]\n", "    right_rst = right_results[pid]\n", "    left_rst = left_results[pid]\n", "    \n", "    print(pid)\n", "    print(row[\"score\"])\n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                row[\"patch_content\"],\n", "                # right_rst[\"generation\"],\n", "                row[\"matched\"],\n", "                fromdesc=\"patch content\",\n", "                todesc=\"best matched\",\n", "            )\n", "        )\n", "    )\n", "    \n", "    \n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                right_rst[\"generation\"],\n", "                left_rst[\"generation\"],\n", "                fromdesc=f\"origin ({right_rst['result']})\",\n", "                todesc=f\"origin ({left_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "    if input(\"Continue\") == \"c\":\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Run Hydra Evals"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "def make_new_result(o: dict):\n", "    new_result = copy.deepcopy(left_results[o[\"patch_id\"]])\n", "    chunk = o[\"chunk\"]\n", "\n", "    base_prompt = new_result[\"prompt\"]\n", "    cur_filepath = new_result[\"filename\"]\n", "\n", "\n", "    chunk_text = chunk['text']\n", "    filepath = chunk['parent_doc']['path']\n", "\n", "    chunk_text = \"\\n\".join([f\"# {line}\" for line in chunk_text.split(\"\\n\")])\n", "    new_prompt = f\"\"\"<fim_prefix># --------------------------------------------------\n", "# the below code fragment can be found in:\n", "# {filepath}\n", "# --------------------------------------------------\n", "{chunk_text}\n", "# --------------------------------------------------\n", "\n", "# Current source file:\n", "# {cur_filepath}\n", "{base_prompt}\n", "<fim_suffix>\n", "<fim_middle>\"\"\"\n", "\n", "    new_result[\"prompt\"] = new_prompt\n", "    return new_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from multiprocessing.pool import ThreadPool\n", "from multiprocessing import Queue\n", "import threading\n", "import tqdm\n", "\n", "\n", "queue = Queue()\n", "\n", "def run_generation(o: dict) -> dict:\n", "    new_result = make_new_result(o)\n", "\n", "    # Get new completion with new prompt.\n", "    new_completion = starcoder_16b.generate(\n", "        maybe_split_fim_prompt(new_result['prompt']),\n", "        GenerationOptions(\n", "            temperature=0.0,\n", "            max_generated_tokens=560,\n", "            stop_criteria=StopCriteria(\n", "                stop_texts=[], check_stopping_condition_every=4\n", "            ),\n", "        ),\n", "    )\n", "    new_completion = trim_completion(new_completion)\n", "    new_result[\"completion\"] = new_completion\n", "    new_result[\"generation\"] = new_completion\n", "    # Run hydra eval\n", "    patch = left_results[o[\"patch_id\"]][\"patch_rst\"][\"patch\"]\n", "    new_patch = patch.with_patch_content(new_completion)\n", "    new_result[\"patch_rst\"][\"patch\"] = new_patch\n", "    return new_result\n", "\n", "\n", "_new_results = Queue()\n", "def run_hydra():\n", "    \"\"\"for Multithread\"\"\"\n", "    while True:\n", "        new_result = queue.get()\n", "        if new_result is None:\n", "            queue.put(None)\n", "            break\n", "        print(f\"Running Hydra on {new_result['patch_rst']['patch'].patch_id}. Queue size: {queue.qsize()}\")\n", "        new_patch = new_result[\"patch_rst\"][\"patch\"]\n", "\n", "        output = driver.dispatch(new_patch)\n", "        if \"PASSED\" in output['result_str']:\n", "            new_result[\"result\"] = \"PASSED\"\n", "        elif \"FAILED\" in output['result_str']:\n", "            new_result[\"result\"] = \"FAILED\"\n", "        elif \"TIMEOUT\" in output['result_str']:\n", "            new_result[\"result\"] = \"TIMEOUT\"\n", "        _new_results.put(new_result)\n", "\n", "_thread_pool = []\n", "number_threads = 16\n", "for _ in range(number_threads):\n", "    thread = threading.Thread(target=run_hydra)\n", "    thread.start()\n", "    _thread_pool.append(thread)\n", "\n", "\n", "for d in tqdm.tqdm(data):\n", "    if d['score'] > 0.6:\n", "        queue.put(run_generation(d))\n", "queue.put(None)\n", "\n", "\n", "for t in _thread_pool:\n", "    t.join()\n", "\n", "new_results = [_new_results.get() for _ in range(_new_results.qsize())]\n", "_new_results.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["collections.Counter([e['result'] for e in new_results])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tmp = pd.DataFrame(\n", "    [\n", "        {\n", "            \"patch_id\": e[\"patch_rst\"][\"patch\"].patch_id,\n", "            \"result\": e[\"result\"],\n", "            # 'prompt': e['prompt'],\n", "        }\n", "        for e in new_results\n", "    ]\n", ")\n", "df_joined = pd.merge(df_best_match, df_tmp, left_on=\"patch_id\", right_on=\"patch_id\").set_index(['patch_id'])\n", "df_joined"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "foo = df_joined.loc[df_joined['result']=='PASSED']['score']\n", "plt.scatter(foo, np.ones_like(foo))\n", "\n", "foo = df_joined.loc[df_joined['result']=='FAILED']['score']\n", "plt.scatter(foo, np.ones_like(foo))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hypothesis Two: Something from the suffix helped the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
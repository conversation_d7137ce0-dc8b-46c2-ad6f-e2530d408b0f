{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.api_proxy.client.client import AugmentClient, UploadContent"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["client = AugmentClient(\n", "    url=\"https://dogfood.api.augmentcode.com\",\n", "    token=\"AUGMENT_TOKEN\",\n", ")\n", "client.get_models().models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports and Utilities"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'experimental.vzhao.notebooks.metrics' from '/home/<USER>/augment/experimental/vzhao/notebooks/metrics.py'>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import importlib\n", "import pandas as pd\n", "from IPython.display import HTML, clear_output, display\n", "import ipywidgets as widgets\n", "\n", "from experimental.vzhao.notebooks import utils as vz_utils \n", "from experimental.vzhao.notebooks import metrics \n", "\n", "importlib.reload(vz_utils)\n", "importlib.reload(metrics)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# RepoCode function completion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Register Experiments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function\n", "EXPERIMENTS = {\n", "    ## Baselines\n", "    # 'EmptyCompletion': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/Dr9tY9zu',\n", "    #     description='Sanity Check with empty completion. All test cases should fail.',\n", "    #     weight=100.0,\n", "    # ),\n", "    # 'GoldCompletion': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/TGsikzze',\n", "    #     description='Sanity Check with gold completion. All test cases should pass.',\n", "    #     weight=100.0,\n", "    # ),\n", "    # 'StarCoderBase+NoRetrieval': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/RXxWdbqR',\n", "    #     description='StarCoder prompted with prefix. No RAG.',\n", "    #     weight=100.0,\n", "    # ),\n", "    'RagFIMAV17B+NoRetrieval': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/bSsRodK6',\n", "        description='StarCoder prompted with prefix. No RAG.',\n", "        weight=64.0,\n", "    ),\n", "    \n", "\n", "\n", "    ## GreedyOracle\n", "    \n", "\n", "\n", "\n", "\n", "    # Retrieval, no Oracle\n", "    # 'DiffRe': vz_utils.Experiment(\n", "    #     poc='igor',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/8aynUvdJ',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "\n", "    # 'BoykinTop1': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/eanTSfm7',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    # 'BoykinTop2': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/fDcERf3J',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    # 'BoykinTop10': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/XL8p2dnz',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "\n", "    # pyd<PERSON>7b, <PERSON><PERSON><PERSON>\n", "    'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/Q95ba8mA',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "    'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/57cAF324',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "    'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/a7WqeD3U',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "    # 'RagFIMAV17B+TopMAX': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/3BgHNSCn',\n", "    #     description='Rag prompted with prefix. No RAG.',\n", "    #     weight=1.0,\n", "    # ),\n", "\n", "    # pydiff7b, <PERSON>\n", "    'RagFIMAV17B+OracleTop1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/QzXhJpdX',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=4.0,\n", "    ),\n", "    'RagFIMAV17B+OracleTop2': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/9pHpudYV',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=4.0,\n", "    ),\n", "    'RagFIMAV17B+OracleTop10': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/4YgJoguP',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=4.0,\n", "    ),\n", "\n", "    # pydiff7b, <PERSON>\n", "    # 'Pydiff7BGreedyOracleTop2Rank1000': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/Cm6vgzgP',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=4.0,\n", "    # ),\n", "    \n", "\n", "    # ## Greedy Oracle\n", "    # 'GreedyOracleTop1Rank1000': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/fDJ4RE55',\n", "    #     description='Greedy oracle max number chunk = 1',\n", "    #     weight=1.2,\n", "    # ),\n", "    # 'GreedyOracleTop1Rank100': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/9oY3XT2V',\n", "    #     description='Greedy oracle max number chunk = 1',\n", "    #     weight=1.2,\n", "    # ),\n", "\n", "    # ## Oracle (non Greedy)\n", "    # 'OracleTop2Rank100': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/ea67TC3R',\n", "    #     description='Oracle max number chunk = 2',\n", "    #     weight=1.2,\n", "    # ),\n", "    # 'OracleMAXRank1000': vz_utils.Experiment(\n", "    #     poc='igor',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/kXRjLYLi',\n", "    #     description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "    #     weight=50.0,\n", "    # ),\n", "}\n", "\n", "\n", "path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/function_patch_ids_small.json\"\n", "import json\n", "ALLOW_DENY_IDS = json.load(open(path_to_json, 'r'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multiline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Multiline\n", "EXPERIMENTS = {\n", "    ## Baselines\n", "    'EmptyCompletion': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/RxkjHdTh',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.0,\n", "    ),\n", "    'RagFIMAV17B+NoRetrieval': vz_utils.Experiment(\n", "        poc='colin',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/XAPHwL3r',\n", "        description='StarCoder prompted with prefix. No RAG.',\n", "        weight=32.0,\n", "    ),\n", "    # 'RagFIMAV17B+NoRetrieval+NoIndent': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/SguwrbfF',\n", "    #     description='StarCoder prompted with prefix. No RAG.',\n", "    #     weight=32.0,\n", "    # ),\n", "    # 'FIMAv2+No': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/Eh9VPVR9',\n", "    #     description='StarCoder prompted with prefix. No RAG.',\n", "    #     weight=32.0,\n", "    # ),\n", "    # 'FIMAv2+No+NoIndent': vz_utils.Experiment(\n", "    #     poc='colin',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/DjmwjA2Y',\n", "    #     description='StarCoder prompted with prefix. No RAG.',\n", "    #     weight=32.0,\n", "    # ),\n", "\n", "    ## Boy<PERSON><PERSON> Retrieval, no oracle\n", "    # 'BoykinTop1': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/84q7HC3X',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    # 'BoykinTop2': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/fBBE8qEy',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "    # 'BoykinTop10': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/bCnaf45T',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=1.0,\n", "    # ),\n", "\n", "    # pyd<PERSON>7b, <PERSON><PERSON><PERSON>\n", "    'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/L9kTWMQj',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "    'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/GWkQiefu',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "    'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/f2d3poDc',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=1.0,\n", "    ),\n", "\n", "    # pydiff7b, <PERSON>\n", "    'RagFIMAV17B+OracleTop1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/MVS875TU',\n", "        description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "        weight=4.0,\n", "    ),\n", "    # 'RagFIMAV17B+OracleTop2': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/BvjDubA9',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix. Rerank top 1000.',\n", "    #     weight=4.0,\n", "    # ),\n", "    # 'RagFIMAV17B+OracleTop10': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/NXeCus8a',\n", "    #     description='StarCoder augmented with diff trained retriever + FIM prompting with prefix and suffix.',\n", "    #     weight=4.0,\n", "    # ),\n", "\n", "\n", "    ## Oracle Greedy\n", "    # 'GreedyOracleTop1Rank100': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/9oY3XT2V',\n", "    #     description='Greedy oracle max number chunk = 1',\n", "    #     weight=1.2,\n", "    # ),\n", "\n", "\n", "\n", "    ## Oracle (non Greedy)\n", "    # 'OracleTop2Rank100': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/bPNZL9X4',\n", "    #     description='Oracle max number chunk = 2',\n", "    #     weight=2,\n", "    # ),\n", "    # 'OracleTop5Rank100': vz_utils.Experiment(\n", "    #     poc='vzhao',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/gDpSJ7Tm',\n", "    #     description='Oracle max number chunk = 5',\n", "    #     weight=1,\n", "    # ),\n", "\n", "    # 'DiffRe1000+PPL1000': vz_utils.Experiment(\n", "    #     poc='igor',\n", "    #     eval_dir='/mnt/efs/augment/eval/jobs/oY8zbris',\n", "    #     description='Rerank top 1000 retrieved chunks by perplexityo on ground truth.',\n", "    #     weight=10.0,\n", "    # )\n", "}\n", "\n", "path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_small.json\"\n", "import json\n", "ALLOW_DENY_IDS = json.load(open(path_to_json, 'r'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### AllLang"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENTS = {\n", "    # Baseline\n", "    'RagFIMAV17B+NoRetrieval': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/ax29JN3u',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    # Dense Retrieval\n", "    'RagFIMAV17B+Top1': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/MBTPu5wQ',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    'RagFIMAV17B+Top2': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/dfJSHNvz',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    'RagFIMAV17B+Top10': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/HuobqgP4',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.\n", "    ),\n", "    # Oracle\n", "    \n", "}\n", "\n", "ALLOW_DENY_IDS = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistics"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">patch_id</th>\n", "      <th colspan=\"2\" halign=\"left\">num_chunks_in_prompt</th>\n", "      <th colspan=\"2\" halign=\"left\">line_prompt_minus_line_suffix</th>\n", "    </tr>\n", "    <tr>\n", "      <th>pass/fail</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "      <th>FAILED</th>\n", "      <th>PASSED</th>\n", "    </tr>\n", "    <tr>\n", "      <th>experiment</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>EmptyCompletion</th>\n", "      <td>413</td>\n", "      <td>134</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+NoRetrieval</th>\n", "      <td>124</td>\n", "      <td>155</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9116.637097</td>\n", "      <td>9377.045161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top1</th>\n", "      <td>104</td>\n", "      <td>175</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>10088.221154</td>\n", "      <td>10773.531429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top2</th>\n", "      <td>97</td>\n", "      <td>182</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>11607.628866</td>\n", "      <td>11932.879121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+Top10</th>\n", "      <td>92</td>\n", "      <td>187</td>\n", "      <td>9.782609</td>\n", "      <td>9.796791</td>\n", "      <td>21697.217391</td>\n", "      <td>22347.433155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RagFIMAV17B+OracleTop1</th>\n", "      <td>83</td>\n", "      <td>196</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>10301.686747</td>\n", "      <td>10686.836735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        patch_id        num_chunks_in_prompt             \n", "pass/fail                 FAILED PASSED               FAILED    PASSED   \n", "experiment                                                               \n", "EmptyCompletion              413    134             0.000000  0.000000  \\\n", "RagFIMAV17B+NoRetrieval      124    155             0.000000  0.000000   \n", "RagFIMAV17B+Top1             104    175             1.000000  1.000000   \n", "RagFIMAV17B+Top2              97    182             2.000000  2.000000   \n", "RagFIMAV17B+Top10             92    187             9.782609  9.796791   \n", "RagFIMAV17B+OracleTop1        83    196             1.000000  1.000000   \n", "\n", "                        line_prompt_minus_line_suffix                \n", "pass/fail                                      FAILED        PASSED  \n", "experiment                                                           \n", "EmptyCompletion                              0.000000      0.000000  \n", "RagFIMAV17B+NoRetrieval                   9116.637097   9377.045161  \n", "RagFIMAV17B+Top1                         10088.221154  10773.531429  \n", "RagFIMAV17B+Top2                         11607.628866  11932.879121  \n", "RagFIMAV17B+Top10                        21697.217391  22347.433155  \n", "RagFIMAV17B+OracleTop1                   10301.686747  10686.836735  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from multiprocessing.pool import ThreadPool, Pool\n", "import re\n", "import collections\n", "\n", "\n", "def helper(key, exp):\n", "    results = vz_utils.load(exp)\n", "    output = []\n", "    for pid in results:\n", "        # This works for 'comment2' style prompt.\n", "        num_chunks_in_prompt = sum(\n", "            [\n", "                \"# the below code fragment can be found in:\" == line\n", "                for line in results[pid][\"prompt\"].splitlines()\n", "            ]\n", "        )\n", "        # This works for <PERSON> prompt.\n", "        num_chunks_in_prompt = max( num_chunks_in_prompt, len(re.findall('<filename>', results[pid][\"prompt\"])))\n", "\n", "        output.append(\n", "            \n", "            {\n", "                \"experiment\": key,\n", "                \"patch_id\": pid,\n", "                \"pass/fail\": results[pid][\"result\"],\n", "                \"num_chunks_in_prompt\": num_chunks_in_prompt,\n", "                \"line_prompt_minus_line_suffix\": len(\n", "                    results[pid][\"prompt\"].split(\"<fim_middle>\")[0]\n", "                ),\n", "                \"weight\": exp.weight,\n", "            }\n", "        )\n", "    return output, results\n", "\n", "\n", "data = []\n", "results = {}\n", "for key, exp in EXPERIMENTS.items():\n", "    d, rst = helper(key, exp)\n", "    data.append(d)\n", "    results[key] = rst\n", "\n", "# pool = ThreadPool(4)\n", "# pool = Pool(16)\n", "# data = pool.starmap(helper, EXPERIMENTS.items())\n", "\n", "df = []\n", "for d in data:\n", "    df.extend(d)\n", "\n", "df = pd.DataFrame(df)\n", "\n", "# Optional: Filter IDS.\n", "# df = df.loc[df[\"patch_id\"].apply(lambda x: x in ALLOW_DENY_IDS[\"allowed\"]), :]\n", "# df = df.loc[df[\"patch_id\"].apply(lambda x: x in tmp_list), :]\n", "\n", "clear_output(wait=True)\n", "df.groupby([\"experiment\", \"pass/fail\"]).agg(\n", "    {\n", "        \"patch_id\": \"count\",\n", "        \"num_chunks_in_prompt\": \"mean\",\n", "        \"line_prompt_minus_line_suffix\": \"mean\",\n", "    }\n", ").reset_index().pivot(columns=[\"pass/fail\"], index=\"experiment\").fillna(0).sort_values(\n", "    [(\"patch_id\", \"PASSED\")]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gets a list of pid for retrieval eval."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['amazon-science/patchcore-inspection/1b371d87e25563c5', 'amazon-science/patchcore-inspection/3315b943ab58930f', 'amazon-science/patchcore-inspection/3d7c20bcb10c9362', 'amazon-science/patchcore-inspection/458e77f43260205e', 'amazon-science/patchcore-inspection/8e2208b9d74c0a39', 'amazon-science/patchcore-inspection/993e1a0fa8421d74', 'amazon-science/patchcore-inspection/bda1c5661ad05766', 'deepmind/tracr/03972df225b4d47d', 'deepmind/tracr/054d46bb3af6bd21', 'deepmind/tracr/05b017f1306d50ba', 'deepmind/tracr/0cd0bb2b8420994b', 'deepmind/tracr/1a2d5255c580146d', 'deepmind/tracr/1e9822bc574913d9', 'deepmind/tracr/20ccfe84a4601988', 'deepmind/tracr/26d6c2434f9e4d71', 'deepmind/tracr/29481bf0b1809422', 'deepmind/tracr/2967275dcdb2f03e', 'deepmind/tracr/2a02fc2675f511b0', 'deepmind/tracr/2b8960d851596f41', 'deepmind/tracr/34af47958c1e8cb5', 'deepmind/tracr/48e9116650f87004', 'deepmind/tracr/5a4256fe26c8ee3a', 'deepmind/tracr/5e50466e6777b080', 'deepmind/tracr/62d8ba1ff46cc37a', 'deepmind/tracr/6be2d5acbe643da6', 'deepmind/tracr/744157f8501226cc', 'deepmind/tracr/750d873d713d793a', 'deepmind/tracr/771704da5e7ce214', 'deepmind/tracr/784bbaaafed2bb13', 'deepmind/tracr/7ba63a0bc5c52e96', 'deepmind/tracr/7bc09c205fe906f3', 'deepmind/tracr/7d8242942bacd42b', 'deepmind/tracr/824e077957d0e502', 'deepmind/tracr/848002a031048afe', 'deepmind/tracr/85c9956b3d59159c', 'deepmind/tracr/8a38526be640b272', 'deepmind/tracr/8ae6ccd151e3ff99', 'deepmind/tracr/8e2139a0ee09216f', 'deepmind/tracr/938e990789012698', 'deepmind/tracr/9efdbb8e932559bc', 'deepmind/tracr/b6920d5ed0501169', 'deepmind/tracr/c19046d6c9a58aac', 'deepmind/tracr/c3dec94fa1b7c7e0', 'deepmind/tracr/c6456ac43cd45560', 'deepmind/tracr/caf19b949724d99a', 'deepmind/tracr/dc909ac464f834a3', 'deepmind/tracr/de46d2375624f881', 'deepmind/tracr/de7d9293fe1c7e91', 'deepmind/tracr/e2a67ee4d9cc9698', 'facebookresearch/omnivore/0bd01310fdf7c2fa', 'facebookresearch/omnivore/0de809a47b6282fc', 'facebookresearch/omnivore/101a9b0d53507c3a', 'facebookresearch/omnivore/174fbb941081e570', 'facebookresearch/omnivore/2e8a7392c337c297', 'facebookresearch/omnivore/35d1c46a1893f285', 'facebookresearch/omnivore/383bb4de9d485ccb', 'facebookresearch/omnivore/4255146be3f8adc1', 'facebookresearch/omnivore/47713e23733c741e', 'facebookresearch/omnivore/48701a2b80d371a0', 'facebookresearch/omnivore/519930b4238d09e1', 'facebookresearch/omnivore/5688c9e2eca2ef1a', 'facebookresearch/omnivore/6a09735c1a67212a', 'facebookresearch/omnivore/6dd301fa10fe197d', 'facebookresearch/omnivore/6f35b50753385be5', 'facebookresearch/omnivore/7a8636194824a169', 'facebookresearch/omnivore/7e82f098aedb3106', 'facebookresearch/omnivore/8f99b9f88832847b', 'facebookresearch/omnivore/90aca2ea159b2ac5', 'facebookresearch/omnivore/a4002cbf7b6eb13c', 'facebookresearch/omnivore/a77d89c7e11968c7', 'facebookresearch/omnivore/c980ff5773586e5f', 'facebookresearch/omnivore/cba679f315093dc3', 'facebookresearch/omnivore/ce3824a22e9a7782', 'facebookresearch/omnivore/d1c4badab63345ba', 'facebookresearch/omnivore/d58325b7f2a4316c', 'facebookresearch/omnivore/f3aa20546e084a21', 'facebookresearch/omnivore/f97f9ef2bccb5c7a', 'facebookresearch/omnivore/fb6e5505df27fdd2', 'facebookresearch/omnivore/fe3af57f2f79a17a', 'google/lightweight_mmm/056395259264d494', 'google/lightweight_mmm/0a71cb35858d164b', 'google/lightweight_mmm/0fec6101d8a04990', 'google/lightweight_mmm/1621c56e79ddc2ff', 'google/lightweight_mmm/1ca509b2ca59217e', 'google/lightweight_mmm/23126a6f8661ba37', 'google/lightweight_mmm/303bef2dff441a17', 'google/lightweight_mmm/34cb95a518d4699e', 'google/lightweight_mmm/368f89d1cffe0083', 'google/lightweight_mmm/3ed918dfe881ce79', 'google/lightweight_mmm/4f8d1c540e8a7dfe', 'google/lightweight_mmm/55e81e6f7458583c', 'google/lightweight_mmm/6d8c668b3e79b520', 'google/lightweight_mmm/6ee7a4a18ad6d5ca', 'google/lightweight_mmm/6f8938dfa5ef4948', 'google/lightweight_mmm/9cb0cebd11b31d6f', 'google/lightweight_mmm/a24b430f09782c62', 'google/lightweight_mmm/a6f8d26f9dbc48ef', 'google/lightweight_mmm/a801c9b115f3ca32', 'google/lightweight_mmm/ad3f0cf292f8a83a', 'google/lightweight_mmm/b8b42a070249b447', 'google/lightweight_mmm/bde33fcda5eb8074', 'google/lightweight_mmm/c08e4e8d797a055f', 'google/lightweight_mmm/e1ce80c2d6bf41f2', 'google/lightweight_mmm/e78c7215706db94b', 'google/lightweight_mmm/ebd822c0d7345ec0', 'google/lightweight_mmm/ee0cf412a6ead68d', 'google/lightweight_mmm/f0900e723a9140ac', 'google/lightweight_mmm/f223b6eec5e10c1a', 'google/lightweight_mmm/f742ecf4596010fe', 'google/lightweight_mmm/f870bd17626fe495', 'google/lightweight_mmm/f9e7736381b2076b', 'google/lightweight_mmm/fb884c597d35988d', 'leopard-ai/betty/0eb68040527e2a71', 'leopard-ai/betty/1f6d2a196793de73', 'leopard-ai/betty/21ca6095a2787246', 'leopard-ai/betty/259758b448b78995', 'leopard-ai/betty/2ed48fd8abe02d21', 'leopard-ai/betty/2fc20314c21f0c3d', 'leopard-ai/betty/536372c15c9cbcd7', 'leopard-ai/betty/53c9a7aa6ee5e836', 'leopard-ai/betty/61200f87e12ddee4', 'leopard-ai/betty/6a24042985d38012', 'leopard-ai/betty/6bc01edcaf5d685f', 'leopard-ai/betty/762512e0e61051d0', 'leopard-ai/betty/7db1e7f9d2120432', 'leopard-ai/betty/87dee6d7a1f06e02', 'leopard-ai/betty/8b732f4c612d23cc', 'leopard-ai/betty/8f4c536cede9aa06', 'leopard-ai/betty/9685655abc9921ba', 'leopard-ai/betty/9a30d3ff92a5e90a', 'leopard-ai/betty/9d13cdac7f93b272', 'leopard-ai/betty/ac8e166abb403dfc', 'leopard-ai/betty/ace1c4e167a2cc5b', 'leopard-ai/betty/ae7c105c87bc243d', 'leopard-ai/betty/c933afbcdd66e637', 'leopard-ai/betty/cf648e15526a3c1e', 'leopard-ai/betty/cfbf64b85c2c6713', 'leopard-ai/betty/f52719c4d5ee6e24', 'leopard-ai/betty/f7606d8c6c7824c6', 'lucidrains/imagen-pytorch/038b89859aeface3', 'lucidrains/imagen-pytorch/0b82f4f872840a7a', 'lucidrains/imagen-pytorch/15a2789940a31fa1', 'lucidrains/imagen-pytorch/1d7ea139678be400', 'lucidrains/imagen-pytorch/1fdfe81e5705ca13', 'lucidrains/imagen-pytorch/289d0cda0d611f4f', 'lucidrains/imagen-pytorch/3745d37e0dd3604d', 'lucidrains/imagen-pytorch/3dc81c4dc4e4be7e', 'lucidrains/imagen-pytorch/40bfa75eeb9fdcee', 'lucidrains/imagen-pytorch/4fc72f85f27c5a1f', 'lucidrains/imagen-pytorch/50a3904c269b091f', 'lucidrains/imagen-pytorch/6c4c0ab8e53d8ef6', 'lucidrains/imagen-pytorch/6dbf7cb8dc0e007d', 'lucidrains/imagen-pytorch/71ffa8b20792db49', 'lucidrains/imagen-pytorch/771117b83a5fc81e', 'lucidrains/imagen-pytorch/786c36b04599a281', 'lucidrains/imagen-pytorch/91e019f14aebe249', 'lucidrains/imagen-pytorch/9443148276554fc2', 'lucidrains/imagen-pytorch/95801b4aedc1dad7', 'lucidrains/imagen-pytorch/a1ef3af1792d62be', 'lucidrains/imagen-pytorch/a8bdd8c6fdd3fc4d', 'lucidrains/imagen-pytorch/b5bbee5e47fc9be2', 'lucidrains/imagen-pytorch/bafa307ce49a9832', 'lucidrains/imagen-pytorch/c047614425d396ea', 'lucidrains/imagen-pytorch/c8c364329235d148', 'lucidrains/imagen-pytorch/d3ae9ac78594ed2a', 'lucidrains/imagen-pytorch/db2d4007bfc85df4', 'lucidrains/imagen-pytorch/df5fac6fdd21cfed', 'lucidrains/imagen-pytorch/e3014b18d6785e9b', 'lucidrains/imagen-pytorch/eb9caebd875923e6', 'lucidrains/imagen-pytorch/f1801995403c7942', 'lucidrains/imagen-pytorch/f8b45d26658cb514', 'maxhumber/redframes/00852872974a91e9', 'maxhumber/redframes/013cef3eb31f9004', 'maxhumber/redframes/13db34111e9bab4d', 'maxhumber/redframes/14f6b325d1e66018', 'maxhumber/redframes/17af995c0c25866d', 'maxhumber/redframes/191264707c39f99c', 'maxhumber/redframes/198f50e3e057c8b3', 'maxhumber/redframes/22a71b1caae1abfc', 'maxhumber/redframes/3af3fe5ff9a29dfc', 'maxhumber/redframes/4cc0071e3d30b003', 'maxhumber/redframes/5948b5b72baaf246', 'maxhumber/redframes/5e00478ac5964952', 'maxhumber/redframes/6119034865e85c73', 'maxhumber/redframes/68430bf5ea050fb2', 'maxhumber/redframes/7202ce738271e226', 'maxhumber/redframes/8227baad43e4b765', 'maxhumber/redframes/8e49e01575351be7', 'maxhumber/redframes/9497fd5e33037f22', 'maxhumber/redframes/9fc5ba6569bd5eb5', 'maxhumber/redframes/c9b8e4704c5ac41e', 'maxhumber/redframes/cc03fa988a60d99c', 'maxhumber/redframes/d2098d16af61496a', 'maxhumber/redframes/d4d155966fc66006', 'maxhumber/redframes/f99e31cb02ff49c0', 'trlx/d60a6e949472cbcf']\n", "196\n"]}], "source": ["want_pids = {\n", "    \"EmptyCompletion\": \"FAILED\",\n", "    # 'StarCoderBase+NoRetrieval': 'FAILED',\n", "    # \"RagFIMAV17B+NoRetrieval\": \"FAILED\",\n", "    # 'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    \"RagFIMAV17B+OracleTop1\": \"PASSED\",\n", "    # \"RagFIMAV17B+OracleTop2\": \"PASSED\",\n", "    # \"RagFIMAV17B+OracleTop10\": \"PASSED\",\n", "}\n", "\n", "\n", "pid_list = set(df[\"patch_id\"])\n", "for exp, pf in want_pids.items():\n", "    _ids = set(df.loc[(df[\"experiment\"] == exp) & (df[\"pass/fail\"] == pf)][\"patch_id\"])\n", "    pid_list = pid_list & _ids\n", "pid_list = list(sorted(pid_list))\n", "print(pid_list)\n", "print(len(pid_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optional: Save retrieval eval ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "output = {'allowed': pid_list}\n", "with open(\"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_retrieval_wins.json\", 'w') as f:\n", "    json.dump(output, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prototype Retrieval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.retrieval import chunking_functions\n", "from research.core.types import Document\n", "from research.eval.harness.tasks.hydra_task import download_hydra\n", "from research.core.model_input import ModelInput\n", "from research.static_analysis.parsing import ScopeOrSpan, GlobalParser, SrcScope, SrcSpan\n", "\n", "from research.retrieval.scorers.good_enough_bm25_scorer import <PERSON><PERSON><PERSON><PERSON><PERSON>25Scorer\n", "import json\n", "from research.eval.patch_lib import Patch\n", "from research.retrieval.prompt_formatters import _as_stub"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid = pid_list[2]\n", "print(pid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_query(patch: Patch):\n", "    root_scope = GlobalParser.parse(patch.file_content, patch.file_name, \"python\")\n", "    output = []\n", "    for span in root_scope.get_all_spans():\n", "        if span.get_leaf_unit().range.contains(patch.range):\n", "            output.append(span)\n", "    return output\n", "\n", "\n", "def proto_retrieval(pid):\n", "    repo_id = tuple(pid.split('/')[:2])\n", "    # Step 1: Chunking a repo\n", "    chunker = chunking_functions.ScopeAwareChunker(max_lines_per_chunk=256)\n", "    (\n", "        _,\n", "        _,\n", "        files_for_retrieval_by_repo,\n", "    ) = download_hydra(\n", "        \"deepmind\", \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines\"\n", "    )\n", "\n", "    id2chunks = {}\n", "    id2docs = {}\n", "    chunks = {}\n", "    for doc in files_for_retrieval_by_repo[repo_id]:\n", "        id2docs[doc.id] = doc\n", "        if chunks_ls := chunker.split_into_chunks(doc):\n", "            chunks[doc.id] = chunks_ls\n", "        if chunks_ls:\n", "            for c in chunks_ls:\n", "                id2chunks[c.id] = c\n", "    # Step 2: Adds chunks to scorer.\n", "    bm25_scorer = GoodE<PERSON>ughBM25Scorer()\n", "    bm25_scorer.load()\n", "    for c in chunks.values():\n", "        bm25_scorer.add_doc(c)\n", "    \n", "    # Step 3: Test scorer on the selected set.\n", "    rst = results['RagFIMAV17B+OracleTop1'][pid]\n", "    patch = Patch.from_json(json.dumps(rst['patch_rst']['patch']))\n", "    # Query:\n", "    foo = get_query(patch)\n", "    query = _as_stub(foo[0])\n", "    print(query)\n", "\n", "    import numpy as np\n", "    scored, scores =  bm25_scorer.score(query)\n", "    idx = np.argsort(scores)[::-1]\n", "    scored = [scored[i] for i in idx]\n", "    \n", "\n", "    chunks_scored = [id2chunks[s[1]] for s in scored]\n", "    scores = [scores[i] for i in idx]\n", "    return chunks_scored, scores, query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sanity check document formatter.\n", "\n", "toks = bm25_scorer.document_formatter.prepare_prompt(\n", "    ModelInput(retrieved_chunks=[c[3]])\n", ")\n", "\n", "print(bm25_scorer.document_formatter.tokenizer.detokenize(toks))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rst = results['RagFIMAV17B+OracleTop1'][pid]\n", "patch = Patch.from_json(json.dumps(rst['patch_rst']['patch']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_query(patch: Patch):\n", "    root_scope = GlobalParser.parse(patch.file_content, patch.file_name, \"python\")\n", "    output = []\n", "    for span in root_scope.get_all_spans():\n", "        if span.get_leaf_unit().range.contains(patch.range):\n", "            output.append(span)\n", "    return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["foo = get_query(patch)\n", "query = _as_stub(foo[0])\n", "print(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(_as_stub(foo[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(foo[0].code)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(patch.file_content[patch.char_start:patch.char_end])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(foo[0].code.replace(patch.file_content[patch.char_start:patch.char_end], \" \"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compare SxS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Utilities.\n", "\n", "def print_prefix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\"\\n\".join(left_rst[\"patch_rst\"][\"prefix\"].splitlines()[-num_line:]))\n", "\n", "\n", "def generation_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"completion\"],\n", "                right_rst[\"completion\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def print_suffix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\"\\n\".join(left_rst[\"patch_rst\"][\"suffix\"].splitlines()[:num_line]))\n", "\n", "\n", "def prompt_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"prompt\"],\n", "                right_rst[\"prompt\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def generation_vs_ground_truth(pid: str, results):\n", "    rst = results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                rst[\"ground_truth\"],\n", "                rst[\"generation\"],\n", "                fromdesc=\"Ground Truth\",\n", "                todesc=f\"{select_right_experiment.value} ({rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def best_matched_sxs(pid: str, feature=\"ground_truth\"):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display_num_lines = 5\n", "\n", "    left_best_matched, left_score = vz_utils.get_best_match(\n", "        query=left_rst[feature],\n", "        corpus=left_rst[\"prompt\"],\n", "    )\n", "    splits = left_rst[\"prompt\"].split(left_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    lpre, lsuf = splits\n", "    lpre = '\\n'.join(lpre.splitlines()[-display_num_lines:])\n", "    lsuf = '\\n'.join(lsuf.splitlines()[:display_num_lines])\n", "\n", "    right_best_matched, right_score = vz_utils.get_best_match(\n", "        query=right_rst[feature],\n", "        corpus=right_rst[\"prompt\"],\n", "    )\n", "    splits = right_rst[\"prompt\"].split(right_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    rpre, rsuf = splits\n", "    rpre = '\\n'.join(rpre.splitlines()[-display_num_lines:])\n", "    rsuf = '\\n'.join(rsuf.splitlines()[:display_num_lines])\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lpre,\n", "                rpre,\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_best_matched,\n", "                right_best_matched,\n", "                fromdesc=f\"match score: {left_score}\",\n", "                todesc=f\"match score: {right_score}\",\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lsuf,\n", "                rsuf,\n", "            )\n", "        )\n", "    )\n", "\n", "    return left_best_matched, right_best_matched\n", "\n", "def last_n_lines(text, n=10):\n", "    return '\\n'.join(text.splitlines()[-n:])\n", "\n", "def bold(text: str):\n", "    return '\\033[1m' + text + '\\033[0m'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["select_patch_id = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description='Patch ID:',\n", "    disabled=False,\n", ")\n", "display(select_patch_id)\n", "\n", "oracle_results = results['RagFIMAV17B+OracleTop1']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rst = oracle_results[select_patch_id.value]\n", "oracle_chunk = rst['prompt'].split('<fim_prefix>')[0]\n", "\n", "print(bold('RETRIEVAL'))\n", "print(oracle_chunk)\n", "\n", "print(bold('PREFIX'))\n", "print(last_n_lines(rst['patch_rst']['prefix'], 20))\n", "\n", "print(bold('COMPLETION'))\n", "print(rst['completion'])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks_scored, scores, query = proto_retrieval(select_patch_id.value)\n", "clear_output()\n", "print(query)\n", "\n", "show_topk = 20\n", "for chunk, score in zip(chunks_scored[:show_topk], scores[:show_topk]):\n", "    print(f\"----- {score}/{metrics.jaccard_similarity(oracle_chunk, chunk.text)} ------\")\n", "    print(chunk.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from research.eval.patch_lib import Patch\n", "import json"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["rst = results['RagFIMAV17B+OracleTop1']['facebookresearch/omnivore/0bd01310fdf7c2fa']\n", "patch = Patch.from_json(json.dumps(rst['patch_rst']['patch']))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from research.static_analysis import parsing\n", "\n", "parser = parsing.ScopeTreeParser(parse_errored_root=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0:7954\n", "2877:3674\n"]}], "source": ["root_scope = parser.parse(patch.file_content, patch.file_name, \"python\")\n", "output = []\n", "for span in root_scope.get_all_spans():\n", "    if overlap := span.get_leaf_unit().range.intersect(patch.range):\n", "        output.append((len(overlap), span))\n", "output = list(sorted(output, reverse=True))\n", "span = output[0][1]\n", "print(span.range)\n", "print(span.get_leaf_unit().range)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["IntRange(3138, 3211)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["patch.range"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<PERSON><PERSON><PERSON><PERSON><PERSON>(2877, 3138), <PERSON><PERSON><PERSON><PERSON><PERSON>(3211, 3674)]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["bar =  span.get_leaf_unit().range - patch.range\n", "bar"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["'    parameter_names = []\\n    for param_name in scheduler_cfg.param_names:'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["rst['ground_truth']"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["import textwrap\n", "file_content = textwrap.dedent(\n", "            \"\"\"\\\n", "            import itertools\n", "            \n", "            class AClass:\n", "            \n", "                def __init__(self):\n", "                    pass\n", "\n", "                def function(foo, bar):\n", "                    print(foo)\n", "                    print(bar)\n", "                    print('another)\n", "                    return None\n", "            \"\"\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import itertools\n", "\n", "class AClass:\n", "\n", "    def __init__(self):\n", "        pass\n", "\n", "    def function(foo, bar):\n", "        print(foo)\n", "        print(bar)\n"]}], "source": ["print(file_content[:136])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Copyright (c) Meta Platforms, Inc. and affiliates.\n", "# All rights reserved.\n", "\n", "# This source code is licensed under the license found in the\n", "# LICENSE file in the root directory of this source tree.\n", "\n", "# pyre-ignore-all-errors\n", "\n", "import fnmatch\n", "import itertools\n", "import logging\n", "from dataclasses import dataclass\n", "from typing import Any, Dict, Iterable, List, Optional, Set, Tuple, Union\n", "\n", "import hydra\n", "import torch\n", "import torch.nn as nn\n", "from omegaconf import DictConfig, MISSING\n", "\n", "from . import LARS, OmniOptimizer\n", "\n", "\n", "def create_lars_optimizer(params, opt, **lars_params):\n", "    optim = hydra.utils.instantiate(opt, params=params)\n", "    return LARS(optim, **lars_params)\n", "\n", "\n", "def validate_param_group_params(param_groups, model):\n", "    parameters = [set(param_group[\"params\"]) for param_group in param_groups]\n", "    model_parameters = {parameter for _, parameter in model.named_parameters()}\n", "    for p1, p2 in itertools.permutations(parameters, 2):\n", "        assert p1.isdisjoint(p2), \"Scheduler generated param_groups should be disjoint\"\n", "    assert (\n", "        set.union(*parameters) == model_parameters\n", "    ), \"Scheduler generated param_groups include all parameters of the model\"\n", "\n", "\n", "def unix_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig, model: nn.<PERSON><PERSON><PERSON>\n", ") -> Union[None, Set[str]]:\n", "    if \"param_names\" not in scheduler_cfg and \"module_cls_names\" not in scheduler_cfg:\n", "        return None\n", "    return unix_param_pattern_to_parameter_names(scheduler_cfg, model).union(\n", "        unix_module_cls_pattern_to_parameter_names(scheduler_cfg, model)\n", "    )\n", "\n", "\n", "def get_full_parameter_name(module_name, param_name):\n", "    if module_name == \"\":\n", "        return param_name\n", "    return f\"{module_name}.{param_name}\"\n", "\n", "\n", "def unix_module_cls_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig,\n", "    model: nn.<PERSON><PERSON>,\n", ") -> Union[None, Set[str]]:\n", "    if \"module_cls_names\" not in scheduler_cfg:\n", "        return set()\n", "    module_cls_to_params = {}\n", "    for module_name, module in model.named_modules():\n", "        module_cls = type(module)\n", "        module_cls_to_params.setdefault(module_cls, set())\n", "        module_cls_to_params[module_cls] |= set(\n", "            get_full_parameter_name(module_name, param_name)\n", "            for param_name, _ in module.named_parameters()\n", "        )\n", "    parameter_names = []\n", "    for module_cls_name in scheduler_cfg.module_cls_names:\n", "        module_cls = hydra.utils.get_class(module_cls_name)\n", "        matching_parameters = module_cls_to_params.get(module_cls, set())\n", "        assert len(matching_parameters) > 0, (\n", "            f\"Optimizer option for {scheduler_cfg.option} module_cls_name\"\n", "            f\" {module_cls_name} does not match any classes in the model\"\n", "        )\n", "        logging.info(\n", "            f\"Matches for module_cls_name [{module_cls_name}]: {matching_parameters} \"\n", "        )\n", "        parameter_names.append(matching_parameters)\n", "    return set.union(*parameter_names)\n", "\n", "\n", "def unix_param_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig,\n", "    model: nn.<PERSON><PERSON>,\n", ") -> Union[None, Set[str]]:\n", "    if \"param_names\" not in scheduler_cfg:\n", "        return set()\n", "    all_parameter_names = {name for name, _ in model.named_parameters()}\n", "    parameter_names = []\n", "    for param_name in scheduler_cfg.param_names:\n", "        matching_parameters = set(fnmatch.filter(all_parameter_names, param_name))\n", "        assert len(matching_parameters) >= 1, (\n", "            f\"Optimizer option for {scheduler_cfg.option} param_names {param_name} \"\n", "            \"does not match any parameters in the model\"\n", "        )\n", "        logging.info(f\"Matches for param_name [{param_name}]: {matching_parameters}\")\n", "        parameter_names.append(matching_parameters)\n", "    return set.union(*parameter_names)\n", "\n", "\n", "def set_default_parameters(\n", "    scheduler_cfgs: List[DictConfig], all_parameter_names: Set[str]\n", ") -> None:\n", "    constraints = [\n", "        scheduler_cfg.parameter_names\n", "        for scheduler_cfg in scheduler_cfgs\n", "        if scheduler_cfg.parameter_names is not None\n", "    ]\n", "    if len(constraints) == 0:\n", "        default_params = set(all_parameter_names)\n", "    else:\n", "\n", "        default_params = all_parameter_names - set.union(*constraints)\n", "    default_count = 0\n", "    for scheduler_cfg in scheduler_cfgs:\n", "        if scheduler_cfg.parameter_names is None:\n", "            scheduler_cfg.parameter_names = default_params\n", "            default_count += 1\n", "    assert default_count <= 1, \"Only one scheduler per option can be default\"\n", "    if default_count == 0:  # Add defaults without options\n", "        scheduler_cfgs.append({\"parameter_names\": default_params})\n", "\n", "\n", "def name_constraints_to_parameters(\n", "    param_constraints: List[Set[str]], model: torch.nn.Module\n", ") -> List[torch.nn.Parameter]:\n", "    matching_names = set.intersection(*param_constraints)\n", "    return [value for name, value in model.named_parameters() if name in matching_names]\n", "\n", "\n", "def map_scheduler_cfgs_to_param_groups(\n", "    scheduler_cfgs_per_param_group: Iterable[List[Dict]], model: torch.nn.Module\n", ") -> Tuple[List[Dict[Any, Any]], List[Dict[str, List[torch.nn.Parameter]]]]:\n", "    schedulers = []\n", "    param_groups = []\n", "    for scheduler_cfgs in scheduler_cfgs_per_param_group:\n", "        param_constraints = [\n", "            scheduler_cfg[\"parameter_names\"] for scheduler_cfg in scheduler_cfgs\n", "        ]\n", "        matching_parameters = name_constraints_to_parameters(param_constraints, model)\n", "        if len(matching_parameters) == 0:  # If no overlap of parameters, skip\n", "            continue\n", "        schedulers_for_group = {\n", "            scheduler_cfg[\"option\"]: scheduler_cfg[\"scheduler\"]\n", "            for scheduler_cfg in scheduler_cfgs\n", "            if \"option\" in scheduler_cfg\n", "        }\n", "        schedulers.append(schedulers_for_group)\n", "        param_groups.append({\"params\": matching_parameters})\n", "    return schedulers, param_groups\n", "\n", "\n", "def construct_optimizer(\n", "    model: <PERSON>.nn.<PERSON>,\n", "    optimizer_conf,\n", "    options_conf=None,\n", "    param_group_modifiers_conf=None,\n", ") -> OmniOptimizer:  # noqa\n", "    \"\"\"\n", "    Constructs a stochastic gradient descent or ADAM (or ADAMw) optimizer\n", "    with momentum. i.e, constructs a torch.optim.Optimizer with zero-weight decay\n", "    Batchnorm and/or no-update 1-D parameters support, based on the config.\n", "\n", "    Supports wrapping the optimizer with Layer-wise Adaptive Rate Scaling\n", "    (LARS): https://arxiv.org/abs/1708.03888\n", "\n", "    Args:\n", "        model (nn.<PERSON><PERSON><PERSON>): model to perform stochastic gradient descent\n", "            optimization or ADAM optimization.\n", "        cfg (OptimizerConf): Hydra/Omega conf object consisting hyper-parameters\n", "            of SGD or ADAM, includes base learning rate,  momentum, weight_decay,\n", "            dampening and etc. The supported config schema is `OptimizerConf`.\n", "    \"\"\"\n", "    if not options_conf:\n", "        optimizer = hydra.utils.instantiate(optimizer_conf, params=model.parameters())\n", "        return OmniOptimizer(optimizer)\n", "\n", "    scheduler_cfgs_per_option = hydra.utils.instantiate(options_conf)\n", "    all_parameter_names = {name for name, _ in model.named_parameters()}\n", "    flattened_scheduler_cfgs = []\n", "    for option, scheduler_cfgs in scheduler_cfgs_per_option.items():\n", "        for config in scheduler_cfgs:\n", "            config.option = option\n", "            config.parameter_names = unix_pattern_to_parameter_names(config, model)\n", "        set_default_parameters(scheduler_cfgs, all_parameter_names)\n", "        flattened_scheduler_cfgs.append(scheduler_cfgs)\n", "\n", "    if param_group_modifiers_conf:\n", "        for custom_param_modifier in param_group_modifiers_conf:\n", "            custom_param_modifier = hydra.utils.instantiate(custom_param_modifier)\n", "            flattened_scheduler_cfgs = custom_param_modifier(\n", "                scheduler_cfgs=flattened_scheduler_cfgs, model=model\n", "            )\n", "    schedulers, param_groups = map_scheduler_cfgs_to_param_groups(\n", "        itertools.product(*flattened_scheduler_cfgs), model\n", "    )\n", "    validate_param_group_params(param_groups, model)\n", "    optimizer = hydra.utils.instantiate(optimizer_conf, param_groups)\n", "    return OmniOptimizer(optimizer, schedulers)\n", "\n"]}], "source": ["print(patch.file_content)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def unix_param_pattern_to_parameter_names(\n", "    scheduler_cfg: DictConfig,\n", "    model: nn.<PERSON><PERSON>,\n", ") -> Union[None, Set[str]]:\n", "    if \"param_names\" not in scheduler_cfg:\n", "        return set()\n", "    all_parameter_names = {name for name, _ in model.named_parameters()}\n", "...\n", "\n", "        matching_parameters = set(fnmatch.filter(all_parameter_names, param_name))\n", "        assert len(matching_parameters) >= 1, (\n", "            f\"Optimizer option for {scheduler_cfg.option} param_names {param_name} \"\n", "            \"does not match any parameters in the model\"\n", "        )\n", "        logging.info(f\"Matches for param_name [{param_name}]: {matching_parameters}\")\n", "        parameter_names.append(matching_parameters)\n", "    return set.union(*parameter_names)\n", "\n", "\n", "\n"]}], "source": ["print(patch.file_content[bar[0].slice()] + '...\\n' + patch.file_content[3211: 3674])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Case SxS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Utilities.\n", "\n", "def print_prefix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\"\\n\".join(left_rst[\"patch_rst\"][\"prefix\"].splitlines()[-num_line:]))\n", "\n", "\n", "def generation_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"completion\"],\n", "                right_rst[\"completion\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def print_suffix_lines(pid: str, num_line=5):\n", "    left_rst = left_results[pid]\n", "    print(\"\\n\".join(left_rst[\"patch_rst\"][\"suffix\"].splitlines()[:num_line]))\n", "\n", "\n", "def prompt_sxs(pid: str):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_rst[\"prompt\"],\n", "                right_rst[\"prompt\"],\n", "                fromdesc=f\"{select_left_experiment.value} ({left_rst['result']})\",\n", "                todesc=f\"{select_right_experiment.value} ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def generation_vs_ground_truth(pid: str, results):\n", "    rst = results[pid]\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                rst[\"ground_truth\"],\n", "                rst[\"generation\"],\n", "                fromdesc=\"Ground Truth\",\n", "                todesc=f\"{select_right_experiment.value} ({rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def best_matched_sxs(pid: str, feature=\"ground_truth\"):\n", "    left_rst = left_results[pid]\n", "    right_rst = right_results[pid]\n", "    display_num_lines = 5\n", "\n", "    left_best_matched, left_score = vz_utils.get_best_match(\n", "        query=left_rst[feature],\n", "        corpus=left_rst[\"prompt\"],\n", "    )\n", "    splits = left_rst[\"prompt\"].split(left_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    lpre, lsuf = splits\n", "    lpre = '\\n'.join(lpre.splitlines()[-display_num_lines:])\n", "    lsuf = '\\n'.join(lsuf.splitlines()[:display_num_lines])\n", "\n", "    right_best_matched, right_score = vz_utils.get_best_match(\n", "        query=right_rst[feature],\n", "        corpus=right_rst[\"prompt\"],\n", "    )\n", "    splits = right_rst[\"prompt\"].split(right_best_matched, maxsplit=1)\n", "    if len(splits) == 1:\n", "        splits.append(\"\")\n", "    rpre, rsuf = splits\n", "    rpre = '\\n'.join(rpre.splitlines()[-display_num_lines:])\n", "    rsuf = '\\n'.join(rsuf.splitlines()[:display_num_lines])\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lpre,\n", "                rpre,\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                left_best_matched,\n", "                right_best_matched,\n", "                fromdesc=f\"match score: {left_score}\",\n", "                todesc=f\"match score: {right_score}\",\n", "            )\n", "        )\n", "    )\n", "\n", "    display(\n", "        HTML(\n", "            vz_utils.get_diff_html(\n", "                lsuf,\n", "                rsuf,\n", "            )\n", "        )\n", "    )\n", "\n", "    return left_best_matched, right_best_matched"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1: Select what you want to compare.\n", "select_left_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Left Patch ID:\",\n", "    disabled=False,\n", ")\n", "select_right_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Right Patch ID:\",\n", "    disabled=False,\n", ")\n", "clear_output(wait=True)\n", "display(select_left_experiment, select_right_experiment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: Load results for left and right.\n", "import json\n", "from research.eval.patch_lib import Patch\n", "\n", "left_id = select_left_experiment.value\n", "left_results = vz_utils.load(EXPERIMENTS[select_left_experiment.value])\n", "for pid in left_results:\n", "    # Normalize to patch object.\n", "    cache = left_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        left_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "right_id = select_right_experiment.value\n", "right_results = vz_utils.load(EXPERIMENTS[select_right_experiment.value])\n", "for pid in right_results:\n", "    # Normalize to patch object.\n", "    cache = right_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        right_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["want_pids = {\n", "    'EmptyCompletion': 'FAILED',\n", "    # 'StarCoderBase+NoRetrieval': 'FAILED',\n", "    'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    # 'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    'RagFIMAV17B+OracleTop1': 'PASSED',\n", "    'RagFIMAV17B+OracleTop2': 'PASSED',\n", "    'RagFIMAV17B+OracleTop10': 'PASSED',\n", "}\n", "\n", "\n", "pid_list = set(df['patch_id'])\n", "for exp, pf in want_pids.items():\n", "    _ids = set(df.loc[(df['experiment'] == exp) & (df['pass/fail'] == pf)]['patch_id'])\n", "    pid_list = pid_list & _ids\n", "pid_list = list(sorted(pid_list))\n", "print(len(pid_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gold_patch = {}\n", "for pid in pid_list:\n", "    chunk = results[pid]['patch_rst']['filtered_chunks'][0]\n", "    gold_patch[pid] = chunk['id']\n", "print(len(gold_patch))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = vz_utils.load(EXPERIMENTS['RagFIMAV17B+Top1'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["foo = results[pid_list[0]]['patch_rst']['filtered_chunks']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = 0\n", "for pid in pid_list:\n", "    for idx, c in enumerate(results[pid]['patch_rst']['filtered_chunks'][:20]):\n", "        if c['id'] == gold_patch[pid]:\n", "            break\n", "    score += 1.0 / (idx+1)\n", "score /= len(pid_list)\n", "score\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(foo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Option 1: Select one pid and display."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["select_patch_id = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description='Left Patch ID:',\n", "    disabled=False,\n", ")\n", "display(select_patch_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid = select_patch_id.value\n", "\n", "clear_output(wait=True)\n", "print(pid)\n", "print(f\"File Path: {right_results[pid]['patch_rst']['patch'].file_name}\")\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "print_prefix_lines(pid, 20)\n", "print('Generation Side by Side')\n", "generation_sxs(pid)\n", "generation_vs_ground_truth(pid, right_results)\n", "print('Prompt Side by Side')\n", "prompt_sxs(pid)\n", "print('Best matched in the prompt')\n", "# best_matched_sxs(pid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Option 2: loop over all pids and display."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["annotations = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.DataFrame([{'patch_id':key, 'annotation': value} for key, value in annotations.items()])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for pid in pid_list:\n", "    if pid in annotations:\n", "        continue\n", "    clear_output(wait=True)\n", "    print(f\"{len(annotations)} out of {len(pid_list)}\")\n", "    print(f\"PATCH ID: {pid}\")\n", "    print(f\"File Path: {right_results[pid]['patch_rst']['patch'].file_name}\")\n", "    print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "    print(\"Generation Side by Side\")\n", "    print_prefix_lines(pid, 10)\n", "    generation_sxs(pid)\n", "    # print(\"Ground True\")\n", "    generation_vs_ground_truth(pid, right_results)\n", "    print_suffix_lines(pid, 5)\n", "    print('---')\n", "    print(\"Prompt Side by Side\")\n", "    prompt_sxs(pid)\n", "    print('---')\n", "    # print(\"Best matched ground truth in the prompt\")\n", "    # left_best_matched, right_best_matched = best_matched_sxs(pid, feature ='ground_truth')\n", "    # print(\"Best matched generation in the prompt\")\n", "    # best_matched_sxs(pid, feature ='generation')\n", "    annotation = input(\"Continue\")\n", "    if annotation == \"c\":\n", "        break\n", "    annotations[pid] = annotation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Yaml config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "import json\n", "\n", "EXPERIMENTS.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["left_experiment = \"StarCoder+No\"\n", "right_experimet = \"DiffRe1000+PPL1000\"\n", "\n", "left_config = vz_utils.load_experiment_config(EXPERIMENTS[left_experiment])\n", "right_config = vz_utils.load_experiment_config(EXPERIMENTS[right_experimet])\n", "\n", "display(\n", "    HTML(\n", "        vz_utils.get_diff_html(\n", "            json.dumps(left_config, indent=2),\n", "            json.dumps(right_config, indent=2),\n", "            fromdesc=left_experiment,\n", "            todesc=right_experimet,\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Draft"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines_noindent/CarperAI/trlx_patches.jsonl', 'r') as f:\n", "    for l in f:\n", "        d = json.loads(l)\n", "        # char_start = d['char_start']\n", "        # char_end = d['char_end']\n", "        # while d['file_content'][char_start].isspace() and char_start <= char_end:\n", "        #     char_start += 1\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import shutil\n", "\n", "folder = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines\"\n", "new_folder = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_2-3lines_noindent\"\n", "for repo_name in os.listdir(folder):\n", "    new_repo_dir = os.path.join(new_folder, repo_name)\n", "    patchfile = glob.glob(os.path.join(folder, repo_name, \"*_patches.jsonl\"))\n", "    assert len(patchfile) == 1\n", "    patchfile = patchfile[0]\n", "    with open(patchfile, \"r\") as fread:\n", "        os.makedirs(new_repo_dir, exist_ok=True)\n", "        with open(\n", "            os.path.join(new_repo_dir, os.path.basename(patchfile)), \"w\"\n", "        ) as fwrite:\n", "            for line in fread:\n", "                data = json.loads(line)\n", "                char_start = data[\"char_start\"]\n", "                char_end = data[\"char_end\"]\n", "                while (\n", "                    data[\"file_content\"][char_start].isspace()\n", "                    and char_start <= char_end\n", "                ):\n", "                    char_start += 1\n", "                data[\"char_start\"] = char_start\n", "                fwrite.write(json.dumps(data) + \"\\n\")\n", "    # Copy db file\n", "    dbfile = glob.glob(os.path.join(folder, repo_name, \"*_db.jsonl\"))\n", "    assert len(dbfile) == 1\n", "    dbfile = dbfile[0]\n", "    shutil.copyfile(\n", "        dbfile, os.path.join(new_repo_dir, os.path.basename(dbfile))\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
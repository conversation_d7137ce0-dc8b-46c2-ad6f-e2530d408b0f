{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer, util\n", "\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# BGE Model"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2023-12-19 07:19:22.775489: W tensorflow/stream_executor/platform/default/dso_loader.cc:64] Could not load dynamic library 'libcudart.so.11.0'; dlerror: libcudart.so.11.0: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/lib/x86_64-linux-gnu:/usr/local/nvidia/lib:/usr/local/nvidia/lib64\n", "2023-12-19 07:19:22.775518: I tensorflow/stream_executor/cuda/cudart_stub.cc:29] Ignore above cudart dlerror if you do not have a GPU set up on your machine.\n"]}], "source": ["model = SentenceTransformer(\"BAAI/bge-large-en-v1.5\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.06784754, -0.02643291, -0.00285155, ...,  0.00966671,\n", "        -0.01829344,  0.01715526],\n", "       [-0.0359618 ,  0.00071898, -0.01443804, ..., -0.03110052,\n", "        -0.05301636, -0.01805334]], dtype=float32)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["foo = model.encode(['dfdfdfd','abd'])\n", "foo"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.06461835, -0.03527668, -0.00391069, ...,  0.01247919,\n", "        0.00356921,  0.0259515 ], dtype=float32)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["foo = model.encode('dfdf')\n", "foo"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Tensor"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["bar = util.dot_score(model.encode('dfdfdfd'), model.encode(['dfdfdfd','abd']))\n", "type(bar)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1.0000001192092896, 0.6148288249969482]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["bar.flatten().tolist()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['sf', '##ds', '##f']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["model.tokenizer.tokenize('sfdsf')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/notebooks/20231108_hf_embedding.ipynb Cell 9\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/notebooks/20231108_hf_embedding.ipynb#X11sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m model\u001b[39m.\u001b[39mtokenizer\u001b[39m.\u001b[39mtokenzier(\u001b[39m'\u001b[39m\u001b[39mdfdfd\u001b[39m\u001b[39m'\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["model.tokenizer.tokenzier('dfdfd')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Voyage AI"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import voyageai\n", "voyageai.api_key_path = None\n", "voyageai.api_key = 'pa-w8rk0D-C7rHzwTRaz4w1rM2GzUec6YFSPdauiPRz_zM'"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 1024)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "foo = np.array(voyageai.get_embeddings(\n", "    [\n", "        \"hello\",\n", "        \"world\",\n", "    ],\n", "    model=\"voyage-01\",\n", "    input_type=None,\n", "))\n", "foo.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
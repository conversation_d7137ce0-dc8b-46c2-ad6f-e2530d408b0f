{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Sample Random Completions from BigQuery"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "import logging\n", "from collections.abc import Iterable\n", "from datetime import datetime\n", "from dataclasses import dataclass\n", "\n", "from base.datasets import tenants, completion, completion_dataset\n", "from services.api_proxy.client.client import AugmentClient, UploadContent\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "def get_background_color(prob: float):\n", "    uncertain_color = (255, 0, 0)\n", "    certain_color = (0, 180, 180)\n", "    if prob >= 0.99:\n", "        r, g, b = certain_color\n", "        return f\"background-color: rgba({r},{g},{b},0.2);\"\n", "    elif prob >= 0.5:\n", "        return \"\"\n", "    else:\n", "        alpha = 0.5 - prob\n", "        r, g, b = uncertain_color\n", "        return f\"background-color: rgba({r},{g},{b},{alpha:.3f});\"\n", "\n", "def render_code(prefix, tokens, scores=None):\n", "    if scores is not None:\n", "        assert len(scores) == len(tokens)\n", "\n", "    spans = [\n", "        f'<pre><code>{last_n_lines(prefix, 3)}</code></pre>'\n", "    ]\n", "    for idx, token in enumerate(tokens):\n", "        if scores is not None:\n", "            score = scores[idx]\n", "            color = get_background_color(score)\n", "            spans.append(f'<span class=\"token\" style=\"{color}\">{token}</span>')\n", "        else:\n", "            spans.append(f'<span class=\"token\">{token}</span>')\n", "    return ''.join(spans)\n", "    \n", "\n", "def last_n_lines(text, n):\n", "    return \"\".join(text.splitlines(keepends=True)[-n:])\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Querying BigQuery: SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND resolution.accepted_idx >= 0\n", "        AND response.completion_length >= 1\n", "        AND rand() < 0.1\n", "        ORDER BY request.request_id\n", "        LIMIT 4000\n", "\n", "INFO:base.datasets.completion:Enqueuing batch 0\n", "INFO:base.datasets.completion:Processing batch 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 128 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 128 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 128 keys (total size 1493720 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1493720 bytes to insert 128 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 1\n", "INFO:base.datasets.completion:Processing batch 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 99 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:22\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 99 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:22\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 99 keys (total size 1473440 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:22\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1473440 bytes to insert 99 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:22\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 2\n", "INFO:base.datasets.completion:Processing batch 2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 100 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 100 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 100 keys (total size 1721232 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1721232 bytes to insert 100 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 3\n", "INFO:base.datasets.completion:Processing batch 3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 108 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 108 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 108 keys (total size 1475064 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1475064 bytes to insert 108 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 4\n", "INFO:base.datasets.completion:Processing batch 4\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 95 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:39\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 95 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:39\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 95 keys (total size 1303816 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:39\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1303816 bytes to insert 95 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:39\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 5\n", "INFO:base.datasets.completion:Processing batch 5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 90 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 90 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 90 keys (total size 1311024 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1311024 bytes to insert 90 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 6\n", "INFO:base.datasets.completion:Processing batch 6\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 89 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 89 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 89 keys (total size 1398936 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1398936 bytes to insert 89 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 7\n", "INFO:base.datasets.completion:Processing batch 7\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:26:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 79 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 79 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 79 keys (total size 1146384 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:26:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1146384 bytes to insert 79 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:26:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 8\n", "INFO:base.datasets.completion:Processing batch 8\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 80 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 80 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 80 keys (total size 870520 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 870520 bytes to insert 80 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 9\n", "INFO:base.datasets.completion:Processing batch 9\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 83 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 83 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 83 keys (total size 1624080 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1624080 bytes to insert 83 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 10\n", "INFO:base.datasets.completion:Processing batch 10\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 88 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 88 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 88 keys (total size 1269144 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1269144 bytes to insert 88 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 11\n", "INFO:base.datasets.completion:Processing batch 11\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 84 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 84 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 84 keys (total size 1559360 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1559360 bytes to insert 84 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 12\n", "INFO:base.datasets.completion:Processing batch 12\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 79 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 79 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 79 keys (total size 1210624 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1210624 bytes to insert 79 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 13\n", "INFO:base.datasets.completion:Processing batch 13\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:30\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 87 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:30\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 87 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:30\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 87 keys (total size 1016480 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:30\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1016480 bytes to insert 87 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:30\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 14\n", "INFO:base.datasets.completion:Processing batch 14\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:35\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 77 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 77 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 77 keys (total size 1114504 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1114504 bytes to insert 77 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 15\n", "INFO:base.datasets.completion:Processing batch 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:42\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 79 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:42\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 79 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:42\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 79 keys (total size 935392 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:42\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 935392 bytes to insert 79 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:42\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 16\n", "INFO:base.datasets.completion:Processing batch 16\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 74 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 74 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 74 keys (total size 1377840 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1377840 bytes to insert 74 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 17\n", "INFO:base.datasets.completion:Processing batch 17\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:53\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 69 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:53\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 69 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:53\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 69 keys (total size 1034448 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:53\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1034448 bytes to insert 69 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:53\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 18\n", "INFO:base.datasets.completion:Processing batch 18\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:27:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 69 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 69 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 69 keys (total size 1057504 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:27:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1057504 bytes to insert 69 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:27:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 19\n", "INFO:base.datasets.completion:Processing batch 19\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:28:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 64 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:06\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 64 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:06\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 64 keys (total size 849624 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:28:06\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 849624 bytes to insert 64 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:06\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 20\n", "INFO:base.datasets.completion:Processing batch 20\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:28:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 73 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 73 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 73 keys (total size 1476968 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:28:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1476968 bytes to insert 73 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 21\n", "INFO:base.datasets.completion:Processing batch 21\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:28:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 66 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 66 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 66 keys (total size 1106416 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:28:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1106416 bytes to insert 66 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 22\n", "INFO:base.datasets.completion:Processing batch 22\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-23 05:28:19\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 52 missing entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 52 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 52 keys (total size 797584 bytes).\u001b[0m\n", "\u001b[2m2024-02-23 05:28:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 797584 bytes to insert 52 entries.\u001b[0m\n", "\u001b[2m2024-02-23 05:28:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n", "1983\n"]}], "source": ["# Sample random\n", "\n", "from base.datasets import tenants, completion\n", "from google.cloud import bigquery, storage\n", "from typing import Literal, Optional, TypedDict, cast\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, PathAndContent\n", "\n", "query = \"\"\"SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND resolution.accepted_idx >= 0\n", "        AND response.completion_length >= 1\n", "        AND rand() < 0.1\n", "        ORDER BY request.request_id\n", "        LIMIT 4000\n", "\"\"\"\n", "\n", "\n", "TENANT_NAME = \"dogfood\"\n", "\n", "dataset = completion_dataset.CompletionDataset.create_from_query(\n", "    query, tenants.get_tenant(TENANT_NAME)\n", ")\n", "\n", "# tenant = tenants.get_tenant(TENANT_NAME)\n", "# bucket = storage.Client(tenant.project_id).bucket(tenant.blob_bucket_name)\n", "# blob_cache = GCSBlobCache(\n", "#     bucket,\n", "#     tenant.blob_bucket_prefix,\n", "#     2**30,\n", "#     num_threads=10,\n", "# )\n", "# bigquery_client = bigquery.Client(tenant.project_id)\n", "# rows = bigquery_client.query_and_wait(query)\n", "# dataset = completion_dataset.CompletionDataset(\n", "#     rows,\n", "#     cast(Optional[int], rows.total_rows),\n", "#     blob_cache=blob_cache,\n", "# )\n", "completions = list(\n", "    c for c in dataset.get_completions() if \"roguesl-farpref-16B\" in c.response.model\n", ")\n", "print(len(completions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sample A Set of Request IDs"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Querying BigQuery: \n", "        SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND resolution.event_type = 'completion_resolution'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND response.completion_length >= 1\n", "AND request.request_id IN (\"38a87727-f608-486e-baa0-d580f06a4701\")\n", "        ORDER BY request.request_id\n", "        LIMIT 100\n", "        \n", "INFO:base.datasets.completion:Enqueuing batch 0\n", "INFO:base.datasets.completion:Processing batch 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-22 19:25:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-02-22 19:25:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-22 19:25:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 72 bytes).\u001b[0m\n", "\u001b[2m2024-02-22 19:25:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 72 bytes to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-02-22 19:25:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n", "1\n"]}], "source": ["TENANT_NAME = \"dogfood\"\n", "DATASET_FILTERS = completion_dataset.CompletionDataset.Filters(\n", "    request_ids=[\n", "        \"38a87727-f608-486e-baa0-d580f06a4701\",\n", "    ]\n", ")\n", "dataset = completion_dataset.CompletionDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=DATASET_FILTERS,\n", "    order_by=\"request_id\",\n", "    limit=100,\n", ")\n", "completions = list(\n", "    c for c in dataset.get_completions() if \"roguesl-farpref-16B\" in c.response.model\n", ")\n", "print(len(completions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Filtering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Min token ppl"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1854\n"]}, {"data": {"text/html": ["<pre><code>            },\n", "            ...breadcrumbs?.map(() => {\n", "              </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> key</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.412);\">requests</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> title</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Requests</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> href</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> <</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Link</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">={\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">/</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">requests</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"}</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Requests</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"></</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Link</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> };</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.08806434839167583\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/6c07e94c-17e6-44e9-bfa7-369937af6f70\n"]}, {"data": {"text/html": ["<pre><code></code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">import</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.444);\"> client</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> }</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> from</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> './</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">lib</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">/</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">client</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">'</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.0556500738064504\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/a884ab40-fa38-448d-b5ed-db6d042725df\n"]}, {"data": {"text/html": ["<pre><code>\n", "  // the namespace of the source of the request.\n", "  //</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> //</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> used</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> determine</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.416);\"> source</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> embeddings</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> string</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> source</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">namespace</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">3</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.08436047860698273\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/ba360a7b-4e5d-4cca-9c25-cabadc09e44c\n"]}, {"data": {"text/html": ["<pre><code># 1. sort the list\n", "# 2. find the index of the first element >= 3.5\n", "# 3. return the element at that index</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">#</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.413);\"> sort</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> list</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.08720853058869749\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0f723c56-6db8-46d4-af4c-d9dee97bbd6f\n"]}, {"data": {"text/html": ["<pre><code>- `ResearchTokenizerWrapper`: wraps the research tokenizer into the production tokenizer and production tokenizer into research tokenizer.\n", "\n", "## </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.439);\">AU</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">8</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Research</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> vs</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Production</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Document</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Prompt</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Formatter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Input</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.06100039130459345\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/f917514c-0462-4080-9caf-019ee87afd6e\n"]}, {"data": {"text/html": ["<pre><code>#\n", "# Answer\n", "# ------\n", "</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">#</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.413);\"> It</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> called</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> from</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> following</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> places</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">#</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.0865298684194228\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/8c5526eb-8da7-4239-9763-3f244ca30ef8\n"]}, {"data": {"text/html": ["<pre><code>                request_id: request_id.to_string(),\n", "                client_name: \"some client\".to_string(),\n", "                user_id: \"some user\".to_string(),</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> user</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">agent</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">some</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> agent</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\".</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">string</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.417);\"> feedback</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Some</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">feedback</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">clone</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">()),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> }],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> };</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.08256737725124999\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/3442f863-1196-442f-aa50-57d633eca644\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"Special tokens used for RAG finetuned models within Augment.\n", "    \n", "    Convenience class </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">for</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.415);\"> R</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">AG</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> fin</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">et</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">uned</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> models</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.0848562292624658\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/deb3df5b-af1b-4530-b3f3-6e866e2b4ff4\n"]}, {"data": {"text/html": ["<pre><code>            if is_ignored(path, ignore_patterns):\n", "                continue\n", "            if path.is_file():</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> good</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">files</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">append</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> elif</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">dir</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">():</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> good</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">folders</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">]</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> process</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">folder</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> else</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> print</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">f</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.412);\">Unknown</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> type</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}\")</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> good</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">folders</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> process</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">folder</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">root</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> good</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">files</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> +</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> list</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">good</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">folders</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">keys</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">())</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.08840902250593455\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/91b258be-ea0c-4fc1-a426-3fa3fe3a1f8c\n"]}], "source": ["import numpy as np\n", "from IPython.display import HTML, display\n", "import random\n", "\n", "model = f\"min_token\"\n", "low_conf = []\n", "seen = set()\n", "for c in completions:\n", "    if c.request_id in seen:\n", "        continue\n", "    min_prob = np.min(np.exp(c.response.token_log_probs))\n", "    low_conf.append((c, min_prob))\n", "\n", "print(len(low_conf))\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "\n", "seen = set()\n", "count = 0\n", "random.shuffle(low_conf)\n", "for c, s in low_conf:\n", "    if c.request_id in seen:\n", "        continue\n", "    if not 0.05 < s < 0.10:\n", "        continue\n", "    count += 1\n", "    if count == 10:\n", "        break\n", "    \n", "    seen.add(c.request_id)\n", "    probs = np.exp(c.response.token_log_probs)\n", "    scores = np.ones(len(c.response.token_log_probs))\n", "    idx = np.argmin(probs)\n", "    scores[idx] = probs[idx]\n", "    display(HTML(render_code(c.request.prefix, c.response.tokens, scores)))\n", "    print(s)\n", "    print(f\"{SUPPORT_DOGFOOD}{c.request_id}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Consecutive low prob tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from itertools import accumulate\n", "\n", "import numpy as np\n", "\n", "\n", "THRE = 0.2\n", "\n", "low_conf = []\n", "for c in completions:\n", "    cache = (np.exp(c.response.token_log_probs) < THRE).astype(np.int32)\n", "    cache = list(accumulate(cache, lambda x, y: 0 if y == 0 else x + y))\n", "    low_conf.append((c, max(cache)))\n", "print(len(low_conf))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, display\n", "import numpy as np\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "\n", "for c, score in sorted(low_conf, key=lambda x: x[1], reverse=True)[:10]:\n", "    print(f\"{SUPPORT_DOGFOOD}{c.request_id}\", score)\n", "    probs = np.exp(c.response.token_log_probs)\n", "    display(HTML(render_code(c.response.tokens, probs)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Moving Average"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["856\n"]}, {"data": {"text/html": ["<pre><code>        result.unknown_checkpoint_id = retrieval_result.get_unknown_checkpoint_id()\n", "\n", "        # TODO(rich): m</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ove</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.435);\"> completion</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.389);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.386);\">host</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.09343935074085248\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0134c82c-ffb9-4393-9c10-a21f105f9e79\n"]}, {"data": {"text/html": ["<pre><code>            report_fn(step, group_metrics)\n", "            # TODO(carl): our old determined version is missing this generic `report_metrics`,\n", "            # hence the </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.406);\">above</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.406);\"> code</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.331);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> working</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.11392664292414115\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006d46ce-3341-4f2a-9b1c-e923549d900e\n"]}, {"data": {"text/html": ["<pre><code>\n", "    def get_checkpoint(ids: workspace_manager.Blobs):\n", "        # This mock is complicated by the </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">fact</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.249);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.426);\"> checkpoint</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.405);\"> id</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.12107705413868601\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006332d4-9db0-4e3e-a4d8-3fd738280574\n"]}, {"data": {"text/html": ["<pre><code>A series of Python language sets to test pretraining on.\n", "\n", "- </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.376);\">[</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.235);\"> ]</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.444);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.12298374030379641\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0014c5d4-2337-4854-b559-ec8c2a8e2223\n"]}, {"data": {"text/html": ["<pre><code>        file_after: str,\n", "    ) -> str:\n", "        \"\"\"Simulate a WI</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">P</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> file</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> by</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.430);\"> removing</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.289);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.355);\"> TODO</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">like</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> markers</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"><|skip|></span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1286343687209221\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00f42998-1ded-47e4-811d-977ee096540f\n"]}, {"data": {"text/html": ["<pre><code>    Doing this in one place makes it possible for torch.compile to fuse casting with the loss.\n", "\n", "    Di</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">st</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ributed</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.456);\">-</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.209);\">friendly</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.309);\"> version</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> torch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">nn</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">functional</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">cross</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">entropy</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1351591800952618\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/002a1110-5a17-4f1c-86ec-c4fc93171e05\n"]}, {"data": {"text/html": ["<pre><code>\n", "\n", "# NOTE</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> This</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> hack</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> get</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> fast</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">backward</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> work</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> with</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.302);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.414);\"> latest</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.327);\"> determined</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0107acd2-b662-4164-a337-6dad8a11af52\n"]}, {"data": {"text/html": ["<pre><code>\n", "// The service accounts generated by this function are for use by CoreWeave, to pull in data used\n", "// for research and to export data from research models. Their c</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">re</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ation</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.407);\"> not</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.381);\"> automated</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.212);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/********-7594-4e49-8a37-************\n"]}, {"data": {"text/html": ["<pre><code>message RequestMetadata {\n", "    // What type of request this is. Probably not very useful currently, but eventually we can use\n", "    // this to </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.390);\">filter</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.221);\"> out</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.388);\"> requests</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> that</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> are</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> interesting</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> us</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.15083005153891785\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/002a819e-9fe5-400d-bcc8-cc01edc2ba8c\n"]}, {"data": {"text/html": ["<pre><code>from google.cloud.bigtable.table import Table\n", "\n", "# expected to be used only in dev, </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">so</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> no</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> need</span><span class=\"token\" style=\"\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.413);\"> use</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.442);\"> dat</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">aclass</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.15094056323372346\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014debf7-6d84-4785-b0ee-0144a76b8fc1\n"]}, {"data": {"text/html": ["<pre><code>\n", "Her score is low because she is not motivated. \n", "\n", "</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.435);\">She</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.233);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.290);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">She</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mot</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iv</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> she</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reading</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.15410560150675437\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00cec2a2-222b-436a-a211-54ecf6508724\n"]}, {"data": {"text/html": ["<pre><code>        \n", "\n", "        # </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.365);\">Only</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.348);\"> allow</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.305);\"> Deep</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Seek</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Coder</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Base</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Tokenizer</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> and</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> T</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ik</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">token</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Star</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Coder</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Tokenizer</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.15863555202476515\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0125056d-3f6a-4623-b31d-98bc72f3f986\n"]}, {"data": {"text/html": ["<pre><code>from</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> augment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">experimental</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ar</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">un</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.300);\">augment</span><span class=\"token\" style=\"\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.459);\">pb</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> import</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1662486311218953\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b5f12a-cc72-46bf-bf31-40b32ffb58f9\n"]}, {"data": {"text/html": ["<pre><code>            )\n", "\n", "        #### TODO: add </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.427);\">this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.192);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.284);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> prompt</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> formatter</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.16974847438608623\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00eb8934-aed8-4ff2-8d53-502756b3ef8b\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"The outputs after a model's forward pass on a batch of ModelInput.\n", "\n", "    </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">The</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> outputs</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> are</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> list</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.399);\"> tensors</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.172);\">,</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.349);\"> one</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> each</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> input</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.17102414718626174\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00f7714b-7382-4e64-9a7e-af99fec8d73d\n"]}, {"data": {"text/html": ["<pre><code>        self.url = url\n", "\n", "    def generate(self. magic</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.296);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.423);\">token</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.172);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"><|skip|></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> -></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Completion</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Result</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Completion</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Result</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> generated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=\"\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> prompt</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">tokens</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=[],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> retrieved</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">chunks</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=[],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> )</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1724216238937528\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003d2cad-7079-4040-a95f-531c6cf4b958\n"]}, {"data": {"text/html": ["<pre><code>    // had trouble starting up (perhaps too much data for badger DB).\n", "    // Switch prod to a new PVC - jaeger-badger-claim2.\n", "    // Note: the PersistentVolumeCLaim obje</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ct</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.252);\"> not</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.319);\"> used</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.372);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> but</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> th</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.17905682179249266\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/007da157-e8b1-4e15-ba17-af132fe73b0b\n"]}, {"data": {"text/html": ["<pre><code>local PREFIX_FRACTION = 0.75;\n", "\n", "// </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">model</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> model</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.179);\"> configuration</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.403);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.305);\"> convert</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> model</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> instance</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> configuration</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.18258840115769476\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00185a46-07cf-4891-8811-a549183edae1\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"Create a mixture of datasets with a given sampling ratio in each set.\n", "\n", "    While you can supply a seed to the sampling, shuffling is ver</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">y</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> important</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.322);\"> for</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.294);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.334);\"> final</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> dataset</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.18294536761528557\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00f87552-99f1-41a0-a1e4-e3268ab10a05\n"]}, {"data": {"text/html": ["<pre><code>\n", "    def get_checkpoint(ids: workspace_manager.Blobs):\n", "        # This mock is compl</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">icated</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> because</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> we</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> need</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.364);\"> return</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.270);\"> a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.301);\"> different</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> set</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.18390069374049395\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000420bf-60de-424b-ab44-978b9f0b503e\n"]}, {"data": {"text/html": ["<pre><code>\"\"\"Job to generate the github_snapshot dataset.\n", "\n", "1. Standardize the language column and keep </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">only</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.250);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.421);\"> ones</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.182);\"> that</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> are</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> covered</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> by</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> common</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> rules</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.18480084218625653\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/008efe2e-1da4-407f-a951-28b7eca40984\n"]}, {"data": {"text/html": ["<pre><code>            .max_by_key(|m| match m.inference_config {\n", "                Some(config) => config.model_priority,\n", "                None => -1, // TODO: default_ma</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">del</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.365);\"> should</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.056);\"> be</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.383);\"> optional</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.19135100148586745\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006a1776-d1f9-4302-907b-33cc48b896db\n"]}, {"data": {"text/html": ["<pre><code>\n", "# %%\n", "def main():</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.351);\"> print</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.099);\">(\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.381);\">Number</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> repos</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> grouped</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">df</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">count</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">())</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1918811889110201\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0126b50c-8a7b-46bb-a88c-ccbd20b496ec\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"Create a mixture of datasets with a given sampling ratio in each set.\n", "\n", "    While you can supply a seed </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.195);\">to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.303);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.370);\"> sampling</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> it</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> used</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sh</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">uff</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ling</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.19852068471576448\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/011f97d6-1db7-48a5-83af-a763f8ba4f9d\n"]}, {"data": {"text/html": ["<pre><code>export const SESSION_SCOPES = [\"email\"];\n", "\n", "// The AuthSessionStore is res</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">p</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ons</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ible</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> managing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.330);\"> session</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.310);\"> state</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.249);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> extension</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.20118797176977135\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00291402-4e8b-4552-b7ab-6eeb6a681fe9\n"]}, {"data": {"text/html": ["<pre><code>                  REVIEW URL: https://github.com/celo-org/celo-blockchain/pull/942#discussion_r399574609\n", "                      COMMIT: https://github.com/celo-org/celo-blockchain/commit/089beac2328d3ff23d064c7e330665653a6c0a87\n", "MANUALLY REVISED INSTRUCTION: Simplify </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> code</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> by</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> using</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.262);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.274);\"> `</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.343);\">Contains</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">By</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Address</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">`</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> method</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00a465c3-74c6-4b3a-8d3a-0be60caf2218\n"]}, {"data": {"text/html": ["<pre><code>        '%s-test-runner-iam' % namespace,\n", "      ],\n", "      </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.413);\">iam</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.076);\">ServiceAccount</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.270);\">Emails</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> '%</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">test</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">runner</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">bes</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iam</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">@</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">system</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">services</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">dev</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iam</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">g</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">service</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">account</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">com</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">'</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> %</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> namespace</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> '%</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">test</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">runner</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iam</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">@</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">system</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">services</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">dev</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">iam</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">g</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">service</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">account</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">com</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">'</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> %</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> namespace</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "     </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ]</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/010694f8-7859-4a3a-83e1-1e7c85dca7f0\n"]}, {"data": {"text/html": ["<pre><code>        expect(extension.modelInfo!.suggestedSuffixCharCount).toBe(params.suggestedSuffixCharCount);\n", "\n", "        // The second request is </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.384);\">a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.338);\"> sentinel</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.045);\"> request</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2045891817298287\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/004e3ecf-b4b0-4df7-a05e-bd07072843fd\n"]}, {"data": {"text/html": ["<pre><code>\n", "// returns detailed information about the given request\n", "// Use desiredEvents wh</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">en</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> you</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> want</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.393);\"> get</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.200);\"> only</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.226);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> subset</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> events</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.206294499717495\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0129f8f0-0709-4d5e-8f99-ab6d5fcecb13\n"]}, {"data": {"text/html": ["<pre><code>;\n", "\n", "local </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.127);\">get</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.178);\">Job</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.427);\">Container</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> function</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">env</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> namespace</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> cloud</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.20659691036416317\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00d9ef84-860a-4956-b5ef-7b0bc33f279a\n"]}, {"data": {"text/html": ["<pre><code>VENDOR_NAME = \"pareto\"\n", "\n", "# This is ba</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ck</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ed</span><span class=\"token\" style=\"\"> by</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.330);\"> a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.397);\"> G</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">CS</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> bucket</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.20920238945240033\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014f9f4f-6cc1-47c8-8a3e-14de77abfe05\n"]}, {"data": {"text/html": ["<pre><code>        # And create an according ModelInput\n", "            \n", "        # Once again: so we use t</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">he</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.229);\"> same</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.396);\"> code</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.173);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> both</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> edit</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> and</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> PR</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2097478973300265\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00056ccf-13b2-4071-b4a7-37cbf8cfedd0\n"]}, {"data": {"text/html": ["<pre><code>    to_check = [local_blob_name, bogus_blob_name]\n", "\n", "    # We have not uploaded anythi</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ng</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> yet</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> so</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> we</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> should</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> get</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.298);\"> back</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.182);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.347);\"> two</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> names</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.21423320717271865\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000a858d-1c13-47d1-b140-872134e5ebc0\n"]}, {"data": {"text/html": ["<pre><code>fwd_utils.generate(step_fn_, attn, prefix, 2)\n", "\n", "tknzr.detokenize([0,5</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.253);\">1</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.287);\">,</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.312);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.21507169223819197\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0124a81e-f00a-46f5-89f1-2260a6664f35\n"]}, {"data": {"text/html": ["<pre><code>def join_samples_with_repos(spark: SparkSession, synthetic_samples_path: Path, raw_repos_uri: str) -> pyspark.sql.DataFrame:\n", "    with synthetic_samples_path.open() as f:\n", "        synthetic_samples = json.load(f)[:300]  </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">#</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.421);\"> TODO</span><span class=\"token\" style=\"\">:</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.291);\"> remove</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> this</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.21686438539190891\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/010f0f24-4bd8-4189-b964-2356a155a9f3\n"]}, {"data": {"text/html": ["<pre><code>        self.w3 = column_parallel(dim, hidden_dim, bias=False)\n", "\n", "    # This i</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> workaround</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> torch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">compile</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.293);\"> not</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.066);\"> supporting</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.368);\"> torch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">nn</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">functional</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">sil</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">u</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.22797583898656873\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/008a3e36-de8c-4b25-a440-a90007cf9318\n"]}, {"data": {"text/html": ["<pre><code>                extensions: vec![\".c\".to_string()],\n", "            }],\n", "            // TODO (D</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">mit</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ry</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">):</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.378);\"> add</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.367);\"> feature</span><span class=\"token\" style=\"\"> flags</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.22914766180016105\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00a9c093-75e6-45f4-9fad-30a57c242a20\n"]}, {"data": {"text/html": ["<pre><code>\n", "from services.embedder_host.server.embedder_model import EmbedderModel\n", "from services.shared</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">metrics</span><span class=\"token\" style=\"\"> import</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.360);\"> Metrics</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.401);\"><|skip|></span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2308415434287631\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/004bfecb-cd39-447c-aca0-3ec84241bed0\n"]}, {"data": {"text/html": ["<pre><code># Ground Truth\n", "print(sample[\"patch\"][\"patch_content\"][14</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.230);\">:</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.298);\">1</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.262);\">8</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2348300473675046\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b78ba6-a163-43af-8ca5-73b12b553f92\n"]}, {"data": {"text/html": ["<pre><code>    parser = argparse.ArgumentParser()\n", "    # parser.add_argument('character', type=str, help='The character to be queried.')\n", "    # Make the line above </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">comment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ed</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> out</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> and</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> uncomment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> line</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> below</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.303);\"> make</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.118);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.325);\"> script</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> accept</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> named</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> cmd</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> line</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> argument</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2360947349045196\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0070a5c9-e2ee-41c2-a8fd-861783cd4183\n"]}, {"data": {"text/html": ["<pre><code>#!/bin/bash\n", "# </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.440);\">Print</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.024);\">s</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.012);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> size</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> all</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> files</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> in</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> current</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> git</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> repository</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24016423151966368\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/010de004-4f7b-4c9c-b941-cf2119d972c0\n"]}, {"data": {"text/html": ["<pre><code>\n", "        updated_code = postprocess_fn(full_response, self.post_process_strategy)\n", "        assert inner_extra_outputs.prompt_tokens is not None  # this is to let pylint</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> know</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.218);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.330);\"> it</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.205);\">'s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> None</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24181160344669234\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0101eb5e-faf7-4e75-b4e3-be31d38edc0b\n"]}, {"data": {"text/html": ["<pre><code>                // Construct tracked event\n", "\n", "                const content_changes = ChangeInstructionCommand</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.428);\">get</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.101);\">Custom</span><span class=\"token\" style=\"\">Content</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Changes</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">event</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">);</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00c8f1bf-0d1c-4437-a63b-b1b14d4c99d8\n"]}, {"data": {"text/html": ["<pre><code>// - iamServiceAccountNames: the names of the service accounts to grant access to the table\n", "// - nameSuffix: a suffix to add to the name of the policy. The name suffix is important to ensure that the policy is unique. No two policies can have the same name.\n", "//  </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.333);\">            </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.241);\"> The</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.152);\"> name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> suffix</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> added</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> policy</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24698257287629313\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003d5f68-1ced-4924-a034-ff1ce33bb8b2\n"]}, {"data": {"text/html": ["<pre><code>            }\n", "\n", "            // The surviving entries in </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">blob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Map</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> are</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ones</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.302);\"> we</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.264);\"> should</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.174);\"> notify</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> client</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24802996111499073\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00cb1745-dc91-430b-996e-c06b5d06b2ba\n"]}, {"data": {"text/html": ["<pre><code>    # - If I try to report train and validation metrics with the same step, the job crashes\n", "    # - The reporting is generally slow (tens of ms) and occurs at a CPU/GPU sync point\n", "    # Putting it together, the current solution is</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> report</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> train</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> metrics</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.318);\"> with</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.266);\"> a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.140);\"> step</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24835312714241642\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00dcc897-47d8-40c1-bde4-b044525c7c55\n"]}, {"data": {"text/html": ["<pre><code>import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">/</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">experimental</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">/</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">y</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">uri</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">/</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">notebooks</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"><|skip|></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"><|skip|></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">from</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> augment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">experimental</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">y</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">uri</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">notebooks</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.384);\">utils</span><span class=\"token\" style=\"\"> import</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.349);\"> *</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.25421679024410154\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00a32b65-7fb2-425a-bd19-90dfc4a9993d\n"]}, {"data": {"text/html": ["<pre><code>new_selected_range = expand_range(commented_range_in_file, 60, len(whole_file_text.splitlines(True)))\n", "\n", "# If new selected range is not covering the original selected range, then we can</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">'t</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.263);\"> use</span><span class=\"token\" style=\"\"> this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.378);\"> sample</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2545739125659662\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00f3b490-a59c-4246-b59e-20e67d59392c\n"]}, {"data": {"text/html": ["<pre><code>         * pending completion, and has merely paused their typing. Second, the new completion\n", "         * request may not be successful -- for example, it could get cancelled locally by\n", "         * vscode. If that happens, vscode contine</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> call</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> method</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.285);\"> and</span><span class=\"token\" style=\"\"> we</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.351);\"> want</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2554455269705268\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0061908d-9e45-411a-8ba4-72fa0b34c736\n"]}, {"data": {"text/html": ["<pre><code>    time: string;\n", "\n", "    // File path of the file th e</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.248);\">vent</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.202);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.268);\"> about</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.259172758951013\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014f5bc2-22a4-47fc-bb2f-2a5b39d1c43c\n"]}, {"data": {"text/html": ["<pre><code>};\n", "\n", "export</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> type</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.332);\"> Completion</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.237);\">Feedback</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.097);\">Result</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">id</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> string</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> rating</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> number</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> note</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> string</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">};</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.26099299300342393\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/015f78d8-c5b1-463e-81c8-c6835ae98b8b\n"]}, {"data": {"text/html": ["<pre><code>\n", "Note that this job only applies filtering logic on file path and language,\n", "which won't be modif</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ied</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.260);\"> by</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.150);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.279);\"> Star</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Coder</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> filter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2648011686318718\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00fc17a3-1244-4da0-9a43-244cd4f0f01a\n"]}, {"data": {"text/html": ["<pre><code>    input_df = read_dataset(spark, get_single_source(github_snapshot))\n", "\n", "    EXCLUDED_EXTENSIONS</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.059);\">append</span><span class=\"token\" style=\"\">(\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.430);\">patch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\")</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.26705183522634335\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003fabd2-0b54-44c0-b89d-75a503f86182\n"]}, {"data": {"text/html": ["<pre><code>        },\n", "        \"post_process_config\": {\n", "            </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.361);\">max</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.360);\">tokens</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">keep</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\":</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">4</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">max</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">tokens</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">keep</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">non</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">completion</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\":</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">0</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">4</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.26832452532632334\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0014365a-c5fe-4093-b609-a8689ec5ff73\n"]}, {"data": {"text/html": ["<pre><code>        raise ValueError(f\"Total ratio of {total_ratio} is too high.  Maximum is 1.\")\n", "\n", "    # The remainder of the ratio l</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">e</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">aves</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> us</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> with</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> number</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> rows</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.179);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.305);\"> we</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.180);\"> need</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sample</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.27143394700240336\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00272e5c-d523-492d-be64-44f2b55845dc\n"]}, {"data": {"text/html": ["<pre><code>\n", "        // TODO(rich): the missing blob names may exceed the gRPC message size.\n", "        // We should consider either streaming the respons</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">e</span><span class=\"token\" style=\"\"> or</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.382);\"> returning</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.231);\"> a</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2728735590383486\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0166742d-7e3c-4e91-a774-5f914f6df6ff\n"]}, {"data": {"text/html": ["<pre><code>        )\n", "\n", "    # Ensure we </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">get</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.164);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.225);\"> expected</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.278);\"> missing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> blob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> names</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.27347379392584426\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0090b2d5-90a5-4bc1-a0ae-e89ddaee95a5\n"]}, {"data": {"text/html": ["<pre><code>        it(\"should set the webview's html\", async () => {\n", "            const provider = new FeedbackWebviewProvider(\n", "                vscode.Uri.</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">parse</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">vscode</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">://</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.289);\">example</span><span class=\"token\" style=\"\">/</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.346);\">path</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> new</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Aug</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Extension</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> new</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Aug</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Listener</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">()</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2748347457583022\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00958bef-93f3-4186-b49a-1a593cfada06\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"Special tokens for the StarCoder model.\"\"\"\n", "\n", "    def __init__(self, tokenizer: Tokenizer, include_ext</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">r</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">act</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.144);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.379);\">tokens</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.007);\">=</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">False</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2771932494536211\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00c21ad4-8b80-4a7d-bbf7-aa0a99e34e58\n"]}, {"data": {"text/html": ["<pre><code>    // uploadedBlobName: blob name corresponding to the most recently uploaded in-memory\n", "    //     state of this document\n", "    // uploadedSeq: sequence number of upload. All changes in any</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.351);\"> upload</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.256);\"> with</span><span class=\"token\" style=\"\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sequence</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> number</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.27836875323894084\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000fdeeb-09cf-4c93-a235-fcd255dfe0dc\n"]}, {"data": {"text/html": ["<pre><code>            }\n", "\n", "            // </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Fire</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> event</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> each</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> blob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.162);\"> was</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.317);\"> found</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.146);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.27950374617860024\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/012d55be-ab69-4b57-9e86-171dab72febc\n"]}, {"data": {"text/html": ["<pre><code>                        const blobName = this._blobNameCalculator.calculate(pathName, text);\n", "                        if (blobName === undefined) {\n", "                            // todo(mlm): should Bl</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Manager</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> be</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> responsible</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.363);\"> reporting</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.143);\"> this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.044);\">?</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2816959212294454\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0073d338-9c65-430f-ae32-b3a08d64eb1d\n"]}, {"data": {"text/html": ["<pre><code>            this._pendingInlineCompletion.start + this._pendingInlineCompletion.consumed\n", "        ) {\n", "            const startBefore = event.document.positionAt(time</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.293);\">stamp</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.363);\"><|skip|></span><span class=\"token\" style=\"\">;</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2822466462178974\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000a5f36-f8ec-46e2-b500-a539baa06215\n"]}, {"data": {"text/html": ["<pre><code>    )\n", "    parser.add_argument(\n", "        \"--</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">verbose</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> action</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">store</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">true</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> default</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">False</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> help</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Print</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.295);\"> verbose</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.055);\"> output</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.244);\">.\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> )</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.28575966126810837\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0035fcd1-a53d-4cc6-8d0b-b28eea6d349c\n"]}, {"data": {"text/html": ["<pre><code>        # with `torch.compile`. For unknown reasons, this causes the \"CUDA already initialized\"\n", "        # issue when importing at the top level in a pytest run (that executes imports during\n", "        # test collection). So we </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">import</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.320);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.119);\"> model</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.157);\"> code</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> here</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.28678472336148925\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0111fc33-474d-4a0c-af96-ac299b83d2f6\n"]}, {"data": {"text/html": ["<pre><code>        \"\"\"Run a forward pass on the model in inference mode.\n", "\n", "        Note: </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> function</span><span class=\"token\" style=\"\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.370);\"> optional</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.155);\"> and</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> mandatory</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> implement</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2909218743635861\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00d8666a-de0d-40c3-b856-9855916e7995\n"]}, {"data": {"text/html": ["<pre><code>            }\n", "        }\n", "        // Since we stepped over each line in rever</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">se</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> order</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> we</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> need</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> reverse</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.044);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.320);\"> array</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.197);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2919179375255229\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/01595fd3-6ff7-432b-b9d7-c5519a3c2a63\n"]}, {"data": {"text/html": ["<pre><code>            modified_file[\"content\"],\n", "        ):\n", "            </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">print</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">sample</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">prefix</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"]</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> +</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sample</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">new</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">middle</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"]</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> +</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sample</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">suffix</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"])</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> print</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">modified</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">file</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">content</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"])</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> raise</span><span class=\"token\" style=\"\"> ValueError</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.068);\">(\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.385);\">Strings</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> are</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> same</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\")</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.29320360290370173\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/007cb49c-badd-4d77-af17-d539af9717b8\n"]}, {"data": {"text/html": ["<pre><code>\n", "changes = [\n", "    \"L</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.192);\">et</span><span class=\"token\" style=\"\">'s</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.349);\"> change</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> requirements</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2941117232083187\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/013abb9f-e1d9-4e8e-a057-4a369dc9c8a6\n"]}, {"data": {"text/html": ["<pre><code>                ];\n", "            } else {\n", "                // TODO(guy) make </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.206);\"> a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.150);\"> debug</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.235);\"> log</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3007435424537527\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b4ca6e-62ad-44b2-9c24-8abbe2e618b7\n"]}, {"data": {"text/html": ["<pre><code>\n", "class TrackedBlob {\n", "    // persistedBlobName is the name of </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> blob</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.152);\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.217);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.221);\"> currently</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> persisted</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> in</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3017241204663219\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003a6e0e-5c61-4a15-a87c-2ecb7a64a5f7\n"]}, {"data": {"text/html": ["<pre><code>    )\n", "\n", "    # TODO: update to use </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.365);\">`</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.285);\">pre</span><span class=\"token\" style=\"\">compute</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">freq</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">s</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">cis</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">`</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3026186090215145\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0070aa47-f17a-4fbc-8a95-d044b0ecc244\n"]}, {"data": {"text/html": ["<pre><code>\n", "// Wrapper around information we're interested in for all request types. The primary motivation for\n", "// this table is to have a single table in BigQu</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ery</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> that</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> can</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> be</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.190);\"> used</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.056);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.296);\"> query</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> all</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> types</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.303744312003636\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003aef04-6441-404a-8099-5b8ba9558f9d\n"]}, {"data": {"text/html": ["<pre><code>\"\"\"Create the </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.089);\">website</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.073);\"> for</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.334);\"> b</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">rowsing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> code</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">edit</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> samples</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> from</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> contract</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ors</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3077752664661063\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0164315c-5e28-461d-a520-83245f4a3a52\n"]}, {"data": {"text/html": ["<pre><code>    logo: str\n", "\n", "class gcp</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> project</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> region</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> zone</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> cluster</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> namespace</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.093);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.358);\"> service</span><span class=\"token\" style=\"\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">account</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> service</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">account</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">key</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00ff03e7-0800-41f8-9ed5-95aeec703988\n"]}, {"data": {"text/html": ["<pre><code>    private lastContextRoot: ContextRoot | undefined;\n", "\n", "    private</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.123);\"> _</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.359);\">status</span><span class=\"token\" style=\"\">Bar</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Item</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">!:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> vscode</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">StatusBar</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Item</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.31801059698128553\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/012ca941-4e70-4da3-aeb8-1e8bc3dea315\n"]}, {"data": {"text/html": ["<pre><code>    ) {\n", "        super();\n", "        </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">._</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">logger</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">info</span><span class=\"token\" style=\"\">(`</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.337);\">Using</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.157);\"> quick</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> suggestion</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> strategy</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ${</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">quick</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Suggestions</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">strategy</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}`);</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3192552552662921\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00cbc9bf-3e6d-4919-98d0-92d363291f90\n"]}, {"data": {"text/html": ["<pre><code>                        {\n", "                            location: vscode.ProgressLocation.Notification,\n", "                            title: \"Augment is reading your code. Go gra</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.342);\">y</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.270);\"><|skip|></span><span class=\"token\" style=\"\">,</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.31962570011467656\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0001aafe-2c43-4c33-9574-181ff46ad693\n"]}, {"data": {"text/html": ["<pre><code>\"\"</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">A</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.155);\"> system</span><span class=\"token\" style=\"\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.311);\"> calls</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> a</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> remote</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> API</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.\"\"\"</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3221260018134028\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0153bcf3-23bd-477c-800d-76e3f91adf36\n"]}, {"data": {"text/html": ["<pre><code>        }\n", "\n", "        //</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Queue</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> blob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> for</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> upload</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> if</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.095);\"> it</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.228);\"> has</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.193);\"> not</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> already</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> been</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> queued</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3232289021869488\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/015e69bf-9bac-4ebb-902a-d0e7f87f0bcc\n"]}, {"data": {"text/html": ["<pre><code>\n", "\n", "    def __iter</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">__(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">self</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> -></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Batch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Scheduler</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.051);\"> \"\"\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.325);\">Return</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.061);\"> self</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.\"\"\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> raise</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> NotImplementedError</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">()</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.32534012202220486\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0082563b-af70-473d-bca5-c868c544e564\n"]}, {"data": {"text/html": ["<pre><code>        namespace,\n", "        app,\n", "        // table na</code></pre><span class=\"token\" style=\"\">me</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.197);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.363);\"> unique</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.32614865056071046\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/002ddb49-b835-4073-baf7-bc9558247967\n"]}, {"data": {"text/html": ["<pre><code>                    }\n", "                    disabled = false;\n", "                    console.log(`Rat</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ing</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.221);\"> done</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.213);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.048);\"> Updating</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3307863593295047\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014de710-e286-4ec1-844e-d111755d04e5\n"]}, {"data": {"text/html": ["<pre><code>\n", "\n", "def </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.293);\">find</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.324);\">uppercase</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">character</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> str</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> -></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> int</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"\"\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Find</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> index</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> first</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> uppercase</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> character</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> in</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Args</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> to</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> search</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> must</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> be</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> lowercase</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> \"\"\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> find</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">character</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> uppercase</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">text</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33079729554206033\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00545095-52ec-41c3-bfc0-94e53b938baf\n"]}, {"data": {"text/html": ["<pre><code>};\n", "\n", "confirm</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">({</span><span class=\"token\" style=\"\">\n", " </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.270);\"> //</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.312);\"> eslint</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">disable</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">next</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">line</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> //</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> @</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ts</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ignore</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> get</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Container</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ()</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =></span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> document</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">getElementById</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">root</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">});</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3335300005710635\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/001302b2-48af-4916-8ab7-1f6989e8d6c7\n"]}, {"data": {"text/html": ["<pre><code>\n", "\n", "        </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">FORMAT</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">TER</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">TAGS</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> [</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.140);\"> f</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.043);\">\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.274);\">query</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">formatter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">={</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">query</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">formatter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">['</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">']</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> f</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">doc</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">formatter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">={</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">doc</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">formatter</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">['</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">']</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ]</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33397815661511865\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00810335-2ccb-4278-a6cd-49c43aca571e\n"]}, {"data": {"text/html": ["<pre><code>button {\n", "  display: inline-block;\n", "  background-color: inherit.</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.147);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.309);\">2</span><span class=\"token\" style=\"\">5</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">;</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33430446646373607\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/007a62b2-562c-47f9-a267-5e3006354185\n"]}, {"data": {"text/html": ["<pre><code>  };\n", "\n", "  local </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.386);\">secrets</span><span class=\"token\" style=\"\"> =</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.166);\"> [</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3344745457580181\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00007876-2008-435e-b3c7-2d206936af21\n"]}, {"data": {"text/html": ["<pre><code>    // probe the server to return the current indexing status;\n", "    // when true, CompletionResponse will only include unknown_blob_names and text will always be empty.\n", "    // This flag is deprecated. Please use</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.305);\"> `</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.253);\">blobs</span><span class=\"token\" style=\"\">`</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> instead</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33497167268024924\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/007fa49f-9bea-4f21-9b7a-cf87a5315934\n"]}, {"data": {"text/html": ["<pre><code>\n", "    While you can supply a seed to the sampling, shuffling is very difficult to be consistently random,\n", "    and will behave differently due to the dynamic nature of compute environments, i</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.094);\">n</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.081);\">cluding</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.277);\"> the</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> number</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> of</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> cores</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33599548652067646\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0147fbfa-aa61-4641-b030-d5fcc412c5cf\n"]}, {"data": {"text/html": ["<pre><code>    with output_path.open(\"w\") as f:\n", "        for question in tqdm(questions):\n", "            instruction, response = question</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> prompt</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.097);\"> f</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.186);\">\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.199);\">###</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Instruction</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:\\</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">n</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">{</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">instruction</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}\\</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">n</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">###</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Response</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:\\</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">n</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33632376738028086\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00823ed7-71f5-40b6-9499-28ad54e37013\n"]}, {"data": {"text/html": ["<pre><code>    \"\"\"\n", "    def find_all(self) -> list[auth_pb2.User]:\n", "        self.users</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">append</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">auth</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">pb</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">User</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">user</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">id</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">2</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">3</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\",</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> email</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.292);\">test</span><span class=\"token\" style=\"\">@</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.169);\">test</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">com</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\"))</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> return</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> self</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">users</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3364332655943163\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/01704418-245d-46cb-b351-0dfdd6894311\n"]}, {"data": {"text/html": ["<pre><code>})\n", "\n", "con</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">st</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.370);\"> foo</span><span class=\"token\" style=\"\"> =</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.165);\"> Promise</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">resolve</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(\"</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">foo</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\");</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3373379871917587\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/001e1a97-bada-474c-87e2-e7f19ed00bad\n"]}, {"data": {"text/html": ["<pre><code>    # base list to add and delete blob names.\n", "    # Note: we add and delete an overlapping blob name, 7, with the baseline,\n", "    # to test operation precede</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">d</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.082);\"> by</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.325);\"> a</span><span class=\"token\" style=\"\"> checkpoint</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.33841034081601723\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0006e7e2-796b-4d82-99d2-f4191514fe58\n"]}, {"data": {"text/html": ["<pre><code>            ), f\"Found more results {len(search_result.query_results.chunks)=} than requested {self._num_results=}\"\n", "\n", "            # When creating the key for content lookup, ensure that the </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">blob</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.288);\"> a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.154);\"> hex</span><span class=\"token\" style=\"\"> string</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/********-bb5d-4640-b8a9-f1f52741946e\n"]}, {"data": {"text/html": ["<pre><code>  ],\n", "\n", "  // Create service accounts fo</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">r</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.298);\"> each</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.025);\"> tenant</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.064);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014bc629-200f-41f1-9d35-b8dd95f0abf8\n"]}, {"data": {"text/html": ["<pre><code>    now = datetime.datetime.utcnow()\n", "    event.time.FromDatetime(now)\n", "    event.get_request_ids_in_session</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.373);\">append</span><span class=\"token\" style=\"\">(\"</span><span class=\"token\" style=\"\">test</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">-</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\")</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">events</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">append</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">event</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> response</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">insight</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">services</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Record</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">User</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Events</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> context</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">=</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Magic</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Mock</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">())</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> assert</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> response</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "   </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> assert</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> request</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">insight</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">services</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">publisher</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">publish</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">call</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">count</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ==</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">1</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.34731059695858163\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/016f2a1c-54d2-4c86-821d-2b961f1b62b8\n"]}, {"data": {"text/html": ["<pre><code>        _queued_requests.start()\n", "\n", "        self.queues</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> typing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Dict</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> embed</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">der</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">batch</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.010);\">Round</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.260);\">,</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.139);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> typing</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Tuple</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Future</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">np</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ndarray</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Future</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">np</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ndarray</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Future</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">np</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ndarray</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Future</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">np</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ndarray</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "           </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ],</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "       </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ]</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {}</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3488686126080799\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/001a53dd-da67-4ea7-af19-965edc4b2ae1\n"]}, {"data": {"text/html": ["<pre><code>instance_id = 'your-instance-id'\n", "table_id = 'fake-table'\n", "\n", "</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">#</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.127);\"> Set</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.030);\"> your</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.254);\"> Google</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Cloud</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Platform</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> project</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> and</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Big</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">table</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> instance</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> ID</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3506903766911724\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00910f91-bef9-4e91-a5dc-a6d8ff0df90b\n"]}, {"data": {"text/html": ["<pre><code>  };\n", "  std.assertEqual(actual, expected);\n", "\n", "</code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">local</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> big</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">table</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">in</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Prod</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.275);\">Legacy</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.004);\">Namespace</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.112);\">Test</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">()</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.3508616540390219\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00ad9360-0f1e-4b2b-a06c-ac91d4f29827\n"]}], "source": ["from itertools import accumulate\n", "from IPython.display import HTML, display\n", "import numpy as np\n", "\n", "window = 3\n", "model = f\"moving_average_{window}\"\n", "kernel = np.ones(window) / window\n", "\n", "low_conf = []\n", "seen = set()\n", "for c in completions:\n", "    if len(c.response.token_log_probs) < window:\n", "        continue\n", "    if c.request_id in seen:\n", "        continue\n", "    seen.add(c.request_id)\n", "    cache = c.response.token_log_probs\n", "    cache = np.convolve(cache, kernel, mode='valid')\n", "    low_conf.append((c, min(cache)))\n", "print(len(low_conf))\n", "\n", "from IPython.display import HTML, display\n", "import numpy as np\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "\n", "for c, s in sorted(low_conf, key=lambda x: x[1])[:100]:\n", "    logprobs = c.response.token_log_probs\n", "    ma = np.convolve(logprobs, kernel, mode='valid')\n", "    idx = np.argmin(ma)\n", "    scores = np.ones(len(logprobs))\n", "    scores[idx: idx+ window] = np.exp(logprobs[idx: idx+ window])\n", "    # scores[idx: idx+ window] = 0\n", "    display(HTML(render_code(c.request.prefix, c.response.tokens, scores)))\n", "    print(np.exp(s))\n", "    print(f\"{SUPPORT_DOGFOOD}{c.request_id}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Average"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1536\n"]}, {"data": {"text/html": ["<pre><code>        )\n", "\n", "        # TODO: </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.450);\">Record</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.197);\"> request</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.12274377210624232\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/009373ad-c995-45c5-80b6-033ed75dabc8\n"]}, {"data": {"text/html": ["<pre><code>  },\n", "  {\n", "    namespace: 'fasikl</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.367);\">ab</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.132977129392765\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/007837e8-72e5-4ad2-b480-8e51ff910ef3\n"]}, {"data": {"text/html": ["<pre><code>STARCODER_PATH = \"s3a://starcoder/by_repo_starcoder_tokenizer_4k_fim_sorted/\"\n", "STACKEXCHANGE_PATH = stackexchange_tokenized.location\n", "TEMPORARY_MIX_PATH = \"s3a://augment-eh</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.347);\">-</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.368);\">temp</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.14231575954976075\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0113f981-e452-433d-b6c8-1e38cf1abf71\n"]}, {"data": {"text/html": ["<pre><code>        \"enableDebugCommands\": true,\n", "        \"codeEdit\": {\n", "            \"url\": \"hacks</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.356);\">/</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1443453083942394\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003fe4ab-a6ee-4e21-9702-61c8bf16a33c\n"]}, {"data": {"text/html": ["<pre><code>\n", "class DeferentialQuickSuggestion implements vscode.Disposable\n", " ExtensionDisabled</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.355);\"> {</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.14476881510817305\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0103675e-229a-403b-9edc-97ba905f0b27\n"]}, {"data": {"text/html": ["<pre><code>            report_fn(step, group_metrics)\n", "            # TODO(carl): our old determined version is missing this generic `report_metrics`,\n", "            # hence the </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.406);\">above</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.406);\"> code</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.331);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.308);\"> not</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.356);\"> working</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.081);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.16066231792330882\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006d46ce-3341-4f2a-9b1c-e923549d900e\n"]}, {"data": {"text/html": ["<pre><code># given dataframe df, count the number of </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.315);\">rows</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.1846204607050395\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0173c7f7-fff4-4521-8297-2f44b0d81ac8\n"]}, {"data": {"text/html": ["<pre><code>            )\n", "\n", "        #### TODO: add </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.427);\">this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.192);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.284);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.354);\"> prompt</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.016);\"> formatter</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.20316232275153173\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00eb8934-aed8-4ff2-8d53-502756b3ef8b\n"]}, {"data": {"text/html": ["<pre><code>        result.unknown_checkpoint_id = retrieval_result.get_unknown_checkpoint_id()\n", "\n", "        # TODO(rich): m</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.070);\">ove</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.054);\"> this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.085);\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.249);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.435);\"> completion</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.389);\">_</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.386);\">host</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.314);\">.</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.204231530237928\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0134c82c-ffb9-4393-9c10-a21f105f9e79\n"]}, {"data": {"text/html": ["<pre><code>        expect(extension.modelInfo!.suggestedSuffixCharCount).toBe(params.suggestedSuffixCharCount);\n", "\n", "        // The second request is </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.384);\">a</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.338);\"> sentinel</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.045);\"> request</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2045891817298287\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/004e3ecf-b4b0-4df7-a05e-bd07072843fd\n"]}, {"data": {"text/html": ["<pre><code>from google.cloud.bigtable.table import Table\n", "\n", "# expected to be used only in dev, </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.397);\">so</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.308);\"> no</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.176);\"> need</span><span class=\"token\" style=\"\"> to</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.413);\"> use</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.442);\"> dat</span><span class=\"token\" style=\"\">aclass</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2048057398957115\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014debf7-6d84-4785-b0ee-0144a76b8fc1\n"]}, {"data": {"text/html": ["<pre><code>\n", "    def get_checkpoint(ids: workspace_manager.Blobs):\n", "        # This mock is complicated by the </code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.242);\">fact</span><span class=\"token\" style=\"\"> that</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.249);\"> the</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.426);\"> checkpoint</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.405);\"> id</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.063);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.407);\"> not</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.20945335362379475\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006332d4-9db0-4e3e-a4d8-3fd738280574\n"]}, {"data": {"text/html": ["<pre><code>        });\n", "\n", "        console.log('</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.305);\">Zip</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.260);\"> created</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.21615959438051743\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/004b5f77-6413-46e4-8b51-831709b3b6b7\n"]}, {"data": {"text/html": ["<pre><code>            .max_by_key(|m| match m.inference_config {\n", "                Some(config) => config.model_priority,\n", "                None => -1, // TODO: default_ma</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.123);\">del</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.365);\"> should</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.056);\"> be</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.383);\"> optional</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/006a1776-d1f9-4302-907b-33cc48b896db\n"]}, {"data": {"text/html": ["<pre><code>  };\n", "  local pod = {\n", "    serviceAccountName: accountService.access</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.302);\">or</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.225);\">,</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.*****************\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b65f4a-efd3-48cf-aa30-75aa3f498d7b\n"]}, {"data": {"text/html": ["<pre><code># Ground Truth\n", "print(sample[\"patch\"][\"patch_content\"][14</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.230);\">:</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.298);\">1</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.262);\">8</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.23483004736750454\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b78ba6-a163-43af-8ca5-73b12b553f92\n"]}, {"data": {"text/html": ["<pre><code>new_selected_range = expand_range(commented_range_in_file, 60, len(whole_file_text.splitlines(True)))\n", "\n", "# If new selected range is not covering the original selected range, then we can</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.310);\">'t</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.263);\"> use</span><span class=\"token\" style=\"\"> this</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.378);\"> sample</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.23671036619866295\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00f3b490-a59c-4246-b59e-20e67d59392c\n"]}, {"data": {"text/html": ["<pre><code>VENDOR_NAME = \"turing\"\n", "VENDOR_DATA = Path(f\"/home/<USER>/data/data-export-jan-31/aitutor-{VENDOR_NAME}\")\n", "\n", "</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.260);\">bucket</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.24032063934970863\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/003fbefb-1b3e-4d87-a115-da080942fab1\n"]}, {"data": {"text/html": ["<pre><code>\n", "import pytest\n", "import e</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.343);\">n</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.099);\">viron</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.25123974000340477\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000947a7-7e24-4c93-bb74-af3288c92562\n"]}, {"data": {"text/html": ["<pre><code>    time: string;\n", "\n", "    // File path of the file th e</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.248);\">vent</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.202);\"> is</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.268);\"> about</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.25917275895101294\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/014f5bc2-22a4-47fc-bb2f-2a5b39d1c43c\n"]}, {"data": {"text/html": ["<pre><code>    deps = [\n", "        \":deeproguesl_plain_33B_eth6_04_1_kubecfg\",\n", "        \":deeproguesl_plain_5</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.234);\">3</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0.2657510495179386\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b282d0-6075-4268-9083-8991f8ded2bd\n"]}], "source": ["from itertools import accumulate\n", "\n", "import numpy as np\n", "\n", "model = f\"average_token\"\n", "\n", "low_conf = []\n", "for c in completions:\n", "    cache = c.response.token_log_probs\n", "    # cache = np.exp(c.response.token_log_probs)\n", "    cache = np.mean(cache)\n", "    low_conf.append((c, cache))\n", "print(len(low_conf))\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "\n", "seen = set()\n", "for c, score in sorted(low_conf, key=lambda x: x[1])[:30]:\n", "    if c.request_id in seen:\n", "        continue\n", "    seen.add(c.request_id)\n", "    probs = np.exp(c.response.token_log_probs)\n", "    display(HTML(render_code(c.request.prefix, c.response.tokens, probs)))\n", "    print(np.exp(score))\n", "    print(f\"{SUPPORT_DOGFOOD}{c.request_id}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# HTML"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, display\n", "\n", "html_str = \"\"\"<style>\n", "    .token {\n", "        white-space: pre;\n", "        font-size: 11pt;\n", "        font-family: monospace;\n", "        line-height: 1.2em;\n", "    }\n", "    .token:hover {\n", "        text-decoration: underline;\n", "    }\n", "    .special-token {\n", "        color: rgb(90, 120, 220) !important;\n", "        font-weight: bold;\n", "    }\n", "    #checkbox:checked ~ .fim-ctx {\n", "        color: gray;\n", "        background-color: transparent !important;\n", "    }\n", "</style>\n", "\n", "<span class=\"token special-token fim-ctx\">&lt;fim_prefix&gt;</span><span class=\"token fim-ctx\">\n", "</span><span class=\"token fim-ctx\">def</span><span class=\"token fim-ctx\"> print</span><span class=\"token fim-ctx\">_</span><span class=\"token fim-ctx\">odd</span><span class=\"token fim-ctx\">_</span><span class=\"token fim-ctx\">even</span><span class=\"token fim-ctx\">(</span><span class=\"token fim-ctx\">n</span><span class=\"token fim-ctx\">:</span><span class=\"token fim-ctx\"> int</span><span class=\"token fim-ctx\">):</span><span class=\"token fim-ctx\">\n", "   </span><span class=\"token fim-ctx\"> &quot;&quot;&quot;</span><span class=\"token fim-ctx\">print</span><span class=\"token fim-ctx\"> whether</span><span class=\"token fim-ctx\"> n</span><span class=\"token fim-ctx\"> is</span><span class=\"token fim-ctx\"> odd</span><span class=\"token fim-ctx\"> or</span><span class=\"token fim-ctx\"> even</span><span class=\"token fim-ctx\">.&quot;&quot;&quot;</span><span class=\"token fim-ctx\">\n", "   </span><span class=\"token fim-ctx\"> if</span><span class=\"token fim-ctx\"> n</span><span class=\"token fim-ctx\"> %</span><span class=\"token fim-ctx\"> </span><span class=\"token fim-ctx\">2</span><span class=\"token fim-ctx\"> ==</span><span class=\"token fim-ctx\"> </span><span class=\"token fim-ctx\">0</span><span class=\"token fim-ctx\">:</span><span class=\"token fim-ctx\">\n", "       </span><span class=\"token fim-ctx\"> print</span><span class=\"token fim-ctx\">(</span><span class=\"token fim-ctx\">f</span><span class=\"token fim-ctx\">&quot;{</span><span class=\"token fim-ctx\">n</span><span class=\"token fim-ctx\">}</span><span class=\"token fim-ctx\"> is</span><span class=\"token fim-ctx\"> even</span><span class=\"token fim-ctx\">&quot;)</span><span class=\"token fim-ctx\">\n", "   </span><span class=\"token fim-ctx\"> else</span><span class=\"token fim-ctx\">:</span><span class=\"token special-token fim-ctx\">&lt;fim_suffix&gt;</span><span class=\"token special-token\">&lt;fim_middle&gt;</span><span class=\"token\">\n", "       </span><span class=\"token\"> print</span><span class=\"token\">(</span><span class=\"token\">f</span><span class=\"token\">&quot;{</span><span class=\"token\">n</span><span class=\"token\">}</span><span class=\"token\"> is</span><span class=\"token\"> even</span><span class=\"token\">&quot;)</span><span class=\"token special-token\">&lt;|endoftext|&gt;</span><span class=\"token fim-ctx\">\n", "</span><span class=\"token fim-ctx\">\n", "</span><span class=\"token fim-ctx\">if</span><span class=\"token fim-ctx\"> __</span><span class=\"token fim-ctx\">name</span><span class=\"token fim-ctx\">__</span><span class=\"token fim-ctx\"> ==</span><span class=\"token fim-ctx\"> &quot;__</span><span class=\"token fim-ctx\">main</span><span class=\"token fim-ctx\">__&quot;:</span><span class=\"token fim-ctx\">\n", "   </span><span class=\"token fim-ctx\"> main</span><span class=\"token fim-ctx\">()</span><span class=\"token fim-ctx\">        \n", "</span>\"\"\"\n", "\n", "display(HTML(html_str))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export to Sheet"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["sh: 0: getcwd() failed: No such file or directory\n", "The factory function of \"vs/server/node/server.cli\" has thrown an exception\n", "Error: ENOENT: no such file or directory, uv_cwd\n", "    at process.wrappedCwd [as cwd] (node:internal/bootstrap/switches/does_own_process_state:126:28)\n", "    at cwd (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/vs/server/node/server.cli.js:11:59082)\n", "    at Object.<anonymous> (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/vs/server/node/server.cli.js:14:8239)\n", "    at y._safeInvokeFunction (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1245)\n", "    at y._invokeFactory (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1425)\n", "    at y.complete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1682)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1051)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1297)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1297)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1297)\n", "    at d._resolve (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:510)\n", "    at d.defineModule (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:5561)\n", "    at r (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:9603)\n", "    at Object.D (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:5901)\n", "    at y._safeInvokeFunction (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1245)\n", "    at y._invokeFactory (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1425)\n", "    at y.complete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:1682)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1051)\n", "    at d._onModuleComplete (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1297)\n", "    at d._resolve (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:510)\n", "    at d.defineModule (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:3:5561)\n", "    at g (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:4:1741)\n", "    at m (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:2:2448)\n", "    at Object.<anonymous> (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/vs/server/node/server.cli.nls.js:3:61)\n", "    at n._createAndEvalScript (/home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:2:2492)\n", "    at /home/<USER>/.vscode-server/bin/fdb98833154679dbaa7af67a5a29fe19e55c2b73/out/server-cli.js:2:2133\n", "    at FSReqCallback.readFileAfterClose [as oncomplete] (node:internal/fs/read_file_context:68:3) {\n", "  errno: -2,\n", "  code: 'ENOENT',\n", "  syscall: 'uv_cwd',\n", "  phase: 'factory',\n", "  moduleId: 'vs/server/node/server.cli',\n", "  neededBy: [ '===anonymous1===' ]\n", "}\n", "Here are the modules that depend on it:\n", "[ '===anonymous1===' ]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Please visit this URL to authorize this application: https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=************-9un02l6j66lbn6m3t8akt7jabthooqhh.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2F&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fspreadsheets&state=0tB7xbvsOLDGFkLfHpoH8P0P3GmIlk&access_type=offline\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:google_auth_oauthlib.flow:\"GET /?state=0tB7xbvsOLDGFkLfHpoH8P0P3GmIlk&code=4/0AeaYSHAlS7t8994_wWKc87QwboiPWWwD4CLeSc22grGTI_VTRltzsIUXcvey0r3dF_O27Q&scope=https://www.googleapis.com/auth/spreadsheets HTTP/1.1\" 200 65\n", "INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0\n"]}], "source": ["# Get credentials\n", "\n", "from base.datasets import google_sheet_utils\n", "creds = google_sheet_utils.get_google_sheet_credentials()\n", "service = google_sheet_utils.get_google_sheets_service(creds)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n", "average_token\n", "Added new sheet with title: average_token\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n", "2 cells updated.\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from base.datasets import google_sheet_utils\n", "\n", "spreadsheet_id = \"183OGpRrbKMf0GL9YWWVG9dk2r1WOH1Ft8_qiDXTr7ek\"\n", "print(model)\n", "google_sheet_utils.create_new_sheet(service, spreadsheet_id, model)\n", "\n", "\n", "urls = []\n", "seen = set()\n", "count = 0\n", "for _, (c, s) in enumerate(sorted(low_conf, key=lambda x: x[1])):\n", "    if c.request_id in seen:\n", "        continue\n", "    seen.add(c.request_id)\n", "    urls.append(f\"{SUPPORT_DOGFOOD}{c.request_id}\")\n", "    google_sheet_utils.update_row(\n", "        service,\n", "        spreadsheet_id,\n", "        model,\n", "        count + 1,\n", "        values=[\n", "            f\"{SUPPORT_DOGFOOD}{c.request_id}\",\n", "            np.exp(s),\n", "            # s,\n", "        ],\n", "    )\n", "    count += 1\n", "    if count == 30:\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sample Long Reject"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Querying BigQuery: SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND response.completion_length >= 1\n", "        AND rand() < 0.1\n", "        ORDER BY request.request_id\n", "        LIMIT 4000\n", "\n", "INFO:base.datasets.completion:Enqueuing batch 0\n", "INFO:base.datasets.completion:Processing batch 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 128 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 128 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 128 keys (total size 714208 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:00:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 714208 bytes to insert 128 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 1\n", "INFO:base.datasets.completion:Processing batch 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 42 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 42 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 42 keys (total size 264624 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:00:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 264624 bytes to insert 42 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 2\n", "INFO:base.datasets.completion:Processing batch 2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 48 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 48 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 48 keys (total size 815328 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:00:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 815328 bytes to insert 48 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 3\n", "INFO:base.datasets.completion:Processing batch 3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:55\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 39 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 39 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 39 keys (total size 677088 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:00:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 677088 bytes to insert 39 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 4\n", "INFO:base.datasets.completion:Processing batch 4\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 34 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 34 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 34 keys (total size 945720 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:00:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 945720 bytes to insert 34 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 5\n", "INFO:base.datasets.completion:Processing batch 5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:00:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 41 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:00:59\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 41 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:00\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 41 keys (total size 726312 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:00\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 726312 bytes to insert 41 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:00\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 6\n", "INFO:base.datasets.completion:Processing batch 6\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 34 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 34 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 34 keys (total size 424600 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 424600 bytes to insert 34 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 7\n", "INFO:base.datasets.completion:Processing batch 7\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 36 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 36 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 36 keys (total size 757824 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 757824 bytes to insert 36 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 8\n", "INFO:base.datasets.completion:Processing batch 8\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 32 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 32 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 32 keys (total size 594688 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 594688 bytes to insert 32 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:05\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 9\n", "INFO:base.datasets.completion:Processing batch 9\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:07\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 52 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:07\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 52 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:07\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 52 keys (total size 1615888 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:07\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1615888 bytes to insert 52 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:07\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 10\n", "INFO:base.datasets.completion:Processing batch 10\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 43 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 43 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 43 keys (total size 688288 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 688288 bytes to insert 43 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 11\n", "INFO:base.datasets.completion:Processing batch 11\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 39 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 39 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 39 keys (total size 1164488 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1164488 bytes to insert 39 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 12\n", "INFO:base.datasets.completion:Processing batch 12\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 37 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 37 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 37 keys (total size 833856 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 833856 bytes to insert 37 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 13\n", "INFO:base.datasets.completion:Processing batch 13\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 33 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 33 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 33 keys (total size 243624 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 243624 bytes to insert 33 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 14\n", "INFO:base.datasets.completion:Processing batch 14\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:19\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 38 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 38 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 38 keys (total size 1045504 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1045504 bytes to insert 38 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 15\n", "INFO:base.datasets.completion:Processing batch 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 36 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 36 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 36 keys (total size 513816 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 513816 bytes to insert 36 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:21\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 16\n", "INFO:base.datasets.completion:Processing batch 16\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:23\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 45 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:23\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 45 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:23\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 45 keys (total size 627888 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:23\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 627888 bytes to insert 45 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:23\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 17\n", "INFO:base.datasets.completion:Processing batch 17\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 42 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 42 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 42 keys (total size 580888 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 580888 bytes to insert 42 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 18\n", "INFO:base.datasets.completion:Processing batch 18\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:29\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 31 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:29\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 31 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:29\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 31 keys (total size 489264 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:29\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 489264 bytes to insert 31 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:29\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 19\n", "INFO:base.datasets.completion:Processing batch 19\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 28 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 28 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 28 keys (total size 440496 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 440496 bytes to insert 28 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 20\n", "INFO:base.datasets.completion:Processing batch 20\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:32\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 36 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 36 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 36 keys (total size 624456 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 624456 bytes to insert 36 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:33\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 21\n", "INFO:base.datasets.completion:Processing batch 21\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:34\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 29 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:35\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 29 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:35\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 29 keys (total size 796208 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:35\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 796208 bytes to insert 29 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:35\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 22\n", "INFO:base.datasets.completion:Processing batch 22\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 27 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:37\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 27 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:37\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 27 keys (total size 315632 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:37\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 315632 bytes to insert 27 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:37\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 23\n", "INFO:base.datasets.completion:Processing batch 23\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 40 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 40 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 40 keys (total size 877544 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 877544 bytes to insert 40 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 24\n", "INFO:base.datasets.completion:Processing batch 24\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:40\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 30 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:40\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 30 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:40\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 30 keys (total size 315312 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:40\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 315312 bytes to insert 30 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:40\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 25\n", "INFO:base.datasets.completion:Processing batch 25\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 36 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 36 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 36 keys (total size 769856 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 769856 bytes to insert 36 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:44\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 26\n", "INFO:base.datasets.completion:Processing batch 26\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 27 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 27 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 27 keys (total size 416736 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 416736 bytes to insert 27 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:45\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 27\n", "INFO:base.datasets.completion:Processing batch 27\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 31 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 31 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 31 keys (total size 380768 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 380768 bytes to insert 31 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 28\n", "INFO:base.datasets.completion:Processing batch 28\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 36 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 36 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 36 keys (total size 967800 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 967800 bytes to insert 36 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 29\n", "INFO:base.datasets.completion:Processing batch 29\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:50\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 33 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:51\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 33 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:51\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 33 keys (total size 479080 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:51\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 479080 bytes to insert 33 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:51\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Enqueuing batch 30\n", "INFO:base.datasets.completion:Processing batch 30\n", "INFO:base.datasets.completion:Enqueuing batch 31\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 29 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 29 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 29 keys (total size 797680 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 797680 bytes to insert 29 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion:Processing batch 31\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 10 missing entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 10 missing entries from; 0 still missing.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 10 keys (total size 406728 bytes).\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 406728 bytes to insert 10 entries.\u001b[0m\n", "\u001b[2m2024-02-21 07:01:52\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 bytes.\u001b[0m\n", "1521\n"]}], "source": ["# Sample random\n", "\n", "from base.datasets import tenants, completion\n", "from google.cloud import bigquery, storage\n", "from typing import Literal, Optional, TypedDict, cast\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, PathAndContent\n", "\n", "query = \"\"\"SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND response.completion_length >= 1\n", "        AND rand() < 0.1\n", "        ORDER BY request.request_id\n", "        LIMIT 4000\n", "\"\"\"\n", "\n", "\n", "TENANT_NAME = \"dogfood\"\n", "\n", "dataset = completion_dataset.CompletionDataset.create_from_query(\n", "    query, tenants.get_tenant(TENANT_NAME)\n", ")\n", "\n", "# tenant = tenants.get_tenant(TENANT_NAME)\n", "# bucket = storage.Client(tenant.project_id).bucket(tenant.blob_bucket_name)\n", "# blob_cache = GCSBlobCache(\n", "#     bucket,\n", "#     tenant.blob_bucket_prefix,\n", "#     2**30,\n", "#     num_threads=10,\n", "# )\n", "# bigquery_client = bigquery.Client(tenant.project_id)\n", "# rows = bigquery_client.query_and_wait(query)\n", "# dataset = completion_dataset.CompletionDataset(\n", "#     rows,\n", "#     cast(Optional[int], rows.total_rows),\n", "#     blob_cache=blob_cache,\n", "# )\n", "completions = list(\n", "    c\n", "    for c in dataset.get_completions()\n", "    if \"roguesl-farpref-16B\" in c.response.model\n", ")\n", "print(len(completions))"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [], "source": ["deltas = []\n", "rst = []\n", "for c in completions:\n", "    delta = c.resolution.timestamp - c.response.timestamp\n", "    if delta.total_seconds() > 1.0 and c.resolution.accepted:\n", "        rst.append(c)\n", "    # deltas.append(c.resolution.timestamp - c.response.timestamp)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre><code>  }\n", "}\n", "\n", "</code></pre><span class=\"token\" style=\"\">function</span><span class=\"token\" style=\"\"> folder</span><span class=\"token\" style=\"\"> (</span><span class=\"token\" style=\"\">arg</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"\"> options</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> sub</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Command</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> {</span><span class=\"token\" style=\"\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> const</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> operation</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> =</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> operations</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">[</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">sub</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Command</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">._</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">name</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">]</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", " </span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> operation</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">arg</span><span class=\"token\" style=\"\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> options</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">)</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">}</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/000a6bea-4f9b-4b54-b5d4-038338cb143f\n"]}, {"data": {"text/html": ["<pre><code>            request_in.model.clone()\n", "        } else {\n", "            MODEL_FLAG.get_from_service(&</code></pre><span class=\"token\" style=\"\">self</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">feature</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">_</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">flags</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00405dec-f78c-4d18-940d-6cea561d7e8b\n"]}, {"data": {"text/html": ["<pre><code>                this._quickSuggestion.notifyInlineCompletion(\"\", replacementRange.start);\n", "                return [\n", "                    new </code></pre><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">vscode</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Inline</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Completion</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Item</span><span class=\"token\" style=\"\">(\"\",</span><span class=\"token\" style=\"\"> new</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> vscode</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Range</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">(</span><span class=\"token\" style=\"\">position</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">,</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> position</span><span class=\"token\" style=\"\">)),</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/0045a4c1-63ef-40a4-88d3-4dac572bc21d\n"]}, {"data": {"text/html": ["<pre><code>        it(\"should set the webview's html\", async () => {\n", "            const provider = new FeedbackWebviewProvider(\n", "                vscode.Uri.</code></pre><span class=\"token\" style=\"background-color: rgba(255,0,0,0.027);\">parse</span><span class=\"token\" style=\"\">(\"</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.150);\">vscode</span><span class=\"token\" style=\"\">://</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.289);\">example</span><span class=\"token\" style=\"\">/</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.346);\">path</span><span class=\"token\" style=\"\">\"),</span><span class=\"token\" style=\"\">\n", "               </span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.076);\"> new</span><span class=\"token\" style=\"\"> Aug</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Extension</span><span class=\"token\" style=\"\">(),</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">\n", "               </span><span class=\"token\" style=\"\"> new</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\"> Aug</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ment</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Config</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">Listener</span><span class=\"token\" style=\"\">()</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00958bef-93f3-4186-b49a-1a593cfada06\n"]}, {"data": {"text/html": ["<pre><code>        }\n", "        if (event.contentChanges[0].rangeLength !== 0) {\n", "            </code></pre><span class=\"token\" style=\"\">this</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">._</span><span class=\"token\" style=\"\">logger</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">.</span><span class=\"token\" style=\"\">info</span><span class=\"token\" style=\"\">(`</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">ml</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">m</span><span class=\"token\" style=\"background-color: rgba(0,180,180,0.2);\">:</span><span class=\"token\" style=\"background-color: rgba(255,0,0,0.014);\"> not</span><span class=\"token\" style=\"\"> a</span><span class=\"token\" style=\"\"> plain</span><span class=\"token\" style=\"\"> insertion</span><span class=\"token\" style=\"\">`);</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00abe925-4f16-4a5d-abd5-eb111eaa240c\n"]}, {"data": {"text/html": ["<pre><code>            for i in 0..entry.tensor.size() {\n", "                let value = entry.tensor.get(i);\n", "                tracing::info(\"</code></pre><span class=\"token\" style=\"\">value</span><span class=\"token\" style=\"\">={</span><span class=\"token\" style=\"\">}\",</span><span class=\"token\" style=\"\"> value</span><span class=\"token\" style=\"\">);</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["True\n", "https://support.dogfood.t.us-central1.prod.augmentcode.com/request/00b918a3-4203-4d2b-8869-d700b189d6f3\n"]}], "source": ["for c in rst[:30]:\n", "    probs = np.exp(c.response.token_log_probs)\n", "    display(HTML(render_code(c.request.prefix, c.response.tokens, probs)))\n", "    print(c.resolution.accepted)\n", "    print(f\"{SUPPORT_DOGFOOD}{c.request_id}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}
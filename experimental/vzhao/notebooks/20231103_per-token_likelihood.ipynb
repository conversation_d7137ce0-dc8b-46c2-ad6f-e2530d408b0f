{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This PR is to demonstrate and test model scoring to return per-token likelihood."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eval harness is not available (No module named 'lm_eval').\n"]}], "source": ["from research.models.all_models import get_model\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads StarCoder and call `log_likelihood_continuation` to get per-token loss."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/config.yml')]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "> initializing torch distributed ...\n", "[2023-11-06 20:04:20,029] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-11-06 20:04:20,449] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=*************, master_port=6001\n", "[2023-11-06 20:04:20,450] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-11-06 20:04:20,455] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[vzhao-dev:573680] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "> building StarCoderTokenizer tokenizer ...\n", " > padded vocab (size: 49165) with 2035 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-11-06 20:04:20,923] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    0.position_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.key_value.weight requires_grad=True\n", "    2.attention.key_value.bias requires_grad=True\n", "    2.attention.query.weight requires_grad=True\n", "    2.attention.query.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.post_attention_layernorm.weight requires_grad=True\n", "    2.post_attention_layernorm.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.key_value.weight requires_grad=True\n", "    3.attention.key_value.bias requires_grad=True\n", "    3.attention.query.weight requires_grad=True\n", "    3.attention.query.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.post_attention_layernorm.weight requires_grad=True\n", "    3.post_attention_layernorm.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.key_value.weight requires_grad=True\n", "    4.attention.key_value.bias requires_grad=True\n", "    4.attention.query.weight requires_grad=True\n", "    4.attention.query.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.post_attention_layernorm.weight requires_grad=True\n", "    4.post_attention_layernorm.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.key_value.weight requires_grad=True\n", "    5.attention.key_value.bias requires_grad=True\n", "    5.attention.query.weight requires_grad=True\n", "    5.attention.query.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.post_attention_layernorm.weight requires_grad=True\n", "    5.post_attention_layernorm.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.key_value.weight requires_grad=True\n", "    6.attention.key_value.bias requires_grad=True\n", "    6.attention.query.weight requires_grad=True\n", "    6.attention.query.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.post_attention_layernorm.weight requires_grad=True\n", "    6.post_attention_layernorm.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.key_value.weight requires_grad=True\n", "    7.attention.key_value.bias requires_grad=True\n", "    7.attention.query.weight requires_grad=True\n", "    7.attention.query.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.post_attention_layernorm.weight requires_grad=True\n", "    7.post_attention_layernorm.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.key_value.weight requires_grad=True\n", "    8.attention.key_value.bias requires_grad=True\n", "    8.attention.query.weight requires_grad=True\n", "    8.attention.query.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.post_attention_layernorm.weight requires_grad=True\n", "    8.post_attention_layernorm.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.key_value.weight requires_grad=True\n", "    9.attention.key_value.bias requires_grad=True\n", "    9.attention.query.weight requires_grad=True\n", "    9.attention.query.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.post_attention_layernorm.weight requires_grad=True\n", "    9.post_attention_layernorm.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.key_value.weight requires_grad=True\n", "    10.attention.key_value.bias requires_grad=True\n", "    10.attention.query.weight requires_grad=True\n", "    10.attention.query.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.post_attention_layernorm.weight requires_grad=True\n", "    10.post_attention_layernorm.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.key_value.weight requires_grad=True\n", "    11.attention.key_value.bias requires_grad=True\n", "    11.attention.query.weight requires_grad=True\n", "    11.attention.query.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.post_attention_layernorm.weight requires_grad=True\n", "    11.post_attention_layernorm.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.key_value.weight requires_grad=True\n", "    12.attention.key_value.bias requires_grad=True\n", "    12.attention.query.weight requires_grad=True\n", "    12.attention.query.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.post_attention_layernorm.weight requires_grad=True\n", "    12.post_attention_layernorm.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.key_value.weight requires_grad=True\n", "    13.attention.key_value.bias requires_grad=True\n", "    13.attention.query.weight requires_grad=True\n", "    13.attention.query.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.post_attention_layernorm.weight requires_grad=True\n", "    13.post_attention_layernorm.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    14.input_layernorm.weight requires_grad=True\n", "    14.input_layernorm.bias requires_grad=True\n", "    14.attention.key_value.weight requires_grad=True\n", "    14.attention.key_value.bias requires_grad=True\n", "    14.attention.query.weight requires_grad=True\n", "    14.attention.query.bias requires_grad=True\n", "    14.attention.dense.weight requires_grad=True\n", "    14.attention.dense.bias requires_grad=True\n", "    14.post_attention_layernorm.weight requires_grad=True\n", "    14.post_attention_layernorm.bias requires_grad=True\n", "    14.mlp.dense_h_to_4h.weight requires_grad=True\n", "    14.mlp.dense_h_to_4h.bias requires_grad=True\n", "    14.mlp.dense_4h_to_h.weight requires_grad=True\n", "    14.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.input_layernorm.weight requires_grad=True\n", "    15.input_layernorm.bias requires_grad=True\n", "    15.attention.key_value.weight requires_grad=True\n", "    15.attention.key_value.bias requires_grad=True\n", "    15.attention.query.weight requires_grad=True\n", "    15.attention.query.bias requires_grad=True\n", "    15.attention.dense.weight requires_grad=True\n", "    15.attention.dense.bias requires_grad=True\n", "    15.post_attention_layernorm.weight requires_grad=True\n", "    15.post_attention_layernorm.bias requires_grad=True\n", "    15.mlp.dense_h_to_4h.weight requires_grad=True\n", "    15.mlp.dense_h_to_4h.bias requires_grad=True\n", "    15.mlp.dense_4h_to_h.weight requires_grad=True\n", "    15.mlp.dense_4h_to_h.bias requires_grad=True\n", "    16.input_layernorm.weight requires_grad=True\n", "    16.input_layernorm.bias requires_grad=True\n", "    16.attention.key_value.weight requires_grad=True\n", "    16.attention.key_value.bias requires_grad=True\n", "    16.attention.query.weight requires_grad=True\n", "    16.attention.query.bias requires_grad=True\n", "    16.attention.dense.weight requires_grad=True\n", "    16.attention.dense.bias requires_grad=True\n", "    16.post_attention_layernorm.weight requires_grad=True\n", "    16.post_attention_layernorm.bias requires_grad=True\n", "    16.mlp.dense_h_to_4h.weight requires_grad=True\n", "    16.mlp.dense_h_to_4h.bias requires_grad=True\n", "    16.mlp.dense_4h_to_h.weight requires_grad=True\n", "    16.mlp.dense_4h_to_h.bias requires_grad=True\n", "    17.input_layernorm.weight requires_grad=True\n", "    17.input_layernorm.bias requires_grad=True\n", "    17.attention.key_value.weight requires_grad=True\n", "    17.attention.key_value.bias requires_grad=True\n", "    17.attention.query.weight requires_grad=True\n", "    17.attention.query.bias requires_grad=True\n", "    17.attention.dense.weight requires_grad=True\n", "    17.attention.dense.bias requires_grad=True\n", "    17.post_attention_layernorm.weight requires_grad=True\n", "    17.post_attention_layernorm.bias requires_grad=True\n", "    17.mlp.dense_h_to_4h.weight requires_grad=True\n", "    17.mlp.dense_h_to_4h.bias requires_grad=True\n", "    17.mlp.dense_4h_to_h.weight requires_grad=True\n", "    17.mlp.dense_4h_to_h.bias requires_grad=True\n", "    18.input_layernorm.weight requires_grad=True\n", "    18.input_layernorm.bias requires_grad=True\n", "    18.attention.key_value.weight requires_grad=True\n", "    18.attention.key_value.bias requires_grad=True\n", "    18.attention.query.weight requires_grad=True\n", "    18.attention.query.bias requires_grad=True\n", "    18.attention.dense.weight requires_grad=True\n", "    18.attention.dense.bias requires_grad=True\n", "    18.post_attention_layernorm.weight requires_grad=True\n", "    18.post_attention_layernorm.bias requires_grad=True\n", "    18.mlp.dense_h_to_4h.weight requires_grad=True\n", "    18.mlp.dense_h_to_4h.bias requires_grad=True\n", "    18.mlp.dense_4h_to_h.weight requires_grad=True\n", "    18.mlp.dense_4h_to_h.bias requires_grad=True\n", "    19.input_layernorm.weight requires_grad=True\n", "    19.input_layernorm.bias requires_grad=True\n", "    19.attention.key_value.weight requires_grad=True\n", "    19.attention.key_value.bias requires_grad=True\n", "    19.attention.query.weight requires_grad=True\n", "    19.attention.query.bias requires_grad=True\n", "    19.attention.dense.weight requires_grad=True\n", "    19.attention.dense.bias requires_grad=True\n", "    19.post_attention_layernorm.weight requires_grad=True\n", "    19.post_attention_layernorm.bias requires_grad=True\n", "    19.mlp.dense_h_to_4h.weight requires_grad=True\n", "    19.mlp.dense_h_to_4h.bias requires_grad=True\n", "    19.mlp.dense_4h_to_h.weight requires_grad=True\n", "    19.mlp.dense_4h_to_h.bias requires_grad=True\n", "    20.input_layernorm.weight requires_grad=True\n", "    20.input_layernorm.bias requires_grad=True\n", "    20.attention.key_value.weight requires_grad=True\n", "    20.attention.key_value.bias requires_grad=True\n", "    20.attention.query.weight requires_grad=True\n", "    20.attention.query.bias requires_grad=True\n", "    20.attention.dense.weight requires_grad=True\n", "    20.attention.dense.bias requires_grad=True\n", "    20.post_attention_layernorm.weight requires_grad=True\n", "    20.post_attention_layernorm.bias requires_grad=True\n", "    20.mlp.dense_h_to_4h.weight requires_grad=True\n", "    20.mlp.dense_h_to_4h.bias requires_grad=True\n", "    20.mlp.dense_4h_to_h.weight requires_grad=True\n", "    20.mlp.dense_4h_to_h.bias requires_grad=True\n", "    21.input_layernorm.weight requires_grad=True\n", "    21.input_layernorm.bias requires_grad=True\n", "    21.attention.key_value.weight requires_grad=True\n", "    21.attention.key_value.bias requires_grad=True\n", "    21.attention.query.weight requires_grad=True\n", "    21.attention.query.bias requires_grad=True\n", "    21.attention.dense.weight requires_grad=True\n", "    21.attention.dense.bias requires_grad=True\n", "    21.post_attention_layernorm.weight requires_grad=True\n", "    21.post_attention_layernorm.bias requires_grad=True\n", "    21.mlp.dense_h_to_4h.weight requires_grad=True\n", "    21.mlp.dense_h_to_4h.bias requires_grad=True\n", "    21.mlp.dense_4h_to_h.weight requires_grad=True\n", "    21.mlp.dense_4h_to_h.bias requires_grad=True\n", "    22.input_layernorm.weight requires_grad=True\n", "    22.input_layernorm.bias requires_grad=True\n", "    22.attention.key_value.weight requires_grad=True\n", "    22.attention.key_value.bias requires_grad=True\n", "    22.attention.query.weight requires_grad=True\n", "    22.attention.query.bias requires_grad=True\n", "    22.attention.dense.weight requires_grad=True\n", "    22.attention.dense.bias requires_grad=True\n", "    22.post_attention_layernorm.weight requires_grad=True\n", "    22.post_attention_layernorm.bias requires_grad=True\n", "    22.mlp.dense_h_to_4h.weight requires_grad=True\n", "    22.mlp.dense_h_to_4h.bias requires_grad=True\n", "    22.mlp.dense_4h_to_h.weight requires_grad=True\n", "    22.mlp.dense_4h_to_h.bias requires_grad=True\n", "    23.input_layernorm.weight requires_grad=True\n", "    23.input_layernorm.bias requires_grad=True\n", "    23.attention.key_value.weight requires_grad=True\n", "    23.attention.key_value.bias requires_grad=True\n", "    23.attention.query.weight requires_grad=True\n", "    23.attention.query.bias requires_grad=True\n", "    23.attention.dense.weight requires_grad=True\n", "    23.attention.dense.bias requires_grad=True\n", "    23.post_attention_layernorm.weight requires_grad=True\n", "    23.post_attention_layernorm.bias requires_grad=True\n", "    23.mlp.dense_h_to_4h.weight requires_grad=True\n", "    23.mlp.dense_h_to_4h.bias requires_grad=True\n", "    23.mlp.dense_4h_to_h.weight requires_grad=True\n", "    23.mlp.dense_4h_to_h.bias requires_grad=True\n", "    24.input_layernorm.weight requires_grad=True\n", "    24.input_layernorm.bias requires_grad=True\n", "    24.attention.key_value.weight requires_grad=True\n", "    24.attention.key_value.bias requires_grad=True\n", "    24.attention.query.weight requires_grad=True\n", "    24.attention.query.bias requires_grad=True\n", "    24.attention.dense.weight requires_grad=True\n", "    24.attention.dense.bias requires_grad=True\n", "    24.post_attention_layernorm.weight requires_grad=True\n", "    24.post_attention_layernorm.bias requires_grad=True\n", "    24.mlp.dense_h_to_4h.weight requires_grad=True\n", "    24.mlp.dense_h_to_4h.bias requires_grad=True\n", "    24.mlp.dense_4h_to_h.weight requires_grad=True\n", "    24.mlp.dense_4h_to_h.bias requires_grad=True\n", "    25.input_layernorm.weight requires_grad=True\n", "    25.input_layernorm.bias requires_grad=True\n", "    25.attention.key_value.weight requires_grad=True\n", "    25.attention.key_value.bias requires_grad=True\n", "    25.attention.query.weight requires_grad=True\n", "    25.attention.query.bias requires_grad=True\n", "    25.attention.dense.weight requires_grad=True\n", "    25.attention.dense.bias requires_grad=True\n", "    25.post_attention_layernorm.weight requires_grad=True\n", "    25.post_attention_layernorm.bias requires_grad=True\n", "    25.mlp.dense_h_to_4h.weight requires_grad=True\n", "    25.mlp.dense_h_to_4h.bias requires_grad=True\n", "    25.mlp.dense_4h_to_h.weight requires_grad=True\n", "    25.mlp.dense_4h_to_h.bias requires_grad=True\n", "    27.norm.weight requires_grad=True\n", "    27.norm.bias requires_grad=True\n", "    28.final_linear.weight requires_grad=True\n", "DeepSpeed is enabled.\n", "[2023-11-06 20:04:22,632] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+d08ec4e, git-hash=d08ec4e, git-branch=HEAD\n", "[2023-11-06 20:04:22,633] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-11-06 20:04:22,690] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-11-06 20:04:22,693] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-11-06 20:04:22,694] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-11-06 20:04:22,695] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-11-06 20:04:22,696] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-11-06 20:04:22,697] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-11-06 20:04:22,697] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-11-06 20:04:22,698] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-11-06 20:04:22,700] [INFO] [config.py:763:print]   disable_allgather ............ False\n", "[2023-11-06 20:04:22,701] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-11-06 20:04:22,702] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-11-06 20:04:22,703] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-11-06 20:04:22,704] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-11-06 20:04:22,704] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-11-06 20:04:22,705] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-11-06 20:04:22,705] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-11-06 20:04:22,706] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-11-06 20:04:22,706] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-11-06 20:04:22,707] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-11-06 20:04:22,707] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-11-06 20:04:22,708] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-11-06 20:04:22,709] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-11-06 20:04:22,710] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-11-06 20:04:22,710] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-11-06 20:04:22,712] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 2e-05}\n", "[2023-11-06 20:04:22,713] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-11-06 20:04:22,714] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-11-06 20:04:22,717] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-11-06 20:04:22,717] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-11-06 20:04:22,718] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-11-06 20:04:22,718] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-11-06 20:04:22,720] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-11-06 20:04:22,720] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-11-06 20:04:22,722] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-11-06 20:04:22,723] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-11-06 20:04:22,724] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-11-06 20:04:22,725] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-11-06 20:04:22,726] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-11-06 20:04:22,727] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-11-06 20:04:22,727] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-11-06 20:04:22,728] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-11-06 20:04:22,729] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-11-06 20:04:22,729] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-11-06 20:04:22,730] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-11-06 20:04:22,731] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-11-06 20:04:22,731] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-11-06 20:04:22,733] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 2e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:761: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu121 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu121/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 1.1157684326171875 seconds\n", "[2023-11-06 20:04:24,859] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-11-06 20:04:24,917] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang\n", "[2023-11-06 20:04:24,956] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-11-06 20:04:26,288] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_00-model_00-model_states.pt\n", "[2023-11-06 20:04:26,626] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_02-model_00-model_states.pt\n", "[2023-11-06 20:04:26,960] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_03-model_00-model_states.pt\n", "[2023-11-06 20:04:27,143] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_04-model_00-model_states.pt\n", "[2023-11-06 20:04:27,371] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_05-model_00-model_states.pt\n", "[2023-11-06 20:04:28,042] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_06-model_00-model_states.pt\n", "[2023-11-06 20:04:28,142] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_07-model_00-model_states.pt\n", "[2023-11-06 20:04:28,472] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_08-model_00-model_states.pt\n", "[2023-11-06 20:04:28,994] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_09-model_00-model_states.pt\n", "[2023-11-06 20:04:29,402] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_10-model_00-model_states.pt\n", "[2023-11-06 20:04:29,625] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_11-model_00-model_states.pt\n", "[2023-11-06 20:04:29,861] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_12-model_00-model_states.pt\n", "[2023-11-06 20:04:30,082] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_13-model_00-model_states.pt\n", "[2023-11-06 20:04:30,342] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_14-model_00-model_states.pt\n", "[2023-11-06 20:04:30,680] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_15-model_00-model_states.pt\n", "[2023-11-06 20:04:30,987] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_16-model_00-model_states.pt\n", "[2023-11-06 20:04:31,176] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_17-model_00-model_states.pt\n", "[2023-11-06 20:04:31,410] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_18-model_00-model_states.pt\n", "[2023-11-06 20:04:31,672] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_19-model_00-model_states.pt\n", "[2023-11-06 20:04:32,057] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_20-model_00-model_states.pt\n", "[2023-11-06 20:04:32,534] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_21-model_00-model_states.pt\n", "[2023-11-06 20:04:32,956] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_22-model_00-model_states.pt\n", "[2023-11-06 20:04:33,184] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_23-model_00-model_states.pt\n", "[2023-11-06 20:04:33,576] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_24-model_00-model_states.pt\n", "[2023-11-06 20:04:33,798] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_25-model_00-model_states.pt\n", "[2023-11-06 20:04:33,800] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_27-model_00-model_states.pt\n", "[2023-11-06 20:04:34,304] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/fima-v2/fima-v2.7-starcoder1B-7langs-1M_batch-500Kfiles_per_lang/global_step6000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["starcoder_1b = get_model(\"starcoderbase_1b_fim_aligned\")\n", "starcoder_1b.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/augment/research/models/meta_gpt_neox_model.py:369: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n"]}, {"data": {"text/plain": ["[[-15.198090553283691, -6.357507705688477]]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["starcoder_1b.log_likelihood_continuation([ModelInput('hello')], ['world world'], reduction='none')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creates `OraclePerplexityReranker` and Returns Per-token Loss."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/augment/research/models/meta_gpt_neox_model.py:369: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n"]}, {"data": {"text/plain": ["[[-8.889805793762207, -4.309062480926514],\n", " [-9.008655548095703, -4.226484775543213],\n", " [-9.219002723693848, -4.323105335235596],\n", " [-8.699868202209473, -4.388906955718994]]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.retrieval.rerankers.oracle_perplexity_reranker import (\n", "    OraclePerplexityReranker,\n", ")\n", "from research.retrieval.types import Chunk, Document\n", "\n", "reranker = OraclePerplexityReranker(starcoder_1b, top_k=16, batchsize=1)\n", "\n", "reranker._score(\n", "    ModelInput(\n", "        \"hello\",\n", "        retrieved_chunks=[\n", "            Chunk(1, \"chunk 1\", Document(0, \"\"), 0, 0, 0, 0),\n", "            Chunk(2, \"chunk 2\", Document(0, \"\"), 0, 0, 0, 0),\n", "            Chunk(3, \"chunk 3\", Document(0, \"\"), 0, 0, 0, 0),\n", "            Chunk(4, \"chunk 4\", Document(0, \"\"), 0, 0, 0, 0),\n", "        ],\n", "    ),\n", "    # Target sequence length is 2.\n", "    \"world world\",\n", "    reduction='none',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Spark Stage to get per-token loss."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["INPUT_PATH = 's3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/'\n", "OUTPUT_PATH = 's3a://augment-temporary/vzhao/test_per_token_loss/'\n", "LANGUAGE_MODEL_CONFIG = {\n", "    \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "    \"name\": \"rogue\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 100,\n", "        \"max_suffix_tokens\": 100,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 750,\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["delete: 's3://augment-temporary/vzhao/test_per_token_loss/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet'\n", "delete: 's3://augment-temporary/vzhao/test_per_token_loss/part-01000-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet'\n", "delete: 's3://augment-temporary/vzhao/test_per_token_loss/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet'\n", "delete: 's3://augment-temporary/vzhao/test_per_token_loss/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet'\n", "delete: 's3://augment-temporary/vzhao/test_per_token_loss/part-01003-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet'\n"]}], "source": ["!s3cmd rm --recursive s3://augment-temporary/vzhao/test_per_token_loss/"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Doing a timing run.  Processing one batch per file (batchsize: 8) and a maximum of 5 files.\n", "23/11/19 08:45:59 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "/mnt/efs/augment/python_env/2023-11-19/vzhao-dev/54264c91-ade5-4ef0-847a-72eafbfde5af/lib/python3.9/site-packages/research/models/meta_gpt_neox_model.py:369: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n", "/mnt/efs/augment/python_env/2023-11-19/vzhao-dev/54264c91-ade5-4ef0-847a-72eafbfde5af/lib/python3.9/site-packages/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "WARNING:research.core.prompt_formatters:No model_input.extra['ground_truth_span'] found: Will not do overlap detection on retrieved chunks.\n", "\n"]}], "source": ["import pandas as pd\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "def compute_ppl(\n", "    prefix,\n", "    suffix,\n", "    middle,\n", "    file_path,\n", "    retrieved_chunks,\n", ") -> pd.Series:\n", "    import datetime\n", "    import json\n", "\n", "    from experimental.vzhao.data import common\n", "    from research.eval.harness.factories import create_model, create_reranker\n", "\n", "    def print_with_time(*args):\n", "        print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "    global cached_reranker\n", "    if \"cached_reranker\" not in globals():\n", "        # Construct a reranker\n", "        print_with_time(\"Constructing the model...\")\n", "        model = create_model(LANGUAGE_MODEL_CONFIG)\n", "\n", "        print_with_time(\"Constructing the reranker...\")\n", "        cached_reranker = create_reranker(\n", "            model,\n", "            config={\n", "                \"name\": \"oracle_perplexity_reranker\",\n", "                \"top_k\": 256,\n", "                \"batchsize\": 4,\n", "            },\n", "        )\n", "\n", "        # Load the reranking model\n", "        print_with_time(\"Loading the model...\")\n", "        model.load()\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),\n", "        path=file_path,\n", "    )\n", "\n", "    print_with_time(f\"Reranking...\")\n", "    # NOTE: Set `reduction='none'` to return per-token losses/scores.\n", "    scores = cached_reranker._score(model_input, middle, reduction='none')\n", "    assert len(scores) == len(\n", "        common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ), f\"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}\"\n", "    return pd.Series({\"ppl\": json.dumps(scores)})\n", "\n", "\n", "def stage5a():\n", "    spark_gpu = k8s_session(\n", "        max_workers=8,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"A40\",\n", "    )\n", "    files = map_parquet.list_files(\n", "        spark_gpu, INPUT_PATH, suffix=\"parquet\", include_path=False\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors([compute_ppl]),\n", "        input_path=INPUT_PATH,\n", "        output_path=OUTPUT_PATH,\n", "        timing_run=True,\n", "        batch_size=8,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5a()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of chunks: 127\n", "#tokens: [43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43]\n"]}], "source": ["# Sanity check the output parquet files.\n", "import json\n", "import os\n", "\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.spark import get_session\n", "\n", "\n", "spark = get_session()\n", "files = [\n", "    os.path.join(OUTPUT_PATH, f)\n", "    for f in map_parquet.list_files(\n", "        spark, OUTPUT_PATH, suffix=\"parquet\", include_path=False\n", "    )\n", "]\n", "df = spark.read.parquet(files[0])\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "df.head()\n", "\n", "\n", "scores = json.loads(df[\"ppl\"][0])\n", "print(f\"Number of chunks: {len(scores)}\")\n", "print(f\"#tokens: {[len(s) for s in scores]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Debug"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eval harness is not available (No module named 'lm_eval').\n", "Creating model with config: {'checkpoint_path': 'rogue/diffb1m_1b_alphal_fixtoken', 'name': 'rogue', 'prompt': {'max_prefix_tokens': 100, 'max_suffix_tokens': 100, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 750}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> initializing torch distributed ...\n", "[2023-11-19 07:55:30,454] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-11-19 07:55:30,804] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=*************, master_port=6000\n", "[2023-11-19 07:55:30,807] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-11-19 07:55:30,845] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[vzhao-dev:950484] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> building StarCoderTokenizer tokenizer ...\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", " > padded vocab (size: 49165) with 2035 dummy tokens (new size: 51200)\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-11-19 07:55:31,315] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ParallelLinearPipe\n", "  loss: partial\n", "Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    0.position_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.key_value.weight requires_grad=True\n", "    2.attention.key_value.bias requires_grad=True\n", "    2.attention.query.weight requires_grad=True\n", "    2.attention.query.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.post_attention_layernorm.weight requires_grad=True\n", "    2.post_attention_layernorm.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.key_value.weight requires_grad=True\n", "    3.attention.key_value.bias requires_grad=True\n", "    3.attention.query.weight requires_grad=True\n", "    3.attention.query.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.post_attention_layernorm.weight requires_grad=True\n", "    3.post_attention_layernorm.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.key_value.weight requires_grad=True\n", "    4.attention.key_value.bias requires_grad=True\n", "    4.attention.query.weight requires_grad=True\n", "    4.attention.query.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.post_attention_layernorm.weight requires_grad=True\n", "    4.post_attention_layernorm.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.key_value.weight requires_grad=True\n", "    5.attention.key_value.bias requires_grad=True\n", "    5.attention.query.weight requires_grad=True\n", "    5.attention.query.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.post_attention_layernorm.weight requires_grad=True\n", "    5.post_attention_layernorm.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.key_value.weight requires_grad=True\n", "    6.attention.key_value.bias requires_grad=True\n", "    6.attention.query.weight requires_grad=True\n", "    6.attention.query.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.post_attention_layernorm.weight requires_grad=True\n", "    6.post_attention_layernorm.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.key_value.weight requires_grad=True\n", "    7.attention.key_value.bias requires_grad=True\n", "    7.attention.query.weight requires_grad=True\n", "    7.attention.query.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.post_attention_layernorm.weight requires_grad=True\n", "    7.post_attention_layernorm.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.key_value.weight requires_grad=True\n", "    8.attention.key_value.bias requires_grad=True\n", "    8.attention.query.weight requires_grad=True\n", "    8.attention.query.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.post_attention_layernorm.weight requires_grad=True\n", "    8.post_attention_layernorm.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.key_value.weight requires_grad=True\n", "    9.attention.key_value.bias requires_grad=True\n", "    9.attention.query.weight requires_grad=True\n", "    9.attention.query.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.post_attention_layernorm.weight requires_grad=True\n", "    9.post_attention_layernorm.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.key_value.weight requires_grad=True\n", "    10.attention.key_value.bias requires_grad=True\n", "    10.attention.query.weight requires_grad=True\n", "    10.attention.query.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.post_attention_layernorm.weight requires_grad=True\n", "    10.post_attention_layernorm.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.key_value.weight requires_grad=True\n", "    11.attention.key_value.bias requires_grad=True\n", "    11.attention.query.weight requires_grad=True\n", "    11.attention.query.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.post_attention_layernorm.weight requires_grad=True\n", "    11.post_attention_layernorm.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.key_value.weight requires_grad=True\n", "    12.attention.key_value.bias requires_grad=True\n", "    12.attention.query.weight requires_grad=True\n", "    12.attention.query.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.post_attention_layernorm.weight requires_grad=True\n", "    12.post_attention_layernorm.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.key_value.weight requires_grad=True\n", "    13.attention.key_value.bias requires_grad=True\n", "    13.attention.query.weight requires_grad=True\n", "    13.attention.query.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.post_attention_layernorm.weight requires_grad=True\n", "    13.post_attention_layernorm.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    14.input_layernorm.weight requires_grad=True\n", "    14.input_layernorm.bias requires_grad=True\n", "    14.attention.key_value.weight requires_grad=True\n", "    14.attention.key_value.bias requires_grad=True\n", "    14.attention.query.weight requires_grad=True\n", "    14.attention.query.bias requires_grad=True\n", "    14.attention.dense.weight requires_grad=True\n", "    14.attention.dense.bias requires_grad=True\n", "    14.post_attention_layernorm.weight requires_grad=True\n", "    14.post_attention_layernorm.bias requires_grad=True\n", "    14.mlp.dense_h_to_4h.weight requires_grad=True\n", "    14.mlp.dense_h_to_4h.bias requires_grad=True\n", "    14.mlp.dense_4h_to_h.weight requires_grad=True\n", "    14.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.input_layernorm.weight requires_grad=True\n", "    15.input_layernorm.bias requires_grad=True\n", "    15.attention.key_value.weight requires_grad=True\n", "    15.attention.key_value.bias requires_grad=True\n", "    15.attention.query.weight requires_grad=True\n", "    15.attention.query.bias requires_grad=True\n", "    15.attention.dense.weight requires_grad=True\n", "    15.attention.dense.bias requires_grad=True\n", "    15.post_attention_layernorm.weight requires_grad=True\n", "    15.post_attention_layernorm.bias requires_grad=True\n", "    15.mlp.dense_h_to_4h.weight requires_grad=True\n", "    15.mlp.dense_h_to_4h.bias requires_grad=True\n", "    15.mlp.dense_4h_to_h.weight requires_grad=True\n", "    15.mlp.dense_4h_to_h.bias requires_grad=True\n", "    16.input_layernorm.weight requires_grad=True\n", "    16.input_layernorm.bias requires_grad=True\n", "    16.attention.key_value.weight requires_grad=True\n", "    16.attention.key_value.bias requires_grad=True\n", "    16.attention.query.weight requires_grad=True\n", "    16.attention.query.bias requires_grad=True\n", "    16.attention.dense.weight requires_grad=True\n", "    16.attention.dense.bias requires_grad=True\n", "    16.post_attention_layernorm.weight requires_grad=True\n", "    16.post_attention_layernorm.bias requires_grad=True\n", "    16.mlp.dense_h_to_4h.weight requires_grad=True\n", "    16.mlp.dense_h_to_4h.bias requires_grad=True\n", "    16.mlp.dense_4h_to_h.weight requires_grad=True\n", "    16.mlp.dense_4h_to_h.bias requires_grad=True\n", "    17.input_layernorm.weight requires_grad=True\n", "    17.input_layernorm.bias requires_grad=True\n", "    17.attention.key_value.weight requires_grad=True\n", "    17.attention.key_value.bias requires_grad=True\n", "    17.attention.query.weight requires_grad=True\n", "    17.attention.query.bias requires_grad=True\n", "    17.attention.dense.weight requires_grad=True\n", "    17.attention.dense.bias requires_grad=True\n", "    17.post_attention_layernorm.weight requires_grad=True\n", "    17.post_attention_layernorm.bias requires_grad=True\n", "    17.mlp.dense_h_to_4h.weight requires_grad=True\n", "    17.mlp.dense_h_to_4h.bias requires_grad=True\n", "    17.mlp.dense_4h_to_h.weight requires_grad=True\n", "    17.mlp.dense_4h_to_h.bias requires_grad=True\n", "    18.input_layernorm.weight requires_grad=True\n", "    18.input_layernorm.bias requires_grad=True\n", "    18.attention.key_value.weight requires_grad=True\n", "    18.attention.key_value.bias requires_grad=True\n", "    18.attention.query.weight requires_grad=True\n", "    18.attention.query.bias requires_grad=True\n", "    18.attention.dense.weight requires_grad=True\n", "    18.attention.dense.bias requires_grad=True\n", "    18.post_attention_layernorm.weight requires_grad=True\n", "    18.post_attention_layernorm.bias requires_grad=True\n", "    18.mlp.dense_h_to_4h.weight requires_grad=True\n", "    18.mlp.dense_h_to_4h.bias requires_grad=True\n", "    18.mlp.dense_4h_to_h.weight requires_grad=True\n", "    18.mlp.dense_4h_to_h.bias requires_grad=True\n", "    19.input_layernorm.weight requires_grad=True\n", "    19.input_layernorm.bias requires_grad=True\n", "    19.attention.key_value.weight requires_grad=True\n", "    19.attention.key_value.bias requires_grad=True\n", "    19.attention.query.weight requires_grad=True\n", "    19.attention.query.bias requires_grad=True\n", "    19.attention.dense.weight requires_grad=True\n", "    19.attention.dense.bias requires_grad=True\n", "    19.post_attention_layernorm.weight requires_grad=True\n", "    19.post_attention_layernorm.bias requires_grad=True\n", "    19.mlp.dense_h_to_4h.weight requires_grad=True\n", "    19.mlp.dense_h_to_4h.bias requires_grad=True\n", "    19.mlp.dense_4h_to_h.weight requires_grad=True\n", "    19.mlp.dense_4h_to_h.bias requires_grad=True\n", "    20.input_layernorm.weight requires_grad=True\n", "    20.input_layernorm.bias requires_grad=True\n", "    20.attention.key_value.weight requires_grad=True\n", "    20.attention.key_value.bias requires_grad=True\n", "    20.attention.query.weight requires_grad=True\n", "    20.attention.query.bias requires_grad=True\n", "    20.attention.dense.weight requires_grad=True\n", "    20.attention.dense.bias requires_grad=True\n", "    20.post_attention_layernorm.weight requires_grad=True\n", "    20.post_attention_layernorm.bias requires_grad=True\n", "    20.mlp.dense_h_to_4h.weight requires_grad=True\n", "    20.mlp.dense_h_to_4h.bias requires_grad=True\n", "    20.mlp.dense_4h_to_h.weight requires_grad=True\n", "    20.mlp.dense_4h_to_h.bias requires_grad=True\n", "    21.input_layernorm.weight requires_grad=True\n", "    21.input_layernorm.bias requires_grad=True\n", "    21.attention.key_value.weight requires_grad=True\n", "    21.attention.key_value.bias requires_grad=True\n", "    21.attention.query.weight requires_grad=True\n", "    21.attention.query.bias requires_grad=True\n", "    21.attention.dense.weight requires_grad=True\n", "    21.attention.dense.bias requires_grad=True\n", "    21.post_attention_layernorm.weight requires_grad=True\n", "    21.post_attention_layernorm.bias requires_grad=True\n", "    21.mlp.dense_h_to_4h.weight requires_grad=True\n", "    21.mlp.dense_h_to_4h.bias requires_grad=True\n", "    21.mlp.dense_4h_to_h.weight requires_grad=True\n", "    21.mlp.dense_4h_to_h.bias requires_grad=True\n", "    22.input_layernorm.weight requires_grad=True\n", "    22.input_layernorm.bias requires_grad=True\n", "    22.attention.key_value.weight requires_grad=True\n", "    22.attention.key_value.bias requires_grad=True\n", "    22.attention.query.weight requires_grad=True\n", "    22.attention.query.bias requires_grad=True\n", "    22.attention.dense.weight requires_grad=True\n", "    22.attention.dense.bias requires_grad=True\n", "    22.post_attention_layernorm.weight requires_grad=True\n", "    22.post_attention_layernorm.bias requires_grad=True\n", "    22.mlp.dense_h_to_4h.weight requires_grad=True\n", "    22.mlp.dense_h_to_4h.bias requires_grad=True\n", "    22.mlp.dense_4h_to_h.weight requires_grad=True\n", "    22.mlp.dense_4h_to_h.bias requires_grad=True\n", "    23.input_layernorm.weight requires_grad=True\n", "    23.input_layernorm.bias requires_grad=True\n", "    23.attention.key_value.weight requires_grad=True\n", "    23.attention.key_value.bias requires_grad=True\n", "    23.attention.query.weight requires_grad=True\n", "    23.attention.query.bias requires_grad=True\n", "    23.attention.dense.weight requires_grad=True\n", "    23.attention.dense.bias requires_grad=True\n", "    23.post_attention_layernorm.weight requires_grad=True\n", "    23.post_attention_layernorm.bias requires_grad=True\n", "    23.mlp.dense_h_to_4h.weight requires_grad=True\n", "    23.mlp.dense_h_to_4h.bias requires_grad=True\n", "    23.mlp.dense_4h_to_h.weight requires_grad=True\n", "    23.mlp.dense_4h_to_h.bias requires_grad=True\n", "    24.input_layernorm.weight requires_grad=True\n", "    24.input_layernorm.bias requires_grad=True\n", "    24.attention.key_value.weight requires_grad=True\n", "    24.attention.key_value.bias requires_grad=True\n", "    24.attention.query.weight requires_grad=True\n", "    24.attention.query.bias requires_grad=True\n", "    24.attention.dense.weight requires_grad=True\n", "    24.attention.dense.bias requires_grad=True\n", "    24.post_attention_layernorm.weight requires_grad=True\n", "    24.post_attention_layernorm.bias requires_grad=True\n", "    24.mlp.dense_h_to_4h.weight requires_grad=True\n", "    24.mlp.dense_h_to_4h.bias requires_grad=True\n", "    24.mlp.dense_4h_to_h.weight requires_grad=True\n", "    24.mlp.dense_4h_to_h.bias requires_grad=True\n", "    25.input_layernorm.weight requires_grad=True\n", "    25.input_layernorm.bias requires_grad=True\n", "    25.attention.key_value.weight requires_grad=True\n", "    25.attention.key_value.bias requires_grad=True\n", "    25.attention.query.weight requires_grad=True\n", "    25.attention.query.bias requires_grad=True\n", "    25.attention.dense.weight requires_grad=True\n", "    25.attention.dense.bias requires_grad=True\n", "    25.post_attention_layernorm.weight requires_grad=True\n", "    25.post_attention_layernorm.bias requires_grad=True\n", "    25.mlp.dense_h_to_4h.weight requires_grad=True\n", "    25.mlp.dense_h_to_4h.bias requires_grad=True\n", "    25.mlp.dense_4h_to_h.weight requires_grad=True\n", "    25.mlp.dense_4h_to_h.bias requires_grad=True\n", "    27.norm.weight requires_grad=True\n", "    27.norm.bias requires_grad=True\n", "    28.final_linear.weight requires_grad=True\n", "DeepSpeed is enabled.\n", "[2023-11-19 07:55:34,409] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+d08ec4e, git-hash=d08ec4e, git-branch=HEAD\n", "[2023-11-19 07:55:34,411] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-11-19 07:55:34,489] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-11-19 07:55:34,492] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-11-19 07:55:34,493] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-11-19 07:55:34,494] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-11-19 07:55:34,495] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-11-19 07:55:34,497] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-11-19 07:55:34,500] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-11-19 07:55:34,502] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-11-19 07:55:34,506] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-11-19 07:55:34,507] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-11-19 07:55:34,508] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-11-19 07:55:34,509] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-11-19 07:55:34,511] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-11-19 07:55:34,519] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-11-19 07:55:34,529] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-11-19 07:55:34,544] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-11-19 07:55:34,564] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-11-19 07:55:34,565] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-11-19 07:55:34,567] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-11-19 07:55:34,568] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-11-19 07:55:34,570] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-11-19 07:55:34,574] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-11-19 07:55:34,575] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-11-19 07:55:34,577] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2023-11-19 07:55:34,578] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': '_ 0.9 _ 0.95', 'eps': 1e-08, 'lr': 5e-05}\n", "[2023-11-19 07:55:34,580] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-11-19 07:55:34,581] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-11-19 07:55:34,583] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-11-19 07:55:34,583] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-11-19 07:55:34,584] [INFO] [config.py:763:print]   prescale_gradients ........... <PERSON>alse\n", "[2023-11-19 07:55:34,585] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-11-19 07:55:34,585] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-11-19 07:55:34,586] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-11-19 07:55:34,589] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-11-19 07:55:34,599] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-11-19 07:55:34,600] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-11-19 07:55:34,601] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-11-19 07:55:34,605] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-11-19 07:55:34,606] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-11-19 07:55:34,610] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-11-19 07:55:34,613] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-11-19 07:55:34,614] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-11-19 07:55:34,615] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-11-19 07:55:34,616] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-11-19 07:55:34,617] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-11-19 07:55:34,618] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-11-19 07:55:34,619] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": \"_ 0.9 _ 0.95\", \n", "            \"eps\": 1e-08, \n", "            \"lr\": 5e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n", "[2023-11-19 07:55:34,624] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-11-19 07:55:34,655] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1246259201 (1246.259M) TOTAL_PARAMS=1246259201 (1246.259M) UNIQUE_PARAMS=1246259201 (1246.259M)\n", " > number of parameters on model parallel rank 0: 1246259201\n", " > total params: 1,246,259,201\n", " > embedding params: 209,715,200\n", "Loading: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken\n", "[2023-11-19 07:55:34,702] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/mp_rank_00_model_states.pt\n", "[2023-11-19 07:55:35,455] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_00-model_00-model_states.pt\n", "[2023-11-19 07:55:35,906] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_02-model_00-model_states.pt\n", "[2023-11-19 07:55:36,232] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_03-model_00-model_states.pt\n", "[2023-11-19 07:55:36,449] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_04-model_00-model_states.pt\n", "[2023-11-19 07:55:36,807] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_05-model_00-model_states.pt\n", "[2023-11-19 07:55:37,250] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_06-model_00-model_states.pt\n", "[2023-11-19 07:55:37,670] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_07-model_00-model_states.pt\n", "[2023-11-19 07:55:37,903] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_08-model_00-model_states.pt\n", "[2023-11-19 07:55:38,292] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_09-model_00-model_states.pt\n", "[2023-11-19 07:55:38,557] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_10-model_00-model_states.pt\n", "[2023-11-19 07:55:38,792] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_11-model_00-model_states.pt\n", "[2023-11-19 07:55:39,054] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_12-model_00-model_states.pt\n", "[2023-11-19 07:55:39,323] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_13-model_00-model_states.pt\n", "[2023-11-19 07:55:39,720] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_14-model_00-model_states.pt\n", "[2023-11-19 07:55:39,980] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_15-model_00-model_states.pt\n", "[2023-11-19 07:55:40,376] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_16-model_00-model_states.pt\n", "[2023-11-19 07:55:40,802] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_17-model_00-model_states.pt\n", "[2023-11-19 07:55:41,171] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_18-model_00-model_states.pt\n", "[2023-11-19 07:55:41,538] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_19-model_00-model_states.pt\n", "[2023-11-19 07:55:41,847] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_20-model_00-model_states.pt\n", "[2023-11-19 07:55:42,112] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_21-model_00-model_states.pt\n", "[2023-11-19 07:55:42,465] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_22-model_00-model_states.pt\n", "[2023-11-19 07:55:42,866] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_23-model_00-model_states.pt\n", "[2023-11-19 07:55:43,054] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_24-model_00-model_states.pt\n", "[2023-11-19 07:55:43,388] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_25-model_00-model_states.pt\n", "[2023-11-19 07:55:43,393] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_27-model_00-model_states.pt\n", "[2023-11-19 07:55:44,073] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/layer_28-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken/global_step1000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Process Process-1:\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/multiprocessing/process.py\", line 315, in _bootstrap\n", "    self.run()\n", "  File \"/opt/conda/lib/python3.9/multiprocessing/process.py\", line 108, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"/tmp/ipykernel_948833/2251269059.py\", line 20, in func\n", "    model.generate(\"def hello():\")\n", "TypeError: generate() missing 1 required positional argument: 'options'\n"]}], "source": ["from multiprocessing import Process\n", "# from research.models import fastforward_models\n", "# from research.eval.harness.factories import create_model\n", "\n", "\n", "def func():\n", "    from research.eval.harness.factories import create_model\n", "    LANGUAGE_MODEL_CONFIG = {\n", "        \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "        \"name\": \"rogue\",\n", "        \"prompt\": {\n", "            \"max_prefix_tokens\": 100,\n", "            \"max_suffix_tokens\": 100,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_prompt_tokens\": 750,\n", "        },\n", "    }\n", "    model = create_model(LANGUAGE_MODEL_CONFIG)\n", "    model.load()\n", "    model.generate(\"def hello():\")\n", "\n", "\n", "p = Process(target=func)\n", "p.start()\n", "p.join()\n", "p.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NOTE: Remove `token.json` to re-authentic.\n", "\n", "import os.path\n", "\n", "from google.auth.transport.requests import Request\n", "from google.oauth2.credentials import Credentials\n", "from google_auth_oauthlib.flow import InstalledAppFlow\n", "from googleapiclient.discovery import build\n", "from googleapiclient.errors import HttpError\n", "\n", "# If modifying these scopes, delete the file token.json.\n", "SCOPES = [\"https://www.googleapis.com/auth/spreadsheets\"]\n", "\n", "creds = None\n", "if os.path.exists(\"token.json\"):\n", "    creds = Credentials.from_authorized_user_file(\"token.json\", SCOPES)\n", "if not creds or not creds.valid:\n", "    if creds and creds.expired and creds.refresh_token:\n", "        creds.refresh(Request())\n", "    else:\n", "        flow = InstalledAppFlow.from_client_secrets_file(\"/home/<USER>/.gcp/credentials.json\", SCOPES)\n", "        creds = flow.run_local_server(port=8080)\n", "    # Save the credentials for the next run\n", "    with open(\"token.json\", \"w\") as token:\n", "        token.write(creds.to_json())"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["service = build(\"sheets\", \"v4\", credentials=creds)\n", "sheet = service.spreadsheets()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creates a sheet"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["spreadsheet_id = \"1rv3pirCmTdlBvfrJfy_eCikGihxxusRO5nU9_Bd_Hf0\"\n", "new_sheet_title = \"test\"\n", "\n", "batch_update_spreadsheet_request_body = {\n", "    \"requests\": [\n", "        {\n", "            \"addSheet\": {\n", "                \"properties\": {\n", "                    \"title\": new_sheet_title,\n", "                }\n", "            }\n", "        }\n", "    ]\n", "}\n", "\n", "request = sheet.batchUpdate(\n", "    spreadsheetId=spreadsheet_id, body=batch_update_spreadsheet_request_body\n", ").execute()\n", "\n", "print(f\"Added new sheet with title: {new_sheet_title}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Write to a single range"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets import google_sheet_utils"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["google_sheet_utils.update_values(service, spreadsheet_id, \"test!A1:B\", \"USER_ENTERED\", [[\"A\", \"B\"], [\"C\", \"D\"]])\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["google_sheet_utils.update_row(service, spreadsheet_id, \"test\", 5, [\"A\", \"B\", \"C\", \"D\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
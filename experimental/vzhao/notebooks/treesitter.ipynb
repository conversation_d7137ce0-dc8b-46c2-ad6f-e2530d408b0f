{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from augment.research.static_analysis import parsing\n", "from augment.research.static_analysis import usage_analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads a file from repo."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from augment.research.eval.harness.tasks.hydra_task import download_hydra\n", "\n", "docker_imgs_path, patches_by_repo, files_for_retrieval_by_repo = download_hydra(\"repoeval_2-3lines\")\n", "\n", "# files_for_retrieval_by_repo.keys()\n", "k = ('amazon-science', 'patchcore-inspection')\n", "files_for_retrieval_by_repo[k][0]\n", "\n", "docs = files_for_retrieval_by_repo[k]\n", "patches = {patch.patch_id: patch for patch in patches_by_repo[('amazon-science', 'patchcore-inspection')]}"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["'test/test_patchcore.py'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["patch_id = 'amazon-science/patchcore-inspection/3d7c20bcb10c9362'\n", "patch = patches[patch_id]\n", "\n", "patch.file_name"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(id='48542719fc324abcaf625a76d98189477e82776658622b5c3d1ccff88064bc71', text='import numpy as np\\nimport torch.utils.data\\nfrom torchvision import models\\n\\nimport patchcore\\nfrom patchcore import patchcore as patchcore_model\\n\\n\\ndef _dummy_features(number_of_examples, shape_of_examples):\\n    return torch.Tensor(\\n        np.stack(number_of_examples * [np.ones(shape_of_examples)], axis=0)\\n    )\\n\\n\\ndef _dummy_constant_dataloader(number_of_examples, shape_of_examples):\\n    features = _dummy_features(number_of_examples, shape_of_examples)\\n    return torch.utils.data.DataLoader(features, batch_size=1)\\n\\n\\ndef _dummy_various_features(number_of_examples, shape_of_examples):\\n    images = torch.ones((number_of_examples, *shape_of_examples))\\n    multiplier = torch.arange(number_of_examples) / float(number_of_examples)\\n    for _ in range(images.ndim - 1):\\n        multiplier = multiplier.unsqueeze(-1)\\n    return multiplier * images\\n\\n\\ndef _dummy_various_dataloader(number_of_examples, shape_of_examples):\\n    features = _dummy_various_features(number_of_examples, shape_of_examples)\\n    return torch.utils.data.DataLoader(features, batch_size=1)\\n\\n\\ndef _dummy_images(number_of_examples, image_shape):\\n    torch.manual_seed(0)\\n    return torch.rand([number_of_examples, *image_shape])\\n\\n\\ndef _dummy_image_random_dataloader(number_of_examples, image_shape):\\n    images = _dummy_images(number_of_examples, image_shape)\\n    return torch.utils.data.DataLoader(images, batch_size=4)\\n\\n\\ndef _standard_patchcore(image_dimension):\\n    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\\n    backbone = models.wide_resnet50_2(pretrained=False)\\n    backbone.name, backbone.seed = \"wideresnet50\", 0\\n    patchcore_instance.load(\\n        backbone=backbone,\\n        layers_to_extract_from=[\"layer2\", \"layer3\"],\\n        device=torch.device(\"cpu\"),\\n        input_shape=[3, image_dimension, image_dimension],\\n        pretrain_embed_dimension=1024,\\n        target_embed_dimension=1024,\\n        patchsize=3,\\n        patchstride=1,\\n        spade_nn=2,\\n    )\\n    return patchcore_instance\\n\\n\\ndef _load_patchcore_from_path(load_path):\\n    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\\n    patchcore_instance.load_from_path(\\n        load_path=load_path,\\n        device=torch.device(\"cpu\"),\\n        prepend=\"temp_patchcore\",\\n        nn_method=patchcore.common.FaissNN(False, 4),\\n    )\\n    return patchcore_instance\\n\\n\\ndef _approximate_greedycoreset_sampler_with_reduction(\\n    sampling_percentage, johnsonlindenstrauss_dim\\n):\\n    return patchcore.sampler.ApproximateGreedyCoresetSampler(\\n        percentage=sampling_percentage,\\n        device=torch.device(\"cpu\"),\\n        number_of_starting_points=10,\\n        dimension_to_project_features_to=johnsonlindenstrauss_dim,\\n    )\\n\\n\\ndef test_dummy_patchcore():\\n    image_dimension = 112\\n    model = _standard_patchcore(image_dimension)\\n    training_dataloader = _dummy_constant_dataloader(\\n        4, [3, image_dimension, image_dimension]\\n    )\\n    print(model.featuresampler)\\n    model.fit(training_dataloader)\\n\\n    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\\n    scores, masks = model.predict(test_features)\\n\\n    assert all([score > 0 for score in scores])\\n    for mask in masks:\\n        assert np.all(mask.shape == (image_dimension, image_dimension))\\n\\n\\ndef test_patchcore_on_dataloader():\\n    \"\"\"Test PatchCore on dataloader and assure training scores are zero.\"\"\"\\n    image_dimension = 112\\n    model = _standard_patchcore(image_dimension)\\n\\n    training_dataloader = _dummy_constant_dataloader(\\n        4, [3, image_dimension, image_dimension]\\n    )\\n    model.fit(training_dataloader)\\n    scores, masks, labels_gt, masks_gt = model.predict(training_dataloader)\\n\\n    assert all([score < 1e-3 for score in scores])\\n    for mask, mask_gt in zip(masks, masks_gt):\\n        assert np.all(mask.shape == (image_dimension, image_dimension))\\n        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\\n\\n\\ndef test_patchcore_load_and_saveing(tmpdir):\\n    image_dimension = 112\\n    model = _standard_patchcore(image_dimension)\\n\\n    training_dataloader = _dummy_constant_dataloader(\\n        4, [3, image_dimension, image_dimension]\\n    )\\n    model.fit(training_dataloader)\\n    model.save_to_path(tmpdir, \"temp_patchcore\")\\n\\n    test_features = torch.Tensor(\\n        1.234 * np.ones([2, 3, image_dimension, image_dimension])\\n    )\\n    scores, masks = model.predict(test_features)\\n    other_scores, other_masks = model.predict(test_features)\\n\\n    assert np.all(scores == other_scores)\\n    for mask, other_mask in zip(masks, other_masks):\\n        assert np.all(mask == other_mask)\\n\\n\\ndef test_patchcore_real_data():\\n    image_dimension = 112\\n    sampling_percentage = 0.1\\n    model = _standard_patchcore(image_dimension)\\n    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\\n        sampling_percentage=sampling_percentage,\\n        johnsonlindenstrauss_dim=64,\\n    )\\n\\n    num_dummy_train_images = 50\\n    training_dataloader = _dummy_various_dataloader(\\n        num_dummy_train_images, [3, image_dimension, image_dimension]\\n    )\\n    model.fit(training_dataloader)\\n\\n    num_dummy_test_images = 5\\n    test_dataloader = _dummy_various_dataloader(\\n        num_dummy_test_images, [3, image_dimension, image_dimension]\\n    )\\n    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\\n\\n    for mask, mask_gt in zip(masks, masks_gt):\\n        assert np.all(mask.shape == (image_dimension, image_dimension))\\n        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\\n\\n    assert len(scores) == 5\\n', path='test/test_patchcore.py', meta={'repo_identifier': ('amazon-science', 'patchcore-inspection')})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["for doc in docs:\n", "    if doc.path == patch.file_name:\n", "        break\n", "doc"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["import tree_sitter as ts\n", "import dataclasses"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["def print_children(node, type='text'):\n", "    for c in node.children:\n", "        attr = getattr(c, type)\n", "        if isinstance(attr, bytes):\n", "            attr = attr.decode()\n", "        print(attr)\n", "        print('----')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["parsed_file = usage_analysis.ParsedFile.parse_document(doc)\n", "ts_tree = parsed_file.ts_tree\n", "root_node = ts_tree.root_node"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Plan\n", "\n", "- import node: create a Node, no predecessor, add to key global variable\n", "- function defintion: create a Node, no predecessor, add to global variable\n", "- assignment: create a Node left to variable\n", "- \n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import numpy as np\n", "import torch.utils.data\n", "from torchvision import models\n", "\n", "import patchcore\n", "from patchcore import patchcore as patchcore_model\n", "\n", "\n", "def _dummy_features(number_of_examples, shape_of_examples):\n", "    return torch.Tensor(\n", "        np.stack(number_of_examples * [np.ones(shape_of_examples)], axis=0)\n", "    )\n", "\n", "\n", "def _dummy_constant_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_various_features(number_of_examples, shape_of_examples):\n", "    images = torch.ones((number_of_examples, *shape_of_examples))\n", "    multiplier = torch.arange(number_of_examples) / float(number_of_examples)\n", "    for _ in range(images.ndim - 1):\n", "        multiplier = multiplier.unsqueeze(-1)\n", "    return multiplier * images\n", "\n", "\n", "def _dummy_various_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_various_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_images(number_of_examples, image_shape):\n", "    torch.manual_seed(0)\n", "    return torch.rand([number_of_examples, *image_shape])\n", "\n", "\n", "def _dummy_image_random_dataloader(number_of_examples, image_shape):\n", "    images = _dummy_images(number_of_examples, image_shape)\n", "    return torch.utils.data.DataLoader(images, batch_size=4)\n", "\n", "\n", "def _standard_patchcore(image_dimension):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    backbone = models.wide_resnet50_2(pretrained=False)\n", "    backbone.name, backbone.seed = \"wideresnet50\", 0\n", "    patchcore_instance.load(\n", "        backbone=backbone,\n", "        layers_to_extract_from=[\"layer2\", \"layer3\"],\n", "        device=torch.device(\"cpu\"),\n", "        input_shape=[3, image_dimension, image_dimension],\n", "        pretrain_embed_dimension=1024,\n", "        target_embed_dimension=1024,\n", "        patchsize=3,\n", "        patchstride=1,\n", "        spade_nn=2,\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _load_patchcore_from_path(load_path):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    patchcore_instance.load_from_path(\n", "        load_path=load_path,\n", "        device=torch.device(\"cpu\"),\n", "        prepend=\"temp_patchcore\",\n", "        nn_method=patchcore.common.FaissNN(False, 4),\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _approximate_greedycoreset_sampler_with_reduction(\n", "    sampling_percentage, joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim\n", "):\n", "    return patchcore.sampler.ApproximateGreedyCoresetSampler(\n", "        percentage=sampling_percentage,\n", "        device=torch.device(\"cpu\"),\n", "        number_of_starting_points=10,\n", "        dimension_to_project_features_to=joh<PERSON><PERSON><PERSON><PERSON>uss_dim,\n", "    )\n", "\n", "\n", "def test_dummy_patchcore():\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    print(model.featuresampler)\n", "    model.fit(training_dataloader)\n", "\n", "    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\n", "    scores, masks = model.predict(test_features)\n", "\n", "    assert all([score > 0 for score in scores])\n", "    for mask in masks:\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_on_dataloader():\n", "    \"\"\"Test PatchCore on dataloader and assure training scores are zero.\"\"\"\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    scores, masks, labels_gt, masks_gt = model.predict(training_dataloader)\n", "\n", "    assert all([score < 1e-3 for score in scores])\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_load_and_saveing(tmpdir):\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    model.save_to_path(tmpdir, \"temp_patchcore\")\n", "\n", "    test_features = torch.Tensor(\n", "        1.234 * np.ones([2, 3, image_dimension, image_dimension])\n", "    )\n", "    scores, masks = model.predict(test_features)\n", "    other_scores, other_masks = model.predict(test_features)\n", "\n", "    assert np.all(scores == other_scores)\n", "    for mask, other_mask in zip(masks, other_masks):\n", "        assert np.all(mask == other_mask)\n", "\n", "\n", "def test_patchcore_real_data():\n", "    image_dimension = 112\n", "    sampling_percentage = 0.1\n", "    model = _standard_patchcore(image_dimension)\n", "    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\n", "        sampling_percentage=sampling_percentage,\n", "        joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim=64,\n", "    )\n", "\n", "    num_dummy_train_images = 50\n", "    training_dataloader = _dummy_various_dataloader(\n", "        num_dummy_train_images, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "\n", "    num_dummy_test_images = 5\n", "    test_dataloader = _dummy_various_dataloader(\n", "        num_dummy_test_images, [3, image_dimension, image_dimension]\n", "    )\n", "    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\n", "\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "    assert len(scores) == 5\n", "\n"]}], "source": []}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["tree_sitter.Node"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["type(root_node)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["from typing import Union\n", "\n", "@dataclasses.dataclass\n", "class Node:\n", "    text: str\n", "    ts_node: ts.<PERSON>de\n", "    children: list[\"Node\"] = dataclasses.field(default_factory=list)\n", "    parent: Union[\"Node\", None] = None\n", "\n", "Node.ts_nod\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def node_from_import_statement(ts_node):\n", "    return Node(\n", "        text=ts_node.text,\n", "        ts_node=ts_node,\n", "        \n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For node lookup.\n", "nodes: dict[str, Node] = {}    "]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import numpy as np\n", "import torch.utils.data\n", "from torchvision import models\n", "\n", "import patchcore\n", "from patchcore import patchcore as patchcore_model\n", "\n", "\n", "def _dummy_features(number_of_examples, shape_of_examples):\n", "    return torch.Tensor(\n", "        np.stack(number_of_examples * [np.ones(shape_of_examples)], axis=0)\n", "    )\n", "\n", "\n", "def _dummy_constant_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_various_features(number_of_examples, shape_of_examples):\n", "    images = torch.ones((number_of_examples, *shape_of_examples))\n", "    multiplier = torch.arange(number_of_examples) / float(number_of_examples)\n", "    for _ in range(images.ndim - 1):\n", "        multiplier = multiplier.unsqueeze(-1)\n", "    return multiplier * images\n", "\n", "\n", "def _dummy_various_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_various_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "\n", "\n", "def _dummy_images(number_of_examples, image_shape):\n", "    torch.manual_seed(0)\n", "    return torch.rand([number_of_examples, *image_shape])\n", "\n", "\n", "def _dummy_image_random_dataloader(number_of_examples, image_shape):\n", "    images = _dummy_images(number_of_examples, image_shape)\n", "    return torch.utils.data.DataLoader(images, batch_size=4)\n", "\n", "\n", "def _standard_patchcore(image_dimension):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    backbone = models.wide_resnet50_2(pretrained=False)\n", "    backbone.name, backbone.seed = \"wideresnet50\", 0\n", "    patchcore_instance.load(\n", "        backbone=backbone,\n", "        layers_to_extract_from=[\"layer2\", \"layer3\"],\n", "        device=torch.device(\"cpu\"),\n", "        input_shape=[3, image_dimension, image_dimension],\n", "        pretrain_embed_dimension=1024,\n", "        target_embed_dimension=1024,\n", "        patchsize=3,\n", "        patchstride=1,\n", "        spade_nn=2,\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _load_patchcore_from_path(load_path):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    patchcore_instance.load_from_path(\n", "        load_path=load_path,\n", "        device=torch.device(\"cpu\"),\n", "        prepend=\"temp_patchcore\",\n", "        nn_method=patchcore.common.FaissNN(False, 4),\n", "    )\n", "    return patchcore_instance\n", "\n", "\n", "def _approximate_greedycoreset_sampler_with_reduction(\n", "    sampling_percentage, joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim\n", "):\n", "    return patchcore.sampler.ApproximateGreedyCoresetSampler(\n", "        percentage=sampling_percentage,\n", "        device=torch.device(\"cpu\"),\n", "        number_of_starting_points=10,\n", "        dimension_to_project_features_to=joh<PERSON><PERSON><PERSON><PERSON>uss_dim,\n", "    )\n", "\n", "\n", "def test_dummy_patchcore():\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    print(model.featuresampler)\n", "    model.fit(training_dataloader)\n", "\n", "    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\n", "    scores, masks = model.predict(test_features)\n", "\n", "    assert all([score > 0 for score in scores])\n", "    for mask in masks:\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_on_dataloader():\n", "    \"\"\"Test PatchCore on dataloader and assure training scores are zero.\"\"\"\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    scores, masks, labels_gt, masks_gt = model.predict(training_dataloader)\n", "\n", "    assert all([score < 1e-3 for score in scores])\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "\n", "def test_patchcore_load_and_saveing(tmpdir):\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    model.save_to_path(tmpdir, \"temp_patchcore\")\n", "\n", "    test_features = torch.Tensor(\n", "        1.234 * np.ones([2, 3, image_dimension, image_dimension])\n", "    )\n", "    scores, masks = model.predict(test_features)\n", "    other_scores, other_masks = model.predict(test_features)\n", "\n", "    assert np.all(scores == other_scores)\n", "    for mask, other_mask in zip(masks, other_masks):\n", "        assert np.all(mask == other_mask)\n", "\n", "\n", "def test_patchcore_real_data():\n", "    image_dimension = 112\n", "    sampling_percentage = 0.1\n", "    model = _standard_patchcore(image_dimension)\n", "    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\n", "        sampling_percentage=sampling_percentage,\n", "        joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim=64,\n", "    )\n", "\n", "    num_dummy_train_images = 50\n", "    training_dataloader = _dummy_various_dataloader(\n", "        num_dummy_train_images, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "\n", "    num_dummy_test_images = 5\n", "    test_dataloader = _dummy_various_dataloader(\n", "        num_dummy_test_images, [3, image_dimension, image_dimension]\n", "    )\n", "    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\n", "\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "    assert len(scores) == 5\n", "\n"]}], "source": ["print(root_node.text.decode())"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'root_node' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/notebooks/treesitter.ipynb Cell 16\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/notebooks/treesitter.ipynb#X36sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mtype\u001b[39m(root_node)\n", "\u001b[0;31mNameError\u001b[0m: name 'root_node' is not defined"]}], "source": ["type(root_node)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["['import_statement',\n", " 'import_statement',\n", " 'import_from_statement',\n", " 'import_statement',\n", " 'import_from_statement',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition',\n", " 'function_definition']"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["func = root_node.children[5]\n", "[c.type for c in root_node.children]"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<Node type=\"def\", start_point=(8, 0), end_point=(8, 3)>,\n", " <Node type=identifier, start_point=(8, 4), end_point=(8, 19)>,\n", " <Node type=parameters, start_point=(8, 19), end_point=(8, 58)>,\n", " <Node type=\":\", start_point=(8, 58), end_point=(8, 59)>,\n", " <Node type=block, start_point=(9, 4), end_point=(11, 5)>]"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["func.children"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def test_patchcore_real_data():\n", "    image_dimension = 112\n", "    sampling_percentage = 0.1\n", "    model = _standard_patchcore(image_dimension)\n", "    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\n", "        sampling_percentage=sampling_percentage,\n", "        joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim=64,\n", "    )\n", "\n", "    num_dummy_train_images = 50\n", "    training_dataloader = _dummy_various_dataloader(\n", "        num_dummy_train_images, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "\n", "    num_dummy_test_images = 5\n", "    test_dataloader = _dummy_various_dataloader(\n", "        num_dummy_test_images, [3, image_dimension, image_dimension]\n", "    )\n", "    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\n", "\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "    assert len(scores) == 5\n"]}], "source": ["print(func.text.decode())"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "expression_statement\n", "----\n", "for_statement\n", "----\n", "assert_statement\n", "----\n"]}], "source": ["print_children(func.children[-1], 'type')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import numpy as np\n", "----\n", "import torch.utils.data\n", "----\n", "from torchvision import models\n", "----\n", "import patchcore\n", "----\n", "from patchcore import patchcore as patchcore_model\n", "----\n", "def _dummy_features(number_of_examples, shape_of_examples):\n", "    return torch.Tensor(\n", "        np.stack(number_of_examples * [np.ones(shape_of_examples)], axis=0)\n", "    )\n", "----\n", "def _dummy_constant_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "----\n", "def _dummy_various_features(number_of_examples, shape_of_examples):\n", "    images = torch.ones((number_of_examples, *shape_of_examples))\n", "    multiplier = torch.arange(number_of_examples) / float(number_of_examples)\n", "    for _ in range(images.ndim - 1):\n", "        multiplier = multiplier.unsqueeze(-1)\n", "    return multiplier * images\n", "----\n", "def _dummy_various_dataloader(number_of_examples, shape_of_examples):\n", "    features = _dummy_various_features(number_of_examples, shape_of_examples)\n", "    return torch.utils.data.DataLoader(features, batch_size=1)\n", "----\n", "def _dummy_images(number_of_examples, image_shape):\n", "    torch.manual_seed(0)\n", "    return torch.rand([number_of_examples, *image_shape])\n", "----\n", "def _dummy_image_random_dataloader(number_of_examples, image_shape):\n", "    images = _dummy_images(number_of_examples, image_shape)\n", "    return torch.utils.data.DataLoader(images, batch_size=4)\n", "----\n", "def _standard_patchcore(image_dimension):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    backbone = models.wide_resnet50_2(pretrained=False)\n", "    backbone.name, backbone.seed = \"wideresnet50\", 0\n", "    patchcore_instance.load(\n", "        backbone=backbone,\n", "        layers_to_extract_from=[\"layer2\", \"layer3\"],\n", "        device=torch.device(\"cpu\"),\n", "        input_shape=[3, image_dimension, image_dimension],\n", "        pretrain_embed_dimension=1024,\n", "        target_embed_dimension=1024,\n", "        patchsize=3,\n", "        patchstride=1,\n", "        spade_nn=2,\n", "    )\n", "    return patchcore_instance\n", "----\n", "def _load_patchcore_from_path(load_path):\n", "    patchcore_instance = patchcore_model.PatchCore(torch.device(\"cpu\"))\n", "    patchcore_instance.load_from_path(\n", "        load_path=load_path,\n", "        device=torch.device(\"cpu\"),\n", "        prepend=\"temp_patchcore\",\n", "        nn_method=patchcore.common.FaissNN(False, 4),\n", "    )\n", "    return patchcore_instance\n", "----\n", "def _approximate_greedycoreset_sampler_with_reduction(\n", "    sampling_percentage, joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim\n", "):\n", "    return patchcore.sampler.ApproximateGreedyCoresetSampler(\n", "        percentage=sampling_percentage,\n", "        device=torch.device(\"cpu\"),\n", "        number_of_starting_points=10,\n", "        dimension_to_project_features_to=joh<PERSON><PERSON><PERSON><PERSON>uss_dim,\n", "    )\n", "----\n", "def test_dummy_patchcore():\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    print(model.featuresampler)\n", "    model.fit(training_dataloader)\n", "\n", "    test_features = torch.Tensor(2 * np.ones([2, 3, image_dimension, image_dimension]))\n", "    scores, masks = model.predict(test_features)\n", "\n", "    assert all([score > 0 for score in scores])\n", "    for mask in masks:\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "----\n", "def test_patchcore_on_dataloader():\n", "    \"\"\"Test PatchCore on dataloader and assure training scores are zero.\"\"\"\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    scores, masks, labels_gt, masks_gt = model.predict(training_dataloader)\n", "\n", "    assert all([score < 1e-3 for score in scores])\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "----\n", "def test_patchcore_load_and_saveing(tmpdir):\n", "    image_dimension = 112\n", "    model = _standard_patchcore(image_dimension)\n", "\n", "    training_dataloader = _dummy_constant_dataloader(\n", "        4, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "    model.save_to_path(tmpdir, \"temp_patchcore\")\n", "\n", "    test_features = torch.Tensor(\n", "        1.234 * np.ones([2, 3, image_dimension, image_dimension])\n", "    )\n", "    scores, masks = model.predict(test_features)\n", "    other_scores, other_masks = model.predict(test_features)\n", "\n", "    assert np.all(scores == other_scores)\n", "    for mask, other_mask in zip(masks, other_masks):\n", "        assert np.all(mask == other_mask)\n", "----\n", "def test_patchcore_real_data():\n", "    image_dimension = 112\n", "    sampling_percentage = 0.1\n", "    model = _standard_patchcore(image_dimension)\n", "    model.sampler = _approximate_greedycoreset_sampler_with_reduction(\n", "        sampling_percentage=sampling_percentage,\n", "        joh<PERSON><PERSON><PERSON><PERSON><PERSON>_dim=64,\n", "    )\n", "\n", "    num_dummy_train_images = 50\n", "    training_dataloader = _dummy_various_dataloader(\n", "        num_dummy_train_images, [3, image_dimension, image_dimension]\n", "    )\n", "    model.fit(training_dataloader)\n", "\n", "    num_dummy_test_images = 5\n", "    test_dataloader = _dummy_various_dataloader(\n", "        num_dummy_test_images, [3, image_dimension, image_dimension]\n", "    )\n", "    scores, masks, labels_gt, masks_gt = model.predict(test_dataloader)\n", "\n", "    for mask, mask_gt in zip(masks, masks_gt):\n", "        assert np.all(mask.shape == (image_dimension, image_dimension))\n", "        assert np.all(mask_gt.shape == (image_dimension, image_dimension))\n", "\n", "    assert len(scores) == 5\n", "----\n"]}], "source": ["for child in root_node.children:\n", "    print(child.text.decode())\n", "    print('----')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Topological Sort"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["import graphlib"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["('A', 'C', 'B', 'D')"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = {\"D\": {\"B\", \"C\"}, \"C\": {\"A\"}, \"B\": {\"A\"}}\n", "ts = graphlib.Topological<PERSON><PERSON>er(graph)\n", "tuple(ts.static_order())"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
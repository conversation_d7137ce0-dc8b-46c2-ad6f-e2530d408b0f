"""Utilities for working with SXS data."""

from base.datasets.sxs import constants


def last_n_lines(text, n):
    lines = text.splitlines(keepends=True)
    return "".join(lines[-n:])


def first_n_lines(text, n):
    lines = text.splitlines(keepends=True)
    return "".join(lines[:n])


def make_sxs_html(replay):
    return constants.HTML_TEMPLATE.render(
        prefix=last_n_lines(replay.request.prefix, n=50),
        suffix=first_n_lines(replay.request.suffix, n=50),
        original_request_id=replay.request_id,
        original_text=replay.response.text,
        original_request_url=f"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/{replay.request_id}",
        original_model=replay.response.model,
        new_request_id=replay.replayed_request_id,
        new_text=replay.replayed_response.text,
        new_request_url=f"https://support.dev-vzhao.t.us-central1.dev.augmentcode.com/request/{replay.replayed_request_id}",
        new_model=replay.replayed_response.model,
    )

"""Utilities for working with Google Sheets."""

import os

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


def get_google_sheet_credentials():
    """
    Returns an authorized GSheets API service instance.
    Load pre-authorized user credentials from the environment.
    TODO - See https://developers.google.com/identity
    for guides on implementing OAuth2 for the application.
    """
    # If modifying these scopes, delete the file token.json.
    SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]
    creds = None
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                f"/home/<USER>/augment/credentials.json", SCOPES
            )
            creds = flow.run_local_server(port=8080)
        # Save the credentials for the next run
        with open(
            f"/home/<USER>/augment/token.json", "w", encoding="utf-8"
        ) as token:
            token.write(creds.to_json())
    return creds


def get_google_sheets_service(creds):
    return build("sheets", "v4", credentials=creds)


def update_row(
    service,
    spreadsheet_id,
    sheet_name,
    row_number,
    values,
    value_input_option="USER_ENTERED",
):
    """Updates a single row in a spreadsheet.

    Args:
        service: An authorized GSheets API service instance.
        spreadsheet_id: The ID of the spreadsheet to update.
        row_number: The row number to update.
        value_input_option: How the input data should be interpreted.
        values: The values to update. It should be 1D array of strings.

    """
    update_values(
        service,
        spreadsheet_id,
        f"{sheet_name}!{row_number}:{row_number}",
        value_input_option,
        [values],
    )


def update_values(service, spreadsheet_id, range_name, value_input_option, values):
    """
    Creates the batch_update the user has access to.
    Load pre-authorized user credentials from the environment.
    TODO(developer) - See https://developers.google.com/identity
    for guides on implementing OAuth2 for the application.
    """
    # pylint: disable=maybe-no-member
    try:
        body = {"values": values}
        result = (
            service.spreadsheets()
            .values()
            .update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body,
            )
            .execute()
        )
        print(f"{result.get('updatedCells')} cells updated.")
        return result
    except HttpError as error:
        print(f"An error occurred: {error}")
        return error


def compute_range(start_cell, values):
    num_rows, num_cols = len(values), len(values[0])
    end_cell = f"{chr(ord('A') + num_cols - 1)}{num_rows}"  # Calculate column letter dynamically
    return f"{start_cell}:{end_cell}"


def create_new_sheet(service, spreadsheet_id, title):
    batch_update_spreadsheet_request_body = {
        "requests": [
            {
                "addSheet": {
                    "properties": {
                        "title": title,
                    }
                }
            }
        ]
    }

    _ = (
        service.spreadsheets()
        .batchUpdate(
            spreadsheetId=spreadsheet_id, body=batch_update_spreadsheet_request_body
        )
        .execute()
    )

    print(f"Added new sheet with title: {title}")

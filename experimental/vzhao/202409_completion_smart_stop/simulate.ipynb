{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Look at this model\n", "elden_fprefretsignpfsuf_smartheader - starrepro smart\n", "\n", "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/85080/logs\n", "\n", "\n", "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/85079/code"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads Client"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient\n", "from base.augment_client.client import BlobsJson\n", "\n", "with open(os.path.expanduser(\"~/.config/augment/api_token\")) as f:\n", "    client = AugmentClient(\n", "        url=\"https://staging-shard-0.api.augmentcode.com/\",\n", "        # url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "        token=f.read().strip(),\n", "    )\n", "MODEL = \"eldenv5-1-15b\"\n", "# MODEL = \"eldenv7-0-15b\"\n", "model_client = client.client_for_model(MODEL)\n", "\n", "\n", "model_client.complete(\n", "    prompt=\"hello\",\n", "    suffix=\"\",\n", "    path=\"hello.py\",\n", "    cursor_position=5,\n", "    prefix_begin=0,\n", "    suffix_end=5,\n", "    blobs=BlobsJson(\n", "        checkpoint_id=None,\n", "        added_blobs=[],\n", "        deleted_blobs=[],\n", "    ),\n", "    recency_info=None,\n", "    filter_threshold=0.99,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CC-eval"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval.harness.tasks.cceval import _prepare_dataset\n", "from research.core.data_paths import canonicalize_path\n", "\n", "\n", "all_patches_by_repo, all_documents_by_repo = _prepare_dataset(\n", "    Path(canonicalize_path(\"data/eval/cceval\"))\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hindsight"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import zstandard as zstd\n", "from pathlib import Path\n", "from research.eval.harness.tasks.hindsight import (\n", "    HindsightCompletionDataset,\n", "    HindsightCompletionTask,\n", ")\n", "from base.datasets.tenants import get_tenant\n", "\n", "# Configuration\n", "config = {\n", "    \"dataset\": \"2024-04-25-v0.7\",\n", "    \"tenant_name\": \"dogfood\",\n", "    \"dataset_base_dir\": Path(canonicalize_path(\"data/eval/hindsight\")),\n", "    \"limit\": 1000,  # Set a number if you want to limit the dataset size\n", "}\n", "\n", "# Create a Config object\n", "cfg = HindsightCompletionTask.Config(**config)\n", "\n", "# Construct the dataset path\n", "dataset_path = cfg.dataset_base_dir / cfg.dataset / cfg.tenant_name / \"data.jsonl.zst\"\n", "\n", "# Load the dataset\n", "if dataset_path.exists():\n", "    print(f\"Loading dataset from {dataset_path}\")\n", "    with zstd.open(dataset_path, \"r\", encoding=\"utf-8\") as f:\n", "        data = HindsightCompletionDataset.load_data(f, cfg.limit)\n", "else:\n", "    dataset_path = dataset_path.with_name(\"data.jsonl\")\n", "    print(f\"Loading dataset from {dataset_path}\")\n", "    with dataset_path.open() as f:\n", "        data = HindsightCompletionDataset.load_data(f, cfg.limit)\n", "\n", "print(f\"Loaded {len(data)} examples from the dataset.\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from base.datasets import replay_utils\n", "\n", "blob_cache = replay_utils.get_blob_cache(get_tenant(\"dogfood-shard\"))\n", "replay_utils.upload_and_ensure_blobs_exist(\n", "    client,\n", "    blob_cache,\n", "    data[0].completion.request.blob_names,\n", "    MODEL,\n", "    upload_batch_size=10,\n", ")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["datum = dataset.data[0]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["type(datum)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hindsight import convert_hindsight_datum_to_model_input\n", "\n", "model_input = convert_hindsight_datum_to_model_input(dataset.data[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Mapping, Optional\n", "from base.datasets.gcs_blob_cache import PathAndContent\n", "from research.core.types import Document\n", "from research.eval.harness.systems.abs_system import CodeCompleteSystem\n", "from research.eval.harness.systems.static_analysis_systems import EnderSystem\n", "\n", "\n", "class UpdateIndex:\n", "    def __init__(self, client: AugmentClient):\n", "        self.client = client\n", "        self.seen_blobs = set()\n", "\n", "    def __call__(self, new_blobs: Mapping[str, Optional[PathAndContent]]):\n", "        docs_to_add = [\n", "            Document(\n", "                id=blob_name,\n", "                text=blob.content,\n", "                path=str(blob.path),\n", "                meta=None,\n", "            )\n", "            for blob_name in (new_blobs.keys() - self.seen_blobs)\n", "            if (blob := new_blobs.get(blob_name)) is not None\n", "        ]\n", "\n", "        system.add_docs(docs_to_add)\n", "\n", "        seen_blobs.update(new_blobs.keys() - seen_blobs)\n", "        print(f\"The index now contains {len(seen_blobs)} docs.\")\n", "\n", "        return seen_blobs\n", "\n", "\n", "# Usage example:\n", "# seen_blobs = update_index(system, new_blobs, seen_blobs, signature_blobs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Simulation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["all_documents_by_repo.keys()"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [], "source": ["from base.augment_client import remote_lib\n", "from base.datasets.itertools import batched\n", "from base.augment_client.client import UploadContent\n", "import tenacity\n", "\n", "\n", "@tenacity.retry(stop=tenacity.stop_after_attempt(2))\n", "def _add_docs(docs, upload_batch_size=100):\n", "    \"\"\"Uploads the doc to the server.\"\"\"\n", "    all_blob_names = set()\n", "    docs = [UploadContent(doc.text, doc.path) for doc in docs]\n", "    blobs = [remote_lib._doc_to_blob_info(doc) for doc in docs]\n", "    blobs, filtered_by_id = remote_lib._exclude_ids(blobs, set())\n", "    # blobs, filtered_by_extension = _include_extensions(blobs, self._extensions)\n", "    blobs, filtered_by_size = remote_lib._filter_by_max_size(blobs, 1024 * 1024)\n", "    for batch in batched(blobs, upload_batch_size):\n", "        blob_batch = [blob_info.blob for blob_info in batch]\n", "        blob_names = client.batch_upload(blob_batch)\n", "        all_blob_names.update(blob_names)\n", "    return all_blob_names"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "import os\n", "from base.augment_client.client import BlobsJson\n", "\n", "\n", "class CompletionEvent(BaseModel):\n", "    completion: str\n", "    accepted: str\n", "\n", "\n", "class TypeEvent(BaseModel):\n", "    typed: str\n", "\n", "\n", "class Simulation(BaseModel):\n", "    repo: str\n", "    task_id: str\n", "    groundtruth: str\n", "    events: list[CompletionEvent | TypeEvent]\n", "\n", "\n", "def simulate(cceval_input) -> Simulation:\n", "    rst = []\n", "    last_prefix = cceval_input.prompt\n", "    remaining_gt = cceval_input.groundtruth\n", "    while remaining_gt:\n", "        response = model_client.complete(\n", "            prompt=last_prefix,\n", "            suffix=cceval_input.right_context,\n", "            path=cceval_input.metadata.file,\n", "            cursor_position=len(last_prefix),\n", "            prefix_begin=0,\n", "            suffix_end=len(last_prefix) + len(cceval_input.right_context),\n", "            blobs=BlobsJson(\n", "                checkpoint_id=None,\n", "                added_blobs=list(blob_names),\n", "                deleted_blobs=[],\n", "            ),\n", "            recency_info=None,\n", "        )\n", "        common_prefix = os.path.commonprefix([remaining_gt, response.text])\n", "        rst.append(CompletionEvent(completion=response.text, accepted=common_prefix))\n", "        remaining_gt = remaining_gt[len(common_prefix) :]\n", "        if remaining_gt:\n", "            last_prefix = last_prefix + common_prefix + remaining_gt[0]\n", "            rst.append(TypeEvent(typed=remaining_gt[0]))\n", "            remaining_gt = remaining_gt[1:]\n", "    return Simulation(\n", "        repo=cceval_input.metadata.repository,\n", "        task_id=cceval_input.metadata.task_id,\n", "        groundtruth=cceval_input.groundtruth,\n", "        events=rst,\n", "    )\n", "\n", "\n", "import time\n", "from threading import Lock\n", "\n", "\n", "class SimpleRateLimiter:\n", "    def __init__(self, requests_per_second: float):\n", "        self.requests_per_second = requests_per_second\n", "        self.requests = requests_per_second\n", "        self.last_refill = time.time()\n", "        self.lock = Lock()\n", "\n", "    def acquire(self):\n", "        with self.lock:\n", "            now = time.time()\n", "            time_passed = now - self.last_refill\n", "            # Number of requests available at the moment.\n", "            self.requests = min(\n", "                self.requests + time_passed * self.requests_per_second,\n", "                self.requests_per_second,\n", "            )\n", "            self.last_refill = now\n", "\n", "            if self.requests >= 1:\n", "                self.requests -= 1\n", "                return 0\n", "            else:\n", "                sleep_time = (1 - self.requests) / self.requests_per_second\n", "                time.sleep(sleep_time)\n", "                self.requests = 0\n", "                return sleep_time"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [], "source": ["from base.augment_client.client import BlobsJson\n", "import tqdm\n", "\n", "simulations = []\n", "finished_task_ids = set()\n", "rate_limiter = SimpleRateLimiter(2)\n", "progress_path = (\n", "    f\"/mnt/efs/augment/public_html/vzhao/smart_stop/simulations/cceval_{MODEL}_done\"\n", ")\n", "output_path = (\n", "    f\"/mnt/efs/augment/public_html/vzhao/smart_stop/simulations/cceval_{MODEL}.jsonl\"\n", ")\n", "\n", "if os.path.exists(progress_path):\n", "    with open(progress_path, \"r\") as f:\n", "        for line in f:\n", "            finished_task_ids.add(line.strip())\n", "\n", "with open(output_path, \"a\") as f, open(progress_path, \"a\") as f_p:\n", "    for repo in tqdm.tqdm(all_documents_by_repo):\n", "        docs = all_documents_by_repo[repo]\n", "        patches = all_patches_by_repo[repo]\n", "        try:\n", "            blob_names = _add_docs(docs, 10)\n", "        except Exception as e:\n", "            print(f\"Failed to upload blobs for {repo}: {e}\")\n", "            continue\n", "        for cceval_input in patches:\n", "            try:\n", "                if cceval_input.metadata.task_id in finished_task_ids:\n", "                    continue\n", "                rate_limiter.acquire()\n", "                response = model_client.complete(\n", "                    prompt=cceval_input.prompt,\n", "                    suffix=cceval_input.right_context,\n", "                    path=cceval_input.metadata.file,\n", "                    cursor_position=len(cceval_input.prompt),\n", "                    prefix_begin=0,\n", "                    suffix_end=len(cceval_input.prompt)\n", "                    + len(cceval_input.right_context),\n", "                    blobs=BlobsJson(\n", "                        checkpoint_id=None,\n", "                        added_blobs=list(blob_names),\n", "                        deleted_blobs=[],\n", "                    ),\n", "                    recency_info=None,\n", "                )\n", "                if cceval_input.groundtruth not in response.text:\n", "                    sim = simulate(cceval_input)\n", "                    f.write(sim.model_dump_json() + \"\\n\")\n", "                    simulations.append(simulate(cceval_input))\n", "                f_p.write(cceval_input.metadata.task_id + \"\\n\")\n", "            except Exception as e:\n", "                print(f\"{cceval_input.metadata.task_id}: {e}\")"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets\n", "from IPython.display import display, HTML\n", "from termcolor import colored\n", "\n", "\n", "def display_simulation(simulations):\n", "    def render_simulation(sim_index, event_index):\n", "        sim = simulations[sim_index]\n", "        output = f\"Simulation {sim_index + 1}/{len(simulations)}\\n\"\n", "        output += f\"Repository: {sim.repo}\\n\"\n", "        output += f\"Task ID: {sim.task_id}\\n\\n\"\n", "\n", "        for i, event in enumerate(sim.events[: event_index + 1]):\n", "            if isinstance(event, CompletionEvent):\n", "                output += colored(f\"Event {i+1}: Completion\\n\", \"blue\")\n", "                output += (\n", "                    colored(\"Completion: \", \"blue\")\n", "                    + colored(event.completion, \"red\", \"on_black\")\n", "                    + \"\\n\"\n", "                )\n", "                output += (\n", "                    colored(\"Accepted: \", \"green\")\n", "                    + colored(event.accepted, \"green\", \"on_black\")\n", "                    + \"\\n\\n\"\n", "                )\n", "            elif isinstance(event, TypeEvent):\n", "                output += colored(f\"Event {i+1}: Typed\\n\", \"yellow\")\n", "                output += (\n", "                    colored(\"Typed: \", \"yellow\")\n", "                    + colored(event.typed, \"yellow\", \"on_black\")\n", "                    + \"\\n\\n\"\n", "                )\n", "\n", "        return output\n", "\n", "    sim_options = [(f\"Simulation {i+1}\", i) for i in range(len(simulations))]\n", "    dropdown = widgets.Dropdown(\n", "        options=sim_options,\n", "        value=0,\n", "        description=\"Simulation:\",\n", "    )\n", "    event_slider = widgets.IntSlider(min=0, max=0, step=1, description=\"Event:\")\n", "    output = widgets.Output()\n", "\n", "    def update_event_slider(*args):\n", "        sim_index = dropdown.value\n", "        event_slider.max = len(simulations[sim_index].events) - 1\n", "        event_slider.value = 0\n", "\n", "    def on_value_change(*args):\n", "        output.clear_output()\n", "        with output:\n", "            print(render_simulation(dropdown.value, event_slider.value))\n", "\n", "    dropdown.observe(update_event_slider, names=\"value\")\n", "    dropdown.observe(on_value_change, names=\"value\")\n", "    event_slider.observe(on_value_change, names=\"value\")\n", "\n", "    display(widgets.VBox([dropdown, event_slider, output]))\n", "    update_event_slider()\n", "    on_value_change()  # Display the first simulation and event initially\n", "\n", "\n", "# Usage\n", "display_simulation(simulations)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "# # Hindsight\n", "# eval_dir = Path(\"/mnt/efs/augment/eval/jobs/QeDytipD\")\n", "# CCEval\n", "eval_dir = Path(\"/mnt/efs/augment/eval/jobs/bEJg7QbW\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.eval.harness import utils\n", "\n", "utils.read_jsonl(eval_dir / \"000_results.jsonl\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from research.eval.harness import utils\n", "\n", "# data = utils.read_jsonl_zst(eval_dir / \"000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\")\n", "data = utils.read_jsonl_zst(\n", "    eval_dir / \"000_starcoder2_fastforward_CCEval_completed_patches.jsonl.zst\"\n", ")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["data[0].keys()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.cceval import CCEval\n", "\n", "cceval = CCEval()\n", "\n", "cceval.create_model_input(patches[1], [])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["patches[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["from research.core import str_diff\n", "\n", "for d in data:\n", "    if d[\"generation\"] == d[\"ground_truth\"]:\n", "        continue\n", "    str_diff.print_diff(\n", "        str_diff.build_str_diff(d[\"ground_truth\"], d[\"generation\"], \"precise_chardiff\")\n", "    )\n", "    print(\"\\n====\\n\")\n", "    if input() == \"c\":\n", "        break"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [], "source": ["data[2][\"repository\"]"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["d.keys()"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["print(d[\"generation\"])"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["print(d[\"ground_truth\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads Results"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "simulate = importlib.import_module(\n", "    \"experimental.vzhao.202409_completion_smart_stop.simulate\"\n", ")\n", "\n", "rst_file = \"/mnt/efs/augment/public_html/vzhao/smart_stop/simulations/hindsight_eldenv4-0c-15b.jsonl\"\n", "\n", "data = {}\n", "with open(rst_file, \"r\") as f:\n", "    for line in f.readlines():\n", "        sim = simulate.Simulation.parse_raw(line)\n", "        data[sim.task_id] = sim"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["for e in data[\"00835a72-be6b-41a9-97ac-99c7565e4cff\"].events:\n", "    print(\n", "        e.__class__.__name__,\n", "        e.request_id if isinstance(e, simulate.CompletionEvent) else None,\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
from pydantic import BaseModel
from pathlib import Path
from research.eval.harness.tasks.hindsight import (
    HindsightCompletionDataset,
    HindsightCompletionTask,
)
from base.datasets.hindsight_completion import HindsightCompletionDatum
from research.core.data_paths import canonicalize_path
from research.eval.harness.tasks.cceval import _prepare_dataset
import zstandard as zstd


class CompletionEvent(BaseModel):
    completion: str
    accepted: str
    request_id: str


class TypeEvent(BaseModel):
    typed: str


class Simulation(BaseModel):
    repo: str
    task_id: str
    groundtruth: str
    events: list[CompletionEvent | TypeEvent]


def highsight_dataset() -> list[HindsightCompletionDatum]:
    # Configuration
    config = {
        "dataset": "2024-04-25-v0.7",
        "tenant_name": "dogfood",
        "dataset_base_dir": Path(canonicalize_path("data/eval/hindsight")),
        "limit": 1000,  # Set a number if you want to limit the dataset size
    }

    # Create a Config object
    cfg = HindsightCompletionTask.Config(**config)

    # Construct the dataset path
    dataset_path = (
        cfg.dataset_base_dir / cfg.dataset / cfg.tenant_name / "data.jsonl.zst"
    )

    # Load the dataset
    if dataset_path.exists():
        print(f"Loading dataset from {dataset_path}")
        with zstd.open(dataset_path, "r", encoding="utf-8") as f:
            data = HindsightCompletionDataset.load_data(f, cfg.limit)
    else:
        dataset_path = dataset_path.with_name("data.jsonl")
        print(f"Loading dataset from {dataset_path}")
        with dataset_path.open() as f:
            data = HindsightCompletionDataset.load_data(f, cfg.limit)
    return data


def cceval_data():
    # Prepare dataset
    all_patches_by_repo, all_documents_by_repo = _prepare_dataset(
        Path(canonicalize_path("data/eval/cceval"))
    )
    return all_patches_by_repo, all_documents_by_repo

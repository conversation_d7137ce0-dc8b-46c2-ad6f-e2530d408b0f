"""
Usage

python experimental/vzhao/202409_completion_smart_stop/simulate.py \
--api_url=https://staging-shard-0.api.augmentcode.com/ \
--model_name=eldenv5-1-15b \
--dataset=cceval

"""

import collections
import importlib
import os
import time
from pathlib import Path

import tenacity
import tqdm
from absl import app, flags

from base.augment_client import remote_lib
from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    B<PERSON>bs<PERSON>son,
    UploadContent,
)
from base.datasets import replay_utils
from base.datasets.itertools import batched
from base.datasets.tenants import get_tenant
from base.languages.comment_guesser import (
    guess_is_multi_line_comment,
    guess_is_single_line_comment,
)
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from research.eval.harness.tasks.hindsight import HindsightCompletionDatum

common = importlib.import_module(
    "experimental.vzhao.202409_completion_smart_stop.simulate"
)
Simulation = common.Simulation
CompletionEvent = common.CompletionEvent
TypeEvent = common.TypeEvent

# Initialize tokenizer and client
tokenizer = StarCoder2Tokenizer()


_API_URL = flags.DEFINE_string(
    "api_url", "https://staging-shard-0.api.augmentcode.com/", "API URL"
)

_MODEL_NAME = flags.DEFINE_string("model_name", "eldenv4-0c-15b", "Model name")

_OUTPUT_DIR = Path("/mnt/efs/augment/public_html/vzhao/smart_stop/simulations")

_API_RATE = flags.DEFINE_integer("api_rate", 4, "API rate limit.")

_DATASET = flags.DEFINE_enum(
    "dataset",
    "cceval",
    ["cceval", "hindsight", "hindsight_full_file"],
    "Dataset to use.",
)

_FILTER_THRESHOLD = flags.DEFINE_float(
    "filter_threshold", 1.0, "Filter threshold to use."
)

_TENANT = get_tenant("dogfood-shard")
_BLOB_CACHE = replay_utils.get_blob_cache(_TENANT)


def get_augment_client():
    with open(os.path.expanduser("~/.config/augment/api_token")) as f:
        client = AugmentClient(
            url=_API_URL.value,
            token=f.read().strip(),
            user_agent="vzhao/0 (simulation)",
        )
    return client


def get_model_client(client: AugmentClient):
    return client.client_for_model(_MODEL_NAME.value)


# Define models
class SimpleRateLimiter:
    def __init__(self, rate):
        self.rate = rate
        self.last_call = 0

    def acquire(self):
        now = time.time()
        sleep_time = max(0, 1 / self.rate - (now - self.last_call))
        time.sleep(sleep_time)
        self.last_call = now
        return sleep_time


# Helper functions
def simulate_basic(
    prompt: str,
    suffix: str,
    groundtruth: str,
    file_path: str,
    blob_names: set[str],
    model_client: AugmentModelClient,
    rate_limiter: SimpleRateLimiter,
) -> list[CompletionEvent | TypeEvent]:
    events = []
    last_prefix = prompt
    remaining_gt = groundtruth
    while remaining_gt:
        rate_limiter.acquire()
        response = model_client.complete(
            prompt=last_prefix,
            suffix=suffix,
            path=file_path,
            cursor_position=len(last_prefix),
            prefix_begin=0,
            suffix_end=len(last_prefix) + len(suffix),
            blobs=BlobsJson(
                checkpoint_id=None,
                added_blobs=list(blob_names),
                deleted_blobs=[],
            ),
            recency_info=None,
            filter_threshold=_FILTER_THRESHOLD.value,
        )
        common_prefix = os.path.commonprefix([remaining_gt, response.text])
        events.append(
            CompletionEvent(
                completion=response.text,
                accepted=common_prefix,
                request_id=str(response.request_id),
            )
        )
        if common_prefix != "":
            last_prefix = last_prefix + common_prefix
            remaining_gt = remaining_gt[len(common_prefix) :]
        else:
            last_prefix = last_prefix + remaining_gt[0]
            events.append(TypeEvent(typed=remaining_gt[0]))
            remaining_gt = remaining_gt[1:]
    return events


def has_type_event(events) -> bool:
    return any(isinstance(event, TypeEvent) for event in events)


def simulate_cceval(
    cceval_input,
    blob_names: set[str],
    model_client: AugmentModelClient,
    rate_limiter: SimpleRateLimiter,
) -> Simulation:
    return Simulation(
        repo=cceval_input.metadata.repository,
        task_id=cceval_input.metadata.task_id,
        groundtruth=cceval_input.groundtruth,
        events=simulate_basic(
            prompt=cceval_input.prompt,
            suffix=cceval_input.right_context,
            groundtruth=cceval_input.groundtruth,
            file_path=cceval_input.metadata.file,
            blob_names=blob_names,
            model_client=model_client,
            rate_limiter=rate_limiter,
        ),
    )


def simulate_hindsight(
    hindsight_datum: HindsightCompletionDatum,
    model_client: AugmentModelClient,
    rate_limiter: SimpleRateLimiter,
) -> Simulation:
    return Simulation(
        repo="augment",
        task_id=hindsight_datum.completion.request_id,
        groundtruth=hindsight_datum.ground_truth,
        events=simulate_basic(
            prompt=hindsight_datum.completion.request.prefix,
            suffix=hindsight_datum.completion.request.suffix,
            groundtruth=hindsight_datum.ground_truth,
            file_path=hindsight_datum.completion.request.path,
            blob_names=set(hindsight_datum.completion.request.blob_names),
            model_client=model_client,
            rate_limiter=rate_limiter,
        ),
    )


def simulate_hindsight_full_file(
    hindsight_datum: HindsightCompletionDatum,
    model_client: AugmentModelClient,
    rate_limiter: SimpleRateLimiter,
) -> Simulation:
    full_file = (
        hindsight_datum.completion.request.prefix
        + hindsight_datum.ground_truth
        + hindsight_datum.completion.request.suffix
    )
    return Simulation(
        repo="augment",
        task_id=hindsight_datum.completion.request_id,
        groundtruth=hindsight_datum.ground_truth,
        events=simulate_basic(
            prompt="",
            suffix="",
            groundtruth=full_file,
            file_path=hindsight_datum.completion.request.path,
            blob_names=set(hindsight_datum.completion.request.blob_names),
            model_client=model_client,
            rate_limiter=rate_limiter,
        ),
    )


@tenacity.retry(stop=tenacity.stop_after_attempt(2))
def _add_docs(
    client: AugmentClient, docs: list[UploadContent], upload_batch_size=100
) -> set[str]:
    """This is used to upload docs."""
    all_blob_names = set()
    blobs = [remote_lib._doc_to_blob_info(doc) for doc in docs]
    blobs, filtered_by_id = remote_lib._exclude_ids(blobs, set())
    blobs, filtered_by_size = remote_lib._filter_by_max_size(blobs, 1024 * 1024)
    for batch in batched(blobs, upload_batch_size):
        blob_batch = [blob_info.blob for blob_info in batch]
        blob_names = client.batch_upload(blob_batch)
        all_blob_names.update(blob_names)
    return all_blob_names


def _upload_and_ensure_blobs_exist(client, blob_names, upload_batch_size=100):
    """This is used to upload known blob_names."""
    replay_utils.upload_and_ensure_blobs_exist(
        client,
        _BLOB_CACHE,
        blob_names,
        _MODEL_NAME.value,
        upload_batch_size=upload_batch_size,
    )


def _skip_comment_middle(prefix, suffix, path) -> bool:
    if guess_is_multi_line_comment(prefix, suffix, path):
        return True
    elif guess_is_single_line_comment(prefix, suffix, path):
        return True
    return False


def main(argv):
    client = get_augment_client()
    model_client = get_model_client(client)

    # Main simulation loop
    finished_task_ids = set()
    rate_limiter = SimpleRateLimiter(_API_RATE.value)
    progress_path = _OUTPUT_DIR / f"{_DATASET.value}_{_MODEL_NAME.value}_done"
    output_path = _OUTPUT_DIR / f"{_DATASET.value}_{_MODEL_NAME.value}.jsonl"

    if os.path.exists(progress_path):
        with open(progress_path, "r") as f:
            for line in f:
                finished_task_ids.add(line.strip())

    counter = collections.Counter()

    with open(output_path, "a", buffering=1) as f, open(
        progress_path, "a", buffering=1
    ) as f_p:
        if _DATASET.value == "cceval":
            all_patches_by_repo, all_documents_by_repo = common.cceval_data()
            tqdm_iterator = tqdm.tqdm(all_documents_by_repo)
            for repo in tqdm_iterator:
                tqdm_iterator.set_description(f"Status: {counter}")
                docs = all_documents_by_repo[repo]
                patches = all_patches_by_repo[repo]
                try:
                    blob_names = _add_docs(
                        client, [UploadContent(doc.text, doc.path) for doc in docs], 10
                    )
                    counter["indexed_success"] += 1
                except Exception as e:
                    print(f"Failed to upload blobs for {repo}: {e}")
                    counter["indexed_failure"] += 1
                    continue
                for cceval_input in patches:
                    try:
                        if cceval_input.metadata.task_id in finished_task_ids:
                            counter["simulated_patches"] += 1
                            continue
                        sim = simulate_cceval(
                            cceval_input, blob_names, model_client, rate_limiter
                        )
                        if has_type_event(sim.events):
                            f.write(sim.model_dump_json() + "\n")
                        f_p.write(cceval_input.metadata.task_id + "\n")
                        counter["simulated_patches"] += 1
                    except Exception as e:
                        print(f"{cceval_input.metadata.task_id}: {e}")
                        counter["failed_patches"] += 1
                counter["simulated_repo"] += 1
        elif _DATASET.value in ("hindsight", "hindsight_full_file"):
            data: list[HindsightCompletionDatum] = common.highsight_dataset()
            tqdm_iterator = tqdm.tqdm(data)
            for datum in tqdm_iterator:
                tqdm_iterator.set_description(f"Status: {counter}")
                if datum.completion.request_id in finished_task_ids:
                    counter["success"] += 1
                    continue
                try:
                    if _skip_comment_middle(
                        datum.completion.request.prefix,
                        datum.completion.request.suffix,
                        datum.completion.request.path,
                    ):
                        counter["skipped"] += 1
                        continue

                    _upload_and_ensure_blobs_exist(
                        client,
                        datum.completion.request.blob_names,
                        upload_batch_size=10,
                    )
                    if _DATASET.value == "hindsight_full_file":
                        sim = simulate_hindsight_full_file(
                            datum, model_client, rate_limiter
                        )
                    else:
                        sim = simulate_hindsight(datum, model_client, rate_limiter)
                    if has_type_event(sim.events):
                        f.write(sim.model_dump_json() + "\n")
                    f_p.write(datum.completion.request_id + "\n")
                except Exception as e:
                    print(f"{datum.completion.request_id}: {e}")
                    counter["failure"] += 1
                counter["success"] += 1


if __name__ == "__main__":
    app.run(main)

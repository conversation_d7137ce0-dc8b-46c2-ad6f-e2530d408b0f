See at which tokens the completion model makes mistakes.

Can we predict the wrong token location?
Can we stop before the wrong token location?
Can we make people accept more tokens by the smart stop?


# Simulation
We simulation a greedy algorithm that user accepts as much prefix as possible and type a single character when no prefix can be accepted.

See the [viewer for results](https://research-webserver.tenant-augment-eng.las1.ingress.coreweave.cloud/vzhao/smart_stop/simulations/viewer.html?file_path=hindsight_eldenv7-0-15b.jsonl&task_id=00484346-6723-4a31-84d9-1dd57be6af8e).
r"""Metrics for tokens per intervention.

Usage:

python experimental/vzhao/202409_completion_smart_stop/chars_per_intervention.py \
--rst_file=public_html/vzhao/smart_stop/simulations/cceval_eldenv7-0-15b.jsonl
"""

import collections
import importlib
from pathlib import Path

from absl import app, flags
from research.core.data_paths import canonicalize_path

common = importlib.import_module(
    "experimental.vzhao.202409_completion_smart_stop.common"
)
Simulation = common.Simulation

_RST_FILE = flags.DEFINE_string(
    "rst_file",
    default=None,
    help="Path to the rst file.",
    required=True,
)


def count_tokens(sim: Simulation) -> collections.Counter:
    counter = collections.Counter()
    for e in sim.events:
        if isinstance(e, common.CompletionEvent):
            counter["accepted"] += len(e.accepted)
        elif isinstance(e, common.TypeEvent):
            counter["typed"] += len(e.typed)
    return counter


def count_tokens_hindsight(task_ids_no_typed: set) -> collections.Counter:
    """Count the number of tokens in hindsight.

    Args:
        task_ids_no_typed: The task ids that have no typed events.
    """
    counter = collections.Counter()
    dataset = common.highsight_dataset()
    for d in dataset:
        if d.completion.request_id in task_ids_no_typed:
            counter["accepted"] += len(d.ground_truth)
    return counter


def count_tokens_cceval(task_ids_no_typed: set) -> collections.Counter:
    """Count the number of tokens in cceval.

    Args:
        task_ids_no_typed: The task ids that have no typed events.
    """
    counter = collections.Counter()
    all_patches_by_repo, _ = common.cceval_data()
    for repo in all_patches_by_repo:
        for p in all_patches_by_repo[repo]:
            if p.metadata.task_id in task_ids_no_typed:
                counter["accepted"] += len(p.groundtruth)
    return counter


def count_partial_accepted_events(sim: Simulation) -> collections.Counter:
    """Count the number of partial accepted events."""
    counter = collections.Counter()
    last_event_accepted_non_empty = False
    for e in sim.events:
        if isinstance(e, common.CompletionEvent):
            if len(e.accepted) > 0:
                counter["accepted"] += 1
                last_event_accepted_non_empty = True
            elif last_event_accepted_non_empty:
                # We count consecutive rejected events as one rejected event.
                # If we know you need to type multiple characters before next accept, we count it as one
                # rejected event because ideally we don't show completions during typing.
                counter["rejected"] += 1
                last_event_accepted_non_empty = False
    return counter


def main(argv) -> None:
    simulations = {}
    rst_file = Path(canonicalize_path(_RST_FILE.value))
    with open(rst_file, "r") as f:
        for line in f.readlines():
            sim = common.Simulation.parse_raw(line)
            simulations[sim.task_id] = sim

    done_path = rst_file.parent / f"{rst_file.stem}_done"
    all_task_ids = set()
    with open(done_path, "r") as f:
        for line in f.readlines():
            all_task_ids.add(line.strip())
    task_ids_no_typed = all_task_ids - set(simulations.keys())

    # Count chars in Simulations.
    counter = collections.Counter()
    for sim in simulations.values():
        counter.update(count_tokens(sim))
    # Count chars for the rest of task ids.
    if "hindsight" in rst_file.stem:
        counter.update(count_tokens_hindsight(task_ids_no_typed))
    elif "cceval" in rst_file.stem:
        counter.update(count_tokens_cceval(task_ids_no_typed))
    else:
        raise ValueError(f"Unrecognized rst file: {rst_file}")
    print(f"Total accepted tokens: {counter['accepted']}")
    print(f"Total typed tokens: {counter['typed']}")
    print(
        f"Tokens per intervention: {(counter['accepted'] + counter['typed']) / counter['typed']}"
    )

    # Estimate Accept Rate
    counter = collections.Counter()
    for sim in simulations.values():
        counter.update(count_partial_accepted_events(sim))
    # WARNING: For the tasks that have no typed events, we assume there is one accept
    # per task. In reality, there could be multiple accepts.
    counter["accepted"] += len(task_ids_no_typed)
    print(f"Accept rate: {counter['accepted'] / counter.total()}")


if __name__ == "__main__":
    app.run(main)

# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/api/ethanol_plus/dffb1m_16b_1025_7b_1pos_32total_03_08_lp_fp_lr_1e-6.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  # name: diffb1m_16b_alphal_fimv2, 1025_7b_1pos_32total_03_08_lp_fp_lr_1e-6, 30LineChunk, finegrained-python.large
  workspace: Dev
  project: vzhao-eval
import_modules:
  - experimental.vzhao.20231129_star_ethanol.modeling.ethanol
  - experimental.vzhao.20231129_star_ethanol.modeling.chunking_functions
  - research.retrieval.libraries.chunk_formatters
  - research.core.prompt_formatters
system:
  name: basic_rag
  # model:
  #   checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
  #   name: rogue
  #   prompt:
  #     max_prefix_tokens: 1280
  #     max_prompt_tokens: 3816
  #     max_retrieved_chunk_tokens: -1
  #     max_suffix_tokens: 768
  #     # NOTE: Change this to control the max number of retrieved chunks in the prompt.
  #     max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    # scorer:
    #   name: starcoder_1b
    #   checkpoint: star_ethanol/1b_16.1_500_step
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    # query_formatter:
    #   name: ethanol6_query
    #   max_tokens: 1023
    #   add_path: True
    #   add_suffix: True
    #   prefix_ratio: 0.9
    #   tokenizer_name: StarCoderTokenizer
    # document_formatter:
    #   name: ethanol6_document
    #   max_tokens: 999
    #   add_path: True
    #   tokenizer_name: StarCoderTokenizer
  experimental:
    remove_suffix: False
    retriever_top_k: 100
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: api
  dataset: finegrained-python.large
podspec: 1xA100.yaml

# includes is an ordered list of gpt-neox config files to be loaded
# Usage:
# python research/gpt-neox/jobs/experiment.py --metaconfig experimental/vzhao/modeling/20231013_label_balance_path/20231029_ethanol_plus_1b_6pos_32total_dsam2.yml
# DET LINK:
# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/25601/overview
# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/25623/overview
# LR Decay to 1000 steps:
# Query/Doc formatter:
# {
#     "encoder_seq_length": 1024,
#     "input_path": "s3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample_2/",
#     "key_formatter": {
#         "add_path": true,
#         "max_tokens": 1023,
#         "name": "simple_document"
#     },
#     "max_workers": 64,
#     "output_path": "/mnt/efs/augment/user/vincent/data/ppl_gain/1029_6pos_32total_dsam2",
#     "parameters": {
#         "max_neg_ppg": 0.03,
#         "min_pos_ppg": 0.12,
#         "min_pos_ppl": -1,
#         "num_positives": 6,
#         "total_chunks": 32
#     },
#     "query_formatter": {
#         "add_path": true,
#         "max_lines": -1,
#         "max_tokens": 1023,
#         "name": "ethanol3_query",
#         "retokenize": true
#     },
#     "s3root": "s3a://augment-temporary/vzhao/ppl_gain/1029_6pos_32total_dsam2",
#     "skip_tokenize": false
# }

includes:
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/ethanol.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/conan-350M.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/loss_scale-128-w8.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: &exp_name ethanol_plus_1b_6pos_32total_dsam2_lr-2e-5
  description: null
  workspace: Dev
  project: vzhao
  labels: ["ethanol_plus", "test"]
  perform_initial_validation: False # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 8 # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc: True # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  wandb_project: vzhao-exp
  wandb_name: *exp_name
  wandb_group: ethanol_plus
  # mode
  ppl_distill: true

  # training batch & schedule
  seq_length: 1024
  # Currently, the loss assumes one query per micro batch.
  train_micro_batch_size_per_gpu: 32
  # This is effectively batch size per gpu.
  gradient_accumulation_steps: 32
  # train_batch_size / train_micro_batch_size_per_gpu is effectively batch size.
  train_batch_size: 8192
  train_iters: 1000
  lr_decay_iters: 1000
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 999999999999 # Never
  eval_iters: 1
  save_interval: 100

  # load checkpoint
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  data-path: null
  train_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1029_6pos_32total_dsam2/dataset
  valid_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1029_6pos_32total_dsam2/validation_dataset
  test_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1029_6pos_32total_dsam2/validation_dataset
  max_valid_data_size: 8192

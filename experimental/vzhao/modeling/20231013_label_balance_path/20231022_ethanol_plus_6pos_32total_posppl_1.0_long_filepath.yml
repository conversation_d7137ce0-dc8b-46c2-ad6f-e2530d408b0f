# includes is an ordered list of gpt-neox config files to be loaded
# Usage:
# python research/gpt-neox/jobs/experiment.py --metaconfig /home/<USER>/augment/experimental/vzhao/modeling/20231013_label_balance_path/20231022_ethanol_plus_6pos_32total_posppl_1.0_long_filepath.yml
# DET LINK:
# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/24913/overview
# LR Decay to 1000 steps:
# Query/Doc formatter:
# CONFIG["stage_7"] = dict(
#     encoder_seq_length=1024,
#     query_formatter={
#         "name": "ethanol3_query",
#         "max_tokens": 1024 - 1,
#         "max_lines": -1,
#         "add_path": True,
#         "retokenize": True,
#     },
#     key_formatter={
#         "name": "simple_document",
#         "add_path": True,
#         "max_tokens": 1024 - 1,
#     },
#     max_retrieved_docs=32 - 1,
# )
#
# Download:
# bash research/utils/download_checkpoint.sh 8fafc2e4-795c-4907-9646-8c78562f7542 ethanol_plus/6pos_32total_posppl_1.0_long_filepath

includes:
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/ethanol.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/conan-350M.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-6.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/loss_scale-128-w8.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: &exp_name ethanol_plus_6pos_32total_posppl_1.0_long_filepath_2e-6
  description: null
  workspace: Dev
  project: vzhao
  labels: ["ethanol_plus", "test"]
  perform_initial_validation: False # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 8 # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc: True # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  wandb_project: vzhao-exp
  wandb_name: *exp_name
  wandb_group: ethanol_plus
  # mode
  ppl_distill: true

  # training batch & schedule
  seq_length: 1024
  # Currently, the loss assumes one query per micro batch.
  train_micro_batch_size_per_gpu: 32
  # This is effectively batch size per gpu.
  gradient_accumulation_steps: 32
  # train_batch_size / train_micro_batch_size_per_gpu is effectively batch size.
  train_batch_size: 8192
  train_iters: 1000
  lr_decay_iters: 1000
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 999999999999 # Never
  eval_iters: 1
  save_interval: 100

  # load checkpoint
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  data-path: null
  train_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1022_6pos_32total_posppl_1.0_long_filepath/dataset
  valid_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1022_6pos_32total_posppl_1.0_long_filepath/validation_dataset
  test_data_paths:
    - /mnt/efs/augment/user/vincent/data/ppl_gain/1022_6pos_32total_posppl_1.0_long_filepath/validation_dataset
  max_valid_data_size: 8192

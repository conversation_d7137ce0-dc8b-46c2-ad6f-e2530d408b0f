{"cells": [{"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [], "source": ["import sentencepiece as spm\n", "\n", "GEMMA_TOKENIZER_PATH = \"/mnt/efs/augment/user/yury/gemma/tokenizer.model\"\n", "tk = spm.SentencePieceProcessor()\n", "tk.Load(GEMMA_TOKENIZER_PATH)\n", "tk_len = lambda x: len(tk.tokenize(x))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Initialize Gemini API and loads the retriever."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Init Gemini API"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! 👋  How can I help you today? 😊 \n", "\n"]}], "source": ["# @title Initialize Gemini API.\n", "import vertexai\n", "from vertexai.generative_models import (\n", "    ChatSession,\n", "    GenerationConfig,\n", "    GenerativeModel,\n", "    SafetySetting,\n", ")\n", "from vertexai import generative_models\n", "\n", "# This is Augment GCP account.\n", "project_id = \"system-services-dev\"\n", "location = \"us-west1\"\n", "vertexai.init(project=project_id, location=location)\n", "\n", "# generation_config = GenerationConfig(\n", "#     candidate_count=1,\n", "#     temperature=0.7,\n", "#     max_output_tokens=8192,\n", "# )\n", "generation_config = GenerationConfig(\n", "    candidate_count=1,\n", "    temperature=0.7,\n", "    top_p=0.95,\n", "    # top_k=64,\n", "    max_output_tokens=8192,\n", "    response_mime_type=\"text/plain\",\n", ")\n", "GOOGLE_MODEL = GenerativeModel(\n", "    # model_name=\"gemini-1.5-pro-preview-0409\",\n", "    model_name=\"gemini-1.5-pro\",\n", "    # model_name=\"gemini-1.5-flash\",\n", "    generation_config=generation_config,\n", ")\n", "responses = GOOGLE_MODEL.generate_content(\n", "    \"hello\", stream=False, generation_config=generation_config\n", ")\n", "print(responses.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Init llama cpp "]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" I'm an AI assistant, so I don't have feelings, but I'm here to help you with any questions or tasks you have!\n"]}], "source": ["from research.llm_apis.chat_utils import DeepSeekCoderV2ChatClient\n", "\n", "address = \"**************:8080\"\n", "chat_client = DeepSeekCoderV2ChatClient(address=address, timeout=1000)\n", "\n", "\n", "print(chat_client.generate(messages=[\"how are you?\"], max_tokens=2048))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_32k.json\"\n", "# file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_64k.json\"\n", "# file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_128k.json\"\n", "file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/DeepFaceLab/deepseek_api/vanilla_chatanol_119k.json\"\n", "\n", "with open(file, \"r\") as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The overall architecture of the DeepFaceLab application is a modular and extensible design that integrates various components for face manipulation tasks such as extraction, training, and merging. Here is a high-level overview of the architecture:\n", "\n", "1. **Core Components**:\n", "   - **DFLIMG**: Handles the loading and manipulation of DFL image files, which contain metadata about the face images.\n", "   - **Facelib**: Contains libraries for face detection, landmarks extraction, and other face-related operations.\n", "   - **Leras**: A lightweight neural network library based on TensorFlow, used for building and training models.\n", "   - **Samplelib**: Manages the loading and processing of sample data, including face images and masks.\n", "   - **Models**: Defines different model architectures for face manipulation tasks.\n", "   - **Merger**: Contains classes and functions for merging faces using trained models.\n", "   - **Main Scripts**: Provides the main entry points for various tasks such as extraction, training, and merging.\n", "\n", "2. **Data Flow**:\n", "   - **Face Extraction**: The application uses the `Extractor` class to extract faces from images or videos, leveraging `S3FDExtractor` and `FANExtractor` from `facelib` for detection and landmarks extraction.\n", "   - **Training**: The `Trainer` class initializes and manages the training process, using models defined in the `models` directory. The `SampleGenerator` classes from `samplelib` are used to feed data into the training process.\n", "   - **Merging**: The `Merger` class uses trained models to merge faces into target images or videos, applying various configurations and optimizations.\n", "\n", "3. **Model Architectures**:\n", "   - **AMPModel**: A model architecture for advanced face manipulation tasks.\n", "   - **QModel**: A quick model architecture for faster face manipulation.\n", "   - **SAEHDModel**: A model architecture for high-definition face manipulation.\n", "   - **XSegModel**: A model architecture specifically for face segmentation tasks.\n", "\n", "4. **User Interaction**:\n", "   - The application provides a command-line interface (`main.py`) for users to interact with various functionalities.\n", "   - The `InteractiveMergerSubprocessor` class in the `merger` directory allows for interactive face merging sessions.\n", "\n", "5. **Parallel Processing**:\n", "   - The application utilizes multiprocessing and threading for efficient data loading and processing (`MPSharedList`, `Subprocessor`, `SubprocessGenerator`, etc.).\n", "\n", "6. **Data Storage**:\n", "   - The application supports packing and unpacking of facesets using `PackedFaceset` in `samplelib`.\n", "   - Metadata and model weights are saved and loaded using pickle and numpy for serialization.\n", "\n", "In summary, the DeepFaceLab application is structured around a core set of libraries and modules that handle face extraction, training, and merging. It leverages TensorFlow for neural network operations and provides a command-line interface for user interaction. The architecture is designed to be modular, allowing for easy extension and customization of model architectures and processing pipelines.\n"]}], "source": ["print(data[0]['answer'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"ename": "ConnectionError", "evalue": "HTTPConnectionPool(host='**************', port=8080): Read timed out.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTimeoutError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:444\u001b[0m, in \u001b[0;36mHTTPResponse._error_catcher\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    443\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 444\u001b[0m     \u001b[39myield\u001b[39;00m\n\u001b[1;32m    446\u001b[0m \u001b[39mexcept\u001b[39;00m SocketTimeout:\n\u001b[1;32m    447\u001b[0m     \u001b[39m# FIXME: Ideally we'd like to include the url in the ReadTimeoutError but\u001b[39;00m\n\u001b[1;32m    448\u001b[0m     \u001b[39m# there is yet no clean way to get at it from this context.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:828\u001b[0m, in \u001b[0;36mHTTPResponse.read_chunked\u001b[0;34m(self, amt, decode_content)\u001b[0m\n\u001b[1;32m    827\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[0;32m--> 828\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_update_chunk_length()\n\u001b[1;32m    829\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mchunk_left \u001b[39m==\u001b[39m \u001b[39m0\u001b[39m:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:758\u001b[0m, in \u001b[0;36mHTTPResponse._update_chunk_length\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    757\u001b[0m     \u001b[39mreturn\u001b[39;00m\n\u001b[0;32m--> 758\u001b[0m line \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_fp\u001b[39m.\u001b[39mfp\u001b[39m.\u001b[39mreadline()\n\u001b[1;32m    759\u001b[0m line \u001b[39m=\u001b[39m line\u001b[39m.\u001b[39msplit(\u001b[39mb\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m;\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m1\u001b[39m)[\u001b[39m0\u001b[39m]\n", "File \u001b[0;32m/opt/conda/lib/python3.11/socket.py:706\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    705\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 706\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sock\u001b[39m.\u001b[39;49mrecv_into(b)\n\u001b[1;32m    707\u001b[0m \u001b[39mexcept\u001b[39;00m timeout:\n", "\u001b[0;31mTimeoutError\u001b[0m: timed out", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mReadTimeoutError\u001b[0m                          Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/models.py:816\u001b[0m, in \u001b[0;36mResponse.iter_content.<locals>.generate\u001b[0;34m()\u001b[0m\n\u001b[1;32m    815\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 816\u001b[0m     \u001b[39myield from\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mraw\u001b[39m.\u001b[39mstream(chunk_size, decode_content\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[1;32m    817\u001b[0m \u001b[39mexcept\u001b[39;00m ProtocolError \u001b[39mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:624\u001b[0m, in \u001b[0;36mHTTPResponse.stream\u001b[0;34m(self, amt, decode_content)\u001b[0m\n\u001b[1;32m    623\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mchunked \u001b[39mand\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39msupports_chunked_reads():\n\u001b[0;32m--> 624\u001b[0m     \u001b[39mfor\u001b[39;49;00m line \u001b[39min\u001b[39;49;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mread_chunked(amt, decode_content\u001b[39m=\u001b[39;49mdecode_content):\n\u001b[1;32m    625\u001b[0m         \u001b[39myield\u001b[39;49;00m line\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:816\u001b[0m, in \u001b[0;36mHTTPResponse.read_chunked\u001b[0;34m(self, amt, decode_content)\u001b[0m\n\u001b[1;32m    811\u001b[0m     \u001b[39mraise\u001b[39;00m BodyNotHttplibCompatible(\n\u001b[1;32m    812\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mBody should be http.client.HTTPResponse like. \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    813\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mIt should have have an fp attribute which returns raw chunks.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    814\u001b[0m     )\n\u001b[0;32m--> 816\u001b[0m \u001b[39mwith\u001b[39;49;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_error_catcher():\n\u001b[1;32m    817\u001b[0m     \u001b[39m# Don't bother reading the body of a HEAD request.\u001b[39;49;00m\n\u001b[1;32m    818\u001b[0m     \u001b[39mif\u001b[39;49;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_original_response \u001b[39mand\u001b[39;49;00m is_response_to_head(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_original_response):\n", "File \u001b[0;32m/opt/conda/lib/python3.11/contextlib.py:158\u001b[0m, in \u001b[0;36m_GeneratorContextManager.__exit__\u001b[0;34m(self, typ, value, traceback)\u001b[0m\n\u001b[1;32m    157\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 158\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mgen\u001b[39m.\u001b[39mthrow(typ, value, traceback)\n\u001b[1;32m    159\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mStopIteration\u001b[39;00m \u001b[39mas\u001b[39;00m exc:\n\u001b[1;32m    160\u001b[0m     \u001b[39m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[1;32m    161\u001b[0m     \u001b[39m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[1;32m    162\u001b[0m     \u001b[39m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/response.py:449\u001b[0m, in \u001b[0;36mHTTPResponse._error_catcher\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    446\u001b[0m \u001b[39mexcept\u001b[39;00m SocketTimeout:\n\u001b[1;32m    447\u001b[0m     \u001b[39m# FIXME: Ideally we'd like to include the url in the ReadTimeoutError but\u001b[39;00m\n\u001b[1;32m    448\u001b[0m     \u001b[39m# there is yet no clean way to get at it from this context.\u001b[39;00m\n\u001b[0;32m--> 449\u001b[0m     \u001b[39mraise\u001b[39;00m ReadTimeoutError(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_pool, \u001b[39mNone\u001b[39;00m, \u001b[39m\"\u001b[39m\u001b[39mRead timed out.\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m    451\u001b[0m \u001b[39mexcept\u001b[39;00m BaseSSLError \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    452\u001b[0m     \u001b[39m# FIXME: Is there a better way to differentiate between SSLErrors?\u001b[39;00m\n", "\u001b[0;31mReadTimeoutError\u001b[0m: HTTPConnectionPool(host='**************', port=8080): Read timed out.", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mConnectionError\u001b[0m                           Traceback (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb Cell 8\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mfor\u001b[39;49;00m response \u001b[39min\u001b[39;49;00m chat_client\u001b[39m.\u001b[39;49mgenerate_stream(\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     messages\u001b[39m=\u001b[39;49m[data[\u001b[39m2\u001b[39;49m][\u001b[39m\"\u001b[39;49m\u001b[39mprompt\u001b[39;49m\u001b[39m\"\u001b[39;49m]],\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m     max_tokens\u001b[39m=\u001b[39;49m\u001b[39m2048\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m ):\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m     \u001b[39mprint\u001b[39;49m(response, end\u001b[39m=\u001b[39;49m\u001b[39m\"\u001b[39;49m\u001b[39m\"\u001b[39;49m)\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/20240620_summary/retrieve_expand_answer.ipynb#Y113sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m     \u001b[39mbreak\u001b[39;49;00m\n", "File \u001b[0;32m~/augment/research/llm_apis/chat_utils.py:508\u001b[0m, in \u001b[0;36mDeepSeekCoderV2ChatClient.generate_stream\u001b[0;34m(self, messages, max_tokens, system_prompt)\u001b[0m\n\u001b[1;32m    501\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mgenerate_stream\u001b[39m(\n\u001b[1;32m    502\u001b[0m     \u001b[39mself\u001b[39m,\n\u001b[1;32m    503\u001b[0m     messages: \u001b[39mlist\u001b[39m[\u001b[39mstr\u001b[39m],\n\u001b[1;32m    504\u001b[0m     max_tokens: \u001b[39mint\u001b[39m,\n\u001b[1;32m    505\u001b[0m     system_prompt: Optional[\u001b[39mstr\u001b[39m] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m,\n\u001b[1;32m    506\u001b[0m ) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m Iterable[\u001b[39mstr\u001b[39m]:\n\u001b[1;32m    507\u001b[0m     prompt \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_prepare_prompt_text(messages, system_prompt)\n\u001b[0;32m--> 508\u001b[0m     \u001b[39mfor\u001b[39;49;00m response \u001b[39min\u001b[39;49;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mclient\u001b[39m.\u001b[39;49mgenerate_stream(\n\u001b[1;32m    509\u001b[0m         prompt\u001b[39m=\u001b[39;49mprompt, max_generated_tokens\u001b[39m=\u001b[39;49mmax_tokens\n\u001b[1;32m    510\u001b[0m     ):\n\u001b[1;32m    511\u001b[0m         \u001b[39myield\u001b[39;49;00m response\n", "File \u001b[0;32m~/augment/research/llm_apis/completion_utils.py:384\u001b[0m, in \u001b[0;36mLlamaCppClient.generate_stream\u001b[0;34m(self, prompt, max_generated_tokens, temperature, stop)\u001b[0m\n\u001b[1;32m    375\u001b[0m payload[\u001b[39m\"\u001b[39m\u001b[39mstream\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n\u001b[1;32m    377\u001b[0m \u001b[39mwith\u001b[39;00m requests\u001b[39m.\u001b[39mpost(\n\u001b[1;32m    378\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39murl,\n\u001b[1;32m    379\u001b[0m     headers\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mheaders,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    382\u001b[0m     stream\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m,\n\u001b[1;32m    383\u001b[0m ) \u001b[39mas\u001b[39;00m http_response:\n\u001b[0;32m--> 384\u001b[0m     \u001b[39mfor\u001b[39;49;00m line \u001b[39min\u001b[39;49;00m http_response\u001b[39m.\u001b[39;49miter_lines():\n\u001b[1;32m    385\u001b[0m         \u001b[39m# Example model response: data: {\"content\":\"?\",\"stop\":false,\"id_slot\":0,\"multimodal\":false}\u001b[39;49;00m\n\u001b[1;32m    386\u001b[0m         serialized_json \u001b[39m=\u001b[39;49m (\n\u001b[1;32m    387\u001b[0m             line\u001b[39m.\u001b[39;49mdecode(encoding\u001b[39m=\u001b[39;49m\u001b[39m\"\u001b[39;49m\u001b[39mutf-8\u001b[39;49m\u001b[39m\"\u001b[39;49m)\u001b[39m.\u001b[39;49mreplace(\u001b[39m\"\u001b[39;49m\u001b[39mdata: \u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39m\"\u001b[39;49m\u001b[39m\"\u001b[39;49m)\u001b[39m.\u001b[39;49mstrip()\n\u001b[1;32m    388\u001b[0m         )\n\u001b[1;32m    389\u001b[0m         \u001b[39mif\u001b[39;49;00m \u001b[39mnot\u001b[39;49;00m serialized_json:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/models.py:865\u001b[0m, in \u001b[0;36mResponse.iter_lines\u001b[0;34m(self, chunk_size, decode_unicode, delimiter)\u001b[0m\n\u001b[1;32m    856\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"Iterates over the response data, one line at a time.  When\u001b[39;00m\n\u001b[1;32m    857\u001b[0m \u001b[39mstream=True is set on the request, this avoids reading the\u001b[39;00m\n\u001b[1;32m    858\u001b[0m \u001b[39mcontent at once into memory for large responses.\u001b[39;00m\n\u001b[1;32m    859\u001b[0m \n\u001b[1;32m    860\u001b[0m \u001b[39m.. note:: This method is not reentrant safe.\u001b[39;00m\n\u001b[1;32m    861\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m    863\u001b[0m pending \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[0;32m--> 865\u001b[0m \u001b[39mfor\u001b[39;49;00m chunk \u001b[39min\u001b[39;49;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49miter_content(\n\u001b[1;32m    866\u001b[0m     chunk_size\u001b[39m=\u001b[39;49mchunk_size, decode_unicode\u001b[39m=\u001b[39;49mdecode_unicode\n\u001b[1;32m    867\u001b[0m ):\n\u001b[1;32m    869\u001b[0m     \u001b[39mif\u001b[39;49;00m pending \u001b[39mis\u001b[39;49;00m \u001b[39mnot\u001b[39;49;00m \u001b[39mNone\u001b[39;49;00m:\n\u001b[1;32m    870\u001b[0m         chunk \u001b[39m=\u001b[39;49m pending \u001b[39m+\u001b[39;49m chunk\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/models.py:822\u001b[0m, in \u001b[0;36mResponse.iter_content.<locals>.generate\u001b[0;34m()\u001b[0m\n\u001b[1;32m    820\u001b[0m     \u001b[39mraise\u001b[39;00m ContentDecodingError(e)\n\u001b[1;32m    821\u001b[0m \u001b[39mexcept\u001b[39;00m ReadTimeoutError \u001b[39mas\u001b[39;00m e:\n\u001b[0;32m--> 822\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m(e)\n\u001b[1;32m    823\u001b[0m \u001b[39mexcept\u001b[39;00m SSLError \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    824\u001b[0m     \u001b[39mraise\u001b[39;00m RequestsSSLError(e)\n", "\u001b[0;31mConnectionError\u001b[0m: HTTPConnectionPool(host='**************', port=8080): Read timed out."]}], "source": ["for response in chat_client.generate_stream(\n", "    messages=[data[2][\"prompt\"]],\n", "    max_tokens=2048,\n", "):\n", "    print(response, end=\"\")\n", "    break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deepseek API "]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "with open('/home/<USER>/.config/deepseek/api_token', 'r') as f:\n", "    api_key = f.read().strip()\n", "\n", "deepseek_client = OpenAI(api_key=api_key, base_url=\"https://api.deepseek.com\")\n"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n"]}], "source": ["response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant\"},\n", "        {\"role\": \"user\", \"content\": \"Hello\"},\n", "    ],\n", "    stream=False\n", ")\n", "\n", "print(response.choices[0].message.content)\n"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello! How can I assist you today?'"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["utils.deepseek_api_answer(\"Hello\", client)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Esitmate TTFT"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["35332\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5/5 [02:01<00:00, 24.21s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["24.213396501541137\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import json\n", "import tqdm\n", "import numpy as np\n", "\n", "file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_32k.json\"\n", "# file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_64k.json\"\n", "# file = \"/mnt/efs/augment/public_html/vzhao/summary_questions/augment/gemini_1.5_pro/vanilla_chatanol_128k.json\"\n", "\n", "with open(file, \"r\") as f:\n", "    data = json.load(f)\n", "prompt = data[2][\"prompt\"]\n", "print(utils.tk_len(prompt))\n", "\n", "import time\n", "\n", "ttfts = []\n", "\n", "for d in tqdm.tqdm(data[:5]):\n", "    prompt = d[\"prompt\"]\n", "    responses = GOOGLE_MODEL.generate_content(prompt, stream=True)\n", "    # responses = deepseek_client.chat.completions.create(\n", "    #     model=\"deepseek-chat\",\n", "    #     messages=[\n", "    #         {\"role\": \"system\", \"content\": \"You are a helpful assistant\"},\n", "    #         {\"role\": \"user\", \"content\": prompt},\n", "    #     ],\n", "    #     stream=True\n", "    # )\n", "    start_time = time.time()\n", "    first_token_time = None\n", "    for response in responses:\n", "        if first_token_time is None:\n", "            first_token_time = time.time()\n", "            ttfts.append(first_token_time - start_time)\n", "        break\n", "\n", "\n", "print(np.mean(ttfts))"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["'What is the folder structure and naming convention?'"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["data[4]['question']"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The folder structure and naming convention for the Augmented-Stack dataset is as follows:\n", "\n", "```\n", "/mnt/efs/spark-data/shared/aug-stack/\n", "├── v1\n", "│   └── filtered\n", "│       └── ... (Parquet files for the initial Augmented-Stack dataset)\n", "├── deduped_0_7\n", "│   └── ... (Parquet files for the soft-deduplicated Augmented-Stack dataset)\n", "├── by-repo\n", "│   └── ... (Parquet files for the Augmented-Stack dataset grouped by repos)\n", "└── _intermediate\n", "    ├── pre-fim\n", "    │   └── ... (Parquet files for samples before FIM processing)\n", "    └── pre-indexing\n", "        └── ... (Parquet files for samples after FIM processing but before indexing)\n", "```\n", "\n", "**Explanation:**\n", "\n", "- **v1/filtered:** This folder contains the initial Augmented-Stack dataset after filtering out unfavorable languages/extensions and low-quality files. The data is stored in Parquet format.\n", "- **deduped_0_7:** This folder contains the soft-deduplicated Augmented-Stack dataset. The data is stored in Parquet format.\n", "- **by-repo:** This folder contains the Augmented-Stack dataset grouped by repositories. The data is stored in Parquet format.\n", "- **_intermediate:** This folder contains intermediate datasets generated during the data processing pipeline.\n", "    - **pre-fim:** This subfolder contains Parquet files for samples before FIM (Fill-in-the-Middle) processing.\n", "    - **pre-indexing:** This subfolder contains Parquet files for samples after FIM processing but before indexing.\n", "\n", "**Naming Convention:**\n", "\n", "- The dataset names are descriptive and indicate the stage of processing (e.g., `filtered`, `deduped`, `by-repo`).\n", "- Intermediate datasets are prefixed with an underscore (`_`) to indicate their temporary nature.\n", "- The version number (`v1`) is included in the folder structure to allow for future updates to the dataset.\n", "\n", "**File Naming:**\n", "\n", "- Parquet files are typically named according to the partition they belong to (e.g., language, repository).\n", "\n", "**Example File Path:**\n", "\n", "`/mnt/efs/spark-data/shared/aug-stack/by-repo/lang=python/repo=google/gson/part-00000-tid-1234567890123456789-123456.parquet`\n", "\n", "This file path indicates that the file contains data for the Python language, from the repository `google/gson`, and is part of a specific partition.\n", "\n", "This folder structure and naming convention ensures that the Augmented-Stack dataset is well-organized and easy to navigate. It also allows for efficient data processing and retrieval.\n", "\n"]}], "source": ["print(data[4]['answer'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<p>The overall architecture of the DeepFaceLab application is a modular and extensible design that integrates various components for face manipulation tasks such as extraction, training, and merging. Here is a high-level overview of the architecture:</p>\n", "<ol>\n", "<li><strong>Core Components</strong>:</li>\n", "<li><strong>DFLIMG</strong>: Handles the loading and manipulation of DFL image files, which contain metadata about the face images.</li>\n", "<li><strong>Facelib</strong>: Contains libraries for face detection, landmarks extraction, and other face-related operations.</li>\n", "<li><strong>Leras</strong>: A lightweight neural network library based on TensorFlow, used for building and training models.</li>\n", "<li><strong>Sam<PERSON>lib</strong>: Manages the loading and processing of sample data, including face images and masks.</li>\n", "<li><strong>Models</strong>: Defines different model architectures for face manipulation tasks.</li>\n", "<li><strong>Merger</strong>: Contains classes and functions for merging faces using trained models.</li>\n", "<li>\n", "<p><strong>Main Scripts</strong>: Provides the main entry points for various tasks such as extraction, training, and merging.</p>\n", "</li>\n", "<li>\n", "<p><strong>Data Flow</strong>:</p>\n", "</li>\n", "<li><strong>Face Extraction</strong>: The application uses the <code>Extractor</code> class to extract faces from images or videos, leveraging <code>S3FDExtractor</code> and <code>FANExtractor</code> from <code>facelib</code> for detection and landmarks extraction.</li>\n", "<li><strong>Training</strong>: The <code>Trainer</code> class initializes and manages the training process, using models defined in the <code>models</code> directory. The <code>SampleGenerator</code> classes from <code>samplelib</code> are used to feed data into the training process.</li>\n", "<li>\n", "<p><strong>Merging</strong>: The <code>Merger</code> class uses trained models to merge faces into target images or videos, applying various configurations and optimizations.</p>\n", "</li>\n", "<li>\n", "<p><strong>Model Architectures</strong>:</p>\n", "</li>\n", "<li><strong>AMPModel</strong>: A model architecture for advanced face manipulation tasks.</li>\n", "<li><strong>QModel</strong>: A quick model architecture for faster face manipulation.</li>\n", "<li><strong>SAEHDModel</strong>: A model architecture for high-definition face manipulation.</li>\n", "<li>\n", "<p><strong>XSegModel</strong>: A model architecture specifically for face segmentation tasks.</p>\n", "</li>\n", "<li>\n", "<p><strong>User Interaction</strong>:</p>\n", "</li>\n", "<li>The application provides a command-line interface (<code>main.py</code>) for users to interact with various functionalities.</li>\n", "<li>\n", "<p>The <code>InteractiveMergerSubprocessor</code> class in the <code>merger</code> directory allows for interactive face merging sessions.</p>\n", "</li>\n", "<li>\n", "<p><strong>Parallel Processing</strong>:</p>\n", "</li>\n", "<li>\n", "<p>The application utilizes multiprocessing and threading for efficient data loading and processing (<code>MPSharedList</code>, <code>Subprocessor</code>, <code>SubprocessGenerator</code>, etc.).</p>\n", "</li>\n", "<li>\n", "<p><strong>Data Storage</strong>:</p>\n", "</li>\n", "<li>The application supports packing and unpacking of facesets using <code>PackedFaceset</code> in <code>samplelib</code>.</li>\n", "<li>Metadata and model weights are saved and loaded using pickle and numpy for serialization.</li>\n", "</ol>\n", "<p>In summary, the DeepFaceLab application is structured around a core set of libraries and modules that handle face extraction, training, and merging. It leverages TensorFlow for neural network operations and provides a command-line interface for user interaction. The architecture is designed to be modular, allowing for easy extension and customization of model architectures and processing pipelines.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import markdown\n", "from IPython import display\n", "\n", "display.HTML(markdown.markdown(data[0]['answer']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> Chatanol"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import importlib\n", "\n", "from research.eval.harness.factories import create_retriever\n", "\n", "utils = importlib.import_module(\"experimental.vzhao.20240620_summary.utils\")\n", "\n", "retriever = create_retriever(utils.CHATANOL_CONFIG)\n", "retriever.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads files from a Repo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loads a dir from disk"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "utils = importlib.import_module(\"experimental.vzhao.20240620_summary.utils\")\n", "PATH_DENYLIST = [\n", "    \"augment/experimental\",\n", "]\n", "\n", "REPO = \"DeepFaceLab\"\n", "GITHUB_URL = \"https://github.com/iperov/DeepFaceLab\"\n", "documents = list(utils.load_files_from_root(\"/home/<USER>/DeepFaceLab\"))\n", "\n", "# REPO = \"jvector\"\n", "# GITHUB_URL = \"https://github.com/jbellis/jvector\"\n", "# documents = list(utils.load_files_from_root(\"/home/<USER>/jvector\"))\n", "\n", "\n", "# REPO = \"Augment\"\n", "# GITHUB_URL = \"https://github.com/augmentcode/augment\"\n", "# documents = list(\n", "#     doc\n", "#     for doc in utils.load_files_from_root(\"/home/<USER>/augment\")\n", "#     if not any(utils.is_subpath(doc.path, p) for p in PATH_DENYLIST)\n", "# )\n", "\n", "\n", "doc_lookup = utils.DocLookUp(documents)\n", "print(len(documents))\n", "\n", "retriever.remove_all_docs()\n", "retriever.add_docs(documents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Experiments"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Full repo QA with <PERSON>.\n", "\n", "Concate all files into a string."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simply concate all files into a string."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def make_prompt_full_repo(question):\n", "    context = utils.concat_docs(documents)\n", "    print(question)\n", "    prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(\n", "        context=context,\n", "        question=question,\n", "    )\n", "    print(tk_len(prompt))\n", "    return prompt\n", "\n", "\n", "METHOD = \"Method: Concate all files and add to the prompt.\""]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "\n", "\n", "prompt = make_prompt_full_repo(question)\n", "\n", "import markdown\n", "from IPython import display\n", "\n", "html = utils.gemini_answer_html(prompt, GOOGLE_MODEL)\n", "\n", "display.HTML(html)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["output = utils.generate_answers(utils.QUESTIONS, make_prompt_full_repo, GOOGLE_MODEL)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "basename = \"full_repo\"\n", "\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.html\", \"w\") as f:\n", "    f.write(utils.format_html(output, REPO, GITHUB_URL, METHOD))\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"w\") as f:\n", "    json.dump([o.to_dict() for o in output], f, indent=2)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"r\") as f:\n", "    data = json.load(f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Indentation based file sketch"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["print(\n", "    utils.collapse_document(\n", "        documents[7],\n", "        level=0,\n", "    ).text\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def make_prompt_all_file_sketch(question):\n", "    context = utils.concat_docs(\n", "        utils.collapse_document(\n", "            d,\n", "            level=1,\n", "        )\n", "        for d in documents\n", "    )\n", "    print(question)\n", "    prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(\n", "        context=context,\n", "        question=question,\n", "    )\n", "    print(tk_len(prompt))\n", "    return prompt\n", "\n", "\n", "METHOD = \"All file sketch: Concate all level-1 file sketches and add to the prompt.\""]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "\n", "prompt = make_prompt_all_file_sketch(question)\n", "\n", "import markdown\n", "from IPython import display\n", "\n", "# html = utils.gemini_answer_html(prompt, GOOGLE_MODEL)\n", "\n", "# display.HTML(html)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["output = utils.generate_answers(\n", "    utils.QUESTIONS, make_prompt_all_file_sketch, GOOGLE_MODEL\n", ")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "basename = \"all_file_sketch\"\n", "\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.html\", \"w\") as f:\n", "    f.write(utils.format_html(output, REPO, GITHUB_URL, METHOD))\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"w\") as f:\n", "    json.dump([o.to_dict() for o in output], f, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> retrieves chunks.\n", "From chunks, we infer the important files. Get the prompt up to a max sequence length."]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["MAX_CONTEXT_LEN = 128_000"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Strategy 1: Use top-k chunks. If multiple chunks for the same file, concatenate them to get a formatted file."]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "def make_prompt_vanilla_chatanol(question):\n", "    chunks, scores = retriever.query(ModelInput(question))\n", "\n", "    final_chunks = utils.strategy_1(chunks, MAX_CONTEXT_LEN)\n", "    context = utils.concat_chunks(final_chunks)\n", "\n", "    prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(\n", "        context=context,\n", "        question=question,\n", "    )\n", "    print(\"Number of unique documents: \", len(final_chunks))\n", "    print(tk_len(prompt))\n", "    return prompt, len(final_chunks)\n", "\n", "\n", "METHOD = f\"Vanilla ChatAnol {MAX_CONTEXT_LEN//1000}k: Concate all files and add to the prompt.\""]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "\n", "prompt, unique_docs = make_prompt_vanilla_chatanol(question)\n", "\n", "import markdown\n", "from IPython import display\n", "\n", "html = utils.gemini_answer_html(prompt, GOOGLE_MODEL)\n", "\n", "display.HTML(html)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["output = utils.generate_answers(\n", "    utils.QUESTIONS, make_prompt_vanilla_chatanol, GOOGLE_MODEL\n", ")"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "basename = f\"vanilla_chatanol_{MAX_CONTEXT_LEN//1000}k\"\n", "\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.html\", \"w\") as f:\n", "    f.write(utils.format_html(output, REPO, GITHUB_URL, METHOD))\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"w\") as f:\n", "    json.dump([o.to_dict() for o in output], f, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Strategy 2: Use top-k chunks to find top-k files (using max pooling). Include full documents in the context."]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "\n", "importlib.reload(utils)\n", "\n", "\n", "def make_prompt_top_file_max_pooling(question):\n", "    print(question)\n", "    chunks, scores = retriever.query(ModelInput(question))\n", "\n", "    final_docs = utils.strategy_2(\n", "        chunks,\n", "        scores,\n", "        doc_lookup,\n", "        MAX_CONTEXT_LEN,\n", "        aggregate_fn=np.max,\n", "        root_dirname=\"DeepFaceLab\",\n", "    )\n", "    context = utils.concat_docs(final_docs)\n", "\n", "    prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(\n", "        context=context,\n", "        question=question,\n", "    )\n", "    print(\"Number of unique documents: \", len(final_docs))\n", "    print(tk_len(prompt))\n", "    return prompt, len(final_docs)\n", "\n", "\n", "METHOD = f\"Top k files max pooling {MAX_CONTEXT_LEN//1000}k: Concate top ranked files and add to the prompt. The file score is defined as the max score of all chunks in that file.\""]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "\n", "prompt, _ = make_prompt_top_file_max_pooling(question)\n", "\n", "import markdown\n", "from IPython import display\n", "\n", "html = utils.gemini_answer_html(prompt, GOOGLE_MODEL)\n", "\n", "display.HTML(html)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["output = utils.generate_answers(\n", "    utils.QUESTIONS, make_prompt_top_file_max_pooling, GOOGLE_MODEL\n", ")"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "basename = f\"top_file_max_pooling_chatanol_{MAX_CONTEXT_LEN//1000}k\"\n", "\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.html\", \"w\") as f:\n", "    f.write(utils.format_html(output, REPO, GITHUB_URL, METHOD))\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"w\") as f:\n", "    json.dump([o.to_dict() for o in output], f, indent=2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "import pathlib\n", "\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "\n", "print(question)\n", "chunks, scores = retriever.query(ModelInput(question))\n", "print(chunks[0].path)\n", "file_tree = utils.FileTree(\n", "    chunks,\n", "    scores,\n", "    pathlib.Path(\"DeepFaceLab\"),\n", "    np.max,\n", ")\n", "df = file_tree.sunburst(max_depth=4)\n", "\n", "rst = file_tree.display_scores(max_depth=5, normalize=False)\n", "rst[:500]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Strategy 3: Use top-k chunks to find top-k files (using meaning pooling). Include full documents in the context."]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "def make_prompt_top_file_mean_pooling(question):\n", "    print(question)\n", "    chunks, scores = retriever.query(ModelInput(question))\n", "\n", "    final_docs = utils.strategy_2(\n", "        chunks,\n", "        scores,\n", "        doc_lookup,\n", "        MAX_CONTEXT_LEN,\n", "        aggregate_fn=np.mean,\n", "        root_dirname=\"DeepFaceLab\",\n", "    )\n", "    context = utils.concat_docs(final_docs)\n", "\n", "    prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(\n", "        context=context,\n", "        question=question,\n", "    )\n", "    print(\"Number of unique documents: \", len(final_docs))\n", "    print(tk_len(prompt))\n", "    return prompt, len(final_docs)\n", "\n", "\n", "METHOD = f\"Top k files mean pooling {MAX_CONTEXT_LEN//1000}k: Concate top ranked files and add to the prompt. The file score is defined as the mean score of all chunks in that file.\""]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "\n", "prompt, _ = make_prompt_top_file_mean_pooling(question)\n", "\n", "import markdown\n", "from IPython import display\n", "\n", "html = utils.gemini_answer_html(prompt, GOOGLE_MODEL)\n", "\n", "display.HTML(html)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["output = utils.generate_answers(\n", "    utils.QUESTIONS, make_prompt_top_file_mean_pooling, GOOGLE_MODEL\n", ")"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "basename = f\"top_file_mean_pooling_chatanol_{MAX_CONTEXT_LEN//1000}k\"\n", "\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.html\", \"w\") as f:\n", "    f.write(utils.format_html(output, REPO, GITHUB_URL, METHOD))\n", "with open(utils.HTML_ROOT / REPO / f\"{basename}.json\", \"w\") as f:\n", "    json.dump([o.to_dict() for o in output], f, indent=2)"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["question = \"Give a high level overview of the project.\"\n", "import pathlib\n", "\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "\n", "print(question)\n", "chunks, scores = retriever.query(ModelInput(question))\n", "print(chunks[0].path)\n", "file_tree = utils.FileTree(\n", "    chunks,\n", "    scores,\n", "    pathlib.Path(\"DeepFaceLab\"),\n", "    np.mean,\n", ")\n", "df = file_tree.sunburst(max_depth=4)\n", "\n", "rst = file_tree.display_scores(max_depth=5, normalize=False)\n", "rst[:500]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Strategy 4: Optimized File Sketches.\n", "\n", "$\\max \\sum \\text{score}(l_i)$ given that total tokens is smaller than budget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from base.ranges.range_types import LineRange\n", "    "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["a = LineRange(0, 10)\n", "b = LineRange(5, 15)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
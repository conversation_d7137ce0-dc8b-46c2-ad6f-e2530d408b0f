"""Python script to run summary QA on a repo.

This script runs long-context QA on a repo using the following methods.
1. Concate all files and add to the prompt.
2. Concate all level-1 file sketches and add to the prompt.
3. Vanilla Chatanol
4. Concate top ranked files and add to the prompt. The file score is defined as the max/mean score of all chunks in that file.

TODO:
1. Indentation based file expansion/collapse.
"""

import importlib
import json
import logging
import os
import pathlib

import numpy as np
import vertexai
from vertexai import generative_models

from research.core.model_input import ModelInput
from openai import OpenAI
from research.core.types import Document
from research.eval.harness.factories import create_retriever
from research.llm_apis.chat_utils import DeepSeekCoderV2ChatClient

utils = importlib.import_module("experimental.vzhao.20240620_summary.utils")

# ==========
# Gemini API using Augment GCP account.
PROJECT_ID = "system-services-dev"
LOCATION = "us-west1"
vertexai.init(project=PROJECT_ID, location=LOCATION)

# Gemini models to use.
# MODEL_NAME = "gemini-1.5-pro"
# MODEL_NAME = "gemini-1.5-flash"
MODEL_NAME = "deepseek_v2"  # llama cpp
# MODEL_NAME = "deepseek_api"


# Max context length for prompt.
# This doesn't apply to `full_repo` and `all_file_sketch` methods, since both feeds the
# whole repo in the context up to 1M tokens.
MAX_CONTEXT_LEN_LIST = [
    # 32_000,
    # 64_000,
    115_000,
    # 128_000,
    # 256_000,
]

# ==========
# Loads a repo from disk.
# * For new github repo, git clone the repo first.
# * Update the following global constants.
# Paths that start with these will NOT be loaded.
REPO_ROOT = "/home/<USER>/augment"
PATH_DENYLIST = [
    "augment/experimental",
]
# File extension allowlist.
ALLOWED_EXTENSIONS = (
    "py",
    "rs",
    "js",
    "ts",
    "java",
    "cpp",
    "h",
    "cs",
)
REPO = os.path.basename(REPO_ROOT)
GITHUB_URL = "https://github.com/jbellis/jvector"

OUTPUT_ROOT = (
    pathlib.Path("/mnt/efs/augment/public_html/vzhao/summary_questions")
    / REPO
    / utils.replace_hyphen(MODEL_NAME)
)

# ==========
# List of summary questions.
QUESTIONS = utils.QUESTIONS


# ====== Full repo: concate all files in the prompt ======
def make_prompt_full_repo(documents: list[Document]):
    def impl(question: str):
        """"""
        context = utils.concat_docs(documents)
        prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(
            context=context,
            question=question,
        )
        return prompt, len(documents)

    return impl


def run_prompt_full_method(
    questions,
    documents,
    google_model,
):
    METHOD = "Method: Concate all files and add to the prompt."
    print(f"Running {METHOD}")
    output = utils.generate_answers(
        questions, make_prompt_full_repo(documents), google_model
    )
    basename = "full_repo"
    utils.write_html(OUTPUT_ROOT / f"{basename}.html", output, REPO, GITHUB_URL, METHOD)
    utils.write_json(OUTPUT_ROOT / f"{basename}.json", output)


# ====== All file sketch: Concate all level-1 file sketches. ======
def make_prompt_all_file_sketch(documents, indent_level):
    def impl(question: str):
        context = utils.concat_docs(
            utils.collapse_document(
                d,
                level=indent_level,
            )
            for d in documents
        )
        prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(
            context=context,
            question=question,
        )
        return prompt

    return impl


def run_prompt_all_file_sketch(questions, documents, google_model, indent_level: int):
    METHOD = f"All file sketch: Concate all level-{indent_level} file sketches and add to the prompt."
    print(f"Running {METHOD}")
    output = utils.generate_answers(
        questions, make_prompt_all_file_sketch(documents, indent_level), google_model
    )
    basename = "all_file_sketch"
    utils.write_html(
        OUTPUT_ROOT / f"{basename}_indent_{indent_level}.html",
        output,
        REPO,
        GITHUB_URL,
        METHOD,
    )
    utils.write_json(OUTPUT_ROOT / f"{basename}_indent_{indent_level}.json", output)


# ====== Vanilla Chatanol  ======
def make_prompt_vanilla_chatanol(retriever, max_context_len):
    def impl(question):
        chunks, _ = retriever.query(ModelInput(question))

        final_chunks = utils.strategy_1(chunks, max_context_len)
        context = utils.concat_chunks(final_chunks)

        prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(
            context=context,
            question=question,
        )
        print("Number of unique documents: ", len(final_chunks))
        print(utils.tk_len(prompt))
        return prompt, len(final_chunks)

    return impl


def run_prompt_vanilla_chatanol(questions, retriever, google_model, max_context_len):
    METHOD = f"Vanilla Chatanol {max_context_len//1000}k: Concate all files and add to the prompt."
    print(f"Running {METHOD}")
    output = utils.generate_answers(
        questions,
        make_prompt_vanilla_chatanol(retriever, max_context_len),
        google_model,
    )
    basename = f"vanilla_chatanol_{max_context_len//1000}k"
    utils.write_html(OUTPUT_ROOT / f"{basename}.html", output, REPO, GITHUB_URL, METHOD)
    utils.write_json(OUTPUT_ROOT / f"{basename}.json", output)


# ====== Top k files. File score is the max of all its chunk scores. ======
def make_prompt_top_file_max_pooling(retriever, doc_lookup, max_context_len):
    def impl(question):
        chunks, scores = retriever.query(ModelInput(question))

        final_docs = utils.strategy_2(
            chunks,
            scores,
            doc_lookup,
            max_context_len,
            aggregate_fn=np.max,
            root_dirname=REPO,
        )
        context = utils.concat_docs(final_docs)

        prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(
            context=context,
            question=question,
        )
        print("Number of unique documents: ", len(final_docs))
        print(utils.tk_len(prompt))
        return prompt, len(final_docs)

    return impl


def run_prompt_top_file_max_pooling(
    questions, retriever, doc_lookup, google_model, max_context_len
):
    METHOD = f"Top k files max pooling {max_context_len//1000}k: Concate top ranked files and add to the prompt. The file score is defined as the max score of all chunks in that file."
    print(f"Running {METHOD}")
    output = utils.generate_answers(
        questions,
        make_prompt_top_file_max_pooling(retriever, doc_lookup, max_context_len),
        google_model,
    )

    basename = f"top_file_max_pooling_chatanol_{max_context_len//1000}k"
    utils.write_html(OUTPUT_ROOT / f"{basename}.html", output, REPO, GITHUB_URL, METHOD)
    utils.write_json(OUTPUT_ROOT / f"{basename}.json", output)


# ====== Top k files. File score is the mean of all its chunk scores. ======
def make_prompt_top_file_mean_pooling(retriever, doc_lookup, max_context_len):
    def impl(question):
        print(question)
        chunks, scores = retriever.query(ModelInput(question))

        final_docs = utils.strategy_2(
            chunks,
            scores,
            doc_lookup,
            max_context_len,
            aggregate_fn=np.mean,
            root_dirname=REPO,
        )
        context = utils.concat_docs(final_docs)

        prompt = utils.SINGLE_TURN_TEMPLATE_V2.format(
            context=context,
            question=question,
        )
        print("Number of unique documents: ", len(final_docs))
        print(utils.tk_len(prompt))
        return prompt, len(final_docs)

    return impl


def run_prompt_top_file_mean_pooling(
    questions, retriever, doc_lookup, google_model, max_context_len
):
    METHOD = f"Top k files mean pooling {max_context_len//1000}k: Concate top ranked files and add to the prompt. The file score is defined as the mean score of all chunks in that file."
    print(f"Running {METHOD}")
    output = utils.generate_answers(
        questions,
        make_prompt_top_file_mean_pooling(retriever, doc_lookup, max_context_len),
        google_model,
    )
    basename = f"top_file_mean_pooling_chatanol_{max_context_len//1000}k"
    utils.write_html(OUTPUT_ROOT / f"{basename}.html", output, REPO, GITHUB_URL, METHOD)
    utils.write_json(OUTPUT_ROOT / f"{basename}.json", output)


def main():
    # Creates output directory.
    OUTPUT_ROOT.mkdir(parents=True, exist_ok=True)

    # Initialize Gemini API.
    model = None
    if MODEL_NAME == "deepseek_v2":
        # DeepSeek client
        DEEPSEEK_ADDRESS = "**************:8080"
        model = DeepSeekCoderV2ChatClient(address=DEEPSEEK_ADDRESS, timeout=3600 * 8)
    elif MODEL_NAME in ["gemini-1.5-pro", "gemini-1.5-flash"]:
        model = generative_models.GenerativeModel(
            model_name=MODEL_NAME,
            generation_config=utils.GENERATION_CONFIG,
        )
    elif MODEL_NAME == "deepseek_api":
        # Note: Do not send augment internal repos to DeepSeek!
        assert REPO.lower() != "augment"
        # You need a API key to use DeepSeek API.
        # https://platform.deepseek.com/api-docs/api/deepseek-api/
        with open("/home/<USER>/.config/deepseek/api_token", "r") as f:
            api_key = f.read().strip()
        model = OpenAI(api_key=api_key, base_url="https://api.deepseek.com")
    else:
        raise ValueError(f"Unknown model: {MODEL_NAME}")

    # Loads documents for the repo.
    documents = list(
        doc
        for doc in utils.load_files_from_root(REPO_ROOT, allowlist=ALLOWED_EXTENSIONS)
        # Filters out files in the denylist.
        if not any(utils.is_subpath(doc.path, p) for p in PATH_DENYLIST)
    )
    print(f"Loaded {len(documents)} documents.")
    print(f"Saves results to {OUTPUT_ROOT}.")
    if input("Continue? [Y/n]").lower() == "n":
        return

    # Loads Chatanol and run indexing.
    retriever = create_retriever(utils.CHATANOL_CONFIG)
    retriever.load()
    retriever.remove_all_docs()
    retriever.add_docs(documents)

    # Init `DocLookUp`.
    doc_lookup = utils.DocLookUp(documents)

    # Comment/uncomment to run different methods.
    # run_prompt_full_method(QUESTIONS, documents, model)
    # run_prompt_all_file_sketch(QUESTIONS, documents, model, indent_level=0)
    # run_prompt_all_file_sketch(QUESTIONS, documents, model, indent_level=1)
    for max_context_len in MAX_CONTEXT_LEN_LIST:
        run_prompt_vanilla_chatanol(QUESTIONS, retriever, model, max_context_len)
        # run_prompt_top_file_max_pooling(
        #     QUESTIONS, retriever, doc_lookup, model, max_context_len
        # )
        # run_prompt_top_file_mean_pooling(
        #     QUESTIONS, retriever, doc_lookup, model, max_context_len
        # )


if __name__ == "__main__":
    main()

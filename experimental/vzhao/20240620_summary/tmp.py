"""
Install the Google AI Python SDK

$ pip install google-generativeai

See the getting started guide for more information:
https://ai.google.dev/gemini-api/docs/get-started/python
"""

import os
import time

import google.generativeai as genai

genai.configure(api_key=os.environ["GEMINI_API_KEY"])


def upload_to_gemini(path, mime_type=None):
    """Uploads the given file to Gemini.

    See https://ai.google.dev/gemini-api/docs/prompting_with_media
    """
    file = genai.upload_file(path, mime_type=mime_type)
    print(f"Uploaded file '{file.display_name}' as: {file.uri}")
    return file


def wait_for_files_active(files):
    """Waits for the given files to be active.

    Some files uploaded to the Gemini API need to be processed before they can be
    used as prompt inputs. The status can be seen by querying the file's "state"
    field.

    This implementation uses a simple blocking polling loop. Production code
    should probably employ a more sophisticated approach.
    """
    print("Waiting for file processing...")
    for name in (file.name for file in files):
        file = genai.get_file(name)
        while file.state.name == "PROCESSING":
            print(".", end="", flush=True)
            time.sleep(10)
            file = genai.get_file(name)
        if file.state.name != "ACTIVE":
            raise Exception(f"File {file.name} failed to process")
    print("...all files ready")
    print()


# Create the model
# See https://ai.google.dev/api/python/google/generativeai/GenerativeModel
generation_config = {
    "temperature": 1,
    "top_p": 0.95,
    "top_k": 64,
    "max_output_tokens": 8192,
    "response_mime_type": "text/plain",
}

model = genai.GenerativeModel(
    model_name="gemini-1.5-flash",
    generation_config=generation_config,
    # safety_settings = Adjust safety settings
    # See https://ai.google.dev/gemini-api/docs/safety-settings
)

# TODO Make these files available on the local file system
# You may need to update the file paths
files = [
    upload_to_gemini("combined_code_dump.txt", mime_type="text/plain"),
]

# Some files have a processing delay. Wait for them to be ready.
wait_for_files_active(files)

chat_session = model.start_chat(
    history=[
        {
            "role": "user",
            "parts": [
                files[0],
                "What this project is about?",
            ],
        },
        {
            "role": "model",
            "parts": [
                "This code is a collection of Python modules related to face manipulation and analysis within the DeepFaceLab project. The goal of these modules is to provide functionalities for:\n\n* **Face Detection:** Detecting faces in images using the S3FD model.\n* **Landmark Detection:** Locating key facial points using the FAN model (both 2D and 3D).\n* **Face Enhancement:** Upscaling the resolution of face images by a factor of 4 using a dedicated neural network.\n* **Semantic Segmentation:** Generating segmentation masks for facial features using the XSeg model.\n* **Landmark Manipulation:** Performing various operations on facial landmarks, such as mirroring, transforming, and aligning.\n\nThe modules define classes and functions that allow users to utilize these models and functionalities within their applications. \n\nHere's a breakdown of the key modules:\n\n**1. facelib/FaceType.py:** Defines an enumeration (`FaceType`) to represent different face types (e.g., `FULL`, `HEAD`, `HALF`). This helps categorize and manage different face regions of interest.\n\n**2. facelib/S3FDExtractor.py:**  Implements a face detector based on the S3FD model. It provides a `extract` function that takes an image as input and returns a list of detected face bounding boxes.\n\n**3. facelib/FANExtractor.py:** Implements a facial landmark extractor using the FAN model. It offers `extract` for obtaining 2D or 3D landmarks for faces within an image.\n\n**4. facelib/FaceEnhancer.py:**  Provides a class for upscaling the resolution of face images using a trained neural network. The `enhance` function allows users to increase the resolution of face images.\n\n**5. facelib/XSegNet.py:**  Implements a semantic segmentation model based on the XSeg architecture. It enables users to generate masks for facial features (e.g., eyes, eyebrows, mouth) in images.\n\n**6. facelib/LandmarksProcessor.py:**  Contains functions for manipulating and analyzing facial landmarks. This includes conversions between different landmark formats, transformation operations, mask generation based on landmarks, and even estimation of head pose (pitch, yaw, roll).\n\n**7. facelib/__init__.py:** Acts as a central import point for all the modules in the `facelib` package, allowing users to access the functionalities directly through this file.\n\nThese modules work together to provide a robust set of tools for face-related tasks within DeepFaceLab. They are likely used in various parts of the application, such as face swapping, facial animation, and other face manipulation tasks. \n",
            ],
        },
    ]
)

response = chat_session.send_message("INSERT_INPUT_HERE")

print(response.text)

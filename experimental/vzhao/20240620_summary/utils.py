"""Utilities for working with Git repos."""

import collections
import dataclasses
import hashlib
import itertools
import json
import logging
import pathlib
from pathlib import Path
from typing import Callable, Iterable, Iterator, Optional, Sequence, Union

import dataclasses_json
import markdown
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import sentencepiece as spm
import torch
import tqdm
from openai import OpenAI
from vertexai import generative_models

from research.core.types import Chunk, Document
from research.next_edits import smart_chunking
from research.llm_apis.chat_utils import DeepSeekCoderV2ChatClient

CHATANOL_CONFIG = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3",
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 30,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "max_tokens": 1023,
        "tokenizer_name": "StarCoderTokenizer",
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "max_tokens": 999,
        "add_path": True,
        "tokenizer_name": "StarCoderTokenizer",
    },
}

# Add more extensions as needed
ALLOWED_EXTENSIONS = (
    "py",
    "rs",
    "js",
    "ts",
    # "html",
    # "css",
    # "java",
    # "cpp",
    # "h",
    # "cs",
)


def loads_augment_repo() -> Iterator[Document]:
    """Loads the augment repo."""

    augment_repo = json.load(
        pathlib.Path(
            "/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_jun_08_2024.json"
        ).open()
    )
    yield from (Document(**doc) for doc in augment_repo["docs"])


def load_files_from_root(
    root: pathlib.Path, allowlist=ALLOWED_EXTENSIONS
) -> Iterator[Document]:
    """Loads the repo from the root."""
    if isinstance(root, str):
        root = pathlib.Path(root)
    for file in root.rglob("*"):
        if is_file_allowed(file, allowlist):
            yield Document(
                id=hashlib.sha256(file.read_bytes()).hexdigest(),
                path=str(root.name / file.relative_to(root)),
                text=file.read_text(errors="ignore"),
            )


def is_file_allowed(file: pathlib.Path, allowlist) -> bool:
    """Checks if the file is allowed."""
    return file.is_file() and file.suffix[1:] in allowlist


def concat_docs(docs: Iterable[Document]) -> str:
    """Concatenates the documents into a single string."""
    output = []
    for id, doc in enumerate(docs):
        # Gets extension of the path
        output.append(f"ID: {id} |\n// File: {doc.path}:\n{doc.text}\n| END ID: {id}")
    return "\n\n".join(output)


def document_to_chunk(doc: Document) -> Chunk:
    return Chunk(
        id=doc.id,
        text=doc.text,
        parent_doc=doc,
        char_offset=0,
        length=len(doc.text),
        line_offset=0,
        length_in_lines=len(doc.text.splitlines()),
    )


def concat_chunks(chunks: Iterable[Chunk]) -> str:
    """Concatenates the chunks into a single string."""
    output = []
    for id, chunk in enumerate(chunks):
        if chunk.line_offset == 0:
            text = (
                f"ID: {id} |\n// File: {chunk.path}:\n...\n{chunk.text}\n| END ID: {id}"
            )
        else:
            text = f"ID: {id} |\n// File: {chunk.path}:\n{chunk.text}\n| END ID: {id}"
        output.append(text)
    return "\n\n".join(output)


Trie = lambda: collections.defaultdict(Trie)  # noqa: E731


def build_tree_from_chunks(
    chunks: Iterable[Chunk], scores: list[float], root: pathlib.Path
):
    """Builds a tree from the chunks."""
    tree = Trie()
    for chunk, score in zip(chunks, scores):
        path = pathlib.Path(chunk.path).relative_to(root).parts
        node = tree
        for p in path:
            node = node[p]
        if "scores" not in node:
            node["scores"] = []
        node["scores"].append(score)
    return tree


def compute_scores_for_tree(root, aggregate_fn):
    """Computes the scores for a file directory tree."""

    def visit(node) -> float:
        """Traverses a tree in Depth-First Search (DFS) order."""
        if "scores" in node:
            return aggregate_fn(node["scores"])
        scores = []
        for child in node.values():
            scores.append(visit(child))
        node["scores"] = scores
        return aggregate_fn(scores)

    visit(root)


def all_node_has_scores(root):
    assert "scores" in root
    for key in root:
        if key != "scores":
            all_node_has_scores(root[key])


class FileTree:
    """A file tree."""

    def __init__(
        self,
        chunks: Sequence[Chunk],
        scores: Sequence[float],
        root: pathlib.Path,
        aggregate_fn: Callable[[Sequence[float]], float],
    ):
        self.root_path = pathlib.Path(root)
        self.root_node = build_tree_from_chunks(chunks, scores, root)
        self.aggregate_fn = aggregate_fn
        #
        compute_scores_for_tree(self.root_node, aggregate_fn)
        all_node_has_scores(self.root_node)

    def display_scores(
        self,
        max_depth: int,
        path: Optional[pathlib.Path] = None,
        normalize=True,
        temperature=1.0,
        topk=-1,
        sort_by="score",
    ):
        """Traverses the tree up to certain depth and computes the aggregated scores."""
        path = path or self.root_path
        if not path.is_relative_to(self.root_path):
            raise ValueError(
                f"Path {path} is not relative to root path {self.root_path}."
            )

        results: list[tuple] = []

        def visit(node, current_depth: int, current_path: pathlib.Path):
            """Traverses a tree in Depth-First Search (DFS) order."""
            if current_depth == max_depth or (len(node) == 1 and "scores" in node):
                results.append((current_path, self.aggregate_fn(node["scores"])))
                return
            for key in node:
                if key == "scores":
                    continue
                visit(node[key], current_depth + 1, current_path / key)

        rel_path = path.relative_to(self.root_path)
        visit(self.get_node(rel_path), 0, self.root_path / rel_path)
        if normalize:
            scores = torch.softmax(
                torch.tensor([score for _, score in results]) / temperature, 0
            ).tolist()
            results = [
                (path, normalized) for (path, _), normalized in zip(results, scores)
            ]
        if sort_by == "score":
            sorted_results = sorted(results, key=lambda x: x[1], reverse=True)
        elif sort_by == "path":
            sorted_results = sorted(results, key=lambda x: x[0])
        else:
            raise ValueError(f"Unknown sort_by: {sort_by}")
        return sorted_results if topk < 0 else sorted_results[:topk]

    def get_node(self, path: pathlib.Path):
        path = pathlib.Path(path)
        node = self.root_node
        for p in path.parts:
            node = node[p]
        return node

    def topk_files(self, topk=-1):
        results: list[tuple] = []

        def visit(node, current_path: pathlib.Path):
            """Traverses a tree in Depth-First Search (DFS) order."""
            if len(node) == 1 and "scores" in node:
                # This is a file node.
                results.append((current_path, self.aggregate_fn(node["scores"])))
                return
            for key in node:
                if key == "scores":
                    continue
                visit(node[key], current_path / key)

        visit(self.root_node, self.root_path)
        sorted_results = sorted(results, key=lambda x: x[1], reverse=True)
        return sorted_results if topk < 0 else sorted_results[:topk]

    def sunburst(self, max_depth: int, title: str = ""):
        """Plot a sunburst chart."""
        data = []
        queue = collections.deque()
        queue.append((self.root_node, self.root_path, 0))

        while queue:
            node, current_path, current_depth = queue.popleft()
            parent = current_path.parent
            data.append(
                (
                    str(current_path),
                    current_path.name,
                    str(parent),
                    self.aggregate_fn(node["scores"]),
                )
            )
            if current_depth == max_depth:
                continue
            for key in node:
                if key == "scores":
                    continue
                queue.append((node[key], current_path / key, current_depth + 1))

        ids, labels, parents, values = list(zip(*data))
        fig = go.Figure(
            go.Sunburst(
                ids=ids,
                labels=labels,
                parents=parents,
                values=values,
                branchvalues="total",
                marker=dict(
                    colors=values,
                    colorscale="RdBu_r",
                    cmid=np.median(values),
                    cmin=np.percentile(values, 5),
                    cmax=np.percentile(values, 95),
                ),
            )
        )
        marker = fig.data[0].marker
        marker.showscale = True

        fig.update_layout(
            title={
                "text": title,
                "y": 0.1,
                "x": 0.1,
                "xanchor": "left",
                "yanchor": "top",
            },
            margin=dict(t=0, l=0, r=0, b=0),
        )

        fig.show()


def is_subpath(path, parent) -> bool:
    path = Path(path)
    parent = Path(parent)
    try:
        path.relative_to(parent)
        return True
    except ValueError:
        return False


# This prompt is inspired by 'https://arxiv.org/pdf/2406.13121'
SINGLE_TURN_TEMPLATE_V1 = """You will be given a repository of code. You need to read the code carefully and understand all of them. Then you will be asked a question that may require you to use multiple files to find the answer. Your goal is to find all documents from the list that are revelant to answer the question.

{context}

====== Now let's start! ======
Your goal is to read the code above carefully and answer the question.
Question: {question}
"""

SINGLE_TURN_TEMPLATE_V2 = """You will be given a repository of code. You need to read the code carefully and understand all of them. Then you will be asked a question that may require you to use multiple files to find the answer. Your goal is to find all documents from the list that are revelant to answer the question.

{context}

====== Now let's start! ======
Your goal is to read the code above carefully and answer the question. You should refer to filenames and code snippets in your answer when necessary.
Question: {question}
"""


def create_joint_chunk(chunks: Sequence[Chunk]) -> Chunk:
    for c in chunks:
        assert c.path == chunks[0].path

    sorted_chunks = sorted(chunks, key=lambda c: c.line_offset)
    text = "...\n".join(c.text for c in sorted_chunks)
    return Chunk(
        id=hashlib.sha256(((chunks[0].path or "") + text).encode("utf-8")).hexdigest(),
        text="...\n".join(c.text for c in sorted_chunks),
        parent_doc=chunks[0].parent_doc,
        char_offset=sorted_chunks[0].char_offset,
        length=sorted_chunks[-1].char_offset
        + sorted_chunks[-1].length
        - sorted_chunks[0].char_offset,
        line_offset=sorted_chunks[0].line_offset,
        length_in_lines=sorted_chunks[-1].char_offset
        + sorted_chunks[-1].length_in_lines
        - sorted_chunks[0].char_offset,
    )


GEMMA_TOKENIZER_PATH = "/mnt/efs/augment/user/yury/gemma/tokenizer.model"
tk = spm.SentencePieceProcessor()
tk.Load(GEMMA_TOKENIZER_PATH)
tk_len = lambda x: len(tk.tokenize(x))  # noqa: E731


def strategy_1(chunks: Sequence[Chunk], max_tokens: int) -> list[Chunk]:
    # Step 1: Select chunks that are not too long
    selected = []
    total_length = 0
    for chunk in chunks:
        total_length += tk_len(chunk.text)
        if total_length > max_tokens:
            break
        selected.append(chunk)

    # Step 2:
    selected.sort(key=lambda x: x.path)
    groups = itertools.groupby(selected, key=lambda x: x.path)
    final_chunks = []
    for _, group in groups:
        final_chunks.append(create_joint_chunk(list(group)))
    return final_chunks


SAFETY_CONFIG = [
    generative_models.SafetySetting(
        category=generative_models.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=generative_models.HarmBlockThreshold.BLOCK_NONE,
    ),
    generative_models.SafetySetting(
        category=generative_models.HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=generative_models.HarmBlockThreshold.BLOCK_NONE,
    ),
    generative_models.SafetySetting(
        category=generative_models.HarmCategory.HARM_CATEGORY_UNSPECIFIED,
        threshold=generative_models.HarmBlockThreshold.BLOCK_NONE,
    ),
]


class DocLookUp:
    def __init__(self, documents: list[Document]) -> None:
        self._dict = {doc.path: idx for idx, doc in enumerate(documents)}
        self._documents = documents

    def __getitem__(self, path: str | pathlib.Path) -> Document:
        return self._documents[self._dict[str(path)]]


def strategy_2(
    chunks: Sequence[Chunk],
    scores: Sequence[float],
    doc_lookup: DocLookUp,
    max_tokens: int,
    aggregate_fn,
    root_dirname: str,
) -> list[Document]:
    file_tree = FileTree(
        chunks,
        scores,
        pathlib.Path(root_dirname),
        aggregate_fn,
    )
    rst = file_tree.topk_files()
    output = []
    total_tokens = 0
    for path, _ in rst:
        doc = doc_lookup[path]
        total_tokens += tk_len(doc.text)
        if total_tokens > max_tokens:
            break
        output.append(doc)
    return output


def collapse_document(document: Document, level) -> Document:
    """Collapses the document into a single chunk."""
    text = document.text
    # Computes the indentation level for each line
    all_lines = text.splitlines(keepends=True)
    indent_levels = smart_chunking.compute_line_indentations(text)

    unique_levels = list(sorted(set(indent_levels)))
    level = min(level, len(unique_levels) - 1)

    is_empty_line = [line.strip() == "" for line in all_lines]
    # Line mask: if True, the line is included.
    mask = [
        (current_level <= unique_levels[level] and not e)
        for current_level, e in zip(indent_levels, is_empty_line)
    ]
    text = []
    ellipsis = False
    for line, m, e in zip(all_lines, mask, is_empty_line):
        if m:
            if ellipsis:
                text.append("...\n")
                ellipsis = False
            text.append(line)
        else:
            ellipsis = ellipsis or not e
    if ellipsis:
        text.append("...\n")

    text = "".join(text)
    return Document(
        id=document.id,
        text=text,
        path=document.path,
        meta=document.meta,
    )


QUESTIONS = [
    "What is the overall architecture of the application? ",
    "What is the main data structure used in the application? ",
    "Give a high level overview of the project.",
    "What are the main modules or components, and how do they interact?",
    "What is the folder structure and naming convention?",
    "How are test cases organized?",
    "What are the most complex or lengthy methods in the codebase?",
    "How are dependencies managed?",
    "Are there any third-party libraries or services heavily relied upon?",
]

GENERATION_CONFIG = generative_models.GenerationConfig(
    candidate_count=1,
    temperature=0.0,
    top_p=0.95,
    max_output_tokens=8192,
    response_mime_type="text/plain",
)


def gemini_answer(prompt: str, google_model: generative_models.GenerativeModel) -> str:
    responses = google_model.generate_content(
        prompt,
        stream=False,
        generation_config=GENERATION_CONFIG,
        safety_settings=SAFETY_CONFIG,
    )
    return responses.text


def gemini_answer_html(
    prompt: str, google_model: generative_models.GenerativeModel
) -> str:
    answer = gemini_answer(prompt, google_model)
    return markdown.markdown(answer)


def deepseek_answer(prompt: str, client: DeepSeekCoderV2ChatClient) -> str:
    return client.generate(
        messages=[prompt],
        max_tokens=2048,
    )


def deepseek_answer_html(prompt: str, client: DeepSeekCoderV2ChatClient) -> str:
    answer = deepseek_answer(prompt, client)
    return markdown.markdown(answer)


HTML_ROOT = pathlib.Path("/mnt/efs/augment/public_html/vzhao/summary_questions")


@dataclasses_json.dataclass_json
@dataclasses.dataclass
class GeminiAnswer:
    question: str
    prompt: str
    answer: str
    answer_html: str
    unique_docs: int = -1


def format_html(
    gemini_answers: list[GeminiAnswer],
    repo: str,
    github_url: str,
    method: str,
) -> str:
    all_qa = ""

    for ga in gemini_answers:
        question = ga.question
        html = ga.answer_html
        all_qa += f"""
<div class="question">
<p>{question}</p>
<p>prompt length: {tk_len(ga.prompt)}</p>
<p>unique docs: {ga.unique_docs if ga.unique_docs > 0 else "N/A"}</p>
</div>
<div class="answer">
{html}
</div>
"""
    full_html = f"""
<!DOCTYPE html>
<html>
<head>
<title>Q&A</title>
<style>
    body {{
    font-family: Arial, sans-serif;
    }}
    .question {{
    background-color: #f0f0f0; /* light gray background */
    padding: 10px;
    border-radius: 10px;
    }}
    .answer {{
    margin-bottom: 20px;
    }}
</style>
</head>
<body>
<h1><a href="{github_url}">{repo}</a></h1>
<p>{method}</p>
{all_qa}
</body>
</html>
"""
    return full_html


def write_html(output_path, gemini_answers, repo, github_url, method):
    if not gemini_answers:
        return
    with open(output_path, "w") as f:
        f.write(format_html(gemini_answers, repo, github_url, method))


def write_json(output_path, gemini_answers):
    if not gemini_answers:
        return
    with open(output_path, "w") as f:
        json.dump([o.to_dict() for o in gemini_answers], f, indent=2)


GEMINI_CONTEXT_LEN_LIMIT = 950_000
DEEPSEEK_CONTEXT_LEN_LIMIT = 124_904


def generate_answers(
    questions: Sequence[str],
    prompt_fn: Callable[..., Union[str, tuple[str, int]]],
    model: generative_models.GenerativeModel | DeepSeekCoderV2ChatClient | OpenAI,
) -> list[GeminiAnswer]:
    output = []
    for question in tqdm.tqdm(questions):
        prompt = prompt_fn(question)
        unique_docs = None
        if isinstance(prompt, tuple):
            prompt, unique_docs = prompt
        p_len = tk_len(prompt)
        if isinstance(model, DeepSeekCoderV2ChatClient):
            if p_len > DEEPSEEK_CONTEXT_LEN_LIMIT:
                logging.warning(
                    "Prompt too long: %d > %d", p_len, DEEPSEEK_CONTEXT_LEN_LIMIT
                )
                continue
            answer = deepseek_answer(prompt, model)
        elif isinstance(model, generative_models.GenerativeModel):
            if p_len > GEMINI_CONTEXT_LEN_LIMIT:
                logging.warning(
                    "Prompt too long: %d > %d", p_len, GEMINI_CONTEXT_LEN_LIMIT
                )
                continue
            answer = gemini_answer(prompt, model)
        elif isinstance(model, OpenAI):
            # This is DeepSeek API.
            if p_len > DEEPSEEK_CONTEXT_LEN_LIMIT:
                logging.warning(
                    "Prompt too long: %d > %d", p_len, DEEPSEEK_CONTEXT_LEN_LIMIT
                )
                continue
            answer = deepseek_api_answer(prompt, model)
        else:
            raise ValueError(f"Unknown model type: {type(model)}")
        output.append(
            GeminiAnswer(
                question=question,
                prompt=prompt,
                answer=answer,
                answer_html=markdown.markdown(answer),
                unique_docs=unique_docs if unique_docs else -1,
            )
        )
    return output


def deepseek_api_answer(prompt: str, client: OpenAI) -> str:
    return (
        client.chat.completions.create(
            # This is Deepseek-v2
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": ""},
                {"role": "user", "content": prompt},
            ],
            stream=False,
        )
        .choices[0]
        .message.content
        or ""
    )


def replace_hyphen(name: str):
    return name.replace("-", "_")

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "# from research.core.model_input import ModelInput\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"generic_neox\",\n", "        \"checkpoint_path\": \"starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 40,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"max_lines\": -1,\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"prefix_ratio\": 0.9,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "}\n", "\n", "retrieval_database = create_retriever(retriever_config)\n", "retrieval_database.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
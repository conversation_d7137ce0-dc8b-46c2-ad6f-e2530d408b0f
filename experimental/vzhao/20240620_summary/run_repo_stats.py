"""Statistics for a repo.

This script computes the number of tokens for a repo and visualizes the file tree.

Usage:
python experimental/vzhao/20240620_summary/run_repo_stats.py /path/to/root <depth>

For example,
python experimental/vzhao/20240620_summary/run_repo_stats.py /home/<USER>/augment 4
"""

import importlib
import click
import pathlib
import tqdm

utils = importlib.import_module("experimental.vzhao.20240620_summary.utils")

# File extension allowlist.
ALLOWED_EXTENSIONS = (
    "py",
    "rs",
    "js",
    "ts",
    "java",
    "cpp",
    "h",
    "cs",
)


@click.command()
@click.argument("repo_root", type=click.Path(exists=True))
@click.argument("depth", type=int, default=4)
def main(repo_root: str, depth):
    documents = list(
        utils.load_files_from_root(repo_root, allowlist=ALLOWED_EXTENSIONS)
    )
    num_tokens = [
        utils.tk_len(doc.text)
        for doc in tqdm.tqdm(documents, desc="Estimating file length")
    ]
    file_tree = utils.FileTree(
        [utils.document_to_chunk(doc) for doc in documents],
        num_tokens,
        pathlib.Path(repo_root).name,
        sum,
    )
    _ = file_tree.sunburst(max_depth=depth, title="Number of Tokens")


if __name__ == "__main__":
    main()

r"""Runs curated eval aganst a dev deployment.

Usage:
# Runs the script to replay requests.
    --url=https://dev-vzhao.us-central.api.augmentcode.com \
    --model_name=raven-edit-v6-15b \

python experimental/vzhao/202501_next_edit_curate_eval/run_curate_eval.py \
    --url=https://dev-jiayi.us-central.api.augmentcode.com \
    --model_name=raven-edit-v7-15b \
    --dataset_path=next_edits/curate_202412.json

--url=https://staging-shard-0.api.augmentcode.com \
    --model_name=raven-edit-v6-15b \
    --dataset_path=next_edits/curate_202412.json

python experimental/vzhao/202501_next_edit_curate_eval/run_curate_eval.py \
    --url=https://dev-jiayi.us-central.api.augmentcode.com \
    --model_name=raven-edit-v7-15b \
    --dataset_path=next_edits/curate_202412.json \
    --nowait_for_indexing

"""

from absl import app
from absl import flags
import os
import time
import tqdm
import json
from base.augment_client.client import AugmentClient, AugmentModelClient
from base.blob_names import blob_names_pb2
from google.protobuf.json_format import ParseDict
from base.datasets import replay_utils
from base.datasets import tenants
from base.datasets.itertools import batched
import tenacity
from google.protobuf.json_format import MessageToDict
from services.next_edit_host import next_edit_pb2
from services.request_insight import request_insight_pb2
from research.core import constants
from base.augment_client.client import ClientException

FLAGS = flags.FLAGS

_URL = flags.DEFINE_string("url", None, "URL of the dev deployment", required=True)
_MODEL_NAME = flags.DEFINE_string(
    "model_name", None, "Name of the model to evaluate", required=True
)
_DATASET_PATH = flags.DEFINE_string(
    "dataset_path",
    "/mnt/efs/augment/data/eval/next_edits/curate_202412.json",
    "Path to the curated dataset",
)
_WAIT_FOR_INDEXING = flags.DEFINE_bool(
    "wait_for_indexing",
    True,
    "Whether to wait for indexing to complete before replaying requests.",
)

TENANT = tenants.DOGFOOD_SHARD
CKPT_CACHE = replay_utils.get_checkpoint_cache(TENANT)
BLOB_CACHE = replay_utils.get_blob_cache(TENANT)


@tenacity.retry(
    stop=tenacity.stop_after_delay(300),  # 5 minutes timeout
    wait=tenacity.wait_fixed(5),  # 5 seconds between retries
    retry=tenacity.retry_if_result(lambda result: bool(result[1]))
    | tenacity.retry_if_exception_type(ClientException),
    retry_error_callback=lambda retry_state: retry_state.outcome.result(),
    after=lambda retry_state: print(
        f"Retry attempt {retry_state.attempt_number}: {len(retry_state.outcome.result()[1])} nonindexed blobs."
    ),
)
def _wait_for_indexing(
    client: AugmentClient,
    blob_names: set[str],
    model_name: str,
    find_missing_batch_size: int = 2000,
):
    """Check the index status of the blobs for a given set of completions."""
    unknown_memory_names, nonindexed_blob_names = [], []
    for batch in batched(list(blob_names), find_missing_batch_size):
        rst = client.find_missing(model_name, list(batch))
        unknown_memory_names.extend(rst.unknown_memory_names)
        nonindexed_blob_names.extend(rst.nonindexed_blob_names)
    if not _WAIT_FOR_INDEXING.value:
        return unknown_memory_names, []
    return unknown_memory_names, nonindexed_blob_names


# Figure out a way that we can see the request without dev deploy support UI.
@tenacity.retry(
    stop=tenacity.stop_after_delay(60),  # 1 minutes timeout
    wait=tenacity.wait_fixed(5),  # 5 seconds between retries
    retry=tenacity.retry_if_exception_type(ClientException),
)
def _replay(model_client: AugmentModelClient, request: dict) -> str | None:
    """Replay a request against the model client and return the new request id."""
    request_pb = ParseDict(request, request_insight_pb2.RINextEditRequest())
    # NOTE: We are sending the same request to the dev deploy system for reply. See
    # `FrontNextEditRequest` for all the request fields.
    responses = list(
        model_client.next_edit_stream(
            sequence_id=request_pb.request.sequence_id,
            lang=request_pb.request.lang,
            # The dev deploy system might not have `checkpoint_id`. Therefore, we use
            # the list of blob names directly.
            blobs={
                "checkpoint_id": None,
                "added_blobs": replay_utils.resolve_checkpoint(
                    request_pb.request.blobs, CKPT_CACHE
                ),
                "deleted_blobs": [],
            },
            recent_changes=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,
                    preserving_proto_field_name=True,
                )
                for e in request_pb.request.recent_changes
            ],
            instruction=request_pb.request.instruction,
            path=request_pb.request.path,
            blob_name=request_pb.request.blob_name,
            selection_begin_char=request_pb.request.selection_begin_char,
            selection_end_char=request_pb.request.selection_end_char,
            prefix=request_pb.request.prefix,
            selected_text=request_pb.request.selected_text,
            suffix=request_pb.request.suffix,
            diagnostics=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,
                    preserving_proto_field_name=True,
                )
                for e in request_pb.request.diagnostics
            ],
            mode=next_edit_pb2.NextEditMode.Name(request_pb.request.mode),
            scope=next_edit_pb2.NextEditScope.Name(request_pb.request.scope),
            edit_events=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,
                    preserving_proto_field_name=True,
                )
                for e in request_pb.request.edit_events
            ],
            blocked_locations=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,
                    preserving_proto_field_name=True,
                )
                for e in request_pb.request.blocked_locations
            ],
            # TODO: The response json decoding is not working properly. Use the new
            # request id to see results on the support site.
            warn_on_parse_error=True,
        )
    )
    if not responses:
        return None
    return responses[0].request_id


# Define models
class SimpleRateLimiter:
    def __init__(self, rate):
        self.rate = rate
        self.last_call = 0

    def acquire(self):
        now = time.time()
        sleep_time = max(0, 1 / self.rate - (now - self.last_call))
        time.sleep(sleep_time)
        self.last_call = now
        return sleep_time


def main(argvs):
    with open(os.path.expanduser("~/.config/augment/api_token")) as f:
        client = AugmentClient(
            url=_URL.value,
            token=f.read().strip(),
        )
        model_client = client.client_for_model(_MODEL_NAME.value)

    with (constants.AUGMENT_EVAL_ROOT / _DATASET_PATH.value).open("r") as f:
        eval_data = json.load(f)

    results = {}
    for key, value in tqdm.tqdm(eval_data.items()):
        blobs_proto = ParseDict(value["request"]["blobs"], blob_names_pb2.Blobs())
        blob_names = replay_utils.resolve_checkpoint(blobs_proto, CKPT_CACHE)
        replay_utils.upload_and_ensure_blobs_exist(
            client,
            BLOB_CACHE,
            set(blob_names),
            _MODEL_NAME.value,
            upload_batch_size=20,
        )
        unknown, nonindexed = _wait_for_indexing(
            client, set(blob_names), _MODEL_NAME.value
        )
        if unknown or nonindexed:
            results[key] = "Not all blobs are indexed."
            continue
        # Replay
        new_request_id = _replay(model_client, value)
        if new_request_id is None:
            results[key] = "Empty response."
            continue
        results[key] = new_request_id

    for k, v in sorted(results.items()):
        print(f"{k}\t{v}")


if __name__ == "__main__":
    app.run(main)

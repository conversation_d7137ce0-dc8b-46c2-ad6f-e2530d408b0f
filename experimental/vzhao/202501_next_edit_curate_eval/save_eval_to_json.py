"""Saves the eval data to json file."""

import os
from re import T
import time
import tqdm
import json
from base.datasets.nextedit import common
from base.datasets import replay_utils
from base.datasets import tenants
from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.tenants import DatasetTenant

DEV_VZHAO = DatasetTenant(
    name="dev-vzhao",
    project_id="system-services-dev",
    tenant_id="d166ca820b90dbc75f623e85aebaf34b",
    events_bucket_name="dev-vzhao-request-insight-events",
    dataset_name="",
    blob_bucket_name="augment-blob-exporter-dev-vzhao-dev",
    blob_bucket_prefix="blobs",
    checkpoint_bucket_name="augment-blob-exporter-dev-vzhao-dev",
    checkpoint_bucket_prefix="checkpoints",
)
TENANT = tenants.DOGFOOD_SHARD
FETCHERS = [GCSRequestInsightFetcher.from_tenant(TENANT)]
RIDS = [
    "8b788908-5527-4482-9077-8c16d1bc8ccf",
    "9463659c-0cee-42b9-bbf5-057c27edd091",
    "ccaa986f-c2a1-459c-8fde-3e132d2c2b98",
    "07a71320-37d7-42f7-a954-704403c2e55b",
    "9192e432-80d3-419c-94b9-0768ce298fe4",
    "511199ee-ff0a-4d1f-b968-3d81ac3648a6",
    "50ba9c9d-9587-4d6f-a941-cfb093794cad",
    "3eab8e8f-aa29-49a7-9b57-079bfb879b04",
    "7931c5c7-dae7-4543-84a5-bd600e4d09f3",
    "a535736f-5c0a-4eb1-8a61-b19434bf7d33",
    "9171b464-57d8-4174-a0af-a92650a67b40",
    "287df02b-8e71-40c0-9f3b-f2d42fd3049b",
    "2f95bc94-43b4-453e-b34c-eb013c8c2aa7",
    "e56a6295-e4fa-436d-93f2-e9cded324c2f",
    "8e5f9c9d-107a-4f47-9495-987fe10c7565",
    "27ea54d7-75e2-4850-af9f-71c5972b8101",
    "854466b4-dec5-408c-b3d1-61aee07c0417",
    "16b9c850-1bac-4a90-a66b-b2fc028b9a96",
    "80d98ae6-1fd1-4ae6-8b8f-f070baec44c9",
    "d1c93f61-b0cb-448b-8927-92eca8463da9",
    "e08cab3c-6d06-4632-b1f3-7f0362be3c3f",
    "56931dd1-84fa-41bd-8acb-7e360608ab9d",
    "a1f2bc25-447d-48ea-b401-ecb91649ab03",
    "18ff0e7a-2ef0-4d89-b3be-4c1b6d6b370d",
    "ebde209c-a383-4d51-b3a4-7f4c649387c9",
    "3719c3e4-9fd3-4398-982c-3a95e2d2aa5a",
    "6c3ff706-33df-4807-a73e-eb459c0a834e",
    "5ae94e59-14bb-4ede-9ed7-bfe3a075b767",
    "7a0f5c3f-e898-4ddf-8244-6babb6d45213",
    "078dc484-6773-4264-86e0-06630bd20ea4",
    "df3b6d1d-b25c-4fcb-882a-3c4ce13b49fe",
    "e1e3d294-c0f4-4946-b7a7-a1db5e642806",
    "26543de4-3576-45b7-8ef5-fdab0737c42d",
    "8127f5fd-7d7f-4ecc-9458-0d2f3e1e7b96",
    "d37f5a07-e1b5-4b89-b5e4-2e329fb23813",
    "60d8e9a8-4418-424d-96a9-93b8fe5f2590",
    "63142063-3990-4dd6-b78e-8e0001cca7f4",
    "5d39911d-2e11-44c8-93c8-321dc974f716",
    "33a3f109-0135-46c9-99cc-00fe6d209d67",
    "05e1fad1-ea7f-41dc-a6c2-90317cbad9ce",
    "d18d2555-8c08-477d-b9b4-bd8f9638ee9f",
    "349c5ffe-0fcd-4c8a-93f9-077191dc129b",
    "3fa50b93-a786-4e72-91c5-5fc6b5ad09d1",
    "6c8be31b-1c00-41c4-a872-9560e8d9c07f",
]
OUTPUT_FILE = "/mnt/efs/augment/data/eval/next_edits/v7_feedback.json"


def main():
    data = {}
    for key in tqdm.tqdm(RIDS):
        data[key] = common.get_next_edit_request(key, FETCHERS)
    with open(OUTPUT_FILE, "w") as f:
        json.dump(data, f)


if __name__ == "__main__":
    main()

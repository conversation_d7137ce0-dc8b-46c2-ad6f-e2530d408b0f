{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Loads a Dense Retriever"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from augment.research.retrieval import retrieval_database\n", "\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "\n", "config = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"butanol/butanol_fr_seth6_0419.1_proj_512_384\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 120,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"simple_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "\n", "retriever = retrieval_database.RetrievalDatabase.from_yaml_config(config)\n", "retriever.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Augment Repo\n", "\n", "This takes about 7 mins using a single A40 GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from research.core.types import Document\n", "\n", "augment_repo = json.load(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_apr_10_2024.json\"\n", "    ).open()\n", ")\n", "\n", "retriever.add_docs(\n", "    (Document.from_json(json.dumps(doc)) for doc in augment_repo[\"docs\"])\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "\n", "question = \"What does VSCode extension sends to the chat backend as context?\"\n", "chunks, scores = retriever.query(ModelInput(question), top_k=32)\n", "\n", "print(question + '\\n')\n", "for idx, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print(f\"({idx}) -- {chunk.path}: {score} --\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "plt.plot(retriever.query(ModelInput(question))[1])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
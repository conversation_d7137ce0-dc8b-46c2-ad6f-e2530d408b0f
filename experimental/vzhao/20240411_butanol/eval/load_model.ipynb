{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Load Scorer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.neox_arguments import NeoXArgs\n", "import pathlib\n", "neox_args = NeoXArgs.from_ymls(\n", "    [pathlib.Path(\"/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/config.yml\")]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval.libraries.scorers import dense_scorer\n", "from augment.research.retrieval.libraries import prompt_formatters\n", "from research.core.data_paths import canonicalize_path\n", "from research.core.constants import AUGMENT_ROOT\n", "\n", "scorer = dense_scorer.Starcoder_1B_Scorer(\n", "    checkpoint_path=\"butanol/butanol_fr_starethanol_v2_proj_512_384\",\n", "    # checkpoint_path=\"starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "    additional_yaml_files=[\n", "        canonicalize_path(\n", "            \"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\",\n", "            new_path=AUGMENT_ROOT,\n", "        ),\n", "    ],\n", ")\n", "scorer.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
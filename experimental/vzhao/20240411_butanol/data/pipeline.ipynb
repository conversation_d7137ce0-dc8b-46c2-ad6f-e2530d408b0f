{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["CONFIG = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1: Reads jsonl and writes parquet."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["# SOURCE = '/mnt/efs/augment/user/yury/binks/retrieval-binks-v1/qa_data.v2.jsonl'\n", "SOURCE = '/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl'\n", "STAGE1_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage1_raw\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE1_URI}"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "import pyspark.sql.functions as sp\n", "\n", "# get spark monitoring if available\n", "try:\n", "    import sparkmonitor\n", "    monitor_path = sparkmonitor.__path__[0]\n", "except:\n", "    monitor_path = None\n", "\n", "spark = SparkSession.builder.appName(\"vzhao-butanol\").config(\n", "    \"spark.sql.shuffle.partitions\", 100\n", ")\n", "spark = spark.getOrCreate()\n", "\n", "df = spark.read.json(SOURCE)\n", "print(f'  Number of rows: {df.count()}')\n", "\n", "df = df.repartition(100)\n", "print(f'  Number of partitions: {df.rdd.getNumPartitions()}')\n", "\n", "df.write.mode('overwrite').parquet(STAGE1_URI)\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 2: Get prompt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["STAGE1_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage1_raw\"\n", "# STAGE1_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage1_raw/part-00000-ae3a67cf-b3b0-4942-8140-f94315b6ff01-c000.zstd.parquet\"\n", "STAGE2_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/0419.1/stage2_prompt\"\n", "\n", "CONFIG.update(\n", "    {\n", "        \"retrieved_docs\": 31,\n", "        \"negative_sample_temperature\": 1.0,\n", "        # \n", "        \"dataset_config\": {\n", "            \"seq_length\": 1024,\n", "            # \"num_validation_samples\": 8192,\n", "        },\n", "        'max_lines_per_chunk':120,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        \"doc_seq_length\": 1000,\n", "        \"allow_doc_clipping\": True,\n", "    }\n", ")\n", "\n", "query_prompt_formatter_config = {\n", "    \"name\": \"simple_query\",\n", "    \"max_tokens\": CONFIG[\"dataset_config\"][\"seq_length\"] - 1,\n", "    \"add_path\": <PERSON>als<PERSON>,\n", "}\n", "\n", "key_prompt_formatter_config = {\n", "    \"name\": \"ethanol6_document\",\n", "    \"max_tokens\": CONFIG['doc_seq_length'] - 1,\n", "    \"add_path\": True,\n", "}\n", "\n", "\n", "print(STAGE2_URI)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE2_URI}\n", "!rm -R {STAGE2_URI + '.error'}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Iterator\n", "\n", "from experimental.vzhao.data import common, pandas_functions\n", "from research.core.model_input import ModelInput\n", "from research.data.spark.pipelines.utils import map_parquet\n", "import importlib\n", "import importlib\n", "\n", "from experimental.vzhao.data import common\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240411_butanol.data.pandas_functions\"\n", ")\n", "\n", "# NOTE: Need to add one padding for data loader.\n", "def pack_prompt(prompt) -> bytearray:\n", "    return bytearray(\n", "        np.pad(prompt, (0, 1 + CONFIG[\"dataset_config\"][\"seq_length\"] - len(prompt)))\n", "        .astype(np.uint16)\n", "        .newbyteorder(\"<\")\n", "        .tobytes()\n", "    )\n", "\n", "\n", "def create_pack_prompts(\n", "    query_prompt_formatter_config,\n", "    key_prompt_formatter_config,\n", ") -> pd.Series:\n", "    \"\"\"A function to tokenize and prepare prompts for dual encoder training.\"\"\"\n", "    query_prompt_formatter = None\n", "    key_prompt_formatter = None\n", "    tokenizer = None\n", "\n", "\n", "    @map_parquet.passthrough_feature()\n", "    @map_parquet.allow_unused_args()\n", "    def pack_prompts(\n", "        question,\n", "        train_chunks: str,\n", "        labels: list[int],\n", "    ) -> Iterator[pd.Series]:\n", "        chunks = common.deserialize_retrieved_chunks(train_chunks)[:CONFIG['retrieved_docs']]\n", "        scores = labels\n", "\n", "        from megatron.tokenizer import get_tokenizer\n", "        nonlocal tokenizer\n", "        if tokenizer is None:\n", "            tokenizer = get_tokenizer(\"StarCoderTokenizer\")\n", "\n", "        # Pulls in registrations\n", "        # ethanol = importlib.import_module(\"experimental.igor.systems.ethanol\")\n", "        import research.retrieval.libraries.prompt_formatters\n", "        import research.retrieval.libraries.chunk_formatters\n", "\n", "        all_prompts = []\n", "        all_texts = []\n", "\n", "        # NOTE: Generates query prompt.\n", "        nonlocal query_prompt_formatter\n", "        if query_prompt_formatter is None:\n", "            query_prompt_formatter = pandas_functions.create_prompt_formatter(query_prompt_formatter_config)\n", "        query_prompt = pandas_functions.apply_query_formatter(\n", "            ModelInput(prefix=question), query_prompt_formatter, tokenizer\n", "        )\n", "        assert sum([1 for t in query_prompt if t == tokenizer.vocab[\"<|ret-endofquery|>\"]]) == 1\n", "\n", "        if len(query_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "            raise ValueError(\n", "                f\"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "            )\n", "        all_prompts.append(pack_prompt(query_prompt))\n", "        all_texts.append(\n", "            query_prompt_formatter.prepare_prompt_text(\n", "                ModelInput(prefix=question)\n", "            )[0]\n", "        )\n", "\n", "        # NOTE: Generates document prompt.\n", "        nonlocal key_prompt_formatter\n", "        if key_prompt_formatter is None:\n", "            key_prompt_formatter = pandas_functions.create_chunk_formatter(key_prompt_formatter_config)\n", "        key_prompt_formatter.tokenizer = tokenizer\n", "        end_of_key_token = key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "        pad_token = key_prompt_formatter.tokenizer.pad_id\n", "        for chunk_idx, chunk in enumerate(chunks):\n", "            # Format the prompt\n", "            # chunk_prompt = key_prompt_formatter.prepare_prompt(\n", "            #     ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "            # )\n", "            formatted = key_prompt_formatter.format(chunk)\n", "            chunk_prompt = formatted.tokens\n", "            if len(chunk_prompt) > CONFIG[\"doc_seq_length\"]:\n", "                if CONFIG[\"allow_doc_clipping\"]:\n", "                    chunk_prompt = chunk_prompt[: CONFIG[\"doc_seq_length\"]]\n", "                else:\n", "                    raise ValueError(\n", "                        f\"Prompt too long: {len(chunk_prompt)} > {CONFIG['doc_seq_length']}\"\n", "                    )\n", "\n", "            # Encode the perplexity score into tokens.\n", "            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(\n", "                f\"{scores[chunk_idx]}\"\n", "            )\n", "\n", "            # Format the footer of the prompt\n", "            suffix = [end_of_key_token] + ppl_info_tokens + [pad_token]\n", "            chunk_prompt.extend(suffix)\n", "            assert sum([1 for t in chunk_prompt if t == end_of_key_token]) == 1\n", "\n", "            # Check that the prompt is not too long. `chunk_prompt` contains both\n", "            # key prompt and score.\n", "            if len(chunk_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "                print(\"===================================================\")\n", "                print(key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "                print(\"===================================================\")\n", "                raise ValueError(\n", "                    f\"{chunk_idx} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "                )\n", "\n", "            all_prompts.append(pack_prompt(chunk_prompt))\n", "            all_texts.append(formatted.text)\n", "\n", "        # +1 because the first prompt is the query prompt.\n", "        yield pd.Series(\n", "            {\n", "                \"prompt_tokens\": all_prompts,\n", "                \"prompt_texts\": all_texts,\n", "            }\n", "        )\n", "\n", "    return pack_prompts"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# from experimental.vzhao.data import pandas_functions\n", "import importlib\n", "from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.retrieval.libraries import chunking_functions\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240411_butanol.data.pandas_functions\"\n", ")\n", "\n", "chunker = chunking_functions.LineLevelChunker(\n", "    max_lines_per_chunk=CONFIG[\"max_lines_per_chunk\"]\n", ")\n", "\n", "\n", "def stage2():\n", "    # spark = get_session(name=\"vzhao-butanol\")\n", "    spark = k8s_session(\n", "        name=\"vzhao-butanol\",\n", "        max_workers=128,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"32G\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                pandas_functions.parse_file_list,\n", "                # pandas_functions.create_get_all_chunks(chunker),\n", "                pandas_functions.process_repo(\n", "                    num_negatives=CONFIG[\"retrieved_docs\"] - 1,\n", "                    chunker=chunker,\n", "                    sample_chunk_fn=pandas_functions.create_distance_sample_chunks(\n", "                        CONFIG[\"negative_sample_temperature\"]\n", "                    ),\n", "                ),\n", "                create_pack_prompts(\n", "                    query_prompt_formatter_config=query_prompt_formatter_config,\n", "                    key_prompt_formatter_config=key_prompt_formatter_config,\n", "                ),\n", "            ]\n", "        ),\n", "        # input_path=[STAGE1_URI],\n", "        input_path=STAGE1_URI,\n", "        output_path=STAGE2_URI,\n", "        batch_size=1,\n", "        timeout=7200,\n", "        ignore_error=False,\n", "        #\n", "        # timing_run=True,\n", "    )\n", "    spark.stop()\n", "    return result\n", "\n", "\n", "result = stage2()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read Parquet files"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "from research.data.spark import get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "from experimental.vzhao.data import common as vz_common\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "spark = get_session()\n", "\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/qa_retriever/01_qa_data_v2/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/qa_retriever/02_qa_data_v2_chunks\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/qa_retriever/03_qa_data_v2_randneg\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/qa_retriever/04_qa_data_v2_prompt\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage1_raw\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage2_chunks\"\n", "path = \"/mnt/efs/spark-data/temp_weekly/vzhao/butanol/repos_with_qa/stage2_prompt\"\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "print(df.count())\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["row = df.iloc[1]\n", "print(row['documents_with_questions'][0]['path'])\n", "print(row['question'])\n", "\n", "from experimental.vzhao.data import common\n", "chunks = common.deserialize_retrieved_chunks(row['train_chunks'])\n", "\n", "print(chunks[0].path, row['labels'][0])\n", "for c in chunks[1:]:\n", "    print(c.path)"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import importlib\n", "from base.static_analysis import usage_analysis\n", "from pathlib import Path\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240411_butanol.data.pandas_functions\"\n", ")\n", "print(gold_path)\n", "rst = pandas_functions.create_distance_sample_files(1)(gold_path, target_paths, 10)\n", "\n", "for r in rst:\n", "    print(r, usage_analysis.path_distance(Path(gold_path), Path(r)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import importlib\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240402_dense_signature.data.pandas_functions\"\n", ")\n", "importlib.reload(pandas_functions)\n", "\n", "rst = list(pandas_functions.random_signature_chunks()(df))\n", "rst[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["x = []\n", "y = []\n", "row_ids = []\n", "for idx, row in df.iterrows():\n", "    ppl_scores = json.loads(row[\"ppl_scores\"])\n", "    if (max_dense := max(ppl_scores[:40])) and (\n", "        max_sig := max(ppl_scores[40:], default=None)\n", "    ):\n", "        x.append(max_dense)\n", "        y.append(max_sig)\n", "        row_ids.append(idx)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["np.a<PERSON><PERSON><PERSON>(y)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["print(row_ids[285], x[285], y[285])"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["print(df.iloc[318, :][\"prefix\"])"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["print(df.iloc[318, :][\"middle\"])"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["json.loads(df.iloc[318, :][\"ppl_scores\"])[40:]"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [], "source": ["print(json.loads(df.iloc[318, :][\"signature_chunks\"])[0])"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [], "source": ["from research.retrieval.libraries.types import Chunk"]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [], "source": ["Chunk(**json.loads(df.iloc[318, :][\"signature_chunks\"])[0])"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["json.loads(df.iloc[472, :][\"ppl_scores\"])[:40]"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.scatter(x, y)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["ppl_scores[42]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["max(ppl_scores[40:])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["len(ppl_scores)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "foo = json.loads(df[\"retrieved_chunks\"][0])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["len(json.loads(df[\"retrieved_chunks\"][0]))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["json.loads(df[\"signature_chunks\"][0])[0].keys()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["len(json.loads(df[\"ppl_scores\"][0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "json.loads(df[\"retrieved_chunks\"][0])[7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages import common as spark_common\n", "\n", "print(tokenizer.detokenize(spark_common.unpack_tokens(df[\"prompt_tokens\"][0][5])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Size"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_filtered_tokens/'\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_1stsp_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_1stsp_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_1stsp_tokens/\"\n", "path = (\n", "    \"/mnt/efs/spark-data/temp_weekly/vzhao/qa_retriever/04_qa_data_v2_prompt\"\n", ")\n", "\n", "spark = k8s_session(max_workers=8)\n", "# spark = get_session()\n", "print(path, flush=True)\n", "df = spark.read.parquet(path)\n", "print(f\"Examples: {df.count()}\")\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Indexed Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def rstrip(tokens, id):\n", "    while tokens[-1] == id:\n", "        tokens = tokens[:-1]\n", "    return tokens"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import IndexedDataset, MMapIndexedDataset\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "\n", "# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", "path = \"/mnt/efs/augment/user/vzhao/data/qa_retriever/v2_qa_data_v2/dataset\"\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "path = (\n", "    \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", ")\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(f\"size: {data.sizes.sum()}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["data.sizes.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
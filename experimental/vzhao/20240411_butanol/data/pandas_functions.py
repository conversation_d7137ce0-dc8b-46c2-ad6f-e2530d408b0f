"""Pandas functions."""

import random
from pathlib import Path
from typing import Callable, Iterator

import numpy as np
import pandas as pd
import torch

from base.static_analysis import usage_analysis
from experimental.vzhao.data import common
from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.core.types import EMPTY_CHUNK, Chunk, Document
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval import chunking_functions
from research.retrieval import utils as rutils
from research.retrieval.chunk_formatters import get_chunk_formatter


def create_get_all_chunks(chunker):
    """Returns a row-wise function to be applied to a DataFrame."""

    @map_parquet.passthrough_feature()
    @map_parquet.allow_unused_args()
    def get_all_chunks(files: dict[str, str]) -> pd.Series:
        """This is a row-wise function to be applied to a DataFrame."""
        all_chunks = {
            f"{p}#{idx}": c
            for p, t in files.items()
            for idx, c in enumerate(
                chunker.split_into_chunks(
                    Document(
                        text=t,
                        id=str(hash(p + t)),
                        path=p,
                    )
                )
            )
        }

        return pd.Series(
            {
                "all_chunks": {
                    k: common.serialize_retrieved_chunks([v])
                    for k, v in all_chunks.items()
                },
            }
        )

    return get_all_chunks


@map_parquet.passthrough_feature()
@map_parquet.allow_unused_args()
def parse_file_list(file_list) -> Iterator[pd.Series]:
    """Drops the row if the column `max_middle_len` is too large."""
    output: dict[str, str] = {}
    for file in file_list:
        output[file["max_stars_repo_path"]] = file["content"]
    yield pd.Series({"files": output})


def process_repo(
    *,
    num_negatives,
    chunker,
    sample_chunk_fn: Callable[[str, list[str], int], list[str]],
):
    """Sample random negative chunks and yield training data."""

    @map_parquet.allow_unused_args()
    def _sample_negatives(
        documents_with_questions, files: dict[str, str]
    ) -> Iterator[pd.Series]:
        """Sample negatives."""
        all_chunks = _get_all_chunks(files)

        for gold_doc in documents_with_questions:
            gold_path = gold_doc["path"]
            gold_text = gold_doc["text"]
            for question in gold_doc["questions"]:
                train_chunks = [
                    get_random_chunk_from_doc(gold_path, gold_text, chunker)
                ]
                labels = [1]

                # Samples random negative chunks.
                neg_chunk_keys = sample_chunk_fn(
                    gold_path, list(all_chunks.keys()), num_negatives
                )
                if not neg_chunk_keys:
                    continue
                for k in neg_chunk_keys:
                    # train_chunks.append(get_random_chunk_from_doc(k, files[k], chunker))
                    train_chunks.append(all_chunks[k])
                    labels.append(0)

                yield pd.Series(
                    {
                        "question": question["question"],
                        # "answers": question["answers"],
                        "gold_doc": {
                            "path": gold_path,
                            "text": gold_text,
                        },
                        "train_chunks": common.serialize_retrieved_chunks(train_chunks),
                        "labels": labels,
                    }
                )

    return _sample_negatives


def _get_all_chunks(files: dict[str, str]) -> dict[str, Chunk]:
    """Returns all chunks in a dictionary."""
    chunks = {}
    for k, v in files.items():
        for idx, c in enumerate(
            chunking_functions.LineLevelChunker(
                max_lines_per_chunk=120
            ).split_into_chunks(
                Document(
                    text=v,
                    id=str(hash(k + v)),
                    path=k,
                )
            )
        ):
            chunks[f"{k}#{idx}"] = c
    return chunks


def get_random_chunk_from_doc(
    path: str, text: str, chunker: chunking_functions.Chunker
) -> Chunk:
    """Returns a random chunk from a document."""
    doc = Document(
        text=text,
        id=str(hash(path + text)),
        path=path,
    )
    chunks = chunker.split_into_chunks(doc)
    return random.choice(chunks) if chunks is not None else EMPTY_CHUNK


def create_distance_sample_chunks(temperature: float):
    """Returns a function to sample chunks from a list of target paths."""

    def impl(source_path: str, target_paths: list[str], k: int) -> list[str]:
        # Do not sample negatives from source path.
        valid_paths = [t for t in target_paths if t.split("#")[0] != source_path]
        if len(valid_paths) < k:
            return []

        # Target_paths might be {file_path}#{chunk_id}
        weights = torch.Tensor(
            [
                -usage_analysis.path_distance(Path(source_path), Path(t.split("#")[0]))
                / temperature
                for t in valid_paths
            ]
        )
        probs = torch.nn.functional.softmax(weights)
        return np.random.choice(
            valid_paths, p=probs.numpy(), size=k, replace=False
        ).tolist()

    return impl


def create_prompt_formatter(formatter_config):
    cls_name, kwargs = rutils.parse_yaml_config(formatter_config)
    return get_prompt_formatter(cls_name, **kwargs)


def create_chunk_formatter(formatter_config):
    cls_name, kwargs = rutils.parse_yaml_config(formatter_config)
    return get_chunk_formatter(cls_name, **kwargs)

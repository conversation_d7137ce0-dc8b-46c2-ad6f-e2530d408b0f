{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import time\n", "import logging\n", "from collections.abc import Iterable\n", "from datetime import datetime\n", "from dataclasses import dataclass\n", "\n", "from base.datasets import tenants, completion, completion_dataset\n", "from services.api_proxy.client.client import AugmentClient, UploadContent\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Downloads the replays from the augment-replays bucket."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets.sxs import sampler\n", "dataset, completions = sampler.sample_challenge_set()\n", "print(f'Found: {len(completions)}')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets.sxs import sampler\n", "from base.datasets import replay_utils\n", "\n", "# Your api token should either be in $AUGMENT_TOKEN or in ~/.config/augment/api_token\n", "# dog_food_client = replay_utils.get_augment_client(\n", "#     url=\"https://dogfood.api.augmentcode.com\"\n", "# )\n", "\n", "client = replay_utils.get_augment_client(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\"\n", ")\n", "\n", "sampler.ensure_blobs_exist(client, dataset, completions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replays"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os \n", "import pathlib\n", "\n", "\n", "# MODEL_NAME = \"roguesl-farpref-16B-seth6-p1024\"\n", "MODEL_NAME = \"roguesl-farpref-16B-seth6-p512\"\n", "\n", "from base.datasets import replay\n", "\n", "replayed_responses = replay.replay(client, MODEL_NAME, completions)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepares Google Sheet for rating"]}, {"cell_type": "code", "execution_count": 342, "metadata": {}, "outputs": [], "source": ["# Get credentials\n", "\n", "from base.datasets import google_sheet_utils\n", "creds = google_sheet_utils.get_google_sheet_credentials()\n", "service = google_sheet_utils.get_google_sheets_service(creds)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from base.datasets import google_sheet_utils\n", "\n", "root = pathlib.Path(f\"/mnt/efs/augment/user/{os.getlogin()}/sxs/\")\n", "suffix = 'reject'\n", "eval_name = f\"{MODEL_NAME.replace('-', '_')}_{suffix}\"\n", "eval_dir = root / eval_name\n", "\n", "spreadsheet_id = \"1rv3pirCmTdlBvfrJfy_eCikGihxxusRO5nU9_Bd_Hf0\"\n", "title = f\"{MODEL_NAME} vs. PROD {suffix}\"\n", "\n", "google_sheet_utils.create_new_sheet(service, spreadsheet_id, title)\n", "\n", "# Write the instruction row.\n", "google_sheet_utils.update_row(\n", "    service,\n", "    spreadsheet_id,\n", "    title,\n", "    1,\n", "    values=[\"\"\"Instruction: \"\"\"],\n", ")\n", "\n", "# Write the header row.\n", "google_sheet_utils.update_row(\n", "    service,\n", "    spreadsheet_id,\n", "    title,\n", "    2,\n", "    values=[\n", "        \"original_request_id\",\n", "        \"original_completion\",\n", "        \"new_request_id\",\n", "        \"new_completion\",\n", "        \"sxs_link\",\n", "        \"ratings\",\n", "        \"comments\",\n", "    ],\n", ")\n", "\n", "ROW_OFFSET = 7\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from termcolor import colored\n", "from base.datasets import google_sheet_utils\n", "from base.datasets.sxs import utils\n", "\n", "\n", "if not os.path.exists(eval_dir):\n", "    os.makedirs(eval_dir)\n", "\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "SUPPORT_VZHAO = \"https://support.dev-vzhao.t.us-central1.dev.augmentcode.com/request/\"\n", "\n", "\n", "# Print out the differences for each request.\n", "for idx, replay in enumerate(replayed_responses[:30]):\n", "    if replay.response.model != 'roguesl-farpref-16B-eth6-c':\n", "        continue\n", "    print(f\"== Original request id: {replay.request_id} ({replay.response.model}) ==\")\n", "    print(colored(replay.response.text, \"green\"))\n", "    print(\n", "        f\"== Replayed request id: {replay.replayed_request_id} ({replay.replayed_response.model}) ==\"\n", "    )\n", "    print(colored(replay.replayed_response.text, \"blue\"))\n", "\n", "    html = utils.make_sxs_html(replay)\n", "    filename = f\"{replay.replayed_request_id}.html\"\n", "    with open(eval_dir / filename, \"w\") as f:\n", "        f.write(html)\n", "\n", "    google_sheet_utils.update_row(\n", "        service,\n", "        spreadsheet_id,\n", "        title,\n", "        idx + ROW_OFFSET + 1,\n", "        values=[\n", "            f'=HYPERLINK(\"{SUPPORT_DOGFOOD}{replay.request_id}\", \"{replay.request_id}\")',\n", "            replay.response.text,\n", "            f'=HYPERLINK(\"{SUPPORT_VZHAO}{replay.replayed_request_id}\", \"{replay.replayed_request_id}\")',\n", "            replay.replayed_response.text,\n", "            f\"http://localhost:8000/{eval_name}/{filename}\",\n", "        ],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [], "source": ["!pip install textdistance"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Sample random\n", "\n", "from base.datasets import completion, tenants\n", "from google.cloud import bigquery, storage\n", "from typing import Literal, Optional, TypedDict, cast\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, PathAndContent\n", "\n", "query = \"\"\"SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND rand() < 0.01\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND resolution.event_type = 'completion_resolution'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND response.completion_length >= 1\n", "        AND resolution.accepted_idx < 0\n", "        ORDER BY request.request_id\n", "        LIMIT 500\n", "\"\"\"\n", "\n", "dataset = completion_dataset.CompletionDataset.create_from_query(query,tenants.get_tenant(TENANT_NAME),)\n", "\n", "\n", "\n", "TENANT_NAME = \"dogfood\"\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "bucket = storage.Client(tenant.project_id).bucket(tenant.blob_bucket_name)\n", "blob_cache = GCSBlobCache(\n", "    bucket,\n", "    tenant.blob_bucket_prefix,\n", "    2**30,\n", "    num_threads=10,\n", ")\n", "bigquery_client = bigquery.Client(tenant.project_id)\n", "rows = bigquery_client.query_and_wait(query)\n", "dataset = completion_dataset.CompletionDataset(\n", "    rows,\n", "    cast(Optional[int], rows.total_rows),\n", "    blob_cache=blob_cache,\n", ")\n", "completions = list(\n", "    [\n", "        c\n", "        for c in dataset.get_completions()\n", "        if c.response.model == \"roguesl-farpref-16B-eth6-c\"\n", "    ]\n", ")\n", "print(len(completions))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Replay\n", "\n", "import os \n", "import pathlib\n", "\n", "from base.datasets.sxs import sampler\n", "from base.datasets import replay\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "    token=\"B41CEAD9-B173-4A42-9AD4-1B5DDD486489\",\n", ")\n", "sampler.ensure_blobs_exist(client, dataset, completions)\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# MODEL_NAME = \"roguesl-farpref-16B-seth6-p1024\"\n", "MODEL_NAME = \"roguesl-farpref-16B-seth6-p512\"\n", "replayed_responses = replay.replay(client, MODEL_NAME, completions)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Sort by editing distance.\n", "\n", "import tqdm\n", "import textdistance\n", "from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer\n", "\n", "tokenizer = TiktokenStarCoderTokenizer()\n", "\n", "scored = []\n", "for rr in tqdm.tqdm(replayed_responses):\n", "    old = rr.response.text\n", "    new = rr.replayed_response.text\n", "    distance = textdistance.levenshtein.distance(\n", "        tokenizer.tokenize_safe(old), tokenizer.tokenize_safe(new)\n", "    )\n", "    scored.append((distance, rr))\n", "\n", "scored = sorted(scored, key=lambda x: x[0], reverse=True)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["accepted_scored = sorted(scored, key=lambda x: x[0], reverse=True)\n", "replayed_responses = [e[1] for e in accepted_scored]\n", "len(accepted_scored)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["accepted_scored[20][0]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["rejected_scored = sorted(scored, key=lambda x: x[0], reverse=True)\n", "replayed_responses = [e[1] for e in rejected_scored]\n", "len(replayed_responses)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["rejected_scored[30][0]"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["dd"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.ticker import PercentFormatter\n", "\n", "max_value = 20\n", "\n", "plt.figure(figsize=(8, 3))\n", "dd = plt.hist(\n", "    [\n", "        [min(s[0], max_value + 1) for s in accepted_scored],\n", "        [min(s[0], max_value + 1) for s in rejected_scored],\n", "    ],\n", "    # 30,\n", "    bins=range(0, max_value+2),\n", "    density=True,\n", "    histtype=\"bar\",\n", "    # alpha=0.7,\n", "    # cumulative=-1,\n", "    # cumulative=True,\n", "    align=\"left\",\n", ")\n", "# plt.gca().yaxis.set_major_formatter(PercentFormatter(1))\n", "\n", "plt.legend([\"Accepted\", \"Rejected\"])\n", "plt.xlabel('Token-level Edit Distance of Completions')\n", "plt.xticks(ticks=np.arange(0, 25, 5), labels=['0', '5', '10', '15', '20+'])\n", "plt.ylabel('Probability')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [], "source": ["dd[0].sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Missing Blobs"]}, {"cell_type": "code", "execution_count": 290, "metadata": {}, "outputs": [], "source": ["name = \"521b60d07f1e792424b37d67e0221d5b69502e5de4c15f183768d5abe30ab974\""]}, {"cell_type": "code", "execution_count": 293, "metadata": {}, "outputs": [], "source": ["dog_food_client = AugmentClient(\n", "    url=\"https://dogfood.api.augmentcode.com\",\n", "    token=\"B41CEAD9-B173-4A42-9AD4-1B5DDD486489\",\n", ")\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "    token=\"B41CEAD9-B173-4A42-9AD4-1B5DDD486489\",\n", ")"]}, {"cell_type": "code", "execution_count": 296, "metadata": {}, "outputs": [], "source": ["client.find_missing([name])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
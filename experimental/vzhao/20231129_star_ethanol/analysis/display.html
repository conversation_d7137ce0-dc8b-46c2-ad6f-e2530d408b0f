<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Display</title>
    <style>
        pre {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            padding: 10px;
            box-shadow: 1px 1px 2px #bbb;
        }

        .toggle {
            color: #333;
            border: 2px solid #333;
            padding: 3px 15px;
            cursor: pointer;
            background-color: transparent;
            border-radius: 5px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .toggle:hover {
            background-color: #404040;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/diff/dist/diff.min.js"></script>
</head>

<body>
    <pre><code>import { ContextRoots } from "./extension";
import { RingBuffer } from "./ring-buffer";
import { BlobNameCalculator } from "./blob-name-calculator";

import { exec, ExecOptions } from "child_process";
import { getLogger } from "./logging";
import { FeatureFlagManager, FeatureFlagChangeEvent, FeatureFlags } from "./feature-flags";
import { Logger } from "winston";
import { ErrorHandler, WorkQueue } from "./work-queue";
import { APIServer, BlobMetadata } from "./augment-api";
import { APIError, APIStatus } from "./exceptions";</code></pre>

    <blockquote id="section1" prefix="Original" request-id="58512ff9-45ca-4f45-8446-be352e2c3bdb"
        url="https://support.dogfood.t.us-central1.prod.augmentcode.com/request/58512ff9-45ca-4f45-8446-be352e2c3bdb"
        completion-host="roguesl-farpref-16B-eth6-c"></blockquote>

    <div id="button1" class="toggle">
        <pre><code>def generate(instruction):
    return client.generate(instruction)
</code></pre>
    </div>


    <blockquote id="section2" prefix="Replayed" request-id="be12f321-0832-4bcc-a62b-55a32d73aaac" url=""
        completion-host="roguesl-farpref-16B-seth6-p1024">
    </blockquote>

    <div id="button2" class="toggle">
        <pre><code>def generate(text):
    return client.generate(text)
    </code></pre>
    </div>


    <blockquote>Diff</blockquote>

    <div id="difference"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            showShort('section1');
            showShort('section2');
            compareText();
        });

        document.getElementById("button1").onclick = function () {
            toggleChange("section1");
            toggleChange("section2");
        };
        document.getElementById("button2").onclick = function () {
            toggleChange("section1");
            toggleChange("section2");
        };
        function toggleChange(id) {
            var section = document.getElementById(id);
            const prefix = section.getAttribute("prefix");
            var requestId = section.getAttribute("request-id");
            var completionHost = section.getAttribute("completion-host");
            var url = section.getAttribute("url");

            var shortText = `== request id: <a href="${url}">${requestId}</a> ==`
            var longText = `== ${prefix} request id: <a href="${url}">${requestId}</a> ("${completionHost}") ==`

            if (section.innerHTML === shortText) {
                // if (section.innerHTML.includes("prefix")) {
                section.innerHTML = longText;
            } else {
                section.innerHTML = shortText;
            }
        }
        function showShort(id) {
            var section = document.getElementById(id);
            var requestId = section.getAttribute("request-id");
            var url = section.getAttribute("url");
            var shortText = `== request id: <a href="${url}">${requestId}</a> ==`
            section.innerHTML = shortText;
        }

        function compareText() {
            var text1 = document.getElementById('button1').innerText;
            var text2 = document.getElementById('button2').innerText;

            // Use jsdiff to find the difference
            var diff = Diff.diffWords(text1, text2);

            // Prepare HTML to display differences
            var display = diff.map(function (part) {
                // Styling: green for additions, red for deletions, grey for common parts
                var color = part.added ? 'green' :
                    part.removed ? 'red' : 'grey';
                var span = document.createElement('span');
                span.style.color = color;
                span.appendChild(document.createTextNode(part.value));
                return span.outerHTML;
            }).join('');

            document.getElementById('difference').innerHTML = `<pre><code>${display}</code></pre>`;;
        }



    </script>

</body>

</html>

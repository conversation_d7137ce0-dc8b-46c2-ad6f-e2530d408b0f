{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from multiprocessing.pool import ThreadPool, Pool\n", "import re\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "tokenizer = StarCoderTokenizer()\n", "import pandas as pd\n", "from experimental.vzhao.notebooks import utils as vz_utils \n", "pd.set_option('display.precision', 2)\n", "\n", "def helper(key, exp):\n", "    results = vz_utils.load(exp)\n", "    output = []\n", "    for pid in results:\n", "        # This works for 'comment2' style prompt.\n", "        num_chunks_in_prompt = sum(\n", "            [\n", "                \"# the below code fragment can be found in:\" == line\n", "                for line in results[pid][\"prompt\"].splitlines()\n", "            ]\n", "        )\n", "        # This works for <PERSON> prompt.\n", "        num_chunks_in_prompt = max( num_chunks_in_prompt, len(re.findall('<filename>', results[pid][\"prompt\"])))\n", "\n", "        output.append(\n", "            \n", "            {\n", "                \"experiment\": key,\n", "                \"patch_id\": pid,\n", "                \"pass/fail\": results[pid][\"result\"],\n", "                \"num_chunks_in_prompt\": num_chunks_in_prompt,\n", "                \"line_prompt_minus_line_suffix\": len(\n", "                    results[pid][\"prompt\"].split(\"<fim_middle>\")[0]\n", "                ),\n", "                \"sequence_length\": len(tokenizer.tokenize(results[pid][\"prompt\"])),\n", "                \"weight\": exp.weight,\n", "            }\n", "        )\n", "    return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENTS = {}\n", "\n", "EXPERIMENTS.update({\n", "    '1b_16.1_H100_1epoch_bs512_ls4096': vz_utils.Experiment(\n", "        poc='vzhao',\n", "        eval_dir='/mnt/efs/augment/eval/jobs/kTMDHjWq',\n", "        description='Sanity Check with empty completion. All test cases should fail.',\n", "        weight=128.0,\n", "    ),\n", "})"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
# Usage
# python research/eval/eval.py --v2 experimental/vzhao/20231129_star_ethanol/eval/alllang/dffb1m_16b_igor_ethanor6_16.1.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b_alphal_fixtoken, original igor_ethanor6_16.1, 30Line<PERSON>hunk, all_languages_2-3lines
  workspace: Dev
  project: vzhao-eval
import_modules: experimental.igor.systems.ethanol
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: ethanol
      # checkpoint_path: star_ethanol/igor_ethanor6_16.1
      checkpoint_path: ethanol/ethanol6-16.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      add_path: True
      add_suffix: True
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: True
  experimental:
    remove_suffix: False
    retriever_top_k: 100
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  hydra_block_resource_internet_access: true
podspec: 1xA100.yaml

"""A script to launch Hydra evals.

Read yaml template and update with the appropriate values.
Usually change:
    model.checkpoint_path:
    retriever.scorer.checkpoint:
    retriever.query_formatter
    retriever.document_formatter

Usage:
python experimental/vzhao/20231129_star_ethanol/eval/run_hydra_evals_ethanol6_formatters.py \
    --det_id=31223 \
    --scorer_name=ethanol
"""
import pathlib
import subprocess
import tempfile

import yaml
from absl import app, flags
from determined.experimental import client

_DET_ID = flags.DEFINE_integer(
    "det_id", None, "Determined experiment id.", required=True
)

_UUID = flags.DEFINE_string("uuid", None, "Checkpoint uuid.", required=False)

_SCORER_NAME = flags.DEFINE_enum(
    "scorer_name",
    None,
    ["starcoder_1b", "ethanol"],
    "Scorer name.",
    required=True,
)

ADDITIONAL_TAGS = [
    "v1",
]

RUN_EVAL = [
    "multiline",
    "all_lang",
    "api",
]

LM_MODELS = [
    {
        "name": "rogue",
        "checkpoint_path": "rogue/diffb1m_16b_alphal_fixtoken",
        "prompt": {
            "max_prefix_tokens": 1280,
            "max_prompt_tokens": 3816,
            "max_retrieved_chunk_tokens": -1,
            "max_suffix_tokens": 768,
            # NOTE: Change this to control the max number of retrieved chunks in the prompt.
            "max_number_chunks": 32,
        },
    },
    {
        "name": "rogue",
        "checkpoint_path": "rogue/16b_eth61m_depause",
        "prompt": {
            "max_prefix_tokens": 1280,
            "max_suffix_tokens": 768,
            "max_retrieved_chunk_tokens": -1,
            "max_prompt_tokens": 3816,
            "max_filename_tokens": 50,
            "always_use_suffix_token": True,
            "only_truncate_true_prefix": True,
            # NOTE: Change this to control the max number of retrieved chunks in the prompt.
            "max_number_chunks": 32,
        },
    },
]


# Ethanol 6 formatters.
QUERY_FORMATTER = {
    "name": "ethanol6_query",
    "max_tokens": 1023,
    "add_path": True,
    "add_suffix": True,
    "prefix_ratio": 0.9,
}

DOC_FORMATTER = {
    "name": "ethanol6_document",
    "max_tokens": 999,
    "add_path": True,
    "add_prefix": False,
    "add_suffix": False,
}

# Ethanol formatters w/ scope path features.


# These values will be used to udpate the config.
OVERRIDES = {
    # "system.retriever.chunker.include_scope_annotation": False,
}


def maybe_download_ckpt() -> str:
    """Download the checkpoint if it doesn't exist."""
    experiment = client.get_experiment(_DET_ID.value)
    trial = experiment.get_trials()[0]
    if _UUID.value:
        ckpt = trial.select_checkpoint(uuid=_UUID.value)
    else:
        ckpt = trial.select_checkpoint(latest=True)
    uuid = ckpt.uuid
    model_name = experiment.get_config()["name"].replace("-", "_").lower()
    ckpt_root = pathlib.Path("/mnt/efs/augment/checkpoints/")
    ckpt_path = f"star_ethanol/{model_name}_{ckpt.metadata['steps_completed']}"
    print(f"checkpoint path: {ckpt_root/ckpt_path}")
    if (ckpt_root / ckpt_path).exists():
        return ckpt_path
    subprocess.run(
        f"bash research/utils/download_checkpoint.sh {uuid} {ckpt_path}",
        shell=True,
        check=False,
    )
    return ckpt_path


def override(config: dict) -> dict:
    """Override the config with the appropriate values."""
    for key_path, value in OVERRIDES.items():
        root = config
        keys = key_path.split(".")
        for key in keys[:-1]:
            root = root[key]
        root[keys[-1]] = value
    return config


def main(argvs):
    del argvs
    ckpt_path = maybe_download_ckpt()

    SCORER = {
        "name": _SCORER_NAME.value,
        "checkpoint_path": ckpt_path,
    }
    for LM_MODEL in LM_MODELS:
        # DET experiment name.
        NAME = [
            pathlib.Path(LM_MODEL["checkpoint_path"]).name,
            pathlib.Path(ckpt_path).name,
            str(_DET_ID.value),
        ] + ADDITIONAL_TAGS

        if "multiline" in RUN_EVAL:
            # Evaluate python multiline.
            with pathlib.Path(
                "/home/<USER>/augment/experimental/vzhao/eval/templates/multiline_default_template.yml"
            ).open("r") as f:
                config = yaml.safe_load(f)

            config["system"]["model"] = LM_MODEL
            config["system"]["retriever"]["scorer"] = SCORER
            config["system"]["retriever"]["query_formatter"] = QUERY_FORMATTER
            config["system"]["retriever"]["document_formatter"] = DOC_FORMATTER
            config["determined"]["name"] = ", ".join(NAME + ["repoeval_2-3lines"])
            override(config)

            with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
                yaml.dump(config, f)
                print(f.name)
                subprocess.run(
                    f"python research/eval/eval.py --v2 {f.name}",
                    shell=True,
                    check=False,
                )

        if "all_lang" in RUN_EVAL:
            # Evaluate all lang.
            with pathlib.Path(
                "/home/<USER>/augment/experimental/vzhao/eval/templates/alllang_default_template.yml"
            ).open("r") as f:
                config = yaml.safe_load(f)

            config["system"]["model"] = LM_MODEL
            config["system"]["retriever"]["scorer"] = SCORER
            config["system"]["retriever"]["query_formatter"] = QUERY_FORMATTER
            config["system"]["retriever"]["document_formatter"] = DOC_FORMATTER
            config["determined"]["name"] = ", ".join(NAME + ["all_languages_2-3lines"])
            override(config)

            with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
                yaml.dump(config, f)
                print(f.name)
                subprocess.run(
                    f"python research/eval/eval.py --v2 {f.name}",
                    shell=True,
                    check=False,
                )

        if "api" in RUN_EVAL:
            # Evaluate API
            with pathlib.Path(
                "/home/<USER>/augment/experimental/vzhao/eval/templates/api_default_template.yml"
            ).open("r") as f:
                config = yaml.safe_load(f)

            config["system"]["model"] = LM_MODEL
            config["system"]["retriever"]["scorer"] = SCORER
            config["system"]["retriever"]["query_formatter"] = QUERY_FORMATTER
            config["system"]["retriever"]["document_formatter"] = DOC_FORMATTER
            config["determined"]["name"] = ", ".join(
                NAME + ["finegrained-python.large"]
            )
            override(config)

            with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
                yaml.dump(config, f)
                print(f.name)
                subprocess.run(
                    f"python research/eval/eval.py --v2 {f.name}",
                    shell=True,
                    check=False,
                )


if __name__ == "__main__":
    app.run(main)

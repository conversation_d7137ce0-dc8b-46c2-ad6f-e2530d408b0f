"""A script to launch Hydra evals.

Read yaml template and update with the appropriate values.
Usually change:
    model.checkpoint_path:
    retriever.scorer.checkpoint:
    retriever.query_formatter
    retriever.document_formatter

Usage:
python experimental/vzhao/20231129_star_ethanol/eval/run_hydra_evals_star_ethanol6_formatters.py \
    --det_id=29241 \
    --scorer_name=starcoder_1b
"""

from absl import app, flags
from augment.experimental.vzhao.eval import common as eval_common

_DET_ID = flags.DEFINE_integer(
    "det_id", None, "Determined experiment id.", required=True
)

_UUID = flags.DEFINE_string("uuid", None, "Checkpoint uuid.", required=False)

_SCORER_NAME = flags.DEFINE_enum(
    "scorer_name",
    None,
    ["starcoder_1b", "ethanol"],
    "Scorer name.",
    required=True,
)

RUN_EVAL = [
    "multiline",
    "all_lang",
    "api",
    "cceval",
]

MAX_NUMBER_CHUNKS = 1

LM_MODELS = [
    {
        "name": "rogue",
        "checkpoint_path": "rogue/diffb1m_16b_alphal_fixtoken",
        "prompt": {
            "max_prefix_tokens": 1280,
            "max_prompt_tokens": 3816,
            "max_retrieved_chunk_tokens": -1,
            "max_suffix_tokens": 768,
            # NOTE: Change this to control the max number of retrieved chunks in the prompt.
            "max_number_chunks": MAX_NUMBER_CHUNKS,
        },
    },
    {
        "name": "rogue",
        "checkpoint_path": "rogue/16b_eth61m_depause",
        "prompt": {
            "max_prefix_tokens": 1280,
            "max_suffix_tokens": 768,
            "max_retrieved_chunk_tokens": -1,
            "max_prompt_tokens": 3816,
            "max_filename_tokens": 50,
            "always_use_suffix_token": True,
            "only_truncate_true_prefix": True,
            # NOTE: Change this to control the max number of retrieved chunks in the prompt.
            "max_number_chunks": MAX_NUMBER_CHUNKS,
        },
    },
]


# Ethanol6 formatters.
QUERY_FORMATTER = {
    "name": "ethanol6_query",
    "max_tokens": 1023,
    "add_path": True,
    "add_suffix": True,
    "prefix_ratio": 0.9,
}

DOC_FORMATTER = {
    "name": "ethanol6_document",
    "max_tokens": 999,
    "add_path": True,
    "add_prefix": False,
    "add_suffix": False,
}

# # StarEthanol6 formatters.
# QUERY_FORMATTER = {
#     "name": "star_ethanol6_query",
#     "max_tokens": 1023,
#     "add_path": True,
#     "add_scope_path": "first_line",
#     "add_suffix": True,
#     "prefix_ratio": 0.9,
# }

# DOC_FORMATTER = {
#     "name": "star_ethanol6_document",
#     "max_tokens": 999,
#     "add_path": True,
#     "add_scope_path": "first_line",
#     "add_prefix": False,
#     "add_suffix": False,
# }

FORMATTER_TAGS = [
    # "add_scope_path",
    f"include_{MAX_NUMBER_CHUNKS}_chunks",
]


# These values will be used to udpate the config.
OVERRIDES = {
    # "system.retriever.chunker.include_scope_annotation": True,
}


def main(argvs):
    del argvs

    eval_common.run_eval_suite(
        det_id=_DET_ID.value,
        selected_evals=RUN_EVAL,
        lm_models=LM_MODELS,
        scorer={
            "name": _SCORER_NAME.value,
            "checkpoint_path": eval_common.maybe_download_ckpt(
                det_id=_DET_ID.value, uuid=_UUID.value
            ),
        },
        query_formatter=QUERY_FORMATTER,
        doc_formatter=DOC_FORMATTER,
        overrides=OVERRIDES,
        other_tags=["star_ethanol6_formatters"] + FORMATTER_TAGS,
    )


if __name__ == "__main__":
    app.run(main)

# Usage
# python research/eval/eval.py --v2 experimental/vzhao/20231129_star_ethanol/eval/multiline/dffb1m_16b_star_ethanol_test.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b_alphal_fixtoken, StarCoder Ethanol, 30LineChunk, repoeval_2-3lines, Test
  workspace: Dev
  project: vzhao-eval
import_modules: experimental.igor.systems.ethanol
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: starcoder_1b
      checkpoint: star_ethanol/1205.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      max_lines: -1
      add_path: True
      add_suffix: True
      tokenizer_name: StarCoderTokenizer
    document_formatter:
      name: ethanol6_document
      max_tokens: 1023
      add_path: True
      add_prefix: True
      tokenizer_name: StarCoderTokenizer
  experimental:
    remove_suffix: False
    retriever_top_k: 64
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: repoeval_2-3lines
podspec: 1xA100.yaml

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["CONFIG = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7: Filtering"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# STAGE6_URI = \"s3a://igor-dev-bucket/ethanol6/ethanol6-16.1/06_shuffled/\"\n", "STAGE6_URI = \"s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/\"\n", "STAGE7_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled/\"\n", "CONFIG.update(\n", "    {\n", "        \"ppl_reduction\": \"mean\",\n", "        # \"ppl_reduction\": \"mean_linear\",\n", "    }\n", ")\n", "print(STAGE7_URI)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["!s3cmd rm --recursive {STAGE7_URI.replace(\"s3a://\", \"s3://\")}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from experimental.vzhao.data import pandas_functions\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "def stage7():\n", "    spark = k8s_session(\n", "        name=\"vzhao-starethanol\",\n", "        max_workers=96,\n", "        # max_workers=8,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.blacklist.node.maxFailedTasksPerNode\": 1,\n", "        },\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                # Adds `chunk.meta[\"scope_paths\"]`.\n", "                pandas_functions.add_scope_path_to_linechunk_fn,\n", "            ]\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        batch_size=1000,\n", "        # batch_size=10,\n", "        timeout=7200,\n", "        # timing_run=True,\n", "    )\n", "    spark.stop()\n", "    return result\n", "\n", "\n", "result = stage7()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 7B: Add `LineAnnotation`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STAGE6_URI = \"s3a://igor-dev-bucket/ethanol6/ethanol6-16.1/06_shuffled/\"\n", "STAGE6_URI = \"s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/\"\n", "STAGE7_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd rm --recursive {STAGE7_URI.replace(\"s3a://\", \"s3://\")}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Iterator, Optional, Sequence, Any\n", "\n", "from experimental.vzhao.data import pandas_functions\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.retrieval import chunking_functions\n", "from research.retrieval.types import Chunk, Document\n", "\n", "\n", "def serialize_retrieved_chunks(\n", "    retrieved_chunks: Sequence[Chunk],\n", "    add_parent_doc_text: bool = False,\n", ") -> str:\n", "    \"\"\"Serializes retrieved chunks to string.\"\"\"\n", "\n", "    def to_dict(chunk: Chunk) -> dict[str, Any]:\n", "        chunk_dict = {\n", "            \"id\": chunk.id,\n", "            \"text\": chunk.text,\n", "            \"parent_doc\": {\n", "                \"id\": chunk.parent_doc.id,\n", "                \"path\": chunk.parent_doc.path,\n", "                # WARNING: just storing empty string, we don't want to store  file\n", "                \"text\": chunk.parent_doc.text if add_parent_doc_text else \"\"\n", "                # Not supporting meta field\n", "            },\n", "            \"char_offset\": chunk.char_offset,\n", "            \"length\": chunk.length,\n", "            \"line_offset\": chunk.line_offset,\n", "            \"length_in_lines\": chunk.length_in_lines,\n", "            \"meta\": {\n", "                \"line_annotations\": {\n", "                    key: chunk.meta[\"line_annotations\"][key].to_dict()\n", "                    for key in chunk.meta[\"line_annotations\"]\n", "                }\n", "            },\n", "        }\n", "        return chunk_dict\n", "\n", "    return json.dumps([to_dict(chunk) for chunk in retrieved_chunks])\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> list[Chunk]:\n", "    \"\"\"Deserializes retrieved chunks.\"\"\"\n", "\n", "    def to_chunk(dict_: dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def add_scope_path_to_linechunk(chunk: Chunk) -> Chunk:\n", "    assert chunk.parent_doc is not None\n", "    scope_paths = chunking_functions._doc_to_scope_paths(\n", "        chunk.parent_doc, parse_errored_root=False, merge_empty_chunks=True\n", "    )\n", "    selected = chunking_functions._range_search_scope_paths(scope_paths, chunk.line_range)\n", "    line_annotations = chunking_functions.create_line_annotations(\n", "        selected, chunk.line_range\n", "    )\n", "    chunk.meta = chunk.meta or {}\n", "    chunk.meta[\"line_annotations\"] = line_annotations\n", "    return chunk\n", "\n", "\n", "@map_parquet.passthrough_feature()\n", "@map_parquet.allow_unused_args()\n", "def add_scope_path_to_linechunk_fn(retrieved_chunks) -> pd.Series:\n", "    \"\"\"Adds a new feature `scope_path` to the `retrieved_chunks`.\"\"\"\n", "    chunks = deserialize_retrieved_chunks(retrieved_chunks)\n", "    for idx, chunk in enumerate(chunks):\n", "        if chunk.parent_doc is None:\n", "            # Skips the chunk with no parent doc.\n", "            continue\n", "        chunk = add_scope_path_to_linechunk(chunk)\n", "        assert chunk.meta is not None\n", "        chunks[idx] = chunk\n", "    return pd.Series(\n", "        {\n", "            \"retrieved_chunks\": serialize_retrieved_chunks(\n", "                chunks, add_parent_doc_text=True\n", "            )\n", "        }\n", "    )\n", "\n", "\n", "def stage7():\n", "    spark = k8s_session(\n", "        name=\"vzhao-starethanol\",\n", "        # max_workers=96,\n", "        max_workers=180,\n", "        # max_workers=8,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.blacklist.node.enabled\": True,\n", "            \"spark.blacklist.node.maxFailedTasksPerNode\": 1,\n", "        },\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                add_scope_path_to_linechunk_fn,\n", "            ]\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        batch_size=1000,\n", "        timeout=7200,\n", "        # batch_size=10,\n", "        # timing_run=True,\n", "    )\n", "    spark.stop()\n", "    return result\n", "\n", "\n", "result = stage7()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8: Tokenization"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["STAGE7_URI = \"s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/\"\n", "# STAGE7_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled/\"\n", "# STAGE7_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/\"\n", "# STAGE7_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\"\n", "STAGE8_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_starcoder_tokens/\"\n", "\n", "CONFIG.update(\n", "    {\n", "        \"dataset_config\": {\n", "            \"seq_length\": 1024,\n", "            \"num_validation_samples\": 8192,\n", "        },\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        \"doc_seq_length\": 1000,\n", "        \"retrieved_docs\": 127,\n", "        \"allow_doc_clipping\": <PERSON><PERSON><PERSON>,\n", "    }\n", ")\n", "\n", "query_prompt_formatter_config = {\n", "    \"name\": \"ethanol6_query\",\n", "    \"max_tokens\": CONFIG['dataset_config']['seq_length'] - 1,\n", "    \"add_path\": True,\n", "    \"add_suffix\": True,\n", "    \"prefix_ratio\": 0.9,\n", "}\n", "\n", "key_prompt_formatter_config = {\n", "    \"name\": \"ethanol6_document\",\n", "    \"max_tokens\": CONFIG['doc_seq_length'] - 1,\n", "    \"add_path\": True,\n", "    \"add_prefix\": <PERSON>als<PERSON>,\n", "    \"add_suffix\": False,\n", "}\n", "\n", "\n", "\n", "# # NOTE: Config for document side scope path.\n", "# query_prompt_formatter_config = {\n", "#     \"name\": \"star_ethanol6_query\",\n", "#     \"max_tokens\": CONFIG['dataset_config']['seq_length'] - 1,\n", "#     \"add_path\": True,\n", "#     \"add_scope_path\": True,\n", "#     \"add_suffix\": True,\n", "#     \"prefix_ratio\": 0.9,\n", "# }\n", "\n", "# key_prompt_formatter_config = {\n", "#     \"name\": \"star_ethanol6_document\",\n", "#     \"max_tokens\": CONFIG['doc_seq_length'] - 1,\n", "#     \"add_path\": True,\n", "#     \"add_scope_path\": False,\n", "#     \"add_prefix\": <PERSON>als<PERSON>,\n", "#     \"add_suffix\": False,\n", "# }\n", "\n", "print(STAGE8_URI)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["!s3cmd rm --recursive {STAGE8_URI.replace(\"s3a://\", \"s3://\")}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import importlib\n", "import json\n", "from typing import Any, Iterator\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from experimental.vzhao.data import common, pandas_functions\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.core.model_input import ModelInput\n", "from research.core.types import EMPTY_CHUNK, Chunk, Document\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.retrieval import chunking_functions\n", "from research.retrieval.chunk_formatters import get_chunk_formatter\n", "from research.retrieval.utils import parse_yaml_config\n", "\n", "\n", "def create_prompt_formatter(formatter_config):\n", "    cls_name, kwargs = parse_yaml_config(formatter_config)\n", "    return get_prompt_formatter(cls_name, **kwargs)\n", "\n", "\n", "def create_chunk_formatter(formatter_config):\n", "    cls_name, kwargs = parse_yaml_config(formatter_config)\n", "    return get_chunk_formatter(cls_name, **kwargs)\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> list[Chunk]:\n", "    \"\"\"Deserializes retrieved chunks.\"\"\"\n", "\n", "    def to_chunk(dict_: dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "            meta={\n", "                # \"line_annotations\": {\n", "                #     int(key): chunking_functions.LineAnnotation.from_dict(value)\n", "                #     for key, value in dict_[\"meta\"][\"line_annotations\"].items()\n", "                # }\n", "            },\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "def stage8():\n", "    # NOTE: Need to add one padding for data loader.\n", "    def pack_prompt(prompt) -> bytearray:\n", "        return bytearray(\n", "            np.pad(\n", "                prompt, (0, 1 + CONFIG[\"dataset_config\"][\"seq_length\"] - len(prompt))\n", "            )\n", "            .astype(np.uint16)\n", "            .newbyteorder(\"<\")\n", "            .tobytes()\n", "        )\n", "\n", "    def create_pack_prompts(\n", "        query_prompt_formatter_config,\n", "        key_prompt_formatter_config,\n", "    ) -> pd.Series:\n", "        \"\"\"A function to tokenize and prepare prompts for dual encoder training.\"\"\"\n", "\n", "        @map_parquet.passthrough_feature()\n", "        @map_parquet.allow_unused_args()\n", "        def pack_prompts(\n", "            prefix,\n", "            suffix,\n", "            file_path,\n", "            retrieved_chunks,\n", "            retrieval_rank,\n", "            ppl,\n", "        ) -> Iterator[pd.Series]:\n", "            retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)\n", "            # if len(retrieved_chunks) < config.retrieved_docs:\n", "            #    raise ValueError(\n", "            #        f\"Too few retrieved chunks: {len(retrieve d_chunks)}, expected {config.retrieved_docs}\"\n", "            #    )\n", "            #    return None\n", "            # assert len(retrieved_chunks) >= config.retrieved_docs\n", "            retrieved_chunks = retrieved_chunks[\n", "                : CONFIG[\"retrieved_docs\"] + 1\n", "            ]  # +1 for the empty chunk\n", "            ppl_scores = json.loads(ppl)\n", "            retrieval_rank = json.loads(retrieval_rank)\n", "\n", "            # Pulls in registrations\n", "            \n", "            e = importlib.import_module(\"research.core.prompt_formatters\")\n", "            c = importlib.import_module(\"research.retrieval.chunk_formatters\")\n", "            # my_ethanol = importlib.import_module(\n", "            #     \"experimental.vzhao.20231129_star_ethanol.modeling.ethanol\"\n", "            # )\n", "            # my_chunking_functions = importlib.import_module(\n", "            #     \"experimental.vzhao.20231129_star_ethanol.modeling.chunking_functions\"\n", "            # )\n", "\n", "            all_prompts = []\n", "            all_texts = []\n", "\n", "            # Gets query prompt.\n", "            query_prompt_formatter = create_prompt_formatter(\n", "                query_prompt_formatter_config\n", "            )\n", "            # TODO: use deepseek tokenizer\n", "\n", "            end_of_query_token = query_prompt_formatter.tokenizer.vocab[\n", "                \"<|ret-endofquery|>\"\n", "            ]\n", "            query_prompt, _ = query_prompt_formatter.prepare_prompt(\n", "                ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "            )\n", "            query_prompt.append(end_of_query_token)\n", "            assert sum([1 for t in query_prompt if t == end_of_query_token]) == 1\n", "\n", "            if len(query_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "                raise ValueError(\n", "                    f\"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "                )\n", "            all_prompts.append(pack_prompt(query_prompt))\n", "            all_texts.append(\"\")\n", "\n", "            # Gets document prompt.\n", "            key_prompt_formatter = create_chunk_formatter(key_prompt_formatter_config)\n", "            end_of_key_token = key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "            pad_token = key_prompt_formatter.tokenizer.pad_id\n", "\n", "            assert len(retrieved_chunks) == len(retrieval_rank)\n", "\n", "            for chunk_idx, chunk in enumerate(retrieved_chunks):\n", "                # Drop Empty Chunk.\n", "                if retrieval_rank[chunk_idx] < 0:\n", "                    continue\n", "                assert retrieval_rank[chunk_idx] + 1 == chunk_idx\n", "\n", "                # Format the prompt\n", "                # chunk_prompt = key_prompt_formatter.prepare_prompt(\n", "                #     ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "                # )\n", "                formatted = key_prompt_formatter.format(chunk)\n", "                chunk_prompt = formatted.tokens\n", "                if len(chunk_prompt) > CONFIG[\"doc_seq_length\"]:\n", "                    if CONFIG[\"allow_doc_clipping\"]:\n", "                        chunk_prompt = chunk_prompt[: CONFIG[\"doc_seq_length\"]]\n", "                    else:\n", "                        raise ValueError(\n", "                            f\"Prompt too long: {len(chunk_prompt)} > {CONFIG['doc_seq_length']}\"\n", "                        )\n", "\n", "                # Encode the perplexity score into tokens.\n", "                ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(\n", "                    f\"{ppl_scores[chunk_idx]}\"\n", "                )\n", "\n", "                # Format the footer of the prompt\n", "                suffix = [end_of_key_token] + ppl_info_tokens + [pad_token]\n", "                chunk_prompt.extend(suffix)\n", "                assert sum([1 for t in chunk_prompt if t == end_of_key_token]) == 1\n", "\n", "                # Check that the prompt is not too long. `chunk_prompt` contains both\n", "                # key prompt and score.\n", "                if len(chunk_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "                    print(\"===================================================\")\n", "                    print(key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "                    print(\"===================================================\")\n", "                    raise ValueError(\n", "                        f\"{id} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "                    )\n", "\n", "                all_prompts.append(pack_prompt(chunk_prompt))\n", "                all_texts.append(formatted.text)\n", "\n", "            # +1 because the first prompt is the query prompt.\n", "            if len(all_prompts) == CONFIG[\"retrieved_docs\"] + 1:\n", "                yield pd.Series(\n", "                    {\n", "                        \"prompt_tokens\": all_prompts,\n", "                        # Useful for debug.\n", "                        # \"texts\": all_texts,\n", "                    }\n", "                )\n", "\n", "        return pack_prompts\n", "\n", "    spark = k8s_session(\n", "        name=\"vzhao-starethanol\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        # max_workers=5,\n", "        max_workers=360,\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                pandas_functions.keep_homogeneous_token_ppl,\n", "                pandas_functions.mean_ppl,\n", "                # pandas_functions.compute_ppl_gain,\n", "                # pandas_functions.create_patch_filter(\n", "                #     min_pos_ppg=0.12,\n", "                #     min_pos_ppl=-1.0,\n", "                #     num_positives=1,\n", "                # ),\n", "                # This will replace ppl.\n", "                # pandas_functions.create_linear_weight_ppl(64),\n", "                # Always run the following.\n", "                create_pack_prompts(\n", "                    query_prompt_formatter_config=query_prompt_formatter_config,\n", "                    key_prompt_formatter_config=key_prompt_formatter_config,\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=STAGE7_URI,\n", "        output_path=STAGE8_URI,\n", "        timeout=7 * 24 * 3600,\n", "        # batch_size=1000,\n", "        # For debug.\n", "        batch_size=10,\n", "        timing_run=True,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "\n", "    for e in result[\"task_info\"][\"stdout\"]:\n", "        print(e)\n", "\n", "\n", "stage8()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8b: Reshuffle and maybe repeat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE8_URI = \"s3a://augment-temporary/vzhao/ethanol6-16.1/08_tokens_starethanol/\"\n", "STAGE8b_URI = \"s3a://augment-temporary/vzhao/ethanol6-16.1/08_tokens_starethanol_rpt2_shuffled/\"\n", "print(STAGE8b_URI)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd rm --recursive {STAGE8b_URI.replace(\"s3a://\", \"s3://\")}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vzhao.data import common\n", "\n", "common.reshuffle(\n", "    input_parquet=STAGE8_URI,\n", "    output_parquet=STAGE8b_URI,\n", "    num_partitions=5000,\n", "    columns=None,\n", "    max_workers=128,\n", "    override=True,\n", "    repeat=2,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 9: Explode"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE8_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/\"\n", "STAGE9_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_mean_scopepath_explode/\"\n", "print(STAGE9_URI)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd rm --recursive {STAGE9_URI.replace('s3a://', 's3://')}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.spark import k8s_session\n", "import pandas as pd\n", "from experimental.vzhao.data import spark_stages\n", "\n", "def stage9():\n", "    def explode_prompts(batch: pd.DataFrame) -> pd.DataFrame:\n", "        \"\"\"Explode prompts to tokens.\n", "\n", "        Args:\n", "            batch: pd.DataFrame\n", "        \"\"\"\n", "        # Note: str.len() works for lists too\n", "        filtered_batch = batch[\n", "            batch[\"prompt_tokens\"].str.len() == CONFIG[\"retrieved_docs\"] + 1\n", "        ]\n", "        # Drop all other columns.\n", "        filtered_batch = filtered_batch[[\"prompt_tokens\"]]\n", "        results = filtered_batch.explode(\"prompt_tokens\")\n", "        return results if len(results) > 0 else pd.DataFrame()\n", "\n", "    spark = k8s_session(\n", "        name=\"vzhao-explode2\",\n", "        max_workers=480,\n", "    )\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        explode_prompts,\n", "        input_path=STAGE8_URI,\n", "        output_path=STAGE9_URI,\n", "        output_column=\"prompt_tokens\",\n", "        drop_original_columns=True,\n", "        timeout=7200,\n", "    )\n", "\n", "\n", "stage9()\n", "\n", "# spark_stages.stage_explode(\n", "#     STAGE8_URI,\n", "#     STAGE9_URI,\n", "#     # num_chunks_per_query=CONFIG[\"retrieved_docs\"],\n", "#     num_chunks_per_query=127,\n", "#     max_workers=380,\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 10: Export"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE9_URI = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_mean_scopepath_explode/\"\n", "DATA_OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6_17.1_mean_scopepath\"\n", "print(DATA_OUTPUT_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!rm -R {DATA_OUTPUT_PATH}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages.common import (\n", "    export_indexed_dataset,\n", ")\n", "from research.data.spark import k8s_session\n", "from megatron.tokenizer import get_tokenizer\n", "\n", "class ObjectDict(dict):\n", "    \"\"\"Provides both namespace-like and dict-like access to fields.\n", "\n", "    Allows access to fields using both obj.name notation and obj[\"name\"]\n", "    notation. The latter is useful when \"name\" contains periods, for example.\n", "    \"\"\"\n", "\n", "    def __getattr__(self, name: str):\n", "        if name in self:\n", "            return self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "    def __setattr__(self, name: str, value):\n", "        self[name] = value\n", "\n", "    def __delattr__(self, name: str):\n", "        if name in self:\n", "            del self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "def stage10():\n", "    spark = k8s_session(\n", "        name=\"vzhao-dev-export_indexed_dataset\",\n", "        max_workers=64,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "        },\n", "    )\n", "\n", "    export_indexed_dataset(\n", "        config=ObjectDict(\n", "            {\n", "                \"input\": STAGE9_URI,\n", "                \"output\": DATA_OUTPUT_PATH,\n", "                \"samples_column\": \"prompt_tokens\",\n", "                'num_validation_samples': 8192,\n", "            }\n", "        ),\n", "        spark=spark,\n", "        tokenizer=get_tokenizer(CONFIG[\"tokenizer_name\"]),\n", "    )\n", "    spark.stop()\n", "\n", "    return spark\n", "\n", "\n", "spark = stage10()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from research.data.spark import get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "spark = get_session()\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/07_filter_mean/\"\n", "path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/08_mean_doc_prefix_tokens/\"\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "print(df.count())\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokens = spark_common.unpack_tokens(df['prompt_tokens'][0][1])\n", "print(len(tokens))\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
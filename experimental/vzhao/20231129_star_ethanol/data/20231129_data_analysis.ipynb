{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Load data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from research.data.spark import get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "from experimental.vzhao.data import common as vz_common\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "spark = get_session()\n", "\n", "# path = \"s3a://igor-dev-bucket/ethanol6/ethanol6-16.1/06_shuffled/\"\n", "# path = \"s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/08_mean_doc_prefix_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/08_mean_doc_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_filtered_tokens/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_1stsp_tokens/\"\n", "\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear64_1stsp_codegen_tokens/\"\n", "path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\"\n", "\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "print(df.count())\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "json.loads(df['retrieved_chunks'][0])[7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages import common as spark_common\n", "\n", "print(tokenizer.detokenize(spark_common.unpack_tokens(df['prompt_tokens'][0][5])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_filtered_tokens/'\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_1stsp_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_1stsp_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_1stsp_tokens/\"\n", "path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear64_1stsp_codegen_tokens/\"\n", "\n", "spark = k8s_session(max_workers=8)\n", "# spark = get_session()\n", "print(path, flush=True)\n", "df = spark.read.parquet(path)\n", "print(f'Examples: {df.count()}')\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Indexed Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def rstrip(tokens, id):\n", "    while tokens[-1] == id:\n", "        tokens = tokens[:-1]\n", "    return tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import IndexedDataset, MMapIndexedDataset\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "\n", "# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
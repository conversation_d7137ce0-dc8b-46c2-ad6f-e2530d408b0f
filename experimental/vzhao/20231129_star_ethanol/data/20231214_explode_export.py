"""Run last part of the data pipeline.

Usage:

python experimental/vzhao/20231129_star_ethanol/data/20231214_explode_export.py
"""
import pathlib
import re

import pandas as pd

from experimental.vzhao.data import spark_stages
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet

# noqa
# flake8: noqa
# pylint: skip-file

CONFIG = {
    "retrieved_docs": 127,
    "tokenizer_name": "StarCoderTokenizer",
}
STAGE8_URI = (
    "s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_1stsp_codegen_tokens/"
)
basename = pathlib.Path(STAGE8_URI).name
match = re.fullmatch(r"^08_(.*)_tokens$", basename)
if match is None:
    raise ValueError("Bad stage8 uri")

STAGE9_URI = (
    f"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_{match.group(1)}_explode/"
)
DATA_OUTPUT_PATH = (
    f"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_{match.group(1)}"
)


def stage9():
    def explode_prompts(batch: pd.DataFrame) -> pd.DataFrame:
        """Explode prompts to tokens.

        Args:
            batch: pd.DataFrame
        """
        # Note: str.len() works for lists too
        filtered_batch = batch[
            batch["prompt_tokens"].str.len() == CONFIG["retrieved_docs"] + 1
        ]
        # Drop all other columns.
        filtered_batch = filtered_batch[["prompt_tokens"]]
        results = filtered_batch.explode("prompt_tokens")
        return results if len(results) > 0 else pd.DataFrame()

    spark = k8s_session(
        name="vzhao-explode2",
        max_workers=480,
    )
    map_parquet.apply_pandas(
        spark,
        explode_prompts,
        input_path=STAGE8_URI,
        output_path=STAGE9_URI,
        output_column="prompt_tokens",
        drop_original_columns=True,
        timeout=7200,
    )


stage9()

from megatron.tokenizer import get_tokenizer

from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset


class ObjectDict(dict):
    """Provides both namespace-like and dict-like access to fields.

    Allows access to fields using both obj.name notation and obj["name"]
    notation. The latter is useful when "name" contains periods, for example.
    """

    def __getattr__(self, name: str):
        if name in self:
            return self[name]
        else:
            raise AttributeError("No such attribute: " + name)

    def __setattr__(self, name: str, value):
        self[name] = value

    def __delattr__(self, name: str):
        if name in self:
            del self[name]
        else:
            raise AttributeError("No such attribute: " + name)


def stage10():
    spark = k8s_session(
        name="vzhao-dev-export_indexed_dataset",
        max_workers=64,
        conf={
            "spark.executor.pyspark.memory": "1050g",
        },
    )

    export_indexed_dataset(
        config=ObjectDict(
            {
                "input": STAGE9_URI,
                "output": DATA_OUTPUT_PATH,
                "samples_column": "prompt_tokens",
                "num_validation_samples": 8192,
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(CONFIG["tokenizer_name"]),
    )
    spark.stop()

    return spark


spark = stage10()

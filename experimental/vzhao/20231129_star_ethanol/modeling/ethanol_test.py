"""Unit tests for Ethanol formatters."""

import importlib

import pytest

ethanol = importlib.import_module(
    "experimental.vzhao.20231129_star_ethanol.modeling.ethanol"
)


@pytest.mark.parametrize(
    "list_of_tokens, max_tokens, from_left, expected",
    [
        ([[1, 2, 3, 4, 5]], 10, True, [[1, 2, 3, 4, 5]]),
        ([[1, 2, 3, 4, 5]], 1, True, []),
    ],
)
def test_trim_lines(list_of_tokens, max_tokens, from_left, expected):
    rst = ethanol._trim_lines(list_of_tokens, max_tokens, from_left=from_left)
    assert rst == expected

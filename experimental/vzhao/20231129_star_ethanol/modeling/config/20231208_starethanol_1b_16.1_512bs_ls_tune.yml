# includes is an ordered list of gpt-neox config files to be loaded
# Usage:
# python research/gpt-neox/jobs/experiment.py --metaconfig experimental/vzhao/20231129_star_ethanol/modeling/20231208_starethanol_1b_16.1_512bs_ls_tune.yml
#
# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/28556/checkpoints?sortKey=SORT_BY_BATCH_NUMBER&tableLimit=100
# Download:
# bash research/utils/download_checkpoint.sh 4aa70537-222e-4a47-b600-f8ebac8898fb star_ethanol/1b_16.1_H100_1epoch_bs512_ls4096 [gcp]

includes:
  - augment_configs/starcoder/model/starcoder.yml
  - augment_configs/starcoder/model/starcoder-1b.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/ethanol.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/loss_scale_p14_w8.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: &exp_name starethanol_1b_16.1_H100_1epoch_bs512_ls16384
  description: null
  workspace: Dev
  project: vzhao
  labels: ["ethanol_plus", "test"]
  perform_initial_validation: False # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xH100.yaml"
  gpu_count: 32 # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc: True # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  wandb_project: vzhao-exp
  wandb_name: *exp_name
  wandb_group: ethanol_plus
  # mode
  ppl_distill: true

  # training batch & schedule
  seq_length: 1024
  # Currently, the loss assumes one query per micro batch.
  train_micro_batch_size_per_gpu: 128
  # This is effectively batch size per gpu.
  gradient_accumulation_steps: 16
  # train_batch_size / train_micro_batch_size_per_gpu is effectively batch size.
  train_batch_size: 65536
  train_iters: 2000
  lr_decay_iters: 2000
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 999999999999 # Never
  eval_iters: 1
  save_interval: 125

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  data-path: null
  train_data_paths:
    - /mnt/efs/augment/user/vincent/data/starethanol6/ethanol6_16.1/dataset
  valid_data_paths:
    - /mnt/efs/augment/user/vincent/data/starethanol6/ethanol6_16.1/validation_dataset
  test_data_paths:
    - /mnt/efs/augment/user/vincent/data/starethanol6/ethanol6_16.1/validation_dataset
  max_valid_data_size: 8192

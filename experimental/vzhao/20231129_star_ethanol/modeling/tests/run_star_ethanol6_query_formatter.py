"""Unit tests for Ethanol formatters."""
import importlib

from research.core.model_input import ModelInput


def main():
    # %%

    PREFIX = """
    import itertools

    class AClass:
        def function(foo, bar):
            print(foo)
            print(bar)
            print('another)
            return None

    """

    SUFFIX = """
        def another_function(foo, bar):
            print('another)
            return None

    """

    # %%

    ethanol = importlib.import_module(
        "experimental.vzhao.20231129_star_ethanol.modeling.ethanol"
    )

    # %%
    # l = 26
    for value in range(10, 200):
        formatter = ethanol.StarEthanol6QueryFormatter(
            max_tokens=value,
            add_path=True,
            add_scope_path="first_line",
            add_suffix=True,
            prefix_ratio=0.9,
            tokenizer_name="StarCoderTokenizer",
        )
        file_path = "path/to/file.py"

        query_prompt, _ = formatter.prepare_prompt(
            ModelInput(prefix=PREFIX, suffix=SUFFIX, path=file_path)
        )

        # %%
        print(len(query_prompt), value)
        print(formatter.tokenizer.detokenize(query_prompt))
        input()


if __name__ == "__main__":
    main()

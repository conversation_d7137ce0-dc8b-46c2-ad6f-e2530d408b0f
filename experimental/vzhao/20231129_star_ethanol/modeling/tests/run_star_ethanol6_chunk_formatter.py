"""Unit tests for Ethanol formatters."""

# %%
import importlib

from research.core.types import Document
from research.retrieval import chunking_functions
from research.retrieval.tests.data import patchcore_test_data

ethanol = importlib.import_module(
    "experimental.vzhao.20231129_star_ethanol.modeling.ethanol"
)

document = Document(
    text=patchcore_test_data.COMMON_PY,
    id="common.py_uuid",
    path="common.py",
    meta={},
)

chunker = chunking_functions.LineLevelChunker(
    max_lines_per_chunk=20, include_scope_annotation=True
)
chunks = chunker.split_into_chunks(document)

# %%
formatter = ethanol.StarEthanol6ChunkFormatter(
    max_tokens=100,
    add_path=True,
    add_scope_path="first_line",
    tokenizer_name="StarCoderTokenizer",
)
formatted = formatter.format(chunks[0])
print(formatted.text)

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "PREFIX = \"\"\"\n", "import itertools\n", "\n", "class AClass:\n", "    def function(foo, bar):\n", "        print(foo)\n", "        print(bar)\n", "        print('another)\n", "        return None\n", "\n", "\"\"\"\n", "\n", "SUFFIX = \"\"\"\n", "    def another_function(foo, bar):\n", "        print('another)\n", "        return None\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-01-04 20:12:09.873384: W tensorflow/stream_executor/platform/default/dso_loader.cc:64] Could not load dynamic library 'libcudart.so.11.0'; dlerror: libcudart.so.11.0: cannot open shared object file: No such file or directory\n", "2024-01-04 20:12:09.873450: I tensorflow/stream_executor/cuda/cudart_stub.cc:29] Ignore above cudart dlerror if you do not have a GPU set up on your machine.\n"]}], "source": ["import importlib\n", "from research.core.model_input import ModelInput\n", "ethanol = importlib.import_module(\n", "    \"experimental.vzhao.20231129_star_ethanol.modeling.ethanol\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb Cell 3\u001b[0m line \u001b[0;36m1\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m formatter \u001b[39m=\u001b[39m ethanol\u001b[39m.\u001b[39mStarEthanol6QueryFormatter(\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     max_tokens\u001b[39m=\u001b[39m\u001b[39m10\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m     add_path\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m     tokenizer_name\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mStarCoderTokenizer\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m )\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m file_path \u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mpath/to/file.py\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m query_prompt, _ \u001b[39m=\u001b[39m formatter\u001b[39m.\u001b[39mprepare_prompt(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m     ModelInput(prefix\u001b[39m=\u001b[39mPREFIX, suffix\u001b[39m=\u001b[39mSUFFIX, path\u001b[39m=\u001b[39mfile_path)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m )\n", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb Cell 3\u001b[0m line \u001b[0;36m1\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m formatter \u001b[39m=\u001b[39m ethanol\u001b[39m.\u001b[39mStarEthanol6QueryFormatter(\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     max_tokens\u001b[39m=\u001b[39m\u001b[39m10\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m     add_path\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m     tokenizer_name\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mStarCoderTokenizer\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m )\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m file_path \u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mpath/to/file.py\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m query_prompt, _ \u001b[39m=\u001b[39m formatter\u001b[39m.\u001b[39mprepare_prompt(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m     ModelInput(prefix\u001b[39m=\u001b[39mPREFIX, suffix\u001b[39m=\u001b[39mSUFFIX, path\u001b[39m=\u001b[39mfile_path)\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d6465762d64617461227d@ssh-remote%2Bvzhao-dev-data/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/modeling/tests/test_formatter.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m )\n", "File \u001b[0;32m_pydevd_bundle/pydevd_cython.pyx:1457\u001b[0m, in \u001b[0;36m_pydevd_bundle.pydevd_cython.SafeCallWrapper.__call__\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m_pydevd_bundle/pydevd_cython.pyx:701\u001b[0m, in \u001b[0;36m_pydevd_bundle.pydevd_cython.PyDBFrame.trace_dispatch\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m_pydevd_bundle/pydevd_cython.pyx:1395\u001b[0m, in \u001b[0;36m_pydevd_bundle.pydevd_cython.PyDBFrame.trace_dispatch\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m_pydevd_bundle/pydevd_cython.pyx:1344\u001b[0m, in \u001b[0;36m_pydevd_bundle.pydevd_cython.PyDBFrame.trace_dispatch\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m_pydevd_bundle/pydevd_cython.pyx:312\u001b[0m, in \u001b[0;36m_pydevd_bundle.pydevd_cython.PyDBFrame.do_wait_suspend\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/debugpy/_vendored/pydevd/pydevd.py:2070\u001b[0m, in \u001b[0;36mPyDB.do_wait_suspend\u001b[0;34m(self, thread, frame, event, arg, exception_type)\u001b[0m\n\u001b[1;32m   2067\u001b[0m             from_this_thread\u001b[39m.\u001b[39mappend(frame_custom_thread_id)\n\u001b[1;32m   2069\u001b[0m     \u001b[39mwith\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_threads_suspended_single_notification\u001b[39m.\u001b[39mnotify_thread_suspended(thread_id, thread, stop_reason):\n\u001b[0;32m-> 2070\u001b[0m         keep_suspended \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_do_wait_suspend(thread, frame, event, arg, suspend_type, from_this_thread, frames_tracker)\n\u001b[1;32m   2072\u001b[0m frames_list \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m   2074\u001b[0m \u001b[39mif\u001b[39;00m keep_suspended:\n\u001b[1;32m   2075\u001b[0m     \u001b[39m# This means that we should pause again after a set next statement.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/debugpy/_vendored/pydevd/pydevd.py:2106\u001b[0m, in \u001b[0;36mPyDB._do_wait_suspend\u001b[0;34m(self, thread, frame, event, arg, suspend_type, from_this_thread, frames_tracker)\u001b[0m\n\u001b[1;32m   2103\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_call_input_hook()\n\u001b[1;32m   2105\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mprocess_internal_commands()\n\u001b[0;32m-> 2106\u001b[0m     time\u001b[39m.\u001b[39;49msleep(\u001b[39m0.01\u001b[39;49m)\n\u001b[1;32m   2108\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcancel_async_evaluation(get_current_thread_id(thread), \u001b[39mstr\u001b[39m(\u001b[39mid\u001b[39m(frame)))\n\u001b[1;32m   2110\u001b[0m \u001b[39m# process any stepping instructions\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["formatter = ethanol.StarEthanol6QueryFormatter(\n", "    max_tokens=10,\n", "    add_path=True,\n", "    add_scope_path=\"first_line\",\n", "    add_suffix=True,\n", "    prefix_ratio=0.9,\n", "    tokenizer_name=\"StarCoderTokenizer\",\n", ")\n", "file_path = \"path/to/file.py\"\n", "\n", "query_prompt, _ = formatter.prepare_prompt(\n", "    ModelInput(prefix=PREFIX, suffix=SUFFIX, path=file_path)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(formatter.tokenizer.detokenize(query_prompt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.retrieval.tests.data import patchcore_test_data\n", "from research.core.types import Chunk, Document\n", "from research.retrieval import chunking_functions\n", "\n", "document = Document(\n", "    text=patchcore_test_data.COMMON_PY,\n", "    id=\"common.py_uuid\",\n", "    path=\"common.py\",\n", "    meta={},\n", ")\n", "\n", "chunker = chunking_functions.LineLevelChunker(\n", "    max_lines_per_chunk=20, include_scope_annotation=True\n", ")\n", "chunks = chunker.split_into_chunks(document)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-01-05 00:18:27.448855: W tensorflow/stream_executor/platform/default/dso_loader.cc:64] Could not load dynamic library 'libcudart.so.11.0'; dlerror: libcudart.so.11.0: cannot open shared object file: No such file or directory\n", "2024-01-05 00:18:27.448921: I tensorflow/stream_executor/cuda/cudart_stub.cc:29] Ignore above cudart dlerror if you do not have a GPU set up on your machine.\n"]}], "source": ["import importlib\n", "from research.core.model_input import ModelInput\n", "ethanol = importlib.import_module(\n", "    \"experimental.vzhao.20231129_star_ethanol.modeling.ethanol\"\n", ")\n", "\n", "formatter = ethanol.StarEthanol6ChunkFormatter(\n", "    max_tokens=100,\n", "    add_path=True,\n", "    add_scope_path=\"first_line\",\n", "    tokenizer_name=\"StarCoderTokenizer\",\n", ")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["common.py:FaissNN.run<fim_middle>\n", "        # Build a search index just for this search.\n", "        search_index = self._create_index(index_features.shape[-1])\n", "        self._train(search_index, index_features)\n", "        search_index.add(index_features)\n", "        return search_index.search(query_features, n_nearest_neighbours)\n", "\n", "    def save(self, filename: str) -> None:\n", "        faiss.\n"]}], "source": ["print(formatter.format(chunks[4]).text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
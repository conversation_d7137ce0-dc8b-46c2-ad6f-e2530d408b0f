# Usage:
# python research/eval/eval.py experimental/vzhao/20240402_dense_signature/eval/configs/reproduce_ender_repo23.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  # name: repoeval_2-3lines, reproduce_ender, ender/16b_multie..., NoSig
  name: debug det code
  project: vzhao-eval
  workspace: Dev
podspec: 1xA100.yaml
system:
  name: ender_sys
  fim_mode: evaluation
  model:
    model_path: ender/16b_multieth64m_addrust_noinline_sigretprefsuf
    name: ender_fastforward
    prompt:
      component_order:
        - signature
        - retrieval
        - prefix
        - suffix
      max_prefix_tokens: 1280
      max_prompt_tokens: 5120
      max_retrieved_chunk_tokens: -1
      max_signature_tokens: 1024
      max_suffix_tokens: 768
  generation_options:
    max_generated_tokens: 280
  dense_retriever:
    scorer:
      name: generic_neox
      checkpoint_path: star_ethanol/starethanol6_16.1_mean_proj_512_2000
    chunker:
      max_lines_per_chunk: 40
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
      tokenizer_name: StarCoderTokenizer
    query_formatter:
      add_path: true
      add_suffix: true
      max_lines: -1
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
      tokenizer_name: StarCoderTokenizer
  sig_prompt_formatter:
    max_middle_tks: 1024
  signature_index:
    est_prefix_chars: 3840
    est_suffix_chars: 2304
    # max_ctx_signature_chars: 3000
    max_ctx_signature_chars: 0
  verbose: false
task:
  dataset: repoeval_2-3lines
  name: hydra

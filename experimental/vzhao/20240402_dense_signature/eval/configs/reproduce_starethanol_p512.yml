# Usage:
# python research/eval/eval.py --v2 experimental/vzhao/20240402_dense_signature/eval/configs/reproduce_starethanol_p512.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b_alphal_fixtoken, starethanol6_16.1_mean_proj_512_2000, 31136,
    repoeval_2-3lines
  project: vzhao-eval
  workspace: Dev
# import_modules:
# - experimental.vzhao.20231129_star_ethanol.modeling.ethanol
# - experimental.vzhao.20231129_star_ethanol.modeling.chunking_functions
# - research.retrieval.libraries.chunk_formatters
# - research.core.prompt_formatters
podspec: 1xA100.yaml
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 32
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_number_chunks: 32
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 40
      name: line_level
    document_formatter:
      add_path: true
      add_prefix: false
      add_suffix: false
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
    scorer:
      name: generic_neox
      checkpoint_path: star_ethanol/starethanol6_16.1_mean_proj_512_2000
task:
  dataset: repoeval_2-3lines
  name: hydra

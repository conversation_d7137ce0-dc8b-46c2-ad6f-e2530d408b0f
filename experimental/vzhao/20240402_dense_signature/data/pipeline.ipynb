{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["CONFIG = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 6: Adds Random Negatives"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["STAGE5_URI = \"/mnt/efs/spark-data/user/arun/ender_ppl/eggnog/05_perplexity_score.2/\"\n", "STAGE6_URI = (\n", "    \"/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/06_perplexity_score_randneg/\"\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rm: cannot remove '/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/06_perplexity_score_randneg/': No such file or directory\n"]}], "source": ["!rm -R {STAGE6_URI}"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/04/05 01:45:01 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "WARNING:root:Cleaning up shared folder /mnt/efs/spark-data/python_env/2024-04-05/vzhao-dev/14106b86-ec05-403a-82de-7606387766f9\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# from experimental.vzhao.data import pandas_functions\n", "import importlib\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240402_dense_signature.data.pandas_functions\"\n", ")\n", "\n", "\n", "def stage6():\n", "    spark = k8s_session(\n", "        name=\"vzhao-dense-signature\",\n", "        # max_workers=180,\n", "        max_workers=8,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.blacklist.node.enabled\": True,\n", "            \"spark.blacklist.node.maxFailedTasksPerNode\": 1,\n", "        },\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        pandas_functions.random_signature_chunks(),\n", "        input_path=STAGE5_URI,\n", "        output_path=STAGE6_URI,\n", "        # batch_size=1000,\n", "        # Use a very large batch here to sample random negatives from the whole parquet\n", "        # file. Each file contains about 500 rows.\n", "        batch_size=4096,\n", "        timeout=7200,\n", "        # timing_run=True,\n", "    )\n", "    spark.stop()\n", "    return result\n", "\n", "\n", "result = stage6()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7 Prompt Formatting"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/07_sig_only_tokens/\n"]}], "source": ["CONFIG.update(\n", "    {\n", "        \"dataset_config\": {\n", "            \"seq_length\": 1024,\n", "            \"num_validation_samples\": 8192,\n", "        },\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        \"doc_seq_length\": 1000,\n", "        \"retrieved_docs\": 31,\n", "        \"allow_doc_clipping\": True,\n", "    }\n", ")\n", "\n", "from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig\n", "\n", "STAGE6_URI = (\n", "    \"/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/06_perplexity_score_randneg/\"\n", ")\n", "STAGE7_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/07_sig_only_tokens/\"\n", "\n", "query_prompt_formatter_config = {\n", "    \"name\": \"star_ethanol6_query\",\n", "    \"max_tokens\": CONFIG['dataset_config']['seq_length'] - 2,\n", "    \"add_path\": True,\n", "    \"add_scope_path\": True,\n", "    \"add_suffix\": True,\n", "    \"prefix_ratio\": 0.9,\n", "}\n", "\n", "key_prompt_formatter_config = {\n", "    \"name\": \"simple_document\",\n", "    \"add_path\": <PERSON>als<PERSON>,\n", "}\n", "\n", "print(STAGE7_URI)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE7_URI}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Iterator, Any\n", "\n", "from experimental.vzhao.data import common, pandas_functions\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.core.model_input import ModelInput\n", "from research.core.types import EMPTY_CHUNK\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.retrieval.libraries.chunk_formatters import get_chunk_formatter\n", "from research.retrieval.libraries.utils import parse_yaml_config\n", "import importlib\n", "from research.core.types import Chunk, Document\n", "from research.retrieval.libraries import chunking_functions\n", "\n", "\n", "def create_prompt_formatter(formatter_config):\n", "    cls_name, kwargs = parse_yaml_config(formatter_config)\n", "    return get_prompt_formatter(cls_name, **kwargs)\n", "\n", "\n", "def create_chunk_formatter(formatter_config):\n", "    cls_name, kwargs = parse_yaml_config(formatter_config)\n", "    return get_chunk_formatter(cls_name, **kwargs)\n", "\n", "\n", "def deserialize_signature_chunks(signature_chunks: str) -> list[Chunk]:\n", "    \"\"\"Deserializes signature chunks.\n", "\n", "    Also see `signature_to_chunk` in `research_siganture_index.py`.\n", "    \"\"\"\n", "\n", "    def to_chunk(dict_: dict) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document.new(\"?\", \"?\"),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "            meta={},\n", "        )\n", "\n", "    dicts = json.loads(signature_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "\n", "# NOTE: Need to add one padding for data loader.\n", "def pack_prompt(prompt) -> bytearray:\n", "    return bytearray(\n", "        np.pad(prompt, (0, 1 + CONFIG[\"dataset_config\"][\"seq_length\"] - len(prompt)))\n", "        .astype(np.uint16)\n", "        .newbyteorder(\"<\")\n", "        .tobytes()\n", "    )\n", "\n", "\n", "def create_pack_prompts(\n", "    query_prompt_formatter_config,\n", "    key_prompt_formatter_config,\n", ") -> pd.Series:\n", "    \"\"\"A function to tokenize and prepare prompts for dual encoder training.\"\"\"\n", "\n", "    @map_parquet.passthrough_feature()\n", "    @map_parquet.allow_unused_args()\n", "    def pack_prompts(\n", "        prefix,\n", "        suffix,\n", "        file_path,\n", "        signature_chunks,\n", "        ppl_scores_sig,\n", "    ) -> Iterator[pd.Series]:\n", "        signature_chunks = deserialize_signature_chunks(signature_chunks)\n", "        if len(signature_chunks) < CONFIG[\"retrieved_docs\"]:\n", "            raise ValueError(\n", "                f\"Too few retrieved chunks: {len(signature_chunks)}, expected {CONFIG['retrieved_docs']}\"\n", "            )\n", "            return None\n", "        assert len(signature_chunks) >= CONFIG[\"retrieved_docs\"]\n", "        signature_chunks = signature_chunks[: CONFIG[\"retrieved_docs\"]]\n", "\n", "        ppl_scores = json.loads(ppl_scores_sig)\n", "\n", "        # Pulls in registrations\n", "        # ethanol = importlib.import_module(\"experimental.igor.systems.ethanol\")\n", "        my_ethanol = importlib.import_module(\n", "            \"experimental.vzhao.20231129_star_ethanol.modeling.ethanol\"\n", "        )\n", "        my_chunking_functions = importlib.import_module(\n", "            \"experimental.vzhao.20231129_star_ethanol.modeling.chunking_functions\"\n", "        )\n", "        from megatron.tokenizer import get_tokenizer\n", "        tokenizer = get_tokenizer('StarCoderTokenizer')\n", "        max_seq_length = CONFIG['dataset_config']['seq_length']\n", "\n", "        all_prompts = []\n", "        all_texts = []\n", "\n", "        # NOTE: Generates query prompt.\n", "        query_prompt_formatter = create_prompt_formatter(query_prompt_formatter_config)\n", "        query_prompt_formatter.tokenizer = tokenizer\n", "        end_of_query_token = query_prompt_formatter.tokenizer.vocab[\n", "            \"<|ret-endofquery|>\"\n", "        ]\n", "        query_prompt, _ = query_prompt_formatter.prepare_prompt(\n", "            ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "        )\n", "        query_prompt.append(end_of_query_token)\n", "        assert sum([1 for t in query_prompt if t == end_of_query_token]) == 1\n", "        if len(query_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "            raise ValueError(\n", "                f\"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "            )\n", "        all_prompts.append(pack_prompt(query_prompt))\n", "        all_texts.append(\n", "            query_prompt_formatter.prepare_prompt_text(\n", "                ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "            )[0]\n", "        )\n", "\n", "        # NOTE: Generates document prompt.\n", "        key_prompt_formatter = create_chunk_formatter(key_prompt_formatter_config)\n", "        key_prompt_formatter.tokenizer = tokenizer\n", "        end_of_key_token = key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "        pad_token = key_prompt_formatter.tokenizer.pad_id\n", "        for chunk_idx, chunk in enumerate(signature_chunks):\n", "            # Format the prompt\n", "            # chunk_prompt = key_prompt_formatter.prepare_prompt(\n", "            #     ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "            # )\n", "            formatted = key_prompt_formatter.format(chunk)\n", "            chunk_prompt = formatted.tokens\n", "            if len(chunk_prompt) > CONFIG[\"doc_seq_length\"]:\n", "                if CONFIG[\"allow_doc_clipping\"]:\n", "                    chunk_prompt = chunk_prompt[: CONFIG[\"doc_seq_length\"]]\n", "                else:\n", "                    raise ValueError(\n", "                        f\"Prompt too long: {len(chunk_prompt)} > {CONFIG['doc_seq_length']}\"\n", "                    )\n", "\n", "            # Encode the perplexity score into tokens.\n", "            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(\n", "                f\"{ppl_scores[chunk_idx]}\"\n", "            )\n", "\n", "            # Format the footer of the prompt\n", "            suffix = [end_of_key_token] + ppl_info_tokens + [pad_token]\n", "            chunk_prompt.extend(suffix)\n", "            assert sum([1 for t in chunk_prompt if t == end_of_key_token]) == 1\n", "\n", "            # Check that the prompt is not too long. `chunk_prompt` contains both\n", "            # key prompt and score.\n", "            if len(chunk_prompt) > CONFIG[\"dataset_config\"][\"seq_length\"]:\n", "                print(\"===================================================\")\n", "                print(key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "                print(\"===================================================\")\n", "                raise ValueError(\n", "                    f\"{chunk_idx} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['dataset_config']['seq_length']}\"\n", "                )\n", "\n", "            all_prompts.append(pack_prompt(chunk_prompt))\n", "            all_texts.append(formatted.text)\n", "\n", "        # +1 because the first prompt is the query prompt.\n", "        if len(all_prompts) == CONFIG[\"retrieved_docs\"] + 1:\n", "            yield pd.Series(\n", "                {\n", "                    \"prompt_tokens\": all_prompts,\n", "                    \"prompt_texts\": all_texts,\n", "                }\n", "            )\n", "\n", "    return pack_prompts"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n", "ERROR:root:Exception while sending command.                  (210 + 320) / 2000]\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py\", line 511, in send_command\n", "    answer = smart_decode(self.stream.readline()[:-1])\n", "RuntimeError: reentrant call inside <_io.BufferedReader name=59>\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py\", line 1038, in send_command\n", "    response = connection.send_command(command)\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py\", line 539, in send_command\n", "    raise Py4JNetworkError(\n", "py4j.protocol.Py4JNetworkError: Error while sending or receiving\n", "ERROR:root:Exception while sending command.\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py\", line 511, in send_command\n", "    answer = smart_decode(self.stream.readline()[:-1])\n", "  File \"/opt/conda/lib/python3.9/socket.py\", line 704, in readinto\n", "    return self._sock.recv_into(b)\n", "  File \"/opt/conda/lib/python3.9/site-packages/pyspark/context.py\", line 377, in signal_handler\n", "    self.cancelAllJobs()\n", "  File \"/opt/conda/lib/python3.9/site-packages/pyspark/context.py\", line 2255, in cancelAllJobs\n", "    self._jsc.sc().cancelAllJobs()\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py\", line 1322, in __call__\n", "    return_value = get_return_value(\n", "  File \"/opt/conda/lib/python3.9/site-packages/pyspark/errors/exceptions/captured.py\", line 169, in deco\n", "    return f(*a, **kw)\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/protocol.py\", line 334, in get_return_value\n", "    raise Py4JError(\n", "py4j.protocol.Py4JError: An error occurred while calling o4323.sc\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py\", line 1038, in send_command\n", "    response = connection.send_command(command)\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py\", line 539, in send_command\n", "    raise Py4JNetworkError(\n", "py4j.protocol.Py4JNetworkError: Error while sending or receiving\n"]}, {"ename": "Py4JError", "evalue": "An error occurred while calling o8405.collectToPython", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPy4JError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb Cell 10\u001b[0m line \u001b[0;36m4\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=36'>37</a>\u001b[0m     \u001b[39mfor\u001b[39;00m e \u001b[39min\u001b[39;00m result[\u001b[39m\"\u001b[39m\u001b[39mtask_info\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mstdout\u001b[39m\u001b[39m\"\u001b[39m]:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=37'>38</a>\u001b[0m         \u001b[39mprint\u001b[39m(e)\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=40'>41</a>\u001b[0m stage8()\n", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb Cell 10\u001b[0m line \u001b[0;36m1\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mstage8\u001b[39m():\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     spark \u001b[39m=\u001b[39m k8s_session(\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m         name\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mvzhao-starethanol\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m         conf\u001b[39m=\u001b[39m{\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m         max_workers\u001b[39m=\u001b[39m\u001b[39m320\u001b[39m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m     )\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m     result \u001b[39m=\u001b[39m map_parquet\u001b[39m.\u001b[39;49mapply_pandas(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m         spark,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m         map_parquet\u001b[39m.\u001b[39;49mchain_processors(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m             [\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m                 \u001b[39m# Always run the following.\u001b[39;49;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m                 create_pack_prompts(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=17'>18</a>\u001b[0m                     query_prompt_formatter_config\u001b[39m=\u001b[39;49mquery_prompt_formatter_config,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m                     key_prompt_formatter_config\u001b[39m=\u001b[39;49mkey_prompt_formatter_config,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m                 ),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m             ]\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m         ),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m         input_path\u001b[39m=\u001b[39;49mSTAGE6_URI,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m         output_path\u001b[39m=\u001b[39;49mSTAGE7_URI,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m         timeout\u001b[39m=\u001b[39;49m\u001b[39m7\u001b[39;49m \u001b[39m*\u001b[39;49m \u001b[39m24\u001b[39;49m \u001b[39m*\u001b[39;49m \u001b[39m3600\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m         batch_size\u001b[39m=\u001b[39;49m\u001b[39m32\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m         \u001b[39m# For debug.\u001b[39;49;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m         \u001b[39m# batch_size=10,\u001b[39;49;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m         \u001b[39m# timing_run=True,\u001b[39;49;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m         ignore_error\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m     )\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=31'>32</a>\u001b[0m     spark\u001b[39m.\u001b[39mstop()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/20240402_dense_signature/data/pipeline.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=33'>34</a>\u001b[0m     \u001b[39mfor\u001b[39;00m e \u001b[39min\u001b[39;00m result[\u001b[39m\"\u001b[39m\u001b[39mtask_info\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mstderr\u001b[39m\u001b[39m\"\u001b[39m]:\n", "File \u001b[0;32m~/augment/research/data/spark/pipelines/utils/map_parquet.py:779\u001b[0m, in \u001b[0;36mapply_pandas\u001b[0;34m(spark_session, pandas_func, input_path, output_path, region, endpoint_url, timeout, poll_time, batch_size, input_columns, timing_run, profile, drop_original_columns, output_column, ignore_error, task_info_location)\u001b[0m\n\u001b[1;32m    777\u001b[0m     info_df \u001b[39m=\u001b[39m task_info\n\u001b[1;32m    778\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m--> 779\u001b[0m     info_df \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39;49mtoPandas()\n\u001b[1;32m    780\u001b[0m     storage_path \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m    781\u001b[0m status_count \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39mgroupBy(\u001b[39m\"\u001b[39m\u001b[39mstatus\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39magg(F\u001b[39m.\u001b[39mcount(\u001b[39m\"\u001b[39m\u001b[39m*\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39malias(\u001b[39m\"\u001b[39m\u001b[39mcount\u001b[39m\u001b[39m\"\u001b[39m))\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/pandas/conversion.py:208\u001b[0m, in \u001b[0;36mPandasConversionMixin.toPandas\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    205\u001b[0m             \u001b[39mraise\u001b[39;00m\n\u001b[1;32m    207\u001b[0m \u001b[39m# Below is to<PERSON><PERSON><PERSON> without Arrow optimization.\u001b[39;00m\n\u001b[0;32m--> 208\u001b[0m pdf \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mDataFrame\u001b[39m.\u001b[39mfrom_records(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcollect(), columns\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    209\u001b[0m column_counter \u001b[39m=\u001b[39m Counter(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    211\u001b[0m corrected_dtypes: List[Optional[Type]] \u001b[39m=\u001b[39m [\u001b[39mNone\u001b[39;00m] \u001b[39m*\u001b[39m \u001b[39mlen\u001b[39m(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mschema)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/dataframe.py:1216\u001b[0m, in \u001b[0;36mDataFrame.collect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1196\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"Returns all the records as a list of :class:`Row`.\u001b[39;00m\n\u001b[1;32m   1197\u001b[0m \n\u001b[1;32m   1198\u001b[0m \u001b[39m.. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1213\u001b[0m \u001b[39m[Row(age=14, name='<PERSON>'), <PERSON>(age=23, name='<PERSON>'), <PERSON>(age=16, name='<PERSON>')]\u001b[39;00m\n\u001b[1;32m   1214\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m   1215\u001b[0m \u001b[39mwith\u001b[39;00m SCCallSiteSync(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_sc):\n\u001b[0;32m-> 1216\u001b[0m     sock_info \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_jdf\u001b[39m.\u001b[39;49mcollectToPython()\n\u001b[1;32m   1217\u001b[0m \u001b[39mreturn\u001b[39;00m \u001b[39mlist\u001b[39m(_load_from_socket(sock_info, BatchedSerializer(CPickleSerializer())))\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[39m=\u001b[39m proto\u001b[39m.\u001b[39mCALL_COMMAND_NAME \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcommand_header \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[39m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mgateway_client\u001b[39m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[39m=\u001b[39m get_return_value(\n\u001b[1;32m   1323\u001b[0m     answer, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mgateway_client, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mtarget_id, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mname)\n\u001b[1;32m   1325\u001b[0m \u001b[39mfor\u001b[39;00m temp_arg \u001b[39min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mhasattr\u001b[39m(temp_arg, \u001b[39m\"\u001b[39m\u001b[39m_detach\u001b[39m\u001b[39m\"\u001b[39m):\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/errors/exceptions/captured.py:169\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    167\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mdeco\u001b[39m(\u001b[39m*\u001b[39ma: Any, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkw: Any) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m Any:\n\u001b[1;32m    168\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 169\u001b[0m         \u001b[39mreturn\u001b[39;00m f(\u001b[39m*\u001b[39;49ma, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkw)\n\u001b[1;32m    170\u001b[0m     \u001b[39mexcept\u001b[39;00m Py4JJavaError \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    171\u001b[0m         converted \u001b[39m=\u001b[39m convert_exception(e\u001b[39m.\u001b[39mjava_exception)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/protocol.py:334\u001b[0m, in \u001b[0;36mget_return_value\u001b[0;34m(answer, gateway_client, target_id, name)\u001b[0m\n\u001b[1;32m    330\u001b[0m             \u001b[39mraise\u001b[39;00m Py4JError(\n\u001b[1;32m    331\u001b[0m                 \u001b[39m\"\u001b[39m\u001b[39mAn error occurred while calling \u001b[39m\u001b[39m{0}\u001b[39;00m\u001b[39m{1}\u001b[39;00m\u001b[39m{2}\u001b[39;00m\u001b[39m. Trace:\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m{3}\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\"\u001b[39m\u001b[39m.\u001b[39m\n\u001b[1;32m    332\u001b[0m                 \u001b[39mformat\u001b[39m(target_id, \u001b[39m\"\u001b[39m\u001b[39m.\u001b[39m\u001b[39m\"\u001b[39m, name, value))\n\u001b[1;32m    333\u001b[0m     \u001b[39melse\u001b[39;00m:\n\u001b[0;32m--> 334\u001b[0m         \u001b[39mraise\u001b[39;00m Py4JError(\n\u001b[1;32m    335\u001b[0m             \u001b[39m\"\u001b[39m\u001b[39mAn error occurred while calling \u001b[39m\u001b[39m{0}\u001b[39;00m\u001b[39m{1}\u001b[39;00m\u001b[39m{2}\u001b[39;00m\u001b[39m\"\u001b[39m\u001b[39m.\u001b[39m\n\u001b[1;32m    336\u001b[0m             \u001b[39mformat\u001b[39m(target_id, \u001b[39m\"\u001b[39m\u001b[39m.\u001b[39m\u001b[39m\"\u001b[39m, name))\n\u001b[1;32m    337\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    338\u001b[0m     \u001b[39mtype\u001b[39m \u001b[39m=\u001b[39m answer[\u001b[39m1\u001b[39m]\n", "\u001b[0;31mPy4JError\u001b[0m: An error occurred while calling o8405.collectToPython"]}, {"name": "stderr", "output_type": "stream", "text": ["24/04/09 22:12:45 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["def stage8():\n", "    spark = k8s_session(\n", "        name=\"vzhao-menthol\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        # max_workers=5,\n", "        max_workers=320,\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                # Always run the following.\n", "                create_pack_prompts(\n", "                    query_prompt_formatter_config=query_prompt_formatter_config,\n", "                    key_prompt_formatter_config=key_prompt_formatter_config,\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        timeout=7 * 24 * 3600,\n", "        batch_size=32,\n", "        # For debug.\n", "        # batch_size=10,\n", "        # timing_run=True,\n", "        ignore_error=True,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "\n", "    for e in result[\"task_info\"][\"stdout\"]:\n", "        print(e)\n", "\n", "\n", "stage8()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
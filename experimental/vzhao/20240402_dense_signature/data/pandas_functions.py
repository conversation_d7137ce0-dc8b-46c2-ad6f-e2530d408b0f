"""Pandas functions."""

import json
import random
from typing import Callable, Iterator

import pandas as pd
import torch
import torch.nn.functional as F

from experimental.vzhao.data import common, pandas_functions
from research.core.model_input import ModelInput
from research.core.types import EMPTY_CHUNK
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.types import Chunk

_log_with_time = pandas_functions._log_with_time
import numpy as np

from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.models import ModelForwardOutput

create_query_formatter = pandas_functions.create_query_formatter
create_chunk_formatter = pandas_functions.create_chunk_formatter


def _score_all_chunks(
    model_input: ModelInput,
    chunks: list[Chunk],
    model,
    batchsize: int,
) -> tuple[list[list[float]], list[list[int]], list[list[int]]]:
    """Returns a score for each retrieval candidate based on resulting perplexity."""
    # This function is only good for Ender models.
    from research.models.fastforward_models import Ender_FastForward

    assert isinstance(model, Ender_FastForward)
    log_probs = []
    input_toks = []
    target_toks = []

    # List of chunks -> a model input
    def model_input_builder(_chunks) -> ModelInput:
        output = model_input.clone()
        output.extra = {
            "signature_chunks": _chunks,
            "ground_truth_span": CharRange(-999999999999999, -999999999999999),
        }
        return output

    for i in range(0, len(chunks), batchsize):
        model_inputs = list[ModelInput]()
        for chunk in chunks[i : (i + batchsize)]:
            iter_input = model_input_builder([chunk])
            iter_input = maybe_truncate_target_tokens(iter_input, model)
            model_inputs.append(iter_input)
        model_outputs = model.forward_pass(model_inputs)
        log_probs.extend(
            [target_per_token_log_probs(o).tolist() for o in model_outputs]
        )
        input_toks.extend([o.input_tokens.cpu().tolist() for o in model_outputs])
        target_toks.extend(
            [o.label_tokens[o.target_mask].cpu().tolist() for o in model_outputs]
        )
    assert len(log_probs) == len(input_toks)
    return log_probs, input_toks, target_toks


def maybe_truncate_target_tokens(model_input: ModelInput, model) -> ModelInput:
    if model_input.target is None:
        return model_input
    prompt_tokens, _ = model.prompt_formatter.prepare_prompt(model_input)
    max_target_seq_length = model.seq_length - len(prompt_tokens)
    model_input.target = _truncate_text(
        model_input.target, model.tokenizer, max_target_seq_length
    )
    return model_input


def _truncate_text(text: str, tokenizer, max_seq_length: int) -> str:
    tokens = tokenizer.tokenize(text)
    if len(tokens) > max_seq_length:
        return tokenizer.detokenize(tokens[:max_seq_length])
    return text


def target_per_token_log_probs(model_output: ModelForwardOutput) -> torch.Tensor:
    label_tokens = model_output.label_tokens[model_output.target_mask]
    logits = model_output.logits[model_output.target_mask]
    return -F.cross_entropy(logits, label_tokens, reduction="none").cpu()


def random_signature_chunks(
    total_chunks: int = 32,
) -> Callable[[pd.DataFrame], Iterator[pd.Series]]:
    def _random_signature_chunks(df: pd.DataFrame) -> Iterator[pd.Series]:
        """Randomly selects a subset of signature chunks for randon negatives."""
        # Get all chunks in the batch.
        df["signature_chunks"] = df["signature_chunks"].apply(json.loads)
        df["ppl_scores"] = df["ppl_scores"].apply(json.loads)
        all_chunks = []
        for e in df["signature_chunks"]:
            all_chunks.extend(e)

        for _, row in df.iterrows():
            num_sig_chunks = len(row["signature_chunks"])
            if not num_sig_chunks:
                # Remove the row if there is no signature chunks.
                continue
            # Splits dense and signature chunks scores.
            num_dense_chunks = len(json.loads(row["retrieved_chunks"]))
            row["ppl_scores_dense"] = json.dumps(row["ppl_scores"][:num_dense_chunks])
            row["ppl_scores_sig"] = row["ppl_scores"][num_dense_chunks:]

            # Randomly select a subset of chunks.
            min_score = min(row["ppl_scores_sig"])
            ids = set(c["id"] for c in row["signature_chunks"])
            random_chunks = random.sample(all_chunks, total_chunks + num_sig_chunks)
            if len(all_chunks) < total_chunks + num_sig_chunks:
                raise ValueError(
                    f"Not enough chunks in the batch: {len(all_chunks)} < {total_chunks + num_sig_chunks}."
                )
            while len(row["signature_chunks"]) < total_chunks:
                chunk = random_chunks.pop()
                if chunk["id"] not in ids:
                    row["signature_chunks"].append(chunk)
                    row["ppl_scores_sig"].append(min_score)
                    ids.add(chunk["id"])
            row["signature_chunks"] = json.dumps(row["signature_chunks"])
            row["ppl_scores_sig"] = json.dumps(row["ppl_scores_sig"])
            yield row

    return _random_signature_chunks


class ComputePPL:
    """A stateful row-wise function to compute perplexity using Ender Model."""

    def __init__(
        self,
        model_config: dict,
        score_batch_size: int = 2,
        max_middle_len: int = 1024,
    ):
        """Constructor.

        Args:
            model_config: The model config.
            score_batch_size: The batch size for scoring.
            max_middle_len: The maximum length of the middle to score.
        """
        self._model_config = model_config
        self._score_batch_size = score_batch_size
        self._max_middle_len = max_middle_len
        self._model = None

    def get_model(self):
        assert torch.cuda.is_available()

        if self._model is None:
            # Imports are here to avoid CUDA re-initialization RuntimeError.
            from research.eval.harness.factories import create_model
            from research.models.fastforward_models import Ender_FastForward

            self._model = create_model(self._model_config)
            assert isinstance(self._model, Ender_FastForward)
            self._model.load()
        return self._model

    @map_parquet.passthrough_feature(bound=True)
    @map_parquet.allow_unused_args(bound=True)
    def __call__(
        self,
        *,
        prefix,
        suffix,
        middle,
        file_path,
        train_chunks,
    ) -> pd.Series:
        model_input = ModelInput(
            prefix=prefix,
            suffix=suffix,
            # We score the whole middle up to `max_middle_len`.
            target=_truncate_text(
                middle, self.get_model().tokenizer, self._max_middle_len
            ),
            path=file_path,
        )
        all_chunks = common.deserialize_retrieved_chunks(train_chunks) + [EMPTY_CHUNK]

        _log_with_time("Reranking...")
        log_probs, input_toks, target_toks = _score_all_chunks(
            model_input,
            all_chunks,
            self.get_model(),
            self._score_batch_size,
        )
        # Make sure all `log_probs` and `target_toks` have the same length.
        assert all(len(p) == len(t) for p, t in zip(log_probs, target_toks))
        min_target_len = min(len(t) for t in target_toks)
        expect = target_toks[0][:min_target_len]
        for idx, (p, t) in enumerate(zip(log_probs, target_toks)):
            log_probs[idx] = p[:min_target_len]
            assert t[:min_target_len] == expect
            target_toks[idx] = t[:min_target_len]

        assert (
            len(log_probs) == len(common.deserialize_retrieved_chunks(train_chunks)) + 1
        ), f"{len(log_probs)} {len(common.deserialize_retrieved_chunks(train_chunks))}"
        _log_with_time("Done reranking.")
        print(f"Peak GPU Memory: {torch.cuda.max_memory_allocated()}.")
        return pd.Series(
            {
                "token_ppl": log_probs[:-1],
                "empty_chunk_token_ppl": log_probs[-1],
                # For sanity check.
                "input_prompts": input_toks,
                "target_prompts": target_toks,
            }
        )


def mean_ppl(*, input_col_name: str, output_col_name: str):
    """"""

    @map_parquet.passthrough_feature()
    def _mean_ppl(**all_cols) -> pd.Series:
        """Computes sequence perplexity by taking average."""
        token_ppl = all_cols[input_col_name]
        token_ppl = maybe_json_loads(token_ppl)
        assert isinstance(token_ppl, list)
        # 2D array of shape (num_chunks, target_sequence_length).
        values = np.array(token_ppl)
        ppl = np.mean(values, axis=-1).tolist()
        return pd.Series(
            {
                output_col_name: ppl,
            }
        )

    return _mean_ppl


@map_parquet.passthrough_feature()
@map_parquet.allow_unused_args()
def compute_ppg(retrieved_chunks, ppl) -> pd.Series:
    """Computes perplexity gain."""
    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    # Note: This function assumes the first chunk is EmptyChunk.
    assert retrieved_chunks[0].id == ""
    ppl = maybe_json_loads(ppl)
    # ppl of the empty chunk.
    ppl_empty = ppl[0]
    ppg = [s - ppl_empty for s in ppl]
    # TODO(vzhao): Add a new function to add DE ranks.
    # retrieval_rank = list(range(len(retrieved_chunks)))
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
        }
    )


def maybe_json_loads(value):
    if isinstance(value, str):
        return json.loads(value)
    return value

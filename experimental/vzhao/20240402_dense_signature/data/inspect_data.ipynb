{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "from experimental.vzhao.data import common\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read Parquet files"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "from research.data.spark import get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "from experimental.vzhao.data import common as vz_common\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "spark = get_session()\n", "\n", "# path = \"s3a://igor-dev-bucket/ethanol6/ethanol6-16.1/06_shuffled/\"\n", "# path = \"s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/08_mean_doc_prefix_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-16.1/08_mean_doc_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_filtered_tokens/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_1stsp_tokens/\"\n", "\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear64_1stsp_codegen_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/06_shuffled_line_annotated/\"\n", "\n", "# Dense Signature data\n", "# path = \"/mnt/efs/spark-data/user/arun/ender_ppl/eggnog/05_perplexity_score.2/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/06_perplexity_score_randneg/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/ender_ppl/07_sig_only_tokens/\"\n", "\n", "# path = \"/mnt/efs/spark-data/user/vzhao/repo/2024-04-15_21-20-07/step1/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/step1/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/stage2_ppl.bak/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage3_ppl\"\n", "# path = \"s3a://vzhao-dev-bucket/menthol/0416.1/stage3_ppl\"\n", "path = \"s3a://vzhao-dev-bucket/menthol/0416.3/test_head_8\"\n", "# path = \"s3a://vzhao-dev-bucket/menthol/0416.1/stage3_ppl/part-00001-efd672ac-a546-43c6-93d3-028d34f81830-c000.zstd.parquet\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage4_prompt\"\n", "\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df = spark.read.parquet(path)\n", "print(df.count())\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["print(df['prompt_texts'][1][1])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["print(tokenizer.detokenize(spark_common.unpack_tokens(df['prompt_tokens'][1][0])))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["common.deserialize_retrieved_chunks(df['train_chunks'][0])[0].text"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["len(df['train_chunks'][0])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["for _, row in df.iterrows():\n", "    print(max(row['ppl']) - row['empty_chunk_ppl'])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import importlib\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240402_dense_signature.data.pandas_functions\"\n", ")\n", "importlib.reload(pandas_functions)\n", "\n", "rst = list(pandas_functions.random_signature_chunks()(df))\n", "rst[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["x = []\n", "y = []\n", "row_ids = []\n", "for idx, row in df.iterrows():\n", "    ppl_scores = json.loads(row[\"ppl_scores\"])\n", "    if (max_dense := max(ppl_scores[:40])) and (\n", "        max_sig := max(ppl_scores[40:], default=None)\n", "    ):\n", "        x.append(max_dense)\n", "        y.append(max_sig)\n", "        row_ids.append(idx)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["np.a<PERSON><PERSON><PERSON>(y)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["print(row_ids[285], x[285], y[285])"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["print(df.iloc[318, :][\"prefix\"])"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["print(df.iloc[318, :][\"middle\"])"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["json.loads(df.iloc[318, :][\"ppl_scores\"])[40:]"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [], "source": ["print(json.loads(df.iloc[318, :][\"signature_chunks\"])[0])"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [], "source": ["from research.retrieval.libraries.types import Chunk"]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [], "source": ["Chunk(**json.loads(df.iloc[318, :][\"signature_chunks\"])[0])"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["json.loads(df.iloc[472, :][\"ppl_scores\"])[:40]"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.scatter(x, y)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["ppl_scores[42]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["max(ppl_scores[40:])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["len(ppl_scores)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "foo = json.loads(df[\"retrieved_chunks\"][0])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["len(json.loads(df[\"retrieved_chunks\"][0]))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["json.loads(df[\"signature_chunks\"][0])[0].keys()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["len(json.loads(df[\"ppl_scores\"][0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "json.loads(df[\"retrieved_chunks\"][0])[7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages import common as spark_common\n", "\n", "print(tokenizer.detokenize(spark_common.unpack_tokens(df[\"prompt_tokens\"][0][5])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Size"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/07_filter_mean/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_mean_scopepath_tokens/'\n", "# path = 's3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_filtered_tokens/'\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_1stsp_tokens/\"\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/09_1stsp_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/star_ethanol6-17.1/08_linear_1stsp_tokens/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/step1/\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/stage1_retrieval\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage2_shuffle\"\n", "# path = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage3_ppl\"\n", "# path = \"s3a://vzhao-dev-bucket/menthol/0416.1/stage3_ppl\"\n", "# path = \"s3a://augment-temporary/vzhao/menthol/0416.1/stage5_explode\"\n", "path = \"s3a://vzhao-dev-bucket/menthol/0416.3/test_head_8\"\n", "\n", "spark = get_session()\n", "print(path, flush=True)\n", "df = spark.read.parquet(path)\n", "print(f\"Examples: {df.count()}\")\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Indexed Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def rstrip(tokens, id):\n", "    while tokens[-1] == id:\n", "        tokens = tokens[:-1]\n", "    return tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import IndexedDataset, MMapIndexedDataset\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "\n", "# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# path = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath/dataset\"\n", "# path = \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_linear64_codegen/dataset\"\n", "path = (\n", "    \"/mnt/efs/augment/user/vincent/data/starethanol6/ethanol6-17.1_mean_codegen/dataset\"\n", ")\n", "\n", "data = MMapIndexedDataset(path, skip_warmup=True)\n", "d = data[10]\n", "tokenizer = CodeGenTokenizer()\n", "print(f\"length: {len(d)}\")\n", "print(tokenizer.de<PERSON><PERSON><PERSON>(d))\n", "print(d[-10:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
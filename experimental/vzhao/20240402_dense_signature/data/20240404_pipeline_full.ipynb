{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This pipeline runs\n", "1. Signature retrieval using `ResearchSignatureIndex`\n", "2. Scores Siganture Chunks using `<PERSON><PERSON>`."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Full pipeline:\n", "# Stage 1: Gets All signature chunks used in the file and computes PPL scores using <PERSON><PERSON>.\n", "# Stage 2: Gets\n", "\n", "CONFIG = {}\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\",\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1: Run Signature Lookup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.signature_utils import SignaturePrinter\n", "from research.data.retriever import menthol\n", "from research.data.retriever.file_sampler import FileSampler\n", "from research.eval.harness.systems.research_signature_index import (\n", "    ResearchSignatureIndex,\n", ")\n", "\n", "SOURCE = \"/mnt/efs/spark-data/user/vzhao/repo/2024-04-17_06-36-27/\"\n", "STAGE1_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/stage1_retrieval\"\n", "\n", "CONFIG.update(\n", "    {\n", "        \"retrieved_docs\": 127,\n", "    }\n", ")\n", "\n", "pfile_to_samples_fn = menthol.create_pfile_to_samples(\n", "    random_seed=74912,\n", "    import_dropout_prob=0.5,\n", "    max_import_dropout=0.5,\n", "    usage_based_sampling_rate=0.0,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", ")\n", "\n", "file_sampler = FileSampler(\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    language_allowlist=set(SAMPLE_LANGUAGES),\n", ")\n", "\n", "sig_printer = SignaturePrinter(show_full_method_signatures=False)\n", "\n", "signature_index_factory = lambda: ResearchSignatureIndex(\n", "    use_ctx_signatures=True,\n", "    use_cursor_signatures=False,\n", "    top_k_sigs=5,\n", "    est_prefix_chars=0,\n", "    est_suffix_chars=0,\n", "    # use very large number to return all sigantures for the whole file.\n", "    max_ctx_signature_chars=1000000,\n", "    sig_printer=sig_printer,\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE1_URI}"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.data.spark import k8s_session, get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.retriever import menthol\n", "import pandas as pd\n", "from experimental.vzhao.data import common\n", "from typing import Iterator\n", "\n", "def filter_not_enough_chunks(num_of_chunks: int):\n", "\n", "    @map_parquet.passthrough_feature()\n", "    @map_parquet.allow_unused_args()\n", "    def _filter_not_enough_chunks(signature_chunks, random_chunks)-> Iterator[pd.Series]:\n", "        signature_chunks = common.deserialize_retrieved_chunks(signature_chunks)\n", "        random_chunks = common.deserialize_retrieved_chunks(random_chunks)\n", "        if len(signature_chunks) + len(random_chunks) < num_of_chunks:\n", "            # Discard the patch if there are not enough chunks.\n", "            return\n", "        yield pd.Series({})\n", "    return _filter_not_enough_chunks\n", "\n", "def stage1():\n", "    # Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "    # spark = get_session()\n", "    spark = k8s_session(\n", "        max_workers=256,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                menthol.filter_files(SAMPLE_LANGUAGES),\n", "                menthol.apply_signature_lookup(\n", "                    signature_index_factory=signature_index_factory,\n", "                    file_sampler=file_sampler,\n", "                    pfile_to_samples_fn=pfile_to_samples_fn,\n", "                    signature_printer=sig_printer,\n", "                    num_random_chunks=CONFIG[\"retrieved_docs\"],\n", "                ),\n", "                filter_not_enough_chunks(CONFIG[\"retrieved_docs\"]),\n", "            ]\n", "        ),\n", "        input_path=SOURCE,\n", "        output_path=STAGE1_URI,\n", "        batch_size=128,  # small batch size so that we can get estimates quickly\n", "        # timing_run=True,\n", "        # profile=True,  # do some profiling to see where the computes are spent\n", "        drop_original_columns=True,\n", "        ignore_error=False,\n", "    )\n", "    spark.stop()\n", "\n", "    # A rough view of the results give you the success rates, wall time and memory use etc\n", "    stderr = result[\"task_info\"][\"stderr\"]\n", "    print(\"stderr\", stderr)\n", "\n", "    stdout = result[\"task_info\"][\"stdout\"]\n", "    print(\"stdout\", stdout)\n", "\n", "    print(\"result\", result)\n", "\n", "\n", "stage1()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage2: Shuffle & Subsample"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["STAGE1_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416/stage1_retrieval\"\n", "STAGE2_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.2/stage2_shuffle\"\n", "\n", "CONFIG.update(\n", "    {\n", "        \"target_num_samples\": 1_024_000,\n", "    }\n", ")\n", "CONFIG.update(\n", "    {\n", "        \"num_partitions\": int(CONFIG[\"target_num_samples\"] / 96),\n", "    }\n", ")\n", "\n", "print(STAGE2_URI)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE2_URI}"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["from experimental.vzhao.data import common\n", "\n", "common.reshuffle(\n", "    STAGE1_URI,\n", "    STAGE2_URI,\n", "    target_num_samples=CONFIG['target_num_samples'],\n", "    num_partitions=CONFIG['num_partitions'],\n", "    max_workers=32,\n", "    override=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 3: Computes Perplexity Score"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["STAGE2_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage2_shuffle\"\n", "# STAGE3_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.1/stage3_ppl/\"\n", "STAGE3_URI = \"s3a://vzhao-dev-bucket/menthol/0416.1/stage3_ppl\"\n", "\n", "CONFIG['language_model'] = {\n", "    \"name\": \"ender_fastforward\",\n", "    \"model_path\": \"ender/16b_multieth64m_addrust_noinline_sigretprefsuf\",\n", "    \"prompt\": {\n", "        \"component_order\": [\"signature\", \"retrieval\", \"prefix\", \"suffix\"],\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_prompt_tokens\": 5120,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_signature_tokens\": 1024,\n", "        \"max_suffix_tokens\": 768,\n", "    },\n", "}\n", "\n", "CONFIG.update(\n", "    {\n", "        \"dataset_config\": {\n", "            \"seq_length\": 1024,\n", "            \"num_validation_samples\": 8192,\n", "        },\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        \"doc_seq_length\": 1000,\n", "        \"retrieved_docs\": 127,\n", "        \"allow_doc_clipping\": True,\n", "    }\n", ")\n", "\n", "# CONFIG['language_model'] = {\n", "#     \"name\": \"rogue\",\n", "#     \"checkpoint_path\": \"rogue/diffb1m_7b_alphal_fixtoken\",\n", "#     \"prompt\": {\n", "#         \"max_prefix_tokens\": 250,\n", "#         \"max_suffix_tokens\": 250,\n", "#         \"max_retrieved_chunk_tokens\": -1,\n", "#         \"max_prompt_tokens\": 1050,\n", "#     },\n", "# }\n", "\n", "print(STAGE3_URI)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE3_URI}"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "from typing import Iterator\n", "import importlib\n", "import pandas as pd\n", "import json\n", "import os\n", "import random\n", "import torch\n", "from experimental.vzhao.data import common\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240402_dense_signature.data.pandas_functions\"\n", ")\n", "\n", "\n", "@map_parquet.passthrough_feature()\n", "@map_parquet.allow_unused_args()\n", "def test():\n", "    assert torch.cuda.is_available()\n", "    return pd.Series({})\n", "\n", "# TODO(v<PERSON><PERSON>): Try other ways to get candidate.\n", "def get_train_chunks(num_of_chunks: int):\n", "    \"\"\"Uses all signature chunks from the file and random chunks from the repo.\"\"\"\n", "\n", "    @map_parquet.passthrough_feature()\n", "    @map_parquet.allow_unused_args()\n", "    def _get_train_chunks(signature_chunks, random_chunks) -> Iterator[pd.Series]:\n", "        signature_chunks = common.deserialize_retrieved_chunks(signature_chunks)\n", "        random_chunks = common.deserialize_retrieved_chunks(random_chunks)\n", "        if len(signature_chunks) + len(random_chunks) < num_of_chunks:\n", "            # Discard the patch if there are not enough chunks.\n", "            return\n", "\n", "        random.shuffle(signature_chunks)\n", "        train_chunks = signature_chunks[:num_of_chunks]\n", "        train_chunks.extend(\n", "            random.sample(random_chunks, num_of_chunks - len(train_chunks))\n", "        )\n", "        yield pd.Series(\n", "            {\n", "                # \"total_chunks\": len(signature_chunks) + len(random_chunks),\n", "                \"train_chunks\": common.serialize_retrieved_chunks(train_chunks),\n", "            }\n", "        )\n", "\n", "    return _get_train_chunks\n", "\n", "\n", "def stage2():\n", "    # spark_gpu = get_session()\n", "    spark_gpu = k8s_session(\n", "        name=f'vzhao-menthol-{os.path.basename(STAGE3_URI)}',\n", "        max_workers=80,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"32G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"A100_NVLINK_80GB\",\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                get_train_chunks(CONFIG[\"retrieved_docs\"]),\n", "                pandas_functions.ComputePPL(\n", "                    CONFIG[\"language_model\"], score_batch_size=2\n", "                ),\n", "                pandas_functions.mean_ppl(\n", "                    input_col_name=\"token_ppl\", output_col_name=\"ppl\"\n", "                ),\n", "                pandas_functions.mean_ppl(\n", "                    input_col_name=\"empty_chunk_token_ppl\",\n", "                    output_col_name=\"empty_chunk_ppl\",\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=STAGE2_URI,\n", "        output_path=STAGE3_URI,\n", "        batch_size=2,  # small batch size so that we can get estimates quickly\n", "        timing_run=True,\n", "        profile=True,\n", "        timeout=7200,\n", "        # Writes error to a separate file.\n", "        ignore_error=True,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    for e in result[\"task_info\"][\"stdout\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage2()\n", "result"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["print(result['task_info']['stdout'][0])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["result['task_info']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_model\n", "import research.models.fastforward_models\n", "\n", "model = create_model(config)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "\n", "model_input = ModelInput(\"hello\", target=\"world\", extra={\"signature_chunks\": []})\n", "rst = model.forward_pass([model_input])[0]\n", "rst"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["tokenizer.detokenize(rst.label_tokens)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["model.prompt_formatter.prepare_prompt(model_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 4: Create Prompts"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# STAGE3_URI = \"s3a://vzhao-dev-bucket/menthol/0416.1/stage3_ppl\"\n", "STAGE3_URI = \"s3a://vzhao-dev-bucket/menthol/0416.3/stage3_ppl\"\n", "STAGE4_URI = \"/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.3/stage4_prompt\"\n", "\n", "CONFIG.update(\n", "    {\n", "        \"dataset_config\": {\n", "            \"seq_length\": 1024,\n", "            \"num_validation_samples\": 8192,\n", "        },\n", "        # \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        \"retrieved_docs\": 127,\n", "        \"allow_doc_clipping\": True,\n", "    }\n", ")\n", "\n", "query_prompt_formatter_config = {\n", "    \"name\": \"star_ethanol6_query\",\n", "    \"max_tokens\": CONFIG[\"dataset_config\"][\"seq_length\"] - 1,\n", "    \"add_path\": True,\n", "    \"add_scope_path\": True,\n", "    \"add_suffix\": True,\n", "    \"prefix_ratio\": 0.9,\n", "}\n", "\n", "key_prompt_formatter_config = {\n", "    \"name\": \"simple_document\",\n", "    \"add_path\": <PERSON>als<PERSON>,\n", "}\n", "\n", "print(STAGE4_URI)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["!rm -R {STAGE4_URI}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import importlib\n", "from typing import Iterator\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from research.core.model_input import ModelInput\n", "from research.data.spark import k8s_session, get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "\n", "pandas_functions = importlib.import_module(\n", "    \"experimental.vzhao.20240402_dense_signature.data.pandas_functions\"\n", ")\n", "\n", "def pad(prompt, seq_leng) -> np.ndarray:\n", "    return np.pad(prompt, (0, 1 + seq_leng - len(prompt)))\n", "\n", "def create_pack_prompts(\n", "    query_prompt_formatter_config,\n", "    key_prompt_formatter_config,\n", "    *,\n", "    retrieved_docs,\n", "    max_seq_length,\n", "    allow_doc_clipping,\n", "):\n", "    \"\"\"A function to tokenize and prepare prompts for dual encoder training.\"\"\"\n", "\n", "    @map_parquet.allow_unused_args()\n", "    def pack_prompts(\n", "        prefix,\n", "        suffix,\n", "        file_path,\n", "        train_chunks,\n", "        ppl,\n", "    ) -> Iterator[pd.Series]:\n", "        chunks = common.deserialize_retrieved_chunks(train_chunks)\n", "        assert len(chunks) >= retrieved_docs\n", "        assert len(chunks) == len(ppl)\n", "\n", "        chunks = chunks[:retrieved_docs]\n", "        ppl_scores = ppl[:retrieved_docs]\n", "\n", "        # Pulls in registrations\n", "        # ethanol = importlib.import_module(\"experimental.igor.systems.ethanol\")\n", "        import research.core.prompt_formatters\n", "        import research.retrieval.chunk_formatters\n", "\n", "        my_ethanol = importlib.import_module(\n", "            \"experimental.vzhao.20231129_star_ethanol.modeling.ethanol\"\n", "        )\n", "        my_chunking_functions = importlib.import_module(\n", "            \"experimental.vzhao.20231129_star_ethanol.modeling.chunking_functions\"\n", "        )\n", "        from megatron.tokenizer import get_tokenizer\n", "        tokenizer = get_tokenizer('StarCoderTokenizer')\n", "\n", "        all_prompts = []\n", "        all_texts = []\n", "\n", "        # NOTE: Generates query prompt.\n", "        query_prompt_formatter = pandas_functions.create_query_formatter(\n", "            query_prompt_formatter_config\n", "        )\n", "        query_prompt_formatter.tokenizer = tokenizer\n", "        end_of_query_token = query_prompt_formatter.tokenizer.vocab[\n", "            \"<|ret-endofquery|>\"\n", "        ]\n", "        query_prompt, _ = query_prompt_formatter.prepare_prompt(\n", "            ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "        )\n", "        query_prompt.append(end_of_query_token)\n", "        assert sum([1 for t in query_prompt if t == end_of_query_token]) == 1\n", "        if len(query_prompt) > max_seq_length:\n", "            raise ValueError(\n", "                f\"Query token length exceeds seq_len: {len(query_prompt)} > {max_seq_length}\"\n", "            )\n", "        all_prompts.append(spark_common.pack_tokens(pad(query_prompt, max_seq_length)))\n", "        all_texts.append(\n", "            query_prompt_formatter.prepare_prompt_text(\n", "                ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "            )[0]\n", "        )\n", "\n", "        # NOTE: Generates document prompt.\n", "        key_prompt_formatter = pandas_functions.create_chunk_formatter(\n", "            key_prompt_formatter_config\n", "        )\n", "        key_prompt_formatter.tokenizer = tokenizer\n", "        end_of_key_token = key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "        pad_token = key_prompt_formatter.tokenizer.pad_id\n", "        for chunk_idx, (chunk, score) in enumerate(zip(chunks, ppl_scores)):\n", "            # Encode the perplexity score into tokens.\n", "            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(str(f'{score:.5f}'))\n", "            # Format the footer of the prompt\n", "            suffix = [end_of_key_token] + ppl_info_tokens + [pad_token]\n", "            doc_seq_length = max_seq_length - len(suffix)\n", "\n", "            # Format the prompt\n", "            # chunk_prompt = key_prompt_formatter.prepare_prompt(\n", "            #     ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "            # )\n", "            formatted = key_prompt_formatter.format(chunk)\n", "            chunk_prompt = formatted.tokens\n", "            if len(chunk_prompt) > doc_seq_length:\n", "                if allow_doc_clipping:\n", "                    chunk_prompt = chunk_prompt[:doc_seq_length]\n", "                else:\n", "                    raise ValueError(\n", "                        f\"Prompt too long: {len(chunk_prompt)} > {doc_seq_length}\"\n", "                    )\n", "\n", "            chunk_prompt.extend(suffix)\n", "            assert sum([1 for t in chunk_prompt if t == end_of_key_token]) == 1\n", "\n", "            # Check that the prompt is not too long. `chunk_prompt` contains both\n", "            # key prompt and score.\n", "            if len(chunk_prompt) > max_seq_length:\n", "                print(\"===================================================\")\n", "                print(key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "                print(\"===================================================\")\n", "                raise ValueError(\n", "                    f\"{chunk_idx} token length exceeds seq_len: {len(chunk_prompt)} > {max_seq_length}\"\n", "                )\n", "\n", "            all_prompts.append(spark_common.pack_tokens(pad(chunk_prompt, max_seq_length)))\n", "            all_texts.append(formatted.text)\n", "\n", "        # +1 because the first prompt is the query prompt.\n", "        if len(all_prompts) == retrieved_docs + 1:\n", "            yield pd.Series(\n", "                {\n", "                    \"prompt_tokens\": all_prompts,\n", "                    \"prompt_texts\": all_texts,\n", "                }\n", "            )\n", "\n", "    return pack_prompts\n", "\n", "\n", "def stage4():\n", "    # spark = get_session()\n", "    spark = k8s_session(\n", "        name=\"vzhao-menthol\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1050g\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        # max_workers=5,\n", "        max_workers=180,\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                # Always run the following.\n", "                create_pack_prompts(\n", "                    query_prompt_formatter_config,\n", "                    key_prompt_formatter_config,\n", "                    retrieved_docs=CONFIG['retrieved_docs'],\n", "                    max_seq_length=CONFIG['dataset_config']['seq_length'],\n", "                    allow_doc_clipping=CONFIG['allow_doc_clipping'],\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=STAGE3_URI,\n", "        output_path=STAGE4_URI,\n", "        timeout=7 * 24 * 3600,\n", "        batch_size=128,\n", "        # For debug.\n", "        # batch_size=10,\n", "        # timing_run=True,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "\n", "    for e in result[\"task_info\"][\"stdout\"]:\n", "        print(e)\n", "\n", "\n", "stage4()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
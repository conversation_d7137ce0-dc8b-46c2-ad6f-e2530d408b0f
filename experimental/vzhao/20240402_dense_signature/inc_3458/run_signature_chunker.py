"""Debug script for signature chunker."""

from models.retrieval.chunking import signature_chunker
from models.retrieval.chunking import chunking


def main():
    path = "/mnt/efs/spark-data/shared/debug/inc_3458/SplitButton.js"
    with open(path, "r") as f:
        text = f.read()
    doc = chunking.Document(
        blob_name="1",
        text=text,
        path=path,
    )

    chunker = signature_chunker.SignatureChunker()

    chunks = chunker.split_into_chunks(doc)
    print(len(list(chunks)))


if __name__ == "__main__":
    main()

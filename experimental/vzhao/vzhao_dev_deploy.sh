
bazel run -c opt //services/deploy:starethanol6_16_1_proj512_kubecfg -- apply
bazel run -c opt //services/deploy:methanol_0416_4_kubecfg -- apply

bazel run -c opt //services/deploy:rogue_1B_fp8_ethanol6_04_1_kubecfg -- delete

bazel run -c opt //services/deploy:roguesl_v2_16b_starethanol6_16_1_proj512_rec_kubecfg -- delete
bazel run -c opt //services/deploy:ender_16B_methanol_0416_4_starethanol6_16_1_kubecfg -- apply
bazel run -c opt //services/deploy:roguesl_v2_16b_methanol0416_3_rec_kubecfg -- apply

bazel run -c opt //services/request_insight:core_kubecfg -- delete

bazel run -c opt //services/support:kubecfg -- apply

bazel run -c opt //services/embeddings_search_host/server:kubecfg -- delete

bazel run -c opt //services/embeddings_search_host/cpu_server:kubecfg -- apply

bazel run -c opt //services/api_proxy/server:kubecfg -- apply

bazel run -c opt //services/content_manager/server:kubecfg -- apply


kubectl delete deployment --all
kubectl delete configmap --all

kubectl delete bigtabletable dev-vzhao-content-manager


# Next edit
bazel run  -c opt //services/deploy:dev_deploy -- --services default completion nextedit --operation Apply
bazel run  -c opt //services/deploy:raven_edit_v3_15b_kubecfg -- apply
bazel run  -c opt //services/deploy:raven_edit_v4_15b_kubecfg -- delete

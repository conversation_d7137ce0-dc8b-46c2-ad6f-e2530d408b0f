{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Per User Usages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dogfood"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT user_id, ARRAY_AGG(event_name) AS event_name, ARRAY_AGG(count) AS count\n", "FROM (\n", "  SELECT event_name, user_id, COUNT(DISTINCT sid) AS count\n", "  FROM (\n", "    WITH\n", "        SESSION AS (\n", "        SELECT\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "        FROM\n", "          `us_staging_request_insight_analytics_dataset.next_edit_session_event`\n", "        WHERE\n", "          tenant=\"dogfood-shard\"\n", "          AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") IS NOT NULL\n", "          AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") IN (\"accept\", \"hover-shown\", \"suggestion-hint-shown\", \"reject\")\n", "          AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "        METADATA AS (\n", "        SELECT request_id, user_id, user_agent,\n", "        FROM `us_staging_request_insight_analytics_dataset.request_metadata`\n", "        WHERE\n", "          NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "          AND tenant = 'dogfood-shard'\n", "          AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\")\n", "      )\n", "      SELECT SESSION.event_name, SESSION.sid, METADATA.user_id,\n", "      FROM SESSION\n", "      JOIN METADATA\n", "      USING (request_id)\n", "  )\n", "  GROUP BY event_name, user_id)\n", "GROUP BY user_id\n", "\"\"\"\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>hover-shown</th>\n", "      <th>reject</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>user_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>355.0</td>\n", "      <td>739</td>\n", "      <td>595.0</td>\n", "      <td>8.0</td>\n", "      <td>0.596639</td>\n", "      <td>0.805142</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>259.0</td>\n", "      <td>720</td>\n", "      <td>525.0</td>\n", "      <td>1.0</td>\n", "      <td>0.493333</td>\n", "      <td>0.729167</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>66.0</td>\n", "      <td>345</td>\n", "      <td>246.0</td>\n", "      <td>1.0</td>\n", "      <td>0.268293</td>\n", "      <td>0.713043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>msde<PERSON></th>\n", "      <td>166.0</td>\n", "      <td>335</td>\n", "      <td>231.0</td>\n", "      <td>5.0</td>\n", "      <td>0.718615</td>\n", "      <td>0.689552</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>26.0</td>\n", "      <td>116</td>\n", "      <td>78.0</td>\n", "      <td>NaN</td>\n", "      <td>0.333333</td>\n", "      <td>0.672414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z<PERSON><PERSON></th>\n", "      <td>278.0</td>\n", "      <td>662</td>\n", "      <td>433.0</td>\n", "      <td>2.0</td>\n", "      <td>0.642032</td>\n", "      <td>0.654079</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>18.0</td>\n", "      <td>222</td>\n", "      <td>142.0</td>\n", "      <td>NaN</td>\n", "      <td>0.126761</td>\n", "      <td>0.639640</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>5.0</td>\n", "      <td>292</td>\n", "      <td>184.0</td>\n", "      <td>11.0</td>\n", "      <td>0.027174</td>\n", "      <td>0.630137</td>\n", "    </tr>\n", "    <tr>\n", "      <th>edvin</th>\n", "      <td>161.0</td>\n", "      <td>482</td>\n", "      <td>302.0</td>\n", "      <td>7.0</td>\n", "      <td>0.533113</td>\n", "      <td>0.626556</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>83.0</td>\n", "      <td>502</td>\n", "      <td>311.0</td>\n", "      <td>2.0</td>\n", "      <td>0.266881</td>\n", "      <td>0.619522</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ran</th>\n", "      <td>16.0</td>\n", "      <td>181</td>\n", "      <td>111.0</td>\n", "      <td>1.0</td>\n", "      <td>0.144144</td>\n", "      <td>0.613260</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>43.0</td>\n", "      <td>184</td>\n", "      <td>112.0</td>\n", "      <td>2.0</td>\n", "      <td>0.383929</td>\n", "      <td>0.608696</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>44.0</td>\n", "      <td>176</td>\n", "      <td>107.0</td>\n", "      <td>1.0</td>\n", "      <td>0.411215</td>\n", "      <td>0.607955</td>\n", "    </tr>\n", "    <tr>\n", "      <th>colin</th>\n", "      <td>49.0</td>\n", "      <td>665</td>\n", "      <td>401.0</td>\n", "      <td>3.0</td>\n", "      <td>0.122195</td>\n", "      <td>0.603008</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>15.0</td>\n", "      <td>168</td>\n", "      <td>97.0</td>\n", "      <td>NaN</td>\n", "      <td>0.154639</td>\n", "      <td>0.577381</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>20.0</td>\n", "      <td>255</td>\n", "      <td>140.0</td>\n", "      <td>1.0</td>\n", "      <td>0.142857</td>\n", "      <td>0.549020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dirk</th>\n", "      <td>66.0</td>\n", "      <td>741</td>\n", "      <td>395.0</td>\n", "      <td>12.0</td>\n", "      <td>0.167089</td>\n", "      <td>0.533063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jacqueline</th>\n", "      <td>78.0</td>\n", "      <td>396</td>\n", "      <td>207.0</td>\n", "      <td>1.0</td>\n", "      <td>0.376812</td>\n", "      <td>0.522727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>v<PERSON><PERSON>@augmentcode.com</th>\n", "      <td>25.0</td>\n", "      <td>184</td>\n", "      <td>95.0</td>\n", "      <td>3.0</td>\n", "      <td>0.263158</td>\n", "      <td>0.516304</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>26.0</td>\n", "      <td>177</td>\n", "      <td>90.0</td>\n", "      <td>NaN</td>\n", "      <td>0.288889</td>\n", "      <td>0.508475</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>55.0</td>\n", "      <td>444</td>\n", "      <td>213.0</td>\n", "      <td>NaN</td>\n", "      <td>0.258216</td>\n", "      <td>0.479730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>yury</th>\n", "      <td>8.0</td>\n", "      <td>223</td>\n", "      <td>99.0</td>\n", "      <td>1.0</td>\n", "      <td>0.080808</td>\n", "      <td>0.443946</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>1.0</td>\n", "      <td>1681</td>\n", "      <td>703.0</td>\n", "      <td>4.0</td>\n", "      <td>0.001422</td>\n", "      <td>0.418203</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>14.0</td>\n", "      <td>168</td>\n", "      <td>70.0</td>\n", "      <td>1.0</td>\n", "      <td>0.200000</td>\n", "      <td>0.416667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>zhe<PERSON></th>\n", "      <td>NaN</td>\n", "      <td>171</td>\n", "      <td>35.0</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>0.204678</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         accept  suggestion-hint-shown  hover-shown  reject  \\\n", "user_id                                                                       \n", "<EMAIL>    355.0                    739        595.0     8.0   \n", "<EMAIL>      259.0                    720        525.0     1.0   \n", "<EMAIL>      66.0                    345        246.0     1.0   \n", "msdejong                  166.0                    335        231.0     5.0   \n", "<EMAIL>      26.0                    116         78.0     NaN   \n", "<PERSON><PERSON><PERSON>                   278.0                    662        433.0     2.0   \n", "<EMAIL>       18.0                    222        142.0     NaN   \n", "<EMAIL>      5.0                    292        184.0    11.0   \n", "edvin                     161.0                    482        302.0     7.0   \n", "<EMAIL>    83.0                    502        311.0     2.0   \n", "ran                        16.0                    181        111.0     1.0   \n", "<EMAIL>         43.0                    184        112.0     2.0   \n", "<EMAIL>       44.0                    176        107.0     1.0   \n", "colin                      49.0                    665        401.0     3.0   \n", "<EMAIL>      15.0                    168         97.0     NaN   \n", "<EMAIL>       20.0                    255        140.0     1.0   \n", "dirk                       66.0                    741        395.0    12.0   \n", "<PERSON><PERSON><PERSON><PERSON>                 78.0                    396        207.0     1.0   \n", "<EMAIL>      25.0                    184         95.0     3.0   \n", "<EMAIL>     26.0                    177         90.0     NaN   \n", "<EMAIL>      55.0                    444        213.0     NaN   \n", "yury                        8.0                    223         99.0     1.0   \n", "<EMAIL>      1.0                   1681        703.0     4.0   \n", "<EMAIL>       14.0                    168         70.0     1.0   \n", "zhewei                      NaN                    171         35.0     4.0   \n", "\n", "                         accept_rate  hover_rate  \n", "user_id                                           \n", "<EMAIL>      0.596639    0.805142  \n", "<EMAIL>        0.493333    0.729167  \n", "<EMAIL>       0.268293    0.713043  \n", "msdejong                    0.718615    0.689552  \n", "<EMAIL>       0.333333    0.672414  \n", "zhuoran                     0.642032    0.654079  \n", "<EMAIL>        0.126761    0.639640  \n", "<EMAIL>      0.027174    0.630137  \n", "edvin                       0.533113    0.626556  \n", "<EMAIL>     0.266881    0.619522  \n", "ran                         0.144144    0.613260  \n", "<EMAIL>          0.383929    0.608696  \n", "<EMAIL>        0.411215    0.607955  \n", "colin                       0.122195    0.603008  \n", "<EMAIL>       0.154639    0.577381  \n", "<EMAIL>        0.142857    0.549020  \n", "dirk                        0.167089    0.533063  \n", "j<PERSON><PERSON><PERSON>                  0.376812    0.522727  \n", "<EMAIL>       0.263158    0.516304  \n", "<EMAIL>      0.288889    0.508475  \n", "<EMAIL>       0.258216    0.479730  \n", "yury                        0.080808    0.443946  \n", "<EMAIL>      0.001422    0.418203  \n", "<EMAIL>        0.200000    0.416667  \n", "zhewei                           NaN    0.204678  "]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "\n", "def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row.event_name, row.count)}\n", "    output[\"user_id\"] = row.user_id\n", "    return output\n", "\n", "\n", "df_user_dogfood = pd.DataFrame([row_to_dict(row) for row in data])\n", "df_user_dogfood.set_index(\"user_id\", inplace=True)\n", "# df_user_dogfood = df_user_dogfood.sum(axis=0)\n", "\n", "df_user_dogfood[\"accept_rate\"] = (\n", "    df_user_dogfood[\"accept\"] / df_user_dogfood[\"hover-shown\"]\n", ")\n", "df_user_dogfood[\"hover_rate\"] = (\n", "    df_user_dogfood[\"hover-shown\"] / df_user_dogfood[\"suggestion-hint-shown\"]\n", ")\n", "df_user_dogfood[df_user_dogfood[\"suggestion-hint-shown\"] > 100].sort_values(\n", "    \"hover_rate\", ascending=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.13686695372828372\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import hashlib\n", "\n", "\n", "def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row[1], row[2])}\n", "    output[\"user_id\"] = row.user_id\n", "    # output[\"user_id\"] = hashlib.sha256(row.user_id.encode('utf-8')).hexdigest()[:12]\n", "    return output\n", "\n", "\n", "table = pd.DataFrame([row_to_dict(row) for row in data])\n", "table.set_index(\"user_id\", inplace=True)\n", "# table = table[table[\"suggestion-offset-text-shown\"] > 100]\n", "table[\"accept_rate\"] = table[\"accept\"] / table[\"hover-shown\"]\n", "table[\"hove_shown_rate\"] = table[\"hover-shown\"] / table[\"suggestion-hint-shown\"]\n", "\n", "table[\"median_distance\"] = np.sqrt(\n", "    (table[\"accept_rate\"] - table[\"accept_rate\"].median()) ** 2\n", "    + (table[\"hove_shown_rate\"] - table[\"hove_shown_rate\"].median()) ** 2\n", ")\n", "print(table[\"median_distance\"].std())\n", "table = table[\n", "    [\n", "        \"accept\",\n", "        \"reject\",\n", "        \"hover-shown\",\n", "        \"suggestion-offset-text-shown\",\n", "        \"suggestion-hint-shown\",\n", "        \"accept_rate\",\n", "        \"hove_shown_rate\",\n", "        \"median_distance\",\n", "    ]\n", "]\n", "# table.sort_values(\"median_distance\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT\n", "  user_id,\n", "  ARRAY_AGG(LANGUAGE ORDER BY count DESC LIMIT 1)[OFFSET(0)] AS top_language,\n", "FROM (\n", "  SELECT\n", "    user_id,\n", "    LANGUAGE,\n", "    COUNT(*) AS count\n", "  FROM (\n", "    WITH\n", "      REQUEST AS (\n", "      SELECT\n", "        request_id,\n", "        COALESCE(JSON_EXTRACT_SCALAR(raw_json, \"$.request.lang\"), \"null\") AS LANGUAGE,\n", "      FROM\n", "        `staging_request_insight_full_export_dataset.next_edit_host_request`\n", "      WHERE\n", "      tenant=\"dogfood-shard\"\n", "      AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) \n", "        ),\n", "      METADATA AS (\n", "      SELECT request_id, user_id, user_agent,\n", "        FROM `us_staging_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck')\n", "        AND NOT STARTS_WITH(user_agent, 'augment_review_bot')\n", "        AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant = 'dogfood-shard'\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\")\n", "        )\n", "    SELECT\n", "      REQUEST.language,\n", "      METADATA.user_id\n", "    FROM REQUEST\n", "    JOIN\n", "      METADATA\n", "    USING\n", "      (request_id))\n", "  GROUP BY\n", "    user_id,\n", "    LANGUAGE)\n", "GROUP BY\n", "  user_id\n", "\"\"\"\n", "\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)\n", "\n", "table_lang = pd.DataFrame([dict(d.items()) for d in data])\n", "table_lang.set_index(\"user_id\", inplace=True)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-offset-text-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hove_shown_rate</th>\n", "      <th>median_distance</th>\n", "      <th>top_language</th>\n", "    </tr>\n", "    <tr>\n", "      <th>user_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>680.0</td>\n", "      <td>NaN</td>\n", "      <td>1132.0</td>\n", "      <td>0.002941</td>\n", "      <td>0.600707</td>\n", "      <td>0.331238</td>\n", "      <td>svelte</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>240.0</td>\n", "      <td>224.0</td>\n", "      <td>456.0</td>\n", "      <td>0.012500</td>\n", "      <td>0.526316</td>\n", "      <td>0.353512</td>\n", "      <td>svelte</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlm</th>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>62.0</td>\n", "      <td>326.0</td>\n", "      <td>108.0</td>\n", "      <td>0.080645</td>\n", "      <td>0.574074</td>\n", "      <td>0.270635</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>colin</th>\n", "      <td>64.0</td>\n", "      <td>4.0</td>\n", "      <td>595.0</td>\n", "      <td>102.0</td>\n", "      <td>859.0</td>\n", "      <td>0.107563</td>\n", "      <td>0.692666</td>\n", "      <td>0.209920</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>30.0</td>\n", "      <td>54.0</td>\n", "      <td>260.0</td>\n", "      <td>10.0</td>\n", "      <td>347.0</td>\n", "      <td>0.115385</td>\n", "      <td>0.749280</td>\n", "      <td>0.206327</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>8.0</td>\n", "      <td>5.0</td>\n", "      <td>10.0</td>\n", "      <td>0.125000</td>\n", "      <td>0.800000</td>\n", "      <td>0.213940</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "      <td>42.0</td>\n", "      <td>0.125000</td>\n", "      <td>0.571429</td>\n", "      <td>0.234396</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dirk</th>\n", "      <td>28.0</td>\n", "      <td>4.0</td>\n", "      <td>199.0</td>\n", "      <td>204.0</td>\n", "      <td>266.0</td>\n", "      <td>0.140704</td>\n", "      <td>0.748120</td>\n", "      <td>0.181382</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>34.0</td>\n", "      <td>NaN</td>\n", "      <td>195.0</td>\n", "      <td>94.0</td>\n", "      <td>332.0</td>\n", "      <td>0.174359</td>\n", "      <td>0.587349</td>\n", "      <td>0.185451</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>87.0</td>\n", "      <td>1.0</td>\n", "      <td>491.0</td>\n", "      <td>108.0</td>\n", "      <td>702.0</td>\n", "      <td>0.177189</td>\n", "      <td>0.699430</td>\n", "      <td>0.140028</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>2.0</td>\n", "      <td>8.0</td>\n", "      <td>11.0</td>\n", "      <td>NaN</td>\n", "      <td>11.0</td>\n", "      <td>0.181818</td>\n", "      <td>1.000000</td>\n", "      <td>0.323824</td>\n", "      <td>javascript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>76.0</td>\n", "      <td>1.0</td>\n", "      <td>306.0</td>\n", "      <td>545.0</td>\n", "      <td>453.0</td>\n", "      <td>0.248366</td>\n", "      <td>0.675497</td>\n", "      <td>0.075083</td>\n", "      <td>go</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>27.0</td>\n", "      <td>NaN</td>\n", "      <td>108.0</td>\n", "      <td>17.0</td>\n", "      <td>187.0</td>\n", "      <td>0.250000</td>\n", "      <td>0.577540</td>\n", "      <td>0.144718</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ran</th>\n", "      <td>54.0</td>\n", "      <td>1.0</td>\n", "      <td>204.0</td>\n", "      <td>NaN</td>\n", "      <td>255.0</td>\n", "      <td>0.264706</td>\n", "      <td>0.800000</td>\n", "      <td>0.107799</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>16.0</td>\n", "      <td>NaN</td>\n", "      <td>58.0</td>\n", "      <td>NaN</td>\n", "      <td>84.0</td>\n", "      <td>0.275862</td>\n", "      <td>0.690476</td>\n", "      <td>0.043959</td>\n", "      <td>plaintext</td>\n", "    </tr>\n", "    <tr>\n", "      <th>v<PERSON><PERSON>@augmentcode.com</th>\n", "      <td>36.0</td>\n", "      <td>2.0</td>\n", "      <td>121.0</td>\n", "      <td>21.0</td>\n", "      <td>237.0</td>\n", "      <td>0.297521</td>\n", "      <td>0.510549</td>\n", "      <td>0.196204</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>23.0</td>\n", "      <td>NaN</td>\n", "      <td>77.0</td>\n", "      <td>100.0</td>\n", "      <td>115.0</td>\n", "      <td>0.298701</td>\n", "      <td>0.669565</td>\n", "      <td>0.040604</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>54.0</td>\n", "      <td>NaN</td>\n", "      <td>179.0</td>\n", "      <td>1.0</td>\n", "      <td>261.0</td>\n", "      <td>0.301676</td>\n", "      <td>0.685824</td>\n", "      <td>0.025202</td>\n", "      <td>svelte</td>\n", "    </tr>\n", "    <tr>\n", "      <th>xiaolei</th>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>19.0</td>\n", "      <td>4.0</td>\n", "      <td>33.0</td>\n", "      <td>0.315789</td>\n", "      <td>0.575758</td>\n", "      <td>0.130024</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>markus</th>\n", "      <td>13.0</td>\n", "      <td>NaN</td>\n", "      <td>41.0</td>\n", "      <td>NaN</td>\n", "      <td>39.0</td>\n", "      <td>0.317073</td>\n", "      <td>1.051282</td>\n", "      <td>0.345506</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>26.0</td>\n", "      <td>8.0</td>\n", "      <td>76.0</td>\n", "      <td>2.0</td>\n", "      <td>75.0</td>\n", "      <td>0.342105</td>\n", "      <td>1.013333</td>\n", "      <td>0.308575</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>edvin</th>\n", "      <td>97.0</td>\n", "      <td>1.0</td>\n", "      <td>277.0</td>\n", "      <td>1.0</td>\n", "      <td>400.0</td>\n", "      <td>0.350181</td>\n", "      <td>0.692500</td>\n", "      <td>0.035670</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>19.0</td>\n", "      <td>4.0</td>\n", "      <td>52.0</td>\n", "      <td>NaN</td>\n", "      <td>62.0</td>\n", "      <td>0.365385</td>\n", "      <td>0.838710</td>\n", "      <td>0.141441</td>\n", "      <td>markdown</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>32.0</td>\n", "      <td>NaN</td>\n", "      <td>82.0</td>\n", "      <td>213.0</td>\n", "      <td>120.0</td>\n", "      <td>0.390244</td>\n", "      <td>0.683333</td>\n", "      <td>0.076535</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>10.0</td>\n", "      <td>NaN</td>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "      <td>60.0</td>\n", "      <td>0.416667</td>\n", "      <td>0.400000</td>\n", "      <td>0.321586</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>438.0</td>\n", "      <td>NaN</td>\n", "      <td>576.0</td>\n", "      <td>0.456621</td>\n", "      <td>0.760417</td>\n", "      <td>0.149864</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>43.0</td>\n", "      <td>NaN</td>\n", "      <td>94.0</td>\n", "      <td>188.0</td>\n", "      <td>132.0</td>\n", "      <td>0.457447</td>\n", "      <td>0.712121</td>\n", "      <td>0.140517</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jacqueline</th>\n", "      <td>83.0</td>\n", "      <td>NaN</td>\n", "      <td>169.0</td>\n", "      <td>142.0</td>\n", "      <td>195.0</td>\n", "      <td>0.491124</td>\n", "      <td>0.866667</td>\n", "      <td>0.237023</td>\n", "      <td>rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>50.0</td>\n", "      <td>3.0</td>\n", "      <td>98.0</td>\n", "      <td>NaN</td>\n", "      <td>119.0</td>\n", "      <td>0.510204</td>\n", "      <td>0.823529</td>\n", "      <td>0.226198</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>203.0</td>\n", "      <td>2.0</td>\n", "      <td>389.0</td>\n", "      <td>NaN</td>\n", "      <td>393.0</td>\n", "      <td>0.521851</td>\n", "      <td>0.989822</td>\n", "      <td>0.350166</td>\n", "      <td>go</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>18.0</td>\n", "      <td>2.0</td>\n", "      <td>34.0</td>\n", "      <td>NaN</td>\n", "      <td>70.0</td>\n", "      <td>0.529412</td>\n", "      <td>0.485714</td>\n", "      <td>0.305802</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th>moogi</th>\n", "      <td>115.0</td>\n", "      <td>4.0</td>\n", "      <td>217.0</td>\n", "      <td>148.0</td>\n", "      <td>223.0</td>\n", "      <td>0.529954</td>\n", "      <td>0.973094</td>\n", "      <td>0.341727</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>51.0</td>\n", "      <td>3.0</td>\n", "      <td>91.0</td>\n", "      <td>NaN</td>\n", "      <td>114.0</td>\n", "      <td>0.560440</td>\n", "      <td>0.798246</td>\n", "      <td>0.260342</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>20.0</td>\n", "      <td>3.0</td>\n", "      <td>33.0</td>\n", "      <td>NaN</td>\n", "      <td>37.0</td>\n", "      <td>0.606061</td>\n", "      <td>0.891892</td>\n", "      <td>0.343734</td>\n", "      <td>svelte</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z<PERSON><PERSON></th>\n", "      <td>198.0</td>\n", "      <td>NaN</td>\n", "      <td>318.0</td>\n", "      <td>781.0</td>\n", "      <td>435.0</td>\n", "      <td>0.622642</td>\n", "      <td>0.731034</td>\n", "      <td>0.306611</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "      <td>35.0</td>\n", "      <td>4.0</td>\n", "      <td>34.0</td>\n", "      <td>0.685714</td>\n", "      <td>1.029412</td>\n", "      <td>0.490547</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>msde<PERSON></th>\n", "      <td>212.0</td>\n", "      <td>11.0</td>\n", "      <td>296.0</td>\n", "      <td>297.0</td>\n", "      <td>350.0</td>\n", "      <td>0.716216</td>\n", "      <td>0.845714</td>\n", "      <td>0.422963</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mb</th>\n", "      <td>34.0</td>\n", "      <td>2.0</td>\n", "      <td>45.0</td>\n", "      <td>NaN</td>\n", "      <td>41.0</td>\n", "      <td>0.755556</td>\n", "      <td>1.097561</td>\n", "      <td>0.588016</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nikita</th>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>7.0</td>\n", "      <td>14.0</td>\n", "      <td>11.0</td>\n", "      <td>0.857143</td>\n", "      <td>0.636364</td>\n", "      <td>0.544512</td>\n", "      <td>jsonnet</td>\n", "    </tr>\n", "    <tr>\n", "      <th>zhe<PERSON></th>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>14.0</td>\n", "      <td>18.0</td>\n", "      <td>NaN</td>\n", "      <td>0.222222</td>\n", "      <td>NaN</td>\n", "      <td>jsonnet</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>34.0</td>\n", "      <td>10.0</td>\n", "      <td>32.0</td>\n", "      <td>NaN</td>\n", "      <td>1.062500</td>\n", "      <td>NaN</td>\n", "      <td>rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24.0</td>\n", "      <td>102.0</td>\n", "      <td>101.0</td>\n", "      <td>NaN</td>\n", "      <td>0.237624</td>\n", "      <td>NaN</td>\n", "      <td>viml</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>markdown</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dietz</th>\n", "      <td>16.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>carl</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>17.0</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>0.600000</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>52.0</td>\n", "      <td>NaN</td>\n", "      <td>41.0</td>\n", "      <td>NaN</td>\n", "      <td>1.268293</td>\n", "      <td>NaN</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>19.0</td>\n", "      <td>8.0</td>\n", "      <td>NaN</td>\n", "      <td>0.375000</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>1.666667</td>\n", "      <td>NaN</td>\n", "      <td>xml</td>\n", "    </tr>\n", "    <tr>\n", "      <th>yuri</th>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>28.0</td>\n", "      <td>NaN</td>\n", "      <td>0.250000</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>yury</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>81.0</td>\n", "      <td>NaN</td>\n", "      <td>130.0</td>\n", "      <td>NaN</td>\n", "      <td>0.623077</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>jsonnet</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          accept  reject  hover-shown  \\\n", "user_id                                                 \n", "<EMAIL>       2.0     2.0        680.0   \n", "<EMAIL>         3.0     1.0        240.0   \n", "mlm                          5.0     6.0         62.0   \n", "colin                       64.0     4.0        595.0   \n", "<EMAIL>      30.0    54.0        260.0   \n", "<EMAIL>       1.0     NaN          8.0   \n", "<EMAIL>      3.0     NaN         24.0   \n", "dirk                        28.0     4.0        199.0   \n", "<EMAIL>       34.0     NaN        195.0   \n", "<EMAIL>     87.0     1.0        491.0   \n", "<EMAIL>     2.0     8.0         11.0   \n", "<EMAIL>       76.0     1.0        306.0   \n", "<EMAIL>        27.0     NaN        108.0   \n", "ran                         54.0     1.0        204.0   \n", "<EMAIL>          16.0     NaN         58.0   \n", "<EMAIL>       36.0     2.0        121.0   \n", "<EMAIL>      23.0     NaN         77.0   \n", "<EMAIL>       54.0     NaN        179.0   \n", "xiaolei                      6.0     NaN         19.0   \n", "markus                      13.0     NaN         41.0   \n", "<EMAIL>         26.0     8.0         76.0   \n", "edvin                       97.0     1.0        277.0   \n", "<EMAIL>       19.0     4.0         52.0   \n", "<EMAIL>       32.0     NaN         82.0   \n", "<EMAIL>       10.0     NaN         24.0   \n", "<EMAIL>       200.0     1.0        438.0   \n", "<EMAIL>        43.0     NaN         94.0   \n", "<PERSON><PERSON><PERSON><PERSON>                  83.0     NaN        169.0   \n", "<EMAIL>        50.0     3.0         98.0   \n", "<EMAIL>     203.0     2.0        389.0   \n", "<EMAIL>       18.0     2.0         34.0   \n", "moogi                      115.0     4.0        217.0   \n", "<EMAIL>      51.0     3.0         91.0   \n", "<EMAIL>         20.0     3.0         33.0   \n", "zhuoran                    198.0     NaN        318.0   \n", "<EMAIL>       24.0     NaN         35.0   \n", "msdejong                   212.0    11.0        296.0   \n", "mb                          34.0     2.0         45.0   \n", "nikita                       6.0     NaN          7.0   \n", "zhewei                       NaN     1.0          4.0   \n", "<EMAIL>        NaN     NaN         34.0   \n", "<EMAIL>       NaN     NaN         24.0   \n", "<EMAIL>        NaN     NaN          1.0   \n", "dietz                       16.0     NaN          NaN   \n", "carl                         NaN     NaN          3.0   \n", "<EMAIL>         NaN     NaN         52.0   \n", "<EMAIL>          NaN     NaN          3.0   \n", "<EMAIL>       NaN     1.0          5.0   \n", "<PERSON><PERSON>     2.0          7.0   \n", "<EMAIL>     NaN     NaN          2.0   \n", "<EMAIL>         NaN     NaN          2.0   \n", "<PERSON><PERSON>         81.0   \n", "<EMAIL>      NaN     NaN          NaN   \n", "<EMAIL>         NaN     NaN          NaN   \n", "\n", "                          suggestion-offset-text-shown  suggestion-hint-shown  \\\n", "user_id                                                                         \n", "<EMAIL>                             NaN                 1132.0   \n", "<EMAIL>                             224.0                  456.0   \n", "mlm                                              326.0                  108.0   \n", "colin                                            102.0                  859.0   \n", "<EMAIL>                            10.0                  347.0   \n", "<EMAIL>                             5.0                   10.0   \n", "<EMAIL>                            NaN                   42.0   \n", "dirk                                             204.0                  266.0   \n", "<EMAIL>                             94.0                  332.0   \n", "<EMAIL>                          108.0                  702.0   \n", "<EMAIL>                           NaN                   11.0   \n", "<EMAIL>                            545.0                  453.0   \n", "<EMAIL>                              17.0                  187.0   \n", "ran                                                NaN                  255.0   \n", "<EMAIL>                                 NaN                   84.0   \n", "<EMAIL>                             21.0                  237.0   \n", "<EMAIL>                           100.0                  115.0   \n", "<EMAIL>                              1.0                  261.0   \n", "xiaolei                                            4.0                   33.0   \n", "mark<PERSON>                   39.0   \n", "<EMAIL>                                2.0                   75.0   \n", "edvin                                              1.0                  400.0   \n", "<EMAIL>                              NaN                   62.0   \n", "<EMAIL>                            213.0                  120.0   \n", "<EMAIL>                              NaN                   60.0   \n", "<EMAIL>                               NaN                  576.0   \n", "<EMAIL>                             188.0                  132.0   \n", "<PERSON><PERSON><PERSON><PERSON>                                       142.0                  195.0   \n", "<EMAIL>                               NaN                  119.0   \n", "<EMAIL>                             NaN                  393.0   \n", "<EMAIL>                              NaN                   70.0   \n", "moogi                                            148.0                  223.0   \n", "<EMAIL>                             NaN                  114.0   \n", "<EMAIL>                                NaN                   37.0   \n", "zhuoran                                          781.0                  435.0   \n", "<EMAIL>                              4.0                   34.0   \n", "msdejong                                         297.0                  350.0   \n", "mb                                                 NaN                   41.0   \n", "nikita                                            14.0                   11.0   \n", "zhewei                                            14.0                   18.0   \n", "<EMAIL>                             10.0                   32.0   \n", "<EMAIL>                           102.0                  101.0   \n", "<EMAIL>                              4.0                    1.0   \n", "dietz                                              5.0                    1.0   \n", "carl                                              17.0                    5.0   \n", "<EMAIL>                               NaN                   41.0   \n", "<EMAIL>                               19.0                    8.0   \n", "<EMAIL>                             NaN                    3.0   \n", "<PERSON><PERSON>                   28.0   \n", "<EMAIL>                           NaN                    2.0   \n", "<EMAIL>                               NaN                    NaN   \n", "<PERSON><PERSON>                  130.0   \n", "<EMAIL>                            1.0                    NaN   \n", "<EMAIL>                               2.0                    1.0   \n", "\n", "                          accept_rate  hove_shown_rate  median_distance  \\\n", "user_id                                                                   \n", "<EMAIL>       0.002941         0.600707         0.331238   \n", "<EMAIL>         0.012500         0.526316         0.353512   \n", "mlm                          0.080645         0.574074         0.270635   \n", "colin                        0.107563         0.692666         0.209920   \n", "<EMAIL>       0.115385         0.749280         0.206327   \n", "<EMAIL>       0.125000         0.800000         0.213940   \n", "<EMAIL>      0.125000         0.571429         0.234396   \n", "dirk                         0.140704         0.748120         0.181382   \n", "<EMAIL>        0.174359         0.587349         0.185451   \n", "<EMAIL>      0.177189         0.699430         0.140028   \n", "<EMAIL>     0.181818         1.000000         0.323824   \n", "<EMAIL>        0.248366         0.675497         0.075083   \n", "<EMAIL>         0.250000         0.577540         0.144718   \n", "ran                          0.264706         0.800000         0.107799   \n", "<EMAIL>           0.275862         0.690476         0.043959   \n", "<EMAIL>        0.297521         0.510549         0.196204   \n", "<EMAIL>       0.298701         0.669565         0.040604   \n", "<EMAIL>        0.301676         0.685824         0.025202   \n", "xiaolei                      0.315789         0.575758         0.130024   \n", "markus                       0.317073         1.051282         0.345506   \n", "<EMAIL>          0.342105         1.013333         0.308575   \n", "edvin                        0.350181         0.692500         0.035670   \n", "<EMAIL>        0.365385         0.838710         0.141441   \n", "<EMAIL>        0.390244         0.683333         0.076535   \n", "<EMAIL>        0.416667         0.400000         0.321586   \n", "<EMAIL>         0.456621         0.760417         0.149864   \n", "<EMAIL>         0.457447         0.712121         0.140517   \n", "j<PERSON><PERSON><PERSON>                   0.491124         0.866667         0.237023   \n", "<EMAIL>         0.510204         0.823529         0.226198   \n", "<EMAIL>       0.521851         0.989822         0.350166   \n", "<EMAIL>        0.529412         0.485714         0.305802   \n", "moogi                        0.529954         0.973094         0.341727   \n", "<EMAIL>       0.560440         0.798246         0.260342   \n", "<EMAIL>          0.606061         0.891892         0.343734   \n", "zhuoran                      0.622642         0.731034         0.306611   \n", "<EMAIL>        0.685714         1.029412         0.490547   \n", "msdejong                     0.716216         0.845714         0.422963   \n", "mb                           0.755556         1.097561         0.588016   \n", "nikita                       0.857143         0.636364         0.544512   \n", "zhewei                            NaN         0.222222              NaN   \n", "<EMAIL>             NaN         1.062500              NaN   \n", "<EMAIL>            NaN         0.237624              NaN   \n", "<EMAIL>             NaN         1.000000              NaN   \n", "dietz                             NaN              NaN              NaN   \n", "carl                              NaN         0.600000              NaN   \n", "<EMAIL>              NaN         1.268293              NaN   \n", "<EMAIL>               NaN         0.375000              NaN   \n", "<EMAIL>            NaN         1.666667              NaN   \n", "yuri                              <PERSON>         0.250000              NaN   \n", "<EMAIL>          NaN         1.000000              NaN   \n", "<EMAIL>              NaN              NaN              NaN   \n", "yury                              <PERSON>         0.623077              NaN   \n", "<EMAIL>           NaN              NaN              NaN   \n", "<EMAIL>              NaN              NaN              NaN   \n", "\n", "                             top_language  \n", "user_id                                    \n", "<EMAIL>             svelte  \n", "<EMAIL>               svelte  \n", "mlm                            typescript  \n", "colin                              python  \n", "<EMAIL>             python  \n", "<EMAIL>             python  \n", "<EMAIL>            python  \n", "dirk                               python  \n", "<EMAIL>          typescript  \n", "<EMAIL>            python  \n", "<EMAIL>       javascript  \n", "<EMAIL>                  go  \n", "<EMAIL>               python  \n", "ran                                python  \n", "<EMAIL>              plaintext  \n", "<EMAIL>              python  \n", "<EMAIL>             python  \n", "<EMAIL>              svelte  \n", "xiaolei                            python  \n", "markus                             python  \n", "<EMAIL>                python  \n", "edvin                              python  \n", "<EMAIL>            markdown  \n", "<EMAIL>              python  \n", "<EMAIL>          typescript  \n", "<EMAIL>           typescript  \n", "<EMAIL>               python  \n", "jacqueline                           rust  \n", "<EMAIL>               python  \n", "<EMAIL>                 go  \n", "<EMAIL>     typescriptreact  \n", "moogi                          typescript  \n", "<EMAIL>         typescript  \n", "<EMAIL>                svelte  \n", "zhuoran                            python  \n", "<EMAIL>              python  \n", "msdejong                           python  \n", "mb                                 python  \n", "<PERSON><PERSON><PERSON>                            j<PERSON>  \n", "z<PERSON><PERSON>                            jsonnet  \n", "<EMAIL>                rust  \n", "<EMAIL>               viml  \n", "<EMAIL>            markdown  \n", "dietz                              python  \n", "carl                               python  \n", "<EMAIL>           typescript  \n", "<EMAIL>                python  \n", "<EMAIL>                xml  \n", "yuri                               python  \n", "<EMAIL>           python  \n", "<EMAIL>               python  \n", "yury                               python  \n", "<EMAIL>              rust  \n", "<EMAIL>              jsonnet  "]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["table.join(table_lang).sort_values(\"accept_rate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vanguard"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "\n", "QUERY = \"\"\"\n", "SELECT user_id, ARRAY_AGG(event_name) AS event_name, ARRAY_AGG(count) AS count\n", "FROM (\n", "  SELECT event_name, user_id, COUNT(*) AS count\n", "  FROM (\n", "    SELECT\n", "      DISTINCT *\n", "    FROM (\n", "      WITH\n", "        SESSION AS (\n", "        SELECT\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "          JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "        FROM\n", "          `us_prod_request_insight_analytics_dataset.next_edit_session_event`\n", "        WHERE\n", "          tenant=\"i0-vanguard0\"\n", "          AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") IS NOT NULL\n", "          AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") IN (\"accept\", \"hover-shown\", \"suggestion-hint-shown\", \"reject\")\n", "          AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "        METADATA AS (\n", "        SELECT request_id, user_id, user_agent,\n", "        FROM `us_prod_request_insight_analytics_dataset.request_metadata`\n", "        WHERE\n", "          NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "          AND tenant = 'i0-vanguard0'\n", "          AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\")\n", "      )\n", "      SELECT SESSION.event_name, SESSION.sid, METADATA.user_id,\n", "      FROM SESSION\n", "      JOIN METADATA\n", "      USING (request_id)))\n", "  GROUP BY event_name, user_id)\n", "GROUP BY user_id\n", "\"\"\"\n", "\n", "tenant = tenants.VANGUARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>accept</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>user_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>tUCPSReKP+aHD9pQA7Kk42safvaniukBw0CGcKSl86Y=</th>\n", "      <td>1034</td>\n", "      <td>20.0</td>\n", "      <td>367.0</td>\n", "      <td>11.0</td>\n", "      <td>0.029973</td>\n", "      <td>0.354932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gQAdNt4WqiBphMHlCTsOFJB+sYae39ne1ekmJ/vg6j4=</th>\n", "      <td>173</td>\n", "      <td>1.0</td>\n", "      <td>134.0</td>\n", "      <td>82.0</td>\n", "      <td>0.611940</td>\n", "      <td>0.774566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KfmTc/hg2QDvNi3kPzQhDNxkYAi4j6fsvsiFgqJf2u0=</th>\n", "      <td>307</td>\n", "      <td>18.0</td>\n", "      <td>234.0</td>\n", "      <td>131.0</td>\n", "      <td>0.559829</td>\n", "      <td>0.762215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HGRUe6HJpNuagMS3lB35yvMNqcWg1Xn3shSNg5f4TFQ=</th>\n", "      <td>200</td>\n", "      <td>NaN</td>\n", "      <td>117.0</td>\n", "      <td>17.0</td>\n", "      <td>0.145299</td>\n", "      <td>0.585000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>bgnyja0NkSw6QO4MdAjm3aKtD1XNX5oUE5ojYa29FG0=</th>\n", "      <td>442</td>\n", "      <td>NaN</td>\n", "      <td>168.0</td>\n", "      <td>2.0</td>\n", "      <td>0.011905</td>\n", "      <td>0.380090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qe/60Qpxh5UdIS/TpRjkAPKHqVbMGR6xVK231oV3ffo=</th>\n", "      <td>663</td>\n", "      <td>NaN</td>\n", "      <td>376.0</td>\n", "      <td>1.0</td>\n", "      <td>0.002660</td>\n", "      <td>0.567119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>oW1mB0Asts3RF4vSAxNQ0s05rwjcFIaPREeh3aaEr5I=</th>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>266.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.357527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UWkB5C+k0iZSKECOmgIq0Ptl0pywACgmpXTghfspRZc=</th>\n", "      <td>218</td>\n", "      <td>1.0</td>\n", "      <td>72.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.330275</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qi7dupC0env46NKE+HWbvxBkTOJk0kbWpRXY/YW77pg=</th>\n", "      <td>150</td>\n", "      <td>3.0</td>\n", "      <td>55.0</td>\n", "      <td>1.0</td>\n", "      <td>0.018182</td>\n", "      <td>0.366667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>i25rNow/n9J1yoBvXppQ1JlFPdPpFOen3tyNdfvK/u4=</th>\n", "      <td>162</td>\n", "      <td>1.0</td>\n", "      <td>69.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.425926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cZR3VhkuoU50TvdbsKnwoHAxbNWtn3KEgrZVx9/c8PU=</th>\n", "      <td>122</td>\n", "      <td>NaN</td>\n", "      <td>58.0</td>\n", "      <td>15.0</td>\n", "      <td>0.258621</td>\n", "      <td>0.475410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WByZHduWgVxZGBCsiJ9ScIet8LE9vH+x4ATTgmgPmFU=</th>\n", "      <td>422</td>\n", "      <td>5.0</td>\n", "      <td>264.0</td>\n", "      <td>3.0</td>\n", "      <td>0.011364</td>\n", "      <td>0.625592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>vtZHPvHd9LiMTDycnK+rO/u88uAT6tPtQLtWeQNJGmY=</th>\n", "      <td>213</td>\n", "      <td>NaN</td>\n", "      <td>158.0</td>\n", "      <td>63.0</td>\n", "      <td>0.398734</td>\n", "      <td>0.741784</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jfcYg257mNr53W3s0iWd+qNuwUNrVfgmXi1WlEnfnXo=</th>\n", "      <td>293</td>\n", "      <td>NaN</td>\n", "      <td>63.0</td>\n", "      <td>3.0</td>\n", "      <td>0.047619</td>\n", "      <td>0.215017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cp8dro6jquocyve35dA+kR+K6fepPUVrfYGENmPyMyM=</th>\n", "      <td>743</td>\n", "      <td>NaN</td>\n", "      <td>361.0</td>\n", "      <td>84.0</td>\n", "      <td>0.232687</td>\n", "      <td>0.485868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=</th>\n", "      <td>126</td>\n", "      <td>NaN</td>\n", "      <td>86.0</td>\n", "      <td>31.0</td>\n", "      <td>0.360465</td>\n", "      <td>0.682540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=</th>\n", "      <td>1027</td>\n", "      <td>NaN</td>\n", "      <td>789.0</td>\n", "      <td>183.0</td>\n", "      <td>0.231939</td>\n", "      <td>0.768257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>buVpq/A32gg+HkpHngddKviRIB+79w3uGjWD7se+L1g=</th>\n", "      <td>176</td>\n", "      <td>NaN</td>\n", "      <td>107.0</td>\n", "      <td>7.0</td>\n", "      <td>0.065421</td>\n", "      <td>0.607955</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              suggestion-hint-shown  reject  \\\n", "user_id                                                                       \n", "tUCPSReKP+aHD9pQA7Kk42safvaniukBw0CGcKSl86Y=                   1034    20.0   \n", "gQAdNt4WqiBphMHlCTsOFJB+sYae39ne1ekmJ/vg6j4=                    173     1.0   \n", "KfmTc/hg2QDvNi3kPzQhDNxkYAi4j6fsvsiFgqJf2u0=                    307    18.0   \n", "HGRUe6HJpNuagMS3lB35yvMNqcWg1Xn3shSNg5f4TFQ=                    200     NaN   \n", "bgnyja0NkSw6QO4MdAjm3aKtD1XNX5oUE5ojYa29FG0=                    442     NaN   \n", "Qe/60Qpxh5UdIS/TpRjkAPKHqVbMGR6xVK231oV3ffo=                    663     NaN   \n", "oW1mB0Asts3RF4vSAxNQ0s05rwjcFIaPREeh3aaEr5I=                    744     NaN   \n", "UWkB5C+k0iZSKECOmgIq0Ptl0pywACgmpXTghfspRZc=                    218     1.0   \n", "Qi7dupC0env46NKE+HWbvxBkTOJk0kbWpRXY/YW77pg=                    150     3.0   \n", "i25rNow/n9J1yoBvXppQ1JlFPdPpFOen3tyNdfvK/u4=                    162     1.0   \n", "cZR3VhkuoU50TvdbsKnwoHAxbNWtn3KEgrZVx9/c8PU=                    122     NaN   \n", "WByZHduWgVxZGBCsiJ9ScIet8LE9vH+x4ATTgmgPmFU=                    422     5.0   \n", "vtZHPvHd9LiMTDycnK+rO/u88uAT6tPtQLtWeQNJGmY=                    213     NaN   \n", "jfcYg257mNr53W3s0iWd+qNuwUNrVfgmXi1WlEnfnXo=                    293     NaN   \n", "cp8dro6jquocyve35dA+kR+K6fepPUVrfYGENmPyMyM=                    743     NaN   \n", "SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=                    126     NaN   \n", "z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=                   1027     NaN   \n", "buVpq/A32gg+HkpHngddKviRIB+79w3uGjWD7se+L1g=                    176     NaN   \n", "\n", "                                              hover-shown  accept  \\\n", "user_id                                                             \n", "tUCPSReKP+aHD9pQA7Kk42safvaniukBw0CGcKSl86Y=        367.0    11.0   \n", "gQAdNt4WqiBphMHlCTsOFJB+sYae39ne1ekmJ/vg6j4=        134.0    82.0   \n", "KfmTc/hg2QDvNi3kPzQhDNxkYAi4j6fsvsiFgqJf2u0=        234.0   131.0   \n", "HGRUe6HJpNuagMS3lB35yvMNqcWg1Xn3shSNg5f4TFQ=        117.0    17.0   \n", "bgnyja0NkSw6QO4MdAjm3aKtD1XNX5oUE5ojYa29FG0=        168.0     2.0   \n", "Qe/60Qpxh5UdIS/TpRjkAPKHqVbMGR6xVK231oV3ffo=        376.0     1.0   \n", "oW1mB0Asts3RF4vSAxNQ0s05rwjcFIaPREeh3aaEr5I=        266.0     NaN   \n", "UWkB5C+k0iZSKECOmgIq0Ptl0pywACgmpXTghfspRZc=         72.0     NaN   \n", "Qi7dupC0env46NKE+HWbvxBkTOJk0kbWpRXY/YW77pg=         55.0     1.0   \n", "i25rNow/n9J1yoBvXppQ1JlFPdPpFOen3tyNdfvK/u4=         69.0     NaN   \n", "cZR3VhkuoU50TvdbsKnwoHAxbNWtn3KEgrZVx9/c8PU=         58.0    15.0   \n", "WByZHduWgVxZGBCsiJ9ScIet8LE9vH+x4ATTgmgPmFU=        264.0     3.0   \n", "vtZHPvHd9LiMTDycnK+rO/u88uAT6tPtQLtWeQNJGmY=        158.0    63.0   \n", "jfcYg257mNr53W3s0iWd+qNuwUNrVfgmXi1WlEnfnXo=         63.0     3.0   \n", "cp8dro6jquocyve35dA+kR+K6fepPUVrfYGENmPyMyM=        361.0    84.0   \n", "SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=         86.0    31.0   \n", "z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=        789.0   183.0   \n", "buVpq/A32gg+HkpHngddKviRIB+79w3uGjWD7se+L1g=        107.0     7.0   \n", "\n", "                                              accept_rate  hover_rate  \n", "user_id                                                                \n", "tUCPSReKP+aHD9pQA7Kk42safvaniukBw0CGcKSl86Y=     0.029973    0.354932  \n", "gQAdNt4WqiBphMHlCTsOFJB+sYae39ne1ekmJ/vg6j4=     0.611940    0.774566  \n", "KfmTc/hg2QDvNi3kPzQhDNxkYAi4j6fsvsiFgqJf2u0=     0.559829    0.762215  \n", "HGRUe6HJpNuagMS3lB35yvMNqcWg1Xn3shSNg5f4TFQ=     0.145299    0.585000  \n", "bgnyja0NkSw6QO4MdAjm3aKtD1XNX5oUE5ojYa29FG0=     0.011905    0.380090  \n", "Qe/60Qpxh5UdIS/TpRjkAPKHqVbMGR6xVK231oV3ffo=     0.002660    0.567119  \n", "oW1mB0Asts3RF4vSAxNQ0s05rwjcFIaPREeh3aaEr5I=          NaN    0.357527  \n", "UWkB5C+k0iZSKECOmgIq0Ptl0pywACgmpXTghfspRZc=          NaN    0.330275  \n", "Qi7dupC0env46NKE+HWbvxBkTOJk0kbWpRXY/YW77pg=     0.018182    0.366667  \n", "i25rNow/n9J1yoBvXppQ1JlFPdPpFOen3tyNdfvK/u4=          NaN    0.425926  \n", "cZR3VhkuoU50TvdbsKnwoHAxbNWtn3KEgrZVx9/c8PU=     0.258621    0.475410  \n", "WByZHduWgVxZGBCsiJ9ScIet8LE9vH+x4ATTgmgPmFU=     0.011364    0.625592  \n", "vtZHPvHd9LiMTDycnK+rO/u88uAT6tPtQLtWeQNJGmY=     0.398734    0.741784  \n", "jfcYg257mNr53W3s0iWd+qNuwUNrVfgmXi1WlEnfnXo=     0.047619    0.215017  \n", "cp8dro6jquocyve35dA+kR+K6fepPUVrfYGENmPyMyM=     0.232687    0.485868  \n", "SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=     0.360465    0.682540  \n", "z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=     0.231939    0.768257  \n", "buVpq/A32gg+HkpHngddKviRIB+79w3uGjWD7se+L1g=     0.065421    0.607955  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "\n", "def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row.event_name, row.count)}\n", "    output[\"user_id\"] = row.user_id\n", "    return output\n", "\n", "\n", "df_user_vanguard = pd.DataFrame([row_to_dict(row) for row in data])\n", "df_user_vanguard.set_index(\"user_id\", inplace=True)\n", "df_user_vanguard = df_user_vanguard[df_user_vanguard[\"suggestion-hint-shown\"] > 100]\n", "\n", "# df_user_vanguard = df_user_vanguard.sum(axis=0)\n", "\n", "df_user_vanguard[\"accept_rate\"] = df_user_vanguard[\"accept\"] / df_user_vanguard[\"hover-shown\"]\n", "df_user_vanguard[\"hover_rate\"] = df_user_vanguard[\"hover-shown\"] / df_user_vanguard[\"suggestion-hint-shown\"]\n", "df_user_vanguard\n", "# df_user_vanguard.sort_values(\"hove_shown_rate\")"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.13408007080451623\n"]}], "source": ["import pandas as pd\n", "\n", "\n", "def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row[1], row[2])}\n", "    output[\"user_id\"] = row.user_id\n", "    # output[\"user_id\"] = hashlib.sha256(row.user_id.encode('utf-8')).hexdigest()[:12]\n", "    return output\n", "\n", "\n", "table = pd.DataFrame([row_to_dict(row) for row in data])\n", "table.set_index(\"user_id\", inplace=True)\n", "table = table[table[\"suggestion-offset-text-shown\"] > 100]\n", "table[\"accept_rate\"] = table[\"accept\"] / table[\"hover-shown\"]\n", "table[\"hove_shown_rate\"] = table[\"hover-shown\"] / table[\"suggestion-offset-text-shown\"]\n", "table[\"median_distance\"] = np.sqrt(\n", "    (table[\"accept_rate\"] - table[\"accept_rate\"].median()) ** 2\n", "    + (table[\"hove_shown_rate\"] - table[\"hove_shown_rate\"].median()) ** 2\n", ")\n", "print(table[\"median_distance\"].std())\n", "table = table[\n", "    [\n", "        \"accept\",\n", "        \"reject\",\n", "        \"hover-shown\",\n", "        \"suggestion-offset-text-shown\",\n", "        \"suggestion-hint-shown\",\n", "        \"accept_rate\",\n", "        \"hove_shown_rate\",\n", "        \"median_distance\",\n", "    ]\n", "]\n", "# table.sort_values(\"median_distance\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT\n", "  user_id,\n", "  ARRAY_AGG(LANGUAGE ORDER BY count DESC LIMIT 1)[OFFSET(0)] AS top_language,\n", "FROM (\n", "  SELECT\n", "    user_id,\n", "    LANGUAGE,\n", "    COUNT(*) AS count\n", "  FROM (\n", "    WITH\n", "      REQUEST AS (\n", "      SELECT\n", "        request_id,\n", "        COALESCE(JSON_EXTRACT_SCALAR(raw_json, \"$.request.lang\"), \"null\") AS LANGUAGE,\n", "      FROM\n", "        `prod_request_insight_full_export_dataset.next_edit_host_request`\n", "      WHERE\n", "      tenant=\"i0-vanguard0\"\n", "      AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) \n", "        ),\n", "      METADATA AS (\n", "      SELECT\n", "        request_id,\n", "        JSON_VALUE(raw_json, \"$.user_id\") AS user_id,\n", "        JSON_VALUE(raw_json, \"$.user_agent\") AS user_agent,\n", "        CAST(REGEXP_EXTRACT(JSON_VALUE(raw_json, \"$.user_agent\"), \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) AS version\n", "      FROM\n", "        `prod_request_insight_full_export_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'AugmentHealthCheck')\n", "        AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'augment_review_bot')\n", "        AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'Augment-EvalHarness')\n", "        AND tenant=\"i0-vanguard0\"\n", "        )\n", "    SELECT\n", "      REQUEST.language,\n", "      METADATA.user_id\n", "    FROM\n", "      REQUEST\n", "    JOIN\n", "      METADATA\n", "    USING\n", "      (request_id))\n", "  GROUP BY\n", "    user_id,\n", "    LANGUAGE)\n", "GROUP BY\n", "  user_id\n", "\"\"\"\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)\n", "\n", "table_lang = pd.DataFrame([dict(d.items()) for d in data])\n", "table_lang.set_index(\"user_id\", inplace=True)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-offset-text-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hove_shown_rate</th>\n", "      <th>median_distance</th>\n", "      <th>top_language</th>\n", "    </tr>\n", "    <tr>\n", "      <th>user_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>122.0</td>\n", "      <td>342.0</td>\n", "      <td>207.0</td>\n", "      <td>0.008197</td>\n", "      <td>0.356725</td>\n", "      <td>0.119686</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>103.0</td>\n", "      <td>364.0</td>\n", "      <td>247.0</td>\n", "      <td>0.009709</td>\n", "      <td>0.282967</td>\n", "      <td>0.135386</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>136.0</td>\n", "      <td>470.0</td>\n", "      <td>176.0</td>\n", "      <td>0.029412</td>\n", "      <td>0.289362</td>\n", "      <td>0.115154</td>\n", "      <td>rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>37.0</td>\n", "      <td>9.0</td>\n", "      <td>535.0</td>\n", "      <td>1596.0</td>\n", "      <td>743.0</td>\n", "      <td>0.069159</td>\n", "      <td>0.335213</td>\n", "      <td>0.060203</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>408.0</td>\n", "      <td>1655.0</td>\n", "      <td>1059.0</td>\n", "      <td>0.071078</td>\n", "      <td>0.246526</td>\n", "      <td>0.117431</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>14.0</td>\n", "      <td>NaN</td>\n", "      <td>190.0</td>\n", "      <td>555.0</td>\n", "      <td>304.0</td>\n", "      <td>0.073684</td>\n", "      <td>0.342342</td>\n", "      <td>0.054438</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>15.0</td>\n", "      <td>NaN</td>\n", "      <td>196.0</td>\n", "      <td>511.0</td>\n", "      <td>288.0</td>\n", "      <td>0.076531</td>\n", "      <td>0.383562</td>\n", "      <td>0.061477</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>8.0</td>\n", "      <td>NaN</td>\n", "      <td>91.0</td>\n", "      <td>811.0</td>\n", "      <td>637.0</td>\n", "      <td>0.087912</td>\n", "      <td>0.112207</td>\n", "      <td>0.240526</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>25.0</td>\n", "      <td>1.0</td>\n", "      <td>208.0</td>\n", "      <td>632.0</td>\n", "      <td>318.0</td>\n", "      <td>0.120192</td>\n", "      <td>0.329114</td>\n", "      <td>0.021642</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jyo<PERSON><PERSON><PERSON>@type.ai</th>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>47.0</td>\n", "      <td>536.0</td>\n", "      <td>194.0</td>\n", "      <td>0.127660</td>\n", "      <td>0.087687</td>\n", "      <td>0.261740</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>11.0</td>\n", "      <td>NaN</td>\n", "      <td>81.0</td>\n", "      <td>136.0</td>\n", "      <td>114.0</td>\n", "      <td>0.135802</td>\n", "      <td>0.595588</td>\n", "      <td>0.246296</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>58.0</td>\n", "      <td>NaN</td>\n", "      <td>407.0</td>\n", "      <td>1027.0</td>\n", "      <td>554.0</td>\n", "      <td>0.142506</td>\n", "      <td>0.396300</td>\n", "      <td>0.049168</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>34.0</td>\n", "      <td>1.0</td>\n", "      <td>214.0</td>\n", "      <td>553.0</td>\n", "      <td>337.0</td>\n", "      <td>0.158879</td>\n", "      <td>0.386980</td>\n", "      <td>0.048835</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>18.0</td>\n", "      <td>NaN</td>\n", "      <td>113.0</td>\n", "      <td>206.0</td>\n", "      <td>198.0</td>\n", "      <td>0.159292</td>\n", "      <td>0.548544</td>\n", "      <td>0.201614</td>\n", "      <td>typescript</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>15.0</td>\n", "      <td>5.0</td>\n", "      <td>91.0</td>\n", "      <td>331.0</td>\n", "      <td>169.0</td>\n", "      <td>0.164835</td>\n", "      <td>0.274924</td>\n", "      <td>0.083262</td>\n", "      <td>rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>28.0</td>\n", "      <td>NaN</td>\n", "      <td>151.0</td>\n", "      <td>321.0</td>\n", "      <td>189.0</td>\n", "      <td>0.185430</td>\n", "      <td>0.470405</td>\n", "      <td>0.134064</td>\n", "      <td>c</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>55.0</td>\n", "      <td>111.0</td>\n", "      <td>72.0</td>\n", "      <td>0.363636</td>\n", "      <td>0.495495</td>\n", "      <td>0.277527</td>\n", "      <td>javascriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>63.0</td>\n", "      <td>10.0</td>\n", "      <td>151.0</td>\n", "      <td>329.0</td>\n", "      <td>229.0</td>\n", "      <td>0.417219</td>\n", "      <td>0.458967</td>\n", "      <td>0.309586</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jaredv<PERSON><EMAIL></th>\n", "      <td>47.0</td>\n", "      <td>3.0</td>\n", "      <td>69.0</td>\n", "      <td>134.0</td>\n", "      <td>89.0</td>\n", "      <td>0.681159</td>\n", "      <td>0.514925</td>\n", "      <td>0.577713</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>391.0</td>\n", "      <td>1285.0</td>\n", "      <td>828.0</td>\n", "      <td>NaN</td>\n", "      <td>0.304280</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>282.0</td>\n", "      <td>791.0</td>\n", "      <td>435.0</td>\n", "      <td>NaN</td>\n", "      <td>0.356511</td>\n", "      <td>NaN</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>91.0</td>\n", "      <td>135.0</td>\n", "      <td>143.0</td>\n", "      <td>NaN</td>\n", "      <td>0.674074</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mi<PERSON><PERSON>@exposit.ai</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24.0</td>\n", "      <td>105.0</td>\n", "      <td>103.0</td>\n", "      <td>NaN</td>\n", "      <td>0.228571</td>\n", "      <td>NaN</td>\n", "      <td>typescriptreact</td>\n", "    </tr>\n", "    <tr>\n", "      <th><EMAIL></th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>43.0</td>\n", "      <td>146.0</td>\n", "      <td>61.0</td>\n", "      <td>NaN</td>\n", "      <td>0.294521</td>\n", "      <td>NaN</td>\n", "      <td>python</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          accept  reject  hover-shown  \\\n", "user_id                                                 \n", "<EMAIL>      1.0     NaN        122.0   \n", "<EMAIL>              1.0     NaN        103.0   \n", "<EMAIL>              4.0     NaN        136.0   \n", "<EMAIL>       37.0     9.0        535.0   \n", "<EMAIL>            29.0     1.0        408.0   \n", "<EMAIL>            14.0     NaN        190.0   \n", "<EMAIL>        15.0     NaN        196.0   \n", "<EMAIL>              8.0     NaN         91.0   \n", "<EMAIL>             25.0     1.0        208.0   \n", "jyo<PERSON><PERSON><PERSON>@type.ai            6.0     NaN         47.0   \n", "<EMAIL>    11.0     NaN         81.0   \n", "<EMAIL>           58.0     NaN        407.0   \n", "<EMAIL>     34.0     1.0        214.0   \n", "<EMAIL>              18.0     NaN        113.0   \n", "<EMAIL>          15.0     5.0         91.0   \n", "<EMAIL>       28.0     NaN        151.0   \n", "<EMAIL>        20.0     NaN         55.0   \n", "<EMAIL>         63.0    10.0        151.0   \n", "<EMAIL>    47.0     3.0         69.0   \n", "<EMAIL>           NaN     1.0        391.0   \n", "<EMAIL>            NaN     NaN        282.0   \n", "<EMAIL>           NaN     1.0         91.0   \n", "micha<PERSON>@exposit.ai           NaN     NaN         24.0   \n", "<EMAIL>           NaN     NaN         43.0   \n", "\n", "                          suggestion-offset-text-shown  suggestion-hint-shown  \\\n", "user_id                                                                         \n", "<EMAIL>                          342.0                  207.0   \n", "<EMAIL>                                  364.0                  247.0   \n", "<EMAIL>                                  470.0                  176.0   \n", "<EMAIL>                           1596.0                  743.0   \n", "<EMAIL>                                1655.0                 1059.0   \n", "<EMAIL>                                 555.0                  304.0   \n", "<EMAIL>                             511.0                  288.0   \n", "<EMAIL>                                  811.0                  637.0   \n", "<EMAIL>                                  632.0                  318.0   \n", "jyo<PERSON><PERSON><PERSON>@type.ai                                536.0                  194.0   \n", "<EMAIL>                         136.0                  114.0   \n", "<EMAIL>                               1027.0                  554.0   \n", "<EMAIL>                          553.0                  337.0   \n", "<EMAIL>                                   206.0                  198.0   \n", "<EMAIL>                               331.0                  169.0   \n", "<EMAIL>                            321.0                  189.0   \n", "<EMAIL>                             111.0                   72.0   \n", "<EMAIL>                              329.0                  229.0   \n", "<EMAIL>                         134.0                   89.0   \n", "<EMAIL>                              1285.0                  828.0   \n", "<EMAIL>                                791.0                  435.0   \n", "<EMAIL>                               135.0                  143.0   \n", "mi<PERSON><PERSON>@exposit.ai                               105.0                  103.0   \n", "<EMAIL>                               146.0                   61.0   \n", "\n", "                          accept_rate  hove_shown_rate  median_distance  \\\n", "user_id                                                                   \n", "<EMAIL>      0.008197         0.356725         0.119686   \n", "<EMAIL>              0.009709         0.282967         0.135386   \n", "<EMAIL>              0.029412         0.289362         0.115154   \n", "<EMAIL>        0.069159         0.335213         0.060203   \n", "<EMAIL>             0.071078         0.246526         0.117431   \n", "<EMAIL>             0.073684         0.342342         0.054438   \n", "<EMAIL>         0.076531         0.383562         0.061477   \n", "<EMAIL>              0.087912         0.112207         0.240526   \n", "<EMAIL>              0.120192         0.329114         0.021642   \n", "jyo<PERSON><PERSON><PERSON>@type.ai            0.127660         0.087687         0.261740   \n", "<EMAIL>     0.135802         0.595588         0.246296   \n", "<EMAIL>            0.142506         0.396300         0.049168   \n", "<EMAIL>      0.158879         0.386980         0.048835   \n", "<EMAIL>               0.159292         0.548544         0.201614   \n", "<EMAIL>           0.164835         0.274924         0.083262   \n", "<EMAIL>        0.185430         0.470405         0.134064   \n", "<EMAIL>         0.363636         0.495495         0.277527   \n", "<EMAIL>          0.417219         0.458967         0.309586   \n", "<EMAIL>     0.681159         0.514925         0.577713   \n", "<EMAIL>                NaN         0.304280              NaN   \n", "<EMAIL>                 NaN         0.356511              NaN   \n", "<EMAIL>                NaN         0.674074              NaN   \n", "micha<PERSON>@exposit.ai                NaN         0.228571              NaN   \n", "<EMAIL>                NaN         0.294521              NaN   \n", "\n", "                             top_language  \n", "user_id                                    \n", "<EMAIL>            python  \n", "<EMAIL>           typescriptreact  \n", "<EMAIL>                      rust  \n", "<EMAIL>          typescript  \n", "<EMAIL>          typescriptreact  \n", "<EMAIL>                   python  \n", "<EMAIL>           typescript  \n", "<EMAIL>                    python  \n", "<EMAIL>           typescriptreact  \n", "jyo<PERSON><PERSON><PERSON>@type.ai              typescript  \n", "<EMAIL>           python  \n", "<EMAIL>              typescript  \n", "<EMAIL>            python  \n", "<EMAIL>                 typescript  \n", "<EMAIL>                   rust  \n", "<EMAIL>                   c  \n", "<EMAIL>      javascriptreact  \n", "<EMAIL>                python  \n", "<EMAIL>  typescriptreact  \n", "<EMAIL>                 python  \n", "<EMAIL>         typescriptreact  \n", "<EMAIL>                 python  \n", "micha<PERSON>@exposit.ai        typescriptreact  \n", "<EMAIL>                 python  "]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["table.join(table_lang).sort_values(\"accept_rate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wave 1"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "\n", "QUERY = \"\"\"\n", "SELECT session_id, tenant, user_agent, ARRAY_AGG(event_name) AS event_name, ARRAY_AGG(count) AS count\n", "FROM (\n", "  SELECT event_name, session_id, tenant, user_agent, COUNT(DISTINCT sid) AS count\n", "  FROM (\n", "    WITH\n", "      SESSION AS (\n", "      SELECT\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "      FROM\n", "        `us_prod_request_insight_analytics_dataset.next_edit_session_event`\n", "      WHERE\n", "        tenant IN (\"aitutor-mercor\", \"aitutor-turing\", \"ampsortation\", \"collective\", \"montecarlodata\", \"observeinc\", \"reveart\", \"webflow\")\n", "        AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") IS NOT NULL\n", "        AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") IN (\"accept\", \"hover-shown\", \"suggestion-hint-shown\", \"reject\")\n", "        AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "      METADATA AS (\n", "      SELECT request_id, user_id, session_id, user_agent, tenant\n", "      FROM `us_prod_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant IN (\"aitutor-mercor\", \"aitutor-turing\", \"ampsortation\", \"collective\", \"montecarlodata\", \"observeinc\", \"reveart\", \"webflow\")\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\")\n", "      )\n", "    SELECT SESSION.event_name, METADATA.session_id, METADATA.tenant, METADATA.user_agent, SESSION.sid\n", "    FROM SESSION\n", "    JOIN METADATA\n", "    USING (request_id)\n", "  )\n", "  GROUP BY event_name, session_id, tenant, user_agent)\n", "GROUP BY session_id, tenant, user_agent\n", "\"\"\"\n", "\n", "tenant = tenants.VANGUARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>session_id</th>\n", "      <th>tenant</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5f6583a2-29a0-48df-a713-f4a1559151f3</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>101.0</td>\n", "      <td>554</td>\n", "      <td>0.000000</td>\n", "      <td>0.182310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8dfdfaca-4f02-45db-947e-bd4f300198f0</th>\n", "      <th>aitutor-turing</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>152.0</td>\n", "      <td>334</td>\n", "      <td>0.006579</td>\n", "      <td>0.455090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12643ca8-115c-4e0c-bd2b-389b3e742a3c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>52.0</td>\n", "      <td>143</td>\n", "      <td>0.019231</td>\n", "      <td>0.363636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7b11013b-a604-47eb-9e0c-a4ac0d062661</th>\n", "      <th>aitutor-turing</th>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>296.0</td>\n", "      <td>1278</td>\n", "      <td>0.020270</td>\n", "      <td>0.231612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>77.0</td>\n", "      <td>157</td>\n", "      <td>0.051948</td>\n", "      <td>0.490446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c1bb7492-5fca-4c85-84ff-9cb7bc2a209c</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>57.0</td>\n", "      <td>0.0</td>\n", "      <td>174.0</td>\n", "      <td>324</td>\n", "      <td>0.327586</td>\n", "      <td>0.537037</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a39a2e34-a02d-4b28-b9a3-fe303700c680</th>\n", "      <th>aitutor-turing</th>\n", "      <td>35.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>138</td>\n", "      <td>0.466667</td>\n", "      <td>0.543478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9130182f-9cfd-4d98-935a-7533fc45877c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>96.0</td>\n", "      <td>0.0</td>\n", "      <td>202.0</td>\n", "      <td>324</td>\n", "      <td>0.475248</td>\n", "      <td>0.623457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e5983c8b-880c-4e68-a3cf-e12f349e9365</th>\n", "      <th>aitutor-turing</th>\n", "      <td>146.0</td>\n", "      <td>57.0</td>\n", "      <td>169.0</td>\n", "      <td>182</td>\n", "      <td>0.863905</td>\n", "      <td>0.928571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0</th>\n", "      <th>aitutor-turing</th>\n", "      <td>95.0</td>\n", "      <td>44.0</td>\n", "      <td>100.0</td>\n", "      <td>141</td>\n", "      <td>0.950000</td>\n", "      <td>0.709220</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                     accept  reject  \\\n", "session_id                           tenant                           \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor     0.0     0.0   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing     1.0     0.0   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing     1.0     0.0   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing     6.0     0.0   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor     4.0     0.0   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor    57.0     0.0   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing    35.0     0.0   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing    96.0     0.0   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing   146.0    57.0   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing    95.0    44.0   \n", "\n", "                                                     hover-shown  \\\n", "session_id                           tenant                        \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor        101.0   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing        152.0   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing         52.0   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing        296.0   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor         77.0   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor        174.0   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing         75.0   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing        202.0   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing        169.0   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing        100.0   \n", "\n", "                                                     suggestion-hint-shown  \\\n", "session_id                           tenant                                  \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor                    554   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing                    334   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing                    143   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing                   1278   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor                    157   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor                    324   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing                    138   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing                    324   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing                    182   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing                    141   \n", "\n", "                                                     accept_rate  hover_rate  \n", "session_id                           tenant                                   \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor     0.000000    0.182310  \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing     0.006579    0.455090  \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing     0.019231    0.363636  \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing     0.020270    0.231612  \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor     0.051948    0.490446  \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor     0.327586    0.537037  \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing     0.466667    0.543478  \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing     0.475248    0.623457  \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing     0.863905    0.928571  \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing     0.950000    0.709220  "]}, "execution_count": 176, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "\n", "\n", "def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row.event_name, row.count)}\n", "    output[\"session_id\"] = row.session_id\n", "    output[\"tenant\"] = row.tenant\n", "    output[\"user_agent\"] = row.user_agent\n", "    return output\n", "\n", "\n", "df_user_wave1 = pd.DataFrame([row_to_dict(row) for row in data]).fillna(0)[\n", "    [\n", "        \"session_id\",\n", "        \"tenant\",\n", "        \"accept\",\n", "        \"reject\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "        # \"client\",\n", "        # \"user_agent\"\n", "    ]\n", "]\n", "# df_user_wave1['client'] = df_user_wave1['user_agent'].apply(lambda x: x.split(' ')[0])\n", "# df_user_wave1.set_index([\"session_id\", \"tenant\"], inplace=True)\n", "# df_user_vanguard = df_user_vanguard.sum(axis=0)\n", "df_user_wave1 = df_user_wave1.groupby([\"session_id\", \"tenant\"]).sum()\n", "\n", "\n", "df_user_wave1[\"accept_rate\"] = df_user_wave1[\"accept\"] / df_user_wave1[\"hover-shown\"]\n", "df_user_wave1[\"hover_rate\"] = (\n", "    df_user_wave1[\"hover-shown\"] / df_user_wave1[\"suggestion-hint-shown\"]\n", ")\n", "# df_user_wave1\n", "df_user_wave1[\n", "    (df_user_wave1[\"suggestion-hint-shown\"] > 100)\n", "    & (\n", "        df_user_wave1.index.get_level_values(\"tenant\").isin(\n", "            [\"aitutor-mercor\", \"aitutor-turing\"]\n", "        )\n", "    )\n", "].fillna(0).sort_values(\n", "    [\n", "        \"accept_rate\",\n", "    ],\n", "    ascending=[\n", "        True,\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>session_id</th>\n", "      <th>tenant</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>00d6ff68-99f0-4365-b817-2f9fb47715df</th>\n", "      <th>collective</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>108.0</td>\n", "      <td>142</td>\n", "      <td>0.000000</td>\n", "      <td>0.760563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>010a135a-fcd3-49ab-85d7-3b85e3c2d8d7</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.0</td>\n", "      <td>20</td>\n", "      <td>0.000000</td>\n", "      <td>0.450000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>013a033a-45bb-4f4b-bcba-112beede877c</th>\n", "      <th>observeinc</th>\n", "      <td>98.0</td>\n", "      <td>4.0</td>\n", "      <td>256.0</td>\n", "      <td>395</td>\n", "      <td>0.382812</td>\n", "      <td>0.648101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>01ea3250-0039-4c69-b635-f129f7c87950</th>\n", "      <th>observeinc</th>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>163.0</td>\n", "      <td>394</td>\n", "      <td>0.042945</td>\n", "      <td>0.413706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>085468cc-ff24-4d14-ae24-209332a9d6a1</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0a8055ee-eb84-40f1-8104-a6d16fd6158d</th>\n", "      <th>observeinc</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>112.0</td>\n", "      <td>211</td>\n", "      <td>0.008929</td>\n", "      <td>0.530806</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0c2cbb2a-8ab5-49fb-8ada-8d17134080ed</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>15.0</td>\n", "      <td>28</td>\n", "      <td>0.000000</td>\n", "      <td>0.535714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0c485ae0-05b4-4e76-ae81-3a276b6dfa87</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>23.0</td>\n", "      <td>61</td>\n", "      <td>0.000000</td>\n", "      <td>0.377049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10258db1-9e2e-4c55-9870-bf2025a70305</th>\n", "      <th>ampsortation</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>87.0</td>\n", "      <td>105</td>\n", "      <td>0.011494</td>\n", "      <td>0.828571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103820d0-4a25-4587-9282-2e38b98c02ea</th>\n", "      <th>reveart</th>\n", "      <td>64.0</td>\n", "      <td>8.0</td>\n", "      <td>266.0</td>\n", "      <td>627</td>\n", "      <td>0.240602</td>\n", "      <td>0.424242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10cfc0d7-72e8-4b59-b15c-6039cff38c8b</th>\n", "      <th>webflow</th>\n", "      <td>151.0</td>\n", "      <td>5.0</td>\n", "      <td>538.0</td>\n", "      <td>915</td>\n", "      <td>0.280669</td>\n", "      <td>0.587978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11334beb-823e-4f84-8f61-8435f4afaaf4</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>9</td>\n", "      <td>0.000000</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12643ca8-115c-4e0c-bd2b-389b3e742a3c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>52.0</td>\n", "      <td>143</td>\n", "      <td>0.019231</td>\n", "      <td>0.363636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134d006a-9cb9-4769-9c4b-612d77cc5f91</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140bbbea-5b07-4e58-8b11-23915a201f90</th>\n", "      <th>observeinc</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17d6d858-abac-43de-8d3a-ff3d6b8b5c79</th>\n", "      <th>observeinc</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>142.0</td>\n", "      <td>313</td>\n", "      <td>0.035211</td>\n", "      <td>0.453674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1a548214-62cd-459c-b820-358173b3af10</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>3</td>\n", "      <td>0.000000</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1ad37a32-d9dd-43b3-96a3-bae4d07e36cf</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1cc5b754-7411-4486-b8c5-2a26c3671bb0</th>\n", "      <th>collective</th>\n", "      <td>22.0</td>\n", "      <td>3.0</td>\n", "      <td>63.0</td>\n", "      <td>168</td>\n", "      <td>0.349206</td>\n", "      <td>0.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20454f08-755a-4484-b203-b66092376375</th>\n", "      <th>ampsortation</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>83.0</td>\n", "      <td>288</td>\n", "      <td>0.060241</td>\n", "      <td>0.288194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21653165-5a71-46d6-b2c8-4757206d236a</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18.0</td>\n", "      <td>38</td>\n", "      <td>0.000000</td>\n", "      <td>0.473684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23852206-5b56-477f-b50d-44acfe195ce1</th>\n", "      <th>webflow</th>\n", "      <td>57.0</td>\n", "      <td>4.0</td>\n", "      <td>536.0</td>\n", "      <td>1181</td>\n", "      <td>0.106343</td>\n", "      <td>0.453853</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2463c696-9213-485b-91aa-a5a73782fba0</th>\n", "      <th>ampsortation</th>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>84.0</td>\n", "      <td>105</td>\n", "      <td>0.071429</td>\n", "      <td>0.800000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25f21a16-c0cd-4559-9f47-383746508eb2</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26dfc75f-18b5-4e5a-81d2-62a2c8ac4537</th>\n", "      <th>observeinc</th>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>110.0</td>\n", "      <td>605</td>\n", "      <td>0.009091</td>\n", "      <td>0.181818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2af3ff2e-2ff7-4c5e-92cc-ae1892cb43ed</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>258.0</td>\n", "      <td>1072</td>\n", "      <td>0.000000</td>\n", "      <td>0.240672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2c7faade-5a26-4168-a804-92abe32cf58a</th>\n", "      <th>collective</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>348.0</td>\n", "      <td>681</td>\n", "      <td>0.008621</td>\n", "      <td>0.511013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2eb6183f-0b28-4761-b410-d441b1e7a7b9</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>14.0</td>\n", "      <td>13</td>\n", "      <td>0.000000</td>\n", "      <td>1.076923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2f04b8d1-71fe-4208-996f-e670dd7dc22c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3575a9db-2b27-4bbe-b036-3ab93cacf358</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>80.0</td>\n", "      <td>150</td>\n", "      <td>0.000000</td>\n", "      <td>0.533333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35b70283-3e9b-4be6-89d2-1750cf22d1e6</th>\n", "      <th>webflow</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>43.0</td>\n", "      <td>80</td>\n", "      <td>0.116279</td>\n", "      <td>0.537500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38a2307c-2aec-47c1-83bf-c641ee696ba3</th>\n", "      <th>ampsortation</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>82.0</td>\n", "      <td>191</td>\n", "      <td>0.000000</td>\n", "      <td>0.429319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38ea880e-3a8a-42b6-825d-3fac712689e6</th>\n", "      <th>webflow</th>\n", "      <td>68.0</td>\n", "      <td>1.0</td>\n", "      <td>149.0</td>\n", "      <td>216</td>\n", "      <td>0.456376</td>\n", "      <td>0.689815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3b2db685-968b-4b38-8f4a-244cdec1aa55</th>\n", "      <th>webflow</th>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>272.0</td>\n", "      <td>515</td>\n", "      <td>0.040441</td>\n", "      <td>0.528155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3bf90d00-e133-4a38-a95b-419cc581fbdc</th>\n", "      <th>montecarlodata</th>\n", "      <td>44.0</td>\n", "      <td>6.0</td>\n", "      <td>101.0</td>\n", "      <td>140</td>\n", "      <td>0.435644</td>\n", "      <td>0.721429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3d73081a-1303-4c3a-a22f-06f711e41d16</th>\n", "      <th>webflow</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>8.0</td>\n", "      <td>12</td>\n", "      <td>0.500000</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3f84aaaf-b22c-481b-b420-fbe5a1218701</th>\n", "      <th>webflow</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>39.0</td>\n", "      <td>113</td>\n", "      <td>0.076923</td>\n", "      <td>0.345133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>446301dc-2723-42c6-b098-6ef4b77a3f53</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>15</td>\n", "      <td>0.000000</td>\n", "      <td>0.733333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46dfed66-c74d-46b8-b8cd-2917ca44b508</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>18</td>\n", "      <td>0.400000</td>\n", "      <td>0.555556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4a3a949e-1e3d-452f-b413-a4de573ffe18</th>\n", "      <th>webflow</th>\n", "      <td>66.0</td>\n", "      <td>2.0</td>\n", "      <td>358.0</td>\n", "      <td>481</td>\n", "      <td>0.184358</td>\n", "      <td>0.744283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4b2995b6-637b-4331-9b71-ee11490e3dac</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.0</td>\n", "      <td>17</td>\n", "      <td>0.000000</td>\n", "      <td>0.470588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4e00679d-9829-4e93-9173-e38136ac5b69</th>\n", "      <th>ampsortation</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>24.0</td>\n", "      <td>104</td>\n", "      <td>0.083333</td>\n", "      <td>0.230769</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5001416d-9f16-4e8e-a824-8c725dbec307</th>\n", "      <th>webflow</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>80.0</td>\n", "      <td>116</td>\n", "      <td>0.037500</td>\n", "      <td>0.689655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5017b0fd-26e1-42f8-8fff-f389a21f0a02</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18.0</td>\n", "      <td>47</td>\n", "      <td>0.000000</td>\n", "      <td>0.382979</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5232f278-141c-4ab2-8498-fb5fe155cf7a</th>\n", "      <th>observeinc</th>\n", "      <td>66.0</td>\n", "      <td>9.0</td>\n", "      <td>306.0</td>\n", "      <td>651</td>\n", "      <td>0.215686</td>\n", "      <td>0.470046</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52bfa022-9e00-40a0-bb70-aba39d2b0967</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>27.0</td>\n", "      <td>15</td>\n", "      <td>0.000000</td>\n", "      <td>1.800000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5aac6afa-7fc8-4792-9b41-b2731cf940b7</th>\n", "      <th>reveart</th>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>134.0</td>\n", "      <td>222</td>\n", "      <td>0.029851</td>\n", "      <td>0.603604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5f6583a2-29a0-48df-a713-f4a1559151f3</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>101.0</td>\n", "      <td>554</td>\n", "      <td>0.000000</td>\n", "      <td>0.182310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5fbb952d-28ce-4516-a69f-64f7ab9c0014</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>26</td>\n", "      <td>0.000000</td>\n", "      <td>0.192308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>638e1453-e267-43d0-b7d4-7ebe00881570</th>\n", "      <th>observeinc</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>7</td>\n", "      <td>1.000000</td>\n", "      <td>0.571429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64eb51d8-18b0-4ab6-9714-d9c7435def20</th>\n", "      <th>webflow</th>\n", "      <td>9.0</td>\n", "      <td>2.0</td>\n", "      <td>102.0</td>\n", "      <td>204</td>\n", "      <td>0.088235</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6819915b-a8f9-414e-a874-9e6c093a3d24</th>\n", "      <th>reveart</th>\n", "      <td>55.0</td>\n", "      <td>3.0</td>\n", "      <td>217.0</td>\n", "      <td>511</td>\n", "      <td>0.253456</td>\n", "      <td>0.424658</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6d7e015c-4d7c-4f6f-be97-f46ed03fff05</th>\n", "      <th>observeinc</th>\n", "      <td>33.0</td>\n", "      <td>0.0</td>\n", "      <td>104.0</td>\n", "      <td>154</td>\n", "      <td>0.317308</td>\n", "      <td>0.675325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6e2b7743-2f2d-421c-9cc1-508378baff3c</th>\n", "      <th>collective</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>39.0</td>\n", "      <td>91</td>\n", "      <td>0.000000</td>\n", "      <td>0.428571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7022323c-90fa-4c46-819d-bc29f774deb8</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>14.0</td>\n", "      <td>16</td>\n", "      <td>0.000000</td>\n", "      <td>0.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7429eb40-e11d-4c2a-85ea-355a72befab4</th>\n", "      <th>webflow</th>\n", "      <td>17.0</td>\n", "      <td>0.0</td>\n", "      <td>63.0</td>\n", "      <td>501</td>\n", "      <td>0.269841</td>\n", "      <td>0.125749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745a36cc-5465-43b5-9a7d-c79588954df2</th>\n", "      <th>webflow</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>226.0</td>\n", "      <td>328</td>\n", "      <td>0.004425</td>\n", "      <td>0.689024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7574f7c8-7969-4561-8c0e-ace59dade919</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>8.0</td>\n", "      <td>6</td>\n", "      <td>0.000000</td>\n", "      <td>1.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75f4c4c9-46c7-4f5b-ac5a-3f3bd76aa49d</th>\n", "      <th>ampsortation</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>107.0</td>\n", "      <td>241</td>\n", "      <td>0.046729</td>\n", "      <td>0.443983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7771af49-7736-4db8-85a5-ab9f34b6ba16</th>\n", "      <th>webflow</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>35.0</td>\n", "      <td>62</td>\n", "      <td>0.028571</td>\n", "      <td>0.564516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78febf34-1e81-4803-b206-7eea0ff7f477</th>\n", "      <th>reveart</th>\n", "      <td>42.0</td>\n", "      <td>1.0</td>\n", "      <td>297.0</td>\n", "      <td>580</td>\n", "      <td>0.141414</td>\n", "      <td>0.512069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7a7b3326-8753-466d-a7be-3415e1833ece</th>\n", "      <th>reveart</th>\n", "      <td>44.0</td>\n", "      <td>0.0</td>\n", "      <td>187.0</td>\n", "      <td>320</td>\n", "      <td>0.235294</td>\n", "      <td>0.584375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7b11013b-a604-47eb-9e0c-a4ac0d062661</th>\n", "      <th>aitutor-turing</th>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>296.0</td>\n", "      <td>1278</td>\n", "      <td>0.020270</td>\n", "      <td>0.231612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7f4d97bd-2713-4e1f-8bfb-ea961f387c57</th>\n", "      <th>collective</th>\n", "      <td>8.0</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>65</td>\n", "      <td>0.400000</td>\n", "      <td>0.307692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8005e319-9983-4307-ae2e-4b07e17eb0e8</th>\n", "      <th>collective</th>\n", "      <td>67.0</td>\n", "      <td>2.0</td>\n", "      <td>144.0</td>\n", "      <td>362</td>\n", "      <td>0.465278</td>\n", "      <td>0.397790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>828a6944-9363-4e0c-a527-234146c3d7df</th>\n", "      <th>reveart</th>\n", "      <td>159.0</td>\n", "      <td>18.0</td>\n", "      <td>275.0</td>\n", "      <td>768</td>\n", "      <td>0.578182</td>\n", "      <td>0.358073</td>\n", "    </tr>\n", "    <tr>\n", "      <th>849a5e7b-de26-4c18-994e-c470d68c6579</th>\n", "      <th>webflow</th>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>22.0</td>\n", "      <td>37</td>\n", "      <td>0.136364</td>\n", "      <td>0.594595</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88fa44d0-8d33-4f78-9158-80b8cd27abc0</th>\n", "      <th>observeinc</th>\n", "      <td>32.0</td>\n", "      <td>17.0</td>\n", "      <td>110.0</td>\n", "      <td>120</td>\n", "      <td>0.290909</td>\n", "      <td>0.916667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8d26a9a1-73b7-4382-8f7e-950c51fda41b</th>\n", "      <th>collective</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>56.0</td>\n", "      <td>124</td>\n", "      <td>0.089286</td>\n", "      <td>0.451613</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8d3fac81-b818-43bf-94ab-77e0bec56daf</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>34.0</td>\n", "      <td>0.0</td>\n", "      <td>38.0</td>\n", "      <td>40</td>\n", "      <td>0.894737</td>\n", "      <td>0.950000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8dfdfaca-4f02-45db-947e-bd4f300198f0</th>\n", "      <th>aitutor-turing</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>152.0</td>\n", "      <td>334</td>\n", "      <td>0.006579</td>\n", "      <td>0.455090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8e52c7c8-2542-4fe2-a32f-ac711a6cc52c</th>\n", "      <th>montecarlodata</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>94.0</td>\n", "      <td>200</td>\n", "      <td>0.053191</td>\n", "      <td>0.470000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8e79ad4c-cd98-4315-883d-373eb0c59b5d</th>\n", "      <th>collective</th>\n", "      <td>28.0</td>\n", "      <td>3.0</td>\n", "      <td>236.0</td>\n", "      <td>406</td>\n", "      <td>0.118644</td>\n", "      <td>0.581281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8e82342e-a3de-4495-92a9-bcf5bc8c14f6</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>17.0</td>\n", "      <td>34</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9130182f-9cfd-4d98-935a-7533fc45877c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>96.0</td>\n", "      <td>0.0</td>\n", "      <td>202.0</td>\n", "      <td>324</td>\n", "      <td>0.475248</td>\n", "      <td>0.623457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91f60597-e4c7-4791-9886-ef13bbaa76fd</th>\n", "      <th>ampsortation</th>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>68.0</td>\n", "      <td>134</td>\n", "      <td>0.044118</td>\n", "      <td>0.507463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>931d32fd-13e6-42ae-a6fd-24a36b8b0fe0</th>\n", "      <th>ampsortation</th>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>109.0</td>\n", "      <td>198</td>\n", "      <td>0.229358</td>\n", "      <td>0.550505</td>\n", "    </tr>\n", "    <tr>\n", "      <th>937d6be3-a292-4ed1-9dc0-930a266f2407</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>34.0</td>\n", "      <td>1.0</td>\n", "      <td>54.0</td>\n", "      <td>76</td>\n", "      <td>0.629630</td>\n", "      <td>0.710526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940095a1-0791-41eb-8ed2-e3e488b794cf</th>\n", "      <th>reveart</th>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>68.0</td>\n", "      <td>209</td>\n", "      <td>0.000000</td>\n", "      <td>0.325359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9453d128-66ea-4ad9-87db-4e4be2bb19f6</th>\n", "      <th>observeinc</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>146.0</td>\n", "      <td>239</td>\n", "      <td>0.027397</td>\n", "      <td>0.610879</td>\n", "    </tr>\n", "    <tr>\n", "      <th>946c15f0-b7f8-4f9b-a199-f8eeddaa0a37</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>9</td>\n", "      <td>0.000000</td>\n", "      <td>0.111111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94ff144c-8158-4c43-a144-19829e615cb4</th>\n", "      <th>observeinc</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>134.0</td>\n", "      <td>275</td>\n", "      <td>0.007463</td>\n", "      <td>0.487273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99bd059b-242c-41de-94fb-172a423d5739</th>\n", "      <th>webflow</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>248.0</td>\n", "      <td>288</td>\n", "      <td>0.008065</td>\n", "      <td>0.861111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9d636f8c-b147-4f39-a4ce-345d2bcadcf4</th>\n", "      <th>webflow</th>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>49.0</td>\n", "      <td>210</td>\n", "      <td>0.081633</td>\n", "      <td>0.233333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9d66d83a-6a9b-4485-96d0-b56ad67e88c5</th>\n", "      <th>webflow</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>40.0</td>\n", "      <td>64</td>\n", "      <td>0.100000</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9e5083be-e7d9-43da-a325-d4ee20ff2b84</th>\n", "      <th>collective</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>28.0</td>\n", "      <td>160</td>\n", "      <td>0.000000</td>\n", "      <td>0.175000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9f2343ce-6d84-4ed9-ad39-e79126d94266</th>\n", "      <th>reveart</th>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>76.0</td>\n", "      <td>231</td>\n", "      <td>0.000000</td>\n", "      <td>0.329004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a063c637-4f19-4dbc-b1f1-3b188e975542</th>\n", "      <th>collective</th>\n", "      <td>35.0</td>\n", "      <td>0.0</td>\n", "      <td>215.0</td>\n", "      <td>479</td>\n", "      <td>0.162791</td>\n", "      <td>0.448852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>77.0</td>\n", "      <td>157</td>\n", "      <td>0.051948</td>\n", "      <td>0.490446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a1a3f2da-6b19-42dd-8d89-bf29e83d5614</th>\n", "      <th>reveart</th>\n", "      <td>32.0</td>\n", "      <td>0.0</td>\n", "      <td>104.0</td>\n", "      <td>343</td>\n", "      <td>0.307692</td>\n", "      <td>0.303207</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a39a2e34-a02d-4b28-b9a3-fe303700c680</th>\n", "      <th>aitutor-turing</th>\n", "      <td>35.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>138</td>\n", "      <td>0.466667</td>\n", "      <td>0.543478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a4825044-2e1a-4a84-876c-bb294f9c4fe6</th>\n", "      <th>webflow</th>\n", "      <td>8.0</td>\n", "      <td>7.0</td>\n", "      <td>120.0</td>\n", "      <td>197</td>\n", "      <td>0.066667</td>\n", "      <td>0.609137</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a5d5c9cd-07c8-483a-b98f-5ef0ff38ca55</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>160.0</td>\n", "      <td>376</td>\n", "      <td>0.000000</td>\n", "      <td>0.425532</td>\n", "    </tr>\n", "    <tr>\n", "      <th>a6a3c038-c989-44bf-ad9d-25de78e57bab</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>26</td>\n", "      <td>0.000000</td>\n", "      <td>1.115385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aa70db99-f918-4481-abf2-7156d4df6dc0</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ab0465d6-231d-4745-bcd5-aae304e1e8d7</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b16381ca-7897-4e8f-af9e-e06c1a29fa06</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>52.0</td>\n", "      <td>143</td>\n", "      <td>0.000000</td>\n", "      <td>0.363636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b4f90220-8a20-4772-970f-e6717a023ef9</th>\n", "      <th>webflow</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>41.0</td>\n", "      <td>72</td>\n", "      <td>0.024390</td>\n", "      <td>0.569444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b5b03034-2026-4788-9645-7200a6f56665</th>\n", "      <th>webflow</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>12</td>\n", "      <td>0.571429</td>\n", "      <td>0.583333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b60b218d-93db-4dcc-af5a-9f7bf4a18091</th>\n", "      <th>observeinc</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>61.0</td>\n", "      <td>133</td>\n", "      <td>0.065574</td>\n", "      <td>0.458647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b6b01e5c-9c5f-4a07-8be9-319cf63b3c43</th>\n", "      <th>reveart</th>\n", "      <td>17.0</td>\n", "      <td>7.0</td>\n", "      <td>180.0</td>\n", "      <td>659</td>\n", "      <td>0.094444</td>\n", "      <td>0.273141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b6b20bf9-898f-4ed1-a3fc-a9ac1a3c2359</th>\n", "      <th>reveart</th>\n", "      <td>11.0</td>\n", "      <td>30.0</td>\n", "      <td>206.0</td>\n", "      <td>474</td>\n", "      <td>0.053398</td>\n", "      <td>0.434599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b892bc86-82cd-4c6c-b5db-f14896b99a63</th>\n", "      <th>aitutor-turing</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>29</td>\n", "      <td>0.047619</td>\n", "      <td>0.724138</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b8f10a65-7be1-41ca-b927-4e9f329ec97e</th>\n", "      <th>observeinc</th>\n", "      <td>8.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>33</td>\n", "      <td>0.380952</td>\n", "      <td>0.636364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>bb37483c-e3eb-4fd5-a04c-470f8669e50a</th>\n", "      <th>montecarlodata</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>12</td>\n", "      <td>0.000000</td>\n", "      <td>0.416667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>bc5925dc-f1d5-4c69-bdf9-2ce2bc4c9ed1</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>29</td>\n", "      <td>0.000000</td>\n", "      <td>0.103448</td>\n", "    </tr>\n", "    <tr>\n", "      <th>bd5d47d8-08c8-4c6b-b530-f02002945dd3</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>60.0</td>\n", "      <td>131</td>\n", "      <td>0.000000</td>\n", "      <td>0.458015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c192315a-2326-4d47-8624-0ae1a4551a22</th>\n", "      <th>webflow</th>\n", "      <td>6.0</td>\n", "      <td>2.0</td>\n", "      <td>59.0</td>\n", "      <td>103</td>\n", "      <td>0.101695</td>\n", "      <td>0.572816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c1bb7492-5fca-4c85-84ff-9cb7bc2a209c</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>57.0</td>\n", "      <td>0.0</td>\n", "      <td>174.0</td>\n", "      <td>324</td>\n", "      <td>0.327586</td>\n", "      <td>0.537037</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c2ea97ea-1357-4e7c-819c-a7ad36e7e53b</th>\n", "      <th>webflow</th>\n", "      <td>18.0</td>\n", "      <td>1.0</td>\n", "      <td>445.0</td>\n", "      <td>633</td>\n", "      <td>0.040449</td>\n", "      <td>0.703002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c93bfbc9-25ac-41bb-a160-0ea9f943d5d0</th>\n", "      <th>observeinc</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>32.0</td>\n", "      <td>174</td>\n", "      <td>0.156250</td>\n", "      <td>0.183908</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cb106dd0-be80-4c5f-a6ff-15892b5cc75c</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>335.0</td>\n", "      <td>544</td>\n", "      <td>0.000000</td>\n", "      <td>0.615809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cb245b75-9c24-4580-889a-05cf628772eb</th>\n", "      <th>webflow</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>14.0</td>\n", "      <td>22</td>\n", "      <td>0.285714</td>\n", "      <td>0.636364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cbd27dbe-35e4-47f0-95ed-3cd74c7c4c90</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>102.0</td>\n", "      <td>254</td>\n", "      <td>0.000000</td>\n", "      <td>0.401575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cc33f315-e8d5-42b9-ab48-650d9ffbd05c</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d275d108-d606-4664-b3fe-ee19a3eb2172</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100.0</td>\n", "      <td>167</td>\n", "      <td>0.000000</td>\n", "      <td>0.598802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d2ba7763-9d93-498b-a666-018836168f1b</th>\n", "      <th>webflow</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>62.0</td>\n", "      <td>153</td>\n", "      <td>0.016129</td>\n", "      <td>0.405229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d3726c31-14be-4fed-a7ff-f43d6dfc96f2</th>\n", "      <th>reveart</th>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>338.0</td>\n", "      <td>331</td>\n", "      <td>0.008876</td>\n", "      <td>1.021148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d543569f-22b4-4a83-a17c-c215e8bacec4</th>\n", "      <th>webflow</th>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>16</td>\n", "      <td>0.454545</td>\n", "      <td>0.687500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d56ecab6-90d7-4965-9cfa-0c4ca350e0cf</th>\n", "      <th>aitutor-mercor</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>25</td>\n", "      <td>0.000000</td>\n", "      <td>0.280000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d7b45b8a-2174-455d-ba5c-d15ce4dfef67</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>16.0</td>\n", "      <td>38.0</td>\n", "      <td>63</td>\n", "      <td>0.000000</td>\n", "      <td>0.603175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>db4d2bd8-af3e-4c36-a88e-83af6fa3f823</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>82.0</td>\n", "      <td>145</td>\n", "      <td>0.000000</td>\n", "      <td>0.565517</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dda067e0-4386-41e4-a3f1-94b34b747c6b</th>\n", "      <th>webflow</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>58</td>\n", "      <td>0.095238</td>\n", "      <td>0.362069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>df3f2c90-ad64-4c21-ab97-2c344a5d433f</th>\n", "      <th>ampsortation</th>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>32.0</td>\n", "      <td>24</td>\n", "      <td>0.218750</td>\n", "      <td>1.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e059e296-a230-4e9c-9802-4c2322551b2f</th>\n", "      <th>webflow</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>28.0</td>\n", "      <td>55</td>\n", "      <td>0.000000</td>\n", "      <td>0.509091</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e367ecd3-83c4-4cd3-95a3-d4e3ca363aa6</th>\n", "      <th>webflow</th>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>37</td>\n", "      <td>0.300000</td>\n", "      <td>0.540541</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e5121387-4ae4-461f-a3e5-3481eeb77d3e</th>\n", "      <th>webflow</th>\n", "      <td>142.0</td>\n", "      <td>3.0</td>\n", "      <td>384.0</td>\n", "      <td>910</td>\n", "      <td>0.369792</td>\n", "      <td>0.421978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e52b7008-7a10-416d-8337-58cef6074311</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>19.0</td>\n", "      <td>38</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e5983c8b-880c-4e68-a3cf-e12f349e9365</th>\n", "      <th>aitutor-turing</th>\n", "      <td>146.0</td>\n", "      <td>57.0</td>\n", "      <td>169.0</td>\n", "      <td>182</td>\n", "      <td>0.863905</td>\n", "      <td>0.928571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e65ddaf6-8703-4564-92d8-0b51e3af7bad</th>\n", "      <th>collective</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>256.0</td>\n", "      <td>568</td>\n", "      <td>0.007812</td>\n", "      <td>0.450704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e6f926a1-bc09-4014-b516-6f437b26bd9c</th>\n", "      <th>reveart</th>\n", "      <td>11.0</td>\n", "      <td>3.0</td>\n", "      <td>301.0</td>\n", "      <td>552</td>\n", "      <td>0.036545</td>\n", "      <td>0.545290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e800dc0b-1811-4536-9970-d993fe479e09</th>\n", "      <th>aitutor-turing</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>8</td>\n", "      <td>0.000000</td>\n", "      <td>0.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e97f62fd-d756-4c1f-aa05-1f7a72eee4c8</th>\n", "      <th>ampsortation</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>21</td>\n", "      <td>0.666667</td>\n", "      <td>0.285714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ea2228c4-bf6b-4c78-9f5c-765bd15f1e8f</th>\n", "      <th>ampsortation</th>\n", "      <td>6.0</td>\n", "      <td>2.0</td>\n", "      <td>141.0</td>\n", "      <td>364</td>\n", "      <td>0.042553</td>\n", "      <td>0.387363</td>\n", "    </tr>\n", "    <tr>\n", "      <th>eae9980b-0534-4d1e-b07f-5254037e1db2</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>330.0</td>\n", "      <td>684</td>\n", "      <td>0.000000</td>\n", "      <td>0.482456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>eb5d15ed-fdb3-4a98-b0c7-bbf977d275a5</th>\n", "      <th>observeinc</th>\n", "      <td>34.0</td>\n", "      <td>0.0</td>\n", "      <td>105.0</td>\n", "      <td>125</td>\n", "      <td>0.323810</td>\n", "      <td>0.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ec4ba5f0-fffa-4a71-a3b2-80dd7c71e960</th>\n", "      <th>collective</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>11</td>\n", "      <td>0.000000</td>\n", "      <td>0.636364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ee518736-d428-4584-8425-0aa3401d4b13</th>\n", "      <th>webflow</th>\n", "      <td>17.0</td>\n", "      <td>4.0</td>\n", "      <td>279.0</td>\n", "      <td>566</td>\n", "      <td>0.060932</td>\n", "      <td>0.492933</td>\n", "    </tr>\n", "    <tr>\n", "      <th>eee017bd-5311-4934-916a-fbd8f139d9e7</th>\n", "      <th>montecarlodata</th>\n", "      <td>27.0</td>\n", "      <td>0.0</td>\n", "      <td>259.0</td>\n", "      <td>461</td>\n", "      <td>0.104247</td>\n", "      <td>0.561822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0</th>\n", "      <th>aitutor-turing</th>\n", "      <td>95.0</td>\n", "      <td>44.0</td>\n", "      <td>100.0</td>\n", "      <td>141</td>\n", "      <td>0.950000</td>\n", "      <td>0.709220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f6f8b059-2ac7-4198-bad9-b9683af4eb86</th>\n", "      <th>webflow</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>9</td>\n", "      <td>1.000000</td>\n", "      <td>0.222222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f861e701-d995-412f-ba41-ca3029e204b8</th>\n", "      <th>observeinc</th>\n", "      <td>18.0</td>\n", "      <td>6.0</td>\n", "      <td>66.0</td>\n", "      <td>110</td>\n", "      <td>0.272727</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f8dbfefe-d8ac-494b-b5f3-841e43055cbe</th>\n", "      <th>reveart</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>310.0</td>\n", "      <td>589</td>\n", "      <td>0.006452</td>\n", "      <td>0.526316</td>\n", "    </tr>\n", "    <tr>\n", "      <th>f9c29095-b3c2-4664-a180-57cd69685b95</th>\n", "      <th>collective</th>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>70.0</td>\n", "      <td>153</td>\n", "      <td>0.042857</td>\n", "      <td>0.457516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fa46cab0-eef8-4a6e-8e9b-b66dacf657e0</th>\n", "      <th>observeinc</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>17.0</td>\n", "      <td>20</td>\n", "      <td>0.000000</td>\n", "      <td>0.850000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fa6c7d77-22f9-4e4a-a85c-dadebbc7a5af</th>\n", "      <th>montecarlodata</th>\n", "      <td>34.0</td>\n", "      <td>0.0</td>\n", "      <td>213.0</td>\n", "      <td>439</td>\n", "      <td>0.159624</td>\n", "      <td>0.485194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fb4c2ce0-fbac-444e-be3f-3f07c7e9c61f</th>\n", "      <th>webflow</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>20.0</td>\n", "      <td>71</td>\n", "      <td>0.250000</td>\n", "      <td>0.281690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fc06e9ef-b3d9-4505-8f62-3cd335814e6f</th>\n", "      <th>ampsortation</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fc93421e-e4fd-470c-bd5e-de33d629f0a2</th>\n", "      <th>webflow</th>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>85.0</td>\n", "      <td>180</td>\n", "      <td>0.035294</td>\n", "      <td>0.472222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fca46e88-bcb2-4601-a3e0-aaa02c6b8b22</th>\n", "      <th>montecarlodata</th>\n", "      <td>22.0</td>\n", "      <td>4.0</td>\n", "      <td>141.0</td>\n", "      <td>269</td>\n", "      <td>0.156028</td>\n", "      <td>0.524164</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                     accept  reject  \\\n", "session_id                           tenant                           \n", "00d6ff68-99f0-4365-b817-2f9fb47715df collective         0.0     0.0   \n", "010a135a-fcd3-49ab-85d7-3b85e3c2d8d7 webflow            0.0     0.0   \n", "013a033a-45bb-4f4b-bcba-112beede877c observeinc        98.0     4.0   \n", "01ea3250-0039-4c69-b635-f129f7c87950 observeinc         7.0     7.0   \n", "085468cc-ff24-4d14-ae24-209332a9d6a1 observeinc         0.0     0.0   \n", "0a8055ee-eb84-40f1-8104-a6d16fd6158d observeinc         1.0     0.0   \n", "0c2cbb2a-8ab5-49fb-8ada-8d17134080ed observeinc         0.0     1.0   \n", "0c485ae0-05b4-4e76-ae81-3a276b6dfa87 webflow            0.0     0.0   \n", "10258db1-9e2e-4c55-9870-bf2025a70305 ampsortation       1.0     1.0   \n", "103820d0-4a25-4587-9282-2e38b98c02ea reveart           64.0     8.0   \n", "10cfc0d7-72e8-4b59-b15c-6039cff38c8b webflow          151.0     5.0   \n", "11334beb-823e-4f84-8f61-8435f4afaaf4 webflow            0.0     0.0   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing     1.0     0.0   \n", "134d006a-9cb9-4769-9c4b-612d77cc5f91 aitutor-mercor     0.0     0.0   \n", "140bbbea-5b07-4e58-8b11-23915a201f90 observeinc         1.0     0.0   \n", "17d6d858-abac-43de-8d3a-ff3d6b8b5c79 observeinc         5.0     0.0   \n", "1a548214-62cd-459c-b820-358173b3af10 webflow            0.0     0.0   \n", "1ad37a32-d9dd-43b3-96a3-bae4d07e36cf webflow            0.0     0.0   \n", "1cc5b754-7411-4486-b8c5-2a26c3671bb0 collective        22.0     3.0   \n", "20454f08-755a-4484-b203-b66092376375 ampsortation       5.0     1.0   \n", "21653165-5a71-46d6-b2c8-4757206d236a aitutor-turing     0.0     0.0   \n", "23852206-5b56-477f-b50d-44acfe195ce1 webflow           57.0     4.0   \n", "2463c696-9213-485b-91aa-a5a73782fba0 ampsortation       6.0     0.0   \n", "25f21a16-c0cd-4559-9f47-383746508eb2 aitutor-turing     0.0     0.0   \n", "26dfc75f-18b5-4e5a-81d2-62a2c8ac4537 observeinc         1.0     7.0   \n", "2af3ff2e-2ff7-4c5e-92cc-ae1892cb43ed observeinc         0.0     2.0   \n", "2c7faade-5a26-4168-a804-92abe32cf58a collective         3.0     1.0   \n", "2eb6183f-0b28-4761-b410-d441b1e7a7b9 webflow            0.0     1.0   \n", "2f04b8d1-71fe-4208-996f-e670dd7dc22c aitutor-turing     0.0     0.0   \n", "3575a9db-2b27-4bbe-b036-3ab93cacf358 observeinc         0.0     1.0   \n", "35b70283-3e9b-4be6-89d2-1750cf22d1e6 webflow            5.0     0.0   \n", "38a2307c-2aec-47c1-83bf-c641ee696ba3 ampsortation       0.0     0.0   \n", "38ea880e-3a8a-42b6-825d-3fac712689e6 webflow           68.0     1.0   \n", "3b2db685-968b-4b38-8f4a-244cdec1aa55 webflow           11.0     4.0   \n", "3bf90d00-e133-4a38-a95b-419cc581fbdc montecarlodata    44.0     6.0   \n", "3d73081a-1303-4c3a-a22f-06f711e41d16 webflow            4.0     0.0   \n", "3f84aaaf-b22c-481b-b420-fbe5a1218701 webflow            3.0     1.0   \n", "446301dc-2723-42c6-b098-6ef4b77a3f53 webflow            0.0     1.0   \n", "46dfed66-c74d-46b8-b8cd-2917ca44b508 aitutor-mercor     4.0     0.0   \n", "4a3a949e-1e3d-452f-b413-a4de573ffe18 webflow           66.0     2.0   \n", "4b2995b6-637b-4331-9b71-ee11490e3dac webflow            0.0     0.0   \n", "4e00679d-9829-4e93-9173-e38136ac5b69 ampsortation       2.0     0.0   \n", "5001416d-9f16-4e8e-a824-8c725dbec307 webflow            3.0     1.0   \n", "5017b0fd-26e1-42f8-8fff-f389a21f0a02 observeinc         0.0     0.0   \n", "5232f278-141c-4ab2-8498-fb5fe155cf7a observeinc        66.0     9.0   \n", "52bfa022-9e00-40a0-bb70-aba39d2b0967 webflow            0.0     0.0   \n", "5aac6afa-7fc8-4792-9b41-b2731cf940b7 reveart            4.0     4.0   \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor     0.0     0.0   \n", "5fbb952d-28ce-4516-a69f-64f7ab9c0014 aitutor-mercor     0.0     0.0   \n", "638e1453-e267-43d0-b7d4-7ebe00881570 observeinc         4.0     0.0   \n", "64eb51d8-18b0-4ab6-9714-d9c7435def20 webflow            9.0     2.0   \n", "6819915b-a8f9-414e-a874-9e6c093a3d24 reveart           55.0     3.0   \n", "6d7e015c-4d7c-4f6f-be97-f46ed03fff05 observeinc        33.0     0.0   \n", "6e2b7743-2f2d-421c-9cc1-508378baff3c collective         0.0     0.0   \n", "7022323c-90fa-4c46-819d-bc29f774deb8 webflow            0.0     0.0   \n", "7429eb40-e11d-4c2a-85ea-355a72befab4 webflow           17.0     0.0   \n", "745a36cc-5465-43b5-9a7d-c79588954df2 webflow            1.0     0.0   \n", "7574f7c8-7969-4561-8c0e-ace59dade919 observeinc         0.0     1.0   \n", "75f4c4c9-46c7-4f5b-ac5a-3f3bd76aa49d ampsortation       5.0     0.0   \n", "7771af49-7736-4db8-85a5-ab9f34b6ba16 webflow            1.0     0.0   \n", "78febf34-1e81-4803-b206-7eea0ff7f477 reveart           42.0     1.0   \n", "7a7b3326-8753-466d-a7be-3415e1833ece reveart           44.0     0.0   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing     6.0     0.0   \n", "7f4d97bd-2713-4e1f-8bfb-ea961f387c57 collective         8.0     0.0   \n", "8005e319-9983-4307-ae2e-4b07e17eb0e8 collective        67.0     2.0   \n", "828a6944-9363-4e0c-a527-234146c3d7df reveart          159.0    18.0   \n", "849a5e7b-de26-4c18-994e-c470d68c6579 webflow            3.0     0.0   \n", "88fa44d0-8d33-4f78-9158-80b8cd27abc0 observeinc        32.0    17.0   \n", "8d26a9a1-73b7-4382-8f7e-950c51fda41b collective         5.0     0.0   \n", "8d3fac81-b818-43bf-94ab-77e0bec56daf aitutor-mercor    34.0     0.0   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing     1.0     0.0   \n", "8e52c7c8-2542-4fe2-a32f-ac711a6cc52c montecarlodata     5.0     0.0   \n", "8e79ad4c-cd98-4315-883d-373eb0c59b5d collective        28.0     3.0   \n", "8e82342e-a3de-4495-92a9-bcf5bc8c14f6 webflow            0.0     0.0   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing    96.0     0.0   \n", "91f60597-e4c7-4791-9886-ef13bbaa76fd ampsortation       3.0     0.0   \n", "931d32fd-13e6-42ae-a6fd-24a36b8b0fe0 ampsortation      25.0     0.0   \n", "937d6be3-a292-4ed1-9dc0-930a266f2407 aitutor-mercor    34.0     1.0   \n", "940095a1-0791-41eb-8ed2-e3e488b794cf reveart            0.0     3.0   \n", "9453d128-66ea-4ad9-87db-4e4be2bb19f6 observeinc         4.0     1.0   \n", "946c15f0-b7f8-4f9b-a199-f8eeddaa0a37 observeinc         0.0     0.0   \n", "94ff144c-8158-4c43-a144-19829e615cb4 observeinc         1.0     0.0   \n", "99bd059b-242c-41de-94fb-172a423d5739 webflow            2.0     1.0   \n", "9d636f8c-b147-4f39-a4ce-345d2bcadcf4 webflow            4.0     6.0   \n", "9d66d83a-6a9b-4485-96d0-b56ad67e88c5 webflow            4.0     0.0   \n", "9e5083be-e7d9-43da-a325-d4ee20ff2b84 collective         0.0     0.0   \n", "9f2343ce-6d84-4ed9-ad39-e79126d94266 reveart            0.0     2.0   \n", "a063c637-4f19-4dbc-b1f1-3b188e975542 collective        35.0     0.0   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor     4.0     0.0   \n", "a1a3f2da-6b19-42dd-8d89-bf29e83d5614 reveart           32.0     0.0   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing    35.0     0.0   \n", "a4825044-2e1a-4a84-876c-bb294f9c4fe6 webflow            8.0     7.0   \n", "a5d5c9cd-07c8-483a-b98f-5ef0ff38ca55 webflow            0.0     7.0   \n", "a6a3c038-c989-44bf-ad9d-25de78e57bab observeinc         0.0     1.0   \n", "aa70db99-f918-4481-abf2-7156d4df6dc0 aitutor-mercor     0.0     0.0   \n", "ab0465d6-231d-4745-bcd5-aae304e1e8d7 observeinc         0.0     0.0   \n", "b16381ca-7897-4e8f-af9e-e06c1a29fa06 webflow            0.0     0.0   \n", "b4f90220-8a20-4772-970f-e6717a023ef9 webflow            1.0     0.0   \n", "b5b03034-2026-4788-9645-7200a6f56665 webflow            4.0     1.0   \n", "b60b218d-93db-4dcc-af5a-9f7bf4a18091 observeinc         4.0     0.0   \n", "b6b01e5c-9c5f-4a07-8be9-319cf63b3c43 reveart           17.0     7.0   \n", "b6b20bf9-898f-4ed1-a3fc-a9ac1a3c2359 reveart           11.0    30.0   \n", "b892bc86-82cd-4c6c-b5db-f14896b99a63 aitutor-turing     1.0     0.0   \n", "b8f10a65-7be1-41ca-b927-4e9f329ec97e observeinc         8.0     0.0   \n", "bb37483c-e3eb-4fd5-a04c-470f8669e50a montecarlodata     0.0     0.0   \n", "bc5925dc-f1d5-4c69-bdf9-2ce2bc4c9ed1 aitutor-mercor     0.0     0.0   \n", "bd5d47d8-08c8-4c6b-b530-f02002945dd3 webflow            0.0     0.0   \n", "c192315a-2326-4d47-8624-0ae1a4551a22 webflow            6.0     2.0   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor    57.0     0.0   \n", "c2ea97ea-1357-4e7c-819c-a7ad36e7e53b webflow           18.0     1.0   \n", "c93bfbc9-25ac-41bb-a160-0ea9f943d5d0 observeinc         5.0     0.0   \n", "cb106dd0-be80-4c5f-a6ff-15892b5cc75c observeinc         0.0     0.0   \n", "cb245b75-9c24-4580-889a-05cf628772eb webflow            4.0     0.0   \n", "cbd27dbe-35e4-47f0-95ed-3cd74c7c4c90 webflow            0.0     0.0   \n", "cc33f315-e8d5-42b9-ab48-650d9ffbd05c aitutor-turing     0.0     0.0   \n", "d275d108-d606-4664-b3fe-ee19a3eb2172 observeinc         0.0     0.0   \n", "d2ba7763-9d93-498b-a666-018836168f1b webflow            1.0     0.0   \n", "d3726c31-14be-4fed-a7ff-f43d6dfc96f2 reveart            3.0     0.0   \n", "d543569f-22b4-4a83-a17c-c215e8bacec4 webflow            5.0     0.0   \n", "d56ecab6-90d7-4965-9cfa-0c4ca350e0cf aitutor-mercor     0.0     0.0   \n", "d7b45b8a-2174-455d-ba5c-d15ce4dfef67 observeinc         0.0    16.0   \n", "db4d2bd8-af3e-4c36-a88e-83af6fa3f823 webflow            0.0     2.0   \n", "dda067e0-4386-41e4-a3f1-94b34b747c6b webflow            2.0     0.0   \n", "df3f2c90-ad64-4c21-ab97-2c344a5d433f ampsortation       7.0     0.0   \n", "e059e296-a230-4e9c-9802-4c2322551b2f webflow            0.0     0.0   \n", "e367ecd3-83c4-4cd3-95a3-d4e3ca363aa6 webflow            6.0     0.0   \n", "e5121387-4ae4-461f-a3e5-3481eeb77d3e webflow          142.0     3.0   \n", "e52b7008-7a10-416d-8337-58cef6074311 aitutor-turing     0.0     0.0   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing   146.0    57.0   \n", "e65ddaf6-8703-4564-92d8-0b51e3af7bad collective         2.0     0.0   \n", "e6f926a1-bc09-4014-b516-6f437b26bd9c reveart           11.0     3.0   \n", "e800dc0b-1811-4536-9970-d993fe479e09 aitutor-turing     0.0     0.0   \n", "e97f62fd-d756-4c1f-aa05-1f7a72eee4c8 ampsortation       4.0     0.0   \n", "ea2228c4-bf6b-4c78-9f5c-765bd15f1e8f ampsortation       6.0     2.0   \n", "eae9980b-0534-4d1e-b07f-5254037e1db2 observeinc         0.0     0.0   \n", "eb5d15ed-fdb3-4a98-b0c7-bbf977d275a5 observeinc        34.0     0.0   \n", "ec4ba5f0-fffa-4a71-a3b2-80dd7c71e960 collective         0.0     0.0   \n", "ee518736-d428-4584-8425-0aa3401d4b13 webflow           17.0     4.0   \n", "eee017bd-5311-4934-916a-fbd8f139d9e7 montecarlodata    27.0     0.0   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing    95.0    44.0   \n", "f6f8b059-2ac7-4198-bad9-b9683af4eb86 webflow            2.0     0.0   \n", "f861e701-d995-412f-ba41-ca3029e204b8 observeinc        18.0     6.0   \n", "f8dbfefe-d8ac-494b-b5f3-841e43055cbe reveart            2.0     0.0   \n", "f9c29095-b3c2-4664-a180-57cd69685b95 collective         3.0     2.0   \n", "fa46cab0-eef8-4a6e-8e9b-b66dacf657e0 observeinc         0.0     0.0   \n", "fa6c7d77-22f9-4e4a-a85c-dadebbc7a5af montecarlodata    34.0     0.0   \n", "fb4c2ce0-fbac-444e-be3f-3f07c7e9c61f webflow            5.0     1.0   \n", "fc06e9ef-b3d9-4505-8f62-3cd335814e6f ampsortation       0.0     0.0   \n", "fc93421e-e4fd-470c-bd5e-de33d629f0a2 webflow            3.0     0.0   \n", "fca46e88-bcb2-4601-a3e0-aaa02c6b8b22 montecarlodata    22.0     4.0   \n", "\n", "                                                     hover-shown  \\\n", "session_id                           tenant                        \n", "00d6ff68-99f0-4365-b817-2f9fb47715df collective            108.0   \n", "010a135a-fcd3-49ab-85d7-3b85e3c2d8d7 webflow                 9.0   \n", "013a033a-45bb-4f4b-bcba-112beede877c observeinc            256.0   \n", "01ea3250-0039-4c69-b635-f129f7c87950 observeinc            163.0   \n", "085468cc-ff24-4d14-ae24-209332a9d6a1 observeinc              0.0   \n", "0a8055ee-eb84-40f1-8104-a6d16fd6158d observeinc            112.0   \n", "0c2cbb2a-8ab5-49fb-8ada-8d17134080ed observeinc             15.0   \n", "0c485ae0-05b4-4e76-ae81-3a276b6dfa87 webflow                23.0   \n", "10258db1-9e2e-4c55-9870-bf2025a70305 ampsortation           87.0   \n", "103820d0-4a25-4587-9282-2e38b98c02ea reveart               266.0   \n", "10cfc0d7-72e8-4b59-b15c-6039cff38c8b webflow               538.0   \n", "11334beb-823e-4f84-8f61-8435f4afaaf4 webflow                 6.0   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing         52.0   \n", "134d006a-9cb9-4769-9c4b-612d77cc5f91 aitutor-mercor          0.0   \n", "140bbbea-5b07-4e58-8b11-23915a201f90 observeinc              1.0   \n", "17d6d858-abac-43de-8d3a-ff3d6b8b5c79 observeinc            142.0   \n", "1a548214-62cd-459c-b820-358173b3af10 webflow                 1.0   \n", "1ad37a32-d9dd-43b3-96a3-bae4d07e36cf webflow                 2.0   \n", "1cc5b754-7411-4486-b8c5-2a26c3671bb0 collective             63.0   \n", "20454f08-755a-4484-b203-b66092376375 ampsortation           83.0   \n", "21653165-5a71-46d6-b2c8-4757206d236a aitutor-turing         18.0   \n", "23852206-5b56-477f-b50d-44acfe195ce1 webflow               536.0   \n", "2463c696-9213-485b-91aa-a5a73782fba0 ampsortation           84.0   \n", "25f21a16-c0cd-4559-9f47-383746508eb2 aitutor-turing          0.0   \n", "26dfc75f-18b5-4e5a-81d2-62a2c8ac4537 observeinc            110.0   \n", "2af3ff2e-2ff7-4c5e-92cc-ae1892cb43ed observeinc            258.0   \n", "2c7faade-5a26-4168-a804-92abe32cf58a collective            348.0   \n", "2eb6183f-0b28-4761-b410-d441b1e7a7b9 webflow                14.0   \n", "2f04b8d1-71fe-4208-996f-e670dd7dc22c aitutor-turing          0.0   \n", "3575a9db-2b27-4bbe-b036-3ab93cacf358 observeinc             80.0   \n", "35b70283-3e9b-4be6-89d2-1750cf22d1e6 webflow                43.0   \n", "38a2307c-2aec-47c1-83bf-c641ee696ba3 ampsortation           82.0   \n", "38ea880e-3a8a-42b6-825d-3fac712689e6 webflow               149.0   \n", "3b2db685-968b-4b38-8f4a-244cdec1aa55 webflow               272.0   \n", "3bf90d00-e133-4a38-a95b-419cc581fbdc montecarlodata        101.0   \n", "3d73081a-1303-4c3a-a22f-06f711e41d16 webflow                 8.0   \n", "3f84aaaf-b22c-481b-b420-fbe5a1218701 webflow                39.0   \n", "446301dc-2723-42c6-b098-6ef4b77a3f53 webflow                11.0   \n", "46dfed66-c74d-46b8-b8cd-2917ca44b508 aitutor-mercor         10.0   \n", "4a3a949e-1e3d-452f-b413-a4de573ffe18 webflow               358.0   \n", "4b2995b6-637b-4331-9b71-ee11490e3dac webflow                 8.0   \n", "4e00679d-9829-4e93-9173-e38136ac5b69 ampsortation           24.0   \n", "5001416d-9f16-4e8e-a824-8c725dbec307 webflow                80.0   \n", "5017b0fd-26e1-42f8-8fff-f389a21f0a02 observeinc             18.0   \n", "5232f278-141c-4ab2-8498-fb5fe155cf7a observeinc            306.0   \n", "52bfa022-9e00-40a0-bb70-aba39d2b0967 webflow                27.0   \n", "5aac6afa-7fc8-4792-9b41-b2731cf940b7 reveart               134.0   \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor        101.0   \n", "5fbb952d-28ce-4516-a69f-64f7ab9c0014 aitutor-mercor          5.0   \n", "638e1453-e267-43d0-b7d4-7ebe00881570 observeinc              4.0   \n", "64eb51d8-18b0-4ab6-9714-d9c7435def20 webflow               102.0   \n", "6819915b-a8f9-414e-a874-9e6c093a3d24 reveart               217.0   \n", "6d7e015c-4d7c-4f6f-be97-f46ed03fff05 observeinc            104.0   \n", "6e2b7743-2f2d-421c-9cc1-508378baff3c collective             39.0   \n", "7022323c-90fa-4c46-819d-bc29f774deb8 webflow                14.0   \n", "7429eb40-e11d-4c2a-85ea-355a72befab4 webflow                63.0   \n", "745a36cc-5465-43b5-9a7d-c79588954df2 webflow               226.0   \n", "7574f7c8-7969-4561-8c0e-ace59dade919 observeinc              8.0   \n", "75f4c4c9-46c7-4f5b-ac5a-3f3bd76aa49d ampsortation          107.0   \n", "7771af49-7736-4db8-85a5-ab9f34b6ba16 webflow                35.0   \n", "78febf34-1e81-4803-b206-7eea0ff7f477 reveart               297.0   \n", "7a7b3326-8753-466d-a7be-3415e1833ece reveart               187.0   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing        296.0   \n", "7f4d97bd-2713-4e1f-8bfb-ea961f387c57 collective             20.0   \n", "8005e319-9983-4307-ae2e-4b07e17eb0e8 collective            144.0   \n", "828a6944-9363-4e0c-a527-234146c3d7df reveart               275.0   \n", "849a5e7b-de26-4c18-994e-c470d68c6579 webflow                22.0   \n", "88fa44d0-8d33-4f78-9158-80b8cd27abc0 observeinc            110.0   \n", "8d26a9a1-73b7-4382-8f7e-950c51fda41b collective             56.0   \n", "8d3fac81-b818-43bf-94ab-77e0bec56daf aitutor-mercor         38.0   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing        152.0   \n", "8e52c7c8-2542-4fe2-a32f-ac711a6cc52c montecarlodata         94.0   \n", "8e79ad4c-cd98-4315-883d-373eb0c59b5d collective            236.0   \n", "8e82342e-a3de-4495-92a9-bcf5bc8c14f6 webflow                17.0   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing        202.0   \n", "91f60597-e4c7-4791-9886-ef13bbaa76fd ampsortation           68.0   \n", "931d32fd-13e6-42ae-a6fd-24a36b8b0fe0 ampsortation          109.0   \n", "937d6be3-a292-4ed1-9dc0-930a266f2407 aitutor-mercor         54.0   \n", "940095a1-0791-41eb-8ed2-e3e488b794cf reveart                68.0   \n", "9453d128-66ea-4ad9-87db-4e4be2bb19f6 observeinc            146.0   \n", "946c15f0-b7f8-4f9b-a199-f8eeddaa0a37 observeinc              1.0   \n", "94ff144c-8158-4c43-a144-19829e615cb4 observeinc            134.0   \n", "99bd059b-242c-41de-94fb-172a423d5739 webflow               248.0   \n", "9d636f8c-b147-4f39-a4ce-345d2bcadcf4 webflow                49.0   \n", "9d66d83a-6a9b-4485-96d0-b56ad67e88c5 webflow                40.0   \n", "9e5083be-e7d9-43da-a325-d4ee20ff2b84 collective             28.0   \n", "9f2343ce-6d84-4ed9-ad39-e79126d94266 reveart                76.0   \n", "a063c637-4f19-4dbc-b1f1-3b188e975542 collective            215.0   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor         77.0   \n", "a1a3f2da-6b19-42dd-8d89-bf29e83d5614 reveart               104.0   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing         75.0   \n", "a4825044-2e1a-4a84-876c-bb294f9c4fe6 webflow               120.0   \n", "a5d5c9cd-07c8-483a-b98f-5ef0ff38ca55 webflow               160.0   \n", "a6a3c038-c989-44bf-ad9d-25de78e57bab observeinc             29.0   \n", "aa70db99-f918-4481-abf2-7156d4df6dc0 aitutor-mercor          1.0   \n", "ab0465d6-231d-4745-bcd5-aae304e1e8d7 observeinc              1.0   \n", "b16381ca-7897-4e8f-af9e-e06c1a29fa06 webflow                52.0   \n", "b4f90220-8a20-4772-970f-e6717a023ef9 webflow                41.0   \n", "b5b03034-2026-4788-9645-7200a6f56665 webflow                 7.0   \n", "b60b218d-93db-4dcc-af5a-9f7bf4a18091 observeinc             61.0   \n", "b6b01e5c-9c5f-4a07-8be9-319cf63b3c43 reveart               180.0   \n", "b6b20bf9-898f-4ed1-a3fc-a9ac1a3c2359 reveart               206.0   \n", "b892bc86-82cd-4c6c-b5db-f14896b99a63 aitutor-turing         21.0   \n", "b8f10a65-7be1-41ca-b927-4e9f329ec97e observeinc             21.0   \n", "bb37483c-e3eb-4fd5-a04c-470f8669e50a montecarlodata          5.0   \n", "bc5925dc-f1d5-4c69-bdf9-2ce2bc4c9ed1 aitutor-mercor          3.0   \n", "bd5d47d8-08c8-4c6b-b530-f02002945dd3 webflow                60.0   \n", "c192315a-2326-4d47-8624-0ae1a4551a22 webflow                59.0   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor        174.0   \n", "c2ea97ea-1357-4e7c-819c-a7ad36e7e53b webflow               445.0   \n", "c93bfbc9-25ac-41bb-a160-0ea9f943d5d0 observeinc             32.0   \n", "cb106dd0-be80-4c5f-a6ff-15892b5cc75c observeinc            335.0   \n", "cb245b75-9c24-4580-889a-05cf628772eb webflow                14.0   \n", "cbd27dbe-35e4-47f0-95ed-3cd74c7c4c90 webflow               102.0   \n", "cc33f315-e8d5-42b9-ab48-650d9ffbd05c aitutor-turing          0.0   \n", "d275d108-d606-4664-b3fe-ee19a3eb2172 observeinc            100.0   \n", "d2ba7763-9d93-498b-a666-018836168f1b webflow                62.0   \n", "d3726c31-14be-4fed-a7ff-f43d6dfc96f2 reveart               338.0   \n", "d543569f-22b4-4a83-a17c-c215e8bacec4 webflow                11.0   \n", "d56ecab6-90d7-4965-9cfa-0c4ca350e0cf aitutor-mercor          7.0   \n", "d7b45b8a-2174-455d-ba5c-d15ce4dfef67 observeinc             38.0   \n", "db4d2bd8-af3e-4c36-a88e-83af6fa3f823 webflow                82.0   \n", "dda067e0-4386-41e4-a3f1-94b34b747c6b webflow                21.0   \n", "df3f2c90-ad64-4c21-ab97-2c344a5d433f ampsortation           32.0   \n", "e059e296-a230-4e9c-9802-4c2322551b2f webflow                28.0   \n", "e367ecd3-83c4-4cd3-95a3-d4e3ca363aa6 webflow                20.0   \n", "e5121387-4ae4-461f-a3e5-3481eeb77d3e webflow               384.0   \n", "e52b7008-7a10-416d-8337-58cef6074311 aitutor-turing         19.0   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing        169.0   \n", "e65ddaf6-8703-4564-92d8-0b51e3af7bad collective            256.0   \n", "e6f926a1-bc09-4014-b516-6f437b26bd9c reveart               301.0   \n", "e800dc0b-1811-4536-9970-d993fe479e09 aitutor-turing          1.0   \n", "e97f62fd-d756-4c1f-aa05-1f7a72eee4c8 ampsortation            6.0   \n", "ea2228c4-bf6b-4c78-9f5c-765bd15f1e8f ampsortation          141.0   \n", "eae9980b-0534-4d1e-b07f-5254037e1db2 observeinc            330.0   \n", "eb5d15ed-fdb3-4a98-b0c7-bbf977d275a5 observeinc            105.0   \n", "ec4ba5f0-fffa-4a71-a3b2-80dd7c71e960 collective              7.0   \n", "ee518736-d428-4584-8425-0aa3401d4b13 webflow               279.0   \n", "eee017bd-5311-4934-916a-fbd8f139d9e7 montecarlodata        259.0   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing        100.0   \n", "f6f8b059-2ac7-4198-bad9-b9683af4eb86 webflow                 2.0   \n", "f861e701-d995-412f-ba41-ca3029e204b8 observeinc             66.0   \n", "f8dbfefe-d8ac-494b-b5f3-841e43055cbe reveart               310.0   \n", "f9c29095-b3c2-4664-a180-57cd69685b95 collective             70.0   \n", "fa46cab0-eef8-4a6e-8e9b-b66dacf657e0 observeinc             17.0   \n", "fa6c7d77-22f9-4e4a-a85c-dadebbc7a5af montecarlodata        213.0   \n", "fb4c2ce0-fbac-444e-be3f-3f07c7e9c61f webflow                20.0   \n", "fc06e9ef-b3d9-4505-8f62-3cd335814e6f ampsortation            1.0   \n", "fc93421e-e4fd-470c-bd5e-de33d629f0a2 webflow                85.0   \n", "fca46e88-bcb2-4601-a3e0-aaa02c6b8b22 montecarlodata        141.0   \n", "\n", "                                                     suggestion-hint-shown  \\\n", "session_id                           tenant                                  \n", "00d6ff68-99f0-4365-b817-2f9fb47715df collective                        142   \n", "010a135a-fcd3-49ab-85d7-3b85e3c2d8d7 webflow                            20   \n", "013a033a-45bb-4f4b-bcba-112beede877c observeinc                        395   \n", "01ea3250-0039-4c69-b635-f129f7c87950 observeinc                        394   \n", "085468cc-ff24-4d14-ae24-209332a9d6a1 observeinc                          1   \n", "0a8055ee-eb84-40f1-8104-a6d16fd6158d observeinc                        211   \n", "0c2cbb2a-8ab5-49fb-8ada-8d17134080ed observeinc                         28   \n", "0c485ae0-05b4-4e76-ae81-3a276b6dfa87 webflow                            61   \n", "10258db1-9e2e-4c55-9870-bf2025a70305 ampsortation                      105   \n", "103820d0-4a25-4587-9282-2e38b98c02ea reveart                           627   \n", "10cfc0d7-72e8-4b59-b15c-6039cff38c8b webflow                           915   \n", "11334beb-823e-4f84-8f61-8435f4afaaf4 webflow                             9   \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing                    143   \n", "134d006a-9cb9-4769-9c4b-612d77cc5f91 aitutor-mercor                     20   \n", "140bbbea-5b07-4e58-8b11-23915a201f90 observeinc                          2   \n", "17d6d858-abac-43de-8d3a-ff3d6b8b5c79 observeinc                        313   \n", "1a548214-62cd-459c-b820-358173b3af10 webflow                             3   \n", "1ad37a32-d9dd-43b3-96a3-bae4d07e36cf webflow                             2   \n", "1cc5b754-7411-4486-b8c5-2a26c3671bb0 collective                        168   \n", "20454f08-755a-4484-b203-b66092376375 ampsortation                      288   \n", "21653165-5a71-46d6-b2c8-4757206d236a aitutor-turing                     38   \n", "23852206-5b56-477f-b50d-44acfe195ce1 webflow                          1181   \n", "2463c696-9213-485b-91aa-a5a73782fba0 ampsortation                      105   \n", "25f21a16-c0cd-4559-9f47-383746508eb2 aitutor-turing                      1   \n", "26dfc75f-18b5-4e5a-81d2-62a2c8ac4537 observeinc                        605   \n", "2af3ff2e-2ff7-4c5e-92cc-ae1892cb43ed observeinc                       1072   \n", "2c7faade-5a26-4168-a804-92abe32cf58a collective                        681   \n", "2eb6183f-0b28-4761-b410-d441b1e7a7b9 webflow                            13   \n", "2f04b8d1-71fe-4208-996f-e670dd7dc22c aitutor-turing                      7   \n", "3575a9db-2b27-4bbe-b036-3ab93cacf358 observeinc                        150   \n", "35b70283-3e9b-4be6-89d2-1750cf22d1e6 webflow                            80   \n", "38a2307c-2aec-47c1-83bf-c641ee696ba3 ampsortation                      191   \n", "38ea880e-3a8a-42b6-825d-3fac712689e6 webflow                           216   \n", "3b2db685-968b-4b38-8f4a-244cdec1aa55 webflow                           515   \n", "3bf90d00-e133-4a38-a95b-419cc581fbdc montecarlodata                    140   \n", "3d73081a-1303-4c3a-a22f-06f711e41d16 webflow                            12   \n", "3f84aaaf-b22c-481b-b420-fbe5a1218701 webflow                           113   \n", "446301dc-2723-42c6-b098-6ef4b77a3f53 webflow                            15   \n", "46dfed66-c74d-46b8-b8cd-2917ca44b508 aitutor-mercor                     18   \n", "4a3a949e-1e3d-452f-b413-a4de573ffe18 webflow                           481   \n", "4b2995b6-637b-4331-9b71-ee11490e3dac webflow                            17   \n", "4e00679d-9829-4e93-9173-e38136ac5b69 ampsortation                      104   \n", "5001416d-9f16-4e8e-a824-8c725dbec307 webflow                           116   \n", "5017b0fd-26e1-42f8-8fff-f389a21f0a02 observeinc                         47   \n", "5232f278-141c-4ab2-8498-fb5fe155cf7a observeinc                        651   \n", "52bfa022-9e00-40a0-bb70-aba39d2b0967 webflow                            15   \n", "5aac6afa-7fc8-4792-9b41-b2731cf940b7 reveart                           222   \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor                    554   \n", "5fbb952d-28ce-4516-a69f-64f7ab9c0014 aitutor-mercor                     26   \n", "638e1453-e267-43d0-b7d4-7ebe00881570 observeinc                          7   \n", "64eb51d8-18b0-4ab6-9714-d9c7435def20 webflow                           204   \n", "6819915b-a8f9-414e-a874-9e6c093a3d24 reveart                           511   \n", "6d7e015c-4d7c-4f6f-be97-f46ed03fff05 observeinc                        154   \n", "6e2b7743-2f2d-421c-9cc1-508378baff3c collective                         91   \n", "7022323c-90fa-4c46-819d-bc29f774deb8 webflow                            16   \n", "7429eb40-e11d-4c2a-85ea-355a72befab4 webflow                           501   \n", "745a36cc-5465-43b5-9a7d-c79588954df2 webflow                           328   \n", "7574f7c8-7969-4561-8c0e-ace59dade919 observeinc                          6   \n", "75f4c4c9-46c7-4f5b-ac5a-3f3bd76aa49d ampsortation                      241   \n", "7771af49-7736-4db8-85a5-ab9f34b6ba16 webflow                            62   \n", "78febf34-1e81-4803-b206-7eea0ff7f477 reveart                           580   \n", "7a7b3326-8753-466d-a7be-3415e1833ece reveart                           320   \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing                   1278   \n", "7f4d97bd-2713-4e1f-8bfb-ea961f387c57 collective                         65   \n", "8005e319-9983-4307-ae2e-4b07e17eb0e8 collective                        362   \n", "828a6944-9363-4e0c-a527-234146c3d7df reveart                           768   \n", "849a5e7b-de26-4c18-994e-c470d68c6579 webflow                            37   \n", "88fa44d0-8d33-4f78-9158-80b8cd27abc0 observeinc                        120   \n", "8d26a9a1-73b7-4382-8f7e-950c51fda41b collective                        124   \n", "8d3fac81-b818-43bf-94ab-77e0bec56daf aitutor-mercor                     40   \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing                    334   \n", "8e52c7c8-2542-4fe2-a32f-ac711a6cc52c montecarlodata                    200   \n", "8e79ad4c-cd98-4315-883d-373eb0c59b5d collective                        406   \n", "8e82342e-a3de-4495-92a9-bcf5bc8c14f6 webflow                            34   \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing                    324   \n", "91f60597-e4c7-4791-9886-ef13bbaa76fd ampsortation                      134   \n", "931d32fd-13e6-42ae-a6fd-24a36b8b0fe0 ampsortation                      198   \n", "937d6be3-a292-4ed1-9dc0-930a266f2407 aitutor-mercor                     76   \n", "940095a1-0791-41eb-8ed2-e3e488b794cf reveart                           209   \n", "9453d128-66ea-4ad9-87db-4e4be2bb19f6 observeinc                        239   \n", "946c15f0-b7f8-4f9b-a199-f8eeddaa0a37 observeinc                          9   \n", "94ff144c-8158-4c43-a144-19829e615cb4 observeinc                        275   \n", "99bd059b-242c-41de-94fb-172a423d5739 webflow                           288   \n", "9d636f8c-b147-4f39-a4ce-345d2bcadcf4 webflow                           210   \n", "9d66d83a-6a9b-4485-96d0-b56ad67e88c5 webflow                            64   \n", "9e5083be-e7d9-43da-a325-d4ee20ff2b84 collective                        160   \n", "9f2343ce-6d84-4ed9-ad39-e79126d94266 reveart                           231   \n", "a063c637-4f19-4dbc-b1f1-3b188e975542 collective                        479   \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor                    157   \n", "a1a3f2da-6b19-42dd-8d89-bf29e83d5614 reveart                           343   \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing                    138   \n", "a4825044-2e1a-4a84-876c-bb294f9c4fe6 webflow                           197   \n", "a5d5c9cd-07c8-483a-b98f-5ef0ff38ca55 webflow                           376   \n", "a6a3c038-c989-44bf-ad9d-25de78e57bab observeinc                         26   \n", "aa70db99-f918-4481-abf2-7156d4df6dc0 aitutor-mercor                      1   \n", "ab0465d6-231d-4745-bcd5-aae304e1e8d7 observeinc                          1   \n", "b16381ca-7897-4e8f-af9e-e06c1a29fa06 webflow                           143   \n", "b4f90220-8a20-4772-970f-e6717a023ef9 webflow                            72   \n", "b5b03034-2026-4788-9645-7200a6f56665 webflow                            12   \n", "b60b218d-93db-4dcc-af5a-9f7bf4a18091 observeinc                        133   \n", "b6b01e5c-9c5f-4a07-8be9-319cf63b3c43 reveart                           659   \n", "b6b20bf9-898f-4ed1-a3fc-a9ac1a3c2359 reveart                           474   \n", "b892bc86-82cd-4c6c-b5db-f14896b99a63 aitutor-turing                     29   \n", "b8f10a65-7be1-41ca-b927-4e9f329ec97e observeinc                         33   \n", "bb37483c-e3eb-4fd5-a04c-470f8669e50a montecarlodata                     12   \n", "bc5925dc-f1d5-4c69-bdf9-2ce2bc4c9ed1 aitutor-mercor                     29   \n", "bd5d47d8-08c8-4c6b-b530-f02002945dd3 webflow                           131   \n", "c192315a-2326-4d47-8624-0ae1a4551a22 webflow                           103   \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor                    324   \n", "c2ea97ea-1357-4e7c-819c-a7ad36e7e53b webflow                           633   \n", "c93bfbc9-25ac-41bb-a160-0ea9f943d5d0 observeinc                        174   \n", "cb106dd0-be80-4c5f-a6ff-15892b5cc75c observeinc                        544   \n", "cb245b75-9c24-4580-889a-05cf628772eb webflow                            22   \n", "cbd27dbe-35e4-47f0-95ed-3cd74c7c4c90 webflow                           254   \n", "cc33f315-e8d5-42b9-ab48-650d9ffbd05c aitutor-turing                      1   \n", "d275d108-d606-4664-b3fe-ee19a3eb2172 observeinc                        167   \n", "d2ba7763-9d93-498b-a666-018836168f1b webflow                           153   \n", "d3726c31-14be-4fed-a7ff-f43d6dfc96f2 reveart                           331   \n", "d543569f-22b4-4a83-a17c-c215e8bacec4 webflow                            16   \n", "d56ecab6-90d7-4965-9cfa-0c4ca350e0cf aitutor-mercor                     25   \n", "d7b45b8a-2174-455d-ba5c-d15ce4dfef67 observeinc                         63   \n", "db4d2bd8-af3e-4c36-a88e-83af6fa3f823 webflow                           145   \n", "dda067e0-4386-41e4-a3f1-94b34b747c6b webflow                            58   \n", "df3f2c90-ad64-4c21-ab97-2c344a5d433f ampsortation                       24   \n", "e059e296-a230-4e9c-9802-4c2322551b2f webflow                            55   \n", "e367ecd3-83c4-4cd3-95a3-d4e3ca363aa6 webflow                            37   \n", "e5121387-4ae4-461f-a3e5-3481eeb77d3e webflow                           910   \n", "e52b7008-7a10-416d-8337-58cef6074311 aitutor-turing                     38   \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing                    182   \n", "e65ddaf6-8703-4564-92d8-0b51e3af7bad collective                        568   \n", "e6f926a1-bc09-4014-b516-6f437b26bd9c reveart                           552   \n", "e800dc0b-1811-4536-9970-d993fe479e09 aitutor-turing                      8   \n", "e97f62fd-d756-4c1f-aa05-1f7a72eee4c8 ampsortation                       21   \n", "ea2228c4-bf6b-4c78-9f5c-765bd15f1e8f ampsortation                      364   \n", "eae9980b-0534-4d1e-b07f-5254037e1db2 observeinc                        684   \n", "eb5d15ed-fdb3-4a98-b0c7-bbf977d275a5 observeinc                        125   \n", "ec4ba5f0-fffa-4a71-a3b2-80dd7c71e960 collective                         11   \n", "ee518736-d428-4584-8425-0aa3401d4b13 webflow                           566   \n", "eee017bd-5311-4934-916a-fbd8f139d9e7 montecarlodata                    461   \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing                    141   \n", "f6f8b059-2ac7-4198-bad9-b9683af4eb86 webflow                             9   \n", "f861e701-d995-412f-ba41-ca3029e204b8 observeinc                        110   \n", "f8dbfefe-d8ac-494b-b5f3-841e43055cbe reveart                           589   \n", "f9c29095-b3c2-4664-a180-57cd69685b95 collective                        153   \n", "fa46cab0-eef8-4a6e-8e9b-b66dacf657e0 observeinc                         20   \n", "fa6c7d77-22f9-4e4a-a85c-dadebbc7a5af montecarlodata                    439   \n", "fb4c2ce0-fbac-444e-be3f-3f07c7e9c61f webflow                            71   \n", "fc06e9ef-b3d9-4505-8f62-3cd335814e6f ampsortation                        1   \n", "fc93421e-e4fd-470c-bd5e-de33d629f0a2 webflow                           180   \n", "fca46e88-bcb2-4601-a3e0-aaa02c6b8b22 montecarlodata                    269   \n", "\n", "                                                     accept_rate  hover_rate  \n", "session_id                           tenant                                   \n", "00d6ff68-99f0-4365-b817-2f9fb47715df collective         0.000000    0.760563  \n", "010a135a-fcd3-49ab-85d7-3b85e3c2d8d7 webflow            0.000000    0.450000  \n", "013a033a-45bb-4f4b-bcba-112beede877c observeinc         0.382812    0.648101  \n", "01ea3250-0039-4c69-b635-f129f7c87950 observeinc         0.042945    0.413706  \n", "085468cc-ff24-4d14-ae24-209332a9d6a1 observeinc         0.000000    0.000000  \n", "0a8055ee-eb84-40f1-8104-a6d16fd6158d observeinc         0.008929    0.530806  \n", "0c2cbb2a-8ab5-49fb-8ada-8d17134080ed observeinc         0.000000    0.535714  \n", "0c485ae0-05b4-4e76-ae81-3a276b6dfa87 webflow            0.000000    0.377049  \n", "10258db1-9e2e-4c55-9870-bf2025a70305 ampsortation       0.011494    0.828571  \n", "103820d0-4a25-4587-9282-2e38b98c02ea reveart            0.240602    0.424242  \n", "10cfc0d7-72e8-4b59-b15c-6039cff38c8b webflow            0.280669    0.587978  \n", "11334beb-823e-4f84-8f61-8435f4afaaf4 webflow            0.000000    0.666667  \n", "12643ca8-115c-4e0c-bd2b-389b3e742a3c aitutor-turing     0.019231    0.363636  \n", "134d006a-9cb9-4769-9c4b-612d77cc5f91 aitutor-mercor     0.000000    0.000000  \n", "140bbbea-5b07-4e58-8b11-23915a201f90 observeinc         1.000000    0.500000  \n", "17d6d858-abac-43de-8d3a-ff3d6b8b5c79 observeinc         0.035211    0.453674  \n", "1a548214-62cd-459c-b820-358173b3af10 webflow            0.000000    0.333333  \n", "1ad37a32-d9dd-43b3-96a3-bae4d07e36cf webflow            0.000000    1.000000  \n", "1cc5b754-7411-4486-b8c5-2a26c3671bb0 collective         0.349206    0.375000  \n", "20454f08-755a-4484-b203-b66092376375 ampsortation       0.060241    0.288194  \n", "21653165-5a71-46d6-b2c8-4757206d236a aitutor-turing     0.000000    0.473684  \n", "23852206-5b56-477f-b50d-44acfe195ce1 webflow            0.106343    0.453853  \n", "2463c696-9213-485b-91aa-a5a73782fba0 ampsortation       0.071429    0.800000  \n", "25f21a16-c0cd-4559-9f47-383746508eb2 aitutor-turing     0.000000    0.000000  \n", "26dfc75f-18b5-4e5a-81d2-62a2c8ac4537 observeinc         0.009091    0.181818  \n", "2af3ff2e-2ff7-4c5e-92cc-ae1892cb43ed observeinc         0.000000    0.240672  \n", "2c7faade-5a26-4168-a804-92abe32cf58a collective         0.008621    0.511013  \n", "2eb6183f-0b28-4761-b410-d441b1e7a7b9 webflow            0.000000    1.076923  \n", "2f04b8d1-71fe-4208-996f-e670dd7dc22c aitutor-turing     0.000000    0.000000  \n", "3575a9db-2b27-4bbe-b036-3ab93cacf358 observeinc         0.000000    0.533333  \n", "35b70283-3e9b-4be6-89d2-1750cf22d1e6 webflow            0.116279    0.537500  \n", "38a2307c-2aec-47c1-83bf-c641ee696ba3 ampsortation       0.000000    0.429319  \n", "38ea880e-3a8a-42b6-825d-3fac712689e6 webflow            0.456376    0.689815  \n", "3b2db685-968b-4b38-8f4a-244cdec1aa55 webflow            0.040441    0.528155  \n", "3bf90d00-e133-4a38-a95b-419cc581fbdc montecarlodata     0.435644    0.721429  \n", "3d73081a-1303-4c3a-a22f-06f711e41d16 webflow            0.500000    0.666667  \n", "3f84aaaf-b22c-481b-b420-fbe5a1218701 webflow            0.076923    0.345133  \n", "446301dc-2723-42c6-b098-6ef4b77a3f53 webflow            0.000000    0.733333  \n", "46dfed66-c74d-46b8-b8cd-2917ca44b508 aitutor-mercor     0.400000    0.555556  \n", "4a3a949e-1e3d-452f-b413-a4de573ffe18 webflow            0.184358    0.744283  \n", "4b2995b6-637b-4331-9b71-ee11490e3dac webflow            0.000000    0.470588  \n", "4e00679d-9829-4e93-9173-e38136ac5b69 ampsortation       0.083333    0.230769  \n", "5001416d-9f16-4e8e-a824-8c725dbec307 webflow            0.037500    0.689655  \n", "5017b0fd-26e1-42f8-8fff-f389a21f0a02 observeinc         0.000000    0.382979  \n", "5232f278-141c-4ab2-8498-fb5fe155cf7a observeinc         0.215686    0.470046  \n", "52bfa022-9e00-40a0-bb70-aba39d2b0967 webflow            0.000000    1.800000  \n", "5aac6afa-7fc8-4792-9b41-b2731cf940b7 reveart            0.029851    0.603604  \n", "5f6583a2-29a0-48df-a713-f4a1559151f3 aitutor-mercor     0.000000    0.182310  \n", "5fbb952d-28ce-4516-a69f-64f7ab9c0014 aitutor-mercor     0.000000    0.192308  \n", "638e1453-e267-43d0-b7d4-7ebe00881570 observeinc         1.000000    0.571429  \n", "64eb51d8-18b0-4ab6-9714-d9c7435def20 webflow            0.088235    0.500000  \n", "6819915b-a8f9-414e-a874-9e6c093a3d24 reveart            0.253456    0.424658  \n", "6d7e015c-4d7c-4f6f-be97-f46ed03fff05 observeinc         0.317308    0.675325  \n", "6e2b7743-2f2d-421c-9cc1-508378baff3c collective         0.000000    0.428571  \n", "7022323c-90fa-4c46-819d-bc29f774deb8 webflow            0.000000    0.875000  \n", "7429eb40-e11d-4c2a-85ea-355a72befab4 webflow            0.269841    0.125749  \n", "745a36cc-5465-43b5-9a7d-c79588954df2 webflow            0.004425    0.689024  \n", "7574f7c8-7969-4561-8c0e-ace59dade919 observeinc         0.000000    1.333333  \n", "75f4c4c9-46c7-4f5b-ac5a-3f3bd76aa49d ampsortation       0.046729    0.443983  \n", "7771af49-7736-4db8-85a5-ab9f34b6ba16 webflow            0.028571    0.564516  \n", "78febf34-1e81-4803-b206-7eea0ff7f477 reveart            0.141414    0.512069  \n", "7a7b3326-8753-466d-a7be-3415e1833ece reveart            0.235294    0.584375  \n", "7b11013b-a604-47eb-9e0c-a4ac0d062661 aitutor-turing     0.020270    0.231612  \n", "7f4d97bd-2713-4e1f-8bfb-ea961f387c57 collective         0.400000    0.307692  \n", "8005e319-9983-4307-ae2e-4b07e17eb0e8 collective         0.465278    0.397790  \n", "828a6944-9363-4e0c-a527-234146c3d7df reveart            0.578182    0.358073  \n", "849a5e7b-de26-4c18-994e-c470d68c6579 webflow            0.136364    0.594595  \n", "88fa44d0-8d33-4f78-9158-80b8cd27abc0 observeinc         0.290909    0.916667  \n", "8d26a9a1-73b7-4382-8f7e-950c51fda41b collective         0.089286    0.451613  \n", "8d3fac81-b818-43bf-94ab-77e0bec56daf aitutor-mercor     0.894737    0.950000  \n", "8dfdfaca-4f02-45db-947e-bd4f300198f0 aitutor-turing     0.006579    0.455090  \n", "8e52c7c8-2542-4fe2-a32f-ac711a6cc52c montecarlodata     0.053191    0.470000  \n", "8e79ad4c-cd98-4315-883d-373eb0c59b5d collective         0.118644    0.581281  \n", "8e82342e-a3de-4495-92a9-bcf5bc8c14f6 webflow            0.000000    0.500000  \n", "9130182f-9cfd-4d98-935a-7533fc45877c aitutor-turing     0.475248    0.623457  \n", "91f60597-e4c7-4791-9886-ef13bbaa76fd ampsortation       0.044118    0.507463  \n", "931d32fd-13e6-42ae-a6fd-24a36b8b0fe0 ampsortation       0.229358    0.550505  \n", "937d6be3-a292-4ed1-9dc0-930a266f2407 aitutor-mercor     0.629630    0.710526  \n", "940095a1-0791-41eb-8ed2-e3e488b794cf reveart            0.000000    0.325359  \n", "9453d128-66ea-4ad9-87db-4e4be2bb19f6 observeinc         0.027397    0.610879  \n", "946c15f0-b7f8-4f9b-a199-f8eeddaa0a37 observeinc         0.000000    0.111111  \n", "94ff144c-8158-4c43-a144-19829e615cb4 observeinc         0.007463    0.487273  \n", "99bd059b-242c-41de-94fb-172a423d5739 webflow            0.008065    0.861111  \n", "9d636f8c-b147-4f39-a4ce-345d2bcadcf4 webflow            0.081633    0.233333  \n", "9d66d83a-6a9b-4485-96d0-b56ad67e88c5 webflow            0.100000    0.625000  \n", "9e5083be-e7d9-43da-a325-d4ee20ff2b84 collective         0.000000    0.175000  \n", "9f2343ce-6d84-4ed9-ad39-e79126d94266 reveart            0.000000    0.329004  \n", "a063c637-4f19-4dbc-b1f1-3b188e975542 collective         0.162791    0.448852  \n", "a07d8a9f-ea88-4b9c-9e0c-7eb63d489c95 aitutor-mercor     0.051948    0.490446  \n", "a1a3f2da-6b19-42dd-8d89-bf29e83d5614 reveart            0.307692    0.303207  \n", "a39a2e34-a02d-4b28-b9a3-fe303700c680 aitutor-turing     0.466667    0.543478  \n", "a4825044-2e1a-4a84-876c-bb294f9c4fe6 webflow            0.066667    0.609137  \n", "a5d5c9cd-07c8-483a-b98f-5ef0ff38ca55 webflow            0.000000    0.425532  \n", "a6a3c038-c989-44bf-ad9d-25de78e57bab observeinc         0.000000    1.115385  \n", "aa70db99-f918-4481-abf2-7156d4df6dc0 aitutor-mercor     0.000000    1.000000  \n", "ab0465d6-231d-4745-bcd5-aae304e1e8d7 observeinc         0.000000    1.000000  \n", "b16381ca-7897-4e8f-af9e-e06c1a29fa06 webflow            0.000000    0.363636  \n", "b4f90220-8a20-4772-970f-e6717a023ef9 webflow            0.024390    0.569444  \n", "b5b03034-2026-4788-9645-7200a6f56665 webflow            0.571429    0.583333  \n", "b60b218d-93db-4dcc-af5a-9f7bf4a18091 observeinc         0.065574    0.458647  \n", "b6b01e5c-9c5f-4a07-8be9-319cf63b3c43 reveart            0.094444    0.273141  \n", "b6b20bf9-898f-4ed1-a3fc-a9ac1a3c2359 reveart            0.053398    0.434599  \n", "b892bc86-82cd-4c6c-b5db-f14896b99a63 aitutor-turing     0.047619    0.724138  \n", "b8f10a65-7be1-41ca-b927-4e9f329ec97e observeinc         0.380952    0.636364  \n", "bb37483c-e3eb-4fd5-a04c-470f8669e50a montecarlodata     0.000000    0.416667  \n", "bc5925dc-f1d5-4c69-bdf9-2ce2bc4c9ed1 aitutor-mercor     0.000000    0.103448  \n", "bd5d47d8-08c8-4c6b-b530-f02002945dd3 webflow            0.000000    0.458015  \n", "c192315a-2326-4d47-8624-0ae1a4551a22 webflow            0.101695    0.572816  \n", "c1bb7492-5fca-4c85-84ff-9cb7bc2a209c aitutor-mercor     0.327586    0.537037  \n", "c2ea97ea-1357-4e7c-819c-a7ad36e7e53b webflow            0.040449    0.703002  \n", "c93bfbc9-25ac-41bb-a160-0ea9f943d5d0 observeinc         0.156250    0.183908  \n", "cb106dd0-be80-4c5f-a6ff-15892b5cc75c observeinc         0.000000    0.615809  \n", "cb245b75-9c24-4580-889a-05cf628772eb webflow            0.285714    0.636364  \n", "cbd27dbe-35e4-47f0-95ed-3cd74c7c4c90 webflow            0.000000    0.401575  \n", "cc33f315-e8d5-42b9-ab48-650d9ffbd05c aitutor-turing     0.000000    0.000000  \n", "d275d108-d606-4664-b3fe-ee19a3eb2172 observeinc         0.000000    0.598802  \n", "d2ba7763-9d93-498b-a666-018836168f1b webflow            0.016129    0.405229  \n", "d3726c31-14be-4fed-a7ff-f43d6dfc96f2 reveart            0.008876    1.021148  \n", "d543569f-22b4-4a83-a17c-c215e8bacec4 webflow            0.454545    0.687500  \n", "d56ecab6-90d7-4965-9cfa-0c4ca350e0cf aitutor-mercor     0.000000    0.280000  \n", "d7b45b8a-2174-455d-ba5c-d15ce4dfef67 observeinc         0.000000    0.603175  \n", "db4d2bd8-af3e-4c36-a88e-83af6fa3f823 webflow            0.000000    0.565517  \n", "dda067e0-4386-41e4-a3f1-94b34b747c6b webflow            0.095238    0.362069  \n", "df3f2c90-ad64-4c21-ab97-2c344a5d433f ampsortation       0.218750    1.333333  \n", "e059e296-a230-4e9c-9802-4c2322551b2f webflow            0.000000    0.509091  \n", "e367ecd3-83c4-4cd3-95a3-d4e3ca363aa6 webflow            0.300000    0.540541  \n", "e5121387-4ae4-461f-a3e5-3481eeb77d3e webflow            0.369792    0.421978  \n", "e52b7008-7a10-416d-8337-58cef6074311 aitutor-turing     0.000000    0.500000  \n", "e5983c8b-880c-4e68-a3cf-e12f349e9365 aitutor-turing     0.863905    0.928571  \n", "e65ddaf6-8703-4564-92d8-0b51e3af7bad collective         0.007812    0.450704  \n", "e6f926a1-bc09-4014-b516-6f437b26bd9c reveart            0.036545    0.545290  \n", "e800dc0b-1811-4536-9970-d993fe479e09 aitutor-turing     0.000000    0.125000  \n", "e97f62fd-d756-4c1f-aa05-1f7a72eee4c8 ampsortation       0.666667    0.285714  \n", "ea2228c4-bf6b-4c78-9f5c-765bd15f1e8f ampsortation       0.042553    0.387363  \n", "eae9980b-0534-4d1e-b07f-5254037e1db2 observeinc         0.000000    0.482456  \n", "eb5d15ed-fdb3-4a98-b0c7-bbf977d275a5 observeinc         0.323810    0.840000  \n", "ec4ba5f0-fffa-4a71-a3b2-80dd7c71e960 collective         0.000000    0.636364  \n", "ee518736-d428-4584-8425-0aa3401d4b13 webflow            0.060932    0.492933  \n", "eee017bd-5311-4934-916a-fbd8f139d9e7 montecarlodata     0.104247    0.561822  \n", "f49b1daa-f0c1-4ef5-bf3b-f9a3ac2578b0 aitutor-turing     0.950000    0.709220  \n", "f6f8b059-2ac7-4198-bad9-b9683af4eb86 webflow            1.000000    0.222222  \n", "f861e701-d995-412f-ba41-ca3029e204b8 observeinc         0.272727    0.600000  \n", "f8dbfefe-d8ac-494b-b5f3-841e43055cbe reveart            0.006452    0.526316  \n", "f9c29095-b3c2-4664-a180-57cd69685b95 collective         0.042857    0.457516  \n", "fa46cab0-eef8-4a6e-8e9b-b66dacf657e0 observeinc         0.000000    0.850000  \n", "fa6c7d77-22f9-4e4a-a85c-dadebbc7a5af montecarlodata     0.159624    0.485194  \n", "fb4c2ce0-fbac-444e-be3f-3f07c7e9c61f webflow            0.250000    0.281690  \n", "fc06e9ef-b3d9-4505-8f62-3cd335814e6f ampsortation       0.000000    1.000000  \n", "fc93421e-e4fd-470c-bd5e-de33d629f0a2 webflow            0.035294    0.472222  \n", "fca46e88-bcb2-4601-a3e0-aaa02c6b8b22 montecarlodata     0.156028    0.524164  "]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["df_user_wave1.groupby([\"session_id\", \"tenant\"]).sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Per Langauge Usages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DogFood"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT\n", "  LANGUAGE,\n", "  ARRAY_AGG(event_name) AS event_name,\n", "  ARRAY_AGG(count) AS count,\n", "FROM (\n", "  SELECT\n", "    LANGUAGE,\n", "    event_name,\n", "    COUNT(DISTINCT sid) AS count\n", "  FROM (\n", "  WITH\n", "      REQUEST AS (\n", "      SELECT\n", "        request_id,\n", "        COALESCE(JSON_EXTRACT_SCALAR(sanitized_json, \"$.request.lang\"), \"null\") AS LANGUAGE,\n", "      FROM `us_staging_request_insight_analytics_dataset.next_edit_host_request`\n", "      WHERE DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) ),\n", "      SESSION AS (\n", "      SELECT\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "      FROM `us_staging_request_insight_analytics_dataset.next_edit_session_event`\n", "      WHERE tenant=@tenant\n", "        AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") IN (\"accept\", \"hover-shown\", \"suggestion-hint-shown\", \"reject\")\n", "        AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "      METADATA AS (\n", "      SELECT request_id, user_id, user_agent,\n", "      FROM `us_staging_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant = 'dogfood-shard'\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON>ty\"))\n", "    SELECT\n", "      SESSION.event_name,\n", "      SESSION.sid,\n", "      REQUEST.LANGUAGE\n", "    FROM SESSION\n", "    JOIN METADATA\n", "    USING (request_id)\n", "    JOIN REQUEST\n", "    USING (request_id)\n", "  )\n", "  GROUP BY LANGUAGE, event_name )\n", "GROUP BY LANGUAGE\n", "\"\"\"\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(\n", "    QUERY,\n", "    page_size=128,\n", "    job_config=bigquery.QueryJobConfig(\n", "        query_parameters=[\n", "            bigquery.ScalarQueryParameter(\n", "                name=\"tenant\", type_=\"STRING\", value=\"dogfood-shard\"\n", "            )\n", "        ]\n", "    ),\n", ")\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hove_shown_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>LANGUAGE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>svelte</th>\n", "      <td>77.0</td>\n", "      <td>5.0</td>\n", "      <td>615.0</td>\n", "      <td>1415.0</td>\n", "      <td>0.125203</td>\n", "      <td>0.434629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>typescript</th>\n", "      <td>302.0</td>\n", "      <td>5.0</td>\n", "      <td>1166.0</td>\n", "      <td>1999.0</td>\n", "      <td>0.259005</td>\n", "      <td>0.583292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>jsonnet</th>\n", "      <td>99.0</td>\n", "      <td>7.0</td>\n", "      <td>342.0</td>\n", "      <td>736.0</td>\n", "      <td>0.289474</td>\n", "      <td>0.464674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rust</th>\n", "      <td>59.0</td>\n", "      <td>1.0</td>\n", "      <td>183.0</td>\n", "      <td>280.0</td>\n", "      <td>0.322404</td>\n", "      <td>0.653571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>python</th>\n", "      <td>1016.0</td>\n", "      <td>91.0</td>\n", "      <td>2965.0</td>\n", "      <td>4971.0</td>\n", "      <td>0.342664</td>\n", "      <td>0.596459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>typescriptreact</th>\n", "      <td>79.0</td>\n", "      <td>1.0</td>\n", "      <td>225.0</td>\n", "      <td>528.0</td>\n", "      <td>0.351111</td>\n", "      <td>0.426136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>go</th>\n", "      <td>414.0</td>\n", "      <td>13.0</td>\n", "      <td>924.0</td>\n", "      <td>1296.0</td>\n", "      <td>0.448052</td>\n", "      <td>0.712963</td>\n", "    </tr>\n", "    <tr>\n", "      <th>starlark</th>\n", "      <td>46.0</td>\n", "      <td>1.0</td>\n", "      <td>101.0</td>\n", "      <td>166.0</td>\n", "      <td>0.455446</td>\n", "      <td>0.608434</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 accept  reject  hover-shown  suggestion-hint-shown  \\\n", "LANGUAGE                                                              \n", "svelte             77.0     5.0        615.0                 1415.0   \n", "typescript        302.0     5.0       1166.0                 1999.0   \n", "jsonnet            99.0     7.0        342.0                  736.0   \n", "rust               59.0     1.0        183.0                  280.0   \n", "python           1016.0    91.0       2965.0                 4971.0   \n", "typescriptreact    79.0     1.0        225.0                  528.0   \n", "go                414.0    13.0        924.0                 1296.0   \n", "starlark           46.0     1.0        101.0                  166.0   \n", "\n", "                 accept_rate  hove_shown_rate  \n", "LANGUAGE                                       \n", "svelte              0.125203         0.434629  \n", "typescript          0.259005         0.583292  \n", "jsonnet             0.289474         0.464674  \n", "rust                0.322404         0.653571  \n", "python              0.342664         0.596459  \n", "typescriptreact     0.351111         0.426136  \n", "go                  0.448052         0.712963  \n", "starlark            0.455446         0.608434  "]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}], "source": ["def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row.event_name, row.count)}\n", "    output[\"LANGUAGE\"] = row.LANGUAGE\n", "    return output\n", "\n", "\n", "df_lang_dogfood = pd.DataFrame([row_to_dict(d) for d in data])\n", "df_lang_dogfood.set_index(\"LANGUAGE\", inplace=True)\n", "df_lang_dogfood.fillna(0, inplace=True)\n", "df_lang_dogfood[\"accept_rate\"] = df_lang_dogfood[\"accept\"] / df_lang_dogfood[\"hover-shown\"]\n", "df_lang_dogfood[\"hove_shown_rate\"] = df_lang_dogfood[\"hover-shown\"] / df_lang_dogfood[\"suggestion-hint-shown\"]\n", "df_lang_dogfood[\n", "    [\n", "        \"accept\",\n", "        \"reject\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "        \"accept_rate\",\n", "        \"hove_shown_rate\",\n", "    ]\n", "][table[\"suggestion-hint-shown\"] > 100].sort_values(\"accept_rate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vanguard"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT\n", "  LANGUAGE,\n", "  ARRAY_AGG(event_name) AS event_name,\n", "  ARRAY_AGG(count) AS count,\n", "FROM (\n", "  SELECT\n", "    LANGUAGE,\n", "    event_name,\n", "    COUNT(DISTINCT sid) AS count\n", "  FROM (\n", "  WITH\n", "      REQUEST AS (\n", "      SELECT\n", "        request_id,\n", "        COALESCE(JSON_EXTRACT_SCALAR(sanitized_json, \"$.request.lang\"), \"null\") AS LANGUAGE,\n", "      FROM `us_prod_request_insight_analytics_dataset.next_edit_host_request`\n", "      WHERE DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) ),\n", "      SESSION AS (\n", "      SELECT\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "      FROM `us_prod_request_insight_analytics_dataset.next_edit_session_event`\n", "      WHERE tenant=@tenant\n", "        AND JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") IN (\"accept\", \"hover-shown\", \"suggestion-hint-shown\", \"reject\")\n", "        AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "      METADATA AS (\n", "      SELECT request_id, user_id, user_agent,\n", "      FROM `us_prod_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant =@tenant\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON>ty\"))\n", "    SELECT\n", "      SESSION.event_name,\n", "      SESSION.sid,\n", "      REQUEST.LANGUAGE\n", "    FROM SESSION\n", "    JOIN METADATA\n", "    USING (request_id)\n", "    JOIN REQUEST\n", "    USING (request_id)\n", "  )\n", "  GROUP BY LANGUAGE, event_name )\n", "GROUP BY LANGUAGE\n", "\"\"\"\n", "\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(\n", "    QUERY,\n", "    page_size=128,\n", "    job_config=bigquery.QueryJobConfig(\n", "        query_parameters=[\n", "            bigquery.ScalarQueryParameter(\n", "                name=\"tenant\", type_=\"STRING\", value=\"i0-vanguard0\"\n", "            )\n", "        ]\n", "    ),\n", ")\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accept</th>\n", "      <th>reject</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "      <th>accept_rate</th>\n", "      <th>hove_shown_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>LANGUAGE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>starlark</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>48.0</td>\n", "      <td>107</td>\n", "      <td>0.000000</td>\n", "      <td>0.448598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dart</th>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>187.0</td>\n", "      <td>484</td>\n", "      <td>0.010695</td>\n", "      <td>0.386364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>typescriptreact</th>\n", "      <td>34.0</td>\n", "      <td>19.0</td>\n", "      <td>753.0</td>\n", "      <td>1902</td>\n", "      <td>0.045153</td>\n", "      <td>0.395899</td>\n", "    </tr>\n", "    <tr>\n", "      <th>markdown</th>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>88.0</td>\n", "      <td>208</td>\n", "      <td>0.068182</td>\n", "      <td>0.423077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>python</th>\n", "      <td>87.0</td>\n", "      <td>14.0</td>\n", "      <td>1104.0</td>\n", "      <td>2382</td>\n", "      <td>0.078804</td>\n", "      <td>0.463476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>html</th>\n", "      <td>17.0</td>\n", "      <td>0.0</td>\n", "      <td>122.0</td>\n", "      <td>159</td>\n", "      <td>0.139344</td>\n", "      <td>0.767296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>css</th>\n", "      <td>12.0</td>\n", "      <td>1.0</td>\n", "      <td>74.0</td>\n", "      <td>201</td>\n", "      <td>0.162162</td>\n", "      <td>0.368159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rust</th>\n", "      <td>78.0</td>\n", "      <td>2.0</td>\n", "      <td>353.0</td>\n", "      <td>686</td>\n", "      <td>0.220963</td>\n", "      <td>0.514577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>yaml</th>\n", "      <td>8.0</td>\n", "      <td>4.0</td>\n", "      <td>35.0</td>\n", "      <td>111</td>\n", "      <td>0.228571</td>\n", "      <td>0.315315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>typescript</th>\n", "      <td>373.0</td>\n", "      <td>15.0</td>\n", "      <td>1512.0</td>\n", "      <td>2299</td>\n", "      <td>0.246693</td>\n", "      <td>0.657677</td>\n", "    </tr>\n", "    <tr>\n", "      <th>javascript</th>\n", "      <td>59.0</td>\n", "      <td>0.0</td>\n", "      <td>158.0</td>\n", "      <td>241</td>\n", "      <td>0.373418</td>\n", "      <td>0.655602</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 accept  reject  hover-shown  suggestion-hint-shown  \\\n", "LANGUAGE                                                              \n", "starlark            0.0     0.0         48.0                    107   \n", "dart                2.0     0.0        187.0                    484   \n", "typescriptreact    34.0    19.0        753.0                   1902   \n", "markdown            6.0     5.0         88.0                    208   \n", "python             87.0    14.0       1104.0                   2382   \n", "html               17.0     0.0        122.0                    159   \n", "css                12.0     1.0         74.0                    201   \n", "rust               78.0     2.0        353.0                    686   \n", "yaml                8.0     4.0         35.0                    111   \n", "typescript        373.0    15.0       1512.0                   2299   \n", "javascript         59.0     0.0        158.0                    241   \n", "\n", "                 accept_rate  hove_shown_rate  \n", "LANGUAGE                                       \n", "starlark            0.000000         0.448598  \n", "dart                0.010695         0.386364  \n", "typescriptreact     0.045153         0.395899  \n", "markdown            0.068182         0.423077  \n", "python              0.078804         0.463476  \n", "html                0.139344         0.767296  \n", "css                 0.162162         0.368159  \n", "rust                0.220963         0.514577  \n", "yaml                0.228571         0.315315  \n", "typescript          0.246693         0.657677  \n", "javascript          0.373418         0.655602  "]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["def row_to_dict(row):\n", "    output = {k: v for k, v in zip(row.event_name, row.count)}\n", "    output[\"LANGUAGE\"] = row.LANGUAGE\n", "    return output\n", "\n", "\n", "df_lang_vanguard = pd.DataFrame([row_to_dict(d) for d in data])\n", "df_lang_vanguard.set_index(\"LANGUAGE\", inplace=True)\n", "df_lang_vanguard.fillna(0, inplace=True)\n", "df_lang_vanguard[\"accept_rate\"] = df_lang_vanguard[\"accept\"] / df_lang_vanguard[\"hover-shown\"]\n", "df_lang_vanguard[\"hove_shown_rate\"] = df_lang_vanguard[\"hover-shown\"] / df_lang_vanguard[\"suggestion-hint-shown\"]\n", "df_lang_vanguard[\n", "    [\n", "        \"accept\",\n", "        \"reject\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "        \"accept_rate\",\n", "        \"hove_shown_rate\",\n", "    ]\n", "][df_lang_vanguard[\"suggestion-hint-shown\"] > 100].sort_values(\"accept_rate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Accept/Hover Rate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dogfood"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "QUERY = \"\"\"\n", "SELECT\n", "  time,\n", "  event_name,\n", "  user_agent, \n", "  COUNT(*) AS count\n", "FROM (\n", "  SELECT\n", "    DISTINCT *\n", "  FROM (\n", "    WITH\n", "      REQUEST AS (\n", "      SELECT\n", "        request_id,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.request.lang\") AS lang\n", "      FROM\n", "        `us_staging_request_insight_analytics_dataset.next_edit_host_request`\n", "      WHERE\n", "        tenant=\"dogfood-shard\"),\n", "      SESSION AS (\n", "      SELECT\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "        DATE(time) AS time\n", "      FROM\n", "        `us_staging_request_insight_analytics_dataset.next_edit_session_event`\n", "      WHERE\n", "        tenant=\"dogfood-shard\"\n", "        -- AND JSON_EXTRACT_SCALAR(raw_json, \"$.related_suggestion_id\") IS NOT NULL\n", "        AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "      METADATA AS (\n", "      SELECT\n", "        request_id,\n", "        user_id,\n", "        user_agent,\n", "        CAST(REGEXP_EXTRACT(user_agent, \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) as version\n", "      FROM\n", "        `us_staging_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant = 'dogfood-shard'\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\", \"<EMAIL>\")\n", "        -- AND CAST(REGEXP_EXTRACT(user_agent, \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) >= 229\n", "        )\n", "    SELECT\n", "      SESSION.event_name,\n", "      SESSION.sid,\n", "      SESSION.time,\n", "      METADATA.user_agent,\n", "      REQUEST.lang\n", "    FROM\n", "      SESSION\n", "    JOIN\n", "      METADATA\n", "    USING\n", "      (request_id)\n", "    JOIN\n", "      REQUEST\n", "    USING\n", "      (request_id) ))\n", "GROUP BY\n", "  event_name, time, user_agent\n", "\"\"\"\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(\n", "    QUERY,\n", "    page_size=128,\n", ")\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>user_agent</th>\n", "      <th>count</th>\n", "      <th>extension</th>\n", "      <th>os</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th>event_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"20\" valign=\"top\">2024-11-05</th>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 5....</td>\n", "      <td>83</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 6....</td>\n", "      <td>6</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 6....</td>\n", "      <td>12</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 6....</td>\n", "      <td>3</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 6....</td>\n", "      <td>10</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 6....</td>\n", "      <td>36</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (linux; x64; 6....</td>\n", "      <td>6</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (dar<PERSON>; arm64;...</td>\n", "      <td>2</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 6....</td>\n", "      <td>3</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.0 (darwin; arm64;...</td>\n", "      <td>3</td>\n", "      <td>Augment.vscode-augment/0.282.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (dar<PERSON>; arm64;...</td>\n", "      <td>4</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 6....</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 5....</td>\n", "      <td>33</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 6....</td>\n", "      <td>6</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 5....</td>\n", "      <td>11</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.0.3141592 (darwin; ar...</td>\n", "      <td>7</td>\n", "      <td>Augment.vscode-augment/0.0.3141592</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.0 (darwin; arm64;...</td>\n", "      <td>11</td>\n", "      <td>Augment.vscode-augment/0.282.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.283.0 (linux; x64; 5....</td>\n", "      <td>22</td>\n", "      <td>Augment.vscode-augment/0.283.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 5....</td>\n", "      <td>17</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (linux; x64; 6....</td>\n", "      <td>12</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>linux</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                              user_agent  \\\n", "time       event_name                                                      \n", "2024-11-05 accept      Augment.vscode-augment/0.284.0 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.284.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.284.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.282.1 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.283.0 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.284.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.282.0 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.283.0 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.0.3141592 (dar<PERSON>; ar...   \n", "           accept      Augment.vscode-augment/0.282.0 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.283.0 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.284.0 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.277.1 (linux; x64; 6....   \n", "\n", "                       count                           extension      os  \n", "time       event_name                                                     \n", "2024-11-05 accept         83      Augment.vscode-augment/0.284.0   linux  \n", "           accept          6      Augment.vscode-augment/0.283.0   linux  \n", "           accept         12      Augment.vscode-augment/0.283.0   linux  \n", "           accept          3      Augment.vscode-augment/0.284.0   linux  \n", "           accept         10      Augment.vscode-augment/0.283.0   linux  \n", "           accept         36      Augment.vscode-augment/0.284.0   linux  \n", "           accept          6      Augment.vscode-augment/0.282.1   linux  \n", "           accept          2      Augment.vscode-augment/0.283.0  darwin  \n", "           accept          3      Augment.vscode-augment/0.284.0   linux  \n", "           accept          3      Augment.vscode-augment/0.282.0  darwin  \n", "           accept          4      Augment.vscode-augment/0.283.0  darwin  \n", "           accept          1      Augment.vscode-augment/0.283.0   linux  \n", "           accept         33      Augment.vscode-augment/0.283.0   linux  \n", "           accept          6      Augment.vscode-augment/0.283.0   linux  \n", "           accept         11      Augment.vscode-augment/0.283.0   linux  \n", "           accept          7  Augment.vscode-augment/0.0.3141592  darwin  \n", "           accept         11      Augment.vscode-augment/0.282.0  darwin  \n", "           accept         22      Augment.vscode-augment/0.283.0   linux  \n", "           accept         17      Augment.vscode-augment/0.284.0   linux  \n", "           accept         12      Augment.vscode-augment/0.277.1   linux  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "def _get_os(user_agent: str):\n", "    if 'darwin' in user_agent:\n", "        return 'darwin'\n", "    elif 'linux' in user_agent:\n", "        return 'linux'\n", "    elif 'win' in user_agent:\n", "        return 'win'\n", "    else:\n", "        return 'other'\n", "\n", "df_dogfood = pd.DataFrame([dict(d.items()) for d in data])\n", "df_dogfood['extension'] = df_dogfood.apply(lambda row: row['user_agent'].split(' ')[0], axis=1)\n", "df_dogfood['os'] = df_dogfood.apply(lambda row: _get_os(row['user_agent']), axis=1)\n", "df_dogfood.set_index([\"time\", \"event_name\"], inplace=True)\n", "df_dogfood.sort_index(inplace=True)\n", "df_dogfood.head(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accept Rate/ Hover Rate"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "      <th>accept</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-11-05</th>\n", "      <td>0.334982</td>\n", "      <td>0.656917</td>\n", "      <td>474.0</td>\n", "      <td>1415.0</td>\n", "      <td>2154.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-06</th>\n", "      <td>0.309859</td>\n", "      <td>0.697565</td>\n", "      <td>506.0</td>\n", "      <td>1633.0</td>\n", "      <td>2341.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-07</th>\n", "      <td>0.362536</td>\n", "      <td>0.693333</td>\n", "      <td>509.0</td>\n", "      <td>1404.0</td>\n", "      <td>2025.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-08</th>\n", "      <td>0.324675</td>\n", "      <td>0.653821</td>\n", "      <td>325.0</td>\n", "      <td>1001.0</td>\n", "      <td>1531.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-09</th>\n", "      <td>0.320000</td>\n", "      <td>0.538462</td>\n", "      <td>168.0</td>\n", "      <td>525.0</td>\n", "      <td>975.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-10</th>\n", "      <td>0.233333</td>\n", "      <td>0.430622</td>\n", "      <td>84.0</td>\n", "      <td>360.0</td>\n", "      <td>836.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-11</th>\n", "      <td>0.355856</td>\n", "      <td>0.440476</td>\n", "      <td>79.0</td>\n", "      <td>222.0</td>\n", "      <td>504.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name  accept_rate  hover_rate  accept  hover-shown  \\\n", "time                                                       \n", "2024-11-05     0.334982    0.656917   474.0       1415.0   \n", "2024-11-06     0.309859    0.697565   506.0       1633.0   \n", "2024-11-07     0.362536    0.693333   509.0       1404.0   \n", "2024-11-08     0.324675    0.653821   325.0       1001.0   \n", "2024-11-09     0.320000    0.538462   168.0        525.0   \n", "2024-11-10     0.233333    0.430622    84.0        360.0   \n", "2024-11-11     0.355856    0.440476    79.0        222.0   \n", "\n", "event_name  suggestion-hint-shown  \n", "time                               \n", "2024-11-05                 2154.0  \n", "2024-11-06                 2341.0  \n", "2024-11-07                 2025.0  \n", "2024-11-08                 1531.0  \n", "2024-11-09                  975.0  \n", "2024-11-10                  836.0  \n", "2024-11-11                  504.0  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Pivot the DataFrame to convert event_name to column names\n", "rate_dogfood = (\n", "    df_dogfood.groupby([\"time\", \"event_name\"])\n", "    .agg({\"count\": \"sum\"})\n", "    .reset_index()\n", "    .pivot(index=\"time\", columns=\"event_name\", values=\"count\")\n", ")\n", "# Fill NaN values with 0 (for events that didn't occur for some versions)\n", "rate_dogfood = rate_dogfood.fillna(0)\n", "rate_dogfood[\"accept_rate\"] = rate_dogfood[\"accept\"] / rate_dogfood[\"hover-shown\"]\n", "rate_dogfood[\"hover_rate\"] = (\n", "    rate_dogfood[\"hover-shown\"] / rate_dogfood[\"suggestion-hint-shown\"]\n", ")\n", "rate_dogfood[[\"accept_rate\", \"hover_rate\", \"accept\", \"hover-shown\", \"suggestion-hint-shown\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Slice by Version"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "      <th>accept</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "    </tr>\n", "    <tr>\n", "      <th>os</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>win</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>linux</th>\n", "      <td>0.321816</td>\n", "      <td>0.629902</td>\n", "      <td>1928.0</td>\n", "      <td>5991.0</td>\n", "      <td>9511.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>darwin</th>\n", "      <td>0.381371</td>\n", "      <td>0.665497</td>\n", "      <td>217.0</td>\n", "      <td>569.0</td>\n", "      <td>855.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name  accept_rate  hover_rate  accept  hover-shown  \\\n", "os                                                         \n", "win                 NaN         NaN     0.0          0.0   \n", "linux          0.321816    0.629902  1928.0       5991.0   \n", "da<PERSON>win         0.381371    0.665497   217.0        569.0   \n", "\n", "event_name  suggestion-hint-shown  \n", "os                                 \n", "win                           0.0  \n", "linux                      9511.0  \n", "<PERSON><PERSON><PERSON>                      855.0  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Pivot the DataFrame to convert event_name to column names\n", "rate_dogfood_version = (\n", "    df_dogfood.groupby([\"os\", \"event_name\"])\n", "    .agg({\"count\": \"sum\"})\n", "    .reset_index()\n", "    .pivot(index=\"os\", columns=\"event_name\", values=\"count\")\n", ")\n", "# Fill NaN values with 0 (for events that didn't occur for some versions)\n", "rate_dogfood_version = rate_dogfood_version.fillna(0)\n", "rate_dogfood_version[\"accept_rate\"] = (\n", "    rate_dogfood_version[\"accept\"] / rate_dogfood_version[\"hover-shown\"]\n", ")\n", "rate_dogfood_version[\"hover_rate\"] = (\n", "    rate_dogfood_version[\"hover-shown\"] / rate_dogfood_version[\"suggestion-hint-shown\"]\n", ")\n", "rate_dogfood_version[\n", "    [\n", "        \"accept_rate\",\n", "        \"hover_rate\",\n", "        \"accept\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "    ]\n", "].sort_values('os', ascending=False)"]}, {"cell_type": "code", "execution_count": 339, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "      <th>accept</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "    </tr>\n", "    <tr>\n", "      <th>extension_ends</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>True</th>\n", "      <td>0.180297</td>\n", "      <td>0.660123</td>\n", "      <td>97.0</td>\n", "      <td>538.0</td>\n", "      <td>815.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>False</th>\n", "      <td>0.340759</td>\n", "      <td>0.632540</td>\n", "      <td>2019.0</td>\n", "      <td>5925.0</td>\n", "      <td>9367.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name      accept_rate  hover_rate  accept  hover-shown  \\\n", "extension_ends                                                 \n", "True               0.180297    0.660123    97.0        538.0   \n", "False              0.340759    0.632540  2019.0       5925.0   \n", "\n", "event_name      suggestion-hint-shown  \n", "extension_ends                         \n", "True                            815.0  \n", "False                          9367.0  "]}, "execution_count": 339, "metadata": {}, "output_type": "execute_result"}], "source": ["df_dogfood['extension_ends'] = df_dogfood.apply(lambda row: row['extension'].endswith('1'), axis=1)\n", "# Pivot the DataFrame to convert event_name to column names\n", "foo = (\n", "    df_dogfood.groupby([\"extension_ends\", \"event_name\"])\n", "    .agg({\"count\": \"sum\"})\n", "    .reset_index()\n", "    .pivot(index=\"extension_ends\", columns=\"event_name\", values=\"count\")\n", ")\n", "# Fill NaN values with 0 (for events that didn't occur for some versions)\n", "foo = foo.fillna(0)\n", "foo[\"accept_rate\"] = (\n", "    foo[\"accept\"] / foo[\"hover-shown\"]\n", ")\n", "foo[\"hover_rate\"] = (\n", "    foo[\"hover-shown\"] / foo[\"suggestion-hint-shown\"]\n", ")\n", "foo[\n", "    [\n", "        \"accept_rate\",\n", "        \"hover_rate\",\n", "        \"accept\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "    ]\n", "].sort_values('extension_ends', ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Users"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>version</th>\n", "      <th>f0_</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>288</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>287</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>286</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>285</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>284</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>283</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>282</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>281</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>280</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>279</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>278</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>277</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>276</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>275</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>250</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    version  f0_\n", "0       288   10\n", "1       287    8\n", "2       286   19\n", "3       285   18\n", "4       284   21\n", "5       283   20\n", "6       282   22\n", "7       281    3\n", "8       280   15\n", "9       279   26\n", "10      278   15\n", "11      277   19\n", "12      276    3\n", "13      275    1\n", "14      250    1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'version': [288, 287, 286, 285, 284, 283, 282, 281, 280, 279, 278, 277, 276, 275, 250],\n", "    'f0_': [10, 8, 19, 18, 21, 20, 22, 3, 15, 26, 15, 19, 3, 1, 1]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "display(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DAU\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vanguard + Discover"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["QUERY = \"\"\"\n", "SELECT\n", "  time,\n", "  event_name,\n", "  user_agent,\n", "  COUNT(*) AS count\n", "FROM (\n", "  SELECT\n", "    DISTINCT *\n", "  FROM (\n", "    WITH\n", "      SESSION AS (\n", "      SELECT\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_request_id\") AS request_id,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.related_suggestion_id\") AS sid,\n", "        JSON_EXTRACT_SCALAR(sanitized_json, \"$.event_name\") AS event_name,\n", "        DATE(time) AS time\n", "      FROM\n", "        `us_prod_request_insight_analytics_dataset.next_edit_session_event`\n", "      WHERE\n", "        tenant IN (\"i0-vanguard0\", \"discovery0\")\n", "        -- AND JSON_EXTRACT_SCALAR(raw_json, \"$.related_suggestion_id\") IS NOT NULL\n", "        AND DATE(time) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),\n", "      METADATA AS (\n", "      SELECT\n", "        request_id,\n", "        user_id,\n", "        user_agent,\n", "        CAST(REGEXP_EXTRACT(user_agent, \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) as version\n", "      FROM\n", "        `us_prod_request_insight_analytics_dataset.request_metadata`\n", "      WHERE\n", "        NOT STARTS_WITH(user_agent, 'AugmentHealthCheck') AND NOT STARTS_WITH(user_agent, 'augment_review_bot') AND NOT STARTS_WITH(user_agent, 'Augment-EvalHarness')\n", "        AND tenant IN (\"i0-vanguard0\", \"discovery0\")\n", "        AND user_id NOT IN (\"joel\", \"arunch<PERSON><PERSON>\")\n", "        -- AND CAST(REGEXP_EXTRACT(user_agent, \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) >= 229\n", "        )\n", "    SELECT\n", "      SESSION.event_name,\n", "      SESSION.sid,\n", "      SESSION.time,\n", "      METADATA.user_agent\n", "    FROM\n", "      SESSION\n", "    JOIN\n", "      METADATA\n", "    USING\n", "      (request_id) ))\n", "GROUP BY\n", "  event_name, time, user_agent\n", "\"\"\"\n", "\n", "# tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "# blob_cache = replay_utils.get_blob_cache(tenant)\n", "# checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(\n", "    QUERY,\n", "    page_size=128,\n", ")\n", "data = list(rows)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>user_agent</th>\n", "      <th>count</th>\n", "      <th>extension</th>\n", "      <th>os</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th>event_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"20\" valign=\"top\">2024-11-05</th>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (dar<PERSON>; arm64;...</td>\n", "      <td>64</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (linux; x64; 5....</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.284.0 (linux; x64; 6....</td>\n", "      <td>5</td>\n", "      <td>Augment.vscode-augment/0.284.0</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (linux; x64; 5....</td>\n", "      <td>6</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.0 (darwin; arm64;...</td>\n", "      <td>24</td>\n", "      <td>Augment.vscode-augment/0.282.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.0 (darwin; arm64;...</td>\n", "      <td>7</td>\n", "      <td>Augment.vscode-augment/0.282.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...</td>\n", "      <td>5</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (darwin; x64; 2...</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.0 (darwin; arm64;...</td>\n", "      <td>5</td>\n", "      <td>Augment.vscode-augment/0.282.0</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...</td>\n", "      <td>5</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.272.1 (dar<PERSON>; arm64;...</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.272.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (win32; x64; 10...</td>\n", "      <td>4</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>win</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.272.1 (dar<PERSON>; arm64;...</td>\n", "      <td>8</td>\n", "      <td>Augment.vscode-augment/0.272.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (dar<PERSON>; arm64;...</td>\n", "      <td>4</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (darwin; x64; 2...</td>\n", "      <td>2</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...</td>\n", "      <td>1</td>\n", "      <td>Augment.vscode-augment/0.282.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.277.1 (linux; x64; 5....</td>\n", "      <td>18</td>\n", "      <td>Augment.vscode-augment/0.277.1</td>\n", "      <td>linux</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accept</th>\n", "      <td>Augment.vscode-augment/0.272.1 (darwin; x64; 2...</td>\n", "      <td>7</td>\n", "      <td>Augment.vscode-augment/0.272.1</td>\n", "      <td>darwin</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                              user_agent  \\\n", "time       event_name                                                      \n", "2024-11-05 accept      Augment.vscode-augment/0.277.1 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.284.0 (linux; x64; 6....   \n", "           accept      Augment.vscode-augment/0.282.1 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.282.0 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.282.0 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (darwin; x64; 2...   \n", "           accept      Augment.vscode-augment/0.282.0 (darwin; arm64;...   \n", "           accept      Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.272.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (win32; x64; 10...   \n", "           accept      Augment.vscode-augment/0.272.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (darwin; x64; 2...   \n", "           accept      Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64;...   \n", "           accept      Augment.vscode-augment/0.277.1 (linux; x64; 5....   \n", "           accept      Augment.vscode-augment/0.272.1 (dar<PERSON>; x64; 2...   \n", "\n", "                       count                       extension      os  \n", "time       event_name                                                 \n", "2024-11-05 accept         64  Augment.vscode-augment/0.277.1  darwin  \n", "           accept          1  Augment.vscode-augment/0.277.1   linux  \n", "           accept          5  Augment.vscode-augment/0.284.0   linux  \n", "           accept          6  Augment.vscode-augment/0.282.1   linux  \n", "           accept          1  Augment.vscode-augment/0.282.1  darwin  \n", "           accept          1  Augment.vscode-augment/0.282.1  darwin  \n", "           accept         24  Augment.vscode-augment/0.282.0  darwin  \n", "           accept          7  Augment.vscode-augment/0.282.0  darwin  \n", "           accept          5  Augment.vscode-augment/0.282.1  darwin  \n", "           accept          1  Augment.vscode-augment/0.277.1  darwin  \n", "           accept          5  Augment.vscode-augment/0.282.0  darwin  \n", "           accept          5  Augment.vscode-augment/0.282.1  darwin  \n", "           accept          1  Augment.vscode-augment/0.272.1  darwin  \n", "           accept          4  Augment.vscode-augment/0.277.1     win  \n", "           accept          8  Augment.vscode-augment/0.272.1  darwin  \n", "           accept          4  Augment.vscode-augment/0.277.1  darwin  \n", "           accept          2  Augment.vscode-augment/0.277.1  darwin  \n", "           accept          1  Augment.vscode-augment/0.282.1  darwin  \n", "           accept         18  Augment.vscode-augment/0.277.1   linux  \n", "           accept          7  Augment.vscode-augment/0.272.1  darwin  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "def _get_os(user_agent: str):\n", "    if 'darwin' in user_agent:\n", "        return 'darwin'\n", "    elif 'linux' in user_agent:\n", "        return 'linux'\n", "    elif 'win' in user_agent:\n", "        return 'win'\n", "    else:\n", "        return 'other'\n", "\n", "df_vanguard = pd.DataFrame([dict(d.items()) for d in data])\n", "df_vanguard['extension'] = df_vanguard.apply(lambda row: row['user_agent'].split(' ')[0], axis=1)\n", "df_vanguard['os'] = df_vanguard.apply(lambda row: _get_os(row['user_agent']), axis=1)\n", "df_vanguard.set_index([\"time\", \"event_name\"], inplace=True)\n", "df_vanguard.sort_index(inplace=True)\n", "df_vanguard.head(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accept Rate/ Hover Rate"]}, {"cell_type": "code", "execution_count": 294, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>0.093573</td>\n", "      <td>0.309467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>0.139181</td>\n", "      <td>0.404255</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>0.178040</td>\n", "      <td>0.508037</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-01</th>\n", "      <td>0.155393</td>\n", "      <td>0.461214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-02</th>\n", "      <td>0.166667</td>\n", "      <td>0.572770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-03</th>\n", "      <td>0.087359</td>\n", "      <td>0.686178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-04</th>\n", "      <td>0.118377</td>\n", "      <td>0.636755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-05</th>\n", "      <td>0.139404</td>\n", "      <td>0.552538</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-06</th>\n", "      <td>0.131783</td>\n", "      <td>0.558787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-07</th>\n", "      <td>0.165821</td>\n", "      <td>0.569090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-08</th>\n", "      <td>0.202670</td>\n", "      <td>0.569061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-09</th>\n", "      <td>0.143855</td>\n", "      <td>0.565561</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-10</th>\n", "      <td>0.156146</td>\n", "      <td>0.518966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-11</th>\n", "      <td>0.162162</td>\n", "      <td>0.522046</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name  accept_rate  hover_rate\n", "time                               \n", "2024-10-29     0.093573    0.309467\n", "2024-10-30     0.139181    0.404255\n", "2024-10-31     0.178040    0.508037\n", "2024-11-01     0.155393    0.461214\n", "2024-11-02     0.166667    0.572770\n", "2024-11-03     0.087359    0.686178\n", "2024-11-04     0.118377    0.636755\n", "2024-11-05     0.139404    0.552538\n", "2024-11-06     0.131783    0.558787\n", "2024-11-07     0.165821    0.569090\n", "2024-11-08     0.202670    0.569061\n", "2024-11-09     0.143855    0.565561\n", "2024-11-10     0.156146    0.518966\n", "2024-11-11     0.162162    0.522046"]}, "execution_count": 294, "metadata": {}, "output_type": "execute_result"}], "source": ["# Pivot the DataFrame to convert event_name to column names\n", "rate_vanguard = (\n", "    df_vanguard.groupby([\"time\", \"event_name\"])\n", "    .agg({\"count\": \"sum\"})\n", "    .reset_index()\n", "    .pivot(index=\"time\", columns=\"event_name\", values=\"count\")\n", ")\n", "# Fill NaN values with 0 (for events that didn't occur for some versions)\n", "rate_vanguard = rate_vanguard.fillna(0)\n", "rate_vanguard[\"accept_rate\"] = rate_vanguard[\"accept\"] / rate_vanguard[\"hover-shown\"]\n", "rate_vanguard[\"hover_rate\"] = (\n", "    rate_vanguard[\"hover-shown\"] / rate_vanguard[\"suggestion-hint-shown\"]\n", ")\n", "rate_vanguard[[\"accept_rate\", \"hover_rate\"]]"]}, {"cell_type": "code", "execution_count": 302, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Assuming rate_dogfood and rate_vanguard are your DataFrames\n", "# and they have 'date' and 'hover_rate' columns\n", "\n", "# Create the plot\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot dogfood data\n", "plt.plot(rate_dogfood.index, rate_dogfood['hover_rate'], label='Dogfood', color='blue')\n", "\n", "# Plot vanguard data\n", "plt.plot(rate_vanguard.index, rate_vanguard['hover_rate'], label='Vanguard', color='red')\n", "\n", "# Customize the plot\n", "plt.title('Hover Rate Comparison: Dogfood vs Vanguard', fontsize=16)\n", "plt.xlabel('Date', fontsize=12)\n", "plt.ylabel('Hover Rate', fontsize=12)\n", "plt.legend(fontsize=10)\n", "plt.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# Rotate and align the tick labels so they look better\n", "plt.gcf().autofmt_xdate()\n", "\n", "# Add a little space between the axis and the labels\n", "plt.tight_layout()\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Slice by Version"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate</th>\n", "      <th>hover_rate</th>\n", "      <th>accept</th>\n", "      <th>hover-shown</th>\n", "      <th>suggestion-hint-shown</th>\n", "    </tr>\n", "    <tr>\n", "      <th>os</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>darwin</th>\n", "      <td>0.155647</td>\n", "      <td>0.531701</td>\n", "      <td>791.0</td>\n", "      <td>5082.0</td>\n", "      <td>9558.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>win</th>\n", "      <td>0.183303</td>\n", "      <td>0.690476</td>\n", "      <td>202.0</td>\n", "      <td>1102.0</td>\n", "      <td>1596.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>linux</th>\n", "      <td>0.184801</td>\n", "      <td>0.696751</td>\n", "      <td>107.0</td>\n", "      <td>579.0</td>\n", "      <td>831.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name  accept_rate  hover_rate  accept  hover-shown  \\\n", "os                                                         \n", "darwin         0.155647    0.531701   791.0       5082.0   \n", "win            0.183303    0.690476   202.0       1102.0   \n", "linux          0.184801    0.696751   107.0        579.0   \n", "\n", "event_name  suggestion-hint-shown  \n", "os                                 \n", "darwin                     9558.0  \n", "win                        1596.0  \n", "linux                       831.0  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Pivot the DataFrame to convert event_name to column names\n", "rate_vanguard_version = (\n", "    df_vanguard.groupby([\"os\", \"event_name\"])\n", "    .agg({\"count\": \"sum\"})\n", "    .reset_index()\n", "    .pivot(index=\"os\", columns=\"event_name\", values=\"count\")\n", ")\n", "# Fill NaN values with 0 (for events that didn't occur for some versions)\n", "rate_vanguard_version = rate_vanguard_version.fillna(0)\n", "rate_vanguard_version[\"accept_rate\"] = rate_vanguard_version[\"accept\"] / rate_vanguard_version[\"hover-shown\"]\n", "rate_vanguard_version[\"hover_rate\"] = (\n", "    rate_vanguard_version[\"hover-shown\"] / rate_vanguard_version[\"suggestion-hint-shown\"]\n", ")\n", "rate_vanguard_version[\n", "    [\n", "        \"accept_rate\",\n", "        \"hover_rate\",\n", "        \"accept\",\n", "        \"hover-shown\",\n", "        \"suggestion-hint-shown\",\n", "    ]\n", "].sort_values('suggestion-hint-shown', ascending=False)"]}, {"cell_type": "code", "execution_count": 343, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>event_name</th>\n", "      <th>accept_rate_vanguard</th>\n", "      <th>hover_rate_vanguard</th>\n", "      <th>suggestion-hint-shown_vanguard</th>\n", "      <th>accept_rate_dogfood</th>\n", "      <th>hover_rate_dogfood</th>\n", "      <th>suggestion-hint-shown_dogfood</th>\n", "    </tr>\n", "    <tr>\n", "      <th>extension</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.282.1</th>\n", "      <td>0.161959</td>\n", "      <td>0.602724</td>\n", "      <td>4405.0</td>\n", "      <td>0.167028</td>\n", "      <td>0.641168</td>\n", "      <td>719.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.282.0</th>\n", "      <td>0.122334</td>\n", "      <td>0.667916</td>\n", "      <td>1334.0</td>\n", "      <td>0.200000</td>\n", "      <td>0.627530</td>\n", "      <td>247.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.277.1</th>\n", "      <td>0.243961</td>\n", "      <td>0.668281</td>\n", "      <td>1239.0</td>\n", "      <td>0.259740</td>\n", "      <td>0.802083</td>\n", "      <td>96.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.272.1</th>\n", "      <td>0.211401</td>\n", "      <td>0.352596</td>\n", "      <td>1194.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.285.0</th>\n", "      <td>0.108545</td>\n", "      <td>0.509412</td>\n", "      <td>850.0</td>\n", "      <td>0.395137</td>\n", "      <td>0.657562</td>\n", "      <td>1501.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.286.0</th>\n", "      <td>0.028736</td>\n", "      <td>0.362500</td>\n", "      <td>480.0</td>\n", "      <td>0.366210</td>\n", "      <td>0.694356</td>\n", "      <td>1577.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.279.0</th>\n", "      <td>0.147727</td>\n", "      <td>0.747346</td>\n", "      <td>471.0</td>\n", "      <td>0.461538</td>\n", "      <td>0.866667</td>\n", "      <td>15.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.278.0</th>\n", "      <td>0.029851</td>\n", "      <td>0.515385</td>\n", "      <td>390.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.288.0</th>\n", "      <td>0.365854</td>\n", "      <td>0.405941</td>\n", "      <td>303.0</td>\n", "      <td>0.284006</td>\n", "      <td>0.410681</td>\n", "      <td>1629.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.252.1</th>\n", "      <td>0.103448</td>\n", "      <td>0.114173</td>\n", "      <td>254.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.284.0</th>\n", "      <td>0.400000</td>\n", "      <td>0.597826</td>\n", "      <td>92.0</td>\n", "      <td>0.315726</td>\n", "      <td>0.721735</td>\n", "      <td>2282.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.264.1</th>\n", "      <td>0.000000</td>\n", "      <td>0.250000</td>\n", "      <td>88.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.287.0</th>\n", "      <td>0.244444</td>\n", "      <td>0.600000</td>\n", "      <td>75.0</td>\n", "      <td>0.448276</td>\n", "      <td>0.705882</td>\n", "      <td>493.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.266.0</th>\n", "      <td>0.000000</td>\n", "      <td>0.686275</td>\n", "      <td>51.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.283.0</th>\n", "      <td>0.000000</td>\n", "      <td>0.787879</td>\n", "      <td>33.0</td>\n", "      <td>0.342995</td>\n", "      <td>0.656101</td>\n", "      <td>1262.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.280.0</th>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>2.0</td>\n", "      <td>0.263636</td>\n", "      <td>0.709677</td>\n", "      <td>155.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6</th>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f</th>\n", "      <td>0.000000</td>\n", "      <td>inf</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["event_name                                                                                   accept_rate_vanguard  \\\n", "extension                                                                                                           \n", "Augment.vscode-augment/0.282.1                                                                           0.161959   \n", "Augment.vscode-augment/0.282.0                                                                           0.122334   \n", "Augment.vscode-augment/0.277.1                                                                           0.243961   \n", "Augment.vscode-augment/0.272.1                                                                           0.211401   \n", "Augment.vscode-augment/0.285.0                                                                           0.108545   \n", "Augment.vscode-augment/0.286.0                                                                           0.028736   \n", "Augment.vscode-augment/0.279.0                                                                           0.147727   \n", "Augment.vscode-augment/0.278.0                                                                           0.029851   \n", "Augment.vscode-augment/0.288.0                                                                           0.365854   \n", "Augment.vscode-augment/0.252.1                                                                           0.103448   \n", "Augment.vscode-augment/0.284.0                                                                           0.400000   \n", "Augment.vscode-augment/0.264.1                                                                           0.000000   \n", "Augment.vscode-augment/0.287.0                                                                           0.244444   \n", "Augment.vscode-augment/0.266.0                                                                           0.000000   \n", "Augment.vscode-augment/0.283.0                                                                           0.000000   \n", "Augment.vscode-augment/0.280.0                                                                           0.000000   \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                           0.000000   \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f              0.000000   \n", "\n", "event_name                                                                                   hover_rate_vanguard  \\\n", "extension                                                                                                          \n", "Augment.vscode-augment/0.282.1                                                                          0.602724   \n", "Augment.vscode-augment/0.282.0                                                                          0.667916   \n", "Augment.vscode-augment/0.277.1                                                                          0.668281   \n", "Augment.vscode-augment/0.272.1                                                                          0.352596   \n", "Augment.vscode-augment/0.285.0                                                                          0.509412   \n", "Augment.vscode-augment/0.286.0                                                                          0.362500   \n", "Augment.vscode-augment/0.279.0                                                                          0.747346   \n", "Augment.vscode-augment/0.278.0                                                                          0.515385   \n", "Augment.vscode-augment/0.288.0                                                                          0.405941   \n", "Augment.vscode-augment/0.252.1                                                                          0.114173   \n", "Augment.vscode-augment/0.284.0                                                                          0.597826   \n", "Augment.vscode-augment/0.264.1                                                                          0.250000   \n", "Augment.vscode-augment/0.287.0                                                                          0.600000   \n", "Augment.vscode-augment/0.266.0                                                                          0.686275   \n", "Augment.vscode-augment/0.283.0                                                                          0.787879   \n", "Augment.vscode-augment/0.280.0                                                                          0.500000   \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                          1.000000   \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f                  inf   \n", "\n", "event_name                                                                                   suggestion-hint-shown_vanguard  \\\n", "extension                                                                                                                     \n", "Augment.vscode-augment/0.282.1                                                                                       4405.0   \n", "Augment.vscode-augment/0.282.0                                                                                       1334.0   \n", "Augment.vscode-augment/0.277.1                                                                                       1239.0   \n", "Augment.vscode-augment/0.272.1                                                                                       1194.0   \n", "Augment.vscode-augment/0.285.0                                                                                        850.0   \n", "Augment.vscode-augment/0.286.0                                                                                        480.0   \n", "Augment.vscode-augment/0.279.0                                                                                        471.0   \n", "Augment.vscode-augment/0.278.0                                                                                        390.0   \n", "Augment.vscode-augment/0.288.0                                                                                        303.0   \n", "Augment.vscode-augment/0.252.1                                                                                        254.0   \n", "Augment.vscode-augment/0.284.0                                                                                         92.0   \n", "Augment.vscode-augment/0.264.1                                                                                         88.0   \n", "Augment.vscode-augment/0.287.0                                                                                         75.0   \n", "Augment.vscode-augment/0.266.0                                                                                         51.0   \n", "Augment.vscode-augment/0.283.0                                                                                         33.0   \n", "Augment.vscode-augment/0.280.0                                                                                          2.0   \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                                          1.0   \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f                             0.0   \n", "\n", "event_name                                                                                   accept_rate_dogfood  \\\n", "extension                                                                                                          \n", "Augment.vscode-augment/0.282.1                                                                          0.167028   \n", "Augment.vscode-augment/0.282.0                                                                          0.200000   \n", "Augment.vscode-augment/0.277.1                                                                          0.259740   \n", "Augment.vscode-augment/0.272.1                                                                               NaN   \n", "Augment.vscode-augment/0.285.0                                                                          0.395137   \n", "Augment.vscode-augment/0.286.0                                                                          0.366210   \n", "Augment.vscode-augment/0.279.0                                                                          0.461538   \n", "Augment.vscode-augment/0.278.0                                                                               NaN   \n", "Augment.vscode-augment/0.288.0                                                                          0.284006   \n", "Augment.vscode-augment/0.252.1                                                                               NaN   \n", "Augment.vscode-augment/0.284.0                                                                          0.315726   \n", "Augment.vscode-augment/0.264.1                                                                               NaN   \n", "Augment.vscode-augment/0.287.0                                                                          0.448276   \n", "Augment.vscode-augment/0.266.0                                                                               NaN   \n", "Augment.vscode-augment/0.283.0                                                                          0.342995   \n", "Augment.vscode-augment/0.280.0                                                                          0.263636   \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                               NaN   \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f                  NaN   \n", "\n", "event_name                                                                                   hover_rate_dogfood  \\\n", "extension                                                                                                         \n", "Augment.vscode-augment/0.282.1                                                                         0.641168   \n", "Augment.vscode-augment/0.282.0                                                                         0.627530   \n", "Augment.vscode-augment/0.277.1                                                                         0.802083   \n", "Augment.vscode-augment/0.272.1                                                                              NaN   \n", "Augment.vscode-augment/0.285.0                                                                         0.657562   \n", "Augment.vscode-augment/0.286.0                                                                         0.694356   \n", "Augment.vscode-augment/0.279.0                                                                         0.866667   \n", "Augment.vscode-augment/0.278.0                                                                              NaN   \n", "Augment.vscode-augment/0.288.0                                                                         0.410681   \n", "Augment.vscode-augment/0.252.1                                                                              NaN   \n", "Augment.vscode-augment/0.284.0                                                                         0.721735   \n", "Augment.vscode-augment/0.264.1                                                                              NaN   \n", "Augment.vscode-augment/0.287.0                                                                         0.705882   \n", "Augment.vscode-augment/0.266.0                                                                              NaN   \n", "Augment.vscode-augment/0.283.0                                                                         0.656101   \n", "Augment.vscode-augment/0.280.0                                                                         0.709677   \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                              NaN   \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f                 NaN   \n", "\n", "event_name                                                                                   suggestion-hint-shown_dogfood  \n", "extension                                                                                                                   \n", "Augment.vscode-augment/0.282.1                                                                                       719.0  \n", "Augment.vscode-augment/0.282.0                                                                                       247.0  \n", "Augment.vscode-augment/0.277.1                                                                                        96.0  \n", "Augment.vscode-augment/0.272.1                                                                                         NaN  \n", "Augment.vscode-augment/0.285.0                                                                                      1501.0  \n", "Augment.vscode-augment/0.286.0                                                                                      1577.0  \n", "Augment.vscode-augment/0.279.0                                                                                        15.0  \n", "Augment.vscode-augment/0.278.0                                                                                         NaN  \n", "Augment.vscode-augment/0.288.0                                                                                      1629.0  \n", "Augment.vscode-augment/0.252.1                                                                                         NaN  \n", "Augment.vscode-augment/0.284.0                                                                                      2282.0  \n", "Augment.vscode-augment/0.264.1                                                                                         NaN  \n", "Augment.vscode-augment/0.287.0                                                                                       493.0  \n", "Augment.vscode-augment/0.266.0                                                                                         NaN  \n", "Augment.vscode-augment/0.283.0                                                                                      1262.0  \n", "Augment.vscode-augment/0.280.0                                                                                       155.0  \n", "Augment.vscode-augment/0.282.99-navtej-hover-bold-on-win.1730842539.cadd67c7d6                                         NaN  \n", "Augment.vscode-augment/0.283.99-navtej-AU-5180-fix-hover-footer-table.1730495155.ee1532441f                            NaN  "]}, "execution_count": 343, "metadata": {}, "output_type": "execute_result"}], "source": ["rate_vanguard_version[\n", "    [\n", "        \"accept_rate\",\n", "        \"hover_rate\",\n", "        \"suggestion-hint-shown\",\n", "    ]\n", "].join(\n", "    rate_dogfood_version[\n", "        [\n", "            \"accept_rate\",\n", "            \"hover_rate\",\n", "            \"suggestion-hint-shown\",\n", "        ]\n", "    ],\n", "    rsuffix=\"_dogfood\",\n", "    lsuffix=\"_vanguard\",\n", ").sort_values(\"suggestion-hint-shown_vanguard\", ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Users"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>version</th>\n", "      <th>f0_</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>288</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>287</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>286</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>285</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>284</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>282</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>281</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>280</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>279</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>278</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>277</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>276</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>275</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>273</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>272</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>271</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>266</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    version  f0_\n", "0       288    4\n", "1       287    2\n", "2       286    2\n", "3       285    4\n", "4       284    3\n", "5       282   10\n", "6       281    2\n", "7       280    5\n", "8       279    4\n", "9       278    4\n", "10      277    3\n", "11      276    1\n", "12      275    1\n", "13      273    1\n", "14      272    1\n", "15      271    1\n", "16      266    1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'version': [288, 287, 286, 285, 284, 282, 281, 280, 279, 278, 277, 276, 275, 273, 272, 271, 266],\n", "    'f0_': [4, 2, 2, 4, 3, 10, 2, 5, 4, 4, 3, 1, 1, 1, 1, 1, 1]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "display(df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
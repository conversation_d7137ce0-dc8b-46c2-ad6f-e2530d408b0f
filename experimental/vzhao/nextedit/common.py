"""Common utilities for next edit low quality filter."""

import re
from datetime import datetime
from typing import Callable, Literal, Sequence

from google.cloud.storage import Blob
from google.protobuf.json_format import MessageToDict
from pydantic import BaseModel

from base.datasets.gcs_client import GCSRequestInsightFetcher
from services.request_insight import request_insight_pb2


PATTERN = re.compile(r"(?<!^)(?=[A-Z])")


class Suggestion(BaseModel):
    """Dataclass for a single suggestion."""

    request_id: str
    suggest_id: str
    # Features from generation response.
    prompt: str
    prompt_token_ids: list[int]
    log_probs: list[float]
    token_ids: list[int]
    # Features from suggestion response.
    existing_code: str
    suggested_code: str
    # Features from session events.
    event_name: list[str]
    event_time: list[datetime]
    # Derived features.
    user_action: (
        Literal[
            "accepted",
            "strong_rejected",
            "hover",
            "no_hover_shown",
            "weak_rejected",
            "short_hover",
        ]
        | None
    ) = None
    time_to_action: float | None = None
    line_distance: int | None = None
    hover_duration: float | None = None


def camel_to_snake(string: str) -> str:
    """
    Convert a camelCase string to snake_case.

    Args:
        string (str): The camelCase string to convert.

    Returns:
        str: The converted snake_case string.
    """
    return PATTERN.sub("_", string).lower()


def blob_to_json(blob: Blob) -> dict:
    """Convert a GCS blob to a json dict."""
    request_events = request_insight_pb2.RequestEvent()
    request_events.MergeFromString(blob.download_as_bytes())
    return MessageToDict(request_events)


REQUEST_EVENT_NAME = "nextEditHostRequest"


def get_next_edit_request(
    request_id: str, fetchers: list[GCSRequestInsightFetcher]
) -> dict | None:
    """Get the next edit host request event for a given request ID."""
    for fetcher in fetchers:
        events = fetcher.get_request(
            request_id=request_id,
            request_event_names=frozenset({camel_to_snake(REQUEST_EVENT_NAME)}),
        )
        if events.events:
            # There should be only 1 event for `REQUEST_EVENT_NAME`.
            return MessageToDict(events.events[0].next_edit_host_request)


RESPONSE_EVENT_NAME = "nextEditHostResponse"


def get_next_edit_responses(
    request_id: str, fetchers: list[GCSRequestInsightFetcher]
) -> list:
    """Get the next edit host response events for a given request ID."""
    for fetcher in fetchers:
        events = fetcher.get_request(
            request_id=request_id,
            request_event_names=frozenset({camel_to_snake(RESPONSE_EVENT_NAME)}),
        )
        if events.events:
            return [
                MessageToDict(event.next_edit_host_response) for event in events.events
            ]
    return []


def filter_and_get_label_negative(suggest: Suggestion) -> int | None:
    """Filter and get label for a single suggestion."""
    if suggest.user_action in ["accepted", "weak_rejected", "short_hover"]:
        return 0
    if suggest.user_action in ["strong_rejected"]:
        return 1


def filter_and_get_label_positive(suggest: Suggestion) -> int | None:
    """Filter and get label for a single suggestion."""
    if suggest.user_action == "accepted":
        return 0
    if suggest.user_action in ["weak_rejected", "strong_rejected", "short_hover"]:
        return 1


def filter_and_get_label_v3(suggest: Suggestion) -> int | None:
    """Filter and get label for a single suggestion."""
    if suggest.user_action == "accepted":
        return 0
    if suggest.user_action in ["weak_rejected", "strong_rejected"]:
        return 1


def filter_and_get_label_v4(suggest: Suggestion) -> int | None:
    """Filter and get label for a single suggestion."""
    if suggest.user_action in ["accepted", "short_hover"]:
        return 0
    if suggest.user_action in ["weak_rejected", "strong_rejected"]:
        return 1


def get_filter_and_get_label_v5(min_hover_duration: float):
    def _filter_and_get_label(suggest: Suggestion) -> int | None:
        """Filter and get label for a single suggestion."""
        if suggest.user_action in ["accepted"]:
            return 0
        elif suggest.user_action in ["strong_rejected"]:
            return 1
        elif (
            suggest.hover_duration is not None
            and suggest.hover_duration >= min_hover_duration
        ):
            return 1

    return _filter_and_get_label


def get_filter_and_get_label(
    positive_actions: set[str] = {"accepted"},
    negative_actions: set[str] = {"strong_rejected"},
    pos_max_hover_duration: float | None = None,
    neg_min_hover_duration: float | None = None,
):
    """Returns a filter and label function.

    Args:
        positive_actions: A list of positive actions.
        negative_actions: A list of negative actions.
        min_hover_duration: The minimum hover duration to be considered as negatives.

    Returns:
        A filter and label function.
    """

    def _filter_and_get_label(suggest: Suggestion) -> int | None:
        """Filter or returns label for a single suggestion.

        If returns None, the suggestion is filtered out.
        """
        if suggest.user_action in positive_actions:
            return 0
        if suggest.user_action in negative_actions:
            return 1
        if (
            pos_max_hover_duration is not None
            and suggest.hover_duration is not None
            and suggest.hover_duration <= pos_max_hover_duration
        ):
            return 0
        if (
            neg_min_hover_duration is not None
            and suggest.hover_duration is not None
            and suggest.hover_duration >= neg_min_hover_duration
        ):
            return 1
        return None

    return _filter_and_get_label


def load_suggestion_jsonl(
    json_files: Sequence[str], filter_fn: Callable[[Suggestion], int | None]
) -> tuple[list[Suggestion], list[int]]:
    """Loads suggestion data from jsonl files."""
    data: list[Suggestion] = []
    labels: list[int] = []
    for file in json_files:
        with open(file, "r") as f:
            for line in f:
                suggest = Suggestion.model_validate_json(line)
                if (label := filter_fn(suggest)) is not None:
                    data.append(suggest)
                    labels.append(label)
    assert len(data) == len(labels)
    return data, labels


def find_suggestion_response(suggestion_id: str, responses: list[dict]) -> None | dict:
    """Find the suggestion response for a given suggestion."""
    for response in responses:
        if "suggestions" in response:
            if (
                response["suggestions"][0]["result"]["suggestedEdit"]["suggestionId"]
                == suggestion_id
            ):
                return response


def find_generation_response(
    responses: list,
    suggestion_id: str | None = None,
    generation_id: str | None = None,
) -> None | dict:
    """Find the generation response for a given suggestion."""
    if not generation_id and suggestion_id:
        suggestion = find_suggestion_response(suggestion_id, responses)
        if (
            not suggestion
            or "suggestions" not in suggestion
            or len(suggestion["suggestions"]) == 0
        ):
            return None
        generation_id = suggestion["suggestions"][0]["generationId"]
    for response in responses:
        if "generation" in response:
            if response["generation"][0]["generationId"] == generation_id:
                return response

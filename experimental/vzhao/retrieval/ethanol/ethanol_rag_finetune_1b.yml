# Usage:
# python research/gpt-neox/jobs/experiment.py --metaconfig /home/<USER>/augment/experimental/vzhao/retrieval/ethanol/ethanol_rag_finetune_1b.yml

includes:
  - augment_configs/starcoder/model/starcoder.yml
  - augment_configs/starcoder/model/starcoder-1b.yml

determined:
  name: &detname ethanol_rag_1b
  description: null
  workspace: Dev
  project: vzhao
  labels: ["starcoder", "rag"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 16
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_project: "vzhao-exp"
  wandb_name: *detname
  wandb_group: *detname
  # 16k tokens per GPU
  train_micro_batch_size_per_gpu: 16
  gradient_accumulation_steps: 4
  train_batch_size: 1024

  train_iters: 1000
  lr_decay_iters: 1000 # If not set, defaults to train_iters
  warmup_iters: 100
  lr_decay_style: cosine

  # FIM context loss mask
  loss_mask_mode: fim-context
  extra_loss_masks:
    - fim-context
    - pad

  attention_precision: bfloat16

  seq-length: 4096

  min_lr: 1.0e-6
  optimizer:
    params:
      betas:
        - 0.9
        - 0.95
      eps: 1.0e-08
      lr: 1.0e-5
    type: Adam

  # Eval/save frequency
  eval_interval: 5
  save_interval: 250
  log-interval: 10

  # Data
  train_data_paths:
    ["/mnt/efs/augment/user/vincent/data/ethanol_rag/0930/dataset"]
  valid_data_paths:
    ["/mnt/efs/augment/user/vincent/data/ethanol_rag/0930/validation_dataset"]
  test_data_paths:
    ["/mnt/efs/augment/user/vincent/data/ethanol_rag/0930/validation_dataset"]
  max_valid_data_size: 8192
  data_impl: mmap
  dataset_type: direct

"""Download the checkpoint if it doesn't exist.

Usage:

python experimental/vzhao/scripts/download_det_ckpt.py \
    --det_id=57641 \
    --dir="butanol"

"""

import pathlib
import subprocess

from absl import app, flags
from determined.experimental import client

_DIR = flags.DEFINE_string("dir", None, "Checkpoint directory.", required=True)

_DET_ID = flags.DEFINE_integer(
    "det_id", None, "Determined experiment id.", required=True
)

_UUID = flags.DEFINE_string("uuid", None, "Checkpoint uuid.", required=False)


def maybe_download_ckpt() -> str:
    """Download the checkpoint if it doesn't exist."""
    experiment = client.get_experiment(_DET_ID.value)
    trial = experiment.get_trials()[0]
    if _UUID.value:
        ckpt = trial.select_checkpoint(uuid=_UUID.value)
    else:
        ckpt = trial.select_checkpoint(latest=True)
    uuid = ckpt.uuid
    model_name = experiment.config["name"].replace("-", "_").lower()
    ckpt_root = pathlib.Path("/mnt/efs/augment/checkpoints/")
    (ckpt_root / _DIR.value).mkdir(parents=True, exist_ok=True)
    ckpt_path = f"{_DIR.value}/{model_name}_{ckpt.metadata['steps_completed']}"
    print(f"checkpoint path: {ckpt_root/ckpt_path}")
    if (ckpt_root / ckpt_path).exists():
        return ckpt_path
    subprocess.run(
        f"bash research/utils/download_checkpoint.sh {uuid} {ckpt_path}",
        shell=True,
        check=False,
    )
    return ckpt_path


def main(argvs):
    del argvs
    print(maybe_download_ckpt())


if __name__ == "__main__":
    app.run(main)

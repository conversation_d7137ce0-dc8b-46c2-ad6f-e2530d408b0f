"""<PERSON><PERSON><PERSON> to stress test the indexer.

This script uploads a large number of blobs using the Augment client.

Usage:
python experimental/vzhao/scripts/run_stress_test_indexer.py https://dogfood.api.augmentcode.com/ 10
"""

import os
import pathlib

import click
import more_itertools
import tqdm
from google.cloud import storage  # type: ignore

from base.augment_client.client import AugmentClient, UploadContent
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache

PROJECT_ID = "system-services-prod"
BLOB_BUCKET_PREFIX = "blobs"
BLOB_BUCKET_NAME = "augment-blob-exporter-dogfood-staging"


def get_blob_names(blob_bucket: storage.Bucket, num_blobs: int) -> list[str]:
    """Returns a list of blob names."""
    blob_names = []
    for blob in tqdm.tqdm(blob_bucket.list_blobs(prefix=BLOB_BUCKET_PREFIX)):
        blob_names.append(blob.name.lstrip(BLOB_BUCKET_PREFIX + "/"))
        if len(blob_names) >= num_blobs:
            break
    return blob_names


def get_augment_token() -> str:
    """Returns the Augment token."""
    token_file = pathlib.Path(f"/home/<USER>/.config/augment/api_token")
    if not token_file.exists():
        raise ValueError(f"Please saves your Augment token to the file: {token_file}.")

    with token_file.open("r", encoding="utf-8") as f:
        return f.read().strip()


def upload_blobs(
    blob_names: list[str], client: AugmentClient, blob_cache: GCSBlobCache
):
    """Uploads the blobs to Augment."""
    for batch in tqdm.tqdm(more_itertools.chunked(blob_names, 16)):
        rst = blob_cache.get(batch)
        client.batch_upload(
            [
                UploadContent(content=c.content, path_name=str(c.path))
                for c in rst
                if c is not None
            ]
        )


@click.command()
@click.argument("augment_url", required=True, type=str)
@click.argument("num_blobs", required=True, type=int)
def main(augment_url: str, num_blobs: int):
    """Main function."""
    gcp_creds, _ = get_gcp_creds(None)
    storage_client = storage.Client(project=PROJECT_ID, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(BLOB_BUCKET_NAME)

    # Gets a list of blob names.
    blob_names = get_blob_names(blob_bucket, num_blobs)
    print("Found %d blob names." % len(blob_names))

    # Uploads the blobs to Augment.
    upload_blobs(
        blob_names,
        AugmentClient(url=augment_url, token=get_augment_token()),
        GCSBlobCache(
            blob_bucket,
            BLOB_BUCKET_PREFIX,
            2**30,
            num_threads=32,
        ),
    )


if __name__ == "__main__":
    main()

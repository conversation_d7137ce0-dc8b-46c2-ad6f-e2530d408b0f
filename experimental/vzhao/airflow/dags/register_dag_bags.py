"""Adds all DAGs to the Airflow registry.

This is useful for Airflow's CLI.

Usage:
airflow dags list

See:
https://airflow.apache.org/docs/apache-airflow/stable/cli-and-env-variables-ref.html#airflow-dags-list
"""
from airflow.models import DagBag

additional_dags_dirs = [
    "/home/<USER>/augment/experimental/vzhao/20231129_star_ethanol/data/dags",
]

for d in additional_dags_dirs:
    dag_bag = DagBag(d)
    if dag_bag:
        for dag_id, dag in dag_bag.dags.items():
            globals()[dag_id] = dag

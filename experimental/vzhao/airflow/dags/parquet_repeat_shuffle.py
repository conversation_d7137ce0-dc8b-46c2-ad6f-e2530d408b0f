"""Airflow to shuffle parquet files.

Check Dag:
airflow dags list

Test DAG:
python experimental/vzhao/airflow/dags/parquet_repeat_shuffle.py

You can manually start the pipeline on Airflow UI. And you can see logs on WebUI as well
as in the scheduler's terminal.
"""
import datetime

from airflow import DAG
from airflow.decorators import task
from airflow.models.param import Param
from airflow.operators.python import get_current_context

from experimental.vzhao.data import common

with DAG(
    "repeat_shuffle_parquet",
    # These args will get passed on to each operator
    # You can override them on a per-task basis during operator initialization
    default_args={
        "depends_on_past": False,
        "email": ["<EMAIL>"],
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 0,
        "retry_delay": datetime.timedelta(minutes=1),
        "max_active_runs": 10,
    },
    description="Repeat and random shuffle parquet files.",
    params={
        "input_path": Param(
            "",
            type="string",
            description='Path to input parquet files, containing "prefix", "suffix", etc.',
        ),
        "output_path": Param(
            "",
            type="string",
            description="Path to output parquet files.",
        ),
        "num_partitions": 2000,
        "repeat": 1,
        "max_workers": 64,
    },
    schedule=None,
    start_date=datetime.datetime(2021, 1, 1),
    catchup=False,
    tags=["data processing"],
) as dag:

    @task(task_id="shuffle")
    def _shuffle_stage():
        context = get_current_context()
        dag_params = context["params"]

        common.reshuffle(
            input_parquet=dag_params["input_path"],
            output_parquet=dag_params["output_path"],
            num_partitions=dag_params["num_partitions"],
            columns=None,
            max_workers=dag_params["max_workers"],
            override=True,
            repeat=dag_params["repeat"],
        )

    shuffle_task = _shuffle_stage()

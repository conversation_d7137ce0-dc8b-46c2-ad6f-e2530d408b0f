# Install Airflow
```shell
pip install apache-airflow
```

# Run a separate Docker for PostgreSQL with port forwarding.

```shell
# Pull image
docker pull postgres

# Start
docker run --name vzhao-postgre -p 5433:5432 -e POSTGRES_PASSWORD=mysecretpassword -d postgres

# Into container
docker exec -it vzhao-postgre psql -U postgres

```

Inside Docker
```
CREATE DATABASE airflow_db;
CREATE USER airflow_user WITH PASSWORD 'airflow_pass';
GRANT ALL PRIVILEGES ON DATABASE airflow_db TO airflow_user;
<!-- -- PostgreSQL 15 requires additional privileges: -->

\c airflow_db postgres
GRANT ALL ON SCHEMA public TO airflow_user;

```

Connect to the database from outside container.

```shell
psql -d airflow_db -h localhost -p 5433 -U airflow_user
```

# Config Airflow to use PostgreSQL

In `airflow.cfg`, set the following

```
dags_folder = /home/<USER>/augment/experimental/vzhao/airflow/dags

load_examples = False

executor = LocalExecutor

sql_alchemy_conn = postgresql+psycopg2://airflow_user:airflow_pass@localhost:5433/airflow_db
```
In `webserver_config.py`,
```
AUTH_ROLE_PUBLIC = "Admin"
```
# Initialize Airflow and run Airflow server

```shell

airflow db init
airflow webserver
airflow scheduler
```

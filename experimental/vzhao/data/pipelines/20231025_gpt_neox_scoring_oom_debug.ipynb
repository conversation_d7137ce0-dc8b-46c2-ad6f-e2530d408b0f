{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# My custom library.\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Imports\n", "import os\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session, get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "from IPython.display import HTML, clear_output, display\n", "from experimental.vzhao.data import common"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_6_label_1023\n", "s3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_7_rescore_1023\n"]}, {"data": {"text/plain": ["{'language_model': {'checkpoint_path': 'rogue/diffb1m_16b_alphal_fixtoken',\n", "  'name': 'rogue',\n", "  'prompt': {'max_prefix_tokens': 1280,\n", "   'max_suffix_tokens': 768,\n", "   'max_retrieved_chunk_tokens': -1,\n", "   'max_prompt_tokens': 3072}}}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["CONFIG = {}\n", "S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/\"\n", "CONFIG[\"stage_7\"] = dict(\n", "    language_model={\n", "        \"checkpoint_path\": \"rogue/diffb1m_16b_alphal_fixtoken\",\n", "        \"name\": \"rogue\",\n", "        \"prompt\": {\n", "            \"max_prefix_tokens\": 1280,\n", "            \"max_suffix_tokens\": 768,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_prompt_tokens\": 3072,\n", "        },\n", "    },\n", ")\n", "\n", "STAGE6_URI = os.path.join(S3ROOT, f\"stage_6_label_1023\")\n", "STAGE7_URI = os.path.join(S3ROOT, f\"stage_7_rescore_debug\")\n", "print(STAGE6_URI)\n", "print(STAGE7_URI)\n", "display(CONFIG[\"stage_7\"])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import torch\n", "import megatron\n", "from research.eval.harness.factories import create_model\n", "\n", "class NeoxForwardStep:\n", "\n", "    def __init__(self, model_config: dict):\n", "        self._model_config = model_config\n", "        self._model = None\n", "\n", "    def __call__(\n", "        self,\n", "        *,\n", "        retrieved_chunks,\n", "        **unused_cols\n", "    ) -> pd.Series:\n", "        del unused_cols\n", "        if self._model is None:\n", "            # os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:512\"\n", "            self._model = create_model(self._model_config)\n", "            self._model.load()\n", "\n", "        # THe following code is similar to the implementation of\n", "        # `GPTNeoXModel.log_likelihood_continuation`.\n", "        batch_size, seq_len = 2, 4096\n", "        dummy = torch.zeros(batch_size, seq_len, dtype=torch.int64)\n", "        for _ in common.deserialize_retrieved_chunks(retrieved_chunks):\n", "            data_iterator = iter([{\"text\": dummy}])\n", "            self._model.neox_model.module.clear_cache()  # clear the k,v cache before\n", "            with torch.no_grad():\n", "                loss, logits = megatron.training.forward_step(\n", "                    data_iterator=data_iterator,\n", "                    model=self._model.neox_model,\n", "                    neox_args=self._model._neox_args,\n", "                    timers=None,\n", "                    return_logits=True,\n", "                    output_dict=False,\n", "                )\n", "                assert isinstance(loss, torch.Tensor)\n", "                assert isinstance(logits, torch.Tensor)\n", "                loss = loss.cpu().numpy()\n", "                logits = logits[0].cpu()\n", "            self._model.neox_model.module.clear_cache()  # clear the k,v cache after\n", "\n", "        print(f\"Peak GPU Memory: {torch.cuda.max_memory_allocated()}.\")\n", "        return pd.Series({})"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/26 05:01:12 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "23/10/26 05:01:12 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "WARNING:root:Doing a timing run.  Processing one batch per file and a maximum of 5 files.\n", "23/10/26 05:01:59 WARN TaskSetManager: Lost task 3.0 in stage 3.0 (TID 18) (************** executor 1): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 523, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_6_label_1023/part-00003-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Creating extension directory /home/<USER>/.cache/torch_extensions/py39_cu118/utils...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "Loading extension module utils...\n", "[process_file] Error processing s3a://augment-temporary/vzhao/ppl_gain/1020_1b_sample_16b_rescore/stage_6_label_1023/part-00003-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 228, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 912, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 902, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 855, in wrapped\n", "    results = func(**feature)\n", "  File \"/tmp/ipykernel_846905/2259716892.py\", line 31, in __call__\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/megatron/training.py\", line 265, in forward_step\n", "    eval_res = model.eval_batch(data_iterator, return_logits=return_logits)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/deepspeed/runtime/pipe/engine.py\", line 394, in eval_batch\n", "    self._exec_schedule(sched)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/deepspeed/runtime/pipe/engine.py\", line 1308, in _exec_schedule\n", "    self._exec_instr(**cmd.kwargs)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/deepspeed/runtime/pipe/engine.py\", line 700, in _exec_forward_pass\n", "    self.loss = self.loss_model(outputs, labels)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/megatron/model/gpt2_model.py\", line 623, in _cross_entropy_multi_reduce_deepspeed\n", "    loss_dict = cross_entropy_multi_reduce(output, (_labels, loss_masks), _fp16)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/megatron/model/gpt2_model.py\", line 91, in cross_entropy_multi_reduce\n", "    loss_tensor = cross_entropy(output, _labels, _fp16)\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/megatron/model/gpt2_model.py\", line 65, in cross_entropy\n", "    losses = mpu.vocab_parallel_cross_entropy(output.float().contiguous(), labels)\n", "torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 1.56 GiB (GPU 0; 79.15 GiB total capacity; 30.49 GiB already allocated; 46.84 GiB free; 30.69 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 336, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-10-26/vzhao-dev/ae68ce60-893b-4415-94b8-1a2ba0a712d8/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 231, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "ERROR:root:KeyboardInterrupt while sending command.\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py\", line 1038, in send_command\n", "    response = connection.send_command(command)\n", "  File \"/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py\", line 511, in send_command\n", "    answer = smart_decode(self.stream.readline()[:-1])\n", "  File \"/opt/conda/lib/python3.9/socket.py\", line 704, in readinto\n", "    return self._sock.recv_into(b)\n", "KeyboardInterrupt\n", "[Stage 3:>                                                          (0 + 1) / 5]\r"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb Cell 4\u001b[0m line \u001b[0;36m1\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m spark_gpu \u001b[39m=\u001b[39m k8s_session(\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     name\u001b[39m=\u001b[39m\u001b[39m'\u001b[39m\u001b[39mvzhao-rescore-stage7\u001b[39m\u001b[39m'\u001b[39m,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m     max_workers\u001b[39m=\u001b[39m\u001b[39m1\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m     gpu_count\u001b[39m=\u001b[39m\u001b[39m1\u001b[39m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m )\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m spark_gpu \u001b[39m=\u001b[39m get_session()\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m result \u001b[39m=\u001b[39m map_parquet\u001b[39m.\u001b[39;49mapply_pandas(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m     spark_gpu,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m     map_parquet\u001b[39m.\u001b[39;49mchain_processors(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m         [   \n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m             NeoxForwardStep(CONFIG[\u001b[39m\"\u001b[39;49m\u001b[39mstage_7\u001b[39;49m\u001b[39m\"\u001b[39;49m][\u001b[39m'\u001b[39;49m\u001b[39mlanguage_model\u001b[39;49m\u001b[39m'\u001b[39;49m]),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m         ]\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m     ),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m     input_path\u001b[39m=\u001b[39;49mSTAGE6_URI,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     output_path\u001b[39m=\u001b[39;49mSTAGE7_URI,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m     timing_run\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m     drop_original_columns\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m     timeout\u001b[39m=\u001b[39;49m\u001b[39m7200\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m     batch_size\u001b[39m=\u001b[39;49m\u001b[39m4\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=31'>32</a>\u001b[0m )\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231025_gpt_neox_scoring_oom_debug.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=33'>34</a>\u001b[0m spark_gpu\u001b[39m.\u001b[39mstop()\n", "File \u001b[0;32m~/augment/research/data/spark/pipelines/utils/map_parquet.py:681\u001b[0m, in \u001b[0;36mapply_pandas\u001b[0;34m(spark_session, pandas_func, input_path, output_path, region, endpoint_url, timeout, poll_time, batch_size, input_columns, timing_run, profile, drop_original_columns, output_column, ignore_error)\u001b[0m\n\u001b[1;32m    679\u001b[0m \u001b[39m# now filter out the failed files\u001b[39;00m\n\u001b[1;32m    680\u001b[0m task_info \u001b[39m=\u001b[39m df\u001b[39m.\u001b[39mselect(\u001b[39m\"\u001b[39m\u001b[39minput_path\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39moutput_path\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39minfo.*\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39mcache()\n\u001b[0;32m--> 681\u001b[0m info_pdf \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39;49mtoPandas()\n\u001b[1;32m    682\u001b[0m status_count \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39mgroupBy(\u001b[39m\"\u001b[39m\u001b[39mstatus\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39magg(F\u001b[39m.\u001b[39mcount(\u001b[39m\"\u001b[39m\u001b[39m*\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39malias(\u001b[39m\"\u001b[39m\u001b[39mcount\u001b[39m\u001b[39m\"\u001b[39m))\n\u001b[1;32m    683\u001b[0m count_dict: \u001b[39mdict\u001b[39m[\u001b[39mstr\u001b[39m, \u001b[39mint\u001b[39m] \u001b[39m=\u001b[39m {\n\u001b[1;32m    684\u001b[0m     status: count\n\u001b[1;32m    685\u001b[0m     \u001b[39mfor\u001b[39;00m status, count \u001b[39min\u001b[39;00m status_count\u001b[39m.\u001b[39mselect(\u001b[39m\"\u001b[39m\u001b[39mstatus\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39mcount\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39mcollect()\n\u001b[1;32m    686\u001b[0m }\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/pandas/conversion.py:208\u001b[0m, in \u001b[0;36mPandasConversionMixin.toPandas\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    205\u001b[0m             \u001b[39mraise\u001b[39;00m\n\u001b[1;32m    207\u001b[0m \u001b[39m# Below is to<PERSON><PERSON><PERSON> without Arrow optimization.\u001b[39;00m\n\u001b[0;32m--> 208\u001b[0m pdf \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mDataFrame\u001b[39m.\u001b[39mfrom_records(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcollect(), columns\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    209\u001b[0m column_counter \u001b[39m=\u001b[39m Counter(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    211\u001b[0m corrected_dtypes: List[Optional[Type]] \u001b[39m=\u001b[39m [\u001b[39mNone\u001b[39;00m] \u001b[39m*\u001b[39m \u001b[39mlen\u001b[39m(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mschema)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/dataframe.py:1216\u001b[0m, in \u001b[0;36mDataFrame.collect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1196\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"Returns all the records as a list of :class:`Row`.\u001b[39;00m\n\u001b[1;32m   1197\u001b[0m \n\u001b[1;32m   1198\u001b[0m \u001b[39m.. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1213\u001b[0m \u001b[39m[Row(age=14, name='<PERSON>'), <PERSON>(age=23, name='<PERSON>'), <PERSON>(age=16, name='<PERSON>')]\u001b[39;00m\n\u001b[1;32m   1214\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m   1215\u001b[0m \u001b[39mwith\u001b[39;00m SCCallSiteSync(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_sc):\n\u001b[0;32m-> 1216\u001b[0m     sock_info \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_jdf\u001b[39m.\u001b[39;49mcollectToPython()\n\u001b[1;32m   1217\u001b[0m \u001b[39mreturn\u001b[39;00m \u001b[39mlist\u001b[39m(_load_from_socket(sock_info, BatchedSerializer(CPickleSerializer())))\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py:1321\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1314\u001b[0m args_command, temp_args \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_build_args(\u001b[39m*\u001b[39margs)\n\u001b[1;32m   1316\u001b[0m command \u001b[39m=\u001b[39m proto\u001b[39m.\u001b[39mCALL_COMMAND_NAME \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcommand_header \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[39m.\u001b[39mEND_COMMAND_PART\n\u001b[0;32m-> 1321\u001b[0m answer \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mgateway_client\u001b[39m.\u001b[39;49msend_command(command)\n\u001b[1;32m   1322\u001b[0m return_value \u001b[39m=\u001b[39m get_return_value(\n\u001b[1;32m   1323\u001b[0m     answer, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mgateway_client, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mtarget_id, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mname)\n\u001b[1;32m   1325\u001b[0m \u001b[39mfor\u001b[39;00m temp_arg \u001b[39min\u001b[39;00m temp_args:\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py:1038\u001b[0m, in \u001b[0;36mGatewayClient.send_command\u001b[0;34m(self, command, retry, binary)\u001b[0m\n\u001b[1;32m   1036\u001b[0m connection \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_get_connection()\n\u001b[1;32m   1037\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m-> 1038\u001b[0m     response \u001b[39m=\u001b[39m connection\u001b[39m.\u001b[39;49msend_command(command)\n\u001b[1;32m   1039\u001b[0m     \u001b[39mif\u001b[39;00m binary:\n\u001b[1;32m   1040\u001b[0m         \u001b[39mreturn\u001b[39;00m response, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_create_connection_guard(connection)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/clientserver.py:511\u001b[0m, in \u001b[0;36mClientServerConnection.send_command\u001b[0;34m(self, command)\u001b[0m\n\u001b[1;32m    509\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m    510\u001b[0m     \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[0;32m--> 511\u001b[0m         answer \u001b[39m=\u001b[39m smart_decode(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mstream\u001b[39m.\u001b[39;49mreadline()[:\u001b[39m-\u001b[39m\u001b[39m1\u001b[39m])\n\u001b[1;32m    512\u001b[0m         logger\u001b[39m.\u001b[39mdebug(\u001b[39m\"\u001b[39m\u001b[39mAnswer received: \u001b[39m\u001b[39m{0}\u001b[39;00m\u001b[39m\"\u001b[39m\u001b[39m.\u001b[39mformat(answer))\n\u001b[1;32m    513\u001b[0m         \u001b[39m# Happens when a the other end is dead. There might be an empty\u001b[39;00m\n\u001b[1;32m    514\u001b[0m         \u001b[39m# answer before the socket raises an error.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/socket.py:704\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    702\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[1;32m    703\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 704\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sock\u001b[39m.\u001b[39;49mrecv_into(b)\n\u001b[1;32m    705\u001b[0m     \u001b[39mexcept\u001b[39;00m timeout:\n\u001b[1;32m    706\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_timeout_occurred \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["spark_gpu = k8s_session(\n", "    name='vzhao-rescore-stage7',\n", "    max_workers=1,\n", "    conf={\n", "        \"spark.executor.pyspark.memory\": \"50G\",\n", "        \"spark.executor.memory\": \"30G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "        # Setting this to 5 means running one task per Executor.\n", "        \"spark.task.cpus\": \"5\",\n", "    },\n", "    # gpu_type=\"RTX_A5000\",\n", "    # A40 works with bs=1 and seq_len = 4096.\n", "    # gpu_type=\"A40\",\n", "    # torch.cuda.OutOfMemoryError\n", "    gpu_type=\"A100_NVLINK_80GB\",\n", "    gpu_count=1,\n", ")\n", "\n", "spark_gpu = get_session()\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark_gpu,\n", "    map_parquet.chain_processors(\n", "        [   \n", "            NeoxForwardStep(CONFIG[\"stage_7\"]['language_model']),\n", "        ]\n", "    ),\n", "    input_path=STAGE6_URI,\n", "    output_path=STAGE7_URI,\n", "    timing_run=True,\n", "    drop_original_columns=True,\n", "    timeout=7200,\n", "    batch_size=4,\n", ")\n", "\n", "spark_gpu.stop()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/26 05:02:23 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["spark_gpu.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
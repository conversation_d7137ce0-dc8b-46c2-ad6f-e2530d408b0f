{"cells": [{"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["# My custom library.\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "# Imports\n", "import json\n", "import os\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session, get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "from research.core.types import EMPTY_CHUNK\n", "from research.retrieval.types import Chunk, Document\n", "from research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "from research.static_analysis.fim_prompt import _format_middle\n", "from research.static_analysis.fim_sampling import CSTFimSampler, FimProblem\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "from experimental.vzhao.data import common\n", "from experimental.vzhao.data import constants"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CONFIG"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["CONFIG = dict(\n", "    allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "    random_seed=74912,\n", "    # === Stage 5 Compute Perplexity Signal ===\n", "    stage_4=dict(\n", "        num_rows=1024,\n", "        num_partitions=16,\n", "    ),\n", "    # === Stage 5 Compute Perplexity Signal ===\n", "    stage_5=dict(\n", "        language_model={\n", "            \"checkpoint_path\": \"rogue/diffb1m_16b_alphal_fixtoken\",\n", "            \"name\": \"rogue\",\n", "            \"prompt\": {\n", "                \"max_prefix_tokens\": 100,\n", "                \"max_suffix_tokens\": 100,\n", "                \"max_retrieved_chunk_tokens\": -1,\n", "                \"max_prompt_tokens\": 750,\n", "            },\n", "        },\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Data paths.\n", "RAW_REPO_DATA = \"s3a://the-stack-processed/by-repo\"\n", "\n", "# S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/test\"\n", "S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b\"\n", "VERSION_SUFFIX = \"_rouge16b\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 4: Reshuffle"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/\n", "s3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_4_reshuffle\n", "{'num_rows': 1024, 'num_partitions': 16}\n"]}], "source": ["STAGE4_URI_OLD = \"s3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/\"\n", "STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')\n", "print(STAGE4_URI_OLD)\n", "print(STAGE4_URI)\n", "print(CONFIG['stage_4'])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/17 20:43:52 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["common.reshuffle(\n", "    STAGE4_URI_OLD,\n", "    STAGE4_URI,\n", "    num_rows=CONFIG['stage_4']['num_rows'],\n", "    num_partitions=CONFIG['stage_4']['num_partitions'],\n", "    columns=[\n", "        \"prefix\",\n", "        \"suffix\",\n", "        \"middle\",\n", "        \"file_path\",\n", "        \"retrieved_chunks\",\n", "    ],\n", "    max_workers=32,\n", "    override=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5a: Compute Perplexity\n", "\n", "Stage 5a works on computing PPL for all retrieved chunks from scratch."]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_4_reshuffle\n", "s3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_rouge16b\n"]}], "source": ["STAGE5_URI = os.path.join(S3ROOT, f'stage_5_ppg{VERSION_SUFFIX}')\n", "print(STAGE4_URI)\n", "print(STAGE5_URI)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def prepend_empty_chunk(retrieved_chunks) -> pd.Series:\n", "    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(\n", "        retrieved_chunks\n", "    )\n", "    return pd.Series(\n", "        {\"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks)}\n", "    )\n", "\n", "\n", "def compute_ppl(\n", "    prefix,\n", "    suffix,\n", "    middle,\n", "    file_path,\n", "    retrieved_chunks,\n", ") -> pd.Series:\n", "    def print_with_time(*args):\n", "        print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "    global cached_reranker\n", "    if \"cached_reranker\" not in globals():\n", "        # Construct a reranker\n", "        print_with_time(\"Constructing the model...\")\n", "        model = create_model(CONFIG['stage_5'][\"language_model\"])\n", "\n", "        print_with_time(\"Constructing the reranker...\")\n", "        cached_reranker = create_reranker(\n", "            model,\n", "            config={\n", "                \"name\": \"oracle_perplexity_reranker\",\n", "                \"top_k\": 256,\n", "                \"batchsize\": 4,\n", "            },\n", "        )\n", "\n", "        # Load the reranking model\n", "        print_with_time(\"Loading the model...\")\n", "        model.load()\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),\n", "        path=file_path,\n", "    )\n", "\n", "    print_with_time(f\"Reranking...\")\n", "    scores = cached_reranker._score(model_input, middle)\n", "    assert len(scores) == len(\n", "        common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ), f\"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}\"\n", "    return pd.Series({\"ppl\": json.dumps(scores)})\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def compute_ppl_gain(retrieved_chunks, ppl):\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ppl = json.loads(ppl)\n", "\n", "    ppl_empty = ppl[0]\n", "    ppg = [s - ppl_empty for s in ppl]\n", "    retrieval_rank = list(range(len(retrieved_chunks)))\n", "    retrieval_rank, ppg, ppl, retrieved_chunks = list(\n", "        zip(\n", "            *sorted(\n", "                zip(retrieval_rank, ppg, ppl, retrieved_chunks),\n", "                key=lambda x: x[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "    )\n", "    return pd.Series(\n", "        {\n", "            \"ppg\": json.dumps(ppg),\n", "            \"ppl\": json.dumps(ppl),\n", "            \"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks),\n", "            \"retrieval_rank\": json.dumps(retrieval_rank),\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 0:>                                                          (0 + 1) / 5]\r"]}], "source": ["def stage5a():\n", "    spark_gpu = k8s_session(\n", "        max_workers=16,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"A40\",\n", "    )\n", "    files = map_parquet.list_files(\n", "        spark_gpu, STAGE4_URI, suffix=\"parquet\", include_path=False\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                prepend_empty_chunk,\n", "                compute_ppl,\n", "                compute_ppl_gain,\n", "            ]\n", "        ),\n", "        input_path=[os.path.join(STAGE4_URI, f) for f in files[:10]],\n", "        output_path=STAGE5_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5a()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
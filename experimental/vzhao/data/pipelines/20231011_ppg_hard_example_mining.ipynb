{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# My custom library.\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "# Imports\n", "import json\n", "import os\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from augment.research.data.spark import k8s_session, get_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from augment.research.eval.harness.factories import create_retriever\n", "from augment.research.core.types import EMPTY_CHUNK\n", "from augment.research.retrieval.types import Chunk, Document\n", "from augment.research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "from augment.research.static_analysis.fim_prompt import _format_middle\n", "from augment.research.static_analysis.fim_sampling import CSTFimSampler, FimProblem\n", "from augment.research.static_analysis.usage_analysis import ParsedFile\n", "from augment.research.core.model_input import ModelInput\n", "\n", "\n", "from augment.experimental.vzhao.data import common\n", "from augment.experimental.vzhao.data import constants"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Data paths.\n", "RAW_REPO_DATA = \"s3a://the-stack-processed/by-repo\"\n", "\n", "# S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/test\"\n", "# S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard\"\n", "S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg\"\n", "\n", "# Path to the final dataset for training.\n", "DATA_OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1012_6pos_32total_hardneg\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["CONFIG = dict(\n", "    allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "    random_seed=74912,\n", "    # === Stage 1 ===\n", "    # Sampling Repositories.\n", "    repo_min_size=200000,\n", "    repo_max_size=5000000,\n", "    limit_repos=35000,\n", "    downsample_small=True,\n", "    # === Stage 2 ===\n", "    # FIM sampler Configs.\n", "    every_n_lines=150,\n", "    max_problems_per_file=4,\n", "    max_prefix_chars=8000,\n", "    max_suffix_chars=8000,\n", "    # Retrieval Configs.\n", "    num_retrieved_chunks=127,\n", "    retriever={\n", "        \"scorer\": {\n", "            \"name\": \"diff_boykin\",\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 40,\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"simple_query\",\n", "            \"max_lines\": 20,\n", "        },\n", "    },\n", "    # === No Stage 3 ===\n", "    # === Stage 4 ===\n", "    stage_4=dict(\n", "        num_rows=None,\n", "        num_partitions=4096,\n", "    ),\n", "    # === Stage 5 ===\n", "    stage_5=dict(\n", "        language_model={\n", "            \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "            \"name\": \"rogue\",\n", "            \"prompt\": {\n", "                \"max_prefix_tokens\": 100,\n", "                \"max_suffix_tokens\": 100,\n", "                \"max_retrieved_chunk_tokens\": -1,\n", "                \"max_prompt_tokens\": 750,\n", "            },\n", "        },\n", "    ),\n", "    # === Stage 6 ===\n", "    stage_6={\n", "        # Stage 6:\n", "        # args for positives.\n", "        \"min_pos_ppg\": 0.12,\n", "        \"min_pos_ppl\": -0.4,\n", "        \"num_positives\": 6,\n", "        \"num_hard_positives\": 1,\n", "        \"hard_pos_topk\": 16,\n", "        # args for negatives.\n", "        \"max_neg_ppg\": 0.03,\n", "        \"num_hard_negatives\": 0,\n", "        \"hard_neg_topk\": 4,\n", "        \"total_chunks\": 32,\n", "    },\n", "    # === Stage 7 ===\n", "    stage_7=dict(\n", "        encoder_seq_length=1024,\n", "        query_formatter={\n", "            \"name\": \"simple_query\",\n", "            \"max_tokens\": 1024 - 1,\n", "            \"max_lines\": -1,\n", "            \"add_path\": <PERSON>als<PERSON>,\n", "            \"add_sos\": <PERSON><PERSON><PERSON>,\n", "        },\n", "        key_formatter={\n", "            \"name\": \"simple_document\",\n", "            \"add_path\": <PERSON>als<PERSON>,\n", "            \"max_tokens\": 1024 - 1,\n", "        },\n", "        max_retrieved_docs=32 - 1,\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 1: <PERSON><PERSON> and filters <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["STAGE1_URI = os.path.join(S3ROOT, \"stage_1\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 1:====================================================>(2076 + 5) / 2081]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 284413 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/10 06:35:50 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["def stage1():\n", "    spark = k8s_session(max_workers=100)\n", "\n", "    print(\"Processing retrieval samples\")\n", "    df = spark.read.parquet(RAW_REPO_DATA)\n", "\n", "    languages = CONFIG[\"allowed_languages\"]\n", "    if languages:\n", "        languages = [lang.lower() for lang in languages]\n", "        df = df.filter(\n", "            df[constants.REPO_LANG_COLUMN][constants.REPO_LANG_SUBCOL].isin(languages)\n", "        )\n", "\n", "    # if hasattr(config, \"retrieval_languages\"):\n", "    #     config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "    df = common.filter_by_repo_size(\n", "        df, min_size=CONFIG[\"repo_min_size\"], max_size=CONFIG[\"repo_max_size\"]\n", "    )\n", "\n", "    print(f\"Processing {df.count()} repos\", flush=True)\n", "    df = df.limit(CONFIG[\"limit_repos\"])\n", "\n", "    # About 100 to 200 repos per partition.\n", "    df = df.repartition(2000)\n", "    # Perform repo-specific processing\n", "    df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "    spark.stop()\n", "\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage1()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 2: Run Retrieval Augmentation\n", "\n", "This is one job per repo."]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [], "source": ["# STAGE1_URI = os.path.join(\"s3a://augment-temporary/vzhao/ethanol_rag/test\", \"stage_1\")\n", "STAGE2_URI = os.path.join(S3ROOT, \"stage_2_retrieval\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame) -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "    # TODO(michiel) update for retriever query formatting options\n", "    retrieval_database = create_retriever(CONFIG['retriever'])\n", "\n", "    if CONFIG['retriever']['name'] != \"bm25\":\n", "        retrieval_database.scorer.load()\n", "\n", "    sampler = CSTFimSampler()\n", "    sampler.rng.seed(CONFIG['random_seed'])\n", "\n", "    tokenizer = StarCoderTokenizer()\n", "\n", "    for files in batch.file_list:\n", "        yield from common.process_repo(\n", "            files,\n", "            sampler=sampler,\n", "            retrieval_database=retrieval_database,\n", "            tokenizer=tokenizer,\n", "            allowed_languages=CONFIG['allowed_languages'],\n", "            downsample_small=CONFIG['downsample_small'],\n", "            every_n_lines=CONFIG['every_n_lines'],\n", "            max_problems_per_file=CONFIG['max_problems_per_file'],\n", "            random_seed=CONFIG['random_seed'],\n", "            max_prefix_chars=CONFIG['max_prefix_chars'],\n", "            max_suffix_chars=CONFIG['max_suffix_chars'],\n", "            num_retrieved_chunks=CONFIG['num_retrieved_chunks'],\n", "        )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/10 06:42:38 WARN Utils: Your hostname, vzhao-dev resolves to a loopback address: *********; using ************* instead (on interface enp6s0)\n", "23/10/10 06:42:38 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "23/10/10 06:43:11 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "23/10/10 07:31:12 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["def stage2():\n", "    spark_conf = {\n", "        \"spark.executor.pyspark.memory\": \"50G\",\n", "        \"spark.executor.memory\": \"30G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "        \"spark.task.cpus\": \"5\",\n", "    }\n", "    spark = k8s_session(\n", "        max_workers=128,\n", "        conf=spark_conf,\n", "        gpu_type=\"RTX_A5000\",\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        process_partition_pandas,\n", "        input_path=STAGE1_URI,\n", "        output_path=STAGE2_URI,\n", "        timeout=3600,  # one hour timeout\n", "        batch_size=100,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage2()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'s3a://augment-temporary/vzhao/ppl_gain/1009/stage_2_retrieval'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["STAGE2_URI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 4: Reshuffle"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"data": {"text/plain": ["'s3a://augment-temporary/vzhao/ppl_gain/1009/stage_4_reshuffle'"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')\n", "STAGE4_URI"]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/10 16:46:01 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "                                                                                \r"]}], "source": ["common.reshuffle(\n", "    STAGE2_URI,\n", "    STAGE4_URI,\n", "    num_rows=CONFIG['num_rows'],\n", "    num_partitions=CONFIG['num_partitions'],\n", "    columns=[\n", "        \"prefix\",\n", "        \"suffix\",\n", "        \"middle\",\n", "        \"file_path\",\n", "        \"retrieved_chunks\",\n", "    ],\n", "    max_workers=32,\n", "    override=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5a: Compute Perplexity\n", "\n", "Stage 5a works on computing PPL for all retrieved chunks from scratch."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# CONFIG['language_model'] = {\n", "#     \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "#     \"name\": \"rogue\",\n", "#     \"prompt\": {\n", "#         \"max_prefix_tokens\": 100,\n", "#         \"max_suffix_tokens\": 100,\n", "#         \"max_retrieved_chunk_tokens\": -1,\n", "#         \"max_prompt_tokens\": 750,\n", "#     }\n", "# }"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["'s3a://augment-temporary/vzhao/ppl_gain/test/stage_5_ppg'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')\n", "STAGE5_URI"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from augment.research.data.spark.pipelines.utils import map_parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def prepend_empty_chunk(retrieved_chunks) -> pd.Series:\n", "    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(\n", "        retrieved_chunks\n", "    )\n", "    return pd.Series(\n", "        {\"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks)}\n", "    )\n", "\n", "\n", "def compute_ppl(\n", "    prefix,\n", "    suffix,\n", "    middle,\n", "    file_path,\n", "    retrieved_chunks,\n", ") -> pd.Series:\n", "    def print_with_time(*args):\n", "        print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "    global cached_reranker\n", "    if \"cached_reranker\" not in globals():\n", "        # Construct a reranker\n", "        print_with_time(\"Constructing the model...\")\n", "        model = create_model(CONFIG[\"language_model\"])\n", "\n", "        print_with_time(\"Constructing the reranker...\")\n", "        cached_reranker = create_reranker(\n", "            model,\n", "            config={\n", "                \"name\": \"oracle_perplexity_reranker\",\n", "                \"top_k\": 256,\n", "                \"batchsize\": 4,\n", "            },\n", "        )\n", "\n", "        # Load the reranking model\n", "        print_with_time(\"Loading the model...\")\n", "        model.load()\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),\n", "        path=file_path,\n", "    )\n", "\n", "    print_with_time(f\"Reranking...\")\n", "    scores = cached_reranker._score(model_input, middle)\n", "    assert len(scores) == len(\n", "        common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ), f\"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}\"\n", "    return pd.Series({\"ppl\": json.dumps(scores)})\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def compute_ppl_gain(retrieved_chunks, ppl):\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ppl = json.loads(ppl)\n", "\n", "    ppl_empty = ppl[0]\n", "    ppg = [s - ppl_empty for s in ppl]\n", "    retrieval_rank = list(range(len(retrieved_chunks)))\n", "    retrieval_rank, ppg, ppl, retrieved_chunks = list(\n", "        zip(\n", "            *sorted(\n", "                zip(retrieval_rank, ppg, ppl, retrieved_chunks),\n", "                key=lambda x: x[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "    )\n", "    return pd.Series(\n", "        {\n", "            \"ppg\": json.dumps(ppg),\n", "            \"ppl\": json.dumps(ppl),\n", "            \"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks),\n", "            \"retrieval_rank\": json.dumps(retrieval_rank),\n", "        }\n", "    )\n", "\n", "\n", "def stage5a():\n", "    spark_gpu = k8s_session(\n", "        max_workers=128,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"RTX_A5000\",\n", "    )\n", "    files = map_parquet.list_files(\n", "        spark_gpu, STAGE4_URI, suffix=\"parquet\", include_path=False\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                prepend_empty_chunk,\n", "                compute_ppl,\n", "                compute_ppl_gain,\n", "            ]\n", "        ),\n", "        # input_path=STAGE4_URI,\n", "        input_path=STAGE4_URI,\n", "        output_path=STAGE5_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5a()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5b: Add PPL of Empty Chunk.\n", "\n", "Prepend ppl of empty chunk to the existing data."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"data": {"text/plain": ["'s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "\n", "IGOR_STAGE5_URI = 's3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/'\n", "STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')\n", "STAGE5_URI"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/10 20:10:46 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Creating extension directory /home/<USER>/.cache/torch_extensions/py39_cu118/utils...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "Loading extension module utils...\n", "/mnt/efs/augment/python_env/2023-10-10/vzhao-dev/ac3b9ff9-69d9-4c7a-b59b-829a03cc1ef2/lib/python3.9/site-packages/augment/research/models/meta_gpt_neox_model.py:365: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n", "/mnt/efs/augment/python_env/2023-10-10/vzhao-dev/ac3b9ff9-69d9-4c7a-b59b-829a03cc1ef2/lib/python3.9/site-packages/augment/research/models/meta_gpt_neox_model.py:365: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n", "/mnt/efs/augment/python_env/2023-10-10/vzhao-dev/ac3b9ff9-69d9-4c7a-b59b-829a03cc1ef2/lib/python3.9/site-packages/augment/research/models/meta_gpt_neox_model.py:365: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n", "\n"]}], "source": ["from augment.research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "\n", "@map_parquet.allow_unused_args\n", "def prepend_empty_chunk(retrieved_chunks) -> pd.Series:\n", "    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(\n", "        retrieved_chunks\n", "    )\n", "    return pd.Series(\n", "        {\"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks)}\n", "    )\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def prepend_score_empty_chunk(\n", "    prefix, suffix, middle, file_path, retrieved_chunks, ppl_scores\n", ") -> pd.Series:\n", "    def print_with_time(*args):\n", "        print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "    global cached_reranker\n", "    if \"cached_reranker\" not in globals():\n", "        # Construct a reranker\n", "        print_with_time(\"Constructing the model...\")\n", "        model = create_model(CONFIG[\"language_model\"])\n", "\n", "        print_with_time(\"Constructing the reranker...\")\n", "        cached_reranker = create_reranker(\n", "            model,\n", "            config={\n", "                \"name\": \"oracle_perplexity_reranker\",\n", "                \"top_k\": 256,\n", "                \"batchsize\": 4,\n", "            },\n", "        )\n", "\n", "        # Load the reranking model\n", "        print_with_time(\"Loading the model...\")\n", "        model.load()\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        retrieved_chunks=[EMPTY_CHUNK],\n", "        path=file_path,\n", "    )\n", "    print_with_time(f\"Scoring...\")\n", "    scores = cached_reranker._score(model_input, middle)\n", "    assert len(scores) == 1, f\"`scores` should contain only 1 score.\"\n", "    return pd.Series(\n", "        {\n", "            \"ppl\": json.dumps([scores[0]] + json.loads(ppl_scores)),\n", "            \"retrieved_chunks\": common.serialize_retrieved_chunks(\n", "                [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(retrieved_chunks)\n", "            ),\n", "        }\n", "    )\n", "\n", "@map_parquet.allow_unused_args\n", "def compute_ppl_gain(retrieved_chunks, ppl):\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ppl = json.loads(ppl)\n", "\n", "    ppl_empty = ppl[0]\n", "    ppg = [s - ppl_empty for s in ppl]\n", "    retrieval_rank = list(range(len(retrieved_chunks)))\n", "    retrieval_rank, ppg, ppl, retrieved_chunks = list(\n", "        zip(\n", "            *sorted(\n", "                zip(retrieval_rank, ppg, ppl, retrieved_chunks),\n", "                key=lambda x: x[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "    )\n", "    return pd.Series(\n", "        {\n", "            \"ppg\": json.dumps(ppg),\n", "            \"ppl\": json.dumps(ppl),\n", "            \"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks),\n", "            \"retrieval_rank\": json.dumps(retrieval_rank),\n", "        }\n", "    )\n", "\n", "\n", "def stage5b():\n", "    spark_gpu = k8s_session(\n", "        max_workers=160,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"RTX_A5000\",\n", "    )\n", "    files = map_parquet.list_files(\n", "        spark_gpu, IGOR_STAGE5_URI, suffix=\"parquet\", include_path=False\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                prepend_score_empty_chunk,\n", "                compute_ppl_gain,\n", "            ]\n", "        ),\n", "        input_path=IGOR_STAGE5_URI,\n", "        # input_path=[os.path.join(IGOR_STAGE5_URI, files[0])],\n", "        output_path=STAGE5_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5b()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [start-from-here] Stage 6: Hard Example Mining and Label Balancing"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["CONFIG.update(\n", "    {\n", "        \"stage_6\": {\n", "            # Stage 6:\n", "            # args for positives.\n", "            \"min_pos_ppg\": 0.12,\n", "            \"min_pos_ppl\": -0.4,\n", "            \"num_positives\": 6,\n", "            \"num_hard_positives\": 0,\n", "            \"hard_pos_topk\": 16,\n", "            # args for negatives.\n", "            \"max_neg_ppg\": 0.03,\n", "            \"num_hard_negatives\": 8,\n", "            \"hard_neg_topk\": 8,\n", "            \"total_chunks\": 32,\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import pandas as pd\n", "import numpy as np\n", "from typing import Iterator\n", "\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from scipy.stats import rankdata, kendalltau\n", "\n", "def create_balance_labels_fn(\n", "    *,\n", "    # args for positives.\n", "    min_pos_ppg: float,\n", "    min_pos_ppl: float,\n", "    num_positives: int,\n", "    num_hard_positives: int,\n", "    hard_pos_topk: int,\n", "    # args for negatives.\n", "    max_neg_ppg: float,\n", "    num_hard_negatives: int,\n", "    hard_neg_topk: int,\n", "    total_chunks: int,\n", "):\n", "    \"\"\"Returns a row wise function for hard example mining and label balancing.\n", "\n", "    Logics:\n", "      * Hard Positive Mining:\n", "        * Find all chunks whose `ppg` > `min_pos_ppg` and `ppl` > `min_pos_ppl`.\n", "        * Sort chunks by reverse `retrieval_rank`\n", "        * Take top `max_positives`.\n", "      * Hard Negative Mining:\n", "        * Find all chunks whose `ppg` < `max_neg_ppg`.\n", "        * Sort chunks by `retrieval_rank`\n", "        * Take top `total_chunks` - `num_positives`.\n", "    \"\"\"\n", "    if num_hard_positives > num_positives:\n", "        raise ValueError(\n", "            f\"Number of hard positivies ({num_hard_positives}) should be less than or equal to number of positives ({num_positives}).\"\n", "        )\n", "\n", "    @map_parquet.allow_unused_args\n", "    def balance_labels(\n", "        retrieved_chunks, ppl, ppg, retrieval_rank\n", "    ) -> Iterator[pd.Series]:\n", "        \"\"\"Balance the labels of retrieved chunks.\"\"\"\n", "        df = pd.DataFrame(\n", "            {   \n", "                # Only deserialize the first level.\n", "                \"retrieved_chunks\": [json.dumps(c) for c in json.loads(retrieved_chunks)],\n", "                \"ppl\": common.maybe_json_loads(ppl),\n", "                \"ppg\": common.maybe_json_loads(ppg),\n", "                \"ppg_raw\": common.maybe_json_loads(ppg),\n", "                \"retrieval_rank\": common.maybe_json_loads(retrieval_rank),\n", "            }\n", "        )\n", "        # Discard empty chunk.\n", "        df = df.loc[df[\"ppg\"] != 0, :]\n", "\n", "        # === Positive Mining ===\n", "        df_positives = df[(df[\"ppg\"] > min_pos_ppg) & (df['ppl'] > min_pos_ppl)]\n", "        if df_positives.empty:\n", "            return\n", "        # Candidates for hard positives.\n", "        candidates = df_positives[\n", "            df_positives[\"retrieval_rank\"] > hard_pos_topk\n", "        ].sort_values(\"retrieval_rank\", ascending=False)\n", "        df_hard_pos = candidates.iloc[:num_hard_positives, :]\n", "        if num_hard_positives and df_hard_pos.empty:\n", "            # No hard positives.\n", "            return\n", "        # Candidates for regular positives.\n", "        candidates = df_positives.merge(df_hard_pos, how=\"left\", indicator=True)\n", "        candidates = candidates[candidates[\"_merge\"] == \"left_only\"]\n", "        df_other_pos = candidates.sort_values(\"ppg\", ascending=False).iloc[\n", "            : (num_positives - df_hard_pos.shape[0]), :\n", "        ]\n", "        # Final dataframe of positives.\n", "        df_positives = pd.concat([df_other_pos, df_hard_pos], axis=0)\n", "        assert (\n", "            df_positives.shape[0] <= num_positives\n", "        ), f\"Invalid number of positives: {df_positives.shape[0]}\"\n", "\n", "        # === Negative Mining ===\n", "        num_negatives = total_chunks - df_positives.shape[0]\n", "        df_negatives = df[df[\"ppg\"] < max_neg_ppg]\n", "        if df_negatives.shape[0] < num_negatives:\n", "            # Not enough negatives to sample from.\n", "            return\n", "        # Candidates for hard negatives.\n", "        nonlocal num_hard_negatives\n", "        num_hard_negatives = min(num_hard_negatives, num_negatives)\n", "        candidates = df_negatives[\n", "            df_negatives[\"retrieval_rank\"] < hard_neg_topk\n", "        ].sort_values(\"retrieval_rank\", ascending=True)\n", "        df_hard_neg = candidates.iloc[:num_hard_negatives, :]\n", "        if num_hard_negatives and df_hard_neg.empty:\n", "            return\n", "        # Candidates for regular negatives.\n", "        candidates = df_negatives.merge(df_hard_neg, how=\"left\", indicator=True)\n", "        candidates = candidates[candidates[\"_merge\"] == \"left_only\"]\n", "        df_rand_neg = candidates.sample(num_negatives - df_hard_neg.shape[0])\n", "        # Final dataframe of negatives.\n", "        df_negatives = pd.concat([df_rand_neg, df_hard_neg], axis=0)\n", "        # All negatives should have ppg=0.\n", "        df_negatives[\"ppg\"] = 0\n", "\n", "        df_concat = pd.concat([df_positives, df_negatives], axis=0)\n", "\n", "        llm_ranking = rankdata(-df_concat[\"ppg\"], method='min')\n", "        ret_ranking = rankdata(df_concat[\"retrieval_rank\"], method='min')\n", "        tau = kendalltau(llm_ranking, ret_ranking, variant='b')\n", "\n", "\n", "\n", "        yield pd.Series(\n", "            {\n", "                \"retrieved_chunks\": json.dumps([json.loads(c) for c in df_concat[\"retrieved_chunks\"].to_list()]), \n", "                \"ppl\": json.dumps(df_concat[\"ppl\"].to_list()),\n", "                \"ppg\": json.dumps(df_concat[\"ppg\"].to_list()),\n", "                \"ppg_raw\": json.dumps(df_concat[\"ppg_raw\"].to_list()),\n", "                \"retrieval_rank\": json.dumps(df_concat[\"retrieval_rank\"].to_list()),\n", "                'kendalltau': tau.statistic,\n", "            }\n", "        )\n", "\n", "    return balance_labels"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining\n"]}, {"data": {"text/plain": ["{'min_pos_ppg': 0.12,\n", " 'min_pos_ppl': -0.4,\n", " 'num_positives': 6,\n", " 'num_hard_positives': 0,\n", " 'hard_pos_topk': 16,\n", " 'max_neg_ppg': 0.03,\n", " 'num_hard_negatives': 8,\n", " 'hard_neg_topk': 8,\n", " 'total_chunks': 32}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["STAGE5_URI = 's3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/'\n", "STAGE6_URI = os.path.join(S3ROOT, 'stage_6_mining')\n", "print(STAGE6_URI)\n", "display(CONFIG['stage_6'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/12 22:58:55 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "23/10/12 23:00:22 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["def stage6():\n", "    spark_gpu = k8s_session(max_workers=128)\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                create_balance_labels_fn(\n", "                    min_pos_ppg=CONFIG['stage_6'][\"min_pos_ppg\"],\n", "                    min_pos_ppl=CONFIG['stage_6']['min_pos_ppl'],\n", "                    num_positives=CONFIG['stage_6']['num_positives'],\n", "                    num_hard_positives=CONFIG['stage_6']['num_hard_positives'],\n", "                    hard_pos_topk=CONFIG['stage_6']['hard_pos_topk'],\n", "                    max_neg_ppg=CONFIG['stage_6']['max_neg_ppg'],\n", "                    num_hard_negatives=CONFIG['stage_6']['num_hard_negatives'],\n", "                    hard_neg_topk=CONFIG['stage_6'][\"hard_neg_topk\"],\n", "                    total_chunks=CONFIG['stage_6'][\"total_chunks\"],\n", "                )\n", "            ]\n", "        ),\n", "        input_path=STAGE5_URI,\n", "        output_path=STAGE6_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage6()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7: Token<PERSON>"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# CONFIG.update(\n", "#     dict(\n", "#         encoder_seq_length=1024,\n", "#         query_formatter={\n", "#             \"name\": \"simple_query\",\n", "#             \"max_tokens\": 1024 - 1,\n", "#             \"max_lines\": -1,\n", "#             \"add_path\": <PERSON>als<PERSON>,\n", "#             \"add_sos\": <PERSON><PERSON><PERSON>,\n", "#         },\n", "#         key_formatter={\n", "#             \"name\": \"simple_document\",\n", "#             \"add_path\": <PERSON>als<PERSON>,\n", "#             \"max_tokens\": 1024 - 1,\n", "#         },\n", "#         max_retrieved_docs=32-1,\n", "#     )\n", "# )"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval.prompt_formatters import SimpleQueryFormatter\n", "from augment.research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from augment.research.retrieval import utils\n", "from augment.research.data.spark.pipelines.stages import common as spark_common\n", "\n", "\n", "def create_prompt_formatter(formatter_config):\n", "    cls_name, kwargs = utils.parse_yaml_config(formatter_config)\n", "    return get_prompt_formatter(cls_name, **kwargs)\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def pack_prompts(\n", "    prefix,\n", "    suffix,\n", "    file_path,\n", "    retrieved_chunks,\n", "    ppg,\n", ") -> pd.Series:\n", "    from augment.research.retrieval import prompt_formatters\n", "\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    # XXX temporary hack\n", "    retrieved_chunks = retrieved_chunks[: CONFIG[\"stage_7\"][\"max_retrieved_docs\"]]\n", "    ppg_scores = json.loads(ppg)\n", "\n", "    global cached_query_prompt_formatter, cached_key_prompt_formatter\n", "    if \"cached_query_prompt_formatter\" not in globals():\n", "        cached_query_prompt_formatter = create_prompt_formatter(\n", "            CONFIG[\"stage_7\"][\"query_formatter\"]\n", "        )\n", "    if \"cached_key_prompt_formatter\" not in globals():\n", "        cached_key_prompt_formatter = create_prompt_formatter(\n", "            CONFIG[\"stage_7\"][\"key_formatter\"]\n", "        )\n", "\n", "    end_of_query_token = cached_query_prompt_formatter.tokenizer.vocab[\n", "        \"<|ret-endofquery|>\"\n", "    ]\n", "    end_of_key_token = cached_key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "    pad_token = cached_key_prompt_formatter.tokenizer.pad_id\n", "\n", "    query_prompt = cached_query_prompt_formatter.prepare_prompt(\n", "        ModelInput(prefix=prefix, path=file_path)\n", "    )\n", "    query_prompt.append(end_of_query_token)\n", "    if len(query_prompt) > CONFIG[\"stage_7\"][\"encoder_seq_length\"]:\n", "        raise ValueError(\n", "            f\"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}\"\n", "        )\n", "\n", "    prompt_list = [query_prompt]\n", "\n", "    counter_prompts, counter_long_prompts = 0, 0\n", "    for ppg_score, chunk in zip(ppg_scores, retrieved_chunks):\n", "        counter_prompts += 1\n", "        # Encode the perplexity score into tokens.\n", "        ppg_tokens = cached_key_prompt_formatter.tokenizer.tokenize(f\"{ppg_score}\")\n", "        # Format the suffix of the prompt\n", "        suffix = [end_of_key_token] + ppg_tokens + [pad_token]\n", "\n", "        # Format the prompt\n", "        chunk_prompt = cached_key_prompt_formatter.prepare_prompt(\n", "            ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "        )\n", "        len_to_keep = CONFIG[\"stage_7\"][\"encoder_seq_length\"] - len(suffix)\n", "        if len(chunk_prompt) > len_to_keep:\n", "            counter_long_prompts += 1\n", "        chunk_prompt = chunk_prompt[:len_to_keep]\n", "        chunk_prompt.extend(suffix)\n", "        prompt_list.append(chunk_prompt)\n", "\n", "    for id, chunk_prompt in enumerate(prompt_list):\n", "        if len(chunk_prompt) > CONFIG[\"stage_7\"][\"encoder_seq_length\"]:\n", "            print(\"===================================================\")\n", "            print(cached_key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "            print(\"===================================================\")\n", "            raise ValueError(\n", "                f\"{id} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}\"\n", "            )\n", "\n", "    print(\"LONG PROMPTS\", counter_long_prompts, \"/\", counter_prompts)\n", "\n", "    # This is consumed by `common.unpack_tokens`.\n", "    all_tokens = [\n", "        spark_common.pack_tokens(\n", "            np.pad(\n", "                prompt, (0, 1 + CONFIG[\"stage_7\"][\"encoder_seq_length\"] - len(prompt))\n", "            )\n", "        )\n", "        for prompt in prompt_list\n", "    ]\n", "    return pd.Series({\"prompt_tokens\": all_tokens})"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'encoder_seq_length': 1024,\n", " 'query_formatter': {'name': 'simple_query',\n", "  'max_tokens': 1023,\n", "  'max_lines': -1,\n", "  'add_path': <PERSON><PERSON><PERSON>,\n", "  'add_sos': False},\n", " 'key_formatter': {'name': 'simple_document',\n", "  'add_path': <PERSON><PERSON><PERSON>,\n", "  'max_tokens': 1023},\n", " 'max_retrieved_docs': 31}"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_7_token\n"]}], "source": ["STAGE7_URI = os.path.join(S3ROOT, 'stage_7_token')\n", "display(CONFIG['stage_7'])\n", "print(STAGE7_URI)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/12 23:13:27 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "23/10/12 23:17:31 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["def stage7():\n", "    spark = k8s_session(max_workers=64)\n", "\n", "    mp_result2 = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors([pack_prompts,]),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in mp_result2[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "\n", "\n", "stage7()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8: Explode"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import json\n", "import pyspark.sql.functions as F\n", "import pyspark.sql.types as T\n", "\n", "def to_list(chunk_str: str) -> list[str]:\n", "    return [json.dumps(c) for c in json.loads(chunk_str)]\n", "\n", "ret_chunk_udf = F.udf(lambda x: to_list(x), T.ArrayType(T.StringType()))\n", "float_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.DoubleType()))\n", "int_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.IntegerType()))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_8_explode\n"]}], "source": ["STAGE8_URI = os.path.join(S3ROOT, 'stage_8_explode')\n", "print(STAGE8_URI)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/11 05:49:06 WARN Utils: Your hostname, vzhao-dev resolves to a loopback address: *********; using ************* instead (on interface enp6s0)\n", "23/10/11 05:49:06 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "23/10/11 05:49:41 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "23/10/11 05:53:27 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["# spark = k8s_session(max_workers=128)\n", "\n", "# df = spark.read.parquet(STAGE7_URI)\n", "# df = df.filter(\n", "#     F.size(F.col(\"prompt_tokens\")) == CONFIG[\"stage_7\"][\"max_retrieved_docs\"] + 1\n", "# )\n", "\n", "# df = df.select(\n", "#     \"prefix\",\n", "#     \"suffix\",\n", "#     \"middle\",\n", "#     \"file_path\",\n", "#     F.explode(\n", "#         F.arrays_zip(\n", "#             ret_chunk_udf(<PERSON>.col(\"retrieved_chunks\")).alias(\"retrieved_chunks\"),\n", "#             float_array_udf(<PERSON>.col(\"ppl_scores\")).alias(\"ppl_scores\"),\n", "#             float_array_udf(<PERSON>.col(\"ppl\")).alias(\"ppl\"),\n", "#             float_array_udf(<PERSON>.col(\"ppg\")).alias(\"ppg\"),\n", "#             int_array_udf(<PERSON>.col(\"retrieval_rank\")).alias(\"retrieval_rank\"),\n", "#             \"prompt_tokens\",\n", "#         )\n", "#     ).alias(\"zipped\"),\n", "# ).select(\n", "#     \"prefix\",\n", "#     \"suffix\",\n", "#     \"middle\",\n", "#     \"file_path\",\n", "#     \"zipped.retrieved_chunks\",\n", "#     \"zipped.ppl_scores\",\n", "#     \"zipped.ppl\",\n", "#     \"zipped.ppg\",\n", "#     \"zipped.retrieval_rank\",\n", "#     \"zipped.prompt_tokens\",\n", "# )\n", "\n", "# df.write.parquet(STAGE8_URI)\n", "# spark.stop()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/12 23:22:02 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["spark = k8s_session(max_workers=64)\n", "\n", "df = spark.read.parquet(STAGE7_URI)\n", "df = df.filter(F.size(F.col(\"prompt_tokens\")) == CONFIG[\"stage_7\"]['max_retrieved_docs'] + 1)\n", "df = df.withColumn(\"prompt_tokens\", <PERSON><PERSON>explode(\"prompt_tokens\"))\n", "\n", "df.write.parquet(STAGE8_URI)\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 9: Convert to Dataset for Training"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/augment/user/vincent/data/ppl_gain/1012_6pos_32total_hardneg\n"]}], "source": ["from augment.research.data.spark.pipelines.stages.common import export_indexed_dataset\n", "\n", "class ObjectDict(dict):\n", "    \"\"\"Provides both namespace-like and dict-like access to fields.\n", "\n", "    Allows access to fields using both obj.name notation and obj[\"name\"]\n", "    notation. The latter is useful when \"name\" contains periods, for example.\n", "    \"\"\"\n", "\n", "    def __getattr__(self, name: str):\n", "        if name in self:\n", "            return self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "    def __setattr__(self, name: str, value):\n", "        self[name] = value\n", "\n", "    def __delattr__(self, name: str):\n", "        if name in self:\n", "            del self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "print(DATA_OUTPUT_PATH)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_8_explode\n", "Number of partitions: 242\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/12 23:27:40 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["import os\n", "\n", "if not os.path.exists(DATA_OUTPUT_PATH):\n", "    os.makedirs(DATA_OUTPUT_PATH)\n", "\n", "spark = k8s_session()\n", "\n", "export_indexed_dataset(\n", "    config=ObjectDict(\n", "        {\n", "            \"input\": STAGE8_URI,\n", "            \"output\": DATA_OUTPUT_PATH,\n", "            \"samples_column\": \"prompt_tokens\",\n", "            'num_validation_samples': 8192,\n", "        }\n", "    ),\n", "    spark=spark,\n", "    tokenizer=create_prompt_formatter(CONFIG['stage_7']['query_formatter']).tokenizer\n", ")\n", "\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# TO Kill a Running Spark Job\n", "\n", "```bash\n", "# Get appName\n", "kubectl get pods | grep vzhao\n", "\n", "```"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark= SparkSession.builder.appName('vzhao-dev').getOrCreate()\n", "spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
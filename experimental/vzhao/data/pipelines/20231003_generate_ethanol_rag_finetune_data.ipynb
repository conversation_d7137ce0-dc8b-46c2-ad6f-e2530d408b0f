{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# My custom library.\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "# Imports\n", "import json\n", "import os\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from augment.research.data.spark import k8s_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from augment.research.eval.harness.factories import create_retriever\n", "from augment.research.retrieval.types import Chunk, Document\n", "from augment.research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "from augment.research.static_analysis.fim_prompt import _format_middle\n", "from augment.research.static_analysis.fim_sampling import CSTFimSampler, FimProblem\n", "from augment.research.static_analysis.usage_analysis import ParsedFile\n", "from augment.research.core.model_input import ModelInput\n", "\n", "\n", "from augment.experimental.vzhao.data import common\n", "from augment.experimental.vzhao.data import constants"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Data paths.\n", "RAW_REPO_DATA = \"s3a://the-stack-processed/by-repo\"\n", "# S3ROOT = \"s3a://augment-temporary/vzhao/ethanol_rag/0930\"\n", "S3ROOT = \"s3a://augment-temporary/vzhao/ethanol_rag/test\"\n", "# Path to the final dataset for training.\n", "# DATA_OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/ethanol_rag/0930\"\n", "DATA_OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/ethanol_rag/test\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["CONFIG = dict(\n", "    allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "    random_seed=74912,\n", "\n", "    # Sampling Repositories.\n", "    repo_min_size=200000,\n", "    repo_max_size=5000000,\n", "    # limit_repos=35000,\n", "    limit_repos=100,\n", "    downsample_small=True,\n", "\n", "    # FIM sampler Configs.\n", "    every_n_lines=150,\n", "    max_problems_per_file=4,\n", "    max_prefix_chars=8000,\n", "    max_suffix_chars=8000,\n", "\n", "    # Retrieval Configs.\n", "    num_retrieved_chunks=40,\n", "    retriever={\n", "        \"name\": \"ethanol\",\n", "        \"chunker\": {\n", "            'name':\"line_level\",\n", "            'max_lines_per_chunk': 40\n", "        },\n", "        'query_formatter': {\n", "            'name': 'simple_query',\n", "            'max_lines': 20,\n", "        }\n", "    },\n", "\n", "    # Prompt Formatter Configs.\n", "    max_prefix_tokens=1280,\n", "    max_suffix_tokens=768,\n", "    max_retrieved_chunk_tokens=-1,\n", "    max_prompt_tokens=3838,\n", "    max_target_tokens=256,\n", "    retrieval_dropout=0.0,\n", "    preamble=\"\",\n", "    prepend_path_to_retrieved=True,\n", "    # \"add_retrieval_after_context\": False,\n", "    add_retrieval_after_context=True,\n", "    only_truncate_true_prefix=True,\n", "    always_use_suffix_token=True,\n", "    recent_prefix_char_len=250,\n", "    prefix_char_offset=200,\n", "\n", "    # LLM training.\n", "    seq_len=4097,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1: Sam<PERSON> repos for training."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["STAGE1_URI = os.path.join(S3ROOT, \"stage_1\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/10 00:29:55 WARN Utils: Your hostname, vzhao-dev resolves to a loopback address: *********; using ************* instead (on interface enp6s0)\n", "23/10/10 00:29:55 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/10 00:30:29 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "[Stage 3:>                                                          (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 284413 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/10 00:32:08 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "# At 2000 partitions each one is between 100 to 200 repos.\n", "# Probably don't want more than 20000 partitions in anycase because we are\n", "# gonna spend most of the time initalizing retrieval databases to that limit.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(RAW_REPO_DATA)\n", "\n", "languages = CONFIG[\"allowed_languages\"]\n", "if languages:\n", "    languages = [lang.lower() for lang in languages]\n", "    df = df.filter(\n", "        df[constants.REPO_LANG_COLUMN][constants.REPO_LANG_SUBCOL].isin(languages)\n", "    )\n", "\n", "# if hasattr(config, \"retrieval_languages\"):\n", "#     config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = common.filter_by_repo_size(\n", "    df, min_size=CONFIG[\"repo_min_size\"], max_size=CONFIG[\"repo_max_size\"]\n", ")\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "df = df.limit(CONFIG[\"limit_repos\"])\n", "\n", "\n", "df = df.repartition(2000)\n", "# Perform repo-specific processing\n", "df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 2: Run Retrieval Augmentation\n", "\n", "This is one job per repo."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["STAGE2_URI = os.path.join(S3ROOT, \"stage_2\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame) -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "    # TODO(michiel) update for retriever query formatting options\n", "    retrieval_database = create_retriever(CONFIG['retriever'])\n", "\n", "    if CONFIG['retriever']['name'] != \"bm25\":\n", "        retrieval_database.scorer.load()\n", "\n", "    sampler = CSTFimSampler()\n", "    sampler.rng.seed(CONFIG['random_seed'])\n", "\n", "    tokenizer = StarCoderTokenizer()\n", "\n", "    for files in batch.file_list:\n", "        yield from common.process_repo(\n", "            files,\n", "            sampler=sampler,\n", "            retrieval_database=retrieval_database,\n", "            tokenizer=tokenizer,\n", "            allowed_languages=CONFIG['allowed_languages'],\n", "            downsample_small=CONFIG['downsample_small'],\n", "            every_n_lines=CONFIG['every_n_lines'],\n", "            max_problems_per_file=CONFIG['max_problems_per_file'],\n", "            random_seed=CONFIG['random_seed'],\n", "            max_prefix_chars=CONFIG['max_prefix_chars'],\n", "            max_suffix_chars=CONFIG['max_suffix_chars'],\n", "            num_retrieved_chunks=CONFIG['num_retrieved_chunks'],\n", "        )\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/10 00:46:41 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "23/10/10 00:48:56 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/10 01:00:49 WARN JavaUtils: Attempt to delete using native Unix OS command failed for path = /tmp/spark-49565458-3439-4087-85b6-6bc757f8deea. Falling back to Java IO way\n", "java.io.IOException: Failed to delete: /tmp/spark-49565458-3439-4087-85b6-6bc757f8deea\n", "\tat org.apache.spark.network.util.JavaUtils.deleteRecursivelyUsingUnixNative(JavaUtils.java:177)\n", "\tat org.apache.spark.network.util.JavaUtils.deleteRecursively(JavaUtils.java:113)\n", "\tat org.apache.spark.network.util.JavaUtils.deleteRecursively(JavaUtils.java:94)\n", "\tat org.apache.spark.util.Utils$.deleteRecursively(Utils.scala:1231)\n", "\tat org.apache.spark.util.ShutdownHookManager$.$anonfun$new$4(ShutdownHookManager.scala:65)\n", "\tat org.apache.spark.util.ShutdownHookManager$.$anonfun$new$4$adapted(ShutdownHookManager.scala:62)\n", "\tat scala.collection.IndexedSeqOptimized.foreach(IndexedSeqOptimized.scala:36)\n", "\tat scala.collection.IndexedSeqOptimized.foreach$(IndexedSeqOptimized.scala:33)\n", "\tat scala.collection.mutable.ArrayOps$ofRef.foreach(ArrayOps.scala:198)\n", "\tat org.apache.spark.util.ShutdownHookManager$.$anonfun$new$2(ShutdownHookManager.scala:62)\n", "\tat org.apache.spark.util.SparkShutdownHook.run(ShutdownHookManager.scala:214)\n", "\tat org.apache.spark.util.SparkShutdownHookManager.$anonfun$runAll$2(ShutdownHookManager.scala:188)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:2088)\n", "\tat org.apache.spark.util.SparkShutdownHookManager.$anonfun$runAll$1(ShutdownHookManager.scala:188)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat scala.util.Try$.apply(Try.scala:213)\n", "\tat org.apache.spark.util.SparkShutdownHookManager.runAll(ShutdownHookManager.scala:188)\n", "\tat org.apache.spark.util.SparkShutdownHookManager$$anon$2.run(ShutdownHookManager.scala:178)\n", "\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)\n", "\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n"]}], "source": ["# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "    \"spark.task.cpus\": \"5\",\n", "}\n", "spark = k8s_session(\n", "    max_workers=64,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    process_partition_pandas,\n", "    input_path=STAGE1_URI,\n", "    output_path=STAGE2_URI,\n", "    timeout=3600,  # one hour timeout\n", "    batch_size=100,\n", ")\n", "spark.stop()\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_path</th>\n", "      <th>output_path</th>\n", "      <th>status</th>\n", "      <th>timeout</th>\n", "      <th>walltime_ms</th>\n", "      <th>exit_code</th>\n", "      <th>max_memory_gb</th>\n", "      <th>total_memory_gb</th>\n", "      <th>min_free_gb</th>\n", "      <th>stdout</th>\n", "      <th>stderr</th>\n", "      <th>profile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>36781</td>\n", "      <td>0</td>\n", "      <td>2.611607</td>\n", "      <td>35.0</td>\n", "      <td>28.599644</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>25264</td>\n", "      <td>0</td>\n", "      <td>3.107517</td>\n", "      <td>35.0</td>\n", "      <td>28.197208</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>45834</td>\n", "      <td>0</td>\n", "      <td>3.096092</td>\n", "      <td>35.0</td>\n", "      <td>28.207275</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>50300</td>\n", "      <td>0</td>\n", "      <td>3.096687</td>\n", "      <td>35.0</td>\n", "      <td>28.200283</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>19892</td>\n", "      <td>0</td>\n", "      <td>2.325268</td>\n", "      <td>35.0</td>\n", "      <td>28.853828</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>22425</td>\n", "      <td>0</td>\n", "      <td>3.075089</td>\n", "      <td>35.0</td>\n", "      <td>28.207050</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>26661</td>\n", "      <td>0</td>\n", "      <td>2.573532</td>\n", "      <td>35.0</td>\n", "      <td>28.488869</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>20128</td>\n", "      <td>0</td>\n", "      <td>2.332264</td>\n", "      <td>35.0</td>\n", "      <td>28.720753</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>25681</td>\n", "      <td>0</td>\n", "      <td>3.082428</td>\n", "      <td>35.0</td>\n", "      <td>28.128456</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/test...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>18331</td>\n", "      <td>0</td>\n", "      <td>2.355309</td>\n", "      <td>35.0</td>\n", "      <td>28.756733</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>101 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                            input_path  \\\n", "0    s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "1    s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "2    s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "3    s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "4    s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "..                                                 ...   \n", "96   s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "97   s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "98   s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "99   s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "100  s3a://augment-temporary/vzhao/ethanol_rag/test...   \n", "\n", "                                           output_path   status  timeout  \\\n", "0    s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "1    s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "2    s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "3    s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "4    s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "..                                                 ...      ...      ...   \n", "96   s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "97   s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "98   s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "99   s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "100  s3a://augment-temporary/vzhao/ethanol_rag/test...  success    False   \n", "\n", "     walltime_ms  exit_code  max_memory_gb  total_memory_gb  min_free_gb  \\\n", "0          36781          0       2.611607             35.0    28.599644   \n", "1          25264          0       3.107517             35.0    28.197208   \n", "2          45834          0       3.096092             35.0    28.207275   \n", "3          50300          0       3.096687             35.0    28.200283   \n", "4          19892          0       2.325268             35.0    28.853828   \n", "..           ...        ...            ...              ...          ...   \n", "96         22425          0       3.075089             35.0    28.207050   \n", "97         26661          0       2.573532             35.0    28.488869   \n", "98         20128          0       2.332264             35.0    28.720753   \n", "99         25681          0       3.082428             35.0    28.128456   \n", "100        18331          0       2.355309             35.0    28.756733   \n", "\n", "                                                stdout stderr profile  \n", "0    [process_file] Start processing s3a://augment-...                 \n", "1    [process_file] Start processing s3a://augment-...                 \n", "2    [process_file] Start processing s3a://augment-...                 \n", "3    [process_file] Start processing s3a://augment-...                 \n", "4    [process_file] Start processing s3a://augment-...                 \n", "..                                                 ...    ...     ...  \n", "96   [process_file] Start processing s3a://augment-...                 \n", "97   [process_file] Start processing s3a://augment-...                 \n", "98   [process_file] Start processing s3a://augment-...                 \n", "99   [process_file] Start processing s3a://augment-...                 \n", "100  [process_file] Start processing s3a://augment-...                 \n", "\n", "[101 rows x 12 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result['task_info']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 3: Combine and Generate Prompts"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from typing import List, Dict, Any\n", "from augment.research.core.prompt_formatters import PromptFormatterRogue\n", "from augment.research.retrieval import utils as rutils\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "def generate_prompt(\n", "    prefix: str,\n", "    middle: str,\n", "    suffix: str,\n", "    suffix_offset: int,\n", "    middle_char_start: int,\n", "    middle_char_end: int,\n", "    file_path: str,\n", "    retrieved_chunk_str: str,\n", "    tokenizer: StarCoderTokenizer,\n", ") -> List[int]:\n", "    \"\"\"Construct a token prompt.\"\"\"\n", "\n", "    seed = (\n", "        CONFIG['random_seed']\n", "        + int.from_bytes((file_path).encode(), \"little\")\n", "        + middle_char_start\n", "    )\n", "    random.seed(seed)\n", "\n", "    # 10% retrieval dropout\n", "    if CONFIG['retrieval_dropout'] > 0 and random.random() < CONFIG['retrieval_dropout']:\n", "        max_retrieved_chunk_tokens = 0\n", "    else:\n", "        max_retrieved_chunk_tokens = CONFIG['max_retrieved_chunk_tokens']\n", "\n", "    prompt_formatter = PromptFormatterRogue(\n", "        max_prefix_tokens=CONFIG['max_prefix_tokens'],\n", "        max_suffix_tokens=CONFIG['max_suffix_tokens'],\n", "        max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,\n", "        preamble=CONFIG['preamble'],\n", "        # TODO(michiel) Use seperator token and make formatter use at beginning and end\n", "        prepend_path_to_retrieved=CONFIG['prepend_path_to_retrieved'],\n", "        add_retrieval_after_context=CONFIG['add_retrieval_after_context'],\n", "        # only_truncate_true_prefix=config.only_truncate_true_prefix,\n", "        always_use_suffix_token=CONFIG['always_use_suffix_token'],\n", "        recent_prefix_char_len=CONFIG['recent_prefix_char_len'],\n", "        prefix_char_offset=CONFIG['prefix_char_offset'],\n", "    )\n", "    # Make it so we don't load new tokenizer for every row even though we build prompt formatter\n", "    prompt_formatter.tokenizer = tokenizer\n", "    prompt_formatter.max_prompt_tokens = CONFIG['max_prompt_tokens']\n", "\n", "    # Randomly sample prompt formatter settings\n", "\n", "    # 50% of the time use standard settings\n", "\n", "    # Else\n", "    # 20%, use empty suffix\n", "    # 20%, use no retrievals (independent of empty suffix)\n", "\n", "    # 25%, sample max prompt size uniformly over [500, max]\n", "    # 25%, sample size of prefix from [1, max prompt size]\n", "    # 25%, sample size of suffix from [1, max prompt size]\n", "\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunk_str)\n", "\n", "    # Remove chunks that overlap with middle\n", "    filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(middle_char_start, middle_char_end),\n", "        retrieved_chunks,\n", "    )\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        middle=middle,\n", "        suffix=suffix,\n", "        retrieved_chunks=filtered_chunks,\n", "        path=file_path,\n", "    )\n", "\n", "    # TODO(michiel) Add option for sampling different prompt styles\n", "    _, metadata = prompt_formatter.prepare_prompt(model_input)\n", "    # Remove chunks that overlap with prefix or suffix\n", "    new_filtered_chunks = rutils.filter_overlap_chunks(\n", "        file_path,\n", "        rutils.Span(\n", "            middle_char_start - metadata[\"num_prefix_chars_post_truncation\"],\n", "            middle_char_end,\n", "        ),\n", "        filtered_chunks,\n", "    )\n", "    if metadata[\"num_suffix_chars_post_truncation\"] > 0:\n", "        new_filtered_chunks = rutils.filter_overlap_chunks(\n", "            file_path,\n", "            rutils.Span(\n", "                middle_char_end,\n", "                middle_char_end\n", "                + max(metadata[\"num_suffix_chars_post_truncation\"] - suffix_offset, 0),\n", "            ),\n", "            new_filtered_chunks,\n", "        )\n", "\n", "    model_input.retrieved_chunks = new_filtered_chunks\n", "    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)\n", "\n", "    target_tokens = prompt_formatter.tokenizer.tokenize(middle)\n", "\n", "    target_tokens = target_tokens[:CONFIG['max_target_tokens']]\n", "    prompt_tokens += target_tokens\n", "\n", "    return prompt_tokens\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["config = SimpleNamespace(\n", "    **{\n", "        \"random_seed\": 74912,\n", "        \"seq_len\": 4097,\n", "        \"max_prefix_tokens\": 1280,\n", "        \"max_suffix_tokens\": 768,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 3838,\n", "        \"max_target_tokens\": 256,\n", "        \"retrieval_dropout\": 0.0,\n", "        \"preamble\": \"\",\n", "        \"prepend_path_to_retrieved\": True,\n", "        # \"add_retrieval_after_context\": False,\n", "        \"add_retrieval_after_context\": True,\n", "        \"only_truncate_true_prefix\": True,\n", "        \"always_use_suffix_token\": True,\n", "        \"recent_prefix_char_len\": 250,\n", "        \"prefix_char_offset\": 200,\n", "        \"samples_column\": \"prompt_tokens\",\n", "        \"num_validation_samples\": 10000,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/02 17:11:40 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["STAGE3_URI = \"s3a://augment-temporary/vzhao/ethanol_rag/0929/stage_3/\"\n", "\n", "spark = k8s_session()\n", "\n", "tokenizer = StarCoderTokenizer()\n", "result = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        generate_prompt,\n", "        tokenizer=tokenizer,\n", "        config=config,\n", "    ),\n", "    input_path=STAGE2_URI,\n", "    output_path=STAGE3_URI,\n", "    input_columns=[\n", "        \"prefix\",\n", "        \"middle\",\n", "        \"suffix\",\n", "        \"suffix_offset\",\n", "        \"middle_char_start\",\n", "        \"middle_char_end\",\n", "        \"file_path\",\n", "        \"retrieved_chunks\",\n", "    ],\n", "    output_column=\"prompt_tokens\",\n", ")\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_path</th>\n", "      <th>output_path</th>\n", "      <th>status</th>\n", "      <th>timeout</th>\n", "      <th>walltime_ms</th>\n", "      <th>exit_code</th>\n", "      <th>max_memory_gb</th>\n", "      <th>total_memory_gb</th>\n", "      <th>min_free_gb</th>\n", "      <th>stdout</th>\n", "      <th>stderr</th>\n", "      <th>profile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>35431</td>\n", "      <td>0</td>\n", "      <td>0.951759</td>\n", "      <td>45.0</td>\n", "      <td>38.417942</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>37068</td>\n", "      <td>0</td>\n", "      <td>0.947002</td>\n", "      <td>45.0</td>\n", "      <td>38.226944</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>29580</td>\n", "      <td>0</td>\n", "      <td>0.859043</td>\n", "      <td>45.0</td>\n", "      <td>38.543377</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>33915</td>\n", "      <td>0</td>\n", "      <td>0.887802</td>\n", "      <td>45.0</td>\n", "      <td>38.020329</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>32960</td>\n", "      <td>0</td>\n", "      <td>0.865360</td>\n", "      <td>45.0</td>\n", "      <td>38.168499</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1971</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>28894</td>\n", "      <td>0</td>\n", "      <td>0.872753</td>\n", "      <td>45.0</td>\n", "      <td>38.445484</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1972</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>31737</td>\n", "      <td>0</td>\n", "      <td>0.826656</td>\n", "      <td>45.0</td>\n", "      <td>38.275433</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1973</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>23944</td>\n", "      <td>0</td>\n", "      <td>0.761440</td>\n", "      <td>45.0</td>\n", "      <td>38.098595</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1974</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>27174</td>\n", "      <td>0</td>\n", "      <td>0.865635</td>\n", "      <td>45.0</td>\n", "      <td>38.156879</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1975</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>37710</td>\n", "      <td>0</td>\n", "      <td>0.844547</td>\n", "      <td>45.0</td>\n", "      <td>38.882095</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1976 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                             input_path  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "...                                                 ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "\n", "                                            output_path   status  timeout  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "...                                                 ...      ...      ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "\n", "      walltime_ms  exit_code  max_memory_gb  total_memory_gb  min_free_gb  \\\n", "0           35431          0       0.951759             45.0    38.417942   \n", "1           37068          0       0.947002             45.0    38.226944   \n", "2           29580          0       0.859043             45.0    38.543377   \n", "3           33915          0       0.887802             45.0    38.020329   \n", "4           32960          0       0.865360             45.0    38.168499   \n", "...           ...        ...            ...              ...          ...   \n", "1971        28894          0       0.872753             45.0    38.445484   \n", "1972        31737          0       0.826656             45.0    38.275433   \n", "1973        23944          0       0.761440             45.0    38.098595   \n", "1974        27174          0       0.865635             45.0    38.156879   \n", "1975        37710          0       0.844547             45.0    38.882095   \n", "\n", "                                                 stdout stderr profile  \n", "0     [process_file] Start processing s3a://augment-...                 \n", "1     [process_file] Start processing s3a://augment-...                 \n", "2     [process_file] Start processing s3a://augment-...                 \n", "3     [process_file] Start processing s3a://augment-...                 \n", "4     [process_file] Start processing s3a://augment-...                 \n", "...                                                 ...    ...     ...  \n", "1971  [process_file] Start processing s3a://augment-...                 \n", "1972  [process_file] Start processing s3a://augment-...                 \n", "1973  [process_file] Start processing s3a://augment-...                 \n", "1974  [process_file] Start processing s3a://augment-...                 \n", "1975  [process_file] Start processing s3a://augment-...                 \n", "\n", "[1976 rows x 12 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["result['task_info']"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["print(result['task_info']['stderr'][0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 4: <PERSON><PERSON> and <PERSON> Tokens"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "\n", "import numpy as np\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "def pad_pack_tokens(tokens: np.n<PERSON><PERSON>, seq_len, tokenizer: Any) -> bytearray:\n", "    # Spark automatically converts arrays to lists.\n", "    # our thing doesn't\n", "    if len(tokens) > seq_len:\n", "        raise ValueError(f\"token length exceeds seq_len: {len(tokens)} > {seq_len}\")\n", "    num_padding_tokens = seq_len - len(tokens)\n", "    all_tokens = np.pad(\n", "        tokens, (0, num_padding_tokens), constant_values=tokenizer.pad_id\n", "    )\n", "    # No need to use pack_tokens as numpy already pack everything into byte arrays.\n", "    # just need to ensure byteorder is little endian.\n", "    # Also, affirmatively ensure that the array is unsigned 16 bit.\n", "    packed_tokens = all_tokens.astype(np.uint16).newbyteorder(\"<\").tobytes()\n", "    return bytearray(packed_tokens)\n", "\n", "STAGE4_URI = \"s3a://augment-temporary/vzhao/ethanol_rag/0929/stage_4/\""]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/02 17:26:01 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n", "23/10/02 17:26:01 ERROR Utils: Uncaught exception in thread kubernetes-executor-pod-polling-sync\n", "io.fabric8.kubernetes.client.KubernetesClientException: Operation: [list]  for kind: [Pod]  with name: [null]  in namespace: [tenant-augment-eng]  failed.\n", "\tat io.fabric8.kubernetes.client.KubernetesClientException.launderThrowable(KubernetesClientException.java:159)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:429)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:392)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:93)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.$anonfun$run$1(ExecutorPodsPollingSnapshotSource.scala:91)\n", "\tat org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1509)\n", "\tat org.apache.spark.scheduler.cluster.k8s.ExecutorPodsPollingSnapshotSource$PollRunnable.run(ExecutorPodsPollingSnapshotSource.scala:74)\n", "\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)\n", "\tat java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)\n", "\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "Caused by: java.io.InterruptedIOException\n", "\tat io.fabric8.kubernetes.client.dsl.internal.OperationSupport.waitForResult(OperationSupport.java:525)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.BaseOperation.list(BaseOperation.java:427)\n", "\t... 11 more\n", "Caused by: java.lang.InterruptedException\n", "\tat java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:385)\n", "\tat java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2022)\n", "\tat io.fabric8.kubernetes.client.dsl.internal.OperationSupport.waitForResult(OperationSupport.java:520)\n", "\t... 12 more\n"]}], "source": ["tokenizer = StarCoderTokenizer()\n", "\n", "spark = k8s_session()\n", "\n", "result = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer\n", "    ),\n", "    input_path=STAGE3_URI,\n", "    output_path=STAGE4_URI,\n", "    input_columns=[PROMPT_COLUMN],\n", "    output_column=config.samples_column,\n", ")\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_path</th>\n", "      <th>output_path</th>\n", "      <th>status</th>\n", "      <th>timeout</th>\n", "      <th>walltime_ms</th>\n", "      <th>exit_code</th>\n", "      <th>max_memory_gb</th>\n", "      <th>total_memory_gb</th>\n", "      <th>min_free_gb</th>\n", "      <th>stdout</th>\n", "      <th>stderr</th>\n", "      <th>profile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4780</td>\n", "      <td>0</td>\n", "      <td>0.290722</td>\n", "      <td>45.0</td>\n", "      <td>41.384998</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4965</td>\n", "      <td>0</td>\n", "      <td>0.291546</td>\n", "      <td>45.0</td>\n", "      <td>40.823364</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4942</td>\n", "      <td>0</td>\n", "      <td>0.292152</td>\n", "      <td>45.0</td>\n", "      <td>40.879944</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4769</td>\n", "      <td>0</td>\n", "      <td>0.291798</td>\n", "      <td>45.0</td>\n", "      <td>40.897377</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4930</td>\n", "      <td>0</td>\n", "      <td>0.292088</td>\n", "      <td>45.0</td>\n", "      <td>41.325550</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1971</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4873</td>\n", "      <td>0</td>\n", "      <td>0.299374</td>\n", "      <td>45.0</td>\n", "      <td>40.563293</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1972</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4794</td>\n", "      <td>0</td>\n", "      <td>0.299133</td>\n", "      <td>45.0</td>\n", "      <td>40.493977</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1973</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4835</td>\n", "      <td>0</td>\n", "      <td>0.299427</td>\n", "      <td>45.0</td>\n", "      <td>40.259525</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1974</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4776</td>\n", "      <td>0</td>\n", "      <td>0.296795</td>\n", "      <td>45.0</td>\n", "      <td>40.190948</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1975</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4789</td>\n", "      <td>0</td>\n", "      <td>0.298225</td>\n", "      <td>45.0</td>\n", "      <td>40.525169</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1976 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                             input_path  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "...                                                 ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "\n", "                                            output_path   status  timeout  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "...                                                 ...      ...      ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "\n", "      walltime_ms  exit_code  max_memory_gb  total_memory_gb  min_free_gb  \\\n", "0            4780          0       0.290722             45.0    41.384998   \n", "1            4965          0       0.291546             45.0    40.823364   \n", "2            4942          0       0.292152             45.0    40.879944   \n", "3            4769          0       0.291798             45.0    40.897377   \n", "4            4930          0       0.292088             45.0    41.325550   \n", "...           ...        ...            ...              ...          ...   \n", "1971         4873          0       0.299374             45.0    40.563293   \n", "1972         4794          0       0.299133             45.0    40.493977   \n", "1973         4835          0       0.299427             45.0    40.259525   \n", "1974         4776          0       0.296795             45.0    40.190948   \n", "1975         4789          0       0.298225             45.0    40.525169   \n", "\n", "                                                 stdout stderr profile  \n", "0     [process_file] Start processing s3a://augment-...                 \n", "1     [process_file] Start processing s3a://augment-...                 \n", "2     [process_file] Start processing s3a://augment-...                 \n", "3     [process_file] Start processing s3a://augment-...                 \n", "4     [process_file] Start processing s3a://augment-...                 \n", "...                                                 ...    ...     ...  \n", "1971  [process_file] Start processing s3a://augment-...                 \n", "1972  [process_file] Start processing s3a://augment-...                 \n", "1973  [process_file] Start processing s3a://augment-...                 \n", "1974  [process_file] Start processing s3a://augment-...                 \n", "1975  [process_file] Start processing s3a://augment-...                 \n", "\n", "[1976 rows x 12 columns]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["result['task_info']"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["print(result['task_info']['stderr'][0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 5: Convert to Dataset for training"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["from augment.research.data.spark.pipelines.stages.common import export_indexed_dataset"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["class ObjectDict(dict):\n", "    \"\"\"Provides both namespace-like and dict-like access to fields.\n", "\n", "    Allows access to fields using both obj.name notation and obj[\"name\"]\n", "    notation. The latter is useful when \"name\" contains periods, for example.\n", "    \"\"\"\n", "\n", "    def __getattr__(self, name: str):\n", "        if name in self:\n", "            return self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "    def __setattr__(self, name: str, value):\n", "        self[name] = value\n", "\n", "    def __delattr__(self, name: str):\n", "        if name in self:\n", "            del self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/02 17:39:24 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from s3a://augment-temporary/vzhao/ethanol_rag/0929/stage_4/\n", "Number of partitions: 160\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 2:======================================================>(159 + 1) / 160]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/10/02 17:40:46 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/ethanol_rag\"\n", "\n", "spark = k8s_session()\n", "\n", "export_indexed_dataset(\n", "    config=ObjectDict(\n", "        {\n", "            \"input\": STAGE4_URI,\n", "            \"output\": OUTPUT_PATH,\n", "            \"samples_column\": \"prompt_tokens\",\n", "            \"num_validation_samples\": 10000,\n", "        }\n", "    ),\n", "    spark=spark,\n", "    tokenizer=tokenizer,\n", ")\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_path</th>\n", "      <th>output_path</th>\n", "      <th>status</th>\n", "      <th>timeout</th>\n", "      <th>walltime_ms</th>\n", "      <th>exit_code</th>\n", "      <th>max_memory_gb</th>\n", "      <th>total_memory_gb</th>\n", "      <th>min_free_gb</th>\n", "      <th>stdout</th>\n", "      <th>stderr</th>\n", "      <th>profile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4780</td>\n", "      <td>0</td>\n", "      <td>0.290722</td>\n", "      <td>45.0</td>\n", "      <td>41.384998</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4965</td>\n", "      <td>0</td>\n", "      <td>0.291546</td>\n", "      <td>45.0</td>\n", "      <td>40.823364</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4942</td>\n", "      <td>0</td>\n", "      <td>0.292152</td>\n", "      <td>45.0</td>\n", "      <td>40.879944</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4769</td>\n", "      <td>0</td>\n", "      <td>0.291798</td>\n", "      <td>45.0</td>\n", "      <td>40.897377</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4930</td>\n", "      <td>0</td>\n", "      <td>0.292088</td>\n", "      <td>45.0</td>\n", "      <td>41.325550</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1971</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4873</td>\n", "      <td>0</td>\n", "      <td>0.299374</td>\n", "      <td>45.0</td>\n", "      <td>40.563293</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1972</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4794</td>\n", "      <td>0</td>\n", "      <td>0.299133</td>\n", "      <td>45.0</td>\n", "      <td>40.493977</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1973</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4835</td>\n", "      <td>0</td>\n", "      <td>0.299427</td>\n", "      <td>45.0</td>\n", "      <td>40.259525</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1974</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4776</td>\n", "      <td>0</td>\n", "      <td>0.296795</td>\n", "      <td>45.0</td>\n", "      <td>40.190948</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1975</th>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>s3a://augment-temporary/vzhao/ethanol_rag/0929...</td>\n", "      <td>success</td>\n", "      <td>False</td>\n", "      <td>4789</td>\n", "      <td>0</td>\n", "      <td>0.298225</td>\n", "      <td>45.0</td>\n", "      <td>40.525169</td>\n", "      <td>[process_file] Start processing s3a://augment-...</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1976 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                             input_path  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "...                                                 ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...   \n", "\n", "                                            output_path   status  timeout  \\\n", "0     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "2     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "3     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "4     s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "...                                                 ...      ...      ...   \n", "1971  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1972  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1973  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1974  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "1975  s3a://augment-temporary/vzhao/ethanol_rag/0929...  success    False   \n", "\n", "      walltime_ms  exit_code  max_memory_gb  total_memory_gb  min_free_gb  \\\n", "0            4780          0       0.290722             45.0    41.384998   \n", "1            4965          0       0.291546             45.0    40.823364   \n", "2            4942          0       0.292152             45.0    40.879944   \n", "3            4769          0       0.291798             45.0    40.897377   \n", "4            4930          0       0.292088             45.0    41.325550   \n", "...           ...        ...            ...              ...          ...   \n", "1971         4873          0       0.299374             45.0    40.563293   \n", "1972         4794          0       0.299133             45.0    40.493977   \n", "1973         4835          0       0.299427             45.0    40.259525   \n", "1974         4776          0       0.296795             45.0    40.190948   \n", "1975         4789          0       0.298225             45.0    40.525169   \n", "\n", "                                                 stdout stderr profile  \n", "0     [process_file] Start processing s3a://augment-...                 \n", "1     [process_file] Start processing s3a://augment-...                 \n", "2     [process_file] Start processing s3a://augment-...                 \n", "3     [process_file] Start processing s3a://augment-...                 \n", "4     [process_file] Start processing s3a://augment-...                 \n", "...                                                 ...    ...     ...  \n", "1971  [process_file] Start processing s3a://augment-...                 \n", "1972  [process_file] Start processing s3a://augment-...                 \n", "1973  [process_file] Start processing s3a://augment-...                 \n", "1974  [process_file] Start processing s3a://augment-...                 \n", "1975  [process_file] Start processing s3a://augment-...                 \n", "\n", "[1976 rows x 12 columns]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["result['task_info']"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["print(result['task_info']['stderr'][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# My custom library.\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "# Imports\n", "import json\n", "import os\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from research.data.spark import k8s_session, get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "from research.core.types import EMPTY_CHUNK\n", "\n", "from research.retrieval.types import Chunk, Document\n", "from research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "from research.static_analysis.fim_prompt import _format_middle\n", "from research.static_analysis.fim_sampling import CSTFimSampler, FimProblem\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "from experimental.vzhao.data import common, constants, spark_stages\n", "from experimental.vzhao.data import pandas_functions as vz_pdfn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CONFIG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data paths.\n", "RAW_REPO_DATA = \"s3a://the-stack-processed/by-repo\"\n", "\n", "# S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/test\"\n", "S3ROOT = \"s3a://augment-temporary/vzhao/ppl_gain/1023_7b_6pos_32total_posppl_1.0\"\n", "VERSION_SUFFIX = \"_long_pre_filepath\"\n", "\n", "# Path to the final dataset for training.\n", "DATA_OUTPUT_PATH = \"/mnt/efs/augment/user/vincent/data/ppl_gain/1022_6pos_32total_posppl_1.0_long_pre_long_suf_filepath\"\n", "\n", "CONFIG = dict(\n", "    allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "    random_seed=74912,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CONFIG = dict(\n", "#     allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "#     random_seed=74912,\n", "#     # === Stage 1 ===\n", "#     stage_1=dict(\n", "#         # Sampling Repositories.\n", "#         repo_min_size=200000,\n", "#         repo_max_size=5000000,\n", "#         limit_repos=35000,\n", "#         downsample_small=True,\n", "#     ),\n", "#     # === Stage 2 ===\n", "#     stage_2=dict(\n", "#         # FIM sampler Configs.\n", "#         every_n_lines=150,\n", "#         max_problems_per_file=4,\n", "#         max_prefix_chars=8000,\n", "#         max_suffix_chars=8000,\n", "#         # Retrieval Configs.\n", "#         num_retrieved_chunks=127,\n", "#         retriever={\n", "#             \"name\": \"diff_boykin\",\n", "#             \"chunker\": {\n", "#                 \"name\": \"line_level\",\n", "#                 \"max_lines_per_chunk\": 40,\n", "#             },\n", "#             \"query_formatter\": {\n", "#                 \"name\": \"simple_query\",\n", "#                 \"max_lines\": 20,\n", "#             },\n", "#         },\n", "#     ),\n", "#     # === No Stage 3 ===\n", "#     # === Stage 4 ===\n", "#     stage_4=dict(\n", "#         num_rows=None,\n", "#         num_partitions=4096,\n", "#     ),\n", "#     # === Stage 5 ===\n", "#     stage_5=dict(\n", "#         language_model={\n", "#             \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "#             \"name\": \"rogue\",\n", "#             \"prompt\": {\n", "#                 \"max_prefix_tokens\": 100,\n", "#                 \"max_suffix_tokens\": 100,\n", "#                 \"max_retrieved_chunk_tokens\": -1,\n", "#                 \"max_prompt_tokens\": 750,\n", "#             },\n", "#         },\n", "#     ),\n", "#     # === Stage 6 ===\n", "#     stage_6=dict(\n", "#         min_pos_ppg=0.12,\n", "#         min_pos_ppl=-0.4,\n", "#         max_neg_ppg=0.03,\n", "#         num_positives=6,\n", "#         total_chunks=32,\n", "#     ),\n", "#     # === Stage 7 ===\n", "    # stage_7=dict(\n", "    #     encoder_seq_length=1024,\n", "    #     query_formatter={\n", "    #         \"name\": \"ethanol3_query\",\n", "    #         \"max_tokens\": 1024 - 1,\n", "    #         \"max_lines\": 20,\n", "    #         \"add_path\": True,\n", "    #         \"retokenize\": True,\n", "    #     },\n", "    #     key_formatter={\n", "    #         \"name\": \"simple_document\",\n", "    #         \"add_path\": True,\n", "    #         \"max_tokens\": 1024 - 1,\n", "    #     },\n", "    #     max_retrieved_docs=32 - 1,\n", "    # ),\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 1: <PERSON><PERSON> and filters <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE1_URI = os.path.join(S3ROOT, \"stage_1_repo\")\n", "print(STAGE1_URI)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage1():\n", "    spark = k8s_session(max_workers=64)\n", "\n", "    print(\"Processing retrieval samples\")\n", "    df = spark.read.parquet(RAW_REPO_DATA)\n", "\n", "    languages = CONFIG[\"allowed_languages\"]\n", "    if languages:\n", "        languages = [lang.lower() for lang in languages]\n", "        df = df.filter(\n", "            df[constants.REPO_LANG_COLUMN][constants.REPO_LANG_SUBCOL].isin(languages)\n", "        )\n", "\n", "    # if hasattr(config, \"retrieval_languages\"):\n", "    #     config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "    df = common.filter_by_repo_size(\n", "        df, min_size=CONFIG['stage_1'][\"repo_min_size\"], max_size=CONFIG['stage_1'][\"repo_max_size\"]\n", "    )\n", "\n", "    print(f\"Processing {df.count()} repos\", flush=True)\n", "    df = df.limit(CONFIG['stage_1'][\"limit_repos\"])\n", "\n", "    # About 100 to 200 repos per partition.\n", "    df = df.repartition(2000)\n", "    # Perform repo-specific processing\n", "    df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "    spark.stop()\n", "\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage1()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 2: Run Retrieval Augmentation\n", "\n", "This is one job per repo."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STAGE1_URI = os.path.join(\"s3a://augment-temporary/vzhao/ethanol_rag/test\", \"stage_1\")\n", "STAGE2_URI = os.path.join(S3ROOT, \"stage_2_retrieval\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame) -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "    # TODO(michiel) update for retriever query formatting options\n", "    retrieval_database = create_retriever(CONFIG['retriever'])\n", "\n", "    if CONFIG['retriever']['name'] != \"bm25\":\n", "        retrieval_database.scorer.load()\n", "\n", "    sampler = CSTFimSampler()\n", "    sampler.rng.seed(CONFIG['random_seed'])\n", "\n", "    tokenizer = StarCoderTokenizer()\n", "\n", "    for files in batch.file_list:\n", "        yield from common.process_repo(\n", "            files,\n", "            sampler=sampler,\n", "            retrieval_database=retrieval_database,\n", "            tokenizer=tokenizer,\n", "            allowed_languages=CONFIG['allowed_languages'],\n", "            downsample_small=CONFIG['downsample_small'],\n", "            every_n_lines=CONFIG['every_n_lines'],\n", "            max_problems_per_file=CONFIG['max_problems_per_file'],\n", "            random_seed=CONFIG['random_seed'],\n", "            max_prefix_chars=CONFIG['max_prefix_chars'],\n", "            max_suffix_chars=CONFIG['max_suffix_chars'],\n", "            num_retrieved_chunks=CONFIG['num_retrieved_chunks'],\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage2():\n", "    spark_conf = {\n", "        \"spark.executor.pyspark.memory\": \"50G\",\n", "        \"spark.executor.memory\": \"30G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "        \"spark.task.cpus\": \"5\",\n", "    }\n", "    spark = k8s_session(\n", "        max_workers=128,\n", "        conf=spark_conf,\n", "        gpu_type=\"RTX_A5000\",\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        process_partition_pandas,\n", "        input_path=STAGE1_URI,\n", "        output_path=STAGE2_URI,\n", "        timeout=3600,  # one hour timeout\n", "        batch_size=100,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage2()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE2_URI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 4: Reshuffle"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd ls s3://augment-temporary/vzhao/ppl_gain/"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')\n", "STAGE4_URI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.reshuffle(\n", "    STAGE2_URI,\n", "    STAGE4_URI,\n", "    num_rows=CONFIG['num_rows'],\n", "    num_partitions=CONFIG['num_partitions'],\n", "    columns=[\n", "        \"prefix\",\n", "        \"suffix\",\n", "        \"middle\",\n", "        \"file_path\",\n", "        \"retrieved_chunks\",\n", "    ],\n", "    max_workers=32,\n", "    override=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5a: Compute Perplexity\n", "\n", "Stage 5a works on computing PPL for all retrieved chunks from scratch."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CONFIG['language_model'] = {\n", "#     \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "#     \"name\": \"rogue\",\n", "#     \"prompt\": {\n", "#         \"max_prefix_tokens\": 100,\n", "#         \"max_suffix_tokens\": 100,\n", "#         \"max_retrieved_chunk_tokens\": -1,\n", "#         \"max_prompt_tokens\": 750,\n", "#     }\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')\n", "STAGE5_URI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from research.data.spark.pipelines.utils import map_parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def prepend_empty_chunk(retrieved_chunks) -> pd.Series:\n", "    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(\n", "        retrieved_chunks\n", "    )\n", "    return pd.Series(\n", "        {\"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks)}\n", "    )\n", "\n", "\n", "def compute_ppl(\n", "    prefix,\n", "    suffix,\n", "    middle,\n", "    file_path,\n", "    retrieved_chunks,\n", ") -> pd.Series:\n", "    def print_with_time(*args):\n", "        print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "    global cached_reranker\n", "    if \"cached_reranker\" not in globals():\n", "        # Construct a reranker\n", "        print_with_time(\"Constructing the model...\")\n", "        model = create_model(CONFIG[\"language_model\"])\n", "\n", "        print_with_time(\"Constructing the reranker...\")\n", "        cached_reranker = create_reranker(\n", "            model,\n", "            config={\n", "                \"name\": \"oracle_perplexity_reranker\",\n", "                \"top_k\": 256,\n", "                \"batchsize\": 4,\n", "            },\n", "        )\n", "\n", "        # Load the reranking model\n", "        print_with_time(\"Loading the model...\")\n", "        model.load()\n", "\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),\n", "        path=file_path,\n", "    )\n", "\n", "    print_with_time(f\"Reranking...\")\n", "    scores = cached_reranker._score(model_input, middle)\n", "    assert len(scores) == len(\n", "        common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ), f\"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}\"\n", "    return pd.Series({\"ppl\": json.dumps(scores)})\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def compute_ppl_gain(retrieved_chunks, ppl):\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    ppl = json.loads(ppl)\n", "\n", "    ppl_empty = ppl[0]\n", "    ppg = [s - ppl_empty for s in ppl]\n", "    retrieval_rank = list(range(len(retrieved_chunks)))\n", "    retrieval_rank, ppg, ppl, retrieved_chunks = list(\n", "        zip(\n", "            *sorted(\n", "                zip(retrieval_rank, ppg, ppl, retrieved_chunks),\n", "                key=lambda x: x[1],\n", "                reverse=True,\n", "            )\n", "        )\n", "    )\n", "    return pd.Series(\n", "        {\n", "            \"ppg\": json.dumps(ppg),\n", "            \"ppl\": json.dumps(ppl),\n", "            \"retrieved_chunks\": common.serialize_retrieved_chunks(retrieved_chunks),\n", "            \"retrieval_rank\": json.dumps(retrieval_rank),\n", "        }\n", "    )\n", "\n", "\n", "def stage5a():\n", "    spark_gpu = k8s_session(\n", "        max_workers=128,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"RTX_A5000\",\n", "    )\n", "    files = map_parquet.list_files(\n", "        spark_gpu, STAGE4_URI, suffix=\"parquet\", include_path=False\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                prepend_empty_chunk,\n", "                compute_ppl,\n", "                compute_ppl_gain,\n", "            ]\n", "        ),\n", "        # input_path=STAGE4_URI,\n", "        input_path=STAGE4_URI,\n", "        output_path=STAGE5_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5a()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5b: Add PPL of Empty Chunk.\n", "\n", "Prepend ppl of empty chunk to the existing data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sanity Check: Rescore all chunks and compare to the existing ppl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# IGOR_STAGE5_URI = \"s3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/\"\n", "# IGOR_STAGE5_URI = \"s3a://igor-dev-bucket/ethanol5/ethanol5-01/05_with_ppl_scores/\"\n", "IGOR_STAGE5_URI = \"s3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_7_rescore_7b/\"\n", "STAGE5_URI = os.path.join(S3ROOT, \"stage_5b_debug\")\n", "print(IGOR_STAGE5_URI)\n", "print(STAGE5_URI)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !s3cmd ls {STAGE5_URI.replace('s3a', 's3')}\n", "!s3cmd rm {STAGE5_URI.replace('s3a', 's3') + '/*'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from typing import Iterator\n", "from research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "from experimental.vzhao.data import pandas_functions as vz_pdfn\n", "\n", "\n", "def stage5b_debug():\n", "    spark_gpu = k8s_session(\n", "        name=\"vzhao-sanity\",\n", "        max_workers=16,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"80G\",\n", "            # \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        # gpu_type=\"A40\",\n", "        gpu_type=\"A100_NVLINK_80GB\",\n", "        gpu_count=1,\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                vz_pdfn.ComputeSequenceLength(),\n", "                vz_pdfn.ComputePPL(\n", "                    {\n", "                        \"checkpoint_path\": \"rogue/diffb1m_7b_alphal_fixtoken\",\n", "                        \"name\": \"rogue\",\n", "                        \"prompt\": {\n", "                            \"max_prefix_tokens\": 256,\n", "                            \"max_suffix_tokens\": 256,\n", "                            \"max_retrieved_chunk_tokens\": -1,\n", "                            \"max_prompt_tokens\": 3072,\n", "                        },\n", "                    },\n", "                    score_batch_size=2,\n", "                    output_col_name=\"ppl_1\",\n", "                ),\n", "                vz_pdfn.ComputePPL(\n", "                    {\n", "                        \"checkpoint_path\": \"rogue/diffb1m_7b_alphal_fixtoken\",\n", "                        \"name\": \"rogue\",\n", "                        \"prompt\": {\n", "                            \"max_prefix_tokens\": 1280,\n", "                            \"max_suffix_tokens\": 768,\n", "                            \"max_retrieved_chunk_tokens\": -1,\n", "                            \"max_prompt_tokens\": 3072,\n", "                        },\n", "                    },\n", "                    score_batch_size=2,\n", "                    output_col_name=\"ppl_2\",\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=IGOR_STAGE5_URI,\n", "        output_path=STAGE5_URI,\n", "        timing_run=True,\n", "        timeout=7200,\n", "        batch_size=4,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5b_debug()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import os\n", "from research.data.spark.pipelines.utils import map_parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CONFIG[\"stage_5\"] = dict(\n", "    language_model={\n", "        \"checkpoint_path\": \"rogue/diffb1m_7b_alphal_fixtoken\",\n", "        \"name\": \"rogue\",\n", "        \"prompt\": {\n", "            \"max_prefix_tokens\": 256,\n", "            \"max_suffix_tokens\": 256,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_prompt_tokens\": 3072,\n", "        },\n", "    },\n", ")\n", "\n", "\n", "# IGOR_STAGE5_URI = \"s3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/\"\n", "IGOR_STAGE5_URI = \"s3a://igor-dev-bucket/ethanol5/ethanol5-01/05_with_ppl_scores/\"\n", "STAGE5_URI = os.path.join(S3ROOT, \"stage_5_ppg\")\n", "print(IGOR_STAGE5_URI)\n", "print(STAGE5_URI)\n", "print(CONFIG[\"stage_5\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd ls {STAGE5_URI.replace('s3a', 's3')}\n", "# !s3cmd rm {STAGE5_URI.replace('s3a', 's3') + '/*'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterator\n", "from research.eval.harness.factories import create_reranker, create_model\n", "import datetime\n", "\n", "\n", "class PrependScoreEmptyChunk:\n", "    def __init__(self):\n", "        self._reranker = None\n", "\n", "    @map_parquet.allow_unused_args\n", "    def __call__(\n", "        self,\n", "        prefix,\n", "        suffix,\n", "        middle,\n", "        file_path,\n", "        retrieved_chunks,\n", "        ppl_scores,\n", "        retrieval_rank,\n", "    ) -> Iterator[pd.Series]:\n", "        def print_with_time(*args):\n", "            print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "        if self._reranker is None:\n", "            # Construct a reranker\n", "            print_with_time(\"Constructing the model...\")\n", "            model = create_model(CONFIG[\"stage_5\"][\"language_model\"])\n", "\n", "            print_with_time(\"Constructing the reranker...\")\n", "            self._reranker = create_reranker(\n", "                model,\n", "                config={\n", "                    \"name\": \"oracle_perplexity_reranker\",\n", "                    \"top_k\": 256,\n", "                    \"batchsize\": 2,\n", "                },\n", "            )\n", "            # Load the reranking model\n", "            print_with_time(\"Loading the model...\")\n", "            model.load()\n", "\n", "        model_input = ModelInput(\n", "            prefix=prefix,\n", "            suffix=suffix,\n", "            retrieved_chunks=[EMPTY_CHUNK],\n", "            path=file_path,\n", "        )\n", "        print_with_time(f\"Scoring...\")\n", "        \n", "        middel_len = len(self._reranker.model.tokenizer.tokenize(middle))\n", "        if middel_len > self._reranker.model.prompt_formatter.max_prompt_tokens:\n", "            # Target too long.\n", "            return\n", "        scores = self._reranker._score(model_input, middle)\n", "        assert len(scores) == 1, f\"`scores` should contain only 1 score.\"\n", "        yield pd.Series(\n", "            {\n", "                \"ppl\": json.dumps([scores[0]] + json.loads(ppl_scores)),\n", "                \"retrieved_chunks\": common.serialize_retrieved_chunks(\n", "                    [EMPTY_CHUNK]\n", "                    + common.deserialize_retrieved_chunks(retrieved_chunks)\n", "                ),\n", "                'retrieval_rank': json.dumps([-1] + json.loads(retrieval_rank))\n", "            }\n", "        )\n", "\n", "\n", "def stage5b():\n", "    spark_gpu = k8s_session(\n", "        name=\"vzhao-label-balance-stage5\",\n", "        max_workers=64,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"A40\",\n", "        gpu_count=1,\n", "    )\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                vz_pdfn.add_retriever_rank,\n", "                PrependScoreEmptyChunk(),\n", "                vz_pdfn.compute_ppl_gain,\n", "                vz_pdfn.sort_by_ppg,\n", "            ]\n", "        ),\n", "        input_path=IGOR_STAGE5_URI,\n", "        output_path=STAGE5_URI,\n", "        timeout=7200,\n", "        batch_size=512,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5b()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 6: Label Balancing\n", "If change parameters, regenerate data from here."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import pandas as pd\n", "import numpy as np\n", "from typing import Iterator\n", "\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "def create_balance_labels_fn(\n", "    min_pos_ppg: float,\n", "    min_pos_ppl: float,\n", "    max_neg_ppg: float,\n", "    num_positives: int,\n", "    total_chunks: int,\n", "):\n", "    \"\"\"Returns a row wise function for hard example mining and label balancing.\n", "    \n", "    Logics:\n", "      Take top `max_positives` chunks whose `ppg` > `min_pos_ppg`.\n", "      Randomly sample  `total_chunks` - `num_positives` from chunks whose `ppg` < `max_neg_ppg`.\n", "    \n", "    \"\"\"\n", "    @map_parquet.passthrough_feature\n", "    @map_parquet.allow_unused_args\n", "    def balance_labels(\n", "        retrieved_chunks, ppl, ppg, retrieval_rank\n", "    ) -> Iterator[pd.Series]:\n", "        \"\"\"Balance the labels of retrieved chunks.\"\"\"\n", "        df = pd.DataFrame(\n", "            {\n", "                \"retrieved_chunks\": common.maybe_deserialize_retrieved_chunks(\n", "                    retrieved_chunks\n", "                ),\n", "                \"ppl\": common.maybe_json_loads(ppl),\n", "                \"ppg\": common.maybe_json_loads(ppg),\n", "                \"retrieval_rank\": common.maybe_json_loads(retrieval_rank),\n", "            }\n", "        )\n", "        # Discard empty chunk.\n", "        df = df.loc[df['ppg']!=0, :]\n", "        # Takes top `max_positives` as positives.\n", "        candidates = df[(df['ppg'] > min_pos_ppg) & (df['ppl'] > min_pos_ppl)]\n", "        df_positives = candidates.sort_values('ppg', ascending=False).iloc[:num_positives, :]\n", "        if df_positives.empty:\n", "            return\n", "        assert df_positives.shape[0] <= num_positives, f'Bad number of positives: {df_positives.shape[0]}'\n", "\n", "        # Randomly select nagatives.\n", "        num_negatives = total_chunks - df_positives.shape[0]\n", "        df_negatives = df.loc[np.logical_and(df['ppg'] < max_neg_ppg, df['ppg'] != 0), :]\n", "        if df_negatives.shape[0] < num_negatives:\n", "            return\n", "        df_negatives = df_negatives.sample(n=num_negatives, random_state=0)\n", "        df_negatives = df_negatives.sample(num_negatives)\n", "        # All negatives should have ppg=0.\n", "        df_negatives[\"ppg\"] = 0\n", "\n", "        df_concat = pd.concat([df_positives, df_negatives], axis=0)\n", "        yield pd.Series(\n", "            {\n", "                \"retrieved_chunks\": common.serialize_retrieved_chunks(df_concat[\"retrieved_chunks\"].to_list()),\n", "                \"ppl\": json.dumps(df_concat[\"ppl\"].to_list()),\n", "                \"ppg\": json.dumps(df_concat[\"ppg\"].to_list()),\n", "                \"retrieval_rank\": json.dumps(df_concat[\"retrieval_rank\"].to_list()),\n", "            }\n", "        )\n", "\n", "    return balance_labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CONFIG[\"stage_6\"] = dict(\n", "    min_pos_ppg=0.12,\n", "    min_pos_ppl=-1.0,\n", "    max_neg_ppg=0.03,\n", "    num_positives=6,\n", "    total_chunks=32,\n", ")\n", "\n", "STAGE5_URI = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_5_ppg\"\n", "# STAGE6_URI = os.path.join(S3ROOT, f\"stage_6_label\")\n", "STAGE6_URI = os.path.join(S3ROOT, f\"test\")\n", "print(STAGE5_URI)\n", "print(STAGE6_URI)\n", "display(CONFIG[\"stage_6\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage6():\n", "    spark_gpu = k8s_session(max_workers=64)\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                create_balance_labels_fn(\n", "                    min_pos_ppg=CONFIG[\"stage_6\"][\"min_pos_ppg\"],\n", "                    min_pos_ppl=CONFIG[\"stage_6\"][\"min_pos_ppl\"],\n", "                    max_neg_ppg=CONFIG[\"stage_6\"][\"max_neg_ppg\"],\n", "                    num_positives=CONFIG[\"stage_6\"][\"num_positives\"],\n", "                    total_chunks=CONFIG[\"stage_6\"][\"total_chunks\"],\n", "                )\n", "            ]\n", "        ),\n", "        input_path=STAGE5_URI,\n", "        output_path=STAGE6_URI,\n", "        # timing_run=True,\n", "        # profile=True,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage6()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7: Token<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.retrieval import utils\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "import numpy as np\n", "\n", "\n", "def create_prompt_formatter(formatter_config):\n", "    from research.retrieval.prompt_formatters import SimpleQueryFormatter\n", "    import experimental.igor.systems.ethanol\n", "    cls_name, kwargs = utils.parse_yaml_config(formatter_config)\n", "    return get_prompt_formatter(cls_name, **kwargs)\n", "\n", "\n", "@map_parquet.allow_unused_args\n", "def pack_prompts(\n", "    prefix,\n", "    suffix,\n", "    file_path,\n", "    retrieved_chunks,\n", "    ppg,\n", ") -> pd.Series:\n", "    from research.retrieval import prompt_formatters\n", "\n", "    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)\n", "    # XXX temporary hack\n", "    retrieved_chunks = retrieved_chunks[: CONFIG['stage_7'][\"max_retrieved_docs\"]]\n", "    ppg_scores = json.loads(ppg)\n", "\n", "    global cached_query_prompt_formatter, cached_key_prompt_formatter\n", "    if \"cached_query_prompt_formatter\" not in globals():\n", "        cached_query_prompt_formatter = create_prompt_formatter(\n", "            CONFIG['stage_7'][\"query_formatter\"]\n", "        )\n", "    if \"cached_key_prompt_formatter\" not in globals():\n", "        cached_key_prompt_formatter = create_prompt_formatter(CONFIG['stage_7'][\"key_formatter\"])\n", "    # NOTE: The special query token is NOT added by  `cached_query_prompt_formatter`.\n", "    end_of_query_token = cached_query_prompt_formatter.tokenizer.vocab[\n", "        \"<|ret-endofquery|>\"\n", "    ]\n", "    query_prompt = cached_query_prompt_formatter.prepare_prompt(\n", "        ModelInput(prefix=prefix, path=file_path)\n", "    )\n", "    query_prompt.append(end_of_query_token)\n", "    if len(query_prompt) > CONFIG['stage_7'][\"encoder_seq_length\"]:\n", "        raise ValueError(\n", "            f\"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}\"\n", "        )\n", "\n", "    prompt_list = [query_prompt]\n", "\n", "    counter_prompts, counter_long_prompts = 0, 0\n", "    end_of_key_token = cached_key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "    pad_token = cached_key_prompt_formatter.tokenizer.pad_id\n", "    for ppg_score, chunk in zip(ppg_scores, retrieved_chunks):\n", "        counter_prompts += 1\n", "        # Encode the perplexity score into tokens.\n", "        ppg_tokens = cached_key_prompt_formatter.tokenizer.tokenize(f\"{ppg_score}\")\n", "        # Format the suffix of the prompt\n", "        suffix = [end_of_key_token] + ppg_tokens + [pad_token]\n", "\n", "        # Format the prompt\n", "        chunk_prompt = cached_key_prompt_formatter.prepare_prompt(\n", "            ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "        )\n", "        len_to_keep = CONFIG['stage_7'][\"encoder_seq_length\"] - len(suffix)\n", "        if len(chunk_prompt) > len_to_keep:\n", "            counter_long_prompts += 1\n", "        chunk_prompt = chunk_prompt[:len_to_keep]\n", "        chunk_prompt.extend(suffix)\n", "        prompt_list.append(chunk_prompt)\n", "\n", "    for id, chunk_prompt in enumerate(prompt_list):\n", "        if len(chunk_prompt) > CONFIG['stage_7'][\"encoder_seq_length\"]:\n", "            print(\"===================================================\")\n", "            print(cached_key_prompt_formatter.tokenizer.detokenize(chunk_prompt))\n", "            print(\"===================================================\")\n", "            raise ValueError(\n", "                f\"{id} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}\"\n", "            )\n", "\n", "    print(\"LONG PROMPTS\", counter_long_prompts, \"/\", counter_prompts)\n", "\n", "    # This is consumed by `common.unpack_tokens`.\n", "    all_tokens = [\n", "        spark_common.pack_tokens(\n", "            np.pad(prompt, (0, 1 + CONFIG['stage_7'][\"encoder_seq_length\"] - len(prompt)))\n", "        )\n", "        for prompt in prompt_list\n", "    ]\n", "    return pd.Series({\"prompt_tokens\": all_tokens})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.igor.systems import ethanol\n", "\n", "CONFIG[\"stage_7\"] = dict(\n", "    encoder_seq_length=1024,\n", "    query_formatter={\n", "        \"name\": \"ethanol_plus_query_1\",\n", "        \"max_tokens\": 1024 - 1,\n", "        \"max_lines\": -1,\n", "        \"add_path\": True,\n", "        'preamble': \"# Piece of code for code completion.\",\n", "        \"retokenize\": True,\n", "    },\n", "    key_formatter={\n", "        \"name\": \"ethanol_plus_doc_1\",\n", "        \"add_path\": True,\n", "        'preamble': \"# Code chunk for retrieval augmentation.\",\n", "        \"max_tokens\": 1024 - 1,\n", "    },\n", "    max_retrieved_docs=32 - 1,\n", ")\n", "STAGE6_URI = 's3a://augment-temporary/vzhao/ppl_gain/1022_6pos_32total_posppl_1.0/stage_6_label'\n", "STAGE7_URI = os.path.join(S3ROOT, f'stage_7_token{VERSION_SUFFIX}')\n", "print(STAGE6_URI)\n", "print(STAGE7_URI)\n", "display(CONFIG['stage_7'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage7():\n", "    spark = k8s_session(max_workers=96)\n", "\n", "    results = map_parquet.apply_pandas(\n", "        spark,\n", "        map_parquet.chain_processors(\n", "            [\n", "                pack_prompts,\n", "            ]\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        timeout=7200,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in results[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "\n", "\n", "stage7()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8: Explode"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pyspark.sql.functions as F\n", "import pyspark.sql.types as T\n", "\n", "def to_list(chunk_str: str) -> list[str]:\n", "    return [json.dumps(c) for c in json.loads(chunk_str)]\n", "\n", "ret_chunk_udf = F.udf(lambda x: to_list(x), T.ArrayType(T.StringType()))\n", "float_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.DoubleType()))\n", "int_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.IntegerType()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE8_URI = os.path.join(S3ROOT, f'stage_8_explode{VERSION_SUFFIX}')\n", "print(STAGE7_URI)\n", "print(STAGE8_URI)\n", "print(CONFIG['stage_7'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = k8s_session(max_workers=64)\n", "\n", "df = spark.read.parquet(STAGE7_URI)\n", "df = df.filter(F.size(F.col(\"prompt_tokens\")) == CONFIG['stage_7']['max_retrieved_docs'] + 1)\n", "df = df.withColumn(\"prompt_tokens\", <PERSON><PERSON>explode(\"prompt_tokens\"))\n", "\n", "df.write.parquet(STAGE8_URI)\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# spark = k8s_session(max_workers=128)\n", "\n", "# df = spark.read.parquet(STAGE7_URI)\n", "# df = df.filter(F.size(F.col(\"prompt_tokens\")) == CONFIG[\"max_retrieved_docs\"] + 1)\n", "\n", "# df = df.select(\n", "#     \"prefix\",\n", "#     \"suffix\",\n", "#     \"middle\",\n", "#     \"file_path\",\n", "#     F.explode(\n", "#         F.arrays_zip(\n", "#             ret_chunk_udf(<PERSON>.col(\"retrieved_chunks\")).alias(\"retrieved_chunks\"),\n", "#             float_array_udf(<PERSON>.col(\"ppl_scores\")).alias(\"ppl_scores\"),\n", "#             float_array_udf(<PERSON>.col(\"ppl\")).alias(\"ppl\"),\n", "#             float_array_udf(<PERSON>.col(\"ppg\")).alias(\"ppg\"),\n", "#             int_array_udf(<PERSON>.col(\"retrieval_rank\")).alias(\"retrieval_rank\"),\n", "#             \"prompt_tokens\",\n", "#         )\n", "#     ).alias(\"zipped\"),\n", "# ).select(\n", "#     \"prefix\",\n", "#     \"suffix\",\n", "#     \"middle\",\n", "#     \"file_path\",\n", "#     \"zipped.retrieved_chunks\",\n", "#     \"zipped.ppl_scores\",\n", "#     \"zipped.ppl\",\n", "#     \"zipped.ppg\",\n", "#     \"zipped.retrieval_rank\",\n", "#     \"zipped.prompt_tokens\",\n", "# )\n", "\n", "# df.write.parquet(STAGE8_URI)\n", "# spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# spark = k8s_session()\n", "\n", "# df=spark.read.parquet(STAGE6_URI)\n", "# print(df.filter(F.size(F.col(\"prompt_tokens\")) < max_retrieved_docs + 1).count())\n", "\n", "# spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 9: Convert to Dataset for Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages.common import export_indexed_dataset\n", "\n", "class ObjectDict(dict):\n", "    \"\"\"Provides both namespace-like and dict-like access to fields.\n", "\n", "    Allows access to fields using both obj.name notation and obj[\"name\"]\n", "    notation. The latter is useful when \"name\" contains periods, for example.\n", "    \"\"\"\n", "\n", "    def __getattr__(self, name: str):\n", "        if name in self:\n", "            return self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "    def __setattr__(self, name: str, value):\n", "        self[name] = value\n", "\n", "    def __delattr__(self, name: str):\n", "        if name in self:\n", "            del self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "print(STAGE8_URI)\n", "print(DATA_OUTPUT_PATH)\n", "print(CONFIG['stage_7'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE8_URI = 's3a://augment-temporary/vzhao/ppl_gain/1101_1b_128total_long_pre_filepath/stage_8_explode/'\n", "DATA_OUTPUT_PATH = '/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath'\n", "\n", "print(STAGE8_URI)\n", "print(DATA_OUTPUT_PATH)\n", "print(CONFIG['stage_7'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "if not os.path.exists(DATA_OUTPUT_PATH):\n", "    os.makedirs(DATA_OUTPUT_PATH)\n", "\n", "spark = k8s_session(64)\n", "\n", "export_indexed_dataset(\n", "    config=ObjectDict(\n", "        {\n", "            \"input\": STAGE8_URI,\n", "            \"output\": DATA_OUTPUT_PATH,\n", "            \"samples_column\": \"prompt_tokens\",\n", "            'num_validation_samples': 8192,\n", "        }\n", "    ),\n", "    spark=spark,\n", "    tokenizer=create_prompt_formatter(CONFIG['stage_7']['query_formatter']).tokenizer\n", ")\n", "\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# TO Kill a Running Spark Job\n", "\n", "```bash\n", "# Get appName\n", "kubectl get pods | grep vzhao\n", "\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark= SparkSession.builder.appName('vzhao-sanity-ea9cd08b72c21703').getOrCreate()\n", "spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
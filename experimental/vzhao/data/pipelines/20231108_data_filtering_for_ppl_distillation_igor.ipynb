{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# [Stage 5] Spark Stage to compute per-token loss."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["INPUT_PATH = 's3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/'\n", "OUTPUT_PATH = 's3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl'\n", "LANGUAGE_MODEL_CONFIG = {\n", "    \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "    \"name\": \"rogue\",\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 100,\n", "        \"max_suffix_tokens\": 100,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 750,\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING: Remote list is empty.\n"]}], "source": ["!s3cmd rm --recursive s3://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eval harness is not available (No module named 'lm_eval').\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/11/27 18:54:10 WARN Utils: Your hostname, vzhao-dev resolves to a loopback address: *********; using ************* instead (on interface enp6s0)\n", "23/11/27 18:54:10 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "23/11/27 18:54:43 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "WARNING:root:Doing a timing run.  Processing one batch per file (batchsize: 8) and a maximum of 5 files.\n", "23/11/27 18:56:49 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/4b992353-c4f1-4b60-b16d-1facf425f276/lib/python3.9/site-packages/research/models/meta_gpt_neox_model.py:369: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_batch_seqs, dtype=torch.int64)}]\n", "/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/4b992353-c4f1-4b60-b16d-1facf425f276/lib/python3.9/site-packages/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n", "\n"]}], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "def stage5a():\n", "    from experimental.vzhao.data import pandas_functions\n", "    spark_gpu = k8s_session(\n", "        max_workers=8,\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"50G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        gpu_type=\"A40\",\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                # NOTE: This assumes that the current `retrieved_chunks` are sorted in\n", "                # the order of retriever scores.\n", "                pandas_functions.add_retriever_rank,\n", "                # This will prepend `EMPTY_CHUNK` to `retrieved_chunks`.\n", "                pandas_functions.prepend_empty_chunk,\n", "                # NOTE: Set `reduction='none'` to return per-token losses/scores.\n", "                pandas_functions.ComputePPL(\n", "                    LANGUAGE_MODEL_CONFIG,\n", "                    score_batch_size=2,\n", "                    output_col_name=\"token_ppl\",\n", "                    reduction=\"none\",\n", "                ),\n", "            ]\n", "        ),\n", "        input_path=INPUT_PATH,\n", "        output_path=OUTPUT_PATH,\n", "        timing_run=True,\n", "        batch_size=8,\n", "        timeout=7200,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "\n", "result = stage5a()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect the data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/11/27 19:49:18 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prefix</th>\n", "      <th>suffix</th>\n", "      <th>middle</th>\n", "      <th>file_path</th>\n", "      <th>retrieved_chunks</th>\n", "      <th>retrieval_rank</th>\n", "      <th>token_ppl</th>\n", "      <th>token_ppl_prompt_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>(window[\"webpackJsonp\"] = window[\"webpackJsonp...</td>\n", "      <td>;\\n/* harmony import */ var _material_ui_icons...</td>\n", "      <td>_IMPORTED_MODULE_10__ = __webpack_require__(/*...</td>\n", "      <td>public/10.js</td>\n", "      <td>[{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...</td>\n", "      <td>[-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...</td>\n", "      <td>[[-0.0001436368766007945, -1.2397689715726301e...</td>\n", "      <td>[205, 204, 204, 676, 690, 481, 482, 433, 719, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>package seedu.address.logic.commands.csvcomman...</td>\n", "      <td>ring} and writes it into the CSV file provided...</td>\n", "      <td>ts each {@code Entity} in the given {@code ent...</td>\n", "      <td>src/main/java/seedu/address/logic/commands/csv...</td>\n", "      <td>[{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...</td>\n", "      <td>[-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...</td>\n", "      <td>[[-0.05634943023324013, -3.4683923721313477, -...</td>\n", "      <td>[205, 466, 415, 377, 383, 384, 446, 401, 301, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>package com.logviewer;\\n\\nimport com.logviewer...</td>\n", "      <td>;\\n\\n        // load records after found line ...</td>\n", "      <td>),\\n                res -&gt; assertEquals(4, res...</td>\n", "      <td>log-viewer/src/test/java/com/logviewer/LogSess...</td>\n", "      <td>[{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...</td>\n", "      <td>[-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...</td>\n", "      <td>[[-9.562783241271973, -0.3922908902168274, -0....</td>\n", "      <td>[205, 711, 204, 343, 630, 476, 659, 503, 500, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>from tonclient.module import TonModule\\nfrom t...</td>\n", "      <td>\\n\\n    @result_as(classname=ResultOfNaclBoxOp...</td>\n", "      <td>nacl_secret_box(\\n            self, params: Pa...</td>\n", "      <td>tonclient/crypto.py</td>\n", "      <td>[{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...</td>\n", "      <td>[-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...</td>\n", "      <td>[[-0.020330145955085754, -0.000124804340885020...</td>\n", "      <td>[205, 404, 423, 421, 405, 426, 442, 380, 403, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>package com.aviary.android.feather.widget;\\n\\n...</td>\n", "      <td>\\n\\n\\t/** The m child on checked change listen...</td>\n", "      <td>ton, mCenterButton, mLastButton;</td>\n", "      <td>submodules/Android-Feather/src/com/aviary/andr...</td>\n", "      <td>[{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...</td>\n", "      <td>[-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...</td>\n", "      <td>[[-0.0011566146276891232, -7.67267370223999, -...</td>\n", "      <td>[205, 373, 338, 361, 351, 393, 354, 422, 442, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prefix  \\\n", "0  (window[\"webpackJsonp\"] = window[\"webpackJsonp...   \n", "1  package seedu.address.logic.commands.csvcomman...   \n", "2  package com.logviewer;\\n\\nimport com.logviewer...   \n", "3  from tonclient.module import TonModule\\nfrom t...   \n", "4  package com.aviary.android.feather.widget;\\n\\n...   \n", "\n", "                                              suffix  \\\n", "0  ;\\n/* harmony import */ var _material_ui_icons...   \n", "1  ring} and writes it into the CSV file provided...   \n", "2  ;\\n\\n        // load records after found line ...   \n", "3  \\n\\n    @result_as(classname=ResultOfNaclBoxOp...   \n", "4  \\n\\n\\t/** The m child on checked change listen...   \n", "\n", "                                              middle  \\\n", "0  _IMPORTED_MODULE_10__ = __webpack_require__(/*...   \n", "1  ts each {@code Entity} in the given {@code ent...   \n", "2  ),\\n                res -> assertEquals(4, res...   \n", "3  nacl_secret_box(\\n            self, params: Pa...   \n", "4                   ton, mCenterButton, mLastButton;   \n", "\n", "                                           file_path  \\\n", "0                                       public/10.js   \n", "1  src/main/java/seedu/address/logic/commands/csv...   \n", "2  log-viewer/src/test/java/com/logviewer/LogSess...   \n", "3                                tonclient/crypto.py   \n", "4  submodules/Android-Feather/src/com/aviary/andr...   \n", "\n", "                                    retrieved_chunks  \\\n", "0  [{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...   \n", "1  [{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...   \n", "2  [{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...   \n", "3  [{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...   \n", "4  [{\"id\": \"\", \"text\": \"\", \"parent_doc\": {\"id\": \"...   \n", "\n", "                                      retrieval_rank  \\\n", "0  [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...   \n", "1  [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...   \n", "2  [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...   \n", "3  [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...   \n", "4  [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,...   \n", "\n", "                                           token_ppl  \\\n", "0  [[-0.0001436368766007945, -1.2397689715726301e...   \n", "1  [[-0.05634943023324013, -3.4683923721313477, -...   \n", "2  [[-9.562783241271973, -0.3922908902168274, -0....   \n", "3  [[-0.020330145955085754, -0.000124804340885020...   \n", "4  [[-0.0011566146276891232, -7.67267370223999, -...   \n", "\n", "                                token_ppl_prompt_len  \n", "0  [205, 204, 204, 676, 690, 481, 482, 433, 719, ...  \n", "1  [205, 466, 415, 377, 383, 384, 446, 401, 301, ...  \n", "2  [205, 711, 204, 343, 630, 476, 659, 503, 500, ...  \n", "3  [205, 404, 423, 421, 405, 426, 442, 380, 403, ...  \n", "4  [205, 373, 338, 361, 351, 393, 354, 422, 442, ...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sanity check the output parquet files.\n", "import json\n", "import os\n", "\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.spark import get_session\n", "\n", "OUTPUT_PATH = 's3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl'\n", "spark = get_session()\n", "files = [\n", "    os.path.join(OUTPUT_PATH, f)\n", "    for f in map_parquet.list_files(\n", "        spark, OUTPUT_PATH, suffix=\"parquet\", include_path=False\n", "    )\n", "]\n", "df = spark.read.parquet(files[0])\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "df.head()\n", "\n", "\n", "# scores = json.loads(df[\"ppl\"][0])\n", "# print(f\"Number of chunks: {len(scores)}\")\n", "# print(f\"#tokens: {[len(s) for s in scores]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Stage 6] Patch Level Filtering (Remove patches that don't have any positive chunks.)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["INPUT_PATH = 's3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl'\n", "OUTPUT_PATH = 's3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_6_filtering'\n", "\n", "PARAMS = {\n", "    'min_pos_ppg': 0.12,\n", "    'min_pos_ppl': -1.0,\n", "    'num_positives': 1,\n", "}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING: Remote list is empty.\n"]}], "source": ["!s3cmd rm --recursive s3://augment-temporary/vzhao/1109_per_token_loss_data/stage_6_filtering"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from typing import Iterator, Optional\n", "from experimental.vzhao.data import pandas_functions, common\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "@map_parquet.passthrough_feature()\n", "@map_parquet.allow_unused_args()\n", "def compute_sequence_ppl(token_ppl) -> pd.Series:\n", "    \"\"\"Computes sequence level log-likelihood.\"\"\"\n", "    # Shape: (num_chunks, target_sequence_length)\n", "    token_ppl = json.loads(token_ppl)\n", "    ppl = [np.mean(s) for s in token_ppl]\n", "    return pd.Series({\"ppl\": json.dumps(ppl)})\n", "\n", "\n", "def create_patch_filter(\n", "    min_pos_ppg: Optional[float],\n", "    min_pos_ppl: Optional[float],\n", "    num_positives: int,\n", "):\n", "    \"\"\"Returns a row wise function for hard example mining and label balancing.\n", "\n", "    The function implements the main logic for sampling training data for perplexity\n", "    distillation.\n", "\n", "    Main Logics:\n", "    For positive chunks, we require:\n", "      * `ppg` > `min_pos_ppg`: ppg is defined as $logPr(tgt|ctx, chunk) - logPr(tft|ctx)$.\n", "        With the retrieved chunk, the log likelihood of the target should be\n", "        signaficantly larger than that without retrieval.\n", "      * `ppl` > `min_pos_ppl`: With retrieval, the absolute value of log likelihood\n", "        should be reasonably big. Otherwise, `ppg` is noisy.\n", "\n", "    For negative chunks, we require:\n", "      * `ppg` < `max_neg_ppg`\n", "\n", "    If there are no positive chunks, drop the patch. There is no point to train on the\n", "    patch with no positive chunks. Similarly, if there are no enough negative chunks, we\n", "    also drop the patch. Training on all positives is not useful.\n", "    \"\"\"\n", "\n", "    @map_parquet.passthrough_feature()\n", "    @map_parquet.allow_unused_args()\n", "    def filter(retrieved_chunks, ppl, ppg, retrieval_rank) -> Iterator[pd.Series]:\n", "        \"\"\"This is a row-wise function to be applied to a DataFrame.\"\"\"\n", "        retrieved_chunks = common.maybe_deserialize_retrieved_chunks(retrieved_chunks)\n", "        ppl = common.maybe_json_loads(ppl)\n", "        ppg = common.maybe_json_loads(ppg)\n", "        retrieval_rank = common.maybe_json_loads(retrieval_rank)\n", "\n", "        if not retrieved_chunks or not (\n", "            len(retrieved_chunks) == len(ppl) == len(ppg) == len(retrieval_rank)\n", "        ):\n", "            # Discard the row.\n", "            return\n", "\n", "        df = pd.DataFrame(\n", "            {\n", "                \"cid\": [c.id for c in retrieved_chunks],\n", "                \"retrieved_chunks\": retrieved_chunks,\n", "                \"ppl\": ppl,\n", "                \"ppg\": ppg,\n", "                \"retrieval_rank\": retrieval_rank,\n", "            }\n", "        ).set_index(\"cid\")\n", "        # Drop Duplicated Chunks.\n", "        df = df[~df.index.duplicated(keep=\"first\")]\n", "\n", "        # Discard empty chunk. The empty chunk has ppg=0 and retrieval_rank =-1\n", "        df = df[df[\"retrieval_rank\"] >= 0]\n", "\n", "        # Positive chunks should have large ppg and large ppl.\n", "        candidates = df\n", "        if min_pos_ppg is not None:\n", "            candidates = candidates[candidates[\"ppg\"] > min_pos_ppg]\n", "        if min_pos_ppl is not None:\n", "            candidates = candidates[candidates[\"ppl\"] > min_pos_ppl]\n", "        df_positives = candidates.sort_values(\"ppg\", ascending=False)\n", "        if len(df_positives) < num_positives:\n", "            return\n", "\n", "        yield pd.Series({})\n", "\n", "    return filter"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/11/27 19:51:33 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "WARNING:root:Doing a timing run.  Processing one batch per file (batchsize: 8) and a maximum of 5 files.\n", "23/11/27 19:51:50 WARN TaskSetManager: Lost task 2.0 in stage 1.0 (TID 26) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:51:50 WARN TaskSetManager: Lost task 0.0 in stage 1.0 (TID 25) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:51:50 WARN TaskSetManager: Lost task 4.0 in stage 1.0 (TID 27) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:02 WARN TaskSetManager: Lost task 0.1 in stage 1.0 (TID 31) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:02 WARN TaskSetManager: Lost task 4.1 in stage 1.0 (TID 32) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:11 WARN TaskSetManager: Lost task 2.2 in stage 1.0 (TID 33) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:14 WARN TaskSetManager: Lost task 4.2 in stage 1.0 (TID 35) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-00999-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:14 WARN TaskSetManager: Lost task 0.2 in stage 1.0 (TID 34) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01002-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:23 WARN TaskSetManager: Lost task 2.3 in stage 1.0 (TID 36) (************* executor 3): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\n", "RuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n", "    result_list = list(result)\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n", "  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n", "    results = func(**feature)\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n", "    return func(**{key: kwargs[key] for key in func_args})\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n", "    ppl = json.loads(ppl)\n", "  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not list\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer$$anon$1.hasNext(InMemoryRelation.scala:118)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIterator(MemoryStore.scala:223)\n", "\tat org.apache.spark.storage.memory.MemoryStore.putIteratorAsValues(MemoryStore.scala:302)\n", "\tat org.apache.spark.storage.BlockManager.$anonfun$doPutIterator$1(BlockManager.scala:1535)\n", "\tat org.apache.spark.storage.BlockManager.org$apache$spark$storage$BlockManager$$doPut(BlockManager.scala:1462)\n", "\tat org.apache.spark.storage.BlockManager.doPutIterator(BlockManager.scala:1526)\n", "\tat org.apache.spark.storage.BlockManager.getOrElseUpdate(BlockManager.scala:1349)\n", "\tat org.apache.spark.rdd.RDD.getOrCompute(RDD.scala:375)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:326)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:364)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:328)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:92)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "23/11/27 19:52:23 ERROR TaskSetManager: Task 2 in stage 1.0 failed 4 times; aborting job\n", "[Stage 1:=======================>                                   (2 + 2) / 5]\r"]}, {"ename": "PythonException", "evalue": "\n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\nRuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\nTraceback (most recent call last):\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n    result_list = list(result)\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n    results = func(**feature)\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n    return func(**{key: kwargs[key] for key in func_args})\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n    ppl = json.loads(ppl)\n  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\nTypeError: the JSON object must be str, bytes or bytearray, not list\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n    out_batch = collect_result(\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n    raise TypeError(\"Return a dataframe, series or iterable\")\nTypeError: Return a dataframe, series or iterable\n\n\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPythonException\u001b[0m                           Traceback (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb Cell 11\u001b[0m line \u001b[0;36m2\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m         \u001b[39mbreak\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m result\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m result \u001b[39m=\u001b[39m stage_6()\n", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb Cell 11\u001b[0m line \u001b[0;36m7\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mexperimental\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mvzhao\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mdata\u001b[39;00m \u001b[39mimport\u001b[39;00m pandas_functions\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m spark_gpu \u001b[39m=\u001b[39m k8s_session(max_workers\u001b[39m=\u001b[39m\u001b[39m8\u001b[39m)\n\u001b[0;32m----> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m result \u001b[39m=\u001b[39m map_parquet\u001b[39m.\u001b[39;49mapply_pandas(\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m     spark_gpu,\n\u001b[1;32m      <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m     map_parquet\u001b[39m.\u001b[39;49mchain_processors(\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m         [\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m             compute_sequence_ppl,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m             pandas_functions\u001b[39m.\u001b[39;49mcompute_ppl_gain,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m             create_patch_filter(\u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mPARAMS),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m         ]\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m     ),\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m     input_path\u001b[39m=\u001b[39;49mINPUT_PATH,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m     output_path\u001b[39m=\u001b[39;49mOUTPUT_PATH,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=17'>18</a>\u001b[0m     timing_run\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m     batch_size\u001b[39m=\u001b[39;49m\u001b[39m8\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m     timeout\u001b[39m=\u001b[39;49m\u001b[39m7200\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m )\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m spark_gpu\u001b[39m.\u001b[39mstop()\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d767a68616f2d646576222c2273657474696e6773223a7b22686f7374223a227373683a2f2f767a68616f2d646576227d7d/home/<USER>/augment/experimental/vzhao/data/pipelines/20231108_data_filtering_for_ppl_distillation_igor.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m \u001b[39mfor\u001b[39;00m e \u001b[39min\u001b[39;00m result[\u001b[39m\"\u001b[39m\u001b[39mtask_info\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mstderr\u001b[39m\u001b[39m\"\u001b[39m]:\n", "File \u001b[0;32m~/augment/research/data/spark/pipelines/utils/map_parquet.py:700\u001b[0m, in \u001b[0;36mapply_pandas\u001b[0;34m(spark_session, pandas_func, input_path, output_path, region, endpoint_url, timeout, poll_time, batch_size, input_columns, timing_run, profile, drop_original_columns, output_column, ignore_error)\u001b[0m\n\u001b[1;32m    698\u001b[0m \u001b[39m# now filter out the failed files\u001b[39;00m\n\u001b[1;32m    699\u001b[0m task_info \u001b[39m=\u001b[39m df\u001b[39m.\u001b[39mselect(\u001b[39m\"\u001b[39m\u001b[39minput_path\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39moutput_path\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39minfo.*\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39mcache()\n\u001b[0;32m--> 700\u001b[0m info_pdf \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39;49mtoPandas()\n\u001b[1;32m    701\u001b[0m status_count \u001b[39m=\u001b[39m task_info\u001b[39m.\u001b[39mgroupBy(\u001b[39m\"\u001b[39m\u001b[39mstatus\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39magg(F\u001b[39m.\u001b[39mcount(\u001b[39m\"\u001b[39m\u001b[39m*\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39malias(\u001b[39m\"\u001b[39m\u001b[39mcount\u001b[39m\u001b[39m\"\u001b[39m))\n\u001b[1;32m    702\u001b[0m count_dict: \u001b[39mdict\u001b[39m[\u001b[39mstr\u001b[39m, \u001b[39mint\u001b[39m] \u001b[39m=\u001b[39m {\n\u001b[1;32m    703\u001b[0m     status: count\n\u001b[1;32m    704\u001b[0m     \u001b[39mfor\u001b[39;00m status, count \u001b[39min\u001b[39;00m status_count\u001b[39m.\u001b[39mselect(\u001b[39m\"\u001b[39m\u001b[39mstatus\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39mcount\u001b[39m\u001b[39m\"\u001b[39m)\u001b[39m.\u001b[39mcollect()\n\u001b[1;32m    705\u001b[0m }\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/pandas/conversion.py:208\u001b[0m, in \u001b[0;36mPandasConversionMixin.toPandas\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    205\u001b[0m             \u001b[39mraise\u001b[39;00m\n\u001b[1;32m    207\u001b[0m \u001b[39m# Below is to<PERSON><PERSON><PERSON> without Arrow optimization.\u001b[39;00m\n\u001b[0;32m--> 208\u001b[0m pdf \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mDataFrame\u001b[39m.\u001b[39mfrom_records(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcollect(), columns\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    209\u001b[0m column_counter \u001b[39m=\u001b[39m Counter(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns)\n\u001b[1;32m    211\u001b[0m corrected_dtypes: List[Optional[Type]] \u001b[39m=\u001b[39m [\u001b[39mNone\u001b[39;00m] \u001b[39m*\u001b[39m \u001b[39mlen\u001b[39m(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mschema)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/sql/dataframe.py:1216\u001b[0m, in \u001b[0;36mDataFrame.collect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1196\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"Returns all the records as a list of :class:`Row`.\u001b[39;00m\n\u001b[1;32m   1197\u001b[0m \n\u001b[1;32m   1198\u001b[0m \u001b[39m.. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1213\u001b[0m \u001b[39m[Row(age=14, name='<PERSON>'), <PERSON>(age=23, name='<PERSON>'), <PERSON>(age=16, name='<PERSON>')]\u001b[39;00m\n\u001b[1;32m   1214\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m   1215\u001b[0m \u001b[39mwith\u001b[39;00m SCCallSiteSync(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_sc):\n\u001b[0;32m-> 1216\u001b[0m     sock_info \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_jdf\u001b[39m.\u001b[39;49mcollectToPython()\n\u001b[1;32m   1217\u001b[0m \u001b[39mreturn\u001b[39;00m \u001b[39mlist\u001b[39m(_load_from_socket(sock_info, BatchedSerializer(CPickleSerializer())))\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[39m=\u001b[39m proto\u001b[39m.\u001b[39mCALL_COMMAND_NAME \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcommand_header \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[39m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[39m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mgateway_client\u001b[39m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[39m=\u001b[39m get_return_value(\n\u001b[1;32m   1323\u001b[0m     answer, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mgateway_client, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mtarget_id, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mname)\n\u001b[1;32m   1325\u001b[0m \u001b[39mfor\u001b[39;00m temp_arg \u001b[39min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mhasattr\u001b[39m(temp_arg, \u001b[39m\"\u001b[39m\u001b[39m_detach\u001b[39m\u001b[39m\"\u001b[39m):\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyspark/errors/exceptions/captured.py:175\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    171\u001b[0m converted \u001b[39m=\u001b[39m convert_exception(e\u001b[39m.\u001b[39mjava_exception)\n\u001b[1;32m    172\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39misinstance\u001b[39m(converted, UnknownException):\n\u001b[1;32m    173\u001b[0m     \u001b[39m# Hide where the exception came from that shows a non-Pythonic\u001b[39;00m\n\u001b[1;32m    174\u001b[0m     \u001b[39m# JVM exception message.\u001b[39;00m\n\u001b[0;32m--> 175\u001b[0m     \u001b[39mraise\u001b[39;00m converted \u001b[39mfrom\u001b[39;00m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m    176\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    177\u001b[0m     \u001b[39mraise\u001b[39;00m\n", "\u001b[0;31mPythonException\u001b[0m: \n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 540, in process_file_udf\nRuntimeError: Failed to process s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: [process_file] Error processing s3a://augment-temporary/vzhao/1109_per_token_loss_data/stage_5_ppl/part-01001-c0be70c1-8fc9-4def-97e8-4d36cbeb43aa-c000.zstd.parquet: Return a dataframe, series or iterable\nTraceback (most recent call last):\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 229, in collect_result\n    result_list = list(result)\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 966, in pandas_function\n  File \"/home/<USER>/augment/research/data/spark/pipelines/utils/map_parquet.py\", line 952, in chained\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 901, in wrapper\n    results = func(**feature)\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 1007, in wrapper\n    return func(**{key: kwargs[key] for key in func_args})\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/experimental/vzhao/data/pandas_functions.py\", line 159, in compute_ppl_gain\n    ppl = json.loads(ppl)\n  File \"/opt/conda/lib/python3.9/json/__init__.py\", line 339, in loads\n    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\nTypeError: the JSON object must be str, bytes or bytearray, not list\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 340, in process_file\n    out_batch = collect_result(\n  File \"/mnt/efs/augment/python_env/2023-11-27/vzhao-dev/9d8d9f71-ca76-4643-a010-74671b3f738a/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 232, in collect_result\n    raise TypeError(\"Return a dataframe, series or iterable\")\nTypeError: Return a dataframe, series or iterable\n\n\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/11/27 19:52:26 WARN TaskSetManager: Lost task 4.3 in stage 1.0 (TID 37) (************* executor 3): TaskKilled (Stage cancelled)\n", "23/11/27 19:52:26 WARN TaskSetManager: Lost task 0.3 in stage 1.0 (TID 38) (************* executor 3): TaskKilled (Stage cancelled)\n"]}], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "def stage_6():\n", "    from experimental.vzhao.data import pandas_functions\n", "    spark_gpu = k8s_session(max_workers=8)\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        map_parquet.chain_processors(\n", "            [\n", "                compute_sequence_ppl,\n", "                pandas_functions.compute_ppl_gain,\n", "                create_patch_filter(**PARAMS),\n", "            ]\n", "        ),\n", "        input_path=INPUT_PATH,\n", "        output_path=OUTPUT_PATH,\n", "        timing_run=True,\n", "        batch_size=8,\n", "        timeout=7200,\n", "    )\n", "    spark_gpu.stop()\n", "\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(e)\n", "        break\n", "    return result\n", "\n", "result = stage_6()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
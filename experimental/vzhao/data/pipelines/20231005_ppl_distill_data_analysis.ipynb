{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "\n", "from research.data.spark import get_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "from experimental.vzhao.notebooks import utils as vz_utils \n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer, CodeGenTokenizer\n", "\n", "from research.data.spark import get_session, k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from experimental.vzhao.data import common\n", "import os \n", "import json\n", "from torch.nn import functional as F\n", "import matplotlib.pyplot as plt\n", "\n", "from IPython.display import HTML, clear_output, display\n", "from experimental.vzhao.data import pandas_functions as vz_pdfn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = 's3a://augment-temporary/vzhao/ppl_gain/1030_1b_1pos_32total_narrow_neg/stage_6_label' \n", "!s3cmd ls {path.replace('s3a', 's3')}\n", "# !s3cmd rm {path.replace('s3a', 's3') + '/*'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session, k8s_session\n", "\n", "# spark = get_session()\n", "spark = k8s_session(max_workers=8)\n", "\n", "# path = 's3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/'\n", "\n", "\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_6/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_6_label/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_6_label/\"\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining'\n", "\n", "# files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "\n", "# path = 's3a://vzhao-dev-bucket/ppl_gain/1011_rouge1b_6pos_32total/stage_5_ppg/'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_5_ppg'\n", "\n", "# ===\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1022_1pos_16total_posppl_1.0_long_pre_filepath/stage_6_label'\n", "# path = 's3a://vzhao-dev-bucket/ppl_gain/1011_rouge1b_6pos_32total/stage_6_label/'\n", "\n", "# ===== 1b sample llm 7b rescore =====\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample_tiny'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_7_rescore_7b' # 7B rescored\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30/stage_6_label'  # 7B rescored + data filtering\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30_filter2/stage_6_label'  # 7B rescored + data filtering\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30_3p_16t_nofilter/stage_6_label'  # 7B rescored + data filtering\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30_nofilter/stage_6_label'  # 7B rescored + data filtering\n", "\n", "\n", "# ======1b sample llm 7b rescore V2 =====\n", "# path = 's3a://vzhao-dev-bucket/ppl_gain/1011_rouge1b_6pos_32total/stage_5_ppg/'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample_2'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1029_1b_1pos_32total/stage_6_label/'\n", "\n", "\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1030_1b_1pos_32total_narrow_neg/stage_6_label/'\n", "\n", "\n", "print(path, flush=True)\n", "df = spark.read.parquet(path)\n", "print(f'Examples: {df.count()}')\n", "spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inspect Dense Retriever Training Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "from experimental.vzhao.data import common\n", "\n", "def deserialize_to_list(row: pd.Series):\n", "    row['retrieved_chunks'] = common.deserialize_retrieved_chunks(row['retrieved_chunks'])\n", "    row['retrieval_rank'] = json.loads(row['retrieval_rank'])\n", "    row['ppl'] = json.loads(row['ppl'])\n", "    row['ppg'] = json.loads(row['ppg'])\n", "    return row"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.spark import get_session\n", "from experimental.vzhao.data import common\n", "\n", "spark = get_session()\n", "\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "\n", "# ====== IGOR data ======\n", "# path = \"s3a://igor-dev-bucket/perplexity_distill4.01/06_tokens/\"\n", "\n", "\n", "# V0.1 Data\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_6/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_7_token/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_6_label/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_6_label/\"\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard/stage_6_mining'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining'\n", "\n", "# ====== V0.1 data w/o path ======\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_7_token/\"\n", "\n", "# ====== V0.1??? data w/o path ======\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "\n", "\n", "# # ====== V0.1??? data with Path ======\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token_filepath/\"\n", "\n", "# ====== Compare my PPL to Igor's PPL\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1023_7b_6pos_32total_posppl_1.0/stage_5b_debug'\n", "\n", "# ===== 1b sample llm rescore =====\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample/' # before 7B rescored\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_7_rescore_7b'\n", "# path = 's3a://vzhao-dev-bucket/ppl_gain/7b_rescore_12-7-30/stage_5_rescore/' # 7B rescored\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30/stage_6_label'  # 7B rescored + data filtering\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/7b_12-7-30/stage_8_explode'  # 7B rescored + data filtering\n", "\n", "# ======1b sample llm 7b rescore V2 =====\n", "# path = 's3a://vzhao-dev-bucket/ppl_gain/1011_rouge1b_6pos_32total/stage_5_ppg/'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_6_downsample_2' # before 7B rescored\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1029_6pos_32total_dsam2/stage_6_label/'\n", "\n", "\n", "files = [os.path.join(path, f) for f in map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)]\n", "df = spark.read.parquet(*files[:2])\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "print(len(df))\n", "df = df.apply(deserialize_to_list,axis=1)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "ppgs = df[['ppg']].explode('ppg')\n", "sns.histplot(ppgs.sample(10000), binrange=[-0.2,0.2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def row_func(row):\n", "    # return sorted(row['ppg'])[:36][-1]\n", "    return sorted(row['ppg'])[-12:][-10]\n", "    # return len(row['ppg'])\n", "\n", "# number = (df.apply(row_func, axis=1) <= 0.00).sum()\n", "number = (df.apply(row_func, axis=1) >= 0.12).sum()\n", "print(number)\n", "print(number / len(df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cache = df[df.apply(row_func, axis=1) == 0.00]\n", "cache.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import torch\n", "row = 6\n", "ppg = json.loads(df['ppg'][16*row])\n", "print(ppg)\n", "print(torch.softmax(torch.tensor(ppg) * 100, dim=-1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Prefix, Middle, Suffix and Retrieved Chunk"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, clear_output, display\n", "import pandas as pd\n", "\n", "def print_row(row: pd.Series, prefix_lines=10, suffix_lines=10):\n", "    print(f'left file: {row[\"file_path\"]}')\n", "    print(f'right file: {row[\"retrieved_chunks\"].parent_doc.path}')\n", "\n", "\n", "    left =f\"\"\"PREFIX\n", "{vz_utils.last_n_lines(row['prefix'], prefix_lines)}\n", "\n", "MIDDLE\n", "{row['middle']}\n", "\n", "SUFFIX\n", "{vz_utils.first_n_lines(row['suffix'], suffix_lines)}\n", "\"\"\"\n", "    right = row[\"retrieved_chunks\"].text\n", "    # print(left)\n", "    left = left.replace('\\n', '<br>')\n", "    right = right.replace('\\n', '<br>')\n", "    \n", "    left = left.replace(' ', '&nbsp;')\n", "    right = right.replace(' ', '&nbsp;')\n", "\n", "\n", "\n", "    html_str = f\"\"\"\n", "<div style=\"display: flex;\">\n", "    <div style=\"font-family: 'Arial';flex: 50%; padding: 10px; border: 1px solid black;\">{left}</div>\n", "    <div style=\"font-family: 'Arial';flex: 50%; padding: 10px; border: 1px solid black;\">{right}</div>\n", "</div>\n", "\"\"\"\n", "    display(HTML(html_str))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cache.iloc[idx].name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["row = 0\n", "idx = row * 16 + 3\n", "\n", "cache = df[df.apply(row_func, axis=1) == 0.00].explode(['retrieved_chunks', 'retrieval_rank', 'ppl', 'ppg'])\n", "\n", "print(json.dumps(cache['ppg'][cache.iloc[idx].name].to_list()))\n", "print(cache['ppg'].iloc[idx])\n", "print(cache['ppl'].iloc[idx])\n", "print(cache['retrieval_rank'].iloc[idx])\n", "print_row(cache.iloc[idx], 20, 20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# List Rouge Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls /mnt/efs/augment/checkpoints/rogue"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare Perplexity Signal From Different Language Model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## List Annotated Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd ls s3://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> Annotated Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import get_session\n", "def load_parquet(path: str):\n", "    spark = get_session()\n", "    df = spark.read.parquet(path)\n", "    df = df.to<PERSON><PERSON><PERSON>()\n", "    spark.stop()\n", "    print(df.shape)\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load a collection of data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df['prefix'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_collections = {\n", "    # 'rouge_1b': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_rouge1b/',\n", "    'rouge_1b_128_128': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_128_128None/',\n", "    'rouge_1b_256_256': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_256_256None/',\n", "    'rouge_1b_512_512': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_512_512None/',\n", "    'rouge_1b_1024_1024': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_1024_1024None/',\n", "    # 'rouge_7b': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_rouge7b/',\n", "    'rouge_7b_128_128': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_7b_alphal_fixtoken_128_128None/',\n", "    'rouge_7b_256_256': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_7b_alphal_fixtoken_256_256None/',\n", "    'rouge_7b_512_512': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_7b_alphal_fixtoken_512_512None/',\n", "    'rouge_7b_1024_1024': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_7b_alphal_fixtoken_1024_1024None/',\n", "    '7b_diffb1m_chunk30_128_128': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_7b_diffb1m_chunk30_128_128None/',\n", "    '7b_diffb1m_chunk30_512_512': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_7b_diffb1m_chunk30_512_512None/',\n", "    'rouge_16b': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_rouge16b/',\n", "    # 'rouge_16b_512_512': 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppgrouge_16b_512_512/',\n", "}\n", "\n", "cache = {}\n", "for key, value in df_collections.items():\n", "    try:\n", "        cache[key] = load_parquet(value)\n", "    except:\n", "        pass\n", "df_collections = cache\n", "\n", "# Only keep patches in common.\n", "common_prefix = set(list(df_collections.values())[0][\"prefix\"])\n", "for df in df_collections.values():\n", "    common_prefix = common_prefix.intersection(set(df[\"prefix\"]))\n", "for key in df_collections:\n", "    df_collections[key] = (\n", "        df_collections[key]\n", "        .set_index(\"prefix\")\n", "        .loc[list(common_prefix), :]\n", "        .reset_index()\n", "    )\n", "\n", "print(df_collections.keys())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "all_ppgs = []\n", "for v in df_collections['rouge_1b_128_128']['ppg']:\n", "    all_ppgs.extend(json.loads(v))\n", "sns.histplot(all_ppgs, kde=False, stat='percent')\n", "plt.xlim([-0.25,0.25])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ppgs = np.array(all_ppgs)\n", "all_ppgs = all_ppgs[all_ppgs<0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.sqrt(np.mean(all_ppgs**2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "all_ppgs = []\n", "for v in df_collections['rouge_7b_256_256']['ppg']:\n", "    all_ppgs.extend(json.loads(v))\n", "sns.histplot(all_ppgs, kde=False, stat='percent')\n", "plt.xlim([-0.25,0.25])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compute Sequence Length"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_seq_len = df_collections['rouge_1b_128_128'][['prefix', 'suffix', 'middle']]\n", "tokenizer = StarCoderTokenizer()\n", "df_seq_len['prefix_len'] = df_seq_len.apply(lambda row: len(tokenizer.tokenize(row[\"prefix\"])), axis=1)\n", "df_seq_len['suffix_len'] = df_seq_len.apply(lambda row: len(tokenizer.tokenize(row[\"suffix\"])), axis=1)\n", "df_seq_len['middle_len'] = df_seq_len.apply(lambda row: len(tokenizer.tokenize(row[\"middle\"])), axis=1)\n", "df_seq_len = df_seq_len.set_index('prefix')\n", "print(df_seq_len.shape)\n", "df_seq_len.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Estimate Noise STD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def std_below_zero(ppg):\n", "    \"\"\"Only computes \"\"\"\n", "    ppg = np.array(ppg)\n", "    ppg = ppg[ppg<0]\n", "    if not ppg.size:\n", "        return 0\n", "    return np.sqrt(np.mean(ppg ** 2))\n", "\n", "\n", "def std_above_zero(ppg):\n", "    \"\"\"Only computes \"\"\"\n", "    ppg = np.array(ppg)\n", "    ppg = ppg[ppg>0]\n", "    if not ppg.size:\n", "        return 0\n", "    return np.sqrt(np.mean(ppg ** 2))\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["noise_std = {}\n", "\n", "for key, df in df_collections.items():\n", "    all_ppgs = []\n", "    for value in df['ppg']:\n", "        all_ppgs.extend(json.loads(value))\n", "    print('---')\n", "    noise_std[key] = std_below_zero(all_ppgs)\n", "    print(key)\n", "    print(f'std below zero: {std_below_zero(all_ppgs)}')\n", "    print(f'std above zero: {std_above_zero(all_ppgs)}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scatter Plot"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "\n", "\n", "def concat_all_dfs_per_patch(df_collections: dict, idx, temperature=1.0):\n", "    \"\"\"Returns a DataFrame w/ all\"\"\"\n", "    cache = []\n", "    for key, df in df_collections.items():\n", "        chunks = common.deserialize_retrieved_chunks(df[\"retrieved_chunks\"][idx])\n", "        ret_rank = json.loads(df[\"retrieval_rank\"][idx])\n", "        # ppg = torch.tensor(json.loads(df[\"ppg\"][idx])) * temperature\n", "        # prob = torch.softmax(ppg, dim=-1)\n", "        _df = (\n", "            pd.DataFrame(\n", "                {\n", "                    \"cid\": [c.id for c in chunks],\n", "                    \"ppg\": json.loads(df[\"ppg\"][idx]),\n", "                    # \"ppg\": ppg.tolist(),\n", "                    # \"prob\": prob.tolist(),\n", "                    # \"ppg_raw\": json.loads(df1b[\"ppg_raw\"][idx]),\n", "                    \"ppl\": [v for v in json.loads(df[\"ppl\"][idx])],\n", "                }\n", "            )\n", "            .set_index(\"cid\")\n", "            .add_prefix(f\"{key}_\")\n", "        )\n", "        cache.append(_df)\n", "    df_concat = pd.concat(cache, axis=1)\n", "    df_concat[\"prefix\"] = df[\"prefix\"][idx]\n", "    df_concat[\"prefix_len\"] = df_seq_len.loc[df[\"prefix\"][idx]][\"prefix_len\"]\n", "    df_concat[\"suffix\"] = df[\"suffix\"][idx]\n", "    df_concat[\"suffix_len\"] = df_seq_len.loc[df[\"prefix\"][idx]][\"suffix_len\"]\n", "    df_concat[\"ctx_len\"] = (\n", "        df_seq_len.loc[df[\"prefix\"][idx]][\"prefix_len\"]\n", "        + df_seq_len.loc[df[\"prefix\"][idx]][\"suffix_len\"]\n", "    )\n", "    df_concat[\"middle\"] = df[\"middle\"][idx]\n", "    df_concat[\"middle_len\"] = df_seq_len.loc[df[\"prefix\"][idx]][\"middle_len\"]\n", "    df_concat[\"file_path\"] = df[\"file_path\"][idx]\n", "    df_concat[\"lang\"] = guess_lang_from_fp(df[\"file_path\"][idx])\n", "    df_concat[\"chunk\"] = chunks\n", "    df_concat[\"ret_rank\"] = ret_rank\n", "\n", "    # Remove data when the chunk.parent_doc.path == file_path.\n", "    rows_to_drop = []\n", "    for idx, row in df_concat.iterrows():\n", "        if row[\"file_path\"] == row[\"chunk\"].parent_doc.path:\n", "            rows_to_drop.append(idx)\n", "    df_concat = df_concat.drop(rows_to_drop)\n", "\n", "    return df_concat\n", "\n", "\n", "def compute_confusion_matrix(df_concat, col1, col2):\n", "    foo = (\n", "        (df_concat[[col1, col2]] > 0)\n", "        .reset_index()\n", "        .groupby([col1, col2])\n", "        .size()\n", "        .unstack(fill_value=0)\n", "    )\n", "    foo.index = pd.MultiIndex.from_product([[foo.index.name], foo.index.values])\n", "    foo.columns = pd.MultiIndex.from_product(\n", "        [[foo.columns.names[0]], foo.columns.values]\n", "    )\n", "    return foo\n", "\n", "\n", "def compute_confusion_matrix_dist_mean(df_concat, col1, col2, mean_type=\"rms\"):\n", "    \"\"\"Confusion matrix with value being the mean value.\"\"\"\n", "    foo = df_concat[[col1, col2]]\n", "    if mean_type == \"rms\":\n", "        # Option 1: root mean square\n", "        foo = foo.assign(distance=np.sqrt(np.square(foo[col1]) + np.square(foo[col2])))\n", "    elif mean_type == \"geometric\":\n", "        # Option 2: geometric mean\n", "        foo = foo.assign(distance=np.sqrt(np.abs(foo[col1]) * np.abs(foo[col2])))\n", "    else:\n", "        raise ValueError()\n", "\n", "    foo.loc[:, col1] = foo[col1] > 0\n", "    foo.loc[:, col2] = foo[col2] > 0\n", "    foo = foo.groupby([col1, col2]).median().unstack(fill_value=0)\n", "    foo.index = pd.MultiIndex.from_product([[foo.index.name], foo.index.values])\n", "    foo.columns = pd.MultiIndex.from_product(\n", "        [[foo.columns.names[1]], [v[1] for v in foo.columns.values]]\n", "    )\n", "    return foo\n", "\n", "\n", "def compute_confusion_matrix_dist_max(df_concat, col1, col2, mean_type=\"rms\"):\n", "    \"\"\"Confusion matrix with value being the mean value.\"\"\"\n", "    foo = df_concat[[col1, col2]]\n", "    if mean_type == \"rms\":\n", "        # Option 1: root mean square\n", "        foo = foo.assign(distance=np.sqrt(np.square(foo[col1]) + np.square(foo[col2])))\n", "    elif mean_type == \"geometric\":\n", "        # Option 2: geometric mean\n", "        foo = foo.assign(distance=np.sqrt(np.abs(foo[col1]) * np.abs(foo[col2])))\n", "    else:\n", "        raise ValueError()\n", "\n", "    foo.loc[:, col1] = foo[col1] > 0\n", "    foo.loc[:, col2] = foo[col2] > 0\n", "    foo = foo.groupby([col1, col2]).max().unstack(fill_value=0)\n", "    foo.index = pd.MultiIndex.from_product([[foo.index.name], foo.index.values])\n", "    foo.columns = pd.MultiIndex.from_product(\n", "        [[foo.columns.names[1]], [v[1] for v in foo.columns.values]]\n", "    )\n", "    return foo\n", "\n", "\n", "def show_scatter_plot(\n", "    x_col: str,\n", "    y_col: str,\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    square_plot=False,\n", "    joint_lim=1.0,\n", "    center=[0, 0],\n", "    len_norm=False,\n", "):\n", "    print(\"Correlation\")\n", "    print(\n", "        np.dot(df[x_col].values, df[y_col].values)\n", "        / np.sqrt(np.dot(df[x_col].values, df[x_col].values))\n", "        / np.sqrt(np.dot(df[y_col].values, df[y_col].values))\n", "    )\n", "    foo = compute_confusion_matrix(df, x_col, y_col)\n", "    foo = foo / foo.values.sum()\n", "    foo = foo.sort_index(ascending=False)\n", "    ratio = (foo.iloc[:, 1] / foo.iloc[:, 0]).to_list()\n", "    foo['ratio'] = ratio\n", "    foo['ratio2'] = [1.0 / r for r in ratio]\n", "    display(foo)\n", "\n", "    print(\"mean\")\n", "    foo = compute_confusion_matrix_dist_mean(df, x_col, y_col)\n", "    display(foo.sort_index(ascending=False))\n", "\n", "    print(\"max\")\n", "    foo = compute_confusion_matrix_dist_max(df, x_col, y_col)\n", "    display(foo.sort_index(ascending=False))\n", "\n", "    if len_norm:\n", "        plt.scatter(\n", "            df[x_col] / df[\"middle_len\"],\n", "            df[y_col] / df[\"middle_len\"],\n", "            s=1,\n", "            c=df[\"ret_rank\"],\n", "        )\n", "    else:\n", "        plt.scatter(df[x_col], df[y_col], s=1, c=df[\"ret_rank\"])\n", "    plt.plot([-joint_lim + center[0], joint_lim + center[0]], [center[1], center[1]])\n", "    plt.plot([center[0], center[0]], [-joint_lim + center[1], joint_lim + center[1]])\n", "    plt.xlim([-joint_lim + center[0], joint_lim + center[0]])\n", "    plt.ylim([-joint_lim + center[1], joint_lim + center[1]])\n", "    if square_plot:\n", "        plt.axis(\"square\")\n", "    plt.xlabel(x_col)\n", "    plt.ylabel(y_col)\n", "    plt.colorbar()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Concat DFs for all patches."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "all_concat_dfs = [\n", "    concat_all_dfs_per_patch(df_collections, idx, temperature=1.0)\n", "    for idx in range(list(df_collections.values())[0].shape[0])\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON> <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)\n", "df_concat = concat_all_dfs_per_patch(df_collections, 0)\n", "print(df_concat.shape)\n", "df_concat.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import io\n", "\n", "\n", "# x_data = 'rouge_1b_ppg'\n", "x_model = 'rouge_7b_ppg'\n", "y_model = 'rouge_16b_ppg'\n", "\n", "plt.scatter(df_concat[x_model], df_concat[y_model], s=3, c=df_concat['ret_rank'])\n", "x_lim = max(0.1, max(abs(max(df_concat[x_model])), abs(min(df_concat[x_model])))) + 0.05\n", "y_lim = max(0.1, max(abs(max(df_concat[y_model])), abs(min(df_concat[y_model])))) + 0.05\n", "joint_lim = max(x_lim, y_lim)\n", "joint_lim = 1.0\n", "plt.plot([-joint_lim,joint_lim], [0,0])\n", "plt.plot([0,0],[-joint_lim, joint_lim])\n", "plt.xlim([-joint_lim, joint_lim])\n", "plt.ylim([-joint_lim, joint_lim])\n", "plt.colorbar()\n", "\n", "print(df_concat['rouge_1b_ppl'].max())\n", "print(df_concat['rouge_7b_ppl'].max())\n", "print(df_concat['rouge_16b_ppl'].max())\n", "\n", "buf = io.Bytes<PERSON>()\n", "plt.savefig(buf, format='png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["noise_std"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import io\n", "import glob\n", "import contextlib\n", "from PIL import Image, ImageFile\n", "\n", "# buf = io.<PERSON><PERSON>()\n", "# images = []\n", "\n", "for idx in range(16):\n", "    df_concat = concat_all_dfs_per_patch(df_collections, idx)\n", "    \n", "    # # (Optional) Normalize the score.\n", "    # factor = 0.5\n", "    # for col, nstd in zip(['rouge_1b_ppg', 'rouge_7b_ppg', 'rouge_16b_ppg'], noise_std):\n", "    #     df_concat.loc[(df_concat[col] < factor * nstd) & (df_concat[col] > 0), col] = 0\n", "    \n", "    ######### Plot\n", "    print(idx)\n", "    print(f'Rouge 1B ground truth ppl: {df_concat[\"rouge_1b_ppl\"].max()}')\n", "    print(f'Rouge 7B ground truth ppl: {df_concat[\"rouge_7b_ppl\"].max()}')\n", "    print(f'Rouge 16B ground truth ppl: {df_concat[\"rouge_16b_ppl\"].max()}')\n", "\n", "    foo = compute_confusion_matrix(df_concat, 'rouge_1b_ppg', 'rouge_16b_ppg')\n", "    display(foo)\n", "\n", "    foo = compute_confusion_matrix(df_concat, 'rouge_7b_ppg', 'rouge_16b_ppg')\n", "    display(foo)\n", "\n", "    plt.figure(figsize=(16,4))\n", "    plt.subplot(1,3,1)\n", "    x_model = 'rouge_1b_ppg'\n", "    y_model = 'rouge_7b_ppg'\n", "    plt.scatter(df_concat[x_model], df_concat[y_model], s=3)\n", "    x_lim = max(0.1, max(abs(max(df_concat[x_model])), abs(min(df_concat[x_model])))) + 0.05\n", "    y_lim = max(0.1, max(abs(max(df_concat[y_model])), abs(min(df_concat[y_model])))) + 0.05\n", "    joint_lim = max(x_lim, y_lim)\n", "    joint_lim = 1.0\n", "    plt.plot([-joint_lim,joint_lim], [0,0])\n", "    plt.plot([0,0],[-joint_lim, joint_lim])\n", "    plt.xlim([-joint_lim, joint_lim])\n", "    plt.ylim([-joint_lim, joint_lim])\n", "    plt.xlabel(x_model)\n", "    plt.ylabel(y_model)\n", "\n", "    plt.subplot(1,3,2)\n", "    x_model = 'rouge_1b_ppg'\n", "    y_model = 'rouge_16b_ppg'\n", "    plt.scatter(df_concat[x_model], df_concat[y_model], s=3)\n", "    x_lim = max(0.1, max(abs(max(df_concat[x_model])), abs(min(df_concat[x_model])))) + 0.05\n", "    y_lim = max(0.1, max(abs(max(df_concat[y_model])), abs(min(df_concat[y_model])))) + 0.05\n", "    joint_lim = max(x_lim, y_lim)\n", "    joint_lim = 1.0\n", "    plt.plot([-joint_lim,joint_lim], [0,0])\n", "    plt.plot([0,0],[-joint_lim, joint_lim])\n", "    plt.xlim([-joint_lim, joint_lim])\n", "    plt.ylim([-joint_lim, joint_lim])\n", "    plt.xlabel(x_model)\n", "    plt.ylabel(y_model)\n", "\n", "    plt.subplot(1,3,3)\n", "    x_model = 'rouge_7b_ppg'\n", "    y_model = 'rouge_16b_ppg'\n", "    plt.scatter(df_concat[x_model], df_concat[y_model], s=3)\n", "    x_lim = max(0.1, max(abs(max(df_concat[x_model])), abs(min(df_concat[x_model])))) + 0.05\n", "    y_lim = max(0.1, max(abs(max(df_concat[y_model])), abs(min(df_concat[y_model])))) + 0.05\n", "    joint_lim = max(x_lim, y_lim)\n", "    joint_lim = 1.0\n", "    plt.plot([-joint_lim,joint_lim], [0,0])\n", "    plt.plot([0,0],[-joint_lim, joint_lim])\n", "    plt.xlim([-joint_lim, joint_lim])\n", "    plt.ylim([-joint_lim, joint_lim])\n", "    plt.xlabel(x_model)\n", "    plt.ylabel(y_model)\n", "    \n", "    plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Aggregated Scatter Plot "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Define Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import functools\n", "import tqdm\n", "\n", "\n", "@functools.lru_cache(maxsize=10)\n", "def compare_two_models(\n", "    left: str,\n", "    right: str,\n", "    min_pos_ppg=0.08,\n", "    npos=1,\n", "    nneg=16,\n", "):\n", "    \"\"\"Compare two models on a given signal.\"\"\"\n", "    dfs = []\n", "    for df_concat in tqdm.tqdm(all_concat_dfs):\n", "        # # [Positive Patches] Take top-k patches for each model.\n", "        # row_ids_to_keep = set()\n", "        # for model in df_collections:\n", "        #     pos_cand = df_concat[df_concat[f\"{left}_ppg\"] > min_pos_ppg].sort_values(\n", "        #         f\"{model}_ppg\", ascending=False\n", "        #     )\n", "        #     row_ids_to_keep.update(pos_cand.iloc[:npos].index)\n", "        # # [Negative Patch<PERSON>]\n", "        # for model in df_collections:\n", "        #     neg_cand = df_concat[df_concat[f\"{left}_ppg\"] < 0.08]\n", "        #     neg_cand = neg_cand.sample(frac=1)\n", "        #     row_ids_to_keep.update(neg_cand.iloc[:nneg].index)\n", "        # df_concat = df_concat.loc[list(row_ids_to_keep)]\n", "\n", "        # (Optional) Normalize the score. Apply a margin.\n", "        # factor = 0.5\n", "        # for key in df_collections:\n", "        #     ppg_col = f\"{key}_ppg\"\n", "        #     nstd = noise_std[key]\n", "        #     df_concat.loc[\n", "        #         (df_concat[ppg_col] < factor * nstd) & (df_concat[ppg_col] > 0), ppg_col\n", "        #     ] = 0\n", "        #     df_concat.loc[\n", "        #         (df_concat[ppg_col] > - factor * nstd) & (df_concat[ppg_col] < 0), ppg_col\n", "        #     ] = 0\n", "\n", "        dfs.append(\n", "            df_concat[\n", "                [\n", "                    f\"{left}_ppg\",\n", "                    f\"{left}_ppl\",\n", "                    f\"{right}_ppg\",\n", "                    f\"{right}_ppl\",\n", "                    \"prefix\",\n", "                    \"prefix_len\",\n", "                    \"suffix\",\n", "                    \"suffix_len\",\n", "                    \"ctx_len\",\n", "                    \"middle\",\n", "                    \"middle_len\",\n", "                    \"file_path\",\n", "                    \"lang\",\n", "                    \"chunk\",\n", "                    \"ret_rank\",\n", "                ]\n", "            ]\n", "        )\n", "    return pd.concat(dfs, axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "print(df_collections.keys())\n", "display(noise_std)\n", "df = []\n", "for idx in tqdm.tqdm(range(list(df_collections.values())[0].shape[0])):\n", "    df_concat = concat_all_dfs_per_patch(df_collections, idx, temperature=5.0)\n", "\n", "    # ===== Patch-Level Filtering Logic=====\n", "    # if df_concat[\"rouge_1b_1024_1024_ppl\"].max() > -0.5:\n", "    #     continue\n", "    # [Positive Patches] Take top-k patches for each model.\n", "    row_ids_to_keep = set()\n", "    for model in df_collections:\n", "        pos_cand = df_concat[df_concat[f\"{model}_ppg\"] > 0.08].sort_values(\n", "            f\"{model}_ppg\", ascending=False\n", "        )\n", "        row_ids_to_keep.update(pos_cand.iloc[:1].index)\n", "    # [Negative Patch<PERSON>]\n", "    for model in df_collections:\n", "        neg_cand = df_concat[df_concat[f\"{model}_ppg\"] < 0.08]\n", "        neg_cand = neg_cand.sample(frac=1)\n", "        row_ids_to_keep.update(neg_cand.iloc[:16].index)\n", "    df_concat = df_concat.loc[list(row_ids_to_keep)]\n", "\n", "    # (Optional) Normalize the score. Apply a margin.\n", "    # factor = 0.5\n", "    # for key in df_collections:\n", "    #     ppg_col = f\"{key}_ppg\"\n", "    #     nstd = noise_std[key]\n", "    #     df_concat.loc[\n", "    #         (df_concat[ppg_col] < factor * nstd) & (df_concat[ppg_col] > 0), ppg_col\n", "    #     ] = 0\n", "    #     df_concat.loc[\n", "    #         (df_concat[ppg_col] > - factor * nstd) & (df_concat[ppg_col] < 0), ppg_col\n", "    #     ] = 0\n", "\n", "    df.append(df_concat)\n", "\n", "cache_raw = pd.concat(df, axis=0)\n", "print(cache_raw.shape)\n", "display(cache_raw.head(1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    'rouge_1b_128_128',\n", "    'rouge_1b_256_256',\n", "    'rouge_1b_512_512',\n", "    'rouge_1b_512_512',\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x_col = 'rouge_1b_128_128_ppg'\n", "# y_col = 'rouge_7b_128_128_ppg'\n", "\n", "# x_col = 'rouge_1b_256_256_ppg'\n", "# y_col = 'rouge_7b_256_256_ppg'\n", "\n", "# x_col = 'rouge_1b_512_512_ppg'\n", "# y_col = 'rouge_7b_512_512_ppg'\n", "\n", "# x_model = \"rouge_1b_1024_1024\"\n", "# y_model = \"rouge_7b_1024_1024\"\n", "\n", "x_model = 'rouge_1b_128_128'\n", "y_model = 'rouge_16b_512_512'\n", "\n", "cache = compare_two_models(\n", "    x_model,\n", "    y_model,\n", "    min_pos_ppg=0.2,\n", "    npos=1,\n", "    nneg=16,\n", ")\n", "\n", "total_num_chunks = cache.shape[0]\n", "cache = cache.loc[cache[f\"{x_model}_ppl\"] > -1.0, :]\n", "cache = cache.loc[cache[f\"{x_model}_ppg\"] > 0.2, :]\n", "\n", "\n", "# cache = cache.loc[cache['rouge_7b_1024_1024_ppl'] > -1.0, :]\n", "# cache = cache.loc[(cache[\"ctx_len\"] > 1024) & (cache[\"ctx_len\"] < 10000000), :]\n", "# cache = cache.loc[(cache[\"middle_len\"] > 0) & (cache[\"middle_len\"] < 8), :]\n", "# cache = cache.loc[(cache[\"middle_len\"] > 128) & (cache[\"middle_len\"] < 512), :]\n", "\n", "# delta = 256\n", "# i = 10\n", "# cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "# cache = cache.loc[cache[\"ctx_len\"] < (i + 1) * delta, :]\n", "\n", "print(f'total chunks: {total_num_chunks}')\n", "print(f'filtered chunks: {cache.shape} {cache.shape[0] / total_num_chunks:.2%}')\n", "show_scatter_plot(\n", "    f\"{x_model}_ppg\",\n", "    f\"{y_model}_ppg\",\n", "    cache,\n", "    square_plot=False,\n", "    joint_lim=2.0,\n", "    len_norm=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_model = 'rouge_1b_128_128_ppg'\n", "# y_col = 'rouge_1b_256_256_ppg'\n", "\n", "# x_col = 'rouge_1b_256_256_ppg'\n", "# y_col = 'rouge_1b_512_512_ppg'\n", "\n", "# x_col = 'rouge_1b_512_512_ppg'\n", "y_model = 'rouge_1b_1024_1024_ppg'\n", "\n", "\n", "cache = cache_raw\n", "# cache = cache.loc[cache['rouge_1b_256_256_ppl'] > -1.0, :]\n", "# cache = cache.loc[cache['rouge_1b_512_512_ppl'] > -1.0, :]\n", "delta = 256\n", "i = 11\n", "cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "cache = cache.loc[cache[\"ctx_len\"] < (i+1) * delta, :]\n", "\n", "\n", "print(cache.shape)\n", "show_scatter_plot(x_model, y_model, cache, square_plot=False, joint_lim=1.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_model = 'rouge_7b_128_128_ppg'\n", "# y_col = 'rouge_7b_256_256_ppg'\n", "\n", "# x_col = 'rouge_7b_256_256_ppg'\n", "# y_col = 'rouge_7b_512_512_ppg'\n", "\n", "# x_col = \"rouge_7b_512_512_ppg\"\n", "# y_col = \"rouge_7b_1024_1024_ppg\"\n", "\n", "cache = cache_raw\n", "# cache = cache.loc[cache['rouge_1b_1024_1024_ppl'] > -1.0, :]\n", "# cache = cache.loc[cache['rouge_7b_1024_1024_ppl'] > -1.0, :]\n", "\n", "delta = 256\n", "i = 11\n", "cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "cache = cache.loc[cache[\"ctx_len\"] < (i+1) * delta, :]\n", "\n", "print(cache.shape)\n", "show_scatter_plot(x_model, y_model, cache, square_plot=False, joint_lim=1.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x_col = 'rouge_7b_512_512_ppg'\n", "# y_col = '7b_diffb1m_chunk30_512_512_ppg'\n", "\n", "# x_col = 'rouge_7b_128_128_ppg'\n", "# y_col = '7b_diffb1m_chunk30_128_128_ppg'\n", "\n", "# x_col = 'rouge_1b_512_512_ppg'\n", "# y_col = 'rouge_7b_512_512_ppg'\n", "\n", "# x_model = 'rouge_1b_128_128_ppg'\n", "# y_model = 'rouge_16b_512_512_ppg'\n", "\n", "# x_col = 'rouge_1b_512_512_ppg'\n", "# y_col = 'rouge_16b_512_512_ppg'\n", "\n", "# x_col = 'rouge_1b_1024_1024_ppg'\n", "# y_col = 'rouge_16b_512_512_ppg'\n", "\n", "# x_col = 'rouge_7b_128_128_ppg'\n", "# y_col = 'rouge_16b_512_512_ppg'\n", "\n", "# x_col = 'rouge_7b_256_256_ppg'\n", "# y_col = 'rouge_16b_512_512_ppg'\n", "\n", "# x_col = '7b_diffb1m_chunk30_128_128_ppg'\n", "# y_col = 'rouge_16b_512_512_ppg'\n", "\n", "x_model = 'rouge_7b_512_512'\n", "y_model = 'rouge_16b_512_512'\n", "\n", "\n", "cache = compare_two_models(\n", "    x_model,\n", "    y_model,\n", "    min_pos_ppg=0.2,\n", "    npos=1,\n", "    nneg=16,\n", ")\n", "\n", "total_num_chunks = cache.shape[0]\n", "\n", "# cache = cache.loc[cache['rouge_16b_512_512_ppg'] > 0.15, :]\n", "# cache = cache.loc[cache['rouge_7b_256_256_ppl'] > -1.0, :]\n", "# cache = cache.loc[cache['rouge_7b_128_128_ppl'] > -1.0, :]\n", "\n", "# delta = 512\n", "# i = 0\n", "# cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "# cache = cache.loc[cache[\"ctx_len\"] < (i+1) * delta, :]\n", "\n", "# cache = cache.loc[cache[\"lang\"]=='python', :]\n", "\n", "print(f'total chunks: {total_num_chunks}')\n", "print(f'filtered chunks: {cache.shape} {cache.shape[0] / total_num_chunks:.2%}')\n", "show_scatter_plot(\n", "    f\"{x_model}_ppg\",\n", "    f\"{y_model}_ppg\",\n", "    cache,\n", "    square_plot=False,\n", "    joint_lim=2.0,\n", "    len_norm=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x_model = 'rouge_7b_512_512'\n", "# y_model = '7b_diffb1m_chunk30_512_512'\n", "\n", "# x_model = 'rouge_7b_128_128_ppg'\n", "# y_model = '7b_diffb1m_chunk30_128_128_ppg'\n", "\n", "x_model = 'rouge_7b_512_512'\n", "y_model = 'rouge_16b_512_512'\n", "\n", "# x_model = 'rouge_7b_128_128_ppg'\n", "# y_model = 'rouge_16b_ppg'\n", "\n", "\n", "# x_model = 'rouge_1b_128_128_ppg'\n", "# y_model = 'rouge_16b_ppg'\n", "\n", "\n", "cache = compare_two_models(\n", "    x_model,\n", "    y_model,\n", "    min_pos_ppg=0.2,\n", "    npos=1,\n", "    nneg=16,\n", ")\n", "\n", "total_num_chunks = cache.shape[0]\n", "cache = cache.loc[cache[f\"{x_model}_ppl\"] < -2.0, :]\n", "# cache = cache.loc[cache[f\"{x_model}_ppg\"] < 0.2, :]\n", "# cache = cache.loc[cache[f\"{y_model}_ppg\"] < 0.2, :]\n", "\n", "# delta = 256\n", "# i = 10\n", "# cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "# cache = cache.loc[cache[\"ctx_len\"] < (i+1) * delta, :]\n", "\n", "print(f'total chunks: {total_num_chunks}')\n", "print(f'filtered chunks: {cache.shape} {cache.shape[0] / total_num_chunks:.2%}')\n", "show_scatter_plot(\n", "    f\"{x_model}_ppg\",\n", "    f\"{y_model}_ppg\",\n", "    cache,\n", "    square_plot=False,\n", "    joint_lim=2.0,\n", "    len_norm=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Patch Statistics\n", "\n", "Run `Aggregated Scatter Plot` to get `cache_raw` DataFrame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sns.histplot(cache_raw['ctx_len'])\n", "# plt.xlim([0, 10000])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure()\n", "ctx_len = [128, 256, 512, 1024]\n", "delta = 256\n", "x = np.array(range(20))\n", "for x_model, ycol in [\n", "    (f\"rouge_1b_{l}_{l}_ppg\", f\"rouge_7b_{l}_{l}_ppg\") for l in ctx_len\n", "]:\n", "    stats = []\n", "    for i in x:\n", "        cache = cache_raw\n", "        cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "        cache = cache.loc[cache[\"ctx_len\"] < (i + 1) * delta, :]\n", "        matrix = compute_confusion_matrix_dist_mean(\n", "            cache, x_model, y_model, mean_type=\"geometric\"\n", "        )\n", "        stats.append(matrix.iloc[1, 1])\n", "        # break\n", "\n", "    plt.plot((x + 1) * delta, stats)\n", "\n", "plt.legend(ctx_len)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure()\n", "ctx_len = [128, 256, 512, 1024]\n", "delta = 256\n", "x = np.array(range(20))\n", "# for x_col, ycol in [\n", "#     (f\"rouge_1b_{l}_{l}_ppg\", f\"rouge_7b_{l}_{l}_ppg\") for l in ctx_len\n", "# ]:\n", "for x_model, ycol in [\n", "    (\"rouge_1b_128_128_ppg\", 'rouge_16b_ppg'),\n", "    (\"rouge_1b_512_512_ppg\", 'rouge_16b_512_512_ppg'),\n", "]:\n", "    stats = []\n", "    for i in x:\n", "        cache = cache_raw\n", "        cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "        cache = cache.loc[cache[\"ctx_len\"] < (i + 1) * delta, :]\n", "        matrix = compute_confusion_matrix_dist_mean(\n", "            cache, x_model, y_model, mean_type=\"rms\"\n", "        )\n", "        stats.append(matrix.iloc[1, 1])\n", "        # stats.append(matrix.iloc[1, 1] + matrix.iloc[0, 0] + matrix.iloc[1, 0] + matrix.iloc[0, 1])\n", "        # stats.append(matrix.iloc[0, 0])\n", "        # break\n", "\n", "    plt.plot((x + 1) * delta, stats)\n", "\n", "plt.legend(ctx_len)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure()\n", "\n", "x_model = 'rouge_7b_512_512_ppg'\n", "y_model = 'rouge_16b_512_512_ppg'\n", "\n", "stats = [[],[],[]]\n", "for i in x:\n", "    cache = cache_raw\n", "    cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "    cache = cache.loc[cache[\"ctx_len\"] < (i + 1) * delta, :]\n", "    matrix = compute_confusion_matrix_dist_mean(\n", "        cache, x_model, y_model, mean_type=\"rms\"\n", "    )\n", "    stats[0].append(matrix.iloc[1, 1])\n", "    stats[1].append(matrix.iloc[0, 1])\n", "    stats[2].append(matrix.iloc[1, 0])\n", "    # break\n", "\n", "plt.plot((x + 1) * delta, np.transpose(stats))\n", "\n", "# plt.legend(ctx_len)\n", "plt.legend([(True, True), (False, True), (True, False)])\n", "plt.ylim([0,1.0])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Confusion Matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = []\n", "for idx in range(df1b.shape[0]):\n", "    df_concat = concat_all_dfs_per_patch(df1b, df7b, df16b, idx)\n", "\n", "    df_concat = df_concat.loc[df_concat['rouge_16b_ppl'] > -0.5, :]\n", "    # # (Optional) Normalize the score.\n", "    # factor = 1.0\n", "    # for col, nstd in zip(['rouge_1b_ppg', 'rouge_7b_ppg', 'rouge_16b_ppg'], noise_std):\n", "    #     df_concat.loc[(df_concat[col] < factor * nstd) & (df_concat[col] > 0), col] = 0\n", "\n", "    df.append(df_concat)\n", "\n", "df = pd.concat(df, axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_df = \"rouge_1b_ppg\"\n", "y_df = \"rouge_16b_ppg\"\n", "\n", "df = cache_raw\n", "df = df.loc[df['rouge_16b_ppl'] > -0.1, :]\n", "print(df.shape)\n", "\n", "foo = compute_confusion_matrix(df, x_df, y_df)\n", "foo = foo / foo.values.sum()\n", "foo.sort_index(ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_df = \"rouge_7b_ppg\"\n", "y_df = \"rouge_16b_ppg\"\n", "\n", "df = cache_raw\n", "# cache = cache.loc[cache['rouge_16b_ppl'] > -1, :]\n", "print(df.shape)\n", "\n", "foo = compute_confusion_matrix(df, x_df, y_df)\n", "foo = foo / foo.values.sum()\n", "foo.sort_index(ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DataFrame View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "x_model = 'rouge_7b_512_512'\n", "y_model = 'rouge_16b_512_512'\n", "\n", "cache = compare_two_models(\n", "    x_model,\n", "    y_model,\n", "    min_pos_ppg=0.2,\n", "    npos=1,\n", "    nneg=16,\n", ")\n", "\n", "total_num_chunks = cache.shape[0]\n", "cache = cache.loc[cache[f\"{x_model}_ppl\"] < -1.0, :]\n", "cache = cache.loc[cache[f\"{x_model}_ppg\"] > 0.5, :]\n", "cache = cache.loc[cache[f\"{y_model}_ppg\"] < 0.5, :]\n", "\n", "\n", "# delta = 512\n", "# i = 0\n", "# cache = cache.loc[cache[\"ctx_len\"] > i * delta, :]\n", "# cache = cache.loc[cache[\"ctx_len\"] < (i+1) * delta, :]\n", "\n", "cache = cache.loc[(cache[\"ret_rank\"] > 10), :]\n", "\n", "# cache = cache.loc[cache[\"lang\"]=='python', :]\n", "\n", "cache = cache.sample(frac=1)\n", "print(cache.shape)\n", "print(collections.Counter(cache['lang']))\n", "# cache"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualize Prefix, Middle, Suffix and Retrieved Chunk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, clear_output, display\n", "import pandas as pd\n", "\n", "def print_row(row: pd.Series, prefix_lines=10, suffix_lines=10):\n", "    print(f'left file: {row[\"file_path\"]}')\n", "    print(f'right file: {row[\"chunk\"].parent_doc.path}')\n", "\n", "\n", "    left =f\"\"\"PREFIX\n", "{vz_utils.last_n_lines(row['prefix'], prefix_lines)}\n", "\n", "MIDDLE\n", "{row['middle']}\n", "\n", "SUFFIX\n", "{vz_utils.first_n_lines(row['suffix'], suffix_lines)}\n", "\"\"\"\n", "    right = row[\"chunk\"].text\n", "    # print(left)\n", "    left = left.replace('\\n', '<br>')\n", "    right = right.replace('\\n', '<br>')\n", "    \n", "    left = left.replace(' ', '&nbsp;')\n", "    right = right.replace(' ', '&nbsp;')\n", "\n", "\n", "\n", "    html_str = f\"\"\"\n", "<div style=\"display: flex;\">\n", "    <div style=\"font-family: 'Arial';flex: 50%; padding: 10px; border: 1px solid black;\">{left}</div>\n", "    <div style=\"font-family: 'Arial';flex: 50%; padding: 10px; border: 1px solid black;\">{right}</div>\n", "</div>\n", "\"\"\"\n", "    display(HTML(html_str))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["idx = 0\n", "print(cache.iloc[idx][f'{x_model}_ppg'])\n", "print(cache.iloc[idx][f'{y_model}_ppg'])\n", "print(cache.iloc[idx][f'{x_model}_ppl'])\n", "print(cache.iloc[idx][f'{y_model}_ppl'])\n", "print(cache.iloc[idx]['ret_rank'])\n", "print_row(cache.iloc[idx], 20, 20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Logits Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.utils.visualize_logits import LogitsVisualizer\n", "from research.eval.harness import factories\n", "from research.core.model_input import ModelInputt ModelInput\n", "from research.core.types import EMPTY_CHUNK\n", "import torch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "        prefix=df[\"prefix\"][idx],\n", "        suffix=df[\"suffix\"][idx],\n", "        retrieved_chunks=[df[\"chunk\"][idx]],\n", "        # retrieved_chunks=[EMPTY_CHUNK],\n", "        path=df[\"file_path\"][idx],\n", "    )\n", "prompt_toks, _ = model.prompt_formatter.prepare_prompt(model_input)\n", "tgt_toks = model.prompt_formatter.tokenizer.tokenize(df[\"middle\"][idx])\n", "concat_toks = prompt_toks + tgt_toks\n", "prompt = model.prompt_formatter.tokenizer.detokenize(concat_toks)\n", "toks, logits = LogitsVisualizer().visualize_model_prediction(model, prompt)\n", "print(f'loss: {model.log_likelihood_continuation([model_input], [df[\"middle\"][idx]])}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["toks = torch.tensor(toks)\n", "for idx, t in enumerate(toks):\n", "    if t == 2:\n", "        break\n", "print(idx)\n", "print(toks[idx])\n", "print(logits[idx-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.unload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = factories.create_model(\n", "    dict(\n", "        name=\"rogue\",\n", "        checkpoint_path=\"/mnt/efs/augment/checkpoints/rogue/diffb1m_16b_alphal_fixtoken\",\n", "        prompt=dict(\n", "            max_prefix_tokens=100,\n", "            max_prompt_tokens=750,\n", "            max_suffix_tokens=100,\n", "            max_retrieved_chunk_tokens=-1,\n", "            # NOTE: Change this to control the max number of retrieved chunks in the prompt.\n", "            max_number_chunks=32,\n", "        ),\n", "    )\n", ")\n", "model.load()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Filtering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(all_concat_dfs))\n", "all_concat_dfs[0].head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Patch Level Filtering \n", "A lot of patches doesn't contain positive data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Use `rouge_1b_128_128` to remove non-interesting patches.\n", "This removed 61% of patches."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rst = set()\n", "for df in all_concat_dfs:\n", "    if df[\"rouge_1b_128_128_ppg\"].max() < 0.15 or df[\"rouge_1b_128_128_ppl\"].max() < -1:\n", "        rst.add(df[\"prefix\"][0])\n", "print(len(rst))\n", "print(len(all_concat_dfs))\n", "print(len(rst) / len(all_concat_dfs))\n", "\n", "rst1 = rst"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rst = set()\n", "for df in all_concat_dfs:\n", "    if df['rouge_16b_512_512_ppg'].max() < 0.15 or df['rouge_16b_512_512_ppl'].max() < -1:\n", "        rst.add(df['prefix'][0])\n", "print(len(rst))\n", "print(len(rst) / len(all_concat_dfs))\n", "\n", "rst2 = rst"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(rst1.intersection(rst2)) / len(rst1))\n", "print(len(rst1.intersection(rst2)) / len(rst2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chunk Level Filtering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_model = 'rouge_1b_128_128'\n", "y_model = 'rouge_16b_512_512'\n", "\n", "# After Patches level Filtering, keep 27%\n", "\n", "filtered_concat_dfs = []\n", "for df in all_concat_dfs:\n", "    if df[f\"{x_model}_ppg\"].max() < 0.12 or df[f\"{x_model}_ppl\"].max() < -1:\n", "        continue\n", "    filtered_concat_dfs.append(df)\n", "print(len(filtered_concat_dfs))\n", "print(len(filtered_concat_dfs) / len(all_concat_dfs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Positive Criteria\n", "What we want:\n", "Take top8 positives from `rouge_16b_512_512_ppg`.\n", "\n", "`rouge_16b_512_512_ppg` > 0.1\n", "\n", "`rouge_16b_512_512_ppl` > -1.0\n", "\n", "What we can do:\n", "Sort by `rouge_1b_128_128_ppg`.\n", "Take top 20 from `rouge_1b_128_128_ppg` and score by `rouge_16b`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Groundtruth from `rouge_16b_512_512_ppg`.\n", "all_y_pos_df = []\n", "npatches = 0\n", "for df in filtered_concat_dfs:\n", "    cache = df.sort_values(f'{x_model}_ppg', ascending=False)\n", "    cache['rouge_1b_rank'] = list(range(cache.shape[0]))\n", "    # Only keep top 8 positives by rouge_16b_512_512.\n", "    cache = cache[(cache[f'{y_model}_ppg'] > 0.1) & (cache[f'{y_model}_ppl'] > -1.0)]\n", "    cache = cache.sort_values(f'{y_model}_ppg', ascending=False).iloc[:8]\n", "    if not cache.empty:\n", "        all_y_pos_df.append(cache)\n", "        npatches += 1\n", "\n", "print(f'selected patches {npatches}: {npatches / len(filtered_concat_dfs)}')\n", "all_y_pos_df = pd.concat(all_y_pos_df, axis=0)\n", "print(all_y_pos_df.shape)\n", "\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.histplot(all_y_pos_df['rouge_1b_rank'], kde=True, stat='percent', cumulative=True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_pos_ppg = 0.12\n", "min_pos_ppl = -1.0\n", "num_positives = 20\n", "final_pos_df = []\n", "npatches = 0\n", "for df in filtered_concat_dfs:\n", "    # Get positives.\n", "    candidates = df[\n", "        (df[f\"{x_model}_ppg\"] > min_pos_ppg) & (df[f\"{x_model}_ppl\"] > min_pos_ppl)\n", "    ]\n", "    candidates = candidates.sort_values(f\"{x_model}_ppg\", ascending=False).iloc[\n", "        :num_positives, :\n", "    ]\n", "    if not candidates.empty:\n", "        npatches += 1\n", "        final_pos_df.append(candidates)\n", "\n", "final_pos_df = pd.concat(final_pos_df, axis=0)\n", "print(f\"selected patches {npatches}: {npatches / len(filtered_concat_dfs)}\")\n", "print(f\"all positives: {len(final_pos_df)}\")\n", "print(f\"positives per patch: {len(final_pos_df) / npatches:.2f}\")\n", "print(f\"pos to score ratio: {len(final_pos_df) / (len(all_concat_dfs) * len(all_concat_dfs[0])): .2%}\")\n", "\n", "\n", "print(x_model)\n", "print(y_model)\n", "show_scatter_plot(\n", "    f\"{x_model}_ppg\",\n", "    f\"{y_model}_ppg\",\n", "    final_pos_df,\n", "    square_plot=False,\n", "    joint_lim=2.0,\n", "    len_norm=False,\n", ")\n", "\n", "print(\n", "    f\"recall of {y_model} positives: {len(set(all_y_pos_df.index).intersection(final_pos_df.index)) / all_y_pos_df.index.size}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Negative Criteria\n", "What we want:\n", "Negatives are\n", "\n", "`rouge_16b_512_512_ppg` < 0.05\n", "\n", "In addition, hard negatives are\n", "`ret_rank` > 16\n", "\n", "What we can do:\n", "Find `rouge_1b_128_128_ppg` < 0.05\n", "* Randomly sample x from the set.\n", "* Sort by `ret_rank` and take top k\n", "* re-score by `rouge_16b`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Groundtruth from `rouge_16b_512_512_ppg`.\n", "\n", "max_neg_ppg = 0.05\n", "\n", "all_y_neg_df = []\n", "npatches = 0\n", "for df in filtered_concat_dfs:\n", "    cache = df[df[f'{y_model}_ppg'] < max_neg_ppg]\n", "    # Only keep top 8 positives by rouge_16b_512_512.\n", "    if not cache.empty:\n", "        all_y_neg_df.append(cache)\n", "        npatches += 1\n", "\n", "print(f'selected patches {npatches}: {npatches / len(filtered_concat_dfs)}')\n", "all_y_neg_df = pd.concat(all_y_neg_df, axis=0)\n", "print(f'all negs: {all_y_neg_df.shape}')\n", "all_y_hard_neg_df = all_y_neg_df[all_y_neg_df[\"ret_rank\"] < 10]\n", "print(f'all hard negs: {all_y_hard_neg_df.shape}')\n", "\n", "# print(x_model)\n", "# print(y_model)\n", "# show_scatter_plot(\n", "#     f\"{x_model}_ppg\",\n", "#     f\"{y_model}_ppg\",\n", "#     all_y_neg_df,\n", "#     square_plot=False,\n", "#     joint_lim=2.0,\n", "#     len_norm=False,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample Negatives using `rouge_1b_256_256`\n", "\n", "max_neg_ppg = 0.03\n", "hard_neg_max_rank = 16\n", "num_negs = 32\n", "final_neg_df = []\n", "npatches = 0\n", "for df in filtered_concat_dfs:\n", "    # Get negatives.\n", "    candidates = df[df[f\"{x_model}_ppg\"] < max_neg_ppg]\n", "    hard_neg = candidates[candidates['ret_rank'] < hard_neg_max_rank]\n", "    rand_neg = candidates.sample(min(num_negs - hard_neg.shape[0], candidates.shape[0]))\n", "    if not hard_neg.empty or not rand_neg.empty:\n", "        npatches += 1\n", "        final_neg_df.extend([hard_neg, rand_neg])\n", "\n", "final_neg_df = pd.concat(final_neg_df, axis=0)\n", "print(f\"selected patches {npatches}: {npatches / len(filtered_concat_dfs)}\")\n", "print(f'final neg data: {len(final_neg_df)}')\n", "print(f\"final neg per patch: {len(final_neg_df) / npatches:.2f}\")\n", "print(f\"neg to score ratio: {len(final_neg_df) / (len(all_concat_dfs) * len(all_concat_dfs[0])): .2%}\")\n", "\n", "\n", "print(\n", "    f\"neg precision: {len(set(final_neg_df.index).intersection(all_y_neg_df.index)) / final_neg_df.index.size}\"\n", ")\n", "\n", "print(\n", "    f\"hard neg recall: {len(set(all_y_hard_neg_df.index).intersection(final_neg_df.index)) / all_y_hard_neg_df.index.size}\"\n", ")\n", "\n", "\n", "\n", "# print(x_model)\n", "# print(y_model)\n", "# show_scatter_plot(\n", "#     f\"{x_model}_ppg\",\n", "#     f\"{y_model}_ppg\",\n", "#     final_neg_df,\n", "#     square_plot=False,\n", "#     joint_lim=2.0,\n", "#     len_norm=False,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(1343 + 3622) / (448*128)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LLM Scoring Playground"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.eval.harness import factories\n", "from research.core.types import EMPTY_CHUNK\n", "\n", "\n", "def model_input_from_row(row:pd.Series, empty_chunk=False):\n", "    return ModelInput(\n", "        prefix=row[\"prefix\"],\n", "        suffix=row[\"suffix\"],\n", "        retrieved_chunks=[EMPTY_CHUNK if empty_chunk else row[\"chunk\"]],\n", "        path=row[\"file_path\"],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load LLM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.unload()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = dict(\n", "    name=\"rogue\",\n", "    checkpoint_path=\"rogue/diffb1m_16b_alphal_fixtoken\",\n", "    prompt=dict(\n", "        max_prefix_tokens=1280,\n", "        max_suffix_tokens=768,\n", "        max_retrieved_chunk_tokens=-1,\n", "        max_prompt_tokens=3072,\n", "        # NOTE: Change this to control the max number of retrieved chunks in the prompt.\n", "        max_number_chunks=32,\n", "    ),\n", ")\n", "\n", "model = factories.create_model(config)\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_reranker\n", "\n", "rerank = create_reranker(\n", "    model,\n", "    config={\n", "        \"name\": \"oracle_perplexity_reranker\",\n", "        \"top_k\": 256,\n", "        \"batchsize\": 1,\n", "    },\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test GPU Memory for Scoring"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import megatron\n", "import torch\n", "from research.models.gpt_neox_helper import (\n", "    generate_samples_from_prompt_with_stop,\n", "    generate_samples_from_prompt_without_stop,\n", "    pad_tensor,\n", "    unpad_tensor,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["toks = [0] * 1024 * 4\n", "text = model.tokenizer.detokenize(toks)\n", "print(len(model.tokenizer.tokenize(text)))\n", "model.log_likelihood(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_seqs = [\n", "    [0] * 1024,\n", "    [1, 2, 3, 4],\n", "    [10] * 2048,\n", "]\n", "padded_batch_seqs, batch_padding_mask = pad_tensor(batch_seqs, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_iterator = iter([{\"text\": padded_batch_seqs.clone().detach()}])\n", "with torch.no_grad():\n", "    loss, batch_padded_logits = megatron.training.forward_step(\n", "        data_iterator=data_iterator,\n", "        model=model.neox_model,\n", "        neox_args=model._neox_args,\n", "        timers=None,\n", "        return_logits=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_unpadded_logits = unpad_tensor(batch_padded_logits, batch_padding_mask)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_unpadded_logits[0].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_likelihoods = []\n", "for seq, num_toks_to_score, unpadded_logits in zip(\n", "    batch_seqs, [1, 2], batch_unpadded_logits\n", "):\n", "    # Grab relevant logits\n", "    relevant_logits = unpadded_logits[-num_toks_to_score:]\n", "    reference_tokens = torch.tensor(\n", "        seq[-num_toks_to_score:],\n", "        device=relevant_logits.device,\n", "        dtype=torch.int64,\n", "    )\n", "\n", "    # The loss can also be computed from the logits\n", "    loss = (\n", "        F.cross_entropy(\n", "            relevant_logits.float(),\n", "            reference_tokens,\n", "        )\n", "        .cpu()\n", "        .item()\n", "    )\n", "\n", "    log_likelihood = -loss\n", "    log_likelihoods.append(log_likelihood)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logits.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gc\n", "# gc.collect()\n", "torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%timeit\n", "\n", "seq_length = 4096\n", "bs = 4\n", "data_iterator = iter([{\"text\": torch.zeros([bs, seq_length], dtype=torch.int64)}])\n", "\n", "with torch.no_grad():\n", "    loss, logits = megatron.training.forward_step(\n", "        data_iterator=data_iterator,\n", "        model=model.neox_model,\n", "        neox_args=model._neox_args,\n", "        timers=None,\n", "        return_logits=True,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd ls s3://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch.nn.functional as F\n", "import torch\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_128_128_full/'\n", "cache = load_parquet(path)\n", "row = cache.iloc[0]\n", "cache.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch.nn.functional as F\n", "import torch\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b/stage_5_ppg_diffb1m_1b_alphal_fixtoken_128_128_ppg_nosort/'\n", "cache = load_parquet(path)\n", "row = cache.iloc[0]\n", "cache.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json.loads(cache['ppl'][0])[4]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vzhao.data import pandas_functions as vz_pdfn\n", "\n", "func = vz_pdfn.ComputePPL(config, score_batch_size=8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ppl1 = func(\n", "    prefix=row[\"prefix\"],\n", "    suffix=row[\"suffix\"],\n", "    middle=row[\"middle\"],\n", "    file_path=row[\"file_path\"],\n", "    retrieved_chunks=row[\"retrieved_chunks\"],\n", ")\n", "ppl1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_reranker\n", "rerank = create_reranker(\n", "    model,\n", "    config={\n", "        \"name\": \"oracle_perplexity_reranker\",\n", "        \"top_k\": 256,\n", "        \"batchsize\": 1,\n", "    },\n", ")\n", "\n", "row_id = 0\n", "model_input = model_input_from_row(cache.loc[row_id], empty_chunk=True)\n", "chunks = common.deserialize_retrieved_chunks(\n", "    cache[\"retrieved_chunks\"][row_id]\n", ")\n", "model_input.retrieved_chunks = chunks\n", "\n", "\n", "print(len(model_input.retrieved_chunks))\n", "# print(model.log_likelihood_continuation([model_input], [cache[\"middle\"][row_id]]))\n", "print(rerank.batchsize)\n", "print(rerank._score(model_input, cache[\"middle\"][row_id])[:10])\n", "print(func._reranker.batchsize)\n", "print(func._reranker._score(model_input, cache[\"middle\"][row_id])[:10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_inputs = [\n", "    ModelInput(\n", "        prefix=model_input.prefix,\n", "        suffix=model_input.suffix,\n", "        path=model_input.path,\n", "        retrieved_chunks=[chunks[0]],\n", "    ),\n", "    ModelInput(\n", "        prefix=model_input.prefix,\n", "        suffix=model_input.suffix,\n", "        path=model_input.path,\n", "        retrieved_chunks=[chunks[1]],\n", "    ),\n", "    # ModelInput(\n", "    #     prefix=model_input.prefix,\n", "    #     suffix=model_input.suffix,\n", "    #     path=model_input.path,\n", "    #     retrieved_chunks=[chunks[0]],\n", "    # ),\n", "    # ModelInput(\n", "    #     prefix=model_input.prefix,\n", "    #     suffix=model_input.suffix,\n", "    #     path=model_input.path,\n", "    #     retrieved_chunks=[chunks[0]],\n", "    # ),\n", "]\n", "tgts = [cache[\"middle\"][row_id]] * len(model_inputs)\n", "\n", "prompt_toks = [rerank.model.prompt_formatter.prepare_prompt(m)[0] for m in model_inputs] \n", "tgt_toks = [rerank.model.prompt_formatter.tokenizer.tokenize(t) for t in tgts]\n", "print(len(tgt_toks[0]))\n", "print(tgt_toks[0][:5])\n", "toks = [p+t for p, t in zip(prompt_toks, tgt_toks)]\n", "rerank.model.log_likelihood_continuation(model_inputs, tgts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models import gpt_neox_helper\n", "\n", "padded_toks, pad_mask = gpt_neox_helper.pad_tensor(toks, rerank.model.tokenizer.pad_id)\n", "print(padded_toks.shape)\n", "print(padded_toks[0][:10])\n", "print(padded_toks[0][-10:])\n", "\n", "import megatron\n", "\n", "data_iterator = iter([{\"text\": padded_toks}])\n", "with torch.no_grad():\n", "    loss, logits = megatron.training.forward_step(\n", "        data_iterator=data_iterator,\n", "        model=rerank.model.neox_model,\n", "        neox_args=rerank.model._neox_args,\n", "        timers=None,\n", "        return_logits=True,\n", "    )\n", "print(logits.shape)\n", "print(logits[0, 0, :10])\n", "logits1 = logits\n", "\n", "\n", "unpadded_logits = gpt_neox_helper.unpad_tensor(logits, pad_mask)\n", "print(unpadded_logits[0].shape)\n", "print(unpadded_logits[0][0, :10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(tgt_toks[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["-F.cross_entropy(\n", "    unpadded_logits[0][-len(tgt_toks[0]) :].float(),\n", "    torch.tensor(tgt_toks[0], device=unpadded_logits[0].device, dtype=torch.int64),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unpadded_logits[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models import gpt_neox_helper\n", "padded_toks = gpt_neox_helper.pad_tensor(toks, rerank.model.tokenizer.pad_id)[0]\n", "print(padded_toks.shape)\n", "print(padded_toks[0, :89][:10])\n", "print(padded_toks[0, :89][-10:])\n", "\n", "import megatron\n", "\n", "data_iterator = iter([{\"text\": torch.tensor(padded_toks[:, :89],dtype=torch.int64)}])\n", "with torch.no_grad():\n", "    loss, logits = megatron.training.forward_step(\n", "        data_iterator=data_iterator,\n", "        model=rerank.model.neox_model,\n", "        neox_args=rerank.model._neox_args,\n", "        timers=None,\n", "        return_logits=True,\n", "    )\n", "print(logits.shape)\n", "print(logits[0,0, :10])\n", "logits2 = logits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logits1[0, 0, :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logits2[0, 0, :]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Fastforward Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models import fastforward_models\n", "from research.core.model_input import ModelInputt ModelInputt ModelInputt ModelInput\n", "from research.core.constants import AUGMENT_CHECKPOINTS_ROOT\n", "import torch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = fastforward_models.StarCoderBase16B_FastForward(\n", "    checkpoint_path=AUGMENT_CHECKPOINTS_ROOT\n", "    / \"rogue/diffb1m_16b_alphal_fixtoken/global_step1000/\"\n", ")\n", "prompt_config = {\n", "    \"max_prefix_tokens\": 1280,\n", "    \"max_suffix_tokens\": 768,\n", "    \"max_retrieved_chunk_tokens\": -1,\n", "    \"max_prompt_tokens\": 3072,\n", "}\n", "prompt_config.pop(\"retrieval_layout_style\", None)\n", "model.prompt_formatter.rebind(**prompt_config)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["step_fn = model.step_fn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inspect Training Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!s3cmd ls s3://augment-temporary/vzhao/ppl_gain/1022_1pos_16total_posppl_1.0_long_pre_filepath/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.data.spark import get_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from augment.experimental.vzhao.data import common\n", "import os \n", "import torch\n", "import json\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "from research.data.spark.pipelines.stages import common as spark_common\n", "import json\n", "import numpy as np\n", "\n", "spark = get_session()\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1022_1pos_16total_posppl_1.0_long_pre_filepath/stage_8_explode'\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1025_7b_1pos_16total_03_08_lp_fp/stage_8_explode'\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = CodeGenTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ppg = json.loads(df['ppg'][32 * 5])\n", "print(ppg)\n", "print(torch.softmax(torch.tensor(ppg) * 10, dim=-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df) / 32"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ppgs = []\n", "for i in range(1310):\n", "    all_ppgs.append(json.loads(df['ppg'][32*i])[0])\n", "np.mean(all_ppgs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sns.histplot(all_ppgs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ppg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PPL score noise level\n", "\n", "we can use std_below_zero as the margin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ppgs = []\n", "all_chunks = []\n", "all_middles = []\n", "all_prefix = []\n", "all_suffix = []\n", "all_retrieval_ranks = []\n", "for _, row in df.iterrows():\n", "    all_ppgs.extend(json.loads(row[\"ppg\"]))\n", "    all_chunks.extend(common.deserialize_retrieved_chunks(row[\"retrieved_chunks\"]))\n", "    all_middles.extend([row[\"middle\"]] * len(json.loads(row[\"ppg\"])))\n", "    all_prefix.extend([row[\"prefix\"]] * len(json.loads(row[\"ppg\"])))\n", "    all_suffix.extend([row[\"suffix\"]] * len(json.loads(row[\"ppg\"])))\n", "\n", "    all_retrieval_ranks.extend(json.loads(row['retrieval_rank']))\n", "\n", "\n", "\n", "df_all = pd.DataFrame(\n", "    {\n", "        \"prefix\": all_prefix,\n", "        \"middle\": all_middles,\n", "        \"suffix\": all_suffix,\n", "        \"chunk\": all_chunks,\n", "        \"ppg\": all_ppgs,\n", "        'ret_rank': [r-1 for r in all_retrieval_ranks]\n", "    }\n", ")\n", "df_all.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def std_below_zero(ppg):\n", "    \"\"\"Only computes \"\"\"\n", "    ppg = np.array(ppg)\n", "    ppg = ppg[ppg<0]\n", "    if not ppg.size:\n", "        return 0\n", "    return np.sqrt(np.mean(ppg ** 2))\n", "\n", "\n", "def std_above_zero(ppg):\n", "    \"\"\"Only computes \"\"\"\n", "    ppg = np.array(ppg)\n", "    ppg = ppg[ppg>0]\n", "    if not ppg.size:\n", "        return 0\n", "    return np.sqrt(np.mean(ppg ** 2))\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["std_below_zero(all_ppgs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["std_above_zero(all_ppgs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Middle and Retrieval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_all.loc[df_all['ppg']>0.1, :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _, row in df_all.loc[df_all['ppg']>0.1, :].iterrows():\n", "    clear_output(wait=True)\n", "    print(f\"{row['ppg']} {row['ret_rank']}\", flush=True)\n", "    print_row(row, prefix_lines=20, suffix_lines=20)\n", "    time.sleep(0.1)\n", "    if input() == 'c':\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.data.spark import get_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from augment.experimental.vzhao.data import common\n", "import pyspark.sql.functions as F\n", "import pyspark.sql.types as T\n", "import os \n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = get_session()\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/test/stage_5/\"\n", "path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_7_token/\"\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df.head()\n", "# spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def to_list(chunk_str: str) -> list[str]:\n", "    return [json.dumps(c) for c in json.loads(chunk_str)]\n", "\n", "ret_chunk_udf = F.udf(lambda x: to_list(x), T.ArrayType(T.StringType()))\n", "float_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.FloatType()))\n", "int_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.IntegerType()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.select(\n", "    \"prefix\",\n", "    \"suffix\",\n", "    \"middle\",\n", "    \"file_path\",\n", "    <PERSON>.explode(F.arrays_zip(\n", "        ret_chunk_udf(<PERSON><PERSON>col(\"retrieved_chunks\")).alias(\"retrieved_chunks\"),\n", "        float_array_udf(<PERSON><PERSON>col('ppl_scores')).alias('ppl_scores'),\n", "        float_array_udf(F.col('ppl')).alias('ppl'),\n", "        float_array_udf(F.col('ppg')).alias('ppg'),\n", "        int_array_udf(<PERSON>.col('retrieval_rank')).alias('retrieval_rank'),\n", "        \"prompt_tokens\",\n", "    )).alias(\"zipped\"),\n", ").select(\"prefix\", \"zipped.retrieved_chunks\", \"zipped.ppl\",\"zipped.prompt_tokens\",)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.withColumn('ppl', F.udf(lambda x: json.loads(x), T.ArrayType(T.FloatType()))(F.col('ppl'))).select(F.arrays_zip('ppl', 'prompt_tokens'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.select(F.arrays_zip('ppl', 'prompt_tokens'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.withColumn(\"prompt_tokens\", F.arrays_zip('retrieval_rank', 'prompt_tokens') F.explode(\"prompt_tokens\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.data.spark import get_session, k8s_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n", "from augment.experimental.vzhao.data import common\n", "import os \n", "import json\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ppg = []\n", "for _, row in df.iterrows():\n", "    _ppg = np.array(json.loads(row['ppg']))\n", "    _ppl = np.array(json.loads(row['ppl']))\n", "    all_ppg.extend(_ppl[_ppg>0].tolist()) \n", "# plt.hist(all_ppg, bins=20, range=(0, 1))\n", "plt.hist(all_ppg, bins=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = get_session()\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/test/stage_5/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_7_token/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "# path = 's3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/'\n", "path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.sort_values(\"kendalltau\")\n", "\n", "for _, row in df.iterrows():\n", "    _ppg = np.array(json.loads(row[\"ppg\"]))\n", "\n", "    # # idx is the hard positive.\n", "    # idx = len(_ppg[_ppg > 0]) - 1\n", "\n", "    # idx is the hard positive.\n", "    idx = 0\n", "\n", "    new_row = row\n", "    new_row[\"chunk\"] = common.deserialize_retrieved_chunks(row[\"retrieved_chunks\"])[idx]\n", "    clear_output(wait=True)\n", "    print(\n", "        f\"kendaltau: {row['kendalltau']}; ret_rank: {json.loads(row['retrieval_rank'])[idx]}; ppg: {json.loads(row['ppg'])[idx]}, ppl: {json.loads(row['ppl'])[idx]}\"\n", "    )\n", "    print_row(new_row, prefix_lines=20, suffix_lines=20)\n", "    time.sleep(0.1)\n", "    if input() == \"c\":\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON>rgquet Files and Inspect"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from IPython.display import HTML, clear_output, display\n", "from scipy.stats import kendalltau, rankdata\n", "\n", "from augment.experimental.vzhao.data import common\n", "from augment.research.data.spark import get_session, k8s_session\n", "from augment.research.data.spark.pipelines.utils import map_parquet\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = get_session()\n", "\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_1/part-00103-2382dd5b-c6dc-4cdf-88f3-a8b7532f1385-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ethanol_rag/test/stage_2/part-00125-b445f864-0500-4953-8543-a2968d85d660-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/perplexity_distill3/03_processed/part-01999-aa29fafe-d606-499e-9961-68936c37d415-c000.zstd.parquet'\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/test/stage_4/'\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_6/\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1009/stage_8_explode/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_7_token\"\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_label_balance/stage_6_label/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_hard_example_mining/stage_6_mining/\"\n", "\n", "# path = \"s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_6_label/\"\n", "\n", "# path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard/stage_6_mining'\n", "\n", "path = 's3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg/stage_6_mining'\n", "\n", "files = map_parquet.list_files(spark, path, suffix=\"parquet\", include_path=False)\n", "df = spark.read.parquet(os.path.join(path, files[0]))\n", "# df\n", "df = df.to<PERSON><PERSON><PERSON>()\n", "df.head()\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.eval.harness.factories import create_retriever\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for s in scorers.values():\n", "    s.unload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorers = {}\n", "\n", "for new_col, ckpt in {\n", "    \"ethanol_plus\": \"ethanol_plus/6pos_32total_v0.1\",\n", "    \"ethanol_plus_hard\": \"ethanol_plus/1012_6pos_32total_hardneg\",\n", "    \"ethanol\": \"ethanol/ethanol-01-codegen\",\n", "}.items():\n", "    retriever_config = {\n", "        \"scorer\": {\n", "            # Change checkpoint to change models.\n", "            \"name\": \"diff_boykin\",\n", "            \"checkpoint\": ckpt,\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 40,\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"simple_query\",\n", "            \"max_lines\": 20,\n", "        },\n", "    }\n", "    retrieval_database = create_retriever(retriever_config)\n", "    dense_scorers = retrieval_database.scorer\n", "    dense_scorers.load()\n", "    scorers[new_col] = dense_scorers\n", "    clear_output()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_new_rank(df, dense_scorers: list, new_cols):\n", "    assert len(dense_scorers) == len(new_cols)\n", "    for scorer, new_cal in zip(dense_scorers, new_cols):\n", "        rst, _ = scorer.rerank(\n", "            ModelInput(\n", "                df[\"prefix\"][0],\n", "                retrieved_chunks=df['chunks'].tolist(),\n", "            )\n", "        )\n", "        sorted_chunks = rst.retrieved_chunks\n", "        cid_to_rank = {}\n", "        for rank, c in enumerate(sorted_chunks):\n", "            cid_to_rank[c.id] = rank\n", "        df[new_cal] = [cid_to_rank[row[\"chunks\"].id] for _, row in df.iterrows()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pandas as pd\n", "from scipy.stats import rankdata, kendalltau\n", "from IPython.display import display, HTML, clear_output\n", "\n", "for idx in range(20):\n", "    clear_output(wait=True)\n", "    foo = pd.DataFrame(\n", "        {\n", "            \"prefix\": df[\"prefix\"][idx],\n", "            \"ppg\": json.loads(df[\"ppg\"][idx]),\n", "            \"ppg_raw\": json.loads(df[\"ppg_raw\"][idx]),\n", "            \"ppl\": json.loads(df[\"ppl\"][idx]),\n", "            \"ret_rank\": json.loads(df[\"retrieval_rank\"][idx]),\n", "            \"chunks\": common.deserialize_retrieved_chunks(df[\"retrieved_chunks\"][idx]),\n", "        }\n", "    )\n", "    print(df[\"kendalltau\"][idx])\n", "    print(\n", "        kendalltau(\n", "            rankdata(foo[\"ret_rank\"], method=\"min\"),\n", "            rankdata(-foo[\"ppg\"], method=\"min\"),\n", "            variant=\"b\",\n", "        )\n", "    )\n", "    compute_new_rank(foo, scorers.values(), scorers.keys())\n", "    display(foo)\n", "    time.sleep(0.1)\n", "    if input() == \"c\":\n", "        break\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize Logits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.visualize_logits import LogitsVisualizer\n", "from research.eval.harness import factories\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.unload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = factories.create_model(\n", "    dict(\n", "        name=\"rogue\",\n", "        checkpoint_path=\"/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken\",\n", "        prompt=dict(\n", "            max_prefix_tokens=1024,\n", "            max_prompt_tokens=3816,\n", "            max_suffix_tokens=1024,\n", "            max_retrieved_chunk_tokens=-1,\n", "            # NOTE: Change this to control the max number of retrieved chunks in the prompt.\n", "            max_number_chunks=100,\n", "        ),\n", "    )\n", ")\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_toks, _ = model.prompt_formatter.prepare_prompt(ModelInput('hello'))\n", "tgt_toks = model.prompt_formatter.tokenizer.tokenize('world world')\n", "concat_toks = prompt_toks + tgt_toks\n", "prompt = model.prompt_formatter.tokenizer.detokenize(concat_toks)\n", "LogitsVisualizer().visualize_model_prediction(model, prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Draft"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
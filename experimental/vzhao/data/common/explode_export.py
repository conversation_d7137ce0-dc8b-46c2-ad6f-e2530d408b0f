"""Run last part of the data pipeline.

Usage:

python experimental/vzhao/data/common/explode_export.py
"""

import importlib
import pathlib
import re

import pandas as pd
from megatron.tokenizer import get_tokenizer

from experimental.vzhao.data import spark_stages
from research.data.spark import get_session, k8s_session
from research.data.spark.pipelines.stages import common as spark_common
from research.data.spark.pipelines.utils import map_parquet

vzhao_spark_common = importlib.import_module("experimental.vzhao.data.common")

# noqa
# flake8: noqa
# pylint: skip-file

CONFIG = {
    "tokenizer_name": "StarCoderTokenizer",
}
INPUT_URI = "/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.3/stage4_prompt"

EXPLODE_URI = "/mnt/efs/spark-data/temp_weekly/vzhao/menthol/0416.3/stage5_explode"
# EXPLODE_URI = "s3a://augment-temporary/vzhao/menthol/0416.1/stage5_explode"
# if pathlib.Path(EXPLODE_URI).exists():
#     raise ValueError(f"explode path {EXPLODE_URI} already exists")


DATA_OUTPUT_PATH = "/mnt/efs/augment/user/vzhao/data/menthol/0416.3"
if pathlib.Path(DATA_OUTPUT_PATH).exists():
    raise ValueError(f"data output path {DATA_OUTPUT_PATH} already exists")


def stage9():
    def explode_prompts(batch: pd.DataFrame) -> pd.DataFrame:
        """Explode prompts to tokens.

        Args:
            batch: pd.DataFrame
        """
        # Drop all other columns.
        results = batch[["prompt_tokens"]].explode("prompt_tokens")
        return results if len(results) > 0 else pd.DataFrame()

    # spark = get_session(control_plane="local[*]")
    spark = k8s_session(
        name="vzhao-explode",
        max_workers=128,
    )
    map_parquet.apply_pandas(
        spark,
        explode_prompts,
        input_path=INPUT_URI,
        output_path=EXPLODE_URI,
        output_column="prompt_tokens",
        drop_original_columns=True,
        timeout=7200,
        # For debug
        # batch_size=10,
        # timing_run=True,
    )
    spark.stop()


stage9()


class ObjectDict(dict):
    """Provides both namespace-like and dict-like access to fields.

    Allows access to fields using both obj.name notation and obj["name"]
    notation. The latter is useful when "name" contains periods, for example.
    """

    def __getattr__(self, name: str):
        if name in self:
            return self[name]
        else:
            raise AttributeError("No such attribute: " + name)

    def __setattr__(self, name: str, value):
        self[name] = value

    def __delattr__(self, name: str):
        if name in self:
            del self[name]
        else:
            raise AttributeError("No such attribute: " + name)


def stage10():
    spark = get_session(control_plane="local[*]")
    # spark = k8s_session(
    #     name="vzhao-dev-export_indexed_dataset",
    #     max_workers=128,
    #     conf={
    #         "spark.executor.pyspark.memory": "1050g",
    #     },
    # )
    p = pathlib.Path(DATA_OUTPUT_PATH)
    if not p.parent.exists():
        p.parent.mkdir(parents=True, exist_ok=True)

    vzhao_spark_common.export_indexed_dataset(
        spark=spark,
        input=pathlib.Path(EXPLODE_URI),
        output=pathlib.Path(DATA_OUTPUT_PATH),
        samples_column="prompt_tokens",
        num_validation_samples=8192,
        tokenizer=get_tokenizer(CONFIG["tokenizer_name"]),
    )
    spark.stop()

    return spark


spark = stage10()

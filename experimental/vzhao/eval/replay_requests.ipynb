{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import logging\n", "import time\n", "from collections.abc import Iterable\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "\n", "from base.augment_client.client import AugmentClient, UploadContent\n", "from base.datasets import completion, completion_dataset, tenants\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Downloads the replays from the augment-replays bucket."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download Interesting Requests from old proto events\n", "\n", "Need to manually parse the result due to code changes."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion_dataset:Querying BigQuery: \n", "WITH\n", "  request AS (\n", "  SELECT\n", "    request_id,\n", "    raw_proto AS request_proto,\n", "    time AS request_timestamp\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_host_request'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN (\"38a87727-f608-486e-baa0-d580f06a4701\",\"f59e877b-df59-4812-8ce1-b5c4e4686b25\",\"e0a30509-89b0-4d1c-ae83-83077ff1eb24\",\"dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\",\"d46dc24a-f904-41b8-a2bb-d52545513e96\",\"d454140e-dd4b-4cf8-bd19-3a61d85e9163\",\"ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\",\"c2f63804-efed-4711-970b-8b9032476758\",\"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\",\"b535a8bc-cbda-45da-b7fc-1b9976980137\",\"b251b2bf-eed4-475b-b969-cf6807aab4df\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\",\"9f6469a2-65f3-486c-a58e-d34c598087b6\",\"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\",\"86dff485-1174-4bc3-a9d4-5dec6d8be5d2\",\"85f9c626-3b59-41bc-8c2e-a73bed1d81d6\",\"84e6ffca-44de-41eb-8bce-c8d460308c7c\",\"7e6d997c-3d6e-4493-a3a2-13983fbb571f\",\"79484d9e-62cf-4671-a452-5fea63b10196\",\"73e95698-12d1-49cc-b5a0-6179dca2acde\",\"73ad3121-7aec-4dea-8542-1e2f3d2e1b59\",\"669c8097-7324-4af9-afde-8098b6de1fdc\",\"64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\",\"5f9abc1e-bbdb-465f-8ee4-91c5ed178958\",\"5bb9f026-9c2a-4681-b1df-34ba6e7b6407\",\"58512ff9-45ca-4f45-8446-be352e2c3bdb\",\"57d60d7d-c2ce-4755-8a7b-4a3d01214859\",\"488a7a15-da79-4dae-802b-a449cd63cfab\",\"4813df91-f7b9-4e69-9a07-0547b84ccb43\",\"438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\",\"3f773136-5967-4c96-ba75-9ed692d44a77\",\"38315ad8-b8f3-4c6a-84e5-afa5933f38ba\",\"37b49998-03f4-441f-b584-982aae11fcfb\",\"3456d0ed-1abc-42a0-837d-4cf4be2db30b\",\"341aefb7-45b7-4b72-b000-b9f166d3f95f\",\"2c9f54cb-fd41-4adc-8fc5-59074916c06d\",\"2a53b956-82e5-41b5-97b0-4e07ce2b9052\",\"252c43eb-664c-4c07-8b05-97dc013f45de\",\"200e17a6-d2b2-4128-8856-a7a034fd135f\",\"19d3d1b0-0509-4074-bab0-140e12356350\",\"126fbf6e-a739-4ad1-9942-af8da6af17f0\",\"0272b5ff-69e5-4034-a31d-b430756f3f4f\") ),\n", "  response AS (\n", "  SELECT\n", "    request_id,\n", "    ARRAY_AGG(raw_proto) AS response_proto,\n", "    ARRAY_AGG(time) AS response_timestamp\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_host_response'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN (\"38a87727-f608-486e-baa0-d580f06a4701\",\"f59e877b-df59-4812-8ce1-b5c4e4686b25\",\"e0a30509-89b0-4d1c-ae83-83077ff1eb24\",\"dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\",\"d46dc24a-f904-41b8-a2bb-d52545513e96\",\"d454140e-dd4b-4cf8-bd19-3a61d85e9163\",\"ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\",\"c2f63804-efed-4711-970b-8b9032476758\",\"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\",\"b535a8bc-cbda-45da-b7fc-1b9976980137\",\"b251b2bf-eed4-475b-b969-cf6807aab4df\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\",\"9f6469a2-65f3-486c-a58e-d34c598087b6\",\"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\",\"86dff485-1174-4bc3-a9d4-5dec6d8be5d2\",\"85f9c626-3b59-41bc-8c2e-a73bed1d81d6\",\"84e6ffca-44de-41eb-8bce-c8d460308c7c\",\"7e6d997c-3d6e-4493-a3a2-13983fbb571f\",\"79484d9e-62cf-4671-a452-5fea63b10196\",\"73e95698-12d1-49cc-b5a0-6179dca2acde\",\"73ad3121-7aec-4dea-8542-1e2f3d2e1b59\",\"669c8097-7324-4af9-afde-8098b6de1fdc\",\"64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\",\"5f9abc1e-bbdb-465f-8ee4-91c5ed178958\",\"5bb9f026-9c2a-4681-b1df-34ba6e7b6407\",\"58512ff9-45ca-4f45-8446-be352e2c3bdb\",\"57d60d7d-c2ce-4755-8a7b-4a3d01214859\",\"488a7a15-da79-4dae-802b-a449cd63cfab\",\"4813df91-f7b9-4e69-9a07-0547b84ccb43\",\"438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\",\"3f773136-5967-4c96-ba75-9ed692d44a77\",\"38315ad8-b8f3-4c6a-84e5-afa5933f38ba\",\"37b49998-03f4-441f-b584-982aae11fcfb\",\"3456d0ed-1abc-42a0-837d-4cf4be2db30b\",\"341aefb7-45b7-4b72-b000-b9f166d3f95f\",\"2c9f54cb-fd41-4adc-8fc5-59074916c06d\",\"2a53b956-82e5-41b5-97b0-4e07ce2b9052\",\"252c43eb-664c-4c07-8b05-97dc013f45de\",\"200e17a6-d2b2-4128-8856-a7a034fd135f\",\"19d3d1b0-0509-4074-bab0-140e12356350\",\"126fbf6e-a739-4ad1-9942-af8da6af17f0\",\"0272b5ff-69e5-4034-a31d-b430756f3f4f\")\n", "  GROUP BY\n", "    request_id ),\n", "  resolution AS (\n", "  SELECT\n", "    request_id,\n", "    accepted_idx >=0 AS accepted,\n", "    time AS resolution_timestamp,\n", "    raw_proto AS resolution_proto\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_resolution'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN (\"38a87727-f608-486e-baa0-d580f06a4701\",\"f59e877b-df59-4812-8ce1-b5c4e4686b25\",\"e0a30509-89b0-4d1c-ae83-83077ff1eb24\",\"dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\",\"d46dc24a-f904-41b8-a2bb-d52545513e96\",\"d454140e-dd4b-4cf8-bd19-3a61d85e9163\",\"ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\",\"c2f63804-efed-4711-970b-8b9032476758\",\"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\",\"b535a8bc-cbda-45da-b7fc-1b9976980137\",\"b251b2bf-eed4-475b-b969-cf6807aab4df\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\",\"9f6469a2-65f3-486c-a58e-d34c598087b6\",\"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\",\"86dff485-1174-4bc3-a9d4-5dec6d8be5d2\",\"85f9c626-3b59-41bc-8c2e-a73bed1d81d6\",\"84e6ffca-44de-41eb-8bce-c8d460308c7c\",\"7e6d997c-3d6e-4493-a3a2-13983fbb571f\",\"79484d9e-62cf-4671-a452-5fea63b10196\",\"73e95698-12d1-49cc-b5a0-6179dca2acde\",\"73ad3121-7aec-4dea-8542-1e2f3d2e1b59\",\"669c8097-7324-4af9-afde-8098b6de1fdc\",\"64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\",\"5f9abc1e-bbdb-465f-8ee4-91c5ed178958\",\"5bb9f026-9c2a-4681-b1df-34ba6e7b6407\",\"58512ff9-45ca-4f45-8446-be352e2c3bdb\",\"57d60d7d-c2ce-4755-8a7b-4a3d01214859\",\"488a7a15-da79-4dae-802b-a449cd63cfab\",\"4813df91-f7b9-4e69-9a07-0547b84ccb43\",\"438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\",\"3f773136-5967-4c96-ba75-9ed692d44a77\",\"38315ad8-b8f3-4c6a-84e5-afa5933f38ba\",\"37b49998-03f4-441f-b584-982aae11fcfb\",\"3456d0ed-1abc-42a0-837d-4cf4be2db30b\",\"341aefb7-45b7-4b72-b000-b9f166d3f95f\",\"2c9f54cb-fd41-4adc-8fc5-59074916c06d\",\"2a53b956-82e5-41b5-97b0-4e07ce2b9052\",\"252c43eb-664c-4c07-8b05-97dc013f45de\",\"200e17a6-d2b2-4128-8856-a7a034fd135f\",\"19d3d1b0-0509-4074-bab0-140e12356350\",\"126fbf6e-a739-4ad1-9942-af8da6af17f0\",\"0272b5ff-69e5-4034-a31d-b430756f3f4f\") ),\n", "  feedback AS (\n", "  SELECT\n", "    request_id,\n", "    MAX_BY(raw_proto, time) AS feedback_proto,\n", "    MAX(time) AS feedback_timestamp,\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_feedback'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN (\"38a87727-f608-486e-baa0-d580f06a4701\",\"f59e877b-df59-4812-8ce1-b5c4e4686b25\",\"e0a30509-89b0-4d1c-ae83-83077ff1eb24\",\"dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\",\"d46dc24a-f904-41b8-a2bb-d52545513e96\",\"d454140e-dd4b-4cf8-bd19-3a61d85e9163\",\"ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\",\"c2f63804-efed-4711-970b-8b9032476758\",\"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\",\"b535a8bc-cbda-45da-b7fc-1b9976980137\",\"b251b2bf-eed4-475b-b969-cf6807aab4df\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\",\"ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\",\"9f6469a2-65f3-486c-a58e-d34c598087b6\",\"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\",\"86dff485-1174-4bc3-a9d4-5dec6d8be5d2\",\"85f9c626-3b59-41bc-8c2e-a73bed1d81d6\",\"84e6ffca-44de-41eb-8bce-c8d460308c7c\",\"7e6d997c-3d6e-4493-a3a2-13983fbb571f\",\"79484d9e-62cf-4671-a452-5fea63b10196\",\"73e95698-12d1-49cc-b5a0-6179dca2acde\",\"73ad3121-7aec-4dea-8542-1e2f3d2e1b59\",\"669c8097-7324-4af9-afde-8098b6de1fdc\",\"64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\",\"5f9abc1e-bbdb-465f-8ee4-91c5ed178958\",\"5bb9f026-9c2a-4681-b1df-34ba6e7b6407\",\"58512ff9-45ca-4f45-8446-be352e2c3bdb\",\"57d60d7d-c2ce-4755-8a7b-4a3d01214859\",\"488a7a15-da79-4dae-802b-a449cd63cfab\",\"4813df91-f7b9-4e69-9a07-0547b84ccb43\",\"438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\",\"3f773136-5967-4c96-ba75-9ed692d44a77\",\"38315ad8-b8f3-4c6a-84e5-afa5933f38ba\",\"37b49998-03f4-441f-b584-982aae11fcfb\",\"3456d0ed-1abc-42a0-837d-4cf4be2db30b\",\"341aefb7-45b7-4b72-b000-b9f166d3f95f\",\"2c9f54cb-fd41-4adc-8fc5-59074916c06d\",\"2a53b956-82e5-41b5-97b0-4e07ce2b9052\",\"252c43eb-664c-4c07-8b05-97dc013f45de\",\"200e17a6-d2b2-4128-8856-a7a034fd135f\",\"19d3d1b0-0509-4074-bab0-140e12356350\",\"126fbf6e-a739-4ad1-9942-af8da6af17f0\",\"0272b5ff-69e5-4034-a31d-b430756f3f4f\")\n", "  GROUP BY\n", "    request_id )\n", "SELECT\n", "  request.request_id,\n", "  request.request_proto,\n", "  request.request_timestamp,\n", "  response.response_proto,\n", "  response.response_timestamp,\n", "  resolution.accepted,\n", "  resolution.resolution_timestamp,\n", "  feedback.feedback_proto,\n", "  feedback.feedback_timestamp\n", "FROM\n", "  request\n", "JOIN\n", "  response\n", "USING\n", "  (request_id)\n", "LEFT JOIN\n", "  resolution\n", "USING\n", "  (request_id)\n", "LEFT JOIN\n", "  feedback\n", "USING\n", "  (request_id)\n", "WHERE\n", "  TRUE\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["43\n"]}], "source": ["from google.cloud import bigquery\n", "\n", "# Interesting requests.\n", "request_ids = [\n", "    \"302845c8-9ce1-4e3a-9d6e-e2c748592991\",\n", "    \"bcc92d5b-6b8b-4fd5-b538-7af78e43ce2f\",\n", "    \"b79c3c42-23df-4879-bb88-443e2a71f60b\",\n", "    \"81c1cab1-65a1-482b-9f88-d705e70b5018\",\n", "    \"10603d5e-7bdc-47cd-a4ea-4b0667bee238\",\n", "    \"53ac398d-b77d-440f-a710-470e81b92a2f\",\n", "    \"7ee23e95-75ca-4f40-b6d9-b28a6720a754\",\n", "    \"ea3a8051-502a-4023-85bb-5b32afee658b\",\n", "    \"7605dacb-6a80-4b79-bfdd-7b9992d39801\",\n", "]\n", "\n", "\n", "query = f\"\"\"\n", "WITH\n", "  request AS (\n", "  SELECT\n", "    request_id,\n", "    raw_proto AS request_proto,\n", "    time AS request_timestamp\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_host_request'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN ({','.join([f'\"{id}\"' for id in request_ids])}) ),\n", "  response AS (\n", "  SELECT\n", "    request_id,\n", "    ARRAY_AGG(raw_proto) AS response_proto,\n", "    ARRAY_AGG(time) AS response_timestamp\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_host_response'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN ({','.join([f'\"{id}\"' for id in request_ids])})\n", "  GROUP BY\n", "    request_id ),\n", "  resolution AS (\n", "  SELECT\n", "    request_id,\n", "    accepted_idx >=0 AS accepted,\n", "    time AS resolution_timestamp,\n", "    raw_proto AS resolution_proto\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_resolution'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN ({','.join([f'\"{id}\"' for id in request_ids])}) ),\n", "  feedback AS (\n", "  SELECT\n", "    request_id,\n", "    MAX_BY(raw_proto, time) AS feedback_proto,\n", "    MAX(time) AS feedback_timestamp,\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.completion_event`\n", "  WHERE\n", "    event_type = 'completion_feedback'\n", "    AND tenant = 'dogfood'\n", "    AND request_id IN ({','.join([f'\"{id}\"' for id in request_ids])})\n", "  GROUP BY\n", "    request_id )\n", "SELECT\n", "  request.request_id,\n", "  request.request_proto,\n", "  request.request_timestamp,\n", "  response.response_proto,\n", "  response.response_timestamp,\n", "  resolution.accepted,\n", "  resolution.resolution_timestamp,\n", "  feedback.feedback_proto,\n", "  feedback.feedback_timestamp\n", "FROM\n", "  request\n", "JOIN\n", "  response\n", "USING\n", "  (request_id)\n", "LEFT JOIN\n", "  resolution\n", "USING\n", "  (request_id)\n", "LEFT JOIN\n", "  feedback\n", "USING\n", "  (request_id)\n", "WHERE\n", "  TRUE\n", "\"\"\"\n", "\n", "\n", "# Which tenant to grab data from.\n", "TENANT_NAME = \"dogfood\"\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "\n", "dataset = completion_dataset._create_from_query(\n", "    query, bigquery.QueryJobConfig(), tenant\n", ")\n", "print(dataset.num_rows)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-05-16 17:16:47\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 43 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 43 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 43 keys (total size 3096).\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 3096 units to insert 43 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 43 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 43 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 43 keys (total size 533416).\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 533416 units to insert 43 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:16:48\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "43\n"]}], "source": ["from base.datasets.itertools import batched\n", "from base.datasets.pipeline import Pipeline\n", "from services.request_insight import request_insight_pb2\n", "from base.datasets import completion_dataset, tenants\n", "\n", "\n", "def process_batch(batch: Iterable[completion_dataset._Row]):\n", "    # === Parsing Request ===\n", "    request_pbs = [\n", "        request_insight_pb2.CompletionHostRequest.FromString(row[\"request_proto\"])\n", "        for row in batch\n", "    ]\n", "    requests = [\n", "        dataset._build_completion_request(request_pb, row[\"request_timestamp\"])\n", "        for row, request_pb in zip(batch, request_pbs)\n", "    ]\n", "    # Reconstruct after resolving, since we will remove the current\n", "    # blob_name from blob_names.\n", "    requests = dataset._resolve_checkpoints(requests, request_pbs)\n", "    if dataset._with_reconstructed_files:\n", "        requests = dataset._reconstruct_files(requests)\n", "\n", "    # === Parsing Response ===\n", "    responses = []\n", "    for row, request_pb in zip(batch, request_pbs):\n", "        response_ptoto = request_insight_pb2.CompletionHostResponse()\n", "        for serilzed in row[\"response_proto\"]:\n", "            cache = request_insight_pb2.CompletionHostResponse.FromString(serilzed)\n", "            response_ptoto.MergeFrom(cache)\n", "        responses.append(\n", "            dataset._build_completion_response(\n", "                response_ptoto,\n", "                [],\n", "                request_pb,\n", "                row[\"response_timestamp\"],\n", "            )\n", "        )\n", "\n", "    # === Parsing Resolution ===\n", "    resolutions = [\n", "        (\n", "            completion.CompletionResolution(\n", "                accepted=row[\"accepted\"],\n", "                timestamp=row[\"resolution_timestamp\"],\n", "            )\n", "            if \"accepted\" in row.keys()\n", "            and row[\"accepted\"] is not None\n", "            and \"resolution_timestamp\" in row.keys()\n", "            and row[\"resolution_timestamp\"] is not None\n", "            else None\n", "        )\n", "        for row in batch\n", "    ]\n", "\n", "    # === Pa<PERSON> Feedback ===\n", "    feedback = [\n", "        (\n", "            dataset._build_completion_feedback(\n", "                request_insight_pb2.CompletionFeedback.FromString(\n", "                    row[\"feedback_proto\"]\n", "                ),\n", "                row[\"feedback_timestamp\"],\n", "            )\n", "            if \"feedback_proto\" in row.keys()\n", "            and row[\"feedback_proto\"] is not None\n", "            and \"feedback_timestamp\" in row.keys()\n", "            and row[\"feedback_timestamp\"] is not None\n", "            else None\n", "        )\n", "        for row in batch\n", "    ]\n", "\n", "    return [\n", "        completion_dataset.CompletionDatum(\n", "            row[\"request_id\"],\n", "            \"\",\n", "            request,\n", "            response,\n", "            resolution,\n", "            feedback_,\n", "        )\n", "        for row, request, response, resolution, feedback_ in zip(\n", "            batch, requests, responses, resolutions, feedback\n", "        )\n", "        if request and response\n", "    ]\n", "\n", "\n", "def _dataset_to_completions(dataset):\n", "    pipeline = (\n", "        Pipeline.from_source(batched(dataset._rows, dataset._page_size))\n", "        .and_then(process_batch)\n", "        .run(max_queue_size=dataset._queue_size)\n", "    )\n", "    for batch in pipeline:\n", "        yield from batch\n", "\n", "\n", "completions = list(_dataset_to_completions(dataset))\n", "print(len(completions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download Requests from new completions"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:base.datasets.completion_dataset:Querying BigQuery: \n", "        WITH\n", "            request AS (\n", "                SELECT\n", "                    request_id,\n", "                    raw_json,\n", "                    time\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'completion_host_request'\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "            ),\n", "            response AS (\n", "                SELECT\n", "                    request_id,\n", "                    raw_json,\n", "                    time\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'completion_host_response'\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "AND JSON_EXTRACT(raw_json, '$.embeddings_prompt') is NULL\n", "AND JSON_EXTRACT(raw_json, '$.retrieved_chunks') is NULL\n", "AND CHAR_LENGTH(JSON_VALUE(raw_json, '$.text')) >= @min_completion_length\n", "            ),\n", "            retrieval AS (\n", "                SELECT\n", "                    request_id,\n", "                    ARRAY_AGG(raw_json) as raw_jsons,\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'retrieval_response'\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "                GROUP BY request_id\n", "            ),\n", "            resolution AS (\n", "                SELECT\n", "                    request_id,\n", "                    raw_json,\n", "                    time,\n", "                    accepted_idx >= 0 AS accepted,\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'completion_resolution'\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "            ),\n", "            feedback AS (\n", "                SELECT\n", "                    request_id,\n", "                    MAX_BY(raw_json, time) AS raw_json,\n", "                    MAX(time) as time,\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'completion_feedback'\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "                GROUP BY request_id\n", "            ),\n", "            metadata AS (\n", "                SELECT\n", "                    request_id,\n", "                    JSON_VALUE(raw_json, \"$.user_id\") AS user_id\n", "                FROM `staging_request_insight_full_export_dataset.request_event`\n", "                WHERE event_type = 'request_metadata'\n", "                AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'AugmentHealthCheck')\n", "                AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'augment_review_bot')\n", "                AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'Augment-EvalHarness')\n", "                AND tenant = @tenant\n", "AND request_id IN UNNEST(@request_ids)\n", "            )\n", "        SELECT\n", "            request.request_id,\n", "            metadata.user_id,\n", "            request.raw_json as request_json,\n", "            request.time as request_timestamp,\n", "            response.raw_json as response_json,\n", "            response.time as response_timestamp,\n", "            retrieval.raw_jsons as retrieval_jsons,\n", "            resolution.accepted,\n", "            resolution.time as resolution_timestamp,\n", "            feedback.raw_json as feedback_json,\n", "            feedback.time as feedback_timestamp\n", "        FROM request\n", "        JOIN response USING (request_id)\n", "        JOIN metadata USING (request_id)\n", "        LEFT JOIN retrieval USING (request_id)\n", "        LEFT JOIN resolution USING (request_id)\n", "        LEFT JOIN feedback USING (request_id)\n", "        WHERE TRUE\n", "        \n", "        ORDER BY request.request_id\n", "        LIMIT @limit\n", "        \n", "INFO:base.datasets.completion_dataset:Found 8 rows\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-07-12 17:09:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 6 missing entries.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 6 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 6 keys (total size 3928584).\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 3928584 units to insert 6 entries.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 7 missing entries.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 7 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 7 keys (total size 59888).\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 59888 units to insert 7 entries.\u001b[0m\n", "\u001b[2m2024-07-12 17:09:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "8\n"]}], "source": ["import re\n", "import logging\n", "from collections.abc import Iterable\n", "\n", "from base.datasets import tenants, completion_dataset\n", "\n", "TENANT_NAME = \"dogfood\"\n", "DATASET_FILTERS = completion_dataset.CompletionDataset.Filters(\n", "    request_ids=[\n", "        \"302845c8-9ce1-4e3a-9d6e-e2c748592991\",\n", "        \"bcc92d5b-6b8b-4fd5-b538-7af78e43ce2f\",\n", "        \"b79c3c42-23df-4879-bb88-443e2a71f60b\",\n", "        \"81c1cab1-65a1-482b-9f88-d705e70b5018\",\n", "        \"10603d5e-7bdc-47cd-a4ea-4b0667bee238\",\n", "        \"53ac398d-b77d-440f-a710-470e81b92a2f\",\n", "        \"7ee23e95-75ca-4f40-b6d9-b28a6720a754\",\n", "        \"ea3a8051-502a-4023-85bb-5b32afee658b\",\n", "        \"7605dacb-6a80-4b79-bfdd-7b9992d39801\",\n", "    ]\n", ")\n", "dataset = completion_dataset.CompletionDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=DATASET_FILTERS,\n", "    order_by=\"request_id\",\n", "    limit=100,\n", ")\n", "# completions = list(\n", "#     c for c in dataset.get_completions() if \"roguesl-farpref-16B\" in c.response.model\n", "# )\n", "completions = list(dataset.get_completions())\n", "print(len(completions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download Random Requests"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# TODO"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads Hydra dataset."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n", "INFO:datasets:PyTorch version 2.1.0+cu121py311stripe available.\n"]}], "source": ["from research.eval.harness.tasks.hydra_task import download_hydra\n", "from research.retrieval import chunking_functions\n", "import tqdm\n", "import numpy as np\n", "\n", "(\n", "    _,\n", "    _,\n", "    files_for_retrieval_by_repo,\n", ") = download_hydra(\"cceval\", \"/mnt/efs/augment/data/eval/cceval\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["58"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(files_for_retrieval_by_repo[(\"limebrew-org\", \"xpense-service\")])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'HELP.md\\ntarget/\\n!.mvn/wrapper/maven-wrapper.jar\\n!**/src/main/**/target/\\n!**/src/test/**/target/\\n\\n### STS ###\\n.apt_generated\\n.classpath\\n.factorypath\\n.project\\n.settings\\n.springBeans\\n.sts4-cache\\n\\n### IntelliJ IDEA ###\\n.idea\\n*.iws\\n*.iml\\n*.ipr\\n\\n### NetBeans ###\\n/nbproject/private/\\n/nbbuild/\\n/dist/\\n/nbdist/\\n/.nb-gradle/\\nbuild/\\n!**/src/main/**/build/\\n!**/src/test/**/build/\\n\\n### VS Code ###\\n.vscode/\\n\\n### Custom\\nbuild.sh\\n.env\\n.env.dev\\n.env.prod\\n\\ncredentials/'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["files_for_retrieval_by_repo[(\"limebrew-org\", \"xpense-service\")][0].text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replays using Research Server/Dev Deploy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Replays BigQuery"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Uploading 0 missing blobs.\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import pathlib\n", "\n", "from base.datasets import replay_utils\n", "\n", "with open(\"/home/<USER>/.config/augment/api_token\") as f:\n", "    client = AugmentClient(\n", "        # url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "        url=\"https://dogfood.api.augmentcode.com\",\n", "        token=f.read().strip(),\n", "    )\n", "\n", "# MODEL_NAME = \"research-model\"\n", "# MODEL_NAME = \"roguesl-v2-16b-seth616-rec\"\n", "# MODEL_NAME = \"ender-16b-meth0416-4-seth6-16-1\"\n", "# MODEL_NAME = \"ender-16b-meth0416-4-seth6-16-1\"\n", "MODEL_NAME = \"eldenv3-15b\"\n", "\n", "replay_utils.ensure_blobs_exist(\n", "    client, dataset._blob_cache, completions, MODEL_NAME, upload_batch_size=5000\n", ")\n", "# client.batch_upload([UploadContent(doc.text, str(doc.path)) for doc in files_for_retrieval_by_repo[('limebrew-org', 'xpense-service')]])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unknown blob names: 1463\n", "Nonindexed blob names: 0\n"]}], "source": ["replay_utils.index_status(client, completions, MODEL_NAME)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0 of 43) Replaying request 0272b5ff-69e5-4034-a31d-b430756f3f4f\n", "--> Replayed as 07fa6263-5d8b-462e-b6a4-75e88eecaee2\n", "(1 of 43) Replaying request 200e17a6-d2b2-4128-8856-a7a034fd135f\n", "--> Replayed as fa24dfa0-80d3-4095-8372-4abde94e84ce\n", "(2 of 43) Replaying request 3456d0ed-1abc-42a0-837d-4cf4be2db30b\n", "--> Replayed as 134de868-dc74-4ce9-9738-81af5e756d11\n", "(3 of 43) Replaying request 8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\n", "--> Replayed as 25b9e8b4-33a4-4be3-8600-a10dc67cf157\n", "(4 of 43) Replaying request 252c43eb-664c-4c07-8b05-97dc013f45de\n", "--> Replayed as 87fbe125-d379-40c1-ac4d-ccf66af00949\n", "(5 of 43) Replaying request 79484d9e-62cf-4671-a452-5fea63b10196\n", "--> Replayed as 217c355f-16b8-41e3-a3b3-8783d7f003eb\n", "(6 of 43) Replaying request 84e6ffca-44de-41eb-8bce-c8d460308c7c\n", "--> Replayed as 0afe5deb-f38f-4b3e-9561-0f7787e628e0\n", "(7 of 43) Replaying request 341aefb7-45b7-4b72-b000-b9f166d3f95f\n", "--> Replayed as 0b9d3e17-6ab0-4ea9-b9cd-f5f32a6a9e71\n", "(8 of 43) Replaying request 438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\n", "--> Replayed as d50e0ca0-79db-4bfb-9e93-177a732b82a2\n", "(9 of 43) Replaying request f59e877b-df59-4812-8ce1-b5c4e4686b25\n", "--> Replayed as 019a2d4f-6e19-4c9f-8a3b-cac2c2c1fb73\n", "(10 of 43) Replaying request 5bb9f026-9c2a-4681-b1df-34ba6e7b6407\n", "--> Replayed as ca0dfe9b-6ce7-4cde-8f68-adad7d3adf51\n", "(11 of 43) Replaying request c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\n", "--> Replayed as aef5940d-8625-4088-8c90-a9a4be31e74e\n", "(12 of 43) Replaying request ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\n", "--> Replayed as 913fb771-5565-424a-8b8e-254ab8d8065e\n", "(13 of 43) Replaying request 37b49998-03f4-441f-b584-982aae11fcfb\n", "--> Replayed as b89d0a9c-6b3e-433a-bedd-0f6f0f129b34\n", "(14 of 43) Replaying request 38a87727-f608-486e-baa0-d580f06a4701\n", "--> Replayed as 59d300ac-df80-4d08-99f1-11c85e4fdf23\n", "(15 of 43) Replaying request c2f63804-efed-4711-970b-8b9032476758\n", "--> Replayed as d59c7d30-73cb-4987-96cc-ed87fcb97b83\n", "(16 of 43) Replaying request 19d3d1b0-0509-4074-bab0-140e12356350\n", "--> Replayed as 30c4db4f-f3ec-4513-b8e7-da4a3707db40\n", "(17 of 43) Replaying request 7e6d997c-3d6e-4493-a3a2-13983fbb571f\n", "--> Replayed as a85ef85f-d9cb-49f1-857c-7b04c265b450\n", "(18 of 43) Replaying request 5f9abc1e-bbdb-465f-8ee4-91c5ed178958\n", "--> Replayed as 813422c8-2ce7-4b5c-8a33-a6abfe499cc2\n", "(19 of 43) Replaying request d454140e-dd4b-4cf8-bd19-3a61d85e9163\n", "--> Replayed as 9d61c772-47aa-49ce-9b0b-9ca9ac0d14ae\n", "(20 of 43) Replaying request 126fbf6e-a739-4ad1-9942-af8da6af17f0\n", "--> Replayed as a0d488a3-412a-47d2-8a66-d4f3cfeccec0\n", "(21 of 43) Replaying request b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\n", "--> Replayed as f58b7f89-b2f7-448e-b9e4-98839b5ae8b7\n", "(22 of 43) Replaying request b251b2bf-eed4-475b-b969-cf6807aab4df\n", "--> Replayed as eb81588c-e277-48d6-892a-740d08e51f60\n", "(23 of 43) Replaying request b535a8bc-cbda-45da-b7fc-1b9976980137\n", "--> Replayed as fbf1c162-3657-4f76-b819-fe83520ed6df\n", "(24 of 43) Replaying request 3f773136-5967-4c96-ba75-9ed692d44a77\n", "--> Replayed as 0bcd7830-af89-4899-a889-4da84a297681\n", "(25 of 43) Replaying request 488a7a15-da79-4dae-802b-a449cd63cfab\n", "--> Replayed as 382a7655-1dd0-4368-af41-781e5cb72c12\n", "(26 of 43) Replaying request 85f9c626-3b59-41bc-8c2e-a73bed1d81d6\n", "--> Replayed as c73aa467-ce40-40c5-b236-684484ee5563\n", "(27 of 43) Replaying request 2a53b956-82e5-41b5-97b0-4e07ce2b9052\n", "--> Replayed as 5447402f-876e-45d2-be83-8aea3efcddb6\n", "(28 of 43) Replaying request dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\n", "--> Replayed as 5aa1fcf6-ca12-42da-b4e4-4828294d7724\n", "(29 of 43) Replaying request 64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\n", "--> Replayed as 52b0350e-ad2a-427e-be54-fe8ffaa38736\n", "(30 of 43) Replaying request 9f6469a2-65f3-486c-a58e-d34c598087b6\n", "--> Replayed as 27bdf808-0053-46c9-9303-63f422843f16\n", "(31 of 43) Replaying request d46dc24a-f904-41b8-a2bb-d52545513e96\n", "--> Replayed as 2d2e9036-a58b-44b0-b2e7-71596a44eb96\n", "(32 of 43) Replaying request 38315ad8-b8f3-4c6a-84e5-afa5933f38ba\n", "--> Replayed as e39d763b-80d3-4aba-adaf-a7fafa63992c\n", "(33 of 43) Replaying request 669c8097-7324-4af9-afde-8098b6de1fdc\n", "--> Replayed as 66d9e38b-cfe5-4b06-8202-98d0a264d34d\n", "(34 of 43) Replaying request 57d60d7d-c2ce-4755-8a7b-4a3d01214859\n", "--> Replayed as 4d919bd9-f163-41af-b3a3-42b332c29f1e\n", "(35 of 43) Replaying request ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\n", "--> Replayed as 79a00b65-3f56-4f58-901f-64a51838a055\n", "(36 of 43) Replaying request 86dff485-1174-4bc3-a9d4-5dec6d8be5d2\n", "--> Replayed as 1c1f3ffe-a8a4-4a63-9e4d-478f8f07e08a\n", "(37 of 43) Replaying request 2c9f54cb-fd41-4adc-8fc5-59074916c06d\n", "--> Replayed as b2c5d5d9-222f-4ee5-8de3-22d4664adb60\n", "(38 of 43) Replaying request e0a30509-89b0-4d1c-ae83-83077ff1eb24\n", "--> Replayed as cd546367-f2c8-4e68-b953-f2c06a43b228\n", "(39 of 43) Replaying request 4813df91-f7b9-4e69-9a07-0547b84ccb43\n", "--> Replayed as f5d0362b-09b3-4972-bcd5-e26a4824ab94\n", "(40 of 43) Replaying request 73ad3121-7aec-4dea-8542-1e2f3d2e1b59\n", "--> Replayed as a75d9612-63d3-4f4f-a5c6-852cc7082eb9\n", "(41 of 43) Replaying request 58512ff9-45ca-4f45-8446-be352e2c3bdb\n", "--> Replayed as 735d25f7-57ea-4a9e-90c1-481a4c9f02c2\n", "(42 of 43) Replaying request 73e95698-12d1-49cc-b5a0-6179dca2acde\n", "--> Replayed as 191085c4-8592-4c31-8261-53fd0a778f59\n"]}], "source": ["replayed_responses = replay_utils.replay(client, MODEL_NAME, completions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Replay Hydra"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|                                                                                                                                                                                                                                                                                                                     | 0/986 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 986/986 [07:43<00:00,  2.13it/s]\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import collections\n", "\n", "import more_itertools\n", "\n", "from base.augment_client.client import ClientException\n", "from base.datasets import replay_utils\n", "\n", "list(more_itertools.batched(range(100), 9))\n", "\n", "with open(\"/home/<USER>/.config/augment/api_token\") as f:\n", "    client = AugmentClient(\n", "        url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "        # url=\"https://dogfood.api.augmentcode.com\",\n", "        token=f.read().strip(),\n", "    )\n", "\n", "# MODEL_NAME = \"research-model\"\n", "# MODEL_NAME = \"roguesl-v2-16b-seth616-rec\"\n", "MODEL_NAME = \"ender-16b-meth0416-4-seth6-16-1\"\n", "\n", "# replay_utils.ensure_blobs_exist(client, dataset, completions, MODEL_NAME, upload_batch_size=5000)\n", "all_paths = {}\n", "all_missing = collections.defaultdict(list)\n", "\n", "\n", "for key, docs in tqdm.tqdm(files_for_retrieval_by_repo.items()):\n", "    # key = ('czczup', 'GPTrans')\n", "    # docs = files_for_retrieval_by_repo[key]\n", "    # print(key, len(docs), flush=True)\n", "    time.sleep(0.1)\n", "    # for batch in more_itertools.batched(docs, 50):\n", "    #     try:\n", "    #         client.batch_upload([UploadContent(doc.text, str(doc.path)) for doc in batch])\n", "    #     except ClientException:\n", "    #         print(\"Failed to upload batch\")\n", "    # for _ in range(12):\n", "    #     time.sleep(5)\n", "    #     rst = client.find_missing(MODEL_NAME, [doc.id for doc in docs])\n", "    #     if not rst.nonindexed_blob_names:\n", "    #         break\n", "    for batch in more_itertools.batched(docs, 1000):\n", "        if missing := client.find_missing(\n", "            MODEL_NAME, [doc.id for doc in batch]\n", "        ).nonindexed_blob_names:\n", "            all_missing[key].extend(missing)\n", "            break"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(list,\n", "            {('dmlc',\n", "              'tl2cgen'): ['213692b6caa73d30b8ee5cf704ecb7487242e5c61f5a1649ce0784815ad6b65a']})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["all_missing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Debug missing blobs"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(name='ender-16b-meth0416-4-seth6-16-1', suggested_prefix_char_count=5760, suggested_suffix_char_count=5760, max_memorize_size_bytes=131072)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.systems.remote_lib import (\n", "    RemoteRetriever,\n", "    RemoteRetrieverConfig,\n", "    get_model_info,\n", ")\n", "\n", "remote_retriever = RemoteRetriever(\n", "    client,\n", "    get_model_info(client.get_models(), MODEL_NAME),\n", "    client.get_models().languages,\n", "    RemoteRetrieverConfig(),\n", ")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ender-16b-meth0416-4-seth6-16-1'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["remote_retriever.model_info"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'str' object has no attribute 'name'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb Cell 23\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m remote_retriever\u001b[39m.\u001b[39;49m_wait_for_indexing(\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     [\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39md9ecd1d0a3f70320bb76028e5cd22f99c34a553aae846ae7f5f16504f3053001\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m9165f6edb7f2b3022a202808b22dca0c2c5d536c53f14e4498bbf56f818b0ba6\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m49782975ed15299fdeb03544c1ff4da64d8dc722bad176511c4b9b456b325bcc\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39me2de3da0ec9a83caf778169afad5a1133a7f483e93336df47cefabef2cc92759\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mc99830b94cdeb427975531afeaec615025e1f4af7e90acb8e6dac59458b66242\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39ma40a1c8a882a851ea9a4469b551429c7562f15d06ad044469b724bb91c1a0cae\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39md30fabb09d87e93ca30149a62cf392bef809285a684ab808cbbb9a94085c54ce\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m2238ef52f5a4513d8295dfb4f470f89b7c141e07c97d03d365d2a21ae563c9d9\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=10'>11</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m4ebe543d878186f559e27baac2d2b79d8159af7a69c553f89b9fa39263e12ccf\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=11'>12</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m4a26e3d805f8fcda50e5cab4ab2422a13b75c17d517d329e3364f46e5e4fe24b\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=12'>13</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m76da6ab8ca2273787a7193cf60e71b5b8ab0eca57037ef1b963aa7717629f582\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=13'>14</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m4b7e94adda226e362eb9badfe7309792715278db929d2cf3762331b822b95784\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=14'>15</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m6131d454f29be92d97959c7611e04de7c21a021baee196bad5b796fb550c78cd\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m1cca12293d019f3594ebbff5b0bddbe5289352f267a76ff0add1537e602ccc00\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m498bc115d349ac285942cb8abb21ab0849e3a1caa002fb11232f94f6d5d1691a\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=17'>18</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m9c4c27bb28a9499e4f1b665177e81ad862ec9f6aa4191d3d98ca6795cb53ba92\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m60f71383eb9ca0a1b7f06b8174b3f63e67673aa8826f0fa5f4b0d11b155cb35b\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39me0cab607574219ddef999f9bec6dbbabb250b1205944a514320aa0ba18c7aeae\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=20'>21</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39ma7a05f543d627455257ca3d9c6c402cd8f8b72dca7e32d7fcf1239a9609c7cc5\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=21'>22</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m480f74c2324d4ad10d2c8896fd5662a1501ab3747843d02c2f63a95bdeabf36a\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=22'>23</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mb73cb25b6028d0f05165cfdf7112233bd54c7ff066c05c42838da65f384acdbd\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=23'>24</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m58674f5f912077e07cbac56bd448e301b873100f2a0d6e829ded9021d234a001\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=24'>25</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m66be86c78bff5cd3dd4c5e29ee92705daf16e6ba23eee515f6aa532d7e255c6c\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=25'>26</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mff9c6c8b6c73667f98bb5c67604b170ccf5ca154747e5c0773c378ee0224d0d5\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=26'>27</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m2b588493ab62f6b1cf3f1215a980a7cddc7c170ecf4555d9cce2209ea4306946\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39m3a226458ac397a444e56ac960202f5b19bd41604d216b8d1a624e2a03f9f3d5d\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mfd5adec096e79264c3d8acb0a11ac15bc8dd8fa7d319dca9a0dab83ff7b713cd\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m     ]\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#Y104sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m )\n", "File \u001b[0;32m~/augment/research/eval/harness/systems/remote_lib.py:310\u001b[0m, in \u001b[0;36mRemoteRetriever._wait_for_indexing\u001b[0;34m(self, blob_names)\u001b[0m\n\u001b[1;32m    307\u001b[0m \u001b[39mfor\u001b[39;00m blob_name_batch \u001b[39min\u001b[39;00m batched(blob_names, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mconfig\u001b[39m.\u001b[39mmax_find_missing_items):\n\u001b[1;32m    308\u001b[0m     \u001b[39mfor\u001b[39;00m retry \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mconfig\u001b[39m.\u001b[39mwait_indexing_retry_count \u001b[39m+\u001b[39m \u001b[39m1\u001b[39m):\n\u001b[1;32m    309\u001b[0m         \u001b[39m# A bit awkward that find_missing requires model_name.\u001b[39;00m\n\u001b[0;32m--> 310\u001b[0m         resp \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mclient\u001b[39m.\u001b[39mfind_missing(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mmodel_info\u001b[39m.\u001b[39;49mname, blob_name_batch)\n\u001b[1;32m    311\u001b[0m         logger\u001b[39m.\u001b[39mdebug(\n\u001b[1;32m    312\u001b[0m             \u001b[39m\"\u001b[39m\u001b[39mcalled find_missing on \u001b[39m\u001b[39m%d\u001b[39;00m\u001b[39m blob names, response: \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m\"\u001b[39m,\n\u001b[1;32m    313\u001b[0m             \u001b[39mlen\u001b[39m(blob_name_batch),\n\u001b[1;32m    314\u001b[0m             resp,\n\u001b[1;32m    315\u001b[0m         )\n\u001b[1;32m    317\u001b[0m         \u001b[39mif\u001b[39;00m resp\u001b[39m.\u001b[39munknown_memory_names:\n", "\u001b[0;31mAttributeError\u001b[0m: 'str' object has no attribute 'name'"]}], "source": ["remote_retriever._wait_for_indexing(\n", "    [\n", "        \"d9ecd1d0a3f70320bb76028e5cd22f99c34a553aae846ae7f5f16504f3053001\",\n", "        \"9165f6edb7f2b3022a202808b22dca0c2c5d536c53f14e4498bbf56f818b0ba6\",\n", "        \"49782975ed15299fdeb03544c1ff4da64d8dc722bad176511c4b9b456b325bcc\",\n", "        \"e2de3da0ec9a83caf778169afad5a1133a7f483e93336df47cefabef2cc92759\",\n", "        \"c99830b94cdeb427975531afeaec615025e1f4af7e90acb8e6dac59458b66242\",\n", "        \"a40a1c8a882a851ea9a4469b551429c7562f15d06ad044469b724bb91c1a0cae\",\n", "        \"d30fabb09d87e93ca30149a62cf392bef809285a684ab808cbbb9a94085c54ce\",\n", "        \"2238ef52f5a4513d8295dfb4f470f89b7c141e07c97d03d365d2a21ae563c9d9\",\n", "        \"4ebe543d878186f559e27baac2d2b79d8159af7a69c553f89b9fa39263e12ccf\",\n", "        \"4a26e3d805f8fcda50e5cab4ab2422a13b75c17d517d329e3364f46e5e4fe24b\",\n", "        \"76da6ab8ca2273787a7193cf60e71b5b8ab0eca57037ef1b963aa7717629f582\",\n", "        \"4b7e94adda226e362eb9badfe7309792715278db929d2cf3762331b822b95784\",\n", "        \"6131d454f29be92d97959c7611e04de7c21a021baee196bad5b796fb550c78cd\",\n", "        \"1cca12293d019f3594ebbff5b0bddbe5289352f267a76ff0add1537e602ccc00\",\n", "        \"498bc115d349ac285942cb8abb21ab0849e3a1caa002fb11232f94f6d5d1691a\",\n", "        \"9c4c27bb28a9499e4f1b665177e81ad862ec9f6aa4191d3d98ca6795cb53ba92\",\n", "        \"60f71383eb9ca0a1b7f06b8174b3f63e67673aa8826f0fa5f4b0d11b155cb35b\",\n", "        \"e0cab607574219ddef999f9bec6dbbabb250b1205944a514320aa0ba18c7aeae\",\n", "        \"a7a05f543d627455257ca3d9c6c402cd8f8b72dca7e32d7fcf1239a9609c7cc5\",\n", "        \"480f74c2324d4ad10d2c8896fd5662a1501ab3747843d02c2f63a95bdeabf36a\",\n", "        \"b73cb25b6028d0f05165cfdf7112233bd54c7ff066c05c42838da65f384acdbd\",\n", "        \"58674f5f912077e07cbac56bd448e301b873100f2a0d6e829ded9021d234a001\",\n", "        \"66be86c78bff5cd3dd4c5e29ee92705daf16e6ba23eee515f6aa532d7e255c6c\",\n", "        \"ff9c6c8b6c73667f98bb5c67604b170ccf5ca154747e5c0773c378ee0224d0d5\",\n", "        \"2b588493ab62f6b1cf3f1215a980a7cddc7c170ecf4555d9cce2209ea4306946\",\n", "        \"3a226458ac397a444e56ac960202f5b19bd41604d216b8d1a624e2a03f9f3d5d\",\n", "        \"fd5adec096e79264c3d8acb0a11ac15bc8dd8fa7d319dca9a0dab83ff7b713cd\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["CompleteResponse(text='', completion_items=[CompletionItem(text='', skipped_suffix='', suffix_replacement_text='')], unknown_memory_names=[], request_id=UUID('28fadc8e-1cd6-45fb-a8bc-24b6c115c06f'), checkpoint_not_found=False, suggested_prefix_char_count=5760, suggested_suffix_char_count=5760)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.augment_client.client import BlobsJson\n", "\n", "client.client_for_model(MODEL_NAME).complete(\n", "    prompt=\"hello\",\n", "    suffix=\"\",\n", "    path=\"hello.py\",\n", "    max_tokens=100,\n", "    memory_object_names=[],\n", "    blobs=BlobsJson(\n", "        checkpoint_id=None,\n", "        added_blobs=[\n", "            \"d9ecd1d0a3f70320bb76028e5cd22f99c34a553aae846ae7f5f16504f3053001\",\n", "            \"9165f6edb7f2b3022a202808b22dca0c2c5d536c53f14e4498bbf56f818b0ba6\",\n", "            \"49782975ed15299fdeb03544c1ff4da64d8dc722bad176511c4b9b456b325bcc\",\n", "            \"e2de3da0ec9a83caf778169afad5a1133a7f483e93336df47cefabef2cc92759\",\n", "            \"c99830b94cdeb427975531afeaec615025e1f4af7e90acb8e6dac59458b66242\",\n", "            \"a40a1c8a882a851ea9a4469b551429c7562f15d06ad044469b724bb91c1a0cae\",\n", "            \"d30fabb09d87e93ca30149a62cf392bef809285a684ab808cbbb9a94085c54ce\",\n", "            \"2238ef52f5a4513d8295dfb4f470f89b7c141e07c97d03d365d2a21ae563c9d9\",\n", "            \"4ebe543d878186f559e27baac2d2b79d8159af7a69c553f89b9fa39263e12ccf\",\n", "            \"4a26e3d805f8fcda50e5cab4ab2422a13b75c17d517d329e3364f46e5e4fe24b\",\n", "            \"76da6ab8ca2273787a7193cf60e71b5b8ab0eca57037ef1b963aa7717629f582\",\n", "            \"4b7e94adda226e362eb9badfe7309792715278db929d2cf3762331b822b95784\",\n", "            \"6131d454f29be92d97959c7611e04de7c21a021baee196bad5b796fb550c78cd\",\n", "            \"1cca12293d019f3594ebbff5b0bddbe5289352f267a76ff0add1537e602ccc00\",\n", "            \"498bc115d349ac285942cb8abb21ab0849e3a1caa002fb11232f94f6d5d1691a\",\n", "            \"9c4c27bb28a9499e4f1b665177e81ad862ec9f6aa4191d3d98ca6795cb53ba92\",\n", "            \"60f71383eb9ca0a1b7f06b8174b3f63e67673aa8826f0fa5f4b0d11b155cb35b\",\n", "            \"e0cab607574219ddef999f9bec6dbbabb250b1205944a514320aa0ba18c7aeae\",\n", "            \"a7a05f543d627455257ca3d9c6c402cd8f8b72dca7e32d7fcf1239a9609c7cc5\",\n", "            \"480f74c2324d4ad10d2c8896fd5662a1501ab3747843d02c2f63a95bdeabf36a\",\n", "            \"b73cb25b6028d0f05165cfdf7112233bd54c7ff066c05c42838da65f384acdbd\",\n", "            \"58674f5f912077e07cbac56bd448e301b873100f2a0d6e829ded9021d234a001\",\n", "            \"66be86c78bff5cd3dd4c5e29ee92705daf16e6ba23eee515f6aa532d7e255c6c\",\n", "            \"ff9c6c8b6c73667f98bb5c67604b170ccf5ca154747e5c0773c378ee0224d0d5\",\n", "            \"2b588493ab62f6b1cf3f1215a980a7cddc7c170ecf4555d9cce2209ea4306946\",\n", "            \"3a226458ac397a444e56ac960202f5b19bd41604d216b8d1a624e2a03f9f3d5d\",\n", "            \"fd5adec096e79264c3d8acb0a11ac15bc8dd8fa7d319dca9a0dab83ff7b713cd\",\n", "        ],\n", "        deleted_blobs=[],\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "ClientException", "evalue": "(UUID('41f56938-adfe-41d4-b288-73a20692d6e1'), <Response [500]>)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mClientException\u001b[0m                           Traceback (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb Cell 20\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#X66sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m client\u001b[39m.\u001b[39;49mfind_missing(MODEL_NAME, [doc\u001b[39m.\u001b[39;49mid \u001b[39mfor\u001b[39;49;00m doc \u001b[39min\u001b[39;49;00m docs])\n", "File \u001b[0;32m~/augment/base/augment_client/client.py:307\u001b[0m, in \u001b[0;36mAugmentClient.find_missing\u001b[0;34m(self, model_name, memory_object_names)\u001b[0m\n\u001b[1;32m    305\u001b[0m logging\u001b[39m.\u001b[39mdebug(\u001b[39m\"\u001b[39m\u001b[39mFindMissing finished: \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m\"\u001b[39m, request_id, response\u001b[39m.\u001b[39mstatus_code)\n\u001b[1;32m    306\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m response\u001b[39m.\u001b[39mok:\n\u001b[0;32m--> 307\u001b[0m     \u001b[39mraise\u001b[39;00m ClientException(request_id, response)\n\u001b[1;32m    308\u001b[0m j \u001b[39m=\u001b[39m response\u001b[39m.\u001b[39mjson()\n\u001b[1;32m    309\u001b[0m \u001b[39mreturn\u001b[39;00m FindMissingResponse(\n\u001b[1;32m    310\u001b[0m     unknown_memory_names\u001b[39m=\u001b[39mj[\u001b[39m\"\u001b[39m\u001b[39munknown_memory_names\u001b[39m\u001b[39m\"\u001b[39m],\n\u001b[1;32m    311\u001b[0m     nonindexed_blob_names\u001b[39m=\u001b[39mj[\u001b[39m\"\u001b[39m\u001b[39mnonindexed_blob_names\u001b[39m\u001b[39m\"\u001b[39m],\n\u001b[1;32m    312\u001b[0m )\n", "\u001b[0;31mClientException\u001b[0m: (UUID('41f56938-adfe-41d4-b288-73a20692d6e1'), <Response [500]>)"]}], "source": ["client.find_missing(MODEL_NAME, [doc.id for doc in docs])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{('dmlc',\n", "  'tl2cgen'): ['213692b6caa73d30b8ee5cf704ecb7487242e5c61f5a1649ce0784815ad6b65a']}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["all_missing"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['b4865246f6c5daccbd813ebec856f787098e80cd79bd4d2860d76a0d0d000a22',\n", " '632f4f76fd1377c8b26bebbfd9feead5fbb4168236018dd0ac3883a7ea3372c2',\n", " '58a7f70d3d84d1bde867f065593f6b658d90facbedef43edf69040763db3e61b',\n", " '9968b839b5bda82bc7776c5e67f6b789b7052c0fd4c838bea73612cf3ee06dfb',\n", " '26c93f7a33fe6147f3e53abc6359a5866a9ed4644c413e6e69d250a7fdaab2b4',\n", " '150dd13551beb616b2b8ec58eaf21ecfea9e0b56a445bbbf73a5b80e36dabc2d',\n", " '04b4457ca7a1d1a9d6d3a2bc319fb92005998cab627358cbbd20e501b928f358',\n", " '65d14a2ddd0eca0ef5b1eee5d55c2f8855322363389b1c7763afd58feb956452',\n", " '47be17542aa70ea5fe0ffe6293da904cb86747db3ea2ce4f080c8619f46d27ba',\n", " '213692b6caa73d30b8ee5cf704ecb7487242e5c61f5a1649ce0784815ad6b65a',\n", " '06234fa0bfc50c765890d751ef31ff607437d0fcc073b6e83ca2129aaaaa765a',\n", " 'f17d01b51da661fb802a82e5fc5147181381ff7cd8f73829f25f91225930e485',\n", " 'e801dda686122ddb8b42fd37f578b08f6774a3e52a893759d56240314c56b1d2',\n", " '9b3a466d990a0983670a44f29250df6e212383509db8361fe5d5bc6cb56a5545']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["client.find_missing(MODEL_NAME, [doc.id for doc in docs]).nonindexed_blob_names"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['b4865246f6c5daccbd813ebec856f787098e80cd79bd4d2860d76a0d0d000a22',\n", " '632f4f76fd1377c8b26bebbfd9feead5fbb4168236018dd0ac3883a7ea3372c2',\n", " '58a7f70d3d84d1bde867f065593f6b658d90facbedef43edf69040763db3e61b',\n", " '9968b839b5bda82bc7776c5e67f6b789b7052c0fd4c838bea73612cf3ee06dfb',\n", " '26c93f7a33fe6147f3e53abc6359a5866a9ed4644c413e6e69d250a7fdaab2b4',\n", " '04b4457ca7a1d1a9d6d3a2bc319fb92005998cab627358cbbd20e501b928f358',\n", " '150dd13551beb616b2b8ec58eaf21ecfea9e0b56a445bbbf73a5b80e36dabc2d',\n", " '65d14a2ddd0eca0ef5b1eee5d55c2f8855322363389b1c7763afd58feb956452',\n", " '47be17542aa70ea5fe0ffe6293da904cb86747db3ea2ce4f080c8619f46d27ba',\n", " '213692b6caa73d30b8ee5cf704ecb7487242e5c61f5a1649ce0784815ad6b65a',\n", " '06234fa0bfc50c765890d751ef31ff607437d0fcc073b6e83ca2129aaaaa765a',\n", " 'f17d01b51da661fb802a82e5fc5147181381ff7cd8f73829f25f91225930e485',\n", " 'e801dda686122ddb8b42fd37f578b08f6774a3e52a893759d56240314c56b1d2',\n", " '9b3a466d990a0983670a44f29250df6e212383509db8361fe5d5bc6cb56a5545']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["client.find_missing(MODEL_NAME, [doc.id for doc in docs]).nonindexed_blob_names"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replays using System."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'ender_fastforward', 'model_path': 'ender/16b_multieth64m_addrust_noinline_sigretprefsuf', 'prompt': {'component_order': ['signature', 'retrieval', 'prefix', 'suffix'], 'max_prefix_tokens': 1280, 'max_prompt_tokens': 5120, 'max_retrieved_chunk_tokens': -1, 'max_signature_tokens': 1024, 'max_suffix_tokens': 768}}\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/ender/16b_multieth64m_addrust_noinline_sigretprefsuf/config.yml')]\n", "Loading the system (fim_gen_mode=evaluation)...\n", "Created RoundAttention with stable_id ec26966a-8d9b-433b-909b-9413febd701b.\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/config.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building StarCoderTokenizer tokenizer ...\n", " > padded vocab (size: 49176) with 2024 dummy tokens (new size: 51200)\n", "> initializing torch distributed ...\n", "[2024-05-19 06:10:32,851] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "[vzhao-dev-a100:3876135] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024-05-19 06:10:33,908] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=10.144.230.32, master_port=6000\n", "[2024-05-19 06:10:33,908] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2024-05-19 06:10:33,910] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2024-05-19 06:10:33,933] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=29\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: _post_transformer_block\n", "    27: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    28: ContrastiveRetrievalHead\n", "  loss: contrastive_loss\n", "Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    0.position_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.key_value.weight requires_grad=True\n", "    2.attention.key_value.bias requires_grad=True\n", "    2.attention.query.weight requires_grad=True\n", "    2.attention.query.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.post_attention_layernorm.weight requires_grad=True\n", "    2.post_attention_layernorm.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.key_value.weight requires_grad=True\n", "    3.attention.key_value.bias requires_grad=True\n", "    3.attention.query.weight requires_grad=True\n", "    3.attention.query.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.post_attention_layernorm.weight requires_grad=True\n", "    3.post_attention_layernorm.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.key_value.weight requires_grad=True\n", "    4.attention.key_value.bias requires_grad=True\n", "    4.attention.query.weight requires_grad=True\n", "    4.attention.query.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.post_attention_layernorm.weight requires_grad=True\n", "    4.post_attention_layernorm.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.key_value.weight requires_grad=True\n", "    5.attention.key_value.bias requires_grad=True\n", "    5.attention.query.weight requires_grad=True\n", "    5.attention.query.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.post_attention_layernorm.weight requires_grad=True\n", "    5.post_attention_layernorm.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.key_value.weight requires_grad=True\n", "    6.attention.key_value.bias requires_grad=True\n", "    6.attention.query.weight requires_grad=True\n", "    6.attention.query.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.post_attention_layernorm.weight requires_grad=True\n", "    6.post_attention_layernorm.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.key_value.weight requires_grad=True\n", "    7.attention.key_value.bias requires_grad=True\n", "    7.attention.query.weight requires_grad=True\n", "    7.attention.query.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.post_attention_layernorm.weight requires_grad=True\n", "    7.post_attention_layernorm.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.key_value.weight requires_grad=True\n", "    8.attention.key_value.bias requires_grad=True\n", "    8.attention.query.weight requires_grad=True\n", "    8.attention.query.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.post_attention_layernorm.weight requires_grad=True\n", "    8.post_attention_layernorm.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.key_value.weight requires_grad=True\n", "    9.attention.key_value.bias requires_grad=True\n", "    9.attention.query.weight requires_grad=True\n", "    9.attention.query.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.post_attention_layernorm.weight requires_grad=True\n", "    9.post_attention_layernorm.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.key_value.weight requires_grad=True\n", "    10.attention.key_value.bias requires_grad=True\n", "    10.attention.query.weight requires_grad=True\n", "    10.attention.query.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.post_attention_layernorm.weight requires_grad=True\n", "    10.post_attention_layernorm.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.key_value.weight requires_grad=True\n", "    11.attention.key_value.bias requires_grad=True\n", "    11.attention.query.weight requires_grad=True\n", "    11.attention.query.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.post_attention_layernorm.weight requires_grad=True\n", "    11.post_attention_layernorm.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.key_value.weight requires_grad=True\n", "    12.attention.key_value.bias requires_grad=True\n", "    12.attention.query.weight requires_grad=True\n", "    12.attention.query.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.post_attention_layernorm.weight requires_grad=True\n", "    12.post_attention_layernorm.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.key_value.weight requires_grad=True\n", "    13.attention.key_value.bias requires_grad=True\n", "    13.attention.query.weight requires_grad=True\n", "    13.attention.query.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.post_attention_layernorm.weight requires_grad=True\n", "    13.post_attention_layernorm.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    14.input_layernorm.weight requires_grad=True\n", "    14.input_layernorm.bias requires_grad=True\n", "    14.attention.key_value.weight requires_grad=True\n", "    14.attention.key_value.bias requires_grad=True\n", "    14.attention.query.weight requires_grad=True\n", "    14.attention.query.bias requires_grad=True\n", "    14.attention.dense.weight requires_grad=True\n", "    14.attention.dense.bias requires_grad=True\n", "    14.post_attention_layernorm.weight requires_grad=True\n", "    14.post_attention_layernorm.bias requires_grad=True\n", "    14.mlp.dense_h_to_4h.weight requires_grad=True\n", "    14.mlp.dense_h_to_4h.bias requires_grad=True\n", "    14.mlp.dense_4h_to_h.weight requires_grad=True\n", "    14.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.input_layernorm.weight requires_grad=True\n", "    15.input_layernorm.bias requires_grad=True\n", "    15.attention.key_value.weight requires_grad=True\n", "    15.attention.key_value.bias requires_grad=True\n", "    15.attention.query.weight requires_grad=True\n", "    15.attention.query.bias requires_grad=True\n", "    15.attention.dense.weight requires_grad=True\n", "    15.attention.dense.bias requires_grad=True\n", "    15.post_attention_layernorm.weight requires_grad=True\n", "    15.post_attention_layernorm.bias requires_grad=True\n", "    15.mlp.dense_h_to_4h.weight requires_grad=True\n", "    15.mlp.dense_h_to_4h.bias requires_grad=True\n", "    15.mlp.dense_4h_to_h.weight requires_grad=True\n", "    15.mlp.dense_4h_to_h.bias requires_grad=True\n", "    16.input_layernorm.weight requires_grad=True\n", "    16.input_layernorm.bias requires_grad=True\n", "    16.attention.key_value.weight requires_grad=True\n", "    16.attention.key_value.bias requires_grad=True\n", "    16.attention.query.weight requires_grad=True\n", "    16.attention.query.bias requires_grad=True\n", "    16.attention.dense.weight requires_grad=True\n", "    16.attention.dense.bias requires_grad=True\n", "    16.post_attention_layernorm.weight requires_grad=True\n", "    16.post_attention_layernorm.bias requires_grad=True\n", "    16.mlp.dense_h_to_4h.weight requires_grad=True\n", "    16.mlp.dense_h_to_4h.bias requires_grad=True\n", "    16.mlp.dense_4h_to_h.weight requires_grad=True\n", "    16.mlp.dense_4h_to_h.bias requires_grad=True\n", "    17.input_layernorm.weight requires_grad=True\n", "    17.input_layernorm.bias requires_grad=True\n", "    17.attention.key_value.weight requires_grad=True\n", "    17.attention.key_value.bias requires_grad=True\n", "    17.attention.query.weight requires_grad=True\n", "    17.attention.query.bias requires_grad=True\n", "    17.attention.dense.weight requires_grad=True\n", "    17.attention.dense.bias requires_grad=True\n", "    17.post_attention_layernorm.weight requires_grad=True\n", "    17.post_attention_layernorm.bias requires_grad=True\n", "    17.mlp.dense_h_to_4h.weight requires_grad=True\n", "    17.mlp.dense_h_to_4h.bias requires_grad=True\n", "    17.mlp.dense_4h_to_h.weight requires_grad=True\n", "    17.mlp.dense_4h_to_h.bias requires_grad=True\n", "    18.input_layernorm.weight requires_grad=True\n", "    18.input_layernorm.bias requires_grad=True\n", "    18.attention.key_value.weight requires_grad=True\n", "    18.attention.key_value.bias requires_grad=True\n", "    18.attention.query.weight requires_grad=True\n", "    18.attention.query.bias requires_grad=True\n", "    18.attention.dense.weight requires_grad=True\n", "    18.attention.dense.bias requires_grad=True\n", "    18.post_attention_layernorm.weight requires_grad=True\n", "    18.post_attention_layernorm.bias requires_grad=True\n", "    18.mlp.dense_h_to_4h.weight requires_grad=True\n", "    18.mlp.dense_h_to_4h.bias requires_grad=True\n", "    18.mlp.dense_4h_to_h.weight requires_grad=True\n", "    18.mlp.dense_4h_to_h.bias requires_grad=True\n", "    19.input_layernorm.weight requires_grad=True\n", "    19.input_layernorm.bias requires_grad=True\n", "    19.attention.key_value.weight requires_grad=True\n", "    19.attention.key_value.bias requires_grad=True\n", "    19.attention.query.weight requires_grad=True\n", "    19.attention.query.bias requires_grad=True\n", "    19.attention.dense.weight requires_grad=True\n", "    19.attention.dense.bias requires_grad=True\n", "    19.post_attention_layernorm.weight requires_grad=True\n", "    19.post_attention_layernorm.bias requires_grad=True\n", "    19.mlp.dense_h_to_4h.weight requires_grad=True\n", "    19.mlp.dense_h_to_4h.bias requires_grad=True\n", "    19.mlp.dense_4h_to_h.weight requires_grad=True\n", "    19.mlp.dense_4h_to_h.bias requires_grad=True\n", "    20.input_layernorm.weight requires_grad=True\n", "    20.input_layernorm.bias requires_grad=True\n", "    20.attention.key_value.weight requires_grad=True\n", "    20.attention.key_value.bias requires_grad=True\n", "    20.attention.query.weight requires_grad=True\n", "    20.attention.query.bias requires_grad=True\n", "    20.attention.dense.weight requires_grad=True\n", "    20.attention.dense.bias requires_grad=True\n", "    20.post_attention_layernorm.weight requires_grad=True\n", "    20.post_attention_layernorm.bias requires_grad=True\n", "    20.mlp.dense_h_to_4h.weight requires_grad=True\n", "    20.mlp.dense_h_to_4h.bias requires_grad=True\n", "    20.mlp.dense_4h_to_h.weight requires_grad=True\n", "    20.mlp.dense_4h_to_h.bias requires_grad=True\n", "    21.input_layernorm.weight requires_grad=True\n", "    21.input_layernorm.bias requires_grad=True\n", "    21.attention.key_value.weight requires_grad=True\n", "    21.attention.key_value.bias requires_grad=True\n", "    21.attention.query.weight requires_grad=True\n", "    21.attention.query.bias requires_grad=True\n", "    21.attention.dense.weight requires_grad=True\n", "    21.attention.dense.bias requires_grad=True\n", "    21.post_attention_layernorm.weight requires_grad=True\n", "    21.post_attention_layernorm.bias requires_grad=True\n", "    21.mlp.dense_h_to_4h.weight requires_grad=True\n", "    21.mlp.dense_h_to_4h.bias requires_grad=True\n", "    21.mlp.dense_4h_to_h.weight requires_grad=True\n", "    21.mlp.dense_4h_to_h.bias requires_grad=True\n", "    22.input_layernorm.weight requires_grad=True\n", "    22.input_layernorm.bias requires_grad=True\n", "    22.attention.key_value.weight requires_grad=True\n", "    22.attention.key_value.bias requires_grad=True\n", "    22.attention.query.weight requires_grad=True\n", "    22.attention.query.bias requires_grad=True\n", "    22.attention.dense.weight requires_grad=True\n", "    22.attention.dense.bias requires_grad=True\n", "    22.post_attention_layernorm.weight requires_grad=True\n", "    22.post_attention_layernorm.bias requires_grad=True\n", "    22.mlp.dense_h_to_4h.weight requires_grad=True\n", "    22.mlp.dense_h_to_4h.bias requires_grad=True\n", "    22.mlp.dense_4h_to_h.weight requires_grad=True\n", "    22.mlp.dense_4h_to_h.bias requires_grad=True\n", "    23.input_layernorm.weight requires_grad=True\n", "    23.input_layernorm.bias requires_grad=True\n", "    23.attention.key_value.weight requires_grad=True\n", "    23.attention.key_value.bias requires_grad=True\n", "    23.attention.query.weight requires_grad=True\n", "    23.attention.query.bias requires_grad=True\n", "    23.attention.dense.weight requires_grad=True\n", "    23.attention.dense.bias requires_grad=True\n", "    23.post_attention_layernorm.weight requires_grad=True\n", "    23.post_attention_layernorm.bias requires_grad=True\n", "    23.mlp.dense_h_to_4h.weight requires_grad=True\n", "    23.mlp.dense_h_to_4h.bias requires_grad=True\n", "    23.mlp.dense_4h_to_h.weight requires_grad=True\n", "    23.mlp.dense_4h_to_h.bias requires_grad=True\n", "    24.input_layernorm.weight requires_grad=True\n", "    24.input_layernorm.bias requires_grad=True\n", "    24.attention.key_value.weight requires_grad=True\n", "    24.attention.key_value.bias requires_grad=True\n", "    24.attention.query.weight requires_grad=True\n", "    24.attention.query.bias requires_grad=True\n", "    24.attention.dense.weight requires_grad=True\n", "    24.attention.dense.bias requires_grad=True\n", "    24.post_attention_layernorm.weight requires_grad=True\n", "    24.post_attention_layernorm.bias requires_grad=True\n", "    24.mlp.dense_h_to_4h.weight requires_grad=True\n", "    24.mlp.dense_h_to_4h.bias requires_grad=True\n", "    24.mlp.dense_4h_to_h.weight requires_grad=True\n", "    24.mlp.dense_4h_to_h.bias requires_grad=True\n", "    25.input_layernorm.weight requires_grad=True\n", "    25.input_layernorm.bias requires_grad=True\n", "    25.attention.key_value.weight requires_grad=True\n", "    25.attention.key_value.bias requires_grad=True\n", "    25.attention.query.weight requires_grad=True\n", "    25.attention.query.bias requires_grad=True\n", "    25.attention.dense.weight requires_grad=True\n", "    25.attention.dense.bias requires_grad=True\n", "    25.post_attention_layernorm.weight requires_grad=True\n", "    25.post_attention_layernorm.bias requires_grad=True\n", "    25.mlp.dense_h_to_4h.weight requires_grad=True\n", "    25.mlp.dense_h_to_4h.bias requires_grad=True\n", "    25.mlp.dense_4h_to_h.weight requires_grad=True\n", "    25.mlp.dense_4h_to_h.bias requires_grad=True\n", "    27.norm.weight requires_grad=True\n", "    27.norm.bias requires_grad=True\n", "    28.log_logit_scale requires_grad=True\n", "    28.projection.weight requires_grad=True\n", "    28.projection.bias requires_grad=True\n", "DeepSpeed is enabled.\n", "[2024-05-19 06:10:34,759] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+40617c2, git-hash=40617c2, git-branch=HEAD\n", "[2024-05-19 06:10:34,759] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/torch/distributed/distributed_c10d.py:761: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024-05-19 06:10:35,550] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   disable_allgather ............ False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 2048, 'scale_window': 8, 'delayed_shift': 2, 'min_scale': 0.125}\n", "[2024-05-19 06:10:35,550] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 2048\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 2e-05}\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2024-05-19 06:10:35,551] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2024-05-19 06:10:35,552] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 2e-05\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"initial_scale_power\": 11, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 8, \n", "        \"min_loss_scale\": 0.125\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using /home/<USER>/.cache/torch_extensions/py311_cu121 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py311_cu121/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.07748293876647949 seconds\n", "[2024-05-19 06:10:35,960] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2024-05-19 06:10:36,008] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=29 [0, 29) STAGE_PARAMS=1142450690 (1142.451M) TOTAL_PARAMS=1142450690 (1142.451M) UNIQUE_PARAMS=1142450690 (1142.451M)\n", " > number of parameters on model parallel rank 0: 1142450690\n", "Warning: did not find final_linear layer, cannot calculate embedding params\n", " > total params: 1,142,450,690\n", " > embedding params: 0\n", "Loading: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000\n", "[2024-05-19 06:10:36,089] [INFO] [engine.py:1555:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/mp_rank_00_model_states.pt\n", "[2024-05-19 06:10:36,258] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_00-model_00-model_states.pt\n", "[2024-05-19 06:10:36,318] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_02-model_00-model_states.pt\n", "[2024-05-19 06:10:36,376] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_03-model_00-model_states.pt\n", "[2024-05-19 06:10:36,435] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_04-model_00-model_states.pt\n", "[2024-05-19 06:10:36,493] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_05-model_00-model_states.pt\n", "[2024-05-19 06:10:36,553] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_06-model_00-model_states.pt\n", "[2024-05-19 06:10:36,612] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_07-model_00-model_states.pt\n", "[2024-05-19 06:10:36,669] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_08-model_00-model_states.pt\n", "[2024-05-19 06:10:36,719] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_09-model_00-model_states.pt\n", "[2024-05-19 06:10:36,777] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_10-model_00-model_states.pt\n", "[2024-05-19 06:10:36,838] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_11-model_00-model_states.pt\n", "[2024-05-19 06:10:36,891] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_12-model_00-model_states.pt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024-05-19 06:10:36,950] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_13-model_00-model_states.pt\n", "[2024-05-19 06:10:37,002] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/layer_14-model_00-model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"<string>\", line 3, in <module>\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/inference/process_wrap.py\", line 102, in serve\n", "    obj = obj_type(*init_args, **init_kwargs)\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/inference/inference_model.py\", line 62, in __init__\n", "    model, _, _ = setup_model_and_optimizer(\n", "                  ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/training.py\", line 622, in setup_model_and_optimizer\n", "    neox_args.iteration = load_checkpoint(\n", "                          ^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/checkpointing.py\", line 312, in load_checkpoint\n", "    checkpoint_name, state_dict = _load_model_checkpoint()\n", "                                  ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/checkpointing.py\", line 277, in _load_model_checkpoint\n", "    return _get_checkpoint()\n", "           ^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/augment/research/gpt-neox/megatron/checkpointing.py\", line 267, in _get_checkpoint\n", "    return model.load_checkpoint(\n", "           ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/.local/lib/python3.11/site-packages/deepspeed/runtime/engine.py\", line 1527, in load_checkpoint\n", "    load_path, client_states = self._load_checkpoint(load_dir,\n", "                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/.local/lib/python3.11/site-packages/deepspeed/runtime/engine.py\", line 1562, in _load_checkpoint\n", "    self.load_module_state_dict(state_dict=checkpoint['module'],\n", "  File \"/home/<USER>/.local/lib/python3.11/site-packages/deepspeed/runtime/pipe/engine.py\", line 1278, in load_module_state_dict\n", "    self.module.load_state_dir(load_dir=self._curr_ckpt_path, strict=strict)\n", "  File \"/home/<USER>/.local/lib/python3.11/site-packages/deepspeed/runtime/pipe/module.py\", line 571, in load_state_dir\n", "    layer.load_state_dict(torch.load(model_ckpt_path,\n", "                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/serialization.py\", line 1014, in load\n", "    return _load(opened_zipfile,\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/serialization.py\", line 1422, in _load\n", "    result = unpickler.load()\n", "             ^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/serialization.py\", line 1392, in persistent_load\n", "    typed_storage = load_tensor(dtype, nbytes, key, _maybe_decode_ascii(location))\n", "                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/torch/serialization.py\", line 1357, in load_tensor\n", "    storage = zip_file.get_storage_from_record(name, numel, torch.UntypedStorage)._typed_storage()._untyped_storage\n", "              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "KeyboardInterrupt\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb Cell 18\u001b[0m line \u001b[0;36m9\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=86'>87</a>\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mresearch\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39meval\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mharness\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39msystems\u001b[39;00m \u001b[39mimport\u001b[39;00m dual_rag_system\n\u001b[1;32m     <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=88'>89</a>\u001b[0m rag_system \u001b[39m=\u001b[39m dual_rag_system\u001b[39m.\u001b[39mDualRagSystem\u001b[39m.\u001b[39mfrom_yaml_config(config)\n\u001b[0;32m---> <a href='vscode-notebook-cell://ssh-remote%2Bvzhao-dev-a100/home/<USER>/augment/experimental/vzhao/eval/replay_requests.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=89'>90</a>\u001b[0m rag_system\u001b[39m.\u001b[39;49mload()\n", "File \u001b[0;32m~/augment/research/eval/harness/systems/dual_rag_system.py:46\u001b[0m, in \u001b[0;36mDualRagSystem.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     44\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mLoading the system (fim_gen_mode=\u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mfim_gen_mode\u001b[39m.\u001b[39mname\u001b[39m}\u001b[39;00m\u001b[39m)...\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m     45\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mmodel\u001b[39m.\u001b[39mload()\n\u001b[0;32m---> 46\u001b[0m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mdense_index\u001b[39m.\u001b[39;49mload()\n\u001b[1;32m     47\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mdense_signature_index\u001b[39m.\u001b[39mload()\n", "File \u001b[0;32m~/augment/research/retrieval/retrieval_database.py:262\u001b[0m, in \u001b[0;36mRetrievalDatabase.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    260\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mload\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[1;32m    261\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"Loads components.\"\"\"\u001b[39;00m\n\u001b[0;32m--> 262\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mscorer\u001b[39m.\u001b[39;49mload()\n", "File \u001b[0;32m~/augment/research/retrieval/scorers/dense_scorer.py:84\u001b[0m, in \u001b[0;36mDenseRetrievalScorer.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     76\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39membedding_model \u001b[39m=\u001b[39m embedding_model\n\u001b[1;32m     78\u001b[0m \u001b[39m# Loading the process-wrapped inference model is non-blocking, so we\u001b[39;00m\n\u001b[1;32m     79\u001b[0m \u001b[39m# query the model to synchronize model loading with this process.\u001b[39;00m\n\u001b[1;32m     80\u001b[0m \u001b[39m# If we don't block on loading the embedding model, then it may load at the\u001b[39;00m\n\u001b[1;32m     81\u001b[0m \u001b[39m# same time as another model loads in this process. Each model will try to grab a port\u001b[39;00m\n\u001b[1;32m     82\u001b[0m \u001b[39m# for torch.distributed to bind to. This causes a race condition that can result in multiple\u001b[39;00m\n\u001b[1;32m     83\u001b[0m \u001b[39m# models trying to bind to the same port. Therfore, we synchronize model loading.\u001b[39;00m\n\u001b[0;32m---> 84\u001b[0m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49membedding_model\u001b[39m.\u001b[39;49mcontrastive_embed([], [])\n\u001b[1;32m     86\u001b[0m \u001b[39m# Makes `query_formatter` and `document_formatter` use tokenizer of `embedding_model`.\u001b[39;00m\n\u001b[1;32m     87\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mquery_formatter\u001b[39m.\u001b[39mtokenizer \u001b[39m=\u001b[39m embedding_model\u001b[39m.\u001b[39mget_tokenizer()\n", "File \u001b[0;32m~/augment/research/gpt-neox/megatron/inference/process_wrap.py:60\u001b[0m, in \u001b[0;36mProcessWrappedObject.__getattr__.<locals>.proxy_func\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     58\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mproxy_func\u001b[39m(\u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[1;32m     59\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_send((func_name, args, kwargs))\n\u001b[0;32m---> 60\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_receive()\n", "File \u001b[0;32m~/augment/research/gpt-neox/megatron/inference/process_wrap.py:80\u001b[0m, in \u001b[0;36mProcessWrappedObject._receive\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     78\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_receive\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[1;32m     79\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"Receive by reading a pickled object from the fifo.\"\"\"\u001b[39;00m\n\u001b[0;32m---> 80\u001b[0m     \u001b[39mreturn\u001b[39;00m pickle\u001b[39m.\u001b[39;49mload(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mfifo_fh)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from research.eval.harness.systems import dual_rag_system\n", "\n", "config = {\n", "    \"model\": {\n", "        \"name\": \"ender_fastforward\",\n", "        \"model_path\": \"ender/16b_multieth64m_addrust_noinline_sigretprefsuf\",\n", "        \"prompt\": {\n", "            \"component_order\": [\"signature\", \"retrieval\", \"prefix\", \"suffix\"],\n", "            \"max_prefix_tokens\": 1280,\n", "            \"max_prompt_tokens\": 5120,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_signature_tokens\": 1024,\n", "            \"max_suffix_tokens\": 768,\n", "        },\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": 280,\n", "    },\n", "    \"dense_signature_retriever\": {\n", "        \"scorer\": {\n", "            \"name\": \"generic_neox\",\n", "            \"checkpoint_path\": \"methanol/methanol_0416.3_1250\",\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"signature\",\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"max_lines\": -1,\n", "            \"add_path\": True,\n", "            \"add_suffix\": True,\n", "            \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"simple_document\",\n", "            \"add_path\": <PERSON>als<PERSON>,\n", "            \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        },\n", "        \"experimental\": {\n", "            \"remove_suffix\": False,\n", "            # \"retriever_top_k\": 1,\n", "            \"trim_on_dedent\": <PERSON>alse,\n", "            \"trim_on_max_lines\": None,\n", "        },\n", "    },\n", "    \"dense_retriever\": {\n", "        \"scorer\": {\n", "            \"name\": \"generic_neox\",\n", "            \"checkpoint_path\": \"starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 40,\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"max_lines\": -1,\n", "            \"add_path\": True,\n", "            \"add_suffix\": True,\n", "            \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True,\n", "            \"add_prefix\": <PERSON>als<PERSON>,\n", "            \"tokenizer_name\": \"StarCoderTokenizer\",\n", "        },\n", "        \"experimental\": {\n", "            \"remove_suffix\": False,\n", "            # \"retriever_top_k\": 1,\n", "            \"trim_on_dedent\": <PERSON>alse,\n", "            \"trim_on_max_lines\": None,\n", "        },\n", "    },\n", "    \"signature_top_k\": 16,\n", "    \"dense_top_k\": 32,\n", "}\n", "\n", "\n", "rag_system = dual_rag_system.DualRagSystem.from_yaml_config(config)\n", "rag_system.load()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n", "Indexing all dependent documents into the system.\n", "\u001b[2m2024-05-16 17:16:49\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 2134 missing entries.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-05-16 17:17:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 2134 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 2134 keys (total size 13434672).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 13434672 units to insert 2134 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:02\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 160 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 160 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 160 keys (total size 1868632).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1868632 units to insert 160 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 3480).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 3480 units to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:03\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 28 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 28 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 28 keys (total size 218088).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 218088 units to insert 28 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:04\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 674 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 674 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 674 keys (total size 11044608).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 11044608 units to insert 674 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:08\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 179 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 179 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 179 keys (total size 2534968).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 2534968 units to insert 179 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:09\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 237 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 237 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 237 keys (total size 3241688).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 3241688 units to insert 237 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 18 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 18 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 18 keys (total size 257192).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 257192 units to insert 18 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:11\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 19 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 19 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 19 keys (total size 339032).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 339032 units to insert 19 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 18 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 18 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 18 keys (total size 227776).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 227776 units to insert 18 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 2 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 2 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 2 keys (total size 46936).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 46936 units to insert 2 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 2 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 2 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 2 keys (total size 18832).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 18832 units to insert 2 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 17 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 17 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 17 keys (total size 193008).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 193008 units to insert 17 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 45 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 45 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 45 keys (total size 694848).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 694848 units to insert 45 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 20 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 20 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 20 keys (total size 291584).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 291584 units to insert 20 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 25 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 25 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 25 keys (total size 779288).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 779288 units to insert 25 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 20 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 20 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 20 keys (total size 349296).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 349296 units to insert 20 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 5 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 5 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 5 keys (total size 55632).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 55632 units to insert 5 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 7 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 7 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 7 keys (total size 168872).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 168872 units to insert 7 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1334 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1334 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1334 keys (total size 18261400).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 18261400 units to insert 1334 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 21656).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 21656 units to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 1904).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 1904 units to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 12376).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 12376 units to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:24\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 1 missing entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 1 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 1 keys (total size 7512).\u001b[0m\n", "\u001b[2m2024-05-16 17:17:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 7512 units to insert 1 entries.\u001b[0m\n", "\u001b[2m2024-05-16 17:17:25\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Adding 4950 docs to the index.\n", "INFO:research.retrieval.retrieval_database:Starting to index 2729 new documents.\n", "Indexing: 100%|███████████████████████████████████████████████████████████| 2729/2729 [12:57<00:00,  3.51it/s]\n", "INFO:research.retrieval.retrieval_database:Finished indexing 2729 documents in 777.003s.\n", "INFO:research.retrieval.retrieval_database:Starting to index 2729 new documents.\n", "Indexing: 100%|███████████████████████████████████████████████████████████| 2729/2729 [07:47<00:00,  5.83it/s]\n", "INFO:research.retrieval.retrieval_database:Finished indexing 2729 documents in 467.812s.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c34234484eda48259ca2358f75ecbfca", "version_major": 2, "version_minor": 0}, "text/plain": ["Replaying:   0%|          | 0/43 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluating on request 200e17a6-d2b2-4128-8856-a7a034fd135f\n", "Evaluating on request 0272b5ff-69e5-4034-a31d-b430756f3f4f\n", "Evaluating on request 3456d0ed-1abc-42a0-837d-4cf4be2db30b\n", "Evaluating on request 8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\n", "Evaluating on request 252c43eb-664c-4c07-8b05-97dc013f45de\n", "Evaluating on request 79484d9e-62cf-4671-a452-5fea63b10196\n", "Evaluating on request 84e6ffca-44de-41eb-8bce-c8d460308c7c\n", "Evaluating on request 341aefb7-45b7-4b72-b000-b9f166d3f95f\n", "Evaluating on request 19d3d1b0-0509-4074-bab0-140e12356350\n", "Evaluating on request 438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\n", "Evaluating on request f59e877b-df59-4812-8ce1-b5c4e4686b25\n", "Evaluating on request c2f63804-efed-4711-970b-8b9032476758\n", "Evaluating on request 5bb9f026-9c2a-4681-b1df-34ba6e7b6407\n", "Evaluating on request 38a87727-f608-486e-baa0-d580f06a4701\n", "Evaluating on request 5f9abc1e-bbdb-465f-8ee4-91c5ed178958\n", "Evaluating on request 37b49998-03f4-441f-b584-982aae11fcfb\n", "Evaluating on request c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\n", "Evaluating on request ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\n", "Evaluating on request 7e6d997c-3d6e-4493-a3a2-13983fbb571f\n", "Evaluating on request d454140e-dd4b-4cf8-bd19-3a61d85e9163\n", "Evaluating on request 126fbf6e-a739-4ad1-9942-af8da6af17f0\n", "Evaluating on request b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\n", "Evaluating on request b251b2bf-eed4-475b-b969-cf6807aab4df\n", "Evaluating on request b535a8bc-cbda-45da-b7fc-1b9976980137\n", "Evaluating on request 488a7a15-da79-4dae-802b-a449cd63cfab\n", "Evaluating on request 85f9c626-3b59-41bc-8c2e-a73bed1d81d6\n", "Evaluating on request 3f773136-5967-4c96-ba75-9ed692d44a77\n", "Evaluating on request 2a53b956-82e5-41b5-97b0-4e07ce2b9052\n", "Evaluating on request 669c8097-7324-4af9-afde-8098b6de1fdc\n", "Evaluating on request dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\n", "Evaluating on request 38315ad8-b8f3-4c6a-84e5-afa5933f38ba\n", "Evaluating on request ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\n", "Evaluating on request 9f6469a2-65f3-486c-a58e-d34c598087b6\n", "Evaluating on request 86dff485-1174-4bc3-a9d4-5dec6d8be5d2\n", "Evaluating on request 64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\n", "Evaluating on request 57d60d7d-c2ce-4755-8a7b-4a3d01214859\n", "Evaluating on request 4813df91-f7b9-4e69-9a07-0547b84ccb43\n", "Evaluating on request d46dc24a-f904-41b8-a2bb-d52545513e96\n", "Evaluating on request 2c9f54cb-fd41-4adc-8fc5-59074916c06d\n", "Evaluating on request e0a30509-89b0-4d1c-ae83-83077ff1eb24\n", "Evaluating on request 58512ff9-45ca-4f45-8446-be352e2c3bdb\n", "Evaluating on request 73ad3121-7aec-4dea-8542-1e2f3d2e1b59\n", "Evaluating on request 73e95698-12d1-49cc-b5a0-6179dca2acde\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from research.eval.harness.systems import dual_rag_system\n", "\n", "# Replay dataset\n", "from tqdm.auto import tqdm\n", "\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.tools.replay_requests import index_all_docs, to_model_input\n", "\n", "print(\"Indexing all dependent documents into the system.\")\n", "index_all_docs(rag_system, dataset, completions)\n", "\n", "succeeded = []\n", "replayed_responses = list[CompletionResult]()\n", "for datum in tqdm(completions, desc=\"Replaying\"):\n", "    print(f\"Evaluating on request {datum.request_id}\")\n", "    mi = to_model_input(datum)\n", "    if mi.extra[\"doc_ids\"]:\n", "        succeeded.append(datum)\n", "        replayed_responses.append(rag_system.generate(to_model_input(datum)))\n", "\n", "print(len(succeeded), len(replayed_responses))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bffede219f894c1baade7d66655cd511", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HBox(children=(IntText(value=1000, description='Prefix len:'), IntText(value=500, description='…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from research.tools.replay_requests import display_replay_viewer\n", "\n", "display_replay_viewer(succeeded, replayed_responses)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepares Google Sheet for rating"]}, {"cell_type": "code", "execution_count": 342, "metadata": {}, "outputs": [], "source": ["# Get credentials\n", "\n", "from base.datasets import google_sheet_utils\n", "\n", "creds = google_sheet_utils.get_google_sheet_credentials()\n", "service = google_sheet_utils.get_google_sheets_service(creds)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from base.datasets import google_sheet_utils\n", "\n", "root = pathlib.Path(f\"/mnt/efs/augment/user/{os.getlogin()}/sxs/\")\n", "suffix = \"reject\"\n", "eval_name = f\"{MODEL_NAME.replace('-', '_')}_{suffix}\"\n", "eval_dir = root / eval_name\n", "\n", "spreadsheet_id = \"1rv3pirCmTdlBvfrJfy_eCikGihxxusRO5nU9_Bd_Hf0\"\n", "title = f\"{MODEL_NAME} vs. PROD {suffix}\"\n", "\n", "google_sheet_utils.create_new_sheet(service, spreadsheet_id, title)\n", "\n", "# Write the instruction row.\n", "google_sheet_utils.update_row(\n", "    service,\n", "    spreadsheet_id,\n", "    title,\n", "    1,\n", "    values=[\"\"\"Instruction: \"\"\"],\n", ")\n", "\n", "# Write the header row.\n", "google_sheet_utils.update_row(\n", "    service,\n", "    spreadsheet_id,\n", "    title,\n", "    2,\n", "    values=[\n", "        \"original_request_id\",\n", "        \"original_completion\",\n", "        \"new_request_id\",\n", "        \"new_completion\",\n", "        \"sxs_link\",\n", "        \"ratings\",\n", "        \"comments\",\n", "    ],\n", ")\n", "\n", "ROW_OFFSET = 7\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from termcolor import colored\n", "from base.datasets import google_sheet_utils\n", "from base.datasets.sxs import utils\n", "\n", "\n", "if not os.path.exists(eval_dir):\n", "    os.makedirs(eval_dir)\n", "\n", "\n", "SUPPORT_DOGFOOD = \"https://support.dogfood.t.us-central1.prod.augmentcode.com/request/\"\n", "SUPPORT_VZHAO = \"https://support.dev-vzhao.t.us-central1.dev.augmentcode.com/request/\"\n", "\n", "\n", "# Print out the differences for each request.\n", "for idx, replay in enumerate(replayed_responses[:30]):\n", "    if replay.response.model != \"roguesl-farpref-16B-eth6-c\":\n", "        continue\n", "    print(f\"== Original request id: {replay.request_id} ({replay.response.model}) ==\")\n", "    print(colored(replay.response.text, \"green\"))\n", "    print(\n", "        f\"== Replayed request id: {replay.replayed_request_id} ({replay.replayed_response.model}) ==\"\n", "    )\n", "    print(colored(replay.replayed_response.text, \"blue\"))\n", "\n", "    html = utils.make_sxs_html(replay)\n", "    filename = f\"{replay.replayed_request_id}.html\"\n", "    with open(eval_dir / filename, \"w\") as f:\n", "        f.write(html)\n", "\n", "    google_sheet_utils.update_row(\n", "        service,\n", "        spreadsheet_id,\n", "        title,\n", "        idx + ROW_OFFSET + 1,\n", "        values=[\n", "            f'=HYPERLINK(\"{SUPPORT_DOGFOOD}{replay.request_id}\", \"{replay.request_id}\")',\n", "            replay.response.text,\n", "            f'=HYPERLINK(\"{SUPPORT_VZHAO}{replay.replayed_request_id}\", \"{replay.replayed_request_id}\")',\n", "            replay.replayed_response.text,\n", "            f\"http://localhost:8000/{eval_name}/{filename}\",\n", "        ],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [], "source": ["!pip install textdistance"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Sample random\n", "\n", "from base.datasets import tenants\n", "from google.cloud import bigquery, storage\n", "from typing import Literal, Optional, TypedDict, cast\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, PathAndContent\n", "\n", "query = \"\"\"SELECT\n", "            request.request_id,\n", "            request.raw_proto AS request_proto,\n", "            request.time AS request_timestamp,\n", "            response.raw_proto AS response_proto,\n", "            response.time AS response_timestamp,\n", "            CASE WHEN resolution.accepted_idx >= 0 THEN true ELSE false END AS accepted,\n", "            resolution.time AS resolution_timestamp\n", "        FROM `staging_request_insight_full_export_dataset.completion_event` request\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "        JOIN `staging_request_insight_full_export_dataset.completion_event` resolution USING (request_id)\n", "        WHERE request.tenant = 'dogfood'\n", "        AND rand() < 0.01\n", "        AND request.event_type = 'completion_host_request'\n", "        AND response.event_type = 'completion_host_response'\n", "        AND resolution.event_type = 'completion_resolution'\n", "        AND request.raw_proto IS NOT NULL\n", "        AND response.raw_proto IS NOT NULL\n", "        AND resolution.raw_proto IS NOT NULL\n", "        AND response.completion_length >= 1\n", "        AND resolution.accepted_idx < 0\n", "        ORDER BY request.request_id\n", "        LIMIT 500\n", "\"\"\"\n", "\n", "dataset = completion_dataset.CompletionDataset.create_from_query(\n", "    query,\n", "    tenants.get_tenant(TENANT_NAME),\n", ")\n", "\n", "\n", "TENANT_NAME = \"dogfood\"\n", "tenant = tenants.get_tenant(TENANT_NAME)\n", "bucket = storage.Client(tenant.project_id).bucket(tenant.blob_bucket_name)\n", "blob_cache = GCSBlobCache(\n", "    bucket,\n", "    tenant.blob_bucket_prefix,\n", "    2**30,\n", "    num_threads=10,\n", ")\n", "bigquery_client = bigquery.Client(tenant.project_id)\n", "rows = bigquery_client.query_and_wait(query)\n", "dataset = completion_dataset.CompletionDataset(\n", "    rows,\n", "    cast(Optional[int], rows.total_rows),\n", "    blob_cache=blob_cache,\n", ")\n", "completions = list(\n", "    [\n", "        c\n", "        for c in dataset.get_completions()\n", "        if c.response.model == \"roguesl-farpref-16B-eth6-c\"\n", "    ]\n", ")\n", "print(len(completions))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Replay\n", "\n", "import os\n", "import pathlib\n", "\n", "from base.datasets.sxs import sampler\n", "from base.datasets import replay\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "    token=\"FILL\",\n", ")\n", "sampler.ensure_blobs_exist(client, dataset, completions)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# MODEL_NAME = \"roguesl-farpref-16B-seth6-p1024\"\n", "MODEL_NAME = \"roguesl-farpref-16B-seth6-p512\"\n", "replayed_responses = replay.replay(client, MODEL_NAME, completions)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Sort by editing distance.\n", "\n", "import tqdm\n", "import textdistance\n", "from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer\n", "\n", "tokenizer = TiktokenStarCoderTokenizer()\n", "\n", "scored = []\n", "for rr in tqdm.tqdm(replayed_responses):\n", "    old = rr.response.text\n", "    new = rr.replayed_response.text\n", "    distance = textdistance.levenshtein.distance(\n", "        tokenizer.tokenize_safe(old), tokenizer.tokenize_safe(new)\n", "    )\n", "    scored.append((distance, rr))\n", "\n", "scored = sorted(scored, key=lambda x: x[0], reverse=True)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["accepted_scored = sorted(scored, key=lambda x: x[0], reverse=True)\n", "replayed_responses = [e[1] for e in accepted_scored]\n", "len(accepted_scored)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["accepted_scored[20][0]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["rejected_scored = sorted(scored, key=lambda x: x[0], reverse=True)\n", "replayed_responses = [e[1] for e in rejected_scored]\n", "len(replayed_responses)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["rejected_scored[30][0]"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["dd"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.ticker import PercentFormatter\n", "\n", "max_value = 20\n", "\n", "plt.figure(figsize=(8, 3))\n", "dd = plt.hist(\n", "    [\n", "        [min(s[0], max_value + 1) for s in accepted_scored],\n", "        [min(s[0], max_value + 1) for s in rejected_scored],\n", "    ],\n", "    # 30,\n", "    bins=range(0, max_value + 2),\n", "    density=True,\n", "    histtype=\"bar\",\n", "    # alpha=0.7,\n", "    # cumulative=-1,\n", "    # cumulative=True,\n", "    align=\"left\",\n", ")\n", "# plt.gca().yaxis.set_major_formatter(PercentFormatter(1))\n", "\n", "plt.legend([\"Accepted\", \"Rejected\"])\n", "plt.xlabel(\"Token-level Edit Distance of Completions\")\n", "plt.xticks(ticks=np.arange(0, 25, 5), labels=[\"0\", \"5\", \"10\", \"15\", \"20+\"])\n", "plt.ylabel(\"Probability\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [], "source": ["dd[0].sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Missing Blobs"]}, {"cell_type": "code", "execution_count": 290, "metadata": {}, "outputs": [], "source": ["name = \"521b60d07f1e792424b37d67e0221d5b69502e5de4c15f183768d5abe30ab974\""]}, {"cell_type": "code", "execution_count": 293, "metadata": {}, "outputs": [], "source": ["dog_food_client = AugmentClient(\n", "    url=\"https://dogfood.api.augmentcode.com\",\n", "    token=\"FILL\",\n", ")\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "    token=\"FILL\",\n", ")"]}, {"cell_type": "code", "execution_count": 296, "metadata": {}, "outputs": [], "source": ["client.find_missing([name])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}
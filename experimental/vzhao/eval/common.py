"""Common functions for running evaluations."""

import pathlib
import subprocess
import tempfile

import yaml
from determined.experimental import client

from research.core.constants import AUGMENT_CHECKPOINTS_ROOT


def override(config: dict, overrides: dict) -> dict:
    """Override the config with the appropriate values."""
    for key_path, value in overrides.items():
        root = config
        keys = key_path.split(".")
        for key in keys[:-1]:
            root = root[key]
        root[keys[-1]] = value
    return config


def load_template_and_update(
    *,
    template_path: str,
    name: str,
    lm_model: dict,
    scorer: dict,
    query_formatter: dict,
    document_formatter: dict,
    overrides=None,
) -> dict:
    """Load the template and update with the appropriate values."""
    with pathlib.Path(template_path).open("r") as f:
        config = yaml.safe_load(f)

    config["system"]["model"] = lm_model
    config["system"]["retriever"]["scorer"] = scorer
    config["system"]["retriever"]["query_formatter"] = query_formatter
    config["system"]["retriever"]["document_formatter"] = document_formatter
    config["determined"]["name"] = name
    if overrides:
        override(config, overrides)
    return config


def run_determined(config: dict) -> None:
    """Run the evaluation in determined."""
    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        yaml.dump(config, f)
        print(f.name)
        subprocess.run(
            f"python research/eval/eval.py --v2 {f.name}",
            shell=True,
            check=False,
        )


def maybe_download_ckpt(det_id: int, dirname: str, uuid=None) -> str:
    """Download the checkpoint if it doesn't exist."""
    experiment = client.get_experiment(det_id)
    trial = experiment.get_trials()[0]
    if uuid:
        ckpt = trial.select_checkpoint(uuid=uuid)
    else:
        ckpt = trial.select_checkpoint(latest=True)
    uuid = ckpt.uuid
    assert experiment is not None and ckpt is not None
    model_name = experiment.config["name"].replace("-", "_").lower()
    (AUGMENT_CHECKPOINTS_ROOT / dirname).mkdir(parents=True, exist_ok=True)
    ckpt_path = f"{dirname}/{model_name}_{ckpt.metadata['steps_completed']}"
    print(f"checkpoint path: {AUGMENT_CHECKPOINTS_ROOT/ckpt_path}")
    if (AUGMENT_CHECKPOINTS_ROOT / ckpt_path).exists():
        return ckpt_path
    subprocess.run(
        f"bash research/utils/download_checkpoint.sh {uuid} {ckpt_path}",
        shell=True,
        check=False,
    )
    return ckpt_path


def run_eval_suite(
    *,
    det_id=None,
    selected_evals,
    lm_models,
    scorer,
    query_formatter,
    doc_formatter,
    overrides,
    other_tags=None,
):
    for LM_MODEL in lm_models:
        # DET experiment name.
        base_name = [
            pathlib.Path(LM_MODEL["checkpoint_path"]).name,
            pathlib.Path(scorer["checkpoint_path"]).name,
        ]
        if det_id:
            base_name.append(str(det_id))
        if other_tags:
            base_name.extend(other_tags)

        if "multiline" in selected_evals:
            config = load_template_and_update(
                template_path="/home/<USER>/augment/experimental/vzhao/eval/templates/multiline_default_template.yml",
                name=", ".join(base_name + ["repoeval_2-3lines"]),
                lm_model=LM_MODEL,
                scorer=scorer,
                query_formatter=query_formatter,
                document_formatter=doc_formatter,
                overrides=overrides,
            )
            run_determined(config)

        if "all_lang" in selected_evals:
            config = load_template_and_update(
                template_path="/home/<USER>/augment/experimental/vzhao/eval/templates/alllang_default_template.yml",
                name=", ".join(base_name + ["all_languages_2-3lines"]),
                lm_model=LM_MODEL,
                scorer=scorer,
                query_formatter=query_formatter,
                document_formatter=doc_formatter,
                overrides=overrides,
            )
            run_determined(config)

        if "api" in selected_evals:
            config = load_template_and_update(
                template_path="/home/<USER>/augment/experimental/vzhao/eval/templates/api_default_template.yml",
                name=", ".join(base_name + ["finegrained-python.large"]),
                lm_model=LM_MODEL,
                scorer=scorer,
                query_formatter=query_formatter,
                document_formatter=doc_formatter,
                overrides=overrides,
            )
            run_determined(config)

        if "cceval" in selected_evals:
            config = load_template_and_update(
                template_path="/home/<USER>/augment/experimental/vzhao/eval/templates/cceval_default_template.yml",
                name=", ".join(base_name + ["cceval"]),
                lm_model=LM_MODEL,
                scorer=scorer,
                query_formatter=query_formatter,
                document_formatter=doc_formatter,
                overrides=overrides,
            )
            run_determined(config)


def launch_eval(yml_file_path: str):
    """Launch the evaluation."""
    subprocess.run(
        f"python research/eval/eval.py --v2 {yml_file_path}",
        shell=True,
        check=False,
    )

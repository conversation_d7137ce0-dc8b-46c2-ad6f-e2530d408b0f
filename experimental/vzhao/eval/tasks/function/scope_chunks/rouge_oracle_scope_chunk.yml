#
# This file contains an example of an evaluation config
#
# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/function/scope_chunks/rouge_oracle_scope_chunk.yml

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: rag_with_reranker
    model:
      name: rogue
      checkpoint_path: rogue/pydiff2m_7b
      prompt:
        max_prefix_tokens: 1024
        max_prompt_tokens: 7632
        max_suffix_tokens: 1024
        max_retrieved_chunk_tokens: -1
        # NOTE: Change this to control the max number of retrieved chunks in the prompt.
        max_number_chunks: 1
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 560
    retriever:
      name: diff_boykin
      chunker: scope_aware
      max_chunk: 256
      max_query_lines: 10
    reranker:
      name: oracle_perplexity_reranker
      batchsize: 8
      top_k: 1000
    experimental:
      use_fim_when_possible: true
      retriever_top_k: 1000
      # After FIM training, no need for the postprocessing.
      trim_on_dedent: false
      trim_on_max_lines: null

# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: repoeval_functions
    allow_deny_ids_file: /mnt/efs/augment/user/vzhao/data/hydra/function_patch_ids_small.json
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
# podspec: 1xA100.yaml
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: pydiff2m_7b, OracleRerank, ScopeChunk, function, max_number_chunks=1, topk=1000
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

#
# This file contains an example of an evaluation config
#
# Usage:
# python /home/<USER>/augment/research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/gold_system.yml
#
# Result:
#   /mnt/efs/augment/eval/jobs/XmWzvcFR

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: gold_system

# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: repoeval_2-3lines
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
# podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: RepoEvalMultiline, GoldSystem, Hydra
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

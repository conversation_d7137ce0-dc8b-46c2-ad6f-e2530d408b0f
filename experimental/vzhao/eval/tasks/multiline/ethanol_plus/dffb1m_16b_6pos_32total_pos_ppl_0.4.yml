# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/ethanol_plus/dffb1m_16b_6pos_32total_pos_ppl_0.4.yml
# python research/eval/eval.py --v2 --local --dry_run /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/ethanol_plus/dffb1m_16b_6pos_32total_pos_ppl_0.4.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b, 6pos_32total_pos_ppl_0.4, LineChunk, repoeval_2-3lines, max_number_chunks=32, topk=32
  workspace: Dev
  project: vzhao-eval
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: contrieve_350m
      checkpoint: ethanol_plus/6pos_32total_pos_ppl_0.4
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
      name: simple_query
      max_lines: 20
  experimental:
    remove_suffix: False
    retriever_top_k: 32
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: repoeval_2-3lines
podspec: 1xA100.yaml

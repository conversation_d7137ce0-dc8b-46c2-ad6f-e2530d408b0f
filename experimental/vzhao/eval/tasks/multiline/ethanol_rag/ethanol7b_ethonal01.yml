# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/ethanol_rag/ethanol7b_ethonal01.yml
# python research/eval/eval.py --v2 --local --dry_run /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/ethanol_rag/ethanol7b_ethonal01.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: ethanol7b, Ethanol, LineChunk, repoeval_2-3lines, max_number_chunks=32, topk=32
  workspace: Dev
  project: Eval
system:
  name: basic_rag
  model:
    checkpoint_path: ethanol_rag/ethanol_rag_7b_1003
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
      prepend_path_to_retrieved: True
      add_retrieval_after_context: True
      only_truncate_true_prefix: True
      always_use_suffix_token: True
      recent_prefix_char_len: 250
      prefix_char_offset: 200
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: ethanol
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    remove_suffix: False
    retriever_top_k: 32
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: repoeval_2-3lines
podspec: A40.yaml

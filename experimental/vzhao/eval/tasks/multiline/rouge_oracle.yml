#
# This file contains an example of an evaluation config
#
# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/rouge_oracle.yml

determined:
  name: pydiff2m_7b, OracleRerank, repoeval_2-3lines, max_number_chunks=10, topk=1000
  workspace: Dev
  project: vzhao-eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
system:
  name: rag_with_reranker
  model:
    name: rogue
    checkpoint_path: rogue/pydiff2m_7b
    prompt:
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      max_retrieved_chunk_tokens: -1
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 10
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 560
  retriever:
    scorer:
      name: diff_boykin
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
      name: simple_query
      max_lines: 10
  reranker:
    name: oracle_perplexity_reranker
    batchsize: 2
    top_k: 1000
  experimental:
    remove_suffix: False
    retriever_top_k: 1000
    # After FIM training, no need for the postprocessing.
    trim_on_dedent: false
    trim_on_max_lines: null

# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  name: hydra
  dataset: repoeval_2-3lines
  allow_deny_ids_file: /mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_small.json
  # limit: 10
  # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 1xA100.yaml
# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below


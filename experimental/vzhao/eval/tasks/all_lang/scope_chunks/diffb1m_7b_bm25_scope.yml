#
# This file contains an example of an evaluation config
#
# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/all_lang/scope_chunks/diffb1m_7b_bm25_scope.yml

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: rogue
      checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken
      prompt:
        max_prefix_tokens: 1024
        max_prompt_tokens: 3816
        max_suffix_tokens: 1024
        max_retrieved_chunk_tokens: -1
        # NOTE: Change this to control the max number of retrieved chunks in the prompt.
        max_number_chunks: 1
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 560
    retriever:
      name: bm25
      chunker:
        name: scope_aware
        max_lines_per_chunk: 256
        key_type: stub
        parse_errored_root: true
      query_formatter:
        name: signature_query
        max_lines: 256
      document_formatter:
        name: chunk_key_document
    experimental:
      remove_suffix: False
      retriever_top_k: 100
      # After FIM training, no need for the postprocessing.
      trim_on_dedent: false
      trim_on_max_lines: null

# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: all_languages_2-3lines_medium_to_hard.v1.0
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: diffb1m_7b, BM25_Scope_Scope, all_languages_2-3lines_medium_to_hard, max_number_chunks=1, topk=100
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

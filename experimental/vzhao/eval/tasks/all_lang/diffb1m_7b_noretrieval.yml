#
# This file contains an example of an evaluation config
#
# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/all_lang/rouge_7b_noretrieval.yml

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: rogue
      checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken
      prompt:
        max_prefix_tokens: 2048
        max_prompt_tokens: 7912
        max_suffix_tokens: 2048
        max_retrieved_chunk_tokens: -1
        max_number_chunks: 0
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 560
    retriever:
      name: null
      chunker: line_level
      max_chunk: 40
      max_query_lines: 10
    experimental:
      remove_suffix: False
      retriever_top_k: 1000
      # After FIM training, no need for the postprocessing.
      trim_on_dedent: false
      trim_on_max_lines: null

# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: all_languages_2-3lines_medium_to_hard.v1.0
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: diffb1m_7b, NoRetrieval, all_languages_2-4lines_medium_to_hard
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

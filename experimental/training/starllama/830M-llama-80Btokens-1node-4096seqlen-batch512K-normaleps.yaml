# Pretrain a 830M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml
- augment_configs/pretrain/datasets/starcoder_seqlen4096.yml

determined:
  name: arun-830M-llama-pretrain-80Btokens-seqlen4096-batch512K-normaleps  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0
  save_trial_latest: 2

overrides:
  wandb_name: arun-830M-llama-pretrain-80Btokens-seqlen4096-batch512K-normaleps  # pragma: allowlist secret
  wandb_group: arun-830M
  wandb_project: starllama

  load: null
  shuffle_direct_dataset: True

  # 512k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 16
  gradient_accumulation_steps: 1
  train_batch_size: 128   # 16 * 8

  # 160k steps @ 512k tokens ~ 80B tokens
  lr_decay_style: cosine
  lr_decay_iters: null
  train_iters: 160_000
  warmup_iters: 2_000

  save_interval: 5000  # Don't save too often

  # We're setting up our optimization parameters following GPT-3 for 800M:
  # Cosine learning decay from 2.5e-4 to 2.5e-5.
  # AdamW with betas of (0.9, 0.95)
  min_lr: 2.5e-5
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 2.5e-4  # GPT-3 LR
    type: Adam

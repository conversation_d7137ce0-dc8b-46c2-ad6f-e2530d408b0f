# Pretrain a 830M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml
- augment_configs/pretrain/datasets/starcoder_seqlen4096.yml

determined:
  name: 830M-starllama-stable-validation  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0
  save_trial_latest: 2

overrides:
  load: /mnt/efs/augment/checkpoints/arun/starllama830m-stable
  shuffle_direct_dataset: True

  wandb_name: arun-830M-starllama-stable-validation  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  train_micro_batch_size_per_gpu: 16
  gradient_accumulation_steps: 1
  train_batch_size: 128   # 24 * 8
  train_iters: 1

  lr_decay_style: cosine
  lr_decay_iters: 108_000

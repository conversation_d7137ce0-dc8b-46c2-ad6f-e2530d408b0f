# Pretrain a 200M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/200M-wide-model-def.yml
- augment_configs/pretrain/datasets/starcoder_seqlen4096.yml

determined:
  name: arun-200M-wide-llama-pretrain-15Btokens-seqlen4096-1node-batch512K-lr6e-4  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["200M-wide", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: arun-200M-wide-llama-pretrain-15Btokens-seqlen4096-1node-batch1M-lr1.2e-3  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  load: null
  shuffle_direct_dataset: True

  # 1M tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 1
  train_batch_size: 256   # 8 * 32

  # 15B tokens ~ 15k steps @ 1M tokens
  lr_decay_style: cosine
  lr_decay_iters: null
  train_iters: 15000
  warmup: 0.03333  # 500M tokens ~= 500 steps

  save_interval: 5000  # Don't save too often

  # Optimization; 2x GPT-3 LR
  min_lr: 1.2e-4
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 1.2e-4
    type: AdamW

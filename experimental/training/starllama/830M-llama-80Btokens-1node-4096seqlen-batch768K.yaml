# Pretrain a 830M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml
- augment_configs/pretrain/datasets/starcoder_seqlen4096.yml

determined:
  name: arun-830M-llama-pretrain-80Btokens-seqlen4096-batch768K  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0
  save_trial_latest: 2

overrides:
  wandb_name: arun-830M-llama-pretrain-80Btokens-seqlen4096-batch768K  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  load: null
  shuffle_direct_dataset: True

  # 768k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 24
  gradient_accumulation_steps: 1
  train_batch_size: 192   # 24 * 8

  # 80B tokens ~ 768k tokens @ 108k steps
  train_iters: 108_000
  lr_decay_style: cosine
  lr_decay_iters: null
  warmup_iters: 1_300  # ~1B ~= 1.3k iters

  save_interval: 5000  # Don't save too often

  # Optimization: GPT3-LR * 1.5 (for linear scaling)
  min_lr: 3.75e-5
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 3.75e-4  # GPT-3 LR
    type: Adam

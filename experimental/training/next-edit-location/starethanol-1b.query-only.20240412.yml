determined:
  description: null
  workspace: Dev
  project: next-edit-location

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 16
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: seth1b-bs{train_options.batch_size}x{train_options.gradient_accumulation_steps}x16-lr{train_options.optimizer.learning_rate:.0e}-epochs{max_epochs}-K128-onepos
  wandb_project: next-edit-location
  wandb_group: starethanol-1b.query-only.20240412

  components:
    model:
      component_name: research.fastbackward.train_retriever.create_dual_encoder_with_tokenizer
      query_model: query_model
      doc_model: doc_model
      tokenizer: tokenizer
      freeze_document_model: True
    query_model:
      component_name: research.fastbackward.checkpointing.neox.load_starethanol_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    doc_model:
      component_name: research.fastbackward.checkpointing.neox.load_starethanol_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    loss_fn:
      component_name: research.fastbackward.retrieval_models.PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 100.0
        logits_scale: 1.0
        learnable_logits_scale: True

  eval_interval: 10
  checkpoint_interval: 100

  max_epochs: 2
  train_options:
    batch_size: 8
    gradient_accumulation_steps: 1
    max_iters: 0
    log_interval: 1
    grad_clip: 1.0

    optimizer:
      warmup_iters: 20
      learning_rate: 2.0e-5
      min_lr: 2.0e-6

  train_data:
    path: /mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/12-04-2024/next-edit-location-indexed-dataset-new/dataset.train
    tokenizer_name: starcoder
    documents_per_batch: 128
    max_positive_count: 1

  eval_data:
    path: /mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/12-04-2024/next-edit-location-indexed-dataset-new/dataset.val
    tokenizer_name: starcoder
    limit: 1024
    documents_per_batch: 128

# Train scripts for RavenV6.x
# Models are versioned based on their dataset and model configuration.
#

determined:
  description: null
  workspace: Dev
  project: next-edit-location

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: raven1b.v15-tied-1pos-bs{train_options.batch_size}x{train_options.gradient_accumulation_steps}x16-lr{train_options.optimizer.learning_rate:.0e}-iters{train_options.max_iters}-K{train_data.documents_per_batch}
  wandb_project: next-edit-location
  # wandb_group: raven1b.v9

  components:
    model:
      component_name: create_dual_encoder_with_tokenizer
      query_model: query_model
      # doc_model: doc_model
      doc_model: query_model
      tokenizer: tokenizer
      # freeze_document_model: True
    query_model:
      component_name: neox.load_starethanol_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    # doc_model:
    #   component_name: neox.load_starethanol_checkpoint
    #   checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    loss_fn:
      component_name: PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 1000.0
        logits_scale: 1.0
        learnable_logits_scale: True

  eval_interval: 10
  checkpoint_interval: 100

  max_epochs: -1
  train_options:
    # Total batch size should be 256 = 8 x 32
    batch_size: 1
    gradient_accumulation_steps: 8
    max_iters: 2000
    log_interval: 1
    grad_clip: 1.0

    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
  eval_batch_size: 8

  train_data:
    path: /mnt/efs/spark-data/user/arun/next-edit-location/S1.2_prs_2k.keepmost.filter,R1.1_uniform-128,T1.1_5-15lines.downsample10,indexed_dataset/dataset
    tokenizer_name: starcoder
    documents_per_batch: 128
    max_positive_count: 1

  eval_data:
    # Val data TBD
    path: /mnt/efs/spark-data/user/arun/data-pipeline/pr-next-edit-location-v9-wip-sampler-fixed/next-edit-location-validation-indexed-dataset/dataset
    tokenizer_name: starcoder
    limit: 1024
    documents_per_batch: 128

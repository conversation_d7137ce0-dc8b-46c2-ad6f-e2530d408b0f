determined:
  description: null
  workspace: Dev
  project: next-edit-location

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: raven1b.v7-reranker-1pos-bs{train_options.batch_size}x{train_options.gradient_accumulation_steps}x16-lr{train_options.optimizer.learning_rate:.0e}-iters{train_options.max_iters}-K{train_data.documents_per_batch}
  wandb_project: next-edit-location
  wandb_group: raven1b.v7

  components:
    model:
      component_name: create_reranker_with_lm_and_tokenizer
      language_model: language_model
      tokenizer: tokenizer
    language_model:
      component_name: research.fastbackward.checkpointing.neox.load_starcoder_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint
      skip_output: True
    loss_fn:
      component_name: research.fastbackward.retrieval_models.PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 1000.0
        logits_scale: 1.0
        learnable_logits_scale: True
        # reduce_over_targets: sum

  eval_interval: 10
  checkpoint_interval: 100

  # max_epochs: 2
  train_options:
    batch_size: 1
    gradient_accumulation_steps: 4
    max_iters: 2000
    log_interval: 1
    grad_clip: 1.0

    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-4
      min_lr: 2.0e-5
  eval_batch_size: 1

  train_data:
    path: /mnt/efs/spark-data/user/arun/data-pipeline/pr-next-edit-location-v7-wip-sampler/next-edit-location-train-indexed-dataset/dataset
    tokenizer_name: starcoder
    documents_per_batch: 64
    max_query_tokens: 3072
    max_positive_count: 1
    format_for_reranking: True

  eval_data:
    path: /mnt/efs/spark-data/user/arun/data-pipeline/pr-next-edit-location-v7-wip-sampler/next-edit-location-validation-indexed-dataset/dataset
    tokenizer_name: starcoder
    limit: 1024
    max_query_tokens: 3072
    documents_per_batch: 64
    format_for_reranking: True

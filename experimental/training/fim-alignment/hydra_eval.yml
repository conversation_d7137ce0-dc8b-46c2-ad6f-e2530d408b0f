#
# Evaluate the FIM aligned models on humaneval_fim.
#

system:
  name: basic_rag
  model:
    name: null
    checkpoint_path: null
    prompt:
      max_prefix_tokens: 2048
      max_suffix_tokens: 2048
      max_prompt_tokens: 3700
      retrieval_layout_style: comment
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: null
    chunker: line_level
    max_chunk: 20
    max_query_lines: 20
  experimental:
    remove_suffix: False
    retriever_top_k: 25
    trim_on_dedent: True
    trim_on_max_lines: null
    trim_trailing_newline_on_prefix: True

task:
  name: hydra
  dataset: repoeval_functions

podspec: A40.yaml

determined:
  name: null
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

interventions:
  experiment_name: fima_retrieval_experiments

  sweep_generative_models:
    keys:
      - system.model.name
      - system.model.checkpoint_path
    values:
      # Baselines
      - [starcoderbase-1b, null]
      - [starcoderbase-3b, null]
      - [starcoderbase-7b, null]
      - [starcoderbase-16b, null]
      # Checkpoints
      - [starcoderbase-1b, "arun/starcoderfima-1b"]
      - [starcoderbase-3b, "arun/starcoderfima-3b"]
      - [starcoderbase-7b, "arun/starcoderfima-7b"]
      - [starcoderbase-16b, "arun/starcoderfima-16b-8lang"]
      - [starcoderbase-16b, "arun/starcoderfima-16b-largebatch"]

  sweep_retrievers:
    keys:
      - system.retriever.name
    values:
      - [null]
      - [diff_boykin]

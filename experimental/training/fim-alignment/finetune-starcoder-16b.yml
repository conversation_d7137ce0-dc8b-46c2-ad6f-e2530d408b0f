# Configuration for FIM alignment finetuning on ~4B tokens across 8 languages.

includes:
  - augment_configs/starcoder/model/starcoder.yml
determined:
  name: fim-alignment-starcoder-16B-all-large-batch
  workspace: Dev
  project: arun
  labels: ["starcoder", "fim-alignment"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: fim-alignment-starcoder-16B-all-large-batch
  wandb_group: arun # change this to your group
  wandb_project: new-FIM-sampler

  # data (sequence lengths are 8192 + 1)
  train_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/train",
    ]
  valid_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/valid",
    ]
  max_valid_data_size: 2_048
  test_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/test",
    ]
  data_impl: mmap
  dataset_type: direct

  # 512 * 8k = 4M tokens per step
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 64
  train_batch_size: 512   # 2 * 64 * (8 / 2) GPUs

  # 1k * 4M = 4B tokens = 1 epoch
  train_iters: 1000
  warmup: 0.01
  lr_decay_style: constant

  optimizer:
    params:
      betas:
        - 0.9
        - 0.95
      eps: 1.0e-08
      # NOTE(arun): StarCoder trained finished with an LR of 3e-5, but using a batch
      # size of 4M tokens. Linear scaling says this should be ~1e-6.
      lr: 3.0e-5
    type: Adam

  weight-decay: 0.1

  # Eval/save frequency
  eval_interval: 500
  save_interval: 500
  log-interval: 100

  # FIM context loss mask
  loss_mask_mode: pad
  extra_loss_masks:
    - pad
    - fim-context
    - eod-only

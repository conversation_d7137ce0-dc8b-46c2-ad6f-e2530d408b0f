#
# Evaluate the FIM aligned models on humaneval_fim.
#

system:
  name: basic
  model:
    name: starcoderbase-1b
    checkpoint_path: arun/starcoderfima-1b
    prompt:
      # Provide sufficient prefix/suffix for most of the examples.
      max_prefix_tokens: 3072
      max_suffix_tokens: 3072
      max_prompt_tokens: 7912
  generation_options:
    temperature: 0
    max_generated_tokens: 128

task:
  name: humaneval_fim
  variant: multiline
  # limit: 10
  exec: True
  iterations: 1

# Podspec - set the default podspec for all checkpoints
# Use the following for larger models (>=2B)
podspec: A40.yaml
# podspec: 1xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
determined:
  name: null
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

interventions:
  experiment_name: starcoder_fima_experiments

  sweep_generative_models:
    keys:
      - system.model.name
      - system.model.checkpoint_path
    values:
      # Baselines
      - [starcoderbase-1b, null]
      - [starcoderbase-3b, null]
      - [starcoderbase-7b, null]
      - [starcoderbase-16b, null]
      # Checkpoints
      - [starcoderbase-1b, "arun/starcoderfima-1b"]
      - [starcoderbase-3b, "arun/starcoderfima-3b"]
      - [starcoderbase-7b, "arun/starcoderfima-7b"]
      - [starcoderbase-16b, "arun/starcoderfima-16b-8lang"]
      - [starcoderbase-16b, "arun/starcoderfima-16b-largebatch"]

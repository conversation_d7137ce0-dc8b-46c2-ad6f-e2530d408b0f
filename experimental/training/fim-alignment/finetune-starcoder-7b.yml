# Configuration for FIM alignment finetuning on ~4B tokens across 8 languages.

includes:
  - augment_configs/starcoder/model/starcoder.yml
  - augment_configs/starcoder/model/starcoder-7b.yml
determined:
  name: fim-alignment-starcoder-7B-all-4Btoks
  workspace: Dev
  project: arun
  labels: ["starcoder", "fim-alignment"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: fim-alignment-starcoder-7B-all-4Btoks
  wandb_group: arun # change this to your group
  wandb_project: new-FIM-sampler

  # data (sequence lengths are 8192 + 1)
  train_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/train",
    ]
  valid_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/valid",
    ]
  max_valid_data_size: 1_600
  test_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/test",
    ]
  data_impl: mmap
  dataset_type: direct

  # 64 * 8k = 0.5M tokens per step
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 2
  train_batch_size: 64   # 2 * 4 * 8 GPUs

  # 2k * 0.5M = 1B tokens = ~0.5 epoch
  train_iters: 8000
  warmup: 0.01
  lr_decay_style: constant

  optimizer:
    params:
      betas:
        - 0.9
        - 0.95
      eps: 1.0e-08
      lr: 1.6e-05
    type: Adam

  weight-decay: 0.1

  # Eval/save frequency
  eval_interval: 500
  save_interval: 500
  log-interval: 100

  # FIM context loss mask
  loss_mask_mode: pad
  extra_loss_masks:
    - pad
    - fim-context
    - eod-only

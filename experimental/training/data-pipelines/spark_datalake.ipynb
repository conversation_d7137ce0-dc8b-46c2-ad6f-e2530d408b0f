{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Exploring the datalake with Spark on CW Kubernetes\n", "======\n", "Note that this would take ~40min total to execute, mostly compute time not human time."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/11/20 04:49:19 WARN Utils: Your hostname, xz-temp resolves to a loopback address: *********; using ************** instead (on interface enp4s0)\n", "23/11/20 04:49:19 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "23/11/20 04:49:21 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["from research.data.spark import k8s_session\n", "\n", "# this util function creates an autoscaled spark cluster on the CW environment\n", "spark = k8s_session(min_workers=10, max_workers=50)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/11/20 04:49:58 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>content</th><th>is_binary</th><th>path</th><th>pct_modified</th><th>repo_id</th><th>repo_name</th><th>repo_root</th><th>sha</th><th>size</th><th>part</th><th>lang</th><th>is_head</th><th>avg_line_length</th><th>max_line_length</th><th>alphanum_fraction</th><th>date</th></tr>\n", "<tr><td>About pyramids\\n==============\\n\\nHome: https://github.com/MAfarrag/pyramids\\n\\nPackage license: ...</td><td>false</td><td>README.md</td><td>200</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>30bf87831f3b75ce48f5641a021a866c4725fd1a</td><td>5914</td><td>2023-06-25-18</td><td>markdown</td><td>true</td><td>37.16129032258065</td><td>490</td><td>0.735711870138654</td><td>2023-06-25</td></tr>\n", "<tr><td>### GNU GENERAL PUBLIC LICENSE\\n\\nVersion 3, 29 June 2007\\n\\nCopyright (C) 2007 Free Software Fou...</td><td>false</td><td>recipe/LICENSE.md</td><td>200</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>2fb2e74d8d7fa1c9286b18af0afa5c00402f56e3</td><td>34916</td><td>2023-06-25-18</td><td>markdown</td><td>true</td><td>50.65236686390533</td><td>82</td><td>0.7962538664222706</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.4.1&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>832f8f02482d725da8627e58244602617d515632:recipe/meta.yaml</td><td>28</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>bcdcec333a16ed6ca3f05d66a576a114e7b7fec0</td><td>1110</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>19.962264150943398</td><td>74</td><td>0.6234234234234234</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.4.0&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>ef8a508aa13ce2aeb9df9933c51162389b6abcd4:recipe/meta.yaml</td><td>24</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>39db5bf368331905a5cd8dd5f1a64a4f1b8ee869</td><td>1111</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>19.59259259259259</td><td>74</td><td>0.6228622862286228</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.3.3&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>a241f9ceebc27edeee86557922544ddd4830a9c8:recipe/meta.yaml</td><td>43</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>5645ce7231b7df2c7b7d9ec3c933e4edb1fd1280</td><td>1266</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>19.770491803278688</td><td>74</td><td>0.6018957345971564</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.3.2&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>692a908caac0e2183d2321cc84b44036a863cd79:recipe/meta.yaml</td><td>27</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>da77a5aa269f9ec0e0732dad55d091c92a95149f</td><td>1266</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>19.770491803278688</td><td>74</td><td>0.6018957345971564</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.3.1&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>2d3ea026c86905c4dae99926e8d2def8e337c1df:recipe/meta.yaml</td><td>28</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>6afc8be15cb43677c478539917007833bbfbadc0</td><td>1266</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>19.770491803278688</td><td>74</td><td>0.6018957345971564</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.3.0&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>0ae4f05b1ad1ccbc3157664aed58810d45b1609f:recipe/meta.yaml</td><td>26</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>433827822c618d1be06b1b1c1be39b08c815eb71</td><td>1222</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.086206896551722</td><td>74</td><td>0.6088379705400983</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.12&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...</td><td>false</td><td>94f3b023c777e020384e95c123125de6818600f8:recipe/meta.yaml</td><td>23</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>d86034a80b8063835fb5986692d4eefdb7093338</td><td>1204</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.140350877192983</td><td>74</td><td>0.6121262458471761</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.11&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...</td><td>false</td><td>9bca6b697645ca0b3a54cf23a28b2ed4536d13ea:recipe/meta.yaml</td><td>27</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>f7b5b469072a627abb540aa7032f3389988b9ccd</td><td>1204</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.140350877192983</td><td>74</td><td>0.6121262458471761</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.10&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...</td><td>false</td><td>2f246c1f79c559c0770278990c27315b4ee12e3b:recipe/meta.yaml</td><td>29</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>0eb33f7251110abcaa03032b3335c68ed115c898</td><td>1250</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.203389830508474</td><td>74</td><td>0.6064</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.10&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...</td><td>false</td><td>f014e5ef97e0c8955c047104634b7ec022aaff0a:recipe/meta.yaml</td><td>25</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>088761bfaa0392cb6e58ab26a429159e8d6cab6a</td><td>1249</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.1864406779661</td><td>74</td><td>0.6068855084067254</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.9&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>4b93bd0bdfb6a799cdfb0971f5af6bd8632478dd:recipe/meta.yaml</td><td>69</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>8c2a88dedea42050cf6bd5063ca5dcce1743ff79</td><td>1238</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.736842105263158</td><td>74</td><td>0.598546042003231</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.9&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>cca99383cce0ed404ab5a374e6060dde23002e8d:recipe/meta.yaml</td><td>25</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>f7331efd6fc24b295e4ff7673da3ff8256303dbb</td><td>1238</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.736842105263158</td><td>74</td><td>0.598546042003231</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.8&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>7a484318c6eae2f0732688f452f2dcae5edf9ccb:recipe/meta.yaml</td><td>68</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>b212788d69d093cef2648ec24f84fa631ab6c444</td><td>1184</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.160714285714285</td><td>74</td><td>0.6148648648648649</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.8&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>cf66f02d34e8870ed6a43a635279538156087d9e:recipe/meta.yaml</td><td>25</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>c55c8c87bac69105ca4677bb86364de3c90d9643</td><td>1184</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.160714285714285</td><td>74</td><td>0.6148648648648649</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.7&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>30425ce093735bdc549616f9da52f28e39da35f3:recipe/meta.yaml</td><td>31</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>bd7634f0b40a90add115ab8779d96b7ee3f52a7b</td><td>1280</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.35</td><td>74</td><td>0.60234375</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.7&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>25a95f8f494df0b69853b17518b46364f3cd5ecd:recipe/meta.yaml</td><td>25</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>4ea8609356ffdd689c8c593bed85027dbb21ba58</td><td>1280</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.35</td><td>74</td><td>0.60234375</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.6&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>3dec169c15499a900207cff77d9210a669c1af68:recipe/meta.yaml</td><td>38</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>b8dc03f9f37a7ce87d9dd2a3b6d3c6598e2ddd5d</td><td>1276</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.283333333333335</td><td>74</td><td>0.60423197492163</td><td>2023-06-25</td></tr>\n", "<tr><td>{% set name = &quot;pyramids&quot; %}\\n{% set version = &quot;0.2.5&quot; %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...</td><td>false</td><td>5bff40cb0da86c89b5443bc95b5b09b6eaf81901:recipe/meta.yaml</td><td>28</td><td>656e46fc-f3af-41f3-b559-996c536f2ba7</td><td>conda-forge/pyramids-feedstock</td><td>github/497422129</td><td>962c7306d0c2e867da104525cb7fad6bcd06a556</td><td>1276</td><td>2023-06-25-18</td><td>yaml</td><td>false</td><td>20.283333333333335</td><td>74</td><td>0.60423197492163</td><td>2023-06-25</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+----------------------------------------------------------------------------------------------------+---------+---------------------------------------------------------+------------+------------------------------------+------------------------------+----------------+----------------------------------------+-----+-------------+--------+-------+------------------+---------------+------------------+----------+\n", "|                                                                                             content|is_binary|                                                     path|pct_modified|                             repo_id|                     repo_name|       repo_root|                                     sha| size|         part|    lang|is_head|   avg_line_length|max_line_length| alphanum_fraction|      date|\n", "+----------------------------------------------------------------------------------------------------+---------+---------------------------------------------------------+------------+------------------------------------+------------------------------+----------------+----------------------------------------+-----+-------------+--------+-------+------------------+---------------+------------------+----------+\n", "|About pyramids\\n==============\\n\\nHome: https://github.com/MAfarrag/pyramids\\n\\nPackage license: ...|    false|                                                README.md|         200|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|30bf87831f3b75ce48f5641a021a866c4725fd1a| 5914|2023-06-25-18|markdown|   true| 37.16129032258065|            490| 0.735711870138654|2023-06-25|\n", "|### GNU GENERAL PUBLIC LICENSE\\n\\nVersion 3, 29 June 2007\\n\\nCopyright (C) 2007 Free Software Fou...|    false|                                        recipe/LICENSE.md|         200|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|2fb2e74d8d7fa1c9286b18af0afa5c00402f56e3|34916|2023-06-25-18|markdown|   true| 50.65236686390533|             82|0.7962538664222706|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.4.1\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|832f8f02482d725da8627e58244602617d515632:recipe/meta.yaml|          28|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|bcdcec333a16ed6ca3f05d66a576a114e7b7fec0| 1110|2023-06-25-18|    yaml|  false|19.962264150943398|             74|0.6234234234234234|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.4.0\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|ef8a508aa13ce2aeb9df9933c51162389b6abcd4:recipe/meta.yaml|          24|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|39db5bf368331905a5cd8dd5f1a64a4f1b8ee869| 1111|2023-06-25-18|    yaml|  false| 19.59259259259259|             74|0.6228622862286228|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.3.3\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|a241f9ceebc27edeee86557922544ddd4830a9c8:recipe/meta.yaml|          43|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|5645ce7231b7df2c7b7d9ec3c933e4edb1fd1280| 1266|2023-06-25-18|    yaml|  false|19.770491803278688|             74|0.6018957345971564|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.3.2\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|692a908caac0e2183d2321cc84b44036a863cd79:recipe/meta.yaml|          27|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|da77a5aa269f9ec0e0732dad55d091c92a95149f| 1266|2023-06-25-18|    yaml|  false|19.770491803278688|             74|0.6018957345971564|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.3.1\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|2d3ea026c86905c4dae99926e8d2def8e337c1df:recipe/meta.yaml|          28|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|6afc8be15cb43677c478539917007833bbfbadc0| 1266|2023-06-25-18|    yaml|  false|19.770491803278688|             74|0.6018957345971564|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.3.0\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|0ae4f05b1ad1ccbc3157664aed58810d45b1609f:recipe/meta.yaml|          26|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|433827822c618d1be06b1b1c1be39b08c815eb71| 1222|2023-06-25-18|    yaml|  false|20.086206896551722|             74|0.6088379705400983|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.12\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...|    false|94f3b023c777e020384e95c123125de6818600f8:recipe/meta.yaml|          23|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|d86034a80b8063835fb5986692d4eefdb7093338| 1204|2023-06-25-18|    yaml|  false|20.140350877192983|             74|0.6121262458471761|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.11\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...|    false|9bca6b697645ca0b3a54cf23a28b2ed4536d13ea:recipe/meta.yaml|          27|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|f7b5b469072a627abb540aa7032f3389988b9ccd| 1204|2023-06-25-18|    yaml|  false|20.140350877192983|             74|0.6121262458471761|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.10\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...|    false|2f246c1f79c559c0770278990c27315b4ee12e3b:recipe/meta.yaml|          29|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|0eb33f7251110abcaa03032b3335c68ed115c898| 1250|2023-06-25-18|    yaml|  false|20.203389830508474|             74|            0.6064|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.10\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n...|    false|f014e5ef97e0c8955c047104634b7ec022aaff0a:recipe/meta.yaml|          25|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|088761bfaa0392cb6e58ab26a429159e8d6cab6a| 1249|2023-06-25-18|    yaml|  false|  20.1864406779661|             74|0.6068855084067254|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.9\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|4b93bd0bdfb6a799cdfb0971f5af6bd8632478dd:recipe/meta.yaml|          69|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|8c2a88dedea42050cf6bd5063ca5dcce1743ff79| 1238|2023-06-25-18|    yaml|  false|20.736842105263158|             74| 0.598546042003231|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.9\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|cca99383cce0ed404ab5a374e6060dde23002e8d:recipe/meta.yaml|          25|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|f7331efd6fc24b295e4ff7673da3ff8256303dbb| 1238|2023-06-25-18|    yaml|  false|20.736842105263158|             74| 0.598546042003231|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.8\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|7a484318c6eae2f0732688f452f2dcae5edf9ccb:recipe/meta.yaml|          68|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|b212788d69d093cef2648ec24f84fa631ab6c444| 1184|2023-06-25-18|    yaml|  false|20.160714285714285|             74|0.6148648648648649|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.8\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|cf66f02d34e8870ed6a43a635279538156087d9e:recipe/meta.yaml|          25|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|c55c8c87bac69105ca4677bb86364de3c90d9643| 1184|2023-06-25-18|    yaml|  false|20.160714285714285|             74|0.6148648648648649|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.7\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|30425ce093735bdc549616f9da52f28e39da35f3:recipe/meta.yaml|          31|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|bd7634f0b40a90add115ab8779d96b7ee3f52a7b| 1280|2023-06-25-18|    yaml|  false|             20.35|             74|        0.60234375|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.7\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|25a95f8f494df0b69853b17518b46364f3cd5ecd:recipe/meta.yaml|          25|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|4ea8609356ffdd689c8c593bed85027dbb21ba58| 1280|2023-06-25-18|    yaml|  false|             20.35|             74|        0.60234375|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.6\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|3dec169c15499a900207cff77d9210a669c1af68:recipe/meta.yaml|          38|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|b8dc03f9f37a7ce87d9dd2a3b6d3c6598e2ddd5d| 1276|2023-06-25-18|    yaml|  false|20.283333333333335|             74|  0.60423197492163|2023-06-25|\n", "|{% set name = \"pyramids\" %}\\n{% set version = \"0.2.5\" %}\\n\\npackage:\\n  name: {{ name|lower }}\\n ...|    false|5bff40cb0da86c89b5443bc95b5b09b6eaf81901:recipe/meta.yaml|          28|656e46fc-f3af-41f3-b559-996c536f2ba7|conda-forge/pyramids-feedstock|github/497422129|962c7306d0c2e867da104525cb7fad6bcd06a556| 1276|2023-06-25-18|    yaml|  false|20.283333333333335|             74|  0.60423197492163|2023-06-25|\n", "+----------------------------------------------------------------------------------------------------+---------+---------------------------------------------------------+------------+------------------------------------+------------------------------+----------------+----------------------------------------+-----+-------------+--------+-------+------------------+---------------+------------------+----------+\n", "only showing top 20 rows"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# take a brief look at the file data\n", "file_df = spark.read.format('delta').load('s3a://augment-github/file_content/converted/')\n", "file_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# How much content do we have for each langauge in the most recent version (ie HEAD)\n", "import pyspark.sql.functions as F\n", "lang_stat = file_df.filter('is_head').groupBy('lang').agg(\n", "    F.sum(F.length('content')).alias('total_size'),\n", "    F.count('*').alias('files'),\n", ").toPandas().sort_values('total_size', ascending=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lang</th>\n", "      <th>total_size</th>\n", "      <th>files</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>315</th>\n", "      <td></td>\n", "      <td>1795669917133</td>\n", "      <td>86732954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>html</td>\n", "      <td>963056812670</td>\n", "      <td>23994267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>310</th>\n", "      <td>text</td>\n", "      <td>923386630434</td>\n", "      <td>19645674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>json</td>\n", "      <td>821446257587</td>\n", "      <td>53366807</td>\n", "    </tr>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>jupyter notebook</td>\n", "      <td>686046282761</td>\n", "      <td>1151004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>javascript</td>\n", "      <td>654195498523</td>\n", "      <td>19724894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>object code</td>\n", "      <td>522243117892</td>\n", "      <td>2136177</td>\n", "    </tr>\n", "    <tr>\n", "      <th>300</th>\n", "      <td>linker map</td>\n", "      <td>285450758810</td>\n", "      <td>2107102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>unity3d asset</td>\n", "      <td>244568380628</td>\n", "      <td>13471718</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>svg</td>\n", "      <td>190171525741</td>\n", "      <td>7029571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>227</th>\n", "      <td>c</td>\n", "      <td>181194986178</td>\n", "      <td>11225458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>c++</td>\n", "      <td>168323617033</td>\n", "      <td>6941967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308</th>\n", "      <td>java</td>\n", "      <td>163267498004</td>\n", "      <td>20178557</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>css</td>\n", "      <td>107820485209</td>\n", "      <td>3553321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>343</th>\n", "      <td>python</td>\n", "      <td>104230319925</td>\n", "      <td>13218876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>markdown</td>\n", "      <td>102906344253</td>\n", "      <td>20858113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>xml</td>\n", "      <td>85788256420</td>\n", "      <td>11887701</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>typescript</td>\n", "      <td>80661980434</td>\n", "      <td>12752942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>php</td>\n", "      <td>78168964367</td>\n", "      <td>10964305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>pdf</td>\n", "      <td>70698937554</td>\n", "      <td>2978927</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 lang     total_size     files\n", "315                    1795669917133  86732954\n", "95               html   963056812670  23994267\n", "310              text   923386630434  19645674\n", "113              json   821446257587  53366807\n", "307  jupyter notebook   686046282761   1151004\n", "140        javascript   654195498523  19724894\n", "250       object code   522243117892   2136177\n", "300        linker map   285450758810   2107102\n", "166     unity3d asset   244568380628  13471718\n", "191               svg   190171525741   7029571\n", "227                 c   181194986178  11225458\n", "209               c++   168323617033   6941967\n", "308              java   163267498004  20178557\n", "142               css   107820485209   3553321\n", "343            python   104230319925  13218876\n", "91           markdown   102906344253  20858113\n", "164               xml    85788256420  11887701\n", "155        typescript    80661980434  12752942\n", "319               php    78168964367  10964305\n", "206               pdf    70698937554   2978927"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["lang_stat.head(20)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# how many Python tokens do we get a day?\n", "\n", "daily_stat = file_df.filter(\n", "    F.col('is_head') & (F.col('lang') == 'python')\n", ").groupBy('date').agg(\n", "    F.sum(F.length('content')).alias('total_size'),\n", "    F.count('*').alias('files'),\n", ").toPandas().sort_values('date')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>total_size</th>\n", "      <th>files</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2023-06-24</td>\n", "      <td>630922224</td>\n", "      <td>43882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-06-25</td>\n", "      <td>1505088239</td>\n", "      <td>147240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>2023-06-26</td>\n", "      <td>614406685</td>\n", "      <td>69295</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-06-27</td>\n", "      <td>1243984806</td>\n", "      <td>153853</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>2023-06-28</td>\n", "      <td>4600074913</td>\n", "      <td>470233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2023-11-16</td>\n", "      <td>378588410</td>\n", "      <td>48773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>2023-11-17</td>\n", "      <td>353427286</td>\n", "      <td>52910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>2023-11-18</td>\n", "      <td>306377977</td>\n", "      <td>46707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2023-11-19</td>\n", "      <td>179682613</td>\n", "      <td>27282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-11-20</td>\n", "      <td>230421104</td>\n", "      <td>15985</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>135 rows × 3 columns</p>\n", "</div>"], "text/plain": ["           date  total_size   files\n", "29   2023-06-24   630922224   43882\n", "19   2023-06-25  1505088239  147240\n", "99   2023-06-26   614406685   69295\n", "7    2023-06-27  1243984806  153853\n", "111  2023-06-28  4600074913  470233\n", "..          ...         ...     ...\n", "25   2023-11-16   378588410   48773\n", "64   2023-11-17   353427286   52910\n", "120  2023-11-18   306377977   46707\n", "21   2023-11-19   179682613   27282\n", "13   2023-11-20   230421104   15985\n", "\n", "[135 rows x 3 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["daily_stat"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}
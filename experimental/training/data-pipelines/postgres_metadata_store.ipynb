{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Explore the PosgreSQL metastore\n", "======\n", "Access to the metastore is preconfitured on a CoreWeave dev container.   The service can also be\n", "accessed on the internet at `metastore.tenant-augment-eng.coreweave.cloud` with proper credentials.\n", "Use the Kubernetes sercret manager or check the [app](`https://apps.coreweave.com/#/c/default/ns/tenant-augment-eng/apps/helm.packages/v1alpha1/metastore`) for the password.\n", "\n", "Note: if you are familiar with postgres terminal, you can simply run `psql` without any arguments on the dev container to connect to the metastore with the terminal instead."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ResultRow(table_name='vendor_user'),\n", " ResultRow(table_name='repo'),\n", " ResultRow(table_name='file_content'),\n", " ResultRow(table_name='repo_commit'),\n", " ResultRow(table_name='tmp_new_q'),\n", " ResultRow(table_name='repo_file'),\n", " ResultRow(table_name='file_updates'),\n", " ResultRow(table_name='commit_updates'),\n", " ResultRow(table_name='repo_storage'),\n", " ResultRow(table_name='batch_job'),\n", " ResultRow(table_name='git_commit'),\n", " ResultRow(table_name='repo_download_queue'),\n", " ResultRow(table_name='tmp_download_q'),\n", " ResultRow(table_name='repo_metric'),\n", " ResultRow(table_name='license_info'),\n", " ResultRow(table_name='bot_api_key'),\n", " ResultRow(table_name='license_override')]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import research.data.postgres_metastore as pg\n", "\n", "# Show all tables available in the metastore\n", "pg.execute(\"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>owner</th>\n", "      <th>repo_name</th>\n", "      <th>description</th>\n", "      <th>first_seen_at</th>\n", "      <th>created_at</th>\n", "      <th>updated_at</th>\n", "      <th>license</th>\n", "      <th>size_kb</th>\n", "      <th>stars</th>\n", "      <th>...</th>\n", "      <th>additional_info</th>\n", "      <th>is_root</th>\n", "      <th>vendor_name</th>\n", "      <th>vendor_record_id</th>\n", "      <th>source_record_id</th>\n", "      <th>last_response_code</th>\n", "      <th>source</th>\n", "      <th>vcs_type</th>\n", "      <th>canonical_name</th>\n", "      <th>canonical_owner</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6790cbf4-9a1d-49f7-b8a4-a24ba7cdcc3d</td>\n", "      <td>MorningBells</td>\n", "      <td>podman</td>\n", "      <td>Podman: A tool for managing OCI containers and...</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2023-06-13 01:35:04+00:00</td>\n", "      <td>2023-06-13 01:35:04+00:00</td>\n", "      <td>apache-2.0</td>\n", "      <td>141251</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>652879958</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/podman</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ecb4ecd2-4153-46ff-860d-3fb35e31717d</td>\n", "      <td>MorningBells</td>\n", "      <td>Power-Plant-Inspector</td>\n", "      <td>清华大学技创辅82项目：电厂巡检机器小车研究数据集</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2022-05-18 02:11:36+00:00</td>\n", "      <td>2021-07-06 15:47:29+00:00</td>\n", "      <td>mit</td>\n", "      <td>19194</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>493477181</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/power-plant-inspector</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1606ddbb-ed6f-4623-b189-e8ef7a415c9c</td>\n", "      <td>MorningBells</td>\n", "      <td>practical-programming-books</td>\n", "      <td>这里收录比较实用的计算机相关技术书籍，可以在短期之内入门的简单实用教程、一些技术网站以及一些...</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2019-12-21 16:02:10+00:00</td>\n", "      <td>2019-12-21 16:02:12+00:00</td>\n", "      <td>apache-2.0</td>\n", "      <td>771</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>229450675</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/practical-programming-books</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>e36edcc3-f4ce-496c-8281-2c813425d885</td>\n", "      <td>MorningBells</td>\n", "      <td>Program-system</td>\n", "      <td>C/C++学生管理系统, 数据结构, 基于Opencv的车牌识别系统</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2022-05-18 01:45:25+00:00</td>\n", "      <td>2022-05-15 06:13:01+00:00</td>\n", "      <td>mit</td>\n", "      <td>13186</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>493471008</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/program-system</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>63e5b824-7200-40fa-bcbc-afe852eb47f2</td>\n", "      <td>MorningBells</td>\n", "      <td>project_framework</td>\n", "      <td>None</td>\n", "      <td>2023-07-29 05:19:37.552809+00:00</td>\n", "      <td>2018-05-28 09:45:23+00:00</td>\n", "      <td>2019-05-07 02:57:35+00:00</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>True</td>\n", "      <td>github</td>\n", "      <td>135141721</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>gh-archive</td>\n", "      <td>git</td>\n", "      <td>morningbells/project_framework</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0b8b8b8d-fdac-4f5d-a0a2-2bb58754d472</td>\n", "      <td>MorningBells</td>\n", "      <td>prowide-iso20022-examples</td>\n", "      <td>Source code examples for \"Prowide ISO 20022\", ...</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2021-12-17 10:16:47+00:00</td>\n", "      <td>2021-12-17 10:16:48+00:00</td>\n", "      <td>apache-2.0</td>\n", "      <td>159</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>439291606</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/prowide-iso20022-examples</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2a718e78-f231-4b93-b1be-591db1e4c6ce</td>\n", "      <td>MorningBells</td>\n", "      <td>pyfolio</td>\n", "      <td>Portfolio and risk analytics in Python</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2022-01-18 08:59:59+00:00</td>\n", "      <td>2022-01-18 02:06:53+00:00</td>\n", "      <td>apache-2.0</td>\n", "      <td>87861</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>449211058</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/pyfolio</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>09884e5e-005f-4eed-a413-a6b8cacc464e</td>\n", "      <td>MorningBells</td>\n", "      <td>python</td>\n", "      <td>利用python来分析一些财务报表数据</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2021-12-30 09:49:17+00:00</td>\n", "      <td>2021-12-30 09:49:18+00:00</td>\n", "      <td>None</td>\n", "      <td>18096</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>443032725</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/python</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3f4f9d3a-0fec-458f-95d9-7c4cc2c2d024</td>\n", "      <td>MorningBells</td>\n", "      <td>python-sms-activate-ru</td>\n", "      <td>Wrapper for automatic SMS receiving by sms-act...</td>\n", "      <td>2023-08-28 16:10:16.662360+00:00</td>\n", "      <td>2023-02-13 02:44:33+00:00</td>\n", "      <td>2023-01-03 00:49:38+00:00</td>\n", "      <td>apache-2.0</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>False</td>\n", "      <td>github</td>\n", "      <td>600942897</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>collateral</td>\n", "      <td>git</td>\n", "      <td>morningbells/python-sms-activate-ru</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5c6fc5e1-0277-430a-a420-be3ae6efdd31</td>\n", "      <td>MorningBells</td>\n", "      <td>Qbot</td>\n", "      <td>None</td>\n", "      <td>2023-07-02 07:45:02.776575+00:00</td>\n", "      <td>2022-12-28 15:22:41+00:00</td>\n", "      <td>2022-12-28 15:22:41+00:00</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>{}</td>\n", "      <td>True</td>\n", "      <td>github</td>\n", "      <td>583037780</td>\n", "      <td>None</td>\n", "      <td>200</td>\n", "      <td>gh-archive</td>\n", "      <td>git</td>\n", "      <td>morningbells/qbot</td>\n", "      <td>morningbells</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 28 columns</p>\n", "</div>"], "text/plain": ["                                     id         owner  \\\n", "0  6790cbf4-9a1d-49f7-b8a4-a24ba7cdcc3d  MorningBells   \n", "1  ecb4ecd2-4153-46ff-860d-3fb35e31717d  MorningBells   \n", "2  1606ddbb-ed6f-4623-b189-e8ef7a415c9c  <PERSON>B<PERSON><PERSON>   \n", "3  e36edcc3-f4ce-496c-8281-2c813425d885  <PERSON>B<PERSON>s   \n", "4  63e5b824-7200-40fa-bcbc-afe852eb47f2  MorningBells   \n", "5  0b8b8b8d-fdac-4f5d-a0a2-2bb58754d472  MorningBells   \n", "6  2a718e78-f231-4b93-b1be-591db1e4c6ce  MorningBells   \n", "7  09884e5e-005f-4eed-a413-a6b8cacc464e  MorningBells   \n", "8  3f4f9d3a-0fec-458f-95d9-7c4cc2c2d024  MorningBells   \n", "9  5c6fc5e1-0277-430a-a420-be3ae6efdd31  MorningBells   \n", "\n", "                     repo_name  \\\n", "0                       podman   \n", "1        Power-Plant-Inspector   \n", "2  practical-programming-books   \n", "3               Program-system   \n", "4            project_framework   \n", "5    prowide-iso20022-examples   \n", "6                      pyfolio   \n", "7                       python   \n", "8       python-sms-activate-ru   \n", "9                         Qbot   \n", "\n", "                                         description  \\\n", "0  Podman: A tool for managing OCI containers and...   \n", "1                          清华大学技创辅82项目：电厂巡检机器小车研究数据集   \n", "2  这里收录比较实用的计算机相关技术书籍，可以在短期之内入门的简单实用教程、一些技术网站以及一些...   \n", "3                 C/C++学生管理系统, 数据结构, 基于Opencv的车牌识别系统   \n", "4                                               None   \n", "5  Source code examples for \"Prowide ISO 20022\", ...   \n", "6             Portfolio and risk analytics in Python   \n", "7                                利用python来分析一些财务报表数据   \n", "8  Wrapper for automatic SMS receiving by sms-act...   \n", "9                                               None   \n", "\n", "                     first_seen_at                created_at  \\\n", "0 2023-08-28 16:10:16.662360+00:00 2023-06-13 01:35:04+00:00   \n", "1 2023-08-28 16:10:16.662360+00:00 2022-05-18 02:11:36+00:00   \n", "2 2023-08-28 16:10:16.662360+00:00 2019-12-21 16:02:10+00:00   \n", "3 2023-08-28 16:10:16.662360+00:00 2022-05-18 01:45:25+00:00   \n", "4 2023-07-29 05:19:37.552809+00:00 2018-05-28 09:45:23+00:00   \n", "5 2023-08-28 16:10:16.662360+00:00 2021-12-17 10:16:47+00:00   \n", "6 2023-08-28 16:10:16.662360+00:00 2022-01-18 08:59:59+00:00   \n", "7 2023-08-28 16:10:16.662360+00:00 2021-12-30 09:49:17+00:00   \n", "8 2023-08-28 16:10:16.662360+00:00 2023-02-13 02:44:33+00:00   \n", "9 2023-07-02 07:45:02.776575+00:00 2022-12-28 15:22:41+00:00   \n", "\n", "                 updated_at     license  size_kb  stars  ...  additional_info  \\\n", "0 2023-06-13 01:35:04+00:00  apache-2.0   141251      0  ...               {}   \n", "1 2021-07-06 15:47:29+00:00         mit    19194      0  ...               {}   \n", "2 2019-12-21 16:02:12+00:00  apache-2.0      771      0  ...               {}   \n", "3 2022-05-15 06:13:01+00:00         mit    13186      0  ...               {}   \n", "4 2019-05-07 02:57:35+00:00        None        3      1  ...               {}   \n", "5 2021-12-17 10:16:48+00:00  apache-2.0      159      0  ...               {}   \n", "6 2022-01-18 02:06:53+00:00  apache-2.0    87861      0  ...               {}   \n", "7 2021-12-30 09:49:18+00:00        None    18096      0  ...               {}   \n", "8 2023-01-03 00:49:38+00:00  apache-2.0       26      0  ...               {}   \n", "9 2022-12-28 15:22:41+00:00        None        0      0  ...               {}   \n", "\n", "  is_root  vendor_name vendor_record_id source_record_id last_response_code  \\\n", "0   False       github        652879958             None                200   \n", "1   False       github        493477181             None                200   \n", "2   False       github        229450675             None                200   \n", "3   False       github        493471008             None                200   \n", "4    True       github        135141721             None                200   \n", "5   False       github        439291606             None                200   \n", "6   False       github        449211058             None                200   \n", "7   False       github        443032725             None                200   \n", "8   False       github        600942897             None                200   \n", "9    True       github        583037780             None                200   \n", "\n", "       source vcs_type                            canonical_name  \\\n", "0  collateral      git                       morningbells/podman   \n", "1  collateral      git        morningbells/power-plant-inspector   \n", "2  collateral      git  morningbells/practical-programming-books   \n", "3  collateral      git               morningbells/program-system   \n", "4  gh-archive      git            morningbells/project_framework   \n", "5  collateral      git    morningbells/prowide-iso20022-examples   \n", "6  collateral      git                      morningbells/pyfolio   \n", "7  collateral      git                       morningbells/python   \n", "8  collateral      git       morningbells/python-sms-activate-ru   \n", "9  gh-archive      git                         morningbells/qbot   \n", "\n", "   canonical_owner  \n", "0     morningbells  \n", "1     morningbells  \n", "2     morningbells  \n", "3     morningbells  \n", "4     morningbells  \n", "5     morningbells  \n", "6     morningbells  \n", "7     morningbells  \n", "8     morningbells  \n", "9     morningbells  \n", "\n", "[10 rows x 28 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check out some repos\n", "import pandas as pd\n", "\n", "\n", "pd.DataFrame(pg.execute(\"SELECT * FROM repo LIMIT 10\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}
"""
Reduce errors in performing user deletions by autogenrating the deletion commands.

Since user data is spread across many different systems and (as of writing) requires many commands
to be manually run, it is easy to make a mistake. This script generates the deletion commands for a
given user, so that the user can review the commands but doesn't need to copy and paste from
multiple locations to create them. You should *always* still verify the parameters are correct
before running the commands, and understand what the scripts are doing rather than blindly running
them. The commands generated may be out of date. When in doubt, refer to the user deletion runbook
or ask in the #team-growth channel.

User deletion runbook: https://www.notion.so/Runbook-User-Data-Deletion-119bba10175a801f8bc3d613d7f58769

Run using bazel: `bazel run //experimental/mpauly/user_data_deletion:deletion_commands -- <EMAIL>`

Note that this will not work unless you have PII access.
"""

import argparse
import os

from google.cloud import bigquery
from dataclasses import dataclass
from dataclasses_json import dataclass_json


CLOUD = "GCP_US_CENTRAL1_PROD"

QUERY = """SELECT MAX(user_id) as user_id, tenant, tenant_id, shard_namespace
FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
WHERE user_id = @user_id
GROUP BY tenant, tenant_id, shard_namespace;
"""


@dataclass_json
@dataclass
class UserInfo:
    user_id: str
    tenant: str
    tenant_id: str
    namespace: str


def get_user_info(user_id: str) -> list[UserInfo]:
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("user_id", "STRING", user_id),
        ]
    )
    query_job = bigquery.Client().query(QUERY, job_config=job_config)
    rows = list(query_job.result())
    if not rows:
        raise ValueError(f"User {user_id} not found")
    if len(rows) > 1:
        print(
            f"WARNING: Multiple rows found for {user_id}: {[row.tenant for row in rows]}"
        )
    assert all([r.user_id == user_id for r in rows])

    return [
        UserInfo(
            user_id=user_id,
            tenant=r.tenant,
            tenant_id=r.tenant_id,
            namespace=r.shard_namespace,
        )
        for r in rows
    ]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("user_id", help="User ID to delete")
    args = parser.parse_args()

    user_infos = get_user_info(args.user_id)
    print(user_infos)

    for info in user_infos:
        blobs_file = os.path.expanduser(f"~/blobs/{info.user_id.split('@')[0]}.txt")
        requests_file = os.path.expanduser(
            f"~/requests/{info.user_id.split('@')[0]}.txt"
        )
        sessions_file = os.path.expanduser(
            f"~/sessions/{info.user_id.split('@')[0]}.txt"
        )

        user_blobs_command = [
            "bazel",
            "run",
            "//tools/deletion_utils:cm_deletion_util",
            "--",
            "--cloud",
            CLOUD,
            "--namespace",
            info.namespace,
            "--tenant-name",
            info.tenant,
            "--request-token",
            "user_blobs",
            "--tenant-id",
            info.tenant_id,
            "--user-id",
            info.user_id,
            "--output-file",
            blobs_file,
        ]
        print()
        print("User blobs commnad:")
        print(" ".join(user_blobs_command))

        delete_blobs_command = [
            "bazel",
            "run",
            "//tools/deletion_utils:cm_deletion_util",
            "--",
            "--cloud",
            CLOUD,
            "--namespace",
            info.namespace,
            "--tenant-name",
            info.tenant,
            "--request-token",
            "delete_blobs",
            "--tenant-id",
            info.tenant_id,
            "--user-id",
            info.user_id,
            "--blob-names-file",
            blobs_file,
        ]
        print()
        print("Delete blobs commnad:")
        print(" ".join(delete_blobs_command))

        user_events_command = [
            "bazel",
            "run",
            "//tools/deletion_utils:user_events_util",
            "--",
            "--user-id",
            info.user_id,
            "--request-ids-file",
            requests_file,
            "--session-ids-file",
            sessions_file,
        ]
        print()
        print("User events command:")
        print(" ".join(user_events_command))

        delete_request_events_command = [
            "bazel",
            "run",
            "//tools/deletion_utils:gcs_deletion_util",
            "--",
            "requests",
            "--tenant-id",
            info.tenant_id,
            "--request-ids-file",
            requests_file,
        ]
        print()
        print("Delete request events command:")
        print(" ".join(delete_request_events_command))

        delete_session_events_command = [
            "bazel",
            "run",
            "//tools/deletion_utils:gcs_deletion_util",
            "--",
            "sessions",
            "--tenant-id",
            info.tenant_id,
            "--session-ids-file",
            sessions_file,
        ]
        print()
        print("Delete session events command:")
        print(" ".join(delete_session_events_command))


if __name__ == "__main__":
    main()

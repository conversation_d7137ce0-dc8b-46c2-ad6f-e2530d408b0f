"""
Confirm our ability to restore from a bigtable table backup. Since we're unable
to restore from a backup to an existing table, we must restore to a new table
(in this case dev-mpauly-content-manager-restore) then transfer all of the data
over with a script.
"""

from tqdm import tqdm
from google.cloud import bigtable


client = bigtable.Client("system-services-dev", admin=True)
instance = client.instance("bigtable-central-dev")

table = instance.table("dev-mpauly-content-manager")
table_backup = instance.table("dev-mpauly-content-manager-restore")

new_rows = []
for row in tqdm(table_backup.read_rows()):
    new_row = table.direct_row(row.row_key)
    for column_family, columns in row.cells.items():
        for column_name, cells in columns.items():
            for cell in cells:
                new_row.set_cell(
                    column_family, column_name, cell.value, timestamp=cell.timestamp
                )
    new_rows.append(new_row)

    # Write in batches
    if len(new_rows) >= 100:
        table.mutate_rows(new_rows)
        new_rows = []

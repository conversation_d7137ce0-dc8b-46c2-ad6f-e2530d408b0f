"""
Script with utilities for adding fake data into a bigtable table and confirming
its deletion.
"""

import random
from collections import defaultdict
from tqdm import tqdm
import base64

from google.cloud import bigtable
from google.cloud.bigtable.table import Table


PROJECT_ID = "system-services-dev"
INSTANCE_ID = "bigtable-central-dev"
TABLE_ID = "dev-mpauly-content-manager"


def count_prefix(table) -> dict:
    count = defaultdict(lambda: 0)
    rows = table.read_rows()
    for row in rows:
        key = row.row_key.decode()
        prefix = key.split("#")[0]
        count[prefix] += 1
    return dict(count)


def count_type(table) -> dict:
    count = defaultdict(lambda: 0)
    rows = table.read_rows()
    for row in rows:
        key = row.row_key.decode()
        row_type = key.split("#")[1]
        count[row_type] += 1
    return dict(count)


def count_user(table) -> dict:
    count = defaultdict(lambda: 0)
    rows = table.read_rows()
    for row in rows:
        key = row.row_key.decode()
        row_type = key.split("#")[1]
        if row_type != "user":
            continue

        user64 = key.split("#")[2]
        user = base64.b64decode(user64).decode("utf-8")
        count[user] += 1
    return dict(count)


def insert_random_rows(
    table: Table, prefixes: list[str], count_per=100, content_size=10_000
):
    rows = []
    for p in prefixes:
        for _ in tqdm(range(count_per)):
            r = random.randbytes(20).hex()
            content = random.randbytes(content_size).hex()
            key = f"{p}#fake_data#{r}"
            row = table.direct_row(key)
            row.set_cell("Info", "Value", content)
            rows.append(row)
    table.mutate_rows(rows)


def main():
    client = bigtable.Client(PROJECT_ID, admin=True)
    instance = client.instance(INSTANCE_ID)
    table = instance.table(TABLE_ID)

    # prefixes = [
    #     # "9e5f2a4fb212a9ff5bf8655c05ddb687",  # augment (where the actual data is)
    #     # "ba97dda996119cfe57d4a10048d58735",  # dev-mpauly
    #     "f21cc123e66448c813989c7001950b96",  # other-tenant
    # ]
    # insert_random_rows(table, prefixes, 100000)

    print(count_prefix(table))
    print(count_type(table))
    print(count_user(table))


if __name__ == "__main__":
    main()

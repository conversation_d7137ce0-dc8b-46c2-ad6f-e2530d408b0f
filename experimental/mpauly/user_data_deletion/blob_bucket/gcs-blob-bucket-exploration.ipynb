{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from collections.abc import Iterator\n", "from tqdm import tqdm\n", "from collections import Counter\n", "from datetime import timezone\n", "import pytz\n", "import random\n", "import json\n", "import base64\n", "from dateutil import parser\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from google.cloud import storage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["BUCKET = \"augment-blob-exporter-i0-prod\"\n", "\n", "client = storage.Client()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(518960, 241, datetime.timezone.utc)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["blob_iter = client.list_blobs(BUCKET, prefix=\"blobs/\")\n", "blobs = [b for b in blob_iter]\n", "checkpoints_iter = client.list_blobs(BUCKET, prefix=\"checkpoints/\")\n", "checkpoints = [b for b in checkpoints_iter]\n", "\n", "len(blobs), len(checkpoints), blobs[0].time_created.tzinfo"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkIAAAHOCAYAAAB0PWY+AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAABMOklEQVR4nO3deXwM9/8H8NcmkbOSSEgiRETdN1ERlCIVpDR+jlItdbYkLVJXVOMoTUuddVXr6reoXlTxVYqihBBCxF1BHElcyZJIIsn794fHzjebONu9Yl7Px2MfDzvzmZn3bj5mXzvzmVmNiAiIiIiIVMjK3AUQERERmQuDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQUQkwadIkaDQa3Lhx44ltK1eujHfeeeeZt/Hnn39Co9Hgp59++gcVGseKFSug0Whw4cIFk2/7nXfeQeXKlf/1ei5cuACNRoMvvvji3xf1GM/SR4jofxiEiIiISLUYhIiIiEi1GISIiMjgMjMzzV0C0VNhECIqQW7cuIGePXvC2dkZ7u7uGD58OLKzs5+43Pnz59GjRw+4ubnB0dERzZo1w6ZNmx7aNj8/H+PHj4eXlxecnJzQpUsXJCcnP3EbjxpToxu7UphGo0F4eDhWrVqFGjVqwN7eHv7+/ti9e/cTtwMACxcuRJ06dWBnZwdvb2+EhYUhPT1dr82ePXvQo0cPVKpUCXZ2dvDx8cHIkSNx7969Yutbv3496tatC3t7e9StWxfr1q176Ha///57+Pv7o3Tp0nB2dka9evUwd+7cp6oZAGbPng1fX184ODigdevWOH78uDJv+fLl0Gg0OHLkSLHlPv30U1hbW+PKlStP3EZ6ejreeecduLq6wsXFBf3790dWVpZem7y8PHzyySd48cUXYWdnh8qVK2P8+PHIycnRa6fRaDBp0qRi2yg6Dk03lmvXrl0YNmwYPDw8ULFixSfWSmQJbMxdABE9vZ49e6Jy5cqIjo7G/v37MW/ePNy+fRvffvvtI5dJTU1F8+bNkZWVhQ8++ADu7u5YuXIlunTpgp9++gldu3bVaz9t2jRoNBqMHTsWaWlpmDNnDoKCghAfHw8HBweDvZZdu3Zh7dq1+OCDD2BnZ4eFCxeiQ4cOiI2NRd26dR+53KRJkzB58mQEBQVh6NChOH36NBYtWoSDBw9i7969KFWqFADgxx9/RFZWFoYOHQp3d3fExsbiyy+/xOXLl/Hjjz8q69u6dSu6deuG2rVrIzo6Gjdv3kT//v2LfZBv27YNvXv3Rrt27fD5558DAE6ePIm9e/di+PDhT3y93377Le7cuYOwsDBkZ2dj7ty5aNu2LRISEuDp6Ynu3bsjLCwMq1atQqNGjfSWXbVqFV555RVUqFDhidvp2bMn/Pz8EB0djcOHD+Obb76Bh4eHUjMADBo0CCtXrkT37t3x4Ycf4sCBA4iOjsbJkycfGQKfxrBhw1CuXDlERUXxiBCVHEJEFm/ixIkCQLp06aI3fdiwYQJAjh49qkzz9fWVfv36Kc9HjBghAGTPnj3KtDt37oifn59UrlxZ8vPzRURk586dAkAqVKggWq1WafvDDz8IAJk7d+5ja+zXr5/4+vo+svbCAAgAOXTokDLt4sWLYm9vL127dlWmLV++XABIUlKSiIikpaWJra2ttG/fXqlbRGT+/PkCQJYtW6ZMy8rKKlZLdHS0aDQauXjxojKtYcOGUr58eUlPT1embd26VQDovZ7hw4eLs7Oz5OXlPfZ9KCopKUkAiIODg1y+fFmZfuDAAQEgI0eOVKb17t1bvL299V7b4cOHBYAsX778sdvRvc8DBgzQm961a1dxd3dXnsfHxwsAGTRokF67UaNGCQDZsWOHMg2ATJw4sdi2ivYx3d+pZcuWz/z+EJkbT40RlSBhYWF6z99//30AwObNmx+5zObNm9G0aVO0bNlSmfbCCy9gyJAhuHDhAk6cOKHXvm/fvihdurTyvHv37ihfvvxjt/FPBAYGwt/fX3leqVIlvP766/j999+Rn5//0GX++OMP5ObmYsSIEbCy+t/ua/DgwXB2dtY73Vf46FVmZiZu3LiB5s2bQ0SU00/Xrl1DfHw8+vXrBxcXF6X9q6++itq1a+tt29XVFZmZmdi2bds/er2hoaF6R3SaNm2KgIAAvfe1b9++uHr1Knbu3KlMW7VqFRwcHNCtW7en2s57772n9/zll1/GzZs3odVqAfyvr0REROi1+/DDDwHgkadMn8bgwYNhbW39j5cnMgcGIaISpFq1anrPX3zxRVhZWT32PjsXL15EjRo1ik2vVauWMv9x29BoNKhatarB7+VTdDsAUL16dWRlZeH69esPXUZXa9HXY2triypVqui9lkuXLuGdd96Bm5sbXnjhBZQrVw6tW7cGAGRkZOit72G1FN3GsGHDUL16dXTs2BEVK1bEgAEDsGXLlqd9uY98vYXf11dffRXly5fHqlWrAAAFBQVYs2YNXn/9db1w+jiVKlXSe16mTBkAwO3btwE8eM1WVlaoWrWqXjsvLy+4uroW6w/Pws/P7x8vS2QuDEJEJVjRQcjm9KhaHnV0x5jy8/Px6quvYtOmTRg7dizWr1+Pbdu2YcWKFQAeBIxn5eHhgfj4eGzYsAFdunTBzp070bFjR/Tr189gdVtbW+PNN9/Ezz//jOzsbOzcuRNXr17FW2+99UzreBgR0Xv+b/rOo/6mhhxDRmQqDEJEJcjZs2f1np87dw4FBQWPvQOyr68vTp8+XWz6qVOnlPmP24aI4Ny5c0+8y3KZMmWKXbkFFD/i9KjtAMCZM2fg6OiIcuXKPXQZXa1FX09ubi6SkpKU+QkJCThz5gxmzpyJsWPH4vXXX0dQUBC8vb0fur6H1fKw98zW1hadO3fGwoUL8ffff+Pdd9/Ft99+i3Pnzj203qd5vUXf1759+0Kr1eK3337DqlWrUK5cOQQHBz9x/U/L19cXBQUFxepJTU1Fenq6Xn942N80NzcX165dM1g9RObGIERUgixYsEDv+ZdffgkA6Nix4yOX6dSpE2JjYxETE6NMy8zMxJIlS1C5cuViY2F0Vzfp/PTTT7h27dpjtwE8OE2XkZGBY8eOKdOuXbv2yKuQYmJicPjwYeV5cnIyfv31V7Rv3/6RRzWCgoJga2uLefPm6R3hWLp0KTIyMhASEgLgf0dFCrcRkWKXupcvXx4NGzbEypUrldNlwIMrxIqOnbp586becysrK9SvXx8Ail12/jDr16/Xu/w9NjYWBw4cKPa+1q9fH/Xr18c333yDn3/+Gb169YKNjeEu8O3UqRMAYM6cOXrTZ82aBQDKewg8+JsWvaXBkiVLzHKUj8hYePk8UQmSlJSELl26oEOHDoiJicF3332HN998Ew0aNHjkMuPGjcOaNWvQsWNHfPDBB3Bzc8PKlSuRlJSEn3/+WW/QMQC4ubmhZcuW6N+/P1JTUzFnzhxUrVoVgwcPfmxtvXr1wtixY9G1a1d88MEHyMrKwqJFi1C9enW9wKNTt25dBAcH610+DwCTJ09+5DbKlSuHyMhITJ48GR06dECXLl1w+vRpLFy4EC+99JJyCqlmzZp48cUXMWrUKFy5cgXOzs74+eeflXEyhUVHRyMkJAQtW7bEgAEDcOvWLXz55ZeoU6cO7t69q7QbNGgQbt26hbZt26JixYq4ePEivvzySzRs2FAZb/U4VatWRcuWLTF06FDk5ORgzpw5cHd3x5gxY4q17du3L0aNGgUAz3Ra7Gk0aNAA/fr1w5IlS5Ceno7WrVsjNjYWK1euRGhoKNq0aaO0HTRoEN577z1069YNr776Ko4ePYrff/8dZcuWNWhNRGZlzkvWiOjp6C6NPnHihHTv3l1Kly4tZcqUkfDwcLl3755e26KXNouI/P3339K9e3dxdXUVe3t7adq0qWzcuFGvje7y+TVr1khkZKR4eHiIg4ODhISE6F1u/jhbt26VunXriq2trdSoUUO+++67R14+HxYWJt99951Uq1ZN7OzspFGjRrJz5069dkUvn9eZP3++1KxZU0qVKiWenp4ydOhQuX37tl6bEydOSFBQkLzwwgtStmxZGTx4sBw9evShl6L//PPPUqtWLbGzs5PatWvLL7/8Uux2AD/99JO0b99ePDw8xNbWVipVqiTvvvuuXLt27bHvie7y+RkzZsjMmTPFx8dH7Ozs5OWXX9a77UFh165dE2tra6levfpj112Y7n2+fv263vSHvYf379+XyZMni5+fn5QqVUp8fHwkMjJSsrOz9ZbNz8+XsWPHStmyZcXR0VGCg4Pl3Llzj7x8/uDBg09dL5Gl0IgUGUFHRGRkGo0GYWFhmD9/vrlLsUg3btxA+fLlERUVhY8//tjc5RA91zhGiIjIwqxYsQL5+fl4++23zV0K0XOPY4SIiCzEjh07cOLECUybNg2hoaFPvFKPiP49BiEiIgsxZcoU7Nu3Dy1atFCuCCQi4+IYISIiIlItjhEiIiIi1WIQIiIiItXiGKHHKCgowNWrV1G6dGmL+k0nIiIiejQRwZ07d+Dt7V3sprFFMQg9xtWrV+Hj42PuMoiIiOgfSE5ORsWKFR/bhkHoMUqXLg3gwRvp7Oxs5mqIiIjoaWi1Wvj4+Cif44/DIPQYutNhzs7ODEJEREQlzNMMa+FgaSIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUq1nDkK7d+9G586d4e3tDY1Gg/Xr1+vNFxFERUWhfPnycHBwQFBQEM6ePavX5tatW+jTpw+cnZ3h6uqKgQMH4u7du3ptjh07hpdffhn29vbw8fHB9OnTi9Xy448/ombNmrC3t0e9evWwefPmZ66FiIiI1OuZg1BmZiYaNGiABQsWPHT+9OnTMW/ePCxevBgHDhyAk5MTgoODkZ2drbTp06cPEhMTsW3bNmzcuBG7d+/GkCFDlPlarRbt27eHr68v4uLiMGPGDEyaNAlLlixR2uzbtw+9e/fGwIEDceTIEYSGhiI0NBTHjx9/plqIiIhIxeRfACDr1q1TnhcUFIiXl5fMmDFDmZaeni52dnayZs0aERE5ceKEAJCDBw8qbf773/+KRqORK1euiIjIwoULpUyZMpKTk6O0GTt2rNSoUUN53rNnTwkJCdGrJyAgQN59992nruVJMjIyBIBkZGQ8VXsiIiIyv2f5/DboGKGkpCSkpKQgKChImebi4oKAgADExMQAAGJiYuDq6oomTZoobYKCgmBlZYUDBw4obVq1agVbW1ulTXBwME6fPo3bt28rbQpvR9dGt52nqYWIiIjUzaC/Pp+SkgIA8PT01Jvu6empzEtJSYGHh4d+ETY2cHNz02vj5+dXbB26eWXKlEFKSsoTt/OkWorKyclBTk6O8lyr1T7hFRMREVFJxqvGComOjoaLi4vy8PHxMXdJREREZEQGPSLk5eUFAEhNTUX58uWV6ampqWjYsKHSJi0tTW+5vLw83Lp1S1ney8sLqampem10z5/UpvD8J9VSVGRkJCIiIpTnWq2WYYhKpMrjNv2r5S98FmKgSoiILJtBjwj5+fnBy8sL27dvV6ZptVocOHAAgYGBAIDAwECkp6cjLi5OabNjxw4UFBQgICBAabN7927cv39fabNt2zbUqFEDZcqUUdoU3o6ujW47T1NLUXZ2dnB2dtZ7EBER0fPrmYPQ3bt3ER8fj/j4eAAPBiXHx8fj0qVL0Gg0GDFiBKZOnYoNGzYgISEBffv2hbe3N0JDQwEAtWrVQocOHTB48GDExsZi7969CA8PR69eveDt7Q0AePPNN2Fra4uBAwciMTERa9euxdy5c/WO1gwfPhxbtmzBzJkzcerUKUyaNAmHDh1CeHg4ADxVLURERKRuz3xq7NChQ2jTpo3yXBdO+vXrhxUrVmDMmDHIzMzEkCFDkJ6ejpYtW2LLli2wt7dXllm1ahXCw8PRrl07WFlZoVu3bpg3b54y38XFBVu3bkVYWBj8/f1RtmxZREVF6d1rqHnz5li9ejUmTJiA8ePHo1q1ali/fj3q1q2rtHmaWoiIiEi9NCIi5i7CUmm1Wri4uCAjI4OnyahE4RghIlKzZ/n85lVjREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgYPQvn5+fj444/h5+cHBwcHvPjii/jkk08gIkobEUFUVBTKly8PBwcHBAUF4ezZs3rruXXrFvr06QNnZ2e4urpi4MCBuHv3rl6bY8eO4eWXX4a9vT18fHwwffr0YvX8+OOPqFmzJuzt7VGvXj1s3rzZ0C+ZiIiISiiDB6HPP/8cixYtwvz583Hy5El8/vnnmD59Or788kulzfTp0zFv3jwsXrwYBw4cgJOTE4KDg5Gdna206dOnDxITE7Ft2zZs3LgRu3fvxpAhQ5T5Wq0W7du3h6+vL+Li4jBjxgxMmjQJS5YsUdrs27cPvXv3xsCBA3HkyBGEhoYiNDQUx48fN/TLJiIiohJII4UP1RjAa6+9Bk9PTyxdulSZ1q1bNzg4OOC7776DiMDb2xsffvghRo0aBQDIyMiAp6cnVqxYgV69euHkyZOoXbs2Dh48iCZNmgAAtmzZgk6dOuHy5cvw9vbGokWL8NFHHyElJQW2trYAgHHjxmH9+vU4deoUAOCNN95AZmYmNm7cqNTSrFkzNGzYEIsXL37ia9FqtXBxcUFGRgacnZ0N9h4RGVvlcZv+1fIXPgsxUCVERKb3LJ/fBj8i1Lx5c2zfvh1nzpwBABw9ehR//fUXOnbsCABISkpCSkoKgoKClGVcXFwQEBCAmJgYAEBMTAxcXV2VEAQAQUFBsLKywoEDB5Q2rVq1UkIQAAQHB+P06dO4ffu20qbwdnRtdNspKicnB1qtVu9BREREzy8bQ69w3Lhx0Gq1qFmzJqytrZGfn49p06ahT58+AICUlBQAgKenp95ynp6eyryUlBR4eHjoF2pjAzc3N702fn5+xdahm1emTBmkpKQ8djtFRUdHY/Lkyf/kZRMREVEJZPAjQj/88ANWrVqF1atX4/Dhw1i5ciW++OILrFy50tCbMrjIyEhkZGQoj+TkZHOXREREREZk8CNCo0ePxrhx49CrVy8AQL169XDx4kVER0ejX79+8PLyAgCkpqaifPnyynKpqalo2LAhAMDLywtpaWl6683Ly8OtW7eU5b28vJCamqrXRvf8SW1084uys7ODnZ3dP3nZREREVAIZ/IhQVlYWrKz0V2ttbY2CggIAgJ+fH7y8vLB9+3ZlvlarxYEDBxAYGAgACAwMRHp6OuLi4pQ2O3bsQEFBAQICApQ2u3fvxv3795U227ZtQ40aNVCmTBmlTeHt6NrotkNERETqZvAg1LlzZ0ybNg2bNm3ChQsXsG7dOsyaNQtdu3YFAGg0GowYMQJTp07Fhg0bkJCQgL59+8Lb2xuhoaEAgFq1aqFDhw4YPHgwYmNjsXfvXoSHh6NXr17w9vYGALz55puwtbXFwIEDkZiYiLVr12Lu3LmIiIhQahk+fDi2bNmCmTNn4tSpU5g0aRIOHTqE8PBwQ79sIiIiKoEMfmrsyy+/xMcff4xhw4YhLS0N3t7eePfddxEVFaW0GTNmDDIzMzFkyBCkp6ejZcuW2LJlC+zt7ZU2q1atQnh4ONq1awcrKyt069YN8+bNU+a7uLhg69atCAsLg7+/P8qWLYuoqCi9ew01b94cq1evxoQJEzB+/HhUq1YN69evR926dQ39somIiKgEMvh9hJ4nvI8QlVS8jxARqZlZ7yNEREREVFIwCBEREZFqGXyMEBGRmv2b05I8JUlkejwiRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqsUgRERERKrFIERERESqxSBEREREqmWUIHTlyhW89dZbcHd3h4ODA+rVq4dDhw4p80UEUVFRKF++PBwcHBAUFISzZ8/qrePWrVvo06cPnJ2d4erqioEDB+Lu3bt6bY4dO4aXX34Z9vb28PHxwfTp04vV8uOPP6JmzZqwt7dHvXr1sHnzZmO8ZCIiIiqBDB6Ebt++jRYtWqBUqVL473//ixMnTmDmzJkoU6aM0mb69OmYN28eFi9ejAMHDsDJyQnBwcHIzs5W2vTp0weJiYnYtm0bNm7ciN27d2PIkCHKfK1Wi/bt28PX1xdxcXGYMWMGJk2ahCVLliht9u3bh969e2PgwIE4cuQIQkNDERoaiuPHjxv6ZRMREVEJpBERMeQKx40bh71792LPnj0PnS8i8Pb2xocffohRo0YBADIyMuDp6YkVK1agV69eOHnyJGrXro2DBw+iSZMmAIAtW7agU6dOuHz5Mry9vbFo0SJ89NFHSElJga2trbLt9evX49SpUwCAN954A5mZmdi4caOy/WbNmqFhw4ZYvHjxE1+LVquFi4sLMjIy4Ozs/K/eFyJTqjxu079a/sJnIQaqRH3+zXvP953IMJ7l89vgR4Q2bNiAJk2aoEePHvDw8ECjRo3w9ddfK/OTkpKQkpKCoKAgZZqLiwsCAgIQExMDAIiJiYGrq6sSggAgKCgIVlZWOHDggNKmVatWSggCgODgYJw+fRq3b99W2hTejq6NbjtF5eTkQKvV6j2IiIjo+WXwIHT+/HksWrQI1apVw++//46hQ4figw8+wMqVKwEAKSkpAABPT0+95Tw9PZV5KSkp8PDw0JtvY2MDNzc3vTYPW0fhbTyqjW5+UdHR0XBxcVEePj4+z/z6iYiIqOQweBAqKChA48aN8emnn6JRo0YYMmQIBg8e/FSnoswtMjISGRkZyiM5OdncJREREZERGTwIlS9fHrVr19abVqtWLVy6dAkA4OXlBQBITU3Va5OamqrM8/LyQlpamt78vLw83Lp1S6/Nw9ZReBuPaqObX5SdnR2cnZ31HkRERPT8MngQatGiBU6fPq037cyZM/D19QUA+Pn5wcvLC9u3b1fma7VaHDhwAIGBgQCAwMBApKenIy4uTmmzY8cOFBQUICAgQGmze/du3L9/X2mzbds21KhRQ7lCLTAwUG87uja67RAREZG6GTwIjRw5Evv378enn36Kc+fOYfXq1ViyZAnCwsIAABqNBiNGjMDUqVOxYcMGJCQkoG/fvvD29kZoaCiAB0eQOnTogMGDByM2NhZ79+5FeHg4evXqBW9vbwDAm2++CVtbWwwcOBCJiYlYu3Yt5s6di4iICKWW4cOHY8uWLZg5cyZOnTqFSZMm4dChQwgPDzf0yyYiIqISyMbQK3zppZewbt06REZGYsqUKfDz88OcOXPQp08fpc2YMWOQmZmJIUOGID09HS1btsSWLVtgb2+vtFm1ahXCw8PRrl07WFlZoVu3bpg3b54y38XFBVu3bkVYWBj8/f1RtmxZREVF6d1rqHnz5li9ejUmTJiA8ePHo1q1ali/fj3q1q1r6JdNREREJZDB7yP0POF9hKik4n2EzIf3ESIyP7PeR4iIiIiopGAQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1bIxdwFERKRelcdt+sfLXvgsxICVkFrxiBARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGpFoMQERERqRaDEBEREakWgxARERGplo25CyAi+icqj9v0j5e98FmIASshopKMR4SIiIhItRiEiIiISLUYhIiIiEi1GISIiIhItYwehD777DNoNBqMGDFCmZadnY2wsDC4u7vjhRdeQLdu3ZCamqq33KVLlxASEgJHR0d4eHhg9OjRyMvL02vz559/onHjxrCzs0PVqlWxYsWKYttfsGABKleuDHt7ewQEBCA2NtYYL5OIiIhKIKMGoYMHD+Krr75C/fr19aaPHDkSv/32G3788Ufs2rULV69exf/93/8p8/Pz8xESEoLc3Fzs27cPK1euxIoVKxAVFaW0SUpKQkhICNq0aYP4+HiMGDECgwYNwu+//660Wbt2LSIiIjBx4kQcPnwYDRo0QHBwMNLS0oz5somIiKiEMNrl83fv3kWfPn3w9ddfY+rUqcr0jIwMLF26FKtXr0bbtm0BAMuXL0etWrWwf/9+NGvWDFu3bsWJEyfwxx9/wNPTEw0bNsQnn3yCsWPHYtKkSbC1tcXixYvh5+eHmTNnAgBq1aqFv/76C7Nnz0ZwcDAAYNasWRg8eDD69+8PAFi8eDE2bdqEZcuWYdy4ccZ66UREBsFbBBAZn9GOCIWFhSEkJARBQUF60+Pi4nD//n296TVr1kSlSpUQExMDAIiJiUG9evXg6emptAkODoZWq0ViYqLSpui6g4ODlXXk5uYiLi5Or42VlRWCgoKUNkXl5ORAq9XqPYiIiOj5ZZQjQt9//z0OHz6MgwcPFpuXkpICW1tbuLq66k339PRESkqK0qZwCNLN1817XButVot79+7h9u3byM/Pf2ibU6dOPbTu6OhoTJ48+elfKBERWQweQaN/wuBHhJKTkzF8+HCsWrUK9vb2hl69UUVGRiIjI0N5JCcnm7skIiIiMiKDB6G4uDikpaWhcePGsLGxgY2NDXbt2oV58+bBxsYGnp6eyM3NRXp6ut5yqamp8PLyAgB4eXkVu4pM9/xJbZydneHg4ICyZcvC2tr6oW106yjKzs4Ozs7Oeg8iIiJ6fhk8CLVr1w4JCQmIj49XHk2aNEGfPn2Uf5cqVQrbt29Xljl9+jQuXbqEwMBAAEBgYCASEhL0ru7atm0bnJ2dUbt2baVN4XXo2ujWYWtrC39/f702BQUF2L59u9KGiIiI1M3gY4RKly6NunXr6k1zcnKCu7u7Mn3gwIGIiIiAm5sbnJ2d8f777yMwMBDNmjUDALRv3x61a9fG22+/jenTpyMlJQUTJkxAWFgY7OzsAADvvfce5s+fjzFjxmDAgAHYsWMHfvjhB2za9L9zxBEREejXrx+aNGmCpk2bYs6cOcjMzFSuIiMiIiJ1M8uvz8+ePRtWVlbo1q0bcnJyEBwcjIULFyrzra2tsXHjRgwdOhSBgYFwcnJCv379MGXKFKWNn58fNm3ahJEjR2Lu3LmoWLEivvnmG+XSeQB44403cP36dURFRSElJQUNGzbEli1big2gJiIiInUySRD6888/9Z7b29tjwYIFWLBgwSOX8fX1xebNmx+73ldeeQVHjhx5bJvw8HCEh4c/da1ERESkHvytMSIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUi2z/MQGEZElqTxu05MbPcKFz0IMWAkRmRqPCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWrZmLsAIiIyvsrjNv3jZS98FmLASogsC48IERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoxCBEREZFqMQgRERGRajEIERERkWoZPAhFR0fjpZdeQunSpeHh4YHQ0FCcPn1ar012djbCwsLg7u6OF154Ad26dUNqaqpem0uXLiEkJASOjo7w8PDA6NGjkZeXp9fmzz//ROPGjWFnZ4eqVatixYoVxepZsGABKleuDHt7ewQEBCA2NtbQL5mIiIhKKIMHoV27diEsLAz79+/Htm3bcP/+fbRv3x6ZmZlKm5EjR+K3337Djz/+iF27duHq1av4v//7P2V+fn4+QkJCkJubi3379mHlypVYsWIFoqKilDZJSUkICQlBmzZtEB8fjxEjRmDQoEH4/ffflTZr165FREQEJk6ciMOHD6NBgwYIDg5GWlqaoV82ERERlUA2hl7hli1b9J6vWLECHh4eiIuLQ6tWrZCRkYGlS5di9erVaNu2LQBg+fLlqFWrFvbv349mzZph69atOHHiBP744w94enqiYcOG+OSTTzB27FhMmjQJtra2WLx4Mfz8/DBz5kwAQK1atfDXX39h9uzZCA4OBgDMmjULgwcPRv/+/QEAixcvxqZNm7Bs2TKMGzfO0C+diIiIShijjxHKyMgAALi5uQEA4uLicP/+fQQFBSltatasiUqVKiEmJgYAEBMTg3r16sHT01NpExwcDK1Wi8TERKVN4XXo2ujWkZubi7i4OL02VlZWCAoKUtoUlZOTA61Wq/cgIiKi55dRg1BBQQFGjBiBFi1aoG7dugCAlJQU2NrawtXVVa+tp6cnUlJSlDaFQ5Buvm7e49potVrcu3cPN27cQH5+/kPb6NZRVHR0NFxcXJSHj4/PP3vhREREVCIY/NRYYWFhYTh+/Dj++usvY27GYCIjIxEREaE812q1DENkUpXHbfrHy174LMSAlRARqYPRglB4eDg2btyI3bt3o2LFisp0Ly8v5ObmIj09Xe+oUGpqKry8vJQ2Ra/u0l1VVrhN0SvNUlNT4ezsDAcHB1hbW8Pa2vqhbXTrKMrOzg52dnb/7AUTERFRiWPwU2MigvDwcKxbtw47duyAn5+f3nx/f3+UKlUK27dvV6adPn0aly5dQmBgIAAgMDAQCQkJeld3bdu2Dc7Ozqhdu7bSpvA6dG1067C1tYW/v79em4KCAmzfvl1pQ0REROpm8CNCYWFhWL16NX799VeULl1aGY/j4uICBwcHuLi4YODAgYiIiICbmxucnZ3x/vvvIzAwEM2aNQMAtG/fHrVr18bbb7+N6dOnIyUlBRMmTEBYWJhyxOa9997D/PnzMWbMGAwYMAA7duzADz/8gE2b/ndqISIiAv369UOTJk3QtGlTzJkzB5mZmcpVZERERKRuBg9CixYtAgC88soretOXL1+Od955BwAwe/ZsWFlZoVu3bsjJyUFwcDAWLlyotLW2tsbGjRsxdOhQBAYGwsnJCf369cOUKVOUNn5+fti0aRNGjhyJuXPnomLFivjmm2+US+cB4I033sD169cRFRWFlJQUNGzYEFu2bCk2gJqIiIjUyeBBSESe2Mbe3h4LFizAggULHtnG19cXmzdvfux6XnnlFRw5cuSxbcLDwxEeHv7EmoiIiEh9+FtjREREpFpGvXyeiKiwf3N7AIC3CCAiw+MRISIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLV41RkSPxSu9iOh5xiNCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWgxCREREpFoMQkRERKRaDEJERESkWjbmLoDoaVUet+kfL3vhsxADVkJERM8LHhEiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1WIQIiIiItViECIiIiLVYhAiIiIi1eJ9hIiI6Jnwnl70POERISIiIlItHhEiIiIqIXg0zvB4RIiIiIhUi0GIiIiIVItBiIiIiFSLQYiIiIhUi0GIiIiIVItBiIiIiFSLQYiIiIhUi0GIiIiIVItBiIiIiFSLQYiIiIhUi0GIiIiIVIu/NUb0L/G3f4iISi4eESIiIiLVYhAiIiIi1WIQIiIiItXiGCEiIiIV4vjGB3hEiIiIiFSLQYiIiIhUi0GIiIiIVItjhKgYNZw3VsNrJCKiJ+MRISIiIlItBiEiIiJSLZ4aIyIiKoKnz9WDQYiIiMiIGKosG4MQERER/SslOeypYozQggULULlyZdjb2yMgIACxsbHmLomIiIgswHMfhNauXYuIiAhMnDgRhw8fRoMGDRAcHIy0tDRzl0ZERERm9twHoVmzZmHw4MHo378/ateujcWLF8PR0RHLli0zd2lERERkZs/1GKHc3FzExcUhMjJSmWZlZYWgoCDExMSYsbIHSvI51aelhtdIREQl13MdhG7cuIH8/Hx4enrqTff09MSpU6eKtc/JyUFOTo7yPCMjAwCg1WqNUl9BTtY/XrZoTXUn/v6P13V8crDec0PWxXWZZ13/Zj1qWFdJ+BtyXVyXWtdlCLp1isiTG8tz7MqVKwJA9u3bpzd99OjR0rRp02LtJ06cKAD44IMPPvjgg4/n4JGcnPzErPBcHxEqW7YsrK2tkZqaqjc9NTUVXl5exdpHRkYiIiJCeV5QUIBbt27B3d0dGo3G6PXqaLVa+Pj4IDk5Gc7Ozibb7pOwrmfDup4N63o2rOvpWWJNAOsyJhHBnTt34O3t/cS2z3UQsrW1hb+/P7Zv347Q0FAAD8LN9u3bER4eXqy9nZ0d7Ozs9Ka5urqaoNKHc3Z2tshOyLqeDet6Nqzr2bCup2eJNQGsy1hcXFyeqt1zHYQAICIiAv369UOTJk3QtGlTzJkzB5mZmejfv7+5SyMiIiIze+6D0BtvvIHr168jKioKKSkpaNiwIbZs2VJsADURERGpz3MfhAAgPDz8oafCLJWdnR0mTpxY7DSdubGuZ8O6ng3rejas6+lZYk0A67IUGpGnubaMiIiI6Pnz3N9ZmoiIiOhRGISIiIhItRiEiIiISLUYhIiIDIjDLkltSnqfZxAqwS5evIjTp0+buwwAQHp6Ou7cuWPuMoqx1LquX7+OCxcumLuMYiy1Lh1L6vOFJSYmYv78+QAAjUZj1g+Gy5cvIyUlxWzbfxRLrOvSpUs4ePAg8vPzzV2KHkutqzBL6vP/2r//RS8yh6SkJNFoNOLt7S3Hjx83ay2JiYni5eUl0dHRotVqzVpLYZZa1+nTp8XT01OGDRsmZ8+eNXc5CkutS8eS+nxht27dEltbW9FoNPLJJ58o0wsKCkxey7lz50Sj0UiTJk3k6tWrJt/+o1hiXbr+VKNGDYmJiTHL3+thLLWuwiypzxsCjwiVUAUFBfD09ERGRgY6duyIY8eOma2W7777Dqmpqfjkk0+wYMECZGZmmq2Wwiy1ro0bNyItLQ0///wzFi5ciPPnz5u7JACWW5eOJfX5wmxtbfHaa68hICAAU6dOxahRowCY51vyrVu3UKpUKRw5cgSvvfYarl27ZtLtP4ol1pWXlwdnZ2ecO3cO77zzDvbv32/ukgBYbl2FWVKfNwRV3FDxeSMiKFeuHF599VVUqFABycnJaNeuHbZv34769eubvJ6BAwfi/PnzsLKywvjx45GdnY0xY8bA0dHR5LWUhLoGDx6MgwcPIjs7G8uWLcO9e/cwZswY+Pn5sa5HsLQ+X5iTkxNq1aqFpKQkLFmyBGFhYbCyssL06dOVDwZT/GiziKBKlSoIDQ1FrVq1sGvXLrRt2xY7duxA+fLljb79klRXQUEB/Pz88O6778LJyQkHDhxAt27d8PPPPyMwMNAsNVlyXUVZSp83FB4RKmF0Hax06dIIDg7GsmXLEBERgbZt26J9+/ZISEgweU1ly5ZFVlYWGjdujA0bNmDKlCn44osvkJWVZfJaLL2uvLw8lCpVCuXKlUOTJk2wbt06rF69GtOnT0dSUhLreghL7PNFTZw4ETY2Nrh+/Trmzp2L2bNnY+zYsQBM8y1Z9x65u7vD398fq1evxvz58+Hu7o727dub7QiMpdZlZWUFa2trVKtWDd9++y2WL1+OZs2aoUePHoiJiTFLTZZc18OYu88blDnOx9GzO3/+vBw/flxu376tTCsoKJDevXvL3Llz5ebNm9KhQwfx8vKSY8eOGbWW9PR0ycrK0pu2c+dOKV++vJw9e1b+85//iEajkSlTpkhmZqZRaykJdd24cUMuXLigNy0+Pl5cXFwkISFBtm/fLs7OzjJ06FA5f/686uvSsaQ+X1hiYqJER0dLcnKy0t9ycnJkwoQJMnz4cBERWbx4sdjY2MiYMWP0aje0S5cuydWrV/X6c1ZWloSEhMjKlSslKSlJGjRoIPXq1TPp2BxLrOvChQty8OBBSU1N1Zv+2muvyaeffip5eXnSrl078fHxkX379pmkJkuuqzBL6vPGwCBUAly8eFE0Go24uLhI79695auvvpL79++LiMjMmTMlICBARETu3r0rHTt2FB8fHzl8+LBRaklISBBnZ2fp06ePfPbZZyIikpubKyIivXr1kgULFoiIyKJFi0Sj0cjUqVPl7t27RqmlJNR18uRJcXV1lRYtWkhERITcunVLMjIyRERk+PDhyk7j559/FhcXFwkPDzfJQGVLrUvHkvp8YTdv3pQyZcqIRqORN998U7p37y7x8fEi8uA9dXJykh07doiIyFdffSWOjo4SFhZmlFr+/vtv0Wg0UqVKFenfv79s2rRJmTd27Fjp3LmziIgkJydLw4YNpVGjRnL58mWj1GLpdZ0/f140Go14eXlJmzZtZNWqVUrwWLJkibRv315p27ZtW6lSpYrs3r3bqDVZcl2FWVKfNxYGoRLg/PnzUrVqVbGzs5Px48eLt7e3dOvWTaZNmyZZWVlSv359+eqrr0TkwWj+li1bSo0aNSQnJ8fgtUyePFk0Go20bNlSqlevLq1atZKpU6fKlStXZNmyZVKtWjUlYCxdulQ0Go1Mnz7d4HWUlLrmz58vGo1GWrVqJb6+vtKhQwfp27evJCYmyqpVq6R69erKju/XX38VjUYjH374ofKhr7a6dCypzxd248YNGTt2rNjY2MjAgQOV2vr16yfr1q2TyMhIGT58uBQUFEhmZqbMmzdPypUrJ6mpqQb/dhwXFydOTk7i6uoqI0aMEFdXVxk4cKB88803otVqpXLlyvLLL7+IyIOjDn5+ftKiRQvJy8szaB0loa7U1FRxdXUVNzc3CQsLk0qVKknPnj3l448/lhs3boi3t7csXLhQaf/SSy9J3bp15d69e0aryZLrKsyS+ryxMAiVEOfOnZNq1apJ165dJT4+XhYuXCitW7eWGjVqiIeHh/To0UOys7NF5MEHw6VLl4xWy4gRI6R06dKyZs0amTJligwcOFDKlCkjkydPllKlSsnixYslPz9fRES+/fZbOXHihNFqKQl1TZ06VWrUqCGzZs2S+fPny+jRo6VcuXIyduxY0Wg0Mn78eOVDYPPmzXLq1ClV16VjSX1e129ERLKzs2XUqFFiY2Mjf/31lxw5ckSio6OlQoUKUqpUKalYsaJyOi8rK0vv1J6h7d27V8qUKSPh4eGyd+9eiY6Oltq1a0uzZs3Ew8NDhg0bpoTXS5cumewUpyXWlZycLB4eHtK7d2/ZtGmTrFmzRho3bixt2rQRd3d3CQkJUY6Kijw4KmkKllqXpfZ5Y2AQsmC6jqhL1WfOnJEKFSpISEiIpKSkiIjIypUr5e2335b//Oc/Rq2loKBA72hAv379xMPDQ9auXSsiD06hDBo0SNzc3OS3334zai2WXJfub1W0rrFjx0rVqlVl+vTpUlBQIH/99ZdER0dLxYoV5ccff9RbVk11FWVJfV7n3LlzMn78eBkwYIDMnDlTGfcybNgwsbGxUU79XLt2Tb744gvlaIcxXLlyRX766SdZtWqVJCYmisiDcXAODg7y7rvvilarlfv378uMGTOkQ4cOJnuPLLGuS5cuyTfffCNffPGF/PXXXyLy4B49bm5u0qlTJ0lKShIRkbVr18rAgQPl22+/FRH9AKCmugqzpD5vCgxCFigtLa3YwDndN/MzZ85IpUqVpFWrVnLr1i0REaOeDih6CLbwtvr37y+Ojo6yZs0aERG5f/9+sbrVVlfRcUeF6xo/frxUrFhRPvvsM+Ub3p07d1Rdl44l9fnC4uPjpVy5ctKhQwepUqWKlC9fXrp27Sr37t2TnJwcGT58uFhbW8sPP/wgImLUU4dHjx6VatWqSfXq1cXBwUG8vb2VsRl//vmnlC5dWnr16qV8YJrqggBLrOvo0aPi5+cndevWFRcXF7GxsVH2B0lJSVKuXDlp3bq1EjpMxVLrKsyS+rypMAhZmKSkJHnhhRfEx8dHVq1aJQcOHCjW5syZM+Lr6yutW7c26gf88ePHpU6dOjJnzhy9q3IKd/wBAwaIo6OjfP/998ppCmOz1LpOnDghZcuWlWnTpsn69esf2mb8+PHi4+Mjn3/+ucnCmaXWpWNJfb6whIQEcXBwkE8++UTu378v2dnZMmXKFHFzc1OOlt2+fVtGjhwp1tbWyntrjCNo8fHx4ujoKKNHj5bk5GT59ddfpW3btlKtWjXlyr89e/aIi4uL9OrVyyQXAlhqXbqaxo4dKzdv3pSDBw/Km2++KU5OTpKQkCAiD05HeXp6yiuvvGKyU76WWldhltTnTYlByMKcOHFCgoODZfjw4dK9e3fx9/eXIUOGyNGjR/WOgpw5c0aqVKkijRo1krS0NKPUMm3aNHF0dJSAgADp3r27dOrUSc6cOSM3btzQa9e/f39xdXWVFStWmOSbuqXWtWTJEtFoNNKrVy/p2rWrvPTSS7Jz585il6hHRkZKlSpVZOLEiXL9+nXV1qVjSX1e58aNG1KvXj2pU6eO3vTU1FTx9PSUJUuWKNMyMjJk1KhRotFoZOPGjQav5fLly+Li4iJ9+/bVm75gwQJxdXWV06dPK9P27NkjZcuWlc6dOxs9dFhiXSkpKUroKmz9+vXi7OwsMTExyrTk5GSpWLGi+Pv7y5kzZ4xWkyXXVZgl9XlTYxCyMDdu3JCgoCCl0+3Zs0deeeUV6dKli4SEhEh8fLxcu3ZNRB5culi/fv1iH2iGEhcXJ0OHDpW//vpLEhMTpUOHDvLyyy9L586d5b///a/cvHlTaduzZ08pX768SX7Ty1LrSklJkSFDhsgPP/wgGRkZ8tZbb8nrr78u9erVk2+//VYZOyEi8uGHH0rt2rWLhTc11aVjSX1e5+7duzJ8+HBp2bKlREZGKvdOOXbsmDg4OBQbb5aeni7jx483ygD8HTt2SPPmzaVVq1Z6v7G2bds2KVu2rHI0QWfnzp3i6+srV65cMXgtll7X8ePHpUuXLlKxYkWJjY1Vpu/bt0+cnZ2VabqjxxcvXpTq1asbvT9Zal2FWVKfNzUGIQv022+/ia+vrxw8eFCZ1rhxY7GyspKqVavKa6+9JjNmzBCR/90rxxjy8vKkdevW8t577ynTDh8+LB4eHmJvby89e/aUqVOnKqeedB9WxmapdeXn50vfvn2le/fuyrS0tDSpVKmSlClTRtq2bSthYWHK+X9ThQ1LraswS+nzIv87zK/VaiUyMlKaNm0q06ZNk3PnzomPj4+Eh4cXa1v034b2+++/S8eOHaVp06aSmpoqWVlZUq5cORk3btxD2xe9saia6jpx4oT06NFDPDw8JCkpSXJycsTDw0NGjx6t104XOox9OwFLr0vEMvu8KTEIWZiCggK5deuWdO/eXbla4J133pEKFSrIhQsX5LfffpMPPvhA3N3djXpHVt3AxtjYWGnQoIHyjaVfv35SrVo1+e677yQyMlJKly4tTZo0MdnATEutS7dDuHTpklSuXFk2bNig1FWlShVZv369LFmyRDw8PKRevXomu7zUUusqWqMl9PnCdP1Mq9XKuHHjxN/fX+zt7WXIkCFKzaa4iqfwB82WLVukY8eO0rBhQ3Fzc5OIiIhi9ZqKJdZVuKbjx49L9+7dxd3dXVxcXGTUqFGPrMnYH+aWWldRltLnzYFByEJNmTJFatWqJW+88YZ4e3vrHU4VEZOc6hF5cFqlY8eOsnz5chkwYIB4eXnJoUOHROTBf4wbN26Y5ecXLLGugoICyc7OlsGDB8vUqVOld+/eenWJPPiWZ8z73ZSkuoqylD6vU/iDYcKECVKjRg0ZO3asMt1U39gfFjo8PDyUCwVMeeSgJNSlc/z4cenfv7/Y2dnJ/v37LaImEcutS8Ry+rypMQhZGN3O5f79+9KoUSPx9vaWuLg4s9a0bNky5Tbwhc8Hm/uwqKXWtWHDBtFoNOLp6ak32NHcOxFLrctS+vzD+o3uA+DOnTsSGRkpAQEB8tFHHxn99NzjatuyZYt06tRJAgIClPFdpvqmXvQ9MmddT7PuhIQE6dmzp3h4eCinXc11lMqS6tKx5D5vSgxCZvS4D+y8vDwZPXq0tGzZUu9meOaQnp4ur732mnLe35R1PG5b5qjraXdWgwYNkrfeestkl+4X9rj3wZx1iVhen797965kZWUp9yd6mKLfkqtXry6TJ082al0PUzR0dO7cWWrUqCEnT5406navXr2qN6DeEuo6deqUzJ49+6lOlSYmJkrv3r3F2tra6L9Hd/v2bUlKSpLk5GRl2qP6sCnrKiw9PV1u3ryp3KBUp3CdltLnTYVByMSeZserc+XKFXF2dpaZM2eaoLLHGz16tNSqVcskl6E/acdbmCnrepad77x586R69ep6O0RjeZadrynr0nmaHa+OKft8YmKidOrUSRo3biw+Pj7K3cgfFnZ10zIyMmTKlCkGv+Hd2bNnZdSoUdK9e3eJiop65O0BCr9nGzZskB49ehj15nuXL18Wd3d36dq1q95AdnPWdezYMXFzc5PBgwcX+7mJR/X7o0ePSv/+/fUu6Te0hIQEad68uVSpUkUaN278VL9laIq6Cjt27Jg0aNBA6tatK05OTjJq1CjZu3evMv9hYchYfd6SMAiZ0LPseHWnK/r27SsdOnQw+KDfZ93xZmVliaurq4wfP96gdRT1rDteU9X1tDvfwv/28vKS/v37G7Wup935mrounWfZ8Rq7zxeWmJgobm5uMnLkSFmyZImMHj1aNBpNsXFJhRX9+Q9DSUhIEC8vL+natau89dZbUrp0aXnzzTcf2b7w9o19r6CdO3eKjY2NtG3bVvr27at3yjI/P19v32WKuq5duyY1a9bUG2ScnZ2td7+pR53qNeaXpVOnTombm5uMHj1atm7dKu+++668/PLLendof9TRZFPdJf3SpUvi6ekpI0eOlK1bt8rXX38tderUkfbt2yufRSIPD0PmHm5gbAxCJvJPdrwiItu3b5e///7boLX8kx3vvXv3ZMKECXL27FmD1lLUs+54TVHXs+58df/++uuvjfpN71l3vqaqS+ef7HhFjNPnC7tx44a0bdtWRo4cqTe9TZs28tFHHz20JmO5fPmy1KtXTz788ENl2tGjR8XJyUn+/PPPRy5nqvpu3rwpXbp0ka+++koaN24sffr0Ue4Z9LAPdmPXFRcXJ61atZLMzEzJycmRoUOHSuvWreWVV17RuwzdlOPecnNzZciQITJ48GBl2okTJ+TVV1+VxMREvX2TOcfjrV27Vho1aqR3Wnzfvn0SGhoqrVq1kl9//dVstZkbg5AJPA87XhHT/Cd+1h2vKer6pztfYw5+/Dc7X1MNyrTUHW9iYqI0btxY+T0snb59+ypfCEz1/3HFihXSrl07uXz5sog8GDCenp4utWrVki1btpikhkfJy8uTtLQ0qV69uly+fFl++eUXeemll2Tw4MHSvHlz6datm4iY9mjBt99+K35+fiIi0qVLF3n11Vdl3rx5Mnz4cKlfv76EhISYrJbCQkNDpXfv3srzqKgocXFxER8fH6lfv7506tTJLHUVtn79eqlQoYKyb9D93Q4dOiQdO3aUHj16GP0mnJbKCmR0qampSE9PR+fOnfWm+/j4ICkpyaS1/PHHH/Dw8MDIkSMBAHl5efD19UWlSpWQnZ392GWtra2NWlt+fj7y8/Nx6tQphISEYMKECThz5gzmzp2LFi1aoGfPngAAETFpXYmJiUhOToajoyN69OiBc+fOoVu3bmjQoAF+//13vPbaaw+tw8rKeP+9SpUqhbS0NNy9e1eZ9v333yM2NhYdOnRAt27dEBISYvK6CrOzs0NaWhqSk5MBPPi7BQYGYsKECXBycsJ3332Hq1evmqSWwmrXro0ZM2agTZs2AID79+8DAMqWLau8VxqNBgCQm5tr1FpatWqF5s2bo0KFCgAe/K1cXFzg6OiI1NRUo277SaysrFCuXDm89NJLOH78OLp27YpJkyZh3bp1SEhIUPq97r0yhRYtWsDNzQ0zZsxAVlYWFi5ciPfffx+zZs3CpEmTcOXKFfzyyy8mq0dEkJeXh2rVqiE5ORnvvvsuRowYgc8//xzffPMNNm3ahE8++QTJycmIjo42WV0PU6FCBWRnZ2PXrl1K7QDg7++P0aNHY8OGDYiNjTVniWbDIGQC3PE+HUvc8QLc+f5TlrjjLSgoAAC0bdtWeV6qVCkAgK2tLbRardJ2ypQpWLZsmbKMMfj5+WHKlCkAHrw/hft2Zmam8u+1a9fi4MGDRqvjYXS1WFtb488//wQA/PLLL8jPz4ePjw/27Nlj8r+fi4sLXF1d8dVXXyE1NRUVK1YE8GDf8eqrryIrKwunTp0yWT0ajQY2NjYIDw9HixYtYGtri2PHjmHatGno3r076tWrh+DgYJQpUwaXLl0yWV2F6f7fNWnSBGFhYQgPD8eOHTtgZWWF/Px8AECbNm0QEBCg/J3VhkHIyLjjfXqWuOMFuPN9Vpa2471y5Qri4uJQUFBQ7GhY0ed5eXkAgI8//hiTJk1CQECAyY6gaTQaZfsODg5wcXEBAEyYMAG9e/eGu7u7SerQ0f0d27ZtCzs7OwwbNgybN29GXFwcpk6dil27dmH58uVPPJJsSO7u7pg3bx7u3r2L48ePY82aNcq8F154AXXq1EHZsmVNVo9OpUqVMG3aNHz55ZdwcnLSe0/s7Ozg4eEBd3d3yIPhKEavp3Cf12g0yv+78ePHo1evXujcuTM2btyo17etra3h5eVl9NoskY25C3geXblyBSkpKWjUqNEz7XinTZuGuLg4k+94bWxsiu14P/30U5w7d84kdejoglnbtm2RlJSkt+ONj4/H6NGjYWtri/r168Pe3t5kdel2vm3btsX58+exZs0a9O/fH4Bl7Hytra3RuXPnx+58AeMeSSva5/Pz82FtbY3x48fj0qVL6Ny5M9auXaucrgNMs+M9deoUGjVqhGrVqmH58uVo3LhxsfdBV2tBQQEqVKiAOXPmYMaMGTh06BAaNWpk1PqK0tVWUFAAOzs7fPrpp5g9ezZiY2NRpUoVs9Ti5+eH/v37w9PTExs3boSfnx/8/Pyg0WjQoEEDk/5fBB4cYd++fTs6deqE2bNnIykpCW3btsVvv/2GPXv2YMaMGSatR8fKygoFBQVwc3PDsWPHcOTIEXh4eGDx4sXYtWsXpk2bZpKj2Q/r87ozD3Z2dpg1axYcHR3RtWtXDBs2DGXLlsXNmzcRFxeHRYsWGb0+i2SGcUnPtZMnT4q9vb3Uq1dPDh069NCBhLrBq2PGjJEhQ4bI7Nmzxc7Ozix309XV0rx5c/npp59k2rRp4ujo+NhL141t165dyh2jC/8MxLp168zycx46x48fl0qVKkm9evXk448/lp07d0pERISULVtWzp07Z5aadL//07dvX+nZs6ccPnxYLl++LBMmTJBy5cqZ5OqwJ/X5W7duybBhw8TGxkY++OADmTJligwfPlycnZ3l1KlTRqvr+vXr0q5dO+nVq5fUqlVL6tevLwcPHnzk4N6oqCjRaDTi6upq1v4vItKuXTvx9vYWOzs7s9eSm5srS5culaNHj4qI5VxKfebMGRk0aJBUrVpVatWqJU2aNJEjR46Yuyw5cuSIVKhQQby9vaV27dpSvXp1k90w8Vn6/NKlS6Vz587i7+8vr732msTHx5ukRkvEIGRA3PEahqXueEW48y3Kkne88fHxMmTIENm/f7/k5ORInTp1HlvfokWLpFSpUspViuaguyVEo0aNRKPRmLWWwiz1xzZzc3Plzp07cu3aNZP/Ft3jJCUlydq1a+W3335Trgg0hafp84X7/p07dyQ/P1+ysrJMVqMl0oiY4ISlShw9ehQLFy7EgAED0KhRI+WQ5NKlS+Hv71/ssOjixYvxwQcf4MiRI6hTp45ZahYR5OTkoHnz5oiPj0dCQoLZainsYeM5LMX9+/eRk5ODu3fvwsnJCaVLlzZ3SQCACxcuIDY2Fo6OjmjUqJEyIN6YnqbPS6GxaHfv3oWjoyNycnLg4OBg1Nru3buHs2fPok6dOrC2tkZ2djb8/f1hY2ODpUuXokmTJgCgnB4GgOvXr6NcuXJGretpnDx5EiKC2rVrm7sUKkGets/fv39fGatKAIOQAXHHS2pTUna8ubm5sLW1RW5uLho1aqTUV7duXcycOROurq4ICwszW31Ehva4Pj9r1iy4urpi2LBh5i7TIjAIGQl3vKQ2lr7j1X0B0dVnZ2cHX19fbNq0CfHx8fwSQM8d9vmnwyBkROyEpDaW3ud19d25cweurq5wdXXF9u3b0bBhQ7PWRWQs7PNPxsvnjcjGxgZ5eXmwtbXF/v374erqiosXLyI2NtbsHwhExmDpfd7Gxgb37t1DZGQk7O3tsWfPHouoi8hY2OefjEHIyNgJSW0svc/fuHEDZ8+exc6dOy2qLiJjYZ9/PAYhE2AnJLWx5D5fsWJFrF+/3uhXrRFZCvb5x+MYIRMQEWRnZ7MTkmqwzxNRScEgRERERKplmXesIyIiIjIBBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSLQYhIiIiUi0GISIiIlItBiEiIiJSrf8HGS1n6T7/3k4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pdt_tz = pytz.timezone(\"America/Los_Angeles\")\n", "hour_created = [b.time_created.astimezone(pdt_tz).time().hour for b in blobs]\n", "hour_counts = Counter(hour_created)\n", "\n", "hours = sorted(hour_counts.keys())\n", "counts = [hour_counts[hour] for hour in hours]\n", "\n", "plt.bar(hours, counts)\n", "plt.title(\"blob uploads by hour\")\n", "plt.xticks(\n", "    range(0, 24, 2),\n", "    [f'{h%12 or 12} {\"AM\" if h<12 else \"PM\"}' for h in range(0, 24, 2)],\n", "    rotation=45,\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAJOCAYAAABm7rQwAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAAB7JklEQVR4nO3deVhUdf//8dcAsrgAboAkKmm5L4VplKkpiWam3bZofnPJtEwq9b4rtXKt2+q+La1Ms0WttEW7M83SzErvyl3JLS1N01IwN0hURPj8/vDHuRlARYMzM4fn47rmyjnnM+e8XwNz5tObM2dcxhgjAAAAAAAAwEZ+ni4AAAAAAAAApQ9NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSnAQcaMGSOXy6VDhw5dcGytWrXUt2/fi97HN998I5fLpXnz5l1ChSVj5syZcrlc2rNnj+377tu3r2rVqvWXt7Nnzx65XC79+9///utFXYLc3x0AAHxB7nv/unXrbN3vxcy1ipvL5dKYMWNs329xyJ3nzJw50/Z9F+c8sVatWrrlllv+elGXIHcO/s0333hk/0BJoSkFAPhLXn31VY9MMgEAQNH885//1Pz58z1dBjzks88+89mGJpyPphQA4C+hKQUAQMk7efKknnzyyUt6LE2p0u2zzz7T2LFjPV0GUKgATxcAAAAAADi/4OBgT5cAAMWOM6UABzp06JDuvPNOhYaGqnLlynrkkUd06tSpCz7ul19+0R133KFKlSqpbNmyuvbaa7Vo0aJCx2ZnZ2vkyJGKiopSuXLldOutt2rfvn0X3Me5rsFU2DWNXC6XkpKSNHv2bNWtW1fBwcGKi4vTihUrLrgf6ewZPA0bNlRQUJCio6M1ePBgHTt2zG3Mf//7X91xxx2qUaOGgoKCFBMTo6FDh+rkyZMFtjd//nw1atRIwcHBatSokT7++ONC9/v+++8rLi5OFSpUUGhoqBo3bqzJkycXqWZJevHFF1WzZk2FhISoTZs22rJli7VuxowZcrlc2rhxY4HH/fOf/5S/v79+//33827/22+/1TXXXKPg4GDVrl1br732WqHjZsyYoXbt2ikiIkJBQUFq0KCBpk6d6jamVq1a2rp1q5YvXy6XyyWXy6W2bdta648dO6YhQ4YoJiZGQUFBqlOnjp577jnl5OQU+fkAAJQ+v//+u/r376/o6GgFBQUpNjZWgwYN0unTp93GZWZmatiwYapatarKlSun2267TX/88UeB7X3++ee64YYbVK5cOVWoUEGdO3fW1q1bC4zbvn277rzzTlWtWlUhISGqW7eunnjiifPW+uuvv6pOnTpq1KiRUlNTJUlt27ZVo0aNtH79el133XUKCQlRbGyspk2bVuDxBw8eVP/+/RUZGang4GA1bdpUs2bNKjAu/zWlcudOO3fuVN++fRUeHq6wsDD169dPJ06ccHtcRkaGZs2aZb1Xn++6oue6BlNh1zS6mJyF+eqrr6yfS3h4uLp27aoff/zRbcyvv/6qBx98UHXr1lVISIgqV66sO+64o9BrRG3dulXt2rVTSEiIqlevrqeffrrQOce6deuUmJioKlWqWDXfe++9RapZkr744gs1a9ZMwcHBatCggf7zn/9Y63755Re5XC69+OKLBR73/fffy+Vy6b333jvv9n/77Td169ZN5cqVU0REhIYOHarMzMwC44oyj+3bt6+mTJkiSdbPP++cOycnR5MmTVLDhg0VHBysyMhI3X///Tp69GiRnw/gr+BMKcCB7rzzTtWqVUsTJkzQqlWr9NJLL+no0aN6++23z/mY1NRUXXfddTpx4oQefvhhVa5cWbNmzdKtt96qefPm6bbbbnMb/8wzz8jlcunxxx/XwYMHNWnSJCUkJCg5OVkhISHFlmX58uX64IMP9PDDDysoKEivvvqqOnbsqDVr1qhRo0bnfNyYMWM0duxYJSQkaNCgQdqxY4emTp2qtWvX6rvvvlOZMmUkSXPnztWJEyc0aNAgVa5cWWvWrNHLL7+s3377TXPnzrW298UXX6h79+5q0KCBJkyYoMOHD6tfv36qXr26236XLl2qnj17qn379nruueckST/++KO+++47PfLIIxfM+/bbb+vPP//U4MGDderUKU2ePFnt2rXT5s2bFRkZqdtvv12DBw/W7NmzddVVV7k9dvbs2Wrbtq0uu+yyc25/8+bN6tChg6pWraoxY8bozJkzGj16tCIjIwuMnTp1qho2bKhbb71VAQEBWrhwoR588EHl5ORo8ODBkqRJkybpoYceUvny5a1Je+62Tpw4oTZt2uj333/X/fffrxo1auj777/XiBEjdODAAU2aNOmCzwcAoPTZv3+/WrRooWPHjmngwIGqV6+efv/9d82bN08nTpxQYGCgNfahhx5SxYoVNXr0aO3Zs0eTJk1SUlKSPvjgA2vMO++8oz59+igxMVHPPfecTpw4oalTp6pVq1bauHGj9ceyTZs26YYbblCZMmU0cOBA1apVS7t27dLChQv1zDPPFFrrrl271K5dO1WqVElLly5VlSpVrHVHjx7VzTffrDvvvFM9e/bUhx9+qEGDBikwMNBqgJw8eVJt27bVzp07lZSUpNjYWM2dO1d9+/bVsWPHijR3uPPOOxUbG6sJEyZow4YNeuONNxQREWHNQ9555x3dd999atGihQYOHChJql279sX9UM6jKDkL8+WXX6pTp066/PLLNWbMGJ08eVIvv/yyrr/+em3YsMH6uaxdu1bff/+9evTooerVq2vPnj2aOnWq2rZtq23btqls2bKSpJSUFN144406c+aMhg8frnLlymn69OkF5qUHDx605kLDhw9XeHi49uzZ49ZYOp+ff/5Zd911lx544AH16dNHM2bM0B133KHFixfrpptu0uWXX67rr79es2fP1tChQ90eO3v2bFWoUEFdu3Y95/ZPnjyp9u3ba+/evXr44YcVHR2td955R1999VWBsUWZx95///3av3+/li5dqnfeeafANu6//37NnDlT/fr108MPP6zdu3frlVde0caNG93mzECJMQAcY/To0UaSufXWW92WP/jgg0aS+eGHH6xlNWvWNH369LHuDxkyxEgy//3vf61lf/75p4mNjTW1atUy2dnZxhhjvv76ayPJXHbZZSY9Pd0a++GHHxpJZvLkyeetsU+fPqZmzZrnrD0vSUaSWbdunbXs119/NcHBwea2226zls2YMcNIMrt37zbGGHPw4EETGBhoOnToYNVtjDGvvPKKkWTeeusta9mJEycK1DJhwgTjcrnMr7/+ai1r1qyZqVatmjl27Ji17IsvvjCS3PI88sgjJjQ01Jw5c+a8z0N+u3fvNpJMSEiI+e2336zlq1evNpLM0KFDrWU9e/Y00dHRbtk2bNhgJJkZM2acdz/dunUzwcHBbtm2bdtm/P39Czz/hT03iYmJ5vLLL3db1rBhQ9OmTZsCY8ePH2/KlStnfvrpJ7flw4cPN/7+/mbv3r3nrRUAUDr17t3b+Pn5mbVr1xZYl5OTY4z533t/QkKCtcwYY4YOHWr8/f2t9+s///zThIeHmwEDBrhtJyUlxYSFhbktb926talQoYLbe2TefRrzv/nKH3/8YX788UcTHR1trrnmGnPkyBG3x7Rp08ZIMhMnTrSWZWZmmmbNmpmIiAhz+vRpY4wxkyZNMpLMu+++a407ffq0iY+PN+XLl3eba0kyo0ePLlDLvffe67bv2267zVSuXNltWbly5dzmfeeTf16VK3cO+PXXX190ztx5Tt55Su6Yw4cPW8t++OEH4+fnZ3r37m0tK2w+snLlSiPJvP3229ay3Lns6tWrrWUHDx40YWFhbnk+/vhjI6nQ368LqVmzppFkPvroI2tZWlqaqVatmrnqqqusZa+99pqRZH788Udr2enTp02VKlUu+HPI/Z348MMPrWUZGRmmTp06BZ7/os5jBw8eXGCeZ4wx//3vf40kM3v2bLflixcvLnQ5UBL4+B7gQLlnseR66KGHJJ29yOG5fPbZZ2rRooVatWplLStfvrwGDhyoPXv2aNu2bW7je/furQoVKlj3b7/9dlWrVu28+7gU8fHxiouLs+7XqFFDXbt21ZIlS5SdnV3oY7788kudPn1aQ4YMkZ/f/w5zAwYMUGhoqNtHEvP+9SwjI0OHDh3SddddJ2OM9RG5AwcOKDk5WX369FFYWJg1/qabblKDBg3c9h0eHq6MjAwtXbr0kvJ269bN7UynFi1aqGXLlm7Pa+/evbV//359/fXX1rLZs2crJCRE3bt3P+e2s7OztWTJEnXr1k01atSwltevX1+JiYkFxud9btLS0nTo0CG1adNGv/zyi9LS0i6YZe7cubrhhhtUsWJFHTp0yLolJCQoOzu7yB/DBACUHjk5OZo/f766dOmi5s2bF1if/6P+AwcOdFt2ww03KDs7W7/++quks2cwHzt2TD179nR7L/L391fLli2t99I//vhDK1as0L333uv2HlnYPiVpy5YtatOmjWrVqqUvv/xSFStWLDAmICBA999/v3U/MDBQ999/vw4ePKj169dLOjv/ioqKUs+ePa1xZcqU0cMPP6zjx49r+fLlF3zOHnjgAbf7N9xwgw4fPqz09PQLPrY4FCVnfrlzq759+6pSpUrW8iZNmuimm25ym/fknY9kZWXp8OHDqlOnjsLDw7VhwwZr3WeffaZrr71WLVq0sJZVrVpVvXr1ctt3eHi4JOnTTz9VVlbWReeNjo52+wRBaGioevfurY0bNyolJUXS2bPXgoODNXv2bGvckiVLdOjQIf3f//3febf/2WefqVq1arr99tutZWXLlrXOcsurKPPY85k7d67CwsJ00003ub0+4uLiVL58ebe5JlBSaEoBDnTFFVe43a9du7b8/PwK/ex9rl9//VV169YtsLx+/frW+vPtw+VyqU6dOufdx6XIvx9JuvLKK3XixIlCrxkh/a/W/HkCAwN1+eWXu2XZu3evNSEqX768qlatqjZt2kiS1XjJHV9YLfn38eCDD+rKK69Up06dVL16dd17771avHhxUeOeM2/e5/Wmm25StWrVrIlOTk6O3nvvPXXt2tWtUZjfH3/8oZMnTxYphyR99913SkhIsK7zULVqVY0cOVKSitSU+vnnn7V48WJVrVrV7ZaQkCDp7OnzAADk9ccffyg9Pf28H9HPK38DKbc5lHs9nJ9//lmS1K5duwLvR1988YX1XvTLL79IUpH326VLF1WoUEFLlixRaGhooWOio6NVrlw5t2VXXnmlJFnv67/++quuuOIKtz+iSeeefxXmQs9BSStKzvzONVeTzmY/dOiQMjIyJJ39ONuoUaOs61NWqVJFVatW1bFjx9zmI7nPZX7599GmTRt1795dY8eOVZUqVdS1a1fNmDGj0Gs2FaZOnToFGpX584aHh6tLly6aM2eONWb27Nm67LLL1K5du/NuP/caZfn3UdhzVZR57Pn8/PPPSktLU0RERIHXx/Hjx5mrwRZcUwooBQr7C5+nnKuWc531VJKys7N100036ciRI3r88cdVr149lStXTr///rv69u17SRfjjoiIUHJyspYsWaLPP/9cn3/+uWbMmKHevXsXetHSS+Hv76+7775br7/+ul599VV999132r9//wX/8nYxdu3apfbt26tevXp64YUXFBMTo8DAQH322Wd68cUXi/Tc5OTk6KabbtJjjz1W6PrcCRwAAJfK39+/0OXGGEmy3q/eeecdRUVFFRgXEHBp/zvUvXt3zZo1S7Nnz3Y7S8gTLvQcXCxvmqtJZ8/4nzFjhoYMGaL4+HiFhYXJ5XKpR48elzRXc7lcmjdvnlatWqWFCxdqyZIluvfeezVx4kStWrVK5cuXL5a6e/furblz5+r7779X48aNtWDBAj344IMFGpCXqjjmsTk5OYqIiHA7oyuvqlWrFkutwPnQlAIc6Oeff1ZsbKx1f+fOncrJySn0W+9y1axZUzt27CiwfPv27db6/PvIyxijnTt3qkmTJuetrWLFigW+AU86918C8+9Hkn766SeVLVv2nG+UubXu2LFDl19+ubX89OnT2r17t3WmzubNm/XTTz9p1qxZ6t27tzUu/0fvcrdXWC2FPWeBgYHq0qWLunTpopycHD344IN67bXX9NRTT6lOnTqF1nyhvPl/dr1799bEiRO1cOFCff7556patWqhH8HLK/ebhIqSY+HChcrMzNSCBQvc/gJb2Gnc55q81q5dW8ePH7eebwAALqRq1aoKDQ11++bZvyL3gt4RERHnfT/KnS8Udb//+te/FBAQoAcffFAVKlTQ3XffXWDM/v37lZGR4XYW0U8//SRJ1vt6zZo1tWnTJuXk5Lg1K841/7pUF/MHytwzrfLP1841VytKzvzyztXy2759u6pUqWJtb968eerTp48mTpxojTl16lSB+mrWrFnkuZokXXvttbr22mv1zDPPaM6cOerVq5fef/993XfffYWOz7Vz504ZY9ye08LyduzYUVWrVtXs2bPVsmVLnThxQvfcc895t52bY8uWLQX2kT9HUeex0vnnal9++aWuv/76Yv2iIuBi8PE9wIFyv/Y118svvyxJ6tSp0zkfc/PNN2vNmjVauXKltSwjI0PTp09XrVq1Clw7Kfdb4nLNmzdPBw4cOO8+pLNvfmlpadq0aZO17MCBA/r4448LHb9y5Uq36wXs27dPn3zyiTp06HDOvwwmJCQoMDBQL730kttfCd98802lpaWpc+fOkv73l8W8Y4wxmjx5stv2qlWrpmbNmmnWrFlup0IvXbq0wLW2Dh8+7Hbfz8/PatQV5bTw+fPn6/fff7fur1mzRqtXry7wvDZp0kRNmjTRG2+8oY8++kg9evS44F97/f39lZiYqPnz52vv3r3W8h9//FFLliwpMFZyf27S0tI0Y8aMAtstV65coY3GO++8UytXriywbensRPfMmTPnrRcAUPr4+fmpW7duWrhwodatW1dg/cWe/ZOYmKjQ0FD985//LPT6QbmXAqhatapat26tt956y+098lz7dLlcmj59um6//Xb16dNHCxYsKDDmzJkzeu2116z7p0+f1muvvaaqVata18u8+eablZKS4vZtgWfOnNHLL7+s8uXLWx/F+qvO9V5dmNxGXt5rP2ZnZ2v69OmFji9Kzvzyzq3y1rVlyxZ98cUXuvnmm61l/v7+BX4GL7/8coEzt26++WatWrVKa9assZb98ccfBc4COnr0aIHtNWvWTFLR5mr79+93m7emp6fr7bffVrNmzdzOxgsICLC+jXDmzJlq3LjxBf94m5tj//79mjdvnrXsxIkTBZ7/os5jJVkNvvy/A3feeaeys7M1fvz4Ao85c+ZMkX9ngL+CM6UAB9q9e7duvfVWdezYUStXrtS7776ru+++W02bNj3nY4YPH6733ntPnTp10sMPP6xKlSpp1qxZ2r17tz766KMCpxpXqlRJrVq1Ur9+/ZSamqpJkyapTp06GjBgwHlr69Gjhx5//HHddtttevjhh62vZb7yyivdmk+5GjVqpMTERD388MMKCgrSq6++KkkaO3bsOfdRtWpVjRgxQmPHjlXHjh116623aseOHXr11Vd1zTXXWB9zq1evnmrXrq1//OMf+v333xUaGqqPPvqo0GswTJgwQZ07d1arVq1077336siRI3r55ZfVsGFDHT9+3Bp333336ciRI2rXrp2qV6+uX3/9VS+//LKaNWtmXR/ifOrUqaNWrVpp0KBByszM1KRJk1S5cuVCPwLXu3dv/eMf/5CkIn90b+zYsVq8eLFuuOEGPfjgg9bEt2HDhm6Nwg4dOlhnfN1///06fvy4Xn/9dUVEROjAgQNu24yLi9PUqVP19NNPq06dOoqIiFC7du306KOPasGCBbrlllvUt29fxcXFKSMjQ5s3b9a8efO0Z88et6/OBgBAkv75z3/qiy++UJs2bTRw4EDVr19fBw4c0Ny5c/Xtt99aF6ouitDQUE2dOlX33HOPrr76avXo0UNVq1bV3r17tWjRIl1//fV65ZVXJEkvvfSSWrVqpauvvloDBw5UbGys9uzZo0WLFik5ObnAtv38/PTuu++qW7duuvPOO/XZZ5+5XS8oOjpazz33nPbs2aMrr7xSH3zwgZKTkzV9+nSVKVNG0tkLtb/22mvq27ev1q9fr1q1amnevHn67rvvNGnSpPNeK/JixMXF6csvv9QLL7yg6OhoxcbGqmXLloWObdiwoa699lqNGDFCR44cUaVKlfT++++f849JRclZmH/961/q1KmT4uPj1b9/f508eVIvv/yywsLCNGbMGGvcLbfconfeeUdhYWFq0KCBVq5cqS+//FKVK1d2295jjz2md955Rx07dtQjjzyicuXKafr06dbZaLlmzZqlV199Vbfddptq166tP//8U6+//rpCQ0PdmmHncuWVV6p///5au3atIiMj9dZbbyk1NbXQP9z17t1bL730kr7++ms999xzF9y2dPaLeV555RX17t1b69evV7Vq1fTOO++obNmybuMuZh6b2xx8+OGHlZiYKH9/f/Xo0UNt2rTR/fffrwkTJig5OVkdOnRQmTJl9PPPP2vu3LmaPHmy2wXXgRJh+/f9ASgxuV8NvG3bNnP77bebChUqmIoVK5qkpCRz8uRJt7E1a9Ys8JW0u3btMrfffrsJDw83wcHBpkWLFubTTz91G5P7dcDvvfeeGTFihImIiDAhISGmc+fOBb5C+Vy++OIL06hRIxMYGGjq1q1r3n33Xav2vCSZwYMHm3fffddcccUVJigoyFx11VVuX4VrzLm/uviVV14x9erVM2XKlDGRkZFm0KBB5ujRo25jtm3bZhISEkz58uVNlSpVzIABA8wPP/xQ4GuLjTHmo48+MvXr1zdBQUGmQYMG5j//+Y/p06ePqVmzpjVm3rx5pkOHDiYiIsIEBgaaGjVqmPvvv98cOHDgvM9J7lcl/+tf/zITJ040MTExJigoyNxwww3mhx9+KPQxBw4cMP7+/ubKK68877bzW758uYmLizOBgYHm8ssvN9OmTSv0+V+wYIFp0qSJCQ4ONrVq1TLPPfeceeuttwo81ykpKaZz586mQoUKRpJp06aNte7PP/80I0aMMHXq1DGBgYGmSpUq5rrrrjP//ve/ra+JBgAgv19//dX07t3bVK1a1QQFBZnLL7/cDB482GRmZhpj/vfev3btWrfH5c5T8s8Vvv76a5OYmGjCwsJMcHCwqV27tunbt69Zt26d27gtW7aY2267zZoL1a1b1zz11FPW+tz3yz/++MNaduLECdOmTRtTvnx5s2rVKmOMMW3atDENGzY069atM/Hx8SY4ONjUrFnTvPLKKwWypqammn79+pkqVaqYwMBA07hx4wJzEGPOzotGjx593lryPjd536u3b99uWrdubUJCQoykAnPA/Hbt2mUSEhJMUFCQiYyMNCNHjjRLly4t8NwWNWfuPCd/ri+//NJcf/31JiQkxISGhpouXbqYbdu2uY05evSo9fyUL1/eJCYmmu3btxc6l920aZNp06aNCQ4ONpdddpkZP368efPNN92ejw0bNpiePXuaGjVqmKCgIBMREWFuueWWAr8LhalZs6bp3LmzWbJkiWnSpIkJCgoy9erVM3Pnzj3nYxo2bGj8/PzMb7/9dsHt5/r111/NrbfeasqWLWuqVKliHnnkEbN48eICz39R57FnzpwxDz30kKlatapxuVwF5nzTp083cXFxJiQkxFSoUME0btzYPPbYY2b//v1Frhm4VC5jLvEKeABQwlwulwYPHmz9BRPuDh06pGrVqmnUqFF66qmnPF0OAAD4/9q2batDhw4V27WxvFVpyflXXHXVVapUqZKWLVvm6VIAr8Q1pQDAR82cOVPZ2dlFumgmAAAA7LVu3TolJye7XYgcgDuuKQUAPuarr77Stm3b9Mwzz6hbt27n/VZFAAAA2GvLli1av369Jk6cqGrVqumuu+7ydEmA1+JMKQDwMePGjdOwYcPUrFkz65sVAQAA4B3mzZunfv36KSsrS++9956Cg4M9XRLgtbimFAAAAAAAAGzHmVIAAAAAAACwHU0pAAAAAAAA2I4LnZ9HTk6O9u/frwoVKsjlcnm6HAAA4AHGGP3555+Kjo6Wnx9/z7sQ5k8AAKCo8yeaUuexf/9+xcTEeLoMAADgBfbt26fq1at7ugyvx/wJAADkutD8iabUeVSoUEHS2ScxNDTUw9UAAABPSE9PV0xMjDUvwPkxfwIAAEWdP9GUOo/cU85DQ0OZVAEAUMrxUbSiYf4EAAByXWj+xIURAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsF+DpAgAAgHeoNXxRsW9zz7Odi32bAOCtSuI4KnEsBeBcnCkFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYLuLbkqtWLFCXbp0UXR0tFwul+bPn++23hijUaNGqVq1agoJCVFCQoJ+/vlntzFHjhxRr169FBoaqvDwcPXv31/Hjx93G7Np0ybdcMMNCg4OVkxMjJ5//vkCtcydO1f16tVTcHCwGjdurM8+++yiawEAAAAAAID9LroplZGRoaZNm2rKlCmFrn/++ef10ksvadq0aVq9erXKlSunxMREnTp1yhrTq1cvbd26VUuXLtWnn36qFStWaODAgdb69PR0dejQQTVr1tT69ev1r3/9S2PGjNH06dOtMd9//7169uyp/v37a+PGjerWrZu6deumLVu2XFQtAAAAAAAAsJ/LGGMu+cEulz7++GN169ZN0tkzk6Kjo/X3v/9d//jHPyRJaWlpioyM1MyZM9WjRw/9+OOPatCggdauXavmzZtLkhYvXqybb75Zv/32m6KjozV16lQ98cQTSklJUWBgoCRp+PDhmj9/vrZv3y5Juuuuu5SRkaFPP/3Uqufaa69Vs2bNNG3atCLVciHp6ekKCwtTWlqaQkNDL/VpAgDAJ9QavqjYt7nn2c7Fvk27MR+4ODxfKM1K4jgqOeNYCqB0Kep8oFivKbV7926lpKQoISHBWhYWFqaWLVtq5cqVkqSVK1cqPDzcakhJUkJCgvz8/LR69WprTOvWra2GlCQlJiZqx44dOnr0qDUm735yx+Tupyi15JeZman09HS3GwAAAAAAAIpfQHFuLCUlRZIUGRnptjwyMtJal5KSooiICPciAgJUqVIltzGxsbEFtpG7rmLFikpJSbngfi5US34TJkzQ2LFjixYWheKvQwAAAAAAoCj49r08RowYobS0NOu2b98+T5cEAAAAAADgSMXalIqKipIkpaamui1PTU211kVFRengwYNu68+cOaMjR464jSlsG3n3ca4xeddfqJb8goKCFBoa6nYDAAAAAABA8SvWplRsbKyioqK0bNkya1l6erpWr16t+Ph4SVJ8fLyOHTum9evXW2O++uor5eTkqGXLltaYFStWKCsryxqzdOlS1a1bVxUrVrTG5N1P7pjc/RSlFgAAAAAAAHjGRTeljh8/ruTkZCUnJ0s6e0Hx5ORk7d27Vy6XS0OGDNHTTz+tBQsWaPPmzerdu7eio6Otb+irX7++OnbsqAEDBmjNmjX67rvvlJSUpB49eig6OlqSdPfddyswMFD9+/fX1q1b9cEHH2jy5MkaNmyYVccjjzyixYsXa+LEidq+fbvGjBmjdevWKSkpSZKKVAsAAAAAAAA846IvdL5u3TrdeOON1v3cRlGfPn00c+ZMPfbYY8rIyNDAgQN17NgxtWrVSosXL1ZwcLD1mNmzZyspKUnt27eXn5+funfvrpdeeslaHxYWpi+++EKDBw9WXFycqlSpolGjRmngwIHWmOuuu05z5szRk08+qZEjR+qKK67Q/Pnz1ahRI2tMUWoBAAAAAACA/VzGGOPpIrxVenq6wsLClJaWxvWliohv3wMA31USx3AnHL+ZD1wcni+UZsyFAeCsos4H+PY9AAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAApcTUqVPVpEkThYaGKjQ0VPHx8fr888+t9W3btpXL5XK7PfDAAx6sGAAAOFmApwsAAACAPapXr65nn31WV1xxhYwxmjVrlrp27aqNGzeqYcOGkqQBAwZo3Lhx1mPKli3rqXIBAIDD0ZQCAAAoJbp06eJ2/5lnntHUqVO1atUqqylVtmxZRUVFeaI8AABQyvDxPQAAgFIoOztb77//vjIyMhQfH28tnz17tqpUqaJGjRppxIgROnHixHm3k5mZqfT0dLcbAABAUXCmFAAAQCmyefNmxcfH69SpUypfvrw+/vhjNWjQQJJ09913q2bNmoqOjtamTZv0+OOPa8eOHfrPf/5zzu1NmDBBY8eOtat8AADgIDSlAAAASpG6desqOTlZaWlpmjdvnvr06aPly5erQYMGGjhwoDWucePGqlatmtq3b69du3apdu3ahW5vxIgRGjZsmHU/PT1dMTExJZ4DAAD4PppSAAAApUhgYKDq1KkjSYqLi9PatWs1efJkvfbaawXGtmzZUpK0c+fOczalgoKCFBQUVHIFAwAAx+KaUgAAAKVYTk6OMjMzC12XnJwsSapWrZqNFQEAgNKCM6UAAABKiREjRqhTp06qUaOG/vzzT82ZM0fffPONlixZol27dmnOnDm6+eabVblyZW3atElDhw5V69at1aRJE0+XDgAAHIimFAAAQClx8OBB9e7dWwcOHFBYWJiaNGmiJUuW6KabbtK+ffv05ZdfatKkScrIyFBMTIy6d++uJ5980tNlAwAAh6IpBQAAUEq8+eab51wXExOj5cuX21gNAAAo7bimFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHYBni4AgHPVGr6o2Le559nOxb5NAAAAAID9OFMKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAACglpk6dqiZNmig0NFShoaGKj4/X559/bq0/deqUBg8erMqVK6t8+fLq3r27UlNTPVgxAABwMppSAAAApUT16tX17LPPav369Vq3bp3atWunrl27auvWrZKkoUOHauHChZo7d66WL1+u/fv3629/+5uHqwYAAE4V4OkCAAAAYI8uXbq43X/mmWc0depUrVq1StWrV9ebb76pOXPmqF27dpKkGTNmqH79+lq1apWuvfZaT5QMAAAcjDOlAAAASqHs7Gy9//77ysjIUHx8vNavX6+srCwlJCRYY+rVq6caNWpo5cqV59xOZmam0tPT3W4AAABFQVMKAACgFNm8ebPKly+voKAgPfDAA/r444/VoEEDpaSkKDAwUOHh4W7jIyMjlZKScs7tTZgwQWFhYdYtJiamhBMAAACnoCkFAABQitStW1fJyclavXq1Bg0apD59+mjbtm2XvL0RI0YoLS3Nuu3bt68YqwUAAE7GNaUAAABKkcDAQNWpU0eSFBcXp7Vr12ry5Mm66667dPr0aR07dsztbKnU1FRFRUWdc3tBQUEKCgoq6bIBAIADcaYUAABAKZaTk6PMzEzFxcWpTJkyWrZsmbVux44d2rt3r+Lj4z1YIQAAcCrOlAIAACglRowYoU6dOqlGjRr6888/NWfOHH3zzTdasmSJwsLC1L9/fw0bNkyVKlVSaGioHnroIcXHx/PNewAAoEQU+5lS2dnZeuqppxQbG6uQkBDVrl1b48ePlzHGGmOM0ahRo1StWjWFhIQoISFBP//8s9t2jhw5ol69eik0NFTh4eHq37+/jh8/7jZm06ZNuuGGGxQcHKyYmBg9//zzBeqZO3eu6tWrp+DgYDVu3FifffZZcUcGAADwCQcPHlTv3r1Vt25dtW/fXmvXrtWSJUt00003SZJefPFF3XLLLerevbtat26tqKgo/ec///Fw1QAAwKmK/Uyp5557TlOnTtWsWbPUsGFDrVu3Tv369VNYWJgefvhhSdLzzz+vl156SbNmzVJsbKyeeuopJSYmatu2bQoODpYk9erVSwcOHNDSpUuVlZWlfv36aeDAgZozZ44kKT09XR06dFBCQoKmTZumzZs3695771V4eLgGDhwoSfr+++/Vs2dPTZgwQbfccovmzJmjbt26acOGDWrUqFFxRwcAAPBqb7755nnXBwcHa8qUKZoyZYpNFQEAgNKs2M+U+v7779W1a1d17txZtWrV0u23364OHTpozZo1ks6eJTVp0iQ9+eST6tq1q5o0aaK3335b+/fv1/z58yVJP/74oxYvXqw33nhDLVu2VKtWrfTyyy/r/fff1/79+yVJs2fP1unTp/XWW2+pYcOG6tGjhx5++GG98MILVi2TJ09Wx44d9eijj6p+/foaP368rr76ar3yyivFHRsAAAAAAAAXodibUtddd52WLVumn376SZL0ww8/6Ntvv1WnTp0kSbt371ZKSooSEhKsx4SFhally5ZauXKlJGnlypUKDw9X8+bNrTEJCQny8/PT6tWrrTGtW7dWYGCgNSYxMVE7duzQ0aNHrTF595M7Jnc/AAAAAAAA8Ixi//je8OHDlZ6ernr16snf31/Z2dl65pln1KtXL0lSSkqKJCkyMtLtcZGRkda6lJQURUREuBcaEKBKlSq5jYmNjS2wjdx1FStWVEpKynn3k19mZqYyMzOt++np6ReVHQAAAAAAAEVT7GdKffjhh5o9e7bmzJmjDRs2aNasWfr3v/+tWbNmFfeuit2ECRMUFhZm3WJiYjxdEgAAAAAAgCMVe1Pq0Ucf1fDhw9WjRw81btxY99xzj4YOHaoJEyZIkqKioiRJqampbo9LTU211kVFRengwYNu68+cOaMjR464jSlsG3n3ca4xuevzGzFihNLS0qzbvn37Ljo/AAAAAAAALqzYm1InTpyQn5/7Zv39/ZWTkyNJio2NVVRUlJYtW2atT09P1+rVqxUfHy9Jio+P17Fjx7R+/XprzFdffaWcnBy1bNnSGrNixQplZWVZY5YuXaq6deuqYsWK1pi8+8kdk7uf/IKCghQaGup2AwAAAAAAQPEr9qZUly5d9Mwzz2jRokXas2ePPv74Y73wwgu67bbbJEkul0tDhgzR008/rQULFmjz5s3q3bu3oqOj1a1bN0lS/fr11bFjRw0YMEBr1qzRd999p6SkJPXo0UPR0dGSpLvvvluBgYHq37+/tm7dqg8++ECTJ0/WsGHDrFoeeeQRLV68WBMnTtT27ds1ZswYrVu3TklJScUdGwAAAAAAABeh2C90/vLLL+upp57Sgw8+qIMHDyo6Olr333+/Ro0aZY157LHHlJGRoYEDB+rYsWNq1aqVFi9erODgYGvM7NmzlZSUpPbt28vPz0/du3fXSy+9ZK0PCwvTF198ocGDBysuLk5VqlTRqFGjNHDgQGvMddddpzlz5ujJJ5/UyJEjdcUVV2j+/Plq1KhRcccGAAAAAADARXAZY4yni/BW6enpCgsLU1paGh/lK6JawxeVyHb3PNu5RLaLklUSvw/8LgAlh9ds4ZgPXByeL5RmzIUB4KyizgeK/eN7AAAAAAAAwIXQlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAKCUmDBhgq655hpVqFBBERER6tatm3bs2OE2pm3btnK5XG63Bx54wEMVAwAAJ6MpBQAAUEosX75cgwcP1qpVq7R06VJlZWWpQ4cOysjIcBs3YMAAHThwwLo9//zzHqoYAAA4WYCnCwAAAIA9Fi9e7HZ/5syZioiI0Pr169W6dWtredmyZRUVFWV3eQAAoJThTCkAAIBSKi0tTZJUqVIlt+WzZ89WlSpV1KhRI40YMUInTpw45zYyMzOVnp7udgMAACgKzpQCAAAohXJycjRkyBBdf/31atSokbX87rvvVs2aNRUdHa1Nmzbp8ccf144dO/Sf//yn0O1MmDBBY8eOtatsAADgIDSlAAAASqHBgwdry5Yt+vbbb92WDxw40Pp348aNVa1aNbVv3167du1S7dq1C2xnxIgRGjZsmHU/PT1dMTExJVc4AABwDJpSAAAApUxSUpI+/fRTrVixQtWrVz/v2JYtW0qSdu7cWWhTKigoSEFBQSVSJwAAcDaaUgAAAKWEMUYPPfSQPv74Y33zzTeKjY294GOSk5MlSdWqVSvh6gAAQGlDUwoAAKCUGDx4sObMmaNPPvlEFSpUUEpKiiQpLCxMISEh2rVrl+bMmaObb75ZlStX1qZNmzR06FC1bt1aTZo08XD1AADAaWhKAQAAlBJTp06VJLVt29Zt+YwZM9S3b18FBgbqyy+/1KRJk5SRkaGYmBh1795dTz75pAeqBQAATkdTCgAAoJQwxpx3fUxMjJYvX25TNQAAoLTz83QBAAAAAAAAKH1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1KpCn1+++/6//+7/9UuXJlhYSEqHHjxlq3bp213hijUaNGqVq1agoJCVFCQoJ+/vlnt20cOXJEvXr1UmhoqMLDw9W/f38dP37cbcymTZt0ww03KDg4WDExMXr++ecL1DJ37lzVq1dPwcHBaty4sT777LOSiAwAAAAAAICLUOxNqaNHj+r6669XmTJl9Pnnn2vbtm2aOHGiKlasaI15/vnn9dJLL2natGlavXq1ypUrp8TERJ06dcoa06tXL23dulVLly7Vp59+qhUrVmjgwIHW+vT0dHXo0EE1a9bU+vXr9a9//UtjxozR9OnTrTHff/+9evbsqf79+2vjxo3q1q2bunXrpi1bthR3bAAAAAAAAFyEgOLe4HPPPaeYmBjNmDHDWhYbG2v92xijSZMm6cknn1TXrl0lSW+//bYiIyM1f/589ejRQz/++KMWL16stWvXqnnz5pKkl19+WTfffLP+/e9/Kzo6WrNnz9bp06f11ltvKTAwUA0bNlRycrJeeOEFq3k1efJkdezYUY8++qgkafz48Vq6dKleeeUVTZs2rbijAwAAAAAAoIiK/UypBQsWqHnz5rrjjjsUERGhq666Sq+//rq1fvfu3UpJSVFCQoK1LCwsTC1bttTKlSslSStXrlR4eLjVkJKkhIQE+fn5afXq1daY1q1bKzAw0BqTmJioHTt26OjRo9aYvPvJHZO7HwAAAAAAAHhGsTelfvnlF02dOlVXXHGFlixZokGDBunhhx/WrFmzJEkpKSmSpMjISLfHRUZGWutSUlIUERHhtj4gIECVKlVyG1PYNvLu41xjctfnl5mZqfT0dLcbAAAAAAAAil+xf3wvJydHzZs31z//+U9J0lVXXaUtW7Zo2rRp6tOnT3HvrlhNmDBBY8eO9XQZAAAAAAAAjlfsZ0pVq1ZNDRo0cFtWv3597d27V5IUFRUlSUpNTXUbk5qaaq2LiorSwYMH3dafOXNGR44ccRtT2Dby7uNcY3LX5zdixAilpaVZt3379hUtNAAAAAAAAC5KsTelrr/+eu3YscNt2U8//aSaNWtKOnvR86ioKC1btsxan56ertWrVys+Pl6SFB8fr2PHjmn9+vXWmK+++ko5OTlq2bKlNWbFihXKysqyxixdulR169a1vukvPj7ebT+5Y3L3k19QUJBCQ0PdbgAAAAAAACh+xd6UGjp0qFatWqV//vOf2rlzp+bMmaPp06dr8ODBkiSXy6UhQ4bo6aef1oIFC7R582b17t1b0dHR6tatm6SzZ1Z17NhRAwYM0Jo1a/Tdd98pKSlJPXr0UHR0tCTp7rvvVmBgoPr376+tW7fqgw8+0OTJkzVs2DCrlkceeUSLFy/WxIkTtX37do0ZM0br1q1TUlJScccGAAAAAADARSj2a0pdc801+vjjjzVixAiNGzdOsbGxmjRpknr16mWNeeyxx5SRkaGBAwfq2LFjatWqlRYvXqzg4GBrzOzZs5WUlKT27dvLz89P3bt310svvWStDwsL0xdffKHBgwcrLi5OVapU0ahRozRw4EBrzHXXXac5c+boySef1MiRI3XFFVdo/vz5atSoUXHHBgAAAAAAwEUo9qaUJN1yyy265ZZbzrne5XJp3LhxGjdu3DnHVKpUSXPmzDnvfpo0aaL//ve/5x1zxx136I477jh/wQAAAAAAALBVsX98DwAAAAAAALgQmlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2K5Fv3wMAAMWn1vBFxb7NPc92LvZtAgDgy0ri/VbiPRc4H86UAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAACglJgwYYKuueYaVahQQREREerWrZt27NjhNubUqVMaPHiwKleurPLly6t79+5KTU31UMUAAMDJaEoBAACUEsuXL9fgwYO1atUqLV26VFlZWerQoYMyMjKsMUOHDtXChQs1d+5cLV++XPv379ff/vY3D1YNAACcKsDTBQAAAMAeixcvdrs/c+ZMRUREaP369WrdurXS0tL05ptvas6cOWrXrp0kacaMGapfv75WrVqla6+91hNlAwAAh+JMKQAAgFIqLS1NklSpUiVJ0vr165WVlaWEhARrTL169VSjRg2tXLmy0G1kZmYqPT3d7QYAAFAUNKUAAABKoZycHA0ZMkTXX3+9GjVqJElKSUlRYGCgwsPD3cZGRkYqJSWl0O1MmDBBYWFh1i0mJqakSwcAAA5BUwoAAKAUGjx4sLZs2aL333//L21nxIgRSktLs2779u0rpgoBAIDTcU0pAACAUiYpKUmffvqpVqxYoerVq1vLo6KidPr0aR07dsztbKnU1FRFRUUVuq2goCAFBQWVdMkAAMCBOFMKAACglDDGKCkpSR9//LG++uorxcbGuq2Pi4tTmTJltGzZMmvZjh07tHfvXsXHx9tdLgAAcDjOlAIAACglBg8erDlz5uiTTz5RhQoVrOtEhYWFKSQkRGFhYerfv7+GDRumSpUqKTQ0VA899JDi4+P55j0AAFDsaEoBAACUElOnTpUktW3b1m35jBkz1LdvX0nSiy++KD8/P3Xv3l2ZmZlKTEzUq6++anOlAACgNKApBQAAUEoYYy44Jjg4WFOmTNGUKVNsqAgAAJRmXFMKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDu+fQ8AAAAAAPisWsMXldi29zzbucS2Dc6UAgAAAAAAgAfQlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHYBni4AAAAAAHDxag1fVCLb3fNs5xLZLgDkx5lSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2AZ4uAAAAAADg/WoNX1Qi293zbOcS2S7gFCX12pM8//rjTCkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAEqJFStWqEuXLoqOjpbL5dL8+fPd1vft21cul8vt1rFjR88UCwAAHI+mFAAAQCmRkZGhpk2basqUKecc07FjRx04cMC6vffeezZWCAAASpMATxcAAAAAe3Tq1EmdOnU675igoCBFRUXZVBEAACjNOFMKAAAAlm+++UYRERGqW7euBg0apMOHD3u6JAAA4FCcKQUAAABJZz+697e//U2xsbHatWuXRo4cqU6dOmnlypXy9/cv9DGZmZnKzMy07qenp9tVLgAA8HEl3pR69tlnNWLECD3yyCOaNGmSJOnUqVP6+9//rvfff1+ZmZlKTEzUq6++qsjISOtxe/fu1aBBg/T111+rfPny6tOnjyZMmKCAgP+V/M0332jYsGHaunWrYmJi9OSTT6pv375u+58yZYr+9a9/KSUlRU2bNtXLL7+sFi1alHRsAAAAn9OjRw/r340bN1aTJk1Uu3ZtffPNN2rfvn2hj5kwYYLGjh1rV4mSpFrDF5XIdvc827lEtuvNSuq5lErn84nixWsdcL4S/fje2rVr9dprr6lJkyZuy4cOHaqFCxdq7ty5Wr58ufbv36+//e1v1vrs7Gx17txZp0+f1vfff69Zs2Zp5syZGjVqlDVm9+7d6ty5s2688UYlJydryJAhuu+++7RkyRJrzAcffKBhw4Zp9OjR2rBhg5o2barExEQdPHiwJGMDAAA4wuWXX64qVapo586d5xwzYsQIpaWlWbd9+/bZWCEAAPBlJdaUOn78uHr16qXXX39dFStWtJanpaXpzTff1AsvvKB27dopLi5OM2bM0Pfff69Vq1ZJkr744gtt27ZN7777rpo1a6ZOnTpp/PjxmjJlik6fPi1JmjZtmmJjYzVx4kTVr19fSUlJuv322/Xiiy9a+3rhhRc0YMAA9evXTw0aNNC0adNUtmxZvfXWWyUVGwAAwDF+++03HT58WNWqVTvnmKCgIIWGhrrdAAAAiqLEmlKDBw9W586dlZCQ4LZ8/fr1ysrKclter1491ahRQytXrpQkrVy5Uo0bN3b7OF9iYqLS09O1detWa0z+bScmJlrbOH36tNavX+82xs/PTwkJCdaY/DIzM5Wenu52AwAAcIrjx48rOTlZycnJks6eeZ6cnKy9e/fq+PHjevTRR7Vq1Srt2bNHy5YtU9euXVWnTh0lJiZ6tnAAAOBIJXJNqffff18bNmzQ2rVrC6xLSUlRYGCgwsPD3ZZHRkYqJSXFGpO3IZW7Pnfd+cakp6fr5MmTOnr0qLKzswsds3379kLr9sQ1EQAAAOyybt063Xjjjdb9YcOGSZL69OmjqVOnatOmTZo1a5aOHTum6OhodejQQePHj1dQUJCnSgYAAA5W7E2pffv26ZFHHtHSpUsVHBxc3JsvUSNGjLAmZ9LZb4+JiYnxYEUAAADFp23btjLGnHN93mtzAgAAlLRi//je+vXrdfDgQV199dUKCAhQQECAli9frpdeekkBAQGKjIzU6dOndezYMbfHpaamKioqSpIUFRWl1NTUAutz151vTGhoqEJCQlSlShX5+/sXOiZ3G/lxTQQAAAAAAAB7FHtTqn379tq8ebN1vYLk5GQ1b95cvXr1sv5dpkwZLVu2zHrMjh07tHfvXsXHx0uS4uPjtXnzZrdvyVu6dKlCQ0PVoEEDa0zebeSOyd1GYGCg4uLi3Mbk5ORo2bJl1hgAAAAAAAB4RrF/fK9ChQpq1KiR27Jy5cqpcuXK1vL+/ftr2LBhqlSpkkJDQ/XQQw8pPj5e1157rSSpQ4cOatCgge655x49//zzSklJ0ZNPPqnBgwdb1zR44IEH9Morr+ixxx7Tvffeq6+++koffvihFi1aZO132LBh6tOnj5o3b64WLVpo0qRJysjIUL9+/Yo7NgAPqzV80YUHXaQ9z3Yu9m0CAAAAAM4qkQudX8iLL74oPz8/de/eXZmZmUpMTNSrr75qrff399enn36qQYMGKT4+XuXKlVOfPn00btw4a0xsbKwWLVqkoUOHavLkyapevbreeOMNt2+Hueuuu/THH39o1KhRSklJUbNmzbR48eICFz8HAAAAAACAvWxpSn3zzTdu94ODgzVlyhRNmTLlnI+pWbOmPvvss/Nut23bttq4ceN5xyQlJSkpKanItQIAAAAAAKDkFfs1pQAAAAAAAIALoSkFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdgGeLgAAAADAWbWGLyqxbe95tnOJbRsAShOO1cWHM6UAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAACglVqxYoS5duig6Oloul0vz5893W2+M0ahRo1StWjWFhIQoISFBP//8s2eKBQAAjkdTCgAAoJTIyMhQ06ZNNWXKlELXP//883rppZc0bdo0rV69WuXKlVNiYqJOnTplc6UAAKA0CPB0AQAAALBHp06d1KlTp0LXGWM0adIkPfnkk+ratask6e2331ZkZKTmz5+vHj162FkqAAAoBThTCgAAANq9e7dSUlKUkJBgLQsLC1PLli21cuVKD1YGAACcijOlAAAAoJSUFElSZGSk2/LIyEhrXWEyMzOVmZlp3U9PTy+ZAgEAgOPQlAIAAMAlmzBhgsaOHevpMkpUreGLSmS7e57tXCLbBQDAV/DxPQAAACgqKkqSlJqa6rY8NTXVWleYESNGKC0tzbrt27evROsEAADOQVMKAAAAio2NVVRUlJYtW2YtS09P1+rVqxUfH3/OxwUFBSk0NNTtBgAAUBR8fA8AAKCUOH78uHbu3Gnd3717t5KTk1WpUiXVqFFDQ4YM0dNPP60rrrhCsbGxeuqppxQdHa1u3bp5rmgAAOBYNKUAAABKiXXr1unGG2+07g8bNkyS1KdPH82cOVOPPfaYMjIyNHDgQB07dkytWrXS4sWLFRwc7KmSAQCAg9GUAi6gJC5uyoVNAQCe0LZtWxljzrne5XJp3LhxGjdunI1VAQCA0oprSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsx7fvAQAAAADgcCXxreJS4d8sXlL7Otf+4Ls4UwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYrtibUhMmTNA111yjChUqKCIiQt26ddOOHTvcxpw6dUqDBw9W5cqVVb58eXXv3l2pqaluY/bu3avOnTurbNmyioiI0KOPPqozZ864jfnmm2909dVXKygoSHXq1NHMmTML1DNlyhTVqlVLwcHBatmypdasWVPckQEAAAAAAHCRir0ptXz5cg0ePFirVq3S0qVLlZWVpQ4dOigjI8MaM3ToUC1cuFBz587V8uXLtX//fv3tb3+z1mdnZ6tz5846ffq0vv/+e82aNUszZ87UqFGjrDG7d+9W586ddeONNyo5OVlDhgzRfffdpyVLllhjPvjgAw0bNkyjR4/Whg0b1LRpUyUmJurgwYPFHRsAAAAAAAAXIaC4N7h48WK3+zNnzlRERITWr1+v1q1bKy0tTW+++abmzJmjdu3aSZJmzJih+vXra9WqVbr22mv1xRdfaNu2bfryyy8VGRmpZs2aafz48Xr88cc1ZswYBQYGatq0aYqNjdXEiRMlSfXr19e3336rF198UYmJiZKkF154QQMGDFC/fv0kSdOmTdOiRYv01ltvafjw4cUdHQAAAAAAAEVU4teUSktLkyRVqlRJkrR+/XplZWUpISHBGlOvXj3VqFFDK1eulCStXLlSjRs3VmRkpDUmMTFR6enp2rp1qzUm7zZyx+Ru4/Tp01q/fr3bGD8/PyUkJFhj8svMzFR6errbDQAAAAAAAMWvRJtSOTk5GjJkiK6//no1atRIkpSSkqLAwECFh4e7jY2MjFRKSoo1Jm9DKnd97rrzjUlPT9fJkyd16NAhZWdnFzomdxv5TZgwQWFhYdYtJibm0oIDAAAAAADgvEq0KTV48GBt2bJF77//fknuptiMGDFCaWlp1m3fvn2eLgkAAAAAAMCRiv2aUrmSkpL06aefasWKFapevbq1PCoqSqdPn9axY8fczpZKTU1VVFSUNSb/t+Tlfjtf3jH5v7EvNTVVoaGhCgkJkb+/v/z9/Qsdk7uN/IKCghQUFHRpgQEAAAAAAFBkxX6mlDFGSUlJ+vjjj/XVV18pNjbWbX1cXJzKlCmjZcuWWct27NihvXv3Kj4+XpIUHx+vzZs3u31L3tKlSxUaGqoGDRpYY/JuI3dM7jYCAwMVFxfnNiYnJ0fLli2zxgAAAAAAAMAziv1MqcGDB2vOnDn65JNPVKFCBev6TWFhYQoJCVFYWJj69++vYcOGqVKlSgoNDdVDDz2k+Ph4XXvttZKkDh06qEGDBrrnnnv0/PPPKyUlRU8++aQGDx5sncn0wAMP6JVXXtFjjz2me++9V1999ZU+/PBDLVq0yKpl2LBh6tOnj5o3b64WLVpo0qRJysjIsL6NDwAAAAAAT6g1fNGFB12CPc92LpHtAiWh2JtSU6dOlSS1bdvWbfmMGTPUt29fSdKLL74oPz8/de/eXZmZmUpMTNSrr75qjfX399enn36qQYMGKT4+XuXKlVOfPn00btw4a0xsbKwWLVqkoUOHavLkyapevbreeOMNJSYmWmPuuusu/fHHHxo1apRSUlLUrFkzLV68uMDFzwEAAAAAAGCvYm9KGWMuOCY4OFhTpkzRlClTzjmmZs2a+uyzz867nbZt22rjxo3nHZOUlKSkpKQL1gQAAAAAAAD7lOi37wEAAAAAAACFoSkFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsB1NKQAAAAAAANiOphQAAAAAAABsR1MKAAAAAAAAtgvwdAEAgIJqDV9U7Nvc82znYt8mAAAXoyTe33LxPgcAvoczpQAAAAAAAGA7mlIAAAAAAACwHR/fA4BSjo8KAgAAAPAEzpQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAACwjBkzRi6Xy+1Wr149T5cFAAAciAudAwAAwE3Dhg315ZdfWvcDApgyAgCA4scMAwAAAG4CAgIUFRXl6TIAAIDD8fE9AAAAuPn5558VHR2tyy+/XL169dLevXs9XRIAAHAgzpQCAACApWXLlpo5c6bq1q2rAwcOaOzYsbrhhhu0ZcsWVahQocD4zMxMZWZmWvfT09PtLBcAAPgwmlLwWbWGLyr2be55tnOxbxMAAF/SqVMn699NmjRRy5YtVbNmTX344Yfq379/gfETJkzQ2LFj7SwRAAA4BB/fAwAAwDmFh4fryiuv1M6dOwtdP2LECKWlpVm3ffv22VwhAADwVTSlAAAAcE7Hjx/Xrl27VK1atULXBwUFKTQ01O0GAABQFHx8DwAuAh8bBeB0//jHP9SlSxfVrFlT+/fv1+jRo+Xv76+ePXt6ujQAAOAwNKUAAABg+e2339SzZ08dPnxYVatWVatWrbRq1SpVrVrV06UBAACHoSkFAAAAy/vvv+/pEgAAQClBU6qU4CNHAAAAAADAm3ChcwAAAAAAANiOphQAAAAAAABsx8f3AACA7fhYOQAAADhTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAAAAAAABgO5pSAAAAAAAAsF2ApwsAAJQOtYYvKvZt7nm2c7FvEwAAAIA9OFMKAAAAAAAAtqMpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALYL8HQBAAAUp1rDFxX7Nvc827nYtwkAAACUdpwpBQAAAAAAANvRlAIAAAAAAIDtaEoBAAAAAADAdjSlAAAAAAAAYDuaUgAAAAAAALAdTSkAAAAAAADYjqYUAAAAAAAAbEdTCgAAAAAAALajKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHYBni4AwP/UGr6o2Le559nOxb5NAAAAAAD+KppSAAAAABypJP7gl4s//AHAX8fH9wAAAAAAAGA7mlIAAAAAAACwHR/fA0ohrl0FAAAAAPA0zpQCAAAAAACA7WhKAQAAAAAAwHZ8fA8AgEvER2EBAACAS1cqzpSaMmWKatWqpeDgYLVs2VJr1qzxdEkAAABejfkTAAAoaY5vSn3wwQcaNmyYRo8erQ0bNqhp06ZKTEzUwYMHPV0aAACAV2L+BAAA7OD4ptQLL7ygAQMGqF+/fmrQoIGmTZumsmXL6q233vJ0aQAAAF6J+RMAALCDo5tSp0+f1vr165WQkGAt8/PzU0JCglauXOnBygAAALwT8ycAAGAXR1/o/NChQ8rOzlZkZKTb8sjISG3fvr3A+MzMTGVmZlr309LSJEnp6eklVmOj0UuKfZtbxiYWWJaTeaLY91PY81IS+7FzX57cj537ctp+7NyX0/Zj576cth879+W0/di9r+LarjGmRLbvbXxh/mTnfMPu/ZXUvtifb+/PG3432R/7u5j9OeW1V1r3V5zbveD8yTjY77//biSZ77//3m35o48+alq0aFFg/OjRo40kbty4cePGjRu3Ard9+/bZNYXxKOZP3Lhx48aNG7fiul1o/uToM6WqVKkif39/paamui1PTU1VVFRUgfEjRozQsGHDrPs5OTk6cuSIKleuLJfLVeL1nkt6erpiYmK0b98+hYaGeqyO4kQm7+e0PBKZfIHT8khk8hXny2SM0Z9//qno6GgPVWevkpw/OfF3Jy/y+S4nZ5PI5+vI57ucnE0qnvmTo5tSgYGBiouL07Jly9StWzdJZydKy5YtU1JSUoHxQUFBCgoKclsWHh5uQ6VFExoa6rhfZDJ5P6flkcjkC5yWRyKTrzhXprCwMA9U4xl2zJ+c+LuTF/l8l5OzSeTzdeTzXU7OJv21+ZOjm1KSNGzYMPXp00fNmzdXixYtNGnSJGVkZKhfv36eLg0AAMArMX8CAAB2cHxT6q677tIff/yhUaNGKSUlRc2aNdPixYsLXLwTAAAAZzF/AgAAdnB8U0qSkpKSCj3d3FcEBQVp9OjRBU6N92Vk8n5OyyORyRc4LY9EJl/hxEx/VUnMn5z+PJPPdzk5m0Q+X0c+3+XkbFLx5HMZU0q+3xgAAAAAAABew8/TBQAAAAAAAKD0oSkFAAAAAAAA29GUAgAAAAAAgO1oSgEAAAAAAMB2NKUAwEvxPRQAAKC0Yz4EOBtNKQ86ePCgp0soVjt27NAjjzzi6TJKVE5OjqdLKBa8uXu3I0eOSJJcLpeHKyk+O3fu1LPPPuvpMkqMU15TTskBezltPpNfaZjf5OWUuU5eHNt8kxPnQ3k5fW6UnxNfh07M5AkuwzPpERs3blRcXJy++eYbtW7d2tPl/GU//PCD2rdvr4yMDK1evVpNmjTxdEl/2e7du/Xtt9/qyJEjatCggW666SZJZw8+vvrmePz4cQUFBalMmTI+nSPX3r179d///leHDx9WfHy8rrnmGk+X9JflHhvWrFmj5s2be7qcYrFp0ybdeOONCgkJUXJysqpUqeLpkv6SvXv36scff9TBgwfVvHlz1a9fX5KUnZ0tf39/D1d3aZx2bJCceXzwRk6bz+TnxPlNXk6c6+TlxGNbXk4+zjlxPpSX0+ZG+TlxrpQXx5ZiZmC75ORkU6FCBTNs2DBPl1IskpOTTXBwsBk8eLCpVauWefzxxz1d0l+2adMmU6VKFdOtWzdz5ZVXmquvvtq0bdvWpKWlGWOMycnJ8XCFF2/btm0mISHBvPPOOyYzM9MY45s5cm3atMlUr17dtG/f3oSHh5s2bdqYDRs2eLqsv2Tjxo2mQoUK5u9//7unSyk2ycnJJiQkxPTr189UqlTJvPDCC54u6S/54YcfTEREhOnUqZOpXLmyufbaa03v3r2t9WfOnPFgdZfGaccGY5x5fPBGTpvP5OfE+U1eTpzr5OXEY1teTj7OOXE+lJfT5kb5OXGulBfHluJHU8pmmzdvNmXLljVPPvmkMebsL/BPP/1kvvnmG7N//34PV3fxNmzYYEJCQszw4cONMcb861//MrGxseaHH37wcGWX7vDhw6ZZs2bW5DM9Pd3Mnj3buFwuc/3111s/p+zsbE+WeVH27Nlj6tevbwIDA821115r5s6d69MH0e3bt5uoqCjzxBNPmJMnT5rff//dVKlSxcyePdvTpV2yzZs3m5CQEDNq1ChjzNmfy4EDB0xycrI5ffq0h6u7NBs3bnQ7Pjz00EPm2muvNb/99puHK7s0qamppkGDBmbkyJEmKyvLHDp0yIwdO9a4XC7TsWNHaxzHBs9y4vHBGzltPpOfE+c3eTlxrpOXE49teTn5OOfE+VBeTpsb5efEuVJeHFtKBk0pG506dcp07drVuFwua9nNN99srrrqKuNyuUxcXJx55JFHPFfgRfrtt99MTEyMeeyxx6xl3333nbnsssvMm2++aYzxzU745s2bTaNGjcxPP/1kLTtw4IBp1KiRqVatmrnqqqs8WN3FO3PmjJk4caLp0qWLSU5ONh07djRXXXWVzx5EMzIyzIABA8zAgQNNVlaW9aZ2xx13mHHjxpnRo0f73KTszz//NO3atTOhoaHWsttuu806NrRp08a8+OKLnivwEvzyyy8mPDzcjBgxwlo2f/58U6FCBfPFF18YY3xvQvLtt9+apk2buk0ct2/fbmrWrGmqVKniNtnyBU47NhjjzOODN3LafCY/p85v8nLaXCcvJx7b8nLycc6J86G8nDg3ys9pc6W8OLaU3LGFppSNcnJyzOrVq02DBg1MixYtTEJCgunSpYv58ssvzZYtW8yYMWNM06ZNzdixYz1dapGkpqaahQsXFlg+cOBAExsba53+7Ws2b95satasaT766CNr2fbt202TJk3M+++/b2JjY82zzz7rwQov3saNG83cuXONMWff7PIeRE+dOmWM8Z2D6KlTp8ynn35qkpOTrWXjxo0zLpfL/N///Z9p1aqVadKkiU/9D9HJkyfNnDlzTN26dc0tt9xiOnToYLp06WI++ugjs3LlStO7d29zzTXXmJkzZ3q61CLbu3evefvttwss79atm7n++uvNiRMnPFDVX7Ns2TJTs2ZNs2bNGmtZcnKyad68uZk8ebKpW7eumTNnjgcrvHhOOjYY48zjgzdy2nwmP6fOb/Jy4lwnL6cd2/Jy8nHOifOhvJw4N8rPiXOlvDi2lMyxhaaUTfL+cm7YsME0adLEXH311Wbfvn3W8hMnTph77rnHtG/f3uq2eqvCuvi5y5YvX25q165tPvzww3OO9WaHDx827du3N127djUTJkwwCxcuNOHh4Wbo0KHGGGPuuusu07dvXw9XeXGysrLc7mdmZrodRHNPh54/f74nyrtoJ0+etP69efNmU758efPJJ59Yy0aOHGmuvvpqk5KS4onyLsmJEyfMRx99ZGrXrm3i4+PdPv5y7Ngxc8MNN5i77rrLgxX+NbnHwLfffttcfvnlZvXq1cYY3zo+7N2711x++eWmV69eZs6cOeabb74xYWFhZuTIkcYYY+Lj433u+hf5Pwrh68cGY5x5fPAmTpvP5Ofk+U1eTpzr5OW0eU9+Tj7OOX0+lJcT5kb5OXGulJcT5015eerYElCyl1FHVlaWypQpI+l/32TSrFkzvfPOOzpw4ICioqIknf0mgpCQENWtW1dbt2712q/jzc1T2DcM+Pn5SZJat26tyMhIvfXWW7rjjjus5d4qN5M526RVpUqV9Morr+jJJ5/UrFmzZIxRUlKSxo8fL0mKiIjQTz/95OGqzy89PV2HDx9WUFCQKlasqJCQEOv3Lzs7W4GBgZo/f766deumf/7zn8rOztbXX3+tBQsW6JprrlF0dLSnI7jJmyc8PFxly5a1voK1UaNG+vnnnxUVFaWcnBz5+fmpdu3aOnXqlIKCgjxc+bkVlikxMVHBwcHy8/NTRESEpLPHhrCwMF199dXasGGDldEbne/3LveY0bNnT40fP15TpkxRixYtvDaL5J4nNDRUMTEx+uCDDzRgwACtWrVKp0+f1gMPPKBnnnlGkhQbG6vff//dw1Wf36FDh7Rv3z6VLVtWERERqlixovU7debMGZ87NkiFZ/L144M3ctp8Jj8nzm/ycuJcJy+nzXvyc+I8KJcT50N5OW1ulJ8T50p5OXHelJfXzKGKtcUFN9u3bzd33323Wb9+vbUstyOenZ1daBe8X79+pm/fvgX+wuMNCsuTX+41FpYuXWqqVavm1ln1Rvkz5eTkWM99enq6SU9PN7/++qs1Picnx3Tv3t2rO/ybN2821113nalbt665/PLLzSOPPGJSU1PdxuRmzMzMNDfffLMpU6aMKVeu3Hl/tp5SlDz5T5N9+OGHze233+61p0EXlunAgQPGmLM/k8Je/z169DBJSUlee0pwUX5OuceH119/3Vx55ZVup3Z7m8Ly/P7778YYYw4ePGj27dtntm/fbo3PysoyN998sxk/frwxxjtP3f7hhx/MlVdeaWrXrm2qV69u4uLizMqVK93G+NKxwZiiZcr/Xuvtxwdv5LT5TH5OnN/k5cS5Tl5Om/fk58R5UC4nzofyctrcKD8nzpXycuK8KS9vmkPRlCohu3btMjExMSY8PNzcdtttbl+jWNgL8PDhw2bEiBGmatWqZuvWrXaWWiTny1OY/fv3m5iYGPOPf/zDa09BPVemnJycQmv+6aefzIgRI0zFihXNjz/+aHe5RfLjjz+aqlWrmmHDhpn//ve/Zvz48eaaa66xrhmR93cv901w0KBBplKlSmbLli0eqfl8LiaPMWcv0Ddy5EhTtWpVr8xjzLkzzZs3r9DxuZmioqLc3ti9ycX+nHbs2GGCgoLMxIkTPVHuBV3sz+i3334zI0eONFWqVHG7aLA3OXDggKlRo4Z57LHHzI4dO8zHH39sevToYcqUKWPee+89t7G+cGww5uIyGeMbxwdv5LT5TH5OnN/k5cS5Tl5Om/fk58R5UC4nzofyctrcKD8nzpXycuK8KS9vm0PRlCoBJ06cMH379jW33367mTJlimnfvr3p0qXLOSc6ixcvNn369DHVq1e/4GTIEy42T653333Xa1+UF5vp4MGDZty4caZGjRpm48aN9hZbRGlpaaZr167m/vvvd1uemJhobrvttkIfM2XKFONyubzy9+5i8yxYsMD06dPH1KhRwyvzGHPxmT7++GPTs2dPU61aNcdkyvXvf//bK48PF5vnl19+MU888YSJjo722p+RMWcvzNmoUSOze/dua9mJEyfMP/7xDxMYGGg+/fRTY8z//iLmzceGXBeT6ZNPPvH644M3ctp8Jj8nzm/ycuJcJy+nzXvyc+I8KJcT50N5OW1ulJ9T50p5OXHelJe3zaF85wOrPiQkJEQdO3ZUhw4d9OCDD+rBBx/UiRMnNHr0aG3cuLHA+KZNm+qGG27QihUrdNVVV3mg4vO72DzZ2dmSpF69eqlhw4Z2l1skF5upYsWK6tu3r1atWqVmzZrZX3ARHD16VFWqVNEtt9wi6ez1IyTp1ltv1ZkzZyTJ+oxwrrvuuks7d+70yt+7i81z9dVX66qrrtJXX33llXmki88UFxenRo0aee2xQbr4TLnXl/n73//ulceHi80TFRWl7t27a/Xq1V77M5KktLQ0bd261ao9JydHISEhev755zVgwADdfffd+vnnn63rWHjzsSHXxWRq3ry51x8fvJHT5jP5OXF+k5cT5zp5OW3ek58T50G5nDgfystpc6P8nDpXysuJ86a8vG4OVSKtLhQwd+5c6y9UuX99OnXqlPW5W184BTyvc+XJ/Ry4L3JCpqVLl1r/zj0teMaMGebGG290W+YrX2dd1DxHjx41xvjG6+hiM+WeEuzNipopPT3d/uIuQVHzHDt2zP7iLtGZM2dM69atzV133WUOHz5sjPnf6+W3334zrVu3NmPHjj3nR3q8UVEyjRkzxnoNefu1K3yF0+Yz+TlhLnA+TsvntHlPfk6cB+Vy4nwoL6fNjfJz4lwpLyfOm/LytjkUZ0qVsNy/qt1+++26//77deLECY0aNUpr167V0KFD1bx5c2VmZhb6bS/e6EJ54uLilJmZWeAvU97MCZlya0tISLDu5/5OHT9+XEeOHLGWjR8/Xvfdd5/1Vw1vdLF5BgwYoKysLK9+HV1qJm/+BpaLzdS/f3/rL2je6FJ/Rt58bMjl7++vu+66S3v27NFLL72k9PR063frsssuU/ny5bV9+3a5XC6v/p3LqyiZduzYIX9/f0ny6uODL3DafCY/J8wFzsdp+Zw278nPifOgXE6cD+XltLlRfk6eK+XlxHlTXt42hwoo0a2XYrkvRn9/f+treO+44w65XC5Nnz5dnTp1UnZ2tpYsWeITX9fqtDySszLlHihyM7lcLp05c0YBAQEKCwtThQoV5HK59NRTT+m5557T6tWrra/29kZOyyORKTdTQID3vu048Wck/S/PoEGDtGvXLn3yySc6efKknnjiCYWGhkqSKleurIoVKyo7O1t+fn5e/z82TszkrZz0XlkY8vlmPqcer3M5OZ+Ts0nOmxvl5/Sfn+T8OYZX5iu+k66QK/c0t7ynK+Y95a1du3YmPDzcbN682fbaLoXT8hhTejIZY8wHH3xgbr31VjNy5EgTFBRk1q1b54nyLprT8hhDJl/gtDzG/C9T7mnZ48aNMy1btjR169Y1jz76qOnRo4cpX768T1xcNZcTM3kjJ75X5kU+5+UzxreP13k5OZ+TsxlDPqfkc+ocwxvz0ZQqZqdPnzbGGLNnzx7TpEkTs3DhQmtdVlaWefTRR02ZMmVMcnKyp0q8KE7LY0zpyzR9+nTjcrlMuXLlzPr16z1V4kVxWh5jyOQLnJbHGPdMDRo0MF9//bUxxphvvvnGPPTQQ6Zjx46mT58+PvU/pU7M5I2c+F6ZF/mcm89Xj9d5OTmfk7MZQz4n5XPiHMNb8/nOuYJeZs+ePVq6dKlOnjypK664Qp06dZIklSlTRr/88otat26tW265RZ07d7YeExAQoLi4OK1du1ZNmzb1VOmFcloeiUy5atasqbi4OL399tuqX7++p0ovlNPySGTK5c2ZnJZHknbt2qV58+YpPT1dTZs2VefOnVWuXLkCmVq1aiVJatOmjdq0aSNz9o9TXnk9BCdm8kZOfK/Mi3ylL5+3H6/zcnI+J2eTyOfr+Zw+x/C5fLa2wBxi06ZNJiIiwtx4442mbdu2xs/Pz9xzzz1m1apVxhhjBgwYYPr37+92irQ3f+uP0/IYQ6b8Dh48aHe5F+S0PMaQKT9vzOS0PMYYs3nzZhMeHm5at25tWrVqZfz9/c0dd9xhFi9ebIwxZuDAgT53vHNiJm/kxPfKvMhX+vLl8tbjdV5OzufkbMaQz9fzOX2O4Yv5aEpdpEOHDpmmTZuaJ554wlr22WefGT8/P3PLLbeY9evX+9TXQjotjzFkystbczotjzFkystbMzktjzHGnDhxwtx8880mKSnJWrZ69WoTFxdnbrrpJvP11197fKJxsZyYyRs58b0yL/KVzny+ktnJ+ZyczRjy+Xo+p88xfDWfd5935oWOHTumgIAA3X333TLG6PTp02rWrJnq16+vdevWady4cUpLS/N0mUXmtDwSmfLy1lNLnZZHIlNe3prJaXkkKSQkREePHlVERIQkKScnRy1atNCsWbOUmZmpZ599Vps3b/ZwlRfHiZm8kRPfK/MiX+nM583H67ycnM/J2STy+Xo+p88xfDWfb/z2eJE///xTGzZsUEpKilwulwIDA3XixAnFxMRo4sSJWrBggebNm+fpMovMaXkkMvkCp+WRyOQLnJbHGKPjx48rMDBQBw8elHR28pGdna2GDRvqlVde0ebNmzVr1iwPV1p0TszkrZz2esiPfOTzZk7O5+RsEvl8OZ/T5xg+nc/eE7N8X1ZWlrnnnntMnTp1zCuvvGLee+89U7FiRfPggw8aY4wZMmSI6dGjh8nKyvLKU+Pyc1oeY8jkC5mclscYMvlCJqflyfX+++8bl8tlPvnkE2PM2VPoc79d5Z133jEVK1Y0e/fu9WSJF82JmbyNU18PuchHPm/m5HxOzmYM+Xw9nzHOn2P4Yj6aUhfw+++/mzVr1pjPP//cnDlzxhhjzJYtW8ygQYNM5cqVTcOGDd0+c9uvXz+TmJjoqXIvyGl5jCGTMd6fyWl5jCGTMd6fyWl5jPnfV/kac/ailDk5OebMmTMmKSnJBAcHm88//9xt/KeffmoaNGhgDh8+bHepRebETN7Iia+HvMhHPm/m5HxOzmYM+Xw9n9PnGE7JR1PqPH744QcTExNjGjRoYAICAkyzZs3M9OnTzYkTJ4wxxvz2229m//791vicnBzTu3dv8/jjj1u/FN7EaXmMIZMx3p/JaXmMIZMx3p/JaXmMOTtJvPXWW83WrVsLrNu9e7fp37+/CQwMNG+88YZJSUkxp06dMo8//rhp2rSpOXLkiAcqvjAnZvJGTnw95EU+8nkzJ+dzcjZjyOfr+Zw+x3BSPppS5/DHH3+Y+vXrm8cff9zs3r3bHDx40PTs2dNcc801ZsiQIebYsWNu43ft2mVGjhxpwsPDzbZt2zxU9bk5LY8xZDLG+zM5LY8xZDLG+zM5LY8xZycXl19+uXG5XKZZs2Zmx44dBcYcOHDAjBs3zpQpU8bUrl3bNG3a1FSpUsVs2LDBAxVfmBMzeSMnvh7yIh/5vJmT8zk5mzHk8/V8Tp9jOC0fTalz2Lx5s6lVq5b54YcfrGWZmZlm1KhRpkWLFuaJJ54wJ0+eNMacfVE/8MADpm7dul75QzbGeXmMIZMvZHJaHmPI5AuZnJbn1KlTZsyYMea2224za9euNS1atDD169cvdAJijDEbNmww7733npkzZ47ZvXu3vcUWkRMzeSunvR7yIx/5vJmT8zk5mzHk8+V8Tp9jODEfTalz2LFjh4mNjTULFy40xpy96Fvufx999FHTrFkzs2LFCmv8rl27zG+//eaRWovCaXmMIZMx3p/JaXmMIZMx3p/JaXmys7PNRx99ZObOnWuMMebo0aPnnIB4+6n0uZyYyVs57fWQH/nI582cnM/J2Ywhny/nc/ocw4n5aEqdw6lTp0zz5s3NLbfcYl30LffFmpOTYxo3bmx69+7tyRIvitPyGEMmX+C0PMaQyRc4LY8xxsqR69ChQ9YE5KeffjLGnM343XffmVOnTnmixIvmxEzeyImvh7zIRz5v5uR8Ts5mDPl8PZ/T5xhOy+cnFJCTk6OgoCDNmDFDK1as0KBBgyRJAQEBMsbI5XLp1ltv1cGDBz1cadE4LY9EJl/gtDwSmXyB0/Lk8vf3lyQZYyRJlStX1qJFi1ShQgV17dpVW7du1UMPPaShQ4fq+PHjniy1yJyYyds49fWQi3zk82ZOzufkbBL5fD2f5Pw5htPy0ZQqhJ+fn7Kzs9WoUSPNmjVL7733nnr37q3U1FRrzO7du1WxYkVlZ2d7sNKicVoeiUy+kMlpeSQy+UImp+XJlTvpcLlc1v0qVaros88+U3h4uJo0aaJZs2ZpypQpqly5sidLLTInZvI2Tn095CIf+byZk/M5OZtEPsm380nOn2M4LZ/L5CYqxXJycuTn97/+3JkzZxQQEKDjx48rMzNTycnJuvvuu1WzZk1VqlRJlStX1ieffKKVK1eqcePGHqy8cE7LI5HJFzI5LY9EJl/I5LQ8UsFM2dnZ8vf3V3p6unJychQeHu42/t5779WCBQu0YsUKNWjQwOZqi8aJmbyRE18PeZGPfN7MyfmcnE0in9PyOW2O4fR8Uik/U+rQoUOS/tctls7+kAMCArRnzx5deeWVWrt2rdq3b6+tW7fq5ptv1mWXXaaIiAitWbPG616kTssjkckXMjktj0QmX8jktDzSuTP5+/trz549ql+/vlauXGmNN8bo5Zdf1syZM7V06VKvnHg4MZM3cuLrIS/ykc+bOTmfk7NJ5HNqPqfMMZyez01JX7TKW+3YscNUqFDBDBgwwFqWe8GwvXv3mipVqpj+/fubnJwca3nu1euzs7PtL/gCnJbHGDL5Qian5TGGTL6QyWl5jClapvvuu8/tW1RycnLM119/bX7++Wfb6y0KJ2byRk58PeRFPvJ5Myfnc3I2Y8hXGvL58hzD6fnyK7VnSm3btk0hISHavHmz7r//fklnLxh2+vRpLViwQPfcc49ee+01uVwu60JiuXI/u+lNnJZHIpMvZHJaHolMvpDJaXmkomWaNm2aW/0ul0tt27ZVnTp1PFX2eTkxkzdy4ushL/KRz5s5OZ+Ts0nkKw35fHmO4fR8+ZXaplRQUJDCw8PVrVs3rVy5Ug888IAkKTAwUF27dtULL7xwzheoN75QnZZHIlMub87ktDwSmXJ5cyan5ZEuLZO3c2Imb+TE10Ne5COfN3NyPidnk8hXGvP5Eqfnyy/A0wV4SuPGjRUXF6f77rtPgYGBmjlzpoYNG6a0tDS1aNFC9957r8qUKePpMovMaXkkMvkCp+WRyOQLnJZHIhMundOfZ/KRz5s5OZ+Ts0nkI593c3q+Ajz9+UFPycjIME2aNDEbN240GRkZZvr06aZy5crG5XKZTZs2GWP+97lNX+C0PMaQyRc4LY8xZPIFTstjDJlw6Zz+PJOPfN7MyfmcnM0Y8hlDPm/m9Hz5lcqP72VlZSkoKEhRUVE6fvy4ypYtq2XLlikrK0t16tTRG2+8IUk+c0qc0/JIZPIFTssjkckXOC2PRCZcOqc/z+Qjnzdzcj4nZ5PIRz7v5vR8hXH8x/f279+vDRs26PTp06pVq5auvvpq61S3uLg47dy5U9OnT9eKFSu0cOFCbd68Wc8++6wCAgI0ceJED1dfkNPySGTyhUxOyyORyRcyOS2PRCZfyeSNnP48k4983szJ+ZycTSIf+byb0/MVmadP1SpJmzZtMpdffrlp0aKFqVKlimnevLmZO3eutX7MmDHG5XKZ2NhYs379emOMMUePHjWvvvqq2bVrl6fKPien5TGGTL6QyWl5jCGTL2RyWh5jyOQrmbyR059n8pHPmzk5n5OzGUM+8nk3p+e7GI5tSu3cudNUr17dPPbYY+bYsWNm3bp1pk+fPubee+81WVlZxhhjsrKyzIMPPmjWrFljjDEmJyfHGGNMdna2x+o+F6flMYZMvpDJaXmMIZMvZHJaHmPI5CuZvJHTn2fykc+bOTmfk7MZQz5jyOfNnJ7vYjmyKZWZmWmGDRtm7rzzTpOZmWktf/PNN03lypXNoUOHPFjdxXNaHmPI5AuclscYMvkCp+Uxhky4dE5/nslHPm/m5HxOzmYM+cjn3Zye71I48ppSOTk5ql69uurXr6/AwEAZY+RyuXTdddepfPnyysrKKvQxfn7eed13p+WRyJT3Md6ayWl5JDLlfYy3ZnJaHolMeR/jzZm8kdOfZ/KRz5s5OZ+Ts0nkI593c3q+S+HIplRwcLC6deum2NhYt+Xh4eEqU6aM2w9648aNuuqqq7z6h+y0PBKZfCGT0/JIZPKFTE7LI5HJVzJ5I6c/z+Qjnzdzcj4nZ5PIRz7v5vR8l8Ix6Q4cOKA1a9Zo8eLFysnJsX7I2dnZcrlckqS0tDQdPXrUesyoUaPUvn17HT58WMYYj9R9Lk7LI5EplzdncloeiUy5vDmT0/JIZMrl7Zm8kdOfZ/KRz5s5OZ+Ts0nkk8jnzZye7y8ruU8G2ueHH34wNWvWNFdeeaUJCwsz9erVM3PmzDGHDx82xvzvomA7duwwVatWNUeOHDHjx483ISEhZt26dZ4svVBOy2MMmXwhk9PyGEMmX8jktDzGkMlXMnkjpz/P5COfN3NyPidnM4Z85PNuTs9XHHy+KXXw4EFTr149M3LkSLNr1y7z+++/m7vuusvUr1/fjB492hw8eNAam5qaaq666ipz1113mcDAQK/8ITstjzFk8oVMTstjDJl8IZPT8hhDJl/J5I2c/jyTj3zezMn5nJzNGPKRz7s5PV9x8fmm1NatW02tWrUK/NAef/xx07hxY/P888+bjIwMY4wx27ZtMy6Xy4SEhJiNGzd6oNoLc1oeY8jkC5mclscYMvlCJqflMYZMvpLJGzn9eSYf+byZk/M5OZsx5COfd3N6vuLi89eUysrK0pkzZ3TixAlJ0smTJyVJzz77rG688UZNnTpVO3fulCRVrFhRDz74oDZs2KBmzZp5quTzcloeiUy+kMlpeSQy+UImp+WRyOQrmbyR059n8pHPmzk5n5OzSeQjn3dzer7i4jLG96+a1aJFC5UvX15fffWVJCkzM1NBQUGSpGuuuUZ16tTRe++9J0k6deqUgoODPVZrUTgtj0QmX8jktDwSmXwhk9PySGTylUzeyOnPM/nI582cnM/J2STykc+7OT1fcfC5M6UyMjL0559/Kj093Vr22muvaevWrbr77rslSUFBQTpz5owkqXXr1srIyLDGetsP2Wl5JDJJ3p/JaXkkMknen8lpeSQySb6RyRs5/XkmH/m8mZPzOTmbRD6JfN7M6flKik81pbZt26a//e1vatOmjerXr6/Zs2dLkurXr6/Jkydr6dKluuOOO5SVlSU/v7PRDh48qHLlyunMmTNe91WKTssjkckXMjktj0QmX8jktDwSmXwlkzdy+vNMPvJ5Myfnc3I2iXzkI59j2XkBq79i69atpnLlymbo0KFm9uzZZtiwYaZMmTJmw4YNxhhjMjIyzIIFC0z16tVNvXr1TLdu3cydd95pypUrZzZv3uzh6gtyWh5jyOQLmZyWxxgy+UImp+Uxhky+kskbOf15Jh/5vJmT8zk5mzHkI593c3q+kuYT15Q6cuSIevbsqXr16mny5MnW8htvvFGNGzfWSy+9ZC37888/9fTTT+vIkSMKDg7WoEGD1KBBA0+UfU5OyyORyRcyOS2PRCZfyOS0PBKZfCWTN3L680w+8nkzJ+dzcjaJfOQjn9MFeLqAosjKytKxY8d0++23S5JycnLk5+en2NhYHTlyRJJkjJExRhUqVNBzzz3nNs7bOC2PRCZfyOS0PBKZfCGT0/JIZPKVTN7I6c8z+cjnzZycz8nZJPJJ5PNmTs9nB594FiIjI/Xuu+/qhhtukCRlZ2dLki677DLrB+lyueTn5+d2UTGXy2V/sUXgtDwSmXwhk9PySGTyhUxOyyORyVcyeSOnP8/kI583c3I+J2eTyCeRz5s5PZ8dfKIpJUlXXHGFpLMdxTJlykg623E8ePCgNWbChAl64403rKvZe/MP2ml5JDL5Qian5ZHI5AuZnJZHIpOvZPJGTn+eyUc+b+bkfE7OJpFPIp83c3q+kuYTH9/Ly8/PT8YY64eY230cNWqUnn76aW3cuFEBAb4Ty2l5JDL5AqflkcjkC5yWRyITLp3Tn2fykc+bOTmfk7NJ5COfd3N6vpLiM2dK5ZV7bfaAgADFxMTo3//+t55//nmtW7dOTZs29XB1F89peSQy+QKn5ZHI5AuclkciEy6d059n8vk28vkuJ2eTyOfryIf8fLJNl9txLFOmjF5//XWFhobq22+/1dVXX+3hyi6N0/JIZPIFTssjkckXOC2PRCZcOqc/z+TzbeTzXU7OJpHP15EPBRgftnbtWuNyuczWrVs9XUqxcFoeY8jkC5yWxxgy+QKn5TGGTLh0Tn+eyefbyOe7nJzNGPL5OvIhl8uY/39+mY/KyMhQuXLlPF1GsXFaHolMvsBpeSQy+QKn5ZHIhEvn9OeZfL6NfL7Lydkk8vk68kGSfL4pBQAAAAAAAN/jkxc6BwAAAAAAgG+jKQUAAAAAAADb0ZQCAAAAAACA7WhKAQAAAAAAwHY0pQAAAAAAAGA7mlIAAAAAAACwHU0pAAAAAAAA2I6mFAAAAAAAAGxHUwoAAAAAAAC2oykFAAAAAAAA2/0/otg/QrH+pmsAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["date_created = [b.time_created.astimezone(pdt_tz).date() for b in blobs]\n", "date_counts = Counter(date_created)\n", "dates = sorted(date_counts.keys())\n", "counts = [date_counts[date] for date in dates]\n", "\n", "date_created_checkpoint = [\n", "    b.time_created.astimezone(pdt_tz).date() for b in checkpoints\n", "]\n", "date_counts_checkpoint = Counter(date_created_checkpoint)\n", "dates_checkpoint = sorted(date_counts_checkpoint.keys())\n", "counts_checkpoint = [date_counts_checkpoint[date] for date in dates_checkpoint]\n", "\n", "fig, ax = plt.subplots(1, 2, figsize=(12, 6))\n", "\n", "ax[0].bar(dates, counts)\n", "ax[0].set_title(\"blob uploads by date\")\n", "plt.setp(ax[0].xaxis.get_majorticklabels(), rotation=45, ha=\"right\")\n", "\n", "ax[1].bar(dates_checkpoint, counts_checkpoint)\n", "ax[1].set_title(\"checkpoint uploads by date\")\n", "plt.setp(ax[1].xaxis.get_majorticklabels(), rotation=45, ha=\"right\")\n", "\n", "fig.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```sql\n", "SELECT \n", "  request_id, time\n", "FROM `system-services-prod.us_prod_request_insight_analytics_dataset.completion_host_request`\n", "-- FROM `system-services-prod.us_prod_request_insight_analytics_dataset.chat_host_request`\n", "WHERE\n", "  time >= \"2024-09-16\"\n", "  and tenant = \"i0-vanguard0\"\n", "```"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(534554,\n", " Counter({'2024-09-26': 51767,\n", "          '2024-09-25': 47702,\n", "          '2024-09-18': 46113,\n", "          '2024-09-24': 42279,\n", "          '2024-09-17': 39010,\n", "          '2024-09-20': 38514,\n", "          '2024-09-27': 36047,\n", "          '2024-09-30': 35106,\n", "          '2024-10-01': 32930,\n", "          '2024-09-23': 31128,\n", "          '2024-09-19': 28896,\n", "          '2024-09-16': 27012,\n", "          '2024-10-02': 18567,\n", "          '2024-09-21': 16005,\n", "          '2024-09-22': 15568,\n", "          '2024-09-29': 14279,\n", "          '2024-09-28': 13631}))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# get a list of recent completion requests\n", "with open(\"./bq-results-20241002-182935-1727893817514.csv\", \"r\") as f:\n", "    completions = [line.strip().split(\",\") for line in f.readlines()[1:]]\n", "\n", "len(completions), Counter([c[1].split(\" \")[0] for c in completions])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(518960, 'f8295cdbdc48d6679f3ed49576769b714fe93546d6e1951ca2575e38ce892334')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# set of all the blob names in the bucket\n", "blob_names = set([b.name.split(\"/\")[-1] for b in blobs])\n", "len(blob_names), list(blob_names)[0]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[['8155989e-be43-477a-af89-882c1fb88f2c', '2024-09-19 00:05:51.32888 UTC'],\n", " ['a3f8f488-ff64-45e7-bfdd-857f756c9ae5', '2024-09-20 22:02:37.984577 UTC'],\n", " ['867054cc-2da1-449f-bab6-562bd3c94208', '2024-10-01 18:47:26.430411 UTC'],\n", " ['081d20ab-926e-48d4-ad87-2c869d68d044', '2024-09-19 16:45:01.712163 UTC'],\n", " ['b1a22a49-1e00-4a37-8fc2-89b3156d32d9', '2024-09-18 00:14:03.108316 UTC']]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["N = 1000\n", "completions_subset = [completions[i] for i in random.sample(range(len(completions)), N)]\n", "completions_subset[:5]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['867054cc-2da1-449f-bab6-562bd3c94208', '2024-10-01 18:47:26.430411 UTC'] 789b1a18a6970fc4de4cf3aa89a35827/request/867054cc-2da1-449f-bab6-562bd3c94208/completion_host_request/cc170a8c-71ab-4d49-b675-a98ebbd9f269\n", "['64ad0189-3c52-4241-bfbe-63ff7d46b6fc', '2024-10-02 17:14:39.756799 UTC'] 789b1a18a6970fc4de4cf3aa89a35827/request/64ad0189-3c52-4241-bfbe-63ff7d46b6fc/completion_host_request/26299c9a-3fbe-43b8-8399-c5e21768388c\n", "['655bedbf-ada9-4f3a-905a-e6fe9c21a166', '2024-10-02 16:02:54.142542 UTC'] 789b1a18a6970fc4de4cf3aa89a35827/request/655bedbf-ada9-4f3a-905a-e6fe9c21a166/completion_host_request/4aedd094-c51d-4e24-ae17-8b293c1e3a97\n", "['e0410b0b-cada-42c7-8e1e-56c823d81fb8', '2024-10-01 13:06:52.550267 UTC'] 789b1a18a6970fc4de4cf3aa89a35827/request/e0410b0b-cada-42c7-8e1e-56c823d81fb8/completion_host_request/70a2b76a-828c-42a3-bb01-2ee2f8e5fc38\n", "['fa897084-7aab-4aa6-8a74-aec7d675631e', '2024-10-01 21:18:10.750194 UTC'] 789b1a18a6970fc4de4cf3aa89a35827/request/fa897084-7aab-4aa6-8a74-aec7d675631e/completion_host_request/b9565bbb-2786-4674-8868-5dd44565907c\n"]}], "source": ["BUCKET_EVENTS = \"us-prod-request-insight-events-confidential\"\n", "TENANT_ID = \"789b1a18a6970fc4de4cf3aa89a35827\"\n", "\n", "bucket_events = client.bucket(BUCKET_EVENTS)\n", "\n", "for c in completions_subset[:50]:\n", "    request_id = c[0]\n", "\n", "    prefix = f\"{TENANT_ID}/request/{request_id}\"\n", "\n", "    completion_host_request = None\n", "    for b in bucket_events.list_blobs(prefix=prefix):\n", "        if \"completion_host_request\" in b.name:\n", "            completion_host_request = b\n", "            break\n", "\n", "    if completion_host_request:\n", "        print(c, completion_host_request.name)\n", "\n", "# for some reason only very recent requests are in this bucket???"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["request_id = \"8155989e-be43-477a-af89-882c1fb88f2c\"\n", "or request_id = \"a3f8f488-ff64-45e7-bfdd-857f756c9ae5\"\n", "or request_id = \"867054cc-2da1-449f-bab6-562bd3c94208\"\n", "or request_id = \"081d20ab-926e-48d4-ad87-2c869d68d044\"\n", "or request_id = \"b1a22a49-1e00-4a37-8fc2-89b3156d32d9\"\n", "or request_id = \"b5a6b60b-c93b-4843-99f3-56b9a5797f2b\"\n", "or request_id = \"fc8b0c3a-8c61-4e88-931d-764743553476\"\n", "or request_id = \"886736c5-0bb5-452c-b40d-dcbbdc733e59\"\n", "or request_id = \"ac8dc0ca-9167-4e7e-be0d-99f2578c7475\"\n", "or request_id = \"64ad0189-3c52-4241-bfbe-63ff7d46b6fc\"\n", "or request_id = \"d7d701b3-be1c-4559-90f0-10528a5bfb69\"\n", "or request_id = \"655bedbf-ada9-4f3a-905a-e6fe9c21a166\"\n", "or request_id = \"30e70db8-da99-445d-a89d-ae9d1534fae3\"\n", "or request_id = \"d4ff4826-d431-4114-802d-48f9f0ae1d3b\"\n", "or request_id = \"c472ff8e-bd77-40d1-99bc-4ad6fbbecf68\"\n", "or request_id = \"f4e0dc69-6641-4483-b71d-5089f866ae8d\"\n", "or request_id = \"3caf7bba-353a-4acb-aedd-15b825895e1a\"\n", "or request_id = \"e4e29cb3-37ae-4bb1-910d-b9a51258068b\"\n", "or request_id = \"e0410b0b-cada-42c7-8e1e-56c823d81fb8\"\n", "or request_id = \"7ac0c91b-a569-43f5-bc50-db25fdcf4e72\"\n", "or request_id = \"0bb6ba74-12a5-4a66-b8e8-de2ad522fbf1\"\n", "or request_id = \"65801059-82e8-4558-9480-60bc7d009bf7\"\n", "or request_id = \"4d1fdadb-594d-4d1e-8eed-61af174f272b\"\n", "or request_id = \"5fb34762-7eb4-44f6-b998-51d9611ce773\"\n", "or request_id = \"09ceafea-9467-496e-a1a7-2f4a94f5f098\"\n", "or request_id = \"375538d8-4a91-46fd-a84d-3ce7dd287449\"\n", "or request_id = \"2b17f7bf-a221-4c4d-9814-4942c3c5f9f1\"\n", "or request_id = \"d8a095da-9189-4ca2-a770-0831a7e5be46\"\n", "or request_id = \"4209cf27-b939-4934-8ce4-68fd1380ddce\"\n", "or request_id = \"e43ce50a-2cde-4751-bb49-6445ad63fc38\"\n", "or request_id = \"69763470-ed0b-4843-9fce-5f84fe490bb5\"\n", "or request_id = \"cb9e9775-2256-406f-ad54-b9bf635aff19\"\n", "or request_id = \"fa897084-7aab-4aa6-8a74-aec7d675631e\"\n", "or request_id = \"94e0b13d-b683-40fb-9de2-07b24120f2cc\"\n", "or request_id = \"9d027324-1681-465c-9bc5-8ff80ee4b87c\"\n", "or request_id = \"5a7dde34-d553-4341-a783-e2cac0832fd3\"\n", "or request_id = \"a21841c5-2278-4acb-9789-74a4e1b4bc88\"\n", "or request_id = \"e970cf3c-8152-4eaf-a509-171a84872a4f\"\n", "or request_id = \"dd06961e-362f-4155-9742-438c8f00fd10\"\n", "or request_id = \"66e4f3c4-62fa-48e9-b352-b1a361695e4c\"\n", "or request_id = \"c29862da-48cd-442e-9a4b-d315252a8aa2\"\n", "or request_id = \"46136d97-38b7-4224-9125-d0a628c8e69e\"\n", "or request_id = \"92dae4f6-2ae6-4fbf-aba0-2bdd097de953\"\n", "or request_id = \"5da74fd3-634f-49ae-ad1d-c311ac9af0b5\"\n", "or request_id = \"af655e04-c8e1-4435-9c15-684fa34854da\"\n", "or request_id = \"dba7438b-beb4-4ab7-ad3f-908b7a59b95d\"\n", "or request_id = \"8098fd2a-b755-4711-81bd-e082bd0d0eea\"\n", "or request_id = \"3ee72036-aadc-4581-8021-f2d17a48d661\"\n", "or request_id = \"a77dfb51-058b-47bf-b738-38f4ccdcf68f\"\n", "or request_id = \"f5ab5f79-754c-4beb-98dd-7c913464fedc\"\n", "or request_id = \"29a27166-48e0-498c-82c8-b945ce3ccfda\"\n", "or request_id = \"f165a95e-e2c9-4e78-9eb7-3c9a834c304a\"\n", "or request_id = \"75b67fb4-cc24-4ec2-b4b5-ea81569993a2\"\n", "or request_id = \"2dcf0878-ee7c-4f87-863a-1a4adb23183b\"\n", "or request_id = \"65df6ac4-b61a-4bd4-b9a1-740ca4e7db29\"\n", "or request_id = \"1f055ca0-575c-47d1-9354-45a6db10b376\"\n", "or request_id = \"83dbed2f-b4d5-4536-b547-d93502de43ca\"\n", "or request_id = \"2548fee0-058f-40ab-9c05-77552b16f204\"\n", "or request_id = \"857c61e0-61de-4ac8-ba9d-d77457cc5218\"\n", "or request_id = \"87d1a974-6b45-460c-9920-fe3a58839da3\"\n", "or request_id = \"bb5ce83a-4802-4706-bb6c-e083340b84c6\"\n", "or request_id = \"4d29d74d-dc03-4bef-8c41-8683bf3d5bcb\"\n", "or request_id = \"f3e56eab-e27e-46cf-9302-fc36875ab888\"\n", "or request_id = \"6909fb60-6ea9-4871-bf97-d50f79d85665\"\n", "or request_id = \"eb133a40-8a5c-48c7-9ff9-19dd15691bcf\"\n", "or request_id = \"eafbca0c-297d-4d9f-8451-8f82b793bb32\"\n", "or request_id = \"112d51b1-2918-4cce-8d39-e2cae7eba5f7\"\n", "or request_id = \"f9062ae9-cadb-4072-ba2a-db0d578ba22a\"\n", "or request_id = \"840f849d-019b-47d9-8236-2ad6afb8b894\"\n", "or request_id = \"4d10c379-ee7a-47c5-8f04-cde5c3674637\"\n", "or request_id = \"67e6e73a-d3bf-4225-91c9-aea34a0adee0\"\n", "or request_id = \"69e7fa1b-f497-482c-b897-31f50b362dc2\"\n", "or request_id = \"cc03df8c-3743-4354-9cb2-f3a73f9e078f\"\n", "or request_id = \"bcac773a-73d2-4cd1-95f2-463546bd95f3\"\n", "or request_id = \"10c3d5c9-3b17-4f4b-b96a-9d53159b51a9\"\n", "or request_id = \"beaf7e37-81fb-47a2-9159-34895057961c\"\n", "or request_id = \"11509333-8d1a-47f5-89fd-e592565c02e9\"\n", "or request_id = \"5ad28c8e-189a-414c-b0bf-5f9652b7e456\"\n", "or request_id = \"78e105ba-4103-43af-a5f0-a2c8730e04c7\"\n", "or request_id = \"ce95b831-6f7b-45ed-8fff-436f2b53a2e7\"\n", "or request_id = \"bf838d96-8185-4796-8960-de2e70a02aab\"\n", "or request_id = \"8e5dcfde-a9a3-464a-9c0d-935a68e5017c\"\n", "or request_id = \"a3917f69-50c7-4c0d-9c1a-597dd808e5cb\"\n", "or request_id = \"754fa4d7-3b86-4ea3-865b-a13fdd924e9b\"\n", "or request_id = \"56819c25-33e5-40e7-a18d-07cd24f8c9dc\"\n", "or request_id = \"35ba21f0-dd41-4473-9007-25015ee634d9\"\n", "or request_id = \"659643f9-9b59-4626-9b7d-2a356f89b603\"\n", "or request_id = \"5e62110d-ff32-4ff7-89ee-befd64f856ce\"\n", "or request_id = \"437f64ac-a00f-4145-85e4-9425f30f33a5\"\n", "or request_id = \"53e10a79-7c7a-4684-93b7-0457f67870e9\"\n", "or request_id = \"e8c622a7-6e38-420a-8e90-89edfafb2f2d\"\n", "or request_id = \"10a8f154-aa1d-4284-9b9a-561174c631bf\"\n", "or request_id = \"e1dad34c-27f0-4633-a040-8b7a298c7843\"\n", "or request_id = \"1e11bb4d-56bf-4744-b4fa-afd2a8c323a6\"\n", "or request_id = \"6fb35f7d-49e1-4d86-bd2a-5d31392f31a6\"\n", "or request_id = \"be6e2590-9a0d-40d1-958d-2bbb78adabf9\"\n", "or request_id = \"1374954b-55b2-4a0b-8975-4a4dc864cb43\"\n", "or request_id = \"954315f3-540a-4ddc-bfac-ae8939d786b4\"\n", "or request_id = \"75136649-9c8d-4bc5-b3a5-2f2449ec4b58\"\n", "or request_id = \"b1f68cea-21da-4f19-a297-0d22835b5ac4\"\n", "or request_id = \"72995568-4778-484c-a745-2b28659bef7c\"\n", "or request_id = \"5315c203-459b-4692-b877-cec78d50cecb\"\n", "or request_id = \"8aea5ee5-e1ce-45af-b4ea-0eb3ef91a7e0\"\n", "or request_id = \"bf1c77c3-e67f-451f-897e-c55ae716c495\"\n", "or request_id = \"9eb82811-6de6-4401-af90-e59f4345ccb1\"\n", "or request_id = \"79bf0f89-70f0-4534-a16c-7e701b7810b1\"\n", "or request_id = \"4d9df03d-f26e-4423-a707-b69d22b3a9a8\"\n", "or request_id = \"985da0aa-b218-4d49-b094-e01acef59e89\"\n", "or request_id = \"e393264d-3f17-4e7f-bcb9-0937f422ed5a\"\n", "or request_id = \"ef0b5309-a30d-4d74-847d-189da1fa4146\"\n", "or request_id = \"7dfe5dc7-9f1b-49c5-bd91-165ee9d95962\"\n", "or request_id = \"8112e95d-06a3-490e-9be1-597869faca0c\"\n", "or request_id = \"7e7f2d68-0739-4f96-b0ff-4f855441af1c\"\n", "or request_id = \"abeac2d7-6e59-45d0-bf95-ca51e7bcf7f5\"\n", "or request_id = \"c6cf656d-8e7e-41af-aafb-09ae358c827c\"\n", "or request_id = \"7c650957-3c74-4b15-bc47-f921577f6b86\"\n", "or request_id = \"f157aa7c-f0c5-4d44-bdd0-1884f85cb964\"\n", "or request_id = \"3cb02079-5ad4-44e5-8673-d45a5e3aea5d\"\n", "or request_id = \"96538a29-72bd-437e-8dcd-c6a788fdbe8b\"\n", "or request_id = \"80a33850-8c44-4c04-b794-2a3dd69837d1\"\n", "or request_id = \"5a3157d4-48bc-412e-9c61-b41008b46374\"\n", "or request_id = \"b1b66139-b90f-481e-a500-3eb0d6c79b53\"\n", "or request_id = \"2742e462-52c4-4c77-8638-a24b8ff24d2e\"\n", "or request_id = \"282fe86a-796f-4cba-b05c-a9e151e52db5\"\n", "or request_id = \"cdf9806e-203f-4345-ab6a-b0fc3117eab4\"\n", "or request_id = \"697b361e-5e01-40f6-b510-a9a2b149b570\"\n", "or request_id = \"25980c65-c3ef-4e53-a4f4-8e4340404946\"\n", "or request_id = \"0579ff5a-2b7c-4ec2-a537-60dbeec08d62\"\n", "or request_id = \"779c4cb5-e191-4401-8863-c3f11198477c\"\n", "or request_id = \"1732a073-6905-4935-9b71-652b6ad10ab6\"\n", "or request_id = \"7743744d-34ef-4abd-8eb9-c14d6562c41f\"\n", "or request_id = \"f93c7dfe-608a-439a-9971-5ce2159a7400\"\n", "or request_id = \"1f9f9629-cb57-4575-b3f0-1865b4930c5f\"\n", "or request_id = \"b420b359-3466-4e9f-ae7b-bca6728776e8\"\n", "or request_id = \"84fd77e3-b77e-46a4-81e6-8f55ef06175f\"\n", "or request_id = \"9b8005c0-fa53-4ea9-9817-b06b2d571804\"\n", "or request_id = \"fe98c272-4710-4bc4-b5f8-7ac6470c85e9\"\n", "or request_id = \"ce08930a-be7f-40e0-b3b4-2e995d3f0346\"\n", "or request_id = \"7ad1df47-5c43-4145-b7aa-65aa570bea80\"\n", "or request_id = \"df4216b8-73d7-43e2-bb36-29c587a1a7ba\"\n", "or request_id = \"3c74f2a4-04e2-467c-9d35-44b88ccd6307\"\n", "or request_id = \"caf9009d-37a5-4dce-a524-9483d7848d65\"\n", "or request_id = \"67bceddc-94d5-413b-ac18-9bf36c97b802\"\n", "or request_id = \"06808e7d-2a32-4554-8f12-3a0b88037ebc\"\n", "or request_id = \"86af6801-4a86-45af-91ba-78bb7a60b48b\"\n", "or request_id = \"5e9a133d-415e-4318-a2dd-49c100cdd1d8\"\n", "or request_id = \"2c7e5ce4-a879-4286-b53a-85b858b5ff8f\"\n", "or request_id = \"924a37d5-2883-4387-b604-0903bc808400\"\n", "or request_id = \"7de8ce32-b744-458b-86d2-a6c206c11f8a\"\n", "or request_id = \"82f302d1-1ca6-44ea-8416-be902660c615\"\n", "or request_id = \"38cb39f2-3d92-43f4-82b3-1eaf55bcd73f\"\n", "or request_id = \"35d0f6d7-317a-446c-8022-27fe288aaff9\"\n", "or request_id = \"630428e5-3b18-4476-b1f4-fec8977fb918\"\n", "or request_id = \"2c765e45-f816-4a56-b8cf-345cd1e95391\"\n", "or request_id = \"267289ec-656a-4357-bb05-2ab69fe38f56\"\n", "or request_id = \"405be63d-caa8-4bd7-ae35-9cc37e3826c9\"\n", "or request_id = \"f43a1309-8d46-4c41-aa08-c5278e77887d\"\n", "or request_id = \"df0c5196-0543-448c-8c06-46e7fc48d0f7\"\n", "or request_id = \"6d779cbb-7ccb-4e0c-9f23-52ead74d50ee\"\n", "or request_id = \"0165280a-3062-43ab-a068-e37d5bc46976\"\n", "or request_id = \"09f3d6f3-a02f-40a1-b43b-334b4ea86ff7\"\n", "or request_id = \"60a5ab80-09d3-40f7-986e-95b94f4402ef\"\n", "or request_id = \"15a258f5-3099-4497-a8cd-5ef367cd00c0\"\n", "or request_id = \"2a71e995-9602-434a-9a3e-6bc7e60c957a\"\n", "or request_id = \"4327f19a-c5d8-4ff9-9735-8b45c5eb2e69\"\n", "or request_id = \"a5df1f01-e2bf-4c09-83da-1ea77db67aa5\"\n", "or request_id = \"d7d215b7-5ebc-4ab0-910d-16fc471ae1ad\"\n", "or request_id = \"14d2a57f-800e-4246-92c7-6527582b6fee\"\n", "or request_id = \"6fe3cf7e-7fc3-4daf-92f7-719c5bcddafe\"\n", "or request_id = \"7bddbe0e-9672-478d-8a37-d3f18af3a2fb\"\n", "or request_id = \"b0ceeaac-6659-42e5-ac86-2bb273de9725\"\n", "or request_id = \"071c8eb0-e7fe-42b9-802d-10496413cfd0\"\n", "or request_id = \"2ae12cf0-dbde-43be-b8f5-544ccc5e6391\"\n", "or request_id = \"dc7ab605-ff5a-42ca-b0e1-82055fd69b61\"\n", "or request_id = \"20bb2f66-5eb9-4338-bdb4-3a7815f21e87\"\n", "or request_id = \"631a2ee6-afee-420d-b88d-354676c4dd25\"\n", "or request_id = \"11c42b8b-691e-4a7a-b78f-f6b7a670759d\"\n", "or request_id = \"0f69ef87-6c3e-445f-9623-dfd6d79d1536\"\n", "or request_id = \"3822b374-54c9-4cde-9ea6-aeb8dc98ebbe\"\n", "or request_id = \"3c8f4d77-4c05-481f-84ba-e1b75e3fefeb\"\n", "or request_id = \"87662379-a337-4ef8-81a3-9a9a1963b1ae\"\n", "or request_id = \"bc326187-fcf3-428f-b532-909974339c67\"\n", "or request_id = \"4a6c30ce-e99a-47f6-82cc-7282c7d86952\"\n", "or request_id = \"6c7ce9ab-204a-4b83-969f-21096e042357\"\n", "or request_id = \"9d0122bf-2a15-45f4-a41f-2c21d09114c6\"\n", "or request_id = \"cce44828-560f-4fd8-83d7-ced69f163bcf\"\n", "or request_id = \"06104bb2-4c7c-4a0c-b008-a6c28cdbc80b\"\n", "or request_id = \"5b677c77-b09e-4fa8-afb8-75b99f5eff22\"\n", "or request_id = \"7be13ac6-b7d4-408f-9414-13a1306017e2\"\n", "or request_id = \"a49bf9d7-b8d0-4949-9609-828b02aa8ce8\"\n", "or request_id = \"de8bad70-d1ea-4224-bb00-dfd18354c8cd\"\n", "or request_id = \"117c1930-6411-4ecd-81bc-26a2b55b845f\"\n", "or request_id = \"66ec1541-604f-4b9c-8ea2-ae60a8356c5c\"\n", "or request_id = \"b15dd096-ec93-4f65-8d4e-921e2504b78e\"\n", "or request_id = \"6d50cefe-9cc6-4d46-b854-3adfca475a60\"\n", "or request_id = \"69c0128d-432f-4a9b-b868-5db7cf00919e\"\n", "or request_id = \"70f59a91-7ceb-4546-b8eb-eb4f400d95a6\"\n", "or request_id = \"92a708c8-41c5-4237-9935-723c2b1b3405\"\n", "or request_id = \"9b5272fb-80a1-4c54-8996-b6330a3f565e\"\n", "or request_id = \"d368fc0e-3198-4a72-b174-d4388fd1e981\"\n", "or request_id = \"1427992a-1c95-421e-a2c6-13f1e94265f9\"\n", "or request_id = \"8b8542b3-36d4-47fe-aa74-225aba52781b\"\n", "or request_id = \"14a797f1-1bd1-4a2b-8458-0f41362ba586\"\n", "or request_id = \"8e667189-2b5b-411e-a2a0-5c59da790533\"\n", "or request_id = \"4ad671bc-c67c-48e2-a7a0-5ac7d73d0389\"\n", "or request_id = \"7447fee8-b054-486e-99ba-d1255142fd1e\"\n", "or request_id = \"cfd1a922-c076-4aa5-977a-f1c8556f526b\"\n", "or request_id = \"8535e5eb-905e-4273-a6ba-b395f7e78eea\"\n", "or request_id = \"5282c329-07af-4217-97f0-23e8d65f3aab\"\n", "or request_id = \"9179ed94-86b7-4051-b2ad-0b44b8eb9c12\"\n", "or request_id = \"328dbe79-84bf-458b-b883-82814f5c5bbb\"\n", "or request_id = \"4d4701e4-b7b1-4282-9f3e-9ecd58408e7f\"\n", "or request_id = \"c78392a7-c4ba-4205-b77f-2fa1d16fe779\"\n", "or request_id = \"3898ff73-8997-42be-b6b3-4683f725f57c\"\n", "or request_id = \"61d5a938-db33-43fc-9afc-0b20019de083\"\n", "or request_id = \"f5dcc3bb-9ea9-4275-bc3a-430a62f94bda\"\n", "or request_id = \"405e19ed-7f01-451c-b5da-ef047020ba4c\"\n", "or request_id = \"9a549432-60df-473a-a056-a4a617800104\"\n", "or request_id = \"8c9dda02-551d-430a-b902-26d1a4cae6e6\"\n", "or request_id = \"5e197ba3-ebda-402f-a901-290e890ab8fd\"\n", "or request_id = \"90602fbd-3988-4bfc-bb8f-9c90e649f550\"\n", "or request_id = \"4a4594dc-ca66-4dd8-aa21-c207f1da3561\"\n", "or request_id = \"1f660875-e363-48d4-8811-66ae53d2f0c1\"\n", "or request_id = \"e7ef74d8-a6f7-4f81-997d-ce124c1efff0\"\n", "or request_id = \"ec66d2b7-c410-4118-bd69-2cbfd485935d\"\n", "or request_id = \"dc94910c-12c6-4fcd-af79-ffb51ae20708\"\n", "or request_id = \"f896c423-655d-40c6-a971-e563051eb57e\"\n", "or request_id = \"a59bccd7-6e26-4d4d-9864-13a26668ae6c\"\n", "or request_id = \"a6bcefd6-0691-4fe7-bf75-4ccaa2365067\"\n", "or request_id = \"6b1ec463-6792-48ca-bf01-74cf96b78338\"\n", "or request_id = \"46a3baa3-b6e2-4f7e-9298-bbc170ce8e95\"\n", "or request_id = \"d69c4431-b9bf-4062-b586-b5e3ca5a4f46\"\n", "or request_id = \"24bb4488-911d-46d4-b8f5-27a36394dcf7\"\n", "or request_id = \"efe0f2f9-8a1a-41fb-a403-08f16b00f474\"\n", "or request_id = \"9f10f16f-8b16-4576-aaaf-cd8a17d21a68\"\n", "or request_id = \"a1c834b1-16c0-4dcf-bde6-5ab33e205462\"\n", "or request_id = \"986d05ff-800e-4e7a-9e16-eabfb1a3bed5\"\n", "or request_id = \"45078d0b-db69-4a14-b1be-190f6cb9899c\"\n", "or request_id = \"86e5bffa-1502-4345-97e2-81f1cf5022a3\"\n", "or request_id = \"abd7bf25-d793-462e-836b-76ec19de8c9d\"\n", "or request_id = \"745a6da6-a4a4-427f-89a5-ca5d2740242f\"\n", "or request_id = \"6ea0829f-ac19-4c47-ae5f-8fcbadfed76b\"\n", "or request_id = \"45610ba2-1e81-45d7-9ed8-1cb9066eb03c\"\n", "or request_id = \"a358ebad-e728-40c1-963a-e57411a9d3c2\"\n", "or request_id = \"8d3c4b1a-8ff5-4ad1-82db-2a2d7ce2f892\"\n", "or request_id = \"5569e25a-2e72-487f-8534-cc5f9d57c476\"\n", "or request_id = \"c73d2229-3e0a-498c-be4d-cdba3b463422\"\n", "or request_id = \"6a77b5fc-2194-41ba-b07f-6d4741690fed\"\n", "or request_id = \"b89490a9-23d6-4b7a-8a33-a81b439b9db3\"\n", "or request_id = \"68886466-adae-4f8f-a738-f12762332242\"\n", "or request_id = \"13189456-f1fd-48bf-99c5-4bceef23f672\"\n", "or request_id = \"9284548f-ea5a-498f-91ae-ef01d0d7cf91\"\n", "or request_id = \"44443e24-10a9-4018-9e90-e0bf2c7a8540\"\n", "or request_id = \"352b088a-5d83-4f53-84b8-f92ee6cbef99\"\n", "or request_id = \"3438e361-ea4e-411c-8974-19b12f61fb79\"\n", "or request_id = \"7a1d09cf-9f56-4e34-88be-9fd2dacdc433\"\n", "or request_id = \"3b97199a-0416-4144-9575-d6745d1e4e97\"\n", "or request_id = \"ea54ebda-a34c-4df0-9534-d3795e292ee6\"\n", "or request_id = \"1c15edf5-0049-427a-a2b3-0b35f84a0fc9\"\n", "or request_id = \"0fefb8a1-8dc3-4b9b-8be7-3bc683444efc\"\n", "or request_id = \"6574903f-4086-4299-9090-5356c7a6cc03\"\n", "or request_id = \"e4d42f29-62a0-4444-b809-adecdad3402a\"\n", "or request_id = \"1efa1ec9-ba6c-431d-b4ae-7734a820d39e\"\n", "or request_id = \"464dff68-ea05-4615-9e55-20afd3c96bc7\"\n", "or request_id = \"e9bbac46-f717-41ec-aaa6-0f55cbdf9b39\"\n", "or request_id = \"1d0320fd-afe5-494b-87b1-64918d6b921d\"\n", "or request_id = \"0d41e02a-ba3b-408c-aeeb-ee58060f5ebe\"\n", "or request_id = \"44b66b28-8351-4358-9dc7-5525c61d83a5\"\n", "or request_id = \"67d36879-ef03-43c9-822e-a56a7b454c1a\"\n", "or request_id = \"e83a5335-fadf-46e9-a502-8255b227e359\"\n", "or request_id = \"062480ac-b98d-4ac7-b682-cf299c11321a\"\n", "or request_id = \"3e6af1ed-29ce-434a-8063-d447c1ef430b\"\n", "or request_id = \"4ac83ec0-c7bf-46e3-8c30-778413b0a5b3\"\n", "or request_id = \"18936544-d7cc-46c1-9e8e-f085e717e936\"\n", "or request_id = \"93c2cf73-bfda-465f-b65f-03a425838d13\"\n", "or request_id = \"8ab09853-c3a5-45a1-ae6f-7e7c14b2ec70\"\n", "or request_id = \"5647b082-c2e5-4409-8627-372916b825ca\"\n", "or request_id = \"84260fd1-29c0-4d9a-93c8-24aed6963a8a\"\n", "or request_id = \"2e79758d-0969-4b94-ba76-55eedf33c7f8\"\n", "or request_id = \"39738a72-3fe4-4f2d-be4a-4be16d17f72c\"\n", "or request_id = \"3b70d4a3-256c-4aa7-87c9-4a1e2ceed4ba\"\n", "or request_id = \"480085db-d0a4-4c48-bc7f-aebffd9a7db3\"\n", "or request_id = \"23e69ed1-4f36-444c-bb05-c6a9877b3e74\"\n", "or request_id = \"e8d268cc-7b8f-4218-84a3-8b5ece0a5460\"\n", "or request_id = \"3401c83c-2bed-48e2-8072-889a00978785\"\n", "or request_id = \"61f895e0-0085-4ef4-ac83-6ede7b974f04\"\n", "or request_id = \"bf9f6ca2-729a-47c7-91d8-441d9c33d622\"\n", "or request_id = \"e533db55-00ba-4351-9dac-3363de7490bc\"\n", "or request_id = \"7a55a023-a280-4054-8182-ce0d9fdfb615\"\n", "or request_id = \"9ad82a90-60ae-4cfb-88e6-5224f1466856\"\n", "or request_id = \"f1ee0d93-8f05-415a-9d2c-ec34571a44fa\"\n", "or request_id = \"f95a09e3-7d95-437e-abe0-42e2a79903e4\"\n", "or request_id = \"b6f85b43-dadd-4f4c-814b-3f732aa879f9\"\n", "or request_id = \"f9177e4d-cc5b-4877-b71f-038a5a3c3248\"\n", "or request_id = \"ac8178ca-3ddf-4f72-afed-837e5a679861\"\n", "or request_id = \"ff65a732-6bc8-4d09-9433-c0089c9a721b\"\n", "or request_id = \"6b507591-be1b-418f-b780-5c72514f2949\"\n", "or request_id = \"33970029-6857-4f8a-9f70-d89cf6807964\"\n", "or request_id = \"be7fc4a4-fbe5-40d4-ae79-3ee65b9fff15\"\n", "or request_id = \"c03e3a0f-74a3-4f00-acc2-95db197d427c\"\n", "or request_id = \"f3f9064a-8edd-43c1-88b4-1feb0216ca67\"\n", "or request_id = \"261cd933-263f-4c23-9c29-90bd0c0b59f2\"\n", "or request_id = \"e366ffc3-cfb1-4aab-ab30-4a617bfc5541\"\n", "or request_id = \"5bcce87e-ab87-462f-8213-3059a73b6da4\"\n", "or request_id = \"bdc09153-9f17-438b-959c-0969ca1a1e6a\"\n", "or request_id = \"60128a5f-f5d1-4e38-a094-1b9381004a66\"\n", "or request_id = \"72ce36ba-0d71-46a1-9edc-7992a1252ef4\"\n", "or request_id = \"b88a9bf2-8ac3-4e40-811c-e6a4f9673947\"\n", "or request_id = \"9341abdc-90f3-4194-8581-46aa3f02f248\"\n", "or request_id = \"f829c630-aaae-4e0c-b7cc-71c87ff2d603\"\n", "or request_id = \"557e556e-ebd6-4438-bd26-d8bb68c8d94e\"\n", "or request_id = \"864adf0d-88c7-4c0e-894d-3908350d170f\"\n", "or request_id = \"2d0c5eba-46c0-4697-a150-ba3e255e0b97\"\n", "or request_id = \"50d1b7ce-ea25-48c2-ab80-ea2669ff57ec\"\n", "or request_id = \"db9b76cc-0fb9-4548-9c76-6d696ae0e4fd\"\n", "or request_id = \"e0a45fbb-a2d6-4641-941d-e3e4a3b51335\"\n", "or request_id = \"c125170a-22c3-42bd-9432-c75534988a69\"\n", "or request_id = \"81609440-5e31-40f2-8d9a-e929448dcd41\"\n", "or request_id = \"c99ec8cf-2f35-4e77-b653-7bd899d6365b\"\n", "or request_id = \"cbb580a1-2939-44a3-9cc2-e133cdb38211\"\n", "or request_id = \"619c5c73-d0b0-4877-b2ea-4ae2b1c26e47\"\n", "or request_id = \"d91eff45-2f1d-4d6d-9cce-7ace68920bb9\"\n", "or request_id = \"7fce0176-dc39-4f9c-a250-b49881dab1ae\"\n", "or request_id = \"d52fdb2e-d264-444a-ae3c-e6650ca51768\"\n", "or request_id = \"5bf9b4c1-2f58-4460-85f1-0cb1a14c0f1a\"\n", "or request_id = \"b9c8a4e9-8d3f-4aae-ade1-d3f89b1b9f80\"\n", "or request_id = \"72014ecd-709a-4681-8a1e-94a083177a2c\"\n", "or request_id = \"d1b46993-f3c1-4592-877c-d8c3c9031c36\"\n", "or request_id = \"69675109-7da8-411d-b04d-eba3090ba3dc\"\n", "or request_id = \"963beb29-9304-44ff-9d1d-8910c7c72e40\"\n", "or request_id = \"568c03cc-0402-481d-bb8d-e219fa681430\"\n", "or request_id = \"7853a1e0-23a2-4c55-a99a-f3f81678341a\"\n", "or request_id = \"536aea88-3f61-4918-a7bf-92f1e5964982\"\n", "or request_id = \"1e8bf0c1-8bd7-4bf1-9807-e49196dda4f4\"\n", "or request_id = \"d41557cb-8991-43f2-87da-b85799fcc6b2\"\n", "or request_id = \"d2b7a33d-0b88-4682-aceb-57f09b430c3c\"\n", "or request_id = \"2248a510-fd0e-4105-92e7-14afffd8f6bc\"\n", "or request_id = \"62ecc2c0-f8aa-4fe3-b4b1-d49d301cc3ae\"\n", "or request_id = \"ad5f2072-0e50-44fc-a75c-5cec8ecf899f\"\n", "or request_id = \"dbe098b2-fb83-4623-ac8a-2a9fafc78545\"\n", "or request_id = \"ba169cda-4328-4787-9a1f-5ce88e4e86f3\"\n", "or request_id = \"83968fa7-b0a7-44b6-9b76-4168c04e40f5\"\n", "or request_id = \"f526eb02-c5d3-47b6-8f16-d472daa3bb37\"\n", "or request_id = \"2f7f60f7-7851-446a-abad-06ea92eeac0c\"\n", "or request_id = \"4a80be9c-aef5-4992-8e31-892e6d3a9e6e\"\n", "or request_id = \"2bf68cf9-d46a-44a1-b082-967fd213c53f\"\n", "or request_id = \"b656145c-1446-4e58-a382-fa4470761c16\"\n", "or request_id = \"c76a299e-f93f-4ae9-b02c-3f290fc6a7b5\"\n", "or request_id = \"df3abf13-a7a8-4c6e-a784-0230d9e3fe38\"\n", "or request_id = \"03cade83-5ec8-4a8a-9509-88fd3a66b51b\"\n", "or request_id = \"fe8568ba-7c60-49ca-bb83-603cb96c5560\"\n", "or request_id = \"c1355d71-c80a-4ce5-9552-718ef9a43fd3\"\n", "or request_id = \"8c68034e-e6e0-42e8-ba27-ed5a6f0abbdd\"\n", "or request_id = \"2cc87b42-95ee-4775-8158-b8b29e661ccb\"\n", "or request_id = \"c17939da-77a8-4a87-97f0-00f33f01ff1a\"\n", "or request_id = \"390a3038-4a9d-4851-ac96-1d46e24c8108\"\n", "or request_id = \"75abb51b-f0f2-4bb9-8b58-9680b9efb9d8\"\n", "or request_id = \"66830145-182f-46a9-a776-11a0796fd6df\"\n", "or request_id = \"9cb29206-517d-4ca1-b419-e98eaf7a94c5\"\n", "or request_id = \"f1439f6c-8274-401c-bdb9-6e188a7cbad1\"\n", "or request_id = \"73fb340e-c550-4abd-b1ea-372aef37b4b7\"\n", "or request_id = \"c24d3528-48ba-4438-a8b0-8b5faf69f75d\"\n", "or request_id = \"fb56d1ec-c3d3-4830-93e6-15ef0347083c\"\n", "or request_id = \"3c22cbb0-0945-4f2d-b0dd-8ff68827b436\"\n", "or request_id = \"adc1a520-c245-4810-8e4f-cd07e08a4abc\"\n", "or request_id = \"a2b28545-e919-4d06-a931-01bf05f6c422\"\n", "or request_id = \"3f98c571-a176-4959-bb0c-ef6a8f06951f\"\n", "or request_id = \"65aae3f8-aef8-4624-977b-bd6665f77e8e\"\n", "or request_id = \"9f9d4240-19b8-4c5f-823f-b3d94a381d32\"\n", "or request_id = \"85a191fb-b814-41c9-845f-833706a03c3b\"\n", "or request_id = \"4769a7ef-de06-4ace-b01d-66b3b0f89b7a\"\n", "or request_id = \"ebb63e51-804d-4b0d-a5b3-258cf3b39665\"\n", "or request_id = \"832ca3cb-7402-42c7-965a-dc3ec58885b4\"\n", "or request_id = \"6be5d131-5c31-4406-b6d7-f877e435e194\"\n", "or request_id = \"333dd8d7-2ed3-4e2f-9750-c42588d3d306\"\n", "or request_id = \"97ab8dae-3685-4556-bf4a-91951bf4451f\"\n", "or request_id = \"deda5eb6-b0a2-48c1-8465-418082dc2acc\"\n", "or request_id = \"160bf494-fcc0-431c-a02b-1fe7341f5ca1\"\n", "or request_id = \"006c0129-eb9e-42ec-bfc4-4100bc656a48\"\n", "or request_id = \"5f5e3095-7211-43bf-9b19-cd405c678526\"\n", "or request_id = \"8a5f1012-500b-4326-9267-45c3271b8b13\"\n", "or request_id = \"16f65f93-59d3-4247-8360-e4031ba7a09e\"\n", "or request_id = \"72ca8c38-a932-4691-96a5-1e1f99c5ca3d\"\n", "or request_id = \"8f512353-77f4-4a7b-8f5b-2a5c840872d5\"\n", "or request_id = \"29643e54-9300-4cce-b312-1f3548dc0a62\"\n", "or request_id = \"a6f481a2-88c5-430a-aa6f-e50fbff32dba\"\n", "or request_id = \"371eec72-6716-4f0d-932c-516591d84288\"\n", "or request_id = \"8c980df6-09e2-48aa-84d7-b6a294efe7ea\"\n", "or request_id = \"48e3b7e2-96b9-4c3b-ac2d-6387a449436b\"\n", "or request_id = \"55699218-85b1-482c-81e7-df2258e0ff77\"\n", "or request_id = \"b6174fc7-59e8-4fdc-b108-7e30ec5afa6c\"\n", "or request_id = \"18edac60-051d-4b09-8229-54ac1d043702\"\n", "or request_id = \"999a47f0-e685-4910-aa24-3c739b6b86e1\"\n", "or request_id = \"f3305339-0b49-4132-a3f1-df8d1f916a1e\"\n", "or request_id = \"d48641b2-04bf-47b7-a1cd-3b2de456d41a\"\n", "or request_id = \"8d76c848-125f-49f7-9a20-a6f5fa3e8974\"\n", "or request_id = \"a910dd94-a9a3-4b48-aa2f-06f497632468\"\n", "or request_id = \"99ea8056-bf6a-4a81-b356-335ca175237f\"\n", "or request_id = \"af5cafc2-1d78-4f32-a6c9-a4d926b6de77\"\n", "or request_id = \"68c66dc3-b6fa-4971-9139-477964b09a29\"\n", "or request_id = \"0767a645-daac-4e5e-8cd5-7aa5a7172ecc\"\n", "or request_id = \"14e27278-2f74-418e-98c9-79ee1a297fb7\"\n", "or request_id = \"d2c3dab1-75a7-4448-bb8e-ade2a4f915ae\"\n", "or request_id = \"d1376c01-b79b-490b-aba3-8b88a16020a1\"\n", "or request_id = \"2d9d13ba-44d4-4281-8c04-6dd04301d11c\"\n", "or request_id = \"5a854478-1266-4e01-aaf2-b83f4646dd41\"\n", "or request_id = \"020ec318-99ff-48a0-a300-9ff620798cc7\"\n", "or request_id = \"1e97c749-46c4-496f-b355-db3092376bd4\"\n", "or request_id = \"4197f0a3-08e9-48a4-83ea-2ec2ff968887\"\n", "or request_id = \"8d86cb95-9cfc-41d0-8361-66b5c3391471\"\n", "or request_id = \"8e4262d2-ef28-470e-b439-42b87b40f6c6\"\n", "or request_id = \"ecc0e017-7425-4f6b-b90b-571940c19893\"\n", "or request_id = \"90ed2348-ffc2-4c2d-8256-f467e8515232\"\n", "or request_id = \"8a150819-edeb-40aa-a446-0482562ad5a7\"\n", "or request_id = \"30c99267-21c6-4691-8805-81c735b89255\"\n", "or request_id = \"4f87d185-0852-4d20-97e5-dc772521a8d2\"\n", "or request_id = \"3122f29b-5a3c-491c-91f3-0415b7eb25c3\"\n", "or request_id = \"219f5604-60aa-4179-af62-80c11b9c0c1d\"\n", "or request_id = \"621cce43-ac97-4db9-a87e-fe1d6db2b1f0\"\n", "or request_id = \"39c1c557-2ceb-456e-a7ac-e82dedd23f41\"\n", "or request_id = \"ebe8f318-1f09-4e26-bde9-757f5a148f41\"\n", "or request_id = \"69153687-09d9-400b-a037-eb2f1eb9713c\"\n", "or request_id = \"1db80cb8-4551-4215-acf1-ae909faa4eda\"\n", "or request_id = \"1e00de5a-b715-4b6e-bb92-9136da53e350\"\n", "or request_id = \"fd1c0793-efc0-4250-9c05-1290aed41da5\"\n", "or request_id = \"a2efc09c-3ab4-4bfb-b1d9-5e2b66446561\"\n", "or request_id = \"2f2283d7-f4d6-4243-b5ca-8a27dd0da22a\"\n", "or request_id = \"c2a67e09-c0d1-4e07-a68e-8a00cbfa44c6\"\n", "or request_id = \"63a5a81e-095e-4815-92fe-2e0918b22043\"\n", "or request_id = \"3942053f-165e-4596-809e-1a06a53422cf\"\n", "or request_id = \"32eef0cc-c2dc-415b-a44b-ebab113b13ea\"\n", "or request_id = \"988c0adf-ee24-4fd0-99d7-595482a71b9b\"\n", "or request_id = \"36f251ce-cdb4-4a89-8309-9abcd9f525b6\"\n", "or request_id = \"9ce3ca13-7d02-472f-9071-b565ae4c0138\"\n", "or request_id = \"5af35545-592e-4578-a1a9-c52065dba9de\"\n", "or request_id = \"d545f2af-346e-4d4b-8b82-35ec2b32a8aa\"\n", "or request_id = \"78125207-0d9a-43e5-801d-5a7e40f0a954\"\n", "or request_id = \"fb76857a-70a9-4b45-a940-25d866e458c4\"\n", "or request_id = \"c1911cd5-8596-4971-9b19-8b29106fcd5c\"\n", "or request_id = \"dae143c9-4205-4ccc-827d-3f540ae70402\"\n", "or request_id = \"64bf7130-052d-4904-8100-591819a288c6\"\n", "or request_id = \"45afac88-5ae5-40b9-87ab-7e82b014a5dd\"\n", "or request_id = \"5e1695bf-45f6-49c2-9036-e9177d9cfec5\"\n", "or request_id = \"9a775c62-6cc4-4b01-89b0-a17e607dd51b\"\n", "or request_id = \"0763d6e7-d284-4149-bc68-77268c6f3218\"\n", "or request_id = \"ecb86a3f-e94e-46a5-bd74-c334c4fe4cee\"\n", "or request_id = \"4963b22e-5033-42a2-a1cd-9fd9f5c6ac6d\"\n", "or request_id = \"37f473fb-9228-4e9f-af17-512962888136\"\n", "or request_id = \"8b32e865-3d36-4957-a585-8b10289242da\"\n", "or request_id = \"b3041f5e-1f8a-46fb-96df-10ca20425c6d\"\n", "or request_id = \"d2a68c8b-d6c2-4c0a-8773-693d1c57ca8b\"\n", "or request_id = \"e1458050-3763-47d9-b063-90cb32bec31d\"\n", "or request_id = \"08087bc3-8647-4c15-a0ba-97de7ddf24f3\"\n", "or request_id = \"5a1f0af7-417a-4a78-8616-cec3625adb4e\"\n", "or request_id = \"92e823cd-0806-47b2-963f-1d3df257e01f\"\n", "or request_id = \"cffea27f-eb43-4c26-8442-5028f529c3f1\"\n", "or request_id = \"ac0815d5-82fb-467b-91f6-04908f15f2c5\"\n", "or request_id = \"502c831f-d20a-4f9d-bd79-f79ddf3030f1\"\n", "or request_id = \"12a3a0b7-4eb9-45ef-9b5d-6ffb3fad08d1\"\n", "or request_id = \"3f8c1a05-767c-4896-a3d3-0cf951a21991\"\n", "or request_id = \"ea952d91-f39f-4235-bff9-09599ce154c6\"\n", "or request_id = \"fd726663-fbcf-4884-93af-46033edc06c0\"\n", "or request_id = \"99144f58-efd5-45f0-b55e-030ba512ffb8\"\n", "or request_id = \"0052bcc2-67ae-4ba8-a7e3-b9c8ff3ec1ea\"\n", "or request_id = \"8edbee4a-a3da-46b3-b60c-3a419e99efee\"\n", "or request_id = \"38f656e2-a87e-422a-a1f1-a6f81b842eb9\"\n", "or request_id = \"78510f6e-d471-4ef9-81a0-d0a2ae1fceb0\"\n", "or request_id = \"1a701b4d-38b2-4372-a8ff-8c0ba2829361\"\n", "or request_id = \"0e6a95f0-3e57-474c-9f5b-372054c411d7\"\n", "or request_id = \"b3adcc29-0022-4065-b09f-a48544d28d97\"\n", "or request_id = \"e03c9fe0-c4c4-435b-aa9b-00569ed0ddf1\"\n", "or request_id = \"719bd4cd-5cd9-4ab2-ab93-33b591b5a9bc\"\n", "or request_id = \"ebbe5bb9-47c7-4b3b-aa62-fdf70e2c4434\"\n", "or request_id = \"efe36a3e-664b-4ff6-834c-a380989b61f8\"\n", "or request_id = \"0a9084fa-0ffe-45d9-9216-c68ea2544fc3\"\n", "or request_id = \"3d9fa945-dc63-438b-95bb-b0a596363827\"\n", "or request_id = \"4a10bb05-00c8-44ac-a486-562144f04b2c\"\n", "or request_id = \"50806faf-2b58-40d3-b509-b98af56b3ab1\"\n", "or request_id = \"f7d1c97e-ad7a-4fc2-813a-e20139aa3cc4\"\n", "or request_id = \"719e6c00-4ea8-46a0-90f0-3a0904b05686\"\n", "or request_id = \"6370a3fb-322e-4884-9cee-548222cf0cfb\"\n", "or request_id = \"6929fe60-4e1f-4a3a-9a69-e22282c52938\"\n", "or request_id = \"1f1f73bb-7c55-42a9-ab6a-c16dfe69f98a\"\n", "or request_id = \"92a42324-dbb4-4d1a-88df-9336e677a19b\"\n", "or request_id = \"23012f7f-3a8d-43e9-b19d-0fc01f7379a6\"\n", "or request_id = \"8fca7996-5b2c-42dd-af4e-eca599119e23\"\n", "or request_id = \"f07982e8-cdbd-4660-9b4f-472202b27c6d\"\n", "or request_id = \"e6e736d2-65fa-475b-8f8d-6ad01645fa9d\"\n", "or request_id = \"ca1bd785-9717-4679-80d0-ef433c9bf099\"\n", "or request_id = \"ec7a8482-ced5-4f61-b362-36dc413883c5\"\n", "or request_id = \"648335dd-18a6-421f-8507-d13de6759a11\"\n", "or request_id = \"eee86739-5d06-4f6f-92d8-b04a7d8ae06a\"\n", "or request_id = \"78987ef0-9f10-4cd4-8a58-0e41530a281d\"\n", "or request_id = \"95ca168a-2df2-4588-8003-eca36d982c26\"\n", "or request_id = \"cd337716-1700-4798-b99b-ed240349800f\"\n", "or request_id = \"0cfbae81-63fe-4b19-8933-176b2f4d0da0\"\n", "or request_id = \"b58803f5-ddd2-4a01-b821-f76dee396533\"\n", "or request_id = \"73ca2e24-fe8e-4550-9cd3-2cd7d5eca008\"\n", "or request_id = \"8861e29c-20d9-498f-8dfa-f6f4a14d4314\"\n", "or request_id = \"1bee665d-3491-4ca3-9cf6-26a147551a77\"\n", "or request_id = \"ee439f28-ccc7-4531-bf17-f4f1287a10ef\"\n", "or request_id = \"3db7b0c2-f3b3-45f6-a38e-37c0308bf91f\"\n", "or request_id = \"c460ea69-59ca-4a8f-a846-4c89b577bd2b\"\n", "or request_id = \"6ca0ba04-3036-4b8c-bfe1-ee9cf8c4e664\"\n", "or request_id = \"c5ebde5c-68f5-48bf-adbf-3b0f0f85b9dc\"\n", "or request_id = \"721bcce0-5b27-4ab7-b8bd-45900eda9401\"\n", "or request_id = \"457e41ec-f78d-4211-8222-d4e5326c4ff6\"\n", "or request_id = \"b29b243d-a2a3-4cff-aaca-ba61fcfdd3b7\"\n", "or request_id = \"b17bfd14-baa1-4124-816c-9252483d462f\"\n", "or request_id = \"5d9ce775-6777-4ab3-9975-257d9cf70bd3\"\n", "or request_id = \"7e44ffe4-636f-4f77-afc2-652f73730e6a\"\n", "or request_id = \"81efb565-e9dc-40e8-a203-b9eee9afa8cd\"\n", "or request_id = \"7b8791ad-3897-49ba-aec4-da3a2c1ca174\"\n", "or request_id = \"4110888c-1d59-4352-83d0-7426fa1ae45c\"\n", "or request_id = \"330237dc-fada-4dc5-abf0-e0c14d4ec8d3\"\n", "or request_id = \"b73f1e93-2c22-4bb8-8633-284e7700ba4b\"\n", "or request_id = \"f0348a11-f632-4a73-acda-862ac10be10e\"\n", "or request_id = \"1d718dfd-75ad-4047-8d81-842d57a13034\"\n", "or request_id = \"ce5903f1-ca6a-42d7-bdba-a5849fec2613\"\n", "or request_id = \"f729dcfa-8827-4281-aaea-12e66f2dc309\"\n", "or request_id = \"04317fd6-3c28-4b7d-bedd-889fff6dbea5\"\n", "or request_id = \"23ada0c6-d659-42c4-9bac-d51b46b3eb17\"\n", "or request_id = \"06f4dcd2-6d4d-4f39-9e08-c8a1dc6d2941\"\n", "or request_id = \"aadb37dc-92fc-4d3a-bcf1-95a6eb3be1ea\"\n", "or request_id = \"4584331a-4ff4-4f77-a22b-da900ebbe2f3\"\n", "or request_id = \"192cf571-d80c-4b29-ad22-715119657424\"\n", "or request_id = \"1b5891f3-2c29-4351-8d30-9d29cda717ea\"\n", "or request_id = \"718eef1b-1961-428c-afe0-56f7a5362f2a\"\n", "or request_id = \"09ed2c15-d62c-4ba0-94f0-abdd57f363f5\"\n", "or request_id = \"33a2cd17-9b3e-4d19-a38d-4ff57b9685ca\"\n", "or request_id = \"5f2e466a-432d-4132-9f8e-98137a3d8e13\"\n", "or request_id = \"bf181746-00e6-4138-aeb1-2585857337fd\"\n", "or request_id = \"a41a9990-9eea-49b6-8333-5979c13b0865\"\n", "or request_id = \"0318246a-658a-43b4-9a33-0f5b9eb06f06\"\n", "or request_id = \"f561bec5-b2e9-45da-9a73-88ade0c6a28d\"\n", "or request_id = \"1760700f-1a44-4637-b6d7-c62a9ae06ed7\"\n", "or request_id = \"55beff8d-5089-4470-adef-e784d512048a\"\n", "or request_id = \"50aa32af-c3b4-48f2-8a1e-bd007876ec01\"\n", "or request_id = \"3c60bfcc-b410-4033-aee4-c9c096d93ba7\"\n", "or request_id = \"f8570a82-87c8-44d5-b5bc-e819ffabdce7\"\n", "or request_id = \"9782e0a4-08ff-4357-a849-6e0e9f10bcc9\"\n", "or request_id = \"b0007ae9-2f80-4ef4-abb2-f01a4535fc49\"\n", "or request_id = \"47c36d1b-88cd-47d9-a61b-e011476d82c5\"\n", "or request_id = \"52922c6f-0566-4060-bde8-4cd001b25770\"\n", "or request_id = \"aa66363d-fb99-4213-852a-8f97b58399ab\"\n", "or request_id = \"6e6dacf5-0c07-409a-83d8-9c6c70751ae6\"\n", "or request_id = \"a972e568-aa70-4d0b-ba0b-3f3253fe7694\"\n", "or request_id = \"bbc0f7e1-2a33-411c-9ad3-35642271bd69\"\n", "or request_id = \"9dbfce6f-3ea7-4e40-b670-5a3f20e965ab\"\n", "or request_id = \"e89b8f33-4228-4e0f-bbb7-027ca503166c\"\n", "or request_id = \"e49f6a00-9f24-4fda-ac26-547acbbf8f57\"\n", "or request_id = \"3aa6dff9-99a0-4b4d-9d7a-642c398157ea\"\n", "or request_id = \"19ec8d71-7f4d-4965-b230-3c1eb5b7f4e5\"\n", "or request_id = \"6ed6eb8b-2352-4703-bd82-594d591ec91e\"\n", "or request_id = \"7a076d92-53c3-4b4c-93c8-67818a3b63d6\"\n", "or request_id = \"401207c0-5515-43ae-902c-feba2b918cc5\"\n", "or request_id = \"55b12967-c717-4f24-8eef-8f67db00329a\"\n", "or request_id = \"8a1211b0-9d98-49a1-923d-e9c09f320565\"\n", "or request_id = \"1696a072-bb11-4b3c-a315-7d8781bd6f1a\"\n", "or request_id = \"e6974a6f-8222-4a6c-b910-fa4c234abf4a\"\n", "or request_id = \"bbb68138-ae35-43bf-869b-bd285405470a\"\n", "or request_id = \"e752b019-ac71-4d02-b94f-b080ccbfd83f\"\n", "or request_id = \"ba4e90fe-15a7-42da-a0cb-225a7b38549c\"\n", "or request_id = \"1692d0ba-5b5b-47f4-9257-16854ffe06b1\"\n", "or request_id = \"2761d1f5-eb4d-40a8-b949-72260bda8058\"\n", "or request_id = \"4ff34a26-a676-4729-a3d5-5d3a0f503315\"\n", "or request_id = \"a4609710-2187-4b88-aec8-48f74fc925c8\"\n", "or request_id = \"72076d25-083f-4ac0-841e-61deab58cc14\"\n", "or request_id = \"08a2533a-b634-490c-ae8c-b8b29f3a1f38\"\n", "or request_id = \"f60f2202-3f4a-4a84-8366-61b9a7b8744d\"\n", "or request_id = \"bbf74a20-fb18-4f3c-b655-fe898b269797\"\n", "or request_id = \"f1c22d51-1122-4d8c-9a33-a8fe8551e066\"\n", "or request_id = \"7c9fb535-b664-4092-b406-0b49a4bd5642\"\n", "or request_id = \"b1985fab-2c73-4ec4-ba62-eddf94e210e0\"\n", "or request_id = \"0ea2de3a-a6cc-4faa-8f43-bb052745848e\"\n", "or request_id = \"a627c9e2-e7cb-4b1c-aca3-771de3d38ea3\"\n", "or request_id = \"8ade57c4-3608-4d45-83bc-0ad937e97e8f\"\n", "or request_id = \"40b9450e-631d-47cb-872c-ee7545676b0a\"\n", "or request_id = \"bd7e161a-5def-4360-b1f1-544ae9e99f9d\"\n", "or request_id = \"d4b5993d-4e11-4123-8816-5e70559096b2\"\n", "or request_id = \"81ae23e3-186c-4f47-a29e-032693028737\"\n", "or request_id = \"afc0a596-79b4-4193-8955-a53ad74728d3\"\n", "or request_id = \"a936f85a-afd9-42f1-a8b6-e214bc95fbe3\"\n", "or request_id = \"47a7cc60-f91e-45bc-b1e7-05f132c14eb9\"\n", "or request_id = \"aabaf182-0e52-4146-9651-d49c3d882a85\"\n", "or request_id = \"fbd66f79-c2dc-456b-9bd1-3909155b9877\"\n", "or request_id = \"e68effb0-d214-4fcf-b177-22edb05cf0b8\"\n", "or request_id = \"3816b8d3-092c-48e6-add7-97ad1ec1d181\"\n", "or request_id = \"7ff87e28-7549-4b23-bd28-969e1fcba63f\"\n", "or request_id = \"38884504-a6e0-4028-bc3f-c3a53a71610c\"\n", "or request_id = \"a9639ac3-07ae-42b6-9ac2-112554b5c149\"\n", "or request_id = \"580708f0-28b7-4388-9a17-da2e24982600\"\n", "or request_id = \"f7c4253a-af6a-4cdc-9d22-5640a7a09276\"\n", "or request_id = \"5b409eec-5575-49c3-85f4-abb70e0d5678\"\n", "or request_id = \"19acc76b-5197-4fa6-aab2-52a599290797\"\n", "or request_id = \"78457683-95d8-4786-a428-284f0cdb1145\"\n", "or request_id = \"5b6be39a-d428-4e77-8937-69d809780d0a\"\n", "or request_id = \"815cc07b-7832-4b0d-a542-3b92ada6f926\"\n", "or request_id = \"79cbc7f5-5cf8-4957-a515-2714eaa27cf2\"\n", "or request_id = \"4f34038f-a8b5-4af6-87bd-5cc0e378a2e5\"\n", "or request_id = \"2f0742df-9589-4de3-b358-ca8464a52538\"\n", "or request_id = \"4219ad02-7874-4c0e-83f7-14992d4eac17\"\n", "or request_id = \"f6902351-7467-4ca3-8421-0760e161d62c\"\n", "or request_id = \"b848b5c0-126c-4303-8fa1-34de9cb11775\"\n", "or request_id = \"74850706-0e17-4d8f-83ed-59edcb6461ab\"\n", "or request_id = \"88311e53-3577-45b2-baf5-0a8d581ef201\"\n", "or request_id = \"06cbf836-3aca-4705-a6ae-452eb47eb355\"\n", "or request_id = \"5cd33e29-0631-4929-8773-98eb1c2d0d52\"\n", "or request_id = \"8b0148fd-32ad-4279-b1b0-d89e5a72c721\"\n", "or request_id = \"ee174d80-d5ca-4660-91f9-0c59dbc128e4\"\n", "or request_id = \"b2ea9593-dfe8-43e6-b1ed-995ea953956b\"\n", "or request_id = \"74568b87-dcd8-4244-9ba3-0e0b93a128c6\"\n", "or request_id = \"d28c73d5-7b76-492f-bcdb-dc5b0d26cd6e\"\n", "or request_id = \"3359e52a-e6b8-478a-b452-e49d23ef94a3\"\n", "or request_id = \"1bb2f90b-2eab-404e-a773-7683deaee774\"\n", "or request_id = \"9d5f6e28-76f0-4855-a62c-3ebc88b60aa0\"\n", "or request_id = \"32267d28-2f78-40f6-80a2-6596e9f0696e\"\n", "or request_id = \"7b6a48f0-6988-4188-be3c-71a2daf6a92d\"\n", "or request_id = \"19cabeab-628d-4735-83d8-9ca70784bb26\"\n", "or request_id = \"b3eff3e4-023f-45a3-ba91-e031e8347eac\"\n", "or request_id = \"e55ff26a-7d37-4680-b21f-cb8d937924aa\"\n", "or request_id = \"71f4152a-bb25-4532-baee-a94fcfd93759\"\n", "or request_id = \"379cec66-56fc-4837-865f-3c1ebad59519\"\n", "or request_id = \"3599f575-5e13-4589-bfce-ffe8e3a3fda8\"\n", "or request_id = \"70253d7a-4b62-461f-b9e6-54ea70d6f306\"\n", "or request_id = \"0333e2a9-ac7e-48ce-aae7-8deb8a50db7d\"\n", "or request_id = \"4160f54e-6cd3-40b7-8d58-08c439574984\"\n", "or request_id = \"6211b1c0-09b6-4c98-b9eb-27661368531c\"\n", "or request_id = \"c79e109c-a086-42ad-bea5-fff6eae08dcf\"\n", "or request_id = \"ba1d141b-c217-4bb0-b7a0-9e65762342a1\"\n", "or request_id = \"531537f2-3cb8-4f60-a95d-ce72a81d8fe7\"\n", "or request_id = \"161a4e21-0249-4e64-b392-2a8bb93d814e\"\n", "or request_id = \"a970cdee-68db-46b3-a8c7-c80d2dd003dc\"\n", "or request_id = \"fde00598-bbfb-4bc6-bd09-9413abc56afe\"\n", "or request_id = \"6ed48d1f-91c3-49c7-aeb7-430a95cd9b37\"\n", "or request_id = \"bc940d6e-85bc-4dee-8886-6bff66c9c233\"\n", "or request_id = \"b02f2437-c605-4508-ad11-c28c87fd1a15\"\n", "or request_id = \"43fef5bd-b455-4405-86f6-5737b099ea13\"\n", "or request_id = \"584fcd15-bae0-4490-9ffe-5e4646ede9d7\"\n", "or request_id = \"dec7fb40-d641-4ff5-99bf-86bb8a99042c\"\n", "or request_id = \"ceb9e620-6d8f-4dcb-adbb-0517b6fba6d5\"\n", "or request_id = \"e6fbe51f-506c-4f3d-84dd-cf0299d8ccb8\"\n", "or request_id = \"07315b4d-ec69-4d01-b5fe-9b0a75fef7f0\"\n", "or request_id = \"b2189309-8b09-488a-89c2-f9fa47d9bac0\"\n", "or request_id = \"6b9daecf-9642-480e-a4ce-a278c800ff86\"\n", "or request_id = \"8910fdb8-b560-4f62-a240-ac56fce5a2bd\"\n", "or request_id = \"6fe58c9a-be45-4675-9589-25efc18ea145\"\n", "or request_id = \"7ef8f6ae-1e6c-4a07-990d-8c28a3c6fc39\"\n", "or request_id = \"684bce10-f6b5-4879-b1c6-9523479dfb4f\"\n", "or request_id = \"42ac3884-6dc5-46e7-a9d6-b4512a0df936\"\n", "or request_id = \"0f4c42c5-f728-48ba-8f9f-3f035fc58998\"\n", "or request_id = \"a3cf712e-57dd-4a70-a11d-3568521a81f6\"\n", "or request_id = \"dd35782c-ec94-4c46-a1d9-0b952437f0f7\"\n", "or request_id = \"9cc75841-b94d-4b11-acc1-42b28bb4a1d2\"\n", "or request_id = \"3fb221af-bc33-4153-96fa-bc0d1f1e3055\"\n", "or request_id = \"b36b57ca-9a41-4894-a9fe-8097e28d98b7\"\n", "or request_id = \"8d44324d-c044-45e3-8cca-7cd33ce4009a\"\n", "or request_id = \"668cd6ba-c6f2-4902-9583-7c91b9b21c62\"\n", "or request_id = \"7cd23efb-2e97-41d5-af10-90797b5c4a80\"\n", "or request_id = \"835a4504-eb25-4ccb-a5ba-63087961a389\"\n", "or request_id = \"b4c7d055-de40-4b29-ba1e-9fc8b1e4def7\"\n", "or request_id = \"c2dd02eb-83ab-4c51-8713-6c7ccd08acd6\"\n", "or request_id = \"92ff3e52-255e-4443-a5c1-0a3ff9683a47\"\n", "or request_id = \"e37e13e8-e9c7-43e1-916e-830df73c743f\"\n", "or request_id = \"2c9b5c22-6457-4801-986e-6c1d4b5a2fae\"\n", "or request_id = \"65321c82-8109-432d-a5b0-3dc1b9ff78e5\"\n", "or request_id = \"9d194a02-46b9-444a-9358-8d23a28b1600\"\n", "or request_id = \"01c87e33-636b-4c10-b24f-ddfe0eec267c\"\n", "or request_id = \"b41b40a4-02d8-4c19-a3b1-19a12ff80c94\"\n", "or request_id = \"28ca4e8b-fd6c-4efc-8b65-8593587841fe\"\n", "or request_id = \"97e8e471-1daa-4395-9ea1-a7a819a02376\"\n", "or request_id = \"7dedcb6a-aaaa-4406-881c-594db124d094\"\n", "or request_id = \"b37f2069-4ec3-45ec-a5e1-6ae59ecb14b0\"\n", "or request_id = \"7017ac57-dc62-41a8-a957-020d0429e4b6\"\n", "or request_id = \"04da86d6-51e6-417e-972c-e6f7dc7e6da5\"\n", "or request_id = \"9849a9f1-d23a-48ed-9b78-7219b5a7a9a4\"\n", "or request_id = \"595a15e2-27ff-4e04-b50b-28e8095f6842\"\n", "or request_id = \"93052f4c-75fb-41bf-bf2c-4e2be8076ac0\"\n", "or request_id = \"4736ec6a-faea-4a08-a7cd-937af274a874\"\n", "or request_id = \"016ce281-06d4-49bd-b85f-b2553a87181b\"\n", "or request_id = \"61efc0b3-5694-4a67-b0bf-0b7d7c84f2df\"\n", "or request_id = \"805a3d75-3949-4490-8e98-dd8ba94331bd\"\n", "or request_id = \"1d5e297e-a907-4893-bbdf-8dd2e382947a\"\n", "or request_id = \"282296b9-e0ab-4c55-bef6-9b38c2482296\"\n", "or request_id = \"c4cf188a-758f-415e-a582-d328d7e2dba9\"\n", "or request_id = \"441de51f-2829-424e-8526-99c92b2f307b\"\n", "or request_id = \"3d28a681-ae9b-4099-b4f3-46e2d4f919e4\"\n", "or request_id = \"da47b90a-0e78-48ae-a231-6d02ade2a968\"\n", "or request_id = \"937558f8-ee2f-473c-ac19-ab901ffd9916\"\n", "or request_id = \"9ce89b55-ae05-426e-b11e-4bfe797cfb27\"\n", "or request_id = \"6a727024-866e-493f-b6cc-4103f6720175\"\n", "or request_id = \"622352a2-46ca-4b2c-97b6-b00a131d0514\"\n", "or request_id = \"cb8ffcd1-e3b8-4b69-8e69-88fe36af1912\"\n", "or request_id = \"898b3b5c-3abe-454b-b045-e4a327393e63\"\n", "or request_id = \"16c7d486-eff1-460f-811f-a06f94f793d6\"\n", "or request_id = \"6d2c7930-0c33-4d6d-9ec1-16bc76dec280\"\n", "or request_id = \"1af0cbf7-4b60-485c-a11d-b13f8c67a337\"\n", "or request_id = \"dd8e673e-a6ac-47b0-a833-d615acb0f14b\"\n", "or request_id = \"20ec1751-f958-4930-b9df-b419767d28c9\"\n", "or request_id = \"53ac1a37-b842-4605-849f-5a07aedb4e14\"\n", "or request_id = \"e64a9d31-5cc3-4aef-b142-00443fa21003\"\n", "or request_id = \"0aeee65c-8c76-42bb-a68c-7c021018b2d6\"\n", "or request_id = \"54978135-c425-4b7d-92ba-241a0fb7b634\"\n", "or request_id = \"e7eb9cc4-eed5-4149-810c-44c304c59997\"\n", "or request_id = \"387a6559-9594-4c17-b564-68467dcb62f8\"\n", "or request_id = \"8632e83e-17db-4b6e-b19e-4f5891ca93a8\"\n", "or request_id = \"5f07639f-7281-44d1-9e37-1fc9c512713f\"\n", "or request_id = \"343c8d7d-5b0a-4b0a-b959-7f4c647e1563\"\n", "or request_id = \"f19a8d2e-b8f3-400e-a26d-c701207f6e2c\"\n", "or request_id = \"fa794677-91d9-4d24-9539-2f3ff90b3467\"\n", "or request_id = \"ceb57199-c671-4615-b88e-0f53cea8e40c\"\n", "or request_id = \"ff773aeb-3bbe-4a65-9dde-cdf072a7bc88\"\n", "or request_id = \"80dbdb36-9d33-4807-a680-c448b642051f\"\n", "or request_id = \"bc58fed7-2d7f-480f-82b1-e3670fb6fd92\"\n", "or request_id = \"97215b08-912c-4d02-bb94-d5f61d6e16f9\"\n", "or request_id = \"424cf39b-669a-45e3-a576-f39496844ace\"\n", "or request_id = \"7fcf4d5e-5d22-42e9-ae6e-d869eb4dbe59\"\n", "or request_id = \"45d0f920-fb44-44c7-934c-8fb8e14cf930\"\n", "or request_id = \"7e4c46a1-93b5-4517-83bf-e4b6c84d4f66\"\n", "or request_id = \"ce206165-9b80-40fb-9a2f-cb1d83b88396\"\n", "or request_id = \"e29a17eb-4a04-4b89-a1b4-42179f6cdd95\"\n", "or request_id = \"807f119f-5b52-4b42-b0aa-9b93ea8dda28\"\n", "or request_id = \"dee094c8-446d-486f-9123-5c1d7f8656da\"\n", "or request_id = \"dc1caf96-1c44-448c-9d66-761b123d2d5b\"\n", "or request_id = \"e544d213-543b-43a5-8e26-e7e8a7de92cf\"\n", "or request_id = \"06d302fd-78ec-42a4-aeb5-a865e4642ecf\"\n", "or request_id = \"a06c2576-0bbf-4567-b128-2c9a851dc4cf\"\n", "or request_id = \"25fb8c07-33ea-4a47-9bfb-2e857fec4975\"\n", "or request_id = \"b6efcfe9-a42d-44ec-a24a-c89d8e4a6e52\"\n", "or request_id = \"e0c94ba4-fd21-48d7-9e4a-7454c260a43d\"\n", "or request_id = \"78367b3b-703a-429e-958b-6d676b70958d\"\n", "or request_id = \"6e44ea06-be8a-4742-8a7f-e3adc43a56a9\"\n", "or request_id = \"a0bbe7ac-9e84-48d2-8a6d-1bf5cd58f083\"\n", "or request_id = \"96e048b9-4383-4ec1-adf7-37cdd3ee6c71\"\n", "or request_id = \"15886ef5-4ff0-4994-a604-5a40e8fd3541\"\n", "or request_id = \"0714218a-d8e3-4a86-a651-d214e3540177\"\n", "or request_id = \"94873b12-3e5e-4a50-8b18-7398a87e02c3\"\n", "or request_id = \"a5e4ccf7-4be1-487c-9a8a-05a98de208c4\"\n", "or request_id = \"7980c0b5-1f9a-4cce-88a0-fc2285f615da\"\n", "or request_id = \"fc09f1e4-86e6-41e4-a9aa-4a09e4bc4669\"\n", "or request_id = \"c0a481cd-e804-4ac8-8c40-55d43a1b16e8\"\n", "or request_id = \"d60fbebd-7815-4b91-85f5-8b402ca3a459\"\n", "or request_id = \"e24b4847-0bbe-4b08-9886-cec9a6e83bb4\"\n", "or request_id = \"3fff6d07-b344-420a-981c-e0e8fd327644\"\n", "or request_id = \"16c3045a-7dfd-45d8-beef-795f057b03fc\"\n", "or request_id = \"49c7ed7a-c928-440f-965a-6c73ce6d5d23\"\n", "or request_id = \"f4f250dd-97a8-49f8-b059-57d54baf4360\"\n", "or request_id = \"e44c3973-942e-43c8-beaf-8b9572026ea5\"\n", "or request_id = \"3e36f7ba-ff77-4810-aafc-96ebaf284410\"\n", "or request_id = \"9138e24b-5242-4c0e-a264-95bcc43b274f\"\n", "or request_id = \"7df99160-e0ce-41d6-a9e8-2ae969a7ab43\"\n", "or request_id = \"3b83adb2-7897-461c-bc09-b8d19fc49768\"\n", "or request_id = \"842b6d61-8221-46e5-91b4-d759e1bce223\"\n", "or request_id = \"04776fd3-aef3-4a62-b774-98804db662e6\"\n", "or request_id = \"a9875754-82d7-499b-afa1-1fc656b8a5e0\"\n", "or request_id = \"05025cad-4359-4b5a-85d4-28691e2d2eb4\"\n", "or request_id = \"4af0f1ea-4ee6-427f-a3d6-26a33c582659\"\n", "or request_id = \"33a09dbc-8869-40ba-aebd-7d0b8e325cdd\"\n", "or request_id = \"eab2fe30-424a-485e-8ed9-5a45a74eefee\"\n", "or request_id = \"16423758-638a-4df5-9ee0-778f15b8de15\"\n", "or request_id = \"490bfa87-db5d-4981-8aa0-e0fbf6bd8b8c\"\n", "or request_id = \"4de13cdd-591c-4530-b54d-fed461be5daf\"\n", "or request_id = \"57e41153-5567-4580-a06d-5d725aeebc7d\"\n", "or request_id = \"bddc66e6-7a26-4fa0-a8fb-63b45056c2bb\"\n", "or request_id = \"6c563af8-13f3-450c-8149-f0b8f6b93c50\"\n", "or request_id = \"eda520e0-70f3-4e08-9572-2ceed7ed0346\"\n", "or request_id = \"684d37d2-29ac-482a-b39a-9c74c183e6bf\"\n", "or request_id = \"d8bfa78c-e2ce-487c-9b1e-0af227179a65\"\n", "or request_id = \"4ceedfea-1d43-46ed-99c4-018d49b66f5f\"\n", "or request_id = \"75bf6152-6a46-423c-991a-d799320bdd93\"\n", "or request_id = \"792224db-211d-4dfc-9058-3fa6a17f00d6\"\n", "or request_id = \"23935ea1-ae66-455a-bf8d-688c01fa3972\"\n", "or request_id = \"e3119863-2b94-478c-88a9-2926225642f4\"\n", "or request_id = \"a4ae054a-6d3e-4bd8-afac-55e9c54a890a\"\n", "or request_id = \"bc24cb45-b26a-43ef-8195-5295f18702ab\"\n", "or request_id = \"8f87c599-7880-4b1d-8ebf-ccae0abbb9c7\"\n", "or request_id = \"aa9ff073-d385-440a-83bf-59679e3c76af\"\n", "or request_id = \"044ffb59-756a-400a-aa6a-29f9d30266ee\"\n", "or request_id = \"41f1a6d0-9263-4bba-a7e4-b3d0d0f0cc40\"\n", "or request_id = \"3396b4ff-fc8c-4716-984d-58748b13366d\"\n", "or request_id = \"a93b1390-60da-49c8-a0ee-83dc9bf88086\"\n", "or request_id = \"cb62647c-1630-4412-8e34-2d2532f14e40\"\n", "or request_id = \"c4f52656-19b2-428a-adc3-3da065e07cd2\"\n", "or request_id = \"6969d793-5f8b-41b8-aacb-6cd61e740c7e\"\n", "or request_id = \"9d60f7e7-14a1-4395-a7a3-4ae03b0d3b26\"\n", "or request_id = \"12d10be7-5a5e-4c38-a123-d40dcac3c141\"\n", "or request_id = \"2276ed3c-cf66-471d-b7e2-57ba06a16101\"\n", "or request_id = \"43ac8b4b-23bb-4f3b-bbe3-c6346107e68f\"\n", "or request_id = \"7269d294-b151-4fcd-a3e2-f0e2b5ccf0d6\"\n", "or request_id = \"54031b15-496b-4c06-8858-dc0b908e3abe\"\n", "or request_id = \"7782f957-b52d-45af-a3f0-663ac9dbee01\"\n", "or request_id = \"932d6099-893f-4b41-b2ac-2542be54bedc\"\n", "or request_id = \"91a70bb2-f815-4339-ac93-952b084caeeb\"\n", "or request_id = \"84f52a04-b15d-4476-ba34-87e0d6b1745b\"\n", "or request_id = \"b9ecf874-ddc5-4ce3-bef1-707442f6c518\"\n", "or request_id = \"f3f06fd7-10fe-49b0-8130-02b3d4d8e926\"\n", "or request_id = \"ba75561c-d7a2-4122-bbe8-9a4e802de9ed\"\n", "or request_id = \"fb558a04-08bd-4418-8564-533b29bb0263\"\n", "or request_id = \"a47dc4fb-f1db-4533-b0c1-cf41044356b4\"\n", "or request_id = \"b7261324-b0b8-465f-ae47-53dc1799dd51\"\n", "or request_id = \"62c42c4b-12b3-402f-991f-f683acca1b4a\"\n", "or request_id = \"f3c246a0-3780-4759-be40-7bd5e9fa6c8d\"\n", "or request_id = \"8ed35e4a-2adb-4fbc-9c76-e12b08b7ca15\"\n", "or request_id = \"05323bfc-aa34-4dac-8a7a-566cbe091028\"\n", "or request_id = \"0c5a22da-3756-49af-a33c-c78dda827259\"\n", "or request_id = \"e94d2fb6-ccb4-4914-9d98-e5ab5471038b\"\n", "or request_id = \"3c78db73-6a9b-4225-96b5-b1397153f4a7\"\n", "or request_id = \"807b78f9-176e-4d1f-95af-588319433edd\"\n", "or request_id = \"905bfad6-6048-408e-b633-fd4e79ece7d7\"\n", "or request_id = \"c2755036-262d-4db6-991e-21dc807a42a7\"\n", "or request_id = \"6a7fd03f-2544-4c48-a5ab-54ee00dc85e5\"\n", "or request_id = \"d1278322-9dc0-412d-9f43-7d4317dd0f2e\"\n", "or request_id = \"c4b6b28c-913d-4fa6-bc14-3b219d06947f\"\n", "or request_id = \"8cb89e0e-9e79-4f8e-a6f7-3d17688f928f\"\n", "or request_id = \"78551ed0-af5c-4041-9a9f-59ce0770cc32\"\n", "or request_id = \"a8d51db4-edc4-47ed-b0bb-1e027d48f2cb\"\n", "or request_id = \"b7f0cd4a-45a5-4dca-b1c2-d6baeacb0707\"\n", "or request_id = \"de20091b-9f85-40e9-bd64-ef3834d6aebb\"\n", "or request_id = \"5996ebc2-a0e6-46c9-8bf4-b9be6db06442\"\n", "or request_id = \"a6044594-2acf-4efe-83ec-ec92996bf250\"\n", "or request_id = \"03c2e68a-8e33-4943-9fc1-a47b458ac677\"\n", "or request_id = \"df001ba5-658f-42ff-931b-9c884ae8551a\"\n", "or request_id = \"94fca04b-a97d-4bac-953a-0d55dfe4c8d4\"\n", "or request_id = \"cc319b40-e0b7-4848-b040-b133ab37fc0f\"\n", "or request_id = \"ba60a0bd-7a0b-4d82-a30f-99e96d3f9582\"\n", "or request_id = \"a04cde2c-4a08-4acb-9a8b-4d136cf86b92\"\n", "or request_id = \"7d7fe619-0f8c-431e-94e9-e385d4c9eda4\"\n", "or request_id = \"4b9211ae-843c-427d-bd69-ac2c570d175b\"\n", "or request_id = \"3ec32b96-2d87-4bcc-983d-5a95a932f6b3\"\n", "or request_id = \"e7781230-b99c-4838-8441-eb3df3a144f4\"\n", "or request_id = \"aae7939d-f3e4-45d0-bd12-dc05a3301bb7\"\n", "or request_id = \"b3026c1b-d32a-49f6-a9a9-c3fdbfad9284\"\n", "or request_id = \"23977d2d-c982-4b12-8732-80efecdf3694\"\n", "or request_id = \"fc23f9b0-addc-47f9-9c48-3bfa0daa899c\"\n", "or request_id = \"4474cde5-edf8-4625-9c76-64e1ab99b786\"\n", "or request_id = \"ef0cfdcd-52af-4cb5-8fa5-7768a7e8aa40\"\n", "or request_id = \"34f3c09c-63a2-4234-abcf-200c13322651\"\n", "or request_id = \"4ab29374-3394-4058-ac3a-79ed77ed2366\"\n", "or request_id = \"a4a91b5e-ee04-48a8-8fe9-1197bb1b7359\"\n", "or request_id = \"e0448970-4be0-40dd-b7cd-258b8541f182\"\n", "or request_id = \"1e68cd88-03f5-43f4-82ef-7fcbb3160923\"\n", "or request_id = \"9551c12d-e28b-4a30-9e5c-7edee384fc1c\"\n", "or request_id = \"f047c532-a324-4db2-a78f-f9cc6d9e381d\"\n", "or request_id = \"779fd74d-f6ea-4714-a3d4-5303fe00938b\"\n", "or request_id = \"0fd6285a-5579-4b97-8b35-bf13706d7ce9\"\n", "or request_id = \"064ef3c5-0304-433a-a1fc-c5b33992eba7\"\n", "or request_id = \"828da0fe-c847-4b2d-9cc3-61e0c6999ace\"\n", "or request_id = \"30a0387f-8909-458e-b8b1-81f6f2af7637\"\n", "or request_id = \"bf52a36f-b0a2-42c9-8d5c-6fc3af385eff\"\n", "or request_id = \"964eb2a5-500a-4b89-b0b8-813c58058258\"\n", "or request_id = \"7aa9bb4c-2731-408a-b622-a75f08dcaf23\"\n", "or request_id = \"8cb22055-8585-40cd-a64d-4b35386b6d12\"\n", "or request_id = \"96a3bf69-45ca-4096-ae9f-a4f1f41fd43d\"\n", "or request_id = \"0aa98072-b08b-457f-8df1-3415360c11f8\"\n", "or request_id = \"e5c6e32c-656a-4713-9cce-b46e5ea92322\"\n", "or request_id = \"8ecd88a2-058e-4cde-b59a-56df06047c4e\"\n", "or request_id = \"c8a382ee-9e15-4ef7-8c48-1bb6f6cc34e6\"\n", "or request_id = \"537f139d-ae90-4535-a89d-10e892088e29\"\n", "or request_id = \"a86680a1-f79d-460b-87b8-003b9d70f348\"\n", "or request_id = \"d2673d1f-4543-4ba6-a29a-46e9ef9c58b4\"\n", "or request_id = \"337a6388-00e2-498c-894e-ecbcfbe5e5f6\"\n", "or request_id = \"9dc740bf-ca70-45b3-b9c6-a933a2b0b51f\"\n", "or request_id = \"0fab60da-9ae5-4671-a9f5-5d6603499187\"\n", "or request_id = \"86b8d289-d079-49c6-a0ae-e792a25c9e70\"\n", "or request_id = \"f9b057eb-afca-4f20-8feb-f02933b20276\"\n", "or request_id = \"b73261b6-8fb9-4515-90e5-2e753eb66cdd\"\n", "or request_id = \"2db7d046-41ca-413e-b191-c1613dc19b6f\"\n", "or request_id = \"8d6ce0d0-985d-45c8-9866-150849dc7e09\"\n", "or request_id = \"3f8387dd-75d9-4f5b-8544-e9e1a1ae8cc2\"\n", "or request_id = \"a8df5599-139c-4df6-ac8b-bd6d55e694fe\"\n", "or request_id = \"e1228193-f439-426c-902a-2a0f5670b5d3\"\n", "or request_id = \"6927f5ab-3bf4-4ed3-b692-a86a9c52589b\"\n", "or request_id = \"88e408b8-c484-4e47-b4dc-58862e41f882\"\n", "or request_id = \"fdc778cf-ad67-409e-a606-f31403563238\"\n", "or request_id = \"393fac07-cec7-4c8e-b365-7d1c1f33dc26\"\n", "or request_id = \"3763f0b6-fb95-45c5-ae8a-7994e5c189de\"\n", "or request_id = \"bf2db955-5356-4133-b073-67bfa25018d4\"\n", "or request_id = \"8cd5e1ea-3780-48c1-8787-0391a7b5475a\"\n", "or request_id = \"33d3052a-f6be-479d-a201-80b2d23485dc\"\n", "or request_id = \"0a11fa4f-0a11-49fd-8878-d02bf700e528\"\n", "or request_id = \"7b654082-6600-4d60-aec1-66bb45886da9\"\n", "or request_id = \"391de708-6795-4aab-bffb-5c48dfcb6bcc\"\n", "or request_id = \"b013d397-254a-42bc-8f96-49dc054ee4b8\"\n", "or request_id = \"76199fd1-ca07-47de-8d6b-792ca7c96bef\"\n", "or request_id = \"98cf9217-756f-4e33-bbc3-13b4ea990657\"\n", "or request_id = \"8f6cf9cb-c15e-4520-9fef-9e380f9be53d\"\n", "or request_id = \"73757fff-4436-4636-ac79-115b5fc6d7f6\"\n", "or request_id = \"c557f696-260e-4d63-875f-42eee2323bce\"\n", "or request_id = \"d7adf90e-a409-4a37-93ad-912f5ac6df69\"\n", "or request_id = \"4a279298-c4eb-43e4-afff-7ea34850072d\"\n", "or request_id = \"d6de5435-f453-4d63-800e-74e72bf83de8\"\n", "or request_id = \"0abc3fd7-50ce-4aa7-879d-8835bb0c267e\"\n", "or request_id = \"620503d6-fda4-45a1-92bf-05744d31ca78\"\n", "or request_id = \"ee5625a4-6f46-4f4d-a552-ad88db171d86\"\n", "or request_id = \"9017c00b-1ab6-4589-ba2e-2ce973cd39e2\"\n", "or request_id = \"d869163f-89d4-44a5-98c7-a392ef2d673c\"\n", "or request_id = \"e80b5ef0-3d41-46d6-8ff0-6450fa6e6b6c\"\n", "or request_id = \"d399d6a4-5b8f-4b63-9fc8-094e7dc3fc51\"\n", "or request_id = \"63e76850-202b-420c-ba90-824dc20da198\"\n", "or request_id = \"c55f9a5f-9a8c-4baa-86ec-a437e472a97e\"\n", "or request_id = \"e5166182-6ee3-474f-be86-b595efd49ce2\"\n", "or request_id = \"3b3b8f88-4b39-4731-9c91-f67588823429\"\n", "or request_id = \"106cd64f-e045-4640-a0b3-833979b880ac\"\n", "or request_id = \"e02468ac-2889-4ffd-b304-a956b0c915f0\"\n", "or request_id = \"e8ad6944-1e6c-434e-b7a6-7fe5e6908880\"\n", "or request_id = \"71384997-2bb6-4b12-b6c0-a5dcd8b3a5cf\"\n", "or request_id = \"b0a78413-fa2b-48fe-8e77-b505349c044d\"\n", "or request_id = \"5abac3ff-2c1d-4005-90fa-74945a8e7252\"\n", "or request_id = \"f5fd68dc-6aa6-47d0-b099-fc81b43ba4de\"\n", "or request_id = \"a1fae661-8d60-4bda-a17e-7507bda0ec3c\"\n", "or request_id = \"5c4c3bdf-3fdf-4e50-9e4d-0ff5576330f8\"\n", "or request_id = \"28b9aa50-2844-46c9-b0d0-3b5196ebb529\"\n", "or request_id = \"312777e8-a37f-4515-858e-239a293d2f8e\"\n", "or request_id = \"1fd786ce-b53e-4a7a-a5ff-e821c0f5b260\"\n", "or request_id = \"457e9b05-cecb-40d0-907b-92c07b73434b\"\n", "or request_id = \"e3419e21-a3d8-4162-9bc6-382409cb6cf3\"\n", "or request_id = \"e4606d41-6a98-4301-b6a1-9cf2dd1fa8db\"\n", "or request_id = \"5fe25a96-f677-4b97-8a28-5fcf547e9a51\"\n", "or request_id = \"277b0fdd-27b8-4542-8773-c9eee78ae986\"\n", "or request_id = \"8ffee4d2-4333-4db3-93f4-0493b0829eba\"\n", "or request_id = \"38a1cfd8-5453-4d38-88b6-97180115e981\"\n", "or request_id = \"9a1fa2d4-8eab-4c48-b178-55d15ea61706\"\n", "or request_id = \"4fa1731b-d808-4bcf-a006-7f1ae29a38d3\"\n", "or request_id = \"e7735fc0-25f9-48d8-9f8f-41c943f76bc4\"\n", "or request_id = \"a4a60537-1c5a-4cb0-9bdf-bcd38bd42234\"\n", "or request_id = \"845cc5de-5936-4544-88db-6bbf182e3726\"\n", "or request_id = \"0a345a4f-a870-4297-a6d3-9b7fc9779379\"\n", "or request_id = \"d6196c41-04e0-42e5-9575-75735838a78d\"\n", "or request_id = \"ccbbc28a-d6d7-4f3e-bd0b-7fbb5b0bab3e\"\n", "or request_id = \"7d597823-2b11-491a-9c32-1cdac5be2978\"\n", "or request_id = \"b06cc909-33a9-4dd9-8ffb-bab1ce125873\"\n", "or request_id = \"1f55a81c-2efa-4bc8-b7ab-42d9116c8968\"\n", "or request_id = \"33cf2e64-013c-4ba4-9451-c66321263175\"\n", "or request_id = \"80fa710c-453a-45bf-acc6-7e2227a8b797\"\n", "or request_id = \"863b81bb-1d48-413a-b564-6adcffe72d9c\"\n", "or request_id = \"f464df0d-ee45-4133-ada6-6178f3eff17e\"\n", "or request_id = \"71b27b5d-49b8-4eb9-acf8-4b6cd54ec673\"\n", "or request_id = \"f230a375-64e8-4dc6-9deb-6f6d892d4105\"\n", "or request_id = \"78265a58-c130-4355-9169-d216d7aeae07\"\n", "or request_id = \"38e648f0-bde4-40b2-acba-ffd6e5aa23de\"\n", "or request_id = \"627a0865-a8f2-40c0-8dc2-d5f25f39c504\"\n", "or request_id = \"f5980cfa-0aa2-4e92-ac6e-04fd0c7b6a7e\"\n", "or request_id = \"347394ca-8c00-41b1-baf0-47fc03380e98\"\n", "or request_id = \"d61c0978-6e80-43d0-a514-5e6a993c3b34\"\n", "or request_id = \"b17a984c-72ba-4538-a1b7-b93b5d77c53d\"\n", "or request_id = \"78d3fe7b-b76e-47e9-be02-535af4955a76\"\n", "or request_id = \"81fa0d35-9007-4232-af7f-69634c5e1f38\"\n", "or request_id = \"6dd4f741-87b7-4617-b640-ff2adcc9127c\"\n", "or request_id = \"b488a662-9165-4929-9d79-c266ebd03ec8\"\n", "or request_id = \"97328eec-41ee-4b6d-bee1-53dcbebfd4fd\"\n", "or request_id = \"b3f8edcd-d213-47a8-997d-d9dafac67371\"\n", "or request_id = \"83d782cf-e46a-41f4-bb5b-a7a98e5aa5a6\"\n", "or request_id = \"9ba2d436-0063-4c0a-9526-6bbdfc31c9b0\"\n", "or request_id = \"f716a4bd-c21e-4458-906b-d55268d441b5\"\n", "or request_id = \"aaaf02c3-eb3b-4805-b7bb-0f977ea386fc\"\n", "or request_id = \"775b46a0-ff29-4d9b-a60d-9349a4276c43\"\n", "or request_id = \"cba388cb-c0e3-4840-83b5-585e17e5e4c1\"\n", "or request_id = \"d3a1589c-bd73-4027-b37f-cc18a38b903c\"\n", "or request_id = \"22686fb2-5dd0-4327-a9e6-1b672bff3a08\"\n", "or request_id = \"132945f6-e26f-460f-b5c1-6409906e09e0\"\n", "or request_id = \"e9a16740-8486-4969-a946-485482eefbf4\"\n", "or request_id = \"459fb31f-7734-46df-a2c7-46178f2a580c\"\n", "or request_id = \"bacb2f28-ca8e-4d0d-8445-9497b6f78071\"\n", "or request_id = \"f2668a06-ac55-4b00-9f81-ee69b4362020\"\n", "or request_id = \"c6a4be58-bd2b-4d1e-8ba1-5ecb6596e1ee\"\n", "or request_id = \"bdc172c7-8213-4761-8b70-8933c4134d7d\"\n", "or request_id = \"9e69b031-a166-4684-a3fa-817c9ca88214\"\n", "or request_id = \"313a1bf6-40cb-4ee5-a9cb-b81ce0722ebc\"\n", "or request_id = \"97fd1f40-bb16-4cd5-ad5d-bfd54a7ef974\"\n", "or request_id = \"de7787a3-eb48-4a40-bef9-99175d5d1213\"\n", "or request_id = \"398a89b4-f7b0-4a87-b287-618402e4241c\"\n", "or request_id = \"183f9afa-5e00-4c93-b99d-1e668f59a59e\"\n", "or request_id = \"51deee38-6a37-4e24-bf45-9880a51b4185\"\n", "or request_id = \"0ec03d3b-cb99-4231-856d-37b8459e9b7a\"\n", "or request_id = \"ec5f7e4e-c8a4-4d09-a196-fd5fca88dd8b\"\n", "or request_id = \"4eefff38-c4f4-474e-9b63-8649f18ae5a4\"\n", "or request_id = \"237c235d-d5b8-4e41-a4b3-35343c61023a\"\n", "or request_id = \"12d259bf-5c1d-4f3f-b4fb-37a34daf6bee\"\n", "or request_id = \"1c18c2d8-6920-4a92-bff0-d2824095d958\"\n", "or request_id = \"bda23042-1048-4019-b475-652dfd687d69\"\n", "or request_id = \"ef5930b1-a0cf-4948-8d71-09dcc98ef0e2\"\n", "or request_id = \"1a9a342b-6543-4f8f-b5ba-c8d54470c619\"\n", "or request_id = \"b7801791-ad20-4f9d-a382-6c026e200ff7\"\n", "or request_id = \"c94768b6-8c4a-4b0f-9400-8ce13b9e1211\"\n", "or request_id = \"6ad2a6fb-3d46-414b-8317-cdb19b03a741\"\n", "or request_id = \"15ffdbb6-6942-4ad3-9fc6-bc7dddfa2118\"\n", "or request_id = \"5813d47d-3146-4952-a178-bb51a076f6d3\"\n", "or request_id = \"15bde85e-4b9f-43c6-a0b0-e952625c4596\"\n", "or request_id = \"124a3f03-afaf-4a0d-a2e5-527551cb5606\"\n", "or request_id = \"0e66962b-ca98-46a6-9be5-b249d497ac23\"\n", "or request_id = \"3a188021-7b75-4bed-b50e-b7d9470f895f\"\n", "or request_id = \"8e0e17f6-2d74-44c7-929c-891b6fb223de\"\n", "or request_id = \"1b475538-80f5-48a9-9ef1-d93179856b7e\"\n", "or request_id = \"7ccd1853-8182-48be-9881-be541f09a6ab\"\n", "or request_id = \"e77ae584-feb1-4443-bd03-b51fc7ee5c12\"\n", "or request_id = \"38f001ff-a922-403f-aeca-54321e49f97b\"\n", "or request_id = \"6e1dd8c2-3dc7-4c17-920b-21b76b0979ac\"\n", "or request_id = \"3e15a2a1-c076-4c58-9295-7c2729b7a5b6\"\n", "or request_id = \"cc7e0ff0-3bed-45b7-9a10-d2d9306c948e\"\n", "or request_id = \"b07c7531-8f1b-4fc2-bbec-d6408c869e5c\"\n"]}], "source": ["\"\"\"\n", "SELECT \n", "  request_id,\n", "  time,\n", "  sanitized_json\n", "FROM `system-services-prod.us_prod_request_insight_analytics_dataset.completion_host_request`\n", "WHERE\n", "  time >= \"2024-09-16\"\n", "  and tenant = \"i0-vanguard0\"\n", "  and (\n", "    -- paste here\n", "  )\n", "\"\"\"\n", "\n", "# generate a list of request ids to paste into a bigquery request\n", "for i, c in enumerate(completions_subset[:1000]):\n", "    if i != 0:\n", "        print(\"or \", end=\"\")\n", "    print(f'request_id = \"{c[0]}\"')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# with open('./analytics-completion-requests-5.json') as f:\n", "with open(\"./analytics-completion-requests-909.json\") as f:\n", "    reqs = json.load(f)\n", "\n", "completion_req_data = []\n", "for req in reqs:\n", "    blobs = json.loads(req[\"sanitized_json\"])[\"blobs\"]\n", "    added = (\n", "        [base64.b64decode(blob).hex() for blob in blobs[\"added\"]]\n", "        if \"added\" in blobs\n", "        else []\n", "    )\n", "    checkpoint_id = blobs[\"baseline_checkpoint_id\"]\n", "    completion_req_data.append(\n", "        {\n", "            \"request_id\": req[\"request_id\"],\n", "            \"time\": req[\"time\"],\n", "            \"added\": added,\n", "            \"checkpoint_id\": checkpoint_id,\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(241, '5794da412a546f249774072b6de114249637223baa0c64cd32383cff4c72bdb7')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["checkpoint_names = set([b.name.split(\"/\")[-1] for b in checkpoints])\n", "len(checkpoint_names), list(checkpoint_names)[0]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["bucket = client.bucket(BUCKET)\n", "\n", "\n", "def get_checkpoint_blobs(checkpoint_id):\n", "    name = \"checkpoints/\" + checkpoint_id\n", "    return set(json.loads(bucket.get_blob(name).download_as_string().decode()))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["(357, 552)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# we consider 'all blobs present' if all added blobs are in the bucket, the\n", "# checkpoint (if present) is in the bucket, and all blobs included in the\n", "# checkpoint are in the bucket\n", "all_blobs = []\n", "not_all_blobs = []\n", "for data in completion_req_data:\n", "    blob_count = sum([1 for b in data[\"added\"] if b in blob_names])\n", "\n", "    if data[\"checkpoint_id\"]:\n", "        # check checkpoint id\n", "        if data[\"checkpoint_id\"] not in checkpoint_names:\n", "            not_all_blobs.append(data)\n", "            continue\n", "\n", "        # check checkpoint blobs\n", "        # TODO: this is inefficient\n", "        checkpoint_blobs = get_checkpoint_blobs(data[\"checkpoint_id\"])\n", "        checkpoint_count = sum([1 for b in checkpoint_blobs if b in blob_names])\n", "        if checkpoint_count != len(checkpoint_blobs):\n", "            not_all_blobs.append(data)\n", "            continue\n", "\n", "    # check added blobs\n", "    if blob_count == len(data[\"added\"]):\n", "        all_blobs.append(data)\n", "    else:\n", "        not_all_blobs.append(data)\n", "\n", "len(all_blobs), len(not_all_blobs)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def process_data(data):\n", "    dates = [parser.parse(item[\"time\"]).date() for item in data]\n", "    return Counter(dates)\n", "\n", "\n", "all_blobs_counts = process_data(all_blobs)\n", "not_all_blobs_counts = process_data(not_all_blobs)\n", "\n", "all_dates = sorted(set(all_blobs_counts.keys()) | set(not_all_blobs_counts.keys()))\n", "\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "x = np.arange(len(all_dates))\n", "width = 0.35\n", "\n", "all_counts = [all_blobs_counts.get(date, 0) for date in all_dates]\n", "not_all_counts = [not_all_blobs_counts.get(date, 0) for date in all_dates]\n", "\n", "ax.bar(\n", "    x - width / 2, all_counts, width, color=\"blue\", label=\"All Blobs Present in Bucket\"\n", ")\n", "ax.bar(\n", "    x + width / 2,\n", "    not_all_counts,\n", "    width,\n", "    color=\"red\",\n", "    label=\"Not All Blobs Present in Bucket\",\n", ")\n", "\n", "ax.set_title(\"All Blobs in GCS Bucket for Completion Requests\")\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(all_dates, rotation=45, ha=\"right\")\n", "ax.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "venv-8-7", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}
"""
Script with utilities for creating test data in a gcs bucket and verifying its
deletion.
"""

import random
from collections.abc import Iterator
from datetime import datetime, timezone
from tqdm import tqdm
import threading

from google.cloud import storage


BUCKET_NAME = "augment-blob-exporter-i0-prod"
# BUCKET_NAME = "augment-blob-exporter-dev-mpauly-dev"
TARGET_DATE = datetime(2024, 9, 16, tzinfo=timezone.utc)


def count_blobs(client):
    blobs: Iterator[storage.Blob] = client.list_blobs(BUCKET_NAME)

    n_total = 0
    n_delete = 0
    for blob in tqdm(blobs):
        n_total += 1
        if blob.time_created < TARGET_DATE:
            n_delete += 1

    print(f"bucket {BUCKET_NAME} target date {TARGET_DATE}")
    print(f"{n_delete} to delete {n_total} total")


def insert_fake(client, count=100, prefix="test/"):
    bucket = client.bucket(BUCKET_NAME)
    for _ in tqdm(range(count)):
        name = random.randbytes(20).hex()
        blob = bucket.blob(prefix + name)
        blob.upload_from_string("fake content")


def main():
    client = storage.Client()

    # add_count = 250000
    # threads = []
    # for _ in range(50):
    #     t = threading.Thread(target=insert_fake, args=(client, add_count // 50,))
    #     t.start()
    #     threads.append(t)
    # for t in threads:
    #     t.join()

    count_blobs(client)


if __name__ == "__main__":
    main()

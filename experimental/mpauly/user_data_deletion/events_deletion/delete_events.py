from typing import Iterable
from tqdm import tqdm

from google.cloud import storage


BUCKET_NAME = "us-prod-request-insight-events-nonenterprise"
PROJECT_ID = "system-services-prod"
TENANT_ID = "f71d27a356d306effe40005d7450d968"  # jotai

REQ_ID_FILE = "./david_jotai_request_ids.csv"
SESSION_ID = "b51dd4f0-d67b-47b0-8365-84267d590185"

with open(REQ_ID_FILE, "r") as f:
    req_ids = f.read().splitlines()[1:]

client = storage.Client(PROJECT_ID)
bucket = client.bucket(BUCKET_NAME)


# Get request events
print(f"Getting events for {len(req_ids)} requests")

request_events: list[storage.Blob] = []
for req_id in tqdm(req_ids):
    prefix = f"{TENANT_ID}/request/{req_id}"
    request_events.extend(bucket.list_blobs(prefix=prefix))

print(f"{len(request_events)} request events to delete")


# Delete session events
print(f"Getting events for session id {SESSION_ID}")

prefix = f"{TENANT_ID}/session/{SESSION_ID}"
session_events: list[storage.Blob] = list(bucket.list_blobs(prefix=prefix))

print(f"{len(session_events)} session events to delete")


# Delete the blobs
# for blob in request_events + session_events:
#     bucket.delete_blob(blob.name)

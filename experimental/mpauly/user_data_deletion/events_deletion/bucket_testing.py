"""Script for creating test request data in a gcs bucket and verifying its deletion."""

import random
from collections.abc import Iterator
from tqdm import tqdm
import threading
import os

from google.cloud import storage


BUCKET_EVENTS = "dev-mpauly-request-insight-events"
BUCKET_BLOBS = "augment-blob-exporter-dev-mpauly-dev"
TEST_TENANT_ID = "test"


def count_blobs(client: storage.Client):
    blobs: Iterator[storage.Blob] = client.list_blobs(BUCKET_EVENTS, prefix="test/")
    count = sum(1 for _ in blobs)
    print(f"Found {count} blobs")


# Create fake request/session events
def insert_events(
    client: storage.Client,
    n_requests: int,
    n_per_request: int,
    event_type: str = "request",
    tenant_id=TEST_TENANT_ID,
):
    bucket = client.bucket(BUCKET_EVENTS)
    for i in tqdm(range(n_requests)):
        for _ in range(n_per_request):
            name = random.randbytes(20).hex()
            blob = bucket.blob(f"{tenant_id}/{event_type}/{i+1}/{name}")
            blob.upload_from_string("fake content")


def insert_blobs(client: storage.Client, n_blobs: int):
    bucket = client.bucket(BUCKET_BLOBS)
    for i in tqdm(range(n_blobs)):
        blob = bucket.blob(f"blobs/{i}")
        blob.upload_from_string("fake content")


def main():
    client = storage.Client()

    # add_count = 100
    # n_threads = 10
    # threads = []
    # for _ in range(n_threads):
    #     t = threading.Thread(target=insert_fake, args=(client, add_count // n_threads,))
    #     t.start()
    #     threads.append(t)
    # for t in threads:
    #     t.join()

    # n_requests = 5
    # n_per_request = 10
    # insert_events(client, n_requests, n_per_request)
    # with open(os.path.expanduser("~/request_ids.txt"), "w") as f:
    #     for i in range(n_requests):
    #         f.write(f"{i+1}\n")

    n_sessions = 1
    n_per_session = 200
    insert_events(client, n_sessions, n_per_session, event_type="session")
    with open(os.path.expanduser("~/session_ids.txt"), "w") as f:
        for i in range(n_sessions):
            f.write(f"{i+1}\n")

    count_blobs(client)

    # n_blobs = 10
    # insert_blobs(client, n_blobs)
    # with open(os.path.expanduser("~/blob_names.txt"), "w") as f:
    #     for i in range(n_blobs):
    #         f.write(f"{i}\n")


if __name__ == "__main__":
    main()

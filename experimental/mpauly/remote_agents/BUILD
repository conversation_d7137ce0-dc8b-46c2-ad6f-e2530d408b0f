load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "query_remote_agent_mgmt",
    srcs = ["query_remote_agent_mgmt.py"],
    deps = [
        "//base/logging:console_logging",
        requirement("google-cloud-bigquery"),
    ],
)

py_binary(
    name = "query_user_by_email",
    srcs = ["query_user_by_email.py"],
    deps = [
        "//base/logging:console_logging",
        requirement("google-cloud-bigquery"),
    ],
)

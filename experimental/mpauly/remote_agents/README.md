# Remote Agents Tools

## `query_remote_agent_mgmt`

This tool queries the BigQuery `us_cross_env_request_insight_analytics_dataset` dataset for create,
chat, interrupt, and delete requests related to the specified agent ID, displaying results in a
formatted table.

Example usage:

```
$ bazel run //experimental/mpauly/remote_agents:query_remote_agent_mgmt -- 9a6e8e0e-a47c-584b-b773-485bfd2952a5

Request Time                  Request Type       Request ID
---------------------------------------------------------------------------------------
2025-05-06 10:30:39.335945    delete             8a044632-d5ac-44e1-b876-ccef61fbfab9
2025-05-05 09:31:41.030887    interrupt          c72bffba-2bd2-4ef5-811b-9de3382c58ca
2025-05-05 09:31:40.896680    interrupt          bc092398-e594-4810-b575-892620204bb8
2025-05-05 09:31:40.729167    interrupt          4329aa7b-a5e0-4c7a-b4ba-bf062a851525
```

"""
Command-line tool for querying remote agent management data from BigQuery.

This script queries the BigQuery analytics dataset for information about remote
agents with a specific agent ID, including create, chat, interrupt, and delete
requests.
"""

import argparse
from typing import List

from google.cloud import bigquery

from base.logging.console_logging import setup_console_logging


TABLE_SEPARATOR = "    "

QUERY = """
WITH
create_responses AS (
  SELECT
    'create' AS request_type,
    request_id,
    agent_id,
    time,
    sanitized_json,
    tenant
  FROM
    `system-services-prod.us_cross_env_request_insight_analytics_dataset.remote_agents_create_response`
  WHERE
    agent_id = @agent_id
),
chat_requests AS (
  SELECT
    'chat' AS request_type,
    request_id,
    agent_id,
    time,
    sanitized_json,
    tenant
  FROM
    `system-services-prod.us_cross_env_request_insight_analytics_dataset.remote_agents_chat_request`
  WHERE
    agent_id = @agent_id
),
interrupt_requests AS (
  SELECT
    'interrupt' AS request_type,
    request_id,
    agent_id,
    time,
    sanitized_json,
    tenant
  FROM
    `system-services-prod.us_cross_env_request_insight_analytics_dataset.remote_agents_interrupt_request`
  WHERE
    agent_id = @agent_id
),
delete_requests AS (
  SELECT
    'delete' AS request_type,
    request_id,
    agent_id,
    time,
    sanitized_json,
    tenant
  FROM
    `system-services-prod.us_cross_env_request_insight_analytics_dataset.remote_agents_delete_request`
  WHERE
    agent_id = @agent_id
),
all_requests AS (
  SELECT * FROM create_responses
  UNION ALL
  SELECT * FROM chat_requests
  UNION ALL
  SELECT * FROM interrupt_requests
  UNION ALL
  SELECT * FROM delete_requests
),
filtered_metadata AS (
  SELECT *
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
  WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
)

SELECT
  DATETIME(m.time, 'America/Los_Angeles') AS request_time,
  r.request_id,
  r.request_type,
  m.user_id,
  r.tenant,
  r.agent_id,
  r.sanitized_json,
  m.time,
  m.shard_namespace
FROM
  all_requests r
LEFT JOIN
  filtered_metadata m
ON
  r.request_id = m.request_id
ORDER BY
  m.time DESC
LIMIT @limit
"""


def get_support_url(namespace: str, tenant: str, request_id: str) -> str:
    return f"https://support.{namespace}.t.us-central1.prod.augmentcode.com/t/{tenant}/request/{request_id}#tab=Summary"


def query_remote_agents(agent_id: str, limit: int = 100) -> List[bigquery.Row]:
    """
    Query BigQuery for remote agent data with the specified agent ID.

    Args:
        agent_id: The ID of the agent to query for
        limit: Maximum number of results to return (default: 100)

    Returns:
        List of BigQuery Row objects containing the query results
    """
    client = bigquery.Client(project="system-services-prod")
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("agent_id", "STRING", agent_id),
            bigquery.ScalarQueryParameter("limit", "INT64", limit),
        ]
    )

    query_job = client.query(QUERY, job_config=job_config)
    results = query_job.result()
    return list(results)


def format_results(results: List[bigquery.Row]) -> None:
    """
    Format and print the query results in a table format.

    Args:
        results: List of BigQuery Row objects to format and print
    """
    if not results:
        print("No results found for the specified agent ID.")
        return

    # Define table headers and column widths
    headers = ["Request Time", "Request Type", "Request ID", "Support URL"]
    col_widths = [26, 15, 38, 50]

    # Print table header
    print()
    header_row = TABLE_SEPARATOR.join(h.ljust(w) for h, w in zip(headers, col_widths))
    print(header_row)
    print("-" * len(header_row))

    # Print table rows
    for row in results:
        # Format each row
        columns = [
            str(row.request_time),
            row.request_type,
            row.request_id,
            get_support_url(row.shard_namespace, row.tenant, row.request_id),
        ]

        # Truncate long values and ensure proper alignment
        truncated_columns = []
        for i, (col, width) in enumerate(zip(columns, col_widths)):
            if i == len(columns) - 1:
                # Don't truncate the last column (the support URL)
                truncated = col
            else:
                if len(col) > width:
                    truncated = col[: width - 3] + "..."
                else:
                    truncated = col.ljust(width)
            truncated_columns.append(truncated)

        print(TABLE_SEPARATOR.join(truncated_columns))

    print()
    print(f"Agent ID: {results[0].agent_id}")
    print(f"User ID: {results[0].user_id}")
    print(f"Tenant: {results[0].tenant}")


def main():
    setup_console_logging()

    parser = argparse.ArgumentParser(
        description="Query BigQuery for remote agent management API requests"
    )
    parser.add_argument(
        "agent_id",
        type=str,
        help="The ID of the remote agent to query for",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=100,
        help="Maximum number of results to return (default: 100)",
    )
    args = parser.parse_args()

    results = query_remote_agents(args.agent_id, args.limit)
    format_results(results)


if __name__ == "__main__":
    main()

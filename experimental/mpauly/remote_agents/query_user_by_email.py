"""
Command-line tool for querying user ID from BigQuery using an email address.

This script queries the BigQuery analytics dataset for a user ID based on the
provided email address. It can either query a single email or process a file
containing multiple emails.

Note that this tool requires the PII access flag to be set in eng.jsonnet.
"""

import argparse
import os
from typing import Optional

from google.cloud import bigquery

from base.logging.console_logging import setup_console_logging


QUERY = """
SELECT
  id,
  email
FROM
  `system-services-prod.us_prod_request_insight_analytics_dataset.user`
WHERE
  email = @email
"""


def query_user_by_email(email: str) -> Optional[bigquery.Row]:
    """
    Query BigQuery for a user ID based on the provided email address.

    Args:
        email: The email address of the user to query for

    Returns:
        BigQuery Row object containing the query result, or None if no user found

    Raises:
        ValueError: If multiple users are found for the same email
    """
    client = bigquery.Client(project="system-services-prod")
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("email", "STRING", email.lower()),
        ]
    )

    query_job = client.query(QUERY, job_config=job_config)
    results = list(query_job.result())

    if not results:
        return None

    if len(results) > 1:
        raise ValueError(f"Multiple users found for {email}: {len(results)} results")

    return results[0]


def process_email_file(input_file: str) -> str:
    """
    Process a file containing email addresses and return user IDs.

    Args:
        input_file: Path to the file containing email addresses (one per line)

    Returns:
        output_file: Path to the output file containing user IDs (one per line)
    """
    # Convert to absolute path if not already
    input_file = os.path.abspath(input_file)
    output_file = f"{input_file}.out"

    with open(input_file, "r") as f:
        emails = [line.strip() for line in f if line.strip()]

    if not emails:
        raise ValueError(f"No email addresses found in {input_file}")

    print(f"Processing {len(emails)} emails...")
    with open(output_file, "w") as f:
        for email in emails:
            row = query_user_by_email(email)
            if row is not None:
                user_id = row.id
                print(f"Found user ID {user_id} for {email}")
                f.write(f"{user_id}\n")
            else:
                print(f"Warning: No user ID found for {email}")
                f.write(f"# No user ID found for {email}\n")

    return output_file


def main():
    setup_console_logging()

    parser = argparse.ArgumentParser(
        description="Query BigQuery for a user ID based on email address"
    )

    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        "-e",
        "--email",
        type=str,
        help="The email address of the user to query for",
    )
    group.add_argument(
        "-f",
        "--file",
        type=str,
        help="Path to a file containing email addresses (one per line)",
    )

    args = parser.parse_args()

    if args.email:
        result = query_user_by_email(args.email)
        if result is None:
            raise ValueError(
                f"No user found for email: {args.email}. Do you have the PII access flag enabled in eng.jsonnet?"
            )
        print(result.id)
    elif args.file:
        output_file = process_email_file(args.file)
        print(f"Results written to {output_file}")
    else:
        raise ValueError("Must specify either --email or --file")


if __name__ == "__main__":
    main()

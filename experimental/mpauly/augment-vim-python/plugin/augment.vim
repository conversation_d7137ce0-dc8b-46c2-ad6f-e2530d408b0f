let s:command = './run_ls.sh'
let s:plugin_path = expand('<sfile>:p:h')
let s:dist_path = s:plugin_path . '/../dist'
let s:log_path = s:dist_path . '/logs'

if !isdirectory(s:log_path)
    call mkdir(s:log_path, "p")
endif

" global request tracking
let g:rid = 1
let g:request_types = {}
let g:awaiting_completion = 0
let g:completion_text = ''

hi def AugmentSuggestionHighlight guifg=#808080 ctermfg=12
call prop_type_add('AugmentSuggestion', {'highlight': 'AugmentSuggestionHighlight'})

function! s:ResetCompletionState()
    let g:awaiting_completion = 0
    call prop_remove({'type': 'AugmentSuggestion'})
endfunction

" TODO: when does the editor call this? thinking about what we need to consider for concurrency
function! s:OnMessage(channel, msg) abort
    let id = a:msg.id
    let msg_type = g:request_types[id]

    " TODO: implement actual logging
    echom 'type=' . msg_type . ', message=' . string(a:msg)

    if has_key(a:msg, 'error')
        return
    endif

    if msg_type == 'initialize'
      call s:SendNotification('initialized', {}, v:false)
    endif

    if msg_type == 'textDocument/completion'
        " let insert_text = a:msg.result.items[0].insertText
        " let label = a:msg.result.items[0].label
        " TODO: for some reason the format is different for ts

        if empty(a:msg.result)
            return
        endif

        let insert_text = a:msg.result[0].insertText
        let label = a:msg.result[0].label

        echom 'completion request_id: ' . label

        if g:awaiting_completion == id
            call s:ResetCompletionState()

            " call prop_add(line('.'), col('.'), {'type': 'AugmentSuggestion', 'text': insert_text})
            let g:completion_text = insert_text

            " Split text into lines and add properties for each line
            let lines = split(insert_text, '\n', 1)
            let cur_line = line('.')
            let cur_col = col('.')

            " Add property for first line starting at cursor position
            call prop_add(cur_line, cur_col, {'type': 'AugmentSuggestion', 'text': lines[0]})

            " Add properties for subsequent lines starting at column 1
            for line_text in lines[1:]
                let cur_line += 1
                call prop_add(cur_line, 1, {'type': 'AugmentSuggestion', 'text': line_text})
            endfor
        endif
    endif

    if msg_type == 'augment/chat'
        let text = a:msg.result.text

        echom 'chat request_id: ' . a:msg.result.request_id

        " Put the chat response text in a new buffer on the right
        botright vnew
        setlocal buftype=nofile
        setlocal bufhidden=hide
        setlocal noswapfile
        setlocal wrap
        setlocal linebreak
        execute 'file AugmentChat-' . strftime("%Y%m%d-%H%M%S")
        call setline(1, split(text, '\n'))
        setlocal readonly

        " TODO: stream instead?
    endif
endfunction

function! s:OnExit(channel, msg)
    echom "augment language server exited"
endfunction

function! s:IsLanguageServerRunning()
    return exists('g:augment_job') && job_status(g:augment_job) == 'run'
endfunction

function! s:SendNotification(method, params, is_request)
    if !s:IsLanguageServerRunning()
        call s:StartLanguageServer()
    endif

    if a:is_request
      let l:msg = {
          \ 'jsonrpc': '2.0',
          \ 'id': g:rid,
          \ 'method': a:method,
          \ 'params': a:params
          \ }
      call ch_sendexpr(job_getchannel(g:augment_job), l:msg)

      let g:request_types[g:rid] = a:method
      if a:method == 'textDocument/completion'
          let g:awaiting_completion = g:rid
      endif
      let g:rid += 1
    else
      let l:msg = {
          \ 'jsonrpc': '2.0',
          \ 'method': a:method,
          \ 'params': a:params
          \ }
      call ch_sendexpr(job_getchannel(g:augment_job), l:msg)
    endif

endfunction

function! s:StartLanguageServer()
    if s:IsLanguageServerRunning()
        return
    endif

    " echom 'starting job: ' .. s:command .. ' at ' .. s:dist_path

    let job = job_start(s:command, {
        \ 'cwd': s:dist_path,
        \ 'noblock': 1,
        \ 'stoponexit': '',
        \ 'in_mode': 'lsp',
        \ 'out_mode': 'lsp',
        \ 'out_cb': function('s:OnMessage'),
        \ 'exit_cb': function('s:OnExit'),
        \ })
    " TODO: err_cb?

    let g:augment_job = job

    call s:SendNotification('initialize', {'processId': getpid(), 'capabilities': {}}, v:true)
endfunction

function! s:NotifyDidOpen()
    " TODO: is any of this correct?
    let l:uri = 'file://' . expand('%:p')
    let l:text = join(getline(1, '$'), "\n")
    let l:params = {
        \ 'textDocument': {
        \     'languageId': &filetype,
        \     'version': 1,
        \     'uri': l:uri,
        \     'text': l:text,
        \ }
    \ }
    call s:SendNotification('textDocument/didOpen', l:params, v:false)
endfunction

function! s:NotifyDidChange()
    " TODO: proper version number tracking
    " TODO: is this the correct way to specify uri and text?
    let l:uri = 'file://' . expand('%:p')
    let l:text = join(getline(1, '$'), "\n")
    let l:params = {
        \ 'textDocument': {
        \     'uri': l:uri,
        \     'version': 1,
        \ },
        \ 'contentChanges': [{'text': l:text}],
    \ }
    call s:SendNotification('textDocument/didChange', l:params, v:false)

    " TODO: this should be moved to insert mode keypress
    call s:RequestCompletion()
endfunction

function! s:RequestCompletion()
    call s:ResetCompletionState()
    let l:params = {
        \ 'textDocument': {
        \     'uri': 'file://' . expand('%:p'),
        \ },
        \ 'position': {
        \     'line': line('.') - 1,
        \     'character': col('.') - 1,
        \ }
    \ }
    call s:SendNotification('textDocument/completion', l:params, v:true)
endfunction

function! s:SendChatRequest(message)
    if !s:IsLanguageServerRunning()
        call s:StartLanguageServer()
    endif

    let l:params = {
        \ 'textDocument': {
        \     'uri': 'file://' . expand('%:p'),
        \ },
        \ 'message': a:message,
        \ 'position': {
        \     'line': line('.') - 1,
        \     'character': col('.') - 1,
        \ }
    \ }

    call s:SendNotification('augment/chat', l:params, v:true)
endfunction

" TODO: this is probably not the best way to do this, should be inserting the text directly
function! s:AcceptSuggestion()
    let props = prop_list(line('.'), {'col': col('.'), 'types': ['AugmentSuggestion']})

    " TODO: this is so hacky
    if len(props) > 0
        let lines = split(g:completion_text, '\n', 1)
        let cmd = lines[0]
        for line in lines[1:]
            " <C-u> removes any auto-indentation that vim adds
            let cmd .= "\<CR>\<C-u>" . line
        endfor

        call s:ResetCompletionState()
        return cmd
    endif

    " TODO:

    return "\<Tab>"
endfunction

augroup AugmentLSP
    autocmd!
    autocmd VimEnter * call s:StartLanguageServer()
    autocmd BufNewFile,BufRead * call s:NotifyDidOpen()
    autocmd TextChanged,TextChangedI * call s:NotifyDidChange()
    autocmd CursorMoved * call s:ResetCompletionState()
    autocmd InsertEnter * call s:RequestCompletion()
    " TODO: why does it take a little bit of time for the ghost text to go away when hitting escape?
    autocmd InsertLeave * call s:ResetCompletionState()
augroup END

command! AugmentCompletion call s:RequestCompletion()
inoremap <C-y> <C-o>:AugmentCompletion<CR>
inoremap <expr> <Tab> <SID>AcceptSuggestion()
command! -nargs=1 AugmentChat call s:SendChatRequest(<q-args>)
nnoremap <Leader>c :AugmentChat

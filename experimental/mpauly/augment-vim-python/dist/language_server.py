import logging
from pathlib import Path
import uuid
import os

from pygls.server import LanguageServer
from lsprotocol import types

from client import AugmentClient


TOKEN_FILE = ".config/augment/api_token"
API_ENDPOINT = "https://staging-shard-0.api.augmentcode.com/"
USER_AGENT = "Augment.vim/0.1"
SESSION_ID = "6ba938f0-5242-4f79-a90d-473f6b586fb7"  # uuid.uuid4()
LOG_FILE = os.path.expanduser("~/augment-lsp.log")

# setup language server
server = LanguageServer("augment-lsp", "v0.1")

# setup augment client
api_token = (Path.home() / TOKEN_FILE).read_text().strip()

client = AugmentClient(
    url=API_ENDPOINT,
    token=api_token,
    user_agent=USER_AGENT,
    session_id=uuid.UUID(SESSION_ID),
)
model_client = client.client_for_model("")


@server.thread()
@server.feature(types.TEXT_DOCUMENT_COMPLETION)
def completion(ls: LanguageServer, params: types.CompletionParams):
    # TODO: we should be processing the skip tokens and returning a text edit rather than insert text
    # https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#textDocument_completion

    doc = ls.workspace.get_document(params.text_document.uri)
    content = doc.source
    pos = params.position
    offset = doc.offset_at_position(pos)
    prefix = content[:offset]
    suffix = content[offset:]

    response = model_client.complete(
        prompt=prefix,
        path=doc.path,
        suffix=suffix,
    )
    insert_text = response.text

    return types.CompletionList(
        items=[
            types.CompletionItem(
                label=str(response.request_id),
                insert_text=insert_text,
            ),
        ],
        is_incomplete=True,
    )

@server.feature("augment/chat")
# TODO: figure out types for input and output
def chat(ls: LanguageServer, params):
    # logging.debug(f'type {type(params)} params {params}')
    # 2024-11-01 12:33:31 [DEBUG] type <class 'pygls.protocol.Object'> params Object(message='a', textDocument=Object(uri='file://'), position=Object(character=0, line=0))

    message = params.message
    pos = params.position
    try:
        doc = ls.workspace.get_document(params.textDocument.uri)
        content = doc.source
        offset = doc.offset_at_position(pos)
        path = doc.path
    except:
        content = ""
        offset = 0
        path = ""
    prefix = content[:offset]
    suffix = content[offset:]

    # TODO: streaming
    chat_request = {
        "selected_code": "",
        "message": message,
        "prefix": prefix,
        "suffix": suffix,
        "path": path,
    }
    chat_response = model_client.chat(**chat_request)
    # logging.debug(f'chat request: {chat_request}')
    # logging.debug(f'chat_response: {chat_response}')

    response_message = chat_response.text
    request_id = chat_response.request_id

    response = {
        "text": response_message,
        "request_id": request_id,
    }
    # logging.debug(f'message {message}, response {response}')
    return response

if __name__ == "__main__":
    logging.basicConfig(
        filename=LOG_FILE,
        filemode='a',
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    server.start_io()

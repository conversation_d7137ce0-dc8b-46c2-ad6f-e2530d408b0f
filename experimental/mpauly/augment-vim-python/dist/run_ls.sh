#!/usr/bin/env bash

# PYTHON
# . venv/bin/activate
# python3 language_server.py

# GO
# /home/<USER>/.vim/pack/augment/start/lsp/dist/augment_lsp

# TYPESCRIPT
node /home/<USER>/repos/augment-mpauly-vim/experimental/mpauly/ts-augment-server/dist/server.js --stdio

# DENO
# deno run --allow-env /home/<USER>/repos/augment-mpauly-vim/experimental/mpauly/ts-augment-server/dist/server.cjs --stdio

# DENO COMPILED
# /home/<USER>/repos/augment-mpauly-vim/experimental/mpauly/ts-augment-server/dist/server --stdio

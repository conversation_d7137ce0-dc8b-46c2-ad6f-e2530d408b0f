# pylint: disable=protected-access
"""Client for the external augment API."""

from __future__ import annotations

import json
import logging
import time
import urllib.parse
import uuid
from datetime import datetime
from dataclasses import asdict, dataclass
from typing import Any, Iterable, Sequence, Tuple, TypeAlias

import requests
from dateutil.parser import parse
# from services.api_proxy import public_api_pb2
import public_api_pb2
from google.protobuf.json_format import ParseDict

GetModelsResponse: TypeAlias = public_api_pb2.GetModelsResponse
FeatureFlags: TypeAlias = public_api_pb2.GetModelsResponse.FeatureFlags
Language: TypeAlias = public_api_pb2.Language
Model: TypeAlias = public_api_pb2.Model


def sec(dt: datetime):
    return int((dt - datetime(1970, 1, 1, tzinfo=dt.tzinfo)).total_seconds())


def nsec(dt: datetime):
    return dt.microsecond * 1000


class ClientException(Exception):
    """Exception thrown if a request with the augment client failed."""

    def __init__(self, request_id: uuid.UUID, response: requests.Response):
        self.request_id = request_id
        self.response = response

    def is_client_error(self) -> bool:
        """Returns true if and only if the HTTP status code indicates a client error.

        Client error here refers to error where the client provided invalid arguments or other
        cases where the error can go away by making different requests.
        """
        return self.response.status_code >= 400 and self.response.status_code < 500


@dataclass
class CompletionItem:
    """One option provided to VSCode, containing text and associated data."""

    text: str
    skipped_suffix: str
    suffix_replacement_text: str
    filter_score: float | None = None


@dataclass
class CompleteResponse:
    """Class returned by a completion call."""

    text: str
    completion_items: list[CompletionItem]
    unknown_memory_names: list[str]
    request_id: uuid.UUID
    checkpoint_not_found: bool
    suggested_prefix_char_count: int
    suggested_suffix_char_count: int


@dataclass
class EditResponse:
    """Class returned by an edit call."""

    request_id: str
    """Request id for the edit request."""

    text: str
    """The text to replace the selected region."""

    unknown_blob_names: list[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool
    """True if the blobs checkpoint id is not available."""


@dataclass
class IncorporatedExternalSource:
    # name of the source that was used.
    source_name: str

    # The link to the external source.
    link: str | None = None


@dataclass
class WorkspaceFileChunk:
    char_start: int
    char_end: int
    blob_name: str


@dataclass
class ChatResponse:
    """Class returned by an chat call."""

    request_id: str
    """Request id for the chat request."""

    text: str
    """The text to replace the selected region."""

    unknown_blob_names: list[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool
    """True if the blobs checkpoint id is not available."""

    workspace_file_chunks: list[WorkspaceFileChunk] | None = None

    incorporated_external_sources: list[IncorporatedExternalSource] | None = None


@dataclass
class NextEditResponse:
    """Class returned by a next edit call."""

    request_id: str
    """Request id for the next edit request."""

    next_edit: dict[str, Any] | None = None
    """The result of the next edit model."""

    unknown_blob_names: list[str] | None = None
    """List of memory names not known to the server."""

    checkpoint_not_found: bool = False
    """Whether the blob checkpoint was not found."""


@dataclass
class VCSChange:
    """Class containing information about a VCS change."""

    working_directory_changes: list[
        Any
    ]  # leaving unspecified for now. (not programmatically using it)
    commits: list[Any]


@dataclass
class FindMissingResponse:
    """Class returned by a find_missing call."""

    unknown_memory_names: list[str]
    nonindexed_blob_names: list[str]


@dataclass
class CompletionResolution:
    """Class that describes the resolution (acceptance or rejection) of a code completion."""

    request_id: str
    emit_time_sec: int
    emit_time_nsec: int
    resolve_time_sec: int
    resolve_time_nsec: int
    accepted_idx: int


@dataclass(frozen=True)
class UploadContent:
    """Class that describes the content to be memorized."""

    content: str
    path_name: str


@dataclass(frozen=True)
class BlobsJson:
    """Describes the blob names using a starting checkpoint id and delta."""

    checkpoint_id: str | None
    added_blobs: Sequence[str]
    deleted_blobs: Sequence[str]

    def sorted(self) -> BlobsJson:
        """Returns a copy of this object with the added and deleted blobs sorted."""
        return BlobsJson(
            self.checkpoint_id,
            sorted(self.added_blobs),
            sorted(self.deleted_blobs),
        )


@dataclass(frozen=True)
class CheckpointBlobsRequest:
    """Class that describes the blob names to be checkpointed."""

    blobs: BlobsJson


@dataclass(frozen=True)
class CheckpointBlobsResponse:
    """Response to a checkpoint request with the new checkpoint id."""

    new_checkpoint_id: str


@dataclass(frozen=True)
class Exchange:
    """Describes an exchange between the user and the model (a turn in a conversation)."""

    request_message: str
    response_text: str | None
    request_id: uuid.UUID | None = None


@dataclass(frozen=True)
class ExternalSource:
    id: str
    name: str
    title: str
    source_type: str


@dataclass(frozen=True)
class SaveChatResponse:
    uuid: str
    url: str


# TODO(AU-4886): Consider using dataclass_json with original Exchange
@dataclass(frozen=True)
class ExchangeJson:
    """An intermediate representation of an exchange, useful for json encoding."""

    request_message: str
    response_text: str | None
    request_id: str | None

    def to_exchange(self) -> Exchange:
        return Exchange(
            request_message=self.request_message,
            response_text=self.response_text,
            request_id=uuid.UUID(self.request_id) if self.request_id else None,
        )

    @staticmethod
    def from_exchange(exchange: Exchange) -> ExchangeJson:
        return ExchangeJson(
            request_message=exchange.request_message,
            response_text=exchange.response_text,
            request_id=str(exchange.request_id) if exchange.request_id else None,
        )


@dataclass(frozen=True)
class Retry:
    """Class that describes the retry policy."""

    # Number of times to retry if the first request fails.
    retry_count: int

    # Time to sleep between retries in seconds.
    retry_sleep: float


class AugmentClient:
    """Python client to issue requests against an Augment API endpoint."""

    def __init__(
        self,
        url: str,
        token: str,
        timeout: int = 60,
        retry_count: int = 2,
        retry_sleep: float = 0.1,
        session_id: uuid.UUID | None = None,
        user_agent: str | None = None,
        verify: bool = True,
    ):
        """Initialize the client.

        Args:
            url: The URL of the Augment API.
            token: The API token to use for authentication.
            timeout: The timeout for requests to the remote system in seconds.
            retry_count: Number of times to retry if the first request fails.
            retry_sleep: Time to sleep between retries in seconds.
            session_id: The session id to use for requests.
            user_agent: The user agent to use for requests to the remote system.
            verify: Whether to verify the SSL certificate. Never set this to False for production.
        """

        self.url = url
        self.token = token
        self.timeout = timeout
        self.retry_count = retry_count
        self.retry_sleep = retry_sleep
        self.request_session_id = session_id if session_id else uuid.uuid4()
        self.last_request_id: uuid.UUID | None = None
        # see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent
        self.user_agent = user_agent if user_agent else "api_proxy_client/0 (Python)"
        self.verify = verify

    def __repr__(self):
        return f"AugmentClient(url={self.url}, request_session_id={self.request_session_id}, last_request_id={self.last_request_id})"

    def _post(
        self,
        url_suffix: str,
        json: Any,
        retry_policy: Retry | None = None,
        headers: dict[str, str] | None = None,
        timeout: int | None = None,
    ) -> Tuple[requests.Response, uuid.UUID]:
        """Post with build-in retry loop to hide small service outages."""
        if not timeout:
            timeout = self.timeout

        url = urllib.parse.urljoin(self.url, url_suffix)
        response = None
        request_id = None

        retry_policy = (
            retry_policy if retry_policy else Retry(self.retry_count, self.retry_sleep)
        )
        for retry in range(retry_policy.retry_count + 1):
            try:
                if "request_id" in json:
                    request_id = json["request_id"]
                else:
                    request_id = uuid.uuid4()
                logging.debug(
                    "Posting to %s request id %s retry %d",
                    url_suffix,
                    request_id,
                    retry,
                )

                self.last_request_id = request_id
                request_headers = {
                    "authorization": f"Bearer {self.token}",
                    "x-request-id": str(request_id),
                    "x-request-session-id": str(self.request_session_id),
                    "user-agent": self.user_agent,
                    "x-api-version": "1",  # see ApiVersion in services/api_proxy/public_api.proto
                }
                if headers:
                    request_headers.update(headers)
                response = requests.post(
                    url,
                    json=json,
                    headers=request_headers,
                    timeout=timeout,
                    verify=self.verify,
                )
                if str(response.status_code).startswith("5"):
                    time.sleep(retry_policy.retry_sleep)
                    continue
                return (response, request_id)
            except requests.exceptions.SSLError:
                time.sleep(self.retry_sleep)
                continue
        # return the last response we got
        assert response is not None
        assert request_id is not None
        return (response, request_id)

    def _post_stream(
        self,
        url_suffix: str,
        json: Any,
        headers: dict[str, str] | None = None,
        timeout: int | None = None,
    ) -> Tuple[requests.Response, uuid.UUID]:
        if not timeout:
            timeout = self.timeout

        url = urllib.parse.urljoin(self.url, url_suffix)
        response = None
        request_id = None

        if "request_id" in json:
            request_id = json["request_id"]
        else:
            request_id = uuid.uuid4()
        logging.debug(
            "Posting to %s request id %s",
            url_suffix,
            request_id,
        )

        self.last_request_id = request_id
        request_headers = {
            "accept": "text/event-stream",
            "authorization": f"Bearer {self.token}",
            "x-request-id": str(request_id),
            "x-request-session-id": str(self.request_session_id),
            "user-agent": self.user_agent,
            "x-api-version": "1",  # see ApiVersion in services/api_proxy/public_api.proto
        }
        if headers:
            request_headers.update(headers)
        response = requests.post(
            url,
            json=json,
            headers=request_headers,
            timeout=timeout,
            stream=True,
            verify=self.verify,
        )
        return (response, request_id)

    def memorize(
        self,
        content: str,
        path: str,
        blob_name: str | None = None,
        retry_policy: Retry | None = None,
    ) -> str:
        """Memorizes the content and returns the matching object name.

        memorize and the /memorize endpoint should not longer be used.
        """
        data = {"model": "", "t": content, "path": path}
        if blob_name:
            data["blob_name"] = blob_name
        response, request_id = self._post(
            "memorize", json=data, retry_policy=retry_policy
        )
        logging.debug("Memorize finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "mem_object_name" in j
        return j["mem_object_name"]

    def batch_upload(
        self, blobs: Sequence[UploadContent], retry_policy: Retry | None = None
    ) -> list[str]:
        """Uploads the contents and returns the matching blob names."""
        data = {
            "blobs": [
                {"path": blob.path_name, "content": blob.content} for blob in blobs
            ]
        }
        response, request_id = self._post(
            "batch-upload", json=data, retry_policy=retry_policy
        )
        logging.debug("Upload finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "blob_names" in j
        return j["blob_names"]

    def find_missing(
        self,
        model_name: str,
        memory_object_names: Sequence[str],
        retry_policy: Retry | None = None,
    ) -> FindMissingResponse:
        """Find missing memories.

        Returns a 2-element tuple:
          0: unknown_memory_names: list of blob (memory) names that are completely unknown
          1: nonindexed_blob_names: list of blob names that aren't indexed for the given model
        """
        response, request_id = self._post(
            "find-missing",
            json={"model": model_name, "mem_object_names": memory_object_names},
            retry_policy=retry_policy,
        )
        logging.debug("FindMissing finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return FindMissingResponse(
            unknown_memory_names=j["unknown_memory_names"],
            nonindexed_blob_names=j["nonindexed_blob_names"],
        )

    def get_models(
        self, retry_policy: Retry | None = None
    ) -> public_api_pb2.GetModelsResponse:
        """Returns the list of all supported models.

        The models might not be ready at any given point, so calls
        might return 503.
        """
        response, request_id = self._post(
            "get-models", json={}, retry_policy=retry_policy
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return ParseDict(j, public_api_pb2.GetModelsResponse())

    def resolve_completions(
        self, resolutions: list[CompletionResolution], retry_policy: Retry | None = None
    ) -> None:
        """Report on the resolution of the given list of completions."""
        resolutions_as_dicts = [asdict(resolution) for resolution in resolutions]
        response, request_id = self._post(
            "resolve-completions",
            json={"resolutions": resolutions_as_dicts},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)

    def resolve_edit(
        self,
        request_id: str,
        emit_time: str,
        resolve_time: str,
        is_accepted: bool,
        annotated_text: str,
        annotated_instruction: str,
        retry_policy: Retry | None = None,
    ) -> None:
        """Resolve the given edit."""

        emit_time_dt = parse(emit_time)
        resolve_time_dt = parse(resolve_time)

        json = {
            "request_id": request_id,
            "emit_time_sec": sec(emit_time_dt),
            "emit_time_nsec": nsec(emit_time_dt),
            "resolve_time_sec": sec(resolve_time_dt),
            "resolve_time_nsec": nsec(resolve_time_dt),
            "is_accepted": is_accepted,
            "annotated_text": annotated_text,
            "annotated_instruction": annotated_instruction,
        }
        logging.debug("Edit request id %s", request_id)

        response, _ = self._post("resolve-edit", json=json, retry_policy=retry_policy)
        logging.debug("Edit finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def resolve_smart_paste(
        self,
        request_id: str,
        is_accepted_chunks: list[bool],
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: str,
        stream_finish_time: str,
        apply_time: str,
        resolve_time: str,
        retry_policy: Retry | None = None,
    ) -> None:
        """Resolve the given smart paste."""

        initial_request_time_dt = parse(initial_request_time)
        stream_finish_time_dt = parse(stream_finish_time)
        apply_time_dt = parse(apply_time)
        resolve_time_dt = parse(resolve_time)

        json = {
            "request_id": request_id,
            "is_accepted_chunks": is_accepted_chunks,
            "is_accept_all": is_accept_all,
            "is_reject_all": is_reject_all,
            "initial_request_time_sec": sec(initial_request_time_dt),
            "initial_request_time_nsec": nsec(initial_request_time_dt),
            "stream_finish_time_sec": sec(stream_finish_time_dt),
            "stream_finish_time_nsec": nsec(stream_finish_time_dt),
            "apply_time_sec": sec(apply_time_dt),
            "apply_time_nsec": nsec(apply_time_dt),
            "resolve_time_sec": sec(resolve_time_dt),
            "resolve_time_nsec": nsec(resolve_time_dt),
        }
        logging.debug("Smart paste request id %s", request_id)

        response, _ = self._post(
            "resolve-smart-paste", json=json, retry_policy=retry_policy
        )
        logging.debug("Smart paste finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def chat_feedback(
        self, request_id: str, rating: int, note: str, retry_policy: Retry | None = None
    ) -> None:
        """Report chat feedback."""
        json = {"request_id": request_id, "rating": rating, "note": note}
        response, _ = self._post(
            "chat-feedback",
            json=json,
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def checkpoint_blobs(
        self, blobs: BlobsJson, retry_policy: Retry | None = None
    ) -> str:
        """Return the new checkpoint id for the given blobs."""
        blobs = blobs.sorted()
        response, request_id = self._post(
            "checkpoint-blobs",
            json={"blobs": asdict(blobs)},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["new_checkpoint_id"]

    def report_error(
        self,
        original_request_id: str | None,
        sanitized_message: str,
        stack_trace: str,
        diagnostics: list[tuple[str, str]],
        retry_policy: Retry | None = None,
    ) -> None:
        """Return the new checkpoint id for the given blobs."""
        response, request_id = self._post(
            "report-error",
            json={
                "original_request_id": original_request_id,
                "sanitized_message": sanitized_message,
                "stack_trace": stack_trace,
                "diagnostics": [
                    {"key": key, "value": value} for key, value in diagnostics
                ],
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def client_metrics(
        self, metrics: list[tuple[str, int]], retry_policy: Retry | None = None
    ) -> None:
        """Return the new checkpoint id for the given blobs."""
        response, request_id = self._post(
            "client-metrics",
            json={
                "metrics": [
                    {"client_metric": metric, "value": value}
                    for metric, value in metrics
                ]
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def client_for_model(self, model_name: str):
        """Returns the AugmentModelClient for a specific model."""
        return AugmentModelClient(self, model_name)

    def list_external_source_types(
        self, retry_policy: Retry | None = None
    ) -> list[str]:
        """Returns the list of all supported external source types."""
        response, request_id = self._post(
            "list-external-source-types", json={}, retry_policy=retry_policy
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["source_types"]

    def search_external_sources(
        self, query: str, source_types: list[str], retry_policy: Retry | None = None
    ) -> list[ExternalSource]:
        """Search external sources."""
        response, request_id = self._post(
            "search-external-sources",
            json={"query": query, "source_types": source_types},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return [
            ExternalSource(
                id=s["id"],
                title=s["title"],
                source_type=s["source_type"],
                name=s["name"],
            )
            for s in j["sources"]
        ]

    def get_implicit_external_sources(
        self, message: str, retry_policy: Retry | None = None
    ) -> list[ExternalSource]:
        """Get implicit external sources."""
        response, request_id = self._post(
            "get-implicit-external-sources",
            json={"message": message},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return [ExternalSource(**s) for s in j["sources"]]

    def save_chat(
        self,
        conversation_id: str,
        chat_exchange: Sequence[Exchange],
        title: str,
        retry_policy: Retry | None = None,
    ) -> SaveChatResponse:
        history = [ExchangeJson.from_exchange(x) for x in chat_exchange]
        history_json = [asdict(x) for x in history]

        response, request_id = self._post(
            "save-chat",
            json={
                "conversation_id": conversation_id,
                "chat": history_json,
                "title": title,
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return SaveChatResponse(uuid=j["uuid"], url=j["url"])


class AugmentModelClient:
    """Client to access the augment API for a specific model."""

    def __init__(self, augment_client: AugmentClient, model_name: str):
        self.augment_client_ = augment_client
        self.model_name = model_name

    def __repr__(self):
        return f"AugmentModelClient(url={self.augment_client_.url}, request_session_id={self.augment_client_.request_session_id}, last_request_id={self.augment_client_.last_request_id}, model={self.model_name})"

    def complete(
        self,
        prompt: str,
        path: str,
        memories: list[str] | None = None,
        suffix: str | None = None,
        top_k: int | None = None,
        top_p: float | None = None,
        temperature: float | None = None,
        max_tokens: int | None = None,
        lang: str | None = None,
        blob_name: str
        | None = None,  # This is the current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        cursor_position: int | None = None,
        suffix_end: int | None = None,
        probe_only: bool | None = False,
        blobs: BlobsJson | dict | None = None,
        recency_info: dict | None = None,
        filter_threshold: float | None = None,
        timeout: int | None = None,
        retry_policy: Retry | None = None,
    ) -> CompleteResponse:
        """Runs a completion on the given prompt."""
        json: dict[str, Any] = {
            "model": self.model_name,
            "path": path,
            "prompt": prompt,
        }
        if memories:
            json["memories"] = memories
        if suffix:
            json["suffix"] = suffix
        if top_k:
            json["top_k"] = top_k
        if top_p:
            json["top_p"] = top_p
        if temperature:
            json["temperature"] = temperature
        if max_tokens:
            json["max_tokens"] = max_tokens
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if cursor_position is not None:
            json["cursor_position"] = cursor_position
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if probe_only:
            json["probe_only"] = probe_only
        if recency_info is not None:
            json["recency_info"] = recency_info
        if blobs is not None:
            if isinstance(blobs, dict):
                blobs = BlobsJson(**blobs)
            blobs = blobs.sorted()
            json["blobs"] = asdict(blobs)
        else:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )
        if filter_threshold is not None:
            json["filter_threshold"] = filter_threshold

        response, request_id = self.augment_client_._post(
            "completion",
            json=json,
            timeout=timeout,
            retry_policy=retry_policy,
        )
        logging.debug(
            "Completion finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        unknown_memory_names = j.get("unknown_memory_names")
        if unknown_memory_names is None:
            unknown_memory_names = []
        completion_items = [
            CompletionItem(**entry) for entry in j.get("completion_items") or []
        ]
        # Note, calling json.get() will return None if the requested key is not
        # present.
        return CompleteResponse(
            text,
            completion_items=completion_items,
            unknown_memory_names=unknown_memory_names,
            request_id=request_id,
            checkpoint_not_found=j.get("checkpoint_not_found"),
            suggested_prefix_char_count=j.get("suggested_prefix_char_count"),
            suggested_suffix_char_count=j.get("suggested_suffix_char_count"),
        )

    def edit(
        self,
        selected_text: str,
        instruction: str,
        prefix: str,
        suffix: str,
        path: str | None,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        timeout: int | None = None,
        retry_policy: Retry | None = None,
    ) -> EditResponse:
        """Runs an edit on the given selected text."""
        json = {
            "model": self.model_name,
            "selected_text": selected_text,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )
        else:
            json["blobs"] = blobs if isinstance(blobs, dict) else asdict(blobs.sorted())

        response, request_id = self.augment_client_._post(
            "edit", json=json, timeout=timeout, retry_policy=retry_policy
        )
        logging.debug(
            "Edit finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        return EditResponse(
            str(request_id),
            text,
            j.get("unknown_blob_names", []),
            j.get("checkpoint_not_found"),
        )

    def instruction_stream(
        self,
        selected_text: str,
        instruction: str,
        prefix: str,
        suffix: str,
        path: str | None,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: list[Exchange | dict] | None = None,
        code_block: str | None = None,
        target_file_path: str | None = None,
        target_file_content: str | None = None,
        context_code_exchange_request_id: str | None = None,
        timeout: int | None = None,
    ) -> Iterable[EditResponse]:
        """Runs an instruction on the given selected text."""
        json = {
            "model": self.model_name,
            "selected_text": selected_text,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            # For smart paste only
            "code_block": code_block,
            "target_file_path": target_file_path,
            "target_file_content": target_file_content,
            "context_code_exchange_request_id": context_code_exchange_request_id,
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end

        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )
        else:
            json["blobs"] = blobs if isinstance(blobs, dict) else asdict(blobs.sorted())

        if chat_history is None:
            json["chat_history"] = []
        else:
            json["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post_stream(
            "instruction-stream", json=json, timeout=timeout
        )
        logging.debug("Got stream response")
        for chunk in response.iter_content(chunk_size=None):
            yield chunk.decode("utf-8")
        logging.debug("Done yielding stream response")

    def chat(
        self,
        selected_code: str,
        message: str,
        prefix: str,
        suffix: str,
        path: str,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: Sequence[Exchange | dict] | None = None,
        user_guided_blobs: list[str] | None = None,
        context_code_exchange_request_id: str | None = None,
        external_source_ids: list[str] | None = None,
        timeout: int | None = None,
        disable_auto_external_sources: bool = False,
        retry_policy: Retry | None = None,
    ) -> ChatResponse:
        """Chat with the model."""
        json: dict[str, Any] = {
            "model": self.model_name,
            "selected_code": selected_code,
            "message": message,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "disable_auto_external_sources": disable_auto_external_sources,
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if context_code_exchange_request_id is not None:
            json["context_code_exchange_request_id"] = context_code_exchange_request_id
        if user_guided_blobs is not None:
            json["user_guided_blobs"] = user_guided_blobs
        if external_source_ids is not None:
            json["external_source_ids"] = external_source_ids

        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )
        else:
            json["blobs"] = blobs if isinstance(blobs, dict) else asdict(blobs.sorted())

        if chat_history is None:
            json["chat_history"] = []
        else:
            json["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post(
            "chat", json=json, timeout=timeout, retry_policy=retry_policy
        )
        logging.debug(
            "Chat finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        incorporated_external_sources = j.get("incorporated_external_sources", None)
        if incorporated_external_sources is not None:
            incorporated_external_sources = [
                IncorporatedExternalSource(
                    source_name=chunk["source_name"],
                    link=chunk.get("link"),
                )
                for chunk in incorporated_external_sources
            ]
        workspace_file_chunks = j.get("workspace_file_chunks", None)
        if workspace_file_chunks is not None:
            workspace_file_chunks = [
                WorkspaceFileChunk(
                    char_start=chunk["char_start"],
                    char_end=chunk["char_end"],
                    blob_name=chunk["blob_name"],
                )
                for chunk in workspace_file_chunks
            ]
        return ChatResponse(
            str(request_id),
            text,
            j.get("unknown_blob_names", []),
            j.get("checkpoint_not_found"),
            workspace_file_chunks=workspace_file_chunks,
            incorporated_external_sources=incorporated_external_sources,
        )

    def chat_stream(
        self,
        selected_code: str,
        message: str,
        prefix: str,
        suffix: str,
        path: str,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: Sequence[Exchange | dict] | None = None,
        user_guided_blobs: list[str] | None = None,
        context_code_exchange_request_id: str | None = None,
        external_source_ids: list[str] | None = None,
        timeout: int | None = None,
    ) -> Iterable[ChatResponse]:
        """Chat with the model.

        TODO(ran): Fill the arg section.

        Args:
            selected_code: Selected code. Might be empty.
            message: User message.
            prefix: Prefix before selected code.
            suffix: Suffix after selected code.
            path: Path of current file.
            blob_names: DEPRECATED in favor of `blobs`. Blob names
                as a list of hashes.
            lang: The language. Defaults to None.
            blob_name: The blob name of the current blob. Note that it
                is different from `blob_names` and is not deprecated
            prefix_begin:
            suffix_end:
            blobs:
            chat_history: Conversation history.
            user_guided_blobs: User-guided blob names.
            context_code_exchange_request_id:
            external_source_ids:
            timeout:

        Returns:
            An iterable of strings, where a string might or might not be in JSON format separated by newline.
            Note: This is not a iterable over JSON documents or a NLJSON document.
            Each JSON document will be a JSON serialization of a ChatResponse defined in public_api.proto.
        """
        json_request: dict[str, Any] = {
            "model": self.model_name,
            "selected_code": selected_code,
            "message": message,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "blob_names": blob_names,
        }
        if blob_names:
            json_request["blob_names"] = blob_names
        if lang:
            json_request["lang"] = lang
        if blob_names:
            json_request["blob_names"] = blob_names
        if blob_name:
            json_request["blob_name"] = blob_name
        if prefix_begin is not None:
            json_request["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json_request["suffix_end"] = suffix_end
        if context_code_exchange_request_id is not None:
            json_request["context_code_exchange_request_id"] = (
                context_code_exchange_request_id
            )
        if user_guided_blobs is not None:
            json_request["user_guided_blobs"] = user_guided_blobs
        if external_source_ids is not None:
            json_request["external_source_ids"] = external_source_ids

        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json_request["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )
        else:
            json_request["blobs"] = (
                blobs if isinstance(blobs, dict) else asdict(blobs.sorted())
            )

        if chat_history is None:
            json_request["chat_history"] = []
        else:
            json_request["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post_stream(
            "chat-stream", json=json_request, headers={}, timeout=timeout
        )
        logging.debug("Got stream response")

        buffer = []
        for chunk in response.iter_content(chunk_size=None):
            for line in chunk.decode("utf-8").splitlines():
                buffer.append(line)
                json_data = "\n".join(buffer).strip()
                try:
                    json_container = json.loads(json_data)
                    if isinstance(json_container, dict):
                        incorporated_external_sources = json_container.get(
                            "incorporated_external_sources", None
                        )
                        if incorporated_external_sources is not None:
                            del json_container["incorporated_external_sources"]
                            incorporated_external_sources = [
                                IncorporatedExternalSource(
                                    source_name=chunk["source_name"],
                                    link=chunk.get("link"),
                                )
                                for chunk in incorporated_external_sources
                            ]
                        workspace_file_chunks = json_container.get(
                            "workspace_file_chunks", None
                        )
                        if workspace_file_chunks is not None:
                            del json_container["workspace_file_chunks"]
                            workspace_file_chunks = [
                                WorkspaceFileChunk(
                                    char_start=chunk["char_start"],
                                    char_end=chunk["char_end"],
                                    blob_name=chunk["blob_name"],
                                )
                                for chunk in workspace_file_chunks
                            ]
                        yield ChatResponse(
                            **json_container,
                            request_id=str(request_id),
                            incorporated_external_sources=incorporated_external_sources,
                            workspace_file_chunks=workspace_file_chunks,
                        )
                    else:
                        for json_item in json_container:
                            incorporated_external_sources = json_container.get(
                                "incorporated_external_sources", None
                            )
                            if incorporated_external_sources is not None:
                                del json_container["incorporated_external_sources"]
                                incorporated_external_sources = [
                                    IncorporatedExternalSource(
                                        source_name=chunk["source_name"],
                                        link=chunk.get("link"),
                                    )
                                    for chunk in incorporated_external_sources
                                ]
                            workspace_file_chunks = json_container.get(
                                "workspace_file_chunks", None
                            )
                            if workspace_file_chunks is not None:
                                del json_container["workspace_file_chunks"]
                                workspace_file_chunks = [
                                    WorkspaceFileChunk(
                                        char_start=chunk["char_start"],
                                        char_end=chunk["char_end"],
                                        blob_name=chunk["blob_name"],
                                    )
                                    for chunk in workspace_file_chunks
                                ]
                            yield ChatResponse(
                                **json_item,
                                request_id=str(request_id),
                                incorporated_external_sources=incorporated_external_sources,
                                workspace_file_chunks=workspace_file_chunks,
                            )
                    buffer = []
                except json.JSONDecodeError:
                    pass
        logging.debug("Done yielding stream response")

    def next_edit_stream(
        self,
        mode: str,
        scope: str,
        instruction: str,
        prefix: str,
        suffix: str,
        selected_text: str,
        vcs_change: dict | VCSChange = {"working_directory_changes": [], "commits": []},
        edit_events: list[dict] = [],
        recent_changes: list[dict] = [],  # a list of replacement texts
        path: str | None = None,
        blobs: BlobsJson | dict | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
        **kwargs,
    ) -> Iterable[NextEditResponse]:
        """
        Yields the next edit response.

        Returns:
            Next edit suggestions.
        """
        json_request: dict[str, Any] = {
            "model": self.model_name,
            "mode": mode,
            "scope": scope,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "selected_text": selected_text,
            "edit_events": edit_events,
            "recent_changes": recent_changes,
            "path": path,
            # for now, these have to agree with the prefix/selected_text size
            "selection_begin_char": len(prefix),
            "selection_end_char": len(prefix) + len(selected_text),
        }
        json_request.update(kwargs)
        if vcs_change is not None:
            json_request["vcs_change"] = vcs_change

        if blobs is not None:
            if isinstance(blobs, dict):
                blobs = BlobsJson(**blobs)
            blobs = blobs.sorted()
            json_request["blobs"] = asdict(blobs)
        else:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json_request["blobs"] = asdict(
                BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
            )

        response, request_id = self.augment_client_._post_stream(
            "next-edit-stream", json=json_request, headers={}, timeout=timeout
        )
        logging.debug("Got stream response")

        if not response.ok:
            print(response.text)
            raise ClientException(request_id, response)

        buffer = []
        for chunk in response.iter_content(chunk_size=None):
            logging.debug("Next edit stream chunk: %s", chunk)
            yield NextEditResponse(
                request_id=str(request_id),
            )
            for line in chunk.decode("utf-8").splitlines():
                buffer.append(line)
                json_data = "\n".join(buffer).strip()
                try:
                    json_container = json.loads(json_data)
                    if isinstance(json_container, dict):
                        yield NextEditResponse(
                            **json_container, request_id=str(request_id)
                        )
                    else:
                        for json_item in json_container:
                            yield NextEditResponse(
                                **json_item, request_id=str(request_id)
                            )
                    buffer = []
                except json.JSONDecodeError as e:
                    if warn_on_parse_error:
                        logging.warning("Failed to parse: %s", buffer)
                    else:
                        raise Exception(buffer) from e
        logging.debug("Done yielding stream response")

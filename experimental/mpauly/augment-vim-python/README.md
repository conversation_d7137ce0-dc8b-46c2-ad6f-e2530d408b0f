# Augment Vim Plugin

### Setup

- Put an api token at `~/.config/augment/api_token`
- Install a recent version of vim (9.1.X, `brew install vim` will do the trick)
- Run `symlink.sh` to symlink the plugin directory to somewhere vim can see it
- In `dist/`, run `make install` to setup the virtual environment for the
  language server
- Open a file with vim and completions will start appearing automatically. Run
  `:AugmentChat` to send chat requests (which I've bound to `<leader>c` by
  default).

I've only ran this on my machine, so it's possible there's some mpauly-specific
code that I missed.

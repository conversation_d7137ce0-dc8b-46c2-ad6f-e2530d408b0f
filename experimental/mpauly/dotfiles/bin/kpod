#!/usr/bin/env python3

import argparse
import sys

from k8_utils import get_pod_name


DEFAULT_NAMESPACE = "dev-mpauly"


def main():
    parser = argparse.ArgumentParser(
        description="Get Kubernetes pod name based on prefix."
    )
    parser.add_argument("pod_prefix", help="The prefix of the pod name to search for")
    parser.add_argument(
        "--namespace",
        default="dev-mpauly",
        help=f"The Kubernetes namespace (default: {DEFAULT_NAMESPACE})",
    )
    args = parser.parse_args()

    try:
        pod_name = get_pod_name(args.pod_prefix, args.namespace)
        print(pod_name)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)


if __name__ == "__main__":
    main()

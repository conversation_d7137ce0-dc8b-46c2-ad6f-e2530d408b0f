#!/usr/bin/env python3

import argparse
import subprocess

from k8_utils import get_pod_name


DEFAULT_NAMESPACE = "dev-mpauly"
DEFAULT_LINES = 20


def main():
    parser = argparse.ArgumentParser(description="Fetch and parse Kubernetes pod logs.")
    parser.add_argument("pod_pattern", help="Pattern to match pod name")
    parser.add_argument(
        "-n",
        "--namespace",
        default="dev-mpauly",
        help="Specify the namespace (default: dev-mpauly)",
    )
    parser.add_argument(
        "-l",
        "--lines",
        type=int,
        default=DEFAULT_LINES,
        help=f"Number of log lines to show (default: {DEFAULT_LINES})",
    )
    parser.add_argument(
        "-f",
        "--follow",
        action="store_true",
        help="Follow the logs as lines are written",
    )
    args = parser.parse_args()

    pod = get_pod_name(args.pod_pattern, args.namespace)

    kubectl_command = [
        "kubectl",
        "-n",
        args.namespace,
        "logs",
        "--tail",
        str(args.lines),
    ]
    if args.follow:
        kubectl_command.append("-f")
    kubectl_command.append(pod)

    full_command = kubectl_command + ["|", "parse_logs"]
    print("+ " + " ".join(full_command))
    try:
        subprocess.run(
            " ".join(full_command), capture_output=False, shell=True, text=True
        )
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()

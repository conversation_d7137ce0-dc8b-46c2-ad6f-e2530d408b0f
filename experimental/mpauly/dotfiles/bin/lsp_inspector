#!/usr/bin/env python3

# TODO: this seems to live long running processes that have to be killed manually...

import argparse
import sys
import subprocess
import threading
import logging
import os
import io
import json
from datetime import datetime


def setup_logging(log_file: str, raw: bool):
    if raw:
        logging.basicConfig(
            filename=log_file,
            level=logging.DEBUG,
            format="%(asctime)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
    else:
        logging.basicConfig(
            filename=log_file,
            level=logging.DEBUG,
            format="%(message)s",
        )


class Inspector:
    def __init__(self, raw: bool):
        self.raw = raw

    def log_stream(
        self, name: str, in_stream: io.BufferedReader, out_stream: io.BufferedWriter
    ) -> None:
        # self.log_message(f'Tracking stream {name} from {in_stream} to {out_stream}')

        while True:
            raw_chunk, message = self.get_chunk(in_stream)
            if not raw_chunk:
                break

            # message = chunk.decode('utf-8', errors='replace')
            message = message if message else str(raw_chunk)
            self.log_message(message, name)

            out_stream.write(raw_chunk)
            out_stream.flush()

    def get_chunk(self, stream: io.BufferedReader) -> tuple[bytes, str | None]:
        if self.raw:
            # read without blocking
            chunk = stream.read1(1024)
            return chunk, None
        else:
            # TODO: need error handling for this, don't want to hang forever if we're parsing strangely

            # strip the header and return only JSON content
            header = b""
            while b"\r\n\r\n" not in header:
                chunk = stream.read1(1)
                header += chunk

                # TODO:
                # if len(header) > 100:
                #     self.log_message(str(header), 'ERROR')
                #     exit(1)

            content_length = int(header.split(b"Content-Length: ")[1].split(b"\r\n")[0])
            json_content = stream.read(content_length)
            return header + json_content, json_content.decode("utf-8", errors="replace")

    def log_message(self, message: str, name: str | None = None):
        if not self.raw:
            try:
                message_json = json.loads(message)
                message = message_json
            except Exception:
                # logging.debug(f'message load failure: {e} for message: {message}')
                pass

            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "stream": name if name is not None else "DEBUG",
                "message": message,
            }
            logging.debug(json.dumps(log_entry))
        else:
            if name is not None:
                logging.debug(f"{name}: {message}")
            else:
                logging.debug(message)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="LSP Inspector")
    parser.add_argument(
        "server_command", nargs=argparse.REMAINDER, help="LSP server command to run"
    )
    parser.add_argument(
        "--log-file",
        type=str,
        default=os.path.expanduser("~/lsp_communication.log"),
        help="Log file to write to",
    )
    # TODO: this isn't working correctly, also have raw log statements
    parser.add_argument(
        "--raw", action="store_true", help="Output structured log entries"
    )
    args = parser.parse_args()

    # TODO: i feel like there should be a better abstraction here so that i can log messages and debug info
    setup_logging(args.log_file, args.raw)

    inspector = Inspector(args.raw)
    inspector.log_message(f"Starting server process with command {args.server_command}")

    server_process = subprocess.Popen(
        args.server_command,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd=os.getcwd(),
        env=os.environ,
    )

    # TODO: what does daemon do?
    threading.Thread(
        target=inspector.log_stream,
        args=("SERVER -> CLIENT", server_process.stdout, sys.stdout.buffer),
        daemon=True,
    ).start()
    threading.Thread(
        target=inspector.log_stream,
        args=("CLIENT -> SERVER", sys.stdin.buffer, server_process.stdin),
        daemon=True,
    ).start()
    threading.Thread(
        target=inspector.log_stream,
        args=("SERVER ERROR", server_process.stderr, sys.stderr.buffer),
        daemon=True,
    ).start()

    # TODO: show stderr of this process somewhere?

    server_process.wait()

#!/usr/bin/env bash

LOG_FILE=~/lsp_communication.log
N_LINES=0

# ANSI color codes
GREEN='\x1b[32m'
BLUE='\x1b[34m'
RESET='\x1b[0m'

usage() {
    echo "Usage: $(basename "$0") [-h] [-f FILE] [-n LINES]"
    echo
    # TODO: change this
    echo "Tail and format LSP communication logs"
    echo
    echo "Options:"
    echo "  -h          Show this help message"
    echo "  -f FILE     Log file path (default: $LOG_FILE)"
    echo "  -n LINES    Number of lines to show (default: $N_LINES)"
    exit 1
}

while getopts "hf:n:" opt; do
    case $opt in
        h)
            usage
            ;;
        f)
            LOG_FILE="$OPTARG"
            ;;
        n)
            N_LINES="$OPTARG"
            ;;
        *)
            usage
            ;;
    esac
done

touch $LOG_FILE \
&& tail -f -n $N_LINES $LOG_FILE \
| jq --unbuffered -r -M '
    [
        (.timestamp | split("T")[1] | split(".")[0] + "." + (split(".")[1][:3])),
        ("[" + .stream + "]"),
        if (.message | type) == "string" then
            .message
        else
            .message.method,
            (if .message | has("id") then "id=" + (.message.id | tostring) else empty end),
            (if .message.params != null then "params=" + (.message.params | tostring) else empty end),
            (if .message.result != null then "result=" + (.message.result | tostring) else empty end),
            (if .message.error != null then "error=" + (.message.error | tostring) else empty end)
        end
        ]
    |
    map(select(. != null)) |
    join(" ")' \
| sed -E \
    -e 's/\[CLIENT -> SERVER\]/'"$GREEN"'[CLIENT -> SERVER]'"$RESET"'/g' \
    -e 's/\[SERVER -> CLIENT\]/'"$BLUE"'[SERVER -> CLIENT]'"$RESET"'/g'

import subprocess
import sys
from datetime import datetime, timezone


def _run_command(command):
    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error executing command: {' '.join(command)}")
        print(result.stderr)
        sys.exit(1)
    return result.stdout.strip()


def get_pod_age(namespace, pod_name):
    start_time_str = _run_command(
        [
            "kubectl",
            "-n",
            namespace,
            "get",
            "pod",
            pod_name,
            "-o",
            "go-template={{.status.startTime}}",
        ]
    )
    start_time = datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ").replace(
        tzinfo=timezone.utc
    )
    age = datetime.now(timezone.utc) - start_time
    days, seconds = age.days, age.seconds
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds %= 60
    return f"{days}d {hours}h {minutes}m {seconds}s"


def get_pod_name(pod_prefix, namespace):
    try:
        pod_info = _run_command(
            [
                "kubectl",
                "-n",
                namespace,
                "get",
                "pods",
                "-o",
                "custom-columns=NAME:.metadata.name,CREATED:.metadata.creationTimestamp",
                "--sort-by=.metadata.creationTimestamp",
            ]
        )

        pods = [
            line.split()
            for line in pod_info.split("\n")[1:]
            if line.startswith(pod_prefix)
        ]
        pods.reverse()  # reverse so newer pods come first

        if not pods:
            print(
                f"Error: No pods found with prefix '{pod_prefix}' in namespace '{namespace}'",
                file=sys.stderr,
            )
            sys.exit(1)

        if len(pods) == 1:
            return pods[0][0]

        print(
            f"Multiple pods found with prefix '{pod_prefix}' in namespace '{namespace}':\n"
        )
        for i, (pod_name, _) in enumerate(pods, 1):
            age = get_pod_age(namespace, pod_name)
            print(f"{i}) {pod_name} ({age})")

        newest_pod = pods[0][0]
        while True:
            try:
                selection = input(
                    f"\nSelect pod number (1-{len(pods)}) or press Enter for newest [{newest_pod}]: "
                ).strip()
                if not selection:
                    print()
                    return newest_pod
                selection = int(selection)
                if 1 <= selection <= len(pods):
                    print()
                    return pods[selection - 1][0]
                else:
                    print("Invalid selection. Please try again.")
            except ValueError:
                print("Invalid input. Please enter a number or press Enter.")
    except KeyboardInterrupt:
        exit(1)

#!/bin/env python3
# Utility to display json log lines in a more readable format.
#
# This tool should also be usable outside bazel
#
# Usage example:
#   kubernetes logs deploy/<deployment> -f | ./tools/parse_logs
#
# Configuration:
#   AUGMENT_FORMAT_STRING: Format string to use for log lines. Default: "{time} | {severity} | {message} | {span}"
#                          For tabs, use actual tab character and not the escaped version (\t).
#                          Example for tab indented with padding:
#                               export AUGMENT_FORMAT_STRING="{time:30}    {severity:15}  {message}   {span}  {sourceLocation}"
#
import json
import sys
import os
import select

colorized = {
    "INFO": "\033[32mINFO\033[0m",  # Green
    "ERROR": "\033[31mERROR\033[0m",  # Red
    "CRITICAL": "\033[31mCRITICAL\033[0m",  # Red
    "WARN": "\033[33mWARN\033[0m",  # Yellow
}


def _colorize_severity(severity: str, color: bool):
    if not color:
        return severity
    if severity in colorized:
        return colorized[severity]
    return severity


# List of predetermined supported fields
supported_fields = ["time", "severity", "message", "span", "sourceLocation", "error"]


def main() -> int:
    try:
        color = (
            os.getenv("AUGMENT_COLOR", "true" if sys.stdout.isatty() else "false")
            == "true"
        )
        format_string = os.getenv(
            "AUGMENT_FORMAT_STRING", "{time} | {severity} | {message} | {error} | {span}"
        )
        included_fields = [
            key
            for key in supported_fields
            if "{" + key + "}" in format_string or "{" + key + ":" in format_string
        ]

        while True:
            # Check if there's input available
            rlist, _, _ = select.select([sys.stdin], [], [], 0.1)
            if rlist:
                line = sys.stdin.readline()
                if not line:  # EOF
                    break
                line = line.strip()
                try:
                    json_line = json.loads(line)
                    placeholders = {}
                    for key in included_fields:
                        json_key = key
                        if key == "sourceLocation":
                            json_key = "logging.googleapis.com/sourceLocation"
                        if json_key in json_line:
                            placeholders[key] = (
                                _colorize_severity(json_line[json_key].upper(), color=color)
                                if key == "severity"
                                else json_line[json_key]
                            )
                        else:
                            placeholders[key] = ""
                    print(format_string.format(**placeholders), flush=True)
                    if "stack_trace" in json_line:
                        print(json_line["stack_trace"], flush=True)
                except Exception:  # pylint: disable=broad-except
                    print(line, flush=True)
            else:
                # No input available, continue the loop
                continue

    except KeyboardInterrupt:
        return 0
    except BrokenPipeError:
        return 1
    return 0


if __name__ == "__main__":
    sys.exit(main())

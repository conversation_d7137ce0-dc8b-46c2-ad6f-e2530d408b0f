#!/usr/bin/env bash

LOG_FILE=~/lsp_communication.log
N_LINES=0

usage() {
    echo "Usage: $(basename "$0") [-h] [-f FILE] [-n LINES]"
    echo
    echo "Tail and format LSP communication logs"
    echo
    echo "Options:"
    echo "  -h          Show this help message"
    echo "  -f FILE     Log file path (default: $LOG_FILE)"
    echo "  -n LINES    Number of lines to show (default: $N_LINES)"
    exit 1
}

while getopts "hf:n:" opt; do
    case $opt in
        h)
            usage
            ;;
        f)
            LOG_FILE="$OPTARG"
            ;;
        n)
            N_LINES="$OPTARG"
            ;;
        *)
            usage
            ;;
    esac
done

touch $LOG_FILE \
&& tail -f -n $N_LINES $LOG_FILE \
| jq --unbuffered '{
    timestamp,
    stream,
    message: (
        if (.message | type) == "string"
        then .message
        else
            .message | {
                id,
                method,
                params: (if .params == null then null else .params | tojson end),
                result: (if .result == null then null else .result | tojson end),
                error: (if .error == null then null else .error | tojson end),
            } | del(.[] | nulls)
        end
    )
}'

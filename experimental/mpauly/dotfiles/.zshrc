# Add augment bin to path
export PATH=$PATH:$HOME/repos/augment-tools/experimental/mpauly/dotfiles/bin

# Useful aliases
alias k='kubectl --context=gke_system-services-dev_us-central1_us-central1-dev -n dev-mpauly'
alias kp='kubectl --context=gke_system-services-prod_us-central1_us-central1-prod'
alias ka='kubectl --context=gcp-agent0 -n dev-mpauly'
alias kpa='kubectl --context=gcp-prod-agent0 -n staging-shard-0'

alias kps=kpods
alias kds=kdeployments

alias b=bazel
alias bt='bazel test'
alias br='bazel run'
alias bf='bazel run //:format'

# Node setup via nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

export PATH="/usr/local/go/bin:$PATH"
eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
export PATH=$PATH:$HOME/.local/bin  # Add local bin to path (this is where setup.sh installs stuff
source ~/.local/google-cloud-sdk/completion.zsh.inc
source ~/.local/google-cloud-sdk/path.zsh.inc
. "$HOME/.cargo/env"

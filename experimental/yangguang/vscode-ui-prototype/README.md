# AugmentCode UI prototype

This is a VS code extension for experimenting UI features for Augment. This is a *prototype*, for exploring and demoing UI features. It may not be reliable, and most edge cases may fail.

## Demo features

### Pick a model/API

Go to extension settings (from the marketplace page, or vscode settings, then choose "Extensions > Augment UI prototype" in the settings tree on the left), set the model/API per the instruction.

**Only tested all features on `openai`, some newer features may not work on other API choieces.**

### Main feature: Tasks & Scratchpad

To trigger the feature:

- Open the sidebar only
  - Click on the Augment head icon on vscode left sidebar.
  - Keyboard shortcut: `shift+command+W`
  - Then you can type `/sp` to start a scratchpad, or chat in the sidebar, click on the *edit* icon on any code block.
- Open the sidebar, and also start a scratchpad directly.
  - Select a piece of code, then right click "Augment - start a scratchpad"
  - Select a piece of code, then `option+command+P`
  - From the a diff codelens (e.g. triggered by selecting a piece of code, and `option+command+A` to edit), click the codelens button "Scratch pad".

Features:

1. Tasks: The top of the webview has tabs, each one is a task. It's meant for working on multiple things in different threads.
1. Chat: The bottom chat box is where you send messages.
    - It works normally like a chatbot when directly typing.
    - You can click the "paste" icon (bottom right, next to "send"), to get currently selected code in your editor here.
    - You can type tool name - currently only `/sp` for scratchpad - to create a tool block. You can type only `/sp` for an empty scratchpad, or `/sp function someFun()` to start a scratchpad with initial content.
1. Chat reply: In chat reply, code blocks are highlighted, and clickable to make it a scratchpad, for further iteration.
1. Scratchpad: This is where you iterate on a piece of code.
    - It can be triggered by all the methods mentioned above.
    - Once triggered, a color bar and border shows up in around the block, as well as the input area.
    - The code block becomes editable
    - New message send will be scoped to work on the code block only.
    - Once started iterating, all iterations can be viewed via the `<` / `>` button at the top-right of the code block, inside the color bar.
    - For each code version, there are buttons below the code block - copy to clipboard, copy to editor, vote good, vote bad, regenerate.
      - copy to editor will replace your selected code, and show diff colors. (the diff is not perfectly accurate)
    - Click on the `X` above input area to exit the tool. Goes back to chat mode.
    - Click on any code block / scratchpad to activate that code as a scratchpad.
1. Explicitly attach files (UI/UX is not perfect yet)
    - At the bottom of the sidebar, there is a *setting button* (left to the *send button*). Click it to toggle the file attach box. Use it to attach any number of files to your prompt. (note: extra long files, or too many files will be truncated to fit within token limit.)
    - After that, any chat or scratchpad in this task will include selected files.

### Chat (older design)

A more traditional chat experience. Open the webview sidebar, click on the tiny double-arrow button at the bottom-left to toggle between `Chat (old)` design and `Task & Scratchpad` design.

- Chat works as normal.
- Explain code
  - Select a piece of code in editor, and type `/explain` (with optionally any detailed questions after it)
  - It will explain the code in the chat.
- Edit code:
  - Select a piece of code in editor, and type `/edit [your instruction]`
  - The code will be edited directly in the editor, with diff. (the diff is not perfectly accurate)
  - It shows codelens buttons above the diff - accept, reject, explain.
- The same input box can be triggered via `shift+command+A`, which works the same as the input mentioned above, just using vscode native floating input instead.

### Pseudo code

1. Open a file, optionally select a piece of code (if not selected, it takes the whole file)
1. Trigger the command: `Shift + Command + E`, or command pallet - `Augment prototype - Pseudo code`
1. A new tab will open, first explains the code overall. Then explains piece by piece.
1. Hover on the detail pieces to highlight the code. (the line numbers are not accurate, WIP)
1. Click on the detail pieces to edit. (the editing code part is not implemented)

### Misc experiments

Some smaller featuers are not integrated into any main flow. See `package.json` and `src/extension.ts` for all the individual features experiments, like hover info, custom diagnose.

## Known issues

1. Overall, only the "best case scenario" is implemented. Many edge cases are not covered.
1. The inline diff may not be calculated correctly sometimes. Which will also cause *Accept* and *Reject* remove incorrect lines.
1. Copy to editor feature doesn't work correctly on notebooks.
1. If you switch tab, edit file, trigger event, etc. while the content is loading / streaming, it may fail or break.

## Development

### Overview

1. The extension code mainly has 2 parts:

    - The vscode part: All the code of the extension that interacts with vscode. It's the root level `package.json` and `src`.
    - The webview: Content of the sidebar webview, it's a Vue app. It's the files inside `/webview`, with its separate `package.json`.

1. Some files are shared between 2 parts - `/webview/src/shared/types.ts`, and `/webview/src/shared/pure-shared-utils.ts`.
1. The 2 parts communicates with each other by posting window messages. During development, the Vue code is compiled and imported into the extension code (`_getHtmlForWebview` in `/src/featuers/sidebar.ts`).
1. State & Storage
    - vscode part has an object as the store - `editStateStore` in `/src/lib/store.ts`.
    - `context.globalState` is also used, as the persistent store, mostly for saving user's chat/task history across workspaces.
    - in the webview, we manage the state in store normally, in `/webview/src/shared/stores.ts`.
1. Inside the webview, we use [vscode-webview-ui-toolkit webcomponents](https://github.com/microsoft/vscode-webview-ui-toolkit/tree/main) and [codicon icons](https://microsoft.github.io/vscode-codicons/dist/codicon.html) when applicable.

### How-to

- Install dependencies
  - Install nodejs if you don't have it on your computer. (recommand using `nvm`, if you don't do nodejs developement, then just install from https://nodejs.org/en/download)
  - `npm ci` to install dependencies for vscode code part.
  - `cd webview && npm ci` to install dependencies for the webview part.
- Go to `src/` and copy the `env.ts.example` file to `env.ts`. Then optionally (if you plan to use OpenAI) fill in the OpenAI key.
- Run dev host
  - `npm run watch` to watch code edits.
    - It auto compiles code for each save. Compiling the webview code may take a few seconds, if the console shows "Started build", wait for it to finish.
  - Open `src/extension.ts`, then `fn + F5`. VSCode will open a developement host window. (may ask you to pick - "Extension")
    - For the development window to pick up file changes (e.g. after the `watch` command above), in vscode command pallet, select "Developer: Reload window".
- Compile a shareable package. `npm run vsix` to generate a `.vsix`. (output `ui-prototype-[version].vsix` in this folder), then anyone can install manually with it on vscode.

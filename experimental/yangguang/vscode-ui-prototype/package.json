{"name": "ui-prototype", "displayName": "AugmentCode UI Prototype", "publisher": "augmentcode", "description": "", "version": "0.2.4", "engines": {"vscode": "^1.80.0"}, "repository": {"url": "https://github.com/sutterhill/augment-prototype"}, "main": "./out/main.js", "categories": ["Other"], "activationEvents": ["onLanguage:typescript"], "icon": "media/marketplace.png", "contributes": {"configuration": {"type": "object", "title": "Augment UI prototype", "properties": {"apiMethod": {"type": "string", "default": "openai", "description": "Pick one from - openai, llama-7b-selected, llama-34b-selected, or enter Augment server origin (e.g. http://**************:5000)"}}}, "commands": [{"category": "Augment prototype", "command": "extension.pseudo", "title": "Pseudo code"}, {"category": "Augment prototype", "command": "extension.edit-inline", "title": "Edit inline"}, {"category": "Augment prototype", "command": "extension.showSidebar", "title": "Show sidebar"}, {"category": "Augment prototype", "command": "extension.contextMenuScratchpad", "title": "🤖 Augment - start a scratchpad"}, {"category": "Augment prototype", "command": "extension.contextMenuProofread", "title": "🤖 Augment - proofread"}], "keybindings": [{"command": "extension.edit-inline", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}], "viewsContainers": {"activitybar": [{"icon": "media/logo-black-white.png", "id": "augmentSidebar", "title": "Augment UI"}]}, "views": {"augmentSidebar": [{"id": "augmentSidebar.chatWebview", "name": "Augment Chat", "type": "webview"}, {"id": "augmentSidebar.info", "name": "Prototype info", "type": "webview", "visibility": "hidden"}]}, "menus": {"editor/context": [{"command": "extension.contextMenuScratchpad", "group": "inSelection", "when": "editorHasSelection && editorTextFocus", "title": "🤖 Augment - start a scratchpad"}, {"command": "extension.contextMenuProofread", "group": "inSelection", "when": "editorHasSelection && editorTextFocus", "title": "🤖 Augment - proofread"}]}}, "scripts": {"vsix": "npm run webview:build && vsce package", "vscode:_esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=out/main.js --external:vscode --format=cjs --platform=node", "vscode:prepublish": "npm run vscode:_esbuild-base -- --minify", "vscode:build": "npm run vscode:_esbuild-base -- --sourcemap", "vscode:watch": "npm run vscode:_esbuild-base -- --sourcemap --watch", "webview:standalone": "cd webview && npm run standalone", "webview:watch": "cd webview && npm run watch", "webview:build": "cd webview && npm run build", "watch": "run-p --print-label webview:watch vscode:watch"}, "devDependencies": {"@types/diff": "^5.0.3", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vscode/test-electron": "^2.3.2", "esbuild": "^0.19.2", "eslint": "^8.41.0", "glob": "^8.1.0", "mocha": "^10.2.0", "npm-run-all2": "^6.0.6", "sass": "^1.69.2", "typescript": "^5.1.3"}, "dependencies": {"@supabase/supabase-js": "^2.33.1", "@vscode/vsce": "@2.22.0", "diff": "^5.1.0", "gpt-3-encoder": "^1.1.4", "langchain": "^0.0.120", "line-diff": "^2.1.1", "openai": "^3.3.0", "uuid": "^9.0.0", "web-streams-polyfill": "^4.0.0-beta.3", "zod": "^3.21.4"}}
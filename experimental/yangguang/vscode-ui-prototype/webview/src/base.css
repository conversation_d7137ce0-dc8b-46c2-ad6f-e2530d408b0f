:root {
  --scratchpad-color: #49A4FF;
  --transition: .2s ease;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  padding: 0;
}

:where(button) {
  cursor: pointer;
  border: 0;
  background: transparent;
}

._grow {
  flex: 1;
}

.codicon {
  color: currentColor;
}

._markdown-content * {
  all: revert;
}

._markdown-content code {
  white-space: pre-wrap;
}

/* For some reason, the markdown code color is overwritten by another rule. */
code {
  color: var(--vscode-textPreformat-foreground) !important;
}

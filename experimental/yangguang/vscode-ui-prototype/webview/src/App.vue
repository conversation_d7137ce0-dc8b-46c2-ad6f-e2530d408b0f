<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { listenToMessage, requestContentFromVscode } from './shared/utils';
import Chat from '@/components/ChatMode.vue';
import Tasks from '@/components/TasksMode.vue';
import { MessageFromWebviewAction, type Task } from './shared/types';
import { useTasksStore } from './shared/stores';

const tasksStore = useTasksStore();

const isTaskMode = ref(true);

listenToMessage();

onMounted(async () => {
  const tasks = await requestContentFromVscode<Task[]>({action: MessageFromWebviewAction.REQUEST_VSCODE_STORE});
  tasksStore.loadStore(tasks);
});
</script>

<template>
  <div class="wrapper">
    <vscode-button class="switcher" appearance="icon" title="Switch design" :aria-label="isTaskMode ? 'Switch to the Tasks design' : 'Switch to the Chat only design'"
        @click="isTaskMode = !isTaskMode">
      <span class="codicon codicon-arrow-swap"></span>
    </vscode-button>
    <Tasks v-if="isTaskMode" />
    <Chat v-else />
  </div>
</template>

<style scoped>
.wrapper {
  height: calc(100vh - 3px);  /* idk why there is a weird scrolling. */
  position: relative;
}

.switcher {
  position: fixed;
  bottom: 0;
  left: 0;
  scale: .6;
  z-index: 100;
}
</style>

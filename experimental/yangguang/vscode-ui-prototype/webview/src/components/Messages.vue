<script setup lang="ts">
import { useChatStore } from '@/shared/stores';
import ContentChatRender from './ContentChatRender.vue';
const chatStore = useChatStore();
</script>

<template>
  <ul>
    <li v-for="message in chatStore.messages">
      <ContentChatRender :message="message" />
      
      <details v-if="message.action">
        <summary>debug info</summary>

        <p>Action: {{message.action}}</p>
        <p>Model: {{message.model}}</p>
        <p v-if="message.status">API status: {{message.status}}</p>
        <a :href="`https://supabase.com/dashboard/project/jojultdzeeymbwqvhivt/editor/79538?sort=created:desc&filter=id:eq:${message.id}`" target="_blank">Log link</a>
      </details>
    </li>
  </ul>
</template>

<style scoped>
ul {
  list-style: none;
  padding: 0;
  overflow: auto;
}

li {
  margin-bottom: 1em;
}

details {
  margin-top: .5em;
  font-size: .9em;
  text-align: right;
}

summary {
  cursor: pointer;
}
</style>

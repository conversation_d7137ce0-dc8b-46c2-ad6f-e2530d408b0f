<script setup lang="ts">
import { ref } from 'vue';
import Messages from '@/components/Messages.vue';
import { sendMessageToVscode } from '@/shared/utils';
import { MessageFromWebviewAction } from '@/shared/types';

const query = ref('');

function submit() {
  sendMessageToVscode({action: MessageFromWebviewAction.SEND_CHAT_MESSAGE, payload: {content: query.value}});
  query.value = '';
}

function inputChange({target}: {target: HTMLInputElement}) {
  query.value = target.value;
}

</script>

<template>
  <main>
    <div class="message-wrapper">
      <Messages />
    </div>
    <div class="input-wrapper">
      <vscode-text-field :value="query" @input="inputChange" @keydown.enter="submit" autofocus placeholder="Actions: /edit, /explain">
        <span slot="end" class="codicon codicon-send"></span>
      </vscode-text-field>
    </div>
  </main>
</template>

<style scoped>
main {
  height: 100%;
  display: grid;
  grid-template-rows: minmax(0, 1fr) auto;
}

.message-wrapper {
  padding-bottom: 4em;
}

.input-wrapper {
  padding: .5em;
  background: var(--background);  /* vscode injected variable. */
}

vscode-text-field {
  width: 100%;
}
</style>

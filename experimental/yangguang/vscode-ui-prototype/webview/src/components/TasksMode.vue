<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { sendMessageToVscode, focusTextarea, scrollToBottom, requestContentFromVscode } from '@/shared/utils';
import Tasks from './Tasks.vue';
import { MessageFromWebviewAction } from '@/shared/types';
import { useTasksStore, nonReactiveStore } from '@/shared/stores';
import FilePicker from './FilePicker.vue';

const taskStore = useTasksStore();

const isDragging = ref(false);
const showConfig = ref(false);
const bottomHeightPx = ref(100);

let draggingStartY = 0;
let draggingStartHeight = bottomHeightPx.value;
const textarea = ref<HTMLTextAreaElement|undefined>();
const SCRATCHPAD_COMMAND = '/sp';

onMounted(() => {
  focusTextarea();
});

async function submit() {
  if (taskStore.query.startsWith(SCRATCHPAD_COMMAND)) {  // This is the 1st command, so inside a scratchpad, this will jump out of it.
    const content = taskStore.query.replace(SCRATCHPAD_COMMAND, '').trim();
    taskStore.createScratchpad(content);
  } else if (taskStore.activeScratchpad) {
    const id = crypto.randomUUID();
    const taskId = taskStore.activeTask.taskId;
    const messageId = taskStore.activeScratchpad.id;
    // Note this is not `taskStore.activeScratchpad.codeVersions[taskStore.activeScratchpad.activeCodeVersionIndex].content`
    // The content in the editor can be changed by the user.
    const previousCode = nonReactiveStore.getEditorContent();
    taskStore.createNewCodeVersion(taskId, messageId, id, taskStore.query, previousCode);
    sendMessageToVscode({
      action: MessageFromWebviewAction.SEND_TASK_SCRATCHPAD,
      payload: {
        instruction: taskStore.query,
        // Note this is not `taskStore.activeScratchpad.codeVersions[taskStore.activeScratchpad.activeCodeVersionIndex].content`
        // The content in the editor can be changed by the user.
        previousCode,
        taskId,
        messageId,
        id,
        attachedFiles: taskStore.activeTask.attachedFiles,
      }
    });
  } else {
    taskStore.upsertChat({
      id: crypto.randomUUID(),
      content: taskStore.query,
      taskId: taskStore.activeTask.taskId,
      fromUser: true,
    });
    sendMessageToVscode({
      action: MessageFromWebviewAction.SEND_TASK_CHAT,
      payload: {
        taskId: taskStore.activeTask.taskId,
        content: taskStore.query,
        attachedFiles: taskStore.activeTask.attachedFiles,
      }
    });
  }

  if (taskStore.temporaryScratchpad) {
    taskStore.temporaryScratchpad = undefined;
    scrollToBottom();
  }

  setTimeout(() => {
    taskStore.query = '';
  });
}

function exitScratchpad() {
  taskStore.exitScratchpad();
  taskStore.query = '';
  taskStore.temporaryScratchpad = undefined;
  scrollToBottom();
}

function startDrag(e: MouseEvent) {
  isDragging.value = true;
  draggingStartY = e.clientY;
  draggingStartHeight = bottomHeightPx.value;
}

function draggingHeight(e: MouseEvent) {
  if (!isDragging.value) return;

  e.preventDefault();
  const delta = e.clientY - draggingStartY;
  bottomHeightPx.value = draggingStartHeight - delta;
}

function addFile(path: string) {
  if (taskStore.activeTask.attachedFiles.includes(path)) return;

  taskStore.activeTask.attachedFiles.push(path);
  taskStore.saveToVscodeStore();
}

function removeFile(index: number) {
  taskStore.activeTask.attachedFiles.splice(index, 1);
  taskStore.saveToVscodeStore();
}
</script>

<template>
  <main @mouseleave="isDragging = false;" @mouseup="isDragging = false;" @mousemove="draggingHeight">
    <div class="header-scroll">
      <header>
        <button v-for="(tab, index) of taskStore.tasks" class="tab" :class="{'-active': taskStore.activeTaskIndex === index}"
            @click="taskStore.switchTask(index)">
          {{ tab.messages.length ? tab.title : '(empty)' }}
          <vscode-button @click.stop="taskStore.deleteTask(index)" appearance="icon" aria-label="Delete task">
            <span class="codicon codicon-close"></span>
          </vscode-button>
        </button>
        <vscode-button class="add-task" @click="taskStore.createTask" appearance="icon" aria-label="Create task">
          <span class="codicon codicon-add"></span>
        </vscode-button>
      </header>
    </div>
    <div class="message-wrapper _scroll-wrapper">
      <Tasks />
    </div>
    <div class="bottom-wrapper" :class="{chat: !taskStore.activeScratchpad}">
      <div class="resize-control" @mousedown="startDrag"></div>
      <div v-if="taskStore.activeScratchpad" class="active-bar">
        Scratch Pad
        <div class="_grow"></div>
        <vscode-button appearance="icon" aria-label="Exit scratchpad" @click="exitScratchpad">
          <span class="codicon codicon-close"></span>
        </vscode-button>
      </div>

      <!-- This part of the styling is a bit messy - we have the placeholder showing under the textarea. -->
      <div class="textarea-wrapper" :style="{height: `${bottomHeightPx}px`}">
        <textarea class="_textarea" v-model="taskStore.query" autofocus @keypress.enter.exact="submit"></textarea>
        <div class="placeholder" :class="{'hidden': taskStore.query}">
          <p v-if="taskStore.activeScratchpad">Instruct to iterate on this scratchpad.</p>
          <template v-else>
            <p>Ask coding questions</p>
            <p><code>/sp</code>: Start a scratch pad</p>
          </template>
        </div>
      </div>

      <template v-if="showConfig">
        <ul class="selected-files-active">
          <li v-for="(path, index) of taskStore.activeTask.attachedFiles">
            <span class="left-ellipsis" :title="path">{{ path }}</span>
            <vscode-button appearance="icon" aria-label="Remove" @click="removeFile(index)">
              <span class="codicon codicon-close"></span>
            </vscode-button>
          </li>
        </ul>
        <FilePicker class="file-picker" @add="addFile" />
      </template>
      <button v-else-if="taskStore.activeTask.attachedFiles.length" class="selected-files-inactive" @click="showConfig = true">
        Include {{ taskStore.activeTask.attachedFiles.length > 1 ? 'files' : 'file' }}: {{ taskStore.activeTask.attachedFiles.join(', ') }}
      </button>

      <div class="textare-buttons">
        <vscode-button appearance="icon" aria-label="Advanced" title="Advanced"
            @click="showConfig = !showConfig">
          <span class="codicon codicon-settings"></span>
        </vscode-button>
        <vscode-button appearance="icon" aria-label="Send" @click="submit">
          <span class="codicon codicon-send"></span>
        </vscode-button>
      </div>
    </div>
  </main>
</template>

<style lang="scss" scoped>
main {
  height: 100%;
  display: grid;
  grid-template-rows: auto minmax(0, 1fr) auto;
}

.header-scroll {
  overflow-x: auto;
}

header {
  border-bottom: 1px solid var(--chat-color);
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--panel-tab-active-border);

  .tab {
    border: 1px solid #333;
    border-bottom-color: transparent;
    color: #999;
    padding: 0.1em 0.5em 0.3em .5em;
    font-size: .9em;
    display: flex;
    align-items: center;
    white-space: nowrap;

    &.-active {
      color: var(--vscode-editor-foreground);
      border-color: var(--panel-tab-active-border);
      border-bottom-color: var(--background);
    }
  }

  .add-task {
    color: #999;
    margin: 0 0.3em;
  }

  .tab,
  .add-task {
    position: relative;
    top: 1px;  // Move down 1px to cover the border-bottom of the parent.
  }
}

.message-wrapper {
  overflow: auto;
}

.active-bar {
  background: var(--scratchpad-color);
  display: flex;
  align-items: center;
  padding: .2em .6em;
  color: black;
}

.active-bar .codicon {
  color: black;
}

.bottom-wrapper {
  position: relative;

  &.chat {
    border-top: 1px solid var(--panel-tab-active-border);
  }

  .resize-control {
    position: absolute;
    top: -.5em;
    left: 0;
    right: 0;
    height: 1em;
    cursor: ns-resize;
  }
}

.textarea-wrapper {
  position: relative;
  padding-left: 2.2em;
  padding-top: .2em;
  background-image: url(https://jojultdzeeymbwqvhivt.supabase.co/storage/v1/object/public/augment-prototype/icon-user.svg);
  background-size: 1.6em;
  background-position: left .5em top .5em;
  background-repeat: no-repeat;

  .placeholder {
    position: absolute;
    inset: 0;
    left: 2.3em;
    top: .2em;
    padding: .5em;
    z-index: -1;
    transition: opacity var(--transition);
    opacity: .7;

    &.hidden {
      opacity: 0;
    }
  }

  code {
    color: inherit !important;
  }

  textarea {
    outline: none;
    width: 100%;
    border: 0;
    color: inherit;
    font: inherit;
    padding: .5em;
    resize: none;
    height: 100%;
    background: transparent;
  }
}

.textare-buttons {
  display: flex;
  gap: .5em;
  justify-content: end;
}

.selected-files-active,
.selected-files-inactive {
  margin: 0 1em;
  padding: .5em 0;
  border-top: 1px solid #777;
  color: #777;
}

.selected-files-inactive {
  display: block;
  width: -webkit-fill-available;
  text-align: left;
}

.selected-files-active {
  li {
    display: flex;
    align-items: center;
  }

  // This is not perfect, symbols (like '_') in the path name moves.
  .left-ellipsis {
    flex: 1 0 0;
    width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;
  }
}

.file-picker {
  padding: 0.5em;
}
</style>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { requestContentFromVscode } from '@/shared/utils';
import { MessageFromWebviewAction, type FileListItem, type MessageToWebviewListFilePayload } from '@/shared/types';

const emit = defineEmits<{
  (e: 'add', value: string): void
}>();

onMounted(() => {
  _focusInput();
});

const query = ref('');
const isFocused = ref(false);
const listContainer = ref<HTMLElement|null>(null);
const inputEl = ref<HTMLInputElement|null>(null);
const highlightIndex = ref(0);
const currentDir = computed(() => {
  return `/${query.value}`.replace(/\/\//g, '/').split('/').slice(0, -1).join('/').trim();
});
const responseDir = ref<string|undefined>();
const responseItems = ref<FileListItem[]>([]);

const listToShow = computed(() => {
  highlightIndex.value = 0;
  if (responseDir.value !== currentDir.value) {
    fetchNewDir();
    return [];
  }

  const splitted = query.value.split('/');
  const remainingQuery = splitted[splitted.length - 1];
  return responseItems.value.filter((item) => {
    return item.name.match(new RegExp(remainingQuery, 'i'));
  })
});

async function fetchNewDir() {
  const {dir, items} = await requestContentFromVscode<MessageToWebviewListFilePayload>({
    action: MessageFromWebviewAction.REQUEST_FILE_LIST,
    payload: {path: currentDir.value},
  });
  responseDir.value = dir;
  responseItems.value = items;
}

function keyboardHandler(e: KeyboardEvent) {
  switch (e.key) {
    case 'ArrowUp':
      e.preventDefault();  // Do not move text cursor
      highlightIndex.value = highlightIndex.value > 0 ? highlightIndex.value - 1 : 0;
      _scrollActiveOptionIntoView();
      break;
    case 'ArrowDown':
      e.preventDefault();  // Do not move text cursor
      const lastAvailableIndex = listToShow.value.length - 1;
      highlightIndex.value = highlightIndex.value < lastAvailableIndex - 1 ? highlightIndex.value + 1 : lastAvailableIndex;
      _scrollActiveOptionIntoView();
      break;
    case 'Tab':
      e.preventDefault();  // Do not just focus
    case 'Enter':
      const highlightedItem = listToShow.value[highlightIndex.value];
      if (highlightedItem) {
        handleItemClick(highlightedItem);
      }
      break;
  }
}

function _scrollActiveOptionIntoView() {
  if (!listContainer.value) return;
  const el = listContainer.value.querySelectorAll('vscode-option')[highlightIndex.value];
  if (!el) return;

  const elRect = el.getBoundingClientRect();
  const parentRect = listContainer.value.getBoundingClientRect();
  if (elRect.top > parentRect.top && elRect.bottom < parentRect.bottom) return;

  el.scrollIntoView({behavior: 'smooth', block: 'nearest'});
}

function _focusInput() {
  inputEl.value?.focus();
}

function handleItemClick(item: FileListItem) {
  query.value = `${currentDir.value}/${item.name}${item.isDir ? '/' : ''}`.replace(/(\/\/)/g, '/').replace(/^\//, '');
  if (!item.isDir) {
    emit('add', query.value);
  }
  _focusInput();
}

let timer: number|undefined;
function delayedBlur() {
  // If set it immediately, the dropdown DOM would be destroyed before the click is triggered.
  timer = setTimeout(() => {
    isFocused.value = false;
  }, 200);
}

function focus() {
  clearTimeout(timer);
  isFocused.value = true;
}
</script>

<template>
  <div class="config">
    <vscode-text-field ref="inputEl" v-model="query" autofocus
      @keydown="keyboardHandler" @focus="focus" @blur="delayedBlur">Pick files to include</vscode-text-field>
    <div class="options-placeholder">
      <div v-if="isFocused" class="options-scroll-container" ref="listContainer">
        <vscode-option v-for="(item, index) of listToShow" :selected="index === highlightIndex" @click.capture="handleItemClick(item)">
          {{ item.name }} {{ item.isDir ? '+' : '' }}
        </vscode-option>
        <vscode-option v-if="!listToShow.length" disabled>No matching file/dir</vscode-option>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
vscode-option {
  display: block;
  padding: 0 .5em;
}

.options-placeholder {
  --options-height: 6em;
  height: var(--options-height);
}

.options-scroll-container {
  overflow: auto;
  max-height: var(--options-height);
  border: 1px solid var(--focus-border);
}

vscode-text-field {
  width: 100%;
}
</style>

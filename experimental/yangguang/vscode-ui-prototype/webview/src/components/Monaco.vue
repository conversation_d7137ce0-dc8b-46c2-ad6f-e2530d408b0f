<script setup lang="ts">
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import { ref, onMounted, watch } from 'vue';
import { nonReactiveStore } from '@/shared/stores';

const props = defineProps<{
  codeOnly: string;  // i.e. without language name that might be in the markdown code block
  language: string;
  isActive?: boolean;
}>();

const CODE_LINE_HEIGHT_IN_PIXEL = 18;
const MIN_LINES = 8;
const HEIGHT_BUFFER_LINES = 2;

const editorDom = ref(null);
const codeHeight = ref(0);

onMounted(() => {
  codeHeight.value = getHeight(props.codeOnly);
  const editor = monaco.editor.create(editorDom.value!, {
    value: props.codeOnly.trim() + '\n',
    language: props.language,
    readOnly: !props.isActive,
    theme: 'vs-dark',
    automaticLayout: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    scrollbar: {alwaysConsumeMouseWheel: false},
    lineNumbersMinChars: 2,
  });
  if (props.isActive) {
    nonReactiveStore.currentEditingEditor = editor;
  }

  // Auto update the height if user is typing.
  editor.onDidChangeModelContent(() => {
    codeHeight.value = getHeight(editor.getValue());
  });

  // Auto update the content and the height when streaming.
  watch(props, (newProps) => {
    const model = editor.getModel();
    if (model) {
      model.setValue(newProps.codeOnly);
      monaco.editor.setModelLanguage(model, newProps.language);
    }
    codeHeight.value = getHeight(newProps.codeOnly);
  });
});

function getHeight(code: string): number {
  return Math.max(MIN_LINES, code.split('\n').length + HEIGHT_BUFFER_LINES) * CODE_LINE_HEIGHT_IN_PIXEL;
}
</script>

<template>
  <div ref="editorDom" :style="{'height': `${codeHeight}px`}"></div>
</template>

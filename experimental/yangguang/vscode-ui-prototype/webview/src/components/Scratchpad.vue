<script setup lang="ts">
import { type TaskScratchpadContent, MessageFromWebviewAction, type CodeVersionContent } from "@/shared/types";
import Monaco from "./Monaco.vue";
import { computed } from "vue";
import { useTasksStore, nonReactiveStore } from '@/shared/stores';
import { EMPTY_CODE_VERSION_CONTENT, sendMessageToVscode } from '@/shared/utils';
import { parseLanguage } from '@/shared/pure-shared-utils';
const taskStore = useTasksStore();

const props = defineProps<{
  scratchpad: TaskScratchpadContent;
  isActive?: boolean;
  activateFn: () => void;
}>();

const activeCodeMessage = computed(() => {
  return props.scratchpad.codeVersions[props.scratchpad.activeCodeVersionIndex];
});

const disableButtons = computed(() => {
  return !activeCodeMessage.value || activeCodeMessage.value.content === '...'
});

function previousCode() {
  if (props.scratchpad.activeCodeVersionIndex > 0) {
    props.scratchpad.activeCodeVersionIndex --;
  }
  if (taskStore.activeScratchpadId === props.scratchpad.id) {
    taskStore.query = activeCodeMessage.value.instruction;
  }
}

function nextCode() {
  if (props.scratchpad.activeCodeVersionIndex < props.scratchpad.codeVersions.length - 1) {
    props.scratchpad.activeCodeVersionIndex ++;
  }
  if (taskStore.activeScratchpadId === props.scratchpad.id) {
    taskStore.query = activeCodeMessage.value.instruction;
  }
}

function showInfo(content: string) {
  sendMessageToVscode({action: MessageFromWebviewAction.SHOW_INFO_BOX, payload: {content}});
}

async function copyToClipboard(code: string) {
  await navigator.clipboard.writeText(code);
  showInfo('Code has been copied to your clipboard.');
}

async function copyToEditor(code: string) {
  sendMessageToVscode({
    action: MessageFromWebviewAction.WRITE_TO_EDITOR,
    payload: {content: code},
  });
}

function rerun() {
  const taskId = taskStore.activeTask.taskId;
  const messageId = taskStore.activeScratchpad!.id;
  activeCodeMessage.value.content = '...';
  sendMessageToVscode({
    action: MessageFromWebviewAction.SEND_TASK_SCRATCHPAD,
    payload: {
      instruction: activeCodeMessage.value.instruction,
      previousCode: activeCodeMessage.value.previousCode,
      taskId,
      messageId,
      id: activeCodeMessage.value.id,
      attachedFiles: taskStore.activeTask.attachedFiles,
    }
  });
}

function getActiveContent(activeCodeMessage: CodeVersionContent) {
  return props.isActive ? nonReactiveStore.getEditorContent() : parseLanguage(activeCodeMessage!.content).codeOnly;
}
</script>

<template>
  <div class="code-wrapper" :class="{'active': isActive}">
    <div v-if="isActive" class="code-header">
      Scratch Pad<span v-if="isActive">&nbsp;(Editable)</span>
      <div class="_grow"></div>
      <vscode-button @click="previousCode" appearance="icon" aria-label="Previous code" :disabled="scratchpad.activeCodeVersionIndex <= 0">
        <span class="codicon codicon-chevron-left"></span>
      </vscode-button>
      <b>{{ scratchpad.activeCodeVersionIndex + 1 }} / {{ scratchpad.codeVersions.length }}</b>
      <vscode-button @click="nextCode" appearance="icon" aria-label="Next code" :disabled="scratchpad.activeCodeVersionIndex >= scratchpad.codeVersions.length - 1">
        <span class="codicon codicon-chevron-right"></span>
      </vscode-button>
    </div>
    <!-- I'm lazy to handle the internal state of the editor, so here I set a very specific render-key, so the editor is rerendered for any important changes.
        This obviously has performance issue, but we only rerender one at a time, so shouldn't be too bad. -->
    <Monaco
        :key="`${taskStore.activeTask.taskId}-${scratchpad.id}-${scratchpad.activeCodeVersionIndex}-${isActive}-${activeCodeMessage!.content === EMPTY_CODE_VERSION_CONTENT}`"
        :code-only="parseLanguage(activeCodeMessage!.content).codeOnly"
        :language="parseLanguage(activeCodeMessage!.content).language"
        :is-active="isActive" />
    <div class="footer">
      <vscode-button appearance="icon" aria-label="Copy to clipboard" title="Copy to clipboard" :disabled="disableButtons"
          @click="copyToClipboard(getActiveContent(activeCodeMessage))">
        <span class="codicon codicon-copy"></span>
      </vscode-button>
      <vscode-button appearance="icon" aria-label="Insert to editor" title="Insert to editor" :disabled="disableButtons"
          @click="copyToEditor(getActiveContent(activeCodeMessage))">
        <span class="codicon codicon-insert"></span>
      </vscode-button>
      <div class="_grow"></div>
      <vscode-button appearance="icon" class="feedback -good" aria-label="Awesome answer" title="Awesome answer" :disabled="disableButtons"
          @click="showInfo('You voted this is an awesome answer!')">
        <img src="https://jojultdzeeymbwqvhivt.supabase.co/storage/v1/object/public/augment-prototype/icon-good.svg" alt="">
      </vscode-button>
      <vscode-button appearance="icon" class="feedback -bad" aria-label="Bad answer" title="Bad answer" :disabled="disableButtons"
          @click="showInfo('You voted this is not a good answer!')">
        <img src="https://jojultdzeeymbwqvhivt.supabase.co/storage/v1/object/public/augment-prototype/icon-bad.svg" alt="">
      </vscode-button>
      <vscode-button v-if="isActive && activeCodeMessage.instruction" appearance="icon" aria-label="Regenerate" title="Regenerate" :disabled="disableButtons"
          @click="rerun">
        <span class="codicon codicon-refresh"></span>
      </vscode-button>
      <vscode-button v-else appearance="icon" aria-label="Edit in scratch pad" title="Edit in scratch pad" :disabled="disableButtons"
          @click="activateFn">
        <span class="codicon codicon-edit"></span>
      </vscode-button>
    </div>
  </div>
  <details v-if="isActive && activeCodeMessage.action">
    <summary>debug info</summary>
    <p>Action: {{activeCodeMessage.action}}</p>
    <p>Model: {{activeCodeMessage.model}}</p>
    <p v-if="activeCodeMessage.status">API status: {{activeCodeMessage.status}}</p>
    <a :href="`https://supabase.com/dashboard/project/jojultdzeeymbwqvhivt/editor/79538?sort=created:desc&filter=id:eq:${activeCodeMessage.id}`" target="_blank">Log link</a>
  </details>
</template>

<style lang="scss" scoped>
.code-wrapper {
  margin: 2em 0;
  border: 1px solid #444;
  overflow: hidden;
}

.code-header {
  display: flex;
  align-items: center;
}

.active {
  border-color: var(--scratchpad-color);

  .code-header {
    background: var(--scratchpad-color);
    color: black;
    padding: .2em .6em;

    .codicon {
      color: black;
    }
  }
  .footer {
    border-top-color: var(--scratchpad-color);
  }
}

vscode-button {
  margin-left: auto;

  &:disabled {
    opacity: .5;
  }
}

.footer {
  display: flex;
  padding: 0.5em;
  gap: 0.8em;
  border-top: 1px solid transparent;
}

.feedback {
  border-radius: 100%;

  &.-good {
    background: #B5D46E;
  }

  &.-bad {
    background: #F35959;
  }
}

details {
  margin-top: -2em;
  margin-bottom: 2em;
}
</style>

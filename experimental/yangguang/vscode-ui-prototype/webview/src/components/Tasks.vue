<script setup lang="ts">
import { useTasksStore } from '@/shared/stores';
import { TaskMessageType } from "@/shared/types";
import ContentChatRender from "./ContentChatRender.vue";
import Scratchpad from "./Scratchpad.vue";
const taskStore = useTasksStore();
</script>

<template>
  <template v-for="(message, index) of taskStore.activeTask.messages">
    <div v-if="message.type === TaskMessageType.CHAT">
      <ContentChatRender :message="message" allowScratchpad :messageIndex="index" />
    </div>
    <div v-if="message.type === TaskMessageType.RETREIVAL">
      <ContentChatRender :message="message" retrievalMessage />
    </div>
    <div v-if="message.type === TaskMessageType.SCRATCH_PAD && message.id !== taskStore.temporaryScratchpad?.id" class="editor-wrapper">
      <Scratchpad :scratchpad="message" :is-active="taskStore.activeScratchpadId === message.id" :activateFn="() => taskStore.activateScratchpad(message.id)" />
    </div>
  </template>
</template>

<style scoped>
.editor-wrapper {
  position: relative;
}
</style>

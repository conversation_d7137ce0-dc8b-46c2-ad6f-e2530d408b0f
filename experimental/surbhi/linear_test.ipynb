{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method AugmentModelClient.chat of AugmentModelClient(url=https://staging-shard-0.api.augmentcode.com/, request_session_id=ffaeffeb-4e45-48b8-86ca-dcd11a292c1a, last_request_id=None, model=claude-sonnet-3-5-16k-v10-chat)>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.augment_client.client import AugmentClient\n", "from pathlib import Path\n", "\n", "import requests\n", "\n", "\n", "AUGMENT_API_TOKEN = \"SECRET\"\n", "client = AugmentClient(\n", "    url=\"https://staging-shard-0.api.augmentcode.com/\",\n", "    token=AUGMENT_API_TOKEN,\n", ")\n", "model_client = client.client_for_model(\"claude-sonnet-3-5-16k-v10-chat\")\n", "model_client.chat"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class LinearClient:\n", "    def __init__(self, api_key):\n", "        self.api_key = api_key\n", "        self.endpoint = \"https://api.linear.app/graphql\"\n", "        self.headers = {\n", "            \"Authorization\": self.api_key,\n", "            \"Content-Type\": \"application/json\",\n", "        }\n", "\n", "    def execute_query(self, query, variables=None):\n", "        response = requests.post(\n", "            self.endpoint,\n", "            json={\"query\": query, \"variables\": variables},\n", "            headers=self.headers,\n", "        )\n", "        response.raise_for_status()\n", "        return response.json()\n", "\n", "    def get_issue_info(self, issue_id):\n", "        query = \"\"\"\n", "        query($issueId: String!) {\n", "            issue(id: $issueId) {\n", "                id\n", "                title\n", "                description\n", "                assignee {\n", "                    displayName\n", "                }\n", "                creator {\n", "                    displayName\n", "                }\n", "                comments {\n", "                    nodes {\n", "                        body\n", "                        user {\n", "                            displayName\n", "                        }\n", "                    }\n", "                }\n", "                parent {\n", "                    title\n", "                    description\n", "                }\n", "                team {\n", "                    name\n", "                }\n", "                project {\n", "                    name\n", "                }\n", "            }\n", "        }\n", "        \"\"\"\n", "        variables = {\"issueId\": issue_id}\n", "        return self.execute_query(query, variables)\n", "\n", "    def post_comment(self, issue_id, comment_body):\n", "        mutation = \"\"\"\n", "        mutation($input: CommentCreateInput!) {\n", "            commentCreate(input: $input) {\n", "                success\n", "                comment {\n", "                    id\n", "                }\n", "            }\n", "        }\n", "        \"\"\"\n", "        variables = {\"input\": {\"issueId\": issue_id, \"body\": comment_body}}\n", "        return self.execute_query(mutation, variables)\n", "\n", "\n", "linear_client = LinearClient(\"\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Issue info: {'data': {'issue': {'id': '0a3cc9ce-8c01-41ad-8019-ba7cc2bb02a2', 'title': 'Update sync checkpoints job', 'description': \"@mi<PERSON><PERSON> said:\\n\\n> Hey, I'd like to follow up on the syncing. After seeing a few engineers go through deploying models, I think the current method is not quite working out. There's a lot of friction in waiting for the next deploy, and also figuring out when it's actually safe to commit the PR that deploys the model.\\n>\\n> I don't know what the process is for improving the current solution, but I think either\\n> • having a prod-checkpoints folder where a cronjob copies, or\\n> • making it so that you can add the checkpoint sync in the same PR as you deploy the model, and the deploy just makes sure the syncing happens first\\n> would work\", 'assignee': {'displayName': 'surbhi'}, 'creator': {'displayName': 'surbhi'}, 'comments': {'nodes': []}, 'parent': None, 'team': {'name': 'Engineering'}, 'project': None}}}\n"]}], "source": ["# Custom system prompt\n", "SYSTEM_PROMPT = \"\"\"\n", "You are an AI assistant integrated with Linear, a project management tool. \n", "Your task is to analyze issue information and generate appropriate responses or comments.\n", "Provide concise, relevant, and helpful responses based on the issue details provided. Provide just the comment to be posted.\n", "\"\"\"\n", "\n", "FIRST_COMMENT_PROMPT = \"\"\"\n", "Please provide a brief summary of the issue and any relevant context.\n", "\n", "Focus only on specific implementation details from the codebase. Format your response using whatever applies from these sections:\n", "\n", "## 📋 *Implementation Plan*\n", "- Key files/services that need changes (with full paths), dependencies that will be affected, new components needed (if any)\n", "## 🔨 *Technical Details*\n", "- Specific code changes needed\n", "- Required test coverage\n", "## ⚠️ *Gotchas & Dependencies*\n", "- Potential breaking changes, Performance impacts, etc.\n", "If critical implementation details are missing from the ticket, list them as bullet points under a \"Missing Information\" section.\n", "\"\"\"\n", "\n", "SECOND_COMMENT_PROMPT = \"\"\"\n", "Answer the user's question based on the issue information provided and converstation history. \n", "\"\"\"\n", "\n", "\n", "def handle_issue(issue_id):\n", "    # Get issue info\n", "    issue_info = linear_client.get_issue_info(issue_id)\n", "    print(f\"Issue info: {issue_info}\")\n", "\n", "    # Generate comment using AugmentClient\n", "    issue_prompt = f\"\"\"\n", "    Based on the following issue information, generate a helpful comment:\n", "\n", "    Title: {issue_info['data']['issue']['title']}\n", "    Description: {issue_info['data']['issue']['description']}\n", "    Assignee: {issue_info['data']['issue']['assignee']['displayName'] if issue_info['data']['issue']['assignee'] else 'Unassigned'}\n", "    Team: {issue_info['data']['issue']['team']['name']}\n", "    Project: {issue_info['data']['issue']['project']['name'] if issue_info['data']['issue']['project'] else 'No project'}\n", "    \"\"\"\n", "\n", "    chat_history = []\n", "    replied = False\n", "    if issue_info[\"data\"][\"issue\"][\"comments\"][\"nodes\"]:\n", "        for comment in issue_info[\"data\"][\"issue\"][\"comments\"][\"nodes\"]:\n", "            chat_history.append(\n", "                {\n", "                    \"request_message\": comment[\"body\"],\n", "                    \"response_text\": comment[\"user\"][\"displayName\"],\n", "                }\n", "            )\n", "            if comment[\"user\"][\"displayName\"] == \"surbhi\":\n", "                replied = True\n", "\n", "    if not replied:\n", "        full_message = f\"\"\"\n", "        {SYSTEM_PROMPT}\n", "\n", "        {FIRST_COMMENT_PROMPT}\n", "\n", "        {issue_prompt}\n", "        \"\"\"\n", "    else:\n", "        full_message = f\"\"\"\n", "        {SYSTEM_PROMPT}\n", "\n", "        {SECOND_COMMENT_PROMPT}\n", "\n", "        {issue_prompt}\n", "        \"\"\"\n", "\n", "    response = model_client.chat(\n", "        selected_code=\"\",\n", "        message=full_message,\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        path=\"\",\n", "        chat_history=chat_history,\n", "        blobs={\n", "            \"checkpoint_id\": \"ab4dfceaafeb040d0d430e4b7c2fa025fbd86cd4532e7c6c7c266f45f7ef0de7\",\n", "            \"added_blobs\": [],\n", "            \"deleted_blobs\": [],\n", "        },\n", "    )\n", "\n", "    generated_comment = response.text.strip()\n", "    linear_client.post_comment(issue_id, generated_comment)\n", "\n", "\n", "handle_issue(\"AU-5602\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
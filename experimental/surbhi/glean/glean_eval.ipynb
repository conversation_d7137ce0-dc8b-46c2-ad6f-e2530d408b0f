{"cells": [{"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [], "source": ["from base.augment_client.client import AugmentClient\n", "from pathlib import Path\n", "import pathlib\n", "import json\n", "import requests\n", "import uuid"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [], "source": ["api_proxy = \"https://staging-shard-0.api.augmentcode.com\"\n", "token = \"SECRET\""]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["class Document:\n", "    def __init__(self, document_id, data_source, document_type, title, url, content=\"\"):\n", "        self.document_id = document_id\n", "        self.data_source = data_source\n", "        self.document_type = document_type\n", "        self.title = title\n", "        self.url = url\n", "        self.content = content\n", "\n", "    def __str__(self):\n", "        return f\"Document ID: {self.document_id}, Title: {self.title}, URL: {self.url}\"\n", "\n", "\n", "def call_glean(query: str):\n", "    r = requests.post(\n", "        f\"{api_proxy}/grpc-debug/invoke\",\n", "        json={\n", "            \"endpoint\": \"https://glean-svc:50051\",\n", "            \"service\": \"glean.Glean\",\n", "            \"method\": \"Search\",\n", "            \"data\": {\"query\": query},\n", "        },\n", "        headers={\n", "            \"Authorization\": f\"Bearer {token}\",\n", "            \"x-request-id\": str(uuid.uuid4()),\n", "            \"x-request-session-id\": str(uuid.uuid4()),\n", "        },\n", "    )\n", "    r.raise_for_status()\n", "    if \"error\" in r.json():\n", "        print(r.json()[\"error\"])\n", "        return []\n", "\n", "    response_json = json.loads(r.json()[\"response_json\"])\n", "    documents = []\n", "\n", "    for doc in response_json[0].get(\"documents\", []):\n", "        document = Document(\n", "            document_id=doc.get(\"document_id\", \"\"),\n", "            data_source=doc.get(\"data_source\", \"\"),\n", "            document_type=doc.get(\"document_type\", \"\"),\n", "            title=doc.get(\"title\", \"\"),\n", "            url=doc.get(\"url\", \"\"),\n", "            content=doc.get(\"content\", \"\"),\n", "        )\n", "        documents.append(document)\n", "\n", "    return documents\n", "\n", "\n", "def get_data_source(doc: Document):\n", "    \"\"\"Map the data source to the corresponding integer value in the glean proto for the enum DataSource .\"\"\"\n", "    if doc.data_source == \"SLACK\":\n", "        return 1\n", "    elif doc.data_source == \"NOTION\":\n", "        return 2\n", "    elif doc.data_source == \"LINEAR\":\n", "        return 3\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [{"data": {"text/plain": ["'b2e435d49efe5c84b82f5e87c7af879bf0f51b0cf7f0ae1724c2b0c86e91a188'"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.eval.harness import utils\n", "from base.blob_names.python.blob_names import encode_blob_name\n", "import base64\n", "\n", "# Repo that should be used for Augment QA Questions\n", "repo_path = \"/mnt/efs/augment/data/processed/augment_qa/v3/repos/augment_jul_10_2024_f5efe2e.jsonl.zst\"\n", "blobNames = [d[\"id\"] for d in utils.read_jsonl_zst(repo_path)]\n", "\n", "\n", "def checkpoint_eval_blobs(blobNames: list[str]):\n", "    blob_bytes = [encode_blob_name(name) for name in sorted(blobNames)]\n", "\n", "    r = requests.post(\n", "        f\"{api_proxy}/grpc-debug/invoke\",\n", "        json={\n", "            \"endpoint\": \"https://content-manager-svc:50051\",\n", "            \"service\": \"content_manager.ContentManager\",\n", "            \"method\": \"CheckpointBlobs\",\n", "            \"data\": {\n", "                \"blobs\": {\n", "                    \"added\": [base64.b64encode(b).decode(\"ascii\") for b in blob_bytes]\n", "                }\n", "            },\n", "        },\n", "        headers={\n", "            \"Authorization\": f\"Bearer {token}\",\n", "            \"x-request-id\": str(uuid.uuid4()),\n", "            \"x-request-session-id\": str(uuid.uuid4()),\n", "        },\n", "    )\n", "    r.raise_for_status()\n", "    if \"error\" in r.json():\n", "        print(r.json()[\"error\"])\n", "    else:\n", "        response_json = json.loads(r.json()[\"response_json\"])\n", "        return response_json[0][\"checkpointId\"]\n", "\n", "\n", "AUGMENT_QA_CHECKPOINT_ID = checkpoint_eval_blobs(blobNames)\n", "AUGMENT_QA_CHECKPOINT_ID"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [], "source": ["from base.augment_client.client import Exchange, ExchangeJson\n", "\n", "\n", "def post_chat_request(\n", "    message: str,\n", "    checkpoint_id: str,\n", "    glean_documents: list[Document] = [],\n", "    previous_thread_messages: list[str] = [],\n", "    chat_history: list[Exchange] = [],\n", "    slack_formatter: bool = True,\n", "):\n", "    # Create the chat request\n", "    chat_request = {\n", "        \"modelName\": \"claude-sonnet-3-5-16k-v8-chat\",\n", "        \"message\": f\"U12345: {message}\",\n", "        \"blobs\": [\n", "            {\n", "                \"baselineCheckpointId\": checkpoint_id,\n", "            }\n", "        ],\n", "    }\n", "\n", "    if slack_formatter:\n", "        chat_request[\"promptFormatterName\"] = \"slackbot\"\n", "\n", "        # Create the SlackbotChatMetadata\n", "        chat_metadata = {\n", "            \"botId\": \"U54321\",\n", "            \"channelName\": \"engineering\",\n", "            \"previousThreadMessages\": previous_thread_messages,\n", "            \"repos\": [\n", "                {\n", "                    \"repoOwner\": \"augmentCode\",\n", "                    \"repoName\": \"augment\",\n", "                }\n", "            ],\n", "            \"allRepos\": [\n", "                {\n", "                    \"repoOwner\": \"augmentCode\",\n", "                    \"repoName\": \"augment\",\n", "                }\n", "            ],\n", "            \"gleanDocuments\": [\n", "                {\n", "                    \"documentId\": doc.document_id,\n", "                    \"title\": doc.title,\n", "                    \"content\": doc.content,\n", "                    \"dataSource\": get_data_source(doc),\n", "                    \"url\": doc.url,\n", "                }\n", "                for doc in glean_documents\n", "            ],\n", "        }\n", "\n", "        # Convert metadata to JSON string\n", "        chat_metadata_bytes = json.dumps(chat_metadata)\n", "        chat_request[\"chatHistory\"] = [{\"requestMessage\": chat_metadata_bytes}]\n", "    else:\n", "        chat_request[\"chatHistory\"] = [\n", "            ExchangeJson.from_exchange(exchange).__dict__ for exchange in chat_history\n", "        ]\n", "\n", "    request_id = str(uuid.uuid4())\n", "\n", "    r = requests.post(\n", "        f\"{api_proxy}/grpc-debug/invoke\",\n", "        json={\n", "            \"endpoint\": \"https://chat-claude-sonnet-3-5-16k-v8-chat-svc:50051\",\n", "            \"service\": \"chat.Chat\",\n", "            \"method\": \"Chat\",\n", "            \"data\": chat_request,\n", "        },\n", "        headers={\n", "            \"Authorization\": f\"Bearer {token}\",\n", "            \"x-request-id\": request_id,\n", "            \"x-request-session-id\": str(uuid.uuid4()),\n", "        },\n", "    )\n", "    r.raise_for_status()\n", "    if \"error\" in r.json():\n", "        print(r.json()[\"error\"])\n", "        return \"\", \"\"\n", "    else:\n", "        response_json = json.loads(r.json()[\"response_json\"])\n", "        return response_json[0][\"text\"], request_id"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 100 examples in AugmentQATask\n"]}], "source": ["from research.eval.harness.tasks.augment_qa_task import AugmentQATask\n", "\n", "task = AugmentQATask.from_yaml_config(\n", "    {\n", "        \"dataset_path\": \"/mnt/efs/augment/data/processed/augment_qa/v3\",\n", "        \"html_report_output_dir\": \"/mnt/efs/augment/public_html/michiel/augmentqa_v3\",\n", "    }\n", ")\n", "\n", "print(f\"There are {len(task)} examples in {task.name}\")"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["def exchanges_to_strings(exchanges: list[Exchange]) -> list[str]:\n", "    \"\"\"Convert a list of Exchange objects into a list of strings.\"\"\"\n", "    lst = []\n", "    for exchange in exchanges:\n", "        lst.append(f\"U12345: {exchange.request_message}\")\n", "        lst.append(f\"U54321: {exchange.response_text}\")\n", "\n", "    return lst"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2fc0e6c3dcb1b1ae5979e213faa3f584442f3b83029375bd5f9322d0051d3911'"]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["# To answer the questions in the dataset for glean (NOT AUGMENT QA)\n", "examples_path = Path(\n", "    \"/home/<USER>/augment/research/data/external_context/glean_examples.json\"\n", ")\n", "with open(examples_path) as f:\n", "    glean_samples = json.load(f)\n", "\n", "\n", "# Checkpoint for glean questions is a current staging checkpoint without the glean_examples.json doc\n", "staging_checkpoint = \"a9ff008d922f39a94483c1ee7a55b669596a2d5d2789d8fd8b20c952eb67a362\"\n", "glean_examples_blob = \"863fe64e4e9e88071dfa23fd80f643ed1d4516de251aad40d4b3eb82ee7a9a73\"\n", "\n", "\n", "def checkpoint_remove_blob(baseline_checkpoint_id: str, blob_name_to_remove: str):\n", "    blob_to_remove_bytes = encode_blob_name(blob_name_to_remove)\n", "    blob_bytes = [base64.b64encode(blob_to_remove_bytes).decode(\"ascii\")]\n", "\n", "    r = requests.post(\n", "        f\"{api_proxy}/grpc-debug/invoke\",\n", "        json={\n", "            \"endpoint\": \"https://content-manager-svc:50051\",\n", "            \"service\": \"content_manager.ContentManager\",\n", "            \"method\": \"CheckpointBlobs\",\n", "            \"data\": {\n", "                \"blobs\": {\n", "                    \"baselineCheckpointId\": baseline_checkpoint_id,\n", "                    \"deleted\": blob_bytes,\n", "                }\n", "            },\n", "        },\n", "        headers={\n", "            \"Authorization\": f\"Bearer {token}\",\n", "            \"x-request-id\": str(uuid.uuid4()),\n", "            \"x-request-session-id\": str(uuid.uuid4()),\n", "        },\n", "    )\n", "    r.raise_for_status()\n", "    if \"error\" in r.json():\n", "        print(r.json()[\"error\"])\n", "    else:\n", "        response_json = json.loads(r.json()[\"response_json\"])\n", "        return response_json[0][\"checkpointId\"]\n", "\n", "\n", "GLEAN_CHECKPOINT_ID = checkpoint_remove_blob(staging_checkpoint, glean_examples_blob)\n", "GLEAN_CHECKPOINT_ID"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Do folks have a sense how long is bazel run -c opt //services/deploy:dev_deploy expected to take?\n", "EDIT: it's a delete job. after I chose \"delete\", it has been hanging for more than 20 mins at the time of this question.\n", "['ingress', 'hours', 'api-proxy']\n", "Glean recall:  0.6666666666666666\n", "No Glean recall:  0.0\n", "No Slack recall:  0.3333333333333333\n", "I need a refresher... is there any trick to get multi-GPU execution working on a devpod? Or should research-init.sh be sufficient?\n", "Context is that I just switched over to a devpod on GCP and can't get multi-GPU execution (e.g. of profile_models or //base/fastforward:parallel_multigpu_test) working. It seems to time out on communication.\n", "['nccl-fastsocket', 'LD_LIBRARY_PATH']\n", "Glean recall:  1.0\n", "No Glean recall:  0.0\n", "No Slack recall:  0.0\n", "Results written to:\n", "- Text file: glean_eval_results_20250114_034902.txt\n", "- JSON file: glean_eval_results+_20250114_034902.json\n", "- CSV file: glean_eval_results_20250114_034902.csv\n", "\n", "Summary:\n", "Glean Total: 1.667\n", "No Glean Total: 0.000\n", "No Slack Total: 0.333\n"]}], "source": ["import csv\n", "from dataclasses import asdict, dataclass\n", "from typing import List, Optional\n", "\n", "from research.eval.harness.tasks.augment_qa_task import count_keywords\n", "\n", "\n", "@dataclass\n", "class EvalResult:\n", "    question: str\n", "    glean_documents: List[dict]\n", "    answer_with_glean: str\n", "    answer_without_glean: str\n", "    answer_without_slack: str\n", "    keywords: List[str]\n", "    glean_recall: float\n", "    no_glean_recall: float\n", "    no_slack_recall: float\n", "    glean_request_url: str\n", "    no_glean_request_url: str\n", "    no_slack_request_url: str\n", "\n", "\n", "def process_samples(samples, is_augment_qa: bool):\n", "    # Create output files\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = f\"glean_eval_results_{timestamp}.txt\"\n", "    output_csv = f\"glean_eval_results_{timestamp}.csv\"\n", "    output_json = f\"glean_eval_results+_{timestamp}.json\"\n", "\n", "    support_url_base = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "\n", "    results = []\n", "    glean_total = 0\n", "    without_glean_total = 0\n", "    without_slack_total = 0\n", "\n", "    checkpoint = AUGMENT_QA_CHECKPOINT_ID if is_augment_qa else GLEAN_CHECKPOINT_ID\n", "\n", "    with open(output_file, \"w\") as f:\n", "        for sample in samples:\n", "            # Handle different data structures based on flag\n", "            if is_augment_qa:\n", "                message = sample.message\n", "                keywords = sample.keywords\n", "                previous_messages = exchanges_to_strings(sample.chat_history)\n", "            else:\n", "                message = sample[\"question\"]\n", "                keywords = sample[\"keywords\"]\n", "                previous_messages = []\n", "\n", "            print(message)\n", "            print(keywords)\n", "\n", "            f.write(\"=\" * 80 + \"\\n\")\n", "            f.write(f\"Question: {message}\\n\\n\")\n", "\n", "            previous_messages = []\n", "            glean_query = message\n", "\n", "            if len(previous_messages) > 0:\n", "                glean_query = f\"Here is the previous slack conversation:\\n {previous_messages}\\n\\n Here is the user question for you:\\n {sample.message}\"\n", "\n", "            docs = call_glean(glean_query)\n", "            f.write(\"Glean Documents Found:\\n\")\n", "            for doc in docs:\n", "                f.write(f\"- Title: {doc.title}\\n\")\n", "                f.write(f\"  URL: {doc.url}\\n\")\n", "\n", "            resp_glean, glean_rid = post_chat_request(\n", "                message,\n", "                checkpoint_id=checkpoint,\n", "                glean_documents=docs,\n", "                previous_thread_messages=previous_messages,\n", "            )\n", "            resp_no_glean, no_glean_rid = post_chat_request(\n", "                message,\n", "                checkpoint_id=checkpoint,\n", "                previous_thread_messages=previous_messages,\n", "            )\n", "\n", "            if is_augment_qa:\n", "                # pass in chat history for augment qa\n", "                resp_no_slack, no_slack_rid = post_chat_request(\n", "                    message,\n", "                    checkpoint_id=checkpoint,\n", "                    chat_history=sample.chat_history,\n", "                    slack_formatter=False,\n", "                )\n", "            else:\n", "                resp_no_slack, no_slack_rid = post_chat_request(\n", "                    message, checkpoint_id=checkpoint, slack_formatter=False\n", "                )\n", "\n", "            glean_request_url = support_url_base + glean_rid\n", "            no_glean_request_url = support_url_base + no_glean_rid\n", "            no_slack_request_url = support_url_base + no_slack_rid\n", "\n", "            f.write(\"Answer with Glean:\\n\")\n", "            f.write(f\"{resp_glean}\\n\\n\")\n", "            f.write(f\"{glean_request_url}\\n\\n\")\n", "\n", "            f.write(\"Answer without Glean:\\n\")\n", "            f.write(f\"{resp_no_glean}\\n\\n\")\n", "            f.write(f\"{no_glean_request_url}\\n\\n\")\n", "\n", "            f.write(\"Answer without Slack:\\n\")\n", "            f.write(f\"{resp_no_slack}\\n\\n\")\n", "            f.write(f\"{no_slack_rid}\\n\\n\")\n", "\n", "            n_keywords = float(len(set(keywords)))\n", "            n_matched_keywords_glean = count_keywords(resp_glean, keywords)\n", "            answer_keyword_recall_glean = n_matched_keywords_glean / n_keywords\n", "            glean_total += answer_keyword_recall_glean\n", "\n", "            n_matched_keywords_no_glean = count_keywords(resp_no_glean, keywords)\n", "            answer_keyword_recall_no_glean = n_matched_keywords_no_glean / n_keywords\n", "            without_glean_total += answer_keyword_recall_no_glean\n", "\n", "            n_matched_keywords_no_slack = count_keywords(resp_no_slack, keywords)\n", "            answer_keyword_recall_no_slack = n_matched_keywords_no_slack / n_keywords\n", "            without_slack_total += answer_keyword_recall_no_slack\n", "\n", "            f.write(f'Keywords: {\", \".join(keywords)}\\n')\n", "            f.write(f\"Glean recall: {answer_keyword_recall_glean:.3f}\\n\")\n", "            f.write(f\"No Glean recall: {answer_keyword_recall_no_glean:.3f}\\n\\n\")\n", "            f.write(f\"No Slack recall: {answer_keyword_recall_no_slack:.3f}\\n\\n\")\n", "\n", "            print(\"Glean recall: \", answer_keyword_recall_glean)\n", "            print(\"No Glean recall: \", answer_keyword_recall_no_glean)\n", "            print(\"No Slack recall: \", answer_keyword_recall_no_slack)\n", "\n", "            # Store result for JSON/CSV export\n", "            results.append(\n", "                EvalResult(\n", "                    question=message,\n", "                    glean_documents=[\n", "                        {\"title\": doc.title, \"url\": doc.url} for doc in docs\n", "                    ],\n", "                    answer_with_glean=resp_glean,\n", "                    answer_without_glean=resp_no_glean,\n", "                    answer_without_slack=resp_no_slack,\n", "                    keywords=keywords,\n", "                    glean_recall=answer_keyword_recall_glean,\n", "                    no_glean_recall=answer_keyword_recall_no_glean,\n", "                    no_slack_recall=answer_keyword_recall_no_slack,\n", "                    glean_request_url=glean_request_url,\n", "                    no_glean_request_url=no_glean_request_url,\n", "                    no_slack_request_url=no_slack_request_url,\n", "                )\n", "            )\n", "\n", "        f.write(\"=\" * 80 + \"\\n\")\n", "        f.write(\"Final Results:\\n\")\n", "        f.write(f\"Glean total recall: {glean_total}\\n\")\n", "        f.write(f\"No Glean total recall: {without_glean_total}\\n\")\n", "        f.write(f\"No Slack total recall: {without_slack_total}\\n\")\n", "\n", "    # Save JSON version\n", "    with open(output_json, \"w\") as f:\n", "        json.dump([asdict(r) for r in results], f, indent=2)\n", "\n", "    # Save CSV version with concatenated document details\n", "    with open(output_csv, \"w\", newline=\"\", encoding=\"utf-8\") as f:\n", "        writer = csv.DictWriter(\n", "            f,\n", "            fieldnames=[\n", "                \"question\",\n", "                \"answer_with_glean\",\n", "                \"answer_without_glean\",\n", "                \"answer_without_slack\",\n", "                \"glean_recall\",\n", "                \"no_glean_recall\",\n", "                \"no_glean_recall\",\n", "                \"no_slack_recall\",\n", "                \"recall_diff\",\n", "                \"num_keywords\",\n", "                \"keywords\",\n", "                \"num_glean_docs\",\n", "                \"glean_doc_titles\",\n", "                \"glean_doc_urls\",\n", "                \"glean_request_url\",\n", "                \"no_glean_request_url\",\n", "                \"no_slack_request_url\",\n", "            ],\n", "        )\n", "        writer.writeheader()\n", "\n", "        for result in results:\n", "            # Join titles and URLs with newlines for better spreadsheet readability\n", "            doc_titles = \"\\n\".join(doc[\"title\"] for doc in result.glean_documents)\n", "            doc_urls = \"\\n\".join(doc[\"url\"] for doc in result.glean_documents)\n", "\n", "            writer.writerow(\n", "                {\n", "                    \"question\": result.question,\n", "                    \"answer_with_glean\": result.answer_with_glean,\n", "                    \"answer_without_glean\": result.answer_without_glean,\n", "                    \"answer_without_slack\": result.answer_without_slack,\n", "                    \"glean_recall\": f\"{result.glean_recall:.3f}\",\n", "                    \"no_glean_recall\": f\"{result.no_glean_recall:.3f}\",\n", "                    \"no_slack_recall\": f\"{result.no_slack_recall:.3f}\",\n", "                    \"recall_diff\": f\"{(result.glean_recall - result.no_glean_recall):.3f}\",\n", "                    \"num_keywords\": len(result.keywords),\n", "                    \"keywords\": \", \".join(result.keywords),\n", "                    \"num_glean_docs\": len(result.glean_documents),\n", "                    \"glean_doc_titles\": doc_titles,\n", "                    \"glean_doc_urls\": doc_urls,\n", "                    \"glean_request_url\": result.glean_request_url,\n", "                    \"no_glean_request_url\": result.no_glean_request_url,\n", "                    \"no_slack_request_url\": result.no_slack_request_url,\n", "                }\n", "            )\n", "\n", "    print(\"Results written to:\")\n", "    print(f\"- Text file: {output_file}\")\n", "    print(f\"- JSON file: {output_json}\")\n", "    print(f\"- CSV file: {output_csv}\")\n", "\n", "    # Print summary statistics\n", "\n", "    print(\"\\nSummary:\")\n", "    print(f\"Glean Total: {glean_total:.3f}\")\n", "    print(f\"No Glean Total: {without_glean_total:.3f}\")\n", "    print(f\"No Slack Total: {without_slack_total:.3f}\")\n", "\n", "\n", "process_samples(task.samples, True)\n", "process_samples(glean_samples, False)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Which non-neural speculative decoding methods we have in the fastforward?\n", "Unknown blob names: ['d0b26c2138cd7d123af535eb25a02ff0d8680946ccb72889f522a6608afbc942', 'af54f7f5b5c0a353e12abf04b54d03dc066293ba6b2fe5353fda47dc2cd0e710', 'fec37a6c439c41e17f849ee0afe40180196edc3af24356419f5b8f26ad4e1ebb', '6e3200f94421393702dd36528ac70781c4544033100df21dc2e5a3da7f78604a', 'cb10eff2b35399f6a9d01d0683a88ceaac97bb244ed0f61be6b763031d7e7293', 'd76982a3a86785f80726c3fd540b44e915b6415b254043afc2de56520c46c9ff', 'f9dde37fe1b975bc08a4fc8d56e21c01765b4c31cf1e3cce02d26b9a6e1cdeda', '82b071e1b0506d35d8ef798f7e016c98bcae92ac53da5be142e971a941faf477', '458940d1f402f761c9671d221f0e05337e9aed4b976762078e0e8eb6d3fd40cd', 'af246594bbb047d01303c1763ad2df147fd9e992585f6b964125b84e87ad9377', 'cfaf37e5d0c3ef2edac6ed3d1fde40329a0edb28ab4d0901bad4ddeda8b91f3e']\n", "0.6666666666666666\n", "Where is perplexity distillation implemented in FastBackward?\n", "Unknown blob names: ['d0b26c2138cd7d123af535eb25a02ff0d8680946ccb72889f522a6608afbc942', 'af54f7f5b5c0a353e12abf04b54d03dc066293ba6b2fe5353fda47dc2cd0e710', 'fec37a6c439c41e17f849ee0afe40180196edc3af24356419f5b8f26ad4e1ebb', '6e3200f94421393702dd36528ac70781c4544033100df21dc2e5a3da7f78604a', 'cb10eff2b35399f6a9d01d0683a88ceaac97bb244ed0f61be6b763031d7e7293', 'd76982a3a86785f80726c3fd540b44e915b6415b254043afc2de56520c46c9ff', 'f9dde37fe1b975bc08a4fc8d56e21c01765b4c31cf1e3cce02d26b9a6e1cdeda', '82b071e1b0506d35d8ef798f7e016c98bcae92ac53da5be142e971a941faf477', '458940d1f402f761c9671d221f0e05337e9aed4b976762078e0e8eb6d3fd40cd', 'af246594bbb047d01303c1763ad2df147fd9e992585f6b964125b84e87ad9377', 'cfaf37e5d0c3ef2edac6ed3d1fde40329a0edb28ab4d0901bad4ddeda8b91f3e']\n", "1.0\n", "find me a script to change model parallel size for the fastbackward\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[90], line 34\u001b[0m\n\u001b[1;32m     31\u001b[0m f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m80\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     32\u001b[0m f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQuestion: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msample\u001b[38;5;241m.\u001b[39mmessage\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 34\u001b[0m resp, rid \u001b[38;5;241m=\u001b[39m \u001b[43mpost_IDE_chat_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43msample\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     36\u001b[0m f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnswer:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     37\u001b[0m f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresp\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[89], line 14\u001b[0m, in \u001b[0;36mpost_IDE_chat_request\u001b[0;34m(sample)\u001b[0m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost_IDE_chat_request\u001b[39m(\n\u001b[1;32m     12\u001b[0m     sample: ResearchChatPromptInput,\n\u001b[1;32m     13\u001b[0m ):\n\u001b[0;32m---> 14\u001b[0m     chat_response \u001b[38;5;241m=\u001b[39m \u001b[43mMODEL_CLIENT\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchat\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     15\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselected_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselected_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     16\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmessage\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmessage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[43m        \u001b[49m\u001b[43mprefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprefix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     18\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuffix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msuffix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     19\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     20\u001b[0m \u001b[43m        \u001b[49m\u001b[43mprefix_begin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprefix_begin\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     21\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuffix_end\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msuffix_end\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     22\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchat_history\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchat_history\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     23\u001b[0m \u001b[43m        \u001b[49m\u001b[43mblobs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\n\u001b[1;32m     24\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcheckpoint_id\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mCHECKPOINT_ID\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     25\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43madded_blobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     26\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdeleted_blobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     27\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     28\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_guided_blobs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muser_guided_blobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     29\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontext_code_exchange_request_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontext_code_exchange_request_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     30\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_guidelines\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muser_guidelines\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     31\u001b[0m \u001b[43m        \u001b[49m\u001b[43mworkspace_guidelines\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mworkspace_guidelines\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     32\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m chat_response\u001b[38;5;241m.\u001b[39mcheckpoint_not_found:\n\u001b[1;32m     35\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCheckpoint not found: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mCHECKPOINT_ID\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/augment/base/augment_client/client.py:1471\u001b[0m, in \u001b[0;36mAugmentModelClient.chat\u001b[0;34m(self, selected_code, message, prefix, suffix, path, blob_names, lang, blob_name, prefix_begin, suffix_end, blobs, chat_history, user_guided_blobs, context_code_exchange_request_id, external_source_ids, user_guidelines, workspace_guidelines, timeout, disable_auto_external_sources, retry_policy, support_raw_output)\u001b[0m\n\u001b[1;32m   1465\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1466\u001b[0m     json[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mchat_history\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m   1467\u001b[0m         exchange \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(exchange, \u001b[38;5;28mdict\u001b[39m) \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m asdict(exchange)\n\u001b[1;32m   1468\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m exchange \u001b[38;5;129;01min\u001b[39;00m chat_history\n\u001b[1;32m   1469\u001b[0m     ]\n\u001b[0;32m-> 1471\u001b[0m response, request_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43maugment_client_\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1472\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mchat\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry_policy\u001b[49m\n\u001b[1;32m   1473\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1474\u001b[0m logging\u001b[38;5;241m.\u001b[39mdebug(\n\u001b[1;32m   1475\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mChat finished: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1476\u001b[0m     request_id,\n\u001b[1;32m   1477\u001b[0m     response\u001b[38;5;241m.\u001b[39mstatus_code,\n\u001b[1;32m   1478\u001b[0m     response\u001b[38;5;241m.\u001b[39mcontent,\n\u001b[1;32m   1479\u001b[0m )\n\u001b[1;32m   1480\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m response\u001b[38;5;241m.\u001b[39mok:\n", "File \u001b[0;32m~/augment/base/augment_client/client.py:554\u001b[0m, in \u001b[0;36mAugmentClient._post\u001b[0;34m(self, url_suffix, json, retry_policy, headers, timeout)\u001b[0m\n\u001b[1;32m    552\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m headers:\n\u001b[1;32m    553\u001b[0m     request_headers\u001b[38;5;241m.\u001b[39mupdate(headers)\n\u001b[0;32m--> 554\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    555\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    556\u001b[0m \u001b[43m    \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    557\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    558\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    559\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    560\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    561\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mstr\u001b[39m(response\u001b[38;5;241m.\u001b[39mstatus_code)\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m5\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m    562\u001b[0m     time\u001b[38;5;241m.\u001b[39msleep(retry_policy\u001b[38;5;241m.\u001b[39mretry_sleep)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[0;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[1;32m    105\u001b[0m \n\u001b[1;32m    106\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpost\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    664\u001b[0m     timeout \u001b[38;5;241m=\u001b[39m TimeoutSauce(connect\u001b[38;5;241m=\u001b[39mtimeout, read\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    682\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(err, request\u001b[38;5;241m=\u001b[39mrequest)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connectionpool.py:716\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    713\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    715\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 716\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    723\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    724\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    728\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    729\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n\u001b[1;32m    730\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connectionpool.py:468\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    463\u001b[0m             httplib_response \u001b[38;5;241m=\u001b[39m conn\u001b[38;5;241m.\u001b[39mgetresponse()\n\u001b[1;32m    464\u001b[0m         \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    465\u001b[0m             \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    466\u001b[0m             \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    467\u001b[0m             \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[0;32m--> 468\u001b[0m             \u001b[43msix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_from\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    469\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    470\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mread_timeout)\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connectionpool.py:463\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    460\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[1;32m    461\u001b[0m     \u001b[38;5;66;03m# Python 3\u001b[39;00m\n\u001b[1;32m    462\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 463\u001b[0m         httplib_response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    464\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    465\u001b[0m         \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    466\u001b[0m         \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    467\u001b[0m         \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[1;32m    468\u001b[0m         six\u001b[38;5;241m.\u001b[39mraise_from(e, \u001b[38;5;28;01mNone\u001b[39;00m)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:1386\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1384\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1385\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1386\u001b[0m         \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1387\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[1;32m   1388\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:325\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    323\u001b[0m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    324\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 325\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n\u001b[1;32m    327\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:286\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    285\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 286\u001b[0m     line \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfp\u001b[38;5;241m.\u001b[39mreadline(_MAXLINE \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m), \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miso-8859-1\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    287\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) \u001b[38;5;241m>\u001b[39m _MAXLINE:\n\u001b[1;32m    288\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m LineTooLong(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus line\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/socket.py:706\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    704\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    705\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 706\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    707\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[1;32m    708\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/ssl.py:1315\u001b[0m, in \u001b[0;36mSSLSocket.recv_into\u001b[0;34m(self, buffer, nbytes, flags)\u001b[0m\n\u001b[1;32m   1311\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flags \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1312\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1313\u001b[0m           \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv_into() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1314\u001b[0m           \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1315\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnbytes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1316\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mrecv_into(buffer, nbytes, flags)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/ssl.py:1167\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1165\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1166\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m buffer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m-> 1167\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1168\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1169\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# using this to do sanity tests of what we should be expecting from Augment QA\n", "\n", "from datetime import datetime\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "\n", "client = AugmentClient(\n", "    url=api_proxy,\n", "    token=token,\n", ")\n", "\n", "MODEL_CLIENT = client.client_for_model(\"claude-sonnet-3-5-16k-v8-chat\")\n", "\n", "\n", "def post_IDE_chat_request(\n", "    sample: ResearchChatPromptInput,\n", "):\n", "    chat_response = MODEL_CLIENT.chat(\n", "        selected_code=sample.selected_code,\n", "        message=sample.message,\n", "        prefix=sample.prefix,\n", "        suffix=sample.suffix,\n", "        path=sample.path,\n", "        prefix_begin=sample.prefix_begin,\n", "        suffix_end=sample.suffix_end,\n", "        chat_history=sample.chat_history,\n", "        blobs={\n", "            \"checkpoint_id\": AUGMENT_QA_CHECKPOINT_ID,\n", "            \"added_blobs\": [],\n", "            \"deleted_blobs\": [],\n", "        },\n", "        user_guided_blobs=sample.user_guided_blobs,\n", "        context_code_exchange_request_id=sample.context_code_exchange_request_id,\n", "        user_guidelines=sample.user_guidelines,\n", "        workspace_guidelines=sample.workspace_guidelines,\n", "    )\n", "\n", "    if chat_response.checkpoint_not_found:\n", "        raise ValueError(f\"Checkpoint not found: {AUGMENT_QA_CHECKPOINT_ID}\")\n", "    if chat_response.unknown_blob_names:\n", "        print(f\"Unknown blob names: {chat_response.unknown_blob_names}\")\n", "\n", "    return chat_response.text, chat_response.request_id\n", "\n", "\n", "total = 0\n", "\n", "# Create timestamp for file names\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "output_txt = f\"glean_eval_results_{timestamp}.txt\"\n", "output_csv = f\"glean_eval_results_{timestamp}.csv\"\n", "\n", "support_url_base = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "\n", "# Open both files\n", "with open(output_txt, \"w\", encoding=\"utf-8\") as f, open(\n", "    output_csv, \"w\", newline=\"\", encoding=\"utf-8\"\n", ") as csv_file:\n", "    # Setup CSV writer\n", "    writer = csv.DictWriter(\n", "        csv_file, fieldnames=[\"question\", \"model_response\", \"recall\", \"support_link\"]\n", "    )\n", "    writer.writeheader()\n", "\n", "    for sample in task.samples:\n", "        print(sample.message)\n", "\n", "        f.write(\"=\" * 80 + \"\\n\")\n", "        f.write(f\"Question: {sample.message}\\n\\n\")\n", "\n", "        resp, rid = post_IDE_chat_request(sample)\n", "\n", "        f.write(\"Answer:\\n\")\n", "        f.write(f\"{resp}\\n\\n\")\n", "\n", "        request_url = support_url_base + rid\n", "        f.write(f\"{request_url}\\n\\n\")\n", "\n", "        n_keywords = float(len(set(sample.keywords)))\n", "        n_matched_keywords = count_keywords(resp, sample.keywords)\n", "        answer_keyword_recall = n_matched_keywords / n_keywords\n", "        total += answer_keyword_recall\n", "\n", "        print(answer_keyword_recall)\n", "        f.write(f\"Recall: {answer_keyword_recall:.3f}\\n\\n\")\n", "\n", "        # Write to CSV\n", "        writer.writerow(\n", "            {\n", "                \"question\": sample.message,\n", "                \"model_response\": resp,\n", "                \"recall\": answer_keyword_recall,\n", "                \"support_link\": request_url,\n", "            }\n", "        )\n", "\n", "    print(total)\n", "    f.write(f\"Total: {total:.3f}\\n\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from pathlib import Path\n", "from collections import Counter\n", "from google.cloud import bigquery\n", "from markdown2 import markdown as _markdown2\n", "from markdown import markdown as _markdown1\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from copy import deepcopy\n", "import os\n", "import zipfile\n", "import datetime\n", "import re\n", "import html\n", "from matplotlib.dates import DateFormatter\n", "from IPython.display import display, HTML\n", "from scipy import stats\n", "import io\n", "import base64\n", "from collections import defaultdict\n", "from google.cloud.storage import Client, transfer_manager\n", "import numpy as np\n", "import math\n", "import plotly.express as px\n", "from tqdm import tqdm\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.multiclass import OneVsRestClassifier\n", "import io\n", "import base64\n", "from collections import defaultdict\n", "from google.cloud.storage import Client, transfer_manager\n", "import numpy as np\n", "import signal\n", "from contextlib import contextmanager\n", "import cProfile\n", "import pstats\n", "import io\n", "from line_profiler import LineProfiler\n", "import time \n", "\n", "pd.options.display.float_format = '{:.2f}'.format\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "\n", "MERCOR_TENANT_NAME = \"aitutor-mercor\"\n", "\n", "TURING_BUCKET_NAME = \"augment_ai\"\n", "TURING_TENANT_NAME = \"aitutor-turing\"\n", "TURING_PREFIX = \"to_augment_ai\"\n", "START_DAY = \"2024-12-05\"  # Must be in date format ex: YYYY-MM-DD\n", "HISTORY_LIMIT = 3\n", "NUM_SAMPLES_TO_SHOW = 5\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def markdown2(text):\n", "    return _markdown2(\n", "        text, extras=[\"fenced-code-blocks\", \"code-friendly\"], safe_mode=\"escape\"\n", "    )\n", "\n", "\n", "def markdown1(text):\n", "    return _markdown1(text, extensions=[\"fenced_code\", \"codehilite\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def get_preferences_samples(tenant_name):\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.preference_sample\n", "WHERE tenant=\"{tenant_name}\" \n", "AND day >= \"{START_DAY}\"\n", "ORDER BY time DESC\n", "LIMIT 1000000\n", "\"\"\"\n", "    print(query)\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    clean_rows = []\n", "    for row in rows:\n", "        clean_rows.append(\n", "            {\"preference_request_id\": row.request_id, **dict(row)[\"raw_json\"]}\n", "        )\n", "\n", "    return clean_rows\n", "\n", "\n", "def get_chat_sample(request_id, chat_dict):\n", "    if request_id in chat_dict:\n", "        return chat_dict[request_id]\n", "    else:\n", "        return None\n", "\n", "\n", "def get_chat_samples(request_ids, tenant_name):\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_metadata AS metadata\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\tAND metadata.day >= \"{START_DAY}\"\n", "\tAND request.day >= \"{START_DAY}\"\n", "\tAND response.day >= \"{START_DAY}\"\n", "    AND metadata.tenant=\"{tenant_name}\"\n", "\tAND request.tenant=\"{tenant_name}\"\n", "\tAND response.tenant=\"{tenant_name}\"\n", "\"\"\"\n", "    print(query)\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        if row.request_id in chat_rows_dic:\n", "            print(f\"Duplicate request_id: {row.request_id}\")\n", "            continue\n", "        # assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time,\n", "            \"request_token_ids\": row.request[\"tokenization\"][\"token_ids\"],\n", "        }\n", "\n", "    return chat_rows_dic"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def rating_to_letter(rating):\n", "    if rating == -100:\n", "        return \"Empty\"\n", "\n", "    if rating == 0:\n", "        return \"=\"\n", "    elif rating < 0:\n", "        return f\"A{abs(rating)}\"\n", "    else:\n", "        return f\"B{rating}\"\n", "\n", "\n", "def render_sample(sample):\n", "    if 'option_a' not in sample or 'option_b' not in sample or 'response' not in sample['option_a'] or 'response' not in sample['option_b'] or 'text' not in sample['option_a']['response'] or 'text' not in sample['option_b']['response']:\n", "        return \"\"\n", "    assert (\n", "        sample[\"option_a\"][\"request\"][\"message\"]\n", "        == sample[\"option_b\"][\"request\"][\"message\"]\n", "    )\n", "\n", "    message = markdown2(sample[\"option_a\"][\"request\"][\"message\"])\n", "    answers = [\n", "        markdown2(sample[\"option_a\"][\"response\"][\"text\"]),\n", "        markdown2(sample[\"option_b\"][\"response\"][\"text\"]),\n", "    ]\n", "    feedback = {\n", "        \"formattingRating\": rating_to_letter(sample[\"scores\"][\"formattingRating\"]),\n", "        \"hallucinationRating\": rating_to_letter(\n", "            sample[\"scores\"][\"hallucinationRating\"]\n", "        ),\n", "        \"instructionFollowingRating\": rating_to_letter(\n", "            sample[\"scores\"][\"instructionFollowingRating\"]\n", "        ),\n", "        \"overallRating\": rating_to_letter(sample[\"scores\"][\"overallRating\"]),\n", "        \"isHighQuality\": \"Yes\" if sample[\"scores\"][\"isHighQuality\"] else \"No\",\n", "        \"feedback\": markdown1(sample.get(\"feedback\", \"\").replace(\"\\n\", \"<br>\")),\n", "    }\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row feedback\">\n", "    <ul>\n", "\"\"\"\n", "\n", "    for key, value in feedback.items():\n", "        html_content += f\"<li><strong>{key}:</strong> {value}</li>\"\n", "\n", "    html_content += \"\"\"\n", "            </ul>\n", "        </div>\n", "        <div class=\"row\">\n", "    \"\"\"\n", "\n", "    for answer in answers:\n", "        html_content += f'<div class=\"answer\" style=\"flex: 1;\">{answer}</div>'\n", "\n", "    html_content += \"</div>\"\n", "\n", "    return html_content\n", "\n", "\n", "def render_simple_message(message, response):\n", "    message = markdown2(message)\n", "    response = markdown2(response)\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{response}</div>\n", "</div>\n", "\"\"\"\n", "\n", "    return html_content\n", "\n", "\n", "def render_sample_with_history(sample):\n", "    cur_index = 0\n", "    html_content = \"\"\n", "\n", "    if len(sample[\"option_a\"][\"request\"].get(\"chat_history\", \"\")) > 0:\n", "        for exchange in sample[\"option_a\"][\"request\"][\"chat_history\"][-HISTORY_LIMIT:]:\n", "            if \"response_text\" not in exchange:\n", "                print(\"Missing response_text in exchange:\", exchange)\n", "                continue\n", "            html_content += f\"<h3>Message {cur_index}</h3>\"\n", "            cur_index += 1\n", "            html_content += render_simple_message(\n", "                exchange[\"request_message\"], exchange[\"response_text\"]\n", "            )\n", "            html_content += \"<hr>\"\n", "\n", "        html_content = f\"\"\"\n", "        <details>\n", "            <summary>Chat history</summary>\n", "            {html_content}\n", "        </details>\n", "        \"\"\"\n", "    else:\n", "        html_content += \"\"\"\n", "        <div>\n", "            <p>Chat history: N/A</p>\n", "        </div>\n", "\"\"\"\n", "\n", "    if len(sample[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0:\n", "        cur_selected_code = f\"\"\"```\n", "{sample[\"option_a\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "        html_content += f\"\"\"\n", "        <details>\n", "            <summary>Selected code</summary>\n", "            <div class=\"row\">\n", "                <div class=\"answer\" style=\"flex: 1;\">{markdown2(cur_selected_code)}</div>\n", "            </div>\n", "        </details>\n", "        \"\"\"\n", "    else:\n", "        html_content += \"\"\"\n", "        <div>\n", "            <p>Selected code: N/A</p>\n", "        </div>\n", "\"\"\"\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "\n", "    html_content += render_sample(sample)\n", "\n", "    return html_content\n", "\n", "\n", "def wrap_html(html_content):\n", "    start = \"\"\"<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <title>Question and Answers</title>\n", "    <style>\n", "        .row {\n", "            display: flex;\n", "            justify-content: center;\n", "            align-items: center;\n", "            margin-bottom: 20px;\n", "        }\n", "        .feedback, .answer {\n", "            margin: 10px;\n", "            padding: 10px;\n", "            border: 1px solid #ddd;\n", "            border-radius: 5px;\n", "            background-color: #f9f9f9;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "    end = \"\"\"\n", "    </body>\n", "</html>\"\"\"\n", "\n", "    return \"\\n\".join([start, html_content, end])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def get_data_df(samples):\n", "    all_data = []\n", "    for sample in samples:\n", "        cur_scores = deepcopy(sample[\"scores\"])\n", "\n", "        cur_scores[\"user_id\"] = sample[\"option_a\"][\"user_id\"]\n", "\n", "        all_data.append(cur_scores)\n", "    df = pd.DataFrame(all_data)\n", "    return df\n", "\n", "\n", "def request_id_to_link(request_id, tenant_name):\n", "    return f\"https://support.{tenant_name}.t.us-central1.prod.augmentcode.com/t/{tenant_name}/request/{request_id}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# turing_samples = [{**sample, 'tenant_name': TURING_TENANT_NAME}  for sample in get_turing_samples_local(TURING_TENANT_NAME)]\n", "turing_samples = [{**sample, 'tenant_name': TURING_TENANT_NAME}  for sample in get_preferences_samples(TURING_TENANT_NAME)]\n", "\n", "mercor_samples = [{**sample, 'tenant_name': MERCOR_TENANT_NAME}  for sample in get_preferences_samples(MERCOR_TENANT_NAME)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# Only if using BQ version\n", "def enrich_samples_with_chats(samples, tenant_name):\n", "    chat_dict = get_chat_samples(\n", "        list(\n", "            set(\n", "                list(map(lambda x: x[\"request_ids\"][0], samples))\n", "                + list(map(lambda x: x[\"request_ids\"][1], samples))\n", "            )\n", "        ), tenant_name\n", "    )\n", "\n", "    samples_with_chats = []\n", "    for sample in tqdm(samples):\n", "        option_a = get_chat_sample(sample[\"request_ids\"][0], chat_dict)\n", "        option_b = get_chat_sample(sample[\"request_ids\"][1], chat_dict)\n", "        if option_a is None:\n", "            print(\n", "                f\"Failed to find chat sample for option_a: {sample['request_ids'][0]}\"\n", "            )\n", "            continue\n", "        if option_b is None:\n", "            print(\n", "                f\"Failed to find chat sample for option_b: {sample['request_ids'][1]}\"\n", "            )\n", "            continue\n", "\n", "        sample[\"option_a\"] = option_a\n", "        sample[\"option_b\"] = option_b\n", "        samples_with_chats.append(sample)\n", "\n", "    for sample in samples_with_chats:\n", "        # To make local and BQ version the same\n", "        sample[\"option_a\"][\"user_id\"] = sample[\"option_a\"][\"metadata\"][\"user_id\"]\n", "        sample[\"option_b\"][\"user_id\"] = sample[\"option_b\"][\"metadata\"][\"user_id\"]\n", "\n", "        sample[\"user_agent\"] = sample[\"option_a\"][\"metadata\"][\"user_agent\"]\n", "\n", "        sample[\"option_a\"][\"request_id\"] = sample[\"request_ids\"][0]\n", "        sample[\"option_b\"][\"request_id\"] = sample[\"request_ids\"][1]\n", "\n", "    return samples_with_chats"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# mercor_samples = enrich_samples_with_chats(mercor_samples, MERCOR_TENANT_NAME)\n", "turing_samples = enrich_samples_with_chats(turing_samples, TURING_TENANT_NAME)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mercor_samples = enrich_samples_with_chats(mercor_samples, MERCOR_TENANT_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = [sample for sample in mercor_samples + turing_samples if sample['option_a']['user_id'] != \"<EMAIL>\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["analysis_df = get_data_df(samples)\n", "samples_per_user = Counter(analysis_df.user_id)\n", "\n", "for name, num in samples_per_user.most_common(100):\n", "    print(f\"{name}: {num}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# label_names = sorted(samples[0][\"scores\"].keys())\n", "label_names = list(\n", "    [\"formattingRating\", \"instructionFollowingRating\", \"isHighQuality\", \"overallRating\"]\n", ")\n", "user_names = list(map(lambda x: x[0], samples_per_user.most_common(100)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def clean_text(text):\n", "    # Remove any leading/trailing whitespace, newlines, and colons\n", "    text = text.strip().strip(\":\").strip()\n", "    # Replace multiple spaces with a single space\n", "    text = re.sub(r\"\\s+\", \" \", text)\n", "    return text\n", "\n", "def extract_model_name(sample):\n", "    return sample['option_a']['request']['model_name'], sample['option_b']['request']['model_name']\n", "\n", "def extract_categories(text):\n", "    category_pattern = r\"\\[Category\\]\\s*(.+?)(?=\\[|$)\"\n", "    subcategory_pattern = r\"\\[Sub Category\\]\\s*(.+?)(?=\\[|$)\"\n", "    type_pattern = r\"\\[Type\\]\\s*(.+?)(?=\\[|$)\"\n", "\n", "    category_match = re.search(category_pattern, text, re.DOTALL)\n", "    subcategory_match = re.search(subcategory_pattern, text, re.DOTALL)\n", "    type_match = re.search(type_pattern, text, re.DOTALL)\n", "\n", "    category = clean_text(category_match.group(1)) if category_match else \"Unknown\"\n", "    subcategory = (\n", "        clean_text(subcategory_match.group(1))\n", "        if subcategory_match\n", "        else \"No Subcategory\"\n", "    )\n", "    type_ = clean_text(type_match.group(1)) if type_match else \"No Type\"\n", "\n", "    return category, subcategory, type_\n", "\n", "def extract_implicit_external_sources(text):\n", "    start_labels = [\"IMPLICIT_EXTERNAL_SOURCES_START_LABEL\"]\n", "    end_labels = [\"IMPLICIT_EXTERNAL_SOURCES_END_LABEL\"]\n", "    \n", "    for start_label in start_labels:\n", "        for end_label in end_labels:\n", "            pattern = f\"{re.escape(start_label)}(.*?){re.escape(end_label)}\"\n", "            match = re.search(pattern, text, re.DOTALL)\n", "            if match:\n", "                return clean_text(match.group(1))\n", "    \n", "    return \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["from collections import defaultdict, Counter\n", "\n", "\n", "custom_metrics = defaultdict(dict)\n", "\n", "for user_name in user_names:\n", "    print(f\"{user_name}\")\n", "    cur_samples = [\n", "        sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_name\n", "    ]\n", "    print(\n", "        \"\\n\".join(\n", "            map(\n", "                str,\n", "                Counter(list(map(lambda x: x[\"user_agent\"], cur_samples))).most_common(\n", "                    100\n", "                ),\n", "            )\n", "        )\n", "    )\n", "    print(\"#\" * 20)\n", "\n", "    num_w_selected_code = len(\n", "        list(\n", "            filter(\n", "                lambda x: len(x[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0,\n", "                cur_samples,\n", "            )\n", "        )\n", "    )\n", "    num_wo_selected_code = len(cur_samples) - num_w_selected_code\n", "\n", "    custom_metrics[user_name][\"num_w_selected_code\"] = num_w_selected_code\n", "    custom_metrics[user_name][\"num_wo_selected_code\"] = num_wo_selected_code\n", "\n", "    for sample in cur_samples:\n", "        category, sub_category, type_ = extract_categories(sample.get(\"feedback\", \"\"))\n", "        implicit_external_sources = extract_implicit_external_sources(sample.get(\"feedback\", \"\"))\n", "        model_a, model_b = extract_model_name(sample)\n", "        sub_category = f\"{category}_{sub_category}\"\n", "        type_ = f\"{sub_category}_{type_}\"\n", "        sample['option_a']['model_name'] = model_a\n", "        sample['option_b']['model_name'] = model_b\n", "        sample['implicit_external_sources'] = implicit_external_sources\n", "        sample['winning_model_name'] = model_a if sample['scores']['overallRating'] < 0 else sample['option_b']['model_name'] if sample['scores']['overallRating'] > 0 else \"Tie\"\n", "        \n", "        custom_metrics[user_name].setdefault(\"categories\", defaultdict(int))[\n", "            category\n", "        ] += 1\n", "        custom_metrics[user_name].setdefault(\"subcategories\", defaultdict(int))[\n", "            sub_category\n", "        ] += 1\n", "        custom_metrics[user_name].setdefault(\"types\", defaultdict(int))[type_] += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["for user_name in user_names:\n", "    print(\n", "        f\"{user_name}: {custom_metrics[user_name]['num_w_selected_code'] / (custom_metrics[user_name]['num_w_selected_code'] + custom_metrics[user_name]['num_wo_selected_code']):.2f}   {custom_metrics[user_name]['categories']}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if EXTERNAL_SOURCES_FILTER:\n", "    samples = [sample for sample in samples if sample[\"option_a\"][\"model_name\"] != sample[\"option_b\"][\"model_name\"] and sample[\"option_a\"][\"model_name\"] in [\"claude-sonnet-3-5-16k-v3-oe-chat\"] and sample[\"option_b\"][\"model_name\"] in [\"claude-sonnet-3-5-16k-v3-oe-chat\"]]\n", "else:\n", "    samples = [sample for sample in samples if sample[\"option_a\"][\"model_name\"] != sample[\"option_b\"][\"model_name\"] and sample[\"option_a\"][\"model_name\"] not in  [\"claude-sonnet-3-5-16k-v3-oe-chat\"] and sample[\"option_b\"][\"model_name\"] not in [\"claude-sonnet-3-5-16k-v3-oe-chat\"]]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# samples = [sample for sample in samples \n", "#            if \"camel\" not in sample['option_a']['model_name'] \n", "#            and \"camel\" not in sample['option_b']['model_name'] \n", "#            and \"l3-\" not in sample['option_a']['model_name'] \n", "#            and \"l3-\" not in sample['option_b']['model_name']\n", "#            and ('claude-sonnet-3-5-16k-v2-chat' in [sample['option_a']['model_name'], sample['option_b']['model_name']])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["\n", "def calculate_separate_stats(df):\n", "    grouped = df.groupby(['model_a', 'model_b'])\n", "    separate_stats = grouped.agg({\n", "        'overallRating': ['mean', lambda x: (x == 0).sum(), 'count']\n", "    }).reset_index()\n", "    \n", "    separate_stats.columns = ['model_a', 'model_b', 'mean_rating', 'ties', 'total_comparisons']\n", "    \n", "    separate_stats['model_a_wins'] = grouped['overallRating'].apply(lambda x: (x < 0).sum()).values\n", "    separate_stats['model_b_wins'] = grouped['overallRating'].apply(lambda x: (x > 0).sum()).values\n", "    \n", "    separate_stats['model_a_win_pct'] = separate_stats['model_a_wins'] / separate_stats['total_comparisons']\n", "    separate_stats['model_b_win_pct'] = separate_stats['model_b_wins'] / separate_stats['total_comparisons']\n", "    separate_stats['tie_pct'] = separate_stats['ties'] / separate_stats['total_comparisons']\n", "    \n", "    separate_stats['overall_winner'] = np.select(\n", "        [separate_stats['model_a_win_pct'] > separate_stats['model_b_win_pct'],\n", "         separate_stats['model_b_win_pct'] > separate_stats['model_a_win_pct']],\n", "        [separate_stats['model_a'], separate_stats['model_b']],\n", "        default='Tie'\n", "    )\n", "    \n", "    return separate_stats.sort_values('total_comparisons', ascending=False)\n", "\n", "def calculate_combined_stats(df):\n", "    df = df.copy()  # Create a copy to avoid SettingWithCopyWarning\n", "    df['model_pair'] = df.apply(lambda row: tuple(sorted([row['model_a'], row['model_b']])), axis=1)\n", "    grouped = df.groupby('model_pair')\n", "    \n", "    combined_stats = grouped.size().reset_index(name='total_comparisons')\n", "    combined_stats['ties'] = grouped['overallRating'].apply(lambda x: (x == 0).sum()).values\n", "    \n", "    combined_stats['winning_model'] = combined_stats.apply(lambda row: \n", "        df[df['model_pair'] == row['model_pair']]['winning_model'].mode().iloc[0], axis=1)\n", "    \n", "    combined_stats['win_rate'] = combined_stats.apply(lambda row: \n", "        (df[df['model_pair'] == row['model_pair']]['winning_model'] == row['winning_model']).sum() / row['total_comparisons'], axis=1)\n", "    combined_stats['tie_rate'] = combined_stats['ties'] / combined_stats['total_comparisons']\n", "    combined_stats['loss_rate'] = 1 - combined_stats['win_rate'] - combined_stats['tie_rate']\n", "\n", "    # Sort by whether the model pair is different, then by total comparisons\n", "    combined_stats['is_different_pair'] = combined_stats['model_pair'].apply(lambda x: x[0] != x[1])\n", "    combined_stats = combined_stats.sort_values(['is_different_pair', 'total_comparisons'], ascending=[False, False])\n", "    combined_stats = combined_stats.drop('is_different_pair', axis=1)  # Remove the helper column\n", "    return combined_stats\n", "\n", "def style_dataframe(df):\n", "    return df.style.set_properties(**{\n", "        'border-collapse': 'collapse',\n", "        'border': '1px solid #ddd',\n", "        'padding': '8px',\n", "        'text-align': 'left'\n", "    }).set_table_styles([{\n", "        'selector': 'th',\n", "        'props': [('background-color', '#f2f2f2'), ('font-weight', 'bold')]\n", "    }]).format(precision=3)\n", "\n", "def generate_model_comparison_tables(df, user_id=None):\n", "    if user_id:\n", "        df = df[df['user_id'] == user_id].copy()  # Create a copy to avoid SettingWithCopyWarning\n", "    \n", "    if df.empty:\n", "        return \"<p>No data available for the selected criteria.</p>\"\n", "\n", "    separate_stats = calculate_separate_stats(df)\n", "    combined_stats = calculate_combined_stats(df)\n", "    crosstab = pd.crosstab(df['model_a'], df['model_b'], values=df['overallRating'], aggfunc='mean')\n", "    # set the main diagonal to nan\n", "    np.fill_diagonal(crosstab.values, np.nan)\n", "\n", "    # Apply styling to DataFrames\n", "    separate_styled = style_dataframe(separate_stats)\n", "    combined_styled = style_dataframe(combined_stats)\n", "    crosstab_styled = style_dataframe(crosstab)\n", "\n", "    # Convert styled DataFrames to HTML tables\n", "    separate_html = separate_styled.to_html(index=False)\n", "    combined_html = combined_styled.to_html(index=False)\n", "    crosstab_html = crosstab_styled.to_html()\n", "\n", "    css = \"\"\"\n", "    <style>\n", "        .comparison-tables {\n", "            font-family: <PERSON><PERSON>, sans-serif;\n", "            max-width: 1200px;\n", "            background-color: #f9f9f9;\n", "            border-radius: 8px;\n", "            box-shadow: 0 0 10px rgba(0,0,0,0.1);\n", "        }\n", "        .comparison-tables h3 {\n", "            color: #333;\n", "            border-bottom: 2px solid #ddd;\n", "            padding-bottom: 10px;\n", "            margin-bottom: 20px;\n", "        }\n", "        .comparison-tables h4 {\n", "            color: #444;\n", "            margin-top: 30px;\n", "            margin-bottom: 10px;\n", "        }\n", "        .comparison-tables table {\n", "            width: 100%;\n", "            margin-bottom: 30px;\n", "        }\n", "        .comparison-tables th, .comparison-tables td {\n", "            padding: 12px;\n", "            border: 1px solid #ddd;\n", "        }\n", "        .comparison-tables th {\n", "            background-color: #f2f2f2;\n", "            font-weight: bold;\n", "            text-align: left;\n", "        }\n", "        .comparison-tables tr:nth-child(even) {\n", "            background-color: #f8f8f8;\n", "        }\n", "        .comparison-tables tr:hover {\n", "            background-color: #e8e8e8;\n", "        }\n", "    </style>\n", "    \"\"\"\n", "\n", "    return f\"\"\"\n", "    {css}\n", "    <div class=\"comparison-tables\">\n", "        <h3>{'User-specific' if user_id else 'Overall'} Model Comparison Tables</h3>\n", "        <h4>Combined Comparisons:</h4>\n", "        {combined_html}\n", "        <h4>Cross tab Comparisons (-1: A, 0: <PERSON><PERSON>, 1: B):</h4>\n", "        {crosstab_html}\n", "        <h4>Separate A/B Comparisons: (Only usfell for A/B bias testing) </h4>\n", "        {separate_html}\n", "    </div>\n", "    \"\"\"\n", "\n", "\n", "def expected_score(rating_a, rating_b):\n", "    return 1 / (1 + math.pow(10, (rating_b - rating_a) / 400))\n", "\n", "def update_elo(rating_a, rating_b, score_a, k_factor=32):  # Increased k-factor\n", "    expected_a = expected_score(rating_a, rating_b)\n", "    new_rating_a = rating_a + k_factor * (score_a - expected_a)\n", "    return new_rating_a\n", "\n", "def calculate_elo_ratings(df, k_factor=32, num_iterations=100):\n", "    all_models = set(df['model_a'].unique()) | set(df['model_b'].unique())\n", "    avg_ratings = {model: 0 for model in all_models}\n", "    avg_comparison_counts = {model: 0 for model in all_models}\n", "    \n", "    for _ in range(num_iterations):\n", "        ratings = {model: 1500 for model in all_models}\n", "        comparison_counts = {model: 0 for model in all_models}\n", "        \n", "        # Shuffle the DataFrame\n", "        df_shuffled = df.sample(frac=1).reset_index(drop=True)\n", "        \n", "        for _, row in df_shuffled.iterrows():\n", "            model_a, model_b = row['model_a'], row['model_b']\n", "            if model_a == model_b:\n", "                continue  # Skip self-comparisons\n", "            \n", "            comparison_counts[model_a] += 1\n", "            comparison_counts[model_b] += 1\n", "            \n", "            if row['winning_model'] == model_a:\n", "                score_a, score_b = 1, 0\n", "            elif row['winning_model'] == model_b:\n", "                score_a, score_b = 0, 1\n", "            else:  # Tie\n", "                score_a, score_b = 0.5, 0.5\n", "            \n", "            new_rating_a = update_elo(ratings[model_a], ratings[model_b], score_a, k_factor)\n", "            new_rating_b = update_elo(ratings[model_b], ratings[model_a], score_b, k_factor)\n", "            \n", "            ratings[model_a] = new_rating_a\n", "            ratings[model_b] = new_rating_b\n", "        \n", "        # Accumulate ratings and comparison counts\n", "        for model in all_models:\n", "            avg_ratings[model] += ratings[model]\n", "            avg_comparison_counts[model] += comparison_counts[model]\n", "    \n", "    # Calculate average ratings and comparison counts\n", "    for model in all_models:\n", "        avg_ratings[model] /= num_iterations\n", "        avg_comparison_counts[model] /= num_iterations\n", "    \n", "    return avg_ratings, avg_comparison_counts\n", "\n", "\n", "def generate_elo_table(df):\n", "    elo_ratings, comparison_counts = calculate_elo_ratings(df)\n", "    elo_table = pd.DataFrame({\n", "        'Model': list(elo_ratings.keys()),\n", "        'Elo Rating': list(elo_ratings.values()),\n", "        'Comparisons': [comparison_counts[model] for model in elo_ratings.keys()]\n", "    })\n", "    elo_table = elo_table.sort_values('Elo Rating', ascending=False).reset_index(drop=True)\n", "    \n", "    # Round Elo Rating to 2 decimal places\n", "    elo_table['El<PERSON> Rating'] = elo_table['Elo Rating'].round(2)\n", "    \n", "    return elo_table\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def generate_category_tables(categories, subcategories):\n", "    def create_table(data, title):\n", "        sorted_data = sorted(data.items(), key=lambda x: x[1], reverse=True)\n", "        table_html = f\"<h3>{title}</h3>\"\n", "        table_html += (\n", "            \"<table border='1' style='border-collapse: collapse; width: 100%;'>\"\n", "        )\n", "        table_html += \"<tr><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>{}</th><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>Count</th></tr>\".format(\n", "            title\n", "        )\n", "        for category, count in sorted_data:\n", "            table_html += f\"<tr><td style='padding: 8px;'>{category}</td><td style='padding: 8px;'>{count}</td></tr>\"\n", "        table_html += \"</table>\"\n", "        return table_html\n", "\n", "    categories_table = create_table(categories, \"Categories\")\n", "    subcategories_table = create_table(subcategories, \"Sub Categories\")\n", "\n", "    return f\"<div style='display: flex; justify-content: space-between;'><div style='width: 48%;'>{categories_table}</div><div style='width: 48%;'>{subcategories_table}</div></div>\"\n", "\n", "slow_samples = []\n", "\n", "# Add timeout context manager at the top\n", "@contextmanager\n", "def timeout(seconds):\n", "    def signal_handler(signum, frame):\n", "        raise TimeoutError(f\"Timed out after {seconds} seconds\")\n", "    \n", "    signal.signal(signal.SIGALRM, signal_handler)\n", "    signal.alarm(seconds)\n", "    try:\n", "        yield\n", "    finally:\n", "        signal.alarm(0)\n", "\n", "\n", "def plot_to_html(plt):\n", "    img = io.BytesIO()\n", "    plt.savefig(img, format='png', bbox_inches='tight')\n", "    img.seek(0)\n", "    plt.close()  # Close the plot to free up memory\n", "    return base64.b64encode(img.getvalue()).decode()\n", "\n", "def create_score_distribution_plot(data, title):\n", "    plt.figure(figsize=(10, 6))\n", "    for model, scores in data.items():\n", "        plt.hist(scores, bins=7, range=(0, 3), alpha=0.5, label=model)\n", "    plt.title(title)\n", "    plt.xlabel('Score')\n", "    plt.ylabel('Frequency')\n", "    plt.legend()\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    return plot_to_html(plt)\n", "\n", "def calculate_elo_over_time(df, num_iterations=10, k_factor=64):\n", "    df = df.sort_values('datetime')  # Ensure the dataframe is sorted by time\n", "    rating_history = defaultdict(list)\n", "    \n", "    step = max(1, len(df) // 10)  # Ensure step is at least 1\n", "    for i in range(0, len(df), step):\n", "        current_df = df.iloc[:i+1]\n", "        current_ratings, _ = calculate_elo_ratings(current_df, num_iterations, k_factor)\n", "        for model, rating in current_ratings.items():\n", "            rating_history[model].append({'rating': rating, 'time': df.iloc[i]['datetime']})\n", "        \n", "    # Ensure the last point is included if it wasn't already\n", "    if len(df) % step != 0:\n", "        current_ratings, _ = calculate_elo_ratings(df, num_iterations, k_factor)\n", "        for model, rating in current_ratings.items():\n", "            rating_history[model].append({'rating': rating, 'time': df.iloc[-1]['datetime']})\n", "\n", "    return rating_history\n", "\n", "def generate_elo_plot(df, user_id=None):\n", "    if user_id:\n", "        df = df[df['user_id'] == user_id]\n", "\n", "    rating_history = calculate_elo_over_time(df)\n", "\n", "    plt.figure(figsize=(12, 6))\n", "    for model, ratings in rating_history.items():\n", "        times = [r['time'] for r in ratings]\n", "        ratings_values = [r['rating'] for r in ratings]\n", "        plt.plot(times, ratings_values, label=model, marker='o')\n", "\n", "    plt.title(f\"Elo Ratings Over Time{' for ' + user_id if user_id else ''}\")\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Elo Rating')\n", "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    plt.tight_layout()\n", "    \n", "    return plot_to_html(plt)\n", "\n", "\n", "def remove_model_names(feedback):\n", "    pattern = r\"MODEL_IDS_START_LABEL\\s*(.+?)\\s*MODEL_IDS_END_LABEL\"\n", "    return re.sub(pattern, \"\", feedback, flags=re.DOTALL).strip()\n", "\n", "def generate_report(internal : bool):\n", "        multisample_html = \"\"\n", "        if internal:\n", "            # Create DataFrame\n", "            df = pd.DataFrame(\n", "                [\n", "                    {'overallRating': sample[\"scores\"][\"overallRating\"], \n", "                    \"user_id\": sample[\"option_a\"][\"user_id\"],\n", "                    \"model_a\": sample[\"option_a\"][\"model_name\"],\n", "                    \"model_b\": sample[\"option_b\"][\"model_name\"],\n", "                    \"winning_model\": sample[\"winning_model_name\"],\n", "                    \"datetime\": sample[\"option_a\"][\"datetime\"]}\n", "                    for sample in samples\n", "                    if sample[\"option_a\"][\"model_name\"] not in [\"undefined\", \"Undefined\", \"Unknown\"]\n", "                ]\n", "            )\n", "            model_name_mapping = {\n", "                'binks-l3-70B-FP8-ug-chatanol1-16-3-chat': 'Chatanol-16-3',\n", "                'binks-l3-w-refinement-v1': 'Refinement-v1',\n", "            }\n", "            # Apply the mapping to the relevant columns\n", "            columns_to_shorten = ['model_a', 'model_b', 'winning_model']\n", "            for col in columns_to_shorten:\n", "                df[col] = df[col].replace(model_name_mapping)\n", "            elo_table = generate_elo_table(df)\n", "            elo_table_html = style_dataframe(elo_table).to_html(index=False)\n", "\n", "\n", "\n", "        for user_id in user_names:\n", "            cur_samples = [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_id]\n", "            cur_samples = list(reversed(sorted(cur_samples, key=lambda x: x[\"option_a\"][\"datetime\"])))\n", "            \n", "            cur_user_html = \"\"\n", "\n", "            for i, sample in tqdm(enumerate(cur_samples[0:min(NUM_SAMPLES_TO_SHOW, len(cur_samples))])):\n", "                start_time = time.time()\n", "                try:\n", "                    # Handles previous samples that don't have ratings\n", "                    if \"formattingRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"formattingRating\"] = -100\n", "\n", "                    if \"instructionFollowingRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"instructionFollowingRating\"] = -100\n", "\n", "                    if \"hallucinationRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"hallucinationRating\"] = -100\n", "                    sample[\"feedback\"] = remove_model_names(sample[\"feedback\"])\n", "                        \n", "\n", "                    cur_user_html += f\"<h1>Chain {i}</h1>\"\n", "                    cur_user_html += f\"<div>User ID: {user_id}</div>\"\n", "                    cur_user_html += f\"<div>Time: {sample['option_a']['datetime'].strftime('%m-%d-%Y %H:%M:%S')}</div>\"\n", "                    cur_user_html += (\n", "                        f'<div>Request ID (Preference): {sample[\"preference_request_id\"]}</div>'\n", "                    )\n", "                    cur_user_html += (\n", "                        f\"<div>____________________________________________________</div>\"\n", "                    )\n", "                    cur_user_html += f'<div>Model A: {sample[\"option_a\"][\"model_name\"]}</div>'\n", "                    cur_user_html += f'<div>Request ID (A): <a href=\"{request_id_to_link(sample[\"option_a\"][\"request_id\"], sample[\"tenant_name\"])}\">{sample[\"option_a\"][\"request_id\"]}</a></div>'\n", "                    cur_user_html += f'<div>Model B: {sample[\"option_b\"][\"model_name\"]}</div>'\n", "                    cur_user_html += f'<div>Request ID (B): <a href=\"{request_id_to_link(sample[\"option_b\"][\"request_id\"],  sample[\"tenant_name\"])}\">{sample[\"option_b\"][\"request_id\"]}</a></div>'\n", "                    with timeout(1):\n", "                        history_html = render_sample_with_history(sample)\n", "                \n", "                    cur_user_html += history_html\n", "                    cur_user_html += \"<hr>\"\n", "                    \n", "                    \n", "                except TimeoutError:\n", "                    print(f\"TIMEOUT: Sample {sample['preference_request_id']} took too long to render\")\n", "                    history_html = \"<div class='error'>Rendering timed out after 5 seconds</div>\"\n", "            multisample_html += f\"<details><summary>User ID: {user_id} -- {len(cur_samples)} samples</summary>{cur_user_html}</details>\"\n", "        total_categories = Counter()\n", "        total_subcategories = Counter()\n", "        total_samples = 0\n", "\n", "        for user_id in user_names:\n", "            total_categories.update(custom_metrics[user_id][\"categories\"])\n", "            total_subcategories.update(custom_metrics[user_id][\"subcategories\"])\n", "            total_samples += len(\n", "                [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_id]\n", "            )\n", "\n", "        # Create summary HTML\n", "        summary_html = \"<h2>Overall Summary</h2>\"\n", "        summary_html += f\"<div>Total Users: {len(user_names)}</div>\"\n", "        summary_html += f\"<div>Total Samples: {total_samples}</div>\"\n", "        summary_html += generate_category_tables(total_categories, total_subcategories)\n", "        if internal:\n", "            # Generate overall plot\n", "            overall_plot_html = generate_elo_plot(df)\n", "            summary_html += f\"<h2>Overall Score Distribution</h2><img src='data:image/png;base64,{overall_plot_html}'>\"\n", "\n", "            summary_html += \"<h2>Elo Ratings</h2>\"\n", "            summary_html += elo_table_html\n", "            # Add overall model comparison tables\n", "            summary_html += generate_model_comparison_tables(df)\n", "\n", "\n", "\n", "        # Add the summary to the main HTML\n", "        multisample_html += summary_html\n", "        whole_html = wrap_html(multisample_html)\n", "\n", "        with open(\n", "            f\"/mnt/efs/augment/user/tamuz/annotations/model_elo/{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html\",\n", "            \"w\",\n", "        ) as f:\n", "            f.write(whole_html)\n", "        return f\"/mnt/efs/augment/user/tamuz/annotations/model_elo/{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html\"\n", "# samples = [sample for sample in samples if sample[\"option_a\"][\"model_name\"] != sample[\"option_b\"][\"model_name\"] and sample[\"option_a\"][\"model_name\"] not in [\"undefined\", \"Undefined\", \"Unknown\"] and abs(sample['scores']['overallRating'] > 1)]\n", "\n", "# generate_report(internal=True)\n", "\n", "generate_report(internal=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def get_turn_num(request):\n", "    if len(request.get(\"chat_history\", \"\")) > 0:\n", "        return len(request[\"chat_history\"])\n", "    return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["battles = pd.DataFrame(\n", "    [\n", "        {'overallRating': sample[\"scores\"][\"overallRating\"],\n", "        'formattingRating':  sample[\"scores\"][\"formattingRating\"],\n", "        'instructionFollowingRating': sample['scores']['instructionFollowingRating'],\n", "        'isHighQuality': sample['scores']['isHighQuality'],\n", "        \"user_id\": sample[\"option_a\"][\"user_id\"],\n", "        \"model_a\": sample[\"option_a\"][\"model_name\"],\n", "        \"model_b\": sample[\"option_b\"][\"model_name\"],\n", "        \"winner\": \"model_a\" if sample[\"winning_model_name\"] == sample[\"option_a\"][\"model_name\"] else \"model_b\" if sample[\"winning_model_name\"] == sample[\"option_b\"][\"model_name\"] else \"Tie\",\n", "        'turn': get_turn_num(sample['option_a']['request']),\n", "        \"datetime\": sample[\"option_a\"][\"datetime\"]}\n", "        for sample in samples if sample[\"option_a\"][\"model_name\"] not in [\"undefined\", \"Undefined\", \"Unknown\"]\n", "    ]\n", ")\n", "\n", "model_name_mapping = {\n", "    'binks-l3-70B-FP8-ug-chatanol1-16-3-chat': 'Chatanol-16-3',\n", "    'binks-l3-w-refinement-v1': 'Refinement-v1',\n", "}\n", "# Apply the mapping to the relevant columns\n", "columns_to_shorten = ['model_a', 'model_b', 'winner']\n", "for col in columns_to_shorten:\n", "    battles[col] = battles[col].replace(model_name_mapping)\n", "battles.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["print(f\"Total samples: {len(battles)}\")\n", "battles = battles[battles.model_a != battles.model_b]\n", "print(f\"Total head to head battles: {len(battles)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["fig = px.bar(battles[\"winner\"].value_counts(),\n", "             title=\"Counts of Battle Outcomes\", text_auto=True, height=400)\n", "fig.update_layout(xaxis_title=\"Battle Outcome\", yaxis_title=\"Count\",\n", "                  showlegend=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter out models with less then 50 battles\n", "model_counts = pd.concat([battles[\"model_a\"], battles[\"model_b\"]]).value_counts()\n", "model_counts = model_counts[model_counts >= 50]\n", "model_counts.sort_values(ascending=False)\n", "battles = battles[battles[\"model_a\"].isin(model_counts.index) & battles[\"model_b\"].isin(model_counts.index)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["battles_no_ties = battles[~battles[\"winner\"].str.contains(\"Tie\")]\n", "fig = px.bar(pd.concat([battles[\"model_a\"], battles[\"model_b\"]]).value_counts(),\n", "             title=\"Battle Count for Each Model\", text_auto=True)\n", "fig.update_layout(xaxis_title=\"model\", yaxis_title=\"Battle Count\", height=400,\n", "                  showlegend=False)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["def visualize_battle_count(battles, title, show_num_models=30):\n", "    ptbl = pd.pivot_table(battles, index=\"model_a\", columns=\"model_b\", aggfunc=\"size\",\n", "                          fill_value=0)\n", "    battle_counts = ptbl + ptbl.T\n", "    ordering = battle_counts.sum().sort_values(ascending=False).index\n", "    ordering = ordering[:show_num_models]\n", "    fig = px.imshow(battle_counts.loc[ordering, ordering],\n", "                    title=title, text_auto=True)\n", "    fig.update_layout(xaxis_title=\"Model B\",\n", "                      yaxis_title=\"Model A\",\n", "                      xaxis_side=\"top\", height=800, width=800,\n", "                      title_y=0.07, title_x=0.5,\n", "                      font=dict(size=10))\n", "    fig.update_traces(hovertemplate=\n", "                      \"Model A: %{y}<br>Model B: %{x}<br>Count: %{z}<extra></extra>\")\n", "    return fig\n", "\n", "fig = visualize_battle_count(battles, title=\"Battle Count of Each Combination of Models\", show_num_models=30)\n", "fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize_battle_count(battles_no_ties, \"Battle Count for Each Combination of Models (without Ties)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["visualize_battle_count(battles[battles['winner'].str.contains(\"Tie\")], \"Tie Count for Each Combination of Models\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["fig = px.histogram(battles[\"turn\"],\n", "             title=f\"Number of Conversation Turns\",\n", "             text_auto=True, height=400, log_y=True)\n", "fig.update_layout(xaxis_title=\"Turns\", yaxis_title=\"Count\", showlegend=False)\n", "fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def compute_pairwise_win_fraction(battles, max_num_models=30):\n", "    # Times each model wins as Model A\n", "    a_win_ptbl = pd.pivot_table(\n", "        battles[battles['winner'] == \"model_a\"],\n", "        index=\"model_a\", columns=\"model_b\", aggfunc=\"size\", fill_value=0) # type: ignore\n", "\n", "    # Table counting times each model wins as Model B\n", "    b_win_ptbl = pd.pivot_table(\n", "        battles[battles['winner'] == \"model_b\"],\n", "        index=\"model_a\", columns=\"model_b\", aggfunc=\"size\", fill_value=0) # type: ignore\n", "\n", "    # Table counting number of A-B pairs\n", "    num_battles_ptbl = pd.pivot_table(battles,\n", "        index=\"model_a\", columns=\"model_b\", aggfunc=\"size\", fill_value=0) # type: ignore\n", "\n", "    # Computing the proportion of wins for each model as A and as B\n", "    # against all other models\n", "    row_beats_col_freq = (\n", "        (a_win_ptbl + b_win_ptbl.T) /\n", "        (num_battles_ptbl + num_battles_ptbl.T)\n", "    )\n", "\n", "    # Arrange ordering according to proprition of wins\n", "    prop_wins = row_beats_col_freq.mean(axis=1).sort_values(ascending=False)\n", "    prop_wins = prop_wins[:max_num_models]\n", "    model_names = list(prop_wins.keys())\n", "    row_beats_col = row_beats_col_freq.loc[model_names, model_names]\n", "    return row_beats_col\n", "\n", "def visualize_pairwise_win_fraction(battles, title, max_num_models=30):\n", "    row_beats_col = compute_pairwise_win_fraction(battles, max_num_models)\n", "    fig = px.imshow(row_beats_col, color_continuous_scale='RdBu',\n", "                    text_auto=\".2f\", title=title)\n", "    fig.update_layout(xaxis_title=\" Model B: Loser\",\n", "                  yaxis_title=\"Model A: Winner\",\n", "                  xaxis_side=\"top\", height=900, width=900,\n", "                  title_y=0.07, title_x=0.5)\n", "    fig.update_traces(hovertemplate=\n", "                  \"Model A: %{y}<br>Model B: %{x}<br>Fraction of A Wins: %{z}<extra></extra>\")\n", "\n", "    return fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["fig = visualize_pairwise_win_fraction(battles_no_ties,\n", "      title = \"Fraction of Model A Wins for All Non-tied A vs. B Battles\")\n", "fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["def compute_online_elo(battles, K=4, SCALE=400, BASE=10, INIT_RATING=1500):\n", "    rating = defaultdict(lambda: INIT_RATING)\n", "\n", "    for rd, model_a, model_b, winner in battles[['model_a', 'model_b', 'winner']].itertuples():\n", "        ra = rating[model_a]\n", "        rb = rating[model_b]\n", "        ea = 1 / (1 + BASE ** ((rb - ra) / SCALE))\n", "        eb = 1 / (1 + BASE ** ((ra - rb) / SCALE))\n", "        if winner == \"model_a\":\n", "            sa = 1\n", "        elif winner == \"model_b\":\n", "            sa = 0\n", "        elif winner == \"Tie\":\n", "            sa = 0.5\n", "        else:\n", "            raise Exception(f\"unexpected vote {winner}\")\n", "        rating[model_a] += K * (sa - ea)\n", "        rating[model_b] += K * (1 - sa - eb)\n", "\n", "    return rating"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preety_print_model_ratings(ratings):\n", "    df = pd.DataFrame([\n", "        [n, ratings[n]] for n in ratings.keys()\n", "    ], columns=[\"Model\", \"Elo rating\"]).sort_values(\"Elo rating\", ascending=False).reset_index(drop=True)\n", "    # df[\"Elo rating\"] = (df[\"Elo rating\"] + 0.5).astype(int)\n", "    df.index = df.index + 1\n", "    return df\n", "\n", "def preety_print_two_ratings(ratings_1, ratings_2, column_names):\n", "    df = pd.DataFrame([\n", "        [n, ratings_1[n], ratings_2[n]] for n in ratings_1.keys()\n", "    ], columns=[\"Model\", column_names[0], column_names[1]]).sort_values(column_names[0], ascending=False).reset_index(drop=True)\n", "    df[column_names[0]] = (df[column_names[0]] + 0.5).astype(int)\n", "    df[column_names[1]] = (df[column_names[1]] + 0.5).astype(int)\n", "    df.index = df.index + 1\n", "    return df\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["online_elo_ratings = compute_online_elo(battles)\n", "preety_print_model_ratings(online_elo_ratings)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Maximum Likelihood Estimation with [<PERSON><PERSON> model](https://en.wikipedia.org/wiki/Bradley%E2%80%93Terry_model)\n", "\n", "In the context of LLM evaluation, models can be assumed to be static. In this case, we can directly fit the ratings by maximum likelihood estimation method (aka <PERSON><PERSON> model), which produce significantly stable ratings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "\n", "def compute_mle_elo(df, scale=400, BASE=10, INIT_RATING=1500):\n", "    df = df.reset_index(drop=True)\n", "\n", "    # Create a mapping for the score\n", "    score_map = {'model_a': 1, 'model_b': 0, 'Tie': 0.5}\n", "    df['score'] = df['winner'].map(score_map)\n", "    \n", "    # Create a list of all unique models\n", "    all_models = sorted(set(df['model_a'].unique()) | set(df['model_b'].unique()))\n", "    model_to_index = {model: i for i, model in enumerate(all_models)}\n", "    \n", "    # Prepare the feature matrix and target vector\n", "    n_comparisons = len(df)\n", "    n_models = len(all_models)\n", "    X = np.zeros((n_comparisons, n_models))\n", "    \n", "    # Use vectorized operations instead of loop\n", "    X[np.arange(n_comparisons), df['model_a'].map(model_to_index)] = 1+math.log(BASE)\n", "    X[np.arange(n_comparisons), df['model_b'].map(model_to_index)] = -1-math.log(BASE)\n", "    \n", "    # Convert scores to discrete classes\n", "    le = LabelEncoder()\n", "    y = le.fit_transform(df['score'])\n", "    \n", "    # Fit the logistic regression model\n", "    lr = OneVsRestClassifier(LogisticRegression(fit_intercept=False, solver='lbfgs', max_iter=1000))\n", "    lr.fit(X, y)\n", "    \n", "    # Convert coefficients to Elo ratings\n", "    model_a_win_class = np.where(le.classes_ == 1)[0][0]\n", "    elo_ratings = (scale ) * lr.estimators_[model_a_win_class].coef_[0] + INIT_RATING  \n", "    # Create a Series with model names and their Elo ratings\n", "    elo_series = pd.Series(elo_ratings, index=all_models)\n", "    \n", "    return elo_series.sort_values(ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["elo_mle_ratings = compute_mle_elo(battles)\n", "preety_print_model_ratings(elo_mle_ratings)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_bootstrap_result(battles, func_compute_elo, num_round):\n", "    rows = []\n", "    for i in tqdm(range(num_round), desc=\"bootstrap\"):\n", "        rows.append(func_compute_elo(battles.sample(frac=1.0, replace=True)))\n", "    df = pd.DataFrame(rows)\n", "    return df[df.median().sort_values(ascending=False).index]\n", "\n", "BOOTSTRAP_ROUNDS = 200\n", "\n", "np.random.seed(42)\n", "bootstrap_elo_lu = get_bootstrap_result(battles, compute_mle_elo, BOOTSTRAP_ROUNDS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_bootstrap_scores(df, title):\n", "    bars = pd.DataFrame(dict(\n", "        lower = df.quantile(.025),\n", "        rating = df.quantile(.5),\n", "        upper = df.quantile(.975))).reset_index(names=\"model\").sort_values(\"rating\", ascending=False)\n", "    bars['error_y'] = bars['upper'] - bars[\"rating\"]\n", "    bars['error_y_minus'] = bars['rating'] - bars[\"lower\"]\n", "    bars['rating_rounded'] = np.round(bars['rating'], 2)\n", "    fig = px.scatter(bars, x=\"model\", y=\"rating\", error_y=\"error_y\",\n", "                     error_y_minus=\"error_y_minus\", text=\"rating_rounded\",\n", "                     title=title)\n", "    fig.update_layout(xaxis_title=\"Model\", yaxis_title=\"Rating\",\n", "                      height=600)\n", "    return fig\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bootstrap_online_elo = get_bootstrap_result(battles, compute_online_elo, BOOTSTRAP_ROUNDS)\n", "preety_print_two_ratings(bootstrap_elo_lu.quantile(.5),\n", "                         bootstrap_online_elo.quantile(.5),\n", "                         column_names=[\"Bootstrap Median of MLE Elo\", \"Bootstrap Median of Online Elo\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize_bootstrap_scores(bootstrap_elo_lu, \"Bootstrap of MLE Elo Rating Estimates\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["px.violin(bootstrap_elo_lu.melt(), x=\"variable\", y=\"value\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_win_rate(elo_ratings, SCALE=300, BASE=15):\n", "    names = sorted(list(elo_ratings.keys()))\n", "    wins = defaultdict(lambda: defaultdict(lambda: 0))\n", "    for a in names:\n", "        for b in names:\n", "            ea = 1 / (1 + BASE ** ((elo_ratings[b] - elo_ratings[a]) / SCALE))\n", "            wins[a][b] = ea\n", "            wins[b][a] = 1 - ea\n", "\n", "    data = {\n", "        a: [wins[a][b] if a != b else np.NAN for b in names]\n", "        for a in names\n", "    }\n", "\n", "    df = pd.DataFrame(data, index=names)\n", "    df.index.name = \"model_a\"\n", "    df.columns.name = \"model_b\"\n", "    return df.T"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["win_rate = predict_win_rate(dict(bootstrap_elo_lu.quantile(0.5)))\n", "ordered_models = win_rate.mean(axis=1).sort_values(ascending=False).index\n", "ordered_models = ordered_models[:30]\n", "fig = px.imshow(win_rate.loc[ordered_models, ordered_models], # type: ignore\n", "                color_continuous_scale='RdBu', text_auto=\".2f\",\n", "                title=\"Predicted Win Rate Using El<PERSON> Ratings for Model A in an A vs. B Battle\")\n", "fig.update_layout(xaxis_title=\"Model B\",\n", "                  yaxis_title=\"Model A\",\n", "                  xaxis_side=\"top\", height=900, width=900,\n", "                  title_y=0.07, title_x=0.5)\n", "fig.update_traces(hovertemplate=\n", "                  \"Model A: %{y}<br>Model B: %{x}<br>Win Rate: %{z}<extra></extra>\")\n", "fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["battles.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from collections import defaultdict\n", "from typing import Dict, List, NamedTuple\n", "from dataclasses import dataclass\n", "from tqdm import tqdm\n", "\n", "class EloStats(NamedTuple):\n", "    median: float\n", "    lower_ci: float\n", "    upper_ci: float\n", "    num_battles: int\n", "\n", "@dataclass\n", "class BootstrapEloResults:\n", "    ratings: Dict[str, EloStats]\n", "    bootstrap_samples: Dict[str, List[float]]\n", "    \n", "def compute_single_elo_run(battles_df: pd.DataFrame, \n", "                          rating_column: str,\n", "                          k_factor: float = 32,\n", "                          initial_rating: float = 1500) -> Dict[str, float]:\n", "    \"\"\"Compute single Elo rating run.\"\"\"\n", "    ratings = defaultdict(lambda: initial_rating)\n", "    \n", "    for _, battle in battles_df.iterrows():\n", "        model_a = battle['model_a']\n", "        model_b = battle['model_b']\n", "        score = battle[rating_column]\n", "        \n", "        if pd.isna(score):\n", "            continue\n", "            \n", "        # Convert rating to score\n", "        if score < 0:\n", "            actual_score = 1  # A wins\n", "        elif score > 0:\n", "            actual_score = 0  # B wins\n", "        else:\n", "            actual_score = 0.5  # Tie\n", "            \n", "        # Calculate expected scores\n", "        rating_diff = ratings[model_b] - ratings[model_a]\n", "        expected_score_a = 1 / (1 + 10 ** (rating_diff / 400))\n", "        \n", "        # Update ratings\n", "        rating_change = k_factor * (actual_score - expected_score_a)\n", "        ratings[model_a] += rating_change\n", "        ratings[model_b] -= rating_change\n", "        \n", "    return dict(ratings)\n", "\n", "def bootstrap_elo_ratings(battles_df: pd.DataFrame,\n", "                         rating_column: str,\n", "                         n_bootstrap: int = 1000,\n", "                         k_factor: float = 32,\n", "                         initial_rating: float = 1500,\n", "                         ci_width: float = 0.95) -> BootstrapEloResults:\n", "    \"\"\"\n", "    Compute bootstrapped Elo ratings with confidence intervals.\n", "    \n", "    Args:\n", "        battles_df: DataFrame with model battles\n", "        rating_column: Column to use for determining winners\n", "        n_bootstrap: Number of bootstrap iterations\n", "        k_factor: Elo K-factor\n", "        initial_rating: Starting Elo rating\n", "        ci_width: Width of confidence interval (0.95 = 95% CI)\n", "    \"\"\"\n", "    # Count battles per model\n", "    model_battles = defaultdict(int)\n", "    for _, row in battles_df.iterrows():\n", "        model_battles[row['model_a']] += 1\n", "        model_battles[row['model_b']] += 1\n", "\n", "    # Store bootstrap results\n", "    bootstrap_ratings = defaultdict(list)\n", "    \n", "    # Run bootstrap iterations\n", "    for _ in tqdm(range(n_bootstrap), desc=f\"Bootstrap {rating_column}\"):\n", "        # Sam<PERSON> with replacement\n", "        bootstrap_sample = battles_df.sample(n=len(battles_df), replace=True)\n", "        ratings = compute_single_elo_run(bootstrap_sample, rating_column, k_factor, initial_rating)\n", "        \n", "        for model, rating in ratings.items():\n", "            bootstrap_ratings[model].append(rating)\n", "    \n", "    # Compute statistics\n", "    alpha = (1 - ci_width) / 2\n", "    percentiles = [alpha * 100, 50, (1 - alpha) * 100]\n", "    \n", "    ratings_stats = {}\n", "    for model, ratings in bootstrap_ratings.items():\n", "        lower, median, upper = np.percentile(ratings, percentiles)\n", "        ratings_stats[model] = EloStats(\n", "            median=median,\n", "            lower_ci=lower,\n", "            upper_ci=upper,\n", "            num_battles=model_battles[model]\n", "        )\n", "    \n", "    return BootstrapEloResults(ratings_stats, bootstrap_ratings)\n", "\n", "def analyze_all_metrics_bootstrap(battles_df: pd.DataFrame,\n", "                                min_battles: int = 50,\n", "                                n_bootstrap: int = 1000) -> Dict[str, pd.DataFrame]:\n", "    \"\"\"\n", "    Analyze all rating metrics using bootstrap and return sorted DataFrames with results.\n", "    \"\"\"\n", "    metrics = {\n", "        'overall': 'overallRating',\n", "        'formatting': 'formattingRating',\n", "        'instruction_following': 'instructionFollowingRating',\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    for metric_name, column in metrics.items():\n", "        print(f\"\\nAnalyzing {metric_name}...\")\n", "        bootstrap_results = bootstrap_elo_ratings(battles_df, column, n_bootstrap=n_bootstrap)\n", "        \n", "        # Create DataFrame with results\n", "        df = pd.DataFrame([\n", "            {\n", "                'model': model,\n", "                'elo_rating': stats.median,\n", "                'lower_ci': stats.lower_ci,\n", "                'upper_ci': stats.upper_ci,\n", "                'ci_width': stats.upper_ci - stats.lower_ci,\n", "                'num_battles': stats.num_battles\n", "            }\n", "            for model, stats in bootstrap_results.ratings.items()\n", "        ])\n", "        \n", "        # Filter by minimum battles and sort\n", "        df = df[df['num_battles'] >= min_battles].sort_values('elo_rating', ascending=False)\n", "        \n", "        # Round numeric columns\n", "        numeric_cols = ['elo_rating', 'lower_ci', 'upper_ci', 'ci_width']\n", "        df[numeric_cols] = df[numeric_cols].round(1)\n", "        \n", "        results[metric_name] = df\n", "        \n", "    return results\n", "\n", "def print_rankings(rankings: Dict[str, pd.DataFrame]):\n", "    \"\"\"Pretty print all rankings with headers and confidence intervals.\"\"\"\n", "    for metric_name, df in rankings.items():\n", "        print(f\"\\n=== {metric_name.upper()} RANKINGS ===\")\n", "        print(df.to_string(index=False))\n", "        print(f\"Number of qualified models: {len(df)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rankings = analyze_all_metrics_bootstrap(\n", "    battles,\n", "    min_battles=50,\n", "    n_bootstrap=250\n", ")\n", "print_rankings(rankings)\n", "\n", "# Optional: Save to CSV\n", "for metric_name, df in rankings.items():\n", "    df.to_csv(f\"elo_rankings_{metric_name}_bootstrap.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "from typing import Dict, List, Optional\n", "import plotly.graph_objects as go\n", "\n", "\n", "def plot_elo_distributions(bootstrap_results: BootstrapEloResults,\n", "                          metric_name: str,\n", "                          min_battles: int = 50,\n", "                          figsize: tuple = (12, 8)) -> None:\n", "    \"\"\"\n", "    Create violin plot of Elo rating distributions using seaborn.\n", "    \n", "    Args:\n", "        bootstrap_results: BootstrapEloResults object containing ratings and samples\n", "        metric_name: Name of the metric being plotted\n", "        min_battles: Minimum number of battles required to include model\n", "        figsize: Size of the figure (width, height)\n", "    \"\"\"\n", "    # Convert bootstrap samples to long format DataFrame\n", "    data = []\n", "    for model, samples in bootstrap_results.bootstrap_samples.items():\n", "        if bootstrap_results.ratings[model].num_battles >= min_battles:\n", "            data.extend([(model, rating) for rating in samples])\n", "    \n", "    df = pd.DataFrame(data, columns=['Model', 'Elo Rating'])\n", "    \n", "    # Sort models by median rating\n", "    model_order = df.groupby('Model')['Elo Rating'].median().sort_values(ascending=False).index\n", "    \n", "    # Create violin plot\n", "    plt.figure(figsize=figsize)\n", "    sns.violinplot(data=df, x='Model', y='Elo Rating', order=model_order)\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.title(f'Elo Rating Distributions - {metric_name}')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "def analyze_and_visualize_metric(battles_df: pd.DataFrame,\n", "                               metric_name: str,\n", "                               column: str,\n", "                               min_battles: int = 50,\n", "                               n_bootstrap: int = 1000) -> None:\n", "    \"\"\"\n", "    Analyze a single metric and create visualizations.\n", "    \n", "    Args:\n", "        battles_df: DataFrame containing battle data\n", "        metric_name: Name of the metric\n", "        column: Column name in the DataFrame\n", "        min_battles: Minimum number of battles required\n", "        n_bootstrap: Number of bootstrap iterations\n", "    \"\"\"\n", "    print(f\"\\nAnalyzing {metric_name}...\")\n", "    bootstrap_results = bootstrap_elo_ratings(battles_df, column, n_bootstrap=n_bootstrap)\n", "    \n", "    # Print numerical results\n", "    df = pd.DataFrame([\n", "        {\n", "            'model': model,\n", "            'elo_rating': stats.median,\n", "            'lower_ci': stats.lower_ci,\n", "            'upper_ci': stats.upper_ci,\n", "            'ci_width': stats.upper_ci - stats.lower_ci,\n", "            'num_battles': stats.num_battles\n", "        }\n", "        for model, stats in bootstrap_results.ratings.items()\n", "    ])\n", "    df = df[df['num_battles'] >= min_battles].sort_values('elo_rating', ascending=False)\n", "    print(\"\\nNumerical Results:\")\n", "    print(df.round(1).to_string(index=False))\n", "    \n", "    # Create visualizations\n", "    print(\"\\nGenerating visualizations...\")\n", "    plot_elo_distributions(bootstrap_results, metric_name, min_battles)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metrics = {\n", "    'Overall Rating': 'overallRating',\n", "    'Formatting': 'formattingRating',\n", "    'Instruction Following': 'instructionFollowingRating',\n", "}\n", "\n", "for metric_name, column in metrics.items():\n", "    analyze_and_visualize_metric(\n", "        battles,\n", "        metric_name,\n", "        column,\n", "        min_battles=50,\n", "        n_bootstrap=100\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["battles.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["battles.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}
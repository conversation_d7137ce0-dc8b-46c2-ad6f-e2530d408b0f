import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import math
from collections import defaultdict
import io
import base64
from typing import Dict, List, Tuple
import re


def calculate_separate_stats(df: pd.DataFrame) -> pd.DataFrame:
    grouped = df.groupby(["model_a", "model_b"])
    separate_stats = grouped.agg(
        {"overallRating": ["mean", lambda x: (x == 0).sum(), "count"]}
    ).reset_index()

    separate_stats.columns = [
        "model_a",
        "model_b",
        "mean_rating",
        "ties",
        "total_comparisons",
    ]

    separate_stats["model_a_wins"] = (
        grouped["overallRating"].apply(lambda x: (x < 0).sum()).values
    )
    separate_stats["model_b_wins"] = (
        grouped["overallRating"].apply(lambda x: (x > 0).sum()).values
    )

    separate_stats["model_a_win_pct"] = (
        separate_stats["model_a_wins"] / separate_stats["total_comparisons"]
    )
    separate_stats["model_b_win_pct"] = (
        separate_stats["model_b_wins"] / separate_stats["total_comparisons"]
    )
    separate_stats["tie_pct"] = (
        separate_stats["ties"] / separate_stats["total_comparisons"]
    )

    separate_stats["overall_winner"] = np.select(
        [
            separate_stats["model_a_win_pct"] > separate_stats["model_b_win_pct"],
            separate_stats["model_b_win_pct"] > separate_stats["model_a_win_pct"],
        ],
        [separate_stats["model_a"], separate_stats["model_b"]],
        default="Tie",
    )

    return separate_stats.sort_values("total_comparisons", ascending=False)


def calculate_combined_stats(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    df["model_pair"] = df.apply(
        lambda row: tuple(sorted([row["model_a"], row["model_b"]])), axis=1
    )
    grouped = df.groupby("model_pair")

    combined_stats = grouped.size().reset_index(name="total_comparisons")
    combined_stats["ties"] = (
        grouped["overallRating"].apply(lambda x: (x == 0).sum()).values
    )

    combined_stats["winning_model"] = combined_stats.apply(
        lambda row: df[df["model_pair"] == row["model_pair"]]["winning_model"]
        .mode()
        .iloc[0],
        axis=1,
    )

    combined_stats["win_rate"] = combined_stats.apply(
        lambda row: (
            df[df["model_pair"] == row["model_pair"]]["winning_model"]
            == row["winning_model"]
        ).sum()
        / row["total_comparisons"],
        axis=1,
    )
    combined_stats["tie_rate"] = (
        combined_stats["ties"] / combined_stats["total_comparisons"]
    )
    combined_stats["loss_rate"] = (
        1 - combined_stats["win_rate"] - combined_stats["tie_rate"]
    )

    combined_stats["is_different_pair"] = combined_stats["model_pair"].apply(
        lambda x: x[0] != x[1]
    )
    combined_stats = combined_stats.sort_values(
        ["is_different_pair", "total_comparisons"], ascending=[False, False]
    )
    combined_stats = combined_stats.drop("is_different_pair", axis=1)
    return combined_stats


def style_dataframe(
    df: pd.DataFrame,
) -> pd.DataFrame.style:  # Changed return type annotation
    return (
        df.style.set_properties(
            **{
                "border-collapse": "collapse",
                "border": "1px solid #ddd",
                "padding": "8px",
                "text-align": "left",
            }
        )
        .set_table_styles(
            [
                {
                    "selector": "th",
                    "props": [("background-color", "#f2f2f2"), ("font-weight", "bold")],
                }
            ]
        )
        .format(precision=3)
    )


def expected_score(rating_a: float, rating_b: float) -> float:
    return 1 / (1 + math.pow(10, (rating_b - rating_a) / 400))


def update_elo(
    rating_a: float, rating_b: float, score_a: float, k_factor: int = 32
) -> float:
    expected_a = expected_score(rating_a, rating_b)
    return rating_a + k_factor * (score_a - expected_a)


def calculate_elo_ratings(
    df: pd.DataFrame, k_factor: int = 32, num_iterations: int = 100
) -> Tuple[Dict[str, float], Dict[str, float]]:
    all_models = set(df["model_a"].unique()) | set(df["model_b"].unique())
    avg_ratings = {model: 0 for model in all_models}
    avg_comparison_counts = {model: 0 for model in all_models}

    for _ in range(num_iterations):
        ratings = {model: 1500 for model in all_models}
        comparison_counts = {model: 0 for model in all_models}

        df_shuffled = df.sample(frac=1).reset_index(drop=True)

        for _, row in df_shuffled.iterrows():
            model_a, model_b = row["model_a"], row["model_b"]
            if model_a == model_b:
                continue

            comparison_counts[model_a] += 1
            comparison_counts[model_b] += 1

            if row["winning_model"] == model_a:
                score_a, score_b = 1, 0
            elif row["winning_model"] == model_b:
                score_a, score_b = 0, 1
            else:
                score_a, score_b = 0.5, 0.5

            new_rating_a = update_elo(
                ratings[model_a], ratings[model_b], score_a, k_factor
            )
            new_rating_b = update_elo(
                ratings[model_b], ratings[model_a], score_b, k_factor
            )

            ratings[model_a] = new_rating_a
            ratings[model_b] = new_rating_b

        for model in all_models:
            avg_ratings[model] += ratings[model]
            avg_comparison_counts[model] += comparison_counts[model]

    for model in all_models:
        avg_ratings[model] /= num_iterations
        avg_comparison_counts[model] /= num_iterations

    return avg_ratings, avg_comparison_counts


def generate_elo_table(df: pd.DataFrame) -> pd.DataFrame:
    elo_ratings, comparison_counts = calculate_elo_ratings(df)
    elo_table = pd.DataFrame(
        {
            "Model": list(elo_ratings.keys()),
            "Elo Rating": list(elo_ratings.values()),
            "Comparisons": [comparison_counts[model] for model in elo_ratings.keys()],
        }
    )
    elo_table = elo_table.sort_values("Elo Rating", ascending=False).reset_index(
        drop=True
    )
    elo_table["Elo Rating"] = elo_table["Elo Rating"].round(2)
    return elo_table


def calculate_elo_over_time(
    df: pd.DataFrame, num_iterations: int = 10, k_factor: int = 64
) -> Dict[str, List[Dict[str, float]]]:
    df = df.sort_values("datetime")
    rating_history = defaultdict(list)

    step = max(1, len(df) // 10)
    for i in range(0, len(df), step):
        current_df = df.iloc[: i + 1]
        current_ratings, _ = calculate_elo_ratings(current_df, num_iterations, k_factor)
        for model, rating in current_ratings.items():
            rating_history[model].append(
                {"rating": rating, "time": df.iloc[i]["datetime"]}
            )

    if len(df) % step != 0:
        current_ratings, _ = calculate_elo_ratings(df, num_iterations, k_factor)
        for model, rating in current_ratings.items():
            rating_history[model].append(
                {"rating": rating, "time": df.iloc[-1]["datetime"]}
            )

    return rating_history


def plot_to_html(plt) -> str:
    img = io.BytesIO()
    plt.savefig(img, format="png", bbox_inches="tight")
    img.seek(0)
    plt.close()
    return base64.b64encode(img.getvalue()).decode()


def generate_elo_plot(df: pd.DataFrame, user_id: str = None) -> str:
    if user_id:
        df = df[df["user_id"] == user_id]

    rating_history = calculate_elo_over_time(df)

    plt.figure(figsize=(12, 6))
    for model, ratings in rating_history.items():
        times = [r["time"] for r in ratings]
        ratings_values = [r["rating"] for r in ratings]
        plt.plot(times, ratings_values, label=model, marker="o")

    plt.title(f"Elo Ratings Over Time{' for ' + user_id if user_id else ''}")
    plt.xlabel("Time")
    plt.ylabel("Elo Rating")
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.tight_layout()

    return plot_to_html(plt)


def generate_category_tables(categories, subcategories):
    def create_table(data, title):
        sorted_data = sorted(data.items(), key=lambda x: x[1], reverse=True)
        table_html = f"<h3>{title}</h3>"
        table_html += (
            "<table border='1' style='border-collapse: collapse; width: 100%;'>"
        )
        table_html += "<tr><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>{}</th><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>Count</th></tr>".format(
            title
        )
        for category, count in sorted_data:
            table_html += f"<tr><td style='padding: 8px;'>{category}</td><td style='padding: 8px;'>{count}</td></tr>"
        table_html += "</table>"
        return table_html

    categories_table = create_table(categories, "Categories")
    subcategories_table = create_table(subcategories, "Sub Categories")

    return f"<div style='display: flex; justify-content: space-between;'><div style='width: 48%;'>{categories_table}</div><div style='width: 48%;'>{subcategories_table}</div></div>"


def create_score_distribution_plot(data, title):
    plt.figure(figsize=(10, 6))
    for model, scores in data.items():
        plt.hist(scores, bins=7, range=(0, 3), alpha=0.5, label=model)
    plt.title(title)
    plt.xlabel("Score")
    plt.ylabel("Frequency")
    plt.legend()
    plt.grid(True, linestyle="--", alpha=0.7)
    return plot_to_html(plt)


def remove_model_names(feedback):
    pattern = r"MODEL_IDS_START_LABEL\s*(.+?)\s*MODEL_IDS_END_LABEL"
    return re.sub(pattern, "", feedback, flags=re.DOTALL).strip()


def generate_model_comparison_tables(df: pd.DataFrame) -> str:
    """Generate HTML tables for model comparisons including separate stats, combined stats and crosstab."""
    separate_stats = calculate_separate_stats(df)
    combined_stats = calculate_combined_stats(df)
    crosstab = pd.crosstab(
        df["model_a"], df["model_b"], values=df["overallRating"], aggfunc="mean"
    )
    np.fill_diagonal(crosstab.values, np.nan)

    separate_styled = style_dataframe(separate_stats)
    combined_styled = style_dataframe(combined_stats)
    crosstab_styled = style_dataframe(crosstab)

    separate_html = separate_styled.to_html(index=False)
    combined_html = combined_styled.to_html(index=False)
    crosstab_html = crosstab_styled.to_html()

    css = """
    <style>
        .comparison-tables {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .comparison-tables h3 {
            color: #333;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-tables h4 {
            color: #444;
            margin-top: 30px;
            margin-bottom: 10px;
        }
        .comparison-tables table {
            width: 100%;
            margin-bottom: 30px;
        }
        .comparison-tables th, .comparison-tables td {
            padding: 12px;
            border: 1px solid #ddd;
        }
        .comparison-tables th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: left;
        }
        .comparison-tables tr:nth-child(even) {
            background-color: #f8f8f8;
        }
        .comparison-tables tr:hover {
            background-color: #e8e8e8;
        }
    </style>
    """

    return f"""
    {css}
    <div class="comparison-tables">
        <h3>{'User-specific' if 'user_id' in df.columns else 'Overall'} Model Comparison Tables</h3>
        <h4>Combined Comparisons:</h4>
        {combined_html}
        <h4>Cross tab Comparisons (-1: A, 0: Tie, 1: B):</h4>
        {crosstab_html}
        <h4>Separate A/B Comparisons: (Only useful for A/B bias testing) </h4>
        {separate_html}
    </div>
    """


def wrap_html(html_content):
    """Wrap the HTML content with necessary styling and structure."""
    style = """
    <style>
        .row {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }
        .feedback, .answer {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
    </style>
    """

    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Question and Answers</title>
        {style}
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """

import plotly.express as px
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional

# Copyright (c) 2024
# Licensed under MIT License

from typing import List, NamedTuple, Union
from sklearn.preprocessing import LabelEncoder
from sklearn.linear_model import LogisticRegression
from sklearn.multiclass import OneVsRestClassifier
from tqdm import tqdm
import math
from dataclasses import dataclass


class EloStats(NamedTuple):
    median: float
    lower_ci: float
    upper_ci: float
    num_battles: int


@dataclass
class BootstrapEloResults:
    ratings: Dict[str, EloStats]
    bootstrap_samples: Dict[str, List[float]]


def visualize_bootstrap_scores(df: pd.DataFrame, title: str) -> px.scatter:
    """
    Create a scatter plot visualization of bootstrap Elo rating scores with error bars.

    Args:
        df: DataFrame containing bootstrap results with models as columns
        title: Title for the visualization

    Returns:
        plotly.express.scatter: Interactive scatter plot figure

    Example:
        >>> bootstrap_results = pd.DataFrame({
        ...     'model1': [1500, 1550, 1480],
        ...     'model2': [1600, 1620, 1580]
        ... })
        >>> fig = visualize_bootstrap_scores(bootstrap_results, "Bootstrap Results")
        >>> fig.show()
    """
    if df is None or df.empty:
        raise ValueError("Input DataFrame cannot be None or empty")

    # Calculate statistics
    bars = (
        pd.DataFrame(
            {
                "lower": df.quantile(0.025),
                "rating": df.quantile(0.5),
                "upper": df.quantile(0.975),
            }
        )
        .reset_index(names="model")
        .sort_values("rating", ascending=False)
    )

    # Calculate error bars
    bars["error_y"] = bars["upper"] - bars["rating"]
    bars["error_y_minus"] = bars["rating"] - bars["lower"]
    bars["rating_rounded"] = np.round(bars["rating"], 2)

    # Create visualization
    fig = px.scatter(
        bars,
        x="model",
        y="rating",
        error_y="error_y",
        error_y_minus="error_y_minus",
        text="rating_rounded",
        title=title,
    )

    # Update layout
    fig.update_layout(
        xaxis_title="Model",
        yaxis_title="Rating",
        height=600,
        showlegend=False,
        title_x=0.5,
    )

    # Improve readability
    fig.update_xaxes(tickangle=45)
    fig.update_traces(marker=dict(size=10), textposition="top center")

    return fig


def predict_win_rate(
    elo_ratings: Dict[str, float], scale: int = 300, base: int = 15
) -> pd.DataFrame:
    """
    Calculate predicted win rates between all model pairs based on Elo ratings.

    Args:
        elo_ratings: Dictionary mapping model names to their Elo ratings
        scale: Scale factor for Elo calculation (default: 300)
        base: Base factor for Elo calculation (default: 15)

    Returns:
        pd.DataFrame: Matrix of predicted win rates

    Example:
        >>> ratings = {'model1': 1500, 'model2': 1600}
        >>> win_rates = predict_win_rate(ratings)
        >>> print(win_rates)
    """
    if not elo_ratings:
        raise ValueError("Elo ratings dictionary cannot be empty")

    names = sorted(list(elo_ratings.keys()))
    wins = {a: {b: 0.0 for b in names} for a in names}

    # Calculate win probabilities
    for a in names:
        for b in names:
            if a != b:
                ea = 1 / (1 + base ** ((elo_ratings[b] - elo_ratings[a]) / scale))
                wins[a][b] = ea
                wins[b][a] = 1 - ea

    # Convert to DataFrame
    data = {a: [wins[a][b] if a != b else np.nan for b in names] for a in names}

    df = pd.DataFrame(data, index=names)
    df.index.name = "model_a"
    df.columns.name = "model_b"

    return df.T


def plot_win_rate_heatmap(
    win_rate: pd.DataFrame,
    top_n: Optional[int] = 30,
    title: str = "Predicted Win Rate Using Elo Ratings",
) -> px.imshow:
    """
    Create a heatmap visualization of predicted win rates between models.

    Args:
        win_rate: DataFrame containing win rate matrix
        top_n: Number of top models to include (default: 30)
        title: Title for the visualization

    Returns:
        plotly.express.imshow: Interactive heatmap figure

    Example:
        >>> win_rates = predict_win_rate({'model1': 1500, 'model2': 1600})
        >>> fig = plot_win_rate_heatmap(win_rates)
        >>> fig.show()
    """
    if win_rate is None or win_rate.empty:
        raise ValueError("Win rate DataFrame cannot be None or empty")

    # Select top N models
    ordered_models = win_rate.mean(axis=1).sort_values(ascending=False).index
    if top_n:
        ordered_models = ordered_models[:top_n]

    # Create heatmap
    fig = px.imshow(
        win_rate.loc[ordered_models, ordered_models],
        color_continuous_scale="RdBu",
        text_auto=".2f",
        title=title,
    )

    # Update layout
    fig.update_layout(
        xaxis_title="Model B",
        yaxis_title="Model A",
        xaxis_side="top",
        height=900,
        width=900,
        title_y=0.07,
        title_x=0.5,
    )

    # Add hover template
    fig.update_traces(
        hovertemplate="Model A: %{y}<br>Model B: %{x}<br>Win Rate: %{z}<extra></extra>"
    )

    return fig

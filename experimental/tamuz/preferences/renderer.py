"""Module for rendering annotation samples."""

from markdown2 import markdown as _markdown2
from markdown import markdown as _markdown1
from copy import deepcopy


def markdown2(text):
    return _markdown2(
        text, extras=["fenced-code-blocks", "code-friendly"], safe_mode="escape"
    )


def markdown1(text):
    return _markdown1(text, extensions=["fenced_code", "codehilite"])


class SampleRenderer:
    def __init__(self, sample_data, history_limit=3):
        self.data = sample_data
        self.history_limit = history_limit

    @staticmethod
    def rating_to_letter(rating):
        """Convert numerical rating to letter format."""
        if rating == -100:
            return "Empty"
        if rating == 0:
            return "="
        return f"{'A' if rating < 0 else 'B'}{abs(rating)}"

    def get_feedback_data(self):
        """Extract and format feedback scores."""
        scores = self.data["scores"]
        return {
            "formattingRating": self.rating_to_letter(scores["formattingRating"]),
            "hallucinationRating": self.rating_to_letter(scores["hallucinationRating"]),
            "instructionFollowingRating": self.rating_to_letter(
                scores["instructionFollowingRating"]
            ),
            "overallRating": self.rating_to_letter(scores["overallRating"]),
            "isHighQuality": "Yes" if scores["isHighQuality"] else "No",
            "feedback": markdown1(self.data.get("feedback", "").replace("\n", "<br>")),
        }

    def render_simple_message(self, message, response):
        """Render a single message-response pair."""
        message = markdown2(message)
        response = markdown2(response)
        return f"""
        <div class="row feedback">
            <div>{message}</div>
        </div>
        <div class="row">
            <div class="answer" style="flex: 1;">{response}</div>
        </div>
        """

    def render_chat_history(self):
        """Render the chat history section."""
        history = self.data["option_a"]["request"].get("chat_history", "")
        if not history:
            return "<div><p>Chat history: N/A</p></div>"

        html_content = ""
        for i, exchange in enumerate(history[-self.history_limit :]):
            if "response_text" not in exchange:
                continue
            html_content += f"<h3>Message {i}</h3>"
            html_content += self.render_simple_message(
                exchange["request_message"], exchange["response_text"]
            )
            html_content += "<hr>"

        return f"""
        <details>
            <summary>Chat history</summary>
            {html_content}
        </details>
        """

    def render_selected_code(self):
        """Render the selected code section."""
        selected_code = self.data["option_a"]["request"].get("selected_code", "")
        if not selected_code:
            return "<div><p>Selected code: N/A</p></div>"

        code_markdown = f"```\n{selected_code}\n```"
        return f"""
        <details>
            <summary>Selected code</summary>
            <div class="row">
                <div class="answer" style="flex: 1;">{markdown2(code_markdown)}</div>
            </div>
        </details>
        """

    def render_comparison(self):
        """Render the comparison between option A and B."""
        if not self._validate_comparison():
            return ""

        message = markdown2(self.data["option_a"]["request"]["message"])
        answers = [
            markdown2(self.data["option_a"]["response"]["text"]),
            markdown2(self.data["option_b"]["response"]["text"]),
        ]
        feedback = self.get_feedback_data()

        html_content = f"""
        <div class="row feedback">
            <div>{message}</div>
        </div>
        <div class="row feedback">
            <ul>
        """

        for key, value in feedback.items():
            html_content += f"<li><strong>{key}:</strong> {value}</li>"

        html_content += """
            </ul>
        </div>
        <div class="row">
        """

        for answer in answers:
            html_content += f'<div class="answer" style="flex: 1;">{answer}</div>'

        html_content += "</div>"
        return html_content

    def _validate_comparison(self):
        """Validate that the sample contains all required fields for comparison."""
        required_fields = ["option_a", "option_b"]
        for field in required_fields:
            if field not in self.data:
                return False
            if "response" not in self.data[field]:
                return False
            if "text" not in self.data[field]["response"]:
                return False
        return True

    def render(self):
        """Render the complete sample with all components."""
        html_content = ""
        html_content += self.render_chat_history()
        html_content += self.render_selected_code()
        html_content += f"<h3>Message {len(self.data['option_a']['request'].get('chat_history', ''))}</h3>"
        html_content += self.render_comparison()
        return html_content

"""Module for retrieving and processing annotation data."""

from google.cloud import bigquery
from tqdm import tqdm


class AnnotationsRetriever:
    def __init__(self, project_id, dataset_name, start_day, print_query=True):
        self.project_id = project_id
        self.dataset_name = dataset_name
        self.start_day = start_day
        self.print_query = print_query
        self.client = bigquery.Client(project=project_id)

    def get_preferences_samples(self, tenant_name):
        """Retrieve preference samples for a given tenant."""
        query = f"""
        SELECT *
        FROM {self.project_id}.{self.dataset_name}.preference_sample
        WHERE tenant="{tenant_name}"
        AND day >= "{self.start_day}"
        ORDER BY time DESC
        LIMIT 1000000
        """
        print(f"Getting preferences samples from {tenant_name}")
        if self.print_query:
            print(query)

        rows = [*self.client.query_and_wait(query)]
        return [
            {"preference_request_id": row.request_id, **dict(row)["raw_json"]}
            for row in rows
        ]


    def get_lightly_enriched_samples(self, tenant_name):
        """Retrieve preferences and model names for both options A and B."""
        query = f"""
        WITH preference_data AS (
            SELECT
                preference.request_id as preference_request_id,
                preference.raw_json,
                JSON_EXTRACT_SCALAR(request_id_a) as request_id_a,
                JSON_EXTRACT_SCALAR(request_id_b) as request_id_b
            FROM {self.project_id}.{self.dataset_name}.preference_sample as preference
            CROSS JOIN UNNEST(JSON_EXTRACT_ARRAY(preference.raw_json, '$.request_ids')) as request_id_a WITH OFFSET as offset_a
            CROSS JOIN UNNEST(JSON_EXTRACT_ARRAY(preference.raw_json, '$.request_ids')) as request_id_b WITH OFFSET as offset_b
            WHERE preference.tenant = "{tenant_name}"
            AND preference.day >= "{self.start_day}"
            AND offset_a = 0
            AND offset_b = 1
        )

        SELECT
            pd.preference_request_id,
            pd.request_id_a,
            pd.request_id_b,
            pd.raw_json,
            JSON_EXTRACT_SCALAR(metadata_a.raw_json, '$.request.model_name') as model_a,
            JSON_EXTRACT_SCALAR(metadata_b.raw_json, '$.request.model_name') as model_b
        FROM preference_data pd
        LEFT JOIN {self.project_id}.{self.dataset_name}.chat_host_request as metadata_a
            ON metadata_a.request_id = pd.request_id_a
            AND metadata_a.tenant = "{tenant_name}"
            AND metadata_a.day >= "{self.start_day}"
        LEFT JOIN {self.project_id}.{self.dataset_name}.chat_host_request as metadata_b
            ON metadata_b.request_id = pd.request_id_b
            AND metadata_b.tenant = "{tenant_name}"
            AND metadata_b.day >= "{self.start_day}"
        ORDER BY pd.preference_request_id DESC
        LIMIT 1000000
        """
        
        rows = self.client.query(query).result()
        return [dict(row) for row in rows]

    def get_chat_samples(self, request_ids, tenant_name):
        """Retrieve chat samples for given request IDs and tenant."""
        query = f"""
        SELECT
            metadata.request_id AS request_id,
            metadata.raw_json AS metadata,
            request.raw_json AS request,
            response.raw_json AS response,
            metadata.time AS time
        FROM {self.project_id}.{self.dataset_name}.request_metadata AS metadata
        JOIN {self.project_id}.{self.dataset_name}.chat_host_request AS request
            ON request.request_id = metadata.request_id
        JOIN {self.project_id}.{self.dataset_name}.chat_host_response AS response
            ON response.request_id = metadata.request_id
        WHERE
            metadata.request_id IN ({','.join(f'"{request_id}"' for request_id in request_ids)})
            AND metadata.day >= "{self.start_day}"
            AND request.day >= "{self.start_day}"
            AND response.day >= "{self.start_day}"
            AND metadata.tenant="{tenant_name}"
            AND request.tenant="{tenant_name}"
            AND response.tenant="{tenant_name}"
        """
        print(f"Getting chat samples from {tenant_name}")
        if self.print_query:
            print(query)

        chat_rows_dic = {}
        for index, row in enumerate(self.client.query(query).result()):
            if index % 1000 == 0:
                print(f"   Processed {index} samples")

            if row.request_id in chat_rows_dic:
                # print(f"Duplicate request_id: {row.request_id}")
                continue

            chat_rows_dic[row.request_id] = {
                "request": row.request["request"],
                "response": row.response["response"],
                "metadata": row.metadata,
                "datetime": row.time,
                "request_token_ids": row.request["tokenization"]["token_ids"],
            }

        return chat_rows_dic

    def get_enriched_samples(self, excluded_users=None, tenant_name=None):
        """Get enriched samples from all tenants."""
        if excluded_users is None:
            excluded_users = ["<EMAIL>"]

        # Get samples from both tenants
        all_samples = [
            {**sample, "tenant_name": tenant_name}
            for sample in self.get_preferences_samples(tenant_name)
        ]

        # Enrich samples with chat data
        enriched_samples = self._enrich_samples_with_chats(all_samples, tenant_name)

        print(f"Total samples: {len(enriched_samples)}")
        return [
            sample
            for sample in enriched_samples
            if sample["option_a"]["user_id"] not in excluded_users
        ]

    def _enrich_samples_with_chats(self, samples, tenant_name):
        """Enrich samples with chat information."""
        # Get all unique request IDs
        request_ids = set()
        for sample in samples:
            request_ids.update(sample["request_ids"])

        # Fetch chat data
        chat_dict = self.get_chat_samples(list(request_ids), tenant_name)

        enriched_samples = []
        for sample in tqdm(samples):
            option_a = self.get_chat_sample(sample["request_ids"][0], chat_dict)
            option_b = self.get_chat_sample(sample["request_ids"][1], chat_dict)

            if not self._validate_chat_samples(
                option_a, option_b, sample["request_ids"]
            ):
                continue

            enriched_sample = self._prepare_enriched_sample(sample, option_a, option_b)
            enriched_samples.append(enriched_sample)

        return enriched_samples

    def _validate_chat_samples(self, option_a, option_b, request_ids):
        """Validate that both chat samples exist."""
        if option_a is None:
            print(f"Failed to find chat sample for option_a: {request_ids[0]}")
            return False
        if option_b is None:
            print(f"Failed to find chat sample for option_b: {request_ids[1]}")
            return False
        return True

    def _prepare_enriched_sample(self, sample, option_a, option_b):
        """Prepare enriched sample with metadata."""
        sample = sample.copy()

        # Add options
        sample["option_a"] = option_a
        sample["option_b"] = option_b

        # Add user IDs and metadata
        sample["option_a"]["user_id"] = option_a["metadata"]["user_id"]
        sample["option_b"]["user_id"] = option_b["metadata"]["user_id"]
        sample["user_agent"] = option_a["metadata"]["user_agent"]

        # Add request IDs
        sample["option_a"]["request_id"] = sample["request_ids"][0]
        sample["option_b"]["request_id"] = sample["request_ids"][1]

        return sample

    @staticmethod
    def get_chat_sample(request_id, chat_dict):
        """Get a single chat sample from the chat dictionary."""
        return chat_dict.get(request_id)

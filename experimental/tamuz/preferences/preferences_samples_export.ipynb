{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from pathlib import Path\n", "from collections import Counter\n", "from google.cloud import bigquery\n", "from markdown2 import markdown as _markdown2\n", "from markdown import markdown as _markdown1\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from copy import deepcopy\n", "import os\n", "import zipfile\n", "import datetime\n", "import re\n", "import html\n", "from matplotlib.dates import DateFormatter\n", "from IPython.display import display, HTML\n", "from scipy import stats\n", "import io\n", "import base64\n", "from collections import defaultdict\n", "from google.cloud.storage import Client, transfer_manager\n", "import numpy as np\n", "import signal\n", "from contextlib import contextmanager\n", "import cProfile\n", "import pstats\n", "import io\n", "from line_profiler import LineProfiler\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "\n", "TENANT_NAME = \"aitutor-mercor\"\n", "\n", "TURING_BUCKET_NAME = \"augment_ai\"\n", "\n", "# TENANT_NAME = \"aitutor-turing\"\n", "TURING_PATH = \"/mnt/efs/augment/user/tamuz/annotations/turing_data\"\n", "TURING_PREFIX = \"to_augment_ai\"\n", "START_DAY = \"2024-11-02\"  # Must be in date format ex: YYYY-MM-DD\n", "HISTORY_LIMIT = 10\n", "DRY_RUN = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def markdown2(text):\n", "    return _markdown2(\n", "        text, extras=[\"fenced-code-blocks\", \"code-friendly\"], safe_mode=\"escape\"\n", "    )\n", "\n", "\n", "def markdown1(text):\n", "    return _markdown1(text, extensions=[\"fenced_code\", \"codehilite\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["markdown2(\n", "    \"However, A’s use of useMemo is a notable addition that optimizes performance, even though B provides more comprehensive details\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_preferences_samples():\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.preference_sample\n", "WHERE tenant=\"{TENANT_NAME}\" \n", "AND day >= \"{START_DAY}\"\n", "ORDER BY time DESC\n", "LIMIT 1000000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    clean_rows = []\n", "    for row in rows:\n", "        clean_rows.append(\n", "            {\"preference_request_id\": row.request_id, **dict(row)[\"raw_json\"]}\n", "        )\n", "\n", "    return clean_rows\n", "\n", "\n", "def get_chat_sample(request_id, chat_dict):\n", "    if request_id in chat_dict:\n", "        return chat_dict[request_id]\n", "    else:\n", "        return None\n", "\n", "\n", "def get_chat_samples(request_ids):\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_metadata AS metadata\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\tAND metadata.day >= \"{START_DAY}\"\n", "\tAND request.day >= \"{START_DAY}\"\n", "\tAND response.day >= \"{START_DAY}\"\n", "    AND metadata.tenant=\"{TENANT_NAME}\"\n", "\tAND request.tenant=\"{TENANT_NAME}\"\n", "\tAND response.tenant=\"{TENANT_NAME}\"\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        if row.request_id in chat_rows_dic:\n", "            print(f\"Duplicate request_id: {row.request_id}\")\n", "            continue\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time,\n", "            \"request_token_ids\": row.request[\"tokenization\"][\"token_ids\"],\n", "        }\n", "\n", "    return chat_rows_dic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rating_to_letter(rating):\n", "    if rating == -100:\n", "        return \"Empty\"\n", "\n", "    if rating == 0:\n", "        return \"=\"\n", "    elif rating < 0:\n", "        return f\"A{abs(rating)}\"\n", "    else:\n", "        return f\"B{rating}\"\n", "\n", "\n", "def render_sample(sample):\n", "    assert (\n", "        sample[\"option_a\"][\"request\"][\"message\"]\n", "        == sample[\"option_b\"][\"request\"][\"message\"]\n", "    )\n", "\n", "    message = markdown2(sample[\"option_a\"][\"request\"][\"message\"])\n", "    answers = [\n", "        markdown2(sample[\"option_a\"][\"response\"].get(\"text\", \"Missing text\")),\n", "        markdown2(sample[\"option_b\"][\"response\"].get(\"text\", \"Missing text\")),\n", "    ]\n", "    feedback = {\n", "        \"formattingRating\": rating_to_letter(sample[\"scores\"][\"formattingRating\"]),\n", "        \"hallucinationRating\": rating_to_letter(\n", "            sample[\"scores\"][\"hallucinationRating\"]\n", "        ),\n", "        \"instructionFollowingRating\": rating_to_letter(\n", "            sample[\"scores\"][\"instructionFollowingRating\"]\n", "        ),\n", "        \"overallRating\": rating_to_letter(sample[\"scores\"][\"overallRating\"]),\n", "        \"isHighQuality\": \"Yes\" if sample[\"scores\"][\"isHighQuality\"] else \"No\",\n", "        \"feedback\": markdown1(sample.get(\"feedback\", \"\").replace(\"\\n\", \"<br>\")),\n", "    }\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row feedback\">\n", "    <ul>\n", "\"\"\"\n", "\n", "    for key, value in feedback.items():\n", "        html_content += f\"<li><strong>{key}:</strong> {value}</li>\"\n", "\n", "    html_content += \"\"\"\n", "            </ul>\n", "        </div>\n", "        <div class=\"row\">\n", "    \"\"\"\n", "\n", "    for answer in answers:\n", "        html_content += f'<div class=\"answer\" style=\"flex: 1;\">{answer}</div>'\n", "\n", "    html_content += \"</div>\"\n", "\n", "    return html_content\n", "\n", "\n", "def render_simple_message(message, response):\n", "    message = markdown2(message)\n", "    response = markdown2(response)\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{response}</div>\n", "</div>\n", "\"\"\"\n", "\n", "    return html_content\n", "\n", "\n", "def render_sample_with_history(sample):\n", "    cur_index = 0\n", "    html_content = \"\"\n", "\n", "    if len(sample[\"option_a\"][\"request\"].get(\"chat_history\", \"\")) > 0:\n", "        for exchange in sample[\"option_a\"][\"request\"][\"chat_history\"][-HISTORY_LIMIT:]:\n", "            html_content += f\"<h3>Message {cur_index}</h3>\"\n", "            cur_index += 1\n", "            html_content += render_simple_message(\n", "                exchange[\"request_message\"], exchange[\"response_text\"]\n", "            )\n", "            html_content += \"<hr>\"\n", "\n", "        html_content = f\"\"\"\n", "        <details>\n", "            <summary>Chat history</summary>\n", "            {html_content}\n", "        </details>\n", "        \"\"\"\n", "    else:\n", "        html_content += \"\"\"\n", "        <div>\n", "            <p>Chat history: N/A</p>\n", "        </div>\n", "\"\"\"\n", "\n", "    if len(sample[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0:\n", "        cur_selected_code = f\"\"\"```\n", "{sample[\"option_a\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "        html_content += f\"\"\"\n", "        <details>\n", "            <summary>Selected code</summary>\n", "            <div class=\"row\">\n", "                <div class=\"answer\" style=\"flex: 1;\">{markdown2(cur_selected_code)}</div>\n", "            </div>\n", "        </details>\n", "        \"\"\"\n", "    else:\n", "        html_content += \"\"\"\n", "        <div>\n", "            <p>Selected code: N/A</p>\n", "        </div>\n", "\"\"\"\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "\n", "    html_content += render_sample(sample)\n", "\n", "    return html_content\n", "\n", "\n", "def wrap_html(html_content):\n", "    start = \"\"\"<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <title>Question and Answers</title>\n", "    <style>\n", "        .row {\n", "            display: flex;\n", "            justify-content: center;\n", "            align-items: center;\n", "            margin-bottom: 20px;\n", "        }\n", "        .feedback, .answer {\n", "            margin: 10px;\n", "            padding: 10px;\n", "            border: 1px solid #ddd;\n", "            border-radius: 5px;\n", "            background-color: #f9f9f9;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "    end = \"\"\"\n", "    </body>\n", "</html>\"\"\"\n", "\n", "    return \"\\n\".join([start, html_content, end])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def download_turing_samples(workers=8, max_results=1000):\n", "    storage_client = Client()\n", "    bucket = storage_client.bucket(TURING_BUCKET_NAME)\n", "\n", "    # List all blobs in the bucket\n", "    blobs = list(bucket.list_blobs(prefix=TURING_PREFIX, max_results=max_results))\n", "\n", "    # Filter out blobs that already exist locally\n", "    blobs_to_download = []\n", "    for blob in blobs:\n", "        local_path = os.path.join(TURING_PATH, blob.name)\n", "        if not os.path.exists(local_path):\n", "            blobs_to_download.append(blob)\n", "\n", "    if not blobs_to_download:\n", "        print(\"No New Data\")\n", "        return\n", "\n", "    # Prepare the list of blob names to download\n", "    blob_names = [blob.name for blob in blobs_to_download if blob.name.endswith(\".zip\")]\n", "\n", "    # Download the files\n", "    results = transfer_manager.download_many_to_path(\n", "        bucket, blob_names, destination_directory=TURING_PATH, max_workers=workers\n", "    )\n", "\n", "    # Process the results and unzip files\n", "    for name, result in zip(blob_names, results):\n", "        if isinstance(result, Exception):\n", "            print(f\"Failed to download {name} due to exception: {result}\")\n", "        else:\n", "            local_path = os.path.join(TURING_PATH, name)\n", "            print(f\"Downloaded {name} to {local_path}\")\n", "\n", "            try:\n", "                with zipfile.ZipFile(local_path, \"r\") as zip_ref:\n", "                    for zip_info in zip_ref.infolist():\n", "                        if zip_info.filename[-1] == \"/\":\n", "                            continue  # skip directories\n", "                        zip_info.filename = os.path.basename(\n", "                            zip_info.filename\n", "                        )  # remove directory structure\n", "                        zip_ref.extract(zip_info, TURING_PATH)\n", "                print(f\"Unzipped {name} to {TURING_PATH}\")\n", "            except Exception as e:\n", "                print(f\"Failed to unzip {name}: {str(e)}\")\n", "\n", "\n", "def get_turing_samples():\n", "    download_turing_samples()\n", "\n", "    request_ids = []\n", "    for file_path in Path(TURING_PATH).glob(\"*.json\"):\n", "        if file_path.name.startswith(\".\") or \"sample\" in file_path.name:\n", "            continue\n", "        with file_path.open() as f:\n", "            try:\n", "                sample = json.load(f)\n", "            except Exception as e:\n", "                print(f\"Failed to load {file_path}: {str(e)}\")\n", "                continue\n", "        if \"request_id\" in sample:\n", "            request_ids.append(sample[\"request_id\"])\n", "\n", "    all_samples = get_preferences_samples()\n", "    result = list(\n", "        filter(\n", "            lambda sample: sample[\"preference_request_id\"] in request_ids,\n", "            all_samples,\n", "        )\n", "    )\n", "    print(\n", "        f\"Total samples in BQ: {len(all_samples)}. Number of requests in Turing: {len(result)}. Final number of samples: {len(result)}\"\n", "    )\n", "    return result\n", "\n", "\n", "def get_turing_samples_local():\n", "    samples = []\n", "\n", "    download_turing_samples()\n", "    request_ids = []\n", "    for file_path in tqdm(Path(TURING_PATH).glob(\"*.json\")):\n", "        if file_path.name.startswith(\".\") or \"sample\" in file_path.name:\n", "            continue\n", "        with file_path.open() as f:\n", "            try:\n", "                sample = json.load(f)\n", "            except Exception as e:\n", "                print(f\"Failed to load {file_path}: {str(e)}\")\n", "                continue\n", "            if \"request_a\" not in sample:\n", "                print(f\"No request found in sample\")\n", "                continue\n", "            sample[\"preference_request_id\"] = sample[\"request_id\"]\n", "            sample[\"option_a\"] = sample[\"request_a\"]\n", "            sample[\"option_b\"] = sample[\"request_b\"]\n", "\n", "            for e in sample[\"request_a\"][\"request\"][\"chat_history\"]:\n", "                e[\"request_message\"] = e[\"message\"]\n", "                e[\"response_text\"] = e[\"response\"]\n", "\n", "            for e in sample[\"request_b\"][\"request\"][\"chat_history\"]:\n", "                e[\"request_message\"] = e[\"message\"]\n", "                e[\"response_text\"] = e[\"response\"]\n", "\n", "            for k, v in sample[\"scores\"].items():\n", "                sample[\"scores\"][k] = int(v)\n", "        samples.append(sample)\n", "        request_ids.append(sample[\"option_a\"][\"request_id\"])\n", "    chat_dict = get_chat_samples(request_ids)\n", "    samples_in_time_range = []\n", "\n", "    for sample in samples:\n", "        pulled_chat_sample = get_chat_sample(\n", "            sample[\"option_a\"][\"request_id\"], chat_dict\n", "        )\n", "        if pulled_chat_sample:\n", "            sample[\"user_agent\"] = pulled_chat_sample[\"metadata\"][\"user_agent\"]\n", "            sample['request_token_ids'] = pulled_chat_sample['request_token_ids']\n", "            sample['option_a']['request_token_ids'] = pulled_chat_sample['request_token_ids']\n", "            sample['option_b']['request_token_ids'] = pulled_chat_sample['request_token_ids']\n", "\n", "            sample[\"option_a\"][\"datetime\"] = pulled_chat_sample[\"datetime\"]\n", "            samples_in_time_range.append(sample)\n", "    return samples_in_time_range\n", "\n", "\n", "def get_data_df(samples):\n", "    all_data = []\n", "    for sample in samples:\n", "        cur_scores = deepcopy(sample[\"scores\"])\n", "\n", "        cur_scores[\"user_id\"] = sample[\"option_a\"][\"user_id\"]\n", "\n", "        all_data.append(cur_scores)\n", "    df = pd.DataFrame(all_data)\n", "    return df\n", "\n", "\n", "def request_id_to_link(request_id):\n", "    return f\"https://support.{TENANT_NAME}.t.us-central1.prod.augmentcode.com/t/{TENANT_NAME}/request/{request_id}\""]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = get_turing_samples()\n", "if TENANT_NAME == \"aitutor-turing\":\n", "    samples = get_turing_samples_local()\n", "elif <PERSON>_NAME == \"aitutor-mercor\":\n", "    samples = get_preferences_samples()\n", "else:\n", "    raise Exception(\"Invalid tenant name\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Only if using BQ version\n", "if TENANT_NAME == \"aitutor-mercor\":\n", "    chat_dict = get_chat_samples(\n", "        list(\n", "            set(\n", "                list(map(lambda x: x[\"request_ids\"][0], samples))\n", "                + list(map(lambda x: x[\"request_ids\"][1], samples))\n", "            )\n", "        )\n", "    )\n", "    samples_with_chats = []\n", "    for sample in tqdm(samples):\n", "        option_a = get_chat_sample(sample[\"request_ids\"][0], chat_dict)\n", "        option_b = get_chat_sample(sample[\"request_ids\"][1], chat_dict)\n", "        if option_a is None:\n", "            print(\n", "                f\"Failed to find chat sample for option_a: {sample['request_ids'][0]}\"\n", "            )\n", "            continue\n", "        if option_b is None:\n", "            print(\n", "                f\"Failed to find chat sample for option_b: {sample['request_ids'][1]}\"\n", "            )\n", "            continue\n", "\n", "        sample[\"option_a\"] = option_a\n", "        sample[\"option_b\"] = option_b\n", "        samples_with_chats.append(sample)\n", "\n", "    for sample in samples_with_chats:\n", "        # To make local and BQ version the same\n", "        sample[\"option_a\"][\"user_id\"] = sample[\"option_a\"][\"metadata\"][\"user_id\"]\n", "        sample[\"option_b\"][\"user_id\"] = sample[\"option_b\"][\"metadata\"][\"user_id\"]\n", "\n", "        sample[\"user_agent\"] = sample[\"option_a\"][\"metadata\"][\"user_agent\"]\n", "\n", "        sample[\"option_a\"][\"request_id\"] = sample[\"request_ids\"][0]\n", "        sample[\"option_b\"][\"request_id\"] = sample[\"request_ids\"][1]\n", "\n", "    samples = samples_with_chats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["analysis_df = get_data_df(samples)\n", "samples_per_user = Counter(analysis_df.user_id)\n", "\n", "for name, num in samples_per_user.most_common(100):\n", "    print(f\"{name}: {num}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# label_names = sorted(samples[0][\"scores\"].keys())\n", "label_names = list(\n", "    [\"formattingRating\", \"instructionFollowingRating\", \"isHighQuality\", \"overallRating\"]\n", ")\n", "user_names = list(map(lambda x: x[0], samples_per_user.most_common(100)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_text(text):\n", "    # Remove any leading/trailing whitespace, newlines, and colons\n", "    text = text.strip().strip(\":\").strip()\n", "    # Replace multiple spaces with a single space\n", "    text = re.sub(r\"\\s+\", \" \", text)\n", "    return text\n", "\n", "def extract_model_name(text):\n", "    model_pattern = r\"MODEL_IDS_START_LABEL\\s*(.+?)\\s*MODEL_IDS_END_LABEL\"\n", "    model_match = re.search(model_pattern, text, re.DOTALL)\n", "    if model_match:\n", "        models = model_match.group(1).strip().split('\\n')\n", "        if len(models) == 2:\n", "            return models[0], models[1]\n", "        return models[0], \"Unknown\"\n", "    return \"Unknown\", \"Unknown\"\n", "\n", "\n", "def extract_categories(text):\n", "    category_pattern = r\"\\[Category\\]\\s*(.+?)(?=\\[|$)\"\n", "    subcategory_pattern = r\"\\[Sub Category\\]\\s*(.+?)(?=\\[|$)\"\n", "    type_pattern = r\"\\[Type\\]\\s*(.+?)(?=\\[|$)\"\n", "\n", "    category_match = re.search(category_pattern, text, re.DOTALL)\n", "    subcategory_match = re.search(subcategory_pattern, text, re.DOTALL)\n", "    type_match = re.search(type_pattern, text, re.DOTALL)\n", "\n", "    category = clean_text(category_match.group(1)) if category_match else \"Unknown\"\n", "    subcategory = (\n", "        clean_text(subcategory_match.group(1))\n", "        if subcategory_match\n", "        else \"No Subcategory\"\n", "    )\n", "    type_ = clean_text(type_match.group(1)) if type_match else \"No Type\"\n", "\n", "    return category, subcategory, type_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["extract_model_name(samples[-1][\"feedback\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict, Counter\n", "\n", "\n", "custom_metrics = defaultdict(dict)\n", "\n", "for user_name in user_names:\n", "    print(f\"{user_name}\")\n", "    cur_samples = [\n", "        sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_name\n", "    ]\n", "    print(\n", "        \"\\n\".join(\n", "            map(\n", "                str,\n", "                Counter(list(map(lambda x: x[\"user_agent\"], cur_samples))).most_common(\n", "                    100\n", "                ),\n", "            )\n", "        )\n", "    )\n", "    print(\"#\" * 20)\n", "\n", "    num_w_selected_code = len(\n", "        list(\n", "            filter(\n", "                lambda x: len(x[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0,\n", "                cur_samples,\n", "            )\n", "        )\n", "    )\n", "    num_wo_selected_code = len(cur_samples) - num_w_selected_code\n", "\n", "    custom_metrics[user_name][\"num_w_selected_code\"] = num_w_selected_code\n", "    custom_metrics[user_name][\"num_wo_selected_code\"] = num_wo_selected_code\n", "\n", "    for sample in cur_samples:\n", "        category, sub_category, type_ = extract_categories(sample.get(\"feedback\", \"\"))\n", "        model_a, model_b = extract_model_name(sample.get(\"feedback\", \"\"))\n", "        sub_category = f\"{category}_{sub_category}\"\n", "        type_ = f\"{sub_category}_{type_}\"\n", "        sample['option_a']['model_name'] = model_a\n", "        sample['option_b']['model_name'] = model_b\n", "        sample['winning_model_name'] = model_a if sample['scores']['overallRating'] < 0 else sample['option_b']['model_name'] if sample['scores']['overallRating'] > 0 else \"Tie\"\n", "\n", "        custom_metrics[user_name].setdefault(\"categories\", defaultdict(int))[\n", "            category\n", "        ] += 1\n", "        custom_metrics[user_name].setdefault(\"subcategories\", defaultdict(int))[\n", "            sub_category\n", "        ] += 1\n", "        custom_metrics[user_name].setdefault(\"types\", defaultdict(int))[type_] += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for user_name in user_names:\n", "    print(\n", "        f\"{user_name}: {custom_metrics[user_name]['num_w_selected_code'] / (custom_metrics[user_name]['num_w_selected_code'] + custom_metrics[user_name]['num_wo_selected_code']):.2f}   {custom_metrics[user_name]['categories']}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, axes = plt.subplots(\n", "    nrows=len(user_names) + 1,\n", "    ncols=len(label_names),\n", "    figsize=(8 * len(label_names), 8 * len(user_names)),\n", ")\n", "\n", "for i, user_name in enumerate(user_names):\n", "    for j, label_name in enumerate(label_names):\n", "        sns.countplot(\n", "            x=label_name,\n", "            data=analysis_df[analysis_df[\"user_id\"] == user_name],\n", "            ax=axes[i][j],\n", "        )  # , stat='probability')\n", "        axes[i, j].set_title(\n", "            f\"{user_name} ({samples_per_user[user_name]}) - {label_name}\"\n", "        )\n", "        axes[i, j].set_xlabel(\"\")\n", "        axes[i, j].set_ylabel(\"\")\n", "\n", "for j, label_name in enumerate(label_names):\n", "    sns.countplot(\n", "        x=label_name, data=analysis_df, ax=axes[len(user_names)][j]\n", "    )  # , stat='probability')\n", "    axes[len(user_names), j].set_title(f\"Overall - {label_name}\")\n", "    axes[len(user_names), j].set_xlabel(\"\")\n", "    axes[len(user_names), j].set_ylabel(\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def calculate_separate_stats(df):\n", "    grouped = df.groupby(['model_a', 'model_b'])\n", "    separate_stats = grouped.agg({\n", "        'overallRating': ['mean', lambda x: (x == 0).sum(), 'count']\n", "    }).reset_index()\n", "    \n", "    separate_stats.columns = ['model_a', 'model_b', 'mean_rating', 'ties', 'total_comparisons']\n", "    \n", "    separate_stats['model_a_wins'] = grouped['overallRating'].apply(lambda x: (x < 0).sum()).values\n", "    separate_stats['model_b_wins'] = grouped['overallRating'].apply(lambda x: (x > 0).sum()).values\n", "    \n", "    separate_stats['model_a_win_pct'] = separate_stats['model_a_wins'] / separate_stats['total_comparisons']\n", "    separate_stats['model_b_win_pct'] = separate_stats['model_b_wins'] / separate_stats['total_comparisons']\n", "    separate_stats['tie_pct'] = separate_stats['ties'] / separate_stats['total_comparisons']\n", "    \n", "    separate_stats['overall_winner'] = np.select(\n", "        [separate_stats['model_a_win_pct'] > separate_stats['model_b_win_pct'],\n", "         separate_stats['model_b_win_pct'] > separate_stats['model_a_win_pct']],\n", "        [separate_stats['model_a'], separate_stats['model_b']],\n", "        default='Tie'\n", "    )\n", "    \n", "    return separate_stats.sort_values('total_comparisons', ascending=False)\n", "\n", "def calculate_combined_stats(df):\n", "    df = df.copy()  # Create a copy to avoid SettingWithCopyWarning\n", "    df['model_pair'] = df.apply(lambda row: tuple(sorted([row['model_a'], row['model_b']])), axis=1)\n", "    grouped = df.groupby('model_pair')\n", "    \n", "    combined_stats = grouped.size().reset_index(name='total_comparisons')\n", "    combined_stats['ties'] = grouped['overallRating'].apply(lambda x: (x == 0).sum()).values\n", "    \n", "    combined_stats['winning_model'] = combined_stats.apply(lambda row: \n", "        df[df['model_pair'] == row['model_pair']]['winning_model'].mode().iloc[0], axis=1)\n", "    \n", "    combined_stats['win_rate'] = combined_stats.apply(lambda row: \n", "        (df[df['model_pair'] == row['model_pair']]['winning_model'] == row['winning_model']).sum() / row['total_comparisons'], axis=1)\n", "    combined_stats['tie_rate'] = combined_stats['ties'] / combined_stats['total_comparisons']\n", "    combined_stats['loss_rate'] = 1 - combined_stats['win_rate'] - combined_stats['tie_rate']\n", "\n", "    return combined_stats.sort_values('total_comparisons', ascending=False)\n", "\n", "def style_dataframe(df):\n", "    return df.style.set_properties(**{\n", "        'border-collapse': 'collapse',\n", "        'border': '1px solid #ddd',\n", "        'padding': '8px',\n", "        'text-align': 'left'\n", "    }).set_table_styles([{\n", "        'selector': 'th',\n", "        'props': [('background-color', '#f2f2f2'), ('font-weight', 'bold')]\n", "    }]).format(precision=3)\n", "\n", "def generate_model_comparison_tables(df, user_id=None):\n", "    if user_id:\n", "        df = df[df['user_id'] == user_id].copy()  # Create a copy to avoid SettingWithCopyWarning\n", "    \n", "    if df.empty:\n", "        return \"<p>No data available for the selected criteria.</p>\"\n", "\n", "    separate_stats = calculate_separate_stats(df)\n", "    combined_stats = calculate_combined_stats(df)\n", "    crosstab = pd.crosstab(df['model_a'], df['model_b'], values=df['overallRating'], aggfunc='mean')\n", "\n", "    # Apply styling to DataFrames\n", "    separate_styled = style_dataframe(separate_stats)\n", "    combined_styled = style_dataframe(combined_stats)\n", "    crosstab_styled = style_dataframe(crosstab)\n", "\n", "    # Convert styled DataFrames to HTML tables\n", "    separate_html = separate_styled.to_html(index=False)\n", "    combined_html = combined_styled.to_html(index=False)\n", "    crosstab_html = crosstab_styled.to_html()\n", "\n", "    css = \"\"\"\n", "    <style>\n", "        .comparison-tables {\n", "            font-family: <PERSON><PERSON>, sans-serif;\n", "            max-width: 1200px;\n", "            background-color: #f9f9f9;\n", "            border-radius: 8px;\n", "            box-shadow: 0 0 10px rgba(0,0,0,0.1);\n", "        }\n", "        .comparison-tables h3 {\n", "            color: #333;\n", "            border-bottom: 2px solid #ddd;\n", "            padding-bottom: 10px;\n", "            margin-bottom: 20px;\n", "        }\n", "        .comparison-tables h4 {\n", "            color: #444;\n", "            margin-top: 30px;\n", "            margin-bottom: 10px;\n", "        }\n", "        .comparison-tables table {\n", "            width: 100%;\n", "            margin-bottom: 30px;\n", "        }\n", "        .comparison-tables th, .comparison-tables td {\n", "            padding: 12px;\n", "            border: 1px solid #ddd;\n", "        }\n", "        .comparison-tables th {\n", "            background-color: #f2f2f2;\n", "            font-weight: bold;\n", "            text-align: left;\n", "        }\n", "        .comparison-tables tr:nth-child(even) {\n", "            background-color: #f8f8f8;\n", "        }\n", "        .comparison-tables tr:hover {\n", "            background-color: #e8e8e8;\n", "        }\n", "    </style>\n", "    \"\"\"\n", "\n", "    return f\"\"\"\n", "    {css}\n", "    <div class=\"comparison-tables\">\n", "        <h3>{'User-specific' if user_id else 'Overall'} Model Comparison Tables</h3>\n", "        <h4>Separate A/B Comparisons:</h4>\n", "        {separate_html}\n", "        <h4>Combined Comparisons:</h4>\n", "        {combined_html}\n", "        <h4>Cross tab Comparisons (-1: A, 0: <PERSON><PERSON>, 1: B):</h4>\n", "        {crosstab_html}\n", "    </div>\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_category_tables(categories, subcategories):\n", "    def create_table(data, title):\n", "        sorted_data = sorted(data.items(), key=lambda x: x[1], reverse=True)\n", "        table_html = f\"<h3>{title}</h3>\"\n", "        table_html += (\n", "            \"<table border='1' style='border-collapse: collapse; width: 100%;'>\"\n", "        )\n", "        table_html += \"<tr><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>{}</th><th style='padding: 8px; text-align: left; background-color: #f2f2f2;'>Count</th></tr>\".format(\n", "            title\n", "        )\n", "        for category, count in sorted_data:\n", "            table_html += f\"<tr><td style='padding: 8px;'>{category}</td><td style='padding: 8px;'>{count}</td></tr>\"\n", "        table_html += \"</table>\"\n", "        return table_html\n", "\n", "    categories_table = create_table(categories, \"Categories\")\n", "    subcategories_table = create_table(subcategories, \"Sub Categories\")\n", "\n", "    return f\"<div style='display: flex; justify-content: space-between;'><div style='width: 48%;'>{categories_table}</div><div style='width: 48%;'>{subcategories_table}</div></div>\"\n", "\n", "\n", "def plot_to_html(plt):\n", "    img = io.BytesIO()\n", "    plt.savefig(img, format='png', bbox_inches='tight')\n", "    img.seek(0)\n", "    plt.close()  # Close the plot to free up memory\n", "    return base64.b64encode(img.getvalue()).decode()\n", "\n", "def create_score_distribution_plot(data, title):\n", "    plt.figure(figsize=(10, 6))\n", "    for model, scores in data.items():\n", "        plt.hist(scores, bins=7, range=(0, 3), alpha=0.5, label=model)\n", "    plt.title(title)\n", "    plt.xlabel('Score')\n", "    plt.ylabel('Frequency')\n", "    plt.legend()\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    return plot_to_html(plt)\n", "\n", "def generate_score_distribution_plots(samples, user_id=None):\n", "    model_scores = defaultdict(list)\n", "    for sample in samples:\n", "        if sample['option_a']['model_name'] == sample['option_b']['model_name']:\n", "            continue\n", "        if sample['winning_model_name'] in [\"undefined\", \"Unknown\"]:\n", "            continue\n", "        score = abs(sample['scores']['overallRating'])\n", "        model_scores[sample['winning_model_name']].append(score)\n", "\n", "    title = f\"Score Distribution by Model for {user_id}\" if user_id else \"Overall Score Distribution by Model\"\n", "    return create_score_distribution_plot(model_scores, title)\n", "\n", "def remove_model_names(feedback):\n", "    pattern = r\"MODEL_IDS_START_LABEL\\s*(.+?)\\s*MODEL_IDS_END_LABEL\"\n", "    return re.sub(pattern, \"\", feedback, flags=re.DOTALL).strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slow_samples = []\n", "\n", "# Add timeout context manager at the top\n", "@contextmanager\n", "def timeout(seconds):\n", "    def signal_handler(signum, frame):\n", "        raise TimeoutError(f\"Timed out after {seconds} seconds\")\n", "    \n", "    signal.signal(signal.SIGALRM, signal_handler)\n", "    signal.alarm(seconds)\n", "    try:\n", "        yield\n", "    finally:\n", "        signal.alarm(0)\n", "\n", "def generate_report(internal : bool):\n", "        multisample_html = \"\"\n", "        if internal:\n", "            # Create DataFrame\n", "            df = pd.DataFrame(\n", "                [\n", "                    {'overallRating': sample[\"scores\"][\"overallRating\"], \n", "                    \"user_id\": sample[\"option_a\"][\"user_id\"],\n", "                    \"model_a\": sample[\"option_a\"][\"model_name\"],\n", "                    \"model_b\": sample[\"option_b\"][\"model_name\"],\n", "                    \"winning_model\": sample[\"winning_model_name\"]}\n", "                    for sample in samples\n", "                    if sample[\"option_a\"][\"request_token_ids\"] == sample[\"option_b\"][\"request_token_ids\"] and sample[\"option_a\"][\"model_name\"] not in [\"undefined\", \"Undefined\", \"Unknown\"]\n", "                ]\n", "            )\n", "            model_name_mapping = {\n", "                'binks-l3-70B-FP8-ug-chatanol1-16-3-chat': 'Chatanol-16-3',\n", "                'binks-l3-w-refinement-v1': 'Refinement-v1',\n", "            }\n", "            # Apply the mapping to the relevant columns\n", "            columns_to_shorten = ['model_a', 'model_b', 'winning_model']\n", "            for col in columns_to_shorten:\n", "                df[col] = df[col].replace(model_name_mapping)\n", "\n", "\n", "\n", "        for user_id in user_names:\n", "            print(f\"Starting processing for user {user_id}\")\n", "            cur_samples = [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_id]\n", "            print(f\"Found {len(cur_samples)} samples for user {user_id}\")\n", "            cur_samples = random.sample(cur_samples, min(250, len(cur_samples)))\n", "            cur_samples = list(reversed(sorted(cur_samples, key=lambda x: x[\"option_a\"][\"datetime\"])))\n", "            \n", "            cur_user_html = \"\"\n", "            if internal:\n", "                user_plot_html = generate_score_distribution_plots(cur_samples, user_id)\n", "                cur_user_html += f\"<h2>Score Distribution for {user_id}</h2><img src='data:image/png;base64,{user_plot_html}'>\"\n", "                cur_user_html += generate_model_comparison_tables(df, user_id)\n", "                cur_user_html += generate_category_tables(\n", "                    custom_metrics[user_id][\"categories\"],\n", "                    custom_metrics[user_id][\"subcategories\"],\n", "                )\n", "            print(f\"Rendering {len(cur_samples)} samples\")\n", "            for i, sample in tqdm(enumerate(cur_samples)):\n", "                start_time = time.time()\n", "                try:\n", "                    \n", "                    # Handles previous samples that don't have ratings\n", "                    if \"formattingRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"formattingRating\"] = -100\n", "\n", "                    if \"instructionFollowingRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"instructionFollowingRating\"] = -100\n", "\n", "                    if \"hallucinationRating\" not in sample[\"scores\"]:\n", "                        sample[\"scores\"][\"hallucinationRating\"] = -100\n", "                    \n", "                    html_time = time.time()\n", "                    if not internal:\n", "                        sample[\"feedback\"] = remove_model_names(sample[\"feedback\"])\n", "                    \n", "                    cur_user_html += f\"<h1>Chain {i}</h1>\"\n", "                    cur_user_html += f\"<div>User ID: {user_id}</div>\"\n", "                    cur_user_html += f\"<div>Time: {sample['option_a']['datetime'].strftime('%m-%d-%Y %H:%M:%S')}</div>\"\n", "                    cur_user_html += f'<div>Request ID (Preference): {sample[\"preference_request_id\"]}</div>'\n", "                    cur_user_html += f\"<div>____________________________________________________</div>\"\n", "                    cur_user_html += f'<div>Request ID (A): <a href=\"{request_id_to_link(sample[\"option_a\"][\"request_id\"])}\">{sample[\"option_a\"][\"request_id\"]}</a></div>'\n", "                    cur_user_html += f'<div>Request ID (B): <a href=\"{request_id_to_link(sample[\"option_b\"][\"request_id\"])}\">{sample[\"option_b\"][\"request_id\"]}</a></div>'\n", "                    \n", "                    # Render sample history\n", "                    render_time = time.time()\n", "                    with timeout(1):\n", "                        history_html = render_sample_with_history(sample)\n", "                \n", "                    cur_user_html += history_html\n", "                    cur_user_html += \"<hr>\"\n", "                    \n", "                    \n", "                except TimeoutError:\n", "                    print(f\"TIMEOUT: Sample {sample['preference_request_id']} took too long to render\")\n", "                    history_html = \"<div class='error'>Rendering timed out after 5 seconds</div>\"\n", "\n", "            multisample_html += f\"<details><summary>User ID: {user_id} -- {len(cur_samples)} samples</summary>{cur_user_html}</details>\"\n", "        print(\"Done generating reports\")\n", "        total_categories = Counter()\n", "        total_subcategories = Counter()\n", "        total_samples = 0\n", "\n", "        for user_id in user_names:\n", "            total_categories.update(custom_metrics[user_id][\"categories\"])\n", "            total_subcategories.update(custom_metrics[user_id][\"subcategories\"])\n", "            total_samples += len(\n", "                [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_id]\n", "            )\n", "\n", "        # Create summary HTML\n", "        summary_html = \"<h2>Overall Summary</h2>\"\n", "        summary_html += f\"<div>Total Users: {len(user_names)}</div>\"\n", "        summary_html += f\"<div>Total Samples: {total_samples}</div>\"\n", "        summary_html += generate_category_tables(total_categories, total_subcategories)\n", "        if internal:\n", "            # Generate overall plot\n", "            overall_plot_html = generate_score_distribution_plots(samples)\n", "            summary_html += f\"<h2>Overall Score Distribution</h2><img src='data:image/png;base64,{overall_plot_html}'>\"\n", "\n", "            # Add overall model comparison tables\n", "            summary_html += generate_model_comparison_tables(df)\n", "        # Add the summary to the main HTML\n", "        multisample_html += summary_html\n", "        whole_html = wrap_html(multisample_html)\n", "\n", "        with open(\n", "            f\"/mnt/efs/augment/user/tamuz/annotations/{TENANT_NAME}/{'internal_' if internal else ''}{TENANT_NAME}-review-{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html\",\n", "            \"w\",\n", "        ) as f:\n", "            f.write(whole_html)\n", "        return f\"/mnt/efs/augment/user/tamuz/annotations/{TENANT_NAME}/{'internal_' if internal else ''}{TENANT_NAME}-review-{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html\"\n", "\n", "# samples = [sample for sample in samples if sample[\"option_a\"][\"model_name\"] != sample[\"option_b\"][\"model_name\"] and sample[\"option_a\"][\"model_name\"] not in [\"undefined\", \"Undefined\", \"Unknown\"] and abs(sample['scores']['overallRating'] > 1)]\n", "\n", "# generate_report(internal=True)\n", "\n", "# generate_report(internal=True)\n", "generate_report(internal=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if slow_samples:\n", "    df_slow = pd.DataFrame(slow_samples)\n", "    print(\"\\nSlow samples analysis:\")\n", "    print(f\"Total slow samples: {len(slow_samples)}\")\n", "    print(f\"Timed out samples: {len(df_slow[df_slow.get('timeout', False)])}\")\n", "    print(\"\\nSlowest samples:\")\n", "    print(df_slow.sort_values('time', ascending=False).head())\n", "    \n", "    # Analyze correlation with chat history length\n", "    if 'chat_history_length' in df_slow.columns:\n", "        print(\"\\nCorrelation between render time and chat history length:\", \n", "              df_slow['time'].corr(df_slow['chat_history_length']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_id for slow_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(cur_samples)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lightweight export for Mercor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not DRY_RUN and TENANT_NAME == \"aitutor-mercor\":\n", "    exported_samples = []\n", "\n", "    for sample in samples:\n", "        exported_sample = {\n", "            \"preference_request_id\": sample[\"preference_request_id\"],\n", "            \"chat_history\": sample[\"option_a\"][\"request\"].get(\"chat_history\", []),\n", "            \"message\": sample[\"option_a\"][\"request\"][\"message\"],\n", "            \"path\": sample[\"option_a\"][\"request\"].get(\"path\", \"\"),\n", "            \"prefix\": sample[\"option_a\"][\"request\"].get(\"prefix\", \"\"),\n", "            \"selected_code\": sample[\"option_a\"][\"request\"].get(\"selected_code\", \"\"),\n", "            \"suffix\": sample[\"option_a\"][\"request\"].get(\"suffix\", \"\"),\n", "            \"option_a\": sample[\"option_a\"][\"response\"],\n", "            \"option_b\": sample[\"option_b\"][\"response\"],\n", "            \"scores\": sample[\"scores\"],\n", "            \"feedback\": sample.get(\"feedback\", \"\"),\n", "            \"metadata\": sample[\"option_a\"][\"metadata\"],\n", "            \"datetime\": sample[\"option_a\"][\"datetime\"].isoformat(),\n", "        }\n", "\n", "        exported_samples.append(exported_sample)\n", "\n", "    with open(\n", "        f\"/mnt/efs/augment/user/tamuz/annotations/{TENANT_NAME}/{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}-dump-w-timestamps.jsonl\",\n", "        \"w\",\n", "    ) as f:\n", "        for sample in exported_samples:\n", "            f.write(json.dumps(sample))\n", "            f.write(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}
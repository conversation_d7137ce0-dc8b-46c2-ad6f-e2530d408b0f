# AI Tutor Data Retrieval

This module provides utilities to retrieve AI tutor data from BigQuery and Google Cloud Storage (GCS). It uses the newer search and analytics datasets instead of the deprecated full export dataset.

## Features

- Retrieve AI tutor requests by user ID
- Retrieve AI tutor requests by request ID
- Retrieve AI tutor requests by time range
- Get analytics data for specific requests
- Get events from GCS for specific requests

## Usage

### Basic Usage

```python
from aitutor_data_retrieval import AITutorDataRetriever

# Initialize the retriever with a tenant name
retriever = AITutorDataRetriever(tenant_name="aitutor-turing")

# Get requests for a specific user
requests = retriever.get_requests_by_user(
    user_id="<EMAIL>",
    limit=10
)

# Get a specific request with events
request = retriever.get_request_by_id("request_id_123")

# Get requests within a time range
from datetime import datetime, timedelta
start_time = datetime.utcnow() - timedelta(days=7)
requests = retriever.get_requests_by_time_range(
    start_time=start_time,
    request_type="CHAT",
    limit=20
)

# Get analytics data for specific requests
request_ids = [request.request_id for request in requests]
analytics_data = retriever.get_analytics_data(
    request_ids=request_ids,
    table_name="chat_host_response"
)

# Get events for specific requests
events_by_request = retriever.get_events_for_requests(
    request_ids=request_ids,
    event_names={"request_received", "response_sent"}
)
```

### Using the Test Script

The `test_aitutor_data_retrieval.py` script provides a command-line interface to test the data retrieval functionality.

```bash
# Get requests for a specific user
./test_aitutor_data_retrieval.py --tenant aitutor-<NAME_EMAIL> --days 7 --limit 10

# Get a specific request with events
./test_aitutor_data_retrieval.py --tenant aitutor-turing request request_id_123 --events

# Get requests within a time range
./test_aitutor_data_retrieval.py --tenant aitutor-turing time --days 1 --request-type CHAT --limit 20

# Save results to a file
./test_aitutor_data_retrieval.py --tenant aitutor-<NAME_EMAIL> --output results.json
```

## Available Tenants

The module uses the tenant configurations defined in `base.datasets.tenants`. The available AI tutor tenants include:

- `aitutor-turing`
- `aitutor-mercor`

## Data Structure

The `AITutorRequest` class represents an AI tutor request with the following fields:

- `request_id`: The ID of the request
- `tenant_name`: The name of the tenant
- `user_id`: The ID of the user who made the request
- `time`: The timestamp of the request
- `request_type`: The type of the request (e.g., CHAT, EDIT)
- `request_content`: The content of the request
- `response_content`: The content of the response (if available)
- `events`: The events associated with the request (if available)

## Requirements

- Google Cloud BigQuery client
- Google Cloud Storage client
- Access to the appropriate GCP projects and datasets

## Notes

- The module uses the search dataset (`search_dataset_name`) for retrieving request metadata
- The module uses the analytics dataset (`analytics_dataset_name`) for retrieving response data
- The module uses the events bucket (`events_bucket_name`) for retrieving events
- The deprecated full export dataset (`dataset_name`) is not used

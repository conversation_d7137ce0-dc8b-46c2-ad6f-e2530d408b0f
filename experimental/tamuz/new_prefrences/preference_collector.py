#!/usr/bin/env python3

"""
Script to collect preference events from multiple tenants.
Uses improved methods for tenant filtering and data retrieval.
"""

import argparse
import json
import logging
import os
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from aitutor_data_retrieval import AITutorDataRetriever
from google.protobuf.json_format import MessageToDict

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def get_preference_events(
    retriever: AITutorDataRetriever, days: int = 14, limit: int = 1000
) -> List[Dict[str, Any]]:
    """Get preference events from the last N days using improved methods.

    Args:
        retriever: The AITutorDataRetriever instance.
        days: Number of days to look back.
        limit: Maximum number of events to return.

    Returns:
        A list of preference events.
    """

    logger.info(f"Retrieving preference events from the last {days} days...")

    # Use a larger limit for the query to ensure we get enough events
    query_limit = limit * 2

    # Execute a direct BigQuery query to get preference events
    # Make sure to filter by tenant to avoid cross-tenant data issues
    query = f"""
    SELECT
        re.request_id,
        re.tenant,
        re.tenant_id,
        re.event_type,
        re.event_id,
        re.time
    FROM `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_event` re
    WHERE
        re.event_type = 'preference_sample'
        AND re.tenant = '{retriever.tenant_name}'
        AND re.time BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {days} DAY) AND CURRENT_TIMESTAMP()
    ORDER BY re.time DESC
    LIMIT {query_limit}
    """

    # Execute the query
    logger.info("Executing direct BigQuery query for preference events")
    query_job = retriever.bq_client.query(query)
    rows = list(query_job.result())

    # Convert rows to dictionaries
    preference_events = [dict(row.items()) for row in rows]

    logger.info(f"Found {len(preference_events)} preference events")

    # Limit the results if needed
    if len(preference_events) > limit:
        preference_events = preference_events[:limit]
        logger.info(f"Limited to {len(preference_events)} preference events")

    return preference_events


def get_full_preference_event(
    preference_events: List[Dict[str, Any]], retriever: AITutorDataRetriever
) -> List[Dict[str, Any]]:
    """Extract full preference events with improved error handling and metadata.

    Args:
        preference_events: List of preference events.
        retriever: The AITutorDataRetriever instance.

    Returns:
        A list of full preference events.
    """
    if not preference_events:
        logger.info("No preference events to process")
        return []

    # Convert to list for the get_requests method
    request_ids_list = [event["request_id"] for event in preference_events]

    # Use the GCSRequestInsightFetcher directly for batch retrieval
    logger.info(f"Using parallel retrieval for {len(request_ids_list)} request IDs")

    # Filter for preference_sample events only
    request_event_names = frozenset(["preference_sample"])

    # Get requests in parallel with a reasonable batch size
    batch_size = min(32, (os.cpu_count() or 1) + 4)
    requests_iterator = retriever.gcs_fetcher.get_requests(
        request_ids=request_ids_list,
        request_event_names=request_event_names,
        batch_size=batch_size,
    )
    all_preference_events = []

    # Create a mapping of request_id to original event for faster lookups
    request_id_to_event = {event["request_id"]: event for event in preference_events}

    # Process the results
    for result in requests_iterator:
        if isinstance(result, Exception):
            logger.warning(f"Error retrieving request: {result}")
            continue

        # Process the events in this request
        for event in result.events:
            try:
                event_dict = MessageToDict(event)

                # Check if this is a preference_sample event
                if "preferenceSample" in event_dict:
                    # Add metadata from the original event
                    orig_event = request_id_to_event.get(result.request_id)
                    if orig_event:
                        event_dict["eventId"] = orig_event["event_id"]
                        event_dict["request_id"] = orig_event["request_id"]
                        event_dict["tenant"] = orig_event["tenant"]
                        event_dict["tenant_id"] = orig_event["tenant_id"]
                        event_dict["event_type"] = orig_event["event_type"]

                    all_preference_events.append(event_dict)

            except Exception as e:
                logger.warning(
                    f"Error processing event for request {result.request_id}: {e}"
                )

    logger.info(f"Found {len(all_preference_events)} preference events")

    return all_preference_events


def extract_model_names(feedback: str) -> Tuple[str, str]:
    """Extract model names from the feedback text.

    Args:
        feedback: The feedback text.

    Returns:
        A tuple of two model names (model A, model B).
    """
    model_pattern = r"MODEL_IDS_START_LABEL\s*(.+?)\s*MODEL_IDS_END_LABEL"
    model_match = re.search(model_pattern, feedback, re.DOTALL)
    if model_match:
        models = model_match.group(1).strip().split("\n")
        if len(models) == 2:
            return models[0], models[1]
        return models[0], "Unknown"
    return "Unknown", "Unknown"


def main():
    parser = argparse.ArgumentParser(
        description="Collect preference events from multiple tenants"
    )
    parser.add_argument(
        "--tenants",
        type=str,
        nargs="+",
        default=["aitutor-turing", "aitutor-mercor"],
        help="List of tenant names to process",
    )
    parser.add_argument(
        "--days", type=int, default=7, help="Number of days to look back"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=10000,
        help="Maximum number of events to return per tenant",
    )
    parser.add_argument("--output", type=str, help="Output file path (JSON)")
    parser.add_argument("--verbose", action="store_true", help="Print verbose output")
    args = parser.parse_args()

    tenant_names = args.tenants
    days = args.days
    limit = args.limit
    verbose = args.verbose

    # Set logging level based on verbosity
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    combined_preference_events = []
    tenant_stats = {}

    print(f"Starting preference event collection from {len(tenant_names)} tenants")
    print(f"Looking back {days} days with a limit of {limit} events per tenant")

    for tenant_name in tenant_names:
        tenant_stats[tenant_name] = {"found": 0, "loaded": 0, "errors": 0}

        try:
            print(f"\nProcessing tenant: {tenant_name}")
            retriever = AITutorDataRetriever(tenant_name=tenant_name)

            # Get preference events
            preference_events = get_preference_events(
                retriever=retriever, days=days, limit=limit
            )
            tenant_stats[tenant_name]["found"] = len(preference_events)
            print(f"Found {len(preference_events)} preference events")

            # Get full preference events
            full_events = get_full_preference_event(
                preference_events=preference_events, retriever=retriever
            )
            tenant_stats[tenant_name]["loaded"] = len(full_events)
            print(f"Loaded {len(full_events)} preference events")

            # Add model names
            model_counts = {}
            for event in full_events:
                if (
                    "preferenceSample" in event
                    and "feedback" in event["preferenceSample"]
                ):
                    try:
                        event["model_a"], event["model_b"] = extract_model_names(
                            event["preferenceSample"]["feedback"]
                        )

                        # Count model occurrences
                        for model in [event["model_a"], event["model_b"]]:
                            if model != "Unknown":
                                model_counts[model] = model_counts.get(model, 0) + 1
                    except Exception as e:
                        logger.warning(f"Error extracting model names: {e}")
                        tenant_stats[tenant_name]["errors"] += 1

            # Print model statistics
            if model_counts:
                print(f"Model distribution in {tenant_name}:")
                for model, count in sorted(
                    model_counts.items(), key=lambda x: x[1], reverse=True
                )[:5]:
                    print(f"  {model}: {count}")
                if len(model_counts) > 5:
                    print(f"  ... and {len(model_counts) - 5} more models")

            # Print a sample
            if full_events:
                print("\nSample event:")
                print(json.dumps(full_events[:1], indent=2))
            else:
                print("No events found")

            # Add to combined events
            combined_preference_events.extend(full_events)

        except Exception as e:
            print(f"Error processing tenant {tenant_name}: {e}")
            tenant_stats[tenant_name]["errors"] += 1
            logger.exception(f"Exception details for {tenant_name}")

    # Print summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    for tenant, stats in tenant_stats.items():
        print(
            f"{tenant}: Found {stats['found']}, Loaded {stats['loaded']}, Errors {stats['errors']}"
        )
    print(f"Total preference events collected: {len(combined_preference_events)}")

    # Save to file if output path is provided
    if args.output:
        try:
            with open(args.output, "w") as f:
                json.dump(combined_preference_events, f, indent=2)
            print(f"Saved {len(combined_preference_events)} events to {args.output}")
        except Exception as e:
            print(f"Error saving to {args.output}: {e}")
            logger.exception("Exception details for file saving")


if __name__ == "__main__":
    main()

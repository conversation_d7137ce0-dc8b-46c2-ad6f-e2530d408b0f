{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from copy import deepcopy\n", "\n", "import pandas as pd\n", "import argparse\n", "import json\n", "import logging\n", "import os\n", "import re\n", "from datetime import datetime, timedelta\n", "from typing import Any, Dict, List, Optional, Tuple\n", "\n", "from aitutor_data_retrieval import AITutorDataRetriever\n", "from google.protobuf.json_format import MessageToDict\n", "from experimental.tamuz.preferences.html_report_utils import (\n", "    style_dataframe,\n", "    generate_elo_table,\n", "    generate_elo_plot,\n", "    generate_category_tables,\n", "    remove_model_names,\n", "    generate_model_comparison_tables,\n", "    wrap_html,\n", ")\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO, format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\"\n", ")\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_preference_events(\n", "    retriever: AITutorDataRetriever, days: int = 14, limit: int = 1000\n", ") -> List[Dict[str, Any]]:\n", "    \"\"\"Get preference events from the last N days using improved methods.\n", "\n", "    Args:\n", "        retriever: The AITutorDataRetriever instance.\n", "        days: Number of days to look back.\n", "        limit: Maximum number of events to return.\n", "\n", "    Returns:\n", "        A list of preference events.\n", "    \"\"\"\n", "    logger.info(f\"Retrieving preference events from the last {days} days...\")\n", "\n", "    # Use a larger limit for the query to ensure we get enough events\n", "    query_limit = limit * 2\n", "\n", "    # Execute a direct BigQuery query to get preference events\n", "    # Make sure to filter by tenant to avoid cross-tenant data issues\n", "    query = f\"\"\"\n", "    SELECT \n", "        re.request_id,\n", "        re.tenant,\n", "        re.tenant_id,\n", "        re.event_type,\n", "        re.event_id,\n", "        re.time\n", "    FROM `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_event` re\n", "    WHERE \n", "        re.event_type = 'preference_sample'\n", "        AND re.tenant = '{retriever.tenant_name}'\n", "        AND re.time BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {days} DAY) AND CURRENT_TIMESTAMP()\n", "    ORDER BY re.time DESC\n", "    LIMIT {query_limit}\n", "    \"\"\"\n", "\n", "    # Execute the query\n", "    logger.info(\"Executing direct BigQuery query for preference events\")\n", "    query_job = retriever.bq_client.query(query)\n", "    rows = list(query_job.result())\n", "\n", "    # Convert rows to dictionaries\n", "    preference_events = [dict(row.items()) for row in rows]\n", "\n", "    logger.info(f\"Found {len(preference_events)} preference events\")\n", "\n", "    # Limit the results if needed\n", "    if len(preference_events) > limit:\n", "        preference_events = preference_events[:limit]\n", "        logger.info(f\"Limited to {len(preference_events)} preference events\")\n", "\n", "    return preference_events\n", "\n", "\n", "def get_full_preference_event(\n", "    preference_events: List[Dict[str, Any]], retriever: AITutorDataRetriever\n", ") -> List[Dict[str, Any]]:\n", "    \"\"\"Extract full preference events with improved error handling and metadata.\n", "\n", "    Args:\n", "        preference_events: List of preference events.\n", "        retriever: The AITutorDataRetriever instance.\n", "\n", "    Returns:\n", "        A list of full preference events.\n", "    \"\"\"\n", "    if not preference_events:\n", "        logger.info(\"No preference events to process\")\n", "        return []\n", "\n", "    # Convert to list for the get_requests method\n", "    request_ids_list = [event[\"request_id\"] for event in preference_events]\n", "\n", "    # Use the GCSRequestInsightFetcher directly for batch retrieval\n", "    logger.info(f\"Using parallel retrieval for {len(request_ids_list)} request IDs\")\n", "\n", "    # Filter for preference_sample events only\n", "    request_event_names = frozenset([\"preference_sample\"])\n", "\n", "    # Get requests in parallel with a reasonable batch size\n", "    batch_size = min(32, (os.cpu_count() or 1) + 4)\n", "    requests_iterator = retriever.gcs_fetcher.get_requests(\n", "        request_ids=request_ids_list,\n", "        request_event_names=request_event_names,\n", "        batch_size=batch_size,\n", "    )\n", "    all_preference_events = []\n", "\n", "    # Create a mapping of request_id to original event for faster lookups\n", "    request_id_to_event = {event[\"request_id\"]: event for event in preference_events}\n", "\n", "    # Process the results\n", "    for result in requests_iterator:\n", "        if isinstance(result, Exception):\n", "            logger.warning(f\"Error retrieving request: {result}\")\n", "            continue\n", "\n", "        # Process the events in this request\n", "        for event in result.events:\n", "            try:\n", "                event_dict = MessageToDict(event)\n", "\n", "                # Check if this is a preference_sample event\n", "                if \"preferenceSample\" in event_dict:\n", "                    # Add metadata from the original event\n", "                    orig_event = request_id_to_event.get(result.request_id)\n", "                    if orig_event:\n", "                        event_dict[\"eventId\"] = orig_event[\"event_id\"]\n", "                        event_dict[\"request_id\"] = orig_event[\"request_id\"]\n", "                        event_dict[\"tenant\"] = orig_event[\"tenant\"]\n", "                        event_dict[\"tenant_id\"] = orig_event[\"tenant_id\"]\n", "                        event_dict[\"event_type\"] = orig_event[\"event_type\"]\n", "                        event_dict[\"chat_a_request_id\"] = event_dict[\n", "                            \"preferenceSample\"\n", "                        ][\"requestIds\"][0]\n", "                        event_dict[\"chat_b_request_id\"] = event_dict[\n", "                            \"preferenceSample\"\n", "                        ][\"requestIds\"][1]\n", "\n", "                    all_preference_events.append(event_dict)\n", "\n", "            except Exception as e:\n", "                logger.warning(\n", "                    f\"Error processing event for request {result.request_id}: {e}\"\n", "                )\n", "\n", "    logger.info(f\"Found {len(all_preference_events)} preference events\")\n", "\n", "    return all_preference_events\n", "\n", "\n", "def extract_model_names(feedback: str) -> Tuple[str, str]:\n", "    \"\"\"Extract model names from the feedback text.\n", "\n", "    Args:\n", "        feedback: The feedback text.\n", "\n", "    Returns:\n", "        A tuple of two model names (model A, model B).\n", "    \"\"\"\n", "    model_pattern = r\"MODEL_IDS_START_LABEL\\s*(.+?)\\s*MODEL_IDS_END_LABEL\"\n", "    model_match = re.search(model_pattern, feedback, re.DOTALL)\n", "    if model_match:\n", "        models = model_match.group(1).strip().split(\"\\n\")\n", "        if len(models) == 2:\n", "            return models[0], models[1]\n", "        return models[0], \"Unknown\"\n", "    return \"Unknown\", \"Unknown\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tenant_names = [\"aitutor-turing\", \"aitutor-mercor\"]\n", "days = 8\n", "limit = 50000\n", "verbose = False\n", "\n", "# Set logging level based on verbosity\n", "if verbose:\n", "    logging.getLogger().setLevel(logging.DEBUG)\n", "\n", "combined_preference_events = []\n", "tenant_stats = {}\n", "\n", "print(f\"Starting preference event collection from {len(tenant_names)} tenants\")\n", "print(f\"Looking back {days} days with a limit of {limit} events per tenant\")\n", "\n", "for tenant_name in tenant_names:\n", "    tenant_stats[tenant_name] = {\"found\": 0, \"loaded\": 0, \"errors\": 0}\n", "\n", "    try:\n", "        print(f\"\\nProcessing tenant: {tenant_name}\")\n", "        retriever = AITutorDataRetriever(tenant_name=tenant_name)\n", "\n", "        # Get preference events\n", "        preference_events = get_preference_events(\n", "            retriever=retriever, days=days, limit=limit\n", "        )\n", "        tenant_stats[tenant_name][\"found\"] = len(preference_events)\n", "        print(f\"Found {len(preference_events)} preference events\")\n", "\n", "        # Get full preference events\n", "        full_events = get_full_preference_event(\n", "            preference_events=preference_events, retriever=retriever\n", "        )\n", "        tenant_stats[tenant_name][\"loaded\"] = len(full_events)\n", "        print(f\"Loaded {len(full_events)} preference events\")\n", "\n", "        # Add model names\n", "        model_counts = {}\n", "        for event in full_events:\n", "            if \"preferenceSample\" in event and \"feedback\" in event[\"preferenceSample\"]:\n", "                try:\n", "                    event[\"model_a\"], event[\"model_b\"] = extract_model_names(\n", "                        event[\"preferenceSample\"][\"feedback\"]\n", "                    )\n", "                    event[\"winning_model_name\"] = (\n", "                        event[\"model_a\"]\n", "                        if event[\"preferenceSample\"][\"scores\"][\"overallRating\"] < 0\n", "                        else event[\"model_b\"]\n", "                        if event[\"preferenceSample\"][\"scores\"][\"overallRating\"] > 0\n", "                        else \"Tie\"\n", "                    )\n", "                    # Count model occurrences\n", "                    for model in [event[\"model_a\"], event[\"model_b\"]]:\n", "                        if model != \"Unknown\":\n", "                            model_counts[model] = model_counts.get(model, 0) + 1\n", "                except Exception as e:\n", "                    logger.warning(f\"Error extracting model names: {e}\")\n", "                    tenant_stats[tenant_name][\"errors\"] += 1\n", "\n", "        # Print model statistics\n", "        if model_counts:\n", "            print(f\"Model distribution in {tenant_name}:\")\n", "            for model, count in sorted(\n", "                model_counts.items(), key=lambda x: x[1], reverse=True\n", "            )[:5]:\n", "                print(f\"  {model}: {count}\")\n", "            if len(model_counts) > 5:\n", "                print(f\"  ... and {len(model_counts) - 5} more models\")\n", "\n", "        # Print a sample\n", "        if full_events:\n", "            print(\"\\nSample event:\")\n", "            print(json.dumps(full_events[:1], indent=2))\n", "        else:\n", "            print(\"No events found\")\n", "\n", "        # Add to combined events\n", "        combined_preference_events.extend(full_events)\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing tenant {tenant_name}: {e}\")\n", "        tenant_stats[tenant_name][\"errors\"] += 1\n", "        logger.exception(f\"Exception details for {tenant_name}\")\n", "\n", "\n", "# Print summary\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"SUMMARY\")\n", "print(\"=\" * 50)\n", "for tenant, stats in tenant_stats.items():\n", "    print(\n", "        f\"{tenant}: Found {stats['found']}, Loaded {stats['loaded']}, Errors {stats['errors']}\"\n", "    )\n", "print(f\"Total preference events collected: {len(combined_preference_events)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "\n", "def get_request_metadata(\n", "    retriever: AITutorDataRetriever, request_ids: List[str]\n", ") -> Dict[str, Dict[str, Any]]:\n", "    \"\"\"Retrieve metadata for a list of request IDs.\n", "\n", "    Args:\n", "        retriever: The AITutorDataRetriever instance.\n", "        request_ids: List of request IDs to fetch metadata for.\n", "\n", "    Returns:\n", "        A dictionary mapping request IDs to their metadata.\n", "    \"\"\"\n", "    if not request_ids:\n", "        logger.info(\"No request IDs provided for metadata retrieval\")\n", "        return {}\n", "\n", "    # Query the request_metadata table\n", "    metadata_query = f\"\"\"\n", "    SELECT \n", "        request_id,\n", "        user_id,\n", "        user_agent,\n", "    FROM `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_metadata`\n", "    WHERE \n", "        request_id IN UNNEST(@request_ids)\n", "        AND tenant = '{retriever.tenant_name}'\n", "    \"\"\"\n", "\n", "    job_config = bigquery.QueryJobConfig(\n", "        query_parameters=[\n", "            bigquery.ArrayQueryParameter(\"request_ids\", \"STRING\", request_ids),\n", "        ]\n", "    )\n", "\n", "    logger.info(f\"Fetching metadata for {len(request_ids)} requests\")\n", "    metadata_job = retriever.bq_client.query(metadata_query, job_config=job_config)\n", "    metadata_rows = list(metadata_job.result())\n", "\n", "    # Create a dictionary mapping request IDs to their metadata\n", "    metadata_by_request_id = {}\n", "    for row in metadata_rows:\n", "        request_id = row[\"request_id\"]\n", "        metadata_by_request_id[request_id] = {\n", "            \"user_id\": row[\"user_id\"],\n", "            \"user_agent\": row[\"user_agent\"],\n", "            \"request_id\": row[\"request_id\"],\n", "        }\n", "\n", "    logger.info(\n", "        f\"Retrieved metadata for {len(metadata_by_request_id)} out of {len(request_ids)} requests\"\n", "    )\n", "\n", "    return metadata_by_request_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_preference_events[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Model distribution:\")\n", "total_model_counts = {}\n", "for event in combined_preference_events:\n", "    for model in [event[\"model_a\"], event[\"model_b\"]]:\n", "        if model != \"Unknown\":\n", "            total_model_counts[model] = total_model_counts.get(model, 0) + 1\n", "for model, count in sorted(\n", "    total_model_counts.items(), key=lambda x: x[1], reverse=True\n", "):\n", "    print(f\"  {model}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(\n", "    [\n", "        {\n", "            \"overallRating\": sample[\"preferenceSample\"][\"scores\"][\"overallRating\"],\n", "            \"formattingRating\": sample[\"preferenceSample\"][\"scores\"][\n", "                \"formattingRating\"\n", "            ],\n", "            \"instructionFollowingRating\": sample[\"preferenceSample\"][\"scores\"][\n", "                \"instructionFollowingRating\"\n", "            ],\n", "            \"isHighQuality\": sample[\"preferenceSample\"][\"scores\"][\"isHighQuality\"],\n", "            \"model_a\": sample[\"model_a\"],\n", "            \"model_b\": sample[\"model_b\"],\n", "            \"winning_model\": sample[\"winning_model_name\"],\n", "            \"datetime\": sample[\"time\"],\n", "        }\n", "        for sample in combined_preference_events\n", "    ]\n", ")\n", "\n", "# remove battles from models with less then 50 battles\n", "model_counts = pd.concat([df[\"model_a\"], df[\"model_b\"]]).value_counts()\n", "model_counts = model_counts[model_counts >= 50]\n", "model_counts.sort_values(ascending=False)\n", "df = df[df[\"model_a\"].isin(model_counts.index) & df[\"model_b\"].isin(model_counts.index)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["elo_table = generate_elo_table(df)\n", "elo_table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from experimental.tamuz.preferences.html_report_utils import (\n", "    calculate_combined_stats,\n", "    calculate_separate_stats,\n", ")\n", "\n", "\n", "separate_stats = calculate_separate_stats(df)\n", "combined_stats = calculate_combined_stats(df)\n", "crosstab = pd.crosstab(\n", "    df[\"model_a\"], df[\"model_b\"], values=df[\"overallRating\"], aggfunc=\"mean\"\n", ")\n", "np.fill_diagonal(crosstab.values, np.nan)\n", "# Set pandas display options to show full DataFrame\n", "pd.set_option(\"display.max_rows\", None)  # Show all rows\n", "pd.set_option(\"display.max_columns\", None)  # Show all columns\n", "pd.set_option(\"display.width\", None)  # Auto-detect display width\n", "pd.set_option(\"display.max_colwidth\", None)  # Show full content of each column\n", "pd.options.display.float_format = \"{:.2f}\".format  # Format floats to 2 decimal places"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crosstab"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}
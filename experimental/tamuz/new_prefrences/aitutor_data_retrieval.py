"""Module for retrieving AI tutor data from BigQuery and GCS.

This module provides utilities to retrieve AI tutor data from the search and analytics
datasets in BigQuery, as well as from the events bucket in GCS.

The full_export_dataset is deprecated, so this module uses the newer datasets.
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Union, Any

from google.cloud import bigquery  # type: ignore

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant, get_tenant
from base.datasets.gcs_client import GCSRequestInsightFetcher, Request


logger = logging.getLogger(__name__)


@dataclass
class AITutorRequest:
    """Represents an AI tutor request with metadata and content."""

    request_id: str
    tenant_name: str
    user_id: str
    time: datetime
    request_type: str
    request_content: Dict[str, Any]
    response_content: Optional[Dict[str, Any]] = None
    events: Optional[List[Any]] = None


class AITutorDataRetriever:
    """Class for retrieving AI tutor data from BigQuery and GCS."""

    def __init__(
        self,
        tenant_name: str,
        max_pool_connections: int = 100,
    ):
        """Initialize the retriever with the tenant name.

        Args:
            tenant_name: The name of the tenant to retrieve data for.
            max_pool_connections: Maximum number of connections for GCS client.
        """
        self.tenant = get_tenant(tenant_name)
        self.tenant_name = tenant_name
        self.project_id = self.tenant.project_id

        # Initialize BigQuery client
        credentials, _ = get_gcp_creds()
        self.bq_client = bigquery.Client(
            project=self.project_id, credentials=credentials
        )

        # Initialize GCS client for events
        self.gcs_fetcher = GCSRequestInsightFetcher.from_tenant(
            tenant=self.tenant, max_pool_connections=max_pool_connections
        )

    def get_requests_by_user(
        self,
        user_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        request_type: Optional[str] = None,
        limit: int = 100,
    ) -> List[AITutorRequest]:
        """Get requests for a specific user.

        Args:
            user_id: The user ID to retrieve requests for.
            start_time: The start time for the query (default: 7 days ago).
            end_time: The end time for the query (default: now).
            request_type: Filter by request type (e.g., 'CHAT', 'EDIT').
            limit: Maximum number of requests to return.

        Returns:
            A list of AITutorRequest objects.
        """
        if start_time is None:
            start_time = datetime.utcnow() - timedelta(days=7)

        if end_time is None:
            end_time = datetime.utcnow()

        # Build the query
        query = f"""
        SELECT DISTINCT
            rm.request_id,
            rm.tenant,
            rm.user_id,
            rm.time,
            rm.request_type

        FROM `{self.project_id}.{self.tenant.search_dataset_name}.request_metadata` rm
        JOIN `{self.project_id}.{self.tenant.search_dataset_name}.request_event` re
        ON rm.request_id = re.request_id
        AND re.event_type = 'preference_sample'
        WHERE
            rm.tenant = @tenant_name
            AND rm.user_id = @user_id
            AND rm.time BETWEEN @start_time AND @end_time
        """

        if request_type:
            query += " AND rm.request_type = @request_type"

        query += """
        ORDER BY rm.time DESC
        LIMIT @limit
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter(
                    "tenant_name", "STRING", self.tenant_name
                ),
                bigquery.ScalarQueryParameter("user_id", "STRING", user_id),
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
                bigquery.ScalarQueryParameter("limit", "INT64", limit),
            ]
        )

        if request_type:
            job_config.query_parameters.append(
                bigquery.ScalarQueryParameter("request_type", "STRING", request_type)
            )

        # Execute the query
        logger.info(f"Executing query to get requests for user {user_id}")
        query_job = self.bq_client.query(query, job_config=job_config)
        rows = list(query_job.result())

        # Process the results
        request_ids = [row.request_id for row in rows]

        # Get response data for these requests
        responses = self._get_responses(request_ids)

        # Create AITutorRequest objects
        requests = []
        for row in rows:
            request = AITutorRequest(
                request_id=row.request_id,
                tenant_name=row.tenant,
                user_id=row.user_id,
                time=row.time,
                request_type=row.request_type,
                request_content={},
                response_content=responses.get(row.request_id),
            )
            requests.append(request)

        return requests

    def get_request_by_id(self, request_id: str) -> Optional[AITutorRequest]:
        """Get a specific request by its ID.

        Args:
            request_id: The ID of the request to retrieve.

        Returns:
            An AITutorRequest object or None if not found.
        """
        # Query the metadata
        query = f"""
        SELECT DISTINCT
            rm.request_id,
            rm.tenant,
            rm.user_id,
            rm.time,
            rm.request_type

        FROM `{self.project_id}.{self.tenant.search_dataset_name}.request_metadata` rm
        JOIN `{self.project_id}.{self.tenant.search_dataset_name}.request_event` re
        ON rm.request_id = re.request_id
        AND re.event_type = 'preference_sample'
        WHERE
            rm.request_id = @request_id
            AND rm.tenant = @tenant_name
        LIMIT 1
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("request_id", "STRING", request_id),
                bigquery.ScalarQueryParameter(
                    "tenant_name", "STRING", self.tenant_name
                ),
            ]
        )

        # Execute the query
        logger.info(f"Executing query to get request {request_id}")
        query_job = self.bq_client.query(query, job_config=job_config)
        rows = list(query_job.result())

        if not rows:
            logger.warning(f"No request found with ID {request_id}")
            return None

        row = rows[0]

        # Get response data
        responses = self._get_responses([request_id])

        # Get events from GCS
        try:
            request_with_events = self.gcs_fetcher.get_request(request_id)
            events = request_with_events.events
        except Exception as e:
            logger.warning(f"Failed to fetch events for request {request_id}: {e}")
            events = []

        # Create and return the AITutorRequest object
        return AITutorRequest(
            request_id=row.request_id,
            tenant_name=row.tenant,
            user_id=row.user_id,
            time=row.time,
            request_type=row.request_type,
            request_content={},
            response_content=responses.get(request_id),
            events=events,
        )

    def get_requests_by_time_range(
        self,
        start_time: datetime,
        end_time: Optional[datetime] = None,
        request_type: Optional[str] = None,
        limit: int = 100,
    ) -> List[AITutorRequest]:
        """Get requests within a specific time range.

        Args:
            start_time: The start time for the query.
            end_time: The end time for the query (default: now).
            request_type: Filter by request type (e.g., 'CHAT', 'EDIT').
            limit: Maximum number of requests to return.

        Returns:
            A list of AITutorRequest objects.
        """
        if end_time is None:
            end_time = datetime.utcnow()

        # Build the query
        query = f"""
        SELECT DISTINCT
            rm.request_id,
            rm.tenant,
            rm.user_id,
            rm.time,
            rm.request_type

        FROM `{self.project_id}.{self.tenant.search_dataset_name}.request_metadata` rm
        JOIN `{self.project_id}.{self.tenant.search_dataset_name}.request_event` re
        ON rm.request_id = re.request_id
        AND re.event_type = 'preference_sample'
        WHERE
            rm.tenant = @tenant_name
            AND rm.time BETWEEN @start_time AND @end_time
        """

        if request_type:
            query += " AND rm.request_type = @request_type"

        query += """
        ORDER BY rm.time DESC
        LIMIT @limit
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter(
                    "tenant_name", "STRING", self.tenant_name
                ),
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
                bigquery.ScalarQueryParameter("limit", "INT64", limit),
            ]
        )

        if request_type:
            job_config.query_parameters.append(
                bigquery.ScalarQueryParameter("request_type", "STRING", request_type)
            )

        # Execute the query
        logger.info(
            f"Executing query to get requests between {start_time} and {end_time}"
        )
        query_job = self.bq_client.query(query, job_config=job_config)
        rows = list(query_job.result())

        # Process the results
        request_ids = [row.request_id for row in rows]

        # Get response data for these requests
        responses = self._get_responses(request_ids)

        # Create AITutorRequest objects
        requests = []
        for row in rows:
            request = AITutorRequest(
                request_id=row.request_id,
                tenant_name=row.tenant,
                user_id=row.user_id,
                time=row.time,
                request_type=row.request_type,
                request_content={},
                response_content=responses.get(row.request_id),
            )
            requests.append(request)

        return requests

    def get_preference_events(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """Get preference events.

        Args:
            start_time: The start time for the query (default: 30 days ago).
            end_time: The end time for the query (default: now).
            limit: Maximum number of events to return.

        Returns:
            A list of dictionaries containing the preference events.
        """
        if start_time is None:
            start_time = datetime.utcnow() - timedelta(days=30)

        if end_time is None:
            end_time = datetime.utcnow()

        # Build the query to get preference events
        query = f"""
        SELECT
            re.request_id,
            re.tenant,
            re.tenant_id,
            re.event_type,
            re.event_id,
            re.time
        FROM `{self.project_id}.{self.tenant.search_dataset_name}.request_event` re
        WHERE
            re.event_type = 'preference_sample'
            AND re.time BETWEEN @start_time AND @end_time
        ORDER BY re.time DESC
        LIMIT @limit
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
                bigquery.ScalarQueryParameter("limit", "INT64", limit),
            ]
        )

        # Execute the query
        logger.info(
            f"Executing query to get preference events between {start_time} and {end_time}"
        )
        query_job = self.bq_client.query(query, job_config=job_config)
        rows = list(query_job.result())

        # Convert rows to dictionaries
        return [dict(row.items()) for row in rows]

    def get_analytics_data(
        self,
        request_ids: List[str],
        table_name: str,
    ) -> List[Dict[str, Any]]:
        """Get analytics data for specific request IDs.

        Args:
            request_ids: List of request IDs to retrieve data for.
            table_name: The name of the analytics table to query.

        Returns:
            A list of dictionaries containing the analytics data.
        """
        if not request_ids:
            return []

        # Format the request IDs for the query
        formatted_request_ids = ", ".join([f"'{rid}'" for rid in request_ids])

        # Build the query
        query = f"""
        SELECT *
        FROM `{self.project_id}.{self.tenant.analytics_dataset_name}.{table_name}`
        WHERE request_id IN ({formatted_request_ids})
        """

        # Execute the query
        logger.info(
            f"Executing query to get analytics data for {len(request_ids)} requests"
        )
        query_job = self.bq_client.query(query)
        rows = list(query_job.result())

        # Convert rows to dictionaries
        return [dict(row.items()) for row in rows]

    def get_events_for_requests(
        self,
        request_ids: List[str],
        event_names: Optional[Set[str]] = None,
    ) -> Dict[str, List[Any]]:
        """Get events from GCS for specific request IDs.

        Args:
            request_ids: List of request IDs to retrieve events for.
            event_names: Optional set of event names to filter by.

        Returns:
            A dictionary mapping request IDs to lists of events.
        """
        if not request_ids:
            return {}

        result = {}
        for request_id in request_ids:
            try:
                # Convert event_names to frozenset if provided
                frozen_event_names = frozenset(event_names) if event_names else None

                # Get the request with events
                request_with_events = self.gcs_fetcher.get_request(
                    request_id=request_id, request_event_names=frozen_event_names
                )

                # Add to result
                result[request_id] = request_with_events.events
            except Exception as e:
                logger.warning(f"Failed to fetch events for request {request_id}: {e}")
                result[request_id] = []

        return result

    def _get_responses(self, request_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get response data for a list of request IDs.

        Args:
            request_ids: List of request IDs to retrieve responses for.

        Returns:
            A dictionary mapping request IDs to response content.
        """
        if not request_ids:
            return {}

        # Format the request IDs for the query
        formatted_request_ids = ", ".join([f"'{rid}'" for rid in request_ids])

        # Build the query to get responses
        query = f"""
        SELECT
            request_id,
            sanitized_json as response_json
        FROM `{self.project_id}.{self.tenant.analytics_dataset_name}.chat_host_response`
        WHERE request_id IN ({formatted_request_ids})
        """

        # Execute the query
        logger.info(f"Executing query to get responses for {len(request_ids)} requests")
        query_job = self.bq_client.query(query)
        rows = list(query_job.result())

        # Create a dictionary mapping request IDs to response content
        return {row.request_id: row.response_json for row in rows}


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    # Example: Retrieve data for a specific AI tutor tenant
    retriever = AITutorDataRetriever(tenant_name="aitutor-turing")

    # Get preference events
    from datetime import datetime, timedelta

    start_time = datetime.utcnow() - timedelta(days=180)
    preference_events = retriever.get_preference_events(start_time=start_time, limit=10)

    print(
        f"Found {len(preference_events)} preference_sample events in the last 180 days"
    )

    # Print the preference events
    for i, event in enumerate(preference_events):
        print(f"\nPreference Event {i+1}:")
        print(f"  Request ID: {event['request_id']}")
        print(f"  Tenant: {event['tenant']}")
        print(f"  Tenant ID: {event['tenant_id']}")
        print(f"  Event ID: {event['event_id']}")
        print(f"  Event Type: {event['event_type']}")
        print(f"  Time: {event['time']}")

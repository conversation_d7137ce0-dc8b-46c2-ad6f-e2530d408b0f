{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IDE Comparison Analysis\n", "\n", "Analyzing completion differences between JetBrains and VSCode users."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from google.cloud import bigquery\n", "import db_dtypes # type: ignore\n", "from sklearn.cluster import DBSCAN\n", "import numpy as np\n", "from datetime import timedelta\n", "import shapely\n", "\n", "# Constants\n", "PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"us_prod_request_insight_analytics_dataset\"\n", "# Test users and their IDs\n", "USERS = {\n", "    'khushi_vscode': 'M5OXo+WfAfLPtT/C2rWmtO638hV2wfaptBMY7PAXc54=',\n", "    'khushi_jetbrains': 'aV3jawVSNVnDpC7R/9oCKQK3TBBxlaLxxcilrOclt34=',\n", "    'pranav_vscode': '7iOD3Rdhf/NKnU5UyZTXrBVddVXoleuCKGrBDyhYcWA=',\n", "    'pranav_jetbrains': 'hf8iMkvuNPoRfYN9t6SARCtBEMrj7zBvauno93xwJOo='\n", "}\n", "\n", "# Initialize BigQuery client\n", "client = bigquery.Client(project=PROJECT_ID)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set pandas display options for full output\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.expand_frame_repr', False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH completion_data AS (\n", "    SELECT \n", "        rm.time,\n", "        rm.user_agent,\n", "        rm.user_id,\n", "        cr.accepted,\n", "        CASE \n", "            WHEN LOWER(rm.user_agent) LIKE '%pycharm%' THEN 'PyCharm'\n", "            WHEN LOWER(rm.user_agent) LIKE '%webstorm%' THEN 'WebStorm'\n", "            WHEN LOWER(rm.user_agent) LIKE '%rider%' THEN 'Rider'\n", "            WHEN LOWER(rm.user_agent) LIKE '%goland%' THEN 'GoLand'\n", "            WHEN LOWER(rm.user_agent) LIKE '%clion%' THEN 'CLion'\n", "            WHEN LOWER(rm.user_agent) LIKE '%jetbrains%' THEN 'JetBrains'\n", "            WHEN LOWER(rm.user_agent) LIKE '%vscode%' THEN 'VSCode'\n", "            ELSE 'Other'\n", "        END as ide\n", "    FROM `{PROJECT_ID}.{DATASET_NAME}.completion_resolution` cr\n", "    JOIN `{PROJECT_ID}.{DATASET_NAME}.request_metadata` rm \n", "        ON cr.request_id = rm.request_id\n", "    WHERE \n", "        rm.user_id IN UNNEST({list(USERS.values())})\n", "        AND rm.time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 15 DAY)\n", "),\n", "time_gaps AS (\n", "    SELECT \n", "        ide,\n", "        user_agent,\n", "        user_id,\n", "        time,\n", "        accepted,\n", "        LEAD(time) OVER (PARTITION BY user_id, ide, user_agent ORDER BY time) as next_time\n", "    FROM completion_data\n", "    WHERE ide != 'Other'\n", ")\n", "SELECT \n", "    ide,\n", "    user_id,\n", "    user_agent,\n", "    COUNT(*) as total_completions,\n", "    COUNT(DISTINCT DATE(time)) as active_days,\n", "    ROUND(SUM(\n", "        CASE \n", "            WHEN TIMESTAMP_DIFF(next_time, time, SECOND) <= 300  -- 5 minute session timeout\n", "            THEN TIMESTAMP_DIFF(next_time, time, SECOND)\n", "            ELSE 0 \n", "        END\n", "    ) / 3600.0, 2) as estimated_active_hours,\n", "    COUNTIF(accepted) as accepted_completions,\n", "    ROUND(COUNTIF(accepted) * 100.0 / COUNT(*), 2) as acceptance_rate\n", "FROM time_gaps\n", "GROUP BY ide, user_id, user_agent\n", "ORDER BY total_completions DESC;\n", "\"\"\"\n", "df = client.query(query).to_dataframe()\n", "\n", "# Add user names to the DataFrame after the query\n", "user_name_map = {v: k for k, v in USERS.items()}\n", "df['user_name'] = df['user_id'].map(user_name_map)\n", "df.drop(columns=['user_id'], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# Display full DataFrame\n", "print(\"\\nFull Statistics:\")\n", "print(\"-\" * 80)\n", "print(df)\n", "\n", "# Display basic statistics by IDE\n", "print(\"\\nOverall Statistics by IDE:\")\n", "print(\"-\" * 80)\n", "print(df.groupby('ide')[['total_completions', 'accepted_completions', 'acceptance_rate']].mean())\n", "\n", "# Create visualization\n", "fig = px.bar(df, \n", "             x='user_name', \n", "             y='total_completions', \n", "             color='ide',\n", "             barmode='group',\n", "             title='Completions by User and IDE',\n", "             labels={'user_name': 'User', \n", "                    'total_completions': 'Total Completions',\n", "                    'ide': 'IDE'})\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grouped = df.groupby(['user_name', 'ide']).agg({\n", "    'estimated_active_hours': 'sum',\n", "    'total_completions': 'sum',\n", "    'accepted_completions': 'sum'\n", "}).assign(\n", "    acceptance_rate=lambda x: (x['accepted_completions'] / x['total_completions'] * 100).round(2),\n", "    completions_per_hour=lambda x: (x['total_completions'] / x['estimated_active_hours']).round(2)\n", ")\n", "\n", "grouped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH completion_data AS (\n", "    SELECT \n", "        rm.time,\n", "        rm.user_id,\n", "        cr.accepted,\n", "        CASE \n", "            WHEN LOWER(rm.user_agent) LIKE '%pycharm%' THEN 'PyCharm'\n", "            WHEN LOWER(rm.user_agent) LIKE '%webstorm%' THEN 'WebStorm'\n", "            WHEN LOWER(rm.user_agent) LIKE '%rider%' THEN 'Rider'\n", "            WHEN LOWER(rm.user_agent) LIKE '%goland%' THEN 'GoLand'\n", "            WHEN LOWER(rm.user_agent) LIKE '%clion%' THEN 'CLion'\n", "            WHEN LOWER(rm.user_agent) LIKE '%jetbrains%' THEN 'JetBrains'\n", "            WHEN LOWER(rm.user_agent) LIKE '%vscode%' THEN 'VSCode'\n", "            ELSE 'Other'\n", "        END as ide\n", "    FROM `{PROJECT_ID}.{DATASET_NAME}.completion_resolution` cr\n", "    JOIN `{PROJECT_ID}.{DATASET_NAME}.request_metadata` rm \n", "        ON cr.request_id = rm.request_id\n", "    WHERE \n", "        rm.user_id IN UNNEST({list(USERS.values())})\n", "        AND rm.time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 9 DAY)\n", ")\n", "SELECT \n", "    time,\n", "    user_id,\n", "    ide,\n", "    accepted\n", "FROM completion_data\n", "WHERE ide != 'Other'\n", "ORDER BY time;\n", "\"\"\"\n", "\n", "df = client.query(query).to_dataframe()\n", "\n", "# Add user names\n", "user_name_map = {v: k for k, v in USERS.items()}\n", "df['user_name'] = df['user_id'].map(user_name_map)\n", "\n", "# Convert time to datetime if it isn't already\n", "df['time'] = pd.to_datetime(df['time'])\n", "\n", "# Create scatter plot\n", "import plotly.express as px\n", "\n", "fig = px.scatter(df, \n", "    x='time', \n", "    y='user_name',\n", "    color='ide',\n", "    symbol='accepted',\n", "    title='Completion Timeline by User and IDE',\n", "    labels={\n", "        'time': 'Time',\n", "        'user_name': 'User',\n", "        'ide': 'IDE',\n", "        'accepted': 'Accepted'\n", "    },\n", "    height=400\n", ")\n", "\n", "fig.update_traces(marker=dict(size=8))\n", "fig.update_layout(\n", "    xaxis_title=\"Time\",\n", "    yaxis_title=\"User\",\n", "    legend_title=\"IDE\"\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["max_gap_minutes=20\n", "sessions = []\n", "for (user, ide), group in df.groupby(['user_name', 'ide']):\n", "    if len(group) < 2:\n", "        continue\n", "        \n", "    # Convert timestamps to numerical values (seconds since epoch)\n", "    X = group['time'].map(lambda x: x.timestamp()).values.reshape(-1, 1)\n", "\n", "    # Run DBSCAN\n", "    # eps = max_gap_minutes * 60 (convert to seconds)\n", "    clustering = DBSCAN(eps=max_gap_minutes * 60, min_samples=2).fit(X)\n", "\n", "    # Add session labels to the group\n", "    group_with_sessions = group.copy()\n", "    group_with_sessions['session_id'] = clustering.labels_\n", "\n", "    # Only keep valid sessions (label >= 0)\n", "    valid_sessions = group_with_sessions[group_with_sessions['session_id'] >= 0]\n", "\n", "\n", "    for session_id, session_data in valid_sessions.groupby('session_id'):\n", "        if (session_data['time'].max() - session_data['time'].min()).total_seconds() == 0:\n", "            continue\n", "        session_info = {\n", "            'user': user,\n", "            'ide': ide,\n", "            'session_id': f\"{user}_{ide}_{session_id}\",\n", "            'start_time': session_data['time'].min(),\n", "            'end_time': session_data['time'].max(),\n", "            'duration_minutes': (session_data['time'].max() - session_data['time'].min()).total_seconds() / 60,\n", "            'completion_count': len(session_data),\n", "            'accepted_count': session_data['accepted'].sum(),\n", "            'acceptance_rate': (session_data['accepted'].sum() / len(session_data) * 100),\n", "            'completions_per_hour': len(session_data) / ((session_data['time'].max() - session_data['time'].min()).total_seconds() / 3600)\n", "        }\n", "        sessions.append(session_info)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def identify_sessions(df, max_gap_minutes=20):\n", "    \"\"\"\n", "    Identify sessions using DBSCAN clustering on timestamps.\n", "    Parameters:\n", "        df: DataFrame with 'time' column\n", "        max_gap_minutes: Maximum time gap (in minutes) to consider same session\n", "    \"\"\"\n", "    # Group by user and IDE since sessions are per-user, per-IDE\n", "    sessions = []\n", "    \n", "    for (user, ide), group in df.groupby(['user_name', 'ide']):\n", "        if len(group) < 2:\n", "            continue\n", "            \n", "        # Convert timestamps to numerical values (seconds since epoch)\n", "        X = group['time'].map(lambda x: x.timestamp()).values.reshape(-1, 1)\n", "        \n", "        # Run DBSCAN\n", "        # eps = max_gap_minutes * 60 (convert to seconds)\n", "        clustering = DBSCAN(eps=max_gap_minutes * 60, min_samples=2).fit(X)\n", "        \n", "        # Add session labels to the group\n", "        group_with_sessions = group.copy()\n", "        group_with_sessions['session_id'] = clustering.labels_\n", "        \n", "        # Only keep valid sessions (label >= 0)\n", "        valid_sessions = group_with_sessions[group_with_sessions['session_id'] >= 0]\n", "        \n", "        for session_id, session_data in valid_sessions.groupby('session_id'):\n", "            if len(session_data) == 1 or (session_data['time'].max() - session_data['time'].min()).total_seconds() == 0:\n", "                continue\n", "            session_info = {\n", "                'user': user,\n", "                'ide': ide,\n", "                'session_id': f\"{user}_{ide}_{session_id}\",\n", "                'start_time': session_data['time'].min(),\n", "                'end_time': session_data['time'].max(),\n", "                'duration_minutes': (session_data['time'].max() - session_data['time'].min()).total_seconds() / 60,\n", "                'completion_count': len(session_data),\n", "                'accepted_count': session_data['accepted'].sum(),\n", "                'acceptance_rate': (session_data['accepted'].sum() / len(session_data) * 100),\n", "                'completions_per_hour': len(session_data) / ((session_data['time'].max() - session_data['time'].min()).total_seconds() / 3600)\n", "            }\n", "            sessions.append(session_info)\n", "    \n", "    return pd.<PERSON><PERSON><PERSON><PERSON>(sessions)\n", "\n", "# Create sessions DataFrame\n", "sessions_df = identify_sessions(df)\n", "\n", "# Print summary statistics\n", "print(\"\\nSession Statistics:\")\n", "print(\"-\" * 50)\n", "print(f\"Total Sessions: {len(sessions_df)}\")\n", "print(f\"\\nAverage Session Duration (minutes): {sessions_df['duration_minutes'].mean():.2f}\")\n", "print(f\"Average Completions per Session: {sessions_df['completion_count'].mean():.2f}\")\n", "print(f\"Average Completions per Hour: {sessions_df['completions_per_hour'].mean():.2f}\")\n", "\n", "# Visualize sessions\n", "import plotly.figure_factory as ff\n", "\n", "# Create timeline visualization\n", "def create_session_timeline(sessions_df):\n", "    df_plot = sessions_df.copy()\n", "    \n", "    # Create task list for Gantt chart\n", "    tasks = []\n", "    for _, row in df_plot.iterrows():\n", "        tasks.append(dict(\n", "            Task=f\"{row['user']} ({row['ide']})\",\n", "            Start=row['start_time'],\n", "            Finish=row['end_time'],\n", "            Resource=row['ide'],\n", "            CompletionCount=row['completion_count'],\n", "            AcceptanceRate=f\"{row['acceptance_rate']:.1f}%\"\n", "        ))\n", "    \n", "    # Get unique IDE values and create color mapping\n", "    unique_ides = sessions_df['ide'].unique()\n", "    default_colors = ['rgb(46, 117, 182)', 'rgb(75, 172, 198)', 'rgb(98, 182, 149)', 'rgb(147, 120, 196)']\n", "    colors = {ide: default_colors[i % len(default_colors)] for i, ide in enumerate(unique_ides)}\n", "    \n", "    fig = ff.create_gantt(tasks, \n", "                         colors=colors,\n", "                         index_col='Resource',\n", "                         show_colorbar=True,\n", "                         group_tasks=True,\n", "                         showgrid_x=True,\n", "                         showgrid_y=True)\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title='User Sessions Timeline',\n", "        height=400,\n", "        xaxis_title='Time',\n", "        yaxis_title='User (IDE)',\n", "    )\n", "    \n", "    return fig\n", "\n", "# Show timeline\n", "timeline_fig = create_session_timeline(sessions_df)\n", "timeline_fig.show()\n", "\n", "# Additional analysis\n", "print(\"\\nSession Statistics by IDE:\")\n", "print(sessions_df.groupby('ide').agg({\n", "    'duration_minutes': ['count', 'mean'],\n", "    'completion_count': 'mean',\n", "    'acceptance_rate': 'mean',\n", "    'completions_per_hour': 'mean'\n", "}).round(2))\n", "\n", "print(\"\\nSession Statistics by User:\")\n", "print(sessions_df.groupby('user').agg({\n", "    'duration_minutes': ['count', 'mean'],\n", "    'completion_count': 'mean',\n", "    'acceptance_rate': 'mean',\n", "    'completions_per_hour': 'mean'\n", "}).round(2))\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sessions_df.head()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "from pathlib import Path\n", "import logging\n", "import traceback\n", "# Configure batch size and output path\n", "BATCH_SIZE = 5000\n", "output_dir = Path(\"data/completion_events\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Define event types\n", "EVENT_TYPES = [\n", "    'completion_host_response',\n", "    'client_completion_timeline',\n", "    'completion_post_process',\n", "    'completion_emit',\n", "    'completion_resolution',\n", "    # 'completion_host_request',\n", "]\n", "\n", "# Query template for a single event type\n", "query_template = \"\"\"\n", "WITH \n", "filtered_metadata AS (\n", "    SELECT DISTINCT\n", "        request_id, \n", "        JSON_VALUE(raw_json, \"$.user_agent\") as user_agent,\n", "        JSON_VALUE(raw_json, \"$.user_id\") as user_id\n", "    FROM `system-services-prod.prod_request_insight_full_export_dataset.request_metadata`\n", "    WHERE day BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY) AND DATE_ADD(CURRENT_DATE(), INTERVAL 1 DAY)\n", "    AND tenant = \"aitutor-mercor\"\n", "    AND JSON_VALUE(raw_json, \"$.user_id\") IN UNNEST([\n", "            '<EMAIL>',\n", "            '<EMAIL>',\n", "            '<EMAIL>',\n", "            '<EMAIL>'\n", "        ])\n", "),\n", "filtered_events AS (\n", "    SELECT \n", "        request_id,\n", "        time,\n", "        TO_JSON_STRING(raw_json) as request,\n", "        event_type\n", "    FROM `system-services-prod.prod_request_insight_full_export_dataset.request_event`\n", "    WHERE tenant = \"aitutor-mercor\"\n", "    AND DATE(time) BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY) AND DATE_ADD(CURRENT_DATE(), INTERVAL 1 DAY)\n", "    AND event_type = @event_type\n", ")\n", "SELECT \n", "    re.request_id,\n", "    re.time,\n", "    re.request,\n", "    re.event_type,\n", "    rm.user_id,\n", "    rm.user_agent\n", "FROM filtered_events re\n", "INNER JOIN filtered_metadata rm \n", "    ON re.request_id = rm.request_id\n", "ORDER BY re.time DESC\n", "LIMIT {limit} OFFSET {offset};\n", "\"\"\"\n", "\n", "# Count query template for a single event type\n", "count_query_template = \"\"\"\n", "WITH \n", "filtered_metadata AS (\n", "    SELECT DISTINCT request_id \n", "    FROM `system-services-prod.prod_request_insight_full_export_dataset.request_metadata`\n", "    WHERE day BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY) AND DATE_ADD(CURRENT_DATE(), INTERVAL 1 DAY)\n", "    AND tenant = \"aitutor-mercor\"\n", "    AND JSON_VALUE(raw_json, \"$.user_id\") IN UNNEST([\n", "            '<EMAIL>',\n", "            '<EMAIL>',\n", "            '<EMAIL>',\n", "            '<EMAIL>'\n", "        ])\n", ")\n", "SELECT COUNT(*) as total\n", "FROM `system-services-prod.prod_request_insight_full_export_dataset.request_event`\n", "WHERE tenant = \"aitutor-mercor\"\n", "AND event_type = @event_type\n", "AND request_id IN (SELECT request_id FROM filtered_metadata)\n", "\"\"\"\n", "\n", "# Process each event type separately\n", "for event_type in EVENT_TYPES:\n", "    logger.info(f\"\\nProcessing event type: {event_type}\")\n", "    output_base = output_dir / f\"{event_type}_{timestamp}\"\n", "    \n", "    # Check if all batches for this event type are already downloaded\n", "    existing_batches = list(output_dir.glob(f\"{event_type}_{timestamp}.batch*.parquet\"))\n", "    if existing_batches:\n", "        logger.info(f\"Found existing batches for {event_type}, skipping...\")\n", "        continue\n", "    \n", "    try:\n", "        # Get count for this event type\n", "        job_config = bigquery.QueryJobConfig(\n", "            query_parameters=[\n", "                bigquery.ScalarQueryParameter(\"event_type\", \"STRING\", event_type),\n", "            ]\n", "        )\n", "        total_count = client.query(count_query_template, job_config=job_config).to_dataframe().iloc[0]['total']\n", "        logger.info(f\"Total records to process: {total_count}\")\n", "    except Exception as e:\n", "        logger.error(f\"Failed to get count for {event_type}: {str(e)}\")\n", "        logger.error(traceback.format_exc())\n", "        continue\n", "    \n", "    failed_batches = []\n", "    # Process in batches\n", "    for offset in range(0, total_count, BATCH_SIZE):\n", "        batch_num = offset // BATCH_SIZE\n", "        output_file = output_base.with_suffix(f'.batch{batch_num}.parquet')\n", "        \n", "        # Skip if this batch file already exists\n", "        if output_file.exists():\n", "            logger.info(f\"Batch {batch_num} already exists, skipping...\")\n", "            continue\n", "            \n", "        logger.info(f\"Processing batch starting at offset {offset}\")\n", "        \n", "        try:\n", "            # Execute query for current batch\n", "            current_query = query_template.format(limit=BATCH_SIZE, offset=offset)\n", "            job_config = bigquery.QueryJobConfig(\n", "                query_parameters=[\n", "                    bigquery.ScalarQueryParameter(\"event_type\", \"STRING\", event_type),\n", "                ]\n", "            )\n", "            df_batch = client.query(current_query, job_config=job_config).to_dataframe()\n", "            \n", "            # Save batch\n", "            df_batch.to_parquet(output_file)\n", "            logger.info(f\"Saved batch {batch_num} to {output_file}\")\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Failed to process batch {batch_num} for {event_type}: {str(e)}\")\n", "            logger.error(traceback.format_exc())\n", "            failed_batches.append(batch_num)\n", "            continue\n", "    \n", "    if failed_batches:\n", "        logger.warning(f\"Failed batches for {event_type}: {failed_batches}\")\n", "    logger.info(f\"Completed processing {event_type}\")\n", "\n", "logger.info(\"\\nAll event types processed and saved!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from pathlib import Path\n", "import logging\n", "import traceback\n", "import json\n", "\n", "# Configure batch size and output path\n", "BATCH_SIZE = 5000\n", "MAX_BATCHES = 1000\n", "output_dir = Path(\"data/latest_completion_events\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "query_template = \"\"\"\n", "SELECT \n", "    request_id,\n", "    time,\n", "    JSON_EXTRACT_SCALAR(request, \"$.blobs.baseline_checkpoint_id\") as checkpoint_id,\n", "    COALESCE(JSON_EXTRACT(request, \"$.blobs.added\"), JSON '[\"\"]') as added_blobs_json,\n", "    COALESCE(JSON_EXTRACT(request, \"$.blobs.deleted\"), JSON '[\"\"]') as deleted_blobs_json,\n", "    user_id,\n", "    user_agent\n", "FROM `system-services-dev.dev_tamuz.mercor_completions_host_request`\n", "ORDER BY time DESC\n", "LIMIT {limit} OFFSET {offset};\n", "\"\"\"\n", "\n", "count_query = \"\"\"\n", "SELECT COUNT(*) as total\n", "FROM `system-services-dev.dev_tamuz.mercor_completions_host_request`\n", "\"\"\"\n", "\n", "def parse_json_array(json_str):\n", "    if json_str is None or (isinstance(json_str, str) and json_str == 'null'):\n", "        return []\n", "        \n", "    try:\n", "        if isinstance(json_str, np.ndarray):\n", "            return json_str.tolist()\n", "            \n", "        if isinstance(json_str, str):\n", "            result = json.loads(json_str)\n", "        else:\n", "            result = json_str\n", "        return result if isinstance(result, list) else []\n", "    except (json.<PERSON><PERSON>, TypeError):\n", "        return []\n", "\n", "def safe_process_row(row):\n", "    try:\n", "        added_blobs = parse_json_array(row['added_blobs_json'])\n", "        deleted_blobs = parse_json_array(row['deleted_blobs_json'])\n", "        \n", "        if not isinstance(added_blobs, list):\n", "            added_blobs = [added_blobs] if added_blobs is not None else []\n", "        if not isinstance(deleted_blobs, list):\n", "            deleted_blobs = [deleted_blobs] if deleted_blobs is not None else []\n", "            \n", "        deleted_blobs = [blob for blob in deleted_blobs if blob and blob.strip()]\n", "        timestamp = pd.to_datetime(row['time']).tz_convert('UTC')\n", "        \n", "        return {\n", "            'request_id': str(row['request_id']),\n", "            'time': timestamp,\n", "            'user_id': str(row['user_id']),\n", "            'user_agent': str(row['user_agent']) if pd.notna(row['user_agent']) else None,\n", "            'deleted_blobs': json.dumps(deleted_blobs),\n", "            'added_blobs': json.dumps(added_blobs),  # Added this back\n", "            'checkpoint_id': str(row['checkpoint_id']) if pd.notna(row['checkpoint_id']) else None,\n", "            'added_blob_count': len(added_blobs),\n", "            'deleted_blob_count': len(deleted_blobs),\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Error processing row {row['request_id']}: {str(e)}\")\n", "        return None\n", "\n", "schema = {\n", "    'request_id': 'string',\n", "    'time': 'datetime64[ns, UTC]',  # Changed to UTC timezone-aware timestamp\n", "    'user_id': 'string',\n", "    'user_agent': 'string',\n", "    # 'deleted_blobs': 'string',\n", "    # 'added_blobs': 'string',  # Added this back\n", "    'checkpoint_id': 'string',\n", "    'added_blob_count': 'int32',\n", "    'deleted_blob_count': 'int32'\n", "}\n", "\n", "try:\n", "    total_count = client.query(count_query).to_dataframe().iloc[0]['total']\n", "    logger.info(f\"Total records: {total_count}\")\n", "    \n", "    for batch_num in range(MAX_BATCHES):\n", "        offset = batch_num * BATCH_SIZE\n", "        if offset >= total_count:\n", "            break\n", "            \n", "        logger.info(f\"Processing batch {batch_num + 1}/{MAX_BATCHES} (offset: {offset})\")\n", "        current_query = query_template.format(limit=BATCH_SIZE, offset=offset)\n", "        df_batch = client.query(current_query).to_dataframe()\n", "        \n", "        if df_batch.empty:\n", "            break\n", "            \n", "        batch_rows = []\n", "        for _, row in df_batch.iterrows():\n", "            processed_row = safe_process_row(row)\n", "            if processed_row:\n", "                batch_rows.append(processed_row)\n", "        \n", "        if batch_rows:\n", "            output_file = output_dir / f\"completion_host_request_{timestamp}.batch{batch_num}.parquet\"\n", "            processed_df = pd.DataFrame(batch_rows).astype(schema)\n", "            processed_df.to_parquet(output_file)\n", "            logger.info(f\"Saved {len(batch_rows)} rows to {output_file}\")\n", "    \n", "except Exception as e:\n", "    logger.error(f\"Processing failed: {str(e)}\")\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_blob_query = \"\"\"\n", "SELECT \n", "    request_id,\n", "    time,\n", "    JSON_EXTRACT_SCALAR(request, \"$.blobs.baseline_checkpoint_id\") as checkpoint_id,\n", "    JSON_EXTRACT(request, \"$.blobs.added\") as added_blobs_json,\n", "    JSON_EXTRACT(request, \"$.blobs.deleted\") as deleted_blobs_json,\n", "    request as raw_json,\n", "    user_id,\n", "    user_agent\n", "FROM `system-services-dev.dev_tamuz.mercor_completions_host_request`\n", "ORDER BY time DESC\n", "LIMIT 5\n", "\"\"\"\n", "\n", "single_blob_df = client.query(single_blob_query).to_dataframe()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["single_blob_df_without_raw = single_blob_df.drop(columns=['raw_json'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_blob_df[['added_blobs_json', 'deleted_blobs_json', 'checkpoint_id']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_blob_df.raw_json[0]['blobs'].keys()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_blob_df_without_raw"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}
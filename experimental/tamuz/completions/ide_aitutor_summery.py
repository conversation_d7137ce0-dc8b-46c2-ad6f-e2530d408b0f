# %%
"""
IDE Comparison Analysis Script

This script analyzes completion differences between various IDE users (JetBrains and VSCode).
It processes completion event data from parquet files and generates visualizations and metrics
to understand usage patterns and performance across different IDEs.

Main components:
- Data loading and preprocessing
- Timeline analysis of completion durations
- Acceptance rate analysis
- Detailed completion flow analysis
"""

# Standard library imports
import json
from datetime import datetime, timedelta
import glob
import re

# Third-party imports
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from google.cloud import bigquery
import db_dtypes  # type: ignore
from sklearn.cluster import DBSCAN

# %%
# Helper Functions
def get_ide_from_user_agent(user_agent: str) -> str:
    """
    Determine the IDE type from the user agent string.
    
    Args:
        user_agent: String containing the user agent information
        
    Returns:
        String identifying the IDE ('vscode', 'rider', 'webstorm', 'intellij', or 'unknown')
    """
    if user_agent.startswith('Augment.vscode-augment/'):
        return 'vscode'
    elif user_agent.startswith('augment.intellij/'):
        if 'Rider' in user_agent:
            return 'rider'
        elif 'WebStorm' in user_agent:
            return 'webstorm'
        else:
            return 'intellij'  # fallback for other JetBrains IDEs
    return 'unknown'

# %%
# Constants and Configuration
EVENT_TYPES = [
    'completion_host_response',
    'client_completion_timeline',
    'completion_post_process',
    'completion_emit',
    'completion_host_request',
    'completion_resolution'
]

def transform_user_id(user_id: str) -> str:
    """
    Anonymize specific user IDs for privacy.
    
    Args:
        user_id: Original user ID
        
    Returns:
        Anonymized user ID
    """
    user_mapping = {
        '<EMAIL>': 'user_1',
        '<EMAIL>': 'user_1',
        '<EMAIL>': 'user_2',
        '<EMAIL>': 'user_2'
    }
    return user_mapping.get(user_id, user_id)

# %%
# Data Loading and Initial Setup
dfs = {}
data_dir = "/mnt/efs/augment/user/tamuz/completion_ide_mercor_data/latest_completion_events/"

# List available data files
existing_files = glob.glob(f'{data_dir}/*.parquet')
print("Found files:", existing_files)

# %%
# Load and Process Data
for event_type in EVENT_TYPES:
    batch_files = glob.glob(f"{data_dir}{event_type}_*.batch*.parquet")
    if batch_files:
        # Combine all batch files for this event type
        batch_dfs = [pd.read_parquet(f) for f in batch_files]
        dfs[event_type] = pd.concat(batch_dfs, ignore_index=True)
        
        # Add IDE identification if possible
        if 'user_agent' in dfs[event_type].columns:
            dfs[event_type]['ide'] = dfs[event_type]['user_agent'].apply(get_ide_from_user_agent)

        # Process user IDs
        if 'user_id' in dfs[event_type].columns:
            dfs[event_type]['user_id'] = dfs[event_type]['user_id'].apply(transform_user_id)
            dfs[event_type]['ide'] = dfs[event_type]['user_id'] + '_' + dfs[event_type]['ide']
            
        print(f"Loaded {event_type}: {len(dfs[event_type])} rows from {len(batch_files)} batch files")
    else:
        print(f"Warning: No data files found for {event_type}")

# %%
# Data Filtering and Initial Analysis
MIN_COMPLETIONS = 50  # Minimum number of completions required for analysis

for event_type, df in dfs.items():
    # Remove IDEs with insufficient data
    ide_counts = df['ide'].value_counts()
    valid_ides = ide_counts[ide_counts >= MIN_COMPLETIONS].index
    filtered_df = df[df['ide'].isin(valid_ides)]
    dfs[event_type] = filtered_df
    
    # Print summary statistics
    print(f"\n{event_type} DataFrame Info:")
    print(f"Shape: {filtered_df.shape}")
    print("Columns:", filtered_df.columns.tolist())
    print("\nIDE counts after filtering:")
    print(filtered_df['ide'].value_counts())

# %%
# Timing Analysis Functions
def extract_timing(row) -> pd.Series:
    """
    Extract timing information from a completion request.
    
    Args:
        row: DataFrame row containing request data
        
    Returns:
        Series containing start_time, end_time, and duration_ms
    """
    request_data = json.loads(row['request'])
    start_time = datetime.fromisoformat(request_data['api_start_time'].replace('Z', '+00:00'))
    end_time = datetime.fromisoformat(request_data['api_end_time'].replace('Z', '+00:00'))
    duration_ms = (end_time - start_time).total_seconds() * 1000
    return pd.Series({
        'start_time': start_time,
        'end_time': end_time,
        'duration_ms': duration_ms
    })

# %%
# Timeline Analysis
timeline_df = dfs['client_completion_timeline'].copy()
timeline_df = timeline_df.join(timeline_df.apply(extract_timing, axis=1))

# Calculate summary statistics by IDE
summary = (timeline_df
    .groupby(['ide'])
    .agg({
        'duration_ms': ['count', 'mean', 'median', 'std', 'min', 'max'],
        'request_id': 'count'
    })
    .round(2)
)

# Clean up column names
summary.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] for col in summary.columns]
summary = summary.rename(columns={'request_id_count': 'total_requests'})

print("\nCompletion Duration Summary by User and IDE:")
display(summary)

# %%
# Visualization
fig = px.box(timeline_df, 
             x='ide', 
             y='duration_ms',
             color='user_id',
             title='Completion Duration Distribution by IDE and User',
             labels={'duration_ms': 'Duration (ms)',
                    'ide': 'IDE',
                    'user_id': 'User'})
fig.show()

# %%
# Completion Resolution Analysis
print("\n=== Analyzing Completion Resolution ===")
if 'completion_resolution' in dfs:
    resolution_df = dfs['completion_resolution']
    resolution_df['accepted'] = resolution_df['request'].apply(
        lambda x: json.loads(x).get('accepted_idx', 0) != -1 if x != '{}' else True
    )
    resolution_df['time'] = pd.to_datetime(resolution_df['time'])
    
    # Calculate acceptance rates by IDE
    acceptance_by_ide = (resolution_df
        .groupby(['ide'])
        .agg({
            'request_id': 'count',
            'accepted': ['mean', 'sum']
        })
        .round(3)
    )
    
    print("\nAcceptance rates by IDE:")
    display(acceptance_by_ide)

# %%
# Complete Flow Analysis
if all(event_type in dfs for event_type in ['completion_host_request', 'completion_host_response', 'completion_resolution']):
    # Merge data from all stages of the completion flow
    request_df = dfs['completion_host_request']
    response_df = dfs['completion_host_response']
    resolution_df = dfs['completion_resolution']
    
    complete_flow_df = (request_df
        .merge(response_df, on='request_id', suffixes=('_request', '_response'))
        .merge(resolution_df, on='request_id')
    )
    
    # Convert timestamps for timing analysis
    complete_flow_df['request_time'] = pd.to_datetime(complete_flow_df['time_request'])
    complete_flow_df['response_time'] = pd.to_datetime(complete_flow_df['time_response'])
    complete_flow_df['resolution_time'] = pd.to_datetime(complete_flow_df['time'])
    
    # Calculate timing metrics
    complete_flow_df['generation_time_s'] = (
        complete_flow_df['response_time'] - complete_flow_df['request_time']
    ).dt.total_seconds()
    
    complete_flow_df['resolution_time_s'] = (
        complete_flow_df['resolution_time'] - complete_flow_df['response_time']
    ).dt.total_seconds()
    
    complete_flow_df['total_duration_s'] = (
        complete_flow_df['resolution_time'] - complete_flow_df['request_time']
    ).dt.total_seconds()
    
    # Group by IDE with detailed timing breakdown
    flow_summary = (complete_flow_df
        .groupby([ 'ide'])
        .agg({
            'generation_time_s': ['count', 'mean', 'median', 'std'],
            'resolution_time_s': ['mean', 'median', 'std'],
            'total_duration_s': ['mean', 'median', 'std'],
            'accepted': 'mean'
        })
        .round(2)
    )
    
    print("\nEnd-to-end completion flow summary by IDE:")
    display(flow_summary)
    
    # Additional statistics about timing distributions
    print("\nTiming distributions (in seconds):")
    timing_stats = complete_flow_df[['generation_time_s', 'resolution_time_s', 'total_duration_s']].describe()
    display(timing_stats)
    
    # Calculate percentage of time spent in each phase
    complete_flow_df['generation_percentage'] = (
        complete_flow_df['generation_time_s'] / complete_flow_df['total_duration_s'] * 100
    )
    complete_flow_df['resolution_percentage'] = (
        complete_flow_df['resolution_time_s'] / complete_flow_df['total_duration_s'] * 100
    )
    
    print("\nAverage percentage of time spent in each phase:")
    phase_percentages = complete_flow_df[['generation_percentage', 'resolution_percentage']].mean().round(2)
    display(phase_percentages)

# %%
def create_phase_df(df, remove_outliers=False):
    """Create a phase analysis DataFrame, optionally removing outliers."""
    if remove_outliers:
        df = df.copy()
        for col in ['generation_time_s', 'resolution_time_s']:
            Q1, Q3 = df[col].quantile([0.25, 0.75])
            IQR = Q3 - Q1
            lower_bound = Q1 - 3 * IQR
            upper_bound = Q3 + 3 * IQR
            
            n_outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)].shape[0]
            print(f"Removing {n_outliers} outliers from {col}")
            print(f"Bounds for {col}: [{lower_bound:.2f}, {upper_bound:.2f}]")
            
            df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]

    phases_df = pd.melt(
        df,
        id_vars=['ide', 'accepted', 'user_id'],
        value_vars=['generation_time_s', 'resolution_time_s'],
        var_name='phase',
        value_name='duration'
    )
    
    phases_df['phase'] = phases_df['phase'].map({
        'generation_time_s': 'Generation',
        'resolution_time_s': 'Resolution'
    })
    
    return phases_df, df

def create_visualizations(phases_df, df, outliers_removed=False):
    """Create and display all visualizations."""
    suffix = " (Outliers Removed)" if outliers_removed else ""
    
    # 1. Box plot
    fig1 = px.box(phases_df, 
                  x='ide', y='duration', color='phase', facet_col='accepted',
                  title=f'Completion Duration by Phase, IDE and Acceptance{suffix}',
                  labels={'duration': 'Duration (s)', 'ide': 'IDE',
                         'accepted': 'Accepted', 'phase': 'Phase'})
    fig1.update_layout(height=500)
    fig1.show()

    # 2. Violin plot
    fig2 = px.violin(phases_df,
                     x='ide', y='duration', color='phase', box=True,
                     title=f'Duration Distribution by Phase and IDE{suffix}',
                     labels={'duration': 'Duration (s)', 'ide': 'IDE', 'phase': 'Phase'})
    fig2.update_layout(height=500)
    fig2.show()

    # 3. Histogram
    fig3 = px.histogram(phases_df,
                        x='duration', color='phase', facet_col='ide', nbins=200,
                        title=f'Duration Distribution Histogram by Phase and IDE{suffix}',
                        labels={'duration': 'Duration (s)', 'count': 'Frequency', 'phase': 'Phase'})
    fig3.update_layout(height=400)
    fig3.show()

    # 4. Scatter plot
    fig4 = px.scatter(df,
                      x='generation_time_s', y='resolution_time_s', color='ide',
                      facet_col='accepted', opacity=0.6,
                      title=f'Generation Time vs Resolution Time{suffix}',
                      labels={'generation_time_s': 'Generation Time (s)',
                             'resolution_time_s': 'Resolution Time (s)', 'ide': 'IDE'})
    fig4.update_layout(height=400)
    fig4.show()

    # 5. Bar plot
    avg_by_phase = phases_df.groupby(['ide', 'phase', 'accepted'])['duration'].agg(['mean', 'median']).reset_index()
    fig5 = px.bar(avg_by_phase,
                  x='ide', y='median', color='phase', barmode='group', facet_col='accepted',
                  title=f'Median Duration by Phase, IDE and Acceptance{suffix}',
                  labels={'median': 'Median Duration (s)', 'ide': 'IDE', 'phase': 'Phase'})
    fig5.update_layout(height=400)
    fig5.show()

# Generate visualizations with and without outliers
if 'complete_flow_df' in locals():
    # Original data
    phases_df, orig_df = create_phase_df(complete_flow_df)
    create_visualizations(phases_df, orig_df)
    
    # Data with outliers removed
    phases_df_clean, clean_df = create_phase_df(complete_flow_df, remove_outliers=True)
    create_visualizations(phases_df_clean, clean_df, outliers_removed=True)
# %%
if 'complete_flow_df' in locals():
    print("\nCompletion counts by IDE:")
    display(complete_flow_df['ide'].value_counts())
    
    print("\nAcceptance rates by IDE:")
    display(complete_flow_df.groupby('ide')['accepted'].mean().round(3))
    
    print("\nMedian completion times by IDE (ms):")
    display(complete_flow_df.groupby('ide')['total_duration_s'].median().round(2))

# %%
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def analyze_ide_patterns(df):
    """Analyze patterns and differences between IDEs, focusing on per-request metrics"""
    
    # Calculate per-request metrics for each IDE
    ide_stats = df.groupby('ide').agg({
        'request_id': 'count',  # For reference only
        'added_blob_count': ['mean', 'median', 'std', lambda x: x.quantile(0.95)],
        'deleted_blob_count': ['mean', 'median', 'std', lambda x: x.quantile(0.95)],
    }).round(2)
    
    # Flatten column names
    ide_stats.columns = ['total_requests', 
                        'blobs_added_per_request_mean', 'blobs_added_per_request_median', 
                        'blobs_added_per_request_std', 'blobs_added_per_request_p95',
                        'blobs_deleted_per_request_mean', 'blobs_deleted_per_request_median',
                        'blobs_deleted_per_request_std', 'blobs_deleted_per_request_p95']
    
    print("\n=== IDE Usage Patterns (per-request metrics) ===")
    print(ide_stats)
    
    # 1. Box plot of blobs per request by IDE
    fig1 = make_subplots(rows=2, cols=1,
                        subplot_titles=('Added Blobs per Request by IDE',
                                      'Deleted Blobs per Request by IDE'))
    
    fig1.add_trace(
        go.Box(x=df['ide'], y=df['added_blob_count'], 
               name='Added Blobs per Request',
               boxpoints='outliers',  # Show outliers
               showlegend=False),
        row=1, col=1
    )
    
    fig1.add_trace(
        go.Box(x=df['ide'], y=df['deleted_blob_count'], 
               name='Deleted Blobs per Request',
               boxpoints='outliers',  # Show outliers
               showlegend=False),
        row=2, col=1
    )
    
    fig1.update_layout(
        height=800, 
        title_text="Distribution of Blobs per Request by IDE",
        yaxis_title="Added Blobs per Request",
        yaxis2_title="Deleted Blobs per Request"
    )
    fig1.show()

    # 2. Violin plot for more detailed distribution visualization
    fig2 = go.Figure()
    for ide in df['ide'].unique():
        kde_data = df[df['ide'] == ide]['added_blob_count']
        fig2.add_trace(go.Violin(
            x=[ide] * len(kde_data),
            y=kde_data,
            name=ide,
            box_visible=True,
            meanline_visible=True
        ))
    
    fig2.update_layout(
        title='Distribution of Added Blobs per Request by IDE',
        xaxis_title='IDE',
        yaxis_title='Added Blobs per Request',
        showlegend=False
    )
    fig2.show()

    # 3. Calculate and display statistical significance
    from scipy import stats
    
    print("\n=== Statistical Comparison between IDEs ===")
    ides = sorted(df['ide'].unique())
    for i, ide1 in enumerate(ides):
        for ide2 in ides[i+1:]:
            stat, p_value = stats.mannwhitneyu(
                df[df['ide'] == ide1]['added_blob_count'],
                df[df['ide'] == ide2]['added_blob_count'],
                alternative='two-sided'
            )
            print(f"{ide1} vs {ide2}:")
            print(f"  p-value: {p_value:.4f}")
            print(f"  Significant difference: {'Yes' if p_value < 0.05 else 'No'}")

    return ide_stats

def load_completion_data(data_dir):
    """Load and combine all parquet files from the specified directory"""
    df = pd.concat(
        [pd.read_parquet(f) for f in Path(data_dir).glob("completion_host_request_*.parquet")],
        ignore_index=True
    )
    return df

# %%
analyze_ide_patterns(dfs['completion_host_request'])

# %%
# Visualize completion timeline
def visualize_completion_timeline(df):
    """Create a timeline visualization of completions by IDE and acceptance status"""
    if 'complete_flow_df' not in locals():
        print("No completion flow data available")
        return
        
    fig_timeline = px.scatter(
        df,
        x='time',
        y='ide',
        color='accepted',
        opacity=0.7,
        size_max=15,
        title='Completion Timeline by IDE',
        labels={
            'time': 'Time',
            'ide': 'IDE',
            'accepted': 'Accepted',
            'total_duration_s': 'Duration (s)'
        },
        height=600
    )

    fig_timeline.update_traces(marker=dict(symbol='circle'))
    fig_timeline.update_layout(
        xaxis_title="Time",
        yaxis_title="IDE",
        showlegend=True,
        yaxis={'categoryorder': 'category ascending'},
        hovermode='closest',
    )
    
    fig_timeline.show()

# %%
# Analyze event distribution across IDEs
def analyze_event_distribution(dfs):
    """Analyze and visualize the distribution of events across different IDEs"""
    event_counts = []

    # Collect event counts by IDE
    for event_type, df in dfs.items():
        if 'ide' not in df.columns and 'user_agent' in df.columns:
            df['ide'] = df['user_agent'].apply(get_ide_from_user_agent)
        
        if 'ide' in df.columns:
            counts = df['ide'].value_counts().reset_index()
            counts.columns = ['ide', 'count']
            counts['event_type'] = event_type
            event_counts.append(counts)

    event_counts_df = pd.concat(event_counts, ignore_index=True)

    # Create visualization
    fig = px.bar(
        event_counts_df,
        x='event_type',
        y='count',
        color='ide',
        barmode='group',
        title='Event Distribution by Type and IDE',
        labels={
            'event_type': 'Event Type',
            'count': 'Number of Events',
            'ide': 'IDE'
        },
        height=600
    )

    fig.update_layout(
        xaxis_tickangle=-45,
        xaxis_title="Event Type",
        yaxis_title="Count",
        showlegend=True,
        bargap=0.2,
        bargroupgap=0.1
    )

    fig.update_traces(
        texttemplate='%{y}',
        textposition='outside'
    )

    fig.show()

    # Display summary table
    print("\nSummary of Event Counts by IDE:")
    summary_table = event_counts_df.pivot(index='ide', columns='event_type', values='count')
    display(summary_table.fillna(0).astype(int))
    
    return event_counts_df

# %%
def analyze_acceptance_rates(dfs):
    """Analyze completion acceptance rates across different IDEs"""
    # Prepare data
    resolution_df = dfs['completion_resolution'].copy()
    response_df = dfs['completion_host_response'].copy()
    
    resolution_df['accepted'] = resolution_df['request'].apply(
        lambda x: json.loads(x).get('accepted_idx', 0) != -1 if x != '{}' else True
    )
    
    # Add IDE information
    for df in [resolution_df, response_df]:
        if 'ide' not in df.columns and 'user_agent' in df.columns:
            df['ide'] = df['user_agent'].apply(get_ide_from_user_agent)
    
    # Calculate metrics
    resolution_metrics = (resolution_df
        .groupby('ide')
        .agg({
            'request_id': 'count',
            'accepted': ['sum', 'mean']
        })
        .round(3)
    )
    
    resolution_metrics.columns = [
        'resolution_count',
        'accepted_count',
        'resolution_acceptance_rate'
    ]
    
    # Add response counts
    response_counts = response_df.groupby('ide').size().rename('response_count')
    combined_metrics = resolution_metrics.join(response_counts).fillna(0)
    
    # Calculate acceptance rates and confidence intervals
    combined_metrics['response_acceptance_rate'] = (
        combined_metrics['accepted_count'] / combined_metrics['response_count']
    ).round(3)
    
    z = 1.96  # 95% confidence level
    for rate_col in ['resolution_acceptance_rate', 'response_acceptance_rate']:
        combined_metrics[f'{rate_col}_ci'] = z * np.sqrt(
            (combined_metrics[rate_col] * (1 - combined_metrics[rate_col])) / 
            combined_metrics['resolution_count']
        ).round(3)
    
    # Visualize results
    metrics_for_plot = combined_metrics.reset_index()
    metrics_for_plot_long = pd.melt(
        metrics_for_plot,
        id_vars=['ide'],
        value_vars=['resolution_acceptance_rate', 'response_acceptance_rate'],
        var_name='metric_type',
        value_name='acceptance_rate'
    )

    metric_name_map = {
        'resolution_acceptance_rate': 'Acceptance Rate (of Resolved)',
        'response_acceptance_rate': 'Acceptance Rate (of All Shown)'
    }
    
    metrics_for_plot_long['metric_type'] = metrics_for_plot_long['metric_type'].map(metric_name_map)
    
    # Create visualization
    fig = create_acceptance_rate_plot(metrics_for_plot_long)
    fig.show()
    
    # Print statistics
    print("\nAcceptance Rate Statistics by IDE:")
    print("=" * 80)
    display(combined_metrics.round(3))
    
    print("\nRatio of Resolutions to Responses:")
    print("=" * 80)
    resolution_ratio = (combined_metrics['resolution_count'] / 
                       combined_metrics['response_count']).round(3)
    display(resolution_ratio)
    
    return combined_metrics

def create_acceptance_rate_plot(data):
    """Create a bar plot for acceptance rates"""
    fig = px.bar(
        data,
        x='ide',
        y='acceptance_rate',
        color='metric_type',
        barmode='group',
        title='Completion Acceptance Rates by IDE',
        labels={
            'acceptance_rate': 'Acceptance Rate',
            'ide': 'IDE',
            'metric_type': 'Metric Type'
        }
    )
    
    fig.update_traces(
        texttemplate='%{y:.1%}',
        textposition='outside'
    )
    
    fig.update_layout(
        yaxis_tickformat='.1%',
        yaxis_range=[0, 1],
        title_x=0.5,
        legend_title_text='Rate Type',
        margin=dict(t=120),
        height=600,
        title=dict(
            text='Completion Acceptance Rates by IDE<br>'
                 '<sup style="font-size:0.8em">Comparing rates based on resolved completions vs all shown completions</sup>',
            y=0.95,
            x=0.5,
            xanchor='center',
            yanchor='top'
        )
    )
    
    return fig

# %%
# Run analyses
if 'complete_flow_df' in locals():
    print("=== Running Timeline Analysis ===")
    visualize_completion_timeline(complete_flow_df)
    
    print("\n=== Analyzing Event Distribution ===")
    event_counts_df = analyze_event_distribution(dfs)
    
    print("\n=== Analyzing Acceptance Rates ===")
    combined_metrics = analyze_acceptance_rates(dfs)

# %%




{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-01 23:20:31\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    \"system-services-dev\", \"us-east5\", \"claude-3-5-sonnet@20240620\", 0, 1024 * 5\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are Augment, an AI code assistant developed by Augment Code. Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hmm i see. do you think it's a connection reset?\n", "not sure, let's ask augment. augment, what do you think? [(\"hey what's wrong\\nmaybe the proto3 default value is causing issues\\naugment, what do you think?\", \"it may be the proto3 default value, but that looks like it's set correctly!\")]\n"]}], "source": ["messages = [\n", "    (\"hey what's wrong\", 0),\n", "    (\"maybe the proto3 default value is causing issues\", 0),\n", "    (\"augment, what do you think?\", 0),\n", "    (\"it may be the proto3 default value, but that looks like it's set correctly!\", 1),\n", "    (\"hmm i see. do you think it's a connection reset?\", 0),\n", "    (\"not sure, let's ask augment. augment, what do you think?\", 0),\n", "]\n", "\n", "messages_squashed = []\n", "for message in messages:\n", "    if message[1] == 0:\n", "        if messages_squashed and messages_squashed[-1][1] == \"\":\n", "            messages_squashed[-1] = (messages_squashed[-1][0] + \"\\n\" + message[0], \"\")\n", "        else:\n", "            messages_squashed.append((message[0], \"\"))\n", "    else:\n", "        messages_squashed[-1] = (messages_squashed[-1][0], message[0])\n", "cur_message = messages_squashed[-1][0]\n", "messages_squashed = messages_squashed[:-1]\n", "print(cur_message, messages_squashed)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-01 23:56:16\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mgenerating response for 1 messages\u001b[0m\n", "I\n", " apolog\n", "ize for\n", " the confusion earlier\n", ". As\n", " an AI assistant,\n", " I don't have\n", " prior\n", " context\n", " about the\n", " specific issue\n", " you're facing\n", " or\n", " the code\n", " you're working with\n", ". To\n", " provide\n", " a more\n", " accurate\n", " an\n", "d helpful response, I\n", "'ll need more\n", " information\n", " about the problem\n", " you're encountering\n", ".\n", "\n", "\n", "Regarding proto\n", "3 default\n", " values, they\n", " can\n", " indee\n", "d sometimes\n", " cause issues.\n", " Here\n", " are a few points\n", " to consider:\n", "\n", "\n", "1\n", ". In\n", " proto3, fields\n", " have\n", " default\n", " values when\n", " they're\n", " not explicitly set:\n", "\n", "   - For\n", " numeric\n", " types\n", " (int32\n", ", float\n", ",\n", " etc.), the default\n", " is\n", " 0\n", "\n", "   - For strings\n", ", the default is\n", " an empty string\n", "\n", "   - For b\n", "ools\n", ", the default is\n", " false\n", "\n", "   - For en\n", "ums, the default\n", " is the\n", " first define\n", "d enum\n", " value\n", "\n", "   - For message\n", " fields\n", ", the default is\n", " null\n", "\n", "\n", "2. These\n", " default values are\n", " not\n", " serial\n", "ized on\n", " the wire to\n", " save\n", " space\n", ".\n", "\n", "\n", "3. When\n", " deser\n", "ializing, if\n", " a field is not\n", " present,\n", " it will\n", " be set to its\n", " default value.\n", "\n", "\n", "4. This can\n", " sometimes\n", " lead to amb\n", "iguity,\n", " as\n", " you\n", " can\n", "'t distinguish\n", " between an\n", " explicitly\n", " set default\n", " value\n", " and a fiel\n", "d that wasn\n", "'t set\n", " at all.\n", "\n", "\n", "To\n", " help\n", " diagn\n", "ose your\n", " specific\n", " issue, coul\n", "d you provide\n", " more\n", " details about:\n", "\n", "\n", "1\n", ". The specific\n", " problem\n", " you're encountering\n", "\n", "2. The relevant\n", " parts\n", " of your .\n", "proto file\n", "3\n", ". How\n", " you're using the\n", " generate\n", "d code\n", "4.\n", " Any error\n", " messages or unexpecte\n", "d behaviors\n", " you're seeing\n", "\n", "\n", "With\n", " this\n", " information\n", ", I can\n", " provide\n", " a\n", " more targeted an\n", "d helpful response to\n", " your\n", " problem\n", ".\n"]}], "source": ["res = ANTHROPIC_CLIENT.generate_response_stream(\n", "    messages_squashed, system_prompt, cur_message\n", ")\n", "for r in res:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["not sure, let's ask augment. augment, what do you think? [(\"hey what's wrong\", 'ack'), ('maybe the proto3 default value is causing issues', 'ack'), ('augment, what do you think?', \"it may be the proto3 default value, but that looks like it's set correctly!\"), (\"hmm i see. do you think it's a connection reset?\", 'ack')]\n"]}], "source": ["messages_interleaved = []\n", "for message in messages:\n", "    if message[1] == 0:\n", "        if messages_interleaved and messages_interleaved[-1][1] == \"\":\n", "            messages_interleaved[-1] = (messages_interleaved[-1][0], \"ack\")\n", "        messages_interleaved.append((message[0], \"\"))\n", "    else:\n", "        messages_interleaved[-1] = (messages_interleaved[-1][0], message[0])\n", "cur_message = messages_interleaved[-1][0]\n", "messages_interleaved = messages_interleaved[:-1]\n", "print(cur_message, messages_interleaved)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-01 23:59:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mgenerating response for 1 messages\u001b[0m\n", "I\n", " apolog\n", "ize for\n", " the confusion earlier\n", ". As\n", " an AI assistant,\n", " I don't have\n", " prior\n", " context\n", " about the\n", " specific issue\n", " you're facing\n", " or\n", " the code\n", " you're working with\n", ". To\n", " provide\n", " a more\n", " accurate\n", " an\n", "d helpful response, I\n", "'ll need more\n", " information\n", " about the problem\n", " you're encountering\n", ".\n", "\n", "\n", "Regarding proto\n", "3 default\n", " values, they\n", " can\n", " indee\n", "d sometimes\n", " cause issues.\n", " Here\n", " are a few points\n", " to consider:\n", "\n", "\n", "1\n", ". In\n", " proto3, fields\n", " have\n", " default\n", " values when\n", " they're\n", " not explicitly set:\n", "\n", "   - For\n", " numeric\n", " types\n", " (int32\n", ", float\n", ",\n", " etc.), the default\n", " is\n", " 0\n", "\n", "   - For strings\n", ", the default is\n", " an empty string\n", "\n", "   - For b\n", "ools\n", ", the default is\n", " false\n", "\n", "   - For en\n", "ums, the default\n", " is the\n", " first define\n", "d enum\n", " value\n", "\n", "   - For message\n", " fields\n", ", the default is\n", " null\n", "\n", "\n", "2. These\n", " default values are\n", " not\n", " serial\n", "ized on\n", " the wire to\n", " save\n", " space\n", ".\n", "\n", "\n", "3. When\n", " deser\n", "ializing, if\n", " a field is not\n", " present,\n", " it will\n", " be set to its\n", " default value.\n", "\n", "\n", "4. This can\n", " sometimes\n", " lead to amb\n", "iguity,\n", " as\n", " you\n", " can\n", "'t distinguish\n", " between an\n", " explicitly\n", " set default\n", " value\n", " and a fiel\n", "d that wasn\n", "'t set\n", " at all.\n", "\n", "\n", "To\n", " help\n", " diagn\n", "ose your\n", " specific\n", " issue, coul\n", "d you provide\n", " more\n", " details about:\n", "\n", "\n", "1\n", ". The specific\n", " problem\n", " you're encountering\n", "\n", "2. The relevant\n", " parts\n", " of your .\n", "proto file\n", "3\n", ". How\n", " you're using the\n", " generate\n", "d code\n", "4.\n", " Any error\n", " messages or unexpecte\n", "d behaviors\n", " you're seeing\n", "\n", "\n", "With\n", " this\n", " information\n", ", I can\n", " provide\n", " a\n", " more targeted an\n", "d helpful response to\n", " your\n", " problem\n", ".\n"]}], "source": ["res = ANTHROPIC_CLIENT.generate_response_stream(\n", "    messages_squashed, system_prompt, cur_message\n", ")\n", "for r in res:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["not sure, let's ask augment. augment, what do you think? [(\"hey what's wrong\", 'ack'), ('maybe the proto3 default value is causing issues', 'ack'), ('augment, what do you think?', 'ack'), (\"it may be the proto3 default value, but that looks like it's set correctly!\", 'ack'), (\"hmm i see. do you think it's a connection reset?\", 'ack')]\n", "\u001b[2m2024-10-01 23:48:41\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mgenerating response for 5 messages\u001b[0m\n", "I\n", " apolog\n", "ize for\n", " the confusion in\n", " my previous responses.\n", " It\n", " seems there\n", " was\n", " an issue with my\n", " communication\n", ".\n", " Let\n", " me try to\n", " address\n", " your concerns\n", " properly\n", ":\n", "\n", "\n", "Regarding\n", " the proto3 \n", "default value issue\n", ":\n", "\n", "\n", "You\n", "'re right to\n", " consider\n", " this\n", " as\n", " a potential cause\n", ". In\n", " proto\n", "3, fields\n", " have\n", " default\n", " values when\n", " they're\n", " not explicitly set.\n", " For\n", " primitive\n", " types,\n", " these\n", " are\n", " typically\n", " zero\n", " values\n", " (\n", "0 \n", "for numbers\n", ", empty\n", " string for strings,\n", " etc.).\n", " If\n", " you're seeing\n", " unexpecte\n", "d behavior, it's\n", " worth\n", " double-\n", "checking that the default\n", " values\n", " aren\n", "'t interf\n", "ering with your expecte\n", "d logic\n", ".\n", "\n", "\n", "Concerning\n", " the connection reset:\n", "\n", "\n", "A connection reset coul\n", "d indee\n", "d be another\n", " possible\n", " cause of\n", " issues\n", ",\n", " especially if you\n", "'re dealing\n", " with network\n", " communications\n", ". This\n", " often\n", " happens when one\n", " side of a\n", " connection\n", " ab\n", "ruptly closes it\n", ", which\n", " can lead to errors\n", " or unexpecte\n", "d behavior in\n", " the application\n", ".\n", "\n", "\n", "To\n", " properly\n", " diagn\n", "ose the\n", " issue, we\n", "'d need more\n", " information:\n", "\n", "\n", "1\n", ". What specific\n", " problem\n", " are you encountering\n", "?\n", "\n", "2. <PERSON>\n", " you share\n", " relevant\n", " code snippets or\n", " error\n", " messages?\n", "3\n", ". Are\n", " you working\n", " with a particular\n", " framework or library?\n", "\n", "4. Have\n", " you recently\n", " made any changes to\n", " your code\n", " or environment?\n", "\n", "\n", "With\n", " more details, I\n", " can provide\n", " a more targeted an\n", "d helpful\n", " response to\n", " your\n", " problem\n", ". Let\n", " me know if you\n", " have any specific\n", " questions or\n", " if you can\n", " provide more\n", " context about\n", " the\n", " issue\n", " you\n", "'re facing.\n"]}], "source": ["messages_interleaved_simple = [(message[0], \"ack\") for message in messages[:-1]]\n", "cur_message = messages[-1][0]\n", "print(cur_message, messages_interleaved_simple)\n", "\n", "res = ANTHROPIC_CLIENT.generate_response_stream(\n", "    messages_interleaved_simple, system_prompt, cur_message\n", ")\n", "for r in res:\n", "    print(r)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}
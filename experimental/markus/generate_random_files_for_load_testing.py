"""<PERSON>ript to generate many random files for load testing the repository indexer.

Example usage:

python generate_random_files_for_load_testing.py ~/augment/random_files --num_files 100 --seed 123

This triggers 100 files with random content to be generated and uploaded by the extension.

"""

import argparse
import random
from pathlib import Path


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("output_dir", type=Path)
    parser.add_argument("--num_files", type=int, default=1)
    parser.add_argument("--max_num_lines", type=int, default=500)
    parser.add_argument("--max_words_per_line", type=int, default=10)
    parser.add_argument("--seed", type=int)
    parser.add_argument("--file_extension", type=str, default="py")
    args = parser.parse_args()
    output_dir: Path = args.output_dir

    output_dir.mkdir(parents=False, exist_ok=True)
    file_extension: str = args.file_extension

    if args.seed:
        r = random.Random(args.seed)
    else:
        r = random.Random()
    for i in range(args.num_files):
        num_lines = r.randint(1, args.max_num_lines)
        with (output_dir / f"{i}.{file_extension}").open(
            mode="w", encoding="utf-8"
        ) as f:
            print("Writing", num_lines, "lines to", f.name)
            for _ in range(num_lines):
                words = [
                    "".join(r.choices("abcdefghijklmnopqrstuvwxyz", k=r.randint(1, 10)))
                    for _ in range(r.randint(1, args.max_words_per_line))
                ]
                f.write(" ".join(words) + "\n")


if __name__ == "__main__":
    main()

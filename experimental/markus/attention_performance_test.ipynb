{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "import time\n", "\n", "import timeit\n", "import math\n", "from typing import Callable, Dict, List\n", "import functools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dtype = torch.float16\n", "\n", "\n", "# tame randomness\n", "initial_seed = 42\n", "prng = torch.Generator(device='cpu')\n", "prng.manual_seed(initial_seed)\n", "\n", "# Ensure we are using cuda\n", "assert torch.cuda.is_available()\n", "assert torch.cuda.device_count() > 0\n", "for device_idx in range(torch.cuda.device_count()):\n", "    print(f\"device_idx={device_idx}: \", torch.cuda.get_device_name(0))\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "\n", "def memory_usage():\n", "    print('Memory allocated:', round(torch.cuda.memory_allocated(0) / 1024**3, 2), 'GB')\n", "    print('Memory cached:   ', round(torch.cuda.memory_reserved(0) / 1024**3, 2), 'GB')\n", "\n", "memory_usage()\n", "\n", "\n", "print('Initialized inputs', flush=True)\n", "\n", "def manual_attn(q, k, v, attn_mask=None, is_causal=False):\n", "    # attn_mask = torch.ones(L, S, dtype=torch.bool).tril(diagonal=0) if is_causal else attn_mask\n", "    # attn_mask = attn_mask.masked_fill(not attn_mask, -float('inf')) if attn_mask.dtype==torch.bool else attn_mask\n", "    attn_weight = torch.softmax((q @ k.transpose(-2, -1) / math.sqrt(q.size(-1))), dim=-1)\n", "    # attn_weight = torch.dropout(attn_weight, dropout_p)\n", "    return attn_weight @ v\n", "\n", "\n", "def fresh_qkv(batch, num_heads, seq_len, qkv_dim):\n", "    qkv_shape = (batch, num_heads, seq_len, qkv_dim)\n", "    qkv = torch.randn(generator=prng, dtype=dtype, size=qkv_shape)\n", "    return qkv.to(device)\n", "\n", "def timed(fn):\n", "    start = torch.cuda.Event(enable_timing=True)\n", "    end = torch.cuda.Event(enable_timing=True)\n", "    start.record()\n", "    result = fn()\n", "    end.record()\n", "    torch.cuda.synchronize()\n", "    return result, start.elapsed_time(end) / 1000\n", "\n", "\n", "def fn_synced(fn, *args, num_iter=10):\n", "        f = lambda: fn(*args)\n", "        # start = time.time()\n", "        times = []\n", "        try:\n", "            _ = f()  # compile\n", "            # torch.cuda.synchronize()\n", "            for _ in range(num_iter):\n", "                # r = fn(*args)\n", "                _, t = timed(f)\n", "                times.append(t)\n", "            # torch.cuda.synchronize()\n", "        except torch.cuda.OutOfMemoryError:\n", "            return \"OOM\"\n", "        except RuntimeError as e:\n", "            print(e)\n", "            return \"err\"\n", "        # total = time.time() - start\n", "        return sorted(times)[num_iter // 2]\n", "\n", "def measure_attn_fn(name: str, attn_function: Callable, batch: int, num_heads: int, seq_len: int, qkv_dim: int, inference: bool = False):\n", "    torch.cuda.empty_cache()\n", "    q_seq_len = 1 if inference else seq_len\n", "    if True:  # MHA\n", "        q = fresh_qkv(batch, num_heads, q_seq_len, qkv_dim)\n", "        k = fresh_qkv(batch, num_heads, seq_len, qkv_dim)\n", "        v = fresh_qkv(batch, num_heads, seq_len, qkv_dim)\n", "    else:  # GQA\n", "        q = fresh_qkv(batch, num_heads, q_seq_len, qkv_dim)\n", "        q = q.view(batch, num_heads // 8, 8, q_seq_len, qkv_dim)\n", "        k = fresh_qkv(batch, num_heads // 8, seq_len, qkv_dim)\n", "        v = fresh_qkv(batch, num_heads // 8, seq_len, qkv_dim)\n", "    avg_time = fn_synced(attn_function, q, k, v)\n", "    del q, k, v\n", "    return avg_time\n", "\n", "# batch_sizes = [1]  #, 8, 64]\n", "# num_heads_sizes = [1,]  # folded into batch\n", "# seq_lengths = [1, 128, 512, 2048, 8192, 2**15]\n", "# qkv_dim_sizes = [8, 32, 64, 128, 256]\n", "\n", "# batch_sizes = [1]  #, 8, 64]\n", "# batch_sizes_inference = [32]\n", "# num_heads_sizes = [32,]  # folded into batch\n", "# seq_lengths = [1, 128, 512, 2048, 4096, 8192]\n", "# qkv_dim_sizes = [128]\n", "\n", "# for attn speed in batched inference\n", "batch_sizes = [32]  # mimicks layers of 2B model\n", "batch_sizes_inference = None\n", "num_heads_sizes = [32,]  # folded into batch\n", "seq_lengths = [128, 256, 512]\n", "qkv_dim_sizes = [128]\n", "\n", "def time_attn(name: str, attn_fn: Callable, inference: bool = False):\n", "    batch_sizes_this = batch_sizes\n", "    if inference:\n", "        batch_sizes_this = batch_sizes_inference\n", "    timing_results = torch.zeros(size=(\n", "        len(batch_sizes_this), \n", "        len(num_heads_sizes), \n", "        len(seq_lengths), \n", "        len(qkv_dim_sizes)), dtype=torch.float)\n", "\n", "    \n", "    for b, batch_size in enumerate(batch_sizes_this):\n", "        for h, num_heads in enumerate(num_heads_sizes):\n", "            for l, seq_len in enumerate(seq_lengths):\n", "                for f, qkv_dim in enumerate(qkv_dim_sizes):\n", "                    print(name, ' / b h l f:', batch_size, num_heads, seq_len, qkv_dim)\n", "                    time_or_oom = measure_attn_fn(name, attn_fn, batch_size, num_heads, seq_len, qkv_dim, inference)\n", "                    print(time_or_oom)\n", "                    if isinstance(time_or_oom, str):\n", "                        if time_or_oom == \"OOM\":\n", "                            time_or_oom = math.inf\n", "                        if time_or_oom == \"err\":\n", "                            time_or_oom = math.nan\n", "                    # print('  memory', torch.cuda.memory_allocated(0) / 1024**3, 'cached', torch.cuda.memory)\n", "                    timing_results[b, h, l, f] = time_or_oom\n", "    return {name: timing_results}\n", "\n", "\n", "def flash(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=True,\n", "            enable_math=False,\n", "            enable_mem_efficient=False,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args)\n", "    \n", "\n", "def causal_flash(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=True,\n", "            enable_math=False,\n", "            enable_mem_efficient=False,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args, is_causal=True)\n", "\n", "\n", "def mem_efficient(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=False,\n", "            enable_math=False,\n", "            enable_mem_efficient=True,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args)\n", "\n", "\n", "def causal_mem_efficient(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=False,\n", "            enable_math=False,\n", "            enable_mem_efficient=True,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args, is_causal=True)\n", "\n", "\n", "def default(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=False,\n", "            enable_math=True,\n", "            enable_mem_efficient=False,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args)\n", "\n", "\n", "def default_causal(*args):\n", "    with torch.backends.cuda.sdp_kernel(\n", "            enable_flash=False,\n", "            enable_math=True,\n", "            enable_mem_efficient=False,\n", "            ):\n", "        return F.scaled_dot_product_attention(*args, is_causal=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Grouped query attention (or multiple queries per head) as in https://arxiv.org/abs/2305.13245.\"\"\"\n", "\n", "from typing import Optional\n", "\n", "import torch\n", "\n", "# Tuned for training with queries_per_head=8; long sequence lengths; likely\n", "# a good baseline for other sizes.\n", "Q_CHUNK_LEN = 1024\n", "\n", "# TODO: memory-efficient version\n", "# TODO: common prefix for key and value\n", "# TODO: use sub-batches to limit memory usage\n", "\n", "_SOFTMAX_DTYPE_FOR_BFLOAT16 = torch.float16\n", "\n", "\n", "def causal_mask(q_len: int, kv_len: int, device=\"cuda\"):\n", "    q_idxs = torch.arange(q_len, device=device).view(1, 1, q_len, 1)\n", "    kv_idxs = torch.arange(kv_len, device=device).view(1, 1, 1, kv_len)\n", "    return q_idxs >= kv_idxs\n", "\n", "\n", "def window_len_mask(q_len: int, kv_len: int, window_len: int):\n", "    q_idxs = torch.arange(q_len, device=device).view(1, 1, q_len, 1)\n", "    kv_idxs = torch.arange(kv_len, device=device).view(1, 1, 1, kv_len)\n", "    return q_idxs - window_len < kv_idxs\n", "\n", "\n", "def gqa_attention(\n", "    query: torch.<PERSON><PERSON>,\n", "    key: <PERSON>.<PERSON><PERSON>,\n", "    value: torch.Ten<PERSON>,\n", "    mask: Optional[torch.Tensor] = None,\n", "    is_causal: bool = False,\n", "    window_len: Optional[int] = None,\n", ") -> torch.Tensor:\n", "    \"\"\"Grouped query attention (or multiple queries per head).\n", "\n", "    Similar to https://arxiv.org/abs/2305.13245.\n", "\n", "    Additional features:\n", "    - Saves work when causal attention masks are given (if is_causal is set).\n", "    - Saves work for local attention; see window_len.\n", "    - Supports float16, float32, probably bfloat16.\n", "\n", "    Args:\n", "        query: Tensor of shape (batch, heads, queries_per_head, q_len, qk_dim)\n", "        key: Tensor of shape (batch, heads, kv_len, qk_dim)\n", "        value: Tensor of shape (batch, heads, kv_len, value_dim)\n", "        mask: Tensor of shape (batch, heads, q_len, kv_len) of type bool.\n", "            `True` indicates that the entry is kept (same semantics as in\n", "            F.scaled_dot_product_attention and Google implementations).\n", "        is_causal: flag indicating that the given mask is causal.\n", "        window_len: an int indicating window length encoded in the given mask.\n", "\n", "    Returns:\n", "        Tensor of shape (batch, heads, queries_per_head, q_len, value_dim)\n", "    \"\"\"\n", "    device, dtype = query.device, query.dtype\n", "    batch, heads, queries_per_head, q_len, qk_dim = query.size()\n", "    kv_len, value_dim = value.size()[-2:]\n", "    if is_causal and q_len != kv_len:\n", "        raise NotImplementedError(\n", "            \"causal attention that is not self-attention needs an offset\"\n", "        )\n", "    assert key.size()[:-1] == value.size()[:-1]\n", "    assert key.size()[-1] == qk_dim\n", "    assert window_len is None or window_len > 0\n", "    scaled_query = query * (qk_dim**-0.5)\n", "    del query  # protect against accidental use\n", "    # flatten batch and head dim to enable easier splitting\n", "    scaled_query = scaled_query.view(batch * heads, queries_per_head, q_len, qk_dim)\n", "    key = key.view(batch * heads, kv_len, qk_dim)\n", "    value = value.view(batch * heads, kv_len, value_dim)\n", "    if mask is not None:\n", "        mask = mask.view(\n", "            -1, q_len, kv_len\n", "        )  # -1 because mask may not be broadcasted yet\n", "\n", "    efficient_causal_mask = is_causal and mask is None\n", "    assert not efficient_causal_mask or window_len is None, \"Efficient causal mask handling does not work yet with window_len.\"\n", "    q_chunk_len = min(Q_CHUNK_LEN, q_len)\n", "    \n", "    attn_result = torch.empty(\n", "        size=(batch * heads, queries_per_head, q_len, value_dim),\n", "        dtype=dtype,\n", "        device=device,\n", "    )\n", "\n", "    # tensor.split shares the underlying memory, so it is a no-op for `not is_causal`.\n", "    for query_chunk_idx, query_chunk in enumerate(\n", "        scaled_query.split(q_chunk_len, dim=-2)\n", "    ):\n", "        this_q_chunk_len = min(q_chunk_len, query_chunk.size()[-2])\n", "        assert query_chunk.size()[-2] == this_q_chunk_len, (query_chunk.size(), this_q_chunk_len)\n", "        q_start_idx = query_chunk_idx * q_chunk_len\n", "        q_end_idx = query_chunk_idx * q_chunk_len + this_q_chunk_len\n", "        kv_start_idx = 0\n", "        kv_end_idx = kv_len\n", "        chunk_mask = mask\n", "        if chunk_mask is not None:\n", "            chunk_mask = chunk_mask[:, q_start_idx:q_end_idx, :]\n", "        if is_causal:\n", "            kv_end_idx = q_end_idx\n", "        if window_len is not None:\n", "            assert mask is not None\n", "            # window len includes current position, so +1\n", "            kv_start_idx = max(0, q_start_idx - window_len + 1)\n", "\n", "        kv_chunk_len = kv_end_idx - kv_start_idx\n", "        assert kv_chunk_len >= 0\n", "\n", "        # warning: dynamic size; exploits causal mask\n", "        key_chunk = key[:, kv_start_idx:kv_end_idx, :]\n", "        value_chunk = value[:, kv_start_idx:kv_end_idx, :]\n", "        if chunk_mask is not None:\n", "            chunk_mask = chunk_mask[:, :, kv_start_idx:kv_end_idx]\n", "            # print(f\"{chunk_mask[0, , :]=}\")\n", "        # TODO: use sub-batches to limit memory usage\n", "        # sub_batch_size = q_len * kv_len * qk_dim // 10**10\n", "        # sub_batch_size = max(sub_batch_size, 1)\n", "        # sub_batch_size = min(sub_batch_size, batch)\n", "        # print(f\"{sub_batch_size=}\")\n", "\n", "        # n is queries_per_head\n", "        # q is q_seq_len\n", "        # k is seq_len\n", "        # f is head_dim\n", "        # TODO: reserve and reuse scores tensor across iterations, low priority\n", "        # chunk_scores = scores[:,:,:,:kv_chunk_len]\n", "        # torch.matmul(query_chunk, key_chunk.unsqueeze(1).transpose(-1, -2), out=chunk_scores)\n", "        chunk_scores = torch.einsum(\"...nqf,...kf->...nqk\", query_chunk, key_chunk)\n", "        if dtype == torch.bfloat16:\n", "            # softmax in bfloat16 often produces NaNs\n", "            chunk_scores = chunk_scores.to(_SOFTMAX_DTYPE_FOR_BFLOAT16)\n", "        assert chunk_scores.size() == (\n", "            batch * heads,\n", "            queries_per_head,\n", "            this_q_chunk_len,\n", "            kv_chunk_len,\n", "        ), (chunk_scores.size(), (batch * heads, queries_per_head, this_q_chunk_len, kv_chunk_len))\n", "        if chunk_mask is not None:\n", "            chunk_mask = chunk_mask.unsqueeze(1)  # add queries_per_head dim\n", "            neg_inf = torch.tensor([float(\"-inf\")], device=device).to(chunk_scores.dtype)\n", "            torch.where(chunk_mask, chunk_scores, neg_inf, out=chunk_scores)\n", "            # chunk_scores.masked_fill_(chunk_mask, -torch.inf)\n", "        if efficient_causal_mask:\n", "            assert chunk_mask is None\n", "            chunk_mask = causal_mask(q_chunk_len, q_chunk_len, device=device)\n", "            neg_inf = torch.tensor([float(\"-inf\")], device=device).to(chunk_scores.dtype)\n", "            scores_to_mask = chunk_scores[:, :, :, -this_q_chunk_len:]\n", "            torch.where(chunk_mask, scores_to_mask, neg_inf, out=scores_to_mask)\n", "\n", "        # For some reason, inplace softmax costs us performance\n", "        probs = torch.softmax(chunk_scores, dim=-1)\n", "        if dtype == torch.bfloat16:\n", "            probs = probs.to(torch.bfloat16)\n", "        assert chunk_scores.size() == (\n", "            batch * heads,\n", "            queries_per_head,\n", "            this_q_chunk_len,\n", "            kv_chunk_len,\n", "        )\n", "        assert value_chunk.size() == (batch * heads, kv_chunk_len, value_dim)\n", "        attn_result[:, :, q_start_idx:q_end_idx, :] = torch.einsum(\n", "            \"...nqk,...kf->...nqf\", probs, value_chunk\n", "        )\n", "    assert attn_result.dtype == dtype\n", "    return attn_result.view(batch, heads, queries_per_head, q_len, value_dim)\n", "\n", "\n", "mask = causal_mask(2**15, 2**15)  # precompute so that it isn't included in the timing\n", "\n", "\n", "def mha_attention_via_gqa(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    q = q.view(batch, heads, 1, q_len, qk_dim)\n", "    return gqa_attention(q, k, v)\n", "\n", "def mha_attention_via_gqa_causal(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    q = q.view(batch, heads, 1, q_len, qk_dim)\n", "    return gqa_attention(q, k, v, is_causal=True)\n", "\n", "def gqa_attention2(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    queries_per_head = 8\n", "    heads = heads // queries_per_head\n", "    q = q.view(batch, heads, queries_per_head, q_len, qk_dim).clone()\n", "\n", "    k = k[:, :heads, :, :].clone()\n", "    v = v[:, :heads, :, :].clone()\n", "    return gqa_attention(q, k, v)\n", "\n", "\n", "def multiquery(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    queries_per_head = heads\n", "    heads = heads // queries_per_head\n", "    q = q.view(batch, heads, queries_per_head, q_len, qk_dim)\n", "\n", "    k = k[:, :heads, :, :]\n", "    v = v[:, :heads, :, :]\n", "    return gqa_attention(q, k, v)\n", "\n", "\n", "def gqa_attention_causal(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    queries_per_head = 8\n", "    heads = heads // queries_per_head\n", "    q = q.view(batch, heads, queries_per_head, q_len, qk_dim)\n", "\n", "    k = k[:, :heads, :, :]\n", "    v = v[:, :heads, :, :]\n", "\n", "    q_len = q.size()[3]\n", "    kv_len = k.size()[2]\n", "\n", "    return gqa_attention(q, k, v, mask=mask[:, :, :q_len, :kv_len], is_causal=True)\n", "\n", "\n", "def gqa_attention_causal_pure(q, k, v):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    queries_per_head = 8\n", "    heads = heads // queries_per_head\n", "    q = q.view(batch, heads, queries_per_head, q_len, qk_dim)\n", "\n", "    k = k[:, :heads, :, :]\n", "    v = v[:, :heads, :, :]\n", "\n", "    return gqa_attention(q, k, v, mask=None, is_causal=True)\n", "\n", "\n", "def fake_gqa_causal(q, k, v):\n", "    batch, heads_orig, q_len, qk_dim = q.size()\n", "    queries_per_head = 8\n", "    heads = heads_orig // queries_per_head\n", "    # q = q.view(batch, heads, queries_per_head, q_len, qk_dim)\n", "\n", "    k = k[:, :heads, :, :]\n", "    v = v[:, :heads, :, :]\n", "\n", "    new_k = torch.repeat_interleave(k, repeats=queries_per_head, dim=1)\n", "    new_v = torch.repeat_interleave(v, repeats=queries_per_head, dim=1)\n", "    assert new_k.size() == (batch, heads_orig, q_len, qk_dim)\n", "    \n", "    return causal_flash(q, new_k, new_v)\n", "\n", "\n", "def flash_gqa_inference(q, k, v):\n", "    batch, heads, queries_per_head, q_len, qk_dim = q.size()\n", "    q = q.view(batch, heads, queries_per_head * q_len, qk_dim)\n", "    return flash(q, k, v)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# res = dict()\n", "# attn_name = \"fake_gqa_causal\"\n", "# attn_fn = fake_gqa_causal\n", "# # attn_fn_opt = torch.compile(gqa_attention2, mode=\"max-autotune\", dynamic=True)\n", "\n", "# res.update(time_attn(attn_name, attn_fn))\n", "# # res.update(time_attn(attn_name + \"_opt\", attn_fn_opt))\n", "\n", "\n", "res.update(time_attn(\"default_causal\", default))\n", "res.update(time_attn(\"causal_flash\", causal_flash))\n", "# res.update(time_attn(\"gqa_attention2\", gqa_attention2))\n", "res.update(time_attn(\"mha_attention_via_gqa_causal\", mha_attention_via_gqa_causal))\n", "\n", "res.update(time_attn(\"causal_mem_efficient\", causal_mem_efficient))\n", "\n", "\n", "# # res.update(time_attn(\"causal_flash\", causal_flash))\n", "# # res.update(time_attn(\"causal_mem_efficient\", causal_mem_efficient))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Present self-attention results with masked experiments\n", "for b_idx, b in enumerate(batch_sizes):\n", "    print('Batch size', b)\n", "    for l_idx, l in enumerate(seq_lengths):\n", "        print('Sequence length', l)\n", "        print('causal_flash:          ', res['causal_flash'][b_idx, 0, l_idx])\n", "        # print('causal_flash:          ', res['causal_flash'][b_idx, 0, l_idx])\n", "        print('causal_mem_efficient:  ', res['causal_mem_efficient'][b_idx, 0, l_idx])\n", "        # print('causal_mem_efficient:  ', res['causal_mem_efficient'][b_idx, 0, l_idx])\n", "        print('default_causal:        ', res['default_causal'][b_idx, 0, l_idx])\n", "        # # # print('manual:         ', res['manual'][b_idx, 0, l_idx])\n", "        # # print('gqa:            ', res['gqa'][b_idx, 0, l_idx])\n", "        # # print('gqa_einsum:     ', res['gqa_einsum'][b_idx, 0, l_idx])\n", "        # # print('gqa_einsum2:    ', res['gqa_einsum2'][b_idx, 0, l_idx])\n", "        # print('gqa_causal1:      ', res['gqa_causal1'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_False: ', res['gqa_causal_False'][b_idx, 0, l_idx])\n", "        # print('gqa_causal2:      ', res['gqa_causal2'][b_idx, 0, l_idx])  # += mask * -1e5\n", "        # print('gqa_causal_maskedfill: ', res['gqa_causal_maskedfill'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_maskedfill2:', res['gqa_causal_maskedfill2'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_scores:     ', res['gqa_causal_scores'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_scores2:    ', res['gqa_causal_scores2'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_scores3:    ', res['gqa_causal_scores3'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_nomask:     ', res['gqa_causal_nomask'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_rerun(failed):', res['gqa_causal_rerun'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_rerun2:     ', res['gqa_causal_rerun2'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_rerun_nomask:', res['gqa_causal_rerun_nomask'][b_idx, 0, l_idx])\n", "        # print('gqa_1024:              ', res['gqa_1024'][b_idx, 0, l_idx])\n", "        # print('gqa_768:               ', res['gqa_768'][b_idx, 0, l_idx])\n", "        # print('gqa_512:               ', res['gqa_512'][b_idx, 0, l_idx])\n", "        # print('gqa_1024_mask:         ', res['gqa_1024_mask'][b_idx, 0, l_idx])\n", "        print('mha_attention_via_gqa_causal: ', res['mha_attention_via_gqa_causal'][b_idx, 0, l_idx])\n", "\n", "        # print('gqa_causal_pure:       ', res['gqa_causal_pure'][b_idx, 0, l_idx])\n", "        # print('gqa_causal_pure_opt:   ', res['gqa_causal_pure_opt'][b_idx, 0, l_idx])\n", "        # print('default_causal:        ', res['default_causal'][b_idx, 0, l_idx])\n", "        # print('fake_gqa_causal:   ', res['fake_gqa_causal'][b_idx, 0, l_idx])\n", "        \n", "        print()\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Self-attention\n", "# res = dict()\n", "# res.update(time_attn('gqa', gqa_attention2))\n", "# res.update(time_attn('gqa_einsum', gqa_attention2))\n", "# res.update(time_attn('gqa_einsum2', gqa_attention2))\n", "# res.update(time_attn('flash', flash))\n", "# res.update(time_attn('mem_efficient', mem_efficient))\n", "# res.update(time_attn('default', default))\n", "# res.update(time_attn('manual', manual_attn))\n", "# res.update(time_attn('default_causal', default_causal))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# # Present self-attention results\n", "# for b_idx, b in enumerate(batch_sizes):\n", "#     print('Batch size', b)\n", "#     for l_idx, l in enumerate(seq_lengths):\n", "#         print('Sequence length', l)\n", "#         print('flash:          ', res['flash'][b_idx, 0, l_idx])\n", "#         # print('mem_efficient:  ', res['mem_efficient'][b_idx, 0, l_idx])\n", "#         print('default:        ', res['default'][b_idx, 0, l_idx])\n", "#         # print('manual:         ', res['manual'][b_idx, 0, l_idx])\n", "#         print('gqa:            ', res['gqa'][b_idx, 0, l_idx])\n", "#         print('gqa_einsum:     ', res['gqa_einsum'][b_idx, 0, l_idx])\n", "#         print('gqa_einsum2:    ', res['gqa_einsum2'][b_idx, 0, l_idx])\n", "#         print()\n", "#     print()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res_inference = dict()\n", "\n", "# res_inference.update(time_attn('gqa', gqa_attention, inference=True))\n", "res_inference.update(time_attn('flash_gqa_inference', flash_gqa_inference, inference=True))\n", "res_inference.update(time_attn('gqa_attention', gqa_attention, inference=True))\n", "res_inference.update(time_attn('gqa_opt', torch.compile(gqa_attention), inference=True))\n", "# res_inference.update(time_attn('multiquery', multiquery, inference=True))\n", "# res_inference.update(time_attn('multiquery_opt', torch.compile(multiquery), inference=True))\n", "\n", "\n", "\n", "# res_inference.update(time_attn('default', default, inference=True))\n", "# res_inference.update(time_attn('gqa_einsum2', gqa_attention2, inference=True))\n", "# res_inference.update(time_attn('mem_efficient', mem_efficient, inference=True))\n", "# res_inference.update(time_attn('manual', manual_attn, inference=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Present inference results\n", "for b_idx, b in enumerate(batch_sizes_inference):\n", "    print('Batch size', b)\n", "    for l_idx, l in enumerate(seq_lengths):\n", "        print('Sequence length', l)\n", "        print('flash:          ', res_inference['flash'][b_idx, 0, l_idx])\n", "        # print('default:        ', res_inference['default'][b_idx, 0, l_idx])\n", "        print('gqa:            ', res_inference['gqa_attention2'][b_idx, 0, l_idx])\n", "        print('gqa_opt:        ', res_inference['gqa_opt'][b_idx, 0, l_idx])\n", "        # print('gqa_opt:        ', res_inference['gqa_opt'][b_idx, 0, l_idx])\n", "        # print('gqa_einsum:     ', res_inference['gqa_einsum'][b_idx, 0, l_idx])\n", "        # print('gqa_einsum2:    ', res_inference['gqa_einsum2'][b_idx, 0, l_idx])\n", "        # print('mem_efficient:  ', res_inference['mem_efficient'][b_idx, 0, l_idx])\n", "        # print('manual:         ', res_inference['manual'][b_idx, 0, l_idx])\n", "        # print('multiquery:       ', res_inference['multiquery'][b_idx, 0, l_idx])\n", "        # print('multiquery_opt:   ', res_inference['multiquery_opt'][b_idx, 0, l_idx])\n", "        print()\n", "    print()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = torch.tensor([1, 2, 3])\n", "y = torch.repeat_interleave(x, 3, 0)\n", "x[0] = -1\n", "y"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
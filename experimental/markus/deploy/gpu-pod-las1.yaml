apiVersion: v1
kind: Pod
metadata:
  name: a100-test-markus
spec:
  containers:
  - name: a100
    image: "au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/dai_base:cuda-11.7-py-3.9-pytorch-2.0.1-gpt-neox-deepspeed-gpu-0.19.12"
    command: [ "sleep", "36000" ]

    resources:
      limits:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8
      requests:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8

    volumeMounts:
    - mountPath: /mnt/efs/augment
      name: augment-shared-mount

  volumes:
  - name: augment-shared-mount
    persistentVolumeClaim:
      claimName: aug-cw-las1


  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: gpu.nvidia.com/class
                operator: In
                values:
                  - A100_NVLINK_80GB
              - key: topology.kubernetes.io/region
                operator: In
                values:
                  - LAS1

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Reference implementation for grouped multiquery attention (or multiple queries per head).\"\"\"\n", "\n", "import torch\n", "import torch.nn.functional as F\n", "import time\n", "import math\n", "from typing import Dict, List, Optional"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dtype = torch.float16\n", "\n", "# tame randomness\n", "initial_seed = 42\n", "prng_cuda = torch.Generator(device='cuda')\n", "prng_cuda.manual_seed(initial_seed)\n", "\n", "# Ensure we are using cuda\n", "assert torch.cuda.is_available()\n", "assert torch.cuda.device_count() > 0\n", "for device_idx in range(torch.cuda.device_count()):\n", "    print(f\"device_idx={device_idx}: \", torch.cuda.get_device_name(0))\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "\n", "def memory_usage():\n", "    print('Memory allocated:', round(torch.cuda.memory_allocated(0) / 1024**3, 2), 'GB')\n", "    print('Memory cached:   ', round(torch.cuda.memory_reserved(0) / 1024**3, 2), 'GB')\n", "\n", "\n", "memory_usage()\n", "\n", "\n", "def fresh_kv(batch, num_heads, seq_len, qkv_dim):\n", "    kv_shape = (batch, num_heads, seq_len, qkv_dim)\n", "    kv = torch.randn(generator=prng_cuda, device=device, dtype=dtype, size=kv_shape)\n", "    return kv\n", "\n", "def fresh_q(batch, num_heads, queries_per_head, seq_len, qkv_dim):\n", "    q_shape = (batch, num_heads, queries_per_head, seq_len, qkv_dim)\n", "    q = torch.randn(generator=prng_cuda, device=device, dtype=dtype, size=q_shape)\n", "    return q"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b, h, qph, s, f = 1, 2, 3, 5, 7\n", "query = fresh_q(b, h, qph, s, f)\n", "key = fresh_kv(b, h, s, f)\n", "value = fresh_kv(b, h, s, f)\n", "# print(query, key, value)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def attention_gmq_base(query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:\n", "    \"\"\"Grouped multiquery attention.\n", "\n", "    Args:\n", "        query: Tensor of shape (batch, heads, queries_per_head, q_len, qk_dim)\n", "        key: Tensor of shape (batch, heads, kv_len, qk_dim)\n", "        value: Tensor of shape (batch, heads, kv_len, value_dim)\n", "        mask: Tensor of shape (batch, q_len, kv_len) of type bool. The mask\n", "            my have a different batch size size broadcastable to `batch`,\n", "            which can be used to attend over a common prefix.\n", "\n", "    Returns:\n", "        Tensor of shape (batch, heads, queries_per_head, q_len, value_dim)\n", "    \"\"\"\n", "    batch, heads, queries_per_head, q_len, qk_dim = query.size()\n", "    kv_len, value_dim = value.size()[-2:]\n", "    assert key.size()[:-1] == value.size()[:-1]\n", "    assert key.size()[-1] == qk_dim\n", "    scaled_query = query * (qk_dim ** -0.5)\n", "\n", "    # n is queries_per_head\n", "    # q is q_seq_len\n", "    # k is seq_len\n", "    # f is head_dim\n", "    scores = torch.einsum(\"...nqf,...kf->...nqk\", scaled_query, key)\n", "    if mask is not None:\n", "        mask_batch = mask.size()[0]\n", "        mask = mask.view(mask_batch, 1, 1, q_len, kv_len)\n", "        # TODO: measure performance of torch.where\n", "        neg_inf = torch.tensor([float(\"-inf\")], device=device).to(scores.dtype)\n", "        scores = torch.where(mask, scores, neg_inf)\n", "    probs = torch.softmax(scores, dim=-1)\n", "    attn_result = torch.einsum(\"...nqk,...kf->...nqf\", probs, value)\n", "    assert attn_result.size() == (batch, heads, queries_per_head, q_len, value_dim)\n", "    return attn_result\n", "\n", "# attention_gmq_base(query, key, value)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Q_CHUNK_LEN = 2048  # TODO: tune\n", "\n", "\n", "def attention_gmq_split(\n", "    query: torch.<PERSON><PERSON>,\n", "    key: <PERSON>.<PERSON><PERSON>,\n", "    value: torch.Ten<PERSON>,\n", "    mask: Optional[torch.Tensor] = None,\n", "    is_causal: bool = False,\n", "    window_len: Optional[int] = None,\n", ") -> torch.Tensor:\n", "    \"\"\"Grouped multiquery attention.\n", "\n", "    Args:\n", "        query: Tensor of shape (batch, heads, queries_per_head, q_len, qk_dim)\n", "        key: Tensor of shape (batch, heads, kv_len, qk_dim)\n", "        value: Tensor of shape (batch, heads, kv_len, value_dim)\n", "        mask: Tensor of shape (batch, heads, q_len, kv_len) of type bool. The mask\n", "            may have a different batch size size broadcastable to `batch`,\n", "            which can be used to attend over a common prefix.\n", "        is_causal: flag indicating that the given mask is causal.\n", "        window_len: an int indicating window length encoded in the given mask.\n", "\n", "    Returns:\n", "        Tensor of shape (batch, heads, queries_per_head, q_len, value_dim)\n", "    \"\"\"\n", "    device, dtype = query.device, query.dtype\n", "    if dtype == torch.bfloat16:\n", "        query = query.to(torch.float32)\n", "    batch, heads, queries_per_head, q_len, qk_dim = query.size()\n", "    kv_len, value_dim = value.size()[-2:]\n", "    assert key.size()[:-1] == value.size()[:-1]\n", "    assert key.size()[-1] == qk_dim\n", "    scaled_query = query * (qk_dim**-0.5)\n", "    del query  # protect against accidental use\n", "    # flatten batch and head dim to enable easier splitting\n", "    scaled_query = scaled_query.view(batch * heads, queries_per_head, q_len, qk_dim)\n", "    key = key.view(batch * heads, kv_len, qk_dim)\n", "    value = value.view(batch * heads, kv_len, value_dim)\n", "    mask = mask.view(batch * heads, q_len, kv_len)\n", "\n", "    q_chunk_len = q_len\n", "    if is_causal:\n", "        assert (\n", "            mask is not None\n", "        ), \"is_causal only indicates that the given mask is causal.\"\n", "        q_chunk_len = min(Q_CHUNK_LEN, q_len)\n", "\n", "    attn_result = torch.empty(\n", "        size=(batch * heads, queries_per_head, q_len, value_dim),\n", "        dtype=dtype,\n", "        device=device,\n", "    )\n", "\n", "    # tensor.split shares the underlying memory, so it is a no-op for `not is_causal`.\n", "    for query_chunk_idx, query_chunk in enumerate(\n", "        scaled_query.split(q_chunk_len, dim=0)\n", "    ):\n", "        q_start_idx = query_chunk_idx * q_chunk_len\n", "        q_end_idx = (query_chunk_idx + 1) * q_chunk_len\n", "        kv_start_idx = 0\n", "        kv_end_idx = kv_len\n", "        key_chunk = key\n", "        value_chunk = value\n", "        chunk_mask = mask\n", "        if chunk_mask is not None:\n", "            chunk_mask = chunk_mask[:, q_start_idx:q_end_idx, :]\n", "        if is_causal:\n", "            assert chunk_mask is not None\n", "            kv_end_idx = q_end_idx\n", "        if window_len is not None:\n", "            assert chunk_mask is not None\n", "            kv_start_idx = q_start_idx - window_len\n", "\n", "        key_chunk = key_chunk[\n", "            :, kv_start_idx:kv_end_idx\n", "        ]  # warning: dynamic size; exploits causal mask\n", "        value_chunk = value_chunk[:, kv_start_idx:kv_end_idx]\n", "        chunk_mask = chunk_mask[:, :, kv_start_idx:kv_end_idx]\n", "\n", "        # TODO: use sub-batches to limit memory usage\n", "        # sub_batch_size = q_len * kv_len * qk_dim // 10**10\n", "        # sub_batch_size = max(sub_batch_size, 1)\n", "        # sub_batch_size = min(sub_batch_size, batch)\n", "        # print(f\"{sub_batch_size=}\")\n", "\n", "        # n is queries_per_head\n", "        # q is q_seq_len\n", "        # k is seq_len\n", "        # f is head_dim\n", "        # TODO: reserve and reuse scores tensor across iterations, low priority\n", "        scores = torch.einsum(\"...nqf,...kf->...nqk\", query_chunk, key_chunk)\n", "        assert scores.size() == (batch * heads, queries_per_head, q_chunk_len, kv_len)\n", "        if chunk_mask is not None:\n", "            chunk_mask = chunk_mask.unsqueeze(1)  # add queries_per_head dim\n", "            # TODO: measure performance of torch.where\n", "            neg_inf = torch.tensor([float(\"-inf\")], device=device).to(scores.dtype)\n", "            torch.where(chunk_mask, scores, neg_inf, out=scores)\n", "        probs = torch.softmax(scores, dim=-1)\n", "        if dtype == torch.bfloat16:\n", "            probs = probs.to(torch.bfloat16)\n", "        assert probs.size() == (batch * heads, queries_per_head, q_chunk_len, kv_len)\n", "        assert value_chunk.size() == (batch * heads, kv_len, value_dim)\n", "        torch.matmul(\n", "            probs,\n", "            value_chunk.unsqueeze(1),\n", "            out=attn_result[:, :, q_start_idx:q_end_idx, kv_start_idx:kv_end_idx],\n", "        )\n", "    assert attn_result.dtype == dtype\n", "    return attn_result.view(batch, heads, queries_per_head, q_len, value_dim)\n", "\n", "\n", "# TODO: memory-efficient version\n", "# TODO: common prefix for key and value\n", "\n", "\n", "def vanilla_attention(q, k, v, mask=None, is_causal=False):\n", "    batch, heads, q_len, qk_dim = q.size()\n", "    kv_len, value_dim = value.size()[-2:]\n", "    scores = q @ k.transpose(-2, -1) / math.sqrt(q.size(-1))\n", "    if mask is not None:\n", "        if is_causal:\n", "            mask = torch.logical_and(mask, torch.arange(q_len) >= torch.arange(kv_len))\n", "        neg_inf = torch.tensor([float(\"-inf\")], device=device).to(scores.dtype)\n", "        scores = torch.where(mask, scores, neg_inf)\n", "    attn_weight = torch.softmax(scores, dim=-1)\n", "    return attn_weight @ v\n", "\n", "\n", "b, h, qph, s, f = 1, 2, 1, 8, 8\n", "key = fresh_kv(b, h, s, f)\n", "value = fresh_kv(b, h, s, f)\n", "query = fresh_q(b, h, qph, s, f)\n", "mask = torch.randint(0, 2, size=(1, h, s, s), device=device) == 0\n", "\n", "gmq_fn = attention_gmq_split\n", "\n", "vanilla_query = query.clone().detach().squeeze(2)\n", "v_a = vanilla_attention(vanilla_query, key, value, mask=mask)\n", "gmq_a = gmq_fn(query, key, value, mask=mask)\n", "torch.testing.assert_close(v_a, gmq_a.view(v_a.size()), rtol=1e-3, atol=1e-3)\n", "\n", "query = torch.cat([query, query], dim=2)\n", "gmq_a = gmq_fn(query, key, value, mask=mask)\n", "torch.testing.assert_close(v_a, gmq_a[:, :, 0].view(v_a.size()), rtol=1e-3, atol=1e-3)\n", "torch.testing.assert_close(v_a, gmq_a[:, :, 1].view(v_a.size()), rtol=1e-3, atol=1e-3)\n", "print(\"success\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x = torch.tensor([[1,2,3]])\n", "# # x.broadcast_to(3, 3)\n", "# # x.view(3, 3)\n", "# print(x)\n", "# print(x.size())\n", "# print(torch.stack([x, x], dim=0))\n", "mask = torch.randint(0, 2, size=(3, 3, 3))\n", "arrs = mask.split(2, dim=1)\n", "for a in arrs:\n", "    print(a.size())\n", "    a[0,0,0] = -100\n", "\n", "mask = mask.to(torch.float32)\n", "\n", "x = torch.softmax(mask, dim=-1)\n", "y = torch.empty_like(x)\n", "torch.softmax(mask, dim=-1, out=y)\n", "torch.testing.assert_close(x, y)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
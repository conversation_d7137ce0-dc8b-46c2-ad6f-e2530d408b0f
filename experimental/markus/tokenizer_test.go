package tokenizer

import (
	"testing"
)

func TestTrieGetToken(t *testing.T) {
	trie := NewTrieNode()
	trie.AddToken(&Token{Id: 0, bytes: []byte{0}})
	trie.AddToken(&Token{Id: 1, bytes: []byte{1}})

	if trie.GetToken([]byte{0}) == nil {
		t.<PERSON>rf("trie.GetToken([]byte{0}) == nil")
	}
	if trie.GetToken([]byte{1}) == nil {
		t.<PERSON><PERSON>("trie.GetToken([]byte{1}) == nil")
	}
	if trie.GetToken([]byte{2}) != nil {
		t.Errorf("trie.GetToken([]byte{2}) != nil")
	}
}

func TestTrieMaxMatch(t *testing.T) {
	trie := NewTrieNode()
	token1 := &Token{Id: 0, bytes: []byte{0}}
	ok1 := trie.AddToken(token1)
	if !ok1 {
		t.<PERSON><PERSON><PERSON>("trie.AddToken failed")
	}
	token2 := &Token{Id: 1, bytes: []byte{1}}
	ok2 := trie.AddToken(token2)
	if !ok2 {
		t.Errorf("trie.AddToken failed")
	}

	if trie.GetMaxMatch(token1.bytes) == nil {
		t.Errorf("trie.GetMaxMatch([]byte{0}) == nil")
	}
	if trie.GetMaxMatch(token2.bytes) == nil {
		t.Errorf("trie.GetMaxMatch([]byte{1}) == nil")
	}
	if trie.GetMaxMatch([]byte{2}) != nil {
		t.Errorf("trie.GetMaxMatch([]byte{2}) != nil")
	}
	if trie.GetMaxMatch([]byte{0, 1}) != token1 {
		t.Errorf("trie.GetMaxMatch([]byte{0, 1}) != token1")
	}
	if trie.GetMaxMatch([]byte{1, 0}) != token2 {
		t.Errorf("trie.GetMaxMatch([]byte{1, 0}) != token2")
	}
	if trie.GetMaxMatch([]byte{1, 1}) != token2 {
		t.Errorf("trie.GetMaxMatch([]byte{1, 1}) != token2")
	}
}

func TestTrieAddDeep(t *testing.T) {
	trie := NewTrieNode()
	token1 := &Token{Id: 0, bytes: []byte{0}}
	ok1 := trie.AddToken(token1)
	if !ok1 {
		t.Errorf("trie.AddToken failed")
	}
	token2 := &Token{Id: 1, bytes: []byte{1}}
	ok2 := trie.AddToken(token2)
	if !ok2 {
		t.Errorf("trie.AddToken failed")
	}
	token3 := &Token{Id: 2, bytes: []byte{0, 1}}
	ok3 := trie.AddToken(token3)
	if !ok3 {
		t.Errorf("trie.AddToken failed")
	}

	// GetToken
	if trie.GetToken([]byte{0}) != token1 {
		t.Errorf("trie.GetToken([]byte{0}) != token1")
	}
	if trie.GetToken([]byte{1}) != token2 {
		t.Errorf("trie.GetToken([]byte{1}) != token2")
	}
	if trie.GetToken([]byte{2}) != nil {
		t.Errorf("trie.GetToken([]byte{2}) != nil")
	}
	if trie.GetToken([]byte{0, 1}) != token3 {
		t.Errorf("trie.GetToken([]byte{0, 1}) != token3")
	}
	if trie.GetToken([]byte{1, 0}) != nil {
		// nil, because this is not an exact match of a token
		t.Errorf("trie.GetToken([]byte{1, 0}) != nil")
	}
	// GetMaxMatch
	if trie.GetMaxMatch([]byte{0}) != token1 {
		t.Errorf("trie.GetMaxMatch([]byte{0}) != token1")
	}
	if trie.GetMaxMatch([]byte{0, 1}) != token3 {
		t.Errorf("trie.GetMaxMatch([]byte{0, 1}) != token3")
	}
	if trie.GetMaxMatch([]byte{1, 0}) != token2 {
		t.Errorf("trie.GetMaxMatch([]byte{1, 0}) != token2")
	}
}

func TestTrieEmpty(t *testing.T) {
	trie := NewTrieNode()
	if trie.GetToken([]byte{0}) != nil {
		t.Errorf("trie.GetToken([]byte{0}) != nil")
	}
}

func TestTrieAddDuplicate(t *testing.T) {
	trie := NewTrieNode()
	token1 := &Token{Id: 0, bytes: []byte{0}}
	ok1 := trie.AddToken(token1)
	if !ok1 {
		t.Errorf("trie.AddToken failed")
	}
	token2 := &Token{Id: 1, bytes: []byte{0}}
	ok2 := trie.AddToken(token2)
	if ok2 {
		t.Errorf("trie.AddToken succeeded")
	}
}

func TestTrieGetEmpty(t *testing.T) {
	trie := NewTrieNode()
	if trie.GetToken([]byte{}) != nil {
		t.Errorf("trie.GetToken([]byte{}) != nil")
	}
	if trie.GetMaxMatch([]byte{}) != nil {
		t.Errorf("trie.GetMaxMatch([]byte{}) != nil")
	}
}

func TestTrieAddEmpty(t *testing.T) {
	trie := NewTrieNode()
	token1 := &Token{Id: 0, bytes: []byte{}}
	ok1 := trie.AddToken(token1)
	if ok1 {
		t.Errorf("trie.AddToken should not succeed for empty token")
	}
}

func TestVocab(t *testing.T) {
	vocab := EmptyVocab()
	_, ok1 := vocab.AddToken([]byte{0})
	if !ok1 {
		t.Errorf("vocab.AddToken [0] failed")
	}
	_, ok2 := vocab.AddToken([]byte{1})
	if !ok2 {
		t.Errorf("vocab.AddToken [1] failed")
	}
	token3, ok3 := vocab.AddToken([]byte{0, 1})
	if !ok3 {
		t.Errorf("vocab.AddToken [0, 1] failed")
	}
	if token3.bytes[0] != 0 || token3.bytes[1] != 1 {
		t.Errorf("vocab.AddToken [0, 1] failed")
	}
	_, ok4 := vocab.AddToken([]byte{0, 1})
	if ok4 {
		t.Errorf("vocab.AddToken for duplicate succeeded")
	}
}

func TestVocabTokenizeDefaultVocab(t *testing.T) {
	vocab := DefaultVocab()
	tokens := vocab.Tokenize("Hello, World!")
	if len(tokens) != 13 {
		t.Errorf("len(tokens) != 13")
	}
}

func TestVocabTokenizeTrainedVocab(t *testing.T) {
	vocab := DefaultVocab()
	vocab.AddToken([]byte(", "))
	vocab.AddToken([]byte("World"))

	tokens := vocab.Tokenize("Hello, World!")
	if len(tokens) != 8 {
		t.Errorf("len(tokens) != 8")
	}
}

func TestTokenizeOverlappingTokens(t *testing.T) {
	vocab := DefaultVocab()
	vocab.AddToken([]byte("Hell"))
	token2, _ := vocab.AddToken([]byte("Hello"))

	tokens := vocab.Tokenize("Hello, World!")
	if len(tokens) != 9 {
		t.Errorf("len(tokens) != 9")
	}
	if tokens[0] != token2.Id {
		t.Errorf("tokens[0] != token2")
	}
}

func TestTokenizeEmpty(t *testing.T) {
	vocab := DefaultVocab()
	tokens := vocab.Tokenize("")
	if len(tokens) != 0 {
		t.Errorf("len(tokens) != 0")
	}
}

func TestTokenizeNonAscii(t *testing.T) {
	vocab := DefaultVocab()
	heart_emoji := "❤️"
	if len(heart_emoji) != 6 {
		// Length 6 because it's a 3-byte UTF-8 character and a 3-byte variation selector.
		t.Errorf("len(heart_emoji) != 6: heart_emoji: %v", len(heart_emoji))
	}
	tokens := vocab.Tokenize(heart_emoji)
	if len(tokens) != 6 {
		t.Errorf("len(tokens) != 6: tokens: %v", tokens)
	}
}

func TestDetokenizeDefaultVocab(t *testing.T) {
	vocab := DefaultVocab()
	text := "Hello, World!"
	tokens := vocab.Tokenize(text)
	detokenized := vocab.Detokenize(tokens)
	if detokenized != text {
		t.Errorf("detokenized != text")
	}
}

func TestNextUnicode(t *testing.T) {
	r, size, rest, err := NextUnicode("Hello, World!")
	if err != nil {
		t.Errorf("Error: %v", err)
	}
	if r != 'H' {
		t.Errorf("r: got %v, want %v", r, 'H')
	}
	if size != 1 {
		t.Errorf("size: got %v, want %v", size, 1)
	}
	if rest != "ello, World!" {
		t.Errorf("rest: got %v, want %v", rest, "ello, World!")
	}
}

func TestNextUnicodeEmpty(t *testing.T) {
	_, _, _, err := NextUnicode("")
	if err == nil {
		t.Errorf("err: got %v, want %v", err, nil)
	}
}

func TestNextUnicodeInvalid(t *testing.T) {
	_, _, _, err := NextUnicode("\xffHello, World!")
	if err == nil {
		t.Errorf("err: got %v, want %v", err, nil)
	}
}

func TestNextUnicodeHeartEmoji(t *testing.T) {
	r, size, rest, err := NextUnicode("❤️Hello")
	if err != nil {
		t.Errorf("Error: %v", err)
	}
	if r != '❤' {
		t.Errorf("r: got %v, want %v", r, '❤')
	}
	if size != 3 {
		t.Errorf("size: got %v, want %v", size, 3)
	}
	if len(rest) != 8 {
		t.Errorf("rest: got %v", rest)
	}

	_, size2, rest2, err2 := NextUnicode(rest)
	if err2 != nil {
		t.Errorf("Error: %v", err2)
	}
	if size2 != 3 {
		// This is the variation selector
		t.Errorf("size2: got %v", size2)
	}
	if len(rest2) != 5 {
		t.Errorf("rest2: got %v", rest2)
	}
}

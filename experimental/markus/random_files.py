"""Generate a file with random words."""

import random


def generate_file(path: str):
    file1 = open(path, "a")
    PsudoRandomWords = [
        "Apple ",
        "Banana ",
        "Tree ",
        "<PERSON>le ",
        "Toothpick ",
        "Coffee ",
        "Done ",
        "Augment ",
        "is ",
        "a ",
        "tool ",
        "for ",
        "augmenting ",
        "code ",
        "\n",
    ]

    # Increase the range to make a bigger file
    for x in range(10000):
        # Change end range of the randint function below if you add more words
        index = random.randint(0, len(PsudoRandomWords) - 1)
        file1.write(PsudoRandomWords[index])


def generate_multiple_files(path: str, num_files: int):
    for x in range(num_files):
        generate_file(path + "/" + str(x) + ".py")


if __name__ == "__main__":
    generate_multiple_files("random_files", 3000)

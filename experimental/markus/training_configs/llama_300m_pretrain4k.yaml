determined:
  description: "Pre-training of a small llama3 model for speculative decoding purposes."
  workspace: Dev
  project: markus

augment:
  project_group: pretraining
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/llama3_300m_mqa.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  # We follow DeepSeekCoder paper for hyperparameters:
  # See Table 2 https://arxiv.org/pdf/2401.14196
  learning_rate: 5.3e-4
  # Total batch size is 1024
  batch_size: 8
  gradient_accumulation_steps: 2
  warmup_iters: 2000
  # See https://arxiv.org/pdf/2401.02954
  min_lr: 5.3e-5
  decay_lr: True
  # 320B tokens, one epoch over the dataset
  max_iters: 80_000
  eval_interval: 500
  block_size: 4096
  use_activation_checkpointing: False
  train_data_path: /mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/blogs-en/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/blogs-non-en/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/code_contest/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/devdocs/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/discourse/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/forem/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/gmane/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/gwene/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/jupyter_notebooks/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/kaggle_notebooks/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/stackexchange/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/technical-web/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/usenet/training;/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/wikimedia/training;/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-4k-no-fim/training
  eval_data_path: blogs_en@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/blogs-en/validation;blogs_non_en@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/blogs-non-en/validation;code_contest@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/code_contest/validation;devdocs@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/devdocs/validation;discourse@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/discourse/validation;forem@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/forem/validation;gmane@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/gmane/validation;gwene@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/gwene/validation;jupyter_notebooks@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/jupyter_notebooks/validation;kaggle_notebooks@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/kaggle_notebooks/validation;stackexchange@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/stackexchange/validation;technical_web@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/technical-web/validation;usenet@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/usenet/validation;wikimedia@/mnt/efs/spark-data/shared/nl-datasets/v1/dataset/llama3_instruct-4k/wikimedia/validation;code@/mnt/efs/spark-data/shared/aug-stack/v1/datasets/llama3_instruct-4k-no-fim/validation
  checkpoint_optimizer_state: True

  tokenizer_name: Llama3InstructTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: llama3_300m_pretrain_v3_char_emb_2
  wandb_project: markus-llama3-pretrain

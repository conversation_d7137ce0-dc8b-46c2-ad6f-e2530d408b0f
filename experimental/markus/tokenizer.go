// library components for training and running a tokenizer.

package tokenizer

import (
	"fmt"
	"io"
	"unicode/utf8"
)

// "strings"

type Token struct {
	Id    int
	bytes []byte
}

func (t Token) String() string {
	return string(t.bytes)
}

type Trie struct {
	Children map[byte]*Trie
	Token    *Token // Can be nil for non-leaf nodes
}

func NewTrieNode() *Trie {
	return &Trie{
		Children: make(map[byte]*Trie),
		Token:    nil,
	}
}

func (t *Trie) AddToken(token *Token) bool {
	if len(token.bytes) == 0 {
		return false
	}
	current := t
	for _, b := range token.bytes {
		child, ok := current.Children[b]
		if !ok {
			child = NewTrieNode()
			current.Children[b] = child
		}
		current = child
	}
	if current.Token != nil {
		return false // already exists!
	}
	current.Token = token
	return true
}

func (t *Trie) GetToken(text []byte) *Token {
	current := t
	for _, b := range text {
		child, ok := current.Children[b]
		if !ok {
			return nil
		}
		current = child
	}
	return current.Token
}

func (t *Trie) GetMaxMatch(text []byte) *Token {
	current := t
	lastToken := current.Token
	for _, b := range text {
		child, ok := current.Children[b]
		if !ok {
			break
		}
		current = child
		if current.Token != nil {
			lastToken = current.Token
		}
	}
	return lastToken
}

type Vocab struct {
	Tokens []Token
	Trie   *Trie
}

func (v *Vocab) AddToken(b []byte) (*Token, bool) {
	existingToken := v.Trie.GetToken(b)
	if existingToken != nil {
		return existingToken, false
	}
	id := len(v.Tokens)
	v.Tokens = append(v.Tokens, Token{Id: id, bytes: b})
	ok := v.Trie.AddToken(&v.Tokens[id])
	if !ok {
		panic("failed to add token; should not be possible as we just checked it's existence above")
	}
	return &v.Tokens[id], true
}

func (v *Vocab) AddPaddingToken() {
	v.AddToken([]byte{0})
}

func (vocab *Vocab) AddAllBytesToVocab() {
	for i := 0; i < 256; i++ {
		vocab.AddToken([]byte{byte(i)})
	}
}

func EmptyVocab() *Vocab {
	return &Vocab{
		Tokens: make([]Token, 0),
		Trie:   NewTrieNode(),
	}
}

func DefaultVocab() *Vocab {
	vocab := EmptyVocab()
	vocab.AddPaddingToken()
	vocab.AddAllBytesToVocab()
	return vocab
}

func (v *Vocab) Tokenize(text string) []int {
	btext := []byte(text)
	var tokens []int
	for len(btext) > 0 {
		token := v.Trie.GetMaxMatch(btext)
		if token == nil {
			panic("failed to find any matching token")
		}
		tokens = append(tokens, token.Id)
		btext = btext[len(token.bytes):]
	}
	return tokens
}

func (v *Vocab) Detokenize(tokens []int) string {
	var text []byte
	for _, id := range tokens {
		text = append(text, v.Tokens[id].bytes...)
	}
	return string(text)
}

func NextUnicode(str string) (rune, int, string, error) {
	if len(str) == 0 {
		return 0, 0, "", io.EOF
	}
	r, size := utf8.DecodeRuneInString(str)
	if r == utf8.RuneError {
		return r, size, "", fmt.Errorf("invalid UTF-8")
	}
	return r, size, str[size:], nil
}

"""A command-line interface to Augment.

This is a simple command-line interface to Augment.

Commands:

To transform text from stdin, run:
    python augment.py <instruction>

To transform a file, run:
    python augment.py --file <file> <instruction>

To transform a file around a specific line, run:
    python augment.py --file-line <file:line> <instruction>
Note that this will actually transform a region of lines around the given lines.

You can use --interactive to get an interactive mode that makes it easier to
experiment with different prompts.

Passing --debug will print debug information, including Request Ids.

See the Notion page for more documentation.
"""

import argparse
import concurrent.futures
import logging
import os
import re
import sys
from dataclasses import dataclass
from difflib import unified_diff
from pathlib import Path
from textwrap import dedent
from typing import Literal, Optional

from typing_extensions import assert_never

# pylint: disable=no-name-in-module
from base.augment_client.client import AugmentClient, BlobsJson, UploadContent
from base.blob_names.python.blob_names import get_blob_name

_URL = "https://staging-shard-0.api.augmentcode.com/"
"""URL of the augment API endpoint."""

_USER_AGENT = "cli/0.1.0"

MAX_EDIT_QUERY_CHARS = 3500
"""Maximum number of characters for edit queries (including instruction)."""

MAX_CHAT_QUERY_CHARS = 7500
"""Maximum number of characters for chat queries (including instruction)."""

_MAX_INSTRUCTION_CHARS = 2000
"""Maximum number of characters for the instruction."""

NUM_PARALLEL_REQUESTS = 2
"""Number of parallel requests to make to the service."""

_MAX_INDENTATION = 16
"""The maximum indentation to use when trying to split chunks."""


_QueryType = Literal["edit", "chat"]
"""Which API to call."""


@dataclass(frozen=True)
class OutputType:
    """Where to output the result."""

    location: Literal["none", "overwrite", "file", "directory"]
    """Where to output the result."""

    path: Optional[str]
    """The file or directory."""


_PrintType = Literal["full", "diff"]
"""Whether to output the full result or a diff."""


@dataclass(frozen=True)
class BlobInfo:
    """Information about a blob."""

    checkpoint_id: Optional[str]
    """The id of the blob checkpoint to use."""

    cur_blob: Optional[str]
    """The blob name of the current file."""

    blob_names: list[str]
    """Extra blob names (ideally not contained in the checkpoint)."""


def split_and_query(
    *,
    query_type: _QueryType,
    instruction: str,
    text: str,
    file: str,
    model_name: str,
    blob_info: BlobInfo,
    fake_model: bool,
) -> str:
    """Split the text into chunks and query the model.

    We can't fit large files into our context, so we split them into as large
    chunks as possible and send those.  Note that there is currently no overlap
    between chunks and no effort to break at places more meaningful than
    newlines.
    """
    if query_type == "edit":
        max_chars = MAX_EDIT_QUERY_CHARS
    elif query_type == "chat":
        max_chars = MAX_CHAT_QUERY_CHARS
    else:
        assert_never(query_type)
    results = []
    extra_newlines = []
    with concurrent.futures.ThreadPoolExecutor(
        max_workers=NUM_PARALLEL_REQUESTS
    ) as executor:
        prefix = ""
        suffix = text
        while suffix:
            cur, num_ending_newlines = find_chunk(suffix, max_chars - len(instruction))
            results.append(
                executor.submit(
                    query_model,
                    query_type=query_type,
                    instruction=instruction,
                    selected_text=cur,
                    file=file,
                    model_name=model_name,
                    prefix=prefix,
                    suffix=suffix[len(cur) :],
                    blob_info=blob_info,
                    fake_model=fake_model,
                )
            )
            extra_newlines.append(num_ending_newlines)
            prefix += cur + "\n" * num_ending_newlines
            suffix = suffix[len(cur) + num_ending_newlines :]
    result_strs = [result.result() for result in results]
    return "".join(
        result + "\n" * num_newlines
        for result, num_newlines in zip(result_strs, extra_newlines)
    )


def find_chunk(text: str, max_len: int) -> tuple[str, int]:
    """Find a semantically meaningful chunk of text with the given max length."""
    # If the text is short enough, just return it.
    if len(text) <= max_len:
        return text, 0
    text = text[:max_len]
    # Try to find a good place to break the text.
    # We first look for two empty lines, then one empty line, then no empty lines.
    # For each of those, we look for the least indented text.
    for num_newlines in (3, 2, 1):
        for num_spaces in range(_MAX_INDENTATION):
            pat = re.compile(
                r"\n" * num_newlines + r"[ \t]" * num_spaces + r"\S", re.DOTALL
            )
            all_matches = list(pat.finditer(text))
            if all_matches:
                last_match = all_matches[-1]
                # The model's result often contains an ending newline even if we don't have one.
                # We thus put a single newline into the query when possible.
                return text[: last_match.start() + 1], num_newlines - 1
    # We can't find anything good, so just break at the last newline.
    last_newline = text.rfind("\n")
    # If there are no newlines, use the whole thing.
    if last_newline == -1:
        return text, 0
    return text[: last_newline + 1], 0


def handle_file(
    *,
    query_type: _QueryType,
    file: str,
    instruction: str,
    model_name: str,
    blob_info: BlobInfo,
    output_type: OutputType,
    print_type: _PrintType,
    fake_model: bool,
):
    """Handle a file."""
    with Path(file).open("r") as f:
        text = f.read()

    result = split_and_query(
        query_type=query_type,
        instruction=instruction,
        text=text,
        file=file,
        model_name=model_name,
        blob_info=blob_info,
        fake_model=fake_model,
    )

    _print_output(
        print_type,
        result,
        text.splitlines(keepends=True),
        result.splitlines(keepends=True),
        file,
    )
    _write_output(output_type, file=file, result=result)


def handle_file_line(
    *,
    query_type: _QueryType,
    file: str,
    line: int,
    instruction: str,
    model_name: str,
    blob_info: BlobInfo,
    output_type: OutputType,
    print_type: _PrintType,
    fake_model: bool,
):
    """Handle a file at a specific line."""
    with Path(file).open("r") as f:
        all_lines = f.readlines()

    if line < 0:
        logging.error("Line number is too small")
        sys.exit(1)
    if line >= len(all_lines):
        logging.error("Line number is too large")
        sys.exit(1)

    # Extract lines around the given line until we hit empty lines.
    start = line
    while start > 0 and all_lines[start - 1].strip():
        start -= 1
    end = line + 1
    while end < len(all_lines) and all_lines[end].strip():
        end += 1
    selected_text = "".join(all_lines[start:end])

    result = query_model(
        query_type=query_type,
        instruction=instruction,
        selected_text=selected_text,
        file=file,
        model_name=model_name,
        prefix="".join(all_lines[:start]),
        suffix="".join(all_lines[end:]),
        blob_info=blob_info,
        fake_model=fake_model,
    )

    result_spliced = (
        all_lines[:start] + result.splitlines(keepends=True) + all_lines[end:]
    )
    _print_output(print_type, result, all_lines, result_spliced, file)
    _write_output(output_type, file=file, result="".join(result_spliced))


def _handle_blobs(args: argparse.Namespace, cur_file: Optional[str]) -> BlobInfo:
    """Upload and checkpoint blobs as needed."""

    blob_names = []
    cur_blob = None
    if cur_file or args.blob:
        blob_names = upload_blobs(
            ([cur_file] if cur_file else []) + (args.blob or []), args
        )
        if cur_file:
            cur_blob = blob_names[0]

    if args.checkpoint:
        return BlobInfo(
            checkpoint_id=args.checkpoint, cur_blob=cur_blob, blob_names=blob_names
        )

    # If the user passed in a workspace, checkpoint the blobs in that workspace.
    checkpoint_id = None
    workspace = args.workspace or args.compute_checkpoint
    if workspace:
        # To avoid slow indexing, avoid certain file types we are unlikely to want.
        # Ideally we'd reimplement {Blob,Memory}Manager and not need this.
        excluded_suffixes = (
            ".pyc",
            ".yml",
            ".yaml",
            ".json",
            ".jsonnet",
            ".jsonl",
            ".txt",
            ".md",
            ".map",
            ".png",
        )
        # Parse and ignore files in .augmentignore if it is present in the root.
        ignore_patterns = []
        ignore_file = Path(workspace) / ".augmentignore"
        if ignore_file.exists():
            with ignore_file.open("r") as f:
                ignore_patterns = f.readlines()
            ignore_patterns = [
                pattern.strip()
                for pattern in ignore_patterns
                if pattern.strip() and not pattern.strip().startswith("#")
            ]
            ignore_patterns = [
                re.compile(pattern) for pattern in ignore_patterns if pattern
            ]
        workspace_files = []
        for file in Path(workspace).glob("**/*"):
            if (
                file.is_file()
                and file.suffix not in excluded_suffixes
                and not any(
                    pattern.match(file.as_posix()) for pattern in ignore_patterns
                )
                # Avoid hidden files.
                and not any(map(lambda s: s.startswith("."), file.parts))
            ):
                workspace_files.append(str(file))
        if workspace_files:
            blob_names = []
            for file in workspace_files:
                blob_names.append(get_blob_name(file, Path(file).read_bytes()))
            blobs_json = BlobsJson(
                checkpoint_id=None, added_blobs=blob_names, deleted_blobs=[]
            )
            if not args.fake_model:
                client = AugmentClient(
                    url=_URL, token=_get_token(), user_agent=_USER_AGENT
                )
                checkpoint_id = client.checkpoint_blobs(blobs_json)
                logging.info(
                    "Checkpointed %s blobs and got: %s", len(blob_names), checkpoint_id
                )

    return BlobInfo(
        checkpoint_id=checkpoint_id, cur_blob=cur_blob, blob_names=blob_names
    )


def query_model(
    *,
    query_type: _QueryType,
    instruction: str,
    selected_text: str,
    model_name: str,
    file: str,
    prefix: str,
    suffix: str,
    blob_info: BlobInfo,
    fake_model: bool,
) -> str:
    """Query the model."""
    if fake_model:
        logging.info(f"Using fake model for {len(selected_text)} chars in file {file}")
        return selected_text

    client = AugmentClient(url=_URL, token=_get_token(), user_agent=_USER_AGENT)
    model_client = client.client_for_model(model_name)
    kwargs = {
        "path": file,
        "prefix": prefix,
        "suffix": suffix,
        "blob_names": [],
        "blob_name": blob_info.cur_blob,
        "blobs": BlobsJson(
            checkpoint_id=blob_info.checkpoint_id,
            added_blobs=blob_info.blob_names,
            deleted_blobs=[],
        ),
        "prefix_begin": 0,
        # We rely on the fact that we send the entire file contents to this call.
        "suffix_end": len(prefix) + len(selected_text) + len(suffix),
    }

    logging.info(f"Sending request to model server ({len(selected_text)} chars)")
    if query_type == "edit":
        kwargs["instruction"] = instruction
        kwargs["selected_text"] = selected_text
        resp = model_client.edit(**kwargs)
    elif query_type == "chat":
        kwargs["message"] = instruction
        kwargs["selected_code"] = selected_text
        resp = model_client.chat(**kwargs)
    else:
        assert_never(query_type)
    logging.info("Request Session Id: %s", client.request_session_id)
    logging.info("Request Id: %s", client.last_request_id)

    return resp.text


def upload_blobs(blob_paths: list[str], args: argparse.Namespace) -> list[str]:
    """Upload the given blobs and return their names."""
    blobs = []
    for blob_path in blob_paths:
        with Path(blob_path).open("r") as f:
            blobs.append(UploadContent(content=f.read(), path_name=blob_path))
    if args.fake_model:
        return blob_paths
    client = AugmentClient(url=_URL, token=_get_token(), user_agent=_USER_AGENT)
    logging.info("Uploading %d blob(s)", len(blobs))
    resp = client.batch_upload(blobs)
    for path_name, result in zip(blob_paths, resp):
        logging.info("Uploaded %s got %s", path_name, result)
    return resp


def main():
    parser = _create_parser()
    args = parser.parse_args()

    if args.debug:
        _setup_console_logging()

    print_type = (
        args.print_type
        if args.print_type
        else "diff"
        if args.file or args.file_line
        else "full"
    )

    cur_file = _get_file(args)

    if (
        args.output.location == "file" or args.output.location == "directory"
    ) and not cur_file:
        logging.error(
            "Must specify --file or --file-line when using --output=%s",
            args.output.location,
        )
        sys.exit(1)
    if (
        cur_file
        and args.output.location == "directory"
        and Path(cur_file).is_absolute()
    ):
        logging.error("The file must be a relative path when using --output=directory")
        sys.exit(1)

    if not args.compute_checkpoint and not args.instruction and not args.interactive:
        logging.error(
            "Must specify --instruction or --compute-checkpoint or --interactive"
        )
        sys.exit(1)
    if args.instruction and len(args.instruction) > _MAX_INSTRUCTION_CHARS:
        logging.error("Instruction is too long")
        sys.exit(1)

    blob_info = _handle_blobs(args, cur_file)

    _do_stuff(args, args.instruction, blob_info, print_type)

    while args.interactive:
        try:
            instruction = input(
                dedent(
                    """
                    Are you happy with this result?
                    Enter an instruction if not or press Ctrl-D to exit.

                    """
                )
            )
            if not instruction:
                break
        except EOFError:
            break
        print("")
        _do_stuff(args, instruction, blob_info, print_type)


def _do_stuff(
    args: argparse.Namespace,
    instruction: str,
    blob_info: BlobInfo,
    print_type: _PrintType,
):
    if args.compute_checkpoint:
        print(blob_info.checkpoint_id)
        return

    if not instruction:
        instruction = input("Enter an instruction: ")

    if args.no_input:
        result = query_model(
            query_type=args.type,
            instruction=instruction,
            selected_text="",
            model_name=args.model,
            file="",
            prefix="",
            suffix="",
            blob_info=blob_info,
            fake_model=args.fake_model,
        )
        print(result)
    elif args.file:
        handle_file(
            query_type=args.type,
            file=args.file,
            instruction=instruction,
            model_name=args.model,
            blob_info=blob_info,
            output_type=args.output,
            print_type=print_type,
            fake_model=args.fake_model,
        )
    elif args.file_line:
        file, line = _split_file_line(args.file_line)
        handle_file_line(
            query_type=args.type,
            file=file,
            line=line,
            instruction=instruction,
            model_name=args.model,
            blob_info=blob_info,
            output_type=args.output,
            print_type=print_type,
            fake_model=args.fake_model,
        )
    else:
        inpt = sys.stdin.read()
        result = split_and_query(
            query_type=args.type,
            instruction=instruction,
            text=inpt,
            file="",
            model_name=args.model,
            blob_info=blob_info,
            fake_model=args.fake_model,
        )
        _print_output(
            print_type,
            result,
            inpt.splitlines(keepends=True),
            result.splitlines(keepends=True),
            "stdin",
        )


def _create_parser() -> argparse.ArgumentParser:
    """Create the parser."""
    parser = argparse.ArgumentParser(
        description="Query Augment.  Reads from stdin by default unless overridden by an argument."
    )
    group = parser.add_mutually_exclusive_group()
    group.add_argument("instruction", nargs="?", help="The instruction to execute")
    group.add_argument(
        "--compute-checkpoint",
        metavar="workspace_root",
        help="""Computes a checkpoint id for all files in the workspace.
        Note that we will not actually upload the files.""",
    )
    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        "--no-input",
        action="store_true",
        help="Uses the empty string as input.  If not specified, reads from stdin",
    )
    group.add_argument(
        "--file",
        help="Edit a file.  If not specified, reads from stdin",
    )
    group.add_argument(
        "--file-line",
        help="""Edit a file at a specific line (and surrounding lines).
        The format is file:line""",
    )
    parser.add_argument(
        "--type",
        choices=["edit", "chat"],
        default="edit",
        help="""The type of query.  Defaults to edit.""",
    )
    parser.add_argument(
        "--blob",
        action="append",
        help="Path to a file to upload as a blob.  Can be specified multiple times.",
    )
    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        "--checkpoint",
        help="""The checkpoint id to use.""",
    )
    group.add_argument(
        "--workspace",
        help="""The root of the workspace.
        We will use all files in the workspace as blobs.
        However, we will not actually upload them and will just use their blob names.""",
    )
    parser.add_argument("--model", default="", help="The name of the model to use")
    parser.add_argument(
        "--output",
        type=_output_type,
        default="none",
        metavar="{none|overwrite|file=<file>|dir=<dir>}",
        help="""How to write the output.
        If \"none\", don't write anything.
        If \"overwrite\", overwrite the input file.
        If \"file\", write to a file.
        If \"dir\", write to a directory.  In this case the file must be relative not absolute.""",
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enter interactive mode.",
    )
    parser.add_argument(
        "--print-type",
        choices=["full", "diff"],
        help="""Whether to print the full output or a diff.
        This defaults to \"diff\" if a file is specified and \"full\" otherwise.""",
    )
    parser.add_argument(
        "--fake-model",
        action="store_true",
        help="""Use a fake model that just echoes the input and does not upload anything.
        This is useful for testing.""",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Print debug information to stderr.",
    )
    return parser


def _output_type(s: str) -> OutputType:
    """Convert a string to an output type."""
    if s == "none":
        return OutputType(location="none", path=None)
    elif s == "overwrite":
        return OutputType(location="overwrite", path=None)
    elif s.startswith("file="):
        return OutputType(location="file", path=s[5:])
    elif s.startswith("dir="):
        return OutputType(location="directory", path=s[4:])
    else:
        raise argparse.ArgumentTypeError(f"Invalid output type: {s}")


def _write_output(output_type: OutputType, *, file: str, result: str):
    """Write the output to a file as specified."""
    if output_type.location == "none":
        pass
    elif output_type.location == "overwrite":
        with Path(file).open("w") as f:
            f.write(result)
    elif output_type.location == "file" or output_type.location == "directory":
        assert output_type.path is not None
        out_path = Path(output_type.path)
        if output_type.location == "directory":
            out_path = out_path.joinpath(Path(file))
        out_path.parents[0].mkdir(parents=True, exist_ok=True)
        with out_path.open("w") as f:
            f.write(result)
    else:
        assert_never(output_type.location)


def _print_output(
    print_type: _PrintType,
    full_result: str,
    lines1: list[str],
    lines2: list[str],
    file: str,
):
    """Prints the output."""
    if print_type == "full":
        print(full_result)
    elif print_type == "diff":
        sys.stdout.writelines(unified_diff(lines1, lines2, fromfile=file, tofile=file))
    else:
        assert_never(print_type)


def _get_file(args: argparse.Namespace) -> Optional[str]:
    """Get the file from the args."""
    if args.file:
        return args.file
    elif args.file_line:
        return _split_file_line(args.file_line)[0]
    return None


def _split_file_line(file_line_str: str) -> tuple[str, int]:
    """Split a file:line string into a tuple."""
    file_line = file_line_str.split(":")
    if len(file_line) != 2:
        logging.error("Invalid format for --file-line")
        sys.exit(1)
    return file_line[0], int(file_line[1])


# Mostly copied from services/api_proxy/client/util.py.
def _get_token() -> str:
    """Get the API token from the file and aborts if it doesn't exist."""
    token_file = Path("~/.config/augment/api_token").expanduser()
    if not token_file.exists():
        logging.error("Must specify a token at %s", token_file)
        sys.exit(1)
    return token_file.read_text(encoding="utf-8").splitlines()[0].rstrip()


# Mostly copied from base/logging/console_logging.py.
def _setup_console_logging(debug: bool = False, add_timestamp: bool = True) -> None:
    """Setup a default format for logging to be used outside of containers."""
    if not debug:
        debug = "LOG_DEBUG" in os.environ
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        stream=sys.stderr,
        format="%(asctime)s %(levelname)s %(message)s"
        if add_timestamp
        else "%(message)s",
    )


if __name__ == "__main__":
    main()

#!/bin/bash

# This script helps rebase a branch on top of another branch.  It is useful
# when you have stacked PRs and want to rebase the child after the parent
# changes or is merged.  Using git rebase directly doesn't work in these
# cases since the base commit has changed, so this script basically uses
# cherry-pick.
#
# Warning: This script could easily have bugs, miss corner cases, and cause
# problems.  I use it and it has worked for me so far, and I try to keep the
# user in the loop and have them confirm everything, but you should ensure
# that your data are backed up before using it.
#
# USE AT YOUR OWN RISK.

if [ "$#" -lt "2" ]; then
    echo "Expected arguments: $0 <base_branch_name> <child_branch_name...>"
    exit 1
fi

while [ "$#" -gt 1 ]; do

    base_branch=$1
    child_branch=$2
    echo "Rebasing $child_branch on top of $base_branch"

    # Try to check that the branches we got are valid.
    if ! git show-ref --verify refs/heads/$base_branch >& /dev/null; then
        echo "$base_branch is not a valid branch"
        exit 1
    fi
    if ! git show-ref --verify refs/heads/$child_branch >& /dev/null; then
        echo "$child_branch is not a valid branch"
        exit 1
    fi

    # Search first for the topmost commit in the base branch in the child branch.
    # If we can't find it, we might have added a new commit to the base branch, so check the parent commit.
    # Only walk back 100 commits in the child branch.
    i=0
    while true; do
        # Don't look too far back.
        if [ "$i" = "10" ]; then
            echo "Unable to find the top $i commit(s) from $base_branch in the current branch.  Aborting."
            exit 1
        fi

        # The sed command here removes the (#<PR num>) that GitHub adds.
        # This lets us use this script after a PR has been merged.
        target_parent_commit=$(git log --format=%B -n 1 $base_branch~$i | head -n 1 | sed 's/ (#[0-9]\+)$//')

        # Search for the ith commit in the base branch in the child branch.
        # We will cherry pick everything after this commit.
        j=0
        while true; do
            # Don't look too far back.
            if [ "$j" = "100" ]; then
                echo "Unable to find the ${base_branch}~$i commit in the current branch.  Trying the next commit."
                break
            fi
            cur_commit=$(git log --format=%B -n 1 $child_branch~$j | head -n 1)
            if [ "$target_parent_commit" = "$cur_commit" ]; then
                num_commits=$j
                break 2
            fi
            ((j++))
        done
        ((i++))
    done

    # Print a summary of which commits we are going to cherry-pick.
    # Have the user confirm that this list looks correct.
    echo "Cherry-picking $num_commits commit(s):"
    for i in $(seq 1 $num_commits); do
        echo -n "  "
        git log --format=%B -n 1 $child_branch~$((i-1)) | head -n 1
    done
    echo "Press any key to continue (or \"Skip\" to skip this branch or Ctrl-C to abort)."
    read answer
    if [ "$answer" = "Skip" ]; then
        echo "Skipping this branch."
    else
        # Do the cherry-picks.
        git checkout -b tmp-$child_branch $base_branch
        for ((i=$num_commits-1;i>=0;i--)); do
            git cherry-pick $child_branch~$i
            if [ "$?" != "0" ]; then
                echo ""
                echo "Please resolve the merge-conflict and then re-run this script."
                exit 1
            fi
        done

        # Show the user the diff of the before and after branches so they can confirm
        # that everything looks good.
        child_tmpfile=$(mktemp /tmp/joelphite.XXXXXX)
        git diff $child_branch~$num_commits $child_branch > $child_tmpfile
        cur_tmpfile=$(mktemp /tmp/joelphite.XXXXXX)
        git diff tmp-$child_branch~$num_commits tmp-$child_branch > $cur_tmpfile
        echo ""
        echo "The diff of the before/after diffs is:"
        diff -u $child_tmpfile $cur_tmpfile
        rm $child_tmpfile $cur_tmpfile

        # Confirm with the user and then delete the old branch and rename the new branch.
        echo ""
        echo "Confirm that the current branch looks good and then press any key to continue (or Ctrl-C to abort):"
        read
        git branch -D $child_branch && git branch -m $child_branch

        # Confirm with the user and then force push the new branch.
        echo ""
        echo "Confirm that you want to force push the new branch to origin (or \"Skip\" to continue or Ctrl-C to abort):"
        read answer
        if [ "$answer" = "Skip" ]; then
            echo "Skipping push."
        else
            git push --force origin HEAD
        fi
    fi

    if [ "$#" -gt 2 ]; then
        echo ""
    fi
    shift
done

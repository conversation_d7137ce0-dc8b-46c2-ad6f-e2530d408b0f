{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.scripts.start_next_edit_combined_server import (\n", "    build_combined_system,\n", ")\n", "\n", "system = build_combined_system()\n", "system.edit_gen_system.retriever = None  # this disables retrieval for the gen system\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "from research.core.constants import AUGMENT_EFS_ROOT\n", "from research.model_server import launch_model_server\n", "\n", "# always reload the model_server module to avoid API endpoint errors.\n", "importlib.reload(launch_model_server)\n", "\n", "\n", "log_dir = AUGMENT_EFS_ROOT / \"user/jiayi\" / \"model_server_logs\"\n", "args = [\"--port\", \"5001\", \"--log_dir\", str(log_dir)]\n", "launch_model_server.main(\n", "    system,\n", "    input_args=args,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
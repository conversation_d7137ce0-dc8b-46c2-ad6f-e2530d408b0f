{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_config = [\n", "    (\"baseline plain\", \"/mnt/efs/augment/eval/jobs/jRoybPRv\"),\n", "    (\"tree\", \"/mnt/efs/augment/eval/jobs/SWASsvaw\"),\n", "]\n", "\n", "# Hack because parsing signatures is annoying.\n", "# old is show_full_method_signatures = False.  new is show_full_method_signatures and no_class_member_signatures enabled.\n", "signature_scheme = (\"new\", \"new\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "import html\n", "import json\n", "import pandas as pd\n", "import pickle\n", "\n", "from research.eval.harness.utils import read_jsonl_zst\n", "from IPython.core.display import display, HTML\n", "from collections import defaultdict\n", "from pathlib import Path\n", "from types import MethodType\n", "\n", "def read_artifacts(run_path):\n", "    hydra_result_records = None\n", "    matching_files = list(Path(run_path).glob(\"*_hydra.jsonl\"))\n", "    if len(matching_files) != 1:\n", "        print(\n", "            f\"Expected 1 Hydra jsonl file under {run_path}, found {len(matching_files)}\"\n", "        )\n", "    else:\n", "        hydra_results_path = matching_files[0]\n", "        with hydra_results_path.open(\"r\") as f:\n", "            hydra_result_records = [json.loads(x) for x in f]\n", "    \n", "    patch_records = None\n", "    matching_files = list(Path(run_path).glob(\"*_completed_patches.jsonl.zst\"))\n", "    if len(matching_files) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 completed patches jsonl file under {run_path}, found {len(matching_files)}\"\n", "        )\n", "    else:\n", "        patches_path = matching_files[0]\n", "        patch_records = read_jsonl_zst(patches_path)\n", "\n", "    if hydra_result_records and patch_records and len(patch_records) != len(hydra_result_records):\n", "        raise ValueError(\"Inconsistent records!\")\n", "    \n", "    return patch_records, hydra_result_records\n", "\n", "def unpack_hydra_patch_results(a):\n", "    run_metadata = {\n", "        \"prompt\": a[\"prompt\"],\n", "        \"generation\": a[\"generation\"],\n", "        \"completion\": a[\"generation\"],\n", "    }\n", "    \n", "    if \"patch\" in a and \"file_content\" in a[\"patch\"]:\n", "        run_metadata[\"file_content\"] = a[\"patch\"][\"file_content\"]\n", "    else:\n", "        run_metadata[\"file_content\"] = a[\"prefix\"] + a[\"suffix\"]\n", "    if \"ground_truth\" in a:\n", "        run_metadata[\"ground_truth\"] = a[\"ground_truth\"]\n", "    else:\n", "        run_metadata[\"ground_truth\"] = a[\"patch\"][\"file_content\"][a[\"patch\"][\"char_start\"]: a[\"patch\"][\"char_end\"]]\n", "    if \"metrics\" in a:\n", "        run_metadata[\"result\"] = \"PASSED\" if a[\"metrics\"][\"exact_match\"] else \"FAILED\"\n", "        run_metadata[\"result_strict\"] = \"PASSED\" if a[\"metrics\"][\"exact_match_strict\"] else \"FAILED\"\n", "        run_metadata[\"edit_similarity\"] = a[\"metrics\"][\"edit_similarity\"]\n", "        run_metadata[\"log_likelihood_groundtruth\"] = a[\"metrics\"][\"log_likelihood_groundtruth\"]\n", "\n", "    if \"retrieval_metadata\" in a and \"retriever_prompt\" in a[\"retrieval_metadata\"]:\n", "        run_metadata[\"retriever_prompt\"] = a[\"retrieval_metadata\"][\"retriever_prompt\"]\n", "    elif \"retrieval\" in a and \"retriever_prompt\" in a[\"retrieval\"]:\n", "        run_metadata[\"retriever_prompt\"] = a[\"retrieval\"][\"retriever_prompt\"]\n", "    elif \"artifacts\" in a and a[\"artifacts\"] and \"retriever_prompt\" in a[\"artifacts\"][0]:\n", "        run_metadata[\"retriever_prompt\"] = a[\"artifacts\"][0][\"retriever_prompt\"]\n", "    else:\n", "        run_metadata[\"retriever_prompt\"] = \"\"\n", "\n", "    if \"artifacts\" in a and a[\"artifacts\"] and \"metrics\" in a[\"artifacts\"][0] and \"skipped_usages\" in a[\"artifacts\"][0][\"metrics\"]:\n", "        run_metadata[\"skipped_usages\"] = a[\"artifacts\"][0][\"metrics\"][\"skipped_usages\"]\n", "    else:\n", "        run_metadata[\"skipped_usages\"] = []\n", "\n", "    return run_metadata\n", "\n", "def load(run_path):\n", "    api_task_path = Path(run_path) / \"000__RAGSystem_ApiCallTask\"\n", "    if api_task_path.exists():\n", "        return load_api_task(api_task_path)\n", "\n", "    patch_records, hydra_result_records = read_artifacts(run_path)\n", "\n", "    metadata = {}\n", "    for a in patch_records:\n", "        cur = unpack_hydra_patch_results(a)\n", "        patch_id = a[\"patch_id\"] if \"patch_id\" in a else a[\"patch\"][\"patch_id\"]\n", "        metadata[patch_id] = cur\n", "    if hydra_result_records:\n", "        for b in hydra_result_records:\n", "            metadata[b[\"patch_id\"]] = metadata[b[\"patch_id\"]] | {\n", "                \"result\": b[\"_extra\"][\"result\"],\n", "                \"run_output\": b[\"_extra\"][\"run_output\"],\n", "            }\n", "    return metadata\n", "\n", "def load_api_task(api_task_path):\n", "    hydra_patch_records = read_jsonl_zst(api_task_path / \"000_RAGSystem_ApiCallTaskcompleted_patches.jsonl.zst\")\n", "\n", "    api_result_files = sorted(e for e in (api_task_path / \"cache\").glob(\"*.json\"))\n", "    assert len(api_result_files) == len(hydra_patch_records)\n", "\n", "    metadata = {}\n", "    for hydra_patch_rec, api_result_file in zip(hydra_patch_records, api_result_files):\n", "        with open(api_result_file) as fh:\n", "            api_result_rec = json.load(fh)\n", "\n", "        patch_id = hydra_patch_rec['patch']['patch_id']\n", "        result_val = (\n", "            (\"metric@hydra-unit-test-pass\" in api_result_rec) and api_result_rec[\"metric@hydra-unit-test-pass\"]\n", "        )\n", "        metadata[patch_id] = unpack_hydra_patch_results(hydra_patch_rec) | {\n", "            \"ground_truth\": api_result_rec[\"inputs\"][\"target\"],\n", "            \"result\": \"PASSED\" if result_val else \"FAILED\",\n", "            \"run_output\": \"<not available>\",\n", "        }\n", "    return metadata\n", "\n", "\n", "def setup_html_diff(run1, run2) -> difflib.HtmlDiff:\n", "    diff_obj = difflib.HtmlDiff(wrapcolumn=100)\n", "    diff_obj._legend = \"\"\n", "\n", "    markers = [\n", "        '+' if res == \"PASSED\"\n", "        else (\n", "            '-' if res == \"FAILED\" else '^'\n", "        )\n", "        for res in (run1[\"result\"], run2[\"result\"])\n", "    ]\n", "\n", "    # Adjust colors\n", "    orig_convert_flags = diff_obj._convert_flags\n", "    def _convert_flags(self,fromlist,tolist,flaglist,context,numlines):\n", "        def _swap_parity(items):\n", "            result = [\n", "                item.replace('\\0+', '\\0*').replace('\\0-', f'\\0{markers[0]}').replace('\\0*', f'\\0{markers[1]}') if item else None\n", "                for item in items\n", "            ]\n", "            for yellow_token in (\"&lt;fim_prefix&gt;\", \"&lt;fim_suffix&gt;\"):\n", "                result = [item.replace('nowrap=\"nowrap\">', 'nowrap=\"nowrap\">\\0^') if yellow_token in item else item for item in result if item]\n", "            return result\n", "        fromlist = _swap_parity(fromlist)\n", "        tolist = _swap_parity(tolist)\n", "        return orig_convert_flags(fromlist, tolist, flaglist, context, numlines)\n", "\n", "    diff_obj._convert_flags = MethodType(_convert_flags, diff_obj)\n", "\n", "    display(HTML(f\"\"\"\n", "    <style type=\"text/css\">\n", "        {difflib.HtmlDiff()._styles}\n", "        td {{ text-align: left; }}\n", "        :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "    </style>\n", "    \"\"\"))\n", "\n", "    return diff_obj\n", "\n", "\n", "def diff_runs(results, patch_id, context: bool = False):\n", "    f\"diff_runs(results[0]['name'], results[1]['name'])\"\n", "    run1 = results[0]['data'][patch_id]\n", "    run2 = results[1]['data'][patch_id]\n", "    run_names = [res_item['name'] for res_item in results]\n", "    diff_obj = setup_html_diff(run1, run2)\n", "\n", "    display(HTML(f\"<h2>Patch {patch_id}</h2>\"))\n", "\n", "    display(HTML(\"<h3>Results</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        [run1[\"result\"]],\n", "        [run2[\"result\"]],\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "\n", "    if \"result_strict\" in run1 and \"result_strict\" in run2:\n", "        display(HTML(\"<h3>Results Strict</h3>\"))\n", "        display(HTML(diff_obj.make_table(\n", "            [run1[\"result_strict\"]],\n", "            [run2[\"result_strict\"]],\n", "            fromdesc=run_names[0],\n", "            todesc=run_names[1],\n", "        )))\n", "\n", "    if run1[\"result\"] == run2[\"result\"]:\n", "        # No difference in the two runs, so let's showing the details.\n", "        #return\n", "        pass\n", "    \n", "    display(HTML(\"<h3>Prompt</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        run1['prompt'].splitlines(),\n", "        run2['prompt'].splitlines(),\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "        context=context,\n", "    )))\n", "\n", "    if (run1['retriever_prompt'] or run2['retriever_prompt']) and run1['retriever_prompt'] != run2['retriever_prompt']:\n", "        display(HTML(\"<h3>Retriever Prompt</h3>\"))\n", "        display(HTML(diff_obj.make_table(\n", "            run1['retriever_prompt'].splitlines(),\n", "            run2['retriever_prompt'].splitlines(),\n", "            fromdesc=run_names[0],\n", "            todesc=run_names[1],\n", "            context=context,\n", "        )))\n", "\n", "    sig1 = get_signature(run1[\"prompt\"])\n", "    sig2 = get_signature(run2[\"prompt\"])\n", "    if sig1 and sig2:\n", "        sig1_details = _parse_signature(sig1, signature_scheme[0])\n", "        sig2_details = _parse_signature(sig2, signature_scheme[1])\n", "        display(HTML(\"<h3>Signature results</h3>\"))\n", "        if sig1 != sig2:\n", "            if _are_reordered(sig1_details, sig2_details):\n", "                display(HTML(f\"<p>Signatures have the same symbols but in a different order.\"))\n", "            else:\n", "                display(HTML(f\"<p>Signatures have the following unique symbols.\"))\n", "                (s1s, s2s) = get_uniq_symbols(sig1_details, sig2_details)\n", "                display(HTML(diff_obj.make_table(\n", "                    \"\\n\\n\".join([s.full_text for s in s1s]).splitlines(),\n", "                    \"\\n\\n\".join([s.full_text for s in s2s]).splitlines(),\n", "                    fromdesc=run_names[0],\n", "                    todesc=run_names[1],\n", "                )))\n", "                s1_uniq_words, s1_uniq_words_prime, s2_uniq_words, s2_uniq_words_prime = get_uniq_words(run1, run2, sig1_details, sig2_details)\n", "                display(HTML(\"Extra words:\"))\n", "                display(HTML(diff_obj.make_table(\n", "                    s1_uniq_words.union(s1_uniq_words_prime),\n", "                    s2_uniq_words.union(s2_uniq_words_prime),\n", "                    fromdesc=run_names[0],\n", "                    todesc=run_names[1],\n", "                )))\n", "        else:\n", "            display(HTML(f\"<p>Signatures match\"))\n", "        display(HTML(\"Skipped usages:\"))\n", "        skipped1 = run1[\"skipped_usages\"] if \"skipped_usages\" in run1 else []\n", "        skipped2 = run2[\"skipped_usages\"] if \"skipped_usages\" in run2 else []\n", "        # Old skipped usages are a list of names.  New are list of (name, sigs).\n", "        skipped1 = skipped1 if not skipped1 or isinstance(skipped1[0], str) else get_skipped_usage_summary_string(skipped1)\n", "        skipped2 = skipped2 if not skipped2 or isinstance(skipped2[0], str) else get_skipped_usage_summary_string(skipped2)\n", "        display(HTML(diff_obj.make_table(\n", "                    \"\\n\".join(skipped1).splitlines(),\n", "                    \"\\n\".join(skipped2).splitlines(),\n", "            fromdesc=run_names[0],\n", "            todesc=run_names[1],\n", "        )))\n", "\n", "    display(HTML(\"<h3>Completion</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        run1['completion'].splitlines(),\n", "        run2['completion'].splitlines(),\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "    \n", "    display(HTML(diff_obj.make_table(\n", "        [str(difflib.SequenceMatcher(None, run1['ground_truth'], run1['completion']).ratio())],\n", "        [str(difflib.SequenceMatcher(None, run1['ground_truth'], run2['completion']).ratio())],\n", "        fromdesc=run_names[0] + \" string difference against ground truth\",\n", "        todesc=run_names[1] + \" string difference against ground truth\",\n", "    )))\n", "    if \"edit_similarity\" in run1 and \"edit_similarity\" in run2:\n", "        display(HTML(diff_obj.make_table(\n", "            [str(run1['edit_similarity'])],\n", "            [str(run2['edit_similarity'])],\n", "            fromdesc=run_names[0] + \" edit similarity against ground truth\",\n", "            todesc=run_names[1] + \" edit similarity against ground truth\",\n", "        )))\n", "    if \"log_likelihood_groundtruth\" in run1 and \"log_likelihood_groundtruth\" in run2:\n", "        display(HTML(diff_obj.make_table(\n", "            [str(run1['log_likelihood_groundtruth'])],\n", "            [str(run2['log_likelihood_groundtruth'])],\n", "            fromdesc=run_names[0] + \" log likelihood against ground truth\",\n", "            todesc=run_names[1] + \" log likelihood against ground truth\",\n", "        )))\n", "    \n", "    display(HTML(\"<h3>Ground Truth</h3>\"))\n", "    display(HTML(\n", "        \"<table class='diff' summary='Ground Truth' style='border: 1px solid black;'><tr><td>\"\n", "        + \"</tr></td><tr><td>\".join(\n", "            line.replace(\" \", \"&nbsp;\").replace(\"<\", \"&lt;\").replace(\">\", \"&gt;\")\n", "            for line in run1['ground_truth'].splitlines()\n", "        )\n", "        + \"</td></tr></table>\"\n", "    ))\n", "\n", "    if 'run_output' in run1 and 'run_output' in run2:\n", "        display(HTML(\"<h3>Run Output</h3>\"))\n", "        display(HTML(diff_obj.make_table(\n", "            run1['run_output'].splitlines(),\n", "            run2['run_output'].splitlines(),\n", "            fromdesc=run_names[0],\n", "            todesc=run_names[1],\n", "            context=True,\n", "        )))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Signature code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Signature analysis\n", "\n", "import re\n", "from dataclasses import dataclass\n", "from typing import Iterable, Sequence, Tuple, Union\n", "\n", "\n", "def _get_different_runs(results) -> Iterable[str]:\n", "    for patch_id in reversed(list(results[0][\"data\"].keys())):\n", "        passfail = [res_item[\"data\"][patch_id][\"result\"] for res_item in results]\n", "        if passfail[0] != passfail[1]:\n", "            yield patch_id\n", "\n", "\n", "def get_signature(prompt: str) -> str:\n", "    sig_pat = re.compile(r\"<\\|sig-begin\\|>(.*)<\\|sig-end\\|>\", re.DOTALL)\n", "    m = sig_pat.search(prompt)\n", "    if m and m.group(1):\n", "        return m.group(1)\n", "    return \"\"\n", "\n", "\n", "@dataclass(frozen=True)\n", "class ModuleSignature:\n", "    full_text: str\n", "    start_index: int\n", "    path: str\n", "    classes: Sequence[str]\n", "    functions: Sequence[str]\n", "    variables: Sequence[str]\n", "\n", "    def symbols(self) -> set[str]:\n", "        return set(self.classes) | set(self.functions) | set(self.variables)\n", "\n", "\n", "@dataclass(frozen=True)\n", "class SymbolSignature:\n", "    full_text: str\n", "    start_index: int\n", "    path: str\n", "    text: str\n", "    attributes: Sequence[str]\n", "    methods: Sequence[str]\n", "    innerclasses: Sequence[str]\n", "\n", "    def symbols(self) -> set[str]:\n", "        return {self.text} | set(self.attributes) | set(self.methods) | set(self.innerclasses)\n", "\n", "\n", "def _parse_signature(sig: str, scheme: str) -> Sequence[Union[ModuleSignature, SymbolSignature]]:\n", "    module_pat = re.compile(r\"In file: (.*)\\s*(?:CLASS: (.*))?\\s*(?:FUNCTION: (.*))?\\s*(?:VARIABLE: (.*))?\")\n", "    symbol_pat = re.compile(r\"from: (.*)\\n\\s*([\\s\\S]*?)\\s*(?:attributes: (.*))?\\s*(?:methods: ([\\s\\S]*?))?\\s*(?:innerclasses: (.*))?\\n(?:\\n|$)\")\n", "    # TODO: This can still merge adjacent things.\n", "    symbol_pat_expanded = re.compile(r\"from: (.*)\\n\\s*(.*)\\s*(?:attributes: (.*))?\\s*(?:innerclasses: (.*))?\\s*([\\s\\S]*?)?\\n(?:(?=\\nIn file: |\\nfrom: )|$)\")\n", "    module_matches = re.finditer(module_pat, sig)\n", "    symbol_matches = re.finditer(symbol_pat, sig)\n", "    symbol_expanded_matches = re.finditer(symbol_pat_expanded, sig)\n", "    modules = [\n", "        ModuleSignature(module.group(0).strip(), module.start(), module.group(1), _split(module.group(2), \",\"), _split(module.group(3), \",\"), _split(module.group(4), \",\"))\n", "        for module in module_matches\n", "    ]\n", "    symbols = [\n", "        SymbolSignature(symbol.group(0).strip(), symbol.start(), symbol.group(1), symbol.group(2), _split(symbol.group(3), \";\"), _split(symbol.group(4), \";\"), _split(symbol.group(5), \";\"))\n", "        for symbol in symbol_matches\n", "    ]\n", "    symbols_expanded = [\n", "        SymbolSignature(symbol.group(0).strip(), symbol.start(), symbol.group(1), symbol.group(2), _split(symbol.group(3), \";\"), _split(symbol.group(5), \"\\n\\n\"), _split(symbol.group(4), \";\"))\n", "        for symbol in symbol_expanded_matches\n", "    ]\n", "    if scheme == \"old\":\n", "        all = modules + symbols + symbols_expanded\n", "    elif scheme == \"new\":\n", "        all = modules + symbols_expanded + symbols\n", "    else:\n", "        raise ValueError(f\"Unknown scheme: {scheme}\")\n", "    all_dict = {}\n", "    for s in all:\n", "        if not s.start_index in all_dict:\n", "            all_dict[s.start_index] = s\n", "    all = sorted(all_dict.values(), key=lambda x: x.start_index)\n", "    return all\n", "\n", "\n", "def _split(s: str, sep: str) -> Sequence[str]:\n", "    if not s:\n", "        return ()\n", "    return tuple([s.strip() for s in s.split(sep)])\n", "\n", "\n", "def _are_reordered(sig1: Sequence[Union[ModuleSignature, SymbolSignature]], sig2: Sequence[Union[ModuleSignature, SymbolSignature]]) -> bool:\n", "    return {s.full_text for s in sig1} == {s.full_text for s in sig2}\n", "\n", "\n", "def get_uniq_symbols(sig1: Sequence[Union[ModuleSignature, SymbolSignature]], sig2: Sequence[Union[ModuleSignature, SymbolSignature]]) -> Tuple[Sequence[Union[ModuleSignature, SymbolSignature]], Sequence[Union[ModuleSignature, SymbolSignature]]]:\n", "    s1_dict = {s.full_text: s for s in sig1}\n", "    s2_dict = {s.full_text: s for s in sig2}\n", "    return ([s1_dict[k] for k in s1_dict.keys() - s2_dict.keys()], [s2_dict[k] for k in s2_dict.keys() - s1_dict.keys()])\n", "\n", "\n", "def get_uniq_words(run1, run2, s1s: Sequence[Union[ModuleSignature, SymbolSignature]], s2s: Sequence[Union[ModuleSignature, SymbolSignature]]) -> Tuple[set[str], set[str], set[str], set[str]]:\n", "    s1_words = {word for s in s1s for s in s.symbols() for word in re.findall(r\"\\w+\", s)}\n", "    s2_words = {word for s in s2s for s in s.symbols() for word in re.findall(r\"\\w+\", s)}\n", "    r1_completion_words = {word for word in re.findall(r\"\\w+\", run1['completion'])}\n", "    r2_completion_words = {word for word in re.findall(r\"\\w+\", run2['completion'])}\n", "    s1_uniq_words = {s1word for s1word in s1_words.difference(s2_words) if s1word in r1_completion_words and not s1word in r2_completion_words}\n", "    s1_uniq_words_prime = {s1word for s1word in s1_words.difference(s2_words) if s1word in r2_completion_words and not s1word in r1_completion_words}\n", "    s2_uniq_words = {s2word for s2word in s2_words.difference(s1_words) if s2word in r2_completion_words and not s2word in r1_completion_words}\n", "    s2_uniq_words_prime = {s2word for s2word in s2_words.difference(s1_words) if s2word in r1_completion_words and not s2word in r2_completion_words}\n", "    return s1_uniq_words, s1_uniq_words_prime, s2_uniq_words, s2_uniq_words_prime\n", "\n", "\n", "def get_signature_details(results, patch_id):\n", "    run1 = results[0]['data'][patch_id]\n", "    run2 = results[1]['data'][patch_id]\n", "    run_names = [res_item['name'] for res_item in results]\n", "    diff_obj = setup_html_diff(run1, run2)\n", "\n", "    sig1 = get_signature(run1[\"prompt\"])\n", "    sig2 = get_signature(run2[\"prompt\"])\n", "    sig1_details = _parse_signature(sig1, signature_scheme[0])\n", "    sig2_details = _parse_signature(sig2, signature_scheme[1])\n", "\n", "    display(HTML(\"<h3>Signature results</h3>\"))\n", "    if sig1 != sig2:\n", "        if _are_reordered(sig1_details, sig2_details):\n", "            display(HTML(f\"<p>Signatures have the same symbols but in a different order.\"))\n", "        else:\n", "            display(HTML(f\"<p>Signatures have the following unique symbols.\"))\n", "            (s1s, s2s) = get_uniq_symbols(sig1_details, sig2_details)\n", "            display(HTML(diff_obj.make_table(\n", "                \"\\n\".join([s.full_text for s in s1s]).splitlines(),\n", "                \"\\n\".join([s.full_text for s in s2s]).splitlines(),\n", "                fromdesc=run_names[0],\n", "                todesc=run_names[1],\n", "            )))\n", "        display(HTML(diff_obj.make_table(\n", "            sig1.splitlines(),\n", "            sig2.splitlines(),\n", "            fromdesc=run_names[0],\n", "            todesc=run_names[1],\n", "        )))\n", "    else:\n", "        display(HTML(f\"<p>Signatures match\"))\n", "\n", "\n", "def get_overall_signature_info(results):\n", "    patch_ids = _get_different_runs(results)\n", "    equal = set[str]()\n", "    reordered = set[str]()\n", "    different_no_words = set[str]()\n", "    different_words = dict[str, set[str]]()\n", "    for patch_id in patch_ids:\n", "        run1 = results[0]['data'][patch_id]\n", "        run2 = results[1]['data'][patch_id]\n", "        sig1 = get_signature(run1[\"prompt\"])\n", "        sig2 = get_signature(run2[\"prompt\"])\n", "        sig1_details = _parse_signature(sig1, signature_scheme[0])\n", "        sig2_details = _parse_signature(sig2, signature_scheme[1])\n", "        if sig1 == sig2:\n", "            equal.add(patch_id)\n", "        elif _are_reordered(sig1_details, sig2_details):\n", "            reordered.add(patch_id)\n", "        else:\n", "            s1_uniq_words, s1_uniq_words_prime, s2_uniq_words, s2_uniq_words_prime = get_uniq_words(run1, run2, sig1_details, sig2_details)\n", "            if not s1_uniq_words and not s2_uniq_words and not s1_uniq_words_prime and not s2_uniq_words_prime:\n", "                different_no_words.add(patch_id)\n", "            else:\n", "                different_words[patch_id] = s1_uniq_words | s2_uniq_words | s1_uniq_words_prime | s2_uniq_words_prime\n", "    def get_pass_str(patches: set[str]):\n", "        first_pass = 0\n", "        second_pass = 0\n", "        for patch_id in patches:\n", "            run1 = results[0]['data'][patch_id]\n", "            run2 = results[1]['data'][patch_id]\n", "            if run1[\"result\"] == \"PASSED\":\n", "                first_pass += 1\n", "            if run2[\"result\"] == \"PASSED\":\n", "                second_pass += 1\n", "        return f\"{first_pass} vs {second_pass} passing\"\n", "    print(f\"Equal ({get_pass_str(equal)}): {len(equal)}: {', '.join(equal)}\")\n", "    print(f\"Reordered ({get_pass_str(reordered)}): {len(reordered)}: {', '.join(reordered)}\")\n", "    print(f\"Different no words ({get_pass_str(different_no_words)}): {len(different_no_words)}: {', '.join(different_no_words)}\")\n", "    print(f\"Different words ({get_pass_str(set(different_words.keys()))}): {len(different_words)}\")\n", "    for patch_id, words in different_words.items():\n", "        run1 = results[0]['data'][patch_id]\n", "        run2 = results[1]['data'][patch_id]\n", "        print(f\"  {patch_id} ({run1['result']}, {run2['result']}): {', '.join(words)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _get_skipped_usages_runs(results) -> Iterable[str]:\n", "    for patch_id in reversed(list(results[0][\"data\"].keys())):\n", "        skipped = [res_item[\"data\"][patch_id][\"skipped_usages\"] for res_item in results]\n", "        if skipped[0] or skipped[1]:\n", "            yield patch_id\n", "\n", "\n", "def get_skipped_usages_info(results):\n", "    for patch_id in _get_skipped_usages_runs(results):\n", "        print(f\"[{patch_id}]  {results[0]['data'][patch_id]['skipped_usages']} {results[1]['data'][patch_id]['skipped_usages']}\")\n", "\n", "\n", "def get_different_skipped_usages_info(results):\n", "    first_passing = 0\n", "    second_passing = 0\n", "    for patch_id in set(_get_skipped_usages_runs(results)).intersection(_get_different_runs(results)):\n", "        run1 = results[0]['data'][patch_id]\n", "        run2 = results[1]['data'][patch_id]\n", "        result1 = run1[\"result\"]\n", "        result2 = run2[\"result\"]\n", "        print(f\"[{patch_id}] ({result1} vs {result2})  {get_skipped_usage_summary_string(run1['skipped_usages'])} vs {get_skipped_usage_summary_string(run2['skipped_usages'])}\")\n", "        if result1 == \"PASSED\":\n", "            first_passing += 1\n", "        if result2 == \"PASSED\":\n", "            second_passing += 1\n", "    print(f\"{first_passing} vs {second_passing} passing\")\n", "\n", "\n", "def get_skipped_usage_summary_string(skipped: Sequence[tuple[str, Sequence[str]]]) -> Sequence[str]:\n", "    return list(map(lambda x: f\"{x[0]} ({sum(map(lambda x: len(x), x[1]))} chars)\", skipped))\n", "\n", "# Gets a list of patches sorted the ratio of number of characters in the second signature to number of characters in the first signature.\n", "# So patches with empty second signatures are first.\n", "def get_bad_signature_usages_info(results):\n", "    bad_sigs = []\n", "    for patch_id in reversed(list(results[0][\"data\"].keys())):\n", "        if patch_id.startswith(\"project_cc_csharp\"):\n", "            continue\n", "        run1 = results[0]['data'][patch_id]\n", "        run2 = results[1]['data'][patch_id]\n", "        sig1 = get_signature(run1[\"prompt\"])\n", "        sig2 = get_signature(run2[\"prompt\"])\n", "        if sig1.strip():\n", "            bad_sigs.append((patch_id, sig1.strip(), sig2.strip()))\n", "    bad_sigs = sorted(bad_sigs, key=lambda x: len(x[2]) / len(x[1]))\n", "    for i, sig in enumerate(bad_sigs):\n", "        patch_id, sig1, sig2 = sig\n", "        if len(sig2) >= 0.9 * len(sig1):\n", "            break\n", "        sig1_details = _parse_signature(sig1, signature_scheme[0])\n", "        sig2_details = _parse_signature(sig2, signature_scheme[1])\n", "        skipped1 = results[0]['data'][patch_id][\"skipped_usages\"]\n", "        skipped2 = results[1]['data'][patch_id][\"skipped_usages\"]\n", "        result1 = run1[\"result\"]\n", "        result2 = run2[\"result\"]\n", "        print(f\"{i}: [{patch_id}] ({result1} vs {result2}): {len(sig1)} sig chars, {len(sig1_details)} sig(s) {len(skipped1)} skipped vs {len(sig2)} sig chars, {len(sig2_details)} sig(s) {len(skipped2)} skipped\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load the Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = [\n", "    {\n", "        \"data\": load(run_path),\n", "        \"name\": run_name,\n", "    }\n", "    for run_name, run_path in run_config\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summarize the Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if results[0]['data'].keys() != results[1]['data'].keys():\n", "    print(\"ERROR: key sets don't match!\")\n", "\n", "result_stats = {}\n", "for k in reversed(results[0]['data'].keys()):\n", "    kk = tuple(results[i]['data'][k][\"result\"] for i in range(len(results)))\n", "    if kk not in result_stats:\n", "        result_stats[kk] = 0\n", "    result_stats[kk] += 1\n", "\n", "pd.DataFrame([\n", "    {\n", "        results[i][\"name\"]: item[0][i]\n", "        for i in range(len(results))\n", "    } | {\n", "        \"count\": item[1]\n", "    }\n", "    for item in result_stats.items()\n", "])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check for Known Issues"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["issue_count = 0\n", "\n", "for res in results:\n", "    for k in reversed(res['data'].keys()):\n", "        if 'run_output' in res['data'][k] and \"Checkout your internet\" in res['data'][k]['run_output']:\n", "            print(f\"[{res['name']}, {k}]  HuggingFace error, presumed non-deterministic failure.\")\n", "            issue_count += 1\n", "        if (res['data'])[k]['result'] not in [\"PASSED\", \"FAILED\"]:\n", "            print(f\"[{res['name']}, {k}]  Result other than pass/fail: {res['data'][k]['result']}.\")\n", "            issue_count += 1\n", "\n", "if issue_count > 0:\n", "    print(f\"WARNING: {issue_count} issues found.\")\n", "else:\n", "    print(\"All OK.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Examine Signatures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_overall_signature_info(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_different_skipped_usages_info(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_bad_signature_usages_info(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Show Differences Between Runs\n", "\n", "**Note:** Flip the `if False` guard to generate the cells. You can then restore the guard to avoid accidentally adding cells."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from typing import Literal\n", "\n", "def _create_new_cell(contents):\n", "    from IPython.core.getipython import get_ipython\n", "\n", "    shell = get_ipython()\n", "\n", "    payload = dict(\n", "        source=\"set_next_input\",\n", "        text=contents,\n", "        replace=False,\n", "    )\n", "    shell.payload_manager.write_payload(payload, single=False)\n", "\n", "def _create_cell_for_id(patch_id):\n", "    passfail = [res_item[\"data\"][patch_id][\"result\"] for res_item in results]\n", "    _create_new_cell(\n", "        f\"# {patch_id}: {passfail[0]} {passfail[1]}\\n\"\n", "        f\"diff_runs(results, '{patch_id}', context=False)\"\n", "    )\n", "\n", "def generate_all_cells():\n", "    for patch_id in _get_different_runs(results):\n", "        _create_cell_for_id(patch_id)\n", "\n", "def generate_n_cells(n: int):\n", "    for i, patch_id in enumerate(_get_different_runs(results)):\n", "        if i >= n:\n", "            break\n", "        _create_cell_for_id(patch_id)\n", "\n", "def generate_n_random_cells(n: int):\n", "    for patch_id in random.sample(list(_get_different_runs(results)), n):\n", "        _create_cell_for_id(patch_id)\n", "\n", "ResultType = Literal[\"PASSED\", \"FAILED\", \"TIMEOUT\"]\n", "\n", "def generate_n_cells_with_results(n: int, result1: ResultType, result2: ResultType):\n", "    cells = set[str]()\n", "    while len(cells) < n:\n", "        patch_id = random.choice(list(results[0][\"data\"].keys()))\n", "        passfail = [res_item[\"data\"][patch_id][\"result\"] for res_item in results]\n", "        if passfail[0] == result1 and passfail[1] == result2:\n", "            cells.add(patch_id)\n", "            if len(cells) >= n:\n", "                break\n", "    for patch_id in cells:\n", "        _create_cell_for_id(patch_id)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if True:\n", "    #generate_n_cells_with_results(1, \"PASSED\", \"TIMEOUT\")\n", "    generate_n_random_cells(1)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
"""A script to start a model server for next edit generation."""

from typing import cast

import yaml

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
import experimental.joel.start_next_edit_gen_server as gen_server
from research.core.constants import (
    AUGMENT_EFS_ROOT,
    AUGMENT_ROOT,
)
from research.eval.harness import factories
from research.eval.harness.systems.next_edit_combined_system import (
    NextEditCombinedSystem,
)
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
    SingleFileNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerSystem,
)
from research.model_server.launch_model_server import main
from research.models.fastforward_models import StarCoder2_FastForward
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
)
from research.next_edits.smart_chunking import SmartChunker


def build_location_system() -> BasicNextEditLocationSystem:
    config_path = (
        AUGMENT_ROOT / "research/model_server/configs/next_edit_location_raven.yaml"
    )
    with config_path.open(encoding="utf8") as f:
        config = yaml.safe_load(f)
    system = factories.create_system(config)
    assert isinstance(system, BasicNextEditLocationSystem)
    return system


def build_reranking_system(gen_system: NextEditGenSystem):
    edit_model = cast(StarCoder2_FastForward, gen_system.model)

    prompt_formatter = EditGenPromptFormatter(
        edit_model.tokenizer_type()._base_tokenizer,
        config=EditGenFormatterConfig(diff_context_lines=9),
    )

    edit_model = NextEditGenSystem(
        edit_model,
        generation_options=GenerationOptions(max_generated_tokens=1),
        retriever=None,
        prompt_formatter=prompt_formatter,
        chat_client=None,
    )
    localizer = build_location_system()
    rechunker = SmartChunker(max_chunk_chars=2000)
    reranker = NextEditRerankerSystem(localizer, edit_model, rechunker)
    return reranker


def build_combined_system():
    gen_system = gen_server.build_system()
    loc_system = build_reranking_system(gen_system)
    single_file_system = SingleFileNextEditLocationSystem(max_chunk_chars=2000)
    return NextEditCombinedSystem(
        loc_system,
        single_file_system,
        gen_system,
        max_changes_to_return=3,
        default_max_changes_to_attempt=8,
        changes_per_yield=1,
        location_score_filter=0.35,
    )


def start_server():
    logger_dir = AUGMENT_EFS_ROOT / "user/jiayi" / "model_server_logs"
    system = build_combined_system()
    system.load()

    args = ["--port", "5000", "--log_dir", str(logger_dir)]
    main(
        system,
        input_args=args,
    )


if __name__ == "__main__":
    start_server()

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This is a script to experiment with the next edit system and eval task."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading the next edit dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "\n", "#DATA_DIR = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location/prs-with-simulated-diff-subsets/\")\n", "DATA_DIR = Path(\"/mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/03-04-2024/prs-with-simulated-wip\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "from unidiff import PatchSet, UnidiffParseError\n", "\n", "from base.prompt_format.next_edit_location.prompt_formatter import DiffHunk\n", "from base.ranges.range_types import LineRange\n", "from research.core.diff_utils import parse_git_diff_output\n", "from research.core.next_edit_location_prompt_input import FileLocation, ResearchNextEditLocationPromptInput, NextEditLocationLabel\n", "from research.core.types import Document, compute_file_id\n", "from research.eval.harness.tasks.next_edit_location_eval_task import Dataset\n", "\n", "def try_parse(diff: str) -> Optional[PatchSet]:\n", "    try:\n", "        return parse_git_diff_output(diff)\n", "    except UnidiffParseError:\n", "        # unidiff itself can fail, and this wrapper fixes some\n", "        # but not all failures, so just skip those that still fail.\n", "        return None\n", "\n", "def load_next_edit_data(df) -> list[tuple[str, Dataset]]:\n", "    data = []\n", "    for _, row in df.iterrows():\n", "        # The old dataset has \"contents\", the new one has \"content\".\n", "        docs = [Document.new(text=file_info[\"content\"], path=file_info[\"path\"]) for file_info in row[\"wip_files\"]]\n", "        if len(docs) > 1000:\n", "            continue\n", "        golden_diff = try_parse(str(row[\"wip_to_future_diff\"]))\n", "        if not golden_diff:\n", "            continue\n", "        if row[\"past_to_wip_diff\"] == row[\"pr_diff\"]:\n", "            continue\n", "        if not row[\"past_to_wip_diff\"]:\n", "            continue\n", "        # Filter out future hunks that are newly-created files as we can never guess them.\n", "        label = NextEditLocationLabel([\n", "            FileLocation(file.path, LineRange(hunk.source_start, hunk.source_start + hunk.source_length))\n", "            for file in golden_diff\n", "            if not file.is_added_file\n", "            for hunk in file\n", "        ])\n", "        # TODO: Ideally we'd also filter out goldens that intersect with recently-modified chunks, since we do that in the system.\n", "        # Ignore cases where we filtered out all goldens.\n", "        if not label.locations:\n", "            continue\n", "        doc_ids = list(map(lambda doc: compute_file_id(doc.path or \"\", doc.text), docs))\n", "        past_to_wip_diff = try_parse(row[\"past_to_wip_diff\"])\n", "        if not past_to_wip_diff:\n", "            continue\n", "        hunks = [DiffHunk(path=file.path, diff_text=str(file)) for file in past_to_wip_diff]\n", "        prompt = ResearchNextEditLocationPromptInput(instruction=str(row[\"title\"]), recent_changes=hunks, doc_ids=doc_ids, label=label, prev_diff=row[\"past_to_wip_diff\"], future_diff=row[\"wip_to_future_diff\"])\n", "        data.append((row[\"repo_name\"], Dataset(prompt, docs)))\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_data_tuple = list[tuple[str, Dataset]]()\n", "for file in sorted(DATA_DIR.glob(\"*.parquet\")):\n", "    df = pd.read_parquet(file)\n", "    all_data_tuple.extend(load_next_edit_data(df))\n", "# Sort by repo name to put similar repos next to each other.\n", "all_data_tuple = sorted(all_data_tuple, key=lambda t: t[0])\n", "all_data = list(map(lambda t: t[1], all_data_tuple))\n", "len(all_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import asdict\n", "from pathlib import Path\n", "\n", "from research.eval.harness import utils\n", "\n", "\n", "all_data_dict = list(map(lambda dataset: asdict(dataset), all_data))\n", "utils.write_jsonl_zst(Path(\"/tmp/next-edit-eval-data.jsonl.zst\"), all_data_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.eval.harness import utils\n", "from research.eval.harness.tasks.next_edit_location_eval_task import Dataset\n", "from research.utils.dataclass_utils import fromdict\n", "\n", "\n", "raw_dataset = utils.read_jsonl_zst(Path(\"/tmp/next-edit-eval-data.jsonl.zst\"))\n", "all_data = [fromdict(Dataset, **datum) for datum in raw_dataset]\n", "len(all_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading the system"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.eval.harness.systems.next_edit_location_system import BasicNextEditLocationSystem\n", "\n", "\n", "config_path = AUGMENT_ROOT / \"research/model_server/configs/next_edit_location.yaml\"\n", "with config_path.open(\"rt\") as f:\n", "    config = yaml.safe_load(f)\n", "    system = BasicNextEditLocationSystem.from_yaml_config(config)\n", "    system.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eval task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.next_edit_location_eval_task import NextEditLocationEvalTask\n", "\n", "\n", "#eval_task = NextEditLocationEvalTask(dataset_path=\"/tmp/next-edit-location-eval-data-small.jsonl.zst\", limit_of_num_files_in_repo=10000)\n", "#eval_task = NextEditLocationEvalTask(dataset_path=\"/tmp/next-edit-eval-data.jsonl.zst\", limit=10)\n", "eval_task = NextEditLocationEvalTask()\n", "\n", "eval_task.run(system, \"/tmp/next-edit-location-eval-task.out\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.eval.harness import utils\n", "\n", "\n", "eval_result = utils.read_jsonl_zst(Path(\"/tmp/next-edit-location-eval-task.out/next_edit_location_NextEditLocationEvalTask_output.jsonl.zst\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.next_edit_location.prompt_formatter import DiffHunk\n", "from base.ranges.range_types import LineRange\n", "from research.core.next_edit_location_prompt_input import FileLocation, NextEditLocationOutput\n", "from research.core.types import Scored\n", "from research.eval.harness.tasks.next_edit_location_eval_task import Metrics, aggregate_metrics, compute_metrics\n", "\n", "\n", "def get_metrics(ignore_same_files:int = 0):\n", "    all_metrics = list[Metrics]()\n", "    assert len(all_data) == len(eval_result)\n", "    for input, output in zip(all_data, eval_result):\n", "        golden = input.prompt.label\n", "        assert golden\n", "        golden = [FileLocation(location.path, LineRange(location.range.start, location.range.stop)) for location in golden.locations]\n", "        candidate_locations = output[\"candidate_locations\"]\n", "        output = NextEditLocationOutput(candidate_locations=[Scored[FileLocation](FileLocation(location[\"item\"][\"path\"], LineRange(location[\"item\"][\"range\"][\"start\"], location[\"item\"][\"range\"][\"stop\"])), location[\"score\"]) for location in candidate_locations])\n", "        if ignore_same_files > 0:\n", "            recent_changes = input.prompt.recent_changes\n", "            recent_changes = [DiffHunk(hunk.path, hunk.diff_text) for hunk in recent_changes]\n", "            recently_changed_files = set(map(lambda hunk: hunk.path, recent_changes))\n", "            golden = [location for location in golden if location.path not in recently_changed_files]\n", "            if not golden:\n", "                continue\n", "            if ignore_same_files > 1:\n", "                output = NextEditLocationOutput(candidate_locations=[location for location in output.candidate_locations if location.item.path not in recently_changed_files])\n", "        cur_metrics = compute_metrics(output, golden)\n", "        all_metrics.append(cur_metrics)\n", "    result = aggregate_metrics(all_metrics)\n", "    result.correct = []\n", "    return result\n", "\n", "print(get_metrics(0))\n", "print(get_metrics(1))\n", "print(get_metrics(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test code to investigate examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["corrects = sum(map(lambda x: x[\"metrics\"][\"correct\"], eval_result), [])\n", "corrects = [\"\".join(map(lambda x: \"X\" if x else \".\", row)) for row in corrects]\n", "corrects = sorted(list(enumerate(corrects)), key=lambda t: t[1].find(\"X\") if \"X\" in t[1] else 99999)\n", "corrects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_index = 50\n", "\n", "test_input = all_data[test_index]\n", "print(\"Instruction:\", test_input.prompt.instruction)\n", "for hunk in test_input.prompt.recent_changes:\n", "    print(\"Recent change:\", hunk.path, \":\", hunk.diff_text)\n", "print(\"Past to wip diff:\")\n", "print(\"----------------\")\n", "print(test_input.prompt.prev_diff)\n", "assert test_input.prompt.label\n", "for location in test_input.prompt.label.locations:\n", "    print(\"Golden:\", location.path, location.range)\n", "print(\"Wip to future diff:\")\n", "print(\"----------------\")\n", "print(test_input.prompt.future_diff)\n", "print(\"All files:\", [doc.path for doc in test_input.documents])\n", "\n", "for location in eval_result[test_index][\"candidate_locations\"]:\n", "    print(\"Candidate:\", location[\"item\"][\"path\"], location[\"item\"][\"range\"], location[\"score\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_files = [\"setup.py\"]\n", "\n", "for doc in test_input.documents:\n", "    if doc.path in print_files:\n", "        print(doc.path, doc.text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
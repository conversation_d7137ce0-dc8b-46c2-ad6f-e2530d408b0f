{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This notebook is intended to aid static analysis development by printing lots of internal and intermediate information about various things."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Initialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from pathlib import Path\n", "\n", "from base.ranges import CharRange, LineMap\n", "from base.static_analysis.common import LanguageID\n", "from base.static_analysis.parsing import GlobalParser, show_ts_node\n", "from base.static_analysis.signature_index import FileSummaryWithSignatures, SignatureIndex, SignaturePrinter\n", "from base.static_analysis.usage_analysis import FileSummary, ParsedFile, UsageIndex"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class CodeExample:\n", "    code: str\n", "    lang: LanguageID\n", "    filename: str"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_code = [\n", "    CodeExample(\n", "        code=r\"\"\"\n", "        class C:\n", "            field: int\n", "            def getter(self) -> int:\n", "                return self.field\n", "        \n", "        def sum(x: int, y: int, c: C) -> int:\n", "            return x + y + c.field\n", "        \n", "        c = C(42)\n", "        sum(1, 2, c)\n", "        \"\"\",\n", "        lang=\"python\",\n", "        filename=\"file1.py\",\n", "    ),\n", "    CodeExample(\n", "        code=r\"\"\"\n", "        def sum(x: int, y: int) -> int:\n", "            return x + y\n", "        sum(42, 137)\n", "        \"\"\",\n", "        lang=\"python\",\n", "        filename=\"file2.py\",\n", "    ),\n", "    CodeExample(\n", "        code=r\"\"\"\n", "        c = C(42)\n", "        sum(42, 137, c.getter())\n", "        \"\"\",\n", "        lang=\"python\",\n", "        filename=\"file3.py\",\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parsed_files = [ParsedFile.parse(Path(ex.filename), ex.lang, ex.code) for ex in all_code]\n", "parsed_files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_summaries = [FileSummary.from_pfile(pfile) for pfile in parsed_files]\n", "file_summaries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tree-sitter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for code in all_code:\n", "    ts_tree = GlobalParser.parse_ts_tree(code.code, code.lang)\n", "    print(show_ts_node(ts_tree.root_node, show_unnamed=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scope trees"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for pfile in parsed_files:\n", "    pfile.scope_tree.pprint()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Definitions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for summary in file_summaries:\n", "    for definition in summary.definitions:\n", "        print(definition)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Local usage analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for summary in file_summaries:\n", "    print(\"Usages for %s:\" % summary.path)\n", "    for usage in sorted(summary.local_analysis.name_usages, key=lambda x: x.use_site):\n", "        print(usage)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Usage index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = UsageIndex.from_files(file_summaries)\n", "index.pprint()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for summary in file_summaries:\n", "    print(\"Usages in %s:\" % summary.path)\n", "    for usage, defs in index.resolve_file_usages(summary).site2defs.items():\n", "        print(f\"  {usage}: {defs}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Signatures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = SignatureIndex()\n", "sig_printer = SignaturePrinter()\n", "summaries = dict[Path, FileSummaryWithSignatures]()\n", "src_map = dict[Path, str]()\n", "\n", "# The file we want to query.\n", "ex_file = Path(\"file3.py\")\n", "\n", "for pfile in parsed_files:\n", "    src_map[pfile.path] = pfile.code\n", "    summary = FileSummaryWithSignatures.from_pfile(pfile, sig_printer, pfile.path == ex_file)\n", "    summaries[summary.path] = summary\n", "    index.update_file(summary)\n", "\n", "for lang, uindex in index._usage_indexes.items():\n", "    print(\"%s: \" % lang)\n", "    uindex.pprint()\n", "for symdef, sig in index._file_signatures_map.items():\n", "    print(\"%s: %s\" % (symdef, sig))\n", "for mod, sig in index._module_signature_map.items():\n", "    print(\"%s: %s\" % (mod, sig))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cursor_line = 0\n", "\n", "lmap = LineMap(src_map[ex_file])\n", "ex_summary = summaries[ex_file]\n", "cursor_char = lmap.get_char_index(cursor_line, 0)\n", "result = index.get_context_signatures(\n", "    ex_summary.summary, cursor_char, prompt_range=Char<PERSON><PERSON>e(cursor_char, cursor_char)\n", ")\n", "print(result.metrics)\n", "for name, uses in result.ctx_signatures.items():\n", "    lrange = lmap.crange_to_lrange(name.use_site)\n", "    print(\"-\" * 100)\n", "    print(f\"At line {lrange.start + 1}, {name}:\")\n", "    for sig in uses:\n", "        print(f\"{sig.text}\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}
"""A script to help invoke the beachhead locally.

Example uses:

PYTHONPATH=/home/<USER>/augment python experimental/joel/local_beachhead.py --list-remote-agents

PYTHONPATH=/home/<USER>/augment python experimental/joel/local_beachhead.py --delete-remote-agents <hashes>

PYTHONPATH=/home/<USER>/augment python experimental/joel/local_beachhead.py --remote-agent-id=<hash> What is 42?

"""

import os
import tempfile
import json
import argparse
import subprocess
from datetime import datetime
from base.augment_client.client import AugmentClient, RemoteAgentWorkspaceSetup

_URL = "https://staging-shard-0.api.augmentcode.com/"
"""URL of the augment API endpoint."""

_USER_AGENT = "cli/0.1.0"


def format_timestamp(timestamp):
    """Convert protobuf timestamp to human-readable format."""
    if timestamp is None:
        return "N/A"

    # Handle protobuf timestamp with seconds and nanos fields
    if hasattr(timestamp, "seconds"):
        dt = datetime.fromtimestamp(timestamp.seconds + timestamp.nanos / 1e9)
        return dt.strftime("%Y-%m-%d %H:%M:%S UTC")

    # Handle string timestamps (ISO format)
    if isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
            return dt.strftime("%Y-%m-%d %H:%M:%S UTC")
        except ValueError:
            return str(timestamp)

    return str(timestamp)


def get_augment_client():
    with open(os.path.expanduser("~/.config/augment/api_token")) as f:
        client = AugmentClient(
            url=_URL,
            token=f.read().strip(),
            user_agent=_USER_AGENT,
        )
    return client


def make_remote_agent(message: str):
    client = get_augment_client()
    resp = client.create_remote_agent(
        workspace_setup=RemoteAgentWorkspaceSetup(
            starting_files={
                "github_commit_ref": {
                    "repository_url": "https://github.com/augmentcode/SWE-bench",
                    "git_ref": "main",
                }
            }
        ),
        initial_request_nodes=[{"id": 0, "type": 0, "text_node": {"content": message}}],
    )
    print(resp)
    return resp.remote_agent_id


def make_workspace_agent_config(remote_agent_id: str, message: str):
    temp_file = tempfile.NamedTemporaryFile(suffix=".json", delete=False)
    with open(temp_file.name, "w") as f:
        json.dump(
            {
                "remote_agent_id": remote_agent_id,
                "config": {
                    "workspace_setup": {
                        "starting_files": {
                            "github_commit_ref": {
                                "repository_url": "https://github.com/augmentcode/SWE-bench",
                                "git_ref": "main",
                            }
                        }
                    },
                    "starting_nodes": [
                        {"id": 0, "type": 0, "text_node": {"content": message}}
                    ],
                    "user_guidelines": "",
                    "workspace_guidelines": "",
                    "agent_memories": "",
                    "model": "",
                    "mcp_servers": [],
                },
            },
            f,
        )
    return temp_file.name


def list_remote_agents():
    """List all remote agents and print their details."""
    client = get_augment_client()
    response = client.list_remote_agents()

    if not response.remote_agents:
        print("No remote agents found.")
        return

    print(f"Found {len(response.remote_agents)} remote agent(s):")
    print(f"Max remote agents: {response.max_remote_agents}")
    print(f"Max active remote agents: {response.max_active_remote_agents}")
    print()

    for agent in response.remote_agents:
        print(f"Agent ID: {agent.remote_agent_id}")
        print(f"Status: {agent.status}")
        print(f"Started: {format_timestamp(agent.started_at)}")
        print(f"Updated: {format_timestamp(agent.updated_at)}")
        if hasattr(agent, "expires_at") and agent.expires_at:
            print(f"Expires: {format_timestamp(agent.expires_at)}")
        if hasattr(agent, "has_updates") and agent.has_updates is not None:
            print(f"Has updates: {agent.has_updates}")
        if hasattr(agent, "session_summary") and agent.session_summary:
            print(f"Summary: {agent.session_summary}")
        print("---")


def delete_remote_agents(agent_ids: list[str]):
    """Delete multiple remote agents by their IDs."""
    client = get_augment_client()

    if not agent_ids:
        print("No agent IDs provided for deletion.")
        return

    print(f"Deleting {len(agent_ids)} remote agent(s)...")

    success_count = 0
    failed_agents = []

    for agent_id in agent_ids:
        try:
            print(f"Deleting remote agent: {agent_id}")
            client.delete_remote_agent(agent_id)
            print(f"✓ Successfully deleted remote agent: {agent_id}")
            success_count += 1
        except Exception as e:
            print(f"✗ Error deleting remote agent {agent_id}: {e}")
            failed_agents.append(agent_id)

    # Summary
    print("\nDeletion summary:")
    print(f"Successfully deleted: {success_count}/{len(agent_ids)} agents")

    if failed_agents:
        print(f"Failed to delete: {len(failed_agents)} agents")
        print(f"Failed agent IDs: {', '.join(failed_agents)}")
        raise Exception(
            f"Failed to delete {len(failed_agents)} out of {len(agent_ids)} agents"
        )
    else:
        print("All agents deleted successfully!")


def run_beachhead(config_file: str):
    # Read the API token and set it as an environment variable
    with open(os.path.expanduser("~/.config/augment/api_token")) as f:
        api_token = f.read().strip()

    # Set the environment variable
    env = os.environ.copy()
    env["AUGMENT_API_TOKEN"] = api_token
    env["AUGMENT_API_URL"] = _URL

    subprocess.run(
        [
            "bazel",
            "run",
            "//clients/beachhead:beachhead_dev",
            "--",
            "--workspace-root",
            "/home/<USER>/augment/",
            "--workspace-agent-config",
            config_file,
        ],
        env=env,
        check=True,
    )


def main():
    parser = argparse.ArgumentParser(description="Run beachhead with remote agent")
    parser.add_argument(
        "--remote-agent-id",
        type=str,
        help="Use existing remote agent ID instead of creating a new one",
    )
    parser.add_argument(
        "--list-remote-agents",
        action="store_true",
        help="List all remote agents and exit",
    )
    parser.add_argument(
        "--delete-remote-agents",
        type=str,
        nargs="+",
        help="Delete one or more remote agents by ID and exit",
    )
    parser.add_argument(
        "message", nargs="*", help="Initial message to send to the agent (default: '')"
    )
    args = parser.parse_args()

    # Handle --list-remote-agents flag
    if args.list_remote_agents:
        list_remote_agents()
        return

    # Handle --delete-remote-agents flag
    if args.delete_remote_agents:
        delete_remote_agents(args.delete_remote_agents)
        return

    # Process the message
    message = " ".join(args.message) if args.message else ""

    if args.remote_agent_id:
        agent_id = args.remote_agent_id
        print(f"Using existing remote agent ID: {agent_id}")
    else:
        agent_id = make_remote_agent(message)
        print(f"Created new remote agent ID: {agent_id}")

    config_file = make_workspace_agent_config(agent_id, message)
    try:
        run_beachhead(config_file)
    finally:
        os.unlink(config_file)


if __name__ == "__main__":
    main()

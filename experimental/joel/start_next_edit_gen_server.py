"""A script to start a model server for next edit generation."""

from research.core.constants import (
    AUGMENT_CHECKPOINTS_ROOT,
    AUGMENT_EFS_ROOT,
)
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    ethanol_config,
)
from research.eval.harness import factories
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.llm_apis.chat_utils import Llama3ChatClient
from research.model_server.launch_model_server import main
from research.models.fastforward_models import (
    StarCoder2_FastForward,
)
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
)


def build_system(use_retriever: bool = False):
    model = StarCoder2_FastForward(
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S1.6_keep_most-R1.0_starethanol_fixed-P1.7_context9-100K_repos-starcoder2_7b",
        checkpoint_sha256="d0d7dcf480304952a666779cbe5221b6c6979c8a74c1c58a59ceedf8f55d85ff",
    )

    retriever = None
    if use_retriever:
        retriever = factories.create_retriever(dict(ethanol_config))

    prompt_formatter = EditGenPromptFormatter(
        model.tokenizer_type()._base_tokenizer,
    )

    llama_server_address = "**************:8000"
    chat_client = Llama3ChatClient(
        server_type="triton", address=llama_server_address, timeout=180
    )

    return NextEditGenSystem(
        model,
        generation_options=GenerationOptions(max_generated_tokens=1300),
        retriever=retriever,
        prompt_formatter=prompt_formatter,
        chat_client=chat_client,
        max_retrieved_chunks=10,
    )


log_dir = (
    AUGMENT_EFS_ROOT / "user/jiayi" / "model_server_logs" / "next_edits_server_logs"
)


def start_server():
    system = build_system()
    system.load()
    main(
        system,
        input_args=(
            "--port",
            "5000",
            "--log_dir",
            str(log_dir),
        ),
    )


if __name__ == "__main__":
    start_server()

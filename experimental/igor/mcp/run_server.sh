#!/bin/bash

declare -r BASEDIR="$(dirname "$0")"
declare -r AUGMENT="$(readlink -e "$BASEDIR"/../../../..)"

# Select the correct pip and python depending on DevPod (research)
# or dev_vm (services -- default/fallback).
if mountpoint -q /startup; then
	declare -ra pip=(/opt/conda/bin/pip)
	declare -ra python=(/opt/conda/bin/python3)
else
	declare -ra pip=(~/.local/bin/pip)
	declare -ra python=(/usr/bin/python3.11)
fi

# Install uv just in case. Going forward this should be part of the base system system.
if ! type -t uv &>/dev/null; then
	printf "Installing uv...\n"
	curl -LsSf https://astral.sh/uv/install.sh | sh
fi

"${pip[@]}" install mcp pytest pydantic
"${python[@]}" "$BASEDIR"/server.py "$@"

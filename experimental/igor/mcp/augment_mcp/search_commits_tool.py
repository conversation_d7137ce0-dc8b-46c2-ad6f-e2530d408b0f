from augment_mcp.mcp_extend import ExtendedTool
from augment_mcp.tools import Tool


class CommitSearcherTool(Tool):
    def __init__(self, server_url: str = "http://localhost:5050/search"):
        self.server_url = server_url

    @property
    def name(self) -> str:
        return "search_past_commits"

    def check_safe(self, arguments: dict) -> bool:
        # Search a commit is always safe
        return True

    def list_tool(self):
        return ExtendedTool(
            name=self.name,
            description=(
                "Search past commits using a detailed task description or implementation question."
            ),
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": (
                            "A detailed task description or implementation question to help find relevant past commits. "
                            "The query should be coherent sentences describing the commit you are looking for. Provide as much context as you can. End the query with `Keywords: <a few keywords>`."
                        ),
                    }
                },
                "required": ["query"],
            },
            toolSafety="safe",
        )

    def call_tool(self, cwd, args: dict) -> str:
        import requests

        # Get the query from the arguments
        query = args.get("query", "")
        if not query:
            return "Error: No query provided"

        try:
            # Make the request to the retrieval server
            response = requests.post(
                self.server_url, json={"query": query, "top_k": 5}, timeout=30
            )

            # Check if the request was successful
            response.raise_for_status()

            # Parse the response
            data: dict = response.json()
            results: list = data.get("results", [])

            if not results:
                return "No relevant commits found for your query."

            # Format the results as a readable string
            output = f"# Commits based on the query: '{query}'\n\n"

            for i, commit in enumerate(results, 1):
                output += f"## {i}. Subject: {commit['subject']}\n"
                output += f"**SHA:** {commit['hash']}\n"
                output += f"**Author:** {commit['author']}\n"
                output += f"**Date:** {commit['date']}\n"

                # Add summary
                output += f"**Summary:** {commit['summary']}\n\n"

            return output

        except Exception as e:
            return f"Error: {str(e)}"


class CommitReaderTool(Tool):
    def __init__(self, server_url: str = "http://localhost:5050/read_commit"):
        self.server_url = server_url

    @property
    def name(self) -> str:
        return "read_commit"

    def check_safe(self, arguments: dict) -> bool:
        # Read a commit is always safe
        return True

    def list_tool(self):
        return ExtendedTool(
            name=self.name,
            description=(
                "Read the details of a commit using its SHA. Returns the content of the commit, including its diff. "
                "This can be used to read the details of a commit returned by the search_past_commits tool, if needed."
            ),
            inputSchema={
                "type": "object",
                "properties": {
                    "commit_sha": {
                        "type": "string",
                        "description": "The SHA of the commit to read.",
                    }
                },
                "required": ["commit_sha"],
            },
            toolSafety="safe",
        )

    def call_tool(self, cwd, args: dict) -> str:
        import requests

        # Get the commit SHA from the arguments
        commit_sha = args.get("commit_sha", "")
        if not commit_sha:
            return "Error: No commit SHA provided"

        try:
            # Make the request to the retrieval server
            response = requests.post(
                self.server_url, json={"commit_sha": commit_sha}, timeout=30
            )

            # Check if the request was successful
            response.raise_for_status()

            # Parse the response
            data: dict = response.json()
            formatted_commit = data.get("formatted_commit", "")

            if not formatted_commit:
                return f"No details found for commit SHA: {commit_sha}"

            # Return the formatted commit
            return f"# Commit Details for SHA: {commit_sha}\n\n{formatted_commit}"

        except Exception as e:
            return f"Error: {str(e)}"

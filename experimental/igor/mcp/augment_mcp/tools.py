import subprocess

from augment_mcp.mcp_extend import ExtendedTool
from augment_mcp.safety_checker import <PERSON><PERSON><PERSON><PERSON>

from abc import ABC, abstractmethod


class Tool(ABC):
    @abstractmethod
    def list_tool(self) -> ExtendedTool:
        pass

    @abstractmethod
    def call_tool(self, cwd, *args, **kwargs) -> str:
        pass

    def check_safe(self, arguments: dict) -> bool:
        return False

    @property
    @abstractmethod
    def name(self) -> str:
        pass


class ShellTool(Tool):
    def __init__(self, shell_name: str = "bash"):
        self.shell_name = shell_name

    def list_tool(self):
        return ExtendedTool(
            name=self.shell_name,
            description=f"Execute a {self.shell_name} shell command",
            inputSchema={
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": f"{self.shell_name} command.",
                    }
                },
                "required": ["command"],
            },
        )

    def call_tool(self, cwd, args: dict) -> str:
        result = subprocess.run(
            args["command"],
            shell=True,
            text=True,
            cwd=cwd,
            check=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
        )

        if result.returncode != 0:
            raise RuntimeError(result.stdout)

        return result.stdout

    @property
    def name(self) -> str:
        return self.shell_name


class CommandTool(Tool):
    def __init__(
        self,
        cmd: str,
        safety_checker: SafetyChecker | None = None,
        extra_description: str = "",
    ):
        self.cmd = cmd
        self.path = self._discover(cmd)
        self.description_suffix = f" {extra_description}" if extra_description else ""
        self.safety_checker = safety_checker

    def list_tool(self):
        return ExtendedTool(
            name=self.cmd,
            description=f"Use {self.cmd}.{self.description_suffix}",
            inputSchema={
                "type": "object",
                "properties": {
                    "args": {
                        "type": "array",
                        "description": f"Arguments to pass to {self.cmd}.",
                    }
                },
                "required": ["args"],
            },
            toolSafety="check",
        )

    def call_tool(self, cwd, args: dict) -> str:
        result = subprocess.run(
            [self.path] + args["args"],
            shell=False,
            capture_output=True,
            text=True,
            cwd=cwd,
            check=False,
        )

        if result.returncode != 0:
            raise RuntimeError(result.stderr)

        return result.stdout

    def check_safe(self, arguments: dict) -> bool:
        if self.safety_checker is None:
            return False
        return self.safety_checker.check_safe(arguments["args"])

    @property
    def name(self) -> str:
        return self.cmd

    def _discover(self, tool_name) -> str:
        result = subprocess.run(
            ["bash", "-c", f"which {tool_name}"],
            capture_output=True,
            text=True,
            check=False,
        )

        if result.returncode != 0:
            raise RuntimeError(f"{tool_name} not found in PATH")

        return result.stdout.strip()

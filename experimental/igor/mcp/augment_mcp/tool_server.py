import asyncio
import logging
import tempfile

from typing import Sequence
from pathlib import Path


from augment_mcp.mcp_extend import (
    check_safe,
    mcp_types,
    mcp_server,
)
from augment_mcp.tools import Tool


log_dir = Path(tempfile.gettempdir()) / "augment"
log_dir.mkdir(parents=True, exist_ok=True)
log_file = log_dir / "mcp-server-augment.log"
logging.basicConfig(filename="/tmp/mcp-server-augment.log", level=logging.DEBUG)


async def serve_coroutine(tools: list[Tool], cwd: str | None = None) -> None:
    server = mcp_server.Server("mcp-igor")
    tools_map = {tool.name: tool for tool in tools}

    @server.list_tools()
    async def list_tools() -> list[mcp_types.Tool]:
        """List available tools."""
        logging.info("list_tools")
        return [tool.list_tool() for _tool_name, tool in tools_map.items()]

    @server.call_tool()
    async def call_tool(
        name: str, arguments: dict
    ) -> Sequence[
        mcp_types.TextContent | mcp_types.ImageContent | mcp_types.EmbeddedResource
    ]:
        logging.info(f"call_tool: {name} {arguments}")
        if name in tools_map:
            result = tools_map[name].call_tool(cwd, arguments)
            return [mcp_types.TextContent(type="text", text=result)]
        else:
            raise RuntimeError(f"Tool {name} not found")

    @check_safe(server)
    async def _check_safe(name: str, arguments: dict) -> bool:
        logging.info(f"check_safe: {name} {arguments}")
        if name in tools_map.keys():
            return tools_map[name].check_safe(arguments)
        else:
            return False

    options = server.create_initialization_options()
    async with mcp_server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream, options)


def serve_tools(tools: list[Tool], cwd: str | None = None):
    asyncio.run(serve_coroutine(tools, cwd))

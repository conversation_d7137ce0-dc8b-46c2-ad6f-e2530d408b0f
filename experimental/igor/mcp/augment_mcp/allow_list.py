from augment_mcp.match_rule import MatchRules as M


class AllowList:
    git = [
        M.prefix(["status"]),
        <PERSON>.prefix(["log"]),
        <PERSON><PERSON>prefix(["diff"]),
        <PERSON><PERSON>prefix(["show"]),
        <PERSON><PERSON>exact(["branch"]),
        <PERSON><PERSON>prefix(["ls-files"]),
        <PERSON><PERSON>prefix(["blame"]),
        <PERSON>.prefix(["rev-parse"]),
        <PERSON>.prefix(["remote", "-v"]),
        M.prefix(["config", "--list"]),
    ]
    kubectl = [
        M.prefix(["get"]),
        <PERSON>.prefix(["describe"]),
        <PERSON>.prefix(["explain"]),
        <PERSON>.prefix(["logs"]),
        <PERSON><PERSON>prefix(["top"]),
        <PERSON>.prefix(["api-resources"]),
        <PERSON><PERSON>prefix(["api-versions"]),
        <PERSON>.prefix(["version"]),
        M.prefix(["wait"]),
        M.prefix(["auth", "can-i"]),
        <PERSON>.prefix(["config", "get-contexts"]),
        <PERSON><PERSON>prefix(["config", "view"]),
    ]
    bazel = [
        <PERSON>.prefix(["query"]),
        <PERSON><PERSON>prefix(["cquery"]),
        <PERSON><PERSON>prefix(["config"]),
        <PERSON>.prefix(["info"]),
        <PERSON><PERSON>prefix(["version"]),
        M.prefix(["help"]),
        M.prefix(["analyze-profile"]),
        M.prefix(["aquery"]),
        M.prefix(["dump"]),
        M.prefix(["license"]),
        M.prefix(["print"]),
        M.prefix(["build", "--nobuild"]),
        M.prefix(["coverage", "--nobuild"]),
        M.prefix(["mobile-install", "--nobuild"]),
        M.prefix(["run", "--nobuild"]),
        M.prefix(["text", "--nobuild"]),
        M.prefix(["clean", "--expunge", "--dry-run"]),
    ]
    docker = [
        M.prefix(["ps"]),
        M.prefix(["images"]),
        M.prefix(["network", "ls"]),
        M.prefix(["volume", "ls"]),
        M.prefix(["port"]),
        M.prefix(["stats"]),
        M.prefix(["events"]),
        M.prefix(["diff"]),
        M.prefix(["history"]),
        M.prefix(["system", "df"]),
        M.prefix(["top"]),
        M.prefix(["version"]),
        M.prefix(["inspect"]),
    ]
    npm = [
        M.prefix(["list"]),
        M.prefix(["outdated"]),
        M.prefix(["doctor"]),
        M.prefix(["audit"]),
        M.prefix(["token", "list"]),
        M.prefix(["ping"]),
        M.prefix(["view"]),
        M.prefix(["owner", "ls"]),
        M.prefix(["fund"]),
        M.prefix(["explain"]),
        M.prefix(["ls"]),
        M.prefix(["why"]),
        M.prefix(["prefix"]),
    ]
    terraform = [
        M.prefix(["show"]),
        M.prefix(["providers"]),
        M.prefix(["state", "list"]),
        M.prefix(["state", "show"]),
        M.prefix(["version"]),
        M.prefix(["fmt", "--check"]),
        M.prefix(["validate"]),
        M.prefix(["graph"]),
        M.prefix(["console"]),
        M.prefix(["output"]),
        M.prefix(["refresh", "--dry-run"]),
        M.prefix(["plan"]),
    ]
    gradle = [
        M.prefix(["dependencies"]),
        M.prefix(["projects"]),
        M.prefix(["properties"]),
        M.prefix(["tasks"]),
        M.prefix(["components"]),
        M.prefix(["model"]),
        M.prefix(["buildEnvironment"]),
        M.prefix(["projectsEvaluated"]),
        M.prefix(["projects", "--dry-run"]),
        M.prefix(["dependencies", "--dry-run"]),
        M.prefix(["help"]),
        M.prefix(["version"]),
    ]
    helm = [
        M.prefix(["list"]),
        M.prefix(["get", "values"]),
        M.prefix(["get", "manifest"]),
        M.prefix(["get", "hooks"]),
        M.prefix(["get", "notes"]),
        M.prefix(["status"]),
        M.prefix(["dependency", "list"]),
        M.prefix(["show", "chart"]),
        M.prefix(["show", "values"]),
        M.prefix(["verify"]),
        M.prefix(["version"]),
        M.prefix(["env"]),
    ]
    aws = [
        M.prefix(["s3", "ls"]),
        M.prefix(["ec2", "describe-instances"]),
        M.prefix(["rds", "describe-db-instances"]),
        M.prefix(["iam", "list-users"]),
        M.prefix(["iam", "list-roles"]),
        M.prefix(["lambda", "list-functions"]),
        M.prefix(["eks", "list-clusters"]),
        M.prefix(["ecr", "describe-repositories"]),
        M.prefix(["cloudformation", "list-stacks"]),
        M.prefix(["configure", "list"]),
    ]
    gcloud = [
        M.prefix(["projects", "list"]),
        M.prefix(["compute", "instances", "list"]),
        M.prefix(["compute", "zones", "list"]),
        M.prefix(["compute", "regions", "list"]),
        M.prefix(["container", "clusters", "list"]),
        M.prefix(["services", "list"]),
        M.prefix(["iam", "roles", "list"]),
        M.prefix(["config", "list"]),
        M.prefix(["components", "list"]),
        M.prefix(["version"]),
    ]
    postgres = [
        M.prefix(["psql", "-l"]),
        M.prefix(["pg_dump", "--schema-only"]),
        M.prefix(["pg_dump", "--schema", "public", "--dry-run"]),
        M.prefix(["pg_dump", "-s", "-t"]),
        M.prefix(["pg_controldata"]),
        M.prefix(["pg_isready"]),
        M.prefix(["pg_lsclusters"]),
        M.prefix(["pg_activity"]),
        M.prefix(["pgbench", "-i", "--dry-run"]),
    ]
    maven = [
        M.prefix(["dependency:tree"]),
        M.prefix(["dependency:analyze"]),
        M.prefix(["help:effective-pom"]),
        M.prefix(["help:describe"]),
        M.prefix(["help:evaluate"]),
        M.prefix(["dependency:list"]),
        M.prefix(["dependency:build-classpath"]),
        M.prefix(["help:active-profiles"]),
        M.prefix(["help:effective-settings"]),
        M.prefix(["version"]),
    ]
    redis_cli = [
        M.prefix(["info"]),
        M.prefix(["monitor"]),
        M.prefix(["memory", "stats"]),
        M.prefix(["memory", "doctor"]),
        M.prefix(["latency", "doctor"]),
        M.prefix(["cluster", "info"]),
        M.prefix(["client", "list"]),
        M.prefix(["slowlog", "get"]),
        M.prefix(["config", "get"]),
        M.prefix(["info", "keyspace"]),
    ]
    yarn = [
        M.prefix(["list"]),
        M.prefix(["info"]),
        M.prefix(["why"]),
        M.prefix(["licenses", "list"]),
        M.prefix(["outdated"]),
        M.prefix(["check"]),
        M.prefix(["audit"]),
        M.prefix(["workspaces", "info"]),
        M.prefix(["version"]),
        M.prefix(["config", "list"]),
    ]
    az = [
        M.prefix(["account", "list"]),
        M.prefix(["group", "list"]),
        M.prefix(["vm", "list"]),
        M.prefix(["aks", "list"]),
        M.prefix(["acr", "list"]),
        M.prefix(["storage", "account", "list"]),
        M.prefix(["network", "vnet", "list"]),
        M.prefix(["webapp", "list"]),
        M.prefix(["functionapp", "list"]),
        M.prefix(["version"]),
    ]
    vault = [
        M.prefix(["list"]),
        M.prefix(["policy", "list"]),
        M.prefix(["auth", "list"]),
        M.prefix(["secrets", "list"]),
        M.prefix(["audit", "list"]),
        M.prefix(["status"]),
        M.prefix(["token", "lookup"]),
        M.prefix(["read"]),
        M.prefix(["version"]),
    ]
    podman = [
        M.prefix(["ps"]),
        M.prefix(["images"]),
        M.prefix(["pod", "ps"]),
        M.prefix(["volume", "ls"]),
        M.prefix(["network", "ls"]),
        M.prefix(["stats"]),
        M.prefix(["top"]),
        M.prefix(["logs"]),
        M.prefix(["inspect"]),
        M.prefix(["port"]),
    ]
    deno = [
        M.prefix(["info"]),
        M.prefix(["list"]),
        M.prefix(["doc"]),
        M.prefix(["lint"]),
        M.prefix(["types"]),
        M.prefix(["check"]),
        M.prefix(["compile", "--dry-run"]),
        M.prefix(["task", "--list"]),
        M.prefix(["test", "--dry-run"]),
        M.prefix(["version"]),
    ]
    rustup = [
        M.prefix(["show"]),
        M.prefix(["toolchain", "list"]),
        M.prefix(["target", "list"]),
        M.prefix(["component", "list"]),
        M.prefix(["override", "list"]),
        M.prefix(["which"]),
        M.prefix(["doc"]),
        M.prefix(["man"]),
        M.prefix(["version"]),
    ]
    cargo = [
        M.prefix(["tree"]),
        M.prefix(["metadata"]),
        M.prefix(["list"]),
        M.prefix(["verify"]),
        M.prefix(["search"]),
        M.prefix(["vendor", "--dry-run"]),
        M.prefix(["outdated"]),
        M.prefix(["doc"]),
        M.prefix(["config", "get"]),
        M.prefix(["version"]),
    ]
    pip = [
        M.prefix(["list"]),
        M.prefix(["show"]),
        M.prefix(["check"]),
        M.prefix(["debug"]),
        M.prefix(["config", "list"]),
        M.prefix(["index"]),
        M.prefix(["hash"]),
        M.prefix(["cache", "list"]),
        M.prefix(["freeze"]),
        M.prefix(["version"]),
    ]

"""Interface and implementations for checking tool safety levels."""

from augment_mcp.match_rule import MatchRule


class SafetyChecker:
    """Interface for checking tool safety levels."""

    def check_safe(self, args: list[str]) -> bool:
        return False


class SafetyCheckerSafe(SafetyChecker):
    """Implementation of SafetyChecker that always returns True."""

    def check_safe(self, args: list[str]) -> bool:
        return True


class SafetyCheckerAllowList(SafetyChecker):
    """Implementation of SafetyChecker that uses an allow list for safety checks."""

    def __init__(self, match_rules: list[MatchRule]):
        """Initialize the checker with an allow list."""
        self.match_rules = match_rules

    def check_safe(self, args: list[str]) -> bool:
        return any(rule.match(args) for rule in self.match_rules)

from abc import abstractmethod


class MatchRules:
    """Factory class for creating different types of command matching rules."""

    @staticmethod
    def prefix(args_prefix: list[str]):
        """Creates a prefix matching rule.

        Args:
            args_prefix: List of strings to match as a prefix.
        """
        return _PrefixMatchRule(args_prefix)

    @staticmethod
    def exact(args: list[str]):
        """Creates an exact matching rule.

        Args:
            args: List of strings to match exactly.
        """
        return _ExactMatchRule(args)


class MatchRule:
    """Abstract base class for command matching rules."""

    @abstractmethod
    def match(self, args: list[str]) -> bool:
        """Checks if the given arguments match this rule.

        Args:
            args: List of command arguments to check.

        Returns:
            True if arguments match the rule, False otherwise.
        """
        pass


class _PrefixMatchRule(MatchRule):
    """Rule that matches if arguments start with a specific prefix."""

    def __init__(self, args_prefix: list[str]):
        """Initialize with prefix to match.

        Args:
            args_prefix: List of strings that should appear at start of command.
        """
        self.args_prefix = args_prefix

    def match(self, args: list[str]) -> bool:
        """Check if arguments start with the stored prefix.

        Args:
            args: List of command arguments to check.

        Returns:
            True if args start with self.args_prefix, False otherwise.
        """
        return len(args) >= len(self.args_prefix) and all(
            self.args_prefix[i] == args[i] for i in range(len(self.args_prefix))
        )


class _ExactMatchRule(MatchRule):
    """Rule that matches if arguments exactly match a command."""

    def __init__(self, args_exact: list[str]):
        """Initialize with command to match exactly.

        Args:
            args_exact: The exact command arguments to match.
        """
        self.args_exact = args_exact

    def match(self, args: list[str]) -> bool:
        """Check if arguments exactly match the stored command.

        Args:
            args: List of command arguments to check.

        Returns:
            True if args exactly match self.args_exact, False otherwise.
        """
        return len(self.args_exact) == len(args) and all(
            self.args_exact[i] == args[i] for i in range(len(self.args_exact))
        )

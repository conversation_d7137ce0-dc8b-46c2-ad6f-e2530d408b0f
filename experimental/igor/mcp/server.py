import json
import os
import re

from augment_mcp.allow_list import <PERSON>owList
from augment_mcp.tools import Too<PERSON>, <PERSON>Tool, ShellTool
from augment_mcp.safety_checker import <PERSON><PERSON>hecker<PERSON>llowList, SafetyCheckerSafe
from augment_mcp.tool_server import serve_tools
from augment_mcp.search_commits_tool import Commit<PERSON><PERSON>erTool, CommitSearcherTool


class SafetyCheckBazel(SafetyCheckerAllowList):
    def __init__(self):
        super().__init__(AllowList.bazel)

    def check_safe(self, args: list[str]) -> bool:
        if super().check_safe(args):
            return True
        if args[0] == "run":
            for arg in args[1:]:
                if arg.startswith("-"):
                    continue
                return re.search(r"_test\.[^.]+$", arg) is not None

        return False


def get_k8s_dev_namespace():
    with open(f"{os.environ['HOME']}/.augment/user.json") as f:
        user_data = json.load(f)
        user_name = user_data["name"]
        return f"dev-{user_name}"


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("-C", type=str, help="Override the current directory")
    parser.add_argument(
        "--commit_server_url",
        type=str,
        help="Specify the commit server URL. If this is set, only the commit retrieval tools will be loaded.",
    )

    args = parser.parse_args()

    commit_server_url: str | None = args.commit_server_url
    if commit_server_url:
        # Note(jiayi): this is a new use case for the commit retrieval project.
        # Will clean up this code once it's been productionized.
        tools = [
            CommitSearcherTool(f"{commit_server_url}/search"),
            CommitReaderTool(f"{commit_server_url}/read_commit"),
        ]
    else:
        # This is for backwards compatibility.
        tools = [
            ShellTool(),
            CommandTool("git", SafetyCheckerAllowList(AllowList.git)),
            CommandTool("bazel", SafetyCheckBazel()),
            CommandTool(
                "kubectl",
                SafetyCheckerAllowList(AllowList.kubectl),
                extra_description=f"(`-n {get_k8s_dev_namespace()}` for dev namespace)",
            ),
            CommandTool("pytest", SafetyCheckerSafe()),
        ]

    serve_tools(tools, args.C)

from megatron.tokenizer import AbstractTokenizer, get_tokenizer

from base.prompt_format_completion.prompt_formatter import PromptFormatterOutput
from base.prompt_format_retrieve import (
    ChatRetrieverPromptInput,
    RetrieverPromptFormatter,
)
from base.tokenizers import StarCoder<PERSON>pecialTokens, Tokenizer
from research.core.all_prompt_formatters import register_prompt_formatter
from research.core.model_input import ModelInput
from research.core.prompt_formatters import AbstractPromptFormatter
from research.core.types import Chunk, Document


class ChunkSet:
    def __init__(self):
        self.chunks = []
        self.length = 0

    def add_chunk(self, chunk, max_length: int) -> bool:
        new_chunks = []

        for c in self.chunks:
            if c.touches(chunk):
                chunk = c.merge(chunk)
            else:
                new_chunks.append(c)
        new_chunks.append(chunk)
        new_length = sum(c.length for c in new_chunks)

        if new_length > max_length:
            return False

        new_chunks = sorted(
            new_chunks, key=lambda c: (c.parent_doc.path, c.char_offset)
        )
        self.chunks = new_chunks
        self.length = new_length
        return True


def select_chunk_lines(doc: Document, line_offset: int, length_in_lines: int) -> Chunk:
    lines = doc.text.splitlines(keepends=True)

    if line_offset + length_in_lines > len(lines):
        length_in_lines = len(lines) - line_offset
    if line_offset < 0:
        length_in_lines += line_offset
        line_offset = 0

    if (
        length_in_lines < 0
        or line_offset < 0
        or line_offset + length_in_lines > len(lines)
    ):
        raise ValueError(
            f"Invalid line_offset {line_offset} and length_in_lines {length_in_lines}. Total lines {len(lines)}."
        )

    prefix_lines = "".join(lines[:line_offset])
    selected_lines = "".join(lines[line_offset : line_offset + length_in_lines])

    return Chunk(
        id=f"{doc.path}_{line_offset}_{length_in_lines}",
        text=selected_lines,
        char_offset=len(prefix_lines),
        length=len(selected_lines),
        line_offset=line_offset,
        length_in_lines=length_in_lines,
        parent_doc=doc,
    )


class AdjustableChunk:
    def __init__(self, chunk, expand_left_gain, expand_right_gain):
        self.chunk = chunk
        self.expand_left_gain = expand_left_gain
        self.expand_right_gain = expand_right_gain


@register_prompt_formatter("ethanol6_query")
class Ethanol6QueryFormatter(AbstractPromptFormatter):
    """Experimental query formatter for Ethanol6 models."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    max_lines: int = -1
    """Maximum number of allowed lines. -1 means unlimited."""

    add_path: bool = False
    """Whether to prepend path to the query."""

    add_suffix: bool = False
    """Whether to add a suffix."""

    prefix_ratio: float = 0.9
    """The ratio of the prefix to the total prompt."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return get_tokenizer(self.tokenizer_name)

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix
        if self.max_lines and self.max_lines > 0:
            lines = text.splitlines(keepends=True)
            text = "".join(lines[-self.max_lines :])

        # Header tokens
        header_tokens = []

        # Optionally add path
        if self.add_path and model_input.path:
            header_tokens += self.tokenizer.tokenize(
                model_input.path, no_special_tokens=True
            )
            header_tokens.append(self.tokenizer.fim_prefix_id)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize(text, no_special_tokens=True)

        if self.add_suffix:
            suffix_tokens = [self.tokenizer.fim_suffix_id] + self.tokenizer.tokenize(
                model_input.suffix, no_special_tokens=True
            )
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens
            >= len(header_tokens) + len(prefix_tokens) + len(suffix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens + suffix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            if len(suffix_tokens) == 0:
                prompt = header_tokens + prefix_tokens[-token_budget:]
            else:
                # Prefix + suffix don't entirely fit. Split the remaining space
                # between the prefix and the suffix proportionately based on
                # prefix_ratio.
                prefix_budget = round(token_budget * self.prefix_ratio)
                suffix_budget = token_budget - prefix_budget
                if prefix_budget > len(prefix_tokens):
                    suffix_budget += prefix_budget - len(prefix_tokens)
                    prefix_budget = len(prefix_tokens)
                if suffix_budget > len(suffix_tokens):
                    prefix_budget += suffix_budget - len(suffix_tokens)
                    suffix_budget = len(suffix_tokens)
                prompt = (
                    header_tokens
                    + prefix_tokens[-prefix_budget:]
                    + suffix_tokens[:suffix_budget]
                )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        return prompt, {}


class ChatanolRetrieverPromptInput(ChatRetrieverPromptInput):
    """Input for the Chatanol query formatter."""

    def __init__(self, *args, **kwargs):
        extra = kwargs.pop("extra", [])
        super().__init__(*args, **kwargs)
        self.extra = extra


@register_prompt_formatter("chatanol_query")
class ChatanolQueryFormatter(AbstractPromptFormatter, RetrieverPromptFormatter):
    """Experimental query formatter for Chatanol models."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return get_tokenizer(self.tokenizer_name)

    def format_prompt(
        self,
        prompt_input: ChatanolRetrieverPromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Chatanol model."""
        del max_output_token_count

        model_input = ModelInput(
            prefix=prompt_input.prefix,
            suffix=prompt_input.suffix,
            path=prompt_input.path,
            extra={
                "extra": prompt_input.extra,
            },
        )
        prompt, _ = self.prepare_prompt(model_input)

        return PromptFormatterOutput([prompt])

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix

        # Header tokens
        header_tokens = []
        if self.tokenizer.bos_id is not None:
            # DeepSeek tokenizers always have a begin of sequence id.
            header_tokens.append(self.tokenizer.bos_id)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize(text, no_special_tokens=True)

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens >= len(header_tokens) + len(prefix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix does not. Trim prefix.
            prompt = header_tokens + prefix_tokens[-token_budget:]
        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        if "known_chunks" in model_input.extra:
            if (
                "add_bare_query" not in model_input.extra
                or model_input.extra["add_bare_query"]
            ):
                try:
                    ret_endofquery_token = self.tokenizer.vocab[b"<|ret-endofquery|>"]
                except KeyError:
                    # This is a fallback in case self.tokenizer.vocab is a dict[str, int]
                    ret_endofquery_token = self.tokenizer.vocab["<|ret-endofquery|>"]  # type: ignore
                prompt.append(ret_endofquery_token)

            for chunk in model_input.extra["known_chunks"]:
                prompt.extend(
                    self.tokenizer.tokenize(
                        chunk.parent_doc.path, no_special_tokens=True
                    )
                )
                prompt.append(self.tokenizer.fim_prefix_id)
                prompt.extend(
                    self.tokenizer.tokenize(chunk.text, no_special_tokens=True)
                )
                try:
                    chunk_pred_token = self.tokenizer.vocab[b"<|chunk_prediction|>"]
                except KeyError:
                    # This is a fallback in case self.tokenizer.vocab is a dict[str, int]
                    chunk_pred_token = self.tokenizer.vocab["<|chunk_prediction|>"]  # type: ignore
                prompt.append(chunk_pred_token)

        return prompt, {}


class ChatanolQueryPromptFormatter(RetrieverPromptFormatter):
    def __init__(self, tokenizer: Tokenizer, max_tokens: int = -1):
        self.tokenizer = tokenizer
        self.max_tokens = max_tokens

    def format_prompt(
        self,
        prompt_input: ChatanolRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Chatanol model."""
        text = prompt_input.prefix

        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, StarCoderSpecialTokens)

        # Header tokens
        header_tokens = []
        assert not hasattr(
            special_tokens, "begin_of_sequence"
        )  # TODO: this only works for StarCoderTokenizer
        if hasattr(special_tokens, "begin_of_sequence"):
            # DeepSeek tokenizers always have a begin of sequence id.
            header_tokens.append(special_tokens.begin_of_sequence)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(text)

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens >= len(header_tokens) + len(prefix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix does not. Trim prefix.
            prompt = header_tokens + prefix_tokens[-token_budget:]
        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        if "known_chunks" in prompt_input.extra:
            if (
                "add_bare_query" not in prompt_input.extra
                or prompt_input.extra["add_bare_query"]
            ):
                prompt.append(special_tokens.end_of_query)

            for chunk in prompt_input.extra["known_chunks"]:
                prompt.extend(self.tokenizer.tokenize_safe(chunk.parent_doc.path))
                prompt.append(special_tokens.fim_prefix)
                prompt.extend(self.tokenizer.tokenize_safe(chunk.text))
                prompt.append(special_tokens.chunk_prediction)

        # In production prompt formatters, a trailing end-of-query token is required.
        prompt.append(special_tokens.end_of_query)
        return PromptFormatterOutput([prompt])


@register_prompt_formatter("chatanol_secondary_query")
class ChatanolSecondaryQueryFormatter(AbstractPromptFormatter):
    """Experimental query formatter for Chatanol models."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return get_tokenizer(self.tokenizer_name)

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix

        # Header tokens
        header_tokens = []
        if self.tokenizer.bos_id is not None:
            # DeepSeek tokenizers always have a begin of sequence id.
            header_tokens.append(self.tokenizer.bos_id)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize(text, no_special_tokens=True)

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens >= len(header_tokens) + len(prefix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix does not. Trim prefix.
            prompt = header_tokens + prefix_tokens[-token_budget:]
        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        if (
            "known_chunks" in model_input.extra
            and len(model_input.extra["known_chunks"]) > 0
        ):
            for chunk in model_input.extra["known_chunks"]:
                prompt.extend(
                    self.tokenizer.tokenize(
                        chunk.parent_doc.path, no_special_tokens=True
                    )
                )
                prompt.append(self.tokenizer.fim_prefix_id)
                prompt.extend(
                    self.tokenizer.tokenize(chunk.text, no_special_tokens=True)
                )
                prompt.append(self.tokenizer.pad_id)

        return prompt, {}

from dataclasses import dataclass, field
from typing import Optional

from base.prompt_format.util import head_n, trailing_n
from base.prompt_format_completion import (
    PromptFormatterOutput,
    TokenApportionmentConfig,
)
from base.prompt_format_retrieve import (
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    RetrieverPromptFormatter,
)
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import _split_budget
from base.tokenizers import (
    DeepSeekCoderSpecialTokens,
    RetrievalSpecialTokens,
    StarCoderSpecialTokens,
    Tokenizer,
)
from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput


class Ethanol7RetrieverPromptInput(ChatRetrieverPromptInput):
    """Input for the Chatanol query formatter."""

    def __init__(self, *args, **kwargs):
        extra = kwargs.pop("extra", [])
        super().__init__(*args, **kwargs)
        self.extra = extra


class Ethanol7ChatQueryPromptFormatter(
    RetrieverPromptFormatter[ChatanolRetrieverPromptInput]
):
    def __init__(self, tokenizer: Tokenizer, max_tokens: int = -1):
        self.tokenizer = tokenizer
        self.max_tokens = max_tokens

    def format_prompt(
        self,
        prompt_input: Ethanol7RetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Chatanol model."""
        text = prompt_input.prefix

        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, StarCoderSpecialTokens)

        # Header tokens
        header_tokens = []
        header_tokens.append(special_tokens.query_chat)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(text)

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens >= len(header_tokens) + len(prefix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix does not. Trim prefix.
            prompt = header_tokens + prefix_tokens[-token_budget:]
        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        # TODO: check or validate max_tokens
        if "known_chunks" in prompt_input.extra:
            prompt.append(special_tokens.end_of_query)

            for chunk in prompt_input.extra["known_chunks"]:
                prompt.extend(self.tokenizer.tokenize_safe(chunk.parent_doc.path))
                prompt.append(special_tokens.fim_prefix)
                prompt.extend(self.tokenizer.tokenize_safe(chunk.text))
                prompt.append(special_tokens.chunk_prediction)

        # In production prompt formatters, a trailing end-of-query token is required.
        prompt.append(special_tokens.end_of_query)
        return PromptFormatterOutput([prompt])


@dataclass
class Ethanol7CompletionRetrieverPromptInput(CompletionRetrieverPromptInput):
    extra: dict = field(default_factory=dict)


class Ethanol7CompletionQueryFormatter(
    RetrieverPromptFormatter[Ethanol7CompletionRetrieverPromptInput]
):
    """The query formatter for Ethanol6 embedding models."""

    input_type = Ethanol7CompletionRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
        add_suffix: bool,
        prefix_suffix_budget_fraction: float = 0.667,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_path = add_path
        self.add_suffix = add_suffix
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.prefix_suffix_budget_fraction = prefix_suffix_budget_fraction

        self.preamble = (
            [
                tokenizer.special_tokens.begin_sequence,
                tokenizer.special_tokens.query_code_completion,
            ]
            if isinstance(tokenizer.special_tokens, DeepSeekCoderSpecialTokens)
            else [tokenizer.special_tokens.query_code_completion]
        )

    def format_prompt(
        self,
        prompt_input: Ethanol7CompletionRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Ethanol6 model."""
        if not self.apportionment_config:
            raise ValueError(
                "Apportionment configuration is required for Ethanol6 query formatter."
            )
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_query
        )
        if max_tokens < 0:
            raise ValueError(
                "Inconsistent prompt configuration:"
                f"max_content_len={self.apportionment_config.max_content_len}"
            )

        # Header tokens
        header_tokens = []

        # The preamble contains things like DeepSeek's begin_sequence token.
        header_tokens += self.preamble

        # Optionally add path
        if self.add_path and prompt_input.path:
            header_tokens += self.tokenizer.tokenize_safe(prompt_input.path)
            header_tokens.append(self.special_tokens.fim_prefix)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(prompt_input.prefix)

        # Optionally add suffix
        if self.add_suffix:
            suffix_tokens = [
                self.special_tokens.fim_suffix
            ] + self.tokenizer.tokenize_safe(prompt_input.suffix)
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if max_tokens >= len(header_tokens) + len(prefix_tokens) + len(suffix_tokens):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens + suffix_tokens

        elif max_tokens > len(header_tokens):
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            prefix_budget, suffix_budget = _split_budget(
                token_budget=max_tokens - len(header_tokens),
                prefix_fraction=self.prefix_suffix_budget_fraction,
                prefix_len=len(prefix_tokens),
                suffix_len=len(suffix_tokens),
            )

            prompt = (
                header_tokens
                + trailing_n(prefix_tokens, prefix_budget)
                + head_n(suffix_tokens, suffix_budget)
            )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = head_n(header_tokens, max_tokens)

        # TODO: check or validate max_tokens
        if "known_chunks" in prompt_input.extra:
            prompt.append(self.special_tokens.end_of_query)

            for chunk in prompt_input.extra["known_chunks"]:
                prompt.extend(self.tokenizer.tokenize_safe(chunk.parent_doc.path))
                prompt.append(self.special_tokens.fim_prefix)
                prompt.extend(self.tokenizer.tokenize_safe(chunk.text))
                prompt.append(self.special_tokens.chunk_prediction)

        prompt += [self.special_tokens.end_of_query]
        return PromptFormatterOutput([prompt])

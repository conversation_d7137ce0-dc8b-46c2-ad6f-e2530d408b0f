# Agent Tools

Command-line tools for interacting with remote agents and authentication.

## Recommended Usage

For the best experience with these tools, follow these steps:

1. **Deploy in your dev environment**
   ```bash
   bazel run //services/deploy:dev_deploy -- --services chat agents default remote_agents_all --operation Apply
   ```

2. **Sign in via the dev_deploy_signin.js script**
   ```bash
   ./dev_deploy_signin.js
   ```
   This will:
   - Read your username from ~/.augment/user.json
   - Generate an OAuth token
   - Save it to ~/.augment/dev-deploy-oauth2-token.txt

3. **Interact with remote_agent_cli.py**
   - Use it directly from the command line:
     ```bash
     ./remote_agent_cli.py list
     ```
   - Or use it via Augment Agent for a more interactive experience

## Remote Agent CLI

A command-line tool for interacting with remote agents.

### Features

- Create remote agents
- List all remote agents
- Get chat history for a remote agent
- Chat with remote agents (streaming and non-streaming)
- Delete remote agents
- Interrupt remote agents

## Usage

```bash
# List all remote agents
./remote_agent_cli.py list

# Create a new remote agent and wait for it to be ready
./remote_agent_cli.py create --repository-url "https://github.com/igor0/augment" --git-ref "main" --wait

# Get chat history for a remote agent starting from a specific sequence ID
./remote_agent_cli.py history <agent-id> --last-sequence-id <sequence-id>

# Chat with a remote agent and wait for the response
./remote_agent_cli.py chat <agent-id> "What can you help me with?" --wait

# Chat with a remote agent without waiting
./remote_agent_cli.py chat <agent-id> "What else can you help me with?" --wait

# Interrupt a remote agent
./remote_agent_cli.py interrupt <agent-id>

# Get chat history for a remote agent
./remote_agent_cli.py history <agent-id>

# Delete a remote agent
./remote_agent_cli.py delete <agent-id>
```

## Commands

- `list`: List all remote agents
- `create`: Create a new remote agent
- `history <agent-id>`: Get chat history for a remote agent
- `chat <agent-id> <message>`: Send a message to a remote agent
- `delete <agent-id>`: Delete a remote agent
- `interrupt <agent-id>`: Interrupt a remote agent

## Common Options

- `--api-url <url>`: API URL (default: constructed from username in ~/.augment/user.json)
- `--token-file <file>`: File containing the API token (default: ~/.augment/dev-deploy-oauth2-token.txt)

## Create Command Options

- `--system-prompt <prompt>`: System prompt for the new agent
- `--initial-message <message>`: Initial message for the new agent
- `--repository-url <url>`: Repository URL for the new agent
- `--git-ref <ref>`: Git reference for the new agent
- `--model <model>`: Model for the new agent
- `--wait`: Wait for the agent to be ready before returning
- `--wait-timeout <seconds>`: Maximum time to wait for the agent to be ready (default: 5 minutes)

## History Command Options

- `--last-sequence-id <id>`: Last processed sequence ID for chat history

## Chat Command Options

- `--wait`: Wait for the agent to respond before returning
- `--wait-timeout <seconds>`: Maximum time to wait for the agent to respond (default: 5 minutes)

## Requirements

- Python 3.11 or higher
- Augment client library

## Notes

- The streaming API is still under development and may not work correctly in all environments.
- The chat functionality (both streaming and non-streaming) may not return responses in some environments.

## Dev Deploy Sign-in

A Node.js script for generating OAuth tokens for authentication with the dev deployment of the Augment API.

### Usage

```bash
# Run the script
./dev_deploy_signin.js
```

The script will:
1. Read your username from ~/.augment/user.json
2. Construct the namespace as `dev-{username}`
3. Construct the auth server URL as `https://auth-central.{namespace}.us-central1.dev.augmentcode.com`
4. Generate a PKCE code verifier and challenge
5. Provide a URL to open in your browser for authentication
6. Ask for the authentication code received after login
   - Handles both plain code and JSON objects containing a code property
7. Exchange the code for an access token
8. Write the token to ~/.augment/dev-deploy-oauth2-token.txt

### Requirements

- Node.js

{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Determined K8s Dashboard"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load the Dashboard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from internal import determined_k8s_dash\n", "dash = determined_k8s_dash.load()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Nodes Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dash.nodes_summary()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Pods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dash.pods()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dash.nodes()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Containers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dash.containers()"]}], "metadata": {"kernelspec": {"display_name": "augment_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
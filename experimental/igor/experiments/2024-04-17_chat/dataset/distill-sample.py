# Imports

import dataclasses
import json
import math
import random
import time
from functools import partial
from types import SimpleNamespace
from typing import Any, Dict, List, Sequence

import torch
import torch.nn.functional as torchF

from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter
from research.core.model_input import ModelInput
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import create_model
from research.retrieval.types import Chunk, Document

# GLOBAL CONFIGURATION --------------------------------------------------------

ETHANOL_VERSION = "chatanol/chatanol1-07"
TEMP_BUCKET_URI = "s3a://augment-temporary/igor/"
BUCKET_URI = "s3a://igor-dev-bucket/"
STAGE2_URI = f"{BUCKET_URI}{ETHANOL_VERSION}/02_with_retrieved_chunks_sample"
STAGE3_URI = f"{BUCKET_URI}{ETHANOL_VERSION}/03_with_ppl_scores_sample.6"

# CHUNK RETRIEVAL CONFIGURATION -----------------------------------------------

retrieval_config = SimpleNamespace(
    **{
        "retriever": {
            "scorer": {
                "name": "ethanol",
                "checkpoint_path": "ethanol/ethanol3-01.11_b8192_w8_tg0.01",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "ethanol3_query",
                "max_lines": 20,
                "add_path": True,
                "retokenize": True,
            },
            "document_formatter": {
                "name": "simple_document",
                "add_path": True,
            },
        },
        "keep_full_files": True,
        "num_retrieved_chunks": 127,
        "num_retrieved_extra": 128,
        "random_seed": 74912,
    }
)

# DISTILLATION CONFIGURATION --------------------------------------------------

# Configure the distilled model
model_config = {
    "checkpoint_path": "/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-6.7b-instruct/",
    "name": "deepseek_coder_instruct_hf",
}

scorer_config = {
    "batchsize": 1,
    "max_chunks": 3,
}

prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

### Instruction:
{%- for chunk in retrieved_chunks %}
Consider the following excerpt from {{chunk.parent_doc.path}}:
```
{{chunk.text}}
```

{%- endfor %}

{{message}}

### Response:
"""

PATH_COLUMN = "max_stars_repo_path"
REPO_COLUMN = "max_stars_repo_name"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
PROMPT_COLUMN = "prompt_tokens"
SIZE_COLUMN = "size"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
FILE_LANG_COLUMN = "langpart"

# TOKENIZATION CONFIGURATION --------------------------------------------------


dataset_config = SimpleNamespace(
    seq_length=1024,
    num_validation_samples=65536,
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    retrieved_docs=127,
    allow_doc_clipping=False,
)

query_prompt_formatter_config = {
    "name": "ethanol6_query",
    "max_tokens": dataset_config.seq_length - 1,
    "tokenizer_name": dataset_config.tokenizer_name,
}

key_prompt_formatter_config = {
    "name": "ethanol6_document",
    "max_tokens": dataset_config.doc_seq_length - 1,
    "add_path": True,
    "tokenizer_name": dataset_config.tokenizer_name,
}

# UTILS IMPLEMENTATION --------------------------------------------------------


def chunk_to_dict(chunk: Chunk, keep_full_files: bool) -> dict[str, Any]:
    chunk_dict = {
        "id": chunk.id,
        "text": chunk.text,
        "parent_doc": {
            "id": chunk.parent_doc.id,
            "path": chunk.parent_doc.path,
            # WARNING: just storing empty string if we don't want to store file
            "text": chunk.parent_doc.text if keep_full_files else "",
            # Not supporting meta field
        },
        "char_offset": chunk.char_offset,
        "length": chunk.length,
        "line_offset": chunk.line_offset,
        "length_in_lines": chunk.length_in_lines,
        # Not supporting meta field
    }
    return chunk_dict


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], keep_full_files: bool
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, keep_full_files) for chunk in retrieved_chunks]
    )


def serialize_retrieved_chunks_full_n(retrieved_chunks: Sequence[Chunk], n: int) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, i < n) for i, chunk in enumerate(retrieved_chunks)]
    )


def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:
    def to_chunk(dict_: Dict[str, Any]) -> Chunk:
        return Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
        )

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


class Timer:
    def __init__(self):
        self.start_time = 0.0
        self.measurements = {}

    def start(self):
        self.start_time = time.time()

    def stop(self, name):
        self.measurements[name] = {"time": time.time() - self.start_time}

    def log(self):
        for name, duration in self.measurements.items():
            print(f"{name} took {duration:.2f} seconds")

    def get(self):
        return self.measurements


# STAGES IMPLEMENTATION -------------------------------------------------------


def stage3():
    def score_chunks(
        model,
        model_input: ModelInput,
        chunk_lists: list[list[Chunk]],
        batch_size: int,
        name: str,
    ) -> list[float]:
        scores: list[float] = []

        for i in range(0, len(chunk_lists), batch_size):
            try:
                output = model.forward_pass(
                    [
                        dataclasses.replace(model_input, retrieved_chunks=chunk_list)
                        for chunk_list in chunk_lists[i : (i + batch_size)]
                    ]
                )
            except torch.cuda.OutOfMemoryError as e:
                ranges = [
                    [f"{c.parent_doc.path} {c.range}" for c in chunk_list]
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]

                tokens = [
                    len(
                        model.prompt_formatter.prepare_prompt(
                            dataclasses.replace(
                                model_input, retrieved_chunks=chunk_list
                            )
                        )[0]
                    )
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]
                raise RuntimeError(
                    f"[{name}] Out of memory on chunklist: {ranges}, {tokens} tokens"
                ) from e

            scores += [
                -torchF.cross_entropy(
                    o.logits[o.target_mask],
                    o.label_tokens[o.target_mask],
                    reduction="mean",
                ).item()
                for o in output
            ]

        return scores

    def compute_ppl(batch, model_config, scorer_config):
        def select_chunk_lines(
            doc: Document, chunk, line_offset: int, length_in_lines: int
        ) -> Chunk:
            lines = doc.text.splitlines(keepends=True)

            if line_offset + length_in_lines > len(lines):
                length_in_lines = len(lines) - line_offset
            if line_offset < 0:
                length_in_lines += line_offset
                line_offset = 0
            if (
                length_in_lines < 0
                or line_offset < 0
                or (line_offset + length_in_lines) > len(lines)
            ):
                raise ValueError(
                    f"Invalid line_offset {line_offset} and length_in_lines {length_in_lines}. File length: {len(lines)}."
                )

            prefix_lines = "".join(lines[:line_offset])
            selected_lines = "".join(lines[line_offset : line_offset + length_in_lines])

            return Chunk(
                id=f"{doc.path}_{line_offset}_{length_in_lines}",
                text=selected_lines,
                char_offset=len(prefix_lines),
                length=len(selected_lines),
                line_offset=line_offset,
                length_in_lines=length_in_lines,
                parent_doc=doc,
            )

        def expand_chunk(chunk: Chunk, expand_factor: float, to_left: bool):
            if expand_factor < 1:
                raise ValueError(f"Invalid expand_factor {expand_factor}")

            expand_by = round(chunk.length_in_lines * (expand_factor - 1))
            return select_chunk_lines(
                chunk.parent_doc,
                chunk,
                chunk.line_offset - (expand_by if to_left else 0),
                chunk.length_in_lines + expand_by,
            )

        global cached_scorer
        if "cached_scorer" not in globals():  # Construct a scorer
            print("Constructing the model...")
            cached_scorer = create_model(model_config)
            cached_scorer.prompt_formatter = ChatTemplateBasedPromptFormatter(
                template=prompt_template
            )
            cached_scorer.load()

            # Load the reranking model
            print("Loading the model...")
            cached_scorer.load()

        def apply_compute_ppl(
            question,
            answer,
            retrieved_chunks,
        ):
            model_input = ModelInput(
                retrieved_chunks=deserialize_retrieved_chunks(retrieved_chunks),
                target=answer,
                extra={
                    "chat_history": [],
                    "message": question,
                    "selected_code": "",
                    "path": "",
                },
            )

            def score_chunks_f(chunk_lists, timer, name) -> list[float]:
                timer.start()
                scores = score_chunks(
                    model=cached_scorer,
                    model_input=model_input,
                    chunk_lists=chunk_lists,
                    batch_size=scorer_config["batchsize"],
                    name=name,
                )
                timer.stop(name)
                return scores

            # Constants
            max_prompt_chunks = 32
            max_prompt_chars = 15000
            expand_prob = 0.25
            default_expand_factor = 3

            # Construct the secondary retrieval prompt by expanding some chunks
            secondary_chunks = []
            for chunk in deserialize_retrieved_chunks(retrieved_chunks)[
                :max_prompt_chunks
            ]:
                max_chunk_chars = max_prompt_chars / default_expand_factor
                if len(chunk.range) > max_chunk_chars:
                    continue

                if random.random() < expand_prob:
                    max_expand_factor = max_chunk_chars / len(chunk.range)
                    expand_factor = math.exp(
                        random.random() * math.log(max_expand_factor)
                    )
                    chunk = expand_chunk(
                        chunk, expand_factor, to_left=(random.random() < 0.5)
                    )

                secondary_chunks.append(chunk)

            prompt_token_count = 0
            for i, chunk in enumerate(secondary_chunks):
                if prompt_token_count + len(chunk.range) > max_prompt_chars:
                    secondary_chunks = secondary_chunks[:i]
                    break

                prompt_token_count += len(chunk.range)

            retr_chunks = deserialize_retrieved_chunks(retrieved_chunks)

            timer = Timer()
            timer.start()
            secondary_prefix = score_chunks_f(
                [secondary_chunks[0:i] for i in range(len(secondary_chunks) + 1)],
                timer,
                "secondary_prefix",
            )

            score_chunk_gain = [
                secondary_prefix[i + 1] - secondary_prefix[i]
                for i in range(len(secondary_chunks))
            ]

            secondary_chunk_scores = score_chunks_f(
                [[c] for c in secondary_chunks],
                timer,
                "secondary_chunk_scores",
            )
            secondary_expand_left = score_chunks_f(
                [
                    [expand_chunk(c, default_expand_factor, to_left=True)]
                    for c in secondary_chunks
                ],
                timer,
                "secondary_expand_left",
            )
            secondary_expand_right = score_chunks_f(
                [
                    [expand_chunk(c, default_expand_factor, to_left=False)]
                    for c in secondary_chunks
                ],
                timer,
                "secondary_expand_right",
            )

            def argmax(data):
                return max(range(len(data)), key=lambda idx: data[idx])

            best_chunk = secondary_chunks[argmax(secondary_chunk_scores)]
            # good_secondary_chunks = [c for i, c in enumerate(secondary_chunks) if score_chunk_gain[i] > 0.0]
            # good_secondary_chunks = secondary_chunks

            def sub(a, b):
                return [a[i] - b[i] for i in range(len(a))]

            scores = {
                "scores": score_chunks_f(
                    [[c] for c in retr_chunks],
                    timer,
                    "scores",
                ),
                "secondary": {
                    "scores": score_chunks_f(
                        [[best_chunk, c] for c in retr_chunks],
                        timer,
                        "secondary_scores",
                    ),
                    "chunks": serialize_retrieved_chunks(
                        secondary_chunks, keep_full_files=False
                    ),
                    "gain": score_chunk_gain,
                    "gain_expand_left": sub(
                        secondary_expand_left, secondary_chunk_scores
                    ),
                    "gain_expand_right": sub(
                        secondary_expand_right, secondary_chunk_scores
                    ),
                    "prefix": secondary_prefix,
                },
            }
            scores["timer"] = timer.get()
            return json.dumps(scores)

        batch["ppl_scores"] = batch.apply(lambda row: apply_compute_ppl(**row), axis=1)
        return batch

    spark_gpu = k8s_session(
        name="igor-distill-compute-ppl-sample",
        max_workers=32,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="A100_NVLINK_80GB",
    )

    map_parquet.apply_pandas(
        spark_gpu,
        partial(compute_ppl, model_config=model_config, scorer_config=scorer_config),
        input_path=STAGE2_URI,
        output_path=STAGE3_URI,
        timeout=72000,
    )

    spark_gpu.stop()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("stages", type=int, help="stage to run", nargs="+")
    args = parser.parse_args()

    stages = [
        (3, stage3),
    ]

    for stage_id, stage_f in stages:
        if stage_id in args.stages:
            stage_f()

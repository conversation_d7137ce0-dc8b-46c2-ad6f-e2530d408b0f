{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "PATHS = [f\"scores{i+1}.jsonl\" for i in range(6)]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["data = []\n", "for path in PATHS:\n", "    with open(path, \"r\") as f:\n", "        data.append([json.loads(line) for line in f])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sample_file1 = data[5]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["##### Question:\n", "What is the minimum word length for hyphenation?\n", "##### Answer:\n", "The minimum word length for hyphenation is defined in the `packages/hyphenated/src/defaults.js` file and is set to 4 by default. This means that words with 3 or fewer characters will not be hyphenated. You can customize the minimum word length by passing a `minWordLength` option to the `hyphenated` function: \n", "\n", "```javascript\n", "hyphenated('some-text', { minWordLength: 6 }); \n", "```\n", "\n", "In this example, only words with 6 or more characters will be hyphenated.\n", "--- \n", "#\n", "##### Scores:\n", "Best -0.908784806728363 12\n", "0 -1.0152186155319214 -0.9828445911407471 packages/hyphenated/src/index.test.js 60 75\n", "1 -1.1810085773468018 -1.0408506393432617 packages/hyphenated/src/hyphenateByPoints.js 0 12\n", "2 -0.9614456295967102 -0.9454901218414307 packages/hyphenated/src/index.test.js 0 30\n", "3 -1.1894432306289673 -0.9987424612045288 packages/hyphenated/src/hyphenateWord.js 0 30\n", "4 -1.1911022663116455 -1.0119608640670776 packages/hyphenated/src/isHyphenationPoint.js 0 1\n", "5 -1.1056932210922241 -0.9171679019927979 packages/hyphenated/README.md 0 19\n", "6 -1.0102750062942505 -0.9495655298233032 packages/hyphenated/src/index.test.js 30 60\n", "7 -1.1109856367111206 -0.9209451675415039 packages/hyphenated-en-gb/README.md 0 17\n", "8 -1.1745421886444092 -1.0165398120880127 packages/hyphenated/src/hyphenateWord.test.js 30 45\n", "9 -1.1372212171554565 -0.9139060378074646 packages/hyphenated-fr/README.md 0 17\n", "10 -1.0418897867202759 -1.0094579458236694 packages/hyphenated/src/defaults.js 0 7\n", "11 -0.9906739592552185 -0.9455727338790894 packages/hyphenated/src/createHyphenator.js 0 30\n", "12 -1.097611427307129 -0.908784806728363 packages/hyphenated-en-us/README.md 0 24\n", "13 -1.114477515220642 -0.9900485873222351 packages/hyphenated/src/hyphenateWord.test.js 0 30\n", "14 -1.1566449403762817 -0.981514036655426 packages/hyphenated/src/hyphenateException.test.js 30 60\n", "15 -1.1719071865081787 -1.0151151418685913 packages/hyphenated/src/isHyphenationPoint.test.js 0 27\n", "16 -1.1692140102386475 -1.0020725727081299 packages/hyphenated/src/hyphenateByPoints.test.js 0 14\n", "17 -1.0243204832077026 -0.966964840888977 packages/hyphenated/src/index.js 0 5\n", "18 -1.0450550317764282 -0.9488709568977356 packages/hyphenated/src/parseText.test.js 210 240\n", "19 -1.2145525217056274 -0.9638992547988892 packages/hyphenated-de/src/index.js 0 30\n", "20 -1.0706775188446045 -0.9682720303535461 packages/hyphenated/src/parseText.test.js 60 90\n", "21 -1.1056010723114014 -0.9471228122711182 packages/hyphenated/src/parseText.test.js 240 270\n", "22 -1.0832676887512207 -0.9564032554626465 packages/hyphenated/src/hyphenateException.js 0 8\n", "23 -1.1412888765335083 -0.9970254302024841 packages/hyphenated/src/hyphenators.js 0 13\n", "24 -1.0942976474761963 -0.9701372385025024 packages/hyphenated/src/parseText.test.js 90 120\n", "25 -1.1662683486938477 -0.9523574709892273 packages/hyphenated-en-gb/src/index.js 4620 4650\n", "26 -1.1468223333358765 -1.0072264671325684 packages/hyphenated/src/decodePattern.js 0 8\n", "27 -1.0768780708312988 -0.954866349697113 packages/hyphenated/src/createHyphenator.js 30 34\n", "28 -1.0802311897277832 -0.9601662158966064 packages/hyphenated/src/parseText.js 60 79\n", "29 -1.0896707773208618 -0.9538108706474304 packages/hyphenated/src/hyphenators.test.js 0 30\n", "30 -1.1941466331481934 -0.9621886610984802 packages/hyphenated-en-gb/src/index.js 0 30\n", "31 -1.1433333158493042 -0.9788001775741577 packages/hyphenated-de/src/index.js 24540 24554\n", "32 -1.140381932258606 -0.9545994400978088 packages/hyphenated/src/decodePattern.test.js 60 90\n", "33 -1.182677149772644 -0.9494143724441528 packages/hyphenated-fr/src/index.js 0 30\n", "34 -1.1416430473327637 -0.9881517291069031 packages/hyphenated/src/utils.js 0 5\n", "35 -1.0836291313171387 -0.9520777463912964 packages/hyphenated/src/parseText.test.js 120 150\n", "36 -1.0838818550109863 -0.959072470664978 packages/hyphenated/src/parseText.test.js 270 283\n", "37 -1.0540224313735962 -0.9590970277786255 packages/hyphenated/src/parseText.js 0 30\n", "38 -1.151794195175171 -0.9728193283081055 packages/hyphenated/src/hyphenators.test.js 30 34\n", "39 -1.1711536645889282 -0.9833266139030457 packages/hyphenated-en-gb/src/index.js 3780 3810\n", "40 -1.1706584692001343 -0.9692800641059875 packages/hyphenated-en-gb/src/index.js 8520 8550\n", "41 -1.0749975442886353 -0.9446352124214172 packages/hyphenated/src/parseText.test.js 180 210\n", "42 -1.0803072452545166 -0.9516900181770325 packages/hyphenated/src/parseText.test.js 0 30\n", "43 -1.180808186531067 -0.9562594294548035 packages/hyphenated-de/src/index.js 10290 10320\n", "44 -1.1776630878448486 -0.9841485619544983 packages/hyphenated-en-gb/src/index.js 8280 8310\n", "45 -1.0627937316894531 -0.9531953930854797 packages/hyphenated/src/parseText.js 30 60\n", "46 -1.0905001163482666 -0.9509835243225098 packages/hyphenated/src/parseText.test.js 30 60\n", "47 -1.1850022077560425 -0.9497542977333069 packages/hyphenated-de/src/index.js 18780 18810\n", "48 -1.1655844449996948 -0.9573003053665161 packages/hyphenated-en-gb/src/index.js 3300 3330\n", "49 -1.1671664714813232 -0.9420292973518372 packages/hyphenated-de/src/index.js 3900 3930\n", "50 -1.1147340536117554 -0.951564371585846 packages/hyphenated-en-gb/public/index.js 0 3\n", "51 -1.1594372987747192 -0.9515272378921509 packages/hyphenated-de/src/index.js 10260 10290\n", "52 -1.1072512865066528 -0.9628067016601562 packages/hyphenated-en-us/public/index.js 0 3\n", "53 -1.1709672212600708 -0.9874780178070068 packages/hyphenated-fr/src/index.js 1140 1160\n", "54 -1.0304168462753296 -0.9602222442626953 packages/hyphenated/index.js 0 1\n", "55 -1.153369426727295 -0.9477203488349915 packages/hyphenated-de/src/index.js 8490 8520\n", "56 -1.1061755418777466 -0.94265216588974 packages/hyphenated-cs/public/index.js 0 3\n", "57 -1.1573365926742554 -0.9542754888534546 packages/hyphenated-de/src/index.js 10230 10260\n", "58 -1.168982744216919 -0.9526980519294739 packages/hyphenated-de/src/index.js 11790 11820\n", "59 -1.0840866565704346 -0.9339196085929871 packages/hyphenated-en-gb/index.js 0 2\n", "60 -1.147304892539978 -0.963626503944397 packages/hyphenated/src/hyphenateException.test.js 0 30\n", "61 -1.1734418869018555 -0.9414292573928833 packages/hyphenated-de/src/index.js 8400 8430\n", "62 -1.1390851736068726 -0.9572194814682007 packages/hyphenated/src/decodePattern.test.js 90 120\n", "63 -1.1201171875 -0.9464598298072815 packages/hyphenated-de/public/index.js 0 3\n", "64 -1.046726107597351 -0.9508562088012695 packages/hyphenated/public/index.js 0 3\n", "65 -1.1865015029907227 -0.9562176465988159 packages/hyphenated-en-gb/src/index.js 3750 3780\n", "66 -1.161258578300476 -0.950525164604187 packages/hyphenated-de/src/index.js 10320 10350\n", "67 -1.157081961631775 -0.9491326808929443 packages/hyphenated-de/src/index.js 20070 20100\n", "68 -1.1761901378631592 -0.9512271881103516 packages/hyphenated-de/src/index.js 2370 2400\n", "69 -1.1695427894592285 -0.9532093405723572 packages/hyphenated-de/src/index.js 4710 4740\n", "70 -1.1597472429275513 -0.9572464227676392 packages/hyphenated-de/src/index.js 450 480\n", "71 -1.0844544172286987 -0.9403008818626404 packages/hyphenated-en-us/index.js 0 2\n", "72 -1.1827425956726074 -0.9681777954101562 packages/hyphenated-en-gb/src/index.js 6960 6990\n", "73 -1.1757464408874512 -0.9633670449256897 packages/hyphenated-en-gb/src/index.js 7470 7500\n", "74 -1.1175001859664917 -0.9556341171264648 packages/hyphenated-fr/public/index.js 0 3\n", "75 -1.163271427154541 -0.9421275854110718 packages/hyphenated-de/src/index.js 20160 20190\n", "76 -1.1793692111968994 -0.9464900493621826 packages/hyphenated-de/src/index.js 1350 1380\n", "77 -1.1667795181274414 -0.9502294659614563 packages/hyphenated-de/src/index.js 4830 4860\n", "78 -1.1545937061309814 -0.9474180340766907 packages/hyphenated-de/src/index.js 13140 13170\n", "79 -1.170035719871521 -0.9416643381118774 packages/hyphenated-de/src/index.js 13530 13560\n", "80 -1.1022270917892456 -0.9624701738357544 packages/hyphenated/src/parseText.test.js 150 180\n", "81 -1.1839110851287842 -0.9407259225845337 packages/hyphenated-de/src/index.js 3450 3480\n", "82 -1.1669611930847168 -0.9464308619499207 packages/hyphenated-de/src/index.js 13620 13650\n", "83 -1.1690744161605835 -0.941381573677063 packages/hyphenated-de/src/index.js 3360 3390\n", "84 -1.1314456462860107 -0.9471737742424011 packages/hyphenated-de/src/index.js 20190 20220\n", "85 -1.1680066585540771 -0.9572810530662537 packages/hyphenated-de/src/index.js 21060 21090\n", "86 -1.17237389087677 -0.9669451713562012 packages/hyphenated/src/decodePattern.test.js 120 150\n", "87 -1.1610004901885986 -0.9562444686889648 packages/hyphenated-de/src/index.js 150 180\n", "88 -1.167343258857727 -0.9515460729598999 packages/hyphenated-en-gb/src/index.js 1800 1830\n", "89 -1.1679883003234863 -0.9420881867408752 packages/hyphenated-en-gb/src/index.js 2250 2280\n", "90 -1.1697413921356201 -0.9573113918304443 packages/hyphenated-en-gb/src/index.js 2700 2730\n", "91 -1.1632561683654785 -0.9469636082649231 packages/hyphenated-en-gb/src/index.js 4110 4140\n", "92 -1.157543420791626 -0.9494736790657043 packages/hyphenated-de/src/index.js 5580 5610\n", "93 -1.1662416458129883 -0.9566551446914673 packages/hyphenated-en-gb/src/index.js 4170 4200\n", "94 -1.1629705429077148 -0.9499795436859131 packages/hyphenated-de/src/index.js 11820 11850\n", "95 -1.1565978527069092 -0.9431983828544617 packages/hyphenated-de/src/index.js 12990 13020\n", "96 -1.1485010385513306 -0.9494497776031494 packages/hyphenated-de/src/index.js 9870 9900\n", "97 -1.1623629331588745 -0.9555784463882446 packages/hyphenated-de/src/index.js 11850 11880\n", "98 -1.1687943935394287 -0.9439132213592529 packages/hyphenated-de/src/index.js 12210 12240\n", "99 -1.1700347661972046 -0.9589084386825562 packages/hyphenated-en-gb/src/index.js 3690 3720\n", "100 -1.1601104736328125 -0.9687559008598328 packages/hyphenated-en-gb/src/index.js 8220 8250\n", "101 -1.173628568649292 -0.9883045554161072 packages/hyphenated/src/decodePattern.test.js 150 171\n", "102 -1.1724766492843628 -0.9385862350463867 packages/hyphenated-en-gb/src/index.js 4530 4560\n", "103 -1.1894440650939941 -0.9451763033866882 packages/hyphenated-de/src/index.js 23520 23550\n", "104 -1.1513720750808716 -0.9478358030319214 packages/hyphenated-de/src/index.js 7260 7290\n", "105 -1.1642992496490479 -0.9351333379745483 packages/hyphenated-de/src/index.js 19020 19050\n", "106 -1.1576687097549438 -0.9552385807037354 packages/hyphenated-de/src/index.js 7530 7560\n", "107 -1.1747757196426392 -0.958091139793396 packages/hyphenated-en-gb/src/index.js 7170 7200\n", "108 -1.1726024150848389 -0.9556214809417725 packages/hyphenated-de/src/index.js 4050 4080\n", "109 -1.1544569730758667 -0.9417279362678528 packages/hyphenated-en-gb/src/index.js 4650 4680\n", "110 -1.0811525583267212 -0.9349508881568909 packages/hyphenated-cs/index.js 0 2\n", "111 -1.1742788553237915 -0.9365331530570984 packages/hyphenated-de/src/index.js 17790 17820\n", "112 -1.1646517515182495 -0.9700490236282349 packages/hyphenated-en-gb/src/index.js 3420 3450\n", "113 -1.1767486333847046 -0.9446868300437927 packages/hyphenated-de/src/index.js 4140 4170\n", "114 -1.1695141792297363 -0.9516052007675171 packages/hyphenated-de/src/index.js 9000 9030\n", "115 -1.145099401473999 -0.9541234970092773 packages/hyphenated-fr/src/index.js 210 240\n", "116 -1.1619322299957275 -0.9394283294677734 packages/hyphenated-fr/src/index.js 750 780\n", "117 -1.1615846157073975 -0.9755674600601196 packages/hyphenated-en-gb/src/index.js 240 270\n", "118 -1.1852182149887085 -0.9580428600311279 packages/hyphenated-en-gb/src/index.js 1950 1980\n", "119 -1.1528387069702148 -0.9618179798126221 packages/hyphenated-en-gb/src/index.js 5070 5100\n", "120 -1.1744444370269775 -0.9507550597190857 packages/hyphenated-de/src/index.js 8370 8400\n", "121 -1.165893316268921 -0.9534599781036377 packages/hyphenated-de/src/index.js 13170 13200\n", "122 -1.1679075956344604 -0.976170003414154 packages/hyphenated-en-gb/src/index.js 210 240\n", "123 -1.0879780054092407 -0.9352977871894836 packages/hyphenated-de/index.js 0 2\n", "124 -1.1571402549743652 -0.9456049203872681 packages/hyphenated-fr/src/index.js 330 360\n", "125 -1.160459041595459 -0.9469021558761597 packages/hyphenated-de/src/index.js 12270 12300\n", "126 -1.193755030632019 -0.9425824284553528 packages/hyphenated-de/src/index.js 13230 13260\n", "127 -1.1541783809661865 -0.9549474716186523 packages/hyphenated-en-gb/src/index.js 90 120\n", "128 -1.1779406070709229 -0.95106041431427 packages/hyphenated-en-gb/src/index.js 1470 1500\n", "129 -1.1972432136535645 -0.9895844459533691 packages/hyphenated-en-gb/src/index.js 420 450\n", "130 -1.1576062440872192 -0.9518362283706665 packages/hyphenated-en-gb/src/index.js 4740 4770\n", "131 -1.1685987710952759 -0.9569914937019348 packages/hyphenated-en-gb/src/index.js 6390 6420\n", "132 -1.1816859245300293 -0.954895555973053 packages/hyphenated-en-gb/src/index.js 6930 6960\n", "133 -1.1553869247436523 -0.9478558897972107 packages/hyphenated-de/src/index.js 10350 10380\n", "134 -1.1634186506271362 -0.9766517281532288 packages/hyphenated-en-gb/src/index.js 3810 3840\n", "135 -1.1549783945083618 -0.9577193856239319 packages/hyphenated-de/src/index.js 19800 19830\n", "136 -1.1686902046203613 -0.9588348269462585 packages/hyphenated-en-gb/src/index.js 1290 1320\n", "137 -1.166221022605896 -0.9518537521362305 packages/hyphenated-en-gb/src/index.js 6720 6750\n", "138 -1.153344988822937 -0.9544386267662048 packages/hyphenated-de/src/index.js 2580 2610\n", "139 -1.1680049896240234 -0.951160728931427 packages/hyphenated-de/src/index.js 3660 3690\n", "140 -1.173022985458374 -0.9590409398078918 packages/hyphenated-de/src/index.js 7680 7710\n", "141 -1.1634033918380737 -0.9524467587471008 packages/hyphenated-de/src/index.js 23460 23490\n", "142 -1.1934329271316528 -0.9660248160362244 packages/hyphenated-en-gb/src/index.js 3090 3120\n", "143 -1.1571458578109741 -0.9563796520233154 packages/hyphenated-de/src/index.js 240 270\n", "144 -1.1566473245620728 -0.9504759907722473 packages/hyphenated-de/src/index.js 300 330\n", "145 -1.174224615097046 -0.9359093904495239 packages/hyphenated-de/src/index.js 23700 23730\n", "146 -1.1750627756118774 -0.953407883644104 packages/hyphenated-de/src/index.js 23760 23790\n", "147 -1.1684443950653076 -0.9528376460075378 packages/hyphenated-de/src/index.js 24090 24120\n", "148 -1.164440393447876 -0.9689147472381592 packages/hyphenated-en-gb/src/index.js 4140 4170\n", "149 -1.1794310808181763 -0.9588654637336731 packages/hyphenated-en-gb/src/index.js 6990 7020\n", "150 -1.168808102607727 -0.9590414762496948 packages/hyphenated-de/src/index.js 990 1020\n", "151 -1.166193962097168 -0.9386595487594604 packages/hyphenated-de/src/index.js 4590 4620\n", "152 -1.1617437601089478 -0.9619472026824951 packages/hyphenated-de/src/index.js 540 570\n", "153 -1.1726510524749756 -0.9437458515167236 packages/hyphenated-de/src/index.js 12330 12360\n", "154 -1.1663957834243774 -0.9547924995422363 packages/hyphenated-en-gb/src/index.js 5850 5880\n", "155 -1.1762747764587402 -0.9581298828125 packages/hyphenated-en-gb/src/index.js 7290 7320\n", "156 -1.1575891971588135 -0.9538617134094238 packages/hyphenated-de/src/index.js 420 450\n", "157 -1.1729270219802856 -0.9517130255699158 packages/hyphenated-en-gb/src/index.js 3930 3960\n", "158 -1.1714411973953247 -0.9384132623672485 packages/hyphenated-de/src/index.js 4110 4140\n", "159 -1.1702159643173218 -0.9440929889678955 packages/hyphenated-de/src/index.js 4920 4950\n", "160 -1.1564239263534546 -0.9480618238449097 packages/hyphenated-de/src/index.js 12240 12270\n", "161 -1.180967926979065 -0.9606737494468689 packages/hyphenated-en-gb/src/index.js 4710 4740\n", "162 -1.160974144935608 -0.9765989184379578 packages/hyphenated/src/decodePattern.test.js 0 30\n", "163 -1.1572022438049316 -0.9471964836120605 packages/hyphenated-fr/src/index.js 390 420\n", "164 -1.177456259727478 -0.9473062753677368 packages/hyphenated-de/src/index.js 4620 4650\n", "165 -1.1677074432373047 -0.9459437131881714 packages/hyphenated-de/src/index.js 24360 24390\n", "166 -1.1831187009811401 -0.9724626541137695 packages/hyphenated-en-gb/src/index.js 3030 3060\n", "167 -1.154258131980896 -0.9451247453689575 packages/hyphenated-en-gb/src/index.js 3150 3180\n", "168 -1.1750150918960571 -0.9640657305717468 packages/hyphenated-en-gb/src/index.js 6900 6930\n", "169 -1.17500901222229 -0.9611995816230774 packages/hyphenated-en-gb/src/index.js 7050 7080\n", "170 -1.1725883483886719 -0.9847595691680908 packages/hyphenated/src/PatternTrie.js 0 30\n", "171 -1.161783218383789 -0.9375163316726685 packages/hyphenated-de/src/index.js 13080 13110\n", "172 -1.1689939498901367 -0.9755077958106995 packages/hyphenated-en-gb/src/index.js 270 300\n", "173 -1.1815193891525269 -0.9620643258094788 packages/hyphenated-en-gb/src/index.js 5970 6000\n", "174 -1.1521180868148804 -0.9295414090156555 packages/hyphenated-de/src/index.js 2400 2430\n", "175 -1.171824336051941 -0.94618159532547 packages/hyphenated-de/src/index.js 7950 7980\n", "176 -1.1568665504455566 -0.9520916938781738 packages/hyphenated-de/src/index.js 9090 9120\n", "177 -1.1692554950714111 -0.9462692141532898 packages/hyphenated-de/src/index.js 13020 13050\n", "178 -1.1694409847259521 -0.9459884166717529 packages/hyphenated-de/src/index.js 16140 16170\n", "179 -1.1780446767807007 -0.9480147957801819 packages/hyphenated-de/src/index.js 21330 21360\n", "180 -1.1686224937438965 -0.9611566662788391 packages/hyphenated-en-gb/src/index.js 7560 7590\n", "181 -1.1645221710205078 -0.9524655938148499 packages/hyphenated-fr/src/index.js 960 990\n", "182 -1.1588685512542725 -0.9413522481918335 packages/hyphenated-de/src/index.js 660 690\n", "183 -1.144112467765808 -0.9483426809310913 packages/hyphenated-de/src/index.js 6870 6900\n", "184 -1.1321370601654053 -0.9377012848854065 packages/hyphenated-de/src/index.js 16770 16800\n", "185 -1.179396152496338 -0.9530527591705322 packages/hyphenated-de/src/index.js 20250 20280\n", "186 -1.1704537868499756 -0.9451834559440613 packages/hyphenated-de/src/index.js 3840 3870\n", "187 -1.1480772495269775 -0.94832843542099 packages/hyphenated-de/src/index.js 5670 5700\n", "188 -1.168109655380249 -0.9442310333251953 packages/hyphenated-de/src/index.js 6420 6450\n", "189 -1.148193120956421 -0.9553278088569641 packages/hyphenated-de/src/index.js 17370 17400\n", "190 -1.1822478771209717 -0.965090274810791 packages/hyphenated-de/src/index.js 5070 5100\n", "191 -1.1561222076416016 -0.9446921348571777 packages/hyphenated-de/src/index.js 5880 5910\n", "192 -1.1723190546035767 -0.9583234190940857 packages/hyphenated-de/src/index.js 17190 17220\n", "193 -1.1466233730316162 -0.9595115184783936 packages/hyphenated-de/src/index.js 18090 18120\n", "194 -1.1584842205047607 -0.9491496086120605 packages/hyphenated-de/src/index.js 22380 22410\n", "195 -1.09761643409729 -0.9285094141960144 packages/hyphenated-fr/index.js 0 2\n", "196 -1.146914005279541 -0.9424031376838684 packages/hyphenated-fr/src/index.js 540 570\n", "197 -1.1607862710952759 -0.9390152096748352 packages/hyphenated-de/src/index.js 120 150\n", "198 -1.1568183898925781 -0.9526713490486145 packages/hyphenated-de/src/index.js 13500 13530\n", "199 -1.17031991481781 -0.9487416744232178 packages/hyphenated-de/src/index.js 19380 19410\n", "200 -1.1642881631851196 -0.9376553297042847 packages/hyphenated-de/src/index.js 20280 20310\n", "201 -1.1743098497390747 -0.9565739631652832 packages/hyphenated-en-gb/src/index.js 1560 1590\n", "202 -1.1684023141860962 -0.9522042870521545 packages/hyphenated-en-gb/src/index.js 6090 6120\n", "203 -1.165716290473938 -0.9615036845207214 packages/hyphenated-en-gb/src/index.js 7020 7050\n", "204 -1.1665263175964355 -0.9474774599075317 packages/hyphenated-de/src/index.js 11730 11760\n", "205 -1.178019404411316 -0.9481804370880127 packages/hyphenated-de/src/index.js 17970 18000\n", "206 -1.1502679586410522 -0.9594636559486389 packages/hyphenated-en-gb/src/index.js 7860 7890\n", "207 -1.1671264171600342 -0.9466641545295715 packages/hyphenated-de/src/index.js 2490 2520\n", "208 -1.1553150415420532 -0.9453133344650269 packages/hyphenated-de/src/index.js 5370 5400\n", "209 -1.1542185544967651 -0.9449100494384766 packages/hyphenated-de/src/index.js 5460 5490\n", "210 -1.1610240936279297 -0.9383488297462463 packages/hyphenated-de/src/index.js 19260 19290\n", "211 -1.1597890853881836 -0.9449185729026794 packages/hyphenated-de/src/index.js 23850 23880\n", "212 -1.19206702709198 -0.9542266726493835 packages/hyphenated-en-gb/src/index.js 4260 4290\n", "213 -1.155328392982483 -0.9416111707687378 packages/hyphenated-en-gb/src/index.js 4500 4530\n", "214 -1.1761759519577026 -0.9586265087127686 packages/hyphenated-en-gb/src/index.js 4590 4620\n", "215 -1.1388643980026245 -0.944517970085144 packages/hyphenated-en-gb/src/index.js 7080 7110\n", "216 -1.15874445438385 -0.9409865736961365 packages/hyphenated-de/src/index.js 4980 5010\n", "217 -1.179593563079834 -0.9527180194854736 packages/hyphenated-de/src/index.js 9060 9090\n", "218 -1.1477137804031372 -0.9423482418060303 packages/hyphenated-de/src/index.js 14340 14370\n", "219 -1.1541942358016968 -0.9547103047370911 packages/hyphenated-de/src/index.js 17430 17460\n", "220 -1.1765037775039673 -0.9505578279495239 packages/hyphenated-de/src/index.js 18630 18660\n", "221 -1.1516380310058594 -0.9532052278518677 packages/hyphenated-de/src/index.js 18900 18930\n", "222 -1.1608079671859741 -0.936867356300354 packages/hyphenated-de/src/index.js 19470 19500\n", "223 -1.1763769388198853 -0.9441041946411133 packages/hyphenated-en-gb/src/index.js 1830 1860\n", "224 -1.1654168367385864 -0.9458637833595276 packages/hyphenated-de/src/index.js 9390 9420\n", "225 -1.1603295803070068 -0.9355155229568481 packages/hyphenated-de/src/index.js 19170 19200\n", "226 -1.1490319967269897 -0.9531523585319519 packages/hyphenated-de/src/index.js 24420 24450\n", "227 -1.1691570281982422 -0.9510461091995239 packages/hyphenated-en-gb/src/index.js 1230 1260\n", "228 -1.1693576574325562 -0.9414339065551758 packages/hyphenated-de/src/index.js 7500 7530\n", "229 -1.149156093597412 -0.9377114772796631 packages/hyphenated-de/src/index.js 10530 10560\n", "230 -1.1745721101760864 -0.9467930197715759 packages/hyphenated-de/src/index.js 11610 11640\n", "231 -1.1859945058822632 -0.949503481388092 packages/hyphenated-de/src/index.js 20310 20340\n", "232 -1.1587530374526978 -0.9531926512718201 packages/hyphenated-de/src/index.js 23640 23670\n", "233 -1.189893364906311 -0.9508596658706665 packages/hyphenated-en-gb/src/index.js 4320 4350\n", "234 -1.1618801355361938 -0.9584489464759827 packages/hyphenated-de/src/index.js 30 60\n", "235 -1.1646777391433716 -0.9479572176933289 packages/hyphenated-de/src/index.js 1020 1050\n", "236 -1.1580923795700073 -0.9341988563537598 packages/hyphenated-de/src/index.js 6720 6750\n", "237 -1.1724432706832886 -0.9509516954421997 packages/hyphenated-de/src/index.js 9510 9540\n", "238 -1.1567972898483276 -0.9531764388084412 packages/hyphenated-de/src/index.js 13320 13350\n", "239 -1.1728287935256958 -0.9545996785163879 packages/hyphenated-de/src/index.js 18960 18990\n", "240 -1.165165662765503 -0.94989413022995 packages/hyphenated-de/src/index.js 930 960\n", "241 -1.1681088209152222 -0.9524173140525818 packages/hyphenated-de/src/index.js 3990 4020\n", "242 -1.1611002683639526 -0.9496684670448303 packages/hyphenated-de/src/index.js 4200 4230\n", "243 -1.1481744050979614 -0.9477829337120056 packages/hyphenated-de/src/index.js 6060 6090\n", "244 -1.1634639501571655 -0.9549686908721924 packages/hyphenated-de/src/index.js 4320 4350\n", "245 -1.1429665088653564 -0.9494145512580872 packages/hyphenated-de/src/index.js 14100 14130\n", "246 -1.1786595582962036 -0.9498398900032043 packages/hyphenated-de/src/index.js 19140 19170\n", "247 -1.1742366552352905 -0.9533964395523071 packages/hyphenated-en-gb/src/index.js 5250 5280\n", "248 -1.1500428915023804 -0.944018542766571 packages/hyphenated-en-gb/src/index.js 7680 7710\n", "249 -1.1581268310546875 -0.936750590801239 packages/hyphenated-de/src/index.js 2970 3000\n", "250 -1.1421961784362793 -0.9478647708892822 packages/hyphenated-de/src/index.js 17280 17310\n", "251 -1.1605768203735352 -0.9540473222732544 packages/hyphenated-de/src/index.js 18660 18690\n", "252 -1.1725571155548096 -0.946816623210907 packages/hyphenated-en-gb/src/index.js 7890 7920\n", "253 -1.1687310934066772 -0.9504411220550537 packages/hyphenated-de/src/index.js 3930 3960\n", "254 -1.1577554941177368 -0.9545982480049133 packages/hyphenated-de/src/index.js 4080 4110\n"]}], "source": ["row_idx = 0\n", "print(\"##### Question:\")\n", "print(sample_file1[row_idx]['question'])\n", "print(\"##### Answer:\")\n", "print(sample_file1[row_idx]['answer'])\n", "print(\"##### Scores:\")\n", "\n", "def argmax(iterable):\n", "    return max(enumerate(iterable), key=lambda x: x[1])[0]\n", "\n", "ppl_scores = json.loads(sample_file1[row_idx]['ppl_scores'])\n", "ppl_scores.keys()\n", "print(\"Best\", max(ppl_scores['secondary']['scores']), argmax(ppl_scores['secondary']['scores']))\n", "\n", "for i in range(len(ppl_scores['scores'])):\n", "    retrieved_chunks = sample_file1[row_idx]['retrieved_chunks']\n", "    print(\n", "        i,\n", "        ppl_scores['scores'][i],\n", "        ppl_scores['secondary']['scores'][i],\n", "        retrieved_chunks[i]['parent_doc']['path'],\n", "        retrieved_chunks[i]['line_offset'],\n", "        retrieved_chunks[i]['line_offset'] + retrieved_chunks[i]['length_in_lines']\n", "    )\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["packages/hyphenated-en-us/README.md\n", "# hyphenated-en-us\n", "\n", "This package contains hyphenation patterns and exceptions for the American\n", "English language. It is intended to be used with the main `hyphenated` package\n", "and is already included there as a dependency.\n", "\n", "See the [home page](https://github.com/sergeysolovev/hyphenated) for more\n", "information.\n", "\n", "## Installation\n", "\n", "```shell\n", "npm install hyphenated\n", "```\n", "\n", "To install separately from hyphenated:\n", "\n", "```shell\n", "npm install hyphenated-en-us\n", "```\n", "\n", "## License\n", "\n", "MIT\n", "\n"]}], "source": ["chunk_idx = 12\n", "print(retrieved_chunks[chunk_idx]['parent_doc']['path'])\n", "print(retrieved_chunks[chunk_idx]['text'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secondary_chunks = json.loads(ppl_scores['secondary']['chunks'])\n", "for t in zip(range(0, 100000), ppl_scores['secondary']['gain'], ppl_scores['secondary']['gain_expand_right'], ppl_scores['secondary']['gain_expand_left'], [c[\"parent_doc\"][\"path\"] for c in secondary_chunks]):\n", "    print(t)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secondary_chunks = json.loads(ppl_scores['secondary']['chunks'])\n", "for i, c in enumerate(secondary_chunks):\n", "    print(i, \"==============================\")\n", "    print(c[\"parent_doc\"][\"path\"])\n", "    if \"defaults\" in c[\"parent_doc\"][\"path\"]:\n", "        print(c[\"parent_doc\"][\"path\"])\n", "    if \"defaults\" in c[\"text\"]:\n", "        print(c[\"text\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
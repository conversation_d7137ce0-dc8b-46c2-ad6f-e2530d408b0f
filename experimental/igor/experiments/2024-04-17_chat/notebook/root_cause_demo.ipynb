{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Configuration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\"\n", "\n", "import pathlib\n", "\n", "from research.retrieval import retrieval_database\n", "\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "from research.retrieval.utils import parse_yaml_config\n", "\n", "import research.core.prompt_formatters  # pull in registrations for prompt formatters\n", "\n", "SCENARIOS = [\n", "    (\"k8s crash\", pathlib.Path(\"logs/experiment_81012_trial_81033_logs.txt\")),\n", "]\n", "\n", "config_chatanol = {\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "    \"tokenizer_name\": \"starcoder\",\n", "    \"query_formatter_name\": \"chatanol-query\",\n", "    \"document_formatter\": \"ethanol6-embedding-with-path-key\",\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"inference_model\": \"ffw\",\n", "}\n", "\n", "config_expand = {\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-3.expand2-2.epoch3\",\n", "    \"model_key\": \"model\",\n", "    \"abs_gain_score\": True\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare the Chat model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3ChatClient\n", "import random\n", "\n", "\n", "\n", "distill_prompt_template = \"\"\"You are an AI programming assistant, and you only answer questions related to computer science.\n", "For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n", "\n", "### Instruction:\n", "{%- for chunk in retrieved_chunks %}\n", "Consider the following excerpt from {{chunk.parent_doc.path}}:\n", "```\n", "{{chunk.text}}\n", "```\n", "\n", "{%- endfor %}\n", "\n", "{{message}}\n", "\n", "### Response:\n", "\"\"\"\n", "\n", "\n", "prompt_formatter_config = {\n", "    \"name\": \"chat_template\",\n", "    \"template\": distill_prompt_template,\n", "    \"tokenizer_name\": \"deepseekcoderinstructtokenizer\",\n", "}\n", "\n", "dpf_name, dpf_kwargs = parse_yaml_config(prompt_formatter_config)\n", "prompt_formatter = get_prompt_formatter(dpf_name, **dpf_kwargs)\n", "\n", "\n", "ips = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "]\n", "ports = [\n", "    8000,\n", "    8010,\n", "    8020,\n", "    8030,\n", "    8040,\n", "    8050,\n", "    8060,\n", "    8070,\n", "]\n", "random_ip = ips[random.randint(0, len(ips) - 1)]\n", "random_port = ports[random.randint(0, len(ports) - 1)]\n", "\n", "triton_client = Llama3ChatClient(\"triton\", address=f\"{random_ip}:{random_port}\", timeout=180)\n", "\n", "from research.core.model_input import ModelInput, ChatInput\n", "\n", "def chat_model(question, retrieved_chunks):\n", "    tokens, _ = prompt_formatter.prepare_prompt(\n", "        ModelInput(\n", "            chat_input=ChatInput(\n", "                [],\n", "                question,\n", "            ),\n", "            retrieved_chunks=retrieved_chunks,\n", "            selected_code=\"\",\n", "            path=\"\",\n", "        )\n", "    )\n", "    prompt = prompt_formatter.tokenizer.detokenize(tokens)\n", "    return triton_client.generate(\n", "        messages=[prompt],\n", "        temperature=0.0,\n", "        max_tokens=1024,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Logs with FB/FFW Retriever"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:No distributed envvars detected. Running as single-GPU. Consider running with `torchrun` instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Loading StarCoder model from a legacy checkpoint. This is deprecated and will be removed in the future.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id c54b3b17-b18a-40ad-acd4-7ee5d582d945.\n", "Created BasicAttention with stable_id de61205b-79a8-4bd1-bc42-3019ea8f4d8a.\n"]}], "source": ["from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastbackward_checkpoint\n", "from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastforward_checkpoint\n", "from research.fastbackward import distributed\n", "\n", "from research.retrieval import utils as retrieval_utils\n", "from research.retrieval import chunking_functions\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.fastbackward import retrieval_models\n", "\n", "\n", "distributed.init_distributed_for_training(1)\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "if config_chatanol[\"inference_model\"] == \"fb\":\n", "    scorer = create_dense_scorer_from_fastbackward_checkpoint(\n", "        config_chatanol[\"checkpoint_path\"],\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol[\"query_formatter_name\"], tokenizer),\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol[\"document_formatter\"], tokenizer),\n", "        model_key=config_chatanol[\"model_key\"],\n", "    )\n", "elif config_chatanol[\"inference_model\"] == \"ffw\":\n", "    scorer = create_dense_scorer_from_fastforward_checkpoint(\n", "        config_chatanol[\"checkpoint_path\"],\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol[\"query_formatter_name\"], tokenizer),\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol[\"document_formatter\"], tokenizer),\n", "    )  \n", "\n", "chunker_cls_name, chunker_kwargs = retrieval_utils.parse_yaml_config(config_chatanol[\"chunker\"])\n", "chunker = chunking_functions.get_chunker(chunker_cls_name, **chunker_kwargs)\n", "\n", "fb_retriever = RetrievalDatabase(chunker, scorer)\n", "fb_retriever.load()\n", "\n", "import json\n", "import pathlib\n", "\n", "from research.core.types import Document"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "\n", "class ScoredChunk(Chunk):\n", "    def __init__(self, chunk: Chunk, score: float):\n", "        super().__init__(**chunk.__dict__)\n", "        self.score = score\n", "\n", "def limit_chunks(chunks, char_limit) -> list:\n", "    total_len = 0\n", "    for i, chunk in enumerate(chunks):\n", "        total_len += chunk.length\n", "        if total_len > char_limit:\n", "            return chunks[:i]\n", "    return chunks\n", "\n", "def generate_report(answer_func: Callable[[str], list[ScoredChunk]], name):\n", "    questions = [\"Please explain the most likely root cause behind any crash, error or failure that you can see in the log file.\"]\n", "\n", "    #with open(f\"reports/test3-{name}.txt\", \"w\") as f:\n", "    import sys\n", "    f = sys.stdout\n", "    if True:\n", "        for scenario_name, log_path in SCENARIOS:\n", "            fb_retriever.remove_all_docs()\n", "            max_line_len = 200\n", "            log_text = \"\".join(\n", "                [\n", "                    line[:max_line_len] + \" ...\\n\" if len(line) > max_line_len else line + \"\\n\"\n", "                    for line in log_path.read_text().splitlines()\n", "                ]\n", "            )\n", "\n", "            print(\"Max line length\", max(len(line) for line in log_text.splitlines(keepends=True)))\n", "            doc = Document.new(log_text, log_path)\n", "            fb_retriever.add_doc(doc)\n", "        \n", "            for question in questions:\n", "                final_chunks = answer_func(question)\n", "                f.write(\"========================\\n\")\n", "                f.write(\"Scenario: \" + scenario_name + \"\\n\")\n", "                f.write(\"Question: \" + question + \"\\n\")\n", "                f.write(\"------- RETRIEVAL ------\\n\")    \n", "                for i, chunk in enumerate(final_chunks):\n", "                    f.write(f\"{i} {chunk.path} {chunk.line_range} {chunk.score}\\n\")\n", "                f.write(\"Total length in characters: \" + str(sum(chunk.length for chunk in final_chunks)) + \"\\n\")\n", "                f.write(\"\\n\")\n", "                if \"chat_model\" in globals():\n", "                    f.write(\"-------- ANSWER --------\\n\")\n", "                    f.write(chat_model(question, final_chunks) + \"\\n\")\n", "                    f.write(\"========================\\n\")\n", "                    f.write(\"\\n\")\n", "                    f.write(\"\\n\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput\n", "\n", "def _chatanol_input(question, extra=None):\n", "    extra_dict = {} if extra is None else {\n", "        \"extra\": extra\n", "    }\n", "    prompt_input = ChatanolRetrieverPromptInput(\n", "        prefix=question,\n", "        suffix=\"\",\n", "        path=\"\",\n", "        message=\"\",\n", "        selected_code=\"\",\n", "        **extra_dict,\n", "    )\n", "    return prompt_input\n", "\n", "def analyze_question_fb(question, char_limit) -> list[ScoredChunk]:\n", "    chunks, scores = fb_retriever.query(_chatanol_input(question))\n", "    chunks = limit_chunks(chunks, char_limit)\n", "    return [ScoredChunk(chunk, score) for chunk, score in zip(chunks, scores)]\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Max line length 205\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["========================\n", "Scenario: k8s crash\n", "Question: Please explain the most likely root cause behind any crash, error or failure that you can see in the log file.\n", "------- RETRIEVAL ------\n", "0 logs/experiment_81012_trial_81033_logs.txt 210:235 1447.249755859375\n", "1 logs/experiment_81012_trial_81033_logs.txt 0:30 1020.77001953125\n", "2 logs/experiment_81012_trial_81033_logs.txt 30:60 984.71728515625\n", "Total length in characters: 16232\n", "\n", "-------- ANSWER --------\n", "After analyzing the log files, I've identified several errors and failures. The most likely root cause behind these issues is a resource allocation problem, specifically a lack of available GPUs.\n", "\n", "Here are some key observations:\n", "\n", "1. **GPU unavailability**: Many pods are waiting for resources, with 0 GPUs available, but 8 GPUs required. This suggests that the system is unable to allocate the necessary GPU resources, leading to pod failures.\n", "2. **Coscheduling rejections**: Several pods are being rejected by \"Coscheduling\" at the prefilter stage, citing \"less than pgMinAvailable\" as the reason. This implies that the system is unable to schedule the pods due to insufficient resources.\n", "3. **Task failures**: There are multiple instances of \"task failed without an associated exit code\" errors, which may be related to the underlying resource allocation issues.\n", "4. **Pod allocation failures**: Some pods are failing to allocate due to a lack of resources, leading to errors like \"Allocate failed due to requested number of devices unavailable.\"\n", "\n", "Given these observations, it's likely that the root cause of the crashes, errors, and failures is a resource allocation problem, specifically a lack of available GPUs to support the pods' resource requirements.\n", "========================\n", "\n", "\n"]}], "source": ["from pathlib import Path\n", "name = \"fb \" + Path(config_chatanol[\"checkpoint_path\"]).name\n", "\n", "generate_report(\n", "    lambda q: analyze_question_fb(q, char_limit=20000),\n", "    name\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "import pathlib\n", "\n", "from research.retrieval import retrieval_database\n", "\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "from research.retrieval.utils import parse_yaml_config\n", "\n", "import research.core.prompt_formatters  # pull in registrations for prompt formatters\n", "\n", "#AUGMENT_REPO_PATH = pathlib.Path(\"/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_apr_10_2024.json\")\n", "AUGMENT_REPO_PATH = pathlib.Path(\"/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_jun_08_2024.json\")\n", "\n", "config_chatanol_neox = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "config_starethanol = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "config_chatanol_secondary = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-14-secondary\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter_name\": \"chatanol-query\",\n", "    \"document_formatter\": \"ethanol6-embedding-with-path-key\",\n", "}\n", "\n", "config_chatanol_fb = {\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.fix\",\n", "    #\"model_key\": \"model\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.hybrid-noexp\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-1.hybrid\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.hybrid.ppl\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "    \"model_key\": \"model.models.0\",\n", "    \"tokenizer_name\": \"starcoder\",\n", "    \"query_formatter_name\": \"chatanol-query\",\n", "    \"document_formatter\": \"ethanol6-embedding-with-path-key\",\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"inference_model\": \"fb\",\n", "}\n", "\n", "config_chatanol_fb_ethanol7_1 = {\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/ethanol7/ethanol7-1\",\n", "    \"model_key\": \"model\",\n", "    \"tokenizer_name\": \"starcoder\",\n", "    \"query_formatter_name\": \"ethanol7-chat-query\",\n", "    \"document_formatter\": \"ethanol6-embedding-with-path-key\",\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "}\n", "\n", "config_expand = {\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-14-expand.scale0.01\",\n", "    #\"model_key\": \"model\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-1.hybrid\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16.hybrid.1200\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.hybrid.ppl\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-base.expand2-2\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-3.expand2-2\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-3.expand2-2.epoch3\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.expand4.ppl.lr4e-5\",\n", "    #\"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.expand4\",\n", "    #\"model_key\": \"model.models.1\",\n", "    \"model_key\": \"model\",\n", "    \"abs_gain_score\": True\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare the Chat model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.guy.apis.chat_utils import Llama3ChatClient\n", "\n", "distill_prompt_template = \"\"\"You are an AI programming assistant, and you only answer questions related to computer science.\n", "For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n", "\n", "### Instruction:\n", "{%- for chunk in retrieved_chunks %}\n", "Consider the following excerpt from {{chunk.parent_doc.path}}:\n", "```\n", "{{chunk.text}}\n", "```\n", "\n", "{%- endfor %}\n", "\n", "{{message}}\n", "\n", "### Response:\n", "\"\"\"\n", "\n", "\n", "prompt_formatter_config = {\n", "    \"name\": \"chat_template\",\n", "    \"template\": distill_prompt_template,\n", "    \"tokenizer_name\": \"deepseekcoderinstructtokenizer\",\n", "}\n", "\n", "dpf_name, dpf_kwargs = parse_yaml_config(prompt_formatter_config)\n", "prompt_formatter = get_prompt_formatter(dpf_name, **dpf_kwargs)\n", "\n", "\n", "triton_client = Llama3ChatClient(\"triton\", address=\"*************:8000\", timeout=180)\n", "\n", "from research.core.model_input import ModelInput, ChatInput\n", "\n", "def chat_model(question, retrieved_chunks):\n", "    tokens, _ = prompt_formatter.prepare_prompt(\n", "        ModelInput(\n", "            chat_input=ChatInput(\n", "                [],\n", "                question,\n", "            ),\n", "            retrieved_chunks=retrieved_chunks,\n", "            selected_code=\"\",\n", "            path=\"\",\n", "        )\n", "    )\n", "    prompt = prompt_formatter.tokenizer.detokenize(tokens)\n", "    return triton_client.generate(\n", "        messages=[prompt],\n", "        temperature=0.0,\n", "        max_tokens=1024,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Augment Repo with NeoX Retriever"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    import json\n", "\n", "    from research.core.types import Document\n", "\n", "\n", "    augment_repo = json.load(AUGMENT_REPO_PATH.open())\n", "\n", "    retriever = retrieval_database.RetrievalDatabase.from_yaml_config(config_chatanol_neox)\n", "    retriever.load()\n", "\n", "    retriever.add_docs(\n", "        Document(**doc)\n", "        for doc in augment_repo[\"docs\"]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Augment Repo with FB/FFW Retriever"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastbackward_checkpoint\n", "from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastforward_checkpoint\n", "from research.fastbackward import distributed\n", "\n", "from research.retrieval import utils as retrieval_utils\n", "from research.retrieval import chunking_functions\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.fastbackward import retrieval_models\n", "\n", "\n", "distributed.init_distributed_for_training(1)\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "if config_chatanol_fb[\"inference_model\"] == \"fb\":\n", "    scorer = create_dense_scorer_from_fastbackward_checkpoint(\n", "        config_chatanol_fb[\"checkpoint_path\"],\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol_fb[\"query_formatter_name\"], tokenizer),\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol_fb[\"document_formatter\"], tokenizer),\n", "        model_key=config_chatanol_fb[\"model_key\"],\n", "    )\n", "elif config_chatanol_fb[\"inference_model\"] == \"ffw\":\n", "    scorer = create_dense_scorer_from_fastforward_checkpoint(\n", "        config_chatanol_fb[\"checkpoint_path\"],\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol_fb[\"query_formatter_name\"], tokenizer),\n", "        get_retrieval_prompt_formatter_by_name(config_chatanol_fb[\"document_formatter\"], tokenizer),\n", "    )  \n", "\n", "chunker_cls_name, chunker_kwargs = retrieval_utils.parse_yaml_config(config_chatanol_fb[\"chunker\"])\n", "chunker = chunking_functions.get_chunker(chunker_cls_name, **chunker_kwargs)\n", "\n", "fb_retriever = RetrievalDatabase(chunker, scorer)\n", "fb_retriever.load()\n", "\n", "import json\n", "import pathlib\n", "\n", "from research.core.types import Document\n", "\n", "augment_repo = json.load(AUGMENT_REPO_PATH.open())\n", "\n", "fb_retriever.add_docs(\n", "    Document(**doc)\n", "    for doc in augment_repo[\"docs\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Augment Repo with Secondary Retriever"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    from research.retrieval.scorers.dense_scorer_v2 import create_dense_scorer_from_fastbackward_checkpoint\n", "    import torch\n", "    from research.fastbackward.retrieval_models import load_checkpoint\n", "    from research.fastbackward import distributed\n", "\n", "    from research.retrieval import utils as retrieval_utils\n", "    from research.retrieval import chunking_functions\n", "    from research.retrieval.retrieval_database import RetrievalDatabase\n", "\n", "\n", "    distributed.init_distributed_for_training(1)\n", "    checkpoint_path = \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-14-secondary\"\n", "\n", "    # XXX: this is now broken due to API changes\n", "    scorer = create_dense_scorer_from_fastbackward_checkpoint(checkpoint_path, \"starcoder\", \"chatanol-query\", \"ethanol6-embedding-with-path-key\")\n", "\n", "\n", "    chunker_cls_name, chunker_kwargs = retrieval_utils.parse_yaml_config(config_chatanol_secondary[\"chunker\"])\n", "    chunker = chunking_functions.get_chunker(chunker_cls_name, **chunker_kwargs)\n", "\n", "    secondary_retriever = RetrievalDatabase(chunker, scorer)\n", "    secondary_retriever.load()\n", "\n", "    import json\n", "    import pathlib\n", "\n", "    from research.core.types import Document\n", "\n", "    augment_repo = json.load(AUGMENT_REPO_PATH.open())\n", "\n", "    secondary_retriever.add_docs(\n", "        Document(**doc)\n", "        for doc in augment_repo[\"docs\"]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Initialize the Chatanol-Extend Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from research.fastbackward.retrieval_models import load_checkpoint\n", "from research.fastbackward import distributed\n", "\n", "distributed.init_distributed_for_training(1)\n", "components = load_checkpoint(config_expand[\"checkpoint_path\"])\n", "extend_model = components.get_with_type(config_expand[\"model_key\"], torch.nn.Module)\n", "extend_model.to(dtype=torch.float16, device=torch.device(\"cuda\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from experimental.igor.systems import chatanol\n", "from tensordict import TensorDict\n", "\n", "from research.core.types import Chunk\n", "\n", "class ScoredChunk(Chunk):\n", "    def __init__(self, chunk: Chunk, score: float):\n", "        super().__init__(**chunk.__dict__)\n", "        self.score = score\n", "    \n", "    def merge(self, other: \"ScoredChunk\") -> \"ScoredChunk\":\n", "        merged_chunk = ScoredChunk(super().merge(other), 0)\n", "\n", "        lenOverlap = merged_chunk.length - (self.length + other.length)\n", "        lenSelfOnly = self.length - lenOverlap\n", "        lenOtherOnly = other.length - lenOverlap\n", "\n", "        merged_chunk.score = (\n", "            self.score * lenSelfOnly\n", "            + other.score * lenOtherOnly\n", "            + ((self.score + other.score) / 2) * lenOverlap\n", "        ) / max(len<PERSON><PERSON><PERSON>n<PERSON>, lenOtherOnly)\n", "\n", "        return merged_chunk\n", "\n", "    def to_base_chunk(self):\n", "        dict = self.__dict__.copy()\n", "        del dict[\"score\"]\n", "        return Chunk(**dict)\n", "\n", "    def __repr__(self):\n", "        return f\"ScoredChunk({super().__repr__()}, {self.score})\"\n", "\n", "def process_chunks(question, scored_chunks, unscored_chunks, char_limit, batch_len, verbosity=0):\n", "    chatanol_query_prompt_formatter = chatanol.ChatanolQueryFormatter()\n", "    chatanol_query_prompt_formatter.tokenizer_name = \"StarCoderTokenizer\"\n", "    extend_prompt_batch = []\n", "    known_chunks_prompt_order = []\n", "    for i in range(batch_len):\n", "        known_chunks = [\n", "            unscored_chunks[j]\n", "            for j in range(len(unscored_chunks)) if j % batch_len == i\n", "        ]\n", "\n", "        extend_prompt, _ = chatanol_query_prompt_formatter.prepare_prompt(\n", "            ModelInput(\n", "                question,\n", "                extra={ \"known_chunks\": known_chunks },\n", "            )\n", "        )\n", "        extend_prompt_batch.append(extend_prompt)\n", "        known_chunks_prompt_order.extend(known_chunks)\n", "    \n", "    max_len = max(len(extend_prompt) for extend_prompt in extend_prompt_batch)\n", "    for i in range(len(extend_prompt_batch)):\n", "        extend_prompt_batch[i] = extend_prompt_batch[i] + [0] * (max_len - len(extend_prompt_batch[i]))\n", "\n", "    extend_model.eval()\n", "    predictions = extend_model.forward(TensorDict(\n", "        {\n", "            \"query_tokens_BLq\": torch.tensor(extend_prompt_batch, device=torch.device(\"cuda\")),\n", "        },\n", "        batch_size=[len(extend_prompt_batch)],\n", "    ))\n", "\n", "    from research.core.types import Chunk\n", "    def expand_chunk(chunk: Chunk, expand_factor: float, to_left: bool):\n", "        if expand_factor < 1:\n", "            raise ValueError(f\"Invalid expand_factor {expand_factor}\")\n", "\n", "        expand_by = round(chunk.length_in_lines * (expand_factor - 1))\n", "        return chatanol.select_chunk_lines(\n", "            chunk.parent_doc,\n", "            chunk.line_offset - (expand_by if to_left else 0),\n", "            chunk.length_in_lines + expand_by,\n", "        )\n", "\n", "    from experimental.igor.systems.chatanol import ChunkSet\n", "    chunk_set = ChunkSet()\n", "\n", "    gain_tensor = predictions[\"gains_BCD\"]\n", "    newly_scored_chunks = list(\n", "        zip(\n", "            gain_tensor[:, 0].tolist(),\n", "            known_chunks_prompt_order,\n", "        )\n", "    )\n", "\n", "    if config_expand[\"abs_gain_score\"]:\n", "        def get_expand_score(expand_gain, gain) -> float:\n", "            return expand_gain\n", "\n", "        def normalize_score(gain, chunk: Chunk) -> float:\n", "            return gain\n", "    else:\n", "        def get_expand_score(expand_gain, gain) -> float:\n", "            return (gain + expand_gain)\n", "\n", "        def normalize_score(gain, chunk: Chunk) -> float:\n", "            return gain / chunk.length\n", "\n", "\n", "    expand_left_chunks = list(\n", "        zip(\n", "            (get_expand_score(a, b) for a, b in zip(gain_tensor[:, 1].tolist(), gain_tensor[:, 0].tolist())),\n", "            [expand_chunk(c, 3, to_left=True) for c in known_chunks_prompt_order]\n", "        )\n", "    )\n", "\n", "    expand_right_chunks = list(\n", "        zip(\n", "            (get_expand_score(a, b) for a, b in zip(gain_tensor[:, 2].tolist(), gain_tensor[:, 0].tolist())),\n", "            [expand_chunk(c, 3, to_left=False) for c in known_chunks_prompt_order]\n", "        )\n", "    )\n", "\n", "    for i, (c1, c2, c3) in enumerate(zip(\n", "        newly_scored_chunks, expand_left_chunks, expand_right_chunks\n", "    )):\n", "        if verbosity >= 1:\n", "            print(\"UNSORTED\", i, c1[1].path, c1[1].line_range, c1[0], c2[0]-c1[0], c3[0]-c1[0])\n", "\n", "    sorted_chunks = sorted(\n", "        scored_chunks + newly_scored_chunks + expand_left_chunks + expand_right_chunks,\n", "        key=lambda x: normalize_score(x[0], x[1]),\n", "        reverse=True,\n", "    )\n", "\n", "    for i, c in enumerate(sorted_chunks):\n", "        if verbosity >= 1:\n", "            print(\"SORTED\", i, c[1].path, c[1].line_range, c[0])\n", "\n", "    for (ppl, chunk) in sorted_chunks:\n", "        if chunk_set.add_chunk(ScoredChunk(chunk, ppl), char_limit):\n", "            if verbosity >= 2:\n", "                print(\"  - ADDED\", chunk.path, chunk.line_range)\n", "        else:\n", "            if verbosity >= 2:\n", "                print(\"  - SKIPPED\", chunk.path, chunk.line_range, \" chunk of length\", chunk.length)\n", "\n", "    chunk_prompt_batch = list(sorted(chunk_set.chunks, key=lambda x: x.score, reverse=True))\n", "    return sorted_chunks, chunk_prompt_batch\n", "\n", "def limit_chunks(chunks, char_limit) -> list[ScoredChunk]:\n", "    total_len = 0\n", "    for i, chunk in enumerate(chunks):\n", "        total_len += chunk.length\n", "        if total_len > char_limit:\n", "            return chunks[:i]\n", "    return chunks\n", "\n", "\n", "def expand_chunks(question, chunks_in, verbose, interm_char_limit, final_char_limit, num_expansions, batch_len=1):\n", "    assert num_expansions >= 1\n", "    chunks_in = limit_chunks(chunks_in, interm_char_limit)\n", "\n", "    if verbose:\n", "        print(question)\n", "        print(\"======== Retrieved chunks ========\")    \n", "        for idx, chunk in enumerate(chunks_in ):\n", "            print(f\"({idx}) -- {chunk.path} {chunk.line_range} --\")\n", "        print(\"Length:\", sum(chunk.length for chunk in chunks_in ))\n", "    \n", "    all_scored_chunks = []\n", "    next_chunk_batch = []\n", "    chunk_batch = chunks_in\n", "\n", "    for i in range(num_expansions):\n", "        char_limit = final_char_limit if i == num_expansions - 1 else interm_char_limit\n", "        add_scored_chunks, next_chunk_batch = process_chunks(\n", "            question, all_scored_chunks, chunk_batch, char_limit, batch_len=batch_len, verbosity=2\n", "        )\n", "        all_scored_chunks += add_scored_chunks\n", "\n", "        if verbose:\n", "            print(f\"======== Expansion {i+1} ========\")\n", "            for idx, chunk in enumerate(next_chunk_batch):\n", "                print(f\"({idx}) {chunk.path} {chunk.line_range} -- {chunk.score}\")\n", "            print(\"Length:\", sum(chunk.length for chunk in next_chunk_batch))\n", "            print()\n", "\n", "        chunk_batch = [c.to_base_chunk() for c in next_chunk_batch]\n", "\n", "    return next_chunk_batch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "\n", "\n", "def generate_report(answer_func: Callable[[str], list[ScoredChunk]], name):\n", "    vincent_questions = [\n", "        \"Where in the context manager service do we delete old blobs?\",\n", "        \"What does VSCode extension send to the chat backend as context?\",\n", "        \"Is loading the document embeddings in th searcher sequential with computing scores?\",\n", "        \"What's the full class definition of DenseRetrievalScorer?\",\n", "        \"Where is the tokenizer for StarCoder defined?\",\n", "        \"What types are defined in research/gpt-neox/megatron/tokenizer/tokenizer.py?\",\n", "    ]\n", "\n", "    summarize_questions = [\n", "        \"Summarize the code in research/retrieval/libraries/scorers/dense_scorer.py\",\n", "        \"Summarize the code contained in research/retrieval/libraries/rerankers\",\n", "    ]\n", "\n", "    colin_questions = [\n", "        \"Summarize all the different retrieval scorers implemented in our research code.\",\n", "        \"Do we have a method for reading a jsonl zst file?\",\n", "        \"Are there evaluation methods on the AbstractSystem interface?\",\n", "        \"How do I run perplexity distillation with Megatron/NeoX?\",\n", "        \"How do I query s3 data in our research code?\",\n", "        \"Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\",\n", "        \"Have we deployed the diesel retriever to production?\",\n", "        \"Is there a research interface for rerankers?\",\n", "        \"Where does filtering retrieved chunks vs prefix/suffix happen in services?\",\n", "        \"Where does the actual search over document embeddings happen for dense retrieval?\",\n", "        \"Where is the Rogue data pipeline located?\",\n", "        \"Does fastforward model in research use all available GPUs?\"\n", "    ]\n", "\n", "    yury_questions = [\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data?\",\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data in the `experimental/michiel` directory?\",\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data in the `experimental/yury` directory?\",\n", "    ]\n", "\n", "    path_questions = [\n", "        #\"What's in directory experimental/colin/scripts?\",\n", "        \"What's in experimental/colin/scripts/load_multiple_models.py?\",\n", "        #\"What's in experimental/colin/scripts?\",\n", "        \"What's in experimental/igor/experiments/2024-02-16_dialog?\",\n", "        \"What's in experimental/evan/hydra_repos directory?\",\n", "        \"What's in experimental/evan/hydra_repos/generate_patchset.ipynb?\",\n", "    ]\n", "\n", "    triton_questions = [\n", "        \"Where is TritonClient defined?\",\n", "        \"Where is TritonClient defined in experimental/guy?\",\n", "    ]\n", "\n", "    hard_questions = [\n", "        \"What functions does Llama3ChatClient have?\",\n", "        \"What functions does LlamaCppClient have?\",\n", "        \"What does apply_pandas_local do?\",\n", "        \"What IPs are in ALL_IPS list?\",\n", "        \"Show me the definition of apply_pandas_local\",\n", "        \"Show me the contents of experimental/guy/keystroke_deltas.py\",\n", "        \"What functions does AnnealingLR implement?\",\n", "        \"What types are defined in research/model_server/model_server_requests.py?\",\n", "        \"Show me the contents of experimental/guy/keystroke_deltas.py\",\n", "    ]\n", "    zhuoran_questions = [\n", "        \"Are `key_states` and `value_states` input to dbrx `_flash_attention_forward()` same shape?\"\n", "    ]\n", "\n", "    questions = vincent_questions + summarize_questions + colin_questions + yury_questions + path_questions + triton_questions + hard_questions + zhu<PERSON>_questions\n", "    unrelated_questions = [\n", "        \"In Python, what's the difference between yaml safe and unsafe loading?\",\n", "        \"In C++, how do I create a sorted map?\",\n", "        \"What's a good loss function to use for language model distillation (teacher to student)?\",\n", "    ]\n", "    questions = [\n", "        #\"what is the data structure for code edit samples in base/datasets\"\n", "        #\"### Instruction:\\nwhat is the data structure for code edit samples in base/datasets\"\n", "        #\"### Instruction:\\nWhere is DenseRetrievalScorer defined?\"\n", "        \"What types are derived from AbstractPromptFormatter?\",\n", "    ]\n", "    \n", "    with open(f\"reports/test3-{name}.txt\", \"w\") as f:\n", "    #import sys\n", "    #f = sys.stdout\n", "    #if True:\n", "        for question in questions:\n", "            final_chunks = answer_func(question)\n", "            f.write(\"========================\\n\")\n", "            f.write(question + \"\\n\")\n", "            f.write(\"------- RETRIEVAL ------\\n\")    \n", "            for i, chunk in enumerate(final_chunks):\n", "                f.write(f\"{i} {chunk.path} {chunk.line_range} {chunk.score}\\n\")\n", "            f.write(\"Total length in characters: \" + str(sum(chunk.length for chunk in final_chunks)) + \"\\n\")\n", "            f.write(\"\\n\")\n", "            if \"chat_model\" in globals():\n", "                f.write(\"-------- ANSWER --------\\n\")\n", "                f.write(chat_model(question, final_chunks) + \"\\n\")\n", "                f.write(\"========================\\n\")\n", "                f.write(\"\\n\")\n", "                f.write(\"\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput\n", "\n", "VERBOSE = 2\n", "\n", "def _chatanol_input(question, extra=None):\n", "    extra_dict = {} if extra is None else {\n", "        \"extra\": extra\n", "    }\n", "    prompt_input = ChatanolRetrieverPromptInput(\n", "        prefix=question,\n", "        suffix=\"\",\n", "        path=\"\",\n", "        message=\"\",\n", "        selected_code=\"\",\n", "        **extra_dict,\n", "    )\n", "    return prompt_input\n", "\n", "def analyze_question_neox(question, char_limit) -> list[ScoredChunk]:\n", "    chunks, scores = retriever.query(ModelInput(question))\n", "    chunks = limit_chunks(chunks, char_limit)\n", "    return [ScoredChunk(chunk, score) for chunk, score in zip(chunks, scores)]\n", "\n", "def analyze_question_neox_expand(question, char_limit_small_model, char_limit, num_expansions) -> list[ScoredChunk]:\n", "    chunks, _ = retriever.query(ModelInput(question))\n", "    chunks = limit_chunks(chunks, char_limit_small_model)\n", "    chunks = expand_chunks(question, chunks, verbose=VERBOSE, interm_char_limit=char_limit_small_model, final_char_limit=char_limit, num_expansions=num_expansions)\n", "    return limit_chunks(chunks, char_limit)\n", "\n", "def analyze_question_fb(question, char_limit) -> list[ScoredChunk]:\n", "    chunks, scores = fb_retriever.query(_chatanol_input(question))\n", "    chunks = limit_chunks(chunks, char_limit)\n", "    return [ScoredChunk(chunk, score) for chunk, score in zip(chunks, scores)]\n", "\n", "def analyze_question_fb_expand(question, char_limit_small_model, char_limit, num_expansions, batch_len) -> list[ScoredChunk]:\n", "    chunks, _ = fb_retriever.query(_chatanol_input(question))\n", "    chunks = limit_chunks(chunks, char_limit_small_model)\n", "    chunks = expand_chunks(question, chunks, verbose=VERBOSE, interm_char_limit=char_limit_small_model, final_char_limit=char_limit, num_expansions=num_expansions, batch_len=batch_len)\n", "    return limit_chunks(chunks, char_limit)\n", "\n", "def _analyze_question_secondary(retr, question, retr_input, char_limit, char_limit_small_model) -> list[ScoredChunk]:\n", "    chunks1, _scores1 = retr.query(retr_input)\n", "    chunks1 = limit_chunks(chunks1, char_limit_small_model)\n", "\n", "    prompt_input = _chatanol_input(\n", "        question=question,\n", "        extra={\n", "            \"known_chunks\": chunks1,\n", "            \"add_bare_query\": <PERSON>alse,\n", "        }\n", "    )\n", "\n", "    chunks, scores = secondary_retriever.query(\n", "        prompt_input,\n", "        top_k=32\n", "    )\n", "    chunks = limit_chunks(chunks, char_limit)\n", "    return [ScoredChunk(chunk, score) for chunk, score in zip(chunks, scores)]\n", "\n", "def analyze_question_neox_secondary(question, char_limit, char_limit_small_model) -> list[ScoredChunk]:\n", "    return _analyze_question_secondary(retriever, question, ModelInput(question), char_limit, char_limit_small_model)\n", "\n", "def analyze_question_fb_secondary(question, char_limit, char_limit_small_model) -> list[ScoredChunk]:\n", "    return _analyze_question_secondary(fb_retriever, question, _chatanol_input(question), char_limit, char_limit_small_model)\n", "\n", "def _analyze_question_secondary_expand(retr, question, char_limit, char_limit_small_model, num_expansions) -> list[ScoredChunk]:\n", "    chunks1, _scores1 = retr.query(ModelInput(question))\n", "    chunks1 = limit_chunks(chunks1, char_limit_small_model)\n", "\n", "    prompt_input = _chatanol_input(question, {\n", "        \"known_chunks\": chunks1,\n", "        \"add_bare_query\": <PERSON>alse,\n", "    })\n", "\n", "    chunks, scores = secondary_retriever.query(\n", "        prompt_input,\n", "        top_k=32\n", "    )\n", "    chunks = expand_chunks(question, chunks, verbose=VERBOSE, interm_char_limit=char_limit_small_model, final_char_limit=char_limit, num_expansions=num_expansions)\n", "    return chunks\n", "\n", "def analyze_question_neox_secondary_expand(question, char_limit, char_limit_small_model, num_expansions) -> list[ScoredChunk]:\n", "    return _analyze_question_secondary_expand(retriever, question, char_limit, char_limit_small_model, num_expansions)\n", "\n", "def analyze_question_fb_secondary_expand(question, char_limit, char_limit_small_model, num_expansions) -> list[ScoredChunk]:\n", "    return _analyze_question_secondary_expand(fb_retriever, question, char_limit, char_limit_small_model, num_expansions)\n", "\n", "\n", "#generate_report(lambda q: analyze_question_neox(q, char_limit=12000), \"neox\")\n", "#generate_report(lambda q: analyze_question_neox_expand(q, char_limit_small_model=20000, char_limit=12000, num_expansions=2), \"neox_expand\")\n", "#generate_report(lambda q: analyze_question_neox_secondary(q, char_limit=12000, char_limit_small_model=20000), \"neox_secondary\")\n", "#generate_report(lambda q: analyze_question_neox_secondary_expand(q, char_limit=12000, char_limit_small_model=20000, num_expansions=2), \"neox_secondary_expand\")\n", "\n", "#generate_report(lambda q: analyze_question_fb(q, char_limit=12000), \"fb\")\n", "#generate_report(lambda q: analyze_question_fb_expand(q, char_limit_small_model=20000, char_limit=12000, num_expansions=2), \"fb_expand\")\n", "#generate_report(lambda q: analyze_question_fb_secondary(q, char_limit=12000, char_limit_small_model=20000), \"fb_secondary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "for num_expansions in [1]:\n", "    name = \"fb_expand \" + Path(config_chatanol_fb[\"checkpoint_path\"]).name + \" \" + Path(config_expand[\"checkpoint_path\"]).name + \" expansions=\" + str(num_expansions)\n", "\n", "    generate_report(\n", "        lambda q: analyze_question_fb_expand(q, char_limit_small_model=19300, char_limit=12000, num_expansions=num_expansions, batch_len=1),\n", "        name\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "batch_len = 8\n", "for num_expansions in [1]:\n", "    name = f\"fb_expand {Path(config_chatanol_fb['checkpoint_path']).name} {Path(config_expand['checkpoint_path']).name} expansions={str(num_expansions)} batch_len={batch_len}\"\n", "\n", "    generate_report(\n", "        lambda q: analyze_question_fb_expand(q, char_limit_small_model=100000, char_limit=12000, num_expansions=num_expansions, batch_len=8),\n", "        name\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "name = \"fb \" + Path(config_chatanol_fb[\"checkpoint_path\"]).name\n", "\n", "generate_report(\n", "    lambda q: analyze_question_fb(q, char_limit=12000),\n", "    name\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TEST LONGER char_limit\n", "from pathlib import Path\n", "batch_len = 8\n", "for num_expansions in [1]:\n", "    name = f\"fb_expand {Path(config_chatanol_fb['checkpoint_path']).name} {Path(config_expand['checkpoint_path']).name} expansions={str(num_expansions)} batch_len={batch_len} 20k\"\n", "\n", "    generate_report(\n", "        lambda q: analyze_question_fb_expand(q, char_limit_small_model=100000, char_limit=20000, num_expansions=num_expansions, batch_len=8),\n", "        name\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
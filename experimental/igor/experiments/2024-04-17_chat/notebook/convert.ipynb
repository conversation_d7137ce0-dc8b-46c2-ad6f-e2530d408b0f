{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    jsonl_paths = [\n", "        \"/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/binks/binks-v2-haiku/repos_with_qa_fixincomplete.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/binks/binks-v2-haiku-v2/repos_with_qa_fixincomplete.jsonl\",\n", "        \"/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa.jsonl\",\n", "        \"/mnt/efs/augment/user/colin/data/binks/binks_v3/06_final.jsonl\",\n", "    ]\n", "else:\n", "    #jsonl_paths = [\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_questionsonly.jsonl\"]\n", "    #jsonl_paths = [\"/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa.jsonl\"]\n", "    jsonl_paths = [\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl\"]\n", "jsonl_out = \"/mnt/efs/augment/user/igor/data/chatanol/chatanol1-16-multiturn-qa.jsonl\"\n", "\n", "import json\n", "\n", "def postprocess(j):\n", "    doc_with_questions = []\n", "    for e in j['documents_with_questions']:\n", "        if 'question' in e:\n", "            doc_with_questions.append(\n", "                {\n", "                    'question': e['question'],\n", "                    'answer': e['answer'],\n", "                    'paths': e['paths'],\n", "                }\n", "            )\n", "        else:\n", "            for q in e['questions']:\n", "                doc_with_questions.append(\n", "                    {\n", "                        'question': q['question'],\n", "                        'answer': q['answers']['gpt_answer'],\n", "                        'paths': [e['path']],\n", "                    }\n", "                )\n", "\n", "    j['documents_with_questions'] = doc_with_questions\n", "\n", "    # Prune out optional fields to avoid pyspark schema mismatches between batches\n", "    # that may or may not have the fields.\n", "    file_list_cols = {\n", "        \"alphanum_fraction\",\n", "        \"avg_line_length\",\n", "        \"content\",\n", "        \"ext\",\n", "        \"hex<PERSON>\",\n", "        \"langpart\",\n", "        \"max_line_length\",\n", "        \"max_stars_repo_licenses\",\n", "        \"max_stars_repo_name\",\n", "        \"max_stars_repo_path\",\n", "        \"size\",\n", "    }\n", "    j['file_list'] = [\n", "        {\n", "            k: v\n", "            for k, v in entry.items()\n", "            if k in file_list_cols\n", "        }\n", "        for entry in j['file_list']\n", "    ]\n", "    \n", "    return j\n", "\n", "with open(jsonl_out, 'w') as f:\n", "    for jsonl_path in jsonl_paths:\n", "        repo_count, question_count = 0, 0\n", "        for line in open(jsonl_path):\n", "            record = postprocess(json.loads(line))\n", "            record['augment_jsonl_path'] = jsonl_path\n", "            f.write(json.dumps(record) + '\\n')\n", "\n", "            repo_count += 1\n", "            question_count += len(record['documents_with_questions'])\n", "        print(f\"{p}: {repo_count} repos, {question_count} questions\")\n", "\n", "with open(jsonl_out + \".log\", 'w') as f:\n", "    f.write(\n", "        json.dumps(\n", "            {\n", "                \"jsonl_paths\": jsonl_paths,\n", "                \"jsonl_out\": jsonl_out,\n", "            }\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_json_structure(j, indent=0):\n", "    if isinstance(j, dict):\n", "        for k, v in j.items():\n", "            print('    ' * indent + str(k) + \" \" + str(type(j)))\n", "            print_json_structure(v, indent+1)\n", "    elif isinstance(j, list):\n", "        print('    ' * indent + f\"[\")\n", "        if len(j) > 0:\n", "            print_json_structure(j[0], indent+1)\n", "        print('    ' * indent + f\"]\")\n", "    else:\n", "        print('    ' * indent + f\"{type(j)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# read jsonl file line-by-line\n", "\n", "keys = dict()\n", "with open(jsonl_out) as f:\n", "    j = json.loads(f.readline())\n", "    for f in j['file_list']:\n", "        for k in f.keys():\n", "            keys[k] = True\n", "keys.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["j['file_list'][0].keys()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
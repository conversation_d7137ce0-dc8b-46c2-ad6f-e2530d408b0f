{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "from augment.research.retrieval import retrieval_database\n", "\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.core.data_paths import canonicalize_path\n", "\n", "import research.core.prompt_formatters  # pull in registrations for prompt formatters\n", "\n", "config_butanol = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"butanol/butanol_fr_seth6_0419.1_proj_512_384\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 120,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"simple_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "config_chatanol = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/chatanol/chatanol1-14\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "config_starethanol = {\n", "    \"scorer\": {\n", "        \"name\": \"starcoder_1b\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "        \"additional_yaml_files\": [\n", "            canonicalize_path(\"experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml\", new_path=AUGMENT_ROOT),\n", "        ],\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "config = config_chatanol\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loads a Dense Retriever"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "retriever = retrieval_database.RetrievalDatabase.from_yaml_config(config_chatanol)\n", "retriever.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index Augment Repo\n", "\n", "This takes about 7 mins using a single A40 GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from research.core.types import Document\n", "\n", "\n", "augment_repo = json.load(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/data/eval/chat/basic_eval26_apr15/repos/augment_apr_10_2024.json\"\n", "    ).open()\n", ")\n", "\n", "\n", "retriever.add_docs(\n", "    Document(**doc)\n", "    for doc in augment_repo[\"docs\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Initialize the Chatanol-Expand Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from research.fastbackward.retrieval_models import load_checkpoint\n", "from research.fastbackward import distributed\n", "\n", "distributed.init_distributed_for_training(1)\n", "checkpoint_path = \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-14-expand\"\n", "components = load_checkpoint(checkpoint_path)\n", "extend_model = components.get_with_type(\"model\", torch.nn.Module)\n", "extend_model.to(dtype=torch.float16, device=torch.device(\"cuda\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from experimental.igor.systems import chatanol\n", "from tensordict import TensorDict\n", "\n", "from research.core.types import Chunk\n", "\n", "class ScoredChunk(Chunk):\n", "    def __init__(self, chunk: Chunk, score: float):\n", "        super().__init__(**chunk.__dict__)\n", "        self.score = score\n", "    \n", "    def merge(self, other: \"ScoredChunk\") -> \"ScoredChunk\":\n", "        merged_chunk = ScoredChunk(super().merge(other), 0)\n", "\n", "        lenOverlap = merged_chunk.length - (self.length + other.length)\n", "        lenSelfOnly = self.length - lenOverlap\n", "        lenOtherOnly = other.length - lenOverlap\n", "\n", "        merged_chunk.score = (\n", "            self.score * lenSelfOnly\n", "            + other.score * lenOtherOnly\n", "            + ((self.score + other.score) / 2) * lenOverlap\n", "        ) / max(len<PERSON><PERSON><PERSON>n<PERSON>, lenOtherOnly)\n", "\n", "        return merged_chunk\n", "\n", "    def to_base_chunk(self):\n", "        dict = self.__dict__.copy()\n", "        del dict[\"score\"]\n", "        return Chunk(**dict)\n", "\n", "    def __repr__(self):\n", "        return f\"ScoredChunk({super().__repr__()}, {self.score})\"\n", "\n", "def process_chunks(question, scored_chunks, unscored_chunks, char_limit, verbosity=0):\n", "    chatanol_query_prompt_formatter = chatanol.ChatanolQueryFormatter()\n", "    chatanol_query_prompt_formatter.tokenizer_name = \"StarCoderTokenizer\"\n", "    extend_prompt_batch = []\n", "    batch_len = 1\n", "    for i in range(batch_len):\n", "        extend_prompt, _ = chatanol_query_prompt_formatter.prepare_prompt(\n", "            ModelInput(\n", "                question,\n", "                extra={\n", "                    \"known_chunks\": [\n", "                        unscored_chunks[j]\n", "                        for j in range(len(unscored_chunks)) if j % batch_len == i\n", "                    ]\n", "                },\n", "            )\n", "        )\n", "        extend_prompt_batch.append(extend_prompt)\n", "    \n", "    max_len = max(len(extend_prompt) for extend_prompt in extend_prompt_batch)\n", "    for i in range(len(extend_prompt_batch)):\n", "        extend_prompt_batch[i] = extend_prompt_batch[i] + [0] * (max_len - len(extend_prompt_batch[i]))\n", "\n", "    extend_model.eval()\n", "    predictions = extend_model.forward(TensorDict(\n", "        {\n", "            \"query_tokens_BLq\": torch.tensor(extend_prompt_batch, device=torch.device(\"cuda\")),\n", "        },\n", "        batch_size=[len(extend_prompt_batch)],\n", "    ))\n", "\n", "    from research.core.types import Chunk\n", "    def expand_chunk(chunk: Chunk, expand_factor: float, to_left: bool):\n", "        if expand_factor < 1:\n", "            raise ValueError(f\"Invalid expand_factor {expand_factor}\")\n", "\n", "        expand_by = round(chunk.length_in_lines * (expand_factor - 1))\n", "        return chatanol.select_chunk_lines(\n", "            chunk.parent_doc,\n", "            chunk.line_offset - (expand_by if to_left else 0),\n", "            chunk.length_in_lines + expand_by,\n", "        )\n", "\n", "    from experimental.igor.systems.chatanol import ChunkSet\n", "    chunk_set = ChunkSet()\n", "\n", "    gain_tensor = predictions[\"gains_BCD\"]\n", "    newly_scored_chunks = list(\n", "        zip(\n", "            gain_tensor[:, 0].tolist(),\n", "            unscored_chunks,\n", "        )\n", "    )\n", "\n", "    expand_left_chunks = list(\n", "        zip(\n", "            (a + b for a, b in zip(gain_tensor[:, 1].tolist(), gain_tensor[:, 0].tolist())),\n", "            [expand_chunk(c, 3, to_left=True) for c in unscored_chunks]\n", "        )\n", "    )\n", "\n", "    expand_right_chunks = list(\n", "        zip(\n", "            (a + b for a, b in zip(gain_tensor[:, 2].tolist(), gain_tensor[:, 0].tolist())),\n", "            [expand_chunk(c, 3, to_left=False) for c in unscored_chunks]\n", "        )\n", "    )\n", "\n", "    for i, (c1, c2, c3) in enumerate(zip(\n", "        newly_scored_chunks, expand_left_chunks, expand_right_chunks\n", "    )):\n", "        if verbosity >= 1:\n", "            print(\"UNSORTED\", i, c1[1].path, c1[1].line_range, c1[0], c2[0]-c1[0], c3[0]-c1[0])\n", "\n", "    sorted_chunks = sorted(\n", "        scored_chunks + newly_scored_chunks + expand_left_chunks + expand_right_chunks,\n", "        key=lambda x: x[0] / x[1].length,\n", "        #key=lambda x: x[0],\n", "        reverse=True,\n", "    )\n", "\n", "    for i, c in enumerate(sorted_chunks):\n", "        if verbosity >= 1:\n", "            print(\"SORTED\", i, c[1].path, c[1].line_range, c[0])\n", "\n", "    for (ppl, chunk) in sorted_chunks:\n", "        if chunk_set.add_chunk(ScoredChunk(chunk, ppl), char_limit):\n", "            if verbosity >= 2:\n", "                print(\"  - ADDED\", chunk.path, chunk.line_range)\n", "        else:\n", "            if verbosity >= 2:\n", "                print(\"  - SKIPPED\", chunk.path, chunk.line_range)\n", "\n", "    chunks = list(sorted(chunk_set.chunks, key=lambda x: x.score, reverse=True))\n", "    return newly_scored_chunks, chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def limit_chunks(chunks, char_limit):\n", "    total_len = 0\n", "    for i, chunk in enumerate(chunks):\n", "        total_len += chunk.length\n", "        if total_len > char_limit:\n", "            return chunks[:i]\n", "    return chunks\n", "\n", "\n", "def expand_chunks(question, chunks_in, verbose, interm_char_limit, final_char_limit, num_expansions):\n", "    assert num_expansions >= 1\n", "    expanded_chunks = limit_chunks(chunks_in, interm_char_limit)\n", "\n", "    if verbose:\n", "        print(question)\n", "        print(\"======== Retrieved chunks ========\")    \n", "        for idx, chunk in enumerate(expanded_chunks ):\n", "            print(f\"({idx}) -- {chunk.path} {chunk.line_range} --\")\n", "        print(\"Length:\", sum(chunk.length for chunk in expanded_chunks ))\n", "    \n", "    unscored_chunks = expanded_chunks\n", "    all_scored_chunks = []\n", "    best_chunks = []\n", "\n", "    for i in range(num_expansions):\n", "        char_limit = final_char_limit if i == num_expansions - 1 else interm_char_limit\n", "        add_scored_chunks, best_chunks = process_chunks(question, all_scored_chunks, unscored_chunks, char_limit)\n", "        all_scored_chunks  += add_scored_chunks\n", "\n", "        if verbose:\n", "            print(f\"======== Expansion {i+1} ========\")\n", "            for idx, chunk in enumerate(best_chunks):\n", "                print(f\"({idx}) {chunk.path} {chunk.line_range} -- {chunk.score}\")\n", "            print(\"Length:\", sum(chunk.length for chunk in best_chunks))\n", "            print()\n", "\n", "        unscored_chunks = [c.to_base_chunk() for c in best_chunks]\n", "\n", "    return best_chunks\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Callable\n", "\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "\n", "\n", "def generate_report(answer_func: Callable[[str], list[ScoredChunk]], name):\n", "    vincent_questions = [\n", "        \"Where in the context manager service do we delete old blobs?\",\n", "        \"What does VSCode extension send to the chat backend as context?\",\n", "        \"Is loading the document embeddings in th searcher sequential with computing scores?\",\n", "        \"What's the full class definition of DenseRetrievalScorer?\",\n", "        \"Where is the tokenizer for StarCoder defined?\",\n", "        \"What types are defined in research/gpt-neox/megatron/tokenizer/tokenizer.py?\",\n", "    ]\n", "\n", "    summarize_questions = [\n", "        \"Summarize the code in research/retrieval/libraries/scorers/dense_scorer.py\",\n", "        \"Summarize the code contained in research/retrieval/libraries/rerankers\",\n", "        \"Summarize the code in research/retrieval/libraries/scorers/dense_scorer.py\",\n", "    ]\n", "\n", "    colin_questions = [\n", "        \"Summarize all the different retrieval scorers implemented in our research code.\",\n", "        \"Do we have a method for reading a jsonl zst file?\",\n", "        \"Are there evaluation methods on the AbstractSystem interface?\",\n", "        \"How do I run perplexity distillation with Megatron/NeoX?\",\n", "        \"How do I query s3 data in our research code?\",\n", "        \"Is there a utility to convert a ResearchEditPromptInput to a ModelInput?\",\n", "        \"Have we deployed the diesel retriever to production?\",\n", "        \"Is there a research interface for rerankers?\",\n", "        \"Where does filtering retrieved chunks vs prefix/suffix happen in services?\",\n", "        \"Where does the actual search over document embeddings happen for dense retrieval?\",\n", "        \"Where is the Rogue data pipeline located?\",\n", "        \"Does fastforward model in research use all available GPUs?\"\n", "    ]\n", "\n", "    yury_questions = [\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data?\",\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data in the `experimental/michiel` directory?\",\n", "        \"Where is spark pipeline that generates roguesl fine-tuning data in the `experimental/yury` directory?\",\n", "    ]\n", "\n", "    path_questions = [\n", "        \"What's in directory experimental/colin/scripts?\",\n", "        \"What's in experimental/colin/scripts/load_multiple_models.py?\",\n", "        \"What's in experimental/colin/scripts?\",\n", "        \"What's in experimental/igor/experiments/2024-02-16_dialog?\",\n", "        \"What's in experimental/evan/hydra_repos directory?\",\n", "        \"What's in experimental/evan/hydra_repos/generate_patchset.ipynb?\",\n", "    ]\n", "\n", "    questions = vincent_questions + summarize_questions + colin_questions + yury_questions + path_questions\n", "    \n", "    for question in questions:\n", "        final_chunks = answer_func(question)\n", "        print(question)\n", "        for i, chunk in enumerate(final_chunks):\n", "            print(f\"{i} {chunk.path} {chunk.line_range} {chunk.score}\")\n", "        print(sum(chunk.length for chunk in final_chunks))\n", "        print()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput\n", "\n", "def _chatanol_input(question, extra=None):\n", "    extra_dict = {} if extra is None else {\n", "        \"extra\": extra\n", "    }\n", "    prompt_input = ChatanolRetrieverPromptInput(\n", "        prefix=question,\n", "        suffix=\"\",\n", "        path=\"\",\n", "        prefix_begin=0,\n", "        **extra_dict,\n", "    )\n", "    return prompt_input\n", "\n", "def analyze_question_neox(question, char_limit) -> list[ScoredChunk]:\n", "    chunks, scores = retriever.query(ModelInput(question))\n", "    chunks = limit_chunks(chunks, char_limit)\n", "    return [ScoredChunk(chunk, score) for chunk, score in zip(chunks, scores)]\n", "\n", "def analyze_question_neox_expand(question, char_limit_small_model, char_limit, num_expansions) -> list[ScoredChunk]:\n", "    chunks, _ = retriever.query(ModelInput(question))\n", "    chunks = limit_chunks(chunks, char_limit_small_model)\n", "    chunks = expand_chunks(question, chunks, verbose=False, interm_char_limit=char_limit_small_model, final_char_limit=char_limit, num_expansions=num_expansions)\n", "    return limit_chunks(chunks, char_limit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generate_report(lambda q: analyze_question_neox_expand(q, char_limit_small_model=20000, char_limit=12000, num_expansions=2), \"fb_expand\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
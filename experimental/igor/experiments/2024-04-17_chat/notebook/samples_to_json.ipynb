{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Skipping bazel build.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/05/18 23:55:29 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "24/05/18 23:55:37 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}], "source": ["import json\n", "from research.data.spark import k8s_session\n", "import pyspark.sql.functions as F\n", "\n", "ETHANOL_VERSION = \"chatanol/chatanol1-09\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "STAGE2_URI = f\"{BUCKET_URI}{ETHANOL_VERSION}/02_with_retrieved_chunks\"\n", "TEMP_URI = f\"{BUCKET_URI}{ETHANOL_VERSION}/02_with_retrieved_chunks_sample\"\n", "\n", "PATH = \"/mnt/efs/augment/user/igor/experiments/chatanol/\" + ETHANOL_VERSION.replace(\"/\", \"_\") + \".jsonl\"\n", "\n", "spark = k8s_session(name=\"igor-test\", max_workers=4)\n", "\n", "df = spark.read.parquet(f\"{BUCKET_URI}{ETHANOL_VERSION}/03_with_ppl_scores\")\n", "desired_rows = 64\n", "if desired_rows is not None:\n", "    desired_fraction = desired_rows / df.count()\n", "    if desired_fraction < 1:\n", "        df = df.sample(withReplacement=False, fraction=desired_fraction)\n", "\n", "rows = df.orderBy(<PERSON>.rand()).limit(100).collect()\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["with open(PATH, \"w\") as f:\n", "    for row in rows:\n", "        row_dict = row.asDict()\n", "        row_dict['retrieved_chunks'] = json.loads(row_dict['retrieved_chunks'])\n", "        f.write(json.dumps(row_dict) + \"\\n\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "from research.eval.harness.factories import create_model\n", "\n", "# Configure the distilled model\n", "model_config = {\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-6.7b-instruct/\",\n", "    \"name\": \"deepseek_coder_instruct_hf\",\n", "}\n", "\n", "model = create_model(model_config)\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "\n", "from typing import List\n", "import torch\n", "import torch.nn.functional as torchF\n", "import dataclasses\n", "\n", "import importlib\n", "distill_sample = importlib.import_module(\"experimental.igor.experiments.2024-04-17_chat.dataset.distill-sample\")\n", "\n", "def score_chunks(\n", "    model,\n", "    model_input: ModelInput,\n", "    chunk_lists: list[list[Chunk]],\n", "    batch_size: int,\n", "    name: str,\n", ") -> list[float]:\n", "    scores: list[float] = []\n", "\n", "    for i in range(0, len(chunk_lists), batch_size):\n", "        try:\n", "            output = model.forward_pass(\n", "                [\n", "                    dataclasses.replace(model_input, retrieved_chunks=chunk_list)\n", "                    for chunk_list in chunk_lists[i : (i + batch_size)]\n", "                ]\n", "            )\n", "        except torch.cuda.OutOfMemoryError as e:\n", "            ranges = [\n", "                [f\"{c.parent_doc.path} {c.range}\" for c in chunk_list]\n", "                for chunk_list in chunk_lists[i : (i + batch_size)]\n", "            ]\n", "\n", "            tokens = [\n", "                len(\n", "                    model.prompt_formatter.prepare_prompt(\n", "                        dataclasses.replace(\n", "                            model_input, retrieved_chunks=chunk_list\n", "                        )\n", "                    )[0]\n", "                )\n", "                for chunk_list in chunk_lists[i : (i + batch_size)]\n", "            ]\n", "            raise RuntimeError(\n", "                f\"[{name}] Out of memory on chunklist: {ranges}, {tokens} tokens\"\n", "            ) from e\n", "\n", "        scores += [\n", "            -torchF.cross_entropy(\n", "                o.logits[o.target_mask],\n", "                o.label_tokens[o.target_mask],\n", "                reduction=\"mean\",\n", "            ).item()\n", "            for o in output\n", "        ]\n", "\n", "    return scores\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["timer = distill_sample.Timer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "jsonl_path = \"/mnt/efs/augment/user/igor/experiments/chatanol/scores1.jsonl\"\n", "with open(jsonl_path, \"r\") as f:\n", "    samples = [json.loads(line) for line in f]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["row_idx = 0\n", "retrieved_chunks = distill_sample.deserialize_retrieved_chunks(samples[row_idx][\"retrieved_chunks\"])\n", "\n", "model_input = ModelInput(\n", "    retrieved_chunks=retrieved_chunks,\n", "    target=samples[row_idx][\"answer\"],\n", "    extra={\n", "        \"chat_history\": [],\n", "        \"message\": samples[row_idx][\"question\"],\n", "        \"selected_code\": \"\",\n", "        \"path\": \"\",\n", "    },\n", ")\n", "\n", "def score_chunks_f(chunk_lists, timer, name) -> list[float]:\n", "    timer.start()\n", "    scores = score_chunks(\n", "        model=model,\n", "        model_input=model_input,\n", "        chunk_lists=chunk_lists,\n", "        batch_size=1,\n", "        name=name,\n", "    )\n", "    timer.stop(name)\n", "    return scores\n", "\n", "scores = score_chunks_f(\n", "    [[c] for c in retrieved_chunks[:1]],\n", "    timer,\n", "    \"scores\",\n", "),"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
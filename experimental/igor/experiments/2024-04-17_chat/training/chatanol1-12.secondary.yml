# includes is an ordered list of gpt-neox config files to be loaded
includes:
  - augment_configs/starcoder/model/starcoder.yml
  - augment_configs/starcoder/model/starcoder-1b.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/ethanol.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/loss_scale-65536-w8.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
  - /home/<USER>/augment/experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
  - /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: &exp_name chatanol1-12.secondary
  description: null
  workspace: Dev
  project: igor
  perform_initial_validation: True  # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xH100.yaml"
  gpu_count: 32  # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  wandb_project: chatanol
  wandb_name: *exp_name

  # mode
  ppl_distill: true
  ppl_prompt_format: 2

  # attn masking expected by ppl_prompt_format=2
  attn_mask_mode: doc_causal
  scaled_upper_triang_masked_softmax_fusion: false
  flash_attention: true
  use_pytorch_builtin_flash_attention: true

  # training batch & schedule
  seq_length: 8192
  train_micro_batch_size_per_gpu: 17
  gradient_accumulation_steps: 16
  train_batch_size: 8704
  train_iters: 375
  lr_decay_iters: 375
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 100 # Never
  eval_iters: 1
  save_interval: 125

  # load checkpoint
  load: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  data-path: null
  train_data_paths:
  - /mnt/efs/augment/user/igor/data/chatanol/chatanol1-12.secondary/train
  valid_data_paths:
  - /mnt/efs/augment/user/igor/data/chatanol/chatanol1-12.secondary/valid
  test_data_paths:
  - /mnt/efs/augment/user/igor/data/chatanol/chatanol1-12.secondary/valid

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "\n", "import json\n", "import numpy as np\n", "import pyspark.sql.functions as F\n", "\n", "from functools import partial\n", "from typing import Any, Dict, Generator, List\n", "\n", "from megatron.tokenizer import get_tokenizer\n", "\n", "from research.core.model_input import ModelInput\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.stages.common import (\n", "    export_indexed_dataset,\n", ")\n", "from research.retrieval.types import Chunk, Document\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.retrieval.utils import parse_yaml_config\n", "from research.retrieval.prompt_formatters import SimpleQueryFormatter\n", "\n", "# TODO: should be an import (from research.data.spark.pipelines.pipeline import ObjectDict)\n", "# BUT currently that import blows up.\n", "class ObjectDict(dict):\n", "    \"\"\"Provides both namespace-like and dict-like access to fields.\n", "\n", "    Allows access to fields using both obj.name notation and obj[\"name\"]\n", "    notation. The latter is useful when \"name\" contains periods, for example.\n", "    \"\"\"\n", "\n", "    def __getattr__(self, name: str):\n", "        if name in self:\n", "            return self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "    def __setattr__(self, name: str, value):\n", "        self[name] = value\n", "\n", "    def __delattr__(self, name: str):\n", "        if name in self:\n", "            del self[name]\n", "        else:\n", "            raise AttributeError(\"No such attribute: \" + name)\n", "\n", "def create_prompt_formatter(formatter_config):\n", "    cls_name, kwargs = parse_yaml_config(formatter_config)\n", "    return get_prompt_formatter(\n", "        cls_name, **kwargs\n", "    )\n", "\n", "import os\n", "ITERATION=os.getenv(\"ITERATION\")\n", "if ITERATION is None:\n", "    ITERATION=1\n", "\n", "#ETHANOL_VERSION = f\"ethanol6/ethanol6-09.{ITERATION}\"\n", "#ETHANOL_VERSION = f\"ethanol6/ethanol6-04.{ITERATION}\"\n", "ETHANOL_VERSION = f\"ethanol6/ethanol6-15.{ITERATION}\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "\n", "STAGES_ENABLED = [\n", "    6,  # Shu<PERSON>\n", "    7,  # To<PERSON>ize\n", "    8,  # Explode\n", "    9,  # Export\n", "]\n", "\n", "config = ObjectDict(\n", "    {\n", "        \"dataset_config\": ObjectDict({\n", "            \"seq_length\": 1024,\n", "            \"num_validation_samples\": 8192,\n", "        }),\n", "        \"tokenizer_name\": \"CodeGenTokenizer\",\n", "        \"doc_seq_length\": 1000,\n", "        \"retrieved_docs\": 127,\n", "        \"allow_doc_clipping\": <PERSON><PERSON><PERSON>,\n", "    }\n", ")\n", "\n", "spark_config = {\n", "    \"spark.executor.pyspark.memory\": \"1050g\",\n", "}\n", "\n", "if ETHANOL_VERSION.startswith(\"ethanol6/ethanol6-15\"):\n", "    STAGES_ENABLED = [7, 8, 9]\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-15.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"prefix_ratio\": 0.9,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_prefix\": True,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-14\"):\n", "    STAGES_ENABLED = [7, 8, 9]\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"prefix_ratio\": 0.9,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_prefix\": True,\n", "    }\n", "elif (\n", "    ETHANOL_VERSION.startswith(\"ethanol6/ethanol6-11\")\n", "    or ETHANOL_VERSION.startswith(\"ethanol6/ethanol6-13\")\n", "):\n", "    STAGES_ENABLED = [7, 8, 9]\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_prefix\": True,\n", "    }\n", "elif (\n", "    ETHANOL_VERSION.startswith(\"ethanol6/ethanol6-10\")\n", "    or ETHANOL_VERSION.startswith(\"ethanol6/ethanol6-12\")\n", "):\n", "    STAGES_ENABLED = [7, 8, 9]\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"prefix_ratio\": 0.9,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-09\"):\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_sos\": <PERSON><PERSON><PERSON>,\n", "    }\n", "    config[\"doc_seq_length\"] = 500\n", "    config[\"allow_doc_clipping\"] = True\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-08\"):\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_sos\": <PERSON><PERSON><PERSON>,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-07\"):\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-06\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol3_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"retokenize\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"simple_document\",\n", "        \"add_path\": True,\n", "    }\n", "    config[\"doc_seq_length\"] = 500\n", "    config[\"allow_doc_clipping\"] = True\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-06\"):\n", "    ETHANOL_VERSION_IN = f\"ethanol5/ethanol5-01\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-05\"):\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"simple_document\",\n", "        \"add_path\": True,\n", "    }\n", "    config[\"doc_seq_length\"] = 500\n", "    config[\"allow_doc_clipping\"] = True\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-04\"):\n", "    STAGES_ENABLED = [7, 8, 9]\n", "    ETHANOL_VERSION_IN = f\"ethanol6/ethanol6-03.{ITERATION}\"\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-03\"):\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol3_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"retokenize\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"simple_document\",\n", "        \"add_path\": True,\n", "    }\n", "    config[\"doc_seq_length\"] = 500\n", "    config[\"allow_doc_clipping\"] = True\n", "elif <PERSON>_VERSION == \"ethanol6/ethanol6-02.2\":\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "elif <PERSON>_VERSION == \"ethanol6/ethanol6-02.1\":\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "elif <PERSON>_VERSION == \"ethanol6/ethanol6-02\":\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": config.doc_seq_length - 1,\n", "        \"add_path\": True,\n", "        \"add_prefix\": True,\n", "    }\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol6/ethanol6-01\"):\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "    }\n", "    if ETHANOL_VERSION == \"ethanol6/ethanol6-01.1\":\n", "        config[\"doc_seq_length\"] = 500\n", "        config[\"allow_doc_clipping\"] = True\n", "elif <PERSON>OL_VERSION.startswith(\"ethanol5/\"):\n", "    query_prompt_formatter_config = {\n", "        \"name\": \"ethanol3_query\",\n", "        \"max_tokens\": config.dataset_config.seq_length - 1,\n", "        \"add_path\": True,\n", "        \"retokenize\": True,\n", "    }\n", "\n", "    key_prompt_formatter_config = {\n", "        \"name\": \"simple_document\",\n", "        \"add_path\": True,\n", "    }\n", "    config[\"doc_seq_length\"] = 500\n", "    config[\"allow_doc_clipping\"] = True\n", "else:\n", "    raise ValueError(\"Obsolete config.\")\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]\n", "\n", "STAGE5_URI = f\"{BUCKET_URI}{ETHANOL_VERSION_IN}/05_with_ppl_scores/\"\n", "if 6 in STAGES_ENABLED:\n", "    STAGE6_URI = f\"{BUCKET_URI}{ETHANOL_VERSION}/06_shuffled/\"\n", "else:\n", "    STAGE6_URI = f\"{BUCKET_URI}{ETHANOL_VERSION_IN}/06_shuffled/\"\n", "STAGE7_URI = f\"{TEMP_BUCKET_URI}{ETHANOL_VERSION}/07_tokens/\"\n", "STAGE8_URI = f\"{TEMP_BUCKET_URI}{ETHANOL_VERSION}/08_exploded/\"\n", "\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/igor/data/{ETHANOL_VERSION}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 6: Shuffle"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage6():\n", "    # Shuffle dataset\n", "    spark = k8s_session(\n", "        name=\"igor-dev-tokenize-shuffle\",\n", "        conf=spark_config,\n", "    )\n", "    df=spark.read.parquet(STAGE5_URI)\n", "    num_partitions = df.rdd.getNumPartitions()\n", "\n", "    df.orderBy(<PERSON><PERSON>rand()).repartition(num_partitions).write.parquet(STAGE6_URI)\n", "\n", "if 6 in STAGES_ENABLED:\n", "    stage6()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7: Token<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage7():\n", "    spark = k8s_session(\n", "        name=\"igor-dev-tokenize\",\n", "        conf=spark_config,\n", "        max_workers=32,\n", "    )\n", "\n", "    def pack_prompt(prompt) -> bytearray:\n", "        return bytearray(\n", "            np.pad(\n", "                prompt, (0, 1 + config.dataset_config.seq_length - len(prompt))\n", "            ).astype(np.uint16).newbyteorder(\"<\").tobytes()\n", "        )\n", "\n", "    def pack_prompts(\n", "        prefix,\n", "        suffix,\n", "        middle,\n", "        file_path,\n", "        retrieved_chunks_str,\n", "        ppl_scores_str,\n", "        query_prompt_formatter_config,\n", "        key_prompt_formatter_config,\n", "    ):\n", "        retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks_str)\n", "        #if len(retrieved_chunks) < config.retrieved_docs:\n", "        #    raise ValueError(\n", "        #        f\"Too few retrieved chunks: {len(retrieved_chunks)}, expected {config.retrieved_docs}\"\n", "        #    )\n", "        #    return None\n", "        #assert len(retrieved_chunks) >= config.retrieved_docs\n", "        retrieved_chunks = retrieved_chunks[:config.retrieved_docs]\n", "        ppl_scores = json.loads(ppl_scores_str)\n", "\n", "        import experimental.igor.systems.ethanol  # pull in registrations\n", "        query_prompt_formatter = create_prompt_formatter(query_prompt_formatter_config)\n", "        key_prompt_formatter = create_prompt_formatter(key_prompt_formatter_config)\n", "\n", "        end_of_query_token = query_prompt_formatter.tokenizer.vocab[\"<|ret-endofquery|>\"]\n", "        end_of_key_token = key_prompt_formatter.tokenizer.vocab[\"<|ret-endofkey|>\"]\n", "        pad_token = key_prompt_formatter.tokenizer.pad_id\n", "\n", "        query_prompt, _ = query_prompt_formatter.prepare_prompt(\n", "            ModelInput(prefix=prefix, suffix=suffix, path=file_path)\n", "        )\n", "        query_prompt.append(end_of_query_token)\n", "        if len(query_prompt) > config.dataset_config.seq_length:\n", "            raise ValueError(\n", "                f\"Query token length exceeds seq_len: {len(query_prompt)} > {config.dataset_config.seq_length}\"\n", "            )\n", "\n", "        all_prompts = [pack_prompt(query_prompt)]\n", "\n", "        for chunk_idx, chunk in enumerate(retrieved_chunks):\n", "            # Format the prompt\n", "            prompt = key_prompt_formatter.prepare_prompt(\n", "                ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)\n", "            )\n", "            if len(prompt) > config.doc_seq_length:\n", "                if config.allow_doc_clipping:\n", "                    prompt = prompt[:config.doc_seq_length]\n", "                else:\n", "                    raise ValueError(f\"Prompt too long: {len(prompt)} > {config.doc_seq_length}\")\n", "\n", "            # Encode the perplexity score into tokens.\n", "            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(\n", "                f\"{ppl_scores[chunk_idx]}\"\n", "            )\n", "            \n", "            # Format the footer of the prompt\n", "            suffix = [end_of_key_token] + ppl_info_tokens + [pad_token]\n", "            prompt.extend(suffix)\n", "\n", "            # Check that the prompt is not too long\n", "            if len(prompt) > config.dataset_config.seq_length:\n", "                print(\"===================================================\")\n", "                print(key_prompt_formatter.tokenizer.detokenize(prompt))\n", "                print(\"===================================================\")\n", "                raise ValueError(f\"{id} token length exceeds seq_len: {len(prompt)} > {config.dataset_config.seq_length}\")\n", "\n", "            all_prompts.append(pack_prompt(prompt))\n", "\n", "        return all_prompts\n", "\n", "    result = map_parquet.apply(\n", "        spark,\n", "        partial(\n", "            pack_prompts,\n", "            query_prompt_formatter_config=query_prompt_formatter_config,\n", "            key_prompt_formatter_config=key_prompt_formatter_config\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        output_column=\"prompt_tokens\",\n", "        drop_original_columns=True,\n", "        timeout=7200,\n", "    )\n", "    spark.stop()\n", "\n", "    for e in result['task_info']['stderr']:\n", "        print(e)\n", "\n", "    for e in result['task_info']['stdout']:\n", "        print(e)\n", "\n", "if 7 in STAGES_ENABLED:\n", "    stage7()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8: Explode"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#def stage7():\n", "#    spark = k8s_session(name=\"igor-dev-tokenize-explode\")\n", "#\n", "#    df=spark.read.parquet(STAGE6_URI)\n", "#    df = df.filter(F.size(F.col(\"prompt_tokens\")) == config.retrieved_docs + 1)\n", "#    df = df.withColumn(\"prompt_tokens\", <PERSON><PERSON>explode(\"prompt_tokens\"))\n", "#    df.write.parquet(STAGE7_URI)\n", "#\n", "#    spark.stop()\n", "#stage7()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage8_2():\n", "    def explode_prompts(batch, config=None):\n", "        # Note: str.len() works for lists too\n", "        filtered_batch = batch[batch['prompt_tokens'].str.len() == config.retrieved_docs + 1]\n", "        results = filtered_batch.explode(\"prompt_tokens\")\n", "        return results if len(results) > 0 else None\n", "\n", "    spark = k8s_session(name=\"igor-dev-tokenize-explode2\")\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(\n", "            explode_prompts,\n", "            config=config,\n", "        ),\n", "        input_path=STAGE7_URI,\n", "        output_path=STAGE8_URI,\n", "        output_column=\"prompt_tokens\",\n", "        drop_original_columns=True,\n", "        timeout=7200,\n", "    )\n", "if 8 in STAGES_ENABLED:\n", "    stage8_2()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 9: Convert to Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 9 in STAGES_ENABLED:\n", "    spark = k8s_session(name=\"igor-dev-export_indexed_dataset\")\n", "\n", "    export_indexed_dataset(\n", "        config=ObjectDict(\n", "            config.dataset_config | {\n", "                \"input\": STAGE8_URI,\n", "                \"output\": OUTPUT_PATH,\n", "                \"samples_column\": \"prompt_tokens\",\n", "            }\n", "        ),\n", "        spark=spark,\n", "        tokenizer=get_tokenizer(config.tokenizer_name)\n", "    )\n", "\n", "    spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
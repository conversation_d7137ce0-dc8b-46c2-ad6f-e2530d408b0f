{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains stages for generating retrieval-augmented dataset for fine-tuning.\"\"\"\n", "import json\n", "import logging\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "from research.retrieval.types import Chunk, Document\n", "from research.static_analysis.file_language_estimator import guess_lang_from_fp\n", "from research.static_analysis.fim_prompt import _format_middle\n", "from research.static_analysis.fim_sampling import CSTFimSampler, FimProblem\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from research.core.model_input import ModelInput\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"hexsha\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "\n", "ETHANOL_VERSION = \"ethanol6/ethanol6-02\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/\"\n", "\n", "INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "STAGE1_URI = f\"{TEMP_BUCKET_URI}/igor/{ETHANOL_VERSION}/01_raw_repos/\"\n", "STAGE2_URI = f\"{TEMP_BUCKET_URI}/igor/{ETHANOL_VERSION}/02_sample_processed/\"\n", "OUTPUT_URI = f\"s3a://igor-dev-bucket/{ETHANOL_VERSION}/03_processed/\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"output\": OUTPUT_URI,\n", "        \"languages\": [\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "        \"limit_repos\": 35000,\n", "        \"every_n_lines\": 150,\n", "        \"max_problems_per_file\": 4,\n", "        \"max_prefix_chars\": 8000,\n", "        \"max_suffix_chars\": 8000,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"downsample_small\": True,\n", "        \"keep_full_files\": True,\n", "        \"retriever\": {\n", "            \"scorer\": {\n", "                \"name\": \"ethanol\",\n", "                \"checkpoint_path\": \"ethanol/ethanol3-01.11_b8192_w8_tg0.01\",\n", "            },\n", "            \"chunker\": {\n", "                \"name\": \"line_level\",\n", "                \"max_lines_per_chunk\": 30,\n", "            },\n", "            \"query_formatter\": {\n", "                \"name\": \"ethanol3_query\",\n", "                \"max_lines\": 20,\n", "                \"add_path\": True,\n", "                \"retokenize\": True,\n", "            },\n", "            \"document_formatter\": {\n", "                \"name\": \"simple_document\",\n", "                \"add_path\": True,\n", "            }\n", "        },\n", "        \"num_retrieved_chunks\": 127,\n", "        \"num_retrieved_extra\": 128,\n", "        \"random_seed\": 74912,\n", "    }\n", ")\n", "\n", "\n", "# TODO(michiel) add correctness test\n", "def _file_to_samples(\n", "    file_content: str,\n", "    file_id: str,\n", "    file_path: str,\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", ") -> list[FimProblem]:\n", "    \"\"\"Convert each file into samples.\"\"\"\n", "    every_n_lines = config.every_n_lines\n", "    max_problems_per_file = config.max_problems_per_file\n", "\n", "    seed = int.from_bytes(file_id.encode(), \"little\") + config.random_seed\n", "    sampler.rng.seed(seed)\n", "\n", "    try:\n", "        lang = guess_lang_from_fp(file_path)\n", "        pfile = ParsedFile.parse(path=file_path, lang=lang, code=file_content)\n", "        samples, _ = sampler.sample_every_n_lines(\n", "            pfile,\n", "            every_n_lines=every_n_lines,\n", "            max_problems_per_file=max_problems_per_file,\n", "            best_of_k=1,\n", "        )\n", "        # TOD<PERSON>(mi<PERSON><PERSON>) fix insanity.\n", "        # <PERSON><PERSON> can return empty prefix. Prompt formatters don't like this, so we filter them out for now.\n", "        samples = [sample for sample in samples if sample.prefix]\n", "        return samples\n", "    except Exception:\n", "        logging.error(f\"[{file_path}] Failed.\", exc_info=True)\n", "        return []\n", "\n", "\n", "def serialize_retrieved_chunks(retrieved_chunks: Sequence[Chunk]) -> str:\n", "    \"\"\"Convert retrieved chunks to string for use in dataframe.\"\"\"\n", "\n", "    def to_dict(chunk: Chunk) -> dict[str, Any]:\n", "        chunk_dict = {\n", "            \"id\": chunk.id,\n", "            \"text\": chunk.text,\n", "            \"parent_doc\": {\n", "                \"id\": chunk.parent_doc.id,\n", "                \"path\": chunk.parent_doc.path,\n", "                # WARNING: just storing empty string if we don't want to store file\n", "                \"text\": chunk.parent_doc.text if config.keep_full_files else \"\",\n", "                # Not supporting meta field\n", "            },\n", "            \"char_offset\": chunk.char_offset,\n", "            \"length\": chunk.length,\n", "            \"line_offset\": chunk.line_offset,\n", "            \"length_in_lines\": chunk.length_in_lines,\n", "            # Not supporting meta field\n", "        }\n", "        return chunk_dict\n", "\n", "    return json.dumps([to_dict(chunk) for chunk in retrieved_chunks])\n", "\n", "\n", "def process_repo(\n", "    files: Sequence[Mapping[str, Any]],\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", "    retrieval_database: Any,\n", "    tokenizer: StarCoderTokenizer,\n", ") -> Generator[pd.Series, None, None]:\n", "    \"\"\"Convert entire repo into retrieval-augmented FiM samples.\"\"\"\n", "    # Populate retrieval database with files\n", "    file_idx = 0\n", "    for file in files:\n", "        file_idx += 1\n", "        if file_idx % 100 == 0:\n", "            print(f\"Processing file {file_idx}: {file[PATH_COLUMN]}\")\n", "\n", "        # Only add files of desired languages to be retrieved\n", "        if (\n", "            getattr(config, \"retrieval_languages\", None)\n", "            and file[FILE_LANG_COLUMN] not in config.retrieval_languages\n", "        ):\n", "            continue\n", "\n", "        document = Document(\n", "            id=file[ID_COLUMN], text=file[CONTENT_COLUMN], path=file[PATH_COLUMN]\n", "        )\n", "        retrieval_database.add_doc(document)\n", "\n", "    # Create samples from files and query retrieval database\n", "    # Shuffle files to avoid bias in the sampling process\n", "    files_shuffled = list(files)\n", "    sampler.rng.shuffle(files_shuffled)\n", "\n", "    max_samples = 999999999999\n", "    sample_count = 0\n", "    for file in files_shuffled:\n", "        # TODO(michiel) add option for sampling subset of rows\n", "        \n", "        # Subsample small files\n", "        if config.downsample_small and file[SIZE_COLUMN] < 8000:\n", "            if random.random() > 0.1:\n", "                continue\n", "\n", "\n", "        # Only add files of main languages to be trained on\n", "        if file[FILE_LANG_COLUMN] not in config.languages:\n", "            continue\n", "\n", "        # Construct multiple prefix, suffix, middle samples from file\n", "        base_samples = _file_to_samples(\n", "            file_content=file[CONTENT_COLUMN],\n", "            file_id=file[ID_COLUMN],\n", "            file_path=file[PATH_COLUMN],\n", "            config=config,\n", "            sampler=sampler,\n", "        )\n", "        for sample in base_samples:\n", "            # limit to max_samples\n", "            if sample_count >= max_samples:\n", "                break\n", "            sample_count += 1\n", "\n", "            sample = sample.truncated(\n", "                max_prefix_chars=config.max_prefix_chars,\n", "                max_suffix_chars=config.max_suffix_chars,\n", "            )\n", "            \n", "            model_input = ModelInput(\n", "                prefix=sample.prefix,\n", "                path=file[PATH_COLUMN]\n", "            )\n", "            retrieved_chunks = retrieval_database.query(\n", "                model_input=model_input,\n", "                top_k=(config.num_retrieved_chunks + config.num_retrieved_extra),\n", "            )[0]\n", "\n", "            # Remove chunks that overlap with middle\n", "            middle_char_start = sample.middle_span.range.start\n", "            middle_char_end = sample.original_suffix_range().start\n", "            from research.retrieval import utils as rutils\n", "            retrieved_chunks = list(rutils.filter_overlap_chunks(\n", "                file[PATH_COLUMN],\n", "                rutils.Span(middle_char_start, middle_char_end),\n", "                retrieved_chunks,\n", "            ))[:config.num_retrieved_chunks]\n", "\n", "            middle_tokens = _format_middle(\n", "                problem=sample,\n", "                tkn=tokenizer,\n", "                skip_id=tokenizer.skip_id,\n", "                pause_id=tokenizer.pause_id,\n", "                fim_stop_id=tokenizer.eod_id,\n", "            )\n", "            middle = tokenizer.detokenize(middle_tokens)\n", "\n", "            original_suffix_range = sample.original_suffix_range()\n", "            suffix_offset = len(sample.suffix) - (\n", "                original_suffix_range.stop - original_suffix_range.start\n", "            )\n", "            assert suffix_offset >= 0\n", "\n", "            middle_char_start = sample.middle_span.range.start\n", "            middle_char_end = original_suffix_range.start\n", "\n", "            # Convert sample into row\n", "            yield pd.Series(\n", "                dict(\n", "                    prefix=sample.prefix,\n", "                    middle=middle,\n", "                    suffix=sample.suffix,\n", "                    file_path=sample.file_path,\n", "                    retrieved_chunks=serialize_retrieved_chunks(retrieved_chunks),\n", "                )\n", "            )\n", "    # We keep database, but depopulate inbetween repos\n", "    retrieval_database.remove_all_docs()\n", "\n", "def filter_by_repo_size(df, min_size=None, max_size=None):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    if min_size is not None and max_size is not None:\n", "        condition = (F.col(\"total_size\") >= min_size) & (\n", "            F.col(\"total_size\") <= max_size\n", "        )\n", "    elif min_size is not None:\n", "        condition = F.col(\"total_size\") >= min_size\n", "    elif max_size is not None:\n", "        condition = F.col(\"total_size\") <= max_size\n", "    else:\n", "        condition = None\n", "\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame,\n", "    config: SimpleNamespace,\n", ") -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "\n", "    import experimental.igor.systems.ethanol\n", "\n", "    # TODO(michiel) update for retriever query formatting options\n", "    retrieval_database = create_retriever(config.retriever)\n", "\n", "    retrieval_database.scorer.load()\n", "\n", "    sampler = CSTFimSampler()\n", "    sampler.rng.seed(config.random_seed)\n", "\n", "    tokenizer = StarCoderTokenizer()\n", "\n", "    for files in batch.file_list:\n", "        print(f\"Processing repo {batch.max_stars_repo_name}\")\n", "        yield from process_repo(\n", "            files,\n", "            config=config,\n", "            sampler=sampler,\n", "            retrieval_database=retrieval_database,\n", "            tokenizer=tokenizer,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "# At 2000 partitions each one is between 100 to 200 repos.\n", "# Probably don't want more than 20000 partitions in anycase because we are\n", "# gonna spend most of the time initalizing retrieval databases to that limit.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "if hasattr(config, \"languages\"):\n", "    config.languages = [lang.lower() for lang in config.languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.languages))\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=getattr(config, \"repo_min_size\", None),\n", "    max_size=getattr(config, \"repo_max_size\", None),\n", ")\n", "df = df.limit(config.limit_repos)\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "df = df.repartition(2000)\n", "\n", "# Perform repo-specific processing\n", "df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "spark = k8s_session(\n", "    max_workers=64,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    #input_path=\"s3a://augment-temporary/igor/ethanol5/ethanol5-01/01_raw_repos/part-00305-84a0c5c7-8fb0-4f7f-9c57-557431a9a6cd-c000.zstd.parquet\",\n", "    input_path=STAGE1_URI,\n", "    output_path=OUTPUT_URI,\n", "    timeout=36000,  # 10-hour timeout\n", "    batch_size=4,\n", "    drop_original_columns=True,\n", "    ignore_error=True\n", ")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for output in result[\"task_info\"].stdout:\n", "    print(\"=== OUTPUT ===\")\n", "    print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result[\"task_info\"].columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for output in result[\"task_info\"].stderr:\n", "    print(\"=== ERRORS ===\")\n", "    print(output)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "\n", "import datetime\n", "import json\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from functools import partial\n", "from typing import Any, Dict, List\n", "\n", "from research.core.model_input import ModelInput\n", "from research.data.spark import k8s_session\n", "from research.eval.harness.factories import (\n", "    create_reranker, create_model\n", ")\n", "from research.retrieval.types import Chunk, Document\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "ETHANOL_VERSION_IN = \"ethanol6/ethanol6-02\"\n", "ETHANOL_VERSION = \"ethanol6/ethanol6-03\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "\n", "STAGE3_URI = f\"{BUCKET_URI}{ETHANOL_VERSION_IN}/03_processed/\"\n", "STAGE4_URI = f\"{TEMP_BUCKET_URI}{ETHANOL_VERSION_IN}/04_subsampled/\"\n", "STAGE5_URI = f\"{BUCKET_URI}{ETHANOL_VERSION}/05_with_ppl_scores/\"\n", "\n", "# Configure the Reranker\n", "\n", "if False:       \n", "    # Configure the Reranker\n", "\n", "    model_config = {\n", "        \"checkpoint_path\": \"rogue/diffb1m_1b_alphal_fixtoken\",\n", "        \"name\": \"rogue\",\n", "        \"prompt\": {\n", "            \"max_prefix_tokens\": 100,\n", "            \"max_suffix_tokens\": 100,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_prompt_tokens\": 750,\n", "        }\n", "    }\n", "\n", "    reranker_config = {\n", "        \"name\": \"oracle_perplexity_reranker\",\n", "        \"top_k\": 256,\n", "        \"batchsize\": 4,\n", "    }\n", "else:\n", "    model_config = {\n", "        \"checkpoint_path\": \"rogue/diffb1m_7b_alphal_fixtoken\",\n", "        \"name\": \"rogue\",\n", "        \"prompt\": {\n", "            \"max_prefix_tokens\": 250,\n", "            \"max_suffix_tokens\": 250,\n", "            \"max_retrieved_chunk_tokens\": -1,\n", "            \"max_prompt_tokens\": 1050,\n", "        }\n", "    }\n", "\n", "    reranker_config = {\n", "        \"name\": \"oracle_perplexity_reranker\",\n", "        \"top_k\": 256,\n", "        \"batchsize\": 8,\n", "    }\n", "\n", "\n", "def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:\n", "    def to_chunk(dict_: Dict[str, Any]) -> Chunk:\n", "        return Chunk(\n", "            id=dict_[\"id\"],\n", "            text=dict_[\"text\"],\n", "            parent_doc=Document(\n", "                id=dict_[\"parent_doc\"][\"id\"],\n", "                text=dict_[\"parent_doc\"][\"text\"],\n", "                path=dict_[\"parent_doc\"][\"path\"],\n", "            ),\n", "            char_offset=dict_[\"char_offset\"],\n", "            length=dict_[\"length\"],\n", "            line_offset=dict_[\"line_offset\"],\n", "            length_in_lines=dict_[\"length_in_lines\"],\n", "        )\n", "\n", "    dicts = json.loads(retrieved_chunks)\n", "    return [to_chunk(dict_) for dict_ in dicts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 4: Subsample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage4():\n", "    spark = k8s_session()\n", "\n", "    desired_partitions = 2000\n", "    desired_rows = 320000\n", "    desired_columns = [\n", "            \"prefix\",\n", "            \"suffix\",\n", "            \"middle\",\n", "            \"file_path\",\n", "            \"retrieved_chunks\",\n", "        ]\n", "\n", "    df = spark.read.parquet(STAGE3_URI)\n", "    if desired_rows is not None:\n", "        desired_fraction = desired_rows / df.count()\n", "        if desired_fraction < 1:\n", "            df = df.sample(withReplacement=False, fraction=desired_fraction)\n", "    df.select(desired_columns).orderBy(F.rand()).repartition(desired_partitions).write.parquet(STAGE4_URI)\n", "\n", "    # TODO: Only keep records with a sufficient number of retrieved chunks\n", "\n", "    spark.stop()\n", "#stage4()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 5: Compute Perplexity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage5():\n", "    if True:\n", "        spark_gpu = k8s_session(\n", "            max_workers=32,\n", "            conf={\n", "                \"spark.executor.pyspark.memory\": \"1000G\",\n", "                \"spark.executor.memory\": \"32G\",\n", "                \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "                \"spark.task.cpus\": \"5\",\n", "            },\n", "            gpu_type=\"A100_NVLINK_80GB\",\n", "        )\n", "    else:\n", "        spark_gpu = k8s_session(\n", "            max_workers=32,\n", "            conf={\n", "                \"spark.executor.pyspark.memory\": \"1000G\",\n", "                \"spark.executor.memory\": \"32G\",\n", "                \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "                \"spark.task.cpus\": \"5\",\n", "            },\n", "            gpu_type=\"RTX_A5000\",\n", "        )\n", "\n", "    def compute_ppl(\n", "            batch, model_config, reranker_config\n", "    ) -> list[float]:\n", "        def print_with_time(*args):\n", "            print(datetime.datetime.now().strftime(\"%d.%b %Y %H:%M:%S\"), *args)\n", "\n", "        global cached_reranker\n", "        if \"cached_reranker\" not in globals():\n", "            # Construct a reranker\n", "            print_with_time(\"Constructing the model...\")\n", "            reranking_model = create_model(model_config)\n", "\n", "            print_with_time(\"Constructing the reranker...\")\n", "            cached_reranker = create_reranker(reranking_model, reranker_config)\n", "\n", "            # Load the reranking model\n", "            print_with_time(\"Loading the model...\")\n", "            reranking_model.load()\n", "\n", "        def apply_compute_ppl(prefix, suffix, middle, file_path, retrieved_chunks):\n", "            model_input = ModelInput(\n", "                prefix=prefix,\n", "                suffix=suffix,\n", "                retrieved_chunks=deserialize_retrieved_chunks(retrieved_chunks),\n", "                path=file_path,\n", "                extra={\"ground_truth\": middle},\n", "            )\n", "\n", "            print_with_time(f\"Reranking...\")\n", "            scores = cached_reranker._score(model_input, middle)\n", "            scores_json = json.dumps(scores)\n", "            return scores_json\n", "\n", "        batch[\"ppl_scores\"] = batch.apply(lambda row: apply_compute_ppl(**row), axis=1)\n", "        return batch\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark_gpu,\n", "        partial(compute_ppl, model_config=model_config, reranker_config=reranker_config),\n", "        input_path=STAGE4_URI,\n", "        output_path=STAGE5_URI,\n", "        output_column=\"ppl_scores\",\n", "        timeout=7200,\n", "        drop_original_columns=True,\n", "    )\n", "\n", "    spark_gpu.stop()\n", "\n", "    for e in result['task_info']['stderr']:\n", "        print(\"=== ERROR ===\")\n", "        print(e)\n", "\n", "    for e in result['task_info']['stdout']:\n", "        print(\"=== OUTPUT ===\")\n", "        print(e)\n", "stage5()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
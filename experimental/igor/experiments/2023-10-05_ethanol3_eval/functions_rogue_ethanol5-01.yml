determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval function, diffb1m_16b_alphal_fixtoken, ethanol5-01
  workspace: Dev
  project: Eval
import_modules:
  experimental.igor.systems.ethanol
system:
    name: basic_rag
    model:
      name: rogue
      checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      scorer:
        name: ethanol
        checkpoint_path: ethanol/ethanol5-01
      chunker:
        name: line_level
        max_lines_per_chunk: 30
      query_formatter:
        name: ethanol3_query
        max_tokens: 1023
        add_path: true
        retokenize: true
      document_formatter:
        name: simple_document
        add_path: true
    experimental:
      use_fim_when_possible: True
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: null
task:
    name: hydra
    dataset: repoeval_functions
podspec: A40.yaml

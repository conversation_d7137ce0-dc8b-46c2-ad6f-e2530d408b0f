# Imports

import json
import random
import time
from functools import partial
from pathlib import Path
from statistics import mean
from types import SimpleNamespace
from typing import Any, Dict, List, Sequence

import numpy as np
import pyspark.sql.functions as F
from megatron.data.indexed_dataset import MMapIndexedDataset, make_builder
from megatron.tokenizer import get_tokenizer

from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from base.tokenizers import create_tokenizer_by_name
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.types import Chunk, Document

# GLOBAL CONFIGURATION --------------------------------------------------------

TEMP_BUCKET_URI = "s3a://augment-temporary/igor/"
BUCKET_URI = "s3a://igor-dev-bucket/"
PATHS = dict(
    INPUT_PATH="/mnt/efs/augment/user/igor/data/{}.jsonl",
    STAGE3_URI=BUCKET_URI + "{}/03_with_ppl_scores{}",
    STAGE4_URI=BUCKET_URI + "{}/04_shuffled{}",
    STAGE5_URI=BUCKET_URI + "{}/05_tokenized{}",
    STAGE6_URI=BUCKET_URI + "{}/06_exploded{}",
    OUTPUT_PATH="/mnt/efs/augment/user/igor/data/{}{}",
)


# TOKENIZATION CONFIGURATION --------------------------------------------------

dataset_config = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    query_prompt_formatter_name="ethanol7-completion-query",
    key_prompt_formatter_name="ethanol6-embedding-with-path-key",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=127,  # in Ethanol dataset, we only have 127 docs, not 128
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
    known_chunk_format=1,
)


# UTILS IMPLEMENTATION --------------------------------------------------------


def chunk_to_dict(chunk: Chunk, keep_full_files: bool) -> dict[str, Any]:
    chunk_dict = {
        "id": chunk.id,
        "text": chunk.text,
        "parent_doc": {
            "id": chunk.parent_doc.id,
            "path": chunk.parent_doc.path,
            # WARNING: just storing empty string if we don't want to store file
            "text": chunk.parent_doc.text if keep_full_files else "",
            # Not supporting meta field
        },
        "char_offset": chunk.char_offset,
        "length": chunk.length,
        "line_offset": chunk.line_offset,
        "length_in_lines": chunk.length_in_lines,
        # Not supporting meta field
    }
    return chunk_dict


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], keep_full_files: bool
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, keep_full_files) for chunk in retrieved_chunks]
    )


def serialize_retrieved_chunks_full_n(retrieved_chunks: Sequence[Chunk], n: int) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, i < n) for i, chunk in enumerate(retrieved_chunks)]
    )


def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:
    def to_chunk(dict_: Dict[str, Any]) -> Chunk:
        return Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
        )

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


class Timer:
    def __init__(self):
        self.start_time = 0.0
        self.measurements = {}

    def start(self):
        self.start_time = time.time()

    def stop(self, name):
        self.measurements[name] = time.time() - self.start_time

    def get(self):
        return self.measurements


# STAGES IMPLEMENTATION -------------------------------------------------------


def stage4(stage3_uri, stage4_uri, small_cluster):
    desired_partitions = 500
    spark = k8s_session(max_workers=8, name="igor-code-distill-shuffle")
    spark.read.parquet(stage3_uri).orderBy(F.rand()).repartition(
        desired_partitions
    ).write.parquet(stage4_uri)
    spark.stop()


def stage5(stage4_uri, stage5_uri, small_cluster):
    spark_config = {
        "spark.executor.pyspark.memory": "1050g",
    }
    spark = k8s_session(
        name="igor-code-distill-tokenize",
        conf=spark_config,
        max_workers=32,
    )

    def pack_prompt(prompt: List[int], pad_token: int, should_pad=True) -> bytearray:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + dataset_config.seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)

        return bytearray(prompt_arr.astype(np.uint16).newbyteorder("<").tobytes())

    def pack_prompts(
        prefix="",
        middle="",
        suffix="",
        file_path="",
        retrieved_chunks="",
        retrieval_rank="",
        token_ppl="",
        token_ppl_prompt_len="",  # pylint: disable=unused-argument
        ppl="",
        query_prompt_formatter_name="",
        key_prompt_formatter_name="",
    ):
        token_ppl = json.loads(token_ppl)
        retrieval_rank = json.loads(retrieval_rank)

        if ppl == "":
            ppl_primary = [mean(s) for s in token_ppl]
        else:
            ppl_primary = json.loads(ppl)
        ppl_scores: dict = {
            "scores": ppl_primary,
        }

        retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)
        # if len(retrieved_chunks) < dataset_config.retrieved_docs:
        #    raise ValueError(
        #        f"Too few retrieved chunks: {len(retrieved_chunks)}, expected {dataset_config.retrieved_docs}"
        #    )

        retrieved_chunks = retrieved_chunks[
            : dataset_config.retrieved_docs + 1
        ]  # +1 for the empty chunk

        # pull in registrations
        import experimental.igor.systems.chatanol  # noqa: F401
        import research.core.prompt_formatters  # noqa: F401
        import research.retrieval.chunk_formatters  # noqa: F401
        from experimental.igor.systems.ethanol7 import (
            Ethanol7CompletionRetrieverPromptInput,
        )

        tokenizer = create_tokenizer_by_name(dataset_config.tokenizer_name)
        query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            dataset_config.query_prompt_formatter_name, tokenizer
        )
        key_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            dataset_config.key_prompt_formatter_name, tokenizer
        )

        end_of_query_token = (
            query_prompt_formatter.tokenizer.special_tokens.end_of_query
        )
        end_of_key_token = key_prompt_formatter.tokenizer.special_tokens.end_of_key
        eod_token = key_prompt_formatter.tokenizer.special_tokens.eos
        pad_token = key_prompt_formatter.tokenizer.special_tokens.padding

        if "secondary" in ppl_scores:
            known_chunks = deserialize_retrieved_chunks(
                ppl_scores["secondary"]["chunks"],
            )

            assert len(known_chunks) == len(ppl_scores["secondary"]["gain"])
            assert len(known_chunks) == len(ppl_scores["secondary"]["gain_expand_left"])
            assert len(known_chunks) == len(
                ppl_scores["secondary"]["gain_expand_right"]
            )

            # We need to estimate the length of the prompt, so we need to
            # first account for the prefix, suffix and file path.
            known_chunks_tokens = len(
                query_prompt_formatter.tokenizer.tokenize_safe(
                    prefix + suffix + file_path
                )
            )

            # ... and then add estimated lengths of the known chunks.
            # We stop before we exceed the budget.
            for i in range(len(known_chunks)):
                # estimate the length
                known_chunks_tokens += len(
                    query_prompt_formatter.tokenizer.tokenize_safe(
                        known_chunks[i].parent_doc.path + "\n" + known_chunks[i].text,
                    )
                )

                if known_chunks_tokens > dataset_config.known_chunks_budget:
                    known_chunks = known_chunks[:i]
                    break

            known_chunk_labels = list(
                zip(
                    ppl_scores["secondary"]["gain"][: len(known_chunks)],
                    ppl_scores["secondary"]["gain_expand_left"][: len(known_chunks)],
                    ppl_scores["secondary"]["gain_expand_right"][: len(known_chunks)],
                )
            )

            add_bare_query = (
                dataset_config.add_bare_query
                if hasattr(dataset_config, "add_bare_query")
                else True
            )
            known_chunk_extra = {
                "known_chunks": known_chunks,
                "add_bare_query": add_bare_query,
            }
        else:
            known_chunk_labels = None
            known_chunk_extra = {}

        query_prompt = query_prompt_formatter.format_prompt(
            Ethanol7CompletionRetrieverPromptInput(
                prefix=prefix,
                suffix=suffix,
                path=file_path,
                extra=known_chunk_extra,
            )
        ).tokens()

        query_prompt.append(end_of_query_token)
        if len(query_prompt) > dataset_config.seq_length:
            raise ValueError(
                f"Query token length exceeds seq_len: {len(query_prompt)} > {dataset_config.seq_length}"
            )

        scores = ppl_scores["scores"]

        # Fix up the initial "empty chunk". We no longer use this, but the existing datasets
        # have it. (We used to have the "empty chunk" perplexities as a baseline for perplexity
        # gain scores, which we don't use - we just use the perplexity scores directly without
        # subtracting the baseline.)
        assert len(retrieved_chunks) == len(retrieval_rank)
        retrieved_chunks = [
            c for i, c in enumerate(retrieved_chunks) if retrieval_rank[i] >= 0
        ]
        scores = [s for i, s in enumerate(scores) if retrieval_rank[i] >= 0]
        assert len(retrieved_chunks) + 1 == len(retrieval_rank)
        assert len(retrieved_chunks) == len(scores)

        if len(retrieved_chunks) < dataset_config.retrieved_docs:
            return bytearray()

        assert len(retrieved_chunks) == dataset_config.retrieved_docs
        assert len(scores) == dataset_config.retrieved_docs

        chunk_tuples = [(chunk, scores[i]) for i, chunk in enumerate(retrieved_chunks)]

        doc_prompts = []
        for chunk, ppl_score in chunk_tuples:
            # Format the prompt
            prompt = key_prompt_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                ),
            ).tokens()
            if len(prompt) > dataset_config.doc_seq_length:
                if dataset_config.allow_doc_clipping:
                    prompt = prompt[: dataset_config.doc_seq_length]
                else:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {dataset_config.doc_seq_length}"
                    )

            # Encode the perplexity score into tokens.
            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize_safe(
                f"{ppl_score}"
            )

            # Format the footer of the prompt
            if dataset_config.dataset_format == 1:
                suffix = [end_of_key_token] + ppl_info_tokens + [eod_token]
            else:
                assert prompt[-1] == end_of_key_token
                suffix = ppl_info_tokens + [end_of_key_token]

            prompt.extend(suffix)

            # Check that the prompt is not too long
            if len(prompt) > dataset_config.seq_length:
                print("===================================================")
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))

        # sort the documents in descending order of perplexity and keep the better ones
        if False:
            doc_prompts = [
                prompt
                for _ppl_score, prompt in sorted(
                    doc_prompts, key=lambda x: x[0], reverse=True
                )
            ][: dataset_config.retrieved_docs]
        else:
            assert len(doc_prompts) <= dataset_config.retrieved_docs
            doc_prompts = [prompt for _ppl_score, prompt in doc_prompts]

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        assert dataset_config.dataset_format == 2
        all_tokens = query_prompt
        all_tokens.extend(sum(doc_prompts, []))

        # Note: this can exceed the maximum query length -- we won't have a loss
        # on these tokens
        if dataset_config.include_known_chunk_labels:
            all_tokens.extend(
                query_prompt_formatter.tokenizer.tokenize_safe(
                    json.dumps(
                        {
                            "known_chunk_labels": known_chunk_labels,
                        }
                    )
                )
            )
            return pack_prompt(all_tokens, pad_token, False)

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
            key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()


def stage6(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.dataset_format == 2
        assert dataset_config.doc_batch_group_size == 1

        filtered_batch = batch[batch["prompt_tokens"].apply(len) > 0]

        # HACK one-time use only. In future, stage5 should take care of this so that
        # the first filtering step is sufficient.
        from research.data.train.common.pack_utils import unpack_tokens

        filtered_batch = batch[
            batch["prompt_tokens"].apply(
                lambda x: sum(1 for t in unpack_tokens(x) if t == 49167)
                == 2 * dataset_config.retrieved_docs
            )
        ]

        # results = filtered_batch.explode("prompt_tokens")
        results = filtered_batch
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-code-distill-explode",
        max_workers=32 if not small_cluster else 2,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


def stage7(stage6_uri, output_path, small_cluster):
    spark = k8s_session(
        name="igor-code-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": stage6_uri,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(dataset_config.research_tokenizer_name),
    )

    spark.stop()


def stage8(output_path, small_cluster):
    dataset = MMapIndexedDataset(str(Path(output_path) / "dataset"))
    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
            valid_builder.end_document()

    train_builder.finalize(str(Path(output_path) / "train.idx"))
    valid_builder.finalize(str(Path(output_path) / "valid.idx"))


if __name__ == "__main__":
    import argparse

    # Parse the arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("stages", type=int, help="stage to run", nargs="+")
    parser.add_argument(
        "--suffix", type=str, help="suffix to add to input/output paths", default=""
    )
    parser.add_argument("--version", type=str, help="model version", default="")
    parser.add_argument(
        "--small-cluster", action="store_true", help="use a small cluster"
    )
    args = parser.parse_args()

    # Stage functions
    stages = [
        (4, stage4, ["STAGE3_URI", "STAGE4_URI"]),
        (5, stage5, ["STAGE4_URI", "STAGE5_URI"]),
        (6, stage6, ["STAGE5_URI", "STAGE6_URI"]),
        (7, stage7, ["STAGE6_URI", "OUTPUT_PATH"]),
        (8, stage8, ["OUTPUT_PATH"]),
    ]

    # Run the stages
    for stage_id, stage_f, stage_path_names in stages:
        if stage_id in args.stages:
            suffix = "." + args.suffix if args.suffix else ""
            stage_paths = [
                PATHS[name].format(args.version, suffix) for name in stage_path_names
            ]

            print(f"Running stage {stage_id} with paths: {stage_paths}")
            stage_f(*stage_paths, small_cluster=args.small_cluster)

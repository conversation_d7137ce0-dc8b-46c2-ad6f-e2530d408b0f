# Imports

import json
import random
import time
from functools import partial
from pathlib import Path
from types import SimpleNamespace
from typing import Any, Dict, List, Sequence

import numpy as np
from megatron.data.indexed_dataset import MMapIndexedDataset, make_builder
from megatron.tokenizer import get_tokenizer

from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from base.tokenizers import create_tokenizer_by_name
from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput
from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.chunk_formatters import get_chunk_formatter
from research.retrieval.types import Chunk, Document
from research.retrieval.utils import parse_yaml_config

# GLOBAL CONFIGURATION --------------------------------------------------------

TEMP_BUCKET_URI = "s3a://augment-temporary/igor/"
BUCKET_URI = "s3a://igor-dev-bucket/"
PATHS = dict(
    INPUT_PATH="/mnt/efs/augment/user/igor/data/{}.jsonl",
    STAGE1_URI=BUCKET_URI + "{}/01_inputs{}",
    STAGE2_URI=BUCKET_URI + "{}/02_with_retrieved_chunks{}",
    STAGE3_URI=BUCKET_URI + "{}/03_with_ppl_scores{}",
    STAGE4_URI=BUCKET_URI + "{}/04_shuffled{}",
    STAGE5_URI=BUCKET_URI + "{}/05_tokenized{}",
    STAGE6_URI=BUCKET_URI + "{}/06_exploded{}",
    OUTPUT_PATH="/mnt/efs/augment/user/igor/data/{}{}",
)


# CHUNK RETRIEVAL CONFIGURATION -----------------------------------------------

retrieval_config_version = 2

retrieval_config_v1 = SimpleNamespace(
    **{
        "retriever": {
            "scorer": {
                "name": "ethanol",
                "checkpoint_path": "ethanol/ethanol3-01.11_b8192_w8_tg0.01",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "ethanol3_query",
                "max_lines": 20,
                "add_path": True,
                "retokenize": True,
            },
            "document_formatter": {
                "name": "simple_document",
                "add_path": True,
            },
        },
        "num_expandable_chunks": 64,  # these chunks will be available for expansion
        "num_retrieved_chunks": 127,
        "num_retrieved_extra": 128,
        "prioritize_gold_chunks": True,
        "random_seed": 74912,
    }
)

retrieval_config_v2 = SimpleNamespace(
    **{
        "retriever": {
            "scorer_v2": {
                "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.fix",
                "model_key": "model",
                "tokenizer_name": "starcoder",
                "query_formatter_name": "chatanol-query",
                "document_formatter": "ethanol6-embedding-with-path-key",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "ethanol6_query",
                "max_tokens": 1023,
            },
            "document_formatter": {
                "name": "ethanol6_document",
                "max_tokens": 999,
                "add_path": True,
            },
            "tokenizer_name": "starcoder",
        },
        "num_expandable_chunks": 64,
        "num_retrieved_chunks": 127,
        "num_retrieved_extra": 128,
        "prioritize_gold_chunks": False,
        "random_seed": 74912,
    }
)

# DISTILLATION CONFIGURATION --------------------------------------------------

# Configure the distilled model


distill_prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

### Instruction:
{%- for chunk in retrieved_chunks %}
Consider the following excerpt from {{chunk.parent_doc.path}}:
```
{{chunk.text}}
```

{%- endfor %}

{{message}}

### Response:
"""


distill_config = {
    "batchsize": 1,
    "max_chunks": 3,
    "model_config": {
        # "checkpoint_path": "/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-6.7b-instruct/",
        # "name": "deepseek_coder_instruct_hf",
        "name": "fastforward_deepseek_coder_instruct_7b_fp8",
    },
    "prompt_formatter_config": {
        "name": "chat_template",
        "template": distill_prompt_template,
        "tokenizer_name": "deepseekcoderinstructtokenizer",
    },
    "secondary": {
        "max_prompt_chunks": 32,
        "max_prompt_tokens": 8000,
        "expand_prob": 0.25,
        "expand_factor": 3,
    },
}

PATH_COLUMN = "max_stars_repo_path"
REPO_COLUMN = "max_stars_repo_name"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
PROMPT_COLUMN = "prompt_tokens"
SIZE_COLUMN = "size"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
FILE_LANG_COLUMN = "langpart"

# TOKENIZATION CONFIGURATION --------------------------------------------------


dataset_config_fb = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=False,
)

dataset_config_fb_secondary = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
    add_bare_query=False,
    known_chunk_format=1,
)

dataset_config_fb_expand = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=1,  # could be 0, but we make it 1 to avoid a special case in `train_retriever.py`
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
    known_chunk_format=1,
)

# Expand, but predict individual chunk ppl instead of prefix ppl
dataset_config_fb_expand2 = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=1,  # could be 0, but we make it 1 to avoid a special case in `train_retriever.py`
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
    known_chunk_format=2,
)

dataset_config_fb_expand_hybrid = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
    known_chunk_format=1,
)


def set_mode(mode: str) -> None:
    global dataset_config, query_prompt_formatter_config, key_prompt_formatter_config

    if mode == "fb":
        dataset_config = dataset_config_fb
    elif mode == "fb-secondary":
        dataset_config = dataset_config_fb_secondary
    elif mode == "fb-expand":
        dataset_config = dataset_config_fb_expand
    elif mode == "fb-expand2":
        dataset_config = dataset_config_fb_expand2
    elif mode == "fb-expand-hybrid":
        dataset_config = dataset_config_fb_expand_hybrid
    else:
        raise ValueError(f"Unknown mode: {mode}")

    dataset_config.query_prompt_formatter_name = "ethanol7-chat-query"
    dataset_config.key_prompt_formatter_name = "ethanol6-embedding-with-path-key"


# UTILS IMPLEMENTATION --------------------------------------------------------


def chunk_to_dict(chunk: Chunk, keep_full_files: bool) -> dict[str, Any]:
    chunk_dict = {
        "id": chunk.id,
        "text": chunk.text,
        "parent_doc": {
            "id": chunk.parent_doc.id,
            "path": chunk.parent_doc.path,
            # WARNING: just storing empty string if we don't want to store file
            "text": chunk.parent_doc.text if keep_full_files else "",
            # Not supporting meta field
        },
        "char_offset": chunk.char_offset,
        "length": chunk.length,
        "line_offset": chunk.line_offset,
        "length_in_lines": chunk.length_in_lines,
        # Not supporting meta field
    }
    return chunk_dict


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], keep_full_files: bool
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, keep_full_files) for chunk in retrieved_chunks]
    )


def serialize_retrieved_chunks_full_n(retrieved_chunks: Sequence[Chunk], n: int) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, i < n) for i, chunk in enumerate(retrieved_chunks)]
    )


def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:
    def to_chunk(dict_: Dict[str, Any]) -> Chunk:
        return Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
        )

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


class Timer:
    def __init__(self):
        self.start_time = 0.0
        self.measurements = {}

    def start(self):
        self.start_time = time.time()

    def stop(self, name):
        self.measurements[name] = time.time() - self.start_time

    def get(self):
        return self.measurements


# STAGES IMPLEMENTATION -------------------------------------------------------


def stage5(stage4_uri, stage5_uri, small_cluster):
    def create_prompt_formatter(formatter_config):
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_prompt_formatter(cls_name, **kwargs)

    def create_chunk_formatter(formatter_config):
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_chunk_formatter(cls_name, **kwargs)

    spark_config = {
        "spark.executor.pyspark.memory": "1050g",
    }
    spark = k8s_session(
        name="igor-distill-tokenize",
        conf=spark_config,
        max_workers=32,
    )

    def pack_prompt(prompt: List[int], pad_token: int, should_pad=True) -> bytearray:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + dataset_config.seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)

        return bytearray(prompt_arr.astype(np.uint16).newbyteorder("<").tobytes())

    def pack_prompts(
        question="",
        answer="",
        paths="",
        retrieved_chunks="",
        ppl_scores="",
        query_prompt_formatter_name="",
        key_prompt_formatter_name="",
    ):
        retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)
        if len(retrieved_chunks) < dataset_config.retrieved_docs:
            raise ValueError(
                f"Too few retrieved chunks: {len(retrieved_chunks)}, expected {dataset_config.retrieved_docs}"
            )

        retrieved_chunks = retrieved_chunks[: dataset_config.retrieved_docs]
        ppl_scores = json.loads(ppl_scores)

        # pull in registrations
        import experimental.igor.systems.chatanol  # noqa: F401
        import research.core.prompt_formatters  # noqa: F401
        import research.retrieval.chunk_formatters  # noqa: F401

        tokenizer = create_tokenizer_by_name(dataset_config.tokenizer_name)
        query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            query_prompt_formatter_name, tokenizer
        )
        key_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            key_prompt_formatter_name, tokenizer
        )

        end_of_query_token = (
            query_prompt_formatter.tokenizer.special_tokens.end_of_query
        )
        end_of_key_token = key_prompt_formatter.tokenizer.special_tokens.end_of_key
        eod_token = key_prompt_formatter.tokenizer.special_tokens.eos
        pad_token = key_prompt_formatter.tokenizer.special_tokens.padding

        if "secondary" in ppl_scores:
            known_chunks = deserialize_retrieved_chunks(
                ppl_scores["secondary"]["chunks"],
            )

            assert len(known_chunks) == len(ppl_scores["secondary"]["gain"])
            assert len(known_chunks) == len(ppl_scores["secondary"]["gain_expand_left"])
            assert len(known_chunks) == len(
                ppl_scores["secondary"]["gain_expand_right"]
            )

            # We need to estimate the length of the prompt, so we need to
            # first account for the question
            known_chunks_tokens = len(
                query_prompt_formatter.tokenizer.tokenize_safe(
                    question,
                )
            )

            # ... and then add estimated lengths of the known chunks.
            # We stop before we exceed the budget.
            for i in range(len(known_chunks)):
                # estimate the length
                known_chunks_tokens += len(
                    query_prompt_formatter.tokenizer.tokenize_safe(
                        known_chunks[i].parent_doc.path + "\n" + known_chunks[i].text,
                    )
                )

                if known_chunks_tokens > dataset_config.known_chunks_budget:
                    known_chunks = known_chunks[:i]
                    break

            known_chunk_labels = list(
                zip(
                    ppl_scores["secondary"]["gain"][: len(known_chunks)],
                    ppl_scores["secondary"]["gain_expand_left"][: len(known_chunks)],
                    ppl_scores["secondary"]["gain_expand_right"][: len(known_chunks)],
                )
            )

            add_bare_query = (
                dataset_config.add_bare_query
                if hasattr(dataset_config, "add_bare_query")
                else True
            )
            known_chunk_extra = {
                "known_chunks": known_chunks,
                "add_bare_query": add_bare_query,
            }
        else:
            known_chunk_labels = None
            known_chunk_extra = {}

        query_prompt = query_prompt_formatter.format_prompt(
            ChatanolRetrieverPromptInput(
                prefix=question,
                suffix="",
                path="",
                message="",
                selected_code="",
                extra=known_chunk_extra,
            )
        ).tokens()

        assert query_prompt[-1] == end_of_query_token
        if len(query_prompt) > dataset_config.seq_length:
            raise ValueError(
                f"Query token length exceeds seq_len: {len(query_prompt)} > {dataset_config.seq_length}"
            )

        if "scores" in ppl_scores:
            chunk_tuples = [
                (chunk, ppl_scores["scores"][i])
                for i, chunk in enumerate(retrieved_chunks)
            ]
        else:
            # only expand cases should go here
            assert dataset_config.retrieved_docs <= 1, ValueError(
                f"No perplexity scores found: {ppl_scores.keys()}"
            )
            chunk_tuples = [(chunk, 0) for chunk in retrieved_chunks]

        doc_prompts = []
        for chunk, ppl_score in chunk_tuples:
            # Format the prompt
            prompt = key_prompt_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                ),
            ).tokens()
            if len(prompt) > dataset_config.doc_seq_length:
                if dataset_config.allow_doc_clipping:
                    prompt = prompt[: dataset_config.doc_seq_length]
                else:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {dataset_config.doc_seq_length}"
                    )

            # Encode the perplexity score into tokens.
            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize_safe(
                f"{ppl_score}"
            )

            # Format the footer of the prompt
            if dataset_config.dataset_format == 1:
                suffix = [end_of_key_token] + ppl_info_tokens + [eod_token]
            else:
                assert prompt[-1] == end_of_key_token
                suffix = ppl_info_tokens + [end_of_key_token]

            prompt.extend(suffix)

            # Check that the prompt is not too long
            if len(prompt) > dataset_config.seq_length:
                print("===================================================")
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))

        # sort the documents in descending order of perplexity and keep the better ones
        doc_prompts = [
            prompt
            for _ppl_score, prompt in sorted(
                doc_prompts, key=lambda x: x[0], reverse=True
            )
        ][: dataset_config.retrieved_docs]

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        if dataset_config.dataset_format == 2:
            all_tokens = query_prompt
            all_tokens.extend(sum(doc_prompts, []))

            # Note: this can exceed the maximum query length -- we won't have a loss
            # on these tokens
            if dataset_config.include_known_chunk_labels:
                all_tokens.extend(
                    query_prompt_formatter.tokenizer.tokenize_safe(
                        json.dumps(
                            {
                                "known_chunk_labels": known_chunk_labels,
                            }
                        )
                    )
                )
            return pack_prompt(all_tokens, pad_token, False)
        else:
            if dataset_config.include_known_chunk_labels:
                raise ValueError(
                    "Secondary metadata not supported for dataset format 1"
                )

            doc_group = dataset_config.doc_batch_group_size
            doc_prompts = [
                pack_prompt(sum(doc_prompts[i : (i + doc_group)], []), pad_token)
                for i in range(0, len(doc_prompts), doc_group)
            ]

            # add the query prompt
            return [pack_prompt(query_prompt, pad_token)] + doc_prompts

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
            key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()


def stage6(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.retrieved_docs % dataset_config.doc_batch_group_size == 0
        num_doc_groups = (
            dataset_config.retrieved_docs // dataset_config.doc_batch_group_size
        )

        # Note: str.len() works for lists too
        if dataset_config.dataset_format == 1:
            filtered_batch = batch[
                batch["prompt_tokens"].str.len() == num_doc_groups + 1
            ]
        else:
            filtered_batch = batch

        results = filtered_batch.explode("prompt_tokens")
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-distill-explode",
        max_workers=32 if not small_cluster else 2,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


def stage7(stage6_uri, output_path, small_cluster):
    spark = k8s_session(
        name="igor-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": stage6_uri,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(dataset_config.research_tokenizer_name),
    )

    spark.stop()


def stage8(output_path, small_cluster):
    dataset = MMapIndexedDataset(str(Path(output_path) / "dataset"))
    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
            valid_builder.end_document()

    train_builder.finalize(str(Path(output_path) / "train.idx"))
    valid_builder.finalize(str(Path(output_path) / "valid.idx"))


def validate_configs():
    retrieval_config = (
        retrieval_config_v2 if retrieval_config_version == 2 else retrieval_config_v1
    )
    assert (
        retrieval_config.num_expandable_chunks
        >= distill_config["secondary"]["max_prompt_chunks"]
    )


if __name__ == "__main__":
    import argparse

    # Parse the arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("stages", type=int, help="stage to run", nargs="+")
    parser.add_argument(
        "--suffix", type=str, help="suffix to add to input/output paths", default=""
    )
    parser.add_argument("--version", type=str, help="model version", default="")
    parser.add_argument("--mode", type=str, help="mode to run in", default="neox")
    parser.add_argument(
        "--small-cluster", action="store_true", help="use a small cluster"
    )
    args = parser.parse_args()

    # Update the config
    set_mode(args.mode)
    validate_configs()

    # Stage functions
    stages = [
        (5, stage5, ["STAGE4_URI", "STAGE5_URI"]),
        (6, stage6, ["STAGE5_URI", "STAGE6_URI"]),
        (7, stage7, ["STAGE6_URI", "OUTPUT_PATH"]),
        (8, stage8, ["OUTPUT_PATH"]),
    ]

    # Run the stages
    for stage_id, stage_f, stage_path_names in stages:
        if stage_id in args.stages:
            suffix = "." + args.suffix if args.suffix else ""
            stage_paths = [
                PATHS[name].format(args.version, suffix) for name in stage_path_names
            ]

            print(f"Running stage {stage_id} with paths: {stage_paths}")
            stage_f(*stage_paths, small_cluster=args.small_cluster)

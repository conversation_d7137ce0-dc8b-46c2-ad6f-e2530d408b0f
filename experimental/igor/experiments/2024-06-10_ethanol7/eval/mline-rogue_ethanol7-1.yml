determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b_alphal_fixtoken, 1b_16.1_500_step, 1b_16.1_500_step, 30LineChunk, repoeval_2-3lines
  workspace: Dev
  project: igor
import_modules:
  experimental.igor.systems.ethanol
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: dense_scorer_v2_ffwd
      checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/ethanol7/ethanol7-1/neox/global_step3596
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: base:ethanol7-completion-query
      tokenizer: starcoder
    document_formatter:
      name: base:ethanol6-embedding-with-path-key
      tokenizer: starcoder
  experimental:
    remove_suffix: False
    retriever_top_k: 32
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: repoeval_2-3lines
podspec: 1xA100.yaml

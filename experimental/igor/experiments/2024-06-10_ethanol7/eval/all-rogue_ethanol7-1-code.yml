determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval all, ethanol7-1-code
  workspace: Dev
  project: Eval
import_modules:
  experimental.igor.systems.ethanol
system:
    name: basic_rag
    model:
      checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
      name: rogue
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      #scorer:
      #  name: ethanol
      #  checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/ethanol7/ethanol7-1-code/neox
      scorer:
        additional_yaml_files:
          - >-
            /home/<USER>/augment/experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml
        checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/ethanol7/ethanol7-1-code/neox
        name: starcoder_1b
      chunker:
        name: line_level
        max_lines_per_chunk: 30
      query_formatter:
        name: ethanol7_query
        max_tokens: 1023
        add_path: true
        add_suffix: true
        prefix_ratio: 0.9
      document_formatter:
        name: ethanol6_document
        max_tokens: 999
        add_path: true
    experimental:
      remove_suffix: False
      retriever_top_k: 100
      trim_on_dedent: False
      trim_on_max_lines: null
task:
  name: hydra
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  hydra_block_resource_internet_access: true
podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval function, ethanol7-1 dense_scorer_v2_ffwd
  workspace: Dev
  project: Eval
import_modules:
  experimental.igor.systems.ethanol
system:
    name: basic_rag
    model:
      checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
      name: rogue
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      scorer:
        name: dense_scorer_v2_ffwd
        checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/ethanol7/ethanol7-1/neox/global_step3596
        #query_formatter: ethanol7-completion-query
        #document_formatter: ethanol6-embedding-with-path-key
      chunker:
        name: line_level
        max_lines_per_chunk: 30
      query_formatter:
        name: base:ethanol7-completion-query
        tokenizer: starcoder
      document_formatter:
        name: base:ethanol6-embedding-with-path-key
        tokenizer: starcoder
    experimental:
      use_fim_when_possible: True
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: null
task:
    name: hydra
    dataset: repoeval_functions
podspec: A40.yaml

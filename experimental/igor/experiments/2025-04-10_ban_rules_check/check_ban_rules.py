import re
import sys


class BanRules:
    def __init__(
        self,
        regexp: str | None = None,
        strict_regexp: str | None = None,
        block_prompt_regexp: str | None = None,
    ):
        assert regexp != ""
        assert strict_regexp != ""
        assert block_prompt_regexp != ""

        self.regex = None if regexp is None else re.compile(regexp)
        self.strict_regex = None if strict_regexp is None else re.compile(strict_regexp)
        self.block_prompt_regex = (
            None if block_prompt_regexp is None else re.compile(block_prompt_regexp)
        )

    def set(
        self,
        regexp: str | None,
        strict_regexp: str | None = None,
        block_prompt_regexp: str | None = None,
    ) -> None:
        assert regexp != ""
        assert strict_regexp != ""
        assert block_prompt_regexp != ""

        regex = None if regexp is None else re.compile(regexp)
        strict_regex = None if strict_regexp is None else re.compile(strict_regexp)
        block_prompt_regex = (
            None if block_prompt_regexp is None else re.compile(block_prompt_regexp)
        )
        self.regex = regex
        self.strict_regex = strict_regex
        self.block_prompt_regex = block_prompt_regex

    def get(self) -> re.Pattern[str] | None:
        return self.regex

    def get_strict(self) -> re.Pattern[str] | None:
        return self.strict_regex

    def get_block_prompt(self) -> re.Pattern[str] | None:
        return self.block_prompt_regex


def regexp_definition_parser(content: str) -> tuple[str | None, str | None, str | None]:
    """Parse a regexp definition file.

    Args:
        content: The content of the file.

    Returns:
        A tuple of (regular_regexp, strict_regexp, block_prompt_regexp). Any may be None if not present.
    """
    lines = content.splitlines()

    # First line should be the regular regexp
    if len(lines) == 0 or not lines[0].strip():
        return None, None, None

    regular_regexp = lines[0]
    strict_regexp = None
    block_prompt_regexp = None

    # If there's a second line, it's the strict regexp
    if len(lines) > 1 and lines[1].strip():
        strict_regexp = lines[1]

    # If there's a third line, it's the block prompt regexp
    if len(lines) > 2 and lines[2].strip():
        block_prompt_regexp = lines[2]

    return regular_regexp, strict_regexp, block_prompt_regexp


request_content = """
"""

rules = BanRules()
file_path = "/mnt/efs/augment/misuse/blockexp/blockexp_2025-04-10T14-32-37.txt"

# read rules form file_path
with open(file_path, "r") as f:
    content = f.read()
    regular_regexp, strict_regexp, file_block_prompt_regexp = regexp_definition_parser(
        content
    )
    rules.set(regular_regexp, strict_regexp, file_block_prompt_regexp)

if len(sys.argv) > 1:
    # read request content from file
    try:
        with open(sys.argv[1], "r") as f:
            request_content = f.read()
    except Exception as e:
        print(f"Failed to read request content from file: {e}")
        sys.exit(1)

regexp = rules.get()
if regexp is not None:
    match_result = regexp.search(request_content)
    if match_result is not None:
        print(match_result)

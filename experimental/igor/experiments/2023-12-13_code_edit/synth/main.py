"""Run the code edit pipeline."""

import argparse
import json
import sys
import time
from pathlib import Path

from stages import code_edit_pipeline


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=str,
        default=None,
        help="input directory",
    )
    parser.add_argument(
        "--output",
        type=str,
        default=None,
        help="output directory",
    )
    parser.add_argument(
        "--max",
        type=int,
        default=10,
        help="max number of samples to generate (before filtering)",
    )
    parser.add_argument(
        "--skip",
        type=int,
        default=0,
        help="how many samples to skip (before filtering)",
    )
    parser.add_argument(
        "--use_mistral",
        action="store_true",
        help="Use Mistral model instead of GPT-4",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=4,
        help="How many parallel processes to use",
    )
    parser.add_argument(
        "--mix_name",
        type=str,
        default="mix1",
        help="which mix to use",
    )
    args = parser.parse_args()

    timestr = args.mix_name + "." + time.strftime("%Y-%m-%d_%H-%M-%S")
    result_path = Path(args.output) / timestr
    result_path.mkdir()

    print(f"Running code edit pipeline: {result_path}")

    with (Path(result_path) / "code_edit_pipeline.txt").open("w") as f:
        f.write(" ".join(sys.argv))
        f.write("\n\n")
        json.dump(vars(args), f, indent=2)

    code_edit_pipeline.run_pipeline(
        Path(args.input),
        result_path,
        args.max,
        args.skip,
        args.use_mistral,
        args.num_processes,
        mix_name=args.mix_name,
    )


if __name__ == "__main__":
    main()

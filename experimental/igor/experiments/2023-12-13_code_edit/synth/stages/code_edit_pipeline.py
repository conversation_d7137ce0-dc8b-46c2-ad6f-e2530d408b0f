"""Generate synthetic code edits."""

import argparse
import dataclasses
import difflib
import hashlib
import json
import os
import random
import time
import typing
from functools import partial
from multiprocessing import Pool
from pathlib import Path

from mistralai.client import MistralClient
from mistralai.exceptions import MistralException
from mistralai.models.chat_completion import ChatMessage
from stages.prompts import whole_file_prompts
from tqdm import tqdm

from experimental.dxy.edits.api_lib import generate_response_via_chat
from research.core import utils_for_dataclass, utils_for_file
from research.data.synthetic_code_edit import types


@dataclasses.dataclass
class CodeEditMix:
    """A code edit mix."""

    name: str
    is_direct: bool = False
    is_whole_file: bool = False
    seeds: list = dataclasses.field(default_factory=list)
    sizes: list[str] = dataclasses.field(default_factory=list)


mixes_list = [
    CodeEditMix(
        name="mix1",
        is_direct=False,
        seeds=[
            "replace an important part of the code with a language-specific placeholder such as `// TODO` or `throw new NotImplementedException();` or `...`",
            "remove something important from the code",
            "rename a function in the code to another name that also looks plausible",
            "rename a type in the code to another name that also looks plausible",
            "mess up the logic",
            [
                "remove comments in a small part of the code",
                "remove comments",
            ],
            "change code structure",
            "change the control flow",
            "obfuscate variable names",
            [
                "insert an unused variable",
                "insert an unnecessary if statement",
                "insert a no-op loop",
                "add print statements",
                "insert a code path that will never execute",
                "insert unused code",
            ],
            "remove error handling",
            [
                "modify error handling",
                "modify error handling to ignore errors",
                "improperly handle errors",
            ],
            [
                "alter the data types in a way that causes type mismatches",
                "alter the data types in a way that causes errors",
            ],
            "replace a data structure with a different one",
            [
                "introduce a mistake",
                "introduce an inconsistency",
                "make a mistake",
            ],
            [
                "mess up the formatting",
                "mess up the formatting in a small part of the code",
                "change the formatting in a small part of the code",
            ],
            "remove type annotations or hints wherever possible",
            "extract computation into a variable",
            [
                "inline computation",
                "introduce a helpful variable",
                "extract computation into a variable",
                "extract computation into a function",
            ],
            "delete function body",
            "remove a code path",
            "remove a conditional branch",
            "replace a logical chunk of code with commented-out pseudocode",
            "replace chunks of code with verbal TODOs",
            "change the syntax in some way that doesn't change the meaning",
            [
                "change representation of some literals without changing the value",
                "change a string delimeter, escape sequence, or formatting syntax",
            ],
            "make the code unnecessarily more complicated",
            "introduce a bug",
            "use an alternate API",
        ],
        sizes=["small", "large"],
    ),
    CodeEditMix(
        name="remove-comments",
        is_direct=False,
        seeds=[
            "add comments",
            "add documentation",
            "add very detailed comments",
            "add a few comments",
        ],
        sizes=["small", "large"],
    ),
    CodeEditMix(
        name="remove-space",
        is_direct=False,
        seeds=[
            "make formatting as condense as possible",
            "remove blank lines",
            "remove unnecessary spaces",
            "make code as compact as possible",
        ],
        sizes=["small", "large"],
    ),
    CodeEditMix(
        name="remove-comments-direct",
        is_direct=True,
        seeds=[
            "remove comments",
            "remove documentation",
            "remove comments and documentation",
            "remove print statements and logging",
            "remove blank lines",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="case-change-direct",
        is_direct=True,
        seeds=[
            "convert variables to snake_case",
            "convert variables to camelCase",
            "convert variables to PascalCase",
            "convert variables to SCREAMING_SNAKE_CASE",
            "convert functions to snake_case",
            "convert functions to camelCase",
            "convert functions to PascalCase",
            "convert functions to SCREAMING_SNAKE_CASE",
            "convert types to snake_case",
            "convert types to camelCase",
            "convert types to PascalCase",
            "convert types to SCREAMING_SNAKE_CASE",
            "convert string literals to lower case",
            "convert string literals to UPPER CASE",
            "convert documentation to lower case",
            "convert documentation to UPPER CASE",
            "convert comments to lower case",
            "convert comments to UPPER CASE",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="string-format-direct",
        is_direct=True,
        seeds=[
            "change string formatting to another style",
            "change quotes to single quotes or another valid format for this language",
            "change quotes to double quotes or another valid format for this language",
            "change quotes to backticks or another valid format for this language",
            "change to another string formatting style (e.g., f-string, percent formatting, etc.)",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="write-comments-direct",
        is_direct=True,
        seeds=[
            "add comments",
            "add documentation",
            "add very detailed comments",
            "add a few comments",
            "improve the comments",
            "improve the documentation",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="simplify-direct",
        is_direct=True,
        seeds=[
            "simplify something about the code",
            "simplify the code in some way",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="remove-space-direct",
        is_direct=True,
        seeds=[
            "remove blank lines",
            "remove unnecessary spaces",
            "make code as compact as possible",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="formatting-direct",
        is_direct=True,
        seeds=[
            "improve code formatting, while staying true to the original coding style",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="rename-direct",
        is_direct=True,
        seeds=[
            "rename function",
            "rename variable",
            "rename type",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="refactor-direct",
        is_direct=True,
        seeds=[
            "refactor the code in some way",
            "extract common code",
            "refactor conditions",
            "refactor loops",
            "refactor functions",
            "refactor types",
            "refactor variables",
        ],
        sizes=["large"],
    ),
    CodeEditMix(
        name="delete-code-inverse",
        is_direct=False,
        seeds=[
            "delete a section of code (without adding a comment noting the removal)",
            "delete a line of code (without adding a comment noting the removal)",
            "delete a function",
            "replace function-body with an appropriate placeholder such as `...`, `pass`, `throw new NotImplementedException();` or `// ...`",
        ],
        sizes=[],
    ),
    CodeEditMix(
        name="files1",
        is_whole_file=True,
    ),
]

mixes = {mix.name: mix for mix in mixes_list}


def format_prompt1(code, mix: CodeEditMix):
    random.seed(
        int(hashlib.sha256(code.encode()).hexdigest(), 16) % (2**32)
    )  # Make random.choice reproducible across using different models

    sampled_code_edit = mix.seeds
    while isinstance(sampled_code_edit, list):
        sampled_code_edit = random.choice(sampled_code_edit)
    size_descriptor = ""
    if len(mix.sizes) > 0:
        edit_size = random.choice(mix.sizes)
        size_descriptor = (
            f"The instruction should apply to a {edit_size} part of the code."
        )
    return f"""
Consider the following code-editing instruction:
-----
- {sampled_code_edit}
-----

Apply the code-editing instruction to the following code chunk. Adapt the instruction if necessary.
{size_descriptor}

Code chunk:

```
{code}
```

The output should be shown as follows:
- The adapted instruction, formatted as "Instruction: ..."
- The modified code chunk
Yes/No - check your answer and tell me whether the instruction was correctly followed
Yes/No - does the instruction result in meaningful change?

Don't show me anything else!

Example
-------
Instruction: Replace " with '
```
a = 6
```
Yes
No
"""


def format_prompt2(block1, block2):
    return f"""
Code block A:
```
{block1}
```

Code block B:
```
{block2}
```

Give me an instruction that a developer may have followed to edit code block A to get code block B. The instruction shouldn't mention "code block A" or "code block B": e.g., say "fix formatting" instead of "fix formatting in code block A".

Give me versions of the instruction with different complexity from normal (say 8 words) to extremely terse (2 words, maybe just "fix it", "add missing", etc.) The output should just consist one instruction version per line formatted as "Instruction: ..."

Once that's printed out, check over the whole example. Do you think this is a high-quality example of applying an instruction to a chunk of code?  Output one more line assessing the quality as High, Medium or Low. E.g., "Quality: Medium"
"""


def normalize_blocks(block1, block2):
    def count_empty_lines_at_start(lines):
        count = 0
        for line in lines:
            if line.strip() == "":
                count += 1
            else:
                return count
        return count

    def find_range(lines):
        left_count = count_empty_lines_at_start(lines)
        right_count = count_empty_lines_at_start(reversed(lines))
        if left_count + right_count > len(lines):
            return None, None
        return left_count, len(lines) - right_count

    def approx_match(s1, s2, match_threshold=0.7):
        matcher = difflib.SequenceMatcher(None, s1, s2)
        match = matcher.find_longest_match(0, len(s1), 0, len(s2))
        match_ratio = 2 * match.size / (len(s1) + len(s2))
        return match_ratio >= match_threshold

    block1_lines = block1.splitlines(keepends=True)
    block2_lines = block2.splitlines(keepends=True)

    block1_range = find_range(block1_lines)
    block2_range = find_range(block2_lines)

    if block1_range is None or block2_range is None:
        return None, None
    block1_lines = block1_lines[block1_range[0] : block1_range[1]]
    block2_lines = block2_lines[block2_range[0] : block2_range[1]]

    if not approx_match(block1_lines[0], block2_lines[0]):
        return None, None
    if not approx_match(block1_lines[-1], block2_lines[-1]):
        return None, None

    return "".join(block1_lines), "".join(block2_lines)


def parse_response1(response1) -> dict:
    result = {
        "success": False,
    }

    if len(response1) != 1:
        return result

    chunks = response1[0].split("```")
    if len(chunks) != 3:
        return result
    code = chunks[1]
    # the code may start with a language due to the markdown syntax (e.g., "```python")
    code = code[code.find("\n") :]

    result["code"] = code

    instruction_parts = chunks[0].split("Instruction: ")
    if len(instruction_parts) != 2:
        return result
    result["instruction"] = instruction_parts[1]

    # Comment out for now - this seems to be too strict.
    #
    # if chunks[2].count("Yes") != 2:
    #    return result

    result["success"] = True
    return result


def parse_response2(response):
    result = {
        "success": False,
    }
    if len(response) != 1:
        return result

    lines = response[0].splitlines()
    instructions = [
        line.split("Instruction:")[1].strip()
        for line in lines
        if "Instruction:" in line
    ]

    result["success"] = True
    result["instructions"] = instructions
    return result


MISTRAL_CLIENT = None


def generate_via_mistral(prompt, model, max_tokens):
    global MISTRAL_CLIENT  # pylint: disable=global-statement
    if MISTRAL_CLIENT is None:
        assert (
            "MISTRAL_API_KEY" in os.environ
        ), "MISTRAL_API_KEY env variable is missing"
        MISTRAL_CLIENT = MistralClient()
    messages = [ChatMessage(role="user", content=prompt)]

    try:
        response = MISTRAL_CLIENT.chat(
            model=model, messages=messages, max_tokens=max_tokens
        )
        response_text = response.choices[0].message.content
    except MistralException as e:
        print(f"MistralException: {e}")
        response_text = ""

    return (response_text,)  # To be compatible with `generate_response_via_chat`


def generate_via_openai(
    messages: list[str],
    system_prompt: typing.Optional[str] = None,
    temperature: float = 0.2,
    max_tokens: int = 256,
    model="gpt-3.5-turbo-1106",
    seed: typing.Optional[int] = None,
    num_completion: int = 1,
    use_json: bool = False,
) -> typing.Tuple[str, ...]:
    for i in range(10):
        try:
            return generate_response_via_chat(
                messages,
                system_prompt,
                temperature,
                max_tokens,
                model,
                seed,
                num_completion,
                use_json,
            )
        except Exception:  # pylint: disable=broad-except
            print(f"Failed to generate via OpenAI, retrying {i+1}")
            time.sleep(10)
    raise RuntimeError("Failed to generate via OpenAI")


def process_edit_scope_openai(edit_scope):
    return process_edit_scope(edit_scope, use_mistral=False)


def process_edit_scope_mistral(edit_scope):
    return process_edit_scope(edit_scope, use_mistral=True)


def process_edit_scope(edit_scope, use_mistral: bool = False, mix_name: str = "mix1"):
    code = edit_scope.updated_code
    mix = mixes[mix_name]

    # Request 1: Generate the "old" code
    prompt1 = format_prompt1(code, mix)

    if use_mistral:
        response1_text = generate_via_mistral(prompt1, "mistral-medium", 4096)
    else:
        response1_text = generate_via_openai(
            [prompt1],
            num_completion=1,
            model="gpt-4-1106-preview",
            max_tokens=4096,
        )
    response1 = parse_response1(response1_text)

    result = {
        "success": False,
        "new_code": code,
        "code_edit_data": dataclasses.asdict(edit_scope),
        "response1": response1_text[0],
        "prompt1": prompt1,
        "old_code": response1["code"] if "code" in response1 else None,
        "backward_edit": (
            response1["instruction"] if "instruction" in response1 else None
        ),
    }

    old_code = code
    new_code = response1["code"] if "code" in response1 else None
    if not mix.is_direct:
        # Swap the old and new code
        old_code, new_code = new_code, old_code

    if not response1["success"]:
        return result

    old_code, new_code = normalize_blocks(old_code, new_code)
    if old_code is None or new_code is None:
        result["failure_reason"] = "Failed to normalize."
        return result

    if old_code == new_code:
        result["failure_reason"] = "The old code is the same as the new code."
        return result

    result["old_code"] = old_code
    result["new_code"] = new_code

    # Request 2: Generate the instruction
    prompt2 = format_prompt2(old_code, new_code)

    if use_mistral:
        response2_text = generate_via_mistral(prompt2, "mistral-medium", 4096)
    else:
        response2_text = generate_via_openai(
            [prompt2],
            num_completion=1,
            model="gpt-4-1106-preview",
            max_tokens=4096,
        )
    response2 = parse_response2(response2_text)
    result.update(
        {
            "instructions": response2["instructions"]
            if "instructions" in response2
            else None,
            "response2": response2_text[0],
            "prompt2": prompt2,
        }
    )
    if not response2["success"]:
        return result

    if response2["instructions"] is None:
        return result

    result["success"] = True
    return result


def process_edit_scope_files(edit_scope, use_mistral: bool = False):
    assert not use_mistral

    code = edit_scope.updated_code
    if edit_scope.selected_code:
        raise ValueError("Selected code is not empty")

    prompt = whole_file_prompts.format_prompt(code, edit_scope.file_name)

    response_text = generate_via_openai(
        [prompt],
        num_completion=1,
        model="gpt-4-1106-preview",
        max_tokens=4096,
    )

    result = {
        "success": False,
        "new_code": code,
        "code_edit_data": dataclasses.asdict(edit_scope),
        "response1": response_text[0],
        "prompt1": prompt,
        "old_code": "",
    }
    response = whole_file_prompts.parse_response(response_text)
    result.update(
        {
            "response": response,
            "instruction": response["instruction"],
            "prompt": prompt,
        }
    )
    result["success"] = response["success"]
    return result


def format_result_html(result):
    """Get the html string of the edit data."""
    # Get the instructions list string
    instruction_str = (
        "\n".join(f"- {x}" for x in result["instructions"])
        if "instructions" in result
        else None
    )

    # Get the code diff string
    if (
        "old_code" in result
        and "new_code" in result
        and result["old_code"]
        and result["new_code"]
    ):
        diff_obj = difflib.HtmlDiff()
        diff_obj._legend = ""  # pylint: disable=protected-access
        diff_html = diff_obj.make_file(
            result["old_code"].splitlines(), result["new_code"].splitlines()
        )
    else:
        diff_html = "-"

    # Get the backward edit string
    backward_edit_str = result.get("backward_edit")

    # Get the prompts and responses
    prompt1 = result.get("prompt1")
    response1 = result.get("response1")
    prompt2 = result.get("prompt2")
    response2 = result.get("response2")

    return (
        f"""
<!DOCTYPE html>
<html>
<head>
    <title>Code Visualization</title>
    <style>
        pre {{
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-left: 3px solid #f36d33;
            color: #666;
            page-break-inside: avoid;
            font-family: monospace;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 1.6em;
            max-width: 100%;
            overflow: auto;
            padding: 1em 1.5em;
            display: block;
            word-wrap: break-word;
        }}
    </style>
</head>
<body>
<h2>Status</h2>
<div><b>Success:</b> {result.get("success")}</div>
"""
        + (
            f"""<div><b>Failure Reason:</b> {result.get("failure_reason")}</div>
"""
            if "failure_reason" in result
            else ""
        )
        + (
            f"""<h2>Code Diff</h2>
    <div id="code-diff">{diff_html}</div>
"""
            if diff_html
            else ""
        )
        + (
            f"""<h2>Instruction</h2>
    <pre><code>{instruction_str}</code></pre>
"""
            if instruction_str
            else ""
        )
        + (
            f"""<h2>Backward Code Edit</h2>
    <pre><code>{backward_edit_str}</code></pre>
"""
            if backward_edit_str
            else ""
        )
        + (
            f"""<h2>Prompt 1</h2>
    <pre><code>{prompt1}</code></pre>
"""
            if prompt1
            else ""
        )
        + (
            f"""<h2>Response 1</h2>
    <pre><code>{response1}</code></pre>
    """
            if response1
            else ""
        )
        + (
            f"""<h2>Prompt 2</h2>
    <pre><code>{prompt2}</code></pre>
"""
            if prompt2
            else ""
        )
        + (
            f"""
    <h2>Response 2</h2>
    <pre><code>{response2}</code></pre>
"""
            if response2
            else ""
        )
        + """
</body>
</html>
"""
    )


def run_pipeline(
    input_path: Path,
    output_path: Path,
    max_results: int,
    skip_results: int,
    use_mistral: bool,
    num_processes: int,
    mix_name: str,
):
    edit_scopes = [
        utils_for_dataclass.create_from_dict(types.CodeEditData, x)
        for x in utils_for_file.read_jsonl_zst(input_path)
    ]

    all_result_path = output_path / "all"
    all_result_path.mkdir()

    selected_edit_scopes = edit_scopes[skip_results : skip_results + max_results]
    print(
        f"Running {len(selected_edit_scopes)} samples, edit scopes: {len(edit_scopes)}, skip: {skip_results}, max: {max_results}"
    )

    process_edit_scope_func = (
        (partial(process_edit_scope_files, use_mistral=use_mistral))
        if mixes[mix_name].is_whole_file
        else (partial(process_edit_scope, mix_name=mix_name, use_mistral=use_mistral))
    )

    with Pool(num_processes) as pool:
        result_seq = pool.imap(process_edit_scope_func, selected_edit_scopes)

        for idx, result in enumerate(tqdm(result_seq, total=len(selected_edit_scopes))):
            format_result_html(result)

            # Save results to json
            json_path = all_result_path / f"{idx}.json"
            with json_path.open("w") as f:
                json.dump(result, f)

            # Convert to html
            html_content = format_result_html(result)
            with (all_result_path / f"{idx}.html").open("w") as f:
                f.write(html_content)

            if result["success"]:
                with (output_path / f"{idx}.html").open("w") as f:
                    f.write(html_content)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=str, help="path to the input file")
    parser.add_argument("output", type=str, help="path to the output directory")
    parser.add_argument(
        "--max",
        type=int,
        default=10,
        help="max number of samples to generate (before filtering)",
    )
    parser.add_argument(
        "--skip",
        type=int,
        default=0,
        help="how many samples to skip (before filtering)",
    )
    parser.add_argument(
        "--mix_name",
        type=str,
        default="mix1",
        help="which mix to use",
    )
    parser.add_argument(
        "--num_processes",
        type=int,
        default=4,
        help="number of processes to use",
    )
    args = parser.parse_args()
    run_pipeline(
        Path(args.input),
        Path(args.output),
        args.max,
        args.skip,
        mix_name=args.mix_name,
        use_mistral=False,
        num_processes=args.num_processes,
    )


if __name__ == "__main__":
    main()

"""Post-process the output of the code edit pipeline."""

import argparse
import json
import random
import sys
from pathlib import Path

from research.core import utils_for_dataclass
from research.data.synthetic_code_edit import types


class Selection:
    """Constants for synthetic code edit selection."""

    EXACT_FRAC = 0.2
    SMALL_FRAC = 0.6
    LARGE_FRAC = 0.2

    SLACK_SMALL_SELECTION = 20


def sample_middle(old_code, new_code, align_to_newline):
    # Count matching characters from the beginning
    min_len = min(len(old_code), len(new_code))
    head_match = min_len
    for i in range(min_len):
        if old_code[i] != new_code[i]:
            head_match = i
            break

    # Count matching characters from the end
    tail_match = min_len
    for i in range(min_len):
        if old_code[len(old_code) - i - 1] != new_code[len(new_code) - i - 1]:
            tail_match = i
            break

    if tail_match + head_match > min_len:
        # This is possible if code has common substrings with multiple types of matching.
        # E.g., this happens with trailing spaces. E.g., in "A_B_C_" vs. "A_C_",
        # the head match is "A_" and the tail match is "_C_", so
        # head_match + tail_match (5) exceeds the length of one of the strings (4).
        #
        # Clipping either the head_match or tail_match would solve this. We clip the tail
        # because that seems to result in a more logical middle.
        tail_match = min_len - head_match

    sample = random.random()
    assert sample >= 0.0 and sample <= 1.0

    if sample < Selection.EXACT_FRAC:
        # Select exactly the modified characters
        selection_slack = 0
        pass
    elif sample < Selection.EXACT_FRAC + Selection.SMALL_FRAC:
        # Select the modified characters plus some slack
        selection_slack = Selection.SLACK_SMALL_SELECTION
    else:
        # Randomly expand the selection within the entire code chunk
        assert sample >= 1.0 - Selection.LARGE_FRAC
        selection_slack = max(head_match, tail_match)

    # Compute common prefix and suffix by subtracting slack from head/tail match
    common_prefix = random.randint(
        head_match - min(head_match, selection_slack), head_match
    )
    common_suffix = random.randint(
        tail_match - min(tail_match, selection_slack), tail_match
    )

    if align_to_newline:
        # Adjust prefix to end after a newline or at the beginning of the string
        common_prefix_aligned = 0  # Number of characters in the prefix
        for i in range(0, common_prefix):
            if old_code[i] == "\n" or old_code[i] == "\r":
                common_prefix_aligned = i + 1

        # Move common_suffix to start after a newline or end of the string
        common_suffix_aligned = 0  # Number of characters in the suffix
        for i in range(len(old_code) - common_suffix, len(old_code)):
            if old_code[i] == "\n":
                common_suffix_aligned = len(old_code) - (i + 1)
                break
            elif old_code[i] == "\r":
                common_suffix_aligned = len(old_code) - (i + 1)
                if common_suffix_aligned > 0 and old_code[i + 1] == "\n":
                    common_suffix_aligned -= 1
                break

    return (
        common_prefix_aligned,
        len(old_code) - common_suffix_aligned,
        common_prefix_aligned,
        len(new_code) - common_suffix_aligned,
    )


def run_pipeline(
    input_paths: list[Path], output_path: Path, instruction_choice="random"
):
    output_path.mkdir(exist_ok=True)
    output_file = output_path / "prompts.json"

    prompts = []
    for input_path in input_paths:
        print(f"Processing {input_path}")
        with input_path.open("r", encoding="utf-8") as f:
            all_data = json.load(f)
        for data in all_data:
            if not data["success"]:
                continue

            code_edit_data = utils_for_dataclass.create_from_dict(
                types.CodeEditData, data["code_edit_data"]
            )

            # Normalize the line endings for the last line of old/new code.
            # This seems to be important because the synthetic code edit pipeline
            # often adds a newline to the end of the synthetic old code.
            #
            # This is not the case for the new code, which is the ground truth.
            #
            # In any case, we normalize in both directions, just in case. If either
            # the old code or the new code is missing the final newline, copy the
            # newline from the other code.
            for key1, key2 in [["old_code", "new_code"], ["new_code", "old_code"]]:
                c1 = data[key1]
                c2 = data[key2]
                c1_has_newline = c1.endswith("\n") or c1.endswith("\r")
                c2_has_newline = c2.endswith("\n") or c2.endswith("\r")
                if c1_has_newline and not c2_has_newline:
                    ending = c1[-1]
                    if c1.endswith("\r\n"):
                        ending = "\r\n"
                    c2 += ending
                    data[key2] = c2

            old_code = data["old_code"]
            new_code = data["new_code"]

            old_start, old_end, new_start, new_end = sample_middle(
                old_code,
                new_code,
                align_to_newline=True,
            )
            if old_start is None:
                print("Skipping")
                continue
            assert old_code[:old_start] == new_code[:new_start]
            assert old_code[old_end:] == new_code[new_end:]

            chosen_prefix = code_edit_data.prefix + old_code[:old_start]
            chosen_suffix = old_code[old_end:] + code_edit_data.suffix
            old_middle = old_code[old_start:old_end]
            new_middle = new_code[new_start:new_end]
            if "instruction" in data:
                chosen_instruction = data["instruction"]
            elif instruction_choice == "random":
                chosen_instruction = random.choice(data["instructions"])
            elif instruction_choice == "first":
                chosen_instruction = data["instructions"][0]
            else:
                raise ValueError(f"Unknown instruction choice: {instruction_choice}")

            prompts.append(
                {
                    "instruction": chosen_instruction,
                    "instructions": data["instructions"]
                    if "instructions" in data
                    else None,
                    "path": data["code_edit_data"]["file_name"],
                    "prefix": chosen_prefix,
                    "suffix": chosen_suffix,
                    "old_middle": old_middle,
                    "new_middle": new_middle,
                    "repo_url": data["code_edit_data"]["repo_url"],
                }
            )

    with output_file.open("w") as f:
        json.dump(prompts, f, indent=2)
    print(f"Saved to {output_file}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=str, nargs="+", help="paths to input directories")
    parser.add_argument("--output", type=str, help="path to the output directory")
    parser.add_argument(
        "--instruction_choice",
        type=str,
        default="random",
        choices=["random", "first"],
        help="how to choose the instruction",
    )
    args = parser.parse_args()

    with (Path(args.output) / "build_prompts.txt").open("w") as f:
        f.write(" ".join(sys.argv))
        f.write("\n\n")
        json.dump(vars(args), f, indent=2)

    run_pipeline(
        [Path(x) for x in args.input],
        Path(args.output),
        instruction_choice=args.instruction_choice,
    )


if __name__ == "__main__":
    main()

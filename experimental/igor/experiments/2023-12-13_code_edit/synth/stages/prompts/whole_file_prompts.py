"""Prompt builder for samples of type (instruction -> whole file)."""

import random


def format_prompt(file_code, file_path):  # pylint: disable=unused-argument
    instruction_comment = random.choice(
        [
            """
Please provide an instruction that a developer may have followed to write this code. The instruction should be brief, 1-2 sentences.

Format the output as "Instruction:" followed by the instruction, with no other output.
""",
            """
Please provide an instruction that a developer may have followed to write this code. The instruction should be brief, 2-3 sentences.

Format the output as "Instruction:" followed by the instruction, with no other output.
""",
            """
Please provide an instruction that a developer may have followed to write this code. The instruction should be detailed, 5-10 sentences.

Format the output as "Instruction:" followed by the instruction, with no other output.
""",
            """
Please provide the instructions that a developer may have followed to write this code. The instructions should consist of 5-10 bullet points.

Format the output as "Instruction:" followed by the instruction list.
""",
        ]
    )

    return f"""
```
{file_code}
```
{instruction_comment}
"""


def parse_response(response):
    result = {
        "success": False,
    }
    if len(response) != 1:
        return result

    instruction = response[0]
    matched = False
    for prefix in [
        "Instruction:\n",
        "Instruction: ",
        "Instructions:\n",
        "Instructions: ",
    ]:
        if instruction.startswith(prefix):
            instruction = instruction[len(prefix) :]
            matched = True
            break
    assert matched

    result["success"] = True
    result["instruction"] = instruction
    return result

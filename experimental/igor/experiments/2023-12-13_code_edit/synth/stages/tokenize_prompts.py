"""Tokenize code edit data written by build_prompts.py."""

import argparse
import json
import sys
from pathlib import Path

from tqdm import tqdm

from research.core.all_prompt_formatters import get_prompt_formatter
from research.core.model_input import ModelInput


def run_pipeline(args):
    with (Path(args.dir) / "tokenize_prompts.txt").open("w") as f:
        f.write(" ".join(sys.argv))
        f.write("\n\n")
        json.dump(vars(args), f, indent=2)

    prompt_formatter = get_prompt_formatter(
        args.prompt_formatter_name, **args.prompt_formatter_args
    )

    input_file = args.path / "prompts.json"
    output_file = args.path / "tokenized.json"

    tokenized_prompts = []

    print(f"Processing {input_file}")
    with input_file.open("r") as f:
        records = json.load(f)
    for record in tqdm(records, desc="Tokenizing"):
        tokenized_prompt, _ = prompt_formatter.prepare_prompt(
            ModelInput(
                prefix=record["prefix"],
                suffix=record["suffix"],
                path=record["path"],
                extra={
                    "instruction": record["instruction"],
                    "selected_code": record["old_middle"],
                },
            )
        )

        # Mask the prompt
        tokenized_prompt = list(map(lambda x: -1 * x, tokenized_prompt))

        # Append the ground truth completion to the prompt
        tokenized_prompt.extend(
            prompt_formatter.tokenizer.tokenize(record["new_middle"])
            + prompt_formatter.get_prompt_footer()
        )

        # Add the prompt
        tokenized_prompts.append(
            {
                "tokens": tokenized_prompt,
                **record,
            }
        )

    with output_file.open("w") as f:
        json.dump(tokenized_prompts, f, indent=2)
    print(f"Saved to {output_file}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("dir", type=str, help="path to the input/output directory")
    parser.add_argument(
        "--prompt_formatter_name",
        type=str,
        help="name of the prompt formatter",
    )
    parser.add_argument(
        "--prompt_formatter_args",
        type=json.loads,
        default="{}",
        help="arguments to the prompt formatter",
    )
    args = parser.parse_args()

    run_pipeline(args)


if __name__ == "__main__":
    main()

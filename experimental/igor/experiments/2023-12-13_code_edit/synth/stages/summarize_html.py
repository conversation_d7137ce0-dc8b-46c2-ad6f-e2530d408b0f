"""Summarize the HTML files generated by code_edit_pipeline.py.

This script reads all json files generated by code_edit_pipeline.py and generates a summary
of the results.

The output is a single HTML file with a table of results.

Example usage:
python summarize_html.py --input_path /mnt/efs/augment/user/igor/nlp/2023-05-23_16-24-30/all
"""

import argparse
import difflib
import json
from pathlib import Path


def write_html(data):
    def format_diff(record):
        diff_obj = difflib.HtmlDiff()
        diff_obj._legend = ""  # pylint: disable=protected-access
        diff_html = (
            diff_obj.make_file(
                record["prefix"].splitlines()[-5:], record["prefix"].splitlines()[-5:]
            )
            + diff_obj.make_file(
                record["old_middle"].splitlines(), record["new_middle"].splitlines()
            )
            + diff_obj.make_file(
                record["suffix"].splitlines()[:5], record["suffix"].splitlines()[:5]
            )
        )
        return diff_html

    return (
        """
<!DOCTYPE html>
<html>
<head>
    <title>Code Edits</title>
    <style>
        p {{
            font-size: 20px;
            line-height: 1.6;
        }}
    </style>
</head>
<body>
"""
        + "".join(
            [
                f"""<h2>Example {i+1}</h2>
    <p><b>Instruction:</b> {record["instruction"]}</p>
    <p><b>Prefix / Selection / Suffix:</b></p>
    <div id="code-diff">{format_diff(record)}</div>
    """
                for i, record in enumerate(data)
            ]
        )
        + """
</html>
"""
    )


def run_pipeline(path: Path, max_samples: int = -1):
    """The real main function."""
    output_file = path / "summary_short.html"

    with (path / "prompts.json").open() as f:
        data = json.load(f)

    with output_file.open("w") as f:
        if max_samples > 0:
            data = data[:max_samples]
        f.write(write_html(data))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("dir", type=str, help="path to the input/output directory")
    parser.add_argument(
        "--max",
        type=int,
        default=100,
        help="max number of samples to show",
    )
    args = parser.parse_args()

    run_pipeline(Path(args.dir), args.max)


if __name__ == "__main__":
    main()

This is a data pipeline to generate synthetic code edits.

Stages:

- code_edit_pipeline.py: Given a set of code edit sites represented in a jsonl.zst
  file, generate synthetic code edits by using an AI API.
- build_prompts.py: Post-process the output of the code edit pipeline to generate
  prompts for training a code-edit model.
- tokenize_prompts.py: Generate tokenized prompts for training a code-edit model.
- summarize_html.py: Generate a summary of the results of the code edit pipeline.
"""

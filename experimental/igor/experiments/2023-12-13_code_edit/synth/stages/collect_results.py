"""Collect results from the code edit pipeline stored in /all."""

import argparse
import json
import random
import sys
from pathlib import Path

from tqdm import tqdm


def read_dataset(path_spec):
    """Reads a subset of a dataset."""

    if ":" in path_spec:
        path_str, limit = path_spec.split(":")
        limit = int(limit)
    else:
        path_str = path_spec
        limit = None
    path = Path(path_str)

    results = []
    if (path / "all").is_dir():
        file_paths = list(path.glob("all/*.json"))
        file_paths.sort(key=lambda x: int(x.stem))
        for idx, file_path in tqdm(enumerate(file_paths), total=len(file_paths)):
            with file_path.open("r") as f:
                data = json.load(f)
            if data["success"]:
                data["origin1"] = str(file_path)
                data["idx"] = idx
                results.append(data)
    else:
        if path.is_file():
            file_paths = [path]
        else:
            file_paths = sorted(list(path.glob("*.json")))
        for file_path in tqdm(file_paths):
            with file_path.open("r") as f:
                data = json.load(f)

            for record in data:
                if not record["success"]:
                    raise ValueError(f"Record {record} is not successful")

                for i in range(1, 1000):
                    origin_key = f"origin{i}"
                    if origin_key not in data:
                        record[origin_key] = str(file_path)
                        break
            results.extend(data)

    if limit is None:
        limit = len(results)
    results = random.sample(results, limit)
    return results


def run_pipeline(args):
    output_path = Path(args.output)
    with (output_path / "collect_results.txt").open("w") as f:
        f.write(" ".join(sys.argv))
        f.write("\n\n")
        json.dump(vars(args), f, indent=2)

    results = []
    for input_path in args.input:
        print(f"Processing {input_path}")
        input_dataset = read_dataset(input_path)
        print(f"Loaded {len(input_dataset)} records.")
        results.extend(input_dataset)

    random.shuffle(results)
    print(f"Collected {len(results)} records.")

    output_path.mkdir(parents=True, exist_ok=True)
    all_result_path = output_path / "all-results.json"
    with all_result_path.open("w") as f:
        json.dump(results, f)

    print(f"Saved to {all_result_path}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "input", type=str, nargs="+", help="paths to the input/output directories"
    )
    parser.add_argument("--output", type=str, help="path to the output directory")
    args = parser.parse_args()

    run_pipeline(args)


if __name__ == "__main__":
    main()

"""Build the dataset for the fine-tuning."""

import argparse
import random
import typing
from pathlib import Path

import numpy as np
import torch
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder

from experimental.dxy.edits.util_lib import pack_sequences
from research.core import utils_for_file
from research.core.all_prompt_formatters import get_prompt_formatter


def build_dataset(
    data: list[list[int]], output: typing.Union[Path, str], sequence_length: int
):
    prompt_formatter = get_prompt_formatter("deepseek_coder_instruct")
    sequences, _ = pack_sequences(
        data, sequence_length, -prompt_formatter.tokenizer.eod_id
    )
    print(f"Saving {len(sequences)} sequences to {output} ...")
    output_path = Path(output)
    output_path.parent.mkdir(parents=False, exist_ok=True)
    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(".bin"), dtype=np.int32)
    for sequence in sequences:
        # Make the total sequence length to be sequence_length + 1
        builder.add_item(sequence + [-prompt_formatter.tokenizer.eod_id])
        builder.end_document()
    builder.finalize(output_path.with_suffix(".idx"))
    print(f"Saved {len(sequences)} sequences to {output_path}")


def run_pipeline(input_paths, output_path):
    examples = []
    for in_path in input_paths:
        raw_token_data = utils_for_file.read_json(in_path / "tokenized.json")
        print(f"Loaded {len(raw_token_data)} examples.")
        print(f"Type of the first example: {type(raw_token_data[0])}")
        if isinstance(raw_token_data[0], dict):
            print(f"Keys of the first example: {raw_token_data[0].keys()}")
        examples.extend([x["tokens"] for x in raw_token_data])
    random.shuffle(examples)
    print(f"#examples={len(examples)}")

    for seq_len in (4096, 8192, 16384):
        build_dataset(examples, output_path / f"S{seq_len}-DSCI", seq_len)
        print("\n\n")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("dir", type=str, help="input/output directory")
    args = parser.parse_args()

    run_pipeline([Path(args.dir)], Path(args.dir))


if __name__ == "__main__":
    main()

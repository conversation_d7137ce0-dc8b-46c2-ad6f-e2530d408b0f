{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains stages for generating retrieval-augmented dataset for fine-tuning.\n", "\n", "NOTE: this notebook variant is specifically for generating a dataset for whole-file edits.\n", "\"\"\"\n", "import logging\n", "from datetime import datetime\n", "from functools import partial\n", "from pathlib import Path\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from base.static_analysis.usage_analysis import ParsedFile\n", "from research.fim.fim_sampling import CSTFimSampler, FimProblem\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"hexsha\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "\n", "DROID_VERSION = \"droid/droid-files-03\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "\n", "INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "STAGE1_URI = f\"{BUCKET_URI}{DROID_VERSION}/01_raw_repos/\"\n", "STAGE2_URI = f\"{BUCKET_URI}{DROID_VERSION}/02_processed/\"\n", "STAGE3_URI = f\"{BUCKET_URI}{DROID_VERSION}/03_subsampled/\"\n", "\n", "STAGES_ENABLED = [1,2,3,4]\n", "\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/igor/data/{DROID_VERSION}\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"languages\": [\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "        \"limit_repos\": 5000,\n", "        \"min_middle_lines\": 5,\n", "        \"max_middle_lines\": 150,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"target_num_samples\": 500000,\n", "\n", "        \"min_file_size\": 100,\n", "        \"max_file_size\": 10000,\n", "    }\n", ")\n", "\n", "# Post-process the config\n", "if hasattr(config, \"languages\"):\n", "    config.languages = [lang.lower() for lang in config.languages]\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "def process_repo(\n", "    files: Sequence[Mapping[str, Any]],\n", "    config: SimpleNamespace,\n", "    repo_uuid: str,\n", ") -> Generator[pd.Series, None, None]:\n", "    \"\"\"Convert entire repo into retrieval-augmented FiM samples.\"\"\"\n", "\n", "    # Create samples from files and query retrieval database\n", "    for file in files:\n", "        file_size = file[SIZE_COLUMN]\n", "        if file_size < config.min_file_size or file_size > config.max_file_size:\n", "            continue\n", "\n", "        # Only add files of main languages to be trained on\n", "        if file[FILE_LANG_COLUMN] not in config.languages:\n", "            continue\n", "\n", "        # Convert sample into row\n", "        yield pd.Series(\n", "            dict(\n", "                prefix=\"\",\n", "                middle=file[CONTENT_COLUMN],\n", "                suffix=\"\",\n", "                middle_char_start=0,\n", "                middle_char_end=len(file[CONTENT_COLUMN]),\n", "                file_path=file[PATH_COLUMN],\n", "                repo_uuid=repo_uuid,\n", "            )\n", "        )\n", "\n", "def filter_by_repo_size(df, min_size=None, max_size=None):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    if min_size is not None and max_size is not None:\n", "        condition = (F.col(\"total_size\") >= min_size) & (\n", "            F.col(\"total_size\") <= max_size\n", "        )\n", "    elif min_size is not None:\n", "        condition = F.col(\"total_size\") >= min_size\n", "    elif max_size is not None:\n", "        condition = F.col(\"total_size\") <= max_size\n", "    else:\n", "        condition = None\n", "\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame,\n", "    config: SimpleNamespace,\n", ") -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "\n", "    for files, repo_uuid in zip(batch.file_list, batch.repo_uuid):\n", "        print(f\"Processing repo {batch.max_stars_repo_name}\")\n", "        yield from process_repo(\n", "            files,\n", "            config=config,\n", "            repo_uuid=repo_uuid,\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1: Preprocess, filter, and subsample the dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 1 in STAGES_ENABLED:\n", "    # This just does filtering then stores results to parquet files for later processing.\n", "\n", "    spark = k8s_session(max_workers=16)\n", "    print(\"Processing retrieval samples\")\n", "    df = spark.read.parquet(config.input)\n", "\n", "    if hasattr(config, \"languages\"):\n", "        config.languages = [lang.lower() for lang in config.languages]\n", "        df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.languages))\n", "\n", "    if hasattr(config, \"retrieval_languages\"):\n", "        config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "    df = filter_by_repo_size(\n", "        df,\n", "        min_size=getattr(config, \"repo_min_size\", None),\n", "        max_size=getattr(config, \"repo_max_size\", None),\n", "    )\n", "    df = df.limit(config.limit_repos)\n", "\n", "    # add repo_uuid column so that we can later unambiguously refer to repos\n", "    df = df.withColumn(\"repo_uuid\", F.expr(\"uuid()\"))\n", "\n", "    print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "    df = df.repartition(2000)\n", "    df.write.parquet(STAGE1_URI)\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 2: Generate prompts"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XXX STAGE 2 starting\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/03/13 01:02:31 WARN Utils: Your hostname, igor-dev resolves to a loopback address: *********; using ************** instead (on interface enp3s0)\n", "24/03/13 01:02:31 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "24/03/13 01:03:04 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "WARNING:root:5 files failed to process.  Sample error output:                   \n", "WARNING:root:[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00033-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "24/03/13 01:03:36 WARN ExecutorPodsWatchSnapshotSource: Kubernetes client has been closed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["XXX STAGE 2 done\n", "=== STDERR ===\n", "[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00033-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "=== STDERR ===\n", "[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00032-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "=== STDERR ===\n", "[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00030-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "=== STDERR ===\n", "\n", "=== STDERR ===\n", "[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00031-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "=== STDERR ===\n", "[process_file] Error processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00029-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet: Return a dataframe, series or iterable\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 234, in collect_result\n", "    result_list = list(result)\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 138, in process_partition_pandas\n", "  File \"/tmp/ipykernel_246145/1889604287.py\", line 92, in process_repo\n", "NameError: name 'middle_char_end' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 345, in process_file\n", "    out_batch = collect_result(\n", "  File \"/mnt/efs/augment/python_env/2024-03-13/igor-dev/5db95667-8638-481a-936b-c9c855d04571/lib/python3.9/site-packages/research/data/spark/pipelines/utils/map_parquet.py\", line 237, in collect_result\n", "    raise TypeError(\"Return a dataframe, series or iterable\")\n", "TypeError: Return a dataframe, series or iterable\n", "\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00033-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00033-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fcd583e4810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 1\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 9216\n", "Processing batch: 0 of 1 for igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00033-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "[process_file] Processing batch 1 of 1\n", "Batch shape: (1, 6)\n", "Total input memory: 896\n", "Memory usage by column: {'Index': 128, 'max_stars_repo_name': 67, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Dataframe size:  0.000896\n", "No splitting needed.\n", "Applying df of shape (1, 6)\n", "Input mem usage: {'Index': 128, 'max_stars_repo_name': 67, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Function finished in 0.00s\n", "Processing repo 0    0is1/dgclr\n", "Name: max_stars_repo_name, dtype: object\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00032-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00032-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fcd583e4810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 1\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 9406\n", "Processing batch: 0 of 1 for igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00032-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "[process_file] Processing batch 1 of 1\n", "Batch shape: (1, 6)\n", "Total input memory: 903\n", "Memory usage by column: {'Index': 128, 'max_stars_repo_name': 74, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Dataframe size:  0.000903\n", "No splitting needed.\n", "Applying df of shape (1, 6)\n", "Input mem usage: {'Index': 128, 'max_stars_repo_name': 74, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Function finished in 0.00s\n", "Processing repo 0    4k4xs4ph1r3/grype\n", "Name: max_stars_repo_name, dtype: object\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00030-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00030-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fc042346810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 1\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 9816\n", "Processing batch: 0 of 1 for igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00030-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "[process_file] Processing batch 1 of 1\n", "Batch shape: (1, 6)\n", "Total input memory: 916\n", "Memory usage by column: {'Index': 128, 'max_stars_repo_name': 87, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Dataframe size:  0.000916\n", "No splitting needed.\n", "Applying df of shape (1, 6)\n", "Input mem usage: {'Index': 128, 'max_stars_repo_name': 87, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Function finished in 0.00s\n", "Processing repo 0    745184533/lab-s-data-warehouse\n", "Name: max_stars_repo_name, dtype: object\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00000-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00000-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fc042346810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 0\n", "  num_row_groups: 0\n", "  format_version: 1.0\n", "  serialized_size: 4481\n", "[process_file] Processed s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00000-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet to igor-dev-bucket/droid/droid-files-01/02_processed/part-00000-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00031-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00031-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fe528a73810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 1\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 12674\n", "Processing batch: 0 of 1 for igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00031-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "[process_file] Processing batch 1 of 1\n", "Batch shape: (1, 6)\n", "Total input memory: 902\n", "Memory usage by column: {'Index': 128, 'max_stars_repo_name': 73, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Dataframe size:  0.000902\n", "No splitting needed.\n", "Applying df of shape (1, 6)\n", "Input mem usage: {'Index': 128, 'max_stars_repo_name': 73, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Function finished in 0.00s\n", "Processing repo 0    1171933894/guava\n", "Name: max_stars_repo_name, dtype: object\n", "\n", "=== STDOUT ===\n", "[process_file] Start processing s3a://igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00029-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "Reading parquet file: igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00029-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "File metadata: <pyarrow._parquet.FileMetaData object at 0x7fe528a73810>\n", "  created_by: parquet-mr version 1.12.3 (build f8dced182c4c1fbdec6ccb3185537b5a01e6ed6b)\n", "  num_columns: 33\n", "  num_rows: 1\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 13424\n", "Processing batch: 0 of 1 for igor-dev-bucket/droid/droid-files-01/01_raw_repos/part-00029-49b79108-6d47-4889-8d20-cd844b6c45af-c000.zstd.parquet\n", "[process_file] Processing batch 1 of 1\n", "Batch shape: (1, 6)\n", "Total input memory: 913\n", "Memory usage by column: {'Index': 128, 'max_stars_repo_name': 84, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Dataframe size:  0.000913\n", "No splitting needed.\n", "Applying df of shape (1, 6)\n", "Input mem usage: {'Index': 128, 'max_stars_repo_name': 84, 'max_file_lang': 240, 'max_size_lang': 240, 'total_size': 8, 'file_list': 120, 'repo_uuid': 93}\n", "Function finished in 0.00s\n", "Processing repo 0    17173/generator-java-webapp\n", "Name: max_stars_repo_name, dtype: object\n", "\n"]}], "source": ["if 2 in STAGES_ENABLED:\n", "    print(\"XXX STAGE 2 starting\")\n", "    spark_conf = {\n", "        \"spark.executor.pyspark.memory\": \"1000G\",\n", "        \"spark.executor.memory\": \"30G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "    }\n", "\n", "    spark_conf[\"spark.task.cpus\"] = \"5\"\n", "    spark = k8s_session(\n", "        max_workers=64,\n", "        conf=spark_conf,\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_partition_pandas, config=config),\n", "        input_path=STAGE1_URI,\n", "        output_path=STAGE2_URI,\n", "        timeout=36000,  # 10-hour timeout\n", "        batch_size=4,\n", "        drop_original_columns=True,\n", "        ignore_error=True\n", "    )\n", "\n", "    print(\"XXX STAGE 2 done\")\n", "    for e in result[\"task_info\"][\"stderr\"]:\n", "        print(\"=== STDERR ===\")\n", "        print(e)\n", "    for e in result[\"task_info\"][\"stdout\"]:\n", "        print(\"=== STDOUT ===\")\n", "        print(e)\n", "    spark.stop()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 3: Shuffle & subsample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 3 in STAGES_ENABLED:\n", "    spark = k8s_session()\n", "\n", "    desired_partitions = 2000\n", "    desired_rows = config.target_num_samples\n", "    desired_columns = [\n", "            \"prefix\",\n", "            \"suffix\",\n", "            \"middle\",\n", "            \"file_path\",\n", "            \"repo_uuid\",\n", "        ]\n", "\n", "    df = spark.read.parquet(STAGE2_URI)\n", "    if desired_rows is not None:\n", "        desired_fraction = desired_rows / df.count()\n", "        if desired_fraction < 1:\n", "            df = df.sample(withReplacement=False, fraction=desired_fraction)\n", "    df.select(desired_columns).orderBy(F.rand()).repartition(desired_partitions).write.parquet(STAGE3_URI)\n", "\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 4: Filter & write a jsonl.zst file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 4 in STAGES_ENABLED:\n", "    from research.core.utils_for_file import write_jsonl_zst\n", "    spark = k8s_session()\n", "    df = spark.read.parquet(STAGE3_URI)\n", "    rows = df.collect()\n", "    output_path = Path(OUTPUT_PATH)\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "    def postprocess_edits(rows):\n", "        for row in rows:\n", "            edit = row.asDict()\n", "            middle_line_count = len(edit[\"middle\"].splitlines())\n", "            min_middle_lines = config.min_middle_lines\n", "            max_middle_lines = config.max_middle_lines\n", "            if middle_line_count < min_middle_lines or middle_line_count > max_middle_lines:\n", "                # Skip this edit\n", "                continue\n", "\n", "            # yield a dict matching the CodeEditData format\n", "            yield {\n", "                \"repo_url\": edit[\"repo_uuid\"],\n", "                \"file_name\": edit[\"file_path\"],\n", "                \"prefix\": \"\",\n", "                \"updated_code\": edit[\"middle\"],\n", "                \"suffix\": \"\",\n", "            }\n", "\n", "    timestamp = datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "    output_file = output_path / f\"all.{timestamp}.jsonl.zst\"\n", "    write_jsonl_zst(output_file, list(postprocess_edits(rows)))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
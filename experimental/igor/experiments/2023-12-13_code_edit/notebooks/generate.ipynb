{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains stages for generating retrieval-augmented dataset for fine-tuning.\"\"\"\n", "import logging\n", "from datetime import datetime\n", "from functools import partial\n", "from pathlib import Path\n", "from types import SimpleNamespace\n", "from typing import Any, Generator, List, Mapping, Sequence, Iterable\n", "import random\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from base.static_analysis.usage_analysis import ParsedFile\n", "from research.fim.fim_sampling import CSTFimSampler, FimProblem\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.eval.harness.factories import create_retriever\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "ID_COLUMN = \"hexsha\"\n", "CONTENT_COLUMN = \"content\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "SIZE_COLUMN = \"size\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "\n", "DROID_VERSION = \"droid/droid-repo-02\"\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "\n", "INPUT_URI = \"s3a://the-stack-processed/by-repo\"\n", "STAGE1_URI = f\"{BUCKET_URI}{DROID_VERSION}/01_raw_repos/\"\n", "STAGE2_URI = f\"{BUCKET_URI}{DROID_VERSION}/02_processed/\"\n", "STAGE3_URI = f\"{BUCKET_URI}{DROID_VERSION}/03_subsampled/\"\n", "\n", "STAGES_ENABLED = [3]\n", "\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/igor/data/{DROID_VERSION}\"\n", "\n", "config = SimpleNamespace(\n", "    **{\n", "        \"input\": INPUT_URI,\n", "        \"languages\": [\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "        \"limit_repos\": 35000,\n", "        \"every_n_lines\": 150,\n", "        \"max_problems_per_file\": 4,\n", "        \"max_prefix_chars\": 8000,\n", "        \"max_suffix_chars\": 8000,\n", "        \"max_middle_chars\": 2500,\n", "        \"min_middle_lines\": 30,\n", "        \"max_middle_lines\": 100,\n", "        \"repo_min_size\": 200000,\n", "        \"repo_max_size\": 5000000000000,\n", "        \"small_downsampled_probability\": 0.1,\n", "        \"small_downsample_char_threshold\": 1500,\n", "        \"small_filter_char_threshold\": 500,\n", "        \"random_seed\": 74913,\n", "        \"target_num_samples\": 500000,\n", "    }\n", ")\n", "\n", "# Post-process the config\n", "if hasattr(config, \"languages\"):\n", "    config.languages = [lang.lower() for lang in config.languages]\n", "\n", "if hasattr(config, \"retrieval_languages\"):\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "def _file_to_samples(\n", "    file_content: str,\n", "    file_id: str,\n", "    file_path: str,\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", ") -> list[FimProblem]:\n", "    \"\"\"Convert each file into samples.\"\"\"\n", "    every_n_lines = config.every_n_lines\n", "    max_problems_per_file = config.max_problems_per_file\n", "\n", "    seed = int.from_bytes(file_id.encode(), \"little\") + config.random_seed\n", "    sampler.rng.seed(seed)\n", "\n", "    try:\n", "        lang = guess_lang_from_fp(file_path)\n", "        pfile = ParsedFile.parse(path=file_path, lang=lang, code=file_content)\n", "        samples, _ = sampler.sample_every_n_lines(\n", "            pfile,\n", "            every_n_lines=every_n_lines,\n", "            max_problems_per_file=max_problems_per_file,\n", "        )\n", "        # TOD<PERSON>(mi<PERSON><PERSON>) fix insanity.\n", "        # <PERSON><PERSON> can return empty prefix. Prompt formatters don't like this, so we filter them out for now.\n", "        samples = [sample for sample in samples if sample.prefix]\n", "        return samples\n", "    except Exception:\n", "        logging.error(f\"[{file_path}] Failed.\", exc_info=True)\n", "        return []\n", "\n", "def process_repo(\n", "    files: Sequence[Mapping[str, Any]],\n", "    config: SimpleNamespace,\n", "    sampler: CSTFimSampler,\n", "    repo_uuid: str,\n", ") -> Generator[pd.Series, None, None]:\n", "    \"\"\"Convert entire repo into retrieval-augmented FiM samples.\"\"\"\n", "\n", "    # Create samples from files and query retrieval database\n", "    for file in files:\n", "        # TODO(michiel) add option for sampling subset of rows\n", "\n", "        file_size = file[SIZE_COLUMN]\n", "        # Filter tiny files\n", "        if (\n", "            config.small_filter_char_threshold\n", "            and file_size < config.small_filter_char_threshold\n", "        ):\n", "            continue\n", "\n", "        # Subsample small files\n", "        if (\n", "            config.small_downsampled_probability\n", "            and file_size < config.small_downsample_char_threshold\n", "        ):\n", "            if random.random() > config.small_downsampled_probability:\n", "                continue\n", "\n", "        # Only add files of main languages to be trained on\n", "        if file[FILE_LANG_COLUMN] not in config.languages:\n", "            continue\n", "\n", "        # Construct multiple prefix, suffix, middle samples from file\n", "        base_samples = _file_to_samples(\n", "            file_content=file[CONTENT_COLUMN],\n", "            file_id=file[ID_COLUMN],\n", "            file_path=file[PATH_COLUMN],\n", "            config=config,\n", "            sampler=sampler,\n", "        )\n", "        for sample in base_samples:\n", "            middle_spans = sample.middle_spans\n", "            assert all(\n", "                not middle_span.skipped\n", "                for middle_span in middle_spans\n", "            )\n", "            middle_code = \"\".join(\n", "                middle_span.content.code\n", "                for middle_span in middle_spans\n", "            )\n", "\n", "            # Let's do a sanity check to confirm that the spans are behaving as\n", "            # expected.\n", "            assert middle_code == \"\".join([\n", "                span.code\n", "                for span in sample.all_adjacent_spans()\n", "            ])\n", "\n", "            middle_char_start, middle_char_end = sample.middle_range.to_tuple()\n", "            suffix_offset = len(sample.suffix.range) - len(sample.suffix.code)\n", "\n", "            # Convert sample into row\n", "            yield pd.Series(\n", "                dict(\n", "                    prefix=sample.prefix.code,\n", "                    middle=middle_code,\n", "                    suffix=sample.suffix.code,\n", "                    suffix_offset=suffix_offset,\n", "                    middle_char_start=middle_char_start,\n", "                    middle_char_end=middle_char_end,\n", "                    file_path=str(sample.file_path),\n", "                    repo_uuid=repo_uuid,\n", "                )\n", "            )\n", "\n", "def filter_by_repo_size(df, min_size=None, max_size=None):\n", "    \"\"\"Filter df by repo size.\"\"\"\n", "    # Build filter condition\n", "    if min_size is not None and max_size is not None:\n", "        condition = (F.col(\"total_size\") >= min_size) & (\n", "            F.col(\"total_size\") <= max_size\n", "        )\n", "    elif min_size is not None:\n", "        condition = F.col(\"total_size\") >= min_size\n", "    elif max_size is not None:\n", "        condition = F.col(\"total_size\") <= max_size\n", "    else:\n", "        condition = None\n", "\n", "    # If a condition is specified, apply the filter\n", "    if condition is not None:\n", "        df = df.filter(condition)\n", "    return df\n", "\n", "# This processes one partition of the dataset.\n", "# now we know that batch sizes really isn't that much a deal.\n", "# most of the memory is used by treesitter for its leaks\n", "\n", "\n", "def process_partition_pandas(\n", "    batch: pd.DataFrame,\n", "    config: SimpleNamespace,\n", ") -> Iterable[pd.Series]:\n", "    \"\"\"Process a single partition of the dataset.\n", "\n", "    Args:\n", "        batch: A single partition of the dataset.\n", "        config: The configuration object.\n", "\n", "    Returns:\n", "        A generator of processed rows.\n", "    \"\"\"\n", "\n", "    sampler = CSTFimSampler(auto_closing_rate=0.0)\n", "    sampler.rng.seed(config.random_seed)\n", "\n", "    for files, repo_uuid in zip(batch.file_list, batch.repo_uuid):\n", "        print(f\"Processing repo {batch.max_stars_repo_name}\")\n", "        yield from process_repo(\n", "            files,\n", "            config=config,\n", "            sampler=sampler,\n", "            repo_uuid=repo_uuid,\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1: Preprocess, filter, and subsample the dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 1 in STAGES_ENABLED:\n", "    # This just does filtering then stores results to parquet files for later processing.\n", "\n", "    spark = k8s_session(max_workers=16)\n", "    print(\"Processing retrieval samples\")\n", "    df = spark.read.parquet(config.input)\n", "\n", "    if hasattr(config, \"languages\"):\n", "        config.languages = [lang.lower() for lang in config.languages]\n", "        df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.languages))\n", "\n", "    if hasattr(config, \"retrieval_languages\"):\n", "        config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "    df = filter_by_repo_size(\n", "        df,\n", "        min_size=getattr(config, \"repo_min_size\", None),\n", "        max_size=getattr(config, \"repo_max_size\", None),\n", "    )\n", "    df = df.limit(config.limit_repos)\n", "\n", "    # add repo_uuid column so that we can later unambiguously refer to repos\n", "    df = df.withColumn(\"repo_uuid\", F.expr(\"uuid()\"))\n", "\n", "    print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "    df = df.repartition(2000)\n", "    df.write.parquet(STAGE1_URI)\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 2: Generate prompts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 2 in STAGES_ENABLED:\n", "    spark_conf = {\n", "        \"spark.executor.pyspark.memory\": \"1000G\",\n", "        \"spark.executor.memory\": \"30G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "    }\n", "\n", "    spark_conf[\"spark.task.cpus\"] = \"5\"\n", "    spark = k8s_session(\n", "        max_workers=64,\n", "        conf=spark_conf,\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_partition_pandas, config=config),\n", "        input_path=STAGE1_URI,\n", "        output_path=STAGE2_URI,\n", "        timeout=36000,  # 10-hour timeout\n", "        batch_size=4,\n", "        drop_original_columns=True,\n", "        ignore_error=True\n", "    )\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 3: Shuffle & subsample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 3 in STAGES_ENABLED:\n", "    spark = k8s_session()\n", "\n", "    desired_partitions = 2000\n", "    desired_rows = config.target_num_samples\n", "    desired_columns = [\n", "            \"prefix\",\n", "            \"suffix\",\n", "            \"middle\",\n", "            \"file_path\",\n", "            \"repo_uuid\",\n", "        ]\n", "\n", "    df = spark.read.parquet(STAGE2_URI)\n", "    if desired_rows is not None:\n", "        desired_fraction = desired_rows / df.count()\n", "        if desired_fraction < 1:\n", "            df = df.sample(withReplacement=False, fraction=desired_fraction)\n", "    df.select(desired_columns).orderBy(F.rand()).repartition(desired_partitions).write.parquet(STAGE3_URI)\n", "\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 4: Filter & write a jsonl.zst file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 4 in STAGES_ENABLED:\n", "    from research.core.utils_for_file import write_jsonl_zst\n", "    spark = k8s_session()\n", "    df = spark.read.parquet(STAGE3_URI)\n", "    rows = df.collect()\n", "    output_path = Path(OUTPUT_PATH)\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "    def postprocess_edits(rows):\n", "        for row in rows:\n", "            edit = row.asDict()\n", "            if len (edit[\"middle\"]) > config.max_middle_chars:\n", "                continue\n", "\n", "            middle_line_count = len(edit[\"middle\"].splitlines())\n", "            min_middle_lines = config.min_middle_lines\n", "            max_middle_lines = config.max_middle_lines\n", "            if middle_line_count < min_middle_lines:\n", "                extra_lines = 1 + random.randint(0, max_middle_lines - min_middle_lines) // 2\n", "            elif middle_line_count <= max_middle_lines:\n", "                extra_lines = 1 + random.randint(0, max_middle_lines - middle_line_count) // 2\n", "            else:\n", "                # Skip this edit\n", "                continue\n", "            assert extra_lines >= 1\n", "\n", "            middle_prefix = \"\".join(edit[\"prefix\"].splitlines(keepends=True)[-extra_lines:])\n", "            middle_suffix = \"\".join(edit[\"suffix\"].splitlines(keepends=True)[:extra_lines])\n", "\n", "            middle = middle_prefix + edit[\"middle\"] + middle_suffix\n", "            prefix = edit[\"prefix\"][:len(edit[\"prefix\"]) - len(middle_prefix)]\n", "            suffix = edit[\"suffix\"][len(middle_suffix):]\n", "\n", "            # We should be only adjusting the boundaries of the update\n", "            assert prefix + middle + suffix == (\n", "                edit[\"prefix\"] + edit[\"middle\"] + edit[\"suffix\"]\n", "            )\n", "\n", "            # yield a dict matching the CodeEditData format\n", "            yield {\n", "                \"repo_url\": edit[\"repo_uuid\"],\n", "                \"file_name\": edit[\"file_path\"],\n", "                \"prefix\": prefix,\n", "                \"updated_code\": middle,\n", "                \"suffix\": suffix,\n", "            }\n", "\n", "    timestamp = datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "    output_file = output_path / f\"all.{timestamp}.jsonl.zst\"\n", "    write_jsonl_zst(output_file, list(postprocess_edits(rows)))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "output_path = Path(OUTPUT_PATH)\n", "data = read_jsonl_zst(output_file)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
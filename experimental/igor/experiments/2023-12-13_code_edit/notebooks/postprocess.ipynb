{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import logging\n", "import numpy as np\n", "import pandas as pd\n", "import random\n", "\n", "from collections import defaultdict\n", "from functools import partial\n", "from pathlib import Path\n", "from pyspark.sql import functions as F\n", "from types import SimpleNamespace\n", "from typing import Any\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "PATH_COLUMN = \"max_stars_repo_path\"\n", "ID_COLUMN = \"hexsha\"\n", "CONTENT_COLUMN = \"content\"\n", "FILE_LANG_COLUMN = \"langpart\"\n", "\n", "DROID_VERSION_REPOS = \"droid/droid-repo-01\"\n", "#DROID_VERSION_REPOS = \"droid/droid-repo-02\"\n", "#DROID_VERSION_REPOS = \"droid/droid-files-03\"\n", "\n", "#DROID_VERSION_SYNTH = \"droid/droid-repo-52\"\n", "#DROID_VERSION_SYNTH = \"droid/droid-repo-57\"\n", "#DROID_VERSION_SYNTH = \"droid/files/files-2024-03-13\"\n", "#DROID_VERSION = \"droid2/droid2-data-08\"\n", "#DROID_VERSION = \"droid/droid-repo-61\"\n", "#DROID_VERSION = \"droid/droid-direct-03\"\n", "#DROID_VERSION = \"droid/droid-files-03\"\n", "DROID_VERSION = \"droid/droid-refusal-03\"\n", "\n", "TEMP_BUCKET_URI = \"s3a://augment-temporary/igor/\"\n", "BUCKET_URI = \"s3a://igor-dev-bucket/\"\n", "\n", "\n", "SYNTH_DATA = [\n", "    #f\"/mnt/efs/augment/user/igor/data/{DROID_VERSION_SYNTH}\"\n", "    #f\"/mnt/efs/augment/user/yuri/data/droid-repo-48-filtering-run-v1/filtered.json\"\n", "    #\"/mnt/efs/augment/user/yuri/data/direct-other_2024-01-20-filtering-run-v1/filtered.json\"\n", "    #\"/mnt/efs/augment/user/yuri/data/direct-2024-03-09-filtering-run-v1/filtered.json\",\n", "    #\"/mnt/efs/augment/user/igor/data/droid/hand-edit/direct-case-01/filtered_edit_2024-03-15_22-21-33.json\",\n", "    #\"/mnt/efs/augment/user/igor/data/droid/hand-edit/direct-other-01/filtered_edit_2024-03-20_00-27-30.json\",\n", "    #\"/mnt/efs/augment/user/igor/data/droid/hand-edit/mix1/edited-04\"\n", "    \"/mnt/efs/augment/user/igor/data/droid/refusal/droid-refusal-01/all-results.json\"\n", "]\n", "STAGE1_URI = f\"{BUCKET_URI}{DROID_VERSION_REPOS}/01_raw_repos/\"\n", "STAGE6_URI = f\"{TEMP_BUCKET_URI}{DROID_VERSION}/06_joined_with_repos/\"\n", "STAGE7_URI = f\"{TEMP_BUCKET_URI}{DROID_VERSION}/07_tokens/\"\n", "OUTPUT_PATH = f\"/mnt/efs/augment/user/igor/data/{DROID_VERSION}\"\n", "\n", "#STAGES_ENABLED = [6, 7, 8]\n", "STAGES_ENABLED = [7, 8]\n", "#STAGES_ENABLED = [8]\n", "\n", "config = SimpleNamespace(\n", "    tokenizer_name = \"DeepSeekCoderInstructTokenizer\",\n", "    token_format = \"i\",  # 32-bit signed integer\n", "    prompt_formatter_name = \"droid-repo\",\n", "    prompt_formatter_config = SimpleNamespace(\n", "        max_prefix_tokens = 1536,\n", "        max_suffix_tokens = 1024,\n", "        max_instruction_tokens = 512,\n", "        max_output_tokens = 4096,\n", "        max_tokens = 16384,\n", "    ),\n", "    dataset_config = SimpleNamespace(\n", "        seq_length = 16384,\n", "        num_validation_samples = 32,\n", "    ),\n", "    retriever = {\n", "        \"scorer\": {\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30,\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query\",\n", "            \"add_path\": True,\n", "            \"add_suffix\": True,\n", "            \"max_tokens\": 1023,\n", "            \"prefix_ratio\": 0.9,\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"ethanol6_document\",\n", "            \"add_path\": True,\n", "            \"max_tokens\": 999,\n", "        }\n", "    },\n", "    num_retrieved_chunks = 128,\n", "    retrieval_dropout = 0.25,\n", "    should_mask_prompts = True,\n", "    should_shuffle = True,\n", ")\n", "\n", "if DROID_VERSION.startswith(\"droid2/\"):\n", "    config.prompt_formatter_name = \"droid2\"\n", "    config.should_mask_prompts = False\n", "if DROID_VERSION == \"droid/droid-repo-55\":\n", "    config.dataset_config.num_validation_samples = 0\n", "if DROID_VERSION == \"droid/droid-repo-57\" or DROID_VERSION == \"droid/droid-repo-58\":\n", "    config.prompt_formatter_config.prefix_select_suffix = True\n", "\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 6: Join with <PERSON><PERSON> File List"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 6 in STAGES_ENABLED:\n", "    spark = k8s_session(\n", "        max_workers=8,\n", "        name=\"igor-dev-join-by-repo\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "        },\n", "    )\n", "\n", "    prompts = []\n", "    for synth_path in SYNTH_DATA:\n", "        synth_path = Path(synth_path)\n", "        if not synth_path.name.endswith(\".json\"):\n", "            synth_path = synth_path / \"prompts.json\"\n", "        with (synth_path).open(\"r\") as f:\n", "            prompts.extend(json.load(f))\n", "\n", "    prompts_by_repo = defaultdict(list)\n", "    for prompt in prompts:\n", "        repo_url = prompt[\"repo_url\"]\n", "        prompts_by_repo[repo_url].append(json.dumps(prompt))\n", "    df = spark.createDataFrame(\n", "        pd.DataFrame(\n", "            [\n", "                {\n", "                    \"repo_url\": repo_url,\n", "                    \"prompts\": prompts,\n", "                }\n", "                for repo_url, prompts in prompts_by_repo.items()\n", "            ]\n", "        )\n", "    )\n", "\n", "    df_repos = spark.read.parquet(STAGE1_URI)\n", "    df_joined = df.join(df_repos, F.col(\"repo_url\") == F.col(\"repo_uuid\"), \"inner\")\n", "    df_joined.repartition(32).write.parquet(STAGE6_URI)\n", "\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 7: Tokenize prompts for training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def tokenize_prompts(batch, prompt_formatter_name, prompt_formatter_config, is_masked_prompt=True):\n", "    from research.core.all_prompt_formatters import get_prompt_formatter\n", "    from research.core.model_input import ModelInput\n", "    from research.data.spark.pipelines.stages.common import pack_tokens\n", "    from research.eval.harness.factories import create_retriever\n", "    from research.retrieval.types import Document\n", "    from research.core.types import CharRange\n", "\n", "    prompt_formatter = get_prompt_formatter(\n", "        prompt_formatter_name, **vars(prompt_formatter_config)\n", "    )\n", "\n", "\n", "    def pack_prompt(tokens) -> bytearray:\n", "        assert len(tokens) <= config.dataset_config.seq_length\n", "        padded_tokens = tokens + [-prompt_formatter.tokenizer.eod_id] * (\n", "            1 + config.dataset_config.seq_length - len(tokens)\n", "        )\n", "        return pack_tokens(padded_tokens, config.token_format)\n", "\n", "    tokenized_prompts = []\n", "\n", "    retrieval_database = create_retriever(config.retriever)\n", "    retrieval_database.scorer.load()\n", "    \n", "    for prompts, file_list in zip(batch[\"prompts\"], batch[\"file_list\"]):\n", "        # Populate retrieval database with files\n", "        for file in file_list:\n", "            # Only add files of desired languages to be retrieved\n", "            if (\n", "                getattr(config, \"retrieval_languages\", None)\n", "                and file[FILE_LANG_COLUMN] not in config.retrieval_languages\n", "            ):\n", "                continue\n", "\n", "            document = Document(\n", "                id=file[ID_COLUMN], text=file[CONTENT_COLUMN], path=file[PATH_COLUMN]\n", "            )\n", "            retrieval_database.add_doc(document)\n", "        \n", "        for prompt_json in prompts:\n", "            # Load the prompt from the JSON string\n", "            prompt = json.loads(prompt_json)\n", "            \n", "            # Filter the file list to only include the file with the same path as the prompt\n", "            modified_file_list = [\n", "                file[CONTENT_COLUMN]\n", "                for file in file_list\n", "                if file[PATH_COLUMN] == prompt[\"path\"]\n", "            ]\n", "\n", "            # Ensure that exactly one file matches the path\n", "            skip_retrieval = False\n", "            if len(modified_file_list) != 1:\n", "                logger.warning(f\"{len(modified_file_list)} files with path {prompt['path']}\")\n", "                skip_retrieval = True\n", "            modified_file = modified_file_list[0]\n", "\n", "            # Calculate the start and end indices of the middle characters in the modified file\n", "            middle_char_start = len(prompt[\"prefix\"])\n", "            middle_char_end = len(modified_file) - len(prompt[\"suffix\"])\n", "\n", "            # Ensure that the prefix and suffix of the prompt match the file\n", "            # TODO(<PERSON>): there are minor spacing discrepancies in prefix/suffix that I haven't fully tracked down\n", "\n", "            if prompt[\"prefix\"] != modified_file[: middle_char_start]:\n", "                logger.warning(f\"Prefix mismatch: '{prompt['prefix']}' != '{modified_file[: middle_char_start]}'\")\n", "            if prompt[\"suffix\"] != modified_file[middle_char_end : middle_char_end + len(prompt[\"suffix\"])]:\n", "                logger.warning(f\"Suffix mismatch: '{prompt['suffix']}' != '{modified_file[middle_char_end:]}', {len(prompt['suffix'])} != {len(modified_file[middle_char_end:])}\")\n", "\n", "            if middle_char_end < middle_char_start:\n", "                logger.warning(f\"Middle char range is inverted: {middle_char_end} < {middle_char_start}\")\n", "                middle_char_start = middle_char_end\n", "\n", "            # If the retrieval dropout is set, do not retrieve any chunks\n", "            if skip_retrieval or random.random() < config.retrieval_dropout:\n", "                retrieved_chunks = []\n", "            else:\n", "                # Query the retrieval database for the most relevant chunks\n", "                retrieved_chunks = retrieval_database.query(\n", "                    model_input=ModelInput(\n", "                        prefix=prompt[\"prefix\"],\n", "                        suffix=prompt[\"suffix\"],\n", "                        path=prompt[\"path\"],\n", "                    ),\n", "                    top_k=config.num_retrieved_chunks,\n", "                )[0]\n", "\n", "            # Prepare the prompt for tokenization\n", "            tokenized_prompt, _ = prompt_formatter.prepare_prompt(\n", "                ModelInput(\n", "                    prefix=prompt[\"prefix\"],\n", "                    suffix=prompt[\"suffix\"],\n", "                    path=prompt[\"path\"],\n", "                    extra={\n", "                        \"instruction\": prompt[\"instruction\"],\n", "                        \"selected_code\": prompt[\"old_middle\"],\n", "                        \"ground_truth_span\": <PERSON><PERSON><PERSON><PERSON><PERSON>(middle_char_start, middle_char_end),\n", "                        \"mask_prompt\": True,\n", "                    },\n", "                    retrieved_chunks=retrieved_chunks,\n", "                )\n", "            )\n", "\n", "            # If the prompt is masked, invert all token IDs\n", "            if is_masked_prompt:\n", "                tokenized_prompt = list(map(lambda x: -1 * x, tokenized_prompt))\n", "\n", "            # Append the ground truth completion to the prompt\n", "            if \"refusal_generation\" not in prompt:\n", "                tokenized_prompt.extend(\n", "                    prompt_formatter.tokenizer.tokenize(prompt[\"new_middle\"])\n", "                    + prompt_formatter.get_prompt_footer()\n", "                )\n", "            else:\n", "                tokenized_prompt.extend(\n", "                    prompt_formatter.tokenizer.tokenize(prompt[\"old_middle\"])\n", "                    + prompt_formatter.get_prompt_footer()\n", "                )\n", "            if len(tokenized_prompt) > config.dataset_config.seq_length:\n", "                logger.warning(\n", "                    f\"token length exceeds seq_len: {len(tokenized_prompt)} > {config.dataset_config.seq_length}\"\n", "                )\n", "                continue\n", "\n", "            # Return the prompt\n", "            tokenized_prompts.append(pack_prompt(tokenized_prompt))\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"prompt_tokens\": tokenized_prompts,\n", "        }\n", "    )\n", "\n", "if 7 in STAGES_ENABLED:\n", "    spark = k8s_session(\n", "        name=\"igor-dev-tokenize\",\n", "        #gpu_type=\"RTX_A5000\",\n", "        gpu_type=\"A100_NVLINK_80GB\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "    )\n", "\n", "    result = map_parquet.apply_pandas(\n", "        spark,\n", "        partial(\n", "            tokenize_prompts,\n", "            prompt_formatter_name=config.prompt_formatter_name,\n", "            prompt_formatter_config=config.prompt_formatter_config,\n", "            is_masked_prompt=config.should_mask_prompts,\n", "        ),\n", "        input_path=STAGE6_URI,\n", "        output_path=STAGE7_URI,\n", "        output_column=\"prompt_tokens\",\n", "        drop_original_columns=True,\n", "        timeout=72000,\n", "        batch_size=4,\n", "    )\n", "\n", "    spark.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 8: Export to a dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 8 in STAGES_ENABLED:\n", "    if False:\n", "        from megatron.tokenizer.tokenizer import get_tokenizer\n", "        from research.data.spark.pipelines.stages.common import export_indexed_dataset\n", "        spark = k8s_session(\n", "            name=\"igor-dev-export_indexed_dataset\",\n", "            conf={\n", "                \"spark.executor.pyspark.memory\": \"1000G\",\n", "            },\n", "        )\n", "\n", "        export_indexed_dataset(\n", "            config=SimpleNamespace(\n", "                input = STAGE7_URI,\n", "                output = OUTPUT_PATH,\n", "                samples_column = \"prompt_tokens\",\n", "                **vars(config.dataset_config),\n", "            ),\n", "            spark=spark,\n", "            tokenizer=get_tokenizer(config.tokenizer_name),\n", "            token_format=config.token_format,\n", "        )\n", "\n", "        spark.stop()\n", "    \n", "    from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "    from research.data.spark.pipelines.stages.common import unpack_tokens\n", "    import torch\n", "    \n", "    spark = k8s_session(\n", "        name=\"igor-dev-export_indexed_dataset\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "        },\n", "    )\n", "    df = spark.read.parquet(STAGE7_URI)\n", "    record_count = df.count()\n", "\n", "\n", "    output_path = Path(OUTPUT_PATH)\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    train_builder = MMapIndexedDatasetBuilder(output_path / \"train.bin\", dtype=np.int32)\n", "    validation_builder = MMapIndexedDatasetBuilder(output_path / \"validation.bin\", dtype=np.int32)\n", "\n", "    rows = df.collect()\n", "    if config.should_shuffle:\n", "        random.shuffle(rows)\n", "\n", "    for id, row in enumerate(rows):\n", "        is_train = (id < record_count - config.dataset_config.num_validation_samples)\n", "        builder = train_builder if is_train else validation_builder\n", "\n", "        tokens = unpack_tokens(row.asDict()[\"prompt_tokens\"], token_format=config.token_format)\n", "        builder.add_item(torch.Tensor(tokens))\n", "        builder.end_document()\n", "    \n", "    train_builder.finalize(Path(OUTPUT_PATH) / \"train.idx\")\n", "    validation_builder.finalize(Path(OUTPUT_PATH) / \"validation.idx\")\n", "\n", "    print(f\"Saved {record_count} sequences to {OUTPUT_PATH}\")\n", "\n", "    spark.stop()\n", "\n", "    # Write info about the postprocessing into the output path\n", "\n", "    def config_to_dict(config: SimpleNamespace) -> dict:\n", "        d = config.__dict__.copy()\n", "        for k, v in config.__dict__.items():\n", "            if isinstance(v, SimpleNamespace):\n", "                d[k] = config_to_dict(v)\n", "        return d\n", "\n", "\n", "    config_dict = config_to_dict(config)\n", "    with (Path(OUTPUT_PATH) / \"postprocess.txt\").open(\"w\") as f:\n", "        f.write(\"STAGES_ENABLED: \" + \", \".join(str(s) for s in STAGES_ENABLED) + \"\\n\")\n", "        f.write(\"SYNTH_DATA: \" + \", \".join(SYNTH_DATA) + \"\\n\")\n", "        f.write(\"STAGE1_URI: \" + STAGE1_URI + \"\\n\")\n", "        f.write(\"STAGE6_URI: \" + STAGE6_URI + \"\\n\")\n", "        f.write(\"STAGE7_URI: \" + STAGE7_URI + \"\\n\")\n", "        f.write(\"OUTPUT_PATH: \" + OUTPUT_PATH + \"\\n\")\n", "        json.dump(config_dict, f, indent=2)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
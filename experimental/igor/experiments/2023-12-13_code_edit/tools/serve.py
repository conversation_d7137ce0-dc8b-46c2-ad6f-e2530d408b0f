"""Launch a FLASK server for the edit demo only!

Run on a 4-nodes H100 machine

Adapted from: experimental/dxy/edits/notebooks/random/launch_ft.py
"""
import argparse
import json
import logging
import pathlib
import threading
import time

from flask import Flask, jsonify, request

from research.core import utils_for_file, utils_for_log, utils_for_str
from research.eval.edit.experimental_edit_systems import REGISTRY as SYSTEMS_REGISTRY
from research.models.meta_model import GenerationOptions

app = Flask(__name__)
GLOBAL_EDIT_LOG_DIR = pathlib.Path(
    "/mnt/efs/augment/user/igor/datasets/edit.raw/dogfood_edit_logs"
)
GLOBAL_EDIT_LOG_DIR.mkdir(exist_ok=True, parents=True)
LOGGER_SAVE_DIR = GLOBAL_EDIT_LOG_DIR / "loggers"
LOGGER_SAVE_DIR.mkdir(exist_ok=True, parents=True)
logger = utils_for_log.create_logger(
    __file__,
    log_file=LOGGER_SAVE_DIR
    / f"{pathlib.Path(__file__).stem}-{utils_for_log.time_string()}.log",
    log_level=logging.INFO,
)
logger.info(f"__file__: {__file__}")
current_directory = pathlib.Path(__file__).parent
logger.info(f"The Current Directory: {current_directory}")
augment_directory = current_directory.parent.parent.parent.parent.parent
logger.info(f"The Augment Directory: {augment_directory}")
research_directory = augment_directory / "research"
logger.info(f"The Augment Research Directory: {research_directory}")
assert research_directory.exists(), f"{research_directory} must exist"
logger.info(f"The Edit Log Directory: {GLOBAL_EDIT_LOG_DIR}")


def shorten(string: str):
    return string.lower().strip()


@app.route("/edit", methods=["POST"])
def edit():
    """The edit communication func.

    Required fields in the chat mode:
    - selected_code: str
    - instruction: str
    """
    global EDIT_SYSTEM  # pylint: disable=global-statement, global-variable-not-assigned
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    # As defined here https://github.com/augmentcode/augment/blob/main/clients/vscode/src/augment-api.ts#L140
    # our code edit feature accepts these inputs:
    #   - instruction: string;
    #   - selected_code: string;
    #   - prefix: string;
    #   - suffix: string;
    #   - top_k?: number;
    #   - top_p?: number;
    #   - temperature?: number;
    #   - lang?: string;
    #   - lines_in_prefix_suffix?: number;
    #
    request_id = data.get("request_id", None)
    selected_code = data.get("selected_code", None)
    instruction = data.get("instruction", None)
    original_prefix = data.get("prefix", None)
    original_suffix = data.get("suffix", None)
    top_k = data.get("top_k", 0)
    top_p = data.get("top_p", 0.0)
    temperature = data.get("temperature", 0)
    lines_in_prefix_suffix = data.get("lines_in_prefix_suffix", 0)
    language = data.get("lang", None)
    debug = data.get("debug", False)
    # Normalize top_k, top_p, temperature
    try:
        top_k = int(float(top_k))
    except (ValueError, TypeError):
        top_k = 0
    try:
        top_p = float(top_p)
    except (ValueError, TypeError):
        top_p = 0.0
    try:
        temperature = float(temperature)
    except (ValueError, TypeError):
        temperature = 0.0

    thread_id = threading.get_ident()

    if selected_code is None or instruction is None:
        logger.info("Did not find selected_code or instruction")
        return jsonify({"response": "", "status": "missing-message-input"})

    try:
        instruction = instruction.strip()
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(
            "Generation hyper-paramters:\n"
            + f"top_k = {top_k}\n"
            + f"top_p = {top_p}\n"
            + f"temperature = {temperature}\n"
            + f"lines_in_prefix_suffix = {lines_in_prefix_suffix}"
        )
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        else:
            prefix, suffix = original_prefix, original_suffix
        start_time = time.time()
        # sample: Dict[str, Any],
        # gen_options: GenerationOptions,
        response = EDIT_SYSTEM(  # pylint: disable=used-before-assignment
            sample={
                "instruction": instruction,
                "code": selected_code,
                "prefix": prefix,
                "suffix": suffix,
            },
            gen_options=GenerationOptions(
                top_k=top_k,
                top_p=top_p,
                temperature=temperature,
            ),
            edit_prompt=None,
            examples=None,
        )
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        time_cost = time.time() - start_time
        debug_json_data = {}
        try:
            debug_json_data = {
                "request_id": request_id,
                "selected_code": selected_code,
                "prefix": original_prefix,
                "suffix": original_suffix,
                "instruction": instruction,
                "response": response,
                "lines_in_prefix_suffix": lines_in_prefix_suffix,
                "language": language,
                "thread_id": thread_id,
                "sender_ip": request.remote_addr,
                "time_cost(seconds)": time_cost,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Time cost for model inference: {time_cost*1000:.1f} ms")
            logger.info(f"Cache file path: {cache_file_path}")
            logger.info(f"Model output:\n{response}")
        except BaseException as e:  # pylint: disable=broad-except
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify(
                {
                    "response": response,
                    "modified_code": "",
                    "status": "success",
                    **debug_json_data,
                }
            )
        else:
            return jsonify(
                {"response": response, "modified_code": "", "status": "success"}
            )
    except BaseException as e:  # pylint: disable=broad-except
        import traceback

        traceback.print_exc()
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/log_status", methods=["POST"])
def log_status():
    """Logging the status.

    - request_id: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")

    request_id = data.get("request_id", None)
    instruction = data.get("instruction", None)
    human_annotated_text = data.get("human_annotated_text", None)
    human_annotated_instruction = data.get("human_annotated_instruction", None)
    status = data.get("status", None)
    debug = data.get("debug", False)

    if request_id is None or instruction is None:
        logger.info("Did not find the request_id or instruction")
        return jsonify({"status": "incorrect inputs"})

    try:
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(f"status: {status}")
        thread_id = threading.get_ident()
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            "FEEDBACK-"
            + utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        debug_json_data = {}
        try:
            debug_json_data = {
                "instruction": instruction,
                "request_id": request_id,
                "thread_id": thread_id,
                "human_annotated_text": human_annotated_text,
                "human_annotated_instruction": human_annotated_instruction,
                "status": status,
                "sender_ip": request.remote_addr,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Cache file path: {cache_file_path}")
        except BaseException as e:  # pylint: disable=broad-except
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify({"status": "success", **debug_json_data})
        else:
            return jsonify({"status": "success"})
    except BaseException as e:  # pylint: disable=broad-except
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


if __name__ == "__main__":
    global EDIT_SYSTEM  # pylint: disable=global-at-module-level

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--port",
        type=str,
        default="5005",
        help="The port to send request to this server.",
    )
    parser.add_argument(
        "--system",
        "-s",
        type=str,
        help="The system factory name.",
    )
    parser.add_argument(
        "--system_args",
        type=str,
        default=None,
        required=True,
        help="Arguments for the system factory.",
    )
    args = parser.parse_args()
    system_args = json.loads(args.system_args)
    EDIT_SYSTEM = SYSTEMS_REGISTRY.get(args.system)(**system_args)
    app.run(host="0.0.0.0", processes=1, threaded=True, debug=False, port=args.port)

"""Tool to compute Jaccard similarity between two text files."""
import argparse
from pathlib import Path


def _get_ngrams(tokens: list[int], n: int):
    """Returns n-grams for the given list of tokens.

    Returns an empty list if the n-gram size exceeds the list of tokens.
    """
    hashes = [hash(tuple(tokens[i : i + n])) for i in range(len(tokens) - n + 1)]
    return [h for h in hashes if h % 100 == 0]


def _jaccard_sim(x, y, ngram_size):
    x = set(_get_ngrams(x, ngram_size))
    y = set(_get_ngrams(y, ngram_size))
    intersection = x.intersection(y)
    return len(intersection) / (len(x) + len(y) - len(intersection))


def jaccard_similarity(file1: str, file2: str, ngram_size: int) -> float:
    """Compute the Jaccard similarity between two text files."""
    with Path(file1).open() as f1, Path(file2).open() as f2:
        chars1 = f1.read()
        chars2 = f2.read()
        tokens1 = [ord(x) for x in chars1]
        tokens2 = [ord(x) for x in chars2]
        return _jaccard_sim(tokens1, tokens2, ngram_size)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("file1")
    parser.add_argument("file2")
    parser.add_argument("--ngram_size", "-n", type=int, default=20)
    args = parser.parse_args()
    print(jaccard_similarity(args.file1, args.file2, args.ngram_size))


if __name__ == "__main__":
    main()

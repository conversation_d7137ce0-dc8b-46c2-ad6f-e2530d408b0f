"""A tool to edit the dataset."""

import argparse
import json
import random
import re
import socket
import subprocess
from pathlib import Path
from typing import Optional, Tuple


class NewLineFixer:
    """Ensures that the new and old middle code have the same line endings."""

    def get_line_ending(self, s: str) -> Optional[str]:
        if s.endswith("\r\n"):
            return "\r\n"
        elif s.endswith("\n"):
            return "\n"
        elif s.endswith("\r"):
            return "\r"
        else:
            return None

    def detect_nl_type(self, s: str) -> Optional[str]:
        lines = s.splitlines(keepends=True)
        endings = {}
        for line in lines:
            ending = self.get_line_ending(line)
            if ending is not None:
                endings[ending] = ending
            else:
                return None

        if len(endings) == 1:
            return list(endings.keys())[0]
        else:
            return None

    def fix_newlines(self, s1: str, s2: str) -> str:
        """Make line endings in s1 match s2."""
        s1_ending = self.detect_nl_type(s1)
        s2_ending = self.detect_nl_type(s2)
        if s1_ending is None or s2_ending is None:
            return s1
        else:
            return s1.replace(s1_ending, s2_ending)


def get_file_names(args) -> Tuple[Path, Path]:
    hostname = socket.gethostname()
    path = Path(args.path)
    return (
        path.parent / f"{path.stem}_{hostname}_orig.txt",
        path.parent / f"{path.stem}_{hostname}_new.txt",
    )


def apply_auto_fixes(old_middle, new_middle):
    """Apply automatic fixes to the old and new middle code."""
    nl_fixer = NewLineFixer()
    old_middle = nl_fixer.fix_newlines(old_middle, new_middle)
    return old_middle, new_middle


def create_files(data, path_old: Path, path_new: Path, instruction_mode: str):
    # write old_middle and new_middle into temporary files
    with path_old.open("w") as f_old, path_new.open("w") as f_new:
        for idx, record in enumerate(data):
            old_middle = record["old_code"]
            new_middle = record["new_code"]
            old_middle, new_middle = apply_auto_fixes(old_middle, new_middle)

            for fh in [f_old, f_new]:
                fh.write(
                    f"\n>><<[ {idx} ]-------------------------------------------------\n"
                )

            f_old.write("\nKeep: Y\n")

            if instruction_mode == "none":
                pass
            elif instruction_mode == "first":
                record["instruction"] = record["instructions"][0]
            elif instruction_mode == "random":
                record["instruction"] = random.choice(record["instructions"])
            else:
                raise ValueError(f"Unknown instruction mode: {instruction_mode}")

            for instruction in record["instructions"]:
                header = "- " if instruction == record["instruction"] else "  "
                f_old.write(header + instruction + "\n")
            for fh in [f_old, f_new]:
                fh.write(
                    "\n----------------------------------------------------------\n"
                )
            f_old.write(old_middle)
            f_new.write(new_middle)


def read_data(args) -> list:
    path = Path(args.path)
    with path.open(encoding="utf-8") as f:
        data = json.load(f)

    if args.all_results:
        read_optional_data(args, data)
    return data


def optional_data_key(record):
    file_path = record["file_name"] if "file_name" in record else record["path"]
    return f"{record['repo_url']},{file_path}"


def read_optional_data(args, data):
    """Try to re-join with all-results.json on a best-effort basis.

    This helps us recover the "backward-edit" generated by the synthetic data pipeline
    which turns out to be helpful when information when editing by hand.
    """
    path = Path(args.all_results)

    with path.open(encoding="utf-8") as f:
        optional_records = json.load(f)

    optional_data = dict()
    for record in optional_records:
        edit = record["code_edit_data"]

        backward_edit = record["backward_edit"]
        key = optional_data_key(edit)
        if key in optional_data:
            optional_data[key].append(backward_edit)
        else:
            optional_data[key] = [backward_edit]

    # add optional data to data
    optional_data_count = 0
    optional_data_count_missing = 0
    optional_data_count_multiple = 0
    for record in data:
        key = optional_data_key(record)
        if key in optional_data and len(optional_data[key]) == 1:
            record["instructions"].append(optional_data[key][0])
            optional_data_count += 1
        elif key in optional_data and len(optional_data[key]) > 1:
            optional_data_count_multiple += 1
        else:
            optional_data_count_missing += 1

    print(
        f"Added {optional_data_count}/{len(data)} records with optional data; {optional_data_count_missing} records missing optional data; {optional_data_count_multiple} records with multiple optional data."
    )


def show_diff(args, data):
    orig_path, new_path = get_file_names(args)

    # check if files exist
    if orig_path.exists() != new_path.exists():
        print(f"Error: {orig_path} and {new_path} must both exist or both not exist.")
        return

    if not orig_path.exists():
        create_files(data, orig_path, new_path, args.instruction)

    # open them in vimdiff in a subprocess
    subprocess.run(
        f"vimdiff '{orig_path}' '{new_path}' -c 'set foldopen=all'",
        shell=True,
        check=True,
    )


def save_changes(args, data):
    class _SimpleParser:
        def __init__(self, lines, record_idx):
            self.lines = lines
            self.line_idx = 0
            self.record_idx = record_idx

        def expect_line(self, line):
            if self.lines[self.line_idx] != line:
                raise ValueError(
                    f"[{self.record_idx}] Expected line {self.line_idx} to be '{line}', but got '{self.lines[self.line_idx]}'"
                )
            self.line_idx += 1

        def match_line(self, rgx):
            match = re.match(rgx, self.lines[self.line_idx])
            if not match:
                raise ValueError(
                    f"[{self.record_idx}] Expected line {self.line_idx} to match '{rgx}', but got '{self.lines[self.line_idx]}'"
                )
            self.line_idx += 1
            return match

        def peek_line(self):
            return self.lines[self.line_idx]

        def get_line(self):
            if self.line_idx == len(self.lines):
                return None
            else:
                line = self.lines[self.line_idx]
                self.line_idx += 1
                return line

    def parse_records(path: Path, num_records: int):
        assert num_records > 0

        with path.open("r") as f:
            lines = f.readlines()

        line_idx = 1  # Skip initial blank line
        next_record_idx = 0

        record_lines = []
        while True:
            if line_idx == len(lines):
                yield record_lines
                assert (
                    next_record_idx == num_records
                ), f"Mismatch at {next_record_idx} <> {num_records}"
                break

            if (
                lines[line_idx]
                == f">><<[ {next_record_idx} ]-------------------------------------------------\n"
            ):
                if next_record_idx > 0:
                    record_lines = record_lines[:-1]
                    yield record_lines

                next_record_idx += 1
                record_lines = []
            else:
                record_lines.append(lines[line_idx])
            line_idx += 1

    def parse_record(record_lines, idx, is_orig) -> dict:
        parser = _SimpleParser(record_lines, idx)
        record = {}
        parser.expect_line("\n")

        if is_orig:
            match = parser.match_line(r"^Keep: ([Y|N])\n$")
            record["keep"] = match.group(1) == "Y"
            while parser.peek_line() != "\n":
                match = parser.match_line(r"^(.) (.*)\n$")
                marker, instruction = match.group(1), match.group(2)
                if marker == "-":
                    assert (
                        "instruction" not in record
                    ), f"[{idx}] Duplicate instruction: {instruction}"
                    record["instruction"] = instruction
            parser.expect_line("\n")

        parser.expect_line(
            "----------------------------------------------------------\n"
        )

        code_lines = []
        while True:
            line = parser.get_line()
            if line is None:
                break
            code_lines.append(line)
        record["code"] = "".join(code_lines)
        return record

    from datetime import datetime

    orig_path, new_path = get_file_names(args)

    # Parse orig_path and new_path files
    records_orig = list(parse_records(orig_path, len(data)))
    records_new = list(parse_records(new_path, len(data)))

    print(
        f"Parsed files into records: {len(data)} {len(records_orig)} {len(records_new)}"
    )
    data_updated = []
    for idx, (record_orig, record_new) in enumerate(zip(records_orig, records_new)):
        record_orig = parse_record(record_orig, idx=idx, is_orig=True)
        record_new = parse_record(record_new, idx=idx, is_orig=False)

        if record_orig["keep"]:
            if "instruction" not in record_orig:
                print(f"XXX MISSING {idx}")
            record = data[idx]
            record["instruction"] = record_orig["instruction"]
            record["old_code"] = record_orig["code"]
            record["new_code"] = record_new["code"]
            data_updated.append(record)

    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    path = Path(args.path)
    updated_prompts_path = path.parent / f"{path.stem}_edit_{timestamp}.json"
    with updated_prompts_path.open("w") as f:
        json.dump(data_updated, f, indent=2)
    print(f"Saved to {updated_prompts_path}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "path",
        type=str,
        help="paths to datasets (.jsonl files)",
    )
    parser.add_argument(
        "--instruction",
        type=str,
        help="The instruction mode",
        choices=["none", "first", "random"],
        default="none",
    )
    parser.add_argument(
        "--all_results",
        type=str,
        help="Optional path to the dataset (all/*.json files)",
    )
    args = parser.parse_args()

    data = read_data(args)
    show_diff(args, data)
    save_changes(args, data)


if __name__ == "__main__":
    main()

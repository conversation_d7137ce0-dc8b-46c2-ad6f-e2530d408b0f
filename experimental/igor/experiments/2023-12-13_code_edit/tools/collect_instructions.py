"""Extract a set of unique instructions from code_edit_pipeline.py output."""

import argparse
import json
from pathlib import Path


def run_pipeline(path: Path):
    file_path = path / "all-results.json"

    with file_path.open("r") as f:
        data = json.load(f)

    output_path = path / "all-instructions.txt"
    with output_path.open("w") as f:
        for result in data:
            f.write(f"{result['idx']} {result['instructions']}\n")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("path", type=str, help="path to the jsonl file")
    args = parser.parse_args()

    run_pipeline(Path(args.path))


if __name__ == "__main__":
    main()

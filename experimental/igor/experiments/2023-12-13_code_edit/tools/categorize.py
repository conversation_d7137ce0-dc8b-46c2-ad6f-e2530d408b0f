"""Categorize the edit dataset by instruction."""

import argparse
import json
from collections import defaultdict
from pathlib import Path

INSTRUCTION_CAT = {
    "INDENT": ["indent"],
    "IMPORT": ["import", "export"],
    "TODO": ["TODO"],
    "TYPE_ANNOT": ["type annot"],
    "TIME": ["second", "minute", "hour", "day", "week", "month", "year"],
    "LOGGING": ["logging", "print", "log level"],
    "TEST": ["mock", "test"],
    "FIELD": ["setter", "getter"],
    "UNUSED": ["unused", "no-op", "redundant"],
    "COMMENT": ["comment", "javadoc", "docstring"],
    "RENAME": ["rename"],
    "DATASTRUCT": ["list", "array", "hash"],
    "FORMAT": [
        "format",
        "bracket",
        "brace",
        "quote",
        "semicolon",
        "parenthes",
        "literal",
    ],
    "ERROR": ["error", "exception", "raise", "assert"],
    "LOOP": ["loop"],
    "SORT": ["sort"],
    "CHECK": ["conditional", "check"],
    "TYPE": ["type"],
    "NULL": ["null"],
    "SIMPLIFY": ["simplify"],
    "ADD": ["missing"],
    "REPLACE": ["replace", "swap"],
    "IMPLEMENT": ["implement"],
    "LOGIC": ["logic"],
}


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=str, help="path to the jsonl file")
    args = parser.parse_args()

    with Path(args.input).open("r") as f:
        data = json.load(f)

    results_by_cat = defaultdict(list)
    for result in data:
        for instruction in result["instructions"]:
            instruction_lower = instruction.lower()
            for cat, keywords in INSTRUCTION_CAT.items():
                for keyword in keywords:
                    if keyword in instruction_lower:
                        result["instruction_cat"] = cat
                        break
                if "instruction_cat" in result:
                    break
            if "instruction_cat" in result:
                break
        if "instruction_cat" not in result:
            result["instruction_cat"] = "LOGIC"
        results_by_cat[result["instruction_cat"]].append(result)

    for cat, results in results_by_cat.items():
        input_path = Path(args.input)
        output_path = input_path.parent / f"{input_path.stem}_{cat}.json"
        print(f"Saving {len(results)} results to {output_path}")
        with output_path.open("w") as f:
            json.dump(results, f, indent=2)


if __name__ == "__main__":
    main()

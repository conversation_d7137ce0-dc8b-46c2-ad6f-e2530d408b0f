"""This script is used to launch a Determined experiment for training a 1.3B model."""

import argparse
import subprocess
import tempfile

import yaml


def main():
    """Main function to launch a Determined experiment for training a 1.3B model."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--learning_rate", type=float)
    parser.add_argument("--max_iters", type=int)
    parser.add_argument("--max_epochs", type=int)
    parser.add_argument("--decay_lr", default=False, action="store_true")
    parser.add_argument("--warmup_iters", type=int)
    parser.add_argument("--weight_decay", type=float)
    parser.add_argument("--batch_size", type=int)
    parser.add_argument("--gradient_accumulation_steps", type=int)
    parser.add_argument("--eval_interval", type=int)
    parser.add_argument("--eval_iters", type=int)
    parser.add_argument("--block_size", type=int)
    parser.add_argument("--checkpoint_dir", type=str)
    parser.add_argument("--hf_checkpoint_dir", type=str)
    parser.add_argument("--train_data_path", type=str)
    parser.add_argument("--eval_data_path", type=str)
    parser.add_argument("--out_dir", type=str)
    parser.add_argument("--wandb_log", default=False, action="store_true")
    parser.add_argument("--wandb_project", type=str)
    parser.add_argument("--wandb_run_name", type=str)
    args = parser.parse_args()
    fastbackward_args = {k: v for k, v in vars(args).items() if v is not None}

    determined_config = {
        "determined": {
            "name": args.wandb_run_name,
            "description": None,
            "workspace": "Dev",
            "project": "igor",
        },
        "augment": {
            "podspec_path": "8xH100.yaml",
            "gpu_count": 8,
        },
        "fastbackward_configs": ["configs/deepseek_coder_instruct_1.3b.py"],
        "fastbackward_args": fastbackward_args,
    }

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        yaml.dump(determined_config, f)
        print(f.name)
        subprocess.check_call(["python", "determined/launch.py", f.name])


if __name__ == "__main__":
    main()

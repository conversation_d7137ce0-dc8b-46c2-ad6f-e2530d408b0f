determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: droid_hydra_function_eval_with_retrieval_8k_newckpt_improve_instruction2 droid-123
  project: igor
  workspace: Dev
podspec: 1xH100.yaml
system:
  generation_options:
    max_generated_tokens: 1024
    temperature: 0
    top_k: 0
    top_p: 0
  input_token_budget: 10000
  override_instruction: implement function
  model:
    checkpoint_path: /mnt/efs/augment/user/igor/nlp/logs/droid-123/checkpoint_llama_iteration_839/ffw
    name: fastforward_droid_repo
    prompt:
      max_instruction_tokens: 512
      max_output_tokens: 1024
      max_prefix_tokens: 1536
      max_suffix_tokens: 1024
      max_tokens: 8192
  name: droid_repo_code_edit
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
    scorer:
      checkpoint_path: ethanol/ethanol6-16.1
      name: ethanol
  override_selected_code: ''
task:
  dataset: repoeval_functions
  exec: true
  hydra_block_resource_internet_access: true
  name: hydra

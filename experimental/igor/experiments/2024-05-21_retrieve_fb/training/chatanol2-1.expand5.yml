# Run using:
#
# Based on: experimental/vzhao/20231129_star_ethanol/modeling/config/20231214_starethanol6_16.1_mean_doc.yml
#
determined:
  description: null
  workspace: Dev
  project: igor

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 32
  project_group: finetuning
  keep_last_n_checkpoints: 1

fastbackward_args:
  run_name: chatanol2-1 expand5
  wandb_project: chatanol

  # dtype: float32

  components:
    model:
      component_name: create_known_chunk_model
      model: query_model
      tokenizer: tokenizer
    query_model:
      component_name: neox.load_starethanol_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    loss_fn:
      component_name: KnownChunkLoss

  eval_interval: 10
  checkpoint_interval: 100

  max_epochs: -1
  train_options:
    # Original configuration had batch size x grad acc. x gpus of 1 x 16 x 32.
    batch_size: 1
    # This run has 8 gpus, so running with 2 x 32 x 8.
    # gradient_accumulation_steps: 32
    # This run has 32 gpus, so running with 2 x 8 x 32.
    gradient_accumulation_steps: 16
    max_iters: 1468
    log_interval: 1
    grad_clip: 1.0

    # from: /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
      decay_iters: 1468
      # These are the default betas, but `launch.py` doesn't handle lists yet.
      # betas: [0.9, 0.95]
      eps: 1.0e-8
      weight_decay: 0.1

  eval_batch_size: 4

  train_data:
    path: /mnt/efs/augment/user/igor/data/chatanol/chatanol2-1.hybrid5/train
    tokenizer_name: starcoder
    documents_per_batch: 128
    max_query_tokens: 8192
    max_document_tokens: 1024
  eval_data:
    path: /mnt/efs/augment/user/igor/data/chatanol/chatanol2-1.hybrid5/valid
    tokenizer_name: starcoder
    limit: 1024
    documents_per_batch: 128
    max_query_tokens: 8192
    max_document_tokens: 1024

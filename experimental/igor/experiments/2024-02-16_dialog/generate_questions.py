"""Generate questions for the dialog experiment."""

import argparse
import random
import time
from pathlib import Path

from tqdm import tqdm

from research.core.llama_prompt_formatters import Message, SeeThreeFormatter
from research.core.model_input import ModelInput
from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.models.meta_model import ExtraGenerationOutputs, GenerationOptions


def main(args):
    # Read topics from topics.txt
    with open(Path(__file__).parent / "topics.txt", "r") as f:
        topics = [line.strip() for line in f.readlines()]

    model = FastForwardDeepSeekCoderInstruct33B()
    model.prompt_formatter = SeeThreeFormatter()
    model.load()

    file_name = "simple_q." + time.strftime("%Y-%m-%d_%H-%M-%S") + ".txt"
    output_file = Path(args.output) / file_name

    print(f"Writing questions to {output_file}")

    skipped = 0

    with open(output_file, "w") as f:
        for _ in tqdm(range(args.n)):
            topic = random.choice(topics)
            model_input = ModelInput(
                extra={
                    "messages": [
                        Message(
                            type="Instruction",
                            arg=topic + " - hard question",
                            text="",
                            eot=False,
                        ),
                    ]
                }
            )

            prompt = model.prompt_formatter.prepare_prompt(model_input)
            print(model.tokenizer.detokenize(prompt[0]))
            print("==========================================================")
            output = model.generate(
                model_input,
                options=GenerationOptions(
                    max_generated_tokens=1024, temperature=0.5, top_k=10
                ),
                extra_outputs=ExtraGenerationOutputs(),
            )

            if "?" in output:
                output = output.split("?")[0] + "?"
            else:
                skipped += 1
                continue

            f.write("<>" * 50 + "\n")
            f.write(f"{output}\n")

    print(f"Wrote {args.n - skipped} questions.")
    print(f"Skipped {skipped} questions.")
    print(f"Wrote questions to {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("output", type=str, help="output path")
    parser.add_argument(
        "-n", type=int, default=100, help="number of questions to generate"
    )
    args = parser.parse_args()

    main(args)

"""Generate contextualized questions for the dialog experiment."""

import argparse
import json
import time
from pathlib import Path

from tqdm import tqdm

from research.core.llama_prompt_formatters import Message, SeeThreeFormatter
from research.core.model_input import ModelInput
from research.core.utils_for_file import read_jsonl_zst
from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.models.meta_model import GenerationOptions


def main(args):
    code_blocks = read_jsonl_zst(args.input)

    model = FastForwardDeepSeekCoderInstruct33B()
    model.prompt_formatter = SeeThreeFormatter()
    model.load()

    file_name = "code_q." + time.strftime("%Y-%m-%d_%H-%M-%S") + ".txt"

    with open(Path(__file__).parent / file_name, "w") as f:
        for code_block in tqdm(code_blocks):
            result = model.generate(
                ModelInput(
                    extra={
                        "messages": [
                            Message(
                                text=code_block["code"],
                                type="Instruction",
                                eot=False,
                            )
                        ]
                    }
                ),
                options=GenerationOptions(max_generated_tokens=4096, temperature=0.0),
            )
            f.write(json.dumps({"question": result}) + "\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="path to the input file")
    parser.add_argument("--output", type=str, help="path to the output file")
    args = parser.parse_args()
    main(args)

"""Convert a text file to a jsonl file."""

import argparse
import json
from pathlib import Path

EXAMPLE_SEPARATOR = "<><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>"


def parse_questions(lines):
    questions = []
    question = []
    for line in lines:
        if line.startswith(EXAMPLE_SEPARATOR):
            if len(question) > 0:
                questions.append("".join(question))
            question = []
        else:
            question.append(line)

    questions.append("".join(question))
    return questions


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=str, help="input path")
    args = parser.parse_args()

    input_file = Path(args.input)
    output_file = input_file.with_suffix(".jsonl")

    with input_file.open("r", encoding="utf-8") as f:
        lines = f.readlines()
        questions = parse_questions(lines)

    with output_file.open("w", encoding="utf-8") as f:
        for question in questions:
            f.write(json.dumps({"question": question}) + "\n")


if __name__ == "__main__":
    main()

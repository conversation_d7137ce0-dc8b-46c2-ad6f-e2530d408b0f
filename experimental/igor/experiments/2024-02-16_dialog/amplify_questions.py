"""Generate additional questions for the dialog experiment using few-shot prompting."""

import argparse
import json
import random
import time
from pathlib import Path

from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.models.meta_model import GenerationOptions

SEPARATOR = "### Instruction:"


def load_questions(path):
    with path.open("r") as f:
        return [json.loads(line)["question"] for line in f]


def main(args):
    questions = load_questions(args.path)
    print(f"Loaded {len(questions)} questions.")

    output_path = args.path.parent / (
        "amplified." + time.strftime("%Y-%m-%d-%H-%M-%S") + ".jsonl"
    )

    model = FastForwardDeepSeekCoderInstruct33B()
    model.load()

    fewshot_examples = 5

    with output_path.open("w") as f:
        for i in range(args.n):
            print("I", i)
            sampled_questions = random.sample(questions, fewshot_examples)

            tokenizer = model.prompt_formatter.tokenizer
            tokens = [
                tokenizer.tokenize(question) + [tokenizer.eos_id]
                for question in sampled_questions
            ] + [tokenizer.tokenize("\n" + SEPARATOR + "\n")]
            all_tokens = sum(tokens, [])

            output = model.raw_generate(
                all_tokens,
                GenerationOptions(max_generated_tokens=4096, temperature=0.5),
            )
            print(output)
            print("=" * 80)
            f.write(json.dumps({"text": output}) + "\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("path", type=Path)
    parser.add_argument("-n", type=int, default=100)
    args = parser.parse_args()
    main(args)

"""Generate a dataset from dialog + another dataset."""

import argparse
import json
from pathlib import Path

import numpy as np
import torch
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder
from tqdm import tqdm

from research.core.llama_prompt_formatters import Message, SeeThree<PERSON>ormatter
from research.core.model_input import ModelInput

INDEXED_DATASET_IMPL = "mmap"


def load_dialogs(path):
    with path.open("r") as f:
        return [json.loads(line) for line in f.readlines()]


def main(args):
    dialogs = load_dialogs(args.path)

    print(f"Loaded {len(dialogs)} dialogs.")

    output_path = args.path.parent / "dialog_dataset"
    output_path.mkdir(exist_ok=True)

    prompt_formatter = SeeThreeFormatter()
    tokenizer = prompt_formatter.create_default_tokenizer()

    builder = MMapIndexedDatasetBuilder(
        str(output_path.with_suffix(".bin")), dtype=np.int32
    )
    for dialog in tqdm(dialogs):
        messages = []
        if isinstance(dialog, dict):
            # Single question-answer pair
            dialog = [dialog]

        for turn in dialog:
            if isinstance(turn, dict):
                instruction, response = turn["instruction"], turn["response"]
            else:
                instruction, response = turn
            messages.append(
                Message(type="Instruction", text=instruction, masking="all")
            )
            messages.append(
                Message(type="Response", text=response, eot=True, masking="header")
            )

        prompt_tokens, _ = prompt_formatter.prepare_prompt(
            ModelInput(
                extra={
                    "messages": messages,
                }
            )
        )

        pad_length = args.seq_length - len(prompt_tokens) + 1
        prompt_tokens = (
            prompt_tokens[: args.seq_length] + [-tokenizer.pad_id] * pad_length
        )

        builder.add_item(prompt_tokens)
        builder.end_document()

    builder.finalize(str(output_path.with_suffix(".idx")))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("path", type=Path)
    parser.add_argument("--seq-length", type=int, default=16384)
    args = parser.parse_args()
    main(args)

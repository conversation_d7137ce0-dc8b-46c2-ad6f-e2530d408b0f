"""Answer questions for the dialog experiment."""

import argparse
import json
import time
from pathlib import Path

from tqdm import tqdm

from research.core.llama_prompt_formatters import Message, SeeThreeFormatter
from research.core.model_input import ModelInput
from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.models.meta_model import GenerationOptions


def load_questions(path):
    with path.open("r") as f:
        results = []
        for line in f.readlines():
            j = json.loads(line)
            question = j["question"] if "question" in j else j["text"]
            results.append(question)
        return results


def main(args):
    model = FastForwardDeepSeekCoderInstruct33B()
    model.load()
    model.prompt_formatter = SeeThreeFormatter()

    questions = load_questions(args.path)
    print(f"Loaded {len(questions)} questions.")

    output_path = args.path.parent / (
        "answered." + time.strftime("%Y-%m-%d-%H-%M-%S") + ".jsonl"
    )

    with output_path.open("w") as f:
        for question in tqdm(questions):
            answer = model.generate(
                ModelInput(
                    extra={
                        "messages": [
                            Message(type="Instruction", text=question),
                            Message(type="Response", text=""),
                        ],
                    }
                ),
                options=GenerationOptions(max_generated_tokens=4096),
            )
            f.write(json.dumps({"instruction": question, "response": answer}) + "\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("path", type=Path, help="path to the input file")
    args = parser.parse_args()

    main(args)

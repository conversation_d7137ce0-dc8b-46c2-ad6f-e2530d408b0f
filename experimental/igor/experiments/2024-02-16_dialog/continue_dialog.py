"""Generate more question/answer turns."""

import argparse
import json
import random
import time
from pathlib import Path

from tqdm import tqdm

from research.core.llama_prompt_formatters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.models.meta_model import GenerationOptions

followup_topics = [
    "performance",
    "memory usage",
    "change requirements",
    "error handling",
    "add new constraints",
    "clarifying question",
    "explain more",
    "complicate the question",
    "ask a different question",
    "ask about cross-platform",
    "start a new topic",
    "change the topic",
    "compare to another programming language",
    "compare to another solution",
]


def load_answered(path):
    with path.open("r") as f:
        results = []
        for line in f.readlines():
            j = json.loads(line)
            results.append((j["instruction"], j["response"]))
        return results


class SynthModel(FastForwardDeepSeekCoderInstruct33B):
    def __init__(self):
        super().__init__()
        self.newline_count = 0
        self.newline_token = self.tokenizer.tokenize("\n")[0]
        self.terminated = False

    def should_stop(self, next_token):
        terminated = False
        if next_token == self.newline_token:
            self.newline_count += 1
            if self.newline_count > 5:
                self.newline_count = 0
                terminated = True
        else:
            self.newline_count = 0

        if next_token == self.tokenizer.eod_id:
            terminated = True

        return terminated

    def raw_generate(
        self,
        prompt_tokens: list[int],
        options: GenerationOptions,
    ) -> str:
        self.newline_count = 0
        self.terminated = False
        return super().raw_generate(prompt_tokens, options)


def main(args):
    model = SynthModel()
    model.prompt_formatter = SeeThreeFormatter()
    model.load()

    qa_pairs = load_answered(args.path)
    print(f"Loaded {len(qa_pairs)} question-answer pairs.")

    output_path = args.path.parent / (
        "dialog." + time.strftime("%Y-%m-%d-%H-%M-%S") + ".jsonl"
    )

    max_generated_tokens = 4096

    with output_path.open("w") as f:
        for qa_pair in tqdm(qa_pairs):
            print(f"### Instruction:\n{qa_pair[0]}\n")
            print(f"### Response:\n{qa_pair[1]}\n")
            dialog = [qa_pair]
            while True:
                tokenizer = model.tokenizer
                tokens = sum(
                    [
                        tokenizer.tokenize(f"### Instruction:\n{instruction}\n")
                        + [tokenizer.eos_id]
                        + tokenizer.tokenize(f"### Response:\n{response}\n")
                        + [tokenizer.eos_id]
                        for instruction, response in dialog
                    ],
                    [tokenizer.bos_id],
                )

                followup_topic = random.choice(followup_topics)

                tokens_phase1 = tokens + tokenizer.tokenize(
                    f"### Instruction - follow-up question ({followup_topic}):\n"
                )
                if len(tokens_phase1) + max_generated_tokens > model.seq_length:
                    break

                generated_question = model.raw_generate(
                    tokens_phase1,
                    options=GenerationOptions(
                        max_generated_tokens=max_generated_tokens, temperature=0.5
                    ),
                )

                tokens_phase2 = tokens + tokenizer.tokenize(
                    f"### Instruction:\n{generated_question}\n### Response:\n"
                )
                if len(tokens_phase2) + max_generated_tokens > model.seq_length:
                    break

                generated_answer = model.raw_generate(
                    tokens_phase2,
                    options=GenerationOptions(
                        max_generated_tokens=max_generated_tokens
                    ),
                )
                generated_answer = generated_answer.split("\n" * 10)[0]

                # if generated_answer.lower() in qa_pair[1].lower():
                #    continue

                print(
                    f"### Instruction - follow-up question ({followup_topic}):\n{generated_question}\n"
                )
                print(f"### Response:\n{generated_answer}\n")

                dialog.append((generated_question, generated_answer))

            f.write(json.dumps(dialog) + "\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("path", type=Path)
    main(parser.parse_args())

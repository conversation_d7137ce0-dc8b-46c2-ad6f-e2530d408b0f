from PIL import Image, ImageDraw

# Create a new image with white background
img = Image.new("RGB", (300, 200), color="white")

# Get a drawing context
draw = ImageDraw.Draw(img)

# Draw a blue rectangle
draw.rectangle([20, 20, 280, 180], outline="blue", width=5)

# Add some text
draw.text((100, 90), "MCP Test Image", fill="black")

# Save the image
img.save("test_image.png")

print("Test image created: test_image.png")

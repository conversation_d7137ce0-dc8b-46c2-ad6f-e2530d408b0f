# MCP Image Server

A simple MCP server with a read-image tool that accepts a file path and returns the image to the caller.

## Overview

This MCP server provides a single tool:

- `read_image`: Reads an image file from a specified path and returns it to the caller.

## Features

- Supports various image formats (PNG, JPEG, GIF, WebP)
- Handles text files by converting them to simple PNG images with the text content
- Provides helpful error messages if files are not found

## Installation

1. Install the required dependencies:
   ```
   pip install "mcp[cli]" Pillow
   ```

2. Install the server in Claude Desktop:
   ```
   mcp install mcp-image-server.py
   ```

## Usage

Once installed in Claude Desktop, you can use the tool by asking <PERSON> to read an image file:

```
Please read the image file at /path/to/image.png
```

<PERSON> will use the `read_image` tool to load the image and display it in the conversation.

## Development

To run the server in development mode with the MCP Inspector:

```
mcp dev mcp-image-server.py
```

This will start a local server and open the MCP Inspector in your browser, allowing you to test the tool interactively.

## Implementation Details

The server uses the FastMCP framework from the Model Context Protocol (MCP) Python SDK. The `read_image` tool:

1. Validates that the specified file exists
2. Determines the image format from the file extension
3. Reads the file data
4. Returns the image data in the appropriate format

For text files, it attempts to create a simple PNG image with the text content using PIL (Pillow).

## License

MIT

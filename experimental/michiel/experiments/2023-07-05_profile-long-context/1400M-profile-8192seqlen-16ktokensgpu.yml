# Profile 1400M model with 8192 sequence length and 16k tokens per GPU on single node.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/1400M-model-def.yml

determined:
  name: 1400M-profile-8192seqlen-16ktokensgpu
  description: null
  workspace: Dev
  project: pretrain
  labels: ["1400M", "profile"]
  max_restarts: 0
  perform_initial_validation: False

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 1400M-profile-8192seqlen-16ktokensgpu
  wandb_group: 1400M-profile-8192seqlen-16ktokensgpu

  # 16k tokens per GPU
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 1
  train_batch_size: 16
  max-position-embeddings: 8192
  seq-length: 8192

  # Small number of steps
  lr_decay_iters: 500
  train_iters: 500
  warmup: 0.0

  # Eval/save frequency
  eval_interval: 500
  save_interval: null
  save: null

# 830M model pretraining ablation with 1M batch size, keeping total tokens fixed.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml

determined:
  name: 830M-17Btokens-2node-2048seqlen-bsz1M
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 16
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 830M-17Btokens-2node-2048seqlen-bsz1M
  wandb_group: 830M-17Btokens-2node-2048seqlen-bsz1M

  # 1024k tokens per batch
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 1
  train_batch_size: 512
  max-position-embeddings: 2048
  seq-length: 2048

  # 16600 steps @ 1024k tokens ~ 17B tokens
  lr_decay_iters: 16600
  train_iters: 16600
  warmup: 0.18 # ~3k iters

  # Eval/save frequency
  eval_interval: 500
  save_interval: 2500

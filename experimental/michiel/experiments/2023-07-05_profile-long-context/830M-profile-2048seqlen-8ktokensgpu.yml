# Profile 830M model with 2048 sequence length and 8k tokens per GPU on single node.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml

determined:
  name: 830M-profile-2048seqlen-8ktokensgpu
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "profile"]
  max_restarts: 0
  perform_initial_validation: False

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 830M-profile-2048seqlen-8ktokensgpu
  wandb_group: 830M-profile-2048seqlen-8ktokensgpu

  # 8k tokens per GPU
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 1
  train_batch_size: 32
  max-position-embeddings: 2048
  seq-length: 2048

  # Small number of steps
  lr_decay_iters: 500
  train_iters: 500
  warmup: 0.0

  # Eval/save frequency
  eval_interval: 500
  save_interval: null
  save: null

{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "from megatron.data import indexed_dataset\n", "import pathlib\n", "\n", "\n", "path = \"/mnt/efs/augment/data/processed/rag/dataset/eth61m_depause_prefretsuf_npref100_olap0_quant20/dataset\"\n", "tokenizer = StarCoderTokenizer()\n", "\n", "ds = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["average number of tokens in a path: 19.739966753740205\n", "ratio of relative path tokens to absolute path tokens: 0.5729684210526316\n"]}], "source": ["path_token = tokenizer.filename_id\n", "body_token = tokenizer.retrieval_body_id\n", "sample = ds[1]\n", "\n", "absolute_path_num_tokens = 0\n", "relative_path_num_tokens = 0\n", "num_paths = 0\n", "\n", "for sample in ds[:500]:\n", "\n", "    sample = sample.tolist()\n", "\n", "\n", "    # Find main path\n", "    path_tokens = sample[1:sample.index(tokenizer.fim_prefix_id)]\n", "    path = tokenizer.detokenize(path_tokens)\n", "\n", "    # Collect all paths\n", "    retrieval_paths_tokens = []\n", "    active_path = False\n", "    for idx, token in enumerate(sample):\n", "        if token == path_token:\n", "            active_path = True\n", "            active_idx = idx\n", "        elif token == body_token:\n", "            active_path = False\n", "            retrieval_paths_tokens.append(sample[active_idx + 1:idx])\n", "\n", "    retrieval_paths = [\n", "        tokenizer.detokenize(retrieval_path_tokens)\n", "        for retrieval_path_tokens in retrieval_paths_tokens\n", "    ]\n", "\n", "    # Get retrieval paths relative to main path\n", "    relative_retrieval_paths = [\n", "        os.path.relpath(retrieval_path, path)\n", "        for retrieval_path in retrieval_paths\n", "    ]\n", "\n", "    relative_retrieval_paths_tokens = [\n", "        tokenizer.tokenize(relative_retrieval_path)\n", "        for relative_retrieval_path in relative_retrieval_paths\n", "    ]\n", "\n", "    num_paths += len(relative_retrieval_paths)\n", "\n", "    for relative_retrieval_path_tokens in relative_retrieval_paths_tokens:\n", "        relative_path_num_tokens += len(relative_retrieval_path_tokens)\n", "\n", "    for retrieval_path_tokens in retrieval_paths_tokens:\n", "        absolute_path_num_tokens += len(retrieval_path_tokens)\n", "\n", "\n", "print(f\"average number of tokens in a path: {absolute_path_num_tokens / num_paths}\")\n", "print(f\"ratio of relative path tokens to absolute path tokens: {relative_path_num_tokens / absolute_path_num_tokens}\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["['../util.py', '../util.py', '../video.py', '../util.py', '../util.py']"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["relative_retrieval_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
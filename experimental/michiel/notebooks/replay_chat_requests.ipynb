{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "\n", "from google.cloud import storage, bigquery\n", "\n", "from base.datasets.tenants import DOGFOOD\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache\n", "from research.eval.harness.systems.remote_chat_system import RemoteChatSystem\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "from research.core.artifacts import collect_artifacts\n", "from research.core.types import Document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tenant = DOGFOOD\n", "gcp_creds, _ = get_gcp_creds(None)\n", "storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "blob_bucket = storage_client.bucket(tenant.blob_bucket_name)\n", "blob_cache_size_bytes = 2**30\n", "blob_cache_num_threads = 32\n", "blob_cache = GCSBlobCache(\n", "    blob_bucket,\n", "    tenant.blob_bucket_prefix,\n", "    max_size_bytes=blob_cache_size_bytes,\n", "    num_threads=blob_cache_num_threads,\n", ")\n", "checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)\n", "checkpoint_cache = GCSCheckpointCache(\n", "    checkpoint_bucket,\n", "    tenant.checkpoint_bucket_prefix,\n", "    blob_cache_size_bytes,\n", "    num_threads=blob_cache_num_threads,\n", ")\n", "bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_row(row: bigquery.Row) -> dict:\n", "    request_json = row[\"request_json\"]\n", "\n", "    prefix = request_json.get(\"prefix\", \"\")\n", "    suffix = request_json.get(\"suffix\", \"\")\n", "    selected_code = request_json.get(\"selected_code\", \"\")\n", "    prefix_begin = request_json.get(\"prefix_begin\", None)\n", "    suffix_end = request_json.get(\"suffix_end\", None)\n", "    blob_name = request_json.get(\"blob_name\", None)\n", "    blobs = request_json.get(\"blobs\", None)\n", "    if blobs is not None:\n", "        checkpoint_id = blobs.pop(\"baseline_checkpoint_id\")\n", "        if checkpoint_id is None:\n", "            checkpoint_blob_names = []\n", "        else:\n", "            checkpoint_blob_names = set(\n", "                checkpoint_cache.get([checkpoint_id])[0].blob_names\n", "            )\n", "        added_blob_names = set(\n", "            [base64.b64decode(blob).hex() for blob in blobs[\"added\"]]\n", "            if \"added\" in blobs\n", "            else []\n", "        )\n", "        deleted_blob_names = set(\n", "            [base64.b64decode(blob).hex() for blob in blobs[\"deleted\"]]\n", "            if \"deleted\" in blobs\n", "            else []\n", "        )\n", "        blob_names = sorted(\n", "            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)\n", "        )\n", "        blobs = blob_cache.get(blob_names)\n", "\n", "        documents = [\n", "            Document(id=blob_name, text=blob.content, path=str(blob.path))\n", "            for blob_name, blob in zip(blob_names, blobs)\n", "            if blob is not None\n", "        ]\n", "\n", "    chat_history = request_json.get(\"chat_history\", [])\n", "\n", "    return_dict = {\n", "        \"selected_code\": selected_code,\n", "        \"message\": request_json[\"message\"],\n", "        \"prefix\": prefix,\n", "        \"suffix\": suffix,\n", "        \"path\": request_json[\"path\"],\n", "        \"blob_name\": blob_name,\n", "        \"prefix_begin\": prefix_begin,\n", "        \"suffix_end\": suffix_end,\n", "        \"blobs\": blobs,\n", "        \"documents\": documents,\n", "        \"blob_names\": blob_names,\n", "        \"chat_history\": chat_history,\n", "    }\n", "\n", "    return return_dict\n", "\n", "\n", "def download_request(request_id: str):\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}' AND event_type = 'chat_host_request'\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)\n", "\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.raw_json[\"request\"],\n", "    }\n", "    return row\n", "\n", "\n", "def get_request_inputs(request_id: str):\n", "    return parse_row(download_request(request_id))\n", "\n", "\n", "chat_sample_data = get_request_inputs(\"eb5fff38-2799-453b-ab88-3fc70ef8fc83\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat_sample_data[\"blobs\"][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remote_config_dogfood = {\n", "    \"client\": {\n", "        \"url\": \"https://staging-shard-0.api.augmentcode.com/\",\n", "    },\n", "    \"model_name\": \"binks-l3-70B-FP8-ug-chatanol1-16-3-chat\",\n", "}\n", "remote_chat_dogfood = RemoteChatSystem.from_yaml_config(\n", "    remote_config_dogfood,\n", ")\n", "remote_chat_dogfood.load()\n", "remote_chat_dogfood.add_docs(chat_sample_data[\"documents\"])\n", "model_input = ResearchChatPromptInput(\n", "    path=chat_sample_data[\"path\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    message=chat_sample_data[\"message\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    retrieved_chunks=[],\n", "    doc_ids=chat_sample_data[\"blob_names\"],\n", ")\n", "with collect_artifacts() as collector_manager:\n", "    chat_response_dogfood  = remote_chat_dogfood.generate(model_input)\n", "    artifacts = collector_manager.get_artifacts()\n", "\n", "request_id = artifacts[0]['request_id']\n", "print(\n", "    f\"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}\"\n", ")\n", "chat_response_dogfood"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remote_config_8k = {\n", "    \"client\": {\n", "        \"url\": \"https://dev-zhuoran.us-central.api.augmentcode.com/\",\n", "    },\n", "    \"model_name\": \"binks-l3-70B-FP8-8k-noug-cl1-16-3-chat\",\n", "}\n", "remote_chat_8k = RemoteChatSystem.from_yaml_config(\n", "    remote_config_8k,\n", ")\n", "remote_chat_8k.load()\n", "remote_chat_8k.add_docs(chat_sample_data[\"documents\"])\n", "model_input = ResearchChatPromptInput(\n", "    path=chat_sample_data[\"path\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    message=chat_sample_data[\"message\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    retrieved_chunks=[],\n", "    doc_ids=chat_sample_data[\"blob_names\"],\n", ")\n", "with collect_artifacts() as collector_manager:\n", "    chat_response_8k  = remote_chat_8k.generate(model_input)\n", "    artifacts = collector_manager.get_artifacts()\n", "\n", "request_id = artifacts[0][\"request_id\"]\n", "print(\n", "    f\"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}\"\n", ")\n", "chat_response_8k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remote_config_16k = {\n", "    \"client\": {\n", "        \"url\": \"https://dev-zhuoran.us-central.api.augmentcode.com/\",\n", "    },\n", "    \"model_name\": \"binks-l3-70B-FP8-16k-noug-cl1-16-3-chat\",\n", "}\n", "remote_chat_16k = RemoteChatSystem.from_yaml_config(\n", "    remote_config_16k,\n", ")\n", "remote_chat_16k.load()\n", "remote_chat_16k.add_docs(chat_sample_data[\"documents\"])\n", "model_input = ResearchChatPromptInput(\n", "    path=chat_sample_data[\"path\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    message=chat_sample_data[\"message\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    retrieved_chunks=[],\n", "    doc_ids=chat_sample_data[\"blob_names\"],\n", ")\n", "with collect_artifacts() as collector_manager:\n", "    chat_response_16k  = remote_chat_16k.generate(model_input)\n", "    artifacts = collector_manager.get_artifacts()\n", "\n", "request_id = artifacts[0][\"request_id\"]\n", "print(\n", "    f\"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}\"\n", ")\n", "chat_response_16k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_dogfood.generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_8k.generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_16k.generated_text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
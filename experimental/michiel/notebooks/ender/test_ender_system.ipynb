{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from augment.research.core.model_input import ModelInput\n", "from research.core.types import CharRange\n", "from augment.research.model_server.launch_model_server import get_docs_from_files\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.systems.static_analysis_systems import EnderSystem\n", "\n", "system_config = \"\"\"\n", "name: ender_sys\n", "model:\n", "  name: ender_fastforward\n", "  model_path: ender/1b_pydiffb1m_inline_drop_2kcon_1ksig_rdense_at250\n", "  prompt:\n", "    max_prefix_tokens: 1280\n", "    max_suffix_tokens: 768\n", "    max_signature_tokens: 1024\n", "    max_prompt_tokens: 4096\n", "    max_retrieved_chunk_tokens: -1\n", "generation_options:\n", "  temperature: 0\n", "  top_k: 0\n", "  top_p: 0\n", "  max_generated_tokens: 50\n", "dense_retriever:\n", "  name: diff_boykin\n", "  chunker:\n", "    name: line_level\n", "    max_lines_per_chunk: 30\n", "  max_query_lines: 10  \n", "signature_index:\n", "    use_middle_signatures: true\n", "    est_prefix_chars: 3840\n", "    est_suffix_chars: 2304\n", "    max_ctx_signature_chars: 3000\n", "sig_prompt_formatter:\n", "    max_middle_tks: 200\n", "fim_mode: interactive\n", "verbose: True\n", "\"\"\"\n", "\n", "import yaml\n", "\n", "config = yaml.safe_load(system_config)\n", "\n", "system = EnderSystem.from_yaml_config(config)\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["docs = get_docs_from_files(Path(\"/home/<USER>/augment/research\"), [\".py\"])\n", "system.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix=\"def load(\",\n", "    suffix=\"\",\n", "    path=\"test.py\",\n", "    extra={\"ground_truth_span\": <PERSON><PERSON><PERSON><PERSON><PERSON>(9, 9)}\n", ")\n", "\n", "result = system.generate(model_input)\n", "prediction = result.completion_text\n", "prompt = result.prompt_tokens"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Converts retrieval and signature augmented samples into indexed dataset for training.\"\"\"\n", "\n", "import json\n", "from datetime import datetime\n", "from functools import partial\n", "from pathlib import Path\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.data.rag.ender import EnderDataPromptFormattingConfig, generate_prompt\n", "from research.data.rag.utils import pad_pack_tokens, repartition_and_shuffle\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "spark = k8s_session()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = EnderDataPromptFormattingConfig(\n", "    input=\"s3a://michiel-dev-bucket/ragdata/ender_multieth61m_fullsig_noinline/\",\n", "    output=\"/mnt/efs/augment/data/processed/rag/dataset/ender_multieth61m_fullsig_noinline_sigretprefsuf/\",\n", "    # input=\"s3a://michiel-dev-bucket/ragdata/ender_test/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/ender/test/\",\n", "    component_order=[\"signature\", \"retrieval\", \"prefix\", \"suffix\"],\n", "    random_seed=74912,\n", "    seq_len=5377,\n", "    max_prefix_tokens=1280,\n", "    max_suffix_tokens=768,\n", "    max_retrieved_chunk_tokens=3072,\n", "    max_prompt_tokens=5120,\n", "    max_signature_tokens=1024,\n", "    max_target_tokens=256,\n", "    num_validation_samples=50000,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now()\n", "formatted_time = now.strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "step1_uri = f\"s3a://augment-temporary/michiel/{formatted_time}/step1/\"\n", "step2_uri = f\"s3a://augment-temporary/michiel/{formatted_time}/step2/\"\n", "input_columns = [\n", "    \"prefix\",\n", "    \"middle\",\n", "    \"suffix\",\n", "    \"suffix_offset\",\n", "    \"middle_char_start\",\n", "    \"middle_char_end\",\n", "    \"file_path\",\n", "    \"retrieved_chunks\",\n", "    \"signature_chunks\",\n", "]\n", "\n", "tokenizer = StarCoderTokenizer()\n", "result = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        generate_prompt,\n", "        tokenizer=tokenizer,\n", "        config=config,\n", "    ),\n", "    input_path=config.input,\n", "    output_path=step1_uri,\n", "    input_columns=input_columns,\n", "    output_column=PROMPT_COLUMN,\n", "    ignore_error=True,\n", "    drop_original_columns=True,\n", ")\n", "print(result[\"status_count\"])\n", "print(result[\"task_info\"][\"stderr\"][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result2 = map_parquet.apply(\n", "    spark,\n", "    partial(pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer),\n", "    input_path=step1_uri,\n", "    output_path=step2_uri,\n", "    input_columns=[PROMPT_COLUMN],\n", "    output_column=PROMPT_COLUMN,\n", "    drop_original_columns=True,\n", ")\n", "\n", "print(result2[\"task_info\"].stderr[0])\n", "print(result2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(step2_uri).select(PROMPT_COLUMN)\n", "spark.sparkContext.setJobDescription(\"Shuffling dataset\")\n", "df = repartition_and_shuffle(config.random_seed, df)\n", "\n", "spark.sparkContext.setJobDescription(\"Creating indexed dataset\")\n", "export_indexed_dataset_helper(\n", "    df,\n", "    tokenizer=tokenizer,\n", "    samples_column=PROMPT_COLUMN,\n", "    num_validation_samples=config.num_validation_samples,\n", "    indexed_dataset_path=Path(config.output),\n", "    filter_by_langs=None,\n", ")\n", "with open(config.output + \"config.json\", \"w\") as f:\n", "    json.dump(config.as_dict(), f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
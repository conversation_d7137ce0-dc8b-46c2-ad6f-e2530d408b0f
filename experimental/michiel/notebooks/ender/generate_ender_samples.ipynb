{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains stages for generating retrieval-augmented dataset for fine-tuning.\"\"\"\n", "\n", "from datetime import datetime\n", "from functools import partial\n", "\n", "from pyspark.sql import functions as F\n", "\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.data.rag.ender import EnderDataSampleConfig, process_partition_pandas\n", "from research.data.rag.utils import filter_by_repo_size, inspect_samples, save_config_s3\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REPO_COLUMN = \"max_stars_repo_name\"\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"dart\",\n", "    \"jsx\",    \n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"sql\",\n", "    \"lua\",    \n", "    \"c\",\n", "    \"c++\",    \n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"html\",\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = EnderDataSampleConfig(\n", "    input=\"s3a://the-stack-processed/by-repo\",\n", "    output=\"s3a://michiel-dev-bucket/ragdata/ender_test/\",\n", "    # output=\"s3a://michiel-dev-bucket/ragdata/ender_pydiffb1m_drop_noinline_fix/\",\n", "    # output=\"s3a://michiel-dev-bucket/spark-test/intermediate\",\n", "    repo_languages=[\"python\", \"go\", \"java\", \"javascript\", \"typescript\"]\n", "    sample_languages=[\"python\", \"go\", \"java\", \"javascript\", \"typescript\"],\n", "    retrieval_languages=None,  # no filtering\n", "    # languages=[\"python\"],\n", "    # limit_repos=25000,\n", "    limit_repos=300,\n", "    # FIM problem sampling\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    random_seed=74912,\n", "    # Data filtering and downsampling\n", "    # repo_min_size=1000000,\n", "    repo_min_size=250000,\n", "    repo_max_size=50000000,\n", "    repo_min_files=50,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    import_dropout_prob=0.5,\n", "    max_import_dropout=0.5,\n", "    ### Dense retrieval\n", "    scorer_config={\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "    },\n", "    chunker_config={\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    query_config={\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "    document_config={\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "    num_retrieved_chunks=40,\n", "    ### Signature retrieval\n", "    use_inline_signature=False,\n", "    max_signature_chars=3000,\n", "    estimated_prefix_chars=1280 * 3,\n", "    estimated_suffix_chars=768 * 3,\n", "    show_full_method_signatures=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set intermediate storage location\n", "now = datetime.now()\n", "formatted_time = now.strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "STAGE1_URI = f\"s3a://augment-temporary/michiel/{formatted_time}/step1/\"\n", "STAGE2_URI = f\"s3a://augment-temporary/michiel/{formatted_time}/step2/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if config.repo_languages:\n", "    config.repo_languages = [lang.lower() for lang in config.repo_languages]\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "\n", "if config.retrieval_languages:\n", "    config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=getattr(config, \"repo_min_size\", None),\n", "    max_size=getattr(config, \"repo_max_size\", None),\n", ")\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "limit_repos = config.limit_repos\n", "df = df.limit(limit_repos)\n", "\n", "num_partitions = max(limit_repos // 5, 20)\n", "\n", "df = df.repartition(num_partitions)\n", "df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "spark = k8s_session(\n", "    max_workers=5,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    input_path=STAGE1_URI,\n", "    output_path=STAGE2_URI,\n", "    batch_size=20,  # small batch size so that we can get estimates quickly\n", "    timing_run=True,\n", "    profile=True,  # do some profiling to see where the computes are spent\n", "    output_column=PROMPT_COLUMN,\n", "    drop_original_columns=True,\n", "    ignore_error=True,\n", ")\n", "spark.stop()\n", "\n", "# A rough view of the results give you the success rates, wall time and memory use etc\n", "stderr = result[\"task_info\"][\"stderr\"]\n", "print(\"stderr\", stderr)\n", "\n", "stdout = result[\"task_info\"][\"stdout\"]\n", "print(\"stdout\", stdout)\n", "\n", "print(\"result\", result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "spark = k8s_session(\n", "    max_workers=64,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    input_path=STAGE1_URI,\n", "    output_path=config.output,\n", "    timeout=3600,  # one hour timeout\n", "    batch_size=100,\n", "    drop_original_columns=True,\n", "    ignore_error=True,\n", ")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = config.output\n", "path = path.split(\"s3a://\")[1]\n", "bucket_name, file_name = path.split(\"/\", 1)\n", "file_name += \"config.json\"\n", "save_config_s3(config=config.as_dict(), bucket_name=bucket_name, file_name=file_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"result\", result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect output samples\n", "df = inspect_samples(config.output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Report statistics on generated samples by language\n", "spark = k8s_session(max_workers=100)\n", "\n", "files = map_parquet.list_files(\n", "    spark,\n", "    config.output,\n", "    suffix=\"parquet\",\n", "    include_path=False,\n", ")\n", "read_paths = [config.output + filepath for filepath in files]\n", "df = spark.read.parquet(*read_paths)\n", "\n", "# Guess languages from file_path\n", "df = df.withColumn(\"sample_language\", F.udf(guess_lang_from_fp)(<PERSON>.col(\"file_path\")))\n", "\n", "# Count for each language\n", "df.groupBy(\"sample_language\").count().show(100)\n", "\n", "spark.stop()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
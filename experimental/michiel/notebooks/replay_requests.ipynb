{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "from datetime import datetime, timedelta\n", "\n", "from base.datasets import tenants, replay_utils\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from base.augment_client.client import AugmentClient\n", "\n", "from termcolor import colored\n", "\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# == Configure your variables here ==\n", "\n", "# This is the model name you want to connect to.\n", "# API_PROXY_ENDPOINT: str = \"https://dogfood.api.augmentcode.com\"\n", "API_PROXY_ENDPOINT: str = \"https://dev-michiel.us-central.api.augmentcode.com\"\n", "# The model you want to connect to. Leave empty string if you want the default model.\n", "# MODEL_NAME = \"ender-noin4m-16B-eth6-c\"\n", "MODEL_NAME = \"roguesl-v3-16b-seth6-16-1-p512\"\n", "# MODEL_NAME = \"roguesl-v2-16b-seth616-rec\"\n", "# Which tenant to grab data from.\n", "TENANT_NAME = \"dogfood\"\n", "\n", "# The filter criteria for the dataset you want to replay. This is configured to replay\n", "# specific set of request ids, but there are plenty of options you can play with.\n", "DATASET_FILTERS = CompletionDataset.Filters(\n", "    request_ids=[\n", "        \"cb60dc1d-443a-49e3-884d-b149ac153765\",\n", "        # \"341aefb7-45b7-4b72-b000-b9f166d3f95f\",\n", "        # \"65e274bc-0be3-4ce0-85c4-fdc2dca976c8\",\n", "        # \"f13994ff-a7a8-429e-a68b-20f7d7d72c38\",\n", "        # \"4813df91-f7b9-4e69-9a07-0547b84ccb43\",\n", "        # \"669c8097-7324-4af9-afde-8098b6de1fdc\",\n", "        # \"57d60d7d-c2ce-4755-8a7b-4a3d01214859\",\n", "        # \"090ca5db-f208-4dc6-9181-006c888d1fcd\",\n", "        # \"9c309f68-14ac-478e-aded-19ed837ab10b\",\n", "        # \"23b18102-1483-424e-b30b-438faf23949b\",\n", "        # \"d868dc96-3a26-48f6-9975-9850819fe4b7\",\n", "        # \"7eab1f3b-bb10-40bb-a5e4-0e225d1b22bf\",\n", "        # \"d122cde8-36d2-4260-b066-7f42c7b764a9\",\n", "        # \"3456d0ed-1abc-42a0-837d-4cf4be2db30b\",\n", "        # \"655876f1-807e-4519-ac20-f0535ab22327\",\n", "        # \"6f094398-af9a-4656-acf0-edd86e53378f\",\n", "        # \"ad41ae6c-5315-402b-bd77-63d1617434c8\",\n", "        # \"6dedfdf8-eba0-423f-a227-baac53b56698\",\n", "        # \"9a81daa9-ca31-43ae-bb27-c6739c209247\",\n", "        # \"9224d0b2-5531-4319-966e-fb3ed3cc9abc\",\n", "        # \"2c9f54cb-fd41-4adc-8fc5-59074916c06d\",\n", "        # \"fd616f5e-760b-41f4-8bb6-665b6c690b77\",\n", "        # \"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\",\n", "        # \"58512ff9-45ca-4f45-8446-be352e2c3bdb\",\n", "        # \"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\",\n", "        # \"2ec9688d-d355-4389-b658-a58c3f008f24\",\n", "    ]\n", "    # timestamp_begin=datetime.fromisoformat('2024-04-08T22:47:12.923351') - <PERSON><PERSON><PERSON>(days=1),\n", "    # timestamp_end=datetime.fromisoformat('2024-04-08T22:47:12.923351'),\n", ")\n", "# An alternate choice of dataset filter to find a random set of accepted requests.\n", "# DATASET_FILTERS = CompletionDataset.Filters(\n", "#     accepted_only=True,\n", "# )\n", "\n", "# Ordering by request id will effectively give you data in a random order.\n", "DATASET_ORDER_BY: CompletionDataset.OrderBy = \"request_id\"\n", "\n", "# The maximum number of requests to replay. Here to make sure you don't accidentally\n", "# replay thousands of requests.\n", "DATASET_LIMIT = 100\n", "\n", "# Actually get the dataset.\n", "# Your api token should either be in $AUGMENT_TOKEN or in ~/.config/augment/api_token\n", "client = replay_utils.get_augment_client(url=API_PROXY_ENDPOINT)\n", "candidate_models = [model.name for model in client.get_models().models]\n", "assert (\n", "    MODEL_NAME in candidate_models\n", "), f\"Couldn't find model {MODEL_NAME}. Options: {candidate_models}\"\n", "model_client = client.client_for_model(MODEL_NAME)\n", "\n", "dataset = CompletionDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=DATASET_FILTERS,\n", "    order_by=DATASET_ORDER_BY,\n", "    limit=DATASET_LIMIT,\n", ")\n", "completions = list(dataset.get_completions())\n", "logging.info(\"Retrieved %d completions.\", len(completions))\n", "replay_utils.ensure_blobs_exist(client, dataset._blob_cache, completions, MODEL_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replay the requests from the request insight dataset against the model client.\n", "replayed_responses = replay_utils.replay(client, MODEL_NAME, completions)\n", "# Print out the differences for each request.\n", "for replay in replayed_responses:\n", "    print(f\"== Original request id: {replay.request_id} ({replay.response.model}) ==\")\n", "    print(colored(replay.response.text, \"green\"))\n", "    print(\n", "        f\"== Replayed request id: {replay.replayed_request_id} ({replay.replayed_response.model}) ==\"\n", "    )\n", "    print(colored(replay.replayed_response.text, \"blue\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replaying Edit requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Actually get the dataset.\n", "from base.datasets.edit_dataset import EditDataset\n", "\n", "edit_dataset = EditDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=EditDataset.Filters(\n", "        timestamp_begin=datetime.fromisoformat(\"2024-04-08T22:47:12.923351\")\n", "        - <PERSON><PERSON><PERSON>(days=1),\n", "        timestamp_end=datetime.fromisoformat(\"2024-04-08T22:47:12.923351\"),\n", "    ),\n", "    limit=DATASET_LIMIT,\n", ")\n", "edits = list(edit_dataset.get_entries())\n", "logging.info(\"Retrieved %d edits.\", len(edits))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "# Print out the differences for each request.\n", "for edit in edits:\n", "    print(f\"== Original request id: {edit.request_id} ({edit.response.model}) ==\")\n", "    print(\"++ Instruction: \" + colored(edit.request.instruction, \"green\"))\n", "    print(\"++ Selected Text: \")\n", "    print(edit.request.selected_text)\n", "    print(\"-- Suggested Edit: \")\n", "    print(colored(edit.response.text, \"blue\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
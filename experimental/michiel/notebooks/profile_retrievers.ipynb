{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/pandas/core/computation/expressions.py:20: UserWarning: Pandas requires version '2.7.3' or newer of 'numexpr' (version '2.7.2' currently installed).\n", "  from pandas.core.computation.check import NUMEXPR_INSTALLED\n"]}, {"name": "stdout", "output_type": "stream", "text": ["eval harness is not available (module 'tensorflow.python.training.experimental.mixed_precision' has no attribute '_register_wrapper_optimizer_cls').\n"]}], "source": ["# Load repos from parquet\n", "\n", "from time import time\n", "\n", "import pandas as pd\n", "\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import ScopeAwareChunker\n", "from research.retrieval.scorers.dense_scorer import DenseRetrievalScorer\n", "from research.retrieval.scorers.good_enough_bm25_scorer import <PERSON><PERSON><PERSON><PERSON><PERSON>25Scorer\n", "from research.retrieval.types import Document\n", "from research.core.model_input import ModelInput\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["file_path = \"/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=python/data_0000.parquet\"\n", "df = pd.read_parquet(file_path, engine='pyarrow')\n", "data = df.head(1000).to_dict('records')\n", "text_data = [sample['content'] for sample in data]\n", "test_query = text_data[0][:1000]\n", "document_data = [Document(id=sample['hexsha'], text=sample['content'], path=sample['max_stars_repo_path']) for sample in data]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["file_filterer = None\n", "chunker = ScopeAwareChunker(max_lines_per_chunk=50)\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "dense_scorer = DenseRetrievalScorer(yaml_files, overwrite_values)\n", "bm25_scorer = GoodE<PERSON>ughBM25Scorer()\n", "dense_retrieval_database = RetrievalDatabase(chunker, dense_scorer, file_filterer)\n", "bm25_retrieval_database = RetrievalDatabase(chunker, bm25_scorer, file_filterer)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtering doc with path bazel/lightstep_build_system.bzl because we are unable to parse it.\n", "Average time to add 1000 documents to dense retriever is 69.81656551361084\n", "Average time to query dense retriever 3000 times with 1000 documents 402.26358914375305\n", "Average time to remove 1000 documents from dense retriever is 0.09952044486999512\n"]}], "source": ["n_data = 1000\n", "n_repeats = 1\n", "add_time = 0\n", "query_time = 0\n", "remove_time = 0\n", "\n", "for _ in range(n_repeats):\n", "    start = time()\n", "    for idx in range(n_data):\n", "        document = document_data[idx]\n", "        dense_retrieval_database.add_doc(document)\n", "        \n", "    \n", "            \n", "    end = time()\n", "    add_time += (end - start)\n", "    \n", "    start = time()\n", "    for data_idx in range(3 * n_data):    \n", "        dense_retrieval_database.query(ModelInput(prefix=test_query))\n", "    end = time()\n", "    query_time += (end - start)\n", "    \n", "    start = time()\n", "    dense_retrieval_database.remove_all_docs()\n", "    end = time()\n", "    remove_time += (end - start)\n", "    \n", "\n", "print(f\"Average time to add {n_data} documents to dense retriever is {add_time / n_repeats}\")\n", "print(f\"Average time to query dense retriever {n_data * 3} times with {n_data} documents {query_time / n_repeats}\")\n", "print(f\"Average time to remove {n_data} documents from dense retriever is {remove_time / n_repeats}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtering doc with path bazel/lightstep_build_system.bzl because we are unable to parse it.\n", "Average time to add 1000 documents to bm25 retriever is 15.7190682888031\n", "Average time to query bm25 retriever 3000 times with 1000 documents 506.90341305732727\n", "Average time to remove 1000 documents from bm25 retriever is 24.820358276367188\n"]}], "source": ["\n", "\n", "n_data = 1000\n", "n_repeats = 1\n", "add_time = 0\n", "query_time = 0\n", "remove_time = 0\n", "\n", "for _ in range(n_repeats):\n", "    start = time()\n", "    for idx in range(n_data):\n", "        document = document_data[idx]\n", "        bm25_retrieval_database.add_doc(document)\n", "        \n", "    \n", "            \n", "    end = time()\n", "    add_time += (end - start)\n", "    \n", "    start = time()\n", "    for data_idx in range(3 * n_data):\n", "        bm25_retrieval_database.query(ModelInput(prefix=test_query))\n", "    end = time()\n", "    query_time += (end - start)\n", "    \n", "    start = time()\n", "    bm25_retrieval_database.remove_all_docs()\n", "    end = time()\n", "    remove_time += (end - start)\n", "    \n", "\n", "print(f\"Average time to add {n_data} documents to bm25 retriever is {add_time / n_repeats}\")\n", "print(f\"Average time to query bm25 retriever {n_data * 3} times with {n_data} documents {query_time / n_repeats}\")\n", "print(f\"Average time to remove {n_data} documents from bm25 retriever is {remove_time / n_repeats}\")\n", "    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
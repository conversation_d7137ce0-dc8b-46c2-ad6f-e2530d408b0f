{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    StructuredChatPromptOutput,\n", ")\n", "from base.prompt_format_chat.tokenized_qwen_prompt_formatter import (\n", "    StructToTokensQwenPromptFormatter,\n", ")\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from research.models.fastforward_llama_models import FastForwardQwen25Coder_14B\n", "from research.llm_apis.first_party_clients import QwenLocalClient\n", "from research.llm_apis.first_party_clients import get_client\n", "from base.third_party_clients.third_party_model_client import ToolDefinition"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initialize multiprocessing...\n", "Probing for alive signal...\n", "Listening for alive signal response...\n", "process_idx=3: Calling _init_process.\n", "process_idx=1: Calling _init_process.\n", "process_idx=4: Calling _init_process.\n", "process_idx=2: Calling _init_process.\n", "process_idx=7: Calling _init_process.\n", "process_idx=5: Calling _init_process.\n", "process_idx=6: Calling _init_process.\n", "process_idx=0: Calling _init_process.\n", "Process 0 initialized.\n", "process_idx=0: Creating thread to listen to the kill signal.\n", "process_idx=0: Calling given init_fn.\n", "process_idx=0: Calling an init_fn.\n", "Process 3 initialized.\n", "process_idx=3: Creating thread to listen to the kill signal.\n", "process_idx=3: Calling given init_fn.\n", "process_idx=3: Calling an init_fn.\n", "Process 6 initialized.\n", "process_idx=6: Creating thread to listen to the kill signal.\n", "process_idx=6: Calling given init_fn.\n", "process_idx=6: Calling an init_fn.\n", "Process 7 initialized.\n", "process_idx=7: Creating thread to listen to the kill signal.\n", "process_idx=7: Calling given init_fn.\n", "process_idx=7: Calling an init_fn.\n", "Process 1 initialized.\n", "process_idx=1: Creating thread to listen to the kill signal.\n", "process_idx=1: Calling given init_fn.\n", "process_idx=1: Calling an init_fn.\n", "Process 2 initialized.\n", "process_idx=2: Creating thread to listen to the kill signal.\n", "process_idx=2: Calling given init_fn.\n", "process_idx=2: Calling an init_fn.\n", "Process 5 initialized.\n", "process_idx=5: Creating thread to listen to the kill signal.\n", "process_idx=5: Calling given init_fn.\n", "process_idx=5: Calling an init_fn.\n", "Process 4 initialized.\n", "process_idx=4: Creating thread to listen to the kill signal.\n", "process_idx=4: Calling given init_fn.\n", "process_idx=4: Calling an init_fn.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.4.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.9.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.14.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.19.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.24.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.29.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.34.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.39.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.44.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n"]}, {"name": "stdout", "output_type": "stream", "text": ["process_idx=6: Called an init_fn.\n", "process_idx=6: Starting listening loop.\n", "process_idx=6: Received alive probe.\n", "process_idx=1: Called an init_fn.\n", "process_idx=1: Starting listening loop.\n", "process_idx=1: Received alive probe.\n", "process_idx=0: Called an init_fn.\n", "process_idx=0: Starting listening loop.\n", "process_idx=0: Received alive probe.\n", "process_idx=2: Called an init_fn.\n", "process_idx=2: Starting listening loop.\n", "process_idx=2: Received alive probe.\n", "process_idx=3: Called an init_fn.\n", "process_idx=3: Starting listening loop.\n", "process_idx=3: Received alive probe.\n", "process_idx=4: Called an init_fn.\n", "process_idx=4: Starting listening loop.\n", "process_idx=4: Received alive probe.\n", "process_idx=7: Called an init_fn.\n", "process_idx=7: Starting listening loop.\n", "process_idx=7: Received alive probe.\n", "process_idx=5: Called an init_fn.\n", "process_idx=5: Starting listening loop.\n", "process_idx=5: Received alive probe.\n", "Parallel initialization complete.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n", "WARNING:root:Requested to unshard weight layers.47.ffn.shrink.weight but it is not sharded. Pre-shard the weight to 8 to avoid unnecessary loading from checkpoint storage\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id b8f1f429-4e35-4aca-81fa-435628ac45f6.\n"]}], "source": ["# Load client\n", "\n", "\n", "model_config = {\n", "    \"name\": \"fastforward_qwen25coder_14b\",\n", "    \"checkpoint_path\": Path(\n", "        \"/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-ffw\"\n", "    ),\n", "    \"checkpoint_sha256\": \"4ebe13be3a9d7b2b8a70d91c997ae5fc82926ff1b06caf559ce58702a0b4cf1c\",\n", "    \"sequence_length\": 16384,\n", "}\n", "\n", "local_model_client = client = get_client(\n", "    client_name=\"qwen\",\n", "    model_config=model_config,\n", "    tokens_prompt_formatter_name=\"qwen25coder\",\n", "    stop_tokens=[151645],\n", "    tool_start_token=151657,\n", "    tool_end_token=151658,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[autoreload of base.prompt_format_chat.prompt_formatter failed: Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 276, in check\n", "    superreload(m, reload, self.old_objects)\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 500, in superreload\n", "    update_generic(old_obj, new_obj)\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 397, in update_generic\n", "    update(a, b)\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 349, in update_class\n", "    if update_generic(old_obj, new_obj):\n", "       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 397, in update_generic\n", "    update(a, b)\n", "  File \"/opt/conda/lib/python3.11/site-packages/IPython/extensions/autoreload.py\", line 309, in update_function\n", "    setattr(old, name, getattr(new, name))\n", "ValueError: __init__() requires a code object with 1 free vars, not 3\n", "]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["self._process_idx=2: cuda graph attn cache miss\n", "self._process_idx=2: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 138075682373632, 'v_cache': 138072461148160})) on device 2\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=2: Warming up graph for batch size 2048.\n", "self._process_idx=2: Capturing graph for batch size 2048.\n", "self._process_idx=4: cuda graph attn cache miss\n", "self._process_idx=4: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 138750529110016, 'v_cache': 138747307884544})) on device 4\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=4: Warming up graph for batch size 2048.\n", "self._process_idx=4: Capturing graph for batch size 2048.\n", "self._process_idx=2: Cuda graph internal warmup round 0\n", "self._process_idx=7: cuda graph attn cache miss\n", "self._process_idx=7: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 133947681931264, 'v_cache': 133944460705792})) on device 7\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=7: Warming up graph for batch size 2048.\n", "self._process_idx=7: Capturing graph for batch size 2048.\n", "self._process_idx=4: Cuda graph internal warmup round 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[rank2]:[W415 20:44:41.125260926 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank4]:[W415 20:44:41.170304230 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 4]  using GPU 4 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank7]:[W415 20:44:41.221065710 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 7]  using GPU 7 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank6]:[W415 20:44:42.240758633 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 6]  using GPU 6 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank5]:[W415 20:44:42.251964786 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 5]  using GPU 5 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank1]:[W415 20:44:42.315385205 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["self._process_idx=6: cuda graph attn cache miss\n", "self._process_idx=6: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 132469676310528, 'v_cache': 132466455085056})) on device 6\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=6: Warming up graph for batch size 2048.\n", "self._process_idx=6: Capturing graph for batch size 2048.\n", "self._process_idx=7: Cuda graph internal warmup round 0\n", "self._process_idx=5: cuda graph attn cache miss\n", "self._process_idx=5: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 136900471947264, 'v_cache': 136897250721792})) on device 5\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=5: Warming up graph for batch size 2048.\n", "self._process_idx=5: Capturing graph for batch size 2048.\n", "self._process_idx=6: Cuda graph internal warmup round 0\n", "self._process_idx=5: Cuda graph internal warmup round 0\n", "self._process_idx=1: cuda graph attn cache miss\n", "self._process_idx=1: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 139919833956352, 'v_cache': 139916612730880})) on device 1\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=1: Warming up graph for batch size 2048.\n", "self._process_idx=1: Capturing graph for batch size 2048.\n", "self._process_idx=1: Cuda graph internal warmup round 0\n", "self._process_idx=0: cuda graph attn cache miss\n", "self._process_idx=0: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 136501039988736, 'v_cache': 136497818763264})) on device 0\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=0: Warming up graph for batch size 2048.\n", "self._process_idx=0: Capturing graph for batch size 2048.\n", "self._process_idx=0: Cuda graph internal warmup round 0\n", "self._process_idx=3: cuda graph attn cache miss\n", "self._process_idx=3: GraphedFunctionRecapturable: Capturing graph for _RecaptureKey(data_ptrs=immutabledict({'k_cache': 139220291158016, 'v_cache': 139217069932544})) on device 3\n", "Allocating static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=3: Warming up graph for batch size 2048.\n", "self._process_idx=3: Capturing graph for batch size 2048.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[rank0]:[W415 20:44:42.368801941 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[rank3]:[W415 20:44:42.489423537 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NCCL version 2.21.5+cuda12.4\n", "self._process_idx=3: Cuda graph internal warmup round 0\n", "self._process_idx=0: Cuda graph internal warmup round 0; synchronized.self._process_idx=2: Cuda graph internal warmup round 0; synchronized.self._process_idx=5: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=1: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=4: Cuda graph internal warmup round 0; synchronized.\n", "\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0; synchronized.self._process_idx=3: Cuda graph internal warmup round 0; synchronized.\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=5: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 1\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 1\n", "self._process_idx=4: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 1\n", "self._process_idx=1: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=2: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 1\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 1\n", "self._process_idx=7: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 1\n", "self._process_idx=3: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 1; synchronized.self._process_idx=2: Cuda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=5: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=1: Cuda graph internal warmup round 1; synchronized.self._process_idx=6: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 2\n", "self._process_idx=6: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 2\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 2\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=5: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 2\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 2\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 2\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 2\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 2\n", "self._process_idx=2: Cuda graph internal warmup round 2; synchronized.self._process_idx=1: Cuda graph internal warmup round 2; synchronized.self._process_idx=0: Cuda graph internal warmup round 2; synchronized.\n", "\n", "\n", "self._process_idx=3: Cuda graph internal warmup round 2; synchronized.self._process_idx=4: Cuda graph internal warmup round 2; synchronized.self._process_idx=5: Cuda graph internal warmup round 2; synchronized.\n", "\n", "\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=0: Finished capturing graph no 0 for batch size 2048 on device cuda:0\n", "self._process_idx=2: Finished capturing graph no 0 for batch size 2048 on device cuda:2self._process_idx=3: Finished capturing graph no 0 for batch size 2048 on device cuda:3self._process_idx=1: Finished capturing graph no 0 for batch size 2048 on device cuda:1self._process_idx=5: Finished capturing graph no 0 for batch size 2048 on device cuda:5\n", "\n", "\n", "\n", "self._process_idx=7: Finished capturing graph no 0 for batch size 2048 on device cuda:7\n", "self._process_idx=4: Finished capturing graph no 0 for batch size 2048 on device cuda:4\n", "self._process_idx=6: Finished capturing graph no 0 for batch size 2048 on device cuda:6\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=5: Finished restoring static inputs.self._process_idx=3: Finished restoring static inputs.self._process_idx=2: Finished restoring static inputs.\n", "\n", "self._process_idx=1: Finished restoring static inputs.self._process_idx=7: Finished restoring static inputs.\n", "\n", "\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=6: Finished restoring static inputs.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.Backing up static inputs for graph.\n", "Backing up static inputs for graph.Backing up static inputs for graph.\n", "\n", "\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.Backing up static inputs for graph.\n", "\n", "self._process_idx=2: Warming up graph for batch size 512.self._process_idx=5: Warming up graph for batch size 512.self._process_idx=1: Warming up graph for batch size 512.\n", "self._process_idx=7: Warming up graph for batch size 512.\n", "\n", "\n", "self._process_idx=0: Warming up graph for batch size 512.\n", "self._process_idx=3: Warming up graph for batch size 512.\n", "self._process_idx=6: Warming up graph for batch size 512.\n", "self._process_idx=2: Capturing graph for batch size 512.self._process_idx=7: Capturing graph for batch size 512.self._process_idx=0: Capturing graph for batch size 512.self._process_idx=1: Capturing graph for batch size 512.\n", "self._process_idx=5: Capturing graph for batch size 512.\n", "\n", "\n", "\n", "self._process_idx=4: Warming up graph for batch size 512.\n", "self._process_idx=3: Capturing graph for batch size 512.\n", "self._process_idx=6: Capturing graph for batch size 512.\n", "self._process_idx=4: Capturing graph for batch size 512.\n", "self._process_idx=2: Cuda graph internal warmup round 0\n", "self._process_idx=7: Cuda graph internal warmup round 0\n", "self._process_idx=0: Cuda graph internal warmup round 0self._process_idx=5: Cuda graph internal warmup round 0\n", "\n", "self._process_idx=1: Cuda graph internal warmup round 0\n", "self._process_idx=3: Cuda graph internal warmup round 0\n", "self._process_idx=6: Cuda graph internal warmup round 0\n", "self._process_idx=4: Cuda graph internal warmup round 0\n", "self._process_idx=0: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=2: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=1: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=3: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 0; synchronized.self._process_idx=4: Cuda graph internal warmup round 0; synchronized.self._process_idx=5: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 0; synchronized.\n", "\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 1\n", "self._process_idx=1: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 1\n", "self._process_idx=4: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 1\n", "self._process_idx=5: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 1\n", "self._process_idx=7: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 1\n", "self._process_idx=2: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 1\n", "self._process_idx=3: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 1; synchronized.self._process_idx=3: Cuda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=5: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 2\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 2\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=5: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 2\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 2\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 2\n", "self._process_idx=7: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 2\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; finished.self._process_idx=3: Finished restoring static inputs.\n", "\n", "self._process_idx=3: Cuda graph internal warmup round 2\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 2\n", "self._process_idx=0: Cuda graph internal warmup round 2; synchronized.self._process_idx=1: Cuda graph internal warmup round 2; synchronized.\n", "self._process_idx=3: Cuda graph internal warmup round 2; synchronized.self._process_idx=2: Cuda graph internal warmup round 2; synchronized.\n", "\n", "\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 2; synchronized.self._process_idx=4: Cuda graph internal warmup round 2; synchronized.\n", "\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; finished.self._process_idx=4: Finished restoring static inputs.\n", "\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Finished capturing graph no 1 for batch size 512 on device cuda:0self._process_idx=3: Finished capturing graph no 1 for batch size 512 on device cuda:3\n", "self._process_idx=2: Finished capturing graph no 1 for batch size 512 on device cuda:2self._process_idx=6: Finished capturing graph no 1 for batch size 512 on device cuda:6self._process_idx=1: Finished capturing graph no 1 for batch size 512 on device cuda:1\n", "\n", "\n", "\n", "self._process_idx=7: Finished capturing graph no 1 for batch size 512 on device cuda:7self._process_idx=5: Finished capturing graph no 1 for batch size 512 on device cuda:5\n", "\n", "self._process_idx=4: Finished capturing graph no 1 for batch size 512 on device cuda:4\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=7: Finished restoring static inputs.self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=6: Finished restoring static inputs.\n", "\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=4: Finished restoring static inputs.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "\n", "Backing up static inputs for graph.\n", "self._process_idx=3: Warming up graph for batch size 128.\n", "self._process_idx=1: Warming up graph for batch size 128.\n", "self._process_idx=5: Warming up graph for batch size 128.\n", "self._process_idx=2: Warming up graph for batch size 128.self._process_idx=0: Warming up graph for batch size 128.\n", "self._process_idx=3: Capturing graph for batch size 128.self._process_idx=7: Warming up graph for batch size 128.\n", "\n", "self._process_idx=6: Warming up graph for batch size 128.\n", "\n", "self._process_idx=4: Warming up graph for batch size 128.\n", "self._process_idx=1: Capturing graph for batch size 128.self._process_idx=5: Capturing graph for batch size 128.\n", "self._process_idx=0: Capturing graph for batch size 128.self._process_idx=2: Capturing graph for batch size 128.\n", "\n", "self._process_idx=7: Capturing graph for batch size 128.\n", "self._process_idx=6: Capturing graph for batch size 128.\n", "\n", "self._process_idx=4: Capturing graph for batch size 128.\n", "self._process_idx=3: Cuda graph internal warmup round 0\n", "self._process_idx=5: Cuda graph internal warmup round 0self._process_idx=2: Cuda graph internal warmup round 0\n", "\n", "self._process_idx=0: Cuda graph internal warmup round 0self._process_idx=7: Cuda graph internal warmup round 0\n", "\n", "self._process_idx=1: Cuda graph internal warmup round 0\n", "self._process_idx=6: Cuda graph internal warmup round 0\n", "self._process_idx=4: Cuda graph internal warmup round 0\n", "self._process_idx=3: Cuda graph internal warmup round 0; synchronized.self._process_idx=1: Cuda graph internal warmup round 0; synchronized.self._process_idx=6: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=0: Cuda graph internal warmup round 0; synchronized.self._process_idx=2: Cuda graph internal warmup round 0; synchronized.\n", "\n", "\n", "self._process_idx=4: Cuda graph internal warmup round 0; synchronized.self._process_idx=7: Cuda graph internal warmup round 0; synchronized.\n", "\n", "\n", "self._process_idx=5: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 1\n", "self._process_idx=1: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=1: Finished restoring static inputs.self._process_idx=4: Cuda graph internal warmup round 0; finished.\n", "\n", "self._process_idx=1: Cuda graph internal warmup round 1\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 1\n", "self._process_idx=5: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 1\n", "self._process_idx=7: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 1\n", "self._process_idx=2: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 1\n", "self._process_idx=3: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 1\n", "self._process_idx=3: Cuda graph internal warmup round 1; synchronized.self._process_idx=2: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 1; synchronized.self._process_idx=5: Cuda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=7: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 2\n", "self._process_idx=5: Cuda graph internal warmup round 1; finished.self._process_idx=4: Cuda graph internal warmup round 1; finished.\n", "\n", "self._process_idx=5: Finished restoring static inputs.self._process_idx=4: Finished restoring static inputs.\n", "\n", "self._process_idx=4: Cuda graph internal warmup round 2\n", "self._process_idx=5: Cuda graph internal warmup round 2\n", "self._process_idx=6: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 2\n", "self._process_idx=7: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 2\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 2\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 2\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 2\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=1: Cuda graph internal warmup round 2; synchronized.self._process_idx=2: Cuda graph internal warmup round 2; synchronized.\n", "\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=0: Finished capturing graph no 2 for batch size 128 on device cuda:0self._process_idx=3: Finished capturing graph no 2 for batch size 128 on device cuda:3self._process_idx=2: Finished capturing graph no 2 for batch size 128 on device cuda:2\n", "\n", "\n", "self._process_idx=1: Finished capturing graph no 2 for batch size 128 on device cuda:1self._process_idx=5: Finished capturing graph no 2 for batch size 128 on device cuda:5self._process_idx=6: Finished capturing graph no 2 for batch size 128 on device cuda:6\n", "\n", "self._process_idx=7: Finished capturing graph no 2 for batch size 128 on device cuda:7\n", "self._process_idx=4: Finished capturing graph no 2 for batch size 128 on device cuda:4\n", "\n", "self._process_idx=2: Finished restoring static inputs.self._process_idx=3: Finished restoring static inputs.\n", "\n", "self._process_idx=1: Finished restoring static inputs.self._process_idx=5: Finished restoring static inputs.\n", "\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=4: Finished restoring static inputs.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "Backing up static inputs for graph.\n", "self._process_idx=2: Warming up graph for batch size 32.self._process_idx=5: Warming up graph for batch size 32.self._process_idx=3: Warming up graph for batch size 32.\n", "\n", "self._process_idx=1: Warming up graph for batch size 32.\n", "self._process_idx=7: Warming up graph for batch size 32.\n", "\n", "self._process_idx=6: Warming up graph for batch size 32.\n", "self._process_idx=0: Warming up graph for batch size 32.\n", "self._process_idx=4: Warming up graph for batch size 32.\n", "self._process_idx=2: Capturing graph for batch size 32.self._process_idx=5: Capturing graph for batch size 32.self._process_idx=1: Capturing graph for batch size 32.\n", "\n", "self._process_idx=3: Capturing graph for batch size 32.self._process_idx=7: Capturing graph for batch size 32.\n", "\n", "\n", "self._process_idx=6: Capturing graph for batch size 32.\n", "self._process_idx=0: Capturing graph for batch size 32.\n", "self._process_idx=4: Capturing graph for batch size 32.\n", "self._process_idx=5: Cuda graph internal warmup round 0self._process_idx=1: Cuda graph internal warmup round 0self._process_idx=3: Cuda graph internal warmup round 0self._process_idx=2: Cuda graph internal warmup round 0\n", "\n", "\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0self._process_idx=7: Cuda graph internal warmup round 0self._process_idx=0: Cuda graph internal warmup round 0\n", "\n", "\n", "self._process_idx=4: Cuda graph internal warmup round 0\n", "self._process_idx=2: Cuda graph internal warmup round 0; synchronized.self._process_idx=0: Cuda graph internal warmup round 0; synchronized.self._process_idx=1: Cuda graph internal warmup round 0; synchronized.\n", "\n", "self._process_idx=3: Cuda graph internal warmup round 0; synchronized.self._process_idx=5: Cuda graph internal warmup round 0; synchronized.\n", "self._process_idx=7: Cuda graph internal warmup round 0; synchronized.\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0; synchronized.self._process_idx=4: Cuda graph internal warmup round 0; synchronized.\n", "\n", "\n", "self._process_idx=6: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 1\n", "self._process_idx=1: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=1: Cuda graph internal warmup round 1\n", "self._process_idx=4: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 1\n", "self._process_idx=7: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 0; finished.self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 1\n", "\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 1\n", "self._process_idx=2: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 1\n", "self._process_idx=3: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 1\n", "self._process_idx=0: Cuda graph internal warmup round 0; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 1\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=3: Cuda graph internal warmup round 1; synchronized.self._process_idx=2: Cuda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=5: Cuda graph internal warmup round 1; synchronized.self._process_idx=7: Cuda graph internal warmup round 1; synchronized.\n", "\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; synchronized.\n", "self._process_idx=6: Cuda graph internal warmup round 1; synchronized.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 1; finished.self._process_idx=1: Cuda graph internal warmup round 2\n", "\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=5: Cuda graph internal warmup round 2\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=4: Cuda graph internal warmup round 2\n", "self._process_idx=6: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=7: Cuda graph internal warmup round 1; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=6: Cuda graph internal warmup round 2\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=7: Cuda graph internal warmup round 2\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=2: Cuda graph internal warmup round 2\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=0: Cuda graph internal warmup round 2\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 1; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Cuda graph internal warmup round 2\n", "self._process_idx=0: Cuda graph internal warmup round 2; synchronized.self._process_idx=1: Cuda graph internal warmup round 2; synchronized.self._process_idx=2: Cuda graph internal warmup round 2; synchronized.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; synchronized.\n", "\n", "\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; synchronized.\n", "self._process_idx=1: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=5: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=1: Finished restoring static inputs.\n", "self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=4: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=4: Finished restoring static inputs.\n", "self._process_idx=6: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=6: Finished restoring static inputs.\n", "self._process_idx=2: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=2: Finished restoring static inputs.\n", "self._process_idx=7: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=7: Finished restoring static inputs.\n", "self._process_idx=0: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=3: <PERSON>uda graph internal warmup round 2; finished.\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=3: Finished capturing graph no 3 for batch size 32 on device cuda:3self._process_idx=0: Finished capturing graph no 3 for batch size 32 on device cuda:0\n", "\n", "self._process_idx=2: Finished capturing graph no 3 for batch size 32 on device cuda:2self._process_idx=1: Finished capturing graph no 3 for batch size 32 on device cuda:1\n", "\n", "self._process_idx=5: Finished capturing graph no 3 for batch size 32 on device cuda:5self._process_idx=7: Finished capturing graph no 3 for batch size 32 on device cuda:7self._process_idx=4: Finished capturing graph no 3 for batch size 32 on device cuda:4\n", "\n", "\n", "self._process_idx=6: Finished capturing graph no 3 for batch size 32 on device cuda:6\n", "self._process_idx=3: Finished restoring static inputs.\n", "self._process_idx=2: Finished restoring static inputs.self._process_idx=1: Finished restoring static inputs.self._process_idx=7: Finished restoring static inputs.self._process_idx=5: Finished restoring static inputs.\n", "self._process_idx=4: Finished restoring static inputs.\n", "\n", "\n", "\n", "self._process_idx=0: Finished restoring static inputs.\n", "self._process_idx=6: Finished restoring static inputs.\n", "<tool_call>\n", "{\"name\": \"get_temperature\", \"arguments\": {\"location\": \"New York\"}}\n", "</tool_call>\n"]}], "source": ["# Load 1p client\n", "\n", "# Create request to 1st party client\n", "\n", "tool_definitions = [\n", "    ToolDefinition(\n", "        name=\"get_temperature\",\n", "        description=\"Get the current temperature in a given location\",\n", "        input_schema_json=json.dumps(\n", "            {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"location\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The location to get the temperature for\",\n", "                    }\n", "                },\n", "                \"required\": [\"location\"],\n", "            }\n", "        ),\n", "    )\n", "]\n", "\n", "structured_chat_prompt_output = StructuredChatPromptOutput(\n", "    system_prompt=\"You are a helpful assistant.\",\n", "    chat_history=[],\n", "    message=\"What is the temperature in new york?\",\n", "    retrieved_chunks_in_prompt=[],\n", "    retrieval_as_tool=False,\n", "    tools=[\"get_temperature\"],\n", "    tool_definitions=tool_definitions,\n", ")\n", "\n", "\n", "responses = list(\n", "    local_model_client.generate_response(\n", "        prompt_output=structured_chat_prompt_output,\n", "    )\n", ")\n", "print(responses[0])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<tool_call>\n", "{\"name\": \"get_temperature\", \"arguments\": {\"location\": \"New York\"}}\n", "</tool_call>\n"]}], "source": ["for response in responses:\n", "    print(response)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[TextResult(text='Here\\'s a Python function to calculate the factorial of a non-negative integer `n`:\\n\\n```python\\ndef factorial(n):\\n    # Check if the input is a non-negative integer\\n    if not isinstance(n, int) or n < 0:\\n        raise ValueError(\"Input must be a non-negative integer\")\\n    \\n    # Base case: factorial of 0 or 1 is 1\\n    if n == 0 or n == 1:\\n        return 1\\n    \\n    # Recursive case: n * factorial of (n-1)\\n    return n * factorial(n - 1)\\n```\\n\\n### Explanation:\\n\\n1. **Input Validation**: The function first checks if the input `n` is a non-negative integer. If not, it raises a `ValueError`.\\n\\n2. **Base Case**: The factorial of 0 and 1 is defined as 1. This is the stopping condition for the recursion.\\n\\n3. **Recursive Case**: For any other positive integer `n`, the factorial is calculated as `n * factorial(n - 1)`. This recursive call continues until it reaches the base case.\\n\\nThis function uses recursion to compute the factorial, which is a straightforward approach for understanding the concept. However, for very large values of `n`, this recursive approach can lead to a stack overflow due to too many recursive calls. In such cases, an iterative approach or using Python\\'s built-in `math.factorial` function might be more efficient.')]\n"]}], "source": ["# Create LLMClient from 1p client\n", "\n", "from research.llm_apis.first_party_llm_client import FirstPartyLLMClient\n", "from research.llm_apis.llm_client import TextPrompt\n", "\n", "llm_client = FirstPartyLLMClient(local_model_client)\n", "\n", "# Create a simple test request\n", "messages = [\n", "    [TextPrompt(text=\"Write a function to calculate the factorial of a number.\")]\n", "]\n", "response, metadata = llm_client.generate(\n", "    messages=messages,\n", "    max_tokens=1024,\n", "    system_prompt=\"You are a helpful coding assistant.\",\n", ")\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import asdict\n", "import json\n", "from functools import partial\n", "import copy\n", "\n", "import torch\n", "\n", "from research.fastbackward.checkpointing.huggingface import (\n", "    load_huggingface_checkpoint,\n", "    _load_hf_checkpoint,\n", "    _resize_vocab_weight,\n", "    _remap_top_level_state_dict,\n", "    _remap_llama_hf_layer_state_dict,\n", "    _LLAMA_REMAPS,\n", "    _QWEN_ADDITIONAL_REMAPS,\n", ")\n", "from research.fastbackward.model import ModelArgs, GenericAttnSpec\n", "from research.fastbackward import distributed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7B\n", "\n", "path = \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B\"\n", "output_path = \"/mnt/efs/augment/checkpoints/qwen25-coder/7b-fb/\"\n", "\n", "attn_config = GenericAttnSpec(\n", "    hidden_dim=3584,\n", "    n_heads=28,\n", "    n_kv_heads=4,\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    bias=False,\n", "    qkv_bias=True,\n", ")\n", "\n", "model_args = ModelArgs(\n", "    ffn_type=\"glu\",\n", "    bias=\"none\",\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    dim=3584,\n", "    n_layers=28,\n", "    n_heads=32,\n", "    n_kv_heads=32,\n", "    vocab_size=152064,\n", "    multiple_of=512,\n", "    ffn_dim_multiplier=1.95,\n", "    norm_eps=1e-06,\n", "    rope_theta=1000000.0,\n", "    attn_config=attn_config,\n", "    max_seq_len=32768,\n", ")\n", "\n", "\n", "# Initialize transformer distributed\n", "distributed.init_distributed_for_training(1)\n", "state_dict = load_huggingface_checkpoint(path)\n", "torch.save(state_dict, output_path + \"consolidated.00.pth\")\n", "# save serialized model args\n", "model_args_json = asdict(model_args)\n", "with open(output_path + \"params.json\", \"w\") as f:\n", "    json.dump(model_args_json, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check that model loads correctly\n", "from pathlib import Path\n", "from research.models.fastbackward_models import FastBackwardLLM\n", "from research.models import GenerationOptions\n", "from research.core.model_input import ModelInput\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "\n", "def test_model(model_path, model_parallel_size=1):\n", "    model = FastBackwardLLM(\n", "        checkpoint_path=Path(model_path),\n", "        seq_length=4096,\n", "        model_parallel_size=model_parallel_size,\n", "    )\n", "    model.load()\n", "\n", "    tokenizer = create_tokenizer_by_name(\"qwen25coder\")\n", "    model.prompt_formatter.tokenizer = tokenizer\n", "    options = GenerationOptions(temperature=0.0, max_generated_tokens=64)\n", "    model_input = ModelInput(prefix=\"def hello_world() -> str:\\n\")\n", "    print(model.generate(model_input, options))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_model(output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1.5B\n", "\n", "path = \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-1.5B\"\n", "output_path = \"/mnt/efs/augment/checkpoints/qwen25-coder/1b5-fb/\"\n", "\n", "\n", "attn_config = GenericAttnSpec(\n", "    hidden_dim=1536,\n", "    n_heads=12,\n", "    n_kv_heads=2,\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    bias=False,\n", "    qkv_bias=True,\n", ")\n", "\n", "model_args = ModelArgs(\n", "    ffn_type=\"glu\",\n", "    bias=\"none\",\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    dim=1536,\n", "    n_layers=28,\n", "    n_heads=12,\n", "    n_kv_heads=2,\n", "    vocab_size=152064,\n", "    multiple_of=256,\n", "    ffn_dim_multiplier=2.15,\n", "    norm_eps=1e-06,\n", "    rope_theta=1000000.0,\n", "    attn_config=attn_config,\n", "    max_seq_len=32768,\n", ")\n", "\n", "\n", "# Initialize transformer distributed\n", "# distributed.init_distributed_for_training(1)\n", "\n", "config, state_dict = _load_hf_checkpoint(path)\n", "\n", "model_type = \"qwen\"\n", "original_embedding_key = \"model.embed_tokens.weight\"\n", "original_lm_head_key = \"lm_head.weight\"\n", "top_level_remaps = _LLAMA_REMAPS\n", "layer_remap_fn = partial(\n", "    _remap_llama_hf_layer_state_dict,\n", "    extra_remaps=_QWEN_ADDITIONAL_REMAPS,\n", ")\n", "\n", "mp_world_size = 1\n", "mp_rank = 0\n", "model_vocab_size = model_args.vocab_size\n", "\n", "result_state_dict = {}\n", "state_dict[original_embedding_key] = _resize_vocab_weight(\n", "    state_dict[original_embedding_key], model_vocab_size\n", ")\n", "state_dict[original_lm_head_key] = copy.deepcopy(state_dict[original_embedding_key])\n", "_remap_top_level_state_dict(\n", "    state_dict, result_state_dict, mp_world_size, mp_rank, top_level_remaps\n", ")\n", "layer_remap_fn(state_dict, result_state_dict, mp_world_size, mp_rank, config)\n", "\n", "torch.save(result_state_dict, output_path + \"consolidated.00.pth\")\n", "# save serialized model args\n", "model_args_json = asdict(model_args)\n", "with open(output_path + \"params.json\", \"w\") as f:\n", "    json.dump(model_args_json, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_model(output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7B instruct\n", "\n", "path = \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct\"\n", "output_path = \"/mnt/efs/augment/checkpoints/qwen25-coder/7b-instruct-fb-mp2/\"\n", "\n", "attn_config = GenericAttnSpec(\n", "    hidden_dim=3584,\n", "    n_heads=28,\n", "    n_kv_heads=4,\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    bias=False,\n", "    qkv_bias=True,\n", ")\n", "\n", "model_args = ModelArgs(\n", "    ffn_type=\"glu\",\n", "    bias=\"none\",\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    dim=3584,\n", "    n_layers=28,\n", "    n_heads=32,\n", "    n_kv_heads=32,\n", "    vocab_size=152064,\n", "    multiple_of=512,\n", "    ffn_dim_multiplier=1.95,\n", "    norm_eps=1e-06,\n", "    rope_theta=1000000.0,\n", "    attn_config=attn_config,\n", "    max_seq_len=32768,\n", ")\n", "\n", "n_mp = 2\n", "distributed.init_distributed_for_training(n_mp)\n", "for mp_rank in range(n_mp):\n", "    state_dict = load_huggingface_checkpoint(path, mp_world_size=n_mp, mp_rank=mp_rank)\n", "    torch.save(state_dict, output_path + f\"consolidated.{mp_rank:02d}.pth\")\n", "\n", "model_args_json = asdict(model_args)\n", "with open(output_path + \"params.json\", \"w\") as f:\n", "    json.dump(model_args_json, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_model(output_path, n_mp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 14B Instruct\n", "\n", "path = \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-14B-Instruct\"\n", "output_path = \"/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-fb-mp4/\"\n", "\n", "attn_config = GenericAttnSpec(\n", "    hidden_dim=5120,\n", "    n_heads=40,\n", "    n_kv_heads=8,\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    bias=False,\n", "    qkv_bias=True,\n", ")\n", "\n", "model_args = ModelArgs(\n", "    ffn_type=\"glu\",\n", "    bias=\"none\",\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    dim=5120,\n", "    n_layers=48,\n", "    n_heads=40,\n", "    n_kv_heads=8,\n", "    vocab_size=152064,\n", "    multiple_of=256,\n", "    ffn_dim_multiplier=1.0,\n", "    norm_eps=1e-06,\n", "    rope_theta=1000000.0,\n", "    attn_config=attn_config,\n", "    max_seq_len=32768,\n", ")\n", "\n", "n_mp = 4\n", "distributed.init_distributed_for_training(n_mp)\n", "for mp_rank in range(n_mp):\n", "    state_dict = load_huggingface_checkpoint(path, mp_world_size=n_mp, mp_rank=mp_rank)\n", "    torch.save(state_dict, output_path + f\"consolidated.{mp_rank:02d}.pth\")\n", "\n", "model_args_json = asdict(model_args)\n", "with open(output_path + \"params.json\", \"w\") as f:\n", "    json.dump(model_args_json, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_model(output_path, n_mp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 32B Instruct\n", "\n", "path = \"/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-32B-Instruct\"\n", "output_path = \"/mnt/efs/augment/checkpoints/qwen25-coder/32b-instruct-fb-mp8/\"\n", "\n", "attn_config = GenericAttnSpec(\n", "    hidden_dim=5120,\n", "    n_heads=40,\n", "    n_kv_heads=8,\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    bias=False,\n", "    qkv_bias=True,\n", ")\n", "\n", "model_args = ModelArgs(\n", "    ffn_type=\"glu\",\n", "    bias=\"none\",\n", "    norm_type=\"rmsnorm\",\n", "    pos_embed_type=\"rope\",\n", "    dim=5120,\n", "    n_layers=64,\n", "    n_heads=40,\n", "    n_kv_heads=8,\n", "    vocab_size=152064,\n", "    multiple_of=256,\n", "    ffn_dim_multiplier=2.02,\n", "    norm_eps=1e-06,\n", "    rope_theta=1000000.0,\n", "    attn_config=attn_config,\n", "    max_seq_len=32768,\n", ")\n", "\n", "n_mp = 8\n", "distributed.init_distributed_for_training(n_mp)\n", "for mp_rank in range(n_mp):\n", "    state_dict = load_huggingface_checkpoint(path, mp_world_size=n_mp, mp_rank=mp_rank)\n", "    torch.save(state_dict, output_path + f\"consolidated.{mp_rank:02d}.pth\")\n", "\n", "model_args_json = asdict(model_args)\n", "with open(output_path + \"params.json\", \"w\") as f:\n", "    json.dump(model_args_json, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_model(output_path, n_mp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dim = 5120\n", "multiple_of = 256\n", "ffn_dim_multiplier = 1.37\n", "\n", "intermediate_size = int(2 * 4 * dim / 3)\n", "# custom dim factor multiplier\n", "intermediate_size = int(ffn_dim_multiplier * intermediate_size)\n", "intermediate_size = multiple_of * ((intermediate_size + multiple_of - 1) // multiple_of)\n", "print(intermediate_size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!--  -->"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
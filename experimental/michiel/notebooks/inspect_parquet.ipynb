{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading file: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-samples/part-00000-d947f2de-1f89-4832-a66a-1f7026b8ed3f-c000.zstd.parquet\n", "                                              prefix  \\\n", "0  #!/usr/bin/env python3\\n# Copyright 2004-prese...   \n", "1  ﻿using System;\\nusing System.Collections.Gener...   \n", "2  import ConfigParser\\nimport logging\\nimport re...   \n", "3  package com.dongtech.upload;\\n\\nimport java.io...   \n", "4  import {Component} from '@angular/core';\\nimpo...   \n", "\n", "                                        middle_spans  \\\n", "0  b'\\x80\\x04\\x95\\x07\\x08\\x00\\x00\\x00\\x00\\x00\\x00...   \n", "1  b'\\x80\\x04\\x95W\\x01\\x00\\x00\\x00\\x00\\x00\\x00\\x8...   \n", "2                                      b'\\x80\\x04).'   \n", "3  b'\\x80\\x04\\x95\\xb7\\x01\\x00\\x00\\x00\\x00\\x00\\x00...   \n", "4  b'\\x80\\x04\\x95T\\x04\\x00\\x00\\x00\\x00\\x00\\x00(\\x...   \n", "\n", "                                              suffix  suffix_offset  \\\n", "0                                                 \\n              0   \n", "1  )\\n        }\\n\\n        /// <summary>\\n       ...             -1   \n", "2  n' )\\n    fp.write( 'default-push = .\\n' )\\n  ...             -1   \n", "3  ))\\n\\t\\t\\n\\t\\tfilename = Math.abs(file.getOrig...             -2   \n", "4  \\n                    }\\n            ]\\n      ...            -36   \n", "\n", "   middle_char_start  middle_char_end  \\\n", "0                579             2291   \n", "1               5188             5191   \n", "2               2068             2069   \n", "3               1051             1096   \n", "4              15366            16031   \n", "\n", "                                           file_path  \\\n", "0          scan_service/scan_service/utils/alerts.py   \n", "1  Telerik.Sitefinity.Frontend/FilesMonitoring/Gr...   \n", "2  lib/tool_shed/util/repository_maintenance_util.py   \n", "3  dubbo-springboot/dubbo-consumer/src/main/java/...   \n", "4  src/pages/organization-profile/organization-pr...   \n", "\n", "                                         line_chunks  \\\n", "0  [{\"id\": \"d9373604d79a275866c8157c73240c77e997a...   \n", "1  [{\"id\": \"f126aaee9c747f030ed4d7503b42e802de202...   \n", "2  [{\"id\": \"9db7e4e7f9939c7ebb31e55c56a5856c075e6...   \n", "3  [{\"id\": \"60cdc395ae46a3df1b7c7bcd30436a935f4f2...   \n", "4  [{\"id\": \"e3e5e61f15b68136c74451ad46eeab62e296b...   \n", "\n", "                                    signature_chunks    random  \n", "0  [{\"id\": \"8eda03d77597bbad1e466cc6549facc510787...  0.664231  \n", "1  [{\"id\": \"cf0bba88ffa56cb6de24d1a30c76197ab97a4...  0.744380  \n", "2  [{\"id\": \"9022353cf78684d31b9c0a516a09491671e57...  0.264236  \n", "3  [{\"id\": \"60cdc395ae46a3df1b7c7bcd30436a935f4f2...  0.870686  \n", "4  [{\"id\": \"b40cd449b446b62ad746d92fa822810eff67a...  0.595112  \n"]}], "source": ["import pandas as pd\n", "from pathlib import Path\n", "\n", "def load_first_parquet_file(directory_path, num_rows=5):\n", "    # Convert the directory path to a Path object\n", "    dir_path = Path(directory_path)\n", "    \n", "    # Find the first Parquet file in the directory\n", "    parquet_files = list(dir_path.glob(\"*.parquet\"))\n", "    \n", "    if not parquet_files:\n", "        raise FileNotFoundError(f\"No Parquet files found in {directory_path}\")\n", "    \n", "    # Get the path of the first <PERSON><PERSON><PERSON> file\n", "    first_parquet_file = parquet_files[0]\n", "    \n", "    print(f\"Loading file: {first_parquet_file}\")\n", "    \n", "    # Read the first 5 rows of the Parquet file into a pandas DataFrame\n", "    df = pd.read_parquet(first_parquet_file, engine='pyarrow')\n", "    \n", "    return df.head(num_rows)\n", "\n", "# Example usage\n", "directory_path = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-samples\"\n", "result_df = load_first_parquet_file(directory_path)\n", "print(result_df)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
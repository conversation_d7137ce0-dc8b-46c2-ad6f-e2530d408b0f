{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from colorama import Fore, Style\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['ground_truth_log_likelihood', 'ground_truth_token_accuracy', 'patch', 'prefix', 'suffix', 'prompt', 'filtered_chunks', 'generation', 'completion', 'artifacts'])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["run_path = \"/mnt/efs/augment/eval/jobs/Kag8tJHY/000_RAGSystem_hydra_completed_patches.jsonl.zst\"\n", "results = read_jsonl_zst(run_path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def print_sample(prefix, suffix, generation):\n", "    prefix_string = Fore.BLUE + prefix + Style.RESET_ALL\n", "    generation_string = Fore.YELLOW + generation + Style.RESET_ALL\n", "    suffix_string = Fore.WHITE + suffix + Style.RESET_ALL\n", "    print(prefix_string + generation_string + suffix_string)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34m except Exception as e:\n", "        assert False, \"Failed to get optimizer class with error: \" + str(e)\n", "\u001b[0m\u001b[33m \n", "    # Hard-check for one of the optimizers \n", "    _class = utils.get_optimizer_class(\"adamw\")\u001b[0m\u001b[37m\n", "    assert _class == torch.optim.AdamW\n", "    if HAS_BNB:\n", "        _bnb_class = utils.get_optimizer_cla\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34ms_getters(scheduler_name: str):\n", "    try:\n", "        _class = utils.get_scheduler_class(scheduler_name)\n", "\u001b[0m\u001b[33m     except Exception as e:\n", "        assert False, \"Failed to get scheduler class with error: \" + str(e)\n", "\u001b[0m\u001b[37m\n", "    # Hard-check for one of the schedulers\n", "    _class = utils.get_scheduler_class(\"cosine_annealing\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mal_norm,\n", "        modeling_utils.hf_get_decoder_blocks,\n", "        modeling_utils.hf_get_lm_head,\n", "    ]\n", "\u001b[0m\u001b[33m     for get in arch_getters:\n", "        try:\n", "            get(arch)\u001b[0m\u001b[37m\n", "        except Exception as e:\n", "            assert False, \"Failed to get model attribute with error:\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mfies a list of `modified_modules`\n", "    delta_type, delta_kwargs = modeling_utils.parse_delta_kwargs(\n", "\u001b[0m\u001b[33m         delta_kwargs={\"delta_type\": \"lora\", \"modified_modules\": [\"a\", \"b\"]}, \n", "        config=config,\u001b[0m\u001b[37m\n", "        num_layers_unfrozen=2,\n", "    )\n", "    for kwarg_mod in delta_kwargs[\"modified_modules\"]:\n", "       \u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mf.a4.std(unbiased=True), atol=1e-6)\n", "\n", "        a = torch.hstack((self.a1, self.a2, self.a3, self.a4))\n", "\u001b[0m\u001b[33m         assert torch.isclose(self.m.update(a)[1], a.std(unbiased=True), atol=1e-6)\u001b[0m\u001b[37m\n", "\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mirectories of `dir` named `configs`.\"\"\"\n", "    config_dirs = []\n", "    for root, dirs, _ in os.walk(dir):\n", "\u001b[0m\u001b[33m         if config_dir_name in dirs:\n", "            config_dirs.append(os.path.join(root, config_dir_name))\u001b[0m\u001b[37m\n", "    return config_dirs\n", "\n", "\n", "def _get_yaml_filepaths(dir: str) -> List[str]:\n", "    \"\"\"Returns a list of `\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mhs(dir: str) -> List[str]:\n", "    \"\"\"Returns a list of `yml` filepaths in `dir`.\"\"\"\n", "    filepaths = []\n", "\u001b[0m\u001b[33m     for file in os.listdir(dir):\u001b[0m\u001b[37m\n", "            filepaths.append(os.path.join(dir, file))\n", "    return filepaths\n", "\n", "\n", "def test_repo_trl_conf\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34ms\")]\n", "    config_files = sum(map(_get_yaml_filepaths, config_dirs), [])  # sum for flat-map behavior\n", "\u001b[0m\u001b[33m     for file in config_files:\u001b[0m\u001b[37m\n", "        try:\n", "            config = TRLConfig.load_yaml(file)\n", "            assert (\n", "                co\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34m file as TRLConfig.\n", "\n", "        :param yml_fp: Path to yaml file\n", "        :type yml_fp: str\n", "        \"\"\"\n", "\u001b[0m\u001b[33m         with open(yml_fp, \"r\") as f:\n", "            config = yaml.safe_load(f)\n", "\n", "        return cls.from_dict(config)\u001b[0m\u001b[37m\n", "\n", "    def to_dict(self):\n", "        \"\"\"\n", "        Convert TRLConfig to dictionary.\n", "        \"\"\"\n", "        da\u001b[0m\n", "\n", "\n", "\u001b[31m<PERSON><PERSON><PERSON> Sample\u001b[0m\n", "\u001b[34mconfig: Dict):\n", "        \"\"\"\n", "        Convert dictionary to TRLConfig.\n", "        \"\"\"\n", "        return cls(\n", "\u001b[0m\u001b[33m             method=get_method(config[\"train\"][\"pipeline\"]).from_dict(config[\"method\"]), \n", "            model=ModelConfig.from_dict(config[\"model\"]),\u001b[0m\u001b[37m\n", "            tokenizer=TokenizerConfig.from_dict(config[\"tokenizer\"]),\n", "            optimizer=Optimiz\u001b[0m\n", "\n", "\n"]}], "source": ["for result in results[:10]:\n", "    print(Fore.RED + \"Next Sample\" + Style.RESET_ALL)\n", "    print_sample(prefix=result[\"prefix\"][-100:], suffix=result[\"suffix\"][:100], generation=result[\"generation\"])\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mThis is a red text\u001b[0m\n"]}], "source": ["\n", "print(Fore.RED + 'This is a red text' + Style.RESET_ALL)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
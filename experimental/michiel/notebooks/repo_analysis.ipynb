{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "import pyspark.sql.functions as F\n", "from research.data.spark.pipelines.stages.common import (\n", "    load_dataset,\n", ")\n", "from research.data.spark.pipelines.stages.the_stack import (\n", "    _filter_by_alphanum_and_line_length,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["PIPELINE_ROOT = \"/home/<USER>/augment/augment/research/data/spark/pipelines\"\n", "builder = SparkSession.builder\n", "builder = builder.master(\"k8s://https://k8s.ord1.coreweave.com:443\")\n", "builder.config(\"spark.kubernetes.executor.podTemplateFile\", f\"{PIPELINE_ROOT}/configs/templates/spark-executor-template.yaml\")\n", "builder.config(\"spark.kubernetes.driver.podTemplateFile\", f\"{PIPELINE_ROOT}/configs/templates/spark-driver-template.yaml\")\n", "builder.config(\"spark.kubernetes.authenticate.driver.serviceAccountName\", \"spark-sa\")\n", "builder.config(\"spark.kubernetes.container.image.pullPolicy\", \"Always\")\n", "builder.config(\"spark.kubernetes.container.image\", \"au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_spark_python:spark-3.4.1\")\n", "builder.config(\"spark.kubernetes.file.upload.path\", \"/mnt/efs/aug-cw-lga1/spark-uploads\")\n", "builder.config(\"spark.kubernetes.namespace\", \"tenant-augment-eng\")\n", "builder.config(\"spark.executor.memory\", \"40g\")\n", "builder.config(\"spark.executor.instances\", \"4\")\n", "spark = builder.getOrCreate()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["PYTHON_INPUT = \"/mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=python\"\n", "CPP_INPUT = \"/mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=c++\"\n", "JAVA_INPUT = \"/mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=java\"\n", "JAVASCRIPT_INPUT = \"/mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=javascript\"\n", "\n", "REPO_COLUMN = \"max_stars_repo_name\"\n", "SIZE_COLUMN = \"size\"\n", "CONTENT_COLUMN = \"content\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def get_lang_reposize_distribution(spark, path):\n", "    df_lang = load_dataset(spark, path)\n", "    df_lang = _filter_by_alphanum_and_line_length(spark, df_lang)\n", "    # Create a new column \"content_length\" that contains the length of each string in the \"content\" column\n", "    df_content = df_lang.withColumn(\"content_length\", F.length(df_lang.content))\n", "\n", "    # Perform groupBy operation on \"repo_column\" and then sum the \"content_length\" column\n", "    df_grouped = df_content.groupBy(REPO_COLUMN).agg(F.sum(\"content_length\").alias(\"sum_content_length\"))\n", "\n", "    # List of percentiles you're interested in\n", "    percentiles = [0.05, 0.25, 0.5, 0.75, 0.95, 0.98, 0.99, 0.995, 0.998, 0.999, 0.9999]\n", "\n", "\n", "    # Calculate the approximate percentile of sum_content_length column\n", "    percentile_values = df_grouped.stat.approxQuantile(\"sum_content_length\", percentiles, 0.0001)\n", "\n", "    # Create a dictionary to print percentile values\n", "    percentile_dict = dict(zip(percentiles, percentile_values))\n", "    for k, v in percentile_dict.items():\n", "        print(f\"{k*100}th percentile: {v}\")    \n", "    "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from /mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=python\n", "Number of partitions: 303\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 3:==========================================>                (5 + 2) / 7]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["5.0th percentile: 540.0\n", "25.0th percentile: 3228.0\n", "50.0th percentile: 9706.0\n", "75.0th percentile: 29527.0\n", "95.0th percentile: 154466.0\n", "98.0th percentile: 316863.0\n", "99.0th percentile: 512589.0\n", "99.5th percentile: 813845.0\n", "99.8th percentile: 1434457.0\n", "99.9th percentile: 1995317.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Python distribution\n", "get_lang_reposize_distribution(spark, PYTHON_INPUT)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from /mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=java\n", "Number of partitions: 354\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 9:>                                                          (0 + 2) / 2]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["5.0th percentile: 868.0\n", "25.0th percentile: 6028.0\n", "50.0th percentile: 19821.0\n", "75.0th percentile: 63998.0\n", "95.0th percentile: 394321.0\n", "98.0th percentile: 907375.0\n", "99.0th percentile: 1627208.0\n", "99.5th percentile: 2778029.0\n", "99.8th percentile: 5383134.0\n", "99.9th percentile: 8635683.0\n", "99.99th percentile: 1500484027.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Java distribution\n", "get_lang_reposize_distribution(spark, JAVA_INPUT)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from /mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=c++\n", "Number of partitions: 202\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 13:==============================================>           (4 + 1) / 5]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["5.0th percentile: 670.0\n", "25.0th percentile: 5045.0\n", "50.0th percentile: 16586.0\n", "75.0th percentile: 58437.0\n", "95.0th percentile: 440149.0\n", "98.0th percentile: 1057989.0\n", "99.0th percentile: 1896078.0\n", "99.5th percentile: 3303069.0\n", "99.8th percentile: 6533554.0\n", "99.9th percentile: 10769585.0\n", "99.99th percentile: 756533736.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# cpp distribution\n", "get_lang_reposize_distribution(spark, CPP_INPUT)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded dataset from /mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/langpart=javascript\n", "Number of partitions: 556\n", "Partitioner: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 18:>                                                         (0 + 2) / 2]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["5.0th percentile: 301.0\n", "25.0th percentile: 1709.0\n", "50.0th percentile: 5140.0\n", "75.0th percentile: 15913.0\n", "95.0th percentile: 96345.0\n", "98.0th percentile: 226737.0\n", "99.0th percentile: 402179.0\n", "99.5th percentile: 666068.0\n", "99.8th percentile: 1120102.0\n", "99.9th percentile: 1683322.0\n", "99.99th percentile: 235382936.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Javascript distribution\n", "get_lang_reposize_distribution(spark, JAVASCRIPT_INPUT)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
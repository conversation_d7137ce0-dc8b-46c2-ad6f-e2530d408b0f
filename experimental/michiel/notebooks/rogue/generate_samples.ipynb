{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Notebook to generate Rogue samples.\"\"\"\n", "from dataclasses import asdict\n", "from datetime import datetime\n", "from functools import partial\n", "from pathlib import Path\n", "from types import SimpleNamespace\n", "\n", "from pyspark.sql import functions as F\n", "\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.data.rag.rogue import RogueSampleConfig, process_partition_pandas\n", "from research.data.rag.utils import (filter_by_repo_size, inspect_samples,\n", "                                     save_config_s3)\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.utils.generate_fim_data import FimDataProcessor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT_COLUMN = \"prompt_tokens\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "\n", "# These languages names are for the stack\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = RogueSampleConfig(\n", "    input=\"s3a://the-stack-processed/by-repo-3\",\n", "    output=\"s3a://augment-temporary/michiel/gensamples/test/\",\n", "    # output=\"s3a://michiel-dev-bucket/ragdata/eth6_4m_morelang4/\",\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    limit_repos=200,\n", "    repo_min_size=500000,\n", "    repo_max_size=100000000,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    random_seed=74912,\n", "    num_retrieved_chunks=40,\n", "    scorer_config={\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "    },\n", "    chunker_config={\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "        \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "    },\n", "    query_config={\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "    document_config={\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", ")\n", "\n", "now = datetime.now()\n", "formatted_time = now.strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "STAGE1_URI = f\"s3a://augment-temporary/michiel/gensamples/{formatted_time}/step1/\"\n", "STAGE2_URI = f\"s3a://augment-temporary/michiel/gensamples/{formatted_time}/step2/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Report statistics on repo languages\n", "top_languages = (df\n", "                 .groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])\n", "                 .count()\n", "                 .orderBy(<PERSON><PERSON>desc(\"count\"))\n", "                 .limit(100))\n", "\n", "# Show the result\n", "top_languages.show(100)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if hasattr(config, \"repo_languages\"):\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "    \n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=getattr(config, \"repo_min_size\", None),\n", "    max_size=getattr(config, \"repo_max_size\", None),\n", ")\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "limit_repos = config.limit_repos\n", "df = df.limit(limit_repos)\n", "\n", "num_partitions = max(limit_repos // 50, 20)\n", "\n", "df = df.repartition(num_partitions)\n", "df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "max_workers = min(64, limit_repos)\n", "spark = k8s_session(\n", "    max_workers=max_workers,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    process_partition_pandas(config),\n", "    input_path=STAGE1_URI,\n", "    output_path=config.output,\n", "    timeout=3600,  # one hour timeout\n", "    batch_size=min(100, limit_repos // max_workers),\n", "    ignore_error=True,\n", ")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"result\", result)\n", "stderr = result[\"task_info\"][\"stderr\"]\n", "print(\"stderr\", stderr)\n", "stdout = result[\"task_info\"][\"stdout\"]\n", "print(\"stdout\", stdout[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for line in stderr[0].split(\"\\n\"):\n", "    print(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = config.output\n", "path = path.split(\"s3a://\")[1]\n", "bucket_name, file_name = path.split(\"/\", 1)\n", "file_name += \"config.json\"\n", "save_config_s3(config=asdict(config), bucket_name=bucket_name, file_name=file_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect output samples\n", "df = inspect_samples(config.output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Report statistics on generated samples by language\n", "spark = k8s_session(max_workers=100)\n", "\n", "files = map_parquet.list_files(\n", "    spark,\n", "    config.output,\n", "    suffix=\"parquet\",\n", "    include_path=False,\n", ")\n", "read_paths = [config.output + filepath for filepath in files]\n", "df = spark.read.parquet(*read_paths)\n", "\n", "def get_file_extension(file_path):\n", "    return Path(file_path).suffix[1:]\n", "\n", "# Guess languages from file_path\n", "df = df.withColumn(\"extension\", F.udf(get_file_extension)(F.col(\"file_path\")))\n", "\n", "# Count for each language\n", "df.groupBy(\"extension\").count().show(100)\n", "\n", "spark.stop()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Converts Rogue samples into indexed dataset for training.\"\"\"\n", "\n", "import json\n", "from pathlib import Path\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from typing import Any, Dict, List\n", "from datetime import datetime\n", "from dataclasses import asdict\n", "\n", "\n", "from research.data.spark.pipelines.stages.common import (\n", "    export_indexed_dataset_helper,\n", ")\n", "\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.rag.rogue import generate_prompt_sl, RogueSLDataPromptFormattingConfig\n", "from research.data.rag.utils import pad_pack_tokens, repartition_and_shuffle\n", "from megatron.tokenizer.tokenizer import (\n", "    StarCoderTokenizer,\n", "    StarCoder2Tokenizer,\n", "    DeepSeekCoderBaseTokenizer,\n", "    LLama3BaseTokenizer,\n", "    CodestralTokenizer,\n", ")\n", "\n", "PROMPT_COLUMN = \"prompt_tokens\"\n", "spark = k8s_session()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = RogueSLDataPromptFormattingConfig(\n", "    # input=\"s3a://augment-temporary/michiel/gensamples/test/\",\n", "    input=\"s3a://michiel-dev-bucket/ragdata/eth6_morelang3/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/test_retrieval/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/star2_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/star2_roguesl_eth6_4m_morelang3_fprefret_npref250suf_quant50_rdrop015/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang3_prompt4k8k_fprefretnpref250suf_quant50_rdrop015/\",\n", "    # output=\"/mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang3_fprefretnpref250suf_aug05prompt3k8krdrop03/\",\n", "    # output=\"/mnt/efs/augment/data/processed/ralass/dataset/roguesl_eth6_4m_morelang3_fprefsufretnpref_aug05rdrop03/\",\n", "    output=\"/mnt/efs/augment/data/processed/rag/dataset/codestral_eth6_4m_morelang3_fpref1kretsuf0k5npref0k5_aug05rdrop03/\",\n", "    # tokenizer_name=\"starcoder2\",\n", "    # tokenizer_name=\"deepseek\",\n", "    tokenizer_name=\"codestral\",\n", "    # tokenizer_name=\"starcoder\",\n", "    num_validation_samples=50000,\n", "    seq_len=7937,\n", "    # seq_len=4097,\n", "    # component_order=[\"prefix\", \"suffix\", \"retrieval\", \"nearby_prefix\"],\n", "    # component_order=[\"prefix\", \"retrieval\", \"nearby_prefix\", \"suffix\"],\n", "    # component_order=[\"retrieval\", \"prefix\", \"suffix\"],\n", "    component_order=[\"prefix\", \"retrieval\", \"nearby_prefix\", \"suffix\"],\n", "    # component_order=[\"prefix\", \"retrieval\", \"suffix\", \"nearby_prefix\"],\n", "    max_prefix_tokens=1024,\n", "    max_suffix_tokens=512,\n", "    max_retrieved_chunk_tokens=-1,\n", "    max_filename_tokens=50,\n", "    max_target_tokens=256,\n", "    # max_prompt_tokens=3838,\n", "    max_prompt_tokens=6142,\n", "    random_seed=74912,\n", "    data_augmentation_rate=0.5,\n", "    dense_retrieval_dropout_rate=0.3,\n", "    max_prompt_token_range=(3072, 7678),\n", "    # max_prompt_token_range=None,\n", "    context_quant_token_len=64,\n", "    # context_quant_token_len=0,\n", "    nearby_prefix_token_len=512,\n", "    # nearby_prefix_token_len=0,\n", "    nearby_prefix_token_overlap=0,\n", "    nearby_suffix_token_len=0,\n", "    nearby_suffix_token_overlap=0,\n", "    use_far_prefix_token=True,\n", "    # use_far_prefix_token=True,\n", "    # prepend_bos_token=True,\n", ")\n", "\n", "SAMPLES_COLUMN = \"prompt_tokens\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now()\n", "formatted_time = now.strftime('%Y-%m-%d_%H-%M-%S')\n", "step1_uri = f\"s3a://augment-temporary/michiel/{formatted_time}/step1/\"\n", "step2_uri = f\"s3a://augment-temporary/michiel/{formatted_time}/step2/\"\n", "input_columns=[\"prefix\", \"middle\", \"suffix\", \"suffix_offset\", \"middle_char_start\", \"middle_char_end\", \"file_path\", \"retrieved_chunks\"]\n", "\n", "if config.tokenizer_name == \"starcoder\":\n", "    tokenizer = StarCoderTokenizer()\n", "elif config.tokenizer_name == \"starcoder2\":\n", "    tokenizer = StarCoder2Tokenizer()\n", "elif config.tokenizer_name == \"deepseek\":\n", "    tokenizer = DeepSeekCoderBaseTokenizer()\n", "elif config.tokenizer_name == \"llama3\":\n", "    tokenizer = LLama3BaseTokenizer()\n", "elif config.tokenizer_name == \"codestral\":\n", "    tokenizer = CodestralTokenizer()\n", "else:\n", "    raise ValueError(f\"Unknown tokenizer {config.tokenizer_name}\")\n", "\n", "result = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        generate_prompt_sl,\n", "        tokenizer=tokenizer,\n", "        config=config,\n", "    ),\n", "    input_path=config.input,\n", "    output_path=step1_uri,\n", "    input_columns=input_columns,\n", "    output_column=PROMPT_COLUMN,\n", "    ignore_error=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result[\"task_info\"].stdout[1])\n", "print(result[\"status_count\"])\n", "# print(result[\"task_info\"][\"output_path\"][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result2 = map_parquet.apply(\n", "    spark,\n", "    partial(\n", "        pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer\n", "    ),\n", "    input_path=step1_uri,\n", "    output_path=step2_uri,\n", "    input_columns=[PROMPT_COLUMN],\n", "    output_column=SAMPLES_COLUMN,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result2[\"task_info\"].stderr[0])\n", "result2\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(step2_uri).select(SAMPLES_COLUMN)\n", "spark.sparkContext.setJobDescription(\"Shuffling dataset\")\n", "df = repartition_and_shuffle(config.random_seed, df)\n", "\n", "# # TODO(michiel) fix partition naming\n", "spark.sparkContext.setJobDescription(\"Creating indexed dataset\")\n", "export_indexed_dataset_helper(\n", "    df,\n", "    vocab_size=tokenizer.vocab_size,\n", "    samples_column=SAMPLES_COLUMN,\n", "    num_validation_samples=config.num_validation_samples,\n", "    indexed_dataset_path=Path(config.output),\n", ")\n", "with open(config.output + \"config.json\", \"w\") as f:\n", "    json.dump(asdict(config), f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
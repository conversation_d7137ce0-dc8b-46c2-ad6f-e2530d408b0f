{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Notebook Overview\n", "\n", "This notebook shows how to use the oracle ranker to explore best retrievals for a particular model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import logging\n", "from termcolor import colored\n", "from types import MethodType\n", "\n", "import numpy as np\n", "import torch.nn as nn\n", "\n", "from research.models.model_server import add_files_to_index\n", "from research_models import (\n", "    CodeGen_350M_Base_Model,\n", "    CodeGen_2B_Base_Model,\n", "    CodeGen_16B_Base_Model,\n", "    StarCoder_Model,\n", "    CodeGen_16B_Indiana_Model\n", ")\n", "from experimental.michiel.research.oracle_retrieval_ranker import OracleRetrievalRanker\n", "from retrieval.libraries.chunking_functions import scope_aware_chunker\n", "from retrieval.libraries.file_filterer import basic_file_filterer\n", "from retrieval.libraries.types import Document, Chunk\n", "from retrieval.retrieval_database import RetrievalDatabase\n", "from retrieval.libraries.scorers.dense_scorer import DenseRetrievalScorer\n", "from research.core.model_input import ModelInput\n", "\n", "home = os.environ['HOME']\n", "logging.basicConfig(level=logging.WARNING)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### LOAD MODEL\n", "\n", "checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "\n", "print(\"Loading the model...\")\n", "model = CodeGen_16B_Indiana_Model()\n", "# model = CodeGen_16B_Base_Model(checkpoints_root)\n", "# model = StarCoder_Model(checkpoints_root)\n", "model.load()\n", "\n", "# Build dense document index (and retriever model)\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "dense_scorer = DenseRetrievalScorer(\n", "    yaml_files=yaml_files,\n", "    overwrite_values=overwrite_values)\n", "chunker = scope_aware_chunker\n", "file_filterer = basic_file_filterer\n", "dense_retrieval_doc_index = RetrievalDatabase(\n", "    chunker=chunker,\n", "    scorer=dense_scorer,\n", "    file_filterer=file_filterer,\n", ")\n", "\n", "# Set document index\n", "model.document_index = dense_retrieval_doc_index\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Toy example usage of ranker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oracle_ranker = OracleRetrievalRanker(\n", "    model=model,\n", ")\n", "\n", "oracle_ranker.query('prompt', 'reference', ['Potato', 'Bread'])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Real example use of ranker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### ADD FILES FROM REPO\n", "repo_root = os.environ[\"HOME\"] + \"/src/transformers/src/transformers/models/gpt_neox/\"\n", "add_files_to_index(dense_retrieval_doc_index, path=repo_root, extensions=[\".py\"])\n", "repo_root = os.environ[\"HOME\"] + \"/src/transformers/src/transformers/models/gpt2/\"\n", "add_files_to_index(dense_retrieval_doc_index, path=repo_root, extensions=[\".py\"])\n", "repo_root = os.environ[\"HOME\"] + \"/src/transformers/src/transformers/models/gptj/\"\n", "add_files_to_index(dense_retrieval_doc_index, path=repo_root, extensions=[\".py\"])\n", "repo_root = os.environ[\"HOME\"] + \"/src/transformers/src/transformers/models/t5/\"\n", "add_files_to_index(dense_retrieval_doc_index, path=repo_root, extensions=[\".py\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "prompt = \"\"\"def _attn(\n", "        self,\n", "        query,\n", "        key,\n", "        value,\n", "        attention_mask=None,\n", "        head_mask=None,\n", "    ):\n", "        # compute causal mask from causal mask buffer\n", "        query_length, key_length = query.size(-2), key.size(-2)\n", "        causal_mask = self.causal_mask[:, :, key_length - query_length : key_length, :key_length]\n", "\n", "        # Keep the attention weights computation in fp32 to avoid overflow issues\n", "        query = query.to(torch.float32)\n", "        key = key.to(torch.float32)\"\"\"\n", "\n", "reference = \"\"\"\n", "        value = value.to(torch.float32)\n", "\n", "        # compute attention mask\n", "        if attention_mask is not None:\n", "            attention_mask = attention_mask.to(torch.float32)\n", "            if attention_mask.dim() != 3:\n", "                raise ValueError(\n", "                    \"attention_mask should be of size (batch_size, sequence_length, sequence_length)\"\n", "                )\"\"\"\n", "\n", "retrieval_candidate_chunks, retrieval_candidate_scores = dense_retrieval_doc_index.query(ModelInput(prefix=prompt), top_k=1000)\n", "retrieval_candidate_texts = [chunk.text for chunk in retrieval_candidate_chunks]\n", "\n", "sorted_retrievals, sorted_scores = oracle_ranker.query(\n", "    prompt=prompt, \n", "    reference=reference, \n", "    retrieval_texts=retrieval_candidate_texts)\n", "\n", "reference_score = oracle_ranker.score(\n", "    retrieval_text=reference,\n", "    prompt=prompt,\n", "    reference=reference,\n", ")\n", "\n", "no_retrieval_score = oracle_ranker.score(\n", "    retrieval_text='',\n", "    prompt=prompt,\n", "    reference=reference,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print(\"\\n======= Reference:\")\n", "print(colored(prompt, \"blue\"))\n", "\n", "min_score = sorted_scores[-1]\n", "max_score = sorted_scores[0]\n", "mean_score = np.mean(sorted_scores)\n", "\n", "print(f\"\\nScore range: {min_score}, {max_score}\")\n", "print(f\"Mean score: {mean_score}\")\n", "print(f\"Reference score: {reference_score}\")\n", "print(f\"No retrieval score: {no_retrieval_score}\")\n", "\n", "print(\"\\n======= Highest scoring retrievals:\")\n", "for idx in range(25):\n", "    print(f\"\\nScore {idx}: {sorted_scores[idx]}\")\n", "    print(f\"Text {idx}: =======\")\n", "    print(sorted_retrievals[idx])\n", "\n", "print(\"\\n======= Lowest scoring retrievals:\")\n", "for idx in range(10):\n", "    print(f\"\\nScore {idx}: {sorted_scores[-idx - 1]}\")\n", "    print(f\"Text {idx}: =======\")\n", "    print(sorted_retrievals[-idx - 1])    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
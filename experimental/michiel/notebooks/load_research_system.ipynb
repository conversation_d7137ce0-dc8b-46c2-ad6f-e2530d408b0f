{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.eval.harness.systems.elden_system import EldenSystem\n", "from experimental.michiel.configs.eval.model_configs import model_config_dict\n", "from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict\n", "\n", "qwen_7b_elden_6k = model_config_dict[\"qwen_7b_elden_6k\"]\n", "methanol = retriever_config_dict[\"methanol0416\"]\n", "dense = retriever_config_dict[\"ethanolfb_smart\"]\n", "\n", "\n", "system_config = {\n", "    \"model\": qwen_7b_elden_6k,\n", "    \"generation_options\": {\"max_generated_tokens\": 280},\n", "    \"dense_retriever\": dense,\n", "    \"signature_retriever\": methanol,\n", "    \"fim_gen_mode\": \"evaluation\",\n", "    \"experimental\": {\n", "        \"remove_suffix\": False,\n", "        \"trim_on_dedent\": <PERSON>alse,\n", "        \"retriever_top_k\": 32,\n", "        \"signature_retriever_top_k\": 32,\n", "    },\n", "}\n", "\n", "elden_system = EldenSystem.from_yaml_config(system_config)\n", "elden_system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix=\"def hello_world():\\n\",\n", "    suffix=\"\",\n", "    path=\"foo.py\",\n", "    target=\"\",\n", "    doc_ids=None,\n", "    recency_info=None,\n", "    chat_input=None,\n", "    extra={},\n", ")\n", "generation = elden_system.generate(model_input)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
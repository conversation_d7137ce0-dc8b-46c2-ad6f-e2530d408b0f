{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "\n", "\n", "def load_first_parquet_file(directory_path, num_rows=5):\n", "    # Convert the directory path to a Path object\n", "    dir_path = Path(directory_path)\n", "\n", "    # Find the first Parquet file in the directory\n", "    parquet_files = list(dir_path.glob(\"*.parquet\"))\n", "\n", "    if not parquet_files:\n", "        raise FileNotFoundError(f\"No Parquet files found in {directory_path}\")\n", "\n", "    # Get the path of the first <PERSON><PERSON><PERSON> file\n", "    first_parquet_file = parquet_files[0]\n", "\n", "    print(f\"Loading file: {first_parquet_file}\")\n", "\n", "    # Read the first 5 rows of the Parquet file into a pandas DataFrame\n", "    df = pd.read_parquet(first_parquet_file, engine=\"pyarrow\")\n", "\n", "    return df.head(num_rows)\n", "\n", "\n", "# Example usage\n", "directory_path = \"/mnt/efs/spark-data/shared/ethanol/med128smart/score\"\n", "directory_path = \"/mnt/efs/spark-data/shared/ethanol/testsig/score\"\n", "# directory_path = \"/mnt/efs/spark-data/shared/ethanol/testnewelden/score\"\n", "# directory_path = \"/mnt/efs/spark-data/shared/ethanol/test/score\"\n", "# directory_path = \"/mnt/efs/spark-data/shared/ethanol/testcontrol/score\"\n", "result_df = load_first_parquet_file(directory_path, 20)\n", "print(result_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import json\n", "from termcolor import colored\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_chunks\n", "\n", "\n", "def print_score_sample(sample):\n", "    print(colored(sample[\"file_path\"], \"red\"))\n", "    # construct middle from middle spans\n", "    deserialized_middle_spans = pickle.loads(sample[\"middle_spans\"])\n", "    middle_str = \"\"\n", "    for span in deserialized_middle_spans:\n", "        if span.skipped:\n", "            middle_str += f\"<|skip>{span.content.code}</|skip>\"\n", "        else:\n", "            middle_str += span.content.code\n", "    sample_str = (\n", "        sample[\"prefix\"][-500:] + colored(middle_str, \"green\") + sample[\"suffix\"][:300]\n", "    )\n", "    print(sample_str)\n", "\n", "    # # get retrieved chunks\n", "    retrieved_chunks = deserialize_retrieved_chunks(sample[\"retrieved_chunks\"])\n", "    scores = json.loads(sample[\"ppl_scores\"])[\"scores\"]\n", "    print(\"\\n\")\n", "    for idx, chunk in enumerate(retrieved_chunks[:100]):\n", "        score_str = f\"Retrieved chunk {idx} with score {scores[idx]}\"\n", "        score_str = colored(score_str, \"blue\")\n", "        print(score_str)\n", "        # print(chunk.text)\n", "\n", "\n", "# result_df['retrieved_chunks'] = result_df['retrieved_chunks'].apply(deserialize_retrieved_chunks)\n", "# result_df['retrieved_chunks'][1][0].text\n", "\n", "print_score_sample(result_df.iloc[4])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute distribution of perplexity scores\n", "# Compute distribution of perplexity scores\n", "from research.data.spark import k8s_session\n", "from pyspark.sql.functions import (\n", "    col,\n", "    mean,\n", "    from_json,\n", "    explode,\n", "    st<PERSON><PERSON>,\n", "    min,\n", "    max,\n", "    percentile_approx,\n", "    udf,\n", ")\n", "from pyspark.sql.types import ArrayType, DoubleType\n", "\n", "# Create a Spark session\n", "spark = k8s_session(\n", "    name=\"ComputeAvgPerplexityScores\",\n", "    max_workers=8,\n", "    conf={\n", "        \"spark.executor.pyspark.memory\": \"64G\",\n", "        \"spark.executor.memory\": \"32G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"32\",\n", "        \"spark.task.cpus\": \"2\",\n", "    },\n", ")\n", "\n", "path = \"/mnt/efs/spark-data/shared/ethanol/testground/score\"\n", "# Read all parquet files in the directory\n", "df = spark.read.parquet(path)\n", "\n", "\n", "def compute_sample_average_perplexity_score(ppl_scores):\n", "    scores = json.loads(ppl_scores)[\"scores\"]\n", "    average_score = sum(scores) / len(scores)\n", "    return average_score\n", "\n", "\n", "compute_avg_score_udf = udf(compute_sample_average_perplexity_score, DoubleType())\n", "\n", "df_with_avg_score = df.withColumn(\"avg_score\", compute_avg_score_udf(col(\"ppl_scores\")))\n", "\n", "summary_stats = df_with_avg_score.select(\n", "    mean(\"avg_score\").alias(\"mean\"),\n", "    stddev(\"avg_score\").alias(\"stddev\"),\n", "    min(\"avg_score\").alias(\"min\"),\n", "    max(\"avg_score\").alias(\"max\"),\n", "    percentile_approx(\"avg_score\", [0.25, 0.5, 0.75]).alias(\"percentiles\"),\n", ").collect()[0]\n", "\n", "# Print summary statistics\n", "print(f\"Mean: {summary_stats['mean']}\")\n", "print(f\"Standard Deviation: {summary_stats['stddev']}\")\n", "print(f\"Min: {summary_stats['min']}\")\n", "print(f\"Max: {summary_stats['max']}\")\n", "print(f\"25th Percentile: {summary_stats['percentiles'][0]}\")\n", "print(f\"Median: {summary_stats['percentiles'][1]}\")\n", "print(f\"75th Percentile: {summary_stats['percentiles'][2]}\")\n", "\n", "# Stop the Spark session\n", "spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}